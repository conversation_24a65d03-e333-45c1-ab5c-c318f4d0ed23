package com.cszn.integrated.service.provider.member;


import com.cszn.integrated.service.api.member.MmsActivityQrcodeService;
import com.cszn.integrated.service.entity.member.MmsActivityQrcode;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/7/8
 **/
@Bean
public class MmsActivityQrcodeServiceImpl extends JbootServiceBase<MmsActivityQrcode> implements MmsActivityQrcodeService {


    @Override
    public Page<Record> findList(Integer pageNumber, Integer pageSize, String name, String officeId) {
        List<Object> params = new ArrayList<>();
        String sql = "from mms_activity_qrcode q left join sys_user u on q.user_id = u.id " +
                "left join main_branch_office_user b on u.id = b.user_id " +
                "left join main_branch_office m on b.branch_office_id = m.id " +
                "where q.del_flag = 0 ";

        if(StringUtils.isNotBlank(name)){
            sql += "and u.id in (select id from sys_user where name like concat('%',?,'%')) ";
            params.add(name);
        }
        if(StringUtils.isNotBlank(officeId)){
            sql += "and m.id = ? ";
            params.add(officeId);
        }
        sql += "order by q.update_time desc";
        return Db.paginate(pageNumber,pageSize,"select q.id,q.user_id as userId,q.qrcode,u.name,m.full_name as fullName,q.remark,q.del_flag as delFlag,q.create_by as createBy," +
                "q.create_time as createTime,q.update_by as updateBy,q.update_time as updateTime ",sql,params.toArray());
    }




    @Override
    public MmsActivityQrcode get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }




    @Override
    public boolean delActivityQrcode(String id, String userId) {
        MmsActivityQrcode activityQrcode = get(id);
        if(activityQrcode == null){
            return false;
        }
        activityQrcode.setDelFlag("1");
        activityQrcode.setUpdateBy(userId);
        activityQrcode.setUpdateTime(new Date());
        return activityQrcode.update();
    }
}
