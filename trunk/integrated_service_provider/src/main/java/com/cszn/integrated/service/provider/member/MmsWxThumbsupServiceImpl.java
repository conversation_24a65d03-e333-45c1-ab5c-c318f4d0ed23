package com.cszn.integrated.service.provider.member;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsWxThumbsupService;
import com.cszn.integrated.service.entity.member.MmsWxThumbsup;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

/**
 * 微信点赞
 */
@Bean
public class MmsWxThumbsupServiceImpl extends JbootServiceBase<MmsWxThumbsup> implements MmsWxThumbsupService {

    public String saveMmsWxThumbsup(MmsWxThumbsup wxThumbsup){
        //去重
        List<Object> param = new ArrayList<>();
        String sql = "SELECT * FROM mms_wx_thumbsup WHERE user_id = ? AND moment_id = ?";
        param.add(wxThumbsup.getUserId());
        param.add(wxThumbsup.getMomentId());
        List<MmsWxThumbsup> list = DAO.find(sql,param.toArray());
        if(list != null && list.size() >= 1) return "";

        wxThumbsup.setId(IdGen.getUUID());
        wxThumbsup.setDelFlag("0");
        wxThumbsup.setCreateTime(new Date());
        wxThumbsup.setUpdateTime(new Date());
        if(wxThumbsup.save()){
            return "suc";
        }else{
            return null;
        }
    }

    public Long getThumbsupCount(String userId){
        String sql = "SELECT COUNT(t.user_id) FROM mms_wx_moments m LEFT JOIN mms_wx_thumbsup t ON m.id = t.moment_id " +
                "WHERE m.del_flag = 0 AND t.del_flag = 0 AND m.user_id = ?";
        return Db.queryLong(sql,userId);
    }



    @Override
    public List<Record> getThumbsup(String momentId) {
        String sql = "SELECT tp.id, tp.user_id AS userId, tp.moment_id AS momentId, tp.del_flag AS delFlag, " +
                "tp.create_time AS createTime, tp.update_time AS updateTime,wu.nick_name AS nickName FROM mms_wx_thumbsup tp " +
                "LEFT JOIN mms_wx_user wu " +
                "on tp.user_id = wu.id " +
                "WHERE tp.del_flag = 0 AND tp.moment_id = ? " +
                "ORDER BY tp.create_time ASC";
        return Db.find(sql,momentId);
    }
}