package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsQuestionnaireQuestionOptionService;
import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestionOption;
import com.jfinal.kit.StrKit;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.Date;
import java.util.List;

@Bean
public class MmsQuestionnaireQuestionOptionServiceImpl extends JbootServiceBase<MmsQuestionnaireQuestionOption> implements MmsQuestionnaireQuestionOptionService {

    @Override
    public List<MmsQuestionnaireQuestionOption> findOptionListByQuestionIds(List<String> ids){
        String str="";
        for(String id:ids){
            str+="?,";
        }
        str=str.substring(0,str.length()-1);
        String sql="select * from mms_questionnaire_question_option where del_flag='0' and question_id in ("+str+") order by sort ";
        return DAO.find(sql,ids.toArray());
    }


    @Override
    public boolean saveOption(MmsQuestionnaireQuestionOption option,String userId){
        boolean flag=false;
        if(StrKit.isBlank(option.getId())){
            option.setId(IdGen.getUUID());
            option.setDelFlag("0");
            option.setCreateBy(userId);
            option.setCreateDate(new Date());
            option.setUpdateBy(userId);
            option.setUpdateDate(new Date());
            flag=option.save();
        }else{
            option.setUpdateBy(userId);
            option.setUpdateDate(new Date());
            flag=option.update();
        }
        return flag;
    }
}
