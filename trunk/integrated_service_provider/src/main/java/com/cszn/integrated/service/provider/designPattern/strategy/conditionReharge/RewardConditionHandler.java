package com.cszn.integrated.service.provider.designPattern.strategy.conditionReharge;

import com.cszn.integrated.base.utils.HttpClientsUtils;

import java.net.URLEncoder;

/**
 * 条件处理
 */
public abstract class RewardConditionHandler {













    /**
     * 提前生成考勤数据
     * @param args
     */
    public static void main(String[] args) {

        String[] empIds=new String[]{"4EC21FDF-C7FD-4950-8D3C-CE05B98F9A71","12F8AF42-0500-42CE-9179-77DB84F02383","5C82C384-D955-43B8-AAC0-C27661AC50B3","2BA746BC-172A-4129-807E-6292AB4674DB","3F643EB1-6DC1-4F00-BC8F-D7205CD88679","298A8BCE-75A6-44F2-A59D-FA65AC315F0A","F99E9D82-29BC-4C62-A665-61C69124FB0D","1591E2D2-C5BC-4079-A0FD-05B97FFE15F0","3E381E75-069C-4E5A-B3F9-5DC7CE0A7104"};
        String[] checkinTimes=new String[]{"2025-04-30 18:00:00"};
        String[] genDays=new String[]{"2025-04-30"};

        String checkinTimeUrl="http://hrm.cncsgroup.com/api/empNewCheckin";
        String genDayUrl="http://hrm.cncsgroup.com/api/empNewGenDaySummary";
        for (String empId : empIds) {
            for (String checkinTime : checkinTimes) {
                String res = HttpClientsUtils.get(checkinTimeUrl + "?empId=" + empId + "&checkinTime=" + URLEncoder.encode(checkinTime));
                System.out.println("empNewCheckin："+empId+"——————————————————"+res);
            }
            for (String genDay : genDays) {
                String res = HttpClientsUtils.get(genDayUrl + "?empId=" + empId + "&checkinTime=" + genDay);
                System.out.println("empNewGenDaySummary："+empId+"——————————————————"+res);
            }
        }

    }

}
