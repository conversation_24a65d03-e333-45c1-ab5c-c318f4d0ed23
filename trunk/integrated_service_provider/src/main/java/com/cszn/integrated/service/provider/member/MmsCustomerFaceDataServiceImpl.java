package com.cszn.integrated.service.provider.member;

import java.util.List;

import com.cszn.integrated.service.api.member.MmsCustomerFaceDataService;
import com.cszn.integrated.service.entity.member.MmsCustomerFaceData;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsCustomerFaceDataServiceImpl extends JbootServiceBase<MmsCustomerFaceData> implements MmsCustomerFaceDataService {

	@Override
	public List<MmsCustomerFaceData> findList(String faceId) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(faceId)) {
			columns.eq("face_id", faceId);
		}
		return DAO.findListByColumns(columns, "create_time desc");
	}
}