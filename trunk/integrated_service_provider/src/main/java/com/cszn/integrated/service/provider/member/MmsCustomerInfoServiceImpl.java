package com.cszn.integrated.service.provider.member;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsCustomerAssignService;
import com.cszn.integrated.service.api.member.MmsCustomerInfoService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.pers.PersEmpUserService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.weixin.WeiXinActivityService;
import com.cszn.integrated.service.entity.member.MmsCustomerAssign;
import com.cszn.integrated.service.entity.member.MmsCustomerInfo;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsCustomerInfoServiceImpl extends JbootServiceBase<MmsCustomerInfo> implements MmsCustomerInfoService {

	@Inject
	private MmsMemberService mmsMemberService;
	@Inject
	private PersEmpUserService persEmpUserService;
	@Inject
	MmsCustomerAssignService customerAssignService;
	@Inject
	private PersOrgService persOrgService;
	@Inject
	private PersOrgEmployeeService persOrgEmployeeService;
	@Inject
	private WeiXinActivityService weiXinActivityService;

	@Override
	public Page<MmsCustomerInfo> customerPage(int pageNumber, int pageSize, MmsCustomerInfo model) {
		List<Object> param = Lists.newArrayList();
		String selectSql = "select id,member_name,gender,birthday,idcard ";
		String sql = "from mms_customer_info where del_flag='0' ";
		if(StringUtils.isNotBlank(model.getMemberName())){
			sql += "and member_name like concat('%',?,'%') ";
			param.add(model.getMemberName());
		}
		if(StringUtils.isNotBlank(model.getIdcard())){
			sql += "and idcard like concat('%',?,'%') ";
			param.add(model.getIdcard());
		}
		sql += "order by create_time desc";
		Page<MmsCustomerInfo> customerInfoPage=DAO.paginate(pageNumber,pageSize,selectSql,sql,param.toArray());
		return customerInfoPage;
	}

	@Override
	public Page<MmsCustomerInfo> preciseQueryTable(int pageNumber, int pageSize, MmsCustomerInfo model) {
		
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
        if(model != null) {
            if (StringUtils.isNotBlank(model.getMemberName())) {
                columns.eq("member_name", model.getMemberName().trim());
            }
            if (StringUtils.isNotBlank(model.getIdcard())) {
            	columns.eq("idcard", model.getIdcard().trim());
            }
            if (StringUtils.isNotBlank(model.getPhoneNumber())) {
            	columns.eq("phone_number", model.getPhoneNumber().trim());
            }
            if (StringUtils.isNotBlank(model.getIsPreciseQuery())) {
            	columns.eq("is_precise_query", model.getIsPreciseQuery().trim());
            }
        }
        return DAO.paginateByColumns(pageNumber,pageSize,columns.getList(),"create_time desc");
	}
	
	@Override
	public Page<MmsCustomerInfo> pageTable(int pageNumber, int pageSize, MmsCustomerInfo model,String isAssignVisit, String isVisit, String startDate, String endDate, String callPass, String followResult, Integer memberAge) {
		
		List<Object> param = Lists.newArrayList();
        String selectSql = "select ci.* ";
        String sql = "from mms_customer_info ci "
		+ "left join mms_customer_assign ca on ca.customer_id=ci.id "
		+ "left join ("
		+ "select customer_id,count(id)countData,"
		+ "case when count(id)=0 then '' when count(id)=1 then group_concat(call_pass) else SUBSTRING_INDEX(group_concat(call_pass order by create_time desc), ',', 1) end as callPass,"
		+ "case when count(id)=0 then '' when count(id)=1 then group_concat(follow_result) else SUBSTRING_INDEX(group_concat(follow_result order by create_time desc), ',', 1) end as followResult from mms_customer_visit where del_flag='0' group by customer_id"
		+ ")v on v.customer_id=ci.id "
		+ "left join ("
		+ "select customer_id,count(id)visitCount from mms_customer_visit where del_flag='0' and reception_user_id='"+model.getCreateBy()+"' group by customer_id"
		+ ")vv on vv.customer_id=ci.id "
		+ "where ci.del_flag='0' ";
        
        if(StringUtils.isNotBlank(startDate)){
        	sql += "and ci.create_time>=? ";
        	param.add(startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)){
        	sql += "and ci.create_time<=? ";
        	param.add(endDate + " 23:59:59");
        }
        if(StringUtils.isNotBlank(model.getBelongOrgId())){
        	if("111".equals(model.getBelongOrgId())){
        		sql += "and IFNULL(ci.belong_org_id,'')='' ";
        	}else{
        		sql += "and ci.belong_org_id=? ";
        		param.add(model.getBelongOrgId());
        	}
        }
        if(StringUtils.isNotBlank(model.getProvince())){
            sql += "and ci.province = ? ";
            param.add(model.getProvince().trim());
        }
        if(StringUtils.isNotBlank(model.getCity())){
            sql += "and ci.city = ? ";
            param.add(model.getCity().trim());
        }
        if(StringUtils.isNotBlank(model.getTown())){
            sql += "and ci.town = ? ";
            param.add(model.getTown().trim());
        }
        if(StringUtils.isNotBlank(model.getStreet())){
            sql += "and ci.street = ? ";
            param.add(model.getStreet().trim());
        }
        if(StringUtils.isNotBlank(model.getIsPreciseQuery())){
        	sql += "and ci.is_precise_query=? ";
        	param.add(model.getIsPreciseQuery());
        }
        if(StringUtils.isNotBlank(model.getMemberName())){
            sql += "and ci.member_name like concat('%',?,'%') ";
            param.add(model.getMemberName());
        }
        if(StringUtils.isNotBlank(model.getIdcard())){
        	sql += "and ci.idcard like concat('%',?,'%') ";
        	param.add(model.getIdcard());
        }
        if(StringUtils.isNotBlank(model.getPhoneNumber())){
        	sql += "and ci.phone_number=? ";
        	param.add(model.getPhoneNumber());
        }
        if(StringUtils.isNotBlank(model.getCustomerType())){
			sql += "and ci.customer_type=? ";
			param.add(model.getCustomerType());
//        	if("member".equals(model.getCustomerType())) {
//        		sql += "and IFNULL(m.id,'')!=? ";
//            	param.add("");
//        	}else {
//        		sql += "and IFNULL(m.id,'')=? ";
//            	param.add("");
//        	}
        }
        if(StringUtils.isNotBlank(model.getStaffType())){
			sql += "and ci.staff_type=? ";
			param.add(model.getStaffType());
//        	if("staff".equals(model.getStaffType())) {
//        		sql += "and IFNULL(oe.id,'')!=? ";
//            	param.add("");
//        	}else {
//        		sql += "and IFNULL(oe.id,'')=? ";
//            	param.add("");
//        	}
        }
        if(StrKit.notBlank(model.getTouristType())){
			sql += "and ci.tourist_type=? ";
			param.add(model.getTouristType());
		}
        if(StringUtils.isNotBlank(model.getCreateBy())){
        	sql += "and ca.assign_user_id=? ";
        	param.add(model.getCreateBy());
        }
        if(StringUtils.isNotBlank(isAssignVisit)){
        	if("yes".equals(isAssignVisit)) {
        		sql += "and IFNULL(vv.visitCount,0)>? ";
        		param.add(0);
        	}else {
        		sql += "and IFNULL(vv.visitCount,0)=? ";
        		param.add(0);
        	}
        }
        if(StringUtils.isNotBlank(isVisit)){
        	if("yes".equals(isVisit)) {
        		sql += "and IFNULL(v.countData,0)>? ";
            	param.add(0);
        	}else {
        		sql += "and IFNULL(v.countData,0)=? ";
            	param.add(0);
        	}
        }
        if(StringUtils.isNotBlank(callPass)){
        	sql += "and v.callPass=? ";
        	param.add(callPass);
        }
        if(StringUtils.isNotBlank(followResult)){
        	sql += "and v.followResult=? ";
        	param.add(followResult);
        }
        if(memberAge!=null){
			sql += "and IFNULL(TIMESTAMPDIFF(YEAR,SUBSTRING(ci.idcard, 7, 8), now()),0)>? ";
			param.add(memberAge);
		}
        if(StringUtils.isNotBlank(model.getCreateBy())){
        	sql += "order by IFNULL(vv.visitCount,0) asc";
        }else {
        	sql += "order by ci.create_time desc";
        }

        Page<MmsCustomerInfo> customerInfoPage=DAO.paginate(pageNumber,pageSize,selectSql,sql,param.toArray());


        return customerInfoPage;
	}

	@Override
	public Page<MmsCustomerInfo> myPageTable(int pageNumber, int pageSize, MmsCustomerInfo model) {
		
		List<Object> params = Lists.newArrayList();
		String sql=" from mms_customer_info r "
			+ "left join mms_customer_assign a on a.customer_id=r.id and "
			+ "where r.del_flag='0' ";
		
		if(StringUtils.isNotBlank(model.getCreateBy())){
        	sql += "and a.assign_user_id=? ";
        	params.add(model.getCreateBy());
        }
		
        if(StringUtils.isNotBlank(model.getMemberName())){
            sql += "and r.member_name like concat('%',?,'%') ";
            params.add(model.getMemberName());
        }
        return DAO.paginate(pageNumber,pageSize,"select r.* ",sql,params.toArray());
	}
	
	@Override
	public Page<MmsCustomerInfo> salesPageTable(int pageNumber, int pageSize, MmsCustomerInfo model) {
		
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL).add("create_by", model.getCreateBy());
        if(model != null) {
            if (StringUtils.isNotBlank(model.getMemberName())) {
                columns.like("member_name", model.getMemberName().trim());
            }
        }
        return DAO.paginateByColumns(pageNumber,pageSize,columns.getList(),"create_time desc");
	}

	public List<Record> newCountList(String startDate, String endDate) {
		return Db.find("select o.org_name as orgName,v.newCount from ("
			+ "select belong_org_id,count(id)newCount from mms_customer_info "
			+ "where del_flag='0' and create_time>=? and create_time<=? group by belong_org_id"
			+ ")v left join pers_org o on o.id=v.belong_org_id", startDate, endDate);
	}

	public int yesterdayNewCount(String startDate, String endDate) {
		return Db.queryInt("select count(id) as newCount from mms_customer_info where del_flag='0' and create_time>=? and create_time<=?",startDate, endDate);
	}

	@Override
	public MmsCustomerInfo getByIdcard(String idcard) {
        return DAO.findFirst("select * from mms_customer_info where del_flag='0' and idcard = ?",idcard);
	}

	@Override
	public MmsCustomerInfo getByPhoneNumber(String phoneNumber) {
		return DAO.findFirst("select * from mms_customer_info where del_flag='0' and phone_number = ?",phoneNumber);
	}

	@Override
	public MmsCustomerInfo getByIdcardAndPhone(String idcard, String phoneNumber) {
		return DAO.findFirst("select * from mms_customer_info where del_flag='0' and idcard=? and phone_number=?", idcard, phoneNumber);
	}

	@Override
	public MmsCustomerInfo getByWxUserId(String wxUserId) {
		return DAO.findFirst("select * from mms_customer_info where del_flag='0' and wx_user_id=?", wxUserId);
	}

	@Override
	public Ret customerSave(MmsCustomerInfo model, String verifyCode) {
		Ret returnRet = Ret.fail("msg", "保存失败");
		synchronized (this) {
			if(StrKit.isBlank(model.getMemberName())) {
				return Ret.fail("msg", "姓名不能为空!");
			}
			if(StrKit.isBlank(model.getGender())) {
				return Ret.fail("msg", "性别不能为空!");
			}
			if(StrKit.isBlank(model.getIdcardType())) {
				return Ret.fail("msg", "证件类型不能为空!");
			}
			if(StrKit.isBlank(model.getIdcard())) {
				return Ret.fail("msg", "身份证不能为空!");
			}
			if(StrKit.isBlank(model.getPhoneNumber())) {
				return Ret.fail("msg", "手机号不能为空!");
			}
			if(StrKit.isBlank(model.getCreateBy())) {
				return Ret.fail("msg", "创建用户id不能为空!");
			}
			//旅居入住传过来不用短信验证
			if(!"bmp".equalsIgnoreCase(model.getDataSource())){
				if(StrKit.isBlank(verifyCode)) {
					return Ret.fail("msg", "短信验证码不能为空!");
				}
				if(!weiXinActivityService.checkCode(model.getPhoneNumber(), verifyCode)) {
					return Ret.fail("msg", "验证码输入不正确!");
				}
			}
			//姓名处理空格
			if(StrKit.notBlank(model.getMemberName())){
				String memberName = model.getMemberName();
				if(memberName.contains(" ")){
					memberName = memberName.replaceAll("\\s+", "");
				}
				if(memberName.contains("　")){
					memberName = memberName.replaceAll("　", "");
				}
				model.setMemberName(memberName);
			}
			//身份证处理空格
			if(StrKit.notBlank(model.getIdcard())){
				String idcard = model.getIdcard();
				if(idcard.contains(" ")){
					idcard = idcard.replaceAll("\\s+", "");
				}
				if(idcard.contains("　")){
					idcard = idcard.replaceAll("　", "");
				}
				model.setIdcard(idcard);
			}
			//手机号处理空格
			if(StrKit.notBlank(model.getPhoneNumber())){
				String phoneNumber = model.getPhoneNumber();
				if(phoneNumber.contains(" ")){
					phoneNumber = phoneNumber.replaceAll("\\s+", "");
				}
				if(phoneNumber.contains("　")){
					phoneNumber = phoneNumber.replaceAll("　", "");
				}
				model.setPhoneNumber(phoneNumber);
			}
			Map<String, String> idMap = IdGen.getBirAgeSex(model.getIdcard());
			if(StrKit.isBlank(model.getBirthday())){
				model.setBirthday(idMap.get("birthday"));
			}
			MmsCustomerInfo customerInfo = this.getByIdcard(model.getIdcard());
			if(customerInfo==null) {
				//判断是否会员
				MmsMember member = mmsMemberService.getMemberByIdcard(model.getIdcard());
				if(member!=null) {
					model.setCustomerType("member");
					model.setMemberId(member.getId());
				}else {
					model.setCustomerType("not_member");
				}
				//判断是否员工
				PersOrgEmployee emp = persOrgEmployeeService.getByEmpIdcard(model.getIdcard());
				if(emp!=null){
					model.setStaffType("staff");
				}else{
					model.setStaffType("not_staff");
				}
				final String deptName = persEmpUserService.getDeptNameByUserId(model.getCreateBy());
				if(deptName.contains("客服")) {
					model.setBelongTo("customer_service");
				}else if(deptName.contains("分公司")||deptName.contains("部")||deptName.contains("组")) {
					model.setBelongTo("sale");
				}else {
					model.setBelongTo("customer");
				}
				final String deptId = persEmpUserService.getDeptIdByUserId(model.getCreateBy());
				if(StrKit.notBlank(deptId)) {
					PersOrg deptOrg = persOrgService.findById(deptId);
					if(deptOrg!=null) {
						if(deptName.contains("分公司")) {
							model.setBelongOrgId(deptId);
							model.setBelongDeptId(deptId);
						}else if(deptName.contains("客服")||deptName.contains("部")||deptName.contains("组")) {
							model.setBelongOrgId(deptOrg.getParentId());
							model.setBelongDeptId(deptId);
						}else {
							model.setBelongTo("customer");
						}
					}
					
				}
				if(StrKit.isBlank(model.getProvince())){
					model.setProvince(model.getIdcard().substring(0, 2)+"0000");
				}
				if(StrKit.isBlank(model.getCity())){
					model.setCity(model.getIdcard().substring(0, 4)+"00");
				}
				if(StrKit.isBlank(model.getTown())){
					model.setTown(model.getIdcard().substring(0, 6));
				}
				if("we_pub".equals(model.getDataSource())) {								
					model.setSalesId(model.getCreateBy());
				}
				model.setId(IdGen.getUUID());
				model.setDelFlag(DelFlag.NORMAL);
				model.setCreateTime(new Date());
				model.setUpdateTime(new Date());
				if(model.save()) {
					returnRet = Ret.ok("msg", "保存成功");
				}
			}else{
				if(StrKit.notBlank(model.getWxUserId())){
					customerInfo.setWxUserId(model.getWxUserId());
				}
				if(StrKit.notBlank(model.getAddress())){
					customerInfo.setAddress(model.getAddress());
				}
				if(StrKit.notBlank(model.getRemarks())){
					customerInfo.setRemarks(model.getRemarks());
				}
				customerInfo.setMemberName(model.getMemberName());
				customerInfo.setGender(model.getGender());
				customerInfo.setIdcardType(model.getIdcardType());
				if(customerInfo.update()) {
					returnRet = Ret.ok("msg", "保存成功");
				}
			}
		}
		return returnRet;
	}

	@Override
	public boolean assignSave(List<MmsCustomerInfo> list, String assignUserId, String userId) {
		boolean flag = false;
        if(list != null && list.size() > 0){
            for (MmsCustomerInfo info : list) {
                if(StringUtils.isNotBlank(info.getId())) {
                	MmsCustomerInfo consultRegister = DAO.findById(info.getId());
                    if(consultRegister != null) {
                    	MmsCustomerAssign assign = customerAssignService.findByAssignCustomerId(info.getId());
                    	if(assign==null) {
                    		assign = new MmsCustomerAssign();
                    		assign.setId(IdGen.getUUID());
                    		assign.setCustomerId(info.getId());
                    		assign.setAssignUserId(assignUserId);
                    		assign.setCreateBy(userId);
                    		assign.setCreateTime(new Date());
                    		flag = assign.save();
                    	}else {
							if (!(Objects.equals(assign.getAssignUserId(), assignUserId))) {
                    			assign.setAssignUserId(assignUserId);
                    			flag = assign.update();
                    		}
                    	}
                    }
                }
            }
        }
        return flag;
	}

}
