package com.cszn.integrated.service.provider.main;


import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.main.MainBranchOfficeUserService;
import com.cszn.integrated.service.entity.main.MainBranchOfficeUser;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/7/8
 **/
@Bean
public class MainBranchOfficeUserServiceImpl extends JbootServiceBase<MainBranchOfficeUser> implements MainBranchOfficeUserService {




    @Override
    public MainBranchOfficeUser get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }



    @Override
    public boolean saveOfficeUserByUser(String id, String userId) {
        MainBranchOfficeUser officeUser = new MainBranchOfficeUser();
        officeUser.setId(IdGen.getUUID());
        officeUser.setBranchOfficeId(id);
        officeUser.setUserId(userId);
        officeUser.setCreateTime(new Date());
        return officeUser.save();
    }


    @Override
    public MainBranchOfficeUser getByUserId(String userId) {
        return DAO.findFirst("select * from main_branch_office_user where user_id = ?",userId);
    }

}
