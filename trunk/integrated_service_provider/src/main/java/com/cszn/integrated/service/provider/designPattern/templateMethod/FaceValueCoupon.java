package com.cszn.integrated.service.provider.designPattern.templateMethod;

import com.cszn.integrated.service.entity.crm.CrmCardRollRecordUse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class FaceValueCoupon extends CouponAbstract {

    //初始值
    private Double totalValue;
    //剩余值
    private Double remainingValue;
    //使用值
    private Double useValue;

    public FaceValueCoupon(){

    }

    @Override
    protected Map<String, Object> childUse() {

        if("ONE".equals(this.getUseTimes())){
            useValue=remainingValue;
        }
        this.getCrmCardRollRecord().setBalanceRollValue(BigDecimal.valueOf(remainingValue).subtract(BigDecimal.valueOf(useValue)).doubleValue());
        if(BigDecimal.valueOf(this.getCrmCardRollRecord().getBalanceRollValue()).compareTo(BigDecimal.ZERO)==0){
            this.getCrmCardRollRecord().setIsUse("1");
        }
        List<CrmCardRollRecordUse> useList=new ArrayList<>();


        return null;
    }

    @Override
    Map<String, Object> restore() {


        return null;
    }


}
