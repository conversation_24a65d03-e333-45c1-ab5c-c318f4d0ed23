package com.cszn.integrated.service.provider.member;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsWxSubscriptionsService;
import com.cszn.integrated.service.entity.member.MmsWxSubscriptions;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

/**
 * 微信用户关注
 */
@Bean
public class MmsWxSubscriptionsServiceImpl extends JbootServiceBase<MmsWxSubscriptions> implements MmsWxSubscriptionsService {

    public List<MmsWxSubscriptions> find(String sql, Object...params){
        return DAO.find(sql,params);
    }

    public Page<MmsWxSubscriptions> subscriptionsPage(Integer pageNumber, Integer pageSize, String userId, String subscriberId){

        List<Object> param = new ArrayList<>();
        String sql = "FROM mms_wx_subscriptions ";

        if(StringUtils.isNotBlank(userId)){
            sql += "WHERE user_id = ? ";
            param.add(userId);
        }
        if(StringUtils.isNotBlank(subscriberId)){
            sql += "WHERE subscriber_id = ? ";
            param.add(subscriberId);
        }
        sql += "ORDER BY create_time DESC";

        return DAO.paginate(pageNumber,pageSize,"SELECT * ",sql,param.toArray());
    }


    public boolean saveSubscriptions(MmsWxSubscriptions wxSubscriptions){

        //不能自我关注
        if(wxSubscriptions.getUserId().equals(wxSubscriptions.getSubscriberId())) return false;
        //去重
        List<Object> param = new ArrayList<>();
        String sql = "SELECT * FROM mms_wx_subscriptions WHERE user_id = ? AND subscriber_id = ?";
        param.add(wxSubscriptions.getUserId());
        param.add(wxSubscriptions.getSubscriberId());
        List<MmsWxSubscriptions> list = DAO.find(sql,param.toArray());
        if(list != null && list.size() >= 1) return false;

        wxSubscriptions.setId(IdGen.getUUID());
        wxSubscriptions.setCreateTime(new Date());
        return wxSubscriptions.save();
    }


    public Long getSubscriptionCount(String userId){
        String sql = "SELECT COUNT(*) FROM mms_wx_subscriptions WHERE user_id = ?";
        return Db.queryLong(sql,userId);
    }


    //被关注数(粉丝)
    public Long getubscriptionedCount(String subscriberId){
        String sql = "SELECT COUNT(*) FROM mms_wx_subscriptions WHERE subscriber_id = ?";
        return Db.queryLong(sql,subscriberId);
    }



}