package com.cszn.integrated.service.provider.member;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.BioHelper;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsMeasureBpService;
import com.cszn.integrated.service.entity.member.MmsMeasureBp;
import com.cszn.integrated.service.entity.status.MeasureResult;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.crypto.hash.Hash;

import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/28
 **/
@Bean
public class MmsMeasureBpServiceImpl extends JbootServiceBase<MmsMeasureBp> implements MmsMeasureBpService {

    /**
     * 获取会员数据，如果type为day，返回今天的，一小时一条
     * 如果type为week，返回本周的，周日到今天的，一天一条
     * 如果type为month，返回本月的，1号到今天的，一天一条
     * @param cardNumber
     * @param type
     * @return
     */
    @Override
    public Map<String, List> memberBp(String cardNumber, String type) {
        Map<String, List> map=new HashMap<>();
        String sql="";
        if(type.equals("day")){
            sql="select concat(DATE_FORMAT(measure_time,'%H'),':00') as time,measure_sbp_value as sbp,measure_sbp_tips as sbpTips,measure_dbp_value as dbp,measure_dbp_tips as dbpTips from mms_measure_bp " +
                    "where id in (select min(b.id) from mms_measure_bp b left join mms_card_equipment e on b.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=b.member_id " +
                    "where c.card_number=? and e.del_flag='0' and measure_sbp_value>0 and measure_dbp_value>0 and TO_DAYS(b.measure_time)=TO_DAYS(now()) GROUP BY DATE_FORMAT(measure_time,'%Y%m%d%H')) ORDER BY measure_time";
        }else if(type.equals("week")){
            sql="select DATE_FORMAT(measure_time,'%m-%d') as time,measure_sbp_value as sbp,measure_sbp_tips as sbpTips,measure_dbp_value as dbp,measure_dbp_tips as dbpTips from mms_measure_bp " +
                    "where id in (select min(b.id) from mms_measure_bp b left join mms_card_equipment e on b.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=b.member_id " +
                    "where c.card_number=? and e.del_flag='0' and measure_sbp_value>0 and measure_dbp_value>0 and YEARWEEK(b.measure_time)=YEARWEEK(now()) GROUP BY DATE_FORMAT(measure_time,'%Y%m%d')) ORDER BY measure_time";
        }else if(type.equals("month")){
            sql="select DATE_FORMAT(measure_time,'%m-%d') as time,measure_sbp_value as sbp,measure_sbp_tips as sbpTips,measure_dbp_value as dbp,measure_dbp_tips as dbpTips from mms_measure_bp " +
                    "where id in (select min(b.id) from mms_measure_bp b left join mms_card_equipment e on b.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=b.member_id " +
                    "where c.card_number=? and e.del_flag='0' and measure_sbp_value>0 and measure_dbp_value>0 and DATE_FORMAT(b.measure_time,'%Y%m')=DATE_FORMAT(now(),'%Y%m') GROUP BY DATE_FORMAT(measure_time,'%Y%m%d')) ORDER BY measure_time";
        }
        if(StrKit.notBlank(sql)){
            List<Record> recordList=Db.find(sql,cardNumber);
            if(recordList==null){
                return map;
            }
            List<String> timeList=new ArrayList<>();
            List<Integer> sbpList=new ArrayList<>();
            List<String> sbpTipsList=new ArrayList<>();
            List<Integer> dbpList=new ArrayList<>();
            List<String> dbpTipsList=new ArrayList<>();
            for (Record record:recordList){
                timeList.add(record.getStr("time"));
                sbpList.add(Integer.valueOf(record.getStr("sbp")));
                sbpTipsList.add(record.getStr("sbpTips"));
                dbpList.add(Integer.valueOf(record.getStr("dbp")));
                dbpTipsList.add(record.getStr("dbpTips"));
            }
            map.put("time",timeList);
            map.put("sbp",sbpList);
            map.put("sbpTips",sbpTipsList);
            map.put("dbp",dbpList);
            map.put("dbpTips",dbpTipsList);
        }
        return map;
    }

    /**
     * 通过会员卡id获取最新一条数据
     * @param cardNumber
     * @return
     */
    @Override
    public Map<String, Object> latestBp(String cardNumber) {
        Map<String,Object> map=new HashMap<>();
        String sql="select b.measure_sbp_value as sbp,b.measure_sbp_tips as sbpTips,b.measure_dbp_value as dbp,b.measure_dbp_tips as dbpTips from mms_measure_bp b " +
                "left join mms_card_equipment e on b.equipment_no=e.equipment_no " +
                "left join fina_membership_card c on c.id=e.card_id and c.member_id=b.member_id " +
                "where c.card_number=? and e.del_flag='0' and b.measure_dbp_value>0 and b.measure_sbp_value>0 order by b.create_date desc limit 1";
        Record record=Db.findFirst(sql,cardNumber);
        if(record!=null){
            map=record.getColumns();
        }
        return map;
    }

    @Override
    public Map<String, Object> getbpData(String memberId, String dataRecord) {
        Map<String,Object> bpMap = new HashMap<>();
        String sql = "select measure_sbp_value as measureSbpValue,measure_dbp_value as measureDbpValue,measure_time AS measureTime " +
                "from mms_measure_bp where del_flag = 0 and measure_sbp_value is not null and measure_dbp_value is not null and measure_time is not null " +
                "and measure_sbp_value != 0 and measure_dbp_value != 0 and member_id = ? ";

        if("tenRecord".equals(dataRecord)){
            List<Record> bpList = Db.find(sql + "order by measure_time desc limit 10",memberId);
            return setValues(bpList);
        }else if("7DayRecord".equals(dataRecord)){
            List<Record> bpList = Db.find(sql + "and DATE_SUB(CURDATE(), INTERVAL 7 DAY) <= date(measure_time) order by measure_time desc",memberId);
            return setValues(bpList);
        }else if("30DayRecord".equals(dataRecord)){
            List<Record> bpList = Db.find(sql + "and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= date(measure_time) order by measure_time desc",memberId);
            return setValues(bpList);
        }else if("monRecord".equals(dataRecord)){
            List<Record> bpList = Db.find(sql + "and DATE_FORMAT( measure_time, '%Y%m' ) = DATE_FORMAT( CURDATE() , '%Y%m' ) order by measure_time desc",memberId);
            return setValues(bpList);
        }
        return bpMap;
    }





    @Override
    public boolean savebpData(String equipmentNo, String measureSbpValue, String measureDbpValue, String userId) {
        String memberId = BioHelper.getMemberIdByEqNo(equipmentNo);
        MmsMeasureBp bp = new MmsMeasureBp();
        bp.setId(IdGen.getUUID());
        bp.setEquipmentNo(equipmentNo);
        bp.setMemberId(memberId);
        bp.setMeasureName("血压");
        bp.setMeasureTime(new Date());
        bp.setMeasureSbpValue(measureSbpValue);
        bp.setMeasureDbpValue(measureDbpValue);
        bp.setMeasureSbpRange("90~139");
        bp.setMeasureDbpRange("60~89");
        bp.setDelFlag("0");
        bp.setCreateBy(userId);
        bp.setUpdateBy(userId);
        if(Integer.valueOf(measureSbpValue) !=null && Integer.valueOf(measureSbpValue)<90){
            bp.setMeasureSbpTips(MeasureResult.LOW);
        }else if(Integer.valueOf(measureSbpValue) !=null && Integer.valueOf(measureSbpValue)>139){
            bp.setMeasureSbpTips(MeasureResult.HIGH);
        }else{
            bp.setMeasureSbpTips(MeasureResult.NORMAL);
        }

        if(Integer.valueOf(measureDbpValue)!=null && Integer.valueOf(measureDbpValue)<60){
            bp.setMeasureDbpTips(MeasureResult.LOW);
        }else if(Integer.valueOf(measureDbpValue)!=null && Integer.valueOf(measureDbpValue)>189){
            bp.setMeasureDbpTips(MeasureResult.HIGH);
        }else{
            bp.setMeasureDbpTips(MeasureResult.NORMAL);
        }
        bp.setCreateDate(new Date());
        bp.setUpdateDate(new Date());
        return bp.save();
    }



    public Map<String,Object> setValues(List<Record> recordList){
        Map<String,Object> bpMap = new HashMap<>();
        JSONObject sbpJson = new JSONObject();
        JSONArray sbpArray = new JSONArray();
        JSONObject dbpJson = new JSONObject();
        JSONArray dbpArray = new JSONArray();
        JSONArray measureJsonData = new JSONArray();
        JSONArray timeArray = new JSONArray();

        if(recordList != null &&  recordList.size() > 0) {
            for (int i = recordList.size() - 1; i >= 0; i--) {
                sbpArray.add(Long.parseLong(recordList.get(i).get("measureSbpValue")));
                dbpArray.add(Long.parseLong(recordList.get(i).get("measureDbpValue")));
                timeArray.add(recordList.get(i).get("measureTime"));
            }
        }
        sbpJson.put("data",sbpArray);
        sbpJson.put("name","收缩压");
        dbpJson.put("data",dbpArray);
        dbpJson.put("name","舒张压");
        measureJsonData.add(sbpJson);
        measureJsonData.add(dbpJson);
        bpMap.put("measureJsonData",measureJsonData);
        bpMap.put("measureTimeArray",timeArray);
        return bpMap;
    }
}
