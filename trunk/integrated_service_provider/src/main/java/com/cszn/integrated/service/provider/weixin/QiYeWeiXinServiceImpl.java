package com.cszn.integrated.service.provider.weixin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.weixin.QiYeWeiXinService;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.weixin.Department;
import com.cszn.integrated.service.entity.weixin.QiYeUser;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.Jboot;
import io.jboot.aop.annotation.Bean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Bean
public class QiYeWeiXinServiceImpl implements QiYeWeiXinService {

    private static final String CORPID= Jboot.configValue("corpid");
    private static final String CORPSECRET=Jboot.configValue("corpsecret");
    private static final String QIYEON=Jboot.configValue("qiyeOn");
    private static final String CHECKIN_CORPSECRET=Jboot.configValue("checkinCorpsecret");

    private Logger logger= LoggerFactory.getLogger(QiYeWeiXinServiceImpl.class);

    @Inject
    PersOrgService persOrgService;
    @Inject
    PersOrgEmployeeService persOrgEmployeeService;

    /**
     * 获取accessToken
     * @return
     */
    @Override
    public String getAccessToken() {
        //如果配置文件中的开关为1直接返回null
        if("1".equals(QIYEON)){
            return null;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid="+CORPID+"&corpsecret="+CORPSECRET;
        String resultData= HttpKit.post(url,null);
        if(!isJsonStr(resultData)){
            return null;
        }
        JSONObject jsonObject= JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return jsonObject.getString("access_token");
        }
        return null;
    }

    /**
     * 获取打卡应用accessToken
     * @return
     */
    @Override
    public String getCheckinAccessToken() {
        //如果配置文件中的开关为1直接返回null
        /*if("1".equals(QIYEON)){
            return null;
        }*/
        String url="https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid="+CORPID+"&corpsecret="+CHECKIN_CORPSECRET;
        String resultData= HttpKit.post(url,null);
        if(!isJsonStr(resultData)){
            return null;
        }
        JSONObject jsonObject= JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return jsonObject.getString("access_token");
        }
        return null;
    }

    @Override
    public String getAccessTokenByCorpsecret(String corpsecret) {
        //如果配置文件中的开关为1直接返回null
        /*if("1".equals(QIYEON)){
            return null;
        }*/
        String url="https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid="+CORPID+"&corpsecret="+corpsecret;
        String resultData= HttpKit.post(url,null);
        if(!isJsonStr(resultData)){
            return null;
        }
        JSONObject jsonObject= JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return jsonObject.getString("access_token");
        }
        return null;
    }

    /**
     * 判断字符串是否是json字符串
     * @param jsonStr
     * @return
     */
    public boolean isJsonStr(String jsonStr){
        if(!jsonStr.startsWith("{") || !jsonStr.endsWith("}")){
            return false;
        }
        return true;
    }

    /**
     * 创建部门
     * @param department
     * @return
     */
    @Override
    public Integer createDepartment(Department department) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return null;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/department/create?access_token="+accessToken;
        String resultData=HttpKit.post(url,"{\"name\":\""+department.getName()+"\",\"parentid\":"+department.getParentId()+",\"order\":"+department.getOrder()+"}");
        if(!isJsonStr(resultData)){
            return null;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return jsonObject.getInteger("id");
        }
        logger.info("创建企业微信部门失败：data:"+JSON.toJSONString(department)+",msg:"+resultData);
        return null;
    }

    /**
     * 修改部门
     * @param department
     * @return
     */
    @Override
    public boolean updateDepartment(Department department) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return false;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/department/update?access_token="+accessToken;
        String body="{\"id\": "+department.getId()+",\"name\": \""+department.getName()+"\",\"parentid\": "+department.getParentId()+",\"order\": "+department.getOrder()+"}";
        String resultData=HttpKit.post(url,body);
        if(!isJsonStr(resultData)){
            return false;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return true;
        }
        logger.info("修改企业微信部门失败：data:"+JSON.toJSONString(department)+",msg:"+resultData);
        return false;
    }

    /**
     * 删除部门
     * @param id
     * @return
     */
    @Override
    public boolean deleteDepartment(Integer id) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return false;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/department/delete?access_token="+accessToken+"&id="+id;
        String resultData=HttpKit.post(url,null);
        if(!isJsonStr(resultData)){
            return false;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return true;
        }
        logger.info("删除企业微信部门失败：id:"+id+",msg:"+resultData);
        return false;
    }

    @Override
    public String getUser(String userId){
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return null;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token="+accessToken+"&userid="+userId;
        String resultData= HttpClientsUtils.get(url);

        if(!isJsonStr(resultData)){
            return null;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return jsonObject.getString("userid");
        }
        return null;
    }

    @Override
    public String getCorpsecretByAgentId(String agentId){
        JSONObject jsonObject=JSON.parseObject(Global.agentIdSecretJson);
        return jsonObject.getString(agentId);
    }

    /**
     * 创建user
     * @param qiYeUser
     * @return
     */
    @Override
    public boolean createUser(QiYeUser qiYeUser) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return false;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/create?access_token="+accessToken;
        if(StrKit.isBlank(qiYeUser.getEmail())){
            qiYeUser.setEmail(qiYeUser.getUserId()+Global.defaultQiYeEmailSuffix);
        }

        String dataStr="{\"department\":"+Arrays.toString(qiYeUser.getDepartment())+",\"mobile\":\""
                +qiYeUser.getMobile()+"\",\"gender\":\""+qiYeUser.getGender()+"\",\"name\":\""+qiYeUser.getName()+"\",\"userid\":\""+qiYeUser.getUserId()
                +"\",\"position\":\""+qiYeUser.getPosition()+"\"";
        if(StrKit.notBlank(qiYeUser.getEmail())){
            dataStr+=",\"email\":\""+qiYeUser.getEmail()+"\"";
        }
        if(qiYeUser.getDirectLeader()!=null && qiYeUser.getDirectLeader().length>0){
            dataStr+=",\"direct_leader\":"+JSON.toJSONString(qiYeUser.getDirectLeader());
        }

        dataStr+="}";
        String resultData=HttpKit.post(url,dataStr);

        if(!isJsonStr(resultData)){
            return false;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            logger.info("创建企业微信用户参数：data:"+dataStr+",msg:"+resultData);
            return true;
        }
        logger.info("创建企业微信用户失败：data:"+dataStr+",msg:"+resultData);
        return false;
    }

    public static void main(String[] args) {
        QiYeUser qiYeUser=new QiYeUser();
        qiYeUser.setDirectLeader(new String[]{"CS1"});
        qiYeUser.setDepartment(new int[]{2,3});
        qiYeUser.setMobile("1333333333");
        qiYeUser.setGender("mela");
        qiYeUser.setName("啊啊啊");
        qiYeUser.setUserId("CS1111");
        qiYeUser.setPosition("保安");
        qiYeUser.setEmail("<EMAIL>");

        String dataStr="{\"department\":"+Arrays.toString(qiYeUser.getDepartment())+",\"mobile\":\""
                +qiYeUser.getMobile()+"\",\"gender\":\""+qiYeUser.getGender()+"\",\"name\":\""+qiYeUser.getName()+"\",\"userid\":\""+qiYeUser.getUserId()
                +"\",\"position\":\""+qiYeUser.getPosition()+"\"";
        if(StrKit.notBlank(qiYeUser.getEmail())){
            dataStr+=",\"email\":\""+qiYeUser.getEmail()+"\"";
        }
        if(qiYeUser.getDirectLeader()!=null && qiYeUser.getDirectLeader().length>0){
            dataStr+=",\"direct_leader\":"+JSON.toJSONString(qiYeUser.getDirectLeader());;
        }

        dataStr+="}";
        System.out.println(dataStr);
    }


    /**
     * 修改user
     * @param qiYeUser
     * @return
     */
    @Override
    public boolean updateUser(QiYeUser qiYeUser) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return false;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/update?access_token="+accessToken;

        if(StrKit.isBlank(qiYeUser.getEmail())){
            qiYeUser.setEmail(qiYeUser.getUserId()+Global.defaultQiYeEmailSuffix);
        }

        String dataStr="{\"department\":"+Arrays.toString(qiYeUser.getDepartment())+",\"gender\":\""+qiYeUser.getGender()+"\",\"mobile\":\""+qiYeUser.getMobile()+"\",\"name\":\""+qiYeUser.getName()+"\",\"userid\":\""+qiYeUser.getUserId()+"\"";

        if(StrKit.notBlank(qiYeUser.getEmail())){
            dataStr+=",\"email\":\""+qiYeUser.getEmail()+"\"";
        }

        if(StrKit.notBlank(qiYeUser.getPosition())){
            dataStr+=",\"position\":\""+qiYeUser.getPosition()+"\"";
        }

        if(StrKit.notBlank(qiYeUser.getNewUserId())){
            dataStr+=",\"new_userid\":\""+qiYeUser.getNewUserId()+"\"";
        }
        if(qiYeUser.getDirectLeader()!=null && qiYeUser.getDirectLeader().length>0){
            dataStr+=",\"direct_leader\":"+JSON.toJSONString(qiYeUser.getDirectLeader());
        }

        dataStr+="}";
        String resultData=HttpKit.post(url,dataStr);
        if(!isJsonStr(resultData)){
            return false;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return true;
        }
        logger.info("修改企业微信用户失败：data:"+JSON.toJSONString(qiYeUser)+",msg:"+resultData);
        return false;
    }

    /**
     * 修改user
     * @param qiYeUser
     * @return
     */
    @Override
    public boolean updateUserDept(QiYeUser qiYeUser) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return false;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/update?access_token="+accessToken;



        String dataStr="{\"department\":"+Arrays.toString(qiYeUser.getDepartment())+"\",\"userid\":\""+qiYeUser.getUserId();



        dataStr+="\"}";
        String resultData=HttpKit.post(url,dataStr);
        if(!isJsonStr(resultData)){
            return false;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return true;
        }
        logger.info("修改企业微信用户部门失败：data:"+JSON.toJSONString(qiYeUser)+",msg:"+resultData);
        return false;
    }

    /**
     * 删除user
     * @param userId
     * @return
     */
    @Override
    public boolean delUser(String userId) {
        String accessToken=getAccessToken();
        if(StrKit.isBlank(accessToken)){
            return false;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/delete?access_token="+accessToken+"&userid="+userId;
        String resultData=HttpKit.post(url,null);
        if(!isJsonStr(resultData)){
            return false;
        }
        JSONObject jsonObject=JSON.parseObject(resultData);
        if(jsonObject.containsKey("errcode") && jsonObject.getInteger("errcode")==0){
            return true;
        }
        logger.info("删除企业微信用户失败：id:"+userId+",msg:"+resultData);
        return false;
    }

    /**
     * 获取打卡记录
     * @param qiyeIds
     * @return
     */
    @Override
    public String getCheckinData(Object[] qiyeIds,long startTime,long endTime) {
        String accessToken=getCheckinAccessToken();
        if(StrKit.isBlank(accessToken)){
            return null;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckindata?access_token="+accessToken;
        String params="{\"opencheckindatatype\":3,\"starttime\":"+startTime+",\"endtime\":"+endTime+",\"useridlist\":"+JSON.toJSONString(qiyeIds)+"}";
        String resultStr=HttpKit.post(url,params);
        return resultStr;
    }


    @Override
    public Map<String,Object> getCheckinOption(String qiyeUserId, long dateTime){
        String accessToken=getCheckinAccessToken();
        Map<String,Object> resultMap=new HashMap<>();
        if(StrKit.isBlank(accessToken)){
            return resultMap;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckinoption?access_token="+accessToken;
        String params="{\"datetime\":"+dateTime+",\"useridlist\":"+JSON.toJSONString(new String[]{qiyeUserId})+"}";
        String resultStr=HttpKit.post(url,params);
        if(!resultStr.startsWith("{") || !resultStr.endsWith("}")){
            return resultMap;
        }
        JSONObject jsonObject=JSON.parseObject(resultStr);
        if(jsonObject==null || jsonObject.getIntValue("errcode")!=0){
            return resultMap;
        }
        JSONArray infoArray=jsonObject.getJSONArray("info");
        if(infoArray!=null && infoArray.size()==1){
            JSONObject info=infoArray.getJSONObject(0);
            JSONObject group=info.getJSONObject("group");
            int groupType=group.getIntValue("grouptype");
            resultMap.put("groupType",groupType);
            resultMap.put("workDays",group.getJSONArray("checkindate").getJSONObject(0).getJSONArray("workdays"));
            resultMap.put("noneedOffwork",group.getJSONArray("checkindate").getJSONObject(0).getBooleanValue("noneed_offwork"));
        }
        return resultMap;
    }

    @Override
    public String getCheckinMonthData(Object[] qiyeIds,long startTime,long endTime) {
        String accessToken=getCheckinAccessToken();
        if(StrKit.isBlank(accessToken)){
            return null;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_monthdata?access_token="+accessToken;
        String params="{\"starttime\":"+startTime+",\"endtime\":"+endTime+",\"useridlist\":"+JSON.toJSONString(qiyeIds)+"}";
        String resultStr=HttpKit.post(url,params);
        return resultStr;
    }

    @Override
    public String getCheckinDayData(Object[] qiyeIds,long startTime,long endTime) {
        String accessToken=getCheckinAccessToken();
        if(StrKit.isBlank(accessToken)){
            return null;
        }
        String url="https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata?access_token="+accessToken;
        String params="{\"starttime\":"+startTime+",\"endtime\":"+endTime+",\"useridlist\":"+JSON.toJSONString(qiyeIds)+"}";
        String resultStr=HttpKit.post(url,params);
        return resultStr;
    }

    @Override
    public List<String> test(){

        String token=getAccessToken();
        if(StrKit.isBlank(token)){
            return null;
        }

        List<String> returnList=new ArrayList<>();
        String url="https://qyapi.weixin.qq.com/cgi-bin/user/list?access_token="+token+"&department_id="+1+"&fetch_child=1";
        String resultData=HttpKit.post(url,null);
        if(resultData.startsWith("{") && resultData.endsWith("}")){
            JSONObject jsonObject=JSON.parseObject(resultData);
            JSONArray array=jsonObject.getJSONArray("userlist");
            if(array!=null && array.size()>0){
                for(int i=0;i<array.size();i++){
                    JSONObject object=array.getJSONObject(i);
                    String mobile=object.getString("mobile");
                    //通过手机号码查询到人事档案
                    String id= Db.queryStr("select id from pers_org_employee where phone_num=? and del_flag='0'",mobile);
                    if(StrKit.notBlank(id)){
                        Db.update("update pers_org_employee set qiye_userid=? where id=?",object.getString("userid"),id);
                    }else{
                        returnList.add(object.getString("userid"));
                    }
                }
            }

        }

        return returnList;
    }

    public List<String> test2(){
       /* String sql="select b.qiye_id,a.phone_num,a.full_name,d.user_name from pers_org_employee a " +
                "inner join pers_org b on a.dept_id=b.id " +
                "inner join pers_emp_user c on c.emp_id=a.id " +
                "inner join sys_user d on d.id=c.user_id " +
                "where a.del_flag='0' and a.archive_status='incumbency' and (a.qiye_userid is null or a.qiye_userid='' ) " +
                "and d.del_flag='0' ";*/
        String sql="select * from qiye_user_table " +
                "where user_name in ( " +
                "'CS01239','CS00906','CS12650','CS00964','CS01065','CS12518','CS00215','CS12718','CS12765','CS12345','CS00616','CS12815','CS01118','CS00309','CS00855','CS00420','CS12811','CS12374','CS12423','CS12289','CS12536','CS12588','CS12515','CS00290','CS12395','CS01003','CS12563','CS12449','CS00268','CS12554','CS01119','CS00745','CS12308','CS12523','CS12652','CS00337','CS00843','CS01171','CS12625','CS00750','CS01013','CS12607','CS12666','CS00241','CS00325','CS00496','CS01247','CS00742','CS12772','CS00816','CS12546','CS12622','CS12614','CS12431','CS12742','CS12321','CS00544','CS00867','CS12295','CS12467','CS01140','CS01070','CS00554','CS12540','CS01023','CS12450','CS12649','CS00251','CS01030','CS12735','CS12694','CS12796','CS12475','CS01077','CS01006','CS01015','CS01120','CS12639','CS00399','CS00396','CS12364','CS00928','CS01147','CS12665','CS 01122','CS12265','CS12744','CS00403'\n" +
                ")";
        List<Record> eRecord=Db.find(sql);
        List<String> returnList=new ArrayList<>();
        for(Record r:eRecord){
            QiYeUser user=new QiYeUser();
            user.setUserId(r.getStr("user_name"));
            user.setMobile(r.getStr("phone_num"));
            user.setName(r.getStr("full_name"));
            user.setDepartment(new int[]{r.getInt("qiye_id")});
            boolean flag=createUser(user);
            if(!flag){
                returnList.add(user.getUserId());
            }
        }
        return returnList;
    }


    @Override
    public Map<String,Object> createChat(String deptId) {
        String token=getAccessToken();
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        if(StrKit.isBlank(token)|| true){
            resultMap.put("msg","access_token为空");
            return resultMap;
        }
        PersOrg org = persOrgService.findById(deptId);
        PersOrgEmployee employee=new PersOrgEmployee();
        employee.setDeptId(deptId);
        //获取负责人的员工档案信息
        Record empUserRecord = persOrgEmployeeService.getByUserId(org.getLinkMan());
        String ownerEmpId = empUserRecord.getStr("emp_id");
        PersOrgEmployee ownerEmp = persOrgEmployeeService.findById(ownerEmpId);


        Page<PersOrgEmployee> employeePage = persOrgEmployeeService.paginateByCondition(employee, null, null, "1", null, null, null
                , null, null, null, null, 1, 20000);
        List<String> userList=new ArrayList<>();
        if(employeePage.getList()!=null){
            for (PersOrgEmployee persOrgEmployee : employeePage.getList()) {
                if(StrKit.notBlank(persOrgEmployee.getQiyeUserid()) && !userList.contains(persOrgEmployee.getQiyeUserid())){
                    userList.add(persOrgEmployee.getQiyeUserid());
                }
            }
        }
        if(!userList.contains(ownerEmp.getQiyeUserid())){
            userList.add(ownerEmp.getQiyeUserid());
        }

        String url="https://qyapi.weixin.qq.com/cgi-bin/appchat/create?access_token="+token;
        JSONObject params=new JSONObject();
        params.put("name",org.getOrgName()+"员工群");
        params.put("owner",ownerEmp.getQiyeUserid());
        params.put("userlist",userList);
        String resultStr=HttpKit.post(url,JSON.toJSONString(params));
        if(!resultStr.startsWith("{") || !resultStr.endsWith("}")){
            return resultMap;
        }
        JSONObject jsonObject=JSON.parseObject(resultStr);
        if(jsonObject==null || jsonObject.getIntValue("errcode")!=0){
            resultMap.put("msg",jsonObject.getString("errmsg"));
            return resultMap;
        }
        org.setQiyeChatid(jsonObject.getString("chatid"));
        org.setUpdateDate(new Date());
        org.update();
        if(org.update()){
            resultMap.put("flag",true);
        }
        return resultMap;
    }

    @Override
    public Map<String,Object> updateChat(String chatid,String name,String owner,List<String> addUserIdList,List<String> delUserIdList) {
        String token=getAccessToken();
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        if(StrKit.isBlank(token) || true){
            resultMap.put("msg","access_token为空");
            return resultMap;
        }

        String url="https://qyapi.weixin.qq.com/cgi-bin/appchat/update?access_token="+token;
        JSONObject params=new JSONObject();
        params.put("chatid",chatid);
        if(StrKit.notBlank(name)){
            params.put("name",name);
        }
        if(StrKit.notBlank(owner)){
            params.put("owner",owner);
        }
        if(addUserIdList!=null && addUserIdList.size()>0){
            params.put("add_user_list",addUserIdList);
        }
        if(delUserIdList!=null && delUserIdList.size()>0){
            params.put("del_user_list",delUserIdList);
        }

        String resultStr=HttpKit.post(url,JSON.toJSONString(params));
        if(!resultStr.startsWith("{") || !resultStr.endsWith("}")){
            resultMap.put("msg",resultStr);
            return resultMap;
        }
        JSONObject jsonObject=JSON.parseObject(resultStr);
        if(jsonObject==null || jsonObject.getIntValue("errcode")!=0){
            resultMap.put("msg",jsonObject.getString("errmsg"));
            return resultMap;
        }
        resultMap.put("flag",true);
        return resultMap;
    }
}
