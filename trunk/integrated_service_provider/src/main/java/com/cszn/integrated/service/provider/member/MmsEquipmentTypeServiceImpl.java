package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsEquipmentTypeService;
import com.cszn.integrated.service.entity.member.MmsEquipmentType;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/26
 **/
@Bean
public class MmsEquipmentTypeServiceImpl extends JbootServiceBase<MmsEquipmentType> implements MmsEquipmentTypeService {



    @Override
    public Page<MmsEquipmentType> findListPage(Integer pageNumber, Integer pageSize, MmsEquipmentType equipmentType) {
        List<Object> params = Lists.newArrayList();
        String sql = "FROM mms_equipment_type WHERE del_flag = 0 ";
        if(equipmentType != null){
            if(StringUtils.isNotBlank(equipmentType.getManufactor())){
                sql += " AND manufactor LIKE CONCAT('%',?,'%') ";
                params.add(equipmentType.getManufactor());

            }
            if(StringUtils.isNotBlank(equipmentType.getEquipmentModel())){
                sql += " AND equipment_model LIKE CONCAT('%',?,'%') ";
                params.add(equipmentType.getEquipmentModel());
            }
            if(StringUtils.isNotBlank(equipmentType.getEquipmentType())){
                sql += " AND equipment_type = ? ";
                params.add(equipmentType.getEquipmentType());
            }
        }
        sql += "ORDER BY update_date DESC";
        return DAO.paginate(pageNumber,pageSize,"SELECT * ",sql,params.toArray());
    }



    @Override
    public String saveEquipmentType(MmsEquipmentType equipmentType, String userId) {
        if(getDistinct(equipmentType) == null)return "";
        if(StringUtils.isBlank(equipmentType.getId())){
            equipmentType.setId(IdGen.getUUID());
            equipmentType.setDelFlag("0");
            equipmentType.setCreateBy(userId);
            equipmentType.setCreateDate(new Date());
            equipmentType.setUpdateBy(userId);
            equipmentType.setUpdateDate(new Date());
            if(equipmentType.save()){
                return "suc";
            }else{
                return null;
            }
        }else{
            equipmentType.setUpdateBy(userId);
            equipmentType.setUpdateDate(new Date());
            if(equipmentType.update()){
                return "suc";
            }else{
                return null;
            }
        }
    }




    @Override
    public boolean delEquipmentType(String id, String userId) {
        MmsEquipmentType equipmentType =  new MmsEquipmentType();
        equipmentType.setId(id);
        equipmentType.setDelFlag("1");
        equipmentType.setUpdateBy(userId);
        equipmentType.setUpdateDate(new Date());
        return equipmentType.update();
    }



    @Override
    public MmsEquipmentType getEquipmentTypeByModel(String equipmentModel) {
        return DAO.findFirst("SELECT * FROM mms_equipment_type WHERE del_flag = 0 AND equipment_model = ?",equipmentModel);
    }



    @Override
    public String getDistinct(MmsEquipmentType equipmentType) {
        List<MmsEquipmentType> list = null;
        if(StringUtils.isBlank(equipmentType.getId())){
            list = DAO.find("SELECT * FROM mms_equipment_type WHERE del_flag = 0 AND equipment_model = ? AND equipment_type = ?",equipmentType.getEquipmentModel(),equipmentType.getEquipmentType());
        }else{
            list = DAO.find("SELECT * FROM mms_equipment_type WHERE del_flag = 0 AND equipment_model = ? AND equipment_type = ? AND id != ?",equipmentType.getEquipmentModel(),equipmentType.getEquipmentType(),equipmentType.getId());
        }
        if((list != null && list.size() >0 ))return null;
        return "";
    }




    @Override
    public List<Record> getEquipmentTypeList() {
        return Db.find("SELECT t.id,t.manufactor,t.equipment_type AS equipmentType,d.dict_name AS equipmentTypeName, t.equipment_classify AS equipmentClassify,t.equipment_model AS equipmentModel," +
                "t.equipment_color AS equipmentColor,t.remark,t.del_flag AS delFlag,t.create_by AS createBy,t.create_date AS createDate,t.update_by AS updateBy,t.update_date AS updateDate " +
                "FROM mms_equipment_type t LEFT JOIN sys_dict d ON t.equipment_type = d.dict_value WHERE t.del_flag = 0");
    }




    @Override
    public Record getTypeRecord(String id) {
        return Db.findFirst("SELECT t.id,t.manufactor,t.equipment_type AS equipmentType,d.dict_name AS equipmentTypeName, t.equipment_classify AS equipmentClassify,t.equipment_model AS equipmentModel," +
                "t.equipment_color AS equipmentColor,t.remark,t.del_flag AS delFlag,t.create_by AS createBy,t.create_date AS createDate,t.update_by AS updateBy,t.update_date AS updateDate " +
                "FROM mms_equipment_type t LEFT JOIN sys_dict d ON t.equipment_type = d.dict_value WHERE t.id = ?",id);
    }



    @Override
    public MmsEquipmentType get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }
}
