package com.cszn.integrated.service.provider.member;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsWxArticleService;
import com.cszn.integrated.service.entity.member.MmsWxArticle;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

/**
 * 微信文章管理
 */
@Bean
public class MmsWxArticleServiceImpl extends JbootServiceBase<MmsWxArticle> implements MmsWxArticleService {

    /**
     * 获取分页
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsWxArticle> findList(Integer pageNumber, Integer pageSize, MmsWxArticle wxArticle){

        List<Object> param = new ArrayList<Object>();
        String sql = "FROM mms_wx_article WHERE del_flag = 0 ";
        if(wxArticle != null) {
            if (StringUtils.isNotBlank(wxArticle.getTitle())) {
                sql += "AND title LIKE CONCAT('%',?,'%') ";
                param.add(wxArticle.getTitle().trim());
            }
        }
        sql += "ORDER BY update_time DESC";
        return DAO.paginate(pageNumber,pageSize,"SELECT * ",sql,param.toArray());
    }

    /**
     * 删除
     * @param id
     * @param userId
     * @return
     */
    public boolean delWxArticle(String id,String userId){
        MmsWxArticle wxArticle = DAO.findById(id);
        if(wxArticle == null) return false;

        wxArticle.setDelFlag("1");
        wxArticle.setUpdateBy(userId);
        wxArticle.setUpdateTime(new Date());
        return wxArticle.update();
    }

    /**
     * 保存
     * @param wxArticle
     * @param userId
     * @return
     */
    public boolean saveWxArticle(MmsWxArticle wxArticle,String userId){

        if(wxArticle != null){
            if(StringUtils.isBlank(wxArticle.getId())){
                wxArticle.setId(IdGen.getUUID());
                wxArticle.setDelFlag("0");
                wxArticle.setCreateBy(userId);
                wxArticle.setCreateTime(new Date());
                wxArticle.setUpdateBy(userId);
                wxArticle.setUpdateTime(new Date());
                return wxArticle.save();
            }else{
                wxArticle.setUpdateBy(userId);
                wxArticle.setUpdateTime(new Date());
                return wxArticle.update();
            }
        }
        return false;
    }

    public Page<MmsWxArticle> getWxArticleList(Integer pageNumber,Integer pageSize){

        String sql = "FROM mms_wx_article WHERE del_flag = 0 ORDER BY create_time DESC";
        Page<MmsWxArticle> page = DAO.paginate(pageNumber,pageSize,"SELECT * ",sql);
        if(page == null){
            return null;
        }else{
            return page;
        }
    }

    public boolean batchDelArticle(List<MmsWxArticle> list,String userId){
        boolean flag = false;
        if(list != null && list.size() > 0){
            for (MmsWxArticle item : list) {
                if(StringUtils.isNotBlank(item.getId())) {
                    MmsWxArticle articleExist = DAO.findById(item.getId());
                    if(articleExist != null) {
                        MmsWxArticle article = new MmsWxArticle();
                        article.setId(item.getId());
                        article.setDelFlag("1");
                        article.setUpdateBy(userId);
                        article.setUpdateTime(new Date());
                        flag = article.update();
                    }
                }
            }
        }
        return flag;
    }




    @Override
    public MmsWxArticle get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }
}