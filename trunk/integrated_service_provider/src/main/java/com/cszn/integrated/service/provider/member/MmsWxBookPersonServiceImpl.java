package com.cszn.integrated.service.provider.member;

import java.util.Date;
import java.util.List;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsWxBookPersonService;
import com.cszn.integrated.service.entity.member.MmsWxBookPerson;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

/**
 *常用入住人列表
 */
@Bean
public class MmsWxBookPersonServiceImpl extends JbootServiceBase<MmsWxBookPerson> implements MmsWxBookPersonService {

    public List<MmsWxBookPerson> getWxBookPersons(String userId){
        String sql = "SELECT * FROM mms_wx_book_person WHERE user_id = ? ORDER BY create_time ASC";
        return DAO.find(sql,userId);
    }

    public String  saveWxBookPerson(MmsWx<PERSON>ook<PERSON>erson person){
        String sql = "SELECT COUNT(*) FROM mms_wx_book_person WHERE idcard = ? and user_id = ?";
        Long count = Db.queryLong(sql,person.getIdcard(),person.getUserId());
        if(count > 0) return "";
        person.setId(IdGen.getUUID());
        person.setCreateTime(new Date());
        if(person.save()){
            return "suc";
        }else{
            return null;
        }
    }

    public boolean delWxBookPerson(String id){
        MmsWxBookPerson person = DAO.findById(id);
        if(person == null) return false;
        return DAO.deleteById(id);
    }
}