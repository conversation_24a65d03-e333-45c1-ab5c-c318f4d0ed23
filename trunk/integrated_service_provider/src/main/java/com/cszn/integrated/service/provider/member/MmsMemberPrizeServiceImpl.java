package com.cszn.integrated.service.provider.member;


import com.cszn.integrated.service.api.member.MmsMemberPrizeService;
import com.cszn.integrated.service.entity.member.MmsMemberPrize;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MmsMemberPrizeServiceImpl extends JbootServiceBase<MmsMemberPrize> implements MmsMemberPrizeService {




    @Override
    public Page<Record> findPage(Integer pageNumber,Integer pageSize,MmsMemberPrize memberPrize) {
        List<Object> params = new ArrayList<>();
        String sqlSel = "select m.id,m.prize_id as prizeId,m.card_number as cardNumber,m.full_name as fullName," +
                "m.telephone,m.is_received as isReceived,m.create_by as createBy,m.create_time as createTime,m.update_by as updateBy," +
                "m.update_time as updateTime,m.remark,p.prize,p.prize_flag as prizeFlag ";
        String sql = "from mms_member_prize m left join mms_prize p on m.prize_id = p.id " +
                "where m.del_flag = 0 ";
        if(StringUtils.isNotBlank(memberPrize.getCardNumber())){
            sql += "and m.card_number like concat('%',?,'%') ";
            params.add(memberPrize.getCardNumber());
        }
        if(StringUtils.isNotBlank(memberPrize.getFullName())){
            sql += "and m.full_name like concat('%',?,'%') ";
            params.add(memberPrize.getFullName());
        }
        sql += "order by m.update_time desc";

        return Db.paginate(pageNumber,pageSize,sqlSel,sql,params.toArray());
    }



    @Override
    public boolean delEleMember(String id, String userId) {
        MmsMemberPrize memberPrize = DAO.findById(id);
        if(memberPrize == null)return false;

        memberPrize.setDelFlag("1");
        memberPrize.setUpdateBy(userId);
        memberPrize.setUpdateTime(new Date());
        if(memberPrize.update()){
            return true;
        }else{
            return false;
        }
    }


    @Override
    public List<Record> getMemberPrizes() {
        return Db.find("select m.id,m.card_number as cardNumber,m.full_name as fullName,m.telephone,m.prize_id as prizeId," +
                "m.is_received as isReceived,m.create_by as createBy,m.create_time as createTime,m.update_by as updateBy,m.update_time as updateTime," +
                "m.remark,p.prize,p.prize_flag as prizeFlag " +
                "from mms_member_prize m left join mms_prize p on m.prize_id = p.id " +
                "where m.del_flag = 0 order by m.update_time desc");
    }




    @Override
    public Record getMemberPrize(String id) {
        return Db.findFirst("select m.id,m.card_number as cardNumber,m.full_name as fullName,m.telephone,m.prize_id as prizeId," +
                "m.is_received as isReceived,m.create_by as createBy,m.create_time as createTime,m.update_by as updateBy,m.update_time as updateTime," +
                "m.remark,p.prize,p.prize_flag as prizeFlag " +
                "from mms_member_prize m left join mms_prize p on m.prize_id = p.id " +
                "where m.del_flag = 0 and m.id = ?",id);
    }

}
