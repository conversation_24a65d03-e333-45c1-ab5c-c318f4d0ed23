package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsCustomerAssignService;
import com.cszn.integrated.service.entity.member.MmsCustomerAssign;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsCustomerAssignServiceImpl extends JbootServiceBase<MmsCustomerAssign> implements MmsCustomerAssignService {

	@Override
	public MmsCustomerAssign findByAssignCustomerId(String customerId) {
		Columns columns = Columns.create("customer_id", customerId);
		return DAO.findFirstByColumns(columns);
	}

}
