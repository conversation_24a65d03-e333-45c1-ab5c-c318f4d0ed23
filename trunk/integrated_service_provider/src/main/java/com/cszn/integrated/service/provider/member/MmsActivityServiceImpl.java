package com.cszn.integrated.service.provider.member;

import java.util.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsActivityService;
import com.cszn.integrated.service.entity.main.MainSmsConditionList;
import com.cszn.integrated.service.entity.member.MmsActivity;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.google.common.collect.Lists;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsActivityServiceImpl extends JbootServiceBase<MmsActivity> implements MmsActivityService {

	public Page<MmsActivity> paginateByCondition(MmsActivity model, int pageNumber, int pageSize) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if (StrKit.notBlank(model.getActivityName())) {
			columns.like("activity_name", "%" + model.getActivityName() + "%");
		}
		if(StrKit.notBlank(model.getIsEnable())){
			columns.eq("is_enable", model.getIsEnable());
		}
		if(StrKit.notBlank(model.getCreateBy())) {
			columns.eq("create_by", model.getCreateBy());
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "create_time desc");
	}
	
	
	@Override
	public Page<MmsActivity> paginateByCondition(String nowDate, int pageNumber, int pageSize) {
		
		List<Object> params = Lists.newArrayList();
		String mainSql=" from mms_activity where del_flag='0' and is_enable='0' ";
		
		if(StrKit.notBlank(nowDate)) {
			mainSql += "and DATE_FORMAT(create_time,'%Y-%m-%d') = ? ";
            params.add(nowDate);
		}
		mainSql+=" order by create_time desc ";
		
		return DAO.paginate(pageNumber,pageSize,"select * ",mainSql,params.toArray());
	}

	@Override
	public MmsActivity getActivityByQrcode(String qrcode) {
		return DAO.findFirst("select * from mms_activity where activity_qrcode=?  and del_flag='0' ",qrcode);
	}

	@Override
	public void bmpInvokeGetForm(String formId) {
		Map<String,String> paramMap=new HashMap<>();
		paramMap.put("formSubmitId",formId);
		final String returnStr = HttpClientsUtils.httpPostForm(Global.sojournUrl+"Internal/GetFormSubmit",paramMap,null,null);
		if(StrKit.notBlank(returnStr)){
			if(returnStr.startsWith("{") && returnStr.endsWith("}")){
				JSONObject returnObj = JSON.parseObject(returnStr);
				if(returnObj.containsKey("Type") && returnObj.containsKey("Data") && returnObj.getInteger("Type")==1){
					JSONObject formData = returnObj.getJSONObject("Data");
					if(formData!=null && !formData.isEmpty()){
						final String submitDate = formData.getString("SubmitDate");
						JSONArray submitFieldArray = formData.getJSONArray("SubmitFieldList");
						MmsActivity model = new MmsActivity();
						model.setId(IdGen.getUUID());
						model.setApplyFormId(formId);
						model.setApplyTime(DateUtils.parseDate(submitDate));
						model.setIsEnable("0");
						model.setDelFlag(DelFlag.NORMAL);
						for (int i = 0; i < submitFieldArray.size(); i++) {
							JSONObject fieldObj = submitFieldArray.getJSONObject(i);
							final String fieldName = fieldObj.getString("Name");
							final String fieldValue = fieldObj.getString("Value");
							//申请人
							if("Submiter".equalsIgnoreCase(fieldName)
									|| "SubmitDept".equalsIgnoreCase(fieldName)
									|| "ActivityType".equalsIgnoreCase(fieldName)
									|| "BaseId".equalsIgnoreCase(fieldName)
							){
								JSONObject fieldValueObj = fieldObj.getJSONObject("Value");
								if(fieldValueObj!=null && !fieldValueObj.isEmpty()){
									final String fieldValueText = fieldValueObj.getString("Text");
									final String fieldValueValue = fieldValueObj.getString("Value");
									if("Submiter".equalsIgnoreCase(fieldName)){
										model.setApplyId(fieldValueValue.toUpperCase());
										model.setApplyName(fieldValueText);
										model.setCreateBy(fieldValueValue.toUpperCase());
										model.setCreateTime(new Date());
										model.setUpdateBy(fieldValueValue.toUpperCase());
										model.setUpdateTime(new Date());
									}else if("SubmitDept".equalsIgnoreCase(fieldName)){
										model.setDepartmentId(fieldValueValue.toUpperCase());
										model.setDepartmentName(fieldValueText);
									}else if("ActivityType".equalsIgnoreCase(fieldName)){
										model.setActivityType(fieldValueValue);
									}else if("BaseId".equalsIgnoreCase(fieldName)){
										model.setBaseId(fieldValueValue.toUpperCase());
									}
								}
							}
							//活动名称
							if("ActivityName".equalsIgnoreCase(fieldName)){
								model.setActivityName(fieldValue);
							}
							//活动开始时间
							if("StartTime".equalsIgnoreCase(fieldName)){
								model.setStartTime(DateUtils.parseDate(fieldValue));
							}
							//活动结束时间
							if("EndTime".equalsIgnoreCase(fieldName)){
								model.setEndTime(DateUtils.parseDate(fieldValue));
							}
							//备注
							if("Remark".equalsIgnoreCase(fieldName)){
								model.setRemark(fieldValue);
							}
						}
						model.save();
					}
				}
			}
		}
	}
}