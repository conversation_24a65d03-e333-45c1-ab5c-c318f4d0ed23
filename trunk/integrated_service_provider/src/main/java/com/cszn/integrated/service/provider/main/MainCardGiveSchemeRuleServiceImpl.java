package com.cszn.integrated.service.provider.main;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainCardGiveSchemeRuleService;
import com.cszn.integrated.service.api.main.MainCardGiveSchemeService;
import com.cszn.integrated.service.entity.enums.GiveSchemeRuleCycle;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainCardGiveRecord;
import com.cszn.integrated.service.entity.main.MainCardGiveScheme;
import com.cszn.integrated.service.entity.main.MainCardGiveSchemeRule;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MainCardGiveSchemeRuleServiceImpl extends JbootServiceBase<MainCardGiveSchemeRule> implements MainCardGiveSchemeRuleService {

    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private MainCardGiveSchemeService mainCardGiveSchemeService;

    @Override
    public void giveDisposableGiveRule(String cardId, String schemeId) {
        List<MainCardGiveSchemeRule> giveSchemeRuleList=findDisposableGiveRule(cardId,schemeId);
        if(giveSchemeRuleList.size()>0){
            FinaMembershipCard card=finaMembershipCardService.findById(cardId);
            Double totalGiveAmount=card.getGiveBalance();
            Double totalGiveTimes=card.getGiveConsumeTimes();
            List<MainCardGiveRecord> cardGiveRecordList=new ArrayList<>();
            for(MainCardGiveSchemeRule rule:giveSchemeRuleList){
                if(totalGiveAmount==null){
                    totalGiveAmount=rule.getGiveAmount();
                }else{
                    totalGiveAmount=new BigDecimal(totalGiveAmount).add(new BigDecimal(rule.getGiveAmount())).doubleValue();
                }
                if(totalGiveTimes==null){
                    totalGiveTimes=rule.getGiveTimes();
                }else{
                    totalGiveTimes=new BigDecimal(totalGiveTimes).add(new BigDecimal(rule.getGiveTimes())).doubleValue();
                }
                MainCardGiveRecord record=new MainCardGiveRecord();
                record.setId(IdGen.getUUID());
                record.setCardId(cardId);
                record.setGiveAmount(rule.getGiveAmount());
                record.setGiveTimes(rule.getGiveTimes());
                record.setCycle(rule.getCycle());
                record.setRuleId(rule.getId());
                record.setSchemeId(rule.getSchemeId());
                record.setGiveDate(new Date());
                cardGiveRecordList.add(record);
            }
            card.setGiveBalance(totalGiveAmount);
            card.setGiveConsumeTimes(totalGiveTimes);
            card.update();
            Db.batchSave(cardGiveRecordList,cardGiveRecordList.size());
        }
    }

    @Override
    public List<MainCardGiveSchemeRule> findDisposableGiveRule(String cardId, String schemeId) {
        String sql="select * from main_card_give_scheme_rule where scheme_id=? and cycle='disposable' and del_flag='0' ";
        if(StrKit.notBlank(cardId)){
            sql+="and id not in(select rule_id from main_card_give_record where card_id=? and cycle='disposable')";
            return DAO.find(sql,schemeId,cardId);
        }
        return DAO.find(sql,schemeId);
    }

    public List<MainCardGiveSchemeRule> findGiveSchemeRuleBySchemeId(String schemeId){

        return DAO.find("select * from main_card_give_scheme_rule where scheme_id=? and del_flag='0' order by start_seq ",schemeId);
    }


    @Override
    public Page<MainCardGiveSchemeRule> giveSchemeRulePage(Integer pageNumber, Integer pageSize, MainCardGiveSchemeRule schemeRule) {
        String sql="from main_card_give_scheme_rule where del_flag='0' ";
        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(schemeRule.getSchemeId())){
            sql+="and scheme_id=? ";
            params.add(schemeRule.getSchemeId());
        }
        return DAO.paginate(pageNumber,pageSize,"select * ",sql,params.toArray());
    }

    @Override
    public List<MainCardGiveSchemeRule> findGiveSchemeRuleList(String schemeId, String cycle) {
        String sql="select b.* from main_card_give_scheme a left join main_card_give_scheme_rule b on a.id=b.scheme_id " +
                "where a.id=? and a.del_flag='0' and b.del_flag='0'";
        List<Object> params=new ArrayList<>();
        params.add(schemeId);
        if(StrKit.notBlank(cycle)){
            sql+="and b.cycle=? ";
            params.add(cycle);
        }
        return DAO.find(sql,params.toArray());
    }


    @Override
    public List<Record> getCardGiveSchemeRuleResult(Date openTime,String schemeId){
        List<Record> recordList=new ArrayList<>();


        List<MainCardGiveSchemeRule> schemeRuleList=findGiveSchemeRuleBySchemeId(schemeId);
        MainCardGiveScheme scheme = mainCardGiveSchemeService.findById(schemeId);
        for (MainCardGiveSchemeRule schemeRule : schemeRuleList) {

            for (Integer i = schemeRule.getStartSeq(); i <= schemeRule.getEndSeq(); i++) {
                Record record=new Record();
                record.set("ruleId",schemeRule.getId());
                record.set("giveTimes",schemeRule.getGiveTimes());
                record.set("giveAmount",schemeRule.getGiveAmount());
                if(schemeRule.getGiveIntegrals()==null){
                    schemeRule.setGiveIntegrals(0.0);
                }
                record.set("giveIntegrals",schemeRule.getGiveIntegrals());
                record.set("seq",i);

                String unit="";

                String type="";
                if("1".equals(scheme.getType())){
                    type="分期赠送";
                }else if("2".equals(scheme.getType())){
                    type="分期充值";
                }

                String giveDateStr="";
                if(GiveSchemeRuleCycle.year.getKey().equals(schemeRule.getCycle())){
                    Date giveDate=DateUtils.getNextYear(openTime,i-1);
                    record.set("giveDate",DateUtils.formatDate(giveDate,"yyyy-MM-dd"));
                    //record.set("giveRemark","第"+i+GiveSchemeRuleCycle.year.getValue());
                    unit="年";
                    giveDateStr=DateUtils.formatDate(giveDate,"yyyy")+"年";
                }else if(GiveSchemeRuleCycle.season.getKey().equals(schemeRule.getCycle())){
                    Date giveDate=DateUtils.getNextMonth(openTime,(i-1)*3);
                    record.set("giveDate",DateUtils.formatDate(giveDate,"yyyy-MM-dd"));
                    //record.set("giveRemark","第"+i+GiveSchemeRuleCycle.season.getValue());
                    unit="季";
                    giveDateStr=DateUtils.formatDate(giveDate,"yyyy-MM")+"月";
                }else if(GiveSchemeRuleCycle.mon.getKey().equals(schemeRule.getCycle())){
                    Date giveDate=DateUtils.getNextMonth(openTime,i-1);
                    record.set("giveDate",DateUtils.formatDate(giveDate,"yyyy-MM-dd"));
                    //record.set("giveRemark","第"+i+GiveSchemeRuleCycle.mon.getValue());
                    unit="月";
                    giveDateStr=DateUtils.formatDate(giveDate,"yyyy-MM")+"月";
                }else if(GiveSchemeRuleCycle.day.getKey().equals(schemeRule.getCycle())){
                    Date giveDate=DateUtils.getNextDay(openTime,i-1);
                    record.set("giveDate",DateUtils.formatDate(giveDate,"yyyy-MM-dd"));
                    //record.set("giveRemark","第"+i+GiveSchemeRuleCycle.day.getValue());
                    unit="天";
                    giveDateStr=DateUtils.formatDate(giveDate,"yyyy-MM-dd")+"日";
                }
                String giveRemark=DateUtils.formatDate(openTime,"yyyy-MM-dd")+"开卡，"+giveDateStr+type+"第"+i+unit;
                if(schemeRule.getGiveTimes()>0){
                    giveRemark+=schemeRule.getGiveTimes()+"天";
                }
                if(schemeRule.getGiveAmount()>0){
                    giveRemark+=schemeRule.getGiveAmount()+"元";
                }
                if(schemeRule.getGiveIntegrals()>0){
                    giveRemark+=schemeRule.getGiveIntegrals()+"积分";
                }

                record.set("giveRemark",giveRemark);

                recordList.add(record);
            }
        }
        return recordList;
    }
}
