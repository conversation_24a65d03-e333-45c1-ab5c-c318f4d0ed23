package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsMemberVisitService;
import com.cszn.integrated.service.entity.member.MmsMemberVisit;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsMemberVisitServiceImpl extends JbootServiceBase<MmsMemberVisit> implements MmsMemberVisitService {

	public Page<MmsMemberVisit> paginateByCondition(MmsMemberVisit model, int pageNumber, int pageSize) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(model.getMemberId())){
			columns.add("member_id",model.getMemberId());
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "create_time asc");
	}
}