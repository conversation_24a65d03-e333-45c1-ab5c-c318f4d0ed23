package com.cszn.integrated.service.provider.mall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.*;
import com.cszn.integrated.service.api.crm.CrmCardRollRecordService;
import com.cszn.integrated.service.api.fina.FinaCardIntegralRecordService;
import com.cszn.integrated.service.api.fina.FinaCardRollService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.mall.*;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.api.wms.WmsStockModelService;
import com.cszn.integrated.service.api.wms.WmsStockModelWarehousePriceService;
import com.cszn.integrated.service.api.wms.WmsWarehouseService;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecord;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecordUse;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.enums.MallPaymentType;
import com.cszn.integrated.service.entity.enums.PosCardType;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.mall.*;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.status.ConsumeType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.wms.WmsStockModelWarehousePrice;
import com.cszn.integrated.service.entity.wms.WmsStockModels;
import com.cszn.integrated.service.entity.wms.WmsWarehouses;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.Jboot;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import io.jboot.support.redis.JbootRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

@Bean
public class MallOrderServiceImpl extends JbootServiceBase<MallOrder> implements MallOrderService {

    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private WmsStockModelService wmsStockModelService;
    @Inject
    private FinaCardIntegralRecordService finaCardIntegralRecordService;
    @Inject
    private SmsSendRecordService smsSendRecordService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;
    @Inject
    private MallActivityService mallActivityService;
    @Inject
    private MallActivityGiveConfigService mallActivityGiveConfigService;
    @Inject
    private MallOrderPaymentService mallOrderPaymentService;
    @Inject
    private MallOfflineCouponService mallOfflineCouponService;
    @Inject
    private WmsWarehouseService wmsWarehouseService;
    @Inject
    private WmsStockModelWarehousePriceService wmsStockModelWarehousePriceService;
    @Inject
    private CrmCardRollRecordService crmCardRollRecordService;
    @Inject
    private FinaCardRollService finaCardRollService;
    @Inject
    private MmsMemberService mmsMemberService;
    @Inject
    private MainBaseService mainBaseService;

    private JbootRedis jbootRedis= Jboot.getRedis();

    private static Logger logger = LoggerFactory.getLogger(MallOrderServiceImpl.class);


    @Override
    public Page<Record> findOrderByCreatePage(int pageNumber, int pageSize, MallOrder mallOrder) {
        String select="select a.id,a.status,a.card_number as cardNumber,a.order_no as orderNo,a.total_integral as totalIntegral,a.offline_payment_type as offlinePaymentType," +
                "a.reality_payment_integral as realityPaymentIntegral,a.reality_payment_times as realityPaymentTimes,a.reality_payment_card_amount as realityPaymentCardAmount" +
                ",a.total_integral as totalIntegral,a.payment_time as paymentTime,b.full_name as memberName,a.remark ";
        String sql=" from mall_order a left join mms_member b on a.user_id=b.id " +
                "where a.`type`='offline' and a.create_by=? order by a.create_date desc";
        Page<Record> page=Db.paginate(pageNumber,pageSize,select,sql,mallOrder.getCreateBy());
        if(page.getList()!=null){
            for(Record record:page.getList()){
                Double totalIntegral=record.getDouble("totalIntegral");
                Double realityPaymentIntegral=record.getDouble("realityPaymentIntegral");
                if("2".equals(record.getStr("offlinePaymentType"))) {
                    if (totalIntegral == null) {
                        record.set("totalIntegral", "0.0豆豆券");
                    } else {
                        record.set("totalIntegral", totalIntegral + "豆豆券");
                    }
                    if (realityPaymentIntegral == null) {
                        record.set("realityPaymentIntegral", "0.0豆豆券");
                    } else {
                        record.set("realityPaymentIntegral", realityPaymentIntegral + "豆豆券");
                    }

                }else if("4".equals(record.getStr("offlinePaymentType"))){
                    if(totalIntegral==null){
                        record.set("totalIntegral","0.0礼品券");
                    }else{
                        record.set("totalIntegral",totalIntegral+"礼品券");
                    }
                    if(realityPaymentIntegral==null){
                        record.set("realityPaymentIntegral","0.0礼品券");
                    }else{
                        record.set("realityPaymentIntegral",realityPaymentIntegral+"礼品券");
                    }

                }else{
                    if(totalIntegral==null){
                        record.set("totalIntegral","0.0积分");
                    }else{
                        record.set("totalIntegral",totalIntegral+"积分");
                    }
                    if(realityPaymentIntegral==null){
                        record.set("realityPaymentIntegral","0.0积分");
                    }else{
                        record.set("realityPaymentIntegral",realityPaymentIntegral+"积分");
                    }
                }

            }
        }
        return page;
    }

    @Override
    public Page<Record> findCashOrderByCreatePage(int pageNumber, int pageSize, MallOrder mallOrder) {
        String select="select a.id,a.status,a.order_no as orderNo,a.total_amount as totalAmount," +
                "a.reality_payment_amount as realityPaymentAmount,a.payment_time as paymentTime,a.remark,b.activity_name as activityName,c.`name` as createName,f.`name` as warehousesName ";
        String sql=" from mall_order a left join mall_activity  b on b.id=a.activity_id left join sys_user c on c.id=a.create_by" +
                " INNER JOIN mall_order_item e on e.id=(select id from mall_order_item where order_id=a.id limit 1) " +
                " left join wms_warehouses f on f.id=e.out_warehouses_id  " +
                "where a.`type`='offline_cash' ";

        List<String> warehouseIdList=Db.query("select warehouse_id from wms_warehouse_manager where user_id=? ",mallOrder.getCreateBy());


        if(warehouseIdList.size()==0){
            return new Page<Record>();
        }
        String str="";
        for (String warehouseId : warehouseIdList) {
            str+="?,";
        }
        str=str.substring(0,str.length()-1);
        sql+=" and f.id in ("+str+") ";


        sql+=" order by a.create_date desc ";

        Page<Record> recordPage=Db.paginate(pageNumber,pageSize,select,sql,warehouseIdList.toArray());
        if(recordPage.getList()==null || recordPage.getList().size()==0){
            return recordPage;
        }
        for(Record record:recordPage.getList()){
            List<MallOrderPayment> orderPaymentList=mallOrderPaymentService.getOrderPaymentList(record.getStr("id"));
            String payTypes="";
            for(MallOrderPayment orderPayment:orderPaymentList){
                MallPaymentType paymentType=MallPaymentType.aliPay.getTypeByKey(orderPayment.getPayType());
                if("6".equals(paymentType.getKey())){
                    MallOfflineCoupon offlineCoupon=mallOfflineCouponService.findById(orderPayment.getCouponId());
                    if(offlineCoupon!=null){
                        payTypes+= offlineCoupon.getName()+"："+orderPayment.getAmount()+"张;";
                    }
                }else{
                    payTypes+= paymentType.getValue()+"："+orderPayment.getAmount()+"元;";
                }
            }
            record.set("payType",payTypes);
        }
        return recordPage;
    }

    @Override
    public Page<Record> getAllOfflineOrder(int pageNumber, int pageSize,String warehouseId, String cardNumber, String paymentTime
            ,String createName,String remark,String orderNo,String paymentType,String userId) {
        String select="select a.id,a.status,a.card_number as cardNumber,a.order_no as orderNo,a.total_integral as totalIntegral," +
                "a.reality_payment_integral as realityPaymentIntegral,a.reality_payment_times as realityPaymentTimes,a.reality_payment_card_amount as realityPaymentCardAmount,a.payment_time as paymentTime,b.full_name as memberName" +
                ",a.remark,c.`name` as createName,a.offline_payment_type as offlinePaymentType,a.full_name as fullName,a.phone_number as phoneNumber,a.idcard" +
                ",f.`name` as warehouseName ";
        String sql=" from mall_order a left join mms_member b on a.user_id=b.id " +
                " left join sys_user c on c.id=a.create_by " +
                " INNER JOIN mall_order_item e on e.id=(select id from mall_order_item where order_id=a.id limit 1) " +
                " left join wms_warehouses f on f.id=e.out_warehouses_id  " +
                "where a.`type`='offline'  ";
        List<String> params=new ArrayList<>();
        if(StrKit.notBlank(cardNumber)){
            sql+=" and a.card_number=? ";
            params.add(cardNumber);
        }
        if(StrKit.notBlank(orderNo)){
            sql+=" and a.order_no=? ";
            params.add(orderNo);
        }
        if(StrKit.notBlank(paymentType)){
            sql+=" and a.offline_payment_type=? ";
            params.add(paymentType);
        }

        if(StrKit.notBlank(paymentTime)){
            sql+=" and to_days(a.payment_time)=to_days(?) ";
            params.add(paymentTime);
        }

        if(StrKit.notBlank(warehouseId)){
            sql+=" and e.out_warehouses_id=?  ";
            params.add(warehouseId);
        }else{
            List<WmsWarehouses> wmsWarehousesList=wmsWarehouseService.getAllWmsWarehouseByUserId(userId);
            if(wmsWarehousesList==null || wmsWarehousesList.size()==0){

                return new Page<Record>();
            }else{
                String str="";
                for(WmsWarehouses warehouses:wmsWarehousesList){
                    str+="?,";
                    params.add(warehouses.getId());
                }
                str=str.substring(0,str.length()-1);
                sql+=" and e.out_warehouses_id in("+str+") ";

            }

        }

        if(StrKit.notBlank(createName)){
            sql+=" and c.`name`=? ";
            params.add(createName);
        }

        if(StrKit.notBlank(remark)){
            sql+=" and a.remark like concat('%',?,'%') ";
            params.add(remark);
        }

        sql+=" order by a.create_date desc ";
        Page<Record> recordPage=Db.paginate(pageNumber,pageSize,select,sql,params.toArray());
        if(recordPage.getList()!=null && recordPage.getList().size()>0){
            for(Record record:recordPage.getList()){
                if("2".equals(record.getStr("offlinePaymentType"))){
                    List<Record> rollRecordUseList=Db.find("select * from crm_card_roll_record_use where join_id=? ORDER BY create_time desc",record.getStr("id"));

                    Map<String,Record> recordMap=new TreeMap<>();
                    for(Record rollRecordUse:rollRecordUseList){
                        record.set("fullName",rollRecordUse.getStr("full_name"));
                        record.set("idcard",rollRecordUse.getStr("idcard"));
                        record.set("phoneNumber",rollRecordUse.getStr("phone_number"));

                        if(recordMap.containsKey(rollRecordUse.getStr("card_number"))){
                            Record cardInfo=recordMap.get(rollRecordUse.getStr("card_number"));
                            Double beanCoupons=cardInfo.getDouble("beanCoupons");
                            Double useRollValue=rollRecordUse.getDouble("use_roll_value");
                            if(beanCoupons==null){
                                beanCoupons=0.0;
                            }
                            if(useRollValue==null){
                                useRollValue=0.0;
                            }
                            cardInfo.set("beanCoupons",beanCoupons+useRollValue);
                        }else{
                            Record cardInfo=new Record();
                            Record card=Db.findFirst("select * from fina_membership_card where card_number=? order by create_time desc limit 1 ",rollRecordUse.getStr("card_number"));
                            MmsMember member=mmsMemberService.findById(card.getStr("member_id"));
                            cardInfo.set("cardNumber",rollRecordUse.getStr("card_number"));
                            cardInfo.set("cardName",member.getFullName());
                            cardInfo.set("beanCoupons",rollRecordUse.getDouble("use_roll_value"));
                            recordMap.put(rollRecordUse.getStr("card_number"),cardInfo);
                        }
                    }

                    if(recordMap.size()>1){
                        String returnCardNumber="";
                        String returnCardName="";
                        for(String key:recordMap.keySet()){
                            Record cardInfo=recordMap.get(key);
                            returnCardNumber+=cardInfo.getStr("cardNumber")+"、";
                            returnCardName+=cardInfo.getStr("cardName")+"、";
                        }
                        returnCardNumber=returnCardNumber.substring(0,returnCardNumber.length()-1);
                        returnCardName=returnCardName.substring(0,returnCardName.length()-1);
                        record.set("cardNumber",returnCardNumber);
                        record.set("memberName",returnCardName);
                    }
                }
            }
        }
        return recordPage;
    }

    @Override
    public Page<Record> getAllOfflineCashOrder(int pageNumber, int pageSize,String warehouseId, String paymentTime, String createName, String remark,String userId,String orderNo) {
        String select="select a.id,a.status,a.order_no as orderNo,a.total_amount as totalAmount,a.reality_payment_amount as realityPaymentAmount  " +
                ",a.payment_time as paymentTime,a.remark,c.`name` as createName,d.activity_name  as activityName,f.`name` as warehouseName ";
        String sql=" from mall_order a   " +
                " left join sys_user c on c.id=a.create_by " +
                " left join mall_activity d on d.id=a.activity_id " +
                " INNER JOIN mall_order_item e on e.id=(select id from mall_order_item where order_id=a.id limit 1) " +
                " left join wms_warehouses f on f.id=e.out_warehouses_id  " +
                "where a.`type`='offline_cash'  ";
        List<String> params=new ArrayList<>();

        if(StrKit.notBlank(paymentTime)){
            sql+=" and to_days(a.payment_time)=to_days(?) ";
            params.add(paymentTime);
        }
        if(StrKit.notBlank(orderNo)){
            sql+=" and a.order_no=?  ";
            params.add(orderNo.trim());
        }

        if(StrKit.notBlank(warehouseId)){
            sql+=" and e.out_warehouses_id=?  ";
            params.add(warehouseId);
        }else{
            List<WmsWarehouses> wmsWarehousesList=wmsWarehouseService.getAllWmsWarehouseByUserId(userId);
            if(wmsWarehousesList==null || wmsWarehousesList.size()==0){
                return new Page<Record>();
            }else{
                String str="";
                for(WmsWarehouses warehouses:wmsWarehousesList){
                    str+="?,";
                    params.add(warehouses.getId());
                }
                str=str.substring(0,str.length()-1);
                sql+=" and e.out_warehouses_id in("+str+") ";

            }

        }

        if(StrKit.notBlank(createName)){
            sql+=" and c.`name`=? ";
            params.add(createName);
        }

        if(StrKit.notBlank(remark)){
            sql+=" and a.remark like concat('%',?,'%') ";
            params.add(remark);
        }

        sql+=" order by a.create_date desc ";
        Page<Record> page=Db.paginate(pageNumber,pageSize,select,sql,params.toArray());
        if(page.getList()==null || page.getList().size()==0){
            return page;
        }
        for(Record record:page.getList()){
            List<MallOrderPayment> orderPaymentList=mallOrderPaymentService.getOrderPaymentList(record.getStr("id"));
            String payTypes="";
            for(MallOrderPayment orderPayment:orderPaymentList){
                MallPaymentType paymentType=MallPaymentType.aliPay.getTypeByKey(orderPayment.getPayType());
                if("6".equals(paymentType.getKey())){
                    MallOfflineCoupon offlineCoupon=mallOfflineCouponService.findById(orderPayment.getCouponId());
                    if(offlineCoupon!=null){
                        payTypes+= offlineCoupon.getName()+"："+orderPayment.getAmount()+"张;";
                    }
                }else{
                    payTypes+= paymentType.getValue()+"："+orderPayment.getAmount()+"元;";
                }
            }
            record.set("payType",payTypes);
        }

        return page;
    }

    @Override
    public Map<String,Object> saveOfflineOrder(String creatorId, String cardNumber,String warehouseId,String paymentType,String activity,String giveModels,String checkCode,String remark, JSONArray array) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setCardNumber(cardNumber);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setRemark(remark);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        order.setCheckCode(null);
        if(StrKit.notBlank(checkCode)){
            //判断该码是否被使用
            Record record = Db.findFirst("select * from crm_card_roll_record where del_flag='0' and id=? ", checkCode);
            if(record==null){
                resultMap.put("msg","该核销码不存在");
                return resultMap;
            }
            if("1".equals(record.getStr("is_enable"))){
                resultMap.put("msg","该核销码已失效");
                return resultMap;
            }
            if(Db.findFirst("select id from mall_order where del_flag='0' and check_code=? limit 1",checkCode)!=null){
                resultMap.put("msg","该核销码已被使用");
                return resultMap;
            }
            if("1".equals(record.getStr("is_use"))){
                resultMap.put("msg","该核销码已被核销");
                return resultMap;
            }
            order.setIsCheck("0");
            order.setCheckCode(checkCode);
        }else{
            order.setIsCheck("1");
        }


        FinaMembershipCard card=finaMembershipCardService.getCardByNumber(cardNumber);
        if(card==null){
            resultMap.put("msg",cardNumber+"会员卡不存在");
            return resultMap;
        }else if("1".equals(card.getIsLock())){
            resultMap.put("msg",cardNumber+"会员卡已锁定");
            return resultMap;
        }
        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
        if(cardType==null){
            resultMap.put("msg",cardNumber+"会员卡类型未设置");
            return resultMap;
        }


        order.setUserId(card.getMemberId());

        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            WmsStockModels wmsStockModels=wmsStockModelService.findById(obj.getString("id"));
            item.setPrice(wmsStockModels.getExchangePrice());
            item.setType("1");
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        //获取活动
        //获取优惠配置
        List<MallActivity> activityList = mallActivityService.getNewActivityList(warehouseId, "2", array, new Date(),"1");
        JSONObject activityObj=JSON.parseObject(activity);
        if(activityObj!=null){
            if((activityObj!=null && (activityList==null || activityList.size()==0))
                    || (activityObj==null && (activityList!=null && activityList.size()>0))){
                resultMap.put("msg","前后端活动获取不一致");
                return resultMap;
            }
        }
        //活动折扣
        List<MallActivityGiveConfig> updateGiveConfigList=new ArrayList<>();
        Map<String,Double> giveItemArray=new HashMap<>();
        List<MallOrderItem> giveItemList=new ArrayList<>();
        if(activityObj!=null){
            MallActivity useActivity=null;
            for(MallActivity mallActivity:activityList){
                if(mallActivity.getId().equalsIgnoreCase(activityObj.getString("id"))){
                    useActivity=mallActivity;
                    break;
                }
            }
            if(useActivity==null){
                resultMap.put("msg","选中的活动不在可参加活动范围中");
                return resultMap;
            }

            List<MallActivityGiveConfig> giveConfigList=useActivity.getGiveConfigList();

            JSONArray giveArray=JSON.parseArray(giveModels);

            if(giveArray!=null && giveArray.size()>0 && giveArray.size()!=giveConfigList.size()){
                resultMap.put("msg","前后端赠送商品存在差异");
                return resultMap;
            }

            if(giveConfigList!=null && giveConfigList.size()>0){
                Map<String,Double> giveConfigMap=new HashMap<>();
                for(MallActivityGiveConfig giveConfig:giveConfigList){
                    //还剩余多少件
                    giveConfigMap.put(giveConfig.getGiveStockModelId().toLowerCase(),giveConfig.getGiveMaxCount());
                }
                Map<String,Double> giveModelMap=new HashMap<>();
                for(int i=0;i<giveArray.size();i++){
                    JSONObject giveObj=giveArray.getJSONObject(i);
                    if(giveModelMap.containsKey(giveObj.getString("modelsId").toLowerCase())){
                        giveModelMap.put(giveObj.getString("modelsId").toLowerCase(),BigDecimal.valueOf(giveObj.getDouble("giveCount"))
                                .add(BigDecimal.valueOf(giveModelMap.get(giveObj.getString("modelsId").toLowerCase()))).doubleValue());
                        giveItemArray.put(giveObj.getString("modelsId").toLowerCase(),BigDecimal.valueOf(giveObj.getDouble("giveCount"))
                                .add(BigDecimal.valueOf(giveItemArray.get(giveObj.getString("modelsId").toLowerCase()))).doubleValue());
                    }else{
                        giveModelMap.put(giveObj.getString("modelsId").toLowerCase(),giveObj.getDouble("giveCount"));
                        giveItemArray.put(giveObj.getString("modelsId").toLowerCase(),giveObj.getDouble("giveCount"));
                    }

                }

                for(String key:giveConfigMap.keySet()){
                    double giveMaxCount=giveConfigMap.get(key);
                    Double giveCount=giveModelMap.get(key);
                    if(giveCount==0){
                        continue;
                    }
                    if(giveCount>giveMaxCount){
                        resultMap.put("msg","赠送商品存在库存不足");
                        return resultMap;
                    }
                    order.setActivityId(useActivity.getId());
                    WmsStockModels modelInfo=wmsStockModelService.findById(key);

                    //获取销售价格
                    WmsStockModelWarehousePrice warehousePrice=wmsStockModelWarehousePriceService.getModelWarehousePrice(modelInfo.getId(),warehouseId);
                    if(warehousePrice==null || warehousePrice.getSalePrice()==null || warehousePrice.getSalePrice()<0){
                        resultMap.put("msg",modelInfo.getStr("name")+"的仓库销售价格未配置");
                        return resultMap;
                    }

                    modelIds.add(key);
                    MallOrderItem item=new MallOrderItem();
                    item.setId(IdGen.getUUID());
                    item.setOrderId(order.getId());
                    //赠送
                    item.setType("2");
                    item.setModelId(key);
                    item.setNum(giveCount);
                    item.setPrice(modelInfo.getExchangePrice());
                    item.setTotalFee(0.0);
                    item.setOutWarehousesId(warehouseId);
                    giveItemList.add(item);

                    MallActivityGiveConfig updateGiveConifg= mallActivityGiveConfigService.findGiveModelByActivityId(useActivity.getId(),key);
                    if(updateGiveConifg.getUseGiveCount()==null){
                        updateGiveConifg.setUseGiveCount(0D);
                    }
                    updateGiveConifg.setUseGiveCount(updateGiveConifg.getUseGiveCount()+giveCount);
                    updateGiveConifg.setUpdateDate(new Date());
                    updateGiveConfigList.add(updateGiveConifg);
                }

            }
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        boolean isChicken=false;
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());
            Double unitPrice=null;
            WmsStockModels wmsStockModels =null;
            if(modelInfo==null){
                wmsStockModels = wmsStockModelService.findById(item.getModelId());
                modelInfo=new Record();
                modelInfo.set("imgUrl",null);
                modelInfo.set("standard",wmsStockModels.getStandard());
                modelInfo.set("exchangeBeanCoupons",wmsStockModels.getExchangeBeanCoupons());
                modelInfo.set("exchangePrice",wmsStockModels.getExchangePrice());
                modelInfo.set("salePrice",wmsStockModels.getSalePrice());
                modelInfo.set("name",wmsStockModels.getName());
                modelInfo.set("id",wmsStockModels.getId());
                //modelInfo.set()
            }
            if("2".equals(paymentType)){
                //豆豆券
                unitPrice=modelInfo.getDouble("exchangeBeanCoupons");
            }else{
                if("1".equals(paymentType)){
                    //积分
                }else if("3".equals(paymentType)){
                    //天数或余额

                }
                unitPrice=modelInfo.getDouble("exchangePrice");
            }
            if(
        		"85C3A8AD-59FF-434B-B337-C28127067F8E".equalsIgnoreCase(item.getModelId()) || 
        		"F0501E07-1A33-458C-BBB9-7132EBD8767F".equalsIgnoreCase(item.getModelId()) ||
                "CE06E8ED-DF00-4447-B69D-B5F10A3DADE7".equalsIgnoreCase(item.getModelId()) ||
                "29B950FB-6406-11ED-AB8B-00163E0C2357".equalsIgnoreCase(item.getModelId()) ||
                "CD8D07BA-7161-445B-AF42-A38F06B8B52C".equalsIgnoreCase(item.getModelId())
    		){
                isChicken=true;
            }
            item.setPrice(unitPrice);
            item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(BigDecimal.valueOf(item.getNum())).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));
            totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        if(isChicken && orderItemList.size()>1){
            resultMap.put("msg","包含隔水蒸鸡或昌松大福月饼（套装）的订单请将隔水蒸鸡或昌松大福月饼（套装）或平安谷红狐干红葡萄酒或圣卡图骑士干红葡萄酒或旭一蒙乳全脂乳粉 单独提交");
            return resultMap;
        }
        for (MallOrderItem item : giveItemList) {
            Record modelInfo=modelModel.get(item.getModelId());
            Double unitPrice=null;
            WmsStockModels wmsStockModels =null;
            if(modelInfo==null){
                wmsStockModels = wmsStockModelService.findById(item.getModelId());
                modelInfo=new Record();
                modelInfo.set("imgUrl",null);
                modelInfo.set("standard",wmsStockModels.getStandard());
                modelInfo.set("exchangeBeanCoupons",wmsStockModels.getExchangeBeanCoupons());
                modelInfo.set("exchangePrice",wmsStockModels.getExchangePrice());
                modelInfo.set("salePrice",wmsStockModels.getSalePrice());
                modelInfo.set("name",wmsStockModels.getName());
                modelInfo.set("id",wmsStockModels.getId());
            }
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+"(赠);";
        }
        orderItemList.addAll(giveItemList);

        order.setTotalIntegral(totalPrice);
        order.setRealityPaymentIntegral(0.0);
        order.setRealityPaymentTimes(0.0);
        order.setRealityPaymentCardAmount(0.0);
        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());

        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());
        /*if(!"1".equals(cardType.getIsIntegral())){
            resultMap.put("msg",cardNumber+"会员卡未开通积分");
            return resultMap;
        }*/
        // 获取扣除方式
        MainCardDeductScheme deductScheme= mainCardDeductSchemeService.getSchemeByCardNumber(card.getCardNumber());
        if(deductScheme==null){
            deductScheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(card.getCardNumber());
        }

        //扣除前积分
        double cardIntegral=0.0;
        //扣除后积分
        double afterCardIntegral=0.0;
        //扣除前天数
        double consumeTimes=0.0;
        //扣除后天数
        double afterConsumeTimes=0.0;
        //扣除前天数
        double balance=0.0;
        //扣除后天数
        double afterBalance=0.0;
        //订单总金额
        double totalBalance=0.0;
        //订单总天数
        double totalTimes=0.0;
        //扣除总积分
        double totalIntegral=0.0;
        //扣除前豆豆券
        double beanCoupons=0.0;
        //扣除后豆豆券
        double afterBeanCoupons=0.0;
        //扣除总豆豆券
        double totalBeanCoupons=0.0;

        double buyAmount = 0.0;
        double giveAmount = 0.0;
        double buyDays = 0.0;
        double giveDays = 0.0;
        double giveIntegrals = 0.0;

        //扣除会员卡记录
        FinaCardIntegralRecord integralRecord=new FinaCardIntegralRecord();

        Map<String,Double> lockInfo=finaMembershipCardService.getCardLockInfo(card.getCardNumber());

        if (card.getCardIntegrals()==null) {
            card.setCardIntegrals(0.0);
        }
        if (card.getBalance()==null) {
            card.setBalance(0.0);
        }
        if (card.getConsumeTimes()==null) {
            card.setConsumeTimes(0.0);
        }


        if("1".equals(paymentType)){
            //扣积分
            if("1".equals(cardType.getIsIntegral())){
                cardIntegral=card.getCardIntegrals();
                totalIntegral=totalPrice;
                afterCardIntegral=BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(totalIntegral)).doubleValue();
                integralRecord.setId(IdGen.getUUID());

                if(card.getCardMinIntegrals()==null){
                    card.setCardMinIntegrals(0.0);
                }
                if(BigDecimal.valueOf(afterCardIntegral).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals")))
                        .compareTo(BigDecimal.valueOf(card.getCardMinIntegrals()))==-1){
                    resultMap.put("msg",cardNumber+"会员卡积分不足，最小积分为:"+card.getCardMinIntegrals()+",当前为:"+
                            BigDecimal.valueOf(afterCardIntegral).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals"))).doubleValue());
                    return resultMap;
                }
                card.setCardIntegrals(afterCardIntegral);
                order.setOfflinePaymentType("1");
                afterConsumeTimes=card.getConsumeTimes();
                afterBalance=card.getBalance();
                afterBeanCoupons=card.getBeanCoupons();

                order.setRealityPaymentIntegral(totalIntegral);

                //计算消费明细赠送积分多少
                giveIntegrals = totalIntegral;
                if(card.getGiveRechargeIntegrals() > 0){
                    card.setGiveRechargeIntegrals(BigDecimal.valueOf(card.getGiveRechargeIntegrals()).subtract(BigDecimal.valueOf(totalIntegral)).doubleValue());
                }
            }else{
                resultMap.put("msg",cardNumber+"会员卡不是积分类型卡");
                return resultMap;
            }

        }else if("2".equals(paymentType)){
            if(true){
                resultMap.put("msg","豆豆券暂时只支持豆豆券二维码付款");
                return resultMap;
            }

            //扣豆豆券
            if("1".equals(cardType.getIsIntegral())){
                beanCoupons=card.getBeanCoupons();
                totalBeanCoupons=totalPrice;
                afterBeanCoupons=BigDecimal.valueOf(card.getBeanCoupons()).subtract(BigDecimal.valueOf(totalBeanCoupons)).doubleValue();
                integralRecord.setId(IdGen.getUUID());
                if(BigDecimal.valueOf(afterBeanCoupons).subtract(BigDecimal.valueOf(lockInfo.get("lockBeanCoupons")))
                        .compareTo(BigDecimal.ZERO)==-1){
                    resultMap.put("msg",cardNumber+"会员卡豆豆券不足,会员卡可用豆豆券为:"+
                            BigDecimal.valueOf(beanCoupons).subtract(BigDecimal.valueOf(lockInfo.get("lockBeanCoupons"))).doubleValue());
                    return resultMap;
                }
                card.setBeanCoupons(afterBeanCoupons);
                order.setOfflinePaymentType("2");
                afterConsumeTimes=card.getConsumeTimes();
                afterBalance=card.getBalance();
                afterCardIntegral=card.getCardIntegrals();
            }else{
                resultMap.put("msg",cardNumber+"会员卡不是豆豆券类型卡");
                return resultMap;
            }
        }else if("3".equals(paymentType)){
            order.setOfflinePaymentType("3");

            if(isChicken){
                if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
                    consumeTimes=card.getConsumeTimes();
                    totalTimes=totalPrice;
                    afterConsumeTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue();

                    if(BigDecimal.valueOf(afterConsumeTimes).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).compareTo(BigDecimal.ZERO)==-1){
                        resultMap.put("msg",cardNumber+"会员卡剩余天数不足,保存订单失败");
                        return resultMap;
                    }
                    card.setConsumeTimes(afterConsumeTimes);
                    //afterBeanCoupons=card.getBeanCoupons();
                    afterBalance=card.getBalance();
                    afterCardIntegral=card.getCardIntegrals();
                    order.setRealityPaymentTimes(totalTimes);

                    //计算消费明细的购买、赠送天数分别是多少
                    if(card.getGiveRechargeDays() >= totalTimes){
                        giveDays = totalTimes;
                        if(card.getGiveRechargeDays() > 0){
                            card.setGiveRechargeDays(BigDecimal.valueOf(card.getGiveRechargeDays()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue());
                        }
                    }else{
                        giveDays = card.getGiveRechargeDays();
                        buyDays = BigDecimal.valueOf(totalTimes).subtract(BigDecimal.valueOf(card.getGiveRechargeDays())).doubleValue();
                        //分别扣减购买天数、赠送天数
                        card.setGiveRechargeDays(0.0);
                        if(card.getBuyRechargeDays() > 0){
                            card.setBuyRechargeDays(BigDecimal.valueOf(card.getBuyRechargeDays()).subtract(BigDecimal.valueOf(buyDays)).doubleValue());
                        }
                    }
                }else{
                    resultMap.put("msg","隔水蒸鸡或昌松大福月饼（套装）只能使用天数扣除");
                    return resultMap;
                }
            }else{
                if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){

                    if("1".equals(cardType.getIsIntegral())){
                        cardIntegral=card.getCardIntegrals();
                        consumeTimes=card.getConsumeTimes();
                        if(BigDecimal.valueOf(consumeTimes).add(BigDecimal.valueOf(cardIntegral))
                                .subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes")))
                                .subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals"))).compareTo(BigDecimal.ZERO)==-1){
                            resultMap.put("msg",cardNumber+"会员卡剩余天数、积分不足,保存订单失败");
                            return resultMap;
                        }
                        double usableIntegral=BigDecimal.valueOf(cardIntegral).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals"))).doubleValue();
                        double usableTimes=BigDecimal.valueOf(consumeTimes).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).doubleValue();
                        totalIntegral=0.0;
                        totalTimes=0.0;
                        if(usableIntegral>=totalPrice){
                            totalIntegral=totalPrice;
                            afterCardIntegral=BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(totalIntegral)).doubleValue();
                            afterConsumeTimes=card.getConsumeTimes();
                            card.setCardIntegrals(afterCardIntegral);
                        }else{

                            while (usableIntegral>1){
                                totalIntegral+=1;
                                usableIntegral=BigDecimal.valueOf(usableIntegral).subtract(BigDecimal.valueOf(1)).doubleValue();
                            }
                            totalTimes=totalPrice-totalIntegral;
                            afterConsumeTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue();
                            afterCardIntegral=BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(totalIntegral)).doubleValue();
                            card.setCardIntegrals(afterCardIntegral);
                            card.setConsumeTimes(afterConsumeTimes);
                        }
                        order.setRealityPaymentIntegral(totalIntegral);
                        order.setRealityPaymentTimes(totalTimes);
                        afterBalance=card.getBalance();

                        //计算消费明细的购买、赠送天数分别是多少
                        if(card.getGiveRechargeDays() >= totalTimes){
                            giveDays = totalTimes;
                            if(card.getGiveRechargeDays() > 0){
                                card.setGiveRechargeDays(BigDecimal.valueOf(card.getGiveRechargeDays()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue());
                            }
                        }else{
                            giveDays = card.getGiveRechargeDays();
                            buyDays = BigDecimal.valueOf(totalTimes).subtract(BigDecimal.valueOf(card.getGiveRechargeDays())).doubleValue();
                            //分别扣减购买天数、赠送天数
                            card.setGiveRechargeDays(0.0);
                            if(card.getBuyRechargeDays() > 0){
                                card.setBuyRechargeDays(BigDecimal.valueOf(card.getBuyRechargeDays()).subtract(BigDecimal.valueOf(buyDays)).doubleValue());
                            }
                        }
                        //计算消费明细赠送积分多少
                        giveIntegrals = totalIntegral;
                        if(card.getGiveRechargeIntegrals() > 0){
                            card.setGiveRechargeIntegrals(BigDecimal.valueOf(card.getGiveRechargeIntegrals()).subtract(BigDecimal.valueOf(totalIntegral)).doubleValue());
                        }
                    }else{
                        consumeTimes=card.getConsumeTimes();
                        totalTimes=totalPrice;
                        afterConsumeTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue();

                        if(BigDecimal.valueOf(afterConsumeTimes).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).compareTo(BigDecimal.ZERO)==-1){
                            resultMap.put("msg",cardNumber+"会员卡剩余天数不足,保存订单失败");
                            return resultMap;
                        }
                        card.setConsumeTimes(afterConsumeTimes);
                        //afterBeanCoupons=card.getBeanCoupons();
                        afterBalance=card.getBalance();
                        afterCardIntegral=card.getCardIntegrals();
                        order.setRealityPaymentTimes(totalTimes);

                        //计算消费明细的购买、赠送天数分别是多少
                        if(card.getGiveRechargeDays() >= totalTimes){
                            giveDays = totalTimes;
                            if(card.getGiveRechargeDays() > 0){
                                card.setGiveRechargeDays(BigDecimal.valueOf(card.getGiveRechargeDays()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue());
                            }
                        }else{
                            giveDays = card.getGiveRechargeDays();
                            buyDays = BigDecimal.valueOf(totalTimes).subtract(BigDecimal.valueOf(card.getGiveRechargeDays())).doubleValue();
                            //分别扣减购买天数、赠送天数
                            card.setGiveRechargeDays(0.0);
                            if(card.getBuyRechargeDays() > 0){
                                card.setBuyRechargeDays(BigDecimal.valueOf(card.getBuyRechargeDays()).subtract(BigDecimal.valueOf(buyDays)).doubleValue());
                            }
                        }
                    }
                }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                    if(deductScheme.getPrice()==null || deductScheme.getPrice()<=0){
                        resultMap.put("msg",cardNumber+"金额会员卡扣除单价未设置");
                        return resultMap;
                    }
                    balance=card.getBalance();
                    totalBalance=BigDecimal.valueOf(totalPrice).multiply(BigDecimal.valueOf(deductScheme.getPrice())).doubleValue();
                    afterBalance=BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(totalBalance)).doubleValue();

                    if(BigDecimal.valueOf(afterBalance).subtract(BigDecimal.valueOf(lockInfo.get("lockBalance"))).compareTo(BigDecimal.ZERO)==-1){
                        resultMap.put("msg",cardNumber+"会员卡剩余金额不足,保存订单失败");
                        return resultMap;
                    }
                    card.setBalance(afterBalance);

                    afterConsumeTimes=card.getConsumeTimes();
                    afterBalance=card.getBalance();
                    afterCardIntegral=card.getCardIntegrals();
                    order.setRealityPaymentCardAmount(totalBalance);

                    //计算消费明细的购买、赠送金额分别是多少
                    if(card.getGiveRechargeAmount() >= totalBalance){
                        giveAmount = totalBalance;
                        if(card.getGiveRechargeAmount() > 0){
                            card.setGiveRechargeAmount(BigDecimal.valueOf(card.getGiveRechargeAmount()).subtract(BigDecimal.valueOf(totalBalance)).doubleValue());
                        }
                    }else{
                        giveAmount = card.getGiveRechargeAmount();
                        buyAmount = BigDecimal.valueOf(totalBalance).subtract(BigDecimal.valueOf(card.getGiveRechargeAmount())).doubleValue();
                        //分别扣减购买金额、赠送金额
                        card.setGiveRechargeAmount(0.0);
                        if(card.getBuyRechargeAmount() > 0){
                            card.setBuyRechargeAmount(BigDecimal.valueOf(card.getBuyRechargeAmount()).subtract(BigDecimal.valueOf(buyAmount)).doubleValue());
                        }
                    }
                }else{
                    resultMap.put("msg",cardNumber+"会员卡扣除方式不支持");
                    return resultMap;
                }
            }


        }
        /*if("1".equals(cardType.getIsIntegral())){
            cardIntegral=card.getCardIntegrals();
            totalIntegral=totalPrice;
            afterCardIntegral=BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(totalIntegral)).doubleValue();
            integralRecord.setId(IdGen.getUUID());

            if(card.getCardMinIntegrals()==null){
                card.setCardMinIntegrals(0.0);
            }
            if(BigDecimal.valueOf(afterCardIntegral).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals")))
                    .compareTo(BigDecimal.valueOf(card.getCardMinIntegrals()))==-1){
                resultMap.put("msg",cardNumber+"会员卡积分不足，最小积分为:"+card.getCardMinIntegrals()+",当前为:"+
                        BigDecimal.valueOf(afterCardIntegral).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals"))).doubleValue());
                return resultMap;
            }
            card.setCardIntegrals(afterCardIntegral);

        }else{
            if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
                consumeTimes=card.getConsumeTimes();
                totalTimes=totalPrice;
                afterConsumeTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue();

                if(BigDecimal.valueOf(afterConsumeTimes).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).compareTo(BigDecimal.ZERO)==-1){
                    resultMap.put("msg",cardNumber+"会员卡剩余天数不足,保存订单失败");
                    return resultMap;
                }
                card.setConsumeTimes(afterConsumeTimes);
            }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                if(deductScheme.getPrice()==null || deductScheme.getPrice()<=0){
                    resultMap.put("msg",cardNumber+"金额会员卡扣除单价未设置");
                    return resultMap;
                }
                balance=card.getBalance();
                totalBalance=BigDecimal.valueOf(totalPrice).multiply(BigDecimal.valueOf(deductScheme.getPrice())).doubleValue();
                afterBalance=BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(totalBalance)).doubleValue();

                if(BigDecimal.valueOf(afterBalance).subtract(BigDecimal.valueOf(lockInfo.get("lockBalance"))).compareTo(BigDecimal.ZERO)==-1){
                    resultMap.put("msg",cardNumber+"会员卡剩余金额不足,保存订单失败");
                    return resultMap;
                }
                card.setBalance(afterBalance);
            }else{
                resultMap.put("msg",cardNumber+"会员卡扣除方式不支持");
                return resultMap;
            }
        }*/
        card.setUpdateTime(new Date());

        integralRecord.setCardId(card.getId());
        integralRecord.setTransactionType("out");
        integralRecord.setBusinessType("mall_exchange");
        integralRecord.setIntegralValue(totalPrice);
        integralRecord.setCreateBy(creatorId);
        integralRecord.setCreateTime(new Date());
        integralRecord.setUpdateBy(creatorId);
        integralRecord.setUpdateTime(new Date());
        integralRecord.setIntegralSnapshot(cardIntegral);

        FinaCardTransactions cardTran = new FinaCardTransactions();
        cardTran.setId(IdGen.getUUID());
        //cardTran.setExpenseId(crId);
        cardTran.setCardId(card.getId());
        cardTran.setTransactionNo(order.getOrderNo());
        cardTran.setConsumeType(ConsumeType.STORE);
        cardTran.setType("daily_deduction");
        cardTran.setInOutFlag("2");
        cardTran.setAmount(totalBalance);
        cardTran.setTimes(totalTimes);
        cardTran.setPoints(0.0);
        cardTran.setIntegrals(totalIntegral);
        cardTran.setBeanCoupons(totalBeanCoupons);

        cardTran.setAmountSnapshot(afterBalance);//快照
        cardTran.setTimesSnapshot(afterConsumeTimes);//快照
        cardTran.setPointsSnapshot(card.getConsumePoints());
        cardTran.setIntegralsSnapshot(afterCardIntegral);
        cardTran.setBeanCouponsSnapshot(afterBeanCoupons);
        cardTran.setDescribe("积分兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe);
        cardTran.setDealTime(new Date());
        cardTran.setStartDate(new Date());
        cardTran.setEndDate(new Date());

        //生成消费购买、赠送明细列表记录
        List<FinaCardTransactionsDetail> transactionsDetailList = new ArrayList<>();
        if(giveDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTran.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(giveDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(creatorId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTran.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(buyDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(creatorId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTran.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(giveAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(creatorId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTran.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(buyAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(creatorId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveIntegrals > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTran.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("integrals");
            transactionsDetail.setDeductValue(giveIntegrals);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(creatorId);
            transactionsDetailList.add(transactionsDetail);
        }

        JSONArray outItemArray=new JSONArray();
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getIntValue("num"));
            outItemArray.add(outObj);
        }
        //赠送的
        for (String key : giveItemArray.keySet()) {
            boolean isExist=false;
            for (int i = 0; i < outItemArray.size(); i++) {
                JSONObject outObj=outItemArray.getJSONObject(i);
                if(outObj.getString("stockModelId").equalsIgnoreCase(key)){
                    outObj.put("count",outObj.getDouble("count")+giveItemArray.get(key));
                    isExist=true;
                    break;
                }
            }
            if(!isExist){
                JSONObject outObj=new JSONObject();
                outObj.put("stockModelId",key);
                outObj.put("count",giveItemArray.get(key));
                outItemArray.add(outObj);
            }
        }

        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        outToMemberParam.put("cardNo",cardNumber);
        outToMemberParam.put("list", JSON.toJSONString(paramArray));

        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && card.update() && outRecord.save() && cardTran.save()){
                    if(StrKit.notBlank(integralRecord.getId())){
                        integralRecord.save();
                    }
                    int[] nums=Db.batchSave(orderItemList,orderItemList.size());

                    //批量生成消费明细记录
                    if(transactionsDetailList!=null && transactionsDetailList.size()>0){
                        Db.batchSave(transactionsDetailList, transactionsDetailList.size());
                    }

                    try {
                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);

                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();

                                //获取成本价格
                                String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                logger.info("wms获取成本价结果："+operationDetailsResult);
                                if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                    JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                    if("1".equals(operationDetailsObj.getString("Type"))){
                                        JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                        Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                        for (int i = dataArray.size() - 1; i >= 0; i--) {
                                            JSONObject dataObj=dataArray.getJSONObject(i);
                                            //stockModelCostPriceMap.put()
                                            String stockModelId = dataObj.getString("StockModelId");
                                            Double costPrice = dataObj.getDouble("CostPrice");
                                            if(StrKit.notBlank(stockModelId)){
                                                stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                            }
                                        }

                                        for (MallOrderItem mallOrderItem : orderItemList) {
                                            Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                            if(costPrice==null){
                                                costPrice=0.0;
                                            }
                                            mallOrderItem.setCostPrice(costPrice);
                                        }
                                        Db.batchUpdate(orderItemList,orderItemList.size());
                                    }
                                }

                                return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }else{
            //发送短信
            JSONObject obj=new JSONObject();
            obj.put("company",Global.sendMsgCompanyName);
            obj.put("cardNumber",card.getCardNumber());
            obj.put("time", DateUtils.formatDate(order.getPaymentTime(),"yyyy-MM-dd HH:mm:ss"));
            obj.put("baseName","");
            obj.put("costName","兑换商品");


            if(cardTran.getAmount()>0 || cardTran.getIntegrals()>0 || cardTran.getTimes()>0){
                Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(card.getCardNumber());

                MainCardDeductScheme cardDeductScheme=mainCardDeductSchemeService.findById(card.getDeductSchemeId());
                if(cardDeductScheme==null){
                    cardDeductScheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
                }

                String usableStr="";
                if(DeductType.deductTimes.getKey().equals(cardDeductScheme.getDeductWay())){
                    if("1".equals(cardType.getIsIntegral())){
                        double usableTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lock.get("lockConsumeTimes"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double timesLeftRemainder = usableTimes % 1;
                        String usableTimesStr="";
                        if(timesLeftRemainder==0){
                            usableTimesStr=((int)usableTimes)+"";
                        }else{
                            usableTimesStr=usableTimes+"";
                        }

                        double usableIntegrals=BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(lock.get("lockIntegrals"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double integralsLeftRemainder = usableIntegrals % 1;
                        String usableIntegralsStr="";
                        if(integralsLeftRemainder==0){
                            usableIntegralsStr=((int)usableIntegrals)+"";
                        }else{
                            usableIntegralsStr=usableIntegrals+"";
                        }
                        usableStr=usableTimesStr+"天，"+usableIntegralsStr+"积分。";
                    }else{
                        double usableTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lock.get("lockConsumeTimes"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double timesLeftRemainder = usableTimes % 1;
                        String usableTimesStr="";
                        if(timesLeftRemainder==0){
                            usableTimesStr=((int)usableTimes)+"";
                        }else{
                            usableTimesStr=usableTimes+"";
                        }
                        usableStr=usableTimesStr+"天。";
                    }
                }else if(DeductType.deductAmount.getKey().equals(cardDeductScheme.getDeductWay())){
                    double usableAmount=BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(lock.get("lockBalance"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                    double amountLeftRemainder = usableAmount % 1;
                    String usableAmountStr="";
                    if(amountLeftRemainder==0){
                        usableAmountStr=((int)usableAmount)+"";
                    }else{
                        usableAmountStr=usableAmount+"";
                    }
                    usableStr=usableAmountStr+"元。";
                }

                double useTimes=cardTran.getTimes();
                double useIntegrals=cardTran.getIntegrals();
                double useAmount=cardTran.getAmount();

                String useStr="";
                if(useTimes>0){
                    double leftRemainder = useTimes % 1;
                    if(leftRemainder==0){
                        useStr+=((int)useTimes)+"天";
                    }else{
                        useStr+=useTimes+"天";
                    }
                    //useStr+=useTimes+"天";
                }
                if(useIntegrals>0){
                    double leftRemainder = useIntegrals % 1;
                    if(leftRemainder==0){
                        useStr+=((int)useIntegrals)+"积分";
                    }else{
                        useStr+=useIntegrals+"积分";
                    }
                    //useStr+=useIntegrals+"积分";
                }
                if(useAmount>0){
                    double leftRemainder = useAmount % 1;
                    if(leftRemainder==0){
                        useStr+=((int)useAmount)+"元";
                    }else{
                        useStr+=useAmount+"元";
                    }
                    //useStr+=useAmount+"元";
                }

                boolean isAddLockInfo=false;
                double lockTimes=lock.get("lockConsumeTimes");
                double lockAmount=lock.get("lockBalance");
                double lockIntegrals=lock.get("lockIntegrals");
                String lockStr="，已订房预扣除";
                if(lockTimes>0 || lockAmount>0 || lockIntegrals>0){
                    isAddLockInfo=true;
                    if(lockAmount>0){
                        double lockAmountLeftRemainder = lockAmount % 1;
                        if(lockAmountLeftRemainder==0){
                            lockStr+="金额："+((int)lockAmount)+"元";
                        }else{
                            lockStr+="金额："+lockAmount+"元";
                        }

                    }else{
                        //lockStr+="天数：";
                        if(lockTimes>0 && lockIntegrals>0){
                            double lockTimesLeftRemainder = lockTimes % 1;
                            if(lockTimesLeftRemainder==0){
                                lockStr+="天数："+((int)lockTimes)+"天";
                            }else{
                                lockStr+="天数："+lockTimes+"天";
                            }

                            double lockIntegralsLeftRemainder = lockIntegrals % 1;
                            if(lockIntegralsLeftRemainder==0){
                                lockStr+="，"+((int)lockIntegrals)+"积分";
                            }else{
                                lockStr+="，"+lockIntegrals+"积分";
                            }
                        }else if(lockTimes>0){
                            double lockTimesLeftRemainder = lockTimes % 1;
                            if(lockTimesLeftRemainder==0){
                                lockStr+="天数："+((int)lockTimes)+"天";
                            }else{
                                lockStr+="天数："+lockTimes+"天";
                            }
                        }else if(lockIntegrals>0){
                            double lockIntegralsLeftRemainder = lockIntegrals % 1;
                            if(lockIntegralsLeftRemainder==0){
                                lockStr+="天数："+((int)lockIntegrals)+"积分";
                            }else{
                                lockStr+="天数："+lockIntegrals+"积分";
                            }
                        }
                    }
                }
                if(!isAddLockInfo){
                    lockStr="";
                }
                MmsMember member = mmsMemberService.findById(card.getMemberId());
                WmsWarehouses wmsWarehouse=wmsWarehouseService.findById(warehouseId);
                MainBase base = mainBaseService.findById(wmsWarehouse.getBaseId());
                String content=member.getFullName()+"卡号"+card.getCardNumber()+"消费账单：于"+DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss")+"商超购物"
                        +"消费"+useStr+lockStr+"，剩余可用"+usableStr+"如您对账单有异议请于三日内提出，逾期视为无异议。";
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("content",content);
                smsSendRecordService.sendMessage(SendType.customContent,card.getCardNumber(), JSON.toJSONString(jsonObject),null);

            }

        }
        return resultMap;
    }

    @Override
    public Map<String,Object> savePaymentCodeOrder(String creatorId,String code,String warehouseId,String paymentType,String remark, JSONArray array) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setRemark(remark);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);


        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());

            Double unitPrice=null;
            if("2".equals(paymentType)){
                unitPrice=modelInfo.getDouble("exchangeBeanCoupons");
            }else{
                unitPrice=modelInfo.getDouble("exchangePrice");
            }
            item.setPrice(unitPrice);
            item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(BigDecimal.valueOf(item.getNum())).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        order.setTotalIntegral(totalPrice);
        order.setRealityPaymentIntegral(totalPrice);
        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());
        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());

        //扣除总积分
        double totalIntegral=0.0;

        totalIntegral=totalPrice;


        String describe="积分兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe;

        Map<String,String> paymentParams=new HashMap<>();
        paymentParams.put("code",code);
        paymentParams.put("orderNo",order.getOrderNo());
        paymentParams.put("amount","0.0");
        paymentParams.put("times","0.0");
        paymentParams.put("appNo","CSA0001");
        paymentParams.put("points","0.0");
        if("1".equals(paymentType)){
            paymentParams.put("integrals",Double.toString(totalIntegral));
            paymentParams.put("beanCoupons","0.0");
            order.setOfflinePaymentType("1");
        }else if("2".equals(paymentType)){
            paymentParams.put("beanCoupons",Double.toString(totalIntegral));
            paymentParams.put("integrals","0.0");
            order.setOfflinePaymentType("2");

            resultMap.put("msg","豆豆券暂时只支持豆豆券二维码付款");
            return resultMap;
        }else if("3".equals(paymentType)){
            resultMap.put("msg","付款码暂时不支持扣天数和余额");
            return resultMap;
        }
        paymentParams.put("description",describe);

        JSONArray paramsArray=new JSONArray();
        JSONObject baseIdObj=new JSONObject();
        baseIdObj.put("Key","baseId");
        baseIdObj.put("Value","");
        paramsArray.add(baseIdObj);

        JSONObject expenseIdObj=new JSONObject();
        expenseIdObj.put("Key","expenseId");
        expenseIdObj.put("Value","");
        paramsArray.add(expenseIdObj);

        JSONObject startDateObj=new JSONObject();
        startDateObj.put("Key","startDate");
        startDateObj.put("Value",DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
        paramsArray.add(startDateObj);

        JSONObject endDateObj=new JSONObject();
        endDateObj.put("Key","endDate");
        endDateObj.put("Value",DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
        paramsArray.add(endDateObj);

        paymentParams.put("appendParam",JSON.toJSONString(paramsArray));

        logger.info("请求付款吗接口参数："+JSON.toJSONString(paymentParams));
        String resultStr=HttpClientsUtils.httpPostForm(Global.paymentQRCodeScanUrl,paymentParams,null,null);
        logger.info("请求付款吗接口结果："+resultStr);
        if(!resultStr.startsWith("{") || !resultStr.endsWith("}")){
            resultMap.put("msg","调用付款码接口异常");
            return resultMap;
        }
        JSONObject resultObj=JSON.parseObject(resultStr);
        if(!"1".equals(resultObj.getString("Type"))){
            resultMap.put("msg","调用付款码接口失败："+resultObj.getString("Msg"));
            return resultMap;
        }
        String cardNumber=resultObj.getString("Data");
        FinaMembershipCard card=finaMembershipCardService.findCardByCardNumber(cardNumber);

        JSONArray outItemArray=new JSONArray();
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getIntValue("num"));
            outItemArray.add(outObj);
        }

        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        outToMemberParam.put("cardNo",cardNumber);
        outToMemberParam.put("list", JSON.toJSONString(paramArray));

        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        order.setCardNumber(card.getCardNumber());
        order.setUserId(card.getMemberId());

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && outRecord.save() ){

                    int[] nums=Db.batchSave(orderItemList,orderItemList.size());

                    try {
                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);

                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();

                                //获取成本价格
                                String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                logger.info("wms获取成本价结果："+operationDetailsResult);
                                if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                    JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                    if("1".equals(operationDetailsObj.getString("Type"))){
                                        JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                        Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                        for (int i = dataArray.size() - 1; i >= 0; i--) {
                                            JSONObject dataObj=dataArray.getJSONObject(i);
                                            //stockModelCostPriceMap.put()
                                            String stockModelId = dataObj.getString("StockModelId");
                                            Double costPrice = dataObj.getDouble("CostPrice");
                                            if(StrKit.notBlank(stockModelId)){
                                                stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                            }
                                        }

                                        for (MallOrderItem mallOrderItem : orderItemList) {
                                            Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                            if(costPrice==null){
                                                costPrice=0.0;
                                            }
                                            mallOrderItem.setCostPrice(costPrice);
                                        }
                                        Db.batchUpdate(orderItemList,orderItemList.size());
                                    }
                                }

                                return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> saveBeanCouponsCodeOrder(String creatorId, String[] beanCouponsCodes, String warehouseId
            , String paymentType, String remark, JSONArray array,String fullName,String telephone,String idcard) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setOfflinePaymentType(paymentType);
        order.setRemark(remark);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);


        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());

            Double unitPrice=null;
            unitPrice=modelInfo.getDouble("exchangeBeanCoupons");

            item.setPrice(unitPrice);
            item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(BigDecimal.valueOf(item.getNum())).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        order.setTotalIntegral(totalPrice);
        order.setRealityPaymentIntegral(totalPrice);
        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());
        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());
        //总扣除豆豆券
        double totalBeanCoupons=0.0;

        totalBeanCoupons=totalPrice;

        String describe="积分兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe;
        List<CrmCardRollRecord> rollRecordList=new ArrayList<>();

        double totalBalanceRollValue=0.0;

        Map<String,FinaMembershipCard> collCardMap=new HashMap<>();
        Map<String, MmsMember> collMemberMap=new HashMap<>();

        Map<String,Double> cardTotalBeanCoupons=new HashMap<>();


        for(int i=0;i<beanCouponsCodes.length;i++){
            CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(beanCouponsCodes[i]);
            if(rollRecord.getBalanceRollValue()!=null && rollRecord.getBalanceRollValue()>0.0){
                totalBalanceRollValue+=rollRecord.getBalanceRollValue();
            }
            FinaCardRoll cardRoll=finaCardRollService.findByRollId(rollRecord.getId());
            if(cardRoll!=null){
                FinaMembershipCard card=finaMembershipCardService.findById(cardRoll.getCardId());
                if(card!=null){
                    collCardMap.put(rollRecord.getId(),card);
                    MmsMember member=mmsMemberService.findById(card.getMemberId());
                    collMemberMap.put(rollRecord.getId(),member);
                }
            }
            if(!cardTotalBeanCoupons.containsKey(cardRoll.getCardId())){
                Double cardTotalBalanceRollValue=Db.queryDouble("select SUM(balance_roll_value) totalBalanceRollValue  from fina_card_roll a " +
                        "INNER JOIN crm_card_roll_record b on a.roll_id=b.id where b.del_flag='0' and a.card_id=? ",cardRoll.getCardId());
                cardTotalBeanCoupons.put(cardRoll.getCardId(),cardTotalBalanceRollValue);
            }

            rollRecordList.add(rollRecord);
        }
        if(totalBalanceRollValue<totalBeanCoupons){
            resultMap.put("msg","豆豆券余额不足");
            return resultMap;
        }
        //剩余扣除豆豆券
        double surplusDeductBeanCoupons=totalBeanCoupons;
        List<CrmCardRollRecordUse> rollRecordUseList=new ArrayList<>();
        List<CrmCardRollRecord> rollRecordUpdateList=new ArrayList<>();

        String cardNumber=null;
        String memberId=null;

        Map<String,Double> cardTotalDeductBeanCoupons=new HashMap<>();
        for(CrmCardRollRecord rollRecord:rollRecordList){
            Double balanceRollValue=rollRecord.getBalanceRollValue();
            if(balanceRollValue==null){
                balanceRollValue=0.0;
            }
            FinaMembershipCard card=collCardMap.get(rollRecord.getId());
            MmsMember member=collMemberMap.get(rollRecord.getId());
            if(StrKit.isBlank(cardNumber)){
                cardNumber=card.getCardNumber();

                if(StrKit.notBlank(idcard)){
                    order.setIdcard(idcard);
                }else{
                    order.setIdcard(member.getIdcard());
                }
                if(StrKit.notBlank(fullName)){
                    order.setFullName(fullName);
                }else{
                    order.setFullName(member.getFullName());
                }
                if(StrKit.notBlank(telephone)){
                    order.setPhoneNumber(telephone);
                }else{
                    order.setPhoneNumber(card.getTelephone());
                }

            }
            if(StrKit.isBlank(memberId)){
                memberId=card.getMemberId();
            }
            if(surplusDeductBeanCoupons>0.0){
                CrmCardRollRecordUse rollRecordUse=new CrmCardRollRecordUse();
                rollRecordUse.setId(IdGen.getUUID());
                rollRecordUse.setWarehouseId(warehouseId);
                rollRecordUse.setUseType("1");
                rollRecordUse.setRecordId(rollRecord.getId());
                rollRecordUse.setRollNumber(rollRecord.getRollNumber());
                rollRecordUse.setUseTime(new Date());
                rollRecordUse.setJoinId(order.getId());

                if(StrKit.notBlank(idcard)){
                    rollRecordUse.setIdcard(idcard);
                }else{
                    rollRecordUse.setIdcard(member.getIdcard());
                }
                if(StrKit.notBlank(fullName)){
                    rollRecordUse.setFullName(fullName);
                }else{
                    rollRecordUse.setFullName(member.getFullName());
                }
                if(StrKit.notBlank(telephone)){
                    rollRecordUse.setPhoneNumber(telephone);
                }else{
                    rollRecordUse.setPhoneNumber(card.getTelephone());
                }

                rollRecordUse.setCardNumber(card.getCardNumber());
                rollRecordUse.setDelFlag("0");
                rollRecordUse.setCreateBy(creatorId);
                rollRecordUse.setCreateTime(new Date());
                rollRecordUse.setUpdateBy(creatorId);
                rollRecordUse.setUpdateTime(new Date());

                double deductBeanCoupons=0.0;
                if(surplusDeductBeanCoupons==balanceRollValue){
                    rollRecordUse.setUseRollValue(balanceRollValue);
                    rollRecord.setBalanceRollValue(0.0);
                    rollRecord.setIsUse("1");
                    deductBeanCoupons=balanceRollValue;
                    surplusDeductBeanCoupons=0.0;
                }else if(surplusDeductBeanCoupons>balanceRollValue){
                    rollRecordUse.setUseRollValue(balanceRollValue);
                    rollRecord.setBalanceRollValue(0.0);
                    rollRecord.setIsUse("1");
                    deductBeanCoupons=balanceRollValue;
                    surplusDeductBeanCoupons=surplusDeductBeanCoupons-balanceRollValue;
                }else if(surplusDeductBeanCoupons<balanceRollValue){
                    rollRecordUse.setUseRollValue(surplusDeductBeanCoupons);
                    rollRecord.setBalanceRollValue(balanceRollValue-surplusDeductBeanCoupons);
                    deductBeanCoupons=surplusDeductBeanCoupons;
                    surplusDeductBeanCoupons=0.0;
                }
                if(cardTotalDeductBeanCoupons.containsKey(card.getId())){
                    cardTotalDeductBeanCoupons.put(card.getId(),cardTotalDeductBeanCoupons.get(card.getId())+deductBeanCoupons);
                }else{
                    cardTotalDeductBeanCoupons.put(card.getId(),deductBeanCoupons);
                }
                rollRecord.setUpdateTime(new Date());
                rollRecordUseList.add(rollRecordUse);
                rollRecordUpdateList.add(rollRecord);
            }else{
                break;
            }
        }

        JSONArray outItemArray=new JSONArray();
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getIntValue("num"));
            outItemArray.add(outObj);
        }

        Set<String> keySet=cardTotalDeductBeanCoupons.keySet();

        List<FinaCardTransactions> transactionsList=new ArrayList<>();

        Map<String,Record> smsMap=new HashMap<>();
        for(String key:keySet){
            Double deductBeanCoupons=cardTotalDeductBeanCoupons.get(key);
            Double beanCoupons=cardTotalBeanCoupons.get(key);


            FinaMembershipCard card=finaMembershipCardService.findById(key);



            FinaCardTransactions cardTran = new FinaCardTransactions();
            cardTran.setId(IdGen.getUUID());
            //cardTran.setExpenseId(crId);
            cardTran.setCardId(key);
            cardTran.setTransactionNo(order.getOrderNo());
            cardTran.setConsumeType(ConsumeType.STORE);
            cardTran.setType("daily_deduction");
            cardTran.setInOutFlag("2");
            cardTran.setAmount(0.0);
            cardTran.setTimes(0.0);
            cardTran.setPoints(0.0);
            cardTran.setIntegrals(0.0);
            cardTran.setBeanCoupons(deductBeanCoupons);

            Double balance=card.getBalance();
            Double integrals=card.getCardIntegrals();
            Double times=card.getConsumeTimes();
            Double points=card.getConsumePoints();
            if(balance==null){
                balance=0.0;
            }
            if(integrals==null){
                integrals=0.0;
            }
            if(times==null){
                times=0.0;
            }
            if(points==null){
                points=0.0;
            }

            cardTran.setAmountSnapshot(balance);//快照
            cardTran.setTimesSnapshot(times);//快照
            cardTran.setPointsSnapshot(points);
            cardTran.setIntegralsSnapshot(integrals);
            cardTran.setBeanCouponsSnapshot(beanCoupons-deductBeanCoupons);
            cardTran.setDescribe("积分兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe);
            cardTran.setDealTime(new Date());
            cardTran.setStartDate(new Date());
            cardTran.setEndDate(new Date());
            transactionsList.add(cardTran);

            Record sms=new Record();
            sms.set("cardNumber",card.getCardNumber());
            sms.set("useBeanCoupons",deductBeanCoupons);
            sms.set("leftBeanCoupons",cardTran.getBeanCouponsSnapshot());

            smsMap.put(card.getId(),sms);
        }

        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        outToMemberParam.put("cardNo",cardNumber);
        outToMemberParam.put("list", JSON.toJSONString(paramArray));

        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        order.setCardNumber(cardNumber);
        order.setUserId(memberId);

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && outRecord.save() ){
                    try {
                        int[] nums=Db.batchSave(orderItemList,orderItemList.size());
                        Db.batchSave(transactionsList,transactionsList.size());
                        Db.batchSave(rollRecordUseList,rollRecordUseList.size());
                        Db.batchUpdate(rollRecordUpdateList,rollRecordUpdateList.size());


                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);

                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();

                                //获取成本价格
                                String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                logger.info("wms获取成本价结果："+operationDetailsResult);
                                if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                    JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                    if("1".equals(operationDetailsObj.getString("Type"))){
                                        JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                        Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                        for (int i = dataArray.size() - 1; i >= 0; i--) {
                                            JSONObject dataObj=dataArray.getJSONObject(i);
                                            //stockModelCostPriceMap.put()
                                            String stockModelId = dataObj.getString("StockModelId");
                                            Double costPrice = dataObj.getDouble("CostPrice");
                                            if(StrKit.notBlank(stockModelId)){
                                                stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                            }
                                        }

                                        for (MallOrderItem mallOrderItem : orderItemList) {
                                            Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                            if(costPrice==null){
                                                costPrice=0.0;
                                            }
                                            mallOrderItem.setCostPrice(costPrice);
                                        }
                                        Db.batchUpdate(orderItemList,orderItemList.size());
                                    }
                                }
                                return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }else{
            for(String key:smsMap.keySet()){
                /*Record sms=smsMap.get(key);
                //发送短信
                JSONObject obj=new JSONObject();
                obj.put("company",Global.sendMsgCompanyName);
                obj.put("cardNumber",sms.getStr("cardNumber"));
                obj.put("time", DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                obj.put("baseName","兑换商品");
                //obj.put("costName","");
                obj.put("useBeanCoupons",sms.getDouble("useBeanCoupons"));
                obj.put("leftBeanCoupons",sms.getDouble("leftBeanCoupons"));
                smsSendRecordService.sendMessage(SendType.singleDeductionBeanCoupons,sms.getStr("cardNumber"),JSON.toJSONString(obj),null);*/
                //【昌松集团】张三四卡号CS00005268消费账单：于2023-10-11 18:03:52兑换商品消费了1000豆豆券，剩余豆豆券为:10000
                //String content="";

            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> saveBeanCouponsNumberOrder(String creatorId, double beanCouponsNumber, String warehouseId, String paymentType
            , String remark, JSONArray array, String fullName, String telephone, String idcard) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setOfflinePaymentType(paymentType);
        order.setRemark(remark);
        order.setFullName(fullName);
        order.setIdcard(idcard);
        order.setPhoneNumber(telephone);

        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);



        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());

            Double unitPrice=null;
            unitPrice=modelInfo.getDouble("exchangeBeanCoupons");

            item.setPrice(unitPrice);
            item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(BigDecimal.valueOf(item.getNum())).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        order.setTotalIntegral(totalPrice);

        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());
        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());
        //总扣除豆豆券
        double totalBeanCoupons=0.0;

        totalBeanCoupons=totalPrice;

        String describe="积分兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe;
        List<CrmCardRollRecord> rollRecordList=new ArrayList<>();



        if(beanCouponsNumber<totalBeanCoupons){
            resultMap.put("msg","无二维码豆豆券数量不足");
            return resultMap;
        }
        order.setRealityPaymentIntegral(beanCouponsNumber);
        String cardNumber="182611";
        String memberId=null;



        JSONArray outItemArray=new JSONArray();
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getIntValue("num"));
            outItemArray.add(outObj);
        }


        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        outToMemberParam.put("cardNo",cardNumber);
        outToMemberParam.put("list", JSON.toJSONString(paramArray));

        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        //order.setCardNumber(cardNumber);
        order.setUserId(memberId);

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && outRecord.save() ){
                    try {
                        int[] nums=Db.batchSave(orderItemList,orderItemList.size());


                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);

                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();

                                //获取成本价格
                                String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                logger.info("wms获取成本价结果："+operationDetailsResult);
                                if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                    JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                    if("1".equals(operationDetailsObj.getString("Type"))){
                                        JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                        Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                        for (int i = dataArray.size() - 1; i >= 0; i--) {
                                            JSONObject dataObj=dataArray.getJSONObject(i);
                                            //stockModelCostPriceMap.put()
                                            String stockModelId = dataObj.getString("StockModelId");
                                            Double costPrice = dataObj.getDouble("CostPrice");
                                            if(StrKit.notBlank(stockModelId)){
                                                stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                            }
                                        }

                                        for (MallOrderItem mallOrderItem : orderItemList) {
                                            Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                            if(costPrice==null){
                                                costPrice=0.0;
                                            }
                                            mallOrderItem.setCostPrice(costPrice);
                                        }
                                        Db.batchUpdate(orderItemList,orderItemList.size());
                                    }
                                }

                                return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> saveGiftVoucherCodeOrder(String creatorId, String[] giftVoucherCodes, String warehouseId, String paymentType, String remark, JSONArray array, String fullName, String telephone, String idcard) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setOfflinePaymentType(paymentType);
        order.setRemark(remark);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);


        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());

            Double unitPrice=null;
            unitPrice=modelInfo.getDouble("exchangeBeanCoupons");

            //item.setPrice(unitPrice);
            //item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(new BigDecimal(Integer.toString(item.getNum()))).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            //totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        order.setTotalIntegral(giftVoucherCodes.length+0.0);
        order.setRealityPaymentIntegral(giftVoucherCodes.length+0.0);
        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());
        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());
        //总扣除豆豆券
        double totalBeanCoupons=0.0;

        totalBeanCoupons=totalPrice;

        String describe="积分兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe;
        List<CrmCardRollRecord> rollRecordList=new ArrayList<>();

        double totalBalanceRollValue=0.0;

        Map<String,FinaMembershipCard> collCardMap=new HashMap<>();
        Map<String, MmsMember> collMemberMap=new HashMap<>();

        Map<String,Double> cardTotalBeanCoupons=new HashMap<>();


        for(int i=0;i<giftVoucherCodes.length;i++){
            CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(giftVoucherCodes[i]);
            FinaCardRoll cardRoll=finaCardRollService.findByRollId(rollRecord.getId());

            if(cardRoll!=null){
                FinaMembershipCard card=finaMembershipCardService.findById(cardRoll.getCardId());
                if(card!=null){
                    collCardMap.put(rollRecord.getId(),card);
                    MmsMember member=mmsMemberService.findById(card.getMemberId());
                    collMemberMap.put(rollRecord.getId(),member);
                }
            }

            if(!cardTotalBeanCoupons.containsKey(cardRoll.getCardId())){
                Double cardTotalBalanceRollValue=Db.queryDouble("select SUM(balance_roll_value) totalBalanceRollValue  from fina_card_roll a " +
                        "INNER JOIN crm_card_roll_record b on a.roll_id=b.id where b.del_flag='0' and a.card_id=? ",cardRoll.getCardId());
                cardTotalBeanCoupons.put(cardRoll.getCardId(),cardTotalBalanceRollValue);
            }

            rollRecordList.add(rollRecord);
        }
        /*if(totalBalanceRollValue<totalBeanCoupons){
            resultMap.put("msg","豆豆券余额不足");
            return resultMap;
        }*/
        //剩余扣除豆豆券
        //double surplusDeductBeanCoupons=totalBeanCoupons;
        List<CrmCardRollRecordUse> rollRecordUseList=new ArrayList<>();
        List<CrmCardRollRecord> rollRecordUpdateList=new ArrayList<>();

        String cardNumber=null;
        String memberId=null;

        Map<String,Record> smsMap=new HashMap<>();

        Map<String,Double> cardTotalDeductBeanCoupons=new HashMap<>();
        for(CrmCardRollRecord rollRecord:rollRecordList){
            /*Double balanceRollValue=rollRecord.getBalanceRollValue();
            if(balanceRollValue==null){
                balanceRollValue=0.0;
            }*/
            FinaMembershipCard card=collCardMap.get(rollRecord.getId());
            MmsMember member=collMemberMap.get(rollRecord.getId());
            if(StrKit.isBlank(cardNumber)){
                cardNumber=card.getCardNumber();

                if(StrKit.notBlank(idcard)){
                    order.setIdcard(idcard);
                }else{
                    order.setIdcard(member.getIdcard());
                }
                if(StrKit.notBlank(fullName)){
                    order.setFullName(fullName);
                }else{
                    order.setFullName(member.getFullName());
                }
                if(StrKit.notBlank(telephone)){
                    order.setPhoneNumber(telephone);
                }else{
                    order.setPhoneNumber(card.getTelephone());
                }

            }
            if(StrKit.isBlank(memberId)){
                memberId=card.getMemberId();
            }

            CrmCardRollRecordUse rollRecordUse=new CrmCardRollRecordUse();
            rollRecordUse.setId(IdGen.getUUID());
            rollRecordUse.setWarehouseId(warehouseId);
            rollRecordUse.setUseType("1");
            rollRecordUse.setRecordId(rollRecord.getId());
            rollRecordUse.setRollNumber(rollRecord.getRollNumber());
            rollRecordUse.setUseTime(new Date());
            rollRecordUse.setJoinId(order.getId());

            if(StrKit.notBlank(idcard)){
                rollRecordUse.setIdcard(idcard);
            }else{
                rollRecordUse.setIdcard(member.getIdcard());
            }
            if(StrKit.notBlank(fullName)){
                rollRecordUse.setFullName(fullName);
            }else{
                rollRecordUse.setFullName(member.getFullName());
            }
            if(StrKit.notBlank(telephone)){
                rollRecordUse.setPhoneNumber(telephone);
            }else{
                rollRecordUse.setPhoneNumber(card.getTelephone());
            }

            rollRecordUse.setCardNumber(card.getCardNumber());
            rollRecordUse.setDelFlag("0");
            rollRecordUse.setCreateBy(creatorId);
            rollRecordUse.setCreateTime(new Date());
            rollRecordUse.setUpdateBy(creatorId);
            rollRecordUse.setUpdateTime(new Date());


            rollRecord.setIsUse("1");
            rollRecord.setUpdateTime(new Date());
            rollRecordUseList.add(rollRecordUse);
            rollRecordUpdateList.add(rollRecord);

            Record sms=new Record();
            sms.set("cardNumber",card.getCardNumber());
            sms.set("cardRollNum",rollRecord.getRollNumber());

            smsMap.put(rollRecord.getRollNumber(),sms);
        }

        JSONArray outItemArray=new JSONArray();
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getIntValue("num"));
            outItemArray.add(outObj);
        }

        Set<String> keySet=cardTotalDeductBeanCoupons.keySet();

        List<FinaCardTransactions> transactionsList=new ArrayList<>();


        for(String key:keySet){
            Double deductBeanCoupons=cardTotalDeductBeanCoupons.get(key);
            Double beanCoupons=cardTotalBeanCoupons.get(key);


            FinaMembershipCard card=finaMembershipCardService.findById(key);



            FinaCardTransactions cardTran = new FinaCardTransactions();
            cardTran.setId(IdGen.getUUID());
            //cardTran.setExpenseId(crId);
            cardTran.setCardId(key);
            cardTran.setTransactionNo(order.getOrderNo());
            cardTran.setConsumeType(ConsumeType.STORE);
            cardTran.setType("daily_deduction");
            cardTran.setInOutFlag("2");
            cardTran.setAmount(0.0);
            cardTran.setTimes(0.0);
            cardTran.setPoints(0.0);
            cardTran.setIntegrals(0.0);
            cardTran.setBeanCoupons(0.0);

            Double balance=card.getBalance();
            Double integrals=card.getCardIntegrals();
            Double times=card.getConsumeTimes();
            Double points=card.getConsumePoints();
            if(balance==null){
                balance=0.0;
            }
            if(integrals==null){
                integrals=0.0;
            }
            if(times==null){
                times=0.0;
            }
            if(points==null){
                points=0.0;
            }

            cardTran.setAmountSnapshot(balance);//快照
            cardTran.setTimesSnapshot(times);//快照
            cardTran.setPointsSnapshot(points);
            cardTran.setIntegralsSnapshot(integrals);
            cardTran.setBeanCouponsSnapshot(beanCoupons);
            cardTran.setDescribe("礼品券兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe);
            cardTran.setDealTime(new Date());
            cardTran.setStartDate(new Date());
            cardTran.setEndDate(new Date());
            transactionsList.add(cardTran);
        }

        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        outToMemberParam.put("cardNo",cardNumber);
        outToMemberParam.put("list", JSON.toJSONString(paramArray));

        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        order.setCardNumber(cardNumber);
        order.setUserId(memberId);

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && outRecord.save() ){
                    try {
                        int[] nums=Db.batchSave(orderItemList,orderItemList.size());
                        Db.batchSave(transactionsList,transactionsList.size());
                        Db.batchSave(rollRecordUseList,rollRecordUseList.size());
                        Db.batchUpdate(rollRecordUpdateList,rollRecordUpdateList.size());


                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);

                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();

                                //获取成本价格
                                String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                logger.info("wms获取成本价结果："+operationDetailsResult);
                                if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                    JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                    if("1".equals(operationDetailsObj.getString("Type"))){
                                        JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                        Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                        for (int i = dataArray.size() - 1; i >= 0; i--) {
                                            JSONObject dataObj=dataArray.getJSONObject(i);
                                            //stockModelCostPriceMap.put()
                                            String stockModelId = dataObj.getString("StockModelId");
                                            Double costPrice = dataObj.getDouble("CostPrice");
                                            if(StrKit.notBlank(stockModelId)){
                                                stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                            }
                                        }

                                        for (MallOrderItem mallOrderItem : orderItemList) {
                                            Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                            if(costPrice==null){
                                                costPrice=0.0;
                                            }
                                            mallOrderItem.setCostPrice(costPrice);
                                        }
                                        Db.batchUpdate(orderItemList,orderItemList.size());
                                    }
                                }

                                return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }else{
            for(String key:smsMap.keySet()){
                Record sms=smsMap.get(key);
                //发送短信
                JSONObject obj=new JSONObject();
                obj.put("company",Global.sendMsgCompanyName);
                obj.put("cardNumber",sms.getStr("cardNumber"));
                obj.put("cardRollNum",sms.getStr("cardRollNum"));
                smsSendRecordService.sendMessage(SendType.giftVoucherUse,sms.getStr("cardNumber"),JSON.toJSONString(obj),null);
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> saveGiftVoucherNumberOrder(String creatorId, double giftVoucherNum, String warehouseId, String paymentType
            , String remark, JSONArray array, String fullName, String telephone, String idcard) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setOfflinePaymentType(paymentType);
        order.setRemark(remark);
        order.setFullName(fullName);
        order.setIdcard(idcard);
        order.setPhoneNumber(telephone);

        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);



        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());

            Double unitPrice=null;
            unitPrice=modelInfo.getDouble("exchangeBeanCoupons");

            //item.setPrice(unitPrice);
            //item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(new BigDecimal(Integer.toString(item.getNum()))).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            //totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        order.setTotalIntegral(totalPrice);

        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());
        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());
        //总扣除豆豆券
        double totalBeanCoupons=0.0;

        totalBeanCoupons=totalPrice;

        String describe="兑换商品，订单号:"+order.getOrderNo()+";"+modelsDescribe;
        List<CrmCardRollRecord> rollRecordList=new ArrayList<>();



        if(giftVoucherNum<=0){
            resultMap.put("msg","无二维码礼品券数量不能为空");
            return resultMap;
        }
        order.setRealityPaymentIntegral(giftVoucherNum);
        String cardNumber="182611";
        String memberId=null;



        JSONArray outItemArray=new JSONArray();
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getIntValue("num"));
            outItemArray.add(outObj);
        }


        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        outToMemberParam.put("cardNo",cardNumber);
        outToMemberParam.put("list", JSON.toJSONString(paramArray));

        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        //order.setCardNumber(cardNumber);
        order.setUserId(memberId);

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && outRecord.save() ){
                    try {
                        int[] nums=Db.batchSave(orderItemList,orderItemList.size());


                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);

                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();

                                //获取成本价格
                                String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                logger.info("wms获取成本价结果："+operationDetailsResult);
                                if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                    JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                    if("1".equals(operationDetailsObj.getString("Type"))){
                                        JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                        Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                        for (int i = dataArray.size() - 1; i >= 0; i--) {
                                            JSONObject dataObj=dataArray.getJSONObject(i);
                                            //stockModelCostPriceMap.put()
                                            String stockModelId = dataObj.getString("StockModelId");
                                            Double costPrice = dataObj.getDouble("CostPrice");
                                            if(StrKit.notBlank(stockModelId)){
                                                stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                            }
                                        }

                                        for (MallOrderItem mallOrderItem : orderItemList) {
                                            Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                            if(costPrice==null){
                                                costPrice=0.0;
                                            }
                                            mallOrderItem.setCostPrice(costPrice);
                                        }
                                        Db.batchUpdate(orderItemList,orderItemList.size());
                                    }
                                }

                                return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }
        return resultMap;
    }

    /**
     * 获取付款码确认信息
     * @param creatorId
     * @param code
     * @param warehouseId
     * @param paymentType
     * @param remark
     * @param array
     * @return
     */
    @Override
    public Map<String,Object> getPaymentCodeOrderGetTotalPrice(String creatorId,String code,String warehouseId,String paymentType,String remark, JSONArray array) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setTotalAmount(0.0);
        order.setRealityPaymentAmount(0.0);
        order.setType("offline");
        order.setRemark(remark);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);


        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);

            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double totalPrice=0.0;
        String modelsDescribe="";
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());

            Double unitPrice=null;
            if("2".equals(paymentType)){
                unitPrice=modelInfo.getDouble("exchangeBeanCoupons");
            }else{
                unitPrice=modelInfo.getDouble("exchangePrice");
            }
            item.setPrice(unitPrice);
            item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(BigDecimal.valueOf(item.getNum())).doubleValue());
            item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            totalPrice=BigDecimal.valueOf(totalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
            modelsDescribe+=modelInfo.getStr("name")+"("+modelInfo.getStr("standard")+")*"+item.getNum()+";";
        }
        order.setTotalIntegral(totalPrice);
        order.setRealityPaymentIntegral(totalPrice);
        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());

        //扣除总积分
        double totalIntegral=0.0;

        totalIntegral=totalPrice;
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("title","兑换商品");
        if("1".equals(paymentType)){
            jsonObject.put("paymentType","积分");
        }else if("2".equals(paymentType)){
            jsonObject.put("paymentType","豆豆券");
        }
        jsonObject.put("paymentValue",totalIntegral);
        jsonObject.put("status","1");//1创建，2已确认，3取消
        String result=jbootRedis.setex("mall_card_payment_code"+code,60*5, JSON.toJSONString(jsonObject));
        if(!"OK".equals(result)){
            resultMap.put("msg","操作失败，请重试");
            return resultMap;
        }else{
            resultMap.put("flag",true);
        }
        return resultMap;
    }

    /**
     * 获取会员付款码状态
     * @param code
     * @return
     */
    @Override
    public Map<String,Object> getPaymentCodeOrderStatus(String code){
        String result=jbootRedis.get("mall_card_payment_code"+code);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        if(StrKit.isBlank(result)){
            resultMap.put("msg","订单不存在或已超时");
            return resultMap;
        }else{
            JSONObject jsonObject=JSON.parseObject(result);
            resultMap.put("data",jsonObject);
            resultMap.put("flag",true);
        }
        return resultMap;
    }

    /**
     * 保存付款码状态
     * @param code
     * @param status
     * @return
     */
    @Override
    public Map<String,Object> savePaymentCodeOrderStatus(String code,String status){
        String result=jbootRedis.get("mall_card_payment_code"+code);
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        if(StrKit.isBlank(result)){
            resultMap.put("msg","订单不存在或已超时");
            return resultMap;
        }else{
            JSONObject jsonObject=JSON.parseObject(result);
            jsonObject.put("status",status);
            jbootRedis.setex("mall_card_payment_code"+code,60*5, JSON.toJSONString(jsonObject));

            resultMap.put("flag",true);
        }
        return resultMap;
    }


    @Override
    public Map<String, Object> saveCashOrder(String creatorId, String paymentType,String cardNumber
            ,String originalPrice,String discountPrice,String totalPrice,String activity,String giveModels
            ,String warehouseId,String coupon,String checkCode,String remark, JSONArray array,String cardTypeKey,String reallyPay,String scanContent) {
        MallOrder order=new MallOrder();
        order.setId(IdGen.getUUID());
        order.setCreateBy(creatorId);
        order.setCardNumber(cardNumber);
        order.setTotalIntegral(0.0);
        order.setRealityPaymentIntegral(0.0);
        order.setType("offline_cash");
        order.setRemark(remark);

        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);

        order.setCheckCode(null);
        if(StrKit.notBlank(checkCode)){
            //判断该码是否被使用
            Record record = Db.findFirst("select * from crm_card_roll_record where del_flag='0' and id=? ", checkCode);
            if(record==null){
                resultMap.put("msg","该核销码不存在");
                return resultMap;
            }
            if("1".equals(record.getStr("is_enable"))){
                resultMap.put("msg","该核销码已失效");
                return resultMap;
            }
            if(Db.findFirst("select id from mall_order where del_flag='0' and check_code=? limit 1",checkCode)!=null){
                resultMap.put("msg","该核销码已被使用");
                return resultMap;
            }
            if("1".equals(record.getStr("is_use"))){
                resultMap.put("msg","该核销码已被核销");
                return resultMap;
            }
            order.setIsCheck("0");
            order.setCheckCode(checkCode);
        }else{
            order.setIsCheck("1");
        }

        if(StrKit.notBlank(order.getCardNumber())){
            FinaMembershipCard card=finaMembershipCardService.getCardByNumber(cardNumber);
            if(card==null){
                resultMap.put("msg",cardNumber+"会员卡不存在");
                return resultMap;
            }
        }

        List<MallOrderItem> orderItemList=new ArrayList<>();
        List<String> modelIds=new ArrayList<>();
        for (int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);
            modelIds.add(obj.getString("id"));
            MallOrderItem item=new MallOrderItem();
            item.setId(IdGen.getUUID());
            item.setOrderId(order.getId());
            //购买、兑换
            item.setType("1");
            item.setModelId(obj.getString("id"));
            item.setNum(obj.getDouble("num"));
            item.setOutWarehousesId(warehouseId);
            orderItemList.add(item);
        }

        Map<String, Record> modelModel=wmsStockModelService.findModelInfoByIds(modelIds);

        double modelTotalPrice=0.0;
        for(MallOrderItem item:orderItemList){
            Record modelInfo=modelModel.get(item.getModelId());
            //获取销售价格
            WmsStockModelWarehousePrice warehousePrice=wmsStockModelWarehousePriceService.getModelWarehousePrice(item.getModelId(),warehouseId);
            if(warehousePrice==null || warehousePrice.getSalePrice()==null || warehousePrice.getSalePrice()<0){
                resultMap.put("msg",modelInfo.getStr("name")+"的仓库销售价格未配置");
                return resultMap;
            }
            Double unitPrice=warehousePrice.getSalePrice();
            item.setPrice(unitPrice);
            item.setTotalFee(BigDecimal.valueOf(unitPrice).multiply(BigDecimal.valueOf(item.getNum())).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
            item.setPayAmount(item.getTotalFee());
            item.setDiscountPrice(0.0);
            item.setRuboutAmount(0.0);
            //item.setModelImageUrl(modelInfo.getStr("imgUrl"));

            modelTotalPrice=BigDecimal.valueOf(modelTotalPrice).add(BigDecimal.valueOf(item.getTotalFee())).doubleValue();
        }
        //页面计算金额
        Double pageAmount=Double.valueOf(originalPrice);

        //获取优惠配置
        List<MallActivity> activityList=mallActivityService.getActivityList(pageAmount,warehouseId,new Date());

        JSONObject jsonObject=JSON.parseObject(activity);
        if(jsonObject!=null){
            if((jsonObject!=null && (activityList==null || activityList.size()==0))
                    || (jsonObject==null && (activityList!=null && activityList.size()>0))){
                resultMap.put("msg","前后端活动获取不一致");
                return resultMap;
            }
        }

        //折扣金额
        double cDiscountPrice=0.0;
        //总金额
        double ctotalPrice=modelTotalPrice;

        //活动折扣
        List<MallActivityGiveConfig> updateGiveConfigList=new ArrayList<>();
        Map<String,Double> giveItemArray=new HashMap<>();
        if(jsonObject!=null){
            MallActivity useActivity=null;
            for(MallActivity mallActivity:activityList){
                if(mallActivity.getId().equalsIgnoreCase(jsonObject.getString("id"))){
                    useActivity=mallActivity;
                    break;
                }
            }
            if(useActivity==null){
                resultMap.put("msg","选中的活动不在可参加活动范围中");
                return resultMap;
            }

            //计算后端价格
            List<MallActivityDiscountConfig> discountConfigList=useActivity.getDiscountConfigList();
            if(discountConfigList!=null && discountConfigList.size()==1){
                MallActivityDiscountConfig discountConfig=discountConfigList.get(0);
                if("1".equals(discountConfig.getType())){
                    //满减固定值
                    cDiscountPrice=discountConfig.getSatisfySubtractAmount();
                    ctotalPrice=BigDecimal.valueOf(modelTotalPrice).subtract(BigDecimal.valueOf(cDiscountPrice)).doubleValue();

                    discountConfig.getSatisfyAmount();

                    //计算优惠金额分摊
                    List<BigDecimal> orderItemAmountList=new ArrayList<>();
                    for (MallOrderItem mallOrderItem : orderItemList) {
                        orderItemAmountList.add(BigDecimal.valueOf(mallOrderItem.getTotalFee()));
                    }
                    List<BigDecimal> newOrderItemAmountList = MallUtils.preferentialDistributionAlgorithm(orderItemAmountList, BigDecimal.valueOf(cDiscountPrice));
                    for (int i = 0; i < newOrderItemAmountList.size(); i++) {
                        MallOrderItem mallOrderItem = orderItemList.get(i);
                        mallOrderItem.setPayAmount(newOrderItemAmountList.get(i).doubleValue());
                        //折扣金额
                        mallOrderItem.setDiscountPrice(BigDecimal.valueOf(mallOrderItem.getTotalFee()).subtract(BigDecimal.valueOf(mallOrderItem.getPayAmount()))
                                .setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
                    }
                }else if("2".equals(discountConfig.getType())){
                    if(discountConfig.getSatisfyDiscountValue()==0){
                        ctotalPrice=0.0;
                        cDiscountPrice=BigDecimal.valueOf(modelTotalPrice).subtract(BigDecimal.valueOf(ctotalPrice)).doubleValue();
                    }else{
                        //打折
                        double discount=discountConfig.getSatisfyDiscountValue()/10;
                        ctotalPrice=BigDecimal.valueOf(modelTotalPrice).multiply(BigDecimal.valueOf(discount)).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                        cDiscountPrice=BigDecimal.valueOf(modelTotalPrice).subtract(BigDecimal.valueOf(ctotalPrice)).doubleValue();

                        //计算优惠金额分摊
                        List<BigDecimal> orderItemAmountList=new ArrayList<>();
                        for (MallOrderItem mallOrderItem : orderItemList) {
                            orderItemAmountList.add(BigDecimal.valueOf(mallOrderItem.getTotalFee()));
                        }
                        List<BigDecimal> newOrderItemAmountList = MallUtils.preferentialDistributionAlgorithm(orderItemAmountList, BigDecimal.valueOf(cDiscountPrice));
                        for (int i = 0; i < newOrderItemAmountList.size(); i++) {
                            MallOrderItem mallOrderItem = orderItemList.get(i);
                            mallOrderItem.setPayAmount(newOrderItemAmountList.get(i).doubleValue());
                            //折扣金额
                            mallOrderItem.setDiscountPrice(BigDecimal.valueOf(mallOrderItem.getTotalFee()).subtract(BigDecimal.valueOf(mallOrderItem.getPayAmount()))
                                    .setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue());
                        }
                    }
                }
                order.setActivityId(useActivity.getId());
            }
            //原总价
            Double originalPriceD=0.0;
            if(StrKit.notBlank(originalPrice)){
                originalPriceD=Double.valueOf(originalPrice);
            }
            //折扣价
            Double discountPriceD=0.0;
            if(StrKit.notBlank(discountPrice)){
                discountPriceD=Double.valueOf(discountPrice);
            }
            //折扣后价格
            Double totalPriceD=0.0;
            if(StrKit.notBlank(totalPrice)){
                totalPriceD=Double.valueOf(totalPrice);
            }
            if((modelTotalPrice!=originalPriceD)||(discountPriceD!=cDiscountPrice)||(totalPriceD!=ctotalPrice)){
                resultMap.put("msg","前后端计算折扣价存在差异");
                return resultMap;
            }

            List<MallActivityGiveConfig> giveConfigList=useActivity.getGiveConfigList();

            JSONArray giveArray=JSON.parseArray(giveModels);

            if(giveArray!=null && giveArray.size()>0 && giveArray.size()!=giveConfigList.size()){
                resultMap.put("msg","前后端赠送商品存在差异");
                return resultMap;
            }

            if(giveConfigList!=null && giveConfigList.size()>0){
                Map<String,Double> giveConfigMap=new HashMap<>();
                for(MallActivityGiveConfig giveConfig:giveConfigList){
                    //还剩余多少件
                    giveConfigMap.put(giveConfig.getGiveStockModelId().toLowerCase(),giveConfig.getGiveMaxCount());
                }
                Map<String,Double> giveModelMap=new HashMap<>();
                for(int i=0;i<giveArray.size();i++){
                    JSONObject giveObj=giveArray.getJSONObject(i);
                    if(giveModelMap.containsKey(giveObj.getString("modelsId").toLowerCase())){
                        giveModelMap.put(giveObj.getString("modelsId").toLowerCase(),BigDecimal.valueOf(giveObj.getDouble("giveCount"))
                                .add(BigDecimal.valueOf(giveModelMap.get(giveObj.getString("modelsId").toLowerCase()))).doubleValue());
                        giveItemArray.put(giveObj.getString("modelsId").toLowerCase(),BigDecimal.valueOf(giveObj.getDouble("giveCount"))
                                .add(BigDecimal.valueOf(giveItemArray.get(giveObj.getString("modelsId").toLowerCase()))).doubleValue());
                    }else{
                        giveModelMap.put(giveObj.getString("modelsId").toLowerCase(),giveObj.getDouble("giveCount"));
                        giveItemArray.put(giveObj.getString("modelsId").toLowerCase(),giveObj.getDouble("giveCount"));
                    }
                }

                for(String key:giveConfigMap.keySet()){
                    double giveMaxCount=giveConfigMap.get(key);
                    Double giveCount=giveModelMap.get(key);
                    if(giveCount==0){
                        continue;
                    }
                    if(giveCount>giveMaxCount){
                        resultMap.put("msg","赠送商品存在库存不足");
                        return resultMap;
                    }
                    order.setActivityId(useActivity.getId());
                    WmsStockModels modelInfo=wmsStockModelService.findById(key);

                    //获取销售价格
                    WmsStockModelWarehousePrice warehousePrice=wmsStockModelWarehousePriceService.getModelWarehousePrice(modelInfo.getId(),warehouseId);
                    if(warehousePrice==null || warehousePrice.getSalePrice()==null || warehousePrice.getSalePrice()<0){
                        resultMap.put("msg",modelInfo.getStr("name")+"的仓库销售价格未配置");
                        return resultMap;
                    }

                    modelIds.add(key);
                    MallOrderItem item=new MallOrderItem();
                    item.setId(IdGen.getUUID());
                    item.setOrderId(order.getId());
                    //赠送
                    item.setType("2");
                    item.setModelId(key);
                    item.setNum(giveCount);
                    item.setPrice(warehousePrice.getSalePrice());
                    item.setTotalFee(0.0);
                    item.setOutWarehousesId(warehouseId);
                    orderItemList.add(item);

                    MallActivityGiveConfig updateGiveConifg= mallActivityGiveConfigService.findGiveModelByActivityId(useActivity.getId(),key);
                    if(updateGiveConifg.getUseGiveCount()==null){
                        updateGiveConifg.setUseGiveCount(0D);
                    }
                    updateGiveConifg.setUseGiveCount(updateGiveConifg.getUseGiveCount()+giveCount);
                    updateGiveConifg.setUpdateDate(new Date());
                    updateGiveConfigList.add(updateGiveConifg);
                }

            }
        }

        if(BigDecimal.valueOf(pageAmount).compareTo(BigDecimal.valueOf(modelTotalPrice))!=0){
            resultMap.put("msg","前后端计算金额不一致");
            return resultMap;
        }

        //获取卡类型计算折扣
        if(cardTypeKey!=null){
            PosCardType posCardType = PosCardType.getPosCardTypeByKey(cardTypeKey);
            if(posCardType!=null){
                //原总价
                double dOriginalPrice=Double.parseDouble(originalPrice);
                //折扣金额
                double dDiscountPrice=Double.parseDouble(discountPrice);
                //折扣后总价
                double dTotalPrice=Double.parseDouble(totalPrice);
                if(BigDecimal.valueOf(dOriginalPrice).compareTo(BigDecimal.valueOf(modelTotalPrice))!=0
                ){
                    resultMap.put("msg","前后端计算原总价金额不一致");
                    return resultMap;
                }

                //计算出折扣后总价
                ctotalPrice =BigDecimal.valueOf(dOriginalPrice).multiply(BigDecimal.valueOf(posCardType.getDiscountValue()))
                        .setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
                //计算出折扣金额
                cDiscountPrice = BigDecimal.valueOf(modelTotalPrice).subtract(BigDecimal.valueOf(ctotalPrice)).doubleValue();
                //判断计算出的折扣金额和折扣后总价和前端传过来的是否一样
                if(BigDecimal.valueOf(ctotalPrice).compareTo(BigDecimal.valueOf(dTotalPrice))!=0
                ){
                    resultMap.put("msg","前后端计算折扣后总金额不一致");
                    return resultMap;
                }
                if (BigDecimal.valueOf(cDiscountPrice).compareTo(BigDecimal.valueOf(dDiscountPrice))!=0) {
                    resultMap.put("msg","前后端计算折扣金额不一致");
                    return resultMap;
                }



            }
        }




        //原总金额
        order.setTotalAmount(modelTotalPrice);
        //实际付款价格
        order.setRealityPaymentAmount(ctotalPrice);
        //折扣金额
        order.setDiscountPrice(cDiscountPrice);
        //抹零金额
        order.setRuboutAmount(0.0);

        //获取前端输入的收款金额计算抹零金额
        Double reallyPayDouble=Double.valueOf(reallyPay);
        if(reallyPayDouble!=null){
            //实际付款价格-前端输入金额=抹零金额
            double orderRuboutAmount= BigDecimal.valueOf(ctotalPrice).subtract(BigDecimal.valueOf(reallyPayDouble)).doubleValue();
            if(orderRuboutAmount!=0.0){
                List<BigDecimal> bigDecimalList=new ArrayList<>();
                for (MallOrderItem item : orderItemList) {
                    bigDecimalList.add(BigDecimal.valueOf(item.getPayAmount()));
                }
                List<BigDecimal> newBigDecimalList=MallUtils.preferentialDistributionAlgorithm(bigDecimalList,BigDecimal.valueOf(orderRuboutAmount));
                for (int i = 0; i < newBigDecimalList.size(); i++) {
                    MallOrderItem item = orderItemList.get(i);
                    double newPayAmount = newBigDecimalList.get(i).doubleValue();
                    double itemRuboutAmount = BigDecimal.valueOf(item.getPayAmount()).subtract(BigDecimal.valueOf(newPayAmount)).doubleValue();
                    item.setPayAmount(newPayAmount);
                    item.setRuboutAmount(itemRuboutAmount);
                }

                ctotalPrice=reallyPayDouble;
                order.setRealityPaymentAmount(reallyPayDouble);
                order.setRuboutAmount(orderRuboutAmount);
            }
        }


        order.setPaymentTime(new Date());
        order.setConsignTime(new Date());
        order.setDelFlag("0");
        order.setStatus("5");
        order.setCreateDate(new Date());
        order.setUpdateBy(creatorId);
        order.setUpdateDate(new Date());
        order.setOrderNo(MakeOrderNum.makeOrderNum());
        resultMap.put("orderNo",order.getOrderNo());
        resultMap.put("id",order.getId());
        JSONArray outItemArray=new JSONArray();
        //购买的
        for(int i=0;i<array.size();i++){
            JSONObject obj=array.getJSONObject(i);

            JSONObject outObj=new JSONObject();
            outObj.put("stockModelId",obj.getString("id"));
            outObj.put("count",obj.getDouble("num"));
            outItemArray.add(outObj);
        }
        //赠送的
        for (String key : giveItemArray.keySet()) {
            boolean isExist=false;
            for (int i = 0; i < outItemArray.size(); i++) {
                JSONObject outObj=outItemArray.getJSONObject(i);
                if(outObj.getString("stockModelId").equalsIgnoreCase(key)){
                    outObj.put("count",outObj.getDouble("count")+giveItemArray.get(key));
                    isExist=true;
                    break;
                }
            }
            if(!isExist){
                JSONObject outObj=new JSONObject();
                outObj.put("stockModelId",key);
                outObj.put("count",giveItemArray.get(key));
                outItemArray.add(outObj);
            }
        }

        JSONObject warehouseOutObj=new JSONObject();
        warehouseOutObj.put("warehouseId",warehouseId);
        warehouseOutObj.put("list",outItemArray);

        JSONArray paramArray=new JSONArray();
        paramArray.add(warehouseOutObj);
        //发送扣库存记录
        Map<String,String> outToMemberParam=new HashMap<>();
        outToMemberParam.put("userId",creatorId);
        WmsWarehouses warehouses = wmsWarehouseService.findById(warehouseId);
        String baseId=warehouses.getBaseId();
        if(StrKit.isBlank(baseId)){
            resultMap.put("msg","该仓库未设置所属基地");
            return resultMap;
        }
        MainBase mainBase= mainBaseService.findById(warehouses.getBaseId());
        if(paymentType.equals(MallPaymentType.shouqianba.getKey())){

            if(StrKit.isBlank(mainBase.getPosId())){
                resultMap.put("msg","该仓库所属基地的收钱吧收款账号未设置");
                return resultMap;
            }
        }
        JSONObject baseCardNumbers=JSONObject.parseObject(Global.baseCardNumbers);
        if(StrKit.notBlank(order.getCardNumber())){
            outToMemberParam.put("cardNo",order.getCardNumber());
        }else{
            MainBase base = mainBaseService.findById(baseId);
            if(StrKit.isBlank(base.getChargeCardNumber())){
                resultMap.put("msg",base.getBaseName()+"的负责人会员卡未设置");
                return resultMap;
            }
            outToMemberParam.put("cardNo",base.getChargeCardNumber());
        }
        outToMemberParam.put("list", JSON.toJSONString(paramArray));
        MallOrderWarehouseOutRecord outRecord=new MallOrderWarehouseOutRecord();
        outRecord.setId(IdGen.getUUID());
        outRecord.setOrderId(order.getId());
        outRecord.setParam(JSON.toJSONString(outToMemberParam));
        outRecord.setDelFlag("0");
        outRecord.setCreateBy(creatorId);
        outRecord.setCreateDate(new Date());
        outRecord.setUpdateBy(creatorId);
        outRecord.setUpdateDate(new Date());
        outRecord.setStatus("1");

        //创建订单支付记录
        MallOrderPayment orderPayment=null;
        if(StrKit.notBlank(paymentType)){
            orderPayment=new MallOrderPayment();
            orderPayment.setId(IdGen.getUUID());
            orderPayment.setOrderId(order.getId());
            orderPayment.setPayType(paymentType);
            orderPayment.setAmount(ctotalPrice);
            orderPayment.setDelFlag("0");
            orderPayment.setCreateBy(creatorId);
            orderPayment.setCreateDate(new Date());
            orderPayment.setUpdateBy(creatorId);
            orderPayment.setUpdateDate(new Date());
        }


        //现金券支付方式
        MallOrderPayment couponPayment=null;
        if(StrKit.notBlank(coupon)){
            JSONArray couponArray=JSON.parseArray(coupon);
            JSONObject couponObj=couponArray.getJSONObject(0);
            if(couponObj.getIntValue("num")<=0){
                resultMap.put("msg","现金券数量不能为0");
                return resultMap;
            }
            if(couponObj!=null && couponObj.getIntValue("num")>0){
                couponPayment=new MallOrderPayment();
                couponPayment.setId(IdGen.getUUID());
                couponPayment.setOrderId(order.getId());
                couponPayment.setPayType(MallPaymentType.cashCoupons.getKey());
                couponPayment.setCouponId(couponObj.getString("couponId"));
                couponPayment.setAmount(couponObj.getDouble("num"));
                couponPayment.setDelFlag("0");
                couponPayment.setCreateBy(creatorId);
                couponPayment.setCreateDate(new Date());
                couponPayment.setUpdateBy(creatorId);
                couponPayment.setUpdateDate(new Date());
            }
        }
        MallOrderPayment couponOrderPayment=couponPayment;
        MallOrderPayment cashOrderPayment=orderPayment;

        String[] msgArray=new String[1];
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                if(order.save() && outRecord.save()){
                    if(couponOrderPayment!=null){
                        couponOrderPayment.save();
                    }
                    if(cashOrderPayment!=null){
                        cashOrderPayment.save();
                    }
                    int[] nums=Db.batchSave(orderItemList,orderItemList.size());
                    if(updateGiveConfigList.size()>0){
                        Db.batchUpdate(updateGiveConfigList,updateGiveConfigList.size());
                    }
                    try {
                        logger.info("wms扣库存参数："+JSON.toJSONString(outToMemberParam));
                        //发送扣库存请求
                        //String outResult=HttpKit.get(Global.warehouseOutToStockUrl,outToMemberParam);
                        String outResult= HttpClientsUtils.httpPostForm(Global.warehouseOutToStockUrl,outToMemberParam,null,null);
                        logger.info("wms扣库存结果："+outResult);
                        if(outResult.startsWith("{") && outResult.endsWith("}")){
                            JSONObject obj=JSON.parseObject(outResult);
                            if("1".equals(obj.getString("Type"))){
                                JSONArray resultJsonArray=obj.getJSONArray("Data");
                                if(resultJsonArray!=null && resultJsonArray.getJSONObject(0)!=null && resultJsonArray.getJSONObject(0).getString("OperationId")!=null){
                                    outRecord.setOperationId(resultJsonArray.getJSONObject(0).getString("OperationId"));
                                    outRecord.setUpdateDate(new Date());
                                    outRecord.update();

                                    //获取成本价格
                                    String operationDetailsResult=HttpClientsUtils.get(Global.getOperationDetailsUrl+"?operationId="+outRecord.getOperationId());
                                    logger.info("wms获取成本价结果："+operationDetailsResult);
                                    if(operationDetailsResult.startsWith("{") && operationDetailsResult.endsWith("}")){
                                        JSONObject operationDetailsObj=JSON.parseObject(operationDetailsResult);
                                        if("1".equals(operationDetailsObj.getString("Type"))){
                                            JSONArray dataArray=operationDetailsObj.getJSONArray("Data");

                                            Map<String,Double> stockModelCostPriceMap=new HashMap<>();

                                            for (int i = dataArray.size() - 1; i >= 0; i--) {
                                                JSONObject dataObj=dataArray.getJSONObject(i);
                                                //stockModelCostPriceMap.put()
                                                String stockModelId = dataObj.getString("StockModelId");
                                                Double costPrice = dataObj.getDouble("CostPrice");
                                                if(StrKit.notBlank(stockModelId)){
                                                    stockModelCostPriceMap.put(stockModelId.toLowerCase(),costPrice);
                                                }
                                            }

                                            for (MallOrderItem mallOrderItem : orderItemList) {
                                                Double costPrice = stockModelCostPriceMap.get(mallOrderItem.getModelId().toLowerCase());
                                                if(costPrice==null){
                                                    costPrice=0.0;
                                                }
                                                mallOrderItem.setCostPrice(costPrice);
                                            }
                                            Db.batchUpdate(orderItemList,orderItemList.size());
                                        }
                                    }

                                }

                                //return true;
                            }else{
                                outRecord.setStatus("0");
                                outRecord.setUpdateDate(new Date());
                                outRecord.update();
                                resultMap.put("msg",obj.getString("Msg"));
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                    //收钱吧扣费
                    try {
                        if(MallPaymentType.shouqianba.getKey().equals(cashOrderPayment.getPayType())){
                            //创建订单
                            Map<String,String> createPayOrderParams=new HashMap<>();
                            //createPayOrderParams.put("userId",creatorId);
                            createPayOrderParams.put("orderId",order.getId());
                            createPayOrderParams.put("amount",cashOrderPayment.getAmount().toString());
                            createPayOrderParams.put("orderTypeId",Global.orderTypeId);
                            createPayOrderParams.put("title",warehouses.getName()+order.getOrderNo()+"购物");
                            logger.info(order.getOrderNo()+"创建支付订单参数："+JSON.toJSONString(createPayOrderParams));
                            String createPayOrderResult = HttpClientsUtils.httpPostForm(Global.createPayOrderUrl,createPayOrderParams,null,null);
                            logger.info(order.getOrderNo()+"创建支付订单结果："+createPayOrderResult);

                            if(!createPayOrderResult.startsWith("{") || !createPayOrderResult.endsWith("}")){
                                msgArray[0]="创建支付订单异常"+createPayOrderResult;
                                return false;
                            }

                            JSONObject createPayOrderObj = JSON.parseObject(createPayOrderResult);
                            if(createPayOrderObj==null || !"1".equals(createPayOrderObj.getString("Type"))){
                                msgArray[0]="创建支付订单异常返回异常："+createPayOrderResult;
                                return false;
                            }
                            String payOrderId=createPayOrderObj.getJSONObject("Data").getString("PayOrderId");
                            if(StrKit.isBlank(payOrderId)){
                                msgArray[0]="创建支付订单异常无支付id："+createPayOrderResult;
                                return false;
                            }

                            /*Map<String,String> payBtoCParams=new HashMap<>();
                            payBtoCParams.put("userId",creatorId);
                            payBtoCParams.put("orderId",payOrderId);
                            payBtoCParams.put("terminalId","6cca1336-98ee-40d1-ae45-c6bb194c5f1d");
                            payBtoCParams.put("description","x");
                            payBtoCParams.put("scanContent",scanContent);*/

                            String payBtoCUrl=Global.payBtoCUrl+"?userId="+creatorId+"&orderId="+payOrderId
                                    +"&terminalId="+mainBase.getPosId()+"&description="+order.getOrderNo()+"&scanContent="+scanContent;
                            logger.info(order.getOrderNo()+"支付订单参数："+payBtoCUrl);
                            //String payBtoCResult = HttpClientsUtils.httpPostForm(Global.payBtoCUrl,createPayOrderParams,null,null);
                            /*String payBtoCResult = HttpClientsUtils.httpGet(Global.payBtoCUrl+"?userId="+creatorId+"&orderId="+payOrderId
                                    +"&terminalId="+"6cca1336-98ee-40d1-ae45-c6bb194c5f1d"+"&description="+"x"+"&scanContent="+scanContent,null,null);*/
                            String payBtoCResult = HttpClientsUtils.httpGet(payBtoCUrl,null,null);

                            if(!payBtoCResult.startsWith("{") || !payBtoCResult.endsWith("}")){
                                msgArray[0]="收钱吧支付订单异常"+payBtoCResult;
                                return false;
                            }
                            logger.info(order.getOrderNo()+"支付订单结果："+payBtoCResult);
                            JSONObject payBtoCObj = JSON.parseObject(payBtoCResult);
                            if(payBtoCObj==null || !"1".equals(payBtoCObj.getString("Type"))){
                                msgArray[0]="收钱吧支付订单异常"+payBtoCResult;
                                return false;
                            }
                            //收钱吧实际收款类型
                            String payType=payBtoCObj.getJSONObject("Data").getString("PayWayName");

                            Record payStatusRecord=new Record();
                            payStatusRecord.set("wait",false);
                            payStatusRecord.set("flag",false);
                            payStatusRecord.set("count",0);
                            payStatusRecord.set("msg","");

                            ScheduledExecutorService executor = Executors.newSingleThreadScheduledExecutor();
                            CountDownLatch countDownLatch = new CountDownLatch(1);
                            executor.scheduleWithFixedDelay(new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        payStatusRecord.set("count",payStatusRecord.getInt("count")+1);

                                        if(payStatusRecord.getInt("count")>30){
                                            countDownLatch.countDown();
                                            executor.shutdown();
                                        }else{
                                            if(!payStatusRecord.getBoolean("wait")){
                                                payStatusRecord.set("wait",true);
                                                String payOrderStateStr = HttpClientsUtils.httpGet(Global.getPayOrderStateUrl+"?payOrderId="+payOrderId,null,null);
                                                logger.info(order.getOrderNo()+"第"+payStatusRecord.getInt("count")+"次获取"+payOrderId+"收钱吧支付结果:"+payOrderStateStr);
                                                if(!payOrderStateStr.startsWith("{") || !payOrderStateStr.endsWith("}")){
                                                    payStatusRecord.set("msg","获取收钱吧支付结果异常"+payOrderStateStr);
                                                    payStatusRecord.set("wait",false);
                                                    countDownLatch.countDown();
                                                    executor.shutdown();
                                                    return;
                                                }
                                                //logger.info(order.getOrderNo()+"获取收钱吧支付结果："+payOrderStateStr);
                                                JSONObject payOrderStateCObj = JSON.parseObject(payOrderStateStr);
                                                if(payOrderStateCObj==null || !"1".equals(payOrderStateCObj.getString("Type"))){
                                                    payStatusRecord.set("msg","获取收钱吧支付结果异常"+payOrderStateStr);
                                                    if(payOrderStateCObj!=null && StrKit.notBlank(payOrderStateCObj.getString("Msg"))){
                                                        payStatusRecord.set("msg","获取收钱吧支付结果异常："+payOrderStateCObj.getString("Msg"));
                                                    }
                                                    payStatusRecord.set("wait",false);
                                                    countDownLatch.countDown();
                                                    executor.shutdown();
                                                    return;
                                                }
                                                Boolean isError = payOrderStateCObj.getJSONObject("Data").getBoolean("IsError");
                                                Boolean isFinished = payOrderStateCObj.getJSONObject("Data").getBoolean("IsFinished");


                                                if(isError){
                                                    payStatusRecord.set("msg","获取收钱吧支付结果异常："+payOrderStateCObj.getJSONObject("Data").getString("ErrorMsg"));
                                                    payStatusRecord.set("wait",false);
                                                    if(isFinished){
                                                        countDownLatch.countDown();
                                                        executor.shutdown();
                                                    }
                                                    return;
                                                }else{
                                                    Boolean isPaid = payOrderStateCObj.getJSONObject("Data").getBoolean("IsPaid");
                                                    if(isPaid){
                                                        payStatusRecord.set("wait",false);
                                                        payStatusRecord.set("flag",true);
                                                        countDownLatch.countDown();
                                                        executor.shutdown();
                                                    }
                                                }
                                            }
                                        }
                                    }catch (Exception e){
                                        logger.info(order.getOrderNo()+"获取收钱吧支付结果异常:"+e.getMessage());
                                        payStatusRecord.set("msg","获取收钱吧支付结果异常"+e.getMessage());
                                        payStatusRecord.set("wait",false);
                                        countDownLatch.countDown();
                                        executor.shutdown();
                                    }
                                }
                            }, 1, 2, TimeUnit.SECONDS);
                            countDownLatch.await();
                            if(payStatusRecord.getBoolean("flag")){
                                cashOrderPayment.setShowQianBaType(payType);
                                cashOrderPayment.update();
                                return true;
                            }else{
                                msgArray[0]=payStatusRecord.getStr("msg");
                                return false;
                            }

                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                    return true;
                }
                return false;
            }
        });
        resultMap.put("flag",flag);
        if(!flag){
            if(msgArray!=null && StrKit.notBlank(msgArray[0])){
                resultMap.put("msg",msgArray[0]);
            }else if(StrKit.isBlank((String)resultMap.get("msg"))){
                resultMap.put("msg","交易异常");
            }
        }
        return resultMap;
    }

    private static long orderNum = 0L;
    private static String date ;
    /**
     * 生成订单编号
     * @return
     */
    public static synchronized String getOrderNo() {
        String str = new SimpleDateFormat("yyMMddHHmm").format(new Date());
        if(date==null||!date.equals(str)){
            date = str;
            orderNum  = 0L;
        }
        orderNum ++;
        long orderNo = Long.parseLong((date)) * 10000;
        orderNo += orderNum;;
        return orderNo+"";
    }

    public static void main(String[] args) throws InterruptedException {
        for (int i = 0; i < 10000; i++) {
            System.out.println(MallOrderServiceImpl.getOrderNo());
            Thread.sleep(1000);
        }
    }


    @Override
    public MallOrder getMallOrderByCheckCode(String checkCode){
        return DAO.findFirst("select * from mall_order where del_flag='0' and check_code=? ",checkCode);
    }

    @Override
    public List<Record> cashPaymentSummary(String warehouseId, String startDate, String endDate,String userId) {
        String sql="select b.pay_type as payTypeKey,COUNT(a.id) as orderCount,SUM(a.reality_payment_amount) as realityPaymentAmount from mall_order a " +
                " INNER JOIN mall_order_payment b on b.order_id=a.id " +
                " INNER JOIN mall_order_item c on c.id =(select id from mall_order_item where order_id=a.id limit 1) " +
                "  where a.del_flag='0' and a.`type`='offline_cash'  and a.payment_time BETWEEN ? and ? ";
        List<String> params=new ArrayList<>();
        params.add(startDate+" 00:00:00");
        params.add(endDate+" 23:59:59");
        if(StrKit.notBlank(warehouseId)){
            sql+=" and c.out_warehouses_id=? ";
            params.add(warehouseId);
        }else{
            List<WmsWarehouses> wmsWarehousesList=wmsWarehouseService.getAllWmsWarehouseByUserId(userId);
            if(wmsWarehousesList==null || wmsWarehousesList.size()==0){

                return new ArrayList<Record>();
            }else{
                String str="";
                for(WmsWarehouses warehouses:wmsWarehousesList){
                    str+="?,";
                    params.add(warehouses.getId());
                }
                str=str.substring(0,str.length()-1);
                sql+=" and c.out_warehouses_id in("+str+") ";

            }

        }

        sql+="  GROUP BY b.pay_type ";
        List<Record> recordList=Db.find(sql,params.toArray());
        Map<String,Record> map=new HashMap<>();
        if(recordList!=null && recordList.size()>0){
            for(Record record:recordList){
                map.put(record.getStr("payTypeKey"),record);
            }
        }
        MallPaymentType[] types=MallPaymentType.values();
        List<Record> returnList=new ArrayList<>();
        for(MallPaymentType type:types){
            Record record=new Record();
            record.set("payType",type.getValue());
           if(map.containsKey(type.getKey())){
               Record mapRecord=map.get(type.getKey());
               record.set("realityPaymentAmount",mapRecord.getDouble("realityPaymentAmount"));
               record.set("orderCount",mapRecord.getInt("orderCount"));
           }else{
               record.set("realityPaymentAmount",0.0);
               record.set("orderCount",0);
           }
            returnList.add(record);
        }

        return returnList;
    }
}
