package com.cszn.integrated.service.provider.member;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.service.api.main.MainFunctionSwitchService;
import com.cszn.integrated.service.api.member.MmsFaceLiveUploadService;
import com.cszn.integrated.service.entity.enums.BedDynamicType;
import com.cszn.integrated.service.entity.enums.FunctionSwitch;
import com.cszn.integrated.service.entity.enums.SwitchType;
import com.cszn.integrated.service.entity.member.MmsFaceLiveUpload;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Record;
import com.xiaoleilu.hutool.date.DateUtil;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

@Bean
public class MmsFaceLiveUploadServiceImpl extends JbootServiceBase<MmsFaceLiveUpload> implements MmsFaceLiveUploadService {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private MainFunctionSwitchService mainFunctionSwitchService;


    @Override
    public Map<String,Object> uploadLiveFace(String appNo,String baseId,String liveFlag,String times,String recordData) {
        Map<String,Object> result=new HashMap<>();
        try {
            SimpleDateFormat s=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<MmsFaceLiveUpload> uploadList=new ArrayList<>();
            JSONArray jsonArray = JSONArray.parseArray(recordData);
            for(int i=0;i<jsonArray.size();i++){
                JSONObject json = jsonArray.getJSONObject(i);
                String bookNo = json.getString("bookNo");
                String idcard = json.getString("idcard");
                if(StringUtils.isNotBlank(idcard) && idcard.length() > 20){
                    result.put("error","身份证号码过长,不可提交");
                    return result;
                }
                MmsFaceLiveUpload upload=new MmsFaceLiveUpload();
                upload.setId(IdGen.getUUID());
                upload.setAppNo(appNo);
                upload.setBaseId(baseId);
                upload.setBookNo(bookNo);
                upload.setLiveFlag(liveFlag);
                upload.setIdcard(idcard);
                upload.setStatus("0");//未上传
                upload.setTime(s.parse(times));
                upload.setCreateTime(new Date());
                uploadList.add(upload);
            }
            logger.info("入库入退住数据：[{}]", JSON.toJSONString(uploadList));
            boolean flag=Db.tx(new IAtom() {
                @Override
                public boolean run() throws SQLException {
                    try {
                        int[] ints= Db.batchSave(uploadList,uploadList.size());
                        for (int i:ints){
                            if(i<1){
                                return false;
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                    return true;
                }
            });

            if(flag){
                result.put("flag",flag);
            }else{
                result.put("error","保存失败");
            }
            result.put("appNo",appNo);
            result.put("liveFlag",liveFlag);
            result.put("times",times);
        }catch (Exception e){
            e.printStackTrace();
        }
        return result;
    }

    

    @Override
    public List<Map<String,Object>> getPersonFace(String appNo,String baseId, String liveFlag) {
        List<Object> params = new ArrayList<>();
        params.add(appNo);
        params.add(liveFlag);
        String sql = "select * from (select u.id,m.id as memberId,m.full_name as name,m.surname,m.idcard,u.base_id as baseId,f.picture as imgBase64,u.time,m.gender from mms_face_live_upload u " +
                "left join mms_member m on m.idcard=u.idcard " +
                "left join mms_member_face f on f.member_id=m.id " +
                "where m.del_flag = 0 and u.status != 1 and f.verify_flag = 1 and TO_DAYS(time)=TO_DAYS(now()) and u.app_no=? and live_flag=? ";
        if(StringUtils.isNotBlank(baseId)){
            sql += "and u.base_id=? ";
            params.add(baseId);
        }
        sql += "order by u.time desc)tab group by baseId,idcard";
        logger.info("获取当天入住人脸数据查询SQL语句：[{}]",sql);
        List<Map<String,Object>> result=new ArrayList<>();//返回未上传设备人脸数据
        List<Record> recordList= Db.find(sql,params.toArray());
        if(recordList != null && recordList.size() > 0) {
            for (Record record : recordList) {
                if(record != null) {
                    if (record.getBytes("imgBase64") == null) {
                        record.set("imgBase64", "");
                    } else {
                        record.set("imgBase64", new String(record.getBytes("imgBase64")));
                    }
                    if (StringUtils.isBlank(record.getStr("gender"))) {
                        record.set("gender", "");
                    }
                    if(StringUtils.isBlank(record.getStr("surname"))){
                        if(StringUtils.isNotBlank(record.getStr("name"))){
                            String name = record.getStr("name");
                            if(name.length() == 3 || name.length() == 2){
                                record.set("surname",name.substring(0,1));
                            }else if(name.length() == 4){
                                record.set("surname",name.substring(0,2));
                            }
                        }else{
                            record.set("surname","");
                        }
                    }
                    //处理年龄
                    if(StringUtils.isNotBlank(record.getStr("idcard"))){
                        record.set("age",TimeUtils.getAgeByIdcard(record.getStr("idcard")));
                    }
                    result.add(record.getColumns());
                }
            }
        }
        return result;
    }




    @Override
    public boolean ignoreUpload(String appNo,String baseId,String bookNo) {
        String switchStr = mainFunctionSwitchService.getSwitch(FunctionSwitch.cancelBookFace.getKey());//开关
        logger.info("取消预订通知忽略人脸上传设备开关：[{}]",switchStr);
        if(SwitchType.close.getKey().equals(switchStr)){
            return false;
        }

        int count = Db.update("update mms_face_live_upload set status = 2 where live_flag = 1 and app_no = ? and base_id = ? and book_no = ?",appNo,baseId,bookNo);
        logger.info("取消预订通知忽略人脸上传设备更新条数:[{}]",count);
        if(count > 0){ return true; }
        return false;
    }




    @Override
    public Record getFaceByBookNo(MmsFaceLiveUpload fu) {
        List<Object> params = Lists.newArrayList();
        String sql = "select u.id,u.app_no as appNo,u.base_id as baseId,u.book_no as bookNo,u.live_flag as liveFlag,u.idcard,u.status,u.time,u.create_time as createTime,f.picture " +
                "from mms_face_live_upload u left join mms_member m on u.idcard = m.idcard " +
                "left join mms_member_face f on m.id = f.member_id " +
                "where m.del_flag = 0 and live_flag = 1 and f.verify_flag = 1 ";
        if(StringUtils.isNotBlank(fu.getAppNo())){
            sql += "and u.app_no = ? ";
            params.add(fu.getAppNo());
        }
        if(StringUtils.isNotBlank(fu.getBaseId())){
            sql += "and u.base_id = ? ";
            params.add(fu.getBaseId());
        }
        if(StringUtils.isNotBlank(fu.getBookNo())){
            sql += "and u.book_no = ? ";
            params.add(fu.getBookNo());
        }
        if(StringUtils.isNotBlank(fu.getIdcard())){
            sql += "and u.idcard = ? ";
            params.add(fu.getIdcard());
        }
        sql += "order by u.create_time desc limit 1";
        return Db.findFirst(sql,params.toArray());
    }



    @Override
    public List<MmsFaceLiveUpload> getQuery(MmsFaceLiveUpload fu,String date) {
        List<Object> params = Lists.newArrayList();
        String sql = "select * from mms_face_live_upload where 1=1 ";
        if(StringUtils.isNotBlank(fu.getLiveFlag())){
            sql += "and live_flag = ? ";
            params.add(fu.getLiveFlag());
        }
        if(StringUtils.isNotBlank(fu.getBookNo())){
            sql += "and book_no = ? ";
            params.add(fu.getBookNo());
        }
        if(StringUtils.isNotBlank(fu.getIdcard())){
            sql += "and idcard = ? ";
            params.add(fu.getIdcard());
        }
        if(StringUtils.isNotBlank(date)){
            sql += "and DATE_FORMAT(create_time,'%Y-%m-%d') = ? ";
            params.add(date);
        }
        return DAO.find(sql,params.toArray());
    }





    @Override
    public boolean faceLiveUploadSave(JSONObject jsonObject) {
        List<MmsFaceLiveUpload> saveFuList = Lists.newArrayList();
        List<MmsFaceLiveUpload> updateFuList = Lists.newArrayList();

        try {
            String appNo = jsonObject.getString("appNo");
            String baseId = jsonObject.getString("baseId");
            JSONArray jsonArray=jsonObject.getJSONArray("data");
            logger.info("主数据上传人脸入退住数据:应用号:[{}],基地ID:[{}],入退住数组:[{}]",appNo,baseId,jsonArray);
            for(int i=0;i<jsonArray.size();i++){
                JSONObject json = jsonArray.getJSONObject(i);
                String bookNo = json.getString("bookNo");
                String checkinNo = json.getString("checkinNo");
                String idcard = json.getString("idcard");
                String dynamicType = json.getString("dynamicType");
                Integer isLongStay = json.getInteger("isLongStay");
                Date bookStartDate = null;
                if(StringUtils.isNotBlank(json.getString("bookStartDate"))){
                    bookStartDate = DateUtil.parse(json.getString("bookStartDate"));
                }
                Date bookEndDate = null;
                if(StringUtils.isNotBlank(json.getString("bookEndDate"))){
                    bookEndDate = DateUtil.parse(json.getString("bookEndDate"));
                }
                Date checkinDate = null;
                if(StringUtils.isNotBlank(json.getString("checkinDate"))){
                    checkinDate = DateUtil.parse(json.getString("checkinDate"));
                }
                Date checkoutDate = null;
                if(StringUtils.isNotBlank(json.getString("checkoutDate"))){
                    checkoutDate = DateUtil.parse(json.getString("checkoutDate"));
                }

                if(isLongStay != null && isLongStay == 0){//暂只针对短住
                    //根据动态类型区分情况
                    if(BedDynamicType.book.getKey().equals(dynamicType)){//预订
                        Long count = Db.queryLong("select count(*) from mms_face_live_upload where app_no = ? and base_id = ? and book_no = ?",appNo,baseId,bookNo);
                        if(count <= 0){
                            MmsFaceLiveUpload fu = setParams(appNo,baseId,bookNo,null,idcard,"1","0",bookStartDate);
                            saveFuList.add(fu);
                        }
                    }else if(BedDynamicType.checkin.getKey().equals(dynamicType)){//入住
                        if(StringUtils.isNotBlank(bookNo)){
                            MmsFaceLiveUpload fuExist = getFuByBookAndCheckinNo(appNo,baseId,bookNo,null);
                            if(fuExist == null){
                                MmsFaceLiveUpload fu = setParams(appNo,baseId,bookNo,checkinNo,idcard,"1","0",checkinDate);
                                saveFuList.add(fu);
                            }else{
                                fuExist.setCheckinNo(checkinNo);
                                updateFuList.add(fuExist);
                            }
                        }else{
                            //直接入住
                            Long count = Db.queryLong("select count(*) from mms_face_live_upload where app_no = ? and base_id = ? and checkin_no = ?",appNo,baseId,checkinNo);
                            if(count <= 0){
                                MmsFaceLiveUpload fu = setParams(appNo,baseId,bookNo,checkinNo,idcard,"1","0",checkinDate);
                                saveFuList.add(fu);
                            }
                        }
                    }else if(BedDynamicType.checkout.getKey().equals(dynamicType)){//退住
                        MmsFaceLiveUpload fuExist = getFuByBookAndCheckinNo(appNo,baseId,null,checkinNo);
                        if(fuExist == null){
                            logger.info("人脸：系统内未发现" + checkinNo + "入住号订单");
                            continue;
                        }
                        MmsFaceLiveUpload fu = setParams(appNo,baseId,bookNo,checkinNo,idcard,"2","0",checkoutDate);
                        saveFuList.add(fu);
                    }else if(BedDynamicType.cancelBook.getKey().equals(dynamicType)){//取消预订
                        MmsFaceLiveUpload fuExist = getFuByBookAndCheckinNo(appNo,baseId,bookNo,null);
                        if(fuExist == null){
                            logger.info("人脸:系统内未发现" + bookNo + "预订号订单");
                            continue;
                        }
                        fuExist.setStatus("2");
                        updateFuList.add(fuExist);
                    }
                }
            }

            return Db.tx(new IAtom() {
                @Override
                public boolean run() throws SQLException {
                    try {
                        if(saveFuList != null && saveFuList.size() > 0){
                            int[] nums=Db.batchSave(saveFuList,saveFuList.size());
                            for(int num:nums){
                                if(num<=0){
                                    return false;
                                }
                            }
                        }
                        if(updateFuList != null && updateFuList.size() > 0){
                            int[] nums=Db.batchUpdate(updateFuList,updateFuList.size());
                            for(int num:nums){
                                if(num<=0){
                                    return false;
                                }
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                    return true;
                }
            });
        } catch (Exception e) {
            logger.error("主数据上传人脸入退住数据出现异常:[{}]",e);
            return false;
        }
    }




    @Override
    public MmsFaceLiveUpload getFuByBookAndCheckinNo(String appNo, String baseId, String bookNo, String checkinNo) {
        List<Object> params = Lists.newArrayList();
        String sql = "select * from mms_face_live_upload where app_no = ? and base_id = ? ";
        params.add(appNo);
        params.add(baseId);
        if(StringUtils.isNotBlank(bookNo)){
            sql += "and book_no = ? ";
            params.add(bookNo);
        }
        if(StringUtils.isNotBlank(checkinNo)){
            sql += "and checkin_no = ? ";
            params.add(checkinNo);
        }
        return DAO.findFirst(sql,params.toArray());
    }




    @Override
    public MmsFaceLiveUpload setParams(String appNo, String baseId, String bookNo, String checkinNo,
                                       String idcard, String liveFlag, String status,Date time) {
        MmsFaceLiveUpload fu = new MmsFaceLiveUpload();
        fu.setId(IdGen.getUUID());
        fu.setAppNo(appNo);
        fu.setBaseId(baseId);
        fu.setBookNo(bookNo);
        fu.setCheckinNo(checkinNo);
        fu.setLiveFlag(liveFlag);
        fu.setIdcard(idcard);
        fu.setStatus(status);
        fu.setTime(time);
        fu.setCreateTime(new Date());
        return fu;
    }

}
