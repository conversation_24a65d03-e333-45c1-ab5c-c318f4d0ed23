package com.cszn.integrated.service.provider.member;

import java.util.Date;
import java.util.List;

import com.cszn.integrated.service.api.member.MmsActivityDetailService;
import com.cszn.integrated.service.entity.member.MmsActivityDetail;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.provider.weixin.WeiXinActivityServiceImpl;
import com.google.common.collect.Lists;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsActivityDetailServiceImpl extends JbootServiceBase<MmsActivityDetail> implements MmsActivityDetailService {

	public Page<MmsActivityDetail> paginateByCondition(MmsActivityDetail model, int pageNumber, int pageSize) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if (StrKit.notBlank(model.getFullName())) {
			columns.like("full_name", "%" + model.getFullName() + "%");
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "create_time asc");
	}

	@Override
	public Page<MmsActivityDetail> paginateByCondition(String userId, int pageNumber, int pageSize) {
		
		List<Object> params = Lists.newArrayList();
		String mainSql=" from mms_activity_detail where del_flag='0' ";
		
		if(StrKit.notBlank(userId)) {
			mainSql += "and user_id = ? ";
			params.add(userId);
		}
//		if(StrKit.notBlank(nowDate)) {
//			mainSql += "and DATE_FORMAT(create_time,'%Y-%m-%d') = ? ";
//            params.add(nowDate);
//		}
		mainSql+=" order by create_time desc ";
		
		return DAO.paginate(pageNumber,pageSize,"select * ",mainSql,params.toArray());
	}
	
	public List<MmsActivityDetail> findListByActivityId(String activityId) {
		return DAO.find("select * from mms_activity_detail where del_flag='0' and activity_id=?", activityId);
	}

	public MmsActivityDetail getDetailByPrizeNum(String activityId,String prizeNum){

		return DAO.findFirst("select * from mms_activity_detail where activity_id=? and prize_num=? and del_flag='0' ",activityId,prizeNum);
	}

	public String getNotExistPrizeNum(String activityId,String prizeNum){
		if(getDetailByPrizeNum(activityId,prizeNum)!=null){
			String newPrizeNum=WeiXinActivityServiceImpl.randomCode(4);
			return getNotExistPrizeNum(activityId,newPrizeNum);
		}
		return prizeNum;
	}

	@Override
	public MmsActivityDetail getMmsActivityDetailByPhone(String activityId,String phone) {

		return DAO.findFirst("select * from mms_activity_detail where activity_id=? and phone_num=? and del_flag='0'",activityId,phone);
	}

	@Override
	public MmsActivityDetail getMmsActivityDetailByUserId(String activityId,String userId) {

		return DAO.findFirst("select * from mms_activity_detail where activity_id=? and user_id=? and del_flag='0'",activityId,userId);
	}

	@Override
	public boolean saveMmsActivityDetail(MmsActivityDetail detail) {
		boolean flag=false;
		detail.setDelFlag("0");
		detail.setCreateTime(new Date());
		detail.setUpdateTime(new Date());
		synchronized (MmsActivityDetailServiceImpl.class){
			String prizeNum=WeiXinActivityServiceImpl.randomCode(4);
			detail.setPrizeNum(getNotExistPrizeNum(detail.getActivityId(),prizeNum));
			flag=detail.save();
		}
		return flag;
	}

}