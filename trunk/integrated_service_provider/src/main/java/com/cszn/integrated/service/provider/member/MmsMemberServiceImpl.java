package com.cszn.integrated.service.provider.member;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.MiniProgramUtils;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.member.MmsCustomerInfoService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.member.MmsWxUserCardRelaService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.entity.member.MmsCustomerInfo;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.member.MmsWxUserCardRela;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.sys.Area;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.LogFactory;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

@Bean
public class MmsMemberServiceImpl extends JbootServiceBase<MmsMember> implements MmsMemberService {

    @Inject
    AreaService areaService;
    @Inject
    MmsWxUserCardRelaService mmsWxUserCardRelaService;
    @Inject
    FinaMembershipCardService finaMembershipCardService;
    @Inject
    MmsCustomerInfoService mmsCustomerInfoService;
    @Inject
	private PersOrgEmployeeService persOrgEmployeeService;

	@Override
	public Page<Record> consumePage(Integer pageNumber, Integer pageSize, String memberId) {
		String sql = " from (select '本人' as consumName,(count(r.id)+sum(IFNULL(v1.followNum,0)))peopleNum,SUBSTRING(r.checkin_time,1,4)consumYear,"
				+ "GROUP_CONCAT(DISTINCT b.base_name SEPARATOR '、')baseName,SUM(case when category='tourist_bill' then r.actual_times else v.actualTimes end) as actualTimes,"
				+ "SUM(case when category='tourist_bill' then r.actual_amount else v.actualAmount end) as actualAmount,"
				+ "SUM(case when category='tourist_bill' then r.actual_integrals else v.actualIntegrals end) as actualIntegrals "
				+ " from fina_membership_card c "
				+ "left join fina_expense_record r on r.card_number=c.card_number and r.settle_status='1' "
				+ "left join ("
				+ "select expense_id,sum(actual_times)actualTimes,sum(actual_amount)actualAmount,sum(actual_integrals)actualIntegrals from fina_settle_detail "
				+ "where del_flag='0' and settle_status='1' group by expense_id"
				+ ")v on v.expense_id=r.id "
				+ "left join ("
				+ "select expense_id,count(id)followNum from fina_expense_record_follow group by expense_id"
				+ ")v1 on v1.expense_id=r.id "
				+ "left join main_base b on b.id=r.base_id "
				+ "left join mms_member m on m.id=c.member_id "
				+ "where m.full_name=r.`name` and IFNULL(r.parent_id,'')='0' and c.member_id='"+memberId+"' group by SUBSTRING(r.checkin_time,1,4) "
				+ "union all "
				+ "select '他人' as consumName,(count(r.id)+sum(IFNULL(v1.followNum,0)))peopleNum,SUBSTRING(r.checkin_time,1,4)consumYear,"
				+ "GROUP_CONCAT(DISTINCT b.base_name SEPARATOR '、')baseName,SUM(case when category='tourist_bill' then r.actual_times else v.actualTimes end) as actualTimes,"
				+ "SUM(case when category='tourist_bill' then r.actual_amount else v.actualAmount end) as actualAmount,"
				+ "SUM(case when category='tourist_bill' then r.actual_integrals else v.actualIntegrals end) as actualIntegrals from fina_membership_card c "
				+ "left join fina_expense_record r on r.card_number=c.card_number and r.settle_status='1' "
				+ "left join ("
				+ "select expense_id,sum(actual_times)actualTimes,sum(actual_amount)actualAmount,sum(actual_integrals)actualIntegrals from fina_settle_detail "
				+ "where del_flag='0' and settle_status='1' group by expense_id"
				+ ")v on v.expense_id=r.id "
				+ "left join ("
				+ "select expense_id,count(id)followNum from fina_expense_record_follow group by expense_id"
				+ ")v1 on v1.expense_id=r.id "
				+ "left join main_base b on b.id=r.base_id "
				+ "left join mms_member m on m.id=c.member_id "
				+ "where m.full_name!=r.`name` and IFNULL(r.parent_id,'')='0' and c.member_id='"+memberId+"' group by SUBSTRING(r.checkin_time,1,4)"
				+ ")vv ";
		return Db.paginate(pageNumber, pageSize, "select vv.* ", sql);
	}
	
	public Page<Record> archivesPage(Integer pageNumber, Integer pageSize, MmsMember model) {
		List<Object> params = new ArrayList<>();
		final String select = "select m.id,m.full_name as 'fullName',m.gender,m.idcard_type as 'idcardType',m.idcard,m.birthday,"
				+ "l.link_name as 'linkName',m.member_status as 'memberStatus',m.member_lv as 'memberLv',v.link_phone as 'linkPhone',"
				+ "vv.unVoidCards,vvv.hadVoidCards,m.del_flag as 'delFlag'";
		String sql = " from mms_member m "
				+ "left join mms_member_link l on l.id=m.link_id "
				+ "left join("
				+ "select member_id,GROUP_CONCAT(DISTINCT link_phone ORDER BY create_date desc)link_phone from mms_member_link "
				+ "where del_flag=0 group by member_id"
				+ ")v on v.member_id=m.id "
				+ "left join("
				+ "select member_id,GROUP_CONCAT(card_number ORDER BY create_time desc)unVoidCards from fina_membership_card "
				+ "where del_flag='0' group by member_id"
				+ ")vv on vv.member_id=m.id "
				+ "left join("
				+ "select member_id,GROUP_CONCAT(card_number ORDER BY create_time desc)hadVoidCards from fina_membership_card "
				+ "where del_flag='1' group by member_id"
				+ ")vvv on vvv.member_id=m.id where 1=1 ";
			if(StringUtils.isNotBlank(model.getFullName())){
	            sql += " and m.full_name like concat('%',?,'%') ";
	            params.add(model.getFullName());
	        }
			if(StrKit.notBlank(model.getIdcard())){
	            sql+=" and m.idcard=? ";
	            params.add(model.getIdcard());
	        }
			if(StringUtils.isNotBlank(model.getTelephone())){
	            sql += " and v.link_phone like concat('%',?,'%') ";
	            params.add(model.getTelephone());
	        }
			if(StrKit.notBlank(model.getMemberStatus())){
	            sql+=" and m.member_status=? ";
	            params.add(model.getMemberStatus());
	        }
			if(StrKit.notBlank(model.getMemberLv())){
	            sql+=" and m.member_lv=? ";
	            params.add(model.getMemberLv());
	        }
			if(StrKit.notBlank(model.getDelFlag())){
	            sql+=" and m.del_flag=? ";
	            params.add(model.getDelFlag());
	        }
			sql += " order by m.create_time desc";
		return Db.paginate(pageNumber, pageSize, select, sql, params.toArray());
	}

    @Override
    public MmsMember findMemberByCardInfo(String cardNumber, String fullName, String idCard) {
        String sql="select a.* from mms_member a inner JOIN fina_membership_card b on a.id=b.member_id " +
                "where a.full_name=? and a.idcard=? and b.card_number=? ";
        return DAO.findFirst(sql,fullName,idCard,cardNumber);
    }

    @Override
    public Page<MmsMember> findBirthdayMemberList(Integer pageNumber, Integer pageSize, String month) {
        String sql=" from mms_member where del_flag='0' and birthday like concat('____-',?,'-__') order by substr(birthday,9)";
        return DAO.paginate(pageNumber,pageSize,"select * ",sql,month);
    }

    @Override
    public MmsMember findMemberByCardNumber(String cardNumber) {
        String sql="select b.* from fina_membership_card a left join mms_member b on a.member_id=b.id " +
                "where a.del_flag='0' and a.card_number=? ";
        return DAO.findFirst(sql,cardNumber);
    }

    @Override
    public String findTelephoneByCardNumber(String cardNumber) {
        String sql="select b.telephone from fina_membership_card a left join mms_member b on a.member_id=b.id where a.del_flag=0 and a.card_number=? ";
        return Db.queryStr(sql,cardNumber);
    }

    public Page<MmsMember> findList(int pageNumber, int pageSize, MmsMember member) {

        Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
        if(member != null) {
            if (StringUtils.isNotBlank(member.getFullName())) {
                columns.like("full_name", "%" + member.getFullName().trim() + "%");
            }
            if (StringUtils.isNotBlank(member.getIdcard())) {
                columns.like("idcard", "%" + member.getIdcard() + "%");
            }
        }

        return DAO.paginateByColumns(pageNumber,pageSize,columns.getList(),"update_time DESC");
    }

    public List<MmsMember> findList(){
        return DAO.find("SELECT * FROM mms_member where del_flag=? order by create_time asc", DelFlag.NORMAL);
    }

    public boolean delMmsMember(String id,String userId) {
        MmsMember member = get(id);
        if(member == null){
            return false;
        }
        member.setDelFlag("1");
        member.setUpdateBy(userId);
        member.setUpdateTime(new Date());
        return member.update();
    }


    public String saveMmsMember(MmsMember member,String userId) {
        if(member != null){
            if(StringUtils.isNotBlank(member.getId())){
                MmsMember memberExist = get(member.getId());
                if(memberExist != null){
                    //根据身份证去重
                    if(StringUtils.isNotBlank(member.getIdcard())){
                        String sql = "SELECT * FROM mms_member WHERE del_flag = 0 AND idcard = ? AND id != ?";
                        List<MmsMember> list = DAO.find(sql,member.getIdcard(),member.getId());
                        if(list != null && list.size() >0) return "";
                    }
                    member.setUpdateBy(userId);
                    member.setUpdateTime(new Date());
                    if(member.update()){
                        return "suc";
                    }else{
                        return null;
                    }
                }
            }else{
                //根据身份证去重
                if(StringUtils.isNotBlank(member.getIdcard())){
                    String sql = "SELECT * FROM mms_member WHERE del_flag = 0 AND idcard = ?";
                    List<MmsMember> list = DAO.find(sql,member.getIdcard());
                    if(list != null && list.size() >0) return "";
                }
                member.setId(IdGen.getUUID());
                member.setDelFlag("0");
                //默认国家
                member.setCountry("1");
                member.setCreateBy(userId);
                member.setCreateTime(new Date());
                member.setUpdateBy(userId);
                member.setUpdateTime(new Date());
                if(member.save()){
                    return "suc";
                }else{
                    return null;
                }
            }
            MmsCustomerInfo customerInfo = mmsCustomerInfoService.getByIdcard(member.getIdcard());
			//同步到会员客户表
			if(customerInfo!=null) {
				
				//判断是否员工
				PersOrgEmployee emp = persOrgEmployeeService.getByEmpIdcard(member.getIdcard());
				if(emp!=null){
					customerInfo.setStaffType("staff");
				}else{
					customerInfo.setStaffType("not_staff");
				}
				customerInfo.setGender(member.getGender());
				customerInfo.setBirthday(member.getBirthday());
				customerInfo.setIdcardType(member.getIdcardType());
				customerInfo.setIdcard(member.getIdcard());
//				customerInfo.setPhoneNumber(newCard.getTelephone());
				if(StrKit.notBlank(member.getProvince())) {
					customerInfo.setProvince(member.getProvince());
				}else {
					customerInfo.setProvince(member.getIdcard().substring(0, 2)+"0000");
				}
				if(StrKit.notBlank(member.getCity())) {
					customerInfo.setCity(member.getCity());
				}else {
					customerInfo.setCity(member.getIdcard().substring(0, 4)+"00");
				}
				if(StrKit.notBlank(member.getTown())) {
					customerInfo.setTown(member.getTown());
				}else {
					customerInfo.setTown(member.getIdcard().substring(0, 6));
				}
				if(StrKit.notBlank(member.getStreet())) {
					customerInfo.setStreet(member.getStreet());
				}
				if(StrKit.notBlank(member.getAddress())) {
					customerInfo.setAddress(member.getAddress());
				}
				if(StrKit.notBlank(member.getBranchOfficeId())) {
					customerInfo.setBelongOrgId(member.getBranchOfficeId());
				}
				if(StrKit.notBlank(member.getSalesDeptId())) {
					customerInfo.setBelongDeptId(member.getSalesDeptId());
				}
				if(StrKit.notBlank(member.getSalesId())) {
					customerInfo.setBelongTo("sale");
				}else {
					customerInfo.setBelongTo("customer");
				}
				customerInfo.setUpdateTime(new Date());
				customerInfo.setUpdateBy(StrKit.notBlank(userId)?userId:member.getUpdateBy());
				customerInfo.update();
			}else {
				customerInfo = new MmsCustomerInfo();
				customerInfo.setId(IdGen.getUUID());
				customerInfo.setCustomerType("member");
				customerInfo.setMemberId(member.getId());
				//判断是否员工
				PersOrgEmployee emp = persOrgEmployeeService.getByEmpIdcard(member.getIdcard());
				if(emp!=null){
					customerInfo.setStaffType("staff");
				}else{
					customerInfo.setStaffType("not_staff");
				}
				customerInfo.setGender(member.getGender());
				customerInfo.setBirthday(member.getBirthday());
				customerInfo.setIdcardType(member.getIdcardType());
				customerInfo.setIdcard(member.getIdcard());
//				customerInfo.setPhoneNumber(newCard.getTelephone());
				if(StrKit.notBlank(member.getProvince())) {
					customerInfo.setProvince(member.getProvince());
				}else {
					customerInfo.setProvince(member.getIdcard().substring(0, 2)+"0000");
				}
				if(StrKit.notBlank(member.getCity())) {
					customerInfo.setCity(member.getCity());
				}else {
					customerInfo.setCity(member.getIdcard().substring(0, 4)+"00");
				}
				if(StrKit.notBlank(member.getTown())) {
					customerInfo.setTown(member.getTown());
				}else {
					customerInfo.setTown(member.getIdcard().substring(0, 6));
				}
				if(StrKit.notBlank(member.getStreet())) {
					customerInfo.setStreet(member.getStreet());
				}
				if(StrKit.notBlank(member.getAddress())) {
					customerInfo.setAddress(member.getAddress());
				}
				if(StrKit.notBlank(member.getBranchOfficeId())) {
					customerInfo.setBelongOrgId(member.getBranchOfficeId());
				}
				if(StrKit.notBlank(member.getSalesDeptId())) {
					customerInfo.setBelongDeptId(member.getSalesDeptId());
				}
				if(StrKit.notBlank(member.getSalesId())) {
					customerInfo.setBelongTo("sale");
				}else {
					customerInfo.setBelongTo("customer");
				}
				customerInfo.setDataSource("member");
				customerInfo.setDelFlag(DelFlag.NORMAL);
				customerInfo.setCreateTime(new Date());
				customerInfo.setCreateBy(StrKit.notBlank(userId)?userId:member.getCreateBy());
				customerInfo.setUpdateTime(new Date());
				customerInfo.setUpdateBy(StrKit.notBlank(userId)?userId:member.getUpdateBy());
				customerInfo.save();
			}
        }
        return null;
    }



    public List<Record> getCardsByMember(String userId){
        List<MmsWxUserCardRela> cardRelas = mmsWxUserCardRelaService.getListByUserId(userId);
        if(cardRelas == null || cardRelas.size() <= 0) return new ArrayList<>();

        List<Record> cardList = new ArrayList<>();
        for (MmsWxUserCardRela item : cardRelas) {
            if(StringUtils.isNotBlank(item.getCardId())) {
                Record card = finaMembershipCardService.getCardRecord(item.getCardId());
                if(card != null){
                    card.set("cardMapId",item.getId());
                    cardList.add(card);
                }
            }
        }
        if(cardList.size()>0){
            List<String> cardIds=new ArrayList<>();
            String str="";
            for (Record record : cardList) {
                cardIds.add(record.getStr("id"));
                str+="?,";
            }
            str=str.substring(0,str.length()-1);

            String sql="select a.card_id as cardId,a.binding_time as bindingTime,b.id,b.card_roll_id as cardRollId,b.roll_number as rollNumber,b.roll_value as rollValue,b.balance_roll_value as balanceRollValue " +
                    ",b.is_enable as isEnable,b.is_use as isUse,c.roll_name as rollName,c.valid_type as validType,c.valid_days as validDays,c.final_valid_date as finalValidDate, " +
                    "c.valid_start_date as validStartDate,c.valid_end_date as validEndDate,d.type_name as rollTypeName,b.expiration_time as expirationTime " +
                    "from fina_card_roll a " +
                    "inner join crm_card_roll_record b on a.roll_id=b.id " +
                    "inner join crm_card_roll c on c.id=b.card_roll_id " +
                    "inner join crm_card_roll_type d on d.id=c.roll_type_id " +
                    " where a.card_id in ("+str+") and b.del_flag='0' ";
            List<Record> recordList=Db.find(sql,cardIds.toArray());
            Map<String,List<Record>> cardRollListMap=new HashMap<>();
            Set<String> cardRollIdSet=new HashSet<>();
            for (Record record : recordList) {
                String cardId = record.getStr("cardId");
                List<Record> list=null;
                if(cardRollListMap.containsKey(cardId)){
                    list=cardRollListMap.get(cardId);
                }else{
                    list=new ArrayList<>();
                    cardRollListMap.put(cardId,list);
                }
                cardRollIdSet.add(record.getStr("cardRollId"));
                //计算过期时间
                if("0".equals(record.getStr("isEnable"))){
                    if("1".equals(record.getStr("validType"))){
                        //指定有效期范围
                        record.set("validTime", DateUtils.formatDate(record.getDate("validEndDate"),"yyyy-MM-dd")+" 23:59:59");
                    }else{
                        //绑定开始时间指定天数有效
                        if(record.getDate("bindingTime")!=null){
                            Calendar calendar=Calendar.getInstance();
                            calendar.setTime(record.getDate("bindingTime"));
                            calendar.add(Calendar.DATE, record.getInt("validDays"));

                            if(record.getDate("finalValidDate")!=null){
                                Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(record.getDate("finalValidDate"),"yyyy-MM-dd")+" 23:59:59");
                                if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
                                    record.set("validTime",DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss"));
                                }else{
                                    record.set("validTime",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
                                }
                            }else{
                                record.set("validTime",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
                            }
                        }
                    }
                }
                //计算卡券状态，1待使用，2已使用，3已过期
                if("1".equals(record.getStr("isUse"))){
                    record.set("status","2");
                }else{
                    if("0".equals(record.getStr("isEnable"))){
                        record.set("status","1");
                    }else{
                        record.set("status","3");
                    }
                }
                list.add(record);
            }
            if(cardRollIdSet.size()>0){
                str="";
                for (String typeId : cardRollIdSet) {
                    str+="?,";
                }

                str=str.substring(0,str.length()-1);
                List<Record> rollModelRelList=Db.find("select a.roll_id as rollId,b.id as modelId,a.num,b.`name` as modelName from crm_card_roll_model_rel a " +
                        "inner join wms_stock_models b on a.model_id=b.id " +
                        "where a.roll_id in ("+str+")",cardRollIdSet.toArray());
                Map<String,List<Record>> rollModelRelMap=new HashMap<>();
                for (Record record : rollModelRelList) {
                    List<Record> modelList=null;
                    if(rollModelRelMap.containsKey(record.getStr("rollId"))){
                        modelList=rollModelRelMap.get(record.getStr("rollId"));
                    }else{
                        modelList=new ArrayList<>();
                        rollModelRelMap.put(record.getStr("rollId"),modelList);
                    }
                    modelList.add(record);
                }

                for (Record record : recordList) {
                    List<Record> modelList = rollModelRelMap.get(record.getStr("cardRollId"));
                    List<Map<String,Object>> mapList=new ArrayList<>();
                    if(modelList!=null && modelList.size()>0){
                        for (Record model : modelList) {
                            mapList.add(model.getColumns());
                        }
                    }
                    record.set("good",JSONArray.parseArray(JSON.toJSONString(mapList)));
                }

                for (Record record : cardList) {
                    record.set("rollList",cardRollListMap.get(record.getStr("id")));
                }
            }
        }
        MiniProgramUtils.dealRecordNull(cardList);
        return cardList;
    }


    @Override
    public List<Record> getIntegralCardByUserId(String userId){
        String sql="select b.id as cardId,d.full_name as memberName,b.card_number as cardNumber,b.card_integrals as cardIntegrals,b.telephone  " +
                "from mms_wx_user_card_rela a inner join fina_membership_card b on a.card_id=b.id and a.user_id=? " +
                "INNER JOIN main_membership_card_type c on b.card_type_id=c.id and c.is_integral='1' " +
                "inner join mms_member d on d.id=b.member_id where b.del_flag='0' and a.del_flag='0' ";
        return Db.find(sql,userId);
    }




    @Override
    public boolean batchDelMember(List<MmsMember> list,String userId) {
        boolean flag = false;
        if(list != null && list.size() > 0){
            for (MmsMember item : list) {
                if(StringUtils.isNotBlank(item.getId())) {
                    MmsMember memberExist = get(item.getId());
                    if(memberExist != null) {
                        MmsMember member = new MmsMember();
                        member.setId(item.getId());
                        member.setDelFlag("1");
                        member.setUpdateBy(userId);
                        member.setUpdateTime(new Date());
                        flag = member.update();
                    }
                }
            }
        }
        return flag;
    }





    public MmsMember getMemberByIdcard(String idcard) {
        String sql = "select * from mms_member where del_flag = 0 and idcard = ?";
        return DAO.findFirst(sql,idcard);
    }



    public MmsMember get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }

    public MmsMember getMemberByNameAndIdcard(String cardName,String idcard){
        List<Object> param = new ArrayList<Object>();
        String sql = "SELECT * FROM mms_member WHERE del_flag = 0 AND full_name = ? AND idcard = ?";
        param.add(cardName);
        param.add(idcard);
        return DAO.findFirst(sql,param.toArray());
    }

    public MmsMember getMmsMember(String cardName,String idcard){

        if(StringUtils.isBlank(cardName) || StringUtils.isBlank(idcard)){
            return null;
        }

        List<Object> param = new ArrayList<Object>();
        String sql = "SELECT * FROM mms_member WHERE del_flag = 0 AND full_name = ? AND idcard = ?";
        param.add(cardName);
        param.add(idcard);
        MmsMember member = DAO.findFirst(sql,param.toArray());
        if(member != null){
            StringBuilder address = new StringBuilder("");
            String addresDetail = member.getAddress();
            Area province = areaService.findById(member.getProvince());
            Area city = areaService.findById(member.getCity());
            Area town = areaService.findById(member.getTown());
            Area street = areaService.findById(member.getStreet());
            if(province != null)address.append(province.getAreaName());
            if(city != null)address.append(city.getAreaName());
            if(town != null)address.append(town.getAreaName());
            if(street != null)address.append(street.getAreaName());
            if(StringUtils.isNotBlank(addresDetail))address.append(addresDetail);
            member.setAddress(address.toString());
            return member;
        }
        return null;
    }

    public static void main(String[] args) throws Exception {
	    // 设置默认工厂类
        System.setProperty("org.apache.commons.logging.LogFactory", "org.apache.commons.logging.impl.LogFactoryImpl");
        // 设置日志打印类
        LogFactory.getFactory().setAttribute("org.apache.commons.logging.Log", "org.apache.commons.logging.impl.SimpleLog");
        //设置默认日志级别
        LogFactory.getFactory().setAttribute("org.apache.commons.logging.simplelog.defaultlog", "info");
        JSONArray roomTypeJsonArray= JSON.parseArray("[{\"6F39A976-5902-4652-92B9-D61F2CEE0B4B\":\"复式\"},\n" +
                "{\"7C85B019-51DF-4041-90B5-FE68EC9AA5C5\":\"豪华大床房\"},\n" +
                "{\"92B31318-0F4C-4161-849F-DFEE2E5F9F53\":\"仓库\"},\n" +
                "{\"C92C8976-EEE0-41FA-82A4-D67B16507B44\":\"豪华双标\"}]");

        HashMap<String,String> roomTypeJsonMap=new HashMap<>();
        for (int i = 0; i < roomTypeJsonArray.size(); i++) {
            JSONObject jsonObject =roomTypeJsonArray.getJSONObject(i);
            String key=(String)jsonObject.keySet().toArray()[0];
            String value=jsonObject.getString(key);
            roomTypeJsonMap.put(value,key);
        }


        JSONArray baseRoomTypeJsonArray=JSON.parseArray("[{\"3EFE78DD-B143-4A56-B514-0C363869B09A\":\"复式\"},\n" +
                "{\"8D67DAD4-6C3B-41B5-8B4D-AC796A2D3B02\":\"豪华大床房\"},\n" +
                "{\"23CFAF8E-E42A-4209-9E02-30884025D282\":\"豪华双标\"},\n" +
                "{\"96E33898-2DDD-4D34-A064-454A7EDB95EF\":\"仓库\"}]");

        HashMap<String,String> baseRoomTypeJsonMap=new HashMap<>();
        for (int i = 0; i < baseRoomTypeJsonArray.size(); i++) {
            JSONObject jsonObject =baseRoomTypeJsonArray.getJSONObject(i);
            String key=(String)jsonObject.keySet().toArray()[0];
            String value=jsonObject.getString(key);
            baseRoomTypeJsonMap.put(value,key);
        }

        JSONArray floorJsonarr=JSON.parseArray("[{\"052AC35E-E158-44E4-9A04-634B8F75D833\":\"4\"},\n" +
                "{\"362608AC-D94D-4826-BAB1-AA19465B6A1A\":\"6\"},\n" +
                "{\"3947D7BC-2408-481B-AB6A-BAFB193E01A0\":\"3\"},\n" +
                "{\"6976A856-E56A-4ADF-AAEF-F090E22BA13D\":\"1\"},\n" +
                "{\"8DEFF41D-8509-4EC1-B1B9-DE411D675231\":\"10\"},\n" +
                "{\"9848D23A-8AD8-4FC4-9066-6D90EEDE9CB6\":\"16\"},\n" +
                "{\"9ADCCB7A-3BE4-4ECA-90D8-00311DC8BE05\":\"8\"},\n" +
                "{\"A500BB1D-8226-4B82-8971-09F2EC1DB9B6\":\"12\"},\n" +
                "{\"ADE6E5C0-FDEC-473C-AF35-0747A11F6F3C\":\"2\"},\n" +
                "{\"B0FBDD8B-13BD-48C3-A6ED-1D519EDBC4FB\":\"14\"},\n" +
                "{\"B65FF7EF-8AE3-4C0E-947C-FCA2A0279FFB\":\"9\"},\n" +
                "{\"B7D8E17A-C4F5-4A92-8075-6EF1C77EB52B\":\"17\"},\n" +
                "{\"D088B858-D981-4528-ACEF-E80D87802492\":\"15\"},\n" +
                "{\"E4933B03-D501-449B-A156-03076A6C23C4\":\"11\"},\n" +
                "{\"E4B17812-68A4-4828-9194-5ABFF768B8C8\":\"5\"},\n" +
                "{\"E6FC9538-FEBF-4543-AD0C-149AA92DF6EB\":\"13\"},\n" +
                "{\"F39AB807-B997-4A26-8FD5-C906833D3F19\":\"7\"}]");

        HashMap<String,String> floorJsonMap=new HashMap<>();
        for (int i = 0; i < floorJsonarr.size(); i++) {
            JSONObject jsonObject =floorJsonarr.getJSONObject(i);
            String key=(String)jsonObject.keySet().toArray()[0];
            String value=jsonObject.getString(key);
            floorJsonMap.put(value,key);
        }

        List<String> error=new ArrayList<>();

        InputStream inputStream=new FileInputStream("D:\\惠州森林湖-最终版(2)(2).xlsx");
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        inputStream.close();
        XSSFSheet sheet = workbook.getSheetAt(0);
        for(int i=1;i<=sheet.getLastRowNum();i++){
            String roomName=Integer.toString((int)sheet.getRow(i).getCell(2).getNumericCellValue());
            if(StrKit.notBlank(roomName)){
                String roomType=sheet.getRow(i).getCell(3).getStringCellValue();
                Integer bedNum=(int)sheet.getRow(i).getCell(8).getNumericCellValue();
                String bedTypeId="";
                Integer floorNo=(int)sheet.getRow(i).getCell(1).getNumericCellValue();

                String roomTypeId=roomTypeJsonMap.get(roomType);
                String baseRoomTypeId=baseRoomTypeJsonMap.get(roomType);
                String floorId=floorJsonMap.get(String.valueOf(floorNo));

                CookieStore cookieStore = new BasicCookieStore();
                BasicClientCookie cookie = new BasicClientCookie("auid", "478cc71c-3f59-414a-9211-bc30d101ec27");
                cookie.setDomain("main.cncsgroup.com");   // 示意domain
                //cookie.setDomain("10.10.0.12");
                cookie.setPath("/");
                cookieStore.addCookie(cookie);

                CloseableHttpClient httpClient = HttpClients.custom()
                        .setDefaultCookieStore(cookieStore)
                        .build();

                final HttpPost httpGet = new HttpPost("http://main.cncsgroup.com/main/buildingRoomManage/saveRoom");
                //final HttpPost httpGet = new HttpPost("http://10.10.0.12:8890/main/buildingRoomManage/saveRoom");
                List<NameValuePair> params=new ArrayList<>();
                params.add(new BasicNameValuePair("roomName",roomName));
                params.add(new BasicNameValuePair("roomNo",roomName));
                params.add(new BasicNameValuePair("roomType",roomTypeId));
                params.add(new BasicNameValuePair("baseRoomType",baseRoomTypeId));
                params.add(new BasicNameValuePair("peoples",String.valueOf(bedNum)));
                params.add(new BasicNameValuePair("useType","guest_room"));
                if(floorNo>9){
                    params.add(new BasicNameValuePair("lockNo","01-"+floorNo+"-"+roomName));
                }else{
                    params.add(new BasicNameValuePair("lockNo","01-0"+floorNo+"-"+roomName));
                }
                params.add(new BasicNameValuePair("lockType","0"));
                params.add(new BasicNameValuePair("isEnable","0"));
                params.add(new BasicNameValuePair("isBooking","1"));
                params.add(new BasicNameValuePair("allowLock","1"));
                params.add(new BasicNameValuePair("isOutBookabled","0"));
                params.add(new BasicNameValuePair("bookChannel","0459A691-93A7-43C6-B850-E66AC25B7C7C,193271DB-267F-4651-B926-5C59BA3ADEBE,2FDA94E8-1185-48E5-848A-E7E2E89D6FB7,49289458-AB5D-477D-AFA2-281226D894E4"));
                params.add(new BasicNameValuePair("buildingId","894AD73C-BAA3-43D4-9659-AA2D55297930"));
                params.add(new BasicNameValuePair("floorId",floorId));
                params.add(new BasicNameValuePair("maintainState","Normal"));

                httpGet.setEntity(new UrlEncodedFormEntity(params));

                CloseableHttpResponse response = httpClient.execute(httpGet);
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode > 299 || statusCode < 200) {
                    throw new IOException("statusCode error : " + statusCode);
                }
                HttpEntity entity = response.getEntity();
                String responseStr = IOUtils.toString(entity.getContent());
                EntityUtils.consume(entity);
                response.close();

                System.out.println("房间"+responseStr);
                JSONObject res=JSON.parseObject(responseStr);
                if("ok".equalsIgnoreCase(res.getString("state"))){
                    String roomId=res.getString("data");
                    for (int integer = 0; integer < bedNum; integer++) {
                        final HttpPost httpPost = new HttpPost("http://main.cncsgroup.com/main/buildingRoomManage/saveBed");
                        //final HttpPost httpPost = new HttpPost("http://10.10.0.12:8890/main/buildingRoomManage/saveBed");
                        List<NameValuePair> params2=new ArrayList<>();
                        params2.add(new BasicNameValuePair("bedName",roomName+"-"+(integer+1)));
                        params2.add(new BasicNameValuePair("bedNo",roomName+"-"+(integer+1)));
                        params2.add(new BasicNameValuePair("bedTypeId","676450A3-D51E-4442-BCB1-2F86B6287BD2"));
                        params2.add(new BasicNameValuePair("bedStatusId","3E541B4D-2BA2-4993-90EE-86C39353ACF9"));
                        params2.add(new BasicNameValuePair("markupTypeId","8C6D8370-48DB-4A39-9AD0-1A1BB6977A69"));
                        params2.add(new BasicNameValuePair("isEnable","0"));
                        params2.add(new BasicNameValuePair("bookChannel","0459A691-93A7-43C6-B850-E66AC25B7C7C,193271DB-267F-4651-B926-5C59BA3ADEBE,2FDA94E8-1185-48E5-848A-E7E2E89D6FB7,49289458-AB5D-477D-AFA2-281226D894E4"));
                        params2.add(new BasicNameValuePair("mattressTypeId","80030260-A06F-44B6-9BDA-513777106767"));
                        params2.add(new BasicNameValuePair("roomId",roomId));

                        httpPost.setEntity(new UrlEncodedFormEntity(params2));

                        CloseableHttpResponse response2 = httpClient.execute(httpPost);
                        int statusCode2 = response2.getStatusLine().getStatusCode();
                        if (statusCode2 > 299 || statusCode2 < 200) {
                            throw new IOException("statusCode error : " + statusCode);
                        }
                        HttpEntity entity2 = response2.getEntity();
                        String responseStr2 = IOUtils.toString(entity2.getContent());
                        System.out.println("床位"+responseStr2);
                        EntityUtils.consume(entity2);
                        response2.close();
                    }
                }else{
                    error.add(roomName);
                }
                httpClient.close();
                Thread.sleep(1000);
            }
        }
        System.out.println(JSON.toJSONString(error));
    }

}