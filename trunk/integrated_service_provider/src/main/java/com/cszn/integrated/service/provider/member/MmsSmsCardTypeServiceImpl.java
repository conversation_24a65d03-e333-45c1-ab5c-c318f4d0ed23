package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsSmsCardTypeService;
import com.cszn.integrated.service.entity.member.MmsSmsCardType;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.ArrayList;
import java.util.List;

@Bean
public class MmsSmsCardTypeServiceImpl extends JbootServiceBase<MmsSmsCardType> implements MmsSmsCardTypeService {

    @Override
    public Page<Record> tablePage(int pageNumber, int pageSize, String smsId) {
        String sql="from mms_sms_card_type a left join main_membership_card_type b on a.card_type_id=b.id where a.sms_id=? ";

        return Db.paginate(pageNumber,pageSize,"select a.id,b.card_type as cardTypeName ",sql,smsId);
    }

    @Override
    public Page<Record> cardTypeTablePage(int pageNumber, int pageSize, String smsId,String typeClassify,String cardTypeName) {
        String select="select a.card_type as cardTypeName,a.id ";
        String sql=" from main_membership_card_type a " +
                "left join mms_sms_card_type b on b.sms_id=? and a.id=b.card_type_id " +
                "where b.id is null and a.del_flag='0' ";
        List<Object> param=new ArrayList<>();
        param.add(smsId);
        if(StrKit.notBlank(typeClassify)){
            sql+=" and a.type_classify=?";
            param.add(typeClassify);
        }
        if(StrKit.notBlank(cardTypeName)){
        	sql+=" and a.card_type like concat('%',?,'%')";
        	param.add(cardTypeName);
        }
        return Db.paginate(pageNumber,pageSize,select,sql,param.toArray());
    }
}
