package com.cszn.integrated.service.provider.main;

import java.util.List;

import com.cszn.integrated.service.api.main.MainTouristDaysDetailService;
import com.cszn.integrated.service.entity.main.MainTouristDaysDetail;
import com.cszn.integrated.service.entity.status.DelFlag;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MainTouristDaysDetailServiceImpl extends JbootServiceBase<MainTouristDaysDetail> implements MainTouristDaysDetailService {

	public List<MainTouristDaysDetail> findList(String daysId) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		return DAO.findListByColumns(columns, "create_date asc");
	}
    
}