package com.cszn.integrated.service.provider.member;

import java.util.Date;
import java.util.List;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsMemberLinkService;
import com.cszn.integrated.service.entity.member.MmsMemberLink;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsMemberLinkServiceImpl extends JbootServiceBase<MmsMemberLink> implements MmsMemberLinkService {

	public List<MmsMemberLink> findList(String memberId) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(memberId)){
			columns.eq("member_id", memberId);
		}
		return DAO.findListByColumns(columns, "create_date desc");
	}

	public boolean saveMemberLink(MmsMemberLink model) {
		 boolean flag=false;
	        if(StrKit.isBlank(model.getId())){
	        	model.setId(IdGen.getUUID());
	        	model.setDelFlag(DelFlag.NORMAL);
	        	model.setCreateDate(new Date());
	        	model.setUpdateDate(new Date());
	            flag=model.save();
	        }else{
	        	model.setUpdateDate(new Date());
	            flag=model.update();
	        }
	        return flag;
	}
    
}