package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsFaceRecognitionRecordService;
import com.cszn.integrated.service.entity.member.MmsFaceRecognitionRecord;
import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/25
 **/
@Bean
public class MmsFaceRecognitionRecordServiceImpl extends JbootServiceBase<MmsFaceRecognitionRecord> implements MmsFaceRecognitionRecordService {


    @Override
    public Page<Record> findList(Integer pageNumber, Integer pageSize, String fullName, String idcard) {
        List<Object> param = Lists.newArrayList();
        String sqlSelect = "SELECT mf.id, mf.app_no AS appNo, mf.member_id AS memberId, mf.device_key AS deviceKey,mm.full_name AS fullName, mm.idcard AS idcard, mf.type, mf.time," +
                "mf.device_type as deviceType,mf.action_type as actionType,mb.base_name as baseName ";
        String sql = "FROM mms_face_recognition_record mf LEFT JOIN mms_member mm ON mf.member_id = mm.id " +
                "left join main_base mb on mf.base_id = mb.id " +
                "WHERE mm.del_flag = 0 ";
        if(StringUtils.isNotBlank(fullName)){
            sql += "AND mm.full_name LIKE CONCAT('%',?,'%') ";
            param.add(fullName);
        }
        if(StringUtils.isNotBlank(idcard)){
            sql += "AND mm.idcard LIKE CONCAT('%',?,'%') ";
            param.add(idcard);
        }
        sql += "ORDER BY mf.time DESC";
        return Db.paginate(pageNumber,pageSize,sqlSelect,sql,param.toArray());
    }





    @Override
    public MmsFaceRecognitionRecord get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }


}
