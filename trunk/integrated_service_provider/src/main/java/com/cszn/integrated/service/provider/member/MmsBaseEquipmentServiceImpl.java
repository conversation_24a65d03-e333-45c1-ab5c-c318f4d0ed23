package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsBaseEquipmentService;
import com.cszn.integrated.service.entity.member.MmsBaseEquipment;
import com.cszn.integrated.service.entity.member.MmsCardEquipment;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/27
 **/
@Bean
public class MmsBaseEquipmentServiceImpl extends JbootServiceBase<MmsBaseEquipment> implements MmsBaseEquipmentService {


    @Override
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize, MmsBaseEquipment baseEquipment) {
        List<Object> params = Lists.newArrayList();
        String sql = "FROM mms_base_equipment be LEFT JOIN main_base b ON be.base_id = b.id " +
                "LEFT JOIN main_base_building d ON be.building_id = d.id LEFT JOIN main_base_floor f ON be.floor_id = f.id " +
                "LEFT JOIN mms_equipment_type et ON be.equipment_type_id = et.id " +
                "WHERE be.del_flag = 0 ";
        if(baseEquipment != null){
            if(StringUtils.isNotBlank(baseEquipment.getEquipmentNo())){
                sql += " AND be.equipment_no LIKE CONCAT('%',?,'%') ";
                params.add(baseEquipment.getEquipmentNo());
            }
            if(StringUtils.isNotBlank(baseEquipment.getBaseId())){
                sql += " AND be.base_id LIKE CONCAT('%',?,'%') ";
                params.add(baseEquipment.getBaseId());
            }
        }

        sql += "ORDER BY be.update_date DESC ";
        return Db.paginate(pageNumber,pageSize,"SELECT be.id, et.equipment_type AS equipmentType, be.equipment_no AS equipmentNo, be.equipment_type_id AS equipmentTypeId,be.base_id AS baseId, " +
                "b.base_name AS baseName,d.building_name AS buildingName,f.floor_name AS floorName, be.building_id AS buildingId, be.floor_id AS floorId," +
                "be.part_type AS partType, be.position, be.put_way AS putWay,be.del_flag AS delFlag, be.create_by AS createBy, be.create_date AS createDate, be.update_by AS updateBy," +
                "be.update_date AS updateDate ",sql,params.toArray());
    }



    @Override
    public String saveBaseEquipment(MmsBaseEquipment baseEquipment, String userId) {
        //if(getDistinct(baseEquipment) == null)return "";
        if(StringUtils.isBlank(baseEquipment.getId())){
            baseEquipment.setId(IdGen.getUUID());
            baseEquipment.setDelFlag("0");
            baseEquipment.setCreateBy(userId);
            baseEquipment.setCreateDate(new Date());
            baseEquipment.setUpdateBy(userId);
            baseEquipment.setUpdateDate(new Date());
            if(baseEquipment.save()){
                return "suc";
            }else{
                return null;
            }
        }else{
            baseEquipment.setUpdateBy(userId);
            baseEquipment.setUpdateDate(new Date());
            if(baseEquipment.update()){
                return "suc";
            }else{
                return null;
            }
        }
    }



    @Override
    public boolean delBaseEquipment(String id, String userId) {
        MmsBaseEquipment baseEquipment =  new MmsBaseEquipment();
        baseEquipment.setId(id);
        baseEquipment.setDelFlag("1");
        baseEquipment.setUpdateBy(userId);
        baseEquipment.setUpdateDate(new Date());
        return baseEquipment.update();
    }



    @Override
    public String getDistinct(MmsBaseEquipment baseEquipment) {
        List<Object> params = Lists.newArrayList();
        params.add(baseEquipment.getBaseId());
        params.add(baseEquipment.getBuildingId());
        params.add(baseEquipment.getFloorId());
        params.add(baseEquipment.getEquipmentTypeId());
        List<MmsBaseEquipment> list = null;
        if(StringUtils.isBlank(baseEquipment.getId())){
            list = DAO.find("SELECT * FROM mms_base_equipment WHERE del_flag = 0 AND base_id = ? AND building_id = ? AND floor_id = ? AND equipment_type_id = ?",params.toArray());
        }else{
            params.add(baseEquipment.getId());
            list = DAO.find("SELECT * FROM mms_base_equipment WHERE del_flag = 0 AND base_id = ? AND building_id = ? AND floor_id = ? AND equipment_type_id = ? AND id != ?",params.toArray());
        }
        if((list != null && list.size() >0 ))return null;
        return "";
    }



    @Override
    public MmsBaseEquipment get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }
}
