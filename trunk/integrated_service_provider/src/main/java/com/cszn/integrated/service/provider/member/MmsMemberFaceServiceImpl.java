package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.FaceUtils;
import com.cszn.integrated.base.utils.FileUploadKit;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.service.api.main.MainFunctionSwitchService;
import com.cszn.integrated.service.api.member.MmsMemberFaceService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.entity.enums.FunctionSwitch;
import com.cszn.integrated.service.entity.enums.SwitchType;
import com.cszn.integrated.service.entity.member.MmsMemberFace;
import com.cszn.integrated.service.entity.status.Global;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/13
 **/
@Bean
public class MmsMemberFaceServiceImpl extends JbootServiceBase<MmsMemberFace> implements MmsMemberFaceService {

    @Inject
    MmsMemberService mmsMemberService;

    @Inject
    private MainFunctionSwitchService mainFunctionSwitchService;


    @Override
    public boolean uploadFace(String memberId,String image){
        //boolean bo = FileUploadKit.isPompress(image);
        String swicthStr = mainFunctionSwitchService.getSwitch(FunctionSwitch.faceCompliance.getKey());
        image = FileUploadKit.resizeImageTo800(image);
        MmsMemberFace face=new MmsMemberFace();
        face.setId(IdGen.getUUID());
        face.setMemberId(memberId);
        face.setPicture(image.getBytes());
        face.setVerifyFlag("0");
        face.setCollectTime(new Date());
        //region Description
        /*if(SwitchType.open.getKey().equals(swicthStr)) {
            Map<String, Object> map = FaceUtils.faceCompliance(Global.faceComplianceUrl, image,null);
            if (map.containsKey("error")) {
                face.setVerifyFlag("0");
            } else if (map.containsKey("flag")) {
                if ((Boolean) map.get("flag") == false) {
                    face.setVerifyFlag("2");
                } else {
                    face.setVerifyFlag("1");
                }
                face.setVerifyDescribe((String) map.get("msg"));
            }
        }else{
            face.setVerifyFlag("0");
        }*/
        //endregion
        return face.save();
    }

    @Override
    public Record getFace(String idcard){
        String sql="select f.id,f.member_id AS memberId,f.picture AS picture,f.verify_flag AS verifyFlag,f.verify_describe AS verifyDescribe,f.collect_time AS collectTime " +
                "from mms_member_face f left join mms_member m on f.member_id=m.id where m.del_flag = 0 AND m.idcard=? order by f.collect_time desc";
        Record record=Db.findFirst(sql,idcard);
        if(record != null) {
            if (record.getBytes("picture") != null) {
                record.set("picture", new String(record.getBytes("picture")));
            }
            if (StringUtils.isBlank(record.getStr("verifyDescribe"))) {
                record.set("verifyDescribe", "");
            }
        }
        return record;
    }

    @Override
    public Page<Record> findList(Integer pageNumber, Integer pageSize, String fullName, String idcard) {

        List<Object> param = Lists.newArrayList();
        String selectSql = "SELECT mf.id, mf.member_id AS memberId, mm.full_name AS fullName, mm.idcard AS idcard," +
                "mf.verify_flag AS verifyFlag, mf.verify_describe AS verifyDescribe, mf.collect_time AS collectTime ";
        String sql = "FROM mms_member_face mf LEFT JOIN mms_member mm ON mf.member_id = mm.id WHERE 1=1 ";
        if(StringUtils.isNotBlank(fullName)){
            sql += "AND mm.full_name LIKE CONCAT('%',?,'%') ";
            param.add(fullName);
        }
        if(StringUtils.isNotBlank(idcard)){
            sql += "AND mm.idcard LIKE CONCAT('%',?,'%') ";
            param.add(idcard);
        }
        sql += "ORDER BY mf.collect_time DESC";
        return Db.paginate(pageNumber,pageSize,selectSql,sql,param.toArray());
    }



    @Override
    public List<Record> findList(String fullName, String idcard) {
        List<Object> param = Lists.newArrayList();
        String sqlSelect = "select m.id,f.id as faceId,m.full_name as fullName,m.idcard,f.verify_flag as verifyFlag,f.verify_describe as verifyDescribe,f.collect_time as collectTime ";
        String sql = "from mms_member m left join mms_member_face f on m.id = f.member_id where m.del_flag = 0 ";
        if(StringUtils.isNotBlank(fullName)){
            sql += "AND m.full_name = ? ";
            param.add(fullName);
        }
        if(StringUtils.isNotBlank(idcard)){
            sql += "AND m.idcard = ? ";
            param.add(idcard);
        }
        sql += "ORDER BY m.create_time DESC limit 1";
        return Db.find(sqlSelect+sql,param.toArray());
    }


    @Override
    public List<MmsMemberFace> getListByVerifyFlag(String verifyFlag) {
        return DAO.find("select * from mms_member_face where verify_flag = ?",verifyFlag);
    }



    @Override
    public boolean generateMemberFace(String memberId, String imgUrl) {
        if(StringUtils.isNotBlank(imgUrl)){
            String base64Img = FileUploadKit.getImgStr(imgUrl);
            MmsMemberFace face = new MmsMemberFace();
            face.setMemberId(memberId);
            return saveMemberFace(face,base64Img);
        }
        return false;
    }


    @Override
    public boolean saveMemberFace(MmsMemberFace face, String picturePath) {
        String swicthStr = mainFunctionSwitchService.getSwitch(FunctionSwitch.faceCompliance.getKey());
        //String base64Picture = FileUploadKit.getImgStr(picturePath);
        //boolean bo = FileUploadKit.isPompress(picturePath);
        picturePath = FileUploadKit.resizeImageTo800(picturePath);
        face.setId(IdGen.getUUID());
        face.setPicture(picturePath.getBytes());
        face.setCollectTime(new Date());
        if(SwitchType.open.getKey().equals(swicthStr)) {
            Map<String, Object> map = FaceUtils.faceCompliance(Global.faceComplianceUrl, picturePath,null);
            if (map.containsKey("error")) {
                face.setVerifyFlag("0");
            } else if (map.containsKey("flag")) {
                if ((Boolean) map.get("flag") == false) {
                    face.setVerifyFlag("2");
                } else {
                    face.setVerifyFlag("1");
                }
                face.setVerifyDescribe((String) map.get("msg"));
            }
        }else{
            face.setVerifyFlag("0");
        }
        return face.save();
    }



    @Override
    public Record getTempFaceByIdCard(String idcard) {
        Record record = Db.findFirst("select f.id as faceId,f.member_id as memberId,f.picture as imgBase64,f.verify_flag as verifyFlag," +
                "f.verify_describe as verifyDescribe,f.collect_time as collectTime," +
                "m.full_name as name,m.gender,m.surname,m.idcard from mms_member_face f " +
                "left join mms_member m on f.member_id = m.id " +
                "where m.del_flag = 0 and f.verify_flag = 1 and m.idcard = ?",idcard);

        if(record != null){
            if(StringUtils.isBlank(record.getStr("surname"))){
                if(StringUtils.isNotBlank(record.getStr("name"))){
                    String name = record.getStr("name");
                    if(name.length() == 3 || name.length() == 2){
                        record.set("surname",name.substring(0,1));
                    }else if(name.length() == 4){
                        record.set("surname",name.substring(0,2));
                    }
                }else{
                    record.set("surname","");
                }
            }
            //处理年龄
            if(StringUtils.isNotBlank(record.getStr("idcard"))){
                record.set("age", TimeUtils.getAgeByIdcard(record.getStr("idcard")));
            }
            //处理base64
            if (record.getBytes("imgBase64") == null) {
                record.set("imgBase64", "");
            } else {
                record.set("imgBase64", new String(record.getBytes("imgBase64")));
            }
        }
        return record;
    }


    @Override
    public MmsMemberFace get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }
}
