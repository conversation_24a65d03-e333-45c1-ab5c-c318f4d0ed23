package com.cszn.integrated.service.provider.main;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.interceptor.SyncInterceptor;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.main.MainCsamAssetBrandService;
import com.cszn.integrated.service.api.main.MainCsamAssetTypeFieldService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainCsamAssetBrand;
import com.cszn.integrated.service.entity.main.MainCsamAssetTypeField;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MainCsamAssetTypeFieldServiceImpl extends JbootServiceBase<MainCsamAssetTypeField> implements MainCsamAssetTypeFieldService {

    @Inject
    private MainSyncRecordService mainSyncRecordService;

    @Override
    @Before(SyncInterceptor.class)
    public boolean assetTypeFieldSave(MainCsamAssetTypeField field, String userId) {
        boolean flag=false;
        if(StrKit.isBlank(field.getId())){
            field.setId(IdGen.getUUID());
            field.setCreateBy(userId);
            field.setCreateDate(new Date());
            field.setUpdateBy(userId);
            field.setUpdateDate(new Date());
            flag=field.save();
            if(flag){
                flag=mainSyncRecordService.saveSyncRecord(SyncType.csamAssetTypeField.getKey(), SyncDataType.INSERT, JSON.toJSONString(field),userId);
            }
        }else{
            field.setUpdateBy(userId);
            field.setUpdateDate(new Date());
            flag=field.update();
            if(flag){
                flag=mainSyncRecordService.saveSyncRecord(SyncType.csamAssetTypeField.getKey(), SyncDataType.UPDATE, JSON.toJSONString(field),userId);
            }
        }

        return flag;
    }

    @Override
    public Page<MainCsamAssetTypeField> assetTypeFieldPageList(int pageNumber, int pageSize, MainCsamAssetTypeField field) {
        String sql=" from main_csam_asset_type_field where 1=1 ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(field.getFieldName())){
            sql+=" and field_name like concat('%',?,'%') ";
            params.add(field.getFieldName());
        }

        if(StrKit.notBlank(field.getAssetTypeId())){
            sql+=" and asset_type_id=? ";
            params.add(field.getAssetTypeId());
        }

        return DAO.paginate(pageNumber,pageSize,"select * ",sql,params.toArray());
    }
}
