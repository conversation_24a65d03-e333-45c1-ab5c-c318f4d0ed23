package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsQuestionnaireQuestionService;
import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestion;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MmsQuestionnaireQuestionServiceImpl extends JbootServiceBase<MmsQuestionnaireQuestion> implements MmsQuestionnaireQuestionService {

    @Override
    public List<MmsQuestionnaireQuestion> findListByQuestionnaireId(String questionnaireId){
        String questionSql="select id,title,is_required,type from mms_questionnaire_question where del_flag='0' and questionnaire_id=? order by sort ";
        return DAO.find(questionSql,questionnaireId);
    }

    @Override
    public Page<MmsQuestionnaireQuestion> pageList(int pageNumber,int pageSize,MmsQuestionnaireQuestion question){
        String sql=" from mms_questionnaire_question where del_flag='0'  ";
        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(question.getQuestionnaireId())){
            sql+=" and questionnaire_id=? ";
            params.add(question.getQuestionnaireId());
        }
        if(StrKit.notBlank(question.getTitle())){
            sql+=" and title like concat('%',?,'%') ";
            params.add(question.getTitle());
        }
        sql+=" order by sort ";
        return DAO.paginate(pageNumber,pageSize,"select * ",sql,params.toArray());
    }


    @Override
    public boolean saveQuestion(MmsQuestionnaireQuestion question,String userId){
        boolean flag=false;
        if(StrKit.isBlank(question.getId())){
            question.setId(IdGen.getUUID());
            question.setDelFlag("0");
            question.setCreateBy(userId);
            question.setCreateDate(new Date());
            question.setUpdateBy(userId);
            question.setUpdateDate(new Date());
            flag=question.save();
        }else{
            question.setUpdateBy(userId);
            question.setUpdateDate(new Date());
            flag=question.update();
        }
        return flag;
    }
}
