package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsActivityMemberService;
import com.cszn.integrated.service.entity.member.MmsActivityMember;
import com.cszn.integrated.service.entity.member.MmsActivityQrcode;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/7/8
 **/
@Bean
public class MmsActivityMemberServiceImpl extends JbootServiceBase<MmsActivityMember> implements MmsActivityMemberService {


    @Override
    public Page<Record> findList(Integer pageNumber, Integer pageSize, MmsActivityMember member) {
        List<Object> params = new ArrayList<>();
        String sql = "from mms_activity_member m left join mms_activity_qrcode q on m.qrcode = q.qrcode " +
                "left join sys_user u on q.user_id = u.id left join main_branch_office_user b on u.id = b.user_id " +
                "left join main_branch_office o on b.branch_office_id = o.id ";

        if(StringUtils.isNotBlank(member.getName()) ||StringUtils.isNotBlank(member.getTelephone())) sql += "where ";

        if(StringUtils.isNotBlank(member.getName())){
            sql += "m.name like concat('%',?,'%') ";
            params.add(member.getName());
        }
        if(StringUtils.isNotBlank(member.getName()) && StringUtils.isNotBlank(member.getTelephone())) sql += "and ";

        if(StringUtils.isNotBlank(member.getTelephone())){
            sql += "m.telephone like concat('%',?,'%') ";
            params.add(member.getTelephone());
        }
        sql += "order by m.create_time desc ";
        return Db.paginate(pageNumber,pageSize,"select m.id,m.name,m.telephone,m.qrcode,u.name as marketName,o.full_name as fullName,m.prize_flag as prizeFlag," +
                "m.prize_time as prizeTime,m.remark,m.create_time as createTime ",sql,params.toArray());
    }



    @Override
    public MmsActivityMember get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }



    @Override
    public boolean delActivityMember(String id, String userId) {
        MmsActivityMember activityMember = get(id);
        if(activityMember == null){
            return false;
        }
        return activityMember.delete();
    }



    @Override
    public List<MmsActivityMember> getActByPhone(String telephone) {
        return DAO.find("select * from mms_activity_member where telephone = ?",telephone);
    }
}
