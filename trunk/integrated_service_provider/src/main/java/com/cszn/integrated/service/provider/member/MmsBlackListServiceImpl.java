package com.cszn.integrated.service.provider.member;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsBlackListService;
import com.cszn.integrated.service.entity.member.MmsBlackList;
import com.cszn.integrated.service.entity.member.MmsBlackRecord;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsBlackListServiceImpl extends JbootServiceBase<MmsBlackList> implements MmsBlackListService {

//	@Inject
//    private MmsBlackRecordService mmsBlackRecordService;

	@Override
	public Page<MmsBlackList> pageTable(int pageNumber, int pageSize, MmsBlackList model) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if (StrKit.notBlank(model.getFullName())) {
			columns.like("full_name", "%" + model.getFullName() + "%");
		}
		if (StrKit.notBlank(model.getIdcard())) {
			columns.like("idcard", "%" + model.getIdcard() + "%");
		}
		if (StrKit.notBlank(model.getTelephone())) {
			columns.like("telephone", "%" + model.getTelephone() + "%");
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "create_time desc");
	}

	public MmsBlackList getByIdCard(String idCard) {
		return DAO.findFirst("select * from mms_black_list where del_flag='0' and is_enable='0' and idcard=?", idCard);
	}

	public MmsBlackList getByFullName(String fullName) {
		return DAO.findFirst("select * from mms_black_list where del_flag='0' and is_enable='0' and full_name=?", fullName);
	}

	@Override
	public MmsBlackList getByCondition(String fullName, String idCard, String mobile) {
		return DAO.findFirst("select * from mms_black_list where full_name=? and idcard=? and telephone=?", fullName, idCard, mobile);
	}
	
    @Override
    public boolean saveBlackList(MmsBlackList blackList){
        boolean flag=false;
        MmsBlackRecord mmsBlackRecord = new MmsBlackRecord();
        if(StrKit.isBlank(blackList.getId())){
        	blackList.setId(IdGen.getUUID());
        	blackList.setDelFlag("0");
        	if(blackList.getCreateTime()==null) {
        		blackList.setCreateTime(new Date());
        	}
            flag = blackList.save();
            mmsBlackRecord.setDataType("新增");
            mmsBlackRecord.setCreateBy(blackList.getCreateBy());
            mmsBlackRecord.setCreateTime(blackList.getCreateTime());
        }else{
        	if(blackList.getUpdateTime()==null) {
        		blackList.setUpdateTime(new Date());
        	}
            flag = blackList.update();
            if("1".equals(blackList.getDelFlag())) {
            	mmsBlackRecord.setDataType("删除");
            }else {
            	mmsBlackRecord.setDataType("修改");
            }
            mmsBlackRecord.setCreateBy(blackList.getUpdateBy());
            mmsBlackRecord.setCreateTime(blackList.getUpdateTime());
            mmsBlackRecord.setUpdateBy(blackList.getUpdateBy());
            mmsBlackRecord.setUpdateTime(blackList.getUpdateTime());
        }
        mmsBlackRecord.setBlackListId(blackList.getId());
        mmsBlackRecord.setSyncData(JSONObject.toJSONString(blackList));
        if(StringUtils.isNotBlank(blackList.getIsEnable())) {
        	mmsBlackRecord.setIsEnable(blackList.getIsEnable());
        }
        if(StringUtils.isNotBlank(blackList.getRemarks())) {
        	mmsBlackRecord.setRemarks(blackList.getRemarks());
        }
        mmsBlackRecord.setDelFlag("0");
        flag = mmsBlackRecord.save();
        return flag;
    }
}
