package com.cszn.integrated.service.provider.designPattern.templateMethod;

import com.cszn.integrated.service.entity.crm.CrmCardRollBase;
import com.cszn.integrated.service.entity.crm.CrmCardRollBranchOffice;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecord;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecordUse;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public abstract class CouponAbstract {

    //卡券id
    private String id;
    //卡券号码
    private String number;
    //是否已使用
    private String isUse;
    //是否有效，1无效，0有效
    private String isEnable;
    //最大可用时间
    private Date maxUseTime;
    //是否可多次使用；ONE一次;MANY多次
    private String useTimes;
    //是否绑定才能使用
    private String isBingUse;
    //所属会员卡id
    private String cardId;

    private CrmCardRollRecord crmCardRollRecord;

    private List<CrmCardRollRecordUse> crmCardRollRecordUseList;
    //可用的分公司
    private List<CrmCardRollBranchOffice> usableBranchOfficeList;
    //可用的基地
    private List<CrmCardRollBase> usableBaseList;


    /**
     * 校验会员卡是否需要绑定
     * @return
     */
    private boolean checkBinding(){
        if("0".equals(isBingUse)){
            return true;
        }
        String bindingCardId = Db.queryStr("select card_id from fina_card_roll where roll_id=? ", this.id);
        if(StrKit.notBlank(bindingCardId)){
            cardId=bindingCardId;
            return true;
        }
        return false;
    }


    private Map<String,Object> checkCoupon(){
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        if("1".equals(this.isUse)){
            resultMap.put("msg", "该券已被使用，请勿重复操作");
            return resultMap;
        }
        if("1".equals(this.isEnable)){
            resultMap.put("msg", "该券已为失效状态");
            return resultMap;
        }else{
            if(maxUseTime.getTime()<new Date().getTime()){
                resultMap.put("msg", "该券已过最大可用时间");
                return resultMap;
            }
        }
        if(!checkBinding()) {
            resultMap.put("msg", "该券需要绑定会员卡才能使用");
            return resultMap;
        }
        resultMap.put("flag",true);
        return resultMap;
    }


    public Map<String,Object> use(){
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("flag",false);
        Map<String, Object> checkMap = this.checkCoupon();
        if(!(boolean)checkMap.get("flag")){
            resultMap.put("msg",checkMap.get("msg"));
            return resultMap;
        }

        this.childUse();
        return resultMap;
    }

    protected Map<String,Object> childUse(){
        this.getCrmCardRollRecord().setIsUse("1");


        return null;
    }

    //卡券恢复
    abstract Map<String,Object> restore();


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getIsUse() {
        return isUse;
    }

    public void setIsUse(String isUse) {
        this.isUse = isUse;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public Date getMaxUseTime() {
        return maxUseTime;
    }

    public void setMaxUseTime(Date maxUseTime) {
        this.maxUseTime = maxUseTime;
    }

    public String getUseTimes() {
        return useTimes;
    }

    public void setUseTimes(String useTimes) {
        this.useTimes = useTimes;
    }

    public String getIsBingUse() {
        return isBingUse;
    }

    public void setIsBingUse(String isBingUse) {
        this.isBingUse = isBingUse;
    }

    public List<CrmCardRollBranchOffice> getUsableBranchOfficeList() {
        return usableBranchOfficeList;
    }

    public void setUsableBranchOfficeList(List<CrmCardRollBranchOffice> usableBranchOfficeList) {
        this.usableBranchOfficeList = usableBranchOfficeList;
    }

    public List<CrmCardRollBase> getUsableBaseList() {
        return usableBaseList;
    }

    public void setUsableBaseList(List<CrmCardRollBase> usableBaseList) {
        this.usableBaseList = usableBaseList;
    }

    public String getCardId() {
        return cardId;
    }

    public void setCardId(String cardId) {
        this.cardId = cardId;
    }

    public CrmCardRollRecord getCrmCardRollRecord() {
        return crmCardRollRecord;
    }

    public void setCrmCardRollRecord(CrmCardRollRecord crmCardRollRecord) {
        this.crmCardRollRecord = crmCardRollRecord;
    }

    public List<CrmCardRollRecordUse> getCrmCardRollRecordUseList() {
        return crmCardRollRecordUseList;
    }

    public void setCrmCardRollRecordUseList(List<CrmCardRollRecordUse> crmCardRollRecordUseList) {
        this.crmCardRollRecordUseList = crmCardRollRecordUseList;
    }
}
