package com.cszn.integrated.service.provider.main;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.interceptor.SyncInterceptor;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.main.MainCsamAssetTypeUserService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainCsamAssetTypeUser;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.List;

@Bean
public class MainCsamAssetTypeUserServiceImpl extends JbootServiceBase<MainCsamAssetTypeUser> implements MainCsamAssetTypeUserService {

    @Inject
    private MainSyncRecordService mainSyncRecordService;

    @Before(SyncInterceptor.class)
    @Override
    public boolean saveMainCsamAssetTypeUser(MainCsamAssetTypeUser typeUser,String userId){
        boolean flag=false;
        if(StrKit.isBlank(typeUser.getId())){
            typeUser.setId(IdGen.getUUID());
            flag=typeUser.save();
            if(flag){
                flag=mainSyncRecordService.saveSyncRecord(SyncType.mainCsamAssetTypeUser.getKey(), SyncDataType.INSERT, JSON.toJSONString(typeUser),userId);
            }
        }else{
            flag=typeUser.update();
            if(flag){
                flag=mainSyncRecordService.saveSyncRecord(SyncType.mainCsamAssetTypeUser.getKey(), SyncDataType.UPDATE, JSON.toJSONString(typeUser),userId);
            }
        }
        return flag;
    }

    @Before(SyncInterceptor.class)
    @Override
    public boolean delMainCsamAssetTypeUser(String id,String userId){
        boolean flag=false;
        if(StrKit.notBlank(id)){
            flag=DAO.deleteById(id);
            if(flag){
                flag=mainSyncRecordService.saveSyncRecord(SyncType.mainCsamAssetTypeUser.getKey(),SyncDataType.DELETE,"[\""+id+"\"]",userId);
            }
        }
        return flag;
    }

    @Override
    public List<MainCsamAssetTypeUser> findTypeUserList(String typeId) {
        return DAO.find("select * from main_csam_asset_type_user where type_id=?",typeId);
    }
}
