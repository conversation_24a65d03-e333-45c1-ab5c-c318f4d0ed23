package com.cszn.integrated.service.provider.member;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.entity.status.Global;
import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.service.api.member.MmsCustomerVisitService;
import com.cszn.integrated.service.entity.member.MmsCustomerVisit;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.google.common.collect.Lists;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Bean
public class MmsCustomerVisitServiceImpl extends JbootServiceBase<MmsCustomerVisit> implements MmsCustomerVisitService {

	private static Logger logger = LoggerFactory.getLogger(MmsCustomerVisitServiceImpl.class);

	public Page<MmsCustomerVisit> paginateByCondition(String startDate, String endDate, String userId, int pageNumber, int pageSize) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(startDate)){
			columns.ge("visit_date",startDate + " 00:00:00");
		}
		if(StrKit.notBlank(endDate)){
			columns.le("visit_date",endDate + " 23:59:59");
		}
		if(StrKit.notBlank(userId)) {
			columns.eq("reception_user_id", userId);
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "visit_date desc");
	}

	public Page<MmsCustomerVisit> paginateByCondition(MmsCustomerVisit model, int pageNumber, int pageSize) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(model.getCustomerId())){
			columns.add("customer_id",model.getCustomerId());
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "visit_date desc");
	}

	public Page<MmsCustomerVisit> visitRecordPage(int pageNumber, int pageSize, String startDate, String endDate, String receptionUserId,
			String visitType, String actionType) {
		List<Object> param = Lists.newArrayList();
		String selectSql = "select * ";
		String sql = "from mms_customer_visit where del_flag='0'";
		if(StringUtils.isNotBlank(startDate)){
        	sql += " and visit_date>=?";
        	param.add(startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)){
        	sql += " and visit_date<=?";
        	param.add(endDate + " 23:59:59");
        }
        if(StringUtils.isNotBlank(receptionUserId)){
        	sql += " and reception_user_id=?";
        	param.add(receptionUserId);
        }
        if(StringUtils.isNotBlank(visitType)){
        	sql += " and visit_type=?";
        	param.add(visitType);
        }
        if(StringUtils.isNotBlank(actionType)) {
        	if("callPass".equals(actionType)) {
        		sql += " and call_pass is null";
        	}else if("callNoPass".equals(actionType)) {
        		sql += " and call_pass is not null";
        	}else if("callTotal".equals(actionType)) {
        		sql += " and (call_pass is null or call_pass is not null)";
        	}else if("appealTypeConsult".equals(actionType)) {
        		sql += " and appeal_type ='consult'";
        	}else if("appealTypeComplaint".equals(actionType)) {
        		sql += " and appeal_type ='complaint'";
        	}else if("appealTypeRefund".equals(actionType)) {
        		sql += " and appeal_type ='refund'";
        	}else if("appealTypeChangeRoom".equals(actionType)) {
        		sql += " and appeal_type ='change_room'";
        	}else if("appealTypeBuyCard".equals(actionType)) {
        		sql += " and appeal_type ='buy_card'";
        	}else if("appealTypeCheckCard".equals(actionType)) {
        		sql += " and appeal_type ='check_card'";
        	}else if("appealTypeTransferCard".equals(actionType)) {
        		sql += " and appeal_type ='transfer_card'";
        	}else if("appealTypeChangeTrip".equals(actionType)) {
        		sql += " and appeal_type ='change_trip'";
        	}else if("appealTypeCancelTrip".equals(actionType)) {
        		sql += " and appeal_type ='cancel_trip'";
        	}else if("appealTypeOther".equals(actionType)) {
        		sql += " and appeal_type ='other'";
        	}else if("appealTypeTotal".equals(actionType)) {
        		sql += " and appeal_type is not null";
        	}else if("satisfied".equals(actionType)) {
        		sql += " and (handle_result='satisfied' or handle_result='very_satisfied')";
        	}else if("noSatisfied".equals(actionType)) {
        		sql += " and (handle_result='very_bad' or handle_result='dissatisfied')";
        	}else if("satisfiedTotal".equals(actionType)) {
        		sql += " and handle_result is not null";
        	}
        }
        sql += " order by visit_date desc";
		return DAO.paginate(pageNumber,pageSize,selectSql,sql,param.toArray());
	}

	public Record getCallPassStatistics(String startDate, String endDate, String receptionUserId, String visitType) {
		List<Object> param = Lists.newArrayList();
		String sql = "select sum(case when call_pass is null then 1 else '0' end) as passCount,"
			+ "sum(case when call_pass is not null then 1 else '0' end) as noPassCount "
			+ "from mms_customer_visit where del_flag='0'";
		if(StringUtils.isNotBlank(startDate)){
        	sql += " and visit_date>=?";
        	param.add(startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)){
        	sql += " and visit_date<=?";
        	param.add(endDate + " 23:59:59");
        }
        if(StringUtils.isNotBlank(receptionUserId)){
        	sql += " and reception_user_id=?";
        	param.add(receptionUserId);
        }
        if(StringUtils.isNotBlank(visitType)){
        	sql += " and visit_type=?";
        	param.add(visitType);
        }
		return Db.findFirst(sql, param.toArray());
	}

	public Record getAppealTypeStatistics(String startDate, String endDate, String receptionUserId, String visitType) {
		List<Object> param = Lists.newArrayList();
		String sql = "select sum(case when appeal_type ='consult' then 1 else '0' end) as consultCount,"
			+ "sum(case when appeal_type ='complaint' then 1 else '0' end) as complaintCount,"
			+ "sum(case when appeal_type ='refund' then 1 else '0' end) as refundCount,"
			+ "sum(case when appeal_type ='change_room' then 1 else '0' end) as changeRoomCount,"
			+ "sum(case when appeal_type ='buy_card' then 1 else '0' end) as buyCardCount,"
			+ "sum(case when appeal_type ='check_card' then 1 else '0' end) as checkCardCount,"
			+ "sum(case when appeal_type ='transfer_card' then 1 else '0' end) as transferCardCount,"
			+ "sum(case when appeal_type ='change_trip' then 1 else '0' end) as changeTripCount,"
			+ "sum(case when appeal_type ='cancel_trip' then 1 else '0' end) as cancelTripCount,"
			+ "sum(case when appeal_type ='other' then 1 else '0' end) as otherCount "
			+ "from mms_customer_visit where del_flag='0'";
		if(StringUtils.isNotBlank(startDate)){
        	sql += " and visit_date>=?";
        	param.add(startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)){
        	sql += " and visit_date<=?";
        	param.add(endDate + " 23:59:59");
        }
        if(StringUtils.isNotBlank(receptionUserId)){
        	sql += " and reception_user_id=?";
        	param.add(receptionUserId);
        }
        if(StringUtils.isNotBlank(visitType)){
        	sql += " and visit_type=?";
        	param.add(visitType);
        }
		return Db.findFirst(sql, param.toArray());
	}

	public Record getSatisfiedStatistics(String startDate, String endDate, String receptionUserId, String visitType) {
		List<Object> param = Lists.newArrayList();
		String sql = "select sum(case when handle_result='satisfied' or handle_result='very_satisfied' then 1 else '0' end) as satisfiedCount,"
			+ "sum(case when handle_result='very_bad' or handle_result='dissatisfied' then 1 else '0' end) as notSatisfiedCount "
			+ "from mms_customer_visit where del_flag='0'";
		if(StringUtils.isNotBlank(startDate)){
        	sql += " and visit_date>=?";
        	param.add(startDate + " 00:00:00");
        }
        if(StringUtils.isNotBlank(endDate)){
        	sql += " and visit_date<=?";
        	param.add(endDate + " 23:59:59");
        }
        if(StringUtils.isNotBlank(receptionUserId)){
        	sql += " and reception_user_id=?";
        	param.add(receptionUserId);
        }
        if(StringUtils.isNotBlank(visitType)){
        	sql += " and visit_type=?";
        	param.add(visitType);
        }
		return Db.findFirst(sql, param.toArray());
	}
	
}