package com.cszn.integrated.service.provider.main;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.main.MainMallUserAddressService;
import com.cszn.integrated.service.entity.main.MainMallUserAddress;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.Date;
import java.util.List;

@Bean
public class MainMallUserAddressServiceImpl extends JbootServiceBase<MainMallUserAddress> implements MainMallUserAddressService {

    @Override
    public boolean saveUserAddress(MainMallUserAddress address){
        boolean flag=false;
        if(StrKit.isBlank(address.getId())){
            address.setId(IdGen.getUUID());
            address.setDelFlag("0");
            address.setCreateDate(new Date());
            address.setUpdateDate(new Date());
            if("1".equals(address.getIsDefault())){
                Db.update(" update main_mall_user_address set is_default='0' where del_flag='0' and unionid=? ",address.getUnionid());
            }
            flag=address.save();
        }else{
            address.setUpdateDate(new Date());
            if("1".equals(address.getIsDefault())){
                Db.update(" update main_mall_user_address set is_default='0' where del_flag='0' and unionid=? ",address.getUnionid());
            }
            flag=address.update();
        }
        return flag;
    }

    @Override
    public List<MainMallUserAddress> getUserAddress(String unionid){

        return DAO.find("select * from main_mall_user_address where unionid=? and del_flag='0' order by is_default desc,create_date ",unionid);
    }

}
