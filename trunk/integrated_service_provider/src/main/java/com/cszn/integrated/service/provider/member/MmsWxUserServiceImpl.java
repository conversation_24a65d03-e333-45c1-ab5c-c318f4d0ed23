package com.cszn.integrated.service.provider.member;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.MiniProgramUtils;
import com.cszn.integrated.service.api.member.MmsWxSubscriptionsService;
import com.cszn.integrated.service.api.member.MmsWxUserCardRelaService;
import com.cszn.integrated.service.api.member.MmsWxUserService;
import com.cszn.integrated.service.entity.member.MmsWxSubscriptions;
import com.cszn.integrated.service.entity.member.MmsWxUser;
import com.cszn.integrated.service.entity.member.MmsWxUserCardRela;
import com.cszn.integrated.service.entity.status.Global;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 微信用户
 */
@Bean
public class MmsWxUserServiceImpl extends JbootServiceBase<MmsWxUser> implements MmsWxUserService {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);


    @Inject
    MmsWxSubscriptionsService mmsWxSubscriptionsService;

    @Inject
    MmsWxUserCardRelaService mmsWxUserCardRelaService;


    public Page<Record> getSubscriptionsByUser(Integer pageNumber, Integer pageSize, String userId,String subscriberId){

        Page<MmsWxSubscriptions> page=mmsWxSubscriptionsService.subscriptionsPage(pageNumber,pageSize,userId,subscriberId);
        if(page==null)return null;
        if(page.getList() == null || page.getList().size() <= 0) return null;
        List<Record> recordList = Lists.newArrayList();
        for (MmsWxSubscriptions item : page.getList()) {
            if(StringUtils.isNotBlank(userId)) {
                MmsWxUser wxUser = DAO.findById(item.getSubscriberId());
                if(wxUser != null){
                    Record record = getRecordUser(wxUser);
                    if(record != null)recordList.add(record);
                }
            }else{
                int flag = 0;
                Record subExist = Db.findFirst("SELECT * FROM mms_wx_subscriptions WHERE user_id = ? AND subscriber_id = ?",subscriberId,item.getUserId());
                if(subExist != null)flag = 1;
                MmsWxUser wxUser = DAO.findById(item.getUserId());
                if(wxUser != null){
                    Record record = getRecordUser(wxUser);
                    if(record != null) {
                        record.set("fanFlag", flag);
                        recordList.add(record);
                    }
                }
            }
        }
        Page<Record> reocrdPage = new Page<>();
        reocrdPage.setList(recordList);
        reocrdPage.setPageNumber(page.getPageNumber());
        reocrdPage.setPageSize(page.getPageSize());
        reocrdPage.setTotalPage(page.getTotalPage());
        reocrdPage.setTotalRow(page.getTotalRow());
        return reocrdPage;
    }

    /**
     * 根据unionid查询微信用户
     * @param unionid
     * @return
     */
    public MmsWxUser getWxUserByUnionid(String unionid) {
        String sql = "select * from mms_wx_user where unionid=?";
        return DAO.findFirst(sql, unionid);
    }

    /**
     * 根据openId查询微信用户
     * @return
     */
    public MmsWxUser getWxUserByOpenid(String openid) {
        String sql = "select * from mms_wx_user where openid=?";
        return DAO.findFirst(sql, openid);
    }


    public String bindMemberCard(String userId, String cardId) {
        //去重
        String sql = "SELECT * FROM mms_wx_user_card_rela WHERE user_id = ? AND card_id = ? and del_flag='0' ";
        List<MmsWxUserCardRela> relaList = mmsWxUserCardRelaService.find(sql,userId,cardId);
        if(relaList != null && relaList.size()>0) return "";

        /*String sql2 = "SELECT * FROM mms_wx_user_card_rela WHERE card_id = ?";
        if(Db.findFirst(sql2,cardId)!=null){
            return "exist";
        }*/


        MmsWxUserCardRela wxUserCardRela = new MmsWxUserCardRela();
        wxUserCardRela.setId(IdGen.getUUID());
        wxUserCardRela.setUserId(userId);
        wxUserCardRela.setCardId(cardId);
        wxUserCardRela.setDelFlag("0");
        wxUserCardRela.setCreateTime(new Date());
        if(wxUserCardRela.save()){
            return "suc";
        }else{
            return null;
        }
    }



    /**
     * 微信用户信息保存方法
     * @param wxUser
     * @return boolean
     */
    public boolean saveWxuser(MmsWxUser wxUser){
        boolean flag = false;
        try {
            //ID为空是新增，不为空就是修改
            if(StringUtils.isBlank(wxUser.getId())){
                wxUser.setId(UUID.randomUUID().toString().replace("-", ""));
                wxUser.setRegTime(new Date());
                flag = wxUser.save();
            }else{
                flag = wxUser.update();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 保存微信用户信息完成回调旅居同步用户信息
     * @param userId
     * @return
     */
    public boolean CheckMember(String userId){
        logger.info("微信用户回调旅居同步用户信息begin...");
        boolean flag=false;
        CloseableHttpClient httpClient=null;
        try {
            //获取配置文件旅居地址
            String sojournUrl = Global.sojournUrl;
            logger.info("微信用户回调旅居同步用户信息Url：[{}]", sojournUrl);
            httpClient= HttpClients.createDefault();
            HttpPost httpPost=new HttpPost(sojournUrl+"Member/CheckMember");
            List<NameValuePair> list=new ArrayList<>();
            BasicNameValuePair nameValuePair = new BasicNameValuePair("userId", userId);
            list.add(nameValuePair);
            logger.info("微信用户回调旅居同步用户信息接口入参id：[{}]",userId);
            UrlEncodedFormEntity formEntity=new UrlEncodedFormEntity(list);
            httpPost.setEntity(formEntity);
            CloseableHttpResponse response=httpClient.execute(httpPost);
            HttpEntity httpEntiey=response.getEntity();
            String result= EntityUtils.toString(httpEntiey,"UTF-8");
            logger.info("微信用户回调旅居同步用户信息响应数据：[{}]",result);
            JSONObject jsonObject= JSON.parseObject(result);
            if(jsonObject.containsKey("code") && jsonObject.getIntValue("code")==0){
                flag=true;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }finally{
            try {
                if(httpClient!=null){
                    httpClient.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        logger.info("微信用户回调旅居同步用户信息end...");
        return flag;
    }

    public Page<MmsWxUser> findList(Integer pageNumber, Integer pageSize, MmsWxUser wxUser){

        List<Object> param = new ArrayList<>();
        String sql = "FROM mms_wx_user ";
        if(wxUser != null) {
            if (StringUtils.isNotBlank(wxUser.getNickName())) {
                sql += "WHERE nick_name LIKE CONCAT('%',?,'%') ";
                param.add(wxUser.getNickName().trim());
            }
        }
        sql += "ORDER BY reg_time DESC";
        return DAO.paginate(pageNumber,pageSize,"SELECT * ",sql,param.toArray());
    }




    public List<MmsWxUser> getWxUserByNickName(String nickName){

        List<Object> param = new ArrayList<>();
        String sql = "SELECT * FROM mms_wx_user ";

        if(StrKit.isBlank(nickName)) return new ArrayList<>();

        sql += "WHERE nick_name LIKE CONCAT('%',?,'%')";
        param.add(nickName.trim());

        return DAO.find(sql,param.toArray());
    }

	public List<Record> getIntegralCardListByUnionid(String unionid) {
		final String sql = "select c.card_number as cardNumber from mms_wx_user u "
			+ "left join mms_wx_user_card_rela r on r.user_id=u.id "
			+ "left join fina_membership_card c on c.id=r.card_id "
			+ "left join main_membership_card_type t on t.id=c.card_type_id "
			+ "where c.del_flag='0' and c.is_lock='0' and t.is_integral='1' and r.del_flag='0' and u.unionid=?";
		List<Record> cardList = Db.find(sql, unionid);
		MiniProgramUtils.dealRecordNull(cardList);
		return cardList;
	}

	public Record getRecordUser(MmsWxUser wxUser){
        Record record = new Record();
        try {
            record.set("id",wxUser.getId());
            record.set("unionid",wxUser.getUnionid());
            record.set("openid",wxUser.getOpenid());
            record.set("nickName",wxUser.getNickName());
            record.set("gender",wxUser.getGender());
            record.set("avatarUrl",wxUser.getAvatarUrl());
            record.set("country",wxUser.getCountry());
            record.set("province",wxUser.getProvince());
            record.set("city",wxUser.getCity());
            record.set("subscribe",wxUser.getSubscribe());
            record.set("subscribeTime",wxUser.getSubscribeTime());
            record.set("language",wxUser.getLanguage());
            record.set("regTime",wxUser.getRegTime());
        } catch (Exception e) {
            return null;
        }
        return record;
    }

    public static void main(String[] ars) throws Exception{
        String url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET".replace("APPID", "wxc6a8c7c3575ce6e3").replace("APPSECRET", "3f260430000665a2eea4c6e7c130d683");
        String str = HttpKit.get(url);
        System.out.println(str);
    }

}