package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsSmsDetailService;
import com.cszn.integrated.service.api.member.MmsSmsService;
import com.cszn.integrated.service.entity.member.MmsSms;
import com.cszn.integrated.service.entity.member.MmsSmsDetail;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MmsSmsDetailServiceImpl extends JbootServiceBase<MmsSmsDetail> implements MmsSmsDetailService {

    @Inject
    private MmsSmsService mmsSmsService;

    @Override
    public boolean detailDel(String id, String userId) {
        MmsSmsDetail detail=new MmsSmsDetail();
        detail.setId(id);
        detail.setDelFlag("1");
        detail.setUpdateBy(userId);
        detail.setUpdateDate(new Date());
        return detail.update();
    }

    @Override
    public Page<MmsSmsDetail> tablePage(int pageNumber, int pageSize, MmsSmsDetail detail) {
        String sql=" from mms_sms_detail where del_flag='0' and sms_id=? ";
        List<Object> params=new ArrayList<>();
        params.add(detail.getSmsId());
        if(StrKit.notBlank(detail.getSendStatus())){
            sql+=" and send_status=? ";
            params.add(detail.getSendStatus());
        }

        return DAO.paginate(pageNumber,pageSize,"select * ",sql,params.toArray());
    }

    @Override
    public boolean saveSmsDetail(String smsId, String userId) {
        List<String> cardTypeIdList= Db.query("select card_type_id from mms_sms_card_type where sms_id=? ",smsId);
        if(cardTypeIdList==null || cardTypeIdList.size()==0){
            return false;
        }
        MmsSms sms=mmsSmsService.findById(smsId);
        String str="";
        for(String typeId:cardTypeIdList){
            str+="?,";
        }
        str=str.substring(0,str.length()-1);
        List<String> telephoneList=Db.query("select telephone from fina_membership_card where del_flag='0' and card_type_id in("+str+") " +
                "GROUP BY telephone ",cardTypeIdList.toArray());
        List<MmsSmsDetail> detailList=new ArrayList<>();

        String regex = "^1[3-9][0-9]\\d{8}$";
        for(String telephone:telephoneList){
            if(StrKit.isBlank(telephone) || !telephone.matches(regex)){
                continue;
            }
            MmsSmsDetail detail=new MmsSmsDetail();
            detail.setId(IdGen.getUUID());
            detail.setSmsId(smsId);
            detail.setSendTime(sms.getSendTime());
            detail.setSendContent(sms.getMessageContent());
            detail.setPhoneNum(telephone);
            detail.setSendStatus("0");
            detail.setCreateBy(userId);
            detail.setCreateDate(new Date());
            detail.setUpdateBy(userId);
            detail.setUpdateDate(new Date());
            detail.setDelFlag("0");
            detailList.add(detail);
        }
        int[] nums=Db.batchSave(detailList,detailList.size());
        for(int i:nums){
            if(i<1){
                return false;
            }
        }
        return true;
    }



    @Override
    public List<MmsSmsDetail> findNotSentDetail(int size) {
        String sql="select * from mms_sms_detail where del_flag='0' and send_status='0' and send_time<=now() and send_time > DATE_SUB(now(),INTERVAL 10 HOUR)  limit ?";
        return DAO.find(sql,size);
    }
}
