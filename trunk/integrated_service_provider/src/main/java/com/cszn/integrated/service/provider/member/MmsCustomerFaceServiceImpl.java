package com.cszn.integrated.service.provider.member;

import java.util.List;

import com.cszn.integrated.service.api.member.MmsCustomerFaceService;
import com.cszn.integrated.service.entity.member.MmsCustomerFace;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsCustomerFaceServiceImpl extends JbootServiceBase<MmsCustomerFace> implements MmsCustomerFaceService {

	public Page<MmsCustomerFace> paginateByCondition(MmsCustomerFace model, int pageNumber, int pageSize) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if (StrKit.notBlank(model.getCustomerName())) {
			columns.likeAppendPercent("customer_name", model.getCustomerName());
		}
		if (StrKit.notBlank(model.getIdcard())) {
			columns.likeAppendPercent("idcard", model.getIdcard());
		}
		if (StrKit.notBlank(model.getPicType())) {
			columns.eq("pic_type", model.getPicType());
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "create_time desc");
	}
	
	@Override
	public List<MmsCustomerFace> findList(String customerName, String idcard, String picType) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(customerName)) {
			columns.eq("customer_name", customerName);
		}
		if(StrKit.notBlank(idcard)) {
			columns.eq("idcard", idcard);
		}
		if(StrKit.notBlank(picType)) {
			columns.eq("pic_type", picType);
		}
		return DAO.findListByColumns(columns, "create_time desc");
	}
	
	@Override
	public MmsCustomerFace findByIdcardAndPicType(String idcard, String picType) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(idcard)) {
			columns.add("idcard", idcard);
		}
		if(StrKit.notBlank(picType)) {
			columns.add("pic_type", picType);
		}
		return DAO.findFirstByColumns(columns);
	}

	@Override
	public MmsCustomerFace findByNameAndIdcard(String customerName, String idcard) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL).add("customer_name", customerName).add("idcard", idcard);
		return DAO.findFirstByColumns(columns);
	}

	@Override
	public MmsCustomerFace findByTypeAndIdcard(String picType, String idcard) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL).add("pic_type", picType).add("idcard", idcard);
		return DAO.findFirstByColumns(columns);
	}
}