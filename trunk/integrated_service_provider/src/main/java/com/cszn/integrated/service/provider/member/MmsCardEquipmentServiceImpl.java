package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsCardEquipmentService;
import com.cszn.integrated.service.entity.member.MmsCardEquipment;
import com.cszn.integrated.service.entity.member.MmsEquipmentType;
import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/27
 **/
@Bean
public class MmsCardEquipmentServiceImpl extends JbootServiceBase<MmsCardEquipment> implements MmsCardEquipmentService {



    @Override
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize,String cardNumber,String equipmentNo,String equipmentType) {
        List<Object> params = Lists.newArrayList();
        String sql = "FROM mms_card_equipment ce LEFT JOIN fina_membership_card fc ON ce.card_id = fc.id " +
                "LEFT JOIN mms_equipment_type me ON ce.equipment_type_id = me.id WHERE ce.del_flag = 0 ";

        if(StringUtils.isNotBlank(cardNumber)){
            sql += " AND ce.card_id in (SELECT fc.id from fina_membership_card fc WHERE fc.card_number LIKE concat('%',?,'%')) ";
            params.add(cardNumber);
        }
        if(StringUtils.isNotBlank(equipmentNo)){
            sql += " AND ce.equipment_no LIKE CONCAT('%',?,'%') ";
            params.add(equipmentNo);
        }
        if(StringUtils.isNotBlank(equipmentType)){
            sql += " AND equipment_type_id in (SELECT me.id from mms_equipment_type me WHERE equipment_type = ?) ";
            params.add(equipmentType);
        }

        sql += "ORDER BY ce.update_date DESC";
        return Db.paginate(pageNumber,pageSize,"SELECT ce.id, ce.card_id AS cardId, fc.card_number AS cardNumber,ce.equipment_type_id AS equipmentTypeId, me.equipment_type AS equipmentType, ce.equipment_no AS equipmentNo, ce.step_set as stepSet, ce.del_flag AS delFlag, ce.create_by AS createBy, ce.create_date AS createDate, ce.update_by AS updateBy," +
                "ce.update_date AS updateDate ",sql,params.toArray());
    }



    @Override
    public String saveCardEquipment(MmsCardEquipment cardEquipment, String userId) {
        if(getDistinct(cardEquipment) == null)return "";
        if(StringUtils.isBlank(cardEquipment.getId())){
            cardEquipment.setId(IdGen.getUUID());
            cardEquipment.setDelFlag("0");
            cardEquipment.setCreateBy(userId);
            cardEquipment.setCreateDate(new Date());
            cardEquipment.setUpdateBy(userId);
            cardEquipment.setUpdateDate(new Date());
            if(cardEquipment.save()){
                return "suc";
            }else{
                return null;
            }
        }else{
            cardEquipment.setUpdateBy(userId);
            cardEquipment.setUpdateDate(new Date());
            if(cardEquipment.update()){
                return "suc";
            }else{
                return null;
            }
        }
    }



    @Override
    public boolean delCardEquipment(String id, String userId) {
        MmsCardEquipment cardEquipment =  new MmsCardEquipment();
        cardEquipment.setId(id);
        cardEquipment.setDelFlag("1");
        cardEquipment.setUpdateBy(userId);
        cardEquipment.setUpdateDate(new Date());
        return cardEquipment.update();
    }




    @Override
    public String getDistinct(MmsCardEquipment cardEquipment) {
        List<Object> params = Lists.newArrayList();
        params.add(cardEquipment.getCardId());
        params.add(cardEquipment.getEquipmentTypeId());
        List<MmsCardEquipment> list = null;
        if(StringUtils.isBlank(cardEquipment.getId())){
            list = DAO.find("SELECT * FROM mms_card_equipment WHERE del_flag = 0 AND card_id = ? AND equipment_type_id = ?",params.toArray());
        }else{
            params.add(cardEquipment.getId());
            list = DAO.find("SELECT * FROM mms_card_equipment WHERE del_flag = 0 AND card_id = ? AND equipment_type_id = ? AND id != ?",params.toArray());
        }
        if((list != null && list.size() >0 ))return null;
        return "";
    }




    @Override
    public boolean updateSet(String id, String set) {
        MmsCardEquipment ce = new MmsCardEquipment();
        ce.setId(id);
        ce.setStepSet(set);
        return ce.update();
    }




    @Override
    public MmsCardEquipment get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }
}
