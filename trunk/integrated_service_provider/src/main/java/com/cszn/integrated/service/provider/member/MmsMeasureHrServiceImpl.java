package com.cszn.integrated.service.provider.member;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.BioHelper;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsMeasureHrService;
import com.cszn.integrated.service.entity.member.MmsMeasureHr;
import com.cszn.integrated.service.entity.status.MeasureResult;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/28
 **/
@Bean
public class MmsMeasureHrServiceImpl extends JbootServiceBase<MmsMeasureHr> implements MmsMeasureHrService {

    @Override
    public Map<String,List> memberHr(String cardNumber, String type) {
        Map<String, List> map=new HashMap<>();
        String sql="";
        if(type.equals("day")){
            sql="select concat(DATE_FORMAT(measure_time,'%H'),':00') as time,measure_value as hr,measure_tips as hrTips from mms_measure_hr where id in (" +
                    "select min(h.id) from mms_measure_hr h left join mms_card_equipment e on h.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=h.member_id  " +
                    "where c.card_number=? and e.del_flag='0' and measure_value>0 and TO_DAYS(h.measure_time)=TO_DAYS(now()) GROUP BY DATE_FORMAT(measure_time,'%Y%m%d%H')) order by measure_time";
        }else if(type.equals("week")){
            sql="select DATE_FORMAT(measure_time,'%m-%d') as time,measure_value as hr,measure_tips as hrTips from mms_measure_hr where id in (" +
                    "select min(h.id) from mms_measure_hr h left join mms_card_equipment e on h.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=h.member_id " +
                    "where c.card_number=? and e.del_flag='0' and measure_value>0 and YEARWEEK(h.measure_time)=YEARWEEK(now()) GROUP BY DATE_FORMAT(measure_time,'%Y%m%d')) order by measure_time";
        }else if(type.equals("month")){
            sql="select DATE_FORMAT(measure_time,'%m-%d') as time,measure_value as hr,measure_tips as hrTips from mms_measure_hr where id in (" +
                    "select min(h.id) from mms_measure_hr h left join mms_card_equipment e on h.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=h.member_id " +
                    "where c.card_number=? and e.del_flag='0' and measure_value>0 and DATE_FORMAT(h.measure_time,'%Y%m')=DATE_FORMAT(now(),'%Y%m') GROUP BY DATE_FORMAT(measure_time,'%Y%m%d')) order by measure_time";
        }
        if(StrKit.notBlank(sql)){
            List<Record> recordList=Db.find(sql,cardNumber);
            if(recordList==null){
                return map;
            }
            List<String> timeList=new ArrayList<>();
            List<Integer> hrList=new ArrayList<>();
            List<String> hrTipsList=new ArrayList<>();
            for (Record record:recordList){
                timeList.add(record.getStr("time"));
                hrList.add(Integer.valueOf(record.getStr("hr")));
                hrTipsList.add(record.getStr("hrTips"));
            }
            map.put("time",timeList);
            map.put("hr",hrList);
            map.put("hrTips",hrTipsList);
        }
        return map;
    }

    @Override
    public Map<String, Object> latestHr(String cardNumber) {
        Map<String,Object> map=new HashMap<>();
        String sql="select h.measure_value as hr,h.measure_tips as tips from mms_measure_hr h " +
                "left join mms_card_equipment e on h.equipment_no=e.equipment_no " +
                "left join fina_membership_card c on c.id=e.card_id and c.member_id=h.member_id " +
                "where c.card_number=? and e.del_flag='0' and h.measure_value>0 order by h.create_date desc limit 1";
        Record record=Db.findFirst(sql,cardNumber);
        if(record!=null){
            map=record.getColumns();
        }
        return map;
    }

    @Override
    public Map<String, Object> gethrData(String memberId, String dataRecord) {
        Map<String,Object> hrMap = new HashMap<>();
        String sql = "select measure_value as measureValue,measure_time AS measureTime " +
                "from mms_measure_hr where del_flag = 0 and measure_value is not null and measure_time is not null and measure_value != 0 and member_id = ? ";

        if("tenRecord".equals(dataRecord)){
            List<Record> hrList = Db.find(sql + "order by measure_time desc limit 10",memberId);
            return setValues(hrList);
        }else if("7DayRecord".equals(dataRecord)){
            List<Record> hrList = Db.find(sql + "and DATE_SUB(CURDATE(), INTERVAL 7 DAY) <= date(measure_time) order by measure_time desc",memberId);
            return setValues(hrList);
        }else if("30DayRecord".equals(dataRecord)){
            List<Record> hrList = Db.find(sql + "and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= date(measure_time) order by measure_time desc",memberId);
            return setValues(hrList);
        }else if("monRecord".equals(dataRecord)){
            List<Record> hrList = Db.find(sql + "and DATE_FORMAT( measure_time, '%Y%m' ) = DATE_FORMAT( CURDATE() , '%Y%m' ) order by measure_time desc",memberId);
            return setValues(hrList);
        }
        return hrMap;
    }




    @Override
    public boolean saveHrData(String equipmentNo,String measureValue, String userId) {
        String memberId = BioHelper.getMemberIdByEqNo(equipmentNo);
        MmsMeasureHr hr = new MmsMeasureHr();
        hr.setId(IdGen.getUUID());
        hr.setEquipmentNo(equipmentNo);
        hr.setMemberId(memberId);
        hr.setMeasureName("心率");
        hr.setMeasureTime(new Date());
        hr.setMeasureValue(measureValue);
        hr.setMeasureRange("60~100");
        if(Integer.valueOf(measureValue)!=null && Integer.valueOf(measureValue)<60){
            hr.setMeasureTips(MeasureResult.LOW);
        }else if(Integer.valueOf(measureValue)!=null && Integer.valueOf(measureValue)>100){
            hr.setMeasureTips(MeasureResult.HIGH);
        }else{
            hr.setMeasureTips(MeasureResult.NORMAL);
        }
        hr.setDelFlag("0");
        hr.setCreateBy(userId);
        hr.setUpdateBy(userId);
        hr.setCreateDate(new Date());
        hr.setUpdateDate(new Date());
        return hr.save();
    }




    public Map<String,Object> setValues(List<Record> recordList){
        Map<String,Object> hrMap = new HashMap<>();
        JSONObject hrJson = new JSONObject();
        JSONArray hrArray = new JSONArray();
        JSONArray measureJsonData = new JSONArray();
        JSONArray timeArray = new JSONArray();

        if(recordList != null && recordList.size() > 0) {
            for (int i = recordList.size() - 1; i >= 0; i--) {
                hrArray.add(Long.parseLong(recordList.get(i).get("measureValue")));
                timeArray.add(recordList.get(i).get("measureTime"));
            }
        }
        hrJson.put("data",hrArray);
        hrJson.put("name","心率");
        measureJsonData.add(hrJson);
        hrMap.put("measureJsonData",measureJsonData);
        hrMap.put("measureTimeArray",timeArray);
        return hrMap;
    }


}
