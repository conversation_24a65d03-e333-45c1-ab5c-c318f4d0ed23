package com.cszn.integrated.service.provider.member;


import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsGatewayService;
import com.cszn.integrated.service.entity.main.MainBedType;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.google.common.collect.Lists;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;
import java.util.Date;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/26
 **/
@Bean
public class MmsGatewayServiceImpl extends JbootServiceBase<MmsGateway> implements MmsGatewayService {



    @Override
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize, MmsGateway gateway) {
        List<Object> params = Lists.newArrayList();
        String sql = "FROM mms_gateway g LEFT JOIN main_base b ON g.base_id = b.id " +
                "LEFT JOIN main_base_building d ON g.building_id = d.id LEFT JOIN main_base_floor f ON g.floor_id = f.id " +
                "WHERE g.del_flag = 0 ";
        if(gateway != null){
            if(StringUtils.isNotBlank(gateway.getManufactor())){
                sql += " AND g.manufactor LIKE CONCAT('%',?,'%') ";
                params.add(gateway.getManufactor());
            }
            if(StringUtils.isNotBlank(gateway.getBaseId())){
                sql += " AND g.base_id = ? ";
                params.add(gateway.getBaseId());
            }
            if(StringUtils.isNotBlank(gateway.getGatewayName())){
                sql += " AND (g.gateway_no LIKE CONCAT('%',?,'%') OR g.gateway_name LIKE CONCAT('%',?,'%')) ";
                params.add(gateway.getGatewayName());
                params.add(gateway.getGatewayName());
            }
            /*if(StringUtils.isNotBlank(gateway.getGatewayName())){
                sql += " AND g.gateway_name LIKE CONCAT('%',?,'%') ";
                params.add(gateway.getGatewayName());
            }*/
        }
        sql += "ORDER BY g.update_date DESC ";
        return Db.paginate(pageNumber,pageSize,"SELECT g.id, gateway_no AS gatewayNo, manufactor, gateway_name AS gatewayName, gateway_model AS gatewayModel, g.base_id AS baseId, " +
                "b.base_name AS baseName,d.building_name AS buildingName,f.floor_name AS floorName, g.building_id AS buildingId, g.floor_id AS floorId," +
                "part_type AS partType, position, put_way AS putWay, g.remark, g.del_flag AS delFlag, g.create_by AS createBy, g.create_date AS createDate, g.update_by AS updateBy," +
                "g.update_date AS updateDate ",sql,params.toArray());
    }



    @Override
    public String saveGateway(MmsGateway gateway, String userId) {
        if(getDistinct(gateway) == null)return "";
        if(StringUtils.isBlank(gateway.getId())){
            gateway.setId(IdGen.getUUID());
            gateway.setDelFlag("0");
            gateway.setCreateBy(userId);
            gateway.setCreateDate(new Date());
            gateway.setUpdateBy(userId);
            gateway.setUpdateDate(new Date());
            if(gateway.save()){
                return "suc";
            }else{
                return null;
            }
        }else{
            gateway.setUpdateBy(userId);
            gateway.setUpdateDate(new Date());
            if(gateway.update()){
                return "suc";
            }else{
                return null;
            }
        }
    }



    @Override
    public boolean delGateway(String id, String userId) {
        MmsGateway gatewayType =  new MmsGateway();
        gatewayType.setId(id);
        gatewayType.setDelFlag("1");
        gatewayType.setUpdateBy(userId);
        gatewayType.setUpdateDate(new Date());
        return gatewayType.update();
    }



    @Override
    public String getDistinct(MmsGateway gateway) {
        List<MmsGateway> list = null;
        if(StringUtils.isBlank(gateway.getId())){
            list = DAO.find("SELECT * FROM mms_gateway WHERE del_flag = 0 AND gateway_no =?",gateway.getGatewayNo());
        }else{
            list = DAO.find("SELECT * FROM mms_gateway WHERE del_flag = 0 AND gateway_no =? AND id != ?",gateway.getGatewayNo(),gateway.getId());
        }
        if((list != null && list.size() >0 ))return null;
        return "";
    }




    @Override
    public MmsGateway get(String id) {
        if (!StringUtils.isBlank(id)) {
            return DAO.findById(id);
        }
        return null;
    }
}
