package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsBlackRecordService;
import com.cszn.integrated.service.entity.member.MmsBlackRecord;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.aop.annotation.Bean;
import io.jboot.db.model.Columns;
import io.jboot.service.JbootServiceBase;

@Bean
public class MmsBlackRecordServiceImpl extends JbootServiceBase<MmsBlackRecord> implements MmsBlackRecordService {

	public Page<MmsBlackRecord> pageTable(int pageNumber, int pageSize, MmsBlackRecord model) {
		Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(model.getBlackListId())){
			columns.add("black_list_id",model.getBlackListId());
		}
		return DAO.paginateByColumns(pageNumber, pageSize, columns.getList(), "create_time desc");
	}

}
