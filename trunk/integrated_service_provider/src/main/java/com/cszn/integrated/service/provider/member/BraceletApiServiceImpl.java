package com.cszn.integrated.service.provider.member;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.BraceletApiService;
import com.cszn.integrated.service.entity.member.MmsMeasureBp;
import com.cszn.integrated.service.entity.member.MmsMeasureHr;
import com.cszn.integrated.service.entity.member.MmsMeasureSteps;
import com.cszn.integrated.service.entity.status.MeasureResult;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.aop.annotation.Bean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Bean
public class BraceletApiServiceImpl implements BraceletApiService {

    private static Logger logger = LoggerFactory.getLogger(BraceletApiServiceImpl.class);
    private static final Map<String,String> concurrentHashMap=new ConcurrentHashMap<>();

    @Override
    public void analysisData(JSONArray jsonArray) {
        List<MmsMeasureBp> mmsMeasureBpList=new ArrayList<>();
        List<MmsMeasureHr> mmsMeasureHrList=new ArrayList<>();
        List<MmsMeasureSteps> mmsMeasureStepsList=new ArrayList<>();
        String gatewayMac="";
        logger.info("手环上传数据："+jsonArray.toJSONString());
        //设备编号获取memberId
        Map<String,String> cardEquipment=new HashMap<>();
        for (int i=0;i<jsonArray.size();i++){
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            if(jsonObject.containsKey("type") && jsonObject.getString("type").equals("Unknown")){
                //测量数据
                String rawData=jsonObject.getString("rawData");
                //设备名称
                String bleName=jsonObject.getString("bleName");
                //mac
                String mac=jsonObject.getString("mac");
                //时间
                Date timestamp=jsonObject.getDate("timestamp");

                //截取数据字符串
                String dataStr=subStr(rawData);

                //判断数据是否添加
                if(!filterData(mac,dataStr)){
                    return;
                }
                logger.info("添加数据：mac:"+mac+",data:"+dataStr);



                //获取memberId并存放到map中
                String memberId="";
                if(StrKit.isBlank(cardEquipment.get(mac))){
                    String sql="select c.member_id as memberId from mms_card_equipment e left join fina_membership_card c on e.card_id=c.id where e.del_flag='0'" +
                            " and e.equipment_no=? order by e.create_date desc limit 1";
                    memberId=Db.queryStr(sql,mac);
                    cardEquipment.put(mac,memberId);
                }else{
                    memberId=cardEquipment.get(mac);
                }

                //如果为空return
                if(StrKit.isBlank(dataStr)){
                    return;
                }
                Map<String,String> result=analysisData(dataStr);
                //System.out.println(result);
                if(result!=null && result.size()>0){
                    //血压数据
                    MmsMeasureBp mmsMeasureBp=new MmsMeasureBp();
                    mmsMeasureBp.setId(IdGen.getUUID());
                    mmsMeasureBp.setGatewayNo(gatewayMac);
                    mmsMeasureBp.setMemberId(memberId);
                    mmsMeasureBp.setEquipmentNo(mac);
                    mmsMeasureBp.setMeasureName("血压");
                    //收缩压
                    mmsMeasureBp.setMeasureSbpValue(result.get("sbp"));
                    mmsMeasureBp.setMeasureSbpRange("90~139");
                    if(Integer.valueOf(result.get("sbp")) !=null && Integer.valueOf(result.get("sbp"))<90){
                        mmsMeasureBp.setMeasureSbpTips(MeasureResult.LOW);
                    }else if(Integer.valueOf(result.get("sbp")) !=null && Integer.valueOf(result.get("sbp"))>139){
                        mmsMeasureBp.setMeasureSbpTips(MeasureResult.HIGH);
                    }else{
                        mmsMeasureBp.setMeasureSbpTips(MeasureResult.NORMAL);
                    }
                    //舒张压
                    mmsMeasureBp.setMeasureDbpValue(result.get("dbp"));
                    mmsMeasureBp.setMeasureDbpRange("60~89");
                    if(Integer.valueOf(result.get("dbp"))!=null && Integer.valueOf(result.get("dbp"))<60){
                        mmsMeasureBp.setMeasureDbpTips(MeasureResult.LOW);
                    }else if(Integer.valueOf(result.get("dbp"))!=null && Integer.valueOf(result.get("sbp"))>189){
                        mmsMeasureBp.setMeasureDbpTips(MeasureResult.HIGH);
                    }else{
                        mmsMeasureBp.setMeasureDbpTips(MeasureResult.NORMAL);
                    }

                    mmsMeasureBp.setDelFlag("0");
                    mmsMeasureBp.setMeasureTime(timestamp);
                    mmsMeasureBp.setCreateDate(new Date());
                    mmsMeasureBp.setUpdateDate(new Date());
                    mmsMeasureBpList.add(mmsMeasureBp);
                    //心率
                    MmsMeasureHr mmsMeasureHr=new MmsMeasureHr();
                    mmsMeasureHr.setId(IdGen.getUUID());
                    mmsMeasureHr.setGatewayNo(gatewayMac);
                    mmsMeasureHr.setMemberId(memberId);
                    mmsMeasureHr.setEquipmentNo(mac);
                    mmsMeasureHr.setMeasureName("心率");
                    mmsMeasureHr.setMeasureValue(result.get("heart"));
                    mmsMeasureHr.setMeasureRange("60~100");
                    if(Integer.valueOf(result.get("heart"))!=null && Integer.valueOf(result.get("heart"))<60){
                        mmsMeasureHr.setMeasureTips(MeasureResult.LOW);
                    }else if(Integer.valueOf(result.get("heart"))!=null && Integer.valueOf(result.get("heart"))>100){
                        mmsMeasureHr.setMeasureTips(MeasureResult.HIGH);
                    }else{
                        mmsMeasureHr.setMeasureTips(MeasureResult.NORMAL);
                    }

                    mmsMeasureHr.setDelFlag("0");
                    mmsMeasureHr.setMeasureTime(timestamp);
                    mmsMeasureHr.setCreateDate(new Date());
                    mmsMeasureHr.setUpdateDate(new Date());
                    mmsMeasureHrList.add(mmsMeasureHr);

                    //步数
                    MmsMeasureSteps mmsMeasureSteps=new MmsMeasureSteps();
                    mmsMeasureSteps.setId(IdGen.getUUID());
                    mmsMeasureSteps.setGatewayNo(gatewayMac);
                    mmsMeasureSteps.setEquipmentNo(mac);
                    mmsMeasureSteps.setMemberId(memberId);
                    mmsMeasureSteps.setMeasureName("步数");
                    mmsMeasureSteps.setMeasureStepsValue(result.get("steps"));
                    mmsMeasureSteps.setMeasureMilageValue(result.get("milage"));
                    mmsMeasureSteps.setMeasureCalorieValue(result.get("calorie"));
                    mmsMeasureSteps.setDelFlag("0");
                    mmsMeasureSteps.setMeasureTime(timestamp);
                    mmsMeasureSteps.setCreateDate(new Date());
                    mmsMeasureSteps.setUpdateDate(new Date());
                    mmsMeasureStepsList.add(mmsMeasureSteps);
                }
            }else if(jsonObject.containsKey("type") && jsonObject.getString("type").equals("Gateway")){
                //获取网关信息
                gatewayMac=jsonObject.getString("mac");
            }
        }
        Db.batchSave(mmsMeasureBpList,mmsMeasureBpList.size());
        Db.batchSave(mmsMeasureHrList,mmsMeasureHrList.size());
        Db.batchSave(mmsMeasureStepsList,mmsMeasureStepsList.size());
    }

    /**
     * 获取数据字符串
     */
    public String subStr(String str){
        if(StrKit.isBlank(str)){
            return null;
        }
        String start="020105060959756E54750AFF";
        String end="03198001050212180F18";
        if(str.indexOf(start)<0){
            return null;
        }
        if(str.indexOf(end)<0){
            return null;
        }
        return str.substring(start.length(),str.indexOf(end));
    }

    /**
     * 解析测量数据
     * @param rawData
     * @return
     */
    public Map<String,String> analysisData(String rawData){
        Map<String,String> map=new HashMap<>();
        if(StrKit.notBlank(rawData)){
            String steps=rawData.substring(0,4);
            String milage=rawData.substring(4,8);
            String calorie=rawData.substring(8,12);
            String heart=rawData.substring(12,14);
            String bp=rawData.substring(14,18);
            map.put("steps",Integer.valueOf(steps.substring(2,4)+steps.substring(0,2),16).toString());
            map.put("milage",Integer.valueOf(milage.substring(2,4)+milage.substring(0,2),16).toString());
            map.put("calorie",Integer.valueOf(calorie.substring(2,4)+calorie.substring(0,2),16).toString());
            map.put("heart",Integer.valueOf(heart,16).toString());
            map.put("sbp",Integer.valueOf(bp.substring(2,4),16).toString());
            map.put("dbp",Integer.valueOf(bp.substring(0,2),16).toString());
        }
        return map;
    }

    /**
     * 过滤数据
     * @param mac
     * @param dataStr
     * @return
     */
    public boolean filterData(String mac,String dataStr){
        if(StrKit.isBlank(mac) || StrKit.isBlank(dataStr)){
            return false;
        }
        //如果map中该设备的数据为空，则初始化
        if(StrKit.isBlank(concurrentHashMap.get(mac))){
            concurrentHashMap.put(mac,dataStr);
            return true;
        }
        //判断数据是否和map中的一样，如果一样返回false
        if(concurrentHashMap.get(mac).equals(dataStr)){
            return false;
        }else{
            //如果不一样返回true并更新map中的数据
            concurrentHashMap.put(mac,dataStr);
            return true;
        }
    }
}
