package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsQuestionnaireQuestionOptionService;
import com.cszn.integrated.service.api.member.MmsQuestionnaireQuestionService;
import com.cszn.integrated.service.api.member.MmsQuestionnaireService;
import com.cszn.integrated.service.entity.member.MmsQuestionnaire;
import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestion;
import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestionOption;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MmsQuestionnaireServiceImpl extends JbootServiceBase<MmsQuestionnaire> implements MmsQuestionnaireService {

    @Inject
    private MmsQuestionnaireQuestionService mmsQuestionnaireQuestionService;
    @Inject
    private MmsQuestionnaireQuestionOptionService mmsQuestionnaireQuestionOptionService;

    @Override
    public Page<MmsQuestionnaire> pageList(int pageNumber,int pageSize,MmsQuestionnaire questionnaire){
        String sql="from mms_questionnaire where del_flag='0' ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(questionnaire.getName())){
            sql+=" and `name` like concat('%',?,'%') ";
            params.add(questionnaire.getName());
        }
        sql+=" order by sort ";
        return DAO.paginate(pageNumber,pageSize,"select * ",sql,params.toArray());
    }

    @Override
    public boolean saveQuestionnaire(MmsQuestionnaire questionnaire,String userId){
        boolean flag=false;
        if(StrKit.isBlank(questionnaire.getId())){
            questionnaire.setId(IdGen.getUUID());
            questionnaire.setDelFlag("0");
            questionnaire.setCreateBy(userId);
            questionnaire.setCreateDate(new Date());
            questionnaire.setUpdateBy(userId);
            questionnaire.setUpdateDate(new Date());
            flag=questionnaire.save();
        }else{
            questionnaire.setUpdateBy(userId);
            questionnaire.setUpdateDate(new Date());
            flag=questionnaire.update();
        }
        return flag;
    }

    @Override
    public MmsQuestionnaire findQuestionnaire(String id){
        String sql="select id,name,remark from mms_questionnaire where id=?";
        MmsQuestionnaire questionnaire= DAO.findFirst(sql,id);
        if(questionnaire!=null){
            List<MmsQuestionnaireQuestion> questionnaireQuestionList=mmsQuestionnaireQuestionService.findListByQuestionnaireId(id);
            if(questionnaireQuestionList.size()>0){
                List<String> questionIds=new ArrayList<>();
                for(MmsQuestionnaireQuestion question:questionnaireQuestionList){
                    if("radio".equals(question.getType()) || "checkbox".equals(question.getType())){
                        questionIds.add(question.getId());
                    }
                }
                if(questionIds.size()>0){
                    List<MmsQuestionnaireQuestionOption> optionList=mmsQuestionnaireQuestionOptionService.findOptionListByQuestionIds(questionIds);
                    for(MmsQuestionnaireQuestion question:questionnaireQuestionList){
                        List<MmsQuestionnaireQuestionOption> questionOptions=new ArrayList<>();
                        for(MmsQuestionnaireQuestionOption option:optionList){
                            if(question.getId().equals(option.getQuestionId())){
                                questionOptions.add(option);
                            }
                        }
                        question.put("options",question);
                    }
                }
            }
            questionnaire.put("questions",questionnaireQuestionList);
        }
        return questionnaire;
    }
}
