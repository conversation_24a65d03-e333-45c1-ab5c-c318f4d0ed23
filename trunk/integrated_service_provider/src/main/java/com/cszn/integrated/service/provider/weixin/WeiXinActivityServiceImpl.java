package com.cszn.integrated.service.provider.weixin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.api.weixin.WeiXinActivityService;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import io.jboot.Jboot;
import io.jboot.aop.annotation.Bean;
import io.jboot.support.redis.JbootRedis;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.CookieStore;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.cookie.BasicClientCookie;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;

@Bean
public class WeiXinActivityServiceImpl implements WeiXinActivityService {


    @Inject
    private SmsSendRecordService smsSendRecordService;
    private static JbootRedis jbootRedis= Jboot.getRedis();

    @Override
    public String getAccessToken() {


        String str = HttpClientsUtils.get(Global.getWXAccessTokenUrl);
        str=str.replaceAll("\"","");
        /*JSONObject jsonObject = JSON.parseObject(str);
        if(jsonObject.containsKey("Type") && jsonObject.getIntValue("Type")==1){
            return jsonObject.getString("Data");
        }*/
        return str;
    }


    @Override
    public Map<String,Object> getQrcodeTicket(String strParam,int expireSeconds,String actionName) throws Exception{
        String accessToken=getAccessToken();

        Map<String,Object> result=new HashMap<>();
        result.put("flag",false);

        if(StrKit.isBlank(accessToken)){
            result.put("msg","获取accessToken失败");
            return result;
        }
        String url="https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token="+accessToken;
        //
        String jsonParamStr="{\"expire_seconds\": "+expireSeconds+", \"action_name\": \""+actionName+"\", \"action_info\": {\"scene\":{\"scene_str\": \""+"LuckyDraw,"+strParam+"\"}}}";
        String str=HttpClientsUtils.httpPostRaw(url,jsonParamStr,null,null);
        System.out.println("创建二维码接口参数："+jsonParamStr);
        System.out.println("创建二维码接口响应："+str);
        JSONObject jsonObject=JSON.parseObject(str);
        if(jsonObject.containsKey("ticket")){
            result.put("flag",true);

            result.put("ticket",URLEncoder.encode(jsonObject.getString("ticket"),"UTF-8"));
            return result;
        }
        result.put("msg","获取ticket失败");
        return result;
    }


    public static File inputStreamToFile(InputStream ins, String name) throws Exception{
        File file = new File(System.getProperty("java.io.tmpdir") + File.separator + name);
        if (file.exists()) {
            return file;
        }
        OutputStream os = new FileOutputStream(file);
        int bytesRead;
        int len = 8192;
        byte[] buffer = new byte[len];
        while ((bytesRead = ins.read(buffer, 0, len)) != -1) {
            os.write(buffer, 0, bytesRead);
        }
        os.close();
        ins.close();
        return file;
    }

    @Override
    public File getQrcodeImgFile(String ticket){
        try {
            String destUrl = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket="+ticket;
            HttpURLConnection httpUrl = (HttpURLConnection) new URL(destUrl).openConnection();
            httpUrl.connect();
            File file = inputStreamToFile(httpUrl.getInputStream(), IdGen.getUUID() +".png");
            httpUrl.disconnect();
            return file;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public Map<String,Object> sendRandomCode(String phone){
        Map<String,Object> result=new HashMap<>();
        result.put("flag",false);
        //判断发送次数：格式key前缀_日期_时间号码 value次数:最后一次发送的时间戳
        //key：send_verify_20210101_13333333333,value：1:1624503405535
        String sendVerifyValue=jbootRedis.get("send_verify_"+DateUtils.getDate("yyyyMMdd")+"_"+phone);
        int sendCount=0;
        if(StrKit.notBlank(sendVerifyValue)){
            //截取发送次数
            sendCount=Integer.valueOf(sendVerifyValue.substring(0,sendVerifyValue.indexOf(":")));
            //截取最后一次发送时间戳
            long lastSendTime=Long.valueOf(sendVerifyValue.substring(sendVerifyValue.indexOf(":")+1,sendVerifyValue.length()));
            //获取当前时间戳
            Long startTs = System.currentTimeMillis();
            long differ=startTs-lastSendTime;
            if(differ<(1000*60)){
                result.put("msg","操作过于频繁");
                return result;
            }
            if(sendCount>10){
                result.put("msg","今天发送短信次数已上限");
                return result;
            }
            System.out.println("发送次数"+sendCount);
            System.out.println("发送时间戳"+lastSendTime);

        }
        String randomCode=randomCode(6);
        String flagStr=jbootRedis.setex("login_code_"+phone,60*10, randomCode);
        sendCount++;
        //限制设置24小时后过期
        jbootRedis.setex("send_verify_"+DateUtils.getDate("yyyyMMdd")+"_"+phone,60*60*24,sendCount+":"+System.currentTimeMillis());
        if("OK".equals(flagStr)){
            boolean flag=smsSendRecordService.sendMsgByPrize(SendType.phoneCheckCode,null,phone,"{\"tt\":\""+randomCode+"\"}",null);
            if(!flag){
                jbootRedis.del("login_code_"+phone);
            }
            result.put("flag",true);
            result.put("code",randomCode);
            return result;
        }
        return result;
    }

    @Override
    public boolean checkCode(String phone,String randomCode){
        boolean flag=false;
        String redisRandomCode=jbootRedis.get("login_code_"+phone);
        if(StrKit.isBlank(redisRandomCode)){
            return false;
        }

        return randomCode.equals(redisRandomCode);
    }

    public static String randomCode(int digits) {
        StringBuilder str = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < digits; i++) {
            str.append(random.nextInt(10));
        }
        //如果全部是0重新生成
        if(Pattern.matches("^[0]*$", str.toString())){
            return randomCode(digits);
        }
        return str.toString();
    }

    public static void main(String[] args) throws Exception{

        CookieStore cookieStore = new BasicCookieStore();
        BasicClientCookie cookie = new BasicClientCookie("auid", "e59b9df5-333e-49a9-b22b-c51365a44b1b");
        cookie.setDomain("main.cncsgroup.com");   // 示意domain
        //cookie.setDomain("10.10.0.12");
        cookie.setPath("/");
        cookieStore.addCookie(cookie);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setDefaultCookieStore(cookieStore)
                .build();

        final HttpPost httpGet = new HttpPost("http://hrm.cncsgroup.com/employeeLeaveBalance/pageList");
        //final HttpPost httpGet = new HttpPost("http://10.10.0.12:8890/main/buildingRoomManage/saveRoom");
        List<NameValuePair> params=new ArrayList<>();
        params.add(new BasicNameValuePair("page","1"));
        params.add(new BasicNameValuePair("limit","1000"));
        params.add(new BasicNameValuePair("archiveStatus","incumbency"));
        params.add(new BasicNameValuePair("deptId","B97B6F94-140C-4C86-8BC1-6A2BCC86CA3A"));

        httpGet.setEntity(new UrlEncodedFormEntity(params));

        CloseableHttpResponse response = httpClient.execute(httpGet);
        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode > 299 || statusCode < 200) {
            throw new IOException("statusCode error : " + statusCode);
        }
        HttpEntity entity = response.getEntity();
        String responseStr = IOUtils.toString(entity.getContent());
        EntityUtils.consume(entity);
        response.close();

        System.out.println("房间"+responseStr);
        JSONObject res=JSON.parseObject(responseStr);

        File file=new File("D:\\a\\新建 XLSX 工作表.xlsx");
        InputStream inputStream=new FileInputStream(file);
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheetAt = workbook.getSheetAt(0);
        JSONArray data = res.getJSONArray("data");
        for (int i = 0; i < data.size(); i++) {
            JSONObject jsonObject=data.getJSONObject(i);
            XSSFRow row = sheetAt.createRow(i);
            row.createCell(1).setCellValue(jsonObject.getString("fullName"));
            row.createCell(0).setCellValue(jsonObject.getString("workNum"));
            row.createCell(2).setCellValue(jsonObject.getString("deptName"));
            row.createCell(3).setCellValue(jsonObject.getString("positionName"));
            row.createCell(4).setCellValue(jsonObject.getString("yearLeave"));
        }


        FileOutputStream excelFileOutPutStream = new FileOutputStream(file);
        workbook.write(excelFileOutPutStream);
        excelFileOutPutStream.flush();
        excelFileOutPutStream.close();
    }

}
