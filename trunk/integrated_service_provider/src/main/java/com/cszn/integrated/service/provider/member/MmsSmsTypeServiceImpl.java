package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsSmsTypeService;
import com.cszn.integrated.service.entity.member.MmsSmsType;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Bean
public class MmsSmsTypeServiceImpl extends JbootServiceBase<MmsSmsType> implements MmsSmsTypeService {

    @Override
    public Page<MmsSmsType> tablePage(int pageNumber, int pageSize, MmsSmsType type) {
        String sql="from mms_sms_type where del_flag='0' ";
        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(type.getTypeName())){
            sql+=" and type_name=? ";
            params.add(type.getTypeName());
        }

        return DAO.paginate(pageNumber,pageSize,"select * ",sql,params.toArray());
    }

    @Override
    public List<MmsSmsType> findMmsSmsType(){
        String sql="select * from mms_sms_type where del_flag='0' ";

        return DAO.find(sql);
    }

    @Override
    public boolean saveType(MmsSmsType type,String userId){
        boolean flag=false;
        if(StrKit.isBlank(type.getId())){
            type.setId(IdGen.getUUID());
            type.setDelFlag("0");
            type.setCreateBy(userId);
            type.setCreateDate(new Date());
            type.setUpdateBy(userId);
            type.setUpdateDate(new Date());
            flag=type.save();
        }else{
            type.setUpdateBy(userId);
            type.setUpdateDate(new Date());
            flag=type.update();
        }
        return flag;
    }
}
