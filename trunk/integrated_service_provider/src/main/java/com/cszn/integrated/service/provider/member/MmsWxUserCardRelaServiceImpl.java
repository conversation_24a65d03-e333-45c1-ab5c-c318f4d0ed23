package com.cszn.integrated.service.provider.member;

import com.cszn.integrated.service.api.member.MmsWxUserCardRelaService;
import com.cszn.integrated.service.entity.member.MmsWxUserCardRela;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

@Bean
public class MmsWxUserCardRelaServiceImpl extends JbootServiceBase<MmsWxUserCardRela> implements MmsWxUserCardRelaService {

    public List<MmsWxUserCardRela> find(String sql, Object...params){
        return DAO.find(sql,params);
    }

    @Override
    public List<MmsWxUserCardRela> getListByUserId(String userId) {
        return DAO.find("SELECT * FROM mms_wx_user_card_rela WHERE user_id = ? and del_flag='0'", userId);
    }

    @Override
    public MmsWxUserCardRela getRelaByUserIdAndCardId(String userId, String cardId) {
        return DAO.findFirst("select * from mms_wx_user_card_rela where user_id = ? and card_id = ? and del_flag='0'",userId,cardId);
    }


    @Override
    public boolean untyingUserCard(String userId, String[] arr) {
        return Db.tx(new IAtom(){
            public boolean run() throws SQLException {
                try {
                    for (String item : arr) {
                        MmsWxUserCardRela rela = getRelaByUserIdAndCardId(userId,item);
                        if(rela == null){
                            return false;
                        }
                        rela.setDelFlag("1");
                        rela.setUntieTime(new Date());
                        rela.update();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
                return true;
            }
        });
    }




    @Override
    public List<MmsWxUserCardRela> getUserCardByCardId(String cardId) {
        return DAO.find("select * from mms_wx_user_card_rela where card_id = ? and del_flag='0'",cardId);
    }


}