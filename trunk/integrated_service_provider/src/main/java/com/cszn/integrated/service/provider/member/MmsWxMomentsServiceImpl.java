package com.cszn.integrated.service.provider.member;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsWxMomentsService;
import com.cszn.integrated.service.api.member.MmsWxRepliesService;
import com.cszn.integrated.service.api.member.MmsWxSubscriptionsService;
import com.cszn.integrated.service.api.member.MmsWxThumbsupService;
import com.cszn.integrated.service.api.member.MmsWxUserService;
import com.cszn.integrated.service.entity.member.MmsWxMoments;
import com.cszn.integrated.service.entity.member.MmsWxSubscriptions;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

/**
 * 微信图文信息
 */
@Bean
public class MmsWxMomentsServiceImpl extends JbootServiceBase<MmsWxMoments> implements MmsWxMomentsService {

    @Inject
    MmsWxSubscriptionsService mmsWxSubscriptionsService;

    @Inject
    MmsWxUserService mmsWxUserService;


    @Inject
    MmsWxRepliesService mmsWxRepliesService;

    @Inject
    MmsWxThumbsupService mmsWxThumbsupService;


    public Page<Object> getWxMomentsList(Integer pageNumber, Integer pageSize, String userId) {

        List<Object> param = new ArrayList<>();
        String sql = "FROM mms_wx_moments WHERE del_flag = 0 AND user_id = ? ORDER BY create_time DESC";
        param.add(userId);

        Page<MmsWxMoments> page = DAO.paginate(pageNumber, pageSize, "SELECT * ", sql, param.toArray());
        if (page == null || page.getList() == null || page.getList().size() <= 0) {
            return null;
        } else {
            //处理时间格式
            JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
            String jsonStr = JSON.toJSONString(page.getList(), SerializerFeature.WriteDateUseDateFormat);
            JSONArray array = JSONArray.parseArray(jsonStr);
            for (Object item : array) {
                JSONObject jsonObj = (JSONObject) JSON.toJSON(item);
                if (jsonObj != null) {
                    //处理图片
                    if (StringUtils.isBlank(jsonObj.getString("images"))) {
                        jsonObj.put("images", new JSONArray());
                    } else {
                        jsonObj.put("images", JSONArray.parseArray(jsonObj.get("images").toString()));
                    }
                    //处理用户
                    if(StringUtils.isNotBlank(jsonObj.getString("userId"))){
                        jsonObj.put("wxUser",mmsWxUserService.findById(jsonObj.getString("userId")));
                    }
                    if(StringUtils.isNotBlank(jsonObj.getString("id"))){
                        //处理评论
                        jsonObj.put("replies",mmsWxRepliesService.getReplies(jsonObj.getString("id")));
                        //处理点赞
                        jsonObj.put("thumbsup",mmsWxThumbsupService.getThumbsup(jsonObj.getString("id")));
                    }
                }
            }
            Page<Object> pageArray = new Page<>();
            pageArray.setList(array);
            pageArray.setPageNumber(page.getPageNumber());
            pageArray.setPageSize(page.getPageSize());
            pageArray.setTotalPage(page.getTotalPage());
            pageArray.setTotalRow(page.getTotalRow());
            return pageArray;
        }
    }

    public String saveMmsWxMoments(MmsWxMoments wxMoments){
        wxMoments.setId(IdGen.getUUID());
        wxMoments.setDelFlag("0");
        wxMoments.setCreateTime(new Date());
        wxMoments.setUpdateTime(new Date());
        boolean flag = wxMoments.save();
        if(flag){
            return wxMoments.getId();
        }else{
            return null;
        }
    }


    public JSONArray getSubMoments(String userId){

        List<Object> param = new ArrayList<>();

        //获取关注的人
        String sqlSub = "SELECT * FROM mms_wx_subscriptions ";
        if(StrKit.notBlank(userId)){
            sqlSub += "WHERE user_id = ?";
            param.add(userId);
        }

        List<MmsWxSubscriptions> subs = mmsWxSubscriptionsService.find(sqlSub,param.toArray());
        if(subs == null || subs.size() <= 0) return new JSONArray();

        //获取关注的人的图文信息
        List<String> ids = new ArrayList<>();
        String p="";
        for(int i = 0;i < subs.size();i++){
            if(i==subs.size()-1){
                p+="?";
            }else{
                p+="?,";
            }
            ids.add(subs.get(i).getSubscriberId());
        }

        String sqlMo = "SELECT * FROM mms_wx_moments WHERE user_id in ("+p+") ORDER BY create_time DESC";

        List<MmsWxMoments> list = DAO.find(sqlMo,ids.toArray());
        if(list == null || list.size() <= 0) return new JSONArray();
        //处理时间格式
        JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        String jsonStr = JSON.toJSONString(list, SerializerFeature.WriteDateUseDateFormat);
        JSONArray array = JSONArray.parseArray(jsonStr);
        for (Object item : array) {
            JSONObject jsonObj = (JSONObject) JSON.toJSON(item);
            if (jsonObj != null) {
                //处理图片
                if (StringUtils.isBlank(jsonObj.getString("images"))) {
                    jsonObj.put("images", new JSONArray());
                } else {
                    jsonObj.put("images", JSONArray.parseArray(jsonObj.get("images").toString()));
                }
                //处理用户
                if(StringUtils.isNotBlank(jsonObj.getString("userId"))){
                    jsonObj.put("wxUser",mmsWxUserService.findById(jsonObj.getString("userId")));
                }
                if(StringUtils.isNotBlank(jsonObj.getString("id"))){
                    //处理评论
                    jsonObj.put("replies",mmsWxRepliesService.getReplies(jsonObj.getString("id")));
                    //处理点赞
                    jsonObj.put("thumbsup",mmsWxThumbsupService.getThumbsup(jsonObj.getString("id")));
                }
            }
        }
        return array;
    }

    public Long getMomentCount(String userId){
        String sql = "SELECT COUNT(*) FROM mms_wx_moments WHERE del_flag = 0 AND user_id = ?";
        return Db.queryLong(sql,userId);
    }




    @Override
    public Record getMomentById(String momentId) {
        String sql = "SELECT wm.id, wm.user_id AS userId, wm.content, wm.images, wm.del_flag AS delFlag, " +
                "wm.create_time AS createTime, wm.update_time AS updateTime,wu.nick_name AS nickName " +
                "FROM mms_wx_moments wm " +
                "LEFT JOIN mms_wx_user wu " +
                "ON wm.user_id = wu.id " +
                "WHERE wm.id = ?";
        return Db.findFirst(sql,momentId);
    }


}