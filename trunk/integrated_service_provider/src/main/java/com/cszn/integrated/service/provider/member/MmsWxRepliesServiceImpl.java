package com.cszn.integrated.service.provider.member;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.member.MmsWxMomentsService;
import com.cszn.integrated.service.api.member.MmsWxRepliesService;
import com.cszn.integrated.service.entity.member.MmsWxMoments;
import com.cszn.integrated.service.entity.member.MmsWxReplies;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;

/**
 *微信评论
 */
@Bean
public class MmsWxRepliesServiceImpl extends JbootServiceBase<MmsWxReplies> implements MmsWxRepliesService {

    @Inject
    MmsWxMomentsService mmsWxMomentsService;

    public String saveMmsWxReplies(MmsWxReplies wxReplies){
        //查询图文是否存在
        if(StringUtils.isBlank(wxReplies.getMomentId())) return "";
        MmsWxMoments mo = mmsWxMomentsService.findById(wxReplies.getMomentId());
        if(mo == null) return "";

        wxReplies.setId(IdGen.getUUID());
        wxReplies.setDelFlag("0");
        wxReplies.setCreateTime(new Date());
        wxReplies.setUpdateTime(new Date());
        if(wxReplies.save()){
            return "suc";
        }else{
            return null;
        }
    }

    public Long getReplieCount(String userId){
        String sql = "SELECT COUNT(*) FROM mms_wx_replies WHERE del_flag = 0 AND user_id = ?";
        return Db.queryLong(sql,userId);
    }



    @Override
    public List<Record> getReplies(String momentId) {
        String sql = "SELECT rs.id, rs.user_id AS userId, rs.moment_id AS momentId, rs.to_user AS toUser, " +
                "rs.content, rs.del_flag AS delFlag, rs.create_time AS createTime, rs.update_time AS updateTime," +
                "wu.nick_name AS nickName,wr.nick_name AS toUserName FROM mms_wx_replies rs " +
                "LEFT JOIN mms_wx_user wu " +
                "ON rs.user_id = wu.id " +
                "LEFT JOIN mms_wx_user wr " +
                "ON rs.to_user = wr.id " +
                "WHERE rs.del_flag = 0 AND rs.moment_id = ? " +
                "ORDER BY rs.create_time ASC";
        return Db.find(sql,momentId);
    }
}