package com.cszn.integrated.service.provider.member;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.service.api.member.MmsMeasureStepsService;
import com.cszn.integrated.service.entity.member.MmsMeasureSteps;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import io.jboot.service.JbootServiceBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/6/28
 **/
@Bean
public class MmsMeasureStepsServiceImpl extends JbootServiceBase<MmsMeasureSteps> implements MmsMeasureStepsService {

    @Override
    public Map<String, List> memberSteps(String cardNumber, String type) {

        Map<String, List> map=new HashMap<>();
        String sql="";
        if(type.equals("day")){
            sql="select concat(DATE_FORMAT(measure_time,'%H'),':00') as time,measure_steps_value as steps,measure_milage_value as milage,measure_calorie_value as calorie from mms_measure_steps where id in (" +
                    "select min(s.id) from mms_measure_steps s left join mms_card_equipment e on s.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=s.member_id " +
                    "where c.card_number=? and e.del_flag='0' and TO_DAYS(s.measure_time)=TO_DAYS(now()) GROUP BY DATE_FORMAT(s.measure_time,'%Y%m%d%H')) order by measure_time";
        }else if(type.equals("week")){
            sql="select DATE_FORMAT(measure_time,'%m-%d') as time,measure_steps_value as steps,measure_milage_value as milage,measure_calorie_value as calorie from mms_measure_steps where id in ( " +
                    "select min(s.id) from mms_measure_steps s left join mms_card_equipment e on s.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=s.member_id " +
                    "where c.card_number=? and e.del_flag='0' and YEARWEEK(s.measure_time)=YEARWEEK(now()) GROUP BY DATE_FORMAT(s.measure_time,'%Y%m%d')) order by measure_time ";
        }else if(type.equals("month")){
            sql="select DATE_FORMAT(measure_time,'%m-%d') as time,measure_steps_value as steps,measure_milage_value as milage,measure_calorie_value as calorie from mms_measure_steps where id in (" +
                    "select min(s.id) from mms_measure_steps s left join mms_card_equipment e on s.equipment_no=e.equipment_no left join fina_membership_card c on c.id=e.card_id and c.member_id=s.member_id " +
                    "where c.card_number=? and e.del_flag='0' and DATE_FORMAT(s.measure_time,'%Y%m')=DATE_FORMAT(now(),'%Y%m') GROUP BY DATE_FORMAT(s.measure_time,'%Y%m%d')) order by measure_time";
        }
        if(StrKit.notBlank(sql)){
            List<Record> recordList=Db.find(sql,cardNumber);
            if(recordList==null){
                return map;
            }
            List<String> timeList=new ArrayList<>();
            List<Integer> stepsList=new ArrayList<>();
            List<Integer> milageTipsList=new ArrayList<>();
            List<Integer> calorieList=new ArrayList<>();
            for (Record record:recordList){
                timeList.add(record.getStr("time"));
                stepsList.add(Integer.valueOf(record.getStr("steps")));
                milageTipsList.add(Integer.valueOf(record.getStr("milage")));
                calorieList.add(Integer.valueOf(record.getStr("calorie")));
            }
            map.put("time",timeList);
            map.put("steps",stepsList);
            map.put("milage",milageTipsList);
            map.put("calorie",calorieList);
        }
        return map;
    }

    @Override
    public Map<String, Object> latestSteps(String cardNumber) {
        Map<String, Object> map=new HashMap<>();
        String sql="select s.measure_steps_value as steps,s.measure_milage_value as milage,s.measure_calorie_value as calorie from mms_measure_steps s  " +
                "left join mms_card_equipment e on s.equipment_no=e.equipment_no " +
                "left join fina_membership_card c on c.id=e.card_id and c.member_id=s.member_id " +
                "where c.card_number=? and e.del_flag='0' order by s.create_date desc limit 1";
        Record record=Db.findFirst(sql,cardNumber);
        if(record!=null){
            map= record.getColumns();
        }
        return map;
    }

    @Override
    public Map<String, Object> getStepsData(String memberId, String dataRecord) {
        Map<String,Object> stepsMap = new HashMap<>();
        String sql = "select measure_steps_value as measureStepsValue,measure_calorie_value as measureCalorieValue,measure_milage_value as measureMilageValue,measure_time AS measureTime " +
                "from mms_measure_steps where del_flag = 0 and measure_steps_value is not null and measure_calorie_value is not null and measure_milage_value is not null and measure_time is not null " +
                "and measure_steps_value != 0 and measure_calorie_value != 0 and measure_milage_value != 0 and member_id = ? ";

        if("tenRecord".equals(dataRecord)){
            List<Record> stepsList = Db.find(sql + "order by measure_time desc limit 10",memberId);
            return setValues(stepsList);
        }else if("7DayRecord".equals(dataRecord)){
            List<Record> stepsList = Db.find(sql + "and DATE_SUB(CURDATE(), INTERVAL 7 DAY) <= date(measure_time) order by measure_time desc",memberId);
            return setValues(stepsList);
        }else if("30DayRecord".equals(dataRecord)){
            List<Record> stepsList = Db.find(sql + "and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <= date(measure_time) order by measure_time desc",memberId);
            return setValues(stepsList);
        }else if("monRecord".equals(dataRecord)){
            List<Record> stepsList = Db.find(sql + "and DATE_FORMAT( measure_time, '%Y%m' ) = DATE_FORMAT( CURDATE() , '%Y%m' ) order by measure_time desc",memberId);
            return setValues(stepsList);
        }
        return stepsMap;
    }



    public Map<String,Object> setValues(List<Record> recordList){
        Map<String,Object> stepsMap = new HashMap<>();
        JSONObject stepsJson = new JSONObject();
        JSONArray stepsArray = new JSONArray();
        JSONObject calorieJson = new JSONObject();
        JSONArray calorieArray = new JSONArray();
        JSONObject milageJson = new JSONObject();
        JSONArray milageArray = new JSONArray();
        JSONArray measureJsonData = new JSONArray();
        JSONArray timeArray = new JSONArray();

        if(recordList != null && recordList.size() > 0) {
            for (int i = recordList.size() - 1; i >= 0; i--) {
                stepsArray.add(Long.parseLong(recordList.get(i).get("measureStepsValue")));
                calorieArray.add(Long.parseLong(recordList.get(i).get("measureCalorieValue")));
                milageArray.add(Long.parseLong(recordList.get(i).get("measureMilageValue")));
                timeArray.add(recordList.get(i).get("measureTime"));
            }
        }
        stepsJson.put("data",stepsArray);
        stepsJson.put("name","步数");
        JSONObject tipStep = new JSONObject();//小气泡
        tipStep.put("valueSuffix","(步)");
        stepsJson.put("tooltip",tipStep);

        calorieJson.put("data",calorieArray);
        calorieJson.put("name","卡路里");
        JSONObject tipCal = new JSONObject();//小气泡
        tipCal.put("valueSuffix","(cal)");
        calorieJson.put("tooltip",tipCal);

        milageJson.put("data",milageArray);
        milageJson.put("name","里程");
        JSONObject tipKm = new JSONObject();//小气泡
        tipKm.put("valueSuffix","(m)");
        milageJson.put("tooltip",tipKm);

        measureJsonData.add(stepsJson);
        measureJsonData.add(calorieJson);
        measureJsonData.add(milageJson);
        stepsMap.put("measureJsonData",measureJsonData);
        stepsMap.put("measureTimeArray",timeArray);
        return stepsMap;
    }


}
