#---------------------------------------------------------------------------------#
# app info
jboot.admin.app.name=MDM\u4E3B\u6570\u636E\u7BA1\u7406
jboot.admin.app.org=cszn
jboot.admin.app.orgWebsite=www.cszn.com
jboot.admin.app.resourceHost
jboot.admin.app.copyRight=\u5E7F\u5DDE\u5E02\u660C\u677E\u667A\u80FD\u79D1\u6280\u5F00\u53D1\u6709\u9650\u516C\u53F8 All rights reserved
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot\u7684\u5F00\u53D1\u6A21\u5F0F
jboot.mode=dev
jboot.bannerEnable=true
jboot.bannerFile=banner.txt
jboot.cron4jEnable=false
jboot.cron4jFile=cron4j.properties
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#type = local (support:local,motan,dubbo)
#use local
jboot.rpc.type = local
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# mysql config
jboot.datasource.type=mysql
jboot.datasource.url=****************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
jboot.datasource.maximumPoolSize = 5
jboot.datasource.sqlTemplatePath=
jboot.datasource.sqlTemplate=
jboot.datasource.table=
jboot.datasource.excludeTable=
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
jboot.model.idCacheEnable=false
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# cache config : type default ehcache (support:ehcache,redis,ehredis)
#jboot.cache.type=redis
#jboot.cache.redis.host=127.0.0.1
#jboot.cache.redis.password=123456
#jboot.cache.redis.database=0
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# jwt config
jboot.web.jwt.httpHeaderName=jwttoken
jboot.web.jwt.secret=21934e3ccf3c19a6c3299cbc2e33b2d848e6ef9ea4e484b19b07bea82888a7a7
# 60 * 60 * 10
jboot.web.jwt.validityPeriod=36000
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# shiro config
jboot.shiro.ini = shiro.ini
jboot.shiro.loginUrl=/login
jboot.shiro.successUrl
jboot.shiro.unauthorizedUrl=/login
#---------------------------------------------------------------------------------#

uploadPath=E:\\virtualDir\\filePath.war\\
excelTemplatePath=E:\\virtualDir\\filePath.war\\excelTemplate\\cardExcel.xls
fileUrlPrefix=http://127.0.0.1:8890/

#\u673A\u6784\u517B\u8001\u7CFB\u7EDF\u57FA\u5730id
orgBaseId=27a558b2-c205-4937-b6f4-fed1fa68ac55
#\u673A\u6784\u517B\u8001\u7CFB\u7EDF\u5E94\u7528\u53F7
orgAppNo=CS10002
#\u673A\u6784\u540C\u6B65\u5E8A\u4F4D\u52A8\u6001\u5730\u5740
orgBedDynamicUrl=http://localhost:8280/pension/csapi/saveSojournBedBookRecord

#\u65C5\u5C45\u7CFB\u7EDF\u5E94\u7528\u53F7
sojournAppNo=CS10001
#\u65C5\u5C45\u540C\u6B65\u5E8A\u4F4D\u52A8\u6001\u5730\u5740
sojournBedDynamicUrl=http://*************:25144/Base/SyncBedLongStay
#sojournBedDynamicUrl=http://************:8890/api/test
#WMS\u7CFB\u7EDF\u5E94\u7528\u53F7
wmsAppNo=9100003

fileUploadUrl=http://*************:8885/api/upload
orgUrl=http://org.csitd.com
bmpUrl=http://bmp.csitd.com