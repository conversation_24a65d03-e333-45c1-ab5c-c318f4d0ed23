#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()
<style>

    td[data-field="reuseDegree"] .layui-form-select {
        margin-top: -10px;
        margin-left: -15px;
        margin-right: -15px;
    }

    td[data-field="reuseDegree"] .layui-table-cell {
        overflow: visible !important;
    }

    /*.layui-table td, .layui-table th {
        position: relative;
        padding: 9px 3px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }*/
    .layui-table-cell {
        padding: 0 3px;
    }
</style>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 0px;" id="roomTypeForm">
<div class="layui-row" style="padding: 0px 10px;">
    <div class="layui-col-xs6" id="contentDiv2" style="padding: 10px 10px;">

        <div class="demoTable layui-row">
            <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" >
                问题类型:
                <div class="layui-inline" style="width: 300px;">
                    <div id="deptSelect" >

                    </div>
                </div>


                问题名称:
                <div class="layui-inline">
                    <input id="name" name="name" class="layui-input">
                </div>
                &nbsp;&nbsp;
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            </form>

        </div>
        <table id="roomTypeTable" lay-filter="roomTypeTable"></table>


    </div>
    <div class="layui-col-xs6" id="contentDiv" style="padding: 10px 5px;">
       <div class="layui-row layui-form" style="padding-top: 40px;">


           <table id="roomTypeTable2" lay-filter="roomTypeTable2"></table>
       </div>

    </div>
    <div class="layui-form-footer" >
        <div class="pull-right">

            <input type="hidden" id="schemeId" value="#(id??)"/>
            <input type="hidden" id="fileId" name="roomType.fileId" value="#(roomType.fileId??)"/>
            <input type="hidden" id="optionCount" name="optionCount" value="#if(model==null)0#else#(model.allOptions.size()??0)#end" >
            <button class="layui-btn" type="button" id="saveSort">保存排序</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
</div>
#end

#define js()
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="add">添加</a>
</script>

<script type="text/html" id="actionBar2">

    #[[
    {{#if(d.isEnabled=='1'){}}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">禁用</a>
    {{#}}}

    {{#if(d.isEnabled=='0'){}}
    <a class="layui-btn layui-btn-xs" lay-event="enable">启用</a>
    {{#}}}
    ]]#
</script>
<script type="text/javascript">
    layui.use(['form','jquery','upload','table'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload,table=layui.table;

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                //expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/mainExamine/typeXSSelectTree',{},function (res) {
            deptSelect.update({
                data:res
            })
        });



        roomTypeLoad({"schemeId":"#(id??)"});

        sd=form.on("submit(search)",function(data){
            reloadTable();
            return false;
        });


        reloadTable=function() {

            var array=deptSelect.getValue();
            var problemTypeId="";
            if(array.length>0){
                problemTypeId=array[0].id;
            }

            roomTypeLoad({"name":$("#name").val(),"problemTypeId":problemTypeId,"schemeId":$("#schemeId").val()});
            return false;
        }

        function roomTypeLoad(data){
            table.render({
                id : 'roomTypeTable'
                ,elem : '#roomTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 100000
                ,url : '#(ctxPath)/mainExamine/problemPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'sort', title: '问题类型名称', align: 'center', unresize: true,templet: function (d){
                            return d.problemType.name;
                        }}
                    ,{field:'name', title: '问题标题', align: 'center', unresize: true}
                    ,{field:'type', title: '问题填写类型', align: 'center', unresize: true,templet: function (d){
                            if(d.type=='text'){
                                return '文本'
                            }else if(d.type=='radio'){
                                return '单选'
                            }else if(d.type=='checkbox'){
                                return '多选'
                            }
                        }}
                    ,{field:'isEnable', title: '是否必填', align: 'center', unresize: true,templet:"<div>{{ d.isRequired=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isRequired=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                }
            });
        };


        roomTypeLoad2({"schemeId":$("#schemeId").val()});
        reloadTable2=function() {
            roomTypeLoad2({"schemeId":$("#schemeId").val()});
            return false;
        }

        function roomTypeLoad2(data){
            table.render({
                id : 'roomTypeTable2'
                ,elem : '#roomTypeTable2'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 100000
                ,url : '#(ctxPath)/mainExamine/findSchemeProblemList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'typeName', title: '类型名称', align: 'center', unresize: true}
                    ,{field:'problemName', title: '问题标题', align: 'center', unresize: true}
                    ,{field:'type', title: '填写类型', align: 'center', unresize: true,templet: function (d){
                            if(d.type=='text'){
                                return '文本'
                            }else if(d.type=='radio'){
                                return '单选'
                            }else if(d.type=='checkbox'){
                                return '多选'
                            }
                        }}
                    ,{field:'sort', title: '排序', align: 'center', unresize: false,templet:function(d){
                        return '<input type="text" class="layui-input sortInput" data-id="'+d.id+'" value="'+d.sort+'" maxlength="7" autocomplete="off">';
                    }}
                    #if(scheme.isWeightedMean??=='1')
                    ,{field: 'reuseDegree',title: '加权平均类型',width:120, templet:function(d){

                            return '<div id="XM-' + d.id + '" ></div>';
                        }}
                    #end
                    ,{field:'isRequired', title: '是否必填', align: 'center', unresize: true,templet:"<div>{{ d.isRequired=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isRequired=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}

                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar2'}
                ]]
                ,page : false
                ,done:function (a,b,c) {

                    var layerTips;
                    $("td").on("mouseenter", function() {
                        var dataField=$(this).attr("data-field");
                        if("reuseDegree"==dataField || "sort"==dataField){
                            return false;
                        }
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        /*if("reuseDegree"==dataField || "sort"==dataField){
                            return false;
                        }*/
                        layer.close(layerTips);
                    });
                    /*$('.layui-form-select').each(function(){
                        console.log(1);
                        $(this).on('click', function(e) {
                            console.log(12);
                            e.stopPropagation();

                            // 其他处理逻辑
                        });
                    });*/

                    //渲染多选
                    a.data.forEach(item =>  {
                        var xm = xmSelect.render({
                            el: '#XM-' + item.id,
                            autoRow: true,
                            model: { type: 'fixed' },
                            clickClose:true,
                            radio: true,
                            model: {
                                icon: 'hidden',
                                label: {
                                    type: 'text'
                                }
                            },
                            initValue: [item.weightedMeanConfigId],
                            data: [
                                #for(weightedMeanConfig : weightedMeanConfigList)
                                    {name: '#(weightedMeanConfig.name??)', value: '#(weightedMeanConfig.id??)'},
                                #end
                            ],
                            on: function(data){
                                //arr:  当前多选已选中的数据
                                var arr = data.arr;
                                //change, 此次选择变化的数据,数组
                                var change = data.change;
                                //isAdd, 此次操作是新增还是删除
                                var isAdd = data.isAdd;
                                if(isAdd){
                                    util.sendAjax ({
                                        type: 'POST',
                                        url: '#(ctxPath)/mainExamine/schemeProblemWeightedMeanConfigSave',
                                        notice: true,
                                        data: {'id':item.id,"configId":change[0].value},
                                        loadFlag: true,
                                        success : function(rep){
                                            if(rep.state=='ok'){

                                            }
                                        },
                                        complete : function() {
                                            //layer.closeAll();

                                        }
                                    });
                                }

                            }
                        });

                        item.__xm = xm;
                    })

                }
            });
        };

        form.on('select(reuseDegreeFileter)',function (obj) {
            //console.log(obj);
            //console.log($(obj.elem).attr("data-id"));
            //console.log($(obj).attr("data-id")+"________________"+obj.value);
            var relId=$(obj.elem).attr("data-id");
            var weightedMeanConfigId=obj.value;
            if(weightedMeanConfigId!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/schemeProblemWeightedMeanConfigSave',
                    notice: true,
                    data: {'id':relId,"configId":weightedMeanConfigId},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){

                        }
                    },
                    complete : function() {
                        //layer.closeAll();

                    }
                });

            }else{
                layer.msg('请选择正常选项',{icon:5});
            }
        })

        table.on('tool(roomTypeTable)',function(obj){
            if(obj.event==='add'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/schemeProblemSave',
                    notice: true,
                    data: {'problemId':obj.data.id,"id":$("#schemeId").val()},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            reloadTable();
                            reloadTable2();
                        }
                    },
                    complete : function() {
                        layer.closeAll();

                    }
                });
            }
        });

        table.on('tool(roomTypeTable2)',function(obj){
            if(obj.event==='disable'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/schemeProblemRelSave',
                    notice: true,
                    data: {id:obj.data.id,'isEnabled':'0'},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            reloadTable2();
                        }
                    },
                    complete : function() {
                        layer.closeAll();

                    }
                });
            }else if(obj.event==='enable'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/schemeProblemRelSave ',
                    notice: true,
                    data: {id:obj.data.id,'isEnabled':'1'},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            reloadTable2();
                        }
                    },
                    complete : function() {
                        layer.closeAll();
                    }
                });
            }
        });






        $("#addOption").on('click',function () {
            var optionCount=$("#optionCount").val();
            optionCount=Number(optionCount)+1;

            $("#contentDiv").append('<div class="layui-form-item class-option" id="class-option-'+optionCount+'" >\n' +
                '            <label class="layui-form-label">选项'+optionCount+'</label>\n' +
                '            <div class="layui-input-inline">\n' +
                '               <input type="text" name="option'+optionCount+'" class="layui-input" lay-verify="required" value="" placeholder="请输入选项内容"  autocomplete="off"> '+
                '            </div>' +
                '           <label class="layui-form-label">分数</label>\n' +
                '            <div class="layui-input-inline">\n' +
                '                <input type="text" name="score'+optionCount+'" class="layui-input" lay-verify="required|number" value="" placeholder="请输入选项分数" autocomplete="off">\n' +
                '            </div>' +
                '           <label class="layui-form-label">是否可用</label>\n' +
                '                        <div class="layui-input-inline">\n' +
                '                            <input type="radio" name="isEnabled'+optionCount+'" value="1" title="是" checked >\n' +
                '                            <input type="radio" name="isEnabled'+optionCount+'" value="0" title="否" >\n' +
                '                        </div>' +
                '           <button class="layui-btn layui-btn-xs layui-btn-danger" style="margin-top: 9px;" type="button" onclick="delOption('+optionCount+')" >删除</button>' +
                '        </div>');

            $("#optionCount").val(optionCount);
            form.render();
        })

        delOption=function(optionCount){
            console.log(optionCount);
            $('#class-option-'+optionCount).remove();

            return false;
        }

        form.on('select(type)',function (obj) {
            console.log(obj.value);
            if(obj.value=='text'){
                $("#addOption").css("display","none");
                $(".class-option").remove();
            }else{
                $("#addOption").css("display","inline-block");
            }
        })

        //保存
        form.on('submit(saveBtn)', function(){

            var url = "#(ctxPath)/mainExamine/schemeSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#roomTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


        $("#saveSort").on('click',function (){
            var saveFlag = true;
            var dataArray = new Array();
            $(".sortInput").each(function(i,obj){
                var orgId = $(this).attr('data-id');
                var orgSort = obj.value;
                if(orgSort==null || orgSort==''){
                    saveFlag = false;
                    return false;
                }else{
                    var inputObj = {'id':orgId, 'orgSort':orgSort};
                    dataArray.push(inputObj);
                }
            });
            if(saveFlag){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/schemeProblemRelSaveSort',
                    data: {sortDatas:JSON.stringify(dataArray)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            reloadTable2();
                        }
                    },
                    complete : function() {

                    }
                });
            }else{
                layer.msg('排序不能为空，请输入排序!',{icon:5});
            }

        })


    });
</script>
#end
