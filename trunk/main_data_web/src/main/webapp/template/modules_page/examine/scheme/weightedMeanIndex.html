#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        roomTypeLoad({'schemeId':$("#schemeId").val()});

        sd=form.on("submit(search)",function(data){
            roomTypeLoad(data.field);
            return false;
        });


        reloadTable=function() {
            roomTypeLoad({"schemeId":$("#schemeId").val()});
            return false;
        }

        function roomTypeLoad(data){
            table.render({
                id : 'roomTypeTable'
                ,elem : '#roomTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/mainExamine/weightedMeanConfigList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'name', title: '配置名称', align: 'center', unresize: true}
                    ,{field:'fullMark', title: '满分值', align: 'center', unresize: true}
                    ,{field:'weightedMeanValue', title: '加权平均值(%)', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 220, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : false
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mainExamine/weightedMeanForm?schemeId="+$("#schemeId").val() ;
            pop_show("新增加权积分配置",url,900,500);
        });

        table.on('tool(roomTypeTable)',function(obj){
            if(obj.event === 'edit'){
                var url = "#(ctxPath)/mainExamine/weightedMeanForm?id=" + obj.data.id+"&schemeId="+$("#schemeId").val();
                pop_show("编辑加权积分配置",url,900,500);
            }else if(obj.event==='del'){
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mainExamine/weightedMeanConfigSave',
                        notice: true,
                        data: {id:obj.data.id,'delFlag':'1'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                reloadTable();
                            }
                        },
                        complete : function() {
                            layer.closeAll();

                        }
                    });
                });
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('roomTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:roomType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <!--<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">

            方案名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            &nbsp;&nbsp;
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>-->
        #shiroHasPermission("main:roomType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        <input type="hidden" name="schemeId" id="schemeId" value="#(schemeId??)" >
    </div>
    <table id="roomTypeTable" lay-filter="roomTypeTable"></table>
</div>
#end