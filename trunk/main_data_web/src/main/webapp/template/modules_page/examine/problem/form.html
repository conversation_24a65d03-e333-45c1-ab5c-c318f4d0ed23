#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="roomTypeForm">
    <div class="layui-col-xs12" id="contentDiv" style="margin-bottom: 80px;">
        <input type="hidden" id="roomTypeId" name="id" value="#(model.id??)"/>


        <div class="layui-form-item">
            <label class="layui-form-label" style="    padding: 8px 5px;"><font color="red">*</font>问题类型</label>
            <div class="layui-input-block">
                <div id="deptSelect" >

                </div>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>问题内容</label>
            <div class="layui-input-block">
                <textarea id="name" name="name" placeholder="请输入问题内容" class="layui-textarea">#(model.name??)</textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>问题填写类型</label>
            <div class="layui-input-block">
                <select id="type" name="type" lay-filter="type">
                    #for(type : problemType)
                    <option value="#(type.key)" #if(model.type??==type.key)selected#end>#(type.value)</option>
                    #end
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>是否必填</label>
            <div class="layui-input-block">
                <input type="radio" name="isRequired" value="1" title="是" #if(model==null || model.isRequired??=="1") checked #end>
                <input type="radio" name="isRequired" value="0" title="否" #if(model.isRequired??=="0") checked #end>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>是否上传图片</label>
            <div class="layui-input-block">
                <input type="radio" name="isUpload" value="1" title="是" #if(model==null || model.isUpload??=="1") checked #end>
                <input type="radio" name="isUpload" value="0" title="否" #if(model.isUpload??=="0") checked #end>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;width: 150px;"><font color="red">*</font>是否开启描述文本框</label>
            <div class="layui-input-block">
                <input type="radio" name="isRemark" value="1" title="是" #if( model.isRemark??=="1") checked #end>
                <input type="radio" name="isRemark" value="0" title="否" #if(model==null || model.isRemark??=="0") checked #end>
            </div>
        </div>

        #if(model!=null)
            #for(option : model.allOptions)
        <div class="layui-form-item class-option" id="class-option-#(for.index+1)" >
                        <label class="layui-form-label">选项#(for.index+1)</label>
                        <div class="layui-input-inline">
                                <input value="#(option.id??)" type="hidden" id="optionId#(for.index+1)" name="optionId#(for.index+1)">
                               <input type="text" name="option#(for.index+1)" class="layui-input" lay-verify="required" value="#(option.content??)" placeholder="请输入选项内容"  autocomplete="off">
                            </div>
                        <label class="layui-form-label" style="width: 60px;">分数</label>
                        <div class="layui-input-inline" style="width: 60px;">
                                <input type="text" name="score#(for.index+1)" class="layui-input" lay-verify="required|number" value="#(option.score??)" placeholder="请输入选项分数" autocomplete="off">
                        </div>
                        <label class="layui-form-label" style="width: 90px;">是否可用</label>
                        <div class="layui-input-inline" style="width: 160px;">
                            <input type="radio" name="isEnabled#(for.index+1)" value="1" title="是" #if(option.is_enabled=="1") checked #end>
                            <input type="radio" name="isEnabled#(for.index+1)" value="0" title="否" #if(option.is_enabled=="0") checked #end>
                        </div>
                        <label class="layui-form-label" style="width: 90px;padding: 8px 5px;" >开启文本框</label>
                        <div class="layui-input-inline" style="width: 90px;">
                            <input type="checkbox" name="isOpenText#(for.index+1)" lay-skin="primary" title="开启" value="1" #if(option.is_open_text=="1") checked #end>
                        </div>
                        <label class="layui-form-label" style="width: 105px;">文本框标题</label>
                        <div class="layui-input-inline" style="width: 170px;">
                            <input type="text" name="textName#(for.index+1)" class="layui-input" lay-verify="" value="#(option.text_name??)" placeholder="请输入文本框标题" autocomplete="off">
                        </div>

                       <!--<button class="layui-btn layui-btn-xs layui-btn-danger" style="margin-top: 9px;" type="button" onclick="delOption(#(for.index+1))" >删除</button>-->
                    </div>
            #end
        #end


        <!--<div class="layui-form-item">
            <label class="layui-form-label">是否可用</label>
            <div class="layui-input-block">
                <input type="radio" name="sex" value="男" title="男">
                <input type="radio" name="sex" value="女" title="女" checked>
            </div>
        </div>-->
        <!--<div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="text" name="sort" class="layui-input" value="#(model.sort??)" placeholder="请输入排序">
            </div>
        </div>-->

    </div>

    <div class="layui-form-footer" >
        <div class="pull-right">

            <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
            <input type="hidden" id="fileId" name="roomType.fileId" value="#(roomType.fileId??)"/>
            <input type="hidden" id="optionCount" name="optionCount" value="#if(model==null)0#else#(model.allOptions.size()??0)#end" >
            <button class="layui-btn" type="button" id="addOption" #if(model.type??=='text') style="display: none;" #else style="display: inline-block;" #end >添加选项</button>
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;


        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                //expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/mainExamine/typeXSSelectTree',{},function (res) {
            deptSelect.update({
                data:res
            })
            #if(model!=null)
            deptSelect.setValue(["#(model.problemTypeId??)"]);
            #end
        });


        $("#addOption").on('click',function () {
            var optionCount=$("#optionCount").val();
            optionCount=Number(optionCount)+1;

            $("#contentDiv").append('<div class="layui-form-item class-option" id="class-option-'+optionCount+'" >\n' +
                '            <label class="layui-form-label">选项'+optionCount+'</label>\n' +
                '            <div class="layui-input-inline">\n' +
                '               <input type="text" name="option'+optionCount+'" class="layui-input" lay-verify="required" value="" placeholder="请输入选项内容"  autocomplete="off"> '+
                '            </div>' +
                '           <label class="layui-form-label" style="width: 60px;">分数</label>\n' +
                '            <div class="layui-input-inline" style="width: 60px;">\n' +
                '                <input type="text" name="score'+optionCount+'" class="layui-input" lay-verify="required|number" value="" placeholder="请输入选项分数" autocomplete="off">\n' +
                '            </div>' +
                '           <label class="layui-form-label" style="width: 90px;">是否可用</label>\n' +
                '                        <div class="layui-input-inline" style="width: 160px;">\n' +
                '                            <input type="radio" name="isEnabled'+optionCount+'" value="1" title="是" checked >\n' +
                '                            <input type="radio" name="isEnabled'+optionCount+'" value="0" title="否" >\n' +
                '                        </div>' +
                '<label class="layui-form-label" style="width: 90px;padding: 8px 5px;" >开启文本框</label>\n' +
                '                        <div class="layui-input-inline" style="width: 90px;">\n' +
                '                            <input type="checkbox" name="isOpenText'+optionCount+'" lay-skin="primary" title="开启" value="1" >\n' +
                '                        </div>\n' +
                '                        <label class="layui-form-label" style="width: 105px;">文本框标题</label>\n' +
                '                        <div class="layui-input-inline" style="width: 140px;">\n' +
                '                            <input type="text" name="textName'+optionCount+'" class="layui-input" lay-verify="" value="" placeholder="请输入文本框标题" autocomplete="off">\n' +
                '                        </div>' +
                '           <button class="layui-btn layui-btn-xs layui-btn-danger" style="margin-top: 9px;" type="button" onclick="delOption('+optionCount+')" >删除</button>' +
                '        </div>');

            $("#optionCount").val(optionCount);
            form.render();
        })

        delOption=function(optionCount){
            console.log(optionCount);
            $('#class-option-'+optionCount).remove();

            return false;
        }

        form.on('select(type)',function (obj) {
            console.log(obj.value);
            if(obj.value=='text'){
                $("#addOption").css("display","none");
                $(".class-option").remove();
            }else{
                $("#addOption").css("display","inline-block");
            }
        })

        //保存
        form.on('submit(saveBtn)', function(){

            if($("#type").val()!='text'){
                if($(".class-option").length==0){
                    layer.msg('请添加选项', {icon: 5});
                    return false;
                }
            }

            var array=deptSelect.getValue();
            var problemTypeId="";
            if(array.length>0){
                problemTypeId=array[0].id;
            }

            var url = "#(ctxPath)/mainExamine/problemSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#roomTypeForm").serialize()+"&problemTypeId="+problemTypeId,
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


    });
</script>
#end
