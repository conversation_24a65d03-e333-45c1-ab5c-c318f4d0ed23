#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<div class="layui-row layui-col-space10">
    <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>类型</legend>
            <div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
        </fieldset>
    </div>
    <div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
        <div style="margin-bottom: 20px;"></div>
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">上级类型</label>
                <div class="layui-input-block">
                    <input type="text" id="parentName" style="width: 44%;display: inline-block;" class="layui-input" value="#(parentTypeName??'顶级类型')" readonly="readonly">
                    <button class="layui-btn layui-btn-sm" id="setTopType" type="button">设置为顶级类型</button>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>类型名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" placeholder="请输入类型名称">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">是否可用</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnabled" value="1" title="可用" #if(model==null || model.isEnabled??=='1') checked #end>
                    <input type="radio" name="isEnabled" value="0" title="不可用" #if(model.isEnabled??=='0') checked #end>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入内容" name="remark" class="layui-textarea">#(model.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">排序</label>
                <div class="layui-input-block">
                    <input type="text" name="sort" lay-verify="required|number" class="layui-input" value="#(model.sort??)" placeholder="请输入排序">
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-left">
                    <div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
                </div>
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(model.Id??)">
                    <input type="hidden" id="parentId" name="parentId" value="#(model.parentId??'')">
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    layui.use([ 'form' ], function() {
        var form = layui.form
            , layer = layui.layer
        ;

        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/mainExamine/typeTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#parentId").val(treeNode.id);
                    $("#parentName").val(treeNode.name);
                }
            }
        };

        $("#setTopType").on('click',function () {
            $("#parentName").val("顶级类型");
            $("#parentId").val("");
        });

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                //expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/wms/stockType/stockTypeTree',{},function (res) {
            deptSelect.update({
                data:res
            })
            #if(model!=null)
            deptSelect.setValue(["#(model.wmsStockTypeId??)"]);
            #end
        });

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            var data=$(formObj.form).serialize();

            //return false;
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mainExamine/typeSave',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.tableGridReload();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end