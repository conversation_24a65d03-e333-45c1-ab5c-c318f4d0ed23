#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;" id="roomTypeForm">
<div class="layui-row" style="padding: 20px 10px;">
    <!--<div class="layui-col-xs6" id="contentDiv2" style="padding: 10px 10px;">

        <div class="demoTable layui-row">
            <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" >
                问题类型:
                <div class="layui-inline">
                    <select name="problemTypeId" id="problemTypeId">
                        <option value="">请选择问题类型</option>
                        #for(type : typeList)
                        <option value="#(type.id)">#(type.name)</option>
                        #end
                    </select>
                </div>
                问题名称:
                <div class="layui-inline">
                    <input id="name" name="name" class="layui-input">
                </div>
                &nbsp;&nbsp;
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            </form>

        </div>
        <table id="roomTypeTable" lay-filter="roomTypeTable"></table>


        <div style="margin-top: 80px;">

        </div>
    </div>-->
    <div class="layui-col-xs12" id="contentDiv" style="padding: 10px 10px;">
       <div class="layui-row">
           <input type="hidden" id="roomTypeId" name="id" value="#(model.id??)"/>
           <div class="layui-form-item">
               <label class="layui-form-label"><font color="red">*</font>方案名称</label>
               <div class="layui-input-block">
                   <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" placeholder="请输入问题名称"  autocomplete="off">
               </div>
           </div>

           <div class="layui-form-item">
               <label class="layui-form-label" style="width: 180px;"><font color="red">*</font>是否开启加权计分方式</label>
               <div class="layui-input-block">
                   <input type="radio" name="isWeightedMean" value="1" title="是" #if( model.isWeightedMean??=="1") checked #end>
                   <input type="radio" name="isWeightedMean" value="0" title="否" #if(model==null || model.isWeightedMean??=="0") checked #end>
               </div>
           </div>

           <div class="layui-form-item">
               <label class="layui-form-label"><font color="red">*</font>是否可用</label>
               <div class="layui-input-block">
                   <input type="radio" name="isEnabled" value="1" title="是" #if(model==null || model.isEnabled??=="1") checked #end>
                   <input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled??=="0") checked #end>
               </div>
           </div>

           <dov class="layui-form-item">
               <label class="layui-form-label">备注</label>
               <div class="layui-input-block">
                   <textarea id="remark" name="remark" placeholder="请输入内容" class="layui-textarea">#(model.remark??)</textarea>
               </div>
           </dov>

        </div>

        <div style="margin-top: 80px;">

        </div>
    </div>
    <div class="layui-form-footer" >
        <div class="pull-right">

            <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
            <input type="hidden" id="fileId" name="roomType.fileId" value="#(roomType.fileId??)"/>
            <input type="hidden" id="optionCount" name="optionCount" value="#if(model==null)0#else#(model.allOptions.size()??0)#end" >
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload','table'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload,table=layui.table;


        //保存
        form.on('submit(saveBtn)', function(){

            var url = "#(ctxPath)/mainExamine/schemeSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#roomTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


    });
</script>
#end
