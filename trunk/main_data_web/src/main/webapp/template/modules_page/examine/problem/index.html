#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型管理#end

#define css()
#end

#define js()
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                //expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/mainExamine/typeXSSelectTree',{},function (res) {
            deptSelect.update({
                data:res
            })
        });



        roomTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            reloadTable();
            return false;
        });


        reloadTable=function() {
            var array=deptSelect.getValue();
            var problemTypeId="";
            if(array.length>0){
                problemTypeId=array[0].id;
            }
            console.log("problemTypeId:"+problemTypeId);
            roomTypeLoad({"name":$("#name").val(),"problemTypeId":problemTypeId});
            return false;
        }

        function roomTypeLoad(data){
            table.render({
                id : 'roomTypeTable'
                ,elem : '#roomTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/mainExamine/problemPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'sort', title: '问题类型名称', align: 'center', unresize: true,templet: function (d){
                        return d.problemType.name;
                    }}
                   ,{field:'name', title: '问题内容', align: 'center', unresize: true}
                    ,{field:'type', title: '问题填写类型', align: 'center', unresize: true,templet: function (d){
                            if(d.type=='text'){
                                return '文本'
                            }else if(d.type=='radio'){
                                return '单选'
                            }else if(d.type=='checkbox'){
                                return '多选'
                            }
                        }}
                    ,{field:'isEnable', title: '是否必填', align: 'center', unresize: true,templet:"<div>{{ d.isRequired=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isRequired=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mainExamine/problemForm" ;
            pop_show("新增问题",url,1250,650);
        });

        table.on('tool(roomTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/baseRoomType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('roomTypeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mainExamine/problemForm?id=" + obj.data.id ;
                pop_show("编辑问题",url,1250,650);
            }else if(obj.event==='disable'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/problemSave',
                    notice: true,
                    data: {id:obj.data.id,'isEnabled':'0'},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload('roomTypeTable',null);
                        }
                    },
                    complete : function() {
                        layer.closeAll();

                    }
                });
            }else if(obj.event==='enable'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mainExamine/problemSave',
                    notice: true,
                    data: {id:obj.data.id,'isEnabled':'1'},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload('roomTypeTable',null);
                        }
                    },
                    complete : function() {
                        layer.closeAll();
                    }
                });
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('roomTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:roomType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #[[
    {{#if(d.isEnabled=='1'){}}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">禁用</a>
    {{#}}}

    {{#if(d.isEnabled=='0'){}}
    <a class="layui-btn layui-btn-xs" lay-event="enable">启用</a>
    {{#}}}
    ]]#
    #end

</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            问题类型:
            <div class="layui-inline" style="width: 300px;">
                <div id="deptSelect" >

                </div>
            </div>
            问题名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            &nbsp;&nbsp;
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:roomType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end

    </div>
    <table id="roomTypeTable" lay-filter="roomTypeTable"></table>
</div>
#end