#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()仓库表单#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="warehouses">
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>仓库名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="#(warehouse.name??)" autocomplete="off" placeholder="请输入仓库名称" class="layui-input" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span></span>所属基地</label>
                <div class="layui-input-block">
                    <select name="baseId" lay-verify="">
                        <option value="">请选择所属基地</option>
                        #for(base:baseList)
                        <option value="#(base.id)" #if(warehouse.baseId??==base.id) selected #end>#(base.baseName)</option>
                        #end
                    </select>
                </div>
            </div>

            <!--<div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red"></font>所属组织机构</label>
                <div class="layui-input-block">
                    <div id="deptSelect" style="">

                    </div>
                </div>
            </div>-->
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>顺序</label>
                <div class="layui-input-block">
                    <input type="text" name="warehouseOrder" value="#(warehouse.warehouseOrder??)" autocomplete="off" placeholder="请输入顺序" class="layui-input" lay-verify="required|number">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>是否有效</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnabled" title="是" #if(warehouse!=null && warehouse.isEnabled??=='1') checked #else if(warehouse==null) checked #end value="1" autocomplete="off" lay-verify="required">
                    <input type="radio" name="isEnabled" title="否" #if(warehouse!=null && warehouse.isEnabled??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;"><span></span>日报表提交人</label>
                <div class="layui-input-block">
                    <select xm-select="posSubmitUserIds" xm-select-search	 name="posSubmitUserIds" id="posSubmitUserIds">
                        #for(user : userList)
                        <option value="#(user.id??)" #if(warehouse!=null && warehouse.posSubmitUserIds!=null && warehouse.posSubmitUserIds.indexOf(user.id)!=-1) selected #end >#(user.name??)(#(user.user_name??))</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;"><span></span>日报表审核人</label>
                <div class="layui-input-block">
                    <select xm-select="posHandlerUserIds" xm-select-search	 name="posHandlerUserIds" id="posHandlerUserIds">
                        #for(user : userList)
                        <option value="#(user.id??)" #if(warehouse!=null && warehouse.posHandlerUserIds!=null && warehouse.posHandlerUserIds.indexOf(user.id)!=-1) selected #end >#(user.name??)(#(user.user_name??))</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">#(warehouse.description??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" type="hidden" value="#(warehouse.id??)" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>

</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    var formSelects=layui.formSelects;
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form', 'laydate', 'upload','jquery'],function(){
        var form = layui.form;
        var $ = layui.$;

        /*var deptSelect;

        var deptSelectInitValue=[];

        #for(warehouseId : warehouseIdList)
        deptSelectInitValue.push("#(warehouseId??)");
        #end

        $.post('#(ctxPath)/wms/warehouse/orgXmSelectTree',{},function (res) {

            deptSelect = xmSelect.render({
                el: '#deptSelect',
                autoRow: true,
                filterable: true,
                height: '200px',
                prop: {
                    name: 'name',
                    value: 'id',
                },
                tree: {
                    show: true,
                    showFolderIcon: true,
                    showLine: true,
                    indent: 15,
                    lazy: true,
                    clickExpand: true,
                    strict: false,
                    expandedKeys: ['54325705-FF63-43DB-9723-FA31E94AF8E3'],
                    //点击节点是否选中
                    clickCheck: true,
                    load: function(item, cb){

                    }
                },
                height: 'auto',
                data(){
                    return res;
                },
                initValue: deptSelectInitValue
            });
        });*/

        //保存
        form.on('submit(confirmBtn)', function(obj){
            /*var depts=deptSelect.getValue();
            var deptIds=[];
            $.each(depts,function (index,item) {
                deptIds.push(item.id);
            });
            console.log(depts);
            +"&deptIds="+JSON.stringify(deptIds)
            */

            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/warehouse/saveWarehouse',
                data: $("#warehouses").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.warehousesTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end