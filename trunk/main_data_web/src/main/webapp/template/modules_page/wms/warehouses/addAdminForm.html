#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()库区表单#end

#define css()
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="warehousesArea">
            <table class="layui-table">
                <thead>
                    <col style="width:30%;" />
                    <col style="width:30%;" />
                    <col style="width:30%;" />
                    <col style="width:10%;" />
                    <tr>
                        <th>管理员</th>
                        <th>物资接收员</th>
                        <th>餐饮接收员</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    #for(admin : adminList)
                    <tr id="tr-#(for.index)">
                        <td>#(admin.name??)
                            <input name="adminList[#(for.index+1)].id" type="hidden" value="#(admin.id)">
                            <input name="adminList[#(for.index+1)].userId" type="hidden" value="#(admin.user_id)">
                        </td>
                        <td>
                            <select name="adminList[#(for.index+1)].isReceiver">
                                <option value="">请选择</option>
                                <option value="1" #if(admin.is_receiver??=='1') selected  #end>是</option>
                                <option value="0"  #if(admin.is_receiver??=='0') selected  #end>否</option>
                            </select>
                        </td>
                        <td>
                            <select name="adminList[#(for.index+1)].mealReceiver">
                                <option value="">请选择</option>
                                <option value="1" #if(admin.meal_receiver??=='1') selected  #end>是</option>
                                <option value="0"  #if(admin.meal_receiver??=='0') selected  #end>否</option>
                            </select>
                        </td>
                        <td><button class="layui-btn layui-btn-danger layui-btn-sm" type="button" onclick="delAdmin('#(admin.id)',#(for.index));">作废</button></td>
                    </tr>
                    #end
                </tbody>
            </table>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="warehouseId" type="hidden" value="#(warehouseId??)" />
                    <input name="count" id="count" type="hidden" value="#(adminList.size()??0)" >
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="addBtn" class="layui-btn" type="button" >添加</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>

</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="settingTpl">
    <tr id="tr-{{d.idx}}">
        <td>
            <select name="adminList[{{d.idx}}].userId" lay-verify="required" lay-search class="userSelect">
                <option value="">请选择管理员</option>
                #for(user : userList)
                <option value="#(user.id??)">#(user.name??)#[[(]]##(user.userName??)#[[)]]#</option>
                #end
            </select>
        </td>
        <td>
            <select name="adminList[{{d.idx}}].isReceiver" lay-verify="required">
                <option value="">请选择</option>
                <option value="1" >是</option>
                <option value="0" >否</option>
            </select>
        </td>
        <td>
            <select name="adminList[{{d.idx}}].mealReceiver" lay-verify="required">
                <option value="">请选择</option>
                <option value="1" >是</option>
                <option value="0" >否</option>
            </select>
        </td>
        <td>
            <button class="layui-btn layui-btn-danger layui-btn-sm" type="button" onclick="delAdmin('',{{d.idx}});">作废</button>
        </td>
    </tr>
</script>
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload','jquery','laytpl'],function(){
        var form = layui.form;
        var laytpl=layui.laytpl;
        var $ = layui.$;

        //保存
        form.on('submit(confirmBtn)', function(obj){

            var selects=$(".userSelect");
            var flag=false;
            $.each(selects,function (index,item) {
                $.each(selects,function (i,it) {
                    if(i!=index && $(item).val()==$(it).val()){
                        flag=true;
                        return false;
                    }
                })
            });
            if(flag){
                layer.msg('请勿重复添加', {icon: 2, offset: 'auto'});
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/warehouse/saveAdmin',
                data: $("#warehousesArea").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.warehouseManagerForm();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        delAdmin=function (id,index) {
            if(id===''){
                $('#tr-'+index).remove();
            }else{
                layer.confirm('是否要删除?', function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/wms/warehouse/delAdmin',
                        data: {'id':id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pop_close();
                                parent.warehouseManagerForm();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
        }


        $("#addBtn").on('click',function () {
            var idx=Number($("#count").val())+1;
            laytpl(settingTpl.innerHTML).render({"idx": idx}, function(html){
                $("#tbody").append(html);
                $("#count").val(idx);
            });

            form.render();
        });

    });
</script>
#end