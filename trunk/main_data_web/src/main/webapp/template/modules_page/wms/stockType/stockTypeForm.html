#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()货物分类表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="typeForm">
        <div class="layui-row">
            <div class="layui-col-xs3" style="padding:5px 5px 5px 20px;">
                <div id="typeTree" class="ztree" style="height:450px;overflow:auto;"></div>
            </div>

            <div class="layui-col-xs9" style="padding:5px 5px 5px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>父级</label>
                        <div class="layui-input-block">
                            <input type="text" id="parentName" style="width: 60%;display: inline-block;" readonly  value="#(parentType.name??'顶层分类')" autocomplete="off" placeholder="请选择父级类型" class="layui-input" lay-verify="required">
                            <input type="hidden" id="parentId" name="parentId" value="#(type.parentId??)" >
                            <button type="button" id="setTopBtn" class="layui-btn layui-btn-sm">设置为顶层分类</button>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>分类名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="name" value="#(type.name??)" autocomplete="off" placeholder="请输入分类名称" class="layui-input" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>分类编号</label>
                        <div class="layui-input-block">
                            <input type="text" name="stockTypeNo" value="#(type.stockTypeNo??)" maxlength="10" autocomplete="off" placeholder="请输入分类编号" class="layui-input" lay-verify="required">
                        </div>
                    </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 1px;"><span>*</span>货物编号前缀</label>
                    <div class="layui-input-block">
                        <input type="text" name="stockCodePrefix" value="#(type.stockCodePrefix??)" maxlength="10" autocomplete="off" placeholder="请输入货物编号前缀" class="layui-input" lay-verify="required">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;"><span>*</span>是否一物一码</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isOneCode" title="是" #if(type!=null && type.isOneCode??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isOneCode" title="否" #if(type!=null && type.isOneCode??=='0') checked #else if(type==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
<!--                 <div class="layui-form-item"> -->
<!--                     <label class="layui-form-label" style="padding: 8px 5px;width: 135px;"><span>*</span>是否行政专员审核</label> -->
<!--                     <div class="layui-input-block"> -->
<!--                         <input type="radio" name="isAdministrativeStaff" title="是" #if(type!=null && type.isAdministrativeStaff??=='1') checked  #end value="1" autocomplete="off" lay-verify="required"> -->
<!--                         <input type="radio" name="isAdministrativeStaff" title="否" #if(type!=null && type.isAdministrativeStaff??=='0') checked #else if(type==null) checked #end value="0" autocomplete="off" lay-verify="required"> -->
<!--                     </div> -->
<!--                 </div> -->
<!--                 <div class="layui-form-item"> -->
<!--                     <label class="layui-form-label" style="padding: 8px 5px;width: 135px;"><span>*</span>是否财务专员审核</label> -->
<!--                     <div class="layui-input-block"> -->
<!--                         <input type="radio" name="isFinanceStaff" title="是" #if(type!=null && type.isFinanceStaff??=='1') checked  #end value="1" autocomplete="off" lay-verify="required"> -->
<!--                         <input type="radio" name="isFinanceStaff" title="否" #if(type!=null && type.isFinanceStaff??=='0') checked #else if(type==null) checked #end value="0" autocomplete="off" lay-verify="required"> -->
<!--                     </div> -->
<!--                 </div> -->
<!--                 <div class="layui-form-item"> -->
<!--                     <label class="layui-form-label" style="padding: 8px 5px;width: 135px;"><span>*</span>是否文旅专员审核</label> -->
<!--                     <div class="layui-input-block"> -->
<!--                         <input type="radio" name="isTourismStaff" title="是" #if(type!=null && type.isTourismStaff??=='1') checked  #end value="1" autocomplete="off" lay-verify="required"> -->
<!--                         <input type="radio" name="isTourismStaff" title="否" #if(type!=null && type.isTourismStaff??=='0') checked #else if(type==null) checked #end value="0" autocomplete="off" lay-verify="required"> -->
<!--                     </div> -->
<!--                 </div> -->
<!--                 <div class="layui-form-item"> -->
<!--                     <label class="layui-form-label" style="padding: 8px 5px;width: 135px;"><span>*</span>是否工程专员审核</label> -->
<!--                     <div class="layui-input-block"> -->
<!--                         <input type="radio" name="isEngineeringStaff" title="是" #if(type!=null && type.isEngineeringStaff??=='1') checked  #end value="1" autocomplete="off" lay-verify="required"> -->
<!--                         <input type="radio" name="isEngineeringStaff" title="否" #if(type!=null && type.isEngineeringStaff??=='0') checked #else if(type==null) checked #end value="0" autocomplete="off" lay-verify="required"> -->
<!--                     </div> -->
<!--                 </div> -->
<!--                 <div class="layui-form-item"> -->
<!--                     <label class="layui-form-label" style="padding: 8px 5px;width: 135px;"><span>*</span>是否IT专员审核</label> -->
<!--                     <div class="layui-input-block"> -->
<!--                         <input type="radio" name="isItStaff" title="是" #if(type!=null && type.isItStaff??=='1') checked  #end value="1" autocomplete="off" lay-verify="required"> -->
<!--                         <input type="radio" name="isItStaff" title="否" #if(type!=null && type.isItStaff??=='0') checked #else if(type==null) checked #end value="0" autocomplete="off" lay-verify="required"> -->
<!--                     </div> -->
<!--                 </div> -->
                <div class="layui-form-item">
                    <label class="layui-form-label">标签</label>
                    <div class="layui-input-block">
                        <select xm-select="lableIds" name="lableIds" id="lableIds">
                            #for(stockLabel : stockLabelList)
                            <option value="#(stockLabel.id??)" #if(type==null)  #elseif(lableIds??!=null && lableIds.indexOf(stockLabel.id)!=-1) selected #end >#(stockLabel.labelName??)</option>
                            #end
                        </select>
                    </div>
                </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>是否有效</label>
                        <div class="layui-input-block">
                            <input type="radio" name="isEnabled" title="是" #if(type!=null && type.isEnabled??=='1') checked #else if(type==null) checked #end value="1" autocomplete="off" lay-verify="required">
                            <input type="radio" name="isEnabled" title="否" #if(type!=null && type.isEnabled??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">描述</label>
                        <div class="layui-input-block">
                            <textarea name="description" placeholder="请输入描述" class="layui-textarea">#(type.description??)</textarea>
                        </div>
                    </div>

            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" type="hidden" value="#(type.id??)" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>

<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</script>
<script>
    var formSelects=layui.formSelects;
    layui.use(['form','tree'], function() {
        var $ = layui.$, form=layui.form,layer=layui.layer,tree=layui.tree;
        formSelects.render('lableIds');

        //树属性配置
        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/wms/stockType/stockTypeTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#parentId").val(treeNode.id);
                    $("#parentName").val(treeNode.name);
                }
            }
        };

        // 设置顶层菜单按钮点击事件
        var zTreeObj = $.fn.zTree.init($("#typeTree"), setting);

        // 初始化树结构
        layui.$('#setTopBtn').on('click', function() {
            $("#parentName").val('顶层分类');
            $("#parentId").val('00000000-0000-0000-0000-000000000000');
        });

        //监听表单提交
        form.on('submit(confirmBtn)', function(formObj) {
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/stockType/saveStockType',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.treeGridLoad({isEnabled:'1'});
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end