#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()仓库表单#end

#define css()
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="location">
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>库位名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="#(location.name??)" autocomplete="off" placeholder="请输入库位名称" class="layui-input" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>编号</label>
                <div class="layui-input-block">
                    <input type="text" name="warehouseLocationNo" value="#(location.warehouseLocationNo??)" autocomplete="off" placeholder="请输入编号" class="layui-input" lay-verify="required|number">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>是否有效</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnabled" title="是" #if(location!=null && location.isEnabled??=='1') checked #else if(location==null) checked #end value="1" autocomplete="off" lay-verify="required">
                    <input type="radio" name="isEnabled" title="否" #if(location!=null && location.isEnabled??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">#(location.description??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" type="hidden" value="#(location.id??)" />
                    <input name="warehouseAreaId" type="hidden" value="#if(location==null)#(warehouseAreaId??)#else#(location.warehouseAreaId??)#end">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>

</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload','jquery'],function(){
        var form = layui.form;
        var $ = layui.$;

        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/warehouse/saveWarehouseLocation',
                data: $("#location").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.locationTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end