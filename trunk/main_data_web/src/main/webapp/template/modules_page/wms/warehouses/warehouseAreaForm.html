#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()库区表单#end

#define css()
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="warehousesArea">
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>库区名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" value="#(area.name??)" autocomplete="off" placeholder="请输入库区名称" class="layui-input" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>顺序</label>
                <div class="layui-input-block">
                    <input type="text" name="warehouseAreaOrder" value="#(area.warehouseAreaOrder??)" autocomplete="off" placeholder="请输入顺序" class="layui-input" lay-verify="required|number">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>是否有效</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnabled" title="是" #if(area!=null && area.isEnabled??=='1') checked #else if(area==null) checked #end value="1" autocomplete="off" lay-verify="required">
                    <input type="radio" name="isEnabled" title="否" #if(area!=null && area.isEnabled??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">#(area.description??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" type="hidden" value="#(area.id??)" />
                    <input name="warehouseId" type="hidden" value="#if(area==null)#(warehouseId??)#else#(area.warehouseId??)#end" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>

</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload','jquery'],function(){
        var form = layui.form;
        var $ = layui.$;

        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/warehouse/saveWarehouseArea',
                data: $("#warehousesArea").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.areaTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end