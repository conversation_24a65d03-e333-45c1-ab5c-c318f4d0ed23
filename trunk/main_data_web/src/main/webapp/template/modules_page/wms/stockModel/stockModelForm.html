#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()货物分类表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>

#end

#define content()
<div class="layui-collapse">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="typeForm">
        <div class="layui-row layui-col-space10">
            <div class="layui-col-xs4">
                <fieldset class="layui-elem-field" >
                    <legend>分类</legend>
                    <div id="typeTree" class="ztree" style="height:500px;overflow:auto;"></div>
                </fieldset>
            </div>
            <div class="layui-col-xs8">
                <div class="layui-row">
                    <label class="layui-form-label"><span>*</span>所属分类</label>
                    <div class="layui-input-block">
                        <input type="text" id="stockTypeName" readonly  value="#(type.name??)" autocomplete="off" placeholder="请选择分类" class="layui-input" lay-verify="required">
                        <input type="hidden" id="stockTypeId" name="stockTypeId" value="#(model.stockTypeId??)" >
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label"><span>*</span>商品名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="name" value="#(model.name??)" autocomplete="off" placeholder="请输入商品名称" class="layui-input" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label"><span>*</span>货物类型</label>
                    <div class="layui-input-block">
                        <input type="radio" name="type" title="资产" #if(model!=null && model.type??=='Assets') checked  #end value="Assets" autocomplete="off" lay-verify="required">
                        <input type="radio" name="type" title="耗材" #if(model!=null && model.type??=='Consume') checked #else if(model==null) checked #end value="Consume" autocomplete="off" lay-verify="required">
                    </div>
                </div>
				<div class="layui-row">
                    <label class="layui-form-label">品牌</label>
                    <div class="layui-input-block">
                        <select id="brandId" name="brandId" lay-search="">
                        	<option value="">请选择</option>
                            #for(brand : brandList)
                            	<option value="#(brand.id)" #(brand.id == model.brandId??''?'selected':'')>#(brand.assetBrandName)</option>
                            #end
                        </select>
                    </div>
                </div>
				<div class="layui-row">
                    <label class="layui-form-label"><span>*</span>单位</label>
                    <div class="layui-input-block">
                        <select id="unitId" name="unitId" lay-filter="unitIdFilter" lay-search="" lay-verify="required">
                        	<option value="">请选择</option>
                            #for(unit : unitList)
                            	<option value="#(unit.id)" #(unit.id == model.unitId??''?'selected':'')>#(unit.unitName)</option>
                            #end
                        </select>
                    </div>
                </div>
<!--                 <div class="layui-form-item"> -->
<!--                     <label class="layui-form-label"><span>*</span>单位</label> -->
<!--                     <div class="layui-input-block"> -->
<!--                         <input type="text" name="unit" value="#(model.unit??)" autocomplete="off" placeholder="请输入单位" class="layui-input" lay-verify="required"> -->
<!--                     </div> -->
<!--                 </div> -->
                #if(model==null)
                #end
                <div class="layui-row">
                    <label class="layui-form-label"><span>*</span>成本价格</label>
                    <div class="layui-input-block">
                        <input type="text" name="costPrice" value="#(model.costPrice??)" autocomplete="off" placeholder="请输入成本价格" class="layui-input" lay-verify="required|number">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="width: 165px;"><span>*</span>是否允许人民币兑换</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isRmbPay" lay-filter="isRmbPay" title="是" #if(model!=null && model.isRmbPay??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isRmbPay" lay-filter="isRmbPay" title="否" #if(model!=null && model.isRmbPay??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row" id="salePriceDiv" #if(model.isRmbPay??=='1')  #else style="display: none;" #end >
                    <label class="layui-form-label"><span>*</span>销售价格</label>
                    <div class="layui-input-block">
                        <input type="text" name="salePrice" value="#(model.salePrice??)" autocomplete="off" placeholder="请输入销售价格" class="layui-input" #if(model.isRmbPay??=='1') lay-verify="required|number"  #else   #end >
                    </div>
                </div>

                <div class="layui-row">
                    <label class="layui-form-label" style="width: 200px;"><span>*</span>是否允许会员卡天数兑换</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isCardTimesPay" lay-filter="isCardTimesPay" title="是" #if(model!=null && model.isCardTimesPay??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isCardTimesPay" lay-filter="isCardTimesPay" title="否" #if(model!=null && model.isCardTimesPay??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row" id="exchangeTimesDiv" #if(model.isCardTimesPay??=='1')  #else style="display: none;" #end>
                    <label class="layui-form-label" style="padding: 8px 0px;"><span>*</span>兑换价格(天数)</label>
                    <div class="layui-input-block">
                        <input type="text" name="exchangeTimes" value="#(model.exchangeTimesStr??)" autocomplete="off" placeholder="请输入兑换积分" class="layui-input" #if(model.isCardTimesPay??=='1') lay-verify="required|number"  #else   #end >
                    </div>
                </div>

                <div class="layui-row">
                    <label class="layui-form-label" style="width: 200px;"><span>*</span>是否允许会员卡金额兑换</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isCardAmountPay" lay-filter="isCardAmountPay" title="是" #if(model!=null && model.isCardAmountPay??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isCardAmountPay" lay-filter="isCardAmountPay" title="否" #if(model!=null && model.isCardAmountPay??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row" id="exchangeAmountDiv" #if(model.isCardAmountPay??=='1')  #else style="display: none;"   #end>
                    <label class="layui-form-label" style="padding: 8px 0px;width: 160px;"><span>*</span>兑换价格(会员卡金额)</label>
                    <div class="layui-input-block" style="margin-left: 160px;">
                        <input type="text" name="exchangeAmount" value="#(model.exchangeAmountStr??)" autocomplete="off" placeholder="请输入兑换积分" class="layui-input"  #if(model.isCardAmountPay??=='1') lay-verify="required|number"  #else   #end>
                    </div>
                </div>

                <div class="layui-row">
                    <label class="layui-form-label" style="width: 200px;"><span>*</span>是否允许会员卡积分兑换</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isCardIntegralPay" lay-filter="isCardIntegralPay" title="是" #if(model!=null && model.isCardIntegralPay??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isCardIntegralPay" lay-filter="isCardIntegralPay" title="否" #if(model!=null && model.isCardIntegralPay??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row" id="exchangePriceDiv" #if(model.isCardIntegralPay??=='1')  #else style="display: none;"   #end>
                    <label class="layui-form-label" style="padding: 8px 0px;width: 160px"><span>*</span>兑换价格(会员卡积分)</label>
                    <div class="layui-input-block" style="margin-left: 160px;">
                        <input type="text" name="exchangePrice" value="#(model.exchangePriceStr??)" autocomplete="off" placeholder="请输入兑换积分" class="layui-input" #if(model.isCardIntegralPay??=='1') lay-verify="required|number"  #else   #end>
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="width: 210px;"><span>*</span>是否允许会员卡豆豆券兑换</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isBeanCouponsPay" lay-filter="isBeanCouponsPay" title="是" #if(model!=null && model.isBeanCouponsPay??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isBeanCouponsPay" lay-filter="isBeanCouponsPay" title="否" #if(model!=null && model.isBeanCouponsPay??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row" id="exchangeBeanCouponsDiv" #if(model.isBeanCouponsPay??=='1')  #else style="display: none;"   #end>
                    <label class="layui-form-label" style="padding: 8px 0px;width: 115px;"><span>*</span>兑换价格(豆豆券)</label>
                    <div class="layui-input-block" style="margin-left: 115px;">
                        <input type="text" name="exchangeBeanCoupons" value="#(model.exchangeBeanCouponsStr??)" autocomplete="off" placeholder="请输入兑换豆豆券" class="layui-input" #if(model.isBeanCouponsPay??=='1') lay-verify="required|number"  #else   #end>
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="width: 210px;"><span>*</span>是否允许员工卡兑换</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isStaffIntegralPay" lay-filter="isStaffIntegralPay" title="是" #if(model!=null && model.isStaffIntegralPay??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isStaffIntegralPay" lay-filter="isStaffIntegralPay" title="否" #if(model!=null && model.isStaffIntegralPay??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row" id="exchangeStaffIntegralDiv" #if(model.isStaffIntegralPay??=='1')  #else style="display: none;"   #end>
                    <label class="layui-form-label" style="padding: 8px 0px;width: 150px;"><span>*</span>兑换价格(员工卡天数)</label>
                    <div class="layui-input-block" style="margin-left: 150px;">
                        <input type="text" name="exchangeStaffIntegral" value="#(model.exchangeStaffIntegralStr??)" autocomplete="off" placeholder="请输入兑换价格(员工积分)" class="layui-input" #if(model.isStaffIntegralPay??=='1') lay-verify="required|number"  #else   #end>
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label"><span>*</span>规格型号</label>
                    <div class="layui-input-block">
                        <input type="text" name="standard" value="#(model.standard??)" autocomplete="off" placeholder="请输入规格" class="layui-input" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="padding: 8px 8px;"><span>*</span>低库存警报值</label>
                    <div class="layui-input-block">
                        <input type="text" name="warningMin" value="#(model.warningMin??)" autocomplete="off" placeholder="请输入低库存警报值" class="layui-input" lay-verify="required|number">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label">条形码编号</label>
                    <div class="layui-input-block">
                        <input type="text" style="width: 250px;display: inline-block;" id="barcodeNumber" name="barcodeNumber" value="#(model.barcodeNumber??)" autocomplete="off" lay-verify="" placeholder="请输入条形码编号" class="layui-input" >
                        <button class="layui-btn" type="button" onclick="genBarcodeNumber();">生成</button>
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label">标签</label>
                    <div class="layui-input-block">
                        <select xm-select="lableIds" name="lableIds" id="lableIds">
                            #for(stockLabel : stockLabelList)
                            <option value="#(stockLabel.id??)" #if(model==null)  #elseif(lableIds??!=null && lableIds.indexOf(stockLabel.id)!=-1) selected #end >#(stockLabel.labelName??)</option>
                            #end
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="padding: 8px 5px;">不可用自提点</label>
                    <div class="layui-input-block">
                        <select xm-select="notSelfPickupIds" name="notSelfPickupIds" id="notSelfPickupIds">
                            #for(selfPickup : selfPickupList)
                            <option value="#(selfPickup.Id??)" #if(model==null)  #elseif(model.notSelfPickupIds??!=null && model.notSelfPickupIds.indexOf(selfPickup.Id)!=-1) selected #end >#(selfPickup.Name??)</option>
                            #end
                        </select>
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="padding: 8px 8px;"><span>*</span>是否参与销售</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isExchange" title="销售" #if(model!=null && model.isExchange??=='1') checked #else if(model==null) checked #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isExchange" title="不销售" #if(model!=null && model.isExchange??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="padding: 8px 8px;width: 200px;"><span>*</span>是否无库存销售（扣库存）</label>
                    <div class="layui-input-block" style="margin-left: 130px;">
                        <input type="radio" name="isNoStockSale" title="是" #if(model!=null && model.isNoStockSale??=='1') checked  #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isNoStockSale" title="否" #if(model!=null && model.isNoStockSale??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="padding: 8px 8px;width: 230px;"><span>*</span>是否第三方发货（不扣库存）</label>
                    <div class="layui-input-block" style="margin-left: 130px;">
                        <input type="radio" name="isExternal" title="是" #if(model!=null && model.isExternal??=='1') checked #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isExternal" title="否" #if(model!=null && model.isExternal??=='0') checked #else if(model==null) checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label" style="padding: 8px 8px;"><span>*</span>出库顺序类型</label>
                    <div class="layui-input-block">
                        <input type="radio" name="outType" title="先进先出" #if(model!=null && model.outType??=='FirstinFirstout') checked #else if(model==null) checked #end value="FirstinFirstout" autocomplete="off" lay-verify="required">
                        <input type="radio" name="outType" title="先进后出" #if(model!=null && model.outType??=='FirstinLastout') checked #end value="FirstinLastout" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label"><span>*</span>是否有效</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isEnabled" title="是" #if(model!=null && model.isEnabled??=='1') checked #else if(model==null) checked #end value="1" autocomplete="off" lay-verify="required">
                        <input type="radio" name="isEnabled" title="否" #if(model!=null && model.isEnabled??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                    </div>
                </div>
                <div class="layui-row">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea name="description" placeholder="请输入描述" class="layui-textarea">#(model.description??)</textarea>
                    </div>
                </div>
            </div>
        </div>
	    <div class="layui-form-item" style="height:40px;"></div>
	    <div class="layui-form-footer">
	        <div class="pull-right">
	            <input type="hidden" name="id" value="#(model.id??)" />
	            <input type="hidden" id="unit" name="unit" value="#(model.unit??)" />
	            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
	            <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
	        </div>
	    </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>

<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</script>
<script>
    var formSelects=layui.formSelects;
    layui.use(['form','tree'], function() {
        var $ = layui.$, form=layui.form,layer=layui.layer,tree=layui.tree;

        formSelects.render('lableIds');
        formSelects.render('notSelfPickupIds');

        form.on('select(unitIdFilter)', function (data) {
            var unitId = data.value;
            var unitName = $("#unitId").find('option[value="'+unitId+'"]').text();
            console.log('unitName='+unitName);
            if(unitId!=null && unitId!=''){
            	$("#unit").val(unitName);
            }else{
            	$("#unit").val(null);
            }
        });
        
        //树属性配置
        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/wms/stockType/stockTypeTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#stockTypeId").val(treeNode.id);
                    $("#stockTypeName").val(treeNode.name);
                },
                onAsyncSuccess: zTreeOnAsyncSuccess
            }
        };

        // 设置顶层菜单按钮点击事件
        var zTreeObj = $.fn.zTree.init($("#typeTree"), setting);

        function zTreeOnAsyncSuccess(event, treeId, msg) {
            #if(model!=null)
            //获取当前（默认）节点，需要事先知道当前组织id：'b418c3ad-9a6d-445e-b9f7-544f1eee0255'
            var currNode = zTreeObj.getNodeByParam('id', '#(model.stockTypeId??)', null);//id指节点属性id
            zTreeObj.selectNode(currNode);
            zTreeObj.setting.callback.onClick(null, zTreeObj.setting.id, currNode);//回调点击节点时间，其中id指节点属性id
            #end
        }


        //自定义验证规则
        form.verify({
            barcodeNumber: function(value){
                if(value!=''){
                    var number=/^[0-9]*$/;
                    if(number.test(value)===false){
                        return '请输入纯数字';
                    }
                }
            }
        });

        form.on('radio',function (obj) {
            if(obj.elem.name==='isCardTimesPay'){
                if(obj.value=='1'){
                    $("#exchangeTimesDiv").css('display','block');
                    $("input[name='exchangeTimes']").attr('lay-verify','required|number');
                }else{
                    $("#exchangeTimesDiv").css('display','none');
                    $("input[name='exchangeTimes']").attr('lay-verify','');
                }
            }else if(obj.elem.name==='isRmbPay'){
                if(obj.value=='1'){
                    $("#salePriceDiv").css('display','block');
                    $("input[name='salePrice']").attr('lay-verify','required|number');
                }else{
                    $("#salePriceDiv").css('display','none');
                    $("input[name='salePrice']").attr('lay-verify','');
                }
            }else if(obj.elem.name==='isCardAmountPay'){
                if(obj.value=='1'){
                    $("#exchangeAmountDiv").css('display','block');
                    $("input[name='exchangeAmount']").attr('lay-verify','required|number');
                }else{
                    $("#exchangeAmountDiv").css('display','none');
                    $("input[name='exchangeAmount']").attr('lay-verify','');
                }
            }else if(obj.elem.name==='isCardIntegralPay'){
                if(obj.value=='1'){
                    $("#exchangePriceDiv").css('display','block');
                    $("input[name='exchangePrice']").attr('lay-verify','required|number');
                }else{
                    $("#exchangePriceDiv").css('display','none');
                    $("input[name='exchangePrice']").attr('lay-verify','');
                }
            }else if(obj.elem.name==='isBeanCouponsPay'){
                if(obj.value=='1'){
                    $("#exchangeBeanCouponsDiv").css('display','block');
                    $("input[name='exchangeBeanCoupons']").attr('lay-verify','required|number');
                }else{
                    $("#exchangeBeanCouponsDiv").css('display','none');
                    $("input[name='exchangeBeanCoupons']").attr('lay-verify','');
                }
            }else if(obj.elem.name==='isStaffIntegralPay'){
                //isStaffIntegralPay exchangeStaffIntegral
                if(obj.value=='1'){
                    $("#exchangeStaffIntegralDiv").css('display','block');
                    $("input[name='exchangeStaffIntegral']").attr('lay-verify','required|number');
                }else{
                    $("#exchangeStaffIntegralDiv").css('display','none');
                    $("input[name='exchangeStaffIntegral']").attr('lay-verify','');
                }
            }
        });

        //监听表单提交
        form.on('submit(confirmBtn)', function(formObj) {

            if ($("#isNoStockSale").val()=='1' && $("#isExternal").val()=='1') {
                layer.msg('[是否无库存销售]和[是否第三方发货]不能都选[是]', {icon: 2, offset: 'auto'});
                return false;
            }

            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/stockModel/saveStockModel',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.stockModelTableReload();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        $(function(){
            document.onkeydown = function(e){
                var ev = document.all ? window.event : e;
                if(ev.keyCode==13) {
                    $("#confirmBtn").click();
                    return false;
                }
            }
        });



        genBarcodeNumber=function () {
            if($("#barcodeNumber").val()!=''){
                layer.confirm('确定要重新生成吗?', function(index){
                    //提交表单数据
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/wms/stockModel/genBarCodeNumber',
                        notice: false,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                $("#barcodeNumber").val(rep.data);
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }else{
                //提交表单数据
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/wms/stockModel/genBarCodeNumber',
                    notice: false,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            $("#barcodeNumber").val(rep.data);
                        }
                    },
                    complete : function() {
                    }
                });
            }
            return false;
        }
    });
</script>
#end