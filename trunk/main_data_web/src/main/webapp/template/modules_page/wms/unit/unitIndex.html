#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()计量单位查询#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
			<div class="layui-inline">
				<label class="layui-form-label">单位名称:</label>
				<div class="layui-input-inline">
					<input id="unitName" name="unitName" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">是否小数:</label>
				<div class="layui-input-inline">
					<select id="isDecimal" name="isDecimal">
						<option value="">全部</option>
						<option value="0">否</option>
						<option value="1">是</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-btn-group">
					<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
					#shiroHasPermission("wms:unit:addBtn")
						<button type="button" id="addBtn" class="layui-btn">添加</button>
			        #end
				</div>
			</div>
		</form>
	</div>
    <div class="layui-row">
        <table class="layui-table" id="unitTable" lay-filter="unitTable"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="toolBar">
	#shiroHasPermission("wms:unit:editBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
	#shiroHasPermission("wms:unit:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'unitTable'
            ,elem : '#unitTable'
            ,method : 'POST'
            ,where : ''
            ,limit : 10
            ,limits : [10,20,30,40]
            ,url : '#(ctxPath)/wms/unit/unitPage'
            ,cellMinWidth: 80
            ,cols: [[
            	{type: 'numbers', width:80, title: '序号',unresize:true}
                ,{field:'unitName', title: '单位名称', align: 'center', unresize: true}
                ,{field:'', title: '是否小数', align: 'center', width: 200, unresize: true,templet:"#[[<div>{{#if(d.isDecimal=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#toolBar'}
            ]]
            ,page : true
            ,done:function () {
            }
        });

        unitTableReload=function(){
            table.reload('unitTable');
        }
        
        table.on('tool(unitTable)',function (obj) {
            if(obj.event==='edit'){
                pop_show('编辑','#(ctxPath)/wms/unit/unitForm?id='+obj.data.id,'400','300');
            }else if(obj.event==='del'){
            	layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/wms/unit/saveUnit',
                        notice: true,
                        data: {id:obj.data.id, delFlag:'1'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                            	table.reload('unitTable');
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        })

        $("#addBtn").on('click',function () {
            pop_show('添加','#(ctxPath)/wms/unit/unitForm','400','300');
        });
    });
</script>
#end