#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位查询#end

#define css()
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row">

        <div class="layui-col-xs4" style="padding:5px 5px 5px 20px;">
            <fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
                <legend>1.仓库</legend>
                <div class="layui-field-box">


                    <div class="layui-row">

                        <div class="layui-inline">
                            <label class="layui-form-label" style="width: 60px;padding: 9px 5px;">仓库名称:</label>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input id="name" name="name" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline layui-form">
                            <label class="layui-form-label" style="width: 60px;padding: 9px 5px;">是否有效:</label>
                            <div class="layui-input-inline" style="width: 120px;">
                                <select name="isEnabled" id="isEnabled">
                                    <option value="">全部</option>
                                    <option value="1">有效</option>
                                    <option value="0">无效</option>
                                </select>
                            </div>
                        </div>
                        <button class="layui-btn layui-btn-sm" type="button" id="queryBtn">查询</button>
                        <button class="layui-btn layui-btn-sm" type="button" id="addWarehouses">添加仓库</button>
                    </div>
                    <table style="margin-left:20px;" class="layui-hide" id="warehousesTable" lay-filter="warehousesTable"></table>
                </div>
            </fieldset>

            <fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
                <legend>2.库区</legend>
                <div class="layui-field-box">
                    <div class="layui-row">
                        <button class="layui-btn layui-btn-sm" type="button" id="addWarehouseAreas">添加库区</button>
                    </div>
                    <table style="margin-left:20px;" class="layui-hide" id="warehouseAreasTable" lay-filter="warehouseAreasTable"></table>
                </div>
            </fieldset>
        </div>

        <div class="layui-col-xs8" style="padding:5px 5px 5px 20px;">
            <fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
                <legend>3.库位</legend>
                <div class="layui-field-box">
                    <div class="layui-row">
                        <button class="layui-btn layui-btn-sm" type="button" id="addWarehouseLocations">添加库位</button>
                        <!--<button class="layui-btn layui-btn-sm" type="button" id="batchDelWarehouseLocations">批量作废库位</button>-->
                    </div>
                    <table style="margin-left:20px;" class="layui-hide" id="warehouseLocationsTable" lay-filter="warehouseLocationsTable"></table>
                </div>
            </fieldset>

            <input type="hidden" id="warehouseId" >
            <input type="hidden" id="warehouseName">
            <input type="hidden" id="warehouseAreaId" >
        </div>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="warehouseBar">
    <a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="editWarehouse">编辑</a>
    <a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="addAdmin">管理员</a>
    <a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="orgType">关联组织机构</a>
</script>
<script type="text/html" id="warehouseAreaBar">
    <a class="layui-btn layui-btn-xs" lay-event="editWarehouseArea">编辑</a>
</script>
<script type="text/html" id="warehouseLocationBar">
    <a class="layui-btn layui-btn-xs" lay-event="editWarehouseLocation">编辑</a>
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'warehousesTable'
            ,elem : '#warehousesTable'
            ,method : 'POST'
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/wms/warehouse/findWarehousePageList'
            ,height:400
            ,cellMinWidth: 80
            ,cols: [[
                {field:'warehouseOrder', title: '顺序',width: 80, align: 'center', unresize: true}
                ,{field:'name', title: '名称', align: 'center', unresize: true}
                ,{field:'isEnabled', title: '是否有效',width: 90, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.isEnabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#warehouseBar'}
            ]]
            ,page : true
            ,done:function () {
                clickRow(this , table , function(data) {
                    $("#warehouseId").val(data.id);
                    $("#warehouseName").val(data.name);
                    //清空表格数据
                    emptyTable("warehouseAreasTable");
                    emptyTable("warehouseLocationsTable");
                    loadWarehouseAreasTable(data.id);
                })
            }
        });

        $("#queryBtn").on('click',function () {

            table.reload('warehousesTable',{where:{'name':$("#name").val(),'isEnabled':$("#isEnabled").val()},page:{curr:1}});
        })


        table.on('tool(warehousesTable)',function (obj) {
            if(obj.event==='editWarehouse'){
                pop_show("编辑仓库",'#(ctxPath)/wms/warehouse/warehouseForm?id='+obj.data.id,500,600);
            }else if(obj.event==='addAdmin'){
                pop_show("["+obj.data.name+"]仓库添加管理员",'#(ctxPath)/wms/warehouse/addAdminForm?id='+obj.data.id+"&name="+obj.data.name,500,600);
            }else if(obj.event==='orgType'){
                pop_show("["+obj.data.name+"]仓库组织机构类型",'#(ctxPath)/wms/warehouse/warehouseOrgTypeIndex?id='+obj.data.id+"&name="+obj.data.name,700,600);
            }
        });

        warehouseManagerForm=function(){
            pop_show("["+$("#warehouseName").val()+"]仓库添加管理员",'#(ctxPath)/wms/warehouse/addAdminForm?id='+$("#warehouseId").val()+"&name="+$("#warehouseName").val(),500,500);
        }

        warehousesTableReload=function(){
            table.reload('warehousesTable');
        }

        areaTableReload=function () {
            table.reload("warehouseAreasTable",{where:{'warehouseId':$("#warehouseId").val()}});
        }

        locationTableReload=function () {
            table.reload("warehouseLocationsTable",{where:{'warehouseAreaId':$("#warehouseAreaId").val()}});
        }


        //加载库区表格
        loadWarehouseAreasTable=function(warehouseId){
            table.render({
                id : 'warehouseAreasTable'
                ,elem : '#warehouseAreasTable'
                ,method : 'POST'
                ,where : ''
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/wms/warehouse/findWarehouseAreaPageList'
                ,where:{'warehouseId':warehouseId}
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'warehouseAreaOrder', title: '顺序',width: 80, align: 'center', unresize: true}
                    ,{field:'name', title: '名称', align: 'center', unresize: true}
                    ,{field:'isEnabled', title: '是否有效',width: 90, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.isEnabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                    ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#warehouseAreaBar'}
                ]]
                ,page : true
                ,done:function () {
                    clickRow(this , table , function(data) {
                        $("#warehouseAreaId").val(data.id);
                        emptyTable("warehouseLocationsTable");
                        loadWarehouseLocationsTable(data.id);
                    })
                }
            });

            table.on('tool(warehouseAreasTable)',function (obj) {
                if(obj.event==='editWarehouseArea'){
                    pop_show("编辑库区",'#(ctxPath)/wms/warehouse/warehouseAreaForm?id='+obj.data.id,500,500);
                }
            });



        }

        loadWarehouseLocationsTable=function(areaId){
            table.render({
                id : 'warehouseLocationsTable'
                ,elem : '#warehouseLocationsTable'
                ,method : 'POST'
                ,where : ''
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/wms/warehouse/findWarehouseLocationPageList'
                ,where:{'warehouseAreaId':areaId}
                ,height:400
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'}
                    ,{field:'warehouseLocationNo', title: '库位编号',width: 90, align: 'center', unresize: true}
                    ,{field:'name', title: '库位名称', align: 'center', unresize: true}
                    ,{field:'isEnabled', title: '是否有效',width: 90, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.isEnabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                    ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#warehouseLocationBar'}
                ]]
                ,page : true
                ,done:function () {
                    clickRow(this , table , function(data) {
                        $("#roomId").val(data.id);
                        emptyTable("bedTable");
                        //loadBedTable(data.id);
                    })
                }
            });

            table.on('tool(warehouseLocationsTable)',function (obj) {
                if(obj.event==='editWarehouseLocation'){
                    pop_show("编辑库位",'#(ctxPath)/wms/warehouse/warehouseLocationForm?id='+obj.data.id,500,500);
                }
            });


        }


        //清空表格
        emptyTable=function(id){
            $("#"+id).next().find("table tbody").empty();
        }

        //添加仓库按钮点击事件
        $("#addWarehouses").on('click',function () {
            pop_show("添加楼栋",'#(ctxPath)/wms/warehouse/warehouseForm',500,600);
        });

        //添加库区按钮
        $("#addWarehouseAreas").on('click',function () {
            var warehouseId=$("#warehouseId").val();
            if(warehouseId==='' || warehouseId===null || typeof(warehouseId)==='undefined'){
                layer.msg("请先选择仓库",{icon:5,time:5000});
                return false;
            }
            pop_show("添加库区",'#(ctxPath)/wms/warehouse/warehouseAreaForm?warehouseId='+warehouseId,500,500);
        });
        //添加库位按钮
        $("#addWarehouseLocations").on('click',function () {
            var warehouseId=$("#warehouseId").val();
            var warehouseAreaId=$("#warehouseAreaId").val();

            if(warehouseId==='' || warehouseId===null || typeof(warehouseId)==='undefined'){
                layer.msg("请先选择仓库",{icon:5,time:5000});
                return false;
            }
            if(warehouseAreaId==='' || warehouseAreaId===null || typeof(warehouseAreaId)==='undefined'){
                layer.msg("请先选库区",{icon:5,time:5000});
                return false;
            }
            pop_show("添加库位",'#(ctxPath)/wms/warehouse/warehouseLocationForm?warehouseAreaId='+warehouseAreaId,500,500);
        });

        //点击批量作废房间按钮
        $("#batchDelRoom").on('click',function () {
            var data=table.checkStatus("roomTable").data;
            if(data.length===0){
                layer.msg("请至少选择一行数据",{icon:5,time:5000});
                return;
            }
            var ids=new Array();
            for(var i=0;i<data.length;i++){
                ids[i]=data[i].id;
            }
            layer.confirm('确定要批量作废选中的房间吗?',function (index) {
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/buildingRoomManage/batchDelRoom',
                    notice: true,
                    data: {'ids':JSON.stringify(ids)},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            roomTableReload();
                        }
                        layer.close(index);
                    },
                    complete : function() {
                    }
                });
            })

        });


        //获取床位表格勾选的数据


        //点击批量作废床位按钮
        $("#batchDelBed").on('click',function () {
            var data=table.checkStatus("bedTable").data;
            if(data.length===0){
                layer.msg("请至少选择一行数据",{icon:5,time:5000});
                return;
            }
            var ids=new Array();
            for(var i=0;i<data.length;i++){
                ids[i]=data[i].id;
            }
            layer.confirm('确定要批量作废选中的床位吗?',function (index) {
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/buildingRoomManage/batchDelBed',
                    notice: true,
                    data: {'ids':JSON.stringify(ids)},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            bedTableReload();
                        }
                        layer.close(index);
                    },
                    complete : function() {
                    }
                });
            })
        });

    });
</script>
#end