#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        roomTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            roomTypeLoad(data.field);
            return false;
        });


         reloadTable=function() {
            roomTypeLoad({"labelName":$("#labelName").val()});
            return false;
        }

        function roomTypeLoad(data){
            table.render({
                id : 'roomTypeTable'
                ,elem : '#roomTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/wms/label/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'sort', title: '排序', align: 'center', unresize: true}
                    ,{field:'labelName', title: '标签名称', align: 'center', unresize: true}
                    ,{field:'isEnabled', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'description', title: '备注', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/wms/label/form" ;
            pop_show("新增房间类型",url,700,450);
        });

        table.on('tool(roomTypeTable)',function(obj){
            if(obj.event === 'edit'){
                var url = "#(ctxPath)/wms/label/form?id=" + obj.data.id ;
                pop_show("编辑标签",url,700,450);
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('roomTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/baseRoomType/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {roomTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("roomTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:label:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end

</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            标签名称:
            <div class="layui-inline">
                <input id="labelName" name="labelName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:label:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end

    </div>
    <table id="roomTypeTable" lay-filter="roomTypeTable"></table>
</div>
#end