#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>联系人类型</label>
			<div class="layui-input-block">
				<select id="linkType" name="linkType" lay-verify="required" lay-search>
					<option value="">请选择</option>
					#statusOption(com.cszn.integrated.service.entity.status.LinkType::me(), model.linkType??"")
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>联系人名称</label>
			<div class="layui-input-block">
				<input type="text" name="linkName" class="layui-input" lay-verify="required" value="#(model.linkName??'')" placeholder="请输入联系人名称" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">联系人职位</label>
			<div class="layui-input-block">
				<input type="text" name="linkPosition" class="layui-input" value="#(model.linkPosition??'')" placeholder="请输入联系人职位" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>联系人电话</label>
			<div class="layui-input-block">
				<input type="text" name="linkPhone" class="layui-input" lay-verify="required|phone|number" value="#(model.linkPhone??'')" placeholder="请输入联系人电话" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">联系人微信</label>
			<div class="layui-input-block">
				<input type="text" name="linkWechat" class="layui-input" value="#(model.linkWechat??'')" placeholder="请输入联系人微信" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				<input type="hidden" name="supplierId" value="#(model.supplierId??'')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	form.render();
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('supplierLinkTab', 'form');
    }
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/wms/supplier/saveLink',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
					location.reload();
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end