#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()更换扣费会员卡#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post" style="margin-left:25px;margin-top:15px;">
            <div class="layui-row">
                <div class="layui-inline">
                    <label class="layui-form-label">会员卡号</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" id="cardNumber" name="cardNumber">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">姓名</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" id="fullName" name="fullName">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">身份证</label>
                    <div class="layui-input-inline">
                        <input class="layui-input" id="idcard" name="idcard">
                    </div>
                </div>
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            </div>
            &nbsp;&nbsp;
        </form>
    </div>
    <div class="layui-row">
        <table id="cardTable" class="layui-table" lay-filter="cardTable"></table>
        <input type="hidden" id="detailId" value="#(detailId??)" >
        <input type="hidden" id="mainId" value="#(mainId??)">
    </div>
</div>
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#getDictLabel("gender")
#define js()
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="choice">选择</a>
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;


        form.on('submit(search)',function (obj) {
            table.reload('cardTable',{'where':{'idcard':$('#idcard').val(),'fullName':$('#fullName').val(),'cardNumber':$('#cardNumber').val()}});
            return false;
        });

        table.render({
            elem: '#cardTable'
            ,url : '#(ctxPath)/wms/supplier/findListPage'
            ,method : 'POST'
            ,height:$(document).height()*0.75
            ,cols: [[
                {type: 'numbers', width:70, title: '序号',unresize:true}
                ,{field: 'cardNumber', title: '会员卡号',unresize:true,width: 120}
                ,{field: 'cardType', title: '类别',unresize:false,width: 180}
                ,{field: 'fullName', title: '姓名',unresize:false,width:100}
                ,{field: 'gender', title: '性别',width: 80,unresize:false,templet: "<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
                ,{field:'consumeTimes', title: '剩余天数', width:120,align: 'center',unresize: false,templet:function (d) {
                        var str = "";
                        if(d.consumeTimes != null){
                            str = String(d.consumeTimes) +"天";
                        }else{
                            str = "- -";
                        }
                        return str;
                    }}
                ,{field:'balance', title: '剩余金额', width:120,align: 'center',unresize: false,templet:function (d) {
                        var str = "";
                        if(d.balance != null){
                            str = String(d.balance) +"元";
                        }else{
                            str = "- -";
                        }
                        return str;
                    }}
                ,{field: 'describe', title: '备注',unresize:false,width:300,style:"text-align:left;font-size:10px;color:#000;"}
                ,{field: 'openTime', title: '开卡时间',unresize:true,width:170,templet: "<div>{{ dateFormat(d.openTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{field: 'createTime', title: '创建时间',unresize:true,width:170,templet: "<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{title: '操作', fixed: 'right',toolbar:'#toolBar',unresize:true,width: 100}
            ]],
            id:'cardTable',
            page : true,
            limit : 10,
            limits: [10,20,30]
        });

        table.on('tool(cardTable)',function (obj) {
            if(obj.event==='choice'){
                /*layer.confirm('确定要更换成该会员卡吗?',function (index) {
                    $.post('#(ctxPath)/fina/orgSettle/billDetaIlReplaceCard',{'detailId':$("#detailId").val(),'cardNumber':obj.data.cardNumber},function (res) {
                        if(res.state==='ok'){
                            parent.billDetailTableReload($("#mainId").val());
                            parent.layer.msg(res.msg, {icon: 1, offset: 'auto'});
                            pop_close();
                        }else{
                            parent.layer.msg(res.msg, {icon: 2, offset: 'auto'});
                        }
                        layer.close(index);
                    })
                })*/
                parent.$("#cardNumber").val(obj.data.cardNumber+"("+obj.data.fullName+")");
                parent.$("#cardId").val(obj.data.id);
                pop_close();
            }
        })
    });
</script>
#end