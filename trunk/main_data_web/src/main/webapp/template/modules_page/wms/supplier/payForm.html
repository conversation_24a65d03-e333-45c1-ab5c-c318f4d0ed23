#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>付款方式</label>
			<div class="layui-input-block">
				<select id="paymentWay" name="paymentWay" lay-verify="required" lay-search lay-filter="paymentWay">
					<option value="">请选择</option>
					#statusOption(com.cszn.integrated.service.entity.status.PayWay::me(), model.paymentWay??"")
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>付款账号</label>
			<div class="layui-input-block">
				<input type="text" name="payAccount" class="layui-input" lay-verify="required" value="#(model.payAccount??'')" onblur="getCardType(this)" placeholder="请输入付款账号" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item" id="bankAccountNameDiv" #if(model.paymentWay??''=='7') style="display: block;" #else style="display: none;" #end>
			<label class="layui-form-label"><font color="red">*</font>开户行</label>
			<div class="layui-input-block">
				<input type="text" name="bankAccountName" class="layui-input" #if(model.paymentWay??''=='7') lay-verify="required" #else lay-verify="" #end value="#(model.bankAccountName??'')" placeholder="请输入户主姓名" autocomplete="off">

			</div>
		</div>
		<div class="layui-form-item" id="bankTypeDiv" #if(model.paymentWay??''=='7') style="display: block;" #else style="display: none;" #end >
			<label class="layui-form-label"><font color="red">*</font>银行卡类别</label>
			<div class="layui-input-block">
				<select name="bankType" id="bankType" #if(model.paymentWay??''=='7') lay-verify="required" #else lay-verify="" #end  lay-search >

				</select>
			</div>
		</div>
		<div class="layui-form-item" id="bankSourceDiv" #if(model.paymentWay??''=='7') style="display: block;" #else style="display: none;" #end>
			<label class="layui-form-label"><font color="red">*</font>开户行</label>
			<div class="layui-input-block">
				<input type="text" name="bankSource" class="layui-input" #if(model.paymentWay??''=='7') lay-verify="required" #else lay-verify="" #end value="#(model.bankSource??'')" placeholder="请输入银行卡开户行" autocomplete="off">

			</div>
		</div>

		#if(com.jfinal.kit.StrKit::isBlank(model.id))
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>是否有效</label>
				<div class="layui-input-block">
					<input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled=='0') checked #end>
	        		<input type="radio" name="isEnabled" value="1" title="是" #if(model.isEnabled??=='1'||model.isEnabled??''=='') checked #end>
				</div>
			</div>
		#end
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				<input type="hidden" name="supplierId" value="#(model.supplierId??'')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	form.render();
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('supplierPayTab', 'form');
    }
    loadBankType=function(){
    	$.post('#(ctxPath)/api/getBankCardTypeList',{},function (d) {
    		if(d.code=='0'){
				let str="<option value=''>请选择银行卡类型</option>";
				let selectValue='#(model.bankType??)';
				$.each(d.data,function (index,item) {
					for (var key in item){
						str+="<option value='"+item[key]+"' data='"+key+"' ";
						if(item[key]==selectValue){
							str+="selected";
						}
						str+=">"+item[key]+"</option>";
					}
				})
				$("#bankType").html(str);
				form.render('select');
			}
		});

		getCardType=function (obj) {
			let bankCardNo=$(obj).val();
			if("7"==$("#paymentWay").val()){
				let url = "https://ccdcapi.alipay.com/validateAndCacheCardInfo.json?_input_charset=utf-8&cardNo=";
				url += bankCardNo;  //bankCardNo:银行卡号
				url += "&cardBinCheck=true"; //是否检查银行
				$.post(url,{},function (d) {
					if(d.validated){
						$("#bankType option[data='"+d.bank+"']").prop('selected',true);
						form.render('select');
					}
				})
			}
		}
	}
	loadBankType();

    form.on('select(paymentWay)',function (obj) {
    	if(obj.value=='7'){

			$("#bankTypeDiv").css("display","block");
			$("#bankType").prop("disabled",false);
			$("#bankSourceDiv").css("display","block");
			$("#bankSource").prop("disabled",false);
			$("#bankAccountNameDiv").css("display","block");
			$("#bankAccountName").prop("disabled",false);

		}else{
			$("#bankTypeDiv").css("display","none");
			$("#bankType").prop("disabled",true);
			$("#bankSourceDiv").css("display","none");
			$("#bankSource").prop("disabled",true);
			$("#bankAccountNameDiv").css("display","none");
			$("#bankAccountName").prop("disabled",true);
		}
	})
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/wms/supplier/savePay',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
					location.reload();
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end