#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse">
    <div class="layui-row">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="supplierForm">
            <div class="layui-row">
	            <label class="layui-form-label">配送品类</label>
	            <div class="layui-input-block">
	                <div id="goodsTypeSelectTree" class="xm-select-demo" hiddenTargetId="goodsTypeIds" dataValue="#(goodsTypeIds??)"></div>
	            </div>
	        </div>
            <div class="layui-row">
            	<div class="layui-inline">
	                <label class="layui-form-label"><font color="red">*</font>供应商编码</label>
	                <div class="layui-input-block">
	                    <input type="text" name="supplierNo" value="#(supplier.supplierNo??)" class="layui-input" lay-verify="required" readonly="readonly" placeholder="请输入供应商编码" autocomplete="off">
	                </div>
            	</div>
                <div class="layui-inline">
	                <label class="layui-form-label"><font color="red">*</font>供应商名称</label>
	                <div class="layui-input-block">
	                    <input type="text" name="name" value="#(supplier.name??)" class="layui-input" lay-verify="required" placeholder="请输入供应商名称" autocomplete="off">
	                </div>
            	</div>
            </div>
			<div class="layui-row">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>供应商类型</label>
					<div class="layui-input-inline" style="width: 183px;">
						<select name="type" id="type" lay-filter="type">
							<option value="">请选择供应商类型</option>
							<option value="1" #if(supplier.type??=='1') selected #end>企业</option>
							<option value="2" #if(supplier.type??=='2') selected #end>个人</option>
						</select>
					</div>
				</div>

				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>电话号码</label>
					<div class="layui-input-inline" style="width: 183px;">
						<input type="text" name="telephone" value="#(supplier.telephone??)" class="layui-input" lay-verify="required|number" placeholder="请输入供应商电话号码" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-row" id="idcardTypeDiv" #if(supplier.type??=='2') style="display: block;" #else style="display: none;" #end >
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>证件类型</label>
					<div class="layui-input-inline" style="width: 183px;">
						<select name="idcardType" id="idcardType"  #if(supplier.type??=='2') lay-verify="required" #else lay-verify="" #end>
							<option value="">请选择供证件类型</option>
							#dictOption("id_card_type", supplier.idcardType??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>证件号码</label>
					<div class="layui-input-inline" style="width: 183px;">
						<input type="text" name="idcard" id="idcard" #if(supplier.type??=='2') lay-verify="required|checkIdCard" #else lay-verify="" #end value="#(supplier.idcard??)" autocomplete="off" class="layui-input">
					</div>
				</div>
			</div>

			<div class="layui-row">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>关联会员卡</label>
					<div class="layui-input-inline" style="width: 183px;">
						<input type="text"   id="cardNumber" lay-verify="" readonly value="#if(cardNumber!=null)#(cardNumber??)(#(fullName??))#end" autocomplete="off" class="layui-input">
						<input type="hidden" name="cardId" id="cardId" value="#(supplier.cardId??)">
					</div>
				</div>
				<button class="layui-btn" id="selectCardBtn" type="button" >选择</button>
			</div>

            <div class="layui-row">
            	<div class="layui-inline">
	                <label class="layui-form-label">邮政编码</label>
	                <div class="layui-input-block">
	                    <input type="text" name="postalCode" value="#(supplier.postalCode??)" autocomplete="off" placeholder="请输入邮政编码" class="layui-input">
	                </div>
            	</div>
                <div class="layui-inline">
	                <label class="layui-form-label">传真号码</label>
	                <div class="layui-input-block">
	                    <input type="text" name="faxNumber" value="#(supplier.faxNumber??)" autocomplete="off" placeholder="请输入传真号码" class="layui-input">
	                </div>
            	</div>
            </div>
            <div class="layui-row">
            	<div class="layui-inline">
	                <label class="layui-form-label">固定电话</label>
	                <div class="layui-input-block">
	                    <input type="text" name="fixedTelephone" value="#(supplier.fixedTelephone??)" autocomplete="off" placeholder="请输入固定电话" class="layui-input">
	                </div>
            	</div>
                <div class="layui-inline">
	                <label class="layui-form-label">邮箱</label>
	                <div class="layui-input-block">
	                    <input type="text" name="mailBox" value="#(supplier.mailBox??)" autocomplete="off" placeholder="请输入邮箱" class="layui-input">
	                </div>
            	</div>
            </div>
            <div class="layui-row">
            	<div class="layui-inline">
	                <label class="layui-form-label" style="padding: 8px 5px;">社会信用代码</label>
	                <div class="layui-input-block">
	                    <input type="text" name="socialCreditCode" value="#(supplier.socialCreditCode??)" autocomplete="off" placeholder="请输入社会信用代码" class="layui-input">
	                </div>
            	</div>
                <div class="layui-inline">
	                <label class="layui-form-label">开户名称</label>
	                <div class="layui-input-block">
	                    <input type="text" name="openName" value="#(supplier.openName??)" autocomplete="off" placeholder="请输入开户名称" class="layui-input">
	                </div>
            	</div>
            </div>
            <div class="layui-row">
            	<div class="layui-inline">
	                <label class="layui-form-label">开户银行</label>
	                <div class="layui-input-block">
	                    <input type="text" name="openBank" value="#(supplier.openBank??)" autocomplete="off" placeholder="请输入开户银行" class="layui-input">
	                </div>
            	</div>
                <div class="layui-inline">
	                <label class="layui-form-label">银行帐号</label>
	                <div class="layui-input-block">
	                    <input type="text" name="bankAccount" value="#(supplier.bankAccount??)" autocomplete="off" placeholder="请输入银行帐号" class="layui-input">
	                </div>
            	</div>
            </div>
            <div class="layui-row">
            	<div class="layui-inline">
	                <label class="layui-form-label">发票抬头</label>
	                <div class="layui-input-block">
	                    <input type="text" name="invoiceHeader" value="#(supplier.invoiceHeader??)" autocomplete="off" placeholder="请输入发票抬头" class="layui-input">
	                </div>
            	</div>
                <div class="layui-inline">
	                <label class="layui-form-label" style="padding: 8px 5px;">开始合作时间</label>
	                <div class="layui-input-block">
	                    <input type="text" id="startTime" name="startTime" value="#date(supplier.startTime??, "yyyy-MM-dd")" autocomplete="off" placeholder="请选择开始合作时间" class="layui-input">
	                </div>
            	</div>
            </div>
            #if(com.jfinal.kit.StrKit::notBlank(supplier.id??))
	            <div class="layui-row">
	                <label class="layui-form-label">主要联系人</label>
					<div class="layui-input-block">
						<select id="linkId" name="linkId" lay-search>
							<option value="">请选择</option>
							#for(l : linkList??)
								<option value="#(l.id)" #(supplier!=null ? (l.id== supplier.linkId?'selected':'') :'')>#(l.linkName)</option>
			                #end
						</select>
					</div>
	            </div>
			#end
            <div class="layui-row">
               	<label class="layui-form-label">区域</label>
	            <div class="layui-input-block" style="width:69%;">
	                <input type="hidden" id="province" name="province" value="#(supplier.province??)"/>
	                <input type="hidden" id="city" name="city" value="#(supplier.city??)"/>
	                <input type="hidden" id="town" name="town" value="#(supplier.town??)">
	                <input type="hidden" id="street" name="street" value="#(supplier.street??)">
	                <input type="hidden" id="regidentProvinceName" value="#(province??)"/>
	                <input type="hidden" id="regidentCityName" value="#(city??)"/>
	                <input type="hidden" id="regidentCountyName" value="#(town??)">
	                <input type="hidden" id="regidentStreetName" value="#(street??)">
	                <input type="text" id="regidentAddress" style="width: 80%;display: inline-block;" readonly="readonly"
	                       name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
	                <button class="layui-btn layui-btn-sm" style="display: inline-block;" type="button" id="areaClear">清空
	                </button>
	            </div>
            </div>
            <div class="layui-row">
                <label class="layui-form-label">地址</label>
                <div class="layui-input-block">
                    <input type="text" name="supplierAddress" value="#(supplier.supplierAddress??)" autocomplete="off" placeholder="请输入地址" class="layui-input">
                </div>
            </div>
            <div class="layui-row">
                <label class="layui-form-label">等级</label>
                <div class="layui-input-block">
					<select id="supplierLv" name="supplierLv"lay-verify="">
						<option value="">请选择</option>
						#dictOption("supplier_level", supplier.supplierLv??'', "")
					</select>
                </div>
            </div>
            <div class="layui-row">
                <label class="layui-form-label"><font color="red">*</font>是否有效</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnabled" title="是" #if(supplier.isEnabled==null || supplier.isEnabled??=='1') checked #end value="1" autocomplete="off" lay-verify="required">
                    <input type="radio" name="isEnabled" title="否" #if(supplier!=null && supplier.isEnabled??=='0') checked #end value="0" autocomplete="off" lay-verify="required">
                </div>
            </div>
            <div class="layui-row">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea name="description" placeholder="请输入描述" class="layui-textarea">#(supplier.description??)</textarea>
                </div>
            </div>
            <div class="layui-form-item" style="height:50px;"></div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(supplier.id??)" />
                    <input type="hidden" id="goodsTypeIds" name="goodsTypeIds" value="#(goodsTypeIds??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script id="regidentAreaTpl" type="text/html">
    <div id="regidentArea"
         style="width:600px;height:300px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
        <div class="tabs clearfix">
            <ul>
                <li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()"
                       class="current">省份</a></li>
                <li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()">城市</a></li>
                <li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
                <li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()">街道/镇/乡</a></li>
            </ul>
        </div>
        <div class="con">
            <div class="regidentProvinceAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentCityAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentTownAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentStreetAll">
                <div class="list">

                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
	var $ ;
    layui.use(['form', 'laydate', 'laytpl', 'upload','jquery'],function(){
        var form = layui.form;
        var laydate = layui.laydate;
        var laytpl = layui.laytpl;
		$ = layui.$;
        
      	//时间渲染
        laydate.render({
            elem : '#startTime'
            ,trigger:'click'
        });

		//校验
		form.verify({
			checkIdCard:function (value) {

				if($("#idCardType").val()==='sheng_fen_zheng'){
					var idCard=/(^\d{15}$)|(^\d{17}(x|X|\d)$)/
					if(!idCard.test(value)){
						return '请输入正确的身份证号';
					}
				}else{
					var idCard=/^[\u4E00-\u9FA5]+$/;
					if(idCard.test(value)){
						return '请输入正确的港澳台证件号码';
					}
				}

			}
		});

		//选择会员卡按钮点击事件
		$("#selectCardBtn").on('click',function () {
			layerShow('选择会员卡','#(ctxPath)/wms/supplier/selectCardTable',1000,600);
		});

		form.on('select(type)',function (obj) {
			if(obj.value=='2'){
				$("#idcardTypeDiv").css("display","block");
				$("#idcardType").attr("lay-verify","required");
				$("#idcard").attr("lay-verify","required|checkIdCard");
			}else{
				$("#idcardTypeDiv").css("display","none");
				$("#idcardType").attr("lay-verify","");
				$("#idcard").attr("lay-verify","");
			}
		})

      	
        goodsTypeSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
            var goodsTypeXmSelectCheck = xmSelect.render({
                    el: '#' + elem
                    , prop: {
                        name: 'name'
                        , value: 'id'
                    }
                    , toolbar: {show: true}
                    , filterable: true
                    , direction: 'down'
                    , tips: '无'
                    , tree: {
                        show:true
                    }
                    , height:'300px'
                    , autoRow: true
                    , on: function (data) {
                    var dataArray = data.arr;//data.arr:  当前多选已选中的数据
                    if (data.change) {//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                        if (dataArray.length > 0) {
                            var dArray = new Array();
                            for (var i = 0; i < dataArray.length; i++) {
                                dArray.push(dataArray[i].id);
                            }
                            $('#' + hiddenTargetId).val(dArray.toString());
                        } else {
                            $('#' + hiddenTargetId).val('');
                        }
                    }
                }
            });
            $.post('#(ctxPath)/wms/stockType/stockTypeTree', {}, function (rep) {
				var initValueArray = null;
				if (dataValue.indexOf(",") != -1) {
				    initValueArray = dataValue.split(',');
				    goodsTypeXmSelectCheck.update({
				        data: rep
				        , initValue: initValueArray
				    })
				} else {
				    goodsTypeXmSelectCheck.update({
				        data: rep
				        , initValue: [dataValue]
				    })
				}
            });
        };
        
        $('div[class="xm-select-demo"]').each(function (i, obj) {
            var targetId = obj.id;
            var hiddenTargetId = $(obj).attr('hiddenTargetId') != null ? $(obj).attr('hiddenTargetId') : '';
            var dataValue = $(obj).attr('dataValue') != null ? $(obj).attr('dataValue') : '';
            if(targetId=='goodsTypeSelectTree'){
            	goodsTypeSelectCheckLoad(targetId, hiddenTargetId, dataValue);
            }
        });
      	
        //清空区域
        $("#areaClear").on("click", function () {
            $("#province").val("");
            $("#city").val("");
            $("#town").val("");
            $("#street").val("");
            $("#regidentProvinceName").val("");
            $("#regidentCityName").val("");
            $("#regidentCountyName").val("");
            $("#regidentStreetName").val("");
            $("#regidentAddress").val("")
        });

        //--------------------------居住区域begin---------------------------
        $('#regidentAddress').on('click', function () {
            //closeIdArea();

            $('#regidentArea').remove();
            var $this = $(this);
            var getTpl = regidentAreaTpl.innerHTML;
            $this.parent().append(getTpl);
            //event.stopPropagation();

            var street = $("#street").val();
            var regidentStreetName = $("#regidentStreetName").val();
            var town = $("#town").val();
            var regidentCountyName = $("#regidentCountyName").val();
            var city = $("#city").val();
            var regidentCityName = $("#regidentCityName").val();
            var province = $("#province").val();
            var regidentProvinceName = $("#regidentProvinceName").val();
            if (street != '' && regidentStreetName != '') {
                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentStreetLoad(town);
                regidentCountyLoad(city);
                regidentCityLoad(province);
                regidentProvinceLoad();
                $(".con .regidentStreetAll").show().siblings().hide();
                //clickStreet(streetId,streetName);
            } else if (town != '' && regidentCountyName != '') {

                if (town != '') {
                    regidentCityLoad(province);
                    regidentCountyLoad(city);
                    regidentProvinceLoad();
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/area/getAreas',
                        data: {pid: town},
                        notice: false,
                        loadFlag: false,
                        success: function (res) {
                            if (res.state == 'ok') {
                                if (res.data.length > 0) {
                                    $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    var html = "<ul>";
                                    $.each(res.data, function (i, item) {
                                        html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.areaName + '\')">' + item.areaName + '</a></li>';
                                    });
                                    html += "</ul>";
                                    $(".regidentStreetAll .list").append(html);
                                    //viewStreet(countyId,countyName);
                                    $(".con .regidentStreetAll").show().siblings().hide();
                                } else {
                                    //无 街道信息
                                    $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    $(".con .regidentTownAll").show().siblings().hide();
                                }
                            }
                        },
                        complete: function () {
                        }
                    });
                }
            } else if (city != '' && regidentCityName != '') {
                regidentProvinceLoad();
                regidentCityLoad(province);
                viewRegidentCounty(city, regidentCityName);
            } else if (province != '' && regidentProvinceName != '') {
                regidentProvinceLoad();
                viewRegidentCity(province, regidentProvinceName);
            } else {
                regidentProvinceLoad();
            }

            //去除事件冒泡
            var evt = new Object;
            if (typeof (window.event) == "undefined") {//如果是火狐浏览器
                evt = arguments.callee.caller.arguments[0];
            } else {
                evt = event || window.event;
            }
            evt.cancelBubble = true;
            $('#regidentArea').off('click');
            $('#regidentArea').on('click', function () {
                //event.stopPropagation();
                //去除事件冒泡
                var evt = new Object;
                if (typeof (window.event) == "undefined") {//如果是火狐浏览器
                    evt = arguments.callee.caller.arguments[0];
                } else {
                    evt = event || window.event;
                }
                evt.cancelBubble = true;
            })
        });

        regidentProvinceLoad = function () {
            util.sendAjax({
                type: 'POST',
                url: '#(ctxPath)/area/getAreas',
                data: {pid: ''},
                notice: false,
                loadFlag: false,
                success: function (res) {
                    if (res.state == 'ok') {
                        if (res.data.length > 0) {
                            $(".regidentProvinceAll .list").empty();
                            var html = "<ul>";
                            $.each(res.data, function (i, item) {
                                html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCity(\'' + item.id + '\',\'' + item.areaName + '\')">' + item.areaName + '</a></li>';
                            });
                            html += "</ul>";
                            $(".regidentProvinceAll .list").append(html);
                        }
                    }
                },
                complete: function () {
                }
            });
        };

        //点击省事件
        viewRegidentCity = function (province, regidentProvinceName) {
            $("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentCityAll").show().siblings().hide();
            $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            //点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
            $("#province").val(province);
            $("#regidentProvinceName").val(regidentProvinceName);
            $("#city").val("");
            $("#regidentCityName").val("");
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCityLoad(province);
        };

        //加载市
        regidentCityLoad = function (province) {
            if (province != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: province},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $(".regidentCityAll .list").empty();
                                var html = "<ul>";
                                $.each(res.data, function (i, item) {
                                    html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCounty(\'' + item.id + '\',\'' + item.areaName + '\')">' + item.areaName + '</a></li>';
                                });
                                html += "</ul>";
                                $(".regidentCityAll .list").append(html);
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        //点击市事件
        viewRegidentCounty = function (city, RegidentCityName) {
            $("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentTownAll").show().siblings().hide();
            $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#city").val(city);
            $("#regidentCityName").val(RegidentCityName);
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCountyLoad(city);
        };

        //加载区/县
        regidentCountyLoad = function (city) {
            if (city != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: city},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $(".regidentTownAll .list").empty();
                                var html = "<ul>";
                                $.each(res.data, function (i, item) {
                                    html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentStreet(\'' + item.id + '\',\'' + item.areaName + '\')">' + item.areaName + '</a></li>';
                                });
                                html += "</ul>";
                                $(".regidentTownAll .list").append(html);
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        //点击区/县事件
        viewRegidentStreet = function (town, regidentCountyName) {
            $("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentStreetAll").show().siblings().hide();
            $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#town").val(town);
            $("#regidentCountyName").val(regidentCountyName);
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentStreetLoad(town);
        };

        //加载街道/镇/乡
        regidentStreetLoad = function (town) {
            if (town != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: town},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $(".regidentStreetAll .list").empty();
                                var html = "<ul>";
                                $.each(res.data, function (i, item) {
                                    html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.areaName + '\')">' + item.areaName + '</a></li>';
                                });
                                html += "</ul>";
                                $(".regidentStreetAll .list").append(html);
                            } else {
                                //无 街道信息
                                clickRegidentStreet('', '');
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        clickRegidentStreet = function (street, regidentStreetName) {
            $("#street").val(street);
            $("#regidentStreetName").val(regidentStreetName);
            var regidentProvinceName = $("#regidentProvinceName").val();
            var regidentCityName = $("#regidentCityName").val();
            var regidentCountyName = $("#regidentCountyName").val();
            var regidentStreetName = $("#regidentStreetName").val();
            var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
            $("#regidentAddress").val(add);
            $('#regidentArea').remove();
        };

        regidentProvinceAllClick = function () {
            //$(".con .provinceAll").show();
            $("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentProvinceAll").show().siblings().hide();
        };

        regidentCityAllClick = function () {
            // $(".con .cityAll").show();
            if ($("#province").val() != '') {
                $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCityLoad($("#province").val());
                $(".con .regidentCityAll").show().siblings().hide();
            }
        };

        regidentTownAllClick = function () {
            if ($("#city").val() != '') {
                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCountyLoad($("#city").val());
                $(".con .regidentTownAll").show().siblings().hide();
            }
        };

        regidentStreetAllClick = function () {
            if ($("#town").val() != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: $("#town").val()},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                regidentStreetLoad($("#town").val());
                                $(".con .regidentStreetAll").show().siblings().hide();
                            } else {
                                //无 街道信息 显示区/县信息
                                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                //countyLoad(cityId);
                                $(".con .regidentTownAll").show().siblings().hide();
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        $('body').on('click', function () {
            closeRegidentArea();
        });

        //关闭区域选择器
        closeRegidentArea = function () {
            if (typeof ($('#regidentArea').html()) != 'undefined') {
                var regidentProvinceName = $("#regidentProvinceName").val();
                var regidentCityName = $("#regidentCityName").val();
                var regidentCountyName = $("#regidentCountyName").val();
                var regidentStreetName = $("#regidentStreetName").val();
                var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
                $("#regidentAddress").val(add);
            }
            //alert(1);
            $('#regidentArea').remove();
        }
		//-------------------------居住区域end----------------------------

        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/supplier/saveSupplier',
                data: $("#supplierForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.supplierTableReload(null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end