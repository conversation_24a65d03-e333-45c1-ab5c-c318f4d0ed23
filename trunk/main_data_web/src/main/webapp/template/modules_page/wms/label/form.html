#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form" style="margin-left: 10px;margin-top: 20px;" id="roomTypeForm">
    <input type="hidden" id="id" name="id" value="#(model.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">标签名称</label>
        <div class="layui-input-inline">
            <input type="text" name="labelName" class="layui-input" lay-verify="required" value="#(model.labelName??)" placeholder="请输入类型名称">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-inline">
            <select id="isEnabled" name="isEnabled" lay-verify="required">
                <option value="1" #(model != null ?(model.isEnabled == '1' ? 'selected':''):'')>可用</option>
                <option value="0" #(model != null ?(model.isEnabled == '0' ? 'selected':''):'')>不可用</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-inline">
            <input type="text" name="sort" class="layui-input" lay-verify="required|number" value="#(model.sort??)" placeholder="请输入排序">
        </div>
    </div>
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="description" placeholder="请输入内容" class="layui-textarea">#(model.description??)</textarea>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
            <input type="hidden" id="fileId" name="roomType.fileId" value="#(roomType.fileId??)"/>
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/wms/label/saveStockLabel";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#roomTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });



    });
</script>
#end
