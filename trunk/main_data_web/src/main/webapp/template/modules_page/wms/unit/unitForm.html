#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()计量单位表单#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="unitForm">
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>单位名称</label>
            <div class="layui-input-block">
                <input type="text" name="unitName" value="#(model.unitName??)" autocomplete="off" placeholder="请输入单位名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>是否小数</label>
            <div class="layui-input-block">
                <input type="radio" name="isDecimal" value="0" title="否" #if(model.isDecimal=='0'||model.isDecimal??''=='') checked #end>
        		<input type="radio" name="isDecimal" value="1" title="是" #if(model.isDecimal??=='1') checked #end>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <input name="id" type="hidden" value="#(model.id??)" />
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate','jquery'],function(){
        var form = layui.form;
        var laydate = layui.laydate;
        var $ = layui.$;
        
        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/unit/saveUnit',
                data: $("#unitForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.unitTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end