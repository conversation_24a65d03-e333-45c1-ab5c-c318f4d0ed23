#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<div class="layui-upload" style="padding: 5px 10px;">
    <button type="button" class="layui-btn layui-btn-normal" id="testList">选择多文件</button>
    <button type="button" class="layui-btn" id="testListAction">开始上传</button>
    <button type="button" class="layui-btn" id="sortBtn">保存排序</button>
    <form class="layui-form" action="post" enctype="multipart/form-data">
        <div class="layui-upload-list">
            <table class="layui-table">
                <thead>
                    <tr>
                        <th>文件名</th>
                        <th>大小</th>
                        <th>状态</th>
                        <th>图片</th>
                        <th>排序</th>
                        <th>是否为主图</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="demoList">
                #for(img : imageList)
                <tr id="upload-#(for.index)">
                    <td>#(img.imgName??)</td>
                    <td></td>
                    <td>上传成功</td>
                    <td id="imgtr-'+index+'">
                        #if(img.isImag??)
                        <img id="img-'+index+'" src="#(img.imgUrl??)" style="cursor:pointer;" onclick="imgClick('+index+')" >
                        #else if(img.isVideo??)
                        <video id="video-'+index+'" src="#(img.imgUrl??)" controls width="200" height="150"> </video>
                        #end
                    </td>
                    <td>
                        <div class="layui-input-inline" style="width: 60px;">

                            <input type="text" data-id="#(img.id)" name="sort" value="#(img.sort??)" autocomplete="off" placeholder="排序" class="layui-input sortClass" lay-verify="required|number">

                        </div>
                    </td>
                    <td>
                        #if(img.isMain=='1')
                        <span class='layui-badge layui-bg-green'>是</span>
                        #else
                        <span class='layui-badge'>否</span>
                        #end
                    </td>

                    <td>
                        #if(img.isMain=='0')
                            #if(img.isImag??)
                            <button class="layui-btn layui-btn-xs" type="button" onclick="setMain('#(img.id)')">设为主图</button>
                            #end
                        #end
                        <button class="layui-btn layui-btn-xs layui-btn-danger demo-delete" type="button" onclick="imgDelete('#(img.id)','#(for.index)')">删除</button>
                    </td>
                </tr>
                #end
                </tbody>
            </table>
        </div>
    </form>
    <input type="hidden" id="modelId" value="#(modelId??)">
    <div id="photo-list">
        <img class="img_sp" src="" />
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload','layer'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload,layer=layui.layer;

        //多文件列表示例
        var demoListView = $('#demoList')
            ,uploadListIns = upload.render({
            elem: '#testList'
            ,url: '/wms/stockModel/uploadImg' //改成您自己的上传接口
            ,accept: 'file'
            ,multiple: true
            ,auto: false
            /*,accept:'images,video'*/
            ,bindAction: '#testListAction'
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){

                    let suffixIndex = file.name.lastIndexOf('.');
                    let fileType=suffixIndex > 0 ? file.name.substring(suffixIndex + 1) : '';

                    const allowExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp'];
                    const allowExts2=['mp4', 'mov', 'avi', 'mkv'];

                    if(!allowExts.includes(fileType) && !allowExts2.includes(fileType)){
                        layer.msg('只限上传图片和视频类型文件', {icon: 2, offset: 'auto'});
                        return false;
                    }
                    var tr ='';
                    if(allowExts.includes(fileType)){
                        tr = $(['<tr id="upload-'+ index +'">'
                            ,'<td>'+ file.name +'</td>'
                            ,'<td>'+ (file.size/1024).toFixed(1) +'kb</td>'
                            ,'<td>等待上传</td>'
                            ,'<td id="imgtr-'+index+'"><img id="img-'+index+'" src="'+result+'" style="cursor:pointer;" onclick="imgClick('+index+')" ></td>'
                            ,'<td></td>'
                            ,'<td></td>'
                            ,'<td>'
                            ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide" type="button">重传</button>'
                            ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete" type="button" onclick="imgDelete("",'+index+')">删除</button>'
                            ,'</td>'
                            ,'</tr>'].join(''));
                    }else if(allowExts2.includes(fileType)){
                        tr = $(['<tr id="upload-'+ index +'">'
                            ,'<td>'+ file.name +'</td>'
                            ,'<td>'+ (file.size/1024).toFixed(1) +'kb</td>'
                            ,'<td>等待上传</td>'
                            ,'<td id="imgtr-'+index+'" >' +
                                    '<video src="'+result+'" controls width="200" height="150">' +
                            '   ' +
                            '</video>'+
                            /*'<img id="img-'+index+'" src="'+result+'" style="cursor:pointer;" onclick="imgClick('+index+')" >' +*/
                            '</td>'
                            ,'<td></td>'
                            ,'<td></td>'
                            ,'<td>'
                            ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide" type="button">重传</button>'
                            ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete" type="button" onclick="imgDelete("",'+index+')">删除</button>'
                            ,'</td>'
                            ,'</tr>'].join(''));
                    }


                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                    });
                    //删除
                    /*tr.find('.demo-delete').on('click', function(){

                    });*/
                    demoListView.append(tr);
                    form.render();
                });
            }
            ,before:function (obj) {
                this.data={"modelId": $("#modelId").val()}//携带额外的数据
                layer.load();
            }
            ,done: function(res, index, upload){
                setTimeout(function () {
                    layer.closeAll('loading');
                    pop_close();
                    parent.modelImageIndex($("#modelId").val());
                },500);
                if(res.state=='ok'){ //上传成功
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                    var mainBtn='';
                    if(res.data.isMain==='0'){
                        mainBtn='<button class="layui-btn layui-btn-xs" type="button" onclick="setMain('+res.data.id+')">设为主图</button>';
                        tds.eq(4).html('<span class=\'layui-badge layui-bg-green\'>是</span>');
                    }else{
                        tds.eq(4).html('<span class=\'layui-badge\'>否</span>');
                    }
                    tds.eq(5).html(mainBtn +
                        '<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete" type="button" onclick="imgDelete("",'+index+')">删除</button>'); //清空操作
                    return delete this.files[index]; //删除文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                tds.eq(5).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });

        imgDelete=function(id,index){
            if(id===''){
                //delete files[index]; //删除对应的文件
                $("$upload-"+index).remove();
                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
            }else{
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/wms/stockModel/deleteImg',
                        notice: true,
                        data: {'id':id,},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pop_close();
                                parent.modelImageIndex($("#modelId").val());
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }

        }

        setMain=function(id){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/stockModel/saveModelImage',
                notice: true,
                data: {'id':id,'isMain':'1','modelId':$("#modelId").val()},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.modelImageIndex($("#modelId").val());
                    }
                },
                complete : function() {
                }
            });
        }

        $("#sortBtn").on('click',function () {
            var data=[];
            $(".sortClass").each(function (index,item) {
                var id=$(item).attr("data-id");
                var sort=$(item).val();
                if(id!=undefined && id!=''){
                    data.push({"id":id,"sort":sort})
                }
            });
            if(data.length<0){
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/stockModel/saveModelImageSort',
                notice: true,
                data: {'data':JSON.stringify(data)},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.modelImageIndex($("#modelId").val());
                    }
                },
                complete : function() {
                }
            });
        })

        imgClick=function(index){
            /*$(this).attr("src");
            parent.layer.photos({
                photos: '#demo',
                shadeClose: false,
                closeBtn: 2,
                anim: 0
            });

            parent.layer.open({
                type: 1,
                skin: 'layui-layer-rim', //加上边框
                area: ['80%', '80%'], //宽高
                shadeClose: true, //开启遮罩关闭
                end: function (index, layero) {
                    return false;
                },
            });*/
        }
    });
</script>
#end



