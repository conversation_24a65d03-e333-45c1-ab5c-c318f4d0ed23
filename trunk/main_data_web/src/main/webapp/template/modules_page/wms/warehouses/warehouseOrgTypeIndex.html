#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()库区表单#end

#define css()
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="warehousesArea">
            <table class="layui-table">
                <thead>
                <col style="width:60%;" />
                <col style="width: 20%;"/>
                <col style="width:20%;" />
                <tr>
                    <th>组织机构</th>
                    <th>类型</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody id="tbody">
                #for(warehouseOrgType : warehouseOrgTypeList)
                <tr id="tr-#(for.index+1)">
                   <!-- <td>#(warehouseOrgType.orgName??)
                        <input name="warehouseOrgType[#(for.index+1)].orgId" type="hidden" value="#(warehouseOrgType.orgId)">
                    </td>-->
                    <td>
                        <div id="deptSelect-#(for.index+1)" style="">

                        </div>
                    </td>
                    <td>
                        <select name="warehouseOrgType[#(for.index+1)].type" id="warehouseOrgType#(for.index+1)" lay-verify="required">
                            <option value="">请选择</option>
                            #for(type : warehouseOrgTypes)
                            <option value="#(type.key)" #if(warehouseOrgType.type??==type.key) selected  #end>#(type.value)</option>
                            #end
                        </select>
                    </td>
                    <td><button class="layui-btn layui-btn-danger layui-btn-sm" type="button" onclick="delAdmin('',#(for.index+1));">作废</button></td>

                </tr>
                #end
                </tbody>
            </table>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="warehouseId" type="hidden" value="#(warehouseId??)" />
                    <input name="count" id="count" type="hidden" value="#(warehouseOrgTypeList.size()??0)" >
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="addBtn" class="layui-btn" type="button" >添加</button>

                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>

</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="settingTpl">
    <tr id="tr-{{d.idx}}">


        <td>
            <div id="deptSelect-{{d.idx}}" style="">

            </div>
        </td>
        <td>
            <select name="warehouseOrgType[{{d.idx}}].type" id="warehouseOrgType{{d.idx}}" lay-verify="required">
                <option value="">请选择</option>
                #for(type : warehouseOrgTypes)
                <option value="#(type.key)" >#(type.value)</option>
                #end
            </select>
        </td>
        <td><button class="layui-btn layui-btn-danger layui-btn-sm" type="button" onclick="delAdmin('',{{d.idx}});">作废</button></td>
    </tr>
</script>
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form', 'laydate', 'upload','jquery','laytpl'],function(){
        var form = layui.form;
        var laytpl=layui.laytpl;
        var $ = layui.$;

        $("#addBt2n").on('click',function () {

        })

        $.post('#(ctxPath)/wms/warehouse/orgXmSelectTree',{},function (res) {
            #for(warehouseOrgType:warehouseOrgTypeList)
            var deptSelectInitValue = ["#(warehouseOrgType.orgId)"];
            xmSelect.render({
                el: '#deptSelect-#(for.index+1)',
                autoRow: true,
                filterable: true,
                radio: true,
                height: '200px',
                prop: {
                    name: 'name',
                    value: 'id',
                },
                tree: {
                    show: true,
                    showFolderIcon: true,
                    showLine: true,
                    indent: 15,
                    lazy: true,
                    clickExpand: true,

                    strict: false,
                    expandedKeys: ['54325705-FF63-43DB-9723-FA31E94AF8E3'],
                    //点击节点是否选中
                    clickCheck: true,
                    load: function (item, cb) {

                    }
                },
                height: 'auto',
                data() {
                    return res;
                },
                initValue: deptSelectInitValue
            });
            #end
        })
        //保存
        form.on('submit(confirmBtn)', function(obj){

            //var selects=$(".userSelect");
            var flag=false;
            var treeData=[]

            var saveData=$("#warehousesArea").serialize();

            $("#tbody tr").each(function (index,item) {
                var trId=$(item).attr("id")
                var trIndex=trId.split("-")[1];
                if(xmSelect.get('#deptSelect-'+trIndex,true).getValue().length>0){
                    treeData.push({'id':xmSelect.get('#deptSelect-'+trIndex,true).getValue()[0].id,'type':$("#warehouseOrgType"+trIndex).val()});
                    saveData+="&warehouseOrgType["+trIndex+"].orgId="+xmSelect.get('#deptSelect-'+trIndex,true).getValue()[0].id;
                }
            });
            $.each(treeData,function (index,item) {
                $.each(treeData,function (i,it) {
                    if(i!=index && item.id==it.id && item.type==it.type){
                        console.log(index+"__"+JSON.stringify(item))
                        console.log(i+"__"+JSON.stringify(it))
                        flag=true;
                        return false;
                    }
                })
            });

            if(flag){
                layer.msg('请勿重复添加', {icon: 2, offset: 'auto'});
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/warehouse/warehouseOrgTypeSave',
                data: saveData,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        //parent.warehouseManagerForm();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        delAdmin=function (id,index) {
            if(id===''){
                $('#tr-'+index).remove();
            }else{
                layer.confirm('是否要删除?', function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/wms/warehouse/delAdmin',
                        data: {'id':id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pop_close();
                                parent.warehouseManagerForm();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
        }



        $("#addBtn").on('click',function () {
            var idx=Number($("#count").val())+1;
            laytpl(settingTpl.innerHTML).render({"idx": idx}, function(html){
                $("#tbody").append(html);
                $("#count").val(idx);
            });
            $.post('#(ctxPath)/wms/warehouse/orgXmSelectTree',{},function (res) {
                xmSelect.render({
                    el: '#deptSelect-'+idx,
                    autoRow: true,
                    filterable: true,
                    radio: true,
                    height: '200px',
                    prop: {
                        name: 'name',
                        value: 'id',
                    },
                    tree: {
                        show: true,
                        showFolderIcon: true,
                        showLine: true,
                        indent: 15,
                        lazy: true,
                        clickExpand: true,
                        strict: false,
                        expandedKeys: ['54325705-FF63-43DB-9723-FA31E94AF8E3'],
                        //点击节点是否选中
                        clickCheck: true,
                        load: function (item, cb) {

                        }
                    },
                    height: 'auto',
                    data() {
                        return res;
                    },
                    initValue: ''
                });
            });


            form.render();
        });

    });
</script>
#end