#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()货物分类#end

#define css()
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row">
    <form id="frm" class="layui-form" lay-filter="layform" action="" method="post">
		<div class="layui-inline">
			<label class="layui-form-label">是否有效</label>
			<div class="layui-input-inline" style="width:100px;">
				<select id="isEnabled" name="isEnabled">
					<option value="">全部</option>
					<option value="0">否</option>
					<option value="1" selected="selected">是</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<button type="button" id="search"  class="layui-btn" lay-submit="" lay-filter="search">查询</button>
	    	#shiroHasPermission("wms:stockType:editBtn")
		        <button type="button" id="addBtn" class="layui-btn">添加</button>
	        #end
		</div>
    </form>
    </div>
    <div class="layui-row">
        <table id="typeTreeGrid" lay-filter="typeTreeGrid"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()

<script type="text/html" id="toolBar">
	#shiroHasPermission("wms:stockType:editBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['treeGrid','form','vip_table'], function() {
         var $ = layui.$, form=layui.form,layer=layui.layer,treeGrid=layui.treeGrid,vipTable=layui.vip_table;

        // 初始化表格
       	treeGridLoad = function (data) {
	        treeGrid.render({
	            id: 'typeTreeGrid'
	            , elem: '#typeTreeGrid'
	            , method: 'post'
	            , url: '#(ctxPath)/wms/stockType/stockTypeTableTree'
	            , where: data
	            , height: vipTable.getFullHeight()    //容器高度
	            , idField: 'id'//必須字段
	            , treeId: 'id'//树形id字段名称
	            , treeUpId: 'pId'//树形父id字段名称
	            , treeShowName: 'name'//以树形式显示的字段
	            , isOpenDefault: true//节点默认是展开还是折叠【默认展开】
	            , cols: [[
	                {field: '', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
	                ,{field:'name', title:'名称', unresize:true}
	                ,{field:'type', title:'编号', unresize:true}
	                ,{title:'操作', width:200, unresize:true, align:'center', templet:"#toolBar"}
	            ]]
	        });
	        //表格事件绑定
	        treeGrid.on('tool(typeTreeGrid)',function (obj) {
	            if(obj.event==="edit"){//编辑按钮事件
	                pop_show('编辑','#(ctxPath)/wms/stockType/typeForm?id='+obj.data.id,700,600);
	            }
	        });
        }
        
        treeGridLoad({isEnabled:$('#isEnabled').val()});

        form.on("submit(search)",function(data){
        	treeGridLoad(data.field);
    		return false;
    	});
        
        $("#addBtn").on('click',function () {
            pop_show('编辑','#(ctxPath)/wms/stockType/typeForm',700,600);
        });
    });
</script>
#end