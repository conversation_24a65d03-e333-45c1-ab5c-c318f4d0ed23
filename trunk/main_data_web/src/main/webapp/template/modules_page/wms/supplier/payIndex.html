#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商付款信息页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div id="supplierPayTab" class="layui-tab layui-tab-brief" lay-filter="supplierPayTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<form id="frm" class="layui-form" lay-filter="layform" action="" method="post">
				<div class="layui-row">
					<div class="layui-inline">
						<label class="layui-form-label">付款方式</label>
						<div class="layui-input-inline" style="width:150px;">
							<select name="paymentWay">
								<option value="">全部</option>
								#for(payWay : payWay)
									<option value="#(payWay.key??)">#(payWay.value??)</option>
								#end
							</select>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">付款账号</label>
						<div class="layui-input-inline">
							<input id="payAccount" name="payAccount" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<input type="hidden" id="supplierId" name="supplierId" value="#(suppliersId??)">
						<button type="button" id="search"  class="layui-btn" lay-submit="" lay-filter="search">查询</button>
						<button type="button" id="addBtn" class="layui-btn">添加</button>
					</div>
				</div>
				</form>
				<div class="layui-row">
					<table id="supplierPayTable" lay-filter="supplierPayTable"></table>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:delBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</div>
</script>
<script type="text/html" id="isEnabledTpl">
  <!-- 这里的 checked 的状态只是演示 -->
  <input type="checkbox" name="isEnabled" value="{{d.id}}" lay-skin="switch" lay-text="有效|无效" lay-filter="isEnabledFilter" {{ d.isEnabled=='1' ? 'checked' : '' }}>
</script>
<script type="text/javascript">
layui.use([ 'table', 'form', 'element' ], function() {
	var table = layui.table
	, form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
	function payLoad(data){
		table.render({
			id : 'supplierPayTable'
			,elem : '#supplierPayTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/wms/supplier/payPage'
// 			,cellMinWidth: 80
			,height:'full-150'
			,cols: [[
				{field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
				,{field:'', title: '付款方式', align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.PayWay::me(), "paymentWay")'}
				,{field:'payAccount', title: '付款账号',width: 180, align: 'center', unresize: true}
				,{field:'bankType', title: '银行卡类别', align: 'center', unresize: true}
				,{field:'bankAccountName', title: '户主姓名', align: 'center', unresize: true}
				,{field:'bankSource', title: '开户行', align: 'center', unresize: true}
				,{field: '', title: '是否有效', width: 120,unresize:true,templet:'#isEnabledTpl'}
				,{fixed:'right', title: '操作', width:120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 15
			,limits : [15,25,35,45]
			, loading: true
		    , done: function (res, curr, count) {
		    }
		});
		table.on('tool(supplierPayTable)',function(obj){
			if(obj.event==="edit"){//编辑按钮事件
				element.tabAdd('supplierPayTab', {
					title: '编辑'
					, content: '<div id="payForm"></div>'
					, id: 'form'
				});
				$('#payForm').load('#(ctxPath)/wms/supplier/payForm?id='+obj.data.id);
				element.tabChange('supplierPayTab', 'form');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm("确定要作废吗?",function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/wms/supplier/savePay',
	                    notice: true,
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                        	tableReload("supplierPayTable",{supplierId:$('#supplierId').val()});
	                        }
	                        layer.close(index);
	                    },
	                    complete : function() {
	                    }
	                });
	            });
	        }
		});
		//监听有效/无效操作
		form.on('switch(isEnabledFilter)', function(obj){
			var id = this.value;
			var dataFlag = '';
			if(obj.elem.checked){
				dataFlag = '1';
			}else{
				dataFlag = '0';
			}
			var data = {id:id, isEnabled:dataFlag};
			updateSupplierPay(data);
		});
	};
	
	payLoad({supplierId:$('#supplierId').val()});
	
	form.on("submit(search)",function(data){
		payLoad(data.field);
		return false;
	});
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('supplierPayTab', {
			title: '添加'
			, content: '<div id="payForm"></div>'
			, id: 'form'
		});
		$('#payForm').load('#(ctxPath)/wms/supplier/payForm?supplierId='+$('#supplierId').val());
		element.tabChange('supplierPayTab', 'form');
    });
    
    function updateSupplierPay(data){
   		//提交表单数据
   		util.sendAjax ({
               type: 'POST',
               url: '#(ctxPath)/wms/supplier/savePay',
               data: data,
               notice: true,
               loadFlag: true,
               success : function(rep){
               	if(rep.state=='ok'){
               		tableReload("supplierPayTable",{supplierId:$('#supplierId').val()});
               	}
               },
               complete : function() {
   		    }
           });
    }
    
	element.on('tab(supplierPayTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end