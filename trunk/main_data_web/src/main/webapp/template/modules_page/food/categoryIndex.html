#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜类别#end

#define css()
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row">
    	#shiroHasPermission("foodCategory:addBtn")
	        <button id="addBtn" type="button" class="layui-btn layui-btn-sm">添加</button>
        #end
    </div>
    <div class="layui-row">
        <table id="categoryTreeGrid" lay-filter="categoryTreeGrid"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()

<script type="text/html" id="toolBar">
	#shiroHasPermission("foodCategory:editBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['treeGrid','form','vip_table'], function() {
         var $ = layui.$, form=layui.form,layer=layui.layer,treeGrid=layui.treeGrid,vipTable=layui.vip_table;

        // 初始化表格
        var ptable=treeGrid.render({
            id: 'categoryTreeGrid'
            , elem: '#categoryTreeGrid'
            , url: '#(ctxPath)/food/categoryTableTree'
            , method: 'post'
            , height: vipTable.getFullHeight()    //容器高度
            , idField: 'id'//必須字段
            , treeId: 'id'//树形id字段名称
            , treeUpId: 'pId'//树形父id字段名称
            , treeShowName: 'name'//以树形式显示的字段
            , isOpenDefault: true//节点默认是展开还是折叠【默认展开】
            , cols: [[
                {field: '', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                ,{field:'type', title: '类别类型', width: 100, align: 'center', unresize: true}
                ,{field:'name', title:'类型名称', unresize:true}
                ,{field:'', title: '是否有效',width:100, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.pids=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                ,{title:'操作', width:200, unresize:true, align:'center', templet:"#toolBar"}
            ]]
        });

        categoryTreeGridTableReload=function(){
            treeGrid.reload('categoryTreeGrid');
        }

        //表格事件绑定
        treeGrid.on('tool(categoryTreeGrid)',function (obj) {
            if(obj.event==="edit"){//编辑按钮事件
                pop_show('编辑','#(ctxPath)/food/categoryForm?id='+obj.data.id,700,600);
            }
        });
        
        $("#addBtn").on('click',function () {
            pop_show('编辑','#(ctxPath)/food/categoryForm',700,600);
        });
    });
</script>
#end