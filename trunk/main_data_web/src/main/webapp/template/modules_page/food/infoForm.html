#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜信息表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
<link rel="stylesheet" href="#(ctxPath)/static/css/jquery.magnify.min.css">
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <form id="foodInfoForm" class="layui-form layui-form-pane" action="" lay-filter="layform">
        <div class="layui-row" style="padding-bottom: 50px;">
            <div class="layui-col-xs3" style="padding:5px 5px 5px 20px;">
                <fieldset class="layui-elem-field" >
                    <legend>类别</legend>
                    <div id="categoryTree" class="ztree" style="height:80%;overflow:auto;"></div>
                </fieldset>
            </div>
            <div class="layui-col-xs9" style="padding:5px 5px 5px 30px;">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span>*</span>所属类别</label>
                    <div class="layui-input-block">
                        <input type="text" id="categoryName" readonly  value="#(category.categoryName??)" autocomplete="off" placeholder="请选择类别" class="layui-input" lay-verify="required">
                        <input type="hidden" id="categoryId" name="categoryId" value="#(model.categoryId??)">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span>*</span>菜名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="foodName" value="#(model.foodName??)" autocomplete="off" placeholder="请输入菜名称" class="layui-input" lay-verify="required">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><span>*</span>是否有效</label>
                    <div class="layui-input-block">
                        <input type="radio" name="isEnabled" value="1" title="是" #(model != null ? ('1' == model.isEnabled ? 'checked':''):'checked')>
						<input type="radio" name="isEnabled" value="0" title="否" #(model != null ? ('0' == model.isEnabled ? 'checked':''):'')>
                    </div>
                </div>
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">描述</label>
                    <div class="layui-input-block">
                        <textarea name="remarks" placeholder="请输入描述" class="layui-textarea">#(model.remarks??)</textarea>
                    </div>
                </div>
				<div class="layui-form-item">
					<table class="layui-table">
						<colgroup>
						    <col width="35%">
						    <col width="35%">
						    <col width="30%">
						    <col>
						</colgroup>
						<thead>
							<tr>
								<th>文件名</th>
								<th>上传时间</th>
								<th>操作</th>
							</tr>
						</thead>
						<tbody id="upFileList"></tbody>
					</table>
				</div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(model.id??)"/>
                    <input type="hidden" id="fileCount" name="fileCount" value="0">
<!--                     <input type="hidden" id="fileSort" value="0"> -->
                    <button type="button" class="layui-btn" id="uploadBtn"><i class="layui-icon"></i>上传图片</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/jquery.magnify.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</script>
<script id='upFileTrTpl' type='text/html'>
<tr id="upFile-{{d.idx}}">
	<td>{{d.upFileName}}</td>
	<td>{{d.upDate}}</td>
	<td>
		<input type="hidden" id="upFileId-{{d.idx}}" name="fileList[{{d.idx}}].id" class="layui-input" value="{{d.upFileId}}">
		<input type="hidden" id="upFileRelationId-{{d.idx}}" name="fileList[{{d.idx}}].relationId" class="layui-input" value="{{d.upFileRelationId}}">
		<a data-magnify="gallery" class="layui-btn layui-btn-xs" href="{{d.upFileUrl}}">查看</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delFile('upFile-{{d.idx}}','{{d.upFileId}}')">删除</a>
	</td>
</tr>
</script>
<script>
    layui.use(['form','tree', 'laytpl', 'upload', 'util'], function() {
        var $ = layui.$
        , form=layui.form
        , layer=layui.layer
        , tree=layui.tree
        , laytpl=layui.laytpl
        , upload=layui.upload
        , layuiUtil = layui.util
        , infoId = '#(model.id??)'
        ;

        //树属性配置
        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/food/categoryTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#categoryId").val(treeNode.id);
                    $("#categoryName").val(treeNode.name);
                }
            }
        };

        // 设置顶层菜单按钮点击事件
        var zTreeObj = $.fn.zTree.init($("#categoryTree"), setting);
        
    	addTpl = function(targetId, addTpl, idx, upFileRelationId, upFileId, upFileName, upFileUrl, upDate) {
    		$('#fileCount').val(parseInt(idx)+1);
    		laytpl(addTpl).render({'idx':(parseInt(idx)+1), 'upFileRelationId':upFileRelationId, 'upFileId':upFileId, 'upFileName':upFileName, 'upFileUrl':upFileUrl, 'upDate':upDate}, function(html){
    			targetId.append(html);
    		});
        };
        
    	//指定允许上传的文件类型
    	upload.render({
    		elem:'#uploadBtn'
    		, url:'#(uploadDomain)/fileUpload' //改成您自己的上传接口
    		, accept:'images' //图片
    		, data:{
    			fileFolder:'food'
    			, createBy:'#(userId??)'
    		}
    		, before:function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
//     			layer.msg('上传前调用');
//     			console.log(obj);
// 				var fileSort = $('#upFileList tr').length+1;
//     			console.log('fileSort==='+fileSort);
//     			$('#fileSort').val(fileSort);
    		}
    		, done:function(res){
//     			layer.msg('上传成功');
//     			console.log(res);
    			if(res.state=='ok'){
    				addTpl($('#upFileList'), upFileTrTpl.innerHTML, $('#fileCount').val(), '', res.returnData.fileId, res.returnData.fileName, res.returnData.fileUrl, res.returnData.fileCreateTime);
    			}else{
    				layer.msg(res.msg,{icon:5});
    			}
    		}
    	});
        
        loadFileList=function(relationId) {
        	util.sendAjax ({
                type:'POST'
                , url:'#(uploadDomain)/fileList'
                , data:{relationId:relationId}
                , notice: false
                , success:function(rep){
                	if(rep.state=='ok'){
                		if(rep.fileList.length>0){
                			$("#fileCount").val(rep.fileList.length);
                		}else{
                			$("#fileCount").val(0);
                		}
                		$.each(rep.fileList, function (i,f){
                			var upDate = layuiUtil.toDateString(f.createTime, 'yyyy-MM-dd HH:mm:ss');
            				addTpl($('#upFileList'), upFileTrTpl.innerHTML, $('#fileCount').val(), f.relationId, f.id, f.fileName, f.fileUrl, upDate);
                		});
                	}
                }
                , complete:function() {
    		    }
            });
        }
        
        if(infoId!=null && infoId!=''){
        	loadFileList(infoId);
        }
        
        delFile=function(trId, fileId) {
    		if(fileId!=null && fileId!=''){
    			layer.confirm('确定要删除?', {icon:3, title:'询问'}, function(index){
    				util.sendAjax ({
                        type:'POST'
                        , url:'#(uploadDomain)/fileDel'
                        , data:{id:fileId, delFlag:'1', updateBy:'#(userId??)'}
                        , loadFlag:true
                        , success:function(rep){
                        }
                        , complete:function() {
            		    }
                    });
    				layer.close(index);
    				$('#'+trId).remove();
    			});
    		}else{
    			$('#'+trId).remove();
    		}
    	};

        //监听表单提交
        form.on('submit(confirmBtn)', function(formObj) {
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/food/saveInfo',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	parent.foodInfoTableReload();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        $(function(){
            document.onkeydown = function(e){
                var ev = document.all ? window.event : e;
                if(ev.keyCode==13) {
                    $("#confirmBtn").click();
                    return false;
                }
            }
			$('[data-magnify]').magnify({
				headerToolbar: [
					'close'
				],
				footerToolbar: [
					'zoomIn',
					'zoomOut',
					'prev',
					'fullscreen',
					'next',
					'actualSize',
					'rotateRight'
				],
				title: false
			});
        });
    });
</script>
#end