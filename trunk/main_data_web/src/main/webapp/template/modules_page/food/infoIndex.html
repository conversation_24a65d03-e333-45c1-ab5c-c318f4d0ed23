#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜信息页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="typeForm">
        <div class="layui-row">
            <div class="layui-col-xs2" style="padding:20px 5px 5px 20px;">
                <fieldset class="layui-elem-field">
                    <legend>分类</legend>
                    <div id="typeTree" class="ztree" style="height:80%;overflow:auto;"></div>
                </fieldset>
            </div>

            <div class="layui-col-xs10" style="padding:5px 5px 5px 20px;">
                <form class="layui-form" >
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-inline" style="float: left;margin-right: 10px;">
                        <input type="text" name="foodName" id="foodName" placeholder="请输入名称" autocomplete="off" class="layui-input">
                    </div>
                    <input type="hidden" name="categoryId" id="categoryId" >
                    <button class="layui-btn" style="margin-left: 20px;" type="button" id="search">搜索</button>
                    #shiroHasPermission("foodInfo:addBtn")
		            	<button id="addBtn" type="button" class="layui-btn">添加</button>
			        #end
                </form>
                <table class="layui-table" id="foodInfoTable" lay-filter="foodInfoTable"></table>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/html" id="toolBar">
	#shiroHasPermission("foodInfo:editBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
</script>
<script>


    layui.use(['form','tree','table'], function() {
        var $ = layui.$, form=layui.form,layer=layui.layer,tree=layui.tree,table=layui.table, rotateNum = 0;

        //树属性配置
        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/food/categoryTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#categoryId").val(treeNode.id);
                    table.reload('foodInfoTable',{'where':{'categoryId':treeNode.id}});
                }
            }
        };

        // 设置顶层菜单按钮点击事件
        var zTreeObj = $.fn.zTree.init($("#typeTree"), setting);


        table.render({
            id : 'foodInfoTable'
            ,elem : '#foodInfoTable'
            ,method : 'POST'
            ,where : ''
            ,limit : 13
            ,limits : [13,20,30,40]
            ,url : '#(ctxPath)/food/pageTable'
            ,height:$(document).height()*0.75
            ,cellMinWidth: 80
            ,cols: [[
            	{type: 'numbers', width:60, title: '序号',unresize:true}
                ,{field:'categoryName', title: '菜类别',width:120, align: 'center', unresize: true}
                ,{field:'foodName', title: '菜名称', align: 'center', unresize: true}
                ,{field:'', title: '是否有效',width:90, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.isEnabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                ,{field:'fileCount', title: '图片数量',width:90, align: 'center', unresize: true}
                ,{field:'remarks', title: '描述',width:200, align: 'center', unresize: true}
                ,{fixed:'right', title: '操作', width: 165, align: 'center', unresize: true, toolbar: '#toolBar'}
            ]]
            ,page : true
            ,done:function () {

            }
        });

        table.on('tool(foodInfoTable)',function (obj) {
            if(obj.event==='edit'){
                pop_show('编辑','#(ctxPath)/food/infoForm?id='+obj.data.id,700,750);
            }else if(obj.event==='image'){
                var url = "#(ctxPath)/wms/stockModel/modelImage?id=" + obj.data.id ;
                pop_show("商品图片",url,900,650);
            }
        });
        
        $(function(){
            document.onkeydown = function(e){
                var ev = document.all ? window.event : e;
                if(ev.keyCode==13) {
                    return false;
                }
            }
        });

        $("#search").on('click',function () {
            foodInfoTableReload();
        })
        
        foodInfoTableReload=function () {
            table.reload('foodInfoTable',{"where":{foodName:$("#foodName").val(),categoryId:$("#categoryId").val()}});
        }

        $("#addBtn").on('click',function () {
            pop_show('编辑','#(ctxPath)/food/infoForm',700,750);
        });
    });
</script>

#end