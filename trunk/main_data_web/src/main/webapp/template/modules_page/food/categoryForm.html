#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜类别表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="typeForm">
        <div class="layui-row">
            <div class="layui-col-xs3" style="padding:5px 5px 5px 20px;">
                <div id="categoryTree" class="ztree" style="height:80%;overflow:auto;"></div>
            </div>

            <div class="layui-col-xs9" style="padding:5px 5px 5px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>父级</label>
                        <div class="layui-input-block">
                            <input type="text" id="parentName" style="width: 70%;display: inline-block;" readonly  value="#(parentCategory.categoryName??'顶层类别')" autocomplete="off" placeholder="请选择父级类别" class="layui-input" lay-verify="required">
                            <input type="hidden" id="parentId" name="parentId" value="#(category.parentId??)" >
                            <button type="button" id="setTopBtn" class="layui-btn layui-btn-sm">设置为顶层类别</button>
                        </div>
                    </div>
                    <div class="layui-form-item">
						<label class="layui-form-label"><font color="red">*</font>类别类型</label>
						<div class="layui-input-block">
							<select id="categoryType" name="categoryType"lay-verify="required">
								#statusOption(com.cszn.integrated.service.entity.status.FoodCategoryType::me(), category.categoryType??"")
							</select>
						</div>
					</div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>类别名称</label>
                        <div class="layui-input-block">
                            <input type="text" name="categoryName" value="#(category.categoryName??)" autocomplete="off" placeholder="请输入类别名称" class="layui-input" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>是否有效</label>
                        <div class="layui-input-block">
                            <input type="radio" name="isEnabled" value="1" title="是" #(category != null ? ('1' == category.isEnabled ? 'checked':''):'checked')>
							<input type="radio" name="isEnabled" value="0" title="否" #(category != null ? ('0' == category.isEnabled ? 'checked':''):'')>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">描述</label>
                        <div class="layui-input-block">
                            <textarea name="remarks" placeholder="请输入描述" class="layui-textarea">#(category.remarks??)</textarea>
                        </div>
                    </div>

            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" type="hidden" value="#(category.id??)" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</script>
<script>
    layui.use(['form','tree'], function() {
        var $ = layui.$, form=layui.form,layer=layui.layer,tree=layui.tree;

        //树属性配置
        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/food/categoryTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#parentId").val(treeNode.id);
                    $("#parentName").val(treeNode.name);
                }
            }
        };

        // 设置顶层菜单按钮点击事件
        var zTreeObj = $.fn.zTree.init($("#categoryTree"), setting);

        // 初始化树结构
        layui.$('#setTopBtn').on('click', function() {
            $("#parentName").val('顶层类别');
            $("#parentId").val('00000000-0000-0000-0000-000000000000');
        });

        //监听表单提交
        form.on('submit(confirmBtn)', function(formObj) {
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/food/saveCategory',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.categoryTreeGridTableReload();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end