#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位状态展示#end

#define css()

#end

#define content()
<form class="layui-form" style="margin-left: 55px;margin-top: 20px;" id="bedStatusForm">
    <input type="hidden" id="bedStatusId" name="bedStatus.id" value="#(bedStatus.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">状态名称</label>
        <div class="layui-input-inline">
            <input type="text" name="bedStatus.statusName" class="layui-input" lay-verify="required" value="#(bedStatus.statusName??)" placeholder="请输入状态名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">状态编号</label>
        <div class="layui-input-inline">
            <select name="bedStatus.statusCode" lay-verify="required">
                <option value="">请选择状态编号</option>
                #for(status:bedStatusMap)
                <option value="#(status.key??)" #if(status.key==bedStatus.statusCode??)selected #end>#(status.value??)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-inline">
            <select id="isEnable" name="bedStatus.isEnable" lay-verify="required">
                <option value="0" #(bedStatus != null ?(bedStatus.isEnable == '0' ? 'selected':''):'')>可用</option>
                <option value="1" #(bedStatus != null ?(bedStatus.isEnable == '1' ? 'selected':''):'')>不可用</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-inline">
            <input type="text" name="bedStatus.order" class="layui-input" lay-verify="required|number" value="#(bedStatus.order??)" placeholder="请输入状态名称">
        </div>
    </div>
    <div class="layui-form-footer" >
        <div class="layui-pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/bedStatus/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#bedStatusForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("bedStatusTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
