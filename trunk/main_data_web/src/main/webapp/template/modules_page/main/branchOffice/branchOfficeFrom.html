#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地信息展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-table img {
        max-width: 250px;
        max-height:250px;
    }
</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="baseForm">
<!--            <input type="hidden" value="#(base.openDate??)" id="time"/>-->
            <div class="layui-row">
                <table class="layui-table" lay-skin="nob">
                    <colgroup>
                        <col width="30%">
                        <col width="30%">
                        <col width="20%">
                    </colgroup>
                    <tbody>
                    <tr>
                        <td>
                            <label class="layui-form-label"><span>*</span>分公司简称</label>
                            <div class="layui-input-block">
                                <input type="text" id="shortName" name="shortName" value="#(branchOffice.shortName??)" autocomplete="off" placeholder="请输入分公司简称" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><span>*</span>分公司全称</label>
                            <div class="layui-input-block">
                                <input type="text" id="fullName" name="fullName" value="#(branchOffice.fullName??)"  placeholder="请选择分公司全称" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label">*开业时间</label>
                            <div class="layui-input-block">
                                <input type="text" id="openDate" name="openDate" value="#date(branchOffice.openDate??,'yyyy-MM-dd')" autocomplete="off" placeholder="请输入开业时间" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><span>*</span>坐标</label>
                            <div class="layui-input-block" style="width:170px;">
                                <input type="text" id="coordinate" name="coordinate" value="#(branchOffice.coordinate??)" autocomplete="off" placeholder="请输入坐标" class="layui-input" lay-verify="required">
                                <i id="mapMarker" class="fa fa-map-marker fa-2x" style="float:left;margin-left:175px;margin-top: -35px;"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label">*分公司类型</label>
                            <div class="layui-input-block">
                                <select id="type" name="type" lay-verify="required">
                                    <option value="">请选择分公司类型</option>
                                    #dictOption("branch_office_type", branchOffice.type??'', "")
                                </select>
                            </div>
                        </td>
                        <td>

                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label">*座机</label>
                            <div class="layui-input-block">
                                <input type="text" name="tel" value="#(branchOffice.tel??)" autocomplete="off" placeholder="请输入座机" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><span>*</span>是否可用</label>
                            <div class="layui-input-block">
                                <select id="isEnable" name="isEnable" lay-verify="required">
                                    <option value="">请选择可用状态</option>
                                    <option value="0" #if(branchOffice.isEnable??=='0') selected #end >可用</option>
                                    <option value="1" #if(branchOffice.isEnable??=='1') selected #end>不可用</option>
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                    	<td>
                    		<label class="layui-form-label" style="padding: 8px 5px;"><span>*</span>负责人会员卡</label>
                            <div class="layui-input-block">
                                <input type="text" name="chargeCardNumber" value="#(branchOffice.chargeCardNumber??)" autocomplete="off" placeholder="请输入负责人会员卡" class="layui-input" lay-verify="required">
                            </div>
                    	</td>
                    	<td>
                    		<label class="layui-form-label"><span>*</span>负责人</label>
                            <div class="layui-input-block">
                            	<select id="chargePeopleId" name="chargePeopleId" lay-verify="required" lay-search>
									<option value="">请选择负责人(可搜索)</option>
									#for(user : userList)
									<option value="#(user.id)" #if(user.id==branchOffice.chargePeopleId??) selected #end>#(user.name)(#(user.userName))</option>
									#end
								</select>
                            </div>
                    	</td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <label class="layui-form-label"><span>*</span>所在区域</label>
                            <div class="layui-input-block">
                                <input type="hidden" id="province" name="provinceId" value="#(branchOffice.provinceId??)" />
                                <input type="hidden" id="city" name="cityId" value="#(branchOffice.cityId??)" />
                                <input type="hidden" id="town" name="townId" value="#(branchOffice.townId??)">
                                <input type="hidden" id="street" name="streetId" value="#(branchOffice.streetId??)">
                                <input type="hidden" id="regidentProvinceName" value="#(province??)" />
                                <input type="hidden" id="regidentCityName" value="#(city??)" />
                                <input type="hidden" id="regidentCountyName" value="#(town??)">
                                <input type="hidden" id="regidentStreetName" value="#(street??)">
                                <input type="text" id="regidentAddress" readonly="readonly" lay-verify="required"  name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <label class="layui-form-label"><span>*</span>详细地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="address" value="#(branchOffice.address??)" autocomplete="off" placeholder="请输入详细地址" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <label class="layui-form-label"><span>*</span>分公司介绍</label>
                            <div class="layui-input-block">
                                <textarea  name="introduction" rows="5" placeholder="请输入分公司介绍" class="layui-textarea" lay-verify="required">#(branchOffice.introduction??)</textarea>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <input type="text" name="remarks" value="#(branchOffice.remarks??)" autocomplete="off" placeholder="请输入备注" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" type="hidden" value="#(branchOffice.id??)" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;

        //时间渲染
        laydate.render({
            elem : '#openDate'
            ,trigger: 'click' //采用click弹出
        });

        //修改时间格式
        $(function(){
            if($("#time").val() != null && $("#time").val() != '') {
                var openDate = dateFormat($("#time").val(), 'yyyy-MM-dd');
                $("#openDate").val(openDate);
            }
        });

        //校验
        form.verify({
            checkPhone:function(value){
                if(value != null && value.length >0){
                    var reg = new RegExp("^0\\d{2,3}-?\\d{7,8}$");
                    if(!reg.test(value)){
                        return "座机号码格式不正确";
                    }
                }
            }
        });

        $("#mapMarker").click(function(){
            var url = "#(ctxPath)/main/base/map" ;
            pop_show("选择基地坐标",url,800,600);
        });

        getCoordinate = function(lng,lat){
            $("#coordinate").val( lat + ","+lng);
        };

        //保存
        form.on('submit(confirmBtn)', function(obj){
            var url = "#(ctxPath)/main/branchOffice/saveBranchOffice";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: obj.field,
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("baranchofficeTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


        //--------------------------居住区域begin---------------------------
        $('#regidentAddress').on('click', function() {
            //closeIdArea();

            $('#regidentArea').remove();
            var $this = $(this);
            var getTpl = regidentAreaTpl.innerHTML;
            $this.parent().append(getTpl);
            //event.stopPropagation();

            var street=$("#street").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var town=$("#town").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var city=$("#city").val();
            var regidentCityName=$("#regidentCityName").val();
            var province=$("#province").val();
            var regidentProvinceName=$("#regidentProvinceName").val();
            if(street!='' && regidentStreetName!=''){
                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentStreetLoad(town);
                regidentCountyLoad(city);
                regidentCityLoad(province);
                regidentProvinceLoad();
                $(".con .regidentStreetAll").show().siblings().hide();
                //clickStreet(streetId,streetName);
            }else if(town!='' && regidentCountyName!=''){

                if(town!=''){
                    regidentCityLoad(province);
                    regidentCountyLoad(city);
                    regidentProvinceLoad();
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/area/getAreas',
                        data: {pid:town},
                        notice:false,
                        loadFlag: false,
                        success : function(res){
                            if(res.state=='ok'){
                                if(res.data.length>0){
                                    $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    var html="<ul>";
                                    $.each(res.data, function(i, item){
                                        html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                    });
                                    html+="</ul>";
                                    $(".regidentStreetAll .list").append(html);
                                    //viewStreet(countyId,countyName);
                                    $(".con .regidentStreetAll").show().siblings().hide();
                                }else{
                                    //无 街道信息
                                    $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    $(".con .regidentTownAll").show().siblings().hide();
                                }
                            }
                        },
                        complete : function() {
                        }
                    });
                }
            }else if(city!='' && regidentCityName!=''){
                regidentProvinceLoad();
                regidentCityLoad(province);
                viewRegidentCounty(city,regidentCityName);
            }else if(province!='' && regidentProvinceName!=''){
                regidentProvinceLoad();
                viewRegidentCity(province,regidentProvinceName);
            }else{
                regidentProvinceLoad();
            }





            //去除事件冒泡
            var evt =  new Object;
            if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                evt = arguments.callee.caller.arguments[0];
            }else{
                evt = event || window.event;
            }
            evt.cancelBubble = true;
            $('#regidentArea').off('click');
            $('#regidentArea').on('click', function() {
                //event.stopPropagation();
                //去除事件冒泡
                var evt =  new Object;
                if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                    evt = arguments.callee.caller.arguments[0];
                }else{
                    evt = event || window.event;
                }
                evt.cancelBubble = true;
            })
        });

        regidentProvinceLoad=function(){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/area/getAreas',
                data: {pid:''},
                notice:false,
                loadFlag: false,
                success : function(res){
                    if(res.state=='ok'){
                        if(res.data.length>0){
                            $(".regidentProvinceAll .list").empty();
                            var html="<ul>";
                            $.each(res.data, function(i, item){
                                html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCity(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                            });
                            html+="</ul>";
                            $(".regidentProvinceAll .list").append(html);
                        }
                    }
                },
                complete : function() {
                }
            });
        };

        //点击省事件
        viewRegidentCity=function(province,regidentProvinceName) {
            $("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentCityAll").show().siblings().hide();
            $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            //点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
            $("#province").val(province);
            $("#regidentProvinceName").val(regidentProvinceName);
            $("#city").val("");
            $("#regidentCityName").val("");
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCityLoad(province);
        };

        //加载市
        regidentCityLoad=function(province){
            if(province!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:province},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentCityAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCounty(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentCityAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击市事件
        viewRegidentCounty=function(city,RegidentCityName){
            $("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentTownAll").show().siblings().hide();
            $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#city").val(city);
            $("#regidentCityName").val(RegidentCityName);
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCountyLoad(city);
        };

        //加载区/县
        regidentCountyLoad=function(city){
            if(city!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:city},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentTownAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentTownAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击区/县事件
        viewRegidentStreet=function(town,regidentCountyName){
            $("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentStreetAll").show().siblings().hide();
            $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#town").val(town);
            $("#regidentCountyName").val(regidentCountyName);
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentStreetLoad(town);
        };

        //加载街道/镇/乡
        regidentStreetLoad=function(town){
            if(town!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:town},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentStreetAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentStreetAll .list").append(html);
                            }else{
                                //无 街道信息
                                clickRegidentStreet('','');
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        clickRegidentStreet=function(street,regidentStreetName){
            $("#street").val(street);
            $("#regidentStreetName").val(regidentStreetName);
            var regidentProvinceName=$("#regidentProvinceName").val();
            var regidentCityName=$("#regidentCityName").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
            $("#regidentAddress").val(add);
            $('#regidentArea').remove();
        };

        regidentProvinceAllClick=function(){
            //$(".con .provinceAll").show();
            $("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentProvinceAll").show().siblings().hide();
        };

        regidentCityAllClick=function(){
            // $(".con .cityAll").show();
            if($("#province").val()!=''){
                $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCityLoad($("#province").val());
                $(".con .regidentCityAll").show().siblings().hide();
            }
        };

        regidentTownAllClick=function(){
            if($("#city").val()!=''){
                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCountyLoad($("#city").val());
                $(".con .regidentTownAll").show().siblings().hide();
            }
        };

        regidentStreetAllClick=function(){
            if($("#town").val()!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:$("#town").val()},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                regidentStreetLoad($("#town").val());
                                $(".con .regidentStreetAll").show().siblings().hide();
                            }else{
                                //无 街道信息 显示区/县信息
                                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                //countyLoad(cityId);
                                $(".con .regidentTownAll").show().siblings().hide();
                            }
                        }
                    },
                    complete : function() {
                    }
                });



            }
        };


        $('body').on('click', function() {
            closeRegidentArea();
        });

        //关闭区域选择器
        closeRegidentArea=function(){
            if(typeof($('#regidentArea').html())!='undefined'){
                var regidentProvinceName=$("#regidentProvinceName").val();
                var regidentCityName=$("#regidentCityName").val();
                var regidentCountyName=$("#regidentCountyName").val();
                var regidentStreetName=$("#regidentStreetName").val();
                var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
                $("#regidentAddress").val(add);
            }
            //alert(1);
            $('#regidentArea').remove();
            //console.log($('#regidentArea').html());
        }

        //-------------------------居住区域end----------------------------

    });
</script>
<script id="regidentAreaTpl" type="text/html">
    <div id="regidentArea" style="width:600px;height:300px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
        <div class="tabs clearfix">
            <ul>
                <li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
                <li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
                <li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
                <li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
            </ul>
        </div>
        <div class="con">
            <div class="regidentProvinceAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentCityAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentTownAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentStreetAll">
                <div class="list">

                </div>
            </div>
        </div>
    </div>
</script>
#end