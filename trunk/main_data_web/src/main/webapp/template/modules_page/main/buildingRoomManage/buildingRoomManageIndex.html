#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位查询#end

#define css()
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<div class="layui-row">

		<div class="layui-col-xs4" style="padding:5px 5px 5px 20px;">
			<fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
				<legend>1.楼栋列表</legend>
				<div class="layui-field-box">
					<div class="layui-row">
						#shiroHasPermission("main:buildingRoom:addBuildingBtn")
						<button class="layui-btn layui-btn-sm" type="button" id="addBuilding">添加楼栋</button>
						#end
					</div>
					<table style="margin-left:20px;" class="layui-hide" id="buildingTable" lay-filter="buildingTable"></table>
				</div>
			</fieldset>

			<fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
				<legend>2.楼层列表</legend>
				<div class="layui-field-box">
					<div class="layui-row">
						#shiroHasPermission("main:buildingRoom:addFloorBtn")
						<button class="layui-btn layui-btn-sm" type="button" id="addFloor">添加楼层</button>
						#end
					</div>
					<table style="margin-left:20px;" class="layui-hide" id="floorTable" lay-filter="floorTable"></table>
				</div>
			</fieldset>
		</div>

		<div class="layui-col-xs8" style="padding:5px 5px 5px 20px;">
			<fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
				<legend>3.房间列表</legend>
				<div class="layui-field-box">
					<div class="layui-row">
						#shiroHasPermission("main:buildingRoom:addRoomBtn")
						<button class="layui-btn layui-btn-sm" type="button" id="addRoom">添加房间</button>
						#end
						<button class="layui-btn layui-btn-sm" type="button" id="batchDelRoom">批量作废房间</button>
					</div>
					<table style="margin-left:20px;" class="layui-hide" id="roomTable" lay-filter="roomTable"></table>
				</div>
			</fieldset>

			<fieldset class="layui-elem-field layui-field-title" style="min-height: 300px;">
				<legend>4.床位列表</legend>
				<div class="layui-field-box">
					<div class="layui-row">
						#shiroHasPermission("main:buildingRoom:addBedBtn")
						<button class="layui-btn layui-btn-sm" type="button" id="addBed">添加床位</button>
						#end
						<button class="layui-btn layui-btn-sm" type="button" id="batchDelBed">批量作废床位</button>
					</div>
					<table style="margin-left:20px;" class="layui-hide" id="bedTable" lay-filter="bedTable"></table>
				</div>
			</fieldset>
			<input type="hidden" id="buildingId" >
			<input type="hidden" id="floorId" >
			<input type="hidden" id="roomId" >
		</div>
	</div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="buildingBar">
	<div class="layui-btn-group">
	#shiroHasPermission("main:buildingRoom:editBuilding")
	<a class="layui-btn layui-btn-xs" lay-event="editBuilding">编辑</a>
	#end
	#shiroHasPermission("main:buildingRoom:delBuilding")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delBuilding">作废</a>
	#end
	</div>
</script>
<script type="text/html" id="floorBar">
	<div class="layui-btn-group">
	#shiroHasPermission("main:buildingRoom:editFloor")
	<a class="layui-btn layui-btn-xs" lay-event="editFloor">编辑</a>
	#end
	#shiroHasPermission("main:buildingRoom:delFloor")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delFloor">作废</a>
	#end
	</div>
</script>
<script type="text/html" id="roomBar">
	<div class="layui-btn-group">
	#shiroHasPermission("main:buildingRoom:editRoom")
	<a class="layui-btn layui-btn-xs" lay-event="editRoom">编辑</a>
	#end
	#shiroHasPermission("main:buildingRoom:publicDoor")
	<a class="layui-btn layui-btn-xs" lay-event="configDoor">公共门</a>
	#end
	#shiroHasPermission("main:buildingRoom:delRoom")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delRoom">作废</a>
	#end
	</div>
</script>
<script type="text/html" id="bedBar">
	<div class="layui-btn-group">
	#shiroHasPermission("main:buildingRoom:editBed")
	<a class="layui-btn layui-btn-xs" lay-event="editBed">编辑</a>
	#end
	<a class="layui-btn layui-btn-xs" lay-event="equi">智能设备</a>
	#shiroHasPermission("main:buildingRoom:delBed")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delBed">作废</a>
	#end
	</div>
</script>
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

	table.render({
		id : 'buildingTable'
		,elem : '#buildingTable'
		,method : 'POST'
		,limit : 15
		,limits : [15,30,45,50]
		,url : '#(ctxPath)/main/buildingRoomManage/getBuildingPage'
		,where : {'baseId':$("#currBaseId",parent.document).val()}
		,height:400
		,cellMinWidth: 80
		,cols: [[
			{field:'', title: '序号', align: 'center', unresize: true,templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'buildingName', title: '名称', align: 'center', unresize: true}
			,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#buildingBar'}
		]]
		,page : true
		,done:function () {
			clickRow(this , table , function(data) {
				$("#buildingId").val(data.id);
				//清空表格数据
				emptyTable("floorTable");
				emptyTable("roomTable");
				emptyTable("bedTable");
				loadFloorTable(data.id);
			})
		}
	});

	table.on('tool(buildingTable)',function (obj) {
		if(obj.event==='editBuilding'){
			var baseId=$("#currBaseId", parent.document).val();
			pop_show("编辑楼栋",'#(ctxPath)/main/buildingRoomManage/buildingForm?baseId='+baseId+"&buildingId="+obj.data.id,1000,700);
		}else if(obj.event==='delBuilding'){
			layer.confirm("确定要作废该楼栋吗?",function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/buildingRoomManage/buildingDel',
					notice: true,
					data: {'id':obj.data.id},
					loadFlag: true,
					success : function(rep){
						if(rep.state==='ok'){
							buildingTableReload();
						}
						layer.close(index);
					},
					complete : function() {
					}
				});
			});
		}
	});

	buildingTableReload=function(){
		table.reload("buildingTable",{where:{'baseId':$("#currBaseId",parent.document).val()}});
	}

	floorTableReload=function () {
		table.reload("floorTable",{where:{'buildingId':$("#buildingId").val()}});
	}

	roomTableReload=function () {
		table.reload("roomTable",{where:{'floorId':$("#floorId").val()}});
	}

	bedTableReload=function () {
		table.reload("bedTable",{where:{'roomId':$("#roomId").val()}});
	}

	loadFloorTable=function(buildingId){
		table.render({
			id : 'floorTable'
			,elem : '#floorTable'
			,method : 'POST'
			,where : ''
			,limit : 10
			,limits : [10,20,30,40]
			,url : '#(ctxPath)/main/buildingRoomManage/getFloorPage'
			,where:{'buildingId':buildingId}
			,cellMinWidth: 80
			,cols: [[
				{field:'floorNo', title: '编号', align: 'center', unresize: true}
				,{field:'floorName', title: '名称', align: 'center', unresize: true}
				,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#floorBar'}
			]]
			,page : true
			,done:function () {
				clickRow(this , table , function(data) {
					$("#floorId").val(data.id);
					emptyTable("roomTable");
					emptyTable("bedTable");
					loadRoomTable(data.id);
				})
			}
		});

		table.on('tool(floorTable)',function (obj) {
			if(obj.event==='editFloor'){
				pop_show("编辑楼层",'#(ctxPath)/main/buildingRoomManage/floorForm?floorId='+obj.data.id,1000,600);
			}else if(obj.event==='delFloor'){
				layer.confirm("确定要作废该楼层吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/main/buildingRoomManage/floorDel',
						notice: true,
						data: {'id':obj.data.id},
						loadFlag: true,
						success : function(rep){
							if(rep.state==='ok'){
								floorTableReload();
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}
		});



	}

	loadRoomTable=function(floorId){
		table.render({
			id : 'roomTable'
			,elem : '#roomTable'
			,method : 'POST'
			,where : ''
			,limit : 10
			,limits : [10,20,30,40]
			,url : '#(ctxPath)/main/buildingRoomManage/getRoomPage'
			,where:{'floorId':floorId}
			,height:400
			,cellMinWidth: 80
			,cols: [[
				{type:'checkbox'}
				,{field:'roomNo', title: '房间编号', align: 'center', unresize: true}
				,{field:'roomName', title: '房间名称', align: 'center', unresize: true}
				,{field:'roomType', title: '房间类型', align: 'center', unresize: true}
				,{field:'totalBeds', title: '总床位数', align: 'center', unresize: true}
				,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{d.isEnable=='0'?'可用':'不可用'}}</div>"}
				,{field:'isBooking', title: '是否可预定', align: 'center', unresize: true,templet:"<div>{{d.isBooking=='0'?'可预定':'不可预定'}}</div>"}
				,{field:'allowLock', title: '是否发卡', align: 'center', unresize: true,templet:"<div>{{d.allowLock=='0'?'发卡':'不发卡'}}</div>"}
				,{fixed:'right', title: '操作', width: 170, align: 'center', unresize: true, toolbar: '#roomBar'}
			]]
			,page : true
			,done:function () {
				clickRow(this , table , function(data) {
					$("#roomId").val(data.id);
					emptyTable("bedTable");
					loadBedTable(data.id);
				})
			}
		});

		table.on('tool(roomTable)',function (obj) {
			if(obj.event==='editRoom'){
				var baseId=$("#currBaseId", parent.document).val();
				pop_show("编辑房间",'#(ctxPath)/main/buildingRoomManage/roomForm?roomId='+obj.data.id+"&baseId="+baseId,1000,650);
			}else if(obj.event==='configDoor'){
				var baseId=$("#currBaseId", parent.document).val();
				pop_show("公共门",'#(ctxPath)/main/baseDoor/roomDoorForm?roomId='+obj.data.id+"&baseId="+baseId,1000,650);
			}else if(obj.event==='delRoom'){
				layer.confirm("确定要作废该房间吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/main/buildingRoomManage/roomDel',
						notice: true,
						data: {'id':obj.data.id},
						loadFlag: true,
						success : function(rep){
							if(rep.state==='ok'){
								roomTableReload();
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}
		});


	}

	loadBedTable=function(roomId){
		table.render({
			id : 'bedTable'
			,elem : '#bedTable'
			,method : 'POST'
			,where : ''
			,limit : 10
			,limits : [10,20,30,40]
			,url : '#(ctxPath)/main/buildingRoomManage/getBedPage'
			,where:{'roomId':roomId}
			,cellMinWidth: 80
			,cols: [[
				{type:'checkbox'}
				,{field:'bedNo', title: '编号', align: 'center', unresize: true}
				,{field:'bedName', title: '名称', align: 'center', unresize: true}
				,{field:'bedType', title: '床位类型', align: 'center', unresize: true}
				,{field:'bedStatus', title: '床位状态', align: 'center', unresize: true}
				,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{d.isEnable=='0'?'可用':'不可用'}}</div>"}
				,{field:'isManyPeople', title: '是否多人', align: 'center', unresize: true,templet:"<div>{{d.isManyPeople=='0'?'可多人用':'不可多人用'}}</div>"}
				,{field:'peopleMax', title: '最多人数', align: 'center', unresize: true}
				,{fixed:'right', title: '操作', width: 160, align: 'center', unresize: true, toolbar: '#bedBar'}
			]]
			,page : true
		});

		table.on('tool(bedTable)',function (obj) {
			if(obj.event==='editBed'){
				var baseId=$("#currBaseId", parent.document).val();
				pop_show("编辑床位",'#(ctxPath)/main/buildingRoomManage/bedForm?bedId='+obj.data.id+"&baseId="+baseId,1000,600);
			}else if(obj.event==='delBed'){
				layer.confirm("确定要作废该床位吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/main/buildingRoomManage/bedDel',
						notice: true,
						data: {'id':obj.data.id},
						loadFlag: true,
						success : function(rep){
							if(rep.state==='ok'){
								bedTableReload();
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}else if(obj.event==='equi'){
				var buildingId=$("#buildingId").val();
				var roomId=$("#roomId").val();
				var floorId=$("#floorId").val();
				pop_show("["+obj.data.bedName+"]智能设备",'#(ctxPath)/main/buildingRoomManage/bedEquiIndex?buildingId='+
						buildingId+"&roomId="+roomId+"&floorId="+floorId+"&bedId="+obj.data.id,1000,600);
			}
		});


	}

	//清空表格
	emptyTable=function(id){
		$("#"+id).next().find("table tbody").empty();
	}

	//添加楼栋按钮
	$("#addBuilding").on('click',function () {
		var baseId=$("#currBaseId", parent.document).val();
		pop_show("添加楼栋",'#(ctxPath)/main/buildingRoomManage/buildingForm?baseId='+baseId,1000,700);
	});
	//添加楼层按钮
	$("#addFloor").on('click',function () {
		var buildingId=$("#buildingId").val();
		if(buildingId==='' || buildingId===null || typeof(buildingId)==='undefined'){
			layer.msg("请先选择楼栋",{icon:5,time:5000});
			return false;
		}
		pop_show("添加楼层",'#(ctxPath)/main/buildingRoomManage/floorForm?buildingId='+buildingId,1000,600);
	});
	//添加房间按钮
	$("#addRoom").on('click',function () {
		var buildingId=$("#buildingId").val();
		var floorId=$("#floorId").val();
		var baseId=$("#currBaseId", parent.document).val();
		if(baseId==='' || baseId===null || typeof(baseId)==='undefined'){
			layer.msg("请先选择基地",{icon:5,time:5000});
			return false;
		}
		if(buildingId==='' || buildingId===null || typeof(buildingId)==='undefined'){
			layer.msg("请先选择楼栋",{icon:5,time:5000});
			return false;
		}
		if(floorId==='' || floorId===null || typeof(floorId)==='undefined'){
			layer.msg("请先选择层",{icon:5,time:5000});
			return false;
		}
		pop_show("添加房间",'#(ctxPath)/main/buildingRoomManage/roomForm?buildingId='+buildingId+"&floorId="+floorId+"&baseId="+baseId,1000,650);
	});

	//点击批量作废房间按钮
	$("#batchDelRoom").on('click',function () {
		var data=table.checkStatus("roomTable").data;
		if(data.length===0){
			layer.msg("请至少选择一行数据",{icon:5,time:5000});
			return;
		}
		console.log(data);
		var ids=new Array();
		for(var i=0;i<data.length;i++){
			ids[i]=data[i].id;
		}
		layer.confirm('确定要批量作废选中的房间吗?',function (index) {
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/buildingRoomManage/batchDelRoom',
				notice: true,
				data: {'ids':JSON.stringify(ids)},
				loadFlag: true,
				success : function(rep){
					if(rep.state==='ok'){
						roomTableReload();
					}
					layer.close(index);
				},
				complete : function() {
				}
			});
		})

	});

	//添加床位按钮
	$("#addBed").on('click',function () {
		var roomId=$("#roomId").val();
		var baseId=$("#currBaseId", parent.document).val();
		if(baseId==='' || baseId===null || typeof(baseId)==='undefined'){
			layer.msg("请先选择基地",{icon:5,time:5000});
			return false;
		}
		if(roomId==='' || roomId===null || typeof(roomId)==='undefined'){
			layer.msg("请先选择房间",{icon:5,time:5000});
			return false;
		}

		pop_show("添加床位",'#(ctxPath)/main/buildingRoomManage/bedForm?roomId='+roomId+"&baseId="+baseId,1000,600);
	});

	//获取床位表格勾选的数据


	//点击批量作废床位按钮
	$("#batchDelBed").on('click',function () {
		var data=table.checkStatus("bedTable").data;
		if(data.length===0){
			layer.msg("请至少选择一行数据",{icon:5,time:5000});
			return;
		}
		var ids=new Array();
		for(var i=0;i<data.length;i++){
			ids[i]=data[i].id;
		}
		layer.confirm('确定要批量作废选中的床位吗?',function (index) {
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/buildingRoomManage/batchDelBed',
				notice: true,
				data: {'ids':JSON.stringify(ids)},
				loadFlag: true,
				success : function(rep){
					if(rep.state==='ok'){
						bedTableReload();
					}
					layer.close(index);
				},
				complete : function() {
				}
			});
		})
	});

});
</script>
#end