#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()楼栋编辑#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
<style>
	#form .layui-form-radio{
		margin:8px 2px;
	}
</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form" >
		<table width="100%">
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*楼栋名称</label>
						<div class="layui-input-inline">
							<input name="buildingName" value="#(building.buildingName??)" required lay-verify="required" placeholder="请输入楼栋名称" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 5px;">*楼栋显示名称</label>
						<div class="layui-input-inline">
							<input name="displayName" value="#(building.displayName??)" required lay-verify="required" placeholder="请输入显示名称" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td rowspan="4">
					<div class="layui-upload" style="text-align: center;height: 250px;width: 200px;background-color: #f2f2f2;">
						<div class="layui-upload-list">
							<img class="layui-upload-img" id="profilePhotoImg" style="max-height: 250px;max-width: 200px;"  src="#if(building.buildingImage??!=null)#(building.buildingImage??)#end" >
							<p id="profilePhotoText"></p>
							<input type="hidden" id="uploadPath" name="buildingImage" value="#(building.buildingImage??)">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*最低层数</label>
						<div class="layui-input-inline">
							<input name="lowestFloor" value="#(building.lowestFloor??)" required lay-verify="required|number" placeholder="请输入最低层数" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*最高层数</label>
						<div class="layui-input-inline">
							<input name="highestFloor" value="#(building.highestFloor??)" required lay-verify="required|number" placeholder="请输入最高层数" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
			</tr>
			<tr>
                <td>
                    <div class="layui-form-item">
                        <label class="layui-form-label">*是否可预订</label>
                        <div class="layui-input-inline">
                            <input type="radio" name="isBooking" lay-verify="required" value="0" #if(building==null) checked #elseif(building.isBooking??=='0') checked #end  title="可预订">
                            <input type="radio" name="isBooking" lay-verify="required" value="1" #if(building.isBooking??=='1') checked #end title="不可预订">
                        </div>
                    </div>
                </td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否可用</label>
						<div class="layui-input-inline">
							<input type="radio" name="isEnable" lay-verify="required" value="0" #if(building==null) checked #elseif(building.isEnable??=='0') checked #end  title="可用">
							<input type="radio" name="isEnable" lay-verify="required" value="1" #if(building.isEnable??=='1') checked #end title="不可用">
						</div>
					</div>
				</td>
			</tr>


			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否可线上预订</label>
						<div class="layui-input-block">
							<select id="isOutBookabled" name="isOutBookabled" lay-verify="required">
								<option value="">请选择是否可线上预订</option>
								<option value="1" #(building != null ?(building.isOutBookabled == '1' ? 'selected':''):'')>可预订</option>
								<option value="0" #(building != null ?(building.isOutBookabled == '0' ? 'selected':''):'')>不可预订</option>
							</select>
						</div>
					</div>
				</td>

				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*排序</label>
						<div class="layui-input-inline">
							<input name="sort" value="#(building.sort??)" required lay-verify="required|number" placeholder="请输入排序" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>基地锁服务地址</label>
						<div class="layui-input-block">
							<input type="text" id="lockService" name="lockService" value="#(building.lockService??)" autocomplete="off" placeholder="请输入基地锁服务地址" class="layui-input" lay-verify="">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item">
						<label class="layui-form-label">预定渠道</label>
						<div class="layui-input-block">
							<select xm-select="bookChannel" name="bookChannel" id="bookChannel">
								#for(bookChannel : bookChannelList)
								<option value="#(bookChannel.id??)" #if(building==null) selected #elseif(buildingChannelIds??!=null && buildingChannelIds.indexOf(bookChannel.id)!=-1) selected #end >#(bookChannel.name??)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="3">
					<div class="layui-form-item layui-form-text">
						<label class="layui-form-label">介绍</label>
						<div class="layui-input-block">
							<textarea name="introduction" placeholder="请输入介绍" class="layui-textarea">#(building.introduction??)</textarea>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="3">
					<div class="layui-form-item layui-form-text">
						<label class="layui-form-label">描述</label>
						<div class="layui-input-block">
							<textarea name="remark" placeholder="请输入描述" class="layui-textarea">#(building.remark??)</textarea>
						</div>
					</div>
				</td>
			</tr>
		</table>
		<div class="layui-form-footer" >
			<div class="pull-right">
				<input type="hidden" id="fileId" name="fileId" value="#(building.fileId??)"/>
				<input type="hidden" id="commonUpload" value="#(commonUpload)"/>
				<input type="hidden" id="id" name="id" value="#(building.id??)"/>
				<input type="hidden" id="baseId" name="baseId" value="#if(building==null)#(baseId??)#else#(building.baseId??)#end"/>
				<button type="button" class="layui-btn" id="profilePhoto">上传图片</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"/>
<script src="layui.all.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script>
	var formSelects=layui.formSelects;
layui.use(['form','layer','table','upload'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,upload=layui.upload;
	formSelects.render('bookChannel');

	form.on('submit(confirmBtn)',function (obj) {
        if(obj.field.baseId==='' || obj.field.baseId===null || typeof(obj.field.baseId)==='undefined'){
            layer.msg('基地id不能为空',{icon:5,time:5000});
            return false;
        }
        util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/buildingRoomManage/saveBuilding',
            data: obj.field,
            notice: true,
            loadFlag: false,
            success : function(rep){
                if(rep.state==='ok'){
                    pop_close();
                    parent.buildingTableReload();
                }
            },
            complete : function() {
            }
        });


        return false;
    });


	//普通图片上传
	var uploadInst = upload.render({
		elem: '#profilePhoto'
		,url:$("#commonUpload").val() + '/upload?bucket=building'
		,before: function(obj){
			$("#confirmBtn").attr("disabled",true);
			$("#confirmBtn").addClass("layui-btn-disabled");
			//预读本地文件示例，不支持ie8
			obj.preview(function(index, file, result){
				$('#profilePhotoImg').attr('src', result);
			});
		}
		,done: function(res){
			$("#confirmBtn").attr("disabled",false);
			$("#confirmBtn").removeClass("layui-btn-disabled");
			//如果上传失败
			if(res.state === 'ok'){
				$("#uploadPath").val(res.data.src);
				$("#fileId").val(res.data.id);
				layer.msg(res.msg,{icon:1,time:5000});
			}else {
				return layer.msg('上传失败');
			}
		}
		,error: function(){
			//演示失败状态，并实现重传
			var demoText = $('#demoText');
			demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
			demoText.find('.demo-reload').on('click', function(){
				uploadInst.upload();
			});
		}
	});

});
</script>
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end