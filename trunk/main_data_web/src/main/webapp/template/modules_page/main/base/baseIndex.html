#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地管理#end

#define css()
#end

#define content()

<div class="my-btn-box">
    <div class="layui-form-item">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
	        <div class="layui-inline">
		        <label class="layui-form-label">基地名称:</label>
		        <div class="layui-input-inline">
		            <input id="baseName" name="baseName" class="layui-input">
		        </div>
	        </div>
	        <div class="layui-inline">
	        	<div class="layui-btn-group">
			        <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
       			</div>
	        </div>
        </form>
    </div>
    <div class="layui-row">
    	<div class="layui-btn-group">
	        #shiroHasPermission("main:base:addBtn")
	       		<button type="button" id="add" class="layui-btn">添加</button>
	        #end
	        #shiroHasPermission("main:base:editBtn")
		    	<button type="button" id="edit" class="layui-btn" lay-event="edit">编辑</button>
		    #end
			#shiroHasPermission("main:base:restaurantSetBtn")
		    	<button type="button" id="restaurantSet" class="layui-btn" lay-event="restaurantSet">餐厅设置</button>
		    #end
			#shiroHasPermission("main:base:mealTimeBtn")
		    	<button type="button" id="mealTime" class="layui-btn" lay-event="mealTime">用餐时间</button>
		    #end
			#shiroHasPermission("main:base:foodTagBtn")
		    	<button type="button" id="foodTag" class="layui-btn" lay-event="foodTag">菜标签</button>
		    #end
			#shiroHasPermission("main:base:foodListBtn")
		    	<button type="button" id="foodList" class="layui-btn" lay-event="foodList">菜列表</button>
		    #end
			#shiroHasPermission("main:base:packageBtn")
		    	<button type="button" id="package" class="layui-btn" lay-event="package">套餐</button>
		    #end
			#shiroHasPermission("main:base:handCardBtn")
		    	<button type="button" id="handCard" class="layui-btn" lay-event="handCard">手牌</button>
		    #end
			#shiroHasPermission("main:base:customerTypeBtn")
		    	<button type="button" id="customerType" class="layui-btn" lay-event="customerType">客户类型</button>
		    #end
		    	<button type="button" id="teamType" class="layui-btn" lay-event="teamType">团队类型</button>
	        #shiroHasPermission("main:base:batchDelBtn")
	       		<button type="button" id="batchDel" class="layui-btn layui-btn-danger">批量作废</button>
	        #end
       	</div>
    </div>
    <div class="layui-row">
	    <table id="baseTable" lay-filter="baseTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
    #shiroHasPermission("main:base:editBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
	#shiroHasPermission("main:base:restaurantSetBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="restaurantSet">餐厅设置</a>
    #end
	#shiroHasPermission("main:base:mealTimeBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="mealTime">用餐时间</a>
    #end
	#shiroHasPermission("main:base:foodTagBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="foodTag">菜标签</a>
    #end
	#shiroHasPermission("main:base:foodListBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="foodList">菜列表</a>
    #end
	#shiroHasPermission("main:base:packageBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="package">套餐</a>
    #end
	#shiroHasPermission("main:base:handCardBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="handCard">手牌</a>
    #end
	#shiroHasPermission("main:base:customerTypeBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="customerType">客户类型</a>
    #end
    	<a class="layui-btn layui-btn-xs" lay-event="teamType">团队类型</a>
	#shiroHasPermission("main:base:delBtn")
    	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</div>
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        function baseLoad(data){
            table.render({
                id : 'baseTable'
                ,elem : '#baseTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,height:$(document).height()*0.8
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/main/base/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:80, title: '序号',unresize:true}
                    ,{field:'', title: '基地类型', width: 100, align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.BaseType::me(), "baseType")'}
                    ,{field:'', title: '基地状态', width: 100, align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.BaseStat::me(), "baseStatus")'}
                    ,{field:'baseName', title: '基地名称', width:200, align: 'center', unresize: true,templet:"<div><a href='https://map.baidu.com/@12197356.892065747,2111340.270582287,17z/latlng={{d.coordinate}}&title={{d.baseName}}&content={{d.baseName}}&autoOpen=true'target='_blank'>{{d.baseName}}</a></div>"}
                    ,{field:'openDate', title: '开张日期', width:120, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.openDate,'yyyy-MM-dd') }}</div>"}
                    ,{field:'tel', title: '座机', width:120, align: 'center', unresize: true}
                    ,{field:'isBooking', title: '是否可预订', width: 100, align: 'center', unresize: true,templet:"<div>{{ d.isBooking=='0'?'<span class='layui-badge layui-bg-green'>可预订</span>':d.isBooking=='1'?'<span class='layui-badge'>不可预订</span>':'- -' }}</div>"}
                    ,{field:'coordinate', title: '坐标', width:200, align: 'center', unresize: true}
                    ,{field:'isEnable', title: '是否可用', width: 100, align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'address', title: '详细地址', width: 300, align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', width: 180, sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
//                     ,{fixed:'right', title: '操作', width: 560, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
//             table.on('tool(baseTable)',function(obj){
//                 if(obj.event === 'edit'){
//                     var url = "#(ctxPath)/main/base/form?id=" + obj.data.id ;
//                     pop_show("编辑基地信息",url,950,700);
//                 }else if(obj.event === 'restaurantSet'){
//                     var url = "#(ctxPath)/main/base/restaurantIndex?id=" + obj.data.id ;
//                     layerShow("基地餐厅设置",url,'100%','100%');
//                 }else if(obj.event === 'mealTime'){
//                     var url = "#(ctxPath)/main/base/mealTimeIndex?id=" + obj.data.id ;
//                     pop_show("编辑基地用餐时间",url);
//                 }else if(obj.event === 'foodTag'){
//                     var url = "#(ctxPath)/main/base/foodTagIndex?id=" + obj.data.id ;
//                     pop_show("菜标签编辑",url);
//                 }else if(obj.event === 'foodList'){
//                     var url = "#(ctxPath)/main/base/foodListIndex?id=" + obj.data.id ;
//                     pop_show("菜列表编辑",url);
//                 }else if(obj.event === 'package'){
//                     var url = "#(ctxPath)/main/base/packageIndex?id=" + obj.data.id ;
//                     pop_show("套餐编辑",url);
//                 }else if(obj.event === 'handCard'){
//                     var url = "#(ctxPath)/main/base/handCardIndex?id=" + obj.data.id ;
//                     pop_show("手牌编辑",url);
//                 }else if(obj.event === 'customerType'){
//                     var url = "#(ctxPath)/main/base/customerTypeIndex?id=" + obj.data.id ;
//                     pop_show("客户类型编辑",url);
//                 }else if(obj.event === 'teamType'){
//                     var url = "#(ctxPath)/main/base/teamTypeIndex?id=" + obj.data.id ;
//                     pop_show("团队类型编辑",url);
//                 }else if (obj.event === 'del') {
//                     layer.confirm("确定要作废吗?",function(index){
//                         util.sendAjax ({
//                             type: 'POST',
//                             url: '#(ctxPath)/main/base/delete',
//                             notice: true,
//                             data: {id:obj.data.id},
//                             loadFlag: true,
//                             success : function(rep){
//                                 if(rep.state=='ok'){
//                                     tableReload('baseTable',null);
//                                 }
//                                 layer.close(index);
//                             },
//                             complete : function() {
//                             }
//                         });
//                     });
//                 }
//             });
        };
        
        baseLoad(null);

        form.on("submit(search)",function(data){
            baseLoad(data.field);
            return false;
        });

        //批量获取数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('baseTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }
        
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/base/form" ;
            pop_show("新增基地信息",url,950,700);
        });
        
        // 编辑
        $("#edit").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
        		var url = "#(ctxPath)/main/base/form?id=" + tableData[0].id;
                pop_show("编辑基地信息",url,950,700);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 餐厅设置
        $("#restaurantSet").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/restaurantIndex?id=" + tableData[0].id;
                layerShow("基地餐厅设置",url,'100%','100%');
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 用餐时间
        $("#mealTime").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/mealTimeIndex?id=" + tableData[0].id;
                pop_show("编辑基地用餐时间",url);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 菜标签
        $("#foodTag").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/foodTagIndex?id=" + tableData[0].id;
                pop_show("菜标签编辑",url);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 菜列表
        $("#foodList").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/foodListIndex?id=" + tableData[0].id;
                layerShow("菜列表",url,'100%','100%');
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 套餐
        $("#package").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/packageIndex?id=" + tableData[0].id;
                pop_show("套餐编辑",url);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 手牌
        $("#handCard").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/handCardIndex?id=" + tableData[0].id;
                pop_show("手牌编辑",url);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 客户类型
        $("#customerType").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/customerTypeIndex?id=" + tableData[0].id;
                pop_show("客户类型编辑",url);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        // 团队类型
        $("#teamType").click(function(){
        	var tableData = getCheckTableData();
        	if(tableData.length==1){
                var url = "#(ctxPath)/main/base/teamTypeIndex?id=" + tableData[0].id;
                pop_show("团队类型编辑",url);
        	}else if(tableData.length==0){
        		layer.msg('请勾选数据', function () {});
        	}else{
        		layer.msg('只能勾选一条数据', function () {});
        	}
        });
        
        //批量作废
        $("#batchDel").click(function(){
            var tableData = getCheckTableData();
            if(tableData.length>0){
	            layer.confirm("确定批量作废吗?",function(index){
	                var url = "#(ctxPath)/main/base/batchDel";
	                util.sendAjax ({
	                    type: 'POST',
	                    url: url,
	                    data: {baseData:JSON.stringify(tableData)},
	                    notice: true,
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                            tableReload("baseTable",null);
	                        }
	                    },
	                    complete : function() {
	                    }
	                });
	                layer.close(index);
	            });
            }else{
                layer.msg('请勾选数据', function () {});
            }
        });
    });
</script>
#end