#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()扣费方案编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, $ = layui.jquery
	;

	//校验
	form.verify({
		checkNumber:function(value){
			if(value != null){
				var reg = new RegExp("^[0-9]*$");
				if(!reg.test(value)){
					return "只能输入数字";
				}
			}
		}
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/cardDeductScheme/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>方案类型</label>
		<div class="layui-input-block">
			<select name="schemeType" lay-verify="required">
				<option value="">请选择方案类型</option>
				#dictOption("scheme_type", model.schemeType??'', "")
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>方案编号</label>
		<div class="layui-input-block">
			<input type="text" name="schemeNo" class="layui-input" lay-verify="required" value="#(model.schemeNo??)" maxlength="50" placeholder="请输入方案编号" #if(model.schemeNo!=null)readonly="readonly"#end>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>方案名称</label>
		<div class="layui-input-block">
			<input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" maxlength="50" placeholder="请输入方案名称">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>扣费方式</label>
		<div class="layui-input-block">
			<select name="deductWay" lay-verify="required" lay-filter="">
				<option value="">请选择扣费方式</option>
				#dictOption("deduct_way", model.deductWay??'', "")
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>扣费值</label>
		<div class="layui-input-block">
			<input type="text" name="onceConsume" class="layui-input" lay-verify="required" value="#(model.onceConsume??)" maxlength="50" placeholder="请输入扣费值">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">单价</label>
		<div class="layui-input-block">
			<input type="text" name="price" class="layui-input" value="#(model.price??)" maxlength="50" placeholder="请输入单价">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">排序</label>
		<div class="layui-input-block">
			<input type="text" name="sort" class="layui-input" value="#(model.sort??)" placeholder="请输入排序" lay-verify="checkNumber|required">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea" rows="4" placeholder="请输入备注">#(model.remark??)</textarea>
		</div>
	</div>
	<div style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end