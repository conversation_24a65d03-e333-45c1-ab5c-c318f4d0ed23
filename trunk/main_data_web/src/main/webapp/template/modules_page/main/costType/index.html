#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()合同类别管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;width: 120px;padding: 0 20px;">类型</label>
            <input class="layui-input" name="name" id="name" placeholder="" autocomplete="off">
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:costType:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end

        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="contractTypeTable" lay-filter="contractTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'contractTypeTable',
            elem: '#contractTypeTable'
            ,url : '#(ctxPath)/main/costType/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'name', title: '类型名称',unresize:true}
                ,{field: 'isEnabled', title: '是否可用',unresize:true,templet:"<div>{{d.isEnabled == '0'? '<span class='layui-badge'>不可用</span>':d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':'- -'}}</div>"}
                ,{field: 'sort', title: '排序',unresize:true}
                ,{field: 'remark', title: '备注',unresize:true}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(contractTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/costType/form?id=" + obj.data.id ;
                layerShow("编辑费用类型",url,550,450);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/main/costType/delContractType?id="+obj.data.id,
                        type:'post',
                        data:{"id":obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                contractTypeTable();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });



        contractTypeTable=function(){
            var typeName=$("#name").val();
            table.reload('contractTypeTable',{'where':{'name':typeName}});
        }

        $("#search").on('click',function () {
            contractTypeTable();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/costType/form";
            layerShow("添加费用类型",url,550,450);
        });



    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:costType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
#end