#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()合同类别管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;width: 120px;padding: 0 20px;">类型</label>
            <input class="layui-input" name="typeName" id="typeName" placeholder="" autocomplete="off">
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:contractType:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
            #shiroHasPermission("main:contractType:batchDelBtn")
            <button class="layui-btn" type="button" id="batchDel">批量作废</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="contractTypeTable" lay-filter="contractTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'contractTypeTable',
            elem: '#contractTypeTable'
            ,url : '#(ctxPath)/main/contratType/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type:'checkbox'},
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'typeNo', title: '合同编号',unresize:true}
                ,{field: 'typeName', title: '类型名称',unresize:true}
                ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge'>不可用</span>':d.isEnable == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':'- -'}}</div>"}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(contractTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/contratType/contractTypeForm?id=" + obj.data.id ;
                layerShow("编辑合同类型",url,550,450);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/main/contratType/delContractType?id="+obj.data.id,
                        type:'post',
                        data:{"id":obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                contractTypeTable();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });



        contractTypeTable=function(){
            var typeName=$("#typeName").val();
            table.reload('contractTypeTable',{'where':{'typeName':typeName}});
        }

        $("#search").on('click',function () {
            contractTypeTable();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/contratType/contractTypeForm";
            layerShow("添加合同类型",url,550,450);
        });



        $("#batchDel").on('click',function () {
            var data=table.checkStatus('contractTypeTable').data;
            if(data==null || data.length==0){
                layer.msg('请勾选要作废的数据!', {icon: 5, offset: 'auto'});
                return false;
            }

            layer.confirm("确定要作废吗？",function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/contratType/batchContractType',
                    data:{"data":JSON.stringify(data)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            layer.close(index);
                            contractTypeTable();
                        }
                    },
                    complete : function() {
                    }
                });
            });


        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:contractType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:contractType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end