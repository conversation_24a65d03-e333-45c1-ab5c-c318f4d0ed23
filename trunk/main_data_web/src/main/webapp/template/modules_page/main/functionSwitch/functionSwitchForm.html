#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置编辑#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/functionSwitch/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#functionSwitchForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("functionSwitchTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form" style="margin-left: 0px;margin-top: 20px;" id="functionSwitchForm">
    <input type="hidden" id="fsId" name="fs.id" value="#(fs.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">功能配置</label>
        <div class="layui-input-inline">
            <select name="fs.function" lay-search lay-verify="required">
                <option value="">请选择功能配置</option>
                #getDictList("function_switch_configure")
					<option value="#(key)" #(fs != null ?(key == fs.function?? ? 'selected':''):'')>#(value)</option>
				#end
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">开关</label>
        <div class="layui-input-inline">
            <select name="fs.switchFlag" lay-verify="required" lay-filter="switchFlag">
                <option value="">请选择开关状态</option>
                <option value="0" #(fs != null ?(fs.switchFlag == '0' ? 'selected':''):'')>已关闭</option>
                <option value="1" #(fs != null ?(fs.switchFlag == '1' ? 'selected':''):'')>已开启</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left: 150px; margin-top: 70px;">
        <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
    </div>
</form>
#end
