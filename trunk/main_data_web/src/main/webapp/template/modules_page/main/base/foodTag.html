#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜标签页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
    <div class="layui-form-item">
    	<table class="layui-table" lay-size="sm">
			<thead>
				<tr>
					<th width="10%">序号</th>
					<th width="50%"><font color="red">*</font>标签名称</th>
					<th width="30%"><font color="red">*</font>是否有效</th>
					<th width="10%">操作</th>
				</tr>
			</thead>
			<tbody id="foodTagList">
				#for(foodTag : foodTagList)
				<tr id="foodTag-#(for.index+1)">
					<td>#(for.index+1)</td>
					<td>
						<input type="text" name="foodTagList[#(for.index+1)].tagName" class="layui-input" lay-verify="required" value="#(foodTag.tagName??'')" placeholder="请输入标签名称" autocomplete="off">
					</td>
					<td>
						<input type="radio" name="foodTagList[#(for.index+1)].isEnabled" value="0" title="否" #if(foodTag!=null && foodTag.isEnabled=='0') checked #elseif(foodTag==null) checked #end>
            			<input type="radio" name="foodTagList[#(for.index+1)].isEnabled" value="1" title="是" #if(foodTag.isEnabled??=='1') checked #end>
					</td>
					<td>
						<input type="hidden" name="foodTagList[#(for.index+1)].id" value="#(foodTag.id??)">
						<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('foodTag-#(for.index+1)','#(foodTag.id??)')">作废</a>
					</td>
				</tr>
				#end
			</tbody>
		</table>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="baseId" value="#(baseId??)">
			<input type="hidden" id="foodTagCount" name="foodTagCount" value="#(foodTagList.size()??)">
			<div id="addBtn" class="layui-btn">添加</div>
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script id="tagTrTpl" type="text/html">
	<tr id="foodTag-{{d.idx}}">
		<td>{{d.idx}}</td>
		<td><input type="text" name="foodTagList[{{d.idx}}].tagName" class="layui-input" value="" lay-verify="required" placeholder="请输入标签名称" autocomplete="off"></td>
		<td>
			<input type="radio" name="foodTagList[{{d.idx}}].isEnabled" value="0" title="否">
            <input type="radio" name="foodTagList[{{d.idx}}].isEnabled" value="1" title="是" checked>
		</td>
		<td>
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('foodTag-{{d.idx}}','')">作废</a>
		</td>
	</tr>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate','laytpl' ], function() {
	var form = layui.form
	, laytpl = layui.laytpl
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		$('#foodTagCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1)}, function(html){
			targetId.append(html);
		});
		form.render('radio');
    };
    
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		addTpl($('#foodTagList'), tagTrTpl.innerHTML, $('#foodTagCount').val());
	});
	
	//删除方法
	del = function(trId, dataId) {
		if(dataId!=null && dataId!=''){
			layer.confirm('您确定要作废？', {icon: 3, title:'询问'}, function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/base/delFoodTag',
					data: {id:dataId, delFlag:'1'},
					notice: true,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							$("#"+trId).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else{
			$("#"+trId).remove();
		}
	};
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var trLength = $('#foodTagList tr').length;
		if(trLength>0){
			//提交表单数据
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/main/base/saveFoodTag',
	            data: $(formObj.form).serialize(),
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		pop_close();
	            	}
	            },
	            complete : function() {
			    }
	        });
		}else{
			layer.msg('请添加数据!',{icon:5});
		}
		return false;
	});
});
</script>
#end