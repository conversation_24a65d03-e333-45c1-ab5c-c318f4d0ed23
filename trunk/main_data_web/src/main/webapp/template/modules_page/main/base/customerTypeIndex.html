#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地客户类型首页#end

#define css()
#end

#define content()
<div class="layui-row">
	<div id="layuiTab" class="layui-tab layui-tab-brief" lay-filter="layuiTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<form id="frm" class="layui-form" lay-filter="layform" action="" method="post">
					<div class="layui-row">
						<div class="layui-inline">
							<label class="layui-form-label">类型名称</label>
							<div class="layui-input-inline">
								<input id="typeName" name="typeName" class="layui-input">
							</div>
						</div>
						<div class="layui-inline">
							<input type="hidden" id="baseId" name="baseId" value="#(baseId??)">
							<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
							<a type="button" id="addBtn" class="layui-btn btn-default">添加</a>
						</div>
					</div>
					<div class="layui-row">
						<table id="customerTypeTable" lay-filter="customerTypeTable"></table>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:delBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
</div>
</script>
<script type="text/javascript">
layui.use([ 'table', 'form', 'element' ], function() {
	var table = layui.table
	, form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
	function customerTypeLoad(data){
		table.render({
			id : 'customerTypeTable'
			,elem : '#customerTypeTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/main/base/customerTypePage'
			,cellMinWidth: 80
			,height:$(document).height()*0.8
			,cols: [[
				{type: 'numbers', title: '序号', width: 60, unresize:true}
				,{field:'typeName', title: '类型名称', align: 'center', unresize: true}
				,{field:'typePrice', title: '价格', align: 'center', unresize: true}
				,{field:'depositPrice', title: '押金', align: 'center', unresize: true}
				,{field:'entryTimes', title: '进入次数', align: 'center', unresize: true}
				,{field: '', title: '是否有效', width: 120,unresize:true,templet:"<div>{{d.isEnabled == '0'? '<span class='layui-badge'>无效</span>':d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>有效</span>':'- -'}}</div>"}
				,{field: '', title: '是否入住', width: 120,unresize:true,templet:"<div>{{d.isCheckIn == '0'? '<span class='layui-badge'>否</span>':d.isCheckIn == '1'? '<span class='layui-badge layui-bg-green'>是</span>':'- -'}}</div>"}
// 				,{field: '', title: '是否团队', width: 120,unresize:true,templet:"<div>{{d.isTeam == '0'? '<span class='layui-badge'>否</span>':d.isTeam == '1'? '<span class='layui-badge layui-bg-green'>是</span>':'- -'}}</div>"}
				,{field:'remarks', title: '备注', align: 'center', unresize: true}
				,{fixed:'right', title: '操作', width:120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 15
			,limits : [15,25,35,45]
			, loading: true
		    , done: function (res, curr, count) {
		    }
		});
		table.on('tool(customerTypeTable)',function(obj){
			if(obj.event==="edit"){//编辑按钮事件
	        	element.tabAdd('layuiTab', {
	    			title: '编辑'
	    			, content: '<div id="modelForm"></div>'
	    			, id: 'form'
	    		});
	    		$('#modelForm').load('#(ctxPath)/main/base/customerTypeForm?id='+obj.data.id);
	    		element.tabChange('layuiTab', 'form');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm("确定要作废吗?",function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/main/base/delCustomerType',
	                    notice: true,
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                        	tableReload("customerTypeTable",{baseId:$('#baseId').val()});
	                        }
	                        layer.close(index);
	                    },
	                    complete : function() {
	                    }
	                });
	            });
	        }
		});
	};
	
	customerTypeLoad({baseId:'#(baseId??)'});
	
	form.on("submit(search)",function(data){
		customerTypeLoad(data.field);
		return false;
	});
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('layuiTab', {
			title: '添加'
			, content: '<div id="modelForm"></div>'
			, id: 'form'
		});
		$('#modelForm').load('#(ctxPath)/main/base/customerTypeForm?baseId=#(baseId??)');
		element.tabChange('layuiTab', 'form');
    });
	
	element.on('tab(layuiTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end