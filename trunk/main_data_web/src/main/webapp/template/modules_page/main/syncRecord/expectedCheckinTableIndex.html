#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#end

#define css()
#end

#define content()
<div class="layui-row">
    <div class="layui-row" style="padding-left: 40px;padding-top: 10px;" >
        <label class="layui-form-label">日期：</label>
        <div class="layui-inline">
            <input class="layui-input" id="date" readonly >
        </div>
    </div>
</div>
<table id="dataTable" lay-filter="dataTable"></table>
#end

#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
           elem:"#date"
           ,type:'date'
           ,trigger:'click'
            ,value:new Date()
            ,done:function () {
                table.reload('dataTable',{'where':{'date':$("#date").val()}});
            }
        });

        table.render({
            id : 'dataTable'
            ,elem : '#dataTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,url : '#(ctxPath)/main/syncRecord/baseExpectedCheckinList?date='+$("#date").val()
            ,where:{'appNo':$("#app").val(),'status':$("#status").val()}
            ,cellMinWidth: 80
            ,cols: [[
                {field:'baseName', title: '基地', align: 'center', unresize: true}
                ,{field:'expectCheckinNum', title: '预计入住数', align: 'center', unresize: true}
                ,{field:'checkinNum', title: '实际入住数', align: 'center', unresize: true}
                /*,{field:'longCheckinNum', title: '长住入住数', align: 'center', unresize: true}
                ,{field:'expectCheckoutNum', title: '预计退住数', align: 'center', unresize: true}
                ,{field:'checkoutNum', title: '实际退住数', align: 'center', unresize: true}
                ,{field:'longCheckoutNum', title: '长住退住数', align: 'center', unresize: true}*/
            ]]
            ,done:function (res,curr,count) {
                //双击事件
                $('#dataTable').next().find('.layui-table-body').find("table" ).find("tbody").children("tr").on('dblclick',function(){
                    var id = JSON.stringify($('#dataTable').next().find('.layui-table-body').find("table").find("tbody").find(".layui-table-hover").data('index'));
                    var baseId=res.data[id].id;
                    var baseName=res.data[id].baseName;
                    if(baseId!='' && baseId!=undefined){
                        layerShow(baseName+'预定入住详情','#(ctxPath)/main/syncRecord/baseExpectedCheckinDetailIndex?baseId='+baseId+"&date="+$("#date").val(),1300,600);
                    }
                })
            }
        });
    });
</script>
#end