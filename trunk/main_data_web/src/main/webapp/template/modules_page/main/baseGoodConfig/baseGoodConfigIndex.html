#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地商品配置#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label class="layui-form-label" style="line-height: 40px;width: 140px;padding: 0 20px;">基地</label>
            <div class="layui-input-inline">
                <select id="baseId">
                    <option value="">全部</option>
                    #for(base : baseList)
                    <option value="#(base.id)">#(base.baseName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:baseGoodConfig:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="carTypeTable" lay-filter="carTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'carTypeTable',
            elem: '#carTypeTable'
            ,url : '#(ctxPath)/main/baseGoodConfig/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'modelName', title: '商品名称',unresize:true}
                ,{field: 'baseName', title: '基地名称',unresize:true}
                ,{field: 'baseGoodsConfigOrder', title: '排序',unresize:true}
                ,{field:'isBuckleCard', title: '是否可扣卡', align: 'center', unresize: true,templet:"<div>{{ d.isBuckleCard=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isBuckleCard=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                ,{field:'cardTimes', title: '扣卡天数', align: 'center', unresize: true}
                ,{field:'isBuckleCard', title: '是否核销', align: 'center', unresize: true,templet:"<div>{{ d.isWriteOff=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isWriteOff=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled == '0'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                ,{field: 'description', title: '描述',unresize:true}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(carTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/baseGoodConfig/form?id=" + obj.data.id ;
                layerShow("编辑类型",url,550,600);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/main/carType/delCarType",
                        type:'post',
                        data:{"id":obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                carTypeTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });



        carTypeTableReload=function(){
            table.reload('carTypeTable',{'where':{'baseId':$("#baseId").val()}});
        }

        $("#search").on('click',function () {
            carTypeTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/baseGoodConfig/form";
            layerShow("添加类型",url,550,600);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:baseGoodConfig:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end

</script>
#end