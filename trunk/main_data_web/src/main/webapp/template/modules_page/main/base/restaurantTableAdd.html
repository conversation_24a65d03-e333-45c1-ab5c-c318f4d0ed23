#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
    <div class="layui-form-item">
    	<table class="layui-table" lay-size="sm">
			<thead>
				<tr>
					<th width="10%">桌号</th>
					<th width="25%"><font color="red">*</font>桌名</th>
					<th width="15%"><font color="red">*</font>最多人数</th>
					<th width="15%"><font color="red">*</font>是否可预订</th>
					<th width="15%"><font color="red">*</font>是否有效</th>
					<th width="10%"><font color="red">*</font>状态</th>
					<th width="10%">操作</th>
				</tr>
			</thead>
			<tbody id="tableList"></tbody>
		</table>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="restaurantId" value="#(restaurantId??)">
			<input type="hidden" id="tableCount" name="tableCount" value="0">
			<div id="addRowBtn" class="layui-btn">添加</div>
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script id="tableTrTpl" type="text/html">
	<tr id="restaurantTable-{{d.idx}}">
		<td><input type="text" name="tableList[{{d.idx}}].tableNo" class="layui-input" value="" lay-verify="required|number" placeholder="请输入桌号" maxlength="4" autocomplete="off"></td>
		<td><input type="text" name="tableList[{{d.idx}}].tableName" class="layui-input" value="" lay-verify="required" placeholder="请输入桌名" maxlength="4" autocomplete="off"></td>
		<td><input type="text" name="tableList[{{d.idx}}].peopleNum" class="layui-input" value="" lay-verify="required|number" placeholder="请输入最多人数" maxlength="4" autocomplete="off"></td>
		<td>
			<input type="radio" name="tableList[{{d.idx}}].isBooking" value="0" title="否">
            <input type="radio" name="tableList[{{d.idx}}].isBooking" value="1" title="是" checked>
		</td>
		<td>
			<input type="radio" name="tableList[{{d.idx}}].isEnabled" value="0" title="否">
            <input type="radio" name="tableList[{{d.idx}}].isEnabled" value="1" title="是" checked>
		</td>
		<td>
			<select id="tableStatus-{{d.idx}}" name="tableList[{{d.idx}}].tableStatus" lay-verify="required" lay-search>
			</select>
		</td>
		<td>
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('restaurantTable-{{d.idx}}')">作废</a>
		</td>
	</tr>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate','laytpl', 'element' ], function() {
	var form = layui.form
	, laytpl = layui.laytpl
	, element = layui.element
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('restaurantTab', 'form');
    }
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		$('#tableCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1)}, function(html){
			targetId.append(html);
		});
		#for(dict : dictList)
			$('#tableStatus-'+(parseInt(idx)+1)).append('<option value="#(dict.dictValue)")>#(dict.dictName)</option>');
		#end
		form.render();
    };
    
	//添加按钮点击事件
	$('#addRowBtn').on('click', function() {
		addTpl($('#tableList'), tableTrTpl.innerHTML, $('#tableCount').val());
	});
	
	//删除方法
	del = function(trId) {
		$("#"+trId).remove();
	};
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var trLength = $('#tableList tr').length;
		if(trLength>0){
			//提交表单数据
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/main/base/batchSaveBaseRestaurantTable',
	            data: $(formObj.form).serialize(),
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		tableReload("restaurantTableTable",{restaurantId:$('#restaurantId').val()});
						closeTab();
	            	}
	            },
	            complete : function() {
			    }
	        });
		}else{
			layer.msg('请添加数据!',{icon:5});
		}
		return false;
	});
});
</script>
#end