#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()帐号管理首页#end

#define css()
#end

#define js()
<script type="text/html" id="userTableBar">
    <a class="layui-btn layui-btn-sm" lay-event="choice">选择</a>
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','vip_table'],function(){

        // 操作对象
        var layer = layui.layer
            ,table = layui.table
            ,vipTable = layui.vip_table
            ,$ = layui.jquery
            ,tableId = 'userTable'
        ;

        // 表格渲染
        var tableObj = table.render({
            id: tableId
            , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
            , even: true //开启隔行背景
            , url: '#(ctxPath)/user/pageTable'
            , method: 'post'
            , height: vipTable.getFullHeight()    //容器高度
            , where: {userName:$('#userName').val()}
            , cols: [[                  //标题栏
                {field:'userType', title:'帐号类型', width:100, align:'center', templet:'#dictTpl("user_type", "userType")'}
                ,{field:'userName', title:'登陆帐号', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
                ,{field:'loginFlag', title:'登陆状态', align:'center', templet:'#dictTpl("login_flag", "loginFlag")'}
                ,{field:'name', title:'姓名', align:'center'}
                ,{field:'sex', title:'性别', align:'center',width:80, templet:'#dictTpl("gender", "sex")'}
                ,{field:'phoneNumber', title:'手机号码', align:'center'}
                ,{fixed:'right', title:'操作', width:100, align:'center', toolbar:'#userTableBar'}
            ]]
            , page: true
            , loading: true
            , done: function (res, curr, count) {
            }
        });
        // 表格绑定事件
        table.on('tool('+tableId+')',function (obj) {
            if (obj.event === 'choice') {
                parent.$("#auditUserId").val(obj.data.id);
				parent.$("#auditUserName").val(obj.data.name);
				pop_close();
            }
        });
        //重载表格
        pageTableReload = function () {
            tableReload(tableId,{userName:$('#userName').val(),name:$("#name").val()});
        }
        //搜索按钮点击事件
        $('#searchBtn').on('click', function() {
            pageTableReload();
        });
    })
</script>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <label class="layui-form-label">登陆帐号：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="userName" name="userName" class="layui-input" placeholder="请输入登陆帐号" autocomplete="off">
			        </div>
	    		</div>
				<div class="layui-inline">
			        <label class="layui-form-label">用户姓名：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="name" name="name" class="layui-input" placeholder="请输入用户姓名" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
    </div>
    <div class="layui-row">
        <table id="userTable" lay-filter="userTable"></table>
    </div>
</div>
#end