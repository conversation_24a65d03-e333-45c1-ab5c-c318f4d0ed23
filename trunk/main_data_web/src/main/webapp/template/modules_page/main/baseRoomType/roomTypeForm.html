#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form" style="margin-left: 10px;margin-top: 20px;" id="roomTypeForm">
    <div class="layui-col-xs6">
    <input type="hidden" id="roomTypeId" name="roomType.id" value="#(roomType.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">类型名称</label>
        <div class="layui-input-inline">
            <input type="text" name="roomType.typeName" class="layui-input" lay-verify="required" value="#(roomType.typeName??)" placeholder="请输入类型名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">所属基地</label>
        <div class="layui-input-inline">
            <select id="baseId" name="roomType.baseId" lay-verify="required">
                <option value="">请选择基地</option>
                #for(base : baseList)
                <option value="#(base.id??)" #if(base.id??==roomType.baseId??) selected #end>#(base.baseName??)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可预定</label>
        <div class="layui-input-inline">
            <select id="isBooking" name="roomType.isBooking" lay-verify="required">
                <option value="0" #(roomType != null ?(roomType.isBooking == '0' ? 'selected':''):'')>可预定</option>
                <option value="1" #(roomType != null ?(roomType.isBooking == '1' ? 'selected':''):'')>不可预定</option>
            </select>
        </div>
    </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否大床房</label>
            <div class="layui-input-inline">
                <select id="isBigBedRoom" name="roomType.isBigBedRoom" lay-verify="required">
                    <option value="0" #(roomType != null ?(roomType.isBigBedRoom == '0' ? 'selected':''):'')>否</option>
                    <option value="1" #(roomType != null ?(roomType.isBigBedRoom == '1' ? 'selected':''):'')>是</option>
                </select>
            </div>
        </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-inline">
            <select id="isEnable" name="roomType.isEnable" lay-verify="required">
                <option value="0" #(roomType != null ?(roomType.isEnable == '0' ? 'selected':''):'')>可用</option>
                <option value="1" #(roomType != null ?(roomType.isEnable == '1' ? 'selected':''):'')>不可用</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">包房天数</label>
        <div class="layui-input-inline">
            <input type="text" name="roomType.privateRoomDays" class="layui-input" value="#(roomType.privateRoomDays??)" placeholder="请输入包房天数">
        </div>
    </div>
    </div>
    <div class="layui-col-xs6">
        <div class="layui-upload" style="text-align: center;">
            <div class="layui-upload-list">
                <img class="layui-upload-img" id="profilePhotoImg" width="200px" src="#(roomType ? (roomType.roomTypeImage ? roomType.roomTypeImage :'') : '')">
                <p id="profilePhotoText"></p>
                <input type="hidden" id="uploadPath" name="roomType.roomTypeImage" value="#(roomType.roomTypeImage ??)">
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
            <input type="hidden" id="fileId" name="roomType.fileId" value="#(roomType.fileId??)"/>
            <button type="button" class="layui-btn" id="profilePhoto">上传房间类型图片</button>
            &nbsp;&nbsp;
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/baseRoomType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#roomTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        //文件上传
        var uploadInst = upload.render({
            elem: '#profilePhoto'
            ,url:$("#commonUpload").val() +'/upload?bucket=roomType'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#profilePhotoImg').attr('src', result);
                });
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){
                    $("#uploadPath").val(res.data.src);
                    $("#fileId").val(res.data.id);
                    layer.msg(res.msg,{icon:1,time:5000});
                }else {
                    return layer.msg('上传失败');
                }
            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });
    });
</script>
#end
