#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        roomTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            roomTypeLoad(data.field);
            return false;
        });

        function roomTypeLoad(data){
            table.render({
                id : 'roomTypeTable'
                ,elem : '#roomTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/main/businessEntity/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'entityName', title: '业务实体名称', align: 'center', unresize: true}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'sort', title: '排序', align: 'center', unresize: true}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/businessEntity/form" ;
            pop_show("新增业务实体",url,700,450);
        });

        table.on('tool(roomTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/businessEntity/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('roomTypeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/businessEntity/form?id=" + obj.data.id ;
                pop_show("编辑业务实体",url,700,450);
            }
        });


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:entity:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:entity:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            类型名称:
            <div class="layui-inline">
                <input id="entityName" name="entityName" class="layui-input">
            </div>

            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:entity:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end

    </div>
    <table id="roomTypeTable" lay-filter="roomTypeTable"></table>
</div>
#end