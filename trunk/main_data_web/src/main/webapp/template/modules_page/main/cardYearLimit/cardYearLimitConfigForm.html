#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>排序</label>
			<div class="layui-input-block">
				<input type="text" name="sort" class="layui-input" lay-verify="required|Number" value="#(model.sort??)" placeholder="请输入排序">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>基地</label>
			<div class="layui-input-block">
				<select id="baseId" name="baseId"lay-verify="required">
					<option value="">直接选择或搜索选择</option>
					#for(base : baseList)
						<option value="#(base.id)" #if(base.id==model.baseId??)selected="selected"#end>#(base.baseName??)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>床位预订最大数量</label>
			<div class="layui-input-block">
				<input type="text" name="bedBookLimit" class="layui-input" lay-verify="required|Number" value="#(model.bedBookLimit??)" placeholder="请输入床位预订最大数量">
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				<input type="hidden" name="yearLimitId" value="#(model.yearLimitId??'')">
				<input type="hidden" name="oldBaseId" value="#(model.baseId??'')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	form.render('select');
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('layuiTab', 'form');
    }
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/cardYearLimit/saveCardYearLimitConfig',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
					location.reload();
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end