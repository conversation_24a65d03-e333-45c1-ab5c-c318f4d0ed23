#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()短信类型表单#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="modelForm">
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>类型名称</label>
            <div class="layui-input-block">
                <input type="text" name="typeName" value="#(model.typeName??)" autocomplete="off" placeholder="请输入类型名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">模板</label>
            <div class="layui-input-block">
                <textarea name="typeTemplate" rows="5" placeholder="请输入短信模板" class="layui-textarea">#(model.typeTemplate??'')</textarea>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <input name="id" type="hidden" value="#(model.id??)" />
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate','jquery'],function(){
        var form = layui.form;
        var $ = layui.$;
        
        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/smsType/save',
                data: $("#modelForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableLoad(null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end