#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()银行信息编辑#end

#define css()

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>银行名称</label>
			<div class="layui-input-block">
				<input name="bankName" value="#(model.bankName??)" lay-verify="required" placeholder="请输入银行名称" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="id" name="id" value="#(model.id??)"/>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button id="saveBtn" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		form.on('submit(saveBtn)',function (obj) {
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/bank/save',
				data: obj.field,
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state==='ok'){
						pop_close();
						parent.bankTableReload();
					}
				},
				complete : function() {
				}
			});

			return false;
		});

	});
</script>
#end