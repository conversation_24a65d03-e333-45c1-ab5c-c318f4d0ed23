#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()保存旅游线路#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;" id="touristRouteForm">
    <input type="hidden" id="id" name="id" value="#(touristRoute.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">线路名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" class="layui-input" lay-verify="required" value="#(touristRoute.name??)" placeholder="请输入线路名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">收费天数</label>
        <div class="layui-input-inline">
            <input type="text" name="chargeDays" class="layui-input" lay-verify="required|number" value="#(touristRoute.chargeDays??)" placeholder="请输入收费天数">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">收费金额</label>
        <div class="layui-input-inline">
            <input type="text" name="amount" class="layui-input" lay-verify="required|number" value="#(touristRoute.amount??)" placeholder="请输入收费金额">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">收费点数</label>
        <div class="layui-input-inline">
            <input type="text" name="points" class="layui-input" lay-verify="required|number" value="#(touristRoute.points??)" placeholder="请输入收费点数">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 210px;">入住天数（含非基地入住）</label>
        <div class="layui-input-inline">
            <input type="text" name="checkinDays" class="layui-input" lay-verify="required|number" value="#(touristRoute.checkinDays??)" placeholder="请输入入住天数">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">排序</label>
        <div class="layui-input-inline">
            <input type="text" name="order" class="layui-input" lay-verify="required|number" value="#(touristRoute.order??)" placeholder="请输入排序">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="0" lay-verify="required" title="可用" #if(touristRoute!=null && touristRoute.isEnable=='0') checked #elseif(touristRoute==null) checked #end>
            <input type="radio" name="isEnable" value="1" lay-verify="required" title="不可用" #if(touristRoute.isEnable??=='1') checked #end>
        </div>
    </div>
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">描述</label>
        <div class="layui-input-block">
            <textarea name="description" placeholder="请输入内容" class="layui-textarea">#(touristRoute.description??)</textarea>
        </div>
    </div>
    <div class="layui-form-footer" style="margin-top: 90px;">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/touristRoute/saveTouristRoute";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#touristRouteForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.touristRouteTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
