#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;


        /*var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#orgId").val(treeNode.id);
                    $("#orgName").val(treeNode.name);
                }
            }
        };

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);*/



        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/csam/assetTypeFieldSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#modelForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.goodModelTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()

<!--<div class="layui-col-xs3 layui-col-sm3 layui-col-md3 layui-col-lg3">
    <fieldset class="layui-elem-field layui-field-title" style="display:block;">
        <legend>组织架构</legend>
        <div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
    </fieldset>
</div>
<div class="layui-col-xs9 layui-col-sm9 layui-col-md9 layui-col-lg9">

</div>-->
<div class="layui-row" style="padding-left: 10px;">

    <form class="layui-form layui-form-pane" style="margin-top: 20px;" id="modelForm">
        <input type="hidden" name="id" value="#(model.id??)"/>
        <input type="hidden" id="goodTypeId" name="assetTypeId"  class="layui-input" lay-verify="required" value="#if(model==null)#(assetTypeId??)#else#(model.assetTypeId??)#end" placeholder="请输入职位名称">
        <div class="layui-form-item" >
            <label class="layui-form-label" style="padding: 8px 5px;">所属类型</label>
            <div class="layui-input-block">
                <input type="text" class="layui-input" lay-verify="required" readonly value="#(assetTypeName??)" placeholder="">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">属性名称</label>
            <div class="layui-input-block">
                <input type="text" name="fieldName" class="layui-input" lay-verify="required" value="#(model.fieldName??)" placeholder="请输入属性名称">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">最大长度</label>
            <div class="layui-input-block">
                <input type="text" name="maxLength" class="layui-input" lay-verify="required|number" value="#(model.maxLength??)" placeholder="请输入最大长度">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="text" name="assetTypeFieldOrder" class="layui-input" lay-verify="required|number" value="#(model.assetTypeFieldOrder??)" placeholder="请输入排序">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">是否可空</label>
            <div class="layui-input-block">
                <input type="radio" name="isNulllable" value="1" title="是" #if(model==null || model.isNulllable??=='1') checked #end >
                <input type="radio" name="isNulllable" value="0" title="否" #if(model.isNulllable??=='0') checked #end>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">是否可用</label>
            <div class="layui-input-block">
                <input type="radio" name="isEnabled" value="1" title="是" #if(model==null || model.isEnabled??=='1') checked #end >
                <input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled??=='0') checked #end>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
        </div>
    </form>
</div>
#end
