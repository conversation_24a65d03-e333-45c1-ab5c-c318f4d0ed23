#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床垫类型管理#end

#define css()
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            预订渠道名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
        </form>
        #shiroHasPermission("main:bookChannel:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="bookChannelTable" lay-filter="bookChannelTable"></table>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'bookChannelTable'
            ,elem : '#bookChannelTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/main/bookChannel/findPageList'
            ,cellMinWidth: 80
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field:'dictValue', title: '预订渠道编码', align: 'center', unresize: true}
                ,{field:'name', title: '名称', align: 'center', unresize: true}
                ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                /*,{field:'description', title: '备注', align: 'center', unresize: true}*/
                ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });

        table.on('tool(bookChannelTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/bookChannel/delBookChannel',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                bookChannelTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/bookChannel/bookChannelForm?id=" + obj.data.id ;
                pop_show("编辑预订渠道",url,500,500);
            }
        });

        bookChannelTableReload=function(){
            table.reload('bookChannelTable',{"where":{"name":$("#name").val()}});
        }

        // 搜索
        $("#search").click(function(){
            bookChannelTableReload();
        });

        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/bookChannel/bookChannelForm" ;
            pop_show("新增预订渠道",url,500,500);
        });


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:bookChannel:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:bookChannel:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end
