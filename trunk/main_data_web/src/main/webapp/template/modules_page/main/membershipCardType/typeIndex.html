#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别首页#end

#define css()
<style>
	.v-theme .layui-btn-sm{
		margin-top: 5px;
		margin-left: 10px;
	}
</style>
#end

#define js()
<script type="text/javascript">
	var table,form,$ ;
	layui.use(['table','form'],function(){
		table = layui.table ,
				form = layui.form
		$ = layui.$ ;

		typeLoad(null);

		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/main/cardtype/form" ;
			layerShow("新增",url,700,600);
		});

		function typeLoad(data){
			table.render({
				elem: '#typeTable'
				,url : '#(ctxPath)/main/cardtype/findListPage'
				,method : 'POST'
				,height:$(document).height()*0.85
				,where : data
				,cols: [[
					{type:'checkbox'},
					{type: 'numbers', width:60, title: '序号',unresize:true}
					,{field: 'cardType', title: '类别名称',unresize:true}
					,{field:'', title:'类别类型', width:100, unresize:true, align:'center', templet: '#dictTpl("type_category", "typeCategory")'}
					,{field:'', title:'类别分类', width:100, unresize:true, align:'center', templet: '#dictTpl("type_classify", "typeClassify")'}
					,{field: '', width:120, title: '是否开通金额',unresize:true,templet:"<div>{{d.isBalance == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isBalance == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否开通天数',unresize:true,templet:"<div>{{d.isConsumeTimes == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isConsumeTimes == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否开通点数',unresize:true,templet:"<div>{{d.isConsumePoints == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isConsumePoints == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否滚动积分',unresize:true,templet:"<div>{{d.isIntegral == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isIntegral == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, width:100, title: '是否可预订',unresize:true,templet:"<div>{{d.isBooking == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isBooking == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, width:150, title: '是否不发验证码',unresize:true,templet:"<div>{{d.isNotSendSms == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isNotSendSms == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, width:150, title: '是否不签协议',unresize:true,templet:"<div>{{d.isNotSignAgreement == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isNotSignAgreement == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
// 					,{field: 'bookMax', width:120, title: '最大预订数量',unresize:true}
// 					,{field: 'checkinMax', width:120, title: '最大入住数量',unresize:true}
// 					,{field: 'cardPrefix', width:100, title: '卡号前缀',unresize:true}
// 					,{field: 'cardMin', title: '号段',unresize:true,templet:function (d) {
// 						var str="";
// 						if(d.cardMin != null && d.cardMin != ''){
// 							str = d.cardMin;
// 						}else{
// 							str = "--";
// 						}
// 						str += " - ";
// 						if(d.cardMax != null && d.cardMax != null){
// 							str += d.cardMax;
// 						}else{
// 							str += "--";
// 						}
// 						return str;
// 					}}
					,{field:'remark',title:'描述',unresize:true}
					,{title: '操作',width:160,toolbar:'#toolBar',unresize:true}
				]],
				id:'typeTable',
				page : true,
				limit : 15,
				limits: [15,20,25]
			}) ;
		};

		table.on('tool(typeTableFilter)',function(obj){
			if (obj.event === 'edit') {
				var url = "#(ctxPath)/main/cardtype/form?id=" + obj.data.id ;
				layerShow("编辑",url,700,600);
			} else if (obj.event === 'integralConfig') {
				var url = "#(ctxPath)/main/cardtype/integralConfig?id=" + obj.data.id ;
				layerShow("积分配置",url);
			} else if (obj.event === 'del') {
				layer.confirm("确定要作废吗?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/main/cardtype/delete",
						type:'post',
						data:{"id":obj.data.id},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('typeTable');
							}
							layer.close(index);
						}
					});
				});
			}
		});

		form.on("submit(search)",function(data){
			typeLoad(data.field);
			return false;
		});

		//批量获取被作废数据
		getCheckTableData = function(){
			var typeCheckStatus = table.checkStatus('typeTable');
			// 获取选择状态下的数据
			return typeCheckStatus.data;
		}

		//批量作废
		$("#batchDel").click(function(){
			layer.confirm("确定批量作废吗?",function(index){
				var jsonData=getCheckTableData();
				if(jsonData == null || jsonData == ''){
					layer.msg('请勾选作废数据', function () {});
					return;
				}
				util.sendAjax({
					url:"#(ctxPath)/main/cardtype/batchDel",
					type:'post',
					data:{"typeData":JSON.stringify(jsonData)},
					notice:true,
					success:function(returnData){
						if(returnData.state==='ok'){
							table.reload('typeTable') ;
						}
						layer.close(index);
					}
				});
			});
		});
	}) ;
</script>
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
	#shiroHasPermission("main:cardType:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end

	#shiroHasPermission("main:cardType:integralConfigBtn")
	#[[
	{{#if(d.isIntegral==='1'){}}
		<a class="layui-btn layui-btn-xs" lay-event="integralConfig">积分配置</a>
	{{#}}}
	]]#
	#end

	#shiroHasPermission("main:cardType:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
#end

#define content()
<div class="my-btn-box">
	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
		<div class="layui-row">
			<div class="layui-inline">
				<label class="layui-form-label">类别名称</label>
				<div class="layui-input-inline">
					<input class="layui-input" name="cardType" id="cardType" placeholder="" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">类别类型</label>
				<div class="layui-input-inline">
					<select name="typeCategory">
						<option value="">全部</option>
						#dictOption("type_category", type.typeCategory??'', "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">类别分类</label>
				<div class="layui-input-inline">
					<select name="typeClassify">
						<option value="">全部</option>
						#dictOption("type_classify", type.typeClassify??'', "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<button type="button" class="layui-btn" lay-submit="" lay-filter="search">搜索</button>
				#shiroHasPermission("main:cardType:addBtn")
					<button type="button" id="add" class="layui-btn">添加</button>
				#end
				#shiroHasPermission("main:cardType:batchDelBtn")
					<button type="button" id="batchDel" class="layui-btn">批量作废</button>
				#end
			</div>
		</div>
	</form>
	<div class="layui-row">
		<table class="layui-table" id="typeTable" lay-filter="typeTableFilter"></table>
	</div>
</div>
#end