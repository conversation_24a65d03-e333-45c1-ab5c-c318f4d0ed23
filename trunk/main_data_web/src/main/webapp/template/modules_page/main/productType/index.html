#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()组织架构管理首页#end

#define css()
<style>
	/*.laytable-cell-1-sortStr{
		padding: 0 3px !important;
	}*/
</style>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<div class="layui-col-xs4" style="padding-right: 5px;">
			<div class="layui-row">
				<span class="fl"></span>
				<span class="fr">
				<div class="layui-inline">
					<div class="layui-input-inline">
						<div class="layui-btn-group">
							#shiroHasPermission("main:MallManage:typeAddBtn")
							<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
							#end
							<a id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
						</div>
					</div>
				</div>
			</span>
			</div>
			<div class="layui-row">
				<table id="orgTreeGrid" lay-filter="orgTreeGrid"></table>
			</div>
		</div>
		<div class="layui-col-xs8">
			<div class="layui-row">
				<form id="typeForm" class="layui-form layui-form-pane" action="" lay-filter="layform" method="post">
					<!--<div class="layui-inline">
						<label class="layui-form-label">货物类型</label>
						<div class="layui-input-inline" style="width:100px;">
							<select id="type" name="type" lay-filter="aihao">
								<option value="">全部</option>
								<option value="Assets">资产</option>
								<option value="Consume">耗材</option>
							</select>
						</div>
					</div>-->
					<div class="layui-inline">
						<label class="layui-form-label">名称</label>
						<div class="layui-input-inline">
							<input type="text" name="name" id="name" placeholder="请输入名称" autocomplete="off" class="layui-input">
						</div>
					</div>


					<div class="layui-inline">
						<label class="layui-form-label">上下架状态</label>
						<div class="layui-input-inline">
							<select id="isSell" name="isSell">
								<option value="">全部</option>
								<option value="1" selected>已上架</option>
								<option value="0">已下架</option>
							</select>
						</div>
					</div>
					<!--<div class="layui-inline">
						<label class="layui-form-label">创建日期</label>
						<div class="layui-inline">
							<div class="layui-input-inline" style="width:100px;">
								<input id="startDate" name="startDate" type="text" autocomplete="off" class="layui-input" placeholder="开始日期">
							</div>
							<div class="layui-input-inline" style="width:100px;">
								<input id="endDate" name="endDate" type="text" autocomplete="off" class="layui-input" placeholder="结束日期">
							</div>
						</div>
					</div>-->
					<div class="layui-inline">
						<div class="layui-btn-group">
							<input type="hidden" name="typeId" id="typeId" >
							<button type="button" id="search" class="layui-btn">搜索</button>
						</div>
					</div>
				</form>
			</div>
			<div class="layui-row">
				<table class="layui-table" id="stockModelTable" lay-filter="stockModelTable"></table>
			</div>
		</div>

		</div>
	</div>

</div>
#end

#define js()
<script type="text/html" id="toolBar">
	#shiroHasPermission("main:MallManage:goodsEditBtn")
	<a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="edit">编辑</a>
	#end

	#shiroHasPermission("main:MallManage:goodsImgBtn")
	<a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="image">图片</a>
	#end

	#shiroHasPermission("main:MallManage:goodsDetailBtn")
	<a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="detail">商品详情</a>
	#end

	#shiroHasPermission("main:MallManage:goodsDownupBtn")
	#[[
	{{#if(d.is_sell == '1'){}}
	<a class="layui-btn layui-btn-danger layui-btn-xs" style="margin-left: 0px;" lay-event="down">下架</a>
	{{#}else{}}
	<!--<a class="layui-btn layui-btn-xs" style="margin-left: 0px;"   lay-event="up">上架</a>-->
	{{#}}}
	]]#
	#end

	#shiroHasPermission("main:MallManage:goodsDelBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" style="margin-left: 0px;" lay-event="del">作废</a>
	#end

</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['treeGrid','form','vip_table','table'],function(){

        // 操作对象
        var layer = layui.layer
            ,form = layui.form
            ,treeGrid = layui.treeGrid
            ,vipTable = layui.vip_table
            ,$ = layui.jquery
            ,tableId = 'orgTreeGrid'
			,table=layui.table
        ;



        //初始化表格
        var ptable=treeGrid.render({
            id: tableId
            , elem: '#'+tableId
            , url: '#(ctxPath)/mall/productType/treeTableTypeList'
            , method: 'post'
            , idField: 'id'//必須字段
            , treeId: 'id'//树形id字段名称
            , treeUpId: 'pId'//树形父id字段名称
            , treeShowName: 'typeName'//以树形式显示的字段
            , isOpenDefault: true//节点默认是展开还是折叠【默认展开】
            , cols: [[
                {field:'typeName', title:'类型名称', unresize:true}
				,{field:'type_isEnabled', title:'是否可用',width:87, unresize:true,templet:function (d) {
						if(d.isEnabled=='1'){
							return '可用'
						}else if(d.isEnabled=='0'){
							return '不可用'
						}else{
							return '- -'
						}
					}}
                ,{field:'type_sort', title:'排序',width:65, unresize:true,templet:function (d) {
						return d.sort;
					}}
                ,{fixed:'right',title:'操作', unresize:true,width:150, align:'left', templet: function(d){
                        var editBtn = ''
                        var delBtn = ''

                        #shiroHasPermission("main:MallManage:typeEditBtn")
                        editBtn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
						#end
						#shiroHasPermission("main:MallManage:typeGoodsBtn")
                        /*editBtn+='<a class="layui-btn layui-btn-xs" lay-event="manager">商品</a>';*/
						#end
						#shiroHasPermission("main:MallManage:typeDelBtn")
						editBtn+='<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>';
                        #end
                        /*delBtn='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>';*/

                        if(d.type=='main_org'){
                            return editBtn;
                        }else{
                            return editBtn+delBtn;
                        }
                    }
                }
            ]],done:function (res, curr, count) {

				treeGridClickRow(this,res,function (data) {
					$("#typeId").val(data.id);
					productTableLoad({"typeId":data.id,"isSell":$("#isSell").val()});

				})
			}
        });

        $("#search").on('click',function () {
			productTableLoad({'name':$("#name").val(),"typeId":$("#typeId").val(),"isSell":$("#isSell").val()});
		})

		reloadProductTableLoad=function(){
			productTableLoad({"typeId":$("#typeId").val(),"isSell":$("#isSell").val()});
		}

		stockModelTableReload=function(){
			productTableLoad({'name':$("#name").val(),"typeId":$("#typeId").val(),"isSell":$("#isSell").val()});
		}

        productTableLoad=function(data){
        	let sortSaveBtn='<button id="saveSortBtn" type="button" onclick="saveSort()" class="layui-btn layui-btn-xs">保存排序</button>';
			table.render({
				id : 'stockModelTable'
				,elem : '#stockModelTable'
				,method : 'POST'
				,where : data
				,limit : 13
				,limits : [13,20,30,40]
				,url : '#(ctxPath)/mall/productType/findProductPageList'
				,height:$(document).height()*0.75
				,cellMinWidth: 80
				,cols: [[
					{field:'sortStr', title: sortSaveBtn,width:80, align: 'center', unresize: true,templet:function (d) {
							return sortInput='<input type="text" class="layui-input sortInput" data-id="'+d.relId+'" value="'+d.sort+'" maxlength="7" autocomplete="off">';
						}}
					,{field:'new_stock_model_no', title: '新商品编号',width:120, align: 'center', unresize: true}
					,{field:'name', title: '商品名称',width:150, align: 'center', unresize: true}
					,{field:'standard', title: '规格型号', align: 'center',width:150, unresize: true}
					,{field:'unit_name', title: '单位', align: 'center',width:70, unresize: true}
					,{field:'cost_price', title: '成本价格', align: 'center',width:90, unresize: true}
					,{field:'exchange_bean_coupons', title: '兑换豆豆券', align: 'center',width:130, unresize: true}
					,{field:'isEnabled', title: '是否有效',width:120, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.is_enabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
					,{field:'isSell', title: '是否上架',width:120, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.is_sell=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
					,{fixed:'right', title: '操作', width: 240, align: 'center', unresize: true, toolbar: '#toolBar'}
				]]
				,page : true
				,done:function () {
					//
					var layerTips;
					$("td").on("mouseenter", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						if (this.offsetWidth < this.firstChild.scrollWidth) {
							var that = this;
							var text = $(this).text();
							layerTips=layer.tips(text, that, {
								tips: 1,
								time: 0
							});
						}
					});
					$("td").on("mouseleave", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						layer.close(layerTips);
					});
					layer.closeAll('loading');
				}
			});
		}

		saveSort = function() {
			var saveFlag = true;
			var dataArray = new Array();
			$(".sortInput").each(function(i,obj){
				var orgId = $(this).attr('data-id');
				var orgSort = obj.value;
				if(orgSort==null || orgSort==''){
					saveFlag = false;
					return false;
				}else{
					var inputObj = {'id':orgId, 'sort':orgSort};
					dataArray.push(inputObj);
				}
			});
			if(saveFlag){
				console.log(JSON.stringify(dataArray));
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/mall/productType/sortBatchSave',
					data: {sortDatas:JSON.stringify(dataArray)},
					notice: true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							$("#search").click();
						}
					},
					complete : function() {
						//保存按钮点击事件
						/*$('#saveSortBtn').on('click', function() {
							saveSort();
						});*/
					}
				});
			}else{
				layer.msg('排序不能为空，请输入排序!',{icon:5});
			}
		}

		table.on('tool(stockModelTable)',function (obj) {
			if(obj.event==='edit') {
				pop_show('编辑', '#(ctxPath)/wms/stockModel/stockModelForm?id=' + obj.data.id, 700, 750);
			}else if(obj.event==='del'){
				layer.confirm("确定要作废吗?",{
					skin : "my-skin"
				},function(index) {

					util.sendAjax({
						type: 'POST',
						url: '#(ctxPath)/mall/productType/delProductTypeRel',
						data: {"id":obj.data.id,'typeId':$("#typeId").val()},
						notice: true,
						loadFlag: true,
						success: function (rep) {
							if (rep.state == 'ok') {
								stockModelTableReload();
								layer.closeAll('loading');
							}
						}
					});

					layer.close(index);
				});
			}else if(obj.event==='up'){
				//上架
				layer.confirm("确定要上架吗?",{
					skin : "my-skin"
				},function(index) {

					util.sendAjax({
						type: 'POST',
						url: '#(ctxPath)/mall/productType/saveProductTypeRel',
						data: {"id":obj.data.id,'isSell':'1'},
						notice: true,
						loadFlag: true,
						success: function (rep) {
							if (rep.state == 'ok') {
								stockModelTableReload();
								layer.closeAll('loading');
							}
						}
					});

					layer.close(index);
				});
			}else if(obj.event==='down'){
				//下架
				layer.confirm("确定要下架吗?",{
					skin : "my-skin"
				},function(index) {

					util.sendAjax({
						type: 'POST',
						url: '#(ctxPath)/mall/productType/saveProductTypeRel',
						data: {"id":obj.data.id,'isSell':'0'},
						notice: true,
						loadFlag: true,
						success: function (rep) {
							if (rep.state == 'ok') {
								stockModelTableReload();
								layer.closeAll('loading');
							}
						}
					});

					layer.close(index);
				});
			}else if(obj.event==='barCode'){
				if(typeof(obj.data.barcodeNumber)!='undefined' && obj.data.barcodeNumber!=''){

					$.ajax({
						data: {'id':obj.data.id},
						url: '#(ctxPath)/wms/stockModel/stockModelBarCode',
						dataType: 'json',
						timeout:30000,
						responseType:'arraybuffer',
						beforeSend: function(XMLHttpRequest){
							if (true) {
								layer.load();
							}
						},
						success: function(json){
							if (json.state == 'ok') {
								$("#myImg").attr("src",json.data);
								$("#imgDiv").css("display","block");

								//页面层
								layer.open({
									type: 1,
									//skin: 'layui-layer-rim', //加上边框
									area: ['420px', '240px'], //宽高
									shade: false,
									title: false, //不显示标题
									content: '<div class="layer_notice" id="barCodePrint" style="height: 75px;width: 300px;margin: 0 auto;margin-top: 75px;">' +
											'<img id="myImg" src="'+json.data+'" style="width: 100%;height: 100%;" >' +
											'</div>' +
											'<p style="text-align: center;font-size: 16px;font-weight: 600;">'+obj.data.name+'('+obj.data.standard+')'+'</p>' +
											'<div class=""><div class="layui-form-item"><label class="layui-form-label" style="position: absolute;right: 145px;bottom: 20px;">打印张数:</label>' +
											'<div class="layui-input-inline" style="position: absolute;right: 100px;bottom: 20px;width: 50px;">' +
											'<input type="text" id="printNum" lay-verify="required" value="1" style="border-color: #1E9FFF;" autocomplete="off" class="layui-input"></div></div></div>' +
											'<a href="javascript:;" style="position: absolute;right: 20px;bottom: 20px;" onclick="printBarCode(\''+obj.data.barcodeNumber+'\',\''+obj.data.name+'\',\''+obj.data.standard+'\',\''+obj.data.exchangePrice+'\')" class="layui-btn layui-btn-normal" >打印</a>'
								});
							}
						},
						complete :function(XMLHttpRequest, TS){
							layer.closeAll('loading');
						}
					});


				}else{
					layer.msg('该商品的条形码编号为空，请检查!', {icon: 2, offset: 'auto'});
				}
			}else if(obj.event==='image'){
				var url = "#(ctxPath)/wms/stockModel/modelImage?id=" + obj.data.id ;
				pop_show("商品图片",url,900,650);
			}else if(obj.event==='salePrice'){
				var url = "#(ctxPath)/wms/stockModel/warehousePriceForm?modelId=" + obj.data.id ;
				pop_show("商品仓库销售价格",url,900,650);
			}else if(obj.event=='detail'){

				var url = "#(ctxPath)/mall/productType/productDetailIndex?id=" + obj.data.id ;
				pop_show("商品详情",url,1400,750);

			}
		});

		modelImageIndex=function(id){
			var url = "#(ctxPath)/wms/stockModel/modelImage?id=" + id ;
			pop_show("商品图片",url,900,650);
		}

        //表格事件绑定
        treeGrid.on('tool('+tableId+')',function (obj) {
            if(obj.event==="edit"){//编辑按钮事件
                pop_show('编辑','#(ctxPath)/mall/productType/form?id='+obj.data.id,600,600);
            }else if(obj.event==='manager'){
                pop_show('商品','#(ctxPath)/mall/productType/stockModelIndex?typeId='+obj.data.id,'100%', '100%');
            }else if(obj.event==='del'){
				layer.confirm("确定要作废吗?",{
					skin : "my-skin"
				},function(index) {

					util.sendAjax({
						type: 'POST',
						url: '#(ctxPath)/mall/productType/delProductType',
						data: {"id":obj.data.id},
						notice: true,
						loadFlag: true,
						success: function (rep) {
							if (rep.state == 'ok') {
								tableGridReload();
								layer.closeAll('loading');
							}
						}
					});

					layer.close(index);
				});
			}

        });
        //表格重载
        tableGridReload = function () {
            treeGrid.reload(tableId, {});
        }
        //添加按钮点击事件
        $('#addBtn').on('click', function() {
            pop_show('添加','#(ctxPath)/mall/productType/form',600,600);
        });
        // 刷新
        $('#refreshBtn').on('click', function () {
            tableGridReload();
        });

    });
</script>
#end