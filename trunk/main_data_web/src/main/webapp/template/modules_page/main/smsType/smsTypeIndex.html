#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()短信类型管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
			<div class="layui-inline">
				<label class="layui-form-label">类型名称:</label>
				<div class="layui-input-inline">
					<input id="typeName" name="typeName" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-btn-group">
					<button type="button" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
					#shiroHasPermission("main:smsType:addBtn")
						<button type="button" id="addBtn" class="layui-btn">添加</button>
					#end
				</div>
			</div>
		</form>
	</div>
    <div class="layui-row">
        <table class="layui-table" id="smsTypeTable" lay-filter="smsTypeTable"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="toolBar">
	#shiroHasPermission("main:smsType:editBtn")
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("main:smsType:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		tableLoad=function(data){
			table.render({
				id : 'smsTypeTable'
				,elem : '#smsTypeTable'
				,method : 'POST'
				,where : data
				,limit : 10
				,limits : [10,20,30,40]
				,url : '#(ctxPath)/main/smsType/smsTypePage'
				,cellMinWidth: 80
				,cols: [[
					{type: 'numbers', width:80, title: '序号',unresize:true}
					,{field:'typeName', title: '类型名称', align: 'center', unresize: true}
					,{field:'typeTemplate', title: '模板', align: 'center', unresize: true}
					,{field:'createTime', title: '创建时间', width: 180, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#toolBar'}
				]]
				,page : true
				,done:function () {
				}
			});
			table.on('tool(smsTypeTable)',function (obj) {
				if(obj.event==='edit'){
					pop_show('编辑','#(ctxPath)/main/smsType/edit?id='+obj.data.id,'','');
				}else if(obj.event==='del'){
					layer.confirm("确定要作废吗?",function(index){
						util.sendAjax ({
							type: 'POST',
							url: '#(ctxPath)/main/smsType/del',
							notice: true,
							data: {id:obj.data.id, delFlag:'1'},
							loadFlag: true,
							success : function(rep){
								if(rep.state=='ok'){
									tableLoad(null);
								}
								layer.close(index);
							},
							complete : function() {
							}
						});
					});
				}
			});
		}

		tableLoad(null);

		form.on("submit(search)",function(data){
			tableLoad(data.field);
			return false;
		});

        $("#addBtn").on('click',function () {
            pop_show('添加','#(ctxPath)/main/smsType/add','','');
        });
    });
</script>
#end