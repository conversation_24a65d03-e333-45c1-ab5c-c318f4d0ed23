#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()品牌管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
    <div class="layui-row">
        <div class="layui-inline">
            <label class="layui-form-label">品牌名称</label>
            <div class="layui-input-inline">
                <input id="assetBrandName" name="assetBrandName" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <div class="layui-btn-group">
                <button type="button" id="search" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
                #shiroHasPermission("main:csam:brand:addBtn")
                <button type="button" id="add" class="layui-btn">添加</button>
                #end
            </div>
        </div>
    </div>
    </form>
    <div class="layui-row">
        <table id="mattressTypeTable" lay-filter="mattressTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:csam:brand:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        mattressTypeTableReload = function (data) {
            table.render({
                id: 'mattressTypeTable'
                , elem: '#mattressTypeTable'
                , method: 'POST'
                , height: $(document).height() * 0.85
                , limit: 15
                , limits: [15, 30, 45, 50]
                , url: '#(ctxPath)/main/csam/assetBrandPageList'
                , where : data
                , cellMinWidth: 80
                , cols: [[
                    {field: 'assetBrandNo', title: '品牌编码', align: 'center', unresize: true}
                    , {field: 'assetBrandName', title: '品牌名称', align: 'center', unresize: true}
                    , {
                        field: 'isEnabled',
                        title: '是否可用',
                        align: 'center',
                        unresize: true,
                        templet: "<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"
                    }
                    /*,{field:'description', title: '备注', align: 'center', unresize: true}*/
                    , {fixed: 'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                , page: true
            });
            table.on('tool(mattressTypeTable)', function (obj) {
                if (obj.event === 'edit') {
                    var url = "#(ctxPath)/main/csam/assetBrandForm?id=" + obj.data.id;
                    pop_show("编辑床垫类型", url, 500, 500);
                }
            });
        }

        mattressTypeTableReload(null);

        form.on("submit(search)",function(data){
            mattressTypeTableReload(data.field);
            return false;
        });

        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/csam/assetBrandForm" ;
            pop_show("新增品牌",url,500,500);
        });
    });
</script>
#end
