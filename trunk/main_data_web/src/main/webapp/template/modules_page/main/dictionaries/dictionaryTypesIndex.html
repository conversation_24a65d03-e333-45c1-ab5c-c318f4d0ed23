#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        dictionariesLoad(null);

        sd=form.on("submit(search)",function(data){
            dictionariesLoad(data.field);
            return false;
        });

        dictionariesReLoad=function () {
            dictionariesLoad({"name":$("#name").val()});
        }
        
        function dictionariesLoad(data){
            table.render({
                id : 'dictionariesTable'
                ,elem : '#dictionariesTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/main/dictionaries/dictionarieTypesPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '名称', align: 'center', unresize: true}
                    ,{field:'isEnabled', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'description', title: '备注', align: 'center', unresize: true}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/dictionaries/dictionarieTypesForm" ;
            pop_show("新增字典类型",url,500,500);

        });

        table.on('tool(dictionariesTable)',function(obj){
            if (obj.event === 'dictionaries') {
                var url = "#(ctxPath)/main/dictionaries/dictionariesIndex?typeId=" + obj.data.id ;
                layer.open({
                    type: 2,
                    area: ['100%', '100%'],
                    fix: false, //不固定
                    maxmin: true,
                    shadeClose: true,
                    shade:0.4,
                    title: '字典值',
                    content: url,
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/dictionaries/dictionarieTypesForm?id=" + obj.data.id ;
                pop_show("编辑字典类型",url,500,500);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:dictionaries:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:dictionaries:dictionarieValueBtn")
    <a class="layui-btn layui-btn-xs" lay-event="dictionaries">字典值</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            字典类型名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:dictionaries:addTypeBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加字典类型</button>
        #end
    </div>
    <table id="dictionariesTable" lay-filter="dictionariesTable"></table>
</div>
#end