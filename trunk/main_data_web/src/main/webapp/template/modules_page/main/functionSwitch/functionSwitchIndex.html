#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        functionSwitchLoad(null);

        sd=form.on("submit(search)",function(data){
            functionSwitchLoad(data.field);
            return false;
        });

        function functionSwitchLoad(data){
            table.render({
                id : 'functionSwitchTable'
                ,elem : '#functionSwitchTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/main/functionSwitch/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'functionName', title: '功能配置', align: 'center', unresize: true}
                    ,{field:'switchFlag', title: '开关', align: 'center', unresize: true,templet:"<div>{{ d.switchFlag=='1'?'<span class='layui-badge layui-bg-green'>已开启</span>':d.switchFlag=='0'?'<span class='layui-badge'>已关闭</span>':'- -' }}</div>"}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/functionSwitch/form" ;
            pop_show("新增功能配置",url,500,350);
        });

        table.on('tool(functionSwitchTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/functionSwitch/delete',
                        notice: true,
                        data: {id:obj.data.id, delFlag:'1'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('functionSwitchTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/functionSwitch/form?id=" + obj.data.id ;
                pop_show("编辑功能配置",url,500,350);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:functionSwitch:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:functionSwitch:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            功能配置:
            <div class="layui-inline">
                <select name="function" lay-search>
                    <option value="">请选择功能配置</option>
                    #for(b : dictList)
                    <option value="#(b.dictValue)">#(b.dictName)</option>
                    #end
                </select>
            </div>
            &nbsp;&nbsp;
            开关:
            <div class="layui-inline">
                <select name="switchFlag" lay-search>
                    <option value="">请选择开关状态</option>
                    <option value="0">已关闭</option>
                    <option value="1">已开启</option>
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:functionSwitch:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="functionSwitchTable" lay-filter="functionSwitchTable"></table>
</div>
#end