#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()楼栋编辑#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
<style>
	#form .layui-form-radio{
		margin:8px 2px;
	}



</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<table width="100%">
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*楼层名称</label>
						<div class="layui-input-inline">
							<input name="floorName" value="#(floor.floorName??)" required lay-verify="required" placeholder="请输入楼层名称" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*楼层编号</label>
						<div class="layui-input-inline">
							<input name="floorNo" value="#(floor.floorNo??)" required lay-verify="required|number" placeholder="请输入楼层编号" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td rowspan="3">
					<div class="layui-upload" style="text-align: center;height: 250px;width: 200px;background-color: #f2f2f2;" >
						<div class="layui-upload-list">
							<img class="layui-upload-img" id="profilePhotoImg" style="max-height: 250px;max-width: 200px;" src="#if(floor.floorImage??!=null)#(floor.floorImage??)#end" >
							<p id="profilePhotoText"></p>
							<input type="hidden" id="uploadPath" name="floorImage" value="#(floor.floorImage??)">
						</div>
					</div>
				</td>
			</tr>
			<tr>
                <td>
                    <div class="layui-form-item">
                        <label class="layui-form-label">*是否可预订</label>
                        <div class="layui-input-inline">
                            <input type="radio" name="isBooking" lay-verify="required" value="0" #if(floor==null) checked #elseif(floor.isBooking??=='0') checked #end  title="可预订">
                            <input type="radio" name="isBooking" lay-verify="required" value="1" #if(floor.isBooking??=='1') checked #end title="不可预订">
                        </div>
                    </div>
                </td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否可用</label>
						<div class="layui-input-inline">
							<input type="radio" name="isEnable" lay-verify="required" value="0" #if(floor==null) checked #elseif(floor.isEnable??=='0') checked #end  title="可用">
							<input type="radio" name="isEnable" lay-verify="required" value="1" #if(floor.isEnable??=='1') checked #end title="不可用">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否可线上预订</label>
						<div class="layui-input-block">
							<select id="isOutBookabled" name="isOutBookabled" lay-verify="required">
								<option value="">请选择是否可线上预订</option>
								<option value="1" #(floor != null ?(floor.isOutBookabled == '1' ? 'selected':''):'')>可预订</option>
								<option value="0" #(floor != null ?(floor.isOutBookabled == '0' ? 'selected':''):'')>不可预订</option>
							</select>
						</div>
					</div>
				</td>

				<td>

				</td>
			</tr>

			<tr>
				<td colspan="2">
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>基地锁服务地址</label>
						<div class="layui-input-block">
							<input type="text" id="lockService" name="lockService" value="#(floor.lockService??)" autocomplete="off" placeholder="请输入基地锁服务地址" class="layui-input" lay-verify="">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item">
						<label class="layui-form-label">预定渠道</label>
						<div class="layui-input-block">
							<select xm-select="bookChannel" name="bookChannel" id="bookChannel">
								#for(bookChannel : bookChannelList)
								<option value="#(bookChannel.id??)" #if(floor==null) selected #elseif(floorChannelIds??!=null && floorChannelIds.indexOf(bookChannel.id)!=-1) selected #end >#(bookChannel.name??)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item layui-form-text">
						<label class="layui-form-label">描述</label>
						<div class="layui-input-block">
							<textarea name="remark" placeholder="请输入描述" class="layui-textarea">#(floor.remark??)</textarea>
						</div>
					</div>
				</td>
			</tr>

		</table>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="fileId" name="fileId" value="#(floor.fileId??)"/>
				<input type="hidden" id="commonUpload" value="#(commonUpload)"/>
				<input type="hidden" id="id" name="id" value="#(floor.id??)"/>
				<input type="hidden" id="buildingId" name="buildingId" value="#if(floor==null)#(buildingId)#else#(floor.buildingId??)#end"/>
				<button type="button" class="layui-btn" id="profilePhoto">上传图片</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"/>
<script src="layui.all.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script>
	var formSelects=layui.formSelects;
	layui.use(['form','layer','table','upload'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,upload=layui.upload;

		formSelects.render('bookChannel');

		form.on('submit(confirmBtn)',function (obj) {
			if(obj.field.buildingId==='' || obj.field.buildingId===null || typeof(obj.field.buildingId) ==='undefined'){
				layer.msg('楼栋id不能为空',{icon:5,time:5000});
				return false;
			}
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/buildingRoomManage/saveFloor',
				data: obj.field,
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state==='ok'){
						pop_close();
						parent.floorTableReload();
					}
				},
				complete : function() {
				}
			});

			return false;
		});


		//普通图片上传
		var uploadInst = upload.render({
			elem: '#profilePhoto'
			,url:$("#commonUpload").val() +'/upload?bucket=floor'
			,before: function(obj){
				$("#confirmBtn").attr("disabled",true);
				$("#confirmBtn").addClass("layui-btn-disabled");
				//预读本地文件示例，不支持ie8
				obj.preview(function(index, file, result){
					$('#profilePhotoImg').attr('src', result);
				});
			}
			,done: function(res){
				$("#confirmBtn").attr("disabled",false);
				$("#confirmBtn").removeClass("layui-btn-disabled");
				//如果上传失败
				if(res.state === 'ok'){
					$("#uploadPath").val(res.data.src);
					$("#fileId").val(res.data.id);
					layer.msg(res.msg,{icon:1,time:5000});
				}else {
					return layer.msg('上传失败');
				}
			}
			,error: function(){
				//演示失败状态，并实现重传
				var demoText = $('#demoText');
				demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
				demoText.find('.demo-reload').on('click', function(){
					uploadInst.upload();
				});
			}
		});

	});
</script>
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end