#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位类型展示#end

#define css()

#end

#define content()
<form class="layui-form" style="margin-left: 10px;margin-top: 20px;" id="bedTypeForm">
    <div class="layui-col-xs6">
    <input type="hidden" id="bedTypeId" name="bedType.id" value="#(bedType.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 90px;">类型名称</label>
        <div class="layui-input-inline">
            <input type="text" name="bedType.typeName" class="layui-input" lay-verify="required" value="#(bedType.typeName??)" placeholder="请输入类型名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 90px;">是否可用</label>
        <div class="layui-input-inline">
            <select id="isEnable" name="bedType.isEnable" lay-verify="required">
                <option value="0" #(bedType != null ?(bedType.isEnable == '0' ? 'selected':''):'')>可用</option>
                <option value="1" #(bedType != null ?(bedType.isEnable == '1' ? 'selected':''):'')>不可用</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 90px;">是否可多人用</label>
        <div class="layui-input-inline">
            <select id="isManyPeople" name="bedType.isManyPeople" lay-verify="required" lay-filter="isManyPeople">
                <option value="0" #(bedType != null ?(bedType.isManyPeople == '0' ? 'selected':''):'')>可多人用</option>
                <option value="1" #(bedType != null ?(bedType.isManyPeople == '1' ? 'selected':''):'')>不可多人用</option>
            </select>
        </div>
    </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 90px;">是否大床位</label>
            <div class="layui-input-inline">
                <select id="isBigBed" name="bedType.isBigBed" lay-verify="required" lay-filter="required">
                    <option value="">请选择</option>
                    <option value="1" #(bedType != null ?(bedType.isBigBed == '1' ? 'selected':''):'')>是</option>
                    <option value="0" #(bedType != null ?(bedType.isBigBed == '0' ? 'selected':''):'')>否</option>
                </select>
            </div>
        </div>
    <div class="layui-form-item" id="peopleMaxBox" #if(bedType.isManyPeople??=='1') style="display: none;" #end>
        <label class="layui-form-label" style="width: 90px;">最多人数</label>
        <div class="layui-input-inline">
            <input type="text" name="bedType.peopleMax" id="peopleMax" class="layui-input" #if(bedType==null || bedType.isManyPeople??=='0') lay-verify="required" #end value="#(bedType.peopleMax??)" placeholder="请输入类型名称">
        </div>
    </div>
    </div>
    <div class="layui-col-xs6">
        <div class="layui-upload" style="text-align: center;">
            <div class="layui-upload-list">
                <img class="layui-upload-img" id="profilePhotoImg" width="200px" src="#(bedType ? (bedType.bedTypeImage ? bedType.bedTypeImage :'') : '')">
                <p id="profilePhotoText"></p>
                <input type="hidden" id="uploadPath" name="bedType.bedTypeImage" value="#(bedType.bedTypeImage ??)">
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
            <input type="hidden" id="fileId" name="bedType.fileId" value="#(bedType.fileId??)"/>
            <button type="button" class="layui-btn" id="profilePhoto">上传床位类型图片</button>
            &nbsp;&nbsp;
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;

        form.on('select(isManyPeople)',function (obj) {
            if(obj.value==='0'){
                $("#peopleMaxBox").css("display","block");
                $("#peopleMax").attr("lay-verify","required");
            }else{
                $("#peopleMaxBox").css("display","none");
                $("#peopleMax").attr("lay-verify","");
            }
        });


        //保存
        form.on('submit(saveBtn)', function(obj){
            var url = "#(ctxPath)/main/bedType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: obj.field,
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("bedTypeTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        //文件上传
        var uploadInst = upload.render({
            elem: '#profilePhoto'
            ,url:$("#commonUpload").val() +'/upload?bucket=bedType'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#profilePhotoImg').attr('src', result);
                });
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){
                    $("#uploadPath").val(res.data.src);
                    $("#fileId").val(res.data.id);
                    layer.msg(res.msg,{icon:1,time:5000});
                }else {
                    return layer.msg('上传失败');
                }
            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });

    });
</script>
#end
