#include("/template/common/layout/_part_layout.html")
#@part()

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate', 'element' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, element = layui.element
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	laydate.render({
		elem: '#beginTime' //指定元素
		, type: 'time'
		, format: 'HH:mm'
	});
	
	laydate.render({
		elem: '#endTime' //指定元素
		, type: 'time'
		, format: 'HH:mm'
	});
	
	form.render('select');
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('baseMealTimeTab', 'form');
    }
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/base/saveMealTime',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
					location.reload();
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>开始时间</label>
			<div class="layui-input-block">
				<input type="text" id="beginTime" name="beginTime" class="layui-input" lay-verify="required" value="#date(model.beginTime??, 'HH:mm')" placeholder="请输入开始时间">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>结束时间</label>
			<div class="layui-input-block">
				<input type="text" id="endTime" name="endTime" class="layui-input" lay-verify="required" value="#date(model.endTime??, 'HH:mm')" placeholder="请输入结束时间">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>早中晚标识</label>
			<div class="layui-input-block">
				<select id="mealTimeFlag" name="mealTimeFlag"lay-verify="required">
					#statusOption(com.cszn.integrated.service.entity.status.MealTimeFlag::me(), model.mealTimeFlag??"")
				</select>
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				#if(com.jfinal.kit.StrKit::isBlank(model.id))
					<input type="hidden" name="baseId" value="#(model.baseId??'')">
				#end
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end