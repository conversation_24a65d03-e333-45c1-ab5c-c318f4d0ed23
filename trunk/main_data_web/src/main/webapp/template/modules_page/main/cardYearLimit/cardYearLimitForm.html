#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡年限表单#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="cardYearLimitForm">
    <div class="layui-row">
        <input type="hidden" name="cardYearLimit.id" value="#(cardYearLimit.id??)"/>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 145px;">年限类型名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="cardYearLimit.name" class="layui-input" lay-verify="required" value="#(cardYearLimit.name??)" placeholder="请输入年限类型名称">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 145px;">字典值</label>
                <div class="layui-input-inline" style="width: 210px;">
                    <input type="text" name="cardYearLimit.dictValue" class="layui-input" lay-verify="required" value="#(cardYearLimit.dictValue??)" placeholder="请输入字典值">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 145px;">床位预订最大数量</label>
                <div class="layui-input-inline" style="width: 210px;">
                    <input type="text" name="cardYearLimit.bedBookLimit" class="layui-input" lay-verify="required|number" value="#(cardYearLimit.bedBookLimit??)" placeholder="请输入床位预订最大数量">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 145px;">是否可用</label>
                <div class="layui-input-inline" style="width: 210px;">
                    <select id="isEnable" name="cardYearLimit.isEnable" lay-verify="required">
                        <option value="0" #(cardYearLimit != null ?(cardYearLimit.isEnable == '0' ? 'selected':''):'')>可用</option>
                        <option value="1" #(cardYearLimit != null ?(cardYearLimit.isEnable == '1' ? 'selected':''):'')>不可用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 145px;">排序</label>
                <div class="layui-input-inline" style="width: 210px;">
                    <input type="text" name="cardYearLimit.sort" class="layui-input" lay-verify="required|number" value="#(cardYearLimit.sort??)" placeholder="请输入排序">
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/cardYearLimit/saveCardYearLimit";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#cardYearLimitForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.cardYearLimitTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
