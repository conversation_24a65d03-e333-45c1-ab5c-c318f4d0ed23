#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地公共门管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label class="layui-form-label" style="line-height: 40px;width: 140px;padding: 0 20px;">基地</label>
            <div class="layui-input-inline">
                <select id="baseId">
                    <option value="">全部</option>
                    #for(base : baseList)
                    	<option value="#(base.id)">#(base.baseName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:baseDoor:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="doorTable" lay-filter="doorTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'doorTable',
            elem: '#doorTable'
            ,url : '#(ctxPath)/main/baseDoor/pageTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'baseName', title: '基地名称',unresize:true}
                ,{field: 'doorNo', title: '门号',unresize:true}
                ,{field: 'doorName', title: '门名称',unresize:true}
                ,{field: 'lockNo', title: '锁号',unresize:true}
                ,{field: 'lockType', title: '锁类型',unresize:true}
                ,{field: 'remark', title: '描述',unresize:true}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(doorTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/baseDoor/edit?id=" + obj.data.id ;
                layerShow("编辑公共门",url,550,600);
            } else if (obj.event === 'delete') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/main/baseDoor/delete",
                        type:'post',
                        data:{"id":obj.data.id, "delFlag":"1"},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                doorTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });

        doorTableReload=function(){
            table.reload('doorTable',{'where':{'baseId':$("#baseId").val()}});
        }

        $("#search").on('click',function () {
            doorTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/baseDoor/add";
            layerShow("添加公共门",url,550,600);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:baseDoor:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:baseDoor:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">作废</a>
    #end
</script>
#end