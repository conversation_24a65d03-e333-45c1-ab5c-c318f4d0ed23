#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


		var setting = {
			check:{enable:false}
			,view:{selectedMulti:false}
			,data:{simpleData:{enable:true}}
			,async:{enable:true, type:"post", url: '#(ctxPath)/main/csam/assetTypeTree'}
			,callback:{
				onClick: function(event, treeId, treeNode, clickFlag) {

					$("#assetTypeId").val(treeNode.id);
					$("#assetTypeName").val(treeNode.name);

					var data={'fieldName':$("#fieldName").val(),'assetTypeId':treeNode.id};
					goodModelLoad(data);
				},
				onAsyncSuccess: zTreeOnAsyncSuccess
			}
		};
		// 初始化树结构
		var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);

		var firstAsyncSuccessFlag = 0;
		function zTreeOnAsyncSuccess(event, treeId, msg) {

			try {
				//调用默认展开第一个结点
				var selectedNode = zTreeObj.getSelectedNodes();
				var nodes = zTreeObj.getNodes();
				zTreeObj.expandNode(nodes[0], true);
				var childNodes = zTreeObj.transformToArray(nodes[0]);
				zTreeObj.expandNode(childNodes[0], true);
				zTreeObj.selectNode(childNodes[0]);
				var childNodes1 = zTreeObj.transformToArray(childNodes[0]);
				zTreeObj.checkNode(childNodes1[0], true, true);
				firstAsyncSuccessFlag = 1;

				var data={'assetTypeId':nodes[0].id};
				$("#assetTypeId").val(nodes[0].id);
				$("#assetTypeName").val(nodes[0].name);
				goodModelLoad(data);
			} catch (err) {

			}

		}




		sd=form.on("submit(search)",function(data){
			goodModelTableReload();
			return false;
		});

		function goodModelLoad(data){
			table.render({
				id : 'goodModelTable'
				,elem : '#goodModelTable'
				,method : 'POST'
				,where : data
				,limit : 15
				,limits : [15,30,45,50]
				,url : '#(ctxPath)/main/csam/assetTypeFieldPageList'
				,cellMinWidth: 80
				,cols: [[
					{field: 'fieldName', title: '名称',unresize:true}
					,{field:'maxLength', title: '最大长度', align: 'center', unresize: true}
					,{field:'isNulllable', title: '是否可空', align: 'center', unresize: true,templet:"<div>{{ d.isNulllable=='1'?'<span class='layui-badge layui-bg-green'>可空</span>':d.isNulllable=='0'?'<span class='layui-badge'>不可空</span>':'- -' }}</div>"}
					,{field:'isEnabled', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
					,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};

		goodModelTableReload=function(){
			var data={'fieldName':$("#fieldName").val(),'assetTypeId':$("#assetTypeId").val()};
			table.reload('goodModelTable',{'where':data});
		}

		// 添加
		$("#add").click(function(){
			var assetTypeId=$("#assetTypeId").val();
			if(assetTypeId=='' || assetTypeId==null){
				layer.msg('请选择类型', {icon: 2, offset: 'auto'});
				return;
			}
			$(this).blur();
			var url = "#(ctxPath)/main/csam/assetTypeFieldForm?assetTypeId="+assetTypeId+"&assetTypeName="+$("#assetTypeName").val() ;
			pop_show("新增属性",url,600,700);
		});

		table.on('tool(goodModelTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/main/csam/assetTypeFieldForm?id=" + obj.data.id+"&assetTypeId="+$("#assetTypeId").val()+"&assetTypeName="+$("#assetTypeName").val() ;
				pop_show("编辑属性",url,600,700);
			}
		});
	});
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("main:csam:typeField:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
</script>
#end

#define content()
<div>
	<div class="layui-col-xs3 layui-col-sm2 layui-col-md2 layui-col-lg2">
		<fieldset class="layui-elem-field layui-field-title" style="display:block;">
			<legend>类型</legend>
			<div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
		</fieldset>
	</div>
	<div class="layui-col-xs10 layui-col-sm10 layui-col-md10 layui-col-lg10">
		<div class="layui-row">
			<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
				属性名称:
				<div class="layui-inline">
					<input id="fieldName" name="fieldName" class="layui-input">
				</div>
				&nbsp;&nbsp;
				<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
			</form>
			<input type="hidden" id="assetTypeId" value="">
			<input type="hidden" id="assetTypeName" value="">
			#shiroHasPermission("main:csam:typeField:addBtn")
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
			#end
		</div>
		<table class="layui-table" id="goodModelTable" lay-filter="goodModelTable"></table>
	</div>


</div>
#end