#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()选房费#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        bedMarkupTypeTableLoad(null);

        sd=form.on("submit(search)",function(data){
            bedMarkupTypeTableLoad(data.field);
            return false;
        });

        function bedMarkupTypeTableLoad(data){
            table.render({
                id : 'bedMarkupTypeTable'
                ,elem : '#bedMarkupTypeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/main/bedMarkupType/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '名称', align: 'center', unresize: true}
                    ,{field:'amount', title: '选房费', align: 'center', unresize: true}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/bedMarkupType/form" ;
            pop_show("新增类型",url,500,450);
        });

        table.on('tool(bedMarkupTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/bedMarkupType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                table.reload('bedMarkupTypeTable');
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/bedMarkupType/form?id=" + obj.data.id ;
                pop_show("编辑额外价格",url,500,450);
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var bedMarkupTypeTableCheckStatus = table.checkStatus('bedMarkupTypeTable');
            // 获取选择状态下的数据
            return bedMarkupTypeTableCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/bedMarkupType/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {'bedMarkupTypeData':JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            table.reload('bedMarkupTypeTable');
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:bedMarkupType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:bedMarkupType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            状态名称:
            <div class="layui-inline">
                <input id="statusName" name="name" class="layui-input">
            </div>
            <!--&nbsp;&nbsp;
            所属基地:
            <div class="layui-inline">
                <select name="baseId" lay-search>
                    <option value="">请选择所属基地</option>
                    #for(b : baseList)
                    <option value="#(b.id)">#(b.baseName)</option>
                    #end
                </select>
            </div>-->
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:bedMarkupType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        #shiroHasPermission("main:bedMarkupType:batchDelBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
        #end
    </div>
    <table id="bedMarkupTypeTable" lay-filter="bedMarkupTypeTable"></table>
</div>
#end