#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地信息展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
<style>
    .layui-table img {
        max-width: 250px;
        max-height:250px;
    }
    .laydate-time-list{padding-bottom:0;overflow:hidden}
    .laydate-time-list>li{width:50%!important;}
    .laydate-time-list>li:last-child { display: none;}
</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="baseForm">
            <input type="hidden" value="#(base.openDate??)" id="time"/>
            <div class="layui-row">
                <table class="layui-table" lay-skin="nob">
                    <colgroup>
                        <col width="30%">
                        <col width="30%">
                        <col width="30%">
                    </colgroup>
                    <tbody>
                    <tr>
                        <td>
                        	<label class="layui-form-label"><font color="red">*</font>基地类型</label>
							<div class="layui-input-block">
								<select name="base.baseType"lay-verify="required">
									<option value="">无</option>
									#statusOption(com.cszn.integrated.service.entity.status.BaseType::me(), base.baseType??"")
								</select>
							</div>
                        </td>
                        <td>
                        	<label class="layui-form-label"><font color="red">*</font>基地状态</label>
							<div class="layui-input-block">
								<select name="base.baseStatus"lay-verify="required">
									<option value="">无</option>
									#statusOption(com.cszn.integrated.service.entity.status.BaseStat::me(), base.baseStatus??"")
								</select>
							</div>
                        </td>
                        <td rowspan="3" text-align="center" valign="middle" style="padding: 0px;">
                            <div class="layui-upload" style="text-align: center;">
                                <div class="layui-upload-list" style="margin-left: 0px;">
                                    <img class="layui-upload-img" id="profilePhotoImg" src="#(base.baseImage??)">
                                    <p id="profilePhotoText"></p>
<!--                                     <input type="hidden" id="uploadPath" name="base.baseImage" value="#(base.baseImage??)"> -->
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                    	<td>
                            <label class="layui-form-label"><font color="red">*</font>排序</label>
                            <div class="layui-input-block">
                                <input type="text" id="order" name="base.order" value="#(base.order??)" autocomplete="off" placeholder="请输入排序" class="layui-input" lay-verify="myNumber">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><font color="red">*</font>基地名称</label>
                            <div class="layui-input-block">
                                <input type="text" id="baseName" name="base.baseName" value="#(base.baseName??)" autocomplete="off" placeholder="请输入基地名称" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label"><font color="red">*</font>基地编号</label>
                            <div class="layui-input-block">
                                <input type="text" id="baseCode" name="base.baseCode" value="#(base.baseCode??)" autocomplete="off" placeholder="请输入基地编号" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><font color="red">*</font>基地锁编号</label>
                            <div class="layui-input-block">
                                <input type="text" id="lockCode" name="base.lockCode" value="#(base.lockCode??)" autocomplete="off" placeholder="请输入基地锁编号" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                    	<td>
                            <label class="layui-form-label"><font color="red">*</font>开张日期</label>
                            <div class="layui-input-block">
                                <input type="text" id="openDate" name="base.openDate" value="#(base.openDate??)"  placeholder="请选择开张日期" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    	<td>
                            <label class="layui-form-label"><font color="red">*</font>座机</label>
                            <div class="layui-input-block">
                                <input type="text" name="base.tel" value="#(base.tel??)" autocomplete="off" placeholder="请输入座机" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                         <td>
                            <label class="layui-form-label"><font color="red">*</font>坐标</label>
                            <div class="layui-input-block" style="width:170px;">
                                <input type="text" id="coordinate" name="base.coordinate" value="#(base.coordinate??)" autocomplete="off" placeholder="请输入坐标" class="layui-input" lay-verify="required">
                                <i id="mapMarker" class="fa fa-map-marker fa-2x" style="float:left;margin-left:175px;margin-top: -35px;"></i>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label"><font color="red">*</font>建筑面积</label>
                            <div class="layui-input-block">
                                <input type="text" name="base.buildArea" value="#(base.buildArea??)" autocomplete="off" placeholder="请输入建筑面积" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    	<td>
                    		<label class="layui-form-label" style="padding: 8px 5px;"><font color="red"></font>负责人会员卡</label>
                            <div class="layui-input-block">
                                <input type="text" name="base.chargeCardNumber" value="#(base.chargeCardNumber??)" autocomplete="off" placeholder="请输入负责人会员卡" class="layui-input" lay-verify="">
                            </div>
                    	</td>
                    	<td>
                    		<label class="layui-form-label"><font color="red"></font>负责人</label>
                            <div class="layui-input-block">
                            	<select id="chargePeopleId" name="base.chargePeopleId" lay-verify="required" lay-search>
									<option value="">请选择负责人(可搜索)</option>
									#for(user : userList)
									<option value="#(user.id)" #if(user.id==base.chargePeopleId??) selected #end>#(user.name)(#(user.userName))</option>
									#end
								</select>
                            </div>
                    	</td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>会员入住时间</label>
                            <div class="layui-input-block">
                                <input type="text" id="checkinTime" name="base.checkinTime" value="#(base.checkinTime??)" autocomplete="off" placeholder="请输入会员入住时间" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>会员退住时间</label>
                            <div class="layui-input-block">
                                <input type="text" id="checkoutTime" name="base.checkoutTime" value="#(base.checkoutTime??)" autocomplete="off" placeholder="请输入会员退住时间" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>最大预订日期</label>
                            <div class="layui-input-block">
                                <input type="text" id="bookableEndDate" name="base.bookableEndDate" value="#date(base.bookableEndDate??,'yyyy-MM-dd')" autocomplete="off" placeholder="请输入基地最大预订日期" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>散客入住时间</label>
                            <div class="layui-input-block">
                                <input type="text" id="outsiderCheckinTime" name="base.outsiderCheckinTime" value="#(base.outsiderCheckinTime??)" autocomplete="off" placeholder="请输入散客入住时间" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>散客退住时间</label>
                            <div class="layui-input-block">
                                <input type="text" id="outsiderCheckoutTime" name="base.outsiderCheckoutTime" value="#(base.outsiderCheckoutTime??)" autocomplete="off" placeholder="请输入散客退住时间" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>散客开始时间</label>
                            <div class="layui-input-block">
                                <input type="text" id="outsiderStartTime" name="base.outsiderStartTime" value="#(base.outsiderStartTime??)" autocomplete="off" placeholder="请输入散客开始时间" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label"><font color="red">*</font>是否可预订</label>
                            <div class="layui-input-block">
                                <select id="isBooking" name="base.isBooking" lay-verify="required">
                                    <option value="">请选择可预订状态</option>
                                    <option value="0" #(base != null ?(base.isBooking == '0' ? 'selected':''):'')>可预订</option>
                                    <option value="1" #(base != null ?(base.isBooking == '1' ? 'selected':''):'')>不可预订</option>
                                </select>
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><font color="red">*</font>是否可用</label>
                            <div class="layui-input-block">
                                <select id="isEnable" name="base.isEnable" lay-verify="required">
                                    <option value="">请选择可用状态</option>
                                    <option value="0" #(base != null ?(base.isEnable == '0' ? 'selected':''):'')>可用</option>
                                    <option value="1" #(base != null ?(base.isEnable == '1' ? 'selected':''):'')>不可用</option>
                                </select>
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>财务经理</label>
                            <div class="layui-input-block">
                                <input type="text" id="auditUserName" style="width: 70%;display: inline-block;" readonly value="#(base.auditUserName??)"  lay-verify="required" autocomplete="off" placeholder="请选择财务经理" class="layui-input">
                                <input type="hidden" id="auditUserId" name="base.auditUserId" value="#(base.auditUserId??)" >
                                <button class="layui-btn layui-btn-sm" style="max-width: 27%;display: inline-block;" type="button"  id="searchAuditUserBtn">选择</button>
                            </div>

                        </td>
                    </tr>
                     <tr>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>基地成本单价</label>
                            <div class="layui-input-block">
                                <input type="text" id="costPrice" name="base.costPrice" value="#(base.costPrice??)" autocomplete="off" placeholder="请输入基地成本单价" class="layui-input" lay-verify="required|number">
                            </div>
                        </td>
                         <td>
                             <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否可线上预订</label>
                             <div class="layui-input-block">
                                 <select id="isOutBookabled" name="base.isOutBookabled" lay-verify="required">
                                     <option value="">请选择是否可线上预订</option>
                                     <option value="1" #(base != null ?(base.isOutBookabled == '1' ? 'selected':''):'')>可预订</option>
                                     <option value="0" #(base != null ?(base.isOutBookabled == '0' ? 'selected':''):'')>不可预订</option>
                                 </select>
                             </div>
                         </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否弹性就餐</label>
                             <div class="layui-input-block">
                                 <select id="isElasticEat" name="base.isElasticEat" lay-verify="required">
                                     <option value="">请选择是否弹性就餐</option>
                                     <option value="1" #(base != null ?(base.isElasticEat == '1' ? 'selected':''):'')>是</option>
                                     <option value="0" #(base != null ?(base.isElasticEat == '0' ? 'selected':''):'')>否</option>
                                 </select>
                             </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>返租提交人</label>
                            <div class="layui-input-block">
                                <select name="base.leaseSubmitUserId" id="leaseSubmitUserId" lay-search>
                                    <option value="">请选择</option>
                                    #for(user : userList)
                                    <option value="#(user.id??)" #if(user.id??==base.leaseSubmitUserId??) selected #end >#(user.name??)(#(user.user_name??))</option>
                                    #end
                                </select>
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>返租审核人</label>
                            <div class="layui-input-block">
                                <select name="base.leaseCheckUserId" id="leaseCheckUserId" lay-search>
                                    <option value="">请选择</option>
                                    #for(user : userList)
                                    <option value="#(user.id??)" #if(user.id??==base.leaseCheckUserId??) selected #end >#(user.name??)(#(user.user_name??))</option>
                                    #end
                                </select>
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>返租审批人</label>
                            <div class="layui-input-block">
                                <select name="base.leaseApproveUserId" id="leaseApproveUserId" lay-search>
                                    <option value="">请选择</option>
                                    #for(user : userList)
                                    <option value="#(user.id??)" #if(user.id??==base.leaseApproveUserId??) selected #end >#(user.name??)(#(user.user_name??))</option>
                                    #end
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;"><font color="red">*</font>基地财务扣卡人员</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <select xm-select="baseFinanceUserIds" xm-select-search	 name="base.baseFinanceUserIds" id="baseFinanceUserIds">
                                    #for(user : userList)
                                    <option value="#(user.id??)" #if(base!=null && base.baseFinanceUserIds!=null && base.baseFinanceUserIds.indexOf(user.id)!=-1) selected #end >#(user.name??)(#(user.user_name??))</option>
                                    #end
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red">*</font>基地锁服务地址</label>
                            <div class="layui-input-block">
                                <input type="text" id="lockService" name="base.lockService" value="#(base.lockService??)" autocomplete="off" placeholder="请输入基地锁服务地址" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;"><font color="red">*</font>身份证阅读器地址</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <input type="text" id="idcardService" name="base.idcardService" value="#(base.idcardService??)" autocomplete="off" placeholder="请输入身份证阅读器地址" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;">睡眠设备服务地址</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <input type="text" id="sleepaceUrl" name="base.sleepaceUrl" value="#(base.sleepaceUrl??)" autocomplete="off" placeholder="请输入睡眠设备服务地址" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;">睡眠设备服务appId</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <input type="text" id="sleepaceAppId" name="base.sleepaceAppId" value="#(base.sleepaceAppId??)" autocomplete="off" placeholder="请输入睡眠设备服务appId" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="5">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 165px;">睡眠设备服务secureKey</label>
                            <div class="layui-input-block" style="margin-left: 165px;">
                                <input type="text" id="sleepaceSecureKey" name="base.sleepaceSecureKey" value="#(base.sleepaceSecureKey??)" autocomplete="off" placeholder="请输入睡眠设备服务secureKey" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;">睡眠监听地址</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <input type="text" id="sleepaceWebsocketUrl" name="base.sleepaceWebsocketUrl" value="#(base.sleepaceWebsocketUrl??)" autocomplete="off" placeholder="请输入睡眠监听地址" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 200px;">睡眠报告产生时间（0-23点）</label>
                            <div class="layui-input-block" style="margin-left: 200px;">
                                <select id="sleepaceReportTime" name="base.sleepaceReportTime">
                                    <option value="请选择"></option>
                                    #for(i = 0; i <= 23; i++)
                                    <option value="#(i)" #if(i==base.sleepaceReportTime??) selected #end>#(i)</option>
                                    #end
                                </select>
                                #if(base!=null)
                                <button class="layui-btn layui-btn-sm" style="max-width: 27%;display: inline-block;float: right;" type="button"  id="resetSleepaceReportTime">重置睡眠报告生成时间</button>
                                #end
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;">长住天数下限</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <input type="text" id="retainMinDays" name="base.retainMinDays" value="#(base.retainMinDays??)" autocomplete="off" placeholder="长住天数下限" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 280px;">长住进入犹豫期临界天数</label>
                            <div class="layui-input-block" style="margin-left: 280px;">
                                <input type="text" id="inHesitateDays" name="base.inHesitateDays" value="#(base.inHesitateDays??)" autocomplete="off" placeholder="长住进入犹豫期临界天数" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 280px;">犹豫期持续天数（小于犹豫期临界天数）</label>
                            <div class="layui-input-block" style="margin-left: 280px;">
                                <input type="text" id="hesitateDurationDays" name="base.hesitateDurationDays" value="#(base.hesitateDurationDays??)" autocomplete="off" placeholder="犹豫期持续天数（小于犹豫期临界天数）" class="layui-input" lay-verify="">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label" style="padding: 8px 5px;width: 140px;">收钱吧收款账号</label>
                            <div class="layui-input-block" style="margin-left: 140px;">
                                <select name="base.posId" id="posId">
                                    <option value="">请选择</option>
                                    #for(pos : posIdList)
                                    <option value="#(pos.id??)" #if(base!=null && base.posId!=null && base.posId==pos.id) selected #end >#(pos.name??)</option>
                                    #end
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label"><font color="red">*</font>预定渠道</label>
                            <div class="layui-input-block" >
                                <select xm-select="bookChannel" name="bookChannel" id="bookChannel">
                                    #for(bookChannel : bookChannelList)
                                    <option value="#(bookChannel.id??)" #if(base==null) selected #elseif(baseChannelIds??!=null && baseChannelIds.indexOf(bookChannel.id)!=-1) selected #end >#(bookChannel.name??)</option>
                                    #end
                                </select>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label"><font color="red">*</font>所在区域</label>
                            <div class="layui-input-block">
                                <input type="hidden" id="province" name="base.provinceId" value="#(base.provinceId??)" />
                                <input type="hidden" id="city" name="base.cityId" value="#(base.cityId??)" />
                                <input type="hidden" id="town" name="base.townId" value="#(base.townId??)">
                                <input type="hidden" id="street" name="base.streetId" value="#(base.streetId??)">
                                <input type="hidden" id="regidentProvinceName" value="#(province??)" />
                                <input type="hidden" id="regidentCityName" value="#(city??)" />
                                <input type="hidden" id="regidentCountyName" value="#(town??)">
                                <input type="hidden" id="regidentStreetName" value="#(street??)">
                                <input type="text" id="regidentAddress" readonly="readonly" lay-verify="required"  name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label"><font color="red">*</font>详细地址</label>
                            <div class="layui-input-block">
                                <input type="text" name="base.address" value="#(base != null ?base.address:'')" autocomplete="off" placeholder="请输入详细地址" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
<!--                     <tr> -->
<!--                         <td colspan="3"> -->
<!--                             <label class="layui-form-label"><span>*</span>基地介绍</label> -->
<!--                             <div class="layui-input-block"> -->
<!--                                 <textarea  name="base.introduction" rows="5" placeholder="请输入基地介绍" class="layui-textarea" lay-verify="required">#(base != null ?base.introduction:'')</textarea> -->
<!--                             </div> -->
<!--                         </td> -->
<!--                     </tr> -->
                    </tbody>
                </table>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
                    <input name="base.id" id="baseId" type="hidden" value="#(base.id??)" />
                    <input type="hidden" id="fileId" name="base.fileId" value="#(base.fileId??)"/>
<!--                     <button type="button" class="layui-btn" id="basePhoto">上传基地图片</button> -->
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"/>
<script src="layui.all.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script id="regidentAreaTpl" type="text/html">
    <div id="regidentArea" style="width:600px;height:300px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
        <div class="tabs clearfix">
            <ul>
                <li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
                <li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
                <li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
                <li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
            </ul>
        </div>
        <div class="con">
            <div class="regidentProvinceAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentCityAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentTownAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentStreetAll">
                <div class="list">

                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    var formSelects=layui.formSelects;
    layui.use(['form', 'laydate', 'upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;

        formSelects.render('bookChannel');

        //时间渲染
        laydate.render({
            elem : '#openDate'
            ,trigger:'click'
        });

        laydate.render({
           elem:'#bookableEndDate'
            ,trigger:'click'
        });

        //会员入住时间
        laydate.render({
            elem: '#checkinTime'
            ,type: 'time'
            ,format: 'HH:mm'
            ,btns: ['clear', 'confirm']
        });

        //会员退住时间
        laydate.render({
            elem: '#checkoutTime'
            ,type: 'time'
            ,format: 'HH:mm'
            ,btns: ['clear', 'confirm']
        });

        //散客入住时间
        laydate.render({
            elem: '#outsiderCheckinTime'
            ,type: 'time'
            ,format: 'HH:mm'
            ,btns: ['clear', 'confirm']
        });

        //散客退住时间
        laydate.render({
            elem: '#outsiderCheckoutTime'
            ,type: 'time'
            ,format: 'HH:mm'
            ,btns: ['clear', 'confirm']
        });

        //散客开始时间
        laydate.render({
            elem: '#outsiderStartTime'
            ,type: 'time'
            ,format: 'HH:mm'
            ,btns: ['clear', 'confirm']
        });

        //修改时间格式
        $(function(){
            if($("#time").val() != null && $("#time").val() != '') {
                var openDate = dateFormat($("#time").val(), 'yyyy-MM-dd');
                $("#openDate").val(openDate);
            }
        });
        
        //选择财务经理按钮
        $("#searchAuditUserBtn").on('click',function () {
            layerShow('选择财务经理','#(ctxPath)/main/base/auditUserTableIndex',800,500);
        });

        //重置睡眠设备报告产生时间
        $("#resetSleepaceReportTime").on('click',function () {
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/base/resetSleepaceReportTime',
                data: {"baseId":$("#baseId").val()},
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //添加到main页面上基地列表
                        if(rep.data!=null){
                            parent.parent.addBase(rep.data.id,rep.data.baseName);
                        }
                        pop_close();
                        parent.tableReload("baseTable",null);
                    }
                },
                complete : function() {
                }
            });

        })

        //校验
        form.verify({
            myNumber:function(value){
                if(value != null && value.length >0){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            }
        });

        $("#mapMarker").click(function(){
            var url = "#(ctxPath)/main/base/map" ;
            pop_show("选择基地坐标",url,800,600);
        });

        getCoordinate = function(lng,lat){
            $("#coordinate").val( lat + ","+lng);
        };

        //保存
        form.on('submit(confirmBtn)', function(){
            var url = "#(ctxPath)/main/base/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#baseForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //添加到main页面上基地列表
                        if(rep.data!=null){
                            parent.parent.addBase(rep.data.id,rep.data.baseName);
                        }
                        pop_close();
                        parent.tableReload("baseTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        //普通图片上传
        var uploadInst = upload.render({
            elem: '#basePhoto'
            ,url:$("#commonUpload").val() +'/upload?bucket=base'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#profilePhotoImg').attr('src', result);
                });
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){
                    $("#uploadPath").val(res.data.src);
                    $("#fileId").val(res.data.id);
                    layer.msg(res.msg,{icon:1,time:5000});
                }else {
                    return layer.msg('上传失败');
                }

            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });


        //--------------------------居住区域begin---------------------------
        $('#regidentAddress').on('click', function() {
            //closeIdArea();

            $('#regidentArea').remove();
            var $this = $(this);
            var getTpl = regidentAreaTpl.innerHTML;
            $this.parent().append(getTpl);
            //event.stopPropagation();

            var street=$("#street").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var town=$("#town").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var city=$("#city").val();
            var regidentCityName=$("#regidentCityName").val();
            var province=$("#province").val();
            var regidentProvinceName=$("#regidentProvinceName").val();
            if(street!='' && regidentStreetName!=''){
                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentStreetLoad(town);
                regidentCountyLoad(city);
                regidentCityLoad(province);
                regidentProvinceLoad();
                $(".con .regidentStreetAll").show().siblings().hide();
                //clickStreet(streetId,streetName);
            }else if(town!='' && regidentCountyName!=''){

                if(town!=''){
                    regidentCityLoad(province);
                    regidentCountyLoad(city);
                    regidentProvinceLoad();
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/area/getAreas',
                        data: {pid:town},
                        notice:false,
                        loadFlag: false,
                        success : function(res){
                            if(res.state=='ok'){
                                if(res.data.length>0){
                                    $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    var html="<ul>";
                                    $.each(res.data, function(i, item){
                                        html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                    });
                                    html+="</ul>";
                                    $(".regidentStreetAll .list").append(html);
                                    //viewStreet(countyId,countyName);
                                    $(".con .regidentStreetAll").show().siblings().hide();
                                }else{
                                    //无 街道信息
                                    $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    $(".con .regidentTownAll").show().siblings().hide();
                                }
                            }
                        },
                        complete : function() {
                        }
                    });
                }
            }else if(city!='' && regidentCityName!=''){
                regidentProvinceLoad();
                regidentCityLoad(province);
                viewRegidentCounty(city,regidentCityName);
            }else if(province!='' && regidentProvinceName!=''){
                regidentProvinceLoad();
                viewRegidentCity(province,regidentProvinceName);
            }else{
                regidentProvinceLoad();
            }

            //去除事件冒泡
            var evt =  new Object;
            if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                evt = arguments.callee.caller.arguments[0];
            }else{
                evt = event || window.event;
            }
            evt.cancelBubble = true;
            $('#regidentArea').off('click');
            $('#regidentArea').on('click', function() {
                //event.stopPropagation();
                //去除事件冒泡
                var evt =  new Object;
                if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                    evt = arguments.callee.caller.arguments[0];
                }else{
                    evt = event || window.event;
                }
                evt.cancelBubble = true;
            })
        });

        regidentProvinceLoad=function(){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/area/getAreas',
                data: {pid:''},
                notice:false,
                loadFlag: false,
                success : function(res){
                    if(res.state=='ok'){
                        if(res.data.length>0){
                            $(".regidentProvinceAll .list").empty();
                            var html="<ul>";
                            $.each(res.data, function(i, item){
                                html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCity(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                            });
                            html+="</ul>";
                            $(".regidentProvinceAll .list").append(html);
                        }
                    }
                },
                complete : function() {
                }
            });
        };

        //点击省事件
        viewRegidentCity=function(province,regidentProvinceName) {
            $("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentCityAll").show().siblings().hide();
            $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            //点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
            $("#province").val(province);
            $("#regidentProvinceName").val(regidentProvinceName);
            $("#city").val("");
            $("#regidentCityName").val("");
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCityLoad(province);
        };

        //加载市
        regidentCityLoad=function(province){
            if(province!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:province},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentCityAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCounty(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentCityAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击市事件
        viewRegidentCounty=function(city,RegidentCityName){
            $("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentTownAll").show().siblings().hide();
            $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#city").val(city);
            $("#regidentCityName").val(RegidentCityName);
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCountyLoad(city);
        };

        //加载区/县
        regidentCountyLoad=function(city){
            if(city!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:city},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentTownAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentTownAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击区/县事件
        viewRegidentStreet=function(town,regidentCountyName){
            $("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentStreetAll").show().siblings().hide();
            $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#town").val(town);
            $("#regidentCountyName").val(regidentCountyName);
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentStreetLoad(town);
        };

        //加载街道/镇/乡
        regidentStreetLoad=function(town){
            if(town!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:town},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentStreetAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentStreetAll .list").append(html);
                            }else{
                                //无 街道信息
                                clickRegidentStreet('','');
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        clickRegidentStreet=function(street,regidentStreetName){
            $("#street").val(street);
            $("#regidentStreetName").val(regidentStreetName);
            var regidentProvinceName=$("#regidentProvinceName").val();
            var regidentCityName=$("#regidentCityName").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
            $("#regidentAddress").val(add);
            $('#regidentArea').remove();
        };

        regidentProvinceAllClick=function(){
            //$(".con .provinceAll").show();
            $("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentProvinceAll").show().siblings().hide();
        };

        regidentCityAllClick=function(){
            // $(".con .cityAll").show();
            if($("#province").val()!=''){
                $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCityLoad($("#province").val());
                $(".con .regidentCityAll").show().siblings().hide();
            }
        };

        regidentTownAllClick=function(){
            if($("#city").val()!=''){
                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCountyLoad($("#city").val());
                $(".con .regidentTownAll").show().siblings().hide();
            }
        };

        regidentStreetAllClick=function(){
            if($("#town").val()!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:$("#town").val()},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                regidentStreetLoad($("#town").val());
                                $(".con .regidentStreetAll").show().siblings().hide();
                            }else{
                                //无 街道信息 显示区/县信息
                                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                //countyLoad(cityId);
                                $(".con .regidentTownAll").show().siblings().hide();
                            }
                        }
                    },
                    complete : function() {
                    }
                });



            }
        };


        $('body').on('click', function() {
            closeRegidentArea();
        });

        //关闭区域选择器
        closeRegidentArea=function(){
            if(typeof($('#regidentArea').html())!='undefined'){
                var regidentProvinceName=$("#regidentProvinceName").val();
                var regidentCityName=$("#regidentCityName").val();
                var regidentCountyName=$("#regidentCountyName").val();
                var regidentStreetName=$("#regidentStreetName").val();
                var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
                $("#regidentAddress").val(add);
            }
            //alert(1);
            $('#regidentArea').remove();
            //console.log($('#regidentArea').html());
        }

        //-------------------------居住区域end----------------------------

    });
</script>
#end