#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()赠送方案编辑页面#end

#define css()

<style>
	.layui-table td, .layui-table th {
		position: relative;
		padding: 9px 3px;
		min-height: 20px;
		line-height: 20px;
		font-size: 14px;
	}
</style>

#end

#define js()
<script id="ruleTrTpl" type="text/html">
<tr id="rule-{{d.idx}}">
	<td>
		<div class="layui-input-inline">
			<select name="ruleList[{{d.idx}}].cycle" lay-verify="required" lay-filter="">
				<option value="">请选择周期</option>
				#dictOption("give_cycle", '', '')
			</select>
		</div>
	</td>
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].startSeq" class="layui-input" lay-verify="required|number" value="" placeholder="开始序">
		</div>
	</td>
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].endSeq" class="layui-input" lay-verify="required|number" value="" placeholder="结束序">
		</div>
	</td>
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].giveTimes" class="layui-input" lay-verify="required|number" value="0" placeholder="赠送天数">
		</div>
	</td>
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].giveAmount" class="layui-input" lay-verify="required|number" value="0" placeholder="赠送金额">
		</div>
	</td>
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].describe" class="layui-input" value="" placeholder="请输入备注">
		</div>
	</td>
	<td>
		<input type="hidden" name="ruleList[{{d.idx}}].id" value="">
		<input type="hidden" name="ruleList[{{d.idx}}].schemeId" value="{{d.schemeId}}">
		<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delRule('rule-{{d.idx}}','')">作废</a>
	</td>
</tr>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laytpl' ], function() {
	var form = layui.form
	, laytpl = layui.laytpl
	, $ = layui.jquery
	;
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		var schemeId = '#(model.Id??)';
		$('#ruleCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx": (parseInt(idx)+1), "schemeId": schemeId}, function(html){
			targetId.append(html);
		});
		form.render('select');
    };
    
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		addTpl($('#ruleList'), ruleTrTpl.innerHTML, $('#ruleCount').val());
	});
	
	//删除方法
    delRule = function(ruleTrId, ruleId) {
   		if(ruleId!=null && ruleId!=''){
			layer.confirm('您是否要删除当前规则？', {icon: 3, title:'询问'}, function(index){
				//删除操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/main/cardGiveScheme/delRule',
		            data: {id:ruleId, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	$("#"+ruleTrId).remove();
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
   		}else{
	    	$("#"+ruleTrId).remove();
   		}
    };
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/cardGiveScheme/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<!--<div class="layui-form-item" style="margin-top: 10px;">
		<label class="layui-form-label"><font color="red">*</font>方案编号</label>
		<div class="layui-input-block">
			<input type="text" name="schemeNo" class="layui-input" lay-verify="required" value="#(model.schemeNo??)" maxlength="50" placeholder="请输入方案编号" #if(model.schemeNo!=null)readonly="readonly"#end>
		</div>
	</div>-->

	<div class="layui-form-item" style="margin-top: 10px;">
		<label class="layui-form-label"><font color="red">*</font>方案名称</label>
		<div class="layui-input-block"  >
			<input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" maxlength="50" placeholder="请输入方案名称">
		</div>
	</div>

	<div class="layui-form-item" style="margin-top: 10px;">
		<label class="layui-form-label"><font color="red">*</font>方案类型</label>
		<div class="layui-input-block"  >
			<select name="type" id="type" lay-verify="required">
				<option value="">请选择方案类型</option>
				<option value="1" #if(model.type??=='1') selected #end >赠送</option>
				<option value="2" #if(model.type??=='2') selected #end >充值</option>
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea" rows="4" placeholder="请输入备注">#(model.remark??)</textarea>
		</div>
	</div>
	<div class="layui-row" style="">
		<table class="layui-table">
			<colgroup>
				<col width="14%">
				<col width="10%">
				<col width="10%">
				<col width="13%">
				<col width="13%">
				<col width="30%">
				<col width="10%">
			</colgroup>
			<thead>
				<tr>
					<th>周期</th>
					<th>开始序</th>
					<th>结束序(也赠送)</th>
					<th>赠送天数</th>
					<th>赠送金额</th>
					<th>备注</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="ruleList">
				#for(rule : model.ruleList??)
					<tr id="rule-#(for.index+1)">
						<td>
							<div class="layui-input-inline">
								<select name="ruleList[#(for.index+1)].cycle" lay-verify="required" lay-filter="">
									<option value="">请选择周期</option>
									#dictOption("give_cycle", rule.cycle??'', "")
								</select>
							</div>
						</td>
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].startSeq" class="layui-input" lay-verify="required|number" value="#(rule.start_seq??)" placeholder="开始序">
							</div>
						</td>
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].endSeq" class="layui-input" lay-verify="required|number" value="#(rule.end_seq??)" placeholder="结束序">
							</div>
						</td>
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].giveTimes" class="layui-input" lay-verify="required|number" value="#(rule.give_times??)" placeholder="赠送天数">
							</div>
						</td>
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].giveAmount" class="layui-input" lay-verify="required|number" value="#(rule.give_amount??)" placeholder="赠送金额">
							</div>
						</td>
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].describe" class="layui-input" value="#(rule.describe??)" placeholder="请输入备注">
							</div>
						</td>
						<td>
							<input type="hidden" name="ruleList[#(for.index+1)].id" value="#(rule.id??)">
							<input type="hidden" name="ruleList[#(for.index+1)].schemeId" value="#(rule.scheme_id??)">
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delRule('rule-#(for.index+1)','#(rule.id??)')">作废</a>
						</td>
					</tr>
				#end
			</tbody>
		</table>
	</div>
	<div style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<input type="hidden" id="ruleCount" name="ruleCount" value="#(model.ruleList.size()??0)">
			<button type="button" id="addBtn" class="layui-btn">添加规则</button>
			<button type="button" class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end