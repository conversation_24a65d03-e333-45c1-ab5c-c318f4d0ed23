#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地餐厅设置页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div id="restaurantTab" class="layui-tab layui-tab-brief" lay-filter="restaurantTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<div class="layui-row">
					<form id="frm" class="layui-form" lay-filter="layform" action="" method="post" style="margin-top:15px;">
					<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
						<div class="layui-inline">
							<label class="layui-form-label">餐厅名称</label>
							<div class="layui-input-inline">
								<input id="restaurantName" name="restaurantName" class="layui-input">
							</div>
						</div>
						<div class="layui-inline">
							<input type="hidden" id="baseId" name="baseId" value="#(baseId??)">
							<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
							<a type="button" id="addBtn" class="layui-btn btn-default">添加餐厅</a>
						</div>
					</div>
					</form>
					<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
						<div class="layui-inline">
							<input type="hidden" id="restaurantId" value="">
							<a type="button" id="addTableBtn" class="layui-btn btn-default">添加餐桌</a>
							<a type="button" id="printBtn" class="layui-btn">打印二维码</a>
						</div>
					</div>
				</div>
				<div class="layui-row layui-col-space10">
					<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
						<fieldset class="layui-elem-field">
							<legend>餐厅列表</legend>
							<table id="restaurantTable" lay-filter="restaurantTable"></table>
						</fieldset>
					</div>
					<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
						<fieldset class="layui-elem-field">
							<legend>餐桌列表</legend>
							<table id="restaurantTableTable" lay-filter="restaurantTableTable"></table>
						</fieldset>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="restaurantBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:delBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</div>
</script>
<script type="text/html" id="restaurantTableBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:recordDelBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</div>
</script>
<script type="text/javascript">
layui.use([ 'form', 'element','table' ], function() {
	var form = layui.form
	, table = layui.table
	, element = layui.element
	, $ = layui.jquery
	;
	
	function restaurantLoad(data){
		table.render({
			id : 'restaurantTable'
			,elem : '#restaurantTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/main/base/restaurantPage'
			,cellMinWidth: 80
			,width:($(document).width()/2)-5
			,height:$(document).height()*0.8
			,cols: [[
				{type: 'numbers', title: '序号', width: 60, unresize:true}
				,{field:'restaurantName', title: '名称', align: 'center', unresize: true}
				,{field: '', title: '是否有效', width: 120,unresize:true,templet:"<div>{{d.isEnabled == '0'? '<span class='layui-badge'>无效</span>':d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>有效</span>':'- -'}}</div>"}
				,{fixed:'right', title: '操作', width:120, align: 'center', unresize: true, toolbar: '#restaurantBar'}
			]]
			,page : true
			,limit : 15
			,limits : [15,25,35,45]
			, loading: true
		    , done: function (res, curr, count) {
				// 单击行触发回调函数
				clickRow(this , table , function(data) {
					$('#restaurantId').val(data.id);
					restaurantTableLoad(data.id);
				});
		    }
		});
		table.on('tool(restaurantTable)',function(obj){
			if(obj.event==="edit"){//编辑按钮事件
	        	element.tabAdd('restaurantTab', {
	    			title: '编辑餐厅'
	    			, content: '<div id="restaurantForm"></div>'
	    			, id: 'form'
	    		});
	    		$('#restaurantForm').load('#(ctxPath)/main/base/restaurantForm?id='+obj.data.id);
	    		element.tabChange('restaurantTab', 'form');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm("确定要作废吗?",function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/main/base/delBaseRestaurant',
	                    notice: true,
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                        	tableReload("restaurantTable",{baseId:$('#baseId').val(), restaurantName:$('#restaurantName').val()});
	                        }
	                        layer.close(index);
	                    },
	                    complete : function() {
	                    }
	                });
	            });
	        }
		});
	};
	
	function restaurantTableLoad(restaurantId){
		table.render({
		    id: 'restaurantTableTable'
		    , elem: '#restaurantTableTable'                  //指定原始表格元素选择器（推荐id选择器）
// 		    , even: true //开启隔行背景
		    , url: '#(ctxPath)/main/base/restaurantTablePage'
		    , method: 'post'
		    , where: {restaurantId:restaurantId}
			, cellMinWidth: 80
			, width:($(document).width()/2)-5
			, height:$(document).height()*0.8
		    , cols: [[                  //标题栏
		    	{type:'checkbox'}
		        , {field: 'tableNo', title: '桌号', width: 60, unresize:true}
				, {field: 'tableName', title: '桌名称', unresize:true}
				, {field: 'peopleNum', title: '最多人数', unresize:true}
				, {field: '', title: '是否可预订', width: 100,unresize:true,templet:"<div>{{d.isBooking == '0'? '<span class='layui-badge'>否</span>':d.isBooking == '1'? '<span class='layui-badge layui-bg-green'>是</span>':'- -'}}</div>"}
				, {field: '', title: '是否有效', width: 100,unresize:true,templet:"<div>{{d.isEnabled == '0'? '<span class='layui-badge'>无效</span>':d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>有效</span>':'- -'}}</div>"}
				, {field: '', title: '状态', width: 100,unresize:true, templet: '#dictTpl("table_status", "tableStatus")'}
		        , {fixed: 'right', title: '操作', width: 170, align: 'center', toolbar: '#restaurantTableBar'} //这里的toolbar值是模板元素的选择器
		    ]]
		    , page: true
		    , limit : 15
			, limits : [15,25,35,45]
		    , loading: true
		    , done: function (res, curr, count) {
		    }
		});
		// 表格绑定事件
	    table.on('tool(restaurantTableTable)',function (obj) {
	    	if(obj.event==="edit"){//编辑按钮事件
	        	element.tabAdd('restaurantTab', {
	    			title: '编辑餐桌'
	    			, content: '<div id="restaurantTabeForm"></div>'
	    			, id: 'form'
	    		});
	    		$('#restaurantTabeForm').load('#(ctxPath)/main/base/restaurantTableForm?id='+obj.data.id);
	    		element.tabChange('restaurantTab', 'form');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	        	layer.confirm("确定要作废吗?",function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/main/base/delBaseRestaurantTable',
	                    notice: true,
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                        	tableReload("restaurantTableTable",{restaurantId:$('#restaurantId').val()});
	                        }
	                        layer.close(index);
	                    },
	                    complete : function() {
	                    }
	                });
	            });
	        }
// 	    	else if(obj.event==='createQrcode'){
// 				util.sendAjax ({
// 					type: 'POST',
// 					url: '#(ctxPath)/main/base/createTableQrcode?id='+obj.data.id,
// 					loadFlag: true,
// 					success : function(rep){
// 						if(rep.state==='ok'){
// 							downloadFileByBase64(rep.imgName,obj.data.tableName+"_二维码");
// 						}
// 					},
// 					complete : function() {
// 					}
// 				});

// 	        }
	    });
	};

	function dataURLtoBlob(dataurl) {
		var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
				bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}
		return new Blob([u8arr], { type: mime });
	}

	function downloadFile(url,name='What\'s the fuvk'){
		var a = document.createElement("a")
		a.setAttribute("href",url)
		a.setAttribute("download",name)
		a.setAttribute("target","_blank")
		let clickEvent = document.createEvent("MouseEvents");
		clickEvent.initEvent("click", true, true);
		a.dispatchEvent(clickEvent);
	}

	function downloadFileByBase64(base64,name){
		var myBlob = dataURLtoBlob(base64)
		var myUrl = URL.createObjectURL(myBlob)
		downloadFile(myUrl,name)
	}
	
	restaurantLoad({baseId:'#(baseId??)'});
	
	form.on("submit(search)",function(data){
		restaurantLoad(data.field);
		return false;
	});
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('restaurantTab', {
			title: '添加餐厅'
			, content: '<div id="restaurantForm"></div>'
			, id: 'form'
		});
		$('#restaurantForm').load('#(ctxPath)/main/base/restaurantForm?baseId=#(baseId??)');
		element.tabChange('restaurantTab', 'form');
    });
	
    // 添加
    $("#addTableBtn").click(function(){
		var restaurantId = $('#restaurantId').val();
		if(restaurantId!=null && restaurantId!=''){
			element.tabAdd('restaurantTab', {
				title: '添加餐桌'
				, content: '<div id="restaurantTableForm"></div>'
				, id: 'form'
			});
			$('#restaurantTableForm').load('#(ctxPath)/main/base/restaurantTableAdd?restaurantId='+restaurantId);
			element.tabChange('restaurantTab', 'form');
		}else{
			layer.msg('请选择餐厅!',{icon:5});
		}
    });
    
    print=function(templateName,data){
//     	console.log('templateName==='+templateName);
//     	console.log('data==='+data);
        var param={"TemplateName":templateName,"PrinterName":"","Data":data};
        layer.load();
        $.ajax({
            url: "http://127.0.0.1:8050/LabelPrint?Type=LabelPrint",
            type: "POST",
            dataType: "json",
            data: JSON.stringify(param),
            contentType: 'application/json',
            success: function (result) {
                layer.closeAll('loading');
            },
            error: function (e) {
                layer.closeAll('loading');
                layer.msg( e.responseText == null ? "未启动打印服务" : e.responseText, {icon: 2, offset: 'auto'});
            }
        });
    }
    
    //批量获取列表数据
    getCheckTableData = function(){
        var tableCheckStatus = table.checkStatus('restaurantTableTable');
        // 获取选择状态下的数据
        return tableCheckStatus.data;
    }
	
    // 打印
    $("#printBtn").click(function(){
    	var selectData = getCheckTableData();
    	if(selectData!=null && selectData!=''){
// 			console.log('selectData==='+JSON.stringify(selectData));
			util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/base/printTableQrcode',
                data: {selectDatas:JSON.stringify(selectData)},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	if(rep.templateName!=null && rep.templateName!=''){
                			if(rep.printData!=null && rep.printData!=''){
	                    		print(rep.templateName, rep.printData);
                    		}else{
                    			layer.msg('打印参数不能为空!',{icon:5});
                    		}
                		}else{
                			layer.msg('模板名称不能为空!',{icon:5});
                		}
                    }
                },
                complete : function() {
                }
            });
		}else{
			layer.msg('请勾选数据', function () {});
			return;
		}
    });
    
	element.on('tab(restaurantTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
			var liLength = $(".layui-tab-title li").length;
// 			console.log('liLength==='+liLength);
			if(liLength>1){
				//删除指定Tab项
				element.tabDelete('restaurantTab', 'form');
// 				layer.confirm("您有数据未保存,是否确定放弃保存?",function(index){
// 					layer.close(index);
// 	            });
			}
		}
	});
});
</script>
#end