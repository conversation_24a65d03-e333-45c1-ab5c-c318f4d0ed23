#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡年限类型#end

#define css()
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
        </form>
        #shiroHasPermission("main:cardYearLimit:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="cardYearLimitTable" lay-filter="cardYearLimitTable"></table>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
    #shiroHasPermission("main:cardYearLimit:configBtn")
    <a class="layui-btn layui-btn-xs" lay-event="config">配置</a>
    #end
    #shiroHasPermission("main:cardYearLimit:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:cardYearLimit:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</div>
</script>
<script>
layui.use(['form','layer','table'], function() {
    var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

    table.render({
        id : 'cardYearLimitTable'
        ,elem : '#cardYearLimitTable'
        ,method : 'POST'
        ,height:$(document).height()*0.85
        ,limit : 15
        ,limits : [15,30,45,50]
        ,url : '#(ctxPath)/main/cardYearLimit/findPageList'
        ,cellMinWidth: 80
        ,cols: [[
            {type: 'numbers', width:100, title: '序号',unresize:true}
            ,{field:'name', title: '年限名称', align: 'center', unresize: true}
            ,{field:'dictValue', title: '字典值', align: 'center', unresize: true}
            ,{field:'bedBookLimit', title: '床位预订数量限制', align: 'center', unresize: true}
            ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
            ,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true, toolbar: '#actionBar'}
        ]]
        ,page : true
    });

    table.on('tool(cardYearLimitTable)',function(obj){
    	if(obj.event === 'config'){
            var url = "#(ctxPath)/main/cardYearLimit/configIndex?id=" + obj.data.id ;
            pop_show("配置",url,'', '');
        }else if(obj.event === 'edit'){
            var url = "#(ctxPath)/main/cardYearLimit/form?id=" + obj.data.id ;
            pop_show("编辑",url,500,500);
        }else if (obj.event === 'del') {
            layer.confirm("确定要作废吗?",function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/cardYearLimit/delCardYearLimit',
                    notice: true,
                    data: {id:obj.data.id},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            cardYearLimitTableReload();
                        }
                        layer.close(index);
                    },
                    complete : function() {
                    }
                });
            });
        }
    });

    cardYearLimitTableReload=function(){
        table.reload('cardYearLimitTable',{"where":{"name":$("#name").val()}});
    }

    // 搜索
    $("#search").click(function(){
        cardYearLimitTableReload();
    });

    // 添加
    $("#add").click(function(){
        $(this).blur();
        var url = "#(ctxPath)/main/cardYearLimit/form" ;
        pop_show("新增预订渠道",url,500,500);
    });
});
</script>
#end
