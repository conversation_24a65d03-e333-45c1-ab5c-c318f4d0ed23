#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#end

#define css()
#end

#define content()
<div class="layui-row">
    #for(syncType : syncTypeList)
    <div class="layui-row" style="padding: 20px 10px 0px 10px;">
        <div class="layui-row" style="height: 40px;line-height: 40px;border: 1px solid #c9c9c9;padding-left: 20px;border-bottom-color: #ffffff;">
            #(syncType.value??)数据
        </div>
        <div class="layui-row " style="border: 1px solid #c9c9c9;padding: 10px 0px 0px 20px;">
            <form class="layui-form">
                <div class="layui-form-item">
                    <input class="layui-form-radio" name="operationType" lay-filter="operationType" id="allRadio-#(for.index)"  value="all" type="radio" title="全部数据" checked>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-inline" style="width: 120px;">
                        <input class="layui-form-radio" name="operationType" lay-filter="operationType" id="updateDateRadio-#(for.index)"  value="updateDate" type="radio" title="按更新时间">
                    </div>
                    <div class="layui-input-inline">
                        <input class="layui-input startDate" name="startDate" id="startDate-#(for.index)" autocomplete="off">
                    </div>
                    <label style="float: left;height: 38px;line-height: 38px;margin-right: 10px;">至</label>
                    <div class="layui-input-inline">
                        <input class="layui-input endDate" name="endDate" id="endDate-#(for.index)" autocomplete="off">
                    </div>
                </div>
                <div class="layui-form-item">
                    <input type="hidden" name="syncType" value="#(syncType.key??)">
                    #shiroHasPermission("main:bacthSync:startSyncBtn")
                    <button class="layui-btn" lay-submit name="sync" lay-filter="sync" >开始同步</button>
                    #end
                    <!--<button class="layui-btn" lay-submit name="del" lay-filter="del" onclick="removeRequired()">作废所有同步记录</button>-->
                </div>
            </form>
        </div>
    </div>
    #end

</div>
#end

#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        for(var i=0;i<#(syncTypeList.size()??0);i++){
            laydate.render({
                elem:"#startDate-"+i,
                type: 'datetime',
                trigger:"click"
            });

            laydate.render({
                elem:"#endDate-"+i,
                type: 'datetime',
                trigger:"click"
            });
        }



        removeRequired=function(){
            $("#startDate").removeAttr("lay-verify");
            $("#endDate").removeAttr("lay-verify");
        }

        form.on('radio(operationType)',function (obj) {
            var id=obj.elem.id;
            var index=id.substr(id.indexOf("-")+1);
            if(obj.value==='updateDate'){
                $("#startDate-"+index).attr("lay-verify","required");
                $("#endDate-"+index).attr("lay-verify","required");
            }else{
                $("#startDate-"+index).removeAttr("lay-verify");
                $("#endDate-"+index).removeAttr("lay-verify");
            }
        })

        //点击开始同步按钮
        form.on('submit(sync)',function (obj) {
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/syncRecord/startSync',
                notice: true,
                data: obj.field,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){

                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        //作废全部同步记录按钮
        //form.on('submit(del)',function (obj) {
            // util.sendAjax ({
            //     type: 'POST',
            //     url: '#(ctxPath)/main/syncRecord/delAllSyncRecord',
            //     notice: true,
            //     data: obj.field,
            //     loadFlag: true,
            //     success : function(rep){
            //         if(rep.state=='ok'){
            //
            //         }
            //     },
            //     complete : function() {
            //     }
            // });
           // return false;
        //});

    });
</script>
#end