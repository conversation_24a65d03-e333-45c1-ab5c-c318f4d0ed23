#include("/template/common/layout/_page_layout.html")

#@layout()

#define pageTitle()公共门编辑#end

#define css()

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<div class="layui-form-item">
			<table class="layui-table" lay-size="sm">
				<thead>
					<tr>
						<th width="30%">设备名称</th>
						<th width="50%">备注</th>
						<th width="20%">操作</th>
					</tr>
				</thead>
				<tbody id="roomDoorList">
					#for(bedEqui : bedEquiModelList)
					<tr id="roomDoor-#(for.index+1)">
						<td align="left">
							<select id="doorSelect-#(for.index+1)" name="roomDoorList[#(for.index+1)].equiModelId" lay-verify="required" lay-search="">
								<option value="">直接选择或搜索选择</option>
								#for(model : bedEqui.equiModelList)
									<option value="#(model.id)" #if(model.id==bedEqui.equiModelId)selected="selected"#end>#(model.modelName)(#(model.infoId))</option>
								#end
							</select>
						</td>
						<td>
							<input name="roomDoorList[#(for.index+1)].remark" value="#(bedEqui.remark??)" required lay-verify="" placeholder="请输入备注" autocomplete="off" class="layui-input">
						</td>
						<td>
							<input type="hidden" name="roomDoorList[#(for.index+1)].id" value="#(bedEqui.id??)">
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('roomDoor-#(for.index+1)','#(bedEqui.id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="doorCount" name="doorCount" value="#(bedEquiModelList.size()??)">
				<input type="hidden" name="bedId" value="#(bedId)">
				<input type="hidden" name="buildingId" value="#(buildingId)">
				<input type="hidden" name="floorId" value="#(floorId)">
				<input type="hidden" name="roomId" value="#(roomId)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<div id="addDoorBtn" class="layui-btn">添加设备</div>
				<button id="saveBtn" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script id="roomDoorTrTpl" type="text/html">
	<tr id="roomDoor-{{d.idx}}">
		<td>
			<select id="doorSelect-{{d.idx}}" name="roomDoorList[{{d.idx}}].equiModelId" lay-verify="required" lay-search="">
				<option value="">直接选择或搜索选择</option>
				#for(model : modelList)
					<option value="#(model.id)" >#(model.modelName)(#(model.infoId))</option>
				#end
			</select>
		</td>
		<td>
			<input name="roomDoorList[{{d.idx}}].remark" value="" required lay-verify="" placeholder="请输入备注" autocomplete="off" class="layui-input">
		</td>
		<td>
			<input type="hidden" name="roomDoorList[{{d.idx}}].id" value="">
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('roomDoor-{{d.idx}}','')">作废</a>
		</td>
	</tr>
</script>
<script>
layui.use(['form','layer','laytpl'], function() {
	var $ = layui.$, form=layui.form,layer=layui.layer, laytpl = layui.laytpl;

	//添加模板方法
	addTpl = function(targetId, addTpl, idx, roomId) {
		$('#doorCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1), "roomId":roomId}, function(html){
			targetId.append(html);
		});

		form.render('select');
    };
    
	//删除方法
	del = function(trId, dataId) {
		if(dataId!=null && dataId!=''){
			layer.confirm('您确定要作废？', {icon: 3, title:'询问'}, function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/buildingRoomManage/delBedEqui',
					data: {id:dataId},
					notice: true,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							$("#"+trId).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else{
			$("#"+trId).remove();
		}
	};
    
	form.on('submit(saveBtn)',function (obj) {




		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/buildingRoomManage/saveBedEqui',
			data: $(obj.form).serialize(),
			notice: true,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					pop_close();
				}
			},
			complete : function() {
			}
		});
	
		return false;
	});

	//添加按钮点击事件
	$('#addDoorBtn').on('click', function() {
		addTpl($('#roomDoorList'), roomDoorTrTpl.innerHTML, $('#doorCount').val(), '#(roomId??)');
	});
});
</script>
#end