#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户分组管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
            <div class="layui-inline">
                <label class="layui-form-label">分组名称:</label>
                <div class="layui-input-inline">
                    <input id="groupName" name="groupName" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">是否可用:</label>
                <div class="layui-input-inline">
                    <select name="isEnable" id="isEnable">
                        <option value="">全部</option>
                        <option value="1">可用</option>
                        <option value="0">不可用</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-btn-group">
                    <button type="button" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
                    #shiroHasPermission("main:smsType:addBtn")
                    <button type="button" id="addBtn" class="layui-btn">添加</button>
                    #end
                </div>
            </div>
        </form>
    </div>
    <div class="layui-row">
        <table class="layui-table" id="smsTypeTable" lay-filter="smsTypeTable"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:smsType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:smsType:delBtn")
    #[[
    {{# if((d.isEnable=='0')){ }}
    <a class="layui-btn layui-btn-xs" lay-event="enable">启用</a>
    {{# }else{ }}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">禁用</a>
    {{# } }}
    ]]#
    #end
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        tableLoad=function(data){
            table.render({
                id : 'smsTypeTable'
                ,elem : '#smsTypeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/main/userGroup/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'sort', title: '序号', width: 120, align: 'center', unresize: true}
                    ,{field:'groupCode', title: '分组编号', width: 200, align: 'center', unresize: true}
                    ,{field:'groupName', title: '分组名称', width: 200, align: 'center', unresize: true}
                    ,{field:'', title: '是否可用', width: 120, align: 'center', unresize: true,templet:function (d) {
                        if(d.isEnable=='1'){
                            return '可用';
                        }else{
                            return '不可用';
                        }
                    }}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#toolBar'}
                ]]
                ,page : true
                ,done:function () {
                }
            });
            table.on('tool(smsTypeTable)',function (obj) {
                if(obj.event==='edit'){
                    pop_show('编辑','#(ctxPath)/main/userGroup/form?id='+obj.data.id,null,null);
                }else if(obj.event==='enable'){
                    layer.confirm("确定要启用吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/main/userGroup/update',
                            notice: true,
                            data: {id:obj.data.id, isEnable:'1'},
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    tableLoad(null);
                                }
                                layer.close(index);
                            },
                            complete : function() {
                            }
                        });
                    });
                }else if(obj.event==='disable'){
                    layer.confirm("确定要禁用吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/main/userGroup/update',
                            notice: true,
                            data: {id:obj.data.id, isEnable:'0'},
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    tableLoad(null);
                                }
                                layer.close(index);
                            },
                            complete : function() {
                            }
                        });
                    });
                }
            });
        }

        tableLoad(null);

        tableReload=function(){
            tableLoad($("#frm").serialize());
        }

        form.on("submit(search)",function(data){
            tableLoad(data.field);
            return false;
        });

        $("#addBtn").on('click',function () {
            pop_show('添加','#(ctxPath)/main/userGroup/form',null,null);
        });
    });
</script>
#end