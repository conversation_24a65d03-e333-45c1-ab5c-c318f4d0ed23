#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        bedTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            bedTypeLoad(data.field);
            return false;
        });

        function bedTypeLoad(data){
            table.render({
                id : 'bedTypeTable'
                ,elem : '#bedTypeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/main/bedType/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'typeName', title: '类型名称', align: 'center', unresize: true}
                    // ,{field:'baseName', title: '所属基地', align: 'center', unresize: true}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'isManyPeople', title: '是否多人', align: 'center', unresize: true,templet:"<div>{{d.isManyPeople=='0'?'<span class='layui-badge layui-bg-green'>可多人用</span>':d.isManyPeople=='1'?'<span class='layui-badge'>不可多人用</span>':'- -'}}</div>"}
                    ,{field:'isBigBed', title: '是否大床位', align: 'center', unresize: true,templet:"<div>{{d.isBigBed=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isBigBed=='0'?'<span class='layui-badge'>否</span>':'- -'}}</div>"}
                    ,{field:'peopleMax', title: '最多人数', align: 'center', unresize: true,templet:"<div>{{d.isManyPeople=='0'?d.peopleMax:''}}</div>"}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/bedType/form" ;
            pop_show("新增床位类型",url,700,450);
        });

        table.on('tool(bedTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/bedType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('bedTypeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/bedType/form?id=" + obj.data.id ;
                pop_show("编辑床位类型",url,700,450);
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('bedTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/bedType/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {bedTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("bedTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:bedType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:bedType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            类型名称:
            <div class="layui-inline">
                <input id="typeName" name="typeName" class="layui-input">
            </div>
            <!--&nbsp;&nbsp;
            所属基地:
            <div class="layui-inline">
                <select name="baseId" lay-search>
                    <option value="">请选择所属基地</option>
                    #for(b : baseList)
                    <option value="#(b.id)">#(b.baseName)</option>
                    #end
                </select>
            </div>-->
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:bedType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        #shiroHasPermission("main:bedType:batchDelBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
        #end
    </div>
    <table id="bedTypeTable" lay-filter="bedTypeTable"></table>
</div>
#end