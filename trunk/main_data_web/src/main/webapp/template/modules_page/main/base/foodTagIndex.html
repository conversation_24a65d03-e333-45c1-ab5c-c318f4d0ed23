#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地菜标签页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('baseFoodTagTab', {
			title: '添加'
			, content: '<div id="foodTagForm"></div>'
			, id: 'form'
		});
		$('#foodTagForm').load('#(ctxPath)/main/base/foodTagForm?baseId=#(baseId??)');
		element.tabChange('baseFoodTagTab', 'form');
    });
    
    edit = function(id){
    	element.tabAdd('baseFoodTagTab', {
			title: '编辑'
			, content: '<div id="foodTagForm"></div>'
			, id: 'form'
		});
		$('#foodTagForm').load('#(ctxPath)/main/base/foodTagForm?id='+id);
		element.tabChange('baseFoodTagTab', 'form');
    }
    
    del = function(id){
    	layer.confirm("确定要作废吗?",function(index){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/base/delFoodTag',
                notice: true,
                data: {id:id, delFlag:'1'},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	location.reload();
                    }
                    layer.close(index);
                },
                complete : function() {
                }
            });
        });
    }
    
	element.on('tab(baseFoodTagTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div id="baseFoodTagTab" class="layui-tab layui-tab-brief" lay-filter="baseFoodTagTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<a id="addBtn" class="layui-btn">添加</a>
				<table class="layui-table">
				<thead>
					<tr>
						<th>序号</th>
						<th>标签名称</th>
						<th>是否有效</th>
						<th>操作</th>
					</tr>
				</thead>
				<tbody>
					#for(foodTag : foodTagList)
					<tr>
						<td>#(for.index+1)</td>
						<td>#(foodTag.tagName??)</td>
						<td>
							#if(foodTag.isEnabled??''=='1')
								<span class='layui-badge layui-bg-green'>有效</span>
							#else
								<span class='layui-badge'>无效</span>
							#end
						</td>
						<td>
							<a class="layui-btn layui-btn-xs" onclick="edit('#(foodTag.id??)')">编辑</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('#(foodTag.id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
#end