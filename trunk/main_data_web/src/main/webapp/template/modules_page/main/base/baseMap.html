#include("/template/common/layout/_page_layout.html")
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
#@layout()

#define pageTitle()选择基地坐标#end

#define css()
<style type="text/css">
    #allmap {
        width: 100%;
        height:89%;
        overflow: hidden;
        font-family:"微软雅黑";
        border:2px solid #c0c0c0;
        margin-top:10px;
    }
</style>
#end

#define js()
<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=iD2gwtGfo1p98lPenidUyx8h"></script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" style="float:left;margin-top:15px;margin-left: 10px;">
            地点名称:
            <div class="layui-inline">
                <input id="location" name="location" class="layui-input">
            </div>
            <input type="button" class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" lay-filter="search" value="查询">
        </form>
        <input type="button" class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="choose" value="确定选择地点">
    </div>
    <input type="hidden" id="lng" value=""/>
    <input type="hidden" id="lat" value=""/>
    <div id="allmap"></div>
</div>
#end
<script type="text/javascript">
layui.use(['form','layer','table'], function() {
    var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;
    var map = new BMap.Map("allmap");
    $(function(){
        map.centerAndZoom(new BMap.Point(113.247989,23.342099),17);
        map.enableScrollWheelZoom();
        map.enableContinuousZoom();
    });

    map.addEventListener("click",function(e){
        map.clearOverlays();
        var lng=e.point.lng;
        var lat=e.point.lat;
        $("#lng").val(lng);
        $("#lat").val(lat);
        var pt = new BMap.Point(lng, lat);
        map.centerAndZoom(pt, 17);
        var marker = new BMap.Marker(pt);
        marker.setAnimation(BMAP_ANIMATION_BOUNCE);
        map.addOverlay(marker);
    });

    //清除标识
    function clearOverlays() {
        if (markersArray) {
            for (i in markersArray) {
                map.removeOverlay(markersArray[i])
            }
        }
    };

    //地点名称查询
    $("#search").click(function(){
        var location = $("#location").val();
        console.log(location);
        map.centerAndZoom(location,17);
        map.enableScrollWheelZoom();
        map.enableContinuousZoom();
    });

    //获取地点坐标
    $("#choose").click(function(){
        var lng = $("#lng").val();
        var lat = $("#lat").val();
        if((lng == null || lat == null) || typeof(lng)==='undefined' || (lng == "" || lat == "") ){
            layer.msg('请标注地点', function () {});
            return;
        }
        parent.getCoordinate(lng,lat);
        pop_close();
    });

})
</script>