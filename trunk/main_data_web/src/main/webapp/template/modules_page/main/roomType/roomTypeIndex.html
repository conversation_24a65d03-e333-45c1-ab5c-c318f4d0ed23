#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        roomTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            roomTypeLoad(data.field);
            return false;
        });



        function roomTypeLoad(data){
            table.render({
                id : 'roomTypeTable'
                ,elem : '#roomTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/main/roomType/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'baseName', title: '所属基地', align: 'center', unresize: true}
                    ,{field:'typeName', title: '类型名称', align: 'center', unresize: true}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'isBigBedRoom', title: '是否大床房', align: 'center', unresize: true,templet:"<div>{{ d.isBigBedRoom=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isBigBedRoom=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                    ,{field:'isBooking', title: '是否可预定', align: 'center', unresize: true,templet:"<div>{{ d.isBooking=='0'?'<span class='layui-badge layui-bg-green'>可预定</span>':d.isBooking=='1'?'<span class='layui-badge'>不可预定</span>':'- -' }}</div>"}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/roomType/form" ;
            pop_show("新增房间类型",url,700,500);
        });

        roomTypeLoadReload=function() {
            //roomTypeLoad({"typeName":$("#typeName").val(),"baseId":$("#baseId").val()});
            $("#search").click();
        }

        table.on('tool(roomTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/roomType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('roomTypeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/roomType/form?id=" + obj.data.id ;
                pop_show("编辑房间类型",url,700,500);
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('roomTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/roomType/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {roomTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("roomTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:roomType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:roomType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            类型名称:
            <div class="layui-inline">
                <input id="typeName" name="typeName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            所属基地:
            <div class="layui-inline">
                <select name="baseId" lay-search>
                    <option value="">请选择所属基地</option>
                    #for(b : baseList)
                    <option value="#(b.id)">#(b.baseName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search" id="search">查询</button>
        </form>
        #shiroHasPermission("main:roomType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        #shiroHasPermission("main:roomType:batchDelBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
        #end
    </div>
    <table id="roomTypeTable" lay-filter="roomTypeTable"></table>
</div>
#end