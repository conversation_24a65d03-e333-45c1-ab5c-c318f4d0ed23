#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()车型管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;width: 180px;padding: 0 20px;">一级分类名称</label>
            <input class="layui-input" name="name" id="name" placeholder="" autocomplete="off">
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:bookkeepingType:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="carTypeTable" lay-filter="carTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'carTypeTable',
            elem: '#carTypeTable'
            ,url : '#(ctxPath)/main/firstLevel/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'name', title: '一级分类名称',unresize:true}
                ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable == '0'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(carTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/firstLevel/form?id=" + obj.data.id ;
                layerShow("编辑一级分类",url,550,600);
            } else if (obj.event === 'del') {

            }
        });



        carTypeTableReload=function(){
            var name=$("#name").val();
            table.reload('carTypeTable',{'where':{'name':name}});
        }

        $("#search").on('click',function () {
            carTypeTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/firstLevel/form";
            layerShow("添加一级分类",url,550,600);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:bookkeepingType:editBtn")
    #end
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>


</script>
#end