#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公共门编辑#end

#define css()

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<div class="layui-form-item">
			<label class="layui-form-label">*基地</label>
			<div class="layui-input-block">
				<select id="baseId" name="baseId">
                    #for(model : modelList)
                    	<option value="#(model.id)" #if(equiModel.equiModelId??==model.id) selected #end>#(model.modelName)</option>
                    #end
                </select>
			</div>
		</div>
		<div class="layui-form-item layui-form-text">
			<label class="layui-form-label">备注</label>
			<div class="layui-input-block">
				<textarea name="remark" placeholder="请输入描述" class="layui-textarea">#(model.remark??)</textarea>
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="id" name="id" value="#(model.id??)"/>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button id="saveBtn" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		form.on('submit(saveBtn)',function (obj) {
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/baseDoor/save',
				data: obj.field,
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state==='ok'){
						pop_close();
						parent.doorTableReload();
					}
				},
				complete : function() {
				}
			});

			return false;
		});

	});
</script>
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end