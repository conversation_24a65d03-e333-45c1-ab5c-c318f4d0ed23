#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()保存旅游线路#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;" id="touristRouteForm">
    <input type="hidden" id="id" name="id" value="#(model.id??)"/>
    <input type="hidden" id="routeId" name="routeId" value="#(routeId??)">
    <div class="layui-form-item">
        <label class="layui-form-label">开始日期</label>
        <div class="layui-input-inline">
            <input type="text" id="startDate" name="startDate" autocomplete="off" class="layui-input" lay-verify="required" value="#date(model.startDate??,'yyyy-MM-dd')" placeholder="请输入开始日期">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">扣卡天数</label>
        <div class="layui-input-inline">
            <input type="text" name="times" class="layui-input" autocomplete="off" lay-verify="required|number" value="#(model.times??)" placeholder="请输入扣卡天数">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">扣卡金额</label>
        <div class="layui-input-inline">
            <input type="text" name="amount" class="layui-input" autocomplete="off" lay-verify="required|number" value="#(model.amount??)" placeholder="请输入扣卡金额">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="1" lay-verify="required" title="可用" #if(model!=null && model.isEnable=='1') checked #elseif(model==null) checked #end>
            <input type="radio" name="isEnable" value="0" lay-verify="required" title="不可用" #if(model.isEnable??=='0') checked #end>
        </div>
    </div>

    <div class="layui-form-footer" style="margin-top: 90px;">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery;
        var laydate = layui.laydate;

        laydate.render({
            elem: '#startDate'
            ,trigger:'click'
            ,value:''
        });


        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/touristRoute/deductConfigSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#touristRouteForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.touristRouteTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
