#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜列表页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
    <div class="layui-form-item">
    	<table class="layui-table" lay-size="sm">
			<thead>
				<tr>
					<th width="8%"><font color="red">*</font>排序</th>
					<th width="20%"><font color="red">*</font>标签</th>
					<th width="22%"><font color="red">*</font>菜</th>
					<th width="15%"><font color="red">*</font>销售价</th>
					<th width="25%"><font color="red">*</font>是否可用</th>
					<th width="10%">操作</th>
				</tr>
			</thead>
			<tbody id="baseFoodList">
				#for(baseFood : baseFoodList)
				<tr id="food-#(for.index+1)">
					<td>
						<input type="text" name="foodList[#(for.index+1)].foodSort" class="layui-input" lay-verify="required" value="#(baseFood.foodSort??'')" placeholder="请输入排序" autocomplete="off">
					</td>
					<td>
						<select id="foodTagId-#(for.index+1)" name="foodList[#(for.index+1)].tagId" lay-verify="required" lay-search>
							<option value="">请选择标签</option>
							#for(tag : foodTagList)
								<option value="#(tag.id)" #(tag.id == baseFood.tagId?'selected':'')>#(tag.tagName)</option>
							#end
						</select>
					</td>
					<td>
						<select id="foodId-#(for.index+1)" name="foodList[#(for.index+1)].foodId" lay-verify="required" lay-search>
							<option value="">请选择菜</option>
							#for(food : foodInfoList)
								<option value="#(food.id)" #(food.id == baseFood.foodId?'selected':'')>#(food.foodName)</option>
							#end
						</select>
					</td>
					<td>
						<input type="text" name="foodList[#(for.index+1)].salesPrice" class="layui-input" lay-verify="required" value="#(baseFood.salesPrice??'')" placeholder="请输入销售价" autocomplete="off">
					</td>
					<td>
						<input type="radio" name="foodList[#(for.index+1)].isEnabled" value="0" title="否" #if(baseFoodList!=null && baseFood.isEnabled=='0') checked #elseif(baseFoodList==null) checked #end>
            			<input type="radio" name="foodList[#(for.index+1)].isEnabled" value="1" title="是" #if(baseFood.isEnabled??=='1') checked #end>
					</td>
					<td>
						<input type="hidden" name="foodList[#(for.index+1)].id" value="#(baseFood.id??)">
						<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('food-#(for.index+1)','#(baseFood.id??)')">作废</a>
					</td>
				</tr>
				#end
			</tbody>
		</table>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="baseId" value="#(baseId??)">
			<input type="hidden" id="baseFoodCount" name="baseFoodCount" value="#(baseFoodList.size()??)">
			<div id="addBtn" class="layui-btn">添加</div>
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script id="tagTrTpl" type="text/html">
	<tr id="food-{{d.idx}}">
		<td><input type="text" name="foodList[{{d.idx}}].foodSort" class="layui-input" value="" lay-verify="required" placeholder="请输入排序" autocomplete="off"></td>
		<td>
			<select id="foodTagId-{{d.idx}}" name="foodList[{{d.idx}}].tagId" lay-verify="required">
				<option value="">请选择标签</option>
			</select>
		</td>
		<td>
			<select id="foodId-{{d.idx}}" name="foodList[{{d.idx}}].foodId" lay-verify="required" lay-search>
				<option value="">请选择菜</option>
			</select>
		</td>
		<td><input type="text" name="foodList[{{d.idx}}].salesPrice" class="layui-input" value="" lay-verify="required" placeholder="请输入销售价" autocomplete="off"></td>
		<td>
			<input type="radio" name="foodList[{{d.idx}}].isEnabled" value="0" title="否">
            <input type="radio" name="foodList[{{d.idx}}].isEnabled" value="1" title="是" checked>
		</td>
		<td>
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('food-{{d.idx}}','')">作废</a>
		</td>
	</tr>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate','laytpl' ], function() {
	var form = layui.form
	, laytpl = layui.laytpl
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		$('#baseFoodCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1)}, function(html){
			targetId.append(html);
		});
		#for(tag : foodTagList)
			$('#foodTagId-'+(parseInt(idx)+1)).append('<option value="#(tag.id)")>#(tag.tagName)</option>');
		#end
		#for(food : foodInfoList)
			$('#foodId-'+(parseInt(idx)+1)).append('<option value="#(food.id)")>#(food.foodName)</option>');
		#end
		form.render();
    };
    
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		addTpl($('#baseFoodList'), tagTrTpl.innerHTML, $('#baseFoodCount').val());
	});
	
	//删除方法
	del = function(trId, dataId) {
		if(dataId!=null && dataId!=''){
			layer.confirm('您确定要作废？', {icon: 3, title:'询问'}, function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/base/delBaseFood',
					data: {id:dataId, delFlag:'1'},
					notice: true,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							$("#"+trId).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else{
			$("#"+trId).remove();
		}
	};
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var trLength = $('#baseFoodList tr').length;
		if(trLength>0){
			//提交表单数据
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/main/base/saveBaseFood',
	            data: $(formObj.form).serialize(),
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		pop_close();
	            	}
	            },
	            complete : function() {
			    }
	        });
		}else{
			layer.msg('请添加数据!',{icon:5});
		}
		return false;
	});
});
</script>
#end