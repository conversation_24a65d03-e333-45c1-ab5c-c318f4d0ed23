#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()分公司管理#end

#define css()
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            分公司名称:
            <div class="layui-inline">
                <input id="fullName" name="fullName" class="layui-input">
            </div>

            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:branchOffice:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        #shiroHasPermission("main:branchOffice:batchDelBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
        #end
    </div>
    <table id="baranchofficeTable" lay-filter="baranchofficeTable"></table>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        baranchofficeLoad(null);

        sd=form.on("submit(search)",function(data){
            baranchofficeLoad(data.field);
            return false;
        });

        function baranchofficeLoad(data){
            table.render({
                id : 'baranchofficeTable'
                ,elem : '#baranchofficeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.85
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/main/branchOffice/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'shortName', title: '公司简称', align: 'center', unresize: true}
                    ,{field:'fullName', title: '公司名称', align: 'center', unresize: true,templet:"<div><a href='https://map.baidu.com/@12197356.892065747,2111340.270582287,17z/latlng={{d.coordinate}}&title={{d.fullName}}&content={{d.fullName}}&autoOpen=true'target='_blank'>{{d.fullName}}</a></div>"}
                    ,{field:'type', title:'类型', width:100, unresize:true, align:'center', templet: '#dictTpl("branch_office_type", "type")'}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                    ,{field:'openDate', title: '开业时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.openDate,'yyyy-MM-dd') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/branchOffice/banchOfficeForm" ;
            pop_show("新增分公司",url,800,700);
        });

        table.on('tool(baranchofficeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/branchOffice/delBranchoffice',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('baranchofficeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/branchOffice/banchOfficeForm?id=" + obj.data.id ;
                pop_show("编辑分公司",url,800,700);
            }
        });

        //批量获取被作废数据
        getCheckTableData = function(){
            var baranchofficeCheckStatus = table.checkStatus('baranchofficeTable');

            // 获取选择状态下的数据
            return baranchofficeCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/branchOffice/batchDelBranchoffice";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {'branchOfficeData':JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("baranchofficeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:branchOffice:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:branchOffice:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end