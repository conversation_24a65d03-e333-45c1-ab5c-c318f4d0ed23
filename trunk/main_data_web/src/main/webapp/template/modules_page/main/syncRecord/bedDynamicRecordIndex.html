#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        /*//监听基地select
        form.on('select(baseSelect)',function (obj) {
            if(obj.value!=''){
                $("#buildingSelect").empty();
                $.post('#(ctxPath)/main/buildingRoomManage/getBuildingList',{'baseId':obj.value},function (res) {
                    if(res.state==='ok'){
                        var buildingStr='<option value="">请选择楼宇</option>';
                        var data=res.data;
                        $.each(data,function (index,item) {
                            buildingStr+='<option value="'+item.id+'">'+item.buildingName+'</option>'
                        });
                        $("#buildingSelect").html(buildingStr);
                    }
                    form.render('select');
                });
            }else{
                $("#buildingSelect").html('<option value="">请选择楼宇</option>');
            }
            $($("#floorSelect")).empty();
            form.render('select');
        });

        //监听楼宇select
        form.on('select(buildingSelect)',function (obj) {
            if(obj.value!=''){
                $("#floorSelect").empty();
                $.post('#(ctxPath)/main/buildingRoomManage/getFloorList',{'buildingId':obj.value},function (res) {
                    if(res.state==='ok'){
                        var floorStr='<option value="">请选择楼层</option>';
                        $.each(res.data,function (index,item) {
                            floorStr+='<option value="'+item.id+'">'+item.floorName+'</option>'
                        });
                        $("#floorSelect").html(floorStr);
                    }
                    form.render('select');
                });
            }else{
                $("#floorSelect").html('<option value="">请选择楼层</option>');
            }
            form.render('select');
        });*/



        table.render({
            id : 'syncRecordTable'
            ,elem : '#syncRecordTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/main/syncRecord/findBedDynamicRecordPageByAppNo'
            ,where:{'appNo':$("#app").val(),'status':$("#status").val()}
            ,cellMinWidth: 80
            ,cols: [[
                {field:'dynamicType', title: '动态类型',width:110, align: 'center', unresize: true,templet:"<div>#[[ {{#  if(d.dynamicType==='book'){ }} <span class=\"layui-badge layui-bg-green\">预定</span> {{#}else if(d.dynamicType==='checkin'){}} <span class=\"layui-badge layui-bg-blue\">入住</span> {{#  } else if(d.dynamicType==='checkout') { }} <span class=\"layui-badge layui-bg-orange\">退住</span> {{#  }else if(d.dynamicType==='cancelBook'){ }} <span class=\"layui-badge layui-bg-gray\">取消预定</span> {{#}}} ]]#</div>"}
                ,{field:'baseId', title: '基地', align: 'center', unresize: true}
                ,{field:'companyName', title: '分公司',width:100,align: 'center', unresize: true}
                ,{field:'bedId', title: '床位',width:100,align: 'center', unresize: true}
                ,{field:'bookNo', title: '预定号', align: 'center', unresize: true}
                ,{field:'checkinNo', title: '入住号', align: 'center', unresize: true}
                ,{field:'memberName', title: '入住人',width:100, align: 'center', unresize: true}
                ,{field:'gender',title:'性别',align:'center',unresize:true,templet:"<div>#[[ {{#  if(d.gender==='Male'){ }} 男 {{#}else if(d.gender==='Female'){}} 女 {{#  } else { }} 未知 {{#  } }}]]#</div>"}
                ,{field:'isLongStay', title: '是否长住',width:90, align: 'center', unresize: true,templet:"<div>#[[{{#if(d.isLongStay===1){}} 是 {{#}else if(d.isLongStay===0){}} 否 {{#}else{}} - - {{#}}}]]#</div>"}
                ,{field:'syncStatus', title: '同步状态',width:90, align: 'center', unresize: true,templet:"<div>{{ d.syncStatus=='0'?'<span class='layui-badge layui-bg-green'>成功</span>':d.syncStatus=='1'?'<span class='layui-badge'>失败</span>':'- -' }}</div>"}
                ,{field:'bookStartDate', title: '开始预定时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.bookStartDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'bookEndDate', title: '结束预定时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.bookEndDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'checkinDate', title: '入住时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.checkinDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'checkoutDate', title: '退住时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.checkoutDate,'yyyy-MM-dd') }}</div>"}
                ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });

        table.on('tool(syncRecordTable)',function (obj) {
            if(obj.event==='sync'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/syncRecord/sendBedDynamicRecord',
                    data: {'id':obj.data.id},
                    notice: false,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            syncRecordTableReload();
                            layer.msg(rep.msg,{icon:6,time:2000},function(index){});
                        }else{
                            layer.msg(rep.msg,{icon:5,time:30000},function(index){});
                        }
                    },
                    complete : function() {
                    }
                });
            }
        });

        syncRecordTableReload=function(){
            table.reload('syncRecordTable',{"where":{'appNo':$("#app").val(),'dynamicType':$("#dynamicType").val(),'syncStatus':$("#syncStatus").val(),'roomAndBed':$("#roomAndBed").val()}});
        }

        $("#roomAndBed").on('blur',function () {
            syncRecordTableReload();
        });

        form.on('select',function (obj) {
            syncRecordTableReload();
        });

        $("#syncData").on('blur',function () {
            syncRecordTableReload();
        })


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:bedDynamicRecord:syncBtn")
    #[[
    {{#if(d.syncStatus===1){}}
    <a class="layui-btn layui-btn-xs" lay-event="sync">同步</a>
    {{#}}}
    ]]#
    #end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    应用:
                    <div class="layui-inline">
                        <select id="app" lay-filter="app">
                            #for(app:appJoinList)
                            <option value="#(app.appNo)">#(app.appName)</option>
                            #end
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    动态类型:
                    <div class="layui-inline">
                        <select id="dynamicType">
                            <option value="">全部</option>
                            #for(type : typeMap)
                            <option value="#(type.key)">#(type.value)</option>
                            #end
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    状态:
                    <div class="layui-inline">
                        <select id="syncStatus" lay-filter="syncStatus">
                            <option value="">全部</option>
                            <option value="0">成功</option>
                            <option value="1">失败</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    房间号/床位号:
                    <div class="layui-inline">
                        <input class="layui-input" id="roomAndBed" name="roomAndBed" placeholder="搜索房间/床位" autocomplete="off" >
                    </div>
                </div>
            </div>

        </form>
    </div>
    <table id="syncRecordTable" lay-filter="syncRecordTable"></table>
</div>
#end