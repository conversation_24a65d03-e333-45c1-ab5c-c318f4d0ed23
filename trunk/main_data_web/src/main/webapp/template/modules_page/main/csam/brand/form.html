#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="mattressTypeForm">
    <div class="layui-row">
        <input type="hidden"  name="id" value="#(model.id??)"/>

        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">品牌名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="assetBrandName" class="layui-input" lay-verify="required" value="#(model.assetBrandName??)" placeholder="请输入品牌名称">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">排序</label>
            <div class="layui-input-block">
                <input type="text" name="assetBrandOrder" class="layui-input" lay-verify="required|number" value="#(model.assetBrandOrder??)" placeholder="请输入排序">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">是否可用</label>
            <div class="layui-input-block">
                <input type="radio" name="isEnabled" value="1" title="是" #if(model==null || model.isEnabled??=='1') checked #end >
                <input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled??=='0') checked #end>
            </div>
        </div>
        <!--<div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="mattressType.description" placeholder="请输入内容" class="layui-textarea">#(mattressType.description??)</textarea>
            </div>
        </div>-->
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/csam/assetBrandSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#mattressTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.mattressTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
