#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="roomTypeForm">
    <div class="layui-col-xs12">
        <input type="hidden"  name="id" value="#(businessEntity.id??)"/>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">业务实体名称</label>
            <div class="layui-input-block">
                <input type="text" name="entityName" class="layui-input" lay-verify="required" value="#(businessEntity.entityName??)" placeholder="请输入业务实体名称">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">排序</label>
            <div class="layui-input-block">
                <input type="text" name="sort" class="layui-input" lay-verify="required|number" value="#(businessEntity.sort??)" placeholder="请输入排序号">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否可用</label>
            <div class="layui-input-block">
                <select id="isEnable" name="isEnable" lay-verify="required">
                    <option value="1" #(businessEntity != null ?(businessEntity.isEnable == '1' ? 'selected':''):'')>可用</option>
                    <option value="0" #(businessEntity != null ?(businessEntity.isEnable == '0' ? 'selected':''):'')>不可用</option>
                </select>
            </div>
        </div>
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/businessEntity/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#roomTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("roomTypeTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


    });
</script>
#end
