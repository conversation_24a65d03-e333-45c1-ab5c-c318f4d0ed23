#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地用餐时间编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('baseMealTimeTab', {
			title: '添加'
			, content: '<div id="mealTimeForm"></div>'
			, id: 'form'
		});
		$('#mealTimeForm').load('#(ctxPath)/main/base/mealTimeForm?baseId=#(baseId??)');
		element.tabChange('baseMealTimeTab', 'form');
    });
    
    edit = function(id){
    	element.tabAdd('baseMealTimeTab', {
			title: '编辑'
			, content: '<div id="mealTimeForm"></div>'
			, id: 'form'
		});
		$('#mealTimeForm').load('#(ctxPath)/main/base/mealTimeForm?id='+id);
		element.tabChange('baseMealTimeTab', 'form');
    }
    
    del = function(id){
    	layer.confirm("确定要作废吗?",function(index){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/base/deleteMealTime',
                notice: true,
                data: {id:id},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	location.reload();
                    }
                    layer.close(index);
                },
                complete : function() {
                }
            });
        });
    }
    
	element.on('tab(baseMealTimeTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div id="baseMealTimeTab" class="layui-tab layui-tab-brief" lay-filter="baseMealTimeTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<a id="addBtn" class="layui-btn">添加</a>
				<table class="layui-table">
				<thead>
					<tr>
						<th>序号</th>
						<th>开始时段</th>
						<th>结束时段</th>
						<th>时段标识</th>
						<th>操作</th>
					</tr> 
				</thead>
				<tbody>
					#for(mealTime : mealTimeList)
					<tr>
						<td>#(for.index+1)</td>
						<td>#date(mealTime.beginTime??, 'HH:mm')</td>
						<td>#date(mealTime.endTime??, 'HH:mm')</td>
						<td>#statusName(com.cszn.integrated.service.entity.status.MealTimeFlag::me(), mealTime.mealTimeFlag??'')</td>
						<td>
							<a class="layui-btn layui-btn-xs" onclick="edit('#(mealTime.Id??)')">编辑</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('#(mealTime.Id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
#end