#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


		var setting = {
			check:{enable:false}
			,view:{selectedMulti:false}
			,data:{simpleData:{enable:true}}
			,async:{enable:true, type:"post", url: '#(ctxPath)/main/goodType/goodTypeTree'}
			,callback:{
				onClick: function(event, treeId, treeNode, clickFlag) {
					/*layer.confirm('确定要分配所选择的机构？', function(index) {
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/persOrgEmployee/orgEmpSave',
                            data: {empId:'#(empId??)', orgId:treeNode.id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                pageTableReload();
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });*/
					$("#typeId").val(treeNode.id);
					$("#typeName").val(treeNode.name);

					var data={'name':$("#goodName").val(),'goodTypeId':treeNode.id};
					goodModelLoad(data);
				},
				onAsyncSuccess: zTreeOnAsyncSuccess
			}
		};
		// 初始化树结构
		var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);

		var firstAsyncSuccessFlag = 0;
		function zTreeOnAsyncSuccess(event, treeId, msg) {

			try {
				//调用默认展开第一个结点
				var selectedNode = zTreeObj.getSelectedNodes();
				var nodes = zTreeObj.getNodes();
				zTreeObj.expandNode(nodes[0], true);
				var childNodes = zTreeObj.transformToArray(nodes[0]);
				zTreeObj.expandNode(childNodes[0], true);
				zTreeObj.selectNode(childNodes[0]);
				var childNodes1 = zTreeObj.transformToArray(childNodes[0]);
				zTreeObj.checkNode(childNodes1[0], true, true);
				firstAsyncSuccessFlag = 1;

				var data={'goodTypeId':nodes[0].id};
				$("#typeId").val(nodes[0].id);
				$("#typeName").val(nodes[0].name);
				console.log(nodes[0].id);
				console.log(nodes[0].name);
				goodModelLoad(data);
			} catch (err) {

			}

		}




		sd=form.on("submit(search)",function(data){
			goodModelTableReload();
			return false;
		});

		function goodModelLoad(data){
			table.render({
				id : 'goodModelTable'
				,elem : '#goodModelTable'
				,method : 'POST'
				,where : data
				,limit : 15
				,limits : [15,30,45,50]
				,url : '#(ctxPath)/main/goodModel/pageList'
				,cellMinWidth: 80
				,cols: [[
					{field: 'goodModelNo', title: '商品编号',unresize:true}
					,{field:'name', title: '名称', align: 'center', unresize: true}
					,{field:'typeName', title: '类型名称', align: 'center', unresize: true}
					,{field:'unit', title: '单位', align: 'center', unresize: true}
					,{field:'salePrice', title: '销售价', align: 'center', unresize: true}
					,{field:'costPrice', title: '成本价', align: 'center', unresize: true}
					//,{field:'isBuckleCard', title: '是否可扣卡', align: 'center', unresize: true,templet:"<div>{{ d.isBuckleCard=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.isBuckleCard=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
					//,{field:'cardTimes', title: '扣卡天数', align: 'center', unresize: true}
					,{field:'isEnabled', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
					,{field:'description', title: '备注', align: 'center', unresize: true}
					,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};

		goodModelTableReload=function(){
			var data={'name':$("#goodName").val(),'goodTypeId':$("#typeId").val()};
			table.reload('goodModelTable',{'where':data});
		}

		// 添加
		$("#add").click(function(){
			var typeId=$("#typeId").val();
			if(typeId=='' || typeId==null){
				layer.msg('请选择商品类型', {icon: 2, offset: 'auto'});
				return;
			}
			$(this).blur();
			var url = "#(ctxPath)/main/goodModel/form?typeId="+typeId+"&typeName="+$("#typeName").val() ;
			pop_show("新增商品",url,600,700);
		});

		table.on('tool(goodModelTable)',function(obj){
			if (obj.event === 'del') {
				layer.confirm("确定要作废吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/pers/position/delete',
						notice: true,
						data: {id:obj.data.id},
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								goodModelTableReload();
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}else if(obj.event === 'edit'){
				var url = "#(ctxPath)/main/goodModel/form?id=" + obj.data.id+"&typeId="+$("#typeId").val()+"&typeName="+$("#typeName").val() ;
				pop_show("编辑商品",url,600,700);
			}
		});
	});
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("main:goodModel:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
</script>
#end

#define content()
<div>
	<div class="layui-col-xs3 layui-col-sm2 layui-col-md2 layui-col-lg2">
		<fieldset class="layui-elem-field layui-field-title" style="display:block;">
			<legend>商品类型</legend>
			<div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
		</fieldset>
	</div>
	<div class="layui-col-xs10 layui-col-sm10 layui-col-md10 layui-col-lg10">
		<div class="layui-row">
			<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
				商品名称:
				<div class="layui-inline">
					<input id="goodName" name="goodName" class="layui-input">
				</div>
				&nbsp;&nbsp;
				<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
			</form>
			<input type="hidden" id="typeId" value="">
			<input type="hidden" id="typeName" value="">
			#shiroHasPermission("main:goodModel:addBtn")
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
			#end
		</div>
		<table class="layui-table" id="goodModelTable" lay-filter="goodModelTable"></table>
	</div>


</div>
#end