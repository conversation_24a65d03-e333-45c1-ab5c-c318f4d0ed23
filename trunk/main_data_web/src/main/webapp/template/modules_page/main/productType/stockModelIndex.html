#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()货物型号页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <div class="layui-col-xs2">
            <div class="layui-row">
                <button id="addBtn" type="button" class="layui-btn">添加</button>
            </div>
            <div class="layui-row">
                <fieldset class="layui-elem-field">
                    <legend>分类</legend>
                    <div id="typeTree" class="ztree" style="height:600px;overflow:auto;"></div>
                </fieldset>
            </div>
        </div>
        <div class="layui-col-xs10">
            <div class="layui-row">
            <form id="typeForm" class="layui-form layui-form-pane" action="" lay-filter="layform" method="post">
                <div class="layui-inline">
                    <label class="layui-form-label">货物类型</label>
                    <div class="layui-input-inline" style="width:100px;">
                        <select id="type" name="type" lay-filter="aihao">
                            <option value="">全部</option>
                            <option value="Assets">资产</option>
                            <option value="Consume">耗材</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">名称</label>
                    <div class="layui-input-inline">
                        <input type="text" name="name" id="name" placeholder="请输入名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">创建日期</label>
                    <div class="layui-inline">
                        <div class="layui-input-inline" style="width:100px;">
                            <input id="startDate" name="startDate" type="text" autocomplete="off" class="layui-input" placeholder="开始日期">
                        </div>
                        <div class="layui-input-inline" style="width:100px;">
                            <input id="endDate" name="endDate" type="text" autocomplete="off" class="layui-input" placeholder="结束日期">
                        </div>
                    </div>
                </div>
                <div class="layui-inline">
                    <div class="layui-btn-group">
                        <input type="hidden" name="stockTypeId" id="stockTypeId" >
                        <button type="button" id="search" class="layui-btn">搜索</button>
                        <button type="button" id="export" class="layui-btn">导出</button>
                    </div>
                </div>
            </form>
            </div>
            <div class="layui-row">
                <input id="productTypeId" type="hidden" value="#(productTypeId??)">
                <table class="layui-table" id="stockModelTable" lay-filter="stockModelTable"></table>
            </div>
        </div>
    </div>
    <!--<div style="height: 200px;width: 400px;display: none;" id="imgDiv">
        <div class="layer_notice" style="height: 75px;width: 300px;">
            <img id="myImg" src="" style="width: 100%;height: 100%;" >
        </div>
    </div>-->
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script src="#(ctxPath)/static/js/jQuery.print.js"></script>
<script type="text/html" id="toolBar">
    <a class="layui-btn layui-btn-xs" style="margin-left: 2px;" lay-event="select">选择</a>
</script>
<script>
    layui.use(['form','tree','table', 'laydate'], function() {
        var $ = layui.$, form=layui.form,layer=layui.layer,tree=layui.tree,table=layui.table,laydate = layui.laydate;

        //树属性配置
        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/wms/stockType/stockTypeTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#stockTypeId").val(treeNode.id);
                    table.reload('stockModelTable',{'where':{'stockTypeId':treeNode.id}});
                }
            }
        };

        // 设置顶层菜单按钮点击事件
        var zTreeObj = $.fn.zTree.init($("#typeTree"), setting);

        //时间渲染
        laydate.render({
            elem : '#startDate'
            ,trigger:'click'
        });

        //时间渲染
        laydate.render({
            elem : '#endDate'
            ,trigger:'click'
        });

        table.render({
            id : 'stockModelTable'
            ,elem : '#stockModelTable'
            ,method : 'POST'
            ,where : ''
            ,limit : 13
            ,limits : [13,20,30,40]
            ,url : '#(ctxPath)/wms/stockModel/findStockModelPageList'
            ,height:$(document).height()*0.75
            ,cellMinWidth: 80
            ,cols: [[
            	{type: 'numbers', width:80, title: '序号',unresize:true}
//                 ,{field:'stockModelNo', title: '旧商品编号',width:160, align: 'center', unresize: true}
                ,{field:'newStockModelNo', title: '新商品编号',width:160, align: 'center', unresize: true}
                ,{field:'name', title: '商品名称', align: 'center', unresize: true}
                ,{field:'standard', title: '规格型号', align: 'center',width:120, unresize: true}
                //,{field:'type', title: '货物类型', align: 'center', unresize: true,width:90,templet:"#[[<div>{{#if(d.type==='Assets'){}}资产 {{#}else if(d.type==='Consume'){}}耗材{{#}else{}} - - {{#}}} </div>]]#"}
                ,{field:'unitName', title: '单位', align: 'center',width:70, unresize: true}
                /*,{field:'salePrice', title: '销售价格', align: 'center',width:95, unresize: true}*/
                ,{field:'costPrice', title: '成本价格', align: 'center',width:90, unresize: true}
                ,{field:'exchangePrice', title: '兑换积分', align: 'center',width:120, unresize: true}
                ,{field:'exchangeBeanCoupons', title: '兑换豆豆券', align: 'center',width:120, unresize: true}
                ,{field:'isExchange', title: '是否参与兑换',width:130, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.isExchange=='1'){}} <span class='layui-badge layui-bg-green'>兑换</span> {{#}else{}} <span class='layui-badge'>不兑换</span> {{#}}} </div>]]#"}
                ,{field:'isEnabled', title: '是否有效',width:100, align: 'center', unresize: true,templet:"#[[<div>{{#if(d.isEnabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                ,{fixed:'right', title: '操作', width: 100, align: 'center', unresize: true, toolbar: '#toolBar'}
            ]]
            ,page : true
            ,done:function () {

            }
        });

        table.on('tool(stockModelTable)',function (obj) {
            if(obj.event==='select'){
                $.ajax({
                    data: {'productId':obj.data.id,"typeId":$("#productTypeId").val()},
                    url: '#(ctxPath)/mall/productType/typeAddProduct',
                    dataType: 'json',
                    timeout:30000,
                    responseType:'arraybuffer',
                    beforeSend: function(XMLHttpRequest){
                        if (true) {
                            layer.load();
                        }
                    },
                    success: function(json){
                        if (json.state == 'ok') {
                            layer.msg( json.msg, {icon: 6, offset: 'auto'});
                            parent.reloadProductTableLoad();
                        }else{
                            layer.msg( json.msg, {icon: 2, offset: 'auto'});
                            return false;
                        }
                    },
                    complete :function(XMLHttpRequest, TS){
                        layer.closeAll('loading');
                    }
                });
            }
        });

        modelImageIndex=function (modelId){
            var url = "#(ctxPath)/wms/stockModel/modelImage?id=" + modelId ;
            pop_show("商品图片",url,900,650);
        }

        $(function(){
            document.onkeydown = function(e){
                var ev = document.all ? window.event : e;
                if(ev.keyCode==13) {

                    return false;
                }
            }
        });

        printBarCode=function(barcodeNumber,name,standard,exchangePrice){
            var printNum=$("#printNum").val();
            var g = /^[1-9]*[1-9][0-9]*$/;
            if(!g.test(printNum)){
                layer.msg( '请输入正数', {icon: 2, offset: 'auto'});
                return false;
            }
            var data="";
            if(0==exchangePrice){
                exchangePrice='';
            }
            for(var i=0;i<printNum;i++){
                data+=barcodeNumber+"\t"+name+"\t"+standard+"\t"+exchangePrice+"\t\r\n";
            }
            var param={"TemplateName":"barCode.btw","PrinterName":"","Data":data};
            layer.load();
            $.ajax({
                url: "http://127.0.0.1:8050/LabelPrint?Type=LabelPrint",
                type: "POST",
                dataType: "json",
                data: JSON.stringify(param),
                contentType: 'application/json',
                success: function (result) {
                    layer.closeAll('loading');
                },
                error: function (e) {
                    layer.closeAll('loading');
                    layer.msg( e.responseText == null ? "未启动打印服务" : e.responseText, {icon: 2, offset: 'auto'});
                }
            });
        }

        $("#search").on('click',function () {
            stockModelTableReload();
        })
        stockModelTableReload=function () {
            table.reload('stockModelTable',{"where":{"name":$("#name").val(),"type":$("#type").val(),"stockTypeId":$("#stockTypeId").val(),"startDate":$("#startDate").val(),"endDate":$("#endDate").val()}});
        }

        $("#addBtn").on('click',function () {
            pop_show('编辑','#(ctxPath)/wms/stockModel/stockModelForm',700,750);
        });

        $("#export").on('click',function () {
            var type=$("#stockTypeId").val();
            window.location.href="#(ctxPath)/wms/stockModel/stockModelExport?type="+type;
        });
    });
</script>

#end