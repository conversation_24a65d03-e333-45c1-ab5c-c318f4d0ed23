#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<div class="layui-row layui-col-space10">
    <div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>类型</legend>
            <div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
        </fieldset>
    </div>
    <div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
        <div style="margin-bottom: 20px;"></div>
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">上级类型</label>
                <div class="layui-input-block">
                    <input type="text" id="parentName" style="width: 44%;display: inline-block;" class="layui-input" value="#(parentTypeName??'顶级类型')" readonly="readonly">
                    <button class="layui-btn layui-btn-sm" id="setTopType" type="button">设置为顶级类型</button>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>类型名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" placeholder="请输入类型名称">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>字典类型</label>
                <div class="layui-input-block">
                    <select name="unitTypeId" lay-filter="" lay-verify="required">
                        <option value="">请选择字典类型</option>
                        #for(dictionary : dictionaryList)
                        <option value="#(dictionary.id)" #if(dictionary.id==model.unitTypeId??)selected #end>#(dictionary.name)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">是否可用</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnabled" value="1" title="可用" #if(model==null || model.isEnabled??=='1') checked #end>
                    <input type="radio" name="isEnabled" value="0" title="不可用" #if(model.isEnabled??=='0') checked #end>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <input type="text" name="description" class="layui-input" value="#(model.description??)" placeholder="请输入描述">
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-left">
                    <div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
                </div>
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(model.Id??)">
                    <input type="hidden" id="parentId" name="parentId" value="#(model.parentId??'')">
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use([ 'form' ], function() {
        var form = layui.form
            , layer = layui.layer
        ;

        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/main/goodType/goodTypeTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#parentId").val(treeNode.id);
                    $("#parentName").val(treeNode.name);
                }
            }
        };
        
        $("#setTopType").on('click',function () {
            $("#parentName").val("顶级类型");
            $("#parentId").val("");
        });

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/goodType/saveGoodType',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.tableGridReload();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end