#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置编辑#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/dictionaries/dictionarieTypesSave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#form").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload('dictionariesTable');
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 0px;margin-top: 0px;" id="form">
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label">类型名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" lay-verify="required" value="#(type.name??)" placeholder="请输入字典类型名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-inline">
            <input type="radio" name="isEnabled" value="1" title="是" #if(type==null || type.isEnabled??=='1') checked #end >
            <input type="radio" name="isEnabled" value="0" title="否" #if(type.isEnabled??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="description" placeholder="请输入内容" class="layui-textarea">#(type.description??)</textarea>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id"  value="#(type.id??)">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
