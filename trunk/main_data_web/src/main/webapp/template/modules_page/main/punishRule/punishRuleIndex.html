#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()违约规则管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        punishRuleLoad(null);

        sd=form.on("submit(search)",function(data){
            punishRuleLoad(data.field);
            return false;
        });

        function punishRuleLoad(data){
            table.render({
                id : 'punishRuleTable'
                ,elem : '#punishRuleTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/main/punishRule/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'punishName', title: '处罚名称', align: 'center', unresize: true}
                    ,{field:'punishType', title: '处罚类型', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.punishType,'punish_type','- -') }}</div>"}
                    ,{field:'exceedStartDay', title: '超限开始天数', align: 'center', unresize: true}
                    ,{field:'exceedEndDay', title: '超限结束天数', align: 'center', unresize: true}
                    ,{field:'deductType', title: '扣除类型', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.deductType,'deduct_way','- -') }}</div>"}
                    ,{field:'gruelRule', title: '处罚规则', align: 'center', unresize: true,templet:"<div>#[[{{#if(d.gruelRule==='0'){}} 按固定值 {{#}else if(d.gruelRule==='1'){}} 按百分比 {{#}else{}}- -{{#}}}]]#</div>"}
                    ,{field:'deductValue', title: '扣除值', align: 'center', unresize: true,templet:"<div>#[[{{#if(d.gruelRule==='1'){}} {{d.deductValue}}%  {{#}else{}} {{d.deductValue}} {{#}}}]]#</div>"}
                    ,{field:'maxDeductValue', title: '封顶值', align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/punishRule/form" ;
            pop_show("新增处罚规则",url,500,600);
        });

        table.on('tool(punishRuleTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/punishRule/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('punishRuleTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/punishRule/form?id=" + obj.data.id ;
                pop_show("编辑处罚规则",url,500,600);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:punishRule:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:punishRule:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            规则名称:
            <div class="layui-inline">
                <input id="punishName" name="punishName" class="layui-input">
            </div>
            <!--&nbsp;&nbsp;
            设备类型:
            <div class="layui-inline">
                <select name="equipmentType" lay-search>
                    <option value="">请选择设备类型</option>
                    #for(b : typeList)
                    <option value="#(b.dictValue)">#(b.dictName)</option>
                    #end
                </select>
            </div>-->
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("main:punishRule:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="punishRuleTable" lay-filter="punishRuleTable"></table>
</div>
#getDictLabel("punish_type")
#getDictLabel("deduct_way")
#end