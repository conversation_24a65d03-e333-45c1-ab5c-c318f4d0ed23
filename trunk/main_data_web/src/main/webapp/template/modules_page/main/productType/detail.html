#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()商品分类#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form layui-form-pane" action="" id="frm">
        <div class="layui-form-item">
            <label class="layui-form-label" ><span>*</span>名称</label>
            <div class="layui-input-block" >
                <input name="name" value="#(model.name??)" required lay-verify="required" placeholder="请输入名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" ><span>*</span>描述</label>
            <div class="layui-input-block" >
                <input name="describe" value="#(model.describe??)" required lay-verify="required" placeholder="请输入描述" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" ><span>*</span>分享页小图</label>
            <div class="layui-input-block" >
                <div class="layui-upload-drag" id="test10">
                    <i class="layui-icon"></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div #if(model.shareImg??==null) class="layui-hide" #end id="uploadDemoView">
                        <hr>
                        <img src="#(model.shareImg??)" alt="上传成功后渲染" style="max-width: 196px">
                    </div>
                </div>
            </div>
            <input type="hidden" id="shareImg" name="shareImg" value="#(model.shareImg??)" >
        </div>


        <div class="layui-form-item" style="margin-bottom: 60px;">
            <label class="layui-form-label" ><span>*</span>详情</label>
            <div class="layui-input-block" >
                <textarea class="" id="detailText"  > </textarea>

            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <input type="hidden" name="id" value="#(model.id??)">
                <input type="hidden" name="modelId" value="#(modelId??)">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" type="button" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保存</button>
            </div>
        </div>

        <textarea class="" id="detailText2" style="display: none;"  >#(model.detail??)</textarea>
    </form>
</div>
#end

#define js()
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.config({
        base: '/static/js/extend/tinymce/',
    });
    layui.use(['table','form','layedit','tinymce','layer','upload'],function(){
        var table,form,$ ;
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;
        var layedit=layui.layedit;
        var tinymce=layui.tinymce;
        var layer=layui.layer;
        var upload=layui.upload;


        //构建一个默认的编辑器
        //var index = layedit.build('detailText');

        tinymce.render({
            elem: "#detailText"
            , height: 500
            ,images_upload_url:"#(fileUploadUrl)?bucket=mallProductImg"
            ,language : "zh_CN"
            ,form:{
                name:'file'//配置上传文件的字段名称
                ,data:{ 'returnType':'2' }
            }
            // 支持tinymce所有配置
        },(opt, edit)=>{
            // 加载完成后回调 opt 是传入的所有参数
            // edit是当前编辑器实例，等同于t.get返回值
            #if(model!=null)
             edit.setContent($("#detailText2").val());
            #end
        });


        //拖拽上传
        upload.render({
            elem: '#test10'
            ,url: '#(fileUploadUrl)?bucket=mallType' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
            ,size:40
            ,done: function(res){
                if(res.state=='ok'){
                    layer.msg('上传成功');
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data.src);
                    $("#shareImg").val(res.data.src);
                }else{
                    window.top.layer.msg('上传失败', {icon: 2, offset: 'auto'});
                }
            }
        });


        // 保存护理等级信息
        form.on('submit(confirmBtn)',function(obj){
            layer.load();
            var content = tinymce.get('#detailText').getContent()

            var data=$("#frm").serialize()+"&detail="+encodeURIComponent(content);
            util.sendAjax({
                url:"#(ctxPath)/mall/productType/saveModelDetail",
                type:'post',
                data:data,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.reloadProductTableLoad();
                    }else{
                        layer.closeAll('loading');
                    }
                }
            });

            return false;
        }) ;

    }) ;
</script>
#end