#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        dictionariesLoad({"dictionaryTypeId":$("#typeId").val()});

        sd=form.on("submit(search)",function(data){
            dictionariesLoad({"dictionaryTypeId":$("#typeId").val()});
            return false;
        });

        dictionariesReLoad=function () {
            dictionariesLoad({"dictionaryTypeId":$("#typeId").val()});
        }
        
        function dictionariesLoad(data){
            table.render({
                id : 'dictionariesTable'
                ,elem : '#dictionariesTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/main/dictionaries/dictionariesPageList'
                ,cellMinWidth: 80
                ,cols: [[

                    {field:'dictionaryKey', title: '字典名称', align: 'center', unresize: true}
                    ,{field:'dictionaryOrder', title: '字典排序', align: 'center', unresize: true}
                    ,{field:'isEnabled', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnabled=='1'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled=='0'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                    ,{field:'description', title: '备注', align: 'center', unresize: true}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/dictionaries/dictionariesForm?typeId="+$("#typeId").val() ;
            pop_show("新增字典值",url,500,500);
        });

        table.on('tool(dictionariesTable)',function(obj){
            if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/dictionaries/dictionariesForm?id=" + obj.data.id+"&typeId="+$("#typeId").val() ;
                pop_show("编辑字字典值",url,500,350);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:functionSwitch:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <input type="hidden" id="typeId" value="#(typeId??)" >
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加字典类型</button>
    </div>
    <table id="dictionariesTable" lay-filter="dictionariesTable"></table>
</div>
#end