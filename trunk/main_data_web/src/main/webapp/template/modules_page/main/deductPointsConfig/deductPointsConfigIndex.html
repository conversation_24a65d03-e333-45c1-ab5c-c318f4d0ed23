#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地配置#end

#define css()
#end

#define content()
<div>
    <form class="layui-form" id="settingForm" style="width:90%;margin: 0 auto;">
        <div class="demoTable layui-row">
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" type="button" id="addBtn">添加</button>
            #shiroHasPermission("main:deductPointsConfig:saveBtn")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;"  lay-submit="" lay-filter="saveBtn">保存</button>
            #end

            <input name="baseId" type="hidden" value="#(baseId??)" >
            <input name="count" id="count" type="hidden" value="#(configList.size()??0)" >
            <input name="delIds" id="delIds" type="hidden" value="">
        </div>
        <table class="layui-table" id="baseAdvanceBookSettingTable" >
            <thead>
                <tr>
                    <th>时间范围</th>
                    <th>扣除点数(单价)</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="baseAdvanceBookSettingTbody">
                #for(config : configList)
                <tr id="settingTr-#(for.index+1)" index="#(for.index+1)">
                    <td><input name="configList[#(for.index+1)].startDate" autocomplete="off" lay-verify="required" class="layui-input" id="startDate-#(for.index+1)" value="#date(config.startDate??,'yyyy-MM-dd') - #date(config.endDate??,'yyyy-MM-dd')"></td>
                    <td><input name="configList[#(for.index+1)].deductPoints" autocomplete="off" lay-verify="required|number" class="layui-input" value="#(config.deductPoints??)"></td>
                    <td>
                        <input name="configList[#(for.index+1)].id" type="hidden" value="#(config.id)">
                        #shiroHasPermission("main:deductPointsConfig:delBtn")
                        <button class="layui-btn layui-btn-xs layui-btn-danger delBtn" type="button" onclick="del('#(config.id??)','settingTr-#(for.index+1)','#(for.index+1)')" >作废</button>
                        #end
                    </td>
                </tr>
                #end
            </tbody>
        </table>
    </form>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table','laytpl','laydate','element'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laytpl=layui.laytpl,laydate=layui.laydate,element=layui.element;

        var delBtnTips;
        $('.delBtn').hover(function(){
            var that = this;
            delBtnTips = layer.tips('作废后保存成功才会生效', this,{tips:[3],time:0}); //在元素的事件回调体中，follow直接赋予this即可
        },function () {
            layer.close(delBtnTips);
        });

        layDateRender=function(id,index,max,value){
            laydate.render({
                elem:"#"+id,
                trigger: 'click',
                range:true,
                min:max,
                value:value,
                isInitValue:false
            });
        }

        //循环渲染表格中已经存在行中的时间框，最后一个时间框不渲染
        /*for(var i=1;i<$("#count").val();i++){
            layDateRender("startDate-"+i,i);
        }*/
        var trs = $("#baseAdvanceBookSettingTbody").find("tr");
        trs.each(function (index,item) {
            if(index>0){
                var prevTrIndex=$(item).prev().attr("index")
                var currTrIndex=$(item).attr("index");
                var prevDateStr=$("#startDate-"+(prevTrIndex)).val();
                var currDateStr=$("#startDate-"+(currTrIndex)).val();
                var prevDateStrArray=prevDateStr.split(" - ");
                var currDateStrArray=currDateStr.split(" - ");
                var prevDate=prevDateStrArray[1];
                var prevDateNext=new Date(prevDate);
                prevDateNext=prevDateNext.setDate(prevDateNext.getDate()+1);
                layDateRender("startDate-"+currTrIndex,currTrIndex,dateFormat(prevDateNext,'yyyy-MM-dd'),'');
            }else{
                var currTrIndex=$(item).attr("index");
                layDateRender("startDate-"+currTrIndex,currTrIndex,'2000-01-01','');
            }
        });


        $("#addBtn").on('click',function () {
            //$("#baseAdvanceBookSettingTbody").find("tr[index]:last");
            //获取最后一行的index
            var trLastIndex=$("#baseAdvanceBookSettingTbody").find("tr:last").attr("index");
            if(trLastIndex===undefined){
                trLastIndex=0;
            }
            var idx=(Number(trLastIndex)+1);
            /*var startDate="01-01";
            //var endDate="12-31";*/
            var max='2000-01-01';
            var value='';
            if(idx>1){
                //获取
                var dateStr=$("#startDate-"+(Number(idx)-1)).val();
                var dateStrArray=dateStr.split(" - ");
                value=dateStrArray[1];
                var date=new Date(value);
                var date2=new Date(value);
                date=date.setDate(date.getDate()+1);
                max=date;
                date2=date2.setDate(date2.getDate()+2);
                value=dateFormat(date,'yyyy-MM-dd')+" - "+dateFormat(date2,'yyyy-MM-dd');
            }
            //table添加一行
            laytpl(settingTpl.innerHTML).render({"idx": idx}, function(html){
                $("#baseAdvanceBookSettingTbody").append(html);
                $("#count").val(idx);
                //console.log(max);
                layDateRender("startDate-"+idx,idx,max,value);
                //添加一行渲染上一行的时间框
                //layDateRender("endDate-"+(idx-1),(idx-1));
                //$("#endDate-"+(idx-1)).prop('disabled',false);

            });
            form.render();
            return false;
        });

        form.on('submit(saveBtn)',function () {
            //判断
            var trs = $("#baseAdvanceBookSettingTbody").find("tr");
            var flag=false;
            trs.each(function (index,item) {
                if(index>0){
                    var prevTrIndex=$(item).prev().attr("index")
                    var currTrIndex=$(item).attr("index");
                    var prevDateStr=$("#startDate-"+(prevTrIndex)).val();
                    var currDateStr=$("#startDate-"+(currTrIndex)).val();
                    var prevDateStrArray=prevDateStr.split(" - ");
                    var currDateStrArray=currDateStr.split(" - ");
                    var prevDate=prevDateStrArray[1];
                    var currDate=currDateStrArray[0];
                    var prevDateNext=new Date(prevDate);
                    prevDateNext=prevDateNext.setDate(prevDateNext.getDate()+1);
                    var cDate=new Date(currDate);
                    console.log(dateFormat(prevDateNext,'yyyy-MM-dd'));
                    console.log(dateFormat(cDate,'yyyy-MM-dd'));
                    if(dateFormat(prevDateNext,'yyyy-MM-dd')!=dateFormat(cDate,'yyyy-MM-dd')){
                        layer.msg('请添加连续的时间段', {icon: 2, offset: 'auto'});
                        flag=false;
                        return false;
                    }else{
                        flag=true;
                    }
                }
            });
            if(flag){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/deductPointsConfig/saveDeductPointsConfig',
                    data: $("#settingForm").serialize(),
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            //刷新当前tab
                            var src=parent.$(".layui-tab-item.layui-show").find("iframe").attr("src");
                            parent.$(".layui-tab-item.layui-show").find("iframe").attr("src",src);
                        }
                    },
                    complete : function() {
                    }
                });
            }

            return false;
        });

        del=function(id,trId,index) {
            var trLastIndex=$("#baseAdvanceBookSettingTbody").find("tr:last").attr("index");
            if(Number(index)===1){
                //删除的是第一条
                //见下一条的开始时间设置为01-01
                var nextIndex=$("#"+trId).next().attr("index");
                $("#startDate-"+nextIndex).val("01-01");

            }else if(Number(index)===Number(trLastIndex)){

                //删除的是最后一条
                //将上一条的结束时间设置为12-31
                var prevIndex=$("#"+trId).prev().attr("index");
                $("#endDate-"+prevIndex).val("12-31");

                //将上一个的时间框删除
                $("#endDate-"+prevIndex).prop('disabled',true);
            }else{
                //删除的是中间的一条
                //将下一条的开始时间设置为当前条的结束时间
                var startDate=$("#startDate-"+index).val();
                var nextIndex=$("#"+trId).next().attr("index");
                $("#startDate-"+nextIndex).val(startDate);
            }
            if(id!=''){
                var ids=$("#delIds").val();
                if(ids===''){
                    $("#delIds").val(id);
                }else{
                    $("#delIds").val(ids+","+id);
                }
            }
            $("#"+trId).remove();
        }
    });
</script>
<script type="text/html" id="settingTpl">
    <tr id="settingTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="configList[{{d.idx}}].startDate" class="layui-input" lay-verify="required" id="startDate-{{d.idx}}" lay-verify="startDate-{{d.idx}}" autocomplete="off" value=""></td>
        <td><input name="configList[{{d.idx}}].deductPoints" lay-verify="required|number" class="layui-input" value="" autocomplete="off"></td>
        <td>
            #shiroHasPermission("main:deductPointsConfig:delBtn")
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('','settingTr-{{d.idx}}','{{d.idx}}')" >作废</button>
            #end
        </td>
    </tr>
</script>
#end