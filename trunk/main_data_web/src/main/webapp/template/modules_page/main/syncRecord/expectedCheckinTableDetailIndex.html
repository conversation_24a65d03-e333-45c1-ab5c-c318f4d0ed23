#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'bedDynamicDetailTable'
            ,elem : '#bedDynamicDetailTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/main/syncRecord/baseExpectedCheckinDetailList'
            ,where:{'baseId':$("#baseId").val(),'date':$("#date").val()}
            ,cellMinWidth: 80
            ,cols: [[
                {field:'bedName', title: '床位',width:80, align: 'center', unresize: true}
                ,{field:'dynamicType', title: '动态类型',width:100, align: 'center', unresize: true,templet:"<div>#[[ {{#  if(d.dynamicType==='book'){ }} <span class=\"layui-badge layui-bg-green\">预定</span> {{#}else if(d.dynamicType==='checkin'){}} <span class=\"layui-badge layui-bg-blue\">入住</span> {{#  } else if(d.dynamicType==='checkout') { }} <span class=\"layui-badge layui-bg-orange\">退住</span> {{#  }else if(d.dynamicType==='cancelBook'){ }} <span class=\"layui-badge layui-bg-gray\">取消预定</span> {{#}}} ]]#</div>"}
                ,{field:'bookNo', title: '预定号', align: 'center', unresize: true}
                ,{field:'checkinNo', title: '入住号', align: 'center', unresize: true}
                ,{field:'memberName', title: '入住人',width:80, align: 'center', unresize: true}
                ,{field:'gender',title:'性别',align:'center',width:60,unresize:true,templet:"<div>#[[ {{#  if(d.gender==='Male'){ }} 男 {{#}else if(d.gender==='Female'){}} 女 {{#  } else { }} 未知 {{#  } }}]]#</div>"}
                ,{field:'isLongStay', title: '是否长住',width:90, align: 'center', unresize: true,templet:"<div>#[[{{#if(d.isLongStay===1){}} 是 {{#}else if(d.isLongStay===0){}} 否 {{#}else{}} - - {{#}}}]]#</div>"}
                ,{field:'companyName', title: '分公司', align: 'center', unresize: true}
                ,{field:'bookStartDate', title: '开始预定时间',width:120, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.bookStartDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'bookEndDate', title: '结束预定时间',width:120, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.bookEndDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'checkinDate', title: '入住时间',width:120, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.checkinDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'checkoutDate', title: '退住时间',width:120, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.checkoutDate,'yyyy-MM-dd') }}</div>"}
            ]]
            ,page : true
        });

    });
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            <input type="hidden" id="baseId" value="#(baseId??)" >
            <input type="hidden" id="date" value="#(date??)" >
        </form>
    </div>
    <table id="bedDynamicDetailTable" lay-filter="bedDynamicDetailTable"></table>
</div>
#end