#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="mattressTypeForm">
    <div class="layui-row">
        <input type="hidden" name="id" value="#(model.id??)"/>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"><font color="red">*</font>规则名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" placeholder="请输入规则名称" autocomplete="off">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label"><font color="red">*</font>是否可用</label>
                <div class="layui-input-block">
                    <input type="radio" name="isEnable" lay-verify="required" value="1" #if(model==null) checked #elseif(model.isEnable??=='1') checked #end  title="可用">
                    <input type="radio" name="isEnable" lay-verify="required" value="0" #if(model.isEnable??=='0') checked #end title="不可用">
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/markupScheme/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#mattressTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.mattressTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
