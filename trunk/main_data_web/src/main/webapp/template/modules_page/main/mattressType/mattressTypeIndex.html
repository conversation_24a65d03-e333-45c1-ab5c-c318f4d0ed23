#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床垫类型管理#end

#define css()
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            床垫名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
        </form>
        #shiroHasPermission("main:mattressType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="mattressTypeTable" lay-filter="mattressTypeTable"></table>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'mattressTypeTable'
            ,elem : '#mattressTypeTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/main/mattressType/findPageList'
            ,cellMinWidth: 80
            ,cols: [[
                {field: 'order', width:100, title: '序号',unresize:true}
                ,{field:'code', title: '床垫编码', align: 'center', unresize: true}
                ,{field:'name', title: '床垫名称', align: 'center', unresize: true}
                ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable=='1'?'<span class='layui-badge'>不可用</span>':'- -' }}</div>"}
                ,{field:'description', title: '备注', align: 'center', unresize: true}
                ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });

        table.on('tool(mattressTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/mattressType/delMattressType',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                mattressTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/main/mattressType/mattressTypeForm?id=" + obj.data.id ;
                pop_show("编辑床垫类型",url,500,500);
            }
        });

        mattressTypeTableReload=function(){
            table.reload('mattressTypeTable',{"where":{"name":$("#name").val()}});
        }

        // 搜索
        $("#search").click(function(){
            mattressTypeTableReload();
        });

        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/main/mattressType/mattressTypeForm" ;
            pop_show("新增床垫类型",url,500,500);
        });


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:mattressType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("main:mattressType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end
