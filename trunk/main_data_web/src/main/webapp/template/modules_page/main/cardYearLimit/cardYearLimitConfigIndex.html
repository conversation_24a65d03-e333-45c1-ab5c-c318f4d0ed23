#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡年限类型配置页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div id="layuiTab" class="layui-tab layui-tab-brief" lay-filter="layuiTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<a id="addBtn" class="layui-btn">添加</a>
				<table class="layui-table">
				<thead>
					<tr>
						<th>序号</th>
						<th>基地</th>
						<th>床位预订数量限制</th>
						<th>操作</th>
					</tr> 
				</thead>
				<tbody>
					#for(config : configList)
					<tr>
						<td>#(config.sort)</td>
						<td>#(config.baseName??)</td>
						<td>#(config.bedBookLimit??)</td>
						<td>
							<a class="layui-btn layui-btn-xs" onclick="edit('#(config.id??)')">编辑</a>
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('#(config.id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('layuiTab', {
			title: '添加'
			, content: '<div id="modelForm"></div>'
			, id: 'form'
		});
		$('#modelForm').load('#(ctxPath)/main/cardYearLimit/configForm?yearLimitId=#(yearLimitId??)');
		element.tabChange('layuiTab', 'form');
    });
    
    edit = function(id){
    	element.tabAdd('layuiTab', {
			title: '编辑'
			, content: '<div id="modelForm"></div>'
			, id: 'form'
		});
		$('#modelForm').load('#(ctxPath)/main/cardYearLimit/configForm?id='+id);
		element.tabChange('layuiTab', 'form');
    }
    
    del = function(id){
    	layer.confirm("确定要作废吗?",function(index){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/cardYearLimit/delCardYearLimitConfig',
                notice: true,
                data: {id:id, delFlag:'1'},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	location.reload();
                    }
                    layer.close(index);
                },
                complete : function() {
                }
            });
        });
    }
    
	element.on('tab(layuiTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end