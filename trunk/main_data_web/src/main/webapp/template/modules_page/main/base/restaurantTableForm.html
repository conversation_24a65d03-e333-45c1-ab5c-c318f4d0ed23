#include("/template/common/layout/_part_layout.html")
#@part()

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	form.render();
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('restaurantTab', 'form');
    }
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/base/saveBaseRestaurantTable',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		tableReload("restaurantTableTable",{restaurantId:$('#restaurantId').val()});
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>桌号</label>
			<div class="layui-input-block">
				<input type="text" name="tableNo" class="layui-input" lay-verify="required|number" value="#(model.tableNo??'')" placeholder="请输入桌号" maxlength="4" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>桌名称</label>
			<div class="layui-input-block">
				<input type="text" name="tableName" class="layui-input" lay-verify="required" value="#(model.tableName??'')" placeholder="请输入桌名称" maxlength="10" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>最多人数</label>
			<div class="layui-input-block">
				<input type="text" name="peopleNum" class="layui-input" lay-verify="required|number" value="#(model.peopleNum??'')" placeholder="请输入最多人数" maxlength="4" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>是否可预订</label>
			<div class="layui-input-block">
				<input type="radio" name="isBooking" value="0" title="否" #if(model.isBooking=='0') checked #end>
        		<input type="radio" name="isBooking" value="1" title="是" #if(model.isBooking??=='1'||model.isBooking??''=='') checked #end>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>是否有效</label>
			<div class="layui-input-block">
				<input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled=='0') checked #end>
        		<input type="radio" name="isEnabled" value="1" title="是" #if(model.isEnabled??=='1'||model.isEnabled??''=='') checked #end>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>状态</label>
			<div class="layui-input-block">
				<select id="tableStatus" name="tableStatus"lay-verify="required">
					#dictOption("table_status", model.tableStatus??'', "")
				</select>
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end