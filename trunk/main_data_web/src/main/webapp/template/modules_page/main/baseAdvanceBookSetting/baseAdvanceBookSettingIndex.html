#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地配置#end

#define css()
#end

#define content()
<div>
    <form class="layui-form" id="settingForm" style="width:90%;margin: 0 auto;">
        <div class="demoTable layui-row">
            #shiroHasPermission("main:baseSetting:addBtn")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" type="button" id="addBtn">添加</button>
            #end
            #shiroHasPermission("main:baseSetting:saveBtn")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;"  lay-submit="" lay-filter="saveBtn">保存</button>
            #end
            <input name="baseId" type="hidden" value="#(baseId??)" >
            <input name="count" id="count" type="hidden" value="#(bookSettingList.size()??0)" >
            <input name="delIds" id="delIds" type="hidden" value="">
        </div>
        <table class="layui-table" id="baseAdvanceBookSettingTable" >
            <thead>
                <tr>
                    <th><font color="red">*</font>开始日期</th>
                    <th><font color="red">*</font>结束日期</th>
                    <th><font color="red">*</font>在住人可提前预订天数</th>
                    <th><font color="red">*</font>空床时可提前预订天数</th>
                    <th><font color="red">*</font>不是空床时可提前预订天数</th>
                    <th><font color="red">*</font>误房费</th>
                    <th>预订到期时间距离要订房间的已有预订开始时间，时间小的优先</th>
                    <th>预订开始时间距离要订房间的已有预订或入住到期时间，时间大的优先</th>
                    <th>同房间已有其他床位的同住天数，多的优先</th>
                    <th>楼层，高层优先</th>
                    <th>床号，编号小优先</th>
                    <th>有随行则安排大床房</th>
                    <th><font color="red">*</font>淡旺季</th>
                    <th><font color="red">*</font>是否发送入住凭证</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="baseAdvanceBookSettingTbody">
                #for(bookSetting : bookSettingList)
                <tr id="settingTr-#(for.index+1)" index="#(for.index+1)">
                    <td><input name="settingList[#(for.index+1)].startDate" readonly lay-verify="required" class="layui-input startDate" id="startDate-#(for.index+1)" value="#(bookSetting.startDate??)"></td>
                    <td><input name="settingList[#(for.index+1)].endDate" lay-verify="required" readonly class="layui-input endDate"  id="endDate-#(for.index+1)" value="#(bookSetting.endDate??)"></td>
                    <td><input name="settingList[#(for.index+1)].checkedAdvance" lay-verify="required|number" class="layui-input" value="#(bookSetting.checkedAdvance??)"></td>
                    <td><input name="settingList[#(for.index+1)].emptyAdvance" lay-verify="required|number" class="layui-input" value="#(bookSetting.emptyAdvance??)"></td>
                    <td><input name="settingList[#(for.index+1)].notEmptyAdvance" lay-verify="required|number" class="layui-input" value="#(bookSetting.notEmptyAdvance??)"></td>
                    <td><input name="settingList[#(for.index+1)].roomDelayCost" lay-verify="required|number" class="layui-input" value="#(bookSetting.roomDelayCost??)"></td>

                    <td><input name="settingList[#(for.index+1)].BeginDate" lay-verify="double" class="layui-input" value="#(recordList[for.index].BeginDate??)"></td>
                    <td><input name="settingList[#(for.index+1)].EndDate" lay-verify="double" class="layui-input" value="#(recordList[for.index].EndDate??)"></td>
                    <td><input name="settingList[#(for.index+1)].RoomDays" lay-verify="double" class="layui-input" value="#(recordList[for.index].RoomDays??)"></td>
                    <td><input name="settingList[#(for.index+1)].Floor" lay-verify="double" class="layui-input" value="#(recordList[for.index].Floor??)"></td>
                    <td><input name="settingList[#(for.index+1)].BedNo" lay-verify="double" class="layui-input" value="#(recordList[for.index].BedNo??)"></td>
                    <td><input name="settingList[#(for.index+1)].HasEntourageBigBed" lay-verify="double" class="layui-input" value="#(recordList[for.index].HasEntourageBigBed??)"></td>
                    <td>
                        <input name="settingList[#(for.index+1)].peakPeriod" lay-verify="required" type="radio" title="淡季" value="0" #if(bookSetting.peakPeriod??=='0') checked #end>
                        <input name="settingList[#(for.index+1)].peakPeriod" lay-verify="required" type="radio" title="旺季" value="1" #if(bookSetting.peakPeriod??=='1') checked #end>
                    </td>
                    <td>
                        <input name="settingList[#(for.index+1)].isSendCheckinProve" lay-verify="required" type="radio" title="否" value="0" #if(bookSetting.isSendCheckinProve??=='0') checked #end>
                        <input name="settingList[#(for.index+1)].isSendCheckinProve" lay-verify="required" type="radio" title="是" value="1" #if(bookSetting.isSendCheckinProve??=='1') checked #end>
                    </td>
                    <td>
                        <input name="settingList[#(for.index+1)].id" type="hidden" value="#(bookSetting.id)">
                        #shiroHasPermission("main:baseSetting:delBtn")
                        <button class="layui-btn layui-btn-xs layui-btn-danger delBtn" type="button" onclick="del('#(bookSetting.id??)','settingTr-#(for.index+1)','#(for.index+1)')" >作废</button>
                        #end
                    </td>
                </tr>
                #end
            </tbody>
        </table>
    </form>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table','laytpl','laydate','element'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laytpl=layui.laytpl,laydate=layui.laydate,element=layui.element;

        var delBtnTips;
        $('.delBtn').hover(function(){
            var that = this;
            delBtnTips = layer.tips('作废后保存成功才会生效', this,{tips:[3],time:0}); //在元素的事件回调体中，follow直接赋予this即可
        },function () {
            layer.close(delBtnTips);
        });


        //自定义验证规则
        form.verify({
            double: function(value){
                if(value!=''){
                    //var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,4})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
                    var reg=/^(\-?(0|[1-9]\d{0,})((\.[0-9]{1,4})?))$/;
                    if (!reg.test(value)) {
                        return '请输入整数或包含4位小数点的数';
                    }

                }

            }
        });

        layDateRender=function(id,index){
            laydate.render({
                elem:"#"+id,
                format: 'MM-dd',
                trigger: 'click',
                done: function(value, date){
                    var year=date.year;
                    var month=date.month-1;
                    var day=date.date+1;
                    var mdate = new Date(year,month,day);
                    //月
                    var newMonth=(mdate.getMonth()+1);
                    newMonth+='';
                    if(newMonth.length===1){
                        newMonth="0"+newMonth;
                    }
                    //日
                    var newDate=mdate.getDate();
                    newDate+='';
                    if(newDate.length===1){
                        newDate="0"+newDate;
                    }
                    var nextIndex=$("#settingTr-"+index).next().attr("index");
                    $("#startDate-"+nextIndex).val(newMonth+"-"+newDate);
                }
            });
        }

        //循环渲染表格中已经存在行中的时间框，最后一个时间框不渲染
        for(var i=1;i<$("#count").val();i++){
            layDateRender("endDate-"+i,i);
        }


        $("#addBtn").on('click',function () {
            //$("#baseAdvanceBookSettingTbody").find("tr[index]:last");
            //获取最后一行的index
            var trLastIndex=$("#baseAdvanceBookSettingTbody").find("tr:last").attr("index");
            if(trLastIndex===undefined){
                trLastIndex=0;
            }
            var idx=(Number(trLastIndex)+1);
            var startDate="01-01";
            var endDate="12-31";
            if(idx>1){
                /*var date=new Date();
                // date.setMonth();
                var endDate=$("#endDate-"+$("#count").val()).val();
                var month=endDate.substring(0,2);
                var day=endDate.substring(3,5);
                date.setMonth(Number(month)-1);
                date.setMonth(Number(day)+1);
                var newMonth=date.getMonth()-1;
                var newDay=date.getDay()+1;
                //转成字符串
                newMonth+='';
                newDay+='';
                if(newMonth.length===1){
                    newMonth="0"+newMonth;
                }
                if(newDay.length===1){
                    newDay="0"+newDay;
                }*/
                //startDate=newMonth+"-"+newDay;
                startDate=$("#endDate-"+trLastIndex).val();
                endDate='12-31';
            }
            //table添加一行
            laytpl(settingTpl.innerHTML).render({"idx": idx,"startDate":startDate,"endDate":endDate}, function(html){
                $("#baseAdvanceBookSettingTbody").append(html);
                $("#count").val(idx);

                //添加一行渲染上一行的时间框
                layDateRender("endDate-"+(idx-1),(idx-1));
                $("#endDate-"+(idx-1)).prop('disabled',false);

            });
            form.render();
            return false;
        });

        form.on('submit(saveBtn)',function () {
            var data=$("#settingForm").serialize();
            console.log(data);
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/baseAdvanceBookSetting/saveBaseAdvanceBookSetting',
                data: data,
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state==='ok'){
                        //刷新当前tab
                        var src=parent.$(".layui-tab-item.layui-show").find("iframe").attr("src");
                        parent.$(".layui-tab-item.layui-show").find("iframe").attr("src",src);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        del=function(id,trId,index) {
            var trLastIndex=$("#baseAdvanceBookSettingTbody").find("tr:last").attr("index");
            if(Number(index)===1){
                //删除的是第一条
                //见下一条的开始时间设置为01-01
                var nextIndex=$("#"+trId).next().attr("index");
                $("#startDate-"+nextIndex).val("01-01");

            }else if(Number(index)===Number(trLastIndex)){

                //删除的是最后一条
                //将上一条的结束时间设置为12-31
                var prevIndex=$("#"+trId).prev().attr("index");
                $("#endDate-"+prevIndex).val("12-31");

                //将上一个的时间框删除
                $("#endDate-"+prevIndex).prop('disabled',true);
            }else{
                //删除的是中间的一条
                //将下一条的开始时间设置为当前条的结束时间
                var startDate=$("#startDate-"+index).val();
                var nextIndex=$("#"+trId).next().attr("index");
                $("#startDate-"+nextIndex).val(startDate);
            }
            if(id!=''){
                var ids=$("#delIds").val();
                if(ids===''){
                    $("#delIds").val(id);
                }else{
                    $("#delIds").val(ids+","+id);
                }
            }
            $("#"+trId).remove();
        }
    });
</script>
<script type="text/html" id="settingTpl">
    <tr id="settingTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="settingList[{{d.idx}}].startDate" readonly class="layui-input startDate" lay-verify="required" id="startDate-{{d.idx}}" lay-verify="startDate-{{d.idx}}"  value="{{d.startDate}}"></td>
        <td><input name="settingList[{{d.idx}}].endDate" lay-verify="required"#[[ {{#if(d.idx>0){}} readonly value="{{d.endDate}}" {{#}}} class="layui-input endDate" id="endDate-{{d.idx}}" ]]#></td>
        <td><input name="settingList[{{d.idx}}].checkedAdvance" lay-verify="required|number" class="layui-input" value=""></td>
        <td><input name="settingList[{{d.idx}}].emptyAdvance" lay-verify="required|number" class="layui-input" value=""></td>
        <td><input name="settingList[{{d.idx}}].notEmptyAdvance" lay-verify="required|number" class="layui-input" value=""></td>
        <td><input name="settingList[{{d.idx}}].roomDelayCost" lay-verify="required|number" class="layui-input" value=""></td>
        <td><input name="settingList[{{d.idx}}].BeginDate" lay-verify="double" class="layui-input" ></td>
        <td><input name="settingList[{{d.idx}}].EndDate" lay-verify="double" class="layui-input" ></td>
        <td><input name="settingList[{{d.idx}}].RoomDays" lay-verify="double" class="layui-input"></td>
        <td><input name="settingList[{{d.idx}}].Floor" lay-verify="double" class="layui-input" ></td>
        <td><input name="settingList[{{d.idx}}].BedNo" lay-verify="double" class="layui-input" ></td>
        <td><input name="settingList[{{d.idx}}].HasEntourageBigBed" lay-verify="double" class="layui-input"></td>
        <td>
            <div class="layui-input-inline">
                <input type="radio" name="settingList[{{d.idx}}].peakPeriod" lay-verify="required" title="淡季" value="0" checked>
                <input type="radio" name="settingList[{{d.idx}}].peakPeriod" lay-verify="required" title="旺季" value="1">
            </div>
        </td>
        <td>
            #shiroHasPermission("main:baseSetting:delBtn")
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('','settingTr-{{d.idx}}','{{d.idx}}')" >作废</button>
            #end
        </td>
    </tr>
</script>
#end