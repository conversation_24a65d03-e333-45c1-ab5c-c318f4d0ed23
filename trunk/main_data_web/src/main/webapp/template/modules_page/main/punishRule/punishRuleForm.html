#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()处罚规则编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
    layui.use([ 'form' ], function() {
        var form = layui.form
            , $ = layui.jquery
        ;

        //校验
        form.verify({
            checkAmount:function(value){
                if(value != null){
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if(!reg1.test(value) && !reg2.test(value)){
                        return "只能输入数字和小数点后两位小数";
                    }
                }
            },
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            }
        });

        form.on('radio(gruelRule)',function (obj) {
            console.log(obj.value);
            if(obj.value==='0'){
                $("#deductValueLabel").text('扣除值');
                $("#placeholder").attr("placeholder","请输入扣除值");
            }else if(obj.value==='1'){
                $("#deductValueLabel").text('扣除百分比');
                $("#placeholder").attr("placeholder","请输入扣除百分比");
            }
        })

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/punishRule/save',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("punishRuleTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<div class="layui-row">
    <form class="layui-form layui-form-pane">
        <div class="layui-form-item">
            <label class="layui-form-label">处罚名称</label>
            <div class="layui-input-block">
                <input type="text" name="pr.punishName" class="layui-input" lay-verify="required" value="#(pr.punishName??)" maxlength="50" placeholder="请输入处罚名称">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">处罚类型</label>
            <div class="layui-input-block">
                <select name="pr.punishType" lay-verify="required" lay-filter="">
                    <option value="">请选择处罚类型</option>
                    #dictOption("punish_type", pr.punishType??'', "")
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">处罚类型</label>
            <div class="layui-input-block">
                <select name="pr.deductType" lay-verify="required" lay-filter="">
                    <option value="">请选择扣除类型</option>
                    #dictOption("deduct_way", pr.deductType??'', "")
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">超限开始天数</label>
            <div class="layui-input-block">
                <input type="text" name="pr.exceedStartDay" class="layui-input" value="#(pr.exceedStartDay??)" lay-verify="checkNumber" placeholder="请输入超限开始天数">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">超限结束天数</label>
            <div class="layui-input-block">
                <input type="text" name="pr.exceedEndDay" class="layui-input" value="#(pr.exceedEndDay??)" lay-verify="checkNumber" placeholder="请输入超限结束天数">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">处罚规则</label>
            <div class="layui-input-block">
                <input type="radio" name="pr.gruelRule" lay-filter="gruelRule" value="0" title="按固定值" #if(pr==null || pr.gruelRule??=='0') checked #end>
                <input type="radio" name="pr.gruelRule" lay-filter="gruelRule" value="1" title="按百分比" #if(pr.gruelRule??=='1') checked  #end)>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" id="deductValueLabel">扣除值</label>
            <div class="layui-input-block">
                <input type="text" id="deductValue" name="pr.deductValue" class="layui-input" lay-verify="checkAmount|required" value="#(pr.deductValue??)" placeholder="请输入扣除值">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">封顶值</label>
            <div class="layui-input-block">
                <input type="text" id="maxDeductValue" name="pr.maxDeductValue" class="layui-input" lay-verify="checkAmount|required" value="#(pr.maxDeductValue??)" placeholder="请输入封顶值">
            </div>
        </div>

        <div class="layui-form-footer">
            <div class="pull-right">
                <input type="hidden" name="pr.id" value="#(pr.id??)">
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
        </div>
    </form>
</div>
#end