#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/layui/css/layui.css"  media="all">
<style>
    .layui-form-label {
        width: 130px !important;
        text-align: center !important;
    }
    .layui-input-block {
        margin-left: 130px !important;
    }
    .layui-select-disabled .layui-disabled {
        color: black!important;
        border-color: #eee!important;
    }
    input[type="text"]:disabled {
        background-color: #fff;
    }
    /*#roomTable .layui-body{overflow-y: scroll;}*/



    #roomTable .layui-table td,#roomTable .layui-table th {
        position: relative;
        padding: 9px 5px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }


</style>
#end

#define js()
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','laytpl','laydate','upload','table'],function(){
        var form = layui.form ;
        var $ = layui.$ ;
        var laytpl = layui.laytpl;
        var laydate=layui.laydate;
        var upload=layui.upload;
        var table=layui.table;




        form.on('select(baseId)',function (obj) {
            var baseId = obj.value;
            $('#buildingId').empty().append('<option value="">请选择楼栋</option>');
            $('#floorId').empty().append('<option value="">请选择楼层</option>');

            if (baseId) {
                layer.load();
                $.post('#(ctxPath)/api/getBuildingByBaseId',{'baseId':baseId},function (res) {
                    if("0"==res.code){
                        $.each(res.data,function (index,item) {
                            $('#buildingId').append('<option value="'+item.id+'">'+item.buildingName+'</option>');
                        })
                        form.render('select');
                    }
                    layer.closeAll('loading');
                })

                loadData(true);
            }
        })

        layui.event("form", "select(baseId)", {'value': "#(baseList[0].id)"});

        form.on('select(buildingId)',function (obj) {
            var buildingId = obj.value;
            $('#floorId').empty().append('<option value="">请选择楼层</option>');

            if (buildingId) {
                layer.load();
                $.post('#(ctxPath)/api/getFloorByBuildingId',{'buildingId':buildingId},function (res) {
                    if("0"==res.code){
                        $.each(res.data,function (index,item) {
                            $('#floorId').append('<option value="'+item.id+'">'+item.floorName+'</option>');
                        })
                        layer.closeAll('loading')
                    }
                    form.render('select');
                })
                loadData(false);
            }
        })

        form.on('select(floorId)',function (obj) {
            loadData(false);
        })

        form.on('select(roomTypeId)',function (obj) {
            loadData(false);
        })

        $("#search").on('click',function () {

        })

        function loadData(isRestRoomType){
            layer.load();
            $('#roomTbody').empty()
            $.post('#(ctxPath)/main/markupScheme/getBaseBed',{'baseId':$("#baseId").val(),'buildingId':$("#buildingId").val()
                ,'floorId':$("#floorId").val(),'roomTypeId':$("#roomTypeId").val(),'roomName':$("#roomName").val(),'schemeId':$("#schemeId").val()},function (res) {

                if("ok"==res.state){
                    const roomTypeMap = new Map();
                    $.each(res.data,function (index,item) {

                        let bigBed='否';
                        if(item.isBigBed=='1'){
                            bigBed='是'
                        }
                        var clonedSelect = $('#originalSelect').clone(true);

                        roomTypeMap.set(item.roomTypeId,item.roomTypeName);

                        $('#roomTbody').append(
                            `
                            <tr data-id="`+item.bedId+`">
                                <td style="text-align: center;">`+(index+1)+`</td>
                                <td>`+item.buildingName+`</td>
                                <td>`+item.floorName+`</td>
                                <td>`+item.roomTypeName+`</td>
                                <td>`+item.roomName+`</td>
                                <td>`+item.bedName+`</td>
                                <td>`+item.bedTypeName+`</td>
                                <td>`+bigBed+`</td>
                                <td>
                                    <div class="layui-input-inline" style="width: 170px;">
                                        <select name="markupType" id="markupType`+item.bedId+`">
                                        `
                            +$(clonedSelect).html()+
                            `
                                        </select>
                                    </div>
                                </td>
                            </tr>
                            `
                        );
                        if(item.markupTypeId!=undefined){
                            $('#markupType'+item.bedId).val(item.markupTypeId);
                        }
                    })
                    //$('#roomTypeId').empty();
                    if(isRestRoomType){
                        //更新房间类型数据
                        $('#roomTypeId').empty().append('<option value="">请选择房间类型</option>');
                        roomTypeMap.forEach((value, key) => {
                            $('#roomTypeId').append('<option value="'+key+'">'+value+'</option>');
                        });
                    }
                }
                layer.closeAll('loading')
                form.render('select');
            })
        }



        // 保存并提交
        form.on('submit(saveBtn)',function(obj){
            layer.load();

            let schemeId=$("#schemeId").val();

            if($("#roomTbody").find('tr').length==0){
                layer.msg('保存记录条数不能为0', {icon:5,time: 2000});
                layer.closeAll('loading');
                return false;
            }
            let dataArray=[];
            $.each($("#roomTbody").find('tr'),function (index,item) {
                let bedId= $(item).attr('data-id');
                let markupTypeId = $("#markupType"+bedId).val();
                dataArray.push({'bedId':bedId,'markupTypeId':markupTypeId});
            });

            util.sendAjax({
                url:"#(ctxPath)/main/markupScheme/schemeBaseBedMarkupType",
                type:'post',
                data:{'schemeId':schemeId,'dataArray':JSON.stringify(dataArray)},
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        layer.closeAll('loading');
                        //pop_close();
                        //parent.cardTypeTableReload(null);
                        loadData(false);
                    }
                },
                unSuccess:function (returnData) {
                    layer.closeAll('loading');
                }
            });
            return false;
        }) ;



    }) ;
</script>

#end

#define content()
<div class="my-btn-box">
    <form class="layui-form layui-form-pane" action="" id="frm">
        <div class="layui-row">
            <div class="layui-row">
                <div class="layui-inline">
                    <label class="layui-form-label">基地</label>
                    <div class="layui-input-inline">
                        <select id="baseId" lay-filter="baseId">
                            #for(base : baseList)
                            <option value="#(base.id??)">#(base.baseName??)</option>
                            #end
                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">楼栋</label>
                    <div class="layui-input-inline">
                        <select id="buildingId" lay-filter="buildingId" >

                        </select>
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label">楼层</label>
                    <div class="layui-input-inline" >
                        <select id="floorId" lay-filter="floorId">

                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label">房间类型</label>
                    <div class="layui-input-inline">
                        <select id="roomTypeId" lay-filter="roomTypeId" >

                        </select>
                    </div>
                </div>
                <!--<div class="layui-inline">
                    <label class="layui-form-label">房间</label>
                    <div class="layui-input-inline">
                        <select id="bedMarkupTypeSelect">
                            <option value="1">Option 1</option>
                            <option value="2">Option 2</option>
                            <option value="3">Option 3</option>
                        </select>
                    </div>
                </div>-->
                <input id="schemeId" name="schemeId" type="hidden" value="#(schemeId)">
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
            </div>
            <div class="layui-row" style="display: none;">
                <select id="originalSelect">
                    #for(bedMarkupType : bedMarkupTypeList)
                    <option value="#(bedMarkupType.id)">#(bedMarkupType.name)</option>
                    #end
                </select>
            </div>
            <table class="layui-table" style="overflow: auto;"  id="roomTable" >
                <!--<col width="30%">
                <col width="60%">
                <col width="10%">-->
                <thead>
                <tr>
                    <th style="text-align: center;"><font color="red"></font>序号</th>
                    <th ><font color="red"></font>楼栋</th>
                    <th ><font color="red"></font>楼层</th>
                    <th ><font color="red"></font>房间类型</th>
                    <th ><font color="red"></font>房间</th>
                    <th ><font color="red"></font>床位</th>
                    <th ><font color="red"></font>床位类型</th>
                    <th ><font color="red"></font>是否大床位</th>
                    <th ><font color="red"></font>选房费</th>

                </tr>
                </thead>
                <tbody id="roomTbody">

                </tbody>
            </table>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button type="button" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
#end