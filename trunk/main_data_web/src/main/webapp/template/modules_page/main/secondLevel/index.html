#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()车型管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;width: 100px;padding: 0 0px;margin-left: 20px;">二级分类名称</label>
            <div class="layui-input-inline">
                <input class="layui-input" name="name" id="name" placeholder="" autocomplete="off">
            </div>
            <label style="line-height: 40px;width: 70px;padding: 0 0px;margin-left: 20px;">一级分类</label>
            <div class="layui-input-inline">
                <select id="firstLevelId" name="firstLevelId">
                    <option value="">请选择一级分类</option>
                    #for(firstLevel:firstLevelList)
                    <option value="#(firstLevel.id)">#(firstLevel.name)</option>
                    #end
                </select>
            </div>

            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:bookkeepingType:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="carTypeTable" lay-filter="carTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'carTypeTable',
            elem: '#carTypeTable'
            ,url : '#(ctxPath)/main/secondLevel/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'name', title: '二级分类名称',unresize:true}
                ,{field: 'remark', title: '所属一级分类',unresize:true,templet: function (d){
                    return d.firstLevel.name;
                    }}
                ,{field: 'memberRoomDeductTimes', title: '会员包房扣费天数',unresize:true}
                ,{field: 'outsiderRoomDeductTimes', title: '散客包房扣费天数',unresize:true}
                ,{field: 'staffRoomDeductTimes', title: '员工包房扣费天数',unresize:true}
                ,{field: 'bookSort', title: '排房顺序',unresize:true}

                ,{field: 'isCalculateOccupancyRate', title: '是否计算入住率',unresize:true,templet:"<div>{{d.isCalculateOccupancyRate == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isCalculateOccupancyRate == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                //,{field: 'isEnable', title: '是否可预定',unresize:true,templet:"<div>{{d.isBooking == '1'? '<span class='layui-badge layui-bg-green'>可预定</span>':d.isBooking == '0'? '<span class='layui-badge'>不可预定</span>':'- -'}}</div>"}
                //,{field: 'isEnable', title: '是否大床房',unresize:true,templet:"<div>{{d.isBigBedRoom == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isBigBedRoom == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable == '0'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(carTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/secondLevel/form?id=" + obj.data.id ;
                layerShow("编辑二级分类",url,550,600);
            } else if (obj.event === 'del') {

            }
        });



        carTypeTableReload=function(){
            var name=$("#name").val();
            table.reload('carTypeTable',{'where':{'name':name,'firstLevelId':$("#firstLevelId").val()}});
        }

        $("#search").on('click',function () {
            carTypeTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/secondLevel/form";
            layerShow("添加二级分类",url,550,600);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:bookkeepingType:editBtn")
    #end
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>


</script>
#end