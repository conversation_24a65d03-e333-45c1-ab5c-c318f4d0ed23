#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游线路管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">

            <label style="line-height: 40px;padding: 0 0px;">线路名称：</label>
            <div class="layui-input-inline" style="margin-right: 40px;">
                <input class="layui-input" name="name" id="name" placeholder="" autocomplete="off">
            </div>

            <label style="line-height: 40px;">是否可用：</label>
            <div class="layui-input-inline">
                <select name="isEnable" id="isEnable">
                    <option value="">全部</option>
                    <option value="0" selected>是</option>
                    <option value="1">否</option>
                </select>
            </div>

            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:touristRoute:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="touristRouteTable" lay-filter="touristRouteTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'touristRouteTable',
            elem: '#touristRouteTable'
            ,url : '#(ctxPath)/main/touristRoute/findPageList'
            ,method : 'POST'
            ,where:{'isEnable':$("#isEnable").val()}
            ,height:$(document).height()*0.85
            ,cols: [[
                {field: 'order', width:100, title: '序号',unresize:true,align:'center'}
                ,{field: 'name', title: '线路名称',unresize:true}
                /*,{field: 'chargeDays', title: '收费天数',unresize:true}
                ,{field: 'amount', title: '收费金额',unresize:true}
                ,{field: 'points', title: '收费点数',unresize:true}*/
                ,{field: 'checkinDays', title: '入住天数（（含非基地入住））',unresize:true}
                ,{field: 'description', title: '描述',unresize:true}
                ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable == '1'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(touristRouteTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/touristRoute/form?id=" + obj.data.id ;
                layerShow("编辑车型",url,550,550);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?", function (index) {
                    util.sendAjax({
                        url: "#(ctxPath)/main/touristRoute/delTouristRoute",
                        type: 'post',
                        data: {"id": obj.data.id},
                        notice: true,
                        success: function (returnData) {
                            if (returnData.state === 'ok') {
                                touristRouteTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }else if(obj.event==='deduct'){

                var url = "#(ctxPath)/main/touristRoute/deductConfigIndex?id=" + obj.data.id ;
                layerShow("扣卡配置",url,1000,600);
            }
        });



        touristRouteTableReload=function(){
            var name=$("#name").val();
            table.reload('touristRouteTable',{'where':{'name':name,'isEnable':$("#isEnable").val()}});
        }

        $("#search").on('click',function () {
            touristRouteTableReload();
            return false;
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/touristRoute/form";
            layerShow("添加车型",url,550,550);
            return false;
        });
        $(document).keydown(function(event){
            if(event.keyCode==13){
                $("#search").click();
                return false;
            }
        });

    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:touristRoute:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    <a class="layui-btn layui-btn-xs" lay-event="deduct">扣卡配置</a>
    #shiroHasPermission("main:touristRoute:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end