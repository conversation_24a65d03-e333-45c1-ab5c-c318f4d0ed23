#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript">
    layui.use(['form','laytpl','table','element','layer'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;


        downFile=function(filePath) {
            var url = $("#commonUpload").val() + "/downFileByUrl?src=" + filePath;
            var eleLink = document.createElement('a');
            eleLink.style.display = 'none';
            eleLink.href = url;
            // 触发点击
            document.body.appendChild(eleLink);
            eleLink.click();
            // 然后移除
            document.body.removeChild(eleLink);
        }


        //展示文件模板
        showFileTpl = function (filePath, type, url) {
            var divStr =
                '<div style="width: 150px;height: 100px;margin: 5px;" onclick=\'downFile("' + filePath + '")\'>'
                + '<div style="text-align: center;"><i class="' + type + '"></i></div>'
                + '<div><span>' + url + '</span></div>'
                + '</div>'
            return divStr;
        }


        //获取文件表格和查阅人数据
        /*function loadFilesAndUsers() {
            if ($('#noticeId').val() != null && $('#noticeId').val() != '') {
                util.sendAjax({
                    url: '#(ctxPath)/pers/notice/getFilesAndUsers',
                    type: 'post',
                    data: {'id': $('#noticeId').val()},
                    notice: false,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            //处理文件
                            var fileList = returnData.fileList;
                            for (var i = 0; i < fileList.length; i++) {
                                var suffix = fileList[i].url.substring(fileList[i].url.indexOf("."), fileList[i].url.length);
                                var type = '';
                                var regImg = /(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/;//图片
                                var regExcel = /(.*)\.(xlsx|xls|xltx|xlt|xlsm|xlsb|xltm|csv)$/;//excel
                                var regWord = /(.*)\.(docx|doc)$/;//word
                                var regPdf = /(.*)\.(pdf|PDF)$/;//pdf
                                var regVideo = /(.*)\.(mp4|rmvb|flv|mpeg|avi)$/;//视频
                                var regZip = /(.*)\.(zip|rar)$/;//压缩包
                                if (regImg.test(suffix)) {
                                    type = 'fa fa-file-image-o fa-lg';
                                } else if (regExcel.test(suffix)) {
                                    type = 'fa fa-file-excel-o fa-lg';
                                } else if (regWord.test(suffix)) {
                                    type = 'fa fa-file-word-o fa-lg';
                                } else if (regPdf.test(suffix)) {
                                    type = 'fa fa-file-pdf-o fa-lg';
                                } else if (regVideo.test(suffix)) {
                                    type = 'fa fa-file-video-o fa-lg';
                                } else if (regZip.test(suffix)) {
                                    type = 'fa fa-file-archive-o fa-lg';
                                } else {
                                    type = 'fa fa-sticky-note-o fa-lg';
                                }
                                var title = fileList[i].url.substring(fileList[i].url.lastIndexOf("/") + 1,fileList[i].url.length);
                                if(title != null && title.length > 15){
                                    title = "..."+title.substring(title.length-15,title.length);
                                }
                                $("#fileShow").append(showFileTpl(fileList[i].url, type, title));
                            }
                        }
                    }
                });
            }
        }*/

        /*$(function () {
            loadFilesAndUsers();
        });*/
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="border: none;background-color: #F2F2F2;">
    <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
    <input type="hidden" id="msgId" name="msg.id" value="#(msg.id??)"/>
    <div class="layui-row">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
            <div class="layui-row">
                <div class="layui-card">
                    <div class="layui-card-header" style="height:50% !important;">
                        <div style="text-align: left;"><h2>#(msg.title??)</h2></div>
                        <div style="text-align: left;color: #9F9F9F;">#date(msg.createTime??,'yyyy-MM-dd HH:mm:ss')</div>
                    </div>
                </div>
                <hr>
                <div class="layui-card">
                    <div class="layui-card-body" style="min-height:250px;" id="showContent">#(msg.content??)</div>
                </div>
                <!--<div class="layui-card">
                    <div class="layui-card-header">附件</div>
                    <div class="layui-card-body" id="fileShow" style="min-height:100px;display: flex;flex-wrap:wrap;"></div>
                </div>-->
            </div>
        </form>
    </div>
</div>
</body>
#end