#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()应用信息接入管理#end

#define css()
#end

#define content()
<div>
	<div class="demoTable layui-row">
		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
			应用名称:
			<div class="layui-inline">
				<input id="appName" name="appName" class="layui-input">
			</div>
			&nbsp;&nbsp;
			应用编号:
			<div class="layui-inline">
				<input id="appNo" name="appNo" class="layui-input">
			</div>
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
		</form>
		#shiroHasPermission("main:app:addBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
		#end
		#shiroHasPermission("main:app:batchDelBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
		#end
	</div>
	<table id="appTable" lay-filter="appTable"></table>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

	appTableLoad(null);
	
	sd=form.on("submit(search)",function(data){
		appTableLoad(data.field);
		return false;
	});
	
	
	function appTableLoad(data){
		table.render({
			id : 'appTable'
			,elem : '#appTable'
			,method : 'POST'
			,where : data
			,height:$(document).height()*0.85
			,limit : 15
			,limits : [15,30,45,50]
			,url : '#(ctxPath)/main/appJoin/findListPage'
			,cellMinWidth: 80
			,cols: [[
				{type:'checkbox'},
				{type: 'numbers', width:100, title: '序号',unresize:true}
				,{field:'appName', title: '应用名称', align: 'center', unresize: true}
				,{field:'appNo', title: '应用编号', align: 'center', unresize: true}
				,{field:'appid', title: 'appid', align: 'center', unresize: true}
				,{field:'secret', title: 'secret', align: 'center', unresize: true}
				,{field:'status', title: '状态', align: 'center', unresize: true,templet:"<div>{{ d.status=='1'?'<span class='layui-badge layui-bg-green'>启用</span>':d.status=='0'?'<span class='layui-badge'>禁用</span>':'- -' }}</div>"}
				,{field:'appUrl', title: '应用地址', align: 'center', unresize: true}
				,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
				,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
		});
	};
	// 添加
	$("#add").click(function(){
		$(this).blur();
		var url = "#(ctxPath)/main/appJoin/form" ;
		pop_show("新增应用",url,800,500);
	});

	table.on('tool(appTable)',function(obj){
		if (obj.event === 'del') {
			layer.confirm("确定要作废吗?",function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/appJoin/delete',
					data: {id:obj.data.id},
					notice:true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							table.reload('appTable');
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else if(obj.event === 'edit'){
			var url = "#(ctxPath)/main/appJoin/form?id=" + obj.data.id ;
			pop_show("编辑应用",url,800,500);
		}
	});

	//批量获取被作废数据
	getCheckTableData = function(){
		var memberCheckStatus = table.checkStatus('appTable');
		// 获取选择状态下的数据
		return memberCheckStatus.data;
	}

	//批量作废
	$("#batchDel").click(function(){
		layer.confirm("确定批量作废吗?",function(index){
			var jsonData=getCheckTableData();
			if(jsonData == null || jsonData == ''){
				layer.msg('请勾选作废数据', function () {});
				return;
			}
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/appJoin/batchDel',
				data: {appData:JSON.stringify(jsonData)},
				notice: true,
				loadFlag: true,
				success : function(rep){
					if(rep.state=='ok'){
						table.reload('appTable');
					}
				},
				complete : function() {
				}
			});
			layer.close(index);
		});
	});
});
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("main:app:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("main:app:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
#end