#include("/template/common/layout/_page_layout.html")

#@layout()

#define pageTitle()公共门编辑#end

#define css()

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<div class="layui-form-item">
			<table class="layui-table" lay-size="sm">
				<thead>
					<tr>
						<th width="20%">序号</th>
						<th width="60%">公共门</th>
						<th width="20%">操作</th>
					</tr>
				</thead>
				<tbody id="roomDoorList">
					#for(roomDoor : roomDoorList)
					<tr id="roomDoor-#(for.index+1)">
						<td><input type="text" name="roomDoorList[#(for.index+1)].doorSort" class="layui-input" value="#(roomDoor.doorSort??)" lay-verify="required" placeholder="请输入序号" autocomplete="off"></td>
						<td align="left">
							<select id="doorSelect-#(for.index+1)" name="roomDoorList[#(for.index+1)].doorId" lay-verify="required" lay-search="">
								<option value="">直接选择或搜索选择</option>
								#for(door : doorList)
									<option value="#(door.id)" #if(door.id==roomDoor.doorId)selected="selected"#end>#(door.doorName)</option>
								#end
							</select>
						</td>
						<td>
							<input type="hidden" name="roomDoorList[#(for.index+1)].id" value="#(roomDoor.id??)">
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('roomDoor-#(for.index+1)','#(roomDoor.id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="doorCount" name="doorCount" value="#(roomDoorList.size()??)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<div id="addDoorBtn" class="layui-btn">添加公共门</div>
				<button id="saveBtn" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script id="roomDoorTrTpl" type="text/html">
	<tr id="roomDoor-{{d.idx}}">
		<td><input type="text" name="roomDoorList[{{d.idx}}].doorSort" class="layui-input" value="" lay-verify="required" placeholder="请输入序号" autocomplete="off"></td>
		<td>
			<select id="doorSelect-{{d.idx}}" name="roomDoorList[{{d.idx}}].doorId" lay-verify="required" lay-search="">
				<option value="">直接选择或搜索选择</option>
			</select>
		</td>
		<td>
			<input type="hidden" name="roomDoorList[{{d.idx}}].id" value="">
			<input type="hidden" name="roomDoorList[{{d.idx}}].roomId" value="{{d.roomId}}">
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('roomDoor-{{d.idx}}','')">作废</a>
		</td>
	</tr>
</script>
<script>
layui.use(['form','layer','laytpl'], function() {
	var $ = layui.$, form=layui.form,layer=layui.layer, laytpl = layui.laytpl;

	//添加模板方法
	addTpl = function(targetId, addTpl, idx, roomId) {
		$('#doorCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1), "roomId":roomId}, function(html){
			targetId.append(html);
		});
		$.post("#(ctx)/main/baseDoor/doorList", {baseId:'#(baseId??)'}, function(doorList) {
			if(doorList.length>0){
				$.each(doorList, function(i, door){
					$("#doorSelect-"+(parseInt(idx)+1)).append('<option value="'+door.id+'">'+door.doorName+'</option>');
				});
			}
			form.render('select');
		});
    };
    
	//删除方法
	del = function(trId, dataId) {
		if(dataId!=null && dataId!=''){
			layer.confirm('您确定要作废？', {icon: 3, title:'询问'}, function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/baseDoor/delRoomDoor',
					data: {id:dataId},
					notice: true,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							$("#"+trId).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else{
			$("#"+trId).remove();
		}
	};
    
	form.on('submit(saveBtn)',function (obj) {
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/baseDoor/saveRoomDoor',
			data: $(obj.form).serialize(),
			notice: true,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					pop_close();
				}
			},
			complete : function() {
			}
		});
	
		return false;
	});

	//添加按钮点击事件
	$('#addDoorBtn').on('click', function() {
		addTpl($('#roomDoorList'), roomDoorTrTpl.innerHTML, $('#doorCount').val(), '#(roomId??)');
	});
});
</script>
#end