#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>手牌类别</label>
		<div class="layui-input-block">
			<select id="handCardCategory" name="handCardCategory"lay-verify="required">
				#statusOption(com.cszn.integrated.service.entity.status.HandCardCategory::me(), model.handCardCategory??"")
			</select>
		</div>
	</div>
    <div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>卡id</label>
		<div class="layui-input-block">
			<input type="text" name="cardId" class="layui-input" lay-verify="required" value="#(model.cardId??'')" placeholder="请输入卡id" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>卡编号</label>
		<div class="layui-input-block">
			<input type="text" name="cardNo" class="layui-input" lay-verify="required" value="#(model.cardNo??'')" placeholder="请输入卡编号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>是否有效</label>
		<div class="layui-input-block">
			<input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled=='0') checked #end>
       		<input type="radio" name="isEnabled" value="1" title="是" #if(model.isEnabled??=='1'||model.isEnabled??''=='') checked #end>
		</div>
	</div>
	<div class="layui-form-item">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="remarks" placeholder="请输入备注" class="layui-textarea">#(model.remarks??)</textarea>
        </div>
    </div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<div class="layui-btn-group">
				<input type="hidden" name="id" value="#(model.id??)">
				#if(com.jfinal.kit.StrKit::isBlank(model.id))
					<input type="hidden" name="baseId" value="#(model.baseId??'')">
				#end
				<input type="hidden" name="oldCardId" value="#(model.cardId??'')">
				<input type="hidden" name="oldCardNo" value="#(model.cardNo??'')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	form.render();
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('layuiTab', 'form');
    }
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/base/handCardSave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		tableReload("handCardTable",{baseId:'#(model.baseId??)'});
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end