#include("/template/common/layout/_page_layout.html")

#@layout()

#define pageTitle()卡类型积分配置#end

#define css()

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<div class="layui-form-item">
			<table class="layui-table" lay-size="sm">
				<thead>
					<tr>
						<th width="20%">配置名称</th>
						<th width="20%">开始值</th>
						<th width="20%">结束值</th>
						<th width="20%">比例值</th>
						<th width="20%">操作</th>
					</tr>
				</thead>
				<tbody id="configList">
					#for(config : configList)
					<tr id="config-#(for.index+1)">
						<td>
							<input type="text" name="configList[#(for.index+1)].configName" class="layui-input" value="#(config.configName??)" lay-verify="required" placeholder="请输入配置名称" autocomplete="off">
						</td>
						<td>
							<input type="text" name="configList[#(for.index+1)].startValue" class="layui-input" value="#(config.startValue??)" lay-verify="required|number" placeholder="请输入开始值" autocomplete="off">
						</td>
						<td>
							<input type="text" name="configList[#(for.index+1)].endValue" class="layui-input" value="#(config.endValue??)" lay-verify="required|number" placeholder="请输入结束值" autocomplete="off">
						</td>
						<td>
							<input type="text" name="configList[#(for.index+1)].proportionValue" class="layui-input" value="#number(config.proportionValue??, '#.#####')" lay-verify="required|number" placeholder="请输入比例值" autocomplete="off">
						</td>
						<td>
							<input type="hidden" name="configList[#(for.index+1)].id" value="#(config.id??)">
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('config-#(for.index+1)','#(config.id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="configCount" name="configCount" value="#(configList.size()??)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<div id="addDoorBtn" class="layui-btn">添加配置</div>
				<button id="saveBtn" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script id="configTrTpl" type="text/html">
	<tr id="config-{{d.idx}}">
		<td>
			<input type="text" name="configList[{{d.idx}}].configName" class="layui-input" value="" lay-verify="required" placeholder="请输入配置名称" autocomplete="off">
		</td>
		<td>
			<input type="text" name="configList[{{d.idx}}].startValue" class="layui-input" value="" lay-verify="required|number" placeholder="请输入开始值" autocomplete="off">
		</td>
		<td>
			<input type="text" name="configList[{{d.idx}}].endValue" class="layui-input" value="" lay-verify="required|number" placeholder="请输入结束值" autocomplete="off">
		</td>
		<td>
			<input type="text" name="configList[{{d.idx}}].proportionValue" class="layui-input" value="" lay-verify="required|number" placeholder="请输入比例值" autocomplete="off">
		</td>
		<td>
			<input type="hidden" name="configList[{{d.idx}}].id" value="">
			<input type="hidden" name="configList[{{d.idx}}].cardTypeId" value="{{d.cardTypeId}}">
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('config-{{d.idx}}','')">作废</a>
		</td>
	</tr>
</script>
<script>
layui.use(['form','layer','laytpl'], function() {
	var $ = layui.$, form=layui.form,layer=layui.layer, laytpl = layui.laytpl;

	//添加模板方法
	addTpl = function(targetId, addTpl, idx, cardTypeId) {
		$('#configCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1), "cardTypeId":cardTypeId}, function(html){
			targetId.append(html);
		});
    };
    
	//删除方法
	del = function(trId, dataId) {
		if(dataId!=null && dataId!=''){
			layer.confirm('您确定要作废？', {icon: 3, title:'询问'}, function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/main/cardtype/delConfig',
					data: {id:dataId, delFlag:'1'},
					notice: true,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							$("#"+trId).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else{
			$("#"+trId).remove();
		}
	};
    
	form.on('submit(saveBtn)',function (obj) {
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/cardtype/saveConfig',
			data: $(obj.form).serialize(),
			notice: true,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					pop_close();
				}
			},
			complete : function() {
			}
		});
	
		return false;
	});

	//添加按钮点击事件
	$('#addDoorBtn').on('click', function() {
		addTpl($('#configList'), configTrTpl.innerHTML, $('#configCount').val(), '#(cardTypeId??)');
	});
});
</script>
#end