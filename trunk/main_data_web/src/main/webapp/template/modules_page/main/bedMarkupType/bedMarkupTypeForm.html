#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()选房费表单#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 55px;margin-top: 20px;" id="bedMarkupTypeForm">
    <input type="hidden" id="bedMarkupTypeId" name="bedMarkupType.id" value="#(bedMarkupType.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">价格名称</label>
        <div class="layui-input-inline">
            <input type="text" name="bedMarkupType.name" class="layui-input" lay-verify="required" value="#(bedMarkupType.name??)" placeholder="请输入标记类型名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">价格</label>
        <div class="layui-input-inline">
            <input type="text" name="bedMarkupType.amount" class="layui-input" lay-verify="required|number" value="#(bedMarkupType.amount??)" placeholder="请输入价格">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-inline">
            <select id="isEnable" name="bedMarkupType.isEnable" lay-verify="required">
                <option value="0" #(bedMarkupType != null ?(bedMarkupType.isEnable == '0' ? 'selected':''):'')>可用</option>
                <option value="1" #(bedMarkupType != null ?(bedMarkupType.isEnable == '1' ? 'selected':''):'')>不可用</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left: 100px; margin-top: 90px;">
        <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/bedMarkupType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#bedMarkupTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("bedMarkupTypeTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
