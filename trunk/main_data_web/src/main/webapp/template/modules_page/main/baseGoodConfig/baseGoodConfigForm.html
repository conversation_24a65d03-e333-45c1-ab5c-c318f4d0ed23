#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置编辑#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/baseGoodConfig/saveConfig";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#form").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.carTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 0px;margin-top: 0px;" id="form">
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;">基地</label>
        <div class="layui-input-block">
            <select id="baseId" name="baseId" lay-verify="required" >
                <option value="">请选择基地</option>
                #for(base : baseList)
                <option value="#(base.id)" #if(base.id==config.baseId??) selected #end>#(base.baseName)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;">商品型号</label>
        <div class="layui-input-block">
            <select id="goodModelId" name="goodModelId" lay-verify="required" >
                <option value="">请选择商品型号</option>
                #for(model : modelList)
                <option value="#(model.id)" #if(model.id==config.goodModelId??) selected #end>#(model.name)</option>
                #end
            </select>
        </div>
    </div>

    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label">是否可扣卡</label>
        <div class="layui-input-block">
            <input type="radio" name="isBuckleCard" value="1" title="是" #if(config.isBuckleCard??=='1') checked #end >
            <input type="radio" name="isBuckleCard" value="0" title="否" #if(config==null || config.isBuckleCard??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label">扣卡天数</label>
        <div class="layui-input-block">
            <input type="text" name="cardTimes" class="layui-input" lay-verify="required|number" value="#(config.cardTimes??0.0)" placeholder="请输入扣卡天数">
        </div>
    </div>

    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label">是否核销</label>
        <div class="layui-input-block">
            <input type="radio" name="isWriteOff" value="1" title="是" #if(config.isWriteOff??=='1') checked #end >
            <input type="radio" name="isWriteOff" value="0" title="否" #if(config==null || config.isWriteOff??=='0') checked #end>
        </div>
    </div>

    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;">排序</label>
        <div class="layui-input-block">
            <input type="text" name="baseGoodsConfigOrder" lay-verify="required|number" value="#(config.baseGoodsConfigOrder??)" placeholder="请输入排序" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnabled" value="1" title="是" #if(config==null || config.isEnabled??=='1') checked #end >
            <input type="radio" name="isEnabled" value="0" title="否" #if(config.isEnabled??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="description" placeholder="请输入内容" class="layui-textarea">#(config.description??)</textarea>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id"  value="#(config.id??)">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
