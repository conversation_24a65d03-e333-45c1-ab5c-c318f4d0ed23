#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="bookChannelForm">
    <div class="layui-row">
        <input type="hidden" name="bookChannel.id" value="#(bookChannel.id??)"/>
        <!--<div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">序号</label>
                <div class="layui-input-inline">
                    <input type="text" name="mattressType.order" class="layui-input" lay-verify="required|number" value="#(mattressType.order??)" placeholder="请输入序号">
                </div>
            </div>
        </div>-->
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 120px">预订渠道名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="bookChannel.name" class="layui-input" lay-verify="required" value="#(bookChannel.name??)" placeholder="请输入预订渠道名称">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 120px">预订渠道编号</label>
                <div class="layui-input-inline">
                    <select name="bookChannel.dictValue" lay-verify="required">
                        <option value="">请选择预订渠道编号</option>
                        #for(code : bookChannelCode)
                        <option value="#(code.key)" #if(bookChannel.dictValue??==code.key) selected #end >#(code.value)</option>
                        #end
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 120px">是否可用</label>
                <div class="layui-input-inline">
                    <select id="isEnable" name="bookChannel.isEnable" lay-verify="required">
                        <option value="0" #(bookChannel != null ?(bookChannel.isEnable == '0' ? 'selected':''):'')>可用</option>
                        <option value="1" #(bookChannel != null ?(bookChannel.isEnable == '1' ? 'selected':''):'')>不可用</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/bookChannel/saveBookChannel";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#bookChannelForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.bookChannelTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
