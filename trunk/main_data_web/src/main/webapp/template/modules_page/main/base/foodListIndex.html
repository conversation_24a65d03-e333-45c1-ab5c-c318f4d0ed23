#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地菜标签页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<div id="baseFoodTab" class="layui-tab layui-tab-brief" lay-filter="baseFoodTab">
			<ul class="layui-tab-title">
				<li id="list" class="layui-this">列表</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<div class="layui-row">
						<form id="frm" class="layui-form" lay-filter="layform" action="" method="post">
							<div class="layui-inline">
								<label class="layui-form-label">菜标签</label>
								<div class="layui-input-inline" style="width:150px;">
									<select id="tagId" name="tagId" lay-search>
										<option value="">全部</option>
										#for(tag : foodTagList)
											<option value="#(tag.id)")>#(tag.tagName)</option>
										#end
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">菜名称</label>
								<div class="layui-input-inline">
									<input id="foodName" name="foodName" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">是否有效</label>
								<div class="layui-input-inline" style="width:100px;">
									<select id="isEnabled" name="isEnabled">
										<option value="">全部</option>
										<option value="0">否</option>
										<option value="1">是</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">是否上架</label>
								<div class="layui-input-inline" style="width:100px;">
									<select id="isShelf" name="isShelf">
										<option value="">全部</option>
										<option value="0">否</option>
										<option value="1">是</option>
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<input type="hidden" id="baseId" name="baseId" value="#(baseId??)">
								<button type="button" id="search"  class="layui-btn" lay-submit="" lay-filter="search">查询</button>
								<button type="button" id="addBtn" class="layui-btn">添加</button>
							</div>
						</form>
					</div>
					<div class="layui-row">
						<table id="foodTable" lay-filter="foodTable"></table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:delBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</div>
</script>
<script type="text/html" id="isShelfTpl">
  <!-- 这里的 checked 的状态只是演示 -->
  <input type="checkbox" name="isShelf" value="{{d.id}}" lay-skin="switch" lay-text="上架|下架" lay-filter="isShelfFilter" {{ d.isShelf=='1' ? 'checked' : '' }}>
</script>
 
<script type="text/html" id="isEnabledTpl">
  <!-- 这里的 checked 的状态只是演示 -->
  <input type="checkbox" name="isEnabled" value="{{d.id}}" lay-skin="switch" lay-text="有效|无效" lay-filter="isEnabledFilter" {{ d.isEnabled=='1' ? 'checked' : '' }}>
</script>
<script type="text/javascript">
layui.use([ 'table', 'form', 'element' ], function() {
	var table = layui.table
	, form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
	function foodLoad(data){
		table.render({
			id : 'foodTable'
			,elem : '#foodTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/main/base/foodPage'
// 			,cellMinWidth: 80
			,height:'full-150'
			,cols: [[
				{field: 'foodSort', title: '序号', width: 60, unresize:true}
				,{field:'tagName', title: '菜标签', align: 'center', unresize: true}
				,{field:'foodName', title: '菜名称', align: 'center', unresize: true}
				,{field:'', title: '类型', align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.FoodConfigType::me(), "configType")'}
				,{field:'costPrice', title: '成本价', align: 'center', unresize: true}
				,{field:'salesPrice', title: '销售价', align: 'center', unresize: true}
				,{field: '', title: '是否上架', width: 120,unresize:true,templet:'#isShelfTpl'}
				,{field: '', title: '是否有效', width: 120,unresize:true,templet:'#isEnabledTpl'}
				,{fixed:'right', title: '操作', width:120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 15
			,limits : [15,25,35,45]
			, loading: true
		    , done: function (res, curr, count) {
		    }
		});
		table.on('tool(foodTable)',function(obj){
			if(obj.event==="edit"){//编辑按钮事件
				element.tabAdd('baseFoodTab', {
					title: '编辑'
					, content: '<div id="foodListForm"></div>'
					, id: 'form'
				});
				$('#foodListForm').load('#(ctxPath)/main/base/foodListForm?id='+obj.data.id);
				element.tabChange('baseFoodTab', 'form');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm("确定要作废吗?",function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/main/base/delBaseFood',
	                    notice: true,
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                        	tableReload("foodTable",{baseId:$('#baseId').val()});
	                        }
	                        layer.close(index);
	                    },
	                    complete : function() {
	                    }
	                });
	            });
	        }
		});
		//监听上架/下架操作
		form.on('switch(isShelfFilter)', function(obj){
			var id = this.value;
			var dataFlag = '';
			if(obj.elem.checked){
				dataFlag = '1';
			}else{
				dataFlag = '0';
			}
			var data = {id:id, isShelf:dataFlag};
			updateBaseFood(data);
		});
		//监听有效/无效操作
		form.on('switch(isEnabledFilter)', function(obj){
			var id = this.value;
			var dataFlag = '';
			if(obj.elem.checked){
				dataFlag = '1';
			}else{
				dataFlag = '0';
			}
			var data = {id:id, isEnabled:dataFlag};
			updateBaseFood(data);
		});
	};
	
	foodLoad({baseId:'#(baseId??)'});
	
	form.on("submit(search)",function(data){
		foodLoad(data.field);
		return false;
	});
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('baseFoodTab', {
			title: '添加'
			, content: '<div id="foodListForm"></div>'
			, id: 'form'
		});
		$('#foodListForm').load('#(ctxPath)/main/base/foodListForm?baseId=#(baseId??)');
		element.tabChange('baseFoodTab', 'form');
    });
    
    function updateBaseFood(data){
   		//提交表单数据
   		util.sendAjax ({
               type: 'POST',
               url: '#(ctxPath)/main/base/saveBaseFood',
               data: data,
               notice: true,
   		    loadFlag: true,
               success : function(rep){
               	if(rep.state=='ok'){
               		tableReload("foodTable",{baseId:$('#baseId').val()});
               	}
               },
               complete : function() {
   		    }
           });
    }
    
	element.on('tab(baseFoodTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end