#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'syncRecordTable'
            ,elem : '#syncRecordTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/main/syncRecord/findSyncRecordByAppNo'
            ,where:{'appNo':$("#app").val(),'status':$("#status").val()}
            ,cellMinWidth: 80
            ,cols: [[
                {type:'checkbox'},
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field:'syncType', title: '同步类型', align: 'center', unresize: true}
                ,{field:'dataType', title: '数据类型', align: 'center', unresize: true}
                ,{field:'syncUrl', title: '同步地址', align: 'center', unresize: true}
                ,{field:'syncData', title: '同步数据', align: 'center', unresize: true}
                ,{field:'syncStatus', title: '同步状态', align: 'center', unresize: true,templet:"<div>{{ d.syncStatus=='0'?'<span class='layui-badge layui-bg-green'>成功</span>':d.syncStatus=='1'?'<span class='layui-badge'>失败</span>':'- -' }}</div>"}
                ,{field:'createBy', title: '创建人', align: 'center', unresize: true}
                ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });

        table.on('tool(syncRecordTable)',function (obj) {
            if(obj.event==='sync'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/syncRecord/sendSyncRecord',
                    data: {'id':obj.data.id},
                    notice: false,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            syncRecordTableReload();
                            layer.msg(rep.msg,{icon:6,time:2000},function(index){});
                        }else{
                            layer.msg(rep.msg,{icon:5,time:30000},function(index){});
                        }
                    },
                    complete : function() {
                    }
                });
            }else if(obj.event==='del'){
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/main/syncRecord/delSyncRecord',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                syncRecordTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });

        syncRecordTableReload=function(){
            table.reload('syncRecordTable',{"where":{'appNo':$("#app").val(),'syncType':$("#syncType").val(),'syncStatus':$("#syncStatus").val(),'syncData':$("#syncData").val()}});
        }

        form.on('select',function (obj) {
            syncRecordTableReload();
        });

        $("#syncData").on('blur',function () {
            syncRecordTableReload();
        })


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:syncRecord:syncBtn")
    #[[
    {{#if(d.syncStatus==='1'){}}
    <a class="layui-btn layui-btn-xs" lay-event="sync">同步</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("main:syncRecord:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            <div class="layui-inline">
                应用:
                <div class="layui-inline">
                    <select id="app" lay-filter="app">
                        #for(app:appJoinList)
                        <option value="#(app.appNo)">#(app.appName)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                数据类型:
                <div class="layui-inline">
                    <select id="syncType">
                        <option value="">全部</option>
                        #for(type : syncTypes)
                        <option value="#(type.key)">#(type.value)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                状态:
                <div class="layui-inline">
                    <select id="syncStatus" lay-filter="syncStatus">
                        <option value="">全部</option>
                        <option value="0">成功</option>
                        <option value="1">失败</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-inline">
                    <input class="layui-input" id="syncData" name="syncData">
                </div>
            </div>

        </form>
    </div>
    <table id="syncRecordTable" lay-filter="syncRecordTable"></table>
</div>
#end