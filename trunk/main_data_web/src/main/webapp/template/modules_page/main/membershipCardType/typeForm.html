#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别展示页面#end

#define css()
<style>
	.layui-form-label {
		width: 130px !important;
		text-align: center !important;
	}
	.layui-input-block {
		margin-left: 130px !important;
	}
	.inMax{
		width:150px;
	}
</style>
#end

#define js()
<script type="text/javascript">
	layui.use('form',function(){
		var form = layui.form ;
		var $ = layui.$ ;

		//校验
		form.verify({
			checkNumber:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
					if(value.length != 7 && value.length != 8){
						return "号段固定为7位或8位";
					}
				}
			},
			checkPrefix:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
					if(value.length != 2){
						return "卡号前缀固定为2位";
					}
				}
			}
		});

		// 保存护理等级信息
		form.on('submit(confirmBtn)',function(){
			util.sendAjax({
				url:"#(ctxPath)/main/cardtype/save",
				type:'post',
				data:$("#frm").serialize(),
				notice:true,
				success:function(returnData){
					if(returnData.state==='ok'){
						pop_close();
						parent.layui.table.reload('typeTable');
					}
				}
			});
			return false;
		}) ;
	}) ;
</script>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<div class="layui-row">
		<form class="layui-form layui-form-pane" action="" id="frm">
			<input type="hidden" name="officeId" value="1">
			<input type="hidden" name="type.id" value="#(type.id??)">
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>类别类型</label>
				<div class="layui-input-block">
					<select name="type.typeCategory" lay-verify="required">
						<option value="">请选择</option>
						#dictOption("type_category", type.typeCategory??'', "")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>类别分类</label>
				<div class="layui-input-block">
					<select name="type.typeClassify" lay-verify="required">
						<option value="">请选择</option>
						#dictOption("type_classify", type.typeClassify??'', "")
					</select>
				</div>
			</div>
<!-- 			<div class="layui-form-item"> -->
<!-- 				<label class="layui-form-label"><font color="red">*</font>年限</label> -->
<!-- 				<div class="layui-input-block"> -->
<!-- 					<select id="yearLimit" name="type.yearLimit" lay-verify="required"> -->
<!-- 						<option value="">请选择年限</option> -->
<!-- 						#for(cardYearLimit : cardYearLimitList) -->
<!-- 							<option value="#(cardYearLimit.dictValue)" #if(type.yearLimit??==cardYearLimit.dictValue) selected #end>#(cardYearLimit.name)</option> -->
<!-- 						#end -->
<!-- 					</select> -->
<!-- 				</div> -->
<!-- 			</div> -->
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>类别名称</label>
				<div class="layui-input-block">
					<input type="text" name="type.cardType" autocomplete="off" placeholder="请输入名称"
						   lay-verify="required" value="#(type.cardType??)" class="layui-input">
				</div>
			</div>
<!-- 			<div class="layui-form-item"> -->
<!-- 				<label class="layui-form-label"><font color="red">*</font>最大预订数量</label> -->
<!-- 				<div class="layui-input-block"> -->
<!-- 					<input type="number" name="type.bookMax" min="0" autocomplete="off" placeholder="请输入最大预订数量" -->
<!-- 						   lay-verify="required" value="#(type.bookMax??)" class="layui-input"> -->
<!-- 				</div> -->
<!-- 			</div> -->
<!-- 			<div class="layui-form-item"> -->
<!-- 				<label class="layui-form-label"><font color="red">*</font>最大入住数量</label> -->
<!-- 				<div class="layui-input-block"> -->
<!-- 					<input type="number" name="type.checkinMax"  min="0" autocomplete="off" placeholder="请输入最大入住数量" -->
<!-- 						   lay-verify="required" value="#(type.checkinMax??)" class="layui-input"> -->
<!-- 				</div> -->
<!-- 			</div> -->
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 0px;"><span>*</span>是否开通金额</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isBalance" value="1" title="是" #(type != null ? ('1' == type.isBalance ? 'checked':''):'checked')>
					<input type="radio" name="type.isBalance" value="0" title="否" #(type != null ? ('0' == type.isBalance ? 'checked':''):'')>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否开通天数</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isConsumeTimes" value="1" title="是" #(type != null ? ('1' == type.isConsumeTimes ? 'checked':''):'checked')>
					<input type="radio" name="type.isConsumeTimes" value="0" title="否" #(type != null ? ('0' == type.isConsumeTimes ? 'checked':''):'')>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否开通点数</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isConsumePoints" value="1" title="是" #(type != null ? ('1' == type.isConsumePoints ? 'checked':''):'checked')>
					<input type="radio" name="type.isConsumePoints" value="0" title="否" #(type != null ? ('0' == type.isConsumePoints ? 'checked':''):'')>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否滚动积分</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isIntegral" value="1" title="是" #(type != null ? ('1' == type.isIntegral ? 'checked':''):'checked')>
					<input type="radio" name="type.isIntegral" value="0" title="否" #(type != null ? ('0' == type.isIntegral ? 'checked':''):'')>
				</div>
			</div>
<!-- 			<div class="layui-form-item"> -->
<!-- 				<label class="layui-form-label"><font color="red">*</font>是否消费卡</label> -->
<!-- 				<div class="layui-input-block"> -->
<!-- 					<input type="radio" name="type.isExpend" value="1" title="是" #(type != null ? ('1' == type.isExpend ? 'checked':''):'checked')> -->
<!-- 					<input type="radio" name="type.isExpend" value="0" title="否" #(type != null ? ('0' == type.isExpend ? 'checked':''):'')> -->
<!-- 				</div> -->
<!-- 			</div> -->
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>是否可预订</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isBooking" value="1" title="是" #(type != null ? ('1' == type.isBooking ? 'checked':''):'checked')>
					<input type="radio" name="type.isBooking" value="0" title="否" #(type != null ? ('0' == type.isBooking ? 'checked':''):'')>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 0px;font-size: 12px;"><font color="red">*</font>是否可充值豆豆券</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isRechargeBean" value="1" title="是" #(type != null ? ('1' == type.isRechargeBean ? 'checked':''):'checked')>
					<input type="radio" name="type.isRechargeBean" value="0" title="否" #(type != null ? ('0' == type.isRechargeBean ? 'checked':''):'')>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>不发验证码</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isNotSendSms" value="1" title="是" #(type != null ? ('1' == type.isNotSendSms ? 'checked':''):'')>
					<input type="radio" name="type.isNotSendSms" value="0" title="否" #(type != null ? ('0' == type.isNotSendSms ? 'checked':''):'checked')>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>不签入住协议</label>
				<div class="layui-input-block">
					<input type="radio" name="type.isNotSignAgreement" value="1" title="是" #(type != null ? ('1' == type.isNotSignAgreement ? 'checked':''):'')>
					<input type="radio" name="type.isNotSignAgreement" value="0" title="否" #(type != null ? ('0' == type.isNotSignAgreement ? 'checked':''):'checked')>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>校验卡天数、金额</label>
				<div class="layui-input-inline">
					<input type="radio" name="type.isVerifyTimes" value="1" title="是" #(type != null ? ('1' == type.isVerifyTimes ? 'checked':''):'checked')>
					<input type="radio" name="type.isVerifyTimes" value="0" title="否" #(type != null ? ('0' == type.isVerifyTimes ? 'checked':''):'')>
				</div>

			</div>
<!-- 			<fieldset class="layui-elem-field" style="margin-top:30px;"> -->
<!-- 				<legend style="font-size: 14px;margin-bottom:30px;"><b>会员卡规则</b></legend> -->
<!-- 				<div class="layui-form-item"> -->
<!-- 					<label class="layui-form-label"><font color="red">*</font>前缀</label> -->
<!-- 					<div class="layui-input-inline"> -->
<!-- 						<input type="text" name="type.cardPrefix" class="layui-input" lay-verify="required|checkPrefix" value="#(type.cardPrefix??)" placeholder="请输入会员卡前缀"> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 				<div class="layui-form-item"> -->
<!-- 					<label class="layui-form-label"><font color="red">*</font>号段</label> -->
<!-- 					<div class="layui-input-inline"> -->
<!-- 						<input type="text" name="type.cardMin" class="layui-input" lay-verify="required|checkNumber" value="#(type.cardMin??)" placeholder="会员卡号段最小值" style="width:150px;"> -->
<!-- 					</div> -->
<!-- 					<div style="float:left;margin-left:-35px;margin-top:8px;">至</div> -->
<!-- 					<div class="layui-input-inline"> -->
<!-- 						<input type="text" name="type.cardMax" class="layui-input inMax" lay-verify="required|checkNumber" value="#(type.cardMax??)" placeholder="会员卡号段最大值"> -->
<!-- 					</div> -->
<!-- 				</div> -->
<!-- 			</fieldset> -->
			<div class="layui-form-item" style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button id="confirmBtn" type="button" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
				</div>
			</div>
		</form>
	</div>

</div>
#end