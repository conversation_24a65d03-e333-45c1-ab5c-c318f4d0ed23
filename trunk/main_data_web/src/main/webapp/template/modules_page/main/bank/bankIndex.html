#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()银行信息页面#end

#define css()
<style>

</style>
#end

#define content()
<div class="layui-collapse ">
    <div class="layui-row" style="margin-top:23px;">
	    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
	        <label class="layui-form-label">银行名称</label>
	        <div class="layui-input-inline">
	            <input type="text" id="bankName" name="bankName" class="layui-input">
	        </div>
	        <button id="search" class="layui-btn" type="button">搜索</button>
	        #shiroHasPermission("bankInfo:btn:add")
	        <button class="layui-btn" type="button" id="add">添加</button>
	        #end
	    </form>
    </div>
    <div class="layui-row">
        <table class="layui-table" id="bankTable" lay-filter="bankTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
    #shiroHasPermission("bankInfo:btn:edit")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("bankInfo:btn:del")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">作废</a>
    #end
</script>
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'bankTable',
            elem: '#bankTable'
            ,url : '#(ctxPath)/main/bank/pageTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'bankName', title: '银行名称',unresize:true}
                ,{field:'', title: '创建时间', width: 180, sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{title: '操作', width: 120,toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(bankTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/bank/edit?id=" + obj.data.id ;
                layerShow("编辑公共门",url,550,300);
            } else if (obj.event === 'delete') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/main/bank/delete",
                        type:'post',
                        data:{"id":obj.data.id, "delFlag":"1"},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                bankTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });

        bankTableReload=function(){
            table.reload('bankTable',{'where':{'bankName':$("#bankName").val()}});
        }

        $("#search").on('click',function () {
            bankTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/bank/add";
            layerShow("添加",url,550,300);
        });


    }) ;
</script>
#end