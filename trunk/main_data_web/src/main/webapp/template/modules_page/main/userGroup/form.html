#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户分组表单#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="modelForm">
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>分组编号</label>
            <div class="layui-input-block">
                <input type="text" name="groupCode" value="#(model.groupCode??)" autocomplete="off" placeholder="请输入类型名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>分组名称</label>
            <div class="layui-input-block">
                <input type="text" name="groupName" value="#(model.groupName??)" autocomplete="off" placeholder="请输入类型名称" class="layui-input" lay-verify="required">
            </div>
        </div>
<!--        <div class="layui-form-item">-->
<!--            <label class="layui-form-label"><font color="red">*</font>是否可用</label>-->
<!--            <div class="layui-input-block">-->
<!--                <select name="isEnable" id="isEnable">-->
<!--                    <option value="">请选择</option>-->
<!--                    <option value="1" #if(model==null || model.isEnable??=='1') selected #end>可用</option>-->
<!--                    <option value="0" #if(model.isEnable??=='0') selected #end>不可用</option>-->
<!--                </select>-->
<!--            </div>-->
<!--        </div>-->
        <div class="layui-form-item">
            <label class="layui-form-label">关联用户</label>
            <div class="layui-input-block">
                <div id="relatedUserXmSelectTree" class="xm-select-demo" hiddenTargetId="relatedUser" dataValue="#(relatedUser??)"></div>
            </div>
        </div>
<!--        <div class="layui-form-item">-->
<!--            <label class="layui-form-label"><font color="red">*</font>排序</label>-->
<!--            <div class="layui-input-block">-->
<!--                <input type="text" name="sort" value="#(model.sort??)" autocomplete="off" placeholder="请输入类型名称" class="layui-input" lay-verify="required|number">-->
<!--            </div>-->
<!--        </div>-->
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark"  placeholder="请输入备注" class="layui-textarea">#(model.remark??'')</textarea>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <input name="id" type="hidden" value="#(model.id??)" />
                <input type="hidden" id="relatedUser" name="relatedUser" value="#(relatedUser??)">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.use(['form', 'laydate','jquery'],function(){
        var form = layui.form;
        var $ = layui.$;

        relatedUserSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
            var relatedUserXmSelectCheck = xmSelect.render({
                el: '#' + elem
                , prop: {
                    name: 'name'
                    , value: 'id'
                }
                , model: {label: {type: 'text'}}
                , toolbar: {show: true}
                , filterable: true
                , direction: 'down'
                , tips: '无'
                , tree: {
                    show:true
                    , strict: false
                }
                // , layVerify:'required'
                // , height:'300px'
                , autoRow: true
                , on: function (data) {
                    var dataArray = data.arr;//data.arr:  当前多选已选中的数据
                    if (data.change) {//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                        if (dataArray.length > 0) {
                            var dArray = new Array();
                            for (var i = 0; i < dataArray.length; i++) {
                                dArray.push(dataArray[i].id);
                            }
                            $('#' + hiddenTargetId).val(dArray.toString());
                        } else {
                            $('#' + hiddenTargetId).val('');
                        }
                    }
                }
            });
            $.post('#(ctxPath)/user/findUnlockUserList', {}, function (rep) {
                if (rep.state == 'ok') {
                    var initValueArray = null;
                    if (dataValue.indexOf(",") != -1) {
                        initValueArray = dataValue.split(',');
                        relatedUserXmSelectCheck.update({
                            data: rep.userList
                            , initValue: initValueArray
                        })
                    } else {
                        relatedUserXmSelectCheck.update({
                            data: rep.userList
                            , initValue: [dataValue]
                        })
                    }
                }
            });
        };

        $('div[class="xm-select-demo"]').each(function (i, obj) {
            var targetId = obj.id;
            var hiddenTargetId = $(obj).attr('hiddenTargetId') != null ? $(obj).attr('hiddenTargetId') : '';
            var dataValue = $(obj).attr('dataValue') != null ? $(obj).attr('dataValue') : '';
            if(targetId=='relatedUserXmSelectTree'){
                relatedUserSelectCheckLoad(targetId, hiddenTargetId, dataValue);
            }
        });

        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/main/userGroup/saveUserGroup',
                data: $("#modelForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end