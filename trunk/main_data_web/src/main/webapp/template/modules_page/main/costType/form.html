#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()合同类别表单#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form layui-form-pane" action="" id="frm">

<!--         <div class="layui-form-item"> -->
<!--             <label class="layui-form-label" style="width: 130px"><span>*</span>合同编号</label> -->
<!--             <div class="layui-input-block" style="margin-left: 130px"> -->
<!--                 <input name="typeNo" value="#(contractType.typeNo??)" required lay-verify="required" placeholder="请输入合同编号" autocomplete="off" class="layui-input"> -->
<!--             </div> -->
<!--         </div> -->
        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>类型</label>
            <div class="layui-input-block">
                <select name="type" lay-verify="required">
                    <option value="">请选择</option>
                    #for(type : typeMap)
                    <option value="#(type.key)" #if(type.key==costType.type??) selected #end>#(type.value)</option>
                    #end
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="width: 130px"><span>*</span>费用类型名称</label>
            <div class="layui-input-block" style="margin-left: 130px">
                <input name="name" value="#(costType.name??)" required lay-verify="required" placeholder="请输入类型名称" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>排序</label>
            <div class="layui-input-block">
                <input name="sort" value="#(costType.sort??)" required lay-verify="required|number" placeholder="请输入排序" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>是否可用</label>
            <div class="layui-input-block">
                <input type="radio" name="isEnabled" value="1" lay-verify="required" title="可用" #if(costType!=null && costType.isEnabled=='1') checked #elseif(costType==null) checked #end>
                <input type="radio" name="isEnabled" value="0" lay-verify="required" title="不可用" #if(costType.isEnabled??=='0') checked #end>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入内容" class="layui-textarea">#(costType.remark??)</textarea>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <input type="hidden" name="id" value="#(costType.id??)">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" type="button" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保存</button>
            </div>
        </div>
    </form>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        // 保存护理等级信息
        form.on('submit(confirmBtn)',function(obj){
            util.sendAjax({
                url:"#(ctxPath)/main/costType/saveCostType",
                type:'post',
                data:obj.field,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.contractTypeTable();
                    }
                }
            });
            return false;
        }) ;

    }) ;
</script>
#end