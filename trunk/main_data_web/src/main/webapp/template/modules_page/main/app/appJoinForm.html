#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()应用信息接入展示#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<div class="layui-row" style="margin-bottom:50px;">
		<form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="appForm">
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>应用名称</label>
				<div class="layui-input-block">
					<input type="text" id="appName" name="app.appName" value="#(app != null ?app.appName:'')" autocomplete="off" placeholder="请输入应用名称" class="layui-input" lay-verify="required">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>应用编号</label>
				<div class="layui-input-block">
					<input type="text" id="appNo" name="app.appNo" value="#(app != null ?app.appNo:'')" autocomplete="off" placeholder="请输入应用编号" class="layui-input" lay-verify="required">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>appid</label>
				<div class="layui-input-block">
					<input type="text" id="appid" name="app.appid" value="#(app != null ?app.appid:'')" autocomplete="off" placeholder="" class="layui-input" lay-verify="required" readonly>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>secret</label>
				<div class="layui-input-block">
					<input type="text" id="secret" name="app.secret" value="#(app != null ?app.secret:'')" autocomplete="off" placeholder="" class="layui-input" lay-verify="required" readonly>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>状态</label>
				<div class="layui-input-block">
					<select id="status" name="app.status" lay-verify="required">
						<option value="1" #(app != null ?(app.status == '1' ? 'selected':''):'')>启用</option>
						<option value="0" #(app != null ?(app.status == '0' ? 'selected':''):'')>禁用</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>IP白名单</label>
				<div class="layui-input-inline" style="width: 470px;">
					<!--<input type="text" id="ipAddr" name="app.ipAddr" value="#(app != null ?app.ipAddr:'')" autocomplete="off" placeholder="请输入IP白名单" class="layui-input" lay-verify="required">-->
					<select xm-select="ipAddr" id="ipAddr" lay-verify="required"></select>
				</div>
				<input type="text" class="layui-input" id="optIpAddr" value="" style="width: 160px;"/>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>应用地址</label>
				<div class="layui-input-block">
					<input type="text" id="appUrl" name="app.appUrl" value="#(app != null ?app.appUrl:'')" autocomplete="off" placeholder="请输入应用地址" class="layui-input" lay-verify="required">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<input type="text" name="app.remarks" value="#(app != null ?app.remarks:'')" autocomplete="off" placeholder="请输入备注" class="layui-input" lay-verify="required">
				</div>
			</div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<input name="app.id" id="id" type="hidden" value="#(app != null ? app.id : '')" />
					<input type="hidden" name="app.ipAddr" id="ipAddrs" value="#(app.ipAddr??)"/>
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
				</div>
			</div>
		</form>
	</div>

</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"/>
<script src="layui.all.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script type="text/javascript">
var formSelects = layui.formSelects;
layui.use(['form', 'laydate', 'upload','jquery'],function(){
	var form = layui.form;
	var $ = layui.$;
	formSelects.render('ipAddr');
	var arr = [];

	$(function(){
		if($("#id").val() != null && $("#id").val() != ''){
			var ipArr = $("#ipAddrs").val().split(",");
			if(ipArr != null && ipArr.length > 0){
				for(var i=0;i<ipArr.length;i++){
					arr.push(ipArr[i]);
					$("#ipAddr").append("<option value='" + ipArr[i] + "'>" + ipArr[i] + "</option>");
				}
				formSelects.render('ipAddr');
				formSelects.value('ipAddr',ipArr);
			}
		}
	});

	//保存
	form.on('submit(confirmBtn)', function(obj){
		var ipAddrArr = formSelects.value('ipAddr', 'val');
		$("#ipAddrs").val(ipAddrArr.join(','));
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/appJoin/save',
			data: $("#appForm").serialize(),
			notice: true,
			loadFlag: true,
			success : function(rep){
				if(rep.state=='ok'){
					pop_close();
					parent.layui.table.reload('appTable');
				}
			},
			complete : function() {
			}
		});
		return false;
	});


	$("#optIpAddr").keydown(function(e){
		if(e.keyCode==13){
			if($("#optIpAddr").val() != null && $("#optIpAddr") != '') {
                if(arr != null && arr.length > 0){
					for(var i=0;i<arr.length;i++){
						if(arr[i] == $("#optIpAddr").val()){
							layer.msg('该IP已添加');
							$("#optIpAddr").val('');
							return false;
						}
					}
				}else{
                	arr.push($("#optIpAddr").val());
				}
                arr.push($("#optIpAddr").val());
				$("#ipAddr").append("<option value='" + $("#optIpAddr").val() + "'>" + $("#optIpAddr").val() + "</option>");
				formSelects.render('ipAddr');
				if($("#id").val() != null && $("#id").val() != ''){
					var ipArr = $("#ipAddrs").val().split(",");
					if(ipArr != null && ipArr.length > 0){
						formSelects.value('ipAddr',ipArr);
					}
				}
				$("#optIpAddr").val('');
			}
			return false;
		}
	});
});
</script>
#end