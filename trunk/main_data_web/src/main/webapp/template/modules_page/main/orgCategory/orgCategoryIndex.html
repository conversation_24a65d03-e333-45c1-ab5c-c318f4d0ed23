#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人事机构维度首页#end

#define css()
#end

#define js()
<script type="text/html" id="orgCategoryTableBar">
	#shiroHasPermission("main:orgCategory:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("main:orgCategory:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'orgCategoryTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/main/orgCategory/pageTable'
	    , method: 'post'
	    , height: vipTable.getFullHeight()    //容器高度
	    , cols: [[
			{field: '', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			, {field: 'categoryName', title: '维度名称', unresize:true}
			, {field:'createDate', title: '创建时间', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
			, {fixed: 'right', title: '操作', align: 'center', unresize:true, toolbar: '#orgCategoryTableBar'} //这里的toolbar值是模板元素的选择器
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
    table.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/main/orgCategory/edit?id='+obj.data.id,'450','200');
        }else if(obj.event==="del"){//作废按钮事件
        	//作废操作
    		layer.confirm('您确定要作废该的数据？', {icon:3, title:'提示'}, function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/main/orgCategory/del',
                    data: {id:obj.data.id, delFlag:'1'},
                    loadFlag: true,
                    success : function(rep){
                    	if(rep.state=='ok'){
	                    	pageTableReload();
                    	}
                    },
                    complete : function() {
        		    }
                });
				layer.close(index);
            });
        }
    });
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{categoryName:$('#categoryName').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		pop_show('添加','#(ctxPath)/main/orgCategory/add','450','200');
	});
	$(function() {
	});
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <label class="layui-form-label">维度名称：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="categoryName" name="categoryName" class="layui-input" placeholder="请输入维度名称" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
				        #shiroHasPermission("main:orgCategory:addBtn")
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
						#end
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
	<div class="layui-row">
		<table id="orgCategoryTable" lay-filter="orgCategoryTable"></table>
	</div>
</div>
#end