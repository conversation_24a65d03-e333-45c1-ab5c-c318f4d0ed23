#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位查询#end

#define css()
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
<!--	<div style="margin:40px 0px 0px 20px;display: flex;align-items: center;" class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief" >-->
<!--		<span style="margin-right:20px;font-size: 16px;">基地</span>-->
<!--		<div class="layui-btn-group" style="margin-right:20px;">-->
<!--			<button class="layui-btn layui-btn-sm" type="button" id="addBase">添加</button>-->
<!--			<button class="layui-btn layui-btn-sm  layui-btn-normal" type="button" id="editBase">编辑</button>-->
<!--			<button class="layui-btn layui-btn-sm layui-btn-danger" type="button" id="delBase">作废</button>-->
<!--		</div>-->
<!--		<ul class="layui-tab-title">-->
<!--			<div id="base" style="display: flex;flex-wrap:wrap;">-->

<!--			</div>-->
<!--		</ul>-->
<!--	</div>-->
	<div style="margin:40px 0px 0px 20px;display: flex;align-items: center;" class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
		<span style="margin-right:20px;font-size: 16px;">楼栋</span>
		<ul class="layui-tab-title" style="flex-grow:1;height: auto;">
			<div id="building" style="display: flex;flex-wrap:wrap;">

			</div>
		</ul>
	</div>
	<div style="margin:40px 0px 0px 20px;display: flex;align-items: center;" class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
		<span style="margin-right:20px;font-size: 16px;">楼层</span>
		<ul class="layui-tab-title" style="flex-grow:1;height: auto;">
			<div id="floor" style="display: flex;flex-wrap:wrap;">

			</div>
		</ul>
	</div>
	<div style="margin:40px 0px 0px 20px;display: flex;align-items: center;" class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
		<span style="margin-right:20px;font-size: 16px;">房间</span>
		<ul class="layui-tab-title" style="flex-grow:1;height: auto;">
			<div id="room" style="display: flex;flex-wrap:wrap;">

			</div>
		</ul>
	</div>
	<div id="table" style="margin-top: 50px;">
		<table style="margin-left:20px;" class="layui-hide" id="bedTable" lay-filter="bedTable"></table>
	</div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;



	// function loadBase(){
	// 	util.sendAjax ({
	// 		type: 'POST',
	// 		url: '#(ctxPath)/main/bedQuery/getBase',
	// 		data: '',
	// 		notice: false,
	// 		loadFlag: true,
	// 		success : function(rep){
	// 			if(rep.state==='ok'){
	// 				//清除基地
	// 				$("#base").empty();
	// 				//清除楼栋
	// 				$("#building").empty();
	// 				//清除楼层
	// 				$("#floor").empty();
	// 				//清除房间
	// 				$("#room").empty();
	// 				//清空表格
	//
	//
	//
	//
	// 				var data=rep.data;
	// 				if(data.length>0){
	// 					var baseStr="<li class='layui-this' onclick=\"loadBuilding('')\">全部</li>";
	// 					for(var i=0;i<data.length;i++){
	// 						baseStr+="<li data-id='"+data[i].id+"' onclick=\"loadBuilding('"+data[i].id+"')\">"+data[i].baseName+"</li>";
	// 					}
	// 					$("#base").html(baseStr);
	// 					loadBuilding('');
	// 				}
	// 			}
	// 		},
	// 		complete : function() {
	// 		}
	// 	});
	// }

	loadBuilding($("#currBaseId",parent.document).val());

	function loadBuilding(baseId) {
		var baseIds=new Array();
		if(baseId==='' || baseId===null){
			$("#base li[data-id!='']").each(function (i) {
				baseIds[i]=$(this).attr("data-id");
			})
			$("#base li:eq(0)").addClass('layui-this').siblings().removeClass("layui-this");
		}else{
			baseIds[0]=baseId;
			$("#base li[data-id='"+baseId+"']").addClass('layui-this').siblings().removeClass("layui-this");
		}
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/bedQuery/getBuilding',
			data: {'baseIds':baseIds.toString()},
			notice: false,
			loadFlag: true,
			success : function(rep){
				if(rep.state==='ok'){
					//清除楼栋
					$("#building").empty();
					//清除楼层
					$("#floor").empty();
					//清除房间
					$("#room").empty();
					//清空表格
					$("#table .layui-table tbody").empty();
					var data=rep.data;
					if(data.length>0){
						// var buildingStr="<li class='layui-this' onclick=\"loadFloor('')\">全部</li>";
						var buildingStr="";
						for(var i=0;i<data.length;i++){
							buildingStr+="<li data-id='"+data[i].id+"' onclick=\"loadFloor('"+data[i].id+"')\">"+data[i].buildingName+"</li>";
						}
						$("#building").html(buildingStr);
						loadFloor(data[0].id);
					}else{
						console.log("数据为空");
					}

				}else{
					console.log("错误");
				}
			},
			complete : function() {
			}
		});
	}

	loadFloor=function (buildingId) {
		var buildingIds=new Array();
		if(buildingId==='' || buildingId===null){
			$("#building li[data-id!='']").each(function (i) {
				buildingIds[i]=$(this).attr("data-id");
			});
			$("#building li:eq(0)").addClass('layui-this').siblings().removeClass("layui-this");
		}else{
			buildingIds[0]=buildingId;
			$("#building li[data-id='"+buildingId+"']").addClass('layui-this').siblings().removeClass("layui-this");
		}
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/bedQuery/getFloor',
			data: {'buildingIds':buildingIds.toString()},
			notice: false,
			loadFlag: true,
			success : function(rep){
				if(rep.state==='ok'){
					//清除楼层
					$("#floor").empty();
					//清除房间
					$("#room").empty();
					//清空表格
					$("#table .layui-table tbody").empty();

					var data=rep.data;
					if(data.length>0){
						//var floorStr="<li class='layui-this' onclick=\"loadRoom('')\">全部</li>";
						var floorStr="";
						for(var i=0;i<data.length;i++){
							floorStr+="<li data-id='"+data[i].id+"' onclick=\"loadRoom('"+data[i].id+"')\">"+data[i].floorName+"</li>";
						}
						$("#floor").html(floorStr);
						loadRoom(data[0].id);
					}else{
						console.log("数据为空");
					}

				}else{
					console.log("错误");
				}
			},
			complete : function() {
			}
		});
	}
	
	loadRoom=function (floorId) {
		var floorIds=new Array();
		if(floorId==='' || floorId===null){
			$("#floor li[data-id!='']").each(function (i) {
				floorIds[i]=$(this).attr("data-id");
			});
			$("#floor li:eq(0)").addClass('layui-this').siblings().removeClass("layui-this");
		}else{
			floorIds[0]=floorId;
			$("#floor li[data-id='"+floorId+"']").addClass('layui-this').siblings().removeClass("layui-this");
		}
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/main/bedQuery/getRoom',
			data: {'floorIds':floorIds.toString()},
			notice: false,
			loadFlag: true,
			success : function(rep){
				if(rep.state==='ok'){
					//清空楼层
					$("#room").empty();
					var data=rep.data;
					if(data.length>0){
						//var roomStr="<li class='layui-this' onclick=\"loadBedTable('')\">全部</li>";
						var roomStr="";
						for(var i=0;i<data.length;i++){
							roomStr+="<li data-id='"+data[i].id+"' onclick=\"loadBedTable('"+data[i].id+"')\">"+data[i].roomName+"</li>";
						}
						$("#room").html(roomStr);
						loadBedTable(data[0].id);
					}else{
						console.log("数据为空");
					}
				}else{
					console.log("错误");
				}
			},
			complete : function() {
			}
		});
	}
	
	loadBedTable=function (roomId) {
		var roomIds=new Array();
		if(roomId==='' || roomId===null){
			$("#room li[data-id!='']").each(function (i) {
				var dataId=$(this).attr("data-id");
				roomIds[i]=$(this).attr("data-id");
			});
			$("#room li:eq(0)").addClass('layui-this').siblings().removeClass("layui-this");
		}else{
			roomIds[0]=roomId;
			$("#room li[data-id='"+roomId+"']").addClass('layui-this').siblings().removeClass("layui-this");
		}

		table.render({
			id : 'bedTable'
			,elem : '#bedTable'
			,method : 'POST'
			,where : {'roomIds':roomIds.toString()}
			,limit : 10
			,limits : [10,20,30,40]
			,url : '#(ctxPath)/main/bedQuery/getBedPage'
			,cellMinWidth: 80
			,cols: [[
				{field: 'bedName',align: 'center',title: '床位名称',unresize:true}
				,{field:'bedNo', title: '床位编号', align: 'center', unresize: true}
				,{field:'bedType', title: '床位类型', align: 'center', unresize: true}
				,{field:'bedStatus', title: '床位状态', align: 'center', unresize: true}
				,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'可用':'不可用' }}</div>"}
				,{field:'isManyPeople', title: '是否多人', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='0'?'可多人用':'不可多人用' }}</div>"}
				,{field:'peopleMax', title: '最多人数', align: 'center', unresize: true}
				,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
				// ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
		});

		table.on('tool(bedTable)',function (obj) {
			if(obj.event==='edit'){
				pop_show("编辑床位",'#(ctxPath)/main/buildingRoomManage/bedForm?bedId='+obj.data.id,1000,500);
			}else if(obj.event==='del'){
				layer.confirm("确定要作废该床位吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/main/buildingRoomManage/bedDel',
						notice: true,
						data: {'id':obj.data.id},
						loadFlag: true,
						success : function(rep){
							if(rep.state==='ok'){
								bedTableReload();
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}
		});


	}

});
</script>
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end