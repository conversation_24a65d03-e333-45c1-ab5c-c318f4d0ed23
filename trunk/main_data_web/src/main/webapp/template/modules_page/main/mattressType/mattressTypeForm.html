#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="mattressTypeForm">
    <div class="layui-row">
        <input type="hidden" id="roomTypeId" name="mattressType.id" value="#(mattressType.id??)"/>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">序号</label>
                <div class="layui-input-inline">
                    <input type="text" name="mattressType.order" class="layui-input" lay-verify="required|number" value="#(mattressType.order??)" placeholder="请输入序号">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">床垫名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="mattressType.name" class="layui-input" lay-verify="required" value="#(mattressType.name??)" placeholder="请输入类型名称">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">床垫编号</label>
                <div class="layui-input-inline">
                    <select name="mattressType.code" lay-verify="required">
                        <option value="">请选择床垫编号</option>
                        #for(code : mattressTypeCode)
                        <option value="#(code.key)" #if(mattressType.code??==code.key) selected #end >#(code.value)</option>
                        #end
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">是否可用</label>
                <div class="layui-input-inline">
                    <select id="isEnable" name="mattressType.isEnable" lay-verify="required">
                        <option value="0" #(mattressType != null ?(mattressType.isEnable == '0' ? 'selected':''):'')>可用</option>
                        <option value="1" #(mattressType != null ?(mattressType.isEnable == '1' ? 'selected':''):'')>不可用</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="mattressType.description" placeholder="请输入内容" class="layui-textarea">#(mattressType.description??)</textarea>
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/mattressType/saveMattressType";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#mattressTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.mattressTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
