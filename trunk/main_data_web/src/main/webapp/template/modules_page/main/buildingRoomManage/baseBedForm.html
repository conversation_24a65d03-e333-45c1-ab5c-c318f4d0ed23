#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床位编辑#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
<style>
	#form .layui-form-radio{
		margin:8px 2px;
	}
</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<table width="100%">
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*床位名称</label>
						<div class="layui-input-inline">
							<input name="bedName" value="#(bed.bedName??)" required lay-verify="required" placeholder="请输入床位名称" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*床位编号</label>
						<div class="layui-input-inline">
							<input name="bedNo" value="#(bed.bedNo??)" required lay-verify="required" placeholder="请输入床位编号" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td rowspan="4">
					<div class="layui-upload" style="text-align: center;height: 250px;width: 200px;background-color: #f2f2f2;">
						<div class="layui-upload-list">
							<img class="layui-upload-img" id="profilePhotoImg" style="max-height: 250px;max-width: 200px;" src="#if(bed.bedImage??!=null)#(bed.bedImage??)#end" >
							<p id="profilePhotoText"></p>
							<input type="hidden" id="uploadPath" name="bedImage" value="#(bed.bedImage??)">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*床位类型</label>
						<div class="layui-input-inline">
							<select name="bedTypeId" lay-verify="required">
								<option value="">请选择床位类型</option>
								#for(type:baseTypeList)
								<option value="#(type.id)" #if(type.id==bed.bedTypeId??) selected #end>#(type.typeName)</option>
								#end
							</select>
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*床位状态</label>
						<div class="layui-input-inline">
							<select name="bedStatusId" lay-verify="required">
								<option value="">请选择床位状态</option>
								#for(status:bedStatusList)
								<option value="#(status.id)" #if(status.id.equalsIgnoreCase(bed.bedStatusId??)) selected #end>#(status.statusName)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*选房费</label>
						<div class="layui-input-inline">
							<select name="markupTypeId">
								<option value="">请选择选房费</option>
								#for(bedMarkupType:bedMarkupTypeList)
								<option value="#(bedMarkupType.id)" #if(bedMarkupType.id==bed.markupTypeId??) selected #end>#(bedMarkupType.name)</option>
								#end
							</select>
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否可用</label>
						<div class="layui-input-inline">
							<input type="radio" name="isEnable" lay-verify="required" value="0" #if(bed==null) checked #elseif(bed.isEnable??=='0') checked #end  title="可用">
							<input type="radio" name="isEnable" lay-verify="required" value="1" #if(bed.isEnable??=='1') checked #end title="不可用">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item">
						<label class="layui-form-label">*预订渠道</label>
						<div class="layui-input-block">
							<select xm-select="bookChannel" name="bookChannel" id="bookChannel">
								#for(bookChannel : bookChannelList)
								<option value="#(bookChannel.id??)" #if(bed==null) selected #elseif(bedChannelIds??!=null && bedChannelIds.indexOf(bookChannel.id)!=-1) selected #end >#(bookChannel.name??)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*床垫类型</label>
						<div class="layui-input-inline">
							<select name="mattressTypeId">
								<option value="">请选择床垫类型</option>
								#for(mattressType : mattressTypeList)
								<option value="#(mattressType.id)" #if(mattressType.id==bed.mattressTypeId??) selected #end>#(mattressType.name)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item layui-form-text">
						<label class="layui-form-label">描述</label>
						<div class="layui-input-block">
							<textarea name="remark" placeholder="请输入描述" class="layui-textarea">#(bed.remark??)</textarea>
						</div>
					</div>
				</td>
			</tr>
		</table>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="fileId" name="fileId" value="#(bed.fileId??)"/>
				<input type="hidden" id="commonUpload" value="#(commonUpload)"/>
				<input type="hidden" id="id" name="id" value="#(bed.id??)"/>
				<input type="hidden" id="roomId" name="roomId" value="#if(bed==null)#(roomId)#else#(bed.roomId??)#end"/>
				<button type="button" class="layui-btn" id="profilePhoto">上传图片</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"/>
<script src="layui.all.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script>
	var formSelects=layui.formSelects;
	layui.use(['form','layer','table','upload'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,upload=layui.upload;
		formSelects.render('bookChannel');

		form.on('submit(confirmBtn)',function (obj) {
			if(obj.field.roomId==='' || obj.field.roomId===null || typeof(obj.field.roomId) ==='undefined'){
				layer.msg('楼层id不能为空',{icon:5,time:5000});
				return false;
			}
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/buildingRoomManage/saveBed',
				data: obj.field,
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state==='ok'){
						pop_close();
						parent.bedTableReload();
					}
				},
				complete : function() {
				}
			});

			return false;
		});


		//普通图片上传
		var uploadInst = upload.render({
			elem: '#profilePhoto'
			,url:$("#commonUpload").val() +'/upload?bucket=bed'
			,before: function(obj){
				$("#confirmBtn").attr("disabled",true);
				$("#confirmBtn").addClass("layui-btn-disabled");
				//预读本地文件示例，不支持ie8
				obj.preview(function(index, file, result){
					$('#profilePhotoImg').attr('src', result);
				});
			}
			,done: function(res){
				$("#confirmBtn").attr("disabled",false);
				$("#confirmBtn").removeClass("layui-btn-disabled");
				//如果上传失败
				if(res.state === 'ok'){
					$("#uploadPath").val(res.data.src);
					$("#fileId").val(res.data.id);
					layer.msg(res.msg,{icon:1,time:5000});
				}else {
					return layer.msg('上传失败');
				}
			}
			,error: function(){
				//演示失败状态，并实现重传
				var demoText = $('#demoText');
				demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
				demoText.find('.demo-reload').on('click', function(){
					uploadInst.upload();
				});
			}
		});

	});
</script>
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end