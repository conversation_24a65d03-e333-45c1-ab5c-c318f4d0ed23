#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置编辑#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/secondLevel/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#form").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.carTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 0px;margin-top: 0px;" id="form">
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>二级分类名称</label>
        <div class="layui-input-block">
            <input type="text" name="name" lay-verify="required" value="#(model.name??)" placeholder="请输入角色名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>所属一级分类</label>
        <div class="layui-input-block">
            <select name="firstLevelId">
                <option value="">请选择一级分类</option>
                #for(firstLevel : firstLevelList)
                <option value="#(firstLevel.id)" #if(firstLevel.id==model.firstLevelId??) selected #end>#(firstLevel.name)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"><font color="red">*</font>会员包房扣费天数</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="memberRoomDeductTimes" lay-verify="required|number" value="#(model.memberRoomDeductTimes??)" placeholder="请输入会员包房扣费天数" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"><font color="red">*</font>散客包房扣费天数</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="outsiderRoomDeductTimes" lay-verify="required|number" value="#(model.outsiderRoomDeductTimes??)" placeholder="请输入散客包房扣费天数" autocomplete="off" class="layui-input">
        </div>
    </div>

    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"><font color="red">*</font>员工包房扣费天数</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="staffRoomDeductTimes" lay-verify="required|number" value="#(model.staffRoomDeductTimes??)" placeholder="请输入员工包房扣费天数" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"><font color="red">*</font>排房顺序</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="bookSort" lay-verify="required|number" value="#(model.bookSort??)" placeholder="请输入排房顺序" autocomplete="off" class="layui-input">
        </div>
    </div>
    <!--<div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>是否可预定</label>
        <div class="layui-input-block">
            <input type="radio"  name="isBooking" value="1" title="是" #if(model==null || model.isBooking??=='1') checked #end >
            <input type="radio"  name="isBooking" value="0" title="否" #if(model.isBooking??=='0') checked #end>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>是否大床房</label>
        <div class="layui-input-block">
            <input type="radio" name="isBigBedRoom" value="1" title="是" #if(model==null || model.isBigBedRoom??=='1') checked #end >
            <input type="radio" name="isBigBedRoom" value="0" title="否" #if(model.isBigBedRoom??=='0') checked #end>
        </div>
    </div>-->
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 140px;"><font color="red">*</font>是否计算入住率</label>
        <div class="layui-input-block">
            <input type="radio" name="isCalculateOccupancyRate" value="1" title="是" #if(model==null || model.isCalculateOccupancyRate??=='1') checked #end >
            <input type="radio" name="isCalculateOccupancyRate" value="0" title="否" #if(model.isCalculateOccupancyRate??=='0') checked #end>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="1" title="是" #if(model==null || model.isEnable??=='1') checked #end >
            <input type="radio" name="isEnable" value="0" title="否" #if(model.isEnable??=='0') checked #end>
        </div>
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id"  value="#(model.id??)">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
