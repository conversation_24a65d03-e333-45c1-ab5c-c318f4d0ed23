#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间编辑#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
<style>
	#form .layui-form-radio{
		margin:8px 2px;
	}
</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<table width="100%">
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*房间名称</label>
						<div class="layui-input-inline">
							<input name="roomName" value="#(room.roomName??)" required lay-verify="required" placeholder="请输入楼层名称" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*房间编号</label>
						<div class="layui-input-inline">
							<input name="roomNo" value="#(room.roomNo??)" required lay-verify="required" placeholder="请输入楼层编号" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td #if(room!=null) rowspan="5" #else rowspan="5" #end>
					<div class="layui-upload" style="text-align: center;height: 200px;width: 200px;background-color: #f2f2f2;">
						<div class="layui-upload-list">
							<img class="layui-upload-img" id="profilePhotoImg" style="max-height: 200px;max-width: 200px;" src="#if(room.roomImage??!=null)#(room.roomImage??)#end" >
							<p id="profilePhotoText"></p>
							<input type="hidden" id="uploadPath" name="roomImage" value="#(room.roomImage??)">
						</div>a
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*房间类型</label>
						<div class="layui-input-inline">
							<select name="roomType" id="roomType" lay-verify="required" lay-search>
								<option value="">请选择房间类型</option>
								#for(type : roomTypeList)
								<option value="#(type.id)" #if(type.id==room.roomType??) selected #end>#(type.typeName)</option>
								#end
							</select>
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 5px;">*基地房间类型</label>
						<div class="layui-input-inline">
							<select name="baseRoomType" id="baseRoomType" lay-verify="required" lay-search>
								<option value="">请选择房间类型</option>
								#for(type : baseRoomTypeList)
								<option value="#(type.id)" #if(type.id==room.baseRoomType??) selected #end>#(type.typeName)</option>
								#end
							</select>
						</div>
					</div>
					
				</td>
			</tr>
			<tr>
<!-- 				<td> -->
<!-- 					<div class="layui-form-item"> -->
<!-- 						<label class="layui-form-label">用途类型</label> -->
<!-- 						<div class="layui-input-inline"> -->
<!-- 							<select name="useType" lay-filter=""> -->
<!-- 								#dictOption("use_type", room.useType??'', "") -->
<!-- 							</select> -->
<!-- 						</div> -->
<!-- 					</div> -->
<!-- 				</td> -->
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">房间用途</label>
						<div class="layui-input-inline">
							<select name="roomUsage" lay-filter="">
								#statusOption(com.cszn.integrated.service.entity.status.RoomUsage::me(), room.roomUsage??"")
							</select>
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*选房费</label>
						<div class="layui-input-inline">
							<select name="markupTypeId">
								<option value="">请选择选房费</option>
								#for(bedMarkupType:bedMarkupTypeList)
								<option value="#(bedMarkupType.id)" #if(bedMarkupType.id==room.markupTypeId??) selected #end>#(bedMarkupType.name)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*入住人数</label>
						<div class="layui-input-inline">
							<input name="peoples" value="#(room.peoples??)" required lay-verify="required|number" placeholder="请输入入住人数" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">维修状态</label>
						<div class="layui-input-inline">
							<select name="maintainState" id="maintainState" >
								<option value="">请选择维修状态</option>
								#for(state : maintainStateMap)
								<option value="#(state.key??)" #if(room.maintainState??==state.key) selected #end >#(state.value??)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*锁编号</label>
						<div class="layui-input-inline">
							<input name="lockNo" value="#(room.lockNo??)" placeholder="请输入锁编号" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*锁类型</label>
						<div class="layui-input-inline">
							<select name="lockType" id="lockType">
								#for(type : lockType)
								<option value="#(type.key??)"  #if(type.key==room.lock_type??) selected #end>#(type.value??)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>

			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否人脸锁</label>
						<div class="layui-input-inline">
							<input type="radio" name="isFaceLock" lay-verify="required" value="1" #if(room.isFaceLock??=='1') checked #end title="是">
							<input type="radio" name="isFaceLock" lay-verify="required" value="0" #if(room==null) checked #elseif(room.isFaceLock??=='0') checked #end  title="否">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label"  style="width: 120px;">锁服务器地址</label>
						<div class="layui-input-inline">
							<input name="lockService" value="#(room.lockService??)" required lay-verify="" placeholder="请输入锁服务器地址" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
			</tr>
			
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否可用</label>
						<div class="layui-input-inline">
							<input type="radio" name="isEnable" lay-verify="required" value="0" #if(room==null) checked #elseif(room.isEnable??=='0') checked #end  title="可用">
							<input type="radio" name="isEnable" lay-verify="required" value="1" #if(room.isEnable??=='1') checked #end title="不可用">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否可预订</label>
						<div class="layui-input-inline">
							<input type="radio" name="isBooking" lay-verify="required" value="0" #if(room==null) checked #elseif(room.isBooking??=='0') checked #end  title="可预订">
							<input type="radio" name="isBooking" lay-verify="required" value="1" #if(room.isBooking??=='1') checked #end title="不可预订">
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">*是否发卡</label>
						<div class="layui-input-inline">
							<input type="radio" name="allowLock" lay-verify="required" value="0" #if(room==null) checked #elseif(room.allowLock??=='0') checked #end  title="发卡">
							<input type="radio" name="allowLock" lay-verify="required" value="1" #if(room.allowLock??=='1') checked #end title="不发卡">
						</div>
					</div>
				</td>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>是否可线上预订</label>
						<div class="layui-input-block">
							<select id="isOutBookabled" name="isOutBookabled" lay-verify="required">
								<option value="">请选择是否可线上预订</option>
								<option value="1" #(room != null ?(room.isOutBookabled == '1' ? 'selected':''):'')>可预订</option>
								<option value="0" #(room != null ?(room.isOutBookabled == '0' ? 'selected':''):'')>不可预订</option>
							</select>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<div class="layui-form-item">
						<label class="layui-form-label">*预订渠道</label>
						<div class="layui-input-block">
							<select xm-select="bookChannel" name="bookChannel" id="bookChannel">
								#for(bookChannel : bookChannelList)
								<option value="#(bookChannel.id??)" #if(room==null) selected #elseif(roomChannelIds??!=null && roomChannelIds.indexOf(bookChannel.id)!=-1) selected #end >#(bookChannel.name??)</option>
								#end
							</select>
						</div>
					</div>
				</td>
			</tr>

			<tr>
				<td>
					<div class="layui-form-item">
						<label class="layui-form-label">总床位数</label>
						<div class="layui-input-inline">
							<input name="bedCount" value="#(room.totalBeds??)" autocomplete="off" class="layui-input">
						</div>
					</div>
				</td>
			</tr>
			#if(room!=null)
			#end
			<tr>
				<td colspan="2">
					<div class="layui-form-item layui-form-text">
						<label class="layui-form-label">描述</label>
						<div class="layui-input-block">
							<textarea name="remark" placeholder="请输入描述" class="layui-textarea">#(room.remark??)</textarea>
						</div>
					</div>
				</td>
			</tr>
		</table>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="fileId" name="fileId" value="#(room.fileId??)"/>
				<input type="hidden" id="commonUpload" value="#(commonUpload)"/>
				<input type="hidden" id="id" name="id" value="#(room.id??)"/>
				<input type="hidden" id="buildingId" name="buildingId" value="#if(room==null)#(buildingId)#else#(room.buildingId??)#end"/>
				<input type="hidden" id="floorId" name="floorId" value="#if(room==null)#(floorId)#else#(room.floorId??)#end"/>
				<button id="confirmBtn2" class="layui-btn" lay-submit=""  lay-filter="confirmBtn2">保存并自动创床位</button>
				<button type="button" class="layui-btn" id="profilePhoto">上传图片</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"/>
<script src="layui.all.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script>
	var formSelects=layui.formSelects;
	layui.use(['form','layer','table','upload'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,upload=layui.upload;
		formSelects.render('bookChannel');

		form.on('submit(confirmBtn)',function (obj) {
			layer.load();
			if(obj.field.buildingId==='' || obj.field.buildingId===null || typeof(obj.field.buildingId) ==='undefined'){
				layer.msg('楼栋id不能为空',{icon:5,time:5000});
				return false;
			}
			if(obj.field.floorId==='' || obj.field.floorId===null || typeof(obj.field.floorId) ==='undefined'){
				layer.msg('楼层id不能为空',{icon:5,time:5000});
				return false;
			}

			let baseRoomTypeId=$("#baseRoomType").val();
			let baseRoomTypeName=$("#baseRoomType option[value='"+baseRoomTypeId+"']").text();

			let roomTypeId=$("#roomType").val();
			let roomTypeName=$("#roomType option[value='"+roomTypeId+"']").text();

			if(baseRoomTypeName!=roomTypeName){
				layer.msg('房间类型和基地房间类型必须一致',{icon:5,time:5000});
				return false;
			}
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/buildingRoomManage/saveRoom',
				data: obj.field,
				notice: true,
				loadFlag: false,
				success : function(rep){
					layer.closeAll('loading');
					if(rep.state==='ok'){
						pop_close();
						parent.roomTableReload();
					}
				},
				complete : function() {
				}
			});

			return false;
		});

		form.on('submit(confirmBtn2)',function (obj) {
			layer.load();
			if(obj.field.buildingId==='' || obj.field.buildingId===null || typeof(obj.field.buildingId) ==='undefined'){
				layer.msg('楼栋id不能为空',{icon:5,time:5000});
				return false;
			}
			if(obj.field.floorId==='' || obj.field.floorId===null || typeof(obj.field.floorId) ==='undefined'){
				layer.msg('楼层id不能为空',{icon:5,time:5000});
				return false;
			}

			let baseRoomTypeId=$("#baseRoomType").val();
			let baseRoomTypeName=$("#baseRoomType option[value='"+baseRoomTypeId+"']").text();

			let roomTypeId=$("#roomType").val();
			let roomTypeName=$("#roomType option[value='"+roomTypeId+"']").text();

			if(baseRoomTypeName!=roomTypeName){
				layer.msg('房间类型和基地房间类型必须一致',{icon:5,time:5000});
				return false;
			}
			let saveData=obj.field;
			saveData.isGenBed="1";
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/main/buildingRoomManage/saveRoom',
				data: saveData,
				notice: true,
				loadFlag: false,
				success : function(rep){
					layer.closeAll('loading');
					if(rep.state==='ok'){
						//pop_close();
						parent.roomTableReload();
					}
				},
				complete : function() {
				}
			});

			return false;
		});


		//普通图片上传
		var uploadInst = upload.render({
			elem: '#profilePhoto'
			,url:$("#commonUpload").val() +'/upload?bucket=floor'
			,before: function(obj){
				$("#confirmBtn").attr("disabled",true);
				$("#confirmBtn").addClass("layui-btn-disabled");
				//预读本地文件示例，不支持ie8
				obj.preview(function(index, file, result){
					$('#profilePhotoImg').attr('src', result);
				});
			}
			,done: function(res){
				$("#confirmBtn").attr("disabled",false);
				$("#confirmBtn").removeClass("layui-btn-disabled");
				//如果上传失败
				if(res.state === 'ok'){
					$("#uploadPath").val(res.data.src);
					$("#fileId").val(res.data.id);
					layer.msg(res.msg,{icon:1,time:5000});
				}else {
					return layer.msg('上传失败');
				}
			}
			,error: function(){
				//演示失败状态，并实现重传
				var demoText = $('#demoText');
				demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
				demoText.find('.demo-reload').on('click', function(){
					uploadInst.upload();
				});
			}
		});

	});
</script>
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</script>
#end