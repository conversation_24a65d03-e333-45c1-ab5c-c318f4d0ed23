#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>排序</label>
			<div class="layui-input-block">
				<input type="text" name="foodSort" class="layui-input" lay-verify="required|number" value="#(model.foodSort??'')" placeholder="请输入排序" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>菜标签</label>
			<div class="layui-input-block">
				<select id="tagId" name="tagId" lay-verify="required" lay-search>
					<option value="">请选择</option>
					#for(tag : foodTagList)
						<option value="#(tag.id)" #(tag.id == model.tagId?'selected':'')>#(tag.tagName)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>菜</label>
			<div class="layui-input-block">
				<select id="foodId" name="foodId" lay-verify="required" lay-search>
					<option value="">请选择</option>
					#for(food : foodInfoList)
						<option value="#(food.id)" #(food.id == model.foodId?'selected':'')>#(food.foodName)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>类型</label>
			<div class="layui-input-block">
				<select id="configType" name="configType" lay-verify="required" lay-search>
					<option value="">请选择</option>
					#statusOption(com.cszn.integrated.service.entity.status.FoodConfigType::me(), model.configType??"")
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>成本价</label>
			<div class="layui-input-block">
				<input type="text" name="costPrice" class="layui-input" lay-verify="required|number" value="#(model.costPrice??'')" placeholder="请输入成本价" autocomplete="off">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>销售价</label>
			<div class="layui-input-block">
				<input type="text" name="salesPrice" class="layui-input" lay-verify="required|number" value="#(model.salesPrice??'')" placeholder="请输入销售价" autocomplete="off">
			</div>
		</div>
		#if(com.jfinal.kit.StrKit::isBlank(model.id))
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>是否上架</label>
				<div class="layui-input-block">
					<input type="radio" name="isShelf" value="0" title="否" #if(model.isShelf=='0') checked #end>
	        		<input type="radio" name="isShelf" value="1" title="是" #if(model.isShelf??=='1'||model.isShelf??''=='') checked #end>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>是否有效</label>
				<div class="layui-input-block">
					<input type="radio" name="isEnabled" value="0" title="否" #if(model.isEnabled=='0') checked #end>
	        		<input type="radio" name="isEnabled" value="1" title="是" #if(model.isEnabled??=='1'||model.isEnabled??''=='') checked #end>
				</div>
			</div>
		#end
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				#if(com.jfinal.kit.StrKit::isBlank(model.id))
					<input type="hidden" name="baseId" value="#(model.baseId??'')">
				#end
				<input type="hidden" name="oldFoodId" value="#(model.foodId??'')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="closeTab();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'element' ], function() {
	var form = layui.form
	, element = layui.element
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	form.render();
	
    closeTab = function(){
    	//删除指定Tab项
		element.tabDelete('baseFoodTab', 'form');
    }
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/main/base/saveBaseFood',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
					location.reload();
					closeTab();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end