#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()保存车型#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;" id="carTypeForm">
    <input type="hidden" id="id" name="id" value="#(carType.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">车型名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" class="layui-input" lay-verify="required" value="#(carType.name??)" placeholder="请输入车型名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">最多人数</label>
        <div class="layui-input-inline">
            <input type="text" name="personMax" class="layui-input" lay-verify="required|number" value="#(carType.personMax??)" placeholder="请输入最多人数">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="0" lay-verify="required" title="可用" #if(carType!=null && carType.isEnable=='0') checked #elseif(carType==null) checked #end>
            <input type="radio" name="isEnable" value="1" lay-verify="required" title="不可用" #if(carType.isEnable??=='1') checked #end>
        </div>
    </div>
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">描述</label>
        <div class="layui-input-block">
            <textarea name="description" placeholder="请输入内容" class="layui-textarea">#(carType.description??)</textarea>
        </div>
    </div>
    <div class="layui-form-footer" style="margin-top: 90px;">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/main/carType/saveCarType";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#carTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.carTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
