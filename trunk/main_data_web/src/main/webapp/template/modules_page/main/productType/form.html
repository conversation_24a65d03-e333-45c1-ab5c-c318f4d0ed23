#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()商品分类#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form layui-form-pane" action="" id="frm">


        <div class="layui-form-item">
            <label class="layui-form-label"><span></span>上级分类</label>
            <div class="layui-input-block">
                <div id="parentId"  name="parentId" style="margin-top: 1px;" >

                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" ><span>*</span>类型名称</label>
            <div class="layui-input-block" >
                <input name="typeName" value="#(model.typeName??)" required lay-verify="required" placeholder="请输入类型名称" autocomplete="off" class="layui-input">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" ><span>*</span>图标</label>
            <div class="layui-input-block" >
                <div class="layui-upload-drag" id="test10">
                    <i class="layui-icon"></i>
                    <p>点击上传，或将文件拖拽到此处</p>
                    <div #if(model.typeImg??==null) class="layui-hide" #end id="uploadDemoView">
                        <hr>
                        <img src="#(model.typeImg??)" alt="上传成功后渲染" style="max-width: 196px">
                    </div>
                </div>
            </div>
            <input type="hidden" id="typeImg" name="typeImg" value="#(model.typeImg??)" >
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label"><span>*</span>排序</label>
            <div class="layui-input-block">
                <input name="sort" value="#(model.sort??)" required lay-verify="required|number" placeholder="请输入排序" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 100px;">
            <label class="layui-form-label"><span>*</span>是否可用</label>
            <div class="layui-input-block">
                <input type="radio" name="isEnabled" value="1" lay-verify="required" title="可用" #if(model!=null && model.isEnabled=='1') checked #elseif(model==null) checked #end>
                <input type="radio" name="isEnabled" value="0" lay-verify="required" title="不可用" #if(model.isEnabled??=='0') checked #end>
            </div>
        </div>

        <div class="layui-form-footer">
            <div class="pull-right">
                <input type="hidden" name="id" value="#(model.id??)">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" type="button" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保存</button>
            </div>
        </div>
    </form>
</div>
#end

#define js()
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','form','upload'],function(){
        var table,form,$ ;
        table = layui.table ,
            form = layui.form,upload=layui.upload,
        $ = layui.$ ;

        var deptSelect = xmSelect.render({
            el: '#parentId',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                //expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/mall/productType/typeTree',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(model!=null)
            deptSelect.setValue(["#(model.parentId??)"]);
            #end
        });

        // 保存护理等级信息
        form.on('submit(confirmBtn)',function(obj){

            var parentId='';
            if(deptSelect.getValue().length>0){
                parentId=deptSelect.getValue()[0].id;
            }

            var data=$("#frm").serialize()+"&parentId="+parentId;

            util.sendAjax({
                url:"#(ctxPath)/mall/productType/typeSave",
                type:'post',
                data:data,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.tableGridReload();
                    }
                }
            });
            return false;
        }) ;

        //拖拽上传
        upload.render({
            elem: '#test10'
            ,url: '#(fileUploadUrl)?bucket=mallType' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
            ,done: function(res){
                if(res.state=='ok'){
                    layer.msg('上传成功');
                    layui.$('#uploadDemoView').removeClass('layui-hide').find('img').attr('src', res.data.src);
                    $("#typeImg").val(res.data.src);
                }else{
                    window.top.layer.msg('上传失败', {icon: 2, offset: 'auto'});
                }

            }
        });

    }) ;
</script>
#end