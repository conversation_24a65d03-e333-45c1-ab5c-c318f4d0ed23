#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游线路管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;width: 140px;padding: 0 20px;">线路名称</label>
            <input class="layui-input" name="name" id="name" placeholder="" autocomplete="off">
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("main:touristRoute:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="touristRouteTable2" lay-filter="touristRouteTable2"></table>
    </div>
    <input type="hidden" id="id"  value="#(id??)">
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form','laydate'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ,laydate=layui.laydate;


        touristRouteTableLoad=function(data){
            console.log(data);
            table.render({
                id:'touristRouteTable2',
                elem: '#touristRouteTable2'
                ,url : '#(ctxPath)/main/touristRoute/deductConfigPageList'
                ,method : 'POST'
                ,height:$(document).height()*0.85
                ,where:data
                ,cols: [[
                    {field: 'startDate', title: '开始日期',unresize:true,templet:"<div>{{dateFormat(d.startDate,'yyyy-MM-dd')}}</div>"}
                    ,{field: 'times', title: '扣卡天数',unresize:true}
                    ,{field: 'amount', title: '扣卡金额',unresize:true}
                    ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable == '0'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                    ,{field: 'amount', title: '创建时间',unresize:true,templet:"<div>{{dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss')}}</div>"}
                    ,{title: '操作',toolbar:'#toolBar',unresize:true}
                ]],
                page : false,
                limit : 15,
                limits: [15,20,25]
            });
        }



        touristRouteTableLoad({id:'#(id??)'})



        table.on('tool(touristRouteTable2)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/main/touristRoute/deductConfigForm?id=" + obj.data.id+"&routeId="+$("#id").val() ;
                layerShow("编辑",url,550,550);
            }
        });



        touristRouteTableReload=function(){
            var name=$("#name").val();
            table.reload('touristRouteTable2',{'where':{'id':$("#id").val()}});
        }

        $("#search").on('click',function () {
            touristRouteTableReload();
        });


        $("#add").on('click',function () {
            var url = "#(ctxPath)/main/touristRoute/deductConfigForm?routeId="+$("#id").val();
            layerShow("添加",url,550,550);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("main:touristRoute:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
#end