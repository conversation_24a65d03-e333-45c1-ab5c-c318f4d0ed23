#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床垫类型管理#end

#define css()
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            <div class="layui-input-inline">
                类型:
                <div class="layui-inline">
                    <select id="cateId">
                        <option value="">请选择类型</option>
                        #for(cate : categoryList)
                        <option value="#(cate.id)">#(cate.name)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-input-inline">
                商品名称:
                <div class="layui-inline">
                    <input id="name" name="name" class="layui-input">
                </div>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
        </form>
        #shiroHasPermission("main:mattressType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="mattressTypeTable" lay-filter="mattressTypeTable"></table>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'mattressTypeTable'
            ,elem : '#mattressTypeTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/mall/product/pageList'
            ,cellMinWidth: 80
            ,cols: [[
                {field:'code', title: '商品编号', align: 'center', unresize: true}
                ,{field:'categoryName', title: '商品类型', align: 'center', unresize: true}
                ,{field:'productName', title: '商品名称', align: 'center', unresize: true}
                ,{field:'title', title: '商品标题', align: 'center', unresize: true}
                ,{field:'price', title: '兑换积分', align: 'center', unresize: true}
                ,{field:'stockCount', title: '库存数', align: 'center', unresize: true}
                ,{field:'sort', title: '排序', align: 'center', unresize: true}
                ,{field:'publishStatus', title: '状态', align: 'center', unresize: true,templet:"<div>{{ d.publishStatus=='1'?'<span class='layui-badge layui-bg-green'>上架中</span>':d.publishStatus=='0'?'<span class='layui-badge'>下架中</span>':'- -' }}</div>"}
                ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });

        table.on('tool(mattressTypeTable)',function(obj){
            if(obj.event === 'edit'){
                var url = "#(ctxPath)/mall/product/form?id=" + obj.data.id ;
                pop_show("编辑类型",url,800,500);
            }else if(obj.event === 'del'){
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mall/product/saveProduct',
                        notice: true,
                        data: {'id':obj.data.id,'delFlag':'1'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                mattressTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event==='img'){
                var url = "#(ctxPath)/mall/product/productImage?id=" + obj.data.id ;
                pop_show("商品图片",url,900,650);

            }else if(obj.event==='up'){
                layer.confirm("确定要上架吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mall/product/saveProduct',
                        notice: true,
                        data: {'id':obj.data.id,'publishStatus':'1'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                mattressTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event==='down'){
                layer.confirm("确定要下架吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mall/product/saveProduct',
                        notice: true,
                        data: {'id':obj.data.id,'publishStatus':'0'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                mattressTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });

        productImageIndex=function(id){
            var url = "#(ctxPath)/mall/product/productImage?id=" + id ;
            pop_show("商品图片",url,900,650);
        }

        function stockModelTableReload(){
            table.reload('mattressTypeTable',{"where":{"name":$("#name").val(),"cateId":$("#cateId").val()}});
        }

        mattressTypeTableReload=function(){
            table.reload('mattressTypeTable',{"where":{"name":$("#name").val(),"cateId":$("#cateId").val()}});
        }



        // 搜索
        $("#search").click(function(){
            mattressTypeTableReload();
        });

        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mall/product/form"
            pop_show("新增商品",url,800,500);
        });


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:mattressType:editBtn")
    <a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="edit">编辑</a>
    #end
    <a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="img">图片</a>
#[[
    {{#if(d.publishStatus=='1'){}}
        <a class="layui-btn layui-btn-xs layui-btn-danger" style="margin-left: 0px;" lay-event="down">下架</a>
    {{#}else if(d.publishStatus=='0'){}}
        <a class="layui-btn layui-btn-xs" style="margin-left: 0px;" lay-event="up">上架</a>
    {{#}}}
]]#

    <a class="layui-btn layui-btn-xs layui-btn-danger" style="margin-left: 0px;" lay-event="del">作废</a>
</script>
#end
