#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()房间类型展示#end

#define css()

#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 10px;margin-top: 20px;" id="mattressTypeForm">
    <div class="layui-row">
        <input type="hidden"  name="id" value="#(model.id??)"/>

        <div class="layui-form-item">
            <label class="layui-form-label">商品类型</label>
            <div class="layui-input-block">
                <select name="cateId" lay-verify="required">
                    <option value="">请选择商品类型</option>
                    #for(cate : categoryList)
                    <option value="#(cate.id)" #if(cate.id==model.cateId??) selected #end>#(cate.name)</option>
                    #end
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">商品名称</label>
            <div class="layui-input-block">
                <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" placeholder="请输入类型名称">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">商品标题</label>
            <div class="layui-input-block">
                <input type="text" name="title" class="layui-input" lay-verify="required" value="#(model.title??)" placeholder="请输入类型名称">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">是否上架</label>
            <div class="layui-input-block">
                <input type="radio" name="publishStatus" value="1" title="是" #if(model==null || model.publishStatus??=='1') checked #end >
                <input type="radio" name="publishStatus" value="0" title="否" #if(model.publishStatus??=='0') checked #end>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 10px;">库存数</label>
            <div class="layui-input-block">
                <input type="text" name="stockCount" class="layui-input" lay-verify="required|number|positiveNumber" value="#(model.stockCount??)" placeholder="请输入库存数">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 10px;">兑换所需积分</label>
            <div class="layui-input-block">
                <input type="text" name="price" class="layui-input" lay-verify="required|number|positiveNumber" value="#(model.price??)" placeholder="请输入兑换所需积分">
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 10px;">排序</label>
            <div class="layui-input-block">
                <input type="text" name="sort" class="layui-input" lay-verify="required|number|positiveNumber" value="#(model.sort??)" placeholder="请输入排序">
            </div>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery','upload'], function(){
        var form = layui.form,$ = layui.jquery,upload = layui.upload;


        //自定义验证规则
        form.verify({
            positiveNumber: function(value){
                if(value < 0){
                    return '不能小于0';
                }
            }
            ,content: function(value){
                layedit.sync(editIndex);
            }
        });


        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mall/product/saveProduct";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#mattressTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.mattressTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
