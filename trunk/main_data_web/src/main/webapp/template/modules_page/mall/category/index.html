#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床垫类型管理#end

#define css()
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            类型名称:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
        </form>
        #shiroHasPermission("main:mattressType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="mattressTypeTable" lay-filter="mattressTypeTable"></table>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'mattressTypeTable'
            ,elem : '#mattressTypeTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/mall/category/pageList'
            ,cellMinWidth: 80
            ,cols: [[
                {field:'code', title: '商品', align: 'center', unresize: true}
                ,{field:'name', title: '商品名称', align: 'center', unresize: true}
                ,{field:'sort', title: '排序', align: 'center', unresize: true}
                ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });

        table.on('tool(mattressTypeTable)',function(obj){
            if(obj.event === 'edit'){
                var url = "#(ctxPath)/mall/category/form?id=" + obj.data.id ;
                pop_show("编辑类型",url,500,500);
            }else if(obj.event === 'del'){
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mall/category/categoryDelete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                mattressTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });

        mattressTypeTableReload=function(){
            table.reload('mattressTypeTable',{"where":{"name":$("#name").val()}});
        }

        // 搜索
        $("#search").click(function(){
            mattressTypeTableReload();
        });

        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mall/category/form"
            pop_show("新增类型",url,500,500);
        });


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("main:mattressType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
</script>
#end
