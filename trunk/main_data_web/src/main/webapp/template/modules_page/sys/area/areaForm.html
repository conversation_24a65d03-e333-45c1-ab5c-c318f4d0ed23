#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()区域编辑页面#end

#define css()
#end

#define content()
<div style="margin: 15px;">
	<div class="demoTable">
		<form class="layui-form layui-form-pane" action="" method="post">
			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>区域级别</label>
				<div class="layui-input-block">
					<select name="areaLv" id="areaLv" lay-verify="required" lay-filter="typeSelect">
						<option value="" >请选择区域级别</option>
						#if(area.areaLv??==1)
						<option value="1" #if(area.areaLv??==1) selected #end>国家</option>
						#end
						<option value="2" #if(area.areaLv?? == 2) selected #end>省</option>
						<option value="3" #if(area.areaLv?? == 3) selected #end>市</option>
						<option value="4" #if(area.areaLv?? == 4) selected #end >区/县</option>
						<option value="5" #if(area.areaLv?? == 5) selected #end >街道/镇/乡</option>
					</select>
				</div>
			</div>
<!--			<div class="layui-form-item">-->
<!--				<label class="layui-form-label"><span>*</span>区域ID</label>-->
<!--				<div class="layui-input-block">-->
<!--					<input type="text" name="id" #(area.id?? != null ?'readonly="readonly"':'') class="layui-input" lay-verify="number" value="#(area.Id??)" placeholder="请输入区域ID">-->
<!--				</div>-->
<!--			</div>-->

			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>区域名称</label>
				<div class="layui-input-block">
					<input type="text" name="areaName" class="layui-input" lay-verify="required" value="#(area.areaName??)" placeholder="请输入区域名称">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><span>*</span>区域编号</label>
				<div class="layui-input-block">
					<input type="text" name="areaCode" class="layui-input" lay-verify="number" value="#(area.areaCode??)" placeholder="请输入区域编号">
				</div>
			</div>

			<div id="pdiv">

			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">备注信息</label>
				<div class="layui-input-block">
					<textarea name="remarks" class="layui-textarea" rows="8" placeholder="请输入备注说明">#(area.remarks??)</textarea>
				</div>
			</div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<input type="hidden" name="operatingType" value="#(operatingType??)">
					<input type="hidden" name="id" value="#(area.id??)">
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script>
	layui.use(['form','laytpl','layer'], function() {
		var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer;
		//监听区域级别select
		form.on('select(typeSelect)',function(data){
			//console.log(data.value);
			if(data.value==='2'){
				$("#pdiv").empty();
			}else if(data.value==='3'){
				$("#pdiv").empty();
				var getTpl = cityTpl.innerHTML;
				$("#pdiv").append(getTpl);
			}else if(data.value==='4'){
				$("#pdiv").empty();
				var getTpl = countyTpl.innerHTML;
				$("#pdiv").append(getTpl);
			}else if(data.value==='5'){
				$("#pdiv").empty();
				var getTpl = streetTpl.innerHTML;
				$("#pdiv").append(getTpl);
			}
			form.render('select');
		});

		//监听省 select
		form.on('select(provinceSelect)',function(data){
			//console.log(data.value);
			//判断市select是否存在，如果存在则动态加载
			if($("#city").length>0){
				getArea(data.value,"#city");
			}
		});

		//监听市 select
		form.on('select(citySelect)',function(data){
			//console.log(data.value);
			//判断区/县select是否存在，如果存在则动态加载
			if($("#county").length>0){
				getArea(data.value,"#county");
			}
		});

		//获取市、县方法 pId:父级id selectId要赋值的 select id属性值
		function getArea(pId,selectId){
			$.getJSON("#(ctx)/area/getArea/"+pId,function(data){
				//console
				var optionstring=""
				$.each(data,function(i,item){
					if(selectId==='#city'){
						var sed="";
						if(null!="#(cityId??)" && ""!="#(cityId??)" ){
							item["id"]=="#(cityId??)"?sed="selected":"";
						}

					}

					if(selectId==='#county'){
						var sed="";
						if(null!="#(countyId??)" && ""!="#(countyId??)" ){
							item["id"]=="#(countyId??)"?sed="selected":"";
						}
					}

					//获取默认选中属性
					optionstring+="<option value=\"" + item["id"] + "\" "+sed+"  >" + item["areaName"] + "</option>";
				});
				console.log(optionstring);
				$(selectId).html('<option value="">请选择</option>' + optionstring);

				if(selectId==='#city'){
					if(null!="#(cityId??)" && ""!="#(cityId??)" ){
						if($("#county").length>0){
							getArea("#(cityId??)","#county");
						}
					}
				}


				//这个很重要
				form.render('select');
			});
		};

		//监听提交
		form.on('submit(saveBtn)', function(data){
			//layer.msg(JSON.stringify(data.field));
			//console.log($(data.form).serialize());
			//提交表单数据

			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/saveArea',
				data:$(data.form).serialize(),
				notice: true,
				loadFlag: true,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
						parent.layui.table.reload('areaTable');
					}
				},
				complete : function() {
				}
			});
			return false;
		});

		typeSelect=function(data){
			if(data==='2'){
				$("#pdiv").empty();
			}else if(data==='3'){
				$("#pdiv").empty();
				var getTpl = cityTpl.innerHTML;
				$("#pdiv").append(getTpl);
			}else if(data==='4'){
				$("#pdiv").empty();
				var getTpl = countyTpl.innerHTML;
				$("#pdiv").append(getTpl);
			}else if(data==='5'){
				$("#pdiv").empty();
				var getTpl = streetTpl.innerHTML;
				$("#pdiv").append(getTpl);
			}
			form.render('select');
		};

		$(function(){

			var areaLv=$("#areaLv").val();
			typeSelect(areaLv);

			var provinceId=$("#province").val();
			if($("#city").length>0){
				getArea(provinceId,"#city");
			}
		});


	});
</script>
<script type="text/html" id="cityTpl">
	<div class="layui-form-item">
		<label class="layui-form-label">所属省</label>
		<div class="layui-input-block">
			<select name="province" id="province" lay-verify="required" lay-filter="provinceSelect">
				<option value="">请选择</option>
				#for(province : provinceList)
				<option value="#(province.getId())" #(province.getId()?? == provinceId ?'selected':'') >#(province.areaName)</option>
				#end
			</select>
		</div>
	</div>
</script>
<script type="text/html" id="countyTpl">
	<div class="layui-form-item">
		<label class="layui-form-label">所属省</label>
		<div class="layui-input-block">
			<select name="province" id="province" lay-verify="required" lay-filter="provinceSelect">
				<option value="">请选择</option>
				#for(province : provinceList)
				<option value="#(province.getId())" #(province.getId()?? == provinceId ?'selected':'') >#(province.areaName)</option>
				#end
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">所属市</label>
		<div class="layui-input-block">
			<select name="city" id="city" lay-verify="required" lay-filter="citySelect">
				<option value="">请选择</option>
			</select>
		</div>
	</div>
</script>
<script type="text/html" id="streetTpl">
	<div class="layui-form-item">
		<label class="layui-form-label">所属省</label>
		<div class="layui-input-block">
			<select name="province" id="province"  lay-verify="required" lay-filter="provinceSelect">
				<option value="">请选择</option>
				#for(province : provinceList)
				<option value="#(province.getId())" #(province.getId()?? == provinceId ?'selected':'') >#(province.areaName)</option>
				#end
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">所属市</label>
		<div class="layui-input-block">
			<select name="city" id="city" lay-verify="required" lay-filter="citySelect">
				<option value="">请选择</option>
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">所属区/县</label>
		<div class="layui-input-block">
			<select name="county" id="county" lay-verify="required" lay-filter="townSelect">
				<option value="">请选择</option>
			</select>
		</div>
	</div>
</script>
#end