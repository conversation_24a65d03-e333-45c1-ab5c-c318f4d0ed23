#include("/template/common/layout/_part_layout.html")
#@part()

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	;
	
	//树属性配置
	var setting = {
   		check:{enable:true, chkboxType: {"Y":"ps","N":"s"}, nocheckInherit:true},
   		view:{selectedMulti:false},
   		data:{simpleData:{enable:true}},
   		async: {enable:true, type:"post", url:"#(ctxPath)/user/userRolesTree?userId="+'#(model.id??"")'}
   	};
	
	// 初始化树结构
   	var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var checkedNodes = zTreeObj.getCheckedNodes(); //获取 zTree 当前被勾选中的节点数据集合，返回的类型:Array(JSON)
		var roleIdsArray = new Array(checkedNodes.length);//定义角色ID数组
		for (var i = 0; i < checkedNodes.length; i++) {
			roleIdsArray[i] = checkedNodes[i].id;
        }
		$("#roleIds").val(roleIdsArray.toString());
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/user/saveUser',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		window.location.reload();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row layui-col-space10">
	<div class="layui-col-xs3 layui-col-sm3 layui-col-md3">
		<fieldset class="layui-elem-field layui-field-title" style="display:block;">
			<legend>角色列表</legend>
			<div id="zTreeDiv" class="ztree" style="height:310px;overflow:auto;"></div>
		</fieldset>
	</div>
	<div class="layui-col-xs9 layui-col-sm9 layui-col-md9">
		<div style="margin-bottom:23px;"></div>
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>登陆帐号</label>
					<div class="layui-input-inline">
						<input type="text" name="userName" class="layui-input" lay-verify="required" value="#(model.userName??'')" #if(model.userName??)disabled="disabled"#end placeholder="请输入用户名">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>登陆密码</label>
					<div class="layui-input-inline">
						<input type="password" name="password" class="layui-input" lay-verify="required" value="#(model.password??'')" #if(model.password??)disabled="disabled"#end placeholder="请输入密码">
					</div>
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??'')">
					<input type="hidden" id="roleIds" name="roleIds" value="">
					<input type="hidden" name="orgId" value="#(model.orgId??'')">
					<input type="hidden" name="employeeId" value="#(model.employeeId??'')">
					<input type="hidden" name="userType" value="#(model.userType??'ordinary_user')">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="closeEditTab();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end