#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()行政区域管理首页#end

#define css()
#end

#define content()
<div style="margin: 15px;">
	<div class="demoTable">

		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
			国:
			<div class="layui-inline">
				<select name="countryId" id="countryId" lay-filter="countrySelect">
					<option value="">请选择</option>
					#for(country : countryList)
					<option value="#(country.id??)">#(country.areaName??)</option>
					#end
				</select>
			</div>
			省:
			<div class="layui-inline">
				<select name="provinceId" id="provinceId" lay-filter="provinceSelect">
					<option value="">请选择</option>
				</select>
			</div>
			市:
			<div class="layui-inline">
				<select name="cityId" id="cityId" lay-filter="citySelect">
					<option value="">请选择</option>
				</select>
			</div>
			区/县:
			<div class="layui-inline">
				<select name="townId" id="townId" lay-filter="townSelect">

				</select>
			</div>
			区域名称:
			<div class="layui-inline">
				<input id="areaId" name="areaName" class="layui-input">
			</div>
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("main:area:addBtn")
			<button type="button" id="addBtn" class="layui-btn">添加</button>
			#end
			#shiroHasPermission("main:area:batchDelBtn")
			<button type="button" id="batchDelBtn" class="layui-btn">批量作废</button>
			#end
		</form>

	</div>
	<table id="area_page_table" lay-filter="areaTable"></table>
</div>
#end

#define js()
<!-- ID栏替换模板 -->
<script type="text/html" id="serialNumberTpl">
	{{d.LAY_TABLE_INDEX+1}}
</script>
<!-- 操作栏替换模板 -->
<script type="text/html" id="actionBar">
	#shiroHasPermission("main:area:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("main:area:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script type="text/html" id="typeBar">
	#[[
	{{#if(d.areaLv==1){}}
	国家
	{{#}else if(d.areaLv==2){}}
	省
	{{#}else if(d.areaLv==3){}}
	市
	{{#}else if(d.areaLv==4){}}
	区/县
	{{#}else if(d.areaLv==5){}}
	街道/乡/镇
	{{#}}}
	]]#
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		areaLoad(null);

		sd=form.on("submit(search)",function(data){
			// JSON.stringify(data.field) {"member.name":"张杰","member.sex":"male","checkinRecord.status":"already_check_in"}
			//loadMemberCheckinRecord(data.field);
			areaLoad(data.field);
			return false;
		});


		function areaLoad(data){
			table.render({
				id : 'areaTable'
				,elem : '#area_page_table'
				,method : 'POST'
				,where : data
				,limit : 15
				,limits : [15,30,45,50]
				,url : '#(ctxPath)/area/areaPage'
				,cellMinWidth: 80 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
				,cols: [[
					{type: 'checkbox', fixed: 'left'}
					,{field:'areaName', title: '名称', align: 'center', unresize: true} //width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
					,{field:'areaCode', title: '区域编码', align: 'center', unresize: true} //width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
					,{field:'areaLv', title: '级别', align: 'center', unresize: true,templet:"#typeBar"}
					,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:MM:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};
		// 监听操作栏目
		table.on('tool(areaTable)',function(obj){
			if (obj.event === 'edit') { // 编辑
				layerShow("编辑区域" ,"#(ctxPath)/area/editArea?id="+obj.data.id ,'500' ,'600') ;
			} else if (obj.event === 'del') { // 作废
				layer.confirm("确定要作废吗？",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/area/del?id=' + obj.data.id,
						notice: true,
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								layer.close(index);
								table.reload('areaTable');
							}
						},
						complete : function() {
						}
					});


				}) ;
			}
		});

		getAreaCheckedData=function(obj){
			var data=table.checkStatus('areaTable').data;


			return data;
		}

		//批量作废
		$("#batchDelBtn").on('click',function () {
			var data=getAreaCheckedData();

			if(data==null || data=='' || data.length==0){
				layer.msg('请勾选要作废的数据!', {icon: 5, offset: 'auto'});
				return false;
			}

			layer.confirm("确定要作废吗？",function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/area/batchDel',
					data:{"data":JSON.stringify(data)},
					notice: true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							layer.close(index);
							table.reload('areaTable');
						}
					},
					complete : function() {
					}
				});
			});


			return false;
		});

		//点击国家 加载省
		form.on("select(countrySelect)",function(data){
			getArea(data.value,"#provinceId");
		});

		//点击省 加载市
		form.on("select(provinceSelect)",function(data){
			getArea(data.value,"#cityId");
		});

		//点击市 加载县
		form.on("select(citySelect)",function(data){
			getArea(data.value,"#townId");
		});

		//获取市、县方法 pId:父级id selectId要赋值的 select id属性值
		function getArea(pId,selectId){
			$.getJSON("#(ctxPath)/area/getArea/"+pId,function(data){
				var optionstring=""
				$.each(data,function(i,item){
					//获取默认选中属性
					optionstring+="<option value=\"" + item["id"] + "\"  >" + item["areaName"] + "</option>";
				});
				$(selectId).html('<option value="">请选择</option>' + optionstring);
				//这个很重要
				form.render('select');
			});
		};

		$("#addBtn").on('click',function(){
			layerShow("区域添加" ,'#(ctxPath)/area/addArea' ,'500' ,'600') ;
		});
		//form.render();
	});
</script>
#end