#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#(APP.name)#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
	.layui-nav .layui-badge, .layui-nav .layui-badge-dot {
		position: absolute;
		right: 18px;
		top: 29px;
	}
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/vip_comm.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/jquery.cookie.js"></script>
<script type="text/javascript">
layui.use(['layer','vip_nav','element','form'], function () {
	// 操作对象
	var layer = layui.layer
		,vipNav = layui.vip_nav
	    ,$ = layui.jquery
		,element = layui.element
		,form=layui.form
	    ;
//	 	顶部左侧菜单生成 [请求地址,过滤ID,是否展开,携带参数]
//	     vipNav.top_left('#(ctxPath)/system/res/menuTop','side-top-left',false);
//	     you code ...

		$('#infoBtn').on('click', function() {
			pop_show('个人资料', '#(ctxPath)/orgEmployee/info', 650, 600);
		});
		$('#modPwdBtn').on('click', function() {
			pop_show('修改密码', '#(ctxPath)/user/modifyPwd', 450, 300);
		});

	//定时器推送未读的消息
	getUnReadMessageCount = function(){
		util.sendAjax({
			url:'#(ctxPath)/main/msg/getNotReadMsgCount',
			type:'post',
			data:JSON.stringify({}),
			notice:false,
			success : function (result) {
				if(result.state == 'ok' && result.notReadMsgCount > 0){
					//$("#unReadMessageCount").html(result.notReadMsgCount);
					$("#unReadMessageCount").addClass("layui-badge-dot");
				}else{
					if($("#unReadMessageCount").hasClass("layui-badge-dot")){
						$("#unReadMessageCount").removeClass("layui-badge-dot");
					}
				}
			}
		});
	}

	$(function(){
		//getUnReadMessageCount();
	});

	//setInterval("getUnReadMessageCount();",10000);

	//选择基地
	baseClick=function (baseName,baseId) {
		$("#currBaseName").text(baseName);
		$("#currBaseId").val(baseId);
		$.cookie('baseId', baseId);
		//刷新当前tab
		var src=$(".layui-tab-item.layui-show").find("iframe").attr("src");
		$(".layui-tab-item.layui-show").find("iframe").attr("src",src);
		$("#baseDl").removeClass("layui-show");
		$("#baseDl dd[data-id='"+baseId+"']").addClass("layui-this").siblings().removeClass("layui-this");
	}

	//tab切换事件刷新tab
	element.on('tab(card)', function(data){
		var src=$(".layui-tab-item.layui-show").find("iframe").attr("src");
		//$(".layui-tab-item.layui-show").find("iframe").attr("src",src);
	});

	addBase=function(baseId,baseName){
		var str='<dd data-id="'+baseId+'">' +
				'<a href="javascript:;" href-url="" onclick="baseClick(\''+baseName+'\',\''+baseId+'\')">'+baseName+'</a>' +
				'</dd>';
		$("#baseDl").prepend(str);
	}



	$(function () {
		#if (baseList.size()??0>0)
			$.cookie('baseId', '#(baseList[0].id)');
		#end
	})
});
</script>
#end

#define content()
<div class="layui-layout layui-layout-admin">
	<!-- 添加skin-1类可手动修改主题为纯白，添加skin-2类可手动修改主题为蓝白 -->
	<!-- header -->
	<div class="layui-header my-header">
		<a href="#(ctxPath)">
			<div class="my-header-logo">#(APP.name)</div>
		</a>
		<div class="my-header-btn">
			<button id="showHideBtn" class="layui-btn layui-btn-small btn-nav">
				<i class="layui-icon">&#xe65f;</i>
			</button>
		</div>
		
		<!-- 顶部左侧添加选项卡监听 -->
		<ul class="layui-nav" lay-filter="side-top-left"></ul>
		
		<!-- 顶部右侧添加选项卡监听 -->
		<ul class="layui-nav my-header-user-nav" lay-filter="side-top-right">
<!-- 			<li class="layui-nav-item" style="margin-right:10px;"> -->
<!-- 				<a class="name" href="javascript:;"> -->
<!-- 					<i class="layui-icon">&#xe629;</i>主题 -->
<!-- 				</a> -->
<!-- 				<dl class="layui-nav-child"> -->
<!-- 					<dd data-skin="0"> -->
<!-- 						<a href="javascript:;">默认</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="1"> -->
<!-- 						<a href="javascript:;">纯白</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="2"> -->
<!-- 						<a href="javascript:;">蓝白</a> -->
<!-- 					</dd> -->
<!-- 				</dl> -->
<!-- 			</li> -->
			<li class="layui-nav-item" style="margin-right: 15px;margin-top: 5px;">
				<a href="javascript:;" href-url="#(ctxPath)/main/msg/index">
					<i class="layui-icon">&#xe667;</i><span id="unReadMessageCount"></span>
				</a>
			</li>
			<li class="layui-nav-item" style="margin-right:20px;">
				<a class="name" href="javascript:;" style="width: 250px;">
					当前基地：<span id="currBaseName">#if(baseList.size()??0>0)#(baseList[0].baseName)#end</span>
					<input type="hidden" id="currBaseId" value="#if(baseList.size()??0>0)#(baseList[0].id)#end">
				</a>
				<dl class="layui-nav-child" id="baseDl">
					#for(base:baseList)
						<dd data-id="#(base.id)">
							<a href="javascript:;" href-url="" onclick="baseClick('#(base.baseName)','#(base.id)')">#(base.baseName)</a>
						</dd>
					#end
				</dl>
			</li>
			<li class="layui-nav-item" style="margin-right:10px;">
				<a class="name" href="javascript:;">
					<img class="layui-circle" src="#(ctxPath)/static/img/user.png" alt="logo"> 
					#shiroPrincipal() 
						#(principal) 
					#end 
				</a>
				<dl class="layui-nav-child">
					<dd>
						<a id="infoBtn" href="javascript:;" href-url="">个人资料</a>
					</dd>
					<dd>
						<a id="modPwdBtn" href="javascript:;" href-url="">修改密码</a>
					</dd>
				</dl>
			</li>
			<li class="layui-nav-item"><a href="#(ctxPath)/logout" class="layui-btn layui-btn-danger layui-btn-small btn-nav" href-url="">退出</a></li>
		</ul>
	</div>
	
	<!-- side -->
	<div class="layui-side my-side">
		<div class="layui-side-scroll">
			<!-- 左侧主菜单添加选项卡监听 -->
			<ul class="layui-nav layui-nav-tree" lay-filter="side-main">
				#shiroHasPermission("main:basicData")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-user-o"></i>基础数据管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("main:memberTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/cardtype/index"><i class="fa fa-bars"></i>会员卡类别</a>
						</dd>
						#end
						#shiroHasPermission("main:cardGiveScheme")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/cardGiveScheme/index"><i class="fa fa-bars"></i>赠送方案</a>
						</dd>
						#end
						#shiroHasPermission("main:cardDeductScheme")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/cardDeductScheme/index"><i class="fa fa-bars"></i>扣费方案</a>
						</dd>
						#end
						#shiroHasPermission("main:appManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/appJoin/index"><i class="fa fa-credit-card"></i>应用管理</a>
						</dd>
						#end
						#shiroHasPermission("main:baseManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/base/index"><i class="fa fa-credit-card"></i>基地管理</a>
						</dd>
						#end
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/firstLevel/index"><i class="fa fa-credit-card"></i>房间类型一级分类</a>
						</dd>
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/secondLevel/index"><i class="fa fa-credit-card"></i>房间类型二级分类</a>
						</dd>
						#shiroHasPermission("main:roomTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/roomType/index"><i class="fa fa-credit-card"></i>房间类型管理</a>
						</dd>
						#end
						#shiroHasPermission("main:roomTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/baseRoomType/index"><i class="fa fa-credit-card"></i>房间类型管理(新)</a>
						</dd>
						#end
						#shiroHasPermission("main:bedTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bedType/index"><i class="fa fa-credit-card"></i>床位类型管理</a>
						</dd>
						#end
						#shiroHasPermission("main:bedStatusManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bedStatus/index"><i class="fa fa-credit-card"></i>床位状态管理</a>
						</dd>
						#end
						#shiroHasPermission("main:bedMarkupTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bedMarkupType/index"><i class="fa fa-credit-card"></i>选房费管理</a>
						</dd>
						#end
						#shiroHasPermission("main:buildingRoomManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/buildingRoomManage/index"><i class="fa fa-credit-card"></i>楼栋房间管理</a>
						</dd>
						#end
						#shiroHasPermission("main:buildingRoomManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/markupScheme/index"><i class="fa fa-credit-card"></i>选房费规则</a>
						</dd>
						#end
						#shiroHasPermission("main:bedQuery")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bedQuery/index"><i class="fa fa-credit-card"></i>床位查询</a>
						</dd>
						#end
						#shiroHasPermission("main:branchOfficeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/branchOffice/index"><i class="fa fa-credit-card"></i>分公司管理</a>
						</dd>
						#end
						#shiroHasPermission("main:contratTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/contratType/index"><i class="fa fa-credit-card"></i>合同类型管理</a>
						</dd>
						#end
						#shiroHasPermission("main:areaManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/area/index"><i class="fa fa-building-o"></i>区域管理</a>
						</dd>
						#end
						#shiroHasPermission("main:carTypeManager")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/carType/index"><i class="fa fa-building-o"></i>车型管理</a>
						</dd>
						#end
						#shiroHasPermission("main:touristRouteManager")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/touristRoute/index"><i class="fa fa-building-o"></i>旅游线路管理</a>
						</dd>
						#end
						#shiroHasPermission("main:syncRecordManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/syncRecord/index"><i class="fa fa-building-o">同步记录管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:bedDynamicRecordManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/syncRecord/bedDynamicRecordIndex"><i class="fa fa-building-o">床位动态信息</i></a>
						</dd>
						#end
						#shiroHasPermission("main:bedDynamicTableManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/syncRecord/bedDynamicRecordTableIndex"><i class="fa fa-building-o">房态一览表</i></a>
						</dd>
						#end
						#shiroHasPermission("main:expectedCheckinTableManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/syncRecord/expectedCheckinTableIndex"><i class="fa fa-building-o">基地预计入住表</i></a>
						</dd>
						#end
						#shiroHasPermission("main:bacthSyncManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/syncRecord/batchSyncIndex"><i class="fa fa-building-o">批量同步</i></a>
						</dd>
						#end
						#shiroHasPermission("main:baseSettingManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/baseAdvanceBookSetting/index"><i class="fa fa-building-o">基地配置</i></a>
						</dd>
						#end
						#shiroHasPermission("main:mattressTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/mattressType/index"><i class="fa fa-building-o">床垫类型管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:bookChannelManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bookChannel/index"><i class="fa fa-building-o">预订渠道管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:cardYearLimitManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/cardYearLimit/index"><i class="fa fa-building-o">会员卡年限类型管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:functionSwitchManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/functionSwitch/index"><i class="fa fa-building-o">功能配置开关管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:punishRuleManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/punishRule/index"><i class="fa fa-building-o">处罚规则管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:deductPointsConfigManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/deductPointsConfig/index"><i class="fa fa-building-o">点数扣除值配置</i></a>
						</dd>
						#end
						#shiroHasPermission("main:dictionariesManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/dictionaries/index"><i class="fa fa-building-o">字典管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:goodTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/goodType/index"><i class="fa fa-building-o">商品类型管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:goodModelManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/goodModel/index"><i class="fa fa-building-o">商品管理</i></a>
						</dd>
						#end
						#shiroHasPermission("main:bookkeepingTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bookkeepingType/index"><i class="fa fa-building-o">记账类型</i></a>
						</dd>
						#end
						#shiroHasPermission("main:paymentWayManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/paymentWay/index"><i class="fa fa-building-o">付款方式</i></a>
						</dd>
						#end
						#shiroHasPermission("main:baseGoodConfigManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/baseGoodConfig/index"><i class="fa fa-building-o">基地商品配置</i></a>
						</dd>
						#end
						#shiroHasPermission("main:baseDoorManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/baseDoor/index"><i class="fa fa-building-o">基地公共门</i></a>
						</dd>
						#end
						#shiroHasPermission("main:orgCategory")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/orgCategory/index"><i class="fa fa-building-o">人事机构维度</i></a>
						</dd>
						#end
						#shiroHasPermission("main:businessEntity")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/businessEntity/index"><i class="fa fa-building-o">业务实体</i></a>
						</dd>
						#end
						#shiroHasPermission("main:deductMultipleConfig")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/deductMultipleConfig/index"><i class="fa fa-building-o">财务分时扣费配置</i></a>
						</dd>
						#end
						#shiroHasPermission("main:bankInfo")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/bank/index"><i class="fa fa-building-o">银行信息</i></a>
						</dd>
						#end
						#shiroHasPermission("main:costTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/costType/index"><i class="fa fa-building-o">费用类型</i></a>
						</dd>
						#end
						#shiroHasPermission("main:smsType")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/smsType/index"><i class="fa fa-building-o">短信类型</i></a>
						</dd>
						#end
						#shiroHasPermission("main:smsType")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/userGroup/index"><i class="fa fa-building-o">分组管理</i></a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("main:warehouseDataManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>仓储基本数据</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("main:warehouseManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/wms/warehouse/index"><i class="fa fa-list-ul"></i>仓库管理</a>
						</dd>
						#end
						#shiroHasPermission("main:supplierManage")
                        <dd>
                            <a href="javascript:;" href-url="#(ctxPath)/wms/supplier/index"><i class="fa fa-list-ul"></i>供应商管理</a>
                        </dd>
						#end
						#shiroHasPermission("main:supplierLinkManage")
						<dd>
                            <a href="javascript:;" href-url="#(ctxPath)/wms/supplier/supplierLinkIndex"><i class="fa fa-list-ul"></i>供应商联系人</a>
                        </dd>
                        #end
						#shiroHasPermission("main:stockTypeManage")
                        <dd>
                            <a href="javascript:;" href-url="#(ctxPath)/wms/stockType/index"><i class="fa fa-list-ul"></i>货物分类</a>
                        </dd>
						#end
						#shiroHasPermission("main:labelManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/wms/label/index"><i class="fa fa-list-ul"></i>货物标签</a>
						</dd>
						#end

						#shiroHasPermission("main:stockModelManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/wms/stockModel/index"><i class="fa fa-list-ul"></i>货物型号</a>
						</dd>
						#end
						#shiroHasPermission("wms:unit")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/wms/unit/index"><i class="fa fa-list-ul"></i>计量单位</a>
						</dd>
						#end
					</dl>
				</li>
				#end

				#shiroHasPermission("main:csamManager")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>资产管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("main:csam:typeManager")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/csam/assetTypeIndex"><i class="fa fa-list-ul"></i>类型</a>
						</dd>
						#end
						#shiroHasPermission("main:csam:typeFieldManager")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/csam/assetTypeFieldIndex"><i class="fa fa-list-ul"></i>类型属性</a>
						</dd>
						#end
						#shiroHasPermission("main:csam:brandManager")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/main/csam/assetBrandIndex"><i class="fa fa-list-ul"></i>品牌</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("main:food")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>点餐管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("food:foodCategory")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/food/categoryIndex"><i class="fa fa-list-ul"></i>菜类别管理</a>
						</dd>
						#end
						#shiroHasPermission("food:foodInfo")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/food/infoIndex"><i class="fa fa-list-ul"></i>菜信息录入</a>
						</dd>
						#end
					</dl>
				</li>
				#end

				#shiroHasPermission("main:food")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>巡检管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("food:foodCategory")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mainExamine/typeIndex"><i class="fa fa-list-ul"></i>巡检问题类型</a>
						</dd>
						#end
						#shiroHasPermission("food:foodInfo")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mainExamine/problemIndex"><i class="fa fa-list-ul"></i>巡检问题</a>
						</dd>
						#end
						#shiroHasPermission("food:foodInfo")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mainExamine/schemeIndex"><i class="fa fa-list-ul"></i>巡检方案</a>
						</dd>
						#end
					</dl>
				</li>
				#end

				#shiroHasPermission("main:memberMall")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>会员商城</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("main:memberMallManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mall/productType"><i class="fa fa-list-ul"></i>商品类型</a>
						</dd>
						#end
						#shiroHasPermission("main:memberMallSaleManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mall/productSaleApply/index"><i class="fa fa-list-ul"></i>商品上架申请</a>
						</dd>
						#end
					</dl>
				</li>
				#end

				#shiroHasPermission("main:systemSet")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>系统设置</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("main:permissionMenuManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/menu/index"><i class="fa fa-list-ul"></i>菜单权限</a>
						</dd>
						#end
						#shiroHasPermission("main:RoleManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/role/index"><i class="fa fa-user-circle"></i>角色管理</a>
						</dd>
						#end
						#shiroHasPermission("main:userManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/user/index"><i class="fa fa-sliders"></i>帐号管理</a>
						</dd>
						#end
						#shiroHasPermission("main:dictManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/dict/index"><i class="fa fa-tasks"></i>系统字典</a>
						</dd>
						#end
					</dl>
				</li>
				#end
			</ul>
		</div>
	</div>
	
	<!-- body -->
	<div class="layui-body my-body">
		<div class="layui-tab layui-tab-card my-tab" lay-filter="card" lay-allowClose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" lay-id="1"><span><i class="layui-icon">&#xe68e;</i>首页</span></li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<iframe id="iframe" src="#(ctxPath)/welcome" frameborder="0"></iframe>
				</div>
			</div>
		</div>
	</div>
	
	<!-- footer -->
	<div class="layui-footer my-footer" style="height: 30px;">
		<p style="line-height: 30px;">
			<!--<a href="#(APP.orgWebsite)" target="_blank">#(APP.org)</a>
			&nbsp;&nbsp;&&nbsp;&nbsp;
			<a href="javascript:;" target="_blank">#(APP.name)</a>-->
			&nbsp;&nbsp;&nbsp;Copyright #(thisYear??) #(APP.copyRight)
		</p>
		<!--		<p></p>-->
	</div>
</div>

<!-- 右键菜单 -->
<div class="my-dblclick-box none">
	<table class="layui-tab dblclick-tab">
		<tr class="card-refresh">
			<td><i class="layui-icon">&#x1002;</i>刷新当前标签</td>
		</tr>
		<tr class="card-close">
			<td><i class="layui-icon">&#x1006;</i>关闭当前标签</td>
		</tr>
		<tr class="card-close-all">
			<td><i class="layui-icon">&#x1006;</i>关闭所有标签</td>
		</tr>
	</table>
</div>
#end