/*弹出层*/
/*
	参数解释：
	title	标题
	url		请求的url
	id		需要操作的数据id
	w		弹出层宽度（缺省调默认值）
	h		弹出层高度（缺省调默认值）
*/
function pop_show(title,url,w,h,f){
    var width,height;
    if (w && h && w == '100%' && h == '100%') {
        width = w;
        height = h;
    }else{
        if (title == null || title == '') {
            title=false;
        };
        if (url == null || url == '') {
            url="404.html";
        };
        if (w == null || w == '') {
            w=800;
        };
        if (h == null || h == '') {
            h=(layui.$(window).height() - 50);
        };
        var bodyHeight = window.top.layui.jquery(window).height();
        if (h > bodyHeight) {
            h = bodyHeight - 20;
        }
        width = w + 'px';
        height = h +'px';
    }
    layer.open({
        type: 2,
        area: [width, height],
        fix: false, //不固定
        maxmin: true,
        shadeClose: false,
        shade:0.4,
        title: title,
        content: url,
        end: f
    });
}

/*关闭弹出框口*/
function pop_close(){
	var index = parent.layer.getFrameIndex(window.name);
	parent.layer.close(index);
}

/*表格重载*/
function tableReload(tableId, tableParams){
	if(tableId!=null && tableId!=''){
		layui.table.reload(tableId, {
			//分页重新从第 1 页开始
			page : {
				curr : 1
			}
		//设定异步数据接口的额外参数
		,where : tableParams
		});
	}
}

var util = {};
util.sendAjax = function(obj) {
    var url = obj.url;
    var data = obj.data;
    var async = obj.async;
    var loadFlag = obj.loadFlag==undefined?false:obj.loadFlag;
    var type = obj.type;
    var cache = obj.cache;
    var successfn = obj.success;
    var unSuccess = obj.unSuccess;
    var completefn = obj.complete;
    var notice = obj.notice;
    async = (async==null || async==="" || typeof(async)=="undefined")? "true" : async;
    cache = (cache==null || cache==="" || typeof(cache)=="undefined")? "false" : cache;
    type = (type==null || type==="" || typeof(type)=="undefined")? "GET" : type.toLocaleUpperCase();
    data = (data==null || data==="" || typeof(data)=="undefined")? {"date": new Date().getTime()} : data;
    notice = (notice==null || notice==="" || typeof(notice)=="undefined")? "true" : notice;

    if (successfn==null || successfn==="" || typeof(successfn)=="undefined") {
        successfn = function (info) {

        }
    }

    if (unSuccess==null || unSuccess==="" || typeof(unSuccess)=="undefined") {
        unSuccess = function (info) {

        }
    }

    if (completefn==null || completefn==="" || typeof(completefn)=="undefined") {
        completefn = function () {

        }
    }

    //POST,PUT 转化成json字符串
    if(type=="POST" || type=="PUT"){

    }
    
    layui.$.ajax({
        type: type,
        async: async,
        data: data,
        url: url,
        cache: cache,
        contentType : "application/x-www-form-urlencoded;charset=UTF-8",
        dataType: 'json',
        timeout:30000,
        beforeSend: function(XMLHttpRequest){
            if (loadFlag) {
                layer.load();
            }
        },
        success: function(json){
            if (json.state == 'ok') {
            	if (notice) {
                    window.top.layer.msg(json.msg, {icon: 1, offset: 'auto'});
				}
                successfn(json);
            } else {
                window.top.layer.msg(json.msg, {icon: 2, offset: 'auto'});
                unSuccess(json);
            }
        },
        error: function(XMLHttpRequest, status, error) {
            if (XMLHttpRequest.status == 401) {
                layer.msg('请登录后进行操作', {icon: 5});
            } else if (XMLHttpRequest.status == 403) {
                layer.msg('没有权限', {icon: 5});
            } else if (XMLHttpRequest.status == 404) {
                layer.msg('页面未找到', {icon: 5});
            } else if (XMLHttpRequest.status == 500) {
                layer.msg('服务异常，请稍候重试', {icon: 5});
            } else {
                layer.msg('网络异常，请检查网络连接', {icon: 5});
			}
        },
        complete :function(XMLHttpRequest, TS){
            completefn();
            if (loadFlag) {
            	layer.closeAll('loading');
            }
        }
    });
};

//移动电话验证，已做限制
util.isMobile = function(val) {
    var str = val.trim();
    if(str.length!=0){
        reg= /^(((13[0-9]{1})|(15[0-9]{1})|(17[0-9]{1})|(18[0-9]{1}))+\d{8})$/;
        if(!reg.test(str)){
            return false;
        }else{
            return true;
        }
    }else{
        return false;
    }
}

//邮箱验证
util.isEmail = function(val) {
    var str = val.trim();
    if(str.length!=0){
        reg=/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        if(!reg.test(str)){
            return false;
        }else{
            return true;
        }
    }else{
        return false;
    }
}

//邮编验证
util.isPostNo = function(val) {
    var str = val.trim();
    if(str.length!=0){
        reg=/^[1-9]d{5}(?!d)$/;
        if(!reg.test(str)){
            return false;
        }else{
            return true;
        }
    }else{
        return false;
    }
}


function layerShow(title, url, w, h){
    var width,height;
    if (w && h && w == '100%' && h == '100%') {
        width = w;
        height = h;
    } else {
        if (title == null || title == '') {
            title=false;
        };
        if (url == null || url == '') {
            url="404.html";
        };
        if (w == null || w == '') {
            w=800;
        };
        if (h == null || h == '') {
            h=($(window).height() - 50);
        };
        var bodyHeight = window.top.layui.jquery(window).height();
        if (h > bodyHeight) {
            h = bodyHeight - 20;
        }
        width = w + 'px';
        height = h +'px';
    }
    layui.layer.open({
        type: 2,
        area: [width, height],
        fix: true, //不固定
        maxmin: true,
        shade:0.4,
        title: title,
        content: url
    });
}
//时间戳的处理
dateFormat = function(d, format){

    if (d != null && d != undefined && d != '') {
        var date = new Date(d)
            ,ymd = [
            this.digit(date.getFullYear(), 4)
            ,this.digit(date.getMonth() + 1)
            ,this.digit(date.getDate())
        ]
            ,hms = [
            this.digit(date.getHours())
            ,this.digit(date.getMinutes())
            ,this.digit(date.getSeconds())
        ];

        format = format || 'yyyy-MM-dd HH:mm:ss';

        return format.replace(/yyyy/g, ymd[0])
            .replace(/MM/g, ymd[1])
            .replace(/dd/g, ymd[2])
            .replace(/HH/g, hms[0])
            .replace(/mm/g, hms[1])
            .replace(/ss/g, hms[2]);
    } else {
        return '' ;
    }

};

//数字前置补零
digit = function(num, length, end){
    var str = '';
    num = String(num);
    length = length || 2;
    for(var i = num.length; i < length; i++){
        str += '0';
    }
    return num < Math.pow(10, length) ? str + (num|0) : num;
};

/**
 * ================= 数据字典方法开始 =================
 */
function getDicts(type){
    var $ = layui.$;
    var dictObj = $('div[data-id="dict"]');  // 获取封装数据字典数据的div
    var dict = {};	// 封装数据字典数组对象
    $.each(dictObj.find('div'), function(i, o) {
        var key = $(o).attr('data-key');
        var typeObj = new Array(); // 用户封装数据字典value和label组成的json对象
        $.each($(o).find('p'), function(j, v) {
            var label = $(v).attr('data-label');
            var value = $(v).attr('data-value');
            var type = {};
            type[value] = label;
            typeObj.push(type);
        });
        dict[key] = typeObj;
    });
    return dict;
}

/**
 * @param value 数据字典的value值
 * @param type 数据字典类型
 * @returns label
 */
function dictLabel(value, type, str){
    // 返回Json对象,该对象里面封装了数据字典数组
    var dict = getDicts(type);  // {meter_type_enum:Array(3)} 0:{value: "hot", label: "热水表"}1:{value: "cold", label: "冷水表"}2:{value: "watt", label: "电表"}
    var labelList = dict[type];	// 通过类型获取数据字典数组
    for (var i in labelList) {  // 遍历数组，i为下标索引

        var labelObj = labelList[i]; // 通过下标索引获取封装数据字典value值和label的json对象

        if (value) {
            if (labelObj[value]) {
                return labelObj[value];
            }
        } else {
            if (str != null && str != '' && str != undefined) {
                return str;
            } else {
                return str;
            }
        }
    }
}
/**
 * ================= 数据字典方法结束 =================
 */

/**
 * 表格单击行
 * @param thisTable 当前表格对象
 * @param table layui table模块对象
 * @param callback 返回表格行数据的回调函数
 */
function clickRow(thisTable, table , callback) {
    var $ = layui.$;
    // 用于标识是否返回表格第一行数据
    var bl = false;
    var $tr = $(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr');

    $tr.on('click', function() {
        bl = true;
        // 数据行索引值
        var index = $(this).attr('data-index');
        // 首先清空表格所有行的背景样式
        $tr.removeClass('layui-table-click-tr');
        // 选择行添加背景颜色
        $(thisTable.elem.selector).parent().find(".layui-form .layui-table-main table tr:eq("+index+")").addClass('layui-table-click-tr');
        // 获取行数据
        var data = table.cache[thisTable.id][index == undefined ? 0 : index];
        // 回调函数
        if (typeof callback === 'function') {
            callback(data);
        }
    });

    // 加载数据表格完毕后执行
    if (!bl) {
        // 如果表格有数据执行回调，返回表格第一行数据
        if (table.cache[thisTable.id].length > 0) {
            $(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr:eq(0)').addClass('layui-table-click-tr');
            // 获取表格的第I一行数据
            var data = table.cache[thisTable.id][0];
            // 回调函数
            if (typeof callback === 'function') {
                callback(data);
            }
        }
    }
}

function clickRowNew(thisTable, table , bl , callback) {
    var $ = layui.$;
    // 用于标识是否返回表格第一行数据
    var $tr = $(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr');

    $tr.on('click', function() {
        bl = true;
        // 数据行索引值
        var index = $(this).attr('data-index');
        // 首先清空表格所有行的背景样式
        $tr.removeClass('layui-table-click-tr');
        // 选择行添加背景颜色
        $(thisTable.elem.selector).parent().find(".layui-form .layui-table-main table tr:eq("+index+")").addClass('layui-table-click-tr');
        // 获取行数据
        var data = table.cache[thisTable.id][index == undefined ? 0 : index];
        // 回调函数
        if (typeof callback === 'function') {
            callback(data);
        }
    });

    // 加载数据表格完毕后执行
    if (!bl) {
        // 如果表格有数据执行回调，返回表格第一行数据
        if (table.cache[thisTable.id].length > 0) {
            $(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr:eq(0)').addClass('layui-table-click-tr');
            // 获取表格的第I一行数据
            var data = table.cache[thisTable.id][0];
            // 回调函数
            if (typeof callback === 'function') {
                callback(data);
            }
        }
    }
}

/**
 * 表格单击行
 * @param thisTable 当前表格对象
 * @param table layui table模块对象
 * @param callback 返回表格行数据的回调函数
 */
function treeGridClickRow(thisTable,res , callback) {
    var $ = layui.$;
    // 用于标识是否返回表格第一行数据
    var bl = false;
    var $tr = $(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr');

    $.each(res.data,function (index,item) {
        if(item.checkinStatus==='book' && item.settleStatus==='2'){
            $(".layui-table-body").find("table").find("tr:eq("+index+")").attr("id",item.id);
        }
        $(thisTable.elem.selector).parent().find(".layui-form .layui-table-main table tr:eq("+index+")").attr("id",item.id);
    })


    $tr.on('click', function() {
        bl = true;
        // 数据行索引值
        var index = $(this).attr('data-index');
        // 首先清空表格所有行的背景样式
        $tr.removeClass('layui-table-click-tr');
        // 选择行添加背景颜色
        $(thisTable.elem.selector).parent().find(".layui-form .layui-table-main table tr:eq("+index+")").addClass('layui-table-click-tr');
        // 获取行数据
        var data = {'id':$(this).attr('id')};
        // 回调函数
        if (typeof callback === 'function') {
            callback(data);
        }
    });

    // 加载数据表格完毕后执行
    if (!bl) {
        // 如果表格有数据执行回调，返回表格第一行数据
        if ($tr.length > 0) {
            $(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr:eq(0)').addClass('layui-table-click-tr');
            // 获取表格的第I一行数据
            var data = {'id':$(thisTable.elem.selector).parent().find('.layui-form .layui-table-main table tr:eq(0)').attr('id')};
            // 回调函数
            if (typeof callback === 'function') {
                callback(data);
            }
        }
    }
}