package com.cszn.main.data.web.controller.mall;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.mall.MallProductCategoryService;
import com.cszn.integrated.service.api.mall.MallProductImageService;
import com.cszn.integrated.service.api.mall.MallProductService;
import com.cszn.integrated.service.entity.mall.MallProduct;
import com.cszn.integrated.service.entity.mall.MallProductCategory;
import com.cszn.integrated.service.entity.mall.MallProductImage;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.utils.HttpUtil;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping(value = "/mall/product",viewPath = "/modules_page/mall/product")
public class MallProductController extends BaseController {

    @Inject
    private MallProductService mallProductService;
    @Inject
    private MallProductCategoryService mallProductCategoryService;
    @Inject
    private MallProductImageService mallProductImageService;

    public void index(){

        List<MallProductCategory> categoryList=mallProductCategoryService.findAllCategory();

        setAttr("categoryList",categoryList);
        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MallProduct product=mallProductService.findById(id);

            setAttr("model",product);
        }
        List<MallProductCategory> categoryList=mallProductCategoryService.findAllCategory();

        setAttr("categoryList",categoryList);
        render("form.html");
    }


    public void pageList(){
        MallProduct product=getBean(MallProduct.class,"",true);
        Page<Record> page=mallProductService.pageList(getParaToInt("page"),getParaToInt("limit"),product);

        renderJson(new DataTable<Record>(page));
    }

    public void saveProduct(){
        MallProduct product=getBean(MallProduct.class,"",true);

        boolean flag=mallProductService.saveProduct(product, AuthUtils.getUserId());

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void saveProductImage(){
        MallProductImage image=getBean(MallProductImage.class,"",true);

        if(mallProductImageService.saveProductImage(image,null)){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void productImage(){
        String id=getPara("id");
        List<MallProductImage> imageList=mallProductImageService.findAllImageByProductId(id);

        setAttr("productId",id);

        setAttr("imageList",imageList);
        render("imgUpload.html");
    }

    public void deleteImg(){
        String id=getPara("id");

        int i= Db.update("update mall_product_image set del_flag='1' where id=? ",id);
        if(i>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void uploadImg(){
        UploadFile file = getFile("file");
        String productId=getPara("productId");

        try {
            Map<String,String> params=new HashMap<>();
            params.put("bucket","mall");

            Map<String,String> heads=new HashMap<>();
            heads.put("Content-Type","multipart/form-data");
            String result= HttpClientsUtils.httpPostFormMultipart(Global.fileUploadUrl,params,file.getFile(),null,"utf-8");
            if(result.startsWith("{") && result.endsWith("}")){
                JSONObject object= JSON.parseObject(result);
                if("ok".equals(object.getString("state"))){
                    JSONObject data=object.getJSONObject("data");
                    MallProductImage image=new MallProductImage();
                    image.setProductId(productId);
                    image.setImgName(data.getString("title"));
                    image.setImgUrl(data.getString("src"));
                    if(mallProductImageService.saveProductImage(image,null)){
                        renderJson(Ret.ok("msg","上传成功").put("data",image));
                        return;
                    }
                }else{
                    renderJson(Ret.fail("msg","上传接口响应失败"));
                    return;
                }
            }else{
                renderJson(Ret.fail("msg","上传接口调用失败"));
                return;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        renderJson(Ret.fail("msg","上传失败"));
    }

}
