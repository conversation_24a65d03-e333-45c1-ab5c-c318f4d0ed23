package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCardGiveSchemeService;
import com.cszn.integrated.service.entity.main.MainCardGiveScheme;
import com.cszn.integrated.service.entity.main.MainCardGiveSchemeRule;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by LiangHuiLing on 2019年8月29日
 *
 * MainCardGiveSchemeController
 */
@RequestMapping(value="/main/cardGiveScheme", viewPath="/modules_page/main/cardGiveScheme")
public class MainCardGiveSchemeController extends BaseController {

	@Inject
	private MainCardGiveSchemeService mainCardGiveSchemeService;
	
	public void index(){
		render("schemeIndex.html");
	}

	@Clear(LogInterceptor.class)
	public void pageTable(){
		MainCardGiveScheme deductScheme = getBean(MainCardGiveScheme.class, "", true);
        Page<MainCardGiveScheme> modelPage = mainCardGiveSchemeService.paginateByCondition(deductScheme, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MainCardGiveScheme>(modelPage));
	}
	
	/**
     * 添加页面方法
     */
    public void add() {
    	final MainCardGiveScheme model = getBean(MainCardGiveScheme.class, "", true);
    	model.setSchemeNo(System.currentTimeMillis()+"");
    	setAttr("model", model);
        render("schemeForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String schemeId = getPara("id");
        setAttr("model", mainCardGiveSchemeService.findById(schemeId));
        render("schemeForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
    	final int ruleCount = getParaToInt("ruleCount");
        final MainCardGiveScheme deductScheme = getBean(MainCardGiveScheme.class, "", true);
        if(StrKit.notBlank(deductScheme.getId())){
        	deductScheme.setUpdateBy(AuthUtils.getUserId());
        }else{
        	deductScheme.setCreateBy(AuthUtils.getUserId());
        }
        List<MainCardGiveSchemeRule> schemeRuleList = new ArrayList<MainCardGiveSchemeRule>(ruleCount);
        if(ruleCount>0){
        	for (int i = 1; i <= ruleCount; i++) {
        		final MainCardGiveSchemeRule schemeRule = getBean(MainCardGiveSchemeRule.class, "ruleList["+i+"]");
        		if(StrKit.notBlank(schemeRule.getCycle())){
        			if(schemeRule.getStartSeq()>schemeRule.getEndSeq()){
						renderJson(Ret.fail("msg", "开始序不能大于结算序"));
        				return;
					}

        			if(StrKit.notBlank(schemeRule.getId())){
        				schemeRule.setUpdateBy(AuthUtils.getUserId());
        	        }else{
        	        	schemeRule.setCreateBy(AuthUtils.getUserId());
        	        }
        			schemeRuleList.add(schemeRule);
        		}
        	}
        }
		renderJson(mainCardGiveSchemeService.saveCardGiveScheme(deductScheme, schemeRuleList));
    }
    
    public void del(){
    	final MainCardGiveScheme giveScheme = getBean(MainCardGiveScheme.class, "", true);
    	giveScheme.setUpdateBy(AuthUtils.getUserId());
    	giveScheme.setUpdateTime(new Date());
		renderJson(mainCardGiveSchemeService.del(giveScheme));
    }
    
    public void delRule(){
    	final MainCardGiveSchemeRule giveRule = getBean(MainCardGiveSchemeRule.class, "", true);
    	if(giveRule!=null && StrKit.notBlank(giveRule.getId())){
    		giveRule.setUpdateBy(AuthUtils.getUserId());
    		giveRule.setUpdateTime(new Date());
    		if(giveRule.update()){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }
}
