package com.cszn.main.data.web.controller.wms;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.wms.WmsStockLabelService;
import com.cszn.integrated.service.entity.wms.WmsStockLabel;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value="/wms/label", viewPath="/modules_page/wms/label")
public class WmsStockLabelController extends BaseController {

    @Inject
    private WmsStockLabelService wmsStockLabelService;

    public void index(){

        render("index.html");
    }

    public void pageList(){
        WmsStockLabel label=getBean(WmsStockLabel.class,"",true);

        Page<WmsStockLabel> labelPage=wmsStockLabelService.pageList(getParaToInt("page"),getParaToInt("limit"),label);
        renderJson(new DataTable<WmsStockLabel>(labelPage));
    }

    public void form(){
        String id=getPara("id");

        if(StrKit.notBlank(id)){
            WmsStockLabel label=wmsStockLabelService.findById(id);
            setAttr("model",label);
        }

        render("form.html");
    }

    public void saveStockLabel(){
        WmsStockLabel label=getBean(WmsStockLabel.class,"",true);

        boolean flag=wmsStockLabelService.saveStockLabel(label, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
