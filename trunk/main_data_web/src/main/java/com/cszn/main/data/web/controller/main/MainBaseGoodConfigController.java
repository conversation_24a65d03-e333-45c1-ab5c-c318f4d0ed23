package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseGoodConfigsService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainGoodModelsService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBaseGoodConfigs;
import com.cszn.integrated.service.entity.main.MainGoodModels;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value = "/main/baseGoodConfig",viewPath = "/modules_page/main/baseGoodConfig")
public class MainBaseGoodConfigController extends BaseController {

    @Inject
    private MainBaseGoodConfigsService mainBaseGoodConfigsService;
    @Inject
    private MainGoodModelsService mainGoodModelsService;
    @Inject
    private MainBaseService mainBaseService;

    public void index(){
        List<MainBase> baseList=mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        render("baseGoodConfigIndex.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainBaseGoodConfigs config=mainBaseGoodConfigsService.findById(id);
            setAttr("config",config);
        }
        //获取所有可以商品
        List<MainGoodModels> modelList=mainGoodModelsService.findAllGoodModelList();
        //获取所有基地
        List<MainBase> baseList=mainBaseService.findBaseList();

        setAttr("modelList",modelList);
        setAttr("baseList",baseList);

        render("baseGoodConfigForm.html");
    }

    public void pageList(){
        MainBaseGoodConfigs config=getBean(MainBaseGoodConfigs.class,"",true);

        Page<Record> page=mainBaseGoodConfigsService.pageList(getParaToInt("page"),getParaToInt("limit"),config);

        renderJson(new DataTable<Record>(page));
    }

    public void saveConfig(){
        MainBaseGoodConfigs config=getBean(MainBaseGoodConfigs.class,"",true);
        //判断是否重复添加
        if(StrKit.notBlank(config.getId())){
            String sql="select * from main_base_good_configs where base_id=? and good_model_id=? and id<>? ";
            if(Db.findFirst(sql,config.getBaseId(),config.getGoodModelId(),config.getId())!=null){
                renderJson(Ret.fail("msg","请勿重复添加"));
                return;
            }
        }else{
            String sql="select * from main_base_good_configs where base_id=? and good_model_id=? ";
            if(Db.findFirst(sql,config.getBaseId(),config.getGoodModelId())!=null){
                renderJson(Ret.fail("msg","请勿重复添加"));
                return;
            }
        }
        boolean flag=mainBaseGoodConfigsService.saveConfig(config, AuthUtils.getUserId());

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
