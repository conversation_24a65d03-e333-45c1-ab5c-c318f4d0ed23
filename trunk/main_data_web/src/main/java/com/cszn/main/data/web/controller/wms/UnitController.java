package com.cszn.main.data.web.controller.wms;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.wms.WmsUnitService;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.wms.WmsUnit;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/wms/unit",viewPath = "/modules_page/wms/unit")
public class UnitController extends BaseController {

    @Inject
    WmsUnitService wmsUnitService;

    public void index(){
        render("unitIndex.html");
    }

    public void unitPage(){
        WmsUnit model = getBean(WmsUnit.class,"",true);
		Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(model.getUnitName())) {
			columns.likeAppendPercent("unit_name", model.getUnitName());
		}
		if(StrKit.notBlank(model.getIsDecimal())) {
			columns.eq("is_decimal", model.getIsDecimal());
		}
		Page<WmsUnit> modelPage = wmsUnitService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
		renderJson(new DataTable<WmsUnit>(modelPage));
    }

    public void unitForm(){
		WmsUnit model = getBean(WmsUnit.class,"",true);
        if(StrKit.notBlank(model.getId())){
        	model=wmsUnitService.findById(model.getId());
        }
        setAttr("model",model);
        render("unitForm.html");
    }

    public void saveUnit(){
    	WmsUnit model = getBean(WmsUnit.class,"",true);
        if(StrKit.notBlank(model.getId())){
        	model.setUpdateBy(AuthUtils.getUserId());
        }else{
        	model.setUpdateBy(AuthUtils.getUserId());
        	model.setCreateBy(AuthUtils.getUserId());
        }
        if(wmsUnitService.saveUnit(model)){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
