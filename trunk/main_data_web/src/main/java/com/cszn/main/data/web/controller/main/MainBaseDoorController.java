/**
 * 
 */
package com.cszn.main.data.web.controller.main;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseDoorService;
import com.cszn.integrated.service.api.main.MainBaseRoomDoorService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.entity.enums.LockType;
import com.cszn.integrated.service.entity.main.MainBaseDoor;
import com.cszn.integrated.service.entity.main.MainBaseRoomDoor;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2020年8月16日
 *
 * MainBaseDoorController
 */
@RequestMapping(value="/main/baseDoor", viewPath="/modules_page/main/baseDoor")
public class MainBaseDoorController extends BaseController {

	@Inject
    private MainBaseService mainBaseService;
	@Inject
    private MainBaseDoorService mainBaseDoorService;
	@Inject
	private MainBaseRoomDoorService mainBaseRoomDoorService;
	
    public void index() {
        setAttr("baseList",mainBaseService.findBaseList());
        render("doorIndex.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	MainBaseDoor mainBaseDoor = getBean(MainBaseDoor.class, "", true);
        Page<MainBaseDoor> doorPage = mainBaseDoorService.paginateByCondition(getParaToInt("page", 1), getParaToInt("limit", 10), mainBaseDoor);
        renderJson(new DataTable<MainBaseDoor>(doorPage));
    }
    
    public void doorList(){
    	final String baseId = getPara("baseId");
    	renderJson(mainBaseDoorService.findByBaseId(baseId));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	MainBaseDoor mainBaseDoor = new MainBaseDoor();
    	Map<String,String> lockType=new HashMap<>();
        for(LockType type:LockType.values()){
            lockType.put(type.getKey(),type.getValue());
        }
        setAttr("lockType",lockType);
        setAttr("baseList",mainBaseService.findBaseList());
    	setAttr("model", mainBaseDoor);
        render("doorForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String doorId = getPara("id");
        Map<String,String> lockType=new HashMap<>();
        for(LockType type:LockType.values()){
            lockType.put(type.getKey(),type.getValue());
        }
        setAttr("lockType",lockType);
        setAttr("baseList",mainBaseService.findBaseList());
        setAttr("model", mainBaseDoorService.findById(doorId));
        render("doorForm.html");
    }
    
    /**
     * 房间公共门配置方法
     */
    public void roomDoorForm() {
    	final String roomId = getPara("roomId");
    	final String baseId = getPara("baseId");
    	setAttr("roomId",roomId);
    	setAttr("baseId",baseId);
    	setAttr("doorList",mainBaseDoorService.findByBaseId(baseId));
    	setAttr("roomDoorList",mainBaseRoomDoorService.findListByRoomId(roomId));
    	render("roomDoorForm.html");
    }
    
    public void save() {
    	MainBaseDoor door = getBean(MainBaseDoor.class, "", true);
    	if(StringUtils.isBlank(door.getId())) {
    		door.setDelFlag(DelFlag.NORMAL);
    		door.setCreateDate(new Date());
    		door.setCreateBy(AuthUtils.getUserId());
    	}else {
    		door.setUpdateDate(new Date());
    		door.setUpdateBy(AuthUtils.getUserId());
    	}
    	final boolean isExist = mainBaseDoorService.checkDoorNo(door.getDoorNo());
    	if (StrKit.isBlank(door.getId()) && isExist) {
    		renderJson(Ret.fail("msg", "角色名称已存在！"));
    	}else{
    		if(mainBaseDoorService.doorSave(door)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}
    }
    
    public void saveRoomDoor() {
    	final int doorCount = getParaToInt("doorCount");
		List<MainBaseRoomDoor> roomDoorList = new ArrayList<MainBaseRoomDoor>();
		if(doorCount>0){
			for (int i = 1; i <= doorCount; i++) {
				final MainBaseRoomDoor roomDoor = getBean(MainBaseRoomDoor.class, "roomDoorList["+i+"]");
				if(StrKit.notBlank(roomDoor.getDoorId())){
					roomDoorList.add(roomDoor);
				}
			}
		}
		if(mainBaseRoomDoorService.roomDoorSave(roomDoorList, AuthUtils.getUserId())){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    public void delete() {
    	MainBaseDoor door = getBean(MainBaseDoor.class, "", true);
    	door.setUpdateBy(AuthUtils.getUserId());
    	door.setUpdateDate(new Date());
		if(mainBaseDoorService.deleteDoor(door)){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    public void delRoomDoor() {
    	MainBaseRoomDoor roomDoor = getBean(MainBaseRoomDoor.class, "", true);
		if(mainBaseRoomDoorService.deleteRoomDoor(roomDoor, AuthUtils.getUserId())){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
}
