package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainDictionariesService;
import com.cszn.integrated.service.api.main.MainDictionaryTypesService;
import com.cszn.integrated.service.entity.main.MainDictionaries;
import com.cszn.integrated.service.entity.main.MainDictionaryTypes;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import io.netty.handler.codec.MessageToByteEncoder;

import java.util.List;

@RequestMapping(value = "/main/dictionaries",viewPath = "/modules_page/main/dictionaries")
public class MainDictionariesController extends BaseController {

    @Inject
    private MainDictionaryTypesService mainDictionaryTypesService;
    @Inject
    private MainDictionariesService mainDictionariesService;

    public void index(){
        render("dictionaryTypesIndex.html");
    }

    public void dictionarieTypesPageList(){
        MainDictionaryTypes types=getBean(MainDictionaryTypes.class,"",true);

        Page<MainDictionaryTypes> page=mainDictionaryTypesService.pageList(getParaToInt("page"),getParaToInt("limit"),types);

        renderJson(new DataTable<MainDictionaryTypes>(page));
    }

    public void dictionarieTypesForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainDictionaryTypes type=mainDictionaryTypesService.findById(id);
            setAttr("type",type);
        }
        render("dictionaryTypesForm.html");
    }


    public void dictionarieTypesSave(){
        MainDictionaryTypes type=getBean(MainDictionaryTypes.class,"",true);

        boolean flag=mainDictionaryTypesService.saveDictionaryType(type, AuthUtils.getUserId());

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void dictionariesIndex(){
        String typeId=getPara("typeId");

        setAttr("typeId",typeId);

        render("dictionariesIndex.html");
    }

    public void dictionariesForm(){
        String typeId=getPara("typeId");
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainDictionaries dictionarie=mainDictionariesService.findById(id);
            setAttr("dictionarie",dictionarie);
        }
        setAttr("typeId",typeId);
        render("dictionariesForm.html");
    }

    public void dictionariesPageList(){
        MainDictionaries dictionaries= getBean(MainDictionaries.class,"",true);

        Page<MainDictionaries> page=mainDictionariesService.pageList(getParaToInt("page"),getParaToInt("limit"),dictionaries);

        renderJson(new DataTable<MainDictionaries>(page));
    }

    public void dictionariesSave(){
        MainDictionaries dictionaries=getBean(MainDictionaries.class,"",true);

        boolean flag=mainDictionariesService.saveDictionaries(dictionaries,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


}
