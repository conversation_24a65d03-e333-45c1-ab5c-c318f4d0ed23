package com.cszn.main.data.web.controller.mall;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.mall.MallProductCategoryService;
import com.cszn.integrated.service.entity.mall.MallProductCategory;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/mall/category",viewPath = "/modules_page/mall/category")
public class MallProductCategoryController extends BaseController {

    @Inject
    private MallProductCategoryService mallProductCategoryService;

    public void index(){

        render("index.html");
    }

    public void pageList(){
        MallProductCategory category=getBean(MallProductCategory.class,"",true);
        Page<MallProductCategory> page=mallProductCategoryService.pageList(getParaToInt("page"),getParaToInt("limit"),category);
        renderJson(new DataTable<MallProductCategory>(page));
    }


    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MallProductCategory category=mallProductCategoryService.findById(id);
            setAttr("model",category);
        }
        render("form.html");
    }

    public void categorySave(){
        MallProductCategory category=getBean(MallProductCategory.class,"",true);

        boolean flag=mallProductCategoryService.categorySave(category, AuthUtils.getUserId());

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }

    }

    public void categoryDelete(){
        String id=getPara("id");

        MallProductCategory category=new MallProductCategory();
        category.setId(id);
        category.setDelFlag("1");

        boolean flag=mallProductCategoryService.categorySave(category, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

}
