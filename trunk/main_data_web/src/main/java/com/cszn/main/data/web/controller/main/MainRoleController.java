package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainRoleService;
import com.cszn.integrated.service.entity.main.MainRole;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import ws.schild.jave.AudioAttributes;

@RequestMapping(value = "/main/role",viewPath = "/modules_page/main/role/")
public class MainRoleController extends BaseController {

    @Inject
    MainRoleService mainRoleService;

    public void index(){

        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainRole mainRole=mainRoleService.findById(id);
            set("record",mainRole);
        }

        render("form.html");
    }

    public void pageList(){
        MainRole mainRole=getBean(MainRole.class,"",true);
        Columns columns=Columns.create();
        if(StrKit.notBlank(mainRole.getRoleName())){
            columns.like("role_name",mainRole.getRoleName());
        }

        Page<MainRole> page=mainRoleService.paginateByColumns(getParaToInt("page"),getParaToInt("limit"),columns);
        renderJson(new DataTable<MainRole>(page));
    }

    public void save(){
        MainRole mainRole=getBean(MainRole.class,"",true);

        boolean flag=mainRoleService.saveMainRole(mainRole, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
