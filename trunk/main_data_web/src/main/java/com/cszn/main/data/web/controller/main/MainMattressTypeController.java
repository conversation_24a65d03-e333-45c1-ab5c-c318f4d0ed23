package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainMattressTypeService;
import com.cszn.integrated.service.entity.enums.MattressType;
import com.cszn.integrated.service.entity.main.MainMattressType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.Map;

@RequestMapping(value = "/main/mattressType",viewPath = "/modules_page/main/mattressType")
public class MainMattressTypeController extends BaseController {

    @Inject
    private MainMattressTypeService mainMattressTypeService;

    /**
     * 床垫首页
     */
    public void index(){
        render("mattressTypeIndex.html");
    }

    /**
     * 床垫分页
     */
    @Clear(LogInterceptor.class)
    public void findPageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainMattressType mainMattressType=getBean(MainMattressType.class,"",true);
        Page<MainMattressType> mattressTypePage=mainMattressTypeService.findMattressTypePage(pageNumber,pageSize,mainMattressType);
        renderJson(new DataTable<MainMattressType>(mattressTypePage));
    }

    public void mattressTypeForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainMattressType mainMattressType=mainMattressTypeService.findById(id);
            setAttr("mattressType",mainMattressType);
        }
        Map<String,String>map=new HashMap<>();
        for(MattressType type:MattressType.values()){
            map.put(type.getKey(),type.getValue());
        }
        setAttr("mattressTypeCode",map);
        render("mattressTypeForm.html");
    }

    /**
     * 保存床垫
     */
    public void saveMattressType(){
        MainMattressType mainMattressType=getBean(MainMattressType.class,"mattressType",true);

        if(mainMattressTypeService.mattressTypeIsExist(mainMattressType)){
            renderJson(Ret.fail("msg","名字或编号已经存在"));
            return;
        }

        boolean flag=mainMattressTypeService.saveMattressType(mainMattressType, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 删除床垫
     */
    public void delMattressType(){
        String id=getPara("id");
        boolean flag=mainMattressTypeService.delMattressType(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
