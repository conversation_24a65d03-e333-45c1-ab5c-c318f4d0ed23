package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainPunishRuleService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.main.MainPunishRule;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

@RequestMapping(value = "/main/punishRule",viewPath = "/modules_page/main/punishRule")
public class PunishRuleController extends BaseController {


    @Inject
    private MainPunishRuleService mainPunishRuleService;

    @Inject
    private DictService dictService;



    /**
     * 跳转违约规则界面
     */
    public void index(){
        render("punishRuleIndex.html");
    }


    /**
     * 分页列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainPunishRule pr = getBean(MainPunishRule.class,"",true);
        Page<MainPunishRule> page = mainPunishRuleService.findPage(getParaToInt("page"), getParaToInt("limit"),pr);
        renderJson(new DataTable<MainPunishRule>(page));
    }


    /**
     * 跳转新增/编辑界面
     */
    public void form(){
        String id = getPara("id");
        MainPunishRule pr = mainPunishRuleService.get(id);
        List<Dict> typeList = dictService.getListByTypeOnUse("punish_type");
        List<Dict> wayList = dictService.getListByTypeOnUse("punish_deduct_way");
        setAttr("typeList",typeList);
        setAttr("wayList",wayList);
        setAttr("pr",pr);
        render("punishRuleForm.html");
    }


    /**
     * 作废
     */
    public void delete(){
        String id = getPara("id");
        boolean flag = mainPunishRuleService.delPunishRule(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 保存
     */
    public void save(){
        MainPunishRule pr = getBean(MainPunishRule.class,"pr",true);
        //判断是否冲突
        List<Object> params=new ArrayList<>();
        params.add(pr.getDeductType());
        params.add(pr.getPunishType());
        params.add(pr.getExceedStartDay());
        params.add(pr.getExceedStartDay());
        params.add(pr.getExceedEndDay());
        params.add(pr.getExceedEndDay());
        params.add(pr.getExceedStartDay());
        params.add(pr.getExceedEndDay());
        params.add(pr.getExceedStartDay());
        params.add(pr.getExceedEndDay());
        String sql="select count(id) from main_punish_rule where deduct_type=? and punish_type=? and del_flag='0' and ((exceed_start_day<=? and exceed_end_day>=?) or (exceed_start_day<=? and exceed_end_day>=?) or " +
                "(exceed_start_day>=? and exceed_end_day<=?) or (exceed_start_day>=? and exceed_end_day<=?)) ";
        if(StrKit.notBlank(pr.getId())){
            sql+=" and id<>? ";
            params.add(pr.getId());
        }
        if(Db.queryInt(sql,params.toArray())>0){
            renderJson(Ret.fail("msg", "超限天数存在冲突请检查"));
            return;
        }
        boolean flag = mainPunishRuleService.savePunishRule(pr,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }
}
