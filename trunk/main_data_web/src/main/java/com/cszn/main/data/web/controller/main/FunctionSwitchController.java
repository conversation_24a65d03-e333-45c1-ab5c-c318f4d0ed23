package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainFunctionSwitchService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.main.MainFunctionSwitch;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.integrated.service.entity.sys.Role;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@RequestMapping(value = "/main/functionSwitch",viewPath = "/modules_page/main/functionSwitch")
public class FunctionSwitchController extends BaseController {

    @Inject
    private MainFunctionSwitchService mainFunctionSwitchService;

    @Inject
    private DictService dictService;


    /**
     * 跳转功能配置界面
     */
    public void index(){
        List<Dict> dictList = dictService.getListByTypeOnUse("function_switch_configure");
        setAttr("dictList",dictList);
        render("functionSwitchIndex.html");
    }


    /**
     * 功能配置列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainFunctionSwitch fs = getBean(MainFunctionSwitch.class,"",true);
        Page<Record> page = mainFunctionSwitchService.findPage(getParaToInt("page"), getParaToInt("limit"),fs);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 跳转功能配置新增/编辑页面
     */
    public void form(){
        String id = getPara("id");
        setAttr("fs",mainFunctionSwitchService.get(id));
        render("functionSwitchForm.html");
    }


    /**
     * 作废
     */
    public void delete(){
    	MainFunctionSwitch mainFunctionSwitch = getBean(MainFunctionSwitch.class, "", true);
        if(mainFunctionSwitchService.update(mainFunctionSwitch)){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 编辑
     */
    public void save(){
        MainFunctionSwitch fs = getBean(MainFunctionSwitch.class,"fs",true);
        if(fs == null || StringUtils.isBlank(fs.getFunction()) || StringUtils.isBlank(fs.getSwitchFlag())){
            renderJson(Ret.fail("msg", "功能配置缺少参数"));
            return;
        }
        if(StrKit.isBlank(fs.getId()) && mainFunctionSwitchService.getDistinct(fs.getFunction())) {
        	renderJson(Ret.fail("msg", "该功能配置已存在，请不要重复添加!"));
            return;
        }
        if(mainFunctionSwitchService.saveSwitch(fs,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }

}
