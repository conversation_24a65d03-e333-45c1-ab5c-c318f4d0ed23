package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCsamAssetBrandService;
import com.cszn.integrated.service.api.main.MainCsamAssetTypeFieldService;
import com.cszn.integrated.service.api.main.MainCsamAssetTypeService;
import com.cszn.integrated.service.api.main.MainCsamAssetTypeUserService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.wms.WmsStockTypeUser;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;


@RequestMapping(value = "/main/csam",viewPath = "/modules_page/main/csam")
public class MainCsamController extends BaseController {

    @Inject
    private MainCsamAssetTypeService mainCsamAssetTypeService;
    @Inject
    private MainCsamAssetBrandService mainCsamAssetBrandService;
    @Inject
    private MainCsamAssetTypeFieldService mainCsamAssetTypeFieldService;
    @Inject
    private MainCsamAssetTypeUserService mainCsamAssetTypeUserService;
    @Inject
    private UserService userService;

    public void assetTypeIndex(){

        render("type/index.html");
    }


    public void assetTypeForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainCsamAssetType type=mainCsamAssetTypeService.findById(id);
            setAttr("model",type);
            if(type!=null){
                MainCsamAssetType parentType=mainCsamAssetTypeService.findById(type.getParentId());
                if(parentType!=null){
                    setAttr("parentTypeName",parentType.getAssetTypeName());
                }
            }
        }

        render("type/form.html");
    }

    public void assetTypeTreeTable(){
        List<Record> recordList=mainCsamAssetTypeService.csamTypeTableTree(null);
        renderJson(new DataTable<Record>(recordList));
    }

    public void assetTypeTree() {
        String isLeaver=getPara("isLeaver");
        List<Record> recordList = mainCsamAssetTypeService.csamTypeTableTree(isLeaver);
        List<ZTree> zTreeList=new ArrayList<>();
        for(Record record:recordList){
            ZTree zTree=new ZTree(record.getStr("id"),record.getStr("assetTypeName"),null,record.getStr("pId"),record.getStr("pIds"));
            zTreeList.add(zTree);
        }
        renderJson(zTreeList);
    }

    public void assetTypeSave(){
        MainCsamAssetType type=getBean(MainCsamAssetType.class,"",true);
        type.setAssetTypeName(type.getAssetTypeName().trim());
        if(StrKit.notBlank(type.getId())){
            if(Db.findFirst("select * from main_csam_asset_type where `asset_type_name`=? and id<>? ",type.getAssetTypeName(),type.getId())!=null){
                renderJson(Ret.fail("msg","改类型已存在"));
                return;
            }
            if(type.getId().equals(type.getParentId())){
                renderJson(Ret.fail("msg","不能设置自己为自己的父级"));
                return;
            }
            MainCsamAssetType parentType=mainCsamAssetTypeService.findById(type.getParentId());
            if(StrKit.notBlank(type.getParentId()) && parentType!=null && !"00000000-0000-0000-0000-000000000000".equals(type.getParentId()) && parentType.getParentIds().contains(type.getId())){
                renderJson(Ret.fail("msg","不能设置自己的子级为自己的父级"));
                return;
            }

        }else{
            if(Db.findFirst("select * from main_csam_asset_type where `asset_type_name`=? ",type.getAssetTypeName())!=null){
                renderJson(Ret.fail("msg","改类型已存在"));
                return;
            }
        }

        boolean flag=mainCsamAssetTypeService.assetTypeSave(type, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }


    public void assetTypeFieldIndex(){

        render("typeField/index.html");
    }

    public void assetTypeFieldForm(){
        String id=getPara("id");
        String assetTypeId=getPara("assetTypeId");
        String assetTypeName=getPara("assetTypeName");

        if(StrKit.notBlank(id)){
            MainCsamAssetTypeField field=mainCsamAssetTypeFieldService.findById(id);
            setAttr("model",field);
        }
        setAttr("assetTypeId",assetTypeId);
        setAttr("assetTypeName",assetTypeName);
        render("typeField/form.html");
    }

    public void assetTypeFieldPageList(){
        MainCsamAssetTypeField field=getBean(MainCsamAssetTypeField.class,"",true);

        Page<MainCsamAssetTypeField> page=mainCsamAssetTypeFieldService.assetTypeFieldPageList(getParaToInt("page"),getParaToInt("limit"),field);

        renderJson(new DataTable<MainCsamAssetTypeField>(page));
    }

    public void assetTypeFieldSave(){
        MainCsamAssetTypeField field=getBean(MainCsamAssetTypeField.class,"",true);

        boolean flag=mainCsamAssetTypeFieldService.assetTypeFieldSave(field,AuthUtils.getUserId());

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void assetBrandIndex(){

        render("brand/index.html");
    }

    public void assetBrandForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainCsamAssetBrand brand=mainCsamAssetBrandService.findById(id);
            setAttr("model",brand);
        }
        render("brand/form.html");
    }

    public void assetBrandSave(){
        MainCsamAssetBrand brand=getBean(MainCsamAssetBrand.class,"",true);
        boolean flag=mainCsamAssetBrandService.assetBrandSave(brand,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void assetBrandPageList(){
        MainCsamAssetBrand brand=getBean(MainCsamAssetBrand.class,"",true);

        Page<MainCsamAssetBrand> page=mainCsamAssetBrandService.assetBrandPageList(getParaToInt("page"),getParaToInt("limit"),brand);

        renderJson(new DataTable<MainCsamAssetBrand>(page));

    }

    public void typeUserIndex(){
        String typeId=getPara("typeId");

        List<MainCsamAssetTypeUser> typeUserList=mainCsamAssetTypeUserService.findTypeUserList(typeId);

        setAttr("typeUserList",typeUserList);

        List<User> userList=userService.findUserList();
        setAttr("typeId",typeId);
        setAttr("userList",userList);
        render("type/userIndex.html");
    }

    public void saveTypeUser(){
        Integer doorCount=getParaToInt("doorCount");
        String typeId=getPara("typeId");
        if(doorCount==null || doorCount==0){
            renderJson(Ret.fail("msg","请至少选择一条"));
            return;
        }

        List<MainCsamAssetTypeUser> typeUserSaveList=new ArrayList<>();

        for(int i=0;i<=doorCount;i++){
            MainCsamAssetTypeUser typeUser=getBean(MainCsamAssetTypeUser.class,"roomDoorList["+i+"]",true);
            if(StrKit.notBlank(typeUser.getUserId())){
                typeUser.setTypeId(typeId);
                typeUserSaveList.add(typeUser);
            }
        }

        try {
            if(typeUserSaveList.size()>0){
                for(MainCsamAssetTypeUser typeUser:typeUserSaveList){
                    mainCsamAssetTypeUserService.saveMainCsamAssetTypeUser(typeUser,AuthUtils.getUserId());
                }
            }
            renderJson(Ret.ok("msg","操作成功"));
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void delTypeUser(){
        String id=getPara("id");

        boolean flag=mainCsamAssetTypeUserService.delMainCsamAssetTypeUser(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
