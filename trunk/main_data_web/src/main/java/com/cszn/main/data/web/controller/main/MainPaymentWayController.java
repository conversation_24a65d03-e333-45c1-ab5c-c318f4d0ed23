package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainPaymentWaysService;
import com.cszn.integrated.service.entity.enums.PaymentWay;
import com.cszn.integrated.service.entity.main.MainPaymentWays;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.Map;

@RequestMapping(value = "/main/paymentWay",viewPath = "/modules_page/main/paymentWay/")
public class MainPaymentWayController extends BaseController {

    @Inject
    private MainPaymentWaysService mainPaymentWaysService;

    public void index(){
        render("PaymentWayIndex.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainPaymentWays way=mainPaymentWaysService.findById(id);
            setAttr("way",way);
        }

        Map<String,String> codeMap=new HashMap<>();

        for(PaymentWay p :PaymentWay.values()){
            codeMap.put(p.getKey(),p.getValue());
        }
        setAttr("codes",codeMap);
        render("PaymentWayForm.html");
    }

    public void pageList(){
        MainPaymentWays way=getBean(MainPaymentWays.class,"",true);

        Page<MainPaymentWays> page=mainPaymentWaysService.pageList(getParaToInt("page"),getParaToInt("limit"),way);
        renderJson(new DataTable<MainPaymentWays>(page));
    }

    public void saveWay(){
        MainPaymentWays way=getBean(MainPaymentWays.class,"",true);

        if(StrKit.notBlank(way.getId())){
            String sql="select * from main_payment_ways where payment_way_code=? and id<>? ";
            if(Db.findFirst(sql,way.getPaymentWayCode(),way.getId())!=null){
                renderJson(Ret.fail("msg","该编号已存在"));
                return;
            }
        }else{
            String sql="select * from main_payment_ways where payment_way_code=? ";
            if(Db.findFirst(sql,way.getPaymentWayCode())!=null){
                renderJson(Ret.fail("msg","该编号已存在"));
                return;
            }
        }

        boolean flag=mainPaymentWaysService.saveWay(way, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
