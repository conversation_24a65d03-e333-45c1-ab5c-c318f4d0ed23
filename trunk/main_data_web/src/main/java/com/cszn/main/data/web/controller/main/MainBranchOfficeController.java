package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

@RequestMapping(value = "/main/branchOffice",viewPath = "/modules_page/main/branchOffice/")
public class MainBranchOfficeController extends BaseController {

    @Inject
    private MainBranchOfficeService mainBranchOfficeService;
    @Inject
    private AreaService areaService;
    @Inject
    private UserService userService;
    
    /**
     * 分公司首页
     */
    public void index(){
        render("branchOfficeIndex.html");
    }

    /**
     * 获取分公司分页
     */
    @Clear(LogInterceptor.class)
    public void pageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainBranchOffice branchOffice=getBean(MainBranchOffice.class,"",true);
        Page<MainBranchOffice> page=mainBranchOfficeService.branchOfficePage(pageNumber,pageSize,branchOffice);
        renderJson(new DataTable<MainBranchOffice>(page));
    }

    /**
     * 跳转分公司添加页面
     */
    public void banchOfficeForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainBranchOffice mainBranchOffice=mainBranchOfficeService.findById(id);
            if(StringUtils.isNotBlank(mainBranchOffice.getProvinceId())) {
                Area area = areaService.findById(mainBranchOffice.getProvinceId());
                if(area != null)setAttr("province",area.getAreaName());
            }
            if(StringUtils.isNotBlank(mainBranchOffice.getCityId())) {
                Area area = areaService.findById(mainBranchOffice.getCityId());
                if(area != null)setAttr("city",area.getAreaName());
            }
            if(StringUtils.isNotBlank(mainBranchOffice.getTownId())) {
                Area area = areaService.findById(mainBranchOffice.getTownId());
                if(area != null)setAttr("town",area.getAreaName());
            }
            if(StringUtils.isNotBlank(mainBranchOffice.getStreetId())) {
                Area area = areaService.findById(mainBranchOffice.getStreetId());
                if(area != null)setAttr("street",area.getAreaName());
            }
            setAttr("branchOffice",mainBranchOffice);
        }
        setAttr("userList", userService.findUnlockUserList());
        render("branchOfficeFrom.html");
    }

    /**
     * 保存分公司
     */
    public void saveBranchOffice(){
        MainBranchOffice mainBranchOffice=getBean(MainBranchOffice.class,"",true);
        boolean flag=mainBranchOfficeService.saveBranchOffice(mainBranchOffice, AuthUtils.getUserId());
        System.out.println("58："+mainBranchOffice);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 删除分公司
     */
    public void delBranchoffice(){
        String id=getPara("id");
        boolean flag=mainBranchOfficeService.delBranchOffice(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    /**
     * 批量删除分公司
     */
    public void batchDelBranchoffice(){
        String branchOfficeData=getPara("branchOfficeData");
        if(StrKit.isBlank(branchOfficeData) || !branchOfficeData.startsWith("[{") || !branchOfficeData.endsWith("}]")){
            renderJson(Ret.fail("msg","操作失败"));
            return;
        }

        boolean flag=mainBranchOfficeService.batchDelBranchoffice(branchOfficeData,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
