package com.cszn.main.data.web.support.enjoy.directive;

import java.util.List;

import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Inject;
import com.jfinal.template.Env;
import com.jfinal.template.expr.ast.Expr;
import com.jfinal.template.expr.ast.ExprList;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.Scope;

import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

/**
 * 获取数据字典列表Directive
 */
@JFinalDirective("getDictList")
public class DictListDirective extends JbootDirectiveBase {

	@Inject
	DictService dictService;

	private List<Dict> dictList = null;
	
	// ExprList 代表指令参数表达式列表
	public void setExprList(ExprList exprList){
		super.setExprList(exprList);
		
		if (exprList != null && exprList.getExpr(0) != null) {
			// 获取所有的参数
			Expr expr= exprList.getExpr(0) ;
			final String listType = expr.toString();
			dictList = dictService.findDictList(listType) ;
		}
	}


	@Override
	public void exec(Env env, Scope scope, Writer writer) {
		try {
			// 执行body
			if (dictList != null && dictList.size() > 0) {

				for (Dict dict : dictList) {
					scope.set("key", dict.getDictValue());
					scope.set("value", dict.getDictName());
					stat.exec(env, scope, writer); // 向页面写出数据
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// 有#end 结尾符
	public boolean hasEnd(){
		return true ;
	}

	@Override
	public void onRender(Env env, Scope scope, Writer writer) {

	}
}
