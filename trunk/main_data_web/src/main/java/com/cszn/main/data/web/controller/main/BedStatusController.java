package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainBedStatusService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBedStatus;
import com.cszn.integrated.service.entity.status.BedStatus;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.json.JFinalJson;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 床位状态管理
 * <AUTHOR>
 * @Date 2019/6/20
 **/
@RequestMapping(value="/main/bedStatus", viewPath="/modules_page/main/bedStatus")
public class BedStatusController extends BaseController {

    @Inject
    private MainBaseService mainBaseService;

    @Inject
    private MainBedStatusService mainBedStatusService;



    /**
     * 跳转到床位状态界面
     */
    public void index(){
        List<MainBase> baseList = mainBaseService.findAll();
        setAttr("baseList",baseList);
        render("bedStatusIndex.html");
    }


    /**
     * 床位状态列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainBedStatus bedStatus = getBean(MainBedStatus.class,"",true);
        //if(bedStatus != null)bedStatus.setBaseId(getCookie("baseId"));
        Page<MainBedStatus> page = mainBedStatusService.findListPage(getParaToInt("page"),getParaToInt("limit"),bedStatus);
        DataTable dataTable = new DataTable<MainBedStatus>(page);
//        if(dataTable.getData() != null && dataTable.getData().size() > 0){
//            dataTable.getData().stream().forEach(item -> {
//                MainBedStatus statusItem = (MainBedStatus)item;
//                if(statusItem != null && StringUtils.isNotBlank(statusItem.get("baseId"))){
//                    MainBase base = mainBaseService.findById(statusItem.get("baseId"));
//                    if(base != null)statusItem.put("baseName", base.getBaseName());
//                }
//            });
//        }
        renderJson(JFinalJson.getJson().toJson(dataTable));
    }


    /**
     * 跳转到新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MainBedStatus bedStatus = mainBedStatusService.get(id);
        if(bedStatus != null){
            MainBase base = mainBaseService.get(bedStatus.getId());
            setAttr("base",base);
        }
        Field[] fields=BedStatus.class.getFields();
        Map<String,String> bedStatusMap=new HashMap<>();
        try {
            for (Field field:fields){
                if(field.getType()==String.class){
                    bedStatusMap.put((String) field.get(null),BedStatus.me().desc((String) field.get(null)));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        setAttr("bedStatusMap",bedStatusMap);
        setAttr("bedStatus",bedStatus);
        render("bedStatusForm.html");
    }

    /**
     * 保存
     */
    public void save(){
        MainBedStatus bedStatus = getBean(MainBedStatus.class,"bedStatus",true);
        if(bedStatus == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        if(mainBedStatusService.findBedStatusByIdAndCode(bedStatus.getId(),bedStatus.getStatusCode())!=null){
            renderJson(Ret.fail("msg", "该床位状态编号已存在"));
            return;
        }
        boolean flag = mainBedStatusService.saveBedStatus(bedStatus,AuthUtils.getUserId());

        if(flag){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 作废
     */
    public void delete(){
        String id = getPara("id");
        MainBedStatus bedStatus = mainBedStatusService.get(id);
        if(bedStatus != null){
            boolean flag = mainBedStatusService.delBedStatus(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 批量删除
     */
    public void batchDel(){
        String bedStatusData =  getPara("bedStatusData");
        List<MainBedStatus> list = JSONArray.parseArray(bedStatusData,MainBedStatus.class);
        boolean flag = mainBedStatusService.batchDelBedStatus(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量作废成功"));
        }else{
            renderJson(Ret.fail("msg", "批量作废失败"));
        }
    }
}
