package com.cszn.main.data.web.controller.wms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.*;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCsamAssetBrandService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.api.wms.*;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainCsamAssetBrand;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.integrated.service.entity.wms.*;
import com.cszn.integrated.service.provider.wms.WmsStockModelServiceImpl;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.Base64.Encoder;

@RequestMapping(value = "/wms/stockModel",viewPath = "/modules_page/wms/stockModel")
public class StockModelController extends BaseController {

    @Inject
    WmsStockModelService wmsStockModelService;
    @Inject
    WmsStockTypeService wmsStockTypeService;
    @Inject
    WmsStockModelImageService wmsStockModelImageService;
    @Inject
    WmsStockModelWarehousePriceService wmsStockModelWarehousePriceService;
    @Inject
    WmsWarehouseService wmsWarehouseService;
    @Inject
    WmsStockLabelService wmsStockLabelService;
	@Inject
    private MainCsamAssetBrandService mainCsamAssetBrandService;
	@Inject
    WmsUnitService wmsUnitService;
	@Inject
    MainSyncRecordService mainSyncRecordService;

    public void index(){
        render("stockModelIndex.html");
    }

    public void stockModelForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            WmsStockModels model=wmsStockModelService.findById(id);

            if(model!=null){
                WmsStockTypes type=wmsStockTypeService.findById(model.getStockTypeId());
                setAttr("type",type);
            }
            if(model.getExchangePrice()!=null){
                model.put("exchangePriceStr",new BigDecimal(model.getExchangePrice()+""));
            }
            if(model.getExchangeBeanCoupons()!=null){
                model.put("exchangeBeanCouponsStr",new BigDecimal(model.getExchangeBeanCoupons()+""));
            }
            if(model.getExchangeTimes()!=null){
                model.put("exchangeTimesStr",new BigDecimal(model.getExchangeTimes()+""));
            }
            if(model.getExchangeAmount()!=null){
                model.put("exchangeAmountStr",new BigDecimal(model.getExchangeAmount()+""));
            }
            if(model.getSalePrice()!=null){
                model.put("salePriceStr",new BigDecimal(model.getSalePrice()+""));
            }
            if(model.getExchangeStaffIntegral()!=null){
                model.put("exchangeStaffIntegralStr",new BigDecimal(model.getExchangeStaffIntegral()+""));
            }
            setAttr("model",model);
            List<String> lableIdList=Db.query("select label_id from wms_stock_label_rel where stock_id=? ",id);
            String lableIds="";
            for(String str:lableIdList){
                lableIds+=str+",";
            }
            setAttr("lableIds",lableIds);
        }

        String selfPickupDataStr = HttpClientsUtils.get(Global.selfPickupUrl);

        List<WmsStockLabel> stockLabelList=wmsStockLabelService.getStockLabelList();
        List<MainCsamAssetBrand> brandList = mainCsamAssetBrandService.findList();
        List<WmsUnit> unitList = wmsUnitService.findList();
        setAttr("stockLabelList",stockLabelList);
        setAttr("brandList",brandList);
        setAttr("unitList",unitList);
        setAttr("selfPickupList",JSON.parse(selfPickupDataStr));
        render("stockModelForm.html");
    }

    public void findStockModelPageList(){
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        WmsStockModels model=getBean(WmsStockModels.class,"",true);

        Page<WmsStockModels> page=wmsStockModelService.findStockModelPageList(getParaToInt("page"),getParaToInt("limit"),model,startDate,endDate);
        renderJson(new DataTable<WmsStockModels>(page));
    }

    public void saveStockModel(){
        String userId=getPara("userId");
        WmsStockModels model=getBean(WmsStockModels.class,"",true);
        String lableIds=getPara("lableIds");
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }
        if(StrKit.notBlank(model.getNotSelfPickupIds())){
            String[] array=model.getNotSelfPickupIds().split(",");
            model.setNotSelfPickupIds(JSON.toJSONString(array));
        }
        if(StrKit.notBlank(model.getId())){

            model.setUpdateBy(userId);
            Integer n=Db.queryInt("select count(id) from wms_stock_models where name=? and standard=? and id<>?",model.getName(),model.getStandard(),model.getId());
            if(n!=null && n>0){
                renderJson(Ret.fail("msg","该名字和规格已经存在了，请勿重复添加"));
                return;
            }

            if(StrKit.notBlank(model.getBarcodeNumber())){
                Integer num=Db.queryInt("select count(id) from wms_stock_models where barcode_number=? and id<>?",model.getBarcodeNumber(),model.getId());
                if(num!=null && num>0){
                    renderJson(Ret.fail("msg","该条形码编号已存在"));
                    return;
                }
            }
        }else{
            model.setUpdateBy(userId);
            model.setCreateBy(userId);
            model.setUpdateDate(new Date());

            Integer n=Db.queryInt("select count(id) from wms_stock_models where name=? and standard=? ",model.getName(),model.getStandard());
            if(n!=null && n>0){
                renderJson(Ret.fail("msg","该名字和规格已经存在了，请勿重复添加"));
                return;
            }

            if(StrKit.notBlank(model.getBarcodeNumber())){
                Record m=wmsStockModelService.findModelByBarCode(model.getBarcodeNumber(),null);
                if(m!=null){
                    renderJson(Ret.fail("msg","该条形码编号已存在"));
                    return;
                }
            }


        }

        boolean flag=wmsStockModelService.saveStockModel(model);
        if(flag){
            wmsStockLabelService.saveStockLabelRel(model.getId(),lableIds,userId);
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void updateStockModelNewNo(){

        List<WmsStockModels> modelsList=wmsStockModelService.findList();
        for(WmsStockModels wmsStockModels :modelsList){
            String codePrefix=wmsStockTypeService.getAllCodePrefix(wmsStockModels.getStockTypeId());
            if(StrKit.notBlank(codePrefix)){
                synchronized (WmsStockModelServiceImpl.class){
                    String maxNewStockModelNo=Db.queryStr("select MAX(new_stock_model_no) from wms_stock_models where new_stock_model_no like concat(?,'_____') ",codePrefix);
                    Integer count=0;
                    if(StrKit.isBlank(maxNewStockModelNo)){
                        count=1;
                    }else{
                        count=Integer.valueOf(maxNewStockModelNo.substring(codePrefix.length()));
                        count++;
                    }
                    String newNoStr=count.toString();
                    String typeNoSuffixTemplate="00000";
                    String no=codePrefix+typeNoSuffixTemplate.substring(0,typeNoSuffixTemplate.length()-newNoStr.length())+newNoStr;
                    int maxNo=Integer.valueOf(no.replace(codePrefix,""));

                    WmsStockTypes types =wmsStockTypeService.findById(wmsStockModels.getStockTypeId());
                    if(StrKit.isBlank(types.getMaxCode())){
                        types.setMaxCode(no.replace(codePrefix,""));
                    }else{
                        if(Integer.valueOf(types.getMaxCode())<maxNo){
                            types.setMaxCode(no.replace(codePrefix,""));
                        }
                    }
                    types.setUpdateDate(new Date());


                    wmsStockModels.setNewStockModelNo(no);
                    wmsStockModels.setUpdateDate(new Date());
                    wmsStockModels.update();
                }
            }
        }
        renderSuccess();
    }

    public void updateStockModelNo(){

        List<WmsStockModels> modelsList=wmsStockModelService.findNullStockModelNoList();
        for(WmsStockModels wmsStockModels :modelsList){
            synchronized (WmsStockModelServiceImpl.class){
                wmsStockModels.setStockModelNo(WmsStockModelServiceImpl.genModelNo());
                wmsStockModels.setUpdateDate(new Date());
                wmsStockModels.update();
            }
        }
        renderSuccess();
    }

    public void stockModelBarCode(){
        String id=getPara("id");
        WmsStockModels model=wmsStockModelService.findById(id);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();//io流
        String jpg_base64=null;
        try {
            BufferedImage image = BarCodeUtils.insertWords(BarCodeUtils.getBarCode(model.getBarcodeNumber()), model.getBarcodeNumber());
            ImageIO.write(image,"jpg",baos);
            byte[] bytes=baos.toByteArray();
            Encoder encoder = Base64.getEncoder();
            jpg_base64=encoder.encodeToString(bytes).trim();
            jpg_base64 = jpg_base64.replaceAll("\n", "").replaceAll("\r", "");
            baos.flush();
            baos.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        renderJson(Ret.ok("data","data:image/jpg;base64,"+jpg_base64));
    }

    public void modelImage(){
        String id=getPara("id");

        List<Record> imageList=Db.find(" select id,model_id as modelId,img_name as imgName,img_url as imgUrl,is_main as isMain,sort,del_flag as delFlag " +
                " from wms_stock_model_image where del_flag='0' and model_id=? order by is_main desc,sort ",id);
        for (Record record : imageList) {
            int index = record.getStr("imgName").lastIndexOf(".");
            String type=index > 0 ? record.getStr("imgName").substring(index + 1) : "";
            record.set("fileType",type);

            String iTypes= "jpg,jpeg,png,gif,bmp";
            String vTypes="mp4,mov,avi,mkv";
            record.set("isImag",false);
            record.set("isVideo",false);
            if(iTypes.indexOf(type)!=-1){
                record.set("isImag",true);
            }else if(vTypes.indexOf(type)!=-1){
                record.set("isVideo",true);
            }
        }


        setAttr("modelId",id);
        setAttr("imageList",imageList);
        render("image.html");
    }

    public void uploadImg(){
        UploadFile file = getFile("file");
        String modelId=getPara("modelId");

        try {
            Map<String,String> params=new HashMap<>();
            params.put("bucket","mall");

            Map<String,String> heads=new HashMap<>();
            heads.put("Content-Type","multipart/form-data");
            String result= HttpClientsUtils.httpPostFormMultipart(Global.fileUploadUrl,params,file.getFile(),null,"utf-8");
            if(result.startsWith("{") && result.endsWith("}")){
                JSONObject object= JSON.parseObject(result);
                if("ok".equals(object.getString("state"))){

                    Integer sort=Db.queryInt("select max(sort) from wms_stock_model_image where  model_id=? ",modelId);
                    if(sort==null){
                        sort=0;
                    }
                    sort++;
                    JSONObject data=object.getJSONObject("data");
                    WmsStockModelImage image=new WmsStockModelImage();
                    image.setModelId(modelId);
                    image.setImgName(data.getString("title"));
                    image.setImgUrl(data.getString("src"));
                    image.setSort(sort);


                    if(wmsStockModelImageService.saveModelImage(image,null)){

                        mainSyncRecordService.saveSyncRecord(SyncType.wmsStockModelImage.getKey(), SyncDataType.INSERT,JSON.toJSONString(image),AuthUtils.getUserId());

                        renderJson(Ret.ok("msg","上传成功").put("data",image));
                        return;
                    }
                }else{
                    renderJson(Ret.fail("msg","上传接口响应失败"));
                    return;
                }
            }else{
                renderJson(Ret.fail("msg","上传接口调用失败"));
                return;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        renderJson(Ret.fail("msg","上传失败"));
    }

    public void deleteImg(){
        String id=getPara("id");

        int i= Db.update("update wms_stock_model_image set del_flag='1' where id=? ",id);
        if(i>0){
            mainSyncRecordService.saveSyncRecord(SyncType.wmsStockModelImage.getKey(), SyncDataType.DELETE,"[\""+id+"\"]",AuthUtils.getUserId());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void saveModelImage(){
        WmsStockModelImage image=getBean(WmsStockModelImage.class,"",true);

        if(wmsStockModelImageService.saveModelImage(image,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void saveModelImageSort(){
        String data=getPara("data");

        JSONArray jsonArray=JSON.parseArray(data);
        boolean flag=false;
       try {
           for (int i = 0; i < jsonArray.size(); i++) {
               JSONObject jsonObject=jsonArray.getJSONObject(i);
               Db.update("update wms_stock_model_image set sort=? where id=? ",jsonObject.getString("sort"),jsonObject.getString("id"));
           }
           flag=true;
       }catch (Exception e){
           e.printStackTrace();
       }

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void genBarCodeNumber(){

        String barCodeNumber=wmsStockModelService.genBarcodeNumber();

        renderJson(Ret.ok("data",barCodeNumber));
    }

    public void stockModelExport(){
        String typeId=getPara("type");


        List<Record> recordList=wmsStockModelService.getStockListByTypeId(typeId);



        String[] title={"类型","商品编号","商品名称","货物类型","规格型号","单位","销售价格","成本价格","兑换积分","低库存预警","是否参与兑换","出库顺序类型","是否有效","描述"};
        String fileName="主数据全部商品记录.xls";
        if(StrKit.notBlank(typeId)){
            WmsStockTypes types=wmsStockTypeService.findById(typeId);
            if(types!=null){
                fileName="主数据["+types.getName()+"]类型及子类型商品记录.xls";
            }
        }

        String sheetName = "商品记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);

            content[i][0]=record.getStr("type_name");
            content[i][1]=record.getStr("stock_model_no");
            content[i][2]=record.getStr("name");
            if("Assets".equalsIgnoreCase(record.getStr("type"))){
                content[i][3]="资产";
            }else if("Consume".equalsIgnoreCase(record.getStr("type"))){
                content[i][3]="耗材";
            }
            content[i][4]=record.getStr("standard");
            content[i][5]=record.getStr("unit");
            content[i][6]=record.getStr("sale_price");
            content[i][7]=record.getStr("cost_price");
            content[i][8]=record.getStr("exchange_price");
            content[i][9]=record.getStr("warning_min");
            if("1".equals(record.getStr("is_exchange"))){
                content[i][10]="参与积分兑换";
            }else if("0".equals(record.getStr("is_exchange"))){
                content[i][10]="不参与积分兑换";
            }
            if("FirstinFirstout".equalsIgnoreCase(record.getStr("out_type"))){
                content[i][11]="先进先出";
            }else if("FirstinLastout".equalsIgnoreCase(record.getStr("out_type"))){
                content[i][11]="先进后出";
            }
            if("1".equals(record.getStr("is_enabled"))){
                content[i][12]="有效";
            }else if("0".equals(record.getStr("is_enabled"))){
                content[i][12]="无效";
            }
            content[i][13]=record.getStr("description");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public void warehousePriceForm(){
        String modelId=getPara("modelId");

        List<WmsWarehouses> wmsWarehousesList=wmsWarehouseService.getAllWmsWarehouse();
        List<WmsStockModelWarehousePrice> warehousePriceList=wmsStockModelWarehousePriceService.warehousePriceList(modelId);

        Map<String,Double> priceMap=new HashMap<>();

        for(WmsStockModelWarehousePrice price:warehousePriceList){
            priceMap.put(price.getWarehouseId(),price.getSalePrice());
        }
        List<Record> recordList=new ArrayList<>();
        for(WmsWarehouses wmsWarehouse:wmsWarehousesList){
            Record record=new Record();
            record.set("warehouseId",wmsWarehouse.getId());
            record.set("warehouseName",wmsWarehouse.getName());
            if(priceMap.containsKey(wmsWarehouse.getId())){
                record.set("salePrice",priceMap.get(wmsWarehouse.getId()));
            }
            recordList.add(record);
        }
        setAttr("modelId",modelId);
        setAttr("recordList",recordList);
        render("warehousePriceForm.html");
    }

    public void saveWarehousePrice(){
        try {
            Integer count=getParaToInt("count");
            String modelId=getPara("modelId");
            List<WmsStockModelWarehousePrice> warehousePriceList=new ArrayList<>();
            List<String> updateParams=new ArrayList<>();
            updateParams.add(AuthUtils.getUserId());
            updateParams.add(modelId);

            String str="";
            for(int i=1;i<=count;i++){
                WmsStockModelWarehousePrice warehousePrice=getBean(WmsStockModelWarehousePrice.class,"list["+i+"]",true);
                warehousePrice.setId(IdGen.getUUID());
                warehousePrice.setStockModelId(modelId);
                warehousePrice.setUpdateBy(AuthUtils.getUserId());
                warehousePrice.setUpdateDate(new Date());
                warehousePrice.setCreateBy(AuthUtils.getUserId());
                warehousePrice.setCreateDate(new Date());
                warehousePrice.setDelFlag("0");
                warehousePriceList.add(warehousePrice);

                str+="?,";
                updateParams.add(warehousePrice.getWarehouseId());
            }
            str=str.substring(0,str.length()-1);
            Db.update("update wms_stock_model_warehouse_price set del_flag='1',update_by=?,update_date=now() " +
                    "where stock_model_id=? and del_flag='0' and warehouse_id in ("+str+") ",updateParams.toArray());
            Db.batchSave(warehousePriceList,warehousePriceList.size());
            renderJson(Ret.ok("msg","操作成功"));
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","操作失败"));
        }


    }

    public void genNewNo(){
        List<WmsStockModels> stockModelsList=wmsStockModelService.findList();

        for(WmsStockModels wmsStockModels:stockModelsList){
            String codePrefix=wmsStockTypeService.getAllCodePrefix(wmsStockModels.getStockTypeId());
            if(StrKit.notBlank(codePrefix)){
                String maxNewStockModelNo=Db.queryStr("select MAX(new_stock_model_no) from wms_stock_models where new_stock_model_no like concat(?,'_____') ",codePrefix);
                Integer count=0;
                if(StrKit.isBlank(maxNewStockModelNo)){
                    count=1;
                }else{
                    count=Integer.valueOf(maxNewStockModelNo.substring(codePrefix.length()));
                    count++;
                }
                String newNoStr=count.toString();
                String typeNoSuffixTemplate="00000";
                String no=codePrefix+typeNoSuffixTemplate.substring(0,typeNoSuffixTemplate.length()-newNoStr.length())+newNoStr;
                int maxNo=Integer.valueOf(no.replace(codePrefix,""));

                WmsStockTypes types =wmsStockTypeService.findById(wmsStockModels.getStockTypeId());
                if(StrKit.isBlank(types.getMaxCode())){
                    types.setMaxCode(no.replace(codePrefix,""));
                }else{
                    if(Integer.valueOf(types.getMaxCode())<maxNo){
                        types.setMaxCode(no.replace(codePrefix,""));
                    }
                }
                types.setUpdateDate(new Date());
                wmsStockModels.setNewStockModelNo(no);
                wmsStockModels.update();
            }
        }
        renderJson(Ret.ok());
    }

    public void genNewNoIsNull(){
        List<WmsStockModels> stockModelsList=wmsStockModelService.findNullNewStockModelNoList();

        for(WmsStockModels wmsStockModels:stockModelsList){
            String codePrefix=wmsStockTypeService.getAllCodePrefix(wmsStockModels.getStockTypeId());
            if(StrKit.notBlank(codePrefix)){
                String maxNewStockModelNo=Db.queryStr("select MAX(new_stock_model_no) from wms_stock_models where new_stock_model_no like concat(?,'_____') ",codePrefix);
                Integer count=0;
                if(StrKit.isBlank(maxNewStockModelNo)){
                    count=1;
                }else{
                    count=Integer.valueOf(maxNewStockModelNo.substring(codePrefix.length()));
                    count++;
                }
                String newNoStr=count.toString();
                String typeNoSuffixTemplate="00000";
                String no=codePrefix+typeNoSuffixTemplate.substring(0,typeNoSuffixTemplate.length()-newNoStr.length())+newNoStr;
                int maxNo=Integer.valueOf(no.replace(codePrefix,""));

                WmsStockTypes types =wmsStockTypeService.findById(wmsStockModels.getStockTypeId());
                if(StrKit.isBlank(types.getMaxCode())){
                    types.setMaxCode(no.replace(codePrefix,""));
                }else{
                    if(Integer.valueOf(types.getMaxCode())<maxNo){
                        types.setMaxCode(no.replace(codePrefix,""));
                    }
                }
                types.setUpdateDate(new Date());
                wmsStockModels.setNewStockModelNo(no);
                wmsStockModels.update();
            }
        }
        renderJson(Ret.ok());
    }
}
