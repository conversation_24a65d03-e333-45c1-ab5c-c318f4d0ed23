package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainBedTypeService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBedType;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.json.JFinalJson;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * @Description 床位类型管理
 * <AUTHOR>
 * @Date 2019/6/19
 **/
@RequestMapping(value="/main/bedType", viewPath="/modules_page/main/bedType")
public class BedTypeController extends BaseController {

    @Inject
    private MainBedTypeService mainBedTypeService;


    @Inject
    private MainBaseService mainBaseService;


    /**
     * 跳转床位类型界面
     */
    public void index(){
        List<MainBase> baseList = mainBaseService.findAll();
        setAttr("baseList",baseList);
        render("bedTypeIndex.html");
    }


    /**
     * 床位类型列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainBedType bedType = getBean(MainBedType.class,"",true);
//        if(bedType != null)bedType.setBaseId(getCookie("baseId"));
        Page<MainBedType> page = mainBedTypeService.findListPage(getParaToInt("page"),getParaToInt("limit"),bedType);
        DataTable dataTable = new DataTable<MainBedType>(page);
//        if(dataTable.getData() != null && dataTable.getData().size() > 0){
//            dataTable.getData().stream().forEach(item -> {
//                MainBedType typeItem = (MainBedType)item;
//                if(typeItem != null && StringUtils.isNotBlank(typeItem.get("baseId"))){
//                    MainBase base = mainBaseService.findById(typeItem.get("baseId"));
//                    if(base != null)typeItem.put("baseName", base.getBaseName());
//                }
//            });
//        }
        renderJson(JFinalJson.getJson().toJson(dataTable));
    }


    /**
     * 跳转新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MainBedType bedType = mainBedTypeService.get(id);
//        if(bedType != null){
//            MainBase base = mainBaseService.get(bedType.getId());
//            setAttr("base",base);
//        }
//        List<MainBase> baseList = mainBaseService.findAll();
//        setAttr("baseList",baseList);
        setAttr("commonUpload", Global.commonUpload);
        setAttr("bedType",bedType);
        render("bedTypeForm.html");
    }


    /**
     * 保存
     */
    public void save(){
        MainBedType bedType = getBean(MainBedType.class,"bedType",true);
        if(bedType == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        if(mainBedTypeService.findBedTypeByIdAndName(bedType.getId(),bedType.getTypeName())!=null){
            renderJson(Ret.fail("msg", "类型名称不能重复"));
            return;
        }

        boolean flag = mainBedTypeService.saveBedType(bedType,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 床位类型删除
     */
    public void delete(){
        String id = getPara("id");
        MainBedType bedType = mainBedTypeService.get(id);
        if(bedType != null){
            boolean flag = mainBedTypeService.delBedType(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }



    /**
     * 批量删除
     */
    public void batchDel(){
        String bedTypeData =  getPara("bedTypeData");
        List<MainBedType> list = JSONArray.parseArray(bedTypeData,MainBedType.class);
        boolean flag = mainBedTypeService.batchDelBedType(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量作废成功"));
        }else{
            renderJson(Ret.fail("msg", "批量作废失败"));
        }
    }
}
