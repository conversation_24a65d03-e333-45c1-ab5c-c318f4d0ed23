package com.cszn.main.data.web.support.log;

import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.LogService;
import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.Log;
import com.cszn.integrated.service.entity.sys.Menu;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.kit.StrKit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.Map;

/**
 * 系统日志拦截器
 * <AUTHOR>
 *
 */
public class LogInterceptor implements Interceptor {
	
	@Inject
    private MenuService menuService;
	@Inject
	private UserService userService;
	@Inject
    private LogService logService;

	private static final Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

	@Override
	public void intercept(Invocation inv) {
		
		if (inv.getController() instanceof BaseController) {
			final String actionKey = inv.getActionKey();
			BaseController c = (BaseController) inv.getController();
			Log log = new Log();
			try {
				log.setSystemType(SystemType.MAIN_DATA);
				if(actionKey.contains("postLogin")){
					log.setLogTitle("系统登陆");
					final String loginName = c.getPara("loginName");
					log.setParams("login="+loginName);
					User user = userService.getByUserName(loginName);
					if(user!=null){
						if(StrKit.notBlank(user.getId())){
							log.setCreateBy(user.getId());
						}
					}
				}else if(actionKey.contains("logout")){
					log.setLogTitle("系统退出");
					log.setParams("logout=");
					log.setCreateBy(AuthUtils.getUserId());
				}else{
					final String url = c.getRequest().getRequestURI();
					Menu menu = menuService.getByUrl(url);
					if(menu!=null){
						final String menuPid = menu.getParentId();
						final String menuName = menu.getMenuName();
						final String menuType = menu.getMenuType();
						if(menuType.equals("menu")){
							log.setLogTitle(menuName);
						}else{
							Menu parentMenu = menuService.findById(menuPid);
							if(parentMenu!=null){
								log.setLogTitle(parentMenu.getMenuName()+"-"+menuName);
							}
						}
					}
					String parameters = "";
					if (c.getParaMap() != null && c.getParaMap().size() > 0) {
						for(Map.Entry<String, String[]> entry : c.getParaMap().entrySet()){
						    String mapKey = entry.getKey();
						    String mapValue = entry.getValue()[0];
						    parameters += mapKey+"=" + mapValue + "  ";
						}
					}
					log.setParams(parameters);
					log.setCreateBy(AuthUtils.getUserId());
				}
				log.setRemoteAddr(c.getIPAddress());
				log.setRequestUri(c.getRequest().getRequestURI());
				log.setMethod(c.getRequest().getMethod());
				log.setUserAgent(c.getUserAgent());
				log.setCreateDate(new Date());
			} catch (Exception e) {
				log.setException(e.getMessage());
				e.printStackTrace();
			} finally {
				inv.invoke();
				try {
					logService.save(log);
				}catch (Exception e){
					logger.error("操作日志保存异常"+e);
				}
			}
		} else {
			inv.invoke();
		}
	}

}
