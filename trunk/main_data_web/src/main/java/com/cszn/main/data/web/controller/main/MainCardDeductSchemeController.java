package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.Date;

/**
 * Created by LiangHuiLing on 2019年8月29日
 *
 * MainCardDeductSchemeController
 */
@RequestMapping(value="/main/cardDeductScheme", viewPath="/modules_page/main/cardDeductScheme")
public class MainCardDeductSchemeController extends BaseController {

	@Inject
	private MainCardDeductSchemeService mainCardDeductSchemeService;
	@Inject
    private MainSyncRecordService mainSyncRecordService;
	
	public void index(){
		render("schemeIndex.html");
	}

	@Clear(LogInterceptor.class)
	public void pageTable(){
		MainCardDeductScheme deductScheme = getBean(MainCardDeductScheme.class, "", true);
        Page<MainCardDeductScheme> modelPage = mainCardDeductSchemeService.paginateByCondition(deductScheme, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MainCardDeductScheme>(modelPage));
	}
	
	/**
     * 添加页面方法
     */
    public void add() {
    	final MainCardDeductScheme model = getBean(MainCardDeductScheme.class, "", true);
    	model.setSchemeNo(System.currentTimeMillis()+"");
    	setAttr("model", model);
        render("schemeForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String schemeId = getPara("id");
        setAttr("model", mainCardDeductSchemeService.findById(schemeId));
        render("schemeForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
        final MainCardDeductScheme deductScheme = getBean(MainCardDeductScheme.class, "", true);
        if(StrKit.notBlank(deductScheme.getId())){
        	deductScheme.setUpdateBy(AuthUtils.getUserId());
        }else{
        	deductScheme.setCreateBy(AuthUtils.getUserId());
        }
		renderJson(mainCardDeductSchemeService.saveCardDeductScheme(deductScheme));
    }
    
    public void del(){
    	final MainCardDeductScheme model = getBean(MainCardDeductScheme.class, "", true);
    	if(model!=null && StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		if(mainCardDeductSchemeService.update(model)){
//    			mainSyncRecordService.saveSyncRecord(SyncType.cardDeductScheme.getKey(),SyncDataType.DELETE,JSON.toJSONString(model),model.getUpdateBy());
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }
}
