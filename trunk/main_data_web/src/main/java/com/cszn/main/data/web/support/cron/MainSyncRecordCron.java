package com.cszn.main.data.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.api.main.MainBaseBedDynamicRecordService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.integrated.service.entity.main.MainBaseBedDynamicRecord;
import com.cszn.integrated.service.entity.main.MainSyncRecord;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.components.schedule.annotation.Cron;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Cron("*/1 * * * *")
public class MainSyncRecordCron implements Runnable {

    @Inject
    private MainSyncRecordService mainSyncRecordService;
    @Inject
    private MainAppJoinService mainAppJoinService;
    @Inject
    private MainBaseBedDynamicRecordService mainBaseBedDynamicRecordService;

    private static Logger logger = LoggerFactory.getLogger(MainSyncRecordCron.class);

    @Override
    public void run() {
        try {
            httpSynRecord();
            syncBedDynamic();
            updateExamineStatus();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void httpSynRecord(){
        List<MainAppJoin> appJoinList=mainAppJoinService.findAppJoinList();
        if(appJoinList!=null && appJoinList.size()>0){
            RequestConfig requestConfig = RequestConfig.custom()
                    // 获取连接超时时间
                    .setConnectionRequestTimeout(5000)
                    // 请求超时时间
                    .setConnectTimeout(5000)
                    // 响应超时时间
                    .setSocketTimeout(30000)
                    .build();
            CloseableHttpClient httpClient= HttpClients.custom().setDefaultRequestConfig(requestConfig).build();

            for (MainAppJoin app:appJoinList){
                int pageNumber=1;
                int pageSize=100;
                Page<MainSyncRecord> syncRecordPage=mainSyncRecordService.findSyncRecorList(pageNumber,pageSize,app.getAppNo());
                if(syncRecordPage==null || syncRecordPage.getTotalRow()==0){
                    //没有要发生的数据
                    continue;
                }
                //logger.info(JSON.toJSONString("appNo："+app.getAppNo()+",发送数据："+JSON.toJSONString(syncRecordPage.getList())));
                HttpPost httpPost=new HttpPost(app.getAppUrl());
                StringEntity entity=new StringEntity(JSON.toJSONString(syncRecordPage.getList()), Consts.UTF_8);
                httpPost.setEntity(entity);
                httpPost.setHeader("Content-Type", "application/json;charset=utf8");
                CloseableHttpResponse response=null;
                try {
                    response = httpClient.execute(httpPost);
                    HttpEntity responseEntity = response.getEntity();
                    //logger.info("响应状态为:" + response.getStatusLine().getStatusCode());
                    if (response.getStatusLine().getStatusCode() != 200) {
                        //响应状态不为200
                        continue;
                    }
                    String entityStr = EntityUtils.toString(responseEntity);
                    //logger.info("响应内容" + entityStr);
                    if (!entityStr.startsWith("{") || !entityStr.endsWith("}")) {
                        //响应内容不是json格式
                        continue;
                    }
                    JSONObject result = JSON.parseObject(entityStr);
                    if (!result.containsKey("Type") || !result.getString("Type").equals("1") || !result.containsKey("Data")) {
                        //响应不是成功状态
                        continue;
                    }
                    //获取响应回来的id
                    JSONArray array = JSON.parseArray(result.getString("Data"));
                    //设置这些id的数据为同步成功
//                    if (array != null && array.size() > 0) {
//                        Iterator<MainSyncRecord> iterator=syncRecordList.iterator();
//                        while (iterator.hasNext()){
//                            MainSyncRecord syncRecord=iterator.next();
//                            if(array.contains(syncRecord.getId())){
//                                syncRecord.setSyncStatus("0");
//                                syncRecord.setUpdateDate(new Date());
//                            }else{
//                                iterator.remove();
//                            }
//                        }
//                        Db.batchUpdate(syncRecordList,syncRecordList.size());
//                    }
                    List<MainSyncRecord> recordList=new ArrayList<>();
                    if (array != null && array.size() > 0) {
                        for (int i=0;i<array.size();i++){
                            JSONObject jsonObject=array.getJSONObject(i);
                            if(jsonObject.containsKey("id") && jsonObject.containsKey("state")){
                                if("0".equals(jsonObject.getString("state"))){
                                    MainSyncRecord mainSyncRecord=new MainSyncRecord();
                                    mainSyncRecord.setId(jsonObject.getString("id"));
                                    mainSyncRecord.setSyncStatus("0");
                                    mainSyncRecord.setUpdateDate(new Date());
                                    recordList.add(mainSyncRecord);
                                }
                            }
                        }
                        if(recordList!=null && recordList.size()>0){
                            Db.batchUpdate(recordList,recordList.size());
                        }
                    }
                }catch (SocketTimeoutException e){
                    logger.error("响应超时");
                    e.printStackTrace();
                } catch (IOException e) {
                    logger.error("发送失败");
                    e.printStackTrace();
                }finally {
                    if(response!=null){
                        try {
                            response.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                }
            }
            if(httpClient!=null){
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 同步床位动态
     */
    public void syncBedDynamic(){
        try {
            Page<MainBaseBedDynamicRecord> bedDynamicRecordPage=mainBaseBedDynamicRecordService.findBedDynamicRecordPage(1,30,null);
            List<MainBaseBedDynamicRecord> bedDynamicRecordList=bedDynamicRecordPage.getList();
            if(bedDynamicRecordList.size()==0){
                return;
            }
            List<String> ids=new ArrayList<>();
            for(MainBaseBedDynamicRecord record:bedDynamicRecordList){
                String appNo=record.getAppNo();

                JSONObject paramObject=null;
                JSONObject arrayItem=null;
                String url=null;
                if(Global.orgAppNo.equals(appNo)){
                    url=Global.sojournBedDynamicUrl;
                    //机构的床位动态发送到旅居
                    paramObject=new JSONObject();
                    paramObject.put("baseId",record.getBaseId());
                    paramObject.put("appNo",appNo);

                    arrayItem=new JSONObject();
                    arrayItem.put("id", IdGen.getUUID());
                    arrayItem.put("bedId",record.getBedId());
                    arrayItem.put("status",record.getDynamicType());
                    arrayItem.put("isLongStay",record.getIsLongStay());
                    arrayItem.put("gender",record.getGender());

                    JSONArray dataArray=new JSONArray();
                    dataArray.add(arrayItem);
                    paramObject.put("data",dataArray);

                }else if(Global.sojournAppNo.equals(appNo)){
                    url=Global.orgBedDynamicUrl;
                    //机构的床位动态发送到旅居
                    String baseId=record.getBaseId();
                    if(Global.orgBaseId.equalsIgnoreCase(baseId)){
                        paramObject=new JSONObject();
                        paramObject.put("baseId",record.getBaseId());
                        paramObject.put("appNo",appNo);

                        arrayItem=new JSONObject();
                        arrayItem.put("id",IdGen.getUUID());
                        arrayItem.put("bookNo",record.getBookNo());
                        arrayItem.put("checkinNo",record.getCheckinNo());
                        arrayItem.put("bedId",record.getBedId());
                        arrayItem.put("isLongStay",record.getIsLongStay());
                        arrayItem.put("cardNumber",record.getCardNumber());
                        arrayItem.put("cardName",record.getCardName());
                        arrayItem.put("memberName",record.getMemberName());
                        arrayItem.put("idcard",record.getIdcard());
                        arrayItem.put("telephone",record.getTelephone());
                        arrayItem.put("dynamicType",record.getDynamicType());
                        arrayItem.put("bookStartDate",record.getBookStartDate());
                        arrayItem.put("bookEndDate",record.getBookEndDate());
                        arrayItem.put("checkinDate",record.getCheckinDate());
                        arrayItem.put("checkoutDate",record.getCheckoutDate());

                        JSONArray dataArray=new JSONArray();
                        dataArray.add(arrayItem);
                        paramObject.put("data",dataArray);
                    }else{
                        //如果不是机构的id
                        ids.add(record.getId());
                        continue;
                    }
                }
                if(paramObject!=null && arrayItem!=null && StrKit.notBlank(url)){
                    boolean flag=false;
                    logger.info("接口参数："+JSON.toJSONString(paramObject));
                    String resultStr= HttpClientsUtils.httpPostRaw(url,JSON.toJSONString(paramObject),null,"UTF-8");
                    logger.info("接口返回信息："+resultStr);
                    if(resultStr.startsWith("{") && resultStr.endsWith("}")){
                        JSONObject resultObject=JSON.parseObject(resultStr);
                        if(resultObject.containsKey("Type") && "1".equals(resultObject.getString("Type")) && resultObject.containsKey("Data")){
                            JSONArray resultArray=resultObject.getJSONArray("Data");
                            if(resultArray.size()>0 && arrayItem.getString("id").equalsIgnoreCase(resultArray.getString(0))){
                                flag=true;
                            }
                        }
                    }
                    if(flag){
                        ids.add(record.getId());
                    }
                    record.setUpdateDate(new Date());
                }
            }
            if(ids.size()>0){
                String str="";
                for(int i=0;i<ids.size();i++){
                    str+="?,";
                }
                str=str.substring(0,str.length()-1);
                Db.update("update main_base_bed_dynamic_record set update_date=now(),sync_status=0 where id in ("+str+") ",ids.toArray());
            }

        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void updateExamineStatus(){

        List<String> startus1=Db.query("select id from main_examine_record where del_flag='0' and NOW() BETWEEN start_time and end_time and `status`='4' ");
        List<String> startus3=Db.query("select id from main_examine_record where del_flag='0' and  NOW()>end_time  and `status`='1' ");

        if(startus1.size()>0){
            String str="";
            for (String s : startus1) {
                Db.update("update main_examine_record set `status`='1',update_date=now() where id=? ",s);
            }
        }

        if(startus3.size()>0){
            for (String s : startus3) {
                Db.update("update main_examine_record set `status`='3',update_date=now() where id=? ",s);
            }
        }

    }

}
