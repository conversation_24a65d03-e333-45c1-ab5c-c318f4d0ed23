package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainDeductPointsConfigService;
import com.cszn.integrated.service.entity.main.MainBaseAdvanceBookSetting;
import com.cszn.integrated.service.entity.main.MainDeductPointsConfig;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;

@RequestMapping(value="/main/deductPointsConfig",viewPath = "/modules_page/main/deductPointsConfig")
public class MainDeductPointsConfigController extends BaseController {

    @Inject
    MainDeductPointsConfigService mainDeductPointsConfigService;

    public void index(){
        List<MainDeductPointsConfig> configList=mainDeductPointsConfigService.findDeductPointsConfigList();
        setAttr("configList",configList);
        render("deductPointsConfigIndex.html");
    }

    public void saveDeductPointsConfig(){
        Integer count=getParaToInt("count");
        String delIds=getPara("delIds");
        List<MainDeductPointsConfig> configList=new ArrayList<>();
        for(int i=1;i<=count;i++){
            MainDeductPointsConfig config=getBean(MainDeductPointsConfig.class,"configList["+i+"]",true);
            String dateStr=getPara("configList["+i+"].startDate");
            if(StrKit.notBlank(dateStr)){
                String[] dateStrArray=dateStr.split(" - ");
                if(dateStrArray!=null && dateStrArray.length==2){
                    config.setStartDate(DateUtils.parseDate(dateStrArray[0]));
                    config.setEndDate(DateUtils.parseDate(dateStrArray[1]));
                }
            }
            if(config.getStartDate()!=null && config.getEndDate()!=null){
                configList.add(config);
            }
        }
        for(int i=0;i<configList.size();i++){
            if(i!=0){
                //上一条
                MainDeductPointsConfig lastConfig=configList.get(i-1);
                MainDeductPointsConfig config=configList.get(i);
                Date lastConfigEndDate=lastConfig.getEndDate();
                Date configStartDate=config.getStartDate();
                if(!DateUtils.formatDate(DateUtils.getNextDay(lastConfigEndDate,1),"yyyy-MM-dd").equals(DateUtils.formatDate(configStartDate,"yyyy-MM-dd"))){
                    renderJson(Ret.fail("msg","请添加连续的时间段"));
                    return;
                }
            }
        }
        //boolean flag=false;
        boolean flag=mainDeductPointsConfigService.saveDeductPointsConfig(configList,delIds,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void test(){
        Date checkinDate= getDate("startDate");
        Date checkoutDate= getDate("endDate");

        List<Map<String,Object>> configList=new ArrayList<>();

        String sql="select * from main_deduct_points_config where (? BETWEEN start_date and end_date or ? BETWEEN start_date and end_date " +
                " or (?>start_date and ?<end_date) or (start_date>? and end_date<?) ) and del_flag='0' order by start_date ";
        List<Record> recordList= Db.find(sql,checkinDate,checkoutDate,checkinDate,checkoutDate,checkinDate,checkoutDate);

        Map<String,Object> map=new HashMap<>();
        map.put("code","10001");

        if(recordList==null || recordList.size()==0){
            map.put("msg","该时间段未匹配到点数扣除配置，请检查配置");
            renderJson(map);
            return;
        }
        //获取最小开始时间和最大结束时间
        Date minStartDate=recordList.get(0).getDate("start_date");
        Date maxEndDate=recordList.get(recordList.size()-1).getDate("end_date");
        if(DateUtils.compareDays(minStartDate,checkinDate)>0){
            map.put("msg","入住时间没有在点数扣除配置时间范围中，请检查配置");
            renderJson(map);
            return;
        }

        if(DateUtils.compareDays(checkoutDate,maxEndDate)>0){
            map.put("msg","退住时间没有在点数扣除配置时间范围中，请检查配置");
            renderJson(map);
            return;
        }



        for(Record record:recordList){
            Map<String,Object> config=new HashMap<>();
            config.put("startDate",record.getDate("start_date"));
            config.put("endDate",record.getDate("end_date"));
            config.put("deduct",record.getStr("deduct_points"));
            configList.add(config);
        }


        //获取所有时间集合
        List<Date> dateList=DateUtils.getBetweenDates(checkinDate,checkoutDate);
        Map<String,Double> resultMap=new TreeMap<>();
        Double total=0.0;
        for(int i=0;i<dateList.size();i++){
            Date date=dateList.get(i);
            if(i==dateList.size()-1){
                continue;
            }
            for(Map<String,Object> configMap:configList){
                Date configStartDate=(Date) configMap.get("startDate");
                Date configEndDate=(Date)configMap.get("endDate");
                Double deduct=Double.valueOf((String) configMap.get("deduct"));
                if(DateUtils.compareDays(date,configStartDate)>=0 && DateUtils.compareDays(date,configEndDate)<=0){
                    resultMap.put(DateUtils.formatDate(date,"yyyy-MM-dd"),deduct);
                }
            }
        }
        for(String key:resultMap.keySet()){
            total+=resultMap.get(key);
        }

        resultMap.put("total",total);
        renderJson(resultMap);
    }
}
