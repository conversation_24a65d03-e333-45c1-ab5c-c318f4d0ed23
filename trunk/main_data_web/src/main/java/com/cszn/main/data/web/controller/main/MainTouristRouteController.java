package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainTouristRouteDeductConfigService;
import com.cszn.integrated.service.api.main.MainTouristRouteService;
import com.cszn.integrated.service.entity.main.MainTouristRoute;
import com.cszn.integrated.service.entity.main.MainTouristRouteDeductConfig;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value = "/main/touristRoute",viewPath = "/modules_page/main/touristRoute")
public class MainTouristRouteController extends BaseController {

    @Inject
    private MainTouristRouteService mainTouristRouteService;
    @Inject
    private MainTouristRouteDeductConfigService mainTouristRouteDeductConfigService;

    public void index(){
        render("touristRouteIndex.html");
    }

    @Clear(LogInterceptor.class)
    public void findPageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainTouristRoute touristRoute=getBean(MainTouristRoute.class,"",true);
        Page<MainTouristRoute> page=mainTouristRouteService.findTouristRoutePage(pageNumber,pageSize,touristRoute);
        renderJson(new DataTable<MainTouristRoute>(page));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainTouristRoute touristRoute=mainTouristRouteService.findById(id);
            setAttr("touristRoute",touristRoute);
        }
        render("touristRouteForm.html");
    }

    /**
     * 保存
     */
    public void saveTouristRoute(){
        MainTouristRoute touristRoute=getBean(MainTouristRoute.class,"",true);
        if(mainTouristRouteService.findTouristRouteByIdName(touristRoute.getId(),touristRoute.getName())!=null){
            renderJson(Ret.fail("msg","该线路已经存在"));
            return;
        }
        boolean flag=mainTouristRouteService.saveTouristRoute(touristRoute, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 删除
     */
    public void delTouristRoute(){
        String id=getPara("id");
        boolean flag=mainTouristRouteService.delTouristRoute(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void deductConfigIndex(){
        String id = getPara("id");
        setAttr("id",id);
        render("deductConfigIndex.html");
    }

    public void deductConfigForm(){
        String routeId=getPara("routeId");
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainTouristRouteDeductConfig routeDeductConfig = mainTouristRouteDeductConfigService.findById(id);
            setAttr("model",routeDeductConfig);
        }
        setAttr("routeId",routeId);
        render("deductConfigForm.html");
    }

    public void deductConfigSave(){
        MainTouristRouteDeductConfig routeDeductConfig=getBean(MainTouristRouteDeductConfig.class,"",true);


        if(StrKit.notBlank(routeDeductConfig.getId())){
            if( Db.findFirst("select id from main_tourist_route_deduct_config where route_id=? and start_date=? and id<>? "
                    ,routeDeductConfig.getRouteId(),routeDeductConfig.getStartDate(),routeDeductConfig.getId())!=null){
                renderJson(Ret.fail("msg","该日期已配置过，请勿重复配置"));
                return;
            }
        }else{
            if( Db.findFirst("select id from main_tourist_route_deduct_config where route_id=? and start_date=?   "
                    ,routeDeductConfig.getRouteId(),routeDeductConfig.getStartDate())!=null){
                renderJson(Ret.fail("msg","该日期已配置过，请勿重复配置"));
                return;
            }
        }

        boolean flag = mainTouristRouteDeductConfigService.saveRouteDeductConfig(routeDeductConfig,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void deductConfigPageList(){
        String id = getPara("id");
        MainTouristRouteDeductConfig routeDeductConfig=new MainTouristRouteDeductConfig();
        routeDeductConfig.setRouteId(id);

        List<MainTouristRouteDeductConfig> routeDeductConfigList = mainTouristRouteDeductConfigService.findRouteDeductConfigList(routeDeductConfig);
        renderJson(new DataTable<MainTouristRouteDeductConfig>(routeDeductConfigList));
    }
}
