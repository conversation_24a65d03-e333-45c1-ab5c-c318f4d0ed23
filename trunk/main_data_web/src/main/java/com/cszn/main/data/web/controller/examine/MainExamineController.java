package com.cszn.main.data.web.controller.examine;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainExamineProblemService;
import com.cszn.integrated.service.api.main.MainExamineProblemTypeService;
import com.cszn.integrated.service.api.main.MainExamineSchemeService;
import com.cszn.integrated.service.api.main.MainExamineSchemeWeightedMeanConfigService;
import com.cszn.integrated.service.entity.enums.ExamineProblemType;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;

@RequestMapping(value="/mainExamine", viewPath="/modules_page/examine")
public class MainExamineController extends BaseController {

    @Inject
    private MainExamineProblemTypeService mainExamineProblemTypeService;
    @Inject
    private MainExamineProblemService mainExamineProblemService;
    @Inject
    private MainExamineSchemeService mainExamineSchemeService;
    @Inject
    private MainExamineSchemeWeightedMeanConfigService mainExamineSchemeWeightedMeanConfigService;

    public void typeIndex(){

        render("type/index.html");
    }

    public void typePageList(){
        MainExamineProblemType problemType=getBean(MainExamineProblemType.class,"",true);

        Page<MainExamineProblemType> problemTypePage=mainExamineProblemTypeService.findPageList(getParaToInt("page"),getParaToInt("limit"),problemType);

        renderJson(new DataTable<MainExamineProblemType>(problemTypePage));
    }

    public void typeTableTree(){
        List<Record> recordList=mainExamineProblemTypeService.page();
        renderJson(new DataTable<Record>(recordList));
    }

    public void typeTree() {
        String isLeaver=getPara("isLeaver");
        List<Record> recordList = mainExamineProblemTypeService.page();
        List<ZTree> zTreeList=new ArrayList<>();
        for(Record record:recordList){
            ZTree zTree=new ZTree(record.getStr("id"),record.getStr("name"),record.getStr("remark"),record.getStr("pId"),null);
            zTreeList.add(zTree);
        }
        renderJson(zTreeList);
    }

    public void typeXSSelectTree() {
        List<Record> recordList = mainExamineProblemTypeService.page();
        List<ZTree> zTreeList=new ArrayList<>();
        for(Record record:recordList){
            ZTree zTree=new ZTree(record.getStr("id"),record.getStr("name"),null,record.getStr("pId"),null);
            zTreeList.add(zTree);
        }
        List<ZTree> returnZtree=new ArrayList<>();
        for (ZTree tree1 : zTreeList) {
            if("00000000-0000-0000-0000-000000000000".equalsIgnoreCase(tree1.getpId())){
                returnZtree.add(tree1);
            }
            for (ZTree tree2 : zTreeList) {
                if(tree2.getpId().equalsIgnoreCase(tree1.getId())){
                    if(tree1.getChildren()==null){
                        List<ZTree> childrenZtreeList=new ArrayList<>();
                        childrenZtreeList.add(tree2);
                        tree1.setChildren(childrenZtreeList);
                    }else{
                        tree1.getChildren().add(tree2);
                    }
                }

            }
        }

        renderJson(returnZtree);
    }

    public void typeForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){

            MainExamineProblemType problemType=mainExamineProblemTypeService.findById(id);
            if(mainExamineProblemTypeService.findById(problemType.getParentId())!=null){
                setAttr("parentTypeName",mainExamineProblemTypeService.findById(problemType.getParentId()).getName());
            }
            setAttr("model",problemType);
        }

        render("type/form.html");
    }

    public void typeSave(){
        MainExamineProblemType problemType=getBean(MainExamineProblemType.class,"",true);
        boolean flag=mainExamineProblemTypeService.saveProblemType(problemType, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    public void problemIndex(){
        List<MainExamineProblemType> typeList=mainExamineProblemTypeService.findAllProblemType();

        setAttr("typeList",typeList);
        render("problem/index.html");
    }

    public void problemPageList(){
        MainExamineProblem problem=getBean(MainExamineProblem.class,"",true);
        String schemeId=getPara("schemeId");
        problem.setId(schemeId);
        Page<MainExamineProblem> problemPage=mainExamineProblemService.findPageList(getParaToInt("page"),getParaToInt("limit"),problem);
        renderJson(new DataTable<MainExamineProblem>(problemPage));
    }

    public void problemForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainExamineProblem problem=mainExamineProblemService.findById(id);
            setAttr("model",problem);

        }
        Map<String,String> problemTypeMap=new HashMap<>();



        setAttr("problemType", ExamineProblemType.values());
        List<MainExamineProblemType> typeList=mainExamineProblemTypeService.findAllProblemType();
        setAttr("typeList",typeList);
        render("problem/form.html");
    }

    public void problemSave(){
        MainExamineProblem problem=getBean(MainExamineProblem.class,"",true);
        Integer optionCount=getParaToInt("optionCount");
        List<MainExamineProblemOption> optionList=new ArrayList<>();
        List<MainExamineProblemOption> updateOptionList=new ArrayList<>();
        boolean flag=mainExamineProblemService.problemSave(problem,AuthUtils.getUserId());
        if(!ExamineProblemType.text.getKey().equals(problem.getType()) && optionCount!=null){
            int sort=1;
            for (Integer i = 1; i <= optionCount; i++) {
                String optionContent=getPara("option"+i);
                Double score=Double.valueOf(getPara("score"+i));
                String id=getPara("optionId"+i);
                String isEnabled=getPara("isEnabled"+i);
                String isOpenText=getPara("isOpenText"+i);
                String textName=getPara("textName"+i);
                if(StrKit.notBlank(optionContent)){
                    if(StrKit.isBlank(id)){
                        MainExamineProblemOption option=new MainExamineProblemOption();
                        option.setId(IdGen.getUUID());
                        option.setProblemId(problem.getId());
                        option.setContent(optionContent);
                        option.setScore(score);
                        option.setSort(sort);
                        option.setIsEnabled(isEnabled);
                        option.setIsOpenText(isOpenText);
                        option.setTextName(textName);
                        option.setCreateBy(AuthUtils.getUserId());
                        option.setCreateDate(new Date());
                        option.setUpdateBy(AuthUtils.getUserId());
                        option.setUpdateDate(new Date());
                        optionList.add(option);
                    }else{
                        MainExamineProblemOption option=new MainExamineProblemOption();
                        option.setId(id);
                        option.setProblemId(problem.getId());
                        option.setContent(optionContent);
                        option.setIsEnabled(isEnabled);
                        option.setScore(score);
                        option.setIsOpenText(isOpenText);
                        option.setTextName(textName);
                        option.setSort(sort);
                        option.setUpdateBy(AuthUtils.getUserId());
                        option.setUpdateDate(new Date());
                        updateOptionList.add(option);
                    }

                    sort++;
                }
            }

        }
        if(flag){
            if(optionList.size()>0){
                Db.batchSave(optionList,optionList.size());
            }
            if(updateOptionList.size()>0){
                Db.batchUpdate(updateOptionList,updateOptionList.size());
            }
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void schemeIndex(){

        render("scheme/index.html");
    }

    public void schemeForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainExamineScheme scheme=mainExamineSchemeService.findById(id);

            setAttr("model",scheme);
        }
        render("scheme/form.html");
    }

    public void schemePageList(){
        MainExamineScheme scheme=getBean(MainExamineScheme.class,"",true);

        Page<MainExamineScheme> schemePage=mainExamineSchemeService.pageList(getParaToInt("page"),getParaToInt("limit"),scheme);
        renderJson(new DataTable<MainExamineScheme>(schemePage));
    }

    public void schemeSave(){
        MainExamineScheme scheme=getBean(MainExamineScheme.class,"",true);

        boolean flag= mainExamineSchemeService.schemeSave(scheme,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.ok("msg","操作失败"));
        }
    }

    public void addProblemForm(){
        String id=getPara("id");
        MainExamineScheme scheme = mainExamineSchemeService.findById(id);
        setAttr("scheme",scheme);
        setAttr("id",id);
        List<MainExamineSchemeWeightedMeanConfig> weightedMeanConfigList = mainExamineSchemeWeightedMeanConfigService.findListBySchemeId(scheme.getId());
        setAttr("weightedMeanConfigList",weightedMeanConfigList);
        render("scheme/addProblemForm.html");
    }

    public void schemeProblemSave(){
        String id=getPara("id");
        String problemId=getPara("problemId");

        MainExamineSchemeProblemRel problemRel=new MainExamineSchemeProblemRel();

        MainExamineScheme scheme=mainExamineSchemeService.findById(id);

        problemRel.setId(IdGen.getUUID());
        problemRel.setProblemId(problemId);
        problemRel.setSchemeId(id);
        problemRel.setSort(scheme.getProblemList().size()+1);
        problemRel.setIsEnabled("1");
        problemRel.setCreateBy(AuthUtils.getUserId());
        problemRel.setCreateDate(new Date());
        problemRel.setUpdateBy(AuthUtils.getUserId());
        problemRel.setUpdateDate(new Date());

        if(problemRel.save()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void weightedMeanIndex(){
        String schemeId=getPara("schemeId");

        setAttr("schemeId",schemeId);
        render("scheme/weightedMeanIndex.html");
    }

    public void weightedMeanForm(){
        String id=getPara("id");
        String schemeId=getPara("schemeId");

        setAttr("schemeId",schemeId);
        if(StrKit.notBlank(id)){
            MainExamineSchemeWeightedMeanConfig weightedMeanConfig = mainExamineSchemeWeightedMeanConfigService.findById(id);
            setAttr("model",weightedMeanConfig);
        }
        render("scheme/weightedMeanForm.html");
    }

    public void weightedMeanConfigSave(){
        MainExamineSchemeWeightedMeanConfig weightedMeanConfig=getBean(MainExamineSchemeWeightedMeanConfig.class,"",true);

        boolean flag=false;
        if(StrKit.isBlank(weightedMeanConfig.getId())){
            weightedMeanConfig.setId(IdGen.getUUID());
            weightedMeanConfig.setDelFlag("0");
            weightedMeanConfig.setCreateBy(AuthUtils.getUserId());
            weightedMeanConfig.setCreateDate(new Date());
            weightedMeanConfig.setUpdateBy(AuthUtils.getUserId());
            weightedMeanConfig.setUpdateDate(new Date());
            flag=weightedMeanConfig.save();
        }else{
            weightedMeanConfig.setUpdateBy(AuthUtils.getUserId());
            weightedMeanConfig.setUpdateDate(new Date());
            flag=weightedMeanConfig.update();
        }

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void weightedMeanConfigList(){
        String schemeId=getPara("schemeId");

        List<MainExamineSchemeWeightedMeanConfig> weightedMeanConfigList = mainExamineSchemeWeightedMeanConfigService.findListBySchemeId(schemeId);
        renderJson(new DataTable<MainExamineSchemeWeightedMeanConfig>(weightedMeanConfigList));
    }

    public void findSchemeProblemList(){
        String id=getPara("schemeId");
        String sql="select a.id,a.is_enabled as isEnabled,a.sort,b.name as problemName,b.type,c.name as typeName,b.is_required as isRequired,a.weighted_mean_config_id as weightedMeanConfigId " +
                "from main_examine_scheme_problem_rel a " +
                "INNER JOIN main_examine_problem b on a.problem_id=b.id " +
                "INNER JOIN main_examine_problem_type c on c.id=b.problem_type_id where a.scheme_id=?  ORDER BY a.sort ";

        List<Record> recordList=Db.find(sql,id);

        renderJson(new DataTable<Record>(recordList));
    }

    public void schemeProblemRelSave(){
        String id=getPara("id");
        String isEnabled=getPara("isEnabled");

        int i=Db.update(" update main_examine_scheme_problem_rel set is_enabled=?,update_by=?,update_date=? where id=? ",isEnabled,AuthUtils.getUserId(),new Date(),id);
        if(i>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void schemeProblemRelSaveSort(){
        String jsonStr=getPara("sortDatas");

        JSONArray jsonArray= JSON.parseArray(jsonStr);

        boolean flag=false;
        if(jsonArray!=null && jsonArray.size()>0){

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject=jsonArray.getJSONObject(i);
                Db.update(" update main_examine_scheme_problem_rel set sort=? where id=? ",jsonObject.getIntValue("orgSort"),jsonObject.getString("id"));
            }
            flag=true;
        }


        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void schemeProblemWeightedMeanConfigSave(){
        String id=getPara("id");
        String configId=getPara("configId");

        int update = Db.update(" update main_examine_scheme_problem_rel set weighted_mean_config_id=? where id=? ", configId, id);
        if(update>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
