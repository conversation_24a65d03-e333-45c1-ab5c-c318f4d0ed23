package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.api.main.MainUserGroupService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.fina.FinaCardCollect;
import com.cszn.integrated.service.entity.food.FoodInfo;
import com.cszn.integrated.service.entity.main.MainUserGroup;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;
import java.util.stream.Collectors;

@RequestMapping(value = "/main/userGroup",viewPath = "/modules_page/main/userGroup")

public class MainUserGroupController extends BaseController {

    @Inject
    MainUserGroupService mainUserGroupService;
    @Inject
    MainSyncRecordService mainSyncRecordService;

    public void index(){
        render("index.html");
    }

    public void pageList(){
        MainUserGroup userGroup = getBean(MainUserGroup.class,"",true);
        Page<MainUserGroup> page=mainUserGroupService.findPageList(getParaToInt("page"),getParaToInt("limit"),userGroup);
        renderJson(new DataTable<MainUserGroup>(page));
    }

    public void form(){
        String id = getPara("id");
        if(StrKit.notBlank(id)){
            MainUserGroup userGroup = mainUserGroupService.findById(id);
            setAttr("model",userGroup);
            setAttr("relatedUser", Db.queryStr("select group_concat(user_id)userIds from main_user_group_rel where user_group_id=? group by user_group_id", id));
        }
        render("form.html");
    }

    public void saveUserGroup(){
        final String userId = AuthUtils.getUserId();
        final String relatedUser = getPara("relatedUser");
        List<String> userIdList = new ArrayList<String>();
        if(StrKit.notBlank(relatedUser)){
            String[] userIdArray = relatedUser.split(",");
            userIdList = Arrays.asList(userIdArray);
        }
        MainUserGroup userGroup = getBean(MainUserGroup.class,"",true);
        boolean flag=mainUserGroupService.saveUserGroup(userGroup, userId, relatedUser);
        if(flag){
            final String idStr = "["+userIdList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
            //调用旅居接口保存分组和用户关联关系
            Global.executorService.execute(new Runnable() {
                @Override
                public void run() {
                    Map<String,String> paramMap=new HashMap<>();
                    paramMap.put("userId",userId);
                    paramMap.put("groupId",userGroup.getId());
                    paramMap.put("userIds",idStr);
                    final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Account/SetGroupUsers",paramMap,null,null);
                    System.out.println("调用更新合同编号接口返回==="+resultStr+"；参数是："+paramMap.toString());
                }
            });
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void update(){
        final String userId = AuthUtils.getUserId();
        MainUserGroup model = getBean(MainUserGroup.class,"",true);
        model.setUpdateBy(userId);
        model.setUpdateDate(new Date());
        if(mainUserGroupService.update(model)) {
            boolean flag = Boolean.FALSE;
            //禁用
            if("0".equalsIgnoreCase(model.getIsEnable())){
                List<String> idList = Arrays.asList(new String[]{model.getId()});
                final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
                flag = mainSyncRecordService.saveSyncRecord(SyncType.mainUserGroup.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
            //启用
            }else{
                flag = mainSyncRecordService.saveSyncRecord(SyncType.mainUserGroup.getKey(), SyncDataType.UPDATE, JSON.toJSONString(model), userId);
            }
            if(flag){
                renderJson(Ret.ok("msg","操作成功"));
            }else{
                renderJson(Ret.fail("msg","操作失败,同步数据失败"));
            }
        }else {
            renderJson(Ret.fail("msg","更新用户分组数据失败"));
        }
    }
}
