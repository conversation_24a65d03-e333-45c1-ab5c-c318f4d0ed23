package com.cszn.main.data.web.controller.sys;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;

import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import com.cszn.integrated.base.utils.FileUploadKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FileDealService;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.upload.UploadFile;

import io.jboot.Jboot;
import io.jboot.web.controller.annotation.RequestMapping;

/**
 * @Description 文件处理controller
 * <AUTHOR>
 * @Date 2019/4/12
 **/
@RequestMapping(value = "/sys/upload",viewPath = "")
public class FileDealController extends BaseController {

    private Logger logger = Logger.getLogger(FileDealController.class);

    @Inject
    FileDealService fileDealService;


    public void index() {
        UploadFile file = getFile();
        String folder = getPara("folder");// 上传的文件夹
        Map<String, Object> data = FileUploadKit.uploadFile(file, folder);

        renderJson(Ret.ok("msg","上传成功！").set("data",data));
    }


    /**
     * excel上传
     */
    public void excelUpload(){

        UploadFile uploadFile = getFile();
        if(uploadFile == null){
           renderJson(Ret.fail("msg","文件不存在"));
           return;
        }
        boolean flag = false;
        try {
            flag = fileDealService.dealExcel(uploadFile.getFile(),uploadFile.getFileName(), AuthUtils.getUserId());
        } catch (Exception e) {
            //e.printStackTrace();
            logger.error("会员卡导入异常：" + e.getMessage(),e);
        }
        if(flag){
           renderJson(Ret.ok("msg","导入成功"));
        }else{
            renderJson(Ret.fail("msg","导入失败"));
        }
    }


    /**
     * excel下载
     */
    public void excelExport(){
        String excelTemplatePath = Jboot.configValue("excelTemplatePath");

        File file = new File(excelTemplatePath);
        String fileName = "会员信息导入模板.xls";

        try {
            HSSFWorkbook wb = new HSSFWorkbook(new FileInputStream(new File(excelTemplatePath)));
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
