package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@RequestMapping(value = "/main/bedQuery",viewPath = "/modules_page/main/bedQuery")
public class BedQueryController extends BaseController {

    @Inject
    MainBaseBuildingService mainBaseBuildingService;

    @Inject
    MainBaseFloorService mainBaseFloorService;

    @Inject
    MainBaseRoomService mainBaseRoomService;

    @Inject
    MainBaseBedService mainBaseBedService;

    @Inject
    MainBaseService mainBaseService;

    /**
     * 床位查询首页
     */
    public void index(){
        render("bedQueryIndex.html");
    }

    /**
     * 获取所有基地
     */
    @Clear(LogInterceptor.class)
    public void getBase(){
        List<MainBase> baseList = mainBaseService.findAll();
        renderJson(Ret.ok("data",baseList));
    }

    /**
     * 通过基地id获取楼栋
     */
    @Clear(LogInterceptor.class)
    public void getBuilding(){
        String baseIds=getPara("baseIds");
        if(StringUtils.isBlank(baseIds)){
            renderJson(Ret.fail("msg","错误参数"));
            return;
        }
        String[] ids=null;
        if(baseIds.indexOf(",")!=-1){
            ids=baseIds.split(",");
        }else{
            ids=new String[]{baseIds};
        }
        List<MainBaseBuilding> buildingList = mainBaseBuildingService.getBuildingListByBaseIds(ids);
        renderJson(Ret.ok("data",buildingList));
    }

    /**
     * 通过楼栋获取楼层
     */
    @Clear(LogInterceptor.class)
    public void getFloor(){
        String buildingIds=getPara("buildingIds");
        if(StringUtils.isBlank(buildingIds)){
            renderJson(Ret.fail("msg","错误参数"));
            return;
        }

        String[] ids=null;
        if(buildingIds.indexOf(",")!=-1){
            ids=buildingIds.split(",");
        }else{
            ids=new String[]{buildingIds};
        }
        List<MainBaseFloor> floorList=mainBaseFloorService.getFloorListByBuildingIds(ids);
        renderJson(Ret.ok("data",floorList));
    }

    /**
     * 通过楼层获取房间
     */
    @Clear(LogInterceptor.class)
    public void getRoom(){
        String floorIds=getPara("floorIds");
        if(StringUtils.isBlank(floorIds)){
            renderJson(Ret.fail("msg","错误参数"));
            return;
        }

        String[] ids=null;
        if(floorIds.indexOf(",")!=-1){
            ids=floorIds.split(",");
        }else{
            ids=new String[]{floorIds};
        }
        List<MainBaseRoom> roomList=mainBaseRoomService.getRoomListByFloorIds(ids);
        renderJson(Ret.ok("data",roomList));
    }

    /**
     * 通过房间获取床位分页
     */
    @Clear(LogInterceptor.class)
    public void getBedPage(){
        String roomIds=getPara("roomIds");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        if(StringUtils.isBlank(roomIds)){
            renderJson(Ret.fail("msg","错误参数"));
            return;
        }

        String[] ids=null;
        if(roomIds.indexOf(",")!=-1){
            ids=roomIds.split(",");
        }else{
            ids=new String[]{roomIds};
        }
        Page<Record> bedPage=mainBaseBedService.getBedPageByRoomIds(ids,pageNumber,pageSize);
        renderJson(new DataTable<Record>(bedPage));
    }


}
