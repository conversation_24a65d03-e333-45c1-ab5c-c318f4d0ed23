package com.cszn.main.data.web.controller.main;



import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainContractTypeService;
import com.cszn.integrated.service.entity.main.MainContractType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value="/main/contratType",viewPath = "/modules_page/main/contractType")
public class MainContractTypeController extends BaseController {
	
	@Inject
	private MainContractTypeService mainContractTypeService;
	
	/**
	 * 跳转合同类型界面
	 */
	public void index() {
		render("contractTypeIndex.html");
	}

	
	/**
	 * 跳转合同类型分页
	 */
	@Clear(LogInterceptor.class)
	public void pageList() {
		 Integer pageNumber=getParaToInt("page");
	     Integer pageSize=getParaToInt("limit");
	     String typeName = getPara("typeName");
	     MainContractType contractType = new MainContractType();
	     contractType.setTypeName(typeName);
	     Page<MainContractType> page = mainContractTypeService.findListPage(pageNumber, pageSize, contractType);
	     renderJson(new DataTable<MainContractType>(page));
	}
	
	/**
	 * 跳转合同类型表单页
	 */
	public void contractTypeForm() {
		String id = getPara("id");
		if (StrKit.notBlank(id)) {
			MainContractType contractType = mainContractTypeService.findById(id);
			setAttr("contractType", contractType);
		}
		render("contractTypeForm.html");
	}
	/**
	 * 保存合同类型表单页
	 */
	public void saveContractType() {
		MainContractType contractType = getBean(MainContractType.class,"",true);
		if(StrKit.isBlank(contractType.getId())){
			//添加时判断合同类型编号是否重复
			if(mainContractTypeService.findContractTypeByTypeNo(contractType.getTypeNo())!=null){
				renderJson(Ret.fail("msg","该合同编号已存在"));
				return;
			}
		}else{
			//编辑时判断合同类型编号是否重复
			MainContractType type=mainContractTypeService.findContractTypeByTypeNo(contractType.getTypeNo());
			if(type!=null && !type.getId().equals(contractType.getId())){
				renderJson(Ret.fail("msg","该合同编号已存在"));
				return;
			}
		}

		boolean flag = mainContractTypeService.saveContractType(contractType, AuthUtils.getUserId());
		if(flag) {
			renderJson(Ret.ok("msg","操作成功"));
		}else {
			renderJson(Ret.fail("msg","操作失败"));
		}
	}
	
	/**
	 * 单个删除合同类型
	 */
	public void delContractType() {
		String id = getPara("id");
		if(StrKit.isBlank(id)) {
			renderJson(Ret.fail("msg", "id不能为空"));
			return;
		}
		boolean flag = mainContractTypeService.delContractType(id, AuthUtils.getUserId());
		if(flag){
	        renderJson(Ret.ok("msg","操作成功"));
	    }else{
	        renderJson(Ret.fail("msg","操作成功"));
	    }
	}
	
	/**
	 * 批量删除
	 */
	public void batchContractType() {
		String data = getPara("data");
		if(StrKit.isBlank(data) || !data.startsWith("[{") || !data.endsWith("}]")) {
			renderJson(Ret.fail("msg", "参数异常"));
			return;
		}
		boolean flag = mainContractTypeService.batchContractType(data, AuthUtils.getUserId());
		if(flag) {
			renderJson(Ret.ok("msg","操作成功"));
		}else {
			renderJson(Ret.fail("msg","操作成功"));
		}
	}
}
