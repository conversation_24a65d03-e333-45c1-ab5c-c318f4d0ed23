/**
 * 
 */
package com.cszn.main.data.web.controller.food;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.food.FoodCategoryService;
import com.cszn.integrated.service.api.food.FoodInfoService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.food.FoodCategory;
import com.cszn.integrated.service.entity.food.FoodInfo;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2021年9月28日
 *
 * FoodController
 */
@RequestMapping(value="/food", viewPath="/modules_page/food")
public class FoodController extends BaseController {

	@Inject
    private FoodCategoryService foodCategoryService;
	@Inject
    private FoodInfoService foodInfoService;
	@Inject
    MainSyncRecordService mainSyncRecordService;
	
    public void categoryIndex() {
        render("categoryIndex.html");
    }
    
    public void infoIndex() {
    	render("infoIndex.html");
    }
    
    public void categoryTableTree(){
        List<ZTree> zTrees=foodCategoryService.findCategoryTableZtree(false);
        renderJson(new DataTable<ZTree>(zTrees));
    }

    public void categoryTree(){
        List<ZTree> zTrees=foodCategoryService.findCategoryTree(false);
        renderJson(zTrees);
    }
    
    /**
     * 菜信息分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	FoodInfo model = getBean(FoodInfo.class, "", true);
        Page<FoodInfo> modelPage = foodInfoService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FoodInfo>(modelPage));
    }
    
    public void categoryForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
        	FoodCategory category=foodCategoryService.findById(id);
            if(category!=null){
            	FoodCategory parentCategory=foodCategoryService.findById(id);
                setAttr("parentCategory",parentCategory);
            }
            setAttr("category",category);
        }
        render("categoryForm.html");
    }

    public void infoForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
        	FoodInfo model=foodInfoService.findById(id);
            if(model!=null){
            	FoodCategory category = foodCategoryService.findById(model.getCategoryId());
                setAttr("category",category);
            }
            setAttr("model",model);
        }
        setAttr("userId",AuthUtils.getUserId());
        setAttr("uploadDomain",Global.commonUpload);
        render("infoForm.html");
    }

    public void saveCategory(){
        FoodCategory model = getBean(FoodCategory.class,"",true);
        if(StrKit.notBlank(model.getId())){
        	model.setUpdateBy(AuthUtils.getUserId());
        }else{
        	model.setUpdateBy(AuthUtils.getUserId());
        	model.setCreateBy(AuthUtils.getUserId());
        }
        boolean flag=foodCategoryService.saveCategory(model);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
    
    public void saveInfo(){
    	FoodInfo model = getBean(FoodInfo.class,"",true);
    	final int fileCount = getParaToInt("fileCount");
    	List<CfsFileUpload> fileList = new ArrayList<CfsFileUpload>();
    	if(fileCount>0){
    		for (int i = 1; i <= fileCount; i++) {
    			final CfsFileUpload file = getBean(CfsFileUpload.class, "fileList["+i+"]");
    			if(StrKit.notBlank(file.getId())) {
    				fileList.add(file);
    			}
    		}
    	}
    	Long foodCount = 0l;
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		foodCount = Db.queryLong("select count(id)foodCount from food_info where del_flag=? and category_id=? and food_name=? and id!=?",DelFlag.NORMAL, model.getCategoryId(), model.getFoodName(),model.getId());
    	}else{
    		foodCount = Db.queryLong("select count(id)foodCount from food_info where del_flag=? and category_id=? and food_name=?", DelFlag.NORMAL, model.getCategoryId(), model.getFoodName());
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setCreateBy(AuthUtils.getUserId());
    	}
    	if(foodCount>0) {
    		renderJson(Ret.fail("msg","操作失败,菜名称已存在"));
    	}else {
    		if(foodInfoService.saveInfo(model, fileList)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg","操作失败"));
    		}
    	}
    }
    
    public void delInfo(){
    	FoodInfo model = getBean(FoodInfo.class,"",true);
		model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateTime(new Date());
		if(foodInfoService.update(model)) {
			List<String> idList = Arrays.asList(new String[]{model.getId()});
			final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
			boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.foodInfo.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
			if(flag){
				renderJson(Ret.ok("msg","操作成功"));
			}else{
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
			}
		}else {
			renderJson(Ret.fail("msg","操作失败"));
		}
    }
}
