package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBookChannelService;
import com.cszn.integrated.service.entity.enums.BookChannel;
import com.cszn.integrated.service.entity.main.MainBookChannel;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.Map;

@RequestMapping(value = "/main/bookChannel",viewPath = "/modules_page/main/bookChannel/")
public class MainBookChannelMainController extends BaseController {

    @Inject
    private MainBookChannelService mainBookChannelService;

    /**
     * 预订渠道首页
     */
    public void index(){
        render("bookChannelIndex.html");
    }

    /**
     * 预订渠道分页
     */
    @Clear(LogInterceptor.class)
    public void findPageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainBookChannel bookChannel=getBean(MainBookChannel.class,"",true);
        Page<MainBookChannel> bookChannelPage=mainBookChannelService.findPageList(pageNumber,pageSize,bookChannel);
        renderJson(new DataTable<MainBookChannel>(bookChannelPage));
    }

    /**
     * 预订渠道表单页面
     */
    public void bookChannelForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainBookChannel bookChannel=mainBookChannelService.findById(id);
            setAttr("bookChannel",bookChannel);
        }
        Map<String,String> map=new HashMap<>();
        for(BookChannel type: BookChannel.values()){
            map.put(type.getKey(),type.getValue());
        }
        setAttr("bookChannelCode",map);
        render("bookChannelForm.html");
    }

    /**
     * 保存预订渠道
     */
    public void saveBookChannel(){
        MainBookChannel bookChannel=getBean(MainBookChannel.class,"bookChannel",true);

        if(mainBookChannelService.bookChannelIsExist(bookChannel)!=null){
            renderJson(Ret.fail("msg","名字或编号已经存在"));
            return;
        }

        boolean flag=mainBookChannelService.saveBookChannel(bookChannel, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 删除预订渠道
     */
    public void delBookChannel(){
        String id=getPara("id");
        boolean flag=mainBookChannelService.delBookChannel(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
