package com.cszn.main.data.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.main.MainSmsConditionList;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Cron("*/1 * * * *")
public class SmsSendTask implements Runnable {
	
	private static Logger logger = LoggerFactory.getLogger(SmsSendTask.class);

    @Override
    public void run() {
    	List<String> smsCodeList = Arrays.asList(new String[]{"cardType","org","file","videoNumber"});
        for(String smsCode : smsCodeList){
        	
        	String recordId = "";
        	String phoneNum = "";
        	String sendContent = "";
        	List<MainSmsConditionList> updateList = new ArrayList<MainSmsConditionList>();
        	if("videoNumber".equals(smsCode)){//视频推广短信内容不一样,要一条一条发送
        		Record record = Db.findFirst("select * from main_sms_condition_list "
    				+ "where del_flag='0' and send_status='0' and sms_condition_code=? and send_time<=now() limit 1", smsCode);
        		if(record!=null){
        			recordId = record.getStr("id");
        			phoneNum = record.getStr("phone_number");
        			sendContent = record.getStr("send_content");
        		}
        	}else{//会员卡类别、员工、文件导入发送内容一样,要批量发送
        		Record record = Db.findFirst("select group_concat(id) as recordIds,group_concat(phone_number) as phoneNumbers,"
    				+ "group_concat(distinct send_content)sendContent from ("
    				+ "select id,phone_number,send_content from main_sms_condition_list "
    				+ "where del_flag='0' and send_status='0' and sms_condition_code=? and send_time<=now() order by create_time limit 500"
    				+ ")V", smsCode);
        		if(record!=null){
        			recordId = record.getStr("recordIds");
    				phoneNum = record.getStr("phoneNumbers");
    				sendContent = record.getStr("sendContent");
        		}
        	}
        	//发送短信
        	if(StrKit.notBlank(recordId) && StrKit.notBlank(phoneNum) && StrKit.notBlank(sendContent)){
        		Map<String,String> sendParams=new HashMap<>();
        		sendParams.put("tempId", SendType.customContent.getTemplateId());
        		sendParams.put("mobile",phoneNum);
        		sendParams.put("data", "{\"content\":\""+ sendContent+"\"}");
        		String resultStr = HttpClientsUtils.httpPostForm(Global.sendMessageUrl, sendParams,null,"UTF-8");
        		if(resultStr.startsWith("{") && resultStr.endsWith("}")) {
        			JSONObject returnObj = JSON.parseObject(resultStr);
        			if(recordId.contains(",")){//包含,号是多个id用,号分隔
        				String[] idArray = recordId.split(",");
        				final Date realSendTime = new Date();
        				if(idArray!=null && idArray.length>0){
        					for(String id : idArray){
        						MainSmsConditionList model = new MainSmsConditionList();
                				model.setId(id);
                				model.setSendStatus("1");
                				model.setRealSendTime(realSendTime);
                				if (returnObj.containsKey("Type") && "1".equals(returnObj.getString("Type"))) {
                					model.setIsSuccess("1");
                				}else{
                					model.setErrorMsg(resultStr);
                				}
                				updateList.add(model);
        					}
        				}
        			}else{//没有包含,号就是单个id
        				MainSmsConditionList model = new MainSmsConditionList();
        				model.setId(recordId);
        				model.setSendStatus("1");
        				model.setRealSendTime(new Date());
        				if (returnObj.containsKey("Type") && "1".equals(returnObj.getString("Type"))) {
        					model.setIsSuccess("1");
        				}else{
        					model.setErrorMsg(resultStr);
        				}
        				updateList.add(model);
        			}
        			if (returnObj.containsKey("Type") && "1".equals(returnObj.getString("Type"))) {
        				logger.info(smsCode + "短信发送成功:" + updateList.size() + "条");
        			}else{
        				logger.info(smsCode + "短信发送失败:" + updateList.size() + "条");
        			}
        		}
        		//更新短信发送记录
        		if(updateList!=null && updateList.size()>0){
        			Db.batchUpdate(updateList, updateList.size());
        		}
        	}else{
        		logger.info(smsCode + "没有短信发送记录!!!");
        	}
        }
    }
}
