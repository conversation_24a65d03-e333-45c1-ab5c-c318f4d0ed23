package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCostTypeService;
import com.cszn.integrated.service.entity.enums.CostType;
import com.cszn.integrated.service.entity.main.MainCostType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;

@RequestMapping(value = "/main/costType",viewPath = "/modules_page/main/costType/")
public class MainCostTypeController extends BaseController {

    @Inject
    private MainCostTypeService mainCostTypeService;

    public void index(){
        render("index.html");
    }

    public void pageList(){
        int page=getParaToInt("page");
        int limit=getParaToInt("limit");
        MainCostType costType=getBean(MainCostType.class,"",true);
        Page<MainCostType> mainCostTypePage=mainCostTypeService.pageList(page,limit,costType);

        CostType[] costTypes=CostType.values();
        for(MainCostType type:mainCostTypePage.getList()){
            for(CostType typeEnums:costTypes){
                if(type.equals(typeEnums.getKey())){
                    type.setType(typeEnums.getValue());
                }
            }
        }
        renderJson(new DataTable<MainCostType>(mainCostTypePage));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainCostType costType=mainCostTypeService.findById(id);
            setAttr("costType",costType);
        }
        CostType[] costTypes=CostType.values();
        Map<String,String> typeMap=new HashMap<>();
        for(CostType typeEnums:costTypes){
            typeMap.put(typeEnums.getKey(),typeEnums.getValue());
        }
        setAttr("typeMap",typeMap);
        render("form.html");
    }

    public void saveCostType(){
        MainCostType costType=getBean(MainCostType.class,"",true);
        if(mainCostTypeService.saveCostType(costType, AuthUtils.getUserId())){

            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }



}
