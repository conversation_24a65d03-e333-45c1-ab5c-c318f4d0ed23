package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainRoomTypeFirstLevelService;
import com.cszn.integrated.service.api.main.MainRoomTypeSecondLevelService;
import com.cszn.integrated.service.entity.main.MainRoomTypeFirstLevel;
import com.cszn.integrated.service.entity.main.MainRoomTypeSecondLevel;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value = "/main/secondLevel",viewPath = "/modules_page/main/secondLevel/")
public class MainRoomTypeSecondLevelController  extends BaseController {

    @Inject
    MainRoomTypeSecondLevelService mainRoomTypeSecondLevelService;
    @Inject
    MainRoomTypeFirstLevelService mainRoomTypeFirstLevelService;

    public void index(){
        List<MainRoomTypeFirstLevel> firstLevelList=mainRoomTypeFirstLevelService.getList();
        setAttr("firstLevelList",firstLevelList);
        render("index.html");
    }

    public void pageList(){
        MainRoomTypeSecondLevel secondLevel=getBean(MainRoomTypeSecondLevel.class,"",true);

        Page<MainRoomTypeSecondLevel> page=mainRoomTypeSecondLevelService.pageList(getParaToInt("page"),getParaToInt("limit"),secondLevel);

        renderJson(new DataTable<MainRoomTypeSecondLevel>(page));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainRoomTypeSecondLevel secondLevel=mainRoomTypeSecondLevelService.findById(id);
            setAttr("model",secondLevel);
        }
        List<MainRoomTypeFirstLevel> firstLevelList=mainRoomTypeFirstLevelService.getList();
        setAttr("firstLevelList",firstLevelList);

        render("form.html");
    }

    public void save(){
        MainRoomTypeSecondLevel secondLevel=getBean(MainRoomTypeSecondLevel.class,"",true);

        boolean flag=mainRoomTypeSecondLevelService.saveSecondLevel(secondLevel, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
