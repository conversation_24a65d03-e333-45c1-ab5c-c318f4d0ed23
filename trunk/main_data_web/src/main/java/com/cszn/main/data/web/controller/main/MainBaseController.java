package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ZXingCode;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.food.FoodInfoService;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.enums.BaseBuildingFloorChannelType;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 基地管理
 * <AUTHOR>
 * @Date 2019/6/18
 **/
@RequestMapping(value="/main/base", viewPath="/modules_page/main/base")
public class MainBaseController extends BaseController {

    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private AreaService areaService;
    @Inject
    private MainBaseBuildingService mainBaseBuildingService;
    @Inject
    private MainBookChannelService mainBookChannelService;
    @Inject
    private MainBaseBuildingFloorChannelService mainBaseBuildingFloorChannelService;
    @Inject
    private UserService userService;
    @Inject
    private MainBaseMealTimeSettingService mainBaseMealTimeSettingService;
    @Inject
    private FoodInfoService foodInfoService;
    @Inject
    private MainBaseFoodTagService mainBaseFoodTagService;
    @Inject
    private MainBaseFoodService mainBaseFoodService;
    @Inject
    MainBaseRestaurantService mainBaseRestaurantService;
    @Inject
    MainBaseRestaurantTableService mainBaseRestaurantTableService;
    @Inject
    MainBasePackageService mainBasePackageService;
    @Inject
    MainBasePackageFoodService mainBasePackageFoodService;
    @Inject
    MainBaseHandCardService mainBaseHandCardService;
    @Inject
    MainBaseCustomerTypeService mainBaseCustomerTypeService;
    @Inject
    MainBaseTeamTypeService mainBaseTeamTypeService;
    @Inject
    MainSyncRecordService mainSyncRecordService;
    @Inject
	private DictService dictService;

    /**
     * 跳转基地管理界面
     */
    public void index(){
       render("baseIndex.html");
    }


    /**
     * 跳转地图界面
     */
    @Clear(LogInterceptor.class)
    public void map(){
        render("baseMap.html");
    }

    /**
     * 用餐时间列表界面
     */
    public void mealTimeIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	setAttr("mealTimeList",mainBaseMealTimeSettingService.findMealTimeList(id));
    	render("mealTimeIndex.html");
    }
    
    /**
     * 菜标签界面
     */
    public void foodTagIndex(){
    	String id = getPara("id");
    	setAttr("baseId",id);
    	setAttr("foodTagList",mainBaseFoodTagService.findList(id, ""));
    	render("foodTagIndex.html");
    }
    
    /**
     * 菜列表界面
     */
    public void foodListIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	setAttr("foodTagList",mainBaseFoodTagService.findList(id, "1"));
    	render("foodListIndex.html");
    }
    
    /**
     * 餐厅列表界面
     */
    public void restaurantIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	render("restaurantIndex.html");
    }

    /**
     * 套餐列表界面
     */
    public void packageIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	render("packageIndex.html");
    }
    
    /**
     * 手牌列表界面
     */
    public void handCardIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	render("handCardIndex.html");
    }
    
    /**
     * 客户类型列表界面
     */
    public void customerTypeIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	render("customerTypeIndex.html");
    }
    
    /**
     * 基地团队类型表界面
     */
    public void teamTypeIndex(){
    	final String id = getPara("id");
    	setAttr("baseId",id);
    	render("teamTypeIndex.html");
    }

    /**
     * 基地列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainBase base = getBean(MainBase.class,"",true);
        Page<MainBase> page = mainBaseService.findListPage(getParaToInt("page"),getParaToInt("limit"),base);
        renderJson(new DataTable<MainBase>(page));
    }
    
    /**
     * 菜列表
     */
    @Clear(LogInterceptor.class)
    public void foodPage(){
    	final String baseId = getPara("baseId");
    	final String tagId = getPara("tagId");
    	final String isEnabled = getPara("isEnabled");
    	final String isShelf = getPara("isShelf");
    	final String foodName = getPara("foodName");
    	Page<Record> page = mainBaseFoodService.findFoodPage(getParaToInt("page",1),getParaToInt("limit",10), baseId, tagId, isEnabled, isShelf, foodName, "onlyFood");
    	renderJson(new DataTable<Record>(page));
    }
    
    /**
     * 餐厅列表
     */
    @Clear(LogInterceptor.class)
    public void restaurantPage(){
    	MainBaseRestaurant model = getBean(MainBaseRestaurant.class,"",true);
    	Page<MainBaseRestaurant> page = mainBaseRestaurantService.paginate(model,getParaToInt("page"),getParaToInt("limit"));
    	renderJson(new DataTable<MainBaseRestaurant>(page));
    }
    
    /**
     * 餐桌列表
     */
    @Clear(LogInterceptor.class)
    public void restaurantTablePage(){
    	MainBaseRestaurantTable model = getBean(MainBaseRestaurantTable.class,"",true);
    	Page<MainBaseRestaurantTable> page = mainBaseRestaurantTableService.paginate(model,getParaToInt("page"),getParaToInt("limit"));
    	renderJson(new DataTable<MainBaseRestaurantTable>(page));
    }
    
    /**
     * 套餐列表
     */
    @Clear(LogInterceptor.class)
    public void packagePage(){
    	MainBasePackage model = getBean(MainBasePackage.class,"",true);
    	Page<MainBasePackage> page = mainBasePackageService.paginateByCondition(model,getParaToInt("page",1),getParaToInt("limit",10));
    	renderJson(new DataTable<MainBasePackage>(page));
    }
    
    /**
     * 手牌列表
     */
    @Clear(LogInterceptor.class)
    public void handCardPage(){
    	MainBaseHandCard model = getBean(MainBaseHandCard.class,"",true);
    	Page<MainBaseHandCard> page = mainBaseHandCardService.paginateByCondition(model,getParaToInt("page",1),getParaToInt("limit",10));
    	renderJson(new DataTable<MainBaseHandCard>(page));
    }
    
    /**
     * 客户类型列表
     */
    @Clear(LogInterceptor.class)
    public void customerTypePage(){
    	MainBaseCustomerType model = getBean(MainBaseCustomerType.class,"",true);
    	Page<MainBaseCustomerType> page = mainBaseCustomerTypeService.paginateByCondition(model,getParaToInt("page",1),getParaToInt("limit",10));
    	renderJson(new DataTable<MainBaseCustomerType>(page));
    }
    
    /**
     * 基地团队类型列表
     */
    @Clear(LogInterceptor.class)
    public void teamTypePage(){
    	MainBaseTeamType model = getBean(MainBaseTeamType.class,"",true);
    	Page<MainBaseTeamType> page = mainBaseTeamTypeService.paginateByCondition(model,getParaToInt("page",1),getParaToInt("limit",10));
    	renderJson(new DataTable<MainBaseTeamType>(page));
    }

    /**
     * 跳转基地 新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MainBase base = mainBaseService.get(id);
        if(base != null){
            if(StrKit.notBlank(base.getAuditUserId())){
                User user=userService.findById(base.getAuditUserId());
                if(user!=null){
                    base.put("auditUserName",user.getName());
                }
            }
            if(StringUtils.isNotBlank(base.getProvinceId())) {
                Area area = areaService.findById(base.getProvinceId());
                if(area != null)setAttr("province",area.getAreaName());
            }
            if(StringUtils.isNotBlank(base.getCityId())) {
                Area area = areaService.findById(base.getCityId());
                if(area != null)setAttr("city",area.getAreaName());
            }
            if(StringUtils.isNotBlank(base.getTownId())) {
                Area area = areaService.findById(base.getTownId());
                if(area != null)setAttr("town",area.getAreaName());
            }
            if(StringUtils.isNotBlank(base.getStreetId())) {
                Area area = areaService.findById(base.getStreetId());
                if(area != null)setAttr("street",area.getAreaName());
            }
            List<MainBaseBuildingFloorChannel> channelList=mainBaseBuildingFloorChannelService.findChannelByTypeAndJoinId(BaseBuildingFloorChannelType.base.getKey(),id);
            String baseChannelIds="";
            for(MainBaseBuildingFloorChannel channel:channelList){
                baseChannelIds+=channel.getChannelId()+",";
            }
            if(baseChannelIds.indexOf(",")!=-1){
                baseChannelIds=baseChannelIds.substring(0,baseChannelIds.length()-1);
            }
            setAttr("baseChannelIds",baseChannelIds);
        }
        Map<String,String> params=new HashMap<>();
        params.put("configNo","CSCOM0147");
        params.put("paramStr","[{\"ParamName\":\"TheIds\",\"Value\":\"5988911e-e5e5-4917-97d0-c8a7c9cec753\"}]");
        params.put("userId",AuthUtils.getUserId());
        String rest = HttpClientsUtils.httpPostForm(Global.getDataBySIConfigUrl,params,null,null);

        if(rest.startsWith("{") && rest.endsWith("}")){
            JSONObject object=JSON.parseObject(rest);
            if(object!=null && "1".equals(object.getString("Type"))){
                List<Record> recordList=new ArrayList<>();
                JSONArray dataArray = object.getJSONArray("Data");
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject data=dataArray.getJSONObject(i);
                    Record record=new Record();
                    record.set("id",data.getString("TerminalId"));
                    record.set("name",data.getString("Name"));
                    recordList.add(record);
                }
                setAttr("posIdList",recordList);
            }
        }

        List<MainBookChannel> bookChannelList=mainBookChannelService.findBookChannelList();
        setAttr("bookChannelList",bookChannelList);
        setAttr("commonUpload", Global.commonUpload);
        setAttr("userList", userService.findUnlockUserList());
        setAttr("base",base);
        render("baseForm.html");
    }

    
    /**
     * 用餐时间编辑界面
     */
    public void mealTimeForm(){
    	MainBaseMealTimeSetting model = getBean(MainBaseMealTimeSetting.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseMealTimeSettingService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("mealTimeForm.html");
    }
    
    /**
     * 菜标签编辑界面
     */
    public void foodTagForm(){
    	MainBaseFoodTag model = getBean(MainBaseFoodTag.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseFoodTagService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("foodTagForm.html");
    }
    
    /**
     * 菜编辑界面
     */
    public void foodListForm(){
    	MainBaseFood model = getBean(MainBaseFood.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseFoodService.findById(model.getId());
    	}else{
    		final int dataCount = Db.queryInt("select count(id)dataCount from main_base_food where base_id=?", model.getBaseId());
    		model.setFoodSort(dataCount+1);
    	}
    	setAttr("model",model);
    	setAttr("foodTagList",mainBaseFoodTagService.findList(model.getBaseId(), "1"));
    	setAttr("foodInfoList",foodInfoService.findList());
    	render("foodListForm.html");
    }
    
    /**
     * 餐厅编辑界面
     */
    public void restaurantForm(){
    	MainBaseRestaurant model = getBean(MainBaseRestaurant.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseRestaurantService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("restaurantForm.html");
    }
    
    /**
     * 餐厅餐桌添加界面
     */
    public void restaurantTableAdd(){
    	String restaurantId = getPara("restaurantId");
    	setAttr("restaurantId",restaurantId);
    	setAttr("dictList",dictService.findDictList("table_status"));
    	render("restaurantTableAdd.html");
    }
    
    /**
     * 餐厅餐桌编辑界面
     */
    public void restaurantTableForm(){
    	MainBaseRestaurantTable model = getBean(MainBaseRestaurantTable.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseRestaurantTableService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("restaurantTableForm.html");
    }
    
    /**
     * 套餐表单界面
     */
    public void packageForm(){
    	MainBasePackage model = getBean(MainBasePackage.class,"",true);
    	List<MainBasePackageFood> packageFoodList = new ArrayList<MainBasePackageFood>();
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBasePackageService.findById(model.getId());
    		packageFoodList = mainBasePackageFoodService.findListByPackageId(model.getId());
    	}
    	setAttr("model",model);
    	setAttr("packageFoodList",packageFoodList);
    	setAttr("foodList",mainBaseFoodService.findFoodList(model.getBaseId(), "", "1", "", "", "onlyFood"));
    	setAttr("userId",AuthUtils.getUserId());
    	setAttr("uploadDomain",Global.commonUpload);
    	render("packageForm.html");
    }
    
    /**
     * 手牌表单界面
     */
    public void handCardForm(){
    	MainBaseHandCard model = getBean(MainBaseHandCard.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseHandCardService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("handCardForm.html");
    }
    
    /**
     * 客户类型表单界面
     */
    public void customerTypeForm(){
    	MainBaseCustomerType model = getBean(MainBaseCustomerType.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseCustomerTypeService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("customerTypeForm.html");
    }
    
    /**
     * 基地团队类型表单界面
     */
    public void teamTypeForm(){
    	MainBaseTeamType model = getBean(MainBaseTeamType.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseTeamTypeService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("teamTypeForm.html");
    }
    
    /**
     * 创建餐桌二维码
     */
    public void createTableQrcode(){
		String id=getPara("id");
		if(StrKit.notBlank(id)) {
			id = id.replace("-", "@");
		}
//		MainBaseRestaurantTable model = mainBaseRestaurantTableService.findById(id);
		File logoFile = new File(PathKit.getWebRootPath() + Global.logoImg);
		String base64Img = "";
		try {
			base64Img = ZXingCode.drawLogoQRCode(logoFile,Global.tableQrcodeUrl.replace("{1}",id), "");
		} catch (Exception e) {
			e.printStackTrace();
			renderJson(Ret.fail("msg", "生成二维码失败"));
			//logger.info("二维码工具类生成异常：" +e);
			return;
		}
		renderJson(Ret.ok("msg", "生成二维码成功").set("imgName",base64Img));
	}
    
    /**
     * 打印方法
     */
    public void printTableQrcode() {
    	final String selectDatas = getPara("selectDatas");
    	if(StrKit.notBlank(selectDatas)){
    		String baseId = "";
    		StringBuilder sb = new StringBuilder();
    		List<MainBaseRestaurantTable> tableList = JSONArray.parseArray(selectDatas, MainBaseRestaurantTable.class);
    		for(MainBaseRestaurantTable model : tableList) {
    			String tableId = model.getId();
    			final String restaurantId = model.getRestaurantId();
    			if(StrKit.notBlank(tableId)) {
    				tableId = tableId.replace("-", "@");
    			}
    			if(StrKit.isBlank(baseId)) {
    				if(StrKit.notBlank(restaurantId)) {
    					MainBaseRestaurant restaurant = mainBaseRestaurantService.findById(restaurantId);
    					if(restaurant!=null) {
    						baseId = restaurant.getBaseId().replace("-", "@");
    					}
    				}
    			}
    			final String qrcodeUrl = Global.tableQrcodeUrl.replace("{1}",tableId).replace("{2}",baseId);
    			
    			sb.append(model.getTableName()).append("\t");
        		sb.append(qrcodeUrl).append("\t\r\n");
    		}
    		renderJson(Ret.ok("msg", "返回成功!").set("templateName", "tableQrcode.btw").set("printData", sb.toString()));
    	}else {
    		renderJson(Ret.fail("msg", "请勾选数据"));
    	}
    }

    /**
     * 新增/修改
     */
    public void save(){
        MainBase base = getBean(MainBase.class,"base",true);
        String bookChannelIdStr=getPara("bookChannel");
        boolean isAdd= StrKit.isBlank(base.getId());
        if(base == null){ renderJson(Ret.fail("msg", "保存失败")); return; }

        if(mainBaseService.baseIsExist(base.getId(),base.getBaseName())!=null){
            renderJson(Ret.fail("msg", "该基地名称已存在"));
            return;
        }

        boolean flag = mainBaseService.saveBase(base,AuthUtils.getUserId());
        if(flag){
            mainBaseBuildingFloorChannelService.saveBaseBuildingFloorChannel(BaseBuildingFloorChannelType.base.getKey(),base.getId(),bookChannelIdStr,AuthUtils.getUserId());
            //添加时把添加的base信息返回出去，修改不用
            if(isAdd){
                renderJson(Ret.ok("msg", "保存成功").set("data",base));
                return;
            }
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }
    
    /**
     * 基地用餐时间保存
     */
    public void saveMealTime(){
    	MainBaseMealTimeSetting mealTime = getBean(MainBaseMealTimeSetting.class,"",true);
//    	if(mainBaseService.baseIsExist(base.getId(),base.getBaseName())!=null){
//    		renderJson(Ret.fail("msg", "该基地名称已存在"));
//    		return;
//    	}
    	if(mainBaseMealTimeSettingService.save(mealTime ,AuthUtils.getUserId())){
    		renderJson(Ret.ok("msg", "保存成功"));
    	}else{
    		renderJson(Ret.fail("msg", "保存失败"));
    	}
    }
    
    /**
     * 基地菜标签保存
     */
    public void saveFoodTag(){
    	boolean falg = false;
    	MainBaseFoodTag model = getBean(MainBaseFoodTag.class, "", true);
		if(StrKit.notBlank(model.getId())){
			model.setUpdateBy(AuthUtils.getUserId());
			model.setUpdateTime(new Date());
			falg = model.update();
		}else {
			model.setId(IdGen.getUUID());
			model.setDelFlag(DelFlag.NORMAL);
			model.setCreateBy(AuthUtils.getUserId());
			model.setCreateTime(new Date());
			falg = model.save();
		}
		if(falg){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 基地菜保存
     */
    public void saveBaseFood(){
    	final String oldFoodId = getPara("oldFoodId");
    	MainBaseFood model = getBean(MainBaseFood.class, "", true);
    	
    	int dataCount = 0;
		if (StrKit.isBlank(model.getId()) || 
			(
				StrKit.notBlank(model.getId()) && 
				StrKit.isBlank(model.getIsEnabled()) && 
				StrKit.isBlank(model.getIsShelf()) && 
				!oldFoodId.equalsIgnoreCase(model.getFoodId())
			)
		) {
    		dataCount = Db.queryInt("select count(id)dataCount from main_base_food where del_flag=? and food_id=? and base_id=? and config_type=?", DelFlag.NORMAL, model.getFoodId(), model.getBaseId(), model.getConfigType());
    	}
    	if(dataCount>0) {
    		renderJson(Ret.fail("msg","操作失败,该标签该类型下的菜已存在"));
    	}else {
    		boolean flag = false;
    		if(StrKit.notBlank(model.getId())){
    			model.setUpdateBy(AuthUtils.getUserId());
    			model.setUpdateTime(new Date());
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseFood.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
    			if(flag) {
    				flag = model.update();
    			}else {
    				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    				return;
    			}
    		}else {
    			model.setId(IdGen.getUUID());
    			model.setDelFlag(DelFlag.NORMAL);
    			model.setCreateBy(AuthUtils.getUserId());
    			model.setCreateTime(new Date());
    			model.setUpdateBy(AuthUtils.getUserId());
    			model.setUpdateTime(new Date());
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseFood.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
    			if(flag) {
    				flag = model.save();
    			}else {
    				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    				return;
    			}
    		}
    		if(flag){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}
    }
    
    /**
     * 基地餐厅保存
     */
    public void saveBaseRestaurant(){
    	boolean flag = false;
    	MainBaseRestaurant model = getBean(MainBaseRestaurant.class, "", true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    		if(flag) {
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurant.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
    		}
    	}else {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
			model.setUpdateTime(new Date());
    		flag = model.save();
    		if(flag) {
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurant.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
    		}
    	}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    
    /**
     * 批量基地餐厅餐桌保存
     */
    public void batchSaveBaseRestaurantTable(){
    	final String restaurantId = getPara("restaurantId");
    	final int tableCount = getParaToInt("tableCount");
    	List<MainBaseRestaurantTable> tableList = new ArrayList<MainBaseRestaurantTable>();
		if(tableCount>0){
			for (int i = 1; i <= tableCount; i++) {
				final MainBaseRestaurantTable model = getBean(MainBaseRestaurantTable.class, "tableList["+i+"]");
				if(model!=null && StrKit.notBlank(model.getTableName())) {
					if(StrKit.isBlank(model.getId())){
						model.setRestaurantId(restaurantId);
						model.setDelFlag(DelFlag.NORMAL);
						model.setCreateBy(AuthUtils.getUserId());
						model.setCreateTime(new Date());
						model.setUpdateBy(AuthUtils.getUserId());
						model.setUpdateTime(new Date());
					}
					tableList.add(model);
				}
			}
		}
		if(mainBaseRestaurantTableService.saveRestaurantTable(tableList, AuthUtils.getUserId())){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 基地餐厅餐桌保存
     */
    public void saveBaseRestaurantTable(){
    	boolean flag = false;
    	MainBaseRestaurantTable model = getBean(MainBaseRestaurantTable.class, "", true);
		model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateTime(new Date());
		flag = model.update();
		if(flag) {
			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurantTable.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
		}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }

    
    /**
     * 套餐保存
     */
    public void packageSave(){
    	final int foodCount = getParaToInt("foodCount");
    	final int fileCount = getParaToInt("fileCount");
    	MainBasePackage model = getBean(MainBasePackage.class,"",true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setUpdateBy(AuthUtils.getUserId());
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    	}
    	List<MainBasePackageFood> packageFoodList = new ArrayList<MainBasePackageFood>();
		if(foodCount>0){
			for (int i = 1; i <= foodCount; i++) {
				final MainBasePackageFood packageFood = getBean(MainBasePackageFood.class, "packageFoodList["+i+"]", true);
				if(packageFood!=null && StrKit.notBlank(packageFood.getFoodId())) {
					packageFoodList.add(packageFood);
				}
			}
		}
		List<CfsFileUpload> fileList = new ArrayList<CfsFileUpload>();
    	if(fileCount>0){
    		for (int i = 1; i <= fileCount; i++) {
    			final CfsFileUpload file = getBean(CfsFileUpload.class, "fileList["+i+"]");
    			if(StrKit.notBlank(file.getId())) {
    				fileList.add(file);
    			}
    		}
    	}
		if(mainBasePackageService.savePackage(model, packageFoodList, fileList)){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 手牌保存
     */
    public void handCardSave(){
    	boolean flag = false;
    	final String oldCardId = getPara("oldCardId");
    	final String oldCardNo = getPara("oldCardNo");
    	MainBaseHandCard model = getBean(MainBaseHandCard.class, "", true);
    	int dataCount = 0;
		if (StrKit.isBlank(model.getId()) || 
			(
				StrKit.notBlank(model.getId()) && (!oldCardId.equalsIgnoreCase(model.getCardId())||!oldCardNo.equalsIgnoreCase(model.getCardNo()))
			)
		) {
    		dataCount = Db.queryInt("select count(id)dataCount from main_base_hand_card where is_enabled='1' and del_flag=? and card_id=? and card_no=?", DelFlag.NORMAL, model.getCardId(), model.getCardNo());
    	}
    	if(dataCount>0) {
    		renderJson(Ret.fail("msg","操作失败,该手牌已存在"));
    	}else {
    		if(StrKit.notBlank(model.getId())){
    			model.setUpdateBy(AuthUtils.getUserId());
    			model.setUpdateTime(new Date());
    			flag = model.update();
    			if(flag) {
    				flag=mainSyncRecordService.saveSyncRecord(SyncType.baseHandCard.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
    			}
    		}else {
    			model.setId(IdGen.getUUID());
    			model.setDelFlag(DelFlag.NORMAL);
    			model.setCreateBy(AuthUtils.getUserId());
    			model.setCreateTime(new Date());
    			model.setUpdateBy(AuthUtils.getUserId());
    			model.setUpdateTime(new Date());
    			flag = model.save();
    			if(flag) {
    				flag=mainSyncRecordService.saveSyncRecord(SyncType.baseHandCard.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
    			}
    		}
    		if(flag){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}
    }
    
    /**
     * 客户类型保存
     */
    public void customerTypeSave(){
    	boolean flag = false;
    	MainBaseCustomerType model = getBean(MainBaseCustomerType.class, "", true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    		if(flag) {
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseCustomerType.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
    		}
    	}else {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.save();
    		if(flag) {
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseCustomerType.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
    		}
    	}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    
    /**
     * 基地团队类型保存
     */
    public void teamTypeSave(){
    	boolean flag = false;
    	MainBaseTeamType model = getBean(MainBaseTeamType.class, "", true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    		if(flag) {
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseTeamType.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
    		}
    	}else {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.save();
    		if(flag) {
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseTeamType.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
    		}
    	}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }

    /**
     * 基地删除
     */
    public void delete(){
        String id = getPara("id");
        Integer buldingCount=mainBaseBuildingService.queryBuildingCountByBaseId(id);
        if(buldingCount>0){
            renderJson(Ret.fail("msg", "该基地下还用楼栋存在，不能作废"));
            return;
        }
        MainBase base = mainBaseService.get(id);
        if(base != null){
            boolean flag = mainBaseService.delBase(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }
    
    /**
     * 基地用餐时间删除
     */
    public void deleteMealTime(){
        String id = getPara("id");
        boolean flag = mainBaseMealTimeSettingService.delete(id, AuthUtils.getUserId());
        if (flag) {
            renderJson(Ret.ok("msg", "作废成功"));
        } else {
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }
    
    
    public void delFoodTag() {
    	MainBaseFoodTag model = getBean(MainBaseFoodTag.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
		if(mainBaseFoodTagService.update(model)){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    
    public void delBaseFood() {
    	MainBaseFood model = getBean(MainBaseFood.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.baseFood.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    	if(flag){
			if(mainBaseFoodService.update(model)){
				renderJson(Ret.ok("msg","操作成功"));
			}else{
				renderJson(Ret.fail("msg", "操作失败！"));
			}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    	}
    }
    
    
    public void delBaseRestaurant() {
    	MainBaseRestaurant model = getBean(MainBaseRestaurant.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurant.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    	if(flag){
    		if(mainBaseRestaurantService.update(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    	}
    }
    
    
    public void delBaseRestaurantTable() {
    	MainBaseRestaurantTable model = getBean(MainBaseRestaurantTable.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	final boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurantTable.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    	if(flag){
    		if(mainBaseRestaurantTableService.update(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    	}
    }
    

    public void delPackage(){
    	MainBasePackage model = getBean(MainBasePackage.class,"",true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.basePackage.getKey(), SyncDataType.DELETE, idStr,AuthUtils.getUserId());
        if(flag) {
        	if(mainBasePackageService.update(model)){
        		List<String> idListF = Arrays.asList(new String[]{});
        		List<MainBasePackageFood> packageFoodList = mainBasePackageFoodService.findListByPackageId(model.getId());
        		if(packageFoodList!=null && packageFoodList.size()>0) {
        			for(MainBasePackageFood packageFood : packageFoodList) {
        				idListF.add(packageFood.getId());
        				packageFood.delete();
        			}
        		}
        		final String idStrF = "["+idListF.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
        		final boolean flagF = mainSyncRecordService.saveSyncRecord(SyncType.basePackageFood.getKey(), SyncDataType.DELETE, idStrF,AuthUtils.getUserId());
        		if(flagF) {
        			renderJson(Ret.ok("msg", "操作成功"));
        		}else {
        			renderJson(Ret.fail("msg", "操作失败,同步套餐关联数据失败"));
        		}
        	}else{
        		renderJson(Ret.fail("msg", "操作失败"));
        	}
        }else {
        	renderJson(Ret.fail("msg", "操作失败,同步套餐数据失败"));
        }
    }
    
    
    public void delPackageFood(){
    	MainBasePackageFood model = getBean(MainBasePackageFood.class,"",true);
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	final boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.basePackageFood.getKey(), SyncDataType.DELETE, idStr,AuthUtils.getUserId());
    	if(flag) {
    		if(model.delete()){
    			renderJson(Ret.ok("msg", "操作成功"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败"));
    		}
    	}else {
    		renderJson(Ret.fail("msg", "操作失败,同步数据失败"));
    	}
    }
    
    
    public void delHandCard() {
    	MainBaseHandCard model = getBean(MainBaseHandCard.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.baseHandCard.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    	if(flag){
    		if(mainBaseHandCardService.update(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    	}
    }
    
    
    public void delCustomerType() {
    	MainBaseCustomerType model = getBean(MainBaseCustomerType.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.baseCustomerType.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    	if(flag){
    		if(mainBaseCustomerTypeService.update(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    	}
    }
    
    public void delTeamType() {
    	MainBaseTeamType model = getBean(MainBaseTeamType.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
    	List<String> idList = Arrays.asList(new String[]{model.getId()});
    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    	boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.baseTeamType.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    	if(flag){
    		if(mainBaseTeamTypeService.update(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    	}
    }


    /**
     * 批量删除
     */
    public void batchDel(){
        String baseData =  getPara("baseData");
        List<MainBase> list = JSONArray.parseArray(baseData,MainBase.class);
        boolean flag = mainBaseService.batchDelBase(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量删除成功"));
        }else{
            renderJson(Ret.fail("msg", "批量删除失败"));
        }
    }

    /**
     * 财务经理选择页面
     */
    public void auditUserTableIndex(){
        render("auditUserTable.html");
    }

	public static void main(String[] args) throws Exception{
		final String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxd91b34cbe3ab4445&redirect_uri=http://10.10.4.203:8890/login&response_type=code&scope=snsapi_base&state={1}#wechat_redirect";
		System.out.println(URLEncoder.encode(url, "utf-8"));
	}

    public void resetSleepaceReportTime(){
        String baseId = getPara("baseId");

        List<String> equiList=Db.query("select c.info_id from main_bed_equi_model a " +
                "inner join main_base_building b on a.building_id=b.id " +
                "inner join equi_model c on a.equi_model_id=c.id " +
                " where a.del_flag='0' and b.del_flag='0' and b.base_id=? ",baseId);
        if(equiList.size()==0){
            renderJson(Ret.fail("msg","该基地无设备"));
            return;
        }
        MainBase base=mainBaseService.findById(baseId);
        String reportUploadTimeResultStr="";
        for (String infoId : equiList) {
            String reportUploadTimeData="{\"token\":{\"appId\":\""+base.getSleepaceAppId()+"\",\"secureKey\":\""+base.getSleepaceSecureKey()
                    +"\"},\"data\":{\"deviceId\":\""+infoId+"\",\"reportUploadTime\":"+base.getSleepaceReportTime()+"}}";
            reportUploadTimeResultStr = HttpClientsUtils.httpPostRaw(Global.setReportUploadTimeUrl, reportUploadTimeData, null, null);
        }
        if(!reportUploadTimeResultStr.startsWith("{") || !reportUploadTimeResultStr.endsWith("}")){
            renderJson(Ret.fail("msg","第三方接口异常：")+reportUploadTimeResultStr);
            return;
        }
        JSONObject jsonObject=JSON.parseObject(reportUploadTimeResultStr);
        if("0".equals(jsonObject.getString("status"))){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"+JSON.toJSONString(jsonObject)));
        }

    }
}
