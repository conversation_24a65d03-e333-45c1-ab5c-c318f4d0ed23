package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBusinessEntityService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBusinessEntity;
import com.cszn.integrated.service.entity.main.MainRoomType;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.json.JFinalJson;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value="/main/businessEntity", viewPath="/modules_page/main/businessEntity")
public class MainBusinessEntityController extends BaseController {

    @Inject
    private MainBusinessEntityService mainBusinessEntityService;


    /**
     * 跳转房间类型界面batchDel
     */
    public void index(){
        render("index.html");
    }


    /**
     * 房间类型列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainBusinessEntity businessEntity= getBean(MainBusinessEntity.class,"",true);
        Page<MainBusinessEntity> page = mainBusinessEntityService.businessEntityPage(getParaToInt("page"),getParaToInt("limit"),businessEntity);
        DataTable dataTable = new DataTable<MainBusinessEntity>(page);
        renderJson(dataTable);
    }


    /**
     * 跳转新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        if(StrKit.notBlank(id)){
            MainBusinessEntity businessEntity = mainBusinessEntityService.findById(id);
            setAttr("businessEntity",businessEntity);
        }

        render("form.html");
    }


    /**
     * 保存
     */
    public void save(){
        MainBusinessEntity businessEntity = getBean(MainBusinessEntity.class,"",true);

        boolean flag = mainBusinessEntityService.saveBusinessEntity(businessEntity, AuthUtils.getUserId());
        if(flag ){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 房间类型删除
     */
    public void delete(){
        String id = getPara("id");

        if(StrKit.isBlank(id)){
            renderJson(Ret.fail("msg","id不能为空"));
            return;
        }

        boolean flag = mainBusinessEntityService.delBusinessEntity(id, AuthUtils.getUserId());
        if (flag) {
            renderJson(Ret.ok("msg", "操作成功"));
        } else {
            renderJson(Ret.fail("msg", "操作失败"));
        }
    }

}
