package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.api.main.MainBaseBedDynamicRecordService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.enums.BedDynamicType;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBaseBedDynamicRecord;
import com.cszn.integrated.service.entity.main.MainSyncRecord;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping(value = "/main/syncRecord",viewPath = "/modules_page/main/syncRecord")
public class MainSyncRecordController extends BaseController {

    @Inject
    private MainSyncRecordService mainSyncRecordService;
    @Inject
    private MainAppJoinService appJoinService;
    @Inject
    private MainBaseBedDynamicRecordService mainBaseBedDynamicRecordService;
    @Inject
    private MainBaseService mainBaseService;




    /**
     * 同步记录管理首页
     */
    public void index(){
        List<MainAppJoin> appJoinList = appJoinService.findAppJoinList();
        setAttr("appJoinList",appJoinList);
        //获取syncType
        Map<String,String> map=new HashMap<>();
        try {
            for(SyncType syncType:SyncType.values()){
                map.put(syncType.getKey(),syncType.getValue());
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        setAttr("syncTypes",map);
        render("syncRecordIndex.html");
    }


    /**
     * 获取全部同步记录接口
     */
    @Clear(LogInterceptor.class)
    public void findAllSyncRecord(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        String status=getPara("status");
        Page<MainSyncRecord> recordPage=mainSyncRecordService.findAllSyncRecordPage(pageNumber,pageSize,status);
        renderJson(new DataTable<MainSyncRecord>(recordPage));
    }

    /**
     * 通过appNo获取同步记录分页
     */
    @Clear(LogInterceptor.class)
    public void findSyncRecordByAppNo(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainSyncRecord syncRecord=getBean(MainSyncRecord.class,"",true);
        Page<Record> recordPage=mainSyncRecordService.findSyncRecordPageByAppNo(pageNumber,pageSize,syncRecord);
        renderJson(new DataTable<Record>(recordPage));
    }

    /**
     * 发送同步数据
     */
    public void sendSyncRecord(){
        String id=getPara("id");
        Map<String,Object> map=mainSyncRecordService.sendSyncRecord(id);
        if((Boolean) map.get("suc")){
            renderJson(Ret.ok("msg",map.get("msg")));
        }else{
            renderJson(Ret.fail("msg",map.get("msg")));
        }
    }

    /**
     * 删除同步记录
     */
    public void delSyncRecord(){
        String id=getPara("id");
        boolean flag=mainSyncRecordService.delSyncRecord(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 批量同步首页
     */
    public void batchSyncIndex(){
        //获取枚举中的同步记录类型
        List<Map<String,String>> syncTypeList=new ArrayList<>();
        for (com.cszn.integrated.service.entity.enums.SyncType syncType : com.cszn.integrated.service.entity.enums.SyncType.values()){
            Map<String,String> map=new HashMap<>();
            map.put("key",syncType.getKey());
            map.put("value",syncType.getValue());
            map.put("table",syncType.getTable());
            syncTypeList.add(map);
        }
        setAttr("syncTypeList",syncTypeList);
        render("batchSyncIndex.html");
    }

    /**
     * 开始同步
     */
    public void startSync(){
        String syncType=getPara("syncType");
        String operationType=getPara("operationType");
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");

        boolean flag=mainSyncRecordService.startSync(syncType,operationType,startDate,endDate, AuthUtils.getUserId(),1,100);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    /**
     * 作废所有同步记录
     */
    public void delAllSyncRecord(){
        String syncType=getPara("syncType");
        boolean flag=mainSyncRecordService.delAllSyncRecord(syncType,AuthUtils.getUserId(),1,100);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    /**
     * 床位动态首页
     */
    public void bedDynamicRecordIndex(){
        List<MainAppJoin> appJoinList = appJoinService.findAppJoinList();
        //List<MainBase> baseList = mainBaseService.findBaseList();
        Map<String,String> map=new HashMap<>();
        for(BedDynamicType type:BedDynamicType.values()){
            map.put(type.getKey(),type.getValue());
        }
        //setAttr("baseList",baseList);
        setAttr("appJoinList",appJoinList);
        setAttr("typeMap",map);
        render("bedDynamicRecordIndex.html");
    }

    /**
     * 床位动态表单
     */
    public void bedDynamicRecordForm(){
        String id=getPara("id");
        MainBaseBedDynamicRecord record=mainBaseBedDynamicRecordService.findById(id);
        setAttr("record",record);
        render("bedDynamicRecordForm.html");
    }

    /**
     * 床位动态分页
     */
    public void findBedDynamicRecordPageByAppNo(){
        MainBaseBedDynamicRecord record=getBean(MainBaseBedDynamicRecord.class,"",true);
        String roomAndBed=getPara("roomAndBed");
        Page<MainBaseBedDynamicRecord> page=mainBaseBedDynamicRecordService.findBedDynamicRecordPageByAppNo(getParaToInt("page"),getParaToInt("limit"),record,roomAndBed);
        renderJson(new DataTable<MainBaseBedDynamicRecord>(page));
    }

    /**
     * 同步床位动态
     */
    public void sendBedDynamicRecord(){
        String id=getPara("id");
        Map<String,Object> map=mainBaseBedDynamicRecordService.sendBedDynamicRecord(id);
        if((boolean)map.get("flag")){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg",map.get("msg")));
        }
    }

    /**
     *
     */
    public void bedDynamicRecordTableIndex(){
        List<MainBase> baseList=mainBaseService.findBaseList();

        setAttr("baseList",baseList);
        render("bedDynamicRecordTableIndex.html");

    }

    /**
     * 获取床位动态记录
     */
    public void bedDynamicRecordDataPage(){
        String baseId=getPara("baseId");
        String buildingId=getPara("buildingId");
        String floorId=getPara("floorId");
        String roomAndBed=getPara("roomAndBed");
        String dateRange=getPara("dateRange");
        if(StrKit.isBlank(dateRange)){
            renderJson("{\"code\":10001,\"msg\":\"dateRange时间范围不能为空\"}");
            return;
        }
        String[] dateArray=dateRange.split(" - ");
        String startDate=null;
        String endDate=null;
        if(dateArray!=null && dateArray.length==2){
            startDate=dateArray[0];
            endDate=dateArray[1];
        }
        Page<Record> bedPage=mainBaseBedDynamicRecordService.bedDynamicRecordTableData(getParaToInt("page"),getParaToInt("limit"),baseId,buildingId,floorId,roomAndBed,startDate,endDate);
        renderJson(new DataTable<Record>(bedPage));
    }

    public void bedDynamicRecordDetailIndex(){
        String bedId=getPara("bedId");

        setAttr("bedId",bedId);
        render("bedDynamicRecordDetailIndex.html");
    }

    /**
     * 通过床位号获取床位动态记录
     */
    public void findDynamicRecordPageByBedId(){
        String bedId=getPara("bedId");
        Page<Record> page=mainBaseBedDynamicRecordService.findDynamicRecordPageByBedId(getParaToInt("page"),getParaToInt("limit"),bedId);
        renderJson(new DataTable<Record>(page));
    }

    /**
     * 预计入住表首页
     */
    public void expectedCheckinTableIndex(){

        render("expectedCheckinTableIndex.html");
    }

    /**
     * 预计入住数据
     */
    public void baseExpectedCheckinList(){
        String date=getPara("date");
        if(StrKit.isBlank(date)){
            date= DateUtils.getDate();
        }
        List<Record> recordList=mainBaseBedDynamicRecordService.baseExpectedCheckinList(date);
        renderJson(new DataTable<Record>(recordList));
    }

    /**
     * 预计入住详情
     */
    public void baseExpectedCheckinDetailIndex(){
        String baseId=getPara("baseId");
        String date=getPara("date");

        setAttr("baseId",baseId);
        setAttr("date",date);
        render("expectedCheckinTableDetailIndex.html");
    }

    /**
     * 预计入住数据
     */
    public void baseExpectedCheckinDetailList(){
        String baseId=getPara("baseId");
        String date=getPara("date");
        Page<Record> page=mainBaseBedDynamicRecordService.baseExpectedCheckinDetailPageList(getParaToInt("page"),getParaToInt("limit"),baseId,date);
        renderJson(new DataTable<Record>(page));
    }
}
