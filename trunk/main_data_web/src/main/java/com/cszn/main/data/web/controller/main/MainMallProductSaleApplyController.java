package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaLeaseRecordApplyService;
import com.cszn.integrated.service.api.fina.FinaTaskService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainMallProductSaleApplyItemService;
import com.cszn.integrated.service.api.main.MainMallProductSaleApplyService;
import com.cszn.integrated.service.api.main.MainMallProductTypeService;
import com.cszn.integrated.service.api.pers.PersApprovalService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.wms.WmsStockModelService;
import com.cszn.integrated.service.entity.domain.CreateTaskParam;
import com.cszn.integrated.service.entity.enums.EarnestMoneyPaymentType;
import com.cszn.integrated.service.entity.enums.TaskType;
import com.cszn.integrated.service.entity.main.MainMallProductSaleApply;
import com.cszn.integrated.service.entity.main.MainMallProductSaleApplyItem;
import com.cszn.integrated.service.entity.main.MainMallProductType;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.wms.WmsStockModels;
import com.cszn.integrated.service.provider.task.MainProductSaleApplyTask;
import com.cszn.integrated.service.provider.task.Task;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RequestMapping(value = "/mall/productSaleApply",viewPath = "/modules_page/main/productSaleApply")
public class MainMallProductSaleApplyController extends BaseController {

    @Inject
    UserService userService;
    @Inject
    PersOrgService persOrgService;
    @Inject
    MainBaseService mainBaseService;
    @Inject
    FinaLeaseRecordApplyService finaLeaseRecordApplyService;
    @Inject
    PersPositionService persPositionService;
    @Inject
    PersApprovalService persApprovalService;
    @Inject
    FinaTaskService finaTaskService;
    @Inject(MainProductSaleApplyTask.class)
    Task mainProductSaleApplyTask;

    @Inject
    MainMallProductSaleApplyService mainMallProductSaleApplyService;
    @Inject
    MainMallProductSaleApplyItemService mainMallProductSaleApplyItemService;
    @Inject
    MainMallProductTypeService mainMallProductTypeService;
    @Inject
    WmsStockModelService wmsStockModelService;


    public void index(){

        render("index.html");
    }


    public void form(){
        String taskId=getPara("taskId");
        String userId=getPara("userId");
        String id=getPara("id");
        User user =null;
        setAttr("isEdit",true);
        if(StrKit.notBlank(userId)){
            user = userService.findById(userId);
            setAttr("type","H5");
        }else{
            user = AuthUtils.getLoginUser();
        }
        setAttr("user",user);
        String empId= Db.queryStr("select emp_id from pers_emp_user where user_id=? ",user.getId());
        List<Record> deptRecordList=Db.find("select relationship_id as deptId from pers_org_employee_rel  where emp_id=? and relationship_type='dept' group by relationship_id order by is_main desc ",empId);
        for (Record record : deptRecordList) {
            record.set("deptName",persOrgService.getOrgParentNames(record.getStr("deptId")));

            List<Record> positionRecordList=Db.find("select b.id,b.position_name as positionName from pers_org_employee_rel a INNER JOIN pers_position b on a.relationship_id=b.id " +
                    "where relationship_type='position' and a.emp_id=? and b.org_id=? ",empId,record.getStr("deptId"));
            record.set("positionListStr",JSON.toJSONString(positionRecordList));
            record.set("positionList",positionRecordList);
        }
        set("deptRecordList",deptRecordList);


        MainMallProductSaleApply model=null;
        if(StrKit.notBlank(taskId)){
            model=mainMallProductSaleApplyService.findByTaskId(taskId);
        }else if(StrKit.notBlank(id)){
            model=mainMallProductSaleApplyService.findById(id);
        }
        if(model!=null){
            List<Record> recordList=mainMallProductSaleApplyItemService.findListByApplyId(model.getId());
            setAttr("itemList",recordList);
            setAttr("model",model);

            if(StrKit.notBlank(model.getTaskId())){
                Map<String,Object> taskDetail=persApprovalService.getTaskDetail(model.getTaskId(),user.getId());
                setAttr("stepts",taskDetail.get("stepts"));
                setAttr("taskState",taskDetail.get("taskState"));
                setAttr("isSaveHandle",taskDetail.get("isSaveHandle"));
                setAttr("currentStepName",taskDetail.get("currentStepName"));
                setAttr("currentStepAlias",taskDetail.get("currentStepAlias"));
                setAttr("taskId",model.getTaskId());

                JSONArray currentSteps=(JSONArray)taskDetail.get("currentSteps");
                //批准
                boolean allowApprove=false;
                //拒绝
                boolean allowReject=false;
                //中止
                boolean allowAbort=false;
                //提交
                boolean allowSubmit=false;
                if(currentSteps!=null){
                    for(int i=0;i<currentSteps.size();i++){
                        JSONObject currentStep=currentSteps.getJSONObject(i);
                        boolean flag=false;
                        for(int j=0;j<currentStep.getJSONArray("UserIds").size();j++){
                            if(!flag && user.getId().equalsIgnoreCase(currentStep.getJSONArray("UserIds").getString(j))){
                                flag=true;
                            }
                        }
                        if(flag){
                            allowApprove=currentStep.getBoolean("AllowApprove");
                            allowReject=currentStep.getBoolean("AllowReject");
                            allowSubmit=currentStep.getBoolean("AllowSubmit");
                        }
                        allowAbort=currentStep.getBoolean("AllowAbort");
                    }
                }
                setAttr("allowAbort",allowAbort);
                setAttr("allowSubmit",allowSubmit);
                setAttr("allowReject",allowReject);
                setAttr("allowApprove",allowApprove);

                if(!(boolean)taskDetail.get("isSaveHandle")){
                    setAttr("submitUserName",userService.findById(model.getApplyId()).getName());
                    setAttr("submitDeptName",persOrgService.getOrgParentNames(model.getDeptId()));
                    setAttr("submitPositionName", persPositionService.findById(model.getPositionId()).getPositionName());
                }
            }else{

                if(!AuthUtils.getUserId().equalsIgnoreCase(model.getApplyId())){
                    setAttr("isEdit",false);
                    setAttr("submitUserName",userService.findById(model.getApplyId()).getName());
                    setAttr("submitDeptName",persOrgService.getOrgParentNames(model.getDeptId()));
                    setAttr("submitPositionName", persPositionService.findById(model.getPositionId()).getPositionName());
                }

            }
        }
        setAttr("earnestMoneyPaymentType", EarnestMoneyPaymentType.values());
        render("applyForm.html");
        render("form.html");
    }

    public void stockModelIndex(){
        setAttr("trIndex",getPara("trIndex"));
        render("stockModelIndex.html");
    }

    public void checkProductSaleApplyItem(){
        String modelId = getPara("modelId");
        if(StrKit.isBlank(modelId)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        if(Db.findFirst("select * from main_mall_product_type_rel where is_sell='1' and product_id=?",modelId)!=null){
            renderJson(Ret.fail("msg","该商品已上架"));
            return;
        }
        if(Db.findFirst("select * from main_mall_product_sale_apply_item a " +
                "inner join main_mall_product_sale_apply b on b.id=a.sale_apply_id " +
                " where a.del_flag='0' and b.`status` in ('1','2') and a.model_id=?",modelId)!=null){
            renderJson(Ret.fail("msg","该商品已存在未结束的上架流程中"));
            return;
        }
        renderJson(Ret.ok("msg","success"));
    }

    public void savProductSaleApply(){
        //FinaLeaseRecordApply leaseRecordApply=getBean(FinaLeaseRecordApply.class,"",true);
        MainMallProductSaleApply productSaleApply=getBean(MainMallProductSaleApply.class,"",true);


        Integer roomCount = getParaToInt("roomCount");
        String saveType=getPara("saveType");
        String userId=getPara("userId");

        List<MainMallProductSaleApplyItem> productSaleApplyItemList=new ArrayList<>();
        for (Integer i = 1; i <= roomCount; i++) {
            MainMallProductSaleApplyItem productSaleApplyItem=getBean(MainMallProductSaleApplyItem.class,"model"+i,true);
            if(StrKit.notBlank(productSaleApplyItem.getModelId()) && StrKit.notBlank(productSaleApplyItem.getTypeId())){
                productSaleApplyItemList.add(productSaleApplyItem);
            }
        }

        boolean isAdd=false;
        if(StrKit.isBlank(productSaleApply.getId())){
            productSaleApply.setId(IdGen.getUUID());
            productSaleApply.setDelFlag("0");
            productSaleApply.setStatus("1");
            productSaleApply.setCreateBy(userId);
            productSaleApply.setCreateDate(new Date());
            productSaleApply.setUpdateBy(userId);
            productSaleApply.setUpdateDate(new Date());
            //判断是否存在
            for (MainMallProductSaleApplyItem item : productSaleApplyItemList) {
                if(Db.findFirst("select * from main_mall_product_type_rel where is_sell='1' and product_id=? and type_id=?",item.getModelId(),item.getTypeId())!=null){

                    WmsStockModels models=wmsStockModelService.findById(item.getModelId());
                    MainMallProductType type= mainMallProductTypeService.findById(item.getTypeId());
                    renderJson(Ret.fail("msg","["+models.getName()+"]已在["+type.getTypeName()+"]类型中上架"));
                    return;
                }
                if(Db.findFirst("select * from main_mall_product_sale_apply_item a " +
                        "inner join main_mall_product_sale_apply b on b.id=a.sale_apply_id " +
                        " where a.del_flag='0' and b.`status` in ('1','2') and a.model_id=? and a.type_id=? ",item.getModelId(),item.getTypeId())!=null){
                    WmsStockModels models=wmsStockModelService.findById(item.getModelId());
                    MainMallProductType type= mainMallProductTypeService.findById(item.getTypeId());
                    renderJson(Ret.fail("msg","["+models.getName()+"]已在["+type.getTypeName()+"]类型中上架流程中"));
                    return;
                }
            }

            isAdd=true;
        }else{
            productSaleApply.setUpdateBy(userId);
            productSaleApply.setUpdateDate(new Date());
        }
        productSaleApply.setApplyTime(new Date());

        List<MainMallProductSaleApplyItem> addList=new ArrayList<>();
        List<MainMallProductSaleApplyItem> updateList=new ArrayList<>();
        for (MainMallProductSaleApplyItem item : productSaleApplyItemList) {

            if(StrKit.isBlank(item.getId())){
                item.setId(IdGen.getUUID());
                item.setDelFlag("0");
                item.setSaleApplyId(productSaleApply.getId());
                item.setCreateBy(userId);
                item.setCreateDate(new Date());
                item.setUpdateBy(userId);
                item.setUpdateDate(new Date());
                addList.add(item);
            }else{
                item.setUpdateBy(userId);
                item.setUpdateDate(new Date());
                updateList.add(item);
            }

        }

        boolean flag=false;
        if(isAdd){
            flag=productSaleApply.save();
        }else{
            flag=productSaleApply.update();
        }
        if(flag){
            if(addList.size()>0){
                Db.batchSave(addList,addList.size());
            }
            if(updateList.size()>0){
                Db.batchUpdate(updateList,updateList.size());
            }

            if("2".equals(saveType)){
                if(StrKit.isBlank(productSaleApply.getTaskId())){
                    CreateTaskParam createTaskParam=new CreateTaskParam();
                    createTaskParam.setProcessNo(TaskType.mallProductSale.getTaskNo());
                    createTaskParam.setUserId(userId);
                    //保存并提交
                    Map<String,Object> resultMap=mainProductSaleApplyTask.createTask(createTaskParam,productSaleApply);
                    if((boolean)resultMap.get("flag")){
                        productSaleApply.setTaskId((String)resultMap.get("taskId"));
                        productSaleApply.setTaskNo(TaskType.mallProductSale.getTaskNo());
                        productSaleApply.setStatus("2");

                        //leaseRecordApply.setStatus("3");
                        productSaleApply.update();
                        //finaLeaseRecordApplyService.taskCompleted(leaseRecordApply);
                    }else{
                        renderJson(Ret.ok("msg",resultMap.get("msg")));
                        return;
                    }
                }

            }
        }

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void pageList(){
        MainMallProductSaleApply productSaleApply=getBean(MainMallProductSaleApply.class,"",true);


        Page<Record> recordPage=mainMallProductSaleApplyService.pagelist(getParaToInt("page"),getParaToInt("limit"),productSaleApply);

        if(recordPage.getList()!=null){
            for (Record record : recordPage.getList()) {
                if (StrKit.notBlank(record.getStr("taskId"))) {
                    Map<String,Object> taskDetail=persApprovalService.getTaskDetail(record.getStr("taskId"),AuthUtils.getUserId());
                    record.set("currentStepName",taskDetail.get("currentStepName"));
                    record.set("isCurrentUser",taskDetail.get("isCurrentUser"));
                }
                record.set("isEdit",AuthUtils.getUserId().equalsIgnoreCase(record.getStr("applyId")));
            }
        }

        renderJson(new DataTable<Record>(recordPage));
    }

    public void delItem(){
        String id = getPara("id");
        String userId = getPara("userId");
        if(StrKit.isBlank(id) || StrKit.isBlank(userId)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        MainMallProductSaleApplyItem item=mainMallProductSaleApplyItemService.findById(id);
        item.setDelFlag("1");
        item.setUpdateBy(userId);
        item.setUpdateDate(new Date());
        if(item.update()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void doTask(){
        String taskId=getPara("taskId");
        String taskNo=getPara("taskNo");
        String userId=getPara("userId");
        String msg=getPara("msg");
        String currentStepAlias=getPara("currentStepAlias");
        String stepState=getPara("stepState");
        /*if(StrKit.isBlank(msg)){
            renderJson(Ret.fail("msg","意见不能为空"));
            return;
        }*/
        Map<String,Object> map=persApprovalService.doTask(taskId,taskNo,stepState,currentStepAlias,msg,userId);
        if((Boolean) map.get("flag")){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg",(String)map.get("msg")));
        }
    }
}
