package com.cszn.main.data.web.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCheckinDaySummaryService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCheckinRecordService;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinDaySummary;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinRecord;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinWxRecord;
import com.cszn.main.data.web.support.cron.EmployeeDelayCheckinCron;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import com.xiaoleilu.hutool.lang.Base64;
import io.jboot.web.controller.annotation.RequestMapping;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RequestMapping("/testApi")
public class TestApi extends BaseController {

    @Inject
    PersOrgEmployeeCheckinDaySummaryService persOrgEmployeeCheckinDaySummaryService;
    @Inject
    EmployeeDelayCheckinCron employeeDelayCheckinCron;
    @Inject
    PersOrgEmployeeCheckinRecordService persOrgEmployeeCheckinRecordService;


    public static boolean validateSign(String data, String sign, String
            publicKey){
        try {
            Signature signature = Signature.getInstance("SHA256WithRSA");
            PublicKey localPublicKey = getPublicKeyFromX509("RSA", publicKey);
            signature.initVerify(localPublicKey);
            signature.update(data.getBytes("UTF-8"));
            byte[] bytesSign = Base64.decode(sign);
            return signature.verify(bytesSign);
        }catch (Exception e){
            e.printStackTrace();
            return false;
        }
    }

    public static PublicKey getPublicKeyFromX509(String algorithm, String
            publicKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        return keyFactory.generatePublic(new
                X509EncodedKeySpec(Base64.decode(publicKey)));
    }

    public void testValidateSign(){
        JSONObject jsonObject= JSON.parseObject(HttpKit.readData(getRequest()));
        String data=jsonObject.getString("data");
        String sign=jsonObject.getString("sign");

        if(StrKit.isBlank(data)){
            renderCodeFailed("参数缺失");
            return;
        }
        System.out.println(data);
        System.out.println(sign);
        boolean bl=validateSign(data,
                sign,"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5+MNqcjgw4bsSWhJfw2M" +
                        "+gQB7P+pEiYOfvRmA6kt7Wisp0J3JbOtsLXGnErn5ZY2D8KkSAHtMYbeddphFZQJ" +
                        "zUbiaDi75GUAG9XS3MfoKAhvNkK15VcCd8hFgNYCZdwEjZrvx6Zu1B7c29S64LQP" +
                        "HceS0nyXF8DwMIVRcIWKy02cexgX0UmUPE0A2sJFoV19ogAHaBIhx5FkTy+eeBJE" +
                        "bU03Do97q5G9IN1O3TssvbYBAzugz+yUPww2LadaKexhJGg+5+ufoDd0+V3oFL0/" +
                        "ebkJvD0uiBzdE3/ci/tANpInHAUDIHoWZCKxhn60f3/3KiR8xuj2vASgEqphxT5O" +
                        "fwIDAQAB");

        renderCodeSuccess("success",bl);
    }

    public void get3(){




        List<String> notEmpid= Db.query("select emp_id from  pers_org_employee_checkin_day_summary  where del_flag='0' \n" +
                "and \n" +
                "summary_date BETWEEN '2024-03-01 00:00:00' and '2024-03-31 23:59:59'\n" +
                "group by emp_id\n" +
                "HAVING COUNT(id)>=31");

        String str="";
        for (String s : notEmpid) {
            str+="?,";
        }
        str=str.substring(0,str.length()-1);

        String sql="select * from (select * from pers_org_employee where del_flag='0'  \n" +
                "and archive_status='incumbency'  \n" +
                "UNION\n" +
                "select * from pers_org_employee where del_flag='0'  \n" +
                "and archive_status='quit' and quit_time>='2024-03-01 00:00:00') t where id not in ("+str+") ";
        List<Record> recordList=Db.find(sql,notEmpid.toArray());

        List<Date> dateList= DateUtils.getBetweenDates(DateUtils.parseDate("2024-03-01"),DateUtils.parseDate("2024-03-31"));
        //String wxCheckinSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_time BETWEEN '2024-03-31 00:00:00' and '2024-04-01 18:00:00' ORDER BY checkin_time ";
        //String fillCardCheckinSql="select * from pers_org_employee_checkin_fill_card where emp_id=? and del_flag='0' and fill_card_time BETWEEN '2024-03-31 00:00:00' and '2024-04-01 18:00:00' ORDER BY fill_card_time ";
        for (Record record : recordList) {
            String empId=record.getStr("id");
            for (Date date : dateList) {
                try {


                    PersOrgEmployeeCheckinDaySummary daySummary=persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId,date);
                    if(daySummary==null){
                        Map<String,Object> todayMap=employeeDelayCheckinCron.getEmpOverTime(empId,date);
                        Date todayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
                        Date todayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");

                        List<PersOrgEmployeeCheckinRecord> checkinRecordList = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordListByTimeRange(empId, todayMinCheckinTime, todayMaxCheckinTime);
                        if(checkinRecordList.size()>0){
                            for (PersOrgEmployeeCheckinRecord persOrgEmployeeCheckinRecord : checkinRecordList) {
                                persOrgEmployeeCheckinRecord.setDelFlag("1");
                                persOrgEmployeeCheckinRecord.setUpdateDate(new Date());
                                persOrgEmployeeCheckinRecord.update();
                            }
                        }
                        String wxCheckinSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_time BETWEEN ? and ? ORDER BY checkin_time ";

                        List<Record> wxCheckinRecordList = Db.find(wxCheckinSql,empId,todayMinCheckinTime,todayMaxCheckinTime);
                        for (Record wxCheckinRecord : wxCheckinRecordList) {
                            PersOrgEmployeeCheckinWxRecord checkinWxRecord = new PersOrgEmployeeCheckinWxRecord();
                            checkinWxRecord.setId(IdGen.getUUID());
                            checkinWxRecord.setEmpId(empId);
                            checkinWxRecord.setCheckinTime(wxCheckinRecord.getDate("checkin_time"));
                            employeeDelayCheckinCron.empCheckin(checkinWxRecord,checkinWxRecord.getCheckinTime());
                        }




                        employeeDelayCheckinCron.employeeCheckinDaySummary(todayMap);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

            }




        }
        renderJson("ok");
    }

    public void get4(){


        String sql="select * from pers_org_employee where del_flag='0'  \n" +
                "and archive_status='incumbency'  \n" +
                "UNION\n" +
                "select * from pers_org_employee where del_flag='0'  \n" +
                "and archive_status='quit' and quit_time>='2024-03-01 00:00:00'  ";
        List<Record> recordList=Db.find(sql);

        List<Date> dateList=DateUtils.getBetweenDates(DateUtils.parseDate("2024-03-01"),DateUtils.parseDate("2024-03-31"));
        //String wxCheckinSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_time BETWEEN '2024-03-31 00:00:00' and '2024-04-01 18:00:00' ORDER BY checkin_time ";
        //String fillCardCheckinSql="select * from pers_org_employee_checkin_fill_card where emp_id=? and del_flag='0' and fill_card_time BETWEEN '2024-03-31 00:00:00' and '2024-04-01 18:00:00' ORDER BY fill_card_time ";
        for (Record record : recordList) {
            String empId=record.getStr("id");
            for (Date date : dateList) {
                try {


                    PersOrgEmployeeCheckinDaySummary daySummary=persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId,date);
                    if(daySummary==null){
                        Map<String,Object> todayMap=employeeDelayCheckinCron.getEmpOverTime(empId,date);
                        Date todayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
                        Date todayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");

                        List<PersOrgEmployeeCheckinRecord> checkinRecordList = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordListByTimeRange(empId, todayMinCheckinTime, todayMaxCheckinTime);
                        if(checkinRecordList.size()>0){
                            for (PersOrgEmployeeCheckinRecord persOrgEmployeeCheckinRecord : checkinRecordList) {
                                persOrgEmployeeCheckinRecord.setDelFlag("1");
                                persOrgEmployeeCheckinRecord.setUpdateDate(new Date());
                                persOrgEmployeeCheckinRecord.update();
                            }
                        }
                        String wxCheckinSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_time BETWEEN ? and ? ORDER BY checkin_time ";

                        List<Record> wxCheckinRecordList = Db.find(wxCheckinSql,empId,todayMinCheckinTime,todayMaxCheckinTime);
                        for (Record wxCheckinRecord : wxCheckinRecordList) {
                            PersOrgEmployeeCheckinWxRecord checkinWxRecord = new PersOrgEmployeeCheckinWxRecord();
                            checkinWxRecord.setId(IdGen.getUUID());
                            checkinWxRecord.setEmpId(empId);
                            checkinWxRecord.setCheckinTime(wxCheckinRecord.getDate("checkin_time"));
                            employeeDelayCheckinCron.empCheckin(checkinWxRecord,checkinWxRecord.getCheckinTime());
                        }
                        employeeDelayCheckinCron.employeeCheckinDaySummary(todayMap);
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

            }




        }
        renderJson("ok");
    }


    public void get5(){
        String sql="select * from pers_org_employee_checkin_fill_card where emp_id='2BA746BC-172A-4129-807E-6292AB4674DB' " +
                "and del_flag='0' and fill_card_time BETWEEN '2024-03-01 00:00:00' and '2024-03-31 23:59:59' and `status`='2' ORDER BY fill_card_time ";
        List<Record> recordList=Db.find(sql);
        for (Record record : recordList) {

            String empId=record.getStr("emp_id");
            Date fill_card_time=record.getDate("fill_card_time");
            //按小时
            Map<String, Object> todayMap = employeeDelayCheckinCron.getEmpOverTime(empId,fill_card_time);
            //获取今天
            Date todayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
            Date todayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");

            Date minCheckinTime=null;
            Date maxCheckinTime=null;
            Date summaryDate=null;
            if(todayMinCheckinTime.getTime()<=fill_card_time.getTime() && todayMaxCheckinTime.getTime()>fill_card_time.getTime()){
                minCheckinTime=todayMinCheckinTime;
                maxCheckinTime=todayMaxCheckinTime;
                summaryDate=fill_card_time;
            }else{
                if(todayMinCheckinTime.getTime()>fill_card_time.getTime()){
                    //属于昨天的打卡记录
                    Calendar calendar=Calendar.getInstance();
                    calendar.setTime(fill_card_time);
                    calendar.add(Calendar.DATE,-1);
                    Map<String, Object> yesterdayMap = employeeDelayCheckinCron.getEmpOverTime(empId,calendar.getTime());
                    Date yesterdayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
                    Date yesterdayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");
                    minCheckinTime=yesterdayMinCheckinTime;
                    maxCheckinTime=yesterdayMaxCheckinTime;
                    summaryDate=calendar.getTime();
                }else if(todayMaxCheckinTime.getTime()<=fill_card_time.getTime()){
                    //属于明天的打卡记录
                    Calendar calendar=Calendar.getInstance();
                    calendar.setTime(fill_card_time);
                    calendar.add(Calendar.DATE,1);
                    Map<String, Object> nextdayMap = employeeDelayCheckinCron.getEmpOverTime(empId,calendar.getTime());
                    Date nextdayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
                    Date nextdayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");
                    minCheckinTime=nextdayMinCheckinTime;
                    maxCheckinTime=nextdayMaxCheckinTime;
                    summaryDate=calendar.getTime();
                }
            }
            if(minCheckinTime!=null && maxCheckinTime!=null){
                List<PersOrgEmployeeCheckinRecord> checkinRecordList = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordListByTimeRange(empId, minCheckinTime, maxCheckinTime);
                if(checkinRecordList.size()>0){
                    for (PersOrgEmployeeCheckinRecord persOrgEmployeeCheckinRecord : checkinRecordList) {
                        persOrgEmployeeCheckinRecord.setDelFlag("1");
                        persOrgEmployeeCheckinRecord.setUpdateDate(new Date());
                        persOrgEmployeeCheckinRecord.update();
                    }
                }
                String wxCheckinSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_time BETWEEN ? and ? ORDER BY checkin_time ";

                List<Record> wxCheckinRecordList = Db.find(wxCheckinSql,empId,minCheckinTime,maxCheckinTime);
                for (Record wxCheckinRecord : wxCheckinRecordList) {
                    PersOrgEmployeeCheckinWxRecord checkinWxRecord = new PersOrgEmployeeCheckinWxRecord();
                    checkinWxRecord.setId(IdGen.getUUID());
                    checkinWxRecord.setEmpId(empId);
                    checkinWxRecord.setCheckinTime(wxCheckinRecord.getDate("checkin_time"));
                    employeeDelayCheckinCron.empCheckin(checkinWxRecord,checkinWxRecord.getCheckinTime());
                }
                PersOrgEmployeeCheckinWxRecord checkinWxRecord2 = new PersOrgEmployeeCheckinWxRecord();
                checkinWxRecord2.setId(IdGen.getUUID());
                checkinWxRecord2.setEmpId(empId);
                checkinWxRecord2.setCheckinTime(fill_card_time);

                employeeDelayCheckinCron.empCheckin(checkinWxRecord2,checkinWxRecord2.getCheckinTime());

                PersOrgEmployeeCheckinDaySummary empDaySummary = persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId, summaryDate);
                if(empDaySummary!=null){
                    empDaySummary.setDelFlag("1");
                    empDaySummary.setUpdateDate(new Date());
                    empDaySummary.update();
                }


                Map<String,Object> map=employeeDelayCheckinCron.getEmpOverTime(empId,summaryDate);
                employeeDelayCheckinCron.employeeCheckinDaySummary(map);
            }

        }
    }

    public void get6(){
        String sql="select * from pers_org_employee_checkin_fill_card where   " +
                "  del_flag='0' and fill_card_time BETWEEN '2024-03-01 00:00:00' and '2024-03-31 23:59:59' and `status`='2' ORDER BY fill_card_time ";
        List<Record> recordList=Db.find(sql);
        for (Record record : recordList) {

            String empId=record.getStr("emp_id");
            Date fill_card_time=record.getDate("fill_card_time");
            //按小时
            Map<String, Object> todayMap = employeeDelayCheckinCron.getEmpOverTime(empId,fill_card_time);
            //获取今天
            Date todayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
            Date todayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");

            Date minCheckinTime=null;
            Date maxCheckinTime=null;
            Date summaryDate=null;
            if(todayMinCheckinTime.getTime()<=fill_card_time.getTime() && todayMaxCheckinTime.getTime()>fill_card_time.getTime()){
                minCheckinTime=todayMinCheckinTime;
                maxCheckinTime=todayMaxCheckinTime;
                summaryDate=fill_card_time;
            }else{
                if(todayMinCheckinTime.getTime()>fill_card_time.getTime()){
                    //属于昨天的打卡记录
                    Calendar calendar=Calendar.getInstance();
                    calendar.setTime(fill_card_time);
                    calendar.add(Calendar.DATE,-1);
                    Map<String, Object> yesterdayMap = employeeDelayCheckinCron.getEmpOverTime(empId,calendar.getTime());
                    Date yesterdayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
                    Date yesterdayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");
                    minCheckinTime=yesterdayMinCheckinTime;
                    maxCheckinTime=yesterdayMaxCheckinTime;
                    summaryDate=calendar.getTime();
                }else if(todayMaxCheckinTime.getTime()<=fill_card_time.getTime()){
                    //属于明天的打卡记录
                    Calendar calendar=Calendar.getInstance();
                    calendar.setTime(fill_card_time);
                    calendar.add(Calendar.DATE,1);
                    Map<String, Object> nextdayMap = employeeDelayCheckinCron.getEmpOverTime(empId,calendar.getTime());
                    Date nextdayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
                    Date nextdayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");
                    minCheckinTime=nextdayMinCheckinTime;
                    maxCheckinTime=nextdayMaxCheckinTime;
                    summaryDate=calendar.getTime();
                }
            }
            if(minCheckinTime!=null && maxCheckinTime!=null){
                List<PersOrgEmployeeCheckinRecord> checkinRecordList = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordListByTimeRange(empId, minCheckinTime, maxCheckinTime);
                if(checkinRecordList.size()>0){
                    for (PersOrgEmployeeCheckinRecord persOrgEmployeeCheckinRecord : checkinRecordList) {
                        persOrgEmployeeCheckinRecord.setDelFlag("1");
                        persOrgEmployeeCheckinRecord.setUpdateDate(new Date());
                        persOrgEmployeeCheckinRecord.update();
                    }
                }
                String wxCheckinSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_time BETWEEN ? and ? ORDER BY checkin_time ";

                List<Record> wxCheckinRecordList = Db.find(wxCheckinSql,empId,minCheckinTime,maxCheckinTime);
                for (Record wxCheckinRecord : wxCheckinRecordList) {
                    PersOrgEmployeeCheckinWxRecord checkinWxRecord = new PersOrgEmployeeCheckinWxRecord();
                    checkinWxRecord.setId(IdGen.getUUID());
                    checkinWxRecord.setEmpId(empId);
                    checkinWxRecord.setCheckinTime(wxCheckinRecord.getDate("checkin_time"));
                    employeeDelayCheckinCron.empCheckin(checkinWxRecord,checkinWxRecord.getCheckinTime());
                }
                PersOrgEmployeeCheckinWxRecord checkinWxRecord2 = new PersOrgEmployeeCheckinWxRecord();
                checkinWxRecord2.setId(IdGen.getUUID());
                checkinWxRecord2.setEmpId(empId);
                checkinWxRecord2.setCheckinTime(fill_card_time);

                employeeDelayCheckinCron.empCheckin(checkinWxRecord2,checkinWxRecord2.getCheckinTime());

                PersOrgEmployeeCheckinDaySummary empDaySummary = persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId, summaryDate);
                if(empDaySummary!=null){
                    empDaySummary.setDelFlag("1");
                    empDaySummary.setUpdateDate(new Date());
                    empDaySummary.update();
                }


                Map<String,Object> map=employeeDelayCheckinCron.getEmpOverTime(empId,summaryDate);
                employeeDelayCheckinCron.employeeCheckinDaySummary(map);
            }

        }
    }

}
