package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainMallProductTypeService;
import com.cszn.integrated.service.api.wms.WmsStockModelDetailService;
import com.cszn.integrated.service.entity.main.MainMallProductType;
import com.cszn.integrated.service.entity.main.MainMallProductTypeRel;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.wms.WmsStockModelDetail;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequestMapping(value = "/mall/productType",viewPath = "/modules_page/main/productType")
public class MainMallProductTypeController extends BaseController {

    @Inject
    private MainMallProductTypeService mainMallProductTypeService;
    @Inject
    private WmsStockModelDetailService wmsStockModelDetailService;

    public void index(){
        render("index.html");
    }

    public void treeTableTypeList(){

        List<Record> recordList =mainMallProductTypeService.getTreeTableTypeList();
        renderJson(new DataTable<Record>(recordList));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainMallProductType type=mainMallProductTypeService.findById(id);
            setAttr("model",type);
        }
        setAttr("fileUploadUrl",Global.fileUploadUrl);
        render("form.html");
    }

    public void typeTree(){

        List<ZTree> zTreeList = mainMallProductTypeService.typeTreeData();
        renderJson(zTreeList);
    }

    public void typeSave(){
        MainMallProductType type=getBean(MainMallProductType.class,"",true);


        if(StrKit.notBlank(type.getId())){
            if(StrKit.notBlank(type.getParentId())){

                MainMallProductType parentType=mainMallProductTypeService.findById(type.getParentId());

                if(type.getId().equals(type.getParentId())){
                    renderJson(Ret.fail("msg","不能设置当前类型为上级类型"));
                    return;
                }
                if(parentType.getParentIds().indexOf(type.getId())!=-1){
                    renderJson(Ret.fail("msg","不能设置当前类型的子级为上级类型"));
                    return;
                }
            }
            if(Db.findFirst("select * from main_mall_product_type where type_name=? and id<>? ",type.getTypeName(),type.getId())!=null){
                renderJson(Ret.fail("msg","该名称已存在"));
                return;
            }
        }else{
            if(Db.findFirst("select * from main_mall_product_type where type_name=? ",type.getTypeName())!=null){
                renderJson(Ret.fail("msg","该名称已存在"));
                return;
            }
        }

        boolean flag=mainMallProductTypeService.saveProductType(type, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delProductType(){
        String id = getPara("id");
        MainMallProductType parentType=mainMallProductTypeService.findById(id);
        //判断所有子节点是否存在未作废的
        List<Record> productTypeList=Db.find("select id from main_mall_product_type where del_flag='0' and parent_id=?",parentType.getId());
        if(productTypeList.size()>0){
            renderJson(Ret.fail("msg","改分类下面存在子分类，请先作废子分类"));
            return;
        }
        parentType.setDelFlag("1");
        parentType.setUpdateDate(new Date());
        parentType.setUpdateBy(AuthUtils.getUserId());
        if(parentType.update()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void stockModelIndex(){
        String productTypeId=getPara("typeId");
        setAttr("productTypeId",productTypeId);
        render("stockModelIndex.html");
    }


    public void typeAddProduct(){
        MainMallProductTypeRel rel=getBean(MainMallProductTypeRel.class,"",true);

        if(Db.findFirst("select * from main_mall_product_type_rel where type_id=? and product_id=? ",rel.getTypeId(),rel.getProductId())!=null){
            renderCodeFailed("该商品已存在改分类");
            return;
        }
        rel.setId(IdGen.getUUID());
        if(rel.save()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void findProductPageList(){
        String typeId=getPara("typeId");
        String name=getPara("name");
        String isSell = getPara("isSell");

        List<String> idList=new ArrayList<>();
        idList.add(typeId);
        mainMallProductTypeService.findChildren(idList,typeId);

        String sql=" from main_mall_product_type_rel a " +
                "inner join wms_stock_models b on a.product_id=b.id " +
                "where 1=1 ";

        List<Object> params=new ArrayList<>();

        if(idList.size()>0){
            String str="";
            for (String s : idList) {
                str+="?,";
                params.add(s);
            }
            str=str.substring(0,str.length()-1);

            sql+=" and a.type_id in("+str+") ";

        }

        if(StrKit.notBlank(name)){
            sql+=" and b.`name` like concat('%',?,'%') ";
            params.add(name.trim());
        }

        if(StrKit.notBlank(isSell)){
            sql+=" and a.is_sell=? ";
            params.add(isSell);
        }
        sql+=" group by a.product_id order by a.`sort` ";

        Page<Record> recordPage=Db.paginate(getParaToInt("page"),getParaToInt("limit"),"select b.*,a.is_sell,a.sort,a.id as relId ",sql,params.toArray());

        renderJson(new DataTable<Record>(recordPage));
    }

    public void productDetailIndex(){
        String id=getPara("id");
        setAttr("modelId",id);

        WmsStockModelDetail modelDetail=wmsStockModelDetailService.findByModelId(id);
        setAttr("model",modelDetail);
        setAttr("fileUploadUrl", Global.fileUploadUrl);
        render("detail.html");
    }

    public void saveModelDetail(){
        WmsStockModelDetail modelDetail=getBean(WmsStockModelDetail.class,"",true);
        boolean flag=wmsStockModelDetailService.saveModelDetail(modelDetail,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveProductTypeRel(){
        String id=getPara("id");
        String isSell=getPara("isSell");

        int num=Db.update("update main_mall_product_type_rel set is_sell=? where product_id=? ",isSell,id);
        if(num>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delProductTypeRel(){
        String id=getPara("id");
        String typeId = getPara("typeId");

        int num=Db.update("delete from main_mall_product_type_rel where product_id=? and type_id=? ",id,typeId);
        if(num>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void sortBatchSave(){
        String sortDatas = getPara("sortDatas");
        if(StrKit.isBlank(sortDatas)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        JSONArray array= JSON.parseArray(sortDatas);
        List<MainMallProductTypeRel> relList=new ArrayList<>();
        for (int i = 0; i < array.size(); i++) {
            MainMallProductTypeRel typeRel=new MainMallProductTypeRel();
            typeRel.setId(array.getJSONObject(i).getString("id"));
            typeRel.setSort(array.getJSONObject(i).getIntValue("sort"));
            relList.add(typeRel);
        }
        Db.batchUpdate(relList,relList.size());
        renderJson(Ret.ok("msg","操作成功"));
    }

}
