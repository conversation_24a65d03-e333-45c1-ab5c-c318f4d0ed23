package com.cszn.main.data.web.controller.wms;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.wms.WmsStockLabelService;
import com.cszn.integrated.service.api.wms.WmsStockTypeService;
import com.cszn.integrated.service.api.wms.WmsStockTypeUserService;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.wms.WmsStockLabel;
import com.cszn.integrated.service.entity.wms.WmsStockTypeUser;
import com.cszn.integrated.service.entity.wms.WmsStockTypes;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value = "/wms/stockType",viewPath = "/modules_page/wms/stockType")
public class StockTypeController extends BaseController {

    @Inject
    WmsStockTypeService wmsStockTypeService;
    @Inject
    WmsStockTypeUserService wmsStockTypeUserService;
    @Inject
    UserService userService;
    @Inject
    WmsStockLabelService wmsStockLabelService;

    public void index(){
        render("stockTypeIndex.html");
    }

    public void stockTypeTableTree(){
    	String isEnabled = getPara("isEnabled");
        List<ZTree> zTrees=wmsStockTypeService.findStockTypeTableZtree(isEnabled);
        renderJson(new DataTable<ZTree>(zTrees));
    }

    public void typeForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            WmsStockTypes type=wmsStockTypeService.findById(id);
            if(type!=null){
                WmsStockTypes parentType=wmsStockTypeService.findById(id);
                setAttr("parentType",parentType);
            }
            setAttr("type",type);
            List<String> lableIdList= Db.query("select label_id from wms_stock_type_label_rel where type_id=? ",id);
            String lableIds="";
            for(String str:lableIdList){
                lableIds+=str+",";
            }
            setAttr("lableIds",lableIds);
        }
        List<WmsStockLabel> stockLabelList=wmsStockLabelService.getStockLabelList();
        setAttr("stockLabelList",stockLabelList);
        render("stockTypeForm.html");
    }

    public void stockTypeTree(){
        List<ZTree> zTrees=wmsStockTypeService.findStockTypeTree("1");
        renderJson(zTrees);
    }

    public void saveStockType(){
        WmsStockTypes type=getBean(WmsStockTypes.class,"",true);
        String lableIds=getPara("lableIds");
        if(StrKit.notBlank(type.getId())){
            type.setUpdateBy(AuthUtils.getUserId());
        }else{
            type.setUpdateBy(AuthUtils.getUserId());
            type.setCreateBy(AuthUtils.getUserId());
        }
        boolean flag=wmsStockTypeService.saveStockType(type);
        if(flag){
            wmsStockLabelService.saveTypeLabelRel(type.getId(),lableIds,AuthUtils.getUserId());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void stockTypeUserIndex(){
        String typeId=getPara("typeId");

        List<WmsStockTypeUser> typeUserList=wmsStockTypeUserService.findAll();

        setAttr("typeUserList",typeUserList);

        List<User> userList=userService.findUserList();
        setAttr("typeId",typeId);
        setAttr("userList",userList);
        render("typeUserIndex.html");
    }
}
