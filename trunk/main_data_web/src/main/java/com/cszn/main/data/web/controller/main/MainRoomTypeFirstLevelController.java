package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainRoomTypeFirstLevelService;
import com.cszn.integrated.service.entity.main.MainRoomTypeFirstLevel;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value = "/main/firstLevel",viewPath = "/modules_page/main/firstLevel/")
public class MainRoomTypeFirstLevelController extends BaseController {

    @Inject
    MainRoomTypeFirstLevelService mainRoomTypeFirstLevelService;

    public void index(){
        render("index.html");
    }

    public void pageList(){
        MainRoomTypeFirstLevel firstLevel=getBean(MainRoomTypeFirstLevel.class,"",true);

        Page<MainRoomTypeFirstLevel> page=mainRoomTypeFirstLevelService.pageList(getParaToInt("page"),getParaToInt("limit"),firstLevel);

        renderJson(new DataTable<MainRoomTypeFirstLevel>(page));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainRoomTypeFirstLevel firstLevel=mainRoomTypeFirstLevelService.findById(id);
            setAttr("model",firstLevel);
        }
        render("form.html");
    }

    public void save(){
        MainRoomTypeFirstLevel firstLevel=getBean(MainRoomTypeFirstLevel.class,"",true);

        boolean flag=mainRoomTypeFirstLevelService.saveFirstLevel(firstLevel, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
