package com.cszn.main.data.web.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.cfs.CfsFileUploadService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.food.FoodInfoService;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.api.member.MmsFaceLiveUploadService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.wms.WmsStockModelService;
import com.cszn.integrated.service.api.wms.WmsStockTypeService;
import com.cszn.integrated.service.api.wms.WmsSupplierService;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.enums.ExamineProblemType;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.enums.TaskType;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.integrated.service.entity.wms.WmsStockModels;
import com.cszn.integrated.service.entity.wms.WmsSuppliers;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.cszn.main.data.web.validator.CorsInterceptor;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.Jboot;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

@RequestMapping(value = "/api")
@JCors
@Before(CorsInterceptor.class)
public class MainApiController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private MainBaseBedService mainBaseBedService;
    @Inject
    private MainAppJoinService mainAppJoinService;
    @Inject
    private MainBaseBedDynamicRecordService mainBaseBedDynamicRecordService;
    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private MmsFaceLiveUploadService mmsFaceLiveUploadService;
    @Inject
    private MainVideoRecordService mainVideoRecordService;
    @Inject
    private MainVideoTypeService mainVideoTypeService;
    @Inject
    private MainBaseBuildingService mainBaseBuildingService;
    @Inject
    private MainBaseFloorService mainBaseFloorService;
    @Inject
    private MainBaseRoomService mainBaseRoomService;
    @Inject
    private WmsStockTypeService wmsStockTypeService;
    @Inject
    private MainRoomChannelService mainRoomChannelService;
    @Inject
    private MainBedChannelService mainBedChannelService;
    @Inject
    private FoodInfoService foodInfoService;
    @Inject
    private MainBaseFoodTagService mainBaseFoodTagService;
    @Inject
    private MainBaseFoodService mainBaseFoodService;
    @Inject
    private MainBaseRestaurantService mainBaseRestaurantService;
    @Inject
    private MainBaseRestaurantTableService mainBaseRestaurantTableService;
    @Inject
    private MainBasePackageService mainBasePackageService;
    @Inject
    MainBasePackageFoodService mainBasePackageFoodService;
    @Inject
    WmsSupplierService wmsSupplierService;
    @Inject
    private MainSyncRecordService mainSyncRecordService;
    @Inject
    private CfsFileUploadService cfsFileUploadService;
    @Inject
    private MainBaseBuildingFloorChannelService mainBaseBuildingFloorChannelService;
    @Inject
	private MainBankService mainBankService;
    @Inject
    private MainBranchOfficeService mainBranchOfficeService;
    @Inject
    private MainTouristRouteService mainTouristRouteService;
    @Inject
    WmsStockModelService wmsStockModelService;
    @Inject
    private MainSmsTypeService mainSmsTypeService;
    @Inject
    private MainExamineSchemeService mainExamineSchemeService;
    @Inject
    private MainExamineProblemService mainExamineProblemService;
    @Inject
    private MainExamineRecordService mainExamineRecordService;
    @Inject
    private MainExamineRecordResultService mainExamineRecordResultService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private MainBaseRoomTypeService mainBaseRoomTypeService;
    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private MainExamineSchemeWeightedMeanConfigService mainExamineSchemeWeightedMeanConfigService;
    @Inject
    MainUserGroupService mainUserGroupService;
    @Inject
    MainMallProductSaleApplyService mainMallProductSaleApplyService;
    @Inject
    DictService dictService;

	/**
	 * 基地分页表格数据
	 */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
	public void basePage() {
    	MainBase model = getBean(MainBase.class, "", true);
    	model.setIsEnable("0");
		Page<MainBase> modelPage = mainBaseService.findListPage(getParaToInt("page", 1), getParaToInt("limit", 10), model);
		renderJson(new DataTable<MainBase>(modelPage));
	}
    
    /**
     * 获取所有基地列表允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBaseList(){
    	MainBase model = getBean(MainBase.class, "", true);
    	model.setIsEnable("0");
        List<MainBase> baseList=mainBaseService.findAllBase(model);
        renderCodeSuccess("success", baseList);
    }
    
    /**
     * 获取单条基地数据
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void baseOne() {
    	MainBase model = getBean(MainBase.class, "", true);
    	JSONObject jsonObj = new JSONObject();
    	if(StrKit.notBlank(model.getId())) {
    		model = mainBaseService.get(model.getId());
    		jsonObj = (JSONObject) JSON.toJSON(model);
    		final Integer roomNumbers = Db.queryInt("select count(br.id)dataCount from main_base_room br "
				+ "inner join main_base_building bu on bu.id=br.building_id and bu.del_flag='0' and bu.is_enable='0' "
				+ "where br.del_flag='0' and br.is_enable='0' and bu.base_id=?", model.getId());
    		final Integer bedNumbers = Db.queryInt("select count(bb.id)dataCount from main_base_bed bb "
				+ "inner join main_base_room br on br.id=bb.room_id and br.del_flag='0' and br.is_enable='0' "
				+ "inner join main_base_building bu on bu.id=br.building_id and bu.del_flag='0' and bu.is_enable='0' "
				+ "where bb.del_flag='0' and bb.is_enable='0' and bu.base_id=?", model.getId());
    		if(jsonObj!=null && !jsonObj.isEmpty()) {
    			jsonObj.put("roomNumbers", roomNumbers);
    			jsonObj.put("bedNumbers", bedNumbers);
    		}
    	}else {
    		renderCodeFailed("获取失败,主键id不能为空!");
    		return;
    	}
    	renderCodeSuccess("success", jsonObj);
    }
    
    /**
     * 保存基地数据
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void baseSave() {
    	final String userId = getPara("userId");
    	MainBase model = getBean(MainBase.class, "", true);
    	
    	if(model == null){ 
    		renderCodeFailed("操作失败,参数为空!");
    		return; 
    	}

        if(mainBaseService.baseIsExist(model.getId(), model.getBaseName())!=null){
        	renderCodeFailed("操作失败,该基地名称已存在!");
            return;
        }
    	if(mainBaseService.saveBase(model, userId)) {
    		renderCodeSuccess("操作成功!");
    	}else {
    		renderCodeFailed("操作失败!");
    	}
    }
    
    /**
     * 修改床位状态
     */
    public void updateBedStatus(){
        String paramStr= HttpKit.readData(getRequest());
        Map<String,Object> result=new HashMap<>();
        if(!paramStr.startsWith("{") || !paramStr.endsWith("}")){
            result.put("code","10001");
            result.put("msg","参数格式出错,应为json数组格式");
            renderJson(result);
            return;
        }
        JSONObject jsonObject=JSON.parseObject(paramStr);
        if(!jsonObject.containsKey("appNo")){
            result.put("code","10001");
            result.put("msg","请传入应用号");
            renderJson(result);
            return;
        }
        List<String> idList=mainBaseBedService.batchUpdateBedStatus(jsonObject);
        result.put("code","0");
        result.put("msg","success");
        result.put("data",idList);
        renderJson(result);
    }


    /**
     * 床位动态接口
     */
    @Clear(LogInterceptor.class)
    public void bedDynamicRecordSave(){
        String dataStr=HttpKit.readData(getRequest());
        Map<String,Object> resultMap=new HashMap<>();
        if(!dataStr.startsWith("{") || !dataStr.endsWith("}")){
            resultMap.put("code","10001");
            resultMap.put("msg","参数格式不正确，应为json格式");
            renderJson(resultMap);
            return;
        }
        JSONObject jsonObject= JSON.parseObject(dataStr);

        long a=System.currentTimeMillis();
        Map<String,Object> map=mainBaseBedDynamicRecordService.saveDynamicRecord(jsonObject);
        System.out.println("耗时间："+(System.currentTimeMillis()-a));
        if((Boolean) map.get("flag")){
            //人脸
            //boolean flag = mmsFaceLiveUploadService.faceLiveUploadSave(jsonObject);
            //logger.info("主数据上传人脸入退住数据保存成功:[{}]",flag);
            resultMap.put("code","0");
            resultMap.put("msg","success");
            resultMap.put("data",map.get("data"));
            renderJson(resultMap);
        }else{
            resultMap.put("code","10001");
            resultMap.put("msg",map.get("msg"));
            renderJson(resultMap);
        }
    }

    /**
     * 机构判断床位是否可入住
     */
    public void longStayIsBooking(){
        String bedId=getPara("bedId");
        String checkinDate=getPara("checkinDate");

        Map<String,Object> resultMap=new HashMap<>();
        Map<String,Object> map=mainBaseBedDynamicRecordService.longStayIsBooking(bedId,checkinDate);
        if((Boolean)map.get("flag")){
            resultMap.put("code","0");
            resultMap.put("msg","success");
            renderJson(resultMap);
        }else{
            resultMap.put("code","10001");
            resultMap.put("msg",map.get("msg"));
            renderJson(resultMap);
        }
    }
    
    /**
     * 获取所有应用，允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getApp(){
    	List<MainAppJoin> appList =  mainAppJoinService.findAll();
    	List<Record> recordList=new ArrayList<>();
    	Record record=null;
    	for(MainAppJoin app:appList){
    		record=new Record();
    		record.set("AppId",app.getId());
    		record.set("Name",app.getAppName());
    		recordList.add(record);
    	}
    	Map<String,Object> resultMap=new HashMap<>();
    	resultMap.put("Message","");
    	resultMap.put("Result",recordList);
    	renderJson(resultMap);
    }

    /**
     * 获取所有基地，允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBase(){
    	MainBase model = getBean(MainBase.class, "", true);
        List<MainBase> baseList=mainBaseService.findEnableBaseList(model);
        List<Record> recordList=new ArrayList<>();
        Record record=null;
        for(MainBase base:baseList){
            record=new Record();
            record.set("BaseId",base.getId());
            record.set("Name",base.getBaseName());
            recordList.add(record);
        }
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("Message","");
        resultMap.put("Result",recordList);
        renderJson(resultMap);
    }

    /**
     * 获取所有基地，允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBases(){
    	MainBase model = getBean(MainBase.class, "", true);
        List<MainBase> baseList=mainBaseService.findEnableBaseList(model);
        List<Record> recordList=new ArrayList<>();
        Record record=null;
        for(MainBase base:baseList){
            record=new Record();
            record.set("BaseId",base.getId());
            record.set("Name",base.getBaseName());
            record.set("Tel",base.getTel());
            record.set("Address",base.getAddress());
            if(StrKit.notBlank(base.getSleepaceWebsocketUrl())){
                record.set("SleepaceWebsocketUrl",base.getSleepaceWebsocketUrl());
            }else{
                record.set("SleepaceWebsocketUrl","");
            }

            recordList.add(record);
        }
        renderCodeSuccess("success",recordList);
    }
    
    /**
     * 获取所有供应商，允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getSuppliers(){
    	List<WmsSuppliers> suppliersList=wmsSupplierService.findList();
    	List<Record> recordList=new ArrayList<>();
    	Record record=null;
    	for(WmsSuppliers model : suppliersList){
    		record=new Record();
    		record.set("Id", model.getId());
    		record.set("Name", model.getName());
    		recordList.add(record);
    	}
    	renderCodeSuccess("success",recordList);
    }

    /**
     * 获取所有基地，允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBuildingByBaseId(){
        String baseId=getPara("baseId");
        if(StrKit.isBlank(baseId)){
            renderCodeFailed("基地id不能为空");
            return;
        }
        List<MainBaseBuilding> buildingList=mainBaseBuildingService.getBuildingListByBaseIds(new String[]{baseId});

        for (MainBaseBuilding mainBaseBuilding : buildingList) {
            List<Record> recordList=Db.find("select * from main_base_room  where del_flag='0'\n" +
                    "and is_enable='0' and building_id=?",mainBaseBuilding.getId());

            List<String> roomIds=new ArrayList<>();
            String str="";
            if(recordList.size()>0){
                for (Record record : recordList) {
                    str+="?,";
                    roomIds.add(record.getStr("id"));
                }
                str=str.substring(0,str.length()-1);

                Integer beds=Db.queryInt("select count(id) from main_base_bed  where del_flag='0' and is_enable='0' and room_id in ("+str+") ",roomIds.toArray());

                mainBaseBuilding.setTotalRooms(recordList.size());
                mainBaseBuilding.setTotalBeds(beds);
            }
        }

        renderCodeSuccess("success",buildingList);
    }

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getFloorByBuildingId(){
        String buildingId=getPara("buildingId");
        if(StrKit.isBlank(buildingId)){
            renderCodeFailed("楼栋id不能为空");
            return;
        }
        List<MainBaseFloor> floorList=mainBaseFloorService.getFloorListByBuildingIds(new String[]{buildingId});
        renderCodeSuccess("success",floorList);
    }

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBedsByFloorId() throws Exception{
        String floorId=getPara("floorId");
        String roomId=getPara("roomId");
        String userId=getPara("userId");
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        if(pageNumber==null || pageSize==null){
            renderCodeFailed("分页参数不能为空");
            return;
        }
        if(StrKit.isBlank(userId)){
            userId="7B9D2239-5813-4BBE-B7C4-3794BF6C2CDB";
        }
        /*if(StrKit.isBlank(floorId)){
            renderCodeFailed("楼栋id不能为空");
            return;
        }*/
        String baseId=Db.queryStr("select b.base_id from main_base_floor a inner join main_base_building b on a.building_id=b.id where a.id=? ",floorId);

        String sojournResultStr=HttpClientsUtils.get(Global.getSojournCheckinMemberList+"?"
                +"configNo=CSCOM0235&userId="+userId+"&paramStr="+ URLEncoder.encode(JSON.toJSONString(JSONArray.parseArray("[{\"ParamName\":\"BaseId\",\"Value\":\""+baseId+"\"}," +
                        "{\"ParamName\":\"FloorNum\",\"Value\":\""+floorId+"\"}]")),"UTF-8"));

        if(!sojournResultStr.startsWith("{") || !sojournResultStr.endsWith("}")){
            renderCodeFailed("获取旅居入住数据异常："+sojournResultStr);
            return;
        }

        Map<String,Record> longLiveBedCheckinMemberMap=new HashMap<>();
        if("27a558b2-c205-4937-b6f4-fed1fa68ac55".equalsIgnoreCase(baseId)){
            String longLiveResultStr=HttpClientsUtils.get(Global.getLongLiveCheckinMemberList);
            if(!longLiveResultStr.startsWith("{") || !longLiveResultStr.endsWith("}")){
                renderCodeFailed("获取机构入住数据异常："+longLiveResultStr);
                return;
            }
            JSONObject longLiveResultObj=JSON.parseObject(longLiveResultStr);
            if(!"0".equals(longLiveResultObj.getString("code"))){
                renderCodeFailed("获取机构入住数据失败："+JSON.toJSONString(longLiveResultObj));
                return;
            }
            if(longLiveResultObj.getJSONArray("data")!=null && longLiveResultObj.getJSONArray("data").size()>0){
                for (int i = 0; i < longLiveResultObj.getJSONArray("data").size(); i++) {
                    JSONObject item = longLiveResultObj.getJSONArray("data").getJSONObject(i);
                    Record record=new Record();
                    record.set("checkinNo",item.getString("checkin_no"));
                    record.set("memberName",item.getString("name"));
                    String gender="";
                    if("female".equals(item.getString("sex"))){
                        gender="女";
                    }else if("male".equals(item.getString("sex"))){
                        gender="男";
                    }else if("other".equals(item.getString("sex"))){
                        gender="未知";
                    }
                    record.set("gender",gender);
                    record.set("idcard",item.getString("id_card"));
                    record.set("bedName",item.getString("bedName"));
                    record.set("id",item.getString("bedId"));
                    longLiveBedCheckinMemberMap.put(item.getString("bedId").toLowerCase(),record);
                }
            }
        }

        JSONObject sojournResultObj=JSON.parseObject(sojournResultStr);

        if(!"1".equals(sojournResultObj.getString("Type"))){
            renderCodeFailed("获取旅居入住数据失败："+JSON.toJSONString(sojournResultObj));
            return;
        }
        List<Record> recordList=new ArrayList<>();
        if(sojournResultObj.getJSONArray("Data")!=null && sojournResultObj.getJSONArray("Data").size()>0){
            for (int i = 0; i < sojournResultObj.getJSONArray("Data").size(); i++) {
                JSONObject item=sojournResultObj.getJSONArray("Data").getJSONObject(i);

                Record record=new Record();

                if(StrKit.notBlank(item.getString("CheckinNo"))){
                    record.set("checkinNo",item.getString("CheckinNo"));
                    record.set("bedName",item.getString("BedNo"));
                    record.set("memberName",item.getString("Name"));
                    record.set("idcard",item.getString("IdCardNo"));
                    String gender="";
                    if("Male".equals(item.getString("Gender"))){
                        gender="男";
                    }else if("Female".equals(item.getString("Gender"))){
                        gender="女";
                    }else if("Unknown".equals(item.getString("Gender"))){
                        gender="未知";
                    }
                    record.set("gender",gender);
                    record.set("id",item.getString("BedId"));

                }else{
                    if(longLiveBedCheckinMemberMap.get(item.getString("BedId").toLowerCase())!=null){
                        record.setColumns(longLiveBedCheckinMemberMap.get(item.getString("BedId").toLowerCase()));
                    }else{
                        record.set("checkinNo",item.getString("CheckinNo"));
                        record.set("bedName",item.getString("BedNo"));
                        record.set("memberName",item.getString("Name"));
                        record.set("idcard",item.getString("IdCardNo"));
                        String gender="";
                        if("Male".equals(item.getString("Gender"))){
                            gender="男";
                        }else if("Female".equals(item.getString("Gender"))){
                            gender="女";
                        }else if("Unknown".equals(item.getString("Gender"))){
                            gender="未知";
                        }
                        record.set("gender",gender);
                        record.set("id",item.getString("BedId"));
                    }
                }
                recordList.add(record);
            }
        }
        if(recordList.size()>0){
            String str="";
            List<String> bedIdList=new ArrayList<>();
            for (Record record : recordList) {
                str+="?,";
                bedIdList.add(record.getStr("id"));
            }
            str=str.substring(0,str.length()-1);

            List<Record> bedEquiModelInfoList=Db.find("select a.bed_id,a.equi_model_id,info_id from main_bed_equi_model a " +
                    "inner join equi_model b on a.equi_model_id=b.id " +
                    " where a.del_flag='0' and a.bed_id in ("+str+") ",bedIdList.toArray());
            Map<String,Record> bedEquiModelInfoMap=new HashMap<>();
            for (Record record : bedEquiModelInfoList) {
                bedEquiModelInfoMap.put(record.getStr("bed_id").toLowerCase(),record);
            }
            for (Record record : recordList) {
                Record bedEquiModelInfo = bedEquiModelInfoMap.get(record.getStr("id").toLowerCase());
                if(bedEquiModelInfo!=null){
                    record.set("equiModelId",bedEquiModelInfo.getStr("equi_model_id"));
                    record.set("equiModelNo",bedEquiModelInfo.getStr("info_id"));
                }else{
                    record.set("equiModelId","");
                    record.set("equiModelNo","");
                }
            }
        }

        List<Record> checkinBedList=new ArrayList<>();
        List<Record> notCheckinBedList=new ArrayList<>();
        for (Record record : recordList) {
            if(StrKit.notBlank(record.getStr("checkinNo"))){
                checkinBedList.add(record);
            }else{
                notCheckinBedList.add(record);
            }
        }
        List<Record> allList=new ArrayList<>();
        allList.addAll(checkinBedList);
        allList.addAll(notCheckinBedList);
        //计算分页
        int startIndex=(pageNumber-1)*pageSize;
        int endIndex=Math.min(startIndex+pageSize,allList.size());
        renderCodePageSuccess("success",allList.subList(startIndex,endIndex),allList.size());
    }

    public void getBedDeviceByBaseId(){
        String baseId=getPara("baseId");
        if(StrKit.isBlank(baseId)){
            renderCodeFailed("基地id不能为空");
            return;
        }
        String sql="select c.id as BedId,c.bed_name as BedNo,d.info_id as DeviceSN from main_bed_equi_model a " +
                "INNER JOIN main_base_building b on a.building_id=b.id " +
                "inner join main_base_bed c on a.bed_id=c.id " +
                "INNER JOIN equi_model d on d.id=a.equi_model_id " +
                "where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' and b.base_id=?  order by c.bed_name";
        List<Record> recordList=Db.find(sql,baseId);
        if(recordList.size()==0){
            renderCodeSuccess("success",new ArrayList<>());
        }else{
            renderCodeSuccess("success",recordList);
        }
    }


    /**
     * 企业微信通过基地id获取床态，允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getFreeBedList(){
        String baseId=getPara("BaseId");
        Map<String,Object> resultMap=new HashMap<>();
        try {
            JSONObject jsonObject=mainBaseBedService.getFreeBedList(baseId);
            resultMap.put("Type",1);
            resultMap.put("Msg","success");
            resultMap.put("Data",jsonObject);
        }catch (Exception e){
            e.printStackTrace();
            resultMap.put("Type",0);
            resultMap.put("Msg","获取异常");
        }


        renderJson(resultMap);
    }

    /**
     * 获取基地床位预定数量
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBaseBedBooks(){
        String baseId=getPara("baseId");
        String beginDateStr=getPara("beginDate");
        String endDateStr=getPara("endDate");
        Map<String,Object> resultMap=new HashMap<>();
        if(StrKit.isBlank(baseId) || StrKit.isBlank(beginDateStr) || StrKit.isBlank(endDateStr)){
            resultMap.put("Result",new String[]{});
            resultMap.put("Message","无效参数");
            renderJson(resultMap);
            return;
        }
        Date beginDate = null;
        Date endDate = null;
        try {
            beginDate = DateUtils.parseDate(beginDateStr,"yyyy-MM-dd");
            endDate = DateUtils.parseDate(endDateStr,"yyyy-MM-dd");
            if(DateUtils.compareDays(beginDate,endDate)>0){
                resultMap.put("Result",new String[]{});
                resultMap.put("Message","截止日期不可小于开始日期");
                renderJson(resultMap);
                return;
            }
        }catch (Exception e){
            resultMap.put("Result",new String[]{});
            resultMap.put("Message","时间格式异常");
            renderJson(resultMap);
            return;
        }
        TreeMap<String, Object> treeMap=mainBaseBedDynamicRecordService.getBaseBedBooks(baseId,beginDate,endDate);
        resultMap.put("Result",treeMap);
        resultMap.put("Message","success");
        renderJson(resultMap);
    }

    /**
     * 修改预定、入住记录信息
     */
    public void updateBedDynamicRecord(){
        MainBaseBedDynamicRecord dynamicRecord=getBean(MainBaseBedDynamicRecord.class,"",true);
        Map<String,Object> resultMap=new HashMap<>();
        if(dynamicRecord==null || (StrKit.isBlank(dynamicRecord.getBookNo()) &&  StrKit.isBlank(dynamicRecord.getCheckinNo()))){
            resultMap.put("code","10001");
            resultMap.put("msg","预定号和入住号不能同时为空");
            renderJson(resultMap);
            return;
        }
        boolean flag=mainBaseBedDynamicRecordService.updateBedDynamicRecord(dynamicRecord);
        if(flag){
            resultMap.put("code","0");
            resultMap.put("msg","success");
        }else{
            resultMap.put("code","10001");
            resultMap.put("msg","修改失败");
        }
        renderJson(resultMap);
    }

    /**
     * 通过会员卡获取会员卡信息
     * cardNumber：xxx,xxxx,xxxx
     */
    public void getCardInfoByCardNumber(){
        String cardNumberStr=getPara("cardNumbers");
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("code","10001");
        if(StrKit.isBlank(cardNumberStr)){
            resultMap.put("msg","会员卡号不能为空");
            renderJson();
            return;
        }
        String[] cardNumbers=null;
        if(cardNumberStr.indexOf(",")!=-1){
            cardNumbers=cardNumberStr.split(",");
        }else{
            cardNumbers=new String[]{cardNumberStr};
        }

        List<Map<String,Object>> mapList=mainMembershipCardTypeService.getCardInfoByCardNumber(cardNumbers);
        resultMap.put("code","0");
        if(mapList.size()>0){
            resultMap.put("data",mapList);
        }else{
            resultMap.put("data","[]");
        }
        renderJson(resultMap);
    }

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void uploadVideoFile(){
        UploadFile file = getFile("file");
        MainVideoRecord record=getBean(MainVideoRecord.class,"",true);

        if(file==null){
            renderCodeFailed("视频文件不能为空");
            return;
        }
        if(StrKit.isBlank(record.getName())){
            renderCodeFailed("视频名称不能为空");
            return;
        }
        if(StrKit.isBlank(record.getTitle())){
            renderCodeFailed("视频标题不能为空");
            return;
        }
        //file.getFile().
        long max=104857600*2;
        //long max=524288000L;
        if(file.getFile().length()>max){
            renderCodeFailed("文件最大为200M");
            return;
        }

        Map<String,Object> obj=mainVideoRecordService.saveVideoRecord(record,file.getFile());

        if((boolean)obj.get("flag")){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed((String)obj.get("msg"));
        }
    }

    @JCors
    @Before(CorsInterceptor.class)
    public void findVideoPage(){
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        MainVideoRecord record=getBean(MainVideoRecord.class,"",true);
        if(pageNumber==null || pageSize==null){
            renderCodeFailed("分页参数不能为空");
            return;
        }
        Page<MainVideoRecord> page=mainVideoRecordService.findVideoPage(pageNumber,pageSize,record);

        Map<String,Object> map=new HashMap<>();
        if(page!=null && page.getList().size()>0){
            map.put("data",page.getList());
            map.put("count",page.getTotalRow());
        }else{
            map.put("data",new JSONArray());
            map.put("count",0);
        }
        renderCodeSuccess("success",map);
    }

    @JCors
    @Before(CorsInterceptor.class)
    public void findVideoById(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderCodeFailed("参数缺失");
            return;
        }
        MainVideoRecord record=mainVideoRecordService.findById(id);

        renderCodeSuccess("success",record);
    }

    @JCors
    @Before(CorsInterceptor.class)
    public void modifyVideoInfo(){
        MainVideoRecord record=getBean(MainVideoRecord.class,"",true);
        if(StrKit.isBlank(record.getId())){
            renderCodeFailed("id不能为空");
            return;
        }
        if(StrKit.isBlank(record.getTitle())){
            renderCodeFailed("视频标题不能为空");
            return;
        }
        if(StrKit.isBlank(record.getName())){
            renderCodeFailed("视频名称不能为空");
            return;
        }
        record.setCreateDate(new Date());
        if(record.update()){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }

    }

    @JCors
    @Before(CorsInterceptor.class)
    public void delVideoRecord(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderCodeFailed("id不能为空");
            return;
        }
        MainVideoRecord record=mainVideoRecordService.findById(id);

        record.setDelFlag("1");
        record.setUpdateDate(new Date());

        //判断系统
        String uploadPathPrefix = "";
        final String osName = System.getProperty("os.name");
        if(osName.toLowerCase().indexOf("linux") > -1 || osName.toLowerCase().indexOf("centos") > -1){
            uploadPathPrefix = Jboot.configValue("uploadPath.linux");
        }else{
            uploadPathPrefix = Jboot.configValue("uploadPath");
        }

        if(record.update()){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
        /*System.out.println(getRequest().getRequestURL());
        System.out.println(getRequest().getRequestURI());
        renderCodeSuccess("");*/
    }

    public void saveVideoType(){
        MainVideoType videoType=getBean(MainVideoType.class,"",true);
        String userId=getPara("userId");
        if(StrKit.isBlank(videoType.getName()) || StrKit.isBlank(userId)){
            renderCodeFailed("参数缺失");
            return;
        }

        if(StrKit.isBlank(videoType.getId())){
            if(Db.findFirst("select * from main_video_type where del_flag='0' and `name`=? ",videoType.getName())!=null){
                renderCodeFailed("该类型名称已存在请勿重复添加");
                return;
            }

        }else{
            if(Db.findFirst("select * from main_video_type where del_flag='0' and `name`=? and id<>? ",videoType.getName(),videoType.getId())!=null){
                renderCodeFailed("该类型名称已存在请勿重复添加");
                return;
            }
        }

        boolean flag=mainVideoTypeService.saveVideoType(videoType,userId);
        if(flag){
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void videoTypePage(){
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        MainVideoType videoType=getBean(MainVideoType.class,"",true);
        Page<MainVideoType> page=mainVideoTypeService.videoTypePageList(pageNumber,pageSize,videoType);
        renderCodePageSuccess("success",page.getList(),page.getTotalRow());
    }

    public void videoTypeList(){
        renderCodeSuccess("success",mainVideoTypeService.videoTypeList());
    }


    public void getAllBase(){

        List<MainBase> baseList=mainBaseService.findBaseList();

        renderJson(baseList);
    }

    @JCors
    @Before(CorsInterceptor.class)
    public void getPermissionMenu(){
        String userId=getPara("userId");
        String permission=getPara("permission");//video
        if(StrKit.isBlank(userId)){
            renderCodeFailed("userId不能为空");
            return;
        }
        List<ZTree> zTrees=wmsStockTypeService.getPermissionMenu(userId,permission,null,"menu");

        renderCodeSuccess("success",zTrees);
    }

    @JCors
    @Before(CorsInterceptor.class)
    public void getPermissionBtn(){
        String userId=getPara("userId");
        String menuId=getPara("menuId");
        String permission=getPara("permission");
        if(StrKit.isBlank(userId) || StrKit.isBlank(menuId)){
            renderCodeFailed("参数缺失");
            return;
        }
        List<ZTree> zTrees=wmsStockTypeService.getPermissionMenu(userId,permission,menuId,"btn");

        renderCodeSuccess("success",zTrees);
    }

    public void roomImport(){
        UploadFile uploadFile =getFile("file");
        if(uploadFile==null){
            renderJson(Ret.fail("msg","文件不能为空"));
            return;
        }
        boolean flag=false;
        try {
            flag=Db.tx(new IAtom() {
                @Override
                public boolean run() throws SQLException {

                    try {

                        XSSFWorkbook workbook=new XSSFWorkbook(uploadFile.getFile());
                        XSSFSheet sheet = workbook.getSheetAt(0);

                        String userId="2CFA83C2-CA2E-4B1F-BA1A-1285354C2797";

                        String bookChannelIdStr="03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7";

                        List<CellRangeAddress> cellRangeAddressList=getCombineCell(sheet);

                        /**
                         * 方式2 普通for
                         */
                        // 获取最后一行
                        int lastRowNum = sheet.getLastRowNum();
                        DecimalFormat format = new DecimalFormat("0.##");
                        // 从第一行开始遍历，直到最后一行
                        for (int j = 0; j <= lastRowNum; j++) {
                            // 获取当前行
                            XSSFRow row = sheet.getRow(j);
                            if (row != null) {
                                // 获取当前行最后一个单元格
                                short cellNum = row.getLastCellNum();
                                // 从第一个单元格开始遍历，直到最后一个单元格
                                for (int k = 0; k < cellNum; k++) {
                                    if(j>1){
                                        if(k>4){
                                            XSSFCell cell = row.getCell(k);
                                            CellType cellType = cell.getCellTypeEnum();
                                            // 注意，我们的EXCEL有NUMERIC、STRING和BOOLEAN三种类型，但这里省略了BOOLEAN，会发生什么呢？
                                            if (cellType == CellType.NUMERIC) {
                                                System.out.print("[");
                                                //获取房间型号
                                                XSSFCell roomTypeCell = row.getCell(0);
                                                String roomType=isCombineCell(cellRangeAddressList,roomTypeCell,sheet);
                                                //获取楼栋
                                                Record roomTypeRecord=Db.findFirst("select * from main_room_type where del_flag='0' and type_name=? ",roomType);
                                                if(roomTypeRecord==null){
                                                    throw new RuntimeException("房间类型查询失败");
                                                }

                                                System.out.print(roomType+"-");
                                                //获取楼栋
                                                XSSFCell buildingCell = row.getCell(2);
                                                String buildingName=null;
                                                if(isMergedRegion(sheet,j,2)){
                                                    buildingName=isCombineCell(cellRangeAddressList,buildingCell,sheet);
                                                }else{
                                                    buildingName=getCellValue(buildingCell);
                                                }
                                                System.out.print(buildingName+"-");

                                                //获取楼栋
                                                Record buildingRecord=Db.findFirst("select * from main_base_building where base_id='CD5974E2-FA5B-42AA-81BC-B2ACF2BD6E10' " +
                                                        " and del_flag='0' and building_name=? ",buildingName);
                                                if(buildingRecord==null){
                                                    throw new RuntimeException("楼栋查询失败");
                                                }

                                                //获取楼层
                                                XSSFCell floorCell = row.getCell(4);
                                                String floorName=null;
                                                if(isMergedRegion(sheet,j,4)){
                                                    floorName=isCombineCell(cellRangeAddressList,floorCell,sheet);
                                                }else{
                                                    floorName=getCellValue(floorCell);
                                                }

                                                System.out.print("楼层："+floorName+"-");
                                                Record floorRecord=Db.findFirst("select * from main_base_floor where del_flag='0' and floor_no=? and building_id=?",floorName,buildingRecord.getStr("id"));
                                                if(floorRecord==null){
                                                    throw new RuntimeException("楼层查询失败");
                                                }

                                                //获取床位数量
                                                XSSFCell bedCountCell = row.getCell(3);
                                                String bedCount=isCombineCell(cellRangeAddressList,bedCountCell,sheet);
                                                System.out.print(bedCount+"-");

                                                String roomName=format.format(cell.getNumericCellValue());
                                                System.out.print(roomName+ "]\t");

                                                int count=Integer.valueOf(bedCount);

                                                MainBaseRoom room=new MainBaseRoom();
                                                room.setRoomName(roomName);
                                                room.setRoomNo(roomName);
                                                room.setRoomType(roomTypeRecord.getStr("id"));
                                                room.setTotalBeds(count);
                                                room.setPeoples(count);
                                                room.setIsBooking("0");
                                                room.setIsEnable("0");
                                                room.setAllowLock("0");
                                                room.setBuildingId(buildingRecord.getStr("id"));
                                                room.setFloorId(floorRecord.getStr("id"));
                                                room.setCreateBy(userId);
                                                room.setUpdateBy(userId);
                                                room.setCreateBy(userId);
                                                boolean saveRoomFlag=mainBaseRoomService.saveRoom(room);
                                                if(saveRoomFlag){
                                                    if(!mainRoomChannelService.saveRoomChannel(room.getId(),bookChannelIdStr, userId)){
                                                        throw new RuntimeException("添加房间渠道报错");
                                                    }

                                                    int bedNo=0;
                                                    for(int i=0;i<count;i++){
                                                        bedNo++;
                                                        MainBaseBed mainBaseBed=new MainBaseBed();
                                                        mainBaseBed.setRoomId(room.getId());
                                                        mainBaseBed.setBedName(room.getRoomName()+"-"+bedNo);
                                                        mainBaseBed.setBedNo(room.getRoomName()+"-"+bedNo);
                                                        if("行政套房".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("6C4AD9A9-89F0-4A40-A677-B1902F1140D9");
                                                        }else if("行政大床房".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("122A3494-21A7-465F-B2F3-124FFB220EC0");
                                                        }else if("行政双床房".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("4496C13B-A614-4741-839E-08322D0B44EF");
                                                        }else if("行政三人间".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("4496C13B-A614-4741-839E-08322D0B44EF");
                                                        }else if("豪华大床房".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("122A3494-21A7-465F-B2F3-124FFB220EC0");
                                                        }else if("豪华双标房".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("4496C13B-A614-4741-839E-08322D0B44EF");
                                                        }else if("豪华单间".equals(roomType)){
                                                            mainBaseBed.setBedTypeId("4496C13B-A614-4741-839E-08322D0B44EF");
                                                        }
                                                        mainBaseBed.setCreateBy(userId);
                                                        mainBaseBed.setUpdateBy(userId);
                                                        mainBaseBed.setBedStatusId("3E541B4D-2BA2-4993-90EE-86C39353ACF9");
                                                        mainBaseBed.setMattressTypeId("80030260-A06F-44B6-9BDA-513777106767");
                                                        mainBaseBed.setIsEnable("0");
                                                        if(mainBaseBedService.saveBed(mainBaseBed)){
                                                            if(mainBedChannelService.saveBedChannel(mainBaseBed.getId(),bookChannelIdStr,userId)){

                                                            }else{
                                                                throw new RuntimeException("添加床位渠道报错");
                                                            }
                                                        }else{
                                                            throw new RuntimeException("添加床位报错");
                                                        }


                                                    }
                                                }else{
                                                    throw new RuntimeException("添加房间报错");
                                                }





                                            } /*else if (cellType == CellType.STRING) {
                                    System.out.print(cell.getStringCellValue() + "\t");
                                }*/
                                        }
                                    }

                                }
                            }
                        }


                        return true;
                    }catch (Exception e){
                        e.printStackTrace();
                        return false;
                    }
                }
            });

        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","导入异常"+e.getMessage()));
            return;
        }
        if(uploadFile.getFile().exists()){
            uploadFile.getFile().delete();
        }
        if(flag){
            renderJson(Ret.fail());
            return;
        }
        renderJson(Ret.ok());
    }

    /**
     * 合并单元格处理,获取合并行
     * @param sheet
     * @return List<CellRangeAddress>
     */
    public  List<CellRangeAddress> getCombineCell(Sheet sheet)
    {
        List<CellRangeAddress> list = new ArrayList<>();
        //获得一个 sheet 中合并单元格的数量
        int sheetmergerCount = sheet.getNumMergedRegions();
        //遍历所有的合并单元格
        for(int i = 0; i<sheetmergerCount;i++)
        {
            //获得合并单元格保存进list中
            CellRangeAddress ca = sheet.getMergedRegion(i);
            list.add(ca);
        }
        return list;
    }

    private  int getRowNum(List<CellRangeAddress> listCombineCell, Cell cell, Sheet sheet){
        int xr = 0;
        int firstC = 0;
        int lastC = 0;
        int firstR = 0;
        int lastR = 0;
        for(CellRangeAddress ca:listCombineCell)
        {
            //获得合并单元格的起始行, 结束行, 起始列, 结束列
            firstC = ca.getFirstColumn();
            lastC = ca.getLastColumn();
            firstR = ca.getFirstRow();
            lastR = ca.getLastRow();
            if(cell.getRowIndex() >= firstR && cell.getRowIndex() <= lastR)
            {
                if(cell.getColumnIndex() >= firstC && cell.getColumnIndex() <= lastC)
                {
                    xr = lastR;
                }
            }

        }
        return xr;

    }

    /**
     * 判断单元格是否为合并单元格，是的话则将单元格的值返回
     * @param listCombineCell 存放合并单元格的list
     * @param cell 需要判断的单元格
     * @param sheet sheet
     * @return
     */
    public  String isCombineCell(List<CellRangeAddress> listCombineCell,Cell cell,Sheet sheet)
            throws Exception{
        int firstC = 0;
        int lastC = 0;
        int firstR = 0;
        int lastR = 0;
        String cellValue = null;
        for(CellRangeAddress ca:listCombineCell)
        {
            //获得合并单元格的起始行, 结束行, 起始列, 结束列
            firstC = ca.getFirstColumn();
            lastC = ca.getLastColumn();
            firstR = ca.getFirstRow();
            lastR = ca.getLastRow();
            if(cell.getRowIndex() >= firstR && cell.getRowIndex() <= lastR)
            {
                if(cell.getColumnIndex() >= firstC && cell.getColumnIndex() <= lastC)
                {
                    Row fRow = sheet.getRow(firstR);
                    Cell fCell = fRow.getCell(firstC);
                    cellValue = getCellValue(fCell);
                    break;
                }
            }
            else
            {
                cellValue = "";
            }
        }
        return cellValue;
    }

    /**
     * 获取合并单元格的值
     * @param sheet
     * @param row
     * @param column
     * @return
     */
    public  String getMergedRegionValue(Sheet sheet ,int row , int column){
        int sheetMergeCount = sheet.getNumMergedRegions();

        for(int i = 0 ; i < sheetMergeCount ; i++){
            CellRangeAddress ca = sheet.getMergedRegion(i);
            int firstColumn = ca.getFirstColumn();
            int lastColumn = ca.getLastColumn();
            int firstRow = ca.getFirstRow();
            int lastRow = ca.getLastRow();

            if(row >= firstRow && row <= lastRow){
                if(column >= firstColumn && column <= lastColumn){
                    Row fRow = sheet.getRow(firstRow);
                    Cell fCell = fRow.getCell(firstColumn);
                    return getCellValue(fCell) ;
                }
            }
        }

        return null ;
    }

    /**
     * 获取单元格的值
     * @param cell
     * @return
     */
    public  String getCellValue(Cell cell){
        if(cell == null) return "";
        CellType cellType=cell.getCellTypeEnum();
        if(cellType==CellType.STRING){
            return cell.getStringCellValue();
        }else if(cellType==CellType.NUMERIC){
            DecimalFormat format = new DecimalFormat("0.##");
           return format.format(cell.getNumericCellValue());
        }
        return cell.getStringCellValue();
    }

    /**
     * 判断指定的单元格是否是合并单元格
     * @param sheet
     * @param row 行下标
     * @param column 列下标
     * @return
     */
    private  boolean isMergedRegion(Sheet sheet,int row ,int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if(row >= firstRow && row <= lastRow){
                if(column >= firstColumn && column <= lastColumn){
                    return true;
                }
            }
        }
        return false;
    }
    
    /**
     * 获取所有菜列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getFoodList(){
    	renderCodeSuccess("success",foodInfoService.findList());
    }
    
    /**
     * 获取基地菜标签列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBasesFoodTagList(){
    	final String baseId = getPara("baseId");
    	final String isEnabled = getPara("isEnabled");
    	List<MainBaseFoodTag> tagList = new ArrayList<MainBaseFoodTag>();
    	if(StrKit.notBlank(baseId)){
    		tagList = mainBaseFoodTagService.findList(baseId, isEnabled);
    		List<MainBasePackage> packageList = mainBasePackageService.findListByBaseId(baseId);
    		if(packageList!=null && packageList.size()>0) {
    			MainBaseFoodTag foodTag = new MainBaseFoodTag();
    			foodTag.setId("package");
    			foodTag.setBaseId(baseId);
    			foodTag.setTagName("套餐");
    			foodTag.setIsEnabled("1");
    			foodTag.setDelFlag(DelFlag.NORMAL);
    			tagList.add(foodTag);
    		}
    		renderCodeSuccess("success",tagList);
    	}else{
    		renderCodeFailed("请选择基地!");
    	}
    }
    

    /**
     * 获取基地菜分页
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBasesFoodPage(){
    	final String baseId = getPara("baseId");
    	final String tagId = getPara("tagId");
    	final String isEnabled = getPara("isEnabled");
    	final String isShelf = getPara("isShelf");
    	final String foodName = getPara("foodName");
    	
    	Page<Record> page = new Page<Record>();
    	if(StrKit.notBlank(baseId)){
    		if(StrKit.notBlank(tagId) && !"package".equals(tagId)) {
    			page = mainBaseFoodService.findFoodPage(getParaToInt("pageNumber",1),getParaToInt("pageSize",10), baseId, tagId, isEnabled, isShelf, foodName, "onlyFood");
    		}else {
    			page = mainBaseFoodService.findFoodPage(getParaToInt("pageNumber",1),getParaToInt("pageSize",10), baseId, tagId, isEnabled, isShelf, foodName, "unionPackage");
    		}
    		if(page!=null && page.getList()!=null && page.getList().size()>0) {
    			for(Record record : page.getList()) {
    				final String relationId = record.getStr("foodId");
    				final String SourceType = record.getStr("SourceType");
    				List<Record> packageFoodList = new ArrayList<Record>();
    				if("Combo".equals(SourceType)) {
    					packageFoodList = mainBasePackageFoodService.findPackageFoodList(relationId);
    				}
    				record.set("packageFoodList", packageFoodList);
    				
    				//套餐图片列表
    				List<CfsFileUpload> fileList = cfsFileUploadService.findListByRelationId(relationId);
    				List<String> imgList = new ArrayList<String>();
    				for(CfsFileUpload file : fileList) {
    					imgList.add(file.getFileUrl());
    				}
    				record.set("imgList", imgList);
    			}
    		}
    		renderJson(new DataTable<Record>(page));
    	}else{
    		DataTable<Record> dataTable = new DataTable<Record>(page);
    		dataTable.setCode(10001);
    		dataTable.setMsg("请选择基地!");
    		renderJson(dataTable);
    	}
    }
    
    /**
     * 获取基地菜列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBasesFoodList(){
    	final String baseId=getPara("baseId");
        final String tagId=getPara("tagId");
    	final String isEnabled = getPara("isEnabled");
    	final String isShelf = getPara("isShelf");
    	final String foodName = getPara("foodName");
    	final String queryType = getPara("queryType");
    	
        if(StrKit.notBlank(baseId)){
            List<Record> foodList = mainBaseFoodService.findFoodList(baseId, tagId, isEnabled, isShelf, foodName, queryType);
        	for(Record record : foodList) {
        		final String relationId = record.getStr("foodId");
        		final String SourceType = record.getStr("SourceType");
        		List<Record> packageFoodList = new ArrayList<Record>();
        		if("Combo".equals(SourceType)) {
        			packageFoodList = mainBasePackageFoodService.findPackageFoodList(relationId);
        		}
        		record.set("packageFoodList", packageFoodList);
        		
        		//菜品图片列表
        		List<CfsFileUpload> fileList = cfsFileUploadService.findListByRelationId(relationId);
        		List<String> imgList = new ArrayList<String>();
        		for(CfsFileUpload file : fileList) {
        			imgList.add(file.getFileUrl());
        		}
        		record.set("imgList", imgList);
        	}
        	renderCodeSuccess("success",foodList);
        }else{
        	renderCodeFailed("请选择基地!");
        }
    }

    
    /**
     * 获取基地餐厅列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBasesRestaurantList(){
    	final String baseId = getPara("baseId");
    	final String isEnabled = getPara("isEnabled");
    	if(StrKit.notBlank(baseId)){
    		renderCodeSuccess("success",mainBaseRestaurantService.findList(baseId, isEnabled));
    	}else{
    		renderCodeFailed("请选择基地!");
    	}
    }
    
    /**
     * 获取基地有效餐厅餐桌列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBasesRestaurantTableList(){
    	String restaurantId = getPara("restaurantId");
    	final String isEnabled = getPara("isEnabled");
    	List<MainBaseRestaurantTable> tableList = mainBaseRestaurantTableService.findList(restaurantId, isEnabled);
    	renderCodeSuccess("success",tableList);
    }
    
    /**
     * 保存基地菜标签
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveBaseFoodTag(){
		boolean flag = false;
		MainBaseFoodTag model = getBean(MainBaseFoodTag.class,"",true);
		if(StrKit.isBlank(model.getId())) {
			model.setId(IdGen.getUUID());
			model.setDelFlag(DelFlag.NORMAL);
			model.setCreateTime(new Date());
			model.setUpdateTime(new Date());
			flag = model.save();
		}else {
			model.setUpdateTime(new Date());
			flag = model.update();
		}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 保存基地菜信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveBaseFood(){
		boolean flag = false;
		MainBaseFood model = getBean(MainBaseFood.class,"",true);
		if(StrKit.isBlank(model.getId())) {
			model.setId(IdGen.getUUID());
			model.setDelFlag(DelFlag.NORMAL);
			model.setCreateTime(new Date());
			model.setUpdateTime(new Date());
			flag=mainSyncRecordService.saveSyncRecord(SyncType.baseFood.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
			if(flag) {
				flag = model.save();
			}else {
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
				return;
			}
		}else {
			model.setUpdateTime(new Date());
			if("1".equals(model.getDelFlag())) {//作废
				List<String> idList = Arrays.asList(new String[]{model.getId()});
		    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
				flag = mainSyncRecordService.saveSyncRecord(SyncType.baseFood.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
			}else {//修改
				flag=mainSyncRecordService.saveSyncRecord(SyncType.baseFood.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
			}
			if(flag) {
				flag = model.update();
			}else {
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
				return;
			}
		}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 保存基地套餐信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveBasePackage(){
    	boolean flag = false;
    	MainBasePackage model = getBean(MainBasePackage.class,"",true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateTime(new Date());
    		model.setUpdateTime(new Date());
    		flag=mainSyncRecordService.saveSyncRecord(SyncType.basePackage.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
    		if(flag) {
    			flag = model.save();
    		}else {
    			renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    			return;
    		}
    	}else {
    		model.setUpdateTime(new Date());
    		if("1".equals(model.getDelFlag())) {//作废
    			List<String> idList = Arrays.asList(new String[]{model.getId()});
    			final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    			flag = mainSyncRecordService.saveSyncRecord(SyncType.basePackage.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
    			if(flag) {
    				List<String> idListF = Arrays.asList(new String[]{});
            		List<MainBasePackageFood> packageFoodList = mainBasePackageFoodService.findListByPackageId(model.getId());
            		if(packageFoodList!=null && packageFoodList.size()>0) {
            			for(MainBasePackageFood packageFood : packageFoodList) {
            				idListF.add(packageFood.getId());
            				packageFood.delete();
            			}
            		}
            		final String idStrF = "["+idListF.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
            		final boolean flagF = mainSyncRecordService.saveSyncRecord(SyncType.basePackageFood.getKey(), SyncDataType.DELETE, idStrF,AuthUtils.getUserId());
            		if(flagF) {
            			renderJson(Ret.ok("msg", "操作成功"));
            		}else {
            			renderJson(Ret.fail("msg", "操作失败,同步套餐关联数据失败"));
            		}
    			}
    		}else {//修改
    			flag=mainSyncRecordService.saveSyncRecord(SyncType.basePackage.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
    		}
    		if(flag) {
    			flag = model.update();
    		}else {
    			renderJson(Ret.fail("msg","操作失败,同步数据失败"));
    			return;
    		}
    	}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    
    /**
     * 保存餐厅信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveBaseRestaurant(){
    	boolean flag = false;
    	MainBaseRestaurant model = getBean(MainBaseRestaurant.class,"",true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateTime(new Date());
    		model.setUpdateTime(new Date());
    		flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurant.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
			if(flag) {
				flag = model.save();
			}else {
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
				return;
			}
    	}else {
    		model.setUpdateTime(new Date());
    		if("1".equals(model.getDelFlag())) {//作废
				List<String> idList = Arrays.asList(new String[]{model.getId()});
		    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
				flag = mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurant.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
			}else {//修改
				flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurant.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
			}
			if(flag) {
				flag = model.update();
			}else {
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
				return;
			}
    	}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    
    /**
     * 保存餐桌信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveBaseRestaurantTable(){
    	boolean flag = false;
    	MainBaseRestaurantTable model = getBean(MainBaseRestaurantTable.class,"",true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateTime(new Date());
    		model.setUpdateTime(new Date());
    		flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurantTable.getKey(),SyncDataType.INSERT,JSON.toJSONString(model),model.getCreateBy());
			if(flag) {
				flag = model.save();
			}else {
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
				return;
			}
    	}else {
    		model.setUpdateTime(new Date());
    		if("1".equals(model.getDelFlag())) {//作废
				List<String> idList = Arrays.asList(new String[]{model.getId()});
		    	final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
				flag = mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurantTable.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
			}else {//修改
				flag=mainSyncRecordService.saveSyncRecord(SyncType.baseRestaurantTable.getKey(),SyncDataType.UPDATE,JSON.toJSONString(model),model.getUpdateBy());
			}
			if(flag) {
				flag = model.update();
			}else {
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
				return;
			}
    	}
    	if(flag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    /**
     * 保存同步信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveSyncRecord(){
    	boolean flag = false;
    	final String syncType = getPara("syncType");
    	final String dataType = getPara("dataType");
    	final String jsonStr = getPara("jsonStr");
    	final String dataId = getPara("dataId");
    	final String userId = getPara("userId");
    	if(StrKit.isBlank(syncType)) {
    		renderCodeFailed("syncType参数不能为空");
            return;
    	}
    	if(StrKit.isBlank(dataType)) {
    		renderCodeFailed("dataType参数不能为空");
    		return;
    	}
    	if(StrKit.isBlank(jsonStr)) {
    		renderCodeFailed("jsonStr参数不能为空");
    		return;
    	}
    	if(StrKit.isBlank(dataId)) {
    		renderCodeFailed("dataId参数不能为空");
    		return;
    	}
    	if(StrKit.isBlank(userId)) {
    		renderCodeFailed("userId参数不能为空");
    		return;
    	}
    	//作废操作
    	if("delete".equals(dataType)) {
    		List<String> idList = Arrays.asList(new String[]{dataId});
    		final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
    		flag = mainSyncRecordService.saveSyncRecord(syncType, dataType, idStr, userId);
    	}else {
    		//新增、修改操作
    		flag = mainSyncRecordService.saveSyncRecord(syncType, dataType, jsonStr, userId);
    	}
    	if(flag){
    		logger.info("syncType="+syncType+"和dataType="+dataType+"的同步记录操作成功!");
    		renderCodeSuccess("操作成功!");
    	}else{
    		logger.info("syncType="+syncType+"和dataType="+dataType+"的同步记录操作失败!");
    		renderCodeFailed("操作失败!");
    	}
    }

    @JCors
    public void updateRoom(){
        MainBaseRoom room=getBean(MainBaseRoom.class,"",true);
        MainBaseRoom mainBaseRoom=mainBaseRoomService.findById(room.getId());
        if(mainBaseRoom==null){
            renderCodeFailed("该id不存在");
            return;
        }
        mainBaseRoom.setIsBooking(room.getIsBooking());
        mainBaseRoom.setIsEnable(room.getIsEnable());
        mainBaseRoom.setMaintainState(room.getMaintainState());
        if(StrKit.notBlank(room.getRoomUsage())){
            mainBaseRoom.setRoomUsage(room.getRoomUsage());
        }
        boolean flag=mainBaseRoomService.saveRoom(mainBaseRoom);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }

    @JCors
    public void updateRoomChannel(){
        String roomId=getPara("roomId");
        String channelIds=getPara("channelIds");
        String userId=getPara("userId");
        boolean flag=mainRoomChannelService.saveRoomChannel(roomId,channelIds,userId);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }

    @JCors
    public void updateFloor(){
        MainBaseFloor floor=getBean(MainBaseFloor.class,"",true);

        MainBaseFloor mainBaseFloor=mainBaseFloorService.findById(floor.getId());
        if(mainBaseFloor==null){
            renderCodeFailed("改id不存在");
            return;
        }
        mainBaseFloor.setIsBooking(floor.getIsBooking());
        mainBaseFloor.setIsEnable(floor.getIsEnable());
        boolean flag=mainBaseFloorService.saveFloor(mainBaseFloor);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }

    @JCors
    public void updateFloorChannel(){
        String floorId=getPara("floorId");
        String channelIds=getPara("channelIds");
        String userId=getPara("userId");
        boolean flag=mainBaseBuildingFloorChannelService.saveBaseBuildingFloorChannel("floor",floorId,channelIds,userId);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }

    @JCors
    public void updateBuilding(){
        MainBaseBuilding building=getBean(MainBaseBuilding.class,"",true);

        MainBaseBuilding mainBaseBuilding=mainBaseBuildingService.findById(building.getId());
        mainBaseBuilding.setIsBooking(building.getIsBooking());
        mainBaseBuilding.setIsEnable(building.getIsEnable());
        if(mainBaseBuilding==null){
            renderCodeFailed("改id不存在");
            return;
        }

        boolean flag=mainBaseBuildingService.saveBuilding(mainBaseBuilding);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }

    @JCors
    public void updateBuildingChannel(){
        String buildingId=getPara("buildingId");
        String channelIds=getPara("channelIds");
        String userId=getPara("userId");
        boolean flag=mainBaseBuildingFloorChannelService.saveBaseBuildingFloorChannel("building",buildingId,channelIds,userId);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }

    public void getMaintainStateBedNum(){
        Integer num=Db.queryInt("select COUNT(id) from main_base_room where building_id in ( select id from main_base_building where del_flag='0' and base_id='27a558b2-c205-4937-b6f4-fed1fa68ac55'" +
                ") and maintain_state in ('Maintaining','ToMaintain') and del_flag='0' ");

        if(num==null){
            num=0;
        }
        renderCodeSuccess("success",num);
    }
    
    /**
     * 获取所有银行列表允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBankList(){
        renderCodeSuccess("success", mainBankService.findList());
    }

    /**
     * 获取所有分公司列表允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBranchOfficeList(){
        renderCodeSuccess("success", mainBranchOfficeService.getUnDelBranchOffice());
    }
    
    /**
     * 获取旅游线路分页列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void touristRoutePage(){
    	MainTouristRoute model = getBean(MainTouristRoute.class, "", true);
		Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(model.getName())) {
			columns.likeAppendPercent("name", model.getName());
		}
		if(StrKit.notBlank(model.getIsEnable())) {
			columns.eq("is_enable", model.getIsEnable());
		}
		Page<MainTouristRoute> modelPage = mainTouristRouteService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
		renderJson(new DataTable<MainTouristRoute>(modelPage));
    }
    
    /**
     * 获取旅游线路列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void touristRouteList(){
    	List<MainTouristRoute> modelList = mainTouristRouteService.findValidList();
    	renderCodeSuccess("success", modelList);
    }
    
    /**
     * 保存旅游线路信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveTouristRoute(){
    	boolean flag = false;
    	MainTouristRoute model = getBean(MainTouristRoute.class,"",true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateDate(new Date());
    		model.setUpdateDate(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateDate(new Date());
    		flag = model.update();
    	}
    	if(flag){
    		renderCodeSuccess("success");
    	}else{
    		renderCodeFailed("error");
    	}
    }
    
    /**
     * 保存旅游线路项目信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveTouristRouteItem(){
    	boolean flag = false;
    	MainTouristItem model = getBean(MainTouristItem.class,"",true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateDate(new Date());
    		model.setUpdateDate(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateDate(new Date());
    		flag = model.update();
    	}
    	if(flag){
    		renderCodeSuccess("success");
    	}else{
    		renderCodeFailed("error");
    	}
    }
    
    /**
     * 保存旅游线路明细信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveTouristRouteDays(){
    	boolean flag = false;
    	final String touristDetailDays = getPara("touristDetailDays");
    	if(StrKit.notBlank(touristDetailDays)){
            if(!touristDetailDays.startsWith("[") || !touristDetailDays.endsWith("]")){
                renderCodeFailed("传入参数格式不正确");
            }else{
            	List<MainTouristDays> daysList = JSON.parseArray(touristDetailDays, MainTouristDays.class);
            	for(MainTouristDays touristDays : daysList){
            		if(StrKit.notBlank(touristDays.getId())){
            			if(touristDays.getUpdateDate()==null){
            				touristDays.setUpdateDate(new Date());
            			}
            			flag = touristDays.update();
            		}else{
            			touristDays.setId(IdGen.getUUID());
            			touristDays.setDelFlag(DelFlag.NORMAL);
            			if(touristDays.getCreateDate()==null){
            				touristDays.setCreateDate(new Date());
            			}
            			if(touristDays.getUpdateDate()==null){
            				touristDays.setUpdateDate(new Date());
            			}
            			flag = touristDays.save();
            		}
            		if(flag){
            			if(touristDays.getDaysDetailList()!=null && touristDays.getDaysDetailList().size()>0){
            				for(MainTouristDaysDetail daysDetail : touristDays.getDaysDetailList()){
            					if(StrKit.notBlank(daysDetail.getId())){
            						if(daysDetail.getUpdateDate()==null){
            							daysDetail.setUpdateDate(new Date());
                        			}
                        			flag = daysDetail.update();
            					}else{
            						daysDetail.setId(IdGen.getUUID());
            						daysDetail.setDaysId(touristDays.getId());
            						daysDetail.setDelFlag(DelFlag.NORMAL);
                        			if(daysDetail.getCreateDate()==null){
                        				daysDetail.setCreateDate(new Date());
                        			}
                        			if(daysDetail.getUpdateDate()==null){
                        				daysDetail.setUpdateDate(new Date());
                        			}
                        			flag = daysDetail.save();
            					}
            				}
            			}
            		}
            	}
            }
    	}else{
    		renderCodeFailed("传入参数不能为空");
    	}
    	if(flag){
    		renderCodeSuccess("success");
    	}else{
    		renderCodeFailed("error");
    	}
    }
  
    /**
     * 物品分类树信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void goodsTypeTree(){
        List<ZTree> zTrees=wmsStockTypeService.findStockTypeTree("1");
        renderJson(zTrees);
    }
    
    /**
     * 保存物品信息
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void saveGoods(){
    	WmsStockModels model = getBean(WmsStockModels.class,"",true);
    	if(StrKit.isBlank(model.getStockTypeId())){
    		renderCodeFailed("物品分类不能为空!");
            return;
    	}
    	String codePrefix = wmsStockTypeService.getAllCodePrefix(model.getStockTypeId());
    	if(StrKit.isBlank(codePrefix)){
    		renderCodeFailed("物品分类编号前缀不能为空!");
            return;
    	}
    	if(StrKit.notBlank(model.getStockTypeId())){
    		model.setStockTypeId(model.getStockTypeId().toUpperCase());
    	}
    	if(StrKit.notBlank(model.getUnitId())){
    		model.setUnitId(model.getUnitId().toUpperCase());
    	}
    	if(StrKit.notBlank(model.getCreateBy())){
    		model.setCreateBy(model.getCreateBy().toUpperCase());
    	}
    	if(StrKit.notBlank(model.getUpdateBy())){
    		model.setUpdateBy(model.getUpdateBy().toUpperCase());
    	}
        if(StrKit.notBlank(model.getId())){

            Integer n=Db.queryInt("select count(id) from wms_stock_models where name=? and standard=? and id<>?",model.getName(),model.getStandard(),model.getId());
            if(n!=null && n>0){
                renderCodeFailed("该名字和规格已经存在了，请勿重复添加!");
                return;
            }

            if(StrKit.notBlank(model.getBarcodeNumber())){
                Integer num=Db.queryInt("select count(id) from wms_stock_models where barcode_number=? and id<>?",model.getBarcodeNumber(),model.getId());
                if(num!=null && num>0){
                    renderCodeFailed("该条形码编号已存在!");
                    return;
                }
            }
        }else{

            Integer n=Db.queryInt("select count(id) from wms_stock_models where name=? and standard=? ",model.getName(),model.getStandard());
            if(n!=null && n>0){
                renderCodeFailed("该名字和规格已经存在了，请勿重复添加!");
                return;
            }

            if(StrKit.notBlank(model.getBarcodeNumber())){
                Record m=wmsStockModelService.findModelByBarCode(model.getBarcodeNumber(),null);
                if(m!=null){
                    renderCodeFailed("该条形码编号已存在!");
                    return;
                }
            }
        }
    	if(wmsStockModelService.saveStockModel(model)){
    		renderCodeSuccess("操作成功!",model);
    	}else{
    		renderCodeFailed("操作失败!");
    	}
    }


    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getBaseBuildingEquiCount(){
        String baseId=getPara("baseId");
        if(StrKit.isBlank(baseId)){
            renderCodeFailed("参数缺失");
            return;
        }
        //
        List<Record> recordList=Db.find("select a.id,a.building_name,(select count(id) from main_bed_equi_model  where a.id=building_id and del_flag='0') as count from main_base_building a " +
                " where a.base_id=? and a.del_flag='0' ORDER BY a.building_name ",baseId);
        renderCodeSuccess("success",recordList);
    }

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getEquiTypeCount(){
        String baseId=getPara("baseId");
        if(StrKit.isBlank(baseId)){
            renderCodeFailed("参数缺失");
            return;
        }
        //
        List<Record> recordList=Db.find("select d.type_name as typeName,COUNT(c.id) as typeCount from main_base_building a \n" +
                "left join main_bed_equi_model b on a.id=b.building_id \n" +
                "left join equi_model c on c.id=b.equi_model_id\n" +
                "left join equi_model_type d on d.id=c.model_type_id\n" +
                "where a.base_id=? and a.del_flag='0'  and b.del_flag='0'\n" +
                "\n" +
                "GROUP BY d.type_name\n" +
                "\n",baseId);
        renderCodeSuccess("success",recordList);
    }

    /**
     * 获取所有短信类型列表允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void smsTypeList(){
        JSONArray smsTypeArray = new JSONArray();
        List<MainSmsType> smsTypeList = mainSmsTypeService.findList();
        for(MainSmsType smsType : smsTypeList){
            JSONObject smsTypeObj = new JSONObject();
            smsTypeObj.put("Text",smsType.getTypeName());
            smsTypeObj.put("Value",smsType.getId());
            smsTypeObj.put("Content",smsType.getTypeTemplate());
            smsTypeArray.add(smsTypeObj);
        }
        renderJson(smsTypeArray);
    }

    /**
     * 短信发送申请审核通过回调地址允许跨域
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void smsApplyCallBack(){
        Long beginTime = System.currentTimeMillis();
        logger.info("接收开始时间==="+beginTime);
        final String jsonStr = getRawData();
        JSONObject returnJson = JSONObject.parseObject(jsonStr);
        final String taskId = returnJson.getString("TaskId");
        final String currentStep = returnJson.getString("CurrentStep");
        final String currentStepAlias = returnJson.getString("CurrentStepAlias");
        final String currentUserName = returnJson.getString("CurrentUserName");
        final String currentStatus = returnJson.getString("CurrentStatus");
        logger.info("taskId==="+taskId);
        logger.info("currentStep==="+currentStep);
        logger.info("currentStepAlias==="+currentStepAlias);
        logger.info("currentUserName==="+currentUserName);
        logger.info("currentStatus==="+currentStatus);
        Global.executorService.execute(new Runnable() {
           @Override
           public void run() {
               if("Completed".equals(currentStatus)) {
                   mainSmsTypeService.bmpInvokeGetForm(taskId);
               }
           }
        });
        logger.info("接收结束时间==="+System.currentTimeMillis());
        Long opetime = System.currentTimeMillis() - beginTime;
        logger.info("耗时==="+opetime);
        renderCodeSuccess("success");
    }

    public void getExamineSchemeList(){

        List<MainExamineScheme> schemeList=mainExamineSchemeService.findExamineSchemeList();
        renderCodeSuccess("success",schemeList);
    }

    public void getProblemBySchemeId(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderCodeFailed("参数缺失");
            return;
        }
        List<MainExamineProblem> problemList=mainExamineProblemService.getSchemeProblemList(id);
        renderCodeSuccess("success",problemList);
    }

    public void getProblemByRecordId(){
        String id=getPara("recordId");
        if(StrKit.isBlank(id)){
            renderCodeFailed("参数缺失");
            return;
        }
        MainExamineRecord record=mainExamineRecordService.findById(id);

        List<MainExamineProblem> problemList=mainExamineProblemService.getSchemeProblemList(record.getSchemeId());
        renderCodeSuccess("success",problemList);
    }

    public void saveExamineRecord(){
        MainExamineRecord examineRecord=getBean(MainExamineRecord.class,"",true);

        if(DateUtils.compareDays(examineRecord.getEndTime(),examineRecord.getStartTime())<=0){
            renderCodeFailed("结束时间必须大于开始时间，修改失败");
            return;
        }
        if(StrKit.notBlank(examineRecord.getId())){

            MainExamineRecord record=mainExamineRecordService.findById(examineRecord.getId());
            if("2".equals(record.getStatus()) || "3".equals(record.getStatus())){
                renderCodeFailed("该单已完成或已过期，修改失败");
                return;
            }


        }

        boolean flag=mainExamineRecordService.saveExamineRecord(examineRecord,AuthUtils.getUserId());
        if(flag){
            MainExamineScheme scheme = mainExamineSchemeService.findById(examineRecord.getSchemeId());
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("id",examineRecord.getId());
            jsonObject.put("isWeightedMean",scheme.getIsWeightedMean());
            renderCodeSuccess("操作成功",jsonObject);
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void getExamineRecordById(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderCodeFailed("参数缺失");
            return;
        }
        MainExamineRecord examineRecord=mainExamineRecordService.findById(id);
        renderCodeSuccess("success",examineRecord);
    }


    public void getCreateExamineRecordPage(){
        String createBy=getPara("createBy");
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        if(StrKit.isBlank(createBy) || pageNumber==null || pageSize==null){
            renderCodeFailed("参数缺失");
            return;
        }
        MainExamineRecord record=new MainExamineRecord();
        record.setCreateBy(createBy);
        Page<MainExamineRecord> recordPage=mainExamineRecordService.pageList(pageNumber,pageSize,record);
        renderCodePageSuccess("success",recordPage.getList(),recordPage.getTotalRow());
    }

    public void getHandleExamineRecordPage(){
        String userId=getPara("userId");
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        if(StrKit.isBlank(userId) || pageNumber==null || pageSize==null){
            renderCodeFailed("参数缺失");
            return;
        }
        MainExamineRecord record=new MainExamineRecord();
        record.setAnswerUserId(userId);
        Page<MainExamineRecord> recordPage=mainExamineRecordService.pageList(pageNumber,pageSize,record);
        renderCodePageSuccess("success",recordPage.getList(),recordPage.getTotalRow());
    }

    public void examineRecordResultSave(){
        String recordId=getPara("recordId");
        String data=getPara("data");
        String optionTextContent=getPara("optionTextContent");
        String files=getPara("files");
        String remarks=getPara("remarks");
        String isTempStorage=getPara("isTempStorage");
        if(StrKit.isBlank(recordId) || StrKit.isBlank(data)){
            renderCodeFailed("参数缺失");
            return;
        }

        JSONObject optionTextContentJsonObj=JSON.parseObject(optionTextContent);
        JSONObject filesObj=JSON.parseObject(files);
        JSONObject remarksObj=JSON.parseObject(remarks);

        JSONObject jsonObject=JSON.parseObject(data);
        MainExamineRecord record=mainExamineRecordService.findById(recordId);
        record.setCompleteTime(new Date());
        record.setStatus("2");
        record.setUpdateBy(record.getAnswerUserId());
        record.setUpdateDate(new Date());

        List<MainExamineProblem> problemList=mainExamineProblemService.getSchemeProblemList(record.getSchemeId());

        Map<String,Integer> sortMap=new HashMap<>();

        for (MainExamineProblem problem : problemList) {
            sortMap.put(problem.getId(),problem.getSort());
        }

        List<MainExamineRecordResult> recordResultList=new ArrayList<>();
        Double recordTotalSocre=0.0;
        Double recordSocre=0.0;
        for (String key : jsonObject.keySet()) {
            MainExamineProblem problem=mainExamineProblemService.findById(key);
            Double totalSocre=0.0;
            Double socre=0.0;
            if(ExamineProblemType.radio.getKey().equals(problem.getType())){
                totalSocre=Db.queryDouble("select max(score) from main_examine_problem_option where problem_id=? and is_enabled='1'  ",key);
                if(jsonObject.getJSONArray(key)!=null && jsonObject.getJSONArray(key).size()>0) {
                    socre = Db.queryDouble("select score from main_examine_problem_option where id=?  ", jsonObject.getJSONArray(key).getString(0));
                }

            }else if(ExamineProblemType.checkbox.getKey().equals(problem.getType())){
                totalSocre=Db.queryDouble("select SUM(score) from main_examine_problem_option where problem_id=? and is_enabled='1' ",key);

                if(jsonObject.getJSONArray(key)!=null && jsonObject.getJSONArray(key).size()>0){
                    String str="";
                    List<String> optionIds=new ArrayList<>();
                    for (int i = 0; i < jsonObject.getJSONArray(key).size(); i++) {
                        str+="?,";
                        optionIds.add(jsonObject.getJSONArray(key).getString(i));
                    }
                    str=str.substring(0,str.length()-1);
                    socre=Db.queryDouble("select SUM(score) from main_examine_problem_option where id in("+str+") and is_enabled='1' ",optionIds.toArray());
                }



            }else if(ExamineProblemType.text.getKey().equals(problem.getType())){

            }
            if(totalSocre==null){
                totalSocre=0.0;
            }
            if(socre==null){
                socre=0.0;
            }
            recordTotalSocre+=totalSocre;
            recordSocre+=socre;
            MainExamineRecordResult result = mainExamineRecordResultService.getResult(recordId, key);
            if(result!=null){
                result.setDelFlag("1");
                result.setUpdateDate(new Date());
                result.update();
            }

            MainExamineRecordResult recordResult=new MainExamineRecordResult();
            recordResult.setId(IdGen.getUUID());
            recordResult.setRecordId(recordId);
            recordResult.setTotalScore(totalSocre);
            recordResult.setScore(socre);
            recordResult.setDelFlag("0");
            recordResult.setSort(sortMap.get(key));
            recordResult.setProblemId(key);
            recordResult.setUpdateDate(new Date());
            recordResult.setCreateDate(new Date());
            if(filesObj!=null && filesObj.getJSONArray(key)!=null){
                recordResult.setFiles(JSON.toJSONString(filesObj.getJSONArray(key)));
            }else{
                recordResult.setFiles(null);
            }

            if(remarksObj!=null && remarksObj.getString(problem.getId())!=null){
                recordResult.setRemark(remarksObj.getString(problem.getId()));
            }else{
                recordResult.setRemark(null);
            }

            String str=jsonObject.getString(key);
            if(str.startsWith("[") && str.endsWith("]")){
                recordResult.setOptionIds(JSON.toJSONString(jsonObject.getJSONArray(key)));
                recordResult.setContent(null);
                recordResult.setOptionTextContent(null);
                if(optionTextContentJsonObj!=null){
                    recordResult.setOptionTextContent(JSON.toJSONString(optionTextContentJsonObj));
                }
            }else{
                recordResult.setOptionTextContent(null);
                recordResult.setOptionIds(null);
                recordResult.setContent(str);
            }
            recordResultList.add(recordResult);
        }

        if("1".equals(isTempStorage)){
            Db.batchSave(recordResultList,recordResultList.size());
            renderCodeSuccess("操作成功");
            return;
        }


        record.setTotalScore(recordTotalSocre);
        record.setScore(recordSocre);
        record.setScoreRate(new BigDecimal(recordSocre).divide(new BigDecimal(recordTotalSocre),4,BigDecimal.ROUND_HALF_UP).doubleValue());

        MainExamineScheme examineScheme = mainExamineSchemeService.findById(record.getSchemeId());
        //看是否开启加权平均计分
        if("1".equals(examineScheme.getIsWeightedMean())){
            List<MainExamineSchemeWeightedMeanConfig> schemeWeightedMeanConfigList = mainExamineSchemeWeightedMeanConfigService.findListBySchemeId(examineScheme.getId());

            String problemWeightedMeanConfigIdSql="select b.id,a.weighted_mean_config_id from main_examine_scheme_problem_rel a " +
                    " INNER JOIN main_examine_problem b on a.problem_id=b.id where a.is_enabled='1' and a.scheme_id=? ";

            List<Record> problemWeightedMeanConfigIdList=Db.find(problemWeightedMeanConfigIdSql,examineScheme.getId());
            Map<String,List<String>> problemWeightedMeanConfigIdMap=new HashMap<>();
            for (Record problemWeightedMeanConfig : problemWeightedMeanConfigIdList) {
                String weightedMeanConfigId = problemWeightedMeanConfig.getStr("weighted_mean_config_id");
                String problemId = problemWeightedMeanConfig.getStr("id");
                if(problemWeightedMeanConfigIdMap.containsKey(weightedMeanConfigId)){
                    List<String> problemIdList = problemWeightedMeanConfigIdMap.get(weightedMeanConfigId);
                    problemIdList.add(problemId);
                }else{
                    List<String> problemIdList = new ArrayList<>();
                    problemIdList.add(problemId);
                    problemWeightedMeanConfigIdMap.put(weightedMeanConfigId,problemIdList);
                }
            }

            double totalScoreAverage=0.0;
            double allProblemtotalScore=0.0;
            double allFullScore=0.0;
            for (MainExamineSchemeWeightedMeanConfig weightedMeanConfig : schemeWeightedMeanConfigList) {
                List<String> problemIdList = problemWeightedMeanConfigIdMap.get(weightedMeanConfig.getId());
                Double fullMark=weightedMeanConfig.getFullMark();
                double totalProblemScore=0.0;
                for (MainExamineRecordResult recordResult : recordResultList) {
                    if(problemIdList.contains(recordResult.getProblemId())){
                        totalProblemScore+=recordResult.getScore();
                    }
                }
                allProblemtotalScore+=totalProblemScore;
                allFullScore+=fullMark;

                double scoreAverage=BigDecimal.valueOf(totalProblemScore)
                        .divide(BigDecimal.valueOf(fullMark),4,BigDecimal.ROUND_HALF_UP)
                        .multiply(BigDecimal.valueOf(weightedMeanConfig.getWeightedMeanValue()))
                        .divide(BigDecimal.valueOf(100)).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();

                totalScoreAverage=BigDecimal.valueOf(totalScoreAverage).add(BigDecimal.valueOf(scoreAverage)).doubleValue();
            }

            record.setTotalScore(allFullScore);
            record.setScore(allProblemtotalScore);
            record.setScoreRate(totalScoreAverage);

        }

        if(record.update()){
            Db.batchSave(recordResultList,recordResultList.size());
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void getExamineRecordResult(){
        String recordId=getPara("recordId");
        if(StrKit.isBlank(recordId)){
            renderCodeFailed("参数缺失");
            return;
        }

        List<MainExamineProblem> problemList=mainExamineProblemService.recordResultProblemList(recordId);

        renderCodeSuccess("success",problemList);
    }

    public void exceptionRecordResultPageList(){
        Integer pageNumber=getParaToInt("pageIndex");
        Integer pageSize=getParaToInt("pageSize");
        MainExamineRecord record=getBean(MainExamineRecord.class,"",true);

        if(pageNumber==null || pageSize==null){
            renderCodeFailed("参数缺失");
            return;
        }

        Page<Record> recordPage=mainExamineRecordService.recordResultPageList(pageNumber,pageSize,record);
        renderCodePageSuccess("success",recordPage.getList(),recordPage.getTotalRow());
    }

    public void getAllExamineRecord(){
        MainExamineRecord record=getBean(MainExamineRecord.class,"",true);
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        if(pageNumber==null || pageSize==null){
            renderCodeFailed("参数缺失");
            return;
        }
        Page<MainExamineRecord> recordPage=mainExamineRecordService.pageList(pageNumber,pageSize,record);
        renderCodePageSuccess("success",recordPage.getList(),recordPage.getTotalRow());
    }

    public void delExamineRecord(){
        String id=getPara("id");
        String userId=getPara("userId");
        MainExamineRecord mainExamineRecord=mainExamineRecordService.findById(id);
        mainExamineRecord.setDelFlag("1");
        mainExamineRecord.setUpdateDate(new Date());
        mainExamineRecord.setUpdateBy(userId);
        if(mainExamineRecord.update()){
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void getRecordWeightedMeanReportForm(){
        String recordId=getPara("recordId");

        String sql="select `name`,weighted_mean_value as weightedMeanValue,full_mark as fullMark,totalScore,ROUND(totalScore/full_mark*weighted_mean_value,1) as weight " +
                "from (select b.`name`,b.weighted_mean_value,b.full_mark,sum(d.score) as totalScore from main_examine_record a " +
                "inner join main_examine_scheme_weighted_mean_config b on a.scheme_id=b.scheme_id and b.del_flag='0' " +
                "inner join main_examine_scheme_problem_rel c on c.weighted_mean_config_id=b.id and c.is_enabled='1' " +
                "inner join main_examine_record_result d on d.record_id=a.id and c.problem_id=d.problem_id and d.del_flag='0' " +
                "where a.del_flag='0' and a.id=? " +
                "GROUP BY b.id " +
                ") t ";
        List<Record> recordList=Db.find(sql,recordId);
        renderCodeSuccess("success",recordList);
    }

    public void getRoomPrivateRoomDays(){
        String bedId=getPara("bedId");
        String isPrivateRoom=getPara("isPrivateRoom");
        Integer checkinNum=getParaToInt("checkinNum");
        Integer halfNum=getParaToInt("halfNum");
        Integer exemptionNum=getParaToInt("exemptionNum");


        //获取房间类型上的包房倍数，
        MainBaseBed firstBaseBed=mainBaseBedService.findById(bedId);
        MainBaseRoom firstBaseRoom= mainBaseRoomService.findById(firstBaseBed.getRoomId());
        MainBaseRoomType firstRoomType=mainBaseRoomTypeService.findById(firstBaseRoom.getBaseRoomType());

        List<Record> roomBedNumList=Db.find("select a.id,CASE  b.is_big_bed WHEN '1' THEN if(d.is_big_bed_room='1',2,1) else 1 end as bedNum  from main_base_bed a  " +
                "left join main_bed_type b on a.bed_type_id=b.id  " +
                "inner join main_base_room c on c.id=a.room_id " +
                "inner join main_base_room_type d on d.id=c.base_room_type  " +
                "where a.room_id=? and a.del_flag='0' and a.is_enable='0' ",firstBaseBed.getRoomId());
        Map<String,Integer> roomBedNumMap=new HashMap<>();
        int roomTotalBedNum=0;
        for(Record record:roomBedNumList){
            roomBedNumMap.put(record.getStr("id"),record.getInt("bedNum"));
            roomTotalBedNum+=record.getInt("bedNum");
        }
        double bedCost=0.0;
        if(firstRoomType!=null){
            int mainBaseBedCount=mainBaseBedService.queryBedCountByRoomId(firstBaseRoom.getId());
            double privateRoomDays=0.0;
            if(firstRoomType.getPrivateRoomDays()!=null && firstRoomType.getPrivateRoomDays()>0){
                privateRoomDays=firstRoomType.getPrivateRoomDays();
            }else{
                privateRoomDays=(double)finaExpenseRecordService.getBedCount(firstBaseBed.getId()).get("totalBeds");
            }
            if("1".equalsIgnoreCase(isPrivateRoom)){

                if(roomTotalBedNum>=checkinNum){
                    bedCost=privateRoomDays;
                }else{
                    bedCost=(checkinNum-roomTotalBedNum)+privateRoomDays;
                    bedCost=bedCost-(halfNum*0.5);
                    bedCost=bedCost-(exemptionNum);
                }
            }else{
                //使用床位的床位数量
                int checkinBedNum=roomBedNumMap.get(firstBaseBed.getId());
                if("1".equals(firstRoomType.getIsBigBedRoom())){

                    //总床位数量
                    /*if(checkinNum>1){
                        bedCost=privateRoomDays*checkinBedNum/roomTotalBedNum/checkinBedNum;

                    }else{
                        bedCost=privateRoomDays*checkinBedNum/roomTotalBedNum;
                    }*/
                    if(checkinNum>1){
                        bedCost=privateRoomDays*checkinBedNum/roomTotalBedNum/checkinBedNum;

                    }else{
                        bedCost=privateRoomDays*checkinBedNum/roomTotalBedNum;


                    }
                }else{
                    bedCost=privateRoomDays/mainBaseBedCount;
                }
                double returnBedCost=0.0;

                int wholeNum=checkinNum-1-halfNum-exemptionNum;

                for (int i = 0; i < checkinNum; i++) {
                    if(checkinBedNum>i){

                        if(i>0){
                            if(wholeNum>0){
                                returnBedCost+=bedCost;
                                wholeNum--;
                            }else if(halfNum>0){
                                returnBedCost+=(bedCost*0.5);
                                halfNum--;
                            }else if(exemptionNum>0){
                                exemptionNum--;
                            }
                        }else{
                            returnBedCost+=bedCost;
                        }
                    }else{
                        if(i>0){
                            if(wholeNum>0){
                                returnBedCost+=1;
                                wholeNum--;
                            }else if(halfNum>0){
                                returnBedCost+=(1*0.5);
                                halfNum--;
                            }else if(exemptionNum>0){
                                exemptionNum--;
                            }
                        }
                    }
                }

                //bedCost=bedCost*checkinNum;

                //bedCost=bedCost-((bedCost/checkinNum)*halfNum*0.5);
                //bedCost=bedCost-((bedCost/checkinNum)*exemptionNum);
                bedCost=returnBedCost;
            }


        }

        renderCodeSuccess("success",bedCost);
    }




    public void getBaseExamineNum2(){
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");

        String sql="select * from pers_org where parent_id in ('3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7','B97B6F94-140C-4C86-8BC1-6A2BCC86CA3A') and del_flag='0' " +
                " and is_enable='1'  ORDER BY parent_id,org_sort  ";

        List<Record> recordList=Db.find(sql);
        
        Map<String,List<String>> orgMap=new HashMap<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        for (Record record : recordList) {
            List<PersOrg> persOrgList=new ArrayList<>();
            persOrgService.findChildren(zTreeList,persOrgList,record.getStr("id"));

            List<String> idList=new ArrayList<>();
            idList.add(record.getStr("id"));
            for (PersOrg org : persOrgList) {
                idList.add(org.getId());
            }
            orgMap.put(record.getStr("id"),idList);
        }

        String examineRecordSql="select a.scheme_id,a.dept_id,COUNT(a.id) as count,SUM(score_rate)/COUNT(a.id) as avgScore from main_examine_record a where a.del_flag='0' and a.status='2'  ";

        List<String> params=new ArrayList<>();
        if(StrKit.notBlank(startDate) && StrKit.notBlank(endDate)){
            examineRecordSql+=" and a.start_time BETWEEN ? and ? ";
            params.add(startDate);
            params.add(endDate);
        }
        examineRecordSql+=" GROUP BY a.scheme_id,a.dept_id  ";
        List<Record> examineRecordList=Db.find(examineRecordSql,params.toArray());


        List<MainExamineScheme> examineSchemeList=mainExamineSchemeService.findExamineSchemeList();

        Map<String,String> examineSchemeNameMap=new HashMap<>();
        for (MainExamineScheme mainExamineScheme : examineSchemeList) {
            examineSchemeNameMap.put(mainExamineScheme.getId(),mainExamineScheme.getName());
        }


        List<Record> returnList=new ArrayList<>();

        List<Record> scoreList=new ArrayList<>();

        for (String orgId : orgMap.keySet()) {
            List<String> childrenIdList=orgMap.get(orgId);

            Map<String,Integer> countMap=new HashMap<>();

            double totalScore=0.0;
            int num=0;

            Map<String,Record> scoreMap=new HashMap<>();

            for (Record record : examineRecordList) {
                String deptId=record.getStr("dept_id").toUpperCase();
                String schemeId=record.getStr("scheme_id").toUpperCase();
                if(childrenIdList.contains(deptId)){
                    if(countMap.containsKey(schemeId)){
                        countMap.put(schemeId,countMap.get(schemeId)+record.getInt("count"));

                        Record soreRecord=scoreMap.get(schemeId);
                        soreRecord.set("num",soreRecord.getInt("num")+1);
                        soreRecord.set("score",soreRecord.getDouble("score")+record.getDouble("avgScore"));

                    }else{
                        countMap.put(schemeId,record.getInt("count"));

                        Record soreRecord=new Record();
                        soreRecord.set("num",1);
                        soreRecord.set("score",record.getDouble("avgScore"));
                        scoreMap.put(schemeId,soreRecord);
                    }
                }
            }
            for (MainExamineScheme mainExamineScheme : examineSchemeList) {
                if(!countMap.containsKey(mainExamineScheme.getId())){
                    countMap.put(mainExamineScheme.getId(),0);
                }
                if(!scoreMap.containsKey(mainExamineScheme.getId())){
                    scoreMap.put(mainExamineScheme.getId(),null);
                }
            }


            Record record=new Record();
            record.set("name",persOrgService.findById(orgId).getOrgName());
            for (String s : countMap.keySet()) {
                record.set(examineSchemeNameMap.get(s),countMap.get(s));
            }

            Record record2=new Record();
            record2.set("name",record.getStr("name"));
            for (String s : scoreMap.keySet()) {
                if(scoreMap.get(s)==null){
                    record2.set(examineSchemeNameMap.get(s),0.0);
                }else{
                    Record score=scoreMap.get(s);
                    record2.set(examineSchemeNameMap.get(s),new BigDecimal(score.getDouble("score")).divide(new BigDecimal(score.getDouble("num")),4,BigDecimal.ROUND_HALF_UP).doubleValue());
                }

            }

            returnList.add(record);
            scoreList.add(record2);
        }
        renderCodeSuccess("success",returnList);
    }

    public void getBaseExamineNum(){
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");
        String schemeIds=getPara("schemeIds");

        String sql="select * from pers_org where parent_id in ('3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7','B97B6F94-140C-4C86-8BC1-6A2BCC86CA3A') and del_flag='0' " +
                " and is_enable='1'  ORDER BY parent_id,org_sort  ";

        List<Record> recordList=Db.find(sql);

        Map<String,List<String>> orgMap=new HashMap<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        for (Record record : recordList) {
            List<PersOrg> persOrgList=new ArrayList<>();
            persOrgService.findChildren(zTreeList,persOrgList,record.getStr("id"));

            List<String> idList=new ArrayList<>();
            idList.add(record.getStr("id"));
            for (PersOrg org : persOrgList) {
                idList.add(org.getId());
            }
            orgMap.put(record.getStr("id"),idList);
        }

        String examineRecordSql="select a.scheme_id,a.dept_id,COUNT(a.id) as count,SUM(score_rate)/COUNT(a.id) as avgScore from main_examine_record a where a.del_flag='0' and a.status='2'  ";

        List<String> params=new ArrayList<>();
        if(StrKit.notBlank(startDate) && StrKit.notBlank(endDate)){
            examineRecordSql+=" and a.start_time BETWEEN ? and ? ";
            params.add(startDate);
            params.add(endDate);
        }
        examineRecordSql+=" GROUP BY a.scheme_id,a.dept_id  ";
        List<Record> examineRecordList=Db.find(examineRecordSql,params.toArray());


        List<MainExamineScheme> examineSchemeList=mainExamineSchemeService.findExamineSchemeList();

        Map<String,String> examineSchemeNameMap=new HashMap<>();
        for (MainExamineScheme mainExamineScheme : examineSchemeList) {
            examineSchemeNameMap.put(mainExamineScheme.getId(),mainExamineScheme.getName());
        }
        if(StrKit.notBlank(schemeIds)){
            JSONArray jsonArray=JSON.parseArray(schemeIds);
            if(jsonArray!=null && jsonArray.size()>0){
                examineSchemeNameMap.clear();
                for (MainExamineScheme mainExamineScheme : examineSchemeList) {
                    String upperCaseSchemeId=mainExamineScheme.getId().toUpperCase();
                    String lowerCaseSchemeId=mainExamineScheme.getId().toLowerCase();
                    if(jsonArray.contains(upperCaseSchemeId) || jsonArray.contains(lowerCaseSchemeId)){
                        examineSchemeNameMap.put(mainExamineScheme.getId(),mainExamineScheme.getName());
                    }
                }
            }
        }


        List<Record> returnList=new ArrayList<>();

        List<Record> scoreList=new ArrayList<>();

        for (String orgId : orgMap.keySet()) {
            List<String> childrenIdList=orgMap.get(orgId);

            Map<String,Integer> countMap=new HashMap<>();

            double totalScore=0.0;
            int num=0;

            Map<String,Record> scoreMap=new HashMap<>();

            for (Record record : examineRecordList) {
                String deptId=record.getStr("dept_id").toUpperCase();
                String schemeId=record.getStr("scheme_id").toUpperCase();
                if(childrenIdList.contains(deptId)){
                    if(countMap.containsKey(schemeId)){
                        countMap.put(schemeId,countMap.get(schemeId)+record.getInt("count"));

                        Record soreRecord=scoreMap.get(schemeId);
                        soreRecord.set("num",soreRecord.getInt("num")+1);
                        soreRecord.set("score",soreRecord.getDouble("score")+record.getDouble("avgScore"));

                    }else{
                        countMap.put(schemeId,record.getInt("count"));

                        Record soreRecord=new Record();
                        soreRecord.set("num",1);
                        soreRecord.set("score",record.getDouble("avgScore"));
                        scoreMap.put(schemeId,soreRecord);
                    }
                }
            }
            /*for (MainExamineScheme mainExamineScheme : examineSchemeList) {
                if(!countMap.containsKey(mainExamineScheme.getId())){
                    countMap.put(mainExamineScheme.getId(),0);
                }
                if(!scoreMap.containsKey(mainExamineScheme.getId())){
                    scoreMap.put(mainExamineScheme.getId(),null);
                }
            }*/
            for (String key : examineSchemeNameMap.keySet()) {
                if(!countMap.containsKey(key)){
                    countMap.put(key,0);
                }
                if(!scoreMap.containsKey(key)){
                    scoreMap.put(key,null);
                }
            }



            Record record=new Record();
            record.set("name",persOrgService.findById(orgId).getOrgName());
            for (String s : countMap.keySet()) {
                record.set(examineSchemeNameMap.get(s),countMap.get(s));
            }

            Record record2=new Record();
            record2.set("name",record.getStr("name"));
            for (String s : scoreMap.keySet()) {
                if(scoreMap.get(s)==null){
                    record2.set(examineSchemeNameMap.get(s),0.0);
                }else{
                    Record score=scoreMap.get(s);
                    record2.set(examineSchemeNameMap.get(s),new BigDecimal(score.getDouble("score")).divide(new BigDecimal(score.getDouble("num")),4,BigDecimal.ROUND_HALF_UP).doubleValue());
                }

            }

            returnList.add(record);
            scoreList.add(record2);
        }


        Map<String,Map<String,Object>> scoreMap=new HashMap<>();

        for (Record record : scoreList) {
            scoreMap.put(record.getStr("name"),record.getColumns());
        }

        for (Record record : returnList) {
            Map<String,Object> scoreObj=scoreMap.get(record.getStr("name"));
            double avgSocre=0.0;
            int avgNum=0;
            for (String columnName : record.getColumnNames()) {
                if(!columnName.equals("name")) {
                    int num=record.getInt(columnName);
                    double score=(double)scoreObj.get(columnName);
                    Record record1=new Record();
                    record1.set("num",num);
                    record1.set("score",score);
                    record.set(columnName,record1);
                    if(score>0){
                        avgSocre+=score;
                        avgNum+=1;
                    }

                }
            }
            if(avgNum==0){
                record.set("avgSocre",0);
            }else{
                record.set("avgSocre",avgSocre/avgNum);
            }


        }

        renderCodeSuccess("success",returnList);
    }

    public void getBaseRoomType(){
        String baseId=getPara("baseId");
        String name=getPara("name");
        if(StrKit.isBlank(baseId)){
            renderCodeFailed("参数缺失");
            return;
        }

        List<String> params=new ArrayList<>();
        params.add(baseId);
        String sql="select id,type_name as typeName,private_room_days as privateRoomDays,is_big_bed_room as isBigBedRoom from main_base_room_type where del_flag='0' and is_enable='0' and base_id=? ";

        if(StrKit.notBlank(name)){
            sql+=" and type_name=? ";
            params.add(name);
        }

        List<Record> recordList=Db.find(sql,params.toArray());
        for (Record record : recordList) {
            if(record.getDouble("privateRoomDays")==null){
                String roomId=Db.queryStr("select id from main_base_room where base_room_type=? and del_flag='0' and is_enable='0'  limit 1 ",record.getStr("id"));
                Integer bedNum=Db.queryInt("select count(id) from main_base_bed where del_flag='0' and is_enable='0' and room_id=? ",roomId);
                record.set("privateRoomDays",bedNum);
            }
        }
        renderCodeSuccess("success",recordList);
    }

    @Inject
    MainCardGiveSchemeRuleService mainCardGiveSchemeRuleService;
    @Inject
    FinaMembershipCardService finaMembershipCardService;

    public void testGive(){
        String cardId=getPara("cardId");
        FinaMembershipCard card=finaMembershipCardService.findById(cardId);
        renderJson(mainCardGiveSchemeRuleService.getCardGiveSchemeRuleResult(card.getOpenTime(),card.getGiveSchemeId()));
    }

    //获取银行卡卡类型，工商、建设等
    public void getBankCardTypeList(){
        renderCodeSuccess("success",JSON.parseArray("[{\"SRCB\":\"深圳农村商业银行\"},{\"BGB\":\"广西北部湾银行\"},{\"SHRCB\":\"上海农村商业银行\"},{\"BJBANK\":\"北京银行\"},{\"WHCCB\":\"威海市商业银行\"},{\"BOZK\":\"周口银行\"},{\"KORLABANK\":\"库尔勒市商业银行\"},{\"SPABANK\":\"平安银行\"},{\"SDEB\":\"顺德农商银行\"},{\"HURCB\":\"湖北省农村信用社\"},{\"WRCB\":\"无锡农村商业银行\"},{\"BOCY\":\"朝阳银行\"},{\"CZBANK\":\"浙商银行\"},{\"HDBANK\":\"邯郸银行\"},{\"BOC\":\"中国银行\"},{\"BOD\":\"东莞银行\"},{\"CCB\":\"中国建设银行\"},{\"ZYCBANK\":\"遵义市商业银行\"},{\"SXCB\":\"绍兴银行\"},{\"GZRCU\":\"贵州省农村信用社\"},{\"ZJKCCB\":\"张家口市商业银行\"},{\"BOJZ\":\"锦州银行\"},{\"BOP\":\"平顶山银行\"},{\"HKB\":\"汉口银行\"},{\"SPDB\":\"上海浦东发展银行\"},{\"NXRCU\":\"宁夏黄河农村商业银行\"},{\"NYNB\":\"广东南粤银行\"},{\"GRCB\":\"广州农商银行\"},{\"BOSZ\":\"苏州银行\"},{\"HZCB\":\"杭州银行\"},{\"HSBK\":\"衡水银行\"},{\"HBC\":\"湖北银行\"},{\"JXBANK\":\"嘉兴银行\"},{\"HRXJB\":\"华融湘江银行\"},{\"BODD\":\"丹东银行\"},{\"AYCB\":\"安阳银行\"},{\"EGBANK\":\"恒丰银行\"},{\"CDB\":\"国家开发银行\"},{\"TCRCB\":\"江苏太仓农村商业银行\"},{\"NJCB\":\"南京银行\"},{\"ZZBANK\":\"郑州银行\"},{\"DYCB\":\"德阳商业银行\"},{\"YBCCB\":\"宜宾市商业银行\"},{\"SCRCU\":\"四川省农村信用\"},{\"KLB\":\"昆仑银行\"},{\"LSBANK\":\"莱商银行\"},{\"YDRCB\":\"尧都农商行\"},{\"CCQTGB\":\"重庆三峡银行\"},{\"FDB\":\"富滇银行\"},{\"JSRCU\":\"江苏省农村信用联合社\"},{\"JNBANK\":\"济宁银行\"},{\"CMB\":\"招商银行\"},{\"JINCHB\":\"晋城银行JCBANK\"},{\"FXCB\":\"阜新银行\"},{\"WHRCB\":\"武汉农村商业银行\"},{\"HBYCBANK\":\"湖北银行宜昌分行\"},{\"TZCB\":\"台州银行\"},{\"TACCB\":\"泰安市商业银行\"},{\"XCYH\":\"许昌银行\"},{\"CEB\":\"中国光大银行\"},{\"NXBANK\":\"宁夏银行\"},{\"HSBANK\":\"徽商银行\"},{\"JJBANK\":\"九江银行\"},{\"NHQS\":\"农信银清算中心\"},{\"MTBANK\":\"浙江民泰商业银行\"},{\"LANGFB\":\"廊坊银行\"},{\"ASCB\":\"鞍山银行\"},{\"KSRB\":\"昆山农村商业银行\"},{\"YXCCB\":\"玉溪市商业银行\"},{\"DLB\":\"大连银行\"},{\"DRCBCL\":\"东莞农村商业银行\"},{\"GCB\":\"广州银行\"},{\"NBBANK\":\"宁波银行\"},{\"BOYK\":\"营口银行\"},{\"SXRCCU\":\"陕西信合\"},{\"GLBANK\":\"桂林银行\"},{\"BOQH\":\"青海银行\"},{\"CDRCB\":\"成都农商银行\"},{\"QDCCB\":\"青岛银行\"},{\"HKBEA\":\"东亚银行\"},{\"HBHSBANK\":\"湖北银行黄石分行\"},{\"WZCB\":\"温州银行\"},{\"TRCB\":\"天津农商银行\"},{\"QLBANK\":\"齐鲁银行\"},{\"GDRCC\":\"广东省农村信用社联合社\"},{\"ZJTLCB\":\"浙江泰隆商业银行\"},{\"GZB\":\"赣州银行\"},{\"GYCB\":\"贵阳市商业银行\"},{\"CQBANK\":\"重庆银行\"},{\"DAQINGB\":\"龙江银行\"},{\"CGNB\":\"南充市商业银行\"},{\"SCCB\":\"三门峡银行\"},{\"CSRCB\":\"常熟农村商业银行\"},{\"SHBANK\":\"上海银行\"},{\"JLBANK\":\"吉林银行\"},{\"CZRCB\":\"常州农村信用联社\"},{\"BANKWF\":\"潍坊银行\"},{\"ZRCBANK\":\"张家港农村商业银行\"},{\"FJHXBC\":\"福建海峡银行\"},{\"ZJNX\":\"浙江省农村信用社联合社\"},{\"LZYH\":\"兰州银行\"},{\"JSB\":\"晋商银行\"},{\"BOHAIB\":\"渤海银行\"},{\"CZCB\":\"浙江稠州商业银行\"},{\"YQCCB\":\"阳泉银行\"},{\"SJBANK\":\"盛京银行\"},{\"XABANK\":\"西安银行\"},{\"BSB\":\"包商银行\"},{\"JSBANK\":\"江苏银行\"},{\"FSCB\":\"抚顺银行\"},{\"HNRCU\":\"河南省农村信用\"},{\"COMM\":\"交通银行\"},{\"XTB\":\"邢台银行\"},{\"CITIC\":\"中信银行\"},{\"HXBANK\":\"华夏银行\"},{\"HNRCC\":\"湖南省农村信用社\"},{\"DYCCB\":\"东营市商业银行\"},{\"ORBANK\":\"鄂尔多斯银行\"},{\"BJRCB\":\"北京农村商业银行\"},{\"XYBANK\":\"信阳银行\"},{\"ZGCCB\":\"自贡市商业银行\"},{\"CDCB\":\"成都银行\"},{\"HANABANK\":\"韩亚银行\"},{\"CMBC\":\"中国民生银行\"},{\"LYBANK\":\"洛阳银行\"},{\"GDB\":\"广东发展银行\"},{\"ZBCB\":\"齐商银行\"},{\"CBKF\":\"开封市商业银行\"},{\"H3CB\":\"内蒙古银行\"},{\"CIB\":\"兴业银行\"},{\"CRCBANK\":\"重庆农村商业银行\"},{\"SZSBK\":\"石嘴山银行\"},{\"DZBANK\":\"德州银行\"},{\"SRBANK\":\"上饶银行\"},{\"LSCCB\":\"乐山市商业银行\"},{\"JXRCU\":\"江西省农村信用\"},{\"ICBC\":\"中国工商银行\"},{\"JZBANK\":\"晋中市商业银行\"},{\"HZCCB\":\"湖州市商业银行\"},{\"NHB\":\"南海农村信用联社\"},{\"XXBANK\":\"新乡银行\"},{\"JRCB\":\"江苏江阴农村商业银行\"},{\"YNRCC\":\"云南省农村信用社\"},{\"ABC\":\"中国农业银行\"},{\"GXRCU\":\"广西省农村信用\"},{\"PSBC\":\"中国邮政储蓄银行\"},{\"BZMD\":\"驻马店银行\"},{\"ARCU\":\"安徽省农村信用社\"},{\"GSRCU\":\"甘肃省农村信用\"},{\"LYCB\":\"辽阳市商业银行\"},{\"JLRCU\":\"吉林农信\"},{\"URMQCCB\":\"乌鲁木齐市商业银行\"},{\"XLBANK\":\"中山小榄村镇银行\"},{\"CSCB\":\"长沙银行\"},{\"JHBANK\":\"金华银行\"},{\"BHB\":\"河北银行\"},{\"NBYZ\":\"鄞州银行\"},{\"LSBC\":\"临商银行\"},{\"BOCD\":\"承德银行\"},{\"SDRCU\":\"山东农信\"},{\"NCB\":\"南昌银行\"},{\"TCCB\":\"天津银行\"},{\"WJRCB\":\"吴江农商银行\"},{\"CBBQS\":\"城市商业银行资金清算中心\"},{\"HBRCU\":\"河北省农村信用社\"}]"));
    }

    public void getBaseBedTree(){
        String baseId = getPara("baseId");
        if(StrKit.isBlank(baseId)){
            renderCodeFailed("参数缺失");
            return;
        }
        List<Record> recordList=Db.find("select b.id as building_id,b.building_name,c.id as floor_id,c.floor_name,d.id as room_id,d.room_name,e.id as bed_id,e.bed_name  " +
                " from main_base_building b" +
                " inner join main_base_floor c on b.id=c.building_id " +
                "inner join main_base_room d on d.floor_id=c.id " +
                "inner join main_base_bed e on e.room_id=d.id " +
                "where b.del_flag='0' and c.del_flag='0' and d.del_flag='0' and e.del_flag='0'  " +
                "and b.is_enable='0' and c.is_enable='0' and d.is_enable='0' and e.is_enable='0' " +
                "and b.base_id=? " +
                "ORDER BY building_name,floor_name,room_name,bed_name ",baseId);
        List<ZTree> zTreeList=new ArrayList<>();

        Set<String> idSet=new HashSet<>();
        for (Record record : recordList) {
            if(idSet.contains(record.getStr("building_id"))){
                continue;
            }
            zTreeList.add(new ZTree(record.getStr("building_id"),record.getStr("building_name"),"building",baseId,null));
            idSet.add(record.getStr("building_id"));
        }
        for (Record record : recordList) {
            if(idSet.contains(record.getStr("floor_id"))){
                continue;
            }
            zTreeList.add(new ZTree(record.getStr("floor_id"),record.getStr("floor_name"),"floor",record.getStr("building_id"),null));
            idSet.add(record.getStr("floor_id"));
        }
        for (Record record : recordList) {
            if(idSet.contains(record.getStr("room_id"))){
                continue;
            }
            zTreeList.add(new ZTree(record.getStr("room_id"),record.getStr("room_name"),"room",record.getStr("floor_id"),null));
            idSet.add(record.getStr("room_id"));
        }
        for (Record record : recordList) {
            if(idSet.contains(record.getStr("bed_id"))){
                continue;
            }
            zTreeList.add(new ZTree(record.getStr("bed_id"),record.getStr("bed_name"),"bed",record.getStr("room_id"),null));
            idSet.add(record.getStr("bed_id"));
        }

        List<ZTree> returnZtreeList=new ArrayList<>();
        for (ZTree zTree : zTreeList) {
            if(baseId.equalsIgnoreCase(zTree.getpId())){
                returnZtreeList.add(zTree);
            }
            for(ZTree z:zTreeList){
                if(z.getpId().equalsIgnoreCase(zTree.getId())){
                    if(zTree.getChildren()==null){
                        List<ZTree> childrenZtreeList=new ArrayList<>();
                        childrenZtreeList.add(z);
                        zTree.setChildren(childrenZtreeList);
                    }else{
                        zTree.getChildren().add(z);
                    }
                }
            }
        }
        renderCodeSuccess("success",returnZtreeList);
    }

    /**
     * 获取所有用户组列表
     */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getUserGroupPage(){
        MainUserGroup userGroup = getBean(MainUserGroup.class,"",true);
        Page<MainUserGroup> page = mainUserGroupService.findPageList(getParaToInt("page", 1),getParaToInt("limit", 10),userGroup);
        renderCodeSuccess("success",new DataTable<MainUserGroup>(page));
    }

    public void taskCallback() {
        String jsonStr = HttpKit.readData(getRequest());

        if (StrKit.isBlank(jsonStr) || !jsonStr.startsWith("{") || !jsonStr.endsWith("}")) {
            renderCodeFailed("参数不能为空");
            return;
        }
        logger.info("流程回调参数：" + jsonStr);
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        String taskId = jsonObject.getString("TaskId");
        String processNo=jsonObject.getString("ProcessNo");

        boolean flag=false;
        if("已完成".equals(jsonObject.getString("CurrentStep"))){
            if(TaskType.mallProductSale.getTaskNo().equals(processNo)){
                MainMallProductSaleApply productSaleApply = mainMallProductSaleApplyService.findByTaskId(taskId);
                if("3".equals(productSaleApply.getStatus())){
                    renderCodeSuccess("success");
                    return;
                }
                productSaleApply.setStatus("3");
                productSaleApply.setUpdateDate(new Date());
                flag=productSaleApply.update();
                if(flag){
                    mainMallProductSaleApplyService.taskCompleted(productSaleApply.getId());
                }
            }
        }else if("Aborted".equalsIgnoreCase(jsonObject.getString("CurrentStatus"))){
            if(TaskType.mallProductSale.getTaskNo().equals(processNo)){
                MainMallProductSaleApply productSaleApply = mainMallProductSaleApplyService.findByTaskId(taskId);
                productSaleApply.setStatus("5");
                productSaleApply.setUpdateDate(new Date());
                flag=productSaleApply.update();
            }
        }
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed();
        }
    }

    public void getDictByType(){
        String type = getPara("type");

        List<Dict> dictList = dictService.getListByTypeOnUse(type);
        renderCodeSuccess("success",dictList);
    }

}

