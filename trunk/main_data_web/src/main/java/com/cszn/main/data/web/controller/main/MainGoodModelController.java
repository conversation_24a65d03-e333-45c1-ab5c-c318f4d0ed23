package com.cszn.main.data.web.controller.main;

import java.util.List;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainDictionariesService;
import com.cszn.integrated.service.api.main.MainGoodModelsService;
import com.cszn.integrated.service.api.main.MainGoodTypesService;
import com.cszn.integrated.service.entity.main.MainDictionaries;
import com.cszn.integrated.service.entity.main.MainGoodModels;
import com.cszn.integrated.service.entity.main.MainGoodTypes;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/main/goodModel",viewPath = "/modules_page/main/goodModel/")
public class MainGoodModelController extends BaseController {

    @Inject
    private MainGoodModelsService mainGoodModelsService;
    @Inject
    private MainDictionariesService mainDictionariesService;
    @Inject
    private MainGoodTypesService mainGoodTypesService;

    public void index(){
        render("goodModelIndex.html");
    }

    public void pageList(){
        MainGoodModels model=getBean(MainGoodModels.class,"",true);
        Page<Record> recordPage=mainGoodModelsService.pageList(getParaToInt("page"),getParaToInt("limit"),model);

        renderJson(new DataTable<Record>(recordPage));
    }

    public void form(){
        String typeId=getPara("typeId");
        String typeName=getPara("typeName");
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainGoodModels model=mainGoodModelsService.findById(id);
            setAttr("model",model);
        }
        MainGoodTypes type=mainGoodTypesService.findById(typeId);
        if(type!=null){
            List<MainDictionaries> dictionaries=mainDictionariesService.findDictionariesByTypeId(type.getUnitTypeId());
            setAttr("dictionaries",dictionaries);
        }
        setAttr("typeId",typeId);
        setAttr("typeName",typeName);
        render("goodModelForm.html");
    }

    public void saveGoodModel(){
        MainGoodModels model=getBean(MainGoodModels.class,"",true);

        boolean flag=mainGoodModelsService.saveGoodModel(model, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
