package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.equi.EquiModelService;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.entity.enums.BaseBuildingFloorChannelType;
import com.cszn.integrated.service.entity.enums.LockType;
import com.cszn.integrated.service.entity.enums.MaintainState;
import com.cszn.integrated.service.entity.equi.EquiModel;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Cell;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@RequestMapping(value = "/main/buildingRoomManage",viewPath = "/modules_page/main/buildingRoomManage")
public class BuildingRoomManage extends BaseController {

    private static Logger logger= LoggerFactory.getLogger(BuildingRoomManage.class);

    @Inject
    private MainBaseBuildingService mainBaseBuildingService;
    @Inject
    private MainBaseFloorService mainBaseFloorService;
    @Inject
    private MainBaseRoomService mainBaseRoomService;
    @Inject
    private MainBaseBedService mainBaseBedService;
    @Inject
    private MainBedTypeService mainBedTypeService;
    @Inject
    private MainBedStatusService mainBedStatusService;
    @Inject
    private MainRoomTypeService mainRoomTypeService;
    @Inject
    private MainBaseBedMarkupTypeService mainBaseBedMarkupTypeService;
    @Inject
    private MainMattressTypeService mainMattressTypeService;
    @Inject
    private MainBookChannelService mainBookChannelService;
    @Inject
    private MainRoomChannelService mainRoomChannelService;
    @Inject
    private MainBedChannelService mainBedChannelService;
    @Inject
    private MainBaseBuildingFloorChannelService mainBaseBuildingFloorChannelService;
    @Inject
    private MainBaseBedDynamicRecordService mainBaseBedDynamicRecordService;
    @Inject
    private MainBedEquiModelService mainBedEquiModelService;
    @Inject
    private EquiModelService equiModelService;
    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private MainBaseRoomTypeService mainBaseRoomTypeService;


    public void index(){
        render("buildingRoomManageIndex.html");
    }

    @Clear(LogInterceptor.class)
    public void getBuildingPage(){
        String baseId=getPara("baseId");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");

        Page<MainBaseBuilding> buildingPage=mainBaseBuildingService.getBuildingPageByBaseId(baseId,pageNumber,pageSize);
        renderJson(new DataTable<MainBaseBuilding>(buildingPage));
    }

    @Clear(LogInterceptor.class)
    public void getFloorPage(){
        String buildingId=getPara("buildingId");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");

        Page<MainBaseFloor> floorPage=mainBaseFloorService.getFloorPageByBuildingId(buildingId,pageNumber,pageSize);
        renderJson(new DataTable<MainBaseFloor>(floorPage));
    }

    @Clear(LogInterceptor.class)
    public void getRoomPage(){
        String floorId=getPara("floorId");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");

        Page<Record> roomPage=mainBaseRoomService.getRoomPageByFloorId(floorId,pageNumber,pageSize);
        renderJson(new DataTable<Record>(roomPage));
    }

    @Clear(LogInterceptor.class)
    public void getBedPage(){
        String roomId=getPara("roomId");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        Page<Record> bedPage=mainBaseBedService.getBedPageByRoomIds(new String[]{roomId},pageNumber,pageSize);
        renderJson(new DataTable<Record>(bedPage));
    }

    public void getBuildingList(){
        String baseId=getPara("baseId");
        if(StrKit.isBlank(baseId)){
            renderJson(Ret.fail());
            return;
        }
        List<MainBaseBuilding> buildingList=mainBaseBuildingService.getBuildingListByBaseIds(new String[]{baseId});
        renderJson(Ret.ok("data",buildingList));
    }

    public void getFloorList(){
        String buildingId=getPara("buildingId");
        if(StrKit.isBlank(buildingId)){
            renderJson(Ret.fail());
            return;
        }
        List<MainBaseFloor> floorList=mainBaseFloorService.getFloorListByBuildingIds(new String[]{buildingId});
        renderJson(Ret.ok("data",floorList));
    }

    public void buildingForm(){
        String buildingId=getPara("buildingId");
        String baseId=getPara("baseId");
        if(StringUtils.isNotBlank(buildingId)){
            MainBaseBuilding building=mainBaseBuildingService.findById(buildingId);
            setAttr("building",building);
            List<MainBaseBuildingFloorChannel> channelList=mainBaseBuildingFloorChannelService.findChannelByTypeAndJoinId(BaseBuildingFloorChannelType.building.getKey(),buildingId);
            String buildingChannelIds="";
            for(MainBaseBuildingFloorChannel channel : channelList){
                buildingChannelIds+=channel.getChannelId()+",";
            }
            if(buildingChannelIds.indexOf(",")!=-1){
                buildingChannelIds=buildingChannelIds.substring(0,buildingChannelIds.length()-1);
            }
            setAttr("buildingChannelIds",buildingChannelIds);
        }
        List<MainBookChannel> bookChannelList=mainBookChannelService.findBookChannelList();
        setAttr("bookChannelList",bookChannelList);
        setAttr("baseId",baseId);
        setAttr("commonUpload", Global.commonUpload);
        render("baseBuildingForm.html");
    }

    public void floorForm(){
        String floorId=getPara("floorId");
        String buildingId=getPara("buildingId");
        if(StringUtils.isNotBlank(floorId)){
            MainBaseFloor floor=mainBaseFloorService.findById(floorId);
            setAttr("floor",floor);
            List<MainBaseBuildingFloorChannel> channelList=mainBaseBuildingFloorChannelService.findChannelByTypeAndJoinId(BaseBuildingFloorChannelType.floor.getKey(),floorId);
            String floorChannelIds="";
            for(MainBaseBuildingFloorChannel channel : channelList){
                floorChannelIds+=channel.getChannelId()+",";
            }
            if(floorChannelIds.indexOf(",")!=-1){
                floorChannelIds=floorChannelIds.substring(0,floorChannelIds.length()-1);
            }
            setAttr("floorChannelIds",floorChannelIds);
        }
        List<MainBookChannel> bookChannelList=mainBookChannelService.findBookChannelList();
        setAttr("bookChannelList",bookChannelList);
        setAttr("commonUpload", Global.commonUpload);
        setAttr("buildingId",buildingId);
        render("baseFloorForm.html");
    }

    public void roomForm(){
        String roomId=getPara("roomId");
        String buildingId=getPara("buildingId");
        String floorId=getPara("floorId");
        String baseId=getPara("baseId");
        List<MainRoomType> roomTypeList=mainRoomTypeService.findRoomTypeByBaseId(baseId);
        if(StringUtils.isNotBlank(roomId)){
            MainBaseRoom room=mainBaseRoomService.findById(roomId);
            room.put("lock_type",room.getLockType()+"");
            setAttr("room",room);
            List<MainRoomChannel> roomChannelList=mainRoomChannelService.roomChannelList(roomId);
            if(roomChannelList.size()>0){
                String roomChannelIds="";
                for(MainRoomChannel roomChannel:roomChannelList){
                    roomChannelIds+=roomChannel.getChannelId()+",";
                }
                if(roomChannelIds.indexOf(",")!=-1){
                    roomChannelIds=roomChannelIds.substring(0,roomChannelIds.length()-1);
                }
                set("roomChannelIds",roomChannelIds);
            }
        }
        Map<String,String> lockType=new HashMap<>();
        for(LockType type:LockType.values()){
            lockType.put(type.getKey(),type.getValue());
        }
        List<MainBookChannel> bookChannelList=mainBookChannelService.findBookChannelList();

        Map<String,String> maintainStateMap=new HashMap<>();
        for(MaintainState state: MaintainState.values()){
            maintainStateMap.put(state.name(),state.getValue());
        }
        List<MainBaseBedMarkupType> bedMarkupTypeList=mainBaseBedMarkupTypeService.findBedMarkupTypeList();
        setAttr("bedMarkupTypeList",bedMarkupTypeList);
        setAttr("baseRoomTypeList",mainBaseRoomTypeService.getBaseRoomTypeList(baseId));
        setAttr("maintainStateMap",maintainStateMap);
        setAttr("commonUpload", Global.commonUpload);
        setAttr("buildingId",buildingId);
        setAttr("floorId",floorId);
        setAttr("roomTypeList",roomTypeList);
        setAttr("bookChannelList",bookChannelList);
        setAttr("lockType",lockType);
        render("baseRoomForm.html");
    }

    public void bedForm(){
        String bedId=getPara("bedId");
        String roomId=getPara("roomId");
        String baseId=getPara("baseId");
        List<MainBedStatus> bedStatusList=mainBedStatusService.findBedStatusByBaseId();
        List<MainBedType> baseTypeList=mainBedTypeService.findBedTypeListByBaseId();
        List<MainBaseBedMarkupType> bedMarkupTypeList=mainBaseBedMarkupTypeService.findBedMarkupTypeList();
        if(StringUtils.isNotBlank(bedId)){
            MainBaseBed bed=mainBaseBedService.findById(bedId);

            List<MainBedChannel> bedChannelList=mainBedChannelService.bedChannelList(bedId);
            if(bedChannelList.size()>0){
                String bedChannelIds="";
                for(MainBedChannel bedChannel:bedChannelList){
                    bedChannelIds+=bedChannel.getChannelId()+",";
                }
                if(bedChannelIds.indexOf(",")!=-1){
                    bedChannelIds=bedChannelIds.substring(0,bedChannelIds.length()-1);
                }
                set("bedChannelIds",bedChannelIds);
            }
            setAttr("bed",bed);
        }
        List<MainMattressType> mainMattressTypeList=mainMattressTypeService.findAllMattressType();
        List<MainBookChannel> bookChannelList=mainBookChannelService.findBookChannelList();
        setAttr("commonUpload", Global.commonUpload);
        setAttr("roomId",roomId);
        setAttr("bedStatusList",bedStatusList);
        setAttr("baseTypeList",baseTypeList);
        setAttr("bedMarkupTypeList",bedMarkupTypeList);
        setAttr("mattressTypeList",mainMattressTypeList);
        setAttr("bookChannelList",bookChannelList);
        render("baseBedForm.html");
    }

    public void saveBuilding(){
        MainBaseBuilding building=getBean(MainBaseBuilding.class,"",true);
        String bookChannelIdStr=getPara("bookChannel");
        if(StringUtils.isBlank(building.getBaseId())){
            renderJson(Ret.fail("msg","基地Id不能为空"));
            return;
        }
        if(mainBaseBuildingService.buildingIsExist(building)!=null){
            renderJson(Ret.fail("msg","该基地下该楼栋名称或显示名称已存在"));
            return;
        }
        building.setUpdateBy(AuthUtils.getUserId());
        building.setCreateBy(AuthUtils.getUserId());
        boolean flag=mainBaseBuildingService.saveBuilding(building);

        if(flag){
            mainBaseBuildingFloorChannelService.saveBaseBuildingFloorChannel(BaseBuildingFloorChannelType.building.getKey(),building.getId(),bookChannelIdStr,AuthUtils.getUserId());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveFloor(){
        MainBaseFloor floor=getBean(MainBaseFloor.class,"",true);
        String bookChannelIdStr=getPara("bookChannel");
        if(StringUtils.isBlank(floor.getBuildingId())){
            renderJson(Ret.fail("msg","楼栋Id不能为空"));
            return;
        }
        if(mainBaseFloorService.floorIsExist(floor)!=null){
            renderJson(Ret.fail("msg","该楼栋下该楼层名称或楼层编号已存在"));
            return;
        }

        floor.setUpdateBy(AuthUtils.getUserId());
        floor.setCreateBy(AuthUtils.getUserId());
        boolean flag=mainBaseFloorService.saveFloor(floor);
        if(flag){
            mainBaseBuildingFloorChannelService.saveBaseBuildingFloorChannel(BaseBuildingFloorChannelType.floor.getKey(),floor.getId(),bookChannelIdStr,AuthUtils.getUserId());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveRoom(){
        MainBaseRoom room=getBean(MainBaseRoom.class,"",true);
        String isGenBed=getPara("isGenBed");
        Integer bedCount=getParaToInt("bedCount");
        String bookChannelIdStr=getPara("bookChannel");
        if(StringUtils.isBlank(room.getBuildingId())){
            renderJson(Ret.fail("msg","楼栋Id不能为空"));
            return;
        }
        if(StringUtils.isBlank(room.getFloorId())){
            renderJson(Ret.fail("msg","楼层Id不能为空"));
            return;
        }
        if(mainBaseRoomService.roomIsExist(room)!=null){
            renderJson(Ret.fail("msg","该楼层下该房间编号或房间名称已存在"));
            return;
        }
        if(AuthUtils.getLoginUser()!=null) {
            room.setUpdateBy(AuthUtils.getUserId());
            room.setCreateBy(AuthUtils.getUserId());
        }

        MainRoomType roomType=mainRoomTypeService.findById(room.getRoomType());
        if(roomType==null){
            renderJson(Ret.fail("msg","房间类型不能为空"));
            return;
        }
        if(StrKit.isBlank(roomType.getSecondLevelId())){
            renderJson(Ret.fail("msg","所选房间类型的二级分类未设置，请先设置"));

            return;
        }

        boolean isSave=StrKit.isBlank(room.getId());

        boolean flag=mainBaseRoomService.saveRoom(room);
        if(flag){
            if(AuthUtils.getLoginUser()==null){
                mainRoomChannelService.saveRoomChannel(room.getId(),bookChannelIdStr,null);
            }else{
                mainRoomChannelService.saveRoomChannel(room.getId(),bookChannelIdStr,AuthUtils.getUserId());
            }

        }
        if(flag){
            renderJson(Ret.ok("msg","操作成功").set("data",room.getId()));
            //自动床位
            if("1".equals(isGenBed) && isSave){
                if(room.getPeoples()!=null){
                    String bedBookChannelIdStr="03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7";
                    for (Integer i = 0; i < bedCount; i++) {
                        MainBaseBed bed=getBean(MainBaseBed.class,"",true);
                        bed.setBedName(room.getRoomName()+"-"+(i+1));
                        bed.setBedNo(room.getRoomName()+"-"+(i+1));
                        bed.setRoomId(room.getId());
                        bed.setBedTypeId("676450A3-D51E-4442-BCB1-2F86B6287BD2");
                        bed.setBedStatusId("3E541B4D-2BA2-4993-90EE-86C39353ACF9");
                        bed.setMarkupTypeId("8C6D8370-48DB-4A39-9AD0-1A1BB6977A69");
                        bed.setIsEnable("0");
                        bed.setMattressTypeId("80030260-A06F-44B6-9BDA-513777106767");
                        bed.setCreateBy(room.getCreateBy());
                        bed.setUpdateBy(room.getCreateBy());
                        if(mainBaseBedService.saveBed(bed)){
                            mainBedChannelService.saveBedChannel(bed.getId(),bookChannelIdStr,bed.getCreateBy());
                        }
                    }
                }
            }
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveBed(){
        MainBaseBed bed=getBean(MainBaseBed.class,"",true);
        String bookChannelIdStr=getPara("bookChannel");
        if(StringUtils.isBlank(bed.getRoomId())){
            renderJson(Ret.fail("msg","楼层Id不能为空"));
            return;
        }
        if(mainBaseBedService.bedIsExist(bed)!=null){
            renderJson(Ret.fail("msg","该房间下该床位编号或床位名称已存在"));
            return;
        }
        if(AuthUtils.getLoginUser()!=null){
            bed.setUpdateBy(AuthUtils.getUserId());
            bed.setCreateBy(AuthUtils.getUserId());
        }

        boolean flag=mainBaseBedService.saveBed(bed);
        if(flag){
            if(AuthUtils.getLoginUser()==null){
                mainBedChannelService.saveBedChannel(bed.getId(),bookChannelIdStr,null);
            }else{
                mainBedChannelService.saveBedChannel(bed.getId(),bookChannelIdStr,AuthUtils.getUserId());
            }
        }
        if(flag){
            renderJson(Ret.ok("msg","操作成功").set("data",bed.getId()));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    public void buildingDel(){
        String id=getPara("id");
        if(StringUtils.isBlank(id)){
            renderJson(Ret.fail("msg","床位id不能为空"));
            return;
        }
        Integer floorCount=mainBaseFloorService.queryFloorCountByBuildingId(id);
        if(floorCount>0){
            renderJson(Ret.fail("msg","该楼栋下还存在楼层，不能作废"));
            return;
        }
        boolean flag=mainBaseBuildingService.delBuilding(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","作废成功"));
        }else{
            renderJson(Ret.fail("msg","作废失败"));
        }
    }

    public void floorDel(){
        String id=getPara("id");
        if(StringUtils.isBlank(id)){
            renderJson(Ret.fail("msg","床位id不能为空"));
            return;
        }
        Integer roomCount=mainBaseRoomService.queryRoomCountByFloorId(id);
        if(roomCount>0){
            renderJson(Ret.fail("msg","该楼层下还存在房间，不能作废"));
            return;
        }
        boolean flag=mainBaseFloorService.delFloor(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","作废成功"));
        }else{
            renderJson(Ret.fail("msg","作废失败"));
        }
    }

    public void roomDel(){
        String id=getPara("id");

        if(StringUtils.isBlank(id)){
            renderJson(Ret.fail("msg","房间id不能为空"));
            return;
        }
        Integer bedCount=mainBaseBedService.queryBedCountByRoomId(id);
        if(bedCount>0){
            renderJson(Ret.fail("msg","该房间下还存在床位，不能作废"));
            return;
        }
        boolean flag=mainBaseRoomService.delRoom(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","作废成功"));
        }else{
            renderJson(Ret.fail("msg","作废失败"));
        }
    }

    public void bedDel(){
        String id=getPara("id");
        if(StringUtils.isBlank(id)){
            renderJson(Ret.fail("msg","床位id不能为空"));
            return;
        }
        String[] idArray={id};
        List<MainBaseBed> bedList=mainBaseBedService.findCheckinBedByIds(idArray);
        if(bedList.size()>0){
            StringBuilder sb=new StringBuilder();
            for(MainBaseBed bed:bedList){
                sb.append(bed.getBedName()+",");
            }
            String bedNames=sb.substring(0,sb.length()-1);
            renderJson(Ret.fail("msg","["+bedNames+"]的床位状态为入住状态，不能删除"));
            return;
        }
        //判断是否有预定信息
        Map<String,Object> resultMap=mainBaseBedDynamicRecordService.longStayIsBooking(id, DateUtils.getDate());
        if(!(boolean) resultMap.get("flag")){
            renderJson(Ret.fail("msg",resultMap.get("msg")));
            return;
        }
        boolean flag=mainBaseBedService.delBed(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","作废成功"));
        }else{
            renderJson(Ret.fail("msg","作废失败"));
        }
    }

    /**
     * 批量删除房间
     */
    public void batchDelRoom(){
        String ids=getPara("ids");
        if(StrKit.isBlank(ids) || !ids.startsWith("[") || !ids.endsWith("]")){
            renderJson(Ret.fail("msg","作废失败"));
            return;
        }
        JSONArray array= JSON.parseArray(ids);
        if(array==null || array.size()==0){
            renderJson(Ret.fail("msg","作废失败"));
            return;
        }
        String[] idArray=new String[array.size()];
        array.toArray(idArray);
        List<MainBaseBed> bedList=mainBaseBedService.findBedListByRoomIds(idArray);
        if(bedList.size()>0){
            renderJson(Ret.fail("msg","所选中的房间中还存在床位，请检查后再操作"));
            return;
        }
        boolean flag=mainBaseRoomService.batchDelRoom(idArray,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 批量删除床位
     */
    public void batchDelBed(){
        String ids=getPara("ids");
        if(StrKit.isBlank(ids) || !ids.startsWith("[") || !ids.endsWith("]")){
            renderJson(Ret.fail("msg","作废失败"));
            return;
        }
        JSONArray array= JSON.parseArray(ids);
        String[] idArray=new String[array.size()];
        array.toArray(idArray);
        //判断是否存在入住中的床位
        List<MainBaseBed> bedList=mainBaseBedService.findCheckinBedByIds(idArray);
        if(bedList.size()>0){
            StringBuilder sb=new StringBuilder();
            for(MainBaseBed bed:bedList){
                sb.append(bed.getBedName()+",");
            }
            String bedNames=sb.substring(0,sb.length()-1);
            renderJson(Ret.fail("msg","["+bedNames+"]的床位状态为入住状态，不能删除"));
            return;
        }
        for(String id : idArray){
            //判断是否有预定信息
            Map<String,Object> resultMap=mainBaseBedDynamicRecordService.longStayIsBooking(id, DateUtils.getDate());
            if(!(boolean) resultMap.get("flag")){
                renderJson(Ret.fail("msg","批量删除的床位中存在被预定的床位，不能删除"));
                return;
            }
        }

        boolean flag=mainBaseBedService.batchDelBed(array,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    public void bedEquiIndex(){
        String bedId=getPara("bedId");
        String buildingId=getPara("buildingId");
        String floorId=getPara("floorId");
        String roomId=getPara("roomId");

        List<EquiModel> modelList=equiModelService.equiModelUnboundList(null);

        List<MainBedEquiModel> bedEquiModelList=mainBedEquiModelService.findBedEquiModelListByBedId(bedId);

        for(MainBedEquiModel bedEquiModel:bedEquiModelList){
            EquiModel equiModel=equiModelService.findById(bedEquiModel.getEquiModelId());

            List<EquiModel> equiModelList=new ArrayList<>();
            equiModelList.add(equiModel);
            equiModelList.addAll(modelList);

            bedEquiModel.setEquiModelList(equiModelList);
        }

        setAttr("modelList",modelList);
        setAttr("bedEquiModelList",bedEquiModelList);

        setAttr("bedId",bedId);
        setAttr("buildingId",buildingId);
        setAttr("floorId",floorId);
        setAttr("roomId",roomId);

        render("bedEqui/bedEquiIndex.html");
    }

    public void bedEquiForm(){
        String bedId=getPara("bedId");
        String id=getPara("id");
        if(StrKit.notBlank(id)){

            MainBedEquiModel equiModel=mainBedEquiModelService.findById(id);

            setAttr("equiModel",equiModel);
        }

        List<EquiModel> modelList=equiModelService.equiModelList("single");

        setAttr("bedId",bedId);
        setAttr("modelList",modelList);

        render("bedEqui/bedEquiForm.html");
    }

    public void bedEquiPageList(){
        String bedId=getPara("bedId");
        Integer page=getParaToInt("page");
        Integer limit=getParaToInt("limit");

        Page<Record> recordPage=mainBedEquiModelService.bedEquiPageList(page,limit,bedId);

        renderJson(new DataTable<Record>(recordPage));
    }

    public void saveBedEqui(){
        Integer paramCount=getParaToInt("doorCount");
        String bedId=getPara("bedId");
        String roomId=getPara("roomId");
        String floorId=getPara("floorId");
        String buildingId=getPara("buildingId");
        String userId=getPara("userId");
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }

        List<MainBedEquiModel> equiModelAddList=new ArrayList<>();
        List<MainBedEquiModel> equiModelUpdateList=new ArrayList<>();

        for(int i=0;i<=paramCount;i++){
            MainBedEquiModel equiModel=getBean(MainBedEquiModel.class,"roomDoorList["+i+"]",true);
            if(StrKit.notBlank(equiModel.getEquiModelId())){
                if(StrKit.isBlank(equiModel.getId())){
                    equiModel.setId(IdGen.getUUID());
                    equiModel.setBuildingId(buildingId);
                    equiModel.setFloorId(floorId);
                    equiModel.setRoomId(roomId);
                    equiModel.setBedId(bedId);
                    equiModel.setDelFlag("0");
                    equiModel.setUpdateBy(userId);
                    equiModel.setUpdateDate(new Date());
                    equiModel.setCreateBy(userId);
                    equiModel.setCreateDate(new Date());
                    equiModelAddList.add(equiModel);
                }else{
                    equiModel.setUpdateDate(new Date());
                    equiModel.setUpdateBy(userId);
                    equiModelUpdateList.add(equiModel);
                }

            }

        }

        List<MainBedEquiModel> allList=new ArrayList<>();
        allList.addAll(equiModelAddList);
        allList.addAll(equiModelUpdateList);
        MainBaseBuilding building=mainBaseBuildingService.findById(buildingId);
        MainBase base=mainBaseService.findById(building.getBaseId());
        Map<String,String> hearders=new HashMap<>();
        hearders.put("Content-Type","application/json");
        for(MainBedEquiModel equiModel:allList){
            EquiModel eq=equiModelService.findById(equiModel.getEquiModelId());
            String data="{\"token\":{\"appId\":\""+base.getSleepaceAppId()+"\",\"secureKey\":\""+base.getSleepaceSecureKey()
                    +"\"},\"data\":{\"deviceId\":\""+eq.getInfoId()+"\",\"userId\":\""+bedId+"\",\"leftRight\":0,\"gender\":1,\"timezone\":28800,\"age\":28}}";
            String resultStr=HttpClientsUtils.httpPostRaw(base.getSleepaceUrl(),data,null,null);

            Integer sleepaceReportTime=base.getSleepaceReportTime();
            if(sleepaceReportTime==null){
                sleepaceReportTime=8;
            }
            String reportUploadTimeData="{\"token\":{\"appId\":\""+base.getSleepaceAppId()+"\",\"secureKey\":\""+base.getSleepaceSecureKey()
                    +"\"},\"data\":{\"deviceId\":\""+eq.getInfoId()+"\",\"reportUploadTime\":"+sleepaceReportTime+"}}";
            String reportUploadTimeResultStr = HttpClientsUtils.httpPostRaw(Global.setReportUploadTimeUrl, reportUploadTimeData, null, null);
            logger.info("请求绑定设备参数："+data);
            logger.info("请求绑定设备结果："+resultStr);
            logger.info("设置设备报告产生时间："+reportUploadTimeData);
            logger.info("设置设备报告产生时间："+reportUploadTimeResultStr);
        }

        for(MainBedEquiModel model1:allList){
            for(MainBedEquiModel model2:allList){
                if(model1.getEquiModelId().equals(model2.getEquiModelId()) && !model1.getId().equals(model2.getId())){
                    renderJson(Ret.fail("msg","请勿重复添加两个相同id的设备"));
                    return;
                }
            }
        }

        try {
            if(equiModelAddList.size()>0){
                Db.batchSave(equiModelAddList,equiModelAddList.size());
            }
            if(equiModelUpdateList.size()>0){
                Db.batchUpdate(equiModelAddList,equiModelAddList.size());
            }
            renderJson(Ret.ok("msg","操作成功"));
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","操作失败"));
        }

    }



    public static void main(String[] args) {
        String str="[{\"info_if\":\"fct406pwxo7hd\",\"bed_id\":\"********-E4A8-46E3-96D7-E1DE91E02F3E\"},\n" +
                "{\"info_if\":\"aeexvgd7w5j5g\",\"bed_id\":\"CBEB6C40-EBF4-43E3-88C0-916554179C73\"},\n" +
                "{\"info_if\":\"ruq44hnhlq7i2\",\"bed_id\":\"422CF2C2-39CA-4B2A-BAA5-940C0827D474\"},\n" +
                "{\"info_if\":\"xdwr9y5jxakmm\",\"bed_id\":\"55585896-7D69-47DF-8055-CE832D5AC6A7\"},\n" +
                "{\"info_if\":\"jnv2qot5q2k2g\",\"bed_id\":\"29AE9E90-7890-4618-9C94-173E4D8AA894\"},\n" +
                "{\"info_if\":\"puhfbo2k1oc4i\",\"bed_id\":\"495827AC-0F89-4C12-8AF3-D8DB6B6B57B1\"},\n" +
                "{\"info_if\":\"qjssph0kw36w2\",\"bed_id\":\"ACC11879-D6EA-4D32-A162-CF8275AA6E7F\"},\n" +
                "{\"info_if\":\"d4op59qba3plt\",\"bed_id\":\"96FFFB34-BCAA-4139-B9CB-C21A66A07AD3\"},\n" +
                "{\"info_if\":\"2wgwbccy5tk0p\",\"bed_id\":\"C5FAFEBF-FE8E-48AE-9F2A-A1EEBD2951B8\"},\n" +
                "{\"info_if\":\"rhabnkqi31yn4\",\"bed_id\":\"1A3EF2AD-EE09-4EAA-B102-D22B299E9EB7\"},\n" +
                "{\"info_if\":\"ikf9p60ij7ms2\",\"bed_id\":\"9306B512-F91E-4410-A090-D2A6B0CC89BE\"},\n" +
                "{\"info_if\":\"e1n5hdjsaus77\",\"bed_id\":\"EF9BEE4C-383D-4B93-B59D-FFA62DCAE66F\"},\n" +
                "{\"info_if\":\"6it8euxvsfrg1\",\"bed_id\":\"89521391-910A-454D-AA11-1A6EA89032FF\"},\n" +
                "{\"info_if\":\"jwm10d2054y7n\",\"bed_id\":\"E1377E6A-AD7D-468A-ADC5-BD8F1F10AD0C\"},\n" +
                "{\"info_if\":\"h2diup92z68sh\",\"bed_id\":\"BCDC17ED-937D-4B72-9160-2DE86A34247B\"},\n" +
                "{\"info_if\":\"stmdtq53k7wah\",\"bed_id\":\"EEF67B77-52EF-43C7-AAD5-28E2CE64463C\"},\n" +
                "{\"info_if\":\"eb46k3t6qvw8v\",\"bed_id\":\"EC77F98B-8C91-451B-80D0-A080A7213F0C\"},\n" +
                "{\"info_if\":\"ejl4pmm145jbw\",\"bed_id\":\"5D40B855-9BE5-48DB-9C63-14FD31829F79\"},\n" +
                "{\"info_if\":\"ap26cklp2e3tz\",\"bed_id\":\"E4C0A748-7DCC-4798-A667-98C9AC6032F7\"},\n" +
                "{\"info_if\":\"qubulvbqndt5x\",\"bed_id\":\"79C2DC06-B958-4B00-826F-5875BC79013F\"},\n" +
                "{\"info_if\":\"xzhd8rpyeboed\",\"bed_id\":\"9BB06475-C944-4A8C-88EA-5AC988757565\"},\n" +
                "{\"info_if\":\"2yhzp66r1hg5n\",\"bed_id\":\"63929600-67E5-4C57-8E14-39069254613F\"},\n" +
                "{\"info_if\":\"dbfh2q1pio7dd\",\"bed_id\":\"EDDC4F69-A56C-4247-A2B1-AA5EF957A5A9\"},\n" +
                "{\"info_if\":\"5uh94w56majpx\",\"bed_id\":\"A8EE6AC1-4C72-4B8F-9B98-FF0BD0DDDE52\"},\n" +
                "{\"info_if\":\"eyp1mg33fnfze\",\"bed_id\":\"B85EF18E-7846-4955-9B16-12CEC03A1581\"},\n" +
                "{\"info_if\":\"7yjlpuw7q4wck\",\"bed_id\":\"FA27C100-BC78-4A9E-B5E2-CDE91455F748\"},\n" +
                "{\"info_if\":\"pjqbbt842jzk8\",\"bed_id\":\"68FB81D8-359B-45E8-94C6-C40F6C2E7D17\"},\n" +
                "{\"info_if\":\"4je6lu0825h0s\",\"bed_id\":\"DD73508C-EC53-410D-8E16-D8BC5FACFDB7\"},\n" +
                "{\"info_if\":\"r0cotfnni0bgc\",\"bed_id\":\"A121E676-CBA0-4601-90FC-045A5CEA8D11\"},\n" +
                "{\"info_if\":\"4g9ew7l6whbd4\",\"bed_id\":\"C2D7B473-8694-44AE-91DA-357EB88E9D43\"},\n" +
                "{\"info_if\":\"qu1j0qos2hol0\",\"bed_id\":\"6F8D42E9-E73F-4FAF-8154-63B868F5AF47\"},\n" +
                "{\"info_if\":\"2nl35apr8l77w\",\"bed_id\":\"66F10A21-ED93-4D2A-9969-138F35C3FBEB\"},\n" +
                "{\"info_if\":\"7ixbfumycv4a4\",\"bed_id\":\"C456271B-93C9-4BB5-86D0-8A86D8F88EC3\"},\n" +
                "{\"info_if\":\"tcjxndqiae5f9\",\"bed_id\":\"180F0F2F-9E8D-449C-8082-8104419248FD\"},\n" +
                "{\"info_if\":\"9vqat791bsb81\",\"bed_id\":\"2B71FE2F-310B-4610-9477-D8FB47A71FCF\"},\n" +
                "{\"info_if\":\"ig372syjzmts1\",\"bed_id\":\"2601592F-817B-43E4-A549-23040BB90C1F\"},\n" +
                "{\"info_if\":\"4m48oqbo7jzav\",\"bed_id\":\"5A8906B6-7A9B-436C-A372-EBBFA234558F\"},\n" +
                "{\"info_if\":\"0px1r4flhn4re\",\"bed_id\":\"0EA49B76-6861-4154-8C52-50071CB8DC76\"},\n" +
                "{\"info_if\":\"tb4j3k8q305kx\",\"bed_id\":\"08D7B4A8-97D7-46F5-85F2-E299FBED6AB0\"},\n" +
                "{\"info_if\":\"g1vj8p23xmnx1\",\"bed_id\":\"CA6B25D1-EECB-44F0-81DE-BE1F9058AE07\"}]";
        JSONArray jsonArray=JSON.parseArray(str);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            String data="{\"token\":{\"appId\":\""+"56915"+"\",\"secureKey\":\""+"4x8tdaS08AbN"
                    +"\"},\"data\":{\"deviceId\":\""+jsonObject.getString("info_if")+"\",\"userId\":\""+jsonObject.getString("bed_id")+"\",\"leftRight\":0,\"gender\":1,\"timezone\":28800,\"age\":28}}";
            String resultStr=HttpClientsUtils.httpPostRaw("http://120.24.68.136:8091/sleepace/bind",data,null,null);

            String reportUploadTimeData="{\"token\":{\"appId\":\""+"56915"+"\",\"secureKey\":\""+"4x8tdaS08AbN"
                    +"\"},\"data\":{\"deviceId\":\""+jsonObject.getString("info_if")+"\",\"reportUploadTime\":"+8+"}}";
            String reportUploadTimeResultStr = HttpClientsUtils.httpPostRaw("http://120.24.68.136:8091/sleepace/setReportUploadTime", reportUploadTimeData, null, null);
            System.out.println("===="+resultStr);
            System.out.println("===="+reportUploadTimeData);

        }
    }

    public void delBedEqui(){
        String id=getPara("id");
        MainBedEquiModel equiModel=new MainBedEquiModel();
        equiModel.setId(id);
        equiModel.setDelFlag("1");
        equiModel.setUpdateBy(AuthUtils.getUserId());

        if(mainBedEquiModelService.bedEquiSave(equiModel)){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void uploadRoom() throws Exception{
        UploadFile uploadFile = getFile();
        if(uploadFile == null){
            renderJson(Ret.fail("msg","文件不存在"));
            return;
        }
        try {
			/*XSSFWorkbook workbook=new XSSFWorkbook(uploadFile.getFile());
			XSSFSheet sheet = workbook.getSheetAt(0);

			int lastRowNum = sheet.getLastRowNum();


			// 创建样式
			XSSFCellStyle style = workbook.createCellStyle();
			style.setDataFormat(workbook.createDataFormat().getFormat(
					"yyyy-MM-dd"));*/
            FileInputStream inputStream = new FileInputStream(uploadFile.getFile());
            List<Record> recordList = new ArrayList<>();
            HSSFWorkbook hssfWorkbook = new HSSFWorkbook(inputStream);
            HSSFSheet sheet = hssfWorkbook.getSheetAt(0);

            int lastRowNum = sheet.getLastRowNum();

            HSSFCellStyle style = hssfWorkbook.createCellStyle();
            style.setDataFormat(hssfWorkbook.createDataFormat().getFormat(
                    "yyyy-MM-dd"));

            for (int j = 0; j <= lastRowNum; j++) {
                // 获取当前行
                HSSFRow row = sheet.getRow(j);
                //获取第一行表头
                HSSFRow row1 = sheet.getRow(0);
                if (row != null) {
                    // 获取当前行最后一个单元格
                    short cellNum = row.getLastCellNum();

                    Record record = new Record();
                    // 从第一个单元格开始遍历，直到最后一个单元格
                    for (int k = 0; k < cellNum; k++) {
                        HSSFCell cell = row.getCell(k);
                        if (cell != null) {
                            if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
                                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                                    //用于转化为日期格式
                                    Date d = cell.getDateCellValue();
                                    DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
                                    record.set(row1.getCell(k).getStringCellValue(), formater.format(d));
                                }
                            } else if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
                                record.set(row1.getCell(k).getStringCellValue(), cell.getStringCellValue());
                            }
                        } else {
                            System.out.print("null" + "  ");
                        }
                    }
                    recordList.add(record);
                }
            }
            Set<String> bedType=new HashSet<>();

            Set<String> roomType=new HashSet<>();

            Map<String, JSONObject> map=new HashMap<>();
            int i=0;
            for (Record record : recordList) {
                if(i==0){
                    i++;
                    continue;
                }


                String roomName=record.get("房间");
                System.out.println(roomName);

                if(map.containsKey(roomName)){
                    JSONObject jsonObject=map.get(roomName);

                    JSONArray bedArray=jsonObject.getJSONArray("bedArray");
                    String roomId=jsonObject.getString("roomId");


                    String bedTypeName=record.getStr("床位类型");

                    Record bedTypeRecord=Db.findFirst("select * from main_bed_type where type_name=? and del_flag='0' ORDER BY create_date desc ",bedTypeName);
                    if(bedTypeRecord==null && bedTypeName.equals("1.8*2")){
                        bedTypeRecord=Db.findFirst("select * from main_bed_type where type_name=? and del_flag='0' ORDER BY create_date desc ","2*1.8米");
                    }
                    if(bedTypeRecord==null && bedTypeName.equals("1.2*2")){
                        bedTypeRecord=Db.findFirst("select * from main_bed_type where type_name=? and del_flag='0' ORDER BY create_date desc ","1.2*2米");

                    }
                    if(bedTypeRecord==null){
                        renderCodeFailed("床位类型是空");
                        return;
                    }

                    String bedName=record.getStr("床位");
                    JSONObject bedObj=new JSONObject();
                    bedObj.put("bedName",bedName);
                    bedObj.put("bedNo",bedName);
                    bedObj.put("roomId",roomId);
                    bedObj.put("bedTypeId",bedTypeRecord.getStr("id"));
                    bedObj.put("bedStatusId","3E541B4D-2BA2-4993-90EE-86C39353ACF9");
                    bedObj.put("isEnable","0");
                    bedObj.put("bookChannel","03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7");
                    bedObj.put("mattressTypeId","80030260-A06F-44B6-9BDA-513777106767");

                    Map<String,String> bedParams=new HashMap<>();

                    bedParams.put("bedName",bedName);
                    bedParams.put("bedNo",bedName);
                    bedParams.put("roomId",roomId);
                    bedParams.put("bedTypeId",bedTypeRecord.getStr("id"));
                    bedParams.put("bedStatusId","3E541B4D-2BA2-4993-90EE-86C39353ACF9");
                    bedParams.put("isEnable","0");
                    bedParams.put("bookChannel","03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7");
                    bedParams.put("mattressTypeId","80030260-A06F-44B6-9BDA-513777106767");

                    String bedResult=HttpClientsUtils.httpPostForm("http://main.cncsgroup.com/main/buildingRoomManage/saveBed",bedParams,null,null);
                    if(bedResult.startsWith("{") && bedResult.endsWith("}")){
                        JSONObject res=JSON.parseObject(bedResult);
                        roomId=res.getString("data");
                        if(StrKit.isBlank(roomId)){
                            renderCodeFailed("添加床位失败"+bedName);
                            return;
                        }
                    }


                    bedArray.add(bedObj);

                }else{
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("buildingId",record.getStr("楼栋信息"));
                    jsonObject.put("floorId",record.getStr("楼层"));
                    jsonObject.put("roomNo",record.getStr("房间"));
                    jsonObject.put("roomName",record.getStr("房间"));
                    String roomTypeName=record.getStr("房间类型");
                    String bedTypeName=record.getStr("床位类型");
                    Record roomTypeRecord=Db.findFirst("select * from main_room_type where type_name=? and del_flag='0' ORDER BY create_date desc ",roomTypeName);
                    if(roomTypeRecord==null && roomTypeName.equals("双标房")){
                        roomTypeRecord=Db.findFirst("select * from main_room_type where type_name=? and del_flag='0' ORDER BY create_date desc ","双标");
                    }
                    if(roomTypeRecord==null){
                        renderCodeFailed("房间类型是空");
                        return;
                    }
                    Record bedTypeRecord=Db.findFirst("select * from main_bed_type where type_name=? and del_flag='0' ORDER BY create_date desc ",bedTypeName);
                    if(bedTypeRecord==null && bedTypeName.equals("1.8*2")){
                        bedTypeRecord=Db.findFirst("select * from main_bed_type where type_name=? and del_flag='0' ORDER BY create_date desc ","2*1.8米");
                    }
                    if(bedTypeRecord==null && bedTypeName.equals("1.2*2")){
                        bedTypeRecord=Db.findFirst("select * from main_bed_type where type_name=? and del_flag='0' ORDER BY create_date desc ","1.2*2米");

                    }
                    if(bedTypeRecord==null){
                        renderCodeFailed("床位类型是空");
                        return;
                    }
                    jsonObject.put("roomType",roomTypeRecord.getStr("id"));
                    jsonObject.put("peoples",2);
                    jsonObject.put("maintainState","Normal");
                    jsonObject.put("useType","guest_room");
                    jsonObject.put("isEnable","0");
                    jsonObject.put("isBooking","0");
                    jsonObject.put("allowLock","0");
                    jsonObject.put("bookChannel","03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7");


                    //saveRoom
                    Map<String,String> roomParams=new HashMap<>();

                    roomParams.put("buildingId",record.getStr("楼栋信息"));
                    roomParams.put("floorId",record.getStr("楼层"));
                    roomParams.put("roomNo",record.getStr("房间"));
                    roomParams.put("roomName",record.getStr("房间"));
                    roomParams.put("roomType",roomTypeRecord.getStr("id"));
                    roomParams.put("peoples","2");
                    roomParams.put("maintainState","Normal");
                    roomParams.put("useType","guest_room");
                    roomParams.put("isEnable","0");
                    roomParams.put("isBooking","0");
                    roomParams.put("allowLock","0");
                    roomParams.put("bookChannel","03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7");
                    String roomId=null;
                    String result=HttpClientsUtils.httpPostForm("http://main.cncsgroup.com/main/buildingRoomManage/saveRoom",roomParams,null,null);
                    if(result.startsWith("{") && result.endsWith("}")){
                        JSONObject res=JSON.parseObject(result);
                        roomId=res.getString("data");
                        if(StrKit.isBlank(roomId)){
                            renderCodeFailed("添加房间失败"+roomName);
                            return;
                        }
                    }else{
                        renderCodeFailed("添加房间失败"+roomName);
                        return;
                    }
                    jsonObject.put("roomId",roomId);

                    JSONArray jsonArray=new JSONArray();

                    String bedName=record.getStr("床位");
                    JSONObject bedObj=new JSONObject();
                    bedObj.put("bedName",bedName);
                    bedObj.put("bedNo",bedName);
                    bedObj.put("roomId",roomId);
                    bedObj.put("bedTypeId",bedTypeRecord.getStr("id"));
                    bedObj.put("bedStatusId","3E541B4D-2BA2-4993-90EE-86C39353ACF9");
                    bedObj.put("isEnable","0");
                    bedObj.put("bookChannel","03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7");
                    bedObj.put("mattressTypeId","80030260-A06F-44B6-9BDA-513777106767");


                    Map<String,String> bedParams=new HashMap<>();

                    bedParams.put("bedName",bedName);
                    bedParams.put("bedNo",bedName);
                    bedParams.put("roomId",roomId);
                    bedParams.put("bedTypeId",bedTypeRecord.getStr("id"));
                    bedParams.put("bedStatusId","3E541B4D-2BA2-4993-90EE-86C39353ACF9");
                    bedParams.put("isEnable","0");
                    bedParams.put("bookChannel","03E98148-1A31-4C4C-8281-D4AE2574A857,5B54C000-3A24-4B2D-BBCC-98A27D8849D1,7A23D458-C784-4C41-9375-111E2FEA1CC0,8C516932-BA48-4DEC-9783-00C2EC57EAB7");
                    bedParams.put("mattressTypeId","80030260-A06F-44B6-9BDA-513777106767");

                    String bedResult=HttpClientsUtils.httpPostForm("http://main.cncsgroup.com/main/buildingRoomManage/saveBed",bedParams,null,null);
                    if(bedResult.startsWith("{") && bedResult.endsWith("}")){
                        JSONObject res=JSON.parseObject(bedResult);
                        String bedId=res.getString("data");
                        if(StrKit.isBlank(bedId)){
                            renderCodeFailed("添加床位失败"+bedName);
                            return;
                        }
                    }else{
                        renderCodeFailed("添加床位失败"+bedName);
                        return;
                    }

                    jsonArray.add(bedObj);
                    jsonObject.put("bedArray",jsonArray);


                    map.put(roomName,jsonObject);
                }

            }
            System.out.println(JSON.toJSONString(map));

            renderCodeSuccess("操作成功");
        }catch (Exception e){
            e.printStackTrace();
            renderCodeFailed("导入失败");
        }

    }
}
