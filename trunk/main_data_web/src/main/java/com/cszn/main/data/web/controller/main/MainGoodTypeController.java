package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainDictionaryTypesService;
import com.cszn.integrated.service.api.main.MainGoodTypesService;
import com.cszn.integrated.service.entity.main.MainDictionaryTypes;
import com.cszn.integrated.service.entity.main.MainGoodTypes;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

@RequestMapping(value = "/main/goodType",viewPath = "/modules_page/main/goodType/")
public class MainGoodTypeController extends BaseController {

    @Inject
    private MainGoodTypesService mainGoodTypesService;
    @Inject
    private MainDictionaryTypesService mainDictionaryTypesService;

    public void index(){

        render("goodTypeIndex.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainGoodTypes type=mainGoodTypesService.findById(id);
            setAttr("model",type);
            if(type!=null){
                MainGoodTypes parentType=mainGoodTypesService.findById(type.getParentId());
                if(parentType!=null){
                    setAttr("parentTypeName",parentType.getName());
                }
            }
        }
        List<MainDictionaryTypes> dictionaryList=mainDictionaryTypesService.getDictionaryTypes("1");

        setAttr("dictionaryList",dictionaryList);
        render("goodTypeForm.html");
    }

    public void goodTypeTableTree(){
        List<Record> recordList=mainGoodTypesService.goodTypeTableTree();
        renderJson(new DataTable<Record>(recordList));
    }

    public void goodTypeTree() {
        List<Record> recordList = mainGoodTypesService.goodTypeTableTree();
        List<ZTree> zTreeList=new ArrayList<>();
        for(Record record:recordList){
            ZTree zTree=new ZTree(record.getStr("id"),record.getStr("name"),null,record.getStr("pId"),record.getStr("pIds"));
            zTreeList.add(zTree);
        }
        renderJson(zTreeList);
    }


    public void saveGoodType(){
        MainGoodTypes type=getBean(MainGoodTypes.class,"",true);
        type.setName(type.getName().trim());
        if(StrKit.notBlank(type.getId())){
            if(Db.findFirst("select * from main_good_types where `name`=? and id<>? ",type.getName(),type.getId())!=null){
                renderJson(Ret.fail("msg","改类型已存在"));
                return;
            }
            if(type.getId().equals(type.getParentId())){
                renderJson(Ret.fail("msg","不能设置自己为自己的父级"));
                return;
            }
            MainGoodTypes parentType=mainGoodTypesService.findById(type.getParentId());
            if(!"00000000-0000-0000-0000-000000000000".equals(type.getParentId())&& parentType!=null && parentType.getParentIds().contains(type.getId())){
                renderJson(Ret.fail("msg","不能设置自己的子级为自己的父级"));
                return;
            }
        }else{
            if(Db.findFirst("select * from main_good_types where `name`=? ",type.getName())!=null){
                renderJson(Ret.fail("msg","改类型已存在"));
                return;
            }
        }
        boolean flag=mainGoodTypesService.saveGoodType(type, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
