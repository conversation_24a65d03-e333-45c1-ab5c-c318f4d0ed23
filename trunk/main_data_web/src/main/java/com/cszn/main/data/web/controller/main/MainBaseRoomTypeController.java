package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseRoomTypeService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainRoomTypeService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBaseRoomType;
import com.cszn.integrated.service.entity.main.MainRoomType;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.json.JFinalJson;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * @Description 房间类型管理
 * <AUTHOR>
 * @Date 2019/6/20
 **/
@RequestMapping(value="/main/baseRoomType", viewPath="/modules_page/main/baseRoomType")
public class MainBaseRoomTypeController extends BaseController {

    @Inject
    private MainBaseService mainBaseService;

    @Inject
    private MainBaseRoomTypeService mainBaseRoomTypeService;



    /**
     * 跳转房间类型界面batchDel
     */
    public void index(){
        List<MainBase> baseList = mainBaseService.findAll();
        setAttr("baseList",baseList);
        render("roomTypeIndex.html");
    }


    /**
     * 房间类型列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MainBaseRoomType roomType = getBean(MainBaseRoomType.class,"",true);
//        if(roomType != null)roomType.setBaseId(getCookie("baseId"));
        Page<MainBaseRoomType> page = mainBaseRoomTypeService.findListPage(getParaToInt("page"),getParaToInt("limit"),roomType);
        //DataTable dataTable = new DataTable<MainBaseRoomType>(page);
//        if(dataTable.getData() != null && dataTable.getData().size() > 0){
//            dataTable.getData().stream().forEach(item -> {
//                MainRoomType typeItem = (MainRoomType)item;
//                if(typeItem != null && StringUtils.isNotBlank(typeItem.get("baseId"))){
//                    MainBase base = mainBaseService.findById(typeItem.get("baseId"));
//                    if(base != null)typeItem.put("baseName", base.getBaseName());
//                }
//            });
//        }
        renderJson(new DataTable<MainBaseRoomType>(page));
    }


    /**
     * 跳转新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MainBaseRoomType roomType = mainBaseRoomTypeService.get(id);
        if(roomType != null){
            MainBase base = mainBaseService.get(roomType.getId());
            setAttr("base",base);
        }
        List<MainBase> baseList = mainBaseService.findAll();
        setAttr("baseList",baseList);
        setAttr("commonUpload", Global.commonUpload);
        setAttr("roomType",roomType);
        render("roomTypeForm.html");
    }


    /**
     * 保存
     */
    public void save(){
        MainBaseRoomType roomType = getBean(MainBaseRoomType.class,"roomType",true);
        if(roomType == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        /*if(mainBaseRoomTypeService.findRoomTypeIsExist(roomType.getId(),roomType.getTypeName())!=null){
            renderJson(Ret.fail("msg", "类型名称不能重复"));
            return;
        }*/

        boolean flag = mainBaseRoomTypeService.saveRoomType(roomType,AuthUtils.getUserId());
        if(flag ){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 房间类型删除
     */
    public void delete(){
        String id = getPara("id");
        MainBaseRoomType roomType = mainBaseRoomTypeService.get(id);
        if(roomType != null){
            boolean flag = mainBaseRoomTypeService.delRoomType(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "删除成功"));
            } else {
                renderJson(Ret.fail("msg", "删除失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "删除失败"));
        }
    }



    /**
     * 批量删除
     */
    public void batchDel(){
        String roomTypeData =  getPara("roomTypeData");
        List<MainBaseRoomType> list = JSONArray.parseArray(roomTypeData,MainBaseRoomType.class);
        boolean flag = mainBaseRoomTypeService.batchDelRoomType(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量删除成功"));
        }else{
            renderJson(Ret.fail("msg", "批量删除失败"));
        }
    }

}
