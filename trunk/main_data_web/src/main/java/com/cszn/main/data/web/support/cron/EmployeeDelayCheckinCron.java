package com.cszn.main.data.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.entity.enums.PersLeaveRestType;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.provider.pers.PersOrgEmployeeCheckinRuleServiceImpl;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


public class EmployeeDelayCheckinCron implements Runnable {

    @Inject
    PersOrgEmployeeCheckinWxRecordService persOrgEmployeeCheckinWxRecordService;
    @Inject
    private PersOrgEmployeeCheckinRecordService persOrgEmployeeCheckinRecordService;
    @Inject
    private PersOrgService persOrgService;
    private Logger logger= LoggerFactory.getLogger(EmployeeDelayCheckinCron.class);

    @Override
    public void run() {
        Date startDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.getNextDay(new Date(),-2),"yyyy-MM-dd")+" 00:00:00");
        Date endDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd")+" 00:00:00");
        long curr=System.currentTimeMillis();
        persOrgEmployeeCheckinWxRecordService.getWorkWeiXinDelayCheckinData(1,90,startDate.getTime()/1000,endDate.getTime()/1000);
        logger.info("1断网耗时："+(System.currentTimeMillis()-curr));


        Date startDate1=DateUtils.parseDate(DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd")+" 00:00:00");
        Date endDate1=DateUtils.parseDate(DateUtils.formatDate(new Date(),"yyyy-MM-dd")+" 00:00:00");
        long curr1=System.currentTimeMillis();
        persOrgEmployeeCheckinWxRecordService.getWorkWeiXinDelayCheckinData(1,90,startDate1.getTime()/1000,endDate1.getTime()/1000);
        logger.info("2断网耗时："+(System.currentTimeMillis()-curr1));
    }




    /**
     * 合并去重时间段
     *
     * @return
     */
    public static List<Map<String, Date>> getTimePeriodListDumplictcatePeriod(List<Map<String, Date>> periodList) {
        List<Map<String, Date>> result = new ArrayList<>();
        //列表不能为空
        if (periodList == null) {
            return null;
        }
        //对数据排序，开始时间从小到大
        Collections.sort(periodList, new Comparator<Map<String, Date>>() {
            @Override
            public int compare(Map<String, Date> u1, Map<String, Date> u2) {
                long diff = u1.get("startDate").getTime() - u2.get("startDate").getTime();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }

                return 0; //相等为0
            }

        });

        for (int i = 0; i < periodList.size() - 1; i++) {
            int j = i + 1;
            //判断 i的Endtime 与j 的Begintime 是否有交叉，有交叉，更新 j的Begintime 为i的Begintime, 并移除i
            if (periodList.get(i).get("endDate").after(periodList.get(j).get("startDate"))
                    || (!periodList.get(i).get("endDate").after(periodList.get(j).get("startDate"))) && !periodList.get(i).get("endDate").before(periodList.get(j).get("startDate"))) {
                if(periodList.get(i).get("startDate").getTime()<=periodList.get(j).get("startDate").getTime()
                        && periodList.get(i).get("endDate").getTime()>=periodList.get(j).get("endDate").getTime()){
                    //如果包含关系则直接删除
                    periodList.remove(j);
                    i--;
                }else{
                    periodList.get(j).put("startDate", periodList.get(i).get("startDate"));
                    periodList.remove(i);
                    i--;
                }
            }
        }

        result = periodList;
        return result;
    }


    /**
     * 剔除时间段
     */
    public static List<Map<String, Date>> getTimePeriodListDumplictcatePeriod(List<Map<String, Date>> periodList,List<Map<String, Date>> periodList2) {
        List<Map<String, Date>> result = new ArrayList<>();
        //列表不能为空
        if (periodList == null) {
            return null;
        }
        //对数据排序，开始时间从小到大
        Collections.sort(periodList, new Comparator<Map<String, Date>>() {
            @Override
            public int compare(Map<String, Date> u1, Map<String, Date> u2) {
                long diff = u1.get("startDate").getTime() - u2.get("startDate").getTime();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }

        });

        Collections.sort(periodList2, new Comparator<Map<String, Date>>() {
            @Override
            public int compare(Map<String, Date> u1, Map<String, Date> u2) {
                long diff = u1.get("startDate").getTime() - u2.get("startDate").getTime();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }

        });

        for (int i = 0; i < periodList2.size(); i++) {

            for (int j = 0; j < periodList.size(); j++){

                if(
                        (DateUtils.compareDays(periodList2.get(i).get("startDate"),periodList.get(j).get("startDate"))==0 &&
                                DateUtils.compareDays(periodList2.get(i).get("endDate"),periodList.get(j).get("endDate"))==0)


                ){
                    periodList2.remove(i);
                    periodList.remove(j);
                    i--;
                    break;
                }

                if(periodList2.get(i).get("startDate").getTime()>=periodList.get(j).get("startDate").getTime() &&
                        periodList2.get(i).get("endDate").getTime()<=periodList.get(j).get("endDate").getTime()
                ){
                    if(periodList2.get(i).get("startDate").getTime()==periodList.get(j).get("startDate").getTime()){
                        periodList.get(j).put("startDate",periodList2.get(i).get("endDate"));
                    }else if(periodList2.get(i).get("endDate").getTime()==periodList.get(j).get("endDate").getTime()){
                        periodList.get(j).put("endDate",periodList2.get(i).get("startDate"));
                    }else{
                        Map<String,Date> dateMap=new HashMap<>();
                        dateMap.put("startDate",periodList.get(j).get("startDate"));
                        dateMap.put("endDate",periodList2.get(i).get("startDate"));

                        periodList.add(dateMap);

                        periodList.get(j).put("startDate",periodList2.get(i).get("endDate"));
                    }
                    periodList2.remove(i);
                    i--;
                    break;
                }

                if(periodList2.get(i).get("startDate").getTime()<=periodList.get(j).get("startDate").getTime()
                    && periodList2.get(i).get("endDate").getTime()>=periodList.get(j).get("endDate").getTime()
                ){
                    if(periodList2.get(i).get("startDate").getTime()==periodList.get(j).get("startDate").getTime()){
                        periodList2.get(i).put("startDate",periodList.get(j).get("endDate"));
                    }else if(periodList2.get(i).get("endDate").getTime()==periodList.get(j).get("endDate").getTime()){
                        periodList2.get(i).put("endDate",periodList.get(j).get("startDate"));
                    }else{
                        Map<String,Date> dateMap=new HashMap<>();
                        dateMap.put("startDate",periodList2.get(i).get("startDate"));
                        dateMap.put("endDate",periodList.get(j).get("startDate"));

                        periodList2.add(dateMap);
                        periodList2.get(i).put("startDate",periodList.get(j).get("endDate"));
                        periodList.remove(j);
                        i=0;
                        Collections.sort(periodList2, new Comparator<Map<String, Date>>() {
                            @Override
                            public int compare(Map<String, Date> u1, Map<String, Date> u2) {
                                long diff = u1.get("startDate").getTime() - u2.get("startDate").getTime();
                                if (diff > 0) {
                                    return 1;
                                } else if (diff < 0) {
                                    return -1;
                                }
                                return 0; //相等为0
                            }

                        });

                        break;
                    }
                    periodList.remove(j);
                    i--;
                    j--;
                    break;
                }

                //  |--------------|
                //  |------------------|
                if(periodList2.get(i).get("startDate").getTime() >= periodList.get(j).get("startDate").getTime()
                        && periodList2.get(i).get("endDate").getTime() > periodList.get(j).get("endDate").getTime()){
                    if(periodList.get(j).get("endDate").getTime()<=periodList2.get(i).get("startDate").getTime()){
                        continue;
                    }

                    if(periodList2.get(i).get("startDate").getTime() == periodList.get(j).get("startDate").getTime()){

                        periodList2.get(i).put("startDate",periodList.get(j).get("endDate"));
                        periodList.remove(j);
                        j--;
                        break;
                    }else{
                        Date periodList2StartDate=periodList2.get(i).get("startDate");
                        Date periodListEndDate=periodList.get(j).get("endDate");
                        periodList2.get(i).put("startDate",periodListEndDate);
                        periodList.get(j).put("endDate",periodList2StartDate);
                    }
                }else if(periodList2.get(i).get("startDate").getTime() < periodList.get(j).get("startDate").getTime()
                        && periodList2.get(i).get("endDate").getTime()<=periodList.get(j).get("endDate").getTime()
                ){
                    if(periodList2.get(i).get("endDate").getTime()<=periodList.get(j).get("startDate").getTime()){
                        continue;
                    }
                    if(periodList2.get(i).get("endDate").getTime()==periodList.get(j).get("endDate").getTime()){
                        periodList2.get(i).put("endDate",periodList.get(j).get("startDate"));
                        periodList.remove(j);
                        j--;
                        break;
                    }else{
                        Date periodList2EndDate=periodList2.get(i).get("endDate");
                        Date periodListStartDate=periodList.get(j).get("startDate");
                        periodList2.get(i).put("endDate",periodListStartDate);
                        periodList.get(j).put("startDate",periodList2EndDate);
                    }
                }
            }
        }
        result = periodList;
        //对数据排序，开始时间从小到大
        Collections.sort(result, new Comparator<Map<String, Date>>() {
            @Override
            public int compare(Map<String, Date> u1, Map<String, Date> u2) {
                long diff = u1.get("startDate").getTime() - u2.get("startDate").getTime();
                if (diff > 0) {
                    return 1;
                } else if (diff < 0) {
                    return -1;
                }
                return 0; //相等为0
            }

        });
        return result;
    }


    @Inject
    private PersOrgEmployeeCheckinRuleService persOrgEmployeeCheckinRuleService;
    @Inject
    private PersOrgEmployeeCheckinRuleCheckinDateService persOrgEmployeeCheckinRuleCheckinDateService;
    @Inject
    private PersOrgEmployeeCheckinRuleEmpScheduleService persOrgEmployeeCheckinRuleEmpScheduleService;
    @Inject
    private PersOrgEmployeeCheckinRuleScheduleService persOrgEmployeeCheckinRuleScheduleService;
    @Inject
    private PersOrgEmployeeCheckinRuleHolidaysService persOrgEmployeeCheckinRuleHolidaysService;
    @Inject
    private PersOrgEmployeeOverTimeApplyService persOrgEmployeeOverTimeApplyService;
    @Inject
    private PersOrgEmployeeLeaveRestApplyService persOrgEmployeeLeaveRestApplyService;
    @Inject
    private PersOrgEmployeeBusinessTripApplyService persOrgEmployeeBusinessTripApplyService;
    @Inject
    private PersOrgEmployeeLeaveRestApplyDateService persOrgEmployeeLeaveRestApplyDateService;
    @Inject
    private PersOrgEmployeeDispatchApplyDateService persOrgEmployeeDispatchApplyDateService;
    @Inject
    private PersOrgEmployeeBusinessTripApplyDateService persOrgEmployeeBusinessTripApplyDateService;
    @Inject
    private PersOrgEmployeeCheckinExceptionService persOrgEmployeeCheckinExceptionService;

    /**
     * 获取员工指定日期的是否休息或上班下班时间
     */
    public Map<String,Object> getEmpDayWorkTime(String empId,Date checkinTime){

        Map<String,Object> resultMap=new HashMap<>();

        //打卡当天的规则
        PersOrgEmployeeCheckinRule todayCheckinRule =persOrgEmployeeCheckinRuleService.
                getEmpTopCheckinRuleByDate(empId,checkinTime);

        //今天是否休息
        boolean isRest=false;

        Date minWorkCheckinTime=null;
        Date maxOffWorkCheckinTime=null;

        //节假日配置
        PersOrgEmployeeCheckinRuleHolidays checkinRuleHolidays=persOrgEmployeeCheckinRuleHolidaysService.getRuleHolidaysByRuleId(todayCheckinRule.getId());
        JSONArray workDays=null;
        JSONArray restDays=null;
        if(checkinRuleHolidays!=null && StrKit.notBlank(checkinRuleHolidays.getWorkDays())){
            workDays=JSON.parseArray(checkinRuleHolidays.getWorkDays());
        }
        if(checkinRuleHolidays!=null && StrKit.notBlank(checkinRuleHolidays.getRestDays())){
            restDays=JSON.parseArray(checkinRuleHolidays.getRestDays());
        }

        if("1".equals(todayCheckinRule.getType())){
            resultMap.put("ruleType","1");
            //固定上班时间
            PersOrgEmployeeCheckinRuleCheckinDate checkinDate =persOrgEmployeeCheckinRuleCheckinDateService.getRuleCheckinDateByDate(todayCheckinRule.getId(),checkinTime,checkinTime);
            if(checkinDate==null){
                isRest=true;
                if(workDays!=null && workDays.contains(DateUtils.formatDate(checkinTime,"yyyy-MM-dd"))){
                    isRest=false;
                    checkinDate=persOrgEmployeeCheckinRuleCheckinDateService.getCheckinDateByRuleId(todayCheckinRule.getId()).get(0);
                    resultMap.put("isManyCheckin",checkinDate.getIsManyCheckin());
                    int[] workTimeArray=PersOrgEmployeeCheckinRuleServiceImpl.getWorkTimeArray(checkinDate.getCheckinTime(),-1);
                    int[] offWorkTimeArray=PersOrgEmployeeCheckinRuleServiceImpl.getOffWorkTimeArray(checkinDate.getCheckinTime(),-1);

                    Calendar workCalendar=Calendar.getInstance();
                    workCalendar.setTime(checkinTime);
                    workCalendar.set(Calendar.HOUR_OF_DAY,workTimeArray[0]);
                    workCalendar.set(Calendar.MINUTE,workTimeArray[1]);
                    workCalendar.set(Calendar.SECOND,0);
                    minWorkCheckinTime=workCalendar.getTime();

                    Calendar offWorkCalendar=Calendar.getInstance();
                    offWorkCalendar.setTime(checkinTime);
                    offWorkCalendar.set(Calendar.HOUR_OF_DAY,offWorkTimeArray[0]);
                    offWorkCalendar.set(Calendar.MINUTE,offWorkTimeArray[1]);
                    offWorkCalendar.set(Calendar.SECOND,0);
                    maxOffWorkCheckinTime=offWorkCalendar.getTime();

                    //获取上班时间段
                    List<String[]> workTimePeriod=workTimePeriod(checkinTime,checkinDate.getCheckinTime());
                    resultMap.put("workTimePeriod",workTimePeriod);
                }
            }else{
                resultMap.put("isManyCheckin",checkinDate.getIsManyCheckin());
                if(restDays!=null && restDays.contains(DateUtils.formatDate(checkinTime,"yyyy-MM-dd"))){
                    isRest=true;
                }else{
                    int[] workTimeArray=PersOrgEmployeeCheckinRuleServiceImpl.getWorkTimeArray(checkinDate.getCheckinTime(),-1);
                    int[] offWorkTimeArray=PersOrgEmployeeCheckinRuleServiceImpl.getOffWorkTimeArray(checkinDate.getCheckinTime(),-1);

                    Calendar workCalendar=Calendar.getInstance();
                    workCalendar.setTime(checkinTime);
                    workCalendar.set(Calendar.HOUR_OF_DAY,workTimeArray[0]);
                    workCalendar.set(Calendar.MINUTE,workTimeArray[1]);
                    workCalendar.set(Calendar.SECOND,0);
                    minWorkCheckinTime=workCalendar.getTime();

                    Calendar offWorkCalendar=Calendar.getInstance();
                    offWorkCalendar.setTime(checkinTime);
                    offWorkCalendar.set(Calendar.HOUR_OF_DAY,offWorkTimeArray[0]);
                    offWorkCalendar.set(Calendar.MINUTE,offWorkTimeArray[1]);
                    offWorkCalendar.set(Calendar.SECOND,0);
                    maxOffWorkCheckinTime=offWorkCalendar.getTime();

                    List<String[]> workTimePeriod=workTimePeriod(checkinTime,checkinDate.getCheckinTime());
                    resultMap.put("workTimePeriod",workTimePeriod);
                }

            }
        }else if("2".equals(todayCheckinRule.getType())){
            resultMap.put("ruleType","2");
            //排班类型
            PersOrgEmployeeCheckinRuleEmpSchedule empSchedule=persOrgEmployeeCheckinRuleEmpScheduleService.getEmpSchedule(todayCheckinRule.getId(),empId);
            if(empSchedule==null){
                isRest=true;
            }else{
                JSONObject empScheduleConfigObj= JSON.parseObject(empSchedule.getScheduleConfig());
                String sameDayScheduleId=empScheduleConfigObj.getString(DateUtils.formatDate(checkinTime,"yyyy-MM-dd"));
                if(StrKit.isBlank(sameDayScheduleId)){
                    isRest=true;
                }else{
                    PersOrgEmployeeCheckinRuleSchedule checkinRuleSchedule =persOrgEmployeeCheckinRuleScheduleService.findById(sameDayScheduleId);
                    int[] workTimeArray=PersOrgEmployeeCheckinRuleServiceImpl.getWorkTimeArray(checkinRuleSchedule.getCheckinTime(),-1);
                    int[] offWorkTimeArray=PersOrgEmployeeCheckinRuleServiceImpl.getOffWorkTimeArray(checkinRuleSchedule.getCheckinTime(),-1);

                    Calendar workCalendar=Calendar.getInstance();
                    workCalendar.setTime(checkinTime);
                    workCalendar.set(Calendar.HOUR_OF_DAY,workTimeArray[0]);
                    workCalendar.set(Calendar.MINUTE,workTimeArray[1]);
                    workCalendar.set(Calendar.SECOND,0);
                    minWorkCheckinTime=workCalendar.getTime();

                    Calendar offWorkCalendar=Calendar.getInstance();
                    offWorkCalendar.setTime(checkinTime);
                    offWorkCalendar.set(Calendar.HOUR_OF_DAY,offWorkTimeArray[0]);
                    offWorkCalendar.set(Calendar.MINUTE,offWorkTimeArray[1]);
                    offWorkCalendar.set(Calendar.SECOND,0);
                    maxOffWorkCheckinTime=offWorkCalendar.getTime();

                    List<String[]> workTimePeriod=workTimePeriod(checkinTime,checkinRuleSchedule.getCheckinTime());
                    resultMap.put("workTimePeriod",workTimePeriod);
                }
            }

        }else if("3".equals(todayCheckinRule.getType())){
            resultMap.put("ruleType","3");
            //自由打卡
            PersOrgEmployeeCheckinRuleCheckinDate checkinDate =persOrgEmployeeCheckinRuleCheckinDateService.getRuleCheckinDateByDate(todayCheckinRule.getId(),checkinTime,checkinTime);
            //不需要打下班卡

            if(checkinDate==null){
                isRest=true;
                if(workDays!=null && workDays.contains(DateUtils.formatDate(checkinTime,"yyyy-MM-dd"))){
                    isRest=false;

                    Calendar workCalendar=Calendar.getInstance();
                    workCalendar.setTime(checkinTime);
                    workCalendar.set(Calendar.HOUR_OF_DAY,0);
                    workCalendar.set(Calendar.MINUTE,0);
                    workCalendar.set(Calendar.SECOND,0);
                    minWorkCheckinTime=workCalendar.getTime();

                    Calendar offWorkCalendar=Calendar.getInstance();
                    offWorkCalendar.setTime(checkinTime);
                    offWorkCalendar.set(Calendar.HOUR_OF_DAY,23);
                    offWorkCalendar.set(Calendar.MINUTE,59);
                    offWorkCalendar.set(Calendar.SECOND,59);
                    maxOffWorkCheckinTime=offWorkCalendar.getTime();
                    List<String[]> workTimePeriod=new ArrayList<>();
                    workTimePeriod.add(new String[]{DateUtils.formatDate(minWorkCheckinTime,"yyyy-MM-dd HH:mm:ss"),DateUtils.formatDate(maxOffWorkCheckinTime,"yyyy-MM-dd HH:mm:ss")});
                    resultMap.put("workTimePeriod",workTimePeriod);
                }
            }else{
                if(restDays!=null && restDays.contains(DateUtils.formatDate(checkinTime,"yyyy-MM-dd"))){
                    isRest=true;
                }else{
                    resultMap.put("noneedOffwork",checkinDate.getNoneedOffwork());
                    Calendar workCalendar=Calendar.getInstance();
                    workCalendar.setTime(checkinTime);
                    workCalendar.set(Calendar.HOUR_OF_DAY,0);
                    workCalendar.set(Calendar.MINUTE,0);
                    workCalendar.set(Calendar.SECOND,0);
                    minWorkCheckinTime=workCalendar.getTime();

                    Calendar offWorkCalendar=Calendar.getInstance();
                    offWorkCalendar.setTime(checkinTime);
                    offWorkCalendar.set(Calendar.HOUR_OF_DAY,23);
                    offWorkCalendar.set(Calendar.MINUTE,59);
                    offWorkCalendar.set(Calendar.SECOND,59);
                    maxOffWorkCheckinTime=offWorkCalendar.getTime();
                    List<String[]> workTimePeriod=new ArrayList<>();
                    workTimePeriod.add(new String[]{DateUtils.formatDate(minWorkCheckinTime,"yyyy-MM-dd HH:mm:ss"),DateUtils.formatDate(maxOffWorkCheckinTime,"yyyy-MM-dd HH:mm:ss")});
                    resultMap.put("workTimePeriod",workTimePeriod);
                }
            }
        }
        resultMap.put("isRest",isRest);
        resultMap.put("minWorkCheckinTime",minWorkCheckinTime);
        resultMap.put("maxOffWorkCheckinTime",maxOffWorkCheckinTime);
        resultMap.put("checkinRule",todayCheckinRule);
        return resultMap;
    }

    public List<String[]> workTimePeriod(Date checkinTime,String jsonStr){
        JSONArray jsonArray=JSON.parseArray(jsonStr);
        List<String[]> workDateList=new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            String[] dates=new String[2];
            String workTime=jsonArray.getJSONArray(i).getString(0);
            String offWorkTime=jsonArray.getJSONArray(i).getString(1);
            String[] workTimeArray=workTime.split(":");
            String[] offWorkTimeArray=offWorkTime.split(":");
            Calendar workCalendar=Calendar.getInstance();
            workCalendar.setTime(checkinTime);
            workCalendar.set(Calendar.HOUR_OF_DAY,Integer.valueOf(workTimeArray[0]));
            workCalendar.set(Calendar.MINUTE,Integer.valueOf(workTimeArray[1]));
            workCalendar.set(Calendar.SECOND,0);
            dates[0]=DateUtils.formatDate(workCalendar.getTime(),"yyyy-MM-dd HH:mm:ss");

            Calendar offWorkCalendar=Calendar.getInstance();
            offWorkCalendar.setTime(checkinTime);
            offWorkCalendar.set(Calendar.HOUR_OF_DAY,Integer.valueOf(offWorkTimeArray[0]));
            offWorkCalendar.set(Calendar.MINUTE,Integer.valueOf(offWorkTimeArray[1]));
            offWorkCalendar.set(Calendar.SECOND,0);
            dates[1]=DateUtils.formatDate(offWorkCalendar.getTime(),"yyyy-MM-dd HH:mm:ss");
            workDateList.add(dates);
        }
        return workDateList;
    }

    public Map<String, Object> getEmpOverTime(String empId,Date checkinTime){
        Calendar initCalendar=Calendar.getInstance();
        initCalendar.setTime(checkinTime);
        initCalendar.set(Calendar.HOUR_OF_DAY, 23);
        initCalendar.set(Calendar.MINUTE, 59);
        initCalendar.set(Calendar.SECOND, 59);

        checkinTime=initCalendar.getTime();
        //获取昨天的上下班时间
        Date yesterDate=DateUtils.getNextDay(checkinTime,-1);
        Map<String,Object> yesterday=getEmpDayWorkTime(empId,yesterDate);
        //获取今天的上下班时间
        Map<String,Object> today=getEmpDayWorkTime(empId,checkinTime);
        //获取明天的上下班时间
        Date nextdayDate=DateUtils.getNextDay(checkinTime,1);
        Map<String,Object> nextday=getEmpDayWorkTime(empId,nextdayDate);

        //获取昨天的加班记录
        List<PersOrgEmployeeOverTimeApply> yesterdayOverTimeApplyList=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordListByStatus(empId,yesterDate,new String[]{"3"});
        //今天的加班记录
        List<PersOrgEmployeeOverTimeApply> todayOverTimeApplyList=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordListByStatus(empId,checkinTime,new String[]{"3"});
        //获取明天的加班记录
        List<PersOrgEmployeeOverTimeApply> nextdayOverTimeApplyList=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordListByStatus(empId,nextdayDate,new String[]{"3"});



        boolean yesterdayIsRest=(boolean) yesterday.get("isRest");
        Date yesterdayMinWork=(Date) yesterday.get("minWorkCheckinTime");
        Date yesterdayMaxOffWork=(Date) yesterday.get("maxOffWorkCheckinTime");

        boolean todayIsRest=(boolean) today.get("isRest");
        Date todayMinWork=(Date) today.get("minWorkCheckinTime");
        Date todayMaxOffWork=(Date) today.get("maxOffWorkCheckinTime");


        boolean nextdayIsRest=(boolean) nextday.get("isRest");
        Date nextdayMinWork=(Date) nextday.get("minWorkCheckinTime");
        Date nextdayMaxOffWork=(Date) nextday.get("maxOffWorkCheckinTime");

        //今天是否公休
        boolean todayGeneralHoliday=false;
        //今天是否加班
        boolean todayIsOvertime=false;
        //今天是否请休假
        boolean todayIsLeave=false;
        //今天是否外派
        boolean todayIsDispatch=false;
        //今天是否出差
        boolean todayIsBusinessTrip=false;

        //今天初始最小上班时间
        Date todayInitMinWork=todayMinWork;
        //今天初始最大下班时间
        Date todayInitMaxOffWork=todayMaxOffWork;

        List<List<Map<String,Date>>> overTimesList=new ArrayList<>();

        //属于今天的加班记录
        List<PersOrgEmployeeOverTimeApply> actualTodayOverTimeApplyList=new ArrayList<>();

        if(yesterdayIsRest){

            //昨天休息，获取昨天加班记录,加班开始结束时间作为昨天的最小上班时间和最大下班时间
            if(yesterdayOverTimeApplyList!=null && yesterdayOverTimeApplyList.size()>0){
                yesterdayIsRest=false;
                yesterdayMinWork=yesterdayOverTimeApplyList.get(0).getStartTime();
                yesterdayMaxOffWork=yesterdayOverTimeApplyList.get(yesterdayOverTimeApplyList.size()-1).getEndTime();
            }
        }else{
            //如果昨天为工作日，则获取最后一条的加班记录的下班时间作为
            if(yesterdayOverTimeApplyList!=null && yesterdayOverTimeApplyList.size()>0){

                Date lastOverTimeEndTime=yesterdayOverTimeApplyList.get(yesterdayOverTimeApplyList.size()-1).getEndTime();
                if(todayIsRest){
                    if(yesterdayMaxOffWork==null || yesterdayMaxOffWork.getTime()<lastOverTimeEndTime.getTime()){
                        yesterdayMaxOffWork=lastOverTimeEndTime;
                    }
                }else{
                    Date middleDate=getMiddleDate(yesterdayMaxOffWork,todayMinWork);

                    for (PersOrgEmployeeOverTimeApply overTimeApply : yesterdayOverTimeApplyList) {

                        if(overTimeApply.getStartTime().getTime()<middleDate.getTime()){
                            //属于昨天
                            if(yesterdayMaxOffWork==null || yesterdayMaxOffWork.getTime()<overTimeApply.getEndTime().getTime()){
                                yesterdayMaxOffWork=overTimeApply.getEndTime();
                            }
                        }else{
                            //属于今天
                            if(todayMinWork==null || todayMinWork.getTime()>overTimeApply.getStartTime().getTime()){
                                todayMinWork=overTimeApply.getStartTime();
                            }
                            actualTodayOverTimeApplyList.add(overTimeApply);
                        }
                    }


                }
            }
        }

        if(todayIsRest){
            todayGeneralHoliday=true;
            //今天休息，获取今天加班记录,加班开始结束时间作为今天的最小上班时间和最大下班时间
            if(todayOverTimeApplyList!=null && todayOverTimeApplyList.size()>0){
                todayIsRest=false;
                if(todayMinWork==null || todayMinWork.getTime()<todayOverTimeApplyList.get(0).getStartTime().getTime()){
                    todayMinWork=todayOverTimeApplyList.get(0).getStartTime();
                }
                if(todayMaxOffWork==null || todayMaxOffWork.getTime()<todayOverTimeApplyList.get(todayOverTimeApplyList.size()-1).getEndTime().getTime()){
                    todayMaxOffWork=todayOverTimeApplyList.get(todayOverTimeApplyList.size()-1).getEndTime();
                }
                actualTodayOverTimeApplyList.addAll(todayOverTimeApplyList);
            }
        }else{

            if(todayOverTimeApplyList!=null && todayOverTimeApplyList.size()>0){

                for (PersOrgEmployeeOverTimeApply overTimeApply : todayOverTimeApplyList) {
                    if(yesterdayIsRest && nextdayIsRest){
                        if(todayMinWork==null || todayMinWork.getTime()<todayOverTimeApplyList.get(0).getStartTime().getTime()){
                            todayMinWork=todayOverTimeApplyList.get(0).getStartTime();
                        }
                        if(todayMaxOffWork==null || todayMaxOffWork.getTime()<todayOverTimeApplyList.get(todayOverTimeApplyList.size()-1).getEndTime().getTime()){
                            todayMaxOffWork=todayOverTimeApplyList.get(todayOverTimeApplyList.size()-1).getEndTime();
                        }
                        actualTodayOverTimeApplyList.addAll(todayOverTimeApplyList);
                        break;
                    }else if(!yesterdayIsRest && nextdayIsRest ){
                        Date middleDate=getMiddleDate(yesterdayMaxOffWork,todayMinWork);
                        if(overTimeApply.getStartTime().getTime()<middleDate.getTime()){
                            //属于昨天的加班记录
                            if(overTimeApply.getEndTime().getTime()>yesterdayMaxOffWork.getTime()){
                                yesterdayMaxOffWork=overTimeApply.getEndTime();
                            }
                        }else{
                            //属于今天的加班记录
                            if(todayMinWork==null || todayMinWork.getTime()>overTimeApply.getStartTime().getTime()){
                                todayMinWork=overTimeApply.getStartTime();
                            }
                            if(todayMaxOffWork==null || todayMaxOffWork.getTime()<overTimeApply.getEndTime().getTime()){
                                todayMaxOffWork=overTimeApply.getEndTime();
                            }
                            actualTodayOverTimeApplyList.add(overTimeApply);
                        }

                    }else if(yesterdayIsRest && !nextdayIsRest){
                        Date middleDate=getMiddleDate(todayMaxOffWork,nextdayMinWork);
                        if(middleDate.getTime()<overTimeApply.getStartTime().getTime()){
                            //属于明天的加班记录
                            if(overTimeApply.getStartTime().getTime()<nextdayMinWork.getTime()){
                                nextdayMinWork=overTimeApply.getStartTime();
                            }
                        }else{
                            //属于今天的加班记录
                            if(todayMinWork==null || todayMinWork.getTime()>overTimeApply.getStartTime().getTime()){
                                todayMinWork=overTimeApply.getStartTime();
                            }
                            if(todayMaxOffWork==null || todayMaxOffWork.getTime()<overTimeApply.getEndTime().getTime()){
                                todayMaxOffWork=overTimeApply.getEndTime();
                            }
                            actualTodayOverTimeApplyList.add(overTimeApply);
                        }

                    }else if(!yesterdayIsRest && !nextdayIsRest){
                        Date yesterdayMiddleDate=getMiddleDate(yesterdayMaxOffWork,todayMinWork);
                        Date nextdayMiddleDate=getMiddleDate(todayMaxOffWork,nextdayMinWork);
                        //11-07 23:00:00       11-07 01:15:00                                     11-07 23:00:00     11-07 21:00:00
                        if(overTimeApply.getStartTime().getTime()>yesterdayMiddleDate.getTime() && overTimeApply.getStartTime().getTime()<nextdayMiddleDate.getTime()){
                            //属于今天的加班记录
                            if(todayMinWork==null || todayMinWork.getTime()>overTimeApply.getStartTime().getTime()){
                                todayMinWork=overTimeApply.getStartTime();
                            }
                            if(todayMaxOffWork==null || todayMaxOffWork.getTime()<overTimeApply.getEndTime().getTime()){
                                todayMaxOffWork=overTimeApply.getEndTime();
                            }
                            actualTodayOverTimeApplyList.add(overTimeApply);
                        }else{
                            if(yesterdayMiddleDate.getTime()>overTimeApply.getStartTime().getTime()){
                                //昨天的
                                if(yesterdayMaxOffWork.getTime()<overTimeApply.getEndTime().getTime()){
                                    yesterdayMaxOffWork=overTimeApply.getEndTime();
                                }
                            }else if(overTimeApply.getStartTime().getTime()>nextdayMiddleDate.getTime()){
                                //明天的
                                if(nextdayMinWork.getTime()>overTimeApply.getStartTime().getTime()){
                                    nextdayMinWork=overTimeApply.getStartTime();
                                }
                            }

                        }

                    }

                }

            }
        }

        if(nextdayIsRest){
            if(nextdayOverTimeApplyList!=null && nextdayOverTimeApplyList.size()>0){
                nextdayMinWork=nextdayOverTimeApplyList.get(0).getStartTime();
                nextdayMaxOffWork=nextdayOverTimeApplyList.get(nextdayOverTimeApplyList.size()-1).getEndTime();
            }
        }else{
            if(nextdayOverTimeApplyList!=null && nextdayOverTimeApplyList.size()>0){
                Date firstOverTimeStartTime=nextdayOverTimeApplyList.get(0).getStartTime();
                if(todayIsRest){
                    if(nextdayMinWork==null || nextdayMinWork.getTime()>firstOverTimeStartTime.getTime()){
                        nextdayMinWork=firstOverTimeStartTime;
                    }
                }else{
                    Date middleDate=getMiddleDate(todayMaxOffWork,nextdayMinWork);
                    for (PersOrgEmployeeOverTimeApply overTimeApply : nextdayOverTimeApplyList) {

                        if(overTimeApply.getStartTime().getTime()<middleDate.getTime()){
                            //属于今天的加班记录
                            if(todayMaxOffWork.getTime()<overTimeApply.getEndTime().getTime()){
                                todayMaxOffWork=overTimeApply.getEndTime();
                            }
                            actualTodayOverTimeApplyList.add(overTimeApply);
                        }else{
                            //属于明天的加班记录
                            if(nextdayMinWork.getTime()>overTimeApply.getStartTime().getTime()){
                                nextdayMinWork=overTimeApply.getStartTime();
                            }

                        }

                    }


                }
            }

        }



        if(actualTodayOverTimeApplyList.size()>0){
            todayIsOvertime=true;
            for (PersOrgEmployeeOverTimeApply overTimeApply : actualTodayOverTimeApplyList) {
                List<Map<String, Date>> overTimesWorkTimePeriod = getOverTimesWorkTimePeriod(overTimeApply);
                overTimesList.add(overTimesWorkTimePeriod);
            }
        }



        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("empId",empId);
        //昨天是否休息
        resultMap.put("yesterdayIsRest",yesterdayIsRest);
        //昨天最小上班打卡时间
        resultMap.put("yesterdayMinWork",yesterdayMinWork);
        //昨天最大下班时间
        resultMap.put("yesterdayMaxOffWork",yesterdayMaxOffWork);

        //今天是否休息
        resultMap.put("todayIsRest",todayIsRest);
        //今天打卡规则
        resultMap.put("checkinRule",today.get("checkinRule"));

        //今天是否下班不用打卡
        resultMap.put("noneedOffwork",today.get("noneedOffwork"));
        //今天是否多打卡
        resultMap.put("isManyCheckin",today.get("isManyCheckin"));


        resultMap.put("todayMinWork",todayMinWork);
        resultMap.put("todayMaxOffWork",todayMaxOffWork);
        resultMap.put("isRest",today.get("isRest"));
        resultMap.put("isOvertime",false);

        //打卡类型
        resultMap.put("ruleType",today.get("ruleType"));

        if((boolean)today.get("isRest")){
            if(!todayIsRest){
                resultMap.put("isOvertime",true);
            }
        }
        //今天加班时间段
        resultMap.put("todayOverTimes",overTimesList);

        //今天原始上班时间段
        resultMap.put("workTimePeriod",today.get("workTimePeriod"));
        //计算1天上班时间长
        /*if("1".equals(resultMap.get("ruleType")) || "3".equals(resultMap.get("ruleType"))){

        }else{


        }*/
        String deptId= Db.queryStr(" select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? and is_main='1' limit 1 ",empId);
        //获取员工组织机构上的上班小时
        double[] hourArray=persOrgService.getDeptWorkHour(deptId);
        double totalHour=hourArray[0]+hourArray[1];
        //1天实际工作小时
        resultMap.put("dayWorkHour",totalHour);

        //明天是否休息
        resultMap.put("nextdayIsRest",nextdayIsRest);
        //明天最小上班时间
        resultMap.put("nextdayMinWork",nextdayMinWork);
        //明天最大下班时间
        resultMap.put("nextdayMaxOffWork",nextdayMaxOffWork);


        //获取请休假时间段
        List<Record> leaveRecordList=getEmpLeaveRestApply(empId,resultMap);
        System.out.println(JSON.toJSONString(leaveRecordList));
        //今天休息时间段
        resultMap.put("leaveRecordList",leaveRecordList);

        List<Record> overTimeRecordList=new ArrayList<>();
        if(actualTodayOverTimeApplyList.size()>0){
            //处理加班记录
            overTimeRecordList=getEmpOverTimesApply(actualTodayOverTimeApplyList);
        }
        //今天加班时间段
        resultMap.put("overTimeRecordList",overTimeRecordList);

        List<Map<String,Date>> leaveDateMapList=new ArrayList<>();
        boolean todayIsLeaveAllDay=false;

        if(leaveRecordList!=null && leaveRecordList.size()>0){
            todayIsLeave=true;
            for (Record record : leaveRecordList) {
                Map<String,Date> leaveDateMap=new HashMap<>();
                if("1".equals(record.getStr("leaveDateType"))){
                    //休息全天
                    todayIsLeaveAllDay=true;

                    break;
                }else{
                    leaveDateMap.put("startDate",record.getDate("startTime"));
                    leaveDateMap.put("endDate",record.getDate("endTime"));
                    leaveDateMapList.add(leaveDateMap);
                }
            }
        }

        //今天是否加班
        resultMap.put("todayIsOvertime",todayIsOvertime);
        //今天是否请休假
        resultMap.put("todayIsLeave",todayIsLeave);
        //今天是否休息
        resultMap.put("todayGeneralHoliday",todayGeneralHoliday);
        //今天是否休全天
        resultMap.put("todayIsLeaveAllDay",todayIsLeaveAllDay);
        //所属日期
        resultMap.put("summaryDate",checkinTime);

        //会合上班时间和加班时间
        //today.get("workTimePeriod");
        List<Map<String,Date>> workTimePeriodMapList=new ArrayList<>();
        List<String[]> workTimePeriodStrArrayList=(List<String[]>)today.get("workTimePeriod");
        if(workTimePeriodStrArrayList!=null){
            for (String[] dateStrs : workTimePeriodStrArrayList) {
                Map<String,Date> dateMap=new HashMap<>();
                dateMap.put("startDate",DateUtils.parseDate(dateStrs[0]));
                dateMap.put("endDate",DateUtils.parseDate(dateStrs[1]));
                workTimePeriodMapList.add(dateMap);
            }
        }


        if(todayIsLeaveAllDay){
            //休全天
            workTimePeriodMapList.removeAll(workTimePeriodMapList);
        }else{
            //未处理初始上班时间段数
            int oldWorkTimePeriodMapListSize=workTimePeriodMapList.size();
            //未处理请休假时间段数
            int oldLeaveDateMapListSize=leaveDateMapList.size();
            //休小时
            //剔除请休假时间
            workTimePeriodMapList=getTimePeriodListDumplictcatePeriod(workTimePeriodMapList,leaveDateMapList);
            //按小时请假请全天的情况
            if(oldWorkTimePeriodMapListSize>0 && oldLeaveDateMapListSize>0 && workTimePeriodMapList.size()==0){
                //今天是否休全天
                resultMap.put("todayIsLeaveAllDay",true);
            }
        }


        //添加加班时间段
        for (List<Map<String, Date>> mapList : overTimesList) {
            workTimePeriodMapList.addAll(mapList);
        }
        List<Map<String,Date>> actualWorkTimeMapList=getTimePeriodListDumplictcatePeriod(workTimePeriodMapList);



        //实际上班时间段（上班+加班）
        resultMap.put("actualWorkTimeMapList",actualWorkTimeMapList);


        //实际打卡时间段（未处理出差）
        List<Map<String,Date>> workTimeCheckinMapList=new ArrayList<>();
        workTimeCheckinMapList.addAll(actualWorkTimeMapList);
        resultMap.put("workTimeCheckinMapList",workTimeCheckinMapList);

        List<Map<String,Date>> actualWorkTimeCheckinMapList=new ArrayList<>();
        actualWorkTimeCheckinMapList.addAll(actualWorkTimeMapList);
        resultMap.put("actualWorkTimeCheckinMapList",actualWorkTimeCheckinMapList);
        //获取出差时间段
        List<Record> empBusinessTripDateList = getEmpBusinessTripApply(empId, resultMap);
        resultMap.put("empBusinessTripDateList",empBusinessTripDateList);
        //今天是否全天出差
        boolean todayIsBusinessTripAllDay=false;
        if(empBusinessTripDateList!=null && empBusinessTripDateList.size()>0){
            todayIsBusinessTrip=true;
            List<Map<String,Date>> businessTripDateList=new ArrayList<>();
            for (Record record : empBusinessTripDateList) {
                if("1".equals(record.get("dateType"))){
                    //全天出差
                    resultMap.put("actualWorkTimeCheckinMapList",new ArrayList<>());
                    todayIsBusinessTripAllDay=true;
                    break;
                }else{
                    Map<String,Date> dateMap=new HashMap<>();
                    dateMap.put("startDate",record.getDate("startTime"));
                    dateMap.put("endDate",record.getDate("endTime"));
                    businessTripDateList.add(dateMap);
                }
            }
            if(businessTripDateList.size()>0){

                List<Map<String, Date>> copuWorkTimeCheckinMapList=new ArrayList<>();
                for (Map<String, Date> dateMap : workTimeCheckinMapList) {
                    Map<String, Date> newDateMap=new HashMap<>();
                    newDateMap.put("startDate",dateMap.get("startDate"));
                    newDateMap.put("endDate",dateMap.get("endDate"));
                    copuWorkTimeCheckinMapList.add(newDateMap);
                }

                List<Map<String, Date>> newActualWorkTimeCheckinMapList = getTimePeriodListDumplictcatePeriod(copuWorkTimeCheckinMapList, businessTripDateList);
                //按小时出差全天的情况
                if(copuWorkTimeCheckinMapList.size()>0 && businessTripDateList.size()>0 && newActualWorkTimeCheckinMapList.size()==0){
                    //今天是否全天出差
                    todayIsBusinessTripAllDay=true;
                }
                resultMap.put("actualWorkTimeCheckinMapList",newActualWorkTimeCheckinMapList);
            }
        }
        //今天是否出差
        resultMap.put("todayIsBusinessTrip",todayIsBusinessTrip);
        //今天是否全天出差
        resultMap.put("todayIsBusinessTripAllDay",todayIsBusinessTripAllDay);


        //获取外派时间段
        //今天是否全天外派
        boolean todayIsDispatchAllDay=false;
        List<Record> empDispatchDateList = getEmpDispatchApplyApply(empId, resultMap);
        resultMap.put("empDispatchDateList",empDispatchDateList);
        if(empDispatchDateList.size()>0){
            todayIsDispatch=true;

            for (Record record : empDispatchDateList) {
                if("1".equals(record.getStr("dateType")) && !todayIsDispatchAllDay){
                    todayIsDispatchAllDay=true;
                    break;
                }
            }
        }
        //今天是否外派
        resultMap.put("todayIsDispatch",todayIsDispatch);
        resultMap.put("todayIsDispatchAllDay",todayIsDispatchAllDay);
        //处理外派时间段



        //计算今天最大最小和最大打卡时间范围
        Date todayMinCheckinTime=null;
        Date todayMaxCheckinTime=null;
        if(todayMinWork==null && todayMaxOffWork==null){
            //今天休息
            if(yesterdayMaxOffWork!=null){
                //昨天上班最大打卡时间
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(yesterdayMaxOffWork);
                calendar.add(Calendar.HOUR_OF_DAY,8);
                todayMinCheckinTime=calendar.getTime();
            }else{
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(checkinTime);
                calendar.add(Calendar.DAY_OF_MONTH,-1);
                calendar.set(Calendar.HOUR_OF_DAY, 12);
                calendar.set(Calendar.MINUTE, 00);
                calendar.set(Calendar.SECOND, 00);

                todayMinCheckinTime=calendar.getTime();
            }
            if(nextdayMinWork!=null){
                //明天上班最小打卡时间
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(nextdayMinWork);
                calendar.add(Calendar.HOUR_OF_DAY,-8);
                todayMaxCheckinTime=calendar.getTime();
            }else{
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(checkinTime);
                calendar.add(Calendar.DAY_OF_MONTH,1);
                calendar.set(Calendar.HOUR_OF_DAY, 12);
                calendar.set(Calendar.MINUTE, 00);
                calendar.set(Calendar.SECOND, 00);
                todayMaxCheckinTime=calendar.getTime();
            }
            if(todayMinCheckinTime!=null && todayMaxCheckinTime!=null){
                //检查是否交叉
                if(todayMinCheckinTime.getTime()>todayMaxCheckinTime.getTime()){
                    Calendar calendar=Calendar.getInstance();
                    calendar.setTime(todayMinCheckinTime);
                    calendar.add(Calendar.HOUR_OF_DAY,-4);
                    todayMinCheckinTime=calendar.getTime();

                    Calendar calendar2=Calendar.getInstance();
                    calendar2.setTime(todayMaxCheckinTime);
                    calendar2.add(Calendar.HOUR_OF_DAY,4);
                    todayMaxCheckinTime=calendar2.getTime();
                }
            }
        }else{
            //今天需要打卡
            if(yesterdayMaxOffWork!=null){
                Date middleDate = getMiddleDate(yesterdayMaxOffWork, todayMinWork);
                todayMinCheckinTime=middleDate;
            }else{
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(todayMinWork);
                calendar.add(Calendar.HOUR_OF_DAY,-8);
                todayMinCheckinTime=calendar.getTime();
            }

            if(nextdayMinWork!=null){
                Date middleDate = getMiddleDate(todayMaxOffWork, nextdayMinWork);
                todayMaxCheckinTime=middleDate;
            }else{
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(todayMaxOffWork);
                calendar.add(Calendar.HOUR_OF_DAY,8);
                todayMaxCheckinTime=calendar.getTime();
            }
        }

        resultMap.put("todayMinCheckinTime",todayMinCheckinTime);
        resultMap.put("todayMaxCheckinTime",todayMaxCheckinTime);
        return resultMap;
    }

    public List<Map<String,Date>> getOverTimesWorkTimePeriod(PersOrgEmployeeOverTimeApply overTimeApply){
        List<Map<String,Date>> overWorkTimeList=new ArrayList<>();
        Map<String,Date> map=new HashMap<>();
        map.put("startDate",overTimeApply.getStartTime());
        map.put("endDate",overTimeApply.getEndTime());
        overWorkTimeList.add(map);
        if(StrKit.notBlank(overTimeApply.getRestTimes()) && JSON.parseArray(overTimeApply.getRestTimes())!=null && JSON.parseArray(overTimeApply.getRestTimes()).size()>0){
            List<Map<String,Date>> overRestTimeList=new ArrayList<>();
            JSONArray restTimesArray=JSON.parseArray(overTimeApply.getRestTimes());
            for (int i = 0; i < restTimesArray.size(); i++) {
                JSONObject object=restTimesArray.getJSONObject(i);
                Map<String,Date> restTimesMap=new HashMap<>();
                restTimesMap.put("startDate",DateUtils.parseDate(object.getString("restStartTime")));
                restTimesMap.put("endDate",DateUtils.parseDate(object.getString("restEndTime")));
                overRestTimeList.add(restTimesMap);
            }
            List<Map<String, Date>> periodList3=getTimePeriodListDumplictcatePeriod(overWorkTimeList,overRestTimeList);
            return periodList3;
        }
        return overWorkTimeList;
    }




    /**
     * 获取今天请休假时间
     * @param empId
     * @param todayMap
     * @return
     */
    public List<Record> getEmpLeaveRestApply(String empId,Map<String,Object> todayMap){
        boolean todayIsRest=(boolean)todayMap.get("todayIsRest");
        Date todayMinWork=(Date)todayMap.get("todayMinWork");
        Date todayMaxOffWork=(Date)todayMap.get("todayMaxOffWork");
        boolean isOvertime=(boolean)todayMap.get("isOvertime");
        List<String[]> workTimePeriodList=(List<String[]>)todayMap.get("workTimePeriod");

        //List<Map<String,Date>>

        List<Record> restTimeList=new ArrayList<>();


        List<PersOrgEmployeeLeaveRestApplyDate> leaveRestApplyDateList=persOrgEmployeeLeaveRestApplyDateService.getEmpLeaveRestApplyDateListByDateRange(empId,todayMinWork,todayMaxOffWork,new String[]{"3"});
        if(leaveRestApplyDateList!=null && leaveRestApplyDateList.size()>0){

            for (PersOrgEmployeeLeaveRestApplyDate leaveRestApplyDate : leaveRestApplyDateList) {
                if(!PersLeaveRestType.lactationLeave.getKey().equals(leaveRestApplyDate.getLeaveRestType())){
                    //哺乳假排除
                    //restTimeList.add();



                    if("1".equals(leaveRestApplyDate.getLeaveRestDateType())){
                        //按天请休假
                        Record record=new Record();
                        record.set("leaveDateType",leaveRestApplyDate.getLeaveRestDateType());
                        record.set("leaveType",leaveRestApplyDate.getLeaveRestType());
                        restTimeList.add(record);
                    }else{
                        //按小时请休假

                        JSONArray restTimeArray=null;
                        if(StrKit.notBlank(leaveRestApplyDate.getRestTimes()) && leaveRestApplyDate.getRestTimes().startsWith("[") && leaveRestApplyDate.getRestTimes().endsWith("]")){
                            restTimeArray=JSON.parseArray(leaveRestApplyDate.getRestTimes());
                        }
                        //判断是否有休息时间
                        if(restTimeArray!=null && restTimeArray.size()>0){
                            List<Map<String,Date>> restTimeMapList=new ArrayList<>();
                            for (int i = 0; i < restTimeArray.size(); i++) {
                                JSONObject restTimeObj=restTimeArray.getJSONObject(i);
                                Map<String,Date> dateMap=new HashMap<>();
                                dateMap.put("startDate",restTimeObj.getDate("restStartTime"));
                                dateMap.put("endDate",restTimeObj.getDate("restEndTime"));
                                restTimeMapList.add(dateMap);
                            }
                            List<Map<String,Date>> leaveRestTimeMapList=new ArrayList<>();
                            Map<String,Date> leaveRestTimeMap=new HashMap<>();
                            leaveRestTimeMap.put("startDate",leaveRestApplyDate.getStartTime());
                            leaveRestTimeMap.put("endDate",leaveRestApplyDate.getEndTime());
                            leaveRestTimeMapList.add(leaveRestTimeMap);

                            List<Map<String, Date>> timePeriodListDumplictcatePeriod = getTimePeriodListDumplictcatePeriod(leaveRestTimeMapList, restTimeMapList);
                            for (Map<String, Date> dateMap : timePeriodListDumplictcatePeriod) {
                                Record record=new Record();
                                record.set("leaveType",leaveRestApplyDate.getLeaveRestType());
                                record.set("leaveDateType",leaveRestApplyDate.getLeaveRestDateType());

                                record.set("startTime",dateMap.get("startDate"));
                                record.set("endTime",dateMap.get("endDate"));
                                restTimeList.add(record);
                            }
                        }else{
                            Record record=new Record();
                            record.set("leaveType",leaveRestApplyDate.getLeaveRestType());
                            record.set("leaveDateType",leaveRestApplyDate.getLeaveRestDateType());
                            record.set("startTime",leaveRestApplyDate.getStartTime());
                            record.set("endTime",leaveRestApplyDate.getEndTime());
                            restTimeList.add(record);
                        }

                    }

                }else{
                    //哺乳假将下班时间提前一小时
                    if(workTimePeriodList!=null){
                        String[] lastWorkTimeArray=workTimePeriodList.get(workTimePeriodList.size()-1);
                        if(lastWorkTimeArray!=null){
                            Date lastOffWorkTime=DateUtils.parseDate(lastWorkTimeArray[1]);
                            Calendar lastOffWorkTimeCalendar=Calendar.getInstance();
                            lastOffWorkTimeCalendar.setTime(lastOffWorkTime);
                            lastOffWorkTimeCalendar.add(Calendar.HOUR_OF_DAY,-1);
                            lastWorkTimeArray[1]=DateUtils.formatDate(lastOffWorkTimeCalendar.getTime(),"yyyy-MM-dd HH:mm:ss");

                            Record record=new Record();
                            record.set("leaveType",leaveRestApplyDate.getLeaveRestType());
                            record.set("leaveDateType",leaveRestApplyDate.getLeaveRestDateType());
                            record.set("startTime",lastOffWorkTimeCalendar.getTime());
                            record.set("endTime",lastWorkTimeArray[1]);
                            restTimeList.add(record);
                        }
                    }
                }
            }

        }

        return restTimeList;
    }

    public List<Record> getEmpOverTimesApply(List<PersOrgEmployeeOverTimeApply> overTimeApplyList){
        List<Record> recordList=new ArrayList<>();
        if(overTimeApplyList!=null){

            for (PersOrgEmployeeOverTimeApply overTimeApply : overTimeApplyList) {

                if(StrKit.notBlank(overTimeApply.getRestTimes()) && overTimeApply.getRestTimes().startsWith("[") && overTimeApply.getRestTimes().endsWith("]") &&
                JSONArray.parseArray(overTimeApply.getRestTimes())!=null && JSONArray.parseArray(overTimeApply.getRestTimes()).size()>0){
                    JSONArray restTimeArray=JSONArray.parseArray(overTimeApply.getRestTimes());
                    List<Map<String,Date>> restTimeMapList=new ArrayList<>();
                    for (int i = 0; i < restTimeArray.size(); i++) {
                        JSONObject restTimeObj=restTimeArray.getJSONObject(i);
                        Map<String,Date> dateMap=new HashMap<>();
                        dateMap.put("startDate",restTimeObj.getDate("restStartTime"));
                        dateMap.put("endDate",restTimeObj.getDate("restEndTime"));
                        restTimeMapList.add(dateMap);
                    }
                    List<Map<String,Date>> overTimeMapList=new ArrayList<>();
                    Map<String,Date> overTimeMap=new HashMap<>();
                    overTimeMap.put("startDate",overTimeApply.getStartTime());
                    overTimeMap.put("endDate",overTimeApply.getEndTime());
                    overTimeMapList.add(overTimeMap);

                    List<Map<String, Date>> timePeriodListDumplictcatePeriod = getTimePeriodListDumplictcatePeriod(overTimeMapList, restTimeMapList);
                    for (Map<String, Date> dateMap : timePeriodListDumplictcatePeriod) {
                        Record record=new Record();
                        record.set("overSecond",overTimeApply.getOverSecond());
                        record.set("overType",overTimeApply.getOverType());
                        record.set("startTime",dateMap.get("startDate"));
                        record.set("endTime",dateMap.get("endDate"));
                        recordList.add(record);
                    }
                }else{
                    Record record=new Record();
                    record.set("startTime",overTimeApply.getStartTime());
                    record.set("endTime",overTimeApply.getEndTime());
                    record.set("overSecond",overTimeApply.getOverSecond());
                    record.set("overType",overTimeApply.getOverType());
                    recordList.add(record);
                }

            }

        }
        return recordList;
    }

    /**
     * 获取今天出差时间
     * @param empId
     * @param todayMap
     * @return
     */
    public List<Record> getEmpBusinessTripApply(String empId,Map<String,Object> todayMap){
        List<Record> recordList=new ArrayList<>();
        Date todayMinWork=(Date)todayMap.get("todayMinWork");
        Date todayMaxOffWork=(Date)todayMap.get("todayMaxOffWork");


        List<PersOrgEmployeeBusinessTripApplyDate> businessTripApplyList=persOrgEmployeeBusinessTripApplyDateService.getEmpBusinessTripApplyDateListByDateRange(empId,todayMinWork,todayMaxOffWork);
        if(businessTripApplyList!=null && businessTripApplyList.size()>0){
            for (PersOrgEmployeeBusinessTripApplyDate businessTripApplyDate : businessTripApplyList) {
                if("1".equals(businessTripApplyDate.getDateType())){
                    //按天请出差
                    Record record=new Record();
                    record.set("dateType",businessTripApplyDate.getDateType());
                    record.set("destinationCity",businessTripApplyDate.getDestinationCity());
                    recordList.add(record);
                }else{
                    //按小时出差

                    Record record=new Record();
                    record.set("dateType",businessTripApplyDate.getDateType());
                    record.set("destinationCity",businessTripApplyDate.getDestinationCity());
                    record.set("startTime",businessTripApplyDate.getStartTime());
                    record.set("endTime",businessTripApplyDate.getEndTime());
                    recordList.add(record);
                }
            }
        }

        return recordList;
    }

    /**
     * 获取今天外派时间段
     * @param empId
     * @param todayMap
     * @return
     */
    public List<Record> getEmpDispatchApplyApply(String empId,Map<String,Object> todayMap){
        List<Record> recordList=new ArrayList<>();
        Date todayMinWork=(Date)todayMap.get("todayMinWork");
        Date todayMaxOffWork=(Date)todayMap.get("todayMaxOffWork");


        List<PersOrgEmployeeDispatchApplyDate> dispatchApplyList=persOrgEmployeeDispatchApplyDateService.getEmpDispatchApplyDateListByDateRange(empId,todayMinWork,todayMaxOffWork);
        if(dispatchApplyList!=null && dispatchApplyList.size()>0){
            for (PersOrgEmployeeDispatchApplyDate dispatchApplyDate : dispatchApplyList) {
                if("1".equals(dispatchApplyDate.getDateType())){
                    //按天外派
                    Record record=new Record();
                    record.set("dateType",dispatchApplyDate.getDateType());
                    record.set("dispatchDeptId",dispatchApplyDate.getDispatchDeptId());
                    recordList.add(record);
                }else{
                    //按小时外派
                    Record record=new Record();
                    record.set("dateType",dispatchApplyDate.getDateType());
                    record.set("dispatchDeptId",dispatchApplyDate.getDispatchDeptId());
                    record.set("startTime",dispatchApplyDate.getStartTime());
                    record.set("endTime",dispatchApplyDate.getEndTime());
                    recordList.add(record);
                }
            }
        }

        return recordList;
    }


    public static Date getMiddleDate(Date startDate,Date endDate){
        long diff=(endDate.getTime()-startDate.getTime())/2;
        return new Date(startDate.getTime()+diff);
    }

    public void empCheckin(PersOrgEmployeeCheckinWxRecord checkinWxRecord,Date checkinDay){

        Map<String, Object> todayMap = getEmpOverTime(checkinWxRecord.getEmpId(),checkinDay);
        Date checkinTime=checkinWxRecord.getCheckinTime();
        String empId=checkinWxRecord.getEmpId();
        //获取今天
        Date todayMinCheckinTime = (Date) todayMap.get("todayMinCheckinTime");
        Date todayMaxCheckinTime = (Date) todayMap.get("todayMaxCheckinTime");
        if(todayMinCheckinTime.getTime()<=checkinTime.getTime() && todayMaxCheckinTime.getTime()>checkinTime.getTime()){
            //属于今天的打卡记录

            todayMap.put("checkinDay",checkinDay);
            todayMap.put("checkinWxRecord",checkinWxRecord);
            //今天是否休息
            boolean todayGeneralHoliday=(boolean)todayMap.get("todayGeneralHoliday");
            //今天是否加班
            boolean todayIsOvertime=(boolean)todayMap.get("todayIsOvertime");
            //上下班打卡时间段
            List<Map<String,Date>> actualWorkTimeCheckinMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");
            if(todayGeneralHoliday && !todayIsOvertime){
                //休息无加班
                return;
            }
            if(todayMap.get("todayIsLeaveAllDay")!=null && (boolean)todayMap.get("todayIsLeaveAllDay") && actualWorkTimeCheckinMapList.size()==0){
                //全天请假不需要打卡
                return;
            }
            if(todayMap.get("todayIsBusinessTripAllDay")!=null && (boolean)todayMap.get("todayIsBusinessTripAllDay")){
                //全天出差不需要打卡
                return;
            }
            //处理打卡记录
            genEmpCheckinRecord(empId,todayMap);

            //生成日报

        }else{
            if(todayMinCheckinTime.getTime()>checkinTime.getTime()){
                //属于昨天的打卡记录
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(checkinDay);
                calendar.add(Calendar.DATE,-1);
                empCheckin(checkinWxRecord,calendar.getTime());
            }else if(todayMaxCheckinTime.getTime()<=checkinTime.getTime()){
                //属于明天的打卡记录
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(checkinDay);
                calendar.add(Calendar.DATE,1);
                empCheckin(checkinWxRecord,calendar.getTime());
            }
        }

    }

    public void genEmpCheckinRecord(String empId,Map<String,Object> todayMap){
        PersOrgEmployeeCheckinWxRecord checkinWxRecord=(PersOrgEmployeeCheckinWxRecord)todayMap.get("checkinWxRecord");
        PersOrgEmployeeCheckinRule checkinRule=(PersOrgEmployeeCheckinRule)todayMap.get("checkinRule");
        if(checkinRule!=null){
            checkinWxRecord.setGroupid(checkinRule.getId());
            checkinWxRecord.setGroupname(checkinRule.getName());
        }

        //判断该时间是否存在打卡记录
        if(persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByTime(empId,checkinWxRecord.getCheckinTime())!=null){
            //该打卡时间已经存在打卡记录
            return;
        }

        //所属日报日期
        Date summaryDate=(Date) todayMap.get("summaryDate");
        if("3".equals((String)todayMap.get("ruleType"))){
            //自由打卡
            Date todayMinCheckinTime=(Date) todayMap.get("todayMinWork");
            Date todayMaxCheckinTime=(Date) todayMap.get("todayMaxOffWork");


            //下班是否需要打卡
            String noneedOffwork=(String)todayMap.get("noneedOffwork");
            empCheckinRecord(checkinWxRecord,todayMinCheckinTime,todayMaxCheckinTime,noneedOffwork,summaryDate);
        }else{
            //是否多打卡
            String isManyCheckin=(String)todayMap.get("isManyCheckin");
            List<Map<String,Date>> actualWorkTimeMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");
            if("1".equals(isManyCheckin)){
                //最小上班打卡
                Date todayMinCheckinTime=(Date) todayMap.get("todayMinCheckinTime");
                //最大下班打卡
                Date todayMaxCheckinTime=(Date) todayMap.get("todayMaxCheckinTime");
                //多打卡
                for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                    int index = actualWorkTimeMapList.indexOf(dateMap);
                    Date startDate=dateMap.get("startDate");
                    Date endDate=dateMap.get("endDate");
                    if(index==0){
                        if(index==actualWorkTimeMapList.size()-1){
                            //只有1条记录
                            if(todayMinCheckinTime.getTime()<=checkinWxRecord.getCheckinTime().getTime() && checkinWxRecord.getCheckinTime().getTime()<todayMaxCheckinTime.getTime()){
                                empCheckinRecord(checkinWxRecord,todayMinCheckinTime,startDate,endDate,todayMaxCheckinTime,summaryDate);
                                break;
                            }
                        }else{
                            Map<String, Date> newxtDateMap = actualWorkTimeMapList.get(index + 1);
                            Date nextStatDate = newxtDateMap.get("startDate");
                            Date maxOffWorkCheckinDate = getMiddleDate(endDate, nextStatDate);

                            if(todayMinCheckinTime.getTime()<=checkinWxRecord.getCheckinTime().getTime() && checkinWxRecord.getCheckinTime().getTime()<maxOffWorkCheckinDate.getTime()){
                                empCheckinRecord(checkinWxRecord,todayMinCheckinTime,startDate,endDate,maxOffWorkCheckinDate,summaryDate);
                                break;
                            }
                        }

                    }else if(index==actualWorkTimeMapList.size()-1){

                        Map<String, Date> lastDateMap = actualWorkTimeMapList.get(index - 1);
                        Date lastEndDate = lastDateMap.get("endDate");
                        Date minWorkCheckinDate = getMiddleDate(lastEndDate, startDate);
                        if(minWorkCheckinDate.getTime()<=checkinWxRecord.getCheckinTime().getTime() && checkinWxRecord.getCheckinTime().getTime()<todayMaxCheckinTime.getTime()){
                            empCheckinRecord(checkinWxRecord,minWorkCheckinDate,startDate,endDate,todayMaxCheckinTime,summaryDate);
                            break;
                        }
                    }else{
                        Map<String, Date> lastDateMap = actualWorkTimeMapList.get(index - 1);
                        Date lastEndDate = lastDateMap.get("endDate");
                        Map<String, Date> newxtDateMap = actualWorkTimeMapList.get(index + 1);
                        Date nextStatDate = newxtDateMap.get("startDate");
                        Date minWorkCheckinDate = getMiddleDate(lastEndDate, startDate);
                        Date maxOffWorkCheckinDate = getMiddleDate(endDate, nextStatDate);

                        if(minWorkCheckinDate.getTime()<=checkinWxRecord.getCheckinTime().getTime() && checkinWxRecord.getCheckinTime().getTime()<maxOffWorkCheckinDate.getTime()){
                            empCheckinRecord(checkinWxRecord,minWorkCheckinDate,startDate,endDate,maxOffWorkCheckinDate,summaryDate);
                            break;
                        }
                    }
                }
            }else{
                //非多打卡

                //最小上班打卡
                Date todayMinCheckinTime=(Date) todayMap.get("todayMinCheckinTime");
                //最大下班打卡
                Date todayMaxCheckinTime=(Date) todayMap.get("todayMaxCheckinTime");
                //上班打卡
                Date checkinStartDate=actualWorkTimeMapList.get(0).get("startDate");
                //下班打卡
                Date checkinEndDate=actualWorkTimeMapList.get(actualWorkTimeMapList.size()-1).get("endDate");

                empCheckinRecord(checkinWxRecord,todayMinCheckinTime,checkinStartDate,checkinEndDate,todayMaxCheckinTime,summaryDate);
            }

        }
    }

    /**
     * 固定上下班时间和排班规则 打卡记录生成
     * @param checkinWxRecord
     * @param minCheckinTime 最小开始打卡时间
     * @param checkinStartTime 标准打卡开始时间
     * @param checkinEndTime 标准打卡结束时间
     * @param maxCheckinTime 最大结束打卡时间
     */
    public void empCheckinRecord(PersOrgEmployeeCheckinWxRecord checkinWxRecord,Date minCheckinTime,Date checkinStartTime,Date checkinEndTime,Date maxCheckinTime,Date summaryDate){

        String empId=checkinWxRecord.getEmpId();
        Date checkinTime=checkinWxRecord.getCheckinTime();

        Date currDate=new Date();
        PersOrgEmployeeCheckinRecord newCheckinRecord=new PersOrgEmployeeCheckinRecord();

        newCheckinRecord.setId(IdGen.getUUID());
        newCheckinRecord.setCheckinTime(checkinTime);
        newCheckinRecord.setEmpId(empId);
        newCheckinRecord.setDelFlag("0");
        newCheckinRecord.setUpdateDate(currDate);
        newCheckinRecord.setCreateDate(currDate);
        newCheckinRecord.setLocationTitle(checkinWxRecord.getLocationTitle());
        newCheckinRecord.setLocationDetail(checkinWxRecord.getLocationDetail());
        newCheckinRecord.setGroupid(checkinWxRecord.getGroupid());
        newCheckinRecord.setGroupname(checkinWxRecord.getGroupname());
        newCheckinRecord.setSummaryDate(summaryDate);


        if(checkinTime.getTime()>=minCheckinTime.getTime() && checkinTime.getTime()<=checkinStartTime.getTime()){
            //正常上班打卡
            //获取有没有更早的上班打卡记录
            //PersOrgEmployeeCheckinRecord workCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, checkinEndTime,"上班打卡");
            //PersOrgEmployeeCheckinRecord offWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, checkinStartTime, maxCheckinTime,"下班打卡");

            PersOrgEmployeeCheckinRecord existWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, checkinEndTime,"上班打卡");
            PersOrgEmployeeCheckinRecord existOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, checkinStartTime, maxCheckinTime,"下班打卡");
            PersOrgEmployeeCheckinRecord workCheckinRecord=null;
            PersOrgEmployeeCheckinRecord offWorkCheckinRecord=null;
            if(existWorkCheckinRecord!=null && !"未打卡".equals(existWorkCheckinRecord.getExceptionType())){
                workCheckinRecord=existWorkCheckinRecord;
            }
            if(existOffWorkCheckinRecord!=null && !"未打卡".equals(existOffWorkCheckinRecord.getExceptionType())){
                offWorkCheckinRecord=existOffWorkCheckinRecord;
            }

            if(workCheckinRecord!=null){
                //存在上班打卡记录
                if(workCheckinRecord.getCheckinTime().getTime()>checkinStartTime.getTime()){
                    //如果已存在的上班打卡记录超过标准上班打卡时间则改成为下班打卡
                    if(offWorkCheckinRecord==null){
                        workCheckinRecord.setCheckinType("下班打卡");
                        if(dateDiffMinute(workCheckinRecord.getCheckinTime(),checkinEndTime)>0){
                            //早退时间超过1分钟
                            workCheckinRecord.setExceptionType("时间异常");
                        }
                        workCheckinRecord.setSchCheckinTime(checkinEndTime);
                        workCheckinRecord.update();

                        newCheckinRecord.setSchCheckinTime(checkinStartTime);
                        newCheckinRecord.setCheckinType("上班打卡");
                        newCheckinRecord.save();
                    }else{
                        if(workCheckinRecord.getCheckinTime().getTime()>newCheckinRecord.getCheckinTime().getTime()){
                            workCheckinRecord.setDelFlag("1");
                            workCheckinRecord.setUpdateDate(new Date());
                            workCheckinRecord.update();

                            newCheckinRecord.setSchCheckinTime(checkinStartTime);
                            newCheckinRecord.setCheckinType("上班打卡");
                            newCheckinRecord.save();
                        }
                    }

                }else{
                    if(workCheckinRecord.getCheckinTime().getTime()>checkinTime.getTime()){
                        //新的打卡时间早于原上班打卡，则作废旧的保存新的
                        workCheckinRecord.setUpdateDate(currDate);
                        workCheckinRecord.setDelFlag("1");
                        workCheckinRecord.update();

                        newCheckinRecord.setSchCheckinTime(checkinStartTime);
                        newCheckinRecord.setCheckinType("上班打卡");
                        newCheckinRecord.save();
                    }else{
                        //原上班打卡记录正常状态不做处理

                    }

                }
            }else{
                //无打卡记录
                newCheckinRecord.setSchCheckinTime(checkinStartTime);
                newCheckinRecord.setCheckinType("上班打卡");
                newCheckinRecord.save();
            }

        }else if(checkinTime.getTime()>checkinStartTime.getTime() && checkinTime.getTime()<checkinEndTime.getTime()){
            //迟到/早退打卡

            //查询上班打卡记录
            PersOrgEmployeeCheckinRecord existWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, checkinEndTime, "上班打卡");
            //查询下班打卡记录
            PersOrgEmployeeCheckinRecord existOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, checkinStartTime, maxCheckinTime,"下班打卡");
            PersOrgEmployeeCheckinRecord workCheckinRecord=null;
            PersOrgEmployeeCheckinRecord offWorkCheckinRecord=null;
            if(existWorkCheckinRecord!=null && !"未打卡".equals(existWorkCheckinRecord.getExceptionType())){
                workCheckinRecord=existWorkCheckinRecord;
            }
            if(existOffWorkCheckinRecord!=null && !"未打卡".equals(existOffWorkCheckinRecord.getExceptionType())){
                offWorkCheckinRecord=existOffWorkCheckinRecord;
            }



            if(workCheckinRecord==null && offWorkCheckinRecord==null){
                //上班打卡
                newCheckinRecord.setSchCheckinTime(checkinStartTime);
                newCheckinRecord.setCheckinType("上班打卡");
                if(dateDiffMinute(checkinStartTime,newCheckinRecord.getCheckinTime())>0){
                    //迟到时间超过1分钟
                    newCheckinRecord.setExceptionType("时间异常");
                }
                newCheckinRecord.save();

            }else if(workCheckinRecord==null && offWorkCheckinRecord!=null){
                newCheckinRecord.setSchCheckinTime(checkinStartTime);
                newCheckinRecord.setCheckinType("上班打卡");
                if(dateDiffMinute(checkinStartTime,newCheckinRecord.getCheckinTime())>0){
                    if(dateDiffMinute(checkinStartTime,newCheckinRecord.getCheckinTime())>0){
                        //迟到时间超过1分钟
                        newCheckinRecord.setExceptionType("时间异常");
                    }
                }
                newCheckinRecord.save();

            }else if(workCheckinRecord!=null && offWorkCheckinRecord==null){

                if(checkinTime.getTime()<workCheckinRecord.getCheckinTime().getTime()){
                    newCheckinRecord.setSchCheckinTime(checkinStartTime);
                    newCheckinRecord.setCheckinType("上班打卡");
                    if(dateDiffMinute(checkinStartTime,newCheckinRecord.getCheckinTime())>0){
                        //迟到时间超过1分钟
                        newCheckinRecord.setExceptionType("时间异常");
                    }
                    newCheckinRecord.save();

                    workCheckinRecord.setCheckinType("下班打卡");
                    if(workCheckinRecord.getCheckinTime().getTime()<checkinEndTime.getTime()){
                        if(dateDiffMinute(workCheckinRecord.getCheckinTime(),checkinEndTime)>0){
                            //早退时间超过1分钟
                            workCheckinRecord.setExceptionType("时间异常");
                        }
                    }
                    workCheckinRecord.setSchCheckinTime(checkinEndTime);
                    workCheckinRecord.setUpdateDate(new Date());
                    workCheckinRecord.update();
                }else{
                    newCheckinRecord.setSchCheckinTime(checkinEndTime);
                    newCheckinRecord.setCheckinType("下班打卡");
                    if(dateDiffMinute(newCheckinRecord.getCheckinTime(),checkinEndTime)>0){
                        //早退时间超过1分钟
                        newCheckinRecord.setExceptionType("时间异常");
                    }
                    newCheckinRecord.save();
                }



            }else if(workCheckinRecord!=null && offWorkCheckinRecord!=null){
                if(workCheckinRecord.getCheckinTime().getTime()<checkinTime.getTime() && checkinTime.getTime()<offWorkCheckinRecord.getCheckinTime().getTime()){
                    //在已有上下班打卡记录中间不需要处理
                }else{

                    if(workCheckinRecord.getCheckinTime().getTime()>checkinTime.getTime()){
                        //添加为上班打卡记录
                        workCheckinRecord.setDelFlag("1");
                        workCheckinRecord.setUpdateDate(new Date());
                        workCheckinRecord.update();

                        newCheckinRecord.setSchCheckinTime(checkinStartTime);
                        newCheckinRecord.setCheckinType("上班打卡");
                        if(dateDiffMinute(checkinStartTime,newCheckinRecord.getCheckinTime())>0){
                            //迟到时间超过1分钟
                            newCheckinRecord.setExceptionType("时间异常");
                        }
                        newCheckinRecord.save();

                    }else if(checkinTime.getTime()>offWorkCheckinRecord.getCheckinTime().getTime()){
                        //添加为下班打卡记录
                        offWorkCheckinRecord.setDelFlag("1");
                        offWorkCheckinRecord.setUpdateDate(new Date());
                        offWorkCheckinRecord.update();

                        newCheckinRecord.setSchCheckinTime(checkinEndTime);
                        newCheckinRecord.setCheckinType("下班打卡");
                        if(dateDiffMinute(newCheckinRecord.getCheckinTime(),checkinEndTime)>0){
                            //早退时间超过1分钟
                            newCheckinRecord.setExceptionType("时间异常");
                        }
                        newCheckinRecord.save();
                    }

                }
            }

        }else if(checkinTime.getTime()>=checkinEndTime.getTime() && checkinTime.getTime()<maxCheckinTime.getTime()){
            //正常下班打卡

            //查询下班打卡记录
            PersOrgEmployeeCheckinRecord existWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, checkinEndTime,"上班打卡");
            PersOrgEmployeeCheckinRecord existOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, checkinStartTime, maxCheckinTime,"下班打卡");

            PersOrgEmployeeCheckinRecord workCheckinRecord=null;
            PersOrgEmployeeCheckinRecord offWorkCheckinRecord=null;
            if(existWorkCheckinRecord!=null && !"未打卡".equals(existWorkCheckinRecord.getExceptionType())){
                workCheckinRecord=existWorkCheckinRecord;
            }
            if(existOffWorkCheckinRecord!=null && !"未打卡".equals(existOffWorkCheckinRecord.getExceptionType())){
                offWorkCheckinRecord=existOffWorkCheckinRecord;
            }
            if(offWorkCheckinRecord!=null){
                //存在上班打卡记录
                if(offWorkCheckinRecord.getCheckinTime().getTime()<checkinEndTime.getTime()){
                    //如果已存在的上班打卡记录超过标准上班打卡时间则改成为下班打卡
                    if(workCheckinRecord==null){
                        offWorkCheckinRecord.setCheckinType("上班打卡");
                        //offWorkCheckinRecord.setExceptionType("时间异常");

                        if(dateDiffMinute(checkinStartTime,offWorkCheckinRecord.getCheckinTime())>0){
                            //迟到时间超过1分钟
                            offWorkCheckinRecord.setExceptionType("时间异常");
                        }

                        offWorkCheckinRecord.setSchCheckinTime(checkinStartTime);
                        offWorkCheckinRecord.update();
                    }

                    if(offWorkCheckinRecord.getCheckinTime().getTime()<newCheckinRecord.getCheckinTime().getTime()){
                        offWorkCheckinRecord.setDelFlag("1");
                        offWorkCheckinRecord.setUpdateDate(new Date());
                        offWorkCheckinRecord.update();

                        newCheckinRecord.setSchCheckinTime(checkinEndTime);
                        newCheckinRecord.setCheckinType("下班打卡");
                        newCheckinRecord.save();
                    }

                }else{
                    if(offWorkCheckinRecord.getCheckinTime().getTime()<checkinTime.getTime()){
                        //新的打卡时间早于原上班打卡，则作废旧的保存新的
                        offWorkCheckinRecord.setUpdateDate(currDate);
                        offWorkCheckinRecord.setDelFlag("1");
                        offWorkCheckinRecord.update();

                        newCheckinRecord.setSchCheckinTime(checkinEndTime);
                        newCheckinRecord.setCheckinType("下班打卡");
                        newCheckinRecord.save();
                    }else{
                        //原下班打卡记录正常状态不做处理

                    }

                }
            }else{
                //无打卡记录
                newCheckinRecord.setSchCheckinTime(checkinEndTime);
                newCheckinRecord.setCheckinType("下班打卡");
                newCheckinRecord.save();
            }


        }

    }

    /**
     * 自由打卡规则 打卡记录生成
     * @param checkinWxRecord
     * @param minCheckinTime
     * @param maxCheckinTime
     */
    public void empCheckinRecord(PersOrgEmployeeCheckinWxRecord checkinWxRecord,Date minCheckinTime,Date maxCheckinTime,String noneedOffwork,Date summaryDate){
        String empId=checkinWxRecord.getEmpId();
        Date checkinTime=checkinWxRecord.getCheckinTime();

        Date currDate=new Date();
        PersOrgEmployeeCheckinRecord newCheckinRecord=new PersOrgEmployeeCheckinRecord();
        newCheckinRecord.setId(IdGen.getUUID());
        newCheckinRecord.setCheckinTime(checkinTime);
        newCheckinRecord.setEmpId(empId);
        newCheckinRecord.setDelFlag("0");
        newCheckinRecord.setUpdateDate(currDate);
        newCheckinRecord.setCreateDate(currDate);
        newCheckinRecord.setLocationTitle(checkinWxRecord.getLocationTitle());
        newCheckinRecord.setLocationDetail(checkinWxRecord.getLocationDetail());
        newCheckinRecord.setSummaryDate(summaryDate);
        newCheckinRecord.setGroupid(checkinWxRecord.getGroupid());
        newCheckinRecord.setGroupname(checkinWxRecord.getGroupname());

        Map<String,Object> returnMap=new HashMap<>();
        //该打卡记录是否有效
        returnMap.put("isUsable",false);
        //重新生成日报
        returnMap.put("isRegenerate",false);


        if("1".equals(noneedOffwork)){
            //不需要打下班卡
            PersOrgEmployeeCheckinRecord existWorkCheckinRecord= persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime,"上班打卡");
            PersOrgEmployeeCheckinRecord workCheckinRecord=null;
            if(existWorkCheckinRecord!=null && !"未打卡".equals(existWorkCheckinRecord.getExceptionType())){
                workCheckinRecord=existWorkCheckinRecord;
            }

            if(workCheckinRecord!=null){

                if(workCheckinRecord.getCheckinTime().getTime()>newCheckinRecord.getCheckinTime().getTime()){
                    workCheckinRecord.setDelFlag("1");
                    workCheckinRecord.setUpdateDate(currDate);
                    workCheckinRecord.update();

                    newCheckinRecord.setCheckinType("上班打卡");
                    newCheckinRecord.save();
                }

            }else{
                newCheckinRecord.setCheckinType("上班打卡");
                newCheckinRecord.save();
            }

        }else{
            PersOrgEmployeeCheckinRecord existWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime,"上班打卡");
            PersOrgEmployeeCheckinRecord existOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime,"下班打卡");
            PersOrgEmployeeCheckinRecord workCheckinRecord=null;
            PersOrgEmployeeCheckinRecord offWorkCheckinRecord=null;
            if(existWorkCheckinRecord!=null && !"未打卡".equals(existWorkCheckinRecord.getExceptionType())){
                workCheckinRecord=existWorkCheckinRecord;
            }
            if(existOffWorkCheckinRecord!=null && !"未打卡".equals(existOffWorkCheckinRecord.getExceptionType())){
                offWorkCheckinRecord=existOffWorkCheckinRecord;
            }
            if(workCheckinRecord==null && offWorkCheckinRecord==null){
                newCheckinRecord.setCheckinType("上班打卡");
                newCheckinRecord.save();


            }else if(workCheckinRecord==null && offWorkCheckinRecord!=null){

                if(offWorkCheckinRecord.getCheckinTime().getTime()>newCheckinRecord.getCheckinTime().getTime()){
                    newCheckinRecord.setCheckinType("上班打卡");
                    newCheckinRecord.save();
                }else{
                    offWorkCheckinRecord.setCheckinType("上班打卡");
                    offWorkCheckinRecord.setUpdateDate(currDate);
                    offWorkCheckinRecord.update();

                    newCheckinRecord.setCheckinType("下班打卡");
                    newCheckinRecord.save();
                }

            }else if(workCheckinRecord!=null && offWorkCheckinRecord==null){
                if(workCheckinRecord.getCheckinTime().getTime()>newCheckinRecord.getCheckinTime().getTime()){

                    workCheckinRecord.setCheckinType("下班打卡");
                    workCheckinRecord.setUpdateDate(currDate);
                    workCheckinRecord.update();

                    newCheckinRecord.setCheckinType("上班打卡");
                    newCheckinRecord.save();

                }else{

                    newCheckinRecord.setCheckinType("下班打卡");
                    newCheckinRecord.save();
                }

            }else if(workCheckinRecord!=null && offWorkCheckinRecord!=null){

                if(workCheckinRecord.getCheckinTime().getTime()<newCheckinRecord.getCheckinTime().getTime() &&
                        offWorkCheckinRecord.getCheckinTime().getTime()>newCheckinRecord.getCheckinTime().getTime()){
                    //不需要处理
                }else{
                    if(workCheckinRecord.getCheckinTime().getTime()>newCheckinRecord.getCheckinTime().getTime()){
                        workCheckinRecord.setDelFlag("1");
                        workCheckinRecord.setUpdateDate(currDate);
                        workCheckinRecord.update();

                        newCheckinRecord.setCheckinType("上班打卡");
                        newCheckinRecord.save();
                    }else if(offWorkCheckinRecord.getCheckinTime().getTime()<newCheckinRecord.getCheckinTime().getTime()){
                        offWorkCheckinRecord.setDelFlag("1");
                        offWorkCheckinRecord.setUpdateDate(currDate);
                        offWorkCheckinRecord.update();

                        newCheckinRecord.setCheckinType("下班打卡");
                        newCheckinRecord.save();
                    }

                }

            }

        }

    }


    public int dateDiffMinute(Date minDate,Date maxDate){
        return DateUtils.getTimeDiffMinute(maxDate,minDate);
    }

    public void employeeCheckinDaySummary(Map<String,Object> todayMap){
        //今天是否休息
        boolean todayGeneralHoliday=(boolean)todayMap.get("todayGeneralHoliday");
        //今天是否加班
        boolean todayIsOvertime=(boolean)todayMap.get("todayIsOvertime");
        //今天是否请休假
        boolean todayIsLeave=(boolean)todayMap.get("todayIsLeave");
        //今天是否全天请休假
        boolean todayIsLeaveAllDay=(boolean)todayMap.get("todayIsLeave");
        //今天实际上下班打卡时间段
        List<Map<String,Date>> actualWorkTimeCheckinMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");
        if(todayGeneralHoliday && !todayIsOvertime){
            //休息日
            genRestDaySummary(todayMap,1);
            return;
        }

        if(todayMap.get("todayIsLeaveAllDay")!=null && (boolean)todayMap.get("todayIsLeaveAllDay") && actualWorkTimeCheckinMapList.size()==0){
            //全天请休假日日报
            genLeaveDaySummary(todayMap);
            return;
        }
        if(todayMap.get("todayIsBusinessTripAllDay")!=null && (boolean)todayMap.get("todayIsBusinessTripAllDay")){
            //全天出差日

            if(todayGeneralHoliday && todayIsOvertime){
                //加班日出差
                genBusinessTripOverTimeDaySummary(todayMap);
            }else{
                //工作日日出差
                genBusinessTripWorkDaySummary(todayMap);
            }

            return;
        }
        if(todayGeneralHoliday && todayIsOvertime){
            genOverTimeDaySummary(todayMap);
            //加班日
            return;
        }
        //正常出勤日
        genWorkDaySummary(todayMap);
    }

    /**
     * 休息日日报
     * @param todayMap
     * @param dayType
     */
    public void genRestDaySummary(Map<String,Object> todayMap,Integer dayType){
        Date summaryDate=(Date) todayMap.get("summaryDate");
        PersOrgEmployeeCheckinRule employeeCheckinRule=(PersOrgEmployeeCheckinRule) todayMap.get("checkinRule");

        PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
        checkinDaySummary.setId(IdGen.getUUID());
        checkinDaySummary.setEmpId((String)todayMap.get("empId"));
        checkinDaySummary.setCheckinCount(0);
        checkinDaySummary.setDayType(dayType);
        checkinDaySummary.setSummaryDate(summaryDate);

        checkinDaySummary.setRecordType(Integer.valueOf((String)todayMap.get("ruleType")));
        checkinDaySummary.setGroupId(employeeCheckinRule.getId());
        checkinDaySummary.setGroupName(employeeCheckinRule.getName());
        checkinDaySummary.setRegularWorkSec(0);
        checkinDaySummary.setStandardWorkSec(0);
        checkinDaySummary.setIsException("0");
        checkinDaySummary.setDelFlag("0");
        checkinDaySummary.setCreateDate(new Date());
        checkinDaySummary.setUpdateDate(new Date());
        checkinDaySummary.setIsLeave("0");
        checkinDaySummary.setIsRest("1");
        checkinDaySummary.setIsActualWork("0");
        checkinDaySummary.setIsActualSalary("0");
        checkinDaySummary.setIsAbsenteeism("0");
        checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));

        checkinDaySummary.setNoneedOffwork("0");
        if("1".equals(todayMap.get("noneedOffwork"))){
            checkinDaySummary.setNoneedOffwork("1");
        }
        checkinDaySummary.setIsShouldWork("0");
        checkinDaySummary.setIsManyCheckin("0");
        if("1".equals(todayMap.get("isManyCheckin"))){
            checkinDaySummary.setIsManyCheckin("1");
        }
        checkinDaySummary.setIsLeaveAllDay("0");


        checkinDaySummary.save();
    }

    /**
     * 全天请休假日报
     * @param todayMap
     */
    public void genLeaveDaySummary(Map<String,Object> todayMap){
        Date summaryDate=(Date) todayMap.get("summaryDate");
        PersOrgEmployeeCheckinRule employeeCheckinRule=(PersOrgEmployeeCheckinRule) todayMap.get("checkinRule");

        PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
        checkinDaySummary.setId(IdGen.getUUID());
        checkinDaySummary.setEmpId((String)todayMap.get("empId"));
        checkinDaySummary.setCheckinCount(0);
        checkinDaySummary.setDayType(3);
        checkinDaySummary.setSummaryDate(summaryDate);

        checkinDaySummary.setRecordType(Integer.valueOf((String)todayMap.get("ruleType")));
        checkinDaySummary.setGroupId(employeeCheckinRule.getId());
        checkinDaySummary.setGroupName(employeeCheckinRule.getName());
        checkinDaySummary.setRegularWorkSec(0);
        checkinDaySummary.setStandardWorkSec(0);
        checkinDaySummary.setIsException("0");
        checkinDaySummary.setDelFlag("0");
        checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
        checkinDaySummary.setCreateDate(new Date());
        checkinDaySummary.setUpdateDate(new Date());
        //是否公休日
        checkinDaySummary.setIsRest("0");
        //是否实出勤天数
        checkinDaySummary.setIsActualWork("0");
        //是否请假日
        checkinDaySummary.setIsLeave("1");
        //是否实算工资日，根据请假类型判断
        String leaveTypeKey="";
        if(PersLeaveRestType.illnessLeave.getKey().equals(leaveTypeKey)
        ||PersLeaveRestType.affairLeave.getKey().equals(leaveTypeKey)
        ){
            checkinDaySummary.setIsActualSalary("0");
        }else{
            checkinDaySummary.setIsActualSalary("1");
        }
        //是否是否缺勤天
        checkinDaySummary.setIsAbsenteeism("0");
        checkinDaySummary.setNoneedOffwork("0");
        if("1".equals(todayMap.get("noneedOffwork"))){
            checkinDaySummary.setNoneedOffwork("1");
        }

        List<Record> leaveRecordList = (List<Record>) todayMap.get("leaveRecordList");
        int leaveSecond=0;
        if (leaveRecordList != null && leaveRecordList.size() > 0) {
            JSONArray jsonArray = new JSONArray();
            int totalMinute = 0;
            for (Record record : leaveRecordList) {
                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null){
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("leaveType",record.getStr("leaveType"));
                    jsonObject.put("leaveDateType",record.getStr("leaveDateType"));

                    int leaveMinute = (int) (record.getDate("endTime").getTime() - record.getDate("startTime").getTime()) / 1000 / 60;
                    if (leaveMinute > 0) {
                        totalMinute += leaveMinute;
                    }
                    jsonArray.add(jsonObject);
                }else{
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("leaveType",record.getStr("leaveType"));
                    jsonObject.put("leaveDateType",record.getStr("leaveDateType"));
                    jsonArray.add(jsonObject);
                }
            }
            leaveSecond = totalMinute * 60;
            //checkinDaySummary.setlea
            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
        }
        //checkinDaySummary.setleave
        //是否应出勤天
        checkinDaySummary.setIsShouldWork("1");
        checkinDaySummary.setIsManyCheckin("0");
        if("1".equals(todayMap.get("isManyCheckin"))){
            checkinDaySummary.setIsManyCheckin("1");
        }
        checkinDaySummary.setIsLeaveAllDay("0");
        if((boolean) todayMap.get("todayIsLeaveAllDay")){
            checkinDaySummary.setIsLeaveAllDay("1");
        }
        checkinDaySummary.save();

    }

    /**
     * 添加工作日全天出差日报
     * @param todayMap
     */
    public void genBusinessTripWorkDaySummary(Map<String,Object> todayMap){
        String ruleType=(String) todayMap.get("ruleType");
        String empId=(String) todayMap.get("empId");
        Date summaryDate=(Date) todayMap.get("summaryDate");
        Date minCheckinTime=(Date) todayMap.get("todayMinCheckinTime");
        Date maxCheckinTime=(Date) todayMap.get("todayMaxCheckinTime");

        //今天是否加班
        boolean todayIsOvertime=(boolean)todayMap.get("todayIsOvertime");
        //今天是否请休假
        boolean todayIsLeave=(boolean)todayMap.get("todayIsLeave");
        //是否出差
        boolean todayIsBusinessTrip=(boolean)todayMap.get("todayIsBusinessTrip");
        //是否外派
        boolean todayIsDispatch=(boolean)todayMap.get("todayIsDispatch");

        PersOrgEmployeeCheckinRule checkinRule=(PersOrgEmployeeCheckinRule)todayMap.get("checkinRule");
        String groupId="";
        String groupName="";
        if(checkinRule!=null){
            groupId=checkinRule.getId();
            groupName=checkinRule.getName();
        }

        if("3".equals(ruleType)){
            String noneedOffwork=(String) todayMap.get("noneedOffwork");
            //添加日报记录
            PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
            checkinDaySummary.setId(IdGen.getUUID());
            checkinDaySummary.setEmpId(empId);
            checkinDaySummary.setSummaryDate(summaryDate);
            checkinDaySummary.setCheckinCount(2);
            checkinDaySummary.setDayType(6);
            checkinDaySummary.setRecordType(4);
            checkinDaySummary.setGroupId(groupId);
            checkinDaySummary.setGroupName(groupName);
            checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
            //checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
            //checkinDaySummary.setOffWorkSec(employeeOffWorkCheckinRecord.getCheckinTime());
            checkinDaySummary.setStandardWorkSec(0);
            //checkinDaySummary.setRegularWorkSec((int)(employeeOffWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getCheckinTime().getTime())/1000/60*60);
            //checkinDaySummary.setEarliestTime(employeeWorkCheckinRecord.getCheckinTime());
            //checkinDaySummary.setLastestTime(employeeOffWorkCheckinRecord.getCheckinTime());
            checkinDaySummary.setIsException("0");
            checkinDaySummary.setNoneedOffwork(noneedOffwork);
            checkinDaySummary.setIsShouldWork("1");
            checkinDaySummary.setIsManyCheckin("0");
            checkinDaySummary.setIsActualWork("1");
            checkinDaySummary.setIsAbsenteeism("0");
            checkinDaySummary.setIsRest("0");
            checkinDaySummary.setIsActualSalary("1");
            //判断按小时请休假的情况
            if(todayIsLeave){
                //有休息时间段
                List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                if(leaveRecordList!=null && leaveRecordList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Record record : leaveRecordList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("leaveType", record.getStr("leaveType"));
                            jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                            //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                            int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                            if (leaveMinute > 0) {
                                totalMinute += leaveMinute;
                            }
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("leaveType", record.getStr("leaveType"));
                            jsonObject.put("leaveDateType", record.getStr("leaveDateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    //checkinDaySummary.setlea
                    checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsLeave("1");
                checkinDaySummary.setIsLeaveAllDay("0");
            }else{
                checkinDaySummary.setIsLeave("0");
                checkinDaySummary.setIsLeaveAllDay("0");
            }

            //判断按小时出差的情况
            if(todayIsOvertime){
                handleOverTime(checkinDaySummary,todayMap);
                checkinDaySummary.setIsOvertime("1");
            }else{
                checkinDaySummary.setIsOvertime("0");
            }

            //判断按小时出差的情况
            if(todayIsBusinessTrip){
                List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                if(businessTripDateList!=null && businessTripDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : businessTripDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null ){
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(record.getDate("startTime"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(record.getDate("endTime"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsBusinessTrip("1");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }else{
                checkinDaySummary.setIsBusinessTrip("0");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }

            if(todayIsDispatch){
                List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                if(dispatchDateList!=null && dispatchDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : dispatchDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null){
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(record.getDate("startTime"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(record.getDate("endTime"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dispatchDeptId",record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("dispatchDeptId",record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }

                    }
                    checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsDispatch("1");
                if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                    checkinDaySummary.setIsDispatchAllDay("1");
                }else{
                    checkinDaySummary.setIsDispatchAllDay("0");
                }
            }else{
                checkinDaySummary.setIsDispatch("0");
                checkinDaySummary.setIsDispatchAllDay("0");
            }
            checkinDaySummary.setMinCheckinTime(minCheckinTime);
            checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
            checkinDaySummary.setDelFlag("0");
            checkinDaySummary.setCreateDate(new Date());
            checkinDaySummary.setUpdateDate(new Date());
            checkinDaySummary.save();
            return;
        }else{
            //初始上下班时间段
            List<String[]> workTimePeriodStrArrayList=(List<String[]>)todayMap.get("workTimePeriod");

            List<Map<String,Date>> workTimePeriodMapList=new ArrayList<>();
            for (String[] strings : workTimePeriodStrArrayList) {
                Map<String,Date> dateMap=new HashMap<>();
                dateMap.put("startDate",DateUtils.parseDate(strings[0]));
                dateMap.put("endDate",DateUtils.parseDate(strings[1]));
                workTimePeriodMapList.add(dateMap);
            }

            //处理请休假、加班后的上下班时间段
            List<Map<String,Date>> actualWorkTimeMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeMapList");

            //处理出差后的上下班打卡时间段
            List<Map<String,Date>> actualWorkTimeCheckinMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");

            //标准工作时长（秒）
            int standardWorkSecSecond=0;
            for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                //standardWorkSecSecond+=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60*60;
                standardWorkSecSecond+=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"))*60;
            }
            String isManyCheckin=(String) todayMap.get("isManyCheckin");

            //迟到分钟
            int beLateMinute=0;
            //早退分钟
            int leaveEarlyMinute=0;

            //判断下班打卡是否早退
            //leaveEarlyMinute=(int)(employeeOffWorkCheckinRecord.getSchCheckinTime().getTime()-employeeOffWorkCheckinRecord.getCheckinTime().getTime())/1000/60;
            //判断上班打卡是否迟到
            //beLateMinute=(int)(employeeWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getSchCheckinTime().getTime())/1000/60;



            //添加日报记录
            PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
            checkinDaySummary.setId(IdGen.getUUID());
            checkinDaySummary.setEmpId(empId);
            checkinDaySummary.setSummaryDate(summaryDate);
            checkinDaySummary.setCheckinCount(2);
            checkinDaySummary.setDayType(6);
            checkinDaySummary.setRecordType(4);
            checkinDaySummary.setGroupId(groupId);
            checkinDaySummary.setGroupName(groupName);
            checkinDaySummary.setIsException("0");
            checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
            checkinDaySummary.setNoneedOffwork("0");
            checkinDaySummary.setIsShouldWork("1");
            checkinDaySummary.setIsManyCheckin(isManyCheckin);
            /*checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
            checkinDaySummary.setOffWorkSec(employeeOffWorkCheckinRecord.getCheckinTime());
            checkinDaySummary.setEarliestTime(minCheckinDate);
            checkinDaySummary.setLastestTime(maxCheckinDate);*/
            checkinDaySummary.setIsActualWork("1");
            checkinDaySummary.setIsAbsenteeism("0");
            checkinDaySummary.setIsRest("0");
            checkinDaySummary.setIsActualSalary("1");

            //判断按小时请休假的情况
            int leaveSecond=0;
            if(todayIsLeave){
                //有休息时间段
                List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                if(leaveRecordList!=null && leaveRecordList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Record record : leaveRecordList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("leaveType", record.getStr("leaveType"));
                            jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                            //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                            int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                            if (leaveMinute > 0) {
                                totalMinute += leaveMinute;
                            }
                            jsonArray.add(jsonObject);
                        }
                    }
                    leaveSecond=totalMinute*60;
                    //checkinDaySummary.setlea
                    checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsLeave("1");
                checkinDaySummary.setIsLeaveAllDay("0");
            }else{
                checkinDaySummary.setIsLeave("0");
                checkinDaySummary.setIsLeaveAllDay("0");
            }

            //判断按小时加班的情况
            int overTimeSecond=0;
            if(todayIsOvertime){
                overTimeSecond=handleOverTime(checkinDaySummary,todayMap);
                checkinDaySummary.setIsOvertime("1");
            }else{
                checkinDaySummary.setIsOvertime("0");
            }

            //判断按小时出差的情况
            if(todayIsBusinessTrip){
                List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                if(businessTripDateList!=null && businessTripDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : businessTripDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null){
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(record.getDate("startTime"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(record.getDate("endTime"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsBusinessTrip("1");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }else{
                checkinDaySummary.setIsBusinessTrip("0");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }
            //判断按小时外派的情况
            if(todayIsDispatch){
                List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                if(dispatchDateList!=null && dispatchDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : dispatchDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsDispatch("1");
                if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                    checkinDaySummary.setIsDispatchAllDay("1");
                }else{
                    checkinDaySummary.setIsDispatchAllDay("0");
                }
            }else{
                checkinDaySummary.setIsDispatch("0");
                checkinDaySummary.setIsDispatchAllDay("0");
            }

            if(workTimePeriodMapList!=null){
                //workTimePeriodMapList
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : workTimePeriodMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setInitialWorkMinutes(totalMinute);
                checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
            }
            if(actualWorkTimeMapList!=null){
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
            }
            if(actualWorkTimeCheckinMapList!=null){
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
            }


            checkinDaySummary.setMinCheckinTime(minCheckinTime);
            checkinDaySummary.setMaxCheckinTime(maxCheckinTime);

            //checkinDaySummary.setRegularWorkSec(regularWorkSec);
            checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
            if(beLateMinute<0){
                beLateMinute=0;
            }
            if(leaveEarlyMinute<0){
                leaveEarlyMinute=0;
            }
            int regularWorkSecond=standardWorkSecSecond-(beLateMinute*60)-(leaveEarlyMinute*60);
            checkinDaySummary.setRegularWorkSec(regularWorkSecond);
            checkinDaySummary.setDelFlag("0");
            checkinDaySummary.setCreateDate(new Date());
            checkinDaySummary.setUpdateDate(new Date());
            checkinDaySummary.save();

            return;








        }
    }

    public int handleOverTime(PersOrgEmployeeCheckinDaySummary checkinDaySummary,Map<String,Object> todayMap){
        List<Record> overTimeRecordList=(List<Record>)todayMap.get("overTimeRecordList");
        JSONArray jsonArray = new JSONArray();
        int overTimeSecond=0;
        if (overTimeRecordList != null && overTimeRecordList.size() > 0) {
            int totalMinute = 0;
            for (Record todayOverTime : overTimeRecordList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("startTime", DateUtils.formatDate(todayOverTime.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                jsonObject.put("endTime", DateUtils.formatDate(todayOverTime.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                jsonObject.put("overType",todayOverTime.getStr("overType"));
                int leaveMinute = (int) (todayOverTime.getDate("endTime").getTime() - todayOverTime.getDate("startTime").getTime()) / 1000 / 60;
                jsonObject.put("overTimeMinute",leaveMinute);
                jsonArray.add(jsonObject);

                if (leaveMinute > 0) {
                    totalMinute += leaveMinute;
                }
            }
            overTimeSecond = totalMinute * 60;
            checkinDaySummary.setOvertimeTimes(JSON.toJSONString(jsonArray));
        }
        return overTimeSecond;
    }

    /**
     * 添加加班日全天出差日报
     * @param todayMap
     */
    public void genBusinessTripOverTimeDaySummary(Map<String,Object> todayMap){
        String ruleType=(String) todayMap.get("ruleType");
        String empId=(String) todayMap.get("empId");
        Date summaryDate=(Date) todayMap.get("summaryDate");
        Date minCheckinTime=(Date) todayMap.get("todayMinCheckinTime");
        Date maxCheckinTime=(Date) todayMap.get("todayMaxCheckinTime");

        //今天是否加班
        boolean todayIsOvertime=(boolean)todayMap.get("todayIsOvertime");
        //今天是否请休假
        boolean todayIsLeave=(boolean)todayMap.get("todayIsLeave");
        //是否出差
        boolean todayIsBusinessTrip=(boolean)todayMap.get("todayIsBusinessTrip");
        //是否外派
        boolean todayIsDispatch=(boolean)todayMap.get("todayIsDispatch");

        PersOrgEmployeeCheckinRule checkinRule=(PersOrgEmployeeCheckinRule)todayMap.get("checkinRule");
        String groupId="";
        String groupName="";
        if(checkinRule!=null){
            groupId=checkinRule.getId();
            groupName=checkinRule.getName();
        }

        //初始上下班时间段
        List<String[]> workTimePeriodStrArrayList=(List<String[]>)todayMap.get("workTimePeriod");

        List<Map<String,Date>> workTimePeriodMapList=new ArrayList<>();
        for (String[] strings : workTimePeriodStrArrayList) {
            Map<String,Date> dateMap=new HashMap<>();
            dateMap.put("startDate",DateUtils.parseDate(strings[0]));
            dateMap.put("endDate",DateUtils.parseDate(strings[1]));
            workTimePeriodMapList.add(dateMap);
        }

        //处理请休假、加班后的上下班时间段
        List<Map<String,Date>> actualWorkTimeMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeMapList");

        //处理出差后的上下班打卡时间段
        List<Map<String,Date>> actualWorkTimeCheckinMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");

        //标准工作时长（秒）
        int standardWorkSecSecond=0;
        for (Map<String, Date> dateMap : actualWorkTimeMapList) {
            //standardWorkSecSecond+=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60*60;
            standardWorkSecSecond+=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"))*60;
        }


        //添加日报记录
        PersOrgEmployeeCheckinDaySummary checkinDaySummary = new PersOrgEmployeeCheckinDaySummary();
        checkinDaySummary.setId(IdGen.getUUID());
        checkinDaySummary.setEmpId(empId);
        checkinDaySummary.setSummaryDate(summaryDate);
        checkinDaySummary.setCheckinCount(2);
        checkinDaySummary.setDayType(6);
        checkinDaySummary.setRecordType(5);
        checkinDaySummary.setGroupId(groupId);
        checkinDaySummary.setGroupName(groupName);
        checkinDaySummary.setIsException("0");
        checkinDaySummary.setNoneedOffwork("0");
        checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
        //是否应出勤天
        checkinDaySummary.setIsShouldWork("0");
        //是否多打卡
        checkinDaySummary.setIsManyCheckin("0");
        //checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
        //checkinDaySummary.setOffWorkSec(employeeOffWorkCheckinRecord.getCheckinTime());
        //checkinDaySummary.setEarliestTime(minCheckinDate);
        //checkinDaySummary.setLastestTime(maxCheckinDate);
        //是否实出勤天数
        checkinDaySummary.setIsActualWork("1");
        //是否是否缺勤天
        checkinDaySummary.setIsAbsenteeism("0");
        //是否公休日
        checkinDaySummary.setIsRest("0");
        //是否实算工资日
        checkinDaySummary.setIsActualSalary("0");

        //判断按小时请休假的情况
        int leaveSecond = 0;
        if (todayIsLeave) {
            //有休息时间段
            List<Record> leaveRecordList = (List<Record>) todayMap.get("leaveRecordList");
            if (leaveRecordList != null && leaveRecordList.size() > 0) {
                JSONArray jsonArray = new JSONArray();
                int totalMinute = 0;
                for (Record record : leaveRecordList) {
                    if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("leaveType", record.getStr("leaveType"));
                        jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                        int leaveMinute = (int) (record.getDate("endTime").getTime() - record.getDate("startTime").getTime()) / 1000 / 60;
                        if (leaveMinute > 0) {
                            totalMinute += leaveMinute;
                        }
                        jsonArray.add(jsonObject);
                    }
                }
                leaveSecond = totalMinute * 60;
                //checkinDaySummary.setlea
                checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
            }
            checkinDaySummary.setIsLeave("1");
            checkinDaySummary.setIsLeaveAllDay("0");
        } else {
            checkinDaySummary.setIsLeave("0");
            checkinDaySummary.setIsLeaveAllDay("0");
        }

        //判断按小时加班的情况
        int overTimeSecond = 0;
        if (todayIsOvertime) {
            //获取加班时间段，并返回加班时间
            overTimeSecond=handleOverTime(checkinDaySummary,todayMap);

            checkinDaySummary.setIsOvertime("1");
        } else {
            checkinDaySummary.setIsOvertime("0");
        }

        //判断按小时出差的情况
        if (todayIsBusinessTrip) {
            List<Record> businessTripDateList = (List<Record>) todayMap.get("empBusinessTripDateList");
            if (businessTripDateList != null && businessTripDateList.size() > 0) {
                JSONArray jsonArray = new JSONArray();
                for (Record record : businessTripDateList) {
                    if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("dateType",record.getStr("dateType"));
                        jsonObject.put("destinationCity",record.getStr("destinationCity"));
                        jsonArray.add(jsonObject);
                    }else{
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("dateType",record.getStr("dateType"));
                        jsonObject.put("destinationCity",record.getStr("destinationCity"));
                        jsonArray.add(jsonObject);
                    }
                }
                checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
            }
            checkinDaySummary.setIsBusinessTrip("1");
            checkinDaySummary.setIsBusinessTripAllDay("0");
        } else {
            checkinDaySummary.setIsBusinessTrip("0");
            checkinDaySummary.setIsBusinessTripAllDay("0");
        }
        //判断按小时外派的情况
        if (todayIsDispatch) {
            List<Record> dispatchDateList = (List<Record>) todayMap.get("empDispatchDateList");
            if (dispatchDateList != null && dispatchDateList.size() > 0) {
                JSONArray jsonArray = new JSONArray();
                for (Record record : dispatchDateList) {
                    if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                        jsonObject.put("dateType",record.getStr("dateType"));
                        jsonArray.add(jsonObject);
                    }
                }
                checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
            }
            checkinDaySummary.setIsDispatch("1");
            if (todayMap.get("todayIsDispatch") != null && (boolean) todayMap.get("todayIsDispatch")) {
                checkinDaySummary.setIsDispatchAllDay("1");
            } else {
                checkinDaySummary.setIsDispatchAllDay("0");
            }
        } else {
            checkinDaySummary.setIsDispatch("0");
            checkinDaySummary.setIsDispatchAllDay("0");
        }

        if (workTimePeriodMapList != null) {
            //workTimePeriodMapList
            JSONArray jsonArray = new JSONArray();
            int totalMinute = 0;
            for (Map<String, Date> dateMap : workTimePeriodMapList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("startTime", DateUtils.formatDate(dateMap.get("startDate"), "yyyy-MM-dd HH:mm:ss"));
                jsonObject.put("endTime", DateUtils.formatDate(dateMap.get("endDate"), "yyyy-MM-dd HH:mm:ss"));
                jsonArray.add(jsonObject);
                int minute = (int) (dateMap.get("endDate").getTime() - dateMap.get("startDate").getTime()) / 1000 / 60;

                if (minute > 0) {
                    totalMinute += minute;
                }
            }
            checkinDaySummary.setInitialWorkMinutes(totalMinute);
            checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
        }
        if (actualWorkTimeMapList != null) {
            JSONArray jsonArray = new JSONArray();
            int totalMinute = 0;
            for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("startTime", DateUtils.formatDate(dateMap.get("startDate"), "yyyy-MM-dd HH:mm:ss"));
                jsonObject.put("endTime", DateUtils.formatDate(dateMap.get("endDate"), "yyyy-MM-dd HH:mm:ss"));
                jsonArray.add(jsonObject);
                int minute = (int) (dateMap.get("endDate").getTime() - dateMap.get("startDate").getTime()) / 1000 / 60;
                if (minute > 0) {
                    totalMinute += minute;
                }
            }
            checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
            checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
        }
        if (actualWorkTimeCheckinMapList != null) {
            JSONArray jsonArray = new JSONArray();
            int totalMinute = 0;
            for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("startTime", DateUtils.formatDate(dateMap.get("startDate"), "yyyy-MM-dd HH:mm:ss"));
                jsonObject.put("endTime", DateUtils.formatDate(dateMap.get("endDate"), "yyyy-MM-dd HH:mm:ss"));
                jsonArray.add(jsonObject);
                int minute = (int) (dateMap.get("endDate").getTime() - dateMap.get("startDate").getTime()) / 1000 / 60;
                if (minute > 0) {
                    totalMinute += minute;
                }
            }
            checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
        }


        checkinDaySummary.setMinCheckinTime(minCheckinTime);
        checkinDaySummary.setMaxCheckinTime(maxCheckinTime);

        //checkinDaySummary.setRegularWorkSec(regularWorkSec);
        checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
        //int regularWorkSecond = standardWorkSecSecond + overTimeSecond - leaveSecond - (beLateMinute * 60) - (leaveEarlyMinute * 60);
        //checkinDaySummary.setRegularWorkSec(regularWorkSecond);
        checkinDaySummary.setDelFlag("0");
        checkinDaySummary.setCreateDate(new Date());
        checkinDaySummary.setUpdateDate(new Date());
        checkinDaySummary.save();


        /*if (beLateMinute > 0) {
            //添加迟到异常记录
            PersOrgEmployeeCheckinException beLateException = new PersOrgEmployeeCheckinException();
            beLateException.setType("day");
            beLateException.setSummaryId(checkinDaySummary.getId());
            beLateException.setEmpId(empId);
            beLateException.setDuration(beLateMinute * 60);
            beLateException.setCount(1);
            beLateException.setException(1);
            persOrgEmployeeCheckinExceptionService.saveCheckinException(beLateException);
        }
        if (leaveEarlyMinute > 0) {
            //添加早退异常记录
            PersOrgEmployeeCheckinException leaveEarlyException = new PersOrgEmployeeCheckinException();
            leaveEarlyException.setType("day");
            leaveEarlyException.setSummaryId(checkinDaySummary.getId());
            leaveEarlyException.setEmpId(empId);
            leaveEarlyException.setDuration(leaveEarlyMinute * 60);
            leaveEarlyException.setCount(1);
            leaveEarlyException.setException(2);
            persOrgEmployeeCheckinExceptionService.saveCheckinException(leaveEarlyException);
        }*/

        return;
    }
    /**
     * 生成加班日报
     * @param todayMap
     */
    public void genOverTimeDaySummary(Map<String,Object> todayMap){
        String ruleType=(String) todayMap.get("ruleType");
        String empId=(String) todayMap.get("empId");
        Date summaryDate=(Date) todayMap.get("summaryDate");
        Date minCheckinTime=(Date) todayMap.get("todayMinCheckinTime");
        Date maxCheckinTime=(Date) todayMap.get("todayMaxCheckinTime");

        //今天是否加班
        boolean todayIsOvertime=(boolean)todayMap.get("todayIsOvertime");
        //今天是否请休假
        boolean todayIsLeave=(boolean)todayMap.get("todayIsLeave");
        //是否出差
        boolean todayIsBusinessTrip=(boolean)todayMap.get("todayIsBusinessTrip");
        //是否外派
        boolean todayIsDispatch=(boolean)todayMap.get("todayIsDispatch");

        PersOrgEmployeeCheckinRule checkinRule=(PersOrgEmployeeCheckinRule)todayMap.get("checkinRule");
        String groupId="";
        String groupName="";
        if(checkinRule!=null){
            groupId=checkinRule.getId();
            groupName=checkinRule.getName();
        }

        //初始上下班时间段
        List<String[]> workTimePeriodStrArrayList=(List<String[]>)todayMap.get("workTimePeriod");

        List<Map<String,Date>> workTimePeriodMapList=new ArrayList<>();
        if(workTimePeriodStrArrayList!=null){
            for (String[] strings : workTimePeriodStrArrayList) {
                Map<String,Date> dateMap=new HashMap<>();
                dateMap.put("startDate",DateUtils.parseDate(strings[0]));
                dateMap.put("endDate",DateUtils.parseDate(strings[1]));
                workTimePeriodMapList.add(dateMap);
            }
        }


        //处理请休假、加班后的上下班时间段
        List<Map<String,Date>> actualWorkTimeMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeMapList");

        //处理出差后的上下班打卡时间段
        List<Map<String,Date>> actualWorkTimeCheckinMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");

        //标准工作时长（秒）
        int standardWorkSecSecond=0;
        for (Map<String, Date> dateMap : actualWorkTimeMapList) {
            //standardWorkSecSecond+=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60*60;
            standardWorkSecSecond+=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"))*60;
        }

        //非多打卡
        PersOrgEmployeeCheckinRecord employeeWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "上班打卡");
        PersOrgEmployeeCheckinRecord employeeOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "下班打卡");
        if(employeeWorkCheckinRecord!=null && "未打卡".equals(employeeWorkCheckinRecord.getExceptionType())){
            employeeWorkCheckinRecord=null;
        }
        if(employeeOffWorkCheckinRecord!=null && "未打卡".equals(employeeOffWorkCheckinRecord.getExceptionType())){
            employeeOffWorkCheckinRecord=null;
        }
        Date workCheckinTime = actualWorkTimeCheckinMapList.get(0).get("startDate");
        Date offWorkCheckinTime = actualWorkTimeCheckinMapList.get(actualWorkTimeMapList.size()-1).get("endDate");

        Date minCheckinDate=null;
        Date maxCheckinDate=null;
        //获取最小、最大打卡时间
        if(employeeWorkCheckinRecord!=null){
            if(minCheckinDate==null || (minCheckinDate.getTime()>employeeWorkCheckinRecord.getCheckinTime().getTime())){
                minCheckinDate=employeeWorkCheckinRecord.getCheckinTime();
            }
            if(maxCheckinDate==null || (maxCheckinDate.getTime()<employeeWorkCheckinRecord.getCheckinTime().getTime())){
                maxCheckinDate=employeeWorkCheckinRecord.getCheckinTime();
            }
        }
        if(employeeOffWorkCheckinRecord!=null){
            if(minCheckinDate==null || (minCheckinDate.getTime()>employeeOffWorkCheckinRecord.getCheckinTime().getTime())){
                minCheckinDate=employeeOffWorkCheckinRecord.getCheckinTime();
            }
            if(maxCheckinDate==null || (maxCheckinDate.getTime()<employeeOffWorkCheckinRecord.getCheckinTime().getTime())){
                maxCheckinDate=employeeOffWorkCheckinRecord.getCheckinTime();
            }
        }

        if(employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord==null){
            //添加未打卡日报记录
            employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
            employeeWorkCheckinRecord.setId(IdGen.getUUID());
            employeeWorkCheckinRecord.setEmpId(empId);
            employeeWorkCheckinRecord.setCheckinType("上班打卡");
            employeeWorkCheckinRecord.setExceptionType("未打卡");
            employeeWorkCheckinRecord.setSchCheckinTime(null);
            employeeWorkCheckinRecord.setGroupid(groupId);
            employeeWorkCheckinRecord.setGroupname(groupName);
            employeeWorkCheckinRecord.setSummaryDate(summaryDate);
            employeeWorkCheckinRecord.setCheckinTime(workCheckinTime);
            employeeWorkCheckinRecord.setSchCheckinTime(workCheckinTime);
            employeeWorkCheckinRecord.setDelFlag("0");
            employeeWorkCheckinRecord.setCreateDate(new Date());
            employeeWorkCheckinRecord.setUpdateDate(new Date());
            employeeWorkCheckinRecord.save();

            employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
            employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
            employeeOffWorkCheckinRecord.setEmpId(empId);
            employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
            employeeOffWorkCheckinRecord.setExceptionType("未打卡");
            employeeOffWorkCheckinRecord.setSchCheckinTime(null);
            employeeOffWorkCheckinRecord.setSchCheckinTime(offWorkCheckinTime);
            employeeOffWorkCheckinRecord.setGroupid(groupId);
            employeeOffWorkCheckinRecord.setGroupname(groupName);
            employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
            employeeOffWorkCheckinRecord.setCheckinTime(offWorkCheckinTime);
            employeeOffWorkCheckinRecord.setDelFlag("0");
            employeeOffWorkCheckinRecord.setCreateDate(new Date());
            employeeOffWorkCheckinRecord.setUpdateDate(new Date());
            employeeOffWorkCheckinRecord.save();


            //添加日报记录
            PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
            checkinDaySummary.setId(IdGen.getUUID());
            checkinDaySummary.setEmpId(empId);
            checkinDaySummary.setSummaryDate(summaryDate);
            checkinDaySummary.setCheckinCount(0);
            checkinDaySummary.setDayType(2);
            checkinDaySummary.setRecordType(5);
            checkinDaySummary.setGroupId(groupId);
            checkinDaySummary.setGroupName(groupName);
            checkinDaySummary.setIsException("1");
            checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
            checkinDaySummary.setNoneedOffwork("0");
            //是否应出勤天
            checkinDaySummary.setIsShouldWork("0");
            //标准工作时长（秒）
            checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
            //实际工作时长（秒）
            checkinDaySummary.setRegularWorkSec(0);
            //是否多打卡
            checkinDaySummary.setIsManyCheckin("0");
            //是否实出勤天
            checkinDaySummary.setIsActualWork("0");
            //是否缺勤天
            checkinDaySummary.setIsAbsenteeism("1");
            //是否公休天
            checkinDaySummary.setIsRest("1");
            //是否实算工资日
            checkinDaySummary.setIsActualSalary("0");

            //判断按小时请休假的情况
            if(todayIsLeave){
                //有休息时间段
                List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                if(leaveRecordList!=null && leaveRecordList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Record record : leaveRecordList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("leaveType", record.getStr("leaveType"));
                            jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                            //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                            int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                            if (leaveMinute > 0) {
                                totalMinute += leaveMinute;
                            }
                            jsonArray.add(jsonObject);
                        }
                    }
                    //checkinDaySummary.setlea
                    checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsLeave("1");
                checkinDaySummary.setIsLeaveAllDay("0");
            }else{
                checkinDaySummary.setIsLeave("0");
                checkinDaySummary.setIsLeaveAllDay("0");
            }

            //判断按小时出差的情况
            if(todayIsOvertime){
                handleOverTime(checkinDaySummary,todayMap);
                checkinDaySummary.setIsOvertime("1");
            }else{
                checkinDaySummary.setIsOvertime("0");
            }

            //判断按小时出差的情况
            if(todayIsBusinessTrip){
                List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                if(businessTripDateList!=null && businessTripDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : businessTripDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsBusinessTrip("1");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }else{
                checkinDaySummary.setIsBusinessTrip("0");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }
            //判断按小时外派的情况
            if(todayIsDispatch){
                List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                if(dispatchDateList!=null && dispatchDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : dispatchDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsDispatch("1");
                if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                    checkinDaySummary.setIsDispatchAllDay("1");
                    checkinDaySummary.setDayType(4);
                }else{
                    checkinDaySummary.setIsDispatchAllDay("0");
                }
            }else{
                checkinDaySummary.setIsDispatch("0");
                checkinDaySummary.setIsDispatchAllDay("0");
            }

            if(workTimePeriodMapList!=null){
                //workTimePeriodMapList
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : workTimePeriodMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setInitialWorkMinutes(totalMinute);
                checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
            }
            if(actualWorkTimeMapList!=null){
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
            }
            if(actualWorkTimeCheckinMapList!=null){
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
            }


            checkinDaySummary.setMinCheckinTime(minCheckinTime);
            checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
            checkinDaySummary.setDelFlag("0");
            checkinDaySummary.setCreateDate(new Date());
            checkinDaySummary.setUpdateDate(new Date());
            checkinDaySummary.save();

            //添加缺卡记录
            PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
            exception.setType("day");
            exception.setSummaryId(checkinDaySummary.getId());
            exception.setEmpId(empId);
            exception.setDuration(0);
            exception.setCount(2);
            exception.setException(3);
            persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);
            return;


        }else if((employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord!=null) || (employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord==null)){
            //添加未打卡日报记录

            //迟到分钟
            int beLateMinute=0;
            //早退分钟
            int leaveEarlyMinute=0;
            Date workSec=null;
            Date offWorkSec=null;
            if(employeeWorkCheckinRecord==null){
                employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                employeeWorkCheckinRecord.setId(IdGen.getUUID());
                employeeWorkCheckinRecord.setEmpId(empId);
                employeeWorkCheckinRecord.setCheckinType("上班打卡");
                employeeWorkCheckinRecord.setExceptionType("未打卡");
                employeeWorkCheckinRecord.setSchCheckinTime(null);
                employeeWorkCheckinRecord.setGroupid(groupId);
                employeeWorkCheckinRecord.setGroupname(groupName);
                employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                employeeWorkCheckinRecord.setCheckinTime(workCheckinTime);
                employeeWorkCheckinRecord.setSchCheckinTime(workCheckinTime);
                employeeWorkCheckinRecord.setDelFlag("0");
                employeeWorkCheckinRecord.setCreateDate(new Date());
                employeeWorkCheckinRecord.setUpdateDate(new Date());
                employeeWorkCheckinRecord.save();

                offWorkSec=employeeOffWorkCheckinRecord.getCheckinTime();
                //判断下班打卡是否早退
                //leaveEarlyMinute=(int)(employeeOffWorkCheckinRecord.getSchCheckinTime().getTime()-employeeOffWorkCheckinRecord.getCheckinTime().getTime())/1000/60;
                leaveEarlyMinute=DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getSchCheckinTime(),employeeOffWorkCheckinRecord.getCheckinTime());
            }else if(employeeOffWorkCheckinRecord==null){
                employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                employeeOffWorkCheckinRecord.setEmpId(empId);
                employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                employeeOffWorkCheckinRecord.setSchCheckinTime(offWorkCheckinTime);
                employeeOffWorkCheckinRecord.setGroupid(groupId);
                employeeOffWorkCheckinRecord.setGroupname(groupName);
                employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                employeeOffWorkCheckinRecord.setCheckinTime(offWorkCheckinTime);
                employeeOffWorkCheckinRecord.setDelFlag("0");
                employeeOffWorkCheckinRecord.setCreateDate(new Date());
                employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                employeeOffWorkCheckinRecord.save();
                workSec=employeeWorkCheckinRecord.getCheckinTime();
                //beLateMinute=(int)(employeeWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getSchCheckinTime().getTime())/1000/60;
                beLateMinute=DateUtils.getTimeDiffMinute(employeeWorkCheckinRecord.getCheckinTime(),employeeWorkCheckinRecord.getSchCheckinTime());
            }



            //添加日报记录
            PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
            checkinDaySummary.setId(IdGen.getUUID());
            checkinDaySummary.setEmpId(empId);
            checkinDaySummary.setSummaryDate(summaryDate);
            checkinDaySummary.setCheckinCount(1);
            checkinDaySummary.setDayType(2);
            checkinDaySummary.setRecordType(5);
            checkinDaySummary.setGroupId(groupId);
            checkinDaySummary.setGroupName(groupName);
            checkinDaySummary.setWorkSec(workSec);
            checkinDaySummary.setOffWorkSec(offWorkSec);
            checkinDaySummary.setRegularWorkSec(0);
            checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
            checkinDaySummary.setEarliestTime(minCheckinDate);
            checkinDaySummary.setLastestTime(maxCheckinDate);
            checkinDaySummary.setIsException("1");
            checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
            checkinDaySummary.setNoneedOffwork("0");
            //是否应出勤天
            checkinDaySummary.setIsShouldWork("0");
            checkinDaySummary.setIsManyCheckin("0");
            //是否实出勤天
            checkinDaySummary.setIsActualWork("1");
            //是否缺勤天
            checkinDaySummary.setIsAbsenteeism("0");
            //是否公休天
            checkinDaySummary.setIsRest("1");
            //是否实算工资天
            checkinDaySummary.setIsActualSalary("0");

            //判断按小时请休假的情况
            if(todayIsLeave){
                //有休息时间段
                List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                if(leaveRecordList!=null && leaveRecordList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Record record : leaveRecordList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("leaveType", record.getStr("leaveType"));
                            jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                            //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                            int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                            if (leaveMinute > 0) {
                                totalMinute += leaveMinute;
                            }
                            jsonArray.add(jsonObject);
                        }
                    }
                    //checkinDaySummary.setlea
                    checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsLeave("1");
                checkinDaySummary.setIsLeaveAllDay("0");
            }else{
                checkinDaySummary.setIsLeave("0");
                checkinDaySummary.setIsLeaveAllDay("0");
            }

            //判断按小时出差的情况
            if(todayIsOvertime){
                handleOverTime(checkinDaySummary,todayMap);
                checkinDaySummary.setIsOvertime("1");
            }else{
                checkinDaySummary.setIsOvertime("0");
            }

            //判断按小时出差的情况
            if(todayIsBusinessTrip){
                List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                if(businessTripDateList!=null && businessTripDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : businessTripDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsBusinessTrip("1");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }else{
                checkinDaySummary.setIsBusinessTrip("0");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }
            //判断按小时外派的情况
            if(todayIsDispatch){
                List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                if(dispatchDateList!=null && dispatchDateList.size()>0){
                    JSONArray jsonArray=new JSONArray();
                    for (Record record : dispatchDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsDispatch("1");
                if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                    checkinDaySummary.setIsDispatchAllDay("1");
                    checkinDaySummary.setDayType(4);
                }else{
                    checkinDaySummary.setIsDispatchAllDay("0");
                }
            }else{
                checkinDaySummary.setIsDispatch("0");
                checkinDaySummary.setIsDispatchAllDay("0");
            }

            if(workTimePeriodMapList!=null){
                //workTimePeriodMapList
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : workTimePeriodMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setInitialWorkMinutes(totalMinute);
                checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
            }
            if(actualWorkTimeMapList!=null){
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
            }
            if(actualWorkTimeCheckinMapList!=null){
                JSONArray jsonArray=new JSONArray();
                int totalMinute=0;
                for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                    int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                    if(minute>0){
                        totalMinute+=minute;
                    }
                }
                checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
            }


            checkinDaySummary.setMinCheckinTime(minCheckinTime);
            checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
            checkinDaySummary.setDelFlag("0");
            checkinDaySummary.setCreateDate(new Date());
            checkinDaySummary.setUpdateDate(new Date());
            checkinDaySummary.save();

            //添加缺卡记录
            PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
            exception.setType("day");
            exception.setSummaryId(checkinDaySummary.getId());
            exception.setEmpId(empId);
            exception.setDuration(0);
            exception.setCount(1);
            exception.setException(3);
            persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);

            if(beLateMinute>0){
                //添加迟到异常记录
                PersOrgEmployeeCheckinException beLateException=new PersOrgEmployeeCheckinException();
                beLateException.setType("day");
                beLateException.setSummaryId(checkinDaySummary.getId());
                beLateException.setEmpId(empId);
                beLateException.setDuration(beLateMinute*60);
                beLateException.setCount(1);
                beLateException.setException(1);
                persOrgEmployeeCheckinExceptionService.saveCheckinException(beLateException);
            }
            if(leaveEarlyMinute>0){
                //添加早退异常记录
                PersOrgEmployeeCheckinException leaveEarlyException=new PersOrgEmployeeCheckinException();
                leaveEarlyException.setType("day");
                leaveEarlyException.setSummaryId(checkinDaySummary.getId());
                leaveEarlyException.setEmpId(empId);
                leaveEarlyException.setDuration(leaveEarlyMinute*60);
                leaveEarlyException.setCount(1);
                leaveEarlyException.setException(2);
                persOrgEmployeeCheckinExceptionService.saveCheckinException(leaveEarlyException);
            }

            return;

        }else if(employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord!=null) {
            //迟到分钟
            int beLateMinute = 0;
            //早退分钟
            int leaveEarlyMinute = 0;

            //判断下班打卡是否早退
            leaveEarlyMinute = (int) (employeeOffWorkCheckinRecord.getSchCheckinTime().getTime() - employeeOffWorkCheckinRecord.getCheckinTime().getTime()) / 1000 / 60;
            //判断上班打卡是否迟到
            beLateMinute = (int) (employeeWorkCheckinRecord.getCheckinTime().getTime() - employeeWorkCheckinRecord.getSchCheckinTime().getTime()) / 1000 / 60;


            //添加日报记录
            PersOrgEmployeeCheckinDaySummary checkinDaySummary = new PersOrgEmployeeCheckinDaySummary();
            checkinDaySummary.setId(IdGen.getUUID());
            checkinDaySummary.setEmpId(empId);
            checkinDaySummary.setSummaryDate(summaryDate);
            checkinDaySummary.setCheckinCount(2);
            checkinDaySummary.setDayType(2);
            checkinDaySummary.setRecordType(5);
            checkinDaySummary.setGroupId(groupId);
            checkinDaySummary.setGroupName(groupName);
            checkinDaySummary.setIsException("0");
            checkinDaySummary.setNoneedOffwork("0");
            checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
            //是否应出勤天
            checkinDaySummary.setIsShouldWork("0");
            checkinDaySummary.setIsManyCheckin("0");
            checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
            checkinDaySummary.setOffWorkSec(employeeOffWorkCheckinRecord.getCheckinTime());
            checkinDaySummary.setEarliestTime(minCheckinDate);
            checkinDaySummary.setLastestTime(maxCheckinDate);
            //是否实出勤天数
            checkinDaySummary.setIsActualWork("1");
            //是否是否缺勤天
            checkinDaySummary.setIsAbsenteeism("0");
            //是否公休日
            checkinDaySummary.setIsRest("1");
            //是否实算工资日
            checkinDaySummary.setIsActualSalary("0");

            //判断按小时请休假的情况
            int leaveSecond = 0;
            if (todayIsLeave) {
                //有休息时间段
                List<Record> leaveRecordList = (List<Record>) todayMap.get("leaveRecordList");
                if (leaveRecordList != null && leaveRecordList.size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    int totalMinute = 0;
                    for (Record record : leaveRecordList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("leaveType", record.getStr("leaveType"));
                            jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                            int leaveMinute = (int) (record.getDate("endTime").getTime() - record.getDate("startTime").getTime()) / 1000 / 60;
                            if (leaveMinute > 0) {
                                totalMinute += leaveMinute;
                            }
                            jsonArray.add(jsonObject);
                        }
                    }
                    leaveSecond = totalMinute * 60;
                    //checkinDaySummary.setlea
                    checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsLeave("1");
                checkinDaySummary.setIsLeaveAllDay("0");
            } else {
                checkinDaySummary.setIsLeave("0");
                checkinDaySummary.setIsLeaveAllDay("0");
            }

            //判断按小时加班的情况
            int overTimeSecond = 0;
            if (todayIsOvertime) {
                overTimeSecond=handleOverTime(checkinDaySummary,todayMap);
                checkinDaySummary.setIsOvertime("1");
            } else {
                checkinDaySummary.setIsOvertime("0");
            }

            //判断按小时出差的情况
            if (todayIsBusinessTrip) {
                List<Record> businessTripDateList = (List<Record>) todayMap.get("empBusinessTripDateList");
                if (businessTripDateList != null && businessTripDateList.size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    for (Record record : businessTripDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonObject.put("destinationCity",record.getStr("destinationCity"));

                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsBusinessTrip("1");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            } else {
                checkinDaySummary.setIsBusinessTrip("0");
                checkinDaySummary.setIsBusinessTripAllDay("0");
            }
            //判断按小时外派的情况
            if (todayIsDispatch) {
                List<Record> dispatchDateList = (List<Record>) todayMap.get("empDispatchDateList");
                if (dispatchDateList != null && dispatchDateList.size() > 0) {
                    JSONArray jsonArray = new JSONArray();
                    for (Record record : dispatchDateList) {
                        if(record.getDate("startTime")!=null && record.getDate("endTime")!=null){
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("dispatchDeptId",record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }else{
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                            jsonObject.put("dateType",record.getStr("dateType"));
                            jsonArray.add(jsonObject);
                        }
                    }
                    checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                }
                checkinDaySummary.setIsDispatch("1");
                if (todayMap.get("todayIsDispatch") != null && (boolean) todayMap.get("todayIsDispatch")) {
                    checkinDaySummary.setIsDispatchAllDay("1");
                    checkinDaySummary.setDayType(4);
                } else {
                    checkinDaySummary.setIsDispatchAllDay("0");
                }
            } else {
                checkinDaySummary.setIsDispatch("0");
                checkinDaySummary.setIsDispatchAllDay("0");
            }

            if (workTimePeriodMapList != null) {
                //workTimePeriodMapList
                JSONArray jsonArray = new JSONArray();
                int totalMinute = 0;
                for (Map<String, Date> dateMap : workTimePeriodMapList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("startTime", DateUtils.formatDate(dateMap.get("startDate"), "yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime", DateUtils.formatDate(dateMap.get("endDate"), "yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    int minute = (int) (dateMap.get("endDate").getTime() - dateMap.get("startDate").getTime()) / 1000 / 60;
                    if (minute > 0) {
                        totalMinute += minute;
                    }
                }
                checkinDaySummary.setInitialWorkMinutes(totalMinute);
                checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
            }
            if (actualWorkTimeMapList != null) {
                JSONArray jsonArray = new JSONArray();
                int totalMinute = 0;
                for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("startTime", DateUtils.formatDate(dateMap.get("startDate"), "yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime", DateUtils.formatDate(dateMap.get("endDate"), "yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    int minute = (int) (dateMap.get("endDate").getTime() - dateMap.get("startDate").getTime()) / 1000 / 60;
                    if (minute > 0) {
                        totalMinute += minute;
                    }
                }
                checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
            }
            if (actualWorkTimeCheckinMapList != null) {
                JSONArray jsonArray = new JSONArray();
                int totalMinute = 0;
                for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("startTime", DateUtils.formatDate(dateMap.get("startDate"), "yyyy-MM-dd HH:mm:ss"));
                    jsonObject.put("endTime", DateUtils.formatDate(dateMap.get("endDate"), "yyyy-MM-dd HH:mm:ss"));
                    jsonArray.add(jsonObject);
                    int minute = (int) (dateMap.get("endDate").getTime() - dateMap.get("startDate").getTime()) / 1000 / 60;
                    if (minute > 0) {
                        totalMinute += minute;
                    }
                }
                checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
            }


            checkinDaySummary.setMinCheckinTime(minCheckinTime);
            checkinDaySummary.setMaxCheckinTime(maxCheckinTime);

            //checkinDaySummary.setRegularWorkSec(regularWorkSec);
            checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
            if(beLateMinute<0){
                beLateMinute=0;
            }
            if(leaveEarlyMinute<0){
                leaveEarlyMinute=0;
            }
            int regularWorkSecond = standardWorkSecSecond - (beLateMinute * 60) - (leaveEarlyMinute * 60);
            checkinDaySummary.setRegularWorkSec(regularWorkSecond);
            checkinDaySummary.setDelFlag("0");
            checkinDaySummary.setCreateDate(new Date());
            checkinDaySummary.setUpdateDate(new Date());
            checkinDaySummary.save();


            if (beLateMinute > 0) {
                //添加迟到异常记录
                PersOrgEmployeeCheckinException beLateException = new PersOrgEmployeeCheckinException();
                beLateException.setType("day");
                beLateException.setSummaryId(checkinDaySummary.getId());
                beLateException.setEmpId(empId);
                beLateException.setDuration(beLateMinute * 60);
                beLateException.setCount(1);
                beLateException.setException(1);
                persOrgEmployeeCheckinExceptionService.saveCheckinException(beLateException);
            }
            if (leaveEarlyMinute > 0) {
                //添加早退异常记录
                PersOrgEmployeeCheckinException leaveEarlyException = new PersOrgEmployeeCheckinException();
                leaveEarlyException.setType("day");
                leaveEarlyException.setSummaryId(checkinDaySummary.getId());
                leaveEarlyException.setEmpId(empId);
                leaveEarlyException.setDuration(leaveEarlyMinute * 60);
                leaveEarlyException.setCount(1);
                leaveEarlyException.setException(2);
                persOrgEmployeeCheckinExceptionService.saveCheckinException(leaveEarlyException);
            }

            return;
        }

    }

    /**
     * 工作日日报生成
     * @param todayMap
     */
    public void genWorkDaySummary(Map<String,Object> todayMap){

        String ruleType=(String) todayMap.get("ruleType");
        String empId=(String) todayMap.get("empId");
        Date summaryDate=(Date) todayMap.get("summaryDate");
        Date minCheckinTime=(Date) todayMap.get("todayMinCheckinTime");
        Date maxCheckinTime=(Date) todayMap.get("todayMaxCheckinTime");

        //今天是否加班
        boolean todayIsOvertime=(boolean)todayMap.get("todayIsOvertime");
        //今天是否请休假
        boolean todayIsLeave=(boolean)todayMap.get("todayIsLeave");
        //是否出差
        boolean todayIsBusinessTrip=(boolean)todayMap.get("todayIsBusinessTrip");
        //是否外派
        boolean todayIsDispatch=(boolean)todayMap.get("todayIsDispatch");

        PersOrgEmployeeCheckinRule checkinRule=(PersOrgEmployeeCheckinRule)todayMap.get("checkinRule");
        String groupId="";
        String groupName="";
        if(checkinRule!=null){
            groupId=checkinRule.getId();
            groupName=checkinRule.getName();
        }

        if("3".equals(ruleType)){
            //自由打卡

            //下班打卡不需要打
            String noneedOffwork=(String) todayMap.get("noneedOffwork");

            if("1".equals(noneedOffwork)){
                //不需要打下班卡
                PersOrgEmployeeCheckinRecord employeeWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "上班打卡");
                if(employeeWorkCheckinRecord!=null && "未打卡".equals(employeeWorkCheckinRecord.getExceptionType())){
                    employeeWorkCheckinRecord=null;
                }
                if(employeeWorkCheckinRecord==null){
                    //添加未打卡日报记录
                    employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                    employeeWorkCheckinRecord.setId(IdGen.getUUID());
                    employeeWorkCheckinRecord.setEmpId(empId);
                    employeeWorkCheckinRecord.setCheckinType("上班打卡");
                    employeeWorkCheckinRecord.setExceptionType("未打卡");
                    employeeWorkCheckinRecord.setSchCheckinTime(null);
                    employeeWorkCheckinRecord.setGroupid(groupId);
                    employeeWorkCheckinRecord.setGroupname(groupName);
                    employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                    employeeWorkCheckinRecord.setCheckinTime(DateUtils.parseDate(DateUtils.formatDate(summaryDate,"yyyy-MM-dd")+" 00:00:00"));
                    employeeWorkCheckinRecord.setDelFlag("0");
                    employeeWorkCheckinRecord.setCreateDate(new Date());
                    employeeWorkCheckinRecord.setUpdateDate(new Date());
                    employeeWorkCheckinRecord.save();

                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(0);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setIsException("1");
                    checkinDaySummary.setNoneedOffwork(noneedOffwork);
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin("0");
                    checkinDaySummary.setIsActualWork("0");
                    checkinDaySummary.setIsAbsenteeism("1");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("0");

                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }

                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }
                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();


                    //添加缺卡记录
                    PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
                    exception.setType("day");
                    exception.setSummaryId(checkinDaySummary.getId());
                    exception.setEmpId(empId);
                    exception.setDuration(0);
                    exception.setCount(1);
                    exception.setException(3);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);

                    return;
                }else{
                    //添加正常日报
                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(1);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setIsException("0");
                    checkinDaySummary.setNoneedOffwork(noneedOffwork);
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin("0");
                    checkinDaySummary.setIsActualWork("1");
                    checkinDaySummary.setIsAbsenteeism("0");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setEarliestTime(employeeWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setLastestTime(employeeWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setIsActualSalary("1");
                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }

                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }
                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();
                    return;
                }

            }else{
                //下班打卡需要打
                PersOrgEmployeeCheckinRecord employeeWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "上班打卡");
                PersOrgEmployeeCheckinRecord employeeOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "下班打卡");
                if(employeeWorkCheckinRecord!=null && "未打卡".equals(employeeWorkCheckinRecord.getExceptionType())){
                    employeeWorkCheckinRecord=null;
                }
                if(employeeOffWorkCheckinRecord!=null && "未打卡".equals(employeeOffWorkCheckinRecord.getExceptionType())){
                    employeeOffWorkCheckinRecord=null;
                }

                if(employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord==null){
                    //添加未打卡日报记录
                    employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                    employeeWorkCheckinRecord.setId(IdGen.getUUID());
                    employeeWorkCheckinRecord.setEmpId(empId);
                    employeeWorkCheckinRecord.setCheckinType("上班打卡");
                    employeeWorkCheckinRecord.setExceptionType("未打卡");
                    employeeWorkCheckinRecord.setSchCheckinTime(null);
                    employeeWorkCheckinRecord.setGroupid(groupId);
                    employeeWorkCheckinRecord.setGroupname(groupName);
                    employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                    employeeWorkCheckinRecord.setCheckinTime(DateUtils.parseDate(DateUtils.formatDate(summaryDate,"yyyy-MM-dd")+" 00:00:00"));
                    employeeWorkCheckinRecord.setDelFlag("0");
                    employeeWorkCheckinRecord.setCreateDate(new Date());
                    employeeWorkCheckinRecord.setUpdateDate(new Date());
                    employeeWorkCheckinRecord.save();

                    employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                    employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                    employeeOffWorkCheckinRecord.setEmpId(empId);
                    employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                    employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                    employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                    employeeOffWorkCheckinRecord.setGroupid(groupId);
                    employeeOffWorkCheckinRecord.setGroupname(groupName);
                    employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                    employeeOffWorkCheckinRecord.setCheckinTime(DateUtils.parseDate(DateUtils.formatDate(summaryDate,"yyyy-MM-dd")+" 23:59:59"));
                    employeeOffWorkCheckinRecord.setDelFlag("0");
                    employeeOffWorkCheckinRecord.setCreateDate(new Date());
                    employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                    employeeOffWorkCheckinRecord.save();


                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(0);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setIsException("1");
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setNoneedOffwork(noneedOffwork);
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin("0");
                    checkinDaySummary.setIsActualWork("0");
                    checkinDaySummary.setIsAbsenteeism("1");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("0");

                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }

                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }
                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();

                    //添加缺卡记录
                    PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
                    exception.setType("day");
                    exception.setSummaryId(checkinDaySummary.getId());
                    exception.setEmpId(empId);
                    exception.setDuration(0);
                    exception.setCount(2);
                    exception.setException(3);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);

                    return;
                }else if((employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord!=null) || (employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord==null)){

                    Date workSec=null;
                    Date offWorkSec=null;
                    if(employeeWorkCheckinRecord==null){
                        employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                        employeeWorkCheckinRecord.setId(IdGen.getUUID());
                        employeeWorkCheckinRecord.setEmpId(empId);
                        employeeWorkCheckinRecord.setCheckinType("上班打卡");
                        employeeWorkCheckinRecord.setExceptionType("未打卡");
                        employeeWorkCheckinRecord.setSchCheckinTime(null);
                        employeeWorkCheckinRecord.setGroupid(groupId);
                        employeeWorkCheckinRecord.setGroupname(groupName);
                        employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                        employeeWorkCheckinRecord.setCheckinTime(DateUtils.parseDate(DateUtils.formatDate(summaryDate,"yyyy-MM-dd")+" 00:00:00"));
                        employeeWorkCheckinRecord.setDelFlag("0");
                        employeeWorkCheckinRecord.setCreateDate(new Date());
                        employeeWorkCheckinRecord.setUpdateDate(new Date());
                        employeeWorkCheckinRecord.save();

                        offWorkSec=employeeOffWorkCheckinRecord.getCheckinTime();
                    }else if(employeeOffWorkCheckinRecord==null){
                        employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                        employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                        employeeOffWorkCheckinRecord.setEmpId(empId);
                        employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                        employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                        employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                        employeeOffWorkCheckinRecord.setGroupid(groupId);
                        employeeOffWorkCheckinRecord.setGroupname(groupName);
                        employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                        employeeOffWorkCheckinRecord.setCheckinTime(DateUtils.parseDate(DateUtils.formatDate(summaryDate,"yyyy-MM-dd")+" 23:59:59"));
                        employeeOffWorkCheckinRecord.setDelFlag("0");
                        employeeOffWorkCheckinRecord.setCreateDate(new Date());
                        employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                        employeeOffWorkCheckinRecord.save();

                        workSec=employeeWorkCheckinRecord.getCheckinTime();
                    }

                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(1);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setIsException("1");
                    checkinDaySummary.setNoneedOffwork(noneedOffwork);
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setWorkSec(workSec);
                    checkinDaySummary.setOffWorkSec(offWorkSec);
                    if(workSec!=null){
                        checkinDaySummary.setEarliestTime(workSec);
                        checkinDaySummary.setLastestTime(workSec);
                    }
                    if(offWorkSec!=null){
                        checkinDaySummary.setEarliestTime(offWorkSec);
                        checkinDaySummary.setLastestTime(offWorkSec);
                    }
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin("0");
                    checkinDaySummary.setIsActualWork("1");
                    checkinDaySummary.setIsAbsenteeism("0");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("1");
                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }

                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }
                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();

                    //添加缺卡记录
                    PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
                    exception.setType("day");
                    exception.setSummaryId(checkinDaySummary.getId());
                    exception.setEmpId(empId);
                    exception.setDuration(0);
                    exception.setCount(1);
                    exception.setException(3);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);

                    return;
                }else if(employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord!=null){
                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(2);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setOffWorkSec(employeeOffWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setStandardWorkSec(0);
                    //checkinDaySummary.setRegularWorkSec((int)(employeeOffWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getCheckinTime().getTime())/1000/60*60);
                    checkinDaySummary.setRegularWorkSec(DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getCheckinTime(),employeeWorkCheckinRecord.getCheckinTime())*60);
                    checkinDaySummary.setEarliestTime(employeeWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setLastestTime(employeeOffWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setIsException("0");
                    checkinDaySummary.setNoneedOffwork(noneedOffwork);
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin("0");
                    checkinDaySummary.setIsActualWork("1");
                    checkinDaySummary.setIsAbsenteeism("0");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("1");
                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }

                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }
                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();
                    return;

                }

            }
        }else{
            //固定时间和排班

            //初始上下班时间段
            List<String[]> workTimePeriodStrArrayList=(List<String[]>)todayMap.get("workTimePeriod");

            List<Map<String,Date>> workTimePeriodMapList=new ArrayList<>();
            for (String[] strings : workTimePeriodStrArrayList) {
                Map<String,Date> dateMap=new HashMap<>();
                dateMap.put("startDate",DateUtils.parseDate(strings[0]));
                dateMap.put("endDate",DateUtils.parseDate(strings[1]));
                workTimePeriodMapList.add(dateMap);
            }

            //处理请休假、加班后的上下班时间段
            List<Map<String,Date>> actualWorkTimeMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeMapList");

            //处理出差后的上下班打卡时间段
            List<Map<String,Date>> actualWorkTimeCheckinMapList=(List<Map<String,Date>>)todayMap.get("actualWorkTimeCheckinMapList");

            //标准工作时长（秒）
            int standardWorkSecSecond=0;
            for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                //standardWorkSecSecond+=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60*60;
                standardWorkSecSecond+=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"))*60;
            }

            //判断是否多打卡
            String isManyCheckin=(String) todayMap.get("isManyCheckin");
            if("1".equals(isManyCheckin)){
                //多打卡

                //缺卡次数
                int lackCheckinCount=0;
                //迟到次数
                int beLateCount=0;
                //迟到分钟
                int beLateMinute=0;
                //早退次数
                int leaveEarlyCount=0;
                //早退分钟
                int leaveEarlyMinute=0;

                //应该打卡次数
                int shouldCheckinCount=0;

                //标准
                PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                checkinDaySummary.setId(IdGen.getUUID());
                checkinDaySummary.setEmpId(empId);
                checkinDaySummary.setSummaryDate(summaryDate);

                checkinDaySummary.setDayType(0);
                checkinDaySummary.setRecordType(4);
                checkinDaySummary.setGroupId(groupId);
                checkinDaySummary.setGroupName(groupName);
                checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                Date workSec=null;
                Date offWorkSec=null;
                int regularWorkSecSecond=0;

                //最小打卡时间
                Date minCheckinDate=null;
                //最大打卡时间
                Date maxCheckinDate=null;
                for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                    int index = actualWorkTimeMapList.indexOf(dateMap);
                    Date startDate=dateMap.get("startDate");
                    Date endDate=dateMap.get("endDate");
                    shouldCheckinCount+=2;
                    Date checkinRangeStartTime=null;
                    Date checkinRangeEndTime=null;
                    if(index==0){
                        if(index==actualWorkTimeMapList.size()-1){
                            //只有1条记录
                            checkinRangeStartTime=minCheckinTime;
                            checkinRangeEndTime=maxCheckinTime;
                        }else{
                            Map<String, Date> newxtDateMap = actualWorkTimeMapList.get(index + 1);
                            Date nextStatDate = newxtDateMap.get("startDate");
                            Date maxOffWorkCheckinDate = getMiddleDate(endDate, nextStatDate);
                            checkinRangeStartTime=minCheckinTime;
                            checkinRangeEndTime=maxOffWorkCheckinDate;
                        }

                    }else if(index==actualWorkTimeMapList.size()-1){

                        Map<String, Date> lastDateMap = actualWorkTimeMapList.get(index - 1);
                        Date lastEndDate = lastDateMap.get("endDate");
                        Date minWorkCheckinDate = getMiddleDate(lastEndDate, startDate);
                        //minWorkCheckinDate maxCheckinTime
                        checkinRangeStartTime=minWorkCheckinDate;
                        checkinRangeEndTime=maxCheckinTime;
                    }else{
                        Map<String, Date> lastDateMap = actualWorkTimeMapList.get(index - 1);
                        Date lastEndDate = lastDateMap.get("endDate");
                        Map<String, Date> newxtDateMap = actualWorkTimeMapList.get(index + 1);
                        Date nextStatDate = newxtDateMap.get("startDate");
                        Date minWorkCheckinDate = getMiddleDate(lastEndDate, startDate);
                        Date maxOffWorkCheckinDate = getMiddleDate(endDate, nextStatDate);
                        //minWorkCheckinDate  maxOffWorkCheckinDate
                        checkinRangeStartTime=minWorkCheckinDate;
                        checkinRangeEndTime=maxOffWorkCheckinDate;
                    }

                    PersOrgEmployeeCheckinRecord employeeWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, checkinRangeStartTime, checkinRangeEndTime, "上班打卡");
                    PersOrgEmployeeCheckinRecord employeeOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, checkinRangeStartTime, checkinRangeEndTime, "下班打卡");
                    if(employeeWorkCheckinRecord!=null && "未打卡".equals(employeeWorkCheckinRecord.getExceptionType())){
                        employeeWorkCheckinRecord=null;
                    }
                    if(employeeOffWorkCheckinRecord!=null && "未打卡".equals(employeeOffWorkCheckinRecord.getExceptionType())){
                        employeeOffWorkCheckinRecord=null;
                    }
                    if(workSec==null && employeeWorkCheckinRecord!=null){
                        workSec=employeeWorkCheckinRecord.getCheckinTime();
                    }
                    if(employeeOffWorkCheckinRecord!=null){
                        offWorkSec=employeeOffWorkCheckinRecord.getCheckinTime();
                    }
                    //获取最小、最大打卡时间
                    if(employeeWorkCheckinRecord!=null){
                        if(minCheckinDate==null || (minCheckinDate.getTime()>employeeWorkCheckinRecord.getCheckinTime().getTime())){
                            minCheckinDate=employeeWorkCheckinRecord.getCheckinTime();
                        }
                        if(maxCheckinDate==null || (maxCheckinDate.getTime()<employeeWorkCheckinRecord.getCheckinTime().getTime())){
                            maxCheckinDate=employeeWorkCheckinRecord.getCheckinTime();
                        }
                    }
                    if(employeeOffWorkCheckinRecord!=null){
                        if(minCheckinDate==null || (minCheckinDate.getTime()>employeeOffWorkCheckinRecord.getCheckinTime().getTime())){
                            minCheckinDate=employeeOffWorkCheckinRecord.getCheckinTime();
                        }
                        if(maxCheckinDate==null || (maxCheckinDate.getTime()<employeeOffWorkCheckinRecord.getCheckinTime().getTime())){
                            maxCheckinDate=employeeOffWorkCheckinRecord.getCheckinTime();
                        }
                    }

                    if(employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord==null){
                        employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                        employeeWorkCheckinRecord.setId(IdGen.getUUID());
                        employeeWorkCheckinRecord.setEmpId(empId);
                        employeeWorkCheckinRecord.setCheckinType("上班打卡");
                        employeeWorkCheckinRecord.setExceptionType("未打卡");
                        employeeWorkCheckinRecord.setSchCheckinTime(null);
                        employeeWorkCheckinRecord.setGroupid(groupId);
                        employeeWorkCheckinRecord.setGroupname(groupName);
                        employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                        employeeWorkCheckinRecord.setCheckinTime(startDate);
                        employeeWorkCheckinRecord.setSchCheckinTime(startDate);
                        employeeWorkCheckinRecord.setDelFlag("0");
                        employeeWorkCheckinRecord.setCreateDate(new Date());
                        employeeWorkCheckinRecord.setUpdateDate(new Date());
                        employeeWorkCheckinRecord.save();

                        employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                        employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                        employeeOffWorkCheckinRecord.setEmpId(empId);
                        employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                        employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                        employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                        employeeOffWorkCheckinRecord.setSchCheckinTime(endDate);
                        employeeOffWorkCheckinRecord.setGroupid(groupId);
                        employeeOffWorkCheckinRecord.setGroupname(groupName);
                        employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                        employeeOffWorkCheckinRecord.setCheckinTime(endDate);
                        employeeOffWorkCheckinRecord.setDelFlag("0");
                        employeeOffWorkCheckinRecord.setCreateDate(new Date());
                        employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                        employeeOffWorkCheckinRecord.save();
                        lackCheckinCount+=2;
                    }else if((employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord!=null) || (employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord==null)){
                        if(employeeWorkCheckinRecord==null){
                            employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                            employeeWorkCheckinRecord.setId(IdGen.getUUID());
                            employeeWorkCheckinRecord.setEmpId(empId);
                            employeeWorkCheckinRecord.setCheckinType("上班打卡");
                            employeeWorkCheckinRecord.setExceptionType("未打卡");
                            employeeWorkCheckinRecord.setSchCheckinTime(null);
                            employeeWorkCheckinRecord.setGroupid(groupId);
                            employeeWorkCheckinRecord.setGroupname(groupName);
                            employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                            employeeWorkCheckinRecord.setCheckinTime(startDate);
                            employeeWorkCheckinRecord.setSchCheckinTime(startDate);
                            employeeWorkCheckinRecord.setDelFlag("0");
                            employeeWorkCheckinRecord.setCreateDate(new Date());
                            employeeWorkCheckinRecord.setUpdateDate(new Date());
                            employeeWorkCheckinRecord.save();

                            //判断下班卡是否早退
                            //int minute=(int)(employeeOffWorkCheckinRecord.getSchCheckinTime().getTime()-employeeOffWorkCheckinRecord.getCheckinTime().getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getSchCheckinTime(),employeeOffWorkCheckinRecord.getCheckinTime());
                            if(minute>0){
                                leaveEarlyCount++;
                                leaveEarlyMinute+=minute;
                            }

                        }else if(employeeOffWorkCheckinRecord==null){
                            employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                            employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                            employeeOffWorkCheckinRecord.setEmpId(empId);
                            employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                            employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                            employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                            employeeOffWorkCheckinRecord.setSchCheckinTime(endDate);
                            employeeOffWorkCheckinRecord.setGroupid(groupId);
                            employeeOffWorkCheckinRecord.setGroupname(groupName);
                            employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                            employeeOffWorkCheckinRecord.setCheckinTime(endDate);
                            employeeOffWorkCheckinRecord.setDelFlag("0");
                            employeeOffWorkCheckinRecord.setCreateDate(new Date());
                            employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                            employeeOffWorkCheckinRecord.save();

                            //int minute=(int)(employeeWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getSchCheckinTime().getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(employeeWorkCheckinRecord.getCheckinTime(),employeeWorkCheckinRecord.getSchCheckinTime());
                            if(minute>0){
                                beLateMinute+=minute;
                                beLateCount++;
                            }
                        }
                        lackCheckinCount+=1;
                    }else if(employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord!=null){

                        //判断下班打卡是否早退
                        //int lMinute=(int)(employeeOffWorkCheckinRecord.getSchCheckinTime().getTime()-employeeOffWorkCheckinRecord.getCheckinTime().getTime())/1000/60;
                        int lMinute=DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getSchCheckinTime(),employeeOffWorkCheckinRecord.getCheckinTime());
                        //判断上班打卡是否迟到
                        //int bMinute=(int)(employeeWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getSchCheckinTime().getTime())/1000/60;
                        int bMinute=DateUtils.getTimeDiffMinute(employeeWorkCheckinRecord.getCheckinTime(),employeeWorkCheckinRecord.getSchCheckinTime());

                        if(lMinute>0){
                            leaveEarlyCount++;
                            leaveEarlyMinute+=lMinute;
                        }else{
                            lMinute=0;
                        }

                        if(bMinute>0){
                            beLateCount++;
                            beLateMinute+=bMinute;
                        }else{
                            bMinute=0;
                        }
                        //regularWorkSecSecond+=(int)(employeeOffWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getCheckinTime().getTime())/1000/60*60;
                        regularWorkSecSecond+=DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getCheckinTime()
                                ,employeeWorkCheckinRecord.getCheckinTime())*60-(bMinute*60)-(lMinute*60);
                    }

                }

                checkinDaySummary.setCheckinCount(shouldCheckinCount-lackCheckinCount);
                checkinDaySummary.setIsException("0");
                checkinDaySummary.setNoneedOffwork("0");

                checkinDaySummary.setIsShouldWork("1");
                checkinDaySummary.setIsManyCheckin(isManyCheckin);
                checkinDaySummary.setIsRest("0");
                checkinDaySummary.setWorkSec(workSec);
                checkinDaySummary.setOffWorkSec(offWorkSec);
                checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
                checkinDaySummary.setRegularWorkSec(regularWorkSecSecond);
                checkinDaySummary.setEarliestTime(minCheckinDate);
                checkinDaySummary.setLastestTime(maxCheckinDate);

                if(lackCheckinCount==shouldCheckinCount && shouldCheckinCount>0){
                    //一次卡都没打
                    checkinDaySummary.setIsActualWork("0");
                    checkinDaySummary.setIsAbsenteeism("1");
                    checkinDaySummary.setIsActualSalary("0");
                }else{
                    checkinDaySummary.setIsActualWork("1");
                    checkinDaySummary.setIsAbsenteeism("0");
                    checkinDaySummary.setIsActualSalary("1");
                }

                if(todayIsLeave){
                    //有休息时间段
                    List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                    if(leaveRecordList!=null && leaveRecordList.size()>0){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Record record : leaveRecordList) {
                            if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                jsonObject.put("leaveType", record.getStr("leaveType"));
                                jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                if (leaveMinute > 0) {
                                    totalMinute += leaveMinute;
                                }
                                jsonArray.add(jsonObject);
                            }
                        }
                        //checkinDaySummary.setlea
                        checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                    }
                    checkinDaySummary.setIsLeave("1");
                    checkinDaySummary.setIsLeaveAllDay("0");
                }else{
                    checkinDaySummary.setIsLeave("0");
                    checkinDaySummary.setIsLeaveAllDay("0");
                }

                //判断按小时加班的情况
                if(todayIsOvertime){
                    handleOverTime(checkinDaySummary,todayMap);
                    checkinDaySummary.setIsOvertime("1");
                }else{
                    checkinDaySummary.setIsOvertime("0");
                }

                //判断按小时出差的情况
                if(todayIsBusinessTrip){
                    List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                    if(businessTripDateList!=null && businessTripDateList.size()>0){
                        JSONArray jsonArray=new JSONArray();
                        for (Record record : businessTripDateList) {
                            if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                jsonObject.put("dateType",record.getStr("dateType"));
                                jsonArray.add(jsonObject);
                            }
                        }
                        checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                    }
                    checkinDaySummary.setIsBusinessTrip("1");
                    checkinDaySummary.setIsBusinessTripAllDay("0");
                }else{
                    checkinDaySummary.setIsBusinessTrip("0");
                    checkinDaySummary.setIsBusinessTripAllDay("0");
                }
                //判断按小时外派的情况
                if(todayIsDispatch){
                    List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                    if(dispatchDateList!=null && dispatchDateList.size()>0){
                        JSONArray jsonArray=new JSONArray();
                        for (Record record : dispatchDateList) {
                            if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                jsonObject.put("dateType",record.getStr("dateType"));
                                jsonArray.add(jsonObject);
                            }else{
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                jsonObject.put("dateType",record.getStr("dateType"));
                                jsonArray.add(jsonObject);
                            }
                        }
                        checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                    }
                    checkinDaySummary.setIsDispatch("1");
                    if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                        checkinDaySummary.setIsDispatchAllDay("1");
                        checkinDaySummary.setDayType(4);
                    }else{
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }
                }else{
                    checkinDaySummary.setIsDispatch("0");
                    checkinDaySummary.setIsDispatchAllDay("0");
                }

                if(workTimePeriodMapList!=null){
                    //workTimePeriodMapList
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Map<String, Date> dateMap : workTimePeriodMapList) {
                        JSONObject jsonObject=new JSONObject();
                        jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                        jsonArray.add(jsonObject);
                        //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                        int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                        if(minute>0){
                            totalMinute+=minute;
                        }
                    }
                    checkinDaySummary.setInitialWorkMinutes(totalMinute);
                    checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
                }
                if(actualWorkTimeMapList!=null){
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                        JSONObject jsonObject=new JSONObject();
                        jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                        jsonArray.add(jsonObject);
                        //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                        int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                        if(minute>0){
                            totalMinute+=minute;
                        }
                    }
                    checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                    checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
                }
                if(actualWorkTimeCheckinMapList!=null){
                    JSONArray jsonArray=new JSONArray();
                    int totalMinute=0;
                    for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                        JSONObject jsonObject=new JSONObject();
                        jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                        jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                        jsonArray.add(jsonObject);
                        //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                        int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                        if(minute>0){
                            totalMinute+=minute;
                        }
                    }
                    checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
                }


                checkinDaySummary.setMinCheckinTime(minCheckinTime);
                checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                checkinDaySummary.setDelFlag("0");
                checkinDaySummary.setCreateDate(new Date());
                checkinDaySummary.setUpdateDate(new Date());
                checkinDaySummary.save();


                if(lackCheckinCount>0){
                    checkinDaySummary.setIsException("1");
                    PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
                    exception.setType("day");
                    exception.setSummaryId(checkinDaySummary.getId());
                    exception.setEmpId(empId);
                    exception.setDuration(0);
                    exception.setCount(lackCheckinCount);
                    exception.setException(3);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);
                }
                if(beLateCount>0){
                    checkinDaySummary.setIsException("1");
                    //添加迟到异常记录
                    PersOrgEmployeeCheckinException beLateException=new PersOrgEmployeeCheckinException();
                    beLateException.setType("day");
                    beLateException.setSummaryId(checkinDaySummary.getId());
                    beLateException.setEmpId(empId);
                    beLateException.setDuration(beLateMinute*60);
                    beLateException.setCount(beLateCount);
                    beLateException.setException(1);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(beLateException);
                }
                if(leaveEarlyCount>0){
                    checkinDaySummary.setIsException("1");
                    //添加早退异常记录
                    PersOrgEmployeeCheckinException leaveEarlyException=new PersOrgEmployeeCheckinException();
                    leaveEarlyException.setType("day");
                    leaveEarlyException.setSummaryId(checkinDaySummary.getId());
                    leaveEarlyException.setEmpId(empId);
                    leaveEarlyException.setDuration(leaveEarlyMinute*60);
                    leaveEarlyException.setCount(leaveEarlyCount);
                    leaveEarlyException.setException(2);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(leaveEarlyException);
                }
                return;
            }else{
                //非多打卡
                PersOrgEmployeeCheckinRecord employeeWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "上班打卡");
                PersOrgEmployeeCheckinRecord employeeOffWorkCheckinRecord = persOrgEmployeeCheckinRecordService.getEmpCheckinRecordByDateRange(empId, minCheckinTime, maxCheckinTime, "下班打卡");
                if(employeeWorkCheckinRecord!=null && "未打卡".equals(employeeWorkCheckinRecord.getExceptionType())){
                    employeeWorkCheckinRecord=null;
                }
                if(employeeOffWorkCheckinRecord!=null && "未打卡".equals(employeeOffWorkCheckinRecord.getExceptionType())){
                    employeeOffWorkCheckinRecord=null;
                }
                Date workCheckinTime = actualWorkTimeCheckinMapList.get(0).get("startDate");
                Date offWorkCheckinTime = actualWorkTimeCheckinMapList.get(actualWorkTimeCheckinMapList.size()-1).get("endDate");

                Date minCheckinDate=null;
                Date maxCheckinDate=null;
                //获取最小、最大打卡时间
                if(employeeWorkCheckinRecord!=null){
                    if(minCheckinDate==null || (minCheckinDate.getTime()>employeeWorkCheckinRecord.getCheckinTime().getTime())){
                        minCheckinDate=employeeWorkCheckinRecord.getCheckinTime();
                    }
                    if(maxCheckinDate==null || (maxCheckinDate.getTime()<employeeWorkCheckinRecord.getCheckinTime().getTime())){
                        maxCheckinDate=employeeWorkCheckinRecord.getCheckinTime();
                    }
                }
                if(employeeOffWorkCheckinRecord!=null){
                    if(minCheckinDate==null || (minCheckinDate.getTime()>employeeOffWorkCheckinRecord.getCheckinTime().getTime())){
                        minCheckinDate=employeeOffWorkCheckinRecord.getCheckinTime();
                    }
                    if(maxCheckinDate==null || (maxCheckinDate.getTime()<employeeOffWorkCheckinRecord.getCheckinTime().getTime())){
                        maxCheckinDate=employeeOffWorkCheckinRecord.getCheckinTime();
                    }
                }

                if(employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord==null){
                    //添加未打卡日报记录
                    employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                    employeeWorkCheckinRecord.setId(IdGen.getUUID());
                    employeeWorkCheckinRecord.setEmpId(empId);
                    employeeWorkCheckinRecord.setCheckinType("上班打卡");
                    employeeWorkCheckinRecord.setExceptionType("未打卡");
                    employeeWorkCheckinRecord.setSchCheckinTime(null);
                    employeeWorkCheckinRecord.setGroupid(groupId);
                    employeeWorkCheckinRecord.setGroupname(groupName);
                    employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                    employeeWorkCheckinRecord.setCheckinTime(workCheckinTime);
                    employeeWorkCheckinRecord.setSchCheckinTime(workCheckinTime);
                    employeeWorkCheckinRecord.setDelFlag("0");
                    employeeWorkCheckinRecord.setCreateDate(new Date());
                    employeeWorkCheckinRecord.setUpdateDate(new Date());
                    employeeWorkCheckinRecord.save();

                    employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                    employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                    employeeOffWorkCheckinRecord.setEmpId(empId);
                    employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                    employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                    employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                    employeeOffWorkCheckinRecord.setSchCheckinTime(offWorkCheckinTime);
                    employeeOffWorkCheckinRecord.setGroupid(groupId);
                    employeeOffWorkCheckinRecord.setGroupname(groupName);
                    employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                    employeeOffWorkCheckinRecord.setCheckinTime(offWorkCheckinTime);
                    employeeOffWorkCheckinRecord.setDelFlag("0");
                    employeeOffWorkCheckinRecord.setCreateDate(new Date());
                    employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                    employeeOffWorkCheckinRecord.save();


                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(0);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setIsException("1");
                    checkinDaySummary.setNoneedOffwork("0");
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
                    checkinDaySummary.setIsManyCheckin(isManyCheckin);
                    checkinDaySummary.setIsActualWork("0");
                    checkinDaySummary.setIsAbsenteeism("1");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("0");

                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }
                    //判断按小时外派的情况
                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }

                    if(workTimePeriodMapList!=null){
                        //workTimePeriodMapList
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : workTimePeriodMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setInitialWorkMinutes(totalMinute);
                        checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
                    }
                    if(actualWorkTimeMapList!=null){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                        checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
                    }
                    if(actualWorkTimeCheckinMapList!=null){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
                    }


                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();

                    //添加缺卡记录
                    PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
                    exception.setType("day");
                    exception.setSummaryId(checkinDaySummary.getId());
                    exception.setEmpId(empId);
                    exception.setDuration(0);
                    exception.setCount(2);
                    exception.setException(3);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);
                    return;


                }else if((employeeWorkCheckinRecord==null && employeeOffWorkCheckinRecord!=null) || (employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord==null)){
                    //添加未打卡日报记录

                    //迟到分钟
                    int beLateMinute=0;
                    //早退分钟
                    int leaveEarlyMinute=0;
                    Date workSec=null;
                    Date offWorkSec=null;
                    if(employeeWorkCheckinRecord==null){
                        employeeWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                        employeeWorkCheckinRecord.setId(IdGen.getUUID());
                        employeeWorkCheckinRecord.setEmpId(empId);
                        employeeWorkCheckinRecord.setCheckinType("上班打卡");
                        employeeWorkCheckinRecord.setExceptionType("未打卡");
                        employeeWorkCheckinRecord.setSchCheckinTime(null);
                        employeeWorkCheckinRecord.setGroupid(groupId);
                        employeeWorkCheckinRecord.setGroupname(groupName);
                        employeeWorkCheckinRecord.setSummaryDate(summaryDate);
                        employeeWorkCheckinRecord.setCheckinTime(workCheckinTime);
                        employeeWorkCheckinRecord.setSchCheckinTime(workCheckinTime);
                        employeeWorkCheckinRecord.setDelFlag("0");
                        employeeWorkCheckinRecord.setCreateDate(new Date());
                        employeeWorkCheckinRecord.setUpdateDate(new Date());
                        employeeWorkCheckinRecord.save();

                        offWorkSec=employeeOffWorkCheckinRecord.getCheckinTime();
                        //判断下班打卡是否早退
                        //leaveEarlyMinute=(int)(employeeOffWorkCheckinRecord.getSchCheckinTime().getTime()-employeeOffWorkCheckinRecord.getCheckinTime().getTime())/1000/60;
                        leaveEarlyMinute=DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getSchCheckinTime(),employeeOffWorkCheckinRecord.getCheckinTime());
                    }else if(employeeOffWorkCheckinRecord==null){
                        employeeOffWorkCheckinRecord=new PersOrgEmployeeCheckinRecord();
                        employeeOffWorkCheckinRecord.setId(IdGen.getUUID());
                        employeeOffWorkCheckinRecord.setEmpId(empId);
                        employeeOffWorkCheckinRecord.setCheckinType("下班打卡");
                        employeeOffWorkCheckinRecord.setExceptionType("未打卡");
                        employeeOffWorkCheckinRecord.setSchCheckinTime(null);
                        employeeOffWorkCheckinRecord.setSchCheckinTime(offWorkCheckinTime);
                        employeeOffWorkCheckinRecord.setGroupid(groupId);
                        employeeOffWorkCheckinRecord.setGroupname(groupName);
                        employeeOffWorkCheckinRecord.setSummaryDate(summaryDate);
                        employeeOffWorkCheckinRecord.setCheckinTime(offWorkCheckinTime);
                        employeeOffWorkCheckinRecord.setDelFlag("0");
                        employeeOffWorkCheckinRecord.setCreateDate(new Date());
                        employeeOffWorkCheckinRecord.setUpdateDate(new Date());
                        employeeOffWorkCheckinRecord.save();
                        workSec=employeeWorkCheckinRecord.getCheckinTime();
                        //beLateMinute=(int)(employeeWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getSchCheckinTime().getTime())/1000/60;
                        leaveEarlyMinute=DateUtils.getTimeDiffMinute(employeeWorkCheckinRecord.getCheckinTime(),employeeWorkCheckinRecord.getSchCheckinTime());
                    }



                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(1);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setWorkSec(workSec);
                    checkinDaySummary.setOffWorkSec(offWorkSec);
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setRegularWorkSec(0);
                    checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
                    checkinDaySummary.setEarliestTime(minCheckinDate);
                    checkinDaySummary.setLastestTime(maxCheckinDate);
                    checkinDaySummary.setIsException("0");
                    checkinDaySummary.setNoneedOffwork("0");
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin(isManyCheckin);
                    checkinDaySummary.setIsActualWork("1");
                    checkinDaySummary.setIsAbsenteeism("0");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("1");

                    //判断按小时请休假的情况
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    if(todayIsOvertime){
                        handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }
                    //判断按小时外派的情况
                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }

                    if(workTimePeriodMapList!=null){
                        //workTimePeriodMapList
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : workTimePeriodMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setInitialWorkMinutes(totalMinute);
                        checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
                    }
                    if(actualWorkTimeMapList!=null){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                        checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
                    }
                    if(actualWorkTimeCheckinMapList!=null){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
                    }


                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();

                    //添加缺卡记录
                    PersOrgEmployeeCheckinException exception=new PersOrgEmployeeCheckinException();
                    exception.setType("day");
                    exception.setSummaryId(checkinDaySummary.getId());
                    exception.setEmpId(empId);
                    exception.setDuration(0);
                    exception.setCount(1);
                    exception.setException(3);
                    persOrgEmployeeCheckinExceptionService.saveCheckinException(exception);

                    if(beLateMinute>0){
                        //添加迟到异常记录
                        PersOrgEmployeeCheckinException beLateException=new PersOrgEmployeeCheckinException();
                        beLateException.setType("day");
                        beLateException.setSummaryId(checkinDaySummary.getId());
                        beLateException.setEmpId(empId);
                        beLateException.setDuration(beLateMinute*60);
                        beLateException.setCount(1);
                        beLateException.setException(1);
                        persOrgEmployeeCheckinExceptionService.saveCheckinException(beLateException);
                    }
                    if(leaveEarlyMinute>0){
                        //添加早退异常记录
                        PersOrgEmployeeCheckinException leaveEarlyException=new PersOrgEmployeeCheckinException();
                        leaveEarlyException.setType("day");
                        leaveEarlyException.setSummaryId(checkinDaySummary.getId());
                        leaveEarlyException.setEmpId(empId);
                        leaveEarlyException.setDuration(leaveEarlyMinute*60);
                        leaveEarlyException.setCount(1);
                        leaveEarlyException.setException(2);
                        persOrgEmployeeCheckinExceptionService.saveCheckinException(leaveEarlyException);
                    }

                    return;

                }else if(employeeWorkCheckinRecord!=null && employeeOffWorkCheckinRecord!=null){
                    //迟到分钟
                    int beLateMinute=0;
                    //早退分钟
                    int leaveEarlyMinute=0;

                    //判断下班打卡是否早退
                    //leaveEarlyMinute=(int)(employeeOffWorkCheckinRecord.getSchCheckinTime().getTime()-employeeOffWorkCheckinRecord.getCheckinTime().getTime())/1000/60;
                    leaveEarlyMinute=DateUtils.getTimeDiffMinute(employeeOffWorkCheckinRecord.getSchCheckinTime(),employeeOffWorkCheckinRecord.getCheckinTime());
                    //判断上班打卡是否迟到
                    //beLateMinute=(int)(employeeWorkCheckinRecord.getCheckinTime().getTime()-employeeWorkCheckinRecord.getSchCheckinTime().getTime())/1000/60;
                    beLateMinute=DateUtils.getTimeDiffMinute(employeeWorkCheckinRecord.getCheckinTime(),employeeWorkCheckinRecord.getSchCheckinTime());



                    //添加日报记录
                    PersOrgEmployeeCheckinDaySummary checkinDaySummary=new PersOrgEmployeeCheckinDaySummary();
                    checkinDaySummary.setId(IdGen.getUUID());
                    checkinDaySummary.setEmpId(empId);
                    checkinDaySummary.setSummaryDate(summaryDate);
                    checkinDaySummary.setCheckinCount(2);
                    checkinDaySummary.setDayType(0);
                    checkinDaySummary.setRecordType(4);
                    checkinDaySummary.setGroupId(groupId);
                    checkinDaySummary.setDayWorkHour((Double)todayMap.get("dayWorkHour"));
                    checkinDaySummary.setGroupName(groupName);
                    checkinDaySummary.setIsException("0");
                    checkinDaySummary.setNoneedOffwork("0");
                    checkinDaySummary.setIsShouldWork("1");
                    checkinDaySummary.setIsManyCheckin(isManyCheckin);
                    checkinDaySummary.setWorkSec(employeeWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setOffWorkSec(employeeOffWorkCheckinRecord.getCheckinTime());
                    checkinDaySummary.setEarliestTime(minCheckinDate);
                    checkinDaySummary.setLastestTime(maxCheckinDate);
                    checkinDaySummary.setIsActualWork("1");
                    checkinDaySummary.setIsAbsenteeism("0");
                    checkinDaySummary.setIsRest("0");
                    checkinDaySummary.setIsActualSalary("1");

                    //判断按小时请休假的情况
                    int leaveSecond=0;
                    if(todayIsLeave){
                        //有休息时间段
                        List<Record> leaveRecordList=(List<Record>)todayMap.get("leaveRecordList");
                        if(leaveRecordList!=null && leaveRecordList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            int totalMinute=0;
                            for (Record record : leaveRecordList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("leaveType", record.getStr("leaveType"));
                                    jsonObject.put("leaveDateType", record.getStr("leaveDateType"));

                                    //int leaveMinute=(int)(record.getDate("endTime").getTime()-record.getDate("startTime").getTime())/1000/60;
                                    int leaveMinute = DateUtils.getTimeDiffMinute(record.getDate("endTime"), record.getDate("startTime"));
                                    if (leaveMinute > 0) {
                                        totalMinute += leaveMinute;
                                    }
                                    jsonArray.add(jsonObject);
                                }
                            }
                            leaveSecond=totalMinute*60;
                            //checkinDaySummary.setlea
                            checkinDaySummary.setLeaveTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsLeave("1");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }else{
                        checkinDaySummary.setIsLeave("0");
                        checkinDaySummary.setIsLeaveAllDay("0");
                    }

                    //判断按小时加班的情况
                    int overTimeSecond=0;
                    if(todayIsOvertime){
                        overTimeSecond=handleOverTime(checkinDaySummary,todayMap);
                        checkinDaySummary.setIsOvertime("1");
                    }else{
                        checkinDaySummary.setIsOvertime("0");
                    }

                    //判断按小时出差的情况
                    if(todayIsBusinessTrip){
                        List<Record> businessTripDateList=(List<Record>)todayMap.get("empBusinessTripDateList");
                        if(businessTripDateList!=null && businessTripDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : businessTripDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("destinationCity",record.getStr("destinationCity"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setBusinessTripTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsBusinessTrip("1");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }else{
                        checkinDaySummary.setIsBusinessTrip("0");
                        checkinDaySummary.setIsBusinessTripAllDay("0");
                    }
                    //判断按小时外派的情况
                    if(todayIsDispatch){
                        List<Record> dispatchDateList=(List<Record>)todayMap.get("empDispatchDateList");
                        if(dispatchDateList!=null && dispatchDateList.size()>0){
                            JSONArray jsonArray=new JSONArray();
                            for (Record record : dispatchDateList) {
                                if(record.getDate("startTime")!=null && record.getDate("endTime")!=null) {
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("startTime", DateUtils.formatDate(record.getDate("startTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("endTime", DateUtils.formatDate(record.getDate("endTime"), "yyyy-MM-dd HH:mm:ss"));
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }else{
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("dispatchDeptId", record.getStr("dispatchDeptId"));
                                    jsonObject.put("dateType",record.getStr("dateType"));
                                    jsonArray.add(jsonObject);
                                }
                            }
                            checkinDaySummary.setDispatchTimes(JSON.toJSONString(jsonArray));
                        }
                        checkinDaySummary.setIsDispatch("1");
                        if(todayMap.get("todayIsDispatchAllDay")!=null && (boolean)todayMap.get("todayIsDispatchAllDay")){
                            checkinDaySummary.setIsDispatchAllDay("1");
                            checkinDaySummary.setDayType(4);
                        }else{
                            checkinDaySummary.setIsDispatchAllDay("0");
                        }
                    }else{
                        checkinDaySummary.setIsDispatch("0");
                        checkinDaySummary.setIsDispatchAllDay("0");
                    }

                    if(workTimePeriodMapList!=null){
                        //workTimePeriodMapList
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : workTimePeriodMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setInitialWorkMinutes(totalMinute);
                        checkinDaySummary.setInitialWorkTimes(JSON.toJSONString(jsonArray));
                    }
                    if(actualWorkTimeMapList!=null){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : actualWorkTimeMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setLeaveAfterWorkMinutes(totalMinute);
                        checkinDaySummary.setLeaveAfterWorkTimes(JSON.toJSONString(jsonArray));
                    }
                    if(actualWorkTimeCheckinMapList!=null){
                        JSONArray jsonArray=new JSONArray();
                        int totalMinute=0;
                        for (Map<String, Date> dateMap : actualWorkTimeCheckinMapList) {
                            JSONObject jsonObject=new JSONObject();
                            jsonObject.put("startTime",DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonObject.put("endTime",DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));
                            jsonArray.add(jsonObject);
                            //int minute=(int)(dateMap.get("endDate").getTime()-dateMap.get("startDate").getTime())/1000/60;
                            int minute=DateUtils.getTimeDiffMinute(dateMap.get("endDate"),dateMap.get("startDate"));
                            if(minute>0){
                                totalMinute+=minute;
                            }
                        }
                        checkinDaySummary.setBusinessTripAfterWorkTimes(JSON.toJSONString(jsonArray));
                    }


                    checkinDaySummary.setMinCheckinTime(minCheckinTime);
                    checkinDaySummary.setMaxCheckinTime(maxCheckinTime);

                    //checkinDaySummary.setRegularWorkSec(regularWorkSec);
                    checkinDaySummary.setStandardWorkSec(standardWorkSecSecond);
                    if(beLateMinute<0){
                        beLateMinute=0;
                    }
                    if(leaveEarlyMinute<0){
                        leaveEarlyMinute=0;
                    }
                    int regularWorkSecond=standardWorkSecSecond-(beLateMinute*60)-(leaveEarlyMinute*60);
                    checkinDaySummary.setRegularWorkSec(regularWorkSecond);
                    checkinDaySummary.setDelFlag("0");
                    checkinDaySummary.setCreateDate(new Date());
                    checkinDaySummary.setUpdateDate(new Date());
                    checkinDaySummary.save();


                    if(beLateMinute>0){
                        //添加迟到异常记录
                        PersOrgEmployeeCheckinException beLateException=new PersOrgEmployeeCheckinException();
                        beLateException.setType("day");
                        beLateException.setSummaryId(checkinDaySummary.getId());
                        beLateException.setEmpId(empId);
                        beLateException.setDuration(beLateMinute*60);
                        beLateException.setCount(1);
                        beLateException.setException(1);
                        persOrgEmployeeCheckinExceptionService.saveCheckinException(beLateException);
                    }
                    if(leaveEarlyMinute>0){
                        //添加早退异常记录
                        PersOrgEmployeeCheckinException leaveEarlyException=new PersOrgEmployeeCheckinException();
                        leaveEarlyException.setType("day");
                        leaveEarlyException.setSummaryId(checkinDaySummary.getId());
                        leaveEarlyException.setEmpId(empId);
                        leaveEarlyException.setDuration(leaveEarlyMinute*60);
                        leaveEarlyException.setCount(1);
                        leaveEarlyException.setException(2);
                        persOrgEmployeeCheckinExceptionService.saveCheckinException(leaveEarlyException);
                    }

                    return;

                }

            }
        }

    }

    //genWorkDaySummaryNoneedOffwork()

    public static void main(String[] args) {

        /*List<Map<String,Date>> dateList=new ArrayList<>();
        Map<String,Date> map1=new HashMap<>();
        map1.put("startDate",DateUtils.parseDate("2023-12-15 00:00:00"));
        map1.put("endDate",DateUtils.parseDate("2023-12-15 23:59:59"));
        dateList.add(map1);
        Map<String,Date> map2=new HashMap<>();
        map2.put("startDate",DateUtils.parseDate("2023-12-15 00:00:00"));
        map2.put("endDate",DateUtils.parseDate("2023-12-15 10:00:00"));
        dateList.add(map2);

        Map<String,Date> map3=new HashMap<>();
        map3.put("startDate",DateUtils.parseDate("2023-12-15 00:00:00"));
        map3.put("endDate",DateUtils.parseDate("2023-12-15 12:00:00"));
        dateList.add(map3);


        List<Map<String, Date>> timePeriodListDumplictcatePeriod = getTimePeriodListDumplictcatePeriod(dateList);
        for (Map<String, Date> dateMap : timePeriodListDumplictcatePeriod) {

            System.out.println(DateUtils.formatDate(dateMap.get("startDate"),"yyyy-MM-dd HH:mm:ss")+"_______"+DateUtils.formatDate(dateMap.get("endDate"),"yyyy-MM-dd HH:mm:ss"));

        }*/
        //Date middleDate=getMiddleDate(DateUtils.parseDate("2023-12-18 18:00:00"),DateUtils.parseDate("2023-12-19 08:30:00"));
        //System.out.println(DateUtils.formatDate(middleDate,"yyyy-MM-dd HH:mm:ss"));

        List<String> parmas=new ArrayList<>();
        parmas.add("a");
        parmas.add("b");
        parmas.add("c");

        parmas.addAll(parmas);
        System.out.println(JSON.toJSONString(parmas));
    }
}
