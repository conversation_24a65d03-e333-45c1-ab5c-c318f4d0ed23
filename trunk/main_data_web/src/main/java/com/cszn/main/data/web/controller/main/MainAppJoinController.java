package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;


@RequestMapping(value="/main/appJoin", viewPath="/modules_page/main/app")
public class MainAppJoinController extends BaseController {

	@Inject
	private MainAppJoinService mainAppJoinService;




	public void index() {
		render("appJoinIndex.html");
	}


	@Clear(LogInterceptor.class)
	public void findListPage() {

		MainAppJoin app = getBean(MainAppJoin.class,"",true);
		Page<MainAppJoin> page = mainAppJoinService.findListPage(getParaToInt("page"), getParaToInt("limit"), app);
		renderJson(new DataTable<MainAppJoin>(page));
	}

	public void delete() {
		String id = getPara("id");
		boolean flag = mainAppJoinService.delete(id, AuthUtils.getUserId());
		if (flag) {
			renderJson(Ret.ok("msg", "删除成功"));
		} else {
			renderJson(Ret.fail("msg", "删除失败"));
		}
	}

	public void form() {
		String id = getPara("id");
		MainAppJoin app = mainAppJoinService.get(id);
		if (null == app) {
			app = new MainAppJoin();
			app.setAppid(IdGen.getUUID());
			String secret = IdGen.getUUID().substring(10, 20) + IdGen.getUUID();
			app.setSecret(secret);
		}
		setAttr("app", app);
		render("appJoinForm.html");
	}

	public void save() {
		MainAppJoin app = getBean(MainAppJoin.class, "app", true);
		String flag = mainAppJoinService.save(app,AuthUtils.getUserId());
		if ("suc".equals(flag)) {
			renderJson(Ret.ok("msg", "保存成功"));
		} else if("".equals(flag)){
			renderJson(Ret.fail("msg", "应用名称或应用编号不可重复"));
		}else{
			renderJson(Ret.fail("msg", "保存失败"));
		}
	}

	/**
	 * 批量删除
	 */
	public void batchDel() {
		String merchantData = getPara("appData");
		List<MainAppJoin> list = JSONArray.parseArray(merchantData, MainAppJoin.class);
		boolean flag = mainAppJoinService.batchDel(list,AuthUtils.getUserId());
		if (flag) {
			renderJson(Ret.ok("msg", "批量删除成功"));
		} else {
			renderJson(Ret.fail("msg", "批量删除失败"));
		}
	}

}
