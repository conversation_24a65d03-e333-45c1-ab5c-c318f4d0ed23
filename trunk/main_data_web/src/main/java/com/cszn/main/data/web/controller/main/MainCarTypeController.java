package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCarTypeService;
import com.cszn.integrated.service.entity.main.MainCarType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/main/carType",viewPath = "/modules_page/main/carType")
public class MainCarTypeController extends BaseController {

    @Inject
    private MainCarTypeService mainCarTypeService;

    public void index(){
        render("carTypeIndex.html");
    }

    @Clear(LogInterceptor.class)
    public void findPageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainCarType carType=getBean(MainCarType.class,"",true);
        Page<MainCarType> page=mainCarTypeService.findCarTypePage(pageNumber,pageSize,carType);
        renderJson(new DataTable<MainCarType>(page));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainCarType carType=mainCarTypeService.findById(id);
            setAttr("carType",carType);
        }
        render("carTypeForm.html");
    }

    /**
     * 保存
     */
    public void saveCarType(){
        MainCarType carType=getBean(MainCarType.class,"",true);
        if(mainCarTypeService.findCarTypeByIdName(carType.getId(),carType.getName())!=null){
            renderJson(Ret.fail("msg","该车型已经存在"));
            return;
        }
        boolean flag=mainCarTypeService.saveCarType(carType, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 删除
     */
    public void delCarType(){
        String id=getPara("id");
        boolean flag=mainCarTypeService.delCarType(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


}
