package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseAdvanceBookSettingService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.entity.main.MainBaseAdvanceBookSetting;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

/**
 * 基地配置
 */
@RequestMapping(value = "/main/baseAdvanceBookSetting",viewPath = "/modules_page/main/baseAdvanceBookSetting")
public class MainBaseAdvanceBookSettingController extends BaseController {

    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private MainBaseAdvanceBookSettingService mainBaseAdvanceBookSettingService;

    /**
     * 首页
     */
    public void index(){
        String baseId=getCookie("baseId");
        List<MainBaseAdvanceBookSetting> bookSettingList=mainBaseAdvanceBookSettingService.baseAdvanceBookSettingList(baseId);
        List<Record> recordList=new ArrayList<>();
        for(MainBaseAdvanceBookSetting setting:bookSettingList){
            if(StrKit.notBlank(setting.getRoomPlanRule())){
                JSONObject jsonObject=JSON.parseObject(setting.getRoomPlanRule());
                Record record=new Record();
                for(Object key:jsonObject.keySet()){
                    record.set((String)key,jsonObject.get(key));
                }
                recordList.add(record);
            }

        }
        setAttr("baseId",baseId);
        setAttr("bookSettingList",bookSettingList);
        setAttr("recordList",recordList);
        render("baseAdvanceBookSettingIndex.html");
    }

    /**
     * 保存基地配置
     */
    public void saveBaseAdvanceBookSetting(){
        Integer count=getParaToInt("count");
        String baseId=getPara("baseId");
        String delIds=getPara("delIds");
        List<MainBaseAdvanceBookSetting> settingList=new ArrayList<>();
        for(int i=1;i<=count;i++){
            MainBaseAdvanceBookSetting setting=getBean(MainBaseAdvanceBookSetting.class,"settingList["+i+"]",true);
            if(StrKit.notBlank(setting.getStartDate()) && StrKit.notBlank(setting.getEndDate())){
                String BeginDate=getPara("settingList["+i+"].BeginDate");
                String EndDate=getPara("settingList["+i+"].EndDate");
                String RoomDays=getPara("settingList["+i+"].RoomDays");
                String Floor=getPara("settingList["+i+"].Floor");
                String BedNo=getPara("settingList["+i+"].BedNo");
                String HasEntourageBigBed=getPara("settingList["+i+"].HasEntourageBigBed");
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("BeginDate",StrKit.isBlank(BeginDate)?null:Double.valueOf(BeginDate));
                jsonObject.put("EndDate",StrKit.isBlank(EndDate)?null:Double.valueOf(EndDate));
                jsonObject.put("RoomDays",StrKit.isBlank(RoomDays)?null:Double.valueOf(RoomDays));
                jsonObject.put("Floor",StrKit.isBlank(Floor)?null:Double.valueOf(Floor));
                jsonObject.put("BedNo",StrKit.isBlank(BedNo)?null:Double.valueOf(BedNo));
                jsonObject.put("HasEntourageBigBed",StrKit.isBlank(HasEntourageBigBed)?null:Double.valueOf(HasEntourageBigBed));

                setting.setRoomPlanRule(JSON.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue));
                setting.setBaseId(baseId);
                settingList.add(setting);
            }
        }
        boolean flag=mainBaseAdvanceBookSettingService.saveBaseAdvanceBookSetting(settingList,delIds, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 删除基地配置
     */
    public void delBaseAdvanceBookSetting(){
        String id=getPara("id");
        boolean flag=mainBaseAdvanceBookSettingService.delBaseAdvanceBookSetting(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
