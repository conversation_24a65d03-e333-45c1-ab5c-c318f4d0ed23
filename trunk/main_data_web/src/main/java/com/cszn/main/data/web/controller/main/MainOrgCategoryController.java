package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainOrgCategoryService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainOrgCategory;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/main/orgCategory",viewPath = "/modules_page/main/orgCategory/")
public class MainOrgCategoryController extends BaseController {

    @Inject
    private MainOrgCategoryService mainOrgCategoryService;
    @Inject
    private MainSyncRecordService mainSyncRecordService;

    public void index() {
        render("orgCategoryIndex.html");
    }
    
    /**
     * 角色分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	MainOrgCategory model = getBean(MainOrgCategory.class, "", true);
        Page<MainOrgCategory> modelPage = mainOrgCategoryService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MainOrgCategory>(modelPage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	MainOrgCategory model = new MainOrgCategory();
    	setAttr("model", model);
        render("orgCategoryForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String modelId = getPara("id");
        setAttr("model", mainOrgCategoryService.findById(modelId));
        render("orgCategoryForm.html");
    }
    
    public void save() {
    	MainOrgCategory model = getBean(MainOrgCategory.class, "", true);
    	if(StrKit.isBlank(model.getId())) {
    		model.setCreateBy(AuthUtils.getUserId());
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    	}
		renderJson(mainOrgCategoryService.modelSave(model));
    }
    
    public void del(){
    	final MainOrgCategory model = getBean(MainOrgCategory.class, "", true);
    	if(mainOrgCategoryService.update(model)){
    		final boolean flag=mainSyncRecordService.saveSyncRecord(SyncType.persOrgCategory.getKey(),SyncDataType.DELETE,model.getId(),AuthUtils.getUserId());
			if(flag) {
				renderJson(Ret.ok("msg", "操作成功!"));
			}else {
				renderJson(Ret.fail("msg", "同步记录失败!"));
			}
		}
    }
}
