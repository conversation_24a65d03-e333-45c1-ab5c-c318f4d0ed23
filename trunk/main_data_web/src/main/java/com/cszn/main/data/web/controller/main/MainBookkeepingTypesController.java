package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBookkeepingTypesService;
import com.cszn.integrated.service.entity.enums.BookkeepingType;
import com.cszn.integrated.service.entity.main.MainBookkeepingTypes;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.codehaus.groovy.transform.AbstractASTTransformUtil;

import java.util.HashMap;
import java.util.Map;

@RequestMapping(value = "/main/bookkeepingType",viewPath = "/modules_page/main/bookkeepingType")
public class MainBookkeepingTypesController extends BaseController {

    @Inject
    private MainBookkeepingTypesService mainBookkeepingTypesService;

    public void index(){
        render("bookkeepingTypeIndex.html");
    }

    public void pageList(){
        MainBookkeepingTypes type=getBean(MainBookkeepingTypes.class,"",true);

        Page<MainBookkeepingTypes> page=mainBookkeepingTypesService.pageList(getParaToInt("page"),getParaToInt("limit"),type);

        renderJson(new DataTable<MainBookkeepingTypes>(page));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainBookkeepingTypes type=mainBookkeepingTypesService.findById(id);
            setAttr("type",type);
        }

        Map<String,String> codeMap=new HashMap<>();

        for(BookkeepingType type : BookkeepingType.values()){
            codeMap.put(type.getKey(),type.getValue());
        }
        setAttr("codes",codeMap);
        render("bookkeepingTypeForm.html");
    }

    public void saveType(){
        MainBookkeepingTypes type=getBean(MainBookkeepingTypes.class,"",true);

        //判断编号是否重复
        if(StrKit.notBlank(type.getId())){
            String sql="select * from main_bookkeeping_types where bookkeeping_type_code=? and id<>?  ";
            if(Db.findFirst(sql,type.getBookkeepingTypeCode(),type.getId())!=null){
                renderJson(Ret.fail("msg","该编号已存在"));
                return;
            }
        }else{
            String sql="select * from main_bookkeeping_types where bookkeeping_type_code=?  ";
            if(Db.findFirst(sql,type.getBookkeepingTypeCode())!=null){
                renderJson(Ret.fail("msg","该编号已存在"));
                return;
            }
        }

        boolean flag=mainBookkeepingTypesService.saveType(type, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
