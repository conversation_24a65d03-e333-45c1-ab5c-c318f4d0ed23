package com.cszn.main.data.web.support.enjoy.directive;

import java.util.ArrayList;
import java.util.List;

import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Inject;
import com.jfinal.template.Env;
import com.jfinal.template.expr.ast.Expr;
import com.jfinal.template.expr.ast.ExprList;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.Scope;

import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

/**
 * 替换数据表格数据字典的value值为label
 */
@JFinalDirective("getDictLabel")
public class DictLabelDirective extends JbootDirectiveBase {

	@Inject
	DictService dictService;

	/**
	 * 封装参数列表的集合
	 */
	private List<Expr> exprLists = new ArrayList<Expr>();
	
	// ExprList 代表指令参数表达式列表
	public void setExprList(ExprList exprList){
		super.setExprList(exprList);
		
		if (exprList != null) {
			
			// 获取所有的参数
			Expr[] exprArray = exprList.getExprArray() ;
			
			if (exprArray.length > 0) {
				for (int i = 0; i < exprArray.length; i++) {
					Expr expr = exprArray[i] ;
					exprLists.add(expr) ;
				}
			}
		}
	}
	
	@Override
	public void exec(Env env, Scope scope, Writer writer) {
		String retStr = "<div data-id=\"dict\" style=\"display:none;\">";
		if (exprLists != null && exprLists.size() > 0) { // 如果存在参数列表
			for (int i=0;i<exprLists.size();i++) { // 获取参数
				Expr expr = exprLists.get(i);
				// 获取数据字典集合
				List<Dict> dictList = dictService.findDictList(expr.toString()) ;
				retStr += "<div data-key=\""+exprLists.get(i)+"\">";
				if (dictList != null && dictList.size() > 0) {
					for (int j = 0;j < dictList.size(); j++) {
						// 获取数据字典对象
						Dict d = dictList.get(j);
						retStr += "<p data-value=\""+d.getDictValue()+"\" data-label=\""+d.getDictName()+"\"></p>";
					}
				}
				retStr += "</div>";
			}
		}
		retStr += "</div>";
		try {
			write(writer, retStr);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}



    @Override
	public void onRender(Env env, Scope scope, Writer writer) {

	}

}
