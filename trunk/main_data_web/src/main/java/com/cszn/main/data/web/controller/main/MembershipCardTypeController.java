package com.cszn.main.data.web.controller.main;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainCardTypeConfigService;
import com.cszn.integrated.service.api.main.MainCardYearLimitService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.entity.main.MainCardTypeConfig;
import com.cszn.integrated.service.entity.main.MainCardYearLimit;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;


@RequestMapping(value="/main/cardtype", viewPath="/modules_page/main/membershipCardType")
public class MembershipCardTypeController extends BaseController {




	@Inject
	private MainMembershipCardTypeService mainMembershipCardTypeService;
	@Inject
	private MainCardTypeConfigService mainCardTypeConfigService;
	@Inject
    private MainCardYearLimitService mainCardYearLimitService;

	/**
	 * 跳转会员卡类别页面
	 */
	public void index(){
		render("typeIndex.html");
	}


	/**
	 * 分页获取会员卡类别列表
	 */
	@Clear(LogInterceptor.class)
	public void findListPage(){
		MainMembershipCardType type = getBean(MainMembershipCardType.class,"",true);
		Page<MainMembershipCardType> page = mainMembershipCardTypeService.findListPage(getParaToInt("page"),getParaToInt("limit"),type);
		renderJson(new DataTable<MainMembershipCardType>(page));
	}




	/**
	 * 删除
	 */
	public void delete(){
		String id = getPara("id");
		String userId = AuthUtils.getUserId();
		boolean bl = mainMembershipCardTypeService.delete(id,userId);
		if (bl) {
			renderJson(Ret.ok("msg", "作废成功"));
		} else {
			renderJson(Ret.fail("msg", "作废失败"));
		}
	}


	/**
	 * 跳转新增/修改 界面
	 */
	public void form(){
		String id = getPara("id");
		MainMembershipCardType type = null;
		if (StringUtils.isNotBlank(id)) {
			type = mainMembershipCardTypeService.get(id);
		}
		List<MainCardYearLimit> cardYearLimitList=mainCardYearLimitService.findCardYearLimitList();
		setAttr("type", type);
		setAttr("cardYearLimitList",cardYearLimitList);
		render("typeForm.html");
	}
	
	/**
	 * 积分配置 界面
	 */
	public void integralConfig(){
		String id = getPara("id");
		setAttr("cardTypeId", id);
		setAttr("configList", mainCardTypeConfigService.findListByTypeId(id));
		render("integralConfig.html");
	}


	/**
	 * 保存
	 */
	public void save(){
		MainMembershipCardType type = getBean(MainMembershipCardType.class,"type",true);
		if(type.getCardMin() != null && type.getCardMax() != null){
			if(type.getCardMin().length() != type.getCardMax().length()){
				renderJson(Ret.fail("msg", "会员卡号段位数必须一致"));
				return;
			}
            if(Integer.parseInt(type.getCardMin()) >= Integer.parseInt(type.getCardMax())){
				renderJson(Ret.fail("msg", "会员卡号最小值不可大于等于最大值"));
                return;
			}
		}
		if(StrKit.notBlank(type.getCardType()) && mainMembershipCardTypeService.cardTypeIsExist(type.getId(),type.getCardType(),null)!=null){
			renderJson(Ret.fail("msg", "会员卡类别不可重复"));
			return;
		}
		if(StrKit.notBlank(type.getCardPrefix()) && mainMembershipCardTypeService.cardTypeIsExist(type.getId(),null,type.getCardPrefix())!=null){
			renderJson(Ret.fail("msg", "会员卡前缀不可重复"));
			return;
		}
		boolean flag = mainMembershipCardTypeService.saveMainMembershipCardType(type,AuthUtils.getUserId());
		if (flag){
			renderJson(Ret.ok("msg", "保存成功"));
		}else{
			renderJson(Ret.fail("msg", "保存失败"));
		}
	}
	
    public void saveConfig() {
    	final int configCount = getParaToInt("configCount");
		List<MainCardTypeConfig> roomDoorList = new ArrayList<MainCardTypeConfig>();
		if(configCount>0){
			for (int i = 1; i <= configCount; i++) {
				final MainCardTypeConfig config = getBean(MainCardTypeConfig.class, "configList["+i+"]");
				if(StrKit.notBlank(config.getConfigName())){
					roomDoorList.add(config);
				}
			}
		}
		if(mainCardTypeConfigService.configSave(roomDoorList, AuthUtils.getUserId())){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }


	/**
	 * 批量删除
	 */
	public void batchDel(){
		String typeData =  getPara("typeData");
		List<MainMembershipCardType> list = JSONArray.parseArray(typeData,MainMembershipCardType.class);
		String userId = AuthUtils.getUserId();
		boolean flag = mainMembershipCardTypeService.batchDelType(list,userId);
		if(flag){
			renderJson(Ret.ok("msg", "批量作废成功"));
		}else{
			renderJson(Ret.fail("msg", "批量作废失败"));
		}

	}
	
    public void delConfig() {
    	MainCardTypeConfig config = getBean(MainCardTypeConfig.class, "", true);
		if(mainCardTypeConfigService.update(config)){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
}
