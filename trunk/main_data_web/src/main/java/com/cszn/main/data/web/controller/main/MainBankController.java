/**
 * 
 */
package com.cszn.main.data.web.controller.main;

import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBankService;
import com.cszn.integrated.service.entity.main.MainBank;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2022年1月27日
 *
 * MainBaseDoorController
 */
@RequestMapping(value="/main/bank", viewPath="/modules_page/main/bank")
public class MainBankController extends BaseController {

	@Inject
    private MainBankService mainBankService;
	
    public void index() {
        render("bankIndex.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	MainBank model = getBean(MainBank.class, "", true);
        Page<MainBank> modelPage = mainBankService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MainBank>(modelPage));
    }
    
    /**
     * 添加页面方法
     */
    public void add() {
    	MainBank model = getBean(MainBank.class, "", true);
    	setAttr("model", model);
        render("bankForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
    	MainBank model = getBean(MainBank.class, "", true);
        setAttr("model", mainBankService.findById(model.getId()));
        render("bankForm.html");
    }
    
    public void save() {
    	MainBank model = getBean(MainBank.class, "", true);
    	boolean flag = false;
    	if(StringUtils.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateDate(new Date());
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setUpdateDate(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		flag = model.save();
    	}else {
    		model.setUpdateDate(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		flag = model.update();
    	}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    public void delete() {
    	MainBank model = getBean(MainBank.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateDate(new Date());
		if(model.update()){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
}
