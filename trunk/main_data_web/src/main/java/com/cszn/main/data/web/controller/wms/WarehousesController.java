package com.cszn.main.data.web.controller.wms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.wms.WmsWarehouseAreaService;
import com.cszn.integrated.service.api.wms.WmsWarehouseLocationService;
import com.cszn.integrated.service.api.wms.WmsWarehouseService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.enums.WarehouseOrgType;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.wms.*;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping(value = "/wms/warehouse",viewPath = "/modules_page/wms/warehouses")
public class WarehousesController extends BaseController {

    @Inject
    WmsWarehouseService wmsWarehouseService;
    @Inject
    WmsWarehouseAreaService wmsWarehouseAreaService;
    @Inject
    WmsWarehouseLocationService wmsWarehouseLocationService;
    @Inject
    MainBaseService mainBaseService;
    @Inject
    private UserService userService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    MainSyncRecordService mainSyncRecordService;

    /**
     * 仓库管理首页
     */
    public void index(){

        render("warehousesIndex.html");
    }

    /**
     * 仓库分页
     */
    public void findWarehousePageList(){
        WmsWarehouses wmsWarehouses=getBean(WmsWarehouses.class,"",true);
        Page<WmsWarehouses> page=wmsWarehouseService.findWarehousePageList(getParaToInt("page"),getParaToInt("limit"),wmsWarehouses);

        renderJson(new DataTable<WmsWarehouses>(page));
    }

    /**
     * 获取库区分页
     */
    public void findWarehouseAreaPageList(){
        WmsWarehouseAreas warehouseAreas=getBean(WmsWarehouseAreas.class,"",true);
        Page<WmsWarehouseAreas> page=wmsWarehouseAreaService.findWarehouseAreaPageList(getParaToInt("page"),getParaToInt("limit"),warehouseAreas);
        renderJson(new DataTable<WmsWarehouseAreas>(page));
    }

    /**
     * 获取库位分页
     */
    public void findWarehouseLocationPageList(){
        WmsWarehouseLocations location=getBean(WmsWarehouseLocations.class,"",true);
        Page<WmsWarehouseLocations> page=wmsWarehouseLocationService.findWarehouseLocationPageList(getParaToInt("page"),getParaToInt("limit"),location);
        renderJson(new DataTable<WmsWarehouseLocations>(page));
    }

    /**
     * 仓库表单
     */
    public void warehouseForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            WmsWarehouses warehouse=wmsWarehouseService.findById(id);
            set("warehouse",warehouse);
            List<String> warehouseIdList=Db.query("select org_id  from wms_warehouse_org_rel where warehouse_id=? ",id);
            set("warehouseIdList",warehouseIdList);
        }
        List<User> userList=userService.findUnlockUserList();

        setAttr("userList",userList);
        setAttr("baseList",mainBaseService.findBaseList());
        render("warehouseForm.html");
    }

    /**
     * 库区表单
     */
    public void warehouseAreaForm(){
        String id=getPara("id");
        String warehouseId=getPara("warehouseId");
        if(StrKit.notBlank(id)){
            WmsWarehouseAreas area=wmsWarehouseAreaService.findById(id);
            set("area",area);
        }
        set("warehouseId",warehouseId);
        render("warehouseAreaForm.html");
    }

    /**
     * 库位表单
     */
    public void warehouseLocationForm(){
        String id=getPara("id");
        String warehouseAreaId=getPara("warehouseAreaId");
        if(StrKit.notBlank(id)){
            WmsWarehouseLocations location=wmsWarehouseLocationService.findById(id);
            set("location",location);
        }
        set("warehouseAreaId",warehouseAreaId);
        render("warehouseLocationForm.html");
    }

    public void saveWarehouse(){
        WmsWarehouses wmsWarehouse=getBean(WmsWarehouses.class,"",true);
        String deptIds=getPara("deptIds");
        JSONArray deptIdArray=JSON.parseArray(deptIds);
        String orgIdStr="";
        if(deptIdArray!=null && deptIdArray.size()>0){
            String str="";
            List<String> params=new ArrayList<>();
            for(int i=0;i<deptIdArray.size();i++){
                str+="?,";
                params.add(deptIdArray.getString(i));
                orgIdStr+=deptIdArray.getString(i)+",";
            }
            orgIdStr=orgIdStr.substring(0,orgIdStr.length()-1);
            str=str.substring(0,str.length()-1);
            String sql=" select org_id,warehouse_id from wms_warehouse_org_rel where  org_id in("+str+") ";
            if(StrKit.notBlank(wmsWarehouse.getId())){
                sql+=" and warehouse_id <>? ";
                params.add(wmsWarehouse.getId());
            }
            List<Record> deptRangeList=Db.find(sql,params.toArray());
            if(deptRangeList!=null && deptRangeList.size()>0){
                String msg="";
                /*for(Record record:deptRangeList){
                    String orgId=record.getStr("org_id");
                    String warehouseId=record.getStr("warehouse_id");
                    String orgNames=persOrgService.getOrgParentNames(orgId);
                    WmsWarehouses wmsWarehouses=wmsWarehouseService.findById(warehouseId);
                    msg+="组织机构："+orgNames+"已被"+wmsWarehouses.getName()+"设置；";
                }*/
                renderJson(Ret.fail("msg",msg));
                return;
            }
        }


        if(StrKit.notBlank(wmsWarehouse.getId())){
            wmsWarehouse.setUpdateBy(AuthUtils.getUserId());
        }else{
            wmsWarehouse.setCreateBy(AuthUtils.getUserId());
            wmsWarehouse.setUpdateBy(AuthUtils.getUserId());
        }
        boolean flag=wmsWarehouseService.saveWarehouse(wmsWarehouse);
        if(flag){
            wmsWarehouseService.saveWarehouseOrgRel(wmsWarehouse.getId(),orgIdStr,AuthUtils.getUserId());

            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveWarehouseArea(){
        WmsWarehouseAreas area=getBean(WmsWarehouseAreas.class,"",true);
        if(StrKit.notBlank(area.getId())){
            area.setUpdateBy(AuthUtils.getUserId());
        }else{
            area.setCreateBy(AuthUtils.getUserId());
            area.setUpdateBy(AuthUtils.getUserId());
        }
        boolean flag=wmsWarehouseAreaService.saveWarehouseArea(area);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveWarehouseLocation(){
        WmsWarehouseLocations location=getBean(WmsWarehouseLocations.class,"",true);
        if(StrKit.notBlank(location.getId())){
            location.setUpdateBy(AuthUtils.getUserId());
        }else{
            location.setCreateBy(AuthUtils.getUserId());
            location.setUpdateBy(AuthUtils.getUserId());
        }
        boolean flag=wmsWarehouseLocationService.saveWarehouseLocation(location);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void addAdminForm(){
        String warehouseId=getPara("id");
        List<Record> recordList= Db.find("select a.id,a.user_id,a.is_receiver,a.meal_receiver,b.`name`,b.user_name from wms_warehouse_manager a left join sys_user b on a.user_id=b.id where a.warehouse_id=? ",warehouseId);

        List<Record> userList=Db.find("select id,user_name as userName,name from sys_user where del_flag='0' and login_flag='un_locked' ");
        setAttr("adminList",recordList);
        setAttr("warehouseId",warehouseId);
        setAttr("userList",userList);
        render("addAdminForm.html");
    }

    public void saveAdmin(){
        Integer count=getParaToInt("count");
        String warehouseId=getPara("warehouseId");

        List<WmsWarehouseManager> managerList=new ArrayList<>();
        List<WmsWarehouseManager> updateManagerList=new ArrayList<>();
        int receiveCount=0;
        int mealReceiveCount=0;
        for(int i=1;i<=count;i++){
            WmsWarehouseManager manager=getBean(WmsWarehouseManager.class,"adminList["+i+"]",true);
            if(StrKit.notBlank(manager.getUserId())){
                if("1".equals(manager.getIsReceiver())){
                    receiveCount++;
                }
                if("1".equals(manager.getMealReceiver())){
                	mealReceiveCount++;
                }
                if(StrKit.notBlank(manager.getId())){
                    Record record=Db.findFirst("select * from wms_warehouse_manager where user_id=? and warehouse_id=? and id<>?",manager.getUserId(),warehouseId,manager.getId());
                    if(record!=null){
                        renderJson(Ret.fail("msg","请勿重复添加"));
                        return;
                    }
                    updateManagerList.add(manager);
                }else{
                    Record record=Db.findFirst("select * from wms_warehouse_manager where user_id=? and warehouse_id=?",manager.getUserId(),warehouseId);
                    if(record!=null){
                        renderJson(Ret.fail("msg","请勿重复添加"));
                        return;
                    }
                    manager.setId(IdGen.getUUID());
                    manager.setWarehouseId(warehouseId);
                    managerList.add(manager);
                }
            }
        }
        if(receiveCount>2){
            renderJson(Ret.fail("msg","最多只能设置两个物资接收员"));
            return;
        }
        if(mealReceiveCount>1){
        	renderJson(Ret.fail("msg","只能设置一个餐饮接收员"));
        	return;
        }

        if(managerList.size()>0 || updateManagerList.size()>0){

            boolean flag=wmsWarehouseService.saveWarehouseManager(managerList,updateManagerList,AuthUtils.getUserId());
            if(flag){
                renderJson(Ret.ok("msg","操作成功"));
                return;
            }else{
                renderJson(Ret.fail("msg","操作失败"));
                return;
            }
        }
        renderJson(Ret.fail("msg","操作失败"));
    }

    public void delAdmin(){
        String id=getPara("id");

        boolean flag=wmsWarehouseService.delWarehouseManager(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void orgXmSelectTree() {
        List<Record> orgList=null;
        String sql="select * from pers_org b  " +
                "where  b.del_flag='0' and b.is_enable='1' ";
        orgList=Db.find(sql);

        if(orgList==null || orgList.size()==0){
            renderJson(new Object());
            return;
        }
        Map<String, ZTree> zTreeMap=new HashMap<>();
        for(Record record:orgList){
            ZTree zTree=new ZTree(record.getStr("id"),record.getStr("org_name"),record.getStr("org_type"),record.getStr("parent_id"),record.getStr("parent_ids"));
            zTreeMap.put(record.getStr("id"),zTree);
        }
        List<ZTree> returnZtree=new ArrayList<>();

        for(Record record:orgList ){
            if("00000000-0000-0000-0000-000000000000".equals(record.getStr("parent_id"))){
                //第一级直接添加
                returnZtree.add(zTreeMap.get(record.getStr("id")));
            }else{
                // 不是第一级，帮它找到parent，并把它自己设置到parent里，如果没有parent则自己为最高级
                ZTree parent=zTreeMap.get(record.getStr("parent_id"));
                if(parent==null){
                    returnZtree.add(zTreeMap.get(record.getStr("id")));
                }else{
                    List<ZTree> children=parent.getChildren();
                    if(children==null){
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(zTreeMap.get(record.getStr("id")));
                }
            }
        }


        renderJson(returnZtree);
    }

    public void warehouseOrgTypeIndex(){
        String warehouseId=getPara("id");

        String sql="select a.warehouse_id,a.org_id as orgId,b.type from wms_warehouse_org_rel a  " +
                "left join wms_warehouse_org_type_rel b on a.warehouse_id=b.warehouse_id and a.org_id=b.org_id " +
                "where a.warehouse_id=? ";
        List<Record> warehouseOrgTypeList=Db.find(sql,warehouseId);

        for (Record record : warehouseOrgTypeList) {
            record.set("orgName",persOrgService.getOrgParentNames(record.getStr("orgId")));

        }

        setAttr("warehouseOrgTypes", WarehouseOrgType.values());
        setAttr("warehouseOrgTypeList",warehouseOrgTypeList);
        setAttr("warehouseId",warehouseId);
        render("warehouseOrgTypeIndex.html");
    }

    public void warehouseOrgTypeSave(){
        Integer count=getParaToInt("count");
        String warehouseId=getPara("warehouseId");
        String orgStrId="";
        int update = Db.update("delete from wms_warehouse_org_type_rel where warehouse_id=?", warehouseId);
        List<WmsWarehouseOrgTypeRel> typeRelList=new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            String orgId=getPara("warehouseOrgType["+i+"].orgId");
            String type=getPara("warehouseOrgType["+i+"].type");
            if(StrKit.notBlank(orgId) && StrKit.notBlank(type)){
                String warId=Db.queryStr("select warehouse_id from wms_warehouse_org_type_rel where `type`=? and org_id=? and warehouse_id<>? ",type,orgId,warehouseId);

                if(StrKit.notBlank(warId)){
                    WarehouseOrgType orgType=WarehouseOrgType.valueOf(type);
                    String orgName=persOrgService.getOrgParentNames(orgId);
                    WmsWarehouses wmsWarehouses=wmsWarehouseService.findById(warId);
                    renderCodeFailed(orgType.getValue()+"+"+orgName+"已被"+wmsWarehouses.getName()+"添加，请勿重复添加");
                    return;
                }
                if(orgStrId.indexOf(orgId)==-1){
                    orgStrId+=orgId+",";
                }
                WmsWarehouseOrgTypeRel orgTypeRel=new WmsWarehouseOrgTypeRel();
                orgTypeRel.setId(IdGen.getUUID());
                orgTypeRel.setWarehouseId(warehouseId);
                orgTypeRel.setType(type);
                orgTypeRel.setOrgId(orgId);
                orgTypeRel.save();
                typeRelList.add(orgTypeRel);
            }
        }
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("warehouseId",warehouseId);
        if(typeRelList.size()==0){
            jsonObject.put("orgList",new JSONArray());
            mainSyncRecordService.saveSyncRecord(SyncType.warehouseOrgTypeRel.getKey(), SyncDataType.INSERT, JSON.toJSONString(jsonObject),AuthUtils.getUserId());
        }else{
            JSONArray jsonArray=new JSONArray();
            for (WmsWarehouseOrgTypeRel orgTypeRel : typeRelList) {
                JSONObject obj=new JSONObject();
                obj.put("id",orgTypeRel.getId());
                obj.put("orgId",orgTypeRel.getOrgId());
                obj.put("type",orgTypeRel.getType());
                jsonArray.add(obj);
            }
            jsonObject.put("orgList",jsonArray);
            mainSyncRecordService.saveSyncRecord(SyncType.warehouseOrgTypeRel.getKey(), SyncDataType.INSERT, JSON.toJSONString(jsonObject),AuthUtils.getUserId());
        }

        wmsWarehouseService.saveWarehouseOrgRel(warehouseId,orgStrId,AuthUtils.getUserId());

        renderJson(Ret.ok("msg","操作成功"));
    }
}
