package com.cszn.main.data.web.controller.main;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseBedMarkupTypeService;
import com.cszn.integrated.service.entity.main.MainBaseBedMarkupType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value = "/main/bedMarkupType",viewPath = "/modules_page/main/bedMarkupType")
public class MainBedMarkupTypeController extends BaseController {

    @Inject
    private MainBaseBedMarkupTypeService mainBaseBedMarkupTypeService;

    public void index(){
        render("bedMarkupTypeIndex.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainBaseBedMarkupType bedMarkupType=mainBaseBedMarkupTypeService.findById(id);
            setAttr("bedMarkupType",bedMarkupType);
        }
        render("bedMarkupTypeForm.html");
    }

    @Clear(LogInterceptor.class)
    public void findListPage(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        String name=getPara("name");

        MainBaseBedMarkupType bedMarkupType=new MainBaseBedMarkupType();
        bedMarkupType.setName(name);
        Page<MainBaseBedMarkupType> bedMarkupTypePage=mainBaseBedMarkupTypeService.findListPage(pageNumber,pageSize,bedMarkupType);

        renderJson(new DataTable<MainBaseBedMarkupType>(bedMarkupTypePage));
    }

    public void save(){
        MainBaseBedMarkupType bedMarkupType=getBean(MainBaseBedMarkupType.class,"bedMarkupType",true);
        //添加的时候判断name是否存在
        MainBaseBedMarkupType findBedMarkupType=mainBaseBedMarkupTypeService.findBedMarkupTypeByName(bedMarkupType.getName());
        if(StrKit.isBlank(bedMarkupType.getId())){
            if(findBedMarkupType!=null){
                renderJson(Ret.fail("msg","该名字已经存在"));
                return;
            }
        }else{
            if(findBedMarkupType!=null && !findBedMarkupType.getId().equals(bedMarkupType.getId())){
                renderJson(Ret.fail("msg","该名字已经存在"));
                return;
            }
        }

        boolean flag=mainBaseBedMarkupTypeService.saveBedMarkupType(bedMarkupType, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delete(){
        String id=getPara("id");
        boolean flag=mainBaseBedMarkupTypeService.delBedMarkupType(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 批量删除
     */
    public void batchDel(){
        String bedStatusData =  getPara("bedMarkupTypeData");
        List<MainBaseBedMarkupType> list = JSONArray.parseArray(bedStatusData,MainBaseBedMarkupType.class);
        boolean flag = mainBaseBedMarkupTypeService.batchDelBedMarkupType(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量删除成功"));
        }else{
            renderJson(Ret.fail("msg", "批量删除失败"));
        }
    }
}
