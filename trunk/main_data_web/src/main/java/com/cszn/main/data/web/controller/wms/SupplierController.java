package com.cszn.main.data.web.controller.wms;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.api.wms.WmsSupplierService;
import com.cszn.integrated.service.api.wms.WmsSuppliersLinkService;
import com.cszn.integrated.service.api.wms.WmsSuppliersPayService;
import com.cszn.integrated.service.entity.enums.PaymentWay;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.integrated.service.entity.wms.WmsSuppliers;
import com.cszn.integrated.service.entity.wms.WmsSuppliersLink;
import com.cszn.integrated.service.entity.wms.WmsSuppliersPay;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

@RequestMapping(value = "/wms/supplier",viewPath = "/modules_page/wms/supplier")
public class SupplierController extends BaseController {

    @Inject
    WmsSupplierService wmsSupplierService;
    @Inject
    WmsSuppliersPayService wmsSupplierPayService;
    @Inject
    WmsSuppliersLinkService wmsSupplierLinkService;
    @Inject
    private AreaService areaService;
    @Inject
	private FinaMembershipCardService finaMembershipCardService;
    @Inject
	MmsMemberService mmsMemberService;

    public void index(){
        render("supplierIndex.html");
    }
    
    public void payIndex(){
    	final String suppliersId = getPara("id");
		Map<String, String> payWay = new HashMap<>();
		for (PaymentWay paymentWay : PaymentWay.values()) {
			payWay.put(paymentWay.getKey(), paymentWay.getValue());
		}
    	setAttr("suppliersId", suppliersId);
    	setAttr("payWay", payWay);
    	render("payIndex.html");
    }
    
    public void linkIndex(){
    	final String suppliersId = getPara("id");
    	setAttr("suppliersId", suppliersId);
    	render("linkIndex.html");
    }
    
    public void supplierLinkIndex(){
    	List<WmsSuppliers> suppliersList = wmsSupplierService.findList();
    	setAttr("suppliersList", suppliersList);
    	render("supplierLinkIndex.html");
    }

    public void supplierPage(){
        WmsSuppliers wmsSupplier=getBean(WmsSuppliers.class,"",true);
        Page<WmsSuppliers> page=wmsSupplierService.findSupplierPageList(getParaToInt("page"),getParaToInt("limit"),wmsSupplier);

        renderJson(new DataTable<WmsSuppliers>(page));
    }
    
    public void payPage(){
    	WmsSuppliersPay model = getBean(WmsSuppliersPay.class,"",true);
    	Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL).add("supplier_id", model.getSupplierId());
		if(StrKit.notBlank(model.getPaymentWay())) {
			columns.eq("payment_way", model.getPaymentWay());
		}
		if(StrKit.notBlank(model.getPayAccount())) {
			columns.likeAppendPercent("pay_account", model.getPayAccount());
		}
		Page<WmsSuppliersPay> modelPage = wmsSupplierPayService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
		renderJson(new DataTable<WmsSuppliersPay>(modelPage));
    }
    
    public void linkPage(){
    	WmsSuppliersLink model = getBean(WmsSuppliersLink.class,"",true);
    	Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
    	if(StrKit.notBlank(model.getSupplierId())) {
    		columns.eq("supplier_id", model.getSupplierId());
    	}
    	if(StrKit.notBlank(model.getLinkType())) {
    		columns.eq("link_type", model.getLinkType());
    	}
    	if(StrKit.notBlank(model.getLinkName())) {
    		columns.likeAppendPercent("link_name", model.getLinkName());
    	}
    	Page<WmsSuppliersLink> modelPage = wmsSupplierLinkService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
    	renderJson(new DataTable<WmsSuppliersLink>(modelPage));
    }

    public void supplierForm(){
        String id=getPara("id");
        List<WmsSuppliersLink> linkList = new ArrayList<WmsSuppliersLink>();
        if(StrKit.notBlank(id)){
            WmsSuppliers supplier=wmsSupplierService.findById(id);
            setAttr("supplier",supplier);
            final String goodsTypeIds = Db.queryStr("select GROUP_CONCAT(stock_type_id) from wms_suppliers_type_rel where supplier_id=? group by supplier_id", id);
            setAttr("goodsTypeIds",goodsTypeIds);

            if(StrKit.notBlank(supplier.getCardId())){
				FinaMembershipCard card = finaMembershipCardService.findById(supplier.getCardId());
				setAttr("cardNumber",card.getCardNumber());
				setAttr("fullName",mmsMemberService.findById(card.getMemberId()).getFullName());
            }

            //处理区域
			if(StringUtils.isNotBlank(supplier.getProvince())) {
			    Area area = areaService.findById(supplier.getProvince());
			    if(area != null)setAttr("province",area.getAreaName());
			}
			if(StringUtils.isNotBlank(supplier.getCity())) {
			    Area area = areaService.findById(supplier.getCity());
			    if(area != null)setAttr("city",area.getAreaName());
			}
			if(StringUtils.isNotBlank(supplier.getTown())) {
			    Area area = areaService.findById(supplier.getTown());
			    if(area != null)setAttr("town",area.getAreaName());
			}
			if(StringUtils.isNotBlank(supplier.getStreet())) {
			    Area area = areaService.findById(supplier.getStreet());
			    if(area != null)setAttr("street",area.getAreaName());
			}
			linkList = wmsSupplierLinkService.findList(id);
			setAttr("linkList",linkList);
        }else{
        	final int supplierCount = Db.queryInt("select count(id)countData from wms_suppliers");
        	final String supplierNo = String.format("%"+5+"d", supplierCount+1).replace(" ", "0");
			WmsSuppliers supplier = new WmsSuppliers();
			supplier.setSupplierNo("CS-GYS-"+supplierNo);
			setAttr("supplier",supplier);
		}
        render("supplierForm.html");
    }
    
    public void payForm(){
    	WmsSuppliersPay model = getBean(WmsSuppliersPay.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model = wmsSupplierPayService.findById(model.getId());
    	}
        Map<String,String> payWay=new HashMap<>();
        for(PaymentWay paymentWay:PaymentWay.values()){
        	payWay.put(paymentWay.getKey(),paymentWay.getValue());
        }
        setAttr("model",model);
        setAttr("payWay", payWay);
    	render("payForm.html");
    }
    
    public void linkForm(){
    	WmsSuppliersLink model = getBean(WmsSuppliersLink.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model = wmsSupplierLinkService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("linkForm.html");
    }
    
    public void supplierLinkForm(){
    	WmsSuppliersLink model = getBean(WmsSuppliersLink.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model = wmsSupplierLinkService.findById(model.getId());
    	}
    	List<WmsSuppliers> suppliersList = wmsSupplierService.findList();
    	setAttr("model",model);
    	setAttr("suppliersList", suppliersList);
    	render("supplierLinkForm.html");
    }

    public void saveSupplier(){
    	final String goodsTypeIds = getPara("goodsTypeIds");
        WmsSuppliers wmsSupplier=getBean(WmsSuppliers.class,"",true);
        if(StrKit.notBlank(wmsSupplier.getId())){
            wmsSupplier.setUpdateBy(AuthUtils.getUserId());
        }else{
            wmsSupplier.setUpdateBy(AuthUtils.getUserId());
            wmsSupplier.setCreateBy(AuthUtils.getUserId());
        }
        List<String> goodsTypeIdsArray = new ArrayList<String>();
        if(StrKit.notBlank(goodsTypeIds)){
        	if(goodsTypeIds.contains(",")){
        		Collections.addAll(goodsTypeIdsArray, goodsTypeIds.split(","));
        	}else{
        		goodsTypeIdsArray.add(goodsTypeIds);
        	}
        }
        boolean flag=wmsSupplierService.saveSupplier(wmsSupplier,goodsTypeIdsArray);
        if(flag){
            renderJson(Ret.ok("msg","操作成功").set("id",wmsSupplier.getId()));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
    
    public void savePay(){
    	WmsSuppliersPay model = getBean(WmsSuppliersPay.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    	}else{
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setCreateBy(AuthUtils.getUserId());
    	}
    	boolean flag=wmsSupplierPayService.savePay(model);
    	if(flag){
    		renderJson(Ret.ok("msg","操作成功"));
    	}else{
    		renderJson(Ret.fail("msg","操作失败"));
    	}
    }
    
    public void saveLink(){
    	WmsSuppliersLink model = getBean(WmsSuppliersLink.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    	}else{
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setCreateBy(AuthUtils.getUserId());
    	}
    	boolean flag=wmsSupplierLinkService.saveLink(model);
    	if(flag){
    		renderJson(Ret.ok("msg","操作成功"));
    	}else{
    		renderJson(Ret.fail("msg","操作失败"));
    	}
    }

    public void selectCardTable(){
    	render("selectCardTable.html");
	}

	public void findListPage(){

		FinaMembershipCard card = getBean(FinaMembershipCard.class,"",true);
		String fullName = getPara("fullName");
		String idcard = getPara("idcard");
		String province = getPara("province");
		String city = getPara("city");
		String town = getPara("town");
		String street = getPara("street");
		String noDealStartDate = getPara("noDealStartDate");
		String noDealEndDate = getPara("noDealEndDate");
		String nearExpireStartYear = getPara("nearExpireStartYear");
		String nearExpireEndYear = getPara("nearExpireEndYear");
		String orderBy = getPara("orderBy");
		Page<Record> page = finaMembershipCardService.findList(getParaToInt("page"),getParaToInt("limit"),card,fullName,idcard,province,city,town,street,noDealStartDate,noDealEndDate,nearExpireStartYear,nearExpireEndYear,orderBy);
		if(page.getList()!=null && page.getList().size()>0){
			for(Record record : page.getList()){
				final String cardId = record.getStr("id");
				final String deductSchemeId = record.getStr("deductSchemeId");
				final String longDeductSchemeId = record.getStr("longDeductSchemeId");
				double price = 0.0;
				if(StrKit.notBlank(deductSchemeId)){
					final Double tempPrice = Db.queryDouble("select ROUND(SUM(IFNULL(pay_amount,0))/SUM(IFNULL(average_days,0)),2)price from fina_card_recharge " +
							"where type='2' and is_income='1' and del_flag='0' and cancel_flag='0' and is_review='1' and card_id=?", cardId);
					if(tempPrice!=null && tempPrice>0){
						price = tempPrice;
					}
				}else if(StrKit.notBlank(longDeductSchemeId)){
					price = Db.queryDouble("select IFNULL(price,0)price from main_card_deduct_scheme where id=?", longDeductSchemeId);
				}
				record.set("price",price);
			}
		}
		ValueFilter valueFilter=new ValueFilter() {
			@Override
			public Object process(Object object, String name, Object value) {
				if(value!=null && value instanceof Double){
					return Double.toString((Double)value);
				}
				return value;
			}
		};
		renderJson(JSONObject.toJSONString(new DataTable<Record>(page),valueFilter));
	}
}
