package com.cszn.main.data.web.controller.main;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainCardYearLimitConfigService;
import com.cszn.integrated.service.api.main.MainCardYearLimitService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainCardYearLimit;
import com.cszn.integrated.service.entity.main.MainCardYearLimitConfig;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/main/cardYearLimit",viewPath = "/modules_page/main/cardYearLimit/")
public class MainCardYearLimitController extends BaseController {

	@Inject
    private MainBaseService mainBaseService;
    @Inject
    private MainCardYearLimitService mainCardYearLimitService;
    @Inject
    private MainCardYearLimitConfigService mainCardYearLimitConfigService;
    @Inject
    MainSyncRecordService mainSyncRecordService;

    /**
     * 会员卡年限首页
     */
    public void index(){
        render("cardYearLimitIndex.html");
    }
    
    /**
     * 会员卡年限配置首页
     */
    public void configIndex(){
    	final String yearLimitId = getPara("id");
    	setAttr("yearLimitId",yearLimitId);
    	setAttr("configList",mainCardYearLimitConfigService.findCardYearLimitConfigList(yearLimitId));
    	render("cardYearLimitConfigIndex.html");
    }

    /**
     * 获取会员卡年限类型
     */
    @Clear(LogInterceptor.class)
    public void findPageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        MainCardYearLimit cardYearLimit=getBean(MainCardYearLimit.class,"",true);
        Page<MainCardYearLimit> cardYearLimitPage=mainCardYearLimitService.cardYearLimitPage(pageNumber,pageSize,cardYearLimit);
        renderJson(new DataTable<MainCardYearLimit>(cardYearLimitPage));
    }
    
    /**
     * 获取会员卡年限类型配置分页
     */
    @Clear(LogInterceptor.class)
    public void yearLimitConfigTable() {
    	MainCardYearLimitConfig model = getBean(MainCardYearLimitConfig.class, "", true);
        Page<MainCardYearLimitConfig> modelPage = mainCardYearLimitConfigService.cardYearLimitConfigPage(getParaToInt("page", 1), getParaToInt("limit", 10), model);
        renderJson(new DataTable<MainCardYearLimitConfig>(modelPage));
    }

    /**
     * 表单页面
     */
    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainCardYearLimit cardYearLimit=mainCardYearLimitService.findById(id);
            setAttr("cardYearLimit",cardYearLimit);
        }
        render("cardYearLimitForm.html");
    }
    
    /**
     * 卡年限类型配置表单页面
     */
    public void configForm(){
    	MainCardYearLimitConfig model = getBean(MainCardYearLimitConfig.class,"",true);
    	if(StrKit.notBlank(model.getId())) {
    		model = mainCardYearLimitConfigService.findById(model.getId());
    	}
    	setAttr("model",model);
    	setAttr("baseList",mainBaseService.findBaseList());
    	render("cardYearLimitConfigForm.html");
    }

    /**
     * 保存会员卡年限类型
     */
    public void saveCardYearLimit(){
        MainCardYearLimit cardYearLimit=getBean(MainCardYearLimit.class,"cardYearLimit",true);
        if(mainCardYearLimitService.cardYearLimitIsExist(cardYearLimit)){
            renderJson(Ret.fail("msg","名称或字典值已存在"));
            return;
        }
        boolean flag=mainCardYearLimitService.saveCardYearLimit(cardYearLimit, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 保存会员卡年限类型配置
     */
    public void saveCardYearLimitConfig(){
    	boolean countFlag = false;
    	final String oldBaseId = getPara("oldBaseId");
    	MainCardYearLimitConfig model = getBean(MainCardYearLimitConfig.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		//改变基地
    		if(!oldBaseId.equalsIgnoreCase(model.getBaseId())) {
    			int dataCount = Db.queryInt("select count(id)dataCount from main_card_year_limit_config where del_flag=? and year_limit_id=? and base_id=?", DelFlag.NORMAL, model.getYearLimitId(), model.getBaseId());
    			if(dataCount>0) {
    				countFlag = true;
    			}
    		}
    	}else{
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setCreateBy(AuthUtils.getUserId());
    		int dataCount = Db.queryInt("select count(id)dataCount from main_card_year_limit_config where del_flag=? and year_limit_id=? and base_id=?", DelFlag.NORMAL, model.getYearLimitId(), model.getBaseId());
    		if(dataCount>0) {
    			countFlag = true;
    		}
    	}
    	if(countFlag) {
    		renderJson(Ret.fail("msg","操作失败,该基地配置已存在"));
    	}else {
    		if(mainCardYearLimitConfigService.saveCardYearLimitConfig(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg","操作失败"));
    		}
    	}
    }

    /**
     * 作废会员卡年限类型
     */
    public void delCardYearLimit(){
        String id=getPara("id");
        boolean flag=mainCardYearLimitService.delCardYearLimit(id,AuthUtils.getUserId());
        if(flag) {
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.ok("msg","操作失败"));
        }
    }
    
    /**
     * 作废会员卡年限类型配置
     */
    public void delCardYearLimitConfig(){
    	MainCardYearLimitConfig model = getBean(MainCardYearLimitConfig.class,"",true);
		model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateDate(new Date());
		if(mainCardYearLimitConfigService.update(model)) {
			List<String> idList = Arrays.asList(new String[]{model.getId()});
			final String idStr = "["+idList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
			boolean flag = mainSyncRecordService.saveSyncRecord(SyncType.cardYearLimitConfig.getKey(), SyncDataType.DELETE, idStr,model.getUpdateBy());
			if(flag){
				renderJson(Ret.ok("msg","操作成功"));
			}else{
				renderJson(Ret.fail("msg","操作失败,同步数据失败"));
			}
		}else {
			renderJson(Ret.fail("msg","操作失败"));
		}
    }
}
