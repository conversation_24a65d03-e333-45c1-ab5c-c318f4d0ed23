package com.cszn.main.data.web.controller.main;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainSmsTypeService;
import com.cszn.integrated.service.api.wms.WmsUnitService;
import com.cszn.integrated.service.entity.main.MainSmsType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.wms.WmsUnit;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.Date;

@RequestMapping(value = "/main/smsType",viewPath = "/modules_page/main/smsType")
public class SmsTypeController extends BaseController {

    @Inject
    private MainSmsTypeService mainSmsTypeService;

    public void index(){
        render("smsTypeIndex.html");
    }

    public void smsTypePage(){
        MainSmsType model = getBean(MainSmsType.class,"",true);
		Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
		if(StrKit.notBlank(model.getTypeName())) {
			columns.likeAppendPercent("type_name", model.getTypeName());
		}
		Page<MainSmsType> modelPage = mainSmsTypeService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
		renderJson(new DataTable<MainSmsType>(modelPage));
    }

    public void add(){
        setAttr("model",getBean(MainSmsType.class,"",true));
        render("smsTypeForm.html");
    }

    public void edit(){
        MainSmsType model = getBean(MainSmsType.class,"",true);
        if(StrKit.notBlank(model.getId())){
            model = mainSmsTypeService.findById(model.getId());
        }
        setAttr("model",model);
        render("smsTypeForm.html");
    }

    public void save(){
        MainSmsType model = getBean(MainSmsType.class,"",true);
        if(StrKit.notBlank(model.getId())){
        	model.setUpdateBy(AuthUtils.getUserId());
            model.setUpdateDate(new Date());
        }else{
            model.setCreateBy(AuthUtils.getUserId());
            model.setUpdateDate(new Date());
        	model.setUpdateBy(AuthUtils.getUserId());
            model.setUpdateDate(new Date());
        }
        if(mainSmsTypeService.saveType(model)){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void del(){
        MainSmsType model = getBean(MainSmsType.class,"",true);
        if(StrKit.notBlank(model.getId())){
        	model.setUpdateBy(AuthUtils.getUserId());
            model.setUpdateDate(new Date());
        }else{
            renderJson(Ret.fail("msg","操作失败,id参数不能为空!"));
            return;
        }
        if(model.update()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
