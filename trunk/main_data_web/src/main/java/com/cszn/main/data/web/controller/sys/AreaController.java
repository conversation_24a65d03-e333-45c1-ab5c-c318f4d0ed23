package com.cszn.main.data.web.controller.sys;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.main.data.web.support.auth.AuthUtils;
import com.cszn.main.data.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;
import java.util.Map;

/**
 * Created by LiangHuiLing
 * 
 */
@RequestMapping(value="/area", viewPath="/modules_page/sys/area")
public class AreaController extends BaseController {
    
	@Inject
    private AreaService areaService;

	public void index(){
		List<Area> countryList = areaService.getArea("0");
		setAttr("countryList", countryList);
		render("areaIndex.html");
	}

	/**
	 * 区域分页
	 */
	@Clear(LogInterceptor.class)
	public void areaPage(){
		final Map<String, String[]> params=getParaMap();
		Page<Area> areaPage=areaService.page(params);
		renderJson(new DataTable<Area>(areaPage));
	}
	/**
	 * 获取市，县
	 */
	@Clear(LogInterceptor.class)
	public void getArea(){
		final String pid=getPara(0);
		List<Area> areaList = areaService.getArea(pid);
		renderJson(areaList);
	}

	/**
	 * 添加区域
	 */
	public void addArea(){
		List<Area> provinceList = areaService.getAreaByPid(null);
		setAttr("provinceList", provinceList);
		setAttr("operatingType", "add");
		render("areaForm.html");
	}

	/**
	 * 保存区域信息
	 */

	public void saveArea(){
		final Area area=getBean(Area.class,"",true);
		String provinceId=getPara("province");
		String cityId=getPara("city");
		String countyId=getPara("county");
		String operatingType=getPara("operatingType");
		if("add".equals(operatingType)){
//			Area areaFlag=areaService.findById(area.getId());
//			if(null==areaFlag){
				if(null!=area.getAreaLv() && area.getAreaLv().equals("1")){
					area.setParentId("0");
					area.setParentIds("0,");
					//添加省
				}else if(null!=area.getAreaLv() && area.getAreaLv()==2){
					area.setParentId("61a018df9dfe11e9b29800155d01c403");
					area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,");
					//添加市
				}else if(null!=area.getAreaLv() && area.getAreaLv()==3 && StrKit.notBlank(provinceId)){
					area.setParentId(provinceId);
					area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,"+provinceId+",");
					//添加区/县
				}else if(null!=area.getAreaLv() && area.getAreaLv()==4 && StrKit.notBlank(provinceId) && StrKit.notBlank(cityId)){
					area.setParentId(cityId);
					area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,"+provinceId+","+cityId+",");
					//添加街道/镇/乡
				}else if(null!=area.getAreaLv() && area.getAreaLv()==5 && StrKit.notBlank(provinceId) && StrKit.notBlank(countyId)){
					area.setParentId(countyId);
					area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,"+provinceId+","+countyId+",");
				}
				if(areaService.addArea(area,AuthUtils.getUserId())){
					renderJson(Ret.ok("msg","操作成功"));
					return;
				}else{
					renderJson(Ret.fail("msg","操作失败"));
					return;
				}
//			}else{
//				renderJson(Ret.fail("msg","该ID已存在"));
//			}
		}else{

			if(null!=area.getAreaLv() && area.getAreaLv().equals("1")){
				area.setParentId("0");
				area.setParentIds("0,,");
				//添加省
			}else if(null!=area.getAreaLv() && area.getAreaLv().equals("2")){
				area.setParentId("61a018df9dfe11e9b29800155d01c403");
				area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,");
				//添加市
			}else if(null!=area.getAreaLv() && area.getAreaLv().equals("3") && null != provinceId && ""!=provinceId){
				area.setParentId(provinceId);
				area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,"+provinceId+",");
				//添加区/县
			}else if(null!=area.getAreaLv() && area.getAreaLv().equals("4") && null != provinceId && ""!=provinceId && null != cityId && ""!=cityId){
				area.setParentId(cityId);
				area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,"+provinceId+","+cityId+",");
				//添加街道/镇/乡
			}else if(null!=area.getAreaLv() && area.getAreaLv().equals("5") && null != provinceId && ""!=provinceId && null != countyId && ""!=countyId){
				area.setParentId(countyId);
				area.setParentIds("0,61a018df9dfe11e9b29800155d01c403,"+provinceId+","+countyId+",");
			}
			if(areaService.editArea(area,AuthUtils.getUserId())){
				renderJson(Ret.ok("msg","操作成功"));
			}else{
				renderJson(Ret.fail("msg","操作失败"));
			}
		}
	}
	/**
	 * 编辑区域
	 */
	public void editArea(){
		String id=getPara("id");
		Area area=areaService.findById(id);
		List<Area> provinceList = areaService.getAreaByPid(null);
		String provinceId=null;
		String cityId=null;
		String countyId=null;
		if(null!=area.getParentIds() && ""!=area.getParentIds()){
			String parentStr=area.getParentIds();
			String[] parents= parentStr.split(",");
			if(3==area.getAreaLv() && parents.length==3 ){
				provinceId=parents[2];
			}

			if(3==area.getAreaLv() && parents.length==4 ){
				provinceId=parents[2];
				cityId=parents[3];
			}

			if(5==area.getAreaLv() && parents.length==4 ){
				provinceId=parents[2];
				countyId=parents[3];
				if(null!=countyId && ""!=countyId){
					Area sysArea=areaService.findById(countyId);
					if(null!=sysArea){
						cityId=sysArea.getParentId();
					}
				}
			}
		}
		setAttr("provinceList", provinceList);
		setAttr("area", area);
		setAttr("provinceId", provinceId);
		setAttr("cityId", cityId);
		setAttr("countyId", countyId);
		setAttr("operatingType", "edit");
		render("areaForm.html");
	}
	/**
	 * 删除区域
	 */
	public void del(){
		final String id=getPara("id");
		List<Area> areaList=areaService.getArea(id);
		if(areaList!=null && areaList.size()>0){
			renderJson(Ret.fail("msg","该区域下存在子区域，请先删除子区域再删除"));
			return;
		}
		if(areaService.delArea(id,AuthUtils.getUserId())){
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg","操作失败"));
		}
	}

	/**
	 * 批量删除区域
	 */
	public void batchDel(){
		String data=getPara("data");
		System.out.println("data："+data);
		if(StrKit.isBlank(data) || !data.startsWith("[{") || !data.endsWith("}]")){
			renderJson(Ret.fail("msg","数据异常"));
			return;
		}

		boolean flag=areaService.batchDel(data, AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg","操作失败"));
		}
	}

	/**
	 * 获取地区：插件使用
	 */
	@Clear(LogInterceptor.class)
	public void getAreas(){
		String pid=getPara("pid");
		List<Area> sysAreaList = areaService.getAreaByPid(pid);
		System.out.println(Ret.ok().set("data",sysAreaList).toJson());
		renderJson(Ret.ok().set("data",sysAreaList));
	}
}
