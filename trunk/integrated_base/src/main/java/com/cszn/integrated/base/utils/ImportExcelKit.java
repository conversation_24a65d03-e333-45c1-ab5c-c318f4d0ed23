package com.cszn.integrated.base.utils;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.kit.StrKit;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description excel导入工具类
 * <AUTHOR>
 * @Date 2019/4/15
 **/
public class ImportExcelKit {


    /**
     * 获取excel数据
     * @param inputStream
     * @param fileName
     * @return
     */
    public static List<Map> getData(FileInputStream inputStream,String fileName){
        final String xlsx = "xlsx";
        final String xls = "xls";
        String[] fields = {"cardNumber","openTime","cardTypeId","consumeTimes","balance","fullName","idcard","gender","birthday","operator","telephone","describe","deductSchemeId","yearLimit","useYears","giveSchemeId"};
        if (fileName.contains(xlsx)) {
            return ImportExcelKit.readExcelPlus(inputStream,fields);
        }else if (fileName.contains(xls)) {
            return ImportExcelKit.readExcel(inputStream,fields);
        }
        return null;
    }


    /**
     * 获取excel数据
     * @param inputStream
     * @param fileName
     * @param fields
     * @return
     */
    public static List<Map> getExcelData(FileInputStream inputStream,String fileName,String[] fields){
        final String xlsx = "xlsx";
        final String xls = "xls";
        List<Map> excelMap = null;
        if(fields!=null && fields.length>0){
        	if (fileName.contains(xlsx)) {
        		excelMap = ImportExcelKit.read2007PlusExcel(inputStream,fields);
        	}else if (fileName.contains(xls)) {
        		excelMap = ImportExcelKit.read2007Excel(inputStream,fields);
        	}
        }
        return excelMap;
    }
    /**
     * 获取excel数据
     * @param inputStream
     * @param fileName
     * @param fields
     * @return
     */
    public static List<Map> getExcelData(InputStream inputStream, String fileName, String[] fields){
        final String xlsx = "xlsx";
        final String xls = "xls";
        List<Map> excelMap = null;
        if(fields!=null && fields.length>0){
            if (fileName.contains(xlsx)) {
                excelMap = ImportExcelKit.read2007PlusExcel(inputStream,fields);
            }else if (fileName.contains(xls)) {
                excelMap = ImportExcelKit.read2007Excel(inputStream,fields);
            }
        }
        return excelMap;
    }

    /**
     * excel读取 2007-
     */
    public static List<Map> readExcel(FileInputStream is, String[] fields){

             List<Map> list = new ArrayList<>();

        try {
            HSSFWorkbook wk = new HSSFWorkbook(is);
            HSSFSheet sheet = wk.getSheetAt(0);

            int lastRowNum = sheet.getLastRowNum();
            for (int i=1; i<=lastRowNum; i++){
                HSSFRow row = sheet.getRow(i);
                Map map = new HashMap();
                short cellNum = row.getLastCellNum();

                for (int j=0; j<cellNum && j<fields.length; j++){
                    HSSFCell cell = row.getCell(j);
                    //去空
                    Object cellObj = getCellValue(cell);
                    if(cell == null || "".equals(cell) || cellObj == null || "".equals(cellObj)) continue;
                    if(cellObj instanceof Boolean) continue;

                    map.put(fields[j], cellObj);
                }
                list.add(map);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }


    /**
     * excel读取 2007+
     */
    private static List<Map> readExcelPlus(FileInputStream is, String[] fields){
            List<Map> list = new ArrayList<>();

        try {
            XSSFWorkbook wk = new XSSFWorkbook(is);
            XSSFSheet sheet = wk.getSheetAt(0);

            int lastRowNum = sheet.getLastRowNum();
            for (int i=1; i<=lastRowNum; i++){
                XSSFRow row = sheet.getRow(i);
                Map map = new HashMap();
                short cellNum = row.getLastCellNum();

                for (int j=0; j<cellNum && j<fields.length; j++){
                    XSSFCell cell = row.getCell(j);
                    //去空
                    Object cellObj = getCellValue(cell);
                    if(cell == null || "".equals(cell) || cellObj == null || "".equals(cellObj)) continue;
                    if(cellObj instanceof Boolean) continue;

                    map.put(fields[j], cellObj);
                }
                list.add(map);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return list;
    }
    
    /**
     * excel读取 2007-
     */
    public static List<Map> read2007Excel(FileInputStream is, String[] fields){
    	
    	List<Map> list = new ArrayList<>();
    	
    	try {
    		HSSFWorkbook wk = new HSSFWorkbook(is);
    		HSSFSheet sheet = wk.getSheetAt(0);
    		
    		int lastRowNum = sheet.getLastRowNum();
    		for (int i=1; i<=lastRowNum; i++){
    			HSSFRow row = sheet.getRow(i);
    			Map map = new HashMap();
    			short cellNum = row.getLastCellNum();
    			for (int j=0; j<cellNum && j<fields.length; j++){
    				HSSFCell cell = row.getCell(j);
    				//去空
    				Object cellObj = getCellObject(cell);
    				if(cell == null || "".equals(cell) || cellObj == null || "".equals(cellObj)) continue;
    				if(cellObj instanceof Boolean) continue;
    				map.put(fields[j], cellObj);
    			}
    			list.add(map);
    		}
    	} catch (IOException e) {
    		e.printStackTrace();
    	}
    	return list;
    }

    public static List<Map> read2007Excel(InputStream is, String[] fields){

        List<Map> list = new ArrayList<>();

        try {
            HSSFWorkbook wk = new HSSFWorkbook(is);
            HSSFSheet sheet = wk.getSheetAt(0);

            int lastRowNum = sheet.getLastRowNum();
            for (int i=1; i<=lastRowNum; i++){
                HSSFRow row = sheet.getRow(i);
                Map map = new HashMap();
                short cellNum = row.getLastCellNum();
                for (int j=0; j<cellNum && j<fields.length; j++){
                    HSSFCell cell = row.getCell(j);
                    //去空
                    Object cellObj = getCellObject(cell);
                    if(cell == null || "".equals(cell) || cellObj == null || "".equals(cellObj)) continue;
                    if(cellObj instanceof Boolean) continue;
                    map.put(fields[j], cellObj);
                }
                list.add(map);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return list;
    }
    
    
    /**
     * excel读取 2007+
     */
    private static List<Map> read2007PlusExcel(FileInputStream is, String[] fields){
    	List<Map> list = new ArrayList<>();
    	
    	try {
    		XSSFWorkbook wk = new XSSFWorkbook(is);
    		XSSFSheet sheet = wk.getSheetAt(0);
    		
    		int lastRowNum = sheet.getLastRowNum();
    		for (int i=1; i<=lastRowNum; i++){
    			XSSFRow row = sheet.getRow(i);
    			Map map = new HashMap();
    			short cellNum = row.getLastCellNum();
    			
    			for (int j=0; j<cellNum && j<fields.length; j++){
    				XSSFCell cell = row.getCell(j);
    				//去空
    				Object cellObj = getCellObject(cell);
    				if(cell == null || "".equals(cell) || cellObj == null || "".equals(cellObj)) continue;
    				if(cellObj instanceof Boolean) continue;
    				
    				map.put(fields[j], cellObj);
    			}
    			list.add(map);
    		}
    	} catch (IOException e) {
    		e.printStackTrace();
    	}
    	
    	return list;
    }

    private static List<Map> read2007PlusExcel(InputStream is, String[] fields){
        List<Map> list = new ArrayList<>();

        try {
            XSSFWorkbook wk = new XSSFWorkbook(is);
            XSSFSheet sheet = wk.getSheetAt(0);

            int lastRowNum = sheet.getLastRowNum();
            for (int i=1; i<=lastRowNum; i++){
                XSSFRow row = sheet.getRow(i);
                Map map = new HashMap();
                short cellNum = row.getLastCellNum();

                for (int j=0; j<cellNum && j<fields.length; j++){
                    XSSFCell cell = row.getCell(j);
                    //去空
                    Object cellObj = getCellObject(cell);
                    if(cell == null || "".equals(cell) || cellObj == null || "".equals(cellObj)) continue;
                    if(cellObj instanceof Boolean) continue;

                    map.put(fields[j], cellObj);
                }
                list.add(map);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return list;
    }

    /**
     * 处理单元格类型
     * @param cell
     * @return
     */
    public static Object getCellValue(Cell cell){
        Object val = "";
        try{
            if (cell != null){
                if(cell.getColumnIndex() !=1 && cell.getColumnIndex() !=8) {
                    if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                        DecimalFormat df = new DecimalFormat("#.##");
                        val = df.format(cell.getNumericCellValue());
                    } else if (cell.getCellTypeEnum() == CellType._NONE) {
                        val = cell.getStringCellValue();
                    } else if (cell.getCellTypeEnum() == CellType.BLANK) {
                        val = cell.getBooleanCellValue();
                    } else if (cell.getCellTypeEnum() == CellType.BOOLEAN) {
                        val = cell.getBooleanCellValue();
                    } else if (cell.getCellTypeEnum() == CellType.ERROR) {
                        val = cell.getErrorCellValue();
                    } else if (cell.getCellTypeEnum() == CellType.FORMULA) {
                        val = cell.getCellFormula();
                    } else if (cell.getCellTypeEnum() == CellType.STRING) {
                        val = cell.getStringCellValue();
                    } else if (HSSFDateUtil.isCellDateFormatted(cell)) {
                        val = new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                }else{
                    if(cell.getCellTypeEnum() == CellType.STRING){
                        val = new SimpleDateFormat("yyyy-MM-dd").format(new SimpleDateFormat("yyyy/MM/dd").parse(cell.getStringCellValue().toString()));
                    }else{
                        val = new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                }
            }
        }catch (Exception e) {
            return val;
        }
        return val;
    }
    
    /**
     * 处理单元格类型
     * @param cell
     * @return
     */
    public static Object getCellObject(Cell cell){
    	Object val = "";
    	try{
    		if (cell != null){
				if (cell.getCellTypeEnum() == CellType.NUMERIC) {
					DecimalFormat df = new DecimalFormat("#.##");
					val = df.format(cell.getNumericCellValue());
				} else if (cell.getCellTypeEnum() == CellType._NONE) {
					val = cell.getStringCellValue();
				} else if (cell.getCellTypeEnum() == CellType.BLANK) {
					val = cell.getBooleanCellValue();
				} else if (cell.getCellTypeEnum() == CellType.BOOLEAN) {
					val = cell.getBooleanCellValue();
				} else if (cell.getCellTypeEnum() == CellType.ERROR) {
					val = cell.getErrorCellValue();
				} else if (cell.getCellTypeEnum() == CellType.FORMULA) {
					val = cell.getCellFormula();
				} else if (cell.getCellTypeEnum() == CellType.STRING) {
					val = cell.getStringCellValue();
				} else if (HSSFDateUtil.isCellDateFormatted(cell)) {
					val = new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
				}
    		}
    	}catch (Exception e) {
    		return val;
    	}
    	return val;
    }



    /**
     * map转对象
     * @param map
     * @param beanClass
     * @return
     * @throws Exception
     */
    public static Object mapToObject(Map<String, Object> map, Class<?> beanClass) {
          if (map == null) {
              return null;
          }
        Object obj = null;
        try {
            obj = beanClass.newInstance();
            org.apache.commons.beanutils.BeanUtils.populate(obj, map);
        } catch (InstantiationException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }
        return obj;
    }


    public static String getImageJson(String str){
        if(StringUtils.isEmpty(str)){
            return new JSONArray().toString();
        }

        JSONArray jsonArray = new JSONArray();
        String[] strs = str.split(";");
        for (String item : strs) {
            String[] arr = item.split(",");
            if(arr.length>=1) {
                JSONObject json = new JSONObject();
                json.put("id", arr[0]);
                json.put("image", arr[1]);
                jsonArray.add(json);
            }
        }
        return jsonArray.toJSONString();
    }

    /**
     * 导出Excel
     * @param sheetName sheet名称
     * @param title 标题
     * @param values 内容
     * @param wb HSSFWorkbook对象
     * @return
     */
    public static HSSFWorkbook getHSSFWorkbook(String sheetName, String []title, String [][]values, HSSFWorkbook wb){

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if(wb == null){
            wb = new HSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，并设置值表头 设置表头居中
       HSSFCellStyle style = wb.createCellStyle();
        style.setWrapText(true);
       //style.setAlignment(org.apache.poi.hssf.usermodel.HSSFCellStyle.ALIGN_CENTE); // 创建一个居中格式

        //声明列对象
        HSSFCell cell = null;

        //创建标题
        for(int i=0;i<title.length;i++){
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
        }

        //创建内容
        for(int i=0;i<values.length;i++){
            row = sheet.createRow(i + 1);
            for(int j=0;j<values[i].length;j++){
                //将内容按顺序赋给对应的列对象
                HSSFCell rowCell=row.createCell(j);
                rowCell.setCellValue(values[i][j]);
                if(StrKit.notBlank(values[i][j]) && values[i][j].indexOf("\n")!=-1){
                    rowCell.setCellStyle(style);
                }
            }
        }
        return wb;
    }
}
