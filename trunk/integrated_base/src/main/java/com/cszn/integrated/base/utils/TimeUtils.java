package com.cszn.integrated.base.utils;

import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Record;
import com.xiaoleilu.hutool.date.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description 时间工具类
 * <AUTHOR>
 * @Date 2019/6/26
 **/
public class TimeUtils {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);


    /**
     * 判断字符串是否可以转换为时间格式
     * @return
     */
    public static Date isParseDate(String dateString,String pattern){
        Date date = null;
        try {
            date = new SimpleDateFormat(pattern).parse(dateString);
        } catch (ParseException e) {
            return null;
        }
        return date;
    }


    /**
     * 根据日期和日期格式化处理日期格式
     * @param date
     * @param pattern
     * @return
     */
    public static String getStrDateByPattern(Date date,String pattern){
        String strDate = "";
        try {
            strDate = new SimpleDateFormat(pattern).format(date);
        } catch (Exception e) {
            return strDate;
        }
        return strDate;
    }


    /**
     * 通过身份证获取年龄
     * @return
     */
    public static Integer getAgeByIdcard(String idcard){
        Integer age = 0;
        try {
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            Integer newYear = c.get(Calendar.YEAR);

            if(idcard!=null && idcard.length()==18){
                age = newYear - Integer.parseInt(idcard.substring(6,10));
            }else if(idcard!=null && idcard.length()==15){
                age = newYear - Integer.parseInt("19"+idcard.substring(6,8));
            }else{
                return age;
            }
        } catch (NumberFormatException e) {
            logger.info("通过身份证获取年龄异常：[{}]",e.getMessage());
            return age;
        }
        return age;
    }


    /**
     * 转换bio手环时间
     * @param date
     * @param time
     * @return
     */
    public static Date getBioDateToDate(String date,String time){
        Date datePattern = null;

        //截取当前时间前两位
        String strDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

        date = strDate.substring(0,2) + date.substring(4,6) + "-" + date.substring(2,4) + "-" +date.substring(0,2);
        String dateTime = date + " " + time.substring(0,2) + ":" +time.substring(2,4) + ":" + time.substring(4,6);

        try {
            datePattern = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateTime);
        } catch (ParseException e) {
            e.printStackTrace();
            return datePattern;
        }
        return datePattern;
    }


    /**
     * 处理传递给bio手环的时间段格式
     * @return
     */
    public static String dealTimeRange(String times){
        times = times.replace(" ","");
        String[] timsArr = times.split("-");
        //处理时间前的0
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < timsArr.length; i++) {
            if(timsArr[i].startsWith("0")){
                timsArr[i] = timsArr[i].substring(1);
            }
            if(i == 0){
                sb.append(timsArr[i]);
            }else{
                sb.append("-");
                sb.append(timsArr[i]);
            }
        }
        return sb.toString();
    }


    /**
     * 设置时间减一天
     * @param date
     * @return
     */
    public static Date dayReduce(Date date){
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.add(Calendar.DATE, -1);
        return ca.getTime();
    }


    /**
     * 设置时间加一天
     * @param date
     * @return
     */
    public static Date dayAdd(Date date){
        Calendar ca = Calendar.getInstance();
        ca.setTime(date);
        ca.add(Calendar.DATE, 1);
        return ca.getTime();
    }



    /**
     * 求开始时间与结束时间之间的天数差值
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer caculateTotalTime(String startTime,String endTime){
        Integer totalTime = null;
        try {
            SimpleDateFormat formatter =   new SimpleDateFormat( "yyyy-MM-dd" );
            Date date1=null;
            Date date = formatter.parse(startTime);
            long ts = date.getTime();
            date1 =  formatter.parse(endTime);
            long ts1 = date1.getTime();
            long ts2=ts1-ts;
            totalTime=(int) (ts2/(24*3600*1000)+1);
            return totalTime;
        } catch (ParseException e) {
            e.printStackTrace();
            return totalTime;
        }
    }


    /**
     * 求开始时间和结束时间的时差
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer caculateTotalTimeByHour(Date startTime,Date endTime){
        return (int) ((endTime.getTime() - startTime.getTime())/(1000 * 60 * 60));
    }


    /**
     * 根据日期和时间点构造时间
     * @param date
     * @return
     */
    public static Date getMyselfDate(Date date,String time){
        String strDate = new SimpleDateFormat( "yyyy-MM-dd").format(date) + " " + time;
        Date newDate = null;
        try {
            newDate = new SimpleDateFormat( "yyyy-MM-dd HH:mm:ss").parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
        return newDate;
    }


    /**
     * 给list排序
     */
    public static void listAsc(List<Record> recordList){
        if(recordList != null && recordList.size() > 1) {
            List<Record> parentList = new ArrayList<>();
            List<Record> sonList = new ArrayList<>();
            for (Record item : recordList) {
                if ("0".equals(item.getStr("parentId"))) {
                    parentList.add(item);
                } else {
                    sonList.add(item);
                }
            }
            recordList.clear();
            recordList.addAll(parentList);
            recordList.addAll(sonList);
        }
    }


    /**
     * 统一校验/预订：根据开始时间，结束时间计算明细数量
     * @param startTime
     * @param endTime
     * @return
     */
    public static Integer getCountByDiffTimes(Date startTime,Date endTime){
        int x=0;
        Date timeStart = null;
        Date timeEnd = null;
        Date newStartTime = null;
        Date newEndTime = null;
        do{
            if(x == 0) {
                timeStart = startTime;
                timeEnd = TimeUtils.getMyselfDate(TimeUtils.dayAdd(startTime), "12:00:00");
                if(TimeUtils.compareDateDtr(timeEnd,endTime)){
                    timeEnd = endTime;
                }
                newStartTime = timeStart;//用于新一天时间的计算
                newEndTime = timeEnd;//用于新一天时间的计算
            }else{
                Date startDate = newEndTime;
                Date endDate = TimeUtils.getMyselfDate(TimeUtils.dayAdd(newEndTime),"12:00:00");
                if(TimeUtils.compareDateDtr(endDate,endTime)){
                    endDate = endTime;
                }
                newStartTime = startDate;
                newEndTime = endDate;
            }
            x++;
        }while(endTime.compareTo(newEndTime) > 0);
        return x;
    }



    /**
     * 续住/随行:根据入住开始时间,结束时间计算明细时间
     * @return
     */
    public static List<Record> getDetailDateList(Date startTime,Date endTime){
        List<Record> list = new ArrayList<>();
        int x=0;
        Date timeStart = null;
        Date timeEnd = null;
        Date newStartTime = null;
        Date newEndTime = null;
        do{
            if(x == 0) {
                timeStart = startTime;
                timeEnd = TimeUtils.getMyselfDate(TimeUtils.dayAdd(startTime), "12:00:00");
                if(TimeUtils.compareDateDtr(timeEnd,endTime)){
                    timeEnd = endTime;
                }
                newStartTime = timeStart;//用于新一天时间的计算
                newEndTime = timeEnd;//用于新一天时间的计算
            }else{
                Date startDate = newEndTime;
                Date endDate = TimeUtils.getMyselfDate(TimeUtils.dayAdd(newEndTime),"12:00:00");
                if(TimeUtils.compareDateDtr(endDate,endTime)){
                    endDate = endTime;
                }
                newStartTime = startDate;
                newEndTime = endDate;
            }

            Record record = new Record();
            record.set("startTime",newStartTime);
            record.set("endTime",newEndTime);
            list.add(record);
            x++;
        }while(endTime.compareTo(newEndTime) > 0);
        return list;
    }



    /**
     *入住:根据初始化时间,预定开始时间,入住开始时间,结束时间计算明细时间
     * @param bookNo 预订号
     * @param initialDate 初始化时间
     * @param bookStartTime 预定开始时间
     * @param checkinTime 入住开始时间
     * @param checkoutTime 入住结束时间
     * @return
     */
    public static List<Record> getDetailDateListByCheckin(String bookNo,String initialDate,Date bookStartTime,Date checkinTime,Date checkoutTime){
        List<Record> list = new ArrayList<>();
        int i=0;
        Date startTime = null;
        Date endTime = null;
        Date newStartTime = null;
        Date newEndTime = null;
        do{
            if(i == 0) {
                if(StringUtils.isNotBlank(initialDate)){//初始化时间不为空则使用
                    Date initDate = DateUtil.parse(initialDate);
                    startTime = initDate;
                    endTime = TimeUtils.getMyselfDate(TimeUtils.dayAdd(initDate), "12:00:00");
                }else{//某种情况：若有预订则先判断预订时间和入住时间哪个在前，在前的时间为计费时间 => 不管预订时间，只按入住和退住时间
                    if(StringUtils.isBlank(bookNo)) {
                        startTime = checkinTime;
                        endTime = TimeUtils.getMyselfDate(TimeUtils.dayAdd(checkinTime), "12:00:00");
                    }else{
                        Date diffTime = null;
                        if(checkinTime.compareTo(bookStartTime) >= 0){
                            diffTime = bookStartTime;
                        }else{
                            diffTime = checkinTime;
                        }
                        startTime = diffTime;
                        endTime = TimeUtils.getMyselfDate(TimeUtils.dayAdd(diffTime), "12:00:00");
                    }
                }

                if(TimeUtils.compareDateDtr(endTime,checkoutTime)){
                    endTime = checkoutTime;
                }
                newStartTime = startTime;//用于新一天时间的计算
                newEndTime = endTime;//用于新一天时间的计算
            }else{
                Date startDate = newEndTime;
                Date endDate = TimeUtils.getMyselfDate(TimeUtils.dayAdd(newEndTime),"12:00:00");
                if(TimeUtils.compareDateDtr(endDate,checkoutTime)){
                    endDate = checkoutTime;
                }
                newStartTime = startDate;
                newEndTime = endDate;
            }
            Record record = new Record();
            record.set("startTime",newStartTime);
            record.set("endTime",newEndTime);
            list.add(record);
            i++;
        }while(checkoutTime.compareTo(newEndTime) > 0);
        return list;
    }






    /**
     * 比较两个时间是否相等
     * @param beginDate
     * @param endDate
     * @return
     */
    public static boolean compareDateDtr(Date beginDate,Date endDate){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String beginDateStr = sdf.format(beginDate);
        String endDateStr = sdf.format(endDate);
        if(beginDateStr.equals(endDateStr)){
            return true;
        }else{
            return false;
        }
    }




    /**
     * 时间戳转固定格式时间字符串 yyyy/MM/dd HH:mm:ss
     * @param timeStampStr
     * @return
     */
    public static String toDateTimes(String timeStampStr){
        String dateStr = "";
        try {
            long timeStamp = Long.parseLong(timeStampStr);
            dateStr = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date(timeStamp));
        } catch (NumberFormatException e) {
            return dateStr;
        }
        return dateStr;
    }


    /**
     * 更换会员卡
      * @param startTimeStr
     * @param endTimeStr
     * @return
     */
    public static boolean changeTimeCompare(String startTimeStr,String endTimeStr){
        Date startTime = null;
        Date endTime = null;

        try {
            startTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startTimeStr);
            endTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endTimeStr);
        } catch (ParseException e) {
            return false;
        }

        if(startTime.compareTo(endTime) >= 0){
            return true;
        }else{
            return false;
        }
    }



    public static void main(String[] args) {
        //System.out.println(getBioDateToDate("120414","101930"));
        /*String dateString = "2017-08-01 14:02:08";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateParse = null;
        try {
            dateParse = sdf.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        System.out.println(dayReduce(dateParse));*/
    }
}
