/**
 * 
 */
package com.cszn.integrated.base.common;

import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.jfinal.kit.StrKit;

@SuppressWarnings({"serial", "rawtypes", "unchecked"})
public class RetApi extends HashMap {

	private static final String CODE = "code";
	private static final String CODE_OK = "0";
	private static final String CODE_FAIL = "10001";
	
	public RetApi() {
	}
	
	public static RetApi by(Object key, Object value) {
		return new RetApi().set(key, value);
	}
	
	public static RetApi create(Object key, Object value) {
		return new RetApi().set(key, value);
	}
	
	public static RetApi create() {
		return new RetApi();
	}
	
	public static RetApi ok() {
		return new RetApi().setOk();
	}
	
	public static RetApi ok(Object key, Object value) {
		return ok().set(key, value);
	}
	
	public static RetApi fail() {
		return new RetApi().setFail();
	}
	
	public static RetApi fail(Object key, Object value) {
		return fail().set(key, value);
	}
	
	public RetApi setOk() {
		super.put(CODE, CODE_OK);
		return this;
	}
	
	public RetApi setFail() {
		super.put(CODE, CODE_FAIL);
		return this;
	}
	
	public boolean isOk() {
		Object state = get(CODE);
		if (CODE_OK.equals(state)) {
			return true;
		}
		if (CODE_FAIL.equals(state)) {
			return false;
		}
		
		throw new IllegalStateException("调用 isOk() 之前，必须先调用 ok()、fail() 或者 setOk()、setFail() 方法");
	}
	
	public boolean isFail() {
		Object state = get(CODE);
		if (CODE_OK.equals(state)) {
			return true;
		}
		if (CODE_OK.equals(state)) {
			return false;
		}
		
		throw new IllegalStateException("调用 isFail() 之前，必须先调用 ok()、fail() 或者 setOk()、setFail() 方法");
	}
	
	public RetApi set(Object key, Object value) {
		super.put(key, value);
		return this;
	}
	
	public RetApi setIfNotBlank(Object key, String value) {
		if (StrKit.notBlank(value)) {
			set(key, value);
		}
		return this;
	}
	
	public RetApi setIfNotNull(Object key, Object value) {
		if (value != null) {
			set(key, value);
		}
		return this;
	}
	
	public RetApi set(Map map) {
		super.putAll(map);
		return this;
	}
	
	public RetApi set(RetApi ret) {
		super.putAll(ret);
		return this;
	}
	
	public RetApi delete(Object key) {
		super.remove(key);
		return this;
	}
	
	public <T> T getAs(Object key) {
		return (T)get(key);
	}
	
	public String getStr(Object key) {
		Object s = get(key);
		return s != null ? s.toString() : null;
	}
	
	public Integer getInt(Object key) {
		Number n = (Number)get(key);
		return n != null ? n.intValue() : null;
	}
	
	public Long getLong(Object key) {
		Number n = (Number)get(key);
		return n != null ? n.longValue() : null;
	}
	
	public Number getNumber(Object key) {
		return (Number)get(key);
	}
	
	public Boolean getBoolean(Object key) {
		return (Boolean)get(key);
	}
	
	/**
	 * key 存在，并且 value 不为 null
	 */
	public boolean notNull(Object key) {
		return get(key) != null;
	}
	
	/**
	 * key 不存在，或者 key 存在但 value 为null
	 */
	public boolean isNull(Object key) {
		return get(key) == null;
	}
	
	/**
	 * key 存在，并且 value 为 true，则返回 true
	 */
	public boolean isTrue(Object key) {
		Object value = get(key);
		return (value instanceof Boolean && ((Boolean)value == true));
	}
	
	/**
	 * key 存在，并且 value 为 false，则返回 true
	 */
	public boolean isFalse(Object key) {
		Object value = get(key);
		return (value instanceof Boolean && ((Boolean)value == false));
	}
	
	public String toJson() {
		return JSON.toJSONString(this);
	}
	
	public boolean equals(Object ret) {
		return ret instanceof RetApi && super.equals(ret);
	}
}
