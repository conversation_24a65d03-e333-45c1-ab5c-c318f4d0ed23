package com.cszn.integrated.base.utils;

import com.cszn.integrated.base.common.ZTree;

import java.util.List;
import java.util.Set;

public class ZtreeUtils {

    /**
     * 通过某id获取节点
     * @param id
     * @param zTreeList
     * @return
     */
    public static ZTree getZtreeById(String id,List<ZTree> zTreeList){
        for (ZTree zTree : zTreeList) {
            if(zTree.getId().equalsIgnoreCase(id)){
                return zTree;
            }
            if(zTree.getChildren()!=null && zTree.getChildren().size()>0){
                ZTree tree=getZtreeById(id,zTree.getChildren());
                if(tree!=null){
                    return tree;
                }
            }
        }
        return null;
    }

    /**
     * 获取所有包括子节点的id
     * @param childrenIdList
     * @param zTreeList
     */
    public static void getAllChildrenId(Set<String> childrenIdList, List<ZTree> zTreeList){
        for (ZTree zTree : zTreeList) {
            childrenIdList.add(zTree.getId());
            if(zTree.getChildren()!=null && zTree.getChildren().size()>0){
                getAllChildrenId(childrenIdList,zTree.getChildren());
            }
        }
    }

}
