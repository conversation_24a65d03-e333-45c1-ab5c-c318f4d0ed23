package com.cszn.integrated.base.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

/**
 * 序列化、反序列化对象
 */
public class ObjectSerializeUtils {

    private static Logger LOG= LoggerFactory.getLogger(ObjectSerializeUtils.class);

    /**
     * 序列化对象
     * @param obj
     * @param <T>
     * @return
     */
    public static <T> byte[] serialize(T obj){
        ObjectOutputStream objectOutputStream;
        ByteArrayOutputStream byteArrayOutputStream;
        try {
            byteArrayOutputStream=new ByteArrayOutputStream();
            objectOutputStream=new ObjectOutputStream(byteArrayOutputStream);
            objectOutputStream.writeObject(obj);
            byte[] byteArray=byteArrayOutputStream.toByteArray();
            return byteArray;
        }catch (Exception e){
            LOG.error("序列化对象失败"+e);
        }
        return null;
    }

    /**
     * 反序列化对象
     * @param byteArray
     * @param <T>
     * @return
     */
    public static <T> Object unserizlize(byte[] byteArray){
        ObjectInputStream inputStream;
        ByteArrayInputStream byteArrayInputStream;
        byteArrayInputStream=new ByteArrayInputStream(byteArray);
        try {
            inputStream=new ObjectInputStream(byteArrayInputStream);
            T obj=(T)inputStream.readObject();
            return obj;
        } catch (Exception e) {
            LOG.error("反序列化对象失败"+e);
        }

        return null;
    }

}
