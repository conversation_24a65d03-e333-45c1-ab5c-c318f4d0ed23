package com.cszn.integrated.base.interceptor;


import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;

/**
 * 同步添加事务
 */
public class SyncInterceptor implements Interceptor {

    private Logger log= LoggerFactory.getLogger(SyncInterceptor.class);

    @Override
    public void intercept(Invocation invocation) {
        Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    invocation.invoke();
                    boolean flag=invocation.getReturnValue();
                    StringBuilder sb=new StringBuilder("[");
                    Object[] params=invocation.getArgs();
                    for(int i=0;i<params.length;i++){
                        sb.append("参数"+i+"："+params[i]+"，");
                    }
                    sb.append("]");
                    log.info("执行方法："+invocation.getMethodName()+":"+sb.toString()+"。结果："+flag);
                    invocation.setReturnValue(flag);
                    return flag;
                }catch (Exception e){
                    log.error("执行方法："+invocation.getMethodName()+"异常");
                    e.printStackTrace();
                    invocation.setReturnValue(false);
                    return false;
                }
            }
        });
    }
}
