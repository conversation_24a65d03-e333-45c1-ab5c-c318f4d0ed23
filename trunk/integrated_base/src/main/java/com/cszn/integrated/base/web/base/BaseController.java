package com.cszn.integrated.base.web.base;

import io.jboot.web.controller.JbootController;

import java.util.HashMap;
import java.util.Map;

/**
 * 控制器基类
 * <AUTHOR>
 *
 */
public class BaseController extends JbootController {
	
	public String getUserId(){
		final String userId = getJwtAttr("jwtToken");
		return userId;
	}



	/**
	 * 返回成功
	 *
	 * @param msg 成功信息
	 */
	protected void renderSuccess(String msg,Object data) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("result", Boolean.TRUE);
		result.put("msg", msg);
		result.put("data",data);
		renderJson(result);
	}


	/**
	 * 返回成功
	 *
	 * @param msg 成功信息
	 */
	protected void renderSuccess(String msg) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("result", Boolean.TRUE);
		result.put("msg", msg);
		renderJson(result);
	}

	/**
	 * 返回成功
	 */
	protected void renderSuccess() {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("result", Boolean.TRUE);
		renderJson(result);
	}

	/**
	 * 返回失败
	 *
	 * @param msg 失败描述
	 */
	protected void renderFailed(String msg) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("result", Boolean.FALSE);
		result.put("msg", msg);
		renderJson(result);
	}

	/**
	 * 返回失败
	 */
	protected void renderFailed() {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("result", Boolean.FALSE);
		renderJson(result);
	}

	protected void renderCodeFailed(){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "10001");
		renderJson(result);
	}

	protected void renderCodeFailed(String msg){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "10001");
		result.put("msg",msg);
		renderJson(result);
	}

	protected void renderCodeSuccess(String msg) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "0");
		result.put("msg", msg);
		renderJson(result);
	}

	protected void renderCodeSuccess(String msg,Object data) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "0");
		result.put("msg", msg);
		result.put("data",data);
		renderJson(result);
	}

	protected void renderCodePageSuccess(String msg,Object data,int total) {
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "0");
		result.put("msg", msg);
		result.put("data",data);
		result.put("total",total);
		renderJson(result);
	}
}
