package com.cszn.integrated.base.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.ext.interceptor.LogInterceptor;
import io.jboot.db.model.JbootModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Objects;

public class EntityCompareUtils {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    public static <T> JSONArray getChangedFields(JbootModel newEntity, JbootModel oldEntity) {

        JSONArray changedFields = new JSONArray();//用来保存有修改的字段数组
        try {
            if (newEntity == null) {
                throw new IllegalAccessException("新数据不能为空");
            }

            if (newEntity.getClass() != oldEntity.getClass()) {
                throw new IllegalAccessException("新旧数据不是同一个实体");
            }

            Field[] fields = newEntity.getClass().getFields();//从新实体上拿需要保存的字段
            logger.info("fields.length==="+ fields.length);
            logger.info("fields.toString==="+ fields.toString());
            for (Field field : fields) {
                logger.info("field.getName==="+ field.getName());
                field.setAccessible(true);
                Object newValue = field.get(newEntity);
                Object oldValue = field.get(oldEntity);
                logger.info("newValue.toString==="+ newValue.toString());
                logger.info("oldValue.toString==="+ oldValue.toString());

                if (!(Objects.equals(newValue, oldValue))) {
                    logger.info("值变更，进入取值===");
                    changedFields = appendFieldAndValue(changedFields, field.getName(), newValue, oldValue);
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return changedFields;
    }

    private static JSONArray appendFieldAndValue(JSONArray array, String fieldName, Object newValue, Object oldValue) {
        JSONArray newArray = new JSONArray();
        System.arraycopy(array, 0, newArray, 0, array.size());
        JSONObject newJsonObj = new JSONObject();
        newJsonObj.put("fieldName", fieldName);
        newJsonObj.put("newValue", newValue.toString());
        newJsonObj.put("oldValue", oldValue.toString());
        newArray.add(newJsonObj);
        return newArray;
    }

}
