package com.cszn.integrated.base.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.ext.interceptor.LogInterceptor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2019/10/9
 **/
public class MsgUtils {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);


    /**
     * 发送短信：添加会员卡
     * @param name 姓名
     * @param company 分公司
     * @param cardNumber 会员卡
     * @param phone 手机号
     * @param msgTemplate 短信id
     * @param sendMsgUrl 短信接口url
     * @return
     */
    public static boolean sendMsgBySaveMsg(String name,String company,String cardNumber,String phone,String msgTemplate,String sendMsgUrl){

        JSONObject json = new JSONObject();
        json.put("tempId", msgTemplate);
        json.put("mobile",phone);
        Map<String,Object> params = new HashMap<>();
        params.put("fullName",name);
        params.put("company",company);
        params.put("cardNumber",cardNumber);
        String parmTT = JSON.toJSONString(params);
        json.put("data",parmTT);
        String jsonStr = JSON.toJSONString(json);
        logger.info("添加会员卡发送短信接口入参：[{}]",jsonStr);

        String content = HttpClientsUtils.httpPostRaw(sendMsgUrl,jsonStr,null,null);
        logger.info("旅居发送短信接口响应内容：" + content);
        if(StringUtils.isBlank(content))return false;
        JSONObject jsonObject = JSONObject.parseObject(content);
        if(jsonObject == null ||jsonObject.getInteger("Type") == null|| jsonObject.getInteger("Type") != 1)return false;
        return true;
    }

}
