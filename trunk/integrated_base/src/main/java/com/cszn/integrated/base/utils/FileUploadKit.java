package com.cszn.integrated.base.utils;

import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.upload.UploadFile;
import com.xiaoleilu.hutool.io.FileUtil;
import io.jboot.Jboot;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Base64.Decoder;
import java.util.Base64.Encoder;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by jie on 2017/4/9.
 * 文件上传
 */
public class FileUploadKit {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);


    /**
     * 文件重命名方法
     *
     * @param uploadFile
     * @param username
     * @return
     * @throws IOException
     */
    public static String mv(UploadFile uploadFile, String username) throws IOException {
        if (null != uploadFile && null != uploadFile.getFile()) {
            File file = uploadFile.getFile();
            String uploadPath = Jboot.configValue("uploadPath");
            String fileName = uploadPath + File.separator + username + file.getName();
            FileUtil.move(file, new File(fileName), true);
            return fileName;
        }
        return null;
    }


    /**
     * 上传文件
     * @param file
     * @param folder
     * @return
     */
    public static Map<String, Object> uploadFile(UploadFile file, String folder){

        String dirPath = "";
        String uploadPath = "";
        String uploadPathPrefix = "";
        String urlPrefix = Jboot.configValue("fileUrlPrefix");

        String fileName = file.getFileName();
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        String newFileName = UUID.randomUUID().toString().replace("-","") + "." + suffix;
        if(StringUtils.isNotBlank(folder) && "notice".equals(folder)){
            newFileName = IdGen.getUUID() + "." + suffix;
        }

        //判断系统
        final String osName = System.getProperty("os.name");
        if(osName.toLowerCase().indexOf("linux") > -1 || osName.toLowerCase().indexOf("centos") > -1){
            uploadPathPrefix = Jboot.configValue("uploadPath.linux");
            dirPath = uploadPathPrefix+folder+"/";
            uploadPath = uploadPathPrefix+folder+"/"+newFileName;
        }else{
            uploadPathPrefix = Jboot.configValue("uploadPath");
            dirPath = uploadPathPrefix+folder+"\\";
            uploadPath = uploadPathPrefix+folder+"\\"+newFileName;
        }
        File newFile = new File(uploadPath);
        File dir = new File(dirPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }
        /*if (file.getFile().renameTo(newFile)) {
            file.getFile().delete();
        }*/
        FileInputStream inputStream=null;
        FileOutputStream outputStream=null;
        try {
            inputStream=new FileInputStream(file.getFile());
            outputStream=new FileOutputStream(newFile);
            if(
                "JPG".equals(suffix.toUpperCase())
                ||"JPEG".equals(suffix.toUpperCase())
                ||"GIF".equals(suffix.toUpperCase())
                ||"PNG".equals(suffix.toUpperCase())
                ||"BMP".equals(suffix.toUpperCase())
                ||"WBMP".equals(suffix.toUpperCase())
            ) {
                final long size = file.getFile().length();
                final long picZipSize = 2*1024*1024;
                //图片超过2M就要压缩
                if(size>picZipSize){
                    //压缩处理
                    outputStream.write(PicUtils.compressPicForScale(PicUtils.compressOfQuality(inputStream, 1f), 200));
                }else{
                    //不处理上传文件
                    byte bytes[]=new byte[1024];
                    int bytesRead;
                    while ((bytesRead=inputStream.read(bytes))!=-1){
                        outputStream.write(bytes,0,bytesRead);
                    }
                }
            }else{
                //不处理上传文件
                byte bytes[]=new byte[1024];
                int bytesRead;
                while ((bytesRead=inputStream.read(bytes))!=-1){
                    outputStream.write(bytes,0,bytesRead);
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                if(inputStream!=null){
                    inputStream.close();
                }
                if(outputStream!=null){
                    outputStream.close();
                }
            }catch (Exception e){
                e.printStackTrace();
            }

        }

        if(file.getFile().exists()){
            boolean delete = file.getFile().delete();
        }
        Map<String, Object> data = new HashMap<>();
        data.put("src", urlPrefix + ""+folder+"/" + newFile.getName());
        data.put("path", "/"+folder+"/" + newFile.getName());
        data.put("title", newFile.getName());

        return data;
    }
    
    
    /**
     * 上传文件保存
     * @param file
     * @param folder
     * @return
     */
    public static Map<String, Object> fileSave(UploadFile file, String folder) {
    	
    	String dirPath = "";
    	String uploadPath = "";
    	String uploadPathPrefix = "";
    	final String urlPrefix = Jboot.configValue("fileUrlPrefix");
    	
    	final String fileName = file.getFileName();
    	final String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
    	String newFileName = UUID.randomUUID().toString().replace("-","") + "." + suffix;
    	if(StringUtils.isNotBlank(folder) && "notice".equals(folder)){
    		newFileName = IdGen.getUUID() + "." + suffix;
    	}
    	
    	//判断系统
    	final String osName = System.getProperty("os.name");
    	SimpleDateFormat sdfYear = new SimpleDateFormat("yyyy");
		SimpleDateFormat sdfMonthDay = new SimpleDateFormat("MM-dd");
		String year = sdfYear.format(new Date());
		String monthDay = sdfMonthDay.format(new Date());
    	if(osName.toLowerCase().indexOf("linux") > -1 || osName.toLowerCase().indexOf("centos") > -1){
    		uploadPathPrefix = Jboot.configValue("uploadPath.linux");
    		dirPath = uploadPathPrefix+folder+"/"+year+"/"+monthDay+"/";
    		uploadPath = uploadPathPrefix+folder+"/"+year+"/"+monthDay+"/"+newFileName;
    	}else{
    		uploadPathPrefix = Jboot.configValue("uploadPath");
    		dirPath = uploadPathPrefix+folder+"\\"+year+"\\"+monthDay+"\\";
    		uploadPath = uploadPathPrefix+folder+"\\"+year+"\\"+monthDay+"\\"+newFileName;
    	}
    	File newFile = new File(uploadPath);
    	//如果是图片后缀就执行压缩
    	if(
			"JPG".equals(suffix.toUpperCase())
			||"JPEG".equals(suffix.toUpperCase())
			||"GIF".equals(suffix.toUpperCase())
			||"PNG".equals(suffix.toUpperCase())
			||"BMP".equals(suffix.toUpperCase())
			||"WBMP".equals(suffix.toUpperCase())
		) {
    		long time = System.currentTimeMillis();
    		System.out.println("开始压缩图片了--> ");
    		try {
				double scale = 1.0d ;
				final long size = file.getFile().length();
				final long picZipSize = 2*1024*1024;//图片超过2M就要压缩
//				System.out.println("size--> "+size);
				if(size >= picZipSize) {
			        scale = picZipSize / size  ;
				}
				if(size < picZipSize){
				    Thumbnails.of(file.getFile()).scale(1f).toFile(newFile);
				}else{
				    Thumbnails.of(file.getFile()).scale(1f).outputQuality(scale).toFile(newFile);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
    		System.out.println("压缩总耗时：" + (System.currentTimeMillis() - time)/1000);
    	}
    	//如果是视频后缀就执行压缩
    	if(
			"MP4".equals(suffix.toUpperCase())
			||"M4V".equals(suffix.toUpperCase())
			||"3GP".equals(suffix.toUpperCase())
			||"3GPP".equals(suffix.toUpperCase())
			||"3G2".equals(suffix.toUpperCase())
			||"3GPP2".equals(suffix.toUpperCase())
			||"WMV".equals(suffix.toUpperCase())
			||"RMVB".equals(suffix.toUpperCase())
		) {
    		newFile = VideoCoverCut.compressionVideo(file.getFile(), newFile);
    	}
    	File dir = new File(dirPath);
    	if (!dir.exists()) {
    		dir.mkdirs();
    	}
    	if (file.getFile().renameTo(newFile)) {
    		file.getFile().delete();
    	}
    	if(file.getFile().exists()){
    		file.getFile().delete();
    	}
    	Map<String, Object> returnData = new HashMap<>();
//    	returnData.put("fileName", newFile.getName());
    	returnData.put("fileName", fileName);
    	returnData.put("fileSize", newFile.length()/1024+"");
    	returnData.put("filePath", "/"+folder+"/"+year+"/"+monthDay+"/"+newFile.getName());
    	returnData.put("fileUrl", urlPrefix+folder+"/"+year+"/"+monthDay+"/"+newFile.getName());
    	
    	return returnData;
    }



    /**
     * 将图片转换成Base64编码
     *
     * @param imgFile 待处理图片
     * @return
     */
    public static String getImgStr(String imgFile) {
        InputStream in = null;
        byte[] data = null;
        try {
            URL url = new URL(imgFile);
            URLConnection con = url.openConnection();
            // 设置请求超时为5s
            con.setConnectTimeout(5 * 1000);
            in = con.getInputStream();
            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            //每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            while ((len = in.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            in.close();
            data = outStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }

        Encoder encoder = Base64.getEncoder();
        return encoder.encodeToString(data);
    }


    /**
     * 通过图片长宽检验是否图片
     * @param imgPath
     * @return
     */
    public static boolean isImageByAttr(String imgPath){

        InputStream is = null;
        try {
            URL url = new URL(imgPath);
            URLConnection con = url.openConnection();
            // 设置请求超时为5s
            con.setConnectTimeout(5 * 1000);
            is = con.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }

        Image img = null;
        try {
            img = ImageIO.read(is);
            if (img == null || img.getWidth(null) <= 0 || img.getHeight(null) <= 0) {
                return false;
            }
            return true;
        } catch (Exception e) {
            return false;
        } finally {
            img = null;
        }
    }


    /**
     * 根据图片后缀校验是否图片
     * @param imgPath
     * @return
     */
    public static boolean isImageBySuffix(String imgPath){
        String reg = ".+(.JPEG|.jpeg|.JPG|.jpg|.GIF|.gif|.BMP|.bmp|.PNG|.png)$";
        Pattern pattern = Pattern.compile(reg);
        Matcher matcher = pattern.matcher(imgPath.toLowerCase());
        return matcher.find();
    }



    /**
     * BufferImage转base64
     * @param image
     * @return
     */
    public static String bufferImgToBase64(BufferedImage image){
        String png_base64 = "";
        try {
            Encoder encoder = Base64.getEncoder();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();//io流
            ImageIO.write(image, "png", baos);//写入流中
            byte[] bytes = baos.toByteArray();//转换成字节
            png_base64 = encoder.encodeToString(bytes).trim();//转换成base64串
            logger.info("未转换的base64字符串：[{}]",png_base64);
            //删除 \r\n
            png_base64 = png_base64.replaceAll("\n", "").replaceAll("\r", "");
            png_base64 = "data:image/png;base64," + png_base64;
        } catch (IOException e) {
            logger.info("bufferImage转base64异常：[{}]",e.getMessage());
            e.printStackTrace();
        }
        return png_base64;
    }


    /**
     * 下载网络图片
     * @param fileUrl
     * @return
     */
    public static File downNetworkFile(String fileUrl){
        String fileName = fileUrl.substring(fileUrl.lastIndexOf(".") + 1);
        String suffix = fileUrl.substring(fileUrl.lastIndexOf("."));
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        BufferedOutputStream stream =null;
        InputStream in = null;
        File file = null;
        try {
            URL fileURL = new URL(fileUrl);
            URLConnection con = fileURL.openConnection();
            // 设置请求超时为5s
            con.setConnectTimeout(5 * 1000);
            in = con.getInputStream();
            byte[] buffer = new byte[1024];

            int len = 0;
            while ((len = in.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            file = File.createTempFile(fileName + "-",suffix);
            FileOutputStream fileOutputStream =new FileOutputStream(file);
            stream =new BufferedOutputStream(fileOutputStream);
            stream.write(outStream.toByteArray());

        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (in !=null) in.close();
                if (stream !=null) stream.close();
                outStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }


    /**
     * 下载网络图片
     * @param fileUrl
     * @return
     */
    public static File downNetworkFile(String fileUrl,String fileName){
        String suffix = fileUrl.substring(fileUrl.lastIndexOf("."));
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        BufferedOutputStream stream =null;
        InputStream in = null;
        File file = null;
        try {
            URL fileURL = new URL(fileUrl);
            URLConnection con = fileURL.openConnection();
            // 设置请求超时为5s
            con.setConnectTimeout(5 * 1000);
            in = con.getInputStream();
            byte[] buffer = new byte[1024];

            int len = 0;
            while ((len = in.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }
            file=new File(fileName+suffix);
            FileOutputStream fileOutputStream =new FileOutputStream(file);
            stream =new BufferedOutputStream(fileOutputStream);
            stream.write(outStream.toByteArray());

        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (in !=null) in.close();
                if (stream !=null) stream.close();
                outStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file;
    }

    public static File inStreamToFile(InputStream inputStream,String name,String suffix){
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        BufferedOutputStream stream=null;
        File file=null;
        try {
            byte[] buffer=new byte[1024];
            int len=0;
            while ((len= inputStream.read(buffer))!=-1){
                os.write(buffer,0,len);
                os.flush();
            }
            file=new File(name);
            FileOutputStream outputStream=new FileOutputStream(file);
            stream=new BufferedOutputStream(outputStream);
            stream.write(os.toByteArray());


        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                if(inputStream!=null){
                    inputStream.close();
                }
                if(stream!=null){
                    stream.close();
                }
                os.close();
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        return file;
    }


    /**
     * 压缩图片
     * @param base64Img
     * @return
     */
    public static String resizeImageTo800(String base64Img) {
        try {
            BufferedImage src = base64String2BufferedImage(base64Img);
            BufferedImage output = Thumbnails.of(src).size(800, 800).outputQuality(1f).asBufferedImage();
            String base64 = imageToBase64(output);
            output = Thumbnails.of(output).scale(1f).asBufferedImage();
            base64 = imageToBase64(output);
            return imageToBase64(output);
        } catch (Exception e) {
            return base64Img;
        }
    }

    public static void main(String[] args) throws Exception {
        File file=new File("C:\\Users\\<USER>\\Desktop\\新建文件夹 (4)\\35163d31c49b49486d7f5c286175043.jpg");
        FileInputStream stream=new FileInputStream(file);
        byte[] buffer=new byte[(int)file.length()];
        stream.read(buffer);
        stream.close();
        Encoder encoder = Base64.getEncoder();
        String base64Str=encoder.encodeToString(buffer);
        File file1=new File("C:\\Users\\<USER>\\Desktop\\新建文件夹 (4)\\a.txt");

        FileOutputStream outputStream=new FileOutputStream(file1);
        outputStream.write(base64Str.getBytes());
        outputStream.close();

        String feafea=resizeImageTo800(base64Str);

        File file2=new File("C:\\Users\\<USER>\\Desktop\\新建文件夹 (4)\\b.txt");

        FileOutputStream outputStream2=new FileOutputStream(file2);
        outputStream2.write(feafea.getBytes());
        outputStream2.close();
    }


    /**
     * base64转换成为BufferedImage
     * @param base64string
     * @return
     */
    public static BufferedImage base64String2BufferedImage(String base64string) {
        BufferedImage image = null;
        try {
            InputStream stream = BaseToInputStream(base64string);
            image = ImageIO.read(stream);
        } catch (IOException e) {
        }
        return image;
    }


    /**
     * BufferedImage转换成base64
     * @param bufferedImage
     * @return
     */
    public static String imageToBase64(BufferedImage bufferedImage) {
        Encoder encoder = Base64.getEncoder();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(bufferedImage, "jpg", baos);
        } catch (IOException e) {
        }
        return encoder.encodeToString(baos.toByteArray());
    }


    /**
     * base64转inputStream
     * @param base64string
     * @return
     */
    private static InputStream BaseToInputStream(String base64string){
        ByteArrayInputStream stream = null;
        try {
            Decoder decoder = Base64.getDecoder();
            byte[] bytes1 = decoder.decode(base64string);
            stream = new ByteArrayInputStream(bytes1);
        } catch (Exception e) {

        }
        return stream;
    }


    /**
     * 判断是否压缩
     * @param base64Img
     * @return
     */
    public static boolean isPompress(String base64Img){
        try {
        	Decoder decoder = Base64.getDecoder();
            // 将 base64 字符转转为二进制数组类型
            byte[] decoderBytes = decoder.decode(base64Img);
            // 二进制输入流
            InputStream in = new ByteArrayInputStream(decoderBytes);
            BufferedImage ImageOne = ImageIO.read(in);
            //如果高或宽大于800，则返回true
            if(ImageOne.getHeight()> 800 || ImageOne.getWidth() > 800){
                return true;
            }
            in.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }
}
