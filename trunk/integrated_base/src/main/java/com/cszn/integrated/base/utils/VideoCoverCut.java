package com.cszn.integrated.base.utils;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.net.URLConnection;
import java.util.UUID;

import javax.imageio.ImageIO;

import org.apache.commons.io.IOUtils;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;

import ws.schild.jave.AudioAttributes;
import ws.schild.jave.AudioInfo;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncodingAttributes;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.VideoAttributes;
import ws.schild.jave.VideoInfo;
import ws.schild.jave.VideoSize;

public class VideoCoverCut {

    /**
     * 截取视频第六帧的图片
     *
     * @param file 视频对象
     * @param dir      文件存放的根目录
     * @return 图片的相对路径 例：pic/1.png
     */
    public static String videoImage(File file, String dir) throws Exception {
        String pngPath = "";
//        FFmpegFrameGrabber ff = FFmpegFrameGrabber.createDefault(filePath);
        FFmpegFrameGrabber ff = FFmpegFrameGrabber.createDefault(file);

        ff.start();
        int ffLength = ff.getLengthInFrames();
        Frame f;
        int i = 0;
        while (i < ffLength) {
            f = ff.grabFrame();
            //截取第6帧
            if ((i > 5) && (f.image != null)) {
                //生成图片的相对路径 例如：pic/uuid.png
                pngPath = getPngPath();
                //执行截图并放入指定位置
                doExecuteFrame(f, dir + pngPath);
                break;
            }
            i++;
        }
        ff.stop();

        return pngPath;
    }

    /**
     * 截取视频第六帧的图片
     *
     * @param url 网络视频地址
     * @param dir 文件存放的根目录
     * @return 图片的相对路径 例：pic/1.png
     */
    public static String getVideoCoverByURL(String url, String dir) throws Exception {
        InputStream videoInputStream = getVideoInputStream(url);
        String pngPath = "";
        FFmpegFrameGrabber ff = new FFmpegFrameGrabber(videoInputStream);
        ff.start();
        int ffLength = ff.getLengthInFrames();
        Frame f;
        int i = 0;
        while (i < ffLength) {
            f = ff.grabFrame();
            //截取第6帧
            if ((i > 5) && (f.image != null)) {
                //生成图片的相对路径 例如：pic/uuid.png
                pngPath = getPngPath();
                //执行截图并放入指定位置
                doExecuteFrame(f, dir + pngPath);
                break;
            }
            i++;
        }
        ff.stop();
        IOUtils.closeQuietly(videoInputStream);

        return pngPath;
    }

    /**
     * 将网络视频地址，转成输入流
     *
     * @param videoUrl
     * @return
     */
    private static InputStream getVideoInputStream(String videoUrl) {
        // 1.下载网络文件
        try {
            URL url = new URL(videoUrl);
            //2.获取链接
            URLConnection conn = url.openConnection();
            //3.输入流
            InputStream inStream = conn.getInputStream();
            return inStream;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 生成图片的相对路径
     *
     * @return 图片的相对路径 例：pic/1.png
     */
    private static String getPngPath() {
        return getUUID() + ".png";
    }


    /**
     * 生成唯一的uuid
     *
     * @return uuid
     */
    private static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }


    /**
     * 截取缩略图
     *
     * @param f                       Frame
     * @param targerFilePath:封面图片存放路径
     */
    private static void doExecuteFrame(Frame f, String targerFilePath) {
        String imagemat = "png";
        if (null == f || null == f.image) {
            return;
        }
        Java2DFrameConverter converter = new Java2DFrameConverter();
        BufferedImage bi = converter.getBufferedImage(f);
        File output = new File(targerFilePath);
        try {
            ImageIO.write(bi, imagemat, output);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 传视频File对象，返回压缩后File对象信息
     * @param source
     */
    public static File compressionVideo(File source,String picName) {
        if(source == null){
            return null;
        }
        String newPath="";
        final String osName = System.getProperty("os.name");
        if(osName.toLowerCase().indexOf("linux") > -1 || osName.toLowerCase().indexOf("centos") > -1){
            newPath = source.getAbsolutePath().substring(0, source.getAbsolutePath().lastIndexOf("/")).concat("/"+picName);
        }else{
            newPath = source.getAbsolutePath().substring(0, source.getAbsolutePath().lastIndexOf("\\")).concat("\\"+picName);
        }
        File target = new File(newPath);
        try {
            MultimediaObject object = new MultimediaObject(source);
            AudioInfo audioInfo = object.getInfo().getAudio();
            // 根据视频大小来判断是否需要进行压缩,
            int maxSize = 100;
            double mb = Math.ceil(source.length()/ 1048576);
            int second = (int)object.getInfo().getDuration()/1000;
            BigDecimal bd = new BigDecimal(String.format("%.4f", mb/second));
            System.out.println("开始压缩视频了--> 视频每秒平均 "+ bd +" MB ");
            // 视频 > 100MB, 或者每秒 > 0.5 MB 才做压缩， 不需要的话可以把判断去掉
            boolean temp = mb > maxSize || bd.compareTo(new BigDecimal(0.5)) > 0;
            if(temp){
                long time = System.currentTimeMillis();
                //TODO 视频属性设置
                int maxBitRate = 128000;
                int maxSamplingRate = 44100;
                int bitRate = 800000;
                int maxFrameRate = 20;
                int maxWidth = 1280;

                AudioAttributes audio = new AudioAttributes();
                // 设置通用编码格式
                audio.setCodec("aac");
                // 设置最大值：比特率越高，清晰度/音质越好
                // 设置音频比特率,单位:b (比特率越高，清晰度/音质越好，当然文件也就越大 128000 = 182kb)
                if(audioInfo.getBitRate() > maxBitRate){
                    audio.setBitRate(new Integer(maxBitRate));
                }

                // 设置重新编码的音频流中使用的声道数（1 =单声道，2 = 双声道（立体声））。如果未设置任何声道值，则编码器将选择默认值 0。
                audio.setChannels(audioInfo.getChannels());
                // 采样率越高声音的还原度越好，文件越大
                // 设置音频采样率，单位：赫兹 hz
                // 设置编码时候的音量值，未设置为0,如果256，则音量值不会改变
                // audio.setVolume(256);
                if(audioInfo.getSamplingRate() > maxSamplingRate){
                    audio.setSamplingRate(maxSamplingRate);
                }

                //TODO 视频编码属性配置
                VideoInfo videoInfo = object.getInfo().getVideo();
                VideoAttributes video = new VideoAttributes();
                video.setCodec("h264");
                //设置音频比特率,单位:b (比特率越高，清晰度/音质越好，当然文件也就越大 800000 = 800kb)
                if(videoInfo.getBitRate() > bitRate){
                    video.setBitRate(bitRate);
                }

                // 视频帧率：15 f / s  帧率越低，效果越差
                // 设置视频帧率（帧率越低，视频会出现断层，越高让人感觉越连续），视频帧率（Frame rate）是用于测量显示帧数的量度。所谓的测量单位为每秒显示帧数(Frames per Second，简：FPS）或“赫兹”（Hz）。
                if(videoInfo.getFrameRate() > maxFrameRate){
                    video.setFrameRate(maxFrameRate);
                }

                // 限制视频宽高
                int width = videoInfo.getSize().getWidth();
                int height = videoInfo.getSize().getHeight();
                if(width > maxWidth){
                    float rat = (float) width / maxWidth;
                    video.setSize(new VideoSize(maxWidth,(int)(height/rat)));
                }

                EncodingAttributes attr = new EncodingAttributes();
                attr.setFormat("mp4");
                attr.setAudioAttributes(audio);
                attr.setVideoAttributes(video);

                // 速度最快的压缩方式， 压缩速度 从快到慢： ultrafast, superfast, veryfast, faster, fast, medium,  slow, slower, veryslow and placebo.
//                attr.setPreset(PresetUtil.VERYFAST);
//                attr.setCrf(27);
//                // 设置线程数
//                attr.setEncodingThreads(Runtime.getRuntime().availableProcessors()/2);

                Encoder encoder = new Encoder();
                encoder.encode(new MultimediaObject(source), target, attr);
                System.out.println("压缩总耗时：" + (System.currentTimeMillis() - time)/1000);
                return target;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(target.length() > 0){
                source.delete();
            }
        }
        return source;
    }
    
    /**
     * 传视频File对象，返回压缩后File对象信息
     * @param source
     */
    public static File compressionVideo(File source,File target) {
    	if(source == null){
    		return null;
    	}
    	try {
    		MultimediaObject object = new MultimediaObject(source);
    		AudioInfo audioInfo = object.getInfo().getAudio();
    		// 根据视频大小来判断是否需要进行压缩,
    		int maxSize = 100;
    		double mb = Math.ceil(source.length()/ 1048576);
    		int second = (int)object.getInfo().getDuration()/1000;
    		BigDecimal bd = new BigDecimal(String.format("%.4f", mb/second));
    		long time = System.currentTimeMillis();
    		System.out.println("开始压缩视频了--> 视频每秒平均 "+ bd +" MB ");
    		// 视频 > 100MB, 或者每秒 > 0.5 MB 才做压缩， 不需要的话可以把判断去掉
    		//mb > maxSize || 
    		if(bd.compareTo(new BigDecimal(0.5)) > 0){
    			//TODO 视频属性设置
    			int maxBitRate = 128000;
    			int maxSamplingRate = 44100;
    			int bitRate = 800000;
    			int maxFrameRate = 20;
    			int maxWidth = 1280;
    			
    			AudioAttributes audio = new AudioAttributes();
    			// 设置通用编码格式
    			audio.setCodec("aac");
    			// 设置最大值：比特率越高，清晰度/音质越好
    			// 设置音频比特率,单位:b (比特率越高，清晰度/音质越好，当然文件也就越大 128000 = 182kb)
    			if(audioInfo.getBitRate() > maxBitRate){
    				audio.setBitRate(new Integer(maxBitRate));
    			}
    			
    			// 设置重新编码的音频流中使用的声道数（1 =单声道，2 = 双声道（立体声））。如果未设置任何声道值，则编码器将选择默认值 0。
    			audio.setChannels(audioInfo.getChannels());
    			// 采样率越高声音的还原度越好，文件越大
    			// 设置音频采样率，单位：赫兹 hz
    			// 设置编码时候的音量值，未设置为0,如果256，则音量值不会改变
    			// audio.setVolume(256);
    			if(audioInfo.getSamplingRate() > maxSamplingRate){
    				audio.setSamplingRate(maxSamplingRate);
    			}
    			
    			//TODO 视频编码属性配置
    			VideoInfo videoInfo = object.getInfo().getVideo();
    			VideoAttributes video = new VideoAttributes();
    			video.setCodec("h264");
    			//设置音频比特率,单位:b (比特率越高，清晰度/音质越好，当然文件也就越大 800000 = 800kb)
    			if(videoInfo.getBitRate() > bitRate){
    				video.setBitRate(bitRate);
    			}
    			
    			// 视频帧率：15 f / s  帧率越低，效果越差
    			// 设置视频帧率（帧率越低，视频会出现断层，越高让人感觉越连续），视频帧率（Frame rate）是用于测量显示帧数的量度。所谓的测量单位为每秒显示帧数(Frames per Second，简：FPS）或“赫兹”（Hz）。
    			if(videoInfo.getFrameRate() > maxFrameRate){
    				video.setFrameRate(maxFrameRate);
    			}
    			
    			// 限制视频宽高
    			int width = videoInfo.getSize().getWidth();
    			int height = videoInfo.getSize().getHeight();
    			if(width > maxWidth){
    				float rat = (float) width / maxWidth;
    				video.setSize(new VideoSize(maxWidth,(int)(height/rat)));
    			}
    			
    			EncodingAttributes attr = new EncodingAttributes();
    			attr.setFormat("mp4");
    			attr.setAudioAttributes(audio);
    			attr.setVideoAttributes(video);
    			
    			// 速度最快的压缩方式， 压缩速度 从快到慢： ultrafast, superfast, veryfast, faster, fast, medium,  slow, slower, veryslow and placebo.
//                attr.setPreset(PresetUtil.VERYFAST);
//                attr.setCrf(27);
//                // 设置线程数
//                attr.setEncodingThreads(Runtime.getRuntime().availableProcessors()/2);
    			
    			Encoder encoder = new Encoder();
    			encoder.encode(new MultimediaObject(source), target, attr);
    		}
    		System.out.println("压缩总耗时：" + (System.currentTimeMillis() - time)/1000);
    		return target;
    	} catch (Exception e) {
    		e.printStackTrace();
    	}finally {
    		if(target.length() > 0){
    			source.delete();
    		}
    	}
    	return source;
    }


    public static void main(String[] args) throws Exception {
        /*File file=new File("E:\\龙沐湾老年大学昌松乒乓.乐东俱乐部 个人友谊赛.mp4");
        File newFile=compressionVideo(file,"aaaa.mp4");
        FileInputStream fileInputStream=new FileInputStream(newFile);

        */

    }

}
