package com.cszn.integrated.base.utils;

import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.DataInputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description 发送bio数据帮助类
 * <AUTHOR>
 * @Date 2019/8/14
 **/
public class SendBioHelper {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    public static Map<String, Socket> socketMap = new HashMap<>();

    public static Map<String, Socket> getSocketMap() {
        return socketMap;
    }


    /**
     * 发送bio手环设置数据
     * @param equipmentNo 设备号
     * @param command 指令
     * @return
     */
    public static String sendBioData(String equipmentNo,String command){

        Socket socket = socketMap.get(equipmentNo);

        if(socket == null){
            logger.info("socket为null");
            return "null";
        }
        if(!socket.isConnected()){
            logger.info("socket未连接");
            return "null";
        }
        if(socket.isClosed()){
            logger.info("socket已关闭");
            return "null";
        }

        PrintStream out = null;
        try {
            out = new PrintStream(socket.getOutputStream());
            out.print(command);
            Db.update("INSERT INTO mms_command_record (id, equipment_no, command, create_time) VALUES (?, ?, ?,?)",
                    IdGen.getUUID(),equipmentNo,command,new Date());
        } catch (IOException e) {
            logger.info("手环发送数据异常：[{}]",e.getMessage());
            e.printStackTrace();
            return null;
        }
        return "suc";
    }
}
