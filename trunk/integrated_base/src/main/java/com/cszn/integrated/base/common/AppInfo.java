package com.cszn.integrated.base.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import io.jboot.app.config.annotation.ConfigModel;

import java.util.HashMap;

/**
 * 应用信息
 * <AUTHOR>
 *
 */
@ConfigModel(prefix = "jboot.admin.app")
public class AppInfo {

    private String name;
    private String org;
    private String orgWebsite;
    private String resourceHost;
    private String copyRight;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOrg() {
        return org;
    }

    public void setOrg(String org) {
        this.org = org;
    }

    public String getOrgWebsite() {
        return orgWebsite;
    }

    public void setOrgWebsite(String orgWebsite) {
        this.orgWebsite = orgWebsite;
    }

    public String getResourceHost() {
        return resourceHost;
    }

    public void setResourceHost(String resourceHost) {
        this.resourceHost = resourceHost;
    }

    public String getCopyRight() {
        return copyRight;
    }

    public void setCopyRight(String copyRight) {
        this.copyRight = copyRight;
    }

    @Override
    public String toString() {
        return "AppInfo{" +
                "name='" + name + '\'' +
                ", org='" + org + '\'' +
                ", orgWebsite='" + orgWebsite + '\'' +
                ", resourceHost='" + resourceHost + '\'' +
                ", copyRight='" + copyRight + '\'' +
                '}';
    }
}
