package com.cszn.integrated.base.utils;

import org.apache.commons.lang3.RandomStringUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class IdGen {
	/**
	 * 生成UUID
	 * @return
	 */
	public static String getUUID() {
		UUID randomUUID = UUID.randomUUID();
		String uuid = randomUUID.toString().toUpperCase();
		return uuid;
	}
	
	/**
	 * 获取现在时间
	 * 
	 * @return返回字符串格式yyyyMMddHHmmss
	 */
	public static String getStringDate() {
		Date currentTime = new Date();
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateString = formatter.format(currentTime);
		//System.out.println("TIME:::" + dateString);
		return dateString;
	}

	/**
	 * 由年月日时分秒+3位随机数 生成流水号
	 * 
	 * @return
	 */
	public synchronized static String getSerialNumber() {
		String t = getStringDate();
		int x = (int) (Math.random() * 900) + 100;
		String serial = t + x;
		return serial;
	}


	/**
	 * 生成8位不重复随机数
	 * @return
	 */
	public synchronized static String getEightRandom(){
		return RandomStringUtils.random(8, "1234567890");
	}


	/**
	 * 生成6位随机数
	 * @return
	 */
	public synchronized static String getVerifyCode(){
		return RandomStringUtils.random(6, "1234567890");
	}



	public static String getIpAddress(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				 ip = request.getHeader("Proxy-Client-IP");
			 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				 ip = request.getHeader("WL-Proxy-Client-IP");
			 }
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				 ip = request.getHeader("HTTP_CLIENT_IP");
			 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				 ip = request.getHeader("HTTP_X_FORWARDED_FOR");
			 }
		 if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
				 ip = request.getRemoteAddr();
			}
		return ip;
	 }


	/**
	 * 处理ip地址
	 * @param ipAddr
	 * @return
	 */
	public static String dealIpAddr(String ipAddr){
		if(ipAddr.contains(":")){
			ipAddr = ipAddr.substring(0,ipAddr.indexOf(":"));
		}
		return ipAddr;
	 }
	
	
    /**
     * 通过身份证号码获取出生日期、性别、年龄
     * @param certificateNo
     * @return 返回的出生日期格式：1990-01-01   性别格式：F-女，M-男
     */
    public static Map<String, String> getBirAgeSex(String certificateNo) {
        String birthday = "";
        String age = "";
        String sexCode = "";

        int year = Calendar.getInstance().get(Calendar.YEAR);
        char[] number = certificateNo.toCharArray();
        boolean flag = true;
        if (number.length == 15) {
            for (int x = 0; x < number.length; x++) {
                if (!flag) return new HashMap<String, String>();
                flag = Character.isDigit(number[x]);
            }
        } else if (number.length == 18) {
            for (int x = 0; x < number.length - 1; x++) {
                if (!flag) return new HashMap<String, String>();
                flag = Character.isDigit(number[x]);
            }
        }
        if (flag && certificateNo.length() == 15) {
            birthday = "19" + certificateNo.substring(6, 8) + "-"
                    + certificateNo.substring(8, 10) + "-"
                    + certificateNo.substring(10, 12);
            sexCode = Integer.parseInt(certificateNo.substring(certificateNo.length() - 3, certificateNo.length())) % 2 == 0 ? "F" : "M";
            age = (year - Integer.parseInt("19" + certificateNo.substring(6, 8))) + "";
        } else if (flag && certificateNo.length() == 18) {
            birthday = certificateNo.substring(6, 10) + "-"
                    + certificateNo.substring(10, 12) + "-"
                    + certificateNo.substring(12, 14);
            sexCode = Integer.parseInt(certificateNo.substring(certificateNo.length() - 4, certificateNo.length() - 1)) % 2 == 0 ? "F" : "M";
            age = (year - Integer.parseInt(certificateNo.substring(6, 10))) + "";
        }
        Map<String, String> map = new HashMap<String, String>();
        map.put("birthday", birthday);
        map.put("age", age);
        map.put("sexCode", sexCode);
        return map;
    }


	/*public static void main(String[] args) {
		System.out.println(dealIpAddr("*************:51700"));
	}*/
}
