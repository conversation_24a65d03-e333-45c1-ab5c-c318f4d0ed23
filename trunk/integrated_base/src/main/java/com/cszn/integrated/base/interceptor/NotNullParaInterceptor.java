package com.cszn.integrated.base.interceptor;

import java.lang.reflect.Method;

import com.cszn.integrated.base.common.RestResult;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;

import io.jboot.utils.ArrayUtil;
import io.jboot.utils.StrUtil;
import io.jboot.web.controller.JbootController;

/**
 * 非空参数拦截器
 * <AUTHOR>
 *
 */
public class NotNullParaInterceptor implements Interceptor {

    /** 异常页面 */
    private String exceptionView = "/exception.html";

    public NotNullParaInterceptor(String exceptionView) {
        this.exceptionView = exceptionView;
    }

    public void intercept(Invocation inv) {
        Method method = inv.getMethod();

        NotNullPara notNullPara = method.getAnnotation(NotNullPara.class);
        if (notNullPara == null) {
            inv.invoke();
            return;
        }

        String[] paraKeys = notNullPara.value();
        if (ArrayUtil.isNullOrEmpty(paraKeys)) {
            inv.invoke();
            return;
        }

        for (String param : paraKeys) {
            String value = inv.getController().getPara(param);
            if (value == null || value.trim().length() == 0) {
                renderError(inv, param, notNullPara.errorRedirect());
                return;
            }
        }

        inv.invoke();
    }


    private void renderError(Invocation inv, String param, String errorRedirect) {
        if (StrUtil.isNotBlank(errorRedirect)) {
            inv.getController().redirect(errorRedirect);
            return;
        }

        Controller controller = inv.getController();
        if (controller instanceof JbootController) {
            JbootController jc = (JbootController) controller;
            if (jc.isAjaxRequest()) {
                jc.renderJson(RestResult.buildError("参数["+param+"]不可为空"));
                return;
            }
        }
        controller.setAttr(BusinessExceptionInterceptor.MESSAGE_TAG, "参数["+param+"]不可为空").render(exceptionView);
    }
}
