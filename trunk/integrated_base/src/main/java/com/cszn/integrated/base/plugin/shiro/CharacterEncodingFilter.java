package com.cszn.integrated.base.plugin.shiro;

import com.jfinal.core.Const;
import org.apache.shiro.web.servlet.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;


/**
 * shiro 编码过滤器
 * <AUTHOR>
 *
 */
public class CharacterEncodingFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws ServletException, IOException {
        request.setCharacterEncoding(Const.DEFAULT_ENCODING);
        chain.doFilter(request, response);
    }
}
