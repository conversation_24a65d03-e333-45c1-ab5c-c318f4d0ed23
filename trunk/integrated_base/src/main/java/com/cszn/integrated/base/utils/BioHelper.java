package com.cszn.integrated.base.utils;

import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

/**
 * @Description Bio手环帮助类
 * <AUTHOR>
 * @Date 2019/8/9
 **/
public class BioHelper {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);


    /**
     * 处理发送指令
     * @param manufacturer 厂商
     * @param equipmentNo 设备号
     * @param length 内容长度
     * @param code 内容
     * @param sets 设置
     * @return
     */
    public static String dealSendCommand(String manufacturer,String equipmentNo,String length,String code,String[] sets){

        String command = "["+ manufacturer + "*" + equipmentNo + "*" + length + "*" + code;
        if(sets == null || sets.length <= 0){
            command += "]";
        }else{
            for (int i = 0; i < sets.length; i++) {
                command += ",";
                command +=sets[i];
            }
            command += "]";
        }
        return command;
    }



    /***
     * 处理回复指令
     * @param command
     * @return
     */
    public static String dealReplyCommand(String command){
        String[] commandArr = command.replace("*",",").split(",");
        commandArr[2] = "0002";
        return StringUtils.join(commandArr,"*");
    }



    /**
     * 处理手环设备号
     * @return
     */
    public static String dealEquipmentNumber(String equipmentNumber){
        String[] equipmentNumberArr = equipmentNumber.replace("*",",").split(",");
        return equipmentNumberArr[1];
    }


    /**
     * 处理终端状态
     * @return
     */
    public static Map<String,String> dealTerminalStatus(String terminalStatus,Map<String,String> mapStatus){
        String binarySys = dealHaxToArray(terminalStatus);
        if(binarySys.length() >= 20) {
            mapStatus.put("低电状态", binarySys.substring(0, 1));
            mapStatus.put("出围栏状态", binarySys.substring(1, 2));
            mapStatus.put("进围栏状态", binarySys.substring(2, 3));
            mapStatus.put("手环戴上取下状态", binarySys.substring(3, 4));
            mapStatus.put("手表运行静止状态", binarySys.substring(4, 5));
            mapStatus.put("SOS报警", binarySys.substring(16, 17));
            mapStatus.put("低电报警", binarySys.substring(17, 18));
            mapStatus.put("出围栏报警", binarySys.substring(18, 19));
            mapStatus.put("进围栏报警", binarySys.substring(19, 20));
            mapStatus.put("手环拆除报警", binarySys.substring(20, 21));
        }
        return mapStatus;
    }


    /**
     * 16进制转2进制
     * @return
     */
    public static String dealHaxToArray(String terminalStatus){
        String[] h2b = {"0000", "0001", "0010", "0011", "0100", "0101", "0110", "0111", "1000", "1001", "1010", "1011", "1100", "1101", "1110", "1111",};
        char[] chs=terminalStatus.toCharArray();
        String[] b = new String[chs.length];
        for (int i = 0; i < chs.length; i++) {
            b[i] = chs[i] >= 'a' ? h2b[chs[i] - 'a' + 10] : chs[i] >= 'A' ? h2b[chs[i] - 'A' + 10] : h2b[chs[i] - '0'];
        }
        return StringUtils.join(b);
    }


    /**
     * 根据设备号获取会员id
     * @return
     */
    public static String getMemberIdByEqNo(String equipmentNo){
        String sql="select c.member_id as memberId from mms_card_equipment e left join fina_membership_card c on e.card_id=c.id where e.del_flag='0'" +
                " and e.equipment_no=? order by e.create_date desc limit 1";
        String memberId = Db.queryStr(sql,equipmentNo);
        if(StringUtils.isBlank(memberId)){
            return null;
        }else{
            return memberId;
        }
    }



    public static void main(String[] args) {
        //System.out.println(dealEquipmentNumber("3G*8800000015*0002*LK"));
        /*Map<String,String> map = new HashMap<>();
        map = dealTerminalStatus("00000010",map);
        System.out.println(map);*/
        /*String[] h2b = {
                "0000",
                "0001",
                "0010",
                "0011",
                "0100",
                "0101",
                "0110",
                "0111",
                "1000",
                "1001",
                "1010",
                "1011",
                "1100",
                "1101",
                "1110",
                "1111",
        };

        String a = "00000010";
        char[] chs=a.toCharArray();
        String[] b = new String[chs.length];
        for (int i = 0; i < chs.length; i++) {
            b[i] = chs[i] >= 'a' ? h2b[chs[i] - 'a' + 10] : chs[i] >= 'A' ? h2b[chs[i] - 'A' + 10] : h2b[chs[i] - '0'];
        }
        String B = StringUtils.join(b);
        System.out.println(B);*/
        //System.out.println(dealCommand("3G*8800000015*0002*LK"));
        /*String bioData = "3G*8800000015*0002*LK";
        String[] bioDataArr = bioData.split(",");
        for (String item : bioDataArr) {
            System.out.println("=="+item);
        }*/
        /*String str = dealSendCommand("CS","5786476","0004","CD",new String[]{});
        System.out.println(str);*/
    }
}
