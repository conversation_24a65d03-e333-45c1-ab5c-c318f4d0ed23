package com.cszn.integrated.base.captcha;

import com.cszn.integrated.base.common.CacheKey;
import com.jfinal.captcha.Captcha;
import com.jfinal.captcha.ICaptchaCache;

import io.jboot.Jboot;

/**
 * 验证码 redis 集群
 * <AUTHOR>
 *
 */
public class CaptchaCache implements ICaptchaCache {

	public void put(Captcha captcha) {
		Jboot.getCache().put(CacheKey.CACHE_CAPTCHAR_SESSION, captcha.getKey(), captcha, (int) (captcha.getExpireAt() - System.currentTimeMillis()) / 1000);
	}

	public Captcha get(String key) {
		return Jboot.getCache().get(CacheKey.CACHE_CAPTCHAR_SESSION, key);
	}

	public void remove(String key) {
		Jboot.getCache().remove(CacheKey.CACHE_CAPTCHAR_SESSION, key);
	}

	public void removeAll() {
		Jboot.getCache().removeAll(CacheKey.CACHE_CAPTCHAR_SESSION);
	}

}
