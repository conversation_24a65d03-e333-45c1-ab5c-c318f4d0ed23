package com.cszn.integrated.base.utils;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 日期工具类, 继承org.apache.commons.lang.time.DateUtils类
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

	private static String[] parsePatterns = {
			"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", 
			"yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
			"yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};
	
	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd）
	 */
	public static String getDate() {
		return getDate("yyyy-MM-dd");
	}
	
	/**
	 * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String getDate(String pattern) {
		return DateFormatUtils.format(new Date(), pattern);
	}
	
	/**
	 * 得到日期字符串 默认格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
	 */
	public static String formatDate(Date date, Object... pattern) {
		String formatDate = null;
		if (pattern != null && pattern.length > 0) {
			formatDate = DateFormatUtils.format(date, pattern[0].toString());
		} else {
			formatDate = DateFormatUtils.format(date, "yyyy-MM-dd");
		}
		return formatDate;
	}
	
	/**
	 * 获取两个日期之间的所有日期集合
	 * @param start
	 * @param end
	 * @return
	 */
	public static List<Date> getBetweenDates(Date start, Date end) {

		Calendar startCalendar=Calendar.getInstance();
		startCalendar.setTime(start);
		startCalendar.set(Calendar.HOUR_OF_DAY,0);
		startCalendar.set(Calendar.MINUTE,0);
		startCalendar.set(Calendar.SECOND,0);

		Calendar endCalendar=Calendar.getInstance();
		endCalendar.setTime(end);
		endCalendar.set(Calendar.HOUR_OF_DAY,0);
		endCalendar.set(Calendar.MINUTE,0);
		endCalendar.set(Calendar.SECOND,0);



	    List<Date> result = new ArrayList<Date>();
	    Calendar tempStart = Calendar.getInstance();
	    tempStart.setTime(startCalendar.getTime());
	    
	    Calendar tempEnd = Calendar.getInstance();
	    tempEnd.setTime(endCalendar.getTime());
	    tempEnd.add(Calendar.DAY_OF_YEAR, 1);
	    while (tempStart.before(tempEnd)) {
	        result.add(tempStart.getTime());
	        tempStart.add(Calendar.DAY_OF_YEAR, 1);
	    }
	    return result;
	}
	
	/**
	 * 得到日期时间字符串，转换格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String formatDateTime(Date date) {
		return formatDate(date, "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前时间字符串 格式（HH:mm:ss）
	 */
	public static String getTime() {
		return formatDate(new Date(), "HH:mm:ss");
	}

	/**
	 * 得到当前日期和时间字符串 格式（yyyy-MM-dd HH:mm:ss）
	 */
	public static String getDateTime() {
		return formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
	}

	/**
	 * 得到当前年份字符串 格式（yyyy）
	 */
	public static String getYear() {
		return formatDate(new Date(), "yyyy");
	}

	public static String getYear(Date date) {
		return formatDate(date, "yyyy");
	}

	/**
	 * 得到当前月份字符串 格式（MM）
	 */
	public static String getMonth() {
		return formatDate(new Date(), "MM");
	}

	/**
	 * 得到当天字符串 格式（dd）
	 */
	public static String getDay() {
		return formatDate(new Date(), "dd");
	}
	
	/**
	 * 得到当天字符串 格式（dd）
	 */
	public static String getDay(Date date) {
		return formatDate(date, "dd");
	}

	/**
	 * 得到当前星期字符串 格式（E）星期几
	 */
	public static String getWeek() {
		return formatDate(new Date(), "E");
	}
	
	/**
	 * 日期型字符串转化为日期 格式
	 * { "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", 
	 *   "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm",
	 *   "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm" }
	 */
	public static Date parseDate(Object str) {
		if (str == null){
			return null;
		}
		try {
			return parseDate(str.toString(), parsePatterns);
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * 获取过去的天数
	 * @param date
	 * @return
	 */
	public static long pastDays(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(24*60*60*1000);
	}

	/**
	 * 获取过去的小时
	 * @param date
	 * @return
	 */
	public static long pastHour(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(60*60*1000);
	}
	
	/**
	 * 获取过去的分钟
	 * @param date
	 * @return
	 */
	public static long pastMinutes(Date date) {
		long t = new Date().getTime()-date.getTime();
		return t/(60*1000);
	}
	
	/**
	 * 转换为时间（天,时:分:秒.毫秒）
	 * @param timeMillis
	 * @return
	 */
    public static String formatDateTime(long timeMillis){
		long day = timeMillis/(24*60*60*1000);
		long hour = (timeMillis/(60*60*1000)-day*24);
		long min = ((timeMillis/(60*1000))-day*24*60-hour*60);
		long s = (timeMillis/1000-day*24*60*60-hour*60*60-min*60);
		long sss = (timeMillis-day*24*60*60*1000-hour*60*60*1000-min*60*1000-s*1000);
		return (day>0?day+",":"")+hour+":"+min+":"+s+"."+sss;
    }
	
	/**
	 * 获取两个日期之间的天数
	 * 
	 * @param before
	 * @param after
	 * @return
	 */
	public static double getDistanceOfTwoDate(Date before, Date after) {
		long beforeTime = before.getTime(); //获取的是毫秒数
		long afterTime = after.getTime();
		return (afterTime - beforeTime) / (1000 * 60 * 60 * 24);
	}
	
	/**
	 * 根据参数date加上整数参数days可能向前或向后的日期
	 * 
	 * @param before
	 * @param after
	 * @return
	 */
	public static Date getNextDay(Date date, final int days) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DAY_OF_MONTH, days);//+1今天的时间加一天
		date = calendar.getTime();
		return date;
	}
	
	public static Date getNextMonth(Date date, final int month) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, month);
		date = calendar.getTime();
		return date;
	}
	
	public static Date getNextYear(Date date, final int year) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.YEAR, year);//+1今天的时间加一年
		date = calendar.getTime();
		return date;
	}

	public static Date getNextSecond(Date date, final int second) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.SECOND, second);
		date = calendar.getTime();
		return date;
	}
	
	/**
     * 根据参数date加上整数参数days可能向前或向后的日期
     * 
     * @param before
     * @param after
     * @return
     */
	public static Date getDayTime0(Date date) {
        String d = formatDate(date, "yyyy-MM-dd") + " 00:00:00";
        return parseDate(d);
    }

	public static Date getDayTimeLast(Date date) {
		String d = formatDate(date, "yyyy-MM-dd") + " 23:59:59";
		return parseDate(d);
	}

	/**以date1为准比较两个日期的大小(返回负数表示date1比date2小;返回正整数则为相反;0为相等)
	 * @param date1
	 * @param date2
	 * @return
	 */
	public static int compareDays(final Date date1, final Date date2){
	    return date1.compareTo(date2);
	}
	
	public static int getMonthLashDay() {
		// 获取Calendar
		Calendar calendar = Calendar.getInstance();
		// 设置时间,当前时间不用设置
		// calendar.setTime(new Date());
		// 设置日期为本月最大日期
		calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));
		
		// 打印
		DateFormat format = new SimpleDateFormat("dd");
		return Integer.parseInt(format.format(calendar.getTime()));
	}
	
	public static int getMonthLashDay(Date d) {
		 // 获取Calendar
        Calendar calendar = Calendar.getInstance();
        // 设置时间,当前时间不用设置
        calendar.setTime(d);
        // 设置日期为本月最大日期
        calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DATE));

        // 打印
        DateFormat format = new SimpleDateFormat("dd");
        return Integer.parseInt(format.format(calendar.getTime()));
	}
	
	 /**
     * 判断当天是否为本月第一天
     * 
     * @return
     */
    public static boolean isFirstDayOfMonth() {
        boolean flag = false;
        Calendar calendar = Calendar.getInstance();
        int today = calendar.get(calendar.DAY_OF_MONTH);
        if (1 == today) {
            flag = true;
        }
        return flag;
    }

    /**
     * 获取当前月份最后一天
     * 
     * @param date
     * @return
     * @throws ParseException
     */
    public static String getMaxMonthDate() {
        SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        // calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return dft.format(calendar.getTime());
    }
    
    /**
     * 获取任意时间的月的最后一天
     * 描述:<描述函数实现的功能>.
     * @param repeatDate
     * @return
     */
    public static String getMaxMonthDate(String repeatDate) {
    	SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
    	Calendar calendar = Calendar.getInstance();
    	try {
    		if(StringUtils.isNotBlank(repeatDate) && !"null".equals(repeatDate)){
    			calendar.setTime(dft.parse(repeatDate));
    		}
    	} catch (ParseException e) {
    		e.printStackTrace();
    	}
    	// calendar.add(Calendar.MONTH, -1);
    	calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
    	return dft.format(calendar.getTime());
    }
    
    /**
     * 获取任意时间的月的最后一天
     * 描述:<描述函数实现的功能>.
     * @param repeatDate
     * @return
     */
    public static String getMaxMonthDate(String repeatDate, String format) {
        SimpleDateFormat dft = new SimpleDateFormat(format);
        Calendar calendar = Calendar.getInstance();
        try {
            if(StringUtils.isNotBlank(repeatDate) && !"null".equals(repeatDate)){
                calendar.setTime(dft.parse(repeatDate));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        // calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return dft.format(calendar.getTime());
    }

    
    /**
     * 获取下个月最后一天
     * 
     * @param date
     * @return
     * @throws ParseException
     */
    public static String getNextMaxMonthDate() {
    	SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(new Date());
    	calendar.add(Calendar.MONTH, 1);
    	calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
    	return dft.format(calendar.getTime());
    }
    
    /**
     * 获取当前月份最后一天
     * 
     * @param 
     * @return 返回dd
     * @throws 
     */
    public static String getMaxMonthDay() {
    	SimpleDateFormat dft = new SimpleDateFormat("dd");
    	Calendar calendar = Calendar.getInstance();
    	calendar.set(Integer.valueOf(getYear()), Integer.valueOf(getMonth()), 1);
    	calendar.add(Calendar.DATE, -1);
    	return dft.format(calendar.getTime());
    }
    
    /**
     * 
     * 获取当前月份最后一天返回Date日期
     * 
     * @param date
     * @return 返回dd
     * @throws ParseException 
     */
    public static Date getMaxMonthLastDate() {
    	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    	try {
    		return getDayTimeLast(sdf.parse(getMaxMonthDate()));
    	} catch (ParseException e) {
    		e.printStackTrace();
    		return null;
    	}
    }
    
    /**
     * 
     * 描述:获取下一个月的第一天.
     * 
     * @return
     */
    public static String getPerFirstDayOfMonth() {
        SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        return dft.format(calendar.getTime());
    }

    /**
     * 
     * 描述:获取下一个月的最后一天.
     * 
     * @return
     */
    public static String getPerLastDay(int i) {
    	Calendar cal = Calendar.getInstance();
    	cal.set(Integer.valueOf(getYear()), Integer.valueOf(getMonth()), 1);
    	cal.add(cal.MONTH, i);
    	cal.add(Calendar.DATE, -1);
    	SimpleDateFormat dft = new SimpleDateFormat("dd");
    	String preDay = dft.format(cal.getTime());
    	return preDay;
    }
    
    /**
     * 
     * 描述:获取当前月的最后一天是几号.
     * 
     * @return
     */
    public static int getCurMaxMonthDay() {
    	SimpleDateFormat dft = new SimpleDateFormat("dd");
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(new Date());
    	calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
    	return Integer.parseInt(dft.format(calendar.getTime()));
    }
    
    /**
     * 
     * 描述:获取上个月的最后一天是几号.
     * 
     * @return
     */
    public static int getLastMaxMonthDay() {
    	SimpleDateFormat dft = new SimpleDateFormat("dd");
    	Calendar calendar = Calendar.getInstance();
    	calendar.setTime(new Date());
    	calendar.add(Calendar.MONTH, -1);
    	calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
    	return Integer.parseInt(dft.format(calendar.getTime()));
    }

	public static int getMonthMaxDay(Date date) {
		SimpleDateFormat dft = new SimpleDateFormat("dd");
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		//calendar.add(Calendar.MONTH, -1);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return Integer.parseInt(dft.format(calendar.getTime()));
	}

    /**
     * 
     * 描述:获取上个月的最后一天.
     * 
     * @return
     */
    public static String getLastMaxMonthDate() {
        SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return dft.format(calendar.getTime());
    }

    /**
     * 获取上一个月
     * 
     * @return
     */
    public static String getLastMonth() {
        Calendar cal = Calendar.getInstance();
        cal.add(cal.MONTH, -1);
        SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM");
        String lastMonth = dft.format(cal.getTime());
        return lastMonth;
    }

    /**
     * 
     * 描述:获取下一个月.
     * 
     * @return
     */
    public static String getMonth(int i) {
    	Calendar cal = Calendar.getInstance();
    	cal.add(cal.MONTH, i);
    	SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM");
    	String preMonth = dft.format(cal.getTime());
    	return preMonth;
    }
    
	/**
	 * 
	 * 描述:获取下一个月.
	 * 
	 * @return
	 */
	public static String getPreMonth() {
		Calendar cal = Calendar.getInstance();
		cal.add(cal.MONTH, 1);
		SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM");
		String preMonth = dft.format(cal.getTime());
		return preMonth;
	}

	// 是否是最后一天
	public static boolean isLastDayOfMonth() {
		boolean flag = false;
		if (StringUtils.isNotBlank(getDate()) && StringUtils.isNotBlank(getMaxMonthDate())
				&& StringUtils.equals(getDate(), getMaxMonthDate())) { // getMaxMonthDate().equals(getNowTime())
			flag = true;
		}
		return flag;
	}

	/**
	 * 判断今天是否是当前月的最后一天
	 */
	public static boolean isCurrentMonthLastDay(){
		boolean bl = false;
		
		if (StringUtils.isNotBlank(getDay()) && StringUtils.isNotBlank(getMaxMonthDay())) {
			if (getDay().equals(getMaxMonthDay())) {
				bl = true;
			}
		}
		
		return bl;
	}
	
	/**
	 * 获取任意时间的下一个月 描述:<描述函数实现的功能>.
	 * 
	 * @param repeatDate
	 * @return
	 */
	public static String getPreMonth(String repeatDate) {
		String lastMonth = "";
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM");
		int year = Integer.parseInt(repeatDate.substring(0, 4));
		String monthsString = repeatDate.substring(4, 6);
		int month;
		if ("0".equals(monthsString.substring(0, 1))) {
			month = Integer.parseInt(monthsString.substring(1, 2));
		} else {
			month = Integer.parseInt(monthsString.substring(0, 2));
		}
		cal.set(year, month, Calendar.DATE);
		lastMonth = dft.format(cal.getTime());
		return lastMonth;
	}

	/**
	 * 获取任意时间的上一个月 描述
	 * 
	 * @param repeatDate
	 * @return
	 */
	public static String getLastMonth(String repeatDate) {
		String lastMonth = "";
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
		int year = Integer.parseInt(repeatDate.substring(0, 4));
		String monthsString = repeatDate.substring(4, 6);
		int month;
		if ("0".equals(monthsString.substring(0, 1))) {
			month = Integer.parseInt(monthsString.substring(1, 2));
		} else {
			month = Integer.parseInt(monthsString.substring(0, 2));
		}
		cal.set(year, month - 2, Calendar.DATE);
		lastMonth = dft.format(cal.getTime());
		return lastMonth;
	}

	/**
	 * 获取任意时间的月的最后一天 描述
	 * 
	 * @param repeatDate
	 * @return
	 */
	public static String getFinalMonthDate(String repeatDate) {
		SimpleDateFormat dft = new SimpleDateFormat("yyyy-MM-dd");
		Calendar calendar = Calendar.getInstance();
		try {
			if (StringUtils.isNotBlank(repeatDate) && !"null".equals(repeatDate)) {
				calendar.setTime(dft.parse(repeatDate));
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		// calendar.add(Calendar.MONTH, -1);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		return dft.format(calendar.getTime());
	}

	/**
	 * 获取任意时间的月第一天 描述
	 * 
	 * @param repeatDate
	 * @return
	 */
	private static String getMinMonthDate(String repeatDate) {
		SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
		Calendar calendar = Calendar.getInstance();
		try {
			if (StringUtils.isNotBlank(repeatDate) && !"null".equals(repeatDate)) {
				calendar.setTime(dft.parse(repeatDate));
			}
		} catch (ParseException e) {
			e.printStackTrace();
		}
		// calendar.add(Calendar.MONTH, -1);
		calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		return dft.format(calendar.getTime());
	}

	/**
	 * 不论是当前时间，还是历史时间 皆是时间点的前天 repeatDate 任意时间
	 */
	public static String getModify2DaysAgo(String repeatDate) {
		Calendar cal = Calendar.getInstance();
		String daysAgo = "";
		SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
		if (repeatDate == null || "".equals(repeatDate)) {
			cal.set(Calendar.DATE, cal.get(Calendar.DATE) - 2);

		} else {
			int year = Integer.parseInt(repeatDate.substring(0, 4));
			String monthsString = repeatDate.substring(4, 6);
			int month;
			if ("0".equals(monthsString.substring(0, 1))) {
				month = Integer.parseInt(monthsString.substring(1, 2));
			} else {
				month = Integer.parseInt(monthsString.substring(0, 2));
			}
			String dateString = repeatDate.substring(6, 8);
			int date;
			if ("0".equals(dateString.subSequence(0, 1))) {
				date = Integer.parseInt(dateString.substring(1, 2));
			} else {
				date = Integer.parseInt(dateString.substring(0, 2));
			}
			cal.set(year, month - 1, date - 1);
			System.out.println(dft.format(cal.getTime()));
		}
		daysAgo = dft.format(cal.getTime());
		return daysAgo;
	}

	/**
	 * 不论是当前时间，还是历史时间 皆是时间点的T-N天 repeatDate 任意时间 param 数字 可以表示前几天
	 */
	public static String getModifyNumDaysAgo(String repeatDate, int param) {
		Calendar cal = Calendar.getInstance();
		String daysAgo = "";
		SimpleDateFormat dft = new SimpleDateFormat("yyyyMMdd");
		if (repeatDate == null || "".equals(repeatDate)) {
			cal.set(Calendar.DATE, cal.get(Calendar.DATE) - param);

		} else {
			int year = Integer.parseInt(repeatDate.substring(0, 4));
			String monthsString = repeatDate.substring(4, 6);
			int month;
			if ("0".equals(monthsString.substring(0, 1))) {
				month = Integer.parseInt(monthsString.substring(1, 2));
			} else {
				month = Integer.parseInt(monthsString.substring(0, 2));
			}
			String dateString = repeatDate.substring(6, 8);
			int date;
			if ("0".equals(dateString.subSequence(0, 1))) {
				date = Integer.parseInt(dateString.substring(1, 2));
			} else {
				date = Integer.parseInt(dateString.substring(0, 2));
			}
			cal.set(year, month - 1, date - param + 1);
			System.out.println(dft.format(cal.getTime()));
		}
		daysAgo = dft.format(cal.getTime());
		return daysAgo;
	}
	
	/**
	 * 将时间转换为时间戳
	 * 
	 * @param s
	 * @return
	 * @throws ParseException
	 */
	public static String dateToStamp(String s) throws ParseException {
		String res;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = simpleDateFormat.parse(s);
		long ts = date.getTime();
		res = String.valueOf(ts);
		return res;
	}

	/**
	 * 将时间戳转换为时间
	 * 
	 * @param lt
	 * @return
	 */
	public static String stampToDate(long lt) {
		String res;
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date date = new Date(lt);
		res = simpleDateFormat.format(date);
		return res;
	}
	
	public static int[] startYearArray(final int startYear){
		final int nowYear = Integer.parseInt(DateUtils.getYear());
		int[] yearArray = null;
		if((nowYear-startYear)>=0){
			yearArray = new int[((nowYear-startYear)+1)];
			for (int i=startYear,j = 0; i <= nowYear; i++,j++) {
				yearArray[j]=i;
			}
		}
		return yearArray;
	}
	
	/**
	 * 根据当前时间推算出下一个月今天的前一天
	 * @param date 传入指定的时间，推算出下一个月今天的前一天，如果不需要传参，设置为null
	 * @param pattern 指定格式化时间的模式
	 * @param T 如果想获取String类型的日期，传入String.class ,如果想获取Date类型的日期，传入Date.class
	 */
	public static <T> T getNextMonthToDayTheDayBefore(Date date,String pattern,Class<T> T){
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		Calendar cal = null;
		
		// 获取月、日
		String month = null;
		String day = null;
		
		if (date != null && !"".equals(date.toString().trim())) {
			cal = sdf.getCalendar();
			cal.setTime(date);
			String sdfDate = sdf.format(date);
			// 获取月、日
			month = sdfDate.substring(5, 7);
			day = sdfDate.substring(8, 10);
		} else {
			cal = Calendar.getInstance();
			// 获取当前月
			month = getMonth();
			// 获取今天
			day = getDay();
		}
		
		cal.add(cal.MONTH, 1);
		
		String regex = "(29|30|31)";
		boolean regBl = Pattern.matches(regex, day);
		
		if ("01".equals(month) && regBl) {
			cal.set(cal.get(Calendar.YEAR), 2, 1);
			cal.add(Calendar.DATE,-1); // 获取2月份的最后一天
			
			if ("29".equals(String.valueOf(cal.get(Calendar.DATE))) && !("30".equals(day) || "31".equals(day))) {
				cal.add(Calendar.DATE, -1);
			}
		} else {
			cal.add(Calendar.DATE,-1);
		}
		
		if (String.class == T) {
			return (T) sdf.format(cal.getTime());
		} else {
			try {
				String d = formatDate(cal.getTime(), pattern);
				return (T) parseDate(d);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	
	/**
	 * 获取两个日期之间的天数
	 * 
	 * @param before
	 * @param after
	 * @return
	 */
	public static long getTwoDateBetweenDays(Date before, Date after){
		// 获取日期毫秒数
		long beforeTime = before.getTime();
		long afterTime = after.getTime();
		return (afterTime - beforeTime) / (24*60*60*1000);
	}
	
	/**
	 * 把一个日期范围转为Date类型
	 * @param arg
	 */
	public static Date[] toDateArray(String strDate) {
		Date[] dateArr = new Date[2];
		String[] date = strDate.trim().split(" - ");
		
		try {
			dateArr[0] = parseDate(date[0].trim(), "yyyy-MM-dd");
			dateArr[1] = parseDate(date[1].trim(), "yyyy-MM-dd");
		} catch (ParseException e) {
			e.printStackTrace();
		}
		
		return dateArr;
	}
	
	/**
	 * 把一个毫秒数转为指定格式的Date类型
	 * @param source
	 * @param pattern
	 * @return
	 */
	public static Date parse(String source, String pattern){
		SimpleDateFormat sdf = null;
		if (StringUtils.isNoneBlank(source) && StringUtils.isNoneBlank(pattern)) {
			Date date = new Date(Long.valueOf(source));
			sdf = new SimpleDateFormat(pattern);
			try {
				String formateDate = sdf.format(date);
				return sdf.parse(formateDate);
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	public static List<String> getMonthBetween(String minDate, String maxDate){
		try {
			ArrayList<String> result = new ArrayList<String>();
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");//格式化为年月

			Calendar min = Calendar.getInstance();
			Calendar max = Calendar.getInstance();

			min.setTime(sdf.parse(minDate));
			min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

			max.setTime(sdf.parse(maxDate));
			max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

			Calendar curr = min;
			while (curr.before(max)) {
				result.add(sdf.format(curr.getTime()));
				curr.add(Calendar.MONTH, 1);
			}

			return result;
		}catch (Exception e){
			e.printStackTrace();
		}
		return null;
	}

	public static  int getAge(Date birthDay){
		try {
			Calendar cal = Calendar.getInstance();
			if (cal.before(birthDay)) { //出生日期晚于当前时间，无法计算
				throw new IllegalArgumentException(
						"The birthDay is before Now.It's unbelievable!");
			}
			int yearNow = cal.get(Calendar.YEAR);  //当前年份
			int monthNow = cal.get(Calendar.MONTH);  //当前月份
			int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH); //当前日期
			cal.setTime(birthDay);
			int yearBirth = cal.get(Calendar.YEAR);
			int monthBirth = cal.get(Calendar.MONTH);
			int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
			int age = yearNow - yearBirth;   //计算整岁数
			if (monthNow <= monthBirth) {
				if (monthNow == monthBirth) {
					if (dayOfMonthNow < dayOfMonthBirth) age--;//当前日期在生日之前，年龄减一
				}else{
					age--;//当前月份在生日之前，年龄减一
				}
			}
			return age;
		}catch (Exception e){
			e.printStackTrace();
			return 0;
		}
	}

	public static  int getAge(Date birthDay,Date date) throws Exception {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		if (cal.before(birthDay)) { //出生日期晚于当前时间，无法计算
			throw new IllegalArgumentException(
					"The birthDay is before Now.It's unbelievable!");
		}
		int yearNow = cal.get(Calendar.YEAR);  //当前年份
		int monthNow = cal.get(Calendar.MONTH);  //当前月份
		int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH); //当前日期
		cal.setTime(birthDay);
		int yearBirth = cal.get(Calendar.YEAR);
		int monthBirth = cal.get(Calendar.MONTH);
		int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);
		int age = yearNow - yearBirth;   //计算整岁数
		if (monthNow <= monthBirth) {
			if (monthNow == monthBirth) {
				if (dayOfMonthNow < dayOfMonthBirth) age--;//当前日期在生日之前，年龄减一
			}else{
				age--;//当前月份在生日之前，年龄减一
			}
		}
		return age;
	}


	public static Map<String,Integer> getDateDiff(String startDate, String endDate) {
		Map<String,Integer> map=new HashMap<>();
		map.put("month",0);
		map.put("day",0);
		if (startDate == null) {
			return map;
		}

		String[] data = startDate.split("-");

		if (data.length < 3) {
			return map;
		}
		Calendar birthday = new GregorianCalendar(Integer.valueOf(data[0]), Integer.valueOf(data[1]), Integer.valueOf(data[2]));
		Calendar now = Calendar.getInstance();
		now.setTime(DateUtils.parseDate(endDate));

		int day = now.get(Calendar.DAY_OF_MONTH) - birthday.get(Calendar.DAY_OF_MONTH);
		//月份从0开始计算，所以需要+1
		int month = now.get(Calendar.MONTH)+1  - birthday.get(Calendar.MONTH);
		int year = now.get(Calendar.YEAR) - birthday.get(Calendar.YEAR);
		//按照减法原理，先day相减，不够向month借；然后month相减，不够向year借；最后year相减。
		if (day < 0) {
			month -= 1;
			now.add(Calendar.MONTH, -1);//得到上一个月，用来得到上个月的天数。
			day = day + now.getActualMaximum(Calendar.DAY_OF_MONTH);
		}
		if (month < 0) {
			month = (month + 12) % 12;
			year--;
		}
		StringBuffer tag = new StringBuffer();

		int yearMonth=0;
		if (year > 0) {
			//tag.append(year + "岁");
			yearMonth=year*12;
		}
		if (month > 0) {
			tag.append(month+yearMonth + "月");
			map.put("month",month+yearMonth);
		}
		if (day > 0) {
			tag.append(day + "天");
			map.put("day",day);
		}

		return map;

	}

	public static Map<String,Integer> getDateDiffYear(String startDate, String endDate) {
		Map<String,Integer> map=new HashMap<>();
		map.put("month",0);
		map.put("day",0);
		map.put("year",0);
		if (startDate == null) {
			return map;
		}

		String[] data = startDate.split("-");

		if (data.length < 3) {
			return map;
		}
		Calendar birthday = new GregorianCalendar(Integer.valueOf(data[0]), Integer.valueOf(data[1]), Integer.valueOf(data[2]));
		Calendar now = Calendar.getInstance();
		now.setTime(DateUtils.parseDate(endDate));

		int day = now.get(Calendar.DAY_OF_MONTH) - birthday.get(Calendar.DAY_OF_MONTH);
		//月份从0开始计算，所以需要+1
		int month = now.get(Calendar.MONTH)+1  - birthday.get(Calendar.MONTH);
		int year = now.get(Calendar.YEAR) - birthday.get(Calendar.YEAR);
		//按照减法原理，先day相减，不够向month借；然后month相减，不够向year借；最后year相减。
		if (day < 0) {
			month -= 1;
			now.add(Calendar.MONTH, -1);//得到上一个月，用来得到上个月的天数。
			day = day + now.getActualMaximum(Calendar.DAY_OF_MONTH);
		}
		if (month < 0) {
			month = (month + 12) % 12;
			year--;
		}
		StringBuffer tag = new StringBuffer();

		int yearMonth=0;

		if (year > 0) {
			//tag.append(year + "岁");
			//yearMonth=year*12;
			tag.append(year + "年");
			map.put("year",year);
		}
		if (month > 0) {
			tag.append(month + "月");
			map.put("month",month);
		}
		if (day > 0) {
			tag.append(day + "天");
			map.put("day",day);
		}

		return map;

	}

	/**
	 * 判断多个时间段是否有重叠，true是，false否
	 * @param dateList
	 * @return
	 */
	public static boolean timePeriodIsOverlap(List<Map<String,Date>> dateList){

		try {
			//对数据排序，开始时间从小到大
			Collections.sort(dateList, new Comparator<Map<String, Date>>() {
				@Override
				public int compare(Map<String, Date> u1, Map<String, Date> u2) {
					long diff = u1.get("startDate").getTime() - u2.get("startDate").getTime();
					if (diff > 0) {
						return 1;
					} else if (diff < 0) {
						return -1;
					}
					return 0; //相等为0
				}
			});

			for(int i =0;i<dateList.size();i++){

				Date startDate1 = dateList.get(i).get("startDate");
				Date endDate1 =  dateList.get(i).get("endDate");
				// 不让自己和自己进行比较，j从i的下一个开始
				for (int j =i+1;j<dateList.size();j++){
					Date startDate2 = dateList.get(j).get("startDate");
					Date endDate2 = dateList.get(j).get("endDate");
					if(startDate1.before(startDate2) && endDate1.after(startDate2)){
						return true;
					}
					if(startDate1.getTime()==startDate2.getTime() && endDate1.getTime()==endDate2.getTime()){
						return true;
					}
				}
			}
			return false;
		}catch (Exception e){
			return true;
		}
	}


	/**
	 * 获取两个时间相差多少分钟，忽略秒数
	 * @param maxTime
	 * @param minTime
	 * @return
	 */
	public static int getTimeDiffMinute(Date maxTime,Date minTime){
		if(maxTime==null || minTime==null){
			return 0;
		}
		Calendar nowCalendar=Calendar.getInstance();
		nowCalendar.setTime(minTime);
		nowCalendar.set(Calendar.SECOND,0);
		long nd = 1000 * 24 * 60 * 60;
		long nh = 1000 * 60 * 60;
		long nm = 1000 * 60;
		//long ns = 1000;
		// 获得两个时间的毫秒时间差异
		long diff = maxTime.getTime() - nowCalendar.getTime().getTime();
		// 计算差多少天
		long day = diff / nd;
		// 计算差多少小时
		long hour = diff % nd / nh;
		// 计算差多少分钟
		long min = diff % nd % nh / nm;
		// 计算差多少秒//输出结果
		//long sec = diff % nd % nh % nm / ns;
		return (int)((day*24*60)+(hour*60)+ min);
	}

	public static String secondToTime(long second){
		long days = second / 86400;            //转换天数
		second = second % 86400;            //剩余秒数
		long hours = second / 3600;            //转换小时
		second = second % 3600;                //剩余秒数
		long minutes = second /60;            //转换分钟
		second = second % 60;                //剩余秒数
		if(days>0){
			return (days*24) + hours + "小时" + minutes + "分";
		}else if(hours>0){
			return hours + "小时" + minutes + "分";
		}else{
			return minutes + "分";
		}
	}

	public static String secondToTime(long second,long workSecond){
		if(second==0){
			return "0";
		}
		long days = second / workSecond;            //转换天数
		second = second % workSecond;            //剩余秒数
		long hours = second / 3600;            //转换小时
		second = second % 3600;                //剩余秒数
		long minutes = second /60;            //转换分钟
		second = second % 60;                //剩余秒数
		/*if(days!=0){
			return days+"天" + hours + "小时" + minutes + "分";
		}else if(hours!=0){
			return hours + "小时" + minutes + "分";
		}else if(minutes!=0){
			return minutes + "分";
		}else{
			return "0";
		}*/
		if(days>0 || hours>0 || minutes>0){
			String str="";
			if(days>0){
				str+=days+"天";
			}
			if(hours>0){
				str+=hours+"小时";
			}
			if(minutes>0){
				str+=minutes+"分钟";
			}
			return str;
		}else{
			return "0";
		}
	}

	public static String[] secondToTimeArray(long second,long workSecond){
		if(second==0){
			return new String[]{"0","0"};
		}
		long days = second / workSecond;            //转换天数
		second = second % workSecond;            //剩余秒数
		long hours = second / 3600;            //转换小时
		second = second % 3600;                //剩余秒数
		long minutes = second /60;            //转换分钟
		second = second % 60;                //剩余秒数

		if(days!=0 || hours!=0 || minutes!=0){
			String str="";
			if(days!=0){
				str+=days+"天";
			}
			if(hours!=0){
				str+=hours+"小时";
			}
			double minutesDouble=0.0;
			if(minutes!=0){
				minutesDouble=BigDecimal.valueOf(minutes).divide(BigDecimal.valueOf(60),2,BigDecimal.ROUND_HALF_UP).doubleValue();
			}
			if(minutesDouble!=0){
				double hoursDouble=hours+minutesDouble;
				return new String[]{days+"",hoursDouble+""};
			}else{
				return new String[]{days+"",hours+""};
			}



		}else{
			return new String[]{"0","0"};
		}
	}

	public static String secondToTimeArrayToStr(long second,long workSecond){
		String[] strings = secondToTimeArray(second, workSecond);
		int day=Integer.valueOf(strings[0]);
		double hour=Double.valueOf(strings[1]);
		if(day==0 && hour==0){
			return "0";
		}

		String str="";
		if(day<0 || hour<0){
			str+="-";
		}
		if(day!=0){
			str+=Math.abs(day)+"天";
		}
		if(hour!=0){
			str+=Math.abs(hour)+"小时";
		}
		return str;
	}

	class ClassZ{
		private String[] strArray;
	}

	public static void main(String[] args) throws Exception {

		estt2(4,5,"1","0",5);
	}

	/**
	 *
	 * @param ruzhurenshu 入住人数
	 * @param roomTotalBedNum 房间总床位数
	 * @param isPrivateRoom 是否包房
	 * @param isBigBedRoom 是否大床房间
	 * @param privateRoomDays 包房扣卡天数
	 */
	public static void estt2(int ruzhurenshu,int roomTotalBedNum,String isPrivateRoom,String isBigBedRoom,int privateRoomDays){

		for (int i = 0; i < ruzhurenshu; i++) {
			double totalBeds=0.0;
			boolean isConfigDays=false;
			if(ruzhurenshu>=roomTotalBedNum){
				if(i>=roomTotalBedNum){
					//包房超出床位数的随行扣费
					totalBeds=1.0;
				}else{
					int currUseBedNum=1;
					if("1".equals(isPrivateRoom) && "1".equals(isBigBedRoom)){
						currUseBedNum=2;
					}
					//房间扣费 除 房间总床位数（大床=2）*当前使用床位数
                        /*totalBeds=BigDecimal.valueOf(privateRoomDays).divide(BigDecimal.valueOf(roomTotalBedNum),1,BigDecimal.ROUND_DOWN)
                                .multiply(BigDecimal.valueOf(currUseBedNum)).doubleValue();*/
					totalBeds=BigDecimal.valueOf(privateRoomDays).divide(BigDecimal.valueOf(roomTotalBedNum),1,BigDecimal.ROUND_DOWN)
							.multiply(BigDecimal.valueOf(1)).doubleValue();
				}
			}else{
				if(!isConfigDays){
					//包房
					int bedUnitPrice=(int)roomTotalBedNum/ruzhurenshu;
					int bedUnitPriceSurplus=(int)roomTotalBedNum%ruzhurenshu;
					int useBedUnitPrice=bedUnitPrice;
					if(i<bedUnitPriceSurplus && bedUnitPriceSurplus>0){
						useBedUnitPrice+=1;
					}
					totalBeds=BigDecimal.valueOf(privateRoomDays).divide(BigDecimal.valueOf(roomTotalBedNum),2,BigDecimal.ROUND_DOWN)
							.multiply(BigDecimal.valueOf(useBedUnitPrice)).doubleValue();
				}else{
					//不包房
					totalBeds=privateRoomDays;
				}
			}
			System.out.println(totalBeds);
		}


	}

	public static Date getMiddleDate(Date startDate,Date endDate){
		long diff=(endDate.getTime()-startDate.getTime())/2;
		return new Date(startDate.getTime()+diff);
	}

}
