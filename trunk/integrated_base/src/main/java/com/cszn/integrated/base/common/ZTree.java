package com.cszn.integrated.base.common;

import com.jfinal.plugin.activerecord.Record;

import java.util.List;

/**
 * ztree 基本model
 * 
 * <AUTHOR>
 * 
 */
public class ZTree implements java.io.Serializable {

	private static final long serialVersionUID = 6625307952110627894L;
	private String id;
	private String pId;
	private String pIds;
	private String name;
	private String type;
	private String url;
	private String permission;
	private String stockTypeNo;
	private String isEnabled;
	private String description;
	private int sort;
	private boolean open = true;
	private boolean checked = false;
	private boolean chkDisabled = false;
	private Record record;
	private boolean lay_is_open;
	private List<ZTree> children;

	public ZTree(String id, String name, String type, String pid, String pids) {
		this.id = id;
		this.name = name;
		this.type = type;
		this.pId = pid;
		this.pIds = pids;
	}

	public ZTree(String id, String name, String type, String pid, String pids,Record record) {
		this.id = id;
		this.name = name;
		this.type = type;
		this.pId = pid;
		this.pIds = pids;
		this.record=record;
	}
	
	public ZTree(String id, String name, String pid, String type, String url, String permission, int sort) {
		this.id = id;
		this.name = name;
		this.pId = pid;
		this.type = type;
		this.url = url;
		this.permission = permission;
		this.sort = sort;
	}
	
	public ZTree(String id, String name, String pid, String stockTypeNo, String isEnabled, String description) {
		this.id = id;
		this.name = name;
		this.pId = pid;
		this.stockTypeNo = stockTypeNo;
		this.isEnabled = isEnabled;
		this.description = description;
	}

	public void setDisCheck(boolean b) {
		if (b) {
			checked = true;
			chkDisabled = true;
		} else {
			chkDisabled = false;
			checked = false;
		}
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getpId() {
		return pId;
	}

	public void setpId(String pId) {
		this.pId = pId;
	}

	public String getpIds() {
		return pIds;
	}

	public void setpIds(String pIds) {
		this.pIds = pIds;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getPermission() {
		return permission;
	}

	public void setPermission(String permission) {
		this.permission = permission;
	}

	public String getStockTypeNo() {
		return stockTypeNo;
	}

	public void setStockTypeNo(String stockTypeNo) {
		this.stockTypeNo = stockTypeNo;
	}

	public String getIsEnabled() {
		return isEnabled;
	}

	public void setIsEnabled(String isEnabled) {
		this.isEnabled = isEnabled;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public int getSort() {
		return sort;
	}

	public void setSort(int sort) {
		this.sort = sort;
	}

	public boolean isOpen() {
		return open;
	}

	public void setOpen(boolean open) {
		this.open = open;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	public boolean isChkDisabled() {
		return chkDisabled;
	}

	public void setChkDisabled(boolean chkDisabled) {
		this.chkDisabled = chkDisabled;
	}

	public Record getRecord() {
		return record;
	}

	public void setRecord(Record record) {
		this.record = record;
	}

	public void setLay_is_open(boolean lay_is_open) {
		this.lay_is_open = lay_is_open;
	}

	public boolean isLay_is_open() {
		return lay_is_open;
	}

	public List<ZTree> getChildren() {
		return children;
	}

	public void setChildren(List<ZTree> children) {
		this.children = children;
	}
}
