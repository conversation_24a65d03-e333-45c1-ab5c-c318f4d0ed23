#---------------------------------------------------------------------------------#
# Generator Config
# jboot.admin.service.ge.entity.package: the entity package;
# jboot.admin.service.ge.servicepackage\uff1aservice \u63a5\u53e3 package
# jboot.admin.service.ge.localdev.removedtablenameprefixes: \u9700\u8981\u79fb\u9664\u8868\u540d\u524d\u7f00\u53ea\u7559\u4e0b\u540e\u90e8\u5206\uff0c\u591a\u4e2a\u9017\u53f7\u9694\u5f00
# jboot.admin.service.ge.localdev.excludedtable: \u751f\u6210\u65f6\u4e0d\u5305\u542b\u8868\u540d\u5217\u8868\uff0c\u591a\u4e2a\u9017\u53f7\u9694\u5f00
# jboot.admin.service.ge.excludedtableprefixes: \u751f\u6210\u65f6\u4e0d\u5305\u542b\u8868\u524d\u7f00\uff0c\u591a\u4e2a\u9017\u53f7\u9694\u5f00
#---------------------------------------------------------------------------------#
jboot.admin.service.ge.modelpackage=com.cszn.integrated.service.entity.crm
jboot.admin.service.ge.servicepackage=com.cszn.integrated.service.api.crm
jboot.admin.service.ge.removedtablenameprefixes=
jboot.admin.service.ge.excludedtable=
jboot.admin.service.ge.excludedtableprefixes=cfs_,equi_,fina_,main_,mall_,mms_,msg_,pers_,qiye_,sms_,srm_,sys_,wms_

#---------------------------------------------------------------------------------#
jboot.datasource.type=mysql
jboot.datasource.url=****************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
#---------------------------------------------------------------------------------#