package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersAgencyInfo;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface PersAgencyInfoService {

    public PersAgencyInfo findById(Object id);

    boolean saveAgencyInfo(PersAgencyInfo AgencyInfo);

    Page<Record> agencyInfoPageList(int page, int limit, PersAgencyInfo agencyInfo);
}
