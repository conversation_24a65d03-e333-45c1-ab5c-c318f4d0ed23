package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBookkeepingTypes;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBookkeepingTypesService {

    public boolean saveType(MainBookkeepingTypes type,String userId);

    public Page<MainBookkeepingTypes> pageList(Integer pageNumber,Integer pageSize,MainBookkeepingTypes type);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBookkeepingTypes findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBookkeepingTypes
     */
    public List<MainBookkeepingTypes> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBookkeepingTypes model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBookkeepingTypes model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBookkeepingTypes model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBookkeepingTypes model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBookkeepingTypes> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBookkeepingTypes> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBookkeepingTypes> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
