package com.cszn.integrated.service.api.pers;


import java.util.List;

import com.cszn.integrated.service.entity.pers.PersNotice;
import com.cszn.integrated.service.entity.pers.PersNoticeRelation;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;

public interface PersNoticeService  {

    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param notice
     * @return
     */
    public Page<Record> lookNoticePage(Integer pageNumber, Integer pageSize, PersNotice model, String userId);
    
    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param notice
     * @return
     */
    public Page<PersNotice> findNoticePage(Integer pageNumber, Integer pageSize, PersNotice model);


    /**
     * find all model
     *
     * @return all <PersNotice
     */
    public Page<Record> lookNamePageList(Integer pageNumber, Integer pageSize, String noticeId, String isRead);
    
    
    /**
     * find all model
     *
     * @return all <PersNotice
     */
    public List<Record> noticeUserList(String noticeId);

    /**
     * 新增公告
     * @param notice
     * @param fileUrls
     * @param receiverList
     * @return
     */
    public String saveNotice(PersNotice notice, List<String> fileUrls,List<String> fileIdList,List<PersNoticeRelation> relationList,String userId);


    /**
     * 删除公告
     * @param id
     * @param userId
     * @return
     */
    public boolean delNotice(String id,String userId);


    /**
     * 根据id获取公告对象
     * @param id
     * @return
     */
    public PersNotice get(String id);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersNotice findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNotice
     */
    public List<PersNotice> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersNotice model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersNotice model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersNotice model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersNotice model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersNotice> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersNotice> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersNotice> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}