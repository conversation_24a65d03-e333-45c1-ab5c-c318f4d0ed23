package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallProduct;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface MallProductService {


    public Record getProductById(String id);

    public Page<Record> getProductByCategory(int pageNumber,int pageSize,String categoryId);

    public boolean saveProduct(MallProduct mallProduct,String userId);

    public Page<Record> pageList(int pageNumber,int pageSize,MallProduct mallProduct);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallProduct findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallProduct> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallProduct model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallProduct model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallProduct model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallProduct model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);

}
