package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeePerformance;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeSalary;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeSalaryService {


    public PersOrgEmployeeSalary getEmpSalaryByYearMonth(String empId,String yearMonth);

    public List<PersOrgEmployeeSalary> genEmployeeSalary(List<PersOrgEmployeePerformance> performanceList,String userId) throws Exception;

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeSalary findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeSalary> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeSalary model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeSalary model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeSalary model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeSalary model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeSalary> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeSalary> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeSalary> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);





}
