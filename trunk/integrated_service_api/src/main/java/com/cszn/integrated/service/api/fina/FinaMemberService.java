package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsMember;

import java.util.List;

public interface FinaMemberService {


    /**
     * 根据姓名身份证查询分页对象
     * @param pageNumber
     * @param pageSize
     * @param fullName
     * @param idcard
     * @return
     */
    public Page<MmsMember> findList(int pageNumber, int pageSize,String fullName,String idcard);



    /**
     * 删除对象
     * @param id
     * @return
     */
    public boolean delMmsMember(String id,String userId);


    /**
     * 新增对象
     * @param member
     * @return
     */
    public String saveMmsMember(MmsMember member,String userId);




    /**
     * 根据身份证查询对象
     * @param idcard
     * @return
     */
    public MmsMember getMemberByIdcard(String idcard);


    /**
     * 根据id查询对象
     * @param id
     * @return
     */
    public MmsMember get(String id);


    /**
     * 根据用户id查询List
     * @param userId
     * @return
     */
    public List<FinaMembershipCard> getCardsByMember(String userId);


    /**
     * 根据卡号和身份证查询会员档案
     * @param cardName
     * @param idcard
     * @return
     */
    public MmsMember getMemberByNameAndIdcard(String cardName,String idcard);

    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelMember(List<MmsMember> list,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMember findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMember
     */
    public List<MmsMember> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMember model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public boolean save(MmsMember model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public boolean saveOrUpdate(MmsMember model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMember model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);


    public void join(Page<? extends Model> page, String joinOnField);

    public void join(Page<? extends Model> page, String joinOnField, String[] attrs);

    public void join(Page<? extends Model> page, String joinOnField, String joinName);

    public void join(Page<? extends Model> page, String joinOnField, String joinName, String[] attrs);

    public void join(List<? extends Model> models, String joinOnField);

    public void join(List<? extends Model> models, String joinOnField, String[] attrs);

    public void join(List<? extends Model> models, String joinOnField, String joinName);

    public void join(List<? extends Model> models, String joinOnField, String joinName, String[] attrs);

    public void join(Model model, String joinOnField);

    public void join(Model model, String joinOnField, String[] attrs);

    public void join(Model model, String joinOnField, String joinName);

    public void join(Model model, String joinOnField, String joinName, String[] attrs);

    public void keep(Model model, String... attrs);

    public void keep(List<? extends Model> models, String... attrs);

}