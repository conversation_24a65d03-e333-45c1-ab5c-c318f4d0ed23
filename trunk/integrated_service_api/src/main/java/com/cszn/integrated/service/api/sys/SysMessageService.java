package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.sys.SysMessage;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface SysMessageService  {
	
    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SysMessage> paginate(SysMessage model, int page, int pageSize);
    
    /**
     * 批量标记已读
     * @param List<SysMessage> msgList
     * @param userId
     * @return
     */
    public boolean signRead(List<SysMessage> msgList, String userId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public SysMessage findById(Object id);


    /**
     * find all model
     *
     * @return all <SysMessage
     */
    public List<SysMessage> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(SysMessage model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(SysMessage model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(SysMessage model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(SysMessage model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SysMessage> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<SysMessage> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<SysMessage> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}