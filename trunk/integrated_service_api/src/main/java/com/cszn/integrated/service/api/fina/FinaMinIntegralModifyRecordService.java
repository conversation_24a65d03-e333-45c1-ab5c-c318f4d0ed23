package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaMinIntegralModifyRecord;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaMinIntegralModifyRecordService {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMinIntegralModifyRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordTourist
     */
    public List<FinaMinIntegralModifyRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMinIntegralModifyRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaMinIntegralModifyRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaMinIntegralModifyRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMinIntegralModifyRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaMinIntegralModifyRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaMinIntegralModifyRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaMinIntegralModifyRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
