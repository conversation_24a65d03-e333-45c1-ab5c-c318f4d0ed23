package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardTransferDetail;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardTransferDetailService {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardTransferDetail findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FinaCardTransferDetail
     */
    public List<FinaCardTransferDetail> findList(String transferId);


    /**
     * find all model
     *
     * @return all <FoodInfo
     */
    public List<FinaCardTransferDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardTransferDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardTransferDetail model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardTransferDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardTransferDetail model);

    
    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardTransferDetail> paginate(int page, int pageSize);
    
    
    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardTransferDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardTransferDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}