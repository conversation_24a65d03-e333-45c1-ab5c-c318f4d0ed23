package com.cszn.integrated.service.api.pers;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.pers.PersEmpUser;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersEmpUserService  {


    public String getEmpIdByUserId(String userId);
    
    public String getDeptIdByUserId(String userId);
    
    public String getDeptNameByUserId(String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersEmpUser findById(Object id);


    /**
     * find all model
     *
     * @return all <PersEmpUser
     */
    public List<PersEmpUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersEmpUser model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersEmpUser model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersEmpUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersEmpUser model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersEmpUser> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersEmpUser> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersEmpUser> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}