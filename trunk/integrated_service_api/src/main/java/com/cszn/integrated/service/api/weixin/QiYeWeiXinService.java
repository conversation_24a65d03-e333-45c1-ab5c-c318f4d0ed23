package com.cszn.integrated.service.api.weixin;

import com.cszn.integrated.service.entity.weixin.Department;
import com.cszn.integrated.service.entity.weixin.QiYeUser;

import java.util.List;
import java.util.Map;

public interface QiYeWeiXinService {


    public List<String> test2();

    public List<String> test();

    public String getCheckinAccessToken();

    public String getCheckinData(Object[] qiyeIds,long StartTime,long endTime);

    public String getCheckinMonthData(Object[] qiyeIds,long startTime,long endTime);

    public Map<String,Object> getCheckinOption(String qiyeUserId, long dateTime);

    public String getCheckinDayData(Object[] qiyeIds,long startTime,long endTime);

    public boolean updateUserDept(QiYeUser qiYeUser);

    public String getCorpsecretByAgentId(String agentId);

    /**
     * 获取企业微信accessToken
     * 如果返回null则是获取失败
     * @return
     */
    public String getAccessToken();

    /**
     * 通过corpsecret获取企业微信token
     * @param corpsecret
     * @return
     */
    public String getAccessTokenByCorpsecret(String corpsecret);

    /**
     * 创建部门
     * @param department
     * @return
     */
    public Integer createDepartment(Department department);


    /**
     * 更新部门
     * @param department
     * @return
     */
    public boolean updateDepartment(Department department);


    /**
     * 删除部门
     * @param id
     * @return
     */
    public boolean deleteDepartment(Integer id);


    /**
     * 获取user
     * @param userId
     * @return
     */
    public String getUser(String userId);


    /**
     * 创建user
     * @param qiYeUser
     * @return
     */
    public boolean createUser(QiYeUser qiYeUser);


    /**
     * 修改user
     * @param qiYeUser
     * @return
     */
    public boolean updateUser(QiYeUser qiYeUser);

    /**
     * 删除user
     * @param userId
     * @return
     */
    public boolean delUser(String userId);

    public Map<String,Object> createChat(String deptId);

    public Map<String,Object> updateChat(String chatid,String name,String owner,List<String> addUserIdList,List<String> delUserIdList);
}
