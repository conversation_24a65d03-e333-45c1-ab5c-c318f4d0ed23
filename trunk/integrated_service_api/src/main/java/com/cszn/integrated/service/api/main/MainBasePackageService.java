package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.main.MainBasePackage;
import com.cszn.integrated.service.entity.main.MainBasePackageFood;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBasePackageService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainBasePackage> paginateByCondition(MainBasePackage model, int pageNumber, int pageSize);
    
    
    /**
     * find all model
     *
     * @return all <MainBasePackage
     */
    public List<MainBasePackage> findListByBaseId(String baseId);
    
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBasePackage findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBasePackage
     */
    public List<MainBasePackage> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBasePackage model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBasePackage model);

    /**
     * 保存信息
     * @param model
     * @return
     */
    public boolean savePackage(MainBasePackage model, List<MainBasePackageFood> foodList, List<CfsFileUpload> fileList);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBasePackage model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBasePackage model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBasePackage> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBasePackage> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBasePackage> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}