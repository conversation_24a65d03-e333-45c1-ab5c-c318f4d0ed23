package com.cszn.integrated.service.api.fina;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FinaExpenseRecordService  {

    public List<FinaExpenseRecord> findListBySql(String sql,Object... params);

    /**
     * 月扣费卡旅居预定 扣费总费用 以及每月费用
     * @param scheme
     * @param startTime
     * @param endTime
     * @param isPrivateRoom
     * @return
     */
    public Map<String,Object> getAmountCardDeductDetail(MainCardDeductScheme scheme,Date startTime,Date endTime,String isPrivateRoom,String idCard,double dayTimes,int checkinNum,String bedId,boolean isConfigDay);

    /**
     * 已结算
     * @param baseId
     * @param yearMonth
     * @return
     */
    public List<Record> monthSettledRecordList(String baseId,String yearMonth);

    /**
     * 未结算
     * @param baseId
     * @param startDate
     * @param endDate
     * @param isSettle
     * @return
     */
    public List<Record> monthUnsettleRecordList(String baseId,String startDate,String endDate,String isSettle);

    public List<FinaExpenseRecord> getFollowRecordListByMainId(String erId);

    public List<Record> findCardTypeYearSummary(String year);

    public List<Record> findBaseYearSummary(String year);

    public List<Record> findBaseMonthSummary(String month);

    public Record findCheckoutCount(String baseId,String date);

    public Page<Record> findDeductionDetailPage(int pageNumber,int pageSize,String baseId,String startDate,String endDate);

    public FinaExpenseRecord findMainRecordByBookNo(String bookNo);

    public FinaExpenseRecord findMainRecordByCheckinNo(String checkinNo);

    public Map<String,Object> newCheckout(String appNo,String baseId,String checkoutData);

    public void genBookDetail(int pageNumber,int pageSize);

    public void genTouristDetail(int pageNumber,int pageSize);

    public void genCheckinSettleDetail(int pageNumber,int pageSize);

    public void genCheckoutSettleDetail(int pageNumber,int pageSize);

    public Map<String,Object> updateCard(FinaExpenseRecord record,String uniqueId,JSONArray jsonArray);

    public Map<String,Object> updateCard2(FinaExpenseRecord record,String uniqueId,JSONArray jsonArray);
    
    public Map<String,Object> upgradeCardReplace(FinaExpenseRecord record,String uniqueId,JSONArray jsonArray);

    public List<FinaExpenseRecord> getFollowListByCheckinNoPart(String appNo, String baseId,String uniqueId, String checkinNo, String name, String idCard);

    /**
     * 根据cardNumber查询帐单列表
     * @param cardNumber
     * @return
     */
    public List<FinaExpenseRecord> findByCardNumber(String checkinStatus, Date settleDate, String cardNumber);
    
    /**
     *
     * @param pageNumber
     * @param pageSize
     * @param cardNumber
     * @param fullName
     * @return
     */
    public Page<Record> findCardExpenseRecordCount(int pageNumber,int pageSize,String cardNumber,String fullName);

    /**
     * 续订入住
     * @return
     */
    public Map<String,Object> renewBookCheckin(String checkinNo,String bookNo,String checkinDate,String checkoutDate,String followIds);

    /**
     * 取消续订
     * @param bookNo
     * @param followIds
     * @return
     */
    public Map<String,Object> cancelRenewBook(String bookNo,String followIds);




    /**
     * 续订
     * @param bookNo
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<String,Object> renewBook(String parentBookNo,String parentCheckinNo,String bookNo,String startDate,String endDate,String followBookNos);

    /**
     * 消费主记录分页查询
     * @param pageNumber
     * @param pageSize
     * @param er
     * @return
     */
    public Page<Record> findList(Integer pageNumber, Integer pageSize, String fullName,FinaExpenseRecord er,String followName,String notSettle);



    /**
     *作废
     * @param id
     * @param userId
     * @return
     */
    public Map<String,Object> delExpenseRecord(String id,String userId);



    /**
     * 通过主记录对象查询出主记录及其子记录
     * @param er
     * @return
     */
    public List<Record> getRecordsByParent(FinaExpenseRecord er);



    /**
     * 获取主记录下的消费明细
     * @param id
     * @return
     */
    public List<Record> getDetailByParent(String id);



    /**
     * 获取主记录使用的会员卡的所有消费记录
     * @param er
     * @return
     */
    public List<Record> getDeductCardTotalByParent(FinaExpenseRecord er);



    /**
     *获取主记录的续住记录
     * @return
     */
    public List<Record> getStayinRecord(FinaExpenseRecord er);




    /**
     * 获取会员卡变更记录
     * @param er
     * @return
     */
    public List<Record> getChangeCardRecord(FinaExpenseRecord er);




    /**
     * 获取随行人员记录
     * @param er
     * @return
     */
    public List<Record> getFollowRecord(FinaExpenseRecord er);



    /**
     * 获取随行人员的离开记录
     * @param id
     * @return
     */
    public List<Record> getFollowLeaveRecord(String id);


    /**
     * 根据旅游团编号获取旅游账单list
     * @param er
     * @return
     */
    public List<Record> getTouristExpenseList(FinaExpenseRecord er,String fullName);




    /**
     * 旅居账单扣卡结算：退住结算
     * @param settleList
     * @param userId
     * @return
     */
    public Map<String,Object> getBillSettle(List<Map<String,Object>> settleList, String userId);


    /**
     * 旅居账单扣卡结算：中途结算
     * @param settleList
     * @param userId
     * @return
     */
    public Map<String,Object> getBillSettleCenter(List<Map<String,Object>> settleList, String userId);



    /**
     * 中途结算
     * 设置会员卡交易记录
     * @param tran
     * @param card
     * @param erMap
     * @param scheme
     * @param erId
     */
    public void setCardTransCenter(FinaCardTransactions tran, FinaMembershipCard card, Map<String, Object> erMap, MainCardDeductScheme scheme,String erId,
                                   Double unLockTimes,Double unLockAmount);




    /**
     * 退住结算
     * 设置会员卡交易记录
     * @param tran
     * @param card
     * @param erMap
     * @param erId
     */
    public void setCardTrans(FinaCardTransactions tran, FinaMembershipCard card, Map<String, Object> erMap, MainCardDeductScheme scheme,String erId,
                             Double unLockTimes,Double unLockAmount);



    /**
     * 旅居预订
     * @param appNo
     * @param baseId
     * @param bookData
     * @return
     */
    public Map<String,Object> sojournBook(String appNo,String baseId,String officeId,String businessEntityId,String checkinType,String customerChannel,String bookData,String createBy);




    /**
     * 旅居退住
     * @param appNo
     * @param baseId
     * @param checkoutData
     * @return
     */
    public Map<String,Object> sojournCheckout(String appNo,String baseId,String checkoutData);




    /**
     * 旅居取消预订
     * @param appNo
     * @param baseId
     * @param bookData
     * @return
     */
    public Map<String,Object> sojournCancelBook(String appNo,String baseId,String bookData);



    /**
     * 旅居随行人员 离开/返回
     * @param uniqueId
     * @param checkinNo
     * @param name
     * @param time
     * @return
     */
    public boolean sojournFollowLeave(List<FinaExpenseRecord> erList,String uniqueId,String checkinNo,String type,String name,String time);



    /**
     * 更换会员卡数据:预订
     * @param fh
     * @param er
     * @param cardList
     * @return
     */
    public boolean updateChangeCard(FinaExpenseRecordChangecard fh,FinaExpenseRecord er,List<FinaMembershipCard> cardList);


    /**
     * 【新】更换会员卡数据:预订
     * @param fc
     * @param er
     * @param cardList
     * @return
     */
    public boolean updateChangeCard(FinaExpenseRecordDeductionCard firstFc,FinaExpenseRecordDeductionCard fc,FinaExpenseRecord er,List<FinaMembershipCard> cardList);



    /**
     * 更换会员卡数据:入住
     * @param fh
     * @param list
     * @param cur
     * @param fr
     * @return
     */
    public boolean updateChangeCard(FinaExpenseRecord er,FinaExpenseRecordChangecard fh,List<FinaExpenseRecordDetail> list,int cur,FinaCardDeductRecord fr);


    /**
     * 【新】更换会员卡数据:入住
     * @param er
     * @param fc
     * @param list
     * @param cur
     * @param fr
     * @return
     */
    public boolean updateChangeCard(FinaExpenseRecordDeductionCard firstFc,FinaExpenseRecord er,FinaExpenseRecordDeductionCard fc,List<FinaExpenseRecordDetail> list,int cur,FinaCardDeductRecord fr);


    /**
     * 设置主账单记录预订入参
     */
    public void setEr(FinaExpenseRecord er,String id,String parentId, String type,String expenseNo,String appNo, String baseId, String bookNo, String checkinNo,String name, String idCard, String cardNumber, String telephone, String bedId,
                          String isPrivateRoom,Date bookStartDate,Date bookEndDate,Date checkinTime,Date checkoutTime,String checkinStatus,String settleStatus,Date createTime,String category,String uniqueId,String officeId,String remark);


    /**
     * 设置参团人主账单记录入参
     */
    public void setTouristEr(FinaExpenseRecord er,String appNo,String touristNo,String uniqueId,String name,String idCard,String telephone,String cardNumber,
                             Date checkinDate,Date checkoutDate,Double withholdTimes,Double actualTimes,Double withholdAmount,Double actualAmount,Double withholdPoints,Double actualPoints,
                             Double withholdIntegrals,Double actualIntegrals,String officeId,String remark);



    /**
     * 根据id获取主记录及其连带主记录
     * @param id
     * @return
     */
    public List<FinaExpenseRecord> getMainRecords(String id);




    /**
     * 根据消费主记录获取描述map
     * @param er
     * @return
     */
    public Map<String,String> getExplainMap(FinaExpenseRecord er);


    /**
     * 根据预订号等查询主记录
     * @param appNo
     * @param baseId
     * @param bookNo
     * @return
     */
    public FinaExpenseRecord getErByBookNoPart(String appNo,String baseId,String bookNo);


    /**
     * 获取未作废未删除预订账单
     * @param appNo
     * @param baseId
     * @param bookNo
     * @return
     */
    public FinaExpenseRecord getErByBookNo(String appNo,String baseId,String bookNo);


    /**
     *取消预订：获取预订状态的数据
     * @param appNo
     * @param baseId
     * @param bookNo
     * @return
     */
    public FinaExpenseRecord getErByBookStatus(String appNo,String baseId,String bookNo);




    /**
     * 根据入住号查询主记录
     * @param checkinNo
     * @return
     */
    public FinaExpenseRecord getErByCheckinNoPart(String appNo,String baseId,String checkinNo);


    /**
     * 修改预订时间
     * @param bookNo
     * @param bookStartDateStr
     * @param bookEndDateStr
     * @return
     */
    public Map<String,Object> updateBook(String bookNo,String bookStartDateStr,String bookEndDateStr);

    /**
     * 根据应用号，基地id,入住号查找主记录
     * @param appNo
     * @param baseId
     * @param checkinNo
     * @return
     */
    public FinaExpenseRecord getErByCheckinNoAndBaseAndApp(String appNo,String baseId,String checkinNo);


    /***
     * 根据入住号和退住时间退住账单
     * @param list
     * @return
     */
    public boolean retreatByCheckinNo(List<Record> list,List<FinaMembershipCard> cardList,List<FinaCardDeductRecord> wFrList,List<String> detailIds,List<FinaCardDeductRecord> aFrList);


    /**
     * 根据预订号List取消预订
     * @param erList
     * @return
     */
    public boolean cancelBook(List<FinaExpenseRecord> erList,List<FinaMembershipCard> cardList,List<FinaCardTransactions> tranList,List<FinaExpenseRecordDetail> detailList);


    /**
     * 根据...获取随行人员记录
     * @param appNo
     * @param baseId
     * @param checkinNo
     * @param name
     * @param idCard
     * @return
     */
    public FinaExpenseRecord getFollowByCheckinNoPart(String appNo,String baseId,String uniqueId,String checkinNo,String name,String idCard);


    /**
     * 根据...获取主记录
     * @param appNo
     * @param baseId
     * @param checkinNo
     * @param name
     * @param idCard
     * @return
     */
    public FinaExpenseRecord getRecordByCheckinNoPart(String appNo,String baseId,String uniqueId,String checkinNo,String name,String idCard);


    /**
     * 根据预订号等获取预订记录
     * @param appNo
     * @param baseId
     * @param bookNo
     * @param name
     * @param idCard
     * @return
     */
    public FinaExpenseRecord getRecordByBookNoPart(String appNo,String baseId,String bookNo,String name,String idCard);



    /**
     * 正常入住：获取主账单新增修改List和随行人员
     * @param appNo
     * @param baseId
     * @param jsonArray
     * @return
     */
    public Map<String,Object> getErSaveAndUpdateListAndFfListByParams(String appNo, String baseId, String officeId
            ,String initialDate,String businessEntityId,String checkinType,String customerChannel,JSONArray jsonArray,String createBy);

    
    public Map<String, Object> getDetailsByErListBook(List<FinaExpenseRecord> erList, String initialDate);

    /**
     * 正常入住：根据主账单或初始化计费时间获取消费明细
     * @param erList
     * @param initialDate
     * @return
     */
    public Map<String,Object> getDetailsByErListCheckin(List<FinaExpenseRecord> erList,String initialDate);


    /**
     * 续住/随行：根据主账单获取消费明细
     * @param erList
     * @return
     */
    public Map<String,Object> getDetailsByErList(List<FinaExpenseRecord> erList,String type);


    /**
     * 续住:获取续住，随行主账单和续住随行记录
     * @param appNo
     * @param baseId
     * @param jsonArray
     * @return
     */
    public Map<String,Object> getErListAndFcAndFfListByParams(String appNo, String baseId, String officeId,JSONArray jsonArray);


    /**
     * 随行登记入住：获取随行主账单和随行记录
     * @param appNo
     * @param baseId
     * @param jsonArray
     * @return
     */
    public Map<String,Object> getErListAndFfListByParams(String appNo, String baseId,JSONArray jsonArray);




    /**
     * 根据主账单list统计扣卡汇总锁定
     * @param erList
     * @return
     */
    public boolean getCardTotalLock(List<FinaExpenseRecord> erList);


    /**
     * 根据主账单list统计扣卡汇总实扣
     * @param erList
     * @return
     */
    public boolean getCardTotalActual(List<FinaExpenseRecord> erList);



    /**
     * 根据所有账单更新会员卡的锁定天数和金额
     * @param erList
     * @return
     */
    public boolean getCardLock(List<FinaExpenseRecord> erList,Integer flag,String initialDate);




    /**
     * 获取包房内的床位数
     */
    public Map<String,Object> getBedCount(String bedId);

    public int getRoomBedNum(String bedId);

    public Map<String,Object> updateBookInfo(String officeId, String name, String idCard, String cardNumber, String telephone
            , String bedId, String isPrivateRoom, String remark,String businessEntityId,String customerChannel, FinaExpenseRecord er,String followData);

    /**
     *判断是否可以预订或入住
     * @param appNo
     * @param baseId
     * @return
     */
    public Map<String,Object> isBookAndCheckin(String appNo,String bookNo,String checkinNo
            ,String baseId,String type,String checkData,String addCheckinNo,String addBookNo,String addStartDate,String isLock,String checkinType);



    /**
     * 保存扣卡日志
     * @param appNo
     * @param baseId
     * @param data
     * @return
     */
    public boolean saveApiRecord(String apiName,String appNo,String baseId,String officeId,String initialDate,String data);


    /**
     * 获取所有主账单
     * @return
     */
    public List<FinaExpenseRecord> getMainList();


    /**
     * 校验旅游团参团会员卡
     * @param checkData
     * @return
     */
    public Map<String,Object> isCheckinTourist(String touristRouteId,String checkinDate,String checkData);


    /**
     * 处理旅游账单
     * @param appNo
     * @param touristNo
     * @param touristName
     * @param touristRoute
     * @param touristRouteId
     * @param startDate
     * @param endDate
     * @param expenseTimes
     * @param touristData
     * @return
     */
    public Map<String,Object> dealTouristBill(String appNo,String touristNo,String touristName,String touristRoute
            ,String touristRouteId,String startDate,String endDate,String expenseTimes,String businessEntityId
            ,String customerChannel,String touristData,String touristDetail,String createBy);


    /**
     * 分页获取未结算的旅居账单
     * @return
     */
    public Page<FinaExpenseRecord> getErPageNotSettle(Integer pageNumber,Integer pageSize);






    /**
     * 更换会员卡：预订，入住（部分/全部）
     * @param er
     * @param uniqueId
     * @param cardNumber
     * @param changeType
     * @return
     */
    public Map<String,Object> sojournChangeCard(FinaExpenseRecord er,String uniqueId,String cardNumber,String changeType);




    /**
     * 【新】更换会员卡：支持预订(全部换卡)，入住(全部换卡，分段换卡)
     * @param er
     * @param uniqueId
     * @param changeType
     * @param jsonArray
     * @return
     */
    public Map<String,Object> sojournChangeCard(FinaExpenseRecord er,String uniqueId,JSONArray jsonArray);




    /**
     * 获取需要新增的换卡记录
     * @param erId
     * @param uniqueId
     * @param originalCardNumber
     * @param newCardNumber
     * @return
     */
    public FinaExpenseRecordChangecard getSaveFc(String erId,String uniqueId,String originalCardNumber,String newCardNumber,String changeType);


    /**
     * 根据需新增的会员卡和换卡的账单处理预订换卡情况
     * @param fc
     * @param er
     * @return
     */
    public Map<String,Object> dealChangeCardByBook(FinaExpenseRecordChangecard fc,FinaExpenseRecord er,FinaMembershipCard card,
                                                   MainCardDeductScheme scheme,Double bedCount);


    /**
     * 【新】根据换卡的账单处理预订换卡情况
     * @param fc
     * @param er
     * @param card
     * @param scheme
     * @param bedCount
     * @return
     */
    public Map<String,Object> dealChangeCardByBook(FinaExpenseRecordDeductionCard firstFc,FinaExpenseRecordDeductionCard fc,FinaExpenseRecord er,FinaMembershipCard card,
                                                   MainCardDeductScheme scheme,Double bedCount);



    /**
     * 根据需新增的会员卡和换卡的账单处理入住全部换卡
     * @param fc
     * @param er
     * @param cardNew
     * @param schemeNew
     * @param bedCount
     * @return
     */
    public Map<String,Object> dealChangeCardByCheckinAll(FinaExpenseRecordChangecard fc,FinaExpenseRecord er,FinaMembershipCard cardNew,
                                                          MainCardDeductScheme schemeNew,Double bedCount);


    /**
     * 【新】根据需新增的会员卡和换卡的账单处理入住全部换卡
     * @param fc
     * @param er
     * @param cardNew
     * @param schemeNew
     * @param bedCount
     * @return
     */
    public Map<String,Object> dealChangeCardByCheckinAll(FinaExpenseRecordDeductionCard firstFc,FinaExpenseRecordDeductionCard fc,FinaExpenseRecord er,FinaMembershipCard cardNew,
                                                         MainCardDeductScheme schemeNew,Double bedCount);



    /**
     * 根据需新增的会员卡和换卡的账单处理入住部分换卡
     * @param fc
     * @param er
     * @param cardNew
     * @param schemeNew
     * @param bedCount
     * @param changeType
     * @return
     */
    public Map<String,Object> dealChangeCardByCheckinPart(FinaExpenseRecordChangecard fc,FinaExpenseRecord er,FinaMembershipCard cardNew,
                                                          MainCardDeductScheme schemeNew,Double bedCount,String changeType);


    /**
     * 【新】换卡的账单处理入住部分换卡
     * @param fcList
     * @param er
     * @param bedCount
     * @param jsonArray
     * @return
     */
    public Map<String,Object> dealChangeCardByCheckinPart(FinaExpenseRecordDeductionCard firstFc,List<FinaExpenseRecordDeductionCard> fcList,FinaExpenseRecord er,JSONArray jsonArray);



    /**
     * 更换会员卡：获取新明细及需要更换的旧明细汇总
     * @param list
     * @param er
     * @param cardNew
     * @param schemeNew
     * @return
     */
    public Map<String,Object> getNewDetailsAndOldDetailsTotal(List<FinaExpenseRecordDetail> list,FinaExpenseRecord er,FinaMembershipCard cardNew,MainCardDeductScheme schemeNew,Double bedCount,Date date);


    /**
     * 【新】更换会员卡：变更旧明细
     * @param list
     * @param er
     * @param cardNew
     * @param schemeNew
     * @param newCardMap
     * @return
     */
    public Map<String,Object> changeErDetails(List<FinaExpenseRecordDetail> list,FinaExpenseRecord er,FinaMembershipCard cardNew,MainCardDeductScheme schemeNew,Map<String,Record> oldCardMap,Map<String,Record> newCardMap);


    /**
     * 【新】更换会员卡：根据账单获取扣卡汇总中旧卡的锁定并区分哪些更新，哪些删除
     * @param er
     * @return
     */
    public Map<String,Object> getOldCardLockPart(FinaExpenseRecord er);




    /**
     * 【新】更换会员卡：根据需要更新的扣卡汇总获取已执行未结算的明细并去除
     * @param updateFrList
     */
    public void getNotSettleDetailAndLose(List<FinaExpenseRecord> updateFrList);


    /**
     * 更换会员卡：获取需要更换的旧明细汇总
     * @return
     */
    public Map<String,Record> getOldDetailsTotal(FinaExpenseRecord er,Date date);



    /**
     * 【新】更换会员卡：获取需要更换的旧明细汇总
     * @param er
     * @param beginDate
     * @param endDate
     * @return
     */
    public Map<String,Record> getOldDetailsTotal(FinaExpenseRecord er,String beginDate,String endDate);




    /**
     * 根据主账单获取主账单id和附属账单id
     * @param er
     * @return
     */
    public boolean updateDetailSettled(FinaExpenseRecord er);



    /**
     * 根据应用号，基地id和入住号list获取账单list
     * @param list
     * @return
     */
    public List<FinaExpenseRecord> getErListByCheckinNo(List<Record> list);


    /**
     * 处理退住后的明细和扣卡汇总
     * @param list
     * @return
     */
    public boolean dealCheckoutDetails(List<Record> list);


    /**
     * 获取更换卡账单已执行未结算的明细扣卡汇总
     * @param er
     * @param cardMap
     */
    public boolean getNotSettledDetailsCount(FinaExpenseRecord er,Map<String,Record> cardMap);


    /**
     * 设置主账单记录到list中
     * @param mainList
     * @param mainEr
     */
    public void setMainToList(List<FinaExpenseRecord> mainList,FinaExpenseRecord mainEr);


    /**
     * 续住/随行（设置明细）:根据以下参数设置明细数据
     * @param list
     * @param er
     * @param scheme
     * @param map
     */
    public List<FinaExpenseRecordDetail> setDetail(int checkinNum,List<Record> list,FinaExpenseRecord er,MainCardDeductScheme scheme,Map<String,String> map,String initialDate,FinaMembershipCard card,Map<String,Map<String,Object>> integralGenDetailInfo,boolean isConfigDays,double bedCost);



    /**
     * 非即时退住处理
     * @param list
     * @return
     */
    public Map<String,Object> dealCheckoutDetailList(List<Record> list);


    /**
     * 处理退住锁定：换住
     * @param list
     * @return
     */
    public Map<String,Object> dealCheckoutChange(List<Record> list);


   /*批量作废begin*/
    /*查询需作废账单*/
    public List<FinaExpenseRecord> findBookAndNotSettleErs();

    /*作废预订*/
    public Map<String,Object> delBookAndCheckin(FinaExpenseRecord er);
    /*批量作废end*/


    /**
     * 通过会员卡获取交易记录中的备注
     * @param cardNumber
     * @return
     */
    public String getRemarkTransByCardNumber(String cardNumber,List<FinaCardTransactions> list);


    /**
     * 处理续住重复
     * @param fcList
     * @return
     */
    public Map<String,Object> dealContinuedRepeat(String appNo,String baseId,List<FinaExpenseRecordContinue> fcList);


    /**
     * 处理随行重复
     * @param ffList
     * @return
     */
    public Map<String,Object> dealFollowRepeat(String appNo,String baseId,List<FinaExpenseRecordFollow> ffList);


    /**
     * 获取主记录信息
     * @param id
     * @return
     */
    public Record getMainRecordDetail(String id);

    public List<Record> getAllRecordDetail(String id);

    /**
     * 获取不包含续住的账单记录
     * @param id
     * @return
     */
    public List<Record> getErListNotCon(String id);


    /**
     * 获取最大的续住时间
     * @param id
     * @return
     */
    public Map<String,String> getMaxConTime(String id);


    /**
     *
     * @param record
     * @return
     */
    public Map<String,Object> changCard(Record record);


    public Map<String,Object> changSingleCard(Record record);

    public Map<String, Object> changSingleCard2(Record record);

    /**
     * 转移会员卡升级：根据会员卡获取旅居账单(入住)
     * @param cardNumber
     * @return
     */
    public List<FinaExpenseRecord> getExpenseRecors(String cardNumber);


    /**
     * 转移会员卡升级：根据会员卡获取旅居账单(预订/旅游团)
     * @param cardNumber
     * @return
     */
    public List<FinaExpenseRecord> getBookAndTouristExpenseRecors(String cardNumber);


    /**
     * 转移会员卡升级：查找出里面的主账单
     * @param erList
     * @return
     */
    public List<FinaExpenseRecord> getMainList(List<FinaExpenseRecord> erList);


    /**
     * 转移会员卡升级：获取旧卡展示的账单记录
     * @param cardNumber
     * @return
     */
    public List<FinaExpenseRecord> getErsByCard(String cardNumber);


    /**
     * 修改预订账单
     * @param officeId
     * @param name
     * @param idCard
     * @param cardNumber
     * @param telephone
     * @param bedId
     * @param isPrivateRoom
     * @param remark
     * @param er
     * @return
     */
    public Map<String,Object> updateBookEr(String officeId,String name,String idCard,String cardNumber,String telephone
            ,String bedId,String isPrivateRoom,String remark,String businessEntityId,String customerChannel,FinaExpenseRecord er,String followData);




    /**
     * 财务判断是否可以换住校验
     * @param er
     * @param isPrivateRoom
     * @param checkoutDateBefore
     * @return
     */
    public Map<String,Object> isToChangeBookAndCheckin(FinaExpenseRecord er,String type,String isPrivateRoom,String checkoutDateBefore,Double totalBeds,String roomName);




    /**
     * 根据id查询消费主记录
     * @param id
     * @return
     */
    public FinaExpenseRecord get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecord
     */
    public List<FinaExpenseRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}