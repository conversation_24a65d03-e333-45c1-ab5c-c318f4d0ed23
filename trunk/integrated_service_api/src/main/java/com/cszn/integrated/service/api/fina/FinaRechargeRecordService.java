package com.cszn.integrated.service.api.fina;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.ParseException;
import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRechargeRecord;
import com.cszn.integrated.service.entity.fina.FinaRechargeList;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaRechargeRecordService  {
	
    /**
     * 批量导入充值名单
     * @param model
     * @param file
     * @param fileName
     * @return
     * @throws FileNotFoundException
     * @throws ParseException
     */
    public Ret importRechargeList(FinaRechargeRecord model, File file, String fileName, String userId) throws Exception;
    
    /**
     * 确认发送充值名单
     * @param model
     * @return
     * @throws FileNotFoundException
     * @throws ParseException
     */
    public Ret confirmRechargeList(FinaRechargeRecord model, String userId) throws Exception;
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<FinaRechargeRecord> paginateByCondition(FinaRechargeRecord model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaRechargeRecord findById(Object id);

    /**
     * find all model
     *
     * @return all <FinaLottery
     */
    public List<FinaRechargeRecord> findAll();

    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);

    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaRechargeRecord model);

    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaRechargeRecord model);

    /**
     * 保存信息
     * @param model
     * @return
     */
    public boolean saveRechargeRecord(FinaRechargeRecord model, List<FinaRechargeList> rechargeList, String userId);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaRechargeRecord model);

    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaRechargeRecord model);

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaRechargeRecord> paginate(int page, int pageSize);

    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaRechargeRecord> paginateByColumns(int page, int pageSize, Columns columns);

    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaRechargeRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}