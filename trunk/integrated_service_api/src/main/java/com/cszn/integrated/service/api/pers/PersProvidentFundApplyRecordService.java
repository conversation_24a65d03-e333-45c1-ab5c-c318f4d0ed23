package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersProvidentFundApplyRecord;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface PersProvidentFundApplyRecordService {

    Page<Record> pageList(Integer page, Integer limit , PersProvidentFundApplyRecord record, String userId);

    PersProvidentFundApplyRecord findById(Object id);

}
