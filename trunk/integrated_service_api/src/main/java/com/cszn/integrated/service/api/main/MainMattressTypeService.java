package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainMattressType;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainMattressTypeService {

    /**
     * 通过编号和名字判断床垫是否存在
     * @param mattressType
     * @return
     */
    public boolean mattressTypeIsExist(MainMattressType mattressType);

    /**
     * 获取所有床垫
     * @return
     */
    public List<MainMattressType> findAllMattressType();

    /**
     * 获取床垫分页
     * @param pageNumber
     * @param pageSize
     * @param mattressType
     * @return
     */
    public Page<MainMattressType> findMattressTypePage(Integer pageNumber,Integer pageSize,MainMattressType mattressType);

    /**
     * 保存床垫
     * @return
     */
    public boolean saveMattressType(MainMattressType mainMattressType,String userId);

    /**
     * 删除床垫
     * @return
     */
    public boolean delMattressType(String id,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainMattressType findById(Object id);


    /**
     * find all model
     *
     * @return all MainMattressType
     */
    public List<MainMattressType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainMattressType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainMattressType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainMattressType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainMattressType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainMattressType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainMattressType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainMattressType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
