package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBedChannel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBedChannelService {

    /**
     * 通过床位id获取预定渠道
     * @param bedId
     * @return
     */
    public List<MainBedChannel> bedChannelList(String bedId);

    /**
     * 保存床位预订渠道
     * @param bedId
     * @param bookChannelIdStr
     * @return
     */
    public boolean saveBedChannel(String bedId,String bookChannelIdStr,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBedChannel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainBedChannel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBedChannel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBedChannel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBedChannel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBedChannel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBedChannel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBedChannel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBedChannel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
