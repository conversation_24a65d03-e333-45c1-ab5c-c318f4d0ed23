package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestion;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsQuestionnaireQuestionService {

    public boolean saveQuestion(MmsQuestionnaireQuestion question,String userId);


    public Page<MmsQuestionnaireQuestion> pageList(int pageNumber,int pageSize,MmsQuestionnaireQuestion question);


    public List<MmsQuestionnaireQuestion> findListByQuestionnaireId(String questionnaireId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsQuestionnaireQuestion findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsPrize
     */
    public List<MmsQuestionnaireQuestion> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsQuestionnaireQuestion model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsQuestionnaireQuestion model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsQuestionnaireQuestion model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsQuestionnaireQuestion model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsQuestionnaireQuestion> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsQuestionnaireQuestion> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsQuestionnaireQuestion> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
