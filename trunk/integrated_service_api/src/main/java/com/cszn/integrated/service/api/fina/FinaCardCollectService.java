package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardCollect;
import com.jfinal.plugin.activerecord.Page;

import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

public interface FinaCardCollectService  {

    /**
     * 获取收款统计数据
     * @param type
     * @param yearMonthDay
     * @param yearMonth
     * @param year
     * @return
     */
    public List<Record> collectStatistics(String type, String yearMonthDay, String yearMonth, String year);

    /**
     * 获取收款列表数据
     * @param type
     * @param yearMonthDay
     * @param yearMonth
     * @param year
     * @return
     */
    public Page<Record> collectList(String type, String yearMonthDay, String yearMonth, String year, String branchOfficeName, Integer pageNumber, Integer pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardCollect findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FinaCardCollect
     */
    public List<FinaCardCollect> findList(String cardId);


    /**
     * find all model
     *
     * @return all <FinaCardCollect
     */
    public List<FinaCardCollect> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardCollect model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardCollect model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardCollect model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardCollect model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardCollect> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardCollect> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardCollect> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}