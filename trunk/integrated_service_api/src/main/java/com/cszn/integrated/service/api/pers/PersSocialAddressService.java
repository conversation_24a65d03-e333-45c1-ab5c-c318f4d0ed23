package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersSocialAddress;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersSocialAddressService {

    public Page<PersSocialAddress> findSocialAddressPageList(int pageNumber,int pageSize,PersSocialAddress socialAddress);

    public List<PersSocialAddress> getSocialAddressList(PersSocialAddress socialAddress);

    public boolean saveSocialAddress(PersSocialAddress socialAddress,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersSocialAddress findById(Object id);


    /**
     * find all model
     *
     * @return all <PersPosition
     */
    public List<PersSocialAddress> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersSocialAddress model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersSocialAddress model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersSocialAddress model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersSocialAddress model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersSocialAddress> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersSocialAddress> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersSocialAddress> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
