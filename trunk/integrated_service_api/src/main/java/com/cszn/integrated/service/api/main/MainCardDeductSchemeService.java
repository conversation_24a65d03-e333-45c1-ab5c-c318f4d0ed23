package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainCardDeductSchemeService  {


    public MainCardDeductScheme getLongSchemeByCardNumber(String cardNumber);

    /**
     * 根据条件查询表格分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainCardDeductScheme> paginateByCondition(MainCardDeductScheme model, int pageNumber, int pageSize);
    
    
    /**
     * 查询未删除的所有扣费方案列表
     *
     * @return List<MainCardDeductScheme>
     */
    public List<MainCardDeductScheme> findListOnUse(String schemeType);
    
    /**
     * 扣费保存方法
     * 
     * @param model
     * @return
     */
    public Ret saveCardDeductScheme(MainCardDeductScheme model);


    /**
     * 通过会员卡获取扣卡方案
     * @param cardNumber
     * @return
     */
    public MainCardDeductScheme getSchemeByCardNumber(String cardNumber);
    
    
    /**
     * 通过会员卡获取扣卡方案
     * @param cardNumber
     * @return
     */
    public MainCardDeductScheme getSchemeLongByCardNumber(String cardNumber);


    /**
     * 通过扣卡规则编号查询扣卡规则
     * @param schemeNo
     * @return
     */
    public MainCardDeductScheme getSchemeBySchemeNo(String schemeNo);


    /**
     * 会员卡升级：判断新旧卡扣卡方是是否一致
     * @param oldDeductSchemeId
     * @param newDeductSchemeId
     * @return
     */
    public boolean isSomeDeductScheme(String oldDeductSchemeId,String newDeductSchemeId);


    /**
     * 获取扣卡规则对象
     * @param id
     * @return
     */
    public MainCardDeductScheme get(String id);



    /**
     *
     * @param sql
     * @param params
     * @return
     */
    public MainCardDeductScheme findFirst(String sql, Object... params);



    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardDeductScheme findById(Object id);


    /**
     * find all model
     *
     * @return all <Dict
     */
    public List<MainCardDeductScheme> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardDeductScheme model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainCardDeductScheme model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainCardDeductScheme model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardDeductScheme model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}