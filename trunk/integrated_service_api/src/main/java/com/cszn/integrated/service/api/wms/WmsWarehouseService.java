package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsWarehouseManager;
import com.cszn.integrated.service.entity.wms.WmsWarehouses;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface WmsWarehouseService {

    public List<WmsWarehouses> getWmsWarehouseList(String activityId);

    public boolean saveWarehouseOrgRel(String warehouseId,String orgIds,String userId);

    public List<WmsWarehouses> getAllWmsWarehouseByUserId(String userId);

    public List<WmsWarehouses> getAllWmsWarehouse();

    public boolean delWarehouseManager(String id,String userId);

    public boolean saveWarehouseManager(List<WmsWarehouseManager> managerList,List<WmsWarehouseManager> updateManagerList,String userId);

    /**
     * 保存
     * @param wmsWarehouses
     * @return
     */
    public boolean saveWarehouse(WmsWarehouses wmsWarehouses);

    /**
     * 获取分页
     * @param pageNumber
     * @param pageSize
     * @param wmsWarehouses
     * @return
     */
    public Page<WmsWarehouses> findWarehousePageList(Integer pageNumber,Integer pageSize,WmsWarehouses wmsWarehouses);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsWarehouses findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsWarehouses> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsWarehouses model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsWarehouses model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsWarehouses model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsWarehouses model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsWarehouses> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsWarehouses> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsWarehouses> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
