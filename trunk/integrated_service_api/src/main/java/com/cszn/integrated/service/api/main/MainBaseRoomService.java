package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseRoom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface MainBaseRoomService  {


    /**
     * 判断房间是否属于这个基地
     * @param roomId
     * @param baseId
     * @return
     */
    public boolean roomBelongToBase(String roomId,String baseId);

    /**
     * 批量删除房间
     * @param ids
     * @param userId
     * @return
     */
    public boolean batchDelRoom(String[] ids,String userId);

    /**
     * 判断房间是否存在
     * @param room
     * @return
     */
    public MainBaseRoom roomIsExist(MainBaseRoom room);

    /**
     *
     */
    public Integer queryRoomCountByFloorId(String floorId);


    //public boolean batchDelMainBaseRoom(List<MainBaseRoom> roomList,String userId);

    public Page<Record> getRoomPageByFloorId(String floorId, Integer pageNumber, Integer pageSize);

    /**
     * 通过楼层id获取房间
     * @param floorIds
     * @return
     */
    public List<MainBaseRoom> getRoomListByFloorIds(String[] floorIds);

    /**
     * 保存楼层
     * @param mainBaseRoom
     * @return
     */
    public boolean saveRoom(MainBaseRoom mainBaseRoom);

    /**
     * 删除房间
     * @param id
     * @return
     */
    public boolean delRoom(String id,String userId);

    /**
     * 增加房间床位数量
     * @param id
     * @param num
     * @return
     */
    public boolean updateBedNumber(String id,int num,String userId);



    /**
     * 判断房间和房间的床位数是否存在
     * @param roomId
     * @return
     */
    public Map<String,String> isHaveRoomAndBedNum(String roomId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseRoom findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseRoom
     */
    public List<MainBaseRoom> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseRoom model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseRoom model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseRoom model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseRoom model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseRoom> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseRoom> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseRoom> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}