package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainPunishRule;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainPunishRuleService  {

    /**
     * 通过类型和违约天数获取配置
     * @param punishType
     * @param exceedDay
     * @return
     */
    public MainPunishRule getPunishRuleByCondition(String deductWay,String punishType,int exceedDay);

    /**
     * 分页
     * @param pageNumber
     * @param pageSize
     * @param pr
     * @return
     */
    public Page<MainPunishRule> findPage(Integer pageNumber, Integer pageSize, MainPunishRule pr);


    /**
     * 根据处罚类型获取处罚规则
     * @param punishType
     * @return
     */
    public MainPunishRule getPunishRuleByType(String punishType);


    /**
     * 删除违约规则
     * @param id
     * @param userId
     * @return
     */
    public boolean delPunishRule(String id,String userId);


    /**
     * 保存违约规则
     * @param pr
     * @param userId
     * @return
     */
    public boolean savePunishRule(MainPunishRule pr,String userId);


    /**
     * 去重
     * @param pr
     * @return
     */
    public boolean getDistinct(MainPunishRule pr);


    /**
     * 获取违约对象
     * @param id
     * @return
     */
    public MainPunishRule get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainPunishRule findById(Object id);


    /**
     * find all model
     *
     * @return all <MainPunishRule
     */
    public List<MainPunishRule> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainPunishRule model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainPunishRule model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainPunishRule model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainPunishRule model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainPunishRule> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainPunishRule> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainPunishRule> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}