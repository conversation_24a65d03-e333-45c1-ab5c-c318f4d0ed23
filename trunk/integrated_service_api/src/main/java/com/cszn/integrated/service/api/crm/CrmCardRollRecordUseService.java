package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecordUse;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollRecordUseService  {

    public List<CrmCardRollRecordUse> findListByJoinId(String joinId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecordUse findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollRecordUse
     */
    public List<CrmCardRollRecordUse> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollRecordUse model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollRecordUse model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollRecordUse model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollRecordUse model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecordUse> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollRecordUse> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollRecordUse> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}