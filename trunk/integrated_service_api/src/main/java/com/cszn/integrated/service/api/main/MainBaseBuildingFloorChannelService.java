package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseBedMarkupType;
import com.cszn.integrated.service.entity.main.MainBaseBuildingFloorChannel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseBuildingFloorChannelService {


    /**
     * 保存预定渠道
     * @param type
     * @param joinId
     * @param channelIds
     * @return
     */
    public boolean saveBaseBuildingFloorChannel(String type,String joinId,String channelIds,String userId);

    /**
     * 通过类型和连接id获取预定渠道
     * @param type
     * @param joinId
     * @return
     */
    public List<MainBaseBuildingFloorChannel> findChannelByTypeAndJoinId(String type,String joinId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseBuildingFloorChannel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainBaseBuildingFloorChannel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseBuildingFloorChannel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseBuildingFloorChannel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseBuildingFloorChannel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseBuildingFloorChannel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseBuildingFloorChannel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseBuildingFloorChannel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseBuildingFloorChannel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
