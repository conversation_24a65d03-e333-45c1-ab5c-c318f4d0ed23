package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeSocialSecurity;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeSocialSecurityService {

    public PersOrgEmployeeSocialSecurity findEmployeeSocialSecurityMaxMonth(String empId);

    List<PersOrgEmployeeSocialSecurity> findEmployeeSocialSecurityList(String yearMonth, List<String> empIds);

    List<PersOrgEmployeeSocialSecurity> findEmployeeSocialSecurityListByApplyId(String applyId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeSocialSecurity findById(Object id);



    /**
     * find all model
     *
     * @return all <PersOrgEmployeeSocialSecurity
     */
    public List<PersOrgEmployeeSocialSecurity> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeSocialSecurity model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeSocialSecurity model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeSocialSecurity model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeSocialSecurity model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeSocialSecurity> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeSocialSecurity> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeSocialSecurity> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
