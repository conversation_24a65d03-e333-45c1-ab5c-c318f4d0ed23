package com.cszn.integrated.service.api.pers;


import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.pers.PersNoticeType;
import io.jboot.db.model.Columns;
import java.util.List;

public interface PersNoticeTypeService  {


    /**
     * 公告类型分页
     * @param pageNumber
     * @param pageSize
     * @param noticeType
     * @return
     */
    public Page<PersNoticeType> findListPage(Integer pageNumber, Integer pageSize, PersNoticeType noticeType);


    /**
     * 根据id获取公告类型对象
     * @param id
     * @return
     */
    public PersNoticeType get(String id);


    /**
     * 保存公告类型
     * @param noticeType
     * @param userId
     * @return
     */
    public String saveNoticeType(PersNoticeType noticeType,String userId);



    /**
     * 删除公告类型
     * @param id
     * @param userId
     * @return
     */
    public boolean delNoticeType(String id,String userId);



    /**
     * 获取所有未删除公告类型
     * @return
     */
    public List<PersNoticeType> getNuDelList();


    /**
     * 保存：去重
     * @param noticeType
     * @return
     */
    public String getDistinct(PersNoticeType noticeType);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersNoticeType findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeType
     */
    public List<PersNoticeType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersNoticeType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersNoticeType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersNoticeType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersNoticeType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersNoticeType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersNoticeType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersNoticeType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}