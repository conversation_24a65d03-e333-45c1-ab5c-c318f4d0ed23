package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRollType;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollTypeService  {


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollType> paginate(CrmCardRollType model, int page, int pageSize);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollType findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollType
     */
    public List<CrmCardRollType> findList();


    /**
     * find all model
     *
     * @return all <CrmCardRollType
     */
    public List<CrmCardRollType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}