package com.cszn.integrated.service.api.fina;

import java.util.Date;
import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardIntegralRecord;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaCardIntegralRecordService  {

    /**
     * 通过会员卡Id获取list
     * @param cardId
     * @return
     */
    public List<FinaCardIntegralRecord> findByCardId(String cardId);
    
    /**
     * 通过会员卡Id和大于充值日期获取的list
     * @param cardId
     * @return
     */
    public List<FinaCardIntegralRecord> findByCardIdAndRechargeDate(String cardId, Date giveDate);
    
    /**
     * 根据会员卡id获取所有赠送积分总数
     * @param cardId
     * @return
     */
    public Double getTotalGiveIntegralByCardId(String cardId);
    
    /**
     * 根据会员卡id和赠送日期查找赠送记录
     * @param cardId
     * @param giveDate
     * @return
     */
    public FinaCardIntegralRecord getByCardIdAndGiveDate(String cardId, Date giveDate);
    
    /**
     * 根据会员卡id查找赠送日期倒序第1条记录
     * @param cardId
     * @return
     */
    public FinaCardIntegralRecord getByCardIdAndGiveDateDescFirst(String cardId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardIntegralRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardIntegralRecord
     */
    public List<FinaCardIntegralRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardIntegralRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardIntegralRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardIntegralRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardIntegralRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardIntegralRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardIntegralRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardIntegralRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}