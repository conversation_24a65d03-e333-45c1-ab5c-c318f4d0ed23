package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeBusinessTripApply;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeBusinessTripApplyService {

    public PersOrgEmployeeBusinessTripApply findById(Object id);

    public List<PersOrgEmployeeBusinessTripApply> findBySql(String sql,Object[] params);

    public PersOrgEmployeeBusinessTripApply findEmpBusinessTripApplyByStatus(String empId, Date date, String[] status);
}
