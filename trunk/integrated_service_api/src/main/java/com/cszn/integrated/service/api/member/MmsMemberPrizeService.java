package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsMemberPrize;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsMemberPrizeService  {

    /**
     * 分页
     * @return
     */
    public Page<Record> findPage(Integer pageNumber,Integer pageSize,MmsMemberPrize memberPrize);


    /**
     * 删除
     * @param id
     * @param userId
     * @return
     */
    public boolean delEleMember(String id,String userId);


    /**
     *获取所有中奖会员
     * @return
     */
    public List<Record> getMemberPrizes();


    /**
     * 根据id获取中奖会员
     * @param id
     * @return
     */
    public Record getMemberPrize(String id);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMemberPrize findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMemberPrize
     */
    public List<MmsMemberPrize> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMemberPrize model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsMemberPrize model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsMemberPrize model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMemberPrize model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsMemberPrize> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsMemberPrize> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsMemberPrize> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}