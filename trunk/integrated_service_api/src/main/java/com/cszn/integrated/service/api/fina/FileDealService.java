package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.member.MmsMember;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.ParseException;

/**
 * 文件处理
 */
public interface FileDealService {
    /**
     * 处理excel数据
     * @param file
     * @param fileName
     * @return
     * @throws FileNotFoundException
     * @throws ParseException
     */
    public boolean dealExcel(File file, String fileName, String userId) throws Exception;

    /**
     * 处理性别
     * @param member
     */
    public void dealGender(MmsMember member);


    /**
     *处理卡类别
     */
    public boolean dealCardType(FinaMembershipCard card,String userId);


    /**
     * 处理扣费方案
     * @param card
     * @param userId
     * @return
     */
    public boolean dealDeductScheme(FinaMembershipCard card,String userId);


    /**
     * 处理年限类型
     * @param card
     * @param userId
     * @return
     */
    public boolean dealYearLimit(FinaMembershipCard card,String userId);



    /**
     * 处理赠送方案
     * @param card
     * @param userId
     * @return
     */
    public boolean dealGiveScheme(FinaMembershipCard card,String userId);

    /**
     *处理档案关联
     * @param member
     * @param card
     * @param userId
     * @return
     */
    public boolean saveAndUpdateCard(MmsMember member, FinaMembershipCard card, MmsMember memberExist,String userId);


}
