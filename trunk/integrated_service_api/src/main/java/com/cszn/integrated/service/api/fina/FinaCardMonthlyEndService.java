package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardMonthlyEnd;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaCardMonthlyEndService{
	
	/**
	 * cardMonthlyEndVoid
	 *
	 * @param yearMonth
	 * @return
	 */
	public boolean cardMonthlyEndVoid(String yearMonth, String userId);
	
	/**
	 * cardMonthlyEnd
	 *
	 * @param yearMonth
	 * @return
	 */
	public boolean cardMonthlyEnd(String yearMonth, String userId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardMonthlyEnd findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardMonthlyEnd
     */
    public List<FinaCardMonthlyEnd> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardMonthlyEnd model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardMonthlyEnd model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardMonthlyEnd model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardMonthlyEnd model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardMonthlyEnd> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardMonthlyEnd> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardMonthlyEnd> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
