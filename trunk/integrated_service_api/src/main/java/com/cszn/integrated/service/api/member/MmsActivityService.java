package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRefundApply;
import com.cszn.integrated.service.entity.member.MmsActivity;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

public interface MmsActivityService  {

    public void bmpInvokeGetForm(String formId);

    public MmsActivity getActivityByQrcode(String qrcode);

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsActivity> paginateByCondition(MmsActivity model, int pageNumber, int pageSize);
    
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsActivity> paginateByCondition(String nowDate, int pageNumber, int pageSize);
    

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsActivity findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsActivityQrcode
     */
    public List<MmsActivity> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsActivity model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsActivity model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsActivity model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsActivity model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);

    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsActivity> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsActivity> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}