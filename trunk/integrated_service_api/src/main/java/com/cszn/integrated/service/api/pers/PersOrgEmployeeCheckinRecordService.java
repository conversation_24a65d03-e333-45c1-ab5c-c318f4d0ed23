package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinRecord;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinWxRecord;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeCheckinRecordService {

    public List<PersOrgEmployeeCheckinRecord> getEmpCheckinRecordListBySummaryDate(String empId,Date summaryDate);
    public List<PersOrgEmployeeCheckinRecord> getEmpCheckinRecordListByTimeRange(String empId,Date startTime,Date endTime);

    public Page<Record> dayCheckinStatusPageList(String date,String deptId,String fullName,String userId,int page,int limit);

    public PersOrgEmployeeCheckinRecord getEmpCheckinRecordByTime(String empId,Date date);

    public List<Record> getRecordListByDate(String empId,Date date);

    public List<PersOrgEmployeeCheckinRecord> findEmpCheckinRecordBySchCheckinTime(String empId,List<Date> schCheckinTimeList);

    public PersOrgEmployeeCheckinRecord geEmpCheckinRecordByType(String empId,Date startDate,Date endDate,String checkinType);

    public PersOrgEmployeeCheckinRecord getScheduleWorkRecord(String empId,Date workTime,Date offWorkTime);

    public PersOrgEmployeeCheckinRecord getScheduleOffWorkRecord(String empId,Date workTime,Date offWorkTime);

    public boolean saveCheckinRecord(PersOrgEmployeeCheckinWxRecord checkinWxRecord, String checkinType, String exception, Date schCheckinTime, String ruleId, String ruleName, String checkinDateId, String scheduleId);

    public List<PersOrgEmployeeCheckinRecord> getCheckinRecordByDate(String empId, Date date);

    public List<PersOrgEmployeeCheckinRecord> getCheckinList(String empId,String startDateStr,String endDateStr);

    public Page<Record> pageList(int pageNumber, int pageSize, String empId, String startDateStr, String endDateStr);

    Page<Record> pageList(int pageNumber,int pageSize,PersOrgEmployeeCheckinRecord checkinRecord);

    PersOrgEmployeeCheckinRecord getEmpCheckinRecordByDateRange(String empId,Date startDate,Date endDate,String checkinType);

    public void getWorkWeiXinCheckinData(int pageNumber,int pageSize,long startTime,long endTime);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeCheckinRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
