package com.cszn.integrated.service.api.member;


import com.cszn.integrated.service.entity.member.MmsEquipmentType;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import java.util.List;

public interface MmsEquipmentTypeService  {


    /**
     * 设备分页
     * @param pageNumber
     * @param pageSize
     * @param equipmentType
     * @return
     */
    public Page<MmsEquipmentType> findListPage(Integer pageNumber, Integer pageSize, MmsEquipmentType equipmentType);


    /**
     * 根据id获取设备对象
     * @param id
     * @return
     */
    public MmsEquipmentType get(String id);


    /**
     * 保存设备
     * @param equipmentType
     * @param userId
     * @return
     */
    public String saveEquipmentType(MmsEquipmentType equipmentType,String userId);



    /**
     * 删除设备
     * @param id
     * @param userId
     * @return
     */
    public boolean delEquipmentType(String id,String userId);


    /**
     * 通过设备型号查询设备类型
     * @param equipmentModel
     * @return
     */
    public MmsEquipmentType getEquipmentTypeByModel(String equipmentModel);



    /**
     * 保存：去重
     * @param equipmentType
     * @return
     */
    public String getDistinct(MmsEquipmentType equipmentType);


    /**
     * 获取未删除测量设备类型
     * @return
     */
    public List<Record> getEquipmentTypeList();


    /**
     * 根据id获取设备类型
     * @param id
     * @return
     */
    public Record getTypeRecord(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsEquipmentType findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsEquipmentType
     */
    public List<MmsEquipmentType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsEquipmentType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsEquipmentType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsEquipmentType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsEquipmentType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsEquipmentType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsEquipmentType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsEquipmentType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}