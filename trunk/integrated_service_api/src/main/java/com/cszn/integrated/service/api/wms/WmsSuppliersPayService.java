package com.cszn.integrated.service.api.wms;

import java.util.List;

import com.cszn.integrated.service.entity.wms.WmsSuppliersPay;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface WmsSuppliersPayService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsSuppliersPay findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <WmsSuppliersPay
     */
    public List<WmsSuppliersPay> findList(String suppliersId);


    /**
     * find all model
     *
     * @return all <WmsSuppliersPay
     */
    public List<WmsSuppliersPay> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsSuppliersPay model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsSuppliersPay model);
    
    /**
     * 保存供单位
     * @param WmsUnit
     * @return
     */
    public boolean savePay(WmsSuppliersPay model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsSuppliersPay model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsSuppliersPay model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsSuppliersPay> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsSuppliersPay> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsSuppliersPay> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}