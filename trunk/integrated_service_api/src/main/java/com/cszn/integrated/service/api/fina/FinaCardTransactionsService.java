package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardTransactions;
import com.cszn.integrated.service.entity.fina.FinaCardTransfer;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FinaCardTransactionsService {

    public List<FinaCardTransactions> getFinaCardTransactionsByExpenseId(String ExpenseId,String inOutFlag);

    public List<FinaCardTransactions> getFinaCardTransactions(String cardId);
	/**
	 * 获取收入明细分页
	 * @param yearMonth
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<Record> monthlyEndDetailPage(String yearMonth, String cardNumber, Integer pageNumber, Integer pageSize);
	
	/**
	 * 获取收入明细分页
	 * @param yearMonth
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<Record> cardTransactionsPricePage(String yearMonth, String cardTypeId, String cardNumber, Integer pageNumber, Integer pageSize);

    public void updateCardTransactionsDescribe(int pageNumber,int pageSize);

	/**
	 * 获取扣卡统计数据
	 * @param type
	 * @param yearMonthDay
	 * @param yearMonth
	 * @param year
	 * @return
	 */
	public List<Record> deductStatistics(String type, String yearMonthDay, String yearMonth, String year);
	
	/**
	 * 获取扣卡统计明细分页
	 * @param type
	 * @param yearMonthDay
	 * @param yearMonth
	 * @param year
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<Record> deductStatisticsPage(String type, String yearMonthDay, String yearMonth, String year,Integer pageNumber, Integer pageSize);
	
	/**
	 * 获取扣卡统计明细列表
	 * @param type
	 * @param yearMonthDay
	 * @param yearMonth
	 * @param year
	 * @return
	 */
	public List<Record> deductStatisticsList(String type, String yearMonthDay, String yearMonth, String year);

    public List<FinaCardTransactions> findRecordList(String cardId);

    /**
     *
     * @param cardId
     * @param date
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<Record> getPage(String cardId,String date, String type,Integer pageNumber, Integer pageSize);


    /**
     * 获取消费记录list
     * @param cardNumber
     * @param fullName
     * @param inOutFlag
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Record> getConsumeList(String cardTypeId, String cardNumber, String fullName,String inOutFlag,String startDate, String endDate);
    
    /**
     * 根据会员卡id获取总的扣除积分
     *
     * @param cardId
     * @return
     */
    public Double getTotalDeductIntegralsByCardId(String cardId);

    /**
     * 插入交易记录表
     * @param cardId
     * @param type
     * @param flag
     * @param amount
     * @param consumeTimes
     * @param describe
     * @return
     */
    public boolean saveCardTransactions(String tranId, String expenseId, String cardId, String consumeType, String type, String flag, Double amount, Double consumeTimes, Double points, Double integrals, Double beanCoupons, String describe, Date startDate,Date endDate, Date dealTime, FinaMembershipCard card);




    /**
     * 获取充值/消费记录
     * @param cardNumber
     * @param fullName
     * @param startDate
     * @param endDate
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<Record> getConsumeRecord(String cardTypeId,String cardNumber,String fullName,String inOutFlag,String startDate,String endDate,Integer pageNumber, Integer pageSize);




    /**
     * 会员卡月交易统计接口
     * @param cardId
     * @param type
     * @param date
     * @return
     */
   public Map<String,Object> getCardTransMonthStatistics(String cardId,String type,String date);



    /**
     *充值/消费记录展示
     * @param pageNumber
     * @param pageSize
     * @param cardNumber
     * @param startDate
     * @param endDate
     * @return
     */
   public Page<Record> getTransByParams(Integer pageNumber,Integer pageSize,String cardId,String cardNumber,String startDate,String endDate,String flag,String beanCouponsFlag);

    public Page<FinaCardTransactions> getTransByParams2(Integer pageNumber, Integer pageSize,String cardId, String cardNumber
            , String startDate, String endDate,String flag,String beanCouponsFlag);

    public List<Record> getTransListByParams(String cardId,String cardNumber,String startDate,String endDate,String flag);
    
    public List<Record> getTransListByParams2(String cardId,String cardNumber,String startDate,String endDate,String flag);

    /**
     * 修改备注
      * @param cardTran
     * @param userId
     * @return
     */
   public boolean updateRemark(FinaCardTransactions cardTran,String oldDescribe,String userId);


    /**
     * 设置会员卡交易对象参数
     * @param expenseId
     * @param cardId
     * @param type
     * @param inOutFlag
     * @param amount
     * @param times
     * @param describe
     * @return
     */
   public FinaCardTransactions setParams(String expenseId,String cardId,String type,String inOutFlag,Double amount,Double times,Double points
   		,Double integrals,Double beanCoupons,String describe,Date startDate,Date endDate,FinaMembershipCard card);


    /**
     * 转移会员卡升级-处理会员卡交易记录
     * @param oldCard
     * @param newCard
     * @param remark
     * @return
     */
   public List<FinaCardTransactions> dealCardTransCardTransfer(FinaCardTransfer cardTransfer,FinaMembershipCard oldCard,FinaMembershipCard newCard,String remark);


    /**
     * 会员卡交易快照
     * @param card
     * @param tran
     */
   public void tranSnapshot(FinaMembershipCard card,FinaCardTransactions tran);


    /**
     * 会员卡交易快照2
     * @param cardList
     * @param transList
     */
   public void tranSnapshotTwo(List<FinaMembershipCard> cardList,List<FinaCardTransactions> transList);


    /**
     *获取会员卡交易记录
     * @param tran
     * @return
     */
   public List<Record> getCardTrans(FinaCardTransactions tran,String date);




    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardTransactions findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardTransactions
     */
    public List<FinaCardTransactions> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardTransactions model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaCardTransactions model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaCardTransactions model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardTransactions model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}