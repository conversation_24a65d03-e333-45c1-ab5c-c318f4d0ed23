package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeProvidentFund;

import java.util.List;

public interface PersOrgEmployeeProvidentFundService {

    PersOrgEmployeeProvidentFund findById(Object id);

    List<PersOrgEmployeeProvidentFund> findEmployeeProvidentFundList(String yearMonth, List<String> empIds);

    List<PersOrgEmployeeProvidentFund> findEmployeeProvidentFundListByApplyId(String id);

    PersOrgEmployeeProvidentFund findEmployeeProvidentFundMaxMonth(String empId);
}
