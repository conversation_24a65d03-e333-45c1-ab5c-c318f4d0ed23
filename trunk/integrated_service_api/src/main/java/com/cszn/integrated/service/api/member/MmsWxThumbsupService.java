package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxThumbsup;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

/**
 * 微信点赞
 */
public interface MmsWxThumbsupService  {

    public String saveMmsWxThumbsup(MmsWxThumbsup wxThumbsup);

    public Long getThumbsupCount(String userId);


    /**
     * genuine图文id获取点赞
     * @param momentId
     * @return
     */
    public List<Record> getThumbsup(String momentId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxThumbsup findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxThumbsup
     */
    public List<MmsWxThumbsup> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxThumbsup model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxThumbsup model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxThumbsup model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxThumbsup model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}