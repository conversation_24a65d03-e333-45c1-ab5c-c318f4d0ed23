package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseBedMarkupType;
import com.cszn.integrated.service.entity.main.MainBedStatus;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseBedMarkupTypeService {

    /**
     * 查询可用的额外价格List
     * @return
     */
    public List<MainBaseBedMarkupType> findBedMarkupTypeList();

    /**
     * 标记价格分页
     * @param pageNumber
     * @param pageSize
     * @param bedMarkupType
     * @return
     */
    public Page<MainBaseBedMarkupType> findListPage(Integer pageNumber, Integer pageSize, MainBaseBedMarkupType bedMarkupType);



    /**
     * 保存床位状态
     * @param bedStatus
     * @param userId
     * @return
     */
    public boolean saveBedMarkupType(MainBaseBedMarkupType bedStatus,String userId);


    /**
     * 通过名字判断是否存在
     * @param name
     * @return
     */
    public MainBaseBedMarkupType findBedMarkupTypeByName(String name);


    /**
     * 删除床位状态
     * @param id
     * @param userId
     * @return
     */
    public boolean delBedMarkupType(String id,String userId);


    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelBedMarkupType(List<MainBaseBedMarkupType> list,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseBedMarkupType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainBaseBedMarkupType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseBedMarkupType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseBedMarkupType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseBedMarkupType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseBedMarkupType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseBedMarkupType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseBedMarkupType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseBedMarkupType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
