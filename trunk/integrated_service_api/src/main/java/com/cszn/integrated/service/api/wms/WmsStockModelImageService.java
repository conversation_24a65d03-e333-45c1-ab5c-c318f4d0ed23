package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsStockModelImage;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface WmsStockModelImageService {

    public List<WmsStockModelImage> findAllImageByModelId(String modelId);

    public WmsStockModelImage findMainImage(String modelId);

    public boolean saveModelImage(WmsStockModelImage image, String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsStockModelImage findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsStockModelImage> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsStockModelImage model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsStockModelImage model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsStockModelImage model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsStockModelImage model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsStockModelImage> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsStockModelImage> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsStockModelImage> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
