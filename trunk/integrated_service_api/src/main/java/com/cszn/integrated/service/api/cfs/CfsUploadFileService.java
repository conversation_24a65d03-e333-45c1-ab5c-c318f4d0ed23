package com.cszn.integrated.service.api.cfs;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.cfs.CfsUploadFile;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CfsUploadFileService  {

    public CfsUploadFile getByName(String name);

    public CfsUploadFile getBySrc(String src);

    /**
     * 根据id获取显示路径
     * @param id
     * @return
     */
    public String getSrcById(String id);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CfsUploadFile findById(Object id);


    /**
     * find all model
     *
     * @return all <CfsUploadFile
     */
    public List<CfsUploadFile> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CfsUploadFile model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CfsUploadFile model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CfsUploadFile model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CfsUploadFile model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CfsUploadFile> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CfsUploadFile> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CfsUploadFile> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}