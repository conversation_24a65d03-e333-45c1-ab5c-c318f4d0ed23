package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardGiveRule;
import com.cszn.integrated.service.entity.fina.FinaCardGiveRuleDetail;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface FinaCardGiveRuleService {

    List<FinaCardGiveRule> find(String sql, Object...params);

    public Page<FinaCardGiveRule> findList(FinaCardGiveRule cardGiveRule, Integer pageNumber, Integer pageSize);


    public boolean saveRuleAndDetail(FinaCardGiveRule cardGiveRule, List<FinaCardGiveRuleDetail> detailList, String userId);


    public boolean delRuleAndDetail(String id,String userId);

    public List<FinaCardGiveRule> getRules();

    /**
     * 规则重复
     * @param rule
     * @return
     */
    public boolean getDistinct(FinaCardGiveRule rule);


    public boolean batchDelRule(List<FinaCardGiveRule> list,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardGiveRule findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardGiveRule
     */
    public List<FinaCardGiveRule> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardGiveRule model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaCardGiveRule model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaCardGiveRule model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardGiveRule model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}