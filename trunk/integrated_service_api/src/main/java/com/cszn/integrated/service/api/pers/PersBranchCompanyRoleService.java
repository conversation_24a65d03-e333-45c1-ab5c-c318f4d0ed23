package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.pers.PersBranchCompanyRole;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeePerformance;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PersBranchCompanyRoleService {

    public void calculationUnderlingPerformanceTotalAmount(List<ZTree> zTreeList,List<PersOrgEmployeePerformance> performanceList);

    public void calculationTeamPerformance(List<ZTree> zTreeList, String month, List<PersOrgEmployeePerformance> performanceList, Map<String, Set<String>> stringSetMap);

    public List<PersBranchCompanyRole> getBranchCompanyRoleByOrgId(String orgId);

    public boolean saveBranchCompanyRole(PersBranchCompanyRole role,String userId);

    public Page<PersBranchCompanyRole> pageList(int pageNumber,int pageSize,PersBranchCompanyRole role);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersBranchCompanyRole findById(Object id);


    /**
     * find all model
     *
     * @return all <PersBranchCompanyRole
     */
    public List<PersBranchCompanyRole> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersBranchCompanyRole model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersBranchCompanyRole model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersBranchCompanyRole model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersBranchCompanyRole model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersBranchCompanyRole> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersBranchCompanyRole> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersBranchCompanyRole> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
