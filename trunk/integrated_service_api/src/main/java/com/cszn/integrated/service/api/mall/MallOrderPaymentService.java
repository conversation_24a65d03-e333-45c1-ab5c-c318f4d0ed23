package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallOrderPayment;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MallOrderPaymentService {

    public List<MallOrderPayment> getOrderPaymentList(String orderId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallOrderPayment findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallOrderPayment> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallOrderPayment model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallOrderPayment model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallOrderPayment model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallOrderPayment model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);

}
