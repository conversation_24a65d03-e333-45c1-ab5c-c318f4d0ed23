package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCertificateType;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface PersOrgEmployeeCertificateTypeService {

    PersOrgEmployeeCertificateType findById(Object id);

    Page<PersOrgEmployeeCertificateType> findPageList(int page,int limit,PersOrgEmployeeCertificateType certificateType);

    boolean saveCertificateType(PersOrgEmployeeCertificateType certificateType,String userId);

    List<PersOrgEmployeeCertificateType> findCertificateTypeList(String isEnable);
}
