package com.cszn.integrated.service.api.sys;

import com.cszn.integrated.service.entity.common.XmSelect;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface UserService  {

    public Page<Record> findOfficeUserList(int pageNumber, int pageSize, PersOrgEmployee orgEmployee,String officeId);

    public boolean updateUser(PersOrgEmployee employee, String userId);

    public User getUserByEmpId(String employeeId);

    String getUserGroupIdByUserId(String userId);

    public List<User> findUnlockUserList();
    
    /**
     * find all model
     *
     * @return all <XmSelect
     */
    public List<XmSelect> userXmSelectTree();

    public boolean updateSaveUser(PersOrgEmployee employee, String userId);

    public boolean addSaveUser(PersOrgEmployee employee, String userId);

    public boolean delUser(String id,String userId);

    public boolean saveUser(PersOrgEmployee employee,String userId);

    public void createQiYeUser();

    /**
     * 判断名字是否存在
     * @param name
     * @param id
     * @return
     */
    public boolean nameIsExist(String name,String id);

    /**
     * 判断手机号码是否存在
     * @param phoneNumber
     * @param id
     * @return
     */
    public boolean phoneNumberIsExist(String phoneNumber,String id);

    /**
     * 重置密码
     * @param id
     * @param password
     * @return
     */
    public boolean resetPassword(String id,String password,String userId);

    /**
     * 锁定与解锁
     * @param user
     * @param userId
     * @return
     */
    public boolean lockUser(User user,String userId);


    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param user
     * @return
     */
    public Page<Record> findList(int pageNumber, int pageSize,User user,String officeId);


    /**
     * 辅助接口：查询所有用户
     * @param pageNumber
     * @param pageSize
     * @param user
     * @return
     */
    public Page<Record> findAllUsers(Integer pageNumber,Integer pageSize,User user,String officeId);


    public Page<User> paginateUserByCondition(User user, int pageNumber, int pageSize);

    /**
     * 分页
     *
     * @param user
     * @param pageSize
     * @return
     */
    public Page<User> paginateByCondition(User user, int pageNumber, int pageSize);
    
    public Page<Record> pageByEmpId(final String empId, int pageNumber, int pageSize);
    
    public List<User> findUserList();

    public Ret loginByUsername(String userName, String pwd);
    
    public Ret loginByWx(String userName, String pwd);

    public User getByUserName(String userName);
    
    public boolean verifyOldPassword(final String inputOldPwd, final String oldPwd, final String oldSalt);
    
    public boolean saveUser(User user, final String roleIds,String branchOfficeId,String systemType);
    
    public boolean modifyPwdSave(User user);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public User findById(Object id);

    /**
     * find all model
     *
     * @return all <User
     */
    public List<User> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(User model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(User model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(User model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(User model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}