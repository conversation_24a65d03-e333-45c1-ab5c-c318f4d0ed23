package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaExpenseRecordTouristDetail;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaExpenseRecordTouristDetailService {


    public List<Record> findTouristDetailListByUniqueId(String uniqueId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordTouristDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordTourist
     */
    public List<FinaExpenseRecordTouristDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordTouristDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordTouristDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordTouristDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordTouristDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordTouristDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordTouristDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordTouristDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
