package com.cszn.integrated.service.api.fina;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.fina.FinaCardTransfer;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardTransferService  {


    /**
     * 获取未同步的数据
     * @return
     */
    public List<FinaCardTransfer> getIsNotSync();

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardTransfer findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardTransfer
     */
    public List<FinaCardTransfer> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardTransfer model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardTransfer model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardTransfer model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardTransfer model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardTransfer> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardTransfer> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardTransfer> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}