package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeRemind;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeRemindService {

    public Page<Record> pageList(int pageNumber,int pageSize,PersOrgEmployeeRemind remind);

    public boolean saveRemind(PersOrgEmployeeRemind employeeRemind,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeRemind findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeRemind> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeRemind model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeRemind model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeRemind model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeRemind model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeRemind> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeRemind> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeRemind> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
