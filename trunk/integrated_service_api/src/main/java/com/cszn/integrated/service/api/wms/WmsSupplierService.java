package com.cszn.integrated.service.api.wms;

import java.util.List;

import com.cszn.integrated.service.entity.wms.WmsSuppliers;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface WmsSupplierService {


    /**
     * 保存供应商
     * @param wmsSuppliers
     * @return
     */
    public boolean saveSupplier(WmsSuppliers wmsSuppliers, List<String> goodsTypeIds);

    /**
     * 供应商分页
     * @param pageNumber
     * @param pageSize
     * @param wmsSuppliers
     * @return
     */
    public Page<WmsSuppliers> findSupplierPageList(Integer pageNumber, Integer pageSize, WmsSuppliers wmsSuppliers);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsSuppliers findById(Object id);

    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsSuppliers> findAll();
    
    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsSuppliers> findList();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsSuppliers model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsSuppliers model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsSuppliers model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsSuppliers model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsSuppliers> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsSuppliers> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsSuppliers> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
