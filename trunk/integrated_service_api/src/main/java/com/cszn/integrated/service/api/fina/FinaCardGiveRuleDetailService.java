package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardGiveRule;
import com.cszn.integrated.service.entity.fina.FinaCardGiveRuleDetail;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface FinaCardGiveRuleDetailService {

    public List<FinaCardGiveRuleDetail> findByRuleId(String sql, String ruleId);

    public List<FinaCardGiveRuleDetail> getRuleDetails(FinaCardGiveRule finaCardGiveRule);

    public List<FinaCardGiveRuleDetail> getCardGiveRuleDetailsByRuleId(String ruleId);

    public boolean delRuleDetail(String id);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardGiveRuleDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardGiveRuleDetail
     */
    public List<FinaCardGiveRuleDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardGiveRuleDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaCardGiveRuleDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaCardGiveRuleDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardGiveRuleDetail model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}