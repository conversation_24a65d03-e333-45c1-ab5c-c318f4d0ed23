package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainDictionaries;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainDictionariesService {

    /**
     *
     * @param typeId
     * @return
     */
    public List<MainDictionaries> findDictionariesByTypeId(String typeId);

    /**
     * 字典分页
     * @param pageNumber
     * @param pageSize
     * @param dictionaries
     * @return
     */
    public Page<MainDictionaries> pageList(Integer pageNumber,Integer pageSize,MainDictionaries dictionaries);


    /**
     * 保存字典
     * @param dictionaries
     * @return
     */
    public boolean saveDictionaries(MainDictionaries dictionaries,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainDictionaries findById(Object id);


    /**
     * find all model
     *
     * @return all <MainDictionaries
     */
    public List<MainDictionaries> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainDictionaries model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainDictionaries model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainDictionaries model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainDictionaries model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainDictionaries> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainDictionaries> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainDictionaries> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
