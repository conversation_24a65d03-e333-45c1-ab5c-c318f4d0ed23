package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainDeductMultipleConfig;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainDeductMultipleConfigService {
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainDeductMultipleConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<MainDeductMultipleConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainDeductMultipleConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainDeductMultipleConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainDeductMultipleConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainDeductMultipleConfig model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainDeductMultipleConfig> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainDeductMultipleConfig> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainDeductMultipleConfig> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
