package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainPaymentWays;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainPaymentWaysService {

    public boolean saveWay(MainPaymentWays way,String userId);

    public Page<MainPaymentWays> pageList(Integer pageNumber,Integer pageSize,MainPaymentWays way);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainPaymentWays findById(Object id);


    /**
     * find all model
     *
     * @return all <MainPaymentWays
     */
    public List<MainPaymentWays> findAll();
    
    
    /**
     * find all model
     *
     * @return all <MainPaymentWays
     */
    public List<MainPaymentWays> findList();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainPaymentWays model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainPaymentWays model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainPaymentWays model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainPaymentWays model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainPaymentWays> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainPaymentWays> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainPaymentWays> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
