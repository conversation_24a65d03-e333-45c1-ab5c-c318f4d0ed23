package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsActivityQrcode;
import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsActivityMember;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsActivityMemberService  {

    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param member
     * @return
     */
    public Page<Record> findList(Integer pageNumber, Integer pageSize,MmsActivityMember member);


    /**
     * 获取对象
     * @param id
     * @return
     */
    public MmsActivityMember get(String id);



    /**
     * 删除对象
     * @param id
     * @return
     */
    public boolean delActivityMember(String id,String userId);


    /**
     * 通过姓名电话查重
     * @param telephone
     * @return
     */
    public List<MmsActivityMember> getActByPhone(String telephone);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsActivityMember findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsActivityMember
     */
    public List<MmsActivityMember> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsActivityMember model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsActivityMember model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsActivityMember model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsActivityMember model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsActivityMember> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsActivityMember> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsActivityMember> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}