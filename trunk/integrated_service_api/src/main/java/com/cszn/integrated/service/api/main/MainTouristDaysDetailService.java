package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainTouristDaysDetail;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainTouristDaysDetailService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainTouristDaysDetail findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainTouristDaysDetail
     */
    public List<MainTouristDaysDetail> findList(String daysId);


    /**
     * find all model
     *
     * @return all <MainTouristDaysDetail
     */
    public List<MainTouristDaysDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainTouristDaysDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainTouristDaysDetail model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainTouristDaysDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainTouristDaysDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainTouristDaysDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainTouristDaysDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainTouristDaysDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}