package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.sys.Log;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface LogService  {

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<Record> paginateByCondition(String systemType, final String startDate, final String endDate, final String userAccount, final String userName, int pageNumber, int pageSize);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public Log findById(Object id);


    /**
     * find all model
     *
     * @return all <Log
     */
    public List<Log> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(Log model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(Log model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(Log model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(Log model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}