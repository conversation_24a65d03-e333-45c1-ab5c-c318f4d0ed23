package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardRecharge;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.ParseException;
import java.util.List;

public interface FinaCardRechargeService {
	
    /**
     * 模板导入批量充值
     * @param file
     * @param fileName
     * @return
     * @throws FileNotFoundException
     * @throws ParseException
     */
    public Ret importRecharge(File file, String fileName, String userId) throws Exception;
	
	/**
	 * 获取充值统计数据
	 * @param type
	 * @param yearMonthDay
	 * @param yearMonth
	 * @param year
	 * @return
	 */
	public List<Record> rechargeStatistics(String type, String yearMonthDay, String yearMonth, String year);
	
	/**
	 * 获取充值统计明细分页
	 * @param type
	 * @param yearMonthDay
	 * @param yearMonth
	 * @param year
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<Record> rechargeStatisticsPage(String type, String yearMonthDay, String yearMonth, String year,Integer pageNumber, Integer pageSize);
	
	/**
	 * 获取充值统计明细列表
	 * @param type
	 * @param yearMonthDay
	 * @param yearMonth
	 * @param year
	 * @return
	 */
	public List<Record> rechargeStatisticsList(String type, String yearMonthDay, String yearMonth, String year);
	
	/**
	 * 获取充值日报列表
	 * @param rechargeDate
	 * @return
	 */
	public List<Record> rechargeDailyStatistics(String rechargeDate);

    /**
     * 获取充值记录
     * @param cardNumber
     * @param fullName
     * @param isReview
     * @param startDate
     * @param endDate
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<Record> rechargePage(String cardNumber,String fullName,String isReview,String delFlag,String startDate,String endDate,String createBy,String orderBy,String isAuto,Integer pageNumber, Integer pageSize);

    /**
     * 查找未结算积分的充值列表
     * @param cardId
     * @return
     */
    public List<FinaCardRecharge> findIsNotCountList(String cardId);
	
    /**
     * 会员卡批量充值
     * @param rechargeList
     * @param userId
     * @return
     */
    public boolean batchRecharge(List<FinaCardRecharge> rechargeList,String userId);

    /**
     * 通过会员卡号获取充值记录
     * @param pageNumber
     * @param pageSize
     * @param cardId
     * @return
     */
    public Page<FinaCardRecharge> cancelRechargePage(Integer pageNumber,Integer pageSize,String cardId);

    /**
     * 撤销会员卡充值记录
     * @param recharge 充值记录
     * @return
     */
    public boolean cancelRechargeRecord(FinaCardRecharge recharge, FinaMembershipCard memberCard,String userId);
    
    /**
     * 根据会员卡id获取总的充值积分
     *
     * @param cardId
     * @return
     */
    public Double getTotalRechargeIntegralsByCardId(String cardId);

    /**
     * 保存记录
     * @param cardRecharge
     * @param userId
     * @return
     */
    public boolean saveRecharge(FinaCardRecharge cardRecharge, String userId);

    /**
     * 插入充值记录表
     * @param finaCardRecharge
     * @param userId
     * @return
     */
    public boolean saveCardRecharge(FinaCardRecharge finaCardRecharge,String userId);
    
    /**
     * 审核充值记录
     * @param rechargeId
     * @param userId
     * @return
     */
    public boolean saveReviewRecharge(String rechargeId,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardRecharge findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardRecharge
     */
    public List<FinaCardRecharge> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardRecharge model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaCardRecharge model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaCardRecharge model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardRecharge model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}