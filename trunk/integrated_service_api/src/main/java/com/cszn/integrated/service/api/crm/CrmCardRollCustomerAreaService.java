package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRollCustomerArea;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollCustomerAreaService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollCustomerArea findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollCustomerArea
     */
    public List<CrmCardRollCustomerArea> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollCustomerArea model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollCustomerArea model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollCustomerArea model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollCustomerArea model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollCustomerArea> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollCustomerArea> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollCustomerArea> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}