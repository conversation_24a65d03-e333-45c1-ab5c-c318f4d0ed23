package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsActivityQrcode;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsActivityQrcodeService  {


    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param name
     * @param officeId
     * @return
     */
    public Page<Record> findList(Integer pageNumber,Integer pageSize,String name,String officeId);


    /**
     * 获取对象
     * @param id
     * @return
     */
    public MmsActivityQrcode get(String id);



    /**
     * 删除对象
     * @param id
     * @return
     */
    public boolean delActivityQrcode(String id,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsActivityQrcode findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsActivityQrcode
     */
    public List<MmsActivityQrcode> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsActivityQrcode model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsActivityQrcode model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsActivityQrcode model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsActivityQrcode model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsActivityQrcode> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsActivityQrcode> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsActivityQrcode> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}