package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseDoor;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MainBaseDoorService  {

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainBaseDoor> paginateByCondition(int pageNumber, int pageSize, MainBaseDoor mainBaseDoor);
    
    /**
     * 检查门编号是否重复
     * 
     * @param doorNo
     * @return
     */
    public boolean checkDoorNo(final String doorNo);

    /**
     * findByBaseId
     *
     * @return List<MainBaseDoor>
     */
    public List<MainBaseDoor> findByBaseId(String baseId);
    
    /**
     * 保存方法
     * 
     * @param model
     * @return
     */
    public boolean doorSave(MainBaseDoor model);
    
    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean deleteDoor(MainBaseDoor model);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseDoor findById(Object id);


    /**
     * find all model
     *
     * @return all <Role
     */
    public List<MainBaseDoor> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseDoor model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainBaseDoor model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainBaseDoor model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseDoor model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}