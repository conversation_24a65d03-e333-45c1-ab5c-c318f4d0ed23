package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsCustomerFaceData;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MmsCustomerFaceDataService  {

    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsCustomerFaceData findById(Object id);

    
    /**
     * find all model
     *
     * @return all <MmsCustomerFaceData
     */
    public List<MmsCustomerFaceData> findAll();
    
    
    /**
     * find all model
     *
     * @return all <MmsCustomerFaceData
     */
    public List<MmsCustomerFaceData> findList(String faceId);


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsCustomerFaceData model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsCustomerFaceData model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsCustomerFaceData model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsCustomerFaceData model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsCustomerFaceData> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsCustomerFaceData> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsCustomerFaceData> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}