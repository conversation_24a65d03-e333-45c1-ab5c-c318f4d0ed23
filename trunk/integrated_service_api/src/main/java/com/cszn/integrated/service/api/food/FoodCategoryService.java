package com.cszn.integrated.service.api.food;

import java.util.List;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.food.FoodCategory;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FoodCategoryService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FoodCategory findById(Object id);

    /**
     * 类别表格树
     * @return
     */
    public List<ZTree> findCategoryTableZtree(boolean isEnabled);
    
    /**
     * 类别树
     * @return
     */
    public List<ZTree> findCategoryTree(boolean isEnabled);

    /**
     * find all model
     *
     * @return all <FoodCategory
     */
    public List<FoodCategory> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FoodCategory model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FoodCategory model);
    
    /**
     * 保存类别
     * @param model
     * @return
     */
    public boolean saveCategory(FoodCategory model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FoodCategory model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FoodCategory model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FoodCategory> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FoodCategory> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FoodCategory> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}