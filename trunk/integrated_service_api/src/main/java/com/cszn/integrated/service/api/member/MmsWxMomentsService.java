package com.cszn.integrated.service.api.member;

import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.member.MmsWxMoments;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MmsWxMomentsService  {

    public Page<Object> getWxMomentsList(Integer pageNumber, Integer pageSize, String userId);


    public String saveMmsWxMoments(MmsWxMoments wxMoments);


    public JSONArray getSubMoments(String userId);


    public Long getMomentCount(String userId);


    public Record getMomentById(String momentId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxMoments findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxMoments
     */
    public List<MmsWxMoments> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxMoments model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxMoments model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxMoments model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxMoments model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}