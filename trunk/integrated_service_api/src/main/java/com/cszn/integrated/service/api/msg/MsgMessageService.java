package com.cszn.integrated.service.api.msg;

import com.cszn.integrated.service.entity.msg.MsgMessage;
import com.cszn.integrated.service.entity.msg.MsgMessageUser;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MsgMessageService  {

    /**
     *获取消息分页
     * @param pageNumber
     * @param pageSize
     * @param msgUser
     * @return
     */
    public Page<Record> getMsgPage(Integer pageNumber,Integer pageSize,MsgMessageUser msgUser);


    /**
     * 消息列表分页
     * @param pageNumber
     * @param pageSize
     * @param msgMessage
     * @return
     */
    public Page<MsgMessage> findListPage(Integer pageNumber,Integer pageSize,MsgMessage msgMessage);



    /**
     *根据用户id获取用户所属消息
     * @param pageNum
     * @param pageSize
     * @param msgMessageUser
     * @return
     */
    public Page<Record> findPage(Integer pageNum, Integer pageSize, MsgMessageUser msgMessageUser,String label);


    /**
     * 获取用户未读消息
     * @param userId
     * @return
     */
    public Long getNotReadMsgCount(String userId);


    /**
     * 标记用户已读
     * @param list
     * @param userId
     * @return
     */
    public boolean signRead(List<MsgMessage> list,String userId);


    /**
     * 获取对象
     * @param id
     */
    public MsgMessage get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MsgMessage findById(Object id);


    /**
     * find all model
     *
     * @return all <MsgMessage
     */
    public List<MsgMessage> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MsgMessage model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MsgMessage model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MsgMessage model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MsgMessage model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MsgMessage> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MsgMessage> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MsgMessage> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}