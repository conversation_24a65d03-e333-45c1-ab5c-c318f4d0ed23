package com.cszn.integrated.service.api.sys;

import java.util.List;
import java.util.Set;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.sys.Menu;
import com.cszn.integrated.service.entity.sys.Role;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MenuService  {

	/**
	 * 通过获取该系统下所有的菜单权限
	 * @param systemType
	 * @return
	 */
	public Set<String> findSystemAllMenu(String systemType);

	public Set<String> findPermissionByRole(List<Role> roleList);
    
    /**
     * 根据菜单路径获取菜单
     * @return
     */
    public Menu getByUrl(final String url);
	
	/**
     * 查询菜单树表格
     * @return
     */
    public List<ZTree> menuTreeTable(String systemType);
	
	/**
	 * 查询菜单表单树
	 * @return
	 */
	public List<ZTree> menuFormTree(String systemType);

	/**
	 * 查询角色菜单树
	 * @param orgId
	 * @param roleId
	 * @return
	 */
	public List<ZTree> roleMenuTree(final String roleId,final String systemType);
	
	/**
	 * 菜单排序批量更新
	 * @param menuId
	 * @return
	 */
	public boolean sortBatchSave(List<Menu> menuList);
	
	/**
	 * 菜单保存
	 * @param menu
	 * @return
	 */
	public boolean menuSave(Menu menu);
	
	/**
	 * 菜单删除（包括子菜单）
	 * @param menuId
	 * @return
	 */
	public boolean menuDel(String menuId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public Menu findById(Object id);


    /**
     * find all model
     *
     * @return all <Menu
     */
    public List<Menu> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(Menu model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(Menu model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(Menu model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(Menu model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}