package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersLiabilityInsuranceApplyRecord;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface PersLiabilityInsuranceApplyRecordService {

    public PersLiabilityInsuranceApplyRecord findById(Object id);

    Page<Record> pageList(Integer page, Integer limit , PersLiabilityInsuranceApplyRecord record, String userId);
}
