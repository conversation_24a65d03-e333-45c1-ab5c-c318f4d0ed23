package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseHandCard;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBaseHandCardService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainBaseHandCard> paginateByCondition(MainBaseHandCard model, int pageNumber, int pageSize);
    
    
    /**
     * find all model
     *
     * @return all <MainBaseHandCard
     */
    public List<MainBaseHandCard> findListByBaseId(String baseId);
    
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseHandCard findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseHandCard
     */
    public List<MainBaseHandCard> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseHandCard model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseHandCard model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseHandCard model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseHandCard model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseHandCard> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseHandCard> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseHandCard> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}