package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRollBranchOffice;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollBranchOfficeService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollBranchOffice findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollBranchOffice
     */
    public List<CrmCardRollBranchOffice> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollBranchOffice model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollBranchOffice model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollBranchOffice model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollBranchOffice model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollBranchOffice> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollBranchOffice> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollBranchOffice> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}