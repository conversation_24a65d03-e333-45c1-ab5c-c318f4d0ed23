package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainTouristItem;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainTouristItemService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainTouristItem findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainTouristItem
     */
    public List<MainTouristItem> findList();


    /**
     * find all model
     *
     * @return all <MainTouristItem
     */
    public List<MainTouristItem> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainTouristItem model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainTouristItem model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainTouristItem model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainTouristItem model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainTouristItem> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainTouristItem> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainTouristItem> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}