package com.cszn.integrated.service.api.crm;

import java.util.List;

import com.cszn.integrated.service.entity.crm.CrmCardRollRecordVerify;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;

public interface CrmCardRollRecordVerifyService  {

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecordVerify> paginate(CrmCardRollRecordVerify model, int page, int pageSize);
    
    /**
     * find all model
     *
     * @return all <CrmCardRollRecordVerify
     */
    public List<Record> findCustomerRollList(CrmCardRollRecordVerify model);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecordVerify findByRecordId(String recordId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecordVerify findByRollNumber(String rollNumber);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecordVerify findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollRecordVerify
     */
    public List<CrmCardRollRecordVerify> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollRecordVerify model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollRecordVerify model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollRecordVerify model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollRecordVerify model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecordVerify> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollRecordVerify> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollRecordVerify> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}