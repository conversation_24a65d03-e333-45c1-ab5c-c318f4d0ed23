package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import java.util.List;
import java.util.Map;

public interface MainMembershipCardTypeService {


    /**
     * 获取短信记录没有添加的会员卡类型
     * @return
     */
    public List<MainMembershipCardType> findCardTypeList(String templateId);

    /**
     * 通过会员卡获取会员卡数据
     * @param cardNumbers
     * @return
     */
    public List<Map<String,Object>> getCardInfoByCardNumber(String[] cardNumbers);

    /**
     * 通过卡类型名称或卡前缀判断是否存在
     * @param id
     * @param name
     * @param
     * @return
     */
    public MainMembershipCardType cardTypeIsExist(String id,String name,String prefix);

    /**
     *
     * @param sql
     * @param params
     * @return
     */
    public MainMembershipCardType findFirst(String sql, Object... params);

    /**
     *根据id查询对象
    * @param id
     * @return
     */
    public MainMembershipCardType get(String id);




    /**
     * 会员卡类别条件分页查询
     * @param pageNumber
     * @param pageSize
     * @param type
     * @return
     */
    public Page<MainMembershipCardType> findListPage(Integer pageNumber, Integer pageSize, MainMembershipCardType type);


    /**
     * 新增或修改对象
     * @param type
     * @param userId
     * @return
     */
    public boolean saveMainMembershipCardType(MainMembershipCardType type, String userId);




    /**
     * 批量删除
     * @param list
     * @return
     */
    public boolean batchDelType(List<MainMembershipCardType> list, String userId);


    /**
     * 查询所有未删除对象
     * @return
     */
    public List<MainMembershipCardType> findList(MainMembershipCardType cardType);


    /**
     * 保存：去重
     * @param type
     * @return
     */
    public List<MainMembershipCardType> getDistinctCardType(MainMembershipCardType type);


    /**
     * 保存：去重
     * @param type
     * @return
     */
    public String getDistinct(MainMembershipCardType type);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainMembershipCardType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainMembershipCardType
     */
    public List<MainMembershipCardType> findAll();



    /**
     * 根据id删除对象
     * @param id
     * @return
     */
    public boolean delete(String id, String userId);



    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainMembershipCardType model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainMembershipCardType model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainMembershipCardType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainMembershipCardType model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}