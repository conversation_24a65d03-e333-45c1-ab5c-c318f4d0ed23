package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallActivityDiscountConfig;
import com.cszn.integrated.service.entity.mall.MallActivityGiveConfig;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MallActivityGiveConfigService {

    public List<MallActivityGiveConfig> findGiveByActivityId(String activityId);

    public MallActivityGiveConfig findGiveModelByActivityId(String activityId,String stockModelId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallActivityGiveConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallActivityGiveConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallActivityGiveConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallActivityGiveConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallActivityGiveConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallActivityGiveConfig model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
