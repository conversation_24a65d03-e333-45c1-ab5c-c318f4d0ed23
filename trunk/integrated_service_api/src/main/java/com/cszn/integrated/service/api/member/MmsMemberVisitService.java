package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsMemberVisit;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsMemberVisitService  {

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsMemberVisit> paginateByCondition(MmsMemberVisit model, int pageNumber, int pageSize);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMemberVisit findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMemberVisit
     */
    public List<MmsMemberVisit> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMemberVisit model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsMemberVisit model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsMemberVisit model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMemberVisit model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}