package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordDeductionCard;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaExpenseRecordDeductionCardService  {


    /**
     * 设置更换会员卡新增参数
     * @param erId
     * @param uniqueId
     * @param cardNumber
     * @param startTime
     * @param endTime
     * @return
     */
    public FinaExpenseRecordDeductionCard setSave(String erId,String uniqueId,String cardNumber,String startTime,String endTime,String remark,String useFlag,String type,String isAll);


    /**
     * 获取原始会员卡
     * @param id
     * @return
     */
    public FinaExpenseRecordDeductionCard getOriginalCard(String id);



    /**
     * 根据账单id获取变更会员卡记录
     * @param id
     * @return
     */
    public List<Record> getChangeCardListByExpenseId(String id);


    /**
     * 根据应用号，基地和入住号获取变更会员卡记录
     * @param appNo
     * @param baseId
     * @param checkinNo
     * @return
     */
    public List<Record> getChangeCardListByCheckinNo(String appNo,String baseId,String checkinNo);


    /**
     * 转移会员卡升级：根据主账单list和预订，旅游团list获取交换会员卡记录
     * @param mainList
     * @param bookAndTouristList
     * @return
     */
    public List<FinaExpenseRecordDeductionCard> getRecordByErs(List<FinaExpenseRecord> mainList, List<FinaExpenseRecord> bookAndTouristList, FinaMembershipCard oldCard,FinaMembershipCard newCard);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordDeductionCard findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordDeductionCard
     */
    public List<FinaExpenseRecordDeductionCard> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordDeductionCard model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordDeductionCard model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordDeductionCard model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordDeductionCard model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordDeductionCard> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordDeductionCard> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordDeductionCard> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}