package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersBranchCompanyRoleSalaryConfig;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersBranchCompanyRoleSalaryConfigService {




    public List<PersBranchCompanyRoleSalaryConfig> findListByRoleId(String roleId);


    public boolean saveConfig(List<PersBranchCompanyRoleSalaryConfig> configList,String roleId,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersBranchCompanyRoleSalaryConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <PersBranchCompanyRoleSalaryConfig
     */
    public List<PersBranchCompanyRoleSalaryConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersBranchCompanyRoleSalaryConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersBranchCompanyRoleSalaryConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersBranchCompanyRoleSalaryConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersBranchCompanyRoleSalaryConfig model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersBranchCompanyRoleSalaryConfig> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersBranchCompanyRoleSalaryConfig> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersBranchCompanyRoleSalaryConfig> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
