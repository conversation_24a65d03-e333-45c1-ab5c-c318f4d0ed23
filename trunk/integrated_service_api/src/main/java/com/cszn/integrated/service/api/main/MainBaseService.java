package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBase;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBaseService  {

    /**
     * 通过名字判断基地是否存在
     * @param id
     * @param name
     * @return
     */
    public MainBase baseIsExist(String id,String name);

    /**
     * 获取所有基地
     * @return
     */
    public List<MainBase> findBaseList();
    
    /**
     * 获取所有有效基地
     * @return
     */
    public List<MainBase> findEnableBaseList(MainBase model);
    
    /**
     * 获取所有基地
     * @return
     */
    public List<MainBase> findAllBase(MainBase model);


    /**
     * 基地列表
     * @param pageNumber
     * @param pageSize
     * @param base
     * @return
     */
    public Page<MainBase> findListPage(Integer pageNumber,Integer pageSize,MainBase base);


    /**
     * 根据id查询基地
     * @param id
     * @return
     */
    public MainBase get(String id);


    /**
     * 保存基地
     * @param base
     * @param userId
     * @return
     */
    public boolean saveBase(MainBase base,String userId);


    /**
     * 基地去重
     * @param base
     * @return
     */
    public String getDistinct(MainBase base);


    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelBase(List<MainBase> list,String userId);


    /**
     * 删除基地
     * @param id
     * @param userId
     * @return
     */
    public boolean delBase(String id,String userId);


    /**
     * 同步基地所属物数量
     * @return
     */
    public boolean snycBaseBelongNum(String id,Integer buildingNum,Integer roomNum,Integer bedNum,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBase findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBase
     */
    public List<MainBase> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBase model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBase model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBase model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBase model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBase> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBase> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBase> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}