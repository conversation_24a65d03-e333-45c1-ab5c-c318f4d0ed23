package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsMeasureHr;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MmsMeasureHrService  {


    /**
     * 获取会员心率数据
     * @param cardNumber
     * @param type
     * @return
     */
    public Map<String,List> memberHr(String cardNumber,String type);

    /**
     * 获取会员最新一条心率数据
     * @param cardNumber
     * @return
     */
    public Map<String,Object> latestHr(String cardNumber);


    /**
     * 获取心率数据
     * @param memberId
     * @param dataRecord
     * @return
     */
    public Map<String, Object> gethrData(String memberId, String dataRecord);


    /**
     * 保存心率数据
     * @return
     */
    public boolean saveHrData(String equipmentNo,String measureValue,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMeasureHr findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMeasureHr
     */
    public List<MmsMeasureHr> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMeasureHr model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsMeasureHr model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsMeasureHr model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMeasureHr model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsMeasureHr> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsMeasureHr> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsMeasureHr> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}