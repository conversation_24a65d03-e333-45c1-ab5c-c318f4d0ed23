package com.cszn.integrated.service.api.main;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBedType;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBedTypeService  {


    /**
     * 查询床位类型名字是否存在
     * @param id
     * @param name
     * @return
     */
    public MainBedType findBedTypeByIdAndName(String id,String name);


    /**
     * 通过基地id获取床位类型
     * @return
     */
    public List<MainBedType> findBedTypeListByBaseId();


    /**
     * 床位类型列表
     * @param pageNumber
     * @param pageSize
     * @param bedType
     * @return
     */
    public Page<MainBedType> findListPage(Integer pageNumber,Integer pageSize,MainBedType bedType);


    /**
     * 根据id获取床位类型对象
     * @param id
     * @return
     */
    public MainBedType get(String id);


    /**
     * 保存床位类型
     * @param bedType
     * @param userId
     * @return
     */
    public boolean saveBedType(MainBedType bedType,String userId);


    /**
     * 保存：去重
     * @param bedType
     * @return
     */
    public String getDistinct(MainBedType bedType);


    /**
     * 删除床位类型
     * @param id
     * @param userId
     * @return
     */
    public boolean delBedType(String id,String userId);


    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelBedType(List<MainBedType> list,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBedType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedType
     */
    public List<MainBedType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBedType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBedType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBedType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBedType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBedType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBedType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBedType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}