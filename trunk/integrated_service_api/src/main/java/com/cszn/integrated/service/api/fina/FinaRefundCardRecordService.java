package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRefundCardRecord;
import com.jfinal.plugin.activerecord.Page;

import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

public interface FinaRefundCardRecordService  {

    /**
     * 获取退款统计数据
     * @param type
     * @param yearMonthDay
     * @param yearMonth
     * @param year
     * @return
     */
    public List<Record> refundStatistics(String type, String yearMonthDay, String yearMonth, String year);

    /**
     * 获取退款列表数据
     * @param type
     * @param yearMonthDay
     * @param yearMonth
     * @param year
     * @return
     */
    public Page<Record> refundList(String type, String yearMonthDay, String yearMonth, String year, String branchOfficeName, Integer pageNumber, Integer pageSize);

	/**
     * find all model
     *
     * @return all <FinaRefundCardRecord
     */
    public List<FinaRefundCardRecord> findByCardIdList(String cardId);
    
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaRefundCardRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaRefundCardRecord
     */
    public List<FinaRefundCardRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaRefundCardRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaRefundCardRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaRefundCardRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaRefundCardRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaRefundCardRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaRefundCardRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaRefundCardRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}