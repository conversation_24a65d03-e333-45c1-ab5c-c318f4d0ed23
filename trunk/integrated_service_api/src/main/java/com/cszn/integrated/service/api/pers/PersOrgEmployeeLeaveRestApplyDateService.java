package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveRestApplyDate;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeLeaveRestApplyDateService {

    public PersOrgEmployeeLeaveRestApplyDate findById(Object id);

    public List<PersOrgEmployeeLeaveRestApplyDate> getLeaveRestApplyDateList(String leaveRestApplyId);

    public List<PersOrgEmployeeLeaveRestApplyDate> getEmpLeaveRestApplyDateListByDateRange(String empId, Date startDate,Date endDate,String[] statusArray);
}
