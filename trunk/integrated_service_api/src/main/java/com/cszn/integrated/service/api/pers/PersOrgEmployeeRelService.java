package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeRel;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeRelService  {

    public List<Record> getEmpDeptList(String empId);

    public List<Record> getEmpPosition(String empId,String deptId);

    public void addRel(List<PersOrgEmployeeRel> relList,String userId,String createBy);

    public boolean deleteRelByIds(List<String> ids,String userId);

    public PersOrgEmployeeRel getRel(String empId,String relationshipType,String relationshipId);

    public boolean deleteRel(String empId,String relDeptId,String relPositionId,String relRoleIdc,String userId);

    public PersOrgEmployeeRel getRelByList(List<PersOrgEmployeeRel> list,String relationshipId,List<String> excludeId);


    public List<PersOrgEmployeeRel> getRelList(String empId,String type);

    public PersOrgEmployeeRel findByGroupId(String empId, String groupId);

    public PersOrgEmployeeRel findByDeptId(String empId,String deptId);

	/**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<PersOrgEmployeeRel> paginateByCondition(PersOrgEmployeeRel orgEmployeeRel, int pageNumber, int pageSize);
	
	public List<PersOrgEmployeeRel> findListByEmpId(String empId, String isMain);
	
	public Ret empOrgSave(PersOrgEmployeeRel persOrgEmployeeRel);
	
	public Ret empOrgSave(List<PersOrgEmployeeRel> modelList);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeRel findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeRel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeRel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeRel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeRel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeRel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeRel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeRel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeRel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}