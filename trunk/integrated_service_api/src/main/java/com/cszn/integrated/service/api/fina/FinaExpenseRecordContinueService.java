package com.cszn.integrated.service.api.fina;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordContinue;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface FinaExpenseRecordContinueService  {


    public void setFc(FinaExpenseRecordContinue fc, String expenseId, String checkinNo, String bedId, Date startTime,Date endTime);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordContinue findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordContinue
     */
    public List<FinaExpenseRecordContinue> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordContinue model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordContinue model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordContinue model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordContinue model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordContinue> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordContinue> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordContinue> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}