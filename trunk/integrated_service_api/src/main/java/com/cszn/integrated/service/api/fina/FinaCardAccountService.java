package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardAccount;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface FinaCardAccountService  {

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<FinaCardAccount> paginateByCondition(FinaCardAccount model, int pageNumber, int pageSize);
    
    /**
     * 查询账户列表
     *
     * @return all <FinaCardAccount
     */
    public List<FinaCardAccount> findList(String payWay);
    
    /**
     * 账户保存方法
     * 
     * @param model
     * @param menuIds
     * @return
     */
    public Ret accountSave(FinaCardAccount model, String userId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardAccount findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardAccount
     */
    public List<FinaCardAccount> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardAccount model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaCardAccount model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaCardAccount model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardAccount model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}