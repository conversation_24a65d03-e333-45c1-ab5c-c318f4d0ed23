package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainVideoRecord;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.io.File;
import java.util.List;
import java.util.Map;

public interface MainVideoRecordService {

    public Page<MainVideoRecord> findVideoPage(int pageNumber, int pageSize, MainVideoRecord record);

    public Map<String,Object> saveVideoRecord(MainVideoRecord record, File file);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainVideoRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBranchOfficeUser
     */
    public List<MainVideoRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainVideoRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainVideoRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainVideoRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainVideoRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainVideoRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainVideoRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainVideoRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
