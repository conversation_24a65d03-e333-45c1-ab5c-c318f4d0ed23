package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveBalanceDetail;

import java.util.List;

public interface PersOrgEmployeeLeaveBalanceDetailService {

    public PersOrgEmployeeLeaveBalanceDetail findById(Object id);

    public PersOrgEmployeeLeaveBalanceDetail findByJoinId(String joinId);
    /**
     *
     * @param joinId
     * @param type 1添加，2扣除
     * @return
     */
    public List<PersOrgEmployeeLeaveBalanceDetail> finalLeaveBalanceDetailListByJoinId(String joinId, String type);

    public List<PersOrgEmployeeLeaveBalanceDetail> finalLeaveBalanceDetailListByJoinId(String joinId);
}
