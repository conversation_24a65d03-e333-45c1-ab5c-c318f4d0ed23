package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxBookPerson;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsWxBookPersonService  {

    public List<MmsWxBookPerson> getWxBookPersons(String userId);


    public String  saveWxBookPerson(MmsWx<PERSON>ook<PERSON>erson person);

    public boolean delWxBookPerson(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxBookPerson findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxBookPerson
     */
    public List<MmsWxBookPerson> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxBook<PERSON>erson model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxBook<PERSON>erson model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxBookPerson model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxBookPerson model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}