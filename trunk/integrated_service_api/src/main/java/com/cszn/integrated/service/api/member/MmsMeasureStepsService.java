package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsMeasureSteps;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface MmsMeasureStepsService  {

    /**
     * 获取会员步数数据
     * @param cardNumber
     * @param type
     * @return
     */
    public Map<String,List> memberSteps(String cardNumber,String type);

    /**
     * 获取最新步数数据
     * @param cardNumber
     * @return
     */
    public Map<String,Object> latestSteps(String cardNumber);

    /**
     * 获取步数，卡路里，里程数
     * @param memberId
     * @param dataRecord
     * @return
     */
    public Map<String, Object> getStepsData(String memberId, String dataRecord);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMeasureSteps findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMeasureSteps
     */
    public List<MmsMeasureSteps> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMeasureSteps model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsMeasureSteps model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsMeasureSteps model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMeasureSteps model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsMeasureSteps> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsMeasureSteps> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsMeasureSteps> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}