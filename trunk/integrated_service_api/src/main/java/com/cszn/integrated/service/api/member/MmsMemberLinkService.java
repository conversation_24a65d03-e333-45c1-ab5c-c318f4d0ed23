package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsMemberLink;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MmsMemberLinkService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMemberLink findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MmsMemberLink
     */
    public List<MmsMemberLink> findList(String memberId);
    

    /**
     * find all model
     *
     * @return all <MmsMemberLink
     */
    public List<MmsMemberLink> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMemberLink model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsMemberLink model);
    
    /**
     * 保存供单位
     * @param MmsMemberLink
     * @return
     */
    public boolean saveMemberLink(MmsMemberLink model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsMemberLink model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMemberLink model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsMemberLink> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsMemberLink> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsMemberLink> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}