package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainExamineProblemType;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface MainExamineProblemTypeService {

    Page<MainExamineProblemType> findPageList(int page,int limit,MainExamineProblemType problemType);

    boolean saveProblemType(MainExamineProblemType problemType,String userId);

    MainExamineProblemType findById(Object id);

    List<MainExamineProblemType> findAllProblemType();

    List<Record> page();
}
