package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeePerformance;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeePerformanceService {

    public PersOrgEmployeePerformance findEmpYearMonthPerformance(String empId,String yearMonth);

    public Page<Record> pageList(int pageNumber, int pageSize, String deptId, String yearMonth, String fullName, String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeePerformance findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeePerformance> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeePerformance model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeePerformance model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeePerformance model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeePerformance model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeePerformance> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeePerformance> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeePerformance> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);




}
