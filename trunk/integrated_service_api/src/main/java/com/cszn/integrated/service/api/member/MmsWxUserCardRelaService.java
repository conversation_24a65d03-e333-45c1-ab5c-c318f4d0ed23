package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxUserCardRela;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsWxUserCardRelaService  {

    List<MmsWxUserCardRela> find(String sql,Object...params);

    /**
     * 根据userId获取list
     * @param userId
     * @return
     */
   public List<MmsWxUserCardRela> getListByUserId(String userId);


 /**
  * 根据微信用户id和会员卡id查询两者关系
  * @param userId
  * @param cardId
  * @return
  */
 public MmsWxUserCardRela getRelaByUserIdAndCardId(String userId,String cardId);


 /**
  * 解绑微信用户和会员卡关联关系
  * @param userId
  * @param arr
  * @return
  */
 public boolean untyingUserCard(String userId,String[] arr);



    /**
     * 辅助会员系统更换卡主
     * @param cardId
     * @return
     */
 public List<MmsWxUserCardRela> getUserCardByCardId(String cardId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxUserCardRela findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxUserCardRela
     */
    public List<MmsWxUserCardRela> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxUserCardRela model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxUserCardRela model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxUserCardRela model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxUserCardRela model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}