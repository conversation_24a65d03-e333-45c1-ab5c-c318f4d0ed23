package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBranchOfficeService {

    /**
     * 批量删除分公司
     * @param jsonArrayStr
     */
    public boolean batchDelBranchoffice(String jsonArrayStr,String userId);

    /**
     * 分公司分页信息
     * @param pageNumber
     * @param pageSize
     * @param mainBranchOffice
     * @return
     */
    public Page<MainBranchOffice> branchOfficePage(Integer pageNumber,Integer pageSize,MainBranchOffice mainBranchOffice);

    /**
     * 删除分公司
     * @param id
     * @param userId
     * @return
     */
    public boolean delBranchOffice(String id,String userId);

    /**
     * 保存分公司
     * @param mainBranchOffice
     * @return
     */
    public boolean saveBranchOffice(MainBranchOffice mainBranchOffice,String userId);


    /**
     * 获取未删除的分公司
     * @return
     */
    public List<MainBranchOffice> getUnDelBranchOffice();


    /**
     * 通过userId查询分公司
     * @param userId
     * @return
     */
    public MainBranchOffice getByUserId(String userId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBranchOffice findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomType
     */
    public List<MainBranchOffice> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBranchOffice model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBranchOffice model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBranchOffice model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBranchOffice model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBranchOffice> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBranchOffice> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBranchOffice> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
