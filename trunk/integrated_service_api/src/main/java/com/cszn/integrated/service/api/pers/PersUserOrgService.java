package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersUserOrg;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersUserOrgService {

    public void autoAddOrgChildrenRecord();

    /**
     *
     * @param userId
     * @return
     */
    public List<String> findOrgByUserId(String userId);

    /**
     *
     * @param user
     * @param ids
     * @return
     */
    public boolean saveUserOrg(User user,String ids);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersUserOrg findById(Object id);


    /**
     * find all model
     *
     * @return all <PersPosition
     */
    public List<PersUserOrg> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersUserOrg model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersUserOrg model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersUserOrg model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersUserOrg model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersUserOrg> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersUserOrg> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersUserOrg> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
