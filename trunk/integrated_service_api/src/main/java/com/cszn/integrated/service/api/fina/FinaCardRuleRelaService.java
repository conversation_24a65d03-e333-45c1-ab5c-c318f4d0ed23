package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardRuleRela;

public interface FinaCardRuleRelaService {

    /**
     * 通过会员卡id获取规则id
     * @param cardId
     * @return
     */
    public String findRuleIdByCardId(String cardId);

    public boolean saveAndUpdateRuleRela(String cardId,String ruleId,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardRuleRela findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardRuleRela
     */
    public List<FinaCardRuleRela> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardRuleRela model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaCardRuleRela model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaCardRuleRela model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardRuleRela model);
}