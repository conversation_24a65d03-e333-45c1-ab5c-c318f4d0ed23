package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsStockTypeUser;
import com.cszn.integrated.service.entity.wms.WmsSuppliers;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface WmsStockTypeUserService {

    /*public boolean saveWmsStockTypeUser(WmsStockTypeUser typeUser,String userId);

    public boolean delWmsStockTypeUser(String id,String userId);*/


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsStockTypeUser findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsStockTypeUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsStockTypeUser model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsStockTypeUser model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsStockTypeUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsStockTypeUser model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsStockTypeUser> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsStockTypeUser> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsStockTypeUser> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
