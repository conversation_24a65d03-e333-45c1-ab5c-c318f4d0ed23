package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaMembershipCardBase;
import com.cszn.integrated.service.entity.fina.FinaMembershipCardCheckinConfig;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaMembershipCardCheckinConfigService {

    public List<FinaMembershipCardCheckinConfig> findCheckinConfigListByCardId(String cardId);

    public boolean saveCheckinConfig(FinaMembershipCardCheckinConfig checkinConfig,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMembershipCardCheckinConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaMembershipCardBase
     */
    public List<FinaMembershipCardCheckinConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMembershipCardCheckinConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaMembershipCardCheckinConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaMembershipCardCheckinConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMembershipCardCheckinConfig model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaMembershipCardCheckinConfig> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaMembershipCardCheckinConfig> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaMembershipCardCheckinConfig> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
