package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainExamineRecord;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MainExamineRecordService {

    MainExamineRecord findById(Object id);

    boolean saveExamineRecord(MainExamineRecord record,String userId);

    public Page<MainExamineRecord> pageList(int page,int limit,MainExamineRecord record);

    public Page<Record> recordResultPageList(int page, int limit, MainExamineRecord record);
}
