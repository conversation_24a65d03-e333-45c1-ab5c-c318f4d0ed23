package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxUser;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MmsWxUserService  {

    /**
     * 获取关注的用户/获取用户粉丝
     * @param pageNumber
     * @param pageSize
     * @param userId
     * @return
     */
    public Page<Record> getSubscriptionsByUser(Integer pageNumber, Integer pageSize, String userId,String subscriberId);

    /**
     * 根据unionid查询微信用户
     * @param unionid
     * @return
     */
    public MmsWxUser getWxUserByUnionid(String unionid);

    /**
     * 根据openid查询微信用户
     * @param openid
     * @return
     */
    public MmsWxUser getWxUserByOpenid(String openid);

    /**
     * 微信用户信息保存方法
     * @param wxUser
     * @return boolean
     */
    public boolean saveWxuser(MmsWxUser wxUser);

    /**
     * 保存微信用户信息完成回调旅居同步用户信息
     * @param userId
     * @return
     */
    public boolean CheckMember(String userId);


    public List<MmsWxUser> getWxUserByNickName(String nickName);
    
    /**
     * 根据微信unionid获取会员积分卡列表
     * @param unionid
     * @return
     */
    public List<Record> getIntegralCardListByUnionid(String unionid);

    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param wxUser
     * @return
     */
    public Page<MmsWxUser> findList(Integer pageNumber, Integer pageSize, MmsWxUser wxUser);


    /**
     * 会员卡绑定
     * @param userId
     * @param cardId
     * @return
     */
    public String bindMemberCard(String userId, String cardId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxUser findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxUser
     */
    public List<MmsWxUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxUser model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxUser model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxUser model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}