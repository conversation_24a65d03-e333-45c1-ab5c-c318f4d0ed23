package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinFillCard;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeCheckinFillCardService {

    public PersOrgEmployeeCheckinFillCard getEmpCheckinFillCardByRecordId(String recordId);

    public List<PersOrgEmployeeCheckinFillCard> getEmpCheckinFillCardList(String empId,String startTime,String endTime);

    public boolean saveFillCard(PersOrgEmployeeCheckinFillCard checkinFillCard,String userId);

    public boolean isExistFillCard(String checkinRecordId, Date fillCardTime, String exceptions);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinFillCard findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeType
     */
    public List<PersOrgEmployeeCheckinFillCard> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinFillCard model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinFillCard model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinFillCard model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinFillCard model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinFillCard> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinFillCard> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinFillCard> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
