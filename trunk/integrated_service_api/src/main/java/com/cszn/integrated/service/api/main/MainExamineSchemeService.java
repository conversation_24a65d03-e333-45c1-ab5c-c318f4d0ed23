package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainExamineScheme;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainExamineSchemeService {

    MainExamineScheme findById(Object id);

    Page<MainExamineScheme> pageList(int page,int limit,MainExamineScheme scheme);

    boolean schemeSave(MainExamineScheme scheme,String userId);

    List<MainExamineScheme> findExamineSchemeList();
}
