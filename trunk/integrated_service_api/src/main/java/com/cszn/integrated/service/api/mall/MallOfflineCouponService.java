package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallOfflineCoupon;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MallOfflineCouponService {

    public List<MallOfflineCoupon> getCouponListBywarehouseId(String baseId);

    public boolean saveOfflineCoupon(MallOfflineCoupon offlineCoupon,String userId);

    public Page<MallOfflineCoupon> offlineCouponPage(int pageNumber,int pageSize,MallOfflineCoupon offlineCoupon);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallOfflineCoupon findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallOfflineCoupon> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallOfflineCoupon model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallOfflineCoupon model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallOfflineCoupon model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallOfflineCoupon model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
