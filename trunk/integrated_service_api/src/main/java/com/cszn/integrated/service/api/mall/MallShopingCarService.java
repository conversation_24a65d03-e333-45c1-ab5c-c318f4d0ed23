package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallProduct;
import com.cszn.integrated.service.entity.mall.MallShopingCar;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface MallShopingCarService {

    public boolean delShopingCardRecord(String id);

    public boolean saveShopingCardRecord(MallShopingCar shopingCar);

    public List<Record> getUserShopingCard(String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallShopingCar findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallShopingCar> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallShopingCar model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallShopingCar model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallShopingCar model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallShopingCar model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
