package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.common.XmSelect;
import com.cszn.integrated.service.entity.main.MainRole;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainRoleService {

    public List<MainRole> findEnabledRole();
    
    /**
     * find all model
     *
     * @return all <XmSelect
     */
    public List<XmSelect> mainRoleXmSelectTree();

    public boolean saveMainRole(MainRole mainRole,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainRole findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainRole> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainRole model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainRole model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainRole model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainRole model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainRole> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainRole> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainRole> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
