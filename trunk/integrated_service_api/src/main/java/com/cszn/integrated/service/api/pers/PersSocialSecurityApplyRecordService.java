package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersSocialSecurityApplyRecord;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface PersSocialSecurityApplyRecordService {

    public PersSocialSecurityApplyRecord findById(Object id);

    public Page<Record> pageList(int page, int limit, PersSocialSecurityApplyRecord applyRecord, String userId);

}
