package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsBlackRecord;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsBlackRecordService {

    /**
     * 查询分页对象
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsBlackRecord> pageTable(int pageNumber, int pageSize,MmsBlackRecord model);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsBlackRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsBlackRecord
     */
    public List<MmsBlackRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsBlackRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsBlackRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsBlackRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsBlackRecord model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
