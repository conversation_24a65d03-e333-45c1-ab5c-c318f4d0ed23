package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinWxRecord;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeOverTimeApply;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PersOrgEmployeeOverTimeApplyService {

    public List<PersOrgEmployeeOverTimeApply> getOverTimeApplyListByMap(Map<String,List<PersOrgEmployeeOverTimeApply>> overTimeApplyMap, String empId, Date date);

    public List<PersOrgEmployeeOverTimeApply> findBySql(String sql,Object[] params);

    public PersOrgEmployeeOverTimeApply findEmpRestOverTimeRecord(String empId, Date date);

    public void employeeRestOverTimeCheckin(String empId, PersOrgEmployeeCheckinWxRecord checkinWxRecord);

    public PersOrgEmployeeOverTimeApply findEmpRestOverTimeRecordByStatus(String empId,Date date,String[] status);

    public List<PersOrgEmployeeOverTimeApply> findEmpRestOverTimeRecordListByStatus(String empId,Date date,String[] status);

    public List<PersOrgEmployeeOverTimeApply> findEmpOverTimeNotAddLeaveBalanceList(Date startDate,Date endDate);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeOverTimeApply findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeType
     */
    public List<PersOrgEmployeeOverTimeApply> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeOverTimeApply model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeOverTimeApply model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeOverTimeApply model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeOverTimeApply model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeOverTimeApply> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeOverTimeApply> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeOverTimeApply> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
