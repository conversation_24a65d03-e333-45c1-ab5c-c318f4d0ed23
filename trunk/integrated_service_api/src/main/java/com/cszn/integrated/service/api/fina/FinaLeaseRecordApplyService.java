package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaLeaseRecordApply;
import com.cszn.integrated.service.entity.fina.FinaLeaseRecordRoom;
import com.cszn.integrated.service.entity.fina.FinaLeaseRecordRoomPayment;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface FinaLeaseRecordApplyService {

    FinaLeaseRecordApply findById(Object id);

    Page<Record> leaseRecordApplyPage(int page,int limit,FinaLeaseRecordApply leaseRecordApply,String supplierName,String roomName);

    FinaLeaseRecordApply findByTaskId(String taskId);

    boolean taskCompleted(FinaLeaseRecordApply leaseRecordApply);

    boolean taskCompleted2(FinaLeaseRecordApply leaseRecordApply);

    Page<Record> leaseRoomPageList(int page,int limit,String baseId,String roomName,String contractStatus,String supplierName,String roomStartDate,String roomEndDate);

    List<FinaLeaseRecordRoomPayment> genRoomPaymentList(FinaLeaseRecordRoom leaseRecordRoom);
}
