package com.cszn.integrated.service.api.pers;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.pers.PersNoticeEnclosure;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersNoticeEnclosureService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersNoticeEnclosure findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeEnclosure
     */
    public List<PersNoticeEnclosure> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersNoticeEnclosure model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersNoticeEnclosure model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersNoticeEnclosure model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersNoticeEnclosure model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersNoticeEnclosure> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersNoticeEnclosure> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersNoticeEnclosure> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}