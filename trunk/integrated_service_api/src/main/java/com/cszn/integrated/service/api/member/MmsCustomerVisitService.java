package com.cszn.integrated.service.api.member;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.service.entity.member.MmsCustomerVisit;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MmsCustomerVisitService  {
	
	/**
	 * 分页
	 *
	 * @param page
	 * @param pageSize
	 * @return
	 */
	public Page<MmsCustomerVisit> paginateByCondition(String startDate, String endDate, String userId, int pageNumber, int pageSize);

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsCustomerVisit> paginateByCondition(MmsCustomerVisit model, int pageNumber, int pageSize);
    
    /**
     * 获取回访记录列表数据
     *
     * @param startDate
     * @param endDate
     * @param receptionUserId
     * @param visitType
     * @param visitType
     * @return
     */
    public Page<MmsCustomerVisit> visitRecordPage(int pageNumber, int pageSize, String startDate, String endDate, String receptionUserId, String visitType, String actionType);
    
    /**
     * 获取通话情况统计数据
     *
     * @param startDate
     * @param endDate
     * @param receptionUserId
     * @param visitType
     * @return
     */
    public Record getCallPassStatistics(String startDate, String endDate, String receptionUserId, String visitType);
    
    /**
     * 获取来电事项统计数据
     *
     * @param startDate
     * @param endDate
     * @param receptionUserId
     * @param visitType
     * @return
     */
    public Record getAppealTypeStatistics(String startDate, String endDate, String receptionUserId, String visitType);
    
    /**
     * 获取满意情况统计数据
     *
     * @param startDate
     * @param endDate
     * @param receptionUserId
     * @param visitType
     * @return
     */
    public Record getSatisfiedStatistics(String startDate, String endDate, String receptionUserId, String visitType);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsCustomerVisit findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsConsultVisit
     */
    public List<MmsCustomerVisit> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsCustomerVisit model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsCustomerVisit model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsCustomerVisit model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsCustomerVisit model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}