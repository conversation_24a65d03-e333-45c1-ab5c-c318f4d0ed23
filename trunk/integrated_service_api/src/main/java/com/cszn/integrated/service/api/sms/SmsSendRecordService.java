package com.cszn.integrated.service.api.sms;

import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.sms.SmsSendRecord;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface SmsSendRecordService {

    /**
     * 发送短信
     * @param sendType 发送类型枚举
     * @param cardNumber 会员卡号
     * @param paramJsonStr 短信发送参数json字符串
     * @param sendTime 发送时间,指定某个时间发送，如果为空为现在发送
     * @return
     */
    public void sendMessage(SendType sendType,String cardNumber,String paramJsonStr,String sendTime);
    
    /**
     * 发送短信
     * @param sendType 发送类型枚举
     * @param phoneNumber 手机号
     * @param paramJsonStr 短信发送参数json字符串
     * @param sendTime 发送时间,指定某个时间发送，如果为空为现在发送
     * @return
     */
    public void sendMsgByPhoneNumber(SendType sendType,String phoneNumber,String paramJsonStr,String sendTime);

    /**
     * 保存短信发送失败的记录
     * @param phoneNum 手机号码
     * @param paramJsonStr 参数
     * @param sendType 发送类型枚举
     * @param userId 操作员id
     * @return
     */
    public boolean saveSmsSendRecord(String phoneNum, String paramJsonStr, SendType sendType, String userId);


    /**
     * 扣费短信发送:过渡
     * @param cardNumber
     * @param officeId
     * @param baseId
     * @param times
     * @param amount
     * @param remark
     * @param cur
     */
    public void deductCardSendMsg(String cardNumber, String officeId, String baseId, Double times, Double amount, Double points,Double integrals, Double beanCoupons, String remark, String cur, String costName, Date date);


    /**
     * 发送短信方法：双十一抽奖
     * @param sendType
     * @param cardNumber
     * @param telephone
     * @param paramJsonStr
     * @param sendTime
     */
    public boolean sendMsgByPrize(SendType sendType,String cardNumber,String telephone,String paramJsonStr,String sendTime);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public SmsSendRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <PersEmpUser
     */
    public List<SmsSendRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(SmsSendRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(SmsSendRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(SmsSendRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(SmsSendRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SmsSendRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<SmsSendRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<SmsSendRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
