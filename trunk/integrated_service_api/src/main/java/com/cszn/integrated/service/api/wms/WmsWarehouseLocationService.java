package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsWarehouseAreas;
import com.cszn.integrated.service.entity.wms.WmsWarehouseLocations;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface WmsWarehouseLocationService {

    /**
     * 保存库位
     * @param wmsWarehouseLocations
     * @return
     */
    public boolean saveWarehouseLocation(WmsWarehouseLocations wmsWarehouseLocations);

    /**
     * 获取库位分页
     * @param pageNumber
     * @param pageSize
     * @param wmsWarehouseLocations
     * @return
     */
    public Page<WmsWarehouseLocations> findWarehouseLocationPageList(Integer pageNumber, Integer pageSize, WmsWarehouseLocations wmsWarehouseLocations);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsWarehouseLocations findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsWarehouseLocations> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsWarehouseLocations model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsWarehouseLocations model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsWarehouseLocations model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsWarehouseLocations model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsWarehouseLocations> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsWarehouseLocations> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsWarehouseLocations> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
