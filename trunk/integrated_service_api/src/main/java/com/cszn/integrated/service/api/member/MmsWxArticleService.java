package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxArticle;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsWxArticleService  {

    /**
     * 获取分页
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsWxArticle> findList(Integer pageNumber, Integer pageSize, MmsWxArticle wxArticle);

    /**
     * 删除
     * @param id
     * @param userId
     * @return
     */
    public boolean delWxArticle(String id,String userId);

    /**
     * 保存
     * @param wxArticle
     * @param userId
     * @return
     */
    public boolean saveWxArticle(MmsWxArticle wxArticle,String userId);


    public Page<MmsWxArticle> getWxArticleList(Integer pageNumber,Integer pageSize);

    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelArticle(List<MmsWxArticle> list,String userId);


    /**
     * 根据id查询对象
     * @param id
     * @return
     */
    public MmsWxArticle get(String id);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxArticle findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxArticle
     */
    public List<MmsWxArticle> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxArticle model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxArticle model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxArticle model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxArticle model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}