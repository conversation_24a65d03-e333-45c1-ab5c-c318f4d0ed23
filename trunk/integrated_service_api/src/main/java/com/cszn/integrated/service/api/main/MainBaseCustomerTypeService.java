package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseCustomerType;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBaseCustomerTypeService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainBaseCustomerType> paginateByCondition(MainBaseCustomerType model, int pageNumber, int pageSize);
    
    
    /**
     * find all model
     *
     * @return all <MainBaseCustomerType
     */
    public List<MainBaseCustomerType> findListByBaseId(String baseId);
    
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseCustomerType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseCustomerType
     */
    public List<MainBaseCustomerType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseCustomerType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseCustomerType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseCustomerType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseCustomerType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseCustomerType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseCustomerType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseCustomerType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}