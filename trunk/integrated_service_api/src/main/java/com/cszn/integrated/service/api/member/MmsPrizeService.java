package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsPrize;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsPrizeService  {

    /**
     * 获取所有奖品
     * @return
     */
    public List<MmsPrize> getPrizes();


    /**
     * 根据随机数获取奖品
     * @param num
     * @return
     */
    public MmsPrize getPrizeByRan(int num);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsPrize findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsPrize
     */
    public List<MmsPrize> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsPrize model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsPrize model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsPrize model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsPrize model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsPrize> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsPrize> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsPrize> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}