package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaConditionRechargeScheme;
import com.cszn.integrated.service.entity.fina.FinaConditionRechargeSchemeDetail;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface FinaConditionRechargeSchemeService {

    boolean saveConditionRechargeScheme(FinaConditionRechargeScheme conditionRechargeScheme, List<FinaConditionRechargeSchemeDetail> detailList);

    Page<FinaConditionRechargeScheme> paginateByCondition(FinaConditionRechargeScheme rechargeScheme,int page,int limit);

    FinaConditionRechargeScheme findById(Object id);

    boolean delConditionRechargeScheme(FinaConditionRechargeScheme conditionRechargeScheme);

    List<FinaConditionRechargeScheme> getConditionRechargeSchemeList();
}
