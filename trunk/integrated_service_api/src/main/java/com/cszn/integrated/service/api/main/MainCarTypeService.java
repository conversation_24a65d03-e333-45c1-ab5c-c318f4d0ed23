package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCarType;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainCarTypeService {


    /**
     * 获取车型分页信息
     * @param pageNumber
     * @param pageSize
     * @param mainCarType
     * @return
     */
    public Page<MainCarType> findCarTypePage(Integer pageNumber,Integer pageSize,MainCarType mainCarType);

    /**
     * 保存车型
     * @param mainCarType
     * @param userId
     * @return
     */
    public boolean saveCarType(MainCarType mainCarType,String userId);

    /**
     * 删除车型
     * @param id
     * @param userId
     * @return
     */
    public boolean delCarType(String id,String userId);

    /**
     * 通过名字id判断是否存在
     * @param id
     * @param name
     * @return
     */
    public MainCarType findCarTypeByIdName(String id,String name);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCarType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBranchOfficeUser
     */
    public List<MainCarType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCarType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCarType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCarType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCarType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCarType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCarType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCarType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
