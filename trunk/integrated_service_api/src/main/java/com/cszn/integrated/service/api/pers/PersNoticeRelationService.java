package com.cszn.integrated.service.api.pers;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.pers.PersNoticeRelation;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersNoticeRelationService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersNoticeRelation findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeRelation
     */
    public List<PersNoticeRelation> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersNoticeRelation model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersNoticeRelation model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersNoticeRelation model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersNoticeRelation model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersNoticeRelation> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersNoticeRelation> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersNoticeRelation> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}