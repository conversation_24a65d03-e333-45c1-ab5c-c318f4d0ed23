package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCardGiveScheme;
import com.cszn.integrated.service.entity.main.MainCardGiveSchemeRule;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainCardGiveSchemeService  {

    /**
     * 根据条件查询表格分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainCardGiveScheme> paginateByCondition(MainCardGiveScheme model, int pageNumber, int pageSize);
    
    /**
     * 查询未删除的所有赠送方案列表
     *
     * @return List<MainCardGiveScheme>
     */
    public List<MainCardGiveScheme> findListOnUse(String type);
    
    /**
     * 保存方法
     * 
     * @param model
     * @return
     */
    public Ret saveCardGiveScheme(MainCardGiveScheme model, List<MainCardGiveSchemeRule> ruleList);
    
    /**
     * 删除方法
     * 
     * @param model
     * @return
     */
    public Ret del(MainCardGiveScheme model);



    public MainCardGiveScheme findFirst(String sql, Object... params);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardGiveScheme findById(Object id);


    /**
     * find all model
     *
     * @return all <Dict
     */
    public List<MainCardGiveScheme> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardGiveScheme model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainCardGiveScheme model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainCardGiveScheme model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardGiveScheme model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}