package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainDeductPointsConfig;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MainDeductPointsConfigService {

    /**
     * 通过入住时间和退住时间获取总共需要扣除多少点数(不包房)
     * @param checkinDate
     * @param checkoutDate
     * @return
     */
    public Map<String,Object> totalDeductPoints(Date checkinDate,Date checkoutDate);

    /**
     * 保存新配置，删除旧配置
     * @param configList
     * @param delIds
     * @param userId
     * @return
     */
    public boolean saveDeductPointsConfig(List<MainDeductPointsConfig> configList,String delIds,String userId);

    /**
     * 获取扣除点数配置
     * @return
     */
    public List<MainDeductPointsConfig> findDeductPointsConfigList();

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainDeductPointsConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MainDeductPointsConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainDeductPointsConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainDeductPointsConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainDeductPointsConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainDeductPointsConfig model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
