package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainGoodModels;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainGoodModelsService {


    /**
     * 获取所有可以的商品
     * @return
     */
    public List<MainGoodModels> findAllGoodModelList();

    /**
     * 保存商品
     * @param model
     * @param userId
     * @return
     */
    public boolean saveGoodModel(MainGoodModels model,String userId);

    /**
     * 商品分类
     * @param pageNumber
     * @param pageSize
     * @param model
     * @return
     */
    public Page<Record> pageList(Integer pageNumber,Integer pageSize,MainGoodModels model);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainGoodModels findById(Object id);


    /**
     * find all model
     *
     * @return all <MainGoodModels
     */
    public List<MainGoodModels> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainGoodModels model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainGoodModels model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainGoodModels model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainGoodModels model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainGoodModels> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainGoodModels> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainGoodModels> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
