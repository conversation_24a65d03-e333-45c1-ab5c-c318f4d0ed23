package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsCustomerAssign;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsCustomerAssignService {
	
	/**
	 * find model by resterId and resterId
	 *
	 * @param resterId
	 * @param userId
	 * @return
	 */
	public MmsCustomerAssign findByAssignCustomerId(String customerId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsCustomerAssign findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsConsultAssign
     */
    public List<MmsCustomerAssign> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsCustomerAssign model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsCustomerAssign model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsCustomerAssign model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsCustomerAssign model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
