package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardIntegralRecord;
import com.cszn.integrated.service.entity.fina.FinaCardRoll;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardRollService {

    public FinaCardRoll findByRollId(String roolId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardRoll findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardIntegralRecord
     */
    public List<FinaCardRoll> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardRoll model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardRoll model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardRoll model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardRoll model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardRoll> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardRoll> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardRoll> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
