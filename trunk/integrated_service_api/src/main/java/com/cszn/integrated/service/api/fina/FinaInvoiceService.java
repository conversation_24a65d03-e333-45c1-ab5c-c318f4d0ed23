package com.cszn.integrated.service.api.fina;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.fina.FinaInvoice;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaInvoiceService  {

    /**
     * 分页列表
     * @param invoice
     */
    public Page<Record> findList(Integer pageNumber, Integer pageSize, FinaInvoice invoice);



    /**
     *获取发票数据
     * @param invoice
     * @param jsonArray
     * @return
     */
    public Boolean getInvoices(FinaInvoice invoice, JSONArray jsonArray);



    /**
     * 调用旅居：分页列表
     * @param pageNumber
     * @param pageSize
     * @param invoice
     * @return
     */
    public Page<Record> finaListSojourn(Integer pageNumber, Integer pageSize, FinaInvoice invoice);



    /**
     * 调用旅居：获取发票明细
     * @param id
     * @return
     */
    public List<Record> getDetailsSojourn(String id);



    /**
     * 获取发票对象
     * @param id
     * @return
     */
    public FinaInvoice get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaInvoice findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaInvoice
     */
    public List<FinaInvoice> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaInvoice model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaInvoice model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaInvoice model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaInvoice model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaInvoice> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaInvoice> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaInvoice> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}