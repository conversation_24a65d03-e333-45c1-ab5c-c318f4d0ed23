package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallActivityDiscountConfig;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MallActivityDiscountConfigService {

    public List<MallActivityDiscountConfig> findDiscountByActivityId(String activityId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallActivityDiscountConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallActivityDiscountConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallActivityDiscountConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallActivityDiscountConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallActivityDiscountConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallActivityDiscountConfig model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
