package com.cszn.integrated.service.api.main;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBaseFloor;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseFloorService  {

    /**
     * 判断楼层是否属于这个基地
     * @param floorId
     * @param baseId
     * @return
     */
    public boolean floorBelongToBase(String floorId,String baseId);

    /**
     * 判断该楼层是否存在
     * @param baseFloor
     * @return
     */
    public MainBaseFloor floorIsExist(MainBaseFloor baseFloor);

    /**
     * 通过楼栋id获取楼层数
     * @param buildingId
     * @return
     */
    public Integer queryFloorCountByBuildingId(String buildingId);


    //public boolean batchDelMainBaseFloor(List<MainBaseFloor> floorList,String userId);

    public Page<MainBaseFloor> getFloorPageByBuildingId(String buildingId,Integer pageNumber,Integer pageSize);

    /**
     * 通过楼栋id获取楼层
     * @param buildingIds
     * @return
     */
    public List<MainBaseFloor> getFloorListByBuildingIds(String[] buildingIds);

    /**
     * 保存楼层
     * @param mainBaseFloor
     * @return
     */
    public boolean saveFloor(MainBaseFloor mainBaseFloor);

    /**
     * 删除楼层
     * @param id
     * @return
     */
    public boolean delFloor(String id,String userId);


    /**
     * 增加房间和床位数量
     * @param id
     * @param roomNum
     * @param bedRoom
     * @return
     */
    public boolean updateRoomAndBedNumber(String id,int roomNum,int bedNum,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseFloor findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseFloor
     */
    public List<MainBaseFloor> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseFloor model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseFloor model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseFloor model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseFloor model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseFloor> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseFloor> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseFloor> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}