package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.*;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FinaExpenseRecordTouristService  {


    /**
     * 获取旅游团分页
     * @param pageNumber
     * @param pageSize
     * @param tourist
     */
    public Page<FinaExpenseRecordTourist> findListPage(Integer pageNumber,Integer pageSize,String beginTime,String endTime,FinaExpenseRecordTourist tourist);


    /**
     * 设置旅游团对象参数
     * @param tourist
     * @param touristNo
     * @param touristName
     * @param touristRoute
     * @param touristRouteId
     * @param dateStart
     * @param dateEnd
     * @param expenseTimes
     * @param status
     */
    public void setTouristInfo(FinaExpenseRecordTourist tourist, String touristNo, String touristName, String touristRoute, String touristRouteId,
                               Date dateStart, Date dateEnd, Double expenseTimes, String status);


    /**
     * 旅游团结算
     * @return
     */
    public Map<String,Object> touristSettle(List<Map<String,Object>> settleList,String userId);



    /**
     * 根据旅游团扣卡数据更新会员卡和生成交易记录
     * @param settleList
     * @param userId
     * @return
     */
    public Map<String,Object> getCardNumbersAndTransByTourist(List<Map<String,Object>> settleList,FinaExpenseRecordTourist tourist,String userId);



    /**
     * 处理旅游团更换会员卡和财务备注
     * @param er
     * @param cardNumber
     * @param userId
     * @return
     */
    public Map<String,Object> dealTouristCardNumberAndFinanceRemark(FinaExpenseRecordTourist tourist,FinaExpenseRecord er, String cardNumber,Double actual,String userId);



    /**
     * 更新旅游团更换会员卡和财务备注
     * @param oldCard
     * @param newCard
     * @param er
     * @param fc
     * @return
     */
    public boolean updateTouristCardNumberAndFinanceRemark(FinaMembershipCard oldCard, FinaMembershipCard newCard, FinaExpenseRecord er, FinaExpenseRecordDeductionCard fc,FinaExpenseRecordDetail detail);



    /**
     * 根据旅游团编号查询旅游团
     * @param touristNo
     * @return
     */
    public FinaExpenseRecordTourist getTouristByNo(String touristNo);





    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordTourist findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordTourist
     */
    public List<FinaExpenseRecordTourist> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordTourist model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordTourist model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordTourist model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordTourist model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordTourist> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordTourist> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordTourist> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}