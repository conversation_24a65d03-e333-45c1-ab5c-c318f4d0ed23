package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainCardYearLimitConfig;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;

public interface MainCardYearLimitConfigService {


    /**
     * 获取会员卡年限类型配置分页
     * @param pageNumber
     * @param pageSize
     * @param cardYearLimit
     * @return
     */
    public Page<MainCardYearLimitConfig> cardYearLimitConfigPage(int pageNumber, int pageSize, MainCardYearLimitConfig model);
    
    
    /**
     * 获取会员卡年限类型配置list
     * @return
     */
    public List<Record> findCardYearLimitConfigList(String yearLimitId);

    
    /**
     * 保存会员卡年限配置
     * @param model
     * @param userId
     * @return
     */
    public boolean saveCardYearLimitConfig(MainCardYearLimitConfig model);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardYearLimitConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBranchOfficeUser
     */
    public List<MainCardYearLimitConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardYearLimitConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCardYearLimitConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCardYearLimitConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardYearLimitConfig model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCardYearLimitConfig> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCardYearLimitConfig> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCardYearLimitConfig> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
