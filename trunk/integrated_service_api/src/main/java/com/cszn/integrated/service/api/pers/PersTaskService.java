package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersTask;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface PersTaskService {


    public boolean employeeTaskCallback(PersTask persTask);

    public boolean employeeTaskAborted(PersTask persTask);

    public Map<String,Object> httpCreateTask(PersTask persTask);

    public PersTask findByTaskId(String taskId);

    public void getSubmitInfo(PersTask persTask);

    public PersTask findByRecordId(String recordId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersTask findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeType
     */
    public List<PersTask> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersTask model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersTask model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersTask model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersTask model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersTask> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersTask> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersTask> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
