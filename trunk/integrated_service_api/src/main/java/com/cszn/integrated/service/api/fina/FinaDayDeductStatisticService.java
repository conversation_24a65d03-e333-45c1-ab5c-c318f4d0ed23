package com.cszn.integrated.service.api.fina;


import com.cszn.integrated.service.entity.fina.FinaDayDeductStatistic;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import java.util.List;
import java.util.Map;

public interface FinaDayDeductStatisticService  {


    /**
     * 获取扣卡图表数据
     * @param timeType
     * @return
     */
    Map<String,Object> getChartData(String timeType,String urlType);


    /**
     * 获取按时间基地统计的扣卡数据
     * @param timeType
     * @return
     */
    Map<String,Object> getChartDataForBase(String timeType,String urlType);


    /**
     * 处理基地总金额及总天数
     * @param reocrd
     * @return
     */
    public void dealBaseTotal(Record reocrd);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaDayDeductStatistic findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaDayDeductStatistic
     */
    public List<FinaDayDeductStatistic> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaDayDeductStatistic model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaDayDeductStatistic model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaDayDeductStatistic model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaDayDeductStatistic model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaDayDeductStatistic> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaDayDeductStatistic> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaDayDeductStatistic> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}