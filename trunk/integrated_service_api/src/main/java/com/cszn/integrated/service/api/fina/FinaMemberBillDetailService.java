package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaMemberBillDetail;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface FinaMemberBillDetailService {

    /**
     * 添加明细
     * @param billDetail
     */
    public boolean saveMainBillDetail(FinaMemberBillDetail billDetail);

    /**
     * 修改机构明细
     * @param billDetails
     */
    public void sendBillDetail(List<FinaMemberBillDetail> billDetails);

    /**
     * 通过mainid获取所有未结算的明细
     * @param mainId
     * @return
     */
    public Map<String,List<FinaMemberBillDetail>> findWaitSettlementDetailByMainId(String mainId);

    /**
     * 结算当个明细
     * @param userId
     * @return
     */
    public Map<String,Object> memberSettleBillDetail(FinaMemberBillDetail billDetail,String userId);

    /**
     * 修改明细
     * @param billDetail
     * @param userId
     * @return
     */
    public boolean updateMainBillDetail(FinaMemberBillDetail billDetail,String userId);

    /**
     * 作废账单明细
     * @param id
     * @param userId
     * @return
     */
    public boolean delBillDetail(String id,String userId);

    /**
     * 通过账单id查询费用明细
     * @param mainId
     * @return
     */
    public List<Record> findMainBillDetail(String mainId);

    public List<FinaMemberBillDetail> finaMemberBillDetailList(FinaMemberBillDetail billDetail);

    public FinaMemberBillDetail getLastMonthSettledBillDetail(String checkinNo,String yearMonth,String deductType,List<String> names);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMemberBillDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaFunctionSwitch
     */
    public List<FinaMemberBillDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMemberBillDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaMemberBillDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaMemberBillDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMemberBillDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaMemberBillDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaMemberBillDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaMemberBillDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
