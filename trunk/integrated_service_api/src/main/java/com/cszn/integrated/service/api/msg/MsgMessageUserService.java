package com.cszn.integrated.service.api.msg;

import com.cszn.integrated.service.entity.msg.MsgMessageUser;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MsgMessageUserService  {


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MsgMessageUser findById(Object id);


    /**
     * find all model
     *
     * @return all <MsgMessageUser
     */
    public List<MsgMessageUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MsgMessageUser model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MsgMessageUser model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MsgMessageUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MsgMessageUser model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MsgMessageUser> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MsgMessageUser> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MsgMessageUser> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}