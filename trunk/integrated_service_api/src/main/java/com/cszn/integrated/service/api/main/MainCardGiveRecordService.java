package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCardGiveRecord;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainCardGiveRecordService {

    /**
     * 获取会员卡赠送记录分页
     * @param pageNumber
     * @param pageSize
     * @param cardGiveRecord
     * @return
     */
    public Page<MainCardGiveRecord> findGiveRecordPage(Integer pageNumber,Integer pageSize,MainCardGiveRecord cardGiveRecord);

    /**
     * 获取赠送记录
     * @param cardId
     * @param ruleId
     * @param cycle
     * @param give_seq
     * @return
     */
    public MainCardGiveRecord findGiveRecord(String cardId,String ruleId,String cycle,Integer give_seq);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardGiveRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <Dict
     */
    public List<MainCardGiveRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardGiveRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainCardGiveRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainCardGiveRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardGiveRecord model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
