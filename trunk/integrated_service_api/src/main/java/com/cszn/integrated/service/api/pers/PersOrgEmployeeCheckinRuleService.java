package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinRule;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinWxRecord;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PersOrgEmployeeCheckinRuleService {

    public Map<String,Object> getEmpCheckinStatus(String empId, PersOrgEmployeeCheckinRule checkinRule, PersOrgEmployeeCheckinWxRecord checkinWxRecord);

    public void genRepairScheduleCheckinData(String empId,PersOrgEmployeeCheckinWxRecord checkinWxRecord,PersOrgEmployeeCheckinRule checkinRule);

    public boolean isRestDayByDate(String empId,Date date);

    public void getEmpDelayCheckinRuleConfig(String empId,PersOrgEmployeeCheckinWxRecord checkinWxRecord);

    public PersOrgEmployeeCheckinRule getEmpTopCheckinRuleByDate(String empId, Date date);

    public void getEmpCheckinRuleConfig(String empId, PersOrgEmployeeCheckinWxRecord checkinWxRecord);

    public PersOrgEmployeeCheckinRule getEmpTopCheckinRule(String empId);

    public Page<PersOrgEmployeeCheckinRule> checkinRulePageList(int pageNumber,int pageSize,PersOrgEmployeeCheckinRule checkinRule,String userId,String deptId);

    public boolean saveCheckinRule(PersOrgEmployeeCheckinRule checkinRule,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinRule findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeCheckinRule> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinRule model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinRule model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinRule model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinRule model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRule> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRule> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRule> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
