package com.cszn.integrated.service.api.sys;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.sys.SysDataPermission;
import io.jboot.db.model.Columns;

import java.util.List;

public interface SysDataPermissionService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public SysDataPermission findById(Object id);


    /**
     * find all model
     *
     * @return all <SysDataPermission
     */
    public List<SysDataPermission> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(SysDataPermission model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(SysDataPermission model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(SysDataPermission model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(SysDataPermission model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SysDataPermission> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<SysDataPermission> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<SysDataPermission> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}