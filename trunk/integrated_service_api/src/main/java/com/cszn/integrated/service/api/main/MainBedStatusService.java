package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBedType;
import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBedStatus;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBedStatusService  {


    public List<MainBedStatus> findBedStatusByBaseId();

    /**
     * 通过code,id判断该状态是否存在
     * @param id
     * @param code
     * @return
     */
    public MainBedStatus findBedStatusByIdAndCode(String id,String code);

    /**
     * 床位类型列表
     * @param pageNumber
     * @param pageSize
     * @param bedStatus
     * @return
     */
    public Page<MainBedStatus> findListPage(Integer pageNumber, Integer pageSize, MainBedStatus bedStatus);


    /**
     * 根据id获取床位状态对象
     * @param id
     * @return
     */
    public MainBedStatus get(String id);


    /**
     * 保存床位状态
     * @param bedStatus
     * @param userId
     * @return
     */
    public boolean saveBedStatus(MainBedStatus bedStatus,String userId);



    /**
     * 保存：去重
     * @param bedStatus
     * @return
     */
    public String getDistinct(MainBedStatus bedStatus);


    /**
     * 删除床位状态
     * @param id
     * @param userId
     * @return
     */
    public boolean delBedStatus(String id,String userId);


    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelBedStatus(List<MainBedStatus> list,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBedStatus findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainBedStatus> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBedStatus model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBedStatus model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBedStatus model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBedStatus model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBedStatus> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBedStatus> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBedStatus> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}