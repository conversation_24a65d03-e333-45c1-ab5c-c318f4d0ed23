package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRefundAccount;
import com.cszn.integrated.service.entity.fina.FinaRefundApply;
import com.cszn.integrated.service.entity.fina.FinaRefundCard;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaRefundApplyService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaRefundApply findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaRefundApply
     */
    public List<FinaRefundApply> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaRefundApply model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaRefundApply model);
    
    
    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public boolean saveApply(FinaRefundApply model, List<FinaRefundAccount> accountList, List<FinaRefundCard> cardList);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaRefundApply model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaRefundApply model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaRefundApply> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaRefundApply> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaRefundApply> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}