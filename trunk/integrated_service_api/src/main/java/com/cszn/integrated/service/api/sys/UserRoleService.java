package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.sys.UserRole;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface UserRoleService  {

	/**
	 * 用户角色列表
	 * 
	 * @param userId
	 * @return
	 */
	public List<ZTree> userRolesTree(final String userId,final String systemType);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public UserRole findById(Object id);


    /**
     * find all model
     *
     * @return all <UserRole
     */
    public List<UserRole> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(UserRole model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(UserRole model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(UserRole model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(UserRole model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}