package com.cszn.integrated.service.api.fina;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordFollow;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface FinaExpenseRecordFollowService  {


    public List<FinaExpenseRecordFollow> getFollowByMainId(String mainId);

    /**
     * 获取随行
     * @param expenseId
     * @return
     */
    public List<FinaExpenseRecordFollow> getFollowByExpenseId(String expenseId);

    /**
     *设置随行人员参数
     * @param ff
     */
    public void setFF(FinaExpenseRecordFollow ff,String expenseId, String uniqueId,String checkinNo,String name,String idCard,String telephone,String bedId,
                      Date startTime,Date endTime);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordFollow findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordFollow
     */
    public List<FinaExpenseRecordFollow> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordFollow model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordFollow model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordFollow model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordFollow model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordFollow> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordFollow> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordFollow> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}