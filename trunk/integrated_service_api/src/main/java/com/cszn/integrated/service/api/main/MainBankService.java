package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBank;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBankService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainBank> paginateByCondition(MainBank model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBank findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainBank
     */
    public List<MainBank> findList();


    /**
     * find all model
     *
     * @return all <MainBank
     */
    public List<MainBank> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBank model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBank model);

    
    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBank model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBank model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBank> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBank> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBank> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}