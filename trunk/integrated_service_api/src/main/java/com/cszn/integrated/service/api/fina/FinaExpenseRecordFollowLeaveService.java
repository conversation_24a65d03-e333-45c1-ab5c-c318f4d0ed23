package com.cszn.integrated.service.api.fina;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordFollowLeave;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaExpenseRecordFollowLeaveService  {



    /**
     * 根据账单id查询最新离开记录
     * @param expenseId
     * @return
     */
    public FinaExpenseRecordFollowLeave getFl(String expenseId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordFollowLeave findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordFollowLeave
     */
    public List<FinaExpenseRecordFollowLeave> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordFollowLeave model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordFollowLeave model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordFollowLeave model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordFollowLeave model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordFollowLeave> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordFollowLeave> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordFollowLeave> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}