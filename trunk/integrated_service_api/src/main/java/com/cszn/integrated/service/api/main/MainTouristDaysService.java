package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainTouristDays;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainTouristDaysService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainTouristDays findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainTouristDays
     */
    public List<MainTouristDays> findList(String routeId);


    /**
     * find all model
     *
     * @return all <MainTouristDays
     */
    public List<MainTouristDays> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainTouristDays model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainTouristDays model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainTouristDays model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainTouristDays model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainTouristDays> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainTouristDays> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainTouristDays> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}