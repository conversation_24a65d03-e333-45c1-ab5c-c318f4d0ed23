package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainContractType;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainContractTypeService {

    /**
     * 通过合同类型编号查询合同类型
     * @param typeNo
     * @return
     */
    public MainContractType findContractTypeByTypeNo(String typeNo);

	/**
	 * 合同类型列表
	 * */
	public Page<MainContractType> findListPage(Integer pageNumber,Integer pageSize,MainContractType contractType);
	
	/**
	 * 保存合同类型
	 * */
	public boolean saveContractType(MainContractType contractType,String userId);
	
	/**
	 * 删除合同类型
	 * */
	public boolean delContractType(String id,String userId);
	
	/**
	 * 批量删除
	 * */
	public boolean batchContractType(String data,String userId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainContractType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomType
     */
    public List<MainContractType> findAll();
    
    
    /**
     * find all model
     *
     * @return all <MainRoomType
     */
    public List<MainContractType> findList();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainContractType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainContractType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainContractType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainContractType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainContractType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainContractType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainContractType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
