package com.cszn.integrated.service.api.equi;

import com.cszn.integrated.service.entity.equi.EquiModel;
import com.cszn.integrated.service.entity.main.MainBedEquiModel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface EquiModelService {

    public List<EquiModel> equiModelUnboundList(String modelType);

    public List<EquiModel> equiModelList(String modelType);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public EquiModel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<EquiModel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(EquiModel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(EquiModel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(EquiModel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(EquiModel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<EquiModel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<EquiModel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<EquiModel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
