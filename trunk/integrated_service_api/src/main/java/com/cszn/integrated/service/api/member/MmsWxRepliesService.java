package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxReplies;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

/**
 * 微信评论
 */
public interface MmsWxRepliesService  {

    public String saveMmsWxReplies(MmsWxReplies wxReplies);

    public Long getReplieCount(String userId);

    /**
     * 根据图文id获取评论
     * @return
     */
    public List<Record> getReplies(String momentId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxReplies findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxReplies
     */
    public List<MmsWxReplies> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxReplies model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxReplies model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxReplies model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxReplies model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}