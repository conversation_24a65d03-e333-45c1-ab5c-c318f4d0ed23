package com.cszn.integrated.service.api.mall;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.mall.MallOrder;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;
import java.util.Map;

public interface MallOrderService{

    public List<Record> cashPaymentSummary(String warehouseId,String startDate,String endDate,String userId);

    public Page<Record> findCashOrderByCreatePage(int pageNumber, int pageSize, MallOrder mallOrder);

    public Page<Record> getAllOfflineOrder(int pageNumber,int pageSize,String warehouseId,String cardNumber,String paymentTime
            ,String createName,String remark,String orderNo,String paymentType,String userId);

    public Page<Record> getAllOfflineCashOrder(int pageNumber,int pageSize,String warehouseId,String paymentTime,String createName,String remark,String userId,String orderNo);

    public Map<String,Object> getPaymentCodeOrderGetTotalPrice(String creatorId,String code,String warehouseId,String paymentType,String remark, JSONArray array);

    public Map<String,Object> getPaymentCodeOrderStatus(String code);

    public Map<String,Object> savePaymentCodeOrderStatus(String code,String status);

    /**
     * 保存线下现金订单
     * @param creatorId
     * @param paymentType
     * @param warehouseId
     * @param remark
     * @param array
     * @return
     */
    public Map<String,Object> saveCashOrder(String creatorId, String paymentType,String cardNumber
            ,String originalPrice,String discountPrice,String totalPrice,String activity,String giveModels
            ,String warehouseId,String coupon,String checkCode,String remark, JSONArray array,String cardTypeKey,String reallyPay,String scanContent);

    public MallOrder getMallOrderByCheckCode(String checkCode);

    /**
     * 保存线下订单
     * @param creatorId
     * @param cardNumber
     * @param warehouseId
     * @param array
     * @return
     */
    public Map<String,Object> saveOfflineOrder(String creatorId, String cardNumber,String warehouseId,String paymentType,String activity,String giveModels,String checkCode,String remark, JSONArray array);

    /**
     * 付款吗订单
     * @param creatorId
     * @param code
     * @param warehouseId
     * @param array
     * @return
     */
    public Map<String,Object> savePaymentCodeOrder(String creatorId,String code,String warehouseId,String paymentType,String remark, JSONArray array);

    /**
     * 豆豆券二维码
     * @param creatorId
     * @param beanCouponsCodes
     * @param warehouseId
     * @param array
     * @return
     */
    public Map<String,Object> saveBeanCouponsCodeOrder(String creatorId,String[] beanCouponsCodes,String warehouseId
            ,String paymentType,String remark, JSONArray array,String fullName,String telephone,String idcard);

    public Map<String,Object> saveBeanCouponsNumberOrder(String creatorId,double beanCouponsNumber,String warehouseId
            ,String paymentType,String remark, JSONArray array,String fullName,String telephone,String idcard);

    public Map<String,Object> saveGiftVoucherCodeOrder(String creatorId,String[] giftVoucherCodes,String warehouseId
            ,String paymentType,String remark, JSONArray array,String fullName,String telephone,String idcard);

    public Map<String, Object> saveGiftVoucherNumberOrder(String creatorId, double giftVoucherNum, String warehouseId, String paymentType
            , String remark, JSONArray array, String fullName, String telephone, String idcard);

    /**
     * 通过创建人获取订单分页
     * @param pageNumber
     * @param pageSize
     * @param mallOrder
     * @return
     */
    public Page<Record> findOrderByCreatePage(int pageNumber, int pageSize, MallOrder mallOrder);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallOrder findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallOrder> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallOrder model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallOrder model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallOrder model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallOrder model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
