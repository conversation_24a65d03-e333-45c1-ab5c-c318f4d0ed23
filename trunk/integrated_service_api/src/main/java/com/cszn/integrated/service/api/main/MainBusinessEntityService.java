package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBusinessEntity;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainBusinessEntityService {


    public Page<MainBusinessEntity> businessEntityPage(int pageNumber,int pageSize,MainBusinessEntity entity);

    public boolean saveBusinessEntity(MainBusinessEntity entity,String userId);

    public boolean delBusinessEntity(String id,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBusinessEntity findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MainBusinessEntity> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBusinessEntity model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainBusinessEntity model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainBusinessEntity model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBusinessEntity model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);

}
