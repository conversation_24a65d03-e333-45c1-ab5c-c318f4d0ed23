package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRefundCard;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaRefundCardService  {

	
	/**
     * find all model
     *
     * @return all <FinaRefundCard
     */
    public List<FinaRefundCard> findByApplyIdList(String applyId);
    
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaRefundCard findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaRefundCard
     */
    public List<FinaRefundCard> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaRefundCard model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaRefundCard model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaRefundCard model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaRefundCard model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaRefundCard> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaRefundCard> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaRefundCard> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}