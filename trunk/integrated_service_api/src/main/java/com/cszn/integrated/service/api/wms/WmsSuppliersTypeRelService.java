package com.cszn.integrated.service.api.wms;

import java.util.List;

import com.cszn.integrated.service.entity.wms.WmsSuppliersTypeRel;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface WmsSuppliersTypeRelService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsSuppliersTypeRel findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <WmsSuppliersTypeRel
     */
    public List<WmsSuppliersTypeRel> findList(String suppliersId);


    /**
     * find all model
     *
     * @return all <WmsSuppliersTypeRel
     */
    public List<WmsSuppliersTypeRel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsSuppliersTypeRel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsSuppliersTypeRel model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsSuppliersTypeRel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsSuppliersTypeRel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsSuppliersTypeRel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsSuppliersTypeRel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsSuppliersTypeRel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}