package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeBusinessTripApplyDate;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeBusinessTripApplyDateService {

    public PersOrgEmployeeBusinessTripApplyDate findById(Object id);

    public List<PersOrgEmployeeBusinessTripApplyDate> findListByApplyId(String applyId);

    public List<PersOrgEmployeeBusinessTripApplyDate> getEmpBusinessTripApplyDateListByDateRange(String empId, Date startDate, Date endDate);
}
