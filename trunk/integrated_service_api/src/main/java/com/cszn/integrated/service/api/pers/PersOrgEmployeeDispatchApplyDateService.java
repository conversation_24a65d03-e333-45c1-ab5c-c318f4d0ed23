package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeDispatchApplyDate;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeDispatchApplyDateService {
    public PersOrgEmployeeDispatchApplyDate findById(Object id);

    public List<PersOrgEmployeeDispatchApplyDate> findListByApplyId(String applyId);

    public List<PersOrgEmployeeDispatchApplyDate> getEmpDispatchApplyDateListByDateRange(String empId, Date startDate, Date endDate);
}
