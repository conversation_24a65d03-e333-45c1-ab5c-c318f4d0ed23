package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainTouristRoute;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainTouristRouteService {

    /**
     * 获取旅游线路分页
     * @param pageNumber
     * @param pageSize
     * @param mainTouristRoute
     * @return
     */
    public Page<MainTouristRoute> findTouristRoutePage(Integer pageNumber,Integer pageSize,MainTouristRoute mainTouristRoute);

    /**
     * 保存旅游线路
     * @param mainTouristRoute
     * @param userId
     * @return
     */
    public boolean saveTouristRoute(MainTouristRoute mainTouristRoute,String userId);

    /**
     * 删除旅游线路
     * @param id
     * @param userId
     * @return
     */
    public boolean delTouristRoute(String id,String userId);

    /**
     * 通过名字id判断是否存在
     * @param id
     * @param name
     * @return
     */
    public MainTouristRoute findTouristRouteByIdName(String id,String name);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainTouristRoute findById(Object id);


    /**
     * find all model
     *
     * @return all <MainTouristRoute
     */
    public List<MainTouristRoute> findAll();
    
    
    /**
     * find all model
     *
     * @return all <MainTouristRoute
     */
    public List<MainTouristRoute> findValidList();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainTouristRoute model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainTouristRoute model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainTouristRoute model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainTouristRoute model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainTouristRoute> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainTouristRoute> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainTouristRoute> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
