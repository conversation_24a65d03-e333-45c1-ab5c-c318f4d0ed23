package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLiabilityInsurance;

import java.util.List;

public interface PersOrgEmployeeLiabilityInsuranceService {

    PersOrgEmployeeLiabilityInsurance findById(Object id);

    List<PersOrgEmployeeLiabilityInsurance> findEmployeeLiabilityInsuranceList(String yearMonth, List<String> empIds);

    List<PersOrgEmployeeLiabilityInsurance> findEmployeeLiabilityInsuranceByApplyId(String id);

}
