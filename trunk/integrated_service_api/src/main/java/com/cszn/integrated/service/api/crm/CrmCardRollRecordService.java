package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecord;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollRecordService  {

    public boolean isNotbind(String id);

    public List<CrmCardRollRecord> findListByRollId(CrmCardRollRecord record,Integer endNum);

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecord> paginate(CrmCardRollRecord model, int page, int pageSize, Integer minValue, Integer maxValue);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecord findById(Object id);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecord findByRollNumber(String rollNumber);


    /**
     * find all model
     *
     * @return all <CrmCardRollRecord
     */
    public List<CrmCardRollRecord> findAll();


    /**
     * find list
     *
     * @return all <CrmCardRollRecord
     */
    public List<CrmCardRollRecord> findListByRollId(String rollId);

    public List<CrmCardRollRecord> findEnableListByRollId(String rollId);


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollRecord model);
    
    
    /**
     * create and bind roll
     *
     * @param model
     * @return true or false
     */
    public boolean createBindRoll(String rollId, String cardId, int quantity, String userId);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}