package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseRestaurantTable;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBaseRestaurantTableService  {

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseRestaurantTable> paginate(MainBaseRestaurantTable model, int page, int pageSize);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseRestaurantTable findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainBaseRestaurantTable
     */
    public List<MainBaseRestaurantTable> findList(String restaurantId, String isEnabled);

    /**
     * find all model
     *
     * @return all <MainBaseAreaTable
     */
    public List<MainBaseRestaurantTable> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseRestaurantTable model);

    
    /**
     * 保存餐厅餐桌
     * @param model
     * @return
     */
    public boolean saveRestaurantTable(List<MainBaseRestaurantTable> tableList, String userId);
    
    

    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseRestaurantTable model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseRestaurantTable model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseRestaurantTable model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseRestaurantTable> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseRestaurantTable> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseRestaurantTable> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}