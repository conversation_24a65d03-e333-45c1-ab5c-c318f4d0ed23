package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaContractRecord;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaContractRecordService {

    public List<FinaContractRecord> findContractRecord(String id,String cardTypeId,String name);

    public List<FinaContractRecord> contractRecordList();

    public Page<FinaContractRecord> findPageList(int pageNumber,int pageSize,FinaContractRecord record);

    public boolean saveContractRecord(FinaContractRecord contractRecord,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaContractRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaDayDeductStatistic
     */
    public List<FinaContractRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaContractRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaContractRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaContractRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaContractRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaContractRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaContractRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaContractRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
