package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsSmsCardType;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MmsSmsCardTypeService {

    public Page<Record> cardTypeTablePage(int pageNumber,int pageSize,String smsId,String typeClassify,String cardTypeName);

    public Page<Record> tablePage(int pageNumber,int pageSize,String smsId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsSmsCardType findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxArticle
     */
    public List<MmsSmsCardType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsSmsCardType model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsSmsCardType model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsSmsCardType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsSmsCardType model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
