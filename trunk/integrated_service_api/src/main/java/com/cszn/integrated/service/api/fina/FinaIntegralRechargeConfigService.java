package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaIntegralRechargeConfig;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaIntegralRechargeConfigService {

    public FinaIntegralRechargeConfig getConfigByDays(double days);

    public List<FinaIntegralRechargeConfig> findAllConfig();

    public boolean saveConfig(FinaIntegralRechargeConfig config,String userId);

    public Page<FinaIntegralRechargeConfig> configPage(int pageNumber,int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaIntegralRechargeConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordTourist
     */
    public List<FinaIntegralRechargeConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaIntegralRechargeConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaIntegralRechargeConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaIntegralRechargeConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaIntegralRechargeConfig model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaIntegralRechargeConfig> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaIntegralRechargeConfig> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaIntegralRechargeConfig> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);



}
