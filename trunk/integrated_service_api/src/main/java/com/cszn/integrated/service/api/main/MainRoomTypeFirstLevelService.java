package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainRoomTypeFirstLevel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainRoomTypeFirstLevelService {

    public Page<MainRoomTypeFirstLevel> pageList(int page,int limit,MainRoomTypeFirstLevel firstLevel);

    public boolean saveFirstLevel(MainRoomTypeFirstLevel firstLevel,String userId);

    public List<MainRoomTypeFirstLevel> getList();

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainRoomTypeFirstLevel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomTypeFirstLevel
     */
    public List<MainRoomTypeFirstLevel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainRoomTypeFirstLevel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainRoomTypeFirstLevel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainRoomTypeFirstLevel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainRoomTypeFirstLevel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainRoomTypeFirstLevel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainRoomTypeFirstLevel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainRoomTypeFirstLevel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}