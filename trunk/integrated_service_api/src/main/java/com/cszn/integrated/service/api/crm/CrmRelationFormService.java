package com.cszn.integrated.service.api.crm;

import java.util.List;

import com.cszn.integrated.service.entity.crm.CrmRelationForm;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface CrmRelationFormService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmRelationForm findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmRelationForm
     */
    public List<CrmRelationForm> findAll();
    
    /**
     * find list model
     *
     * @return CrmRelationForm list
     */
    public List<CrmRelationForm> findList(CrmRelationForm model);


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmRelationForm model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmRelationForm model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmRelationForm model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmRelationForm model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmRelationForm> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmRelationForm> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmRelationForm> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}