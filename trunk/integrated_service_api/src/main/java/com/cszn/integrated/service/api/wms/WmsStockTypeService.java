package com.cszn.integrated.service.api.wms;

import java.util.List;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.wms.WmsStockTypes;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface WmsStockTypeService {

    public String getAllCodePrefix(String typeId);

    public List<String> getChildTypeIds(String typeId);

    public List<ZTree> getPermissionMenu(String userId,String permission,String parentId,String type);

    public List<ZTree> findStockTypeTree(String isEnabled);
    
    public List<WmsStockTypes> stockTypesList(boolean isEnabled);

    /**
     * 货物分类分页
     * @param pageNumber
     * @param pageSize
     * @param wmsStockTypes
     * @return
     */
    public Page<WmsStockTypes> findStockTypePageList(Integer pageNumber, Integer pageSize, WmsStockTypes wmsStockTypes);

    /**
     * 货物分类表格树
     * @return
     */
    public List<ZTree> findStockTypeTableZtree(String isEnabled);

    /**
     * 保存货物分类
     * @param wmsStockTypes
     * @return
     */
    public boolean saveStockType(WmsStockTypes wmsStockTypes);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsStockTypes findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsStockTypes> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsStockTypes model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsStockTypes model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsStockTypes model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsStockTypes model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsStockTypes> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsStockTypes> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsStockTypes> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
