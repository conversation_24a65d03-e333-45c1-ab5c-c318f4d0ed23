package com.cszn.integrated.service.api.main;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBaseBuilding;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseBuildingService  {

    /**
     * 判断楼栋是否属于这个基地
     * @param buildingId
     * @param baseId
     * @return
     */
    public boolean buildingBelongToBase(String buildingId,String baseId);

    /**
     * 判断是否存在改楼栋
     * @param mainBaseBuilding
     * @return
     */
    public MainBaseBuilding buildingIsExist(MainBaseBuilding mainBaseBuilding);

    /**
     * 通过基地id获取楼栋数
     * @param baseId
     * @return
     */
    public Integer queryBuildingCountByBaseId(String baseId);


    //public boolean batchDelMainBaseBuilding(List<MainBaseBuilding> buildingList,String userId);


    /**
     * 通过基地id获取楼栋
     */
    public Page<MainBaseBuilding> getBuildingPageByBaseId(String baseId,Integer pageNumber,Integer pageSize);


    /**
     * 通过基地id获取楼栋
     * @param baseIds
     * @return
     */
    public List<MainBaseBuilding> getBuildingListByBaseIds(String[] baseIds);

    /**
     * 保存楼栋
     * @param mainBaseBuilding
     * @return
     */
    public boolean saveBuilding(MainBaseBuilding mainBaseBuilding);

    /**
     * 删除楼栋
     * @param id
     * @return
     */
    public boolean delBuilding(String id,String userId);

    /**
     * 修改总床位和总房间数量
     * @param id
     * @param roomNum
     * @param bedNum
     * @param userId
     * @return
     */
    public boolean updateRoomAndBedNumber(String id,int roomNum,int bedNum,String userId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseBuilding findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBuilding
     */
    public List<MainBaseBuilding> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseBuilding model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseBuilding model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseBuilding model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseBuilding model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseBuilding> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseBuilding> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseBuilding> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}