package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBookChannel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBookChannelService {

    /**
     * 获取所有可用的预定渠道
     * @return
     */
    public List<MainBookChannel> findBookChannelList();

    /**
     * 通过预订渠道名字和编号判断是否存在
     * @return
     */
    public MainBookChannel bookChannelIsExist(MainBookChannel bookChannel);

    /**
     * 获取预订渠道
     * @param pageNumber
     * @param pageSize
     * @param bookChannel
     * @return
     */
    public Page<MainBookChannel> findPageList(Integer pageNumber,Integer pageSize,MainBookChannel bookChannel);

    /**
     * 保存预订渠道
     * @param bookChannel
     * @param userId
     * @return
     */
    public boolean saveBookChannel(MainBookChannel bookChannel,String userId);

    /**
     * 删除预订渠道
     * @param id
     * @param userId
     * @return
     */
    public boolean delBookChannel(String id,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBookChannel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainBookChannel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBookChannel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBookChannel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBookChannel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBookChannel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBookChannel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBookChannel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBookChannel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
