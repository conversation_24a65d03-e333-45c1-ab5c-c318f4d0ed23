package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsMemberFace;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsMemberFaceService  {

    /**
     * 会员上传人脸图片接口
     * @param name
     * @param idcard
     * @param image
     * @return
     */
    public boolean uploadFace(String memberId,String image);

    /**
     * 获取最新人脸图片及审核结果
     * @param idcard
     * @return
     */
    public Record getFace(String idcard);

    /**
     * 分页条件查询
     * @param pageNumber
     * @param pageSize
     * @param fullName
     * @param idcard
     * @return
     */
    public Page<Record> findList(Integer pageNumber,Integer pageSize,String fullName,String idcard);


    /**
     * 列表查询
     * @param fullName
     * @param idcard
     * @return
     */
    public List<Record> findList(String fullName,String idcard);


    /**
     * 根据审核标识获取人脸审核list
     * @param verifyFlag
     * @return
     */
    public List<MmsMemberFace> getListByVerifyFlag(String verifyFlag);


    /**
     * 新增档案时生成人脸
     * @param memberId
     * @param imgUrl
     * @return
     */
    public boolean generateMemberFace(String memberId,String imgUrl);


    /**
     * 获取人脸模板对象
     * @param id
     * @return
     */
    public MmsMemberFace get(String id);


    /**
     * 保存人脸
     * @param face
     * @param picturePath
     * @return
     */
    public boolean saveMemberFace(MmsMemberFace face,String picturePath);


    /**
     * 根据身份证临时获取人脸
     * @param idcard
     * @return
     */
    public Record getTempFaceByIdCard(String idcard);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMemberFace findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMemberFace
     */
    public List<MmsMemberFace> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMemberFace model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsMemberFace model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsMemberFace model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMemberFace model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsMemberFace> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsMemberFace> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsMemberFace> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}