package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.common.XmSelect;
import com.cszn.integrated.service.entity.sys.SysGroup;
import com.cszn.integrated.service.entity.sys.SysGroupUser;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface SysGroupService  {

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SysGroup> paginate(SysGroup model, int page, int pageSize);
    
    /**
     * find all model
     *
     * @return all <XmSelect
     */
    public List<XmSelect> groupXmSelectTree();
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public SysGroup findById(Object id);


    /**
     * find all model
     *
     * @return all <SysGroup
     */
    public List<SysGroup> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(SysGroup model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(SysGroup model);
    

    /**
     * 保存信息
     * @param model
     * @return
     */
    public boolean saveGroup(SysGroup model, List<SysGroupUser> userList);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(SysGroup model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(SysGroup model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SysGroup> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<SysGroup> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<SysGroup> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}