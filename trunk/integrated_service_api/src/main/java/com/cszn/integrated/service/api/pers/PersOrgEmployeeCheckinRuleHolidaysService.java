package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinRuleHolidays;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeCheckinRuleHolidaysService {

    public PersOrgEmployeeCheckinRuleHolidays getRuleHolidaysByDate(String ruleId,Date date);

    public boolean isWorkDayByDate(String ruleId,Date createDate,Date workDate);

    public boolean isRestDayByDate(String ruleId, Date createDate,Date workDate);

    public boolean isWorkDay(String ruleId, Date date);

    public boolean isRestDay(String ruleId,Date date);

    public PersOrgEmployeeCheckinRuleHolidays getRuleHolidaysByRuleId(String ruleId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinRuleHolidays findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeCheckinRuleHolidays> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinRuleHolidays model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinRuleHolidays model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinRuleHolidays model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinRuleHolidays model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleHolidays> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleHolidays> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleHolidays> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
