package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.sys.SysGroupUser;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface SysGroupUserService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public SysGroupUser findById(Object id);
    
    
    /**
     * find list
     *
     * @return all <SysGroupUser
     */
    public List<SysGroupUser> findListByGroupId(String groupId);


    /**
     * find all model
     *
     * @return all <SysGroupUser
     */
    public List<SysGroupUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(SysGroupUser model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(SysGroupUser model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(SysGroupUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(SysGroupUser model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<SysGroupUser> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<SysGroupUser> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<SysGroupUser> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}