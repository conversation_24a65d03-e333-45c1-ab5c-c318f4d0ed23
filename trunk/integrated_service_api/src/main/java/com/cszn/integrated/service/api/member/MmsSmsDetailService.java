package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsSmsDetail;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MmsSmsDetailService {

    public List<MmsSmsDetail> findNotSentDetail(int size);

    public Page<MmsSmsDetail> tablePage(int pageNumber,int pageSize,MmsSmsDetail detail);

    public boolean saveSmsDetail(String smsId,String userId);

    public boolean detailDel(String id,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsSmsDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsSmsDetail
     */
    public List<MmsSmsDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsSmsDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsSmsDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsSmsDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsSmsDetail model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
