package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainSmsType;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainSmsTypeService {

    public void bmpInvokeGetForm(String formId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainSmsType findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainSmsType
     */
    public List<MainSmsType> findList();


    /**
     * find all model
     *
     * @return all <MainSmsType
     */
    public List<MainSmsType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainSmsType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainSmsType model);

    /**
     * 保存信息
     * @param model
     * @return
     */
    public boolean saveType(MainSmsType model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainSmsType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainSmsType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainSmsType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainSmsType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainSmsType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}