package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaConsumeRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface FinaConsumeRecordService {


    /**
     * 获取分页  用户商户
     * @param pageNumber
     * @param pageSize
     * @param startDate
     * @param endDate
     * @param consumeRecord
     * @param fullName
     * @return
     */
    public Page<Record> getConsumeRecordByMerchant(Integer pageNumber, Integer pageSize,String startDate, String endDate,FinaConsumeRecord consumeRecord,String fullName);


    /**
     * 获取分页 用于消费记录
     * @param pageNumber
     * @param pageSize
     * @param cardNumber
     * @param startDate
     * @param endDate
     * @return
     */
    public Page<FinaConsumeRecord> getConsumeRecordsByRecord(Integer pageNumber, Integer pageSize,String cardNumber, String startDate, String endDate);

    /**
     * 插入消费记录表
     * @param cardNumber
     * @param describe
     * @param type
     * @param refundFlag
     * @param amount
     * @param consumeTimes
     * @param userId
     * @return
     */
    public boolean saveConsumeRecord(String id,String appNo,String appOrderNo,String baseId,String cardNumber,String describe,String type,String refundFlag,Double amount,Double consumeTimes,Double points,Double integrals,Double beanCoupons,String status,String consumeSource,String userId);


    /**
     * 查询会员所有记录：充值，赠送，日常扣卡
     * @param pageNumber
     * @param pageSize
     * @param cardNumber
     * @param startDate
     * @param endDate
     * @return
     */
    public Page<Record> getAllRecordByCard(Integer pageNumber,Integer pageSize,String cardNumber,String startDate,String endDate);

    /**
     * 删除
     * @param id
     * @param userId
     * @return
     */
    public boolean deleteConsumeRecord(String id,String userId);

    /***
     * 确认消费
     * @param consumeId
     * @return
     */
    public Boolean confirmConsume(String consumeId);


    /**
     * 确认补充扣费
     * @param card
     * @param record
     * @param userId
     * @return
     */
    public Boolean confirmAppend(FinaMembershipCard card,FinaConsumeRecord record,String userId);


    /**
     * 根据会员卡获取会员卡账单
     * @param cardNumber
     * @return
     */
    public List<FinaConsumeRecord> getConsumeRecordsByCardNumber(String cardNumber);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaConsumeRecord findById(Object id);


    /**
     * 根据id查询对象
     * @param id
     * @return
     */
    public FinaConsumeRecord get(String id);


    /**
     * find all model
     *
     * @return all <MmsConsumeRecord
     */
    public List<FinaConsumeRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaConsumeRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaConsumeRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaConsumeRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaConsumeRecord model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}