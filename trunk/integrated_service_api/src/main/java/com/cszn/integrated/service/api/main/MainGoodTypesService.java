package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainGoodTypes;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainGoodTypesService {

    /**
     * 保存商品类型
     * @param type
     * @param userId
     * @return
     */
    public boolean saveGoodType(MainGoodTypes type,String userId);

    /**
     * 获取类型list
     */
    public List<Record> goodTypeTableTree();

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainGoodTypes findById(Object id);


    /**
     * find all model
     *
     * @return all <MainGoodTypes
     */
    public List<MainGoodTypes> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainGoodTypes model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainGoodTypes model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainGoodTypes model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainGoodTypes model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainGoodTypes> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainGoodTypes> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainGoodTypes> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
