package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallOrderItem;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface MallOrderItemService {


    public List<Record> getItemByOrderId(String orderId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallOrderItem findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallOrderItem> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallOrderItem model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallOrderItem model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallOrderItem model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallOrderItem model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
