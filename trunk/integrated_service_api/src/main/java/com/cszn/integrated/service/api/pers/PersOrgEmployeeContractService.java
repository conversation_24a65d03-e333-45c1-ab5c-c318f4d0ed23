package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeContract;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeContractService {

    public Page<Record> employeeContractPageList(int page,int limit,String deptId,String name,String archiveStatus,String contractType,String contractStatus,String userId);

    public boolean saveEmployeeContract(PersOrgEmployeeContract contract,String userId);

    /**
     *
     * @param pageNumber
     * @param pageSize
     * @param contract
     * @return
     */
    public Page<PersOrgEmployeeContract> employeeContractPage(int pageNumber,int pageSize,PersOrgEmployeeContract contract);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeContract findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeContract> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeContract model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeContract model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeContract model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeContract model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeContract> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeContract> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeContract> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
