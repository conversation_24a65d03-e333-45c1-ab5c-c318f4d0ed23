package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.food.FoodInfo;
import com.cszn.integrated.service.entity.main.MainUserGroup;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainUserGroupService {

    MainUserGroup findById(Object id);

    Page<MainUserGroup> findPageList(int page,int limit,MainUserGroup userGroup);

    boolean saveUserGroup(MainUserGroup userGroup,String userId,String relatedUserIds);

    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainUserGroup model);

    List<MainUserGroup> getUserGroupList();
}
