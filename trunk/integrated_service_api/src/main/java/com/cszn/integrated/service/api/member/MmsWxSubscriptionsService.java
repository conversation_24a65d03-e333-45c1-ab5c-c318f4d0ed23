package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsWxSubscriptions;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsWxSubscriptionsService  {

    public List<MmsWxSubscriptions> find(String sql,Object...params);

    public Page<MmsWxSubscriptions> subscriptionsPage(Integer pageNumber, Integer pageSize, String userId, String subscriberId);

    public boolean saveSubscriptions(MmsWxSubscriptions wxSubscriptions);

    public Long getSubscriptionCount(String userId);

    public Long getubscriptionedCount(String subscriberId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsWxSubscriptions findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxSubscriptions
     */
    public List<MmsWxSubscriptions> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsWxSubscriptions model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsWxSubscriptions model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsWxSubscriptions model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsWxSubscriptions model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}