package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgCompany;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface PersOrgCompanyService {

    PersOrgCompany findById(Object id);

    Page<PersOrgCompany> pageList(int page,int limit,PersOrgCompany company);

    boolean companySave(PersOrgCompany company,String userId);

    List<PersOrgCompany>  orgCompanyList();

    List<PersOrgCompany> orgCompanyList(String providentFundUserId,String socialSecurityUserId);
}
