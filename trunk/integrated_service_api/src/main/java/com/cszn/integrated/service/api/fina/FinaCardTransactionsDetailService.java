package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.fina.FinaCardTransactionsDetail;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardTransactionsDetailService {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardTransactionsDetail findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FinaCardTransactionsDetail
     */
    public List<FinaCardTransactionsDetail> findListByTransactionsId(String transactionsId);


    /**
     * find all model
     *
     * @return all <FinaCardTransactionsDetail
     */
    public List<FinaCardTransactionsDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardTransactionsDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardTransactionsDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardTransactionsDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardTransactionsDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardTransactionsDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardTransactionsDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardTransactionsDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}