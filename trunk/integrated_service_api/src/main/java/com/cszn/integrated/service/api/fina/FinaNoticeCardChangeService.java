package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardTransfer;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaNoticeCardChange;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaNoticeCardChangeService  {


    /**
     * 获取未通知到的最新的变更记录
     * @param fc
     * @return
     */
    public FinaNoticeCardChange getRecordNotNotice(FinaNoticeCardChange fc);


    /**
     * 变更最新的变更记录
     * @param fc
     * @return
     */
    public boolean noticeCardChange(FinaNoticeCardChange fc);


    /**
     * 转移会员卡升级-将账单上的旧卡转变为新卡
     * @param erList
     * @param bookAndTouristList
     * @return
     */
    public boolean saveNoticeCardChange(List<FinaExpenseRecord> erList, List<FinaExpenseRecord> bookAndTouristList,String oldCardNumber,String newCardNumber,String userId);


    /**
     * 转移会员卡升级：通知旅居账单编辑会员卡
     * @param cardTransfer
     * @return
     */
    public boolean noticeCardChangeBill(FinaMembershipCard oldCard,FinaMembershipCard newCard,FinaCardTransfer cardTransfer);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaNoticeCardChange findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaNoticeCardChange
     */
    public List<FinaNoticeCardChange> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaNoticeCardChange model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaNoticeCardChange model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaNoticeCardChange model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaNoticeCardChange model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaNoticeCardChange> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaNoticeCardChange> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaNoticeCardChange> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}