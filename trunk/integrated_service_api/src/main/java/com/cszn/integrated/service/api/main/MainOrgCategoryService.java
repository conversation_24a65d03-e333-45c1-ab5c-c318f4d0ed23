package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainOrgCategory;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MainOrgCategoryService  {

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MainOrgCategory> paginateByCondition(MainOrgCategory model, int pageNumber, int pageSize);
    
    /**
     * findList
     *
     * @return List<MainOrgCategory>
     */
    public List<MainOrgCategory> findList();
    
    /**
     * 角色保存方法
     * 
     * @param model
     * @param menuIds
     * @return
     */
    public Ret modelSave(MainOrgCategory model);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainOrgCategory findById(Object id);


    /**
     * find all model
     *
     * @return all <MainOrgCategory
     */
    public List<MainOrgCategory> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainOrgCategory model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainOrgCategory model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainOrgCategory model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainOrgCategory model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}