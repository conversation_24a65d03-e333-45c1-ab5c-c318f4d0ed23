package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseAdvanceBookSetting;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainBaseAdvanceBookSettingService {


    /**
     * 删除基地配置
     * @param id
     * @param userId
     * @return
     */
    public boolean delBaseAdvanceBookSetting(String id,String userId);

    /**
     * 通过基地id获取基地配置List
     * @param baseId
     * @return
     */
    public List<MainBaseAdvanceBookSetting> baseAdvanceBookSettingList(String baseId);

    /**
     *
     * @param baseAdvanceBookSetting
     * @param userId
     * @return
     */
    public boolean saveBaseAdvanceBookSetting(List<MainBaseAdvanceBookSetting> baseAdvanceBookSetting,String delIds,String userId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseAdvanceBookSetting findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MainBaseAdvanceBookSetting> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseAdvanceBookSetting model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainBaseAdvanceBookSetting model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainBaseAdvanceBookSetting model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseAdvanceBookSetting model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
