package com.cszn.integrated.service.api.fina;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.service.entity.fina.FinaMemberBillDetail;
import com.cszn.integrated.service.entity.fina.FinaMemberSettleMain;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface FinaMemberSettleMainService {

    /**
     * 欠费查询list
     * @return
     */
    public List<Record> findArrearageList();
    
    /**
     * 判断主账单中的明细是否全部结算完，如果全部结算完成改变主账单状态
     * @param settleMain
     * @param userId
     * @return
     */
    public boolean updateSettleMainStatus(FinaMemberSettleMain settleMain,String userId);

    /**
     * 保存机构推送过来的费用账单
     * @param jsonObject
     * @return
     */
    public boolean orgBillDetailSave(JSONObject jsonObject);

    /**
     * 作废住账单
     * @param id
     * @param userId
     * @return
     */
    public boolean delSettleMain(String id,String userId);

    public void sendSettleMain(String id,String type,String date);

    /**
     * 结算账单
     * @return
     */
    public Map<String,Object> memberMainSettle(FinaMemberSettleMain settleMain,String userId);

    public Map<String,Object> SettlememberAllMain(FinaMemberSettleMain settleMain,String userId,List<List<Map<String,String>>> allSmsMap,List<List<FinaMemberBillDetail>> allBillList,List<FinaMemberSettleMain> settleMainList);

    /**
     * 通过入住流水号查询账单list
     * @param checkinNo
     * @return
     */
    public List<Record> findSettleMainListByCheckinNo(String checkinNo);

    /**
     * 获取费用明细中的会员分页
     * @param pageNumber
     * @param pageSize
     * @param main
     * @return
     */
    public Page<Record> findSettleMemberPage(Integer pageNumber,Integer pageSize,FinaMemberSettleMain main);

    /**
     * 获取机构养老结算分页
     * @param pageNumber
     * @param pageSize
     * @param main
     * @return
     */
    public Page<Record> findMemberSettleMainPage(Integer pageNumber, Integer pageSize, FinaMemberSettleMain main);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMemberSettleMain findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaFunctionSwitch
     */
    public List<FinaMemberSettleMain> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMemberSettleMain model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaMemberSettleMain model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaMemberSettleMain model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMemberSettleMain model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaMemberSettleMain> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaMemberSettleMain> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaMemberSettleMain> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
