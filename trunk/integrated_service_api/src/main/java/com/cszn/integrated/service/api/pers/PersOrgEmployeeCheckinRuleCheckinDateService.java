package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinRuleCheckinDate;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeCheckinRuleCheckinDateService {

    public PersOrgEmployeeCheckinRuleCheckinDate getRuleCheckinDateByDate(String ruleId, Date createDate,Date workDate);

    public List<PersOrgEmployeeCheckinRuleCheckinDate> getRuleCheckinDateListByDate(String ruleId, Date date);

    public PersOrgEmployeeCheckinRuleCheckinDate getRuleCheckinDateByDate(String ruleId, Date date);

    public PersOrgEmployeeCheckinRuleCheckinDate getCheckinDateByDate(String ruleId, Date date);

    public boolean delCheckinDateByRuleId(String ruleId,String userId);

    public List<PersOrgEmployeeCheckinRuleCheckinDate> getCheckinDateByRuleId(String ruleId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinRuleCheckinDate findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeCheckinRuleCheckinDate> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinRuleCheckinDate model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinRuleCheckinDate model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinRuleCheckinDate model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinRuleCheckinDate model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleCheckinDate> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleCheckinDate> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleCheckinDate> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
