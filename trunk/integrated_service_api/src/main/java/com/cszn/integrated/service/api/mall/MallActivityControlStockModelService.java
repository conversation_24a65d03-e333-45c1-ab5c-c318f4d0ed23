package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallActivityControlStockModel;

import java.util.List;

public interface MallActivityControlStockModelService {

    public MallActivityControlStockModel findById(Object id);

    public List<MallActivityControlStockModel> findStockModelListByActivityId(String activityId);

    public boolean saveActivityStockModel(MallActivityControlStockModel activityStockModel, String userId);

    public List<MallActivityControlStockModel> findActivityControlStockModelList(String activityId,String configId);
}
