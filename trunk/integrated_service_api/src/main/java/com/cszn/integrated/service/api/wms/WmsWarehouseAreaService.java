package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsWarehouseAreas;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface WmsWarehouseAreaService {

    /**
     * 保存库区
     * @param wmsWarehouseAreas
     * @return
     */
    public boolean saveWarehouseArea(WmsWarehouseAreas wmsWarehouseAreas);

    /**
     * 库区分页
     * @param pageNumber
     * @param pageSize
     * @param wmsWarehouseAreas
     * @return
     */
    public Page<WmsWarehouseAreas> findWarehouseAreaPageList(Integer pageNumber, Integer pageSize, WmsWarehouseAreas wmsWarehouseAreas);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsWarehouseAreas findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsWarehouseAreas> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsWarehouseAreas model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsWarehouseAreas model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsWarehouseAreas model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsWarehouseAreas model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsWarehouseAreas> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsWarehouseAreas> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsWarehouseAreas> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
