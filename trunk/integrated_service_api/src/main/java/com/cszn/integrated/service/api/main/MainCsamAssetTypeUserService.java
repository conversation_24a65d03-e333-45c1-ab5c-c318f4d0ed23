package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCsamAssetTypeUser;
import com.cszn.integrated.service.entity.wms.WmsStockTypeUser;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainCsamAssetTypeUserService {

    public boolean saveMainCsamAssetTypeUser(MainCsamAssetTypeUser typeUser,String userId);

    public boolean delMainCsamAssetTypeUser(String id,String userId);

    public List<MainCsamAssetTypeUser> findTypeUserList(String typeId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCsamAssetTypeUser findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<MainCsamAssetTypeUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCsamAssetTypeUser model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCsamAssetTypeUser model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCsamAssetTypeUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCsamAssetTypeUser model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCsamAssetTypeUser> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCsamAssetTypeUser> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCsamAssetTypeUser> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
