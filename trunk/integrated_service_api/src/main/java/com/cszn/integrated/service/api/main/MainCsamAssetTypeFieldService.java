package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCsamAssetTypeField;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainCsamAssetTypeFieldService {


    public boolean assetTypeFieldSave(MainCsamAssetTypeField field,String userId);

    public Page<MainCsamAssetTypeField> assetTypeFieldPageList(int pageNumber,int pageSize,MainCsamAssetTypeField field);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCsamAssetTypeField findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomType
     */
    public List<MainCsamAssetTypeField> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCsamAssetTypeField model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCsamAssetTypeField model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCsamAssetTypeField model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCsamAssetTypeField model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCsamAssetTypeField> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCsamAssetTypeField> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCsamAssetTypeField> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
