package com.cszn.integrated.service.api.member;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.service.entity.member.MmsFaceLiveUpload;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MmsFaceLiveUploadService {


    /**
     * 基地提交人脸识别入住、退住用户
     * @param appNo
     * @param liveFlag
     * @param idcards
     * @param times
     * @return
     */
    public Map<String,Object> uploadLiveFace(String appNo,String baseId,String liveFlag,String times,String recordData);

    /**
     * 获取当天入住或退住人脸数据
     * @param appNo 商户id
     * @param  baseId
     * @param liveFlag 入住退住标记 1：入住 2：退住
     * @return
     */
    public List<Map<String,Object>> getPersonFace(String appNo,String baseId,String liveFlag);


    /**
     * 忽略上传人脸到设备
     * @param appNo
     * @param baseId
     * @param bookNo
     * @return
     */
    public boolean ignoreUpload(String appNo,String baseId,String bookNo);


    /**
     * 根据预订号获取临时人脸
     * @param fu
     * @return
     */
    public Record getFaceByBookNo(MmsFaceLiveUpload fu);


    /**
     * 测试：查询
     * @param fu
     * @return
     */
    public List<MmsFaceLiveUpload> getQuery(MmsFaceLiveUpload fu,String date);


    /**
     * 主数据：保存基地(预订)入住/退住人脸数据
     * @param jsonObject
     * @return
     */
    public boolean faceLiveUploadSave(JSONObject jsonObject);


    /**
     * 根据预订号和入住号查询记录
     * @param appNo
     * @param baseId
     * @param bookNo
     * @param checkinNo
     * @return
     */
    public MmsFaceLiveUpload getFuByBookAndCheckinNo(String appNo,String baseId,String bookNo,String checkinNo);


    /**
     * 设置参数
     * @param appNo
     * @param baseId
     * @param bookNo
     * @param checkinNo
     * @param idcard
     * @param time
     * @return
     */
    public MmsFaceLiveUpload setParams(String appNo, String baseId, String bookNo, String checkinNo,
                                       String idcard, String liveFlag, String status,Date time);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsFaceLiveUpload findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxArticle
     */
    public List<MmsFaceLiveUpload> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsFaceLiveUpload model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsFaceLiveUpload model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsFaceLiveUpload model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsFaceLiveUpload model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
