package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface BioApiService {


    /**
     * bio手环数据处理入口
     * @param bioData
     */
    public String dealBioData(String bioData);


    /**
     * 保存bio设备号及对应命令
     * @param equipmentNo
     * @param command
     * @return
     */
    public boolean saveCommandRecord(String equipmentNo,String command);


    /**
     * 分页查询手环日常定位数据
     * @param pageNumber
     * @param pageSize
     * @param fullName
     * @param idcard
     * @return
     */
    public Page<Record> findListPage(Integer pageNumber,Integer pageSize,String fullName,String idcard);


    /**
     * 分页查询手环报警定位数据
     * @param pageNumber
     * @param pageSize
     * @param fullName
     * @param idcard
     * @return
     */
    public Page<Record> findList(Integer pageNumber,Integer pageSize,String fullName,String idcard);


    /**
     * 删除
     * @param id
     * @param type
     * @return
     */
    public boolean delLocation(String id,String type,String userId);


}
