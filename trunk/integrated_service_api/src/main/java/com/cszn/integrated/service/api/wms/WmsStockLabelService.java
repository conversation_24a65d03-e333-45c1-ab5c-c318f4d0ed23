package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsStockLabel;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface WmsStockLabelService {

    public boolean saveTypeLabelRel(String typeId, String labelIds,String userId);

    public boolean saveStockLabelRel(String stockId, String labelIds,String userId);

    public List<WmsStockLabel> getStockLabelList();

    public Page<WmsStockLabel> pageList(int pageNumber, int pageSize, WmsStockLabel label);

    public boolean saveStockLabel(WmsStockLabel label,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsStockLabel findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<WmsStockLabel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsStockLabel model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(WmsStockLabel model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(WmsStockLabel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsStockLabel model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
