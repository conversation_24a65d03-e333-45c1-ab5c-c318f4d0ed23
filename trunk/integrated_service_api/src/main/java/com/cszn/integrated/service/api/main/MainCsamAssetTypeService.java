package com.cszn.integrated.service.api.main;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.main.MainCsamAssetType;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import sun.reflect.generics.tree.Tree;

import java.util.List;

public interface MainCsamAssetTypeService {

    public boolean assetTypeSave(MainCsamAssetType type,String userId);

    public List<Record> csamTypeTableTree(String isLeaver);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCsamAssetType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomType
     */
    public List<MainCsamAssetType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCsamAssetType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCsamAssetType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCsamAssetType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCsamAssetType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCsamAssetType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCsamAssetType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCsamAssetType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
