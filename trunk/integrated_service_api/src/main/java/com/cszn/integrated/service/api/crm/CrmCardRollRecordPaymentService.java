package com.cszn.integrated.service.api.crm;

import java.util.List;

import com.cszn.integrated.service.entity.crm.CrmCardRollRecordPayment;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface CrmCardRollRecordPaymentService  {

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecordPayment> paginate(CrmCardRollRecordPayment model, int page, int pageSize);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollRecordPayment findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollRecordPayment
     */
    public List<CrmCardRollRecordPayment> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollRecordPayment model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollRecordPayment model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollRecordPayment model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollRecordPayment model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollRecordPayment> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollRecordPayment> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollRecordPayment> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}