package com.cszn.integrated.service.api.mall;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.mall.MallActivity;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;

public interface MallActivityService {

    public List<MallActivity> getProductActivityList(String warehouseId,String type,JSONArray stockModelArray, Date date);

    public List<MallActivity> getNewActivityList(String warehouseId, String type, JSONArray stockModelArray, Date date,String systemType);

    public List<MallActivity> getActivityList(double totalAmount,String warehouseId, Date date);

    public Page<Record> mallActivityPageList(int pageNumber,int pageSize,MallActivity activity,String inUse);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallActivity findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallActivity> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallActivity model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallActivity model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallActivity model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallActivity model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
