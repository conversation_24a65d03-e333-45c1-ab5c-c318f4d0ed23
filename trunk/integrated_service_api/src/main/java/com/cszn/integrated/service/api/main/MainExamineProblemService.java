package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainExamineProblem;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainExamineProblemService {

    MainExamineProblem findById(Object id);

    Page<MainExamineProblem> findPageList(int page,int limit,MainExamineProblem problem);

    boolean problemSave(MainExamineProblem problem,String userId);

    List<MainExamineProblem> getSchemeProblemList(String schemeId);

    List<MainExamineProblem> recordResultProblemList(String recordId);

    public List<MainExamineProblem> recordResultExceptionProblemList(String recordId);
}
