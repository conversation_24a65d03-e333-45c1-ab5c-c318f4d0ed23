package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseGoodConfigs;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseGoodConfigsService {


    public MainBaseGoodConfigs getBaseGoodConfig(String baseId,String goodId);

    public boolean saveConfig(MainBaseGoodConfigs config,String userId);

    public Page<Record> pageList(Integer pageNumber,Integer pageSize,MainBaseGoodConfigs config);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseGoodConfigs findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseFloor
     */
    public List<MainBaseGoodConfigs> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseGoodConfigs model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseGoodConfigs model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseGoodConfigs model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseGoodConfigs model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseGoodConfigs> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseGoodConfigs> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseGoodConfigs> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
