package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface FinaCardDeductRecordService  {


    /**
     * 设置扣卡记录参数：用于结算
     * @param cd
     * @param expenseId
     * @param cardNumber
     * @param fullName
     * @param withholdTimes
     * @param withholdAmount
     * @param actualTimes
     * @param actualAmount
     * @param suppleTimes
     * @param suppleAmount
     * @param returnTimes
     * @param returnAmount
     */
    public void setCdBySettle(FinaCardDeductRecord cd,String expenseId,String cardNumber,String fullName,Double withholdTimes,
                              Double withholdAmount,Double actualTimes,Double actualAmount,Double suppleTimes,Double suppleAmount,
                              Double returnTimes,Double returnAmount);


    /**
     * 设置扣卡记录参数
     * @param cd
     * @param withholdTimes
     * @param withholdAmount
     * @param actualTimes
     * @param actualAmount
     */
    public void setCd(FinaCardDeductRecord cd,String expenseId,String cardNumber,String fullName,Double withholdTimes,
                      Double withholdAmount,Double actualTimes,Double actualAmount);


    /**
     * 更新正在使用卡的总的锁定天数和金额
     * @return
     */
    public List<FinaMembershipCard> getLockTimesAndAmount();



    /**
     * 根据主账单id查找扣卡记录
     * @param expenseId
     * @return
     */
    public FinaCardDeductRecord getNowByexpenseId(String expenseId);



    /**
     * 根据主账单id获取所属的扣卡汇总记录
     * @param expenseId
     * @return
     */
    public List<FinaCardDeductRecord> getListByExpenseId(String expenseId);



    /**
     * 根据主账单id和会员卡号查询会员卡扣卡汇总
     * @param expenseId
     * @param cardNumber
     * @return
     */
    public FinaCardDeductRecord getCardDeductByExpenseIdAndCardNumber(String expenseId,String cardNumber);




    /**
     * 根据主账单或附属账单和会员卡获取扣卡汇总
     * @param er
     * @return
     */
    public List<FinaCardDeductRecord>  getListByEr(FinaExpenseRecord er,String cardNumber);


    /**
     * 根据主账单或附属账单和会员卡获取已结算记录
     * @param er
     * @return
     */
    public List<Record> getSettledListByEr(FinaExpenseRecord er,String cardNumber);


    /**
     * 根据主账单或附属账单及新会员卡获取并修改扣卡汇总
     * @param er
     * @param cardNumber
     * @return
     */
    public Map<String,Object> getFrByErAndCardNumber(FinaExpenseRecord er,String cardNumber,String name,Double total);


    /**
     * 获取需要更新的扣卡汇总，更改锁定
     * @param list
     * @return
     */
    public Map<String,Object> updateCardTotal(List<Record> list, FinaExpenseRecord er, FinaMembershipCard cardNew, MainCardDeductScheme schemeNew,Double total);


    /**
     * 更新的扣卡汇总，更改锁定
     * @param isNotCard
     * @param cardNew
     * @param frList
     * @return
     */
    public boolean updateCardTotalTwo(boolean isNotCard,FinaMembershipCard cardNew,List<FinaCardDeductRecord> frList);


    /**
     * 修改删除该账单其他卡的扣卡汇总并新增或更新新卡的扣卡汇总
     * @param cardMap
     * @param er
     * @param cardNew
     * @param schemeNew
     * @param total
     * @return
     */
    public Map<String,Object> updateCardTotalThree(Map<String,Record> cardMap,FinaExpenseRecord er,FinaMembershipCard cardNew,MainCardDeductScheme schemeNew,Double total);


    /**
     * 【新】变更会员卡：变更扣卡汇总
     * @param er
     * @param oldCardMap
     * @param newCardMap
     * @return
     */
    public Map<String,Object> changeCardTotal(FinaExpenseRecordDeductionCard firstFc,List<FinaExpenseRecordDeductionCard> fcList,FinaExpenseRecord er, Map<String,Record> oldCardMap, Map<String,Record> newCardMap, List<FinaExpenseRecordDetail> detailList);


    /**
     *
     * @param isNotCard 新卡是否存在旧卡中
     * @param cardNew 新卡
     * @param frList  被更新的扣卡汇总
     * @param delFrList 被删除的扣卡汇总
     * @return
     */
    public boolean updateCardTotalFour(boolean isNotCard, FinaMembershipCard cardNew, List<FinaMembershipCard> cardList,List<FinaCardDeductRecord> frList,List<FinaCardDeductRecord> delFrList);


    /**
     * 【新】变更会员卡：增删改 变更扣卡汇总
     * @param saveFrList
     * @param updateFrList
     * @param delFrList
     * @param cardList
     * @return
     */
    public boolean updateChangeCardTotal(FinaExpenseRecord er,FinaExpenseRecordDeductionCard firstFc,List<FinaExpenseRecordDeductionCard> fcList,List<FinaCardDeductRecord> saveFrList,List<FinaCardDeductRecord> updateFrList,
                                         List<FinaCardDeductRecord> delFrList,List<FinaMembershipCard> cardList,List<FinaExpenseRecordDetail> detailList);


    /**
     *根据主账单id集合获取扣卡汇总
     * @param str 问号
     * @param ids 主账单id集合
     * @return
     */
    public List<FinaCardDeductRecord> getFrsByExpenseIds(String str,List<String> ids);


    /**
     * 转移会员卡升级：变更旧卡主账单锁定
     * @param mainList
     * @param oldCard
     * @param newCard
     * @param newLockMap
     * @return
     */
    public Map<String,Object> changeCardTotal(List<FinaExpenseRecord> mainList,List<FinaExpenseRecord> bookAndTouristList,FinaMembershipCard oldCard,FinaMembershipCard newCard,MainCardDeductScheme newScheme,MainCardDeductScheme newSchemeLong,Map<String,Double> newLockMap,String newFullName,String userId);


    /**
     * 根据主账单id和会员卡获取扣卡汇总
     * @param expenseId
     * @param cardNumber
     * @return
     */
    public FinaCardDeductRecord getFrByExpenseIdAndCardNumber(String expenseId,String cardNumber);


    /**
     * 转移会员卡升级-重新统计实扣
     * @param mainList
     * @return
     */
    public boolean cardTransferCardTotal(List<FinaExpenseRecord> mainList);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardDeductRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardDeductRecord
     */
    public List<FinaCardDeductRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardDeductRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardDeductRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardDeductRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardDeductRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardDeductRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardDeductRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardDeductRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}