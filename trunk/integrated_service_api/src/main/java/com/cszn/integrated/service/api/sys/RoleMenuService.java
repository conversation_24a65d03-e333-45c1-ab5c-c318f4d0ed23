package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.sys.RoleMenu;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface RoleMenuService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public RoleMenu findById(Object id);


    /**
     * find all model
     *
     * @return all <RoleMenu
     */
    public List<RoleMenu> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(RoleMenu model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(RoleMenu model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(RoleMenu model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(RoleMenu model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}