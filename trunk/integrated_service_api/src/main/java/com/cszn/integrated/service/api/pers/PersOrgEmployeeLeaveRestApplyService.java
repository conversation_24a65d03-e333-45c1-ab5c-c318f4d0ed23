package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveRestApply;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PersOrgEmployeeLeaveRestApplyService {

    public List<PersOrgEmployeeLeaveRestApply> getLeaveRestApplyListByMap(Map<String,List<PersOrgEmployeeLeaveRestApply>> leaveRestApplyMap,String empId,Date date);

    public List<PersOrgEmployeeLeaveRestApply> getEmpLeaveRestApplyList(String empId, String dateStr);

    public List<PersOrgEmployeeLeaveRestApply> getEmpNewLeaveRestApplyList(String empId, String dateStr);

    public void findAllHalfLeaveRestApply();

    public List<PersOrgEmployeeLeaveRestApply> getEmpLeaveRestApply(String empId,Date startTime,Date endTime,String type);

    /**
     *
     * @param empId
     * @param checkinTime
     * @param type 上班打卡/下班打卡
     * @return
     */
    public Map<String,Object>  leaveRestCheckin(String empId, Date checkinTime,String type);

    public List<PersOrgEmployeeLeaveRestApply> findBySql(String sql,Object[] params);

    public PersOrgEmployeeLeaveRestApply getEmpLeaveRestApply(String empId, String dateStr);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeLeaveRestApply findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeType
     */
    public List<PersOrgEmployeeLeaveRestApply> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeLeaveRestApply model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeLeaveRestApply model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeLeaveRestApply model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeLeaveRestApply model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeLeaveRestApply> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeLeaveRestApply> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeLeaveRestApply> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
