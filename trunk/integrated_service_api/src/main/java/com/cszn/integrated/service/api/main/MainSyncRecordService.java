package com.cszn.integrated.service.api.main;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainSyncRecord;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface MainSyncRecordService  {

    public void saveBedStatusUpdateSyncRecord(String appNo,List<String> bedIdList);

    /**
     * 保存MainSyncRecordList
     * @param recordList
     * @return
     */
    public boolean saveSyncRecordList(List<MainSyncRecord> recordList);

    /**
     * 开始同步
     * @param syncType 数据类型
     * @param operationType 操作类型：全部或按照时间
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param userId 用户id
     * @param pageNumber 页码
     * @param pageSize 页量
     * @return
     */
    public boolean startSync(String syncType, String operationType, String startDate, String endDate,String userId,Integer pageNumber,Integer pageSize);


    /**
     * 作废所有同步记录
     * @param syncType 数据类型
     * @param userId 用户id
     * @param pageNumber 页码
     * @param pageSize 页量
     * @return
     */
    public boolean delAllSyncRecord(String syncType,String userId,Integer pageNumber,Integer pageSize);

    /**
     * 查询全部同步记录
     * @param pageNumber
     * @param pageSize
     * @param status
     * @return
     */
    public Page<MainSyncRecord> findAllSyncRecordPage(Integer pageNumber,Integer pageSize,String status);


    /**
     * 删除同步记录
     * @param id
     * @param userId
     * @return
     */
    public boolean delSyncRecord(String id,String userId);

    /**
     * 推送同步记录到其他系统
     * @param id
     * @return
     */
    public Map<String,Object> sendSyncRecord(String id);

    /**
     * 通过appNo获取同步记录
     * @param pageNumber
     * @param pageSize
     * @param syncRecord
     * @return
     */
    public Page<Record> findSyncRecordPageByAppNo(Integer pageNumber, Integer pageSize,MainSyncRecord syncRecord);


    /**
     * 修改同步记录list的状态为成功
     * @param recordList
     * @return
     */
    public boolean updateSynRecorlisStatus(List<MainSyncRecord> recordList);

    /**
     * 获取需要所有需要同步的数据
     * @return
     */
    public Page<MainSyncRecord> findSyncRecorList(Integer pageNumber,Integer pageSize,String appNo);

    /**
     *
     * @param syncType
     * @param dataType
     * @param data
     * @param userId
     * @return
     */
    public boolean saveSyncRecord(String syncType,String dataType,String data,String userId);



    /**
     * 根据应用号和同步类型查询同步记录
     * @param appNo
     * @param syncType
     * @return
     */
    public Page<MainSyncRecord> findSyncRecorList(Integer pageNumber,Integer pageSize,String appNo,String syncType);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainSyncRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <MainSyncRecord
     */
    public List<MainSyncRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainSyncRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainSyncRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainSyncRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainSyncRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainSyncRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainSyncRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainSyncRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}