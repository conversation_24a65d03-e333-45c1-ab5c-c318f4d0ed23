package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersSmsSend;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersSmsSendService {

    public Page<PersSmsSend> findPageList(int pageIndex,int pageSize,PersSmsSend persSmsSend);

    public boolean saveSmsSend(PersSmsSend smsSend,String userId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersSmsSend findById(Object id);


    /**
     * find all model
     *
     * @return all <PersSmsSend
     */
    public List<PersSmsSend> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersSmsSend model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersSmsSend model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersSmsSend model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersSmsSend model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersSmsSend> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersSmsSend> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersSmsSend> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
