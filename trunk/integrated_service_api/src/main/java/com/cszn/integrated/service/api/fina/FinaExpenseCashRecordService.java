package com.cszn.integrated.service.api.fina;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.fina.FinaExpenseCashRecord;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface FinaExpenseCashRecordService  {

    /**
     * 获取lsit
     * @param expenseId
     * @return
     */
    public List<FinaExpenseCashRecord> findCashRecordPageList(String expenseId);

    /**
     * 修改纸卡记录
     * @param record
     * @return
     */
    public boolean cashRecordSave(FinaExpenseCashRecord record,String userId);

    /**
     * 获取纸卡使用记录
     * @param pageNumber
     * @param pageSize
     * @param checkinNo
     * @param mainId
     * @return
     */
    public Page<FinaExpenseCashRecord> findCashRecordPage(Integer pageNumber,Integer pageSize,String checkinNo,String mainId);

    /**
     * 根据外部系统id获取对象
     * @param uniqueId
     * @return
     */
    public FinaExpenseCashRecord getObjByUniqueId(String uniqueId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseCashRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseCashRecord
     */
    public List<FinaExpenseCashRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseCashRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseCashRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseCashRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseCashRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseCashRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseCashRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseCashRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}