package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsCustomerInfo;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MmsCustomerInfoService {

    /**
     * 查询分页对象
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsCustomerInfo> customerPage(int pageNumber, int pageSize,MmsCustomerInfo model);

	/**
	 * 精确查询分页对象
	 * @param pageNumber
	 * @param pageSize
	 * @return
	 */
	public Page<MmsCustomerInfo> preciseQueryTable(int pageNumber, int pageSize,MmsCustomerInfo model);

    /**
     * 查询分页对象
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsCustomerInfo> pageTable(int pageNumber, int pageSize,MmsCustomerInfo model,String isAssignVisit, String isVisit, String startDate, String endDate, String callPass, String followResult, Integer memberAge);
    
    /**
     * 查询分页对象
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsCustomerInfo> myPageTable(int pageNumber, int pageSize,MmsCustomerInfo model);
    
    /**
     * 查询分页对象
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsCustomerInfo> salesPageTable(int pageNumber, int pageSize,MmsCustomerInfo model);
    
    /**
     * 新增统计对象
     * @param startDate
     * @param endDate
     * @return
     */
    public List<Record> newCountList(String startDate, String endDate);
	
    /**
     * 昨天新增统计对象
     * @param startDate
     * @param endDate
     * @return
     */
    public int yesterdayNewCount(String startDate, String endDate);
 
    /**
     * 根据身份证查询对象
     * @param idcard
     * @return
     */
    public MmsCustomerInfo getByIdcard(String idcard);

    /**
     * 根据手机号查询对象
     * @param phoneNumber
     * @return
     */
    public MmsCustomerInfo getByPhoneNumber(String phoneNumber);

    /**
     * 根据身份证、手机号查询对象
     * @param idcard
     * @param phoneNumber
     * @return
     */
    public MmsCustomerInfo getByIdcardAndPhone(String idcard, String phoneNumber);

    /**
     * 根据wxUserId查询对象
     * @param wxUserId
     * @return
     */
    public MmsCustomerInfo getByWxUserId(String wxUserId);
    
    public boolean assignSave(List<MmsCustomerInfo> list, String assignUserId, String userId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsCustomerInfo findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsConsultRegister
     */
    public List<MmsCustomerInfo> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsCustomerInfo model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsCustomerInfo model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Ret customerSave(MmsCustomerInfo model, String verifyCode);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsCustomerInfo model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsCustomerInfo model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
