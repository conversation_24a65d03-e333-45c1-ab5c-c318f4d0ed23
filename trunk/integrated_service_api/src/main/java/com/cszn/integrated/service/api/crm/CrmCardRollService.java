package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRoll;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollService  {

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRoll> paginate(CrmCardRoll model, int page, int pageSize);

    public void cardRollVoid(String formId);

    public void bmpInvokeGetForm(String formId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRoll findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRoll
     */
    public List<CrmCardRoll> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRoll model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRoll model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRoll model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRoll model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRoll> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRoll> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRoll> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}