package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsStockModels;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface WmsStockModelService {

    public List<WmsStockModels> findNullStockModelNoList();

    public List<WmsStockModels> findList();

    public List<WmsStockModels> findNullNewStockModelNoList();

    public List<Record> getStockListByTypeId(String typeId);
    
    /**
     * 生成条形码编号
     * @return
     */
    public String genBarcodeNumber();

    /**
     * 批量获取商品的价格、图片等信息
     * @param modelIds
     * @return
     */
    public Map<String,Record> findModelInfoByIds(List<String> modelIds);

    /**
     * 获取商品分页，如果typeId为空就查询所有的
     * @param pageNumber
     * @param pageSize
     * @param typeId
     * @return
     */
    public Page<Record> findModelPage(Integer pageNumber,Integer pageSize,String typeId);

    /**
     * 通过条形码获取商品
     * @param barCode
     * @param warehouseId
     * @return
     */
    public Record findModelByBarCode(String barCode,String warehouseId);


    /**
     * 添加货物
     * @param wmsStockModels
     * @return
     */
    public boolean saveStockModel(WmsStockModels wmsStockModels);

    /**
     * 获取分页
     * @param pageNumber
     * @param pageSize
     * @param wmsStockModels
     * @return
     */
    public Page<WmsStockModels> findStockModelPageList(Integer pageNumber, Integer pageSize, WmsStockModels wmsStockModels, String startDate, String endDate);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsStockModels findById(Object id);

    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsStockModels> findAll();
    
    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<WmsStockModels> findList(String typeId);


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsStockModels model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsStockModels model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsStockModels model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsStockModels model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsStockModels> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsStockModels> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsStockModels> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
