package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaSettleDetail;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FinaSettleDetailService {
	
	
	/**
	 * 批量结算列表
	 * @return
	 */
	public Page<Record> batchSettlePage(int pageNumber,int pageSize,String baseId, String yearMonth, String cardNumber, String settleType);

	
	/**
	 * 基地扣卡汇总表个人明细
	 * @return
	 */
	public List<Record> baseDeductCardSummaryDetailList(String baseId, String deductMonth, String cardTypeId);
	
	/**
	 * 基地扣卡汇总表卡类别明细
	 * @return
	 */
	public List<Record> baseDeductCardSummaryCardTypeList(String baseId, String deductMonth);
	
	/**
	 * 基地扣卡汇总表
	 * @return
	 */
	public List<Record> baseDeductCardSummaryList();
	
	/**
	 * 获取扣卡月报列表
	 * @param deductMonth
	 * @param baseId
	 * @return
	 */
	public List<Record> deductCardMonthReportList(String baseId, String deductMonth);

	/**
	 * 获取扣卡日报列表
	 * @param deductDate
	 * @return
	 */
	public List<Record> deductCardDailyReportList(String baseId, String deductDate);
	
    public List<FinaSettleDetail> findAllSettleDetailByCardNumber(String cardNumber);

	public FinaSettleDetail getSettleDetail(String expenseId);

	public FinaSettleDetail getSettleDetail(String expenseId,String cardNumber,String startTime);

    /**
     * 通过状态获取账单结算明细
     * @param expenseId
     * @param settleStatus
     * @return
     */
    public List<FinaSettleDetail> findEpenseSettleDetailByStatus(String expenseId,String settleStatus);


    /**
     * 测试方法
     * @param pageNumber
     * @param pageSize
     */
    public void monthSettleGen(int pageNumber,int pageSize);

    /**
     * 结算明细
     * @param detail
     * @param expenseRecord
     * @param settleType
     * @param actualTimes
     * @param actualAmount
     * @param settleRemark
     * @param userId
     * @return
     */
    public Map<String,Object> settleDateilRecord(FinaSettleDetail detail, FinaExpenseRecord expenseRecord, String settleType, Double actualTimes, Double actualAmount,Double actualPoints
            ,Double actualIntegrals, String settleRemark, String userId);
    
    /**
     * 结算明细
     * @param detail
     * @param expenseRecord
     * @param settleType
     * @param actualTimes
     * @param actualAmount
     * @param settleRemark
     * @param userId
     * @return
     */
    public Map<String,Object> settleDateilRecordNoSms(FinaSettleDetail detail, FinaExpenseRecord expenseRecord, String settleType, Double actualTimes, Double actualAmount,Double actualPoints
    		,Double actualIntegrals, String settleRemark, String userId);

	public void settleDetailSMSSend(FinaSettleDetail detail, FinaMembershipCard card, FinaExpenseRecord expenseRecord
			, String changeBedExpenseRecordId, String settleType, MainBase base, MainCardDeductScheme scheme, MainMembershipCardType cardType);

    /**
     * 退住生产退住明细
     * @param record
     * @param checkoutDate
     */
    public void checkoutGenSettleDetail(FinaExpenseRecord record, Date checkoutDate);

    /**
     * 获取明细list
     * @param expenseId
     * @return
     */
    public List<Record> findSettleDetailList(String expenseId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaSettleDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaNoticeCardChange
     */
    public List<FinaSettleDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaSettleDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaSettleDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaSettleDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaSettleDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaSettleDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaSettleDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaSettleDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
