package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsActivityDetail;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsActivityDetailService  {

    public MmsActivityDetail getMmsActivityDetailByUserId(String activityId,String userId);

    public MmsActivityDetail getMmsActivityDetailByPhone(String activityId,String phone);

    public boolean saveMmsActivityDetail(MmsActivityDetail detail);

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsActivityDetail> paginateByCondition(MmsActivityDetail model, int pageNumber, int pageSize);
    
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsActivityDetail> paginateByCondition(String userId, int pageNumber, int pageSize);


    /**
     * 通过活动ID查询名单列表
     * @param activityId
     * @return
     */
    public List<MmsActivityDetail> findListByActivityId(String activityId);
    

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsActivityDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsActivityQrcode
     */
    public List<MmsActivityDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsActivityDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsActivityDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsActivityDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsActivityDetail model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);


}