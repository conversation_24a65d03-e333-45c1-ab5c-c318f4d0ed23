package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaExpenseRecordRenew;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaExpenseRecordRenewService {


    /**
     * 保存续订关系记录
     * @param recordRenew
     * @return
     */
    public boolean saveFinaExpenseRecordRenew(FinaExpenseRecordRenew recordRenew);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordRenew findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordFollow
     */
    public List<FinaExpenseRecordRenew> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordRenew model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordRenew model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordRenew model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordRenew model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordRenew> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordRenew> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordRenew> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
