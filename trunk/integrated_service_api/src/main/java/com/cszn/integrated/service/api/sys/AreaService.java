package com.cszn.integrated.service.api.sys;

import java.util.List;
import java.util.Map;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.sys.Area;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface AreaService  {

	/**
	 * 获取分页信息
	 * @param params
	 * @return
	 */
	public Page<Area> page(final Map<String, String[]> params);

	/**
	 * 获取省、市、县方法
	 * @param pid
	 * @return
	 */
	public List<Area> getArea(final String pid);

	/**
	 * 添加区域
	 * @param area
	 * @return
	 */
	public boolean addArea(Area area,String userId);


	/**
	 * 修改区域
	 */
	public boolean editArea(Area area,String userId);


	/**
	 * 删除区域
	 * @param id
	 * @return
	 */
	public boolean delArea(final String id,String userId);

	/**
	 * 批量删除区域
	 * @param jsonArrayStr
	 * @param userId
	 * @return
	 */
	public boolean batchDel(String jsonArrayStr,String userId);

	/*-----------------------------------------------------------------*/

	/**
	 * 查询区域分页表格
	 *
	 * @return
	 */
	public Page<Area> paginateByCondition(Area area, int pageNumber, int pageSize);

	/**
	 * 查询区域表单树
	 *
	 * @return
	 */
	public List<ZTree> areaFormTree();

	/**
	 * 排序批量保存方法
	 *
	 * @param areaList
	 * @return
	 */
	public boolean sortBatchSave(List<Area> areaList);

	/**
	 * 区域保存方法
	 *
	 * @param area
	 * @return
	 */
	public boolean areaSave(Area area);

	/**
	 * 区域删除方法
	 *
	 * @param areaId
	 * @return
	 */
	public boolean areaDel(final String areaId);

	/**
	 * 根据区域类型和区域ID查找区域列表
	 *
	 * @param areaType
	 * @param areaId
	 * @return
	 */
	public List<Area> getAreaByParentId(String parentId);


	/**
	 * 获取地区：插件使用
	 */
	public List<Area> getAreaByPid(String pid);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public Area findById(Object id);


    /**
     * find all model
     *
     * @return all <Area
     */
    public List<Area> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(Area model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(Area model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(Area model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(Area model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}