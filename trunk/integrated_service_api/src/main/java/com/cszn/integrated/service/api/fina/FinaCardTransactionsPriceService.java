package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardTransactionsPrice;
import com.cszn.integrated.service.entity.food.FoodInfo;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaCardTransactionsPriceService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardTransactionsPrice findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FinaCardTransactionsPrice
     */
    public List<FinaCardTransactionsPrice> findList(String transactionsId);


    /**
     * find all model
     *
     * @return all <FoodInfo
     */
    public List<FinaCardTransactionsPrice> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardTransactionsPrice model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardTransactionsPrice model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardTransactionsPrice model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardTransactionsPrice model);

    
    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardTransactionsPrice> paginate(int page, int pageSize);
    
    
    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardTransactionsPrice> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardTransactionsPrice> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}