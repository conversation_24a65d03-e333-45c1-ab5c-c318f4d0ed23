package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBasePackageFood;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;

public interface MainBasePackageFoodService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public List<MainBasePackageFood> findListByPackageId(String packageId);
    
	
	/**
	 * find all model
	 *
	 * @return all <Record
	 */
	public List<Record> findPackageFoodList(String packageId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBasePackageFood findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainBasePackageFood
     */
    public List<MainBasePackageFood> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBasePackageFood model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBasePackageFood model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBasePackageFood model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBasePackageFood model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBasePackageFood> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBasePackageFood> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBasePackageFood> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}