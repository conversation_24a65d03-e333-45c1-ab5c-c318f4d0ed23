package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseMealTimeSetting;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MainBaseMealTimeSettingService {

    /**
     * 通过开始时间或者结束时间判断基地用餐时间是否存在
     * @param startTime
     * @param endTime
     * @return
     */
    public MainBaseMealTimeSetting baseMealTimeIsExist(String startTime,String endTime);
    
    /**
     * 获取基地用餐时间列表
     * @return
     */
    public List<MainBaseMealTimeSetting> findMealTimeList(String baseId);

    /**
     * 删除
     * @param id
     * @param userId
     * @return
     */
    public boolean delete(String id, String userId);

    /**
     * 保存
     * @param obj
     * @param userId
     * @return
     */
    public boolean save(MainBaseMealTimeSetting obj, String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseMealTimeSetting findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MainBaseMealTimeSetting> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseMealTimeSetting model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainBaseMealTimeSetting model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainBaseMealTimeSetting model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseMealTimeSetting model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}