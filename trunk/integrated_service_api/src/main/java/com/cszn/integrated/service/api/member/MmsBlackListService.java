package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsBlackList;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MmsBlackListService {
	
    /**
     * 查询分页对象
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<MmsBlackList> pageTable(int pageNumber, int pageSize,MmsBlackList model);
    
    public MmsBlackList getByIdCard(String idCard);
    
    public MmsBlackList getByCondition(String fullName, String idCard, String mobile);

    public boolean saveBlackList(MmsBlackList blackList);

    public MmsBlackList getByFullName(String fullName);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsBlackList findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsBlackList
     */
    public List<MmsBlackList> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsBlackList model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsBlackList model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsBlackList model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsBlackList model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
