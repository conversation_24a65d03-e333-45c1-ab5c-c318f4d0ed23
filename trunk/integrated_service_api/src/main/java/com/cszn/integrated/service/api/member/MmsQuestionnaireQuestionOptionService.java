package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestionOption;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsQuestionnaireQuestionOptionService {


    public boolean saveOption(MmsQuestionnaireQuestionOption option,String userId);

    public List<MmsQuestionnaireQuestionOption> findOptionListByQuestionIds(List<String> ids);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsQuestionnaireQuestionOption findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsPrize
     */
    public List<MmsQuestionnaireQuestionOption> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsQuestionnaireQuestionOption model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsQuestionnaireQuestionOption model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsQuestionnaireQuestionOption model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsQuestionnaireQuestionOption model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsQuestionnaireQuestionOption> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsQuestionnaireQuestionOption> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsQuestionnaireQuestionOption> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
