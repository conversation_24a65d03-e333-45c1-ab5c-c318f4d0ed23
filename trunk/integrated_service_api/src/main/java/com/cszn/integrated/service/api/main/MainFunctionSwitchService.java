package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainFunctionSwitch;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainFunctionSwitchService {


    /**
     * 分页
     * @param pageNumber
     * @param pageSize
     * @param fs
     * @return
     */
    public Page<Record> findPage(Integer pageNumber, Integer pageSize, MainFunctionSwitch fs);


    /**
     * 删除功能配置开关
     * @param id
     * @param userId
     */
    public boolean delSwitch(String id,String userId);


    /**
     * 保存功能配置
     * @param fs
     * @param userId
     * @return
     */
    public boolean saveSwitch(MainFunctionSwitch fs,String userId);


    /**
     * 去重
     * @param fs
     * @return
     */
    public boolean getDistinct(String function);



    /**
     * 根据功能获取对应开关
     * @param function
     * @return
     */
    public String getSwitch(String function);


    /**
     * 获取功能配置对象
     * @param id
     * @return
     */
    public MainFunctionSwitch get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainFunctionSwitch findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaFunctionSwitch
     */
    public List<MainFunctionSwitch> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainFunctionSwitch model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainFunctionSwitch model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainFunctionSwitch model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainFunctionSwitch model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainFunctionSwitch> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainFunctionSwitch> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainFunctionSwitch> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}