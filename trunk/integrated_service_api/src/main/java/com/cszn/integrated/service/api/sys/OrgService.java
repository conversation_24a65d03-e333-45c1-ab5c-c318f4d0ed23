package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.sys.Org;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface OrgService  {

	/**
     * 查询机构树表格
     * @return
     */
    public List<ZTree> orgTreeTable(final String orgId);
    
    /**
     * 机构编辑树数据
     * @return
     */
    public List<ZTree> orgFormTree(final String orgId);
    
    /**
     * 根据机构类型获取机构列表
     * @return
     */
    public List<Org> getOrgListByType(final String parentId, final String orgType);
    
    /**
     * 机构批量排序保存
     * 
     * @param orgList
     * @return
     */
    public boolean sortBatchSave(List<Org> orgList);
    
    /**
     * 机构保存方法(有特殊处理)
     *
     * @param model
     * @return if save or update success
     */
    public boolean orgSave(Org model);
    
    /**
     * 机构删除方法(需要删子机构)
     *
     * @param model
     * @return if save or update success
     */
    public boolean orgDel(final String orgId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public Org findById(Object id);


    /**
     * find all model
     *
     * @return all <Org
     */
    public List<Org> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(Org model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(Org model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(Org model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(Org model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}