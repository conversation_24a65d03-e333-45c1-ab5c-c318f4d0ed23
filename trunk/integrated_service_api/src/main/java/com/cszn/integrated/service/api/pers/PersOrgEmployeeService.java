package com.cszn.integrated.service.api.pers;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.pers.PersEmpUser;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeRel;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeService  {

    public boolean updateEmpChat(PersOrgEmployee persOrgEmployee,String type);

    public Page<PersOrgEmployee> getEmpPage(int page,int limit,PersOrgEmployee employee);

    public List<PersOrgEmployee> getEmployeeListByIds(List<String> empIds);

    public PersOrgEmployee getEmployeeByWorkNumName(String workNum,String name);

    public PersOrgEmployee getEmployeeByWorkNum(String workNum);

    public List<PersOrgEmployee> findEmpListByDepts(List<String> deptIds);

    public boolean updateQiyeUserId(String empId);

    public List<String> getUserQiYeIdByUserId(JSONArray jsonArray);

    public List<String> getUserQiYeIdByWorkNum(JSONArray jsonArray);

    public Page<PersOrgEmployee> birthdayPaginateByCondition(PersOrgEmployee orgEmployee,String userId,String start,String end, int pageNumber, int pageSize);

    public Record getByEmpId(final String empId);

    public Record getByUserId(String userId);
    
    public PersOrgEmployee getByEmpIdcard(final String idcard);

    /**
     * empUserList
     *
     * @return all <Record
     */
    public List<Record> empUserList(String depId);

	/**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<Record> empUserPage(PersOrgEmployee orgEmployee,String userId,int pageNumber,int pageSize);

    /**
     * 通过登录账号获取员工档案
     * @param accountId
     * @return
     */
    public PersOrgEmployee getEmployeeByAccountId(String accountId);

    /**
     * 判断是否存在
     * @param id
     * @param idCard
     * @param phoneNum
     * @return
     */
    public boolean employeeContractExist(String id,String employeeName,String idCard,String phoneNum,String archiveStatus);

    /**
     * 通过员工id获取企业微信id
     * @param empId
     * @return
     */
    public int[] findOrgQiYeId(String empId);

    /**
     * 员工生日分页
     * @param pageNumber
     * @param pageSize
     * @param month
     * @return
     */
    public Page<PersOrgEmployee> findBirthdayEmployeePage(Integer pageNumber,Integer pageSize,String month);

    public void createQiYeUser();

	/**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<PersOrgEmployee> paginateByCondition(PersOrgEmployee orgEmployee,String userId,String sort,String isContainChildren,String workAgeType
            ,String workAgeYearMonth,String formalStartDate,String formalEndDate,String isFilterBranchOffice,String rangeType,String rangeValue, int pageNumber, int pageSize);
    
    public Record getEmpUser(final String empUserId);
    
    /**
     * 保存方法
     * 
     * @param orgEmployee
     * @return
     */
    public boolean saveOrgEmployee(final PersOrgEmployee orgEmployee, List<PersOrgEmployeeRel> addRelList,List<PersOrgEmployeeRel> updateRelList);
    
    /**
     * 保存方法
     * 
     * @param persEmpUser
     * @return
     */
    public Ret saveEmpUser(final PersEmpUser persEmpUser);
    
    
    /**
     * 删除单个员工方法
     * 
     * @param orgEmployee
     * @return
     */
    public Ret del(final PersOrgEmployee orgEmployee);
    
    /**
     * 批量删除方法
     * 
     * @param empList
     * @return
     */
    public boolean batchDel(final List<PersOrgEmployee> empList);
    
    public boolean delEmpUser(PersEmpUser persEmpUser);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployee findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployee
     */
    public List<PersOrgEmployee> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployee model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployee model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployee model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployee model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployee> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployee> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployee> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}