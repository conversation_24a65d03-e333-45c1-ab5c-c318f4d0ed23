package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaMembershipCardTypeConf;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaMembershipCardTypeConfService  {
	
	public FinaMembershipCardTypeConf getByCardTypeId(final String cardTypeId);
	
	public Ret saveConf(FinaMembershipCardTypeConf model);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMembershipCardTypeConf findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaMembershipCardTypeConf
     */
    public List<FinaMembershipCardTypeConf> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMembershipCardTypeConf model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaMembershipCardTypeConf model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaMembershipCardTypeConf model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMembershipCardTypeConf model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaMembershipCardTypeConf> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaMembershipCardTypeConf> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaMembershipCardTypeConf> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}