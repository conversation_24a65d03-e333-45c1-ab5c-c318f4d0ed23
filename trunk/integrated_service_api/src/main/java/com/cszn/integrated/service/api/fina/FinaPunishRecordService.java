package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaPunishRecord;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FinaPunishRecordService  {

    public FinaPunishRecord findRecord(String appNo, String baseId,String bookNo,String checkinNo,String punishType);

    public FinaPunishRecord setParams(String appNo, String baseId, String officeId, String bookNo,
                                      String checkinNo, String punishType, String cardNumber,Date bookCreateTime,Date bookCancelTime,Date planCheckinTime,
                                      Date actualCheckinTime,Date planCheckoutTime,Date actualCheckoutTime,String name,String remark);


    /**
     * 处理取消预订处罚数据
     * @param pr
     * @return
     */
    public Map<String,Object> dealCancelBookPunish(FinaPunishRecord pr);


    /**
     * 处理推迟入住处罚数据
     * @param pr
     * @return
     */
    public Map<String,Object> dealDelayCheckin(FinaPunishRecord pr);


    /**
     * 处理提前退住处罚数据
     * @param pr
     * @return
     */
    public Map<String,Object> dealAdvanceCheckout(FinaPunishRecord pr);


    /**
     * 判断账单是否已有该处罚
     * @param appNo
     * @param baseId
     * @param bookNo
     * @param checkinNo
     * @param punishType
     * @return
     */
    public Long isExist(String appNo,String baseId,String bookNo,String checkinNo,String punishType);


    /**
     *违约结算管理
     * @param pr
     * @param fullName
     * @return
     */
    public Page<Record> findPage(Integer pageNumber,Integer pageSize,FinaPunishRecord pr,String fullName);



    /**
     * 作废违约数据记录
     * @param id
     * @param userId
     * @return
     */
    public boolean delPunish(String id,String userId);


    /**
     * 违约结算
     * @param pr
     * @param userId
     * @return
     */
    public boolean punishSettle(FinaPunishRecord pr, FinaMembershipCard card, MainCardDeductScheme scheme,String userId);


    /**
     * 根据会员卡获取违约账单
     * @param cardNumber
     * @return
     */
    public List<FinaPunishRecord> getPunishRecords(String cardNumber);


    /**
     * 获取违约对象
     * @param id
     * @return
     */
    public FinaPunishRecord get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaPunishRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaPunishRecord
     */
    public List<FinaPunishRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaPunishRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaPunishRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaPunishRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaPunishRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaPunishRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaPunishRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaPunishRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}