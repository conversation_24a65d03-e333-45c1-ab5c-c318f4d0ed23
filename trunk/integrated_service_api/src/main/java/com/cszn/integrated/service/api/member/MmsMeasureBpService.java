package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsMeasureBp;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface MmsMeasureBpService  {

    /**
     * 小程序获取会员血压数据
     * @param cardNumber
     * @param type
     * @return
     */
    public Map<String, List> memberBp(String cardNumber, String type);

    /**
     * 获取会员最新的一条数据
     * @param cardNumber
     * @return
     */
    public Map<String,Object> latestBp(String cardNumber);

    /**
     * 获取血压测量值
     * @param memberId
     * @param dataRecord
     * @return
     */
    public Map<String,Object> getbpData(String memberId,String dataRecord);


    /**
     * 保存血压数据
     * @param equipmentNo
     * @param measureSbpValue
     * @param measureDbpValue
     * @param userId
     * @return
     */
    public boolean savebpData(String equipmentNo,String measureSbpValue,String measureDbpValue,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsMeasureBp findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMeasureBp
     */
    public List<MmsMeasureBp> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsMeasureBp model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsMeasureBp model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsMeasureBp model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsMeasureBp model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsMeasureBp> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsMeasureBp> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsMeasureBp> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}