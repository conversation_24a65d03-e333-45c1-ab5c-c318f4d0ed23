package com.cszn.integrated.service.api.pers;

import java.util.List;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeAccount;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface PersOrgEmployeeAccountService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<PersOrgEmployeeAccount> paginateByCondition(PersOrgEmployeeAccount model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeAccount findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <PersOrgEmployeeAccount
     */
    public List<PersOrgEmployeeAccount> findList(String empId);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeAccount
     */
    public List<PersOrgEmployeeAccount> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeAccount model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeAccount model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeAccount model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeAccount model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeAccount> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeAccount> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeAccount> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}