package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseFood;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;

public interface MainBaseFoodService  {
	
	
    /**
     * 基地菜分页
     * @param pageNumber
     * @param pageSize
     * @param baseEquipment
     * @return
     */
    public Page<Record> findFoodPage(int pageNumber, int pageSize, String baseId, String tagId, String isEnabled, String isShelf, String foodName, String queryType);
    
	
	/**
	 * find all model
	 *
	 * @return all <MainBaseFood
	 */
	public List<Record> findFoodList(String baseId, String tagId, String isEnabled, String isShelf, String foodName, String queryType);
	

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseFood findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseFood
     */
    public List<MainBaseFood> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseFood model);
    
    
    /**
     * 保存方法
     * 
     * @param model
     * @return
     */
    public boolean saveBaseFood(List<MainBaseFood> baseFoodList, String userId);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseFood model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseFood model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseFood model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseFood> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseFood> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseFood> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}