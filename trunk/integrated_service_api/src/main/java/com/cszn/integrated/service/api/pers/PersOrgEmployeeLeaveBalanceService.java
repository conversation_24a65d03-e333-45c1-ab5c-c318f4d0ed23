package com.cszn.integrated.service.api.pers;


import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveBalance;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveRestApply;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveRestApplyDate;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeOverTimeApply;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface PersOrgEmployeeLeaveBalanceService {

    public PersOrgEmployeeLeaveBalance findById(Object id);

    public Page<Record> findEmployeeLeaveBalancePage(int pageIndex,int pageSize,String deptId,String fullName,String archiveStatus,String userId);

    public List<Record> getEmpLeaveBalanceList(String empId);

    public Record getEmpLeaveBalanceRecord(String id);

    public boolean saveLeaveBalance(String empId, String leaveKey, int day, double hour, int minute, String remark, Date detailDate, String userId);

    public Page<Record> detailPageList(int page,int limit,String empId,String leaveType);

    public boolean deductLeaveBalance(PersOrgEmployeeLeaveRestApply leaveRestApply);

    public boolean addLeaveBalance(PersOrgEmployeeOverTimeApply overTimeApply);

    public boolean cancelDeductLeaveBalance(PersOrgEmployeeLeaveRestApply leaveRestApply);

    public boolean cancelAddLeaveBalance(PersOrgEmployeeOverTimeApply overTimeApply);

    public Map<String,Object> verifyLeaveBalance(String empId,PersOrgEmployeeLeaveRestApply restApply, List<PersOrgEmployeeLeaveRestApplyDate> leaveRestApplyDateList);

    public boolean addSubLeaveBalance(String empId,String leaveType,double deductDayValue,double deductHourValue,double deductMinuteValue,String type,String remark, Date detailDate,String userId);

    public boolean resetEmpYearLeave(String empId,double yearLeaveDay);

    public PersOrgEmployeeLeaveBalance findEmpLeaveBalanceByType(String empId,String leaveType);

    public boolean deductLeaveBalance2(PersOrgEmployeeLeaveRestApply leaveRestApply);
}
