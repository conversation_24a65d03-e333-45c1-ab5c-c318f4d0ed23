package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCostType;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainCostTypeService {

    public Page<MainCostType> pageList(int pageNumber,int pageSize,MainCostType costType);

    public boolean saveCostType(MainCostType costType,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCostType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomType
     */
    public List<MainCostType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCostType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCostType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCostType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCostType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCostType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCostType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCostType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
