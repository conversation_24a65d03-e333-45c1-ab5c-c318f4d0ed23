package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsSms;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface MmsSmsService {


    public boolean saveSms(MmsSms sms,String userId);

    /**
     *
     * @param pageNumber
     * @param pageSize
     * @param typeId
     * @return
     */
    public Page<Record> findPageList(Integer pageNumber,Integer pageSize,String typeId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsSms findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsSms
     */
    public List<MmsSms> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsSms model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsSms model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsSms model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsSms model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
