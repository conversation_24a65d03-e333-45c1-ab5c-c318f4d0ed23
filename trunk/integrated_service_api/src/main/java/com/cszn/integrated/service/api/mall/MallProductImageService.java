package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallProduct;
import com.cszn.integrated.service.entity.mall.MallProductImage;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MallProductImageService {

    public List<MallProductImage> findAllImageByProductId(String productId);


    public boolean saveProductImage(MallProductImage image,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallProductImage findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallProductImage> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallProductImage model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallProductImage model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallProductImage model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallProductImage model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);

}
