package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainTouristRouteDeductConfig;

import java.util.List;

public interface MainTouristRouteDeductConfigService{

    public MainTouristRouteDeductConfig findById(Object id);

    public boolean saveRouteDeductConfig(MainTouristRouteDeductConfig routeDeductConfig,String userId);

    public List<MainTouristRouteDeductConfig> findRouteDeductConfigList(MainTouristRouteDeductConfig routeDeductConfig);

    public MainTouristRouteDeductConfig findRouteDeductConfigByCheckinDate(String checkinDate,String routeId);
}
