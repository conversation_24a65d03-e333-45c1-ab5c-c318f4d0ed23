package com.cszn.integrated.service.api.member;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsFaceRecognitionRecord;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsFaceRecognitionRecordService  {


    /**
     * 分页条件查询
     * @param pageNumber
     * @param pageSize
     * @param fullName
     * @param idcard
     * @return
     */
    public Page<Record> findList(Integer pageNumber, Integer pageSize, String fullName, String idcard);


    /**
     * 查询对象
      * @param id
     * @return
     */
    public MmsFaceRecognitionRecord get(String id);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsFaceRecognitionRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsFaceRecognitionRecord
     */
    public List<MmsFaceRecognitionRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsFaceRecognitionRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsFaceRecognitionRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsFaceRecognitionRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsFaceRecognitionRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsFaceRecognitionRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsFaceRecognitionRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsFaceRecognitionRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}