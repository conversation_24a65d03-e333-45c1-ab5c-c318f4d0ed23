package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaCardDeduct;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaCardDeductService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<FinaCardDeduct> paginateByCondition(FinaCardDeduct model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardDeduct findById(Object id);
    
    
    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Ret saveDeductCard(FinaCardDeduct model, FinaMembershipCard card, String userId);
    
    
    /**
     * find all model
     *
     * @return all <FinaCardDeduct
     */
    public List<FinaCardDeduct> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardDeduct model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardDeduct model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardDeduct model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardDeduct model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardDeduct> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardDeduct> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardDeduct> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}