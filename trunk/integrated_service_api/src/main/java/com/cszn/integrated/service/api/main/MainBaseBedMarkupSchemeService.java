package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseBedMarkupScheme;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainBaseBedMarkupSchemeService {

    public MainBaseBedMarkupScheme findById(Object id);

    Page<MainBaseBedMarkupScheme> pageList(int page,int limit,MainBaseBedMarkupScheme scheme);

    boolean save(MainBaseBedMarkupScheme markupScheme,String userId);

    List<MainBaseBedMarkupScheme> findAllList();
}
