package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.main.MainBedType;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsGatewayService {

    /**
     * 设备分页
     * @param pageNumber
     * @param pageSize
     * @param gateway
     * @return
     */
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize, MmsGateway gateway);


    /**
     * 根据id获取设备对象
     * @param id
     * @return
     */
    public MmsGateway get(String id);


    /**
     * 保存设备
     * @param gateway
     * @param userId
     * @return
     */
    public String saveGateway(MmsGateway gateway,String userId);



    /**
     * 删除设备
     * @param id
     * @param userId
     * @return
     */
    public boolean delGateway(String id,String userId);


    /**
     * 保存：去重
     * @param gateway
     * @return
     */
    public String getDistinct(MmsGateway gateway);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsGateway findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsGateway
     */
    public List<MmsGateway> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsGateway model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsGateway model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsGateway model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsGateway model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsGateway> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsGateway> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsGateway> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}