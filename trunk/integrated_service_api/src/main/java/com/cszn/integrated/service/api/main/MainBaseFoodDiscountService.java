package com.cszn.integrated.service.api.main;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBaseFoodDiscount;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseFoodDiscountService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseFoodDiscount findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseFoodDiscount
     */
    public List<MainBaseFoodDiscount> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseFoodDiscount model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseFoodDiscount model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseFoodDiscount model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseFoodDiscount model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseFoodDiscount> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseFoodDiscount> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseFoodDiscount> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}