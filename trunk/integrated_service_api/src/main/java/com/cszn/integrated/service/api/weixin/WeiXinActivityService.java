package com.cszn.integrated.service.api.weixin;

import java.io.File;
import java.util.Map;

public interface WeiXinActivityService {

    String getAccessToken();

    Map<String,Object> getQrcodeTicket(String strParam, int expireSeconds, String actionName) throws Exception;

    File getQrcodeImgFile(String ticket);

    Map<String,Object> sendRandomCode(String phone);

    boolean checkCode(String phone,String randomCode);
}
