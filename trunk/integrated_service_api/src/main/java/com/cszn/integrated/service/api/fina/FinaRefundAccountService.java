package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRefundAccount;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaRefundAccountService  {


    /**
     * find all model
     *
     * @return all <FinaRefundAccount
     */
    public List<FinaRefundAccount> findByApplyIdList(String applyId);
    
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaRefundAccount findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaRefundAccount
     */
    public List<FinaRefundAccount> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaRefundAccount model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaRefundAccount model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaRefundAccount model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaRefundAccount model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaRefundAccount> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaRefundAccount> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaRefundAccount> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}