package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardMonthBalance;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardMonthBalanceService{
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardMonthBalance findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardMonthBalance
     */
    public List<FinaCardMonthBalance> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardMonthBalance model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardMonthBalance model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardMonthBalance model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardMonthBalance model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardMonthBalance> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardMonthBalance> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardMonthBalance> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
