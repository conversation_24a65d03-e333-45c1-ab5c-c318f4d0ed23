package com.cszn.integrated.service.api.fina;


import com.cszn.integrated.service.entity.fina.FinaExpenseRecordTouristSettleDetail;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface FinaExpenseRecordTouristSettleDetailService {

    public FinaExpenseRecordTouristSettleDetail findById(Object id);

    public List<Record> findListByExpenseId(String expenseId,String isSettle);

    public boolean saveTouristSettleDetail(FinaExpenseRecordTouristSettleDetail touristSettleDetail,String userId);

    public List<FinaExpenseRecordTouristSettleDetail> findTouristSettleDetailList(String expenseId);
}
