package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaRechargeList;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FinaRechargeListService  {
	
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<FinaRechargeList> paginateByCondition(FinaRechargeList model, int pageNumber, int pageSize);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaRechargeList findById(Object id);
    
    /**
     * find all model
     *
     * @return all <FinaLotteryRecharge
     */
    public List<FinaRechargeList> findList(String recordId);


    /**
     * find all model
     *
     * @return all <FinaLotteryRecharge
     */
    public List<FinaRechargeList> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaRechargeList model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaRechargeList model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaRechargeList model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaRechargeList model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaRechargeList> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaRechargeList> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaRechargeList> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}