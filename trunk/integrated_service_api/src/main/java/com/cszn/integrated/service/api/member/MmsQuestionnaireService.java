package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsQuestionnaire;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MmsQuestionnaireService {


    public Page<MmsQuestionnaire> pageList(int pageNumber,int pageSize,MmsQuestionnaire questionnaire);

    public boolean saveQuestionnaire(MmsQuestionnaire questionnaire,String userId);

    public MmsQuestionnaire findQuestionnaire(String id);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsQuestionnaire findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsPrize
     */
    public List<MmsQuestionnaire> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsQuestionnaire model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsQuestionnaire model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsQuestionnaire model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsQuestionnaire model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsQuestionnaire> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsQuestionnaire> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsQuestionnaire> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
