package com.cszn.integrated.service.api.fina;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


public interface FinaMembershipCardService {

    public FinaMembershipCard getByNumber(String cardNumber);

    /**
     * 导入福利积分卡并生成档案
     * @param file
     * @param fileName
     * @param userId
     * @return
     * @throws FileNotFoundException
     * @throws ParseException
     */
    public Ret importCardList(File file, String fileName, String userId) throws Exception;

	/**
     * 导入世纪昌松卡并生成档案、充值记录、消费记录
     * @param file
     * @param fileName
     * @param userId
     * @return
     * @throws FileNotFoundException
     * @throws ParseException
     */
    public Ret importShiJiCardList(File file, String fileName, String userId) throws Exception;

    public Map<String,Double> getRecordCardLockInfoByDate(String cardNumber,List<String> expenseIds,Date startDate);

    /**
     * 获取积分会员卡分页
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<Record> findIntegralsPage(Integer pageNumber,Integer pageSize);

    /**
     * 获取该订单中所有的会员卡锁定情况
     * @param mainRecordId
     * @return
     */
    public Map<String,Map<String,Double>> getRecordAllCardLockInfo(String mainRecordId);

    /**
     * 获取积分卡
     * @param cardNumber
     * @return
     */
    public Record getIntegralCardByNumber(String cardNumber);

	/**
	 * 消费卡列表查询
	 * @param cardNumber
	 * @return
	 */
	public List<Record> findExpendList(String cardNumber);


    public Map<String,Double> getRecordCardLockInfo(String cardNumber,List<String> expenseIds);

    /**
     * 消费卡分页查询
     * @param pageNumber
     * @param pageSize
     * @param card
     * @return
     */
    public Page<Record> findExpendPage(Integer pageNumber,Integer pageSize,FinaMembershipCard card);
	
	/**
	 * 查询和会员卡类型关联列表
	 * @param array
	 * @return
	 */
	public List<Record> findListWithCardType();

    /**
     * 批量获取会员卡余额、天数
     * @param array
     * @return
     */
    public List<FinaMembershipCard> getCardBalance(JSONArray array);

    /**
     * 获取主记录会员卡的锁定天数、余额
     * @param cardNumber
     * @param recordId
     * @return
     */
    public Map<String,Double> getRecordCardLockInfo(String cardNumber,String recordId);

    /**
     * 获取会员卡总共的锁定天数和锁定余额
     * @param cardNumber
     * @return
     */
    public Map<String,Double> getCardLockInfo(String cardNumber);

    /**
     * 判断会员卡是否被锁定
     * @param cardNumber
     * @return
     */
    public boolean cardIsLock(String cardNumber);

    /**
     * 通过会员卡号set获取会员卡List
     * @param cardNumberSet
     * @return
     */
    public List<FinaMembershipCard> findCardMapByCardNumbers(Set<String> cardNumberSet);
    
    /**
     * 通过会员ID获取会员卡List
     * @param memberId
     * @return
     */
    public List<FinaMembershipCard> findCardListByMemberId(String memberId);
    
    /**
     * 通过parentId获取会员卡List
     * @param memberId
     * @return
     */
    public List<FinaMembershipCard> findCardListByParentId(String parentId);

    /**
     * 通过会员卡号,身份证号,会员会员姓名模糊查找会员卡
     * @param pageNumber
     * @param pageSize
     * @param nameOrIdcardOrCardNumber
     * @return
     */
    public Page<Record> findCardPageList(Integer pageNumber,Integer pageSize,String nameOrIdcardOrCardNumber,String actionType);

    /**
     * 通过会员卡号查询会员卡信息(模糊查询)
     * @param cardNumber
     * @return
     */
    public List<Record> findCardInfo(String cardNumber);

    /**
     * 修改过期的会员卡
     * @param card
     * @return
     */
    public boolean expiredCardSave(FinaMembershipCard card);

    /**
     * 获取过期的会员卡
     * @param pageNumber
     * @param pageSize
     * @param nameOrIdcardOrCardNumber
     * @return
     */
    public Page<Record> expiredCardPageList(Integer pageNumber, Integer pageSize, String nameOrIdcardOrCardNumber);

    /**
     * 获取周期为季需要生成赠送的会员卡
     * @param pageNumber
     * @param pageSize
     * @param monthDaySet
     * @param cycle
     * @return
     */
    public Page<FinaMembershipCard> findSeasonGiveCardPage(Integer pageNumber, Integer pageSize, List<String> monthDaySet,Date computingDate, String cycle);

    /**
     * 获取周期为年需要生成赠送的会员卡
     * @param pageNumber
     * @param pageSize
     * @param monthDayList
     * @param cycle
     * @return
     */
    public Page<FinaMembershipCard> findYearGiveCardPage(Integer pageNumber, Integer pageSize, List<String> monthDayList,Date lastYear,String cycle);


    /**
     * 获取周期为日需要生成赠送的会员卡
     * @param pageNumber
     * @param pageSize
     * @param date
     * @return
     */
    public Page<FinaMembershipCard> findDayGiveCardPage(Integer pageNumber, Integer pageSize, Date date,String cycle);

    /**
     * 获取周期为月需要生成赠送的会员卡
     * @param pageNumber
     * @param pageSize
     * @param days
     * @return
     */
    public Page<FinaMembershipCard> findMonthGiveCardPage(Integer pageNumber,Integer pageSize,List<String> days,Date lastMonth,String cycle);

    /**
     * 恢复会员卡
     * @param id
     * @param userId
     * @return
     */
    public boolean recoveryCard(String id,String userId);

    /**
     * 获取作废的会员卡分页
     * @param pageNumber
     * @param pageSize
     * @param nameOrIdcardOrCardNumber
     * @return
     */
    public Page<Record> cancelCardPageList(Integer pageNumber,Integer pageSize,String nameOrIdcardOrCardNumber);

    /**
     * 通过会员卡编号获取会员卡
     * @param cardNumber
     * @return
     */
    public FinaMembershipCard findCardByCardNumber(String cardNumber);

    /**
     * 通过姓名，身份证，电话号码获取会员卡信息
     * @param name
     * @param idcard
     * @param telephone
     * @return
     */
    public List<Map<String,Object>> getMemberCardInfo(String name,String idcard,String telephone,String carNumber);
    
    /**
     * 列表查询
     * @param card
     * @param fullName
     * @param idcard
     * @return
     */
    public List<Record> findList(FinaMembershipCard card,String fullName,String idcard,String province,String city,String town,String street,String noDealStartDate,String noDealEndDate,String nearExpireStartYear,String nearExpireEndYear,String orderBy);

    /**
     * 分页查询
     * @param pageNumber
     * @param pageSize
     * @param card
     * @param fullName
     * @param idcard
     * @return
     */
    public Page<Record> findList(Integer pageNumber,Integer pageSize,FinaMembershipCard card,String fullName,String idcard,String province,String city,String town,String street,String noDealStartDate,String noDealEndDate,String nearExpireStartYear,String nearExpireEndYear,String orderBy);



    /**
     * 保存会员卡
     * @param card
     * @param member
     * @param ruleId
     * @param userId
     * @return
     */
    public Ret saveCard(FinaMembershipCard card, MmsMember member,String ruleId,String userId,String cardCollect,String cardRoll,List<CfsFileUpload> fileList);


    /**
     * 保存：去重
     * @param card
     * @return
     */
    public boolean getDistinct(FinaMembershipCard card);



    /**
     * 处理会员关联档案
     * @param member
     * @param card
     * @param memberExist
     * @param userId
     */
    public void dealMember(MmsMember member,FinaMembershipCard card,MmsMember memberExist,String userId);


    /**
     * 根据id查询card Record
     * @param id
     * @return
     */
    public Record getCardRecord(String id);


    /**
     * 根据会员卡获取完整会员卡信息和档案信息
     * @param card
     * @return
     */
    public Record getCardAndMember(FinaMembershipCard card);


    /**
     * 更名过户：根据旧会员卡，新会员卡和新会员信息处理卡账户转移（买卡）
     * @param oldCard
     * @param newCard
     * @param member
     * @return
     */
    public boolean dealCardTransferChangeName(List<FinaCardTransferDetail> transferDetailList,FinaMembershipCard newCard,MmsMember member,String userId, FinaCardTransfer transfer);


    /**
     * 处理转移卡的备注
     * @param oldCard
     * @return
     */
    public String dealCardTransferRemark(FinaMembershipCard oldCard,FinaMembershipCard newCard);
    
    
    /**
     * 处理转移卡的备注
     * @param oldCard
     * @return
     */
    public String dealCardTransferRemark(String oldCardNumber,Double consumeTimes,Double balance,Double consumePoints,Double cardIntegrals,Double coupons,Double transferDays,String isIntegral,FinaMembershipCard newCard);


    /**
     * 通过合同编号获取会员卡
     * @param contractNumber
     * @return
     */
    public FinaMembershipCard findBycontractNumber(String contractNumber);

    public FinaMembershipCard findFirst(String sql, Object...object);

    /**
     * 会员扣卡
     * @param describe
     * @param amount
     * @param consumeTimes
     * @param card
     * @param appNo
     * @param appOrderNo
     * @param userId
     * @return
     */
    public boolean saveDeductCard(String describe,Double amount,Double consumeTimes,Double points,Double integrals,FinaMembershipCard card,String appNo,String appOrderNo,String baseId,String userId,Date startDate,Date endDate,String consumeType);

    public FinaMembershipCard getCardByNumber(String cardNumber);

    public boolean delFinaMembershipCard(String id,String userId);

    public Record getMembershipByCardNumber(String cardNumber);

    public boolean batchDelCard(List<FinaMembershipCard> list,String batchVoidRemark,String userId);

    public FinaMembershipCard getByCardNumber(String cardNumber);


    /**
     * 处理随机生成会员卡
     * @return
     */
    public Map<String,Object> dealRandomNumber(String cardTypeId);


    /**
     * 处理会员卡易主
     * @param cardId
     * @param member
     * @param memberExist
     * @param existFlag
     * @param userId
     * @return
     */
    public boolean dealChangeOwn(String cardId,String memberId,String operator,MmsMember member,MmsMember memberExist,String existFlag,String userId);




    /**
     * 根据会员卡号获取会员卡信息和交易记录
     * @param cardNumber
     * @return
     */
    public Record getCardByCardNumber(String id,String cardNumber,String delFlag);


    /**
     * 根据会员卡号获取手机号码
     * @param cardNumber
     * @return
     */
    public String getTelephoneByCardNumber(String cardNumber);


    /**
     *确认该卡是否为该会员拥有
     * @param cardNumber
     * @param memberName
     * @return
     */
    public String confirmCardMember(String cardNumber,String memberName);


    /**
     * 处理转移会员卡：更名过户
     * @param oldCard
     * @param newCard
     * @param member
     * @param userId
     * @return
     */
    public Map<String,Object> cardTransferChangeName(List<FinaCardTransferDetail> transferDetailList,FinaMembershipCard newCard,MmsMember member,String userId,FinaCardTransfer transfer);


    /**
     * 处理转移会员卡：会员卡升级
     * @param oldCard
     * @param newCard
     * @param member
     * @param userId
     * @return
     */
    public Map<String,Object> cardTransferUpgrade(List<FinaCardTransferDetail> transferDetailList,FinaMembershipCard newCard,MmsMember member,String userId,FinaCardTransfer transfer);



    /**
     * 处理转移会员卡升级：更改会员档案
     * @param oldCard
     * @param newCard
     * @param member
     * @param userId
     * @return
     */
    public Map<String,Object> dealCardTransferForUpdateMember(FinaMembershipCard oldCard,FinaMembershipCard newCard,MmsMember member,String userId,FinaCardTransfer transfer);


    /**
     * 转移会员卡升级：处理普通账单和违约账单
     * @param crList
     * @param prList
     */
    public void dealSimpleBill(List<FinaConsumeRecord> crList,List<FinaPunishRecord> prList,FinaMembershipCard newCard,String userId);


    /**
     * 转移会员卡升级：处理旅居账单
     * @param erList
     * @param oldCard
     * @param newCard
     * @param userId
     */
    public Map<String,Object> dealFifficultyErBill(List<FinaExpenseRecord> erList,FinaMembershipCard oldCard,FinaMembershipCard newCard,List<FinaExpenseRecord> bookAndTouristList,String newFullName,String userId);


    /**
     * 转移会员卡升级：操作增删改
     * @param oldCard
     * @param detailList
     * @param bookAndTouristList
     * @param delFrList
     * @param updateFrList
     * @param saveFrList
     * @param saveDcList
     * @return
     */
    public boolean updateCardTransferUpgrade(List<FinaCardTransferDetail> transferDetailList,FinaMembershipCard newCard,MmsMember member,String userId, FinaCardTransfer transfer);


    /**
     * 转移会员卡升级：处理备注
     * 会员卡属性备注
     * @param oldCard
     * @param newCard
     * @return
     */
    public String getCardTransferUpgradeRemark(FinaMembershipCard oldCard,FinaMembershipCard newCard);


    /**
     * 转移会员卡升级：处理备注
     *
     * @param remark
     * @param mainList
     * @param bookAndTouristList
     * @return
     */
    public String getCardTransferUpgradeRemarkAll(String remark,List<FinaExpenseRecord> mainList,List<FinaExpenseRecord> bookAndTouristList);


    /**
     * 转移会员卡升级：旧账单集合中为旧卡的数据转换为新卡
     * @param erList
     */
    public List<FinaExpenseRecord> oldErChangeNewCard(List<FinaExpenseRecord> erList,String oldCardNumber,String newCardNumber,String userId);


    /**
     * 转移会员卡升级：判断是否符合升级条件
     * @param oldCard
     * @param mmsMember
     * @return
     */
    public boolean isUpgrade(FinaMembershipCard oldCard,MmsMember mmsMember);


    /**
     * 根据会员卡对象查询会员卡全部信息
     * @param keyword
     * @return
     */
    public List<Record> getCardInfo(String keyword);


    /**
     * 根据会员卡数据获取会员卡充值消费数据
     * @param card
     * @return
     */
    public Map<String,Object> getCardRechargeConsumpe(FinaMembershipCard card);


    /**
     * 根据卡号获取会员卡信息
     * @param cardNumber
     * @return
     */
    public FinaMembershipCard getCardByCardNumber(String cardNumber);




    /**
     * 根据id查询对象
     * @param id
     * @return
     */
    public FinaMembershipCard get(String id);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMembershipCard findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaMembershipCard
     */
    public List<FinaMembershipCard> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMembershipCard model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaMembershipCard model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaMembershipCard model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMembershipCard model);


    /**
     * update contractNumber by cardNums
     *
     * @param contractNumber
     * @param cardNums
     * @return
     */
    public boolean updateContractNumByCardNum(String contractNumber, List<String> cardNums, String updateBy);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}