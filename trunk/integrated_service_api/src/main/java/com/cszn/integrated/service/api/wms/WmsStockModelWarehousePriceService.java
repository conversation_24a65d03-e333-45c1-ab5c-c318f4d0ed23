package com.cszn.integrated.service.api.wms;

import com.cszn.integrated.service.entity.wms.WmsStockModelWarehousePrice;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface WmsStockModelWarehousePriceService {

    WmsStockModelWarehousePrice getModelWarehousePrice(String modelId,String warehouseId);

    public List<WmsStockModelWarehousePrice> warehousePriceList(String modelId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsStockModelWarehousePrice findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<WmsStockModelWarehousePrice> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsStockModelWarehousePrice model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsStockModelWarehousePrice model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsStockModelWarehousePrice model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsStockModelWarehousePrice model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsStockModelWarehousePrice> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsStockModelWarehousePrice> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsStockModelWarehousePrice> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);

}
