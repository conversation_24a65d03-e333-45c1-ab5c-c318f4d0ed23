package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.sys.Role;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface RoleService  {

    public Page<Record> findUserRolePageById(int pageNumber,int pageSize,String roleId);

    /**
     * 通过登录名字查询权限
     * @param name
     * @return
     */
    public List<Role> findRoleByName(String name,String roleSystem);

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<Role> paginateByCondition(Role role, int pageNumber, int pageSize);
    
    /**
     * 检查角色名称是否重复
     * 
     * @param roleName
     * @return
     */
    public boolean checkRoleNameRepeat(final String roleName,final String systemType);
    
    /**
     * 角色保存方法
     * 
     * @param model
     * @param menuIds
     * @return
     */
    public boolean roleSave(Role model, String menuIds);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public Role findById(Object id);


    /**
     * find all model
     *
     * @return all <Role
     */
    public List<Role> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(Role model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(Role model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(Role model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(Role model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}