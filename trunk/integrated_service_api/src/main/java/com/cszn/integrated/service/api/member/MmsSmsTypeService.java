package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsSmsType;
import com.cszn.integrated.service.entity.member.MmsWxArticle;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MmsSmsTypeService {

    public Page<MmsSmsType> tablePage(int pageNumber,int pageSize,MmsSmsType type);

    public List<MmsSmsType> findMmsSmsType();

    public boolean saveType(MmsSmsType type,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsSmsType findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsWxArticle
     */
    public List<MmsSmsType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsSmsType model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MmsSmsType model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MmsSmsType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsSmsType model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
