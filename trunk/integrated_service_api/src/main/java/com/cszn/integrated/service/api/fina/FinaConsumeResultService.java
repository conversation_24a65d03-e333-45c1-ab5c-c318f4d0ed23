package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaConsumeResult;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface FinaConsumeResultService {

    /**
     * 获取结果分页
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<FinaConsumeResult> consumeResultPage(Integer pageNumber,Integer pageSize);

    /**
     * 添加消费结果记录
     * @param consumeNo
     * @param pushStatus
     * @param settleTime
     * @return
     */
    public boolean saveConsumeResult(String consumeNo, String pushStatus, Date settleTime);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaConsumeResult findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaDayDeductStatistic
     */
    public List<FinaConsumeResult> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaConsumeResult model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaConsumeResult model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaConsumeResult model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaConsumeResult model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaConsumeResult> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaConsumeResult> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaConsumeResult> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
