package com.cszn.integrated.service.api.food;

import java.util.List;

import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.food.FoodInfo;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface FoodInfoService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<FoodInfo> paginateByCondition(FoodInfo model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FoodInfo findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FoodInfo
     */
    public List<FoodInfo> findList();


    /**
     * find all model
     *
     * @return all <FoodInfo
     */
    public List<FoodInfo> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FoodInfo model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FoodInfo model);

    /**
     * 保存信息
     * @param model
     * @return
     */
    public boolean saveInfo(FoodInfo model, List<CfsFileUpload> fileList);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FoodInfo model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FoodInfo model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FoodInfo> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FoodInfo> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FoodInfo> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}