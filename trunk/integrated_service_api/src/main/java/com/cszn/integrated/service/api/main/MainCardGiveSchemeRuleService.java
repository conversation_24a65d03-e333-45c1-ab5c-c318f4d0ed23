package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCardGiveSchemeRule;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.Date;
import java.util.List;

public interface MainCardGiveSchemeRuleService {

    public List<Record> getCardGiveSchemeRuleResult(Date openTime, String schemeId);

    /**
     * 赠送一次性的赠送规则
     * @param cardId
     * @param schemeId
     */
    public void giveDisposableGiveRule(String cardId,String schemeId);

    /**
     * 查询未赠送的一次性赠送规则
     * @param cardId
     * @param schemeId
     * @return
     */
    public List<MainCardGiveSchemeRule> findDisposableGiveRule(String cardId,String schemeId);

    /**
     * 获取赠送规则分页
     * @param pageNumber
     * @param pageSize
     * @param schemeRule
     * @return
     */
    public Page<MainCardGiveSchemeRule> giveSchemeRulePage(Integer pageNumber,Integer pageSize,MainCardGiveSchemeRule schemeRule);

    /**
     * 通过方案id获取规则list，如果周期为空则返回所有周期的
     * @param schemeId
     * @param cycle
     * @return
     */
    public List<MainCardGiveSchemeRule> findGiveSchemeRuleList(String schemeId,String cycle);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardGiveSchemeRule findById(Object id);


    /**
     * find all model
     *
     * @return all <Dict
     */
    public List<MainCardGiveSchemeRule> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardGiveSchemeRule model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainCardGiveSchemeRule model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainCardGiveSchemeRule model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardGiveSchemeRule model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
