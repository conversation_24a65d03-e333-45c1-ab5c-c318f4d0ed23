package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseRestaurant;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBaseRestaurantService  {

    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseRestaurant> paginate(MainBaseRestaurant model, int page, int pageSize);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseRestaurant findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainBaseRestaurant
     */
    public List<MainBaseRestaurant> findList(String baseId, String isEnabled);
    

    /**
     * find all model
     *
     * @return all <MainBaseArea
     */
    public List<MainBaseRestaurant> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseRestaurant model);

    
    /**
     * 保存餐厅
     * @param model
     * @return
     */
    public boolean saveRestaurant(MainBaseRestaurant model);
    
    
    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseRestaurant model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseRestaurant model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseRestaurant model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseRestaurant> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseRestaurant> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseRestaurant> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}