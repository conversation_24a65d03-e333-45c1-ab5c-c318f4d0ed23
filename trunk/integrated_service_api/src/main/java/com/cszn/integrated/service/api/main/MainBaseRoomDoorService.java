package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseRoomDoor;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MainBaseRoomDoorService  {

    /**
     * 根据房间Id查询列表
     *
     * @param roomId
     * @return List<MainBaseRoomDoor>
     */
	public List<MainBaseRoomDoor> findListByRoomId(String roomId);
    
    /**
     * 保存方法
     * 
     * @param model
     * @return
     */
    public boolean roomDoorSave(List<MainBaseRoomDoor> roomDoorList, String userId);

    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean deleteRoomDoor(MainBaseRoomDoor model, String userId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseRoomDoor findById(Object id);


    /**
     * find all model
     *
     * @return all <Role
     */
    public List<MainBaseRoomDoor> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseRoomDoor model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainBaseRoomDoor model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainBaseRoomDoor model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseRoomDoor model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}