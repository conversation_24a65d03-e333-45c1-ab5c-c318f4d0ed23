package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainBaseRoomType;
import com.cszn.integrated.service.entity.main.MainBaseRoomType;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBaseRoomTypeService {


    public List<MainBaseRoomType> getBaseRoomTypeList(String baseId);

    /**
     * 判断房间类型名称是否存在
     * @param id
     * @param name
     * @return
     */
    public MainBaseRoomType findRoomTypeIsExist(String id, String name);

    /**
     * 获取房间类型
     * @return
     */
    public List<MainBaseRoomType> findRoomTypeByBaseId();

    /**
     * 房间类型列表
     * @param pageNumber
     * @param pageSize
     * @param roomType
     * @return
     */
    public Page<MainBaseRoomType> findListPage(Integer pageNumber, Integer pageSize, MainBaseRoomType roomType);


    /**
     * 根据id获取房间类型对象
     * @param id
     * @return
     */
    public MainBaseRoomType get(String id);


    /**
     * 保存房间类型
     * @param roomType
     * @param userId
     * @return
     */
    public boolean saveRoomType(MainBaseRoomType roomType,String userId);


    /**
     * 保存：去重
     * @param roomType
     * @return
     */
    public String getDistinct(MainBaseRoomType roomType);


    /**
     * 删除房间类型
     * @param id
     * @param userId
     * @return
     */
    public boolean delRoomType(String id,String userId);


    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDelRoomType(List<MainBaseRoomType> list,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseRoomType findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseRoomType
     */
    public List<MainBaseRoomType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseRoomType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseRoomType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseRoomType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseRoomType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseRoomType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseRoomType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseRoomType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}