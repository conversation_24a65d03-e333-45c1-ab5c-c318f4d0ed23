package com.cszn.integrated.service.api.main;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBranchOfficeUser;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainBranchOfficeUserService  {

    /**
     * 根据id获取对象
     * @param id
     * @return
     */
    public MainBranchOfficeUser get(String id);


    /**
     * 通过用户保存用户分公司关系
     * @param id
     * @param userId
     * @return
     */
    public boolean saveOfficeUserByUser(String id,String userId);


    /**
     * 通过userId获取分公司
     * @param userId
     * @return
     */
    public MainBranchOfficeUser getByUserId(String userId);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBranchOfficeUser findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBranchOfficeUser
     */
    public List<MainBranchOfficeUser> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBranchOfficeUser model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBranchOfficeUser model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBranchOfficeUser model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBranchOfficeUser model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBranchOfficeUser> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBranchOfficeUser> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBranchOfficeUser> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}