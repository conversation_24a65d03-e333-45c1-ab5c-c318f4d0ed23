package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCsamAssetBrand;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainCsamAssetBrandService {


    public boolean assetBrandSave(MainCsamAssetBrand brand,String userId);

    public Page<MainCsamAssetBrand> assetBrandPageList(int pageNumber,int pageSize,MainCsamAssetBrand brand);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCsamAssetBrand findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainCsamAssetBrand
     */
    public List<MainCsamAssetBrand> findList();


    /**
     * find all model
     *
     * @return all <MainCsamAssetBrand
     */
    public List<MainCsamAssetBrand> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCsamAssetBrand model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCsamAssetBrand model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCsamAssetBrand model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCsamAssetBrand model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCsamAssetBrand> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCsamAssetBrand> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCsamAssetBrand> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
