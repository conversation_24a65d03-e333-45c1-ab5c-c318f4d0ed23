package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaCardRefund;
import com.cszn.integrated.service.entity.fina.FinaCardRefundDetail;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardRefundService {
	
    /**
     * 分页
     *
     * @param model
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<FinaCardRefund> paginateByCondition(FinaCardRefund model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardRefund findById(Object id);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardRefund findWithDetailById(Object id);


    /**
     * find all model
     *
     * @return all <FinaCardRefund
     */
    public List<FinaCardRefund> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardRefund model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardRefund model);

    /**
     * 保存信息
     * @param model
     * @return
     */
    public boolean saveRefund(FinaCardRefund model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardRefund model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardRefund model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardRefund> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardRefund> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardRefund> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}