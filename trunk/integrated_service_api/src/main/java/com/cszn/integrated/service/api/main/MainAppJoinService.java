package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainAppJoinService {

    /**
     * 获取启用的应用list
     * @return
     */
    public List<MainAppJoin> findAppJoinList();

    /**
     *
     * @param pageNumber
     * @param pageSize
     * @param app
     * @return
     */
    public Page<MainAppJoin> findListPage(Integer pageNumber, Integer pageSize, MainAppJoin app);

    /**
     * 删除
     * @param id
     * @param userId
     * @return
     */
    public boolean delete(String id, String userId);

    /**
     * 保存
     * @param obj
     * @param userId
     * @return
     */
    public String save(MainAppJoin obj, String userId);


    /**
     * 批量删除
     * @param list
     * @param userId
     * @return
     */
    public boolean batchDel(List<MainAppJoin> list, String userId);


    /**
     * 商户去重
     * @param app
     * @return
     */
    public String getDistinct(MainAppJoin app);


    /**
     * 根据商户号获取商户
     * @param appNo
     * @return
     */
    public MainAppJoin getAppJoinByNo(String appNo);


    /**
     * 获取所有商户
     * @return
     */
    public List<MainAppJoin> findList();


    /**
     *
     * @param appNo
     * @return
     */
    public MainAppJoin isHaveAppNo(String appNo);


    /**
     * 根据id获取对象
     * @param id
     * @return
     */
    public MainAppJoin get(String id);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainAppJoin findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MainAppJoin> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainAppJoin model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainAppJoin model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainAppJoin model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainAppJoin model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}