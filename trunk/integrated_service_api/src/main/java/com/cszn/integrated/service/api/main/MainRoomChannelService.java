package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainRoomChannel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainRoomChannelService {

    /**
     * 通过房间id获取预定渠道
     * @param roomId
     * @return
     */
    public List<MainRoomChannel> roomChannelList(String roomId);

    /**
     * 保存房间预定渠道
     * @param roomId
     * @param bookChannelIdStr
     * @param userId
     * @return
     */
    public boolean saveRoomChannel(String roomId, String bookChannelIdStr,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainRoomChannel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBedStatus
     */
    public List<MainRoomChannel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainRoomChannel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainRoomChannel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainRoomChannel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainRoomChannel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainRoomChannel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainRoomChannel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainRoomChannel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);
}
