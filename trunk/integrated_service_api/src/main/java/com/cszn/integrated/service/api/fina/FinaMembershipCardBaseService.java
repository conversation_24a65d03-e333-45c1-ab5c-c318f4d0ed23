package com.cszn.integrated.service.api.fina;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.fina.FinaMembershipCardBase;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaMembershipCardBaseService  {
	
	/**
	 * find model by primary cardId and baseId
	 *
	 * @param cardId
	 * @param baseId
	 * @return
	 */
	public FinaMembershipCardBase findByCardBaseId(String cardId, String baseId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaMembershipCardBase findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaMembershipCardBase
     */
    public List<FinaMembershipCardBase> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaMembershipCardBase model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaMembershipCardBase model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaMembershipCardBase model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaMembershipCardBase model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaMembershipCardBase> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaMembershipCardBase> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaMembershipCardBase> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}