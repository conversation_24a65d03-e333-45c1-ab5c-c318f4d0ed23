package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainMallProductSaleApply;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface MainMallProductSaleApplyService {

    MainMallProductSaleApply findById(Object id);

    MainMallProductSaleApply findByTaskId(String taskId);

    Page<Record> pagelist(int page,int limit,MainMallProductSaleApply productSaleApply);

    boolean taskCompleted(String id);
}
