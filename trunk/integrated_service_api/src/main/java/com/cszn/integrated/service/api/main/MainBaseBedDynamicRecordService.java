package com.cszn.integrated.service.api.main;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.service.entity.main.MainBaseBedDynamicRecord;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;

public interface MainBaseBedDynamicRecordService {

    /**
     * 修改预定、入住记录信息
     * @param dynamicRecord
     * @return
     */
    public boolean updateBedDynamicRecord(MainBaseBedDynamicRecord dynamicRecord);

    /**
     * 获取基地预定数量
     * @param baseId
     * @param beginDate
     * @param endDate
     * @return
     */
    public TreeMap<String, Object> getBaseBedBooks(String baseId, Date beginDate, Date endDate);

    /**
     *
     * @param baseId
     * @param date
     * @return
     */
    public Page<Record> baseExpectedCheckinDetailPageList(Integer pageNumber,Integer pageSize,String baseId,String date);

    /**
     * 机构预计入住数量
     * @return
     */
    public List<Record> baseExpectedCheckinList(String date);

    /**
     * 通过床位号获取床位动态记录
     * @param bedId
     */
    public Page<Record> findDynamicRecordPageByBedId(Integer pageNumber,Integer pageSize,String bedId);

    /**
     * 床位状态一览表数据
     * @param floorId
     * @param startDate
     * @param endDate
     * @return
     */
    public Page<Record> bedDynamicRecordTableData(Integer pageNumber, Integer pageSize, String baseId, String buildingId, String floorId, String roomAndBedName, String startDate, String endDate);

    /**
     *
     * @param id
     * @return
     */
    public Map<String,Object> sendBedDynamicRecord(String id);

    /**
     * 通过appno获取床位动态
     * @param pageNumber
     * @param pageSize
     * @param record
     * @return
     */
    public Page<MainBaseBedDynamicRecord> findBedDynamicRecordPageByAppNo(Integer pageNumber,Integer pageSize,MainBaseBedDynamicRecord record,String roomAndBed);

    /**
     * 获取同步状态为失败的床位动态记录分页
     * @param pageNumber
     * @param pageSize
     * @param appNo
     * @return
     */
    public Page<MainBaseBedDynamicRecord> findBedDynamicRecordPage(Integer pageNumber,Integer pageSize,String appNo);

    /**
     * 长住判断床位是否可用
     * @param bedId
     * @param checkinDate
     * @return
     */
    public Map<String,Object> longStayIsBooking(String bedId,String checkinDate);

    /**
     * 保存动态信息
     * @param jsonObject
     * @return
     */
    public Map<String,Object> saveDynamicRecord(JSONObject jsonObject);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseBedDynamicRecord findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<MainBaseBedDynamicRecord> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseBedDynamicRecord model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseBedDynamicRecord model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseBedDynamicRecord model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseBedDynamicRecord model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseBedDynamicRecord> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseBedDynamicRecord> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseBedDynamicRecord> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
