package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinRuleRange;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeCheckinRuleRangeService {

    public PersOrgEmployeeCheckinRuleRange getEmpTopCheckinRuleRangeByDate(String empId, Date date);

    public boolean delRangeByRuleId(String ruleId,String userId);

    public List<PersOrgEmployeeCheckinRuleRange> getDeptRangeByRuleId(String ruleId);

    public List<PersOrgEmployeeCheckinRuleRange> getEmpRangeByRuleId(String ruleId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinRuleRange findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrgEmployeeRel
     */
    public List<PersOrgEmployeeCheckinRuleRange> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinRuleRange model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinRuleRange model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinRuleRange model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinRuleRange model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleRange> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleRange> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinRuleRange> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
