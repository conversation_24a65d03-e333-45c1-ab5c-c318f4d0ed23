package com.cszn.integrated.service.api.pers;

import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.Map;

public interface PersApprovalService {

    public Page<Record> findEmployeeBusinessTripPageByCreateId(int pageNumber, int pageSize,String deptId,String empId, String createId,String name,String startDate,String endDate);

    public Page<Record> findEmployeeCheckinFillCardPageByCreateId(int pageNumber, int pageSize,String deptId
            ,String empId, String createId,String name,String startDate,String endDate);

    public Record findEmployeeQuitRecordById(String id);

    public Record findEmployeeQualifiedRecordById(String id);

    Map<String,Object> getTaskDetail(String taskId, String userId);


    Record findEmployeeChangeDeptRecord(String id);

    Record findEmployeeQualifiedRecord(String id);

    Record findEmployeeQuitRecord(String id);

    Record findEmployeeOverTimeRecord(String id);

    Record findEmployeeLeaveRestRecord(String id);

    Page<Record> findEmployeeQuitPageByCreateId(int pageNumber, int pageSize,String createId,String name,String startDate,String endDate,String empId,String deptId);

    public Page<Record>  findEmployeeChangePageByCreateId(int pageNumber, int pageSize, String createId,String name,String startDate,String endDate);

    Map<String,Object> doTask(String taskId,String taskNo,String stepState,String currentStepAlias,String msg,String userId);

    Page<Record>  findEmployeeQualifiedPageByCreateId(int pageNumber, int pageSize, String createId,String name
            ,String startDate,String endDate,String deptId,String status);

    public Page<Record> findEmployeeLeaveRestPageByCreateId(int pageNumber, int pageSize,String deptId,String empId, String createId,String name,String startDate,String endDate);

    public Page<Record> findEmployeeOverTimePageByCreateId(int pageNumber, int pageSize,String deptId,String empId, String createId,String name,String startDate,String endDate);

    public Page<Record> findEmployeeDispatchPageByCreateId(int pageNumber, int pageSize,String deptId,String empId, String createId,String name,String startDate,String endDate);

    public Page<Record> findEmployeeChangeApplyPageByCreateId(int pageNumber, int pageSize,String deptId,String newDeptId,String empId, String createId,String name,String startDate,String endDate);
}
