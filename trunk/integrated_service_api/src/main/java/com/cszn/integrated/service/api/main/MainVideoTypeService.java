package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainVideoType;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MainVideoTypeService {

    MainVideoType findById(Object id);

    boolean saveVideoType(MainVideoType videoType,String userId);

    Page<MainVideoType> videoTypePageList(int page,int limit,MainVideoType type);

    List<MainVideoType> videoTypeList();
}
