package com.cszn.integrated.service.api.member;

import java.util.List;

import com.cszn.integrated.service.entity.member.MmsCustomerFace;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MmsCustomerFaceService  {
	
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<MmsCustomerFace> paginateByCondition(MmsCustomerFace model, int pageNumber, int pageSize);

    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsCustomerFace findById(Object id);
    
    
    /**
     * find model by primary key
     *
     * @param idcard
     * @return
     */
    public MmsCustomerFace findByIdcardAndPicType(String idcard, String picType);
    
    
    /**
     * find model by primary key
     *
     * @param customerName
     * @param idcard
     * @return
     */
    public MmsCustomerFace findByNameAndIdcard(String customerName, String idcard);


    /**
     * find model by primary key
     *
     * @param picType
     * @param idcard
     * @return
     */
    public MmsCustomerFace findByTypeAndIdcard(String picType, String idcard);

    
    /**
     * find all model
     *
     * @return all <MmsCustomerFace
     */
    public List<MmsCustomerFace> findAll();
    
    
    /**
     * find all model
     *
     * @return all <MmsCustomerFace
     */
    public List<MmsCustomerFace> findList(String customerName, String idcard, String picType);


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsCustomerFace model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsCustomerFace model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsCustomerFace model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsCustomerFace model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsCustomerFace> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsCustomerFace> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsCustomerFace> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}