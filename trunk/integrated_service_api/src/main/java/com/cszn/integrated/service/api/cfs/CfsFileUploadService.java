package com.cszn.integrated.service.api.cfs;

import java.util.List;

import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface CfsFileUploadService  {
	
	/**
	 * find model by relationId and fileName
	 *
	 * @param id
	 * @return
	 */
	public CfsFileUpload findByIdAndName(String relationId, String fileName);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CfsFileUpload findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FoodInfo
     */
    public List<CfsFileUpload> findListByRelationId(String relationId);
    

    /**
     * find all model
     *
     * @return all <CfsFileUpload
     */
    public List<CfsFileUpload> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CfsFileUpload model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CfsFileUpload model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CfsFileUpload model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CfsFileUpload model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CfsFileUpload> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CfsFileUpload> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CfsFileUpload> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}