package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.*;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;

public interface PersOrgEmployeeCheckinDaySummaryService {

    public Page<Record> pageListNew(int pageNum, int pageSize,String employeeId, String fullName, String startDateStr, String endDateStr
            ,String archiveStatus, List<String> deptIds,String userId,Integer page);

    public void saveLeaveRestDaySummary(PersOrgEmployeeLeaveRestApply leaveRestApply,Date date);

    public void saveOverTimeDaySummary(PersOrgEmployeeOverTimeApply overTimeApply);

    public List<PersOrgEmployeeCheckinDaySummary> findBySql(String sql,Object[] params);

    public void genScheduleCheckinDelayDaySummary(String empId, Date summaryDate, PersOrgEmployeeCheckinRule checkinRule, PersOrgEmployeeCheckinRuleSchedule schedule, Date startDate, Date endDate);

    public void genCheckinDelayDaySummary(String empId, Date summaryDate, PersOrgEmployeeCheckinRule checkinRule, PersOrgEmployeeCheckinRuleCheckinDate checkinDate, Date startDate, Date endDate);

    public void genEmployeeCheckinDaySummary(String empId,Date date);

    List<Record> empCheckinDaySummaryDetailList(String empId,String startDateStr,String endDateStr);

    public List<Record>  empCheckinDaySummaryDetailListNew(String empId, String startDateStr, String endDateStr);

    Page<Record> pageList(int pageNum,int pageSize,String employeeId,String fullName,String startDateStr,String endDateStr,String archiveStatus,List<String> deptIds);

    Page<Record> pageList2(int pageNum, int pageSize,String employeeId, String fullName, String startDateStr, String endDateStr,String archiveStatus, List<String> deptIds,String userId);

    PersOrgEmployeeCheckinDaySummary getEmpDaySummary(String empId, Date date);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeCheckinDaySummary findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeType
     */
    public List<PersOrgEmployeeCheckinDaySummary> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeCheckinDaySummary model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeCheckinDaySummary model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeCheckinDaySummary model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeCheckinDaySummary model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinDaySummary> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinDaySummary> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeCheckinDaySummary> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
