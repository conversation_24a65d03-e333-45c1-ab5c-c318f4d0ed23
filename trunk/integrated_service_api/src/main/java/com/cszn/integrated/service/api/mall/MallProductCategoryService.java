package com.cszn.integrated.service.api.mall;

import com.cszn.integrated.service.entity.mall.MallProductCategory;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

import java.util.List;

public interface MallProductCategoryService {


    public Page<MallProductCategory> pageList(int pageNumber,int pageSize,MallProductCategory category);


    public boolean categorySave(MallProductCategory category,String userId);

    /**
     * 获取全部分类
     * @return
     */
    public List<MallProductCategory> findAllCategory();

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MallProductCategory findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsMerchant
     */
    public List<MallProductCategory> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MallProductCategory model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MallProductCategory model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MallProductCategory model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MallProductCategory model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}
