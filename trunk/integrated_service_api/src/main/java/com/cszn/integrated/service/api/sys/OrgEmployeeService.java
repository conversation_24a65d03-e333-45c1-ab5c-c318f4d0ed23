package com.cszn.integrated.service.api.sys;

import java.util.List;

import com.cszn.integrated.service.entity.sys.OrgEmployee;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface OrgEmployeeService  {

    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<OrgEmployee> paginateByCondition(OrgEmployee orgEmployee, int pageNumber, int pageSize);
    
    /**
     * 保存方法
     * 
     * @param orgEmployee
     * @return
     */
    public boolean saveOrgEmployee(final OrgEmployee orgEmployee);
    
    /**
     * 批量删除方法
     * 
     * @param empList
     * @return
     */
    public boolean batchDel(final List<OrgEmployee> empList, final List<User> userList);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public OrgEmployee findById(Object id);


    /**
     * find all model
     *
     * @return all <OrgEmployee
     */
    public List<OrgEmployee> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(OrgEmployee model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(OrgEmployee model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(OrgEmployee model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(OrgEmployee model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}