package com.cszn.integrated.service.api.member;

import com.cszn.integrated.service.entity.member.MmsCardEquipment;
import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsBaseEquipment;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import java.util.List;

public interface MmsBaseEquipmentService  {


    /**
     * 设备分页
     * @param pageNumber
     * @param pageSize
     * @param baseEquipment
     * @return
     */
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize, MmsBaseEquipment baseEquipment);


    /**
     * 根据id获取设备对象
     * @param id
     * @return
     */
    public MmsBaseEquipment get(String id);


    /**
     * 保存设备
     * @param baseEquipment
     * @param userId
     * @return
     */
    public String saveBaseEquipment(MmsBaseEquipment baseEquipment,String userId);



    /**
     * 删除设备
     * @param id
     * @param userId
     * @return
     */
    public boolean delBaseEquipment(String id,String userId);



    /**
     * 保存：去重
     * @param baseEquipment
     * @return
     */
    public String getDistinct(MmsBaseEquipment baseEquipment);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsBaseEquipment findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsBaseEquipment
     */
    public List<MmsBaseEquipment> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsBaseEquipment model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsBaseEquipment model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsBaseEquipment model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsBaseEquipment model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsBaseEquipment> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsBaseEquipment> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsBaseEquipment> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}