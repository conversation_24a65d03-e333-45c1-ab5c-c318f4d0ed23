package com.cszn.integrated.service.api.crm;

import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.crm.CrmCardRollCustomerType;
import io.jboot.db.model.Columns;

import java.util.List;

public interface CrmCardRollCustomerTypeService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public CrmCardRollCustomerType findById(Object id);


    /**
     * find all model
     *
     * @return all <CrmCardRollCustomerType
     */
    public List<CrmCardRollCustomerType> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(CrmCardRollCustomerType model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(CrmCardRollCustomerType model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(CrmCardRollCustomerType model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(CrmCardRollCustomerType model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<CrmCardRollCustomerType> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<CrmCardRollCustomerType> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<CrmCardRollCustomerType> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}