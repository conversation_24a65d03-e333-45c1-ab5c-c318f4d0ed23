package com.cszn.integrated.service.api.pers;

import java.util.List;

import com.cszn.integrated.service.entity.pers.PersNoticeReceiver;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface PersNoticeReceiverService  {
    
    /**
     * find model by primary key
     *
     * @param noticeId
     * @param userId
     * @return
     */
    public PersNoticeReceiver findByNoticeIdAndUserId(String noticeId, String userId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersNoticeReceiver findById(Object id);


    /**
     * find all model
     *
     * @return all <PersNoticeReceiver
     */
    public List<PersNoticeReceiver> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersNoticeReceiver model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersNoticeReceiver model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersNoticeReceiver model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersNoticeReceiver model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersNoticeReceiver> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersNoticeReceiver> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersNoticeReceiver> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}