package com.cszn.integrated.service.api.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaConsumeRecordDetail;
import com.jfinal.plugin.activerecord.Page;

public interface FinaConsumeRecordDetailService {

    /**
     * 获取分页
     * @param pageNumber
     * @param pageSize
     * @param consumeId
     * @return
     */
    public Page<FinaConsumeRecordDetail> findListPage(int pageNumber, int pageSize, String consumeId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaConsumeRecordDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaConsumeRecordDetail
     */
    public List<FinaConsumeRecordDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaConsumeRecordDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(FinaConsumeRecordDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(FinaConsumeRecordDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaConsumeRecordDetail model);
}