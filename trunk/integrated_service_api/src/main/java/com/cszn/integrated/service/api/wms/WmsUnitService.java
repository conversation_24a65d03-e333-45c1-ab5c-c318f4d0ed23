package com.cszn.integrated.service.api.wms;

import java.util.List;

import com.cszn.integrated.service.entity.wms.WmsUnit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface WmsUnitService  {
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public WmsUnit findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <WmsUnit
     */
    public List<WmsUnit> findList();


    /**
     * find all model
     *
     * @return all <WmsUnit
     */
    public List<WmsUnit> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(WmsUnit model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(WmsUnit model);
    
    /**
     * 保存供单位
     * @param WmsUnit
     * @return
     */
    public boolean saveUnit(WmsUnit model);

    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(WmsUnit model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(WmsUnit model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<WmsUnit> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<WmsUnit> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<WmsUnit> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}