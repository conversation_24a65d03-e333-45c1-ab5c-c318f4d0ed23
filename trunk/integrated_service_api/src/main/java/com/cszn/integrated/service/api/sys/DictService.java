package com.cszn.integrated.service.api.sys;

import java.util.List;
import java.util.Map;

import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface DictService  {

    public Map<String,String> getMapByTypeOnUse(String listType);
	/**
	 * 通过类型获取list
	 * @param listType
	 * @return
	 */
	public String getDictNameByTypeValue(String dictType, String dictValue);

    /**
     * 通过类型获取list
     * @param listType
     * @return
     */
    public List<Dict> findDictList(final String listType);

    /**
     * 根据条件查询表格分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<Dict> paginateByCondition(Dict dict, int pageNumber, int pageSize);
    
    /**
     * 查询字典类型列表
     * 
     * @return
     */
    public List<Record> getDictTypeList();
    
    /**
     * 验证字典名称是否存在
     * 
     * @param dictType
     * @param dictName
     * @return
     */
    public boolean verifyDictName(final String dictType, final String dictName);
    
    /**
     * 字典保存方法
     * 
     * @param model
     * @return
     */
    public Ret saveDict(Dict model);
    
    /**
     * 批量删除方法
     * 
     * @param dictList
     * @return
     */
    public boolean batchDel(final List<Dict> dictList);
	
	/**
	 * 根据字典类型查询字典列表
	 * @param dictType
	 * @return
	 */
	public List<Dict> getListByTypeOnUse(String dictType);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public Dict findById(Object id);


    /**
     * find all model
     *
     * @return all <Dict
     */
    public List<Dict> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(Dict model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(Dict model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(Dict model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(Dict model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}