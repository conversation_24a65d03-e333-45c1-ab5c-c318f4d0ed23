package com.cszn.integrated.service.api.member;


import com.cszn.integrated.service.entity.member.MmsEquipmentType;
import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.member.MmsCardEquipment;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import java.util.List;

public interface MmsCardEquipmentService  {

    /**
     * 会员卡设备分页
     * @param pageNumber
     * @param pageSize
     * @param cardNumber
     * @param equipmentNo
     * @param equipmentType
     * @return
     */
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize, String cardNumber,String equipmentNo,String equipmentType);


    /**
     * 根据id获取会员卡设备对象
     * @param id
     * @return
     */
    public MmsCardEquipment get(String id);


    /**
     * 保存设备
     * @param cardEquipment
     * @param userId
     * @return
     */
    public String saveCardEquipment(MmsCardEquipment cardEquipment,String userId);



    /**
     * 删除设备
     * @param id
     * @param userId
     * @return
     */
    public boolean delCardEquipment(String id,String userId);


    /**
     * 保存：去重
     * @param cardEquipment
     * @return
     */
    public String getDistinct(MmsCardEquipment cardEquipment);


    /**
     * 更改设备的设置
     * @param id
     * @param set
     * @return
     */
    public boolean updateSet(String id,String set);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MmsCardEquipment findById(Object id);


    /**
     * find all model
     *
     * @return all <MmsCardEquipment
     */
    public List<MmsCardEquipment> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MmsCardEquipment model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MmsCardEquipment model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MmsCardEquipment model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MmsCardEquipment model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MmsCardEquipment> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MmsCardEquipment> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MmsCardEquipment> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}