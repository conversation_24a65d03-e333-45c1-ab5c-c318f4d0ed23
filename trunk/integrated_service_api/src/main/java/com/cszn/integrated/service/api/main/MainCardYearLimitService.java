package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainCardYearLimit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainCardYearLimitService {

    /**
     * 通过字典值获取会员卡年限类型
     * @param value
     * @return
     */
    public MainCardYearLimit findCardYearLimitByCardNumber(String value);

    /**
     * 获取会员卡年限类型list
     * @return
     */
    public List<MainCardYearLimit> findCardYearLimitList();

    /**
     * 通过名称和字典值判断是否存在
     * @param cardYearLimit
     * @return
     */
    public boolean cardYearLimitIsExist(MainCardYearLimit cardYearLimit);

    /**
     * 获取会员卡年限类型分页
     * @param pageNumber
     * @param pageSize
     * @param cardYearLimit
     * @return
     */
    public Page<MainCardYearLimit> cardYearLimitPage(Integer pageNumber,Integer pageSize,MainCardYearLimit cardYearLimit);

    /**
     * 保存会员卡年限类型
     * @param cardYearLimit
     * @param userId
     * @return
     */
    public boolean saveCardYearLimit(MainCardYearLimit cardYearLimit,String userId);

    /**
     * 删除会员卡年限类型
     * @param id
     * @param userId
     * @return
     */
    public boolean delCardYearLimit(String id,String userId);



    public MainCardYearLimit findFirst(String sql, Object... params);



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardYearLimit findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBranchOfficeUser
     */
    public List<MainCardYearLimit> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardYearLimit model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainCardYearLimit model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainCardYearLimit model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardYearLimit model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainCardYearLimit> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainCardYearLimit> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainCardYearLimit> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
