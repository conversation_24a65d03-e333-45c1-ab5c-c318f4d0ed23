package com.cszn.integrated.service.api.pers;


import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.pers.PersPosition;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import java.util.List;

public interface PersPositionService  {

    public List<PersPosition> findByIds(List<String> ids);

    public List<Record> getPositionByOrgId(String orgId);

    /**
     * 获取项目的职位
     * @param orgId
     * @return
     */
    public List<PersPosition> findPositionByOrgId(String orgId);

    /**
     * 职位分页
     * @param pageNumber
     * @param pageSize
     * @param position
     * @return
     */
    public Page<Record> findListPage(Integer pageNumber, Integer pageSize, PersPosition position);


    /**
     * 根据id获取职位对象
     * @param id
     * @return
     */
    public PersPosition get(String id);


    /**
     * 保存职位
     * @param position
     * @param userId
     * @return
     */
    public String savePosition(PersPosition position,String userId);



    /**
     * 删除职位
     * @param id
     * @param userId
     * @return
     */
    public boolean delPosition(String id,String userId);


    /**
     * 保存：去重
     * @param position
     * @return
     */
    public String getDistinct(PersPosition position);



    /**
     * 获取未删除的职位
     * @return
     */
    public List<PersPosition> getUnDelList();



    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersPosition findById(Object id);


    /**
     * find all model
     *
     * @return all <PersPosition
     */
    public List<PersPosition> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersPosition model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersPosition model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersPosition model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersPosition model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersPosition> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersPosition> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersPosition> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}