package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainCardTypeConfig;
import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.Page;

public interface MainCardTypeConfigService  {
	
	/**
	 * 根据卡类别Id和值查询
	 *
	 * @param roomId
	 * @return List<MainCardTypeConfig>
	 */
	public List<MainCardTypeConfig> findListByTypeIdAndValue(String typeId, double value);

    /**
     * 根据卡类别Id查询列表
     *
     * @param roomId
     * @return List<MainCardTypeConfig>
     */
	public List<MainCardTypeConfig> findListByTypeId(String typeId);
	
	/**
     * 保存方法
     * 
     * @param model
     * @return
     */
    public boolean configSave(List<MainCardTypeConfig> configList, String userId);
    
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainCardTypeConfig findById(Object id);


    /**
     * find all model
     *
     * @return all <Role
     */
    public List<MainCardTypeConfig> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainCardTypeConfig model);


    /**
     * save model to database
     *
     * @param model
     * @return
     */
    public Object save(MainCardTypeConfig model);


    /**
     * save or update model
     *
     * @param model
     * @return if save or update success
     */
    public Object saveOrUpdate(MainCardTypeConfig model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainCardTypeConfig model);


    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<? extends Model> paginate(int page, int pageSize);
}