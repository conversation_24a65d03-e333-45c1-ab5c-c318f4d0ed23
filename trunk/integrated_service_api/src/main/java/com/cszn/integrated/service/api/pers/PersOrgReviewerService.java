package com.cszn.integrated.service.api.pers;

import java.util.List;

import com.cszn.integrated.service.entity.pers.PersOrgReviewer;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface PersOrgReviewerService  {
	
    /**
     * 分页
     *
     * @param page
     * @param pageSize
     * @return
     */
    public Page<PersOrgReviewer> paginateByCondition(PersOrgReviewer model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgReviewer findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <PersOrgReviewer
     */
    public List<PersOrgReviewer> findList(String orgId, String reviewerType);


    /**
     * find all model
     *
     * @return all <PersOrgReviewer
     */
    public List<PersOrgReviewer> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgReviewer model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgReviewer model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgReviewer model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgReviewer model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgReviewer> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgReviewer> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgReviewer> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}