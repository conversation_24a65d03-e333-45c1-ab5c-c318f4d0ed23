package com.cszn.integrated.service.api.main;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.main.MainMallProductType;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import java.util.List;

public interface MainMallProductTypeService {
	
	Record getMallProductDetail(String modelId);

    List<Record> getTreeTableTypeList();

    MainMallProductType findById(Object id);

    boolean saveProductType(MainMallProductType type,String userId);

    List<ZTree> typeTreeData();

    void findChildren(List<String> idList, String parentId);

    Page<Record> getMallProductPage(int pageIndex, int pageSize, String typeId, String name,String warehouseId);
}
