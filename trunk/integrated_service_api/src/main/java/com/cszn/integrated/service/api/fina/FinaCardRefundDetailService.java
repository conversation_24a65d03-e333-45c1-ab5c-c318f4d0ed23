package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.fina.FinaCardRefundDetail;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface FinaCardRefundDetailService {
	
    /**
     * 分页
     *
     * @param model
     * @param pageNumber
     * @param pageSize
     * @return
     */
    public Page<FinaCardRefundDetail> paginateByCondition(FinaCardRefundDetail model, int pageNumber, int pageSize);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaCardRefundDetail findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <FinaCardRefundDetail
     */
    public List<FinaCardRefundDetail> findListByRefundId(String refundId);


    /**
     * find all model
     *
     * @return all <FinaCardRefundDetail
     */
    public List<FinaCardRefundDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaCardRefundDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaCardRefundDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaCardRefundDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaCardRefundDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaCardRefundDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaCardRefundDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaCardRefundDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}