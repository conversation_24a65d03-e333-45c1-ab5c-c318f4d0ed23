package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrgEmployeeRecruit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

public interface PersOrgEmployeeRecruitService {

    PersOrgEmployeeRecruit findById(Object id);

    PersOrgEmployeeRecruit findByTaskId(String taskId);

    Page<Record> employeeRecruitPageList(int page,int limit,PersOrgEmployeeRecruit recruit);
}
