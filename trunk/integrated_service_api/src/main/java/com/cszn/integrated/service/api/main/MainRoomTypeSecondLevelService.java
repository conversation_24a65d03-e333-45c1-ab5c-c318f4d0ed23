 package com.cszn.integrated.service.api.main;

import com.cszn.integrated.service.entity.main.MainRoomTypeFirstLevel;
import com.cszn.integrated.service.entity.main.MainRoomTypeSecondLevel;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface MainRoomTypeSecondLevelService {

    public Page<MainRoomTypeSecondLevel> pageList(int page, int limit, MainRoomTypeSecondLevel secondLevel);

    public List<MainRoomTypeSecondLevel> getList();

    public boolean saveSecondLevel(MainRoomTypeSecondLevel secondLevel,String userId);

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainRoomTypeSecondLevel findById(Object id);


    /**
     * find all model
     *
     * @return all <MainRoomTypeSecondLevel
     */
    public List<MainRoomTypeSecondLevel> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainRoomTypeSecondLevel model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainRoomTypeSecondLevel model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainRoomTypeSecondLevel model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainRoomTypeSecondLevel model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainRoomTypeSecondLevel> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainRoomTypeSecondLevel> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainRoomTypeSecondLevel> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}