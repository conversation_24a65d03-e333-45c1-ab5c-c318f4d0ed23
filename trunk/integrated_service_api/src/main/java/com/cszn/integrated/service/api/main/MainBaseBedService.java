package com.cszn.integrated.service.api.main;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jfinal.plugin.activerecord.Page;
import com.cszn.integrated.service.entity.main.MainBaseBed;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.List;
import java.util.Map;

public interface MainBaseBedService  {

    /**
     * 企业微信床态查询接口
     * @param baseId
     * @return
     */
    public JSONObject getFreeBedList(String baseId);

    /**
     * 通过id数组获取床位集合
     * @param ids
     * @return
     */
    public List<MainBaseBed> findBedByIds(String[] ids);

    /**
     * 判断床位是否属于这个基地
     * @param bedId
     * @param baseId
     * @return
     */
    public boolean bedBelongToBase(String bedId,String baseId);

    /**
     * 批量修改床位状态并返回修改成功的床位id集合
     * @param jsonObject
     * @return
     */
    public List<String> batchUpdateBedStatus(JSONObject jsonObject);

    /**
     * 通过床位ids查询是否有状态为入住中的床位
     * @param ids
     * @return
     */
    public List<MainBaseBed> findCheckinBedByIds(String[] ids);

    /**
     * 通过房间id数组获取床位集合
     * @param ids
     * @return
     */
    public List<MainBaseBed> findBedListByRoomIds(String[] ids);

    /**
     * 批量删除床位
     * @param jsonArray
     * @param userId
     * @return
     */
    public boolean batchDelBed(JSONArray jsonArray,String userId);

    /**
     * 判断该床位是否存在
     * @return
     */
    public MainBaseBed bedIsExist(MainBaseBed bed);

    /**
     * 通过房间id查询下面的床位数
     * @param roomId
     * @return
     */
    public Integer queryBedCountByRoomId(String roomId);


    //public boolean batchDelMainBaseBed(List<MainBaseBed> baseBedList,String userId);

    /**
     * 通过房间id获取床位
     * @param roomIds
     * @return
     */
    public Page<Record> getBedPageByRoomIds(String[] roomIds, Integer pageNumber, Integer pageSize);

    /**
     * 保存床位
     * @param mainBaseRoom
     * @return
     */
    public boolean saveBed(MainBaseBed mainBaseRoom);

    /**
     * 删除床位
     * @param id
     * @return
     */
    public boolean delBed(String id,String userId);


    /**
     * 根据id查询床位，房间及房间内床位数是否存在
     * @param bedId
     * @return
     */
    public boolean bedExistByBedId(String bedId);


    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseBed findById(Object id);


    /**
     * find all model
     *
     * @return all <MainBaseBed
     */
    public List<MainBaseBed> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseBed model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseBed model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseBed model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseBed model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseBed> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseBed> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseBed> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}