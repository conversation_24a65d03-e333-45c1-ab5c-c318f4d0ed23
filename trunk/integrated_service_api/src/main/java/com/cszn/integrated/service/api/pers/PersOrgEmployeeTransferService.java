package com.cszn.integrated.service.api.pers;

import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeTransfer;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgEmployeeTransferService {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrgEmployeeTransfer findById(Object id);


    /**
     * find all model
     *
     * @return all <PersOrg
     */
    public List<PersOrgEmployeeTransfer> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrgEmployeeTransfer model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrgEmployeeTransfer model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrgEmployeeTransfer model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrgEmployeeTransfer model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrgEmployeeTransfer> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrgEmployeeTransfer> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrgEmployeeTransfer> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}
