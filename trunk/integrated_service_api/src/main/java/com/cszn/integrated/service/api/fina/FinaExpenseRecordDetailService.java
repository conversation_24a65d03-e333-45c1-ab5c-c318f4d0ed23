package com.cszn.integrated.service.api.fina;

import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordDetail;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface FinaExpenseRecordDetailService  {

    public List<FinaExpenseRecordDetail> getExpenseRecordDetailListByIdCard(String mainId,String idCard,Date startDate,Date endDate);

    public void allocationIntegral();

    public List<FinaExpenseRecordDetail> findDetailListBySql(String sql,List<Object> params);


    public List<FinaExpenseRecordDetail> getMainRecordAllDetail(String recordId,String startDate,String endDate);

    public Page<Record> cardLockDetail(Integer pageNumber,Integer pageSize,String cardNumber);

    /**
     * 设置消费明细参数
     * @param detail
     * @param expenseId
     * @param cardNumber
     * @param bedId
     * @param startTime
     * @param endTime
     * @param amount
     * @param times
     * @param explain
     * @param status
     * @param createTime
     */
    public void setDetail(FinaExpenseRecordDetail detail, String expenseId, String cardNumber, String bedId, Date startTime,Date endTime,Double amount,Double times,
                          Double points,Double integrals,String explain,String status,Date createTime);


    /**
     * 根据主账单id和日期获取消费明细
     * @param expenseId
     * @param date
     * @return
     */
    public List<FinaExpenseRecordDetail> getDetailsByExpenseId(String expenseId,Date date);

    public FinaExpenseRecordDetail findFirstDetailsByExpenseId(String expenseId);


    /**
     * 入住分段换卡：根据主账单id和日期获取旧消费明细
     * @param expenseId
     * @param beginDate
     * @param endDate
     * @return
     */
    public List<FinaExpenseRecordDetail> getDetailsByExpenseId(String expenseId,String beginDate,String endDate);




    /**
     * 根据主账单id和日期获取旧消费明细
     * @param er
     * @param beginDate
     * @param endDate
     * @return
     */
    public List<FinaExpenseRecordDetail> getDetailsByExpenseId(FinaExpenseRecord er,String beginDate,String endDate,String flag);


    /**
     * 获取本账单最小换卡开始时间
     * @param er
     * @return
     */
    public String getStartTimeByEr(FinaExpenseRecord er);


    /**
     * 根据账单获取未结算消费明细数量
     * @param er
     * @return
     */
    public Long getNotSettleCount(FinaExpenseRecord er);


    /**
     * 获取已结算提示
     * @param er
     * @return
     */
    public String getTipsByIsSettled(FinaExpenseRecord er);


    /**
     * 获取主账单记录id
     * @param er
     * @return
     */
    public FinaExpenseRecord getMainIdByEr(FinaExpenseRecord er);



    /**
     *根据账单id和离开时间查询消费明细
     * @param leaveTime
     * @return
     */
    public List<FinaExpenseRecordDetail> getDetailsByExpenseIdAndLeaveTime(FinaExpenseRecord er,Date leaveTime);


    /**
     * 根据账单和离开时间查询消费明细
     * @param er
     * @param leaveTime
     * @return
     */
    public List<FinaExpenseRecordDetail> getDetailsByErAndLeaveTime(FinaExpenseRecord er,Date leaveTime);



    /**
     * 通过会员卡号和账单获取该卡未结算的总天数或总金额
     * @param cardNumber
     * @return
     */
    public Record getTotalByCardEr(String cardNumber);



    /**
     * 计算截止到今天的明细总和
     * @param expenseId
     * @return
     */
    public Double getDetailSum(String expenseId,String cardNumber);


    /**
     * 判断该账单的附属账单或并列账单的锁定或明细是否使用新卡
     * @param er
     * @return
     */
    public boolean isHaveCardNumberByOther(FinaExpenseRecord er,String cardNumber);



    /**
     * 获取账单记录明细中旧卡已执行未结算的记录
     * @param er
     * @return
     */
    public List<Record> getDetailsByNotSettled(FinaExpenseRecord er);


    /*定时任务begin*/

    /**
     *根据未结算账单获取消费明细
     * 更新明细状态
     * 统计明细实扣
     * @param erList
     * @return
     */
    public Map<String,Object> getDetailByErListNotSettle(List<FinaExpenseRecord> erList);


    /**
     * 设置更新未结算账单的实扣
     * @param valMap
     * @param erList
     */
    public void updateErActual(Map<String, Record> valMap,List<FinaExpenseRecord> erList);


    /**
     * 在账单数据中筛选出主账单list
     * @param erList
     * @return
     */
    public List<FinaExpenseRecord> getMainList(List<FinaExpenseRecord> erList);


    /**
     * 更新明细和账单记录
     * @param fdList
     * @param erList
     * @return
     */
    public boolean updateDetailsAndErList(List<FinaExpenseRecordDetail> fdList,List<FinaExpenseRecord> erList);

    /*定时任务end*/


    /**
     * 转移会员卡升级：根据旧卡账单将账单变更为新卡
     * @param erList
     * @return
     */
    public Map<String,Object> getNewDetailsByOld(List<FinaExpenseRecord> erList, FinaMembershipCard oldCard, FinaMembershipCard newCard, String newFullName,String userId);


    /**
     * 转移会员卡升级：根据账单id和旧会员卡获取未结算的明细
     * @param er
     * @param cardNumber
     * @return
     */
    public List<FinaExpenseRecordDetail> getDetailsByErIdAndCardNumber(FinaExpenseRecord er,String cardNumber);


    /**
     * 转移会员卡升级：通过主账单获取新卡的账单锁定
     * @param mainList
     * @return
     */
    public void getNewCardLockByMainEr(List<FinaExpenseRecord> mainList, FinaMembershipCard oldCard,MainCardDeductScheme newScheme,MainCardDeductScheme newSchemeLong,Map<String,Double> newLockMap);


    /**
     * 转移会员卡升级：根据主账单获取整个账单未结算的明细的数量
     * @param er
     * @return
     */
    public Long getNotSettleNumDetailsBrErs(FinaExpenseRecord er,FinaMembershipCard oldCard);


    /**
     * 判断是否可以换住：根据主账单记录判断是否有多余的锁定
     * @param er
     * @return
     */
    public Map<String,Object> getSurplusLockDetailsByEr(FinaExpenseRecord er,String checkoutDateBefore);




    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public FinaExpenseRecordDetail findById(Object id);


    /**
     * find all model
     *
     * @return all <FinaExpenseRecordDetail
     */
    public List<FinaExpenseRecordDetail> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(FinaExpenseRecordDetail model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(FinaExpenseRecordDetail model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(FinaExpenseRecordDetail model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(FinaExpenseRecordDetail model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<FinaExpenseRecordDetail> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<FinaExpenseRecordDetail> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<FinaExpenseRecordDetail> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}