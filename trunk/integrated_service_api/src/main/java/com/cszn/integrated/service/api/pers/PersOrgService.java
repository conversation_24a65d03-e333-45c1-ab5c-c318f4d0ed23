package com.cszn.integrated.service.api.pers;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.service.entity.common.XmSelect;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgReviewer;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;

import java.util.List;

public interface PersOrgService  {

    public List<PersOrg> findBySql(String sql,List<Object> params);

    public double[] getDeptWorkHour(String deptId);

    public int getDeptFillLeaveMinValue(String deptId);

    public void getAllParent(List<PersOrg> orgList,String id);

    //public void findChildren(List<PersOrg> orgList,String parentId);

    public void findChildren(List<ZTree> zTreeList,List<PersOrg> orgList,String parentId);

    public void findParentOrgs(List<PersOrg> parentOrgs,PersOrg org);

    public String getOrgParentNames(String deptId);

    public PersOrg getBUOrg(String parentId);

    public List<Integer> getDeptQiYeIdByDeptId(JSONArray array);

    public List<ZTree> CustomerServiceSalesOrgTree();
    
    public List<ZTree> allOrgTree();
    
    public List<ZTree> buOrgTree();
    
    public List<ZTree> customerServiceSalesZTree();

    public List<ZTree> organizationZTree();
    
    public List<ZTree> buOrganizationZTree();

    public List<ZTree> permissionZTree(String userId);

    public void findChiOrg(List<PersOrg> allList,List<PersOrg> list);

    public List<PersOrg> findOrgByParentId(String orgId);

    public List<ZTree> userOrgFormTree(final String userId);

    List<PersOrg> getAllOrgList();
    
    /**
     * find all model
     *
     * @return all <XmSelect
     */
    public List<XmSelect> orgXmSelectTree(String orgId);

    /**
     * 获取部门、小组
     * @param orgId
     * @return
     */
    public List<PersOrg> findDepartment(String orgId);

    /**
     * 获取主机构、基地、分公司
     * @return
     */
    public List<PersOrg> findMainOrg();

    /**
     * 通过id获取org
     * @param ids
     * @return
     */
    public List<PersOrg> findByIds(List<String> ids);

    /**
     * 通过员工号获取部门list
     * @param employeeId
     * @return
     */
    public List<PersOrg> findOrgListByEmployeeId(String employeeId);

    public void createQiYeOrg();

    /**
     * 获取子节点
     * @param orgId
     * @return
     */
    public List<PersOrg> findChildrenById(String orgId);
    
    /**
     * 获取子节点
     * @param orgId
     * @return
     */
    public List<PersOrg> orgCurChildrenList(final String orgId);

	/**
     * 查询机构树表格
     * @return
     */
    public List<ZTree> orgTreeTable(final String orgId);

    /**
     * 通过userId获取org树
     * @param userId
     * @return
     */
    public List<ZTree> orgFormTreeByUserId(String userId,String orgType);
    
    /**
     * 机构编辑树数据
     * @return
     */
    public List<ZTree> orgFormTree(final String orgId);
    
    /**
     * 根据机构类型获取机构列表
     * @return
     */
    public List<PersOrg> getOrgListByType(final String parentId, final String orgType);
    
    /**
     * 机构批量排序保存
     * 
     * @param orgList
     * @return
     */
    public boolean sortBatchSave(List<PersOrg> orgList);
    
    /**
     * 机构保存方法(有特殊处理)
     *
     * @param model
     * @return if save or update success
     */
    public boolean orgSave(PersOrg model);
    
    /**
     * 机构审核人保存方法(有特殊处理)
     *
     * @param model
     * @return if save or update success
     */
    public boolean orgReviewerSave(PersOrgReviewer model);
    
    /**
     * 机构删除方法
     *
     * @param
     * @return if save or update success
     */
    public boolean orgDel(final String orgId,final String userId);
	
    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public PersOrg findById(Object id);
	
    /**
     * find model by org type and name	
     *
     * @param orgType
     * @param orgName
     * @return
     */
    public PersOrg findByName(String orgType, String orgName);

    /**
     * find all model
     *
     * @return all <PersOrg
     */
    public List<PersOrg> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(PersOrg model);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(PersOrg model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(PersOrg model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(PersOrg model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<PersOrg> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<PersOrg> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<PersOrg> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}