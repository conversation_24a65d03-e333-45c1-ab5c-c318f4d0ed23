package com.cszn.integrated.service.api.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.MainBaseFoodTag;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;

public interface MainBaseFoodTagService  {

    /**
     * find model by primary key
     *
     * @param id
     * @return
     */
    public MainBaseFoodTag findById(Object id);
    
    
    /**
     * find all model
     *
     * @return all <MainBaseFoodTag
     */
    public List<MainBaseFoodTag> findList(String baseId, String isEnabled);


    /**
     * find all model
     *
     * @return all <MainBaseFoodTag
     */
    public List<MainBaseFoodTag> findAll();


    /**
     * delete model by primary key
     *
     * @param id
     * @return success
     */
    public boolean deleteById(Object id);


    /**
     * delete model
     *
     * @param model
     * @return
     */
    public boolean delete(MainBaseFoodTag model);
    
    
    /**
     * 保存方法
     * 
     * @param model
     * @return
     */
    public boolean saveFoodTag(List<MainBaseFoodTag> foodTagList, String userId);


    /**
     * save model to database
     *
     * @param model
     * @return id value if save success
     */
    public Object save(MainBaseFoodTag model);


    /**
     * save or update model
     *
     * @param model
     * @return id value if save or update success
     */
    public Object saveOrUpdate(MainBaseFoodTag model);


    /**
     * update data model
     *
     * @param model
     * @return
     */
    public boolean update(MainBaseFoodTag model);


    /**
     * page query
     *
     * @param page
     * @param pageSize
     * @return page data
     */
    public Page<MainBaseFoodTag> paginate(int page, int pageSize);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @return page data
     */
    public Page<MainBaseFoodTag> paginateByColumns(int page, int pageSize, Columns columns);


    /**
     * page query by columns
     *
     * @param page
     * @param pageSize
     * @param columns
     * @param orderBy
     * @return page data
     */
    public Page<MainBaseFoodTag> paginateByColumns(int page, int pageSize, Columns columns, String orderBy);


}