package com.cszn.integrated.service.entity.crm;

import com.cszn.integrated.service.entity.enums.RollType;
import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.crm.base.BaseCrmCardRollType;

/**
 * Generated by Jboot.
 */
@Table(tableName = "crm_card_roll_type", primaryKey = "id")
public class CrmCardRollType extends BaseCrmCardRollType<CrmCardRollType> {

    public String getRollTypeEnumName(){
        for (RollType rollType : RollType.values()) {
            if(rollType.getKey().equals(this.getTypeCode())){
                return rollType.getValue();
            }
        }
        return null;
    }

}
