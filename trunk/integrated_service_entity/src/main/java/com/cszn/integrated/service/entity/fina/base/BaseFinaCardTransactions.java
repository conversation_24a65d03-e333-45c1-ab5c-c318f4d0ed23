package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardTransactions<M extends BaseFinaCardTransactions<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setTransactionNo(String transactionNo) {
		set("transaction_no", transactionNo);
	}

	public String getTransactionNo() {
		return getStr("transaction_no");
	}

	public void setBusinessEntityId(String businessEntityId) {
		set("business_entity_id", businessEntityId);
	}

	public String getBusinessEntityId() {
		return getStr("business_entity_id");
	}

	public void setCustomerChannel(String customerChannel) {
		set("customer_channel", customerChannel);
	}

	public String getCustomerChannel() {
		return getStr("customer_channel");
	}

	public void setConsumeType(String consumeType) {
		set("consume_type", consumeType);
	}
	
	public String getConsumeType() {
		return getStr("consume_type");
	}

	public void setType(String type) { set("type", type); }

	public String getType() { return getStr("type"); }

	public void setInOutFlag(String inOutFlag) {
		set("in_out_flag", inOutFlag);
	}
	
	public String getInOutFlag() {
		return getStr("in_out_flag");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setTimes(Double times) {
		set("times", times);
	}
	
	public Double getTimes() {
		return getDouble("times");
	}

	public void setPoints(Double points){
		set("points",points);
	}

	public Double getPoints(){
		return getDouble("points");
	}
	
	public void setIntegrals(Double integrals){
		set("integrals",integrals);
	}
	
	public Double getIntegrals(){
		return getDouble("integrals");
	}
	
	public void setBeanCoupons(Double beanCoupons){
		set("bean_coupons",beanCoupons);
	}
	
	public Double getBeanCoupons(){
		return getDouble("bean_coupons");
	}

	public void setAmountSnapshot(Double amountSnapshot) {
		set("amount_snapshot", amountSnapshot);
	}
	
	public Double getAmountSnapshot() {
		return getDouble("amount_snapshot");
	}

	public void setTimesSnapshot(Double timesSnapshot) {
		set("times_snapshot", timesSnapshot);
	}

	public Double getTimesSnapshot() {
		return getDouble("times_snapshot");
	}

	public void setPointsSnapshot(Double pointsSnapshot) {
		set("points_snapshot", pointsSnapshot);
	}

	public Double getPointsSnapshot() {
		return getDouble("points_snapshot");
	}
	
	public void setIntegralsSnapshot(Double integralsSnapshot) {
		set("integrals_snapshot", integralsSnapshot);
	}
	
	public Double getIntegralsSnapshot() {
		return getDouble("integrals_snapshot");
	}
	
	public void setBeanCouponsSnapshot(Double beanCouponsSnapshot) {
		set("bean_coupons_snapshot", beanCouponsSnapshot);
	}
	
	public Double getBeanCouponsSnapshot() {
		return getDouble("bean_coupons_snapshot");
	}

	public void setConsumeMoney(Double consumeMoney) {
		set("consume_money", consumeMoney);
	}

	public Double getConsumeMoney() {
		return getDouble("consume_money");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setIsHidden(String isHidden) {
		set("is_hidden", isHidden);
	}
	
	public String getIsHidden() {
		return getStr("is_hidden");
	}

	public void setDealTime(java.util.Date dealTime) {
		set("deal_time", dealTime);
	}
	
	public java.util.Date getDealTime() {
		return get("deal_time");
	}

	public void setStartDate(java.util.Date startDate) {
		set("start_date", startDate);
	}

	public java.util.Date getStartDate() {
		return get("start_date");
	}

	public void setEndDate(java.util.Date endDate) {
		set("end_date", endDate);
	}

	public java.util.Date getEndDate() {
		return get("end_date");
	}

}
