package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseBed<M extends BaseMainBaseBed<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRoomId(String roomId) {
		set("room_id", roomId);
	}
	
	public String getRoomId() {
		return getStr("room_id");
	}

	public void setBedNo(String bedNo) {
		set("bed_no", bedNo);
	}
	
	public String getBedNo() {
		return getStr("bed_no");
	}

	public void setBedName(String bedName) {
		set("bed_name", bedName);
	}
	
	public String getBedName() {
		return getStr("bed_name");
	}

	public void setBedTypeId(String bedTypeId) {
		set("bed_type_id", bedTypeId);
	}
	
	public String getBedTypeId() {
		return getStr("bed_type_id");
	}

	public void setBedStatusId(String bedStatusId) {
		set("bed_status_id", bedStatusId);
	}
	
	public String getBedStatusId() {
		return getStr("bed_status_id");
	}

	public void setFileId(String fileId) {
		set("file_id", fileId);
	}
	
	public String getFileId() {
		return getStr("file_id");
	}

	public void setBedImage(String bedImage) {
		set("bed_image", bedImage);
	}
	
	public String getBedImage() {
		return getStr("bed_image");
	}

	public void setMarkupTypeId(String markupTypeId) {
		set("markup_type_id", markupTypeId);
	}
	
	public String getMarkupTypeId() {
		return getStr("markup_type_id");
	}

	public void setMattressTypeId(String mattressTypeId) {
		set("mattress_type_id", mattressTypeId);
	}
	
	public String getMattressTypeId() {
		return getStr("mattress_type_id");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setIsRetain(Integer isRetain) {
		set("is_retain", isRetain);
	}
	
	public Integer getIsRetain() {
		return getInt("is_retain");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
