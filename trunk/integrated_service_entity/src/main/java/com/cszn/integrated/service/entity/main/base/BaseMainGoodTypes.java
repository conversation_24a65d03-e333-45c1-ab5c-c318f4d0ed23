package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainGoodTypes<M extends BaseMainGoodTypes<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setGoodTypeNo(String goodTypeNo) {
		set("good_type_no", goodTypeNo);
	}
	
	public String getGoodTypeNo() {
		return getStr("good_type_no");
	}

	public void setParentId(String parentId) {
		set("parent_id", parentId);
	}
	
	public String getParentId() {
		return getStr("parent_id");
	}

	public void setParentIds(String parentIds){
		set("parent_ids",parentIds);
	}

	public String getParentIds(){
		return getStr("parent_ids");
	}

	public void setUnitTypeId(String unitTypeId) {
		set("unit_type_id", unitTypeId);
	}
	
	public String getUnitTypeId() {
		return getStr("unit_type_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
