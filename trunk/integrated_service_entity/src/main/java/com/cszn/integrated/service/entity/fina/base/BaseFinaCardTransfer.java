package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardTransfer<M extends BaseFinaCardTransfer<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}
	
	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setNewCardId(String newCardId) {
		set("new_card_id", newCardId);
	}
	
	public String getNewCardId() {
		return getStr("new_card_id");
	}
	
	public void setPayAmount(Double payAmount) {
		set("pay_amount", payAmount);
	}
	
	public Double getPayAmount() {
		return getDouble("pay_amount");
	}
	
	public void setGetAmount(Double getAmount) {
		set("get_amount", getAmount);
	}
	
	public Double getGetAmount() {
		return getDouble("get_amount");
	}
	
	public void setGetDays(Double getDays) {
		set("get_days", getDays);
	}
	
	public Double getGetDays() {
		return getDouble("get_days");
	}
	
	public void setTransferFee(Double transferFee) {
		set("transfer_fee", transferFee);
	}
	
	public Double getTransferFee() {
		return getDouble("transfer_fee");
	}
	
	public void setLossFee(Double lossFee) {
		set("loss_fee", lossFee);
	}
	
	public Double getLossFee() {
		return getDouble("loss_fee");
	}

	public void setIsSync(String isSync) {
		set("is_sync", isSync);
	}
	
	public String getIsSync() {
		return getStr("is_sync");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
