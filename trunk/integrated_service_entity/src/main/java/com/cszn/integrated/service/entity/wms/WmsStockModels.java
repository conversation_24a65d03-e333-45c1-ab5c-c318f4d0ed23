package com.cszn.integrated.service.entity.wms;

import com.cszn.integrated.service.entity.wms.base.BaseWmsStockModels;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "wms_stock_models", primaryKey = "id")
public class WmsStockModels extends BaseWmsStockModels<WmsStockModels> {

    private Integer stock;

    public void setStock(Integer stock) {
        this.stock = stock;
    }

    public Integer getStock() {
        return stock;
    }
    
    public String getStockTypeName() {
    	String stockTypeName = "";
    	if(StrKit.notBlank(this.getStockTypeId())) {
    		stockTypeName = Db.queryStr("select name from wms_stock_types where id=?", this.getStockTypeId());
    	}
    	return stockTypeName;
    }
    
    public String getUnitName() {
    	String unitName = "";
    	if(StrKit.notBlank(this.getUnitId())) {
    		unitName = Db.queryStr("select unit_name from wms_unit where id=?", this.getUnitId());
    	}
    	return unitName;
    }
}
