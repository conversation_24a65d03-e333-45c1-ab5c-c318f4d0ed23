package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseSysDataPermission<M extends BaseSysDataPermission<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setParentId(java.lang.String parentId) {
		set("parent_id", parentId);
	}
	
	public java.lang.String getParentId() {
		return getStr("parent_id");
	}

	public void setParentIds(java.lang.String parentIds) {
		set("parent_ids", parentIds);
	}
	
	public java.lang.String getParentIds() {
		return getStr("parent_ids");
	}

	public void setPermissionName(java.lang.String permissionName) {
		set("permission_name", permissionName);
	}
	
	public java.lang.String getPermissionName() {
		return getStr("permission_name");
	}

	public void setBelongSystem(java.lang.String belongSystem) {
		set("belong_system", belongSystem);
	}
	
	public java.lang.String getBelongSystem() {
		return getStr("belong_system");
	}

	public void setPermission(java.lang.String permission) {
		set("permission", permission);
	}
	
	public java.lang.String getPermission() {
		return getStr("permission");
	}

	public void setPermissionSort(java.lang.Integer permissionSort) {
		set("permission_sort", permissionSort);
	}
	
	public java.lang.Integer getPermissionSort() {
		return getInt("permission_sort");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
