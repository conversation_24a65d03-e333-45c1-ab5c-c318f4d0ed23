package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaLottery<M extends BaseFinaLottery<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setLotteryNum(String lotteryNum) {
		set("lottery_num", lotteryNum);
	}
	
	public String getLotteryNum() {
		return getStr("lottery_num");
	}

	public void setLotteryName(String lotteryName) {
		set("lottery_name", lotteryName);
	}
	
	public String getLotteryName() {
		return getStr("lottery_name");
	}

	public void setRechargeAmount(Double rechargeAmount) {
		set("recharge_amount", rechargeAmount);
	}
	
	public Double getRechargeAmount() {
		return getDouble("recharge_amount");
	}
	
	public void setRechargeTimes(Double rechargeTimes) {
		set("recharge_times", rechargeTimes);
	}
	
	public Double getRechargeTimes() {
		return getDouble("recharge_times");
	}
	
	public void setRechargeIntegrals(Double rechargeIntegrals) {
		set("recharge_integrals", rechargeIntegrals);
	}
	
	public Double getRechargeIntegrals() {
		return getDouble("recharge_integrals");
	}

	public void setIsSend(String isSend) {
		set("is_send", isSend);
	}
	
	public String getIsSend() {
		return getStr("is_send");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
