package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;

import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaRefundAccount<M extends BaseFinaRefundAccount<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setApplyId(java.lang.String applyId) {
		set("apply_id", applyId);
	}
	
	public java.lang.String getApplyId() {
		return getStr("apply_id");
	}

	public void setBankId(java.lang.String bankId) {
		set("bank_id", bankId);
	}
	
	public java.lang.String getBankId() {
		return getStr("bank_id");
	}

	public void setSubBranch(java.lang.String subBranch) {
		set("sub_branch", subBranch);
	}
	
	public java.lang.String getSubBranch() {
		return getStr("sub_branch");
	}

	public void setAccountType(java.lang.String accountType) {
		set("account_type", accountType);
	}
	
	public java.lang.String getAccountType() {
		return getStr("account_type");
	}

	public void setBankAccount(java.lang.String bankAccount) {
		set("bank_account", bankAccount);
	}
	
	public java.lang.String getBankAccount() {
		return getStr("bank_account");
	}

	public void setHolderName(java.lang.String holderName) {
		set("holder_name", holderName);
	}
	
	public java.lang.String getHolderName() {
		return getStr("holder_name");
	}

	public void setStayPhoneNum(java.lang.String stayPhoneNum) {
		set("stay_phone_num", stayPhoneNum);
	}
	
	public java.lang.String getStayPhoneNum() {
		return getStr("stay_phone_num");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
