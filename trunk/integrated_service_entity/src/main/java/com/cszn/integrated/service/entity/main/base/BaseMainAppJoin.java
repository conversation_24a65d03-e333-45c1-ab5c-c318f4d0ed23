package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainAppJoin<M extends BaseMainAppJoin<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setAppName(String appName) {
		set("app_name", appName);
	}
	
	public String getAppName() {
		return getStr("app_name");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setAppid(String appid) {
		set("appid", appid);
	}
	
	public String getAppid() {
		return getStr("appid");
	}

	public void setSecret(String secret) {
		set("secret", secret);
	}
	
	public String getSecret() {
		return getStr("secret");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setAppUrl(String appUrl) {
		set("app_url", appUrl);
	}
	
	public String getAppUrl() {
		return getStr("app_url");
	}

	public void setIpAddr(String ipAddr) {
		set("ip_addr", ipAddr);
	}
	
	public String getIpAddr() {
		return getStr("ip_addr");
	}

	public void setRemarks(String remarks) {
		set("remarks", remarks);
	}
	
	public String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
