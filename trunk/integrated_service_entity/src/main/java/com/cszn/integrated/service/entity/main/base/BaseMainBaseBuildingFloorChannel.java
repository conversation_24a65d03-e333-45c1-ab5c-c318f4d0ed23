package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseBuildingFloorChannel<M extends BaseMainBaseBuildingFloorChannel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setJoinId(String joinId) {
		set("join_id", joinId);
	}
	
	public String getJoinId() {
		return getStr("join_id");
	}

	public void setChannelId(String channelId) {
		set("channel_id", channelId);
	}
	
	public String getChannelId() {
		return getStr("channel_id");
	}

}
