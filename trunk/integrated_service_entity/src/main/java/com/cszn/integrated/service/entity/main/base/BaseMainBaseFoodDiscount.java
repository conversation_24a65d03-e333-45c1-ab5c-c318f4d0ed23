package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseFoodDiscount<M extends BaseMainBaseFoodDiscount<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setFoodId(java.lang.String foodId) {
		set("food_id", foodId);
	}
	
	public java.lang.String getFoodId() {
		return getStr("food_id");
	}

	public void setDiscountType(java.lang.String discountType) {
		set("discount_type", discountType);
	}
	
	public java.lang.String getDiscountType() {
		return getStr("discount_type");
	}

	public void setDiscountValue(java.lang.String discountValue) {
		set("discount_value", discountValue);
	}
	
	public java.lang.String getDiscountValue() {
		return getStr("discount_value");
	}

	public void setStartDate(java.util.Date startDate) {
		set("start_date", startDate);
	}
	
	public java.util.Date getStartDate() {
		return get("start_date");
	}

	public void setEndDate(java.util.Date endDate) {
		set("end_date", endDate);
	}
	
	public java.util.Date getEndDate() {
		return get("end_date");
	}

	public void setIsEnabled(java.lang.String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public java.lang.String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
