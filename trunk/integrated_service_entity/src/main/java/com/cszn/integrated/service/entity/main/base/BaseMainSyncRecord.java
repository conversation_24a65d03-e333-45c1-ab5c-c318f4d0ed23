package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainSyncRecord<M extends BaseMainSyncRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setSyncType(String syncType) {
		set("sync_type", syncType);
	}
	
	public String getSyncType() {
		return getStr("sync_type");
	}

	public void setDataType(String dataType) {
		set("data_type", dataType);
	}
	
	public String getDataType() {
		return getStr("data_type");
	}

	public void setSyncUrl(String syncUrl) {
		set("sync_url", syncUrl);
	}
	
	public String getSyncUrl() {
		return getStr("sync_url");
	}

	public void setSyncData(String syncData) {
		set("sync_data", syncData);
	}
	
	public String getSyncData() {
		return getStr("sync_data");
	}

	public void setSyncStatus(String syncStatus) {
		set("sync_status", syncStatus);
	}
	
	public String getSyncStatus() {
		return getStr("sync_status");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
