package com.cszn.integrated.service.entity.fina;

import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.fina.base.BaseFinaExpenseRecord;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_expense_record", primaryKey = "id")
public class FinaExpenseRecord extends BaseFinaExpenseRecord<FinaExpenseRecord> {
    private String followDeductType;

    public void setFollowDeductType(String followDeductType) {
        this.followDeductType = followDeductType;
    }

    public String getFollowDeductType() {
        return followDeductType;
    }
}
