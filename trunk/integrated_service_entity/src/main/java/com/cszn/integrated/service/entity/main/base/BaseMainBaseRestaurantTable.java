package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseRestaurantTable<M extends BaseMainBaseRestaurantTable<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRestaurantId(java.lang.String restaurantId) {
		set("restaurant_id", restaurantId);
	}
	
	public java.lang.String getRestaurantId() {
		return getStr("restaurant_id");
	}

	public void setTableNo(java.lang.Integer tableNo) {
		set("table_no", tableNo);
	}
	
	public java.lang.Integer getTableNo() {
		return getInt("table_no");
	}

	public void setTableName(java.lang.String tableName) {
		set("table_name", tableName);
	}
	
	public java.lang.String getTableName() {
		return getStr("table_name");
	}

	public void setPeopleNum(java.lang.Integer peopleNum) {
		set("people_num", peopleNum);
	}
	
	public java.lang.Integer getPeopleNum() {
		return getInt("people_num");
	}

	public void setIsBooking(java.lang.String isBooking) {
		set("is_booking", isBooking);
	}
	
	public java.lang.String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsEnabled(java.lang.String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public java.lang.String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setTableStatus(java.lang.String tableStatus) {
		set("table_status", tableStatus);
	}
	
	public java.lang.String getTableStatus() {
		return getStr("table_status");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
