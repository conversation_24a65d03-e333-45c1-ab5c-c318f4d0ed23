package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaConsumeResult<M extends BaseFinaConsumeResult<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setConsumeNo(String consumeNo) {
		set("consume_no", consumeNo);
	}
	
	public String getConsumeNo() {
		return getStr("consume_no");
	}

	public void setPushStatus(String pushStatus) {
		set("push_status", pushStatus);
	}
	
	public String getPushStatus() {
		return getStr("push_status");
	}

	public void setSettleTime(java.util.Date settleTime) {
		set("settle_time", settleTime);
	}
	
	public java.util.Date getSettleTime() {
		return get("settle_time");
	}

}
