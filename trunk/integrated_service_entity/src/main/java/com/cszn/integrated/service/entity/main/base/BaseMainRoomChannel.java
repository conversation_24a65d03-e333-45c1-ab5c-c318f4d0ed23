package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainRoomChannel<M extends BaseMainRoomChannel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRoomId(String roomId) {
		set("room_id", roomId);
	}
	
	public String getRoomId() {
		return getStr("room_id");
	}

	public void setChannelId(String channelId) {
		set("channel_id", channelId);
	}
	
	public String getChannelId() {
		return getStr("channel_id");
	}

}
