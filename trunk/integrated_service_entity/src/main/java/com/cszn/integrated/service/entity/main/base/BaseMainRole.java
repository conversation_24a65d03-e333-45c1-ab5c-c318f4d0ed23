package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainRole<M extends BaseMainRole<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRoleName(String roleName) {
		set("role_name", roleName);
	}
	
	public String getRoleName() {
		return getStr("role_name");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setLv(String lv) {
		set("lv", lv);
	}

	public String getLv() {
		return getStr("lv");
	}

	public void setIsGroupManager(String isGroupManager) {
		set("is_group_manager", isGroupManager);
	}
	
	public String getIsGroupManager() {
		return getStr("is_group_manager");
	}

	public void setIsBaseManager(String isBaseManager) {
		set("is_base_manager", isBaseManager);
	}
	
	public String getIsBaseManager() {
		return getStr("is_base_manager");
	}

	public void setIsMarketManager(String isMarketManager) {
		set("is_market_manager", isMarketManager);
	}
	
	public String getIsMarketManager() {
		return getStr("is_market_manager");
	}

	public void setIsCateringStaff(String isCateringStaff) {
		set("is_catering_staff", isCateringStaff);
	}
	
	public String getIsCateringStaff() {
		return getStr("is_catering_staff");
	}

	public void setIsFinanceStaff(String isFinanceStaff) {
		set("is_finance_staff", isFinanceStaff);
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}

	public String getRemark() {
		return getStr("remark");
	}

	public String getIsFinanceStaff() {
		return getStr("is_finance_staff");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
