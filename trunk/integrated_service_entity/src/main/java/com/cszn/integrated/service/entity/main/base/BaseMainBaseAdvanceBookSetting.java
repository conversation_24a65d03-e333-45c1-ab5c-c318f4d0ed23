package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseAdvanceBookSetting<M extends BaseMainBaseAdvanceBookSetting<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setStartDate(String startDate) {
		set("start_date", startDate);
	}
	
	public String getStartDate() {
		return getStr("start_date");
	}

	public void setEndDate(String endDate) {
		set("end_date", endDate);
	}
	
	public String getEndDate() {
		return getStr("end_date");
	}

	public void setCheckedAdvance(Integer checkedAdvance) {
		set("checked_advance", checkedAdvance);
	}
	
	public Integer getCheckedAdvance() {
		return getInt("checked_advance");
	}

	public void setEmptyAdvance(Integer emptyAdvance) {
		set("empty_advance", emptyAdvance);
	}
	
	public Integer getEmptyAdvance() {
		return getInt("empty_advance");
	}

	public void setNotEmptyAdvance(Integer notEmptyAdvance) {
		set("not_empty_advance", notEmptyAdvance);
	}
	
	public Integer getNotEmptyAdvance() {
		return getInt("not_empty_advance");
	}

	public void setPeakPeriod(String peakPeriod) {
		set("peak_period", peakPeriod);
	}
	
	public String getPeakPeriod() {
		return getStr("peak_period");
	}

	public void setRoomDelayCost(Double roomDelayCost) {
		set("room_delay_cost", roomDelayCost);
	}

	public Double getRoomDelayCost() {
		return getDouble("room_delay_cost");
	}

	public void setRoomPlanRule(String roomPlanRule) {
		set("room_plan_rule", roomPlanRule);
	}

	public String getRoomPlanRule() {
		return getStr("room_plan_rule");
	}

	public void setIsSendCheckinProve(String isSendCheckinProve) {
		set("is_send_checkin_prove", isSendCheckinProve);
	}
	
	public String getIsSendCheckinProve() {
		return getStr("is_send_checkin_prove");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
