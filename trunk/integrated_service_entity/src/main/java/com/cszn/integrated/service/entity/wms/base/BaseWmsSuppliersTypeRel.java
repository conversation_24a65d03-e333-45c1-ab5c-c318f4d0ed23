package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsSuppliersTypeRel<M extends BaseWmsSuppliersTypeRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSupplierId(String supplierId) {
		set("supplier_id", supplierId);
	}
	
	public String getSupplierId() {
		return getStr("supplier_id");
	}
	
	public void setStockTypeId(String stockTypeId) {
		set("stock_type_id", stockTypeId);
	}
	
	public String getStockTypeId() {
		return getStr("stock_type_id");
	}

}
