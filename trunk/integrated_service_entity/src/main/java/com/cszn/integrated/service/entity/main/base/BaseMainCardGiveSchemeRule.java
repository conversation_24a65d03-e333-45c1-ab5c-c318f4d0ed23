package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainCardGiveSchemeRule<M extends BaseMainCardGiveSchemeRule<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSchemeId(String schemeId) {
		set("scheme_id", schemeId);
	}
	
	public String getSchemeId() {
		return getStr("scheme_id");
	}

	public void setCycle(String cycle) {
		set("cycle", cycle);
	}
	
	public String getCycle() {
		return getStr("cycle");
	}
	
	public void setStartSeq(Integer startSeq) {
		set("start_seq", startSeq);
	}
	
	public Integer getStartSeq() {
		return getInt("start_seq");
	}
	
	public void setEndSeq(Integer endSeq) {
		set("end_seq", endSeq);
	}
	
	public Integer getEndSeq() {
		return getInt("end_seq");
	}
	
	public void setGiveTimes(Double giveTimes) {
		set("give_times", giveTimes);
	}
	
	public Double getGiveTimes() {
		return getDouble("give_times");
	}
	
	public void setGiveAmount(Double giveAmount) {
		set("give_amount", giveAmount);
	}
	
	public Double getGiveAmount() {
		return getDouble("give_amount");
	}

	public void setGiveIntegrals(Double giveIntegrals) {
		set("give_integrals", giveIntegrals);
	}

	public Double getGiveIntegrals() {
		return getDouble("give_integrals");
	}

	public void setDescribe(java.lang.String describe) {
		set("describe", describe);
	}
	
	public java.lang.String getDescribe() {
		return getStr("describe");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}

	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
