package com.cszn.integrated.service.entity.crm;

import com.cszn.integrated.service.entity.crm.base.BaseCrmCardRoll;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "crm_card_roll", primaryKey = "id")
public class CrmCardRoll extends BaseCrmCardRoll<CrmCardRoll> {
	
	public String getTypeName() {
		return Db.queryStr("select type_name from crm_card_roll_type where id=?", this.getRollTypeId());
	}

	public String getTypeCode() {

		return Db.queryStr("select type_code from crm_card_roll_type where id=?", this.getRollTypeId());
	}
}
