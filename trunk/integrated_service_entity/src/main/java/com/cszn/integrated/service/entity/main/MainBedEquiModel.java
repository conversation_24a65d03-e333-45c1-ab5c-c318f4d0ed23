package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.equi.EquiModel;
import com.jfinal.aop.Inject;
import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.main.base.BaseMainBedEquiModel;

import java.util.List;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_bed_equi_model", primaryKey = "id")
public class MainBedEquiModel extends BaseMainBedEquiModel<MainBedEquiModel> {

    private List<EquiModel> equiModelList;

    public List<EquiModel> getEquiModelList(){
        return this.equiModelList;
    }

    public void setEquiModelList(List<EquiModel> equiModelList){
        this.equiModelList=equiModelList;
    }

    
}
