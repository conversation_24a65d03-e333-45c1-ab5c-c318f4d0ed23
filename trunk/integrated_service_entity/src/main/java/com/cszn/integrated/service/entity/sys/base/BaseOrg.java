package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseOrg<M extends BaseOrg<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setParentId(java.lang.String parentId) {
		set("parent_id", parentId);
	}
	
	public java.lang.String getParentId() {
		return getStr("parent_id");
	}

	public void setParentIds(java.lang.String parentIds) {
		set("parent_ids", parentIds);
	}
	
	public java.lang.String getParentIds() {
		return getStr("parent_ids");
	}

	public void setOrgName(java.lang.String orgName) {
		set("org_name", orgName);
	}
	
	public java.lang.String getOrgName() {
		return getStr("org_name");
	}

	public void setOrgType(java.lang.String orgType) {
		set("org_type", orgType);
	}
	
	public java.lang.String getOrgType() {
		return getStr("org_type");
	}

	public void setFax(java.lang.String fax) {
		set("fax", fax);
	}
	
	public java.lang.String getFax() {
		return getStr("fax");
	}

	public void setEmail(java.lang.String email) {
		set("email", email);
	}
	
	public java.lang.String getEmail() {
		return getStr("email");
	}

	public void setPostalCode(java.lang.String postalCode) {
		set("postal_code", postalCode);
	}
	
	public java.lang.String getPostalCode() {
		return getStr("postal_code");
	}

	public void setLinkMan(java.lang.String linkMan) {
		set("link_man", linkMan);
	}
	
	public java.lang.String getLinkMan() {
		return getStr("link_man");
	}

	public void setLinkPhone(java.lang.String linkPhone) {
		set("link_phone", linkPhone);
	}
	
	public java.lang.String getLinkPhone() {
		return getStr("link_phone");
	}

	public void setProvinceId(java.lang.String provinceId) {
		set("province_id", provinceId);
	}
	
	public java.lang.String getProvinceId() {
		return getStr("province_id");
	}

	public void setCityId(java.lang.String cityId) {
		set("city_id", cityId);
	}
	
	public java.lang.String getCityId() {
		return getStr("city_id");
	}

	public void setTownId(java.lang.String townId) {
		set("town_id", townId);
	}
	
	public java.lang.String getTownId() {
		return getStr("town_id");
	}

	public void setStreetId(java.lang.String streetId) {
		set("street_id", streetId);
	}
	
	public java.lang.String getStreetId() {
		return getStr("street_id");
	}

	public void setAddress(java.lang.String address) {
		set("address", address);
	}
	
	public java.lang.String getAddress() {
		return getStr("address");
	}

	public void setOrgSort(java.lang.Integer orgSort) {
		set("org_sort", orgSort);
	}
	
	public java.lang.Integer getOrgSort() {
		return getInt("org_sort");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
