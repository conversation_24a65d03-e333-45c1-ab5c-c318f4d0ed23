package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaExpenseRecordTouristSettleDetail<M extends BaseFinaExpenseRecordTouristSettleDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setWithholdTimes(Double withholdTimes) {
		set("withhold_times", withholdTimes);
	}
	
	public Double getWithholdTimes() {
		return getDouble("withhold_times");
	}

	public void setActualTimes(Double actualTimes) {
		set("actual_times", actualTimes);
	}
	
	public Double getActualTimes() {
		return getDouble("actual_times");
	}

	public void setWithholdAmount(Double withholdAmount) {
		set("withhold_amount", withholdAmount);
	}
	
	public Double getWithholdAmount() {
		return getDouble("withhold_amount");
	}

	public void setActualAmount(Double actualAmount) {
		set("actual_amount", actualAmount);
	}
	
	public Double getActualAmount() {
		return getDouble("actual_amount");
	}

	public void setWithholdIntegrals(Double withholdIntegrals) {
		set("withhold_integrals", withholdIntegrals);
	}
	
	public Double getWithholdIntegrals() {
		return getDouble("withhold_integrals");
	}

	public void setActualIntegrals(Double actualIntegrals) {
		set("actual_integrals", actualIntegrals);
	}
	
	public Double getActualIntegrals() {
		return getDouble("actual_integrals");
	}

	public void setIsSettle(String isSettle) {
		set("is_settle", isSettle);
	}
	
	public String getIsSettle() {
		return getStr("is_settle");
	}

	public void setFinanceRemark(String financeRemark) {
		set("finance_remark", financeRemark);
	}

	public String getFinanceRemark() {
		return getStr("finance_remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
