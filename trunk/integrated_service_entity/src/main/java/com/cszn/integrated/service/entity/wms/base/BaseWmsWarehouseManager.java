package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

public abstract class BaseWmsWarehouseManager<M extends BaseWmsWarehouseManager<M>> extends JbootModel<M> implements IBean{

    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

    public void setIsReceiver(String isReceiver) {
        set("is_receiver", isReceiver);
    }

    public String getIsReceiver() {
        return getStr("is_receiver");
    }
    
    public void setMealReceiver(String mealReceiver) {
    	set("meal_receiver", mealReceiver);
    }
    
    public String getMealReceiver() {
    	return getStr("meal_receiver");
    }

    public void setWarehouseId(String warehouseId) {
        set("warehouse_id", warehouseId);
    }

    public String getWarehouseId() {
        return getStr("warehouse_id");
    }

    public void setUserId(String userId) {
        set("user_id", userId);
    }

    public String getUserId() {
        return getStr("user_id");
    }

}
