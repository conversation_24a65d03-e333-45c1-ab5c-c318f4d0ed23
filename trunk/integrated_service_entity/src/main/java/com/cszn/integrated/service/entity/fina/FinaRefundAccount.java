package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaRefundAccount;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_refund_account", primaryKey = "id")
public class FinaRefundAccount extends BaseFinaRefundAccount<FinaRefundAccount> {
	
	public String getBankName() {
		return Db.queryStr("select bank_name from main_bank where del_flag='0' and id=?", this.getBankId());
	}
	
	public String getAccountTypeName() {
		return Db.queryStr("select dict_name from sys_dict where del_flag='0' and dict_type='account_type' and dict_value=?", this.getAccountType());
	}
}
