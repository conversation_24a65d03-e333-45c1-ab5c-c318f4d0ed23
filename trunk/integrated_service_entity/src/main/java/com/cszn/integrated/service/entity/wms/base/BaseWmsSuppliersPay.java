package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsSuppliersPay<M extends BaseWmsSuppliersPay<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSupplierId(String supplierId) {
		set("supplier_id", supplierId);
	}
	
	public String getSupplierId() {
		return getStr("supplier_id");
	}

	public void setPaymentWay(String paymentWay) {
		set("payment_way", paymentWay);
	}
	
	public String getPaymentWay() {
		return getStr("payment_way");
	}

	public void setPayAccount(String payAccount) {
		set("pay_account", payAccount);
	}
	
	public String getPayAccount() {
		return getStr("pay_account");
	}

	public void setBankAccountName(String bankAccountName) {
		set("bank_account_name", bankAccountName);
	}

	public String getBankAccountName() {
		return getStr("bank_account_name");
	}

	public void setBankType(String bankType) {
		set("bank_type", bankType);
	}
	
	public String getBankType() {
		return getStr("bank_type");
	}

	public void setBankSource(String bankSource) {
		set("bank_source", bankSource);
	}
	
	public String getBankSource() {
		return getStr("bank_source");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
