package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMallActivityWarehouseRel<M extends BaseMallActivityWarehouseRel<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setActivityId(java.lang.String activityId) {
		set("activity_id", activityId);
	}
	
	public java.lang.String getActivityId() {
		return getStr("activity_id");
	}

	public void setWarehouseId(java.lang.String warehouseId) {
		set("warehouse_id", warehouseId);
	}
	
	public java.lang.String getWarehouseId() {
		return getStr("warehouse_id");
	}

}
