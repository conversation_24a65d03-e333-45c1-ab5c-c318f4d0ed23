package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaMembershipCard;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_membership_card", primaryKey = "id")
public class FinaMembershipCard extends BaseFinaMembershipCard<FinaMembershipCard> {

    public Double getAllBeanCoupons(){
        Double beanCoupons= Db.queryDouble("select SUM(balance_roll_value) totalBalanceRollValue from fina_card_roll a " +
            " INNER JOIN crm_card_roll_record b on a.roll_id=b.id "
            + "where b.del_flag='0' and b.is_enable='0' and a.card_id=? ",this.getId());
        if(beanCoupons==null){
            beanCoupons=0.0;
        }
        return beanCoupons;
    }

}
