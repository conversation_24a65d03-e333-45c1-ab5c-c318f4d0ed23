package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by <PERSON>boo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainMallProductTypeRel<M extends BaseMainMallProductTypeRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTypeId(String typeId) {
		set("type_id", typeId);
	}
	
	public String getTypeId() {
		return getStr("type_id");
	}

	public void setProductId(String productId) {
		set("product_id", productId);
	}
	
	public String getProductId() {
		return getStr("product_id");
	}

	public void setIsSell(String isSell) {
		set("is_sell", isSell);
	}

	public String getIsSell() {
		return getStr("is_sell");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}

	public Integer getSort() {
		return get("sort");
	}

}
