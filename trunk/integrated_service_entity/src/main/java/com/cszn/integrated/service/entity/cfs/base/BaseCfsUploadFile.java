package com.cszn.integrated.service.entity.cfs.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCfsUploadFile<M extends BaseCfsUploadFile<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setBucket(java.lang.String bucket) {
		set("bucket", bucket);
	}
	
	public java.lang.String getBucket() {
		return getStr("bucket");
	}

	public void setOldName(java.lang.String oldName) {
		set("old_name", oldName);
	}

	public java.lang.String getOldName() {
		return getStr("old_name");
	}

	public void setName(java.lang.String name) {
		set("name", name);
	}
	
	public java.lang.String getName() {
		return getStr("name");
	}

	public void setPath(java.lang.String path) {
		set("path", path);
	}
	
	public java.lang.String getPath() {
		return getStr("path");
	}

	public void setSrc(java.lang.String src) {
		set("src", src);
	}
	
	public java.lang.String getSrc() {
		return getStr("src");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
