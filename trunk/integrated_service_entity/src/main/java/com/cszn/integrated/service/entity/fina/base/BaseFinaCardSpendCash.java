package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardSpendCash<M extends BaseFinaCardSpendCash<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}
	
	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}
	
	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}
	
	public void setCardTypeId(String cardTypeId) {
		set("card_type_id", cardTypeId);
	}
	
	public String getCardTypeId() {
		return getStr("card_type_id");
	}
	
	public void setSpendPrice(Double spendPrice) {
		set("spend_price", spendPrice);
	}
	
	public Double getSpendPrice() {
		return getDouble("spend_price");
	}
	
	public void setSpendAmount(Double spendAmount) {
		set("spend_amount", spendAmount);
	}
	
	public Double getSpendAmount() {
		return getDouble("spend_amount");
	}
	
	public void setSpendDays(Double spendDays) {
		set("spend_days", spendDays);
	}
	
	public Double getSpendDays() {
		return getDouble("spend_days");
	}
	
	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
