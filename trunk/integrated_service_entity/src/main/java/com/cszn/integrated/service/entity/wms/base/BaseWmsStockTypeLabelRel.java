package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockTypeLabelRel<M extends BaseWmsStockTypeLabelRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTypeId(String typeId) {
		set("type_id", typeId);
	}
	
	public String getTypeId() {
		return getStr("type_id");
	}

	public void setLabelId(String labelId) {
		set("label_id", labelId);
	}
	
	public String getLabelId() {
		return getStr("label_id");
	}

}
