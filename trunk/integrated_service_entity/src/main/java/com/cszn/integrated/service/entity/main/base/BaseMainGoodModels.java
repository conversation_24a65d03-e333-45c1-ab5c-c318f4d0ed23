package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainGoodModels<M extends BaseMainGoodModels<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setGoodModelNo(String goodModelNo) {
		set("good_model_no", goodModelNo);
	}
	
	public String getGoodModelNo() {
		return getStr("good_model_no");
	}

	public void setGoodTypeId(String goodTypeId) {
		set("good_type_id", goodTypeId);
	}
	
	public String getGoodTypeId() {
		return getStr("good_type_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setUnitId(String unitId) {
		set("unit_id", unitId);
	}
	
	public String getUnitId() {
		return getStr("unit_id");
	}

	public void setSalePrice(Double salePrice) {
		set("sale_price", salePrice);
	}
	
	public Double getSalePrice() {
		return getDouble("sale_price");
	}

	public void setCostPrice(Double costPrice) {
		set("cost_price", costPrice);
	}
	
	public Double getCostPrice() {
		return getDouble("cost_price");
	}

	public void setIsBuckleCard(String isBuckleCard) {
		set("is_buckle_card", isBuckleCard);
	}

	public String getIsBuckleCard() {
		return getStr("is_buckle_card");
	}

	public void setCardTimes(String cardTimes) {
		set("card_times", cardTimes);
	}

	public String getCardTimes() {
		return getStr("card_times");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
