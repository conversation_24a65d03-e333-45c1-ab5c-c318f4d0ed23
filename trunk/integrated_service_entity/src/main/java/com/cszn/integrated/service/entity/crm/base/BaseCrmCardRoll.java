package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

import java.util.Date;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRoll<M extends BaseCrmCardRoll<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRollTypeId(java.lang.String rollTypeId) {
		set("roll_type_id", rollTypeId);
	}
	
	public java.lang.String getRollTypeId() {
		return getStr("roll_type_id");
	}

	public void setRollCode(java.lang.String rollCode) {
		set("roll_code", rollCode);
	}
	
	public java.lang.String getRollCode() {
		return getStr("roll_code");
	}

	public void setRollCodeSuffix(java.lang.String rollCodeSuffix) {
		set("roll_code_suffix", rollCodeSuffix);
	}

	public java.lang.String getRollCodeSuffix() {
		return getStr("roll_code_suffix");
	}

	public void setRollName(java.lang.String rollName) {
		set("roll_name", rollName);
	}
	
	public java.lang.String getRollName() {
		return getStr("roll_name");
	}

	public void setAddRecords(java.lang.String addRecords) {
		set("add_records", addRecords);
	}

	public java.lang.String getAddRecords() {
		return getStr("add_records");
	}

	public void setRollQuantity(java.lang.Integer rollQuantity) {
		set("roll_quantity", rollQuantity);
	}
	
	public java.lang.Integer getRollQuantity() {
		return getInt("roll_quantity");
	}

	public void setValidType(java.lang.String validType) {
		set("valid_type", validType);
	}

	public java.lang.String getValidType() {
		return getStr("valid_type");
	}

	public void setValidDays(java.lang.Integer validDays) {
		set("valid_days", validDays);
	}

	public java.lang.Integer getValidDays() {
		return getInt("valid_days");
	}

	public void setFinalValidDate(Date finalValidDate) {
		set("final_valid_date", finalValidDate);
	}

	public Date getFinalValidDate() {
		return get("final_valid_date");
	}

	public void setValidStartDate(java.util.Date validStartDate) {
		set("valid_start_date", validStartDate);
	}
	
	public java.util.Date getValidStartDate() {
		return get("valid_start_date");
	}

	public void setValidEndDate(java.util.Date validEndDate) {
		set("valid_end_date", validEndDate);
	}
	
	public java.util.Date getValidEndDate() {
		return get("valid_end_date");
	}

	public void setRollValue(Double rollValue) {
		set("roll_value", rollValue);
	}

	public Double getRollValue() {
		return get("roll_value");
	}

	public void setUseTimes(java.lang.String useTimes) {
		set("use_times", useTimes);
	}

	public java.lang.String getUseTimes() {
		return getStr("use_times");
	}
	
	public void setApplyTime(java.util.Date applyTime) {
		set("apply_time", applyTime);
	}
	
	public java.util.Date getApplyTime() {
		return get("apply_time");
	}
	
	public void setApplyUserId(java.lang.String applyUserId) {
		set("apply_user_id", applyUserId);
	}
	
	public java.lang.String getApplyUserId() {
		return getStr("apply_user_id");
	}
	
	public void setApplyName(java.lang.String applyName) {
		set("apply_name", applyName);
	}
	
	public java.lang.String getApplyName() {
		return getStr("apply_name");
	}
	
	public void setApplyOrgId(java.lang.String applyOrgId) {
		set("apply_org_id", applyOrgId);
	}
	
	public java.lang.String getApplyOrgId() {
		return getStr("apply_org_id");
	}
	
	public void setApplyOrgName(java.lang.String applyOrgName) {
		set("apply_org_name", applyOrgName);
	}
	
	public java.lang.String getApplyOrgName() {
		return getStr("apply_org_name");
	}

	public void setIsEnable(java.lang.String isEnable) {
		set("is_enable", isEnable);
	}
	
	public java.lang.String getIsEnable() {
		return getStr("is_enable");
	}

	public void setDeployStatus(java.lang.String deployStatus) {
		set("deploy_status", deployStatus);
	}
	
	public java.lang.String getDeployStatus() {
		return getStr("deploy_status");
	}

	public void setDeployTime(java.util.Date deployTime) {
		set("deploy_time", deployTime);
	}
	
	public java.util.Date getDeployTime() {
		return get("deploy_time");
	}

	public void setIsAstrictBind(java.lang.String isAstrictBind) {
		set("is_astrict_bind", isAstrictBind);
	}

	public java.lang.String getIsAstrictBind() {
		return getStr("is_astrict_bind");
	}

	public void setIsBingUse(java.lang.String isBingUse) {
		set("is_bing_use", isBingUse);
	}

	public java.lang.String getIsBingUse() {
		return getStr("is_bing_use");
	}

	public void setMaxBindTime(java.util.Date maxBindTime) {
		set("max_bind_time", maxBindTime);
	}

	public java.util.Date getMaxBindTime() {
		return get("max_bind_time");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCardCouponApplyId(java.lang.String cardCouponApplyId) {
		set("card_coupon_apply_id", cardCouponApplyId);
	}

	public java.lang.String getCardCouponApplyId() {
		return getStr("card_coupon_apply_id");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
