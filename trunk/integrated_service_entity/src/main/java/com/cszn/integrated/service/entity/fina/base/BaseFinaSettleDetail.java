package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaSettleDetail<M extends BaseFinaSettleDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setStartTime(java.util.Date startTime) {
		set("start_time", startTime);
	}
	
	public java.util.Date getStartTime() {
		return get("start_time");
	}

	public void setEndTime(java.util.Date endTime) {
		set("end_time", endTime);
	}
	
	public java.util.Date getEndTime() {
		return get("end_time");
	}

	public void setTotalTimes(Double totalTimes) {
		set("total_times", totalTimes);
	}
	
	public Double getTotalTimes() {
		return getDouble("total_times");
	}

	public void setTotalAmount(Double totalAmount) {
		set("total_amount", totalAmount);
	}
	
	public Double getTotalAmount() {
		return getDouble("total_amount");
	}

	public void setTotalPoints(Double totalPoints) {
		set("total_points", totalPoints);
	}

	public Double getTotalPoints() {
		return getDouble("total_points");
	}

	public void setTotalIntegrals(Double totalIntegrals) {
		set("total_integrals", totalIntegrals);
	}

	public Double getTotalIntegrals() {
		return getDouble("total_integrals");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setSettleType(String settleType) {
		set("settle_type", settleType);
	}
	
	public String getSettleType() {
		return getStr("settle_type");
	}

	public void setSettleStatus(String settleStatus) {
		set("settle_status", settleStatus);
	}
	
	public String getSettleStatus() {
		return getStr("settle_status");
	}

	public void setActualTimes(Double actualTimes) {
		set("actual_times", actualTimes);
	}
	
	public Double getActualTimes() {
		return getDouble("actual_times");
	}

	public void setActualAmount(Double actualAmount) {
		set("actual_amount", actualAmount);
	}
	
	public Double getActualAmount() {
		return getDouble("actual_amount");
	}

	public void setActualPoints(Double actualPoints) {
		set("actual_points", actualPoints);
	}

	public Double getActualPoints() {
		return getDouble("actual_points");
	}

	public void setActualIntegrals(Double actualIntegrals) {
		set("actual_integrals", actualIntegrals);
	}

	public Double getActualIntegrals() {
		return getDouble("actual_integrals");
	}

	public void setSettleRemark(String settleRemark) {
		set("settle_remark", settleRemark);
	}

	public String getSettleRemark() {
		return getStr("settle_remark");
	}

	public void setYearMonth(String yearMonth){
		set("year_month",yearMonth);
	}

	public String getYearMonth(){
		return get("year_month");
	}

	public void setIsSendSms(String isSendSms){
		set("is_send_sms",isSendSms);
	}

	public String getIsSendSms(){
		return get("is_send_sms");
	}

	public void setSmsContent(String smsContent){
		set("sms_content",smsContent);
	}

	public String getSmsContent(){
		return get("sms_content");
	}

	public void setSettleTime(java.util.Date settleTime) {
		set("settle_time", settleTime);
	}

	public java.util.Date getSettleTime() {
		return get("settle_time");
	}

	public void setSendSmsTime(java.util.Date sendSmsTime) {
		set("send_sms_time", sendSmsTime);
	}

	public java.util.Date getSendSmsTime() {
		return get("send_sms_time");
	}
}
