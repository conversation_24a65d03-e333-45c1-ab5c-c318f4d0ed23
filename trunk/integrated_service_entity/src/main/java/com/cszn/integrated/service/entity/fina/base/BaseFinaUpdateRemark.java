package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaUpdateRemark<M extends BaseFinaUpdateRemark<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBeforeRemark(String beforeRemark) {
		set("before_remark", beforeRemark);
	}
	
	public String getBeforeRemark() {
		return getStr("before_remark");
	}

	public void setRelationId(String relationId) {
		set("relation_id", relationId);
	}
	
	public String getRelationId() {
		return getStr("relation_id");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
