package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardTransactionsDetail<M extends BaseFinaCardTransactionsDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTransactionsId(String transactionsId) {
		set("transactions_id", transactionsId);
	}
	
	public String getTransactionsId() {
		return getStr("transactions_id");
	}

	public void setDeductType(String deductType) {
		set("deduct_type", deductType);
	}
	
	public String getDeductType() {
		return getStr("deduct_type");
	}

	public void setSettleType(String settleType) {
		set("settle_type", settleType);
	}
	
	public String getSettleType() {
		return getStr("settle_type");
	}

	public void setDeductValue(Double deductValue) {
		set("deduct_value", deductValue);
	}

	public Double getDeductValue() {
		return getDouble("deduct_value");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
