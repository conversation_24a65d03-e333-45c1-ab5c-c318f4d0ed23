package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaInvoice<M extends BaseFinaInvoice<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setUniqueId(String uniqueId) {
		set("unique_id", uniqueId);
	}
	
	public String getUniqueId() {
		return getStr("unique_id");
	}

	public void setApplyNo(String applyNo) {
		set("apply_no", applyNo);
	}
	
	public String getApplyNo() {
		return getStr("apply_no");
	}

	public void setConsumeType(Integer consumeType) {
		set("consume_type", consumeType);
	}
	
	public Integer getConsumeType() {
		return getInt("consume_type");
	}

	public void setInvoiceType(Integer invoiceType) {
		set("invoice_type", invoiceType);
	}
	
	public Integer getInvoiceType() {
		return getInt("invoice_type");
	}

	public void setInvoiceObj(Integer invoiceObj) {
		set("invoice_obj", invoiceObj);
	}
	
	public Integer getInvoiceObj() {
		return getInt("invoice_obj");
	}

	public void setTotalPrice(Double totalPrice) {
		set("total_price", totalPrice);
	}
	
	public Double getTotalPrice() {
		return getDouble("total_price");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setMobile(String mobile) {
		set("mobile", mobile);
	}
	
	public String getMobile() {
		return getStr("mobile");
	}

	public void setAddress(String address) {
		set("address", address);
	}
	
	public String getAddress() {
		return getStr("address");
	}

	public void setEmail(String email) {
		set("email", email);
	}
	
	public String getEmail() {
		return getStr("email");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setIdentificationNo(String identificationNo) {
		set("identification_no", identificationNo);
	}
	
	public String getIdentificationNo() {
		return getStr("identification_no");
	}

	public void setLandlineNo(String landlineNo) {
		set("landline_no", landlineNo);
	}
	
	public String getLandlineNo() {
		return getStr("landline_no");
	}

	public void setBankName(String bankName) {
		set("bank_name", bankName);
	}
	
	public String getBankName() {
		return getStr("bank_name");
	}

	public void setBankCardNo(String bankCardNo) {
		set("bank_card_no", bankCardNo);
	}
	
	public String getBankCardNo() {
		return getStr("bank_card_no");
	}

	public void setContractNo(String contractNo) {
		set("contract_no", contractNo);
	}
	
	public String getContractNo() {
		return getStr("contract_no");
	}

	public void setReceiveName(String receiveName) {
		set("receive_name", receiveName);
	}
	
	public String getReceiveName() {
		return getStr("receive_name");
	}

	public void setReceiveMobile(String receiveMobile) {
		set("receive_mobile", receiveMobile);
	}
	
	public String getReceiveMobile() {
		return getStr("receive_mobile");
	}

	public void setReceiveAddress(String receiveAddress) {
		set("receive_address", receiveAddress);
	}
	
	public String getReceiveAddress() {
		return getStr("receive_address");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setCheckinDays(Integer checkinDays) {
		set("checkin_days", checkinDays);
	}
	
	public Integer getCheckinDays() {
		return getInt("checkin_days");
	}

	public void setInvoiceCode(String invoiceCode) {
		set("invoice_code", invoiceCode);
	}
	
	public String getInvoiceCode() {
		return getStr("invoice_code");
	}

	public void setInvoiceNo(String invoiceNo) {
		set("invoice_no", invoiceNo);
	}
	
	public String getInvoiceNo() {
		return getStr("invoice_no");
	}

	public void setInvoiceTime(java.util.Date invoiceTime) {
		set("invoice_time", invoiceTime);
	}
	
	public java.util.Date getInvoiceTime() {
		return get("invoice_time");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

}
