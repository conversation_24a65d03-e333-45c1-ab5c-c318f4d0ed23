package com.cszn.integrated.service.entity.mall.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMallProduct<M extends BaseMallProduct<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCateId(String cateId) {
		set("cate_id", cateId);
	}
	
	public String getCateId() {
		return getStr("cate_id");
	}

	public void setCode(String code) {
		set("code", code);
	}

	public String getCode() {
		return getStr("code");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setTitle(String title) {
		set("title", title);
	}
	
	public String getTitle() {
		return getStr("title");
	}

	public void setDetail(String detail) {
		set("detail", detail);
	}
	
	public String getDetail() {
		return getStr("detail");
	}

	public void setPrice(java.math.BigDecimal price) {
		set("price", price);
	}
	
	public java.math.BigDecimal getPrice() {
		return get("price");
	}

	public void setPublishStatus(String publishStatus) {
		set("publish_status", publishStatus);
	}
	
	public String getPublishStatus() {
		return getStr("publish_status");
	}

	public void setStockCount(String stockCount) {
		set("stock_count", stockCount);
	}

	public String getStockCount() {
		return getStr("stock_count");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setSort(Integer sort){
		set("sort",sort);
	}

	public Integer getSort(){
		return get("sort");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
