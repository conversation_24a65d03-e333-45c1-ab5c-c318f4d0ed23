package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaMembershipCardGiveScheme<M extends BaseFinaMembershipCardGiveScheme<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSchemeId(String schemeId) {
		set("scheme_id", schemeId);
	}
	
	public String getSchemeId() {
		return getStr("scheme_id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setLastExecuteTime(java.util.Date lastExecuteTime) {
		set("last_execute_time", lastExecuteTime);
	}
	
	public java.util.Date getLastExecuteTime() {
		return get("last_execute_time");
	}

	public void setExecutedTimes(Integer executedTimes) {
		set("executed_times", executedTimes);
	}
	
	public Integer getExecutedTimes() {
		return getInt("executed_times");
	}

	public void setCycle(String cycle) {
		set("cycle", cycle);
	}
	
	public String getCycle() {
		return getStr("cycle");
	}
	
	public void setStartTime(java.util.Date startTime) {
		set("start_time", startTime);
	}
	
	public java.util.Date getStartTime() {
		return get("start_time");
	}
	
	public void setEndTime(java.util.Date endTime) {
		set("end_time", endTime);
	}
	
	public java.util.Date getEndTime() {
		return get("end_time");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
