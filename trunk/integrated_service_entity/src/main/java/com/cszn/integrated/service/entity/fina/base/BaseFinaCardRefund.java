package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardRefund<M extends BaseFinaCardRefund<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setApplyNo(String applyNo) {
		set("apply_no", applyNo);
	}
	
	public String getApplyNo() {
		return getStr("apply_no");
	}

	public void setDepartmentId(String departmentId) {
		set("department_id", departmentId);
	}
	
	public String getDepartmentId() {
		return getStr("department_id");
	}

	public void setDepartmentName(String departmentName) {
		set("department_name", departmentName);
	}
	
	public String getDepartmentName() {
		return getStr("department_name");
	}

	public void setApplyId(String applyId) {
		set("apply_id", applyId);
	}
	
	public String getApplyId() {
		return getStr("apply_id");
	}

	public void setApplyName(String applyName) {
		set("apply_name", applyName);
	}
	
	public String getApplyName() {
		return getStr("apply_name");
	}

	public void setApplyTime(java.util.Date applyTime) {
		set("apply_time", applyTime);
	}
	
	public java.util.Date getApplyTime() {
		return get("apply_time");
	}

	public void setExamineId(String examineId) {
		set("examine_id", examineId);
	}

	public String getExamineId() {
		return getStr("examine_id");
	}

	public void setExamineName(String examineName) {
		set("examine_name", examineName);
	}

	public String getExamineName() {
		return getStr("examine_name");
	}

	public void setExamineTime(java.util.Date examineTime) {
		set("examine_time", examineTime);
	}

	public java.util.Date getExamineTime() {
		return get("examine_time");
	}

	public void setPayId(String payId) {
		set("pay_id", payId);
	}

	public String getPayId() {
		return getStr("pay_id");
	}

	public void setPayName(String payName) {
		set("pay_name", payName);
	}

	public String getPayName() {
		return getStr("pay_name");
	}

	public void setPayTime(java.util.Date payTime) {
		set("pay_time", payTime);
	}

	public java.util.Date getPayTime() {
		return get("pay_time");
	}
	
	public void setTotalAmount(Double totalAmount) {
		set("total_amount", totalAmount);
	}
	
	public Double getTotalAmount() {
		return getDouble("total_amount");
	}

	public void setRemarks(String remarks) {
		set("remarks", remarks);
	}
	
	public String getRemarks() {
		return getStr("remarks");
	}

	public void setRecordStatus(String recordStatus) {
		set("record_status", recordStatus);
	}

	public String getRecordStatus() {
		return getStr("record_status");
	}

	public void setHasSubmit(Integer hasSubmit) {
		set("has_submit", hasSubmit);
	}
	
	public Integer getHasSubmit() {
		return getInt("has_submit");
	}

	public void setHasFinish(Integer hasFinish) {
		set("has_finish", hasFinish);
	}
	
	public Integer getHasFinish() {
		return getInt("has_finish");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
