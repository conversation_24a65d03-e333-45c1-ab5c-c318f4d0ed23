package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainDeductMultipleConfig<M extends BaseMainDeductMultipleConfig<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setStartDate(java.util.Date startDate) {
		set("start_date", startDate);
	}
	
	public java.util.Date getStartDate() {
		return get("start_date");
	}

	public void setEndDate(java.util.Date endDate) {
		set("end_date", endDate);
	}
	
	public java.util.Date getEndDate() {
		return get("end_date");
	}

	public void setTimeMultiple(Double timeMultiple) {
		set("time_multiple", timeMultiple);
	}
	
	public Double getTimeMultiple() {
		return getDouble("time_multiple");
	}

	public void setAmountMultiple(Double amountMultiple) {
		set("amount_multiple", amountMultiple);
	}
	
	public Double getAmountMultiple() {
		return getDouble("amount_multiple");
	}

	public void setPointsMultiple(Double pointsMultiple) {
		set("points_multiple", pointsMultiple);
	}
	
	public Double getPointsMultiple() {
		return getDouble("points_multiple");
	}

	public void setPrivateRoomDeductType(String privateRoomDeductType) {
		set("private_room_deduct_type", privateRoomDeductType);
	}
	
	public String getPrivateRoomDeductType() {
		return getStr("private_room_deduct_type");
	}

	public void setPrivateRoomTimeMultiple(Double privateRoomTimeMultiple) {
		set("private_room_time_multiple", privateRoomTimeMultiple);
	}
	
	public Double getPrivateRoomTimeMultiple() {
		return getDouble("private_room_time_multiple");
	}

	public void setPrivateRoomAmountMultiple(Double privateRoomAmountMultiple) {
		set("private_room_amount_multiple", privateRoomAmountMultiple);
	}
	
	public Double getPrivateRoomAmountMultiple() {
		return getDouble("private_room_amount_multiple");
	}

	public void setPrivateRoomPointsMultiple(Double privateRoomPointsMultiple) {
		set("private_room_points_multiple", privateRoomPointsMultiple);
	}
	
	public Double getPrivateRoomPointsMultiple() {
		return getDouble("private_room_points_multiple");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
