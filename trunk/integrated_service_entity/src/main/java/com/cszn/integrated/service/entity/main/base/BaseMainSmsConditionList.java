package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainSmsConditionList<M extends BaseMainSmsConditionList<M>> extends JbootModel<M> implements IBean {

    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

    public void setSmsApplyId(String smsApplyId) {
        set("sms_apply_id", smsApplyId);
    }

    public String getSmsApplyId() {
        return getStr("sms_apply_id");
    }

    public void setSmsConditionCode(String smsConditionCode) {
        set("sms_condition_code", smsConditionCode);
    }

    public String getSmsConditionCode() {
        return getStr("sms_condition_code");
    }

    public void setSendName(String sendName) {
        set("send_name", sendName);
    }

    public String getSendName() {
        return getStr("send_name");
    }

    public void setPhoneNumber(String phoneNumber) {
        set("phone_number", phoneNumber);
    }

    public String getPhoneNumber() {
        return getStr("phone_number");
    }

    public void setSendContent(String sendContent) {
        set("send_content", sendContent);
    }

    public String getSendContent() {
        return getStr("send_content");
    }

    public void setSendTime(java.util.Date sendTime) {
        set("send_time", sendTime);
    }

    public java.util.Date getSendTime() {
        return get("send_time");
    }

    public void setSendStatus(String sendStatus) {
        set("send_status", sendStatus);
    }

    public String getSendStatus() {
        return getStr("send_status");
    }

    public void setRealSendTime(java.util.Date realSendTime) {
        set("real_send_time", realSendTime);
    }

    public java.util.Date getRealSendTime() {
        return get("real_send_time");
    }

    public void setIsSuccess(String isSuccess) {
        set("is_success", isSuccess);
    }

    public String getIsSuccess() {
        return getStr("is_success");
    }

    public void setErrorMsg(String errorMsg) {
        set("error_msg", errorMsg);
    }

    public String getErrorMsg() {
        return getStr("error_msg");
    }

    public void setDelFlag(String delFlag) {
        set("del_flag", delFlag);
    }

    public String getDelFlag() {
        return getStr("del_flag");
    }

    public void setCreateBy(String createBy) {
        set("create_by", createBy);
    }

    public String getCreateBy() {
        return getStr("create_by");
    }

    public void setCreateTime(java.util.Date createTime) {
        set("create_time", createTime);
    }

    public java.util.Date getCreateTime() {
        return get("create_time");
    }

    public void setUpdateBy(String updateBy) {
        set("update_by", updateBy);
    }

    public String getUpdateBy() {
        return getStr("update_by");
    }

    public void setUpdateTime(java.util.Date updateTime) {
        set("update_time", updateTime);
    }

    public java.util.Date getUpdateTime() {
        return get("update_time");
    }

}
