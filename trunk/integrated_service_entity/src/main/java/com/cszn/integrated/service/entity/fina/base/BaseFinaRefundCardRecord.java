package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaRefundCardRecord<M extends BaseFinaRefundCardRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardId(java.lang.String cardId) {
		set("card_id", cardId);
	}
	
	public java.lang.String getCardId() {
		return getStr("card_id");
	}

	public void setCardNumber(java.lang.String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public java.lang.String getCardNumber() {
		return getStr("card_number");
	}

	public void setRefundTime(java.util.Date refundTime) {
		set("refund_time", refundTime);
	}
	
	public java.util.Date getRefundTime() {
		return get("refund_time");
	}

	public void setRefundAmount(java.lang.Double refundAmount) {
		set("refund_amount", refundAmount);
	}
	
	public java.lang.Double getRefundAmount() {
		return getDouble("refund_amount");
	}

	public void setPayWay(java.lang.String payWay) {
		set("pay_way", payWay);
	}
	
	public java.lang.String getPayWay() {
		return getStr("pay_way");
	}

	public void setIsPay(java.lang.String isPay) {
		set("is_pay", isPay);
	}
	
	public java.lang.String getIsPay() {
		return getStr("is_pay");
	}

	public void setPayTime(java.util.Date payTime) {
		set("pay_time", payTime);
	}
	
	public java.util.Date getPayTime() {
		return get("pay_time");
	}

	public void setPayBy(java.lang.String payBy) {
		set("pay_by", payBy);
	}

	public java.lang.String getPayBy() {
		return getStr("pay_by");
	}

	public void setPayName(java.lang.String payName) {
		set("pay_name", payName);
	}

	public java.lang.String getPayName() {return getStr("pay_name");}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
