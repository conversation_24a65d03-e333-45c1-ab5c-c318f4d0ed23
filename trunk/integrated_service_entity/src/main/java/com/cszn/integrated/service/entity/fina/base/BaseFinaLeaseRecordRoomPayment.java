package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaLeaseRecordRoomPayment<M extends BaseFinaLeaseRecordRoomPayment<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setLeaseId(String leaseId) {
		set("lease_id", leaseId);
	}
	
	public String getLeaseId() {
		return getStr("lease_id");
	}

	public void setRoomId(String roomId) {
		set("room_id", roomId);
	}
	
	public String getRoomId() {
		return getStr("room_id");
	}

	public void setRecordRoomId(String recordRoomId) {
		set("record_room_id", recordRoomId);
	}

	public String getRecordRoomId() {
		return getStr("record_room_id");
	}

	public void setOrderNo(String orderNo) {
		set("order_no", orderNo);
	}
	
	public String getOrderNo() {
		return getStr("order_no");
	}

	public void setIssueNo(Integer issueNo) {
		set("issue_no", issueNo);
	}
	
	public Integer getIssueNo() {
		return getInt("issue_no");
	}

	public void setStartDate(java.util.Date startDate) {
		set("start_date", startDate);
	}
	
	public java.util.Date getStartDate() {
		return get("start_date");
	}

	public void setEndDate(java.util.Date endDate) {
		set("end_date", endDate);
	}
	
	public java.util.Date getEndDate() {
		return get("end_date");
	}

	public void setNeedPayDate(java.util.Date needPayDate) {
		set("need_pay_date", needPayDate);
	}
	
	public java.util.Date getNeedPayDate() {
		return get("need_pay_date");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setMonthRent(Double monthRent) {
		set("month_rent", monthRent);
	}

	public Double getMonthRent() {
		return getDouble("month_rent");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setSendStatus(String sendStatus) {
		set("send_status", sendStatus);
	}
	
	public String getSendStatus() {
		return getStr("send_status");
	}

	public void setSendCount(Integer sendCount) {
		set("send_count", sendCount);
	}
	
	public Integer getSendCount() {
		return getInt("send_count");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setPayAmount(Double payAmount) {
		set("pay_amount", payAmount);
	}

	public Double getPayAmount() {
		return getDouble("pay_amount");
	}

	public void setPayType(String payType) {
		set("pay_type", payType);
	}

	public String getPayType() {
		return getStr("pay_type");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
