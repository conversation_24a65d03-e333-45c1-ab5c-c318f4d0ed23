package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardReconciliationTotalRecord<M extends BaseFinaCardReconciliationTotalRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setYearMonth(String yearMonth) {
		set("year_month", yearMonth);
	}
	
	public String getYearMonth() {
		return getStr("year_month");
	}

	public void setVerifierId(String verifierId) {
		set("verifier_id", verifierId);
	}
	
	public String getVerifierId() {
		return getStr("verifier_id");
	}

	public void setTotalTimes(Double totalTimes) {
		set("total_times", totalTimes);
	}
	
	public Double getTotalTimes() {
		return getDouble("total_times");
	}

	public void setTotalAmount(Double totalAmount) {
		set("total_amount", totalAmount);
	}
	
	public Double getTotalAmount() {
		return getDouble("total_amount");
	}

	public void setTotalIntegrals(Double totalIntegrals) {
		set("total_integrals", totalIntegrals);
	}
	
	public Double getTotalIntegrals() {
		return getDouble("total_integrals");
	}

	public void setActualTotalTimes(Double actualTotalTimes) {
		set("actual_total_times", actualTotalTimes);
	}
	
	public Double getActualTotalTimes() {
		return getDouble("actual_total_times");
	}

	public void setActualTotalAmount(Double actualTotalAmount) {
		set("actual_total_amount", actualTotalAmount);
	}
	
	public Double getActualTotalAmount() {
		return getDouble("actual_total_amount");
	}

	public void setActualTotalIntegrals(Double actualTotalIntegrals) {
		set("actual_total_integrals", actualTotalIntegrals);
	}
	
	public Double getActualTotalIntegrals() {
		return getDouble("actual_total_integrals");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
