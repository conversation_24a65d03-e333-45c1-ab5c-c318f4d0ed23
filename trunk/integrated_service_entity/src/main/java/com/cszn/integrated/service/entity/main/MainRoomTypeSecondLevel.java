package com.cszn.integrated.service.entity.main;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.main.base.BaseMainRoomTypeSecondLevel;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_room_type_second_level", primaryKey = "id")
public class MainRoomTypeSecondLevel extends BaseMainRoomTypeSecondLevel<MainRoomTypeSecondLevel> {

    public Record getFirstLevel(){

        return Db.findById("main_room_type_first_level",this.getFirstLevelId());
    }

}
