package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.fina.base.BaseFinaContractRecord;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_contract_record", primaryKey = "id")
public class FinaContractRecord extends BaseFinaContractRecord<FinaContractRecord> {


    public Record getCardType(){

        return Db.findById("main_membership_card_type",this.getCardTypeId());
    }

    public Record getContractType(){

        return Db.findById("main_contract_type",this.getContractTypeId());
    }
}
