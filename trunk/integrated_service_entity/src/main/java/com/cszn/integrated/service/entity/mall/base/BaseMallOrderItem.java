package com.cszn.integrated.service.entity.mall.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMallOrderItem<M extends BaseMallOrderItem<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setOrderId(String orderId) {
		set("order_id", orderId);
	}
	
	public String getOrderId() {
		return getStr("order_id");
	}

	public void setType(String type) {
		set("type", type);
	}

	public String getType() {
		return getStr("type");
	}

	public void setOutWarehousesId(String outWarehousesId){
		set("out_warehouses_id",outWarehousesId);
	}

	public String getOutWarehousesId() {
		return getStr("out_warehouses_id");
	}

	public void setModelId(String modelId) {
		set("model_id", modelId);
	}
	
	public String getModelId() {
		return getStr("model_id");
	}

	public void setNum(Double num) {
		set("num", num);
	}
	
	public Double getNum() {
		return getDouble("num");
	}

	public void setPrice(Double price) {
		set("price", price);
	}
	
	public Double getPrice() {
		return getDouble("price");
	}

	public void setTotalFee(Double totalFee) {
		set("total_fee", totalFee);
	}
	
	public Double getTotalFee() {
		return getDouble("total_fee");
	}

	public void setPayAmount(Double payAmount) {
		set("pay_amount", payAmount);
	}

	public Double getPayAmount() {
		return getDouble("pay_amount");
	}

	public void setDiscountPrice(Double discountPrice) {
		set("discount_price", discountPrice);
	}

	public Double getDiscountPrice() {
		return getDouble("discount_price");
	}

	public void setRuboutAmount(Double ruboutAmount) {
		set("rubout_amount", ruboutAmount);
	}

	public Double getRuboutAmount() {
		return getDouble("rubout_amount");
	}

	public void setCostPrice(Double costPrice) {
		set("cost_price", costPrice);
	}

	public Double getCostPrice() {
		return getDouble("cost_price");
	}

	public void setModelImageUrl(String modelImageUrl) {
		set("model_image_url", modelImageUrl);
	}
	
	public String getModelImageUrl() {
		return getStr("model_image_url");
	}

	public void setSubmitUser(String submitUser) {
		set("submit_user", submitUser);
	}

	public String getSubmitUser() {
		return getStr("submit_user");
	}

	public void setHandlerUser(String handlerUser) {
		set("handler_user", handlerUser);
	}

	public String getHandlerUser() {
		return getStr("handler_user");
	}

}
