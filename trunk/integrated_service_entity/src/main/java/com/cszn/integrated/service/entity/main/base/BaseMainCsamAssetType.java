package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

public class BaseMainCsamAssetType<M extends BaseMainCsamAssetType<M>> extends JbootModel<M> implements IBean {

    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

    public void setParentId(String parentId) {
        set("parent_id", parentId);
    }

    public String getParentId() {
        return getStr("parent_id");
    }

    public void setParentIds(String parentIds) {
        set("parent_ids", parentIds);
    }

    public String getParentIds() {
        return getStr("parent_ids");
    }

    public void setAssetTypeNo(String assetTypeNo) {
        set("asset_type_no", assetTypeNo);
    }

    public String getAssetTypeNo() {
        return getStr("asset_type_no");
    }

    public void setWmsStockTypeId(String wmsStockTypeId) {
        set("wms_stock_type_id", wmsStockTypeId);
    }

    public String getWmsStockTypeId() {
        return getStr("wms_stock_type_id");
    }

    public void setAssetCodePrefix(String assetCodePrefix) {
        set("asset_code_prefix", assetCodePrefix);
    }

    public String getAssetCodePrefix() {
        return getStr("asset_code_prefix");
    }

    public void setAssetTypeOrder(Integer assetTypeOrder) {
        set("asset_type_order", assetTypeOrder);
    }

    public String getAssetTypeOrder() {
        return getStr("asset_type_order");
    }

    public void setIsLeaver(String isLeaver) {
        set("is_leaver", isLeaver);
    }

    public String getIsLeaver() {
        return getStr("is_leaver");
    }

    public void setAssetTypeName(String assetTypeName) {
        set("asset_type_name", assetTypeName);
    }

    public String getAssetTypeName() {
        return getStr("asset_type_name");
    }

    public void setIsEnabled(String isEnabled) {
        set("is_enabled", isEnabled);
    }

    public String getIsEnabled() {
        return getStr("is_enabled");
    }

    public void setCreateBy(String createBy) {
        set("create_by", createBy);
    }

    public String getCreateBy() {
        return getStr("create_by");
    }

    public void setCreateDate(java.util.Date createDate) {
        set("create_date", createDate);
    }

    public java.util.Date getCreateDate() {
        return get("create_date");
    }

    public void setUpdateBy(String updateBy) {
        set("update_by", updateBy);
    }

    public String getUpdateBy() {
        return getStr("update_by");
    }

    public void setUpdateDate(java.util.Date updateDate) {
        set("update_date", updateDate);
    }

    public java.util.Date getUpdateDate() {
        return get("update_date");
    }

}
