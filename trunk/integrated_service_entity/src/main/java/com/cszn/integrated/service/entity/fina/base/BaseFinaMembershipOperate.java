package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaMembershipOperate<M extends BaseFinaMembershipOperate<M>> extends JbootModel<M> implements IBean {

    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

	public void setOperateObj(String operateObj) {
		set("operate_obj", operateObj);
	}

	public String getOperateObj() {
		return getStr("operate_obj");
	}

	public void setOperateType(String operateType) {
		set("operate_type", operateType);
	}

	public String getOperateType() {
		return getStr("operate_type");
	}

	public void setDataId(String dataId) {
		set("data_id", dataId);
	}

	public String getDataId() {
		return getStr("data_id");
	}

	public void setOperateContent(String operateContent) {
		set("operate_content", operateContent);
	}

	public String getOperateContent() {
		return getStr("operate_content");
	}

	public void setOperateTime(java.util.Date operateTime) {
		set("operate_time", operateTime);
	}

	public java.util.Date getOperateTime() {
		return get("operate_time");
	}

	public void setOperateBy(String operateBy) {
		set("operate_by", operateBy);
	}

	public String getOperateBy() {
		return getStr("operate_by");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}

	public String getDelFlag() {
		return getStr("del_flag");
	}
}
