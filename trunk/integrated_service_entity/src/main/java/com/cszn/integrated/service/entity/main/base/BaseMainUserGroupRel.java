package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by J<PERSON><PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainUserGroupRel<M extends BaseMainUserGroupRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}

	public String getId() {
		return getStr("id");
	}

	public void setUserGroupId(String userGroupId) {
		set("user_group_id", userGroupId);
	}

	public String getUserGroupId() {
		return getStr("user_group_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}

	public String getUserId() {
		return getStr("user_id");
	}

}
