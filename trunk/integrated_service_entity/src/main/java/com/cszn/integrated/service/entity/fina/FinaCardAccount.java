package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaCardAccount;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_card_account", primaryKey = "id")
public class FinaCardAccount extends BaseFinaCardAccount<FinaCardAccount> {
	
	public String getPayWayName(){
		final String payWay = this.getPayWay();
		String payWayName = "";
		if(StrKit.notBlank(payWay)){
			payWayName = Db.queryStr("select name from main_payment_ways where is_enabled='1' and payment_way_code=?", payWay);
		}
        return payWayName;
    }
}
