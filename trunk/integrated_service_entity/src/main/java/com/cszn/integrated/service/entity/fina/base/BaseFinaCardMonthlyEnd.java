package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardMonthlyEnd<M extends BaseFinaCardMonthlyEnd<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setYearMonth(String yearMonth) {
		set("year_month", yearMonth);
	}
	
	public String getYearMonth() {
		return getStr("year_month");
	}

	public void setTimes(Double times) {
		set("times", times);
	}
	
	public Double getTimes() {
		return getDouble("times");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setPoints(Double points) {
		set("points", points);
	}
	
	public Double getPoints() {
		return getDouble("points");
	}

	public void setIntegrals(Double integrals) {
		set("integrals", integrals);
	}
	
	public Double getIntegrals() {
		return getDouble("integrals");
	}

	public void setBeanCoupons(Double beanCoupons) {
		set("bean_coupons", beanCoupons);
	}
	
	public Double getBeanCoupons() {
		return getDouble("bean_coupons");
	}

	public void setConsumeTimes(Double consumeTimes) {
		set("consume_times", consumeTimes);
	}
	
	public Double getConsumeTimes() {
		return getDouble("consume_times");
	}

	public void setConsumeAmount(Double consumeAmount) {
		set("consume_amount", consumeAmount);
	}
	
	public Double getConsumeAmount() {
		return getDouble("consume_amount");
	}

	public void setConsumePoints(Double consumePoints) {
		set("consume_points", consumePoints);
	}
	
	public Double getConsumePoints() {
		return getDouble("consume_points");
	}

	public void setConsumeIntegrals(Double consumeIntegrals) {
		set("consume_integrals", consumeIntegrals);
	}
	
	public Double getConsumeIntegrals() {
		return getDouble("consume_integrals");
	}

	public void setConsumeBeanCoupons(Double consumeBeanCoupons) {
		set("consume_bean_coupons", consumeBeanCoupons);
	}
	
	public Double getConsumeBeanCoupons() {
		return getDouble("consume_bean_coupons");
	}

	public void setTimesIncome(Double timesIncome) {
		set("times_income", timesIncome);
	}
	
	public Double getTimesIncome() {
		return getDouble("times_income");
	}
	
	public void setAmountIncome(Double amountIncome) {
		set("amount_income", amountIncome);
	}
	
	public Double getAmountIncome() {
		return getDouble("amount_income");
	}
	
	public void setTotalIncome(Double totalIncome) {
		set("total_income", totalIncome);
	}
	
	public Double getTotalIncome() {
		return getDouble("total_income");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_date");
	}
}
