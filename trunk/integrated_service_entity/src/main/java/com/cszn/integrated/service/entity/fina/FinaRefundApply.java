package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaRefundApply;
import com.cszn.integrated.service.entity.status.HandlerType;
import com.cszn.integrated.service.entity.status.RefundSituation;
import io.jboot.db.annotation.Table;

import java.util.List;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_refund_apply", primaryKey = "id")
public class FinaRefundApply extends BaseFinaRefundApply<FinaRefundApply> {
	
	private List<FinaRefundAccount> accountList;
	private List<FinaRefundCard> cardList;
	public List<FinaRefundAccount> getAccountList() {
		return accountList;
	}
	public void setAccountList(List<FinaRefundAccount> accountList) {
		this.accountList = accountList;
	}
	public List<FinaRefundCard> getCardList() {
		return cardList;
	}
	public void setCardList(List<FinaRefundCard> cardList) {
		this.cardList = cardList;
	}

	public String getRefundSituationName(){
		return RefundSituation.me().desc(this.getRefundSituation());
	}

	public String getHandlerTypeName(){
		return HandlerType.me().desc(this.getHandlerType());
	}
}
