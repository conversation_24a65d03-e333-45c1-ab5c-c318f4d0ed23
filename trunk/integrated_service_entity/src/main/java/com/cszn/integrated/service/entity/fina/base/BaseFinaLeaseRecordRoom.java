package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaLeaseRecordRoom<M extends BaseFinaLeaseRecordRoom<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setLeaseId(String leaseId) {
		set("lease_id", leaseId);
	}
	
	public String getLeaseId() {
		return getStr("lease_id");
	}

	public void setRoomId(String roomId) {
		set("room_id", roomId);
	}
	
	public String getRoomId() {
		return getStr("room_id");
	}

	public void setRent(Double rent) {
		set("rent", rent);
	}
	
	public Double getRent() {
		return getDouble("rent");
	}

	public void setRentUnit(String rentUnit) {
		set("rent_unit", rentUnit);
	}

	public String getRentUnit() {
		return getStr("rent_unit");
	}

	public void setCurrentRent(Double currentRent) {
		set("current_rent", currentRent);
	}

	public Double getCurrentRent() {
		return getDouble("current_rent");
	}

	public void setEarnestMoney(Double earnestMoney) {
		set("earnest_money", earnestMoney);
	}
	
	public Double getEarnestMoney() {
		return getDouble("earnest_money");
	}

	public void setEarnestMoneyPaymentType(String earnestMoneyPaymentType) {
		set("earnest_money_payment_type", earnestMoneyPaymentType);
	}

	public String getEarnestMoneyPaymentType() {
		return getStr("earnest_money_payment_type");
	}

	public void setPaymentType(String paymentType) {
		set("payment_type", paymentType);
	}
	
	public String getPaymentType() {
		return getStr("payment_type");
	}

	public void setPaymentDate(String paymentDate) {
		set("payment_date", paymentDate);
	}

	public String getPaymentDate() {
		return getStr("payment_date");
	}

	public void setRentPaymentDateType(String rentPaymentDateType) {
		set("rent_payment_date_type", rentPaymentDateType);
	}

	public String getRentPaymentDateType() {
		return getStr("rent_payment_date_type");
	}

	public void setRentPaymentDateDay(String rentPaymentDateDay) {
		set("rent_payment_date_day", rentPaymentDateDay);
	}

	public Integer getRentPaymentDateDay() {
		return get("rent_payment_date_day");
	}

	public void setOrderNo(String orderNo) {
		set("order_no", orderNo);
	}

	public String getOrderNo() {
		return getStr("order_no");
	}

	public void setContractStartDate(java.util.Date contractStartDate) {
		set("contract_start_date", contractStartDate);
	}
	
	public java.util.Date getContractStartDate() {
		return get("contract_start_date");
	}

	public void setContractEndDate(java.util.Date contractEndDate) {
		set("contract_end_date", contractEndDate);
	}
	
	public java.util.Date getContractEndDate() {
		return get("contract_end_date");
	}

	public void setRoomStartDate(java.util.Date roomStartDate) {
		set("room_start_date", roomStartDate);
	}

	public java.util.Date getRoomStartDate() {
		return get("room_start_date");
	}

	public void setRoomEndDate(java.util.Date roomEndDate) {
		set("room_end_date", roomEndDate);
	}

	public java.util.Date getRoomEndDate() {
		return get("room_end_date");
	}

	public void setRentAddYear(Integer rentAddYear) {
		set("rent_add_year", rentAddYear);
	}

	public Integer getRentAddYear() {
		return getInt("rent_add_year");
	}

	public void setRentAddRatio(Integer rentAddRatio) {
		set("rent_add_ratio", rentAddRatio);
	}

	public Integer getRentAddRatio() {
		return get("rent_add_ratio");
	}

	public void setSignContractUserId(String signContractUserId) {
		set("sign_contract_user_id", signContractUserId);
	}

	public String getSignContractUserId() {
		return getStr("sign_contract_user_id");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
