package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseCustomerType<M extends BaseMainBaseCustomerType<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setBaseId(java.lang.String baseId) {
		set("base_id", baseId);
	}
	
	public java.lang.String getBaseId() {
		return getStr("base_id");
	}

	public void setTypeName(java.lang.String typeName) {
		set("type_name", typeName);
	}
	
	public java.lang.String getTypeName() {
		return getStr("type_name");
	}

	public void setTypePrice(java.lang.Double typePrice) {
		set("type_price", typePrice);
	}
	
	public java.lang.Double getTypePrice() {
		return getDouble("type_price");
	}
	
	public void setDepositPrice(java.lang.Double depositPrice) {
		set("deposit_price", depositPrice);
	}
	
	public java.lang.Double getDepositPrice() {
		return getDouble("deposit_price");
	}
	
	public void setEntryTimes(java.lang.Integer entryTimes) {
		set("entry_times", entryTimes);
	}
	
	public java.lang.Integer getEntryTimes() {
		return getInt("entry_times");
	}

	public void setIsEnabled(java.lang.String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public java.lang.String getIsEnabled() {
		return getStr("is_enabled");
	}
	
	public void setIsCheckIn(java.lang.String isCheckIn) {
		set("is_check_in", isCheckIn);
	}
	
	public java.lang.String getIsCheckIn() {
		return getStr("is_check_in");
	}
	
	public void setIsTeam(java.lang.String isTeam) {
		set("is_team", isTeam);
	}
	
	public java.lang.String getIsTeam() {
		return getStr("is_team");
	}
	
	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
