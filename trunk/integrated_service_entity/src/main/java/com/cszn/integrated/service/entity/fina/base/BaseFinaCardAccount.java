package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;

import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardAccount<M extends BaseFinaCardAccount<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setPayWay(String payWay) {
		set("pay_way", payWay);
	}
	
	public String getPayWay() {
		return getStr("pay_way");
	}

	public void setAccountName(String accountName) {
		set("account_name", accountName);
	}
	
	public String getAccountName() {
		return getStr("account_name");
	}

	public void setAccountNum(String accountNum) {
		set("account_num", accountNum);
	}
	
	public String getAccountNum() {
		return getStr("account_num");
	}

	public void setBankName(String bankName) {
		set("bank_name", bankName);
	}
	
	public String getBankName() {
		return getStr("bank_name");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}
	
	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
