package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaPunishRecord<M extends BaseFinaPunishRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setBookNo(String bookNo) {
		set("book_no", bookNo);
	}
	
	public String getBookNo() {
		return getStr("book_no");
	}

	public void setCheckinNo(String checkinNo) {
		set("checkin_no", checkinNo);
	}
	
	public String getCheckinNo() {
		return getStr("checkin_no");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setOfficeId(String officeId) {
		set("office_id", officeId);
	}
	
	public String getOfficeId() {
		return getStr("office_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}
	
	public String getUserId() {
		return getStr("user_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setPunishType(String punishType) {
		set("punish_type", punishType);
	}
	
	public String getPunishType() {
		return getStr("punish_type");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setBookCreateTime(java.util.Date bookCreateTime) {
		set("book_create_time", bookCreateTime);
	}
	
	public java.util.Date getBookCreateTime() {
		return get("book_create_time");
	}

	public void setBookCancelTime(java.util.Date bookCancelTime) {
		set("book_cancel_time", bookCancelTime);
	}
	
	public java.util.Date getBookCancelTime() {
		return get("book_cancel_time");
	}

	public void setPlanCheckinTime(java.util.Date planCheckinTime) {
		set("plan_checkin_time", planCheckinTime);
	}
	
	public java.util.Date getPlanCheckinTime() {
		return get("plan_checkin_time");
	}

	public void setActualCheckinTime(java.util.Date actualCheckinTime) {
		set("actual_checkin_time", actualCheckinTime);
	}
	
	public java.util.Date getActualCheckinTime() {
		return get("actual_checkin_time");
	}

	public void setPlanCheckoutTime(java.util.Date planCheckoutTime) {
		set("plan_checkout_time", planCheckoutTime);
	}
	
	public java.util.Date getPlanCheckoutTime() {
		return get("plan_checkout_time");
	}

	public void setActualCheckoutTime(java.util.Date actualCheckoutTime) {
		set("actual_checkout_time", actualCheckoutTime);
	}
	
	public java.util.Date getActualCheckoutTime() {
		return get("actual_checkout_time");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setTimes(Double times) {
		set("times", times);
	}
	
	public Double getTimes() {
		return getDouble("times");
	}

	public void setPoints(Double points) {
		set("points", points);
	}

	public Double getPoints() {
		return getDouble("points");
	}
	
	public void setIntegrals(Double integrals) {
		set("integrals", integrals);
	}
	
	public Double getIntegrals() {
		return getDouble("integrals");
	}
	
	public void setBeanCoupons(Double beanCoupons) {
		set("bean_coupons", beanCoupons);
	}
	
	public Double getBeanCoupons() {
		return getDouble("bean_coupons");
	}

	public void setTakeTime(java.util.Date takeTime) {
		set("take_time", takeTime);
	}
	
	public java.util.Date getTakeTime() {
		return get("take_time");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
