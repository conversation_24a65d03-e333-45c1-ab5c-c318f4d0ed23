package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardRecharge<M extends BaseFinaCardRecharge<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setClassify(String classify) {
		set("classify", classify);
	}

	public String getClassify() {
		return getStr("classify");
	}
	
	public void setRechargeSource(String rechargeSource) {
		set("recharge_source", rechargeSource);
	}
	
	public String getRechargeSource() {
		return getStr("recharge_source");
	}

	public void setRechargeNo(String rechargeNo) {
		set("recharge_no", rechargeNo);
	}

	public String getRechargeNo() {
		return getStr("recharge_no");
	}

	public void setIsCash(String isCash) {
		set("is_cash", isCash);
	}

	public void setCashValue(Double cashValue) {
		set("cash_value", cashValue);
	}

	public Double getCashValue() {
		return getDouble("cash_value");
	}

	public void setAccountValue(Double accountValue) {
		set("account_value", accountValue);
	}

	public Double getAccountValue() {
		return getDouble("account_value");
	}

	public String getIsCash() {
		return getStr("is_cash");
	}

	public void setAccountId(String accountId) {
		set("account_id", accountId);
	}

	public String getAccountId() {
		return getStr("account_id");
	}

	public void setIsIncome(String isIncome) {
		set("is_income", isIncome);
	}

	public String getIsIncome() {
		return getStr("is_income");
	}

	public void setRechargeTime(java.util.Date rechargeTime) {
		set("recharge_time", rechargeTime);
	}
	
	public java.util.Date getRechargeTime() {
		return get("recharge_time");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setConsumeTimes(Double consumeTimes) {
		set("consume_times", consumeTimes);
	}
	
	public Double getConsumeTimes() {
		return getDouble("consume_times");
	}

	public void setConsumePoints(Double consumePoints) {
		set("consume_points", consumePoints);
	}

	public Double getConsumePoints() {
		return getDouble("consume_points");
	}
	
	public void setConsumeIntegral(Double consumeIntegral) {
		set("consume_integral", consumeIntegral);
	}
	
	public Double getConsumeIntegral() {
		return getDouble("consume_integral");
	}
	
	public void setConsumeBeanCoupons(Double consumeBeanCoupons) {
		set("consume_bean_coupons", consumeBeanCoupons);
	}
	
	public Double getConsumeBeanCoupons() {
		return getDouble("consume_bean_coupons");
	}

	public void setPrice(Double price) {
		set("price", price);
	}
	
	public Double getPrice() {
		return getDouble("price");
	}

	public void setPayAmount(Double payAmount) {
		set("pay_amount", payAmount);
	}
	
	public Double getPayAmount() {
		return getDouble("pay_amount");
	}

	public void setGiveAmount(Double giveAmount) {
		set("give_amount", giveAmount);
	}
	
	public Double getGiveAmount() {
		return getDouble("give_amount");
	}

	public void setGiveConsumeTimes(Double giveConsumeTimes) {
		set("give_consume_times", giveConsumeTimes);
	}
	
	public Double getGiveConsumeTimes() {
		return getDouble("give_consume_times");
	}

	public void setGiveConsumePoints(Double giveConsumePoints) {
		set("give_consume_points", giveConsumePoints);
	}

	public Double getGiveConsumePoints() {
		return getDouble("give_consume_points");
	}
	
	public void setGiveConsumeIntegral(Double giveConsumeIntegral) {
		set("give_consume_integral", giveConsumeIntegral);
	}
	
	public Double getGiveConsumeIntegral() {
		return getDouble("give_consume_integral");
	}
	
	public void setGiveBeanCoupons(Double giveBeanCoupons) {
		set("give_bean_coupons", giveBeanCoupons);
	}
	
	public Double getGiveBeanCoupons() {
		return getDouble("give_bean_coupons");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setCancelFlag(String cancelFlag) {
		set("cancel_flag", cancelFlag);
	}
	
	public String getCancelFlag() {
		return getStr("cancel_flag");
	}

	public void setRechargeWay(String rechargeWay) {
		set("recharge_way", rechargeWay);
	}
	
	public String getRechargeWay() {
		return getStr("recharge_way");
	}

	public void setIsReview(String isReview) {
		set("is_review", isReview);
	}
	
	public String getIsReview() {
		return getStr("is_review");
	}
	
	public void setIsCount(String isCount) {
		set("is_count", isCount);
	}
	
	public String getIsCount() {
		return getStr("is_count");
	}
	
	public void setAverageDays(Double averageDays) {
		set("average_days", averageDays);
	}
	
	public Double getAverageDays() {
		return getDouble("average_days");
	}
	
	public void setCountPrice(Double countPrice) {
		set("count_price", countPrice);
	}
	
	public Double getCountPrice() {
		return getDouble("count_price");
	}
	
	public void setCountDays(Double countDays) {
		set("count_days", countDays);
	}
	
	public Double getCountDays() {
		return getDouble("count_days");
	}

	public void setIsAuto(String isAuto) {
		set("is_auto", isAuto);
	}

	public String getIsAuto() {
		return getStr("is_auto");
	}

	public void setAutoType(String autoType) {
		set("auto_type", autoType);
	}

	public String getAutoType() {
		return getStr("auto_type");
	}

	public void setAutoRuleId(String autoRuleId) {
		set("auto_rule_id", autoRuleId);
	}

	public String getAutoRuleId() {
		return getStr("auto_rule_id");
	}

	public void setAutoSeq(Integer autoSeq) {
		set("auto_seq", autoSeq);
	}

	public Integer getAutoSeq() {
		return get("auto_seq");
	}
	
	public void setRechargeListId(String rechargeListId) {
		set("recharge_list_id", rechargeListId);
	}
	
	public String getRechargeListId() {
		return getStr("recharge_list_id");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
