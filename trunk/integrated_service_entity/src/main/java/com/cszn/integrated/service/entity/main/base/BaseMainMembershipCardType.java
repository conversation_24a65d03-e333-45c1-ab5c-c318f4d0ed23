package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;

import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainMembershipCardType<M extends BaseMainMembershipCardType<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardType(String cardType) {
		set("card_type", cardType);
	}
	
	public String getCardType() {
		return getStr("card_type");
	}

	public void setIsBalance(String isBalance) {
		set("is_balance", isBalance);
	}
	
	public String getIsBalance() {
		return getStr("is_balance");
	}

	public void setMarkupSchemeId(String markupSchemeId) {
		set("markup_scheme_id", markupSchemeId);
	}

	public String getMarkupSchemeId() {
		return getStr("markup_scheme_id");
	}
	public void setIsConsumeTimes(String isConsumeTimes) {
		set("is_consume_times", isConsumeTimes);
	}
	
	public String getIsConsumeTimes() {
		return getStr("is_consume_times");
	}

	public void setIsConsumePoints(String isConsumePoints){
		set("is_consume_points",isConsumePoints);
	}

	public String getIsConsumePoints(){
		return getStr("is_consume_points");
	}
	
	public void setIsIntegral(String isIntegral){
		set("is_integral",isIntegral);
	}
	
	public String getIsIntegral(){
		return getStr("is_integral");
	}
	
	public void setIsExpend(String isExpend){
		set("is_expend",isExpend);
	}
	
	public String getIsExpend(){
		return getStr("is_expend");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}
	
	public void setIsRechargeBean(String isRechargeBean) {
		set("is_recharge_bean", isRechargeBean);
	}
	
	public String getIsRechargeBean() {
		return getStr("is_recharge_bean");
	}
	
	public void setIsNotSendSms(String isNotSendSms) {
		set("is_not_send_sms", isNotSendSms);
	}
	
	public String getIsNotSendSms() {
		return getStr("is_not_send_sms");
	}
	
	public void setIsNotSignAgreement(String isNotSignAgreement) {
		set("is_not_sign_agreement", isNotSignAgreement);
	}
	
	public String getIsNotSignAgreement() {
		return getStr("is_not_sign_agreement");
	}

	public void setIsVerifyTimes(String isVerifyTimes) {
		set("is_verify_times", isVerifyTimes);
	}

	public String getIsVerifyTimes() {
		return getStr("is_verify_times");
	}

	public void setCardPrefix(String cardPrefix) {
		set("card_prefix", cardPrefix);
	}
	
	public String getCardPrefix() {
		return getStr("card_prefix");
	}

	public void setCardMin(String cardMin) {
		set("card_min", cardMin);
	}
	
	public String getCardMin() {
		return getStr("card_min");
	}

	public void setCardMax(String cardMax) {
		set("card_max", cardMax);
	}
	
	public String getCardMax() {
		return getStr("card_max");
	}

	public void setBookMax(Integer bookMax) {
		set("book_max", bookMax);
	}
	
	public Integer getBookMax() {
		return getInt("book_max");
	}

	public void setCheckinMax(Integer checkinMax) {
		set("checkin_max", checkinMax);
	}
	
	public Integer getCheckinMax() {
		return getInt("checkin_max");
	}

	public void setTypeCategory(String typeCategory) {
		set("type_category", typeCategory);
	}
	
	public String getTypeCategory() {
		return getStr("type_category");
	}
	
	public void setTypeClassify(String typeClassify) {
		set("type_classify", typeClassify);
	}
	
	public String getTypeClassify() {
		return getStr("type_classify");
	}
	
	public void setYearLimit(String yearLimit) {
		set("year_limit", yearLimit);
	}
	
	public String getYearLimit() {
		return getStr("year_limit");
	}

	public void setRemark(String remark){
		set("remark",remark);
	}

	public String getRemark(){
		return get("remark");
	}

	public void setContractIds(String contractIds) {
		set("contract_ids", contractIds);
	}

	public String getContractIds() {
		return getStr("contract_ids");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
