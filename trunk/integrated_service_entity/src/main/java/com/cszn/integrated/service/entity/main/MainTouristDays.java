package com.cszn.integrated.service.entity.main;

import java.util.List;

import com.cszn.integrated.service.entity.main.base.BaseMainTouristDays;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_tourist_days", primaryKey = "id")
public class MainTouristDays extends BaseMainTouristDays<MainTouristDays> {
	
	private List<MainTouristDaysDetail> daysDetailList;

	public List<MainTouristDaysDetail> getDaysDetailList() {
		return daysDetailList;
	}

	public void setDaysDetailList(List<MainTouristDaysDetail> daysDetailList) {
		this.daysDetailList = daysDetailList;
	}
}
