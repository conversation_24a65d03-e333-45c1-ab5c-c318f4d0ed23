package com.cszn.integrated.service.entity.msg.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMsgMessage<M extends BaseMsgMessage<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setMsgType(String msgType) {
		set("msg_type", msgType);
	}
	
	public String getMsgType() {
		return getStr("msg_type");
	}

	public void setSourceSystem(String sourceSystem) {
		set("source_system", sourceSystem);
	}
	
	public String getSourceSystem() {
		return getStr("source_system");
	}

	public void setLabel(String label) {
		set("label", label);
	}
	
	public String getLabel() {
		return getStr("label");
	}

	public void setTitle(String title) {
		set("title", title);
	}
	
	public String getTitle() {
		return getStr("title");
	}

	public void setContent(String content) {
		set("content", content);
	}
	
	public String getContent() {
		return getStr("content");
	}

	public void setUrl(String url) {
		set("url", url);
	}
	
	public String getUrl() {
		return getStr("url");
	}

	public void setMediaId(String mediaId) {
		set("media_id", mediaId);
	}
	
	public String getMediaId() {
		return getStr("media_id");
	}

	public void setMsgStatus(String msgStatus) {
		set("msg_status", msgStatus);
	}
	
	public String getMsgStatus() {
		return getStr("msg_status");
	}

	public void setAllFlag(String allFlag) {
		set("all_flag", allFlag);
	}
	
	public String getAllFlag() {
		return getStr("all_flag");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setExpireFlag(String expireFlag) {
		set("expire_flag", expireFlag);
	}
	
	public String getExpireFlag() {
		return getStr("expire_flag");
	}

	public void setExpireDate(java.util.Date expireDate) {
		set("expire_date", expireDate);
	}
	
	public java.util.Date getExpireDate() {
		return get("expire_date");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
