package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaExpenseRecordChangecard<M extends BaseFinaExpenseRecordChangecard<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setUniqueId(String uniqueId) {
		set("unique_id", uniqueId);
	}
	
	public String getUniqueId() {
		return getStr("unique_id");
	}

	public void setOriginalCardNumber(String originalCardNumber) {
		set("original_card_number", originalCardNumber);
	}
	
	public String getOriginalCardNumber() {
		return getStr("original_card_number");
	}

	public void setChangeCardNumber(String changeCardNumber) {
		set("change_card_number", changeCardNumber);
	}
	
	public String getChangeCardNumber() {
		return getStr("change_card_number");
	}

	public void setChangeType(String changeType) {
		set("change_type", changeType);
	}
	
	public String getChangeType() {
		return getStr("change_type");
	}

	public void setChangeTime(java.util.Date changeTime) {
		set("change_time", changeTime);
	}
	
	public java.util.Date getChangeTime() {
		return get("change_time");
	}

}
