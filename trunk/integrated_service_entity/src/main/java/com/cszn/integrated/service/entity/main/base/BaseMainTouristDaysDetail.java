package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainTouristDaysDetail<M extends BaseMainTouristDaysDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}
	
	public void setDaysId(String daysId) {
		set("days_id", daysId);
	}
	
	public String getDaysId() {
		return getStr("days_id");
	}
	
	public void setItemId(String itemId) {
		set("item_id", itemId);
	}
	
	public String getItemId() {
		return getStr("item_id");
	}

	public void setPlaceType(String placeType) {
		set("place_type", placeType);
	}
	
	public String getPlaceType() {
		return getStr("place_type");
	}
	
	public void setPlaceBase(String placeBase) {
		set("place_base", placeBase);
	}
	
	public String getPlaceBase() {
		return getStr("place_base");
	}
	
	public void setPlaceText(String placeText) {
		set("place_text", placeText);
	}
	
	public String getPlaceText() {
		return getStr("place_text");
	}
	
	public void setAge1(String age1) {
		set("age1", age1);
	}
	
	public String getAge1() {
		return getStr("age1");
	}
	
	public void setAge2(String age2) {
		set("age2", age2);
	}
	
	public String getAge2() {
		return getStr("age2");
	}
	
	public void setAge3(String age3) {
		set("age3", age3);
	}
	
	public String getAge3() {
		return getStr("age3");
	}
	
	public void setAge4(String age4) {
		set("age4", age4);
	}
	
	public String getAge4() {
		return getStr("age4");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
