package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseBedDynamicRecord<M extends BaseMainBaseBedDynamicRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setBedId(String bedId) {
		set("bed_id", bedId);
	}
	
	public String getBedId() {
		return getStr("bed_id");
	}

	public void setIsLongStay(Integer isLongStay) {
		set("is_long_stay", isLongStay);
	}
	
	public Integer getIsLongStay() {
		return getInt("is_long_stay");
	}

	public void setDynamicType(String dynamicType) {
		set("dynamic_type", dynamicType);
	}
	
	public String getDynamicType() {
		return getStr("dynamic_type");
	}

	public void setBookNo(String bookNo) {
		set("book_no", bookNo);
	}
	
	public String getBookNo() {
		return getStr("book_no");
	}

	public void setCheckinNo(String checkinNo) {
		set("checkin_no", checkinNo);
	}
	
	public String getCheckinNo() {
		return getStr("checkin_no");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setCardName(String cardName) {
		set("card_name", cardName);
	}
	
	public String getCardName() {
		return getStr("card_name");
	}

	public void setMemberName(String memberName) {
		set("member_name", memberName);
	}
	
	public String getMemberName() {
		return getStr("member_name");
	}

	public void setGender(String gender) {
		set("gender", gender);
	}
	
	public String getGender() {
		return getStr("gender");
	}

	public void setIdcard(String idcard) {
		set("idcard", idcard);
	}
	
	public String getIdcard() {
		return getStr("idcard");
	}

	public void setTelephone(String telephone) {
		set("telephone", telephone);
	}
	
	public String getTelephone() {
		return getStr("telephone");
	}

	public void setBookStartDate(java.util.Date bookStartDate) {
		set("book_start_date", bookStartDate);
	}
	
	public java.util.Date getBookStartDate() {
		return get("book_start_date");
	}

	public void setBookEndDate(java.util.Date bookEndDate) {
		set("book_end_date", bookEndDate);
	}
	
	public java.util.Date getBookEndDate() {
		return get("book_end_date");
	}

	public void setCheckinDate(java.util.Date checkinDate) {
		set("checkin_date", checkinDate);
	}
	
	public java.util.Date getCheckinDate() {
		return get("checkin_date");
	}

	public void setCheckoutDate(java.util.Date checkoutDate) {
		set("checkout_date", checkoutDate);
	}
	
	public java.util.Date getCheckoutDate() {
		return get("checkout_date");
	}

	public void setCompanyId(String companyId) {
		set("company_id", companyId);
	}

	public String getCompanyId() {
		return getStr("company_id");
	}

	public void setCompanyName(String companyName) {
		set("company_name", companyName);
	}
	
	public String getCompanyName() {
		return getStr("company_name");
	}

	public void setSaleId(String saleId) {
		set("sale_id", saleId);
	}
	
	public String getSaleId() {
		return getStr("sale_id");
	}

	public void setSyncStatus(Integer syncStatus) {
		set("sync_status", syncStatus);
	}
	
	public Integer getSyncStatus() {
		return getInt("sync_status");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
