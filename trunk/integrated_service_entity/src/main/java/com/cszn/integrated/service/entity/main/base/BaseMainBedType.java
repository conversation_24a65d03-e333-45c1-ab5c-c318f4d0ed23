package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBedType<M extends BaseMainBedType<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setTypeName(String typeName) {
		set("type_name", typeName);
	}
	
	public String getTypeName() {
		return getStr("type_name");
	}

	public void setFileId(String fileId) {
		set("file_id", fileId);
	}
	
	public String getFileId() {
		return getStr("file_id");
	}

	public void setBedTypeImage(String bedTypeImage) {
		set("bed_type_image", bedTypeImage);
	}
	
	public String getBedTypeImage() {
		return getStr("bed_type_image");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setIsManyPeople(String isManyPeople) {
		set("is_many_people", isManyPeople);
	}
	
	public String getIsManyPeople() {
		return getStr("is_many_people");
	}

	public void setIsBigBed(String isBigBed) {
		set("is_big_bed", isBigBed);
	}

	public String getIsBigBed() {
		return getStr("is_big_bed");
	}

	public void setPeopleMax(Integer peopleMax) {
		set("people_max", peopleMax);
	}
	
	public Integer getPeopleMax() {
		return getInt("people_max");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
