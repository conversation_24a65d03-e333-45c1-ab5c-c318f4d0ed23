package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardTransferPrice<M extends BaseFinaCardTransferPrice<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTransactionsId(String transactionsId) {
		set("transactions_id", transactionsId);
	}
	
	public String getTransactionsId() {
		return getStr("transactions_id");
	}

	public void setCountPrice(java.lang.Double countPrice) {
		set("count_price", countPrice);
	}
	
	public java.lang.Double getCountPrice() {
		return getDouble("count_price");
	}
	
	public void setCountDays(java.lang.Double countDays) {
		set("count_days", countDays);
	}
	
	public java.lang.Double getCountDays() {
		return getDouble("count_days");
	}
	
	public void setCountAmount(java.lang.Double countAmount) {
		set("count_amount", countAmount);
	}
	
	public java.lang.Double getCountAmount() {
		return getDouble("count_amount");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}
	
	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}
	
	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}
}
