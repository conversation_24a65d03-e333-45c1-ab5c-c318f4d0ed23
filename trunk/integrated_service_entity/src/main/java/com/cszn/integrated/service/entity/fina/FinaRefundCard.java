package com.cszn.integrated.service.entity.fina;

import java.util.List;

import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.fina.base.BaseFinaRefundCard;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_refund_card", primaryKey = "id")
public class FinaRefundCard extends BaseFinaRefundCard<FinaRefundCard> {
	
	private static final long serialVersionUID = -6025738120718800941L;
	
	private String recordListJson;
	private String fileListJson;
	private List<FinaRefundCardRecord> recordList;
	private List<CfsFileUpload> fileList;

	public String getRecordListJson() {
		return recordListJson;
	}

	public void setRecordListJson(String recordListJson) {
		this.recordListJson = recordListJson;
	}

	public String getFileListJson() {
		return fileListJson;
	}

	public void setFileListJson(String fileListJson) {
		this.fileListJson = fileListJson;
	}

	public List<FinaRefundCardRecord> getRecordList() {
		return recordList;
	}

	public void setRecordList(List<FinaRefundCardRecord> recordList) {
		this.recordList = recordList;
	}

	public List<CfsFileUpload> getFileList() {
		return fileList;
	}

	public void setFileList(List<CfsFileUpload> fileList) {
		this.fileList = fileList;
	}
}
