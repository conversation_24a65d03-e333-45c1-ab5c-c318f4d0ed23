package com.cszn.integrated.service.entity.food;

import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.food.base.BaseFoodInfo;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

/**
 * Generated by Jboot.
 */
@Table(tableName = "food_info", primaryKey = "id")
public class FoodInfo extends BaseFoodInfo<FoodInfo> {
	
	public String getCategoryName() {
		return Db.queryStr("select category_name from food_category where is_enabled='1' and id=?", this.getCategoryId());
	}
	
	public int getFileCount() {
		Integer fileCount = Db.queryInt("select count(id)fileCount from cfs_file_upload where del_flag='0' and relation_id=? group by relation_id", this.getId());
		if(fileCount==null) {
			fileCount=0;
		}
		return fileCount;
	}
}
