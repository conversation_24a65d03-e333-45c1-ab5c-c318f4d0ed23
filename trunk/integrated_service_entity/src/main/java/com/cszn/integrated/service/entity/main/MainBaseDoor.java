package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainBaseDoor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_base_door", primaryKey = "id")
public class MainBaseDoor extends BaseMainBaseDoor<MainBaseDoor> {
	
	public String getBaseName() {
		String baseName = "";
		final String baseId = this.getBaseId();
		Record door = Db.findFirst("select * from main_base where id=?", baseId);
		if(door!=null) {
			baseName = door.getStr("base_name");
		}
		return baseName;
	}
}
