package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseRoomDoor<M extends BaseMainBaseRoomDoor<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRoomId(String roomId) {
		set("room_id", roomId);
	}
	
	public String getRoomId() {
		return getStr("room_id");
	}

	public void setDoorId(String doorId) {
		set("door_id", doorId);
	}
	
	public String getDoorId() {
		return getStr("door_id");
	}
	
	public void setDoorSort(java.lang.Integer doorSort) {
		set("door_sort", doorSort);
	}
	
	public java.lang.Integer getDoorSort() {
		return getInt("door_sort");
	}
}
