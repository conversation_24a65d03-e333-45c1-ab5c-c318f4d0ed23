package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaApiRecord<M extends BaseFinaApiRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setApiName(String apiName) {
		set("api_name", apiName);
	}
	
	public String getApiName() {
		return getStr("api_name");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setOfficeId(String officeId) {
		set("office_id", officeId);
	}
	
	public String getOfficeId() {
		return getStr("office_id");
	}

	public void setInitialDate(String initialDate) {
		set("initialDate", initialDate);
	}
	
	public String getInitialDate() {
		return getStr("initialDate");
	}

	public void setData(String data) {
		set("data", data);
	}
	
	public String getData() {
		return getStr("data");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
