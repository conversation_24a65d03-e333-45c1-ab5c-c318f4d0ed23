package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsWarehouseLocations<M extends BaseWmsWarehouseLocations<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setWarehouseLocationNo(String warehouseLocationNo) {
		set("warehouse_location_no", warehouseLocationNo);
	}
	
	public String getWarehouseLocationNo() {
		return getStr("warehouse_location_no");
	}

	public void setWarehouseId(String warehouseId) {
		set("warehouse_id", warehouseId);
	}

	public String getWarehouseId() {
		return getStr("warehouse_id");
	}

	public void setWarehouseAreaId(String warehouseAreaId) {
		set("warehouse_area_id", warehouseAreaId);
	}
	
	public String getWarehouseAreaId() {
		return getStr("warehouse_area_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return get("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
