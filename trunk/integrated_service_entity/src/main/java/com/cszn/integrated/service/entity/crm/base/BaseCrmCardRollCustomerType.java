package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollCustomerType<M extends BaseCrmCardRollCustomerType<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardRollId(java.lang.String cardRollId) {
		set("card_roll_id", cardRollId);
	}
	
	public java.lang.String getCardRollId() {
		return getStr("card_roll_id");
	}

	public void setCustomerType(java.lang.String customerType) {
		set("customer_type", customerType);
	}
	
	public java.lang.String getCustomerType() {
		return getStr("customer_type");
	}

}
