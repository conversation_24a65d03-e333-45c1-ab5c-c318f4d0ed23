package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockLabelRel<M extends BaseWmsStockLabelRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setStockId(String stockId) {
		set("stock_id", stockId);
	}
	
	public String getStockId() {
		return getStr("stock_id");
	}

	public void setLabelId(String labelId) {
		set("label_id", labelId);
	}
	
	public String getLabelId() {
		return getStr("label_id");
	}

}
