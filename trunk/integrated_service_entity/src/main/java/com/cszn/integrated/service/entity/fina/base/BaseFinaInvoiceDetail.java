package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaInvoiceDetail<M extends BaseFinaInvoiceDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setInvoiceId(String invoiceId) {
		set("invoice_id", invoiceId);
	}
	
	public String getInvoiceId() {
		return getStr("invoice_id");
	}

	public void setUniqueId(String uniqueId) {
		set("unique_id", uniqueId);
	}
	
	public String getUniqueId() {
		return getStr("unique_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setUnit(String unit) {
		set("unit", unit);
	}
	
	public String getUnit() {
		return getStr("unit");
	}

	public void setUnitPrice(Double unitPrice) {
		set("unit_price", unitPrice);
	}
	
	public Double getUnitPrice() {
		return getDouble("unit_price");
	}

	public void setCount(Integer count) {
		set("count", count);
	}
	
	public Integer getCount() {
		return getInt("count");
	}

	public void setTaxRate(Float taxRate) {
		set("tax_rate", taxRate);
	}
	
	public Float getTaxRate() {
		return getFloat("tax_rate");
	}

	public void setTaxAmount(Double taxAmount) {
		set("tax_amount", taxAmount);
	}
	
	public Double getTaxAmount() {
		return getDouble("tax_amount");
	}

}
