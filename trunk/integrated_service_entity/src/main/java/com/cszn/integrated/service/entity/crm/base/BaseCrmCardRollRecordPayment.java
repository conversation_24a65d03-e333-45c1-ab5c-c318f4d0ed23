package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollRecordPayment<M extends BaseCrmCardRollRecordPayment<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRecordId(java.lang.String recordId) {
		set("record_id", recordId);
	}
	
	public java.lang.String getRecordId() {
		return getStr("record_id");
	}

	public void setRollNumber(java.lang.String rollNumber) {
		set("roll_number", rollNumber);
	}
	
	public java.lang.String getRollNumber() {
		return getStr("roll_number");
	}

	public void setCollectName(java.lang.String collectName) {
		set("collect_name", collectName);
	}
	
	public java.lang.String getCollectName() {
		return getStr("collect_name");
	}

	public void setCollectTime(java.util.Date collectTime) {
		set("collect_time", collectTime);
	}
	
	public java.util.Date getCollectTime() {
		return get("collect_time");
	}

	public void setCollectMoney(java.lang.Double collectMoney) {
		set("collect_money", collectMoney);
	}
	
	public java.lang.Double getCollectMoney() {
		return getDouble("collect_money");
	}

	public void setCollectChannel(java.lang.String collectChannel) {
		set("collect_channel", collectChannel);
	}
	
	public java.lang.String getCollectChannel() {
		return getStr("collect_channel");
	}

	public void setPayWay(java.lang.String payWay) {
		set("pay_way", payWay);
	}
	
	public java.lang.String getPayWay() {
		return getStr("pay_way");
	}

	public void setPayName(java.lang.String payName) {
		set("pay_name", payName);
	}
	
	public java.lang.String getPayName() {
		return getStr("pay_name");
	}

	public void setPayPhone(java.lang.String payPhone) {
		set("pay_phone", payPhone);
	}
	
	public java.lang.String getPayPhone() {
		return getStr("pay_phone");
	}

	public void setPayBank(java.lang.String payBank) {
		set("pay_bank", payBank);
	}
	
	public java.lang.String getPayBank() {
		return getStr("pay_bank");
	}

	public void setPayAccount(java.lang.String payAccount) {
		set("pay_account", payAccount);
	}
	
	public java.lang.String getPayAccount() {
		return getStr("pay_account");
	}

	public void setPayStatus(java.lang.String payStatus) {
		set("pay_status", payStatus);
	}
	
	public java.lang.String getPayStatus() {
		return getStr("pay_status");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
