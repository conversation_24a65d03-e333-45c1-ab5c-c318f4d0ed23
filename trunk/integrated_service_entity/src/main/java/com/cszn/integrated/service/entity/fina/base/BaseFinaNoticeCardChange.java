package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaNoticeCardChange<M extends BaseFinaNoticeCardChange<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setRecordNo(String recordNo) {
		set("record_no", recordNo);
	}
	
	public String getRecordNo() {
		return getStr("record_no");
	}

	public void setUniqueId(String uniqueId) {
		set("unique_id", uniqueId);
	}
	
	public String getUniqueId() {
		return getStr("unique_id");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setIsContinue(String isContinue) {
		set("is_continue", isContinue);
	}
	
	public String getIsContinue() {
		return getStr("is_continue");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

}
