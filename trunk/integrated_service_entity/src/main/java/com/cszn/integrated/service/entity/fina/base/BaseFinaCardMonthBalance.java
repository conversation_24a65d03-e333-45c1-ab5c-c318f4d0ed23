package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardMonthBalance<M extends BaseFinaCardMonthBalance<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setTimes(Double times) {
		set("times", times);
	}
	
	public Double getTimes() {
		return getDouble("times");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setPoints(Double points) {
		set("points", points);
	}
	
	public Double getPoints() {
		return getDouble("points");
	}

	public void setIntegrals(Double integrals) {
		set("integrals", integrals);
	}
	
	public Double getIntegrals() {
		return getDouble("integrals");
	}

	public void setBeanCoupons(Double beanCoupons) {
		set("bean_coupons", beanCoupons);
	}
	
	public Double getBeanCoupons() {
		return getDouble("bean_coupons");
	}

	public void setYearMonth(String yearMonth) {
		set("year_month", yearMonth);
	}
	
	public String getYearMonth() {
		return getStr("year_month");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

	public void setLastMonthTimes(Double lastMonthTimes) {
		set("last_month_times", lastMonthTimes);
	}
	
	public Double getLastMonthTimes() {
		return getDouble("last_month_times");
	}

	public void setLastMonthAmount(Double lastMonthAmount) {
		set("last_month_amount", lastMonthAmount);
	}
	
	public Double getLastMonthAmount() {
		return getDouble("last_month_amount");
	}

	public void setLastMonthPoints(Double lastMonthPoints) {
		set("last_month_points", lastMonthPoints);
	}
	
	public Double getLastMonthPoints() {
		return getDouble("last_month_points");
	}

	public void setLastMonthIntegrals(Double lastMonthIntegrals) {
		set("last_month_integrals", lastMonthIntegrals);
	}
	
	public Double getLastMonthIntegrals() {
		return getDouble("last_month_integrals");
	}

	public void setLastMonthBeanCoupons(Double lastMonthBeanCoupons) {
		set("last_month_bean_coupons", lastMonthBeanCoupons);
	}
	
	public Double getLastMonthBeanCoupons() {
		return getDouble("last_month_bean_coupons");
	}

	public void setConsumeTimes(Double consumeTimes) {
		set("consume_times", consumeTimes);
	}
	
	public Double getConsumeTimes() {
		return getDouble("consume_times");
	}

	public void setConsumeAmount(Double consumeAmount) {
		set("consume_amount", consumeAmount);
	}
	
	public Double getConsumeAmount() {
		return getDouble("consume_amount");
	}

	public void setConsumePoints(Double consumePoints) {
		set("consume_points", consumePoints);
	}
	
	public Double getConsumePoints() {
		return getDouble("consume_points");
	}

	public void setConsumeIntegrals(Double consumeIntegrals) {
		set("consume_integrals", consumeIntegrals);
	}
	
	public Double getConsumeIntegrals() {
		return getDouble("consume_integrals");
	}

	public void setConsumeBeanCoupons(Double consumeBeanCoupons) {
		set("consume_bean_coupons", consumeBeanCoupons);
	}
	
	public Double getConsumeBeanCoupons() {
		return getDouble("consume_bean_coupons");
	}

	public void setRechargeTimes(Double rechargeTimes) {
		set("recharge_times", rechargeTimes);
	}
	
	public Double getRechargeTimes() {
		return getDouble("recharge_times");
	}

	public void setRechargeAmount(Double rechargeAmount) {
		set("recharge_amount", rechargeAmount);
	}
	
	public Double getRechargeAmount() {
		return getDouble("recharge_amount");
	}

	public void setRechargePoints(Double rechargePoints) {
		set("recharge_points", rechargePoints);
	}
	
	public Double getRechargePoints() {
		return getDouble("recharge_points");
	}

	public void setRechargeIntegrals(Double rechargeIntegrals) {
		set("recharge_integrals", rechargeIntegrals);
	}
	
	public Double getRechargeIntegrals() {
		return getDouble("recharge_integrals");
	}

	public void setRechargeBeanCoupons(Double rechargeBeanCoupons) {
		set("recharge_bean_coupons", rechargeBeanCoupons);
	}
	
	public Double getRechargeBeanCoupons() {
		return getDouble("recharge_bean_coupons");
	}

	public void setGiveIntegrals(Double giveIntegrals) {
		set("give_integrals", giveIntegrals);
	}

	public Double getGiveIntegrals() {
		return getDouble("give_integrals");
	}

	/*public void setLockTimes(Double lockTimes) {
		set("lock_times", lockTimes);
	}

	public Double getLockTimes() {
		return getDouble("lock_times");
	}

	public void setLockAmount(Double lockAmount) {
		set("lock_amount", lockAmount);
	}

	public Double getLockAmount() {
		return getDouble("lock_amount");
	}

	public void setLockPoints(Double lockPoints) {
		set("lock_points", lockPoints);
	}

	public Double getLockPoints() {
		return getDouble("recharge_points");
	}

	public void setLockIntegrals(Double lockIntegrals) {
		set("lock_integrals", lockIntegrals);
	}

	public Double getLockIntegrals() {
		return getDouble("lock_integrals");
	}

	public void setLockBeanCoupons(Double lockBeanCoupons) {
		set("lock_bean_coupons", lockBeanCoupons);
	}

	public Double getLockBeanCoupons() {
		return getDouble("lock_bean_coupons");
	}
*/
}
