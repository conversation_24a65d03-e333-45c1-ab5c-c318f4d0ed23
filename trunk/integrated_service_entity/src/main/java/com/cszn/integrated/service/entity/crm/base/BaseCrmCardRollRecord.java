package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollRecord<M extends BaseCrmCardRollRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardRollId(java.lang.String cardRollId) {
		set("card_roll_id", cardRollId);
	}
	
	public java.lang.String getCardRollId() {
		return getStr("card_roll_id");
	}

	public void setRollNumber(java.lang.String rollNumber) {
		set("roll_number", rollNumber);
	}
	
	public java.lang.String getRollNumber() {
		return getStr("roll_number");
	}

	public void setRollQrcode(java.lang.String rollQrcode) {
		set("roll_qrcode", rollQrcode);
	}
	
	public java.lang.String getRollQrcode() {
		return getStr("roll_qrcode");
	}

	public void setRollValue(Double rollValue) {
		set("roll_value", rollValue);
	}

	public Double getRollValue() {
		return get("roll_value");
	}

	public void setBalanceRollValue(Double balanceRollValue) {
		set("balance_roll_value", balanceRollValue);
	}

	public Double getBalanceRollValue() {
		return get("balance_roll_value");
	}

	public void setIsEnable(java.lang.String isEnable) { set("is_enable", isEnable); }

	public java.lang.String getIsEnable() { return getStr("is_enable"); }

	public void setIsUse(java.lang.String isUse) {
		set("is_use", isUse);
	}
	
	public java.lang.String getIsUse() {
		return getStr("is_use");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

	public void setExpirationTime(java.util.Date expirationTime) {
		set("expiration_time", expirationTime);
	}

	public java.util.Date getExpirationTime() {
		return get("expiration_time");
	}

}
