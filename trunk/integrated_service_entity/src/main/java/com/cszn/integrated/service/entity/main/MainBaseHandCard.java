package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainBaseHandCard;
import com.cszn.integrated.service.entity.status.HandCardCategory;
import com.jfinal.kit.StrKit;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_base_hand_card", primaryKey = "id")
public class MainBaseHandCard extends BaseMainBaseHandCard<MainBaseHandCard> {
	
	public String getCategoryName() {
		String categoryName = "";
		final String handCardCategory = this.getHandCardCategory();
		if(StrKit.notBlank(handCardCategory)) {
			categoryName = HandCardCategory.me().desc(handCardCategory);
		}
		return categoryName;
	}
}
