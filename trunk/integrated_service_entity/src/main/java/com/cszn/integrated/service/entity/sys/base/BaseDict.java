package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseDict<M extends BaseDict<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setDictType(java.lang.String dictType) {
		set("dict_type", dictType);
	}
	
	public java.lang.String getDictTypeName() {
		return getStr("dict_type_name");
	}
	
	public void setDictTypeName(java.lang.String dictTypeName) {
		set("dict_type_name", dictTypeName);
	}
	
	public java.lang.String getDictType() {
		return getStr("dict_type");
	}

	public void setDictName(java.lang.String dictName) {
		set("dict_name", dictName);
	}
	
	public java.lang.String getDictName() {
		return getStr("dict_name");
	}

	public void setDictValue(java.lang.String dictValue) {
		set("dict_value", dictValue);
	}
	
	public java.lang.String getDictValue() {
		return getStr("dict_value");
	}

	public void setDictSort(java.lang.Integer dictSort) {
		set("dict_sort", dictSort);
	}
	
	public java.lang.Integer getDictSort() {
		return getInt("dict_sort");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
