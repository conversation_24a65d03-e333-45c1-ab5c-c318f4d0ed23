package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBedChannel<M extends BaseMainBedChannel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBedId(String bedId) {
		set("bed_id", bedId);
	}
	
	public String getBedId() {
		return getStr("bed_id");
	}

	public void setChannelId(String channelId) {
		set("channel_id", channelId);
	}
	
	public String getChannelId() {
		return getStr("channel_id");
	}

}
