package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainRoomType;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.db.annotation.Table;


/**
 * Generated by Jboot.
 */
@Table(tableName = "main_room_type", primaryKey = "id")
public class MainRoomType extends BaseMainRoomType<MainRoomType> {
	public String getBaseName(){
	    return Db.queryStr("select base_name from main_base where id=? ",this.getBaseId());
    }
}
