package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaConditionRechargeSchemeDetail<M extends BaseFinaConditionRechargeSchemeDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSchemeId(String schemeId) {
		set("scheme_id", schemeId);
	}
	
	public String getSchemeId() {
		return getStr("scheme_id");
	}

	public void setConditionType(String conditionType) {
		set("condition_type", conditionType);
	}
	
	public String getConditionType() {
		return getStr("condition_type");
	}

	public void setConditionValue(Double conditionValue) {
		set("condition_value", conditionValue);
	}
	
	public Double getConditionValue() {
		return getDouble("condition_value");
	}

	public void setRechargeType(String rechargeType) {
		set("recharge_type", rechargeType);
	}
	
	public String getRechargeType() {
		return getStr("recharge_type");
	}

	public void setRechargeValue(Double rechargeValue) {
		set("recharge_value", rechargeValue);
	}
	
	public Double getRechargeValue() {
		return getDouble("recharge_value");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}

	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
