package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardTransactionsPrice<M extends BaseFinaCardTransactionsPrice<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}
	
	public void setMontylyEndId(String montylyEndId) {
		set("montyly_end_id", montylyEndId);
	}
	
	public String getMontylyEndId() {
		return getStr("montyly_end_id");
	}

	public void setTransactionsId(String transactionsId) {
		set("transactions_id", transactionsId);
	}
	
	public String getTransactionsId() {
		return getStr("transactions_id");
	}
	
	public void setYearMonth(String yearMonth) {
		set("year_month", yearMonth);
	}
	
	public String getYearMonth() {
		return getStr("year_month");
	}

	public void setCountPrice(Double countPrice) {
		set("count_price", countPrice);
	}
	
	public Double getCountPrice() {
		return getDouble("count_price");
	}

	public void setCountDays(Double countDays) {
		set("count_days", countDays);
	}
	
	public Double getCountDays() {
		return getDouble("count_days");
	}
	
	public void setCountIntegrals(Double countIntegrals) {
		set("count_integrals", countIntegrals);
	}
	
	public Double getCountIntegrals() {
		return getDouble("count_integrals");
	}

	public void setCountAmount(Double countAmount){
		set("count_amount",countAmount);
	}

	public Double getCountAmount(){
		return getDouble("count_amount");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}
	
	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_date");
	}
}
