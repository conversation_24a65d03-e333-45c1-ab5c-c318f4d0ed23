package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaExpenseRecord<M extends BaseFinaExpenseRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setParentId(String parentId) {
		set("parent_id", parentId);
	}
	
	public String getParentId() {
		return getStr("parent_id");
	}

	public void setExpenseNo(String expenseNo) {
		set("expense_no", expenseNo);
	}
	
	public String getExpenseNo() {
		return getStr("expense_no");
	}

	public void setIsChangeBed(String isChangeBed) {
		set("is_change_bed", isChangeBed);
	}

	public String getIsChangeBed() {
		return getStr("is_change_bed");
	}

	public void setCategory(String category) {
		set("category", category);
	}
	
	public String getCategory() {
		return getStr("category");
	}

	public void setIsNotDeductCard(String isNotDeductCard) {
		set("is_not_deduct_card", isNotDeductCard);
	}

	public String getIsNotDeductCard() {
		return getStr("is_not_deduct_card");
	}

	public void setBusinessEntityId(String businessEntityId) {
		set("business_entity_id", businessEntityId);
	}

	public String getBusinessEntityId() {
		return getStr("business_entity_id");
	}

	public void setCheckinType(String checkinType) {
		set("checkin_type", checkinType);
	}

	public String getCheckinType() {
		return getStr("checkin_type");
	}

	public void setCustomerChannel(String customerChannel) {
		set("customer_channel", customerChannel);
	}

	public String getCustomerChannel() {
		return getStr("customer_channel");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}
	
	public String getUserId() {
		return getStr("user_id");
	}

	public void setUnionid(String unionid) {
		set("unionid", unionid);
	}
	
	public String getUnionid() {
		return getStr("unionid");
	}

	public void setTouristNo(String touristNo) {
		set("tourist_no", touristNo);
	}
	
	public String getTouristNo() {
		return getStr("tourist_no");
	}

	public void setBookNo(String bookNo) {
		set("book_no", bookNo);
	}
	
	public String getBookNo() {
		return getStr("book_no");
	}

	public void setUniqueId(String uniqueId) {
		set("unique_id", uniqueId);
	}
	
	public String getUniqueId() {
		return getStr("unique_id");
	}

	public void setCheckinNo(String checkinNo) {
		set("checkin_no", checkinNo);
	}
	
	public String getCheckinNo() {
		return getStr("checkin_no");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setIdCard(String idCard) {
		set("id_card", idCard);
	}
	
	public String getIdCard() {
		return getStr("id_card");
	}

	public void setTelephone(String telephone) {
		set("telephone", telephone);
	}
	
	public String getTelephone() {
		return getStr("telephone");
	}

	public void setBedId(String bedId) {
		set("bed_id", bedId);
	}
	
	public String getBedId() {
		return getStr("bed_id");
	}

	public void setIsPrivateRoom(String isPrivateRoom) {
		set("is_private_room", isPrivateRoom);
	}
	
	public String getIsPrivateRoom() {
		return getStr("is_private_room");
	}

	public void setBookStartTime(java.util.Date bookStartTime) {
		set("book_start_time", bookStartTime);
	}
	
	public java.util.Date getBookStartTime() {
		return get("book_start_time");
	}

	public void setBookEndTime(java.util.Date bookEndTime) {
		set("book_end_time", bookEndTime);
	}
	
	public java.util.Date getBookEndTime() {
		return get("book_end_time");
	}

	public void setCheckinTime(java.util.Date checkinTime) {
		set("checkin_time", checkinTime);
	}
	
	public java.util.Date getCheckinTime() {
		return get("checkin_time");
	}

	public void setCheckoutTime(java.util.Date checkoutTime) {
		set("checkout_time", checkoutTime);
	}
	
	public java.util.Date getCheckoutTime() {
		return get("checkout_time");
	}

	public void setRealCheckoutTime(java.util.Date realCheckoutTime) {
		set("real_checkout_time", realCheckoutTime);
	}
	
	public java.util.Date getRealCheckoutTime() {
		return get("real_checkout_time");
	}

	public void setCheckinStatus(String checkinStatus) {
		set("checkin_status", checkinStatus);
	}
	
	public String getCheckinStatus() {
		return getStr("checkin_status");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setWithholdTimes(Double withholdTimes) {
		set("withhold_times", withholdTimes);
	}
	
	public Double getWithholdTimes() {
		return getDouble("withhold_times");
	}

	public void setActualTimes(Double actualTimes) {
		set("actual_times", actualTimes);
	}
	
	public Double getActualTimes() {
		return getDouble("actual_times");
	}

	public void setWithholdAmount(Double withholdAmount) {
		set("withhold_amount", withholdAmount);
	}
	
	public Double getWithholdAmount() {
		return getDouble("withhold_amount");
	}

	public void setActualAmount(Double actualAmount) {
		set("actual_amount", actualAmount);
	}
	
	public Double getActualAmount() {
		return getDouble("actual_amount");
	}

	public void setWithholdPoints(Double withholdPoints) {
		set("withhold_points", withholdPoints);
	}

	public Double getWithholdPoints() {
		return getDouble("withhold_points");
	}

	public void setActualPoints(Double actualPoints) {
		set("actual_points", actualPoints);
	}

	public Double getActualPoints() {
		return getDouble("actual_points");
	}

	public void setWithholdIntegrals(Double withholdIntegrals) {
		set("withhold_integrals", withholdIntegrals);
	}

	public Double getWithholdIntegrals() {
		return getDouble("withhold_integrals");
	}

	public void setActualIntegrals(Double actualIntegrals) {
		set("actual_integrals", actualIntegrals);
	}

	public Double getActualIntegrals() {
		return getDouble("actual_integrals");
	}
	
	public void setWithholdBeanCoupons(Double withholdBeanCoupons) {
		set("withhold_bean_coupons", withholdBeanCoupons);
	}
	
	public Double getWithholdBeanCoupons() {
		return getDouble("withhold_bean_coupons");
	}
	
	public void setActualBeanCoupons(Double actualBeanCoupons) {
		set("actual_bean_coupons", actualBeanCoupons);
	}
	
	public Double getActualBeanCoupons() {
		return getDouble("actual_bean_coupons");
	}

	public void setSettleTime(java.util.Date settleTime) {
		set("settle_time", settleTime);
	}
	
	public java.util.Date getSettleTime() {
		return get("settle_time");
	}

	public void setSettleStatus(String settleStatus) {
		set("settle_status", settleStatus);
	}
	
	public String getSettleStatus() {
		return getStr("settle_status");
	}

	public void setOfficeId(String officeId) {
		set("office_id", officeId);
	}
	
	public String getOfficeId() {
		return getStr("office_id");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setFinanceRemark(String financeRemark) {
		set("finance_remark", financeRemark);
	}
	
	public String getFinanceRemark() {
		return getStr("finance_remark");
	}

	public void setIsNoMonthlyEnd(String isNoMonthlyEnd) {set("is_no_monthly_end", isNoMonthlyEnd);}

	public String getIsNoMonthlyEnd() {return getStr("is_no_monthly_end");}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
