package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainExamineRecord<M extends BaseMainExamineRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setDeptId(String deptId) {
		set("dept_id", deptId);
	}
	
	public String getDeptId() {
		return getStr("dept_id");
	}

	public void setAnswerUserId(String answerUserId) {
		set("answer_user_id", answerUserId);
	}
	
	public String getAnswerUserId() {
		return getStr("answer_user_id");
	}

	public void setStartTime(java.util.Date startTime) {
		set("start_time", startTime);
	}
	
	public java.util.Date getStartTime() {
		return get("start_time");
	}

	public void setEndTime(java.util.Date endTime) {
		set("end_time", endTime);
	}
	
	public java.util.Date getEndTime() {
		return get("end_time");
	}

	public void setCompleteTime(java.util.Date completeTime) {
		set("complete_time", completeTime);
	}

	public java.util.Date getCompleteTime() {
		return get("complete_time");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setTotalScore(Double totalScore) {
		set("total_score", totalScore);
	}
	
	public Double getTotalScore() {
		return getDouble("total_score");
	}

	public void setScore(Double score) {
		set("score", score);
	}
	
	public Double getScore() {
		return getDouble("score");
	}

	public void setScoreRate(Double scoreRate) {
		set("score_rate", scoreRate);
	}

	public Double getScoreRate() {
		return getDouble("score_rate");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}

	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setSchemeId(String schemeId) {
		set("scheme_id", schemeId);
	}

	public String getSchemeId() {
		return getStr("scheme_id");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
