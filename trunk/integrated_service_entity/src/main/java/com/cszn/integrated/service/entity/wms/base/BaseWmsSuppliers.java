package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsSuppliers<M extends BaseWmsSuppliers<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSupplierNo(String supplierNo) {
		set("supplier_no", supplierNo);
	}
	
	public String getSupplierNo() {
		return getStr("supplier_no");
	}

	public void setType(String type) {
		set("type", type);
	}

	public String getType() {
		return getStr("type");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setIdcardType(String idcardType) {
		set("idcard_type", idcardType);
	}

	public String getIdcardType() {
		return getStr("idcard_type");
	}

	public void setIdcard(String idcard) {
		set("idcard", idcard);
	}

	public String getIdcard() {
		return getStr("idcard");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}

	public String getCardId() {
		return getStr("card_id");
	}
	
	public void setLinkId(String linkId) {
		set("link_id", linkId);
	}
	
	public String getLinkId() {
		return getStr("link_id");
	}
	
	public void setLinkName(String linkName) {
		set("link_name", linkName);
	}
	
	public String getLinkName() {
		return getStr("link_name");
	}

	public void setTelephone(String telephone) {
		set("telephone", telephone);
	}

	public String getTelephone() {
		return getStr("telephone");
	}
	
	public void setLinkPosition(String linkPosition) {
		set("link_position", linkPosition);
	}
	
	public String getLinkPosition() {
		return getStr("link_position");
	}
	
	public void setLinkPhone(String linkPhone) {
		set("link_phone", linkPhone);
	}
	
	public String getLinkPhone() {
		return getStr("link_phone");
	}
	
	public void setLinkWechat(String linkWechat) {
		set("link_wechat", linkWechat);
	}
	
	public String getLinkWechat() {
		return getStr("link_wechat");
	}
	
	public void setPostalCode(String postalCode) {
		set("postal_code", postalCode);
	}
	
	public String getPostalCode() {
		return getStr("postal_code");
	}
	
	public void setFaxNumber(String faxNumber) {
		set("fax_number", faxNumber);
	}
	
	public String getFaxNumber() {
		return getStr("fax_number");
	}
	
	public void setFixedTelephone(String fixedTelephone) {
		set("fixed_telephone", fixedTelephone);
	}
	
	public String getFixedTelephone() {
		return getStr("fixed_telephone");
	}
	
	public void setMailBox(String mailBox) {
		set("mail_box", mailBox);
	}
	
	public String getMailBox() {
		return getStr("mail_box");
	}
	
	public void setSocialCreditCode(String socialCreditCode) {
		set("social_credit_code", socialCreditCode);
	}
	
	public String getSocialCreditCode() {
		return getStr("social_credit_code");
	}
	
	public void setOpenName(String openName) {
		set("open_name", openName);
	}
	
	public String getOpenName() {
		return getStr("open_name");
	}
	
	public void setOpenBank(String openBank) {
		set("open_bank", openBank);
	}
	
	public String getOpenBank() {
		return getStr("open_bank");
	}
	
	public void setBankAccount(String bankAccount) {
		set("bank_account", bankAccount);
	}
	
	public String getBankAccount() {
		return getStr("bank_account");
	}
	
	public void setInvoiceHeader(String invoiceHeader) {
		set("invoice_header", invoiceHeader);
	}
	
	public String getInvoiceHeader() {
		return getStr("invoice_header");
	}

	public void setStartTime(java.util.Date startTime) {
		set("start_time", startTime);
	}
	
	public java.util.Date getStartTime() {
		return get("start_time");
	}
	
	public void setCountry(String country) {
		set("country", country);
	}
	
	public String getCountry() {
		return getStr("country");
	}

	public void setProvince(String province) {
		set("province", province);
	}
	
	public String getProvince() {
		return getStr("province");
	}

	public void setCity(String city) {
		set("city", city);
	}
	
	public String getCity() {
		return getStr("city");
	}

	public void setTown(String town) {
		set("town", town);
	}
	
	public String getTown() {
		return getStr("town");
	}

	public void setStreet(String street) {
		set("street", street);
	}
	
	public String getStreet() {
		return getStr("street");
	}
	
	public void setSupplierAddress(String supplierAddress) {
		set("supplier_address", supplierAddress);
	}
	
	public String getSupplierAddress() {
		return get("supplier_address");
	}
	
	public void setProductNature(String productNature) {
		set("product_nature", productNature);
	}
	
	public String getProductNature() {
		return get("product_nature");
	}
	
	public void setCooperateRelation(String cooperateRelation) {
		set("cooperate_relation", cooperateRelation);
	}
	
	public String getCooperateRelation() {
		return get("cooperate_relation");
	}
	
	public void setCompanyNature(String companyNature) {
		set("company_nature", companyNature);
	}
	
	public String getCompanyNature() {
		return get("company_nature");
	}
	
	public void setSupplierLv(String supplierLv) {
		set("supplier_lv", supplierLv);
	}
	
	public String getSupplierLv() {
		return get("supplier_lv");
	}
	
	public void setPaymentWay(String paymentWay) {
		set("payment_way", paymentWay);
	}
	
	public String getPaymentWay() {
		return get("payment_way");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return get("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return get("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
