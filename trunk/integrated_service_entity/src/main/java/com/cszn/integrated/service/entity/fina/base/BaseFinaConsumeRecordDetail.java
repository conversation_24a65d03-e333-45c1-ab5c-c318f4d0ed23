package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaConsumeRecordDetail<M extends BaseFinaConsumeRecordDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setConsumeId(String consumeId) {
		set("consume_id", consumeId);
	}
	
	public String getConsumeId() {
		return getStr("consume_id");
	}

	public void setAppDetailNo(String appDetailNo) {
		set("app_detail_no", appDetailNo);
	}
	
	public String getAppDetailNo() {
		return getStr("app_detail_no");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setConsumeTimes(Integer consumeTimes) {
		set("consume_times", consumeTimes);
	}
	
	public Integer getConsumeTimes() {
		return getInt("consume_times");
	}

	public void setPoints(Double points) {
		set("points", points);
	}
	
	public Double getPoints() {
		return getDouble("points");
	}

	public void setTakeTime(java.util.Date takeTime) {
		set("take_time", takeTime);
	}
	
	public java.util.Date getTakeTime() {
		return get("take_time");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
