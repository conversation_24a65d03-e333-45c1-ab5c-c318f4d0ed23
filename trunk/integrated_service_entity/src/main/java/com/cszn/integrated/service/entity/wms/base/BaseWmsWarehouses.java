package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsWarehouses<M extends BaseWmsWarehouses<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setWarehouseOrder(Integer warehouseOrder) {
		set("warehouse_order", warehouseOrder);
	}
	
	public Integer getWarehouseOrder() {
		return getInt("warehouse_order");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}

	public String getBaseId() {
		return getStr("base_id");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return get("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setPosSubmitUserIds(String posSubmitUserIds) {
		set("pos_submit_user_ids", posSubmitUserIds);
	}

	public String getPosSubmitUserIds() {
		return getStr("pos_submit_user_ids");
	}

	public void setPosHandlerUserIds(String posHandlerUserIds) {
		set("pos_handler_user_ids", posHandlerUserIds);
	}

	public String getPosHandlerUserIds() {
		return getStr("pos_handler_user_ids");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
