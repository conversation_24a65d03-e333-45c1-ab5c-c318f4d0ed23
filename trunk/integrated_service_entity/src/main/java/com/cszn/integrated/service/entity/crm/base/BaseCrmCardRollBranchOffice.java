package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollBranchOffice<M extends BaseCrmCardRollBranchOffice<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardRollId(java.lang.String cardRollId) {
		set("card_roll_id", cardRollId);
	}
	
	public java.lang.String getCardRollId() {
		return getStr("card_roll_id");
	}

	public void setBranchOfficeId(java.lang.String branchOfficeId) {
		set("branch_office_id", branchOfficeId);
	}
	
	public java.lang.String getBranchOfficeId() {
		return getStr("branch_office_id");
	}

}
