package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainRoomTypeSecondLevel<M extends BaseMainRoomTypeSecondLevel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setFirstLevelId(String firstLevelId) {
		set("first_level_id", firstLevelId);
	}

	public String getFirstLevelId() {
		return getStr("first_level_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setMemberRoomDeductTimes(Double memberRoomDeductTimes) {
		set("member_room_deduct_times", memberRoomDeductTimes);
	}
	
	public Double getMemberRoomDeductTimes() {
		return getDouble("member_room_deduct_times");
	}

	public void setStaffRoomDeductTimes(Double staffRoomDeductTimes) {
		set("staff_room_deduct_times", staffRoomDeductTimes);
	}
	
	public Double getStaffRoomDeductTimes() {
		return getDouble("staff_room_deduct_times");
	}

	public void setOutsiderRoomDeductTimes(Double outsiderRoomDeductTimes) {
		set("outsider_room_deduct_times", outsiderRoomDeductTimes);
	}
	
	public Double getOutsiderRoomDeductTimes() {
		return getDouble("outsider_room_deduct_times");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}

	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsBigBedRoom(String isBigBedRoom) {
		set("is_big_bed_room", isBigBedRoom);
	}

	public String getIsBigBedRoom() {
		return getStr("is_big_bed_room");
	}

	public void setIsCalculateOccupancyRate(String isCalculateOccupancyRate) {
		set("is_calculate_occupancy_rate", isCalculateOccupancyRate);
	}

	public String getIsCalculateOccupancyRate() {
		return getStr("is_calculate_occupancy_rate");
	}

	public void setBookSort(Integer bookSort) {
		set("book_sort", bookSort);
	}

	public Integer getBookSort() {
		return get("book_sort");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
