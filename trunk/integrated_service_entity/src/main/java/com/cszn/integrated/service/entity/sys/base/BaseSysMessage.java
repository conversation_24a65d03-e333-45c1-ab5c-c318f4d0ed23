package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseSysMessage<M extends BaseSysMessage<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setUserId(java.lang.String userId) {
		set("user_id", userId);
	}
	
	public java.lang.String getUserId() {
		return getStr("user_id");
	}

	public void setRelationId(java.lang.String relationId) {
		set("relation_id", relationId);
	}
	
	public java.lang.String getRelationId() {
		return getStr("relation_id");
	}

	public void setBelongSystem(java.lang.String belongSystem) {
		set("belong_system", belongSystem);
	}
	
	public java.lang.String getBelongSystem() {
		return getStr("belong_system");
	}

	public void setMsgCategory(java.lang.String msgCategory) {
		set("msg_category", msgCategory);
	}
	
	public java.lang.String getMsgCategory() {
		return getStr("msg_category");
	}

	public void setMsgUrl(java.lang.String msgUrl) {
		set("msg_url", msgUrl);
	}
	
	public java.lang.String getMsgUrl() {
		return getStr("msg_url");
	}

	public void setMsgTitle(java.lang.String msgTitle) {
		set("msg_title", msgTitle);
	}
	
	public java.lang.String getMsgTitle() {
		return getStr("msg_title");
	}

	public void setIsRead(java.lang.String isRead) {
		set("is_read", isRead);
	}
	
	public java.lang.String getIsRead() {
		return getStr("is_read");
	}

	public void setIsReadTime(java.util.Date isReadTime) {
		set("is_read_time", isReadTime);
	}
	
	public java.util.Date getIsReadTime() {
		return get("is_read_time");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
