package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBranchOfficeUser<M extends BaseMainBranchOfficeUser<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBranchOfficeId(String branchOfficeId) {
		set("branch_office_id", branchOfficeId);
	}
	
	public String getBranchOfficeId() {
		return getStr("branch_office_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}
	
	public String getUserId() {
		return getStr("user_id");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
