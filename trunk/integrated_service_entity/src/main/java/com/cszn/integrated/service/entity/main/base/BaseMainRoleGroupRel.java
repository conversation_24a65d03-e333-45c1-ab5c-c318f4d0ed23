package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainRoleGroupRel<M extends BaseMainRoleGroupRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setUserRoleId(String userRoleId) {
		set("user_role_id", userRoleId);
	}
	
	public String getUserRoleId() {
		return getStr("user_role_id");
	}

	public void setUserGroupId(String userGroupId) {
		set("user_group_id", userGroupId);
	}
	
	public String getUserGroupId() {
		return getStr("user_group_id");
	}

}
