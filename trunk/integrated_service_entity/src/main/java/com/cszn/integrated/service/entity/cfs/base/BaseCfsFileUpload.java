package com.cszn.integrated.service.entity.cfs.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCfsFileUpload<M extends BaseCfsFileUpload<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRelationId(java.lang.String relationId) {
		set("relation_id", relationId);
	}
	
	public java.lang.String getRelationId() {
		return getStr("relation_id");
	}
	
	public void setFileFolder(java.lang.String fileFolder) {
		set("file_folder", fileFolder);
	}
	
	public java.lang.String getFileFolder() {
		return getStr("file_folder");
	}

	public void setFileName(java.lang.String fileName) {
		set("file_name", fileName);
	}
	
	public java.lang.String getFileName() {
		return getStr("file_name");
	}

	public void setFileSize(java.lang.String fileSize) {
		set("file_size", fileSize);
	}
	
	public java.lang.String getFileSize() {
		return getStr("file_size");
	}

	public void setFileSuffix(java.lang.String fileSuffix) {
		set("file_suffix", fileSuffix);
	}
	
	public java.lang.String getFileSuffix() {
		return getStr("file_suffix");
	}

	public void setFilePath(java.lang.String filePath) {
		set("file_path", filePath);
	}
	
	public java.lang.String getFilePath() {
		return getStr("file_path");
	}

	public void setFileUrl(java.lang.String fileUrl) {
		set("file_url", fileUrl);
	}
	
	public java.lang.String getFileUrl() {
		return getStr("file_url");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
