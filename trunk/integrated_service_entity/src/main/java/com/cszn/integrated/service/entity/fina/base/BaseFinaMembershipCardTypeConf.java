package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaMembershipCardTypeConf<M extends BaseFinaMembershipCardTypeConf<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardTypeId(String cardTypeId) {
		set("card_type_id", cardTypeId);
	}
	
	public String getCardTypeId() {
		return getStr("card_type_id");
	}

	public void setBalanceMoney(Double balanceMoney) {
		set("balance_money", balanceMoney);
	}
	
	public Double getBalanceMoney() {
		return getDouble("balance_money");
	}

	public void setBalanceDays(Double balanceDays) {
		set("balance_days", balanceDays);
	}
	
	public Double getBalanceDays() {
		return getDouble("balance_days");
	}
	
	public void setBalancePoints(Double balancePoints) {
		set("balance_points", balancePoints);
	}
	
	public Double getBalancePoints() {
		return getDouble("balance_points");
	}
	
	public void setBalanceIntegrals(Double balanceIntegrals) {
		set("balance_integrals", balanceIntegrals);
	}
	
	public Double getBalanceIntegrals() {
		return getDouble("balance_integrals");
	}

	public void setBuyCardMoney(Double buyCardMoney) {
		set("buy_card_money", buyCardMoney);
	}
	
	public Double getBuyCardMoney() {
		return getDouble("buy_card_money");
	}

	public void setGiveSchemeId(String giveSchemeId) {
		set("give_scheme_id", giveSchemeId);
	}
	
	public String getGiveSchemeId() {
		return getStr("give_scheme_id");
	}

	public void setRechargeSchemeId(String rechargeSchemeId) {
		set("recharge_scheme_id", rechargeSchemeId);
	}
	
	public String getRechargeSchemeId() {
		return getStr("recharge_scheme_id");
	}

	public void setConditionRechargeSchemeId(String conditionRechargeSchemeId) {
		set("condition_recharge_scheme_id", conditionRechargeSchemeId);
	}

	public String getConditionRechargeSchemeId() {
		return getStr("condition_recharge_scheme_id");
	}

	public void setDeductSchemeId(String deductSchemeId) {
		set("deduct_scheme_id", deductSchemeId);
	}
	
	public String getDeductSchemeId() {
		return getStr("deduct_scheme_id");
	}

	public void setLongDeductSchemeId(String longDeductSchemeId) {
		set("long_deduct_scheme_id", longDeductSchemeId);
	}
	
	public String getLongDeductSchemeId() {
		return getStr("long_deduct_scheme_id");
	}

	public void setYearLimit(String yearLimit) {
		set("year_limit", yearLimit);
	}
	
	public String getYearLimit() {
		return getStr("year_limit");
	}

	public void setUseYears(String useYears) {
		set("use_years", useYears);
	}
	
	public String getUseYears() {
		return getStr("use_years");
	}

	public void setPrice(Double price) {
		set("price", price);
	}
	
	public Double getPrice() {
		return getDouble("price");
	}

	public void setReferencePrice(Double referencePrice) {
		set("reference_price", referencePrice);
	}
	
	public Double getReferencePrice() {
		return getDouble("reference_price");
	}

	public void setCollectAmount(Double collectAmount) {
		set("collect_amount", collectAmount);
	}
	
	public Double getCollectAmount() {
		return getDouble("collect_amount");
	}

	public void setContractTimes(Double contractTimes) {
		set("contract_times", contractTimes);
	}
	
	public Double getContractTimes() {
		return getDouble("contract_times");
	}

	public void setContractDiscount(Double contractDiscount) {
		set("contract_discount", contractDiscount);
	}
	
	public Double getContractDiscount() {
		return getDouble("contract_discount");
	}

	public void setGiveDays(Double giveDays) {
		set("give_days", giveDays);
	}
	
	public Double getGiveDays() {
		return getDouble("give_days");
	}

	public void setGiveRechargeAmount(Double giveRechargeAmount) {
		set("give_recharge_amount", giveRechargeAmount);
	}

	public Double getGiveRechargeAmount() {
		return getDouble("give_recharge_amount");
	}

	public void setGiveRechargeDays(Double giveRechargeDays) {
		set("give_recharge_days", giveRechargeDays);
	}

	public Double getGiveRechargeDays() {
		return getDouble("give_recharge_days");
	}

	public void setGiveRechargeIntegrals(Double giveRechargeIntegrals) {
		set("give_recharge_integrals", giveRechargeIntegrals);
	}

	public Double getGiveRechargeIntegrals() {
		return getDouble("give_recharge_integrals");
	}

	public void setBuyRechargeAmount(Double buyRechargeAmount) {
		set("buy_recharge_amount", buyRechargeAmount);
	}

	public Double getBuyRechargeAmount() {
		return getDouble("buy_recharge_amount");
	}

	public void setBuyRechargeDays(Double buyRechargeDays) {
		set("buy_recharge_days", buyRechargeDays);
	}

	public Double getBuyRechargeDays() {
		return getDouble("buy_recharge_days");
	}

	public void setBuyRechargeIntegrals(Double buyRechargeIntegrals) {
		set("buy_recharge_integrals", buyRechargeIntegrals);
	}

	public Double getBuyRechargeIntegrals() {
		return getDouble("buy_recharge_integrals");
	}

	public void setIsReconciliation(String isReconciliation) {
		set("is_reconciliation", isReconciliation);
	}
	
	public String getIsReconciliation() {
		return getStr("is_reconciliation");
	}

	public void setWarnDay(Integer warnDay) {
		set("warn_day", warnDay);
	}
	
	public Integer getWarnDay() {
		return getInt("warn_day");
	}

	public void setDisabledDay(Integer disabledDay) {
		set("disabled_day", disabledDay);
	}
	
	public Integer getDisabledDay() {
		return getInt("disabled_day");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
