package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.enums.ExamineProblemType;
import com.cszn.integrated.service.entity.main.base.BaseMainExamineProblem;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;

import java.util.ArrayList;
import java.util.List;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_examine_problem", primaryKey = "id")
public class MainExamineProblem extends BaseMainExamineProblem<MainExamineProblem> {

    public Record getProblemType(){

        Record record =Db.findById("main_examine_problem_type",getProblemTypeId());
        String parentIds=record.getStr("parent_ids");
        String[] ids=parentIds.split(",");
        String name="";
        if(ids!=null && ids.length>0){
            String str="";
            List<String> params=new ArrayList<>();
            for(String id:ids){
                str+="?,";
                params.add(id);
            }
            str=str.substring(0,str.length()-1);
            params.addAll(params);


            List<String> names=Db.query("select `name` from main_examine_problem_type where id in("+str+") ORDER BY field (id,"+str+")",params.toArray());
            for(String n:names){
                name+=n+"/";
            }
            name+=record.getStr("name");
        }
        Record typeRecord=new Record();
        typeRecord.set("id",this.getProblemTypeId());
        typeRecord.set("name",name);

        return typeRecord;
    }



    public List<Record> getOptions(){
        return Db.find("select * from main_examine_problem_option where problem_id=? and is_enabled='1' order by sort ",this.getId());
    }
    public List<Record> getAllOptions(){
        return Db.find("select * from main_examine_problem_option where problem_id=? order by sort ",this.getId());
    }

    private String optionIds;
    private String content;
    private String optionTextContent;
    private String files;
    private String remark;

    public void setOptionIds(String optionIds) {
        this.optionIds = optionIds;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getOptionIds() {
        return optionIds;
    }

    public String getContent() {
        return content;
    }

    public Integer getScore(){
        if(ExamineProblemType.radio.getKey().equals(this.getType())){
            Integer score=Db.queryInt("select max(score) from main_examine_problem_option where problem_id=? and is_enabled='1'  ",this.getId());
            return score;
        }else if(ExamineProblemType.checkbox.getKey().equals(this.getType())){
            Integer score=Db.queryInt("select SUM(score) from main_examine_problem_option where problem_id=? and is_enabled='1' ",this.getId());
            return score;
        }
        return null;
    }

    public void setOptionTextContent(String optionTextContent) {
        this.optionTextContent = optionTextContent;
    }

    public String getOptionTextContent() {
        return optionTextContent;
    }

    public void setFiles(String files) {
        this.files = files;
    }

    public String getFiles() {
        return files;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }
}
