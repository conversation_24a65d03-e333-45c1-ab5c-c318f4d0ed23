package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollType<M extends BaseCrmCardRollType<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setTypeName(java.lang.String typeName) {
		set("type_name", typeName);
	}
	
	public java.lang.String getTypeName() {
		return getStr("type_name");
	}

	public void setTypeCode(java.lang.String typeCode) {
		set("type_code", typeCode);
	}

	public java.lang.String getTypeCode() {
		return getStr("type_code");
	}

	public void setIsEnable(java.lang.String isEnable) {
		set("is_enable", isEnable);
	}
	
	public java.lang.String getIsEnable() {
		return getStr("is_enable");
	}
	
	public void setTemplateName(java.lang.String templateName) {
		set("template_name", templateName);
	}
	
	public java.lang.String getTemplateName() {
		return getStr("template_name");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
