package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainPunishRule<M extends BaseMainPunishRule<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setPunishName(String punishName) {
		set("punish_name", punishName);
	}
	
	public String getPunishName() {
		return getStr("punish_name");
	}

	public void setPunishType(String punishType) {
		set("punish_type", punishType);
	}
	
	public String getPunishType() {
		return getStr("punish_type");
	}

	public void setExceedStartDay(Integer exceedStartDay) {
		set("exceed_start_day", exceedStartDay);
	}
	
	public Integer getExceedStartDay() {
		return getInt("exceed_start_day");
	}

	public void setExceedEndDay(Integer exceedEndDay) {
		set("exceed_end_day", exceedEndDay);
	}
	
	public Integer getExceedEndDay() {
		return getInt("exceed_end_day");
	}

	public void setDeductType(String deductType){
		set("deduct_type",deductType);
	}

	public String getDeductType(){
		return get("deduct_type");
	}

	public void setDeductValue(Double deductValue) {
		set("deduct_value", deductValue);
	}
	
	public Double getMaxDeductValue() {
		return getDouble("max_deduct_value");
	}

	public void setMaxDeductValue(Double maxDeductValue) {
		set("max_deduct_value", maxDeductValue);
	}

	public Double getDeductValue() {
		return getDouble("deduct_value");
	}

	public void setGruelRule(String gruelRule) {
		set("gruel_rule", gruelRule);
	}
	
	public String getGruelRule() {
		return getStr("gruel_rule");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
