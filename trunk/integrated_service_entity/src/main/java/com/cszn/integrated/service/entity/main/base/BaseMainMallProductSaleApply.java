package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainMallProductSaleApply<M extends BaseMainMallProductSaleApply<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setApplyId(String applyId) {
		set("apply_id", applyId);
	}
	
	public String getApplyId() {
		return getStr("apply_id");
	}

	public void setApplyTime(java.util.Date applyTime) {
		set("apply_time", applyTime);
	}
	
	public java.util.Date getApplyTime() {
		return get("apply_time");
	}

	public void setDeptId(String deptId) {
		set("dept_id", deptId);
	}
	
	public String getDeptId() {
		return getStr("dept_id");
	}

	public void setPositionId(String positionId) {
		set("position_id", positionId);
	}
	
	public String getPositionId() {
		return getStr("position_id");
	}

	public void setTaskId(String taskId) {
		set("task_id", taskId);
	}
	
	public String getTaskId() {
		return getStr("task_id");
	}

	public void setTaskNo(String taskNo) {
		set("task_no", taskNo);
	}
	
	public String getTaskNo() {
		return getStr("task_no");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
