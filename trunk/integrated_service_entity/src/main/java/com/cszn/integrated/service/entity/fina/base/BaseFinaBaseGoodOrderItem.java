package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaBaseGoodOrderItem<M extends BaseFinaBaseGoodOrderItem<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setOrderId(String orderId) {
		set("order_id", orderId);
	}
	
	public String getOrderId() {
		return getStr("order_id");
	}

	public void setGoodId(String goodId) {
		set("good_id", goodId);
	}
	
	public String getGoodId() {
		return getStr("good_id");
	}

	public void setNum(Integer num) {
		set("num", num);
	}
	
	public Integer getNum() {
		return getInt("num");
	}

	public void setUnitTimes(Double unitTimes) {
		set("unit_times", unitTimes);
	}
	
	public Double getUnitTimes() {
		return getDouble("unit_times");
	}

}
