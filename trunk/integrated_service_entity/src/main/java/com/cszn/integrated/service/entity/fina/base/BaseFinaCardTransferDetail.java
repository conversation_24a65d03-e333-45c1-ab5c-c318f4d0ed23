package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardTransferDetail<M extends BaseFinaCardTransferDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTransferId(String transferId) {
		set("transfer_id", transferId);
	}
	
	public String getTransferId() {
		return getStr("transfer_id");
	}

	public void setOldCardId(String oldCardId) {
		set("old_card_id", oldCardId);
	}

	public String getOldCardId() {
		return getStr("old_card_id");
	}

	public void setOldCardNumber(String oldCardNumber) {
		set("old_card_number", oldCardNumber);
	}

	public String getOldCardNumber() {
		return getStr("old_card_number");
	}

	public void setOldCardAmount(java.lang.Double oldCardAmount) {
		set("old_card_amount", oldCardAmount);
	}
	
	public java.lang.Double getOldCardAmount() {
		return getDouble("old_card_amount");
	}

	public void setOldCardBuyAmount(java.lang.Double oldCardBuyAmount) {set("old_card_buy_amount", oldCardBuyAmount);}

	public java.lang.Double getOldCardBuyAmount() {return getDouble("old_card_buy_amount");}

	public void setOldCardGiveAmount(java.lang.Double oldCardGiveAmount) {set("old_card_give_amount", oldCardGiveAmount);}

	public java.lang.Double getOldCardGiveAmount() {return getDouble("old_card_give_amount");}

	public void setOldCardDays(java.lang.Double oldCardDays) {
		set("old_card_days", oldCardDays);
	}
	
	public java.lang.Double getOldCardDays() {
		return getDouble("old_card_days");
	}

	public void setOldCardBuyDays(java.lang.Double oldCardBuyDays) {set("old_card_buy_days", oldCardBuyDays);}

	public java.lang.Double getOldCardBuyDays() {return getDouble("old_card_buy_days");}

	public void setOldCardGiveDays(java.lang.Double oldCardGiveDays) {set("old_card_give_days", oldCardGiveDays);}

	public java.lang.Double getOldCardGiveDays() {return getDouble("old_card_give_days");}

	public void setOldCardPoints(java.lang.Double oldCardPoints) {
		set("old_card_points", oldCardPoints);
	}
	
	public java.lang.Double getOldCardPoints() {
		return getDouble("old_card_points");
	}

	public void setOldCardIntegrals(java.lang.Double oldCardIntegrals) {
		set("old_card_integrals", oldCardIntegrals);
	}

	public java.lang.Double getOldCardIntegrals() {
		return getDouble("old_card_integrals");
	}

	public void setOldCardPeas(java.lang.Double oldCardPeas) {
		set("old_card_peas", oldCardPeas);
	}

	public java.lang.Double getOldCardPeas() {
		return getDouble("old_card_peas");
	}

	public void setOldCardAccountBalance(java.lang.Double oldCardAccountBalance) {set("old_card_account_balance", oldCardAccountBalance);}

	public java.lang.Double getOldCardAccountBalance() {
		return getDouble("old_card_account_balance");
	}
	
	public void setOldCardTransferDays(java.lang.Double oldCardTransferDays) {set("old_card_transfer_days", oldCardTransferDays);}
	
	public java.lang.Double getOldCardTransferDays() {
		return getDouble("old_card_transfer_days");
	}

	public void setOldCardTransferGiveDays(java.lang.Double oldCardTransferGiveDays) {set("old_card_transfer_give_days", oldCardTransferGiveDays);}

	public java.lang.Double getOldCardTransferGiveDays() {return getDouble("old_card_transfer_give_days");}

	public void setOldCardTransferMoney(java.lang.Double oldCardTransferMoney) {set("old_card_transfer_money", oldCardTransferMoney);}
	
	public java.lang.Double getOldCardTransferMoney() {
		return getDouble("old_card_transfer_money");
	}

	public void setOldCardTransferGiveMoney(java.lang.Double oldCardTransferGiveMoney) {set("old_card_transfer_give_money", oldCardTransferGiveMoney);}

	public java.lang.Double getOldCardTransferGiveMoney() {return getDouble("old_card_transfer_give_money");}
}
