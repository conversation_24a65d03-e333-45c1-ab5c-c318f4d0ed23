package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainCardDeductScheme<M extends BaseMainCardDeductScheme<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}
	
	public void setSchemeType(String schemeType) {
		set("scheme_type", schemeType);
	}
	
	public String getSchemeType() {
		return getStr("scheme_type");
	}

	public void setSchemeNo(String schemeNo) {
		set("scheme_no", schemeNo);
	}
	
	public String getSchemeNo() {
		return getStr("scheme_no");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setDeductWay(String deductWay) {
		set("deduct_way", deductWay);
	}
	
	public String getDeductWay() {
		return getStr("deduct_way");
	}

	public void setOnceConsume(Double onceConsume) {
		set("once_consume", onceConsume);
	}
	
	public Double getOnceConsume() {
		return getDouble("once_consume");
	}

	public void setPrice(Double price) {
		set("price", price);
	}
	
	public Double getPrice() {
		return getDouble("price");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}
	
	public Integer getSort() {
		return getInt("sort");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
