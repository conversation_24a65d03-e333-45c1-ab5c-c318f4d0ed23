package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainArea<M extends BaseMainArea<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setParentId(String parentId) {
		set("parent_id", parentId);
	}
	
	public String getParentId() {
		return getStr("parent_id");
	}

	public void setCode(Long code) {
		set("code", code);
	}
	
	public Long getCode() {
		return getLong("code");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setShortName(String shortName) {
		set("short_name", shortName);
	}
	
	public String getShortName() {
		return getStr("short_name");
	}

	public void setLongitude(String longitude) {
		set("longitude", longitude);
	}
	
	public String getLongitude() {
		return getStr("longitude");
	}

	public void setLatitude(String latitude) {
		set("latitude", latitude);
	}
	
	public String getLatitude() {
		return getStr("latitude");
	}

	public void setLevel(Integer level) {
		set("level", level);
	}
	
	public Integer getLevel() {
		return getInt("level");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}
	
	public Integer getSort() {
		return getInt("sort");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
