package com.cszn.integrated.service.entity.wms;

import com.cszn.integrated.service.entity.wms.base.BaseWmsSuppliers;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "wms_suppliers", primaryKey = "id")
public class WmsSuppliers extends BaseWmsSuppliers<WmsSuppliers> {
	
	public String getMainLinkMan(){
		String linkMan = "";
		if(StrKit.notBlank(this.getLinkId())){
			linkMan = Db.queryStr("select link_name from wms_suppliers_link where id=?",this.getLinkId());
		}
		return linkMan;
	}
}
