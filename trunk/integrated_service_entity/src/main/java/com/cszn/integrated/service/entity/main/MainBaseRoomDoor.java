package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainBaseRoomDoor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_base_room_door", primaryKey = "id")
public class MainBaseRoomDoor extends BaseMainBaseRoomDoor<MainBaseRoomDoor> {
	
	public String getDoorName() {
		String doorName = "";
		final String doorId = this.getDoorId();
		Record door = Db.findFirst("select * from main_base_door where id=?", doorId);
		if(door!=null) {
			doorName = door.getStr("door_name");
		}
		return doorName;
	}
}
