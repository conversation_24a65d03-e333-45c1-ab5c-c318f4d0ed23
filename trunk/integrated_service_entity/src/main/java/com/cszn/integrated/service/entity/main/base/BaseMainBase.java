package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBase<M extends BaseMainBase<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseName(String baseName) {
		set("base_name", baseName);
	}
	
	public String getBaseName() {
		return getStr("base_name");
	}

	public void setBaseCode(String baseCode){
		set("base_code",baseCode);
	}

	public String getBaseCode(){
		return get("base_code");
	}

	public void setOpenDate(java.util.Date openDate) {
		set("open_date", openDate);
	}
	
	public java.util.Date getOpenDate() {
		return get("open_date");
	}

	public void setTel(String tel) {
		set("tel", tel);
	}
	
	public String getTel() {
		return getStr("tel");
	}

	public void setCostPrice(String costPrice) {
		set("cost_price", costPrice);
	}

	public String getCostPrice() {
		return getStr("cost_price");
	}

	public void setBaseFinanceUserIds(String baseFinanceUserIds) {
		set("base_finance_user_ids", baseFinanceUserIds);
	}

	public String getBaseFinanceUserIds() {
		return getStr("base_finance_user_ids");
	}

	public void setIntroduction(String introduction) {
		set("introduction", introduction);
	}
	
	public String getIntroduction() {
		return getStr("introduction");
	}

	public void setProvinceId(String provinceId) {
		set("province_id", provinceId);
	}
	
	public String getProvinceId() {
		return getStr("province_id");
	}

	public void setCityId(String cityId) {
		set("city_id", cityId);
	}
	
	public String getCityId() {
		return getStr("city_id");
	}

	public void setTownId(String townId) {
		set("town_id", townId);
	}
	
	public String getTownId() {
		return getStr("town_id");
	}

	public void setStreetId(String streetId) {
		set("street_id", streetId);
	}
	
	public String getStreetId() {
		return getStr("street_id");
	}

	public void setAddress(String address) {
		set("address", address);
	}
	
	public String getAddress() {
		return getStr("address");
	}

	public void setCoordinate(String coordinate) {
		set("coordinate", coordinate);
	}
	
	public String getCoordinate() {
		return getStr("coordinate");
	}

	public void setFileId(String fileId) {
		set("file_id", fileId);
	}
	
	public String getFileId() {
		return getStr("file_id");
	}

	public void setBaseImage(String baseImage) {
		set("base_image", baseImage);
	}
	
	public String getBaseImage() {
		return getStr("base_image");
	}

	public void setTotalBuildings(Integer totalBuildings) {
		set("total_buildings", totalBuildings);
	}
	
	public Integer getTotalBuildings() {
		return getInt("total_buildings");
	}

	public void setTotalRooms(Integer totalRooms) {
		set("total_rooms", totalRooms);
	}
	
	public Integer getTotalRooms() {
		return getInt("total_rooms");
	}

	public void setTotalBeds(Integer totalBeds) {
		set("total_beds", totalBeds);
	}
	
	public Integer getTotalBeds() {
		return getInt("total_beds");
	}

	public void setHoldBeds(Integer holdBeds) {
		set("hold_beds", holdBeds);
	}
	
	public Integer getHoldBeds() {
		return getInt("hold_beds");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setLockCode(String lockCode) {
		set("lock_code", lockCode);
	}
	
	public String getLockCode() {
		return getStr("lock_code");
	}

	public void setLockService(String lockService) {
		set("lock_service", lockService);
	}
	
	public String getLockService() {
		return getStr("lock_service");
	}

	public void setBookableEndDate(java.util.Date bookableEndDate) {
		set("bookable_end_date", bookableEndDate);
	}
	
	public java.util.Date getBookableEndDate() {
		return get("bookable_end_date");
	}

	public void setIdcardService(String idcardService) {
		set("idcard_service", idcardService);
	}
	
	public String getIdcardService() {
		return getStr("idcard_service");
	}

	public void setCheckinTime(String checkinTime) {
		set("checkin_time", checkinTime);
	}
	
	public String getCheckinTime() {
		return getStr("checkin_time");
	}

	public void setCheckoutTime(String checkoutTime) {
		set("checkout_time", checkoutTime);
	}
	
	public String getCheckoutTime() {
		return getStr("checkout_time");
	}
	
	public void setOutsiderCheckinTime(String outsiderCheckinTime) {
		set("outsider_checkin_time", outsiderCheckinTime);
	}
	
	public String getOutsiderCheckinTime() {
		return getStr("outsider_checkin_time");
	}
	
	public void setOutsiderCheckoutTime(String outsiderCheckoutTime) {
		set("outsider_checkout_time", outsiderCheckoutTime);
	}
	
	public String getOutsiderCheckoutTime() {
		return getStr("outsider_checkout_time");
	}
	
	public void setOutsiderStartTime(String outsiderStartTime) {
		set("outsider_start_time", outsiderStartTime);
	}
	
	public String getOutsiderStartTime() {
		return getStr("outsider_start_time");
	}

	public void setAuditUserId(String auditUserId){
		set("audit_user_id",auditUserId);
	}

	public String getAuditUserId(){
		return get("audit_user_id");
	}

	public void setOrder(Integer order) {
		set("order", order);
	}
	
	public Integer getOrder() {
		return getInt("order");
	}

	public void setRetainMinDays(Integer retainMinDays) {
		set("retain_min_days", retainMinDays);
	}

	public Integer getRetainMinDays() {
		return getInt("retain_min_days");
	}

	public void setInHesitateDays(Integer inHesitateDays) {
		set("in_hesitate_days", inHesitateDays);
	}

	public Integer getInHesitateDays() {
		return getInt("in_hesitate_days");
	}

	public void setHesitateDurationDays(Integer hesitateDurationDays) {
		set("hesitate_duration_days", hesitateDurationDays);
	}

	public Integer getHesitateDurationDays() {
		return getInt("hesitate_duration_days");
	}

	public void setSleepaceUrl(String sleepaceUrl) {
		set("sleepace_url", sleepaceUrl);
	}

	public String getSleepaceUrl() {
		return getStr("sleepace_url");
	}

	public void setSleepaceAppId(String sleepaceAppId) {
		set("sleepace_app_id", sleepaceAppId);
	}

	public String getSleepaceAppId() {
		return getStr("sleepace_app_id");
	}

	public void setSleepaceSecureKey(String sleepaceSecureKey) {
		set("sleepace_secure_key", sleepaceSecureKey);
	}

	public String getSleepaceSecureKey() {
		return getStr("sleepace_secure_key");
	}

	public void setSleepaceWebsocketUrl(String sleepaceWebsocketUrl) {
		set("sleepace_websocket_url", sleepaceWebsocketUrl);
	}

	public String getSleepaceWebsocketUrl() {
		return getStr("sleepace_websocket_url");
	}

	public void setSleepaceReportTime(Integer sleepaceReportTime) {
		set("sleepace_report_time", sleepaceReportTime);
	}

	public Integer getSleepaceReportTime() {
		return get("sleepace_report_time");
	}

	public void setChargePeopleId(String chargePeopleId) {
		set("charge_people_id", chargePeopleId);
	}
	
	public String getChargePeopleId() {
		return getStr("charge_people_id");
	}
	
	public void setChargeCardNumber(String chargeCardNumber) {
		set("charge_card_number", chargeCardNumber);
	}
	
	public String getChargeCardNumber() {
		return getStr("charge_card_number");
	}
	
	public void setBaseType(String baseType) {
		set("base_type", baseType);
	}
	
	public String getBaseType() {
		return getStr("base_type");
	}
	
	public void setBaseViceTitle(String baseViceTitle) {
		set("base_vice_title", baseViceTitle);
	}
	
	public String getBaseViceTitle() {
		return getStr("base_vice_title");
	}
	
	public void setBuildArea(String buildArea) {
		set("build_area", buildArea);
	}
	
	public String getBuildArea() {
		return getStr("build_area");
	}
	
	public void setIsRelease(String isRelease) {
		set("is_release", isRelease);
	}
	
	public String getIsRelease() {
		return getStr("is_release");
	}
	
	public void setBaseStatus(String baseStatus) {
		set("base_status", baseStatus);
	}
	
	public String getBaseStatus() {
		return getStr("base_status");
	}
	
	public void setIsElasticEat(String isElasticEat) {
		set("is_elastic_eat", isElasticEat);
	}
	
	public String getIsElasticEat() {
		return getStr("is_elastic_eat");
	}
	public void setLeaseSubmitUserId(String leaseSubmitUserId) {
		set("lease_submit_user_id", leaseSubmitUserId);
	}

	public String getLeaseSubmitUserId() {
		return getStr("lease_submit_user_id");
	}
	public void setLeaseCheckUserId(String leaseCheckUserId) {
		set("lease_check_user_id", leaseCheckUserId);
	}

	public String getLeaseCheckUserId() {
		return getStr("lease_check_user_id");
	}
	public void setLeaseApproveUserId(String leaseApproveUserId) {
		set("lease_approve_user_id", leaseApproveUserId);
	}

	public String getLeaseApproveUserId() {
		return getStr("lease_approve_user_id");
	}

	public void setPosId(String posId) {
		set("pos_id", posId);
	}

	public String getPosId() {
		return getStr("pos_id");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

	public void setIsOutBookabled(String isOutBookabled) {
		set("is_out_bookabled", isOutBookabled);
	}

	public String getIsOutBookabled() {
		return getStr("is_out_bookabled");
	}
}
