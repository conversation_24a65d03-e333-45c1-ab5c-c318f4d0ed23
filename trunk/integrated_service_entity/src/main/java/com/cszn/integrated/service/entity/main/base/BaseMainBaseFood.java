package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseFood<M extends BaseMainBaseFood<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setBaseId(java.lang.String baseId) {
		set("base_id", baseId);
	}
	
	public java.lang.String getBaseId() {
		return getStr("base_id");
	}

	public void setFoodId(java.lang.String foodId) {
		set("food_id", foodId);
	}
	
	public java.lang.String getFoodId() {
		return getStr("food_id");
	}

	public void setTagId(java.lang.String tagId) {
		set("tag_id", tagId);
	}
	
	public java.lang.String getTagId() {
		return getStr("tag_id");
	}
	
	public void setConfigType(java.lang.String configType) {
		set("config_type", configType);
	}
	
	public java.lang.String getConfigType() {
		return getStr("config_type");
	}
	
	public void setCostPrice(java.lang.Double costPrice) {
		set("cost_price", costPrice);
	}
	
	public java.lang.Double getCostPrice() {
		return getDouble("cost_price");
	}

	public void setSalesPrice(java.lang.Double salesPrice) {
		set("sales_price", salesPrice);
	}
	
	public java.lang.Double getSalesPrice() {
		return getDouble("sales_price");
	}

	public void setIsEnabled(java.lang.String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public java.lang.String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setFoodSort(java.lang.Integer foodSort) {
		set("food_sort", foodSort);
	}
	
	public java.lang.Integer getFoodSort() {
		return getInt("food_sort");
	}

	public void setIsShelf(java.lang.String isShelf) {
		set("is_shelf", isShelf);
	}
	
	public java.lang.String getIsShelf() {
		return getStr("is_shelf");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
