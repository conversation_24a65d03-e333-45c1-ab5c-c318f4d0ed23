package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardRefundDetail<M extends BaseFinaCardRefundDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRefundId(String refundId) {
		set("refund_id", refundId);
	}
	
	public String getRefundId() {
		return getStr("refund_id");
	}

	public void setSortNumber(Integer sortNumber) {
		set("sort_number", sortNumber);
	}

	public Integer getSortNumber() {
		return getInt("sort_number");
	}
	
	public void setMemberName(String memberName) {
		set("member_name", memberName);
	}
	
	public String getMemberName() {
		return getStr("member_name");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setCustomerPhone(String customerPhone) {
		set("customer_phone", customerPhone);
	}

	public String getCustomerPhone() {
		return getStr("customer_phone");
	}

	public void setCardTypeId(String cardTypeId) {
		set("card_type_id", cardTypeId);
	}
	
	public String getCardTypeId() {
		return getStr("card_type_id");
	}

	public void setCardType(String cardType) {
		set("card_type", cardType);
	}
	
	public String getCardType() {
		return getStr("card_type");
	}

	public void setBranchOfficeId(String branchOfficeId) {
		set("branch_office_id", branchOfficeId);
	}

	public String getBranchOfficeId() {
		return getStr("branch_office_id");
	}

	public void setBranchOfficeName(String branchOfficeName) {
		set("branch_office_name", branchOfficeName);
	}

	public String getBranchOfficeName() {
		return getStr("branch_office_name");
	}

	public void setDepartmentId(String departmentId) {
		set("department_id", departmentId);
	}

	public String getDepartmentId() {
		return getStr("department_id");
	}

	public void setDepartmentName(String departmentName) {
		set("department_name", departmentName);
	}

	public String getDepartmentName() {
		return getStr("department_name");
	}

	public void setSalesId(String salesId) {
		set("sales_id", salesId);
	}

	public String getSalesId() {
		return getStr("sales_id");
	}

	public void setSalesName(String salesName) {
		set("sales_name", salesName);
	}

	public String getSalesName() {
		return getStr("sales_name");
	}

	public void setBuyCardAmount(Double buyCardAmount) {
		set("buy_card_amount", buyCardAmount);
	}
	
	public Double getBuyCardAmount() {
		return getDouble("buy_card_amount");
	}
	
	public void setBuyCardDays(Double buyCardDays) {
		set("buy_card_days", buyCardDays);
	}
	
	public Double getBuyCardDays() {
		return getDouble("buy_card_days");
	}
	
	public void setCollectAmount(Double collectAmount) {
		set("collect_amount", collectAmount);
	}
	
	public Double getCollectAmount() {
		return getDouble("collect_amount");
	}

	public void setMoneyBalance(Double moneyBalance) {
		set("money_balance", moneyBalance);
	}

	public Double getMoneyBalance() {
		return getDouble("money_balance");
	}

	public void setDaysBalance(Double daysBalance) {
		set("days_balance", daysBalance);
	}

	public Double getDaysBalance() {
		return getDouble("days_balance");
	}

	public void setPointsBalance(Double pointsBalance) {
		set("points_balance", pointsBalance);
	}

	public Double getPointsBalance() {
		return getDouble("points_balance");
	}

	public void setIntegralsBalance(Double integralsBalance) {
		set("integrals_balance", integralsBalance);
	}

	public Double getIntegralsBalance() {
		return getDouble("integrals_balance");
	}

	public void setBeanCouponsBalance(Double beanCouponsBalance) {
		set("bean_coupons_balance", beanCouponsBalance);
	}

	public Double getBeanCouponsBalance() {
		return getDouble("bean_coupons_balance");
	}
	
	public void setAccountAmount(Double accountAmount) {
		set("account_amount", accountAmount);
	}
	
	public Double getAccountAmount() {
		return getDouble("account_amount");
	}

	public void setBuyCardDate(java.util.Date buyCardDate) {
		set("buy_card_date", buyCardDate);
	}

	public java.util.Date getBuyCardDate() {
		return get("buy_card_date");
	}

	public void setExpireDate(java.util.Date expireDate) {
		set("expire_date", expireDate);
	}

	public java.util.Date getExpireDate() {
		return get("expire_date");
	}

	public void setCardRemarks(String cardRemarks) {
		set("card_remarks", cardRemarks);
	}

	public String getCardRemarks() {
		return getStr("card_remarks");
	}

	public void setRefundAmount(Double refundAmount) {
		set("refund_amount", refundAmount);
	}

	public Double getRefundAmount() {
		return getDouble("refund_amount");
	}

	public void setDeductProportion(Double deductProportion) {
		set("deduct_proportion", deductProportion);
	}

	public Double getDeductProportion() {
		return getDouble("deduct_proportion");
	}

	public void setDeductMoney(Double deductMoney) {
		set("deduct_money", deductMoney);
	}

	public Double getDeductMoney() {
		return getDouble("deduct_money");
	}

	public void setRealRefundAmount(Double realRefundAmount) {
		set("real_refund_amount", realRefundAmount);
	}

	public Double getRealRefundAmount() {
		return getDouble("real_refund_amount");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
