package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBranchOffice<M extends BaseMainBranchOffice<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setShortName(String shortName) {
		set("short_name", shortName);
	}
	
	public String getShortName() {
		return getStr("short_name");
	}

	public void setFullName(String fullName) {
		set("full_name", fullName);
	}
	
	public String getFullName() {
		return getStr("full_name");
	}

	public void setType(String type) {
		set("type", type);
	}

	public String getType() {
		return getStr("type");
	}

	public void setOpenDate(java.util.Date openDate) {
		set("open_date", openDate);
	}
	
	public java.util.Date getOpenDate() {
		return get("open_date");
	}

	public void setTel(String tel) {
		set("tel", tel);
	}
	
	public String getTel() {
		return getStr("tel");
	}

	public void setIntroduction(String introduction) {
		set("introduction", introduction);
	}
	
	public String getIntroduction() {
		return getStr("introduction");
	}

	public void setProvinceId(String provinceId) {
		set("province_id", provinceId);
	}
	
	public String getProvinceId() {
		return getStr("province_id");
	}

	public void setCityId(String cityId) {
		set("city_id", cityId);
	}
	
	public String getCityId() {
		return getStr("city_id");
	}

	public void setTownId(String townId) {
		set("town_id", townId);
	}
	
	public String getTownId() {
		return getStr("town_id");
	}

	public void setStreetId(String streetId) {
		set("street_id", streetId);
	}
	
	public String getStreetId() {
		return getStr("street_id");
	}

	public void setAddress(String address) {
		set("address", address);
	}
	
	public String getAddress() {
		return getStr("address");
	}

	public void setCoordinate(String coordinate) {
		set("coordinate", coordinate);
	}
	
	public String getCoordinate() {
		return getStr("coordinate");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setRemarks(String remarks) {
		set("remarks", remarks);
	}
	
	public String getRemarks() {
		return getStr("remarks");
	}

	public void setChargePeopleId(String chargePeopleId) {
		set("charge_people_id", chargePeopleId);
	}
	
	public String getChargePeopleId() {
		return getStr("charge_people_id");
	}
	
	public void setChargeCardNumber(String chargeCardNumber) {
		set("charge_card_number", chargeCardNumber);
	}
	
	public String getChargeCardNumber() {
		return getStr("charge_card_number");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
