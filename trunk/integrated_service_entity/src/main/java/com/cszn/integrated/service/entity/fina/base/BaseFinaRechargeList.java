package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaRechargeList<M extends BaseFinaRechargeList<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRecordId(String recordId) {
		set("record_id", recordId);
	}
	
	public String getRecordId() {
		return getStr("record_id");
	}
	
	public void setSerialNumber(java.lang.Integer serialNumber) {
		set("serial_number", serialNumber);
	}
	
	public java.lang.Integer getSerialNumber() {
		return getInt("serial_number");
	}

	public void setEmpId(String empId) {
		set("emp_id", empId);
	}
	
	public String getEmpId() {
		return getStr("emp_id");
	}
	
	public void setEmpName(String empName) {
		set("emp_name", empName);
	}
	
	public String getEmpName() {
		return getStr("emp_name");
	}
	
	public void setWorkNum(String workNum) {
		set("work_num", workNum);
	}
	
	public String getWorkNum() {
		return getStr("work_num");
	}
	
	public void setMemberId(String memberId) {
		set("member_id", memberId);
	}
	
	public String getMemberId() {
		return getStr("member_id");
	}
	
	public void setMemberName(String memberName) {
		set("member_name", memberName);
	}
	
	public String getMemberName() {
		return getStr("member_name");
	}
	
	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}
	
	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}
	
	public void setRechargeType(String rechargeType) {
		set("recharge_type", rechargeType);
	}
	
	public String getRechargeType() {
		return getStr("recharge_type");
	}
	
	public void setRechargeNo(String rechargeNo) {
		set("recharge_no", rechargeNo);
	}
	
	public String getRechargeNo() {
		return getStr("recharge_no");
	}

	public void setRechargeValue(Double rechargeValue) {
		set("recharge_value", rechargeValue);
	}
	
	public Double getRechargeValue() {
		return getDouble("recharge_value");
	}
	
	public void setSendPhone(String sendPhone) {
		set("send_phone", sendPhone);
	}
	
	public String getSendPhone() {
		return getStr("send_phone");
	}

	public void setIsSendSuccess(String isSendSuccess) {
		set("is_send_success", isSendSuccess);
	}
	
	public String getIsSendSuccess() {
		return getStr("is_send_success");
	}

	public void setBelongArea(String belongArea) {
		set("belong_area", belongArea);
	}
	
	public String getBelongArea() {
		return getStr("belong_area");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
