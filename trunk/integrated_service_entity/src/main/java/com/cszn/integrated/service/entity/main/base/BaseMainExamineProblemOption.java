package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainExamineProblemOption<M extends BaseMainExamineProblemOption<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setProblemId(String problemId) {
		set("problem_id", problemId);
	}
	
	public String getProblemId() {
		return getStr("problem_id");
	}

	public void setContent(String content) {
		set("content", content);
	}
	
	public String getContent() {
		return getStr("content");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}
	
	public Integer getSort() {
		return getInt("sort");
	}

	public void setScore(Double score) {
		set("score", score);
	}
	
	public Double getScore() {
		return getDouble("score");
	}

	public void setIsOpenText(String isOpenText) {
		set("is_open_text", isOpenText);
	}

	public String getIsOpenText() {
		return getStr("is_open_text");
	}

	public void setTextName(String textName) {
		set("text_name", textName);
	}

	public String getTextName() {
		return getStr("text_name");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
