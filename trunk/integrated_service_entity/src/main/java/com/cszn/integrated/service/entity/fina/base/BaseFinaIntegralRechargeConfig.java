package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaIntegralRechargeConfig<M extends BaseFinaIntegralRechargeConfig<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setProductPrice(Double productPrice) {
		set("product_price", productPrice);
	}
	
	public Double getProductPrice() {
		return getDouble("product_price");
	}

	public void setRechargeDays(Double rechargeDays) {
		set("recharge_days", rechargeDays);
	}
	
	public Double getRechargeDays() {
		return getDouble("recharge_days");
	}

	public void setIntegralRatio(Double integralRatio) {
		set("integral_ratio", integralRatio);
	}
	
	public Double getIntegralRatio() {
		return getDouble("integral_ratio");
	}

	public void setDayIntegral(Double dayIntegral) {
		set("day_integral", dayIntegral);
	}
	
	public Double getDayIntegral() {
		return getDouble("day_integral");
	}

	public void setMonthIntegral(Double monthIntegral) {
		set("month_integral", monthIntegral);
	}
	
	public Double getMonthIntegral() {
		return getDouble("month_integral");
	}

	public void setYearIntegral(Double yearIntegral) {
		set("year_integral", yearIntegral);
	}
	
	public Double getYearIntegral() {
		return getDouble("year_integral");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}

	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
