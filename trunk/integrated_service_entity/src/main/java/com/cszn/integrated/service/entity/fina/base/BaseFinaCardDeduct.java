package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardDeduct<M extends BaseFinaCardDeduct<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}
	
	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}
	
	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setStartTime(java.util.Date startTime) {
		set("start_time", startTime);
	}
	
	public java.util.Date getStartTime() {
		return get("start_time");
	}
	
	public void setEndTime(java.util.Date endTime) {
		set("end_time", endTime);
	}
	
	public java.util.Date getEndTime() {
		return get("end_time");
	}
	
	public void setOrderNo(String orderNo) {
		set("order_no", orderNo);
	}
	
	public String getOrderNo() {
		return getStr("order_no");
	}

	public void setDeductAmount(Double deductAmount) {
		set("deduct_amount", deductAmount);
	}
	
	public Double getDeductAmount() {
		return getDouble("deduct_amount");
	}

	public void setDeductTimes(Double deductTimes) {
		set("deduct_times", deductTimes);
	}
	
	public Double getDeductTimes() {
		return getDouble("deduct_times");
	}

	public void setDeductPoints(Double deductPoints) {
		set("deduct_points", deductPoints);
	}

	public Double getDeductPoints() {
		return getDouble("deduct_points");
	}
	
	public void setDeductIntegral(Double deductIntegral) {
		set("deduct_integral", deductIntegral);
	}
	
	public Double getDeductIntegral() {
		return getDouble("deduct_integral");
	}
	
	public void setDeductBeanCoupons(Double deductBeanCoupons) {
		set("deduct_bean_coupons", deductBeanCoupons);
	}
	
	public Double getDeductBeanCoupons() {
		return getDouble("deduct_bean_coupons");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setCancelFlag(String cancelFlag) {
		set("cancel_flag", cancelFlag);
	}
	
	public String getCancelFlag() {
		return getStr("cancel_flag");
	}

	public void setIsReview(String isReview) {
		set("is_review", isReview);
	}
	
	public String getIsReview() {
		return getStr("is_review");
	}
	
	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
