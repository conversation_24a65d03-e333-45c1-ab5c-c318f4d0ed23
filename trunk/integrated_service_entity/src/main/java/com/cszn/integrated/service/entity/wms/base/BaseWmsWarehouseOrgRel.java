package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsWarehouseOrgRel<M extends BaseWmsWarehouseOrgRel<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setWarehouseId(String warehouseId) {
		set("warehouse_id", warehouseId);
	}
	
	public String getWarehouseId() {
		return getStr("warehouse_id");
	}

	public void setOrgId(String orgId) {
		set("org_id", orgId);
	}
	
	public String getOrgId() {
		return getStr("org_id");
	}

}
