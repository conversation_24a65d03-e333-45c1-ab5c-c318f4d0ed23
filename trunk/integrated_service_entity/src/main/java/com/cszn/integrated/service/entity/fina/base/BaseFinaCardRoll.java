package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardRoll<M extends BaseFinaCardRoll<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setRollId(String rollId) {
		set("roll_id", rollId);
	}
	
	public String getRollId() {
		return getStr("roll_id");
	}

	public void setBindingTime(java.util.Date bindingTime) {
		set("binding_time", bindingTime);
	}

	public java.util.Date getBindingTime() {
		return get("binding_time");
	}

	public void setBindChannel(String bindChannel) {
		set("bind_channel", bindChannel);
	}

	public String getBindChannel() {
		return getStr("bind_channel");
	}

	public void setCreateBy(String createBy) { set("create_by", createBy); }

	public String getCreateBy() { return getStr("create_by"); }

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}

	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
