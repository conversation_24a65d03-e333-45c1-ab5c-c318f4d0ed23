package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseDoor<M extends BaseMainBaseDoor<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}
	
	public void setDoorNo(String doorNo) {
		set("door_no", doorNo);
	}
	
	public String getDoorNo() {
		return getStr("door_no");
	}

	public void setDoorName(String doorName) {
		set("door_name", doorName);
	}
	
	public String getDoorName() {
		return getStr("door_name");
	}
	
	public void setLockNo(String lockNo) {
		set("lock_no", lockNo);
	}
	
	public String getLockNo() {
		return getStr("lock_no");
	}

	public void setLockType(String lockType) {
		set("lock_type", lockType);
	}
	
	public String getLockType() {
		return getStr("lock_type");
	}
	
	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
