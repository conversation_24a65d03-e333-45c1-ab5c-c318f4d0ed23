package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMenu<M extends BaseMenu<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setParentId(java.lang.String parentId) {
		set("parent_id", parentId);
	}
	
	public java.lang.String getParentId() {
		return getStr("parent_id");
	}

	public void setParentIds(java.lang.String parentIds) {
		set("parent_ids", parentIds);
	}
	
	public java.lang.String getParentIds() {
		return getStr("parent_ids");
	}

	public void setMenuName(java.lang.String menuName) {
		set("menu_name", menuName);
	}
	
	public java.lang.String getMenuName() {
		return getStr("menu_name");
	}

	public void setMenuIcon(java.lang.String menuIcon) {
		set("menu_icon", menuIcon);
	}
	
	public java.lang.String getMenuIcon() {
		return getStr("menu_icon");
	}

	public void setMenuType(java.lang.String menuType) {
		set("menu_type", menuType);
	}
	
	public java.lang.String getMenuType() {
		return getStr("menu_type");
	}

	public void setMenuUrl(java.lang.String menuUrl) {
		set("menu_url", menuUrl);
	}
	
	public java.lang.String getMenuUrl() {
		return getStr("menu_url");
	}

	public void setMenuSystem(java.lang.String menuSystem) {
		set("menu_system", menuSystem);
	}

	public java.lang.String getMenuSystem() {
		return getStr("menu_system");
	}

	public void setPermission(java.lang.String permission) {
		set("permission", permission);
	}
	
	public java.lang.String getPermission() {
		return getStr("permission");
	}

	public void setMenuSort(java.lang.Integer menuSort) {
		set("menu_sort", menuSort);
	}
	
	public java.lang.Integer getMenuSort() {
		return getInt("menu_sort");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
