package com.cszn.integrated.service.entity.wms;

import com.cszn.integrated.service.entity.wms.base.BaseWmsSuppliersLink;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "wms_suppliers_link", primaryKey = "id")
public class WmsSuppliersLink extends BaseWmsSuppliersLink<WmsSuppliersLink> {
	
	public String getSupplierName(){
		String supplierName = "";
		if(StrKit.notBlank(this.getSupplierId())){
			supplierName = Db.queryStr("select name from wms_suppliers where id=?",this.getSupplierId());
		}
		return supplierName;
	}
}
