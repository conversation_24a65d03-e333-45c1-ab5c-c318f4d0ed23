package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollRecordVerify<M extends BaseCrmCardRollRecordVerify<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRecordId(java.lang.String recordId) {
		set("record_id", recordId);
	}
	
	public java.lang.String getRecordId() {
		return getStr("record_id");
	}

	public void setRollNumber(java.lang.String rollNumber) {
		set("roll_number", rollNumber);
	}
	
	public java.lang.String getRollNumber() {
		return getStr("roll_number");
	}

	public void setVerifyTime(java.util.Date verifyTime) {
		set("verify_time", verifyTime);
	}
	
	public java.util.Date getVerifyTime() {
		return get("verify_time");
	}

	public void setActivityName(java.lang.String activityName) {
		set("activity_name", activityName);
	}
	
	public java.lang.String getActivityName() {
		return getStr("activity_name");
	}

	public void setBranchOfficeId(java.lang.String branchOfficeId) {
		set("branch_office_id", branchOfficeId);
	}
	
	public java.lang.String getBranchOfficeId() {
		return getStr("branch_office_id");
	}

	public void setBaseId(java.lang.String baseId) {
		set("base_id", baseId);
	}
	
	public java.lang.String getBaseId() {
		return getStr("base_id");
	}

	public void setFullName(java.lang.String fullName) {
		set("full_name", fullName);
	}
	
	public java.lang.String getFullName() {
		return getStr("full_name");
	}

	public void setIdcard(java.lang.String idcard) {
		set("idcard", idcard);
	}
	
	public java.lang.String getIdcard() {
		return getStr("idcard");
	}

	public void setPhoneNumber(java.lang.String phoneNumber) {
		set("phone_number", phoneNumber);
	}
	
	public java.lang.String getPhoneNumber() {
		return getStr("phone_number");
	}

	public void setIsMember(java.lang.String isMember) {
		set("is_member", isMember);
	}
	
	public java.lang.String getIsMember() {
		return getStr("is_member");
	}

	public void setCardNumber(java.lang.String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public java.lang.String getCardNumber() {
		return getStr("card_number");
	}

	public void setVerifyStatus(java.lang.String verifyStatus) {
		set("verify_status", verifyStatus);
	}
	
	public java.lang.String getVerifyStatus() {
		return getStr("verify_status");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
