package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

public class BaseMainCsamAssetTypeField<M extends BaseMainCsamAssetTypeField<M>> extends JbootModel<M> implements IBean {

    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

    public void setAssetTypeId(String assetTypeId) {
        set("asset_type_id", assetTypeId);
    }

    public String getAssetTypeId() {
        return getStr("asset_type_id");
    }

    public void setFieldName(String fieldName) {
        set("field_name", fieldName);
    }

    public String getFieldName() {
        return getStr("field_name");
    }

    public void setMaxLength(Integer maxLength) {
        set("max_length", maxLength);
    }

    public String getMaxLength() {
        return getStr("max_length");
    }

    public void setIsNulllable(String isNulllable) {
        set("is_nulllable", isNulllable);
    }

    public String getIsNulllable() {
        return getStr("is_nulllable");
    }

    public void setIsEnabled(String isEnabled) {
        set("is_enabled", isEnabled);
    }

    public String getIsEnabled() {
        return getStr("is_enabled");
    }

    public void setCreateBy(String createBy) {
        set("create_by", createBy);
    }

    public String getCreateBy() {
        return getStr("create_by");
    }

    public void setCreateDate(java.util.Date createDate) {
        set("create_date", createDate);
    }

    public java.util.Date getCreateDate() {
        return get("create_date");
    }

    public void setUpdateBy(String updateBy) {
        set("update_by", updateBy);
    }

    public String getUpdateBy() {
        return getStr("update_by");
    }

    public void setUpdateDate(java.util.Date updateDate) {
        set("update_date", updateDate);
    }

    public java.util.Date getUpdateDate() {
        return get("update_date");
    }

    public void setAssetTypeFieldOrder(Integer assetTypeFieldOrder) {
        set("asset_type_field_order", assetTypeFieldOrder);
    }

    public String getAssetTypeFieldOrder() {
        return getStr("asset_type_field_order");
    }
}
