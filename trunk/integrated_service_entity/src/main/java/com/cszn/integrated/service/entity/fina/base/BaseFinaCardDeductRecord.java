package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardDeductRecord<M extends BaseFinaCardDeductRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setFullName(String fullName) {
		set("full_name", fullName);
	}
	
	public String getFullName() {
		return getStr("full_name");
	}

	public void setWithholdTimes(Double withholdTimes) {
		set("withhold_times", withholdTimes);
	}
	
	public Double getWithholdTimes() {
		return getDouble("withhold_times");
	}

	public void setWithholdAmount(Double withholdAmount) {
		set("withhold_amount", withholdAmount);
	}
	
	public Double getWithholdAmount() {
		return getDouble("withhold_amount");
	}

	public void setWithholdPoints(Double withholdPoints) {
		set("withhold_points", withholdPoints);
	}

	public Double getWithholdPoints() {
		return getDouble("withhold_points");
	}

	public void setWithholdIntegrals(Double withholdIntegrals) {
		set("withhold_integrals", withholdIntegrals);
	}

	public Double getWithholdIntegrals() {
		return getDouble("withhold_integrals");
	}

	public void setSurplusLockTimes(Double surplusLockTimes) {
		set("surplus_lock_times", surplusLockTimes);
	}
	
	public Double getSurplusLockTimes() {
		return getDouble("surplus_lock_times");
	}

	public void setSurplusLockAmount(Double surplusLockAmount) {
		set("surplus_lock_amount", surplusLockAmount);
	}
	
	public Double getSurplusLockAmount() {
		return getDouble("surplus_lock_amount");
	}

	public void setSurplusLockPoints(Double surplusLockPoints) {
		set("surplus_lock_points", surplusLockPoints);
	}

	public Double getSurplusLockPoints() {
		return getDouble("surplus_lock_points");
	}

	public void setSurplusLockIntegrals(Double surplusLockIntegrals) {
		set("surplus_lock_integrals", surplusLockIntegrals);
	}

	public Double getSurplusLockIntegrals() {
		return getDouble("surplus_lock_integrals");
	}

	public void setActualTimes(Double actualTimes) {
		set("actual_times", actualTimes);
	}
	
	public Double getActualTimes() {
		return getDouble("actual_times");
	}

	public void setActualAmount(Double actualAmount) {
		set("actual_amount", actualAmount);
	}
	
	public Double getActualAmount() {
		return getDouble("actual_amount");
	}

	public void setActualPoints(Double actualPoints) {
		set("actual_points", actualPoints);
	}

	public Double getActualPoints() {
		return getDouble("actual_points");
	}

	public void setActualIntegrals(Double actualIntegrals) {
		set("actual_integrals", actualIntegrals);
	}

	public Double getActualIntegrals() {
		return getDouble("actual_integrals");
	}

	public void setDeductedTimes(Double deductedTimes) {
		set("deducted_times", deductedTimes);
	}
	
	public Double getDeductedTimes() {
		return getDouble("deducted_times");
	}

	public void setDeductedAmount(Double deductedAmount) {
		set("deducted_amount", deductedAmount);
	}
	
	public Double getDeductedAmount() {
		return getDouble("deducted_amount");
	}

	public void setDeductedPoints(Double deductedPoints) {
		set("deducted_points", deductedPoints);
	}

	public Double getDeductedPoints() {
		return getDouble("deducted_points");
	}

	public void setDeductedIntegrals(Double deductedIntegrals) {
		set("deducted_integrals", deductedIntegrals);
	}

	public Double getDeductedIntegrals() {
		return getDouble("deducted_integrals");
	}

	public void setSuppleTimes(Double suppleTimes) {
		set("supple_times", suppleTimes);
	}
	
	public Double getSuppleTimes() {
		return getDouble("supple_times");
	}

	public void setSuppleAmount(Double suppleAmount) {
		set("supple_amount", suppleAmount);
	}
	
	public Double getSuppleAmount() {
		return getDouble("supple_amount");
	}

	public void setSupplePoints(Double supplePoints) {
		set("supple_points", supplePoints);
	}

	public Double getSupplePoints() {
		return getDouble("supple_points");
	}

	public void setSuppleIntegrals(Double suppleIntegrals) {
		set("supple_integrals", suppleIntegrals);
	}

	public Double getSuppleIntegrals() {
		return getDouble("supple_integrals");
	}

	public void setReturnTimes(Double returnTimes) {
		set("return_times", returnTimes);
	}
	
	public Double getReturnTimes() {
		return getDouble("return_times");
	}

	public void setReturnAmount(Double returnAmount) {
		set("return_amount", returnAmount);
	}
	
	public Double getReturnAmount() {
		return getDouble("return_amount");
	}

	public void setReturnPoints(Double returnPoints) {
		set("return_points", returnPoints);
	}

	public Double getReturnPoints() {
		return getDouble("return_points");
	}

	public void setReturnIntegrals(Double returnIntegrals) {
		set("return_integrals", returnIntegrals);
	}

	public Double getReturnIntegrals() {
		return getDouble("return_integrals");
	}

	public void setIsSettled(String isSettled) {
		set("is_settled", isSettled);
	}
	
	public String getIsSettled() {
		return getStr("is_settled");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
