package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaExpenseRecordTourist;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_expense_record_tourist", primaryKey = "id")
public class FinaExpenseRecordTourist extends BaseFinaExpenseRecordTourist<FinaExpenseRecordTourist> {

//	public boolean getIsDel() {
//		boolean flag = false;
//		final String touristNo = this.getTouristNo();
//		if(StrKit.notBlank(touristNo)){
//			final int settleDataCount = Db.queryInt("select count(id)dataCount from fina_expense_record where del_flag='0' and settle_status='1' and tourist_no=?", touristNo);
//			if(settleDataCount==0){
//				flag = true;
//			}
//		}
//		return flag;
//	}
}
