package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsSuppliersLink<M extends BaseWmsSuppliersLink<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSupplierId(String supplierId) {
		set("supplier_id", supplierId);
	}
	
	public String getSupplierId() {
		return getStr("supplier_id");
	}
	
	public void setLinkType(String linkType) {
		set("link_type", linkType);
	}
	
	public String getLinkType() {
		return getStr("link_type");
	}
	
	public void setLinkName(String linkName) {
		set("link_name", linkName);
	}
	
	public String getLinkName() {
		return getStr("link_name");
	}
	
	public void setLinkPosition(String linkPosition) {
		set("link_position", linkPosition);
	}
	
	public String getLinkPosition() {
		return getStr("link_position");
	}
	
	public void setLinkPhone(String linkPhone) {
		set("link_phone", linkPhone);
	}
	
	public String getLinkPhone() {
		return getStr("link_phone");
	}
	
	public void setLinkWechat(String linkWechat) {
		set("link_wechat", linkWechat);
	}
	
	public String getLinkWechat() {
		return getStr("link_wechat");
	}
	
	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return get("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
