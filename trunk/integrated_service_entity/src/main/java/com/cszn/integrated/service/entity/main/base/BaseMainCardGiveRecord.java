package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainCardGiveRecord<M extends BaseMainCardGiveRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSchemeId(String schemeId) {
		set("scheme_id", schemeId);
	}
	
	public String getSchemeId() {
		return getStr("scheme_id");
	}

	public void setRuleId(String ruleId) {
		set("rule_id", ruleId);
	}
	
	public String getRuleId() {
		return getStr("rule_id");
	}

	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}

	public void setCycle(String cycle) {
		set("cycle", cycle);
	}
	
	public String getCycle() {
		return getStr("cycle");
	}

	public void setGiveSeq(Integer giveSeq) {
		set("give_seq", giveSeq);
	}
	
	public Integer getGiveSeq() {
		return getInt("give_seq");
	}

	public void setGiveTimes(Double giveTimes) {
		set("give_times", giveTimes);
	}
	
	public Double getGiveTimes() {
		return getDouble("give_times");
	}

	public void setGiveAmount(Double giveAmount) {
		set("give_amount", giveAmount);
	}
	
	public Double getGiveAmount() {
		return getDouble("give_amount");
	}

	public void setGiveDate(java.util.Date giveDate) {
		set("give_date", giveDate);
	}
	
	public java.util.Date getGiveDate() {
		return get("give_date");
	}

}
