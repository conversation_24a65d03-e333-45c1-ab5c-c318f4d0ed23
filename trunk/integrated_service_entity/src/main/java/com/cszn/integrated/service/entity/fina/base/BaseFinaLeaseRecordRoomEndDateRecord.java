package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaLeaseRecordRoomEndDateRecord<M extends BaseFinaLeaseRecordRoomEndDateRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRecordRoomId(String recordRoomId) {
		set("record_room_id", recordRoomId);
	}
	
	public String getRecordRoomId() {
		return getStr("record_room_id");
	}

	public void setOldEndDate(java.util.Date oldEndDate) {
		set("old_end_date", oldEndDate);
	}
	
	public java.util.Date getOldEndDate() {
		return get("old_end_date");
	}

	public void setNewEndDate(java.util.Date newEndDate) {
		set("new_end_date", newEndDate);
	}
	
	public java.util.Date getNewEndDate() {
		return get("new_end_date");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
