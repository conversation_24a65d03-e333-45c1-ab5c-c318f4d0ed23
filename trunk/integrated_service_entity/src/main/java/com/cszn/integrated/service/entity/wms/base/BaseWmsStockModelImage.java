package com.cszn.integrated.service.entity.wms.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockModelImage<M extends BaseWmsStockModelImage<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setModelId(String modelId) {
		set("model_id", modelId);
	}
	
	public String getModelId() {
		return getStr("model_id");
	}

	public void setImgName(String imgName) {
		set("img_name", imgName);
	}

	public String getImgName() {
		return getStr("img_name");
	}

	public void setImgUrl(String imgUrl) {
		set("img_url", imgUrl);
	}
	
	public String getImgUrl() {
		return getStr("img_url");
	}

	public void setIsMain(String isMain) {
		set("is_main", isMain);
	}
	
	public String getIsMain() {
		return getStr("is_main");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}

	public Integer getSort() {
		return get("sort");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

}
