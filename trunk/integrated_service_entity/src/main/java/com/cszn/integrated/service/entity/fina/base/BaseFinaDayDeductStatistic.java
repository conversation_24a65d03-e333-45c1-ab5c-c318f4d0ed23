package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaDayDeductStatistic<M extends BaseFinaDayDeductStatistic<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setStatisticsTime(java.util.Date statisticsTime) {
		set("statistics_time", statisticsTime);
	}
	
	public java.util.Date getStatisticsTime() {
		return get("statistics_time");
	}

	public void setDeductTimes(Integer deductTimes) {
		set("deduct_times", deductTimes);
	}
	
	public Integer getDeductTimes() {
		return getInt("deduct_times");
	}

	public void setDeductAmount(Double deductAmount) {
		set("deduct_amount", deductAmount);
	}
	
	public Double getDeductAmount() {
		return getDouble("deduct_amount");
	}

	public void setDeductPoints(Double deductPoints) {
		set("deduct_points", deductPoints);
	}

	public Double getDeductPoints() {
		return getDouble("deduct_points");
	}

	public void setTotalTimes(Integer totalTimes) {
		set("total_times", totalTimes);
	}
	
	public Integer getTotalTimes() {
		return getInt("total_times");
	}

	public void setTotalAmount(Double totalAmount) {
		set("total_amount", totalAmount);
	}
	
	public Double getTotalAmount() {
		return getDouble("total_amount");
	}

	public void setTotalPoints(Double totalPoints) {
		set("total_points", totalPoints);
	}

	public Double getTotalPoints() {
		return getDouble("total_points");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
