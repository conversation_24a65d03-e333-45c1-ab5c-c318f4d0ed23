package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockTypeUser<M extends BaseWmsStockTypeUser<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTypeId(String typeId) {
		set("type_id", typeId);
	}
	
	public String getTypeId() {
		return getStr("type_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}
	
	public String getUserId() {
		return getStr("user_id");
	}

}
