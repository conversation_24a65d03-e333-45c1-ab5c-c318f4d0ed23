package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

public abstract class BaseFinaExpenseRecordRenew<M extends BaseFinaExpenseRecordRenew<M>> extends JbootModel<M> implements IBean {
    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

    public void setMainExpenseId(String mainExpenseId) {
        set("main_expense_id", mainExpenseId);
    }

    public String getMainExpenseId() {
        return getStr("main_expense_id");
    }

    public void setRenewExpenseId(String renewExpenseId) {
        set("renew_expense_id", renewExpenseId);
    }

    public String getRenewExpenseId() {
        return getStr("renew_expense_id");
    }

}
