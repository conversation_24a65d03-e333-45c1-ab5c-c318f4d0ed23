package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmRelationForm<M extends BaseCrmRelationForm<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRelationId(java.lang.String relationId) {
		set("relation_id", relationId);
	}
	
	public java.lang.String getRelationId() {
		return getStr("relation_id");
	}

	public void setRelationType(java.lang.String relationType) {
		set("relation_type", relationType);
	}
	
	public java.lang.String getRelationType() {
		return getStr("relation_type");
	}
	
	public void setRelationTime(java.util.Date relationTime) {
		set("relation_time", relationTime);
	}
	
	public java.util.Date getRelationTime() {
		return get("relation_time");
	}
	
	public void setRelationBy(java.lang.String relationBy) {
		set("relation_by", relationBy);
	}
	
	public java.lang.String getRelationBy() {
		return getStr("relation_by");
	}
	
	public void setFormId(java.lang.String formId) {
		set("form_id", formId);
	}
	
	public java.lang.String getFormId() {
		return getStr("form_id");
	}
	
	public void setFormNo(java.lang.String formNo) {
		set("form_no", formNo);
	}
	
	public java.lang.String getFormNo() {
		return getStr("form_no");
	}
	
	public void setFormTitle(java.lang.String formTitle) {
		set("form_title", formTitle);
	}
	
	public java.lang.String getFormTitle() {
		return getStr("form_title");
	}
	
	public void setFormTime(java.util.Date formTime) {
		set("form_time", formTime);
	}
	
	public java.util.Date getFormTime() {
		return get("form_time");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
