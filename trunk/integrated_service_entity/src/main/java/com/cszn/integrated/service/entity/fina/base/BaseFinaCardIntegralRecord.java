package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardIntegralRecord<M extends BaseFinaCardIntegralRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardId(java.lang.String cardId) {
		set("card_id", cardId);
	}
	
	public java.lang.String getCardId() {
		return getStr("card_id");
	}

	public void setTransactionType(java.lang.String transactionType) {
		set("transaction_type", transactionType);
	}
	
	public java.lang.String getTransactionType() {
		return getStr("transaction_type");
	}
	
	public void setBusinessType(java.lang.String businessType) {
		set("business_type", businessType);
	}
	
	public java.lang.String getBusinessType() {
		return getStr("business_type");
	}

	public void setGiveDate(java.util.Date giveDate) {
		set("give_date", giveDate);
	}
	
	public java.util.Date getGiveDate() {
		return get("give_date");
	}
	
	public void setCountValue(java.lang.Double countValue) {
		set("count_value", countValue);
	}
	
	public java.lang.Double getCountValue() {
		return getDouble("count_value");
	}
	
	public void setProportionValue(java.lang.Double proportionValue) {
		set("proportion_value", proportionValue);
	}
	
	public java.lang.Double getProportionValue() {
		return getDouble("proportion_value");
	}
	
	public void setIntegralValue(java.lang.Double integralValue) {
		set("integral_value", integralValue);
	}
	
	public java.lang.Double getIntegralValue() {
		return getDouble("integral_value");
	}

	public void setIntegralSnapshot(java.lang.Double integralSnapshot) {
		set("integral_snapshot", integralSnapshot);
	}

	public java.lang.Double getIntegralSnapshot() {
		return getDouble("integral_snapshot");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
