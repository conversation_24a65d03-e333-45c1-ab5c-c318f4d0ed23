package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaMemberBillDetail<M extends BaseFinaMemberBillDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setMainId(String mainId) {
		set("main_id", mainId);
	}
	
	public String getMainId() {
		return getStr("main_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setChargeHandle(String chargeHandle) {
		set("charge_handle", chargeHandle);
	}
	
	public String getChargeHandle() {
		return getStr("charge_handle");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setOriginalAmount(Double originalAmount) {
		set("original_amount", originalAmount);
	}

	public Double getOriginalAmount() {
		return getDouble("original_amount");
	}

	public void setIntegrals(Double integrals) {
		set("integrals", integrals);
	}

	public Double getIntegrals() {
		return getDouble("integrals");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setStartDate(java.util.Date startDate) {
		set("start_date", startDate);
	}
	
	public java.util.Date getStartDate() {
		return get("start_date");
	}

	public void setEndDate(java.util.Date endDate) {
		set("end_date", endDate);
	}
	
	public java.util.Date getEndDate() {
		return get("end_date");
	}

	public void setChargeInterval(String chargeInterval) {
		set("charge_interval", chargeInterval);
	}
	
	public String getChargeInterval() {
		return getStr("charge_interval");
	}

	public void setDeductType(String deductType) {
		set("deduct_type", deductType);
	}
	
	public String getDeductType() {
		return getStr("deduct_type");
	}

	public void setAccountNumber(String accountNumber) {
		set("account_number", accountNumber);
	}
	
	public String getAccountNumber() {
		return getStr("account_number");
	}

	public void setDescribe(String describe){
		set("describe",describe);
	}

	public String getDescribe(){
		return get("describe");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
