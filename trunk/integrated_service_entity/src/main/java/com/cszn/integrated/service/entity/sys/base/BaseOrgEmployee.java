package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseOrgEmployee<M extends BaseOrgEmployee<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setOrgId(java.lang.String orgId) {
		set("org_id", orgId);
	}
	
	public java.lang.String getOrgId() {
		return getStr("org_id");
	}

	public void setOrgCurId(java.lang.String orgCurId) {
		set("org_cur_id", orgCurId);
	}
	
	public java.lang.String getOrgCurId() {
		return getStr("org_cur_id");
	}

	public void setOrgParentId(java.lang.String orgParentId) {
		set("org_parent_id", orgParentId);
	}
	
	public java.lang.String getOrgParentId() {
		return getStr("org_parent_id");
	}

	public void setOrgParentIds(java.lang.String orgParentIds) {
		set("org_parent_ids", orgParentIds);
	}
	
	public java.lang.String getOrgParentIds() {
		return getStr("org_parent_ids");
	}

	public void setPosition(java.lang.String position) {
		set("position", position);
	}
	
	public java.lang.String getPosition() {
		return getStr("position");
	}

	public void setFullName(java.lang.String fullName) {
		set("full_name", fullName);
	}
	
	public java.lang.String getFullName() {
		return getStr("full_name");
	}

	public void setSex(java.lang.String sex) {
		set("sex", sex);
	}
	
	public java.lang.String getSex() {
		return getStr("sex");
	}

	public void setBirthday(java.lang.String birthday) {
		set("birthday", birthday);
	}
	
	public java.lang.String getBirthday() {
		return getStr("birthday");
	}

	public void setIdCard(java.lang.String idCard) {
		set("id_card", idCard);
	}
	
	public java.lang.String getIdCard() {
		return getStr("id_card");
	}

	public void setNationality(java.lang.String nationality) {
		set("nationality", nationality);
	}
	
	public java.lang.String getNationality() {
		return getStr("nationality");
	}

	public void setEducation(java.lang.String education) {
		set("education", education);
	}
	
	public java.lang.String getEducation() {
		return getStr("education");
	}

	public void setMaritalStatus(java.lang.String maritalStatus) {
		set("marital_status", maritalStatus);
	}
	
	public java.lang.String getMaritalStatus() {
		return getStr("marital_status");
	}

	public void setPolitical(java.lang.String political) {
		set("political", political);
	}
	
	public java.lang.String getPolitical() {
		return getStr("political");
	}

	public void setWorkNum(java.lang.String workNum) {
		set("work_num", workNum);
	}
	
	public java.lang.String getWorkNum() {
		return getStr("work_num");
	}

	public void setPhoneNum(java.lang.String phoneNum) {
		set("phone_num", phoneNum);
	}
	
	public java.lang.String getPhoneNum() {
		return getStr("phone_num");
	}

	public void setLinkPeople1(java.lang.String linkPeople1) {
		set("link_people1", linkPeople1);
	}
	
	public java.lang.String getLinkPeople1() {
		return getStr("link_people1");
	}

	public void setLinkPhone1(java.lang.String linkPhone1) {
		set("link_phone1", linkPhone1);
	}
	
	public java.lang.String getLinkPhone1() {
		return getStr("link_phone1");
	}

	public void setLinkPeople2(java.lang.String linkPeople2) {
		set("link_people2", linkPeople2);
	}
	
	public java.lang.String getLinkPeople2() {
		return getStr("link_people2");
	}

	public void setLinkPhone2(java.lang.String linkPhone2) {
		set("link_phone2", linkPhone2);
	}
	
	public java.lang.String getLinkPhone2() {
		return getStr("link_phone2");
	}

	public void setRelation(java.lang.String relation) {
		set("relation", relation);
	}
	
	public java.lang.String getRelation() {
		return getStr("relation");
	}

	public void setResidentAddr(java.lang.String residentAddr) {
		set("resident_addr", residentAddr);
	}
	
	public java.lang.String getResidentAddr() {
		return getStr("resident_addr");
	}

	public void setHeadImg(java.lang.String headImg) {
		set("head_img", headImg);
	}
	
	public java.lang.String getHeadImg() {
		return getStr("head_img");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
