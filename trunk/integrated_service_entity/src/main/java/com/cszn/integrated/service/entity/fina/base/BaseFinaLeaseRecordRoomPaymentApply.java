package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaLeaseRecordRoomPaymentApply<M extends BaseFinaLeaseRecordRoomPaymentApply<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTitle(String title) {
		set("title", title);
	}
	
	public String getTitle() {
		return getStr("title");
	}

	public void setApplyTime(java.util.Date applyTime) {
		set("apply_time", applyTime);
	}
	
	public java.util.Date getApplyTime() {
		return get("apply_time");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setSubmitUserId(String submitUserId) {
		set("submit_user_id", submitUserId);
	}
	
	public String getSubmitUserId() {
		return getStr("submit_user_id");
	}

	public void setCheckUserId(String checkUserId) {
		set("check_user_id", checkUserId);
	}
	
	public String getCheckUserId() {
		return getStr("check_user_id");
	}

	public void setApproveUserId(String approveUserId) {
		set("approve_user_id", approveUserId);
	}
	
	public String getApproveUserId() {
		return getStr("approve_user_id");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setcheckTime(java.util.Date checkTime) {
		set("check_time", checkTime);
	}

	public java.util.Date getcheckTime() {
		return get("check_time");
	}

	public void setApproveTime(java.util.Date approveTime) {
		set("approve_time", approveTime);
	}

	public java.util.Date getApproveTime() {
		return get("approve_time");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
