package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainRoomType<M extends BaseMainRoomType<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSecondLevelId(String secondLevelId) {
		set("second_level_id", secondLevelId);
	}

	public String getSecondLevelId() {
		return getStr("second_level_id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setTypeName(String typeName) {
		set("type_name", typeName);
	}
	
	public String getTypeName() {
		return getStr("type_name");
	}

	public void setFileId(String fileId) {
		set("file_id", fileId);
	}
	
	public String getFileId() {
		return getStr("file_id");
	}

	public void setRoomTypeImage(String roomTypeImage) {
		set("room_type_image", roomTypeImage);
	}
	
	public String getRoomTypeImage() {
		return getStr("room_type_image");
	}

	public void setIsOnlyPrivateRoom(String isOnlyPrivateRoom) {
		set("is_only_private_room", isOnlyPrivateRoom);
	}

	public String getIsOnlyPrivateRoom() {
		return getStr("is_only_private_room");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsBigBedRoom(String isBigBedRoom) {
		set("is_big_bed_room", isBigBedRoom);
	}

	public String getIsBigBedRoom() {
		return getStr("is_big_bed_room");
	}
	
	public void setPrivateRoomDays(Double privateRoomDays) {
		set("private_room_days", privateRoomDays);
	}
	
	public Double getPrivateRoomDays() {
		return getDouble("private_room_days");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

	public void setIsOutBookabled(String isOutBookabled) {
		set("is_out_bookabled", isOutBookabled);
	}

	public String getIsOutBookabled() {
		return getStr("is_out_bookabled");
	}

}
