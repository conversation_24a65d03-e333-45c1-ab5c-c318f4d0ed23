package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaMembershipCard<M extends BaseFinaMembershipCard<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setParentId(String parentId) {
		set("parent_id", parentId);
	}
	
	public String getParentId() {
		return getStr("parent_id");
	}
	
	public void setMemberId(String memberId) {
		set("member_id", memberId);
	}
	
	public String getMemberId() {
		return getStr("member_id");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setCardTypeId(String cardTypeId) {
		set("card_type_id", cardTypeId);
	}
	
	public String getCardTypeId() {
		return getStr("card_type_id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}

	public String getBaseId() {
		return getStr("base_id");
	}

	public void setConditionRechargeSchemeId(String conditionRechargeSchemeId) {
		set("condition_recharge_scheme_id", conditionRechargeSchemeId);
	}

	public String getConditionRechargeSchemeId() {
		return getStr("condition_recharge_scheme_id");
	}

	public void setAccountId(String accountId) {
		set("account_id", accountId);
	}
	
	public String getAccountId() {
		return getStr("account_id");
	}

	public void setOwnerType(String ownerType) {
		set("owner_type", ownerType);
	}
	
	public String getOwnerType() {
		return getStr("owner_type");
	}

	public void setGiveSchemeId(String giveSchemeId) {
		set("give_scheme_id", giveSchemeId);
	}
	
	public String getGiveSchemeId() {
		return getStr("give_scheme_id");
	}

	public void setRechargeSchemeId(String rechargeSchemeId) {
		set("recharge_scheme_id", rechargeSchemeId);
	}

	public String getRechargeSchemeId() {
		return getStr("recharge_scheme_id");
	}

	public void setDeductSchemeId(String deductSchemeId) {
		set("deduct_scheme_id", deductSchemeId);
	}
	
	public String getDeductSchemeId() {
		return getStr("deduct_scheme_id");
	}

	public void setLongDeductSchemeId(String longDeductSchemeId) {
		set("long_deduct_scheme_id", longDeductSchemeId);
	}
	
	public String getLongDeductSchemeId() {
		return getStr("long_deduct_scheme_id");
	}

	public void setYearLimit(String yearLimit) {
		set("year_limit", yearLimit);
	}
	
	public String getYearLimit() {
		return getStr("year_limit");
	}

	public void setCheckinType(String checkinType) {
		set("checkin_type", checkinType);
	}
	
	public String getCheckinType() {
		return getStr("checkin_type");
	}

	public void setBalance(Double balance) {
		set("balance", balance);
	}
	
	public Double getBalance() {
		return getDouble("balance");
	}

	public void setConsumeTimes(Double consumeTimes) {
		set("consume_times", consumeTimes);
	}
	
	public Double getConsumeTimes() {
		return getDouble("consume_times");
	}

	public void setConsumePoints(Double consumePoints) {
		set("consume_points", consumePoints);
	}
	
	public Double getConsumePoints() {
		return getDouble("consume_points");
	}

	public void setCardIntegrals(Double cardIntegrals) {
		set("card_integrals", cardIntegrals);
	}
	
	public Double getCardIntegrals() {
		return getDouble("card_integrals");
	}

	public void setCardMinIntegrals(Double cardMinIntegrals) {
		set("card_min_integrals", cardMinIntegrals);
	}
	
	public Double getCardMinIntegrals() {
		return getDouble("card_min_integrals");
	}

	public void setBeanCoupons(Double beanCoupons) {
		set("bean_coupons", beanCoupons);
	}
	
	public Double getBeanCoupons() {
		return getDouble("bean_coupons");
	}

	public void setAccountBalance(Double accountBalance) {
		set("account_balance", accountBalance);
	}
	
	public Double getAccountBalance() {
		return getDouble("account_balance");
	}

	public void setGiveBalance(Double giveBalance) {
		set("give_balance", giveBalance);
	}
	
	public Double getGiveBalance() {
		return getDouble("give_balance");
	}

	public void setGiveConsumeTimes(Double giveConsumeTimes) {
		set("give_consume_times", giveConsumeTimes);
	}
	
	public Double getGiveConsumeTimes() {
		return getDouble("give_consume_times");
	}

	public void setGiveConsumePoints(Double giveConsumePoints) {
		set("give_consume_points", giveConsumePoints);
	}
	
	public Double getGiveConsumePoints() {
		return getDouble("give_consume_points");
	}

	public void setGiveConsumeIntegrals(Double giveConsumeIntegrals) {
		set("give_consume_integrals", giveConsumeIntegrals);
	}
	
	public Double getGiveConsumeIntegrals() {
		return getDouble("give_consume_integrals");
	}

	public void setGiveBeanCoupons(Double giveBeanCoupons) {
		set("give_bean_coupons", giveBeanCoupons);
	}
	
	public Double getGiveBeanCoupons() {
		return getDouble("give_bean_coupons");
	}

	public void setSubsectionTotalRechargeTimes(Double subsectionTotalRechargeTimes) {
		set("subsection_total_recharge_times", subsectionTotalRechargeTimes);
	}
	
	public Double getSubsectionTotalRechargeTimes() {
		return getDouble("subsection_total_recharge_times");
	}

	public void setSubsectionTotalGiveTimes(Double subsectionTotalGiveTimes) {
		set("subsection_total_give_times", subsectionTotalGiveTimes);
	}
	
	public Double getSubsectionTotalGiveTimes() {
		return getDouble("subsection_total_give_times");
	}

	public void setSubsectionGiveTimes(String subsectionGiveTimes) {
		set("subsection_give_times", subsectionGiveTimes);
	}
	
	public String getSubsectionGiveTimes() {
		return getStr("subsection_give_times");
	}

	public void setIsSubsectionGive(String isSubsectionGive) {
		set("is_subsection_give", isSubsectionGive);
	}
	
	public String getIsSubsectionGive() {
		return getStr("is_subsection_give");
	}

	public void setLockBalance(Double lockBalance) {
		set("lock_balance", lockBalance);
	}
	
	public Double getLockBalance() {
		return getDouble("lock_balance");
	}

	public void setLockConsumeTimes(Double lockConsumeTimes) {
		set("lock_consume_times", lockConsumeTimes);
	}
	
	public Double getLockConsumeTimes() {
		return getDouble("lock_consume_times");
	}

	public void setLockConsumePoints(Double lockConsumePoints) {
		set("lock_consume_points", lockConsumePoints);
	}
	
	public Double getLockConsumePoints() {
		return getDouble("lock_consume_points");
	}

	public void setLockConsumeIntegrals(Double lockConsumeIntegrals) {
		set("lock_consume_integrals", lockConsumeIntegrals);
	}
	
	public Double getLockConsumeIntegrals() {
		return getDouble("lock_consume_integrals");
	}

	public void setLockBeanCoupons(Double lockBeanCoupons) {
		set("lock_bean_coupons", lockBeanCoupons);
	}
	
	public Double getLockBeanCoupons() {
		return getDouble("lock_bean_coupons");
	}

	public void setGiveRechargeAmount(Double giveRechargeAmount) {
		set("give_recharge_amount", giveRechargeAmount);
	}

	public Double getGiveRechargeAmount() {
		return getDouble("give_recharge_amount");
	}

	public void setGiveRechargeDays(Double giveRechargeDays) {
		set("give_recharge_days", giveRechargeDays);
	}

	public Double getGiveRechargeDays() {
		return getDouble("give_recharge_days");
	}

	public void setGiveRechargeIntegrals(Double giveRechargeIntegrals) {
		set("give_recharge_integrals", giveRechargeIntegrals);
	}

	public Double getGiveRechargeIntegrals() {
		return getDouble("give_recharge_integrals");
	}

	public void setBuyRechargeAmount(Double buyRechargeAmount) {
		set("buy_recharge_amount", buyRechargeAmount);
	}

	public Double getBuyRechargeAmount() {
		return getDouble("buy_recharge_amount");
	}

	public void setBuyRechargeDays(Double buyRechargeDays) {
		set("buy_recharge_days", buyRechargeDays);
	}

	public Double getBuyRechargeDays() {
		return getDouble("buy_recharge_days");
	}

	public void setBuyRechargeIntegrals(Double buyRechargeIntegrals) {
		set("buy_recharge_integrals", buyRechargeIntegrals);
	}

	public Double getBuyRechargeIntegrals() {
		return getDouble("buy_recharge_integrals");
	}
	
	public void setCashSpendPrice(Double cashSpendPrice) {
		set("cash_spend_price", cashSpendPrice);
	}
	
	public Double getCashSpendPrice() {
		return getDouble("cash_spend_price");
	}
	
	public void setCashSpendCountAmount(Double cashSpendCountAmount) {
		set("cash_spend_count_amount", cashSpendCountAmount);
	}
	
	public Double getCashSpendCountAmount() {
		return getDouble("cash_spend_count_amount");
	}
	
	public void setCashSpendCountDays(Double cashSpendCountDays) {
		set("cash_spend_count_days", cashSpendCountDays);
	}
	
	public Double getCashSpendCountDays() {
		return getDouble("cash_spend_count_days");
	}
	
	public void setSpendGiveCountDays(Double spendGiveCountDays) {
		set("spend_give_count_days", spendGiveCountDays);
	}
	
	public Double getSpendGiveCountDays() {
		return getDouble("spend_give_count_days");
	}

	public void setUseYears(String useYears) {
		set("use_years", useYears);
	}
	
	public String getUseYears() {
		return getStr("use_years");
	}

	public void setPurchaseCardBalance(Double purchaseCardBalance) {
		set("purchase_card_balance", purchaseCardBalance);
	}
	
	public Double getPurchaseCardBalance() {
		return getDouble("purchase_card_balance");
	}

	public void setPrice(Double price) {
		set("price", price);
	}
	
	public Double getPrice() {
		return getDouble("price");
	}

	public void setReferencePrice(Double referencePrice) {
		set("reference_price", referencePrice);
	}
	
	public Double getReferencePrice() {
		return getDouble("reference_price");
	}

	public void setCountPrice(Double countPrice) {
		set("count_price", countPrice);
	}
	
	public Double getCountPrice() {
		return getDouble("count_price");
	}

	public void setCashMoney(Double cashMoney) {
		set("cash_money", cashMoney);
	}
	
	public Double getCashMoney() {
		return getDouble("cash_money");
	}

	public void setAccountMoney(Double accountMoney) {
		set("account_money", accountMoney);
	}
	
	public Double getAccountMoney() {
		return getDouble("account_money");
	}

	public void setEntryCompany(String entryCompany) {
		set("entry_company", entryCompany);
	}
	
	public String getEntryCompany() {
		return getStr("entry_company");
	}

	public void setOpenTime(java.util.Date openTime) {
		set("open_time", openTime);
	}
	
	public java.util.Date getOpenTime() {
		return get("open_time");
	}

	public void setBranchOfficeId(String branchOfficeId) {
		set("branch_office_id", branchOfficeId);
	}
	
	public String getBranchOfficeId() {
		return getStr("branch_office_id");
	}

	public void setSalesDeptId(String salesDeptId) {
		set("sales_dept_id", salesDeptId);
	}
	
	public String getSalesDeptId() {
		return getStr("sales_dept_id");
	}

	public void setOperator(String operator) {
		set("operator", operator);
	}
	
	public String getOperator() {
		return getStr("operator");
	}
	
	public void setAgentId(String agentId) {
		set("agent_id", agentId);
	}
	
	public String getAgentId() {
		return getStr("agent_id");
	}
	
	public void setAgentSalesId(String agentSalesId) {
		set("agent_sales_id", agentSalesId);
	}
	
	public String getAgentSalesId() {
		return getStr("agent_sales_id");
	}

	public void setTelephone(String telephone) {
		set("telephone", telephone);
	}
	
	public String getTelephone() {
		return getStr("telephone");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setContractType(String contractType) {
		set("contract_type", contractType);
	}
	
	public String getContractType() {
		return getStr("contract_type");
	}

	public void setContractNumber(String contractNumber) {
		set("contract_number", contractNumber);
	}
	
	public String getContractNumber() {
		return getStr("contract_number");
	}

	public void setContractTimes(Double contractTimes) {
		set("contract_times", contractTimes);
	}
	
	public Double getContractTimes() {
		return getDouble("contract_times");
	}

	public void setContractDiscount(Double contractDiscount) {
		set("contract_discount", contractDiscount);
	}
	
	public Double getContractDiscount() {
		return getDouble("contract_discount");
	}

	public void setCollectAmount(Double collectAmount) {
		set("collect_amount", collectAmount);
	}
	
	public Double getCollectAmount() {
		return getDouble("collect_amount");
	}

	public void setIsLock(String isLock) {
		set("is_lock", isLock);
	}
	
	public String getIsLock() {
		return getStr("is_lock");
	}

	public void setLockDate(java.util.Date lockDate) {
		set("lock_date", lockDate);
	}
	
	public java.util.Date getLockDate() {
		return get("lock_date");
	}

	public void setLockRemark(String lockRemark) {
		set("lock_remark", lockRemark);
	}
	
	public String getLockRemark() {
		return getStr("lock_remark");
	}

	public void setLockBy(String lockBy) {
		set("lock_by", lockBy);
	}
	
	public String getLockBy() {
		return getStr("lock_by");
	}

	public void setIsCash(String isCash) {
		set("is_cash", isCash);
	}
	
	public String getIsCash() {
		return getStr("is_cash");
	}

	public void setPassword(String password) {
		set("password", password);
	}
	
	public String getPassword() {
		return getStr("password");
	}

	public void setSalt(String salt) {
		set("salt", salt);
	}
	
	public String getSalt() {
		return getStr("salt");
	}

	public void setIsEnablePwd(String isEnablePwd) {
		set("is_enable_pwd", isEnablePwd);
	}
	
	public String getIsEnablePwd() {
		return getStr("is_enable_pwd");
	}

	public void setExpireFlag(String expireFlag) {
		set("expire_flag", expireFlag);
	}
	
	public String getExpireFlag() {
		return getStr("expire_flag");
	}

	public void setExpireDate(java.util.Date expireDate) {
		set("expire_date", expireDate);
	}
	
	public java.util.Date getExpireDate() {
		return get("expire_date");
	}

	public void setDelayRemark(String delayRemark) {
		set("delay_remark", delayRemark);
	}
	
	public String getDelayRemark() {
		return getStr("delay_remark");
	}

	public void setIsBreakfast(String isBreakfast) {
		set("is_breakfast", isBreakfast);
	}

	public String getIsBreakfast() {
		return getStr("is_breakfast");
	}

	public void setIsLunch(String isLunch) {
		set("is_lunch", isLunch);
	}
	
	public String getIsLunch() {
		return getStr("is_lunch");
	}

	public void setIsDinner(String isDinner) {
		set("is_dinner", isDinner);
	}
	
	public String getIsDinner() {
		return getStr("is_dinner");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsMakeCard(String isMakeCard) {
		set("is_make_card", isMakeCard);
	}
	
	public String getIsMakeCard() {
		return getStr("is_make_card");
	}

	public void setReturnCardStatus(String returnCardStatus) {
		set("return_card_status", returnCardStatus);
	}
	
	public String getReturnCardStatus() {
		return getStr("return_card_status");
	}

	public void setVoidDate(java.util.Date voidDate) {
		set("void_date", voidDate);
	}
	
	public java.util.Date getVoidDate() {
		return get("void_date");
	}

	public void setVoidRemark(String voidRemark) {
		set("void_remark", voidRemark);
	}
	
	public String getVoidRemark() {
		return getStr("void_remark");
	}

	public void setVoidBy(String voidBy) {
		set("void_by", voidBy);
	}
	
	public String getVoidBy() {
		return getStr("void_by");
	}

	public void setArchiveNo(String archiveNo) {
		set("archive_no", archiveNo);
	}
	
	public String getArchiveNo() {
		return getStr("archive_no");
	}

	public void setDepositPlace(String depositPlace) {
		set("deposit_place", depositPlace);
	}
	
	public String getDepositPlace() {
		return getStr("deposit_place");
	}

	public void setIsReport(String isReport) {
		set("is_report", isReport);
	}
	
	public String getIsReport() {
		return getStr("is_report");
	}
	
	public void setIsNotSendSms(String isNotSendSms) {
		set("is_not_send_sms", isNotSendSms);
	}
	
	public String getIsNotSendSms() {
		return getStr("is_not_send_sms");
	}
	
	public void setDataSource(String dataSource) {
		set("data_source", dataSource);
	}
	
	public String getDataSource() {
		return getStr("data_source");
	}
	
	public void setGiveContractTimes(Double giveContractTimes) {
		set("give_contract_times", giveContractTimes);
	}
	
	public Double getGiveContractTimes() {
		return getDouble("give_contract_times");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
