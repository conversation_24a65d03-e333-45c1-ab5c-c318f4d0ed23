package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseArea<M extends BaseArea<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setParentId(java.lang.String parentId) {
		set("parent_id", parentId);
	}
	
	public java.lang.String getParentId() {
		return getStr("parent_id");
	}

	public void setParentIds(java.lang.String parentIds) {
		set("parent_ids", parentIds);
	}
	
	public java.lang.String getParentIds() {
		return getStr("parent_ids");
	}

	public void setAreaCode(java.lang.String areaCode) {
		set("area_code", areaCode);
	}
	
	public java.lang.String getAreaCode() {
		return getStr("area_code");
	}

	public void setAreaName(java.lang.String areaName) {
		set("area_name", areaName);
	}
	
	public java.lang.String getAreaName() {
		return getStr("area_name");
	}

	public void setAreaLv(java.lang.Integer areaLv) {
		set("area_lv", areaLv);
	}
	
	public java.lang.Integer getAreaLv() {
		return getInt("area_lv");
	}

	public void setAreaSort(java.lang.Integer areaSort) {
		set("area_sort", areaSort);
	}
	
	public java.lang.Integer getAreaSort() {
		return getInt("area_sort");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
