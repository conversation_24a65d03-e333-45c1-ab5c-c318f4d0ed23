package com.cszn.integrated.service.entity.main;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.main.base.BaseMainExamineRecord;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_examine_record", primaryKey = "id")
public class MainExamineRecord extends BaseMainExamineRecord<MainExamineRecord> {

    public Record getCreateUser(){
        return Db.findById("sys_user",this.getCreateBy());
    }

    public Record getAnswerUser(){
        return Db.findById("sys_user",this.getAnswerUserId());
    }

}
