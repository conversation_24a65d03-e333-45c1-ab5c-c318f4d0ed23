package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaExpenseRecordFollowLeave<M extends BaseFinaExpenseRecordFollowLeave<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setUniqueId(String uniqueId) {
		set("unique_id", uniqueId);
	}
	
	public String getUniqueId() {
		return getStr("unique_id");
	}

	public void setCheckinNo(String checkinNo) {
		set("checkin_no", checkinNo);
	}
	
	public String getCheckinNo() {
		return getStr("checkin_no");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setLeaveTime(java.util.Date leaveTime) {
		set("leave_time", leaveTime);
	}
	
	public java.util.Date getLeaveTime() {
		return get("leave_time");
	}

	public void setReturnTime(java.util.Date returnTime) {
		set("return_time", returnTime);
	}
	
	public java.util.Date getReturnTime() {
		return get("return_time");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
