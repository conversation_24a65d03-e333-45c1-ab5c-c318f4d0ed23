package com.cszn.integrated.service.entity.fina;

import java.util.List;

import com.cszn.integrated.service.entity.fina.base.BaseFinaCardRefund;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_card_refund", primaryKey = "id")
public class FinaCardRefund extends BaseFinaCardRefund<FinaCardRefund> {
	
	private List<FinaCardRefundDetail> detailList;

	public String getDetailStr() {
		String detailStr = "";
		Record record = Db.findFirst("select refund_id,GROUP_CONCAT(CONCAT(card_number,',',member_name,',',card_type) SEPARATOR '||')detailStr from fina_card_refund_detail where del_flag='0' and refund_id=? group by refund_id", getId());
		if(record != null) {
			detailStr = record.getStr("detailStr");
		}
		return detailStr;
	}
	
	public List<FinaCardRefundDetail> getDetailList() {
		return detailList;
	}

	public void setDetailList(List<FinaCardRefundDetail> detailList) {
		this.detailList = detailList;
	}
}
