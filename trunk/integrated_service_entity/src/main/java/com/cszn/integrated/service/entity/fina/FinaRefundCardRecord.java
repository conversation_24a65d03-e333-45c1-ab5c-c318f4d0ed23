package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaRefundCardRecord;
import com.cszn.integrated.service.entity.status.YesNo;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_refund_card_record", primaryKey = "id")
public class FinaRefundCardRecord extends BaseFinaRefundCardRecord<FinaRefundCardRecord> {
	
	
	public String getPayWayName() {
		return Db.queryStr("select name from main_payment_ways where is_enabled='1' and payment_way_code=?", this.getPayWay());
	}

	public String getIsPayName(){
		return YesNo.me().desc(this.getIsPay());
	}
}
