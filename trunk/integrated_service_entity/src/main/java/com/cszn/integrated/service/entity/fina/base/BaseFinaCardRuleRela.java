package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardRuleRela<M extends BaseFinaCardRuleRela<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardId(java.lang.String cardId) {
		set("card_id", cardId);
	}
	
	public java.lang.String getCardId() {
		return getStr("card_id");
	}

	public void setRuleId(java.lang.String ruleId) {
		set("rule_id", ruleId);
	}
	
	public java.lang.String getRuleId() {
		return getStr("rule_id");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

}
