package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainExamineRecordResult<M extends BaseMainExamineRecordResult<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setRecordId(String recordId) {
		set("record_id", recordId);
	}
	
	public String getRecordId() {
		return getStr("record_id");
	}

	public void setProblemId(String problemId) {
		set("problem_id", problemId);
	}
	
	public String getProblemId() {
		return getStr("problem_id");
	}

	public void setOptionIds(String optionIds) {
		set("option_ids", optionIds);
	}
	
	public String getOptionIds() {
		return getStr("option_ids");
	}

	public void setContent(String content) {
		set("content", content);
	}
	
	public String getContent() {
		return getStr("content");
	}

	public void setOptionTextContent(String optionTextContent) {
		set("option_text_content", optionTextContent);
	}

	public String getOptionTextContent() {
		return getStr("option_text_content");
	}

	public void setFiles(String files) {
		set("files", files);
	}

	public String getFiles() {
		return getStr("files");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}

	public String getRemark() {
		return getStr("remark");
	}

	public void setTotalScore(Double totalScore) {
		set("total_score", totalScore);
	}

	public Double getTotalScore() {
		return getDouble("total_score");
	}

	public void setScore(Double score) {
		set("score", score);
	}

	public Double getScore() {
		return getDouble("score");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}

	public Integer getSort() {
		return getInt("sort");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
