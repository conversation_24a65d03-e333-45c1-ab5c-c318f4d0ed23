package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockModels<M extends BaseWmsStockModels<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setStockTypeId(String stockTypeId) {
		set("stock_type_id", stockTypeId);
	}
	
	public String getStockTypeId() {
		return getStr("stock_type_id");
	}

	public void setStockModelNo(String stockModelNo) {
		set("stock_model_no", stockModelNo);
	}
	
	public String getStockModelNo() {
		return getStr("stock_model_no");
	}

	public void setNewStockModelNo(String newStockModelNo) {
		set("new_stock_model_no", newStockModelNo);
	}
	
	public String getNewStockModelNo() {
		return getStr("new_stock_model_no");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setType(String type) {
		set("type", type);
	}
	
	public String getType() {
		return getStr("type");
	}

	public void setNotSelfPickupIds(String notSelfPickupIds) {
		set("not_self_pickup_ids", notSelfPickupIds);
	}

	public String getNotSelfPickupIds() {
		return getStr("not_self_pickup_ids");
	}

	public void setBrandId(String brandId) {
		set("brand_id", brandId);
	}
	
	public String getBrandId() {
		return getStr("brand_id");
	}

	public void setUnitId(String unitId) {
		set("unit_id", unitId);
	}
	
	public String getUnitId() {
		return getStr("unit_id");
	}

	public void setUnit(String unit) {
		set("unit", unit);
	}
	
	public String getUnit() {
		return getStr("unit");
	}

	public void setIsRmbPay(String isRmbPay) {
		set("is_rmb_pay", isRmbPay);
	}
	
	public String getIsRmbPay() {
		return getStr("is_rmb_pay");
	}

	public void setSalePrice(Double salePrice) {
		set("sale_price", salePrice);
	}
	
	public Double getSalePrice() {
		return getDouble("sale_price");
	}

	public void setCostPrice(Double costPrice) {
		set("cost_price", costPrice);
	}
	
	public Double getCostPrice() {
		return getDouble("cost_price");
	}

	public void setExchangePrice(Double exchangePrice) {
		set("exchange_price", exchangePrice);
	}
	
	public Double getExchangePrice() {
		return getDouble("exchange_price");
	}

	public void setIsBeanCouponsPay(String isBeanCouponsPay) {
		set("is_bean_coupons_pay", isBeanCouponsPay);
	}
	
	public String getIsBeanCouponsPay() {
		return getStr("is_bean_coupons_pay");
	}

	public void setIsCardIntegralPay(String isCardIntegralPay) {
		set("is_card_integral_pay", isCardIntegralPay);
	}
	
	public String getIsCardIntegralPay() {
		return getStr("is_card_integral_pay");
	}

	public void setExchangeBeanCoupons(Double exchangeBeanCoupons) {
		set("exchange_bean_coupons", exchangeBeanCoupons);
	}
	
	public Double getExchangeBeanCoupons() {
		return getDouble("exchange_bean_coupons");
	}

	public void setIsCardTimesPay(String isCardTimesPay) {
		set("is_card_times_pay", isCardTimesPay);
	}
	
	public String getIsCardTimesPay() {
		return getStr("is_card_times_pay");
	}

	public void setExchangeTimes(Double exchangeTimes) {
		set("exchange_times", exchangeTimes);
	}
	
	public Double getExchangeTimes() {
		return getDouble("exchange_times");
	}

	public void setIsCardAmountPay(String isCardAmountPay) {
		set("is_card_amount_pay", isCardAmountPay);
	}
	
	public String getIsCardAmountPay() {
		return getStr("is_card_amount_pay");
	}

	public void setExchangeAmount(Double exchangeAmount) {
		set("exchange_amount", exchangeAmount);
	}
	
	public Double getExchangeAmount() {
		return getDouble("exchange_amount");
	}

	public void setIsStaffIntegralPay(String isStaffIntegralPay) {
		set("is_staff_integral_pay", isStaffIntegralPay);
	}

	public String getIsStaffIntegralPay() {
		return getStr("is_staff_integral_pay");
	}

	public void setExchangeStaffIntegral(Double exchangeStaffIntegral) {
		set("exchange_staff_integral", exchangeStaffIntegral);
	}

	public Double getExchangeStaffIntegral() {
		return getDouble("exchange_staff_integral");
	}

	public void setStandard(String standard) {
		set("standard", standard);
	}
	
	public String getStandard() {
		return getStr("standard");
	}

	public void setWarningMin(Integer warningMin) {
		set("warning_min", warningMin);
	}
	
	public Integer getWarningMin() {
		return getInt("warning_min");
	}

	public void setOutType(String outType) {
		set("out_type", outType);
	}
	
	public String getOutType() {
		return getStr("out_type");
	}

	public void setIsNoStockSale(String isNoStockSale) {
		set("is_no_stock_sale", isNoStockSale);
	}

	public String getIsNoStockSale() {
		return getStr("is_no_stock_sale");
	}

	public void setIsExternal(String isExternal) {
		set("is_external", isExternal);
	}

	public String getIsExternal() {
		return getStr("is_external");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setIsExchange(String isExchange) {
		set("is_exchange", isExchange);
	}
	
	public String getIsExchange() {
		return getStr("is_exchange");
	}

	public void setBarcodeNumber(String barcodeNumber) {
		set("barcode_number", barcodeNumber);
	}
	
	public String getBarcodeNumber() {
		return getStr("barcode_number");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
