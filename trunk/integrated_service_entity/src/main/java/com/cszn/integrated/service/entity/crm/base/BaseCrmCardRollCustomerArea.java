package com.cszn.integrated.service.entity.crm.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseCrmCardRollCustomerArea<M extends BaseCrmCardRollCustomerArea<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setCardRollId(java.lang.String cardRollId) {
		set("card_roll_id", cardRollId);
	}
	
	public java.lang.String getCardRollId() {
		return getStr("card_roll_id");
	}

	public void setCountry(java.lang.String country) {
		set("country", country);
	}
	
	public java.lang.String getCountry() {
		return getStr("country");
	}

	public void setProvince(java.lang.String province) {
		set("province", province);
	}
	
	public java.lang.String getProvince() {
		return getStr("province");
	}

	public void setCity(java.lang.String city) {
		set("city", city);
	}
	
	public java.lang.String getCity() {
		return getStr("city");
	}

	public void setTown(java.lang.String town) {
		set("town", town);
	}
	
	public java.lang.String getTown() {
		return getStr("town");
	}

	public void setStreet(java.lang.String street) {
		set("street", street);
	}
	
	public java.lang.String getStreet() {
		return getStr("street");
	}

}
