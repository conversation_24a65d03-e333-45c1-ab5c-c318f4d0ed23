package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseBedMarkupSchemeDetail<M extends BaseMainBaseBedMarkupSchemeDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSchemeId(String schemeId) {
		set("scheme_id", schemeId);
	}
	
	public String getSchemeId() {
		return getStr("scheme_id");
	}

	public void setBedId(String bedId) {
		set("bed_id", bedId);
	}
	
	public String getBedId() {
		return getStr("bed_id");
	}

	public void setMarkupTypeId(String markupTypeId) {
		set("markup_type_id", markupTypeId);
	}

	public String getMarkupTypeId() {
		return getStr("markup_type_id");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}

	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}

	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}

	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}

	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}

	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
