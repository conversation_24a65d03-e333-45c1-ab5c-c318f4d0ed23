package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaExpenseRecordTourist<M extends BaseFinaExpenseRecordTourist<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setTouristNo(String touristNo) {
		set("tourist_no", touristNo);
	}
	
	public String getTouristNo() {
		return getStr("tourist_no");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setBusinessEntityId(String businessEntityId) {
		set("business_entity_id", businessEntityId);
	}

	public String getBusinessEntityId() {
		return getStr("business_entity_id");
	}

	public void setCustomerChannel(String customerChannel) {
		set("customer_channel", customerChannel);
	}

	public String getCustomerChannel() {
		return getStr("customer_channel");
	}

	public void setRoute(String route) {
		set("route", route);
	}
	
	public String getRoute() {
		return getStr("route");
	}

	public void setRouteId(String routeId) {
		set("route_id", routeId);
	}
	
	public String getRouteId() {
		return getStr("route_id");
	}

	public void setStartDate(java.util.Date startDate) {
		set("start_date", startDate);
	}
	
	public java.util.Date getStartDate() {
		return get("start_date");
	}

	public void setEndDate(java.util.Date endDate) {
		set("end_date", endDate);
	}
	
	public java.util.Date getEndDate() {
		return get("end_date");
	}

	public void setExpenseTimes(Double expenseTimes) {
		set("expense_times", expenseTimes);
	}
	
	public Double getExpenseTimes() {
		return getDouble("expense_times");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
