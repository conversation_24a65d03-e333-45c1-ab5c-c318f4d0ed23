package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardGiveRuleDetail<M extends BaseFinaCardGiveRuleDetail<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRuleId(java.lang.String ruleId) {
		set("rule_id", ruleId);
	}
	
	public java.lang.String getRuleId() {
		return getStr("rule_id");
	}

	public void setTimeSeq(java.lang.Integer timeSeq) {
		set("time_seq", timeSeq);
	}
	
	public java.lang.Integer getTimeSeq() {
		return getInt("time_seq");
	}

	public void setGiveTimes(java.lang.Integer giveTimes) {
		set("give_times", giveTimes);
	}
	
	public java.lang.Integer getGiveTimes() {
		return getInt("give_times");
	}

	public void setGiveAmount(java.lang.Double giveAmount) {
		set("give_amount", giveAmount);
	}
	
	public java.lang.Double getGiveAmount() {
		return getDouble("give_amount");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
