package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockModelWarehousePrice<M extends BaseWmsStockModelWarehousePrice<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setStockModelId(String stockModelId) {
		set("stock_model_id", stockModelId);
	}
	
	public String getStockModelId() {
		return getStr("stock_model_id");
	}

	public void setWarehouseId(String warehouseId) {
		set("warehouse_id", warehouseId);
	}
	
	public String getWarehouseId() {
		return getStr("warehouse_id");
	}

	public void setSalePrice(Double salePrice) {
		set("sale_price", salePrice);
	}
	
	public Double getSalePrice() {
		return getDouble("sale_price");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}

	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}

	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}

	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}

	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}

	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
