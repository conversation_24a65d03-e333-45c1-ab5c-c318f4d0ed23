package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBasePackageFood<M extends BaseMainBasePackageFood<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setPackageId(String packageId) {
		set("package_id", packageId);
	}
	
	public String getPackageId() {
		return getStr("package_id");
	}

	public void setFoodId(String foodId) {
		set("food_id", foodId);
	}
	
	public String getFoodId() {
		return getStr("food_id");
	}
	
	public void setSortValue(java.lang.Integer sortValue) {
		set("sort_value", sortValue);
	}
	
	public java.lang.Integer getSortValue() {
		return getInt("sort_value");
	}
}
