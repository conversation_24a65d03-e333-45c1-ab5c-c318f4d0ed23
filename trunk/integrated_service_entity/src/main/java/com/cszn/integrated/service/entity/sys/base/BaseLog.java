package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseLog<M extends BaseLog<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setSystemType(java.lang.String systemType) {
		set("system_type", systemType);
	}
	
	public java.lang.String getSystemType() {
		return getStr("system_type");
	}

	public void setLogTitle(java.lang.String logTitle) {
		set("log_title", logTitle);
	}
	
	public java.lang.String getLogTitle() {
		return getStr("log_title");
	}

	public void setRemoteAddr(java.lang.String remoteAddr) {
		set("remote_addr", remoteAddr);
	}
	
	public java.lang.String getRemoteAddr() {
		return getStr("remote_addr");
	}

	public void setRequestUri(java.lang.String requestUri) {
		set("request_uri", requestUri);
	}
	
	public java.lang.String getRequestUri() {
		return getStr("request_uri");
	}

	public void setParams(java.lang.String params) {
		set("params", params);
	}
	
	public java.lang.String getParams() {
		return getStr("params");
	}

	public void setMethod(java.lang.String method) {
		set("method", method);
	}
	
	public java.lang.String getMethod() {
		return getStr("method");
	}

	public void setUserAgent(java.lang.String userAgent) {
		set("user_agent", userAgent);
	}
	
	public java.lang.String getUserAgent() {
		return getStr("user_agent");
	}

	public void setException(java.lang.String exception) {
		set("exception", exception);
	}
	
	public java.lang.String getException() {
		return getStr("exception");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

}
