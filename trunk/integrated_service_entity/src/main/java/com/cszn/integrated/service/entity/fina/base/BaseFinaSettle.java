package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaSettle<M extends BaseFinaSettle<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCheckinNo(String checkinNo) {
		set("checkin_no", checkinNo);
	}
	
	public String getCheckinNo() {
		return getStr("checkin_no");
	}

	public void setExpenseId(String expenseId) {
		set("expense_id", expenseId);
	}
	
	public String getExpenseId() {
		return getStr("expense_id");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setSettleType(String settleType) {
		set("settle_type", settleType);
	}
	
	public String getSettleType() {
		return getStr("settle_type");
	}

	public void setUnlockTimes(Double unlockTimes) {
		set("unlock_times", unlockTimes);
	}
	
	public Double getUnlockTimes() {
		return getDouble("unlock_times");
	}

	public void setUnlockAmount(Double unlockAmount) {
		set("unlock_amount", unlockAmount);
	}
	
	public Double getUnlockAmount() {
		return getDouble("unlock_amount");
	}

	public void setUnlockPoints(Double unlockPoints) {
		set("unlock_points", unlockPoints);
	}

	public Double getUnlockPoints() {
		return getDouble("unlock_points");
	}

	public void setUnlockIntegrals(Double unlockIntegrals) {
		set("unlock_integrals", unlockIntegrals);
	}

	public Double getUnlockIntegrals() {
		return getDouble("unlock_integrals");
	}

	public void setActualTimes(Double actualTimes) {
		set("actual_times", actualTimes);
	}
	
	public Double getActualTimes() {
		return getDouble("actual_times");
	}

	public void setActualAmount(Double actualAmount) {
		set("actual_amount", actualAmount);
	}
	
	public Double getActualAmount() {
		return getDouble("actual_amount");
	}

	public void setActualPoints(Double actualPoints) {
		set("actual_points", actualPoints);
	}

	public Double getActualPoints() {
		return getDouble("actual_points");
	}

	public void setActualIntegrals(Double actualIntegrals) {
		set("actual_integrals", actualIntegrals);
	}

	public Double getActualIntegrals() {
		return getDouble("actual_integrals");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setSettleBy(String settleBy) {
		set("settle_by", settleBy);
	}
	
	public String getSettleBy() {
		return getStr("settle_by");
	}

	public void setSettleTime(java.util.Date settleTime) {
		set("settle_time", settleTime);
	}
	
	public java.util.Date getSettleTime() {
		return get("settle_time");
	}

}
