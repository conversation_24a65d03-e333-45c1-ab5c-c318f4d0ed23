package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainCardGiveScheme;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;

import java.util.List;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_card_give_scheme", primaryKey = "id")
public class MainCardGiveScheme extends BaseMainCardGiveScheme<MainCardGiveScheme> {
	
	public List<Record> getRuleList(){
		final String schemeId = getStr("id");
		if(StrKit.notBlank(schemeId)){
			return Db.find("select * from main_card_give_scheme_rule where del_flag=? and scheme_id=? order by start_seq ", DelFlag.NORMAL, schemeId);
		}else{
			return null;
		}
	}
}
