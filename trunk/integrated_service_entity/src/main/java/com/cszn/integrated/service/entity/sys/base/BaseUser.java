package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseUser<M extends BaseUser<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}

	public java.lang.String getId() {
		return getStr("id");
	}

	public void setOrgId(java.lang.String orgId) {
		set("org_id", orgId);
	}

	public java.lang.String getOrgId() {
		return getStr("org_id");
	}

	public void setDeptId(java.lang.String deptId) {
		set("dept_id", deptId);
	}

	public java.lang.String getDeptId() {
		return getStr("dept_id");
	}

	public void setEmployeeId(java.lang.String employeeId) {
		set("employee_id", employeeId);
	}

	public java.lang.String getEmployeeId() {
		return getStr("employee_id");
	}
	
	public void setAgentId(java.lang.String agentId) {
		set("agent_id", agentId);
	}
	
	public java.lang.String getAgentId() {
		return getStr("agent_id");
	}

	public void setUserType(java.lang.String userType) {
		set("user_type", userType);
	}

	public java.lang.String getUserType() {
		return getStr("user_type");
	}

	public void setUserName(java.lang.String userName) {
		set("user_name", userName);
	}

	public java.lang.String getUserName() {
		return getStr("user_name");
	}

	public void setPassword(java.lang.String password) {
		set("password", password);
	}

	public java.lang.String getPassword() {
		return getStr("password");
	}

	public void setSalt(java.lang.String salt) {
		set("salt", salt);
	}

	public java.lang.String getSalt() {
		return getStr("salt");
	}

	public void setName(java.lang.String name) {
		set("name", name);
	}

	public java.lang.String getName() {
		return getStr("name");
	}

	public void setSex(java.lang.String sex) {
		set("sex", sex);
	}

	public java.lang.String getSex() {
		return getStr("sex");
	}

	public void setPhoneNumber(java.lang.String phoneNumber) {
		set("phone_number", phoneNumber);
	}

	public java.lang.String getPhoneNumber() {
		return getStr("phone_number");
	}

	public void setLoginFlag(java.lang.String loginFlag) {
		set("login_flag", loginFlag);
	}

	public java.lang.String getLoginFlag() {
		return getStr("login_flag");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}

	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}

	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}

	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}

	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}

	public java.util.Date getUpdateDate() {
		return get("update_date");
	}


}
