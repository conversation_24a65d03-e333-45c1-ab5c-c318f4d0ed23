package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaLotteryRecharge<M extends BaseFinaLotteryRecharge<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setLotteryId(String lotteryId) {
		set("lottery_id", lotteryId);
	}
	
	public String getLotteryId() {
		return getStr("lottery_id");
	}

	public void setEmpId(String empId) {
		set("emp_id", empId);
	}
	
	public String getEmpId() {
		return getStr("emp_id");
	}
	
	public void setEmpName(String empName) {
		set("emp_name", empName);
	}
	
	public String getEmpName() {
		return getStr("emp_name");
	}
	
	public void setWorkNum(String workNum) {
		set("work_num", workNum);
	}
	
	public String getWorkNum() {
		return getStr("work_num");
	}
	
	public void setMemberId(String memberId) {
		set("member_id", memberId);
	}
	
	public String getMemberId() {
		return getStr("member_id");
	}
	
	public void setMemberName(String memberName) {
		set("member_name", memberName);
	}
	
	public String getMemberName() {
		return getStr("member_name");
	}
	
	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}
	
	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setRechargeAmount(Double rechargeAmount) {
		set("recharge_amount", rechargeAmount);
	}
	
	public Double getRechargeAmount() {
		return getDouble("recharge_amount");
	}
	
	public void setRechargeTimes(Double rechargeTimes) {
		set("recharge_times", rechargeTimes);
	}
	
	public Double getRechargeTimes() {
		return getDouble("recharge_times");
	}
	
	public void setRechargeIntegrals(Double rechargeIntegrals) {
		set("recharge_integrals", rechargeIntegrals);
	}
	
	public Double getRechargeIntegrals() {
		return getDouble("recharge_integrals");
	}
	
	public void setSendPhone(String sendPhone) {
		set("send_phone", sendPhone);
	}
	
	public String getSendPhone() {
		return getStr("send_phone");
	}

	public void setIsSendSuccess(String isSendSuccess) {
		set("is_send_success", isSendSuccess);
	}
	
	public String getIsSendSuccess() {
		return getStr("is_send_success");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
