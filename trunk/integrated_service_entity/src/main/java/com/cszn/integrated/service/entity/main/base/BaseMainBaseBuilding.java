package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseBuilding<M extends BaseMainBaseBuilding<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setBuildingName(String buildingName) {
		set("building_name", buildingName);
	}
	
	public String getBuildingName() {
		return getStr("building_name");
	}

	public void setDisplayName(String displayName) {
		set("display_name", displayName);
	}
	
	public String getDisplayName() {
		return getStr("display_name");
	}

	public void setIntroduction(String introduction) {
		set("introduction", introduction);
	}
	
	public String getIntroduction() {
		return getStr("introduction");
	}

	public void setLowestFloor(Integer lowestFloor) {
		set("lowest_floor", lowestFloor);
	}
	
	public Integer getLowestFloor() {
		return getInt("lowest_floor");
	}

	public void setHighestFloor(Integer highestFloor) {
		set("highest_floor", highestFloor);
	}
	
	public Integer getHighestFloor() {
		return getInt("highest_floor");
	}

	public void setTotalRooms(Integer totalRooms) {
		set("total_rooms", totalRooms);
	}
	
	public Integer getTotalRooms() {
		return getInt("total_rooms");
	}

	public void setTotalBeds(Integer totalBeds) {
		set("total_beds", totalBeds);
	}
	
	public Integer getTotalBeds() {
		return getInt("total_beds");
	}

	public void setHoldBeds(Integer holdBeds) {
		set("hold_beds", holdBeds);
	}
	
	public Integer getHoldBeds() {
		return getInt("hold_beds");
	}

	public void setFileId(String fileId) {
		set("file_id", fileId);
	}
	
	public String getFileId() {
		return getStr("file_id");
	}

	public void setBuildingImage(String buildingImage) {
		set("building_image", buildingImage);
	}
	
	public String getBuildingImage() {
		return getStr("building_image");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setLockService(String lockService) {
		set("lock_service", lockService);
	}

	public String getLockService() {
		return getStr("lock_service");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setSort(Integer sort) {
		set("sort", sort);
	}

	public Integer getSort() {
		return get("sort");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

	public void setIsOutBookabled(String isOutBookabled) {
		set("is_out_bookabled", isOutBookabled);
	}

	public String getIsOutBookabled() {
		return getStr("is_out_bookabled");
	}

}
