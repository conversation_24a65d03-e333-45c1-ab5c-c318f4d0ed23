package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseRoom<M extends BaseMainBaseRoom<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBuildingId(String buildingId) {
		set("building_id", buildingId);
	}
	
	public String getBuildingId() {
		return getStr("building_id");
	}

	public void setFloorId(String floorId) {
		set("floor_id", floorId);
	}
	
	public String getFloorId() {
		return getStr("floor_id");
	}

	public void setRoomNo(String roomNo) {
		set("room_no", roomNo);
	}
	
	public String getRoomNo() {
		return getStr("room_no");
	}

	public void setMarkupTypeId(String markupTypeId) {
		set("markup_type_id", markupTypeId);
	}

	public String getMarkupTypeId() {
		return getStr("markup_type_id");
	}

	public void setMaintainState(String maintainState) {
		set("maintain_state", maintainState);
	}

	public String getMaintainState() {
		return getStr("maintain_state");
	}

	public void setRoomName(String roomName) {
		set("room_name", roomName);
	}
	
	public String getRoomName() {
		return getStr("room_name");
	}

	public void setRoomType(String roomType) {
		set("room_type", roomType);
	}
	
	public String getRoomType() {
		return getStr("room_type");
	}

	public void setIsFaceLock(String isFaceLock) {
		set("is_face_lock", isFaceLock);
	}

	public String getIsFaceLock() {
		return getStr("is_face_lock");
	}

	public void setBaseRoomType(String baseRoomType) {
		set("base_room_type", baseRoomType);
	}

	public String getBaseRoomType() {
		return getStr("base_room_type");
	}

	public void setPeoples(Integer peoples) {
		set("peoples", peoples);
	}
	
	public Integer getPeoples() {
		return getInt("peoples");
	}

	public void setTotalBeds(Integer totalBeds) {
		set("total_beds", totalBeds);
	}
	
	public Integer getTotalBeds() {
		return getInt("total_beds");
	}

	public void setFileId(String fileId) {
		set("file_id", fileId);
	}
	
	public String getFileId() {
		return getStr("file_id");
	}

	public void setRoomImage(String roomImage) {
		set("room_image", roomImage);
	}
	
	public String getRoomImage() {
		return getStr("room_image");
	}

	public void setIsBooking(String isBooking) {
		set("is_booking", isBooking);
	}
	
	public String getIsBooking() {
		return getStr("is_booking");
	}

	public void setIsEnable(String isEnable) {
		set("is_enable", isEnable);
	}
	
	public String getIsEnable() {
		return getStr("is_enable");
	}

	public void setLockNo(String lockNo) {
		set("lock_no", lockNo);
	}
	
	public String getLockNo() {
		return getStr("lock_no");
	}

	public void setLockType(Integer lockType) {
		set("lock_type", lockType);
	}
	
	public Integer getLockType() {
		return getInt("lock_type");
	}

	public void setAllowLock(String allowLock) {
		set("allow_lock", allowLock);
	}
	
	public String getAllowLock() {
		return getStr("allow_lock");
	}
	
	public void setUseType(String useType) {
		set("use_type", useType);
	}
	
	public String getUseType() {
		return getStr("use_type");
	}
	
	public void setRoomUsage(String roomUsage) {
		set("room_usage", roomUsage);
	}
	
	public String getRoomUsage() {
		return getStr("room_usage");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setLockService(String lockService) {
		set("lock_service", lockService);
	}

	public String getLockService() {
		return getStr("lock_service");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

	public void setIsOutBookabled(String isOutBookabled) {
		set("is_out_bookabled", isOutBookabled);
	}

	public String getIsOutBookabled() {
		return getStr("is_out_bookabled");
	}
}
