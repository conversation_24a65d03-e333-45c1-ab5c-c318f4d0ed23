package com.cszn.integrated.service.entity.msg.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMsgMessageUser<M extends BaseMsgMessageUser<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setMsgId(String msgId) {
		set("msg_id", msgId);
	}
	
	public String getMsgId() {
		return getStr("msg_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}
	
	public String getUserId() {
		return getStr("user_id");
	}

	public void setReadFlag(String readFlag) {
		set("read_flag", readFlag);
	}
	
	public String getReadFlag() {
		return getStr("read_flag");
	}

	public void setReadTime(java.util.Date readTime) {
		set("read_time", readTime);
	}
	
	public java.util.Date getReadTime() {
		return get("read_time");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setDelTime(java.util.Date delTime) {
		set("del_time", delTime);
	}
	
	public java.util.Date getDelTime() {
		return get("del_time");
	}

}
