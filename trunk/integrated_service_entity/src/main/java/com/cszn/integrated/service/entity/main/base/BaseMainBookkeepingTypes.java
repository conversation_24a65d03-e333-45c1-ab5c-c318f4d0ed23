package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBookkeepingTypes<M extends BaseMainBookkeepingTypes<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBookkeepingTypeCode(String bookkeepingTypeCode) {
		set("bookkeeping_type_code", bookkeepingTypeCode);
	}
	
	public String getBookkeepingTypeCode() {
		return getStr("bookkeeping_type_code");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setBookkeepingTypeOrder(Integer bookkeepingTypeOrder) {
		set("bookkeeping_type_order", bookkeepingTypeOrder);
	}
	
	public Integer getBookkeepingTypeOrder() {
		return getInt("bookkeeping_type_order");
	}

	public void setIsOut(String isOut) {
		set("is_out", isOut);
	}
	
	public String getIsOut() {
		return getStr("is_out");
	}

	public void setIsDepositCount(String isDepositCount) {
		set("is_deposit_count", isDepositCount);
	}
	
	public String getIsDepositCount() {
		return getStr("is_deposit_count");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
