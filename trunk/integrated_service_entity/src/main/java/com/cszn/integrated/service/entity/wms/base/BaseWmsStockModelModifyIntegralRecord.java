package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockModelModifyIntegralRecord<M extends BaseWmsStockModelModifyIntegralRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setModelId(String modelId) {
		set("model_id", modelId);
	}
	
	public String getModelId() {
		return getStr("model_id");
	}

	public void setBeforeValue(Double beforeValue) {
		set("before_value", beforeValue);
	}
	
	public Double getBeforeValue() {
		return getDouble("before_value");
	}

	public void setAfterValue(Double afterValue) {
		set("after_value", afterValue);
	}
	
	public Double getAfterValue() {
		return getDouble("after_value");
	}

	public void setBeforeSalePrice(Double beforeSalePrice) {
		set("before_sale_price", beforeSalePrice);
	}

	public Double getBeforeSalePrice() {
		return getDouble("before_sale_price");
	}

	public void setAfterSalePrice(Double afterSalePrice) {
		set("after_sale_price", afterSalePrice);
	}

	public Double getAfterSalePrice() {
		return getDouble("after_sale_price");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

}
