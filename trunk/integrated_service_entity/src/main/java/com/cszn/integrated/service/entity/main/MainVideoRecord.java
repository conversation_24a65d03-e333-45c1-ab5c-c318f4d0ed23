package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainVideoRecord;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_video_record", primaryKey = "id")
public class MainVideoRecord extends BaseMainVideoRecord<MainVideoRecord> {

    public Record getVideoType(){

        return Db.findById("main_video_type",this.getTypeId());
    }

}
