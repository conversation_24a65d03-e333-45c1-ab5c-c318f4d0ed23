package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseHandCard<M extends BaseMainBaseHandCard<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setBaseId(java.lang.String baseId) {
		set("base_id", baseId);
	}
	
	public java.lang.String getBaseId() {
		return getStr("base_id");
	}
	
	public void setHandCardCategory(java.lang.String handCardCategory) {
		set("hand_card_category", handCardCategory);
	}
	
	public java.lang.String getHandCardCategory() {
		return getStr("hand_card_category");
	}
	
	public void setCardId(java.lang.String cardId) {
		set("card_id", cardId);
	}
	
	public java.lang.String getCardId() {
		return getStr("card_id");
	}

	public void setCardNo(java.lang.String cardNo) {
		set("card_no", cardNo);
	}
	
	public java.lang.String getCardNo() {
		return getStr("card_no");
	}

	public void setIsEnabled(java.lang.String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public java.lang.String getIsEnabled() {
		return getStr("is_enabled");
	}
	
	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
