package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainBaseGoodConfigs<M extends BaseMainBaseGoodConfigs<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setGoodModelId(String goodModelId) {
		set("good_model_id", goodModelId);
	}
	
	public String getGoodModelId() {
		return getStr("good_model_id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setBaseGoodsConfigOrder(Integer baseGoodsConfigOrder) {
		set("base_goods_config_order", baseGoodsConfigOrder);
	}
	
	public Integer getBaseGoodsConfigOrder() {
		return getInt("base_goods_config_order");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setIsBuckleCard(String isBuckleCard) {
		set("is_buckle_card", isBuckleCard);
	}
	
	public String getIsBuckleCard() {
		return getStr("is_buckle_card");
	}

	public void setCardTimes(Double cardTimes) {
		set("card_times", cardTimes);
	}
	
	public Double getCardTimes() {
		return getDouble("card_times");
	}

	public void setIsWriteOff(String isWriteOff) {
		set("is_write_off", isWriteOff);
	}
	
	public String getIsWriteOff() {
		return getStr("is_write_off");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
