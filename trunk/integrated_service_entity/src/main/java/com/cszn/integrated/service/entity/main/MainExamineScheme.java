package com.cszn.integrated.service.entity.main;

import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;
import com.cszn.integrated.service.entity.main.base.BaseMainExamineScheme;

import java.util.List;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_examine_scheme", primaryKey = "id")
public class MainExamineScheme extends BaseMainExamineScheme<MainExamineScheme> {

    public List<Record> getProblemList(){

        return Db.find("select b.* from main_examine_scheme_problem_rel a  left join main_examine_problem b on a.problem_id=b.id " +
                " where a.scheme_id=?  ORDER BY a.sort ",this.getId());
    }

}
