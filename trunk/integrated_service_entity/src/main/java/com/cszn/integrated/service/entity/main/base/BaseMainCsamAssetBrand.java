package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

public class BaseMainCsamAssetBrand<M extends BaseMainCsamAssetBrand<M>> extends JbootModel<M> implements IBean {

    public void setId(String id) {
        set("id", id);
    }

    public String getId() {
        return getStr("id");
    }

    public void setAssetBrandNo(String assetBrandNo) {
        set("asset_brand_no", assetBrandNo);
    }

    public String getAssetBrandNo() {
        return getStr("asset_brand_no");
    }

    public void setAssetBrandName(String assetBrandName) {
        set("asset_brand_name", assetBrandName);
    }

    public String getAssetBrandName() {
        return getStr("asset_brand_name");
    }

    public void setAssetBrandOrder(Integer assetBrandOrder) {
        set("asset_brand_order", assetBrandOrder);
    }

    public String getAssetBrandOrder() {
        return getStr("asset_brand_order");
    }

    public void setCreateBy(String createBy) {
        set("create_by", createBy);
    }

    public String getCreateBy() {
        return getStr("create_by");
    }

    public void setCreateDate(java.util.Date createDate) {
        set("create_date", createDate);
    }

    public java.util.Date getCreateDate() {
        return get("create_date");
    }

    public void setUpdateBy(String updateBy) {
        set("update_by", updateBy);
    }

    public String getUpdateBy() {
        return getStr("update_by");
    }

    public void setUpdateDate(java.util.Date updateDate) {
        set("update_date", updateDate);
    }

    public java.util.Date getUpdateDate() {
        return get("update_date");
    }

    public void setIsEnabled(String isEnabled) {
        set("is_enabled", isEnabled);
    }

    public String getIsEnabled() {
        return getStr("is_enabled");
    }
}
