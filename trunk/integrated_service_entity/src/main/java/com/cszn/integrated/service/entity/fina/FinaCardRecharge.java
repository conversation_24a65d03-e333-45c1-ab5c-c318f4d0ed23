package com.cszn.integrated.service.entity.fina;

import io.jboot.db.annotation.Table;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.service.entity.fina.base.BaseFinaCardRecharge;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_card_recharge", primaryKey = "id")
public class FinaCardRecharge extends BaseFinaCardRecharge<FinaCardRecharge> {
	
	public String getCardNumber() {
		final String cardId = this.getCardId();
		String cardNumber = "";
		if(StringUtils.isNotBlank(cardId)) {
			Record card = Db.findFirst("select * from fina_membership_card where id=?", cardId);
			if(card!=null) {
				cardNumber = card.getStr("card_number");
			}
		}
		return cardNumber;
	}
	
	public String getCardMemberName() {
		final String cardId = this.getCardId();
		String cardMemberNumber = "";
		if(StringUtils.isNotBlank(cardId)) {
			Record card = Db.findFirst("select * from fina_membership_card where id=?", cardId);
			if(card!=null) {
				final String memberId = card.getStr("member_id");
				if(StringUtils.isNotBlank(memberId)) {
					Record member = Db.findFirst("select * from mms_member where id=?", memberId);
					if(member!=null) {
						cardMemberNumber = member.getStr("full_name");
					}
				}
			}
		}
		return cardMemberNumber;
	}
	
	public String getCardTypeName() {
		final String cardId = this.getCardId();
		String cardTypeName = "";
		if(StringUtils.isNotBlank(cardId)) {
			Record card = Db.findFirst("select * from fina_membership_card where id=?", cardId);
			if(card!=null) {
				final String cardTypeId = card.getStr("card_type_id");
				if(StringUtils.isNotBlank(cardTypeId)) {
					Record cardType = Db.findFirst("select * from main_membership_card_type where id=?", cardTypeId);
					if(cardType!=null) {
						cardTypeName = cardType.getStr("card_type");
					}
				}
			}
		}
		return cardTypeName;
	}
}
