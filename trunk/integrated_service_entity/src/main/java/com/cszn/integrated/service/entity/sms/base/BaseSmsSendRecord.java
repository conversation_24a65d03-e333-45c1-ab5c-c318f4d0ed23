package com.cszn.integrated.service.entity.sms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseSmsSendRecord<M extends BaseSmsSendRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSendType(String sendType) {
		set("send_type", sendType);
	}
	
	public String getSendType() {
		return getStr("send_type");
	}

	public void setPhoneNum(String phoneNum) {
		set("phone_num", phoneNum);
	}
	
	public String getPhoneNum() {
		return getStr("phone_num");
	}

	public void setSmsParam(String smsContent) {
		set("sms_param", smsContent);
	}
	
	public String getSmsParam() {
		return getStr("sms_param");
	}

	public void setSmsTemplateId(String smsTemplateId) {
		set("sms_template_id", smsTemplateId);
	}
	
	public String getSmsTemplateId() {
		return getStr("sms_template_id");
	}

	public void setSmsSendStatus(String smsSendStatus) {
		set("sms_send_status", smsSendStatus);
	}
	
	public String getSmsSendStatus() {
		return getStr("sms_send_status");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
