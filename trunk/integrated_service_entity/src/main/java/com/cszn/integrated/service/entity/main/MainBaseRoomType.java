package com.cszn.integrated.service.entity.main;

import com.cszn.integrated.service.entity.main.base.BaseMainBaseRoomType;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "main_base_room_type", primaryKey = "id")
public class MainBaseRoomType extends BaseMainBaseRoomType<MainBaseRoomType> {

    public Record getBase(){

        return Db.findById("main_base",this.getBaseId());
    }

}
