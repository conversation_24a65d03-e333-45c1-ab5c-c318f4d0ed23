package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainMallUserAddress<M extends BaseMainMallUserAddress<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setUnionid(String unionid) {
		set("unionid", unionid);
	}
	
	public String getUnionid() {
		return getStr("unionid");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setTelephone(String telephone) {
		set("telephone", telephone);
	}
	
	public String getTelephone() {
		return getStr("telephone");
	}

	public void setProvinceId(String provinceId) {
		set("province_id", provinceId);
	}
	
	public String getProvinceId() {
		return getStr("province_id");
	}

	public void setCityId(String cityId) {
		set("city_id", cityId);
	}
	
	public String getCityId() {
		return getStr("city_id");
	}

	public void setAreaId(String areaId) {
		set("area_id", areaId);
	}
	
	public String getAreaId() {
		return getStr("area_id");
	}

	public void setStreetId(String streetId) {
		set("street_id", streetId);
	}
	
	public String getStreetId() {
		return getStr("street_id");
	}

	public void setDetailedAddress(String detailedAddress) {
		set("detailed_address", detailedAddress);
	}
	
	public String getDetailedAddress() {
		return getStr("detailed_address");
	}

	public void setIsDefault(String isDefault) {
		set("is_default", isDefault);
	}
	
	public String getIsDefault() {
		return getStr("is_default");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
