package com.cszn.integrated.service.entity.main.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainCardTypeConfig<M extends BaseMainCardTypeConfig<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setCardTypeId(String cardTypeId) {
		set("card_type_id", cardTypeId);
	}
	
	public String getCardTypeId() {
		return getStr("card_type_id");
	}
	
	public void setConfigName(String configName) {
		set("config_name", configName);
	}
	
	public String getConfigName() {
		return getStr("config_name");
	}
	
	public void setStartValue(Double startValue) {
		set("start_value", startValue);
	}
	
	public Double getStartValue() {
		return getDouble("start_value");
	}
	
	public void setEndValue(Double endValue) {
		set("end_value", endValue);
	}
	
	public Double getEndValue() {
		return getDouble("end_value");
	}
	
	public void setProportionValue(Double proportionValue) {
		set("proportion_value", proportionValue);
	}
	
	public Double getProportionValue() {
		return getDouble("proportion_value");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
