package com.cszn.integrated.service.entity.fina;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.service.entity.fina.base.BaseFinaCardDeduct;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_card_deduct", primaryKey = "id")
public class FinaCardDeduct extends BaseFinaCardDeduct<FinaCardDeduct> {
	
	public String getBaseName() {
		final String baseId = this.getBaseId();
		String baseName = "";
		if(StringUtils.isNotBlank(baseId)) {
			Record base = Db.findFirst("select * from main_base where id=?", baseId);
			if(base!=null) {
				baseName = base.getStr("base_name");
			}
		}
		return baseName;
	}
}
