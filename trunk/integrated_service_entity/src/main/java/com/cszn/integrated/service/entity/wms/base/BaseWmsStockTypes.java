package com.cszn.integrated.service.entity.wms.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseWmsStockTypes<M extends BaseWmsStockTypes<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setStockTypeNo(String stockTypeNo) {
		set("stock_type_no", stockTypeNo);
	}
	
	public String getStockTypeNo() {
		return getStr("stock_type_no");
	}

	public void setParentId(String parentId) {
		set("parent_id", parentId);
	}
	
	public String getParentId() {
		return getStr("parent_id");
	}

	public void setName(String name) {
		set("name", name);
	}
	
	public String getName() {
		return getStr("name");
	}

	public void setStockCodePrefix(String stockCodePrefix) {
		set("stock_code_prefix", stockCodePrefix);
	}
	
	public String getStockCodePrefix() {
		return getStr("stock_code_prefix");
	}

	public void setMaxCode(String maxCode) {
		set("max_code", maxCode);
	}
	
	public String getMaxCode() {
		return getStr("max_code");
	}

	public void setIsOneCode(String isOneCode) {
		set("is_one_code", isOneCode);
	}
	
	public String getIsOneCode() {
		return getStr("is_one_code");
	}

	public void setIsEnabled(String isEnabled) {
		set("is_enabled", isEnabled);
	}
	
	public String getIsEnabled() {
		return getStr("is_enabled");
	}

	public void setIsAdministrativeStaff(String isAdministrativeStaff) {
		set("is_administrative_staff", isAdministrativeStaff);
	}
	
	public String getIsAdministrativeStaff() {
		return getStr("is_administrative_staff");
	}

	public void setIsFinanceStaff(String isFinanceStaff) {
		set("is_finance_staff", isFinanceStaff);
	}
	
	public String getIsFinanceStaff() {
		return getStr("is_finance_staff");
	}

	public void setIsTourismStaff(String isTourismStaff) {
		set("is_tourism_staff", isTourismStaff);
	}
	
	public String getIsTourismStaff() {
		return getStr("is_tourism_staff");
	}

	public void setIsEngineeringStaff(String isEngineeringStaff) {
		set("is_engineering_staff", isEngineeringStaff);
	}
	
	public String getIsEngineeringStaff() {
		return getStr("is_engineering_staff");
	}

	public void setIsItStaff(String isItStaff) {
		set("is_it_staff", isItStaff);
	}
	
	public String getIsItStaff() {
		return getStr("is_it_staff");
	}

	public void setDescription(String description) {
		set("description", description);
	}
	
	public String getDescription() {
		return getStr("description");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
