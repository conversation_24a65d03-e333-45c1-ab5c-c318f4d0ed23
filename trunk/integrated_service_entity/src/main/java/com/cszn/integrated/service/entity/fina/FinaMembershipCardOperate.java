package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaMembershipOperate;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_membership_card_operate", primaryKey = "id")
public class FinaMembershipCardOperate extends BaseFinaMembershipOperate<FinaMembershipCardOperate> {

    	public String getOperateByName(){
		String operateByName = "";
		if(StrKit.notBlank(this.getOperateBy())){
            operateByName = Db.queryStr("select name from sys_user where id=?", this.getOperateBy());
		}
		return operateByName;
	}
}
