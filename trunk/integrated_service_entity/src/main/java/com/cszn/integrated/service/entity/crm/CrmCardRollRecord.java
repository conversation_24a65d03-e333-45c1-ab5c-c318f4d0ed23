package com.cszn.integrated.service.entity.crm;

import com.cszn.integrated.service.entity.crm.base.BaseCrmCardRollRecord;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "crm_card_roll_record", primaryKey = "id")
public class CrmCardRollRecord extends BaseCrmCardRollRecord<CrmCardRollRecord> {


	public int getPrintNum() {
		int printNum = 0;
		Record record = Db.findFirst("select count(id)printNum from crm_card_roll_record_print where del_flag='0' and record_id=?", this.getId());
		if(record!=null) {
			printNum = record.getInt("printNum");
		}
		return printNum;
	}

	public Record getBindingInfo() {
		Record record=Db.findFirst("select * from fina_card_roll where roll_id=? ",this.getId());
		if(record==null){
			return null;
		}
		Record membershipCard=Db.findFirst("select * from fina_membership_card where id=? ",record.getStr("card_id"));
		if(membershipCard==null){
			return null;
		}
		Record member=Db.findFirst("select * from mms_member where id=? ",membershipCard.getStr("member_id"));

		Record returnRecord=new Record();
		returnRecord.set("cardNumber",membershipCard.getStr("card_number"));
		returnRecord.set("cardMember",member.getStr("full_name"));
		if(StrKit.isBlank(record.getStr("create_by"))){
			returnRecord.set("bindingUser","客户");
		}else{
			Record user= Db.findFirst("select * from sys_user where id=? ",record.getStr("create_by"));
			returnRecord.set("bindingUser",user.getStr("name"));
		}
		returnRecord.set("bindingTime",record.getDate("binding_time"));

		return returnRecord;
	}
}
