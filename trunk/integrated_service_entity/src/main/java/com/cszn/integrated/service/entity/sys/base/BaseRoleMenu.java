package com.cszn.integrated.service.entity.sys.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseRoleMenu<M extends BaseRoleMenu<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setRoleId(java.lang.String roleId) {
		set("role_id", roleId);
	}
	
	public java.lang.String getRoleId() {
		return getStr("role_id");
	}

	public void setMenuId(java.lang.String menuId) {
		set("menu_id", menuId);
	}
	
	public java.lang.String getMenuId() {
		return getStr("menu_id");
	}

}
