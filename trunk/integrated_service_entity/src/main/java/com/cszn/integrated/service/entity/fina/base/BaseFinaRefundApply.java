package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaRefundApply<M extends BaseFinaRefundApply<M>> extends JbootModel<M> implements IBean {

	public void setId(java.lang.String id) {
		set("id", id);
	}
	
	public java.lang.String getId() {
		return getStr("id");
	}

	public void setApplyNo(java.lang.String applyNo) {
		set("apply_no", applyNo);
	}
	
	public java.lang.String getApplyNo() {
		return getStr("apply_no");
	}

	public void setDepartmentId(java.lang.String departmentId) {
		set("department_id", departmentId);
	}
	
	public java.lang.String getDepartmentId() {
		return getStr("department_id");
	}

	public void setDepartmentName(java.lang.String departmentName) {
		set("department_name", departmentName);
	}
	
	public java.lang.String getDepartmentName() {
		return getStr("department_name");
	}

	public void setApplyId(java.lang.String applyId) {
		set("apply_id", applyId);
	}
	
	public java.lang.String getApplyId() {
		return getStr("apply_id");
	}

	public void setApplyName(java.lang.String applyName) {
		set("apply_name", applyName);
	}
	
	public java.lang.String getApplyName() {
		return getStr("apply_name");
	}

	public void setApplyTime(java.util.Date applyTime) {
		set("apply_time", applyTime);
	}
	
	public java.util.Date getApplyTime() {
		return get("apply_time");
	}

	public void setRefundSituation(java.lang.String refundSituation) {
		set("refund_situation", refundSituation);
	}
	
	public java.lang.String getRefundSituation() {
		return getStr("refund_situation");
	}

	public void setHandlerType(java.lang.String handlerType) {
		set("handler_type", handlerType);
	}
	
	public java.lang.String getHandlerType() {
		return getStr("handler_type");
	}

	public void setRemarks(java.lang.String remarks) {
		set("remarks", remarks);
	}
	
	public java.lang.String getRemarks() {
		return getStr("remarks");
	}
	
	public void setCurrentNode(java.lang.String currentNode) {
		set("current_node", currentNode);
	}
	
	public java.lang.String getCurrentNode() {
		return getStr("current_node");
	}
	
	public void setCurrentUserName(java.lang.String currentUserName) {
		set("current_user_name", currentUserName);
	}
	
	public java.lang.String getCurrentUserName() {
		return getStr("current_user_name");
	}
	
	public void setCurrentStatus(java.lang.String currentStatus) {
		set("current_status", currentStatus);
	}
	
	public java.lang.String getCurrentStatus() {
		return getStr("current_status");
	}
	
	public void setCurrentStepAlias(java.lang.String currentStepAlias) {
		set("current_step_alias", currentStepAlias);
	}
	
	public java.lang.String getCurrentStepAlias() {
		return getStr("current_step_alias");
	}

	public void setHasSubmit(java.lang.Integer hasSubmit) {
		set("has_submit", hasSubmit);
	}
	
	public java.lang.Integer getHasSubmit() {
		return getInt("has_submit");
	}

	public void setHasFinish(java.lang.Integer hasFinish) {
		set("has_finish", hasFinish);
	}
	
	public java.lang.Integer getHasFinish() {
		return getInt("has_finish");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(java.lang.String createBy) {
		set("create_by", createBy);
	}
	
	public java.lang.String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(java.lang.String updateBy) {
		set("update_by", updateBy);
	}
	
	public java.lang.String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
