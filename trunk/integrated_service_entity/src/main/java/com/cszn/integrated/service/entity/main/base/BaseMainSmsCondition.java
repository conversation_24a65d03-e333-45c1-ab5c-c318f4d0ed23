package com.cszn.integrated.service.entity.main.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseMainSmsCondition<M extends BaseMainSmsCondition<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setSmsApplyId(String smsApplyId) {
		set("sms_apply_id", smsApplyId);
	}
	
	public String getSmsApplyId() {
		return getStr("sms_apply_id");
	}

	public void setSmsConditionCode(String smsConditionCode) {
		set("sms_condition_code", smsConditionCode);
	}
	
	public String getSmsConditionCode() {
		return getStr("sms_condition_code");
	}

	public void setRelationData(String relationData) {
		set("relation_data", relationData);
	}

	public String getRelationData() {
		return getStr("relation_data");
	}
}
