package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;
import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboo<PERSON>, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaMemberSettleMain<M extends BaseFinaMemberSettleMain<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setCheckinNo(String checkinNo) {
		set("checkin_no", checkinNo);
	}
	
	public String getCheckinNo() {
		return getStr("checkin_no");
	}

	public void setMemberId(String memberId) {
		set("member_id", memberId);
	}
	
	public String getMemberId() {
		return getStr("member_id");
	}

	public void setMemberName(String memberName) {
		set("member_name", memberName);
	}

	public String getMemberName() {
		return getStr("member_name");
	}

	public void setName(String name) {
		set("name", name);
	}

	public String getName() {
		return getStr("name");
	}

	public void setYearMonth(String yearMonth) {
		set("year_month", yearMonth);
	}

	public String getYearMonth() {
		return getStr("year_month");
	}

	public void setIdcard(String idcard) {
		set("idcard", idcard);
	}

	public String getIdcard() {
		return getStr("idcard");
	}

	public void setBedNo(String bedNo) {
		set("bed_no", bedNo);
	}
	
	public String getBedNo() {
		return getStr("bed_no");
	}

	public void setSettleDate(java.util.Date settleDate) {
		set("settle_date", settleDate);
	}
	
	public java.util.Date getSettleDate() {
		return get("settle_date");
	}

	public void setSettleStatus(String settleStatus) {
		set("settle_status", settleStatus);
	}
	
	public String getSettleStatus() {
		return getStr("settle_status");
	}

	public void setRemark(String remark) {
		set("remark", remark);
	}
	
	public String getRemark() {
		return getStr("remark");
	}

	public void setCheckinDate(java.util.Date checkinDate) {
		set("checkin_date", checkinDate);
	}
	
	public java.util.Date getCheckinDate() {
		return get("checkin_date");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateDate(java.util.Date createDate) {
		set("create_date", createDate);
	}
	
	public java.util.Date getCreateDate() {
		return get("create_date");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateDate(java.util.Date updateDate) {
		set("update_date", updateDate);
	}
	
	public java.util.Date getUpdateDate() {
		return get("update_date");
	}

}
