package com.cszn.integrated.service.entity.fina.base;

import com.jfinal.plugin.activerecord.IBean;

import io.jboot.db.model.JbootModel;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaCardCollect<M extends BaseFinaCardCollect<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}
	
	public void setCardId(String cardId) {
		set("card_id", cardId);
	}
	
	public String getCardId() {
		return getStr("card_id");
	}
	
	public void setCollectNumber(String collectNumber) {
		set("collect_number", collectNumber);
	}
	
	public String getCollectNumber() {
		return getStr("collect_number");
	}

	public void setSubmitDate(String submitDate) {
		set("submit_date", submitDate);
	}

	public String getSubmitDate() {
		return getStr("submit_date");
	}

	public void setBranchOfficeId(String branchOfficeId) {
		set("branch_office_id", branchOfficeId);
	}

	public String getBranchOfficeId() {
		return getStr("branch_office_id");
	}

	public void setType(String type) {
		set("type", type);
	}

	public String getType() {
		return getStr("type");
	}

	public void setCollectDate(String collectDate) {
		set("collect_date", collectDate);
	}
	
	public String getCollectDate() {
		return getStr("collect_date");
	}

	public void setCollectType(String collectType) {
		set("collect_type", collectType);
	}

	public String getCollectType() {
		return getStr("collect_type");
	}

	public void setSummary(String summary) {
		set("summary", summary);
	}

	public String getSummary() {
		return getStr("summary");
	}

	public void setCollectWay(String collectWay) {
		set("collect_way", collectWay);
	}
	
	public String getCollectWay() {
		return getStr("collect_way");
	}

	public void setCollectAccount(String collectAccount) {
		set("collect_account", collectAccount);
	}
	
	public String getCollectAccount() {
		return getStr("collect_account");
	}

	public void setCollectAmount(Double collectAmount) {
		set("collect_amount", collectAmount);
	}

	public Double getCollectAmount() {
		return getDouble("collect_amount");
	}

	public void setSalesId(String salesId) {
		set("sales_id", salesId);
	}

	public String getSalesId() {
		return getStr("sales_id");
	}

	public void setCustomerName(String customerName) {
		set("customer_name", customerName);
	}

	public String getCustomerName() {
		return getStr("customer_name");
	}
	
	public void setCustomerIdcard(String customerIdcard) {
		set("customer_idcard", customerIdcard);
	}
	
	public String getCustomerIdcard() {
		return getStr("customer_idcard");
	}

	public void setReceiptNumber(String receiptNumber) {
		set("receipt_number", receiptNumber);
	}
	
	public String getReceiptNumber() {
		return getStr("receipt_number");
	}

	public void setReceiptPic(String receiptPic) {
		set("receipt_pic", receiptPic);
	}
	
	public String getReceiptPic() {
		return getStr("receipt_pic");
	}

	public void setFundStatus(String fundStatus) {
		set("fund_status", fundStatus);
	}

	public String getFundStatus() {
		return getStr("fund_status");
	}

	public void setRemarks(String remarks) {
		set("remarks", remarks);
	}

	public String getRemarks() {
		return getStr("remarks");
	}

	public void setDelFlag(java.lang.String delFlag) {
		set("del_flag", delFlag);
	}
	
	public java.lang.String getDelFlag() {
		return getStr("del_flag");
	}
	
	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
