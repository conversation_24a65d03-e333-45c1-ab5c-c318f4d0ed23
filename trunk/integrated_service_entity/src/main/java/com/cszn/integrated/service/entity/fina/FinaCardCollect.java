package com.cszn.integrated.service.entity.fina;

import com.cszn.integrated.service.entity.fina.base.BaseFinaCardCollect;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.db.annotation.Table;

/**
 * Generated by Jboot.
 */
@Table(tableName = "fina_card_collect", primaryKey = "id")
public class FinaCardCollect extends BaseFinaCardCollect<FinaCardCollect> {
	
	public String getBranchOfficeName(){
		return Db.queryStr("select short_name from main_branch_office where id=?", this.getBranchOfficeId());
	}
	
	public String getPayWayName(){
		return Db.queryStr("select name from main_payment_ways where payment_way_code=?", this.getCollectWay());
	}
	
	public String getCollectAccountName(){
		return Db.queryStr("select account_name from fina_card_account where id=?", this.getCollectAccount());
	}
	
	public String getSalesName(){
		return Db.queryStr("select name from sys_user where id=?", this.getSalesId());
	}
}
