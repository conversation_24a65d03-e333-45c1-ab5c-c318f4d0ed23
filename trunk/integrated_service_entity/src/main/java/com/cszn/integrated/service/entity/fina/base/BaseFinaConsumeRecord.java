package com.cszn.integrated.service.entity.fina.base;

import io.jboot.db.model.JbootModel;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by Jboot, do not modify this file.
 */
@SuppressWarnings("serial")
public abstract class BaseFinaConsumeRecord<M extends BaseFinaConsumeRecord<M>> extends JbootModel<M> implements IBean {

	public void setId(String id) {
		set("id", id);
	}
	
	public String getId() {
		return getStr("id");
	}

	public void setConsumeNo(String consumeNo) {
		set("consume_no", consumeNo);
	}
	
	public String getConsumeNo() {
		return getStr("consume_no");
	}

	public void setAppNo(String appNo) {
		set("app_no", appNo);
	}
	
	public String getAppNo() {
		return getStr("app_no");
	}

	public void setAppOrderNo(String appOrderNo) {
		set("app_order_no", appOrderNo);
	}
	
	public String getAppOrderNo() {
		return getStr("app_order_no");
	}

	public void setBaseId(String baseId) {
		set("base_id", baseId);
	}
	
	public String getBaseId() {
		return getStr("base_id");
	}

	public void setUserId(String userId) {
		set("user_id", userId);
	}
	
	public String getUserId() {
		return getStr("user_id");
	}

	public void setUnionid(String unionid) {
		set("unionid", unionid);
	}
	
	public String getUnionid() {
		return getStr("unionid");
	}

	public void setCardNumber(String cardNumber) {
		set("card_number", cardNumber);
	}
	
	public String getCardNumber() {
		return getStr("card_number");
	}

	public void setDescribe(String describe) {
		set("describe", describe);
	}
	
	public String getDescribe() {
		return getStr("describe");
	}

	public void setCheckinType(String checkinType) {
		set("checkin_type", checkinType);
	}
	
	public String getCheckinType() {
		return getStr("checkin_type");
	}

	public void setConsumeType(String consumeType) {
		set("consume_type", consumeType);
	}
	
	public String getConsumeType() {
		return getStr("consume_type");
	}

	public void setCheckinId(String checkinId) {
		set("checkin_id", checkinId);
	}
	
	public String getCheckinId() {
		return getStr("checkin_id");
	}

	public void setRefundFlag(String refundFlag) {
		set("refund_flag", refundFlag);
	}
	
	public String getRefundFlag() {
		return getStr("refund_flag");
	}

	public void setRefundUnionNo(String refundUnionNo) {
		set("refund_union_no", refundUnionNo);
	}
	
	public String getRefundUnionNo() {
		return getStr("refund_union_no");
	}

	public void setAmount(Double amount) {
		set("amount", amount);
	}
	
	public Double getAmount() {
		return getDouble("amount");
	}

	public void setConsumeSource(String consumeSource){
		set("consume_source",consumeSource);
	}

	public String getConsumeSource(){
		return getStr("consume_source");
	}

	public void setConsumeTimes(Double consumeTimes) {
		set("consume_times", consumeTimes);
	}
	
	public Double getConsumeTimes() {
		return getDouble("consume_times");
	}

	public void setPoints(Double points) {
		set("points", points);
	}

	public Double getPoints() {
		return getDouble("points");
	}
	
	public void setIntegrals(Double integrals) {
		set("integrals", integrals);
	}
	
	public Double getIntegrals() {
		return getDouble("integrals");
	}
	
	public void setBeanCoupons(Double beanCoupons) {
		set("bean_coupons", beanCoupons);
	}
	
	public Double getBeanCoupons() {
		return getDouble("bean_coupons");
	}

	public void setTakeTime(java.util.Date takeTime) {
		set("take_time", takeTime);
	}
	
	public java.util.Date getTakeTime() {
		return get("take_time");
	}

	public void setStatus(String status) {
		set("status", status);
	}
	
	public String getStatus() {
		return getStr("status");
	}

	public void setDelFlag(String delFlag) {
		set("del_flag", delFlag);
	}
	
	public String getDelFlag() {
		return getStr("del_flag");
	}

	public void setCreateBy(String createBy) {
		set("create_by", createBy);
	}
	
	public String getCreateBy() {
		return getStr("create_by");
	}

	public void setCreateTime(java.util.Date createTime) {
		set("create_time", createTime);
	}
	
	public java.util.Date getCreateTime() {
		return get("create_time");
	}

	public void setUpdateBy(String updateBy) {
		set("update_by", updateBy);
	}
	
	public String getUpdateBy() {
		return getStr("update_by");
	}

	public void setUpdateTime(java.util.Date updateTime) {
		set("update_time", updateTime);
	}
	
	public java.util.Date getUpdateTime() {
		return get("update_time");
	}

}
