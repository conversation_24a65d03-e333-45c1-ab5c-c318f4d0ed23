package com.cszn.org.web.support.enjoy.directive;

import java.util.List;

import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.template.Env;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.ParseException;
import com.jfinal.template.stat.Scope;

import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

/**
 * 下拉Option指令
 */
@JFinalDirective("dictTpl")
public class DictTplDirective extends JbootDirectiveBase {

	@Inject
    private DictService dictService;

    /** 数据字典类型 */
    private String dictType;
    /** 属性名默认status，laytpl 里 d.attrName */
    private String attrName;

    @Override
    public void exec(Env env, Scope scope, Writer writer) {
        if (exprList.length() > 2) {
            throw new ParseException("Wrong number parameter of #dictTpl directive, 2 parameters allowed at most", location);
        }

        dictType = getPara(0, scope);
        if (StrKit.isBlank(dictType)) {
            throw new ParseException("typeCode is null", location);
        }

        if (exprList.length() > 1) {
            attrName = getPara(1, scope, "type");
        }
        final String[] attrNameArray = attrName.split("\\.");
        List<Dict> list = dictService.getListByTypeOnUse(dictType);
        write(writer, "<div>");
        if(attrNameArray.length>1){
        	write(writer, "{{#  if(d." + attrNameArray[0] + ") { }}");
        	for (Dict dict : list) {
        		write(writer, "{{#  if(d." + attrName + " == \\'" + dict.getDictValue() + "\\') { }}");
        		write(writer, dict.getDictName());
        		write(writer, "{{#  } }}");
        	}
        	write(writer, "{{#  }else{ }}");
        	write(writer, "无");
        	write(writer, "{{#  } }}");
        }else{
        	for (Dict dict : list) {
        		write(writer, "{{#  if(d." + attrName + " == \\'" + dict.getDictValue() + "\\') { }}");
        		write(writer, dict.getDictName());
        		write(writer, "{{#  } }}");
        	}
        }
        write(writer, "</div>");
    }

    @Override
    public void onRender(Env env, Scope scope, Writer writer) {

    }
}
