/**
 * 
 */
package com.cszn.org.web.controller.sys;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.main.MainBranchOfficeUserService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sys.UserRoleService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.main.MainBranchOfficeUser;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.org.web.support.auth.AuthUtils;
import com.cszn.org.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by LiangHuiLing on 2019年4月29日
 *
 * DictController
 */
@RequestMapping(value="/user", viewPath="/modules_page/sys/user")
public class UserController extends BaseController {

	@Inject
	private UserService userService;

	@Inject
	private UserRoleService userRoleService;

	@Inject
	private MainSyncRecordService mainSyncRecordService;

	@Inject
	private MainBranchOfficeService mainBranchOfficeService;

	@Inject
	private MainBranchOfficeUserService mainBranchOfficeUserService;

	@Inject
	private PersOrgService persOrgService;


	public void index(){
		render("userIndex.html");
	}

	/**
	 * 登陆帐号分页表格数据
	 */
	@Clear(LogInterceptor.class)
	public void pageTable() {
		User user = getBean(User.class, "", true);
		Page<User> userPage = userService.paginateByCondition(user, getParaToInt("page", 1), getParaToInt("limit", 10));
		renderJson(new DataTable<User>(userPage));
	}

	/**
	 * 编辑用户页面方法
	 */
	public void edit() {
		String orgId=getPara("orgId");
		String orgName=getPara("orgName");
		User model = userService.findById(getPara("id"));
		if(model != null){
			MainBranchOfficeUser officeUser = mainBranchOfficeUserService.getByUserId(model.getId());
			setAttr("officeUser",officeUser);
		}
		List<MainBranchOffice> officeList = mainBranchOfficeService.getUnDelBranchOffice();
		setAttr("officeList",officeList);
		setAttr("model", model);
		setAttr("orgId",orgId);
		setAttr("orgName",orgName);
		render("userForm.html");
	}

	/**
	 * 表单分配角色权限树数据
	 */
	@Clear(LogInterceptor.class)
	public void userRolesTree() {
		final String userId = getPara("userId");
		List<ZTree> treeNodeList = userRoleService.userRolesTree(userId, SystemType.ORG);
		renderJson(treeNodeList);
	}

	/**
	 * 修改密码页面方法
	 */
	public void modifyPwd() {
		final User model = AuthUtils.getLoginUser();
		setAttr("model", model);
		render("modifyPwd.html");
	}

	/**
	 * 用户修改密码保存方法
	 */
	public void modifyPwdSave() {
		final String inputOldPwd = getPara("inputOldPwd");
		final String oldPwd = AuthUtils.getLoginUser().getPassword();
		final String oldSalt = AuthUtils.getLoginUser().getSalt();
		final User user = getBean(User.class, "", true);
		user.setUserName(AuthUtils.getLoginUser().getUserName());
		Map<String, String> paramMap = new HashMap<>();
		JSONObject dataJson = new JSONObject();
		dataJson.put("inputOldPwd", inputOldPwd);
		dataJson.put("oldPwd", oldPwd);
		dataJson.put("oldSalt", oldSalt);
		dataJson.put("user", user);
		paramMap.put("passData", dataJson.toJSONString());
		final String returnResult = HttpKit.post("http://127.0.0.1:8888/api/modifyPassword", paramMap, null);
		Ret retResult = JSONObject.parseObject(returnResult, Ret.class);
		renderJson(retResult);
	}

	/**
	 * 用户保存方法(包含角色ID字符串)
	 */
	public void saveUser() {
		final String roleIds = getPara("roleIds");
		User user = getBean(User.class, "", true);
		String branchOfficeId = getPara("branchOfficeId");

		//判断名字是否存在
		if(userService.nameIsExist(user.getName(),user.getId())){
			renderJson(Ret.fail("msg", "该名字已存在！"));
			return;
		}
		//判断手机号码是否存在
		if(userService.phoneNumberIsExist(user.getPhoneNumber(),user.getId())){
			renderJson(Ret.fail("msg", "该手机号码已存在！"));
			return;
		}

		//final User oldUser = userService.getByUserName(user.getUserName());
		if( StrKit.isBlank(user.getId()) && StrKit.isBlank(user.getPassword())){
			user.setPassword(Global.defaultPassword);
		}
		//if(StrKit.isBlank(user.getId()) && oldUser!=null){
		/*if(StrKit.isBlank(user.getId())){
			renderJson(Ret.fail("msg", "用户名已存在！"));
		}else{*/
		user.setUpdateBy(AuthUtils.getUserId());
			if (userService.saveUser(user, roleIds,branchOfficeId,SystemType.ORG)) {
				renderJson(Ret.ok("msg", "操作成功!"));
			} else {
				renderJson(Ret.fail("msg", "操作失败！"));
			}
		/*}*/
	}

	public void delUser(){
		String id=getPara("id");
		boolean flag=userService.delUser(id,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}
	/**
	 * 锁定与解锁用户
	 */
	public void save() {
		User user = getBean(User.class, "", true);
		/*if(SyncDataType.UNLOCKED.equals(user.getLoginFlag())){
			//判断是否有该电话号码的账号为未锁定状态
			String sql="select * from sys_user where del_flag='0' and login_flag='un_locked' ";
		}*/
		if(SyncDataType.UNLOCKED.equals(user.getLoginFlag())){
			//判断是否有该电话号码的账号为未锁定状态
			User u=userService.findById(user.getId());
			if(u!=null){
				String sql="select * from sys_user where phone_number=? and del_flag='0' and login_flag='un_locked' and id<>?";
				Record record= Db.findFirst(sql,u.getPhoneNumber(),u.getId());
				if(record!=null){
					renderJson(Ret.fail("msg", "有与该账号电话号码相同的可使用账号！"));
					return;
				}
			}
		}
		if (userService.lockUser(user,AuthUtils.getUserId())) {
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}

	/**
	 * 组织架构tree
	 */
	@Clear(LogInterceptor.class)
	public void employeeOrgTree() {
		String orgId = AuthUtils.getOrgId();
		orgId=null;
		List<ZTree> orgFormTree = persOrgService.orgFormTree(orgId);
		renderJson(orgFormTree);
	}

	/**
	 * 重置密码
	 */
	public void resetPassword(){
		String id=getPara("id");
		if(StrKit.isBlank(id)){
			renderJson(Ret.fail("msg", "操作失败！"));
			return;
		}
		boolean flag = userService.resetPassword(id,Global.defaultPassword,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功！"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}

}
