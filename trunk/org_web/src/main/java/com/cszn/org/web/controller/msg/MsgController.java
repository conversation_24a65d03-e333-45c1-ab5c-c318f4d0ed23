package com.cszn.org.web.controller.msg;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.msg.MsgMessageService;
import com.cszn.integrated.service.entity.msg.MsgMessage;
import com.cszn.integrated.service.entity.msg.MsgMessageUser;
import com.cszn.org.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

@RequestMapping(value="/org/msg", viewPath="/modules_page/org/msg")
public class MsgController extends BaseController {

    @Inject
    private MsgMessageService msgMessageService;



    /**
     * 跳转消息通知界面
     */
    public void index(){
        render("msgIndex.html");
    }


    /**
     * 消息列表
     */
    public void findListPage(){
        String readFlag = getPara("readFlag");
        String userId = AuthUtils.getUserId();
        MsgMessageUser msgUser = new MsgMessageUser();
        msgUser.setUserId(userId);
        msgUser.setReadFlag(readFlag);
        Page<Record> page = msgMessageService.getMsgPage(getParaToInt("page"),getParaToInt("limit"),msgUser);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 跳转消息查看界面
     */
    public void form(){
        MsgMessage msg = getBean(MsgMessage.class,"",true);
        setAttr("msg",msg);
        render("msgForm.html");
    }


    /**
     * 获取用户未读信息
     */
    public void getNotReadMsgCount(){
        Long notReadMsgCount = msgMessageService.getNotReadMsgCount(AuthUtils.getUserId());
        if(notReadMsgCount != null){
            renderJson(Ret.ok().set("notReadMsgCount",notReadMsgCount));
        }else{
            renderJson(Ret.fail());
        }
    }


    /**
     * 标记已读
     */
    public void signRead(){
        String msgData = getPara("data");
        List<MsgMessage> list = JSONArray.parseArray(msgData, MsgMessage.class);
        boolean flag = msgMessageService.signRead(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "标记成功"));
        }else{
            renderJson(Ret.fail("msg", "标记失败"));
        }
    }
}
