/**
 * 
 */
package com.cszn.org.web.controller.sys;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.api.sys.OrgService;
import com.cszn.integrated.service.entity.status.OrgType;
import com.cszn.integrated.service.entity.sys.Org;
import com.cszn.org.web.support.auth.AuthUtils;
import com.cszn.org.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * Created by LiangHuiLing on 2019年4月3日
 *
 * OrgController
 */
@RequestMapping(value="/org", viewPath="/modules_page/sys/org")
public class OrgController extends BaseController {

	@Inject
    private MenuService menuService;
	@Inject
    private OrgService orgService;
	
    public void index() {
        render("orgIndex.html");
    }
    
    public void manageIndex() {
    	render("manageIndex.html");
    }
    
    /**
     * 树表格方法
     */
	@Clear(LogInterceptor.class)
    public void orgTreeGrid() {
    	final String orgId = AuthUtils.getOrgId();
        renderJson(new DataTable<ZTree>(orgService.orgTreeTable(orgId)));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	String viewPath = "dpOrgForm.html";
    	final String formType = getPara("formType");
    	if(formType.equals("main")){
    		viewPath = "mainOrgForm.html";
    	}
    	final String orgId = AuthUtils.getOrgId();
    	Org parentOrg = new Org();
    	if(StrKit.notBlank(orgId)){
    		parentOrg = orgService.findById(orgId);
    	}
    	setAttr("parentOrg", parentOrg);
    	setAttr("model", new Org());
        render(viewPath);
    }
    
    /**
     * 修改页面方法
     */
    public void edit() {
    	String viewPath = "dpOrgForm.html";
    	final String orgId = getPara("id");//获取机构ID
    	Org model = orgService.findById(orgId);
    	if(model!=null && StrKit.notBlank(model.getOrgType())){
    		if(!model.getOrgType().equals(OrgType.mainOrg)){
    			if(StrKit.notBlank(model.getParentIds())){
    	    		final String[] parentIds = model.getParentIds().split(",");
    	    		if(parentIds.length>1){
    	    			setAttr("parentOrg", orgService.findById(parentIds[1]));
    	    		}
    	    	}else{
    	    		setAttr("parentOrg", new Org());
    	    	}
    		}else{
    			viewPath = "mainOrgForm.html";
    		}
    	}
    	setAttr("model", model);
    	render(viewPath);
    }
    
    /**
     * 机构树数据
     */
	@Clear(LogInterceptor.class)
    public void orgFormTree() {
    	final String orgId = AuthUtils.getOrgId();
        List<ZTree> orgFormTree = orgService.orgFormTree(orgId);
        renderJson(orgFormTree);
    }
    
    /**
     * 批量更新排序
     */
    public void sortBatchSave(){
    	final String sortDatas = getPara("sortDatas");
    	if(StrKit.notBlank(sortDatas)){
    		List<Org> orgList = JSONArray.parseArray(sortDatas, Org.class);
    		if (orgService.sortBatchSave(orgList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }
    
	/**
	 * 保存方法
	 */
    public void save() {
        Org org = getBean(Org.class, "", true);
        if (orgService.orgSave(org)) {
        	renderJson(Ret.ok("msg", "操作成功!"));
        } else {
        	renderJson(Ret.fail("msg", "操作失败！"));
        }
    }
    
    /**
     * 删除当前数据及其子数据
     */
    public void del() {
    	if(orgService.orgDel(getPara("id"))){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
}
