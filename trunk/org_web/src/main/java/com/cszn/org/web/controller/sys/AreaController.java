package com.cszn.org.web.controller.sys;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.org.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by LiangHuiLing
 * 
 */
@RequestMapping(value="/area", viewPath="/modules_page/sys/area")
public class AreaController extends BaseController {
    
	@Inject
    private AreaService areaService;
	
	public void index(){
		render("areaIndex.html");
	}

    /**
     * 树表格方法
     */
    public void areaTable() {
    	Area area = getBean(Area.class, "", true);
        Page<Area> areaPage = areaService.paginateByCondition(area, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<Area>(areaPage));
    }
    
    /**
     * 页面联动获取数据方法
     */
    @Clear(LogInterceptor.class)
    public void getAreaByParentId() {
    	final String parentId = getPara("parentId");
    	List<Area> areaList = new ArrayList<Area>();
    	Ret restRsult = Ret.ok("msg", "加载区域成功!");
    	try {
			areaList = areaService.getAreaByParentId(parentId);
		} catch (Exception e) {
			e.printStackTrace();
			restRsult = Ret.fail("msg", "加载区域失败！");
		}
    	restRsult.set("resultData", areaList);
    	renderJson(restRsult);
    }
    
    /**
     * 添加页面方法
     */
    public void add() {
    	final String parentId = getPara("parentId");
    	Area area = new Area();
    	area.setParentId(parentId);
    	setAttr("model", area);
        render("areaForm.html");
    }
    
    /**
     * 修改页面方法
     */
    public void edit() {
    	Area model = areaService.findById(getPara("id"));
    	setAttr("model", model);
    	render("areaForm.html");
    }
    
    /**
     * 上级菜单树数据
     */
	@Clear(LogInterceptor.class)
    public void menuFormTree() {
        List<ZTree> treeNodeList = areaService.areaFormTree();
        renderJson(treeNodeList);
    }
    
    /**
     * 批量更新菜单排序
     */
    public void sortBatchSave(){
    	final String sortDatas = getPara("sortDatas");
    	if(StrKit.notBlank(sortDatas)){
    		List<Area> areaList = JSONArray.parseArray(sortDatas, Area.class);
    		if (areaService.sortBatchSave(areaList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }
    
	/**
	 * 保存方法
	 */
    public void save() {
    	Area area = getBean(Area.class, "", true);
        if (areaService.areaSave(area)) {
        	renderJson(Ret.ok("msg", "操作成功!"));
        } else {
        	renderJson(Ret.fail("msg", "操作失败！"));
        }
    }

    /**
     * 删除当前数据及其子数据
     */
    public void del() {
    	if(areaService.areaDel(getPara("id"))){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
}
