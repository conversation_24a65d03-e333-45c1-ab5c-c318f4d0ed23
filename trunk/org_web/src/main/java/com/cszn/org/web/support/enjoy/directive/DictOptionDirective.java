package com.cszn.org.web.support.enjoy.directive;

import java.util.List;

import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.template.Env;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.ParseException;
import com.jfinal.template.stat.Scope;

import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

/**
 * 字典下拉Option指令
 */
@JFinalDirective("dictOption")
public class DictOptionDirective extends JbootDirectiveBase {

	@Inject
    private DictService dictService;

    private String dictType;
    private String dictValue;
    private String excludeValue;

    @Override
    public void exec(Env env, Scope scope, Writer writer) {
//        LogKit.info("option====="+dictType);
        if (exprList.length() > 3) {
            throw new ParseException("Wrong number parameter of #dictOption directive, 3 parameters allowed at most", location);
        }

        dictType = getPara(0, scope);
        if (StrKit.isBlank(dictType)) {
            throw new ParseException("dictType is null", location);
        }

        if (exprList.length() > 1) {
            dictValue = getPara(1, scope, "");
        }
        
        if (exprList.length() > 2) {
        	excludeValue = getPara(2, scope);
        }

        List<Dict> list = dictService.getListByTypeOnUse(dictType);
        for (Dict dict : list) {
        	if(!dict.getDictValue().equals(excludeValue)){
        		if (dictValue != null && dict.getDictValue().equals(dictValue)) {
        			write(writer, "<option selected value=\"" + dict.getDictValue()  + "\">" + dict.getDictName() + "</option>");
        		} else {
        			write(writer, "<option value=\"" + dict.getDictValue()  + "\">" + dict.getDictName() + "</option>");
        		}
        	}
        }
    }

    @Override
    public void onRender(Env env, Scope scope, Writer writer) {

    }
}
