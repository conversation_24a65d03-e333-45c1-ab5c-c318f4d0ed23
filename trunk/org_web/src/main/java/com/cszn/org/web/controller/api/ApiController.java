package com.cszn.org.web.controller.api;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.internal.StringUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.RetApi;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ObjectSerializeUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.org.web.support.auth.AuthUtils;
import com.cszn.org.web.support.crossorigin.CrossOrigin;
import com.cszn.org.web.support.log.LogInterceptor;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.POST;
import com.jfinal.kit.Base64Kit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.Jboot;
import io.jboot.support.redis.JbootRedis;
import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing
 * 
 */
@RequestMapping(value="/api", viewPath="")
public class ApiController extends BaseController {
    
	@Inject
	private UserService userService;

	private JbootRedis jbootRedis= Jboot.getRedis();

	@CrossOrigin
	public void login() {
		final String userName = getPara("userName");
		final String password = getPara("password");
		Ret resultRet = Ret.create();
		if(StringUtils.isBlank(userName) || StringUtils.isBlank(password)){
			resultRet = Ret.fail("msg", "帐号或密码不能为空!");
			renderJson(resultRet);
			return;
		}
		renderJson(userService.loginByUsername(userName, password));
	}
	
	@CrossOrigin
	public void loginByWx() {
		final String userName = getPara("userName");
		final String password = getPara("password");
		Ret resultRet = Ret.create();
		if(StringUtils.isBlank(userName) || StringUtils.isBlank(password)){
			resultRet = Ret.fail("msg", "帐号或密码不能为空!");
			renderJson(resultRet);
			return;
		}
		renderJson(userService.loginByWx(userName, password));
	}

	@CrossOrigin
	public void loginByUserName() {
		final String userName = getPara("userName");
		Ret resultRet = Ret.create();
		if(StringUtils.isBlank(userName)){
			resultRet = Ret.fail("msg", "帐号不能为空!");
			renderJson(resultRet);
			return;
		}
		User user = userService.getByUserName(userName);
		if(user!=null){
			String userGroupIds = userService.getUserGroupIdByUserId(user.getId());
			if(StrKit.isBlank(userGroupIds)){
				userGroupIds = "none";
			}
			resultRet = Ret.ok("msg", "登录成功")
				.set("id", user.getId())
				.set("userGroupIds", Base64Kit.encode(userGroupIds))
				.set("userName", Base64Kit.encode(user.getUserName()))
				.set("password", Base64Kit.encode(user.getPassword()))
				.set("salt", Base64Kit.encode(user.getSalt()))
				.set("lockFlag", Base64Kit.encode(user.getLoginFlag()));
		}else{
			resultRet = Ret.fail("msg", "帐号不能为空!");
		}
		renderJson(resultRet);
	}

	@CrossOrigin
	public void getUserGroupIdByUserId() {
		final String userId = getPara("userId");
		Ret resultRet = Ret.create();
		if(StringUtils.isBlank(userId)){
			resultRet = Ret.fail("msg", "userId不能为空!");
			renderJson(resultRet);
			return;
		}
		String userGroupIds = userService.getUserGroupIdByUserId(userId);
		if(StrKit.isBlank(userGroupIds)){
			userGroupIds = "none";
		}
		resultRet = Ret.ok("msg", "获取成功").set("userGroupIds", userGroupIds);

		renderJson(resultRet);
	}

	/**
	 * 登陆帐号分页表格数据
	 */
	@CrossOrigin
	@Clear(LogInterceptor.class)
	public void userPage() {
		User user = getBean(User.class, "", true);
		Page<User> userPage = userService.paginateByCondition(user, getParaToInt("page", 1), getParaToInt("limit", 10));
		renderJson(new DataTable<User>(userPage));
	}
	
	/**
	 * 修改密码接口
	 */
	@Before({POST.class})
	@CrossOrigin
	public void modifyPassword() {
		RetApi resultRet = RetApi.create();
		try {
			final String passDataStr = getPara("passData");
			if(StringUtils.isBlank(passDataStr)){ 
				resultRet = RetApi.fail("msg", "参数未传入！");
				renderJson(resultRet);
				return; 
			}
			System.out.println("passData="+passDataStr);
			JSONObject jsonObj = JSONObject.parseObject(passDataStr);
			if(!jsonObj.containsKey("inputOldPwd") || StringUtil.isBlank(jsonObj.getString("inputOldPwd"))) {
				resultRet = RetApi.fail("msg", "旧密码未传入");
				renderJson(resultRet);
				return;
			}
			final String inputOldPwd = jsonObj.getString("inputOldPwd");

			//final String oldPwd = jsonObj.getString("oldPwd");
			//final String oldSalt = jsonObj.getString("oldSalt");
			if(!jsonObj.containsKey("user") || jsonObj.getObject("user",User.class) == null){
				resultRet = RetApi.fail("msg", "用户信息未传入");
				renderJson(resultRet);
				return;
			}
			final User user = jsonObj.getObject("user", User.class);
			if(StringUtils.isBlank(user.getUserName())){ 
				resultRet = RetApi.fail("msg", "用户账号未传入！");
				renderJson(resultRet);
				return; 
			}
			if(StringUtils.isBlank(user.getPassword())){ 
				resultRet = RetApi.fail("msg", "新密码未传入！");
				renderJson(resultRet);
				return; 
			}
			final User userExist = userService.getByUserName(user.getUserName());

			if(userExist == null){
				resultRet = RetApi.fail("msg", "用户不存在！");
				renderJson(resultRet);
				return;
			}
			String oldSalt = userExist.getSalt();//从数据库获取旧盐
			String oldPwd = userExist.getPassword();//从数据库获取旧密码
			user.setId(userExist.getId());
			if(userService.verifyOldPassword(inputOldPwd, oldPwd, oldSalt)){
				if (userService.modifyPwdSave(user)) {
					resultRet = RetApi.ok("msg", "操作成功!");
				} else {
					resultRet = RetApi.fail("msg", "操作失败！");
				}
			}else{
				resultRet = RetApi.fail("msg", "旧密码输入错误！");
			}
		} catch (Exception e) {
			resultRet = RetApi.fail("msg", "操作异常!");
			e.printStackTrace();
		}
		renderJson(resultRet);
	}

	@Before(POST.class)
	@CrossOrigin
	public void h5Login(){
		String loginName=getPara("loginName");
		String password=getPara("password");
		Map<String,Object> resultMap=new HashMap<>();
		resultMap.put("code","10002");
		if(StrKit.isBlank(loginName) || StrKit.isBlank(password)){
			resultMap.put("msg","用户名密码不能为空");
			renderJson(resultMap);
			return;
		}
		User sysUser = userService.getByUserName(loginName);
		if(sysUser==null){
			resultMap.put("msg","用户名或密码错误");
			renderJson(resultMap);
			return;
		}
		boolean flag= AuthUtils.checkPwd(password,sysUser.getPassword(),sysUser.getSalt());
		if(!flag){
			resultMap.put("msg","用户名或密码错误");
			renderJson(resultMap);
			return;
		}
		String token= IdGen.getUUID().toLowerCase();
		String result=jbootRedis.setex("token_"+token,60*60*24, ObjectSerializeUtils.serialize(sysUser));
		if(!"OK".equals(result)){
			setCookie("token",token,-1);
			setCookie("userId",sysUser.getId(),-1);
			resultMap.put("msg","登录异常请稍后重试");
			renderJson(resultMap);
			return;
		}
		Map<String,String> data=new HashMap<>();
		data.put("token",token);
		data.put("userId",sysUser.getId());
		data.put("userName",sysUser.getName());
		data.put("userCode",sysUser.getUserName());
		setCookie("token",token,60*60*24);
		resultMap.put("code","0");
		resultMap.put("msg","success");
		resultMap.put("data",data);
		renderJson(resultMap);
	}


	public static void main(String[] args) throws Exception {

		FileInputStream inputStream = new FileInputStream(
				new File("D:\\soft\\softFile\\weixin\\WeChat Files\\wxid_ubyus9oj0pat22\\FileStorage\\File\\2023-05\\0524.xlsx"));
		XSSFWorkbook wk = new XSSFWorkbook(inputStream);
		XSSFSheet sheet = wk.getSheetAt(0);

		List<Map<String,Object>> data=new ArrayList<>();
		//获取时间范围
		String dateStr=sheet.getRow(3).getCell(0).getStringCellValue();
		String startTime=dateStr.substring(0,19);
		String endTime=dateStr.substring(20);

		List<Date> dateList= DateUtils.getBetweenDates(DateUtils.parseDate(startTime),DateUtils.parseDate(endTime));


		List<String> titles=new ArrayList<>();

		//String[] titles=new String[]{};


		//获取表头数据
		for (int i=0;i<sheet.getRow(4).getLastCellNum();i++){
			String value=sheet.getRow(4).getCell(i).getStringCellValue();
			if("*".equals(value)){
				break;
			}
			if(value==null || "".equals(value)){
				value="类型";
			}
			if(i>3){
				for (Date date : dateList) {
					titles.add(DateUtils.formatDate(date,"yyyy-MM-dd"));
				}
				break;
			}

			titles.add(value);
		}

		System.out.println(JSON.toJSONString(titles));
		String deptName="";
		for (int i = 0; i < sheet.getLastRowNum(); i++) {
			//第7行读取数据
			if(i<6){
				continue;
			}
			XSSFRow row = sheet.getRow(i);
			if(row==null){
				continue;
			}

			if(row.getCell(0).getStringCellValue().indexOf("./")!=-1){
				deptName=row.getCell(0).getStringCellValue();
			}

			Map<String,Object> map=new HashMap<>();

			for (int j = 0; j < row.getLastCellNum(); j++) {
				XSSFCell cell=row.getCell(j);
				if(cell==null){
					continue;
				}
				if(j>titles.size()-1){
					break;
				}
				String value=cell.getStringCellValue();
				map.put(titles.get(j),value);
			}
			map.put("deptName",deptName);
			data.add(map);
		}


		List<Map<String,Object>> data2=new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			Map<String,Object> map=data.get(i);
			String name=(String)map.get("姓名");
			String type=(String)map.get("类型");

			if(StrKit.notBlank(name)){
				Map<String,Object> map2=data.get(i+1);
				data2.add(map);

				//map2.put("姓名",name);
				data2.add(map2);
			}
		}

		JSONArray jsonArray=new JSONArray();

		for (int i = 0; i < data2.size(); i++) {
			Map<String,Object> dataMap=data2.get(i);
			String name=(String)dataMap.get("姓名");
			if(StrKit.isBlank(name)){
				continue;
			}
			JSONObject jsonObject=new JSONObject(true);
			for (String s : dataMap.keySet()) {
				jsonObject.put(s,dataMap.get(s));
			}

			Map<String,Object> nextDataMap=data2.get(i+1);
			if(StrKit.isBlank((String)nextDataMap.get("姓名"))){
				for (String s : nextDataMap.keySet()) {
					if(s.indexOf("-")!=-1){

						if(jsonObject.containsKey(s)){
							jsonObject.put(s,jsonObject.getString(s)+" - "+nextDataMap.get(s));
						}else{
							jsonObject.put(s," - "+nextDataMap.get(s));
						}
					}
				}
			}
			jsonArray.add(jsonObject);
		}

		System.out.println(JSON.toJSONString(jsonArray));


		// 关闭读取的文件
		wk.close();
		inputStream.close();

	}
}
