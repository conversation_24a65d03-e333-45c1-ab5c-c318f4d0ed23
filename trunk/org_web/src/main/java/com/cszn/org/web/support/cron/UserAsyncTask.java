package com.cszn.org.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.integrated.service.entity.main.MainSyncRecord;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.components.schedule.annotation.Cron;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.Date;
import java.util.List;

/**
 * @Description 旅居用户同步定时任务
 * <AUTHOR>
 * @Date 2019/5/31
 **/
@Cron("*/2 * * * *")
public class UserAsyncTask implements Runnable{

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private MainSyncRecordService mainSyncRecordService;

    @Inject
    private MainAppJoinService mainAppJoinService;


    @Override
    public void run() {
        try {
            httpSynRecord();
        }catch (Exception e){
            e.printStackTrace();
        }
    }



    //旅居用户同步
    public void httpSynRecord(){
        List<MainAppJoin> appJoinList=mainAppJoinService.findAppJoinList();
        if(appJoinList!=null && appJoinList.size()>0){
            RequestConfig requestConfig = RequestConfig.custom()
                    // 获取连接超时时间
                    .setConnectionRequestTimeout(5000)
                    // 请求超时时间
                    .setConnectTimeout(5000)
                    // 响应超时时间
                    .setSocketTimeout(30000)
                    .build();
            CloseableHttpClient httpClient= HttpClients.custom().setDefaultRequestConfig(requestConfig).build();

            for (MainAppJoin app:appJoinList){
                logger.info("用户同步数据接收应用编号：[{}]",app.getAppNo());
                int pageNumber=1;
                int pageSize=20;
                Page<MainSyncRecord> syncRecordPage=mainSyncRecordService.findSyncRecorList(pageNumber,pageSize,app.getAppNo(), SyncType.user.getKey());
                if(syncRecordPage==null || syncRecordPage.getTotalRow()== 0){
                    //没有要发生的数据
                    continue;
                }
                logger.info("旅居用户同步入参：[{}]",JSON.toJSONString(syncRecordPage.getList()));
                HttpPost httpPost=new HttpPost(app.getAppUrl());
                StringEntity entity=new StringEntity(JSON.toJSONString(syncRecordPage.getList()), Consts.UTF_8);
                httpPost.setEntity(entity);
                httpPost.setHeader("Content-Type", "application/json;charset=utf8");
                CloseableHttpResponse response=null;
                try {
                    response = httpClient.execute(httpPost);
                    HttpEntity responseEntity = response.getEntity();
                    logger.info("旅居同步用户响应状态："+ response.getStatusLine().getStatusCode());
                    if (response.getStatusLine().getStatusCode() != 200) {
                        //响应状态不为200
                        continue;
                    }
                    String entityStr = EntityUtils.toString(responseEntity);
                    logger.info("旅居同步用户数据响应内容" + entityStr);
                    if (!entityStr.startsWith("{") || !entityStr.endsWith("}")) {
                        //响应内容不是json格式
                        continue;
                    }
                    JSONObject result = JSON.parseObject(entityStr);
                    if (!result.containsKey("Type") || !result.getString("Type").equals("1") || !result.containsKey("Data")) {
                        //响应不是成功状态
                        continue;
                    }
                    //获取响应回来的id
                    JSONArray array = JSON.parseArray(result.getString("Data"));
                    logger.info("旅居响应回来的List数据：[{}]",array);
                    List<MainSyncRecord> contentList = Lists.newArrayList();
                    //设置这些id的数据为同步成功
                    if (array != null && array.size() > 0) {
                        //region Description
                        /*Iterator<MainSyncRecord> iterator=syncRecordList.iterator();
                        while (iterator.hasNext()){
                            MainSyncRecord syncRecord=iterator.next();
                            if(array.contains(syncRecord.getId())){
                                syncRecord.setSyncStatus("0");
                                syncRecord.setUpdateDate(new Date());
                            }else{
                                iterator.remove();
                            }
                        }
                        Db.batchUpdate(syncRecordList,syncRecordList.size());*/
                        //endregion
                        for (Object item : array) {
                            if(item != null) {
                                JSONObject json = JSONObject.parseObject(JSON.toJSONString(item));
                                logger.info("旅居响应回来的json对象数据：[{}]" ,json);
                                if(json.containsKey("id") && json.containsKey("state") && json.containsKey("msg")){
                                    logger.info("返回可查数据：id：[{}]，同步状态：[{}]，同步信息：[{}]",json.getString("id"),json.getString("state"),json.getString("msg"));
                                    if(json.getString("id") != null && json.getString("state") != null && "0".equals(json.getString("state"))){
                                        MainSyncRecord syncRecord = new MainSyncRecord();
                                        syncRecord.setId(json.getString("id"));
                                        syncRecord.setSyncStatus("0");
                                        syncRecord.setUpdateDate(new Date());
                                        contentList.add(syncRecord);
                                    }
                                }
                            }
                        }
                        Db.batchUpdate(contentList,contentList.size());
                    }
                }catch (SocketTimeoutException e){
                    logger.info("旅居同步用户数据响应超时");
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }finally {
                    if(response!=null){
                        try {
                            response.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                }
            }
            if(httpClient!=null){
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
