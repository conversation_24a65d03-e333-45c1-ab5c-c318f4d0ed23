#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#(APP.name)#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
	.layui-nav .layui-badge, .layui-nav .layui-badge-dot {
		position: absolute;
		right: 18px;
		top: 29px;
	}
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/vip_comm.js"></script>
<script type="text/javascript">
layui.use(['layer','vip_nav'], function () {
	// 操作对象
	var layer = layui.layer
		,vipNav = layui.vip_nav
	    ,$ = layui.jquery
	    ;
//	 	顶部左侧菜单生成 [请求地址,过滤ID,是否展开,携带参数]
//	     vipNav.top_left('#(ctxPath)/system/res/menuTop','side-top-left',false);
//	     you code ...

		$('#infoBtn').on('click', function() {
			pop_show('个人资料', '#(ctxPath)/orgEmployee/info', 650, 600);
		});
		$('#modPwdBtn').on('click', function() {
			pop_show('修改密码', '#(ctxPath)/user/modifyPwd', 450, 300);
		});

	//定时器推送未读的消息
	getUnReadMessageCount = function(){
		util.sendAjax({
			url:'#(ctxPath)/org/msg/getNotReadMsgCount',
			type:'post',
			data:JSON.stringify({}),
			notice:false,
			success : function (result) {
				if(result.state == 'ok' && result.notReadMsgCount > 0){
					//$("#unReadMessageCount").html(result.notReadMsgCount);
					$("#unReadMessageCount").addClass("layui-badge-dot");
				}else{
					if($("#unReadMessageCount").hasClass("layui-badge-dot")){
						$("#unReadMessageCount").removeClass("layui-badge-dot");
					}
				}
			}
		});
	}

	$(function(){
		//getUnReadMessageCount();
	});

	//setInterval("getUnReadMessageCount();",10000);
	});
</script>
#end

#define content()
<div class="layui-layout layui-layout-admin">
	<!-- 添加skin-1类可手动修改主题为纯白，添加skin-2类可手动修改主题为蓝白 -->
	<!-- header -->
	<div class="layui-header my-header">
		<a href="#(ctxPath)">
			<div class="my-header-logo">#(APP.name)</div>
		</a>
		<div class="my-header-btn">
			<button id="showHideBtn" class="layui-btn layui-btn-small btn-nav">
				<i class="layui-icon">&#xe65f;</i>
			</button>
		</div>
		
		<!-- 顶部左侧添加选项卡监听 -->
		<ul class="layui-nav" lay-filter="side-top-left"></ul>
		
		<!-- 顶部右侧添加选项卡监听 -->
		<ul class="layui-nav my-header-user-nav" lay-filter="side-top-right">
<!-- 			<li class="layui-nav-item" style="margin-right:10px;"> -->
<!-- 				<a class="name" href="javascript:;"> -->
<!-- 					<i class="layui-icon">&#xe629;</i>主题 -->
<!-- 				</a> -->
<!-- 				<dl class="layui-nav-child"> -->
<!-- 					<dd data-skin="0"> -->
<!-- 						<a href="javascript:;">默认</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="1"> -->
<!-- 						<a href="javascript:;">纯白</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="2"> -->
<!-- 						<a href="javascript:;">蓝白</a> -->
<!-- 					</dd> -->
<!-- 				</dl> -->
<!-- 			</li> -->
			<li class="layui-nav-item" style="margin-right: 15px;margin-top: 5px;">
				<a href="javascript:;" href-url="#(ctxPath)/org/msg/index">
					<i class="layui-icon">&#xe667;</i><span id="unReadMessageCount"></span>
				</a>
			</li>
			<li class="layui-nav-item" style="margin-right:10px;">
				<a class="name" href="javascript:;">
					<img class="layui-circle" src="#(ctxPath)/static/img/user.png" alt="logo"> 
					#shiroPrincipal() 
						#(principal) 
					#end 
				</a>
				<dl class="layui-nav-child">
					<dd>
						<a id="infoBtn" href="javascript:;" href-url="">个人资料</a>
					</dd>
					<dd>
						<a id="modPwdBtn" href="javascript:;" href-url="">修改密码</a>
					</dd>
				</dl>
			</li>
			<li class="layui-nav-item"><a href="#(ctxPath)/logout" class="layui-btn layui-btn-danger layui-btn-small btn-nav" href-url="">退出</a></li>
		</ul>
	</div>
	
	<!-- side -->
	<div class="layui-side my-side">
		<div class="layui-side-scroll">
			#shiroHasPermission("org:systemSet")
			<!-- 左侧主菜单添加选项卡监听 -->
			<ul class="layui-nav layui-nav-tree" lay-filter="side-main">
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>系统设置</a>
					<dl class="layui-nav-child">
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="#(ctxPath)/menu/index"><i class="fa fa-list-ul"></i>菜单权限</a> -->
<!-- 						</dd> -->
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/user/index"><i class="fa fa-list-ul"></i>用户管理</a>
						</dd>
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="#(ctxPath)/area/index"><i class="fa fa-map-o"></i>行政区域</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="#(ctxPath)/dict/index"><i class="fa fa-tasks"></i>系统字典</a> -->
<!-- 						</dd> -->
					</dl>
				</li>
			</ul>
			#end
		</div>
	</div>
	
	<!-- body -->
	<div class="layui-body my-body">
		<div class="layui-tab layui-tab-card my-tab" lay-filter="card" lay-allowClose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" lay-id="1"><span><i class="layui-icon">&#xe68e;</i>首页</span></li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<iframe id="iframe" src="#(ctxPath)/welcome" frameborder="0"></iframe>
				</div>
			</div>
		</div>
	</div>
	
	<!-- footer -->
	<div class="layui-footer my-footer" style="height: 30px;">
		<p style="line-height: 30px;">
			<!--<a href="#(APP.orgWebsite)" target="_blank">#(APP.org)</a>
			&nbsp;&nbsp;&&nbsp;&nbsp;
			<a href="javascript:;" target="_blank">#(APP.name)</a>-->
			&nbsp;&nbsp;&nbsp;Copyright #(thisYear??) #(APP.copyRight)
		</p>
		<!--		<p></p>-->
	</div>
</div>

<!-- 右键菜单 -->
<div class="my-dblclick-box none">
	<table class="layui-tab dblclick-tab">
		<tr class="card-refresh">
			<td><i class="layui-icon">&#x1002;</i>刷新当前标签</td>
		</tr>
		<tr class="card-close">
			<td><i class="layui-icon">&#x1006;</i>关闭当前标签</td>
		</tr>
		<tr class="card-close-all">
			<td><i class="layui-icon">&#x1006;</i>关闭所有标签</td>
		</tr>
	</table>
</div>
#end