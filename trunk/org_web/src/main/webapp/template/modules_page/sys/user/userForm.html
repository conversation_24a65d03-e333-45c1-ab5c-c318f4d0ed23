#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()登陆帐号编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer,
			$=layui.$
	;

	//校验
	form.verify({

		pwdLength: function(value){
			if(value.length > 0 && value.length < 5){
				return '密码长度至少为6位';
			}
		},
		checkPhone:function(value){
			if(value != null && value.length !=11){
				return "手机号为11位数字";
			}
		}
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		// var checkedNodes = zTreeObj.getCheckedNodes(); //获取 zTree 当前被勾选中的节点数据集合，返回的类型:Array(JSON)
		// var roleIdsArray = new Array(checkedNodes.length);//定义角色ID数组
		// for (var i = 0; i < checkedNodes.length; i++) {
		// 	roleIdsArray[i] = checkedNodes[i].id;
        // }
		// $("#roleIds").val(roleIdsArray.toString());
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/user/saveUser',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div class="layui-col-xs12 layui-col-sm12 layui-col-md12" style="text-align:center;">
		<div style="margin-bottom:23px;"></div>
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>登陆帐号</label>
					<div class="layui-input-inline">
						<input type="text" name="userName" class="layui-input" lay-verify="required" value="#(model.userName??'')" #if(model.userName??)disabled="disabled"#end placeholder="请输入用户名">
					</div>
				</div>
			</div>
			<!--<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>登陆密码</label>
					<div class="layui-input-inline">
						<input type="password" name="password" class="layui-input" lay-verify="pwdLength" value="#(model.password??'')" #if(model.password??)disabled="disabled"#end placeholder="请输入密码">
					</div>
				</div>
			</div>-->
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>账户类型</label>
					<div class="layui-input-inline">
						<select name="userType" lay-verify="required">
							<option value="">请选择账户类型</option>
							#getDictList("user_type")
							<option value="#(key)" #(model != null ?(key == model.userType ? 'selected':''):'')>#(value)</option>
							#end
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>姓名</label>
					<div class="layui-input-inline">
						<input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??'')" placeholder="请输入姓名">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>性别</label>
					<div class="layui-input-inline">
						<select name="sex" lay-verify="required">
							<option value="">请选择性别</option>
							#getDictList("gender")
							<option value="#(key)" #(model != null ?(key == model.sex ? 'selected':''):'')>#(value)</option>
							#end
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">分公司</label>
					<div class="layui-input-inline">
						<select name="branchOfficeId" id="branchOfficeId" lay-search>
							<option value="">请选择所属分公司</option>
							#for(o : officeList)
							<option value="#(o.id)" #(model != null ? (o.id == officeUser.branchOfficeId?? ?'selected':'') :'')>#(o.fullName)</option>
							#end
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>手机号码</label>
					<div class="layui-input-inline">
						<input type="text" name="phoneNumber" class="layui-input" lay-verify="required|number|checkPhone" value="#(model.phoneNumber??'')" placeholder="请输入手机号码">
					</div>
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??'')">
					<input type="hidden" id="roleIds" name="roleIds" value="">
					<input type="hidden" name="orgId" value="#(model.orgId??'')">
					<input type="hidden" name="employeeId" value="#(model.employeeId??'')">
					<input type="hidden" name="userType" value="#(model.userType??'ordinary_user')">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end