#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()帐号管理首页#end

#define css()
#end

#define js()
<script type="text/html" id="userTableBar">
    #[[
    {{# if((d.userType!='org_manager')){ }}
    {{# if((d.loginFlag=='un_locked')){ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="locked">锁定</a>
    {{# }else{ }}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="unlock">解锁</a>
    {{# } }}
    {{# } }}
    ]]#
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-xs" lay-event="resetPassword">重置密码</a>
	#[[
	{{# if((d.userType!='org_manager')){ }}
	<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
	{{# } }}
	]]#
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','vip_table'],function(){
		// 操作对象
		var layer = layui.layer
			,table = layui.table
			,vipTable = layui.vip_table
			,$ = layui.jquery
			,tableId = 'userTable'
		;

        // 表格渲染
        var tableObj = table.render({
            id: tableId
            , elem: '#'+tableId
            , even: true
            , url: '#(ctxPath)/user/pageTable'
            , method: 'post'
            , height: vipTable.getFullHeight()
            , where: {userName:$('#userName').val()}
            , cols: [[
                {field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                ,{field:'userType', title:'帐号类型', width:100, align:'center', templet:'#dictTpl("user_type", "userType")'}
                ,{field:'userName', title:'登陆帐号', align:'center'}
                ,{field:'loginFlag', title:'登陆状态', align:'center', templet:'#dictTpl("login_flag", "loginFlag")'}
				,{field:'name', title:'姓名', align:'center'}
				,{field:'sex', title:'性别', align:'center', templet:'#dictTpl("gender", "sex")'}
				,{field:'phoneNumber', title:'手机号码', align:'center'}
                ,{fixed:'right', title:'操作', width:250, align:'center', toolbar:'#userTableBar'}
            ]]
            , page: true
            , loading: true
            , done: function (res, curr, count) {
            }
        });
        // 表格绑定事件
        table.on('tool('+tableId+')',function (obj) {
            if (obj.event === 'locked') {
                layer.confirm('您确定要锁定该帐号?', function(index) {
                    //锁定操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/user/save',
                        data: {id:obj.data.id, loginFlag:'locked'},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pageTableReload();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }else if (obj.event === 'unlock') {
                layer.confirm('您确定要解锁该帐号?', function(index) {
                    //解锁操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/user/save',
                        data: {id:obj.data.id, loginFlag:'un_locked'},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pageTableReload();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }else if (obj.event === 'edit') {
				pop_show('编辑', '#(ctxPath)/user/edit?id=' + obj.data.id, '500', '500');

			}else if(obj.event==='resetPassword'){
				layer.confirm('您确定要重置该帐号的密码吗?', function(index) {
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/user/resetPassword',
						data: {id:obj.data.id},
						notice: true,
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								pageTableReload();
							}
						},
						complete : function() {
						}
					});
					layer.close(index);
				});
            }else if (obj.event === 'del') {
				layer.confirm('您确定要作废当前登陆帐号?', function(index) {
					//作废操作
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/user/delUser',
						data: {id:obj.data.id},
						notice: true,
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								pageTableReload();
							}
						},
						complete : function() {
						}
					});
					layer.close(index);
				});
			}
        });
        //重载表格
        pageTableReload = function () {
            tableReload(tableId,{userName:$('#userName').val(),name:$("#name").val(),orgId:$("#orgId").val()});
        }
        //搜索按钮点击事件
        $('#searchBtn').on('click', function() {
            pageTableReload();
        });
        // 刷新
        $('#refreshBtn').on('click', function () {
            pageTableReload();
        });
		//添加按钮点击事件
		$('#btn-add').on('click', function() {
			pop_show('添加','#(ctxPath)/user/edit','500','500');
		});
    })
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
			<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <label class="layui-form-label">登陆帐号：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="userName" name="userName" class="layui-input" placeholder="请输入登陆帐号" autocomplete="off">
			        </div>
	    		</div>
				<div class="layui-inline">
			        <label class="layui-form-label">用户姓名：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="name" name="name" class="layui-input" placeholder="请输入用户姓名" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
							<a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
				<span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
			</div>
			<div class="layui-row">
				<table id="userTable" lay-filter="userTable"></table>
			</div>
		</div>
	</div>
</div>
#end