#---------------------------------------------------------------------------------#
# app info
jboot.admin.app.name=统一认证管理
jboot.admin.app.org=cszn
jboot.admin.app.orgWebsite=www.cszn.com
jboot.admin.app.resourceHost
jboot.admin.app.copyRight=广州市昌松智能科技开发有限公司 All rights reserved
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot的开发模式
jboot.mode=dev
jboot.bannerEnable=true
jboot.bannerFile=banner.txt
jboot.cron4jEnable=false
jboot.cron4jFile=cron4j.properties
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#type = local (support:local,motan,dubbo)
#use local
jboot.rpc.type = local
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# mysql config
jboot.datasource.type=mysql
jboot.datasource.url=****************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
jboot.datasource.maximumPoolSize = 5
jboot.datasource.sqlTemplatePath=
jboot.datasource.sqlTemplate=
jboot.datasource.table=
jboot.datasource.excludeTable=
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
jboot.model.idCacheEnable=false
#---------------------------------------------------------------------------------#

jboot.redis.host=127.0.0.1
jboot.redis.password=
jboot.redis.port = 6379


#---------------------------------------------------------------------------------#
# cache config : type default ehcache (support:ehcache,redis,ehredis)
#jboot.cache.type=redis
#jboot.cache.redis.host=127.0.0.1
#jboot.cache.redis.password=123456
#jboot.cache.redis.database=0
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# jwt config
jboot.web.jwt.httpHeaderName=jwttoken
jboot.web.jwt.secret=21934e3ccf3c19a6c3299cbc2e33b2d848e6ef9ea4e484b19b07bea82888a7a7
# 60 * 60 * 10
jboot.web.jwt.validityPeriod=36000
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# shiro config
jboot.shiro.ini = shiro.ini
jboot.shiro.loginUrl=/login
jboot.shiro.successUrl
jboot.shiro.unauthorizedUrl=/login
#----------------------------------------------------------------------------------#
userAsyncUrl=https://bmp.csitd.com/Member/CheckUser
#默认密码
defaultPassword=password
#---------------企业微信---------------
corpid=wwec07ceed335b98c4
corpsecret=Vb83Q8kpYX054RzaQjSq2L0ST8np7Y7OOY1udTXekOM
#企业微信同步开关0开启1关闭
qiyeOn=1

#机构养老系统基地id
orgBaseId=27a558b2-c205-4937-b6f4-fed1fa68ac55
#机构养老系统应用号
orgAppNo=9100002