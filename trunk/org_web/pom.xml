<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.cszn</groupId>
		<artifactId>integrated</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>org_web</artifactId>
	<name>org_web</name>
	<url>http://maven.apache.org</url>
	
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<hutool.version>3.1.0</hutool.version>
		<log4j.version>1.2.17</log4j.version>
	</properties>
	
	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>3.8.1</version>
			<scope>test</scope>
		</dependency>
		<!--常用工具类 -->
		<dependency>
			<groupId>com.xiaoleilu</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>
		<dependency>
			<groupId>com.cszn</groupId>
			<artifactId>integrated_service_provider</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>${log4j.version}</version>
        </dependency>
	</dependencies>
	
	<build>
		<plugins>
            <!--
			    jar 包中的配置文件优先级高于 config 目录下的 "同名文件"
			    因此，打包时需要排除掉 jar 包中来自 src/main/resources 目录的
			    配置文件，否则部署时 config 目录中的同名配置文件不会生效
			-->
			<plugin>
			    <groupId>org.apache.maven.plugins</groupId>
			    <artifactId>maven-jar-plugin</artifactId>
			    <version>2.6</version>
			    <configuration>
			        <excludes>
			            <exclude>*.txt</exclude>
			            <exclude>*.xml</exclude>
			            <exclude>*.properties</exclude>
			        </excludes>
			    </configuration>
			</plugin>

            <plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>3.1.0</version>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
			
						<configuration>
							<!-- 打包生成的文件名 -->
							<finalName>${project.artifactId}</finalName>
							<!-- jar 等压缩文件在被打包进入 zip、tar.gz 时是否压缩，设置为 false 可加快打包速度 -->
							<recompressZippedFiles>false</recompressZippedFiles>
							<!-- 打包生成的文件是否要追加 release.xml 中定义的 id 值 -->
							<appendAssemblyId>true</appendAssemblyId>
							<!-- 指向打包描述文件 package.xml -->
							<descriptors>
								<descriptor>package.xml</descriptor>
							</descriptors>
							<!-- 打包结果输出的基础目录 -->
							<outputDirectory>${project.build.directory}/</outputDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>
        </plugins>
	</build>
</project>
