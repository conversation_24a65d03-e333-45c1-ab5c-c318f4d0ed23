package com.cszn.upload.web.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.FileUploadKit;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.cfs.CfsFileUploadService;
import com.cszn.integrated.service.api.cfs.CfsUploadFileService;
import com.cszn.integrated.service.api.fina.FinaCardTransactionsService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeProvidentFundService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeSocialSecurityService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.cfs.CfsUploadFile;
import com.cszn.integrated.service.entity.enums.PayRatioType;
import com.cszn.integrated.service.entity.fina.FinaCardMonthBalanceNew;
import com.cszn.integrated.service.entity.fina.FinaCardTransactions;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeProvidentFund;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeSocialSecurity;
import com.cszn.integrated.service.entity.pers.PersPosition;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.*;

@RequestMapping(value="/api")
@JCors
public class ApiController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(ApiController.class);

    @Inject
    private CfsFileUploadService cfsFileUploadService;
	@Inject
	private CfsUploadFileService cfsUploadFileService;

	//@EnableCORS
	public void upload() {
    	HttpServletResponse response = getResponse();
    	response.setHeader("Access-Control-Allow-Origin","*");
        UploadFile file = getFile("file");
        String returnType=getPara("returnType");
        if(file == null){ renderJson(Ret.fail("msg", "文件未上传")); return; }
        String folder = getPara("bucket");// 上传的空间
        String id = IdGen.getUUID();
        Map<String, Object> data = FileUploadKit.uploadFile(file, folder);
        logger.info("上传文件数据结果：[{}]", JSON.toJSONString(data));
        if(data == null || data.isEmpty()){ renderJson(Ret.fail("msg", "上传失败")); return; }

        CfsUploadFile uploadFile = new CfsUploadFile();
        uploadFile.setId(id);
        uploadFile.setBucket(folder);
		uploadFile.setOldName(file.getFileName());
        uploadFile.setName(data.get("title").toString());
        uploadFile.setPath(data.get("path").toString());
        uploadFile.setSrc(data.get("src").toString());
        uploadFile.setCreateTime(new Date());
        uploadFile.save();
        data.put("id", id);
		data.put("oldName",uploadFile.getOldName());
        if(StrKit.isBlank(returnType)){
			renderJson(Ret.ok("msg", "上传成功!").set("data",data));
		}else{
			renderCodeSuccess("success",data.get("src").toString());
		}


	}
    
	//@EnableCORS
    public void fileUpload() {
    	String returnMsg = "上传失败!";
    	HttpServletResponse response = getResponse();
    	response.setHeader("Access-Control-Allow-Origin","*");
    	
    	UploadFile file = getFile("file");
    	if(file == null){ renderJson(Ret.fail("msg", "文件未上传")); return; }
    	
    	String fileFolder = getPara("fileFolder");// 上传文件目录
    	if(StrKit.isBlank(fileFolder)){ renderJson(Ret.fail("msg", "上传文件目录为空")); return; }
    	
    	Map<String, Object> returnData = FileUploadKit.fileSave(file, fileFolder);
    	logger.info("上传文件数据结果：[{}]", JSON.toJSONString(returnData));
    	if(returnData == null || returnData.isEmpty()){ renderJson(Ret.fail("msg", "上传失败")); return; }
    	
    	final String fileName = returnData.get("fileName").toString();
    	final String fileSize = returnData.get("fileSize").toString();
    	final String filePath = returnData.get("filePath").toString();
    	final String fileUrl = returnData.get("fileUrl").toString();
    	
    	CfsFileUpload model = getBean(CfsFileUpload.class,"",true);
    	String fileId = IdGen.getUUID();
    	model.setId(fileId);
    	model.setFileFolder(fileFolder);
    	model.setFileName(fileName);
    	model.setFileSize(fileSize);
    	model.setFileSuffix(fileName.substring(fileName.lastIndexOf(".")));
    	model.setFilePath(filePath);
    	model.setFileUrl(fileUrl);
    	model.setDelFlag(DelFlag.NORMAL);
    	model.setCreateTime(new Date());
    	if(model.save()) {
    		returnData.put("fileId", fileId);
    		returnData.put("fileName", model.getFileName());
    		returnData.put("fileUrl", model.getFileUrl());
    		returnData.put("fileCreateTime", DateUtils.formatDateTime(model.getCreateTime()));
    		returnMsg = "上传成功!";
    	}else {
    		model = null;
    	}
    	renderJson(Ret.ok("msg", returnMsg).set("returnData",returnData));
    }
    
    //@EnableCORS
    public void fileList() {
    	HttpServletResponse response = getResponse();
    	response.setHeader("Access-Control-Allow-Origin","*");
    	
    	String relationId = getPara("relationId");// 关联id
    	if(StrKit.isBlank(relationId)){ renderJson(Ret.fail("msg", "关联id参数为空")); return; }
    	
    	List<CfsFileUpload> fileList = cfsFileUploadService.findListByRelationId(relationId);
    	renderJson(Ret.ok("msg", "返回成功!").set("fileList",fileList));
    }
    
    //@EnableCORS
    public void fileRelationIdUpdate() {
    	HttpServletResponse response = getResponse();
    	response.setHeader("Access-Control-Allow-Origin","*");
    	
    	final String jsonStr = getPara("jsonStr");// json参数
    	if(StringUtils.isBlank(jsonStr) || !jsonStr.startsWith("[") || !jsonStr.endsWith("]")){
			renderJson(Ret.fail("msg", "参数格式不正确!"));
		}else {
			boolean updateFlag = false;
			List<CfsFileUpload> fileList = JSON.parseArray(jsonStr, CfsFileUpload.class);
			if(fileList!=null&&fileList.size()>0) {
				for(CfsFileUpload file : fileList) {
					if(StrKit.notBlank(file.getId())) {
						file.setUpdateTime(new Date());
						updateFlag = cfsFileUploadService.update(file);
//						System.out.println("updateFlag="+updateFlag);
						if(!updateFlag) {
							break;
						}
					}
				}
			}
			if(updateFlag){
				renderJson(Ret.ok("msg", "操作成功!"));
			}else{
				renderJson(Ret.fail("msg", "操作失败！"));
			}
		}
    }
    
    //@EnableCORS
    public void fileDel() {
    	CfsFileUpload model = getBean(CfsFileUpload.class, "", true);
    	model.setUpdateTime(new Date());
		if(cfsFileUploadService.update(model)){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }


    /**
     * 根据文件url下载文件
     */
    //@EnableCORS
    public void downFileByUrl() {
        String fileUrl = getPara("src");
        if(StringUtils.isBlank(fileUrl)){ renderJson(Ret.fail("msg", "未传入文件路径")); return; }
        File file = FileUploadKit.downNetworkFile(fileUrl);
        renderFile(file);
    }

    //@EnableCORS
	public void downloadFile(){
    	String id=getPara("id");
    	String src=getPara("src");
		String name=getPara("name");
		//renderFile(FileUploadKit.downNetworkFile(employeeFile.getFileUrl(),employee.getFullName()+"-"+employeeFile.getFileTitle()));
		HttpServletResponse response=getResponse();
		//response.setContentType("application/x-download");
		CfsUploadFile uploadFile =null;
		if(StrKit.notBlank(id)){
			uploadFile=cfsUploadFileService.findById(id);
		}else if(StrKit.notBlank(name)){
			uploadFile=cfsUploadFileService.getByName(name);
		}else if(StrKit.notBlank(src)){
			uploadFile=cfsUploadFileService.getBySrc(src);
		}

		if(uploadFile==null){
			renderCodeFailed("文件不存在");
			return;
		}



		String suffix = uploadFile.getSrc().substring(uploadFile.getSrc().lastIndexOf("."));
		String dwonloadFileName=uploadFile.getOldName();
		if(StrKit.isBlank(dwonloadFileName)){
			dwonloadFileName=uploadFile.getName();
		}
		dwonloadFileName=dwonloadFileName.replace(suffix,"");
		InputStream in = null;
		OutputStream outputStream=null;
		try{
			outputStream=response.getOutputStream();
			//response.addHeader("Content-Disposition", "attachment;filename="+new String(dwonloadFileName.getBytes("utf-8"),"GBK")+suffix);
			//response.addHeader("Content-Disposition", "attachment;filename="+dwonloadFileName+suffix);

			/*String agent = getRequest().getHeader("USER-AGENT"); //获取浏览器的信息
			if(agent != null && agent.toLowerCase().indexOf("firefox")>0){
				System.out.println("火狐浏览器");
				//火狐浏览器自己会对URL进行一次URL转码所以区别处理
				response.setHeader("Content-Disposition",
						"attachment; filename="+ new String((dwonloadFileName+suffix).getBytes("GB2312"),"ISO-8859-1"));
			}else if(agent.toLowerCase().indexOf("safari")>0){
				System.out.println("苹果浏览器");
				//苹果浏览器需要用ISO 而且文件名得用UTF-8
				response.setHeader("Content-Disposition",
						"attachment; filename="+new String((dwonloadFileName+suffix).getBytes("UTF-8"),"UTF-8"));
			}else{
				System.out.println("其他的浏览器");
				//其他的浏览器
				response.setHeader("Content-Disposition",
						"attachment; filename=\""+java.net.URLEncoder.encode((dwonloadFileName+suffix), "ISO-8859-1"));
			}*/

			response.setCharacterEncoding("utf-8");
			response.setContentType("application/msword");
			String  fileName=dwonloadFileName+suffix;

			/*if (getRequest().getHeader("User-Agent").toLowerCase().indexOf("firefox") > 0) {
				fileName = new String(fileName.getBytes("UTF-8"), "ISO8859-1"); // firefox浏览器
				System.out.println("----firefox浏览器");
			} else if (getRequest().getHeader("User-Agent").toUpperCase().indexOf("MSIE") > 0) {
				fileName = URLEncoder.encode(fileName, "UTF-8");// IE浏览器
				System.out.println("----IE浏览器");
			}else if (getRequest().getHeader("User-Agent").toUpperCase().indexOf("CHROME") > 0) {
				//fileName = new String(fileName.getBytes("ISO8859-1"), "UTF-8");// 谷歌
				System.out.println("----谷歌");
			}*/
			response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));


			URL fileURL = new URL(uploadFile.getSrc());
			URLConnection con = fileURL.openConnection();
			// 设置请求超时为5s
			con.setConnectTimeout(5 * 1000);
			in = con.getInputStream();
			/*response.setHeader("Content-Length", String.valueOf(con.getContentLength()));
			response.setContentType(con.getHeaderField("Content-Type"));
			response.setContentType("application/zip;charset=UTF-8");
			response.setCharacterEncoding("UTF-8");*/
			byte[] buffer = new byte[1024];
			int len = 0;
			while ((len = in.read(buffer)) != -1) {
				outputStream.write(buffer, 0, len);
				outputStream.flush();
			}

			//response.flushBuffer();
		}catch (IOException e){
			e.printStackTrace();
		}catch (Exception e){
			e.printStackTrace();
		}finally {
			try {
				if(in!=null){
					in.close();
				}

				if(outputStream!=null){
					outputStream.close();
				}
			}catch (Exception e){
				e.printStackTrace();
			}
		}
		renderNull();
	}

	@Inject
	private FinaCardTransactionsService finaCardTransactionsService;

	public void test(){
		String startDate=getPara("startDate");
		String endDate=getPara("endDate");
		List<String> list=new ArrayList<>();
		newMonthBalance2(1,500,startDate,endDate,list);
		logger.info("结存"+JSON.toJSONString(list));
		renderJson("ok");
	}
	public void test2(){
		String startDate=getPara("startDate");
		String endDate=getPara("endDate");
		newMonthBalance3(1,500);
		renderJson("ok");
	}

	public void test3(){
		String startDate=getPara("startDate");
		String endDate=getPara("endDate");
		if(StrKit.isBlank(startDate) || StrKit.isBlank(endDate)){
			renderJson("参数缺失");
			return;
		}
		try {
			newMonthBalance(1,500,startDate,endDate);
			renderSuccess();
		}catch (Exception e){
			e.printStackTrace();
			renderCodeFailed();
		}

	}

	/*public void newMonthBalance(int pageNumber,int pageSize,String startDate,String endDate){
		String cardSql=" from fina_membership_card a left join main_membership_card_type b on a.card_type_id=b.id " +
				"left join main_card_deduct_scheme c on c.id=a.deduct_scheme_id left join main_card_deduct_scheme d on d.id=a.long_deduct_scheme_id " +
				"where a.del_flag='0' and a.create_time<? ";
		Page<Record> cardPage= Db.paginate(pageNumber,pageSize," select a.id,a.card_number,b.is_integral,c.deduct_way,d.deduct_way as long_deduct_way  ",cardSql,endDate);
		if(cardPage.getList()==null || cardPage.getList().size()==0){
			return;
		}
		try {

			List<String> params=new ArrayList<>();
			StringBuffer stringBuffer=new StringBuffer("");
			params.add(endDate);

			Map<String,String> deductWayMap=new HashMap<>();
			Map<String,String> isIntegralMap=new HashMap<>();

			List<String> lastMonthBalance=new ArrayList<>();

			for(Record record:cardPage.getList()){
				stringBuffer.append("?,");
				params.add(record.getStr("id"));
				if(StrKit.notBlank(record.getStr("deduct_way"))){
					deductWayMap.put(record.getStr("card_number"),record.getStr("deduct_way"));
				}else{
					deductWayMap.put(record.getStr("card_number"),record.getStr("long_deduct_way"));
				}
				isIntegralMap.put(record.getStr("card_number"),record.getStr("is_integral"));
				lastMonthBalance.add(record.getStr("card_number"));
			}
			String str=stringBuffer.substring(0,stringBuffer.length()-1);

			String lastYearMonth=DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(startDate),-1),"yyyy-MM");
			lastMonthBalance.add(lastYearMonth);

			String sql=" select a.id,a.card_number,balance,consume_times,consume_points,card_integrals,a.bean_coupons,b.id as transaction_id ,b.amount_snapshot, " +
					"times_snapshot,points_snapshot,integrals_snapshot,bean_coupons_snapshot,b.deal_time " +
					" from fina_membership_card a " +
					"left join fina_card_transactions b on b.id=( " +
					"select id from fina_card_transactions where a.id=card_id and is_hidden='0' and deal_time<=?" +
					"GROUP BY id " +
					"ORDER BY deal_time desc, " +
					"(if(times_snapshot is NULL,0,times_snapshot) " +
					"+if(amount_snapshot is NULL,0,amount_snapshot) " +
					"+if(points_snapshot is NULL,0,points_snapshot) " +
					"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
					"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) " +
					"limit 1 " +
					") " +
					"where  a.id in("+str+")";
			List<Record> recordList=Db.find(sql,params.toArray());
			List<FinaCardMonthBalanceNew> monthBalanceList=new ArrayList<>();

			String cardTransactionsSql="select * from fina_card_transactions where card_id=? and deal_time>? and deal_time<? and id<>?  and is_hidden='0' order by deal_time, "+
					"(if(times_snapshot is NULL,0,times_snapshot) " +
					"+if(amount_snapshot is NULL,0,amount_snapshot) " +
					"+if(points_snapshot is NULL,0,points_snapshot) " +
					"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
					"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc ";

			String cardTransactionsSql2="select * from fina_card_transactions where card_id=? and deal_time BETWEEN ? and ?   and is_hidden='0' order by deal_time, "+
					"(if(times_snapshot is NULL,0,times_snapshot) " +
					"+if(amount_snapshot is NULL,0,amount_snapshot) " +
					"+if(points_snapshot is NULL,0,points_snapshot) " +
					"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
					"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc ";

			String giveIntegralSql="select sum(integral_value) from fina_card_integral_record where card_id=? and transaction_type='in' and business_type='system_give' " +
					"and give_date>? and give_date<=?  and del_flag='0' ";

			String lastMonthBalanceSql="select * from fina_card_month_balance_new where card_number in ("+str+") and `year_month`=? and del_flag='0' ";
			List<Record> lastMonthBalanceList=Db.find(lastMonthBalanceSql,lastMonthBalance.toArray());
			Map<String,Record> lastMonthBalanceMap=new HashMap<>();
			for(Record record:lastMonthBalanceList){
				lastMonthBalanceMap.put(record.getStr("card_number"),record);
			}

			String yearMonth=DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyy-MM");
			for (Record record : recordList) {
				String cardNumber=record.getStr("card_number");
				String cardId=record.getStr("id");

				if(StrKit.isBlank(deductWayMap.get(cardNumber))){
					continue;
				}

				boolean isIntegralCard="1".equals(isIntegralMap.get(cardNumber));
				//期末值
				Double times=0.0;
				Double amount=0.0;
				Double points=0.0;
				Double integrals=0.0;
				Double beanCoupons=0.0;

				//充值值
				Double rtimes=0.0;
				Double ramount=0.0;
				Double rpoints=0.0;
				Double rintegrals=0.0;
				Double rbeanCoupons=0.0;
				//赠送积分
				Double giveIntegrals=0.0;

				//消费值
				Double ctimes=0.0;
				Double camount=0.0;
				Double cpoints=0.0;
				Double cintegrals=0.0;
				Double cbeanCoupons=0.0;



				Date giveIntegralsDate=null;
				if(StrKit.isBlank(record.getStr("transaction_id"))){
					//结束时间内会员卡无消费记录
					Record minRecord=Db.findFirst("select * from fina_card_transactions where card_id=? and is_hidden='0'  ORDER BY deal_time,(if(times_snapshot is NULL,0,times_snapshot) " +
							"+if(amount_snapshot is NULL,0,amount_snapshot) " +
							"+if(points_snapshot is NULL,0,points_snapshot) " +
							"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
							"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc  limit 1",cardId);
					if(minRecord!=null){
						//结束时间后有消费记录，需要minRecord的余额，然后减去 结束时间至minRecord的操作时间的充值 加上 结束时间至minRecord的操作时间的消费
						Date minRecordDealTime=minRecord.getDate("deal_time");
						giveIntegralsDate=minRecordDealTime;
						times=minRecord.getDouble("times_snapshot");
						amount=minRecord.getDouble("amount_snapshot");
						points=minRecord.getDouble("points_snapshot");
						integrals=minRecord.getDouble("integrals_snapshot");
						beanCoupons=minRecord.getDouble("bean_coupons_snapshot");

						List<Record> cardTransactionsRecordList=Db.find(cardTransactionsSql,cardId,endDate,minRecordDealTime,minRecord.getStr("id"));
						cardTransactionsRecordList.add(minRecord);
						for (Record r : cardTransactionsRecordList) {
							//循环充值消费记录，消费的+，充值的-
							if("2".equals(r.getStr("in_out_flag"))){
								if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
									times+=r.getDouble("times");
								}
								if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
									amount+=r.getDouble("amount");
								}
								if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
									points+=r.getDouble("points");
								}
								if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
									integrals+=r.getDouble("integrals");
								}
								if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
									beanCoupons+=r.getDouble("bean_coupons");
								}
							}else if("1".equals(r.getStr("in_out_flag"))){
								if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
									times-=r.getDouble("times");
								}
								if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
									amount-=r.getDouble("amount");
								}
								if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
									points-=r.getDouble("points");
								}
								if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
									integrals-=r.getDouble("integrals");
								}
								if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
									beanCoupons-=r.getDouble("bean_coupons");
								}
							}
						}
						if(isIntegralCard){
							//积分减去结束时间至minRecord时间直接赠送的
							Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,endDate,DateUtils.formatDate(minRecordDealTime,"yyyy-MM-dd"));
							if(giveIntegralValue!=null){
								integrals+=giveIntegralValue;
							}
						}
					}else{
						//结束时间后无消费记录
						times=record.getDouble("consume_times");
						amount=record.getDouble("balance");
						points=record.getDouble("consume_points");
						integrals=record.getDouble("card_integrals");
						beanCoupons=record.getDouble("bean_coupons");
						giveIntegralsDate=new Date();
						if(isIntegralCard){

							//积分减去结束时间至minRecord时间直接赠送的
							Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,endDate,DateUtils.formatDate(giveIntegralsDate,"yyyy-MM-dd"));
							if(giveIntegralValue!=null){
								integrals+=giveIntegralValue;
							}
						}
					}
				}else{
					//结束时间内会员卡有消费记录
					times=record.getDouble("times_snapshot");
					amount=record.getDouble("amount_snapshot");
					points=record.getDouble("points_snapshot");
					integrals=record.getDouble("integrals_snapshot");
					beanCoupons=record.getDouble("bean_coupons_snapshot");

					List<Record> cardTransactionsRecordList=Db.find(cardTransactionsSql2,cardId,startDate,endDate);
					for (Record r : cardTransactionsRecordList) {
						if("2".equals(r.getStr("in_out_flag"))){
							if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
								ctimes += r.getDouble("times");
							}
							if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
								camount += r.getDouble("amount");
							}
							if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
								cpoints += r.getDouble("points");
							}
							if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
								cintegrals+=r.getDouble("integrals");
							}
							if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
								cbeanCoupons += r.getDouble("bean_coupons");
							}
						}else if("1".equals(r.getStr("in_out_flag"))){
							if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
								rtimes += r.getDouble("times");
							}
							if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
								ramount += r.getDouble("amount");
							}
							if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
								rpoints += r.getDouble("points");
							}
							if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
								rintegrals+=r.getDouble("integrals");
							}
							if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
								rbeanCoupons += r.getDouble("bean_coupons");
							}

						}
					}
					if(isIntegralCard){
						//加上最大消费记录至结束日期期间赠送的积分
						Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,DateUtils.formatDate(DateUtils.getNextDay(record.getDate("deal_time"),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.parseDate(endDate),"yyyy-MM-dd"));
						if(giveIntegralValue!=null){
							integrals+=giveIntegralValue;
						}

					}
				}

				if(isIntegralCard){
					Double monthGiveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(startDate),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.parseDate(endDate),"yyyy-MM-dd"));
					if(monthGiveIntegralValue==null){
						monthGiveIntegralValue=0.0;
					}
					giveIntegrals=monthGiveIntegralValue;
				}
				Record lastMonthBalanceRecord=lastMonthBalanceMap.get(cardNumber);
				if(times==null){
					times=0.0;
				}
				if(amount==null){
					amount=0.0;
				}
				if(points==null){
					points=0.0;
				}
				if(integrals==null){
					integrals=0.0;
				}
				if(beanCoupons==null){
					beanCoupons=0.0;
				}

				FinaCardMonthBalanceNew monthBalance=new FinaCardMonthBalanceNew();
				monthBalance.setId(IdGen.getUUID());
				monthBalance.setCardNumber(cardNumber);
				monthBalance.setCardId(cardId);
				monthBalance.setAmount(amount);
				monthBalance.setTimes(times);
				monthBalance.setPoints(points);
				monthBalance.setIntegrals(integrals);
				monthBalance.setGiveIntegrals(giveIntegrals);
				monthBalance.setBeanCoupons(beanCoupons);
				monthBalance.setYearMonth(yearMonth);
				monthBalance.setDelFlag("0");
				monthBalance.setCreateDate(new Date());
				monthBalance.setUpdateDate(new Date());

				monthBalance.setLastMonthTimes(0.0);
				monthBalance.setLastMonthAmount(0.0);
				monthBalance.setLastMonthPoints(0.0);
				monthBalance.setLastMonthIntegrals(0.0);
				monthBalance.setLastMonthBeanCoupons(0.0);
				if(lastMonthBalanceRecord!=null){
					monthBalance.setLastMonthTimes(lastMonthBalanceRecord.getDouble("times"));
					monthBalance.setLastMonthAmount(lastMonthBalanceRecord.getDouble("amount"));
					monthBalance.setLastMonthPoints(lastMonthBalanceRecord.getDouble("points"));
					monthBalance.setLastMonthIntegrals(lastMonthBalanceRecord.getDouble("integrals"));
					monthBalance.setLastMonthBeanCoupons(lastMonthBalanceRecord.getDouble("bean_coupons"));
				}
				monthBalance.setRechargeAmount(ramount);
				monthBalance.setRechargeBeanCoupons(rbeanCoupons);
				monthBalance.setRechargeIntegrals(rintegrals);
				monthBalance.setRechargePoints(rpoints);
				monthBalance.setRechargeTimes(rtimes);
				monthBalance.setConsumeTimes(ctimes);
				monthBalance.setConsumeAmount(camount);
				monthBalance.setConsumePoints(cpoints);
				monthBalance.setConsumeIntegrals(cintegrals);
				monthBalance.setConsumeBeanCoupons(cbeanCoupons);
				monthBalance.setCreateBy("2CFA83C2-CA2E-4B1F-BA1A-1285354C2797");
				monthBalance.setUpdateBy("2CFA83C2-CA2E-4B1F-BA1A-1285354C2797");
				monthBalanceList.add(monthBalance);
			}
			Db.batchSave(monthBalanceList,monthBalanceList.size());
		}catch (Exception e){
			e.printStackTrace();
		}

		pageNumber++;
		if(pageNumber<=cardPage.getTotalPage()){
			newMonthBalance(pageNumber,pageSize,startDate,endDate);
		}
	}*/

	public void newMonthBalance(int pageNumber,int pageSize,String startDate,String endDate){
		String cardSql=" from fina_membership_card a left join main_membership_card_type b on a.card_type_id=b.id " +
				"left join main_card_deduct_scheme c on c.id=a.deduct_scheme_id left join main_card_deduct_scheme d on d.id=a.long_deduct_scheme_id " +
				"where a.del_flag='0' and a.create_time<?  ";
		Page<Record> cardPage= Db.paginate(pageNumber,pageSize," select a.id,a.card_number,b.is_integral,c.deduct_way,d.deduct_way as long_deduct_way  ",cardSql,endDate);
		if(cardPage.getList()==null || cardPage.getList().size()==0){
			return;
		}
		try {

			List<String> params=new ArrayList<>();
			StringBuffer stringBuffer=new StringBuffer("");
			params.add(endDate);

			Map<String,String> deductWayMap=new HashMap<>();
			Map<String,String> isIntegralMap=new HashMap<>();

			List<String> lastMonthBalance=new ArrayList<>();

			for(Record record:cardPage.getList()){
				stringBuffer.append("?,");
				params.add(record.getStr("id"));
				if(StrKit.notBlank(record.getStr("deduct_way"))){
					deductWayMap.put(record.getStr("card_number"),record.getStr("deduct_way"));
				}else{
					deductWayMap.put(record.getStr("card_number"),record.getStr("long_deduct_way"));
				}
				isIntegralMap.put(record.getStr("card_number"),record.getStr("is_integral"));
				lastMonthBalance.add(record.getStr("card_number"));
			}
			String str=stringBuffer.substring(0,stringBuffer.length()-1);

			String lastYearMonth=DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(startDate),-1),"yyyy-MM");
			lastMonthBalance.add(lastYearMonth);

			String sql=" select a.id,a.card_number,balance,consume_times,consume_points,card_integrals,a.bean_coupons,b.id as transaction_id ,b.amount_snapshot, " +
					"times_snapshot,points_snapshot,integrals_snapshot,bean_coupons_snapshot,b.deal_time " +
					" from fina_membership_card a " +
					"left join fina_card_transactions b on b.id=( " +
					"select id from fina_card_transactions where a.id=card_id and is_hidden='0' and deal_time<=?" +
					"GROUP BY id " +
					"ORDER BY deal_time desc, " +
					"(if(times_snapshot is NULL,0,times_snapshot) " +
					"+if(amount_snapshot is NULL,0,amount_snapshot) " +
					"+if(points_snapshot is NULL,0,points_snapshot) " +
					"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
					"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) " +
					"limit 1 " +
					") " +
					"where  a.id in("+str+")";
			List<Record> recordList=Db.find(sql,params.toArray());
			List<FinaCardMonthBalanceNew> monthBalanceList=new ArrayList<>();

			String cardTransactionsSql="select * from fina_card_transactions where card_id=? and deal_time>? and deal_time<? and id<>?  and is_hidden='0' order by deal_time, "+
					"(if(times_snapshot is NULL,0,times_snapshot) " +
					"+if(amount_snapshot is NULL,0,amount_snapshot) " +
					"+if(points_snapshot is NULL,0,points_snapshot) " +
					"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
					"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc ";

			String cardTransactionsSql2="select * from fina_card_transactions where card_id=? and deal_time BETWEEN ? and ?   and is_hidden='0' order by deal_time, "+
					"(if(times_snapshot is NULL,0,times_snapshot) " +
					"+if(amount_snapshot is NULL,0,amount_snapshot) " +
					"+if(points_snapshot is NULL,0,points_snapshot) " +
					"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
					"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc ";

			String giveIntegralSql="select sum(integral_value) from fina_card_integral_record where card_id=? and transaction_type='in' and business_type='system_give' " +
					"and give_date>? and give_date<=?  and del_flag='0' ";

			String lastMonthBalanceSql="select * from fina_card_month_balance_new where card_number in ("+str+") and `year_month`=? and del_flag='0' ";
			List<Record> lastMonthBalanceList=Db.find(lastMonthBalanceSql,lastMonthBalance.toArray());
			Map<String,Record> lastMonthBalanceMap=new HashMap<>();
			for(Record record:lastMonthBalanceList){
				lastMonthBalanceMap.put(record.getStr("card_number"),record);
			}

			Date startTime=DateUtils.parseDate(startDate);
			Date endTime=DateUtils.parseDate(endDate);

			String yearMonth=DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyy-MM");
			for (Record record : recordList) {
				String cardNumber=record.getStr("card_number");
				String cardId=record.getStr("id");

				if(StrKit.isBlank(deductWayMap.get(cardNumber))){
					continue;
				}

				boolean isIntegralCard="1".equals(isIntegralMap.get(cardNumber));
				//期末值
				Double times=0.0;
				Double amount=0.0;
				Double points=0.0;
				Double integrals=0.0;
				Double beanCoupons=0.0;

				//充值值
				Double rtimes=0.0;
				Double ramount=0.0;
				Double rpoints=0.0;
				Double rintegrals=0.0;
				Double rbeanCoupons=0.0;
				//赠送积分
				Double giveIntegrals=0.0;

				//消费值
				Double ctimes=0.0;
				Double camount=0.0;
				Double cpoints=0.0;
				Double cintegrals=0.0;
				Double cbeanCoupons=0.0;

				//过期豆豆券
				Double expirationBeanCoupons=0.0;


				Date giveIntegralsDate=null;
				if(StrKit.isBlank(record.getStr("transaction_id"))){
					//结束时间内会员卡无消费记录
					Record minRecord=Db.findFirst("select * from fina_card_transactions where card_id=? and is_hidden='0'  ORDER BY deal_time,(if(times_snapshot is NULL,0,times_snapshot) " +
							"+if(amount_snapshot is NULL,0,amount_snapshot) " +
							"+if(points_snapshot is NULL,0,points_snapshot) " +
							"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
							"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc  limit 1",cardId);
					if(minRecord!=null){
						//结束时间后有消费记录，需要minRecord的余额，然后减去 结束时间至minRecord的操作时间的充值 加上 结束时间至minRecord的操作时间的消费
						Date minRecordDealTime=minRecord.getDate("deal_time");
						giveIntegralsDate=minRecordDealTime;
						times=minRecord.getDouble("times_snapshot");
						amount=minRecord.getDouble("amount_snapshot");
						points=minRecord.getDouble("points_snapshot");
						integrals=minRecord.getDouble("integrals_snapshot");
						beanCoupons=minRecord.getDouble("bean_coupons_snapshot");

						List<Record> cardTransactionsRecordList=Db.find(cardTransactionsSql,cardId,endDate,minRecordDealTime,minRecord.getStr("id"));
						cardTransactionsRecordList.add(minRecord);
						for (Record r : cardTransactionsRecordList) {
							//循环充值消费记录，消费的+，充值的-
							if("2".equals(r.getStr("in_out_flag"))){
								if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
									times+=r.getDouble("times");
								}
								if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
									amount+=r.getDouble("amount");
								}
								if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
									points+=r.getDouble("points");
								}
								if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
									integrals+=r.getDouble("integrals");
								}
								if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
									beanCoupons+=r.getDouble("bean_coupons");
								}
							}else if("1".equals(r.getStr("in_out_flag"))){
								if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
									times-=r.getDouble("times");
								}
								if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
									amount-=r.getDouble("amount");
								}
								if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
									points-=r.getDouble("points");
								}
								if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
									integrals-=r.getDouble("integrals");
								}
								if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
									beanCoupons-=r.getDouble("bean_coupons");
								}
							}
						}
						if(isIntegralCard){
							//积分减去结束时间至minRecord时间直接赠送的
							Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,endDate,DateUtils.formatDate(minRecordDealTime,"yyyy-MM-dd"));
							if(giveIntegralValue!=null){
								integrals+=giveIntegralValue;
							}
						}
					}else{
						//结束时间后无消费记录
						times=record.getDouble("consume_times");
						amount=record.getDouble("balance");
						points=record.getDouble("consume_points");
						integrals=record.getDouble("card_integrals");
						beanCoupons=record.getDouble("bean_coupons");
						giveIntegralsDate=new Date();
						if(isIntegralCard){

							//积分减去结束时间至minRecord时间直接赠送的
							Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,endDate,DateUtils.formatDate(giveIntegralsDate,"yyyy-MM-dd"));
							if(giveIntegralValue!=null){
								integrals+=giveIntegralValue;
							}
						}
					}
				}else{
					//结束时间内会员卡有消费记录
					times=record.getDouble("times_snapshot");
					amount=record.getDouble("amount_snapshot");
					points=record.getDouble("points_snapshot");
					integrals=record.getDouble("integrals_snapshot");
					beanCoupons=record.getDouble("bean_coupons_snapshot");

					List<Record> cardTransactionsRecordList=Db.find(cardTransactionsSql2,cardId,startDate,endDate);
					for (Record r : cardTransactionsRecordList) {
						if("2".equals(r.getStr("in_out_flag"))){
							if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
								ctimes += r.getDouble("times");
							}
							if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
								camount += r.getDouble("amount");
							}
							if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
								cpoints += r.getDouble("points");
							}
							if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
								cintegrals+=r.getDouble("integrals");
							}
							if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
								cbeanCoupons += r.getDouble("bean_coupons");
							}
						}else if("1".equals(r.getStr("in_out_flag"))){
							if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
								rtimes += r.getDouble("times");
							}
							if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
								ramount += r.getDouble("amount");
							}
							if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
								rpoints += r.getDouble("points");
							}
							if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
								rintegrals+=r.getDouble("integrals");
							}
							if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
								rbeanCoupons += r.getDouble("bean_coupons");
							}

						}
					}
					if(isIntegralCard){
						//加上最大消费记录至结束日期期间赠送的积分
						Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,DateUtils.formatDate(DateUtils.getNextDay(record.getDate("deal_time"),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.parseDate(endDate),"yyyy-MM-dd"));
						if(giveIntegralValue!=null){
							integrals+=giveIntegralValue;
						}

					}
				}

				if(isIntegralCard){
					Double monthGiveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(startDate),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.parseDate(endDate),"yyyy-MM-dd"));
					if(monthGiveIntegralValue==null){
						monthGiveIntegralValue=0.0;
					}
					giveIntegrals=monthGiveIntegralValue;
				}
				Record lastMonthBalanceRecord=lastMonthBalanceMap.get(cardNumber);
				if(times==null){
					times=0.0;
				}
				if(amount==null){
					amount=0.0;
				}
				if(points==null){
					points=0.0;
				}
				if(integrals==null){
					integrals=0.0;
				}
				if(beanCoupons==null){
					beanCoupons=0.0;
				}

				//获取豆豆券
				List<Record> beanCouponsBindingList=Db.find("select b.balance_roll_value,a.create_time from fina_card_roll a " +
						"INNER join crm_card_roll_record b on b.id=a.roll_id and b.is_enable='0' " +
						"INNER join crm_card_roll c on c.id=b.card_roll_id and c.roll_type_id='86F75466-2CD4-455C-9A31-0CFF0846BD7B' " +
						"where card_id=? and a.create_time<?",cardId,endDate);


				for (Record beanCouponsBinding : beanCouponsBindingList) {
					if(beanCouponsBinding.getDouble("balance_roll_value")!=null){
						beanCoupons+=beanCouponsBinding.getDouble("balance_roll_value");
						Date bindingTime=beanCouponsBinding.getDate("create_time");
						if(DateUtils.compareDays(startTime,bindingTime)<0 && DateUtils.compareDays(endTime,bindingTime)>=0  ){
							cbeanCoupons+=beanCouponsBinding.getDouble("balance_roll_value");
						}
					}
				}

				//获取消费豆豆券
				Double expirationDoudouquan=Db.queryDouble("select SUM(b.balance_roll_value) from fina_card_roll a " +
						"INNER join crm_card_roll_record b on b.id=a.roll_id and b.is_enable='1' " +
						"INNER join crm_card_roll c on c.id=b.card_roll_id and c.roll_type_id='86F75466-2CD4-455C-9A31-0CFF0846BD7B' " +
						"where expiration_time BETWEEN ? and ? and a.card_id=? ",startDate,endDate,cardId);
				if(expirationDoudouquan!=null){
					expirationBeanCoupons=expirationDoudouquan;
				}


				//消费豆豆券
				Double xiaofeiDoudouquan=Db.queryDouble("select SUM(d.use_roll_value) from fina_card_roll a " +
						"INNER join crm_card_roll_record b on b.id=a.roll_id  " +
						"INNER join crm_card_roll_record_use d on d.record_id=b.id " +
						"INNER join crm_card_roll c on c.id=b.card_roll_id and c.roll_type_id='86F75466-2CD4-455C-9A31-0CFF0846BD7B'\n" +
						"where d.use_time BETWEEN ? and ? and a.card_id=? and d.del_flag='0' ",startDate,endDate,cardId);
				if(xiaofeiDoudouquan!=null){
					if(cbeanCoupons>xiaofeiDoudouquan || cbeanCoupons<xiaofeiDoudouquan){
						logger.info(cardNumber+"豆豆券消费数量不一致"+startDate+"___"+endDate);
					}
					cbeanCoupons=xiaofeiDoudouquan;

				}


				FinaCardMonthBalanceNew monthBalance=new FinaCardMonthBalanceNew();
				monthBalance.setId(IdGen.getUUID());
				monthBalance.setCardNumber(cardNumber);
				monthBalance.setCardId(cardId);
				monthBalance.setAmount(amount);
				monthBalance.setTimes(times);
				monthBalance.setPoints(points);
				monthBalance.setIntegrals(integrals);
				monthBalance.setGiveIntegrals(giveIntegrals);
				monthBalance.setBeanCoupons(beanCoupons);
				monthBalance.setYearMonth(yearMonth);
				monthBalance.setDelFlag("0");
				monthBalance.setCreateDate(new Date());
				monthBalance.setUpdateDate(new Date());

				monthBalance.setLastMonthTimes(0.0);
				monthBalance.setLastMonthAmount(0.0);
				monthBalance.setLastMonthPoints(0.0);
				monthBalance.setLastMonthIntegrals(0.0);
				monthBalance.setLastMonthBeanCoupons(0.0);
				if(lastMonthBalanceRecord!=null){
					monthBalance.setLastMonthTimes(lastMonthBalanceRecord.getDouble("times"));
					monthBalance.setLastMonthAmount(lastMonthBalanceRecord.getDouble("amount"));
					monthBalance.setLastMonthPoints(lastMonthBalanceRecord.getDouble("points"));
					monthBalance.setLastMonthIntegrals(lastMonthBalanceRecord.getDouble("integrals"));
					monthBalance.setLastMonthBeanCoupons(lastMonthBalanceRecord.getDouble("bean_coupons"));
				}
				monthBalance.setRechargeAmount(ramount);
				monthBalance.setRechargeBeanCoupons(rbeanCoupons);
				monthBalance.setRechargeIntegrals(rintegrals);
				monthBalance.setRechargePoints(rpoints);
				monthBalance.setRechargeTimes(rtimes);
				monthBalance.setConsumeTimes(ctimes);
				monthBalance.setConsumeAmount(camount);
				monthBalance.setConsumePoints(cpoints);
				monthBalance.setConsumeIntegrals(cintegrals);
				monthBalance.setConsumeBeanCoupons(cbeanCoupons);
				monthBalance.setExpirationBeanCoupons(expirationBeanCoupons);
				monthBalance.setCreateBy("2CFA83C2-CA2E-4B1F-BA1A-1285354C2797");
				monthBalance.setUpdateBy("2CFA83C2-CA2E-4B1F-BA1A-1285354C2797");
				monthBalanceList.add(monthBalance);
			}
			Db.batchSave(monthBalanceList,monthBalanceList.size());
		}catch (Exception e){
			e.printStackTrace();
		}

		pageNumber++;
		if(pageNumber<=cardPage.getTotalPage()){
			newMonthBalance(pageNumber,pageSize,startDate,endDate);
		}
	}

	public void newMonthBalance2(int pageNumber,int pageSize,String startDate,String endDate,List<String> list){

		String cardSql=" from fina_membership_card a left join main_membership_card_type b on a.card_type_id=b.id " +
				"left join main_card_deduct_scheme c on c.id=a.deduct_scheme_id left join main_card_deduct_scheme d on d.id=a.long_deduct_scheme_id " +
				"where a.del_flag='0' and a.create_time<?  ";
		Page<Record> cardPage= Db.paginate(pageNumber,pageSize," select a.id,a.card_number,b.is_integral,c.deduct_way,d.deduct_way as long_deduct_way  ",cardSql,endDate);
		if(cardPage.getList()==null || cardPage.getList().size()==0){
			return;
		}
		List<String> params=new ArrayList<>();
		StringBuffer stringBuffer=new StringBuffer("");
		params.add(endDate);

		Map<String,String> deductWayMap=new HashMap<>();
		Map<String,String> isIntegralMap=new HashMap<>();

		List<String> lastMonthBalance=new ArrayList<>();

		for(Record record:cardPage.getList()){
			stringBuffer.append("?,");
			params.add(record.getStr("id"));
			if(StrKit.notBlank(record.getStr("deduct_way"))){
				deductWayMap.put(record.getStr("card_number"),record.getStr("deduct_way"));
			}else{
				deductWayMap.put(record.getStr("card_number"),record.getStr("long_deduct_way"));
			}
			isIntegralMap.put(record.getStr("card_number"),record.getStr("is_integral"));
			lastMonthBalance.add(record.getStr("card_number"));
		}
		String str=stringBuffer.substring(0,stringBuffer.length()-1);

		String lastYearMonth=DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(startDate),-1),"yyyy-MM");
		lastMonthBalance.add(lastYearMonth);

		String sql=" select a.id,a.card_number,balance,consume_times,consume_points,card_integrals,a.bean_coupons,b.id as transaction_id ,b.amount_snapshot, " +
				"times_snapshot,points_snapshot,integrals_snapshot,bean_coupons_snapshot,b.deal_time " +
				" from fina_membership_card a " +
				"left join fina_card_transactions b on b.id=( " +
				"select id from fina_card_transactions where a.id=card_id and is_hidden='0' and deal_time<=?" +
				"GROUP BY id " +
				"ORDER BY deal_time desc, " +
				"(if(times_snapshot is NULL,0,times_snapshot) " +
				"+if(amount_snapshot is NULL,0,amount_snapshot) " +
				"+if(points_snapshot is NULL,0,points_snapshot) " +
				"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
				"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) " +
				"limit 1 " +
				") " +
				"where  a.id in("+str+")";
		List<Record> recordList=Db.find(sql,params.toArray());
		List<FinaCardMonthBalanceNew> monthBalanceList=new ArrayList<>();

		String cardTransactionsSql="select * from fina_card_transactions where card_id=? and deal_time BETWEEN ? and ? and id<>?  and is_hidden='0' order by deal_time, "+
				"(if(times_snapshot is NULL,0,times_snapshot) " +
				"+if(amount_snapshot is NULL,0,amount_snapshot) " +
				"+if(points_snapshot is NULL,0,points_snapshot) " +
				"+if(integrals_snapshot is NULL,0,integrals_snapshot) " +
				"+if(bean_coupons_snapshot is NULL,0,bean_coupons_snapshot)) desc ";

		String giveIntegralSql="select sum(integral_value) from fina_card_integral_record where card_id=? and transaction_type='in' and business_type='system_give' " +
				"and give_date>? and give_date<=?  and del_flag='0' ";

		String lastMonthBalanceSql="select * from fina_card_month_balance_new where card_number in ("+str+") and `year_month`=? and del_flag='0' ";
		List<Record> lastMonthBalanceList=Db.find(lastMonthBalanceSql,lastMonthBalance.toArray());
		Map<String,Record> lastMonthBalanceMap=new HashMap<>();
		for(Record record:lastMonthBalanceList){
			lastMonthBalanceMap.put(record.getStr("card_number"),record);
		}

		String yearMonth=DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyy-MM");
		for (Record record : recordList) {
			String cardNumber=record.getStr("card_number");
			String cardId=record.getStr("id");
			String deductWay=deductWayMap.get(cardNumber);

			if(StrKit.isBlank(deductWay)){
				continue;
			}

			boolean isIntegralCard="1".equals(isIntegralMap.get(cardNumber));
			//期末值
			Double times=0.0;
			Double amount=0.0;
			Double points=0.0;
			Double integrals=0.0;
			Double beanCoupons=0.0;

			//充值值
			Double rtimes=0.0;
			Double ramount=0.0;
			Double rpoints=0.0;
			Double rintegrals=0.0;
			Double rbeanCoupons=0.0;
			//赠送积分
			Double giveIntegrals=0.0;

			//消费值
			Double ctimes=0.0;
			Double camount=0.0;
			Double cpoints=0.0;
			Double cintegrals=0.0;
			Double cbeanCoupons=0.0;



			Date giveIntegralsDate=null;
			if(StrKit.isBlank(record.getStr("transaction_id"))){
				//结束时间内会员卡无消费记录
				Record minRecord=Db.findFirst("select * from fina_card_transactions where card_id=? and is_hidden='0'  ORDER BY deal_time  limit 1",cardId);
				if(minRecord!=null){
					//结束时间后有消费记录，需要minRecord的余额，然后减去 结束时间至minRecord的操作时间的充值 加上 结束时间至minRecord的操作时间的消费
					Date minRecordDealTime=minRecord.getDate("deal_time");
					giveIntegralsDate=minRecordDealTime;
					times=minRecord.getDouble("times_snapshot");
					amount=minRecord.getDouble("amount_snapshot");
					points=minRecord.getDouble("points_snapshot");
					integrals=minRecord.getDouble("integrals_snapshot");
					beanCoupons=minRecord.getDouble("bean_coupons_snapshot");

					if(deductWay.equals("deduct_by_money")){
						if(amount==null){
							list.add(cardNumber+"余额"+minRecord.getStr("id")+"  "+DateUtils.formatDate(minRecord.getDate("deal_time"),"yyyy-MM-dd HH:mm:ss"));
							continue;
						}
					}else if(deductWay.equals("deduct_by_days")){
						String str2="";
						if(times==null){
							str2+="天数";
						}
						if(isIntegralCard){
							if(integrals==null){
								str2+="积分";
							}
						}
						if(StrKit.notBlank(str2)){
							list.add(cardNumber+str2+minRecord.getStr("id")+"  "+DateUtils.formatDate(minRecord.getDate("deal_time"),"yyyy-MM-dd HH:mm:ss"));
							continue;
						}
					}
					if(true){
						continue;
					}

					List<Record> cardTransactionsRecordList=Db.find(cardTransactionsSql,cardId,endDate,minRecordDealTime,minRecord.getStr("id"));
					for (Record r : cardTransactionsRecordList) {
						//循环充值消费记录，消费的+，充值的-
						if("2".equals(r.getStr("in_out_flag"))){
							if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
								times+=r.getDouble("times");
							}
							if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
								amount+=r.getDouble("amount");
							}
							if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
								points+=r.getDouble("points");
							}
							if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
								integrals+=r.getDouble("integrals");
							}
							if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
								beanCoupons+=r.getDouble("bean_coupons");
							}
						}else if("1".equals(r.getStr("in_out_flag"))){
							if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
								times-=r.getDouble("times");
							}
							if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
								amount-=r.getDouble("amount");
							}
							if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
								points-=r.getDouble("points");
							}
							if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
								integrals-=r.getDouble("integrals");
							}
							if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
								beanCoupons-=r.getDouble("bean_coupons");
							}
						}
					}
					if(isIntegralCard){
						//积分减去结束时间至minRecord时间直接赠送的
						Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,endDate,DateUtils.formatDate(minRecordDealTime,"yyyy-MM-dd"));
						if(giveIntegralValue!=null){
							integrals+=giveIntegralValue;
						}
					}
				}else{
					//结束时间后无消费记录
					times=record.getDouble("consume_times");
					amount=record.getDouble("balance");
					points=record.getDouble("consume_points");
					integrals=record.getDouble("card_integrals");
					beanCoupons=record.getDouble("bean_coupons");
					giveIntegralsDate=new Date();
					if(isIntegralCard){
						if(integrals==null){
							System.out.println(1);
						}
						//积分减去结束时间至minRecord时间直接赠送的
						Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,endDate,DateUtils.formatDate(giveIntegralsDate,"yyyy-MM-dd"));
						if(giveIntegralValue!=null){
							integrals+=giveIntegralValue;
						}
					}
				}
			}else{
				//结束时间内会员卡有消费记录
				times=record.getDouble("times_snapshot");
				amount=record.getDouble("amount_snapshot");
				points=record.getDouble("points_snapshot");
				integrals=record.getDouble("integrals_snapshot");
				beanCoupons=record.getDouble("bean_coupons_snapshot");

				if(deductWay.equals("deduct_by_money")){
					if(amount==null){
						list.add(cardNumber+"余额"+record.getStr("transaction_id")+"  "+DateUtils.formatDate(record.getDate("deal_time"),"yyyy-MM-dd HH:mm:ss"));
						continue;
					}
				}else if(deductWay.equals("deduct_by_days")){
					String str2="";
					if(times==null){
						str2+="天数";
					}
					if(isIntegralCard){
						if(integrals==null){
							str2+="积分";
						}
					}
					if(StrKit.notBlank(str2)){
						list.add(cardNumber+str2+record.getStr("transaction_id")+"  "+DateUtils.formatDate(record.getDate("deal_time"),"yyyy-MM-dd HH:mm:ss"));
						continue;
					}
				}
				if(true){
					continue;
				}
				List<Record> cardTransactionsRecordList=Db.find(cardTransactionsSql,cardId,startDate,endDate,null);
				for (Record r : cardTransactionsRecordList) {
					if("2".equals(r.getStr("in_out_flag"))){
						if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
							ctimes += r.getDouble("times");
						}
						if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
							camount += r.getDouble("amount");
						}
						if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
							cpoints += r.getDouble("points");
						}
						if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
							cintegrals-=r.getDouble("integrals");
						}
						if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
							cbeanCoupons += r.getDouble("bean_coupons");
						}
					}else if("1".equals(r.getStr("in_out_flag"))){
						if(r.getDouble("times")!=null && r.getDouble("times")>0.0){
							rtimes += r.getDouble("times");
						}
						if(r.getDouble("amount")!=null && r.getDouble("amount")>0.0){
							ramount += r.getDouble("amount");
						}
						if(r.getDouble("points")!=null && r.getDouble("points")>0.0){
							rpoints += r.getDouble("points");
						}
						if(r.getDouble("integrals")!=null && r.getDouble("integrals")>0.0){
							rintegrals-=r.getDouble("integrals");
						}
						if(r.getDouble("bean_coupons")!=null && r.getDouble("bean_coupons")>0.0){
							rbeanCoupons += r.getDouble("bean_coupons");
						}

					}
				}
				if(isIntegralCard){
					if(integrals==null){
						System.out.println(1);
					}
					//加上最大消费记录至结束日期期间赠送的积分
					Double giveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,DateUtils.formatDate(DateUtils.getNextDay(record.getDate("deal_time"),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.parseDate(endDate),"yyyy-MM-dd"));
					if(giveIntegralValue!=null){
						integrals+=giveIntegralValue;
					}

				}
			}

			if(isIntegralCard){
				Double monthGiveIntegralValue=Db.queryDouble(giveIntegralSql,cardId,DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(startDate),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.parseDate(endDate),"yyyy-MM-dd"));
				if(monthGiveIntegralValue==null){
					monthGiveIntegralValue=0.0;
				}
				giveIntegrals=monthGiveIntegralValue;
			}
			Record lastMonthBalanceRecord=lastMonthBalanceMap.get(cardNumber);

			FinaCardMonthBalanceNew monthBalance=new FinaCardMonthBalanceNew();
			monthBalance.setId(IdGen.getUUID());
			monthBalance.setCardNumber(cardNumber);
			monthBalance.setAmount(amount);
			monthBalance.setTimes(times);
			monthBalance.setPoints(points);
			monthBalance.setIntegrals(integrals);
			monthBalance.setGiveIntegrals(giveIntegrals);
			monthBalance.setBeanCoupons(beanCoupons);
			monthBalance.setYearMonth(yearMonth);
			monthBalance.setDelFlag("0");
			monthBalance.setCreateDate(new Date());
			monthBalance.setUpdateDate(new Date());
			if(lastMonthBalanceRecord!=null){
				monthBalance.setLastMonthTimes(lastMonthBalanceRecord.getDouble("times"));
				monthBalance.setLastMonthAmount(lastMonthBalanceRecord.getDouble("amount"));
				monthBalance.setLastMonthPoints(lastMonthBalanceRecord.getDouble("points"));
				monthBalance.setLastMonthIntegrals(lastMonthBalanceRecord.getDouble("integrals"));
				monthBalance.setLastMonthBeanCoupons(lastMonthBalanceRecord.getDouble("bean_coupons"));
			}
			monthBalance.setRechargeAmount(ramount);
			monthBalance.setRechargeBeanCoupons(rbeanCoupons);
			monthBalance.setRechargeIntegrals(rintegrals);
			monthBalance.setRechargePoints(rpoints);
			monthBalance.setRechargeTimes(rtimes);
			monthBalance.setConsumeTimes(ctimes);
			monthBalance.setConsumeAmount(camount);
			monthBalance.setConsumePoints(cpoints);
			monthBalance.setConsumeIntegrals(cintegrals);
			monthBalance.setConsumeBeanCoupons(cbeanCoupons);
			monthBalanceList.add(monthBalance);
		}
		//Db.batchSave(monthBalanceList,monthBalanceList.size());
		pageNumber++;
		if(pageNumber<=cardPage.getTotalPage()){
			newMonthBalance2(pageNumber,pageSize,endDate,startDate,list);
		}

	}

	@Inject
	private FinaMembershipCardService finaMembershipCardService;

	public void newMonthBalance3(int pageNumber,int pageSize){

		String cardSql=" from fina_membership_card a left join main_membership_card_type b on a.card_type_id=b.id " +
				"left join main_card_deduct_scheme c on c.id=a.deduct_scheme_id left join main_card_deduct_scheme d on d.id=a.long_deduct_scheme_id " +
				"where a.del_flag='0'   ";
		Page<Record> cardPage= Db.paginate(pageNumber,pageSize," select a.id,a.card_number,b.is_integral,c.deduct_way,d.deduct_way as long_deduct_way  ",cardSql);
		if(cardPage.getList()==null || cardPage.getList().size()==0){
			return;
		}
		String giveIntegralSql="select sum(integral_value) from fina_card_integral_record where card_id=? and transaction_type='in' and business_type='system_give' " +
				"and give_date>? and give_date<=?  and del_flag='0' ";
		for (Record record : cardPage.getList()) {
			List<FinaCardTransactions> cardTransactions=finaCardTransactionsService.getFinaCardTransactions(record.getStr("id"));
			String deductWay="";
			if(StrKit.notBlank(record.getStr("deduct_way"))){
				deductWay=record.getStr("deduct_way");
			}else{
				deductWay=record.getStr("long_deduct_way");
			}
			if(StrKit.isBlank(deductWay)){
				continue;
			}
			boolean isIntegralCard="1".equals(record.getStr("is_integral"));
			for (int i = 0; i < cardTransactions.size(); i++) {
				FinaCardTransactions finaCardTransaction=cardTransactions.get(i);
				if(deductWay.equals("deduct_by_money")){
					if(finaCardTransaction.getAmountSnapshot()==null){
						logger.info(record.getStr("card_number")+"余额"+DateUtils.formatDate(finaCardTransaction.getDealTime(),"yyyy-MM-dd HH:mm:ss"));
						if(i==0){
							FinaMembershipCard card =finaMembershipCardService.findById(record.getStr("id"));
							finaCardTransaction.setAmountSnapshot(card.getBalance());
						}else{
							FinaCardTransactions lastFinaCardTransaction=cardTransactions.get(i-1);
							if("2".equals(lastFinaCardTransaction.getInOutFlag())){
								//消费
								if(lastFinaCardTransaction.getAmount()==null){
									lastFinaCardTransaction.setAmount(0.0);
								}
								finaCardTransaction.setAmountSnapshot(lastFinaCardTransaction.getAmountSnapshot()+lastFinaCardTransaction.getAmount());
							}else{
								//充值
								if(lastFinaCardTransaction.getAmount()==null){
									lastFinaCardTransaction.setAmount(0.0);
								}
								finaCardTransaction.setAmountSnapshot(lastFinaCardTransaction.getAmountSnapshot()-lastFinaCardTransaction.getAmount());
							}
						}
						finaCardTransaction.update();
					}
				}else if(deductWay.equals("deduct_by_days")){
					if(finaCardTransaction.getTimesSnapshot()==null){
						logger.info(record.getStr("card_number")+"天数"+DateUtils.formatDate(finaCardTransaction.getDealTime(),"yyyy-MM-dd HH:mm:ss"));
						if(i==0){
							FinaMembershipCard card =finaMembershipCardService.findById(record.getStr("id"));
							finaCardTransaction.setTimesSnapshot(card.getConsumeTimes());
						}else{
							FinaCardTransactions lastFinaCardTransaction=cardTransactions.get(i-1);
							if("2".equals(lastFinaCardTransaction.getInOutFlag())){
								//消费
								if(lastFinaCardTransaction.getTimes()==null){
									lastFinaCardTransaction.setTimes(0.0);
								}
								finaCardTransaction.setTimesSnapshot(lastFinaCardTransaction.getTimesSnapshot()+lastFinaCardTransaction.getTimes());
							}else{
								//充值
								if(lastFinaCardTransaction.getTimes()==null){
									lastFinaCardTransaction.setTimes(0.0);
								}
								finaCardTransaction.setTimesSnapshot(lastFinaCardTransaction.getTimesSnapshot()-lastFinaCardTransaction.getTimes());
							}
						}
						finaCardTransaction.update();
					}
					if(isIntegralCard){
						if(finaCardTransaction.getIntegralsSnapshot()==null){
							logger.info(record.getStr("card_number")+"积分"+DateUtils.formatDate(finaCardTransaction.getDealTime(),"yyyy-MM-dd HH:mm:ss"));
							if(i==0){
								FinaMembershipCard card =finaMembershipCardService.findById(record.getStr("id"));

								Double cardIntegrals=card.getCardIntegrals();

								//减去赠送的
								Double giveIntegrals=Db.queryDouble(giveIntegralSql,record.getStr("id")
										,DateUtils.formatDate(DateUtils.getNextDay(finaCardTransaction.getDealTime(),-1),"yyyy-MM-dd")
										,DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
								if(giveIntegrals==null){
									giveIntegrals=0.0;
								}

								finaCardTransaction.setIntegralsSnapshot(cardIntegrals-giveIntegrals);
							}else{
								FinaCardTransactions lastFinaCardTransaction=cardTransactions.get(i-1);
								if("2".equals(lastFinaCardTransaction.getInOutFlag())){
									//消费
									if(lastFinaCardTransaction.getIntegrals()==null){
										lastFinaCardTransaction.setIntegrals(0.0);
									}
									//减去赠送的
									Double giveIntegrals=Db.queryDouble(giveIntegralSql,record.getStr("id")
											,DateUtils.formatDate(DateUtils.getNextDay(finaCardTransaction.getDealTime(),-1),"yyyy-MM-dd")
											,DateUtils.formatDate(DateUtils.getNextDay(lastFinaCardTransaction.getDealTime(),-1),"yyyy-MM-dd"));
									if(giveIntegrals==null){
										giveIntegrals=0.0;
									}

									finaCardTransaction.setIntegralsSnapshot(lastFinaCardTransaction.getIntegralsSnapshot()+lastFinaCardTransaction.getIntegrals()-giveIntegrals);
								}else{
									//充值
									if(lastFinaCardTransaction.getIntegrals()==null){
										lastFinaCardTransaction.setIntegrals(0.0);
									}
									//减去赠送的
									Double giveIntegrals=Db.queryDouble(giveIntegralSql,record.getStr("id")
											,DateUtils.formatDate(DateUtils.getNextDay(finaCardTransaction.getDealTime(),-1),"yyyy-MM-dd")
											,DateUtils.formatDate(DateUtils.getNextDay(lastFinaCardTransaction.getDealTime(),-1),"yyyy-MM-dd"));
									if(giveIntegrals==null){
										giveIntegrals=0.0;
									}
									finaCardTransaction.setIntegralsSnapshot(lastFinaCardTransaction.getIntegralsSnapshot()-lastFinaCardTransaction.getIntegrals()-giveIntegrals);
								}
							}
							finaCardTransaction.update();
						}
					}
				}
			}
		}

		pageNumber++;
		if(pageNumber<=cardPage.getTotalPage()){
			newMonthBalance3(pageNumber,pageSize);
		}

	}


	/*public static void main(String[] args) {
		String[] array=new String[]{

		*//*"2019-05",
						"2019-06",
						"2019-07","2019-08","2019-09",
						"2019-10","2019-11","2019-12",
						"2020-01","2020-02","2020-03","2020-04","2020-05","2020-06","2020-07","2020-08","2020-09","2020-10","2020-11","2020-12",
						"2021-01","2021-02",
						"2021-03",
						 "2021-04",
						"2021-05",
						"2021-06",
						"2021-07",
						"2021-08","2021-09","2021-10","2021-11","2021-12",
						"2022-01",
						"2022-02","2022-03","2022-04","2022-05",
						"2022-06","2022-07","2022-08","2022-09","2022-10",
						"2022-11","2022-12",
						"2023-01","2023-02","2023-03","2023-04"*//*
				"2023-05"
		};
		for (String s : array) {
			String startDate=s+"-01";
			String endDate=DateUtils.getMaxMonthDate(startDate,"yyyy-MM-dd");
			long time=new Date().getTime();
			String startTime=startDate+"%2000:00:00";
			String endTime=endDate+"%2023:59:59";
			System.out.println(startTime);
			System.out.println(endTime);
			String str= HttpClientsUtils.get("https://oss.cncsgroup.com/api/test3?startDate="+startTime+"&endDate="+endTime);
			//String str= HttpClientsUtils.get("http://10.10.0.12:8885/api/test3?startDate="+startTime+"&endDate="+endTime);
			//System.out.println("https://oss.cncsgroup.com/api/test3?startDate="+startTime+"&endDate="+endTime);
			if(!str.startsWith("{") && !str.endsWith("}") ){
				try {
					//Thread.sleep();
					System.out.println(1);
					Thread.sleep((1000*60*1));
				}catch (Exception e){
					e.printStackTrace();
				}
			}
			System.out.println(str);
			System.out.println((new Date().getTime()-time)/1000);
		}

		//String str= HttpClientsUtils.get("http://10.10.0.12:8885/api/test3?startDate=2019-12-01%2000:00:00&endDate="+"2019-12-31%2023:59:59");
		//System.out.println(str);

	}

*/

	public void test4() throws Exception {
		UploadFile file = getFile("file");
		XSSFWorkbook wk = new XSSFWorkbook(new FileInputStream(file.getFile()));
		XSSFSheet sheet = wk.getSheetAt(0);
		System.out.println(sheet.getLastRowNum());
		if(file.getFile().exists()){
			file.getFile().delete();
		}
		renderSuccess("success","");
	}
	/*public static void main(String[] args) throws Exception {

		FileInputStream inputStream = new FileInputStream(
				new File("D:\\soft\\softFile\\weixin\\WeChat Files\\wxid_ubyus9oj0pat22\\FileStorage\\File\\2023-05\\0524.xlsx"));
		XSSFWorkbook wk = new XSSFWorkbook(inputStream);
		XSSFSheet sheet = wk.getSheetAt(0);

		List<Map<String,Object>> data=new ArrayList<>();
		//获取时间范围
		String dateStr=sheet.getRow(3).getCell(0).getStringCellValue();
		String startTime=dateStr.substring(0,19);
		String endTime=dateStr.substring(20);

		List<Date> dateList=DateUtils.getBetweenDates(DateUtils.parseDate(startTime),DateUtils.parseDate(endTime));


		List<String> titles=new ArrayList<>();

		//String[] titles=new String[]{};


		//获取表头数据
		for (int i=0;i<sheet.getRow(4).getLastCellNum();i++){
			String value=sheet.getRow(4).getCell(i).getStringCellValue();
			if("*".equals(value)){
				break;
			}
			if(value==null || "".equals(value)){
				value="类型";
			}
			if(i>3){
				for (Date date : dateList) {
					titles.add(DateUtils.formatDate(date,"yyyy-MM-dd"));
				}
				break;
			}

			titles.add(value);
		}

		System.out.println(JSON.toJSONString(titles));

		for (int i = 0; i < sheet.getLastRowNum(); i++) {
			//第7行读取数据
			if(i<7){
				continue;
			}
			XSSFRow row = sheet.getRow(i);
			if(row==null){
				continue;
			}
			Map<String,Object> map=new HashMap<>();

			for (int j = 0; j < row.getLastCellNum(); j++) {
				XSSFCell cell=row.getCell(j);
				if(cell==null){
					continue;
				}
				if(j>titles.size()-1){
					break;
				}
				String value=cell.getStringCellValue();
				map.put(titles.get(j),value);
			}
			data.add(map);
		}


		List<Map<String,Object>> data2=new ArrayList<>();
		for (int i = 0; i < data.size(); i++) {
			Map<String,Object> map=data.get(i);
			String name=(String)map.get("姓名");
			String type=(String)map.get("类型");

			if(StrKit.notBlank(name)){
				Map<String,Object> map2=data.get(i+1);
				data2.add(map);

				//map2.put("姓名",name);
				data2.add(map2);
			}
		}

		System.out.println(JSON.toJSONString(data2));


		// 关闭读取的文件
		wk.close();
		inputStream.close();

	}*/

	private static CellRangeAddress getMergedRegion(Sheet sheet, int rowNum, int colNum) {
		for (int i = 0; i < sheet.getNumMergedRegions(); i++) {
			CellRangeAddress merged = sheet.getMergedRegion(i);
			if (merged.isInRange(rowNum, colNum)) {
				return merged;
			}
		}
		return null;
	}

	/*public static void main(String[] args) {
		//A4318265-1D8B-4ACC-96E1-AA742CA5473B
		//D1253CB8-AFFB-4F5D-B417-890A126290F9
		//D5E33546-882F-42B6-AE8E-E44D9615F31E
		//EED65288-C7E3-4718-B3AF-D3DC84831A03
	}*/

	public static void main(String[] args) {
		//String str2= HttpClientsUtils.get("https://oss.cncsgroup.com/api/genDays?id="+"EED65288-C7E3-4718-B3AF-D3DC84831A03"+"&summaryDate=2023-05-14");
		//System.out.println(str2);
		String str="[\"0187EC27-FB5E-4841-A9C4-02DA2417D862\",\n" +
				"\"02F2BEB9-BC8A-4198-B3F0-7DF4426A7E46\",\n" +
				"\"038E554A-D60B-48C0-8FAD-3445B2FE734C\",\n" +
				"\"05C65E4C-2366-438F-81E0-9B56BA1AF6FD\",\n" +
				"\"07C61FD8-245A-4A0B-9CAF-04356BB61416\",\n" +
				"\"083B8207-D3B9-437B-BCD3-1AF19240C338\",\n" +
				"\"0F296572-E74F-4648-B708-8AB5E2AA8AC3\",\n" +
				"\"103220CF-F92C-4801-A886-7DC2470FDF52\",\n" +
				"\"125BC63F-3A8B-45BB-80BF-5D7765EABD0C\",\n" +
				"\"126CD591-E682-4D52-A3D3-1BA868B691D5\",\n" +
				"\"17BE5A86-E775-40DE-9402-59993EC5901A\",\n" +
				"\"183ECB0C-C3BE-4C0A-89BA-81D80268EE67\",\n" +
				"\"195E19FB-4B8A-4C2F-824D-53D2AC25FA65\",\n" +
				"\"1AE66DFC-716D-4239-9AB5-6AAA044EA80E\",\n" +
				"\"1EF8499E-EEBE-4BCA-86ED-32B2180C2ACA\",\n" +
				"\"21A4D949-57F1-442B-8183-5E9DC0BB2158\",\n" +
				"\"2441F5BF-37FD-4B45-ADD6-B1D07225FD17\",\n" +
				"\"25453515-F9E0-404B-B8C6-82F13C65B8D5\",\n" +
				"\"2570FA51-0423-46D8-A8B1-F1FB84CC3FB9\",\n" +
				"\"275D7F0E-A9B9-4151-9300-31B4814B19FB\",\n" +
				"\"287D0D5A-7561-499F-9366-619178485AF7\",\n" +
				"\"29EAA35E-9DF5-4573-AD11-8E302D7E6449\",\n" +
				"\"29F6CFBF-000D-4771-A1FD-68C7C163F14E\",\n" +
				"\"2BC5B01F-7569-44DC-9903-A15B34C05AC9\",\n" +
				"\"2C73B723-02F0-45F8-A7C2-C54195D4636D\",\n" +
				"\"315F104F-E722-4F71-81F4-545A197C7D58\",\n" +
				"\"319F4B40-BA01-4F97-BE22-5B4C6A5B073F\",\n" +
				"\"34E63AC5-0A6D-4C6A-9DB0-18143F19D8A3\",\n" +
				"\"353AFAE3-D3F7-4594-A5B8-D6B4C638BD6B\",\n" +
				"\"36635597-E9ED-4437-A900-DE160C44AB6F\",\n" +
				"\"3BAFF5DE-E033-40D3-9717-98D17FCECE4E\",\n" +
				"\"3E42F521-12BF-4256-BBF8-41781884A57F\",\n" +
				"\"416536CA-A432-4E18-B1F2-6E89F0CB2A4D\",\n" +
				"\"43E57FB4-6EEB-40E2-A5F0-5C906ED10ECE\",\n" +
				"\"43E7455E-19C4-423B-B936-934AF87AE440\",\n" +
				"\"45B7F3A1-8B9D-4EA9-804F-90245C272640\",\n" +
				"\"472E872F-D85E-46E4-BB43-5FED1301E38A\",\n" +
				"\"48FB226F-98EB-43DF-B36A-B367D95BCFCF\",\n" +
				"\"4DAD5E9E-1A5A-4361-8F55-9E9764CB687E\",\n" +
				"\"4EF3B74D-C6F1-40FC-A7D9-32F5A266F459\",\n" +
				"\"50A058E8-8737-4813-BA7E-30F3708F28B6\",\n" +
				"\"51139607-8175-4AC5-9F89-2230EFBAD17B\",\n" +
				"\"52BB927F-58CF-4819-A25E-9A8CC81C56C0\",\n" +
				"\"52EAC377-4448-4CE1-AEF6-8B24D49513FE\",\n" +
				"\"549B55C1-47BB-49DD-9D87-0B0255B2C7F1\",\n" +
				"\"55C06CF7-4150-47CE-92FD-264691929A85\",\n" +
				"\"5761F369-3E7D-4B42-838C-3BD5643E2577\",\n" +
				"\"5AA019D9-491A-446A-B5B4-AB0DF6076D01\",\n" +
				"\"5AA32F3E-C38C-45B0-B8CE-2E11EE5A7DE6\",\n" +
				"\"5EBC8A2E-1413-4654-B359-E668F31EB231\",\n" +
				"\"6148B260-CEDC-40D1-BD57-8FB521C86B99\",\n" +
				"\"614C2444-F838-420B-AC1D-083CBE17F062\",\n" +
				"\"62C5B863-97A0-4A5E-942B-12AEC47C948E\",\n" +
				"\"6588200F-DF44-487C-B7FC-C02C80A15B83\",\n" +
				"\"6762C7DF-979A-4D97-898C-4763EDDDACD8\",\n" +
				"\"68DD1BBC-7545-4F32-91EB-A07F9186765C\",\n" +
				"\"6E40FE23-2945-4BC8-B4DE-5DECF0C33849\",\n" +
				"\"70587E3C-927E-4CF6-BC83-6792D298EF28\",\n" +
				"\"71F945FD-B2B8-4E78-AC68-BC0278F03399\",\n" +
				"\"75370FEA-A397-4161-8D62-6376D068E333\",\n" +
				"\"799BF46A-BE04-4D20-8E5F-8A12A3CD4EE8\",\n" +
				"\"7A7B637B-D385-44F4-A6CF-1626C1999DA0\",\n" +
				"\"805F2CCA-4CF5-42FF-906C-15F2106F9C62\",\n" +
				"\"81DC734D-5FD7-4CB1-83CB-75FA062B11E0\",\n" +
				"\"82E69746-5DBB-4A56-BA76-2031A7E24B2E\",\n" +
				"\"840241AB-C250-4B50-A851-D40C76F36728\",\n" +
				"\"8493DDE7-4397-4431-B0CC-BE10776EEC9E\",\n" +
				"\"852C8A16-A490-4823-87C5-52F112E75D18\",\n" +
				"\"854AFF20-5A77-44A6-B710-652B165908B8\",\n" +
				"\"856D5D95-B83F-4E11-8FEF-A695A7628919\",\n" +
				"\"8761B49A-A24C-43A7-BEA0-6501306A55FD\",\n" +
				"\"8BD40F79-C496-4C25-9F89-C37D83094C8F\",\n" +
				"\"8D4DE63D-2034-4B91-98E5-0C34352D1CD4\",\n" +
				"\"8E34ED75-BDB9-4425-B329-012FA8D61086\",\n" +
				"\"8E415542-7AA3-441D-BE29-E053A7B7C8DC\",\n" +
				"\"927BE366-BAC1-42F9-8EA4-2102C363FB2B\",\n" +
				"\"95705B90-3950-4924-9839-07D59F8A7333\",\n" +
				"\"95FBF8D3-C30E-4A27-BFEE-E299574C6836\",\n" +
				"\"96A59AE5-BF2F-4F14-8BC6-FD8A8C98A30E\",\n" +
				"\"97C71542-ECA0-4914-BEE3-7F6120E1E4F6\",\n" +
				"\"9984F4C2-F362-4F2F-8EF4-97B257D5F5AD\",\n" +
				"\"9A36734F-BC9A-4DC7-B0AC-5C07E19614CD\",\n" +
				"\"9DA462C2-49E7-490C-9BFB-765433BBDC65\",\n" +
				"\"A0A90C16-B2F0-46E0-8C07-5568E186CD50\",\n" +
				"\"A55A758A-CF96-4988-84ED-BD1E1BCE94F2\",\n" +
				"\"A8609D13-2BFF-434B-B00D-773EB0513269\",\n" +
				"\"A9211F96-E26E-4849-A29A-614DEC7C7124\",\n" +
				"\"AC17E14B-8DAA-4C61-B5AB-72A1055F1BA5\",\n" +
				"\"AD5374A5-3788-4056-A65B-1F2A64CB7541\",\n" +
				"\"AEB31D13-6FDE-464F-8210-A720F02F3578\",\n" +
				"\"B0B193B8-147B-412F-8F2B-BC04F03F5138\",\n" +
				"\"B1F31A5B-52B5-4A19-90FC-1B99CD5D1415\",\n" +
				"\"B324F04E-93FA-4089-8D12-099415B4B66B\",\n" +
				"\"B4E307EA-5917-44AC-A154-90F8898EA846\",\n" +
				"\"B8580400-9893-4E8C-9B84-334044397D17\",\n" +
				"\"BCA94D2C-654E-45C6-89D6-C9C972A36611\",\n" +
				"\"BDA8F4E8-6C45-480A-AB7E-068FC757956A\",\n" +
				"\"BDA9395A-EFB7-4E01-949E-2D2CDC423916\",\n" +
				"\"BEA740A7-4504-40B0-A0EF-0A63B4235735\",\n" +
				"\"C155B506-CE30-4BF3-8525-C74D3F25420C\",\n" +
				"\"c1afc6ce-d535-11ea-9d15-00163e08de15\",\n" +
				"\"C3E4DD77-6645-4FCF-A10F-7A3A3DA19CD9\",\n" +
				"\"C8EE8EB6-5D9D-49D8-80B0-E6448074093C\",\n" +
				"\"CFE36AC0-AF4F-4BD2-9D28-2328A73026A6\",\n" +
				"\"D036EBE8-6C24-4968-BE39-CF2FA69B267A\",\n" +
				"\"D2FDB6CC-08DE-4BB3-9648-7F97A7C7442D\",\n" +
				"\"D6669EC9-C8AE-4F4F-A19E-58A33CBC2B1C\",\n" +
				"\"D68919F2-392F-4727-AFA2-F8365592DC25\",\n" +
				"\"D7793C09-922A-4C85-AE2D-91BBD8A5EBD6\",\n" +
				"\"D7B3E07D-32E8-4555-802B-2DC96FCAE715\",\n" +
				"\"DAA8E3AF-3015-4154-A82F-CB1C924E349B\",\n" +
				"\"E4440626-C6B7-44B3-8398-C42939189749\",\n" +
				"\"E51B5043-261C-464C-8E39-DF51FF4E8CD8\",\n" +
				"\"E8CA9EF3-9154-4B11-A7D4-5D3EE39C8673\",\n" +
				"\"E921A22B-3CA7-4FF7-9E39-8B446BB94939\",\n" +
				"\"E96E7DED-820C-4132-82E6-ED18F53D31D7\",\n" +
				"\"EA1754C1-B943-4B3C-9A53-6C43D0EF2175\",\n" +
				"\"EBB844DA-3C40-4511-938E-4DDC402C35BD\",\n" +
				"\"EF3802C2-3731-490D-8B2D-3AAB7ABA9FB8\",\n" +
				"\"EF95CE00-97BC-44FD-A75A-CB548360CF2D\",\n" +
				"\"F124A46C-8231-4034-AB70-657D28204474\",\n" +
				"\"F207ABE0-3681-4BEA-AD01-7E0B9FC91D0C\",\n" +
				"\"F57F296B-CA3C-46DF-A0D9-734BE8031C2B\",\n" +
				"\"F73BF0ED-B0B2-467C-A0EC-A8122F11DA84\",\n" +
				"\"F9A4205D-8CE9-40EF-884D-26E869C1E100\",\n" +
				"\"FC46568D-5CF6-4F82-8431-DC3879083EFE\",\n" +
				"\"FE8695A7-7649-4508-860D-3083ECE5D88D\",\n" +
				"\"FFC27B72-5505-48F3-90C6-9AF0C1C9D94D\"]";
		JSONArray jsonArray=JSON.parseArray(str);
		String companId="292D33ED-6B2E-44FD-8BCC-5F489EB00748";
		for (int i = 0; i < jsonArray.size(); i++) {
			String str2= HttpClientsUtils.get("https://oss.cncsgroup.com/api/socialSecuritySave?empId="+jsonArray.getString(i)+"&yearMonth=2023-06&companyId="+companId);
			System.out.println(jsonArray.getString(i)+" "+str2);
		}

	}

	@Inject
	private PersOrgEmployeeProvidentFundService persOrgEmployeeProvidentFundService;
	@Inject
	private PersOrgEmployeeService persOrgEmployeeService;
	@Inject
	private PersPositionService persPositionService;
	@Inject
	private PersOrgEmployeeSocialSecurityService persOrgEmployeeSocialSecurityService;

	public void providentFundSave(){
		String empId=getPara("empId");
		String yearMonth=getPara("yearMonth");
		String companyId=getPara("companyId");

		PersOrgEmployeeProvidentFund employeeMaxProvidentFund=persOrgEmployeeProvidentFundService.findEmployeeProvidentFundMaxMonth(empId);

		PersOrgEmployee employee=persOrgEmployeeService.findById(empId);

		List<String> deptIds=new ArrayList<>();

		String empDeptId=null;
		List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
		for (String e : empDeptIds) {
			if(deptIds.contains(empDeptId)){
				empDeptId =e;
				break;
			}
		}
		if(empDeptId==null){
			empDeptId=empDeptIds.get(0);
		}
		String empPositionId=null;
		List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
		for (String e : empPositionIds) {
			PersPosition persPosition=persPositionService.findById(e);
			if(empDeptId.equals(persPosition.getOrgId())){
				empPositionId=e;
				break;
			}
		}
		PersOrgEmployeeProvidentFund employeeProvidentFund=new PersOrgEmployeeProvidentFund();
		employeeProvidentFund.setId(IdGen.getUUID());
		employeeProvidentFund.setApplyRecordId(null);
		employeeProvidentFund.setIdcard(employee.getIdCard());
		employeeProvidentFund.setDeptId(empDeptId);
		employeeProvidentFund.setPositionId(empPositionId);
		employeeProvidentFund.setEmpId(employee.getId());
		employeeProvidentFund.setType("2");
		employeeProvidentFund.setCompanyId(companyId);
		if(employeeMaxProvidentFund!=null){
			employeeProvidentFund.setPayRatio(employeeMaxProvidentFund.getPayRatio());
		}else{
			employeeProvidentFund.setPayRatio(PayRatioType.five.getKey());
		}
		employeeProvidentFund.setMaritalStatus(employee.getMaritalStatus());
		employeeProvidentFund.setYearMonth(yearMonth);
		employeeProvidentFund.setPhoneNum(employee.getPhoneNum());
		employeeProvidentFund.setDelFlag("0");
		employeeProvidentFund.setCreateBy(null);
		employeeProvidentFund.setCreateDate(new Date());
		employeeProvidentFund.setUpdateBy(null);
		employeeProvidentFund.setUpdateDate(new Date());
		if(employeeMaxProvidentFund!=null && employeeMaxProvidentFund.getYearMonth().equals(employeeProvidentFund.getYearMonth())){
			renderCodeFailed("已存在");
			return;
		}
		if(employeeProvidentFund.save()){
			renderCodeSuccess("success");
		}else{
			renderCodeFailed();
		}
	}

	public void socialSecuritySave(){
		String empId=getPara("empId");
		String yearMonth=getPara("yearMonth");
		String companyId=getPara("companyId");

		PersOrgEmployeeSocialSecurity socialSecurityMaxMonth=persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityMaxMonth(empId);

		PersOrgEmployee employee=persOrgEmployeeService.findById(empId);

		List<String> deptIds=new ArrayList<>();

		String empDeptId=null;
		List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
		for (String e : empDeptIds) {
			if(deptIds.contains(empDeptId)){
				empDeptId =e;
				break;
			}
		}
		if(empDeptId==null){
			empDeptId=empDeptIds.get(0);
		}
		String empPositionId=null;
		List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
		for (String e : empPositionIds) {
			PersPosition persPosition=persPositionService.findById(e);
			if(empDeptId.equals(persPosition.getOrgId())){
				empPositionId=e;
				break;
			}
		}
		PersOrgEmployeeSocialSecurity socialSecurity=new PersOrgEmployeeSocialSecurity();
		socialSecurity.setId(IdGen.getUUID());
		socialSecurity.setApplyRecordId(null);
		socialSecurity.setIdcard(employee.getIdCard());
		socialSecurity.setDeptId(empDeptId);
		socialSecurity.setPositionId(empPositionId);
		socialSecurity.setEmpId(empId);
		socialSecurity.setType("2");
		socialSecurity.setIdcardAddr(employee.getIdCardAddr());
		socialSecurity.setCompanyId(companyId);
		socialSecurity.setIsFirst("0");
		socialSecurity.setYearMonth(yearMonth);
		socialSecurity.setPhoneNum(employee.getPhoneNum());
		socialSecurity.setDelFlag("0");
		socialSecurity.setCreateBy(null);
		socialSecurity.setCreateDate(new Date());
		socialSecurity.setUpdateBy(null);
		socialSecurity.setUpdateDate(new Date());

		if(socialSecurityMaxMonth!=null && socialSecurityMaxMonth.getYearMonth().equals(socialSecurity.getYearMonth())){
			renderCodeFailed("已存在");
			return;
		}

		if(socialSecurity.save()){
			renderCodeSuccess("success");
		}else{
			renderCodeFailed();
		}

	}
}
