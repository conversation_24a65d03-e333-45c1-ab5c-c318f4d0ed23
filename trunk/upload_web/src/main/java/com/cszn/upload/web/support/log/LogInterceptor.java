package com.cszn.upload.web.support.log;

import com.cszn.integrated.base.web.base.BaseController;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 系统日志拦截器
 * <AUTHOR>
 *
 */
public class LogInterceptor implements Interceptor {

	private static final Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

	@Override
	public void intercept(Invocation inv) {
		if (inv.getController() instanceof BaseController) {
			BaseController c = (BaseController) inv.getController();
			Map<String, String[]> paraMap = c.getParaMap();
			logger.debug("url         : " + c.getRequest().getMethod() + " " + c.getRequest().getRequestURI());
			String parameters = "Parameter   : ";
			if (paraMap != null && paraMap.size() > 0) {
				for(Map.Entry<String, String[]> entry : paraMap.entrySet()){
				    String mapKey = entry.getKey();
				    parameters += mapKey+":"+c.getPara(mapKey) + "  ";
				}
			}
			logger.debug(parameters);
			
//			log.setUid(user.getId());
//			log.setBrowser(browser.getName());
//			log.setOperation(c.getRequest().getMethod());
//			log.setFrom(c.getReferer());
//			log.setIp(c.getIPAddress());
//			log.setUrl(c.getRequest().getRequestURI());
//			log.setCreateDate(new Date());
//			log.setLastUpdAcct(user.getId() == null ? "guest" : user.getName());
//			log.setLastUpdTime(new Date());
//			log.setNote("记录日志");

			try {
//				LogService logService = Jboot.service(LogService.class);
//				logService.save(log);
			} catch (Exception e) {
				logger.error(e.getMessage());
				e.printStackTrace();
			} finally {
				inv.invoke();
			}
		} else {
			inv.invoke();
		}
	}

}
