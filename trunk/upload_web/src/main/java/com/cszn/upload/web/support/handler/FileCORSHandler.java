package com.cszn.upload.web.support.handler;

import com.jfinal.handler.Handler;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class FileCORSHandler extends Handler {

    public FileCORSHandler() {
        super();
    }

    @Override
    public void handle(String target, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, boolean[] booleans) {
        if (target.indexOf('.') != -1) {
            String requestHeaders = httpServletResponse.getHeader("Access-Control-Request-Headers");
            if (StringUtils.isEmpty(requestHeaders)) {
                requestHeaders = "";
            }
            httpServletResponse.setHeader("Access-Control-Allow-Origin", "*");
            httpServletResponse.setHeader("Access-Control-Allow-Credentials", "true");
            httpServletResponse.setHeader("Access-Control-Allow-Methods", "HEAD,PUT,DELETE,POST,GET");
            httpServletResponse.setHeader("Access-Control-Allow-Headers", "Accept, Origin, XRequestedWith, Content-Type, LastModified," + requestHeaders);
        }
        this.next.handle(target, httpServletRequest, httpServletResponse, booleans);
    }
}
