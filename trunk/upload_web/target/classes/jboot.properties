#---------------------------------------------------------------------------------#
# app info
jboot.admin.app.name=\u6587\u4ef6\u4e0a\u4f20\u7ba1\u7406\u7cfb\u7edf
jboot.admin.app.org=cszn
jboot.admin.app.orgWebsite=www.cszn.com
jboot.admin.app.resourceHost
jboot.admin.app.copyRight=2019 \u00a9 copyright \u7ca4ICP\u590712345678\u53f7
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot\u7684\u5f00\u53d1\u6a21\u5f0f
jboot.mode=dev
jboot.bannerEnable=true
jboot.bannerFile=banner.txt
jboot.cron4jEnable=false
jboot.cron4jFile=cron4j.properties
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#type = local (support:local,motan,dubbo)
#use local
jboot.rpc.type = local
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# mysql config
jboot.datasource.type=mysql
jboot.datasource.url=****************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
jboot.datasource.maximumPoolSize = 5
jboot.datasource.sqlTemplatePath=
jboot.datasource.sqlTemplate=
jboot.datasource.table=
jboot.datasource.excludeTable=
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
jboot.model.idCacheEnable=false
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# cache config : type default ehcache (support:ehcache,redis,ehredis)
#jboot.cache.type=redis
#jboot.cache.redis.host=127.0.0.1
#jboot.cache.redis.password=123456
#jboot.cache.redis.database=0
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# jwt config
jboot.web.jwt.httpHeaderName=jwttoken
jboot.web.jwt.secret=21934e3ccf3c19a6c3299cbc2e33b2d848e6ef9ea4e484b19b07bea82888a7a7
# 60 * 60 * 10
jboot.web.jwt.validityPeriod=36000
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# shiro config
jboot.shiro.ini = shiro.ini
jboot.shiro.loginUrl=/login
jboot.shiro.successUrl
jboot.shiro.unauthorizedUrl=/login
#---------------------------------------------------------------------------------#

#--------------------------------------\u4e0a\u4f20\u4e0b\u8f7d\u53c2\u6570begin---------------------------#
uploadPath=E:\\virtualDir\\filePath.war\\
uploadPath.linux=/mnt/virtualDir/filePath.war/
fileUrlPrefix=http://localhost:8885/