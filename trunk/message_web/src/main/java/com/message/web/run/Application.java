package com.message.web.run;

import io.jboot.Jboot;
import io.jboot.app.JbootApplication;
import io.jboot.components.mq.JbootmqMessageListener;

/**
 * 服务启动入口
 * <AUTHOR>
 *
 */
public class Application {
	
    public static void main(String [] args){
    	
    	JbootApplication.run(args);
        
        Jboot.getMq().addMessageListener(new JbootmqMessageListener(){
            @Override
            public void onMessage(String channel, Object obj) {
               System.out.println("receiveMsg====>>>"+obj);//获取到JSON格式消息可以做数据库操作
            }
        }, "userMsgChannel");
        
        Jboot.getMq().startListening();
        
        System.out.println("RabbitMQServerListen started.");
    }
}
