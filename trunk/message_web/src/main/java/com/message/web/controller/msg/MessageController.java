package com.message.web.controller.msg;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.msg.MsgMessageService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.msg.MsgMessage;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.Dict;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.message.web.support.auth.AuthUtils;
import com.message.web.support.log.LogInterceptor;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.Date;
import java.util.List;

@RequestMapping(value="/msg/message", viewPath="/modules_page/msg")
public class MessageController extends BaseController {

    @Inject
    private MsgMessageService msgMessageService;

    @Inject
    private DictService dictService;


    /**
     *跳转消息界面
     */
    public void index(){
        List<Dict> sourceSystems = dictService.getListByTypeOnUse("source_system");
        setAttr("sourceSystems",sourceSystems);
        render("messageIndex.html");
    }


    /**
     * 跳转消息列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MsgMessage msg = getBean(MsgMessage.class,"",true);
        Page<MsgMessage> page = msgMessageService.findListPage(getParaToInt("page"),getParaToInt("limit"),msg);
        renderJson(new DataTable<MsgMessage>(page));
    }



    /**
     * 跳转查看界面
     */
    public void form(){
        String id = getPara("id");
        MsgMessage msg = msgMessageService.get(id);
        setAttr("msg",msg);
        setAttr("commonUpload", Global.commonUpload);
        render("messageForm.html");
    }



    /**
     * 获取附件和查阅人
     */
    public void getFilesAndUsers(){
        String id = getPara("id");
        MsgMessage msg = msgMessageService.get(id);
        if(msg != null){
            //获取附件
            /*List<Record> fileList = Db.find("select id,relation_id as relationId,file_id as fileId,url,create_time as createTime " +
                    "from pers_notice_enclosure where relation_id = ?",msg.getId());*/
            List<Record> fileList = Lists.newArrayList();
            //获取查阅人
            List<Record> userList = Db.find("select mu.read_flag as readFlag,mu.read_time as readTime,u.name " +
                    "from msg_message_user mu left join sys_user u on mu.user_id = u.id where mu.msg_id = ?",msg.getId());
            renderJson(Ret.ok("msg", "获取成功").set("fileList",fileList).set("userList",userList));
        }else{
            renderJson(Ret.ok("msg", "获取失败"));
        }
    }



    /**
     * 发布
     */
    public void release(){
        String id = getPara("id");
        MsgMessage msg = msgMessageService.get(id);
        if(msg == null){ renderJson(Ret.fail("msg", "消息不存在，不可发布")); return; }
        msg.setMsgStatus("release");
        msg.setUpdateBy(AuthUtils.getUserId());
        msg.setUpdateTime(new Date());
        if(msg.update()){
            renderJson(Ret.ok("msg", "发布成功"));
        }else{
            renderJson(Ret.fail("msg", "发布失败"));
        }
    }


    /**
     * 撤回
     */
    public void withdraw() {
        String id = getPara("id");
        MsgMessage msg = msgMessageService.get(id);
        if (msg == null) {
            renderJson(Ret.fail("msg", "消息不存在，不可撤回"));
            return;
        }
        msg.setMsgStatus("draft");
        msg.setUpdateBy(AuthUtils.getUserId());
        msg.setUpdateTime(new Date());
        if (msg.update()) {
            renderJson(Ret.ok("msg", "撤回成功"));
        } else {
            renderJson(Ret.fail("msg", "撤回失败"));
        }
    }
}
