package com.message.web.controller.msgapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.msg.MsgMessageService;
import com.cszn.integrated.service.entity.msg.MsgMessage;
import com.cszn.integrated.service.entity.msg.MsgMessageUser;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.*;

@RequestMapping(value="/api")
public class ApiController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private MsgMessageService msgMessageService;




    /**
     * 发布消息
     */
    public void publishMessage(){
        Map<String, Object> result = new HashMap<String, Object>();

        MsgMessage msgMessage = getBean(MsgMessage.class,"",true);
        String receive = getPara("receive");

        logger.info("发布消息接收参数:[{}],[{}]", JSON.toJSONString(msgMessage),receive);

        List<MsgMessageUser> msgUserList = Lists.newArrayList();
        if(StringUtils.isBlank(msgMessage.getMsgType())){
            result.put("code", "10002");
            result.put("msg", "消息类型为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(msgMessage.getSourceSystem())){
            result.put("code", "10002");
            result.put("msg", "来源系统为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(msgMessage.getTitle())){
            result.put("code", "10002");
            result.put("msg", "消息标题为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(receive)){
            result.put("code", "10002");
            result.put("msg", "消息接收人参数为空");
            renderJson(result);
            return;
        }
        msgMessage.setId(IdGen.getUUID());
        msgMessage.setLabel(getPara("label"));
        msgMessage.setMsgStatus("release");
        msgMessage.setDelFlag("0");
        msgMessage.setCreateTime(new Date());
        msgMessage.setUpdateTime(new Date());

        Map<String,MsgMessageUser> replyMap = Maps.newHashMap();

        if(StringUtils.isNotBlank(receive)) {
            JSONObject json = JSONObject.parseObject(receive);
            List<String> deptIdsArr = JSONObject.parseArray(JSONObject.toJSONString(json.getJSONArray("deptIds")), String.class);
            List<String> userIdArr = JSONObject.parseArray(JSONObject.toJSONString(json.getJSONArray("userIds")), String.class);
            if (userIdArr != null && userIdArr.size() > 0) {
                for (String item : userIdArr) {
                    MsgMessageUser msgUser = new MsgMessageUser();
                    msgUser.setId(IdGen.getUUID());
                    msgUser.setMsgId(msgMessage.getId());
                    msgUser.setUserId(item);
                    msgUser.setReadFlag("0");
                    msgUser.setDelFlag("0");
                    msgUserList.add(msgUser);
                    replyMap.put(item,msgUser);
                }
            }
            if(deptIdsArr != null && deptIdsArr.size() > 0){
                for (String item : deptIdsArr) {
                    List<String> list = Db.query("select user_id from pers_emp_user where emp_id in(select id from pers_org_employee where org_cur_id = ?)",item);
                    if(list != null && list.size() > 0){
                        for (String it : list) {
                            MsgMessageUser msgUser = new MsgMessageUser();
                            msgUser.setId(IdGen.getUUID());
                            msgUser.setMsgId(msgMessage.getId());
                            msgUser.setUserId(item);
                            msgUser.setReadFlag("0");
                            msgUser.setDelFlag("0");
                            if(replyMap.containsKey(it)){
                               continue;
                            }
                            msgUserList.add(msgUser);
                            replyMap.put(it,msgUser);
                        }
                    }
                }
            }
        }

        logger.info("发布消息处理后参数:[{}]",JSON.toJSONString(msgUserList));

        boolean flag = Db.tx(new IAtom(){
            public boolean run() throws SQLException {
                try {
                    msgMessage.save();
                    if(msgUserList != null && msgUserList.size() > 0){
                        int[] ints = Db.batchSave(msgUserList, msgUserList.size());
                        for (int i : ints) {
                            if (i < 1) {
                                return false;
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
                return true;
            }
        });

        if (flag) {
            result.put("code", "0");
            result.put("msg", "success");
        } else {
            result.put("code", "10001");
            result.put("msg", "fail");
        }
        renderJson(result);
    }




    /**
     * 获取消息
     */
    public void getMessage(){
        Map<String, Object> result = new HashMap<String, Object>();

        String userId = getPara("userId");
        String label = getPara("label");
        String readFlag = getPara("readFlag");
        Integer pagenNum = getParaToInt("pageNum");
        Integer pageSize = getParaToInt("pageSize");

        logger.info("获取消息接收参数:用户ID:[{}],标签:[{}],是否已读:[{}],页码:[{}],页容量:[{}]",userId,label,readFlag,pagenNum,pageSize);

        if(StringUtils.isBlank(userId)){
            result.put("code", "10002");
            result.put("msg", "参数未传入");
            renderJson(result);
            return;
        }

        if(pagenNum == null || pageSize == null){
            result.put("code", "10002");
            result.put("msg", "页码或页容量不存在");
            renderJson(result);
            return;
        }
        MsgMessageUser msgUser = new MsgMessageUser();
        msgUser.setUserId(userId);
        msgUser.setReadFlag(readFlag);
        Page<Record> page = msgMessageService.findPage(pagenNum,pageSize,msgUser,label);
        result.put("code", "0");
        result.put("msg", "获取成功");
        if(page != null) {
            result.put("data", page.getList());
            result.put("pageNumber", page.getPageNumber());
            result.put("pageSize", page.getPageSize());
            result.put("totalPage", page.getTotalPage());
            result.put("totalRow", page.getTotalRow());
        }else {
            result.put("data", new ArrayList<>());
            result.put("pageNumber", 1);
            result.put("pageSize", pageSize);
            result.put("totalPage", 0);
        }
        renderJson(result);
    }




    /**
     * 获取用户消息数量(未读/已读/全部)
     */
    public void getMsgCount(){
        Map<String, Object> result = new HashMap<String, Object>();

        String userId = getPara("userId");
        logger.info("获取用户消息数量接收参数:[{}]",userId);

        if(StringUtils.isBlank(userId)){
            result.put("code", "10002");
            result.put("msg", "参数未传入");
            renderJson(result);
            return;
        }

        Long notReadCount = Db.queryLong("select count(*) from msg_message_user u left join msg_message m on u.msg_id = m.id " +
                "where m.del_flag = 0 and msg_status = 'release' and u.read_flag = 0 and u.user_id = ?",userId);
        Long readedCount = Db.queryLong("select count(*) from msg_message_user u left join msg_message m on u.msg_id = m.id " +
                "where m.del_flag = 0 and msg_status = 'release' and u.read_flag = 1 and u.user_id = ?",userId);
        Long totalCount = Db.queryLong("select count(*) from msg_message_user u left join msg_message m on u.msg_id = m.id " +
                "where m.del_flag = 0 and msg_status = 'release' and u.user_id = ?",userId);
        JSONObject countJson = new JSONObject();
        countJson.put("notReadCount",notReadCount);
        countJson.put("readedCount",readedCount);
        countJson.put("totalCount",totalCount);

        result.put("code", "0");
        result.put("msg", "success");
        result.put("data",countJson);
        renderJson(result);
    }



    /**
     * 标记已读
     */
    public void signRead(){
        Map<String, Object> result = new HashMap<String, Object>();

        String userId = getPara("userId");
        String msgIdsStr = getPara("msgIds");
        logger.info("标记已读接收参数:用户ID:[{}],消息ID:[{}]",userId,msgIdsStr);

        if(StringUtils.isBlank(userId) || StringUtils.isBlank(msgIdsStr)){
            result.put("code", "10002");
            result.put("msg", "参数未传入");
            renderJson(result);
            return;
        }
        List<String> msgIds = JSONArray.parseArray(msgIdsStr,String.class);
        List<Object> params = Lists.newArrayList();
        String str="";
        for(String id:msgIds){
            str+="?,";
            params.add(id);
        }
        str=str.substring(0,str.length()-1);
        params.add(userId);
        //判断传过来的消息是否有已读
        Long countRead = Db.queryLong("select count(*) from msg_message_user where del_flag = 0 and read_flag = 1 and msg_id in ("+str+") and user_id = ?",params.toArray());
        if(countRead == msgIds.size()){
            result.put("code", "10001");
            result.put("msg", "标记失败,消息已读");
            renderJson(result);
            return;
        }
        int count = Db.update("update msg_message_user set read_flag = '1',read_time = sysdate() where msg_id in ("+str+") and user_id = ?",params.toArray());
        if(count > 0){
            result.put("code", "0");
            result.put("msg", "标记成功");
        }else{
            result.put("code", "10001");
            result.put("msg", "标记失败");
        }
        renderJson(result);
    }
}
