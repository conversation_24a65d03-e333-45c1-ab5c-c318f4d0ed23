package com.message.web.config;

import com.cszn.integrated.base.common.AppInfo;
import com.cszn.integrated.base.web.handler.CustomActionHandler;
import com.cszn.integrated.base.web.render.AppRenderFactory;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.config.Constants;
import com.jfinal.config.Interceptors;
import com.jfinal.config.Routes;
import com.jfinal.ext.handler.ContextPathHandler;
import com.jfinal.json.FastJsonFactory;
import com.jfinal.log.Log4jLogFactory;
import com.jfinal.template.Engine;
import io.jboot.Jboot;
import io.jboot.aop.jfinal.JfinalHandlers;
import io.jboot.aop.jfinal.JfinalPlugins;
import io.jboot.core.listener.JbootAppListenerBase;

public class JfinalConfigListener extends JbootAppListenerBase {
	
    @Override
    public void onConstantConfig(Constants constants) {
        constants.setError401View("/template/401.html");
        constants.setError403View("/template/403.html");
        constants.setError404View("/template/404.html");
        constants.setError500View("/template/500.html");
        constants.setJsonFactory(new FastJsonFactory());
        constants.setRenderFactory(new AppRenderFactory());
        constants.setLogFactory(new Log4jLogFactory());
    }

    @Override
    public void onRouteConfig(Routes routes) {
        routes.setBaseViewPath("/template");
    }

    @Override
    public void onEngineConfig(Engine engine) {
        engine.setDevMode(true);
        AppInfo app = Jboot.config(AppInfo.class);
        engine.addSharedObject("APP", app);
        engine.addSharedObject("RESOURCE_HOST", app.getResourceHost());
    }

    @Override
    public void onInterceptorConfig(Interceptors interceptors) {
//         interceptors.add(new LogInterceptor());
    }

    @Override
    public void onPluginConfig(JfinalPlugins plugins) {

    }

    @Override
    public void onHandlerConfig(JfinalHandlers handlers) {
        handlers.setActionHandler(new CustomActionHandler());
        handlers.add(new ContextPathHandler("ctxPath"));
    }

    @Override
    public void onStartBefore() {

    }

    @Override
    public void onStop() {
    }

    @Override
    public void onStart() {
        /** 集群模式下验证码使用 redis 缓存 */
//        CaptchaManager.me().setCaptchaCache(new CaptchaCache());
//    	Global.mpAppid = Jboot.configValue("mpAppId");
//    	Global.mpAppSecret = Jboot.configValue("mpAppSecret");
//    	Global.sojournUrl = Jboot.configValue("sojournUrl");
//    	Global.fileUrlPrefix = Jboot.configValue("fileUrlPrefix");
//    	Global.uploadPath = Jboot.configValue("uploadPath");
//    	Global.excelTemplatePath = Jboot.configValue("excelTemplatePath");
//    	Global.uploadPathLinux = Jboot.configValue("uploadPath.linux");
        Global.commonUpload = Jboot.configValue("commonUpload");
    }
}
