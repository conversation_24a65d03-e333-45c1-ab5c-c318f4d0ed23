/**
 * 
 */
package com.message.web.controller.sys;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.UserRoleService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.message.web.support.auth.AuthUtils;
import com.message.web.support.log.LogInterceptor;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by LiangHuiLing on 2019年4月29日
 *
 * DictController
 */
@RequestMapping(value="/user", viewPath="/modules_page/sys/user")
public class UserController extends BaseController {

	@Inject
	private UserService userService;
	@Inject
	private UserRoleService userRoleService;
	
	public void index(){
		render("userIndex.html");
	}
	
    /**
     * 登陆帐号分页表格数据
     */
	@Clear(LogInterceptor.class)
    public void pageTable() {
    	User user = getBean(User.class, "", true);
        Page<User> userPage = userService.paginateByCondition(user, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<User>(userPage));
    }
    
    /**
     * 编辑用户页面方法
     */
    public void edit() {
		User model = userService.findById(getPara("id"));
    	setAttr("model", model);
        render("userForm.html");
    }
    
	/**
	 * 表单分配角色权限树数据
	 */
	@Clear(LogInterceptor.class)
	public void userRolesTree() {
		final String userId = getPara("userId");
		List<ZTree> treeNodeList = userRoleService.userRolesTree(userId, SystemType.MESSAGE);
		renderJson(treeNodeList);
	}
	
	/**
     * 修改密码页面方法
     */
    public void modifyPwd() {
    	final User model = AuthUtils.getLoginUser();
		setAttr("model", model);
		render("modifyPwd.html");
    }
    
	/**
	 * 用户修改密码保存方法
	 */
	public void modifyPwdSave() {
		final String inputOldPwd = getPara("inputOldPwd");
		final String oldPwd = AuthUtils.getLoginUser().getPassword();
		final String oldSalt = AuthUtils.getLoginUser().getSalt();
		final User user = getBean(User.class, "", true);
		Map<String, String> paramMap = new HashMap<>();
		JSONObject dataJson = new JSONObject();
		dataJson.put("inputOldPwd", inputOldPwd);
		dataJson.put("oldPwd", oldPwd);
		dataJson.put("oldSalt", oldSalt);
		dataJson.put("user", user);
		paramMap.put("passData", dataJson.toJSONString());
		final String returnResult = HttpKit.post("http://127.0.0.1:8888/api/modifyPassword", paramMap, null);
		Ret retResult = JSONObject.parseObject(returnResult, Ret.class);
		renderJson(retResult);
	}
    
	/**
	 * 用户保存方法(包含角色ID字符串)
	 */
	public void saveUser() {
		final String roleIds = getPara("roleIds");
		User user = getBean(User.class, "", true);
		final User oldUser = userService.getByUserName(user.getUserName());
		user.setUpdateBy(AuthUtils.getUserId());
		if(StrKit.isBlank(user.getId()) && oldUser!=null){
			renderJson(Ret.fail("msg", "用户名已存在！"));
		}else{
			if (userService.saveUser(user, roleIds,null,SystemType.MESSAGE)) {
				renderJson(Ret.ok("msg", "操作成功!"));
			} else {
				renderJson(Ret.fail("msg", "操作失败！"));
			}
		}
	}
    
	/**
	 * 用户保存方法
	 */
	public void save() {
		User user = getBean(User.class, "", true);
		if (userService.saveOrUpdate(user)!=null) {
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}
}
