package com.message.web.controller;

import com.cszn.integrated.base.common.Consts;
import com.cszn.integrated.base.plugin.shiro.MuitiLoginToken;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Before;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.POST;
import com.jfinal.kit.Ret;
import com.message.web.validator.LoginValidator;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.ExcessiveAttemptsException;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.subject.Subject;

/**
 * 首页控制器
 * <AUTHOR>
 *
 */
@RequestMapping("/")
public class MainController extends BaseController {

	@Inject
    private UserService userService;
    
    public void login() {
    	if (SecurityUtils.getSubject().isAuthenticated()) {
    		redirect("/");
    	} else {
    		render("login.html");
    	}
    }
    
    public void captcha() {
    	renderCaptcha();
    }
    
    @Before( {POST.class, LoginValidator.class} )
    public void postLogin() {
    	String loginName = getPara("loginName");
    	String pwd = getPara("password");
    	
    	MuitiLoginToken token = new MuitiLoginToken(loginName, pwd);
    	Subject subject = SecurityUtils.getSubject();
    	
    	Ret restRsult = Ret.ok("msg", "登录成功");
    	try {
    		if (!subject.isAuthenticated()) {
    			token.setRememberMe(false);
    			subject.login(token);
    			
    			User u = userService.getByUserName(loginName);
    			subject.getSession(true).setAttribute(Consts.SESSION_USER, u);
    		}
    		if (getParaToBoolean("rememberMe") != null && getParaToBoolean("rememberMe")) {
    			setCookie("loginName", loginName, 60 * 60 * 24 * 7);
    		} else {
    			removeCookie("loginName");
    		}
    	} catch (UnknownAccountException une) {
    		restRsult = Ret.fail("msg", "用户名不存在");
    	} catch (LockedAccountException lae) {
    		restRsult = Ret.fail("msg", "帐号已锁定,不能登陆");
    	} catch (IncorrectCredentialsException ine) {
    		restRsult = Ret.fail("msg", "用户名或密码不正确");
    	} catch (ExcessiveAttemptsException exe) {
    		restRsult = Ret.fail("msg", "账户密码错误次数过多，账户已被限制登录1小时");
    	} catch (Exception e) {
    		e.printStackTrace();
    		restRsult = Ret.fail("msg", "服务异常，请稍后重试");
    	}
    	
    	renderJson(restRsult);
    }

    public void index() {
        render("main.html");
    }

    public void welcome() {
        render("welcome.html");
    }

    /**
     * 退出
     */
    public void logout() {
        if (SecurityUtils.getSubject().isAuthenticated()) {
            SecurityUtils.getSubject().logout();
        }
        render("login.html");
    }
}
