package com.message.web.support.directive;

import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.template.Env;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.ParseException;
import com.jfinal.template.stat.Scope;
import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

import java.util.List;

/**
 * 根据data的type和value获取对应的字典描述
 */
@JFinalDirective("dict")
public class DictDirective extends JbootDirectiveBase {

	@Inject
    private DictService dictService;

    /** 数据字典类型编码 */
    private String typeCode;
    /**需要查询的字典代码*/
    private String code;

    @Override
    public void exec(Env env, Scope scope, Writer writer) {
        if (exprList.length() > 2) {
            throw new ParseException("Wrong number parameter of #dict directive, 2 parameters allowed at most", location);
        }

        typeCode = getPara(0, scope);
        if (StrKit.isBlank(typeCode)) {
            throw new ParseException("typeCode is null", location);
        }

        if (exprList.length() > 1) {
            code = getPara(1,scope);
        }

        if (StrKit.isBlank(code)) {
            write(writer, "");
        } else {
        	List<Dict> list = dictService.getListByTypeOnUse(typeCode);
        	if(list != null && list.size() > 0){
        		for (Dict dict : list) {
        			if(code.equals(dict.getDictValue())) {
        				write(writer,dict.getDictName());
        			}else{
        				write(writer, "");
        			}
        		}
        	}else{
        		write(writer, "");
        	}
        }
    }

    @Override
    public void onRender(Env env, Scope scope, Writer writer) {

    }
}
