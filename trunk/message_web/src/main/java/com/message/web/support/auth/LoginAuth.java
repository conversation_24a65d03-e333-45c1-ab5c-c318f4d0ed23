package com.message.web.support.auth;

import com.cszn.integrated.base.plugin.shiro.auth.MuitiAuthenticatied;
import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.api.sys.RoleService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.status.LoginFlag;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.status.UserType;
import com.cszn.integrated.service.entity.sys.Role;
import com.cszn.integrated.service.entity.sys.User;
import io.jboot.Jboot;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 管理端认证授权
 * <AUTHOR>
 *
 */
public class LoginAuth implements MuitiAuthenticatied {

    @Override
    public boolean hasToken(AuthenticationToken authenticationToken) {
    	String loginName = authenticationToken.getPrincipal().toString();
        UserService sysUserApi = Jboot.service(UserService.class);
        User sysUser = sysUserApi.getByUserName(loginName);
        return sysUser!=null?true:false;
    }

    @Override
    public boolean wasLocked(AuthenticationToken authenticationToken) {
    	boolean resultFlag = false;
    	String loginName = authenticationToken.getPrincipal().toString();
        UserService sysUserApi = Jboot.service(UserService.class);
        User sysUser = sysUserApi.getByUserName(loginName);
        if(sysUser!=null && sysUser.getLoginFlag().equals(LoginFlag.LOCKED)){
        	resultFlag = true;
        }
        return resultFlag;
    }

    @Override
    public AuthenticationInfo buildAuthenticationInfo(AuthenticationToken authenticationToken) {
        String loginName = authenticationToken.getPrincipal().toString();
        UserService sysUserApi = Jboot.service(UserService.class);
        User sysUser = sysUserApi.getByUserName(loginName);
        String salt = sysUser.getSalt();
        String pwd = sysUser.getPassword();

        return new SimpleAuthenticationInfo(loginName, pwd, ByteSource.Util.bytes(salt), "ShiroDbRealm");
    }

    @Override
    public AuthorizationInfo buildAuthorizationInfo(PrincipalCollection principals) {
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        String loginName = (String) principals.fromRealm("ShiroDbRealm").iterator().next();
        MenuService menuService = Jboot.service(MenuService.class);
        //如果登录的用户是system_admin类型的用户，则将所有的权限加上
        if(AuthUtils.getLoginUser().getUserType().equals(UserType.SYSTEM_ADMIN)){
            Set<String> allPermissions=menuService.findSystemAllMenu(SystemType.MESSAGE);
            info.addStringPermissions(allPermissions);
            return info;
        }
        RoleService sysRoleApi = Jboot.service(RoleService.class);
        List<Role> sysRoleList = sysRoleApi.findRoleByName(loginName,SystemType.MESSAGE);
        List<String> roleNameList = new ArrayList<>();
        for (Role sysRole : sysRoleList) {
            roleNameList.add(sysRole.getRoleName());
        }
        Set<String> permissions=menuService.findPermissionByRole(sysRoleList);
        if(roleNameList!=null && roleNameList.size()>0) {
        	info.addRoles(roleNameList);
        }
        if(permissions!=null && !permissions.isEmpty()) {
        	info.addStringPermissions(permissions);
        }
        return info;
    }
}
