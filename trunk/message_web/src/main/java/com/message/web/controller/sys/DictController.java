/**
 * 
 */
package com.message.web.controller.sys;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.message.web.support.log.LogInterceptor;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * Created by LiangHuiLing on 2019年4月4日
 *
 * DictController
 */
@RequestMapping(value="/dict", viewPath="/modules_page/sys/dict")
public class DictController extends BaseController {

	@Inject
	private DictService dictService;
	
	public void index(){
		List<Record> dictTypeList = dictService.getDictTypeList();
		setAttr("dictTypeList", dictTypeList);
		render("dictIndex.html");
	}

	@Clear(LogInterceptor.class)
	public void pageTable(){
		Dict dict = getBean(Dict.class, "", true);
        Page<Dict> dictPage = dictService.paginateByCondition(dict, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<Dict>(dictPage));
	}
	
	/**
     * 添加页面方法
     */
    public void add() {
    	final Dict model = getBean(Dict.class, "", true);
    	setAttr("model", model);
        render("dictForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String dictId = getPara("id");
        setAttr("model", dictService.findById(dictId));
        render("dictForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
        final Dict dict = getBean(Dict.class, "", true);
        final boolean verifyDictName = dictService.verifyDictName(dict.getDictType(), dict.getDictName());
        if(StrKit.isBlank(dict.getId()) && verifyDictName){
        	renderJson(Ret.fail("msg", "字典名称已存在，请重新输入！"));
        }else{
    		renderJson(dictService.saveDict(dict));
        }
    }
    
    /**
     * 批量删除方法
     */
    public void batchDel() {
    	final String batchDelDatas = getPara("batchDelDatas");
    	if(StrKit.notBlank(batchDelDatas)){
    		List<Dict> dictList = JSONArray.parseArray(batchDelDatas, Dict.class);
    		if (dictService.batchDel(dictList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }
    
    public void del(){
    	final Dict dict = getBean(Dict.class, "", true);
    	if(dict!=null && StrKit.notBlank(dict.getId())){
    		if(dictService.update(dict)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }
}
