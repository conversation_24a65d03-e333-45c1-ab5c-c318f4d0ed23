package com.message.web.support.directive;

import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.entity.sys.Area;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.template.Env;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.ParseException;
import com.jfinal.template.stat.Scope;
import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

import java.util.List;

/**
 * 区域下拉Option指令
 */
@JFinalDirective("areaOption")
public class AreaOptionDirective extends JbootDirectiveBase {

	@Inject
    private AreaService areaService;

    private String parentId;
    private String areaId;

    @Override
    public void exec(Env env, Scope scope, Writer writer) {
        
    	if (exprList.length() > 2) {
            throw new ParseException("Wrong number parameter of #areaOption directive, 2 parameters allowed at most", location);
        }

    	parentId = getPara(0, scope);
        if (exprList.length() > 1) {
        	areaId = getPara(1, scope, "");
        }
//        LogKit.info("areaParentId====="+parentId);
//        LogKit.info("areaId====="+areaId);

        List<Area> areaList = areaService.getAreaByParentId(parentId);
        for (Area area : areaList) {
            if (StrKit.notBlank(areaId) && area.getId().equals(areaId)) {
                write(writer, "<option value=\"" + area.getId()  + "\" selected>" + area.getAreaName() + "</option>");
            } else {
                write(writer, "<option value=\"" + area.getId()  + "\">" + area.getAreaName() + "</option>");
            }
        }
    }

    @Override
    public void onRender(Env env, Scope scope, Writer writer) {

    }
}
