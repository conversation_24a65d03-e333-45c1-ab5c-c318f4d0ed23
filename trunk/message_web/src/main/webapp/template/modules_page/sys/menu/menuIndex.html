#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜单权限管理首页#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['treeGrid','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,treeGrid = layui.treeGrid
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'menuTreeGrid'
        ;
	
    // 初始化表格
    var ptable=treeGrid.render({
        id: tableId
        , elem: '#'+tableId
        , url: '#(ctxPath)/menu/menuTreeGrid'
    	, method: 'post'
   		, height: vipTable.getFullHeight()    //容器高度
        , idField: 'id'//必須字段
        , treeId: 'id'//树形id字段名称
        , treeUpId: 'pId'//树形父id字段名称
        , treeShowName: 'name'//以树形式显示的字段
        , isOpenDefault: true//节点默认是展开还是折叠【默认展开】
        , cols: [[
        	 {field: '', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'type', title:'类型', width:100, unresize:true, align:'center', templet: '#dictTpl("menu_type", "type")'}
			,{field:'name', title:'名称', unresize:true}
			,{field:'permission', title:'权限', unresize:true}
			,{field:'url', title:'链接', width:200, unresize:true}
    		,{field:'sort', title: '<button id="saveSortBtn" type="button" class="layui-btn layui-btn-xs">保存排序</button>', width:100, align:'center', templet: function(d){
				var sortInput='<input type="text" class="layui-input sortInput" data-id="'+d.id+'" value="'+d.sort+'" maxlength="7" autocomplete="off">';
				return sortInput;
				}
    		}
			,{title:'操作', width:200, unresize:true, align:'center', templet: function(d){
				var editBtn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
				var delBtn='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>';
				return editBtn+delBtn;
				}
			}
        ]]
    });
	//表格事件绑定
	treeGrid.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/menu/edit?id='+obj.data.id,'','');
        }else if(obj.event==="del"){//作废按钮事件
        	layer.confirm('你确定作废数据吗？如果存在下级节点则一并作废，此操作不能撤销！', {icon: 3, title:'提示'}, function(index) {
				//作废操作
				util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/menu/del',
                    data: {id:obj.data.id},
                    notice: true,
                    loadFlag: true,
                    success : function(data){
                    	tableGridReload();
                    },
                    complete : function() {
                    	
                    }
                });
				layer.close(index);
			});
        }
    });
    //重载表格
    tableGridReload = function () {
    	treeGrid.reload(tableId, {});
    }
	//展开/收缩按钮点击事件
// 	$('#openShrinkBtn').on('click', function() {
// 		var treedata=treeGrid.getDataTreeList(tableId);
//         treeGrid.treeOpenAll(tableId,!treedata[0][treeGrid.config.cols.isOpen]);
// 	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		pop_show('添加','#(ctxPath)/menu/add','','');
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	tableGridReload();
    });
	//保存排序按钮点击事件
	$('#saveSortBtn').on('click', function() {
		sortSave();
	});
	sortSave = function() {
		var saveFlag = true;
		var dataArray = new Array();
		$(".sortInput").each(function(i,obj){
			var menuId = $(this).attr('data-id');
			var menuSort = obj.value;
		    if(menuSort==null || menuSort==''){
		    	saveFlag = false;
		    	return false;
		    }else{
		    	var inputObj = {'id':menuId, 'menuSort':menuSort};
		    	dataArray.push(inputObj);
		    }
		});
		if(saveFlag){
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/menu/sortBatchSave',
	            data: {sortDatas:JSON.stringify(dataArray)},
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		tableGridReload();
	            	}
	            },
	            complete : function() {
	            	//保存按钮点击事件
					$('#saveSortBtn').on('click', function() {
						sortSave();
					});
			    }
	        });
		}else{
			layer.msg('排序不能为空，请输入排序!',{icon:5});
		}
	}
});
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl"></span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
	        			<a id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
	</div>
	<div class="layui-row" style="height:550px;">
		<table id="menuTreeGrid" lay-filter="menuTreeGrid"></table>
	</div>
</div>
#end