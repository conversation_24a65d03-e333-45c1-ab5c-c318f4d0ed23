#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工登陆帐号管理页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script type="text/html" id="userTableBar">
#[[
	{{# if((d.userType!='org_manager')){ }}
		{{# if((d.loginFlag=='un_locked')){ }}
    		<a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="locked">锁定</a>
		{{# }else{ }}
    		<a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="unlock">解锁</a>
		{{# } }}
	{{# } }}
]]#
	<a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
#[[
	{{# if((d.userType!='org_manager')){ }}
		<a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="del">作废</a>
	{{# } }}
]]#
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table', 'form', 'element', 'vip_table'], function() {
	// 操作对象
	var table = layui.table
	, form = layui.form
    , layer = layui.layer
    , element = layui.element
	, $ = layui.$
    , vipTable = layui.vip_table
    , tableId = 'userTable'
    ;
	
	// 表格加载渲染
	var tableObj = table.render({
		id : tableId
		,elem : '#'+tableId
		,method : 'POST'
		,url : '#(ctxPath)/user/pageTable'
		,where : {employeeId:$('#empId').val()}//额外查询条件
		,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
		,cols : [[ 
			{field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'userType', title:'帐号类型', width:100, align:'center', templet:'#dictTpl("user_type", "userType")'}
			,{field:'userName', title:'登陆帐号', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{field:'loginFlag', title:'登陆状态', align:'center', templet:'#dictTpl("login_flag", "loginFlag")'}
			,{fixed:'right', title:'操作', width:200, align:'center', toolbar:'#userTableBar'}
		]]
		,page : true
	});
	//监听工具条
	table.on('tool('+tableId+')', function(obj) {
		if (obj.event === 'locked') {
			layer.confirm('您确定要锁定该帐号?', function(index) {
				//锁定操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/user/save',
		            data: {id:obj.data.id, loginFlag:'locked'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}else if (obj.event === 'unlock') {
			layer.confirm('您确定要解锁该帐号?', function(index) {
				//解锁操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/user/save',
		            data: {id:obj.data.id, loginFlag:'un_locked'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}else if (obj.event === 'edit') {
			editTab('编辑', obj.data.id, '#(ctxPath)/orgEmployee/editEmpUser/');
		} else if (obj.event === 'del') {
			layer.confirm('您确定要作废当前登陆帐号?', function(index) {
				//作废操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/user/saveUser',
		            data: {id:obj.data.id, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}
	});
	//重载表格
    pageTableReload = function () {
    	tableReload(tableId,{employeeId:$('#empId').val()});
    }
	//添加/编辑页签方法
	editTab = function(title,empId,url) {
		$("#userTab li").removeAttr("class");
		element.tabAdd('userTab', {
			title: title
			,content: '<div id="user_edit_content"></div>' //支持传入html
			,id: 'userEdit'
		});
		$('#user_edit_content').load(url+empId, null, function(response,status,xhr){
			if(status=='success'){
				form.render('select', '');
				element.tabChange('userTab', 'userEdit');
			}else{
				layer.msg("添加失败!",{icon:5});
			}
		});
    };
	//关闭编辑页签
    closeEditTab = function () {
    	//作废指定Tab项
		element.tabDelete('userTab', 'userEdit');
    }
	element.on('tab(userTab)', function(data){
		var tabId = $(this).attr("lay-id");//当前Tab标题所在的原始DOM元素的lay-id属性值
		if(tabId=='userList'){
			closeEditTab();
		}
	});
	//添加按钮点击事件
	$('#btn-add').on('click', function() {
		editTab('添加', $("#empId").val(), '#(ctxPath)/orgEmployee/addEmpUser/');
	});
	// 刷新
    $('#btn-refresh').on('click', function () {
    	pageTableReload();
    });
});
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-tab layui-tab-card" lay-filter="userTab">
		<ul class="layui-tab-title">
			<li lay-id="userList" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<div class="layui-row">
					<input type="hidden" id="empId" value="#(empId??'')">
					<a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
					<a id="btn-refresh" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
				</div>
				<div class="layui-row">
					<table id="userTable" lay-filter="userTable"></table>
				</div>
			</div>
		</div>
	</div>
</div>
#end