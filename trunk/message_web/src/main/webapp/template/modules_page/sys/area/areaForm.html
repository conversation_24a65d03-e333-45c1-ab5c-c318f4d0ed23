#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()区域编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	,$ = layui.jquery
	;
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/save',
		    data: $(formObj.form).serialize(),
		    notice: true,
		    loadFlag: true,
		    success : function(rep){
		    	if(rep.state=='ok'){
			    	parent.pageTableReload();
			    	pop_close();
		    	}
		    },
		    complete : function() {
		    }
		});
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div style="margin-bottom:23px;"></div>
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>行政代码</label>
			<div class="layui-input-block">
				<input type="text" name="areaCode" class="layui-input" lay-verify="required" value="#(model.areaCode??)" placeholder="请输入行政代码">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>区域名称</label>
			<div class="layui-input-block">
				<input type="text" name="areaName" class="layui-input" lay-verify="required" value="#(model.areaName??)" placeholder="请输入区域名称">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">类型</label>
			<div class="layui-input-block">
				<select name="areaLv" lay-filter="">
					<option value="1" #(model.areaLv??0 == 1?'selected':'')>国家</option>
					<option value="2" #(model.areaLv??0 == 2?'selected':'')>省</option>
					<option value="3" #(model.areaLv??0 == 3?'selected':'')>县/市</option>
					<option value="4" #(model.areaLv??0 == 4?'selected':'') >镇/区</option>
					<option value="5" #(model.areaLv??0 == 5?'selected':'') >街道/乡</option>
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>排序</label>
			<div class="layui-input-block">
				<input type="text" name="areaSort" class="layui-input" lay-verify="required|number" value="#(model.areaSort??)" placeholder="请输入排序">
			</div>
		</div>
<!-- 		<div style="margin-bottom:60px;"></div> -->
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.Id??'')">
				#if(!model.Id??)
					<input type="hidden" id="parentId" name="parentId" value="#(model.parentId??'0')">
				#end
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end