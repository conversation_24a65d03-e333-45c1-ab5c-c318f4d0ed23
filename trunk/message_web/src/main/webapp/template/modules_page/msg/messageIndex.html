#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()消息管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        msgTableReload=function(){
            table.reload('msgTable');
        }


        function msgLoad(data){
            table.render({
                id : 'msgTable'
                ,elem : '#msgTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/msg/message/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'title', title: '消息标题', align: 'center', unresize: false}
                    ,{field:'url', title: '消息链接', align: 'center', unresize: false}
                    ,{field:'label', title: '消息标签', align: 'center', unresize: false,templet:function (d) {
                        if(d.label != undefined && d.label != ''){
                            var str = '';
                            var array=d.label.split(",");
                            for(var i=0;i<array.length;i++){
                                str += '<span class="layui-badge layui-bg-gray">'+array[i]+'</span>&nbsp;'
                            }
                            return str;
                        }else{
                            return '- -';
                        }
                        }}
                    ,{field:'msgType', title: '消息类型', align: 'center', unresize: false,templet:"<div>{{ dictLabel(d.msgType,'msg_type','- -') }}</div>"}
                    ,{field:'sourceSystem', title: '来源系统', align: 'center', unresize: false,templet:"<div>{{ dictLabel(d.sourceSystem,'source_system','- -') }}</div>"}
                    ,{field:'allFlag', title: '是否全部通知', align: 'center', unresize: false,templet:"<div>{{ d.allFlag=='1'?'<span class='layui-badge layui-bg-green'>是</span>':d.allFlag=='0'?'<span class='layui-badge'>否</span>':'- -' }}</div>"}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: false,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 160, align: 'center', unresize: false, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        table.on('tool(msgTable)',function(obj){
            if(obj.event === 'detail'){
                var url = "#(ctxPath)/msg/message/form?id=" + obj.data.id;
                pop_show("查阅消息",url,1050,680);
            }else if(obj.event === 'withdraw'){
                layer.confirm("确定要撤回吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/msg/message/withdraw',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                msgTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'release'){
                layer.confirm("确定要发布吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/msg/message/release',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                msgTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });


        // 添加
        /*$("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/pers/notice/add" ;
            pop_show("发布公告",url,1050,750);
        });*/

        /*msgTableReload=function(){
            table.reload('msgTable');
        }
        table.on('tool(msgTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/notice/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                noticeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/pers/notice/edit?id=" + obj.data.id ;
                pop_show("编辑公告",url,1050,750);
            }else if(obj.event === 'release'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/pers/notice/release',
                    notice: true,
                    data: {id:obj.data.id},
                    loadFlag: false,
                    success : function(rep){
                        if(rep.state=='ok'){
                            noticeTableReload();
                        }
                    },
                    complete : function() {
                    }
                });
            }else if(obj.event === 'withdraw'){
                layer.confirm("确定要撤回吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/notice/withdraw',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                noticeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'detail'){
                var url = "#(ctxPath)/pers/notice/detail?id=" + obj.data.id;
                pop_show("查阅公告",url,1050,680);
            }
        });*/

        <!--region Description-->
        /*//批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('gatewayTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/gatewayTypeTable/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {bedTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("gatewayTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });*/
        <!--endregion-->
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("message:msgQuery:releaseBtn")
    #[[
    {{#if(d.msgStatus=='draft'){}}
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="release">发布</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("message:msgQuery:detailBtn")
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="detail">查看</a>
    #end
    #shiroHasPermission("message:msgQuery:withdrawBtn")
    #[[
    {{#if(d.msgStatus=='release'){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="withdraw">撤回</a>
    {{#}}}
    ]]#
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            标题：
            <div class="layui-inline">
                <input id="title" name="title" class="layui-input">
            </div>
            &nbsp;&nbsp;
            来源系统：
            <div class="layui-inline">
                <select name="sourceSystem" lay-search>
                    <option value="">请选择来源系统</option>
                    #for(s : sourceSystems)
                    <option value="#(s.dictValue)">#(s.dictName)</option>
                    #end
                </select>
            </div>
            &nbsp;&nbsp;
            全部通知：
            <div class="layui-inline">
                <select name="allFlag" lay-search>
                    <option value="">请选择</option>
                    <option value="0">否</option>
                    <option value="1">是</option>
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        <!--#shiroHasPermission("notice:release:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end-->
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table class="layui-table" id="msgTable" lay-filter="msgTable"></table>
</div>
#getDictLabel("msg_type")
#getDictLabel("source_system")
#end