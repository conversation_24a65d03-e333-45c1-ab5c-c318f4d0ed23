/* html,body {width: 100%;height: 100%;margin: 0 ;} */
.layui-contents {height: 90%;}
.layui-main-content {padding: 15px;}
/* .layui-table-body {overflow: hidden;} */
/*表单下拉框高度，原值为300，调节高度建议为50的倍数*/
.layui-form-select dl{max-height:200px;}
/*长者列表单击表格行样式*/
.layui-table-click-tr {background: #F2F2F2;}
/*按钮在最右边*/
.layui-pull-right {text-align: right;}
/*长者基本信息*/
.layui-table-head span{width:200px;height:20px;border-bottom: 1px solid #eee;display: inline-block;padding-top: 10px;color: #333;}
.layui-head {height: 150px;text-align: center;line-height: 135px;}
.layui-head img{width: 130px;height: 130px;border-radius:50%;margin: 0px auto;}
/*laydate日期范围样式*/
.laydate-input-inline-item {width: 510px;height: 38px;margin-bottom: 15px;position: relative;}
.laydate-input-inline {width: 200px;height: 38px;}
.span-group-laydate-addon {width : 40px;height: 36px;background : #FFF ;border-left:0;border-right:0;border-top:1px solid #eee;border-bottom:1px solid #eee;display: block;text-align: center;line-height: 38px;position: absolute;top: 0px;left: 200px;}
.laydate-input-inline-item div:nth-child(3) {position: absolute;top: 0px;left: 240px;} 
.laydate-btn-primary {position: absolute;top: 0px;left: 445px;}
/*表单固定按钮在底部样式*/
.layui-form-footer {width: 100%;position: fixed;z-index: 99;bottom: 0;left: 0;background: #FEFEFE;border-top: 1px solid #EEE;padding: 10px 0;}
.pull-left {float: left!important;margin-left: 15px;}/*!important属性覆盖*/
.pull-right {float: right!important;margin-right: 15px;}/*!important属性覆盖*/
/*入住注册步骤引导样式*/
.layui-step-guide {display: inline-block!important;padding-bottom: 20px;width: 100% ;}
.layui-step-guide li {display: inline-block;background: #EEE;width: 200px;height: 40px;line-height: 40px;font-size: 16px;margin-right: 6px;position: relative;text-align: center; }
.layui-step-guide li em {width: 25px;height: 25px;background: #FFF;display: inline-block;line-height: 25px;border-radius: 50px;margin-right: 5px;text-align: center;color: black!important;}
.layui-step-guide li:first-child {width: 200px;height: 40px;background:#009688;display: inline-block;line-height: 40px; }
.layui-step-guide li i {width: 29px;height: 29px;background:#EEE;display: inline-block;line-height: 29px; transform: rotate(45deg);position: absolute;top:5px;left: 185px;z-index: 1;}
.layui-step-guide .activit,.activit i {background:#009688!important;color: #FFF;}
.layui-step-guide span em:before {content:"";background: #FFF;width: 29px;height: 29px;transform: rotate(45deg);position: absolute;top:5px;left: -14px;}
    
    
    
    
    