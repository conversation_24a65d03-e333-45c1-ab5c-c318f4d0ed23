#---------------------------------------------------------------------------------#
# app info
jboot.admin.app.name=\u6D88\u606F\u7BA1\u7406\u7CFB\u7EDF
jboot.admin.app.org=cszn
jboot.admin.app.orgWebsite=www.cszn.com
jboot.admin.app.resourceHost
jboot.admin.app.copyRight=Copyright 2021 \u5E7F\u5DDE\u5E02\u660C\u677E\u667A\u80FD\u79D1\u6280\u5F00\u53D1\u6709\u9650\u516C\u53F8 All rights reserved
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot\u7684\u5F00\u53D1\u6A21\u5F0F
jboot.mode=dev
jboot.bannerEnable=true
jboot.bannerFile=banner.txt
jboot.cron4jEnable=false
jboot.cron4jFile=cron4j.properties
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#type = local (support:local,motan,dubbo)
#use local
jboot.rpc.type = local
jboot.mq.type = rabbitmq
jboot.mq.channel = userMsgChannel
jboot.mq.rabbitmq.username = admin
jboot.mq.rabbitmq.password = admin
jboot.mq.rabbitmq.host = *************
jboot.mq.rabbitmq.virtualHost = /
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# mysql config
jboot.datasource.type=mysql
jboot.datasource.url=************************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
jboot.datasource.maximumPoolSize = 5
jboot.datasource.sqlTemplatePath=
jboot.datasource.sqlTemplate=
jboot.datasource.table=
jboot.datasource.excludeTable=
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
jboot.model.idCacheEnable=false
#---------------------------------------------------------------------------------#
#---------------------------------------------------------------------------------#
# shiro config
jboot.shiro.ini = shiro.ini
jboot.shiro.loginUrl=/login
jboot.shiro.successUrl
jboot.shiro.unauthorizedUrl=/login
#---------------------------------------------------------------------------------#
commonUpload=http://192.168.4.21:8885/api