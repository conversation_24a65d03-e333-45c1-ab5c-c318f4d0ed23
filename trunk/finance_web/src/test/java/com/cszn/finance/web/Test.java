/**
 * 
 */
package com.cszn.finance.web;

import java.util.UUID;

import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.crypto.hash.SimpleHash;

import io.jboot.Jboot;
import io.jboot.app.JbootApplication;

/**
 * Created by LiangHuiLing on 2019年11月27日
 *
 * Test
 */
public class Test {

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		// TODO Auto-generated method stub
		JbootApplication.setBootArg("jboot.mq.type", "rabbitmq");
		JbootApplication.setBootArg("jboot.mq.rabbitmq.username", "admin");
		JbootApplication.setBootArg("jboot.mq.rabbitmq.password", "admin");
        JbootApplication.setBootArg("jboot.mq.rabbitmq.host", "*************");
        JbootApplication.setBootArg("jboot.mq.rabbitmq.virtualHost", "/");
		Jboot.getMq().enqueue("my name is LiangHuiLing : " + UUID.randomUUID(), "userMsgChannel");
		System.out.println("message send success!");
	}

}
