#---------------------------------------------------------------------------------#
# app info
jboot.admin.app.name=\u8D22\u52A1\u7BA1\u7406
jboot.admin.app.org=cszn
jboot.admin.app.orgWebsite=www.cszn.com
jboot.admin.app.resourceHost
jboot.admin.app.copyRight=\u5E7F\u5DDE\u5E02\u660C\u677E\u667A\u80FD\u79D1\u6280\u5F00\u53D1\u6709\u9650\u516C\u53F8 All rights reserved
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot\u7684\u5F00\u53D1\u6A21\u5F0F
jboot.mode=dev
jboot.bannerEnable=true
jboot.bannerFile=banner.txt
jboot.cron4jEnable=false
jboot.cron4jFile=cron4j.properties
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#type = local (support:local,motan,dubbo)
#use local
jboot.rpc.type = local
#jboot.mq.type = rabbitmq
#jboot.mq.rabbitmq.username = admin
#jboot.mq.rabbitmq.password = admin
#jboot.mq.rabbitmq.host = *************
#jboot.mq.rabbitmq.virtualHost = /
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot.redis.host=127.0.0.1
#jboot.redis.password=
#jboot.redis.port = 6379

#---------------------------------------------------------------------------------#


#---------------------------------------------------------------------------------#
# mysql config
jboot.datasource.type=mysql
jboot.datasource.url=****************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
jboot.datasource.maximumPoolSize = 5
jboot.datasource.sqlTemplatePath=
jboot.datasource.sqlTemplate=
jboot.datasource.table=
jboot.datasource.excludeTable=
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
jboot.model.idCacheEnable=false
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# cache config : type default ehcache (support:ehcache,redis,ehredis)
#jboot.cache.type=redis
#jboot.cache.redis.host=127.0.0.1
#jboot.cache.redis.password=123456
#jboot.cache.redis.database=0
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# jwt config
jboot.web.jwt.httpHeaderName=jwttoken
jboot.web.jwt.secret=21934e3ccf3c19a6c3299cbc2e33b2d848e6ef9ea4e484b19b07bea82888a7a7
# 60 * 60 * 10
jboot.web.jwt.validityPeriod=36000
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# shiro config
jboot.shiro.ini = shiro.ini
jboot.shiro.loginUrl=/login
jboot.shiro.successUrl
jboot.shiro.unauthorizedUrl=/login
#---------------------------------------------------------------------------------#

#---------------------------------\uFFFD\u03F4\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFDbegin-------------------------------#
uploadPath=E:\\virtualDir\\filePath.war\\
excelTemplatePath=E:\\virtualDir\\filePath.war\\excelTemplate\\cardExcel.xls
fileUrlPrefix=http://localhost:8080/
commonUpload=http://127.0.0.1:8885/api
#---------------------------------\uFFFD\u03F4\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\uFFFDend-------------------------------#
# \u673A\u6784\u4E3B\u8D26\u5355\u4FEE\u6539\u5730\u5740
orgUpdateSettleMainUrl=http://localhost:8280/pension/csapi/updateSettleMain
# \u673A\u6784\u660E\u7EC6\u4FEE\u6539\u5730\u5740
orgUpdateBillDateilUrl=http://localhost:8280/pension/csapi/updateBillDateil
#\u77ED\u4FE1\u53D1\u9001\u5730\u5740
sendMessageUrl=http://bmp.csitd.com/Member/SendByTemp
sendMsgCompanyName=\u5E7F\u5DDE\u5E02\u660C\u677E\u7231\u5FC3\u517B\u8001\u670D\u52A1\u6709\u9650\u516C\u53F8
changeCardUrl=http://127.0.0.1:8889/api/newExpenseForChangeCard

taskCreateUrl=http://bmp.csitd.com/Task/Create
taskDaoUrl=http://bmp.csitd.com/Task/Do
getMyTasksUrl=http://bmp.csitd.com/Internal/GetFormTaskList
getTaskDetail=http://bmp.csitd.com/Task/GetTaskDetail

#\u9000\u5361\u6D41\u7A0B\u7F16\u53F7
refundCardProcessNo=PF006
#\u9000\u5361\u8868\u5355\u7F16\u53F7
refundCardNo=RefundCardNo

hrmUrl=http://hrm.csitd.com
orgUrl=http://org.csitd.com
#WMS\u7CFB\u7EDF\u5E94\u7528\u53F7
wmsAppNo=9100003
#\u673A\u6784URL\u5730\u5740
pensionUrl=http://pension.cncsgroup.com/pension
bmpUrl=http://bmp.csitd.com
#\u6DFB\u52A0\u4F1A\u5458\u5361\u77ED\u4FE1\u7C7B\u578B
addCardSmsType=addCard1