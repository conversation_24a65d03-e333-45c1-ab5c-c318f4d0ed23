#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构养老费用账单首页#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .laytable-cell-checkbox .layui-disabled.layui-form-checked i {
        background: #fff !important;
    }
</style>
#end

#define content()
<div class="layui-collapse">

    <div class="layui-row">
        <div class="layui-row" style="padding: 5px;" >
            <button class="layui-btn layui-btn-sm" type="button" onclick="addBill('#(main.id??)')">添加明细</button>
        </div>
        <div style="padding: 0 5px 5px 5px;">
            <table class="layui-table" id="table-#(main.id??)" lay-filter="table-#(main.id??)"></table>
        </div>

    </div>

</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    <div class="layui-btn-group">
    #shiroHasPermission("finance:orgSettle:replaceCardBtn")
    #[[
    {{#if(d.status==='wait_settlement'){}}
    <a class="layui-btn layui-btn-xs" lay-event="replace">更换会员卡</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("finance:orgSettle:settleDateilBtn")
    #[[
    {{#if(d.status==='wait_settlement'){}}
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="settle">结算</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("finance:orgSettle:settleDateilBtn")
    #[[
    {{#if(d.status==='wait_settlement'){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("finance:orgSettle:delDateilBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
    </div>
</script>
<script>
    layui.use(['form','layer','table','laydate','element'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate,element=layui.element;


        //添加明细
        addBill=function(mainId) {
            layerShow('添加明细','#(ctxPath)/fina/orgSettle/addBillDetail?mainId='+mainId,700,600);
        }

        element.on('collapse(mainColla)', function(data){
            var id=$(this).attr("data-id");
            var settleStatus=$(this).attr("data-settleStatus");
            if(data.show){
                billDetailTableLoad(id,settleStatus);
                couponTableLoad(id);
                $("#currId").val(id);
            }else{
                $("#currId").val('');
            }
        });

        $("#print").on('click',function () {
            layer.open({
                type: 2,
                //title: '收藏管理 (考生姓名：张无忌)',
                title: '选择打印', //将姓名设置为红色
                shadeClose: true,           //弹出框之外的地方是否可以点击
                offset: '10%',
                area: ['90%', '80%'],
                content: '#(ctxPath)/fina/orgSettle/printOrgSettle?checkinNo='+$("#checkinNo").val()
            });
        });



        //删除主账单
        delSettleMain=function(id,e){
            layer.confirm('确定要作废该账单吗?',function (index){
                $.post('#(ctxPath)/fina/orgSettle/delSettleMain',{'id':id},function (res) {
                    if(res.state==='ok'){
                        loadSettleMain();
                        layer.msg(res.msg, {icon: 1, offset: 'auto'});
                    }else{
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                    layer.close(index);
                });
            });
            layui.stope(e);
        }
        
        loadSettleMain=function(){
            $.post('#(ctxPath)/fina/orgSettle/findSettleMainList',{'checkinNo':$("#checkinNo").val()},function (res) {
                if(res.state==='ok'){
                    $("#mainColla").empty();
                    var data=res.data;
                    if(data.length===0){
                        $("#none").css("display","block");
                    }else{
                        $("#none").css("display","none");
                    }
                    var str="";
                    var currId=$("#currId").val();
                    $.each(data,function (index,item) {
                        str+='<div class="layui-colla-item">' +
                            '<h2 class="layui-colla-title" data-id="'+item.id+'" data-settleStatus="'+item.settleStatus+'">'+item.mainName ;
                        if(item.settleStatus==='no_settle'){
                            str+='<span class="layui-badge">未结算</span>';
                        }else if(item.settleStatus==='normal'){
                            str+='<span class="layui-badge layui-bg-green">已结算</span>';
                        }
                        str+='<div class="layui-inline" style="float: right;">';

                        #shiroHasPermission("finance:orgSettle:settleMainBtn")
                        if(item.settleStatus==='no_settle'){
                            str+='<button class="layui-btn layui-btn-sm" id="settleBtn" onclick="settleBtnClick(\''+item.id+'\')" type="button">结算</button>';
                        }
                        #end

                        var showStr="";
                        if(item.id===currId){
                            var showStr="layui-show";
                        }

                        str+='<button class="layui-btn layui-btn-sm layui-btn-danger" onclick="delSettleMain(\''+item.id+'\')">作废</button>' +
                            '</div></h2>' +
                            '<div class="layui-colla-content '+showStr+'">' +
                            '<fieldset class="layui-elem-field" id="billFieldset" style="margin-top: 30px;">' +
                            '<legend>费用明细</legend>' +
                            '<div style="padding: 0 5px 5px 5px;">' +
                            '<table class="layui-table" id="table-'+item.id+'" lay-filter="table-'+item.id+'"></table>' +
                            '</div></fieldset>' +

                            '<fieldset class="layui-elem-field" id="couponFieldset" style="margin-top: 30px;">' +
                            '<legend>纸卡记录</legend>' +
                            '<div style="padding: 0 5px 5px 5px;">' +
                            '<table class="layui-table" id="table-coupon-'+item.id+'" lay-filter="table-coupon-'+item.id+'"></table>' +
                            '</div></fieldset>' +
                            '</div></div>';
                    });
                    $("#mainColla").html(str);
                    if(currId!=''){
                        billDetailTableLoad(currId,'');
                        couponTableLoad(currId);
                    }
                    element.render('collapse', 'mainColla');//刷新折叠面板
                }
            });
        }


        //结算按钮点击事件
        settleBtnClick=function(id,e){
            layer.confirm('确定要结算吗?',function (index) {
                layer.load();
                //判断是否有数据
                var data=table.cache["table-"+id];
                /*if(data==null || data.length===0){
                    layer.msg('该账单没有要结算的项', {icon: 2, offset: 'auto'});
                    return false;
                }*/
                loadingIndex = layer.load(1,{shade : [0.1, '#fff']});
                $.post('#(ctxPath)/fina/orgSettle/memberMainSettle',{"id":id},function (res) {
                    if(res.state==='ok'){
                        loadSettleMain();
                        $("#settleBtn").addClass("layui-btn-danger").prop("disabled",true).attr("lay-event","").text("已结算");
                        layer.msg(res.msg, {icon: 1, offset: 'auto'});
                        layer.closeAll('loading');
                    }else{
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                    layer.close(loadingIndex);
                });
                layer.close(index);
            });
            layui.stope(e);
        }

        //纸卡使用记录
        couponTableLoad=function(id){
            table.render({
                id : 'table-coupon-'+id
                ,elem : '#table-coupon-'+id
                ,method : 'POST'
                ,limit : 10
                ,where:{'mainId':id}
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/orgSettle/findMainCashRecordPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'type', title: '纸卡类型',width:140, align: 'center',event:'type', unresize: true,templet:"<div>#[[{{#if(d.type==='1'){}} 金额纸卡{{#}else if(d.type==='2'){}} 天数纸卡 {{#}else if(d.type==='3'){}} 现金{{#}else{}}- -{{#}}}]]#</div>"}
                    ,{field:'amountNum', title: '数量', align: 'center',event:'amountNum', unresize: true}
                    ,{field:'unit', title: '单位', align: 'center', unresize: true,event:'unit',templet:"<div>#[[{{#if(d.type==='1'){}}元{{#}else if(d.type==='2'){}}天{{#}else if(d.type==='3'){}}元{{#}else{}}- -{{#}}}]]#</div>"}
                    ,{field:'receiveName', title: '收款人', align: 'center', unresize: true,event:'receiveName'}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true,event:'remark'}
                    /*,{fixed:'right', title: '操作', width: 200, align: 'left', unresize: true, toolbar: '#actionBar'}*/
                ]]
                ,page:false
                ,done:function (res,curr,count) {
                }
            });

            table.on('tool(table-coupon-'+id+')',function (obj) {
                var settlestatus=$(".layui-colla-item").find("h2[data-id='"+id+"']").attr("data-settlestatus");
                if(settlestatus==='no_settle'){
                    if(obj.event==='type' || obj.event==='amountNum' || obj.event==='unit' || obj.event==='receiveName' || obj.event==='remark'){
                        layerShow('修改纸卡记录','#(ctxPath)/fina/orgSettle/cashRecordForm?id='+obj.data.id,500,400);
                    }
                }
            });

            var couponTips;
            $("#couponFieldset").find("thead").find("tr").find("th[data-field='type'],[data-field='amountNum'],[data-field='unit'],[data-field='receiveName'],[data-field='remark']").hover(function () {
                couponTips=layer.tips("点击行可编辑行数据",this,{tips:[3],time:0});
            },function () {
                layer.close(couponTips);
            });
        }



        billDetailTableReload=function(id){
            table.reload('table-'+id,{'where':{'mainId':id}});
        }



        billDetailTableLoad=function (id) {
            table.render({
                id : 'table-'+id
                ,elem : '#table-'+id
                ,method : 'POST'
                ,limit : 10
                ,where:{'mainId':id}
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/orgSettle/findMainBillDetail'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'accountNumber', title: '扣费会员卡号',width:140, align: 'center', unresize: true}
                    ,{field:'name', title: '费用名称', align: 'center', unresize: true}
                    //,{field:'chargeHandle', title: '费用处理方式',width:120, align: 'center', unresize: true,templet:"<div>{{d.chargeHandle=='charge'?'<span class='layui-badge layui-bg-green'>收费</span>':d.chargeHandle=='refund'?'<span class='layui-badge'>退费</span>':'- -'}}</div>"}
                    ,{field:'status', title: '状态',width:80, align: 'center', unresize: true,templet:"<div>{{d.status=='settled'?'<span class='layui-badge layui-bg-green'>已结算</span>':d.status=='wait_settlement'?'<span class='layui-badge'>待结算</span>':'- -'}}</div>"}
                    ,{field:'amount', title: '扣除值',width:80, align: 'center',edit:'text', unresize: true}
                    ,{title: '单位', align: 'center',width:70, unresize: true,templet:"<div>{{d.deductType==='deduct_by_money'?'元':d.deductType==='deduct_by_days'?'天':'- -'}}</div>"}
                    ,{field:'integrals', title: '扣除积分',width:100, align: 'center', unresize: true,edit:'text'}
                    ,{field:'produceDate',title: '产生时间', align: 'center',width:320, unresize: true,event:'produceDate',templet:"<div>{{dateFormat(d.startDate,'yyyy-MM-dd HH:mm:ss')}}至{{dateFormat(d.endDate,'yyyy-MM-dd HH:mm:ss')}}</div>"}
                    ,{field:'describe', title: '明细描述', align: 'center', unresize: true,event:'describe'}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true,event:'remark'}
                    ,{fixed:'right', title: '操作', width: 200, align: 'left', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page:false
                ,done:function (res,curr,count) {
                    //已经结算过的明细禁止修改费用
                    // 当前页面缓存数据
                    var dataTemp = table.cache[this.id];
                    // 控件渲染出来的table
                    var tableElem = this.elem.next();
                    layui.each(dataTemp, function (index, data) {
                        if(data.status==='settled'){
                            // 关闭金额和天数的修改
                            tableElem.find('tr[data-index="'+index+'"]').find('td[data-field="amount"]').removeAttr('data-edit');
                            tableElem.find('tr[data-index="'+index+'"]').find('td[data-field="times"]').removeAttr('data-edit');
                            tableElem.find('tr[data-index="'+index+'"]').find('td[data-field="integrals"]').removeAttr('data-edit');
                        }
                        if(data.status!='wait_settlement' || data.isIntegral!='1'){
                            tableElem.find('tr[data-index="'+index+'"]').find('td[data-field="integrals"]').removeAttr('data-edit');
                        }
                    });
                }
            });

            var describeTips,amountTips;
            $("#billFieldset").find("thead").find("tr").find("th[data-field='describe'],[data-field='produceDate'],[data-field='remark']").hover(function () {
                describeTips=layer.tips("点击该列可编辑行数据",this,{tips:[3],time:0});
            },function () {
                layer.close(describeTips);
            });

            $("#billFieldset").find("thead").find("tr").find("th[data-field='amount'],[data-field='integrals']").hover(function () {
                amountTips=layer.tips("点击该列可编辑扣除值",this,{tips:[3],time:0});
            },function () {
                layer.close(amountTips);
            });



            table.on('tool(table-'+id+')',function (obj) {

                if(obj.event==='del'){
                    layer.confirm('确定要作废该明细吗?',function (index) {
                        $.post('#(ctxPath)/fina/orgSettle/delBillDetail',{'id':obj.data.id},function (res) {
                            if(res.state==='ok'){
                                layer.msg(res.msg, {icon: 1, offset: 'auto'});
                                billDetailTableLoad(id);
                            }else{
                                layer.msg(res.msg, {icon: 2, offset: 'auto'});
                            }
                            layer.close(index);
                        })
                    });
                }else if(obj.event==='replace'){
                    layerShow('更换会员卡','#(ctxPath)/fina/orgSettle/billDetaIlReplaceCardIndex?detailId='+obj.data.id+"&mainId="+id,1200,800);
                }else if(obj.event==='settle'){
                    layer.confirm('确定要结算该明细吗?',function (index) {
                        layer.load();
                        loadingIndex = layer.load(1,{shade : [0.1, '#fff']});
                        $.post('#(ctxPath)/fina/orgSettle/memberSettleBillDetail',{'id':obj.data.id},function (res) {
                            if(res.state==='ok'){
                                layer.msg(res.msg, {icon: 1, offset: 'auto'});
                                //billDetailTableReload(id);
                                billDetailTableLoad(id);
                                layer.closeAll('loading');
                            }else{
                                layer.msg(res.msg, {icon: 2, offset: 'auto'});
                            }
                            layer.close(loadingIndex);
                            layer.close(index);

                        });
                    });
                }else if(obj.event==='edit'){
                    layerShow('编辑明细','#(ctxPath)/fina/orgSettle/updateBillDetailRemarkIndex?detailId='+obj.data.id,700,600);

                }
            });

            table.on('edit(table-'+id+')', function(obj){
                var value = obj.value //得到修改后的值
                    ,field = obj.field
                    ,data = obj.data //得到所在行所有键值
                var inputElem = $(this);
                var tdElem = inputElem.closest('td');
                var valueOld = inputElem.prev().text();
                var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
                if(!reg.test(value)){
                    data[field] = valueOld;
                    layer.msg('请输入整数或2位小数点');
                    inputElem.prev().text(valueOld);
                    inputElem.val(valueOld);
                    inputElem.blur();
                    tdElem.click();
                    return false;
                }

                layer.confirm("是否要修改该值",{
                    btn: ['确定', '取消']
                }, function (index) {
                    layer.load();
                    $.post("#(ctx)/fina/orgSettle/updateMainBillDetail?id="+data.id+"&"+field+"="+data[field], function(res) {
                        if(res.state==='ok'){
                            layer.msg(res.msg, {icon: 1, offset: 'auto'});
                            billDetailTableLoad(id);
                        }else{
                            layer.msg(res.msg, {icon: 2, offset: 'auto'});
                        }
                        layer.closeAll('loading');
                        layer.close(index);
                    });
                }, function(index){
                    billDetailTableLoad(id);
                    layer.close(index);
                });
            });
        }

        billDetailTableLoad('#(main.id)');

    });
</script>
#end