#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡充值记录#end

#define css()
<style>
    .layui-table-cell {
        font-size:10px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row" style="padding-top: 20px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width:80px;padding-left:0px;">会员卡号：</label>
            <div class="layui-input-inline">
                <input type="text" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width:80px;padding-left:5px;">所属会员：</label>
            <div class="layui-input-inline">
                <input type="text" id="fullName" placeholder="请输入所属会员" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width:80px;padding-left:5px;">充值时间：</label>
            <div class="layui-input-inline">
                <input type="text" id="startDate" name="startDate" autocomplete="off" class="layui-input" placeholder="开始时间">
            </div>
            至
            <div class="layui-input-inline">
                <input type="text" id="endDate" name="endDate" autocomplete="off" class="layui-input" placeholder="结束时间">
            </div>

        </div>
        <button id="searchRechargeBtn" class="layui-btn">搜索</button>
    </div>
    <div style="margin-top: 20px;padding-left:20px;">
        <table id="rechargeRecordTable" lay-filter="rechargeRecordTable"></table>
    </div>
</div>
#getDictLabel("recharge_classify")
#end

#define js()
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">详情</a>
</script>
<script type="text/javascript">
var table,$ ;
layui.use(['table','laydate'],function(){
    table = layui.table;
    $ = layui.$ ;
    var laydate = layui.laydate;

    laydate.render({
        elem:"#startDate",
        type:"date",
        range:false,
        format:"yyyy-MM-dd"
    }) ;

    laydate.render({
        elem:"#endDate",
        type:"date",
        range:false,
        format:"yyyy-MM-dd"
    });

    //加载会员消费信息
    function loadRechargeTable(data){
        //loading层
        var loadingIndex = layer.load(2, { //icon支持传入0-2
            shade: [0.5, 'gray'], //0.5透明度的灰色背景
            content: '加载中...',
            success: function (layero) {
                layero.find('.layui-layer-content').css({
                    'padding-top': '39px',
                    'width': '60px'
                });
            }
        });
        table.render({
            id : 'rechargeRecordTable',
            elem : "#rechargeRecordTable" ,
            url : '#(ctxPath)/fina/consumerecord/getConsumeRecord' ,
            where: data,
            height:'full-110',
            cols : [[
                {field:'fullName', title: '会员卡信息', align: 'left', unresize: false,width:'10%',templet:function (d) {
                        var str="";
                        if(d.cardNumber != null && d.cardNumber != ''){
                            str = d.cardNumber;
                        }else{
                            str = "--";
                        }
                        str += " ";
                        if(d.fullName != null && d.fullName != null){
                            str += d.fullName;
                        }else{
                            str += "--";
                        }
                        return str;
                    }},
                {field:'cardType',title:'卡类别',width:'12%',unresize:false},
                {field:'isIncome',title:'收入性',width:90,unresize:false,templet:function (d){
                        if('1'==d.isIncome) {
                            return  "<span class='layui-badge layui-bg-green'>收入性</span>"
                        }else if('0'==d.isIncome){
                            return  "<span class='layui-badge layui-bg-orange'>非收入性</span>"
                        }else{
                            return '- -'
                        }
                    }},
                {field:'rechargeNo',title:'充值编号',width:110,unresize:false},
                {field:'isCash',title:'是否收现金',width:90,unresize:false,templet:function (d){
                        if('1'==d.isCash) {
                            return "<span class='layui-badge layui-bg-green'>是</span>"
                        }else if('0'==d.isCash){
                            return  "<span class='layui-badge layui-bg-orange'>否</span>"
                        }else{
                            return '- -'
                        }
                    }},
                {field:'accountName',title:'收钱账户',width:140,unresize:false,templet:function (d){
                        var payWay='';
                        var accountName='';
                        if(d.accountName!=undefined){
                            accountName=d.accountName;
                        }
                        if('1'==d.payWay){
                            payWay='(现金)'
                        }else if('2'==d.payWay){
                            payWay='(微信)'
                        }else if('3'==d.payWay){
                            payWay='(支付宝)'
                        }else if('4'==d.payWay){
                            payWay='(信用卡)'
                        }else if('5'==d.payWay){
                            payWay='(会员卡)'
                        }else if('6'==d.payWay){
                            payWay='(Pos机)'
                        }else if('7'==d.payWay){
                            payWay='(转账)'
                        }else if('8'==d.payWay){
                            payWay='(企业微信)'
                        }else{

                        }

                        return payWay+accountName;
                    }},
                {field:'type',title:'类型',width:90,unresize:false,templet:"<div>{{ d.type=='recharge_prestore'?'<span class='layui-badge layui-bg-green'>充值</span>':d.type=='give_prestore'?'<span class='layui-badge layui-bg-orange'>赠送</span>':d.type=='refund'?'<span class='layui-badge layui-bg-black'>退费</span>':'- -' }}</div>"},
                {field:'classify',title:'充值分类',width:90,unresize:false,templet: '#dictTpl("recharge_classify", "classify")'},
                {field:'',title:'付款金额',width:90,unresize:false,templet: function (d) {
                        if(typeof(d.payAmount)==='undefined'){
                            return '0.0'
                        }else{
                            return d.payAmount;
                        }
                    }},
                {field:'',title:'充值金额',width:90,unresize:false,templet: function (d) {
                        if(typeof(d.amount)==='undefined'){
                            return '0元'
                        }else{
                            return d.amount+'元';
                        }
                    }},
                {field:'',title:'充值天数',width:90,unresize:false,templet: function (d) {
                        if(typeof(d.times)==='undefined'){
                            return '0天'
                        }else{
                            return d.times+'天';
                        }
                    }},
                {field:'',title:'充值点数',width:90,unresize:false,templet: function (d) {
                        if(typeof(d.points)==='undefined'){
                            return '0.0'
                        }else{
                            return d.points;
                        }
                    }},
                {field:'integrals',title:'充值积分',width:90,unresize:false,templet: function (d) {
                        if(typeof(d.integrals)==='undefined'){
                            return '0.0'
                        }else{
                            return d.integrals;
                        }
                    }},
                {field:'beanCoupons',title:'充值豆豆券',width:90,unresize:false,templet: function (d) {
                        if(typeof(d.beanCoupons)==='undefined'){
                            return '0.0'
                        }else{
                            return d.beanCoupons;
                        }
                    }},
                {field:'timesSnapshot',title:'天数快照',width:110,unresize:false,templet: "<div>{{ String(d.timesSnapshot)?d.timesSnapshot + '天':'- -' }}</div>"},
                {field:'amountSnapshot',title:'金额快照',width:110,unresize:false,templet: "<div>{{ String(d.amountSnapshot)?d.amountSnapshot + '元':'- -' }}</div>"},
                {field:'pointsSnapshot',title:'点数快照',width:110,unresize:false,templet: function (d) {
                        if(typeof(d.pointsSnapshot)==='undefined'){
                            return '0.0';
                        }else{
                            return d.pointsSnapshot;
                        }
                    }},
                {field:'integralsSnapshot',title:'积分快照',width:110,unresize:false,templet: function (d) {
                        if(typeof(d.integralsSnapshot)==='undefined'){
                            return '0.0';
                        }else{
                            return d.integralsSnapshot;
                        }
                    }},
                {field:'beanCouponsSnapshot',title:'豆豆券快照',width:110,unresize:false,templet: function (d) {
                        if(typeof(d.beanCouponsSnapshot)==='undefined'){
                            return '0.0';
                        }else{
                            return d.beanCouponsSnapshot;
                        }
                    }},
                {field:'describe',title:'说明',width:400,unresize:false,style:"text-align:left;font-size:10px;color:#000;"},
                {field:'rechargeTime',title:'充值/赠送时间',width:180,unresize:false,templet:"<div>{{dateFormat(d.rechargeTime,'yyyy-MM-dd')}}</div>"}
            ]] ,
            page : true,
            limit : 10
            , done: function (res, curr, count) {
                // layer.closeAll('loading');
                layer.close(loadingIndex);
            }
        }) ;
    }

    loadRechargeTable({inOutFlag:'1'});

    // 搜索消费记录按钮
    $('#searchRechargeBtn').on('click', function(){
        var data = {cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),inOutFlag:'1',startDate:$('#startDate').val(),endDate:$('#endDate').val()};
        loadRechargeTable(data);
    });

    //回车事件
    document.onkeydown = function(e){
        var ev =document.all ? window.event:e;
        if(ev.keyCode==13) {
            $('#searchRechargeBtn').click();
            return false
        }
    }
});

</script>
#end