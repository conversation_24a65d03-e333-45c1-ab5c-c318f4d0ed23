#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅居消费主记录列表#end

#define css()
#end

#define content()
<div class="my-btn-box">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
            <div class="layui-row">
            	<div class="layui-inline">
					<label class="layui-form-label">会员卡号</label>
					<div class="layui-input-inline">
						<input type="text" name="cardNumber" class="layui-input" placeholder="请输入会员卡号" autocomplete="off">
					</div>
				</div>
            	<div class="layui-inline">
					<label class="layui-form-label">持卡人</label>
					<div class="layui-input-inline">
						<input type="text" name="fullName" class="layui-input" placeholder="请输入持卡人姓名" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
	                <button type="button" id="search" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
				</div>
            </div>
        </form>
    <div class="layui-row">
        <table id="erTable" lay-filter="erTable"></table>
    </div>
</div>
#getDictLabel("gender")
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
	#shiroHasPermission("finance:sojournCardOrder:billBtn")
	<a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="detail">账单</a>
	#end
</script>
<script>
    layui.use(['form','layer','table','laydate','element'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate,element=layui.element;

        //日期范围
        laydate.render({
            elem:"#checkinTime",
            type:"date",
            format:"yyyy-MM-dd"
        }) ;
        
        function erLoad(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            table.render({
                id : 'erTable'
                ,elem : '#erTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-90'
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/expenseRecord/findCardExpenseRecordCount'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'cardNumber', title: '会员卡号', align: 'center', unresize: false}
                    ,{field:'fullName', title: '持卡人', align: 'center', unresize: true}
                    ,{field:'total', title: '账单总数', align: 'center', unresize: false}
                    ,{field:'book', title: '预定账单数', align: 'center', unresize: true}
                    ,{field:'checkin', title: '入住中账单数', align: 'center', unresize: true}
                    ,{field:'checkout', title: '退住账单数', sort: true, align: 'center', unresize: true,templet:"<div>{{d.checkout}}({{d.unsettleed}}未结算)</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function (res,curr,count) {
                    $.each(res.data,function (index,item) {
                        if(item.checkinStatus==='book' && item.settleStatus==='2'){
                            $(".layui-table-body").find("table").find("tr:eq("+index+")").attr("style","background-color:#ffe3e4;");
                        }
                    });
					layer.close(loadingIndex);
                }
            });
        };
        table.on('tool(erTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/expenseRecord/delete',
                        data: {id:obj.data.id},
                        notice:true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                table.reload('erTable');
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event === 'detail'){
                //addTab('','xx信息','#(ctxPath)/fina/expenseRecord/cardExpenseRecordIndex');
                //新增
                var title=obj.data.cardNumber+"-"+obj.data.fullName+"的账单";
                var src='#(ctxPath)/fina/expenseRecord/cardExpenseRecordIndex?cardNumber='+obj.data.cardNumber;
                var id = new Date().getTime();
                var flag = getTitleId('card', title);
                if(flag>0){
                    id = flag;
                }else{
                    window.parent.layui.element.tabAdd('card', {
                        title: title
                        ,content: '<iframe src="' + src + '" frameborder="0"></iframe>' //支持传入html
                        ,id: id
                    });
                }
                window.parent.layui.element.tabChange('card', id);
            }
        });

        erLoad(null);

        form.on("submit(search)",function(data){
            erLoad(data.field);
            return false;
        });

      	//回车事件
    	document.onkeydown = function(e){
    		var ev =document.all ? window.event:e;  
    		if(ev.keyCode==13) {
    			$('#search').click();
    			return false
    		}
    	}

        // 根据导航栏text获取lay-id
        function getTitleId(card, title) {
            var id = -1;
            $(document).find(".layui-tab[lay-filter=" + card + "] ul li").each(function () {
                if (title === $(this).find('span').html()) {
                    id = $(this).attr('lay-id');
                }
            });
            return id;
        }
    });
</script>
#end