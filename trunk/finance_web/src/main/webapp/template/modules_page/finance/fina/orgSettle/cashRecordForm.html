#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()费用明细修改备注#end

#define css()
#end

#define js()
<script type="text/javascript">
    var table,$ ;
    layui.use(['table','laydate','form'],function(){
        table = layui.table;
        $ = layui.$ ;
        var laydate = layui.laydate;
        var form=layui.form;

        form.on('radio(type)',function (obj) {
            if(obj.value==='1'){
                $("#quantityLabel").text("数量(元)");
            }else if(obj.value==='2'){
                $("#quantityLabel").text("数量(天)");
            }
        })


        //保存
        form.on('submit(saveBtn)', function(obj){
            util.sendAjax({
                url:'#(ctxPath)/fina/orgSettle/cashRecordSave',
                type:'post',
                data:obj.field,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.couponTableLoad($("#mainId").val());
                    }
                }
            });
            return false;
        });

    });

</script>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">详情</a>
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row" style="padding-top: 20px;">
        <form class="layui-form layui-form-pane" action="">

            <div class="layui-form-item">
                <label class="layui-form-label">纸卡类型</label>
                <div class="layui-input-block">
                    <input type="radio" name="type" lay-filter="type" lay-verify="required" value="1" title="金额纸卡" #if(cashRecord.type??=='1') checked #end>
                    <input type="radio" name="type" lay-filter="type" lay-verify="required" value="2" title="天数纸卡" #if(cashRecord.type??=='2') checked #end>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">数量#if(cashRecord.type??=='1')(元)#elseif(cashRecord.couponType??=='2')(天)#elseif(cashRecord==null)(元)#end</label>
                <div class="layui-input-block">
                    <input type="text" name="amountNum" autocomplete="off" lay-verify="required|number" value="#(cashRecord.amountNum??)" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注内容" name="remark" class="layui-textarea">#(cashRecord.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input id="detailId" type="hidden" name="id" value="#(cashRecord.id??)" >
                    <input id="mainId" type="hidden" value="#(cashRecord.expenseId??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end