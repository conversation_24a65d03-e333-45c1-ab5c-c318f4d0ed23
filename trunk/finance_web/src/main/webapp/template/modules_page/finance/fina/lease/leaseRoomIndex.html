#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
<style>
    .layui-tab  td, .layui-tab th {
        padding: 9px 5px;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">

            <div class="layui-inline">
                <label class="layui-form-label" style="width: 40px;">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="baseId">
                        <option value="">全部</option>
                        #for(base : baseList)
                        <option value="#(base.id??)">#(base.baseName??)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"  style="width: 60px;">房间号：</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <input type="text" id="roomName" placeholder="请输入房间号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"  style="width: 80px;">业主姓名：</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <input type="text" id="supplierName" placeholder="请输入业主姓名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="padding: 9px 5px;width: 100px;">合同开始时间：</label>
                <div class="layui-input-inline">
                    <input type="text" id="roomStartDate" placeholder="请输入合同开始时间" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="padding: 9px 5px;width: 100px;">合同结束时间：</label>
                <div class="layui-input-inline">
                    <input type="text" id="roomEndDate" placeholder="请输入合同结束时间" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 60px;">合同状态</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <select id="contractStatus" name="contractStatus">
                        <option value="">全部</option>
                        <option value="1">未开始</option>
                        <option value="2">合同期</option>
                        <option value="3">已结束</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <button type="button" id="searchConsumeBtn" class="layui-btn">搜索</button>
                <button type="button" id="exportBtn" class="layui-btn">导出</button>

            </div>

        </div>
    </form>
    <div class="layui-row">
        <table id="cardTypeTable" lay-filter="cardTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="rentAdd">查看租金涨幅</a>
        #shiroHasPermission("finance:leaseRoomPayBtn")
        <a class="layui-btn layui-btn-xs" lay-event="edit">付款单</a>
        #end
        <a class="layui-btn layui-btn-xs" lay-event="file">附件</a>
        #[[
        {{#if(d.contractStatus=='2'){}}
        <a class="layui-btn layui-btn-xs" lay-event="end">修改租期结束日期</a>
        {{#}}}
        ]]#
        <!--<a class="layui-btn layui-btn-xs" lay-event="see">预览付款单</a>-->
    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate'],function(){
        var table = layui.table
            , $ = layui.$
        ;
        var laydate=layui.laydate;
        laydate.render({
            elem: '#roomStartDate'
            ,trigger:'click'
            ,range: true
        });
        laydate.render({
            elem: '#roomEndDate'
            ,trigger:'click'
            ,range: true
        });


        cardTypeTableReload=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTable',
                elem:"#cardTypeTable",
                url:'#(ctxPath)/fina/leaseRecordApply/leaseRoomPageList',
                where:data,
                height:'full-90',
                cols:[[
                    {type:'numbers',title:'序号',width:70,unresize:true}
                    ,{field: 'baseName', title: '基地', width:180,unresize:true}
                    ,{field:'roomName', title:'房间号', width:90, unresize:true, align:'center'}
                    ,{field:'supplierName', title:'业主', width:100 , unresize:true, align:'center'}
                    ,{field:'rentMonth', title:'租金', width:100 , unresize:true, align:'center',templet:function (d) {
                            let unit='';
                            if(d.rentUnit=='year'){
                                unit="/年";
                            }else if(d.rentUnit=='month'){
                                unit="/月"
                            }else{
                            }
                            return d.rent+unit;
                        }}
                    ,{field:'rentPay', title:'房租支付方式', width:120 , unresize:true, align:'center',templet:function (d) {
                        let unit='';
                        if(d.paymentType=='year') {
                            unit = "年";
                        }else if(d.paymentType=='halfYear'){
                            unit="半年";
                        }else if(d.paymentType=='season'){
                            unit="季";
                        }else if(d.paymentType=='month'){
                            unit="月"
                        }else if(d.paymentType=='disposable'){
                            unit="一次性"
                        }
                        return unit+"付";
                    }}
                    ,{field:'earnestMoney', title:'押金', width:90 , unresize:true, align:'center'}
                    ,{field:'roomDate', title:'合同时间' ,width:190, unresize:true, align:'center',templet:function (d) {
                            return dateFormat(d.roomStartDate,"yyyy-MM-dd")+"至"+dateFormat(d.roomEndDate,"yyyy-MM-dd")
                        }}
                    ,{field:'contractDate', title:'房租付款期限' ,width:190, unresize:true, align:'center',templet:function (d) {
                            return dateFormat(d.contractStartDate,"yyyy-MM-dd")+"至"+dateFormat(d.contractEndDate,"yyyy-MM-dd")
                        }}
                    ,{field:'addRent', title:'租金涨幅' ,width:130, unresize:true, align:'center',templet:function (d) {
                            if(d.rentAddYear>0 && d.rentAddRatio){
                                return '每'+d.rentAddYear+'年递增'+d.rentAddRatio+"%";
                            }else{
                                return '无'
                            }

                        }}
                    ,{field:'num', title:'已付数量/总付数量',width:150 , unresize:true, align:'center',templet:function (d) {
                            return d.payCount+"/"+d.totalCount
                        }}
                    ,{field: 'status', width:120, title: '合同状态',unresize:true,templet:function (d) {
                            if(d.contractStatus=='1'){
                                return '<span class="layui-badge layui-bg-orange">未开始</span>';
                            }else if(d.contractStatus=='2'){
                                return '<span class="layui-badge layui-bg-green">合同期中</span>';
                            }else if(d.contractStatus=='3'){
                                return '<span class="layui-badge">已结束</span>';
                            }
                        }}
                    ,{field:'remark',title:'备注',unresize:true}
                    ,{title:'操作',width:310, fixed:'right',unresize:true,toolbar:'#toolBar'}
                ]],
                page:true,
                limit:10
                , done: function (res, curr, count) {

                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.close(loadingIndex);
                }
            });
            table.on('tool(cardTypeTable)',function(obj){
                if(obj.event === 'rentAdd'){
                    let unit='';
                    if(d.rentUnit=='year'){
                        unit="/年";
                    }else if(d.rentUnit=='month'){
                        unit="/月"
                    }
                    let html='';
                    $.each(obj.data.rentAddList,function (index,item) {
                        html+=`
                            <tr>
                              <td>`+dateFormat(item.startDate,"yyyy-MM-dd")+`</td>
                              <td>`+dateFormat(item.endDate,"yyyy-MM-dd")+`</td>
                              <td>`+item.rent+`</td>
                            </tr>
                        `
                    })
                    layer.open({
                        type: 1,//这就是定义窗口类型的属性
                        title: '租金涨幅',
                        shadeClose: true,
                        shade: 0.3,
                        offset: "20%",
                        shadeClose : false,
                        area: ['500px', '400px'],
                        content: `<div>
                                    <table class="layui-table">
                                      <colgroup>
                                        <col width="150">
                                        <col width="200">
                                        <col>
                                      </colgroup>
                                      <thead>
                                        <tr>
                                          <th>开始时间</th>
                                          <th>结束时间</th>
                                          <th>租金/`+unit+`</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        `+html+`
                                      </tbody>
                                    </table>

                                </div>`
                    });
                }else if(obj.event === 'edit'){
                    layerShow('付款单','#(ctxPath)/fina/leaseRecordApply/leaseRoomPaymentIndex?leaseId='+obj.data.leaseId+"&roomId="+obj.data.roomId,1000,600);

                }else if(obj.event==='see'){
                    layerShow('付款单','#(ctxPath)/fina/leaseRecordApply/previewTableIndex?leaseId='+obj.data.leaseId+"&roomId="+obj.data.roomId,1000,600);
                }else if(obj.event==='file'){
                    layerShow(obj.data.baseName+'['+obj.data.roomName+']的附件','#(ctxPath)/fina/leaseRecordApply/leaseRoomFileIndex?id='+obj.data.id,800,600);
                }else if(obj.event==='end'){
                    layer.open({//parent表示打开二级弹框
                        type: 1,
                        title: "修改租期结束日期",
                        shadeClose: false,
                        shade: 0.5,
                        btn: ['确定', '关闭'],
                        maxmin: false, //开启最大化最小化按钮
                        area:['500px;','500px;'],
                        content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                            "            <div class=\"layui-form-item\" style='margin-top: 20px;'>\n" +
                            "                <label class=\"layui-form-label\" style='padding: 8px 5px;'><font color='red'>*</font>合同结束日期</label>\n" +
                            "                <div class=\"layui-input-block\" >\n" +
                            "                    <input class=\"layui-input\" placeholder=\"请输入合同结束日期\" value='' name=\"endDate\" id=\"endDate\" autocomplete=\"off\">\n" +
                            "                </div>\n" +
                            "            </div>\n" +
                            "\n" +
                            "            <div class=\"layui-form-item\">\n" +
                            "                <label class=\"layui-form-label\"><font color='red'>*</font>备注</label>\n" +
                            "                <div class=\"layui-input-block\"  >" +
                            "                   <textarea placeholder=\"请输入备注内容\" class=\"layui-textarea\" id='remark' name='remark'></textarea>" +
                            "                </div>\n" +
                            "            </div>" +
                            "</form>",
                        success : function(){
                            laydate.render({
                                elem: '#endDate'
                                ,trigger:'click'
                                ,min:dateFormat(obj.data.roomStartDate,"yyyy-MM-dd")
                                ,max:dateFormat(obj.data.roomEndDate,"yyyy-MM-dd")
                            });
                        },
                        end : function(){
                        },yes: function(index, layero){

                            if($("#remark").val()=='' || $("#endDate").val()==''){
                                layer.msg('合同结束日期与备注内容必须填写', {icon: 2, offset: 'auto'});
                                return false;
                            }
                            layer.load();
                            let data={"remark":$("#remark").val(),"endDate":$("#endDate").val(),"id":obj.data.id};
                            util.sendAjax ({
                                type: 'POST',
                                url: '#(ctxPath)/fina/leaseRecordApply/updateRoomContractendDate',
                                data: data,
                                loadFlag: true,
                                success : function(rep){
                                    if(rep.state=='ok'){
                                        //pageTableReload();
                                        $('#searchConsumeBtn').click();
                                        layer.closeAll("loading");
                                        layer.close(index);
                                    }
                                },
                                complete : function() {
                                }
                            });
                            return false;
                        }

                    });
                }
            });
        }

        cardTypeTableReload(null);

        // 搜索消费记录按钮
        $('#searchConsumeBtn').on('click', function(){
            cardTypeTableReload({contractStatus:$('#contractStatus').val(),baseId:$('#baseId').val(),roomName:$('#roomName').val()
                ,supplierName:$('#supplierName').val(),roomStartDate:$("#roomStartDate").val(),roomEndDate:$("#roomEndDate").val()});
        });

        $('#exportBtn').on('click',function () {
            layer.load();
            window.location.href='#(ctxPath)/fina/leaseRecordApply/leaseRoomPageListExport?baseId='+$("#baseId").val()+"&contractStatus="+$('#contractStatus').val()
                +"&roomName="+$('#roomName').val()+"&supplierName="+$('#supplierName').val()+"&roomStartDate="+$("#roomStartDate").val()+"&roomEndDate="+$("#roomEndDate").val();

            setInterval(function () {
                layer.closeAll('loading');
            },5000);
        });

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#searchConsumeBtn').click();
                return false
            }
        }


    });
</script>
#end