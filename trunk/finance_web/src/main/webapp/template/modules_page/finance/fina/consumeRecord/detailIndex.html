#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()消费扣卡明细页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <table id="tranDetailTable" lay-filter="tranDetailTable"></table>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'tranDetailTable'
            ,elem : '#tranDetailTable'
            ,method : 'POST'
            ,where : {"transactionsId": '#(tranId??)'}
            ,url : '#(ctxPath)/fina/consumerecord/detailPageTable'
            ,cols: [[
                {field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                ,{field:'',title:'扣除类型',unresize:false,templet:function (d) {
                    if(d.deductType=='buy'){
                        return "购买";
                    }else if(d.deductType=='give'){
                        return "赠送";
                    }else{
                        return '- -'
                    }
                }}
                ,{field:'',title:'结算类型',unresize:false,templet:function (d) {
                    if(d.settleType=='amount'){
                        return "金额";
                    }else if(d.settleType=='days'){
                        return "天数";
                    }else if(d.settleType=='integrals'){
                        return "积分";
                    }else{
                        return '- -'
                    }
                }}
                ,{field: 'deductValue', title: '扣除值', align: 'center', unresize:true}
                ,{field:'', title: '操作时间',unresize: true, templet:"<div>{{dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss')}}</div>"}
            ]]
            ,page : true
        });

    });
</script>
#end