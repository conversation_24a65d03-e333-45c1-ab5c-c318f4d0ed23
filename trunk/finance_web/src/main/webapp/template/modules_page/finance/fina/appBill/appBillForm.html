#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()应用消费记录信息展示#end
	<!-- 公共Css文件 -->
#define css()
	<link rel="stylesheet" href="#(ctx)/static/css/member.css" media="all" />
	<style>
		.layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
		.layui-form-item{margin-bottom:0;}
	</style>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<div class="layui-row" style="margin-bottom:50px;">
		<form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="billForm">
			<div class="layui-row">
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">会员卡号</label>
						<div class="layui-input-block">
							<input type="text" readonly="readonly" value="#(card.cardNumber??)" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">开卡人</label>
						<div class="layui-input-block">
							<input type="text" readonly="readonly" value="#(member.fullName??)" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">身份证号</label>
						<div class="layui-input-inline">
							<input type="text" readonly="readonly" value="#(member.idcard??)" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-form-item">
					<div class="layui-inline">
						<label class="layui-form-label">余额</label>
						<div class="layui-input-block">
							<input type="text" readonly="readonly" value="#(card.balance??)" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">剩余天数</label>
						<div class="layui-input-inline">
							<input type="text" readonly="readonly" value="#(card.consumeTimes??)" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<fieldset class="layui-elem-field">
					<legend>消费详情</legend>
					<div class="layui-field-box">
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">消费编号</label>
								<div class="layui-input-block">
									<input type="text" readonly="readonly" value="#(record.consumeNo??)" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">应用订单号</label>
								<div class="layui-input-inline">
									<input type="text" readonly="readonly" value="#(record.appOrderNo??)" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-form-item">
							<div class="layui-inline">
								<label class="layui-form-label">消费金额</label>
								<div class="layui-input-block">
									<input type="text" readonly="readonly" value="#(record.amount??)" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">消费天数</label>
								<div class="layui-input-inline">
									<input type="text" readonly="readonly" value="#(record.consumeTimes??)" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-form-item layui-form-text">
							<label class="layui-form-label">消费描述</label>
							<div class="layui-input-block">
								<textarea class="layui-textarea">#(record.describe??)</textarea>
							</div>
						</div>
						<table id="detailTable" lay-filter="detailTable"></table>
					</div>
				</fieldset>
			</div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<input name="consumeId" id="recordId" type="hidden" value="#(record != null ? record.id : '')" />
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">确认扣费</button>
				</div>
			</div>
		</form>
	</div>
</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
layui.use(['form', 'laydate', 'upload', 'table'],function(){
	var form = layui.form;
	var $ = layui.$;
	var laydate = layui.laydate;
	var upload = layui.upload;
	var table = layui.table;

	//保存
	form.on('submit(confirmBtn)', function(){
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/fina/appBill/confirmConsume',
			data: $("#billForm").serialize(),
			notice: true,
			loadFlag: true,
			success : function(rep){
				if(rep.state=='ok'){
					pop_close();
					parent.layui.table.reload('billTable');
				}
			},
			complete : function() {
			}
		});
		return false;
	});
	
	detailLoad({consumeId:$('#recordId').val()});
	
	function detailLoad(data){
		table.render({
			id : 'detailTable'
			,elem : '#detailTable'
			,method : 'POST'
			,where : data
			,limit : 5
			,limits : [5,15,30,45,50]
			,url : '#(ctxPath)/fina/appBill/details'
			,cellMinWidth: 80
			,cols: [[
				{type: 'numbers', width:100, title: '序号',unresize:true}
				,{field:'describe', title: '消费编号', align: 'center', unresize: true}
				,{field:'takeTime', title: '消费时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.takeTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
				,{field:'amount', title: '消费金额', align: 'center', unresize: true}
				,{field:'consumeTimes', title: '扣除天数', align: 'center', unresize: true}
			]]
			,page : true
		});
	};
});
</script>
#end