#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡规则详情页面#end

#define css()
<style>
	.sp{
		float:left;margin-top: 10px;
	}
</style>
#end

#define js()
<script id="paramTrTpl" type="text/html">
	<tr id="itemDetail-{{d.idx}}">
		<td>
			<span style="float:left;margin-top: 10px;">第</span>
			<div class="layui-input-inline" style="width:80px;margin-left:10px;">
				<input type="text" name="itemDetailList[{{d.idx}}].timeSeq" class="layui-input" lay-verify="required|number" value="{{d.num}}"  placeholder="时间序号" readonly>
			</div>
			<span class="sp"></span>
		</td>
		<td>
			<div class="layui-input-inline" style="width:140px;">
				<input type="text" name="itemDetailList[{{d.idx}}].giveTimes" class="layui-input" lay-verify="required|checkNumber" value="" placeholder="赠送天数">
			</div>
		</td>
		<td>
			<div class="layui-input-inline" style="width:140px;">
				<input type="text" name="itemDetailList[{{d.idx}}].giveAmount" class="layui-input" lay-verify="required|number" value="" placeholder="赠送金额">
			</div>
		</td>
		<td>
			<div class="layui-input-inline" style="width:100px;">
				<input type="hidden" name="itemDetailList[{{d.idx}}].id" value="">
				<input type="hidden" id="rid" name="itemDetailList[{{d.idx}}].ruleId" value="#(rule.id??)">
				<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('itemDetail-{{d.idx}}','')">作废</a>
			</div>
		</td>
	</tr>
</script>
<script type="text/javascript">
	layui.use(['form', 'laytpl','element','table'],function(){
		var form = layui.form
				,laytpl = layui.laytpl
				,layer = layui.layer
				,element=layui.element
				,table=layui.table
				,$ = layui.$
		;


		//禁用下拉框
		$(function(){
			if($("#ruleId").val() != null && $("#ruleId").val() != ''){
				$("#cycle").attr("disabled",true);
			}
		});

		//校验
		form.verify({
			checkNumber:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
				}
			}
		});

		//添加按钮点击事件
		$('#addDetail').on('click', function() {
			var num = 0;
			var arr = [];
			$('#detailList').find("tr").each(function(){
				arr.push($(this).find("td").eq(0).find("input").val());
			});
			arr = arr.sort(function(a,b){return a-b;});
			for (var i=0;i<arr.length;i++) {
				if (arr[i] != (i + 1)) {
					num = (i + 1);
					break;
				}
			}
			if (num == 0) {
				num = arr.length + 1;
			}
			addTpl($('#detailList'), paramTrTpl.innerHTML,num,num);
			var cycle = $("#cycle").val();
			if(cycle == "year"){
				$(".sp").html("年");
			}else if(cycle == "season"){
				$(".sp").html("季");
			}else if(cycle == "mon"){
				$(".sp").html("月");
			}
		});

		//下拉框变更
		/*form.on('select(cycle)',function (obj) {
            if($("#ruleId").val() != null && $("#ruleId").val() != '') {
                var name = $('#name').val();
                var cycle = $('#cycle').val();
                $.ajax({
                    url: '#(ctxPath)/fina/rule/getRuleDetailByOption',
                    type: 'post',
                    data: {'name': name, 'cycle': cycle},
                    dataType: 'json',
                    success: function (returnData) {
                        if (returnData.result) {
                            $("#detailList").empty();
                            //重新加载表格内容
                            var detailList = returnData.data;
                            $('#paramCount').val(detailList.length);
                            var appendStr = "";
                            for (var i = 0; i < detailList.length; i++) {
                                appendStr += appenTr(detailList[i], i + 1);
                            }
                            $("#detailList").html(appendStr);
                            form.render();
                        }
                    }
                });
            }
        });*/

		//添加模板方法
		addTpl = function(targetId, addTpl,num,idx) {
			$('#paramCount').val(parseInt($('#paramCount').val())+1);
			laytpl(addTpl).render({"idx": (Number(idx)),"num":num}, function(html){
				targetId.append(html);
			});
			form.render();
		};


		//作废方法
		del = function(itemDetailTrId, itemDetailId) {
			if(itemDetailId!=null && itemDetailId!=''){
				layer.confirm('您确定要作废当前规则明细？', {icon: 3, title:'询问'}, function(index){
					//作废操作
					util.sendAjax({
						url:"#(ctxPath)/fina/rule/delRuleDetail?id="+itemDetailId,
						type:'post',
						data:{},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								$("#"+itemDetailTrId).remove();
							}
							layer.close(index);
						}
					});


				});
			}else{
				$("#"+itemDetailTrId).remove();
				//var paramCount = $('#paramCount').val() -1;
				//$('#paramCount').val(paramCount);
			}
		};


		// 保存规则
		form.on('submit(saveBtn)',function(){
			if($("#ruleId").val() != null && $("#ruleId").val() != ''){
				$("#cycle").attr("disabled",false);
			}
			util.sendAjax({
				url:"#(ctxPath)/fina/rule/save",
				type:'post',
				data:$("#frm").serialize(),
				notice:true,
				success:function(returnData){
					if(returnData.state==='ok'){
						parent.ruleTableReload();
						pop_close();
					}
				}
			});
			return false;
		});


		//加载表格内容
		loadItemDetailTable=function(){
			var ruleId = $('#ruleId').val();
			var flag = 0;
			util.sendAjax({
				url:'#(ctxPath)/fina/rule/getRuleDetailByOption',
				type:'post',
				data:{'id':ruleId,'flag':flag},
				notice:false,
				success:function(returnData){
					if(returnData.state==='ok'){
						$("#detailList").empty();
						//重新加载表格内容
						var detailList=returnData.data;
						$('#paramCount').val(detailList.length);
						var appendStr="";
						for(var i=0;i<detailList.length;i++){
							appendStr+=appenTr(detailList[i],detailList[i].timeSeq);
							if(i == detailList.length - 1){
								$('#paramCount').val(detailList[i].timeSeq);
							}
						}
						$("#detailList").html(appendStr);
						var cycle = $("#cycle").val();
						if(cycle == "year"){
							$(".sp").html("年");
						}else if(cycle == "season"){
							$(".sp").html("季");
						}else if(cycle == "mon"){
							$(".sp").html("月");
						}
						form.render();
					}
				}
			});
		}


		appenTr=function(detail,index){
			var trStr='<tr id="itemDetail-'+index+'">'
					+'<td>'
					+'<span style="float:left;margin-top: 10px;">第</span>'
					+'<div class="layui-input-inline" style="width:80px;margin-left:10px;">'
					+'<input type="text" name="itemDetailList['+index+'].timeSeq" class="layui-input" lay-verify="required|number" value="'+detail.timeSeq+'" placeholder="时间序号" readonly>'
					+'</div>'
					+'<span class="sp"></span>'
					+'</td>'
					+'<td>'
					+'<div class="layui-input-inline" style="width:140px;">'
					+'<input type="text" name="itemDetailList['+index+'].giveTimes" class="layui-input" lay-verify="required|checkNumber" value="'+detail.giveTimes+'" placeholder="赠送天数">'
					+'</select>'
					+'</div>'
					+'</td>'
					+'<td>'
					+'<div class="layui-input-inline" style="width:140px;">'
					+'<input type="text" name="itemDetailList['+index+'].giveAmount" class="layui-input" lay-verify="required|number" value="'+detail.giveAmount+'" placeholder="赠送金额">'
					+'</select>'
					+'</div>'
					+'</td>'
					+'<td>'
					+'<div class="layui-input-inline" style="width:100px;">'
					+'<input type="hidden" name="itemDetailList['+index+'].id" value="'+detail.id+'">'
					+'<input type="hidden" name="itemDetailList['+index+'].ruleId" value="'+detail.ruleId+'">'
					+'<a class="layui-btn layui-btn-danger layui-btn-xs" onclick=\'del("itemDetail-'+index+'","'+detail.id+'")\'>作废</a>'
					+'</div>'
					+'</td>'
					+'</tr>'
			return trStr;
		}

		$(function(){
			loadItemDetailTable();
		})
	});

</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="padding:15px;border-bottom: none;" action="" id="frm">
	<div class="layui-form-item">
		<div class="layui-inline" style="margin-left: 20px;">
			<label class="layui-form-label" >规则名称</label>
			<div class="layui-input-inline">
				<input type="text" id="name" name="rule.name" value="#(rule.name??)" required lay-verify="required" placeholder="请输入规则名称" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline" style="margin-left: 150px;">
			<label class="layui-form-label">赠送周期</label>
			<div class="layui-input-inline">
				<select name="rule.cycle" lay-verify="required" id="cycle" lay-filter="cycle">
					<!--<option value=""></option>-->
					#getDictList("give_cycle")
					<option value="#(key)" #(rule != null ?(key == rule.cycle ? 'selected':''):'selected')>#(value)</option>
					#end
				</select>
			</div>
		</div>
	</div>
	<!---->
	<fieldset class="layui-elem-field">
		<legend style="font-size: 14px;"><b>规则明细</b></legend>
		<div class="layui-form-item" style="margin-left:20px;margin-top: 10px;">
			<div class="layui-inline">
				<div class="layui-btn  layui-btn-sm " type="button" id="addDetail">添加规则明细</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-tab-item layui-show">
				<table class="layui-table" >
					<colgroup>
						<col width="20%">
						<col width="20%">
						<col width="20%">
						<col width="20%">
					</colgroup>
					<thead>
					<tr>
						<th>时间序号</th>
						<th>赠送天数</th>
						<th>赠送金额</th>
						<th>操作</th>
					</tr>
					</thead>
					<tbody id="detailList" ></tbody>
				</table>
			</div>
		</div>
	</fieldset>
	<div class="layui-form-footer">
		<div class="pull-right">
			<input type="hidden" id="ruleId" name="rule.id" value="#(rule.id??)">
			<input type="hidden" id="paramCount" name="paramCount" value="0">
			<div class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</div>
			<div class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</div>
		</div>
	</div>
</form>
#end