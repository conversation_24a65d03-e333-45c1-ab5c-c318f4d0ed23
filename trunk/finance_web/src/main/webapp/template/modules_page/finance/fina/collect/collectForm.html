#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()收款记录表单页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form id="frm" class="layui-form layui-form-pane" action="" lay-filter="layform" method="post">
		<div class="layui-row">
			<table class="layui-table" lay-skin="nob">
				<colgroup>
					<col width="50%">
					<col width="50%">
				</colgroup>
				<tbody>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>收款日期</label>
						<div class="layui-input-block">
							<input type="text" id="collectDate" name="collectDate" value="#(model.collectDate??)"  placeholder="请选择收款日期" class="layui-input" lay-verify="required">
						</div>
					</td>
					<td rowspan="4" text-align="center">
						<div class="layui-upload" align="center">
							<div class="layui-upload-list">
								<input type="hidden" id="uploadPath" name="receiptPic" value="#(model.receiptPic??)">
								<img class="layui-upload-img" id="profilePhotoImg" src="#(model.receiptPic??)" width="230px;" height="170px;" style="max-width:230px;">
							</div>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>收款类型</label>
						<div class="layui-input-block">
							<select id="collectType" name="collectType"lay-verify="required">
								#statusOption(com.cszn.integrated.service.entity.status.CollectType::me(), model.collectType??"")
							</select>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>收款账号</label>
						<div class="layui-input-block">
							<select id="collectAccount" name="collectAccount">
	        					<option value="">请选择收钱账户</option>
	        					#for(account : accountList)
	        						#if(account.payWay??=="1")
	        							#set(payWay = "(现金)")
	        						#else if(account.payWay??=="2")
	        							#set(payWay = "(微信)")
	        						#else if(account.payWay??=="3")
	        							#set(payWay = "(支付宝)")
	        						#else if(account.payWay??=="4")
	        							#set(payWay = "(信用卡)")
	        						#else if(account.payWay??=="5")
	        							#set(payWay = "(会员卡)")
	        						#else if(account.payWay??=="6")
	        							#set(payWay = "(Pos机)")
	        						#else if(account.payWay??=="7")
	        							#set(payWay = "(转账)")
	        						#else if(account.payWay??=="8")
	        							#set(payWay = "(企业微信)")
	        						#end
	        						<option value="#(account.id)" #if(account.id==model.collectAccount??'')selected="selected"#end>#(payWay)#(account.accountName)</option>
	        					#end
	        				</select>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>收款方式</label>
						<div class="layui-input-block">
							<select id="collectWay" name="collectWay"lay-verify="required">
								#statusOption(com.cszn.integrated.service.entity.status.PayWay::me(), model.collectWay??"")
							</select>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>收款金额</label>
						<div class="layui-input-block">
							<input type="text" name="collectAmount" value="#(model.collectAmount??)" autocomplete="off" placeholder="请输入收款金额" class="layui-input">
						</div>
					</td>
					<td>
						<label class="layui-form-label"><font color="red">*</font>资金状态</label>
						<div class="layui-input-block">
							<select id="fundStatus" name="fundStatus"lay-verify="required">
								#statusOption(com.cszn.integrated.service.entity.status.FundStatus::me(), model.fundStatus??"")
							</select>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>收据编号</label>
						<div class="layui-input-block">
							<input type="text" name="receiptNumber" value="#(model.receiptNumber??)" autocomplete="off" placeholder="请输入收据编号" class="layui-input">
						</div>
					</td>
					<td>
						<label class="layui-form-label"><font color="red">*</font>摘要</label>
						<div class="layui-input-block">
							<input type="text" name="summary" value="#(model.summary??)" autocomplete="off" placeholder="请输入摘要" class="layui-input" >
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>分公司</label>
						<div class="layui-input-block">
							<select id="branchOfficeId" name="branchOfficeId" lay-verify="required" lay-search>
								<option value="">请选择分公司</option>
								#for(b : branchOfficeList)
									<option value="#(b.id)" #if(b.id==model.branchOfficeId??'')selected="selected"#end>#(b.shortName)</option>
								#end
							</select>
						</div>
					</td>
					<td>
						<label class="layui-form-label"><font color="red">*</font>销售人员</label>
						<div class="layui-input-block">
							<select id="salesId" name="salesId" lay-verify="required" lay-search>
								<option value="">请选择销售人员</option>
								#for(u : userList)
									<option value="#(u.id)" #if(u.id==model.salesId??'')selected="selected"#end>#(u.name)</option>
								#end
							</select>
						</div>
					</td>
				</tr>
				<tr>
					<td>
						<label class="layui-form-label"><font color="red">*</font>客户姓名</label>
						<div class="layui-input-block">
							<input type="text" name="customerName" value="#(model.customerName??)" autocomplete="off" placeholder="请输入客户姓名" class="layui-input">
						</div>
					</td>
					<td>
						<label class="layui-form-label"><font color="red">*</font>客户身份证</label>
						<div class="layui-input-block">
							<input type="text" name="customerIdcard" value="#(model.customerIdcard??)" autocomplete="off" placeholder="请输入客户身份证" class="layui-input" >
						</div>
					</td>
				</tr>
				<tr>
					<td colspan="2">
						<label class="layui-form-label">备注</label>
						<div class="layui-input-block">
							<input type="text" name="remarks" value="#(model.remarks??)" autocomplete="off" placeholder="请输入备注" class="layui-input">
						</div>
					</td>
				</tr>
				</tbody>
			</table>
		</div>
		<div class="layui-form-item" style="height:50px;"></div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??)" />
				<button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button type="button" id="profilePhoto" class="layui-btn">上传收据</button>
				<button type="button" id="confirmBtn" class="layui-btn" lay-submit="" lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
	layui.use(['form', 'laydate', 'upload'],function(){
		var form = layui.form;
		var $ = layui.$;
		var laydate = layui.laydate;
		var upload = layui.upload;

		//时间渲染
		laydate.render({
			elem : '#collectDate'
		});

		//保存
		form.on('submit(confirmBtn)', function(){
			var url = "#(ctxPath)/fina/collect/save";
			util.sendAjax ({
				type: 'POST',
				url: url,
				data: $("#frm").serialize(),
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
// 						parent.collectLoad(null);
						parent.layui.table.reload('collectTable');
					}
				},
				complete : function() {
				}
			});
			return false;
		});

		//普通图片上传
		var uploadInst = upload.render({
			elem: '#profilePhoto'
			,url:'#(commonUpload)/upload?bucket=profilePhoto'
			,before: function(obj){
				$("#confirmBtn").attr("disabled",true);
				$("#confirmBtn").addClass("layui-btn-disabled");
				//预读本地文件示例，不支持ie8
				obj.preview(function(index, file, result){
					$('#profilePhotoImg').attr('src', result);
				});
			}
			,done: function(res){
				$("#confirmBtn").attr("disabled",false);
				$("#confirmBtn").removeClass("layui-btn-disabled");
				//如果上传失败
				if(res.state == 'ok'){
					$("#uploadPath").val(res.data.src);
					$('#profilePhotoImg').attr('src', res.data.src);
					layer.msg(res.msg,{icon:1,time:5000});

				}else {
					return layer.msg('上传失败');
				}

			}
			,error: function(){
				//演示失败状态，并实现重传
				var demoText = $('#demoText');
				demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
				demoText.find('.demo-reload').on('click', function(){
					uploadInst.upload();
				});
			}
		});
	});
</script>
#end