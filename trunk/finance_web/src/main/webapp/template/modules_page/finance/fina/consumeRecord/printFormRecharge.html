#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡消费小票打印页面#end

#define css()
<style>
    table tr td:nth-child(1) {
        width:150px;
        text-align: right;
    }
    table tr td:nth-child(2) {
        text-align: left;
    }
    #aa{
        display: none;
    }
    @media print{
        #compForm{
            font-size: 8px;
            width:300px;
            height:350px;
        }
        #compForm table{
            border:0px;
            cell-padding:0;
            cell-spacing:0;
        }
        #aa{
            display: block;
            width: 100%;
            height: auto;
            word-wrap:break-word;
            word-break:break-all;
            overflow: hidden;
        }
        #bb{
            display: none;
        }
    }
</style>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/jQuery.print.js"></script>
<script type="text/javascript">
    layui.use(['form','laydate','layer'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var layer=layui.layer;

        $('#printBtn').on('click', function() {
            $("#aa").text($("#val").val());
            $("#compForm table").removeClass("layui-table");
            $.print('#compForm');
            pop_close();
        });

    });
</script>
#end

#define content()
<body class="v-theme">
<input type="hidden" id="type" value="#(type??)"/>
<form class="layui-form" style="margin-top: -10px;width:430px;">
    <div id="compForm" style="text-align: center;">
        <table class="layui-table">
            <tr>
                <td>充值时间：</td>
                <td>#(record.dealTime??)</td>
            </tr>

            <tr>
                <td>操作员：</td>
                <td>#(record.operator??)</td>
            </tr>

            <tr>
                <td>业务类型：</td>
                <td>#(record.serviceType??)</td>
            </tr>

            #if(record.amount?? != null && record.amount?? != 0.00)
            <tr>
                <td>充值金额：</td>
                <td>#(record.amount??)元</td>
            </tr>
            #end

            #if(record.consumeTimes?? != null && record.consumeTimes?? != 0.00)
            <tr>
                <td>充值天数：</td>
                <td>#(record.consumeTimes??)天</td>
            </tr>
            #end

            #if(record.points?? != null && record.points?? != 0.00)
            <tr>
                <td>充值点数：</td>
                <td>#(record.points??)</td>
            </tr>
            #end

            #if(record.integrals?? != null && record.integrals?? != 0.00)
            <tr>
                <td>充值积分：</td>
                <td>#(record.integrals??)</td>
            </tr>
            #end

            #if(record.amount?? != null && record.amount?? != 0.00)
            <tr>
                <td>到账金额：</td>
                <td>#(record.amount??)元</td>
            </tr>
            #end

            #if(record.consumeTimes?? != null && record.consumeTimes?? != 0.00)
            <tr>
                <td>到账天数：</td>
                <td>#(record.consumeTimes??)天</td>
            </tr>
            #end

            <!--#if(record.points?? != null && record.points?? != 0.00)
            <tr>
                <td>到账点数：</td>
                <td>#(record.points??)</td>
            </tr>
            #end-->

            #if(record.integrals?? != null && record.integrals?? != 0.00)
            <tr>
                <td>到账积分：</td>
                <td>#(record.integrals??)</td>
            </tr>
            #end

            #if(record.giveAmount?? != null && record.giveAmount?? != 0.00)
            #if(record.giveConsumeTimes?? == null || record.giveConsumeTimes == 0.00)
            <tr>
                <td>其他：</td>
                <td>赠送金额：#(record.giveAmount??)元</td>
            </tr>
            #end
            #end

            #if(record.giveConsumeTimes?? != null && record.giveConsumeTimes?? != 0.00)
            #if(record.giveAmount?? == null || record.giveAmount == 0.00)
            <tr>
                <td>其他：</td>
                <td>赠送天数：#(record.giveConsumeTimes??)天</td>
            </tr>
            #end
            #end

            <!--#if(record.giveConsumePoints?? != null && record.giveConsumePoints?? != 0.00)
            <tr>
                <td>其他：</td>
                <td>赠送点数：#(record.giveConsumePoints??)</td>
            </tr>
            #end-->


            #if(record.giveConsumeTimes?? != null && record.giveConsumeTimes?? != 0.00 && record.giveAmount?? != null && record.giveAmount?? != 0.00 && record.giveConsumePoints?? != null && record.giveConsumePoints?? != 0.00)
            <tr>
                <td>其他：</td>
                <td>赠送金额：#(record.giveAmount??)元 ,赠送天数：#(record.giveConsumeTimes??)天 ,赠送点数：#(record.giveConsumePoints??)</td>
            </tr>
            #end


            <tr>
                <td>会员卡号：</td>
                <td>#(record.cardNumber??)</td>
            </tr>
            <tr>
                <td>会员姓名：</td>
                <td>#(record.fullName??)</td>
            </tr>
            #if(record.balance?? != null && record.deductWay??=="deduct_by_money")
            <tr>
                <td>剩余余额：</td>
                <td>#(record.balance??)元</td>
            </tr>
            #end

            #if(record.cardTimes?? != null && record.deductWay??=="deduct_by_days")
            <tr>
                <td>剩余天数：</td>
                <td>#(record.cardTimes??)天</td>
            </tr>
            #end

            <!--#if(record.consumePoints?? != null && record.consumePoints?? != 0.00)
            <tr>
                <td>剩余点数：</td>
                <td>#(record.consumePoints??)</td>
            </tr>
            #end-->

            #if(record.cardIntegrals?? != null && record.isIntegral?? =="1")
            <tr>
                <td>剩余积分：</td>
                <td>#(record.cardIntegrals??)</td>
            </tr>
            #end

            #if(record.lockAmount?? != null && record.deductWay??=="deduct_by_money")
            <tr>
                <td>锁定金额：</td>
                <td>#(record.lockAmount??)元</td>
            </tr>
            #end

            #if(record.lockTimes?? != null && record.deductWay??=="deduct_by_days")
            <tr>
                <td>锁定天数：</td>
                <td>#(record.lockTimes??)天</td>
            </tr>
            #end

            <!--#if(record.lockPoints?? != null && record.lockPoints?? != 0.00)
            <tr>
                <td>锁定点数：</td>
                <td>#(record.lockPoints??)</td>
            </tr>
            #end-->

            #if(record.lockIntegrals?? != null && record.isIntegral?? =="1")
            <tr>
                <td>锁定积分：</td>
                <td>#(record.lockIntegrals??)</td>
            </tr>
            #end

            <tr>
                <td>备注：</td>
                <td><p id="bb"><textarea class="layui-textarea" rows="3" id="val">#(record.describe??)</textarea></p>
                    <p id="aa"></p>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;">敬请保留小票!</td>
            </tr>
            <tr>
                <td colspan="2" style="text-align: center;">打印时间：#(record.printTime??)</td>
            </tr>
        </table>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关闭</button>
            <button type="button" class="layui-btn" id="printBtn">打印</button>
        </div>
    </div>
</form>
</body>
#end