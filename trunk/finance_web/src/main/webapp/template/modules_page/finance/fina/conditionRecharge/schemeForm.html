#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()赠送方案编辑页面#end

#define css()

<style>
	.layui-table td, .layui-table th {
		position: relative;
		padding: 9px 3px;
		min-height: 20px;
		line-height: 20px;
		font-size: 14px;
	}
</style>

#end

#define js()
<script id="ruleTrTpl" type="text/html">
<tr id="rule-{{d.idx}}">
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].conditionValue" class="layui-input" lay-verify="required|number" value="#(rule.conditionValue??)" placeholder="满足值">
		</div>

	</td>
	<td>

		<div class="layui-input-inline">
			<select name="ruleList[{{d.idx}}].rechargeType" lay-verify="required" lay-filter="">
				<option value="">请选择赠送资产</option>
				#for(type : conditionRechargeTypes)
				<option value="#(type.key)"  >#(type.value??)</option>
				#end
			</select>
		</div>

	</td>
	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].rechargeValue" class="layui-input" lay-verify="required|number" value="0" placeholder="赠送值">
		</div>
	</td>

	<td>
		<div class="layui-input-inline">
			<input type="text" name="ruleList[{{d.idx}}].remark" class="layui-input" value="" placeholder="请输入备注">
		</div>
	</td>
	<td>
		<input type="hidden" name="ruleList[{{d.idx}}].id" value="#(rule.id??)">
		<input type="hidden" name="ruleList[{{d.idx}}].schemeId" value="#(rule.scheme_id??)">
		<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delRule('rule-{{d.idx}}','')">作废</a>
	</td>
</tr>

</script>
<script type="text/javascript">
layui.use([ 'form', 'laytpl' ], function() {
	var form = layui.form
	, laytpl = layui.laytpl
	, $ = layui.jquery
	;
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		var schemeId = '#(model.Id??)';
		$('#ruleCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx": (parseInt(idx)+1), "schemeId": schemeId}, function(html){
			targetId.append(html);
		});
		form.render('select');
    };
    
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		addTpl($('#ruleList'), ruleTrTpl.innerHTML, $('#ruleCount').val());
	});
	
	//删除方法
    delRule = function(ruleTrId, ruleId) {
   		if(ruleId!=null && ruleId!=''){
			layer.confirm('您是否要删除当前规则？', {icon: 3, title:'询问'}, function(index){
				//删除操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/fina/conditionRecharge/delDetail',
		            data: {id:ruleId, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	$("#"+ruleTrId).remove();
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
   		}else{
	    	$("#"+ruleTrId).remove();
   		}
    };
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/fina/conditionRecharge/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">


	<div class="layui-form-item" style="margin-top: 10px;">
		<label class="layui-form-label"><font color="red">*</font>方案名称</label>
		<div class="layui-input-block"  >
			<input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" maxlength="50" placeholder="请输入方案名称">
		</div>
	</div>

	<div class="layui-form-item" style="margin-top: 10px;">
		<label class="layui-form-label"><font color="red">*</font>条件类型</label>
		<div class="layui-input-block"  >
			<select id="conditionType" name="conditionType" lay-verify="required">
				<option value="">请选择条件类型</option>
				#for(type : conditionTypes)
				<option value="#(type.key)" #if(model.conditionType??==type.key) selected #end >#(type.value??)</option>
				#end
			</select>
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea" rows="4" placeholder="请输入备注">#(model.remark??)</textarea>
		</div>
	</div>
	<div class="layui-row" style="">
		<table class="layui-table">

			<thead>
				<tr>
					<th>满足值</th>
					<th>赠送资产类型</th>
					<th>赠送值</th>
					<th>备注</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody id="ruleList">
				#for(rule : list??)
					<tr id="rule-#(for.index+1)">
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].conditionValue" class="layui-input" lay-verify="required|number" value="#(rule.conditionValue??)" placeholder="满足值">
							</div>

						</td>
						<td>

							<div class="layui-input-inline">
								<select name="ruleList[#(for.index+1)].rechargeType" lay-verify="required" lay-filter="">
									<option value="">请选择赠送资产</option>
									#for(type : conditionRechargeTypes)
									<option value="#(type.key)" #if(rule.rechargeType??==type.key) selected #end >#(type.value??)</option>
									#end
								</select>
							</div>

						</td>
						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].giveTimes" class="layui-input" lay-verify="required|number" value="#(rule.rechargeValue??)" placeholder="赠送值">
							</div>
						</td>

						<td>
							<div class="layui-input-inline">
								<input type="text" name="ruleList[#(for.index+1)].remark" class="layui-input" value="#(rule.remark??)" placeholder="请输入备注">
							</div>
						</td>
						<td>
							<input type="hidden" name="ruleList[#(for.index+1)].id" value="#(rule.id??)">
							<input type="hidden" name="ruleList[#(for.index+1)].schemeId" value="#(rule.schemeId??)">
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delRule('rule-#(for.index+1)','#(rule.id??)')">作废</a>
						</td>
					</tr>
				#end
			</tbody>
		</table>
	</div>
	<div style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<input type="hidden" id="ruleCount" name="ruleCount" value="#(list.size()??0)">
			<button type="button" id="addBtn" class="layui-btn">添加规则</button>
			<button type="button" class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end