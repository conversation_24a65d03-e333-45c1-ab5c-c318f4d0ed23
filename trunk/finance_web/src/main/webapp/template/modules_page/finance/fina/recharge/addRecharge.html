#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡充值详情页面#end

#define css()

#end

#define content()
<div style="margin: 15px;">
	<div class="demoTable">
		<form class="layui-form layui-form-pane" action="" method="post" id="rechargeForm">
			<div class="layui-form-item">
				<label class="layui-form-label">充值时间</label>
				<div class="layui-input-block">
					<input type="text" id="rechargeTime" name="recharge.rechargeTime" class="layui-input" lay-verify="required" value="" autocomplete="off">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">充值类型</label>
				<div class="layui-input-block">
					<input type="radio" name="recharge.type" value="1" title="充值金额" lay-filter="type" checked>
					<input type="radio" name="recharge.type" value="2" title="充值天数" lay-filter="type">
					<input type="radio" name="recharge.type" value="3" title="充值点数" lay-filter="type">
					<input type="radio" name="recharge.type" value="4" title="充值积分" lay-filter="type">
					<input type="radio" name="recharge.type" value="5" title="充值豆豆券" lay-filter="type">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">分类</label>
				<div class="layui-input-block">
					<select name="recharge.classify" id="classify" lay-verify="required" >
						<option value="">请选择分类</option>
						#getDictList("recharge_classify")
						<option value="#(key)" >#(value)</option>
						#end
						<!--<option value="money_recharge">金额充值</option>
						<option value="upgrade_recharge">升级充值</option>
						<option value="give_recharge">赠送充值</option>
						<option value="luck_draw_recharge">抽奖充值</option>
						<option value="paper_card_recharge">纸卡充值</option>-->
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" id="amountTime"></label>
				<div class="layui-input-block">
					<input type="text" id="amountTimeInput" class="layui-input" value="" autocomplete="off">
				</div>
			</div>

            <div class="layui-form-item">
                <label class="layui-form-label" >收入类型</label>

                <div class="layui-input-block">
                    <input type="radio" name="recharge.isIncome" value="1" title="收入性" lay-filter="isIncome" checked>
                    <input type="radio" name="recharge.isIncome" value="0" title="非收入性" lay-filter="isIncome">
                </div>
            </div>

			<div class="layui-form-item">
				<label class="layui-form-label" >是否收现金</label>

				<div class="layui-input-block">
					<input type="radio" name="recharge.isCash" value="0" title="否" lay-filter="isCash"  checked>
					<input type="radio" name="recharge.isCash" value="1" title="是" lay-filter="isCash"  >
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 5px;">现金收款金额</label>
				<div class="layui-input-block">
					<input type="text" id="cashValue" name="recharge.cashValue" class="layui-input" placeholder="请输入现金收款金额" value="0" autocomplete="off" lay-verify="required|checkAmount">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" >收钱账户</label>
				<div class="layui-input-block" >
					<select name="recharge.accountId" id="rechargeAccountId" >
						<option value="">请选择收钱账号</option>
						#for(account : accountList)
						#if(account.payWay??=="1")
						#set(payWay = "(现金)")
						#else if(account.payWay??=="2")
						#set(payWay = "(微信)")
						#else if(account.payWay??=="3")
						#set(payWay = "(支付宝)")
						#else if(account.payWay??=="4")
						#set(payWay = "(信用卡)")
						#else if(account.payWay??=="5")
						#set(payWay = "(会员卡)")
						#else if(account.payWay??=="6")
						#set(payWay = "(Pos机)")
						#else if(account.payWay??=="7")
						#set(payWay = "(转账)")
						#else if(account.payWay??=="8")
						#set(payWay = "(企业微信)")
						#end
						<option value="#(account.id)" >#(payWay)#(account.accountName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 5px;">账户收款金额</label>
				<div class="layui-input-block">
					<input type="text" id="accountValue" name="recharge.accountValue" class="layui-input" value="0" placeholder="请输入账户收款金额" autocomplete="off" lay-verify="required|checkAmount">
				</div>
			</div>

			<!--缴款金额-->
			<div class="layui-form-item">
				<label class="layui-form-label">总缴款金额</label>
				<div class="layui-input-block">
					<input type="text" id="payAmount" name="recharge.payAmount" value="0" class="layui-input" placeholder="请输入缴款金额" autocomplete="off" lay-verify="required|checkAmount">
				</div>
				<div class="layui-form-mid" style="color:red;font-size: 10px;margin-left: 115px;">缴款金额用于开具发票,请谨慎填写/退还或赠送时则填写0</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label">充值编号</label>
				<div class="layui-input-block">
					<input type="text" name="recharge.rechargeNo" class="layui-input" placeholder="请输入充值编号" autocomplete="off" lay-verify="required">
				</div>
			</div>

			<div class="layui-form-item" style="display: none;">
				<label class="layui-form-label">赠送金额</label>
				<div class="layui-input-block">
					<input type="text" name="recharge.giveAmount" class="layui-input" lay-verify="checkAmount" placeholder="请输入赠送金额">
				</div>
			</div>
			<div class="layui-form-item" style="display: none;">
				<label class="layui-form-label">赠送天数</label>
				<div class="layui-input-block">
					<input type="text" name="recharge.giveConsumeTimes" class="layui-input" lay-verify="checkNumber" placeholder="请输入赠送天数">
				</div>
			</div>
			<div class="layui-form-item" style="display: none;">
				<label class="layui-form-label">赠送点数</label>
				<div class="layui-input-block">
					<input type="text" name="recharge.giveConsumePoints" class="layui-input" lay-verify="checkNumber" placeholder="请输入赠送点数">
				</div>
			</div>
			<div class="layui-form-item" style="display: none;">
				<label class="layui-form-label">赠送积分</label>
				<div class="layui-input-block">
					<input type="text" name="recharge.giveConsumeIntegral" class="layui-input" lay-verify="checkNumber" placeholder="请输入赠送积分">
				</div>
			</div>
			<div class="layui-form-item" style="display: none;">
				<label class="layui-form-label">赠送豆豆券</label>
				<div class="layui-input-block">
					<input type="text" name="recharge.giveBeanCoupons" class="layui-input" lay-verify="checkNumber" placeholder="请输入赠送豆豆券">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">说明</label>
				<div class="layui-input-block">
					<textarea type="text" name="recharge.describe" class="layui-textarea" rows="5" placeholder="请输入说明"></textarea>
				</div>
			</div>
			<div class="layui-row" style="margin-bottom: 60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<input type="hidden" id="cardId" name="recharge.cardId" value="#(cardId??)">
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button class="layui-btn" id="saveBtn" lay-submit="" lay-filter="saveBtn">确认充值</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script>
	layui.use(['form','laytpl','layer', 'laydate'], function() {
		var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,laydate = layui.laydate;

		laydate.render({
			elem : '#rechargeTime',
			type: 'date',
			trigger: 'click',
			value:new Date(),
			type:'date',
			done:function () {
			}
		});

		form.render();
		
		//校验
		form.verify({
			checkAmount:function(value){
				if(value != null){
					var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
					var reg2 = new RegExp("^[0-9]*$");
					if(!reg1.test(value) && !reg2.test(value)){
						return "只能输入数字和小数点后两位小数";
					}
				}
			},
			checkNumber:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
				}
			}
		});

		//保存
		form.on('submit(saveBtn)', function(obj){
			var isIncome=obj.field["recharge.isIncome"];
			if(isIncome=='1'){
				var accountId=obj.field["recharge.accountId"];
				var isCash=obj.field["recharge.isCash"];
				if(accountId=='' && isCash=='0'){
					layer.msg('收入性充值，请至少选择一项收钱方式!', {icon: 2, offset: 'auto'});
					return false;
				}
			}
			$("#saveBtn").attr("disabled",true);
			$("#saveBtn").addClass("layui-btn-disabled");

			util.sendAjax({
				url:"#(ctxPath)/fina/recharge/save",
				type:'post',
				data:$("#rechargeForm").serialize(),
				notice:true,
				loadFlag:true,
				success:function(returnData){
					if(returnData.state=='ok'){
						pop_close();
						//parent.findCardInfoById($("#cardId").val());
						parent.reloadCardByCardNumber();
						//parent.consumeRecordTableReload();
					}
				},
				complete :function(){
					$("#saveBtn").attr("disabled",false);
					$("#saveBtn").removeClass("layui-btn-disabled");
				}
			});
			return false;
		});

		//
		$(function(){
			#if(isIntegral??=="1")
			$.post('#(ctxPath)/fina/integralRechargeConfig/getConfigList',{},function (returnData){
				if(returnData.length>0){
					var users=[];
					$.each(returnData,function (index,item){
						users.push({"value":item.rechargeDays,"price":item.productPrice});
					});
					autoShowUser(users,'#amountTimeInput') ;
				}
			});
			#end


			if($("input[name='recharge.type']").val() == '1'){
				$("#amountTime").text("金额");
				$("#amountTimeInput").attr("placeholder","请输入金额");
				$("#amountTimeInput").attr("name","recharge.amount");
				$("#amountTimeInput").attr("lay-verify","required|checkAmount");
				form.render();
			}
		});

		//单选框切换事件
		form.on("radio(type)",function(obj){
			$("input[name!='recharge.cardId'][name!='recharge.type'][name!='recharge.rechargeTime']" +
					"[name!='recharge.isCash'][name!='recharge.isIncome'][name!='recharge.cashValue'][name!='recharge.accountValue']" +
					"[name!='recharge.payAmount']").val("");
			if(obj.value == '1'){
				$("#amountTime").text("金额");
				$("#amountTimeInput").attr("name","recharge.amount");
				$("#amountTimeInput").attr("placeholder","请输入金额");
				$("#amountTimeInput").attr("lay-verify","required|checkAmount");
			}else if(obj.value == '2'){
				$("#amountTime").text("获得天数");
				$("#amountTimeInput").attr("name","recharge.consumeTimes");
				$("#amountTimeInput").attr("placeholder","请输入获得天数");
				$("#amountTimeInput").attr("lay-verify","required|checkAmount");
			}else if(obj.value=='3'){
				$("#amountTime").text("点数");
				$("#amountTimeInput").attr("name","recharge.consumePoints");
				$("#amountTimeInput").attr("placeholder","请输入点数");
				$("#amountTimeInput").attr("lay-verify","required|checkAmount");
			}else if(obj.value=='4'){
				$("#amountTime").text("积分");
				$("#amountTimeInput").attr("name","recharge.consumeIntegral");
				$("#amountTimeInput").attr("placeholder","请输入积分");
				$("#amountTimeInput").attr("lay-verify","required|checkAmount");
			}else if(obj.value=='5'){
				$("#amountTime").text("豆豆券");
				$("#amountTimeInput").attr("name","recharge.consumeBeanCoupons");
				$("#amountTimeInput").attr("placeholder","请输入豆豆券");
				$("#amountTimeInput").attr("lay-verify","required|checkAmount");
			}
			form.render();
		});



		form.on('radio(isCash)',function (obj){
			if(obj.value=='0'){
				/*$("#rechargeAccountId").attr("lay-verify","required");
				$("#rechargeAccountId").prop("disabled",false);*/
			}else if(obj.value=='1'){
				$("#rechargeAccountId").attr("lay-verify","");
				$("#rechargeAccountId").prop("disabled",true);
				$("#rechargeAccountId option:first").prop("selected","selected");
			}
			form.render();
		});

		form.on('radio(isIncome)',function (obj) {
			if(obj.value=='0'){
				$("#rechargeAccountId").attr("lay-verify","");
				$("#rechargeAccountId").prop("disabled",true);
				$("#rechargeAccountId option:first").prop("selected","selected");
				$("input[lay-filter='isCash'][value='0']").prop("checked","checked");
				$("input[lay-filter='isCash']").prop("disabled",true);

				$("#accountValue").val("0");
				$("#cashValue").val("0");
				$("#payAmount").val("0");

				$("#cashValue").prop("disabled",true);
				$("#accountValue").prop("disabled",true);
			}else if(obj.value=='1'){
				$("#rechargeAccountId").attr("lay-verify","required");
				$("#rechargeAccountId").prop("disabled",false);

				$("input[lay-filter='isCash']").prop("disabled",false);
				$("#cashValue").prop("disabled",false);
				$("#accountValue").prop("disabled",false);
			}

			form.render();
		})

		$("#cashValue").blur(function(){
			var accountValue=$("#accountValue").val();
			var cashValue=$("#cashValue").val();
			$("#payAmount").val(Number(accountValue)+Number(cashValue));

		});

		$("#accountValue").blur(function(){
			var accountValue=$("#accountValue").val();
			var cashValue=$("#cashValue").val();
			$("#payAmount").val(Number(accountValue)+Number(cashValue));
		});


		function createstyle(css){
			var mystyle=$('#mystyle');
			if(!mystyle.length>0){
				var style="<style id='mystyle'></style>";
				$(document).find('head').append(style);
				mystyle=$('#mystyle');
			}
			mystyle.append(css);
		}

		function autoShowUser(users,id) {

			id=id||"#amountTimeInput";
			var wd=$(id)[0].clientWidth;
			var hg=$(id)[0].clientHeight;
			var suggest='<ul id="suggest" style="display: block;z-index: 99999;position:absolute;width: '+wd+'px; background-color: white;padding: 0px;margin:0px;"></ul>';
			var css_li="#suggest li{display: block;height:35px;padding-top: 5px;font: 18px/35px '微软雅黑','黑体',sans-serif;margin: 0px;color:rgb(66, 139, 202);text-align: left;padding-left:13px}";
			css_li+="#suggest li:hover{cursor: pointer;margin: 0px;background-color: rgb(227,227,227);}";
			$(id).after(suggest);

			createstyle(css_li);
			$(id).bind('focus',function (e) {
				if($(id).attr("name")!='recharge.consumeTimes'){
					return false;
				}
				var name= $(this).val();
				var num=0;
				/*if(name!='' ){*/
					var html='';
					for (var i=0;i<users.length;i++){
						/*if(users[i].indexOf(name)>-1){
							html += "<li>" +users[i]+"</li>";
							num++;
						}*/

						html += "<li price='"+users[i].price+"'>" +users[i].value+"</li>";
						/*if(num>9){
							break;
						}*/
					}
					if(html !=''){
						$('#suggest').show();
						$('#suggest').html(html);
						$('#suggest li').css(css_li);
						$('#suggest li').bind('click',function(e){
							$(id).val($(e.target).html());
							$("#payAmount").val($(e.target).attr("price"));
							$('#suggest').hide();
						});
					}
				/*}else{
					$('#suggest').hide();
				}*/
			});

			$(id).blur(function () {
				setTimeout(function () {
					$('#suggest').hide();
				},150);
			})

		}


	});
</script>
#end