#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构养老费用账单批量结算首页#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .laytable-cell-checkbox .layui-disabled.layui-form-checked i {
        background: #fff !important;
    }
</style>
#end

#define content()
<div class="layui-collapse">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post" style="margin-left:25px;margin-top:15px;">
            <div class="layui-row">
                <!--会员卡号：
                <div class="layui-inline" style="width:215px;">
                    <input type="text" class="layui-input" id="chargeAccount" name="chargeAccount"/>
                </div>-->
                <span style="margin-left: 10px;">&nbsp;&nbsp;</span>
                &nbsp;&nbsp;
                入住人：
                <div class="layui-inline">
                    <input type="text" class="layui-input" id="memberName" name="memberName"/>
                </div>
                &nbsp;&nbsp;
                入住流水号：
                <div class="layui-inline">
                    <input type="text" class="layui-input" id="checkinNo" name="checkinNo"/>
                </div>
                &nbsp;&nbsp;
                月份：
                <div class="layui-inline">
                    <input type="text" class="layui-input" id="yearMonth" name="yearMonth"/>
                </div>
                &nbsp;&nbsp;
                结算状态：
                <div class="layui-inline">
                    <select name="settleStatus" id="settleStatus">
                        <option value="">请选择结算状态</option>
                        <option value="no_settle">未结算</option>
                        <option value="normal">已结算</option>
                    </select>
                </div>
                &nbsp;&nbsp;
                <button class="layui-btn" id="search" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
                #shiroHasPermission("finance:orgSettle:arrearsBtn")
                #end
                #shiroHasPermission("finance:orgSettle:batchSettlementExport")
                <button class="layui-btn" id="export" style="padding: 0 10px;border-radius: 5px;" type="button" >导出</button>
                #end
                <!--<button class="layui-btn" id="settleAllBtn" style="padding: 0 10px;border-radius: 5px;" type="button">结算全部</button>-->
            </div>
            &nbsp;&nbsp;
        </form>
    </div>
    <div class="layui-row">
        <table id="mainTable" class="layui-table" lay-filter="mainTable"></table>
    </div>
</div>
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#getDictLabel("gender")
#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        //年月选择器
        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,value:'#(yearMonth)'
        });

        mainTableReload=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'mainTable'
                ,elem : '#mainTable'
                ,method : 'POST'
                ,height:$(document).height()*0.85
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/orgSettle/batchSettlePageList'
                ,where: data
                ,cellMinWidth: 80
                ,cols: [[
                    {rowspan: 3,field:'bed_no', title: '房号', align: 'center', unresize: true}
                    ,{rowspan: 3,field:'member_name', title: '姓名', align: 'center', unresize: true}
                    ,{rowspan: 3,field:'year_month',title:'账单月份',align:'center',unresize:true}
                    ,{rowspan: 3,field:'year_month',title:'欠费金额',width: 95,align:'center',unresize:true,templet:function (d) {
                            var str="";
                            if(d.unSettleTimes!=undefined && d.unSettleTimes>0){
                                str+=d.unSettleTimes+"天";
                            }
                            if(d.unSettleAmount!=undefined && d.unSettleAmount>0){
                                str+=d.unSettleAmount+"元";
                            }
                            if(d.unSettleIntegrals!=undefined && d.unSettleIntegrals>0){
                                str+=d.unSettleIntegrals+"积分";
                            }
                            return str;

                        }}
                    ,{rowspan: 3,field:'year_month',title:'小计',width: 95,align:'center',unresize:true,templet:function (d) {
                            var str="";
                            if(d.totalTimes!=undefined && d.totalTimes>0){
                                str+=d.totalTimes+"天";
                            }
                            if(d.totalAmount!=undefined && d.totalAmount>0){
                                str+=d.totalAmount+"元";
                            }
                            if(d.totalIntegrals!=undefined && d.totalIntegrals>0){
                                str+=d.totalIntegrals+"积分";
                            }
                            return str;
                        }}
                    ,{colspan: 24,field:'',title:'应收明细',align:'center',unresize:true}
                    ,{rowspan: 3,fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ],
                    [
                        {colspan: 3,field:'chaungwei_amount',title:'床位费',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'电费(元)',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'护理费(元)',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'包房费(元)',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'卫生费(元)',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'管理费(元)',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'打包价(元)',align:'center',unresize:true}
                        ,{colspan: 3,field:'',title:'送餐费(元)',align:'center',unresize:true}
                    ]
                    ,[
                        {field:'chaungwei_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'chaungwei_amount',title:'收费',align:'center',unresize:true,templet:function (d) {
                                var str="";
                                if(d.chaungwei_amount!=undefined && d.chaungwei_amount>0){
                                    str+=d.chaungwei_amount+"元";
                                }
                                if(d.chaungwei_time!=undefined && d.chaungwei_time>0){
                                    str+=d.chaungwei_time+"天";
                                }
                                if(d.chaungwei_integrals!=undefined && d.chaungwei_integrals>0){
                                    str+=d.chaungwei_integrals+"积分";
                                }
                                return str;
                            }}
                        ,{field:'chaungwei_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.chaungwei_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.chaungwei_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'dianfei_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'dianfei_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'dianfei_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.dianfei_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.dianfei_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'huli_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'huli_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'huli_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.huli_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.huli_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'baofang_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'baofang_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'baofang_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.baofang_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.baofang_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'weisheng_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'weisheng_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'weisheng_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.weisheng_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.weisheng_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'guanli_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'guanli_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'guanli_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.guanli_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.guanli_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'dabao_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'dabao_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'dabao_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.dabao_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.dabao_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}

                        ,{field:'songcan_card_number',width: 120,title:'会员卡',align:'center',unresize:true}
                        ,{field:'songcan_amount',title:'收费',align:'center',unresize:true}
                        ,{field:'songcan_status',title:'状态',align:'center',unresize:true,templet:"<div>{{d.songcan_status=='settled'?'<span class='layui-badge layui-bg-green'>已结</span>':d.songcan_status=='wait_settlement'?'<span class='layui-badge'>待结</span>':''}}</div>"}
                    ]
                ]
                ,page : false
                ,done:function (res, curr, count) {
                    var data = res.data;
                    for (var k = 0; k < data.length; k++) {
                        var tr = $(".layui-table-body>.layui-table").find("tr").eq(k);
                        var tdArr=$(tr).find("td");
                        tdArr.each(function (index,item) {
                            var trName=$(this).attr("data-field");
                            if(trName.indexOf("chaungwei")!=-1){
                                $(this).attr("title",data[k].chaungwei_remark);
                            }else if(trName.indexOf("huli")!=-1){
                                $(this).attr("title",data[k].huli_remark);
                            }else if(trName.indexOf("weisheng")!=-1){
                                $(this).attr("title",data[k].weisheng_remark);
                            }else if(trName.indexOf("guanli")!=-1){
                                $(this).attr("title",data[k].guanli_remark);
                            }else if(trName.indexOf("dabao")!=-1){
                                $(this).attr("title",data[k].dabao_remark);
                            }else if(trName.indexOf("baofang")!=-1){
                                $(this).attr("title",data[k].baofang_remark);
                            }else if(trName.indexOf("songcan")!=-1){
                                $(this).attr("title",data[k].songcan_remark);
                            }else if(trName.indexOf("dianfei")!=-1){
                                $(this).attr("title",data[k].dianfei_remark);
                            }
                        })
                    }
                    layer.close(loadingIndex);
                }
            });
            table.on('tool(mainTable)',function(obj){
                if (obj.event === 'check') {
                    layerShow("查看账单","#(ctxPath)/fina/orgSettle/checkSettleMainIndex?checkinNo="+obj.data.checkinNo+"&memberName="+obj.data.memberName,'100%','100%');
                }else if(obj.event === 'settle'){
                    layui.layer.open({
                        type: 2,
                        area: ['100%', '100%'],
                        fix: true, //不固定
                        maxmin: true,
                        shade:0.4,
                        title: "机构养老消费结算",
                        content: "#(ctxPath)/fina/orgSettle/orgSettleMainIndex?checkinNo="+obj.data.checkinNo+"&memberName="+obj.data.memberName,
                        end:function () {
                            table.reload('mainTable',{'where':{'memberName':$("#memberName").val(),'checkinNo':$("#checkinNo").val(),
                                    'yearMonth':$("#yearMonth").val(),'settleStatus':$("#settleStatus").val()}});
                        }
                    });
                }else if(obj.event==='detail'){
                    layerShow("明细","#(ctxPath)/fina/orgSettle/orgBatchSettleDetailIndex?id="+obj.data.id,1400,600);
                }else if(obj.event==='settleBtn'){
                    settleBtnClick(obj.data.id);
                }
            });
        }

        //结算按钮点击事件
        settleBtnClick=function(id){
            layer.confirm('确定要结算吗?',function (index) {
                layer.load();
                //判断是否有数据
                var data=table.cache["table-"+id];
                /*if(data==null || data.length===0){
                    layer.msg('该账单没有要结算的项', {icon: 2, offset: 'auto'});
                    return false;
                }*/
                $.post('#(ctxPath)/fina/orgSettle/memberMainSettle',{"id":id},function (res) {
                    if(res.state==='ok'){
                        loadSettleMain();
                        $("#settleBtn").addClass("layui-btn-danger").prop("disabled",true).attr("lay-event","").text("已结算");
                        layer.msg(res.msg, {icon: 1, offset: 'auto'});
                        layer.closeAll('loading');
                    }else{
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                });
                layer.close(index);
            });
        }

        mainTableReload(null);

        $("#export").on('click',function () {
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '导出中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            var url="#(ctxPath)/fina/orgSettle/exportBatchSettlePageList?memberName="+$("#memberName").val()+"&checkinNo="+$("#checkinNo").val()
            +"&yearMonth="+$("#yearMonth").val()+"&settleStatus="+$("#settleStatus").val();
            window.location.href=url;

            setTimeout(function (){
                layer.close(loadingIndex);
            },6000);
        })

        form.on("submit(search)",function(data){
            mainTableReload(data.field);
            return false;
        });

        $("#arrears").on('click',function () {
            layerShow("查看账单","#(ctxPath)/fina/orgSettle/arrearsIndex",'100%','100%');
        })

        $("#settleAllBtn").on('click',function () {
            layer.load();
            $.post('#(ctxPath)/fina/orgSettle/batchSettleAll',{'memberName':$("#memberName").val(),'checkinNo':$("#checkinNo").val(),
                'yearMonth':$("#yearMonth").val(),'settleStatus':$("#settleStatus").val()},function (res) {
                if(res.state==='ok'){
                    layer.msg(res.msg, {icon: 1, offset: 'auto'});
                    layer.closeAll('loading');
                    mainTableReload();
                }else{
                    layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll('loading');
                }

            });
        });

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                mainTableReload();
                return false
            }
        }
    });
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="detail">明细</a>
    <a class="layui-btn layui-btn-xs " lay-event="settleBtn" >结算</a>
</script>
#end