#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()合同名称首页#end

#define css()
#end

#define js()
<script type="text/html" id="contractTableBar">
	#shiroHasPermission("finance:contract:edit")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end

</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'contractTable'
        ;
    
	// 表格渲染
	pageTableReload = function (data) {
		//loading层
		var loadingIndex = layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		table.render({
			id: tableId
			, elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
			, even: true //开启隔行背景
			, url: '#(ctxPath)/fina/contract/pageList'
			, where: data
			, method: 'post'
			, height: 'full-90'    //容器高度
			, cols: [[                  //标题栏
				{field: 'name', title: '合同名称', unresize:true}
				, {field: 'type', title: '合同类型', unresize:true,templet:function(d){
						if(typeof(d.contractType)=='undefined'){
							return '';
						}else{
							return d.contractType.type_name;
						}
					}}
				,{field: 'cardType', title: '会员卡类型', unresize:true,templet:function (d) {
						if(typeof(d.cardType)=='undefined'){
							return '';
						}else{
							return d.cardType.card_type;
						}

					}}
				, {field: 'sort', title: '排序', unresize:true}
				,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{ d.isEnable=='1'?'可用':'不可用' }}</div>"}
				, {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#contractTableBar'} //这里的toolbar值是模板元素的选择器
			]]
			, page: true
			, done: function (res, curr, count) {
				layer.close(loadingIndex);
			}
		});
		// 表格绑定事件
		table.on('tool('+tableId+')',function (obj) {
			if(obj.event==="edit"){//编辑按钮事件
				pop_show('编辑','#(ctxPath)/fina/contract/form?id='+obj.data.id,'600','500');
			}else if(obj.event==="del"){//作废按钮事件
				//作废操作
				layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/fina/account/del',
						data: {id:obj.data.id, delFlag:'1'},
						loadFlag: true,
						success : function(rep){
						},
						complete : function() {
							pageTableReload();
						}
					});
					layer.close(index);
				});
			}
		});
	}

	pageTableReload(null);

	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload({name:$('#contractName').val()});
	});
  	//回车事件
	document.onkeydown = function(e){
		var ev =document.all ? window.event:e;  
		if(ev.keyCode==13) {
			$('#searchBtn').click();
			return false
		}
	}
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		var orgId = $('#orgId').val();
		pop_show('添加','#(ctxPath)/fina/contract/form','600','500');
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload(null);
    });
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <label class="layui-form-label">合同名称：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="contractName" name="name" class="layui-input" placeholder="请输入合同名称" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
						#shiroHasPermission("finance:contract:add")
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
						#end
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
	<div class="layui-row">
		<table id="contractTable" lay-filter="contractTable"></table>
	</div>
</div>
#end