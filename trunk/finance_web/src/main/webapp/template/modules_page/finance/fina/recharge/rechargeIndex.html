#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员充值扣卡页面#end

#define css()
<style>
	.layui-table-cell {
		font-size:10px;
		padding:0 5px;
		/*height:auto;
		overflow:visible;
		text-overflow:inherit;
		white-space:normal;
		word-break: break-all;*/
	}
	.layui-form .layui-input{
		height: 25px;
	}
	.layui-form-label {
		float: left;
		display: block;
		padding: 9px 0px;
		width: 80px;
		font-weight: 400;
		line-height: 10px;
		text-align: right;
	}
	.layui-input-inline{
		width: 50%;
	}
	.layui-form-label{
		width: 100px;
		font-size: 14px;
	}
	.layui-btn {
		display: inline-block;
		height: 30px;
		line-height: 30px;
		padding: 0 5px;
		background-color: #009688;
		color: #fff;
		white-space: nowrap;
		text-align: center;
		font-size: 14px;
		border: none;
		border-radius: 2px;
		cursor: pointer;
	}
	.my-layui-row .layui-inline{
		width: 19%;
	}
</style>
#end

#define content()
<div class="layui-row">
	<div class="layui-row">
		<fieldset class="layui-elem-field">
			<legend>会员卡信息</legend>
			<form class="layui-form">
				<div class="layui-row">
					<div class="layui-inline">
						<label class="layui-form-label" style="">会员卡号</label>
						<div class="layui-input-inline">
							<input type="hidden" id="cardId">
							<input type="hidden" id="isIntegral">
							<input id="cardNumber" style="width: 110px;padding-left: 3px;" type="text" value="" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<div class="layui-btn-group">
							<button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
							#shiroHasPermission("finance:rechargeDeduction:rechargeBtn")
							<button type="button" id="addRecharge" class="layui-btn" lay-filter="addRecharge">充值</button>
							#end
							&nbsp;&nbsp;
							#shiroHasPermission("finance:rechargeDeduction:deductionsBtn")
							<button type="button" id="deductCard" class="layui-btn" lay-filter="deductCard">扣卡</button>
							#end
							&nbsp;&nbsp;
							#shiroHasPermission("finance:rechargeDeduction:checkBeanCouponsBtn")
							<button type="button" id="checkBeanCoupons" class="layui-btn">查看豆豆券</button>
							#end
							&nbsp;&nbsp;
							#shiroHasPermission("finance:rechargeDeduction:deductDouDouBtn")
							<button type="button" id="deductDouDou" class="layui-btn" lay-filter="deductDouDou">扣豆豆券</button>
							#end
							&nbsp;&nbsp;
							#shiroHasPermission("finance:rechargeDeduction:cancelRechargeBtn")
<!-- 							<button type="button" id="cancelRecharge" class="layui-btn" lay-filter="cancelRecharge">撤销充值</button> -->
							#end
							&nbsp;&nbsp;
							#shiroHasPermission("finance:rechargeDeduction:getBefore")
							<button type="button" id="getBefore" class="layui-btn" lay-filter="getBefore">查看原会员卡</button>
							#end
							#shiroHasPermission("finance:rechargeDeduction:lockDetailBtn")
							<button type="button" id="lockDetail" class="layui-btn">锁定明细</button>
							#end
							#shiroHasPermission("finance:rechargeDeduction:integralsDetailBtn")
							<button type="button" id="integralsDetail" class="layui-btn">积分明细</button>
							#end
						</div>
						<div class="layui-inline">
							<label class="layui-form-label" style="width: 70px;">扣卡时间：</label>
							<div class="layui-input-inline" style="font-size: 12px;width: 145px;">
								<input type="text" style="padding-left: 3px;" id="date" name="date" class="layui-input my-layui-input" autocomplete="off">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-btn-group">
								<button type="button" id="queryBillRecordBtn" class="layui-btn">查询账单记录</button>
								#shiroHasPermission("finance:rechargeDeduction:batchPrintBtn")
								<button type="button" id="printBillRecordBtn" class="layui-btn">批量打印</button>
								#end
								<button type="button" id="exportBtn" class="layui-btn">导出</button>
							</div>
						</div>
					</div>
				</div>
				<div class="layui-row my-layui-row" >
					<div class="layui-inline">
						<label class="layui-form-label" style="">会员姓名</label>
						<div class="layui-input-inline">
							<input id="fullName" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">会员卡类型</label>
						<div class="layui-input-inline">
							<input id="cardType" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline" style="width:400px;">
						<label class="layui-form-label" style="">会员卡状态</label>
						<div class="layui-input-inline" style="width:200px;">
							<input type="radio" name="cardDelStatus" value="0" title="未作废" lay-filter="delFlagSelect" checked="checked">
							<input type="radio" name="cardDelStatus" value="1" title="已作废" lay-filter="delFlagSelect">
						</div>
					</div>
				</div>
				<div class="layui-row  my-layui-row">
					<div class="layui-inline">
						<label class="layui-form-label" style="">可用天数</label>
						<div class="layui-input-inline">
							<input id="consumeTimes" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">可用余额</label>
						<div class="layui-input-inline">
							<input id="balance" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">可用点数</label>
						<div class="layui-input-inline">
							<input id="consumePoints" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">可用积分</label>
						<div class="layui-input-inline">
							<input id="cardIntegrals" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">可用豆豆券</label>
						<div class="layui-input-inline">
							<input id="beanCoupons" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-row  my-layui-row">
					<div class="layui-inline">
						<label class="layui-form-label" style="">锁定天数</label>
						<div class="layui-input-inline">
							<input id="lockConsumeTimes" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">锁定金额</label>
						<div class="layui-input-inline">
							<input id="lockBalance" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">锁定点数</label>
						<div class="layui-input-inline">
							<input id="lockConsumePoints" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">锁定积分</label>
						<div class="layui-input-inline">
							<input id="lockConsumeIntegrals" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">锁定豆豆券</label>
						<div class="layui-input-inline">
							<input id="lockBeanCoupons" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-row  my-layui-row">
					<div class="layui-inline">
						<label class="layui-form-label" style="">赠送天数</label>
						<div class="layui-input-inline">
							<input id="giveConsumeTimes" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">赠送余额</label>
						<div class="layui-input-inline">
							<input id="giveBalance" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">赠送点数</label>
						<div class="layui-input-inline">
							<input id="giveConsumePoints" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">赠送积分</label>
						<div class="layui-input-inline">
							<input id="giveConsumeIntegrals" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">赠送豆豆券</label>
						<div class="layui-input-inline">
							<input id="giveBeanCoupons" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
				<div class="layui-row  my-layui-row">
					<div class="layui-inline">
						<label class="layui-form-label" style="">累计扣费天数</label>
						<div class="layui-input-inline">
							<input id="cTimes" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">累计扣费余额</label>
						<div class="layui-input-inline">
							<input id="cAmount" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">累计扣费点数</label>
						<div class="layui-input-inline">
							<input id="cPoints" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">累计扣费积分</label>
						<div class="layui-input-inline">
							<input id="cIntegrals" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label" style="">累计扣费豆豆券</label>
						<div class="layui-input-inline">
							<input id="cBeanCoupons" type="text" readonly="readonly" autocomplete="off" class="layui-input">
						</div>
					</div>
				</div>
			</form>
		</fieldset>
	</div>
	<div class="layui-row">
		<fieldset class="layui-elem-field">
			<legend>会员卡充值消费明细记录</legend>
		</fieldset>
		<div class="layui-col-md12">
			<table id="consumeRecordTable" lay-filter="consumeRecordTable"></table>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="barDemo">
	#shiroHasPermission("finance:rechargeDeductionManage:print")
	#[[
	{{#if(d.type!='give_prestore' && d.type!='cancel_recharge' && d.type!='cancel_give'){}}
	<a class="layui-btn layui-btn-xs" lay-event="print">打印</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("finance:rechargeDeductionManage:updateRemark")
	<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="updateRemark">修改备注</a>
	#end
</script>
<script type="text/javascript">
	var table,$ ;
	layui.config({
		base: '/static/js/extend/',
	});
	layui.use(['form','table','laydate','layer','vip_table'],function(){
		table = layui.table;
		$ = layui.$ ;
		var form = layui.form;
		var layer = layui.layer;
		var laydate=layui.laydate;
		var vipTable = layui.vip_table;

		form.render('radio');

		//年范围
		laydate.render({
			elem: '#date'
			,range: true
			,trigger:'click'
		});

		// 搜索消费记录按钮
		$('#searchBtn').on('click', function(){
			var cardNumber = $('#cardNumber').val();
			var delFlag = $('input[name="cardDelStatus"]:checked').val();
			if(cardNumber != null && cardNumber != undefined){
				cardNumber = cardNumber.trim();
				//获取会员卡信息/交易记录
				getCardByCardNumber(cardNumber,delFlag);
			}else{
				layer.msg('会员卡号不能为空!',{icon:5});
			}
		});
		//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;
			if(ev.keyCode==13) {
				$('#searchBtn').click();
				return false
			}
		}
		//keyup事件
		$("#cardNumber").on('change',function(){
			var cardNumber = $('#cardNumber').val();
			var delFlag = $('input[name="cardDelStatus"]:checked').val();
			if(cardNumber != null && cardNumber != undefined){
				cardNumber = cardNumber.trim();
				//获取会员卡信息/交易记录
				getCardByCardNumber(cardNumber,delFlag);
			}else{
				layer.msg('会员卡号不能为空!',{icon:5});
			}
		});

		reloadCardByCardNumber=function(){
			var cardNumber = $('#cardNumber').val();
			var delFlag = $('input[name="cardDelStatus"]:checked').val();
			if(cardNumber != null && cardNumber != undefined){
				cardNumber = cardNumber.trim();
				//获取会员卡信息/交易记录
				getCardByCardNumber(cardNumber,delFlag);
			}else{
				layer.msg('会员卡号不能为空!',{icon:5});
			}
		}

		// 充值
		$("#addRecharge").click(function(){
			$(this).blur();
			var cardId = $("#cardId").val();
			var delFlag = $('input[name="cardDelStatus"]:checked').val();
			if(cardId == null || cardId == '' || cardId == undefined){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			if(delFlag!='0'){
				layer.msg('会员卡已作废!');
				return false;
			}
			var url = "#(ctxPath)/fina/recharge/addRechargeForm?cardId="+cardId+"&isIntegral="+$("#isIntegral").val() ;
			layerShow("会员充值",url,550,700);
		});

		$("#checkBeanCoupons").click(function(){
			var cardId = $("#cardId").val();
			var url = "#(ctxPath)/fina/cardmanager/beanCouponsDetailIndex?id=" + cardId;
			layerShow("查看豆豆券",url,900,700);
		});

		// 扣卡
		$("#deductCard").click(function(){
			$(this).blur();
			var cardId = $("#cardId").val();
			var delFlag = $('input[name="cardDelStatus"]:checked').val();
			if(cardId == null || cardId == '' || cardId == undefined){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			if(delFlag!='0'){
				layer.msg('会员卡已作废!');
				return false;
			}
			var url = "#(ctxPath)/fina/recharge/deductCardForm?cardId="+cardId;
			layerShow("会员扣卡",url,750,550);
		});
		// 扣豆豆券
		$("#deductDouDou").click(function(){
			$(this).blur();
			var cardId = $("#cardId").val();
			var delFlag = $('input[name="cardDelStatus"]:checked').val();
			if(cardId == null || cardId == '' || cardId == undefined){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			if(delFlag!='0'){
				layer.msg('会员卡已作废!');
				return false;
			}
			var url = "#(ctxPath)/fina/deduct/add?cardId="+cardId;
			layerShow("会员扣豆豆券",url,750,550);
		});
		//撤销充值
// 		$("#cancelRecharge").click(function () {
// 			var cardId = $("#cardId").val();
// 			var delFlag = $('input[name="cardDelStatus"]:checked').val();
// 			if(cardId == null || cardId == '' || cardId == undefined){
// 				layer.msg('未获取到会员卡信息!');
// 				return false;
// 			}
// 			if(delFlag!='0'){
// 				layer.msg('会员卡已作废!');
// 				return false;
// 			}
// 			layerShow('撤销充值','#(ctxPath)/fina/recharge/cancelRechargeIndex?cardId='+cardId,1000,600);
// 		});

		//查看原会员卡
		$("#getBefore").click(function(){
			var cardId = $("#cardId").val();
			if(cardId == null || cardId == '' || cardId == undefined){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
// 			util.sendAjax ({
// 				type: 'POST',
// 				url: '#(ctxPath)/fina/consumerecord/checkHaveBeforeCard',
// 				data: {'cardId':cardId},
// 				notice:false,
// 				loadFlag: true,
// 				success : function(rep){
// 					if(rep.state==='ok'){
// 						var oldCardId = rep.data;
// 						layerShow('查看原会员卡','#(ctxPath)/fina/recharge/beforeCardForm?cardId='+oldCardId,1250,700);
// 					}else{
// 						layer.msg(rep.msg, {icon: 5, offset: 'auto'});
// 					}
// 				},
// 				complete : function() {
// 				}
// 			});
			layerShow('查看原会员卡','#(ctxPath)/fina/recharge/beforeCardForm?cardId='+cardId,1250,700);
		});

		//锁定明细
		$("#lockDetail").on('click',function () {
			var cardId = $("#cardId").val();
			if(cardId == null || cardId == '' || cardId == undefined){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			var cardNumber = $('#cardNumber').val();
			if(cardNumber != null && cardNumber != undefined){
				cardNumber = cardNumber.trim();
			}
			layerShow('锁定明细','#(ctxPath)/fina/recharge/lockDetailIndex?cardNumber='+cardNumber,1250,700);
		});
		
		//积分明细
		$("#integralsDetail").on('click',function () {
			var cardId = $("#cardId").val();
			var cardNumber = $("#cardNumber").val();
			if(cardId == null || cardId == '' || cardId == undefined){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			layerShow('积分明细','#(ctxPath)/fina/recharge/integralsDetailIndex?cardId='+cardId+'&cardNumber='+cardNumber,null,null);
		});

		//打印小票
		table.on('tool(consumeRecordTable)',function(obj){
			if (obj.event === 'detail') {
				var url = "#(ctxPath)/fina/consumerecord/detailIndex?id=" + obj.data.id;
				layerShow("消费扣卡明细", url, null, null);
			}else if (obj.event === 'print') {
				var id = obj.data.id;
				if(id == null || id == '' || id == undefined) {
					layer.msg("未获取到交易记录,请重新再试",{time: 1500});
				}else{
					var url = "#(ctxPath)/fina/consumerecord/printForm?id=" + id;
					layerShow("会员费用小票", url, 450, 670);
				}
			}else if(obj.event === 'updateRemark'){
				var id = obj.data.id;
				if(id == null || id == '' || id == undefined) {
					layer.msg("未获取到交易记录,请重新再试",{time: 1500});
				}else{
					var url = "#(ctxPath)/fina/recharge/updateRemarkForm?id=" + id;
					layerShow("修改备注", url, 500,350);
				}
			}
		});

		form.on('radio(delFlagSelect)', function(data){
//	 		console.log(data.elem); //得到radio原始DOM对象
//	 		console.log(data.value); //被点击的radio的value值
			var cardNumber = $('#cardNumber').val();
			var delFlag = data.value;
			if(cardNumber != null && cardNumber != undefined){
				cardNumber = cardNumber.trim();
				//获取会员卡信息/交易记录
				getCardByCardNumber(cardNumber,delFlag);
			}else{
				layer.msg('未获取到会员卡信息!',{icon:5});
			}
		});

		//获取会员卡信息对象
		getCardByCardNumber=function (cardNumber, delFlag) {
			$("#cardId").val('');
			$("#fullName").val('');
			$("#cardType").val('');
			$("#balance").val('');
			$("#consumeTimes").val('');
			$("#cardIntegrals").val('');
			$("#beanCoupons").val('');
			$("#lockBalance").val('');
			$("#lockConsumeTimes").val('');
			$("#lockConsumePoints").val('');
			$("#lockConsumeIntegrals").val('');
			$("#lockBeanCoupons").val('');
			$("#giveBalance").val('');
			$("#giveConsumeTimes").val('');
			$("#giveConsumePoints").val('');
			$("#giveConsumeIntegrals").val('');
			$("#giveBeanCoupons").val('');
			$("#isIntegral").val('');
			$("#cTimes").val('');
			$("#cAmount").val('');
			$("#cPoints").val('');
			$("#cIntegrals").val('');
			$("#cBeanCoupons").val('');
			//loadConsumeRecordTable(null);
			getObjByCard(null,cardNumber,null,delFlag);
		}

		//充值扣卡后调用
		findCardInfoById=function (id,delFlag) {
			getObjByCard(id,null,'1',delFlag);
		}


		//通过会员获取对象
		getObjByCard=function(id,cardNumber,flag,delFlag){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			util.sendAjax({
				url:"#(ctxPath)/fina/cardmanager/getCardByCardNumber",
				type:'post',
				data:{'id':id,'cardNumber':cardNumber,flag:flag,delFlag:delFlag},
				notice:false,
				success:function(returnData){
					if(returnData.state==='ok'){
						if(returnData.data != null) {
							var data = returnData.data;
							$("#cardId").val(data.id == null ? '- -' : data.id);
							$("#fullName").val(data.fullName == null ? '- -' : data.fullName);
							$("#cardType").val(data.cardType == null ? '- -' : data.cardType);
							$("#balance").val(data.balance == null ? '- -' : data.balance);
							$("#consumeTimes").val(data.consumeTimes == null ? '- -' : data.consumeTimes);
							$("#consumePoints").val(data.consumePoints==null ? '- -':data.consumePoints);
							$("#cardIntegrals").val(data.cardIntegrals==null ? '- -':data.cardIntegrals);
							$("#beanCoupons").val(data.beanCoupons==null ? '- -':data.beanCoupons);
							$("#lockBalance").val(data.lockBalance == null ? '- -' : data.lockBalance);
							$("#lockConsumeTimes").val(data.lockConsumeTimes == null ? '- -' : data.lockConsumeTimes);
							$("#lockConsumePoints").val(data.lockConsumePoints == null ? '- -' : data.lockConsumePoints);
							$("#lockConsumeIntegrals").val(data.lockIntegrals == null ? '- -' : data.lockIntegrals);
							$("#lockBeanCoupons").val(data.lockBeanCoupons == null ? '- -' : data.lockBeanCoupons);
							$("#giveBalance").val(data.giveAmountCount == null ? '- -' : data.giveAmountCount);
							$("#giveConsumeTimes").val(data.giveConsumeTimesCount == null ? '- -' : data.giveConsumeTimesCount);
							$("#giveConsumePoints").val(data.giveConsumePointsCount == null ? '- -' : data.giveConsumePointsCount);
							$("#giveConsumeIntegrals").val(data.giveConsumeIntegralCount == null ? '- -' : data.giveConsumeIntegralCount);
							$("#giveBeanCoupons").val(data.giveconsumeBeanCouponsCount == null ? '- -' : data.giveconsumeBeanCouponsCount);
							$("#cTimes").val(data.cTimes == null ? '- -' : data.cTimes);
							$("#cAmount").val(data.cAmount == null ? '- -' : data.cAmount);
							$("#cPoints").val(data.cPoints == null ? '- -' : data.cPoints);
							$("#cIntegrals").val(data.cIntegrals == null ? '- -' : data.cIntegrals);
							$("#cBeanCoupons").val(data.cBeanCoupons == null ? '- -' : data.cBeanCoupons);
							$("#isIntegral").val(data.isIntegral);
							if($("#cardId").val() != null && $("#cardId").val() != '' && $("#cardId").val() != '- -') {
								loadConsumeRecordTable($("#cardId").val());
							}
							if(flag == null || flag == '') {
								layer.msg("获取到会员卡信息");
							}
						}else{
							layer.msg("找不到该会员卡信息",{time: 500});
						}
					}
				},
				complete : function() {
					//关闭loading层
					layer.close(loadingIndex);
				}
			});
		}

		$("#queryBillRecordBtn").on('click',function () {
			var cardId=$("#cardId").val();
			if(cardId==''){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			var date=$("#date").val();
			if(date!=''){
				var dateArray=date.split(" - ");
				loadConsumeRecordTable($("#cardId").val(),dateArray[0],dateArray[1]);
			}else{
				loadConsumeRecordTable($("#cardId").val(),null,null);
			}

		});

		var printCols=[[
			{field:'dealTime',title:'充值/消费时间',width:130,unresize:false,templet:function (d) {
					if(d.inOutFlag==='1'){
						return dateFormat(d.rechargeTime,'yyyy-MM-dd');
					}else if(d.inOutFlag==='2'){
						return dateFormat(d.dealTime,'yyyy-MM-dd HH:mm:ss');
					}
				}},
			{field:'type',title:'类型',width:100,unresize:false,templet:
						"<div>{{ d.type=='recharge_prestore'?'<span class='layui-badge layui-bg-green'>充值</span>'"+
						":d.type=='give_prestore'?'<span class='layui-badge layui-bg-blue'>赠送</span>'"+
						":d.type=='daily_deduction'?'<span class='layui-badge layui-bg-cyan'>日常扣除</span>'"+
						":d.type=='book_deduction'?'<span class='layui-badge layui-bg-orange'>预订扣除</span>'"+
						":d.type=='checkin_deduction'?'<span class='layui-badge layui-bg'>入住扣除</span>'"+
						":d.type=='cancel_recharge'?'<span class='layui-badge layui-bg'>撤销充值</span>'"+
						":d.type=='cancel_give'?'<span class='layui-badge layui-bg'>撤销赠送</span>'"+
						":d.type=='transfer_deduction'?'<span class='layui-badge layui-bg'>过户扣除</span>'"+
						":d.type=='transfer_recharge'?'<span class='layui-badge layui-bg-green'>过户转移</span>'"+
						":d.type=='upgrade_deduction'?'<span class='layui-badge layui-bg'>升级扣除</span>'"+
						":d.type=='upgrade_recharge'?'<span class='layui-badge layui-bg-green'>升级转移</span>'"+
						":d.type=='return_card_deduction'?'<span class='layui-badge layui-bg'>退卡扣除</span>'"+
						":d.type=='recharge_deduction'?'<span class='layui-badge layui-bg'>充值扣除</span>'"+
						":d.type=='give_deduction'?'<span class='layui-badge layui-bg'>赠送扣除</span>'"+
						":'- -' }}</div>"} ,
			{field:'baseName',title:'基地',unresize:false} ,
			{field:'bedName',title:'床位',unresize:false} ,
			{field:'checkinName',title:'入住人',unresize:false} ,
			{field:'startDateStr',title:'入住时间',unresize:false},
			{field:'endDateStr',title:'退住时间',unresize:false},
			{field:'times',title:'天数',width:75,unresize:false,templet: "<div>{{ String(d.times)?d.times + '天':'- -' }}</div>"},
			{field:'amount',title:'金额',width:75,unresize:false,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
			{field:'points',title:'点数',width:75,unresize:false,templet: function (d) {
					if(typeof(d.points)==='undefined'){
						return '0.0'
					}else{
						return d.points;
					}
				}
			},
			{field:'integrals',title:'积分',width:75,unresize:false,templet: function (d) {
					if(typeof(d.integrals)==='undefined'){
						return '0.0'
					}else{
						var re=transferToNumber(d.integrals);
						return re;
					}
				}
			},
			{field:'beanCoupons',title:'豆豆券',width:75,unresize:false,templet: function (d) {
					if(typeof(d.beanCoupons)==='undefined'){
						return '0.0'
					}else{
						return d.beanCoupons;
					}
				}
			}
			/*{field:'timesSnapshot',title:'天数余额',width:110,unresize:false,templet: "<div>{{ String(d.timesSnapshot)?d.timesSnapshot + '天':'- -' }}</div>"},
			{field:'amountSnapshot',title:'金额余额',width:110,unresize:false,templet: "<div>{{ String(d.amountSnapshot)?d.amountSnapshot + '元':'- -' }}</div>"},
			{field:'pointsSnapshot',title:'点数余额',width:110,unresize:false,templet: function (d) {
					if(typeof(d.pointsSnapshot)==='undefined'){
						return '0.0';
					}else{
						return d.pointsSnapshot;
					}
				}
			},
			{field:'integralsSnapshot',title:'积分余额',width:110,unresize:false,templet: function (d) {
					if(typeof(d.integralsSnapshot)==='undefined'){
						return '0.0';
					}else{
						return d.integralsSnapshot;
					}
				}
			},
			{field: '', title: '操作',width:150,unresize:false,toolbar:"#barDemo"}*/
		]];
		function transferToNumber(inputNumber) {
			if (isNaN(inputNumber)) {
// 				console.log(isNaN);
				return inputNumber
			}
// 			console.log(inputNumber);
			inputNumber=Number(inputNumber);
			inputNumber = '' + inputNumber
			inputNumber = parseFloat(inputNumber)
			let eformat = inputNumber.toExponential() // 转换为标准的科学计数法形式（字符串）
			let tmpArray = eformat.match(/\d(?:\.(\d*))?e([+-]\d+)/) // 分离出小数值和指数值
			let number = inputNumber.toFixed(Math.max(0, (tmpArray[1] || '').length - tmpArray[2]))
			return number
		}

// 		console.log(transferToNumber(3.0E-4));

		$("#printBillRecordBtn").on('click',function () {
			var cardId=$("#cardId").val();
			if(cardId==''){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			var date=$("#date").val();
			var startDate=null;
			var endDate=null;
			if(date!=''){
				var dateArray=date.split(" - ");
				startDate=dateArray[0];
				endDate=dateArray[1];
			}
			$.post('#(ctxPath)/fina/consumerecord/getTransListByParams',{'cardId':cardId,'cardNumber':$("#cardNumber").val(),flag:null,startDate:startDate,endDate:endDate},function (res) {
// 				console.log(res);
				print(res,printCols);
			});
		})
		//导出按钮点击事件
		$("#exportBtn").on('click',function () {
			layer.load();
			var cardId=$("#cardId").val();
			if(cardId==''){
				layer.msg('未获取到会员卡信息!');
				return false;
			}
			var date=$("#date").val();
			var startDate='';
			var endDate='';
			var flag='';
			if(date!=''){
				var dateArray=date.split(" - ");
				startDate=dateArray[0];
				endDate=dateArray[1];
			}
			if(table.cache.consumeRecordTable.length>0){
				var url='#(ctxPath)/fina/consumerecord/exportConsumeRecord?cardId='+cardId+'&flag='+flag+'&startDate='+startDate+"&endDate="+endDate;
				window.location.href=url;

				setInterval(function () {
					layer.closeAll('loading');
				},8000);
			}else{
				layer.msg('导出的内容为空', {icon: 2, offset: 'auto'});
			}

		});
		function print (res,cols){
			var v = document.createElement("div");
			var f = ["<head>", "<style>", "body{font-size: 12px; color: #666;}", "table{width: 100%; border-collapse: collapse; border-spacing: 0;}", "th,td{line-height: 20px; padding: 9px 15px; border: 1px solid #ccc; text-align: left; font-size: 12px; color: #666;}", "a{color: #666; text-decoration:none;}", "*.layui-hide{display: none}", "</style>", "</head>"].join("");
			thead = `<h1 style="text-align: center;">`+$("#cardNumber").val()+`会员卡余额明细</h1><table><thead><tr>`
			for(let v2 of cols[0]){
				if((v2.type == 'checkbox') || v2.hasOwnProperty('toolbar')){
					thead += `<th class="layui-table-col-special">空列</th>`
				}else{
					thead += `<th>${v2.title}</th>`
				}
			}
			thead += `</tr></thead></table>`
			$(v).append(thead);

			var tr = `<tbody></tr>`
			for(let v of res){
				tr += '<tr>'
				for(let v2 of cols[0]){
					if((v2.type == 'checkbox') || v2.hasOwnProperty('toolbar')){
						tr += `<td class="layui-table-col-special"></td>`
					}else{

						//var field = v2.field ?? 'id';
						var field = v2.field;

						var value=v[field];
						if(value==undefined){
							if(field=='times' || field=='amount' || field=='points' ||field=='integrals' ||
									field=='timesSnapshot' ||field=='amountSnapshot' ||field=='pointsSnapshot' ||field=='integralsSnapshot' ){
								value='0.0';
							}else{
								value='';
							}
						}else{
							if(field=='type'){
								if(value=='recharge_prestore'){
									value='充值';
								}else if(value=='give_prestore'){
									value='赠送';
								}else if(value=='daily_deduction'){
									value='日常扣除';
								}else if(value=='book_deduction'){
									value='预订扣除';
								}else if(value=='checkin_deduction'){
									value='入住扣除';
								}else if(value=='cancel_recharge'){
									value='撤销充值';
								}else if(value=='cancel_give'){
									value='撤销赠送';
								}else if(value=='transfer_deduction'){
									value='过户扣除';
								}else if(value=='transfer_recharge'){
									value='过户转移';
								}else if(value=='upgrade_deduction'){
									value='升级扣除';
								}else if(value=='upgrade_recharge'){
									value='升级转移';
								}else if(value=='return_card_deduction'){
									value='退卡扣除';
								}else if(value=='recharge_deduction'){
									value='充值扣除';
								}else if(value=='give_deduction'){
									value='赠送扣除';
								}
							}else if(field=='dealTime'){
								var inOutFlag=v['inOutFlag'];
								if(inOutFlag==='1'){
									value=dateFormat(v['rechargeTime'],'yyyy-MM-dd');
								}else if(inOutFlag==='2'){
									value=dateFormat(v['dealTime'],'yyyy-MM-dd HH:mm:ss');
								}
							}else if(field=='integrals'){
								value=transferToNumber(v['integrals']);
							}
						}
						//${v[field]}
						tr += `<td>`+value+`</td>`
					}
				}
				tr += '</tr></tbody>'
			}
			$(v).find('tr').after(tr)
			$(v).find("th.layui-table-patch").remove();
			$(v).find(".layui-table-col-special").remove();
			var h = window.open("打印窗口", "_blank");
			h.document.write(f + $(v).prop("outerHTML"));
			h.document.close();
			h.print();
			h.close();
		}

		var cols=[[
			{field:'dealTime',title:'充值/消费时间',width:130,unresize:true,templet:function (d) {
					if(d.inOutFlag==='1'){
						if(d.type==='transfer_recharge'||d.type==='upgrade_recharge'){
							return dateFormat(d.dealTime,'yyyy-MM-dd HH:mm:ss');
						}else{
							return dateFormat(d.rechargeTime,'yyyy-MM-dd');
						}
					}else if(d.inOutFlag==='2'){
						return dateFormat(d.dealTime,'yyyy-MM-dd HH:mm:ss');
					}
				}},
			{field:'',title:'消费渠道',width:90,unresize:true,templet:'#statusTpl(com.cszn.integrated.service.entity.status.ConsumeType::me(), "consumeType")'},
			{field:'',title:'类型',width:90,unresize:true,templet:
						"<div>{{ d.type=='recharge_prestore'?'<span class='layui-badge layui-bg-green'>充值</span>'"+
						":d.type=='give_prestore'?'<span class='layui-badge layui-bg-blue'>赠送</span>'" +
						":d.type=='book_deduction'?'<span class='layui-badge layui-bg-orange'>预订扣除</span>'"+
						":d.type=='checkin_deduction'?'<span class='layui-badge layui-bg'>入住扣除</span>'"+
						":d.type=='daily_deduction'?'<span class='layui-badge layui-bg-cyan'>日常扣除</span>'"+
						":d.type=='cancel_recharge'?'<span class='layui-badge layui-bg'>撤销充值</span>'"+
						":d.type=='cancel_give'?'<span class='layui-badge layui-bg'>撤销赠送</span>'"+
						":d.type=='transfer_deduction'?'<span class='layui-badge layui-bg'>过户扣除</span>'"+
						":d.type=='transfer_recharge'?'<span class='layui-badge layui-bg-green'>过户转移</span>'"+
						":d.type=='upgrade_deduction'?'<span class='layui-badge layui-bg'>升级扣除</span>'"+
						":d.type=='upgrade_recharge'?'<span class='layui-badge layui-bg-green'>升级转移</span>'"+
						":d.type=='return_card_deduction'?'<span class='layui-badge layui-bg'>退卡扣除</span>'"+
						":d.type=='recharge_deduction'?'<span class='layui-badge layui-bg'>充值扣除</span>'"+
						":d.type=='give_deduction'?'<span class='layui-badge layui-bg'>赠送扣除</span>'"+
						":'- -' }}</div>"
			} ,
			{field:'describe',title:'说明',unresize:false,style:"text-align:left;font-size:10px;color:#000;"} ,
			{field:'settleRemark',title:'结算备注',width:100,unresize:true,style:"text-align:left;font-size:10px;color:#000;"} ,
			/*{field:'describe',title:'入退时间',unresize:false,templet:function (d) {
					if(d.type!='recharge_prestore'){
						return dateFormat(d.startDate,'yyyy-MM-dd')+"至"+dateFormat(d.endDate,'yyyy-MM-dd');
					}
				}},*/
			{field:'times',title:'天数',width:70,unresize:true,templet: "<div>{{ String(d.times)?d.times + '天':'- -' }}</div>"},
			{field:'amount',title:'金额',width:70,unresize:true,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
			{field:'points',title:'点数',width:70,unresize:true,templet: function (d) {
					if(typeof(d.points)==='undefined'){
						return '0.0'
					}else{
						return d.points;
					}
				}
			},
			{field:'integrals',title:'积分',width:70,unresize:true,templet: function (d) {
					if(typeof(d.integrals)==='undefined'){
						return '0.0'
					}else{
						var re=transferToNumber(d.integrals);
						return re;
					}
				}
			},
			{field:'beanCoupons',title:'豆豆券',width:70,unresize:true,templet: function (d) {
					if(typeof(d.beanCoupons)==='undefined'){
						return '0.0'
					}else{
						return d.beanCoupons;
					}
				}
			},
			{field:'timesSnapshot',title:'天数余额',width:90,unresize:true,templet: "<div>{{ String(d.timesSnapshot)?d.timesSnapshot + '天':'- -' }}</div>"},
			{field:'amountSnapshot',title:'金额余额',width:90,unresize:true,templet: "<div>{{ String(d.amountSnapshot)?d.amountSnapshot + '元':'- -' }}</div>"},
			{field:'pointsSnapshot',title:'点数余额',width:90,unresize:true,templet: function (d) {
					if(typeof(d.pointsSnapshot)==='undefined'){
						return '0.0';
					}else{
						return d.pointsSnapshot;
					}
				}
			},
			{field:'integralsSnapshot',title:'积分余额',width:90,unresize:true,templet: function (d) {
					if(typeof(d.integralsSnapshot)==='undefined'){
						return '0.0';
					}else{
						return d.integralsSnapshot;
					}
				}
			},
			{field:'beanCouponsSnapshot',title:'豆豆券余额',width:90,unresize:true,templet: function (d) {
					if(typeof(d.beanCouponsSnapshot)==='undefined'){
						return '0.0';
					}else{
						return d.beanCouponsSnapshot;
					}
				}
			},
			{ title: '操作', fixed: 'right',width:155,unresize:true,templet: function (d) {
					var html = '<div class="layui-btn-group">';
					var delFlag = $('input[name="cardDelStatus"]:checked').val();
					if(d.type!='give_prestore' && d.type!='cancel_recharge' && d.type!='cancel_give' && d.type!='recharge_prestore' && d.type!='transfer_recharge' && d.type!='upgrade_recharge'){
						html+='<a class="layui-btn layui-btn-xs" lay-event="detail">明细</a>';
					}
					#shiroHasPermission("finance:rechargeDeductionManage:print")
					if(d.type!='give_prestore' && d.type!='cancel_recharge' && d.type!='cancel_give'){
						html+='<a class="layui-btn layui-btn-xs" lay-event="print">打印</a>';
					}
					#end
					#shiroHasPermission("finance:rechargeDeductionManage:updateRemark")
					if(delFlag=='0'){
						html+='<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="updateRemark">修改备注</a>';
					}
					#end
					html+='</div>';
					return html;
				}
			}
		]];
		//充值扣卡列表
		function loadConsumeRecordTable(cardId,startDate,endDate){
			if (table != null && table != '') {
				//loading层
				var loadingIndex = layer.load(2, { //icon支持传入0-2
					shade: [0.5, 'gray'], //0.5透明度的灰色背景
					content: '加载中...',
					success: function (layero) {
						layero.find('.layui-layer-content').css({
							'padding-top': '39px',
							'width': '60px'
						});
					}
				});
				table.render({
					id : 'consumeRecordTable',
					elem : "#consumeRecordTable" ,
					url : '#(ctxPath)/fina/consumerecord/getTransByParams' ,
					where: {cardId: cardId,startDate:startDate,endDate:endDate},
					cols : cols ,
					page : true,
					height: 'full-300',
					limit : 10
					,done:function () {
						//
						var layerTips;
						$("td").on("mouseenter", function() {
							//js主要利用offsetWidth和scrollWidth判断是否溢出。
							//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
							if (this.offsetWidth < this.firstChild.scrollWidth) {
								var that = this;
								var text = $(this).text();
								layerTips=layer.tips(text, that, {
									tips: 1,
									time: 0
								});
							}
						});
						$("td").on("mouseleave", function() {
							//js主要利用offsetWidth和scrollWidth判断是否溢出。
							//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
							layer.close(layerTips);
						});
						layer.close(loadingIndex);
					}
				}) ;
			}
		}
	});
</script>
#end