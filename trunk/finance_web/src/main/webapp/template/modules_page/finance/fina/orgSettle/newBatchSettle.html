#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()批量结算首页#end

#define css()
<style>
    .layui-table-cell {
        height: 28px;
        line-height: 28px;
        padding: 0 5px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
            <div class="layui-inline">
                <label class="layui-form-label">基地</label>
                <div class="layui-input-inline" style="width:182px;">
                    <select id="baseId" name="baseId" lay-search>
                        #for(b : baseList)
                        <!--<option value="#(b.id)" #if(for.index==0) selected #end>#(b.baseName)</option>-->
                        <option value="#(b.id)" #if(b.id=='27a558b2-c205-4937-b6f4-fed1fa68ac55') selected #end>#(b.baseName)</option>

                        #end
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">入住类型</label>
                <div class="layui-input-inline" style="width:182px;">
                    <select id="checkinType" name="checkinType" lay-search>
                        <option value="">请选择基地</option>
                        #for(checkinType : checkinTypes)
                        <option value="#(checkinType.key)" #if(checkinType.key=="Member") selected #end >#(checkinType.value)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline" style="width:182px;">
                    <input type="text" id="yearMonth" name="yearMonth" autocomplete="off" class="layui-input"/>

                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">会员卡号</label>
                <div class="layui-input-inline">
                    <div class="layui-inline">
                        <input type="text" id="cardNumber" name="cardNumber" autocomplete="off" class="layui-input"/>
                    </div>
                </div>
            </div>


            <div class="layui-inline">
                <div class="layui-btn-group">
                    <button class="layui-btn" lay-submit="" id="search" lay-filter="search">查询</button>
                    <button type="button" id="settleBtn" class="layui-btn">批量结算</button>
                </div>
            </div>
        </form>
    </div>
    <div class="layui-row">
        <table id="settleTable" lay-filter="settleTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="inputNowTpl">
    #[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="nowDeductTimes" id="nowDeductTimes-{{d.id}}" min="0" value="{{d.actualTimes}}"
                   lay-verify="checkNumber|required"  autocomplete="off" class="layui-input" {{#if(d.deductWay!='deduct_by_days'){}} readonly disabled {{#}}}>
        </div>
    </div>
    {{#}else{}}
    {{d.actualTimes}}
    {{#}}}
    ]]#
</script>
<script type="text/html" id="inputNowATpl">
    #[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline">
        <div class="layui-input-inline">
            <input type="text" name="nowDeductAmount" id="nowDeductAmount-{{d.id}}" min="0" value="{{d.actualAmount}}"
                   lay-verify="checkAmount|required" autocomplete="off" class="layui-input" {{#if(d.deductWay!='deduct_by_money'){}} readonly disabled  {{#}}}>
        </div>
    </div>
    {{#}else{}}
    {{d.actualAmount}}
    {{#}}}
    ]]#
</script>
<script type="text/html" id="inputNowPTpl">
    #[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline">
        <div class="layui-input-inline">
            <input type="text" name="nowDeductPoints" min="0" value="{{d.actualPoints}}"
                   lay-verify="checkAmount|required" autocomplete="off" class="layui-input" {{#if(d.deductWay!='deduct_by_points'){}} readonly disabled {{#}}}>
        </div>
    </div>
    {{#}else{}}
    {{d.actualPoints}}
    {{#}}}
    ]]#
</script>
<script type="text/html" id="inputNowITpl">
    #[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline">
        <div class="layui-input-inline">
            <input type="text" name="nowDeductIntegrals" id="nowDeductIntegrals-{{d.id}}"  min="0" value="{{d.actualIntegrals}}"
                   lay-verify="checkAmount|required" autocomplete="off" class="layui-input" {{#if(d.deductWay!='deduct_by_days' || d.isIntegral!="1"){}} readonly disabled  {{#}}}>
        </div>
    </div>
    {{#}else{}}
    {{d.actualIntegrals}}
    {{#}}}
    ]]#
</script>
<script type="text/html" id="inputRemarkTpl">
    #[[
    <div class="layui-inline">
        <div class="layui-input-inline">
            {{#if(typeof(d.settleRemark)==='undefined'){}}
            <input type="text" name="settleRemark" id="settleRemark-{{d.id}}" min="0" autocomplete="off" oninput="updateRemark(this)" value="" class="layui-input">
            {{#}else{}}
            <input type="text" name="settleRemark" id="settleRemark-{{d.id}}" min="0" autocomplete="off" oninput="updateRemark(this)" value="{{d.settleRemark}}" class="layui-input">
            {{#}}}
        </div>
    </div>
    ]]#
</script>
<script type="text/html" id="toolBar">
    <div class="layui-btn layui-btn-sm" id="{{d.id}}" lay-event="info">详情</div>
    #[[
    {{#if(d.settleStatus==='0'){}}
    {{#if(d.settleType==='checkout_settle'){}}
    <button class="layui-btn layui-btn-sm" lay-event="checkoutSettleBtn">退住结算</button>
    {{#}else if(d.settleType==='month_settle'){}}
    <button class="layui-btn layui-btn-sm" lay-event="monthSettleBtn">月结算</button>
    {{#}}}
    {{#}}}
    ]]#
</script>
<script type="text/html" id="settleStatus">
    #[[
    {{#if(d.settleStatus==='0'){}}
    <span class='layui-badge layui-bg-orange'>未结算</span>
    {{#}else{}}
    <span class='layui-badge layui-bg-green'>已结算</span>
    {{#}}}
    ]]#
</script>
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.use([ 'table', 'form', 'layer','laydate' ], function() {
        var table = layui.table
            , form = layui.form
            , layer = layui.layer
            , $ = layui.jquery
            ,laydate=layui.laydate
        ;
        laydate.render({
            elem:"#yearMonth",
            type:"month",
            value:new Date()
        }) ;


        function batchSettleLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'settleTable'
                ,elem : '#settleTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/settleDetail/newBatchSettlePageList'
                ,height: 'full-90'
                ,cols: [[
// 				{type:'checkbox'}
                    {type:'checkbox'}
                    ,{type:'numbers',title:'序号'}
                    ,{field:'', title: '会员卡', align: 'center', unresize: true,width:160,templet:"<div>{{d.cardNumber}}-{{d.fullName}}</div>"}
                    ,{field:'checkinNo',title: '入住号', align: 'center', unresize: true,width:140}
                    ,{field:'name',title: '入住人', align: 'center', unresize: true,width:80}
                    ,{field:'', title: '结算类型', align: 'center',unresize: true,width:80,templet:function (d) {
                            if(d.settleType==='month_settle'){
                                return "月结算";
                            }else if(d.settleType==='checkout_settle'){
                                return "退住结算";
                            }else{
                                return "- -";
                            }
                        }}
                    ,{field:'yearMonth',title: '账单月份', align: 'center', unresize: true,width:80}
                    ,{field:'', title: '产生时间', align: 'center', unresize: true,width:175,templet:"<div>{{dateFormat(d.startTime,'yyyy-MM-dd')}}至{{dateFormat(d.endTime,'yyyy-MM-dd')}}</div>"}
                    ,{field:'', title: '锁定', align: 'center',unresize: true,width:145,templet:function (d) {
                            var str = "";
                            if(String(d.totalTimes) != null && String(d.totalTimes) != ''){
                                if(typeof(d.totalTimes)=='undefined'){
                                    str = "0天";
                                }else{
                                    str = String(d.totalTimes) +"天";
                                }

                            }else{
                                str = "- -";
                            }
                            str += " ";
                            if(String(d.totalAmount) != null && String(d.totalAmount) != null){

                                if(typeof(d.totalAmount)=='undefined'){
                                    str+="0元";
                                }else{
                                    str += String(d.totalAmount) +"元";
                                }
                            }else{
                                str += "- -";
                            }
                            str += " ";
                            /*if(String(d.totalPoints) != null && String(d.totalPoints) != null){
                                if(typeof(d.totalPoints)=='undefined'){
                                    str+="0点数";
                                }else{
                                    str += String(d.totalPoints) +"点数";
                                }
                            }else{
                                str += "- -";
                            }*/
                            if(String(d.totalIntegrals) != null && String(d.totalIntegrals) != null){
                                if(typeof(d.totalIntegrals)=='undefined'){
                                    str+="0积分";
                                }else{
                                    str += String(d.totalIntegrals) +"积分";
                                }
                            }else{
                                str += "- -";
                            }
                            return str;
                        }}
                    ,{field:'actualTimes', title: '扣卡天数',unresize: true,width:90,templet:'#inputNowTpl'}
                    ,{field:'actualAmount', title: '扣卡金额',unresize: true,width:90,templet:'#inputNowATpl'}
                    /*,{field:'actualPoints', title: '扣卡点数',unresize: true,width:100,templet:'#inputNowPTpl'}*/
                    ,{field:'actualIntegrals', title: '扣卡积分',unresize: true,width:90,templet:'#inputNowITpl'}
//                 ,{field:'settleStatus', title: '结算状态', align: 'center',unresize: true,width:110,templet:'#settleStatus'}
                    ,{field:'remark', title: '入住单备注', align: 'center',unresize: true,width:150}
                    ,{field:'settleRemark', title: '结算备注', align: 'center',unresize: true,width:200,templet:"#inputRemarkTpl"}
                    ,{fixed: 'right', title:'操作', toolbar: '#toolBar', width:180}
                ]]
                ,page : false
                ,limit : 15
                ,limits : 1
                , done: function (res, curr, count) {
                    layer.close(loadingIndex);

                }
            });
            table.on('tool(settleTable)',function (obj) {
                let id=obj.data.id;
                let actualTimes=$("#nowDeductTimes-"+id).val();
                let actualAmount=$("#nowDeductAmount-"+id).val();
                let actualIntegrals=$("#nowDeductIntegrals-"+id).val();
                let settleRemark=$("#settleRemark-"+id).val();

                var settleData={'expenseId':obj.data.expenseId,'detailId':obj.data.id,'actualTimes':actualTimes,'actualAmount':actualAmount
                    ,'actualPoints':0,'actualIntegrals':actualIntegrals,'settleRemark':settleRemark}
                if(obj.event==='monthSettleBtn'){
                    layer.confirm("确定要结算吗?",{
                        skin : "my-skin"
                    },function(index) {
                        util.sendAjax({
                            type: 'POST',
                            url: '#(ctxPath)/fina/settleDetail/settleMonthDetail',
                            data: settleData,
                            notice: true,
                            loadFlag: true,
                            success: function (rep) {
                                if (rep.state == 'ok') {
                                    $("#search").click();
                                }
                            }
                        });
                        layer.close(index);
                    });
                }else if(obj.event==='checkoutSettleBtn'){
                    layer.confirm("确定要结算吗?",{
                        skin : "my-skin"
                    },function(index) {
                        layer.load();
                        util.sendAjax({
                            type: 'POST',
                            url: '#(ctxPath)/fina/settleDetail/settleCheckoutDetail',
                            data: settleData,
                            notice: true,
                            loadFlag: true,
                            success: function (rep) {
                                if (rep.state == 'ok') {
                                    $("#search").click();
                                }
                            }
                        });
                        layer.close(index);
                    });
                }else if(obj.event==='info'){
                    var url = "#(ctxPath)/fina/expenseRecord/form?id=" + obj.data.expenseId ;
                    pop_show("旅居消费详情",url,1100,650);
                }
            });
        };

        //console.log("baseId:"+$("#baseId").val()+",checkinType:"+$("#checkinType").val()+",yearMonth:"+$("#yearMonth").val());


        form.on("submit(search)",function(data){
            batchSettleLoad(data.field);
            return false;
        });

        $("#search").click();

        //批量获取列表选中数据
        getCheckTableData = function(){
            var checkTable = table.checkStatus('settleTable');
            return checkTable.data;
        }

        rowUpdate = function(tableId, index, data) {
            layui.$.extend(table.cache[tableId][index], data);
        }

        //重新
        updateRemark=function(obj){
            var remark=obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {settleRemark:remark};
            rowUpdate('settleTable',index,data);
        }

        // 结算
        $("#settleBtn").click(function(){
            var dataArray = new Array();
            var tableData = getCheckTableData();
            if(tableData.length>0){
                for (var i=0;i<tableData.length;i++){
                    /*let settleRemark = '';
                    if(typeof(tableData[i].settleRemark)!='undefined'){
                        settleRemark = tableData[i].settleRemark;
                    }*/
                    let id=tableData[i].id;
                    let actualTimes=$("#nowDeductTimes-"+id).val();
                    let actualAmount=$("#nowDeductAmount-"+id).val();
                    let actualIntegrals=$("#nowDeductIntegrals-"+id).val();
                    let settleRemark=$("#settleRemark-"+id).val();

                    var dataObj = {
                        'id':tableData[i].id,
                        'expenseId':tableData[i].expenseId,
                        'settleType':tableData[i].settleType,
                        'cardNumber':tableData[i].cardNumber,
                        'actualTimes':actualTimes,
                        'actualAmount':actualAmount,
                        'actualIntegrals':actualIntegrals,
                        'settleRemark':settleRemark
                    };
                    dataArray.push(dataObj);
                };
                if(dataArray.length>0){
                    if(dataArray.length>100){
                        layer.msg('批量结算最大数量不能超过100条', function () {});
                        return false;
                    }
                    layer.confirm("确定要批量结算吗?",function(index){
                        layer.load();
                        $.post('#(ctxPath)/api/batchSettleSave',{batchDatas:JSON.stringify(dataArray)},function (rep) {
                            if(rep.code=='0'){
                                layer.closeAll('loading');
                                window.top.layer.msg(rep.msg, {icon: 1, offset: 'auto'});
                                $("#search").click();
                            }else{
                                layer.closeAll('loading');
                                window.top.layer.msg(rep.msg, {icon: 2, offset: 'auto'});

                            }
                        })
                        layer.close(index);
                    });
                }else{
                    layer.msg('请勾选数据', function () {});
                }
            }else{
                layer.msg('请勾选数据', function () {});
            }
        });
    });
</script>
#end