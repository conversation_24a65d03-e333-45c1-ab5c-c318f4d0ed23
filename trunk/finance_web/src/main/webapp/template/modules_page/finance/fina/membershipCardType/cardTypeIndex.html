#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
		<div class="layui-row">
			<div class="layui-inline">
				<label class="layui-form-label">类别名称：</label>
				<div class="layui-input-inline">
					<input type="text" id="cardType" placeholder="请输入卡类别名称" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">类别类型</label>
				<div class="layui-input-inline">
					<select id="typeCategory" name="typeCategory">
						<option value="">全部</option>
						#dictOption("type_category", type.typeCategory??'', "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">类别分类</label>
				<div class="layui-input-inline">
					<select id="typeClassify" name="typeClassify">
						<option value="">全部</option>
						#statusOption(com.cszn.integrated.service.entity.status.TypeClassify::me(), type.TypeClassify??"")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<button type="button" id="searchConsumeBtn" class="layui-btn">搜索</button>
			</div>
			#shiroHasPermission("finance:cardType:addBtn")
				<button type="button" id="addBtn" class="layui-btn">添加</button>
			#end
		</div>
	</form>
	<div class="layui-row">
		<table id="cardTypeTable" lay-filter="cardTypeTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
	#shiroHasPermission("finance:cardType:setBtn")
	<a class="layui-btn layui-btn-xs" lay-event="set">配置</a>
	#end
	#shiroHasPermission("finance:cardType:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("finance:cardType:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script type="text/javascript">
	layui.use(['table','laydate'],function(){
		var table = layui.table
		, $ = layui.$
		;

		cardTypeTableReload=function(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id:'cardTypeTable',
				elem:"#cardTypeTable",
				url:'#(ctxPath)/fina/cardType/cardTypePage',
				where:data,
				height:'full-90',
				cols:[[
					{type:'numbers',title:'序号',width:60,unresize:true}
					,{field: 'cardType', title: '类别名称',unresize:true}
					,{field:'', title:'类别类型', width:100, unresize:true, align:'center', templet: '#dictTpl("type_category", "typeCategory")'}
					,{field:'', title:'类别分类', width:100, unresize:true, align:'center', templet: '#statusTpl(com.cszn.integrated.service.entity.status.TypeClassify::me(), "typeClassify")'}
					,{field: '', width:120, title: '是否开通金额',unresize:true,templet:"<div>{{d.isBalance == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isBalance == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否开通天数',unresize:true,templet:"<div>{{d.isConsumeTimes == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isConsumeTimes == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否开通点数',unresize:true,templet:"<div>{{d.isConsumePoints == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isConsumePoints == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否滚动积分',unresize:true,templet:"<div>{{d.isIntegral == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isIntegral == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:100, title: '是否可预订',unresize:true,templet:"<div>{{d.isBooking == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isBooking == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '是否可充值豆豆券',unresize:true,templet:"<div>{{d.isRechargeBean == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isRechargeBean == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '不发验证码',unresize:true,templet:"<div>{{d.isNotSendSms == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isNotSendSms == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field: '', width:120, title: '不签入住协议',unresize:true,templet:"<div>{{d.isNotSignAgreement == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isNotSignAgreement == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
					,{field:'remark', width:120, title:'描述',unresize:true}
					,{title:'操作',width:140, fixed:'right',unresize:true,toolbar:'#toolBar'}
				]],
				page:true,
				limit:10
				, done: function (res, curr, count) {
					layer.close(loadingIndex);
				}
			});
			table.on('tool(cardTypeTable)',function(obj){
				if(obj.event === 'set'){
					pop_show('配置','#(ctxPath)/fina/cardType/cardTypeConfig?id='+obj.data.id,800,500);
				}else if(obj.event === 'edit'){
					pop_show('编辑','#(ctxPath)/fina/cardType/cardTypeForm?id='+obj.data.id,800,650);
				} else if (obj.event === 'del') {
					layer.confirm("确定要作废吗?",function(index){
						util.sendAjax({
							url:"#(ctxPath)/fina/cardType/delete",
							type:'post',
							data:{"id":obj.data.id},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									cardTypeTableReload({cardType:$('#cardType').val(),typeCategory:$('#typeCategory').val(),typeClassify:$('#typeClassify').val()});
								}
								layer.close(index);
							}
						});
					});
				}
			});
		}

		cardTypeTableReload(null);

		// 搜索消费记录按钮
		$('#searchConsumeBtn').on('click', function(){
			cardTypeTableReload({cardType:$('#cardType').val(),typeCategory:$('#typeCategory').val(),typeClassify:$('#typeClassify').val()});
		});
		
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#searchConsumeBtn').click();
				return false
			}
		}

		//添加按钮
		$("#addBtn").on('click',function () {
			layerShow('添加会员卡类别','#(ctxPath)/fina/cardType/cardTypeForm',800,650);
		});
	});
</script>
#end