#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm">
        	<div class="layui-inline">
        		<label class="layui-form-label">团号</label>
	            <div class="layui-input-inline">
	                <input id="touristNo" name="touristNo" class="layui-input">
	            </div>
        	</div>
        	<div class="layui-inline">
        		<label class="layui-form-label">团名</label>
	            <div class="layui-input-inline">
	                <input id="name" name="name" class="layui-input">
	            </div>
        	</div>
        	<div class="layui-inline">
        		<label class="layui-form-label">开团时间</label>
        		<div class="layui-inline">
		            <div class="layui-input-inline">
		                <input type="text" id="beginTime" name="beginTime" autocomplete="off" class="layui-input" placeholder="开始时间">
		            </div>
		            <div class="layui-form-mid">至</div>
		            <div class="layui-input-inline">
		                <input type="text" id="endTime" name="endTime" autocomplete="off" class="layui-input" placeholder="结束时间">
		            </div>
        		</div>
        	</div>
            <div class="layui-inline">
	            <button type="button" id="search" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
            </div>
        </form>
    </div>
    <div class="layui-row">
		<table id="touristTable" lay-filter="touristTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
    #shiroHasPermission("finance:sojournSettle:touristDetail")
    	<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="touristDetail">团信息</a>
    #end
    #shiroHasPermission("finance:sojournSettle:touristLook")
    	<a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="touristLook">查看</a>
    #end
    #shiroHasPermission("finance:sojournSettle:touristSettle")
	#[[
    {{#if(d.createBy != '0'){}}
    	<a class="layui-btn layui-btn-xs" lay-event="touristSettle">结算</a>
    {{#}}}
    ]]#
    #end

	<a class="layui-btn layui-btn-xs" lay-event="checkinBase">入住基地汇总</a>
    #shiroHasPermission("finance:sojournSettle:touristDel")
	#[[
    {{#if(d.createBy != '0'){}}
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="touristDel">作废</a>
    {{#}}}
    ]]#
    #end
</div>
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate = layui.laydate;

        laydate.render({
            elem:"#beginTime",
            type:"date",
            range:false,
            format:"yyyy-MM-dd"    //设置日期呈现的格式
        }) ;

        laydate.render({
            elem:"#endTime",
            type:"date",
            range:false,
            format:"yyyy-MM-dd"    //设置日期呈现的格式
        });
        
        function touristLoad(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            table.render({
                id : 'touristTable'
                ,elem : '#touristTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-90'
                ,url : '#(ctxPath)/fina/tourist/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
	                 {field:'touristNo', title: '团号', align: 'center', unresize: true}
	                ,{field:'name', title: '旅游团名称', align: 'center', unresize: true}
	                ,{field:'startDate', title: '旅游团时间', align: 'center',templet:function (d) {
	                    var str="";
	                    if(d.startDate != null && d.startDate != ''){
	                        str = dateFormat(d.startDate,'yyyy-MM-dd');
	                    }else{
	                        str = "--";
	                    }
	                    str += " 至 ";
	                    if(d.endDate != null && d.endDate != null){
	                        str += dateFormat(d.endDate,'yyyy-MM-dd');
	                    }else{
	                        str += "--";
	                    }
	                    return str;
	                }}
                    ,{field:'businessEntityId', title: '业务实体名称', align: 'center', unresize: true}
	                ,{field:'actualTimes', title: '扣除天数', sort: true, align: 'center', unresize: true,templet: "<div>{{ d.expenseTimes?d.expenseTimes + '天': '- -' }}</div>"}
	                ,{field:'route', title: '线路描述', align: 'center', unresize: true}
	                ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
	                ,{fixed:'right', title: '操作', width: 270, align: 'center', unresize: true, toolbar: '#actionBar'}
	        	]]
        		,page : true
        		,limit : 10
                ,limits : [10,20,30,40]
				, done: function (res, curr, count) {
					layer.close(loadingIndex);
				}
	        }); 
	        table.on('tool(touristTable)',function(obj){
	            if(obj.event === 'touristDetail'){
	                var url = "#(ctxPath)/fina/tourist/form?id=" + obj.data.id ;
	                pop_show("团信息",url,750,450);
	            }else if(obj.event === 'touristLook'){
	                var url = "#(ctxPath)/fina/tourist/settleForm?touristNo=" + obj.data.touristNo +"&type=look";
	                layerShow("查看",url,'100%','100%');
	            }else if(obj.event === 'touristSettle'){
	                var url = "#(ctxPath)/fina/tourist/settleForm?touristNo=" + obj.data.touristNo +"&type=settle" ;
	                layerShow("结算",url,'100%','100%');
	            }else if(obj.event === 'touristDel'){
	            	layer.confirm("确定作废?",function(index){
	    				util.sendAjax({
	    					url:"#(ctxPath)/fina/tourist/del",
	    					type:'post',
	    					data:{id:obj.data.id, touristNo:obj.data.touristNo},
	    					loadFlag:true,
	    					notice:true,
	    					success:function(returnData){
	    						if(returnData.state==='ok'){
	    							touristLoad({touristNo:$('#touristNo').val(),name:$('#name').val(),beginTime:$('#beginTime').val(),endTime:$('#endTime').val()});
	    						}
	    						layer.close(index);
	    					}
	    				});
	    			});
	            }else if(obj.event==='checkinBase'){
					var url = "#(ctxPath)/fina/tourist/checkinBaseSummary?touristNo=" + obj.data.touristNo ;
					layerShow("结算",url,'100%','100%');
				}
	            return false;
	        });
		};

        touristLoad(null);

        form.on("submit(search)",function(data){
            touristLoad(data.field);
            return false;
        });

		reloadTouristLoad=function(){
			$("#search").click();
		}
        
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#search').click();
				return false
			}
		}
        
    });
</script>
#end