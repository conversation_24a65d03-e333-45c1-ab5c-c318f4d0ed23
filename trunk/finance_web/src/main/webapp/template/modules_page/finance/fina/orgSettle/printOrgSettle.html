#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构养老费用账单首页#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post" style="margin-left:25px;margin-top:15px;">
            <div class="layui-row" style="float: right;padding-right: 30px;">

                <button class="layui-btn" id="print" style="padding: 0 10px;border-radius: 5px;"  type="button">打印</button>

            </div>
            &nbsp;&nbsp;
        </form>
    </div>
    <div class="layui-row">
        <table id="mainTable" class="layui-table" lay-filter="mainTable"></table>
    </div>
    <input id="checkinNo" type="hidden" value="#(checkinNo??)">

    <div class="layui-row" style="display: none;">
        <div id="compForm" >
            <table class="layui-table">
                <tr style="border: 0px;">
                    <td colspan="4" align="center">长住结算单</td>
                </tr>
                <tr style="border: 0px;">
                    <td colspan="2" align="left">凭证编号：#(checkinNo??)</td>
                    <td colspan="2" align="right">日期：#(date)</td>
                </tr>
                <tr>
                    <td>入住人姓名：</td>
                    <td>#(memberName)</td>
                    <td>入住床位号</td>
                    <td>#(bedName)</td>
                </tr>
                <tr id="billDetail">
                    <td style="font-weight: 600;">费用名称</td>
                    <td style="font-weight: 600;">会员卡号</td>
                    <td colspan="2" style="font-weight: 600;">费用描述</td>
                </tr>



                <tr>
                    <td colspan="4" align="right">制表：#(userName??)</td>
                </tr>
                <tr>
                    <td colspan="">合计</td>
                    <td colspan="3" id="total"></td>
                </tr>
                <tr>
                    <td>财务确认</td>
                    <td></td>
                    <td>收据编号</td>
                    <td></td>
                </tr>
            </table>
        </div>
    </div>
</div>
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#getDictLabel("gender")
#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/jQuery.print.js"></script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        table.render({
            id : 'mainTable'
            ,elem : '#mainTable'
            ,method : 'POST'
            ,height:$(document).height()*0.75
            ,limit : 10
            ,limits : [20,30,40,50]
            ,url : '#(ctxPath)/fina/orgSettle/billDetailList?checkinNo='+$("#checkinNo").val()
            ,cellMinWidth: 80
            ,cols: [[
                {type: "checkbox", fixed:"left", width:50}
                ,{type: 'numbers', width:60, title: '序号',unresize:true}
                ,{field:'mainName',width:240, title: '账单名称', align: 'center', unresize: true}
                ,{field:'bedNo', title: '床位',width:90, align: 'center', unresize: true}
                ,{field:'detailName', title: '费用名称', align: 'center', unresize: true}
                ,{field:'accountNumber', title: '扣费会员卡号',width:130, align: 'center', unresize: true}
                ,{field:'amount', title: '扣除值',width:80, align: 'center', unresize: true,templet:function (d) {
                        if(d.deductType=='deduct_by_money'){
                            if(d.amount>0){
                                return d.amount+"元";
                            }else{
                                return "0元";
                            }
                        }else if(d.deductType=='deduct_by_days'){
                            if(d.amount>0){
                                return d.amount+"天";
                            }else{
                                return "0天";
                            }
                        }
                    }}
                ,{field:'integrals', title: '扣除积分',width:90, align: 'center', unresize: true}
                ,{field:'status', title: '状态',width:80, align: 'center', unresize: true,templet:"<div>{{d.settleStatus=='settled'?'<span class='layui-badge layui-bg-green'>已结算</span>':d.settleStatus=='wait_settlement'?'<span class='layui-badge'>待结算</span>':'- -'}}</div>"}
                ,{field:'produceDate',title: '产生时间', align: 'center',width:320, unresize: true,event:'produceDate',templet:"<div>{{dateFormat(d.startDate,'yyyy-MM-dd HH:mm:ss')}}至{{dateFormat(d.endDate,'yyyy-MM-dd HH:mm:ss')}}</div>"}
                ,{field:'describe', title: '明细描述', align: 'center', unresize: true,event:'describe'}
            ]]
            ,page : false
        });
        table.on('tool(mainTable)',function(obj){
            if (obj.event === 'check') {
                layerShow("查看账单","#(ctxPath)/fina/orgSettle/checkSettleMainIndex?checkinNo="+obj.data.checkinNo+"&memberName="+obj.data.memberName,'100%','100%');
            }else if(obj.event === 'settle'){
                layui.layer.open({
                    type: 2,
                    area: ['100%', '100%'],
                    fix: true, //不固定
                    maxmin: true,
                    shade:0.4,
                    title: "机构养老消费结算",
                    content: "#(ctxPath)/fina/orgSettle/orgSettleMainIndex?checkinNo="+obj.data.checkinNo+"&memberName="+obj.data.memberName,
                    end:function () {
                        table.reload('mainTable',{'where':{'memberName':$("#memberName").val(),'checkinNo':$("#checkinNo").val()}});
                    }
                });
            }else if(obj.event==='print'){

            }
        });


        $('#print').on('click', function() {
            var checkStatus = table.checkStatus('mainTable');
            var i=0;
            var times=0.0;
            var amount=0.0;
            var integrals=0.0;
            $.each(checkStatus.data,function (index,item) {
                var describe="";

                var unit="";
                if(item.deductType=='deduct_by_money'){
                    unit="元";
                    amount+=Number(item.amount);
                }else if(item.deductType=='deduct_by_days'){
                    unit="天";
                    times+=Number(item.amount);
                }
                if(typeof item.integrals!='undefined'){
                    integrals+=Number(item.integrals);
                }
                if(typeof item.describe!='undefined'){
                    describe=item.describe;
                }else{
                    describe=dateFormat(item.startDate,'yyyy-MM-dd')+"至"+dateFormat(item.endDate,'yyyy-MM-dd')+item.detailName+"="+item.amount+unit;
                    if(typeof item.integrals!='undefined'){
                        describe+=",积分="+item.integrals;
                    }
                }

                var cardNumber=item.accountNumber;
                if(item.accountNumber=='**********' || item.accountNumber=='**********'
                    || item.accountNumber=='182614' || item.accountNumber=='182611'){
                    cardNumber='现金';
                }

                var str="<tr>\n" +
                    "                    <td>"+item.detailName+"</td>\n" +
                    "                    <td>"+cardNumber+"</td>\n" +
                    "                    <td colspan=\"2\">"+describe+"</td>\n" +
                    "                </tr>";
                var tr = $("#compForm table:first tbody tr:eq("+Number(3+i)+")");
                tr.after(str);
                i++;
            });
            $("#total").text("合计："+"天数"+times+"，金额"+amount+"，积分"+integrals);
            $.print('#compForm');
            pop_close();
        });

    });
</script>

#end