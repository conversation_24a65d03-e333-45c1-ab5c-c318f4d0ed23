#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()赠送详情页面#end

#define css()

#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'cardGiveRecordTable'
            ,elem : '#cardGiveRecordTable'
            ,method : 'POST'
            ,where : {"cardId":$("#cardId").val()}
            ,limit : 8
            ,limits : [8,20,30,40]
            ,url : '#(ctxPath)/fina/cardmanager/cardGiveRecordPage'
            ,cellMinWidth: 80
            ,cols: [[
                {type: 'cycle', title: '周期',align: 'center',unresize:true,templet:"<div>{{ dictLabel(d.cycle,'give_cycle','- -') }}</div>"}
                ,{field:'giveSeq', title: '赠送序', align: 'center', unresize: true}
                ,{field:'giveTimes', title: '赠送天数',sort: true, align: 'center', unresize: true}
                ,{field:'giveAmount', title: '赠送金额', sort: true, align: 'center', unresize: true}
                ,{field:'giveDate', title: '赠送时间',sort: true, unresize: true,templet:"<div>{{dateFormat(d.giveDate,'yyyy-MM-dd')}}</div>"}
            ]]
            ,page : true
        });

        cardGiveRecordTableReload=function () {
            table.reload("cardGiveRecordTable",{"where":{"cardId":$("#cardId").val(),"cycle":$("#cycle").val()}});
        }

        form.on('select(cycle)',function () {
            cardGiveRecordTableReload();
        })

    });
</script>
#end

#define content()
<div class="layui-row" style="padding: 10px;">
    <input type="hidden" id="cardId" name="cardId" value="#(cardId??)">
    <div class="layui-row">
        <form class="layui-form">
            <div class="layui-inline">
                <label class="layui-form-label">周期：</label>
                <div class="layui-input-inline">
                    <select id="cycle" lay-filter="cycle">
                        <option value="">请选择周期</option>
                        #dictOption("give_cycle", '', '')
                    </select>
                </div>
            </div>
        </form>
    </div>
    <table id="cardGiveRecordTable" lay-filter="cardGiveRecordTable"></table>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</div>
#getDictLabel("give_cycle")
#end