#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()账户编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
    layui.use([ 'form' ], function() {
        var form = layui.form
            , $ = layui.jquery
        ;

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/integralRechargeConfig/saveConfig',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.pageTableReload(null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<div class="layui-row">
    <form class="layui-form layui-form-pane" style="padding: 5px;">
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>产品价格</label>
            <div class="layui-input-block">
                <input type="text" name="productPrice" class="layui-input" lay-verify="required|number" value="#(model.productPrice??)" maxlength="30" placeholder="请输入产品价格">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>天数</label>
            <div class="layui-input-block">
                <input type="text" name="rechargeDays" class="layui-input" lay-verify="required|number" value="#(model.rechargeDays??)" maxlength="30" placeholder="请输入天数">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 3px;"><font color="red">*</font>对应积分系数</label>
            <div class="layui-input-block">
                <input type="text" name="integralRatio" class="layui-input" lay-verify="required|number" value="#(model.integralRatioStr??)" maxlength="30" placeholder="请输入对应积分系数">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>日积分</label>
            <div class="layui-input-block">
                <input type="text" name="dayIntegral" class="layui-input" lay-verify="required|number" value="#(model.dayIntegralStr??)" maxlength="30" placeholder="请输入日积分">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>月积分(30天)</label>
            <div class="layui-input-block">
                <input type="text" name="monthIntegral" class="layui-input" lay-verify="required|number" value="#(model.monthIntegralStr??)" maxlength="30" placeholder="请输入日积分">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red">*</font>年积分(360天)</label>
            <div class="layui-input-block">
                <input type="text" name="yearIntegral" class="layui-input" lay-verify="required|number" value="#(model.yearIntegralStr??)" maxlength="30" placeholder="请输入日积分">
            </div>
        </div>
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="remark" placeholder="请输入内容" class="layui-textarea">#(model.remark??)</textarea>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-left">
                <div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
            </div>
            <div class="pull-right">
                <input type="hidden" name="id" value="#(model.Id??)">
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
        </div>
    </form>
</div>
#end