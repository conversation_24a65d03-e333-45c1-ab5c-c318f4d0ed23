#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()撤销充值页面#end

#define css()

#end

#define js()
<script id="toolBar" type="text/html">
    #[[
    {{#if(d.cancelFlag==='0'){}}
        <a class="layui-btn layui-btn-xs" lay-event="cancel">撤销充值</a>
    {{#}else{}}
        <span>已撤销</span>
    {{#}}}
    ]]#
</script>
<script>
    layui.use(['form','laytpl','layer','table'], function() {
        var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,table=layui.table;

        table.render({
            id : 'rechargeTable' ,
            elem : '#rechargeTable' ,
            url : '#(ctxPath)/fina/recharge/cancelRechargePage' ,
            method:'post',
            where:{'cardId':$("#cardId").val()},
            height:510,
            cols : [[
                {field: 'type', title: '充值类型',unresize:true,templet:function (d) {
                        if(d.type=='1'){
                            return '充值金额';
                        }else if(d.type=='2'){
                            return '充值天数';
                        }else if(d.type=='4'){
                            return '充值积分';
                        }else if(d.type=='5'){
                            return '充值豆豆券';
                        }
                    }},
                {field: 'amount', title: '充值金额',unresize:true,},
                {field: 'consumeTimes', title: '充值天数',unresize:true},
                {field: 'consumeIntegral', title: '充值积分',unresize:true},
                {field: 'consumeBeanCoupons', title: '充值豆豆券',unresize:true},
                {field: 'giveAmount', title: '赠送金额',unresize:true},
                {field: 'giveConsumeTimes', title: '赠送天数',unresize:true},
                {field: 'giveConsumeIntegral', title: '赠送积分',unresize:true},
                {field: 'giveBeanCoupons', title: '赠送豆豆券',unresize:true},
                {field: '', title: '操作',unresize:true,toolbar:"#toolBar"}
            ]] ,
            limit : 10,
            page: {
                layout: ['page','prev', 'next',"skip",'count'],
                curr: 1,
                groups: 2,
            },
        });

        rechargeTableReload=function(){
            table.reload('rechargeTable',{'where':{'cardId':$("cardId").val()}});
        }


        table.on('tool(rechargeTable)',function (obj) {
            if(obj.event==='cancel'){
                layer.confirm('确定要撤销该充值记录吗?',function (index) {
                    layer.load();
                    layui.$('#layui-layer'+index).find('.layui-layer-btn0').prop('disabled',true).addClass('layui-disabled');
                    util.sendAjax({
                        url:"#(ctxPath)/fina/recharge/cancelRechargeRecord",
                        type:'post',
                        data:{'id':obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                rechargeTableReload();
                                parent.findCardInfoById($("#cardId").val());
                                layer.closeAll('loading');
                            }
                        },
                        complete: function () {
                            layui.$('#layui-layer'+index).find('.layui-layer-btn0').prop('disabled',false).removeClass('layui-disabled');
                        }
                    });
                    layer.close(index);
                });

            }
        })

    });

</script>
#end

#define content()
<div style="margin: 15px;">
    <input type="hidden" id="cardId" value="#(cardId??)">
    <div class="layui-row">
        <table class="layui-table" id="rechargeTable" lay-filter="rechargeTable"></table>
    </div>
</div>
#end