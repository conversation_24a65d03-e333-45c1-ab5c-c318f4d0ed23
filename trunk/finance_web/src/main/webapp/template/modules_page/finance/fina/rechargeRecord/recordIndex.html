#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()充值记录页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
			<div class="layui-inline">
				<label class="layui-form-label">充值类别</label>
				<div class="layui-input-inline">
					<select name="rechargeCategory">
						<option value="">全部</option>
						#getDictList("recharge_category")
						<option value="#(key)" >#(value)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">充值标题</label>
				<div class="layui-input-inline">
					<input type="text" name="rechargeTitle" class="layui-input" placeholder="请输入充值标题" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-btn-group">
					<button type="button" id="search" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
					#shiroHasPermission("fina:rechargeRecord:add")
						<button type="button" id="add" class="layui-btn">添加</button>
					#end
				</div>
			</div>
		</form>
	</div>
	<div class="layui-row">
		<table id="recordTable" lay-filter="recordTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("fina:rechargeRecord:view")
	#[[
	{{#if(d.isRecharge=='1'){}}
		<a class="layui-btn layui-bg-blue layui-btn-xs" lay-event="view">查看</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:rechargeRecord:edit")
	#[[
	{{#if(d.isRecharge=='0'){}}
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:rechargeRecord:download")
	#[[
	{{#if(d.isRecharge=='0'){}}
		<a class="layui-btn layui-btn-xs" lay-event="download">模板下载</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:rechargeRecord:import")
	#[[
	{{#if(d.isRecharge=='0'){}}
		<a id="{{d.id}}" class="layui-btn layui-btn-xs upload_btn">导入名单</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:rechargeRecord:confirm")
	#[[
	{{#if(d.isRecharge=='0'){}}
		<a class="layui-btn layui-btn-xs" lay-event="confirm">确认充值</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:rechargeRecord:del")
	#[[
	{{#if(d.isRecharge=='0'){}}
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">作废</a>
	{{#}}}
	]]#
	#end
</div>
</script>
<script>
layui.use(['form','layer','table','upload'], function() {
	var table = layui.table, $ = layui.$, form = layui.form, layer = layui.layer, upload = layui.upload;

	recordLoad = function (data) {
		//loading层
		var loadingIndex = layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		table.render({
			id : 'recordTable'
			,elem : '#recordTable'
			,method : 'POST'
			,url : '#(ctxPath)/fina/rechargeRecord/pageTable'
			,where : data
			,height:'full-90'
			,cols: [[
				{type: 'numbers', width:100, title: '序号',unresize:true}
				,{field:'rechargeTitle', title: '充值标题', align: 'center', unresize: true}
				,{field:'', title: '充值类别', align: 'center',width:150, unresize: true,templet: '#dictTpl("recharge_category", "rechargeCategory")'}
				,{field: '', title: '是否已充值', align: 'center',width:100,unresize:false,templet:function (d) {
					if(d.isRecharge==='1'){
						return '<span class="layui-badge layui-bg-green">已充值</span>';
					}else{
						return '<span class="layui-badge">未充值</span>';
					}
				}}
				,{field:'', title: '创建时间', align: 'center',width:200, unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
				,{fixed:'right', title: '操作', width: 280, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 10
			,limits : [10,20,30,40]
			, done: function (res, curr, count) {
				if(count>0){
// 					var tableElem = this.elem;
// 					var tableViewElem = tableElem.next();
// 					var uploadElem = tableViewElem.find('.upload_btn');
// 					res.data.forEach((item) => {
						//导入名单
				    	upload.render({
				    		elem: '.upload_btn'
				    		,url: '#(ctx)/fina/rechargeRecord/rechargeListImport'
				    		,accept: 'file'
				    		,exts: 'xls|xlsx' //只允许上传xls后缀的文件
				    		,multiple: false
				    		,before: function(obj){
				    			var tableElem = this.item;
				    			var recordId = tableElem.attr('id');
				    			this.data = {id:recordId}
				    			layer.load();
				    		}
				    		,done: function(res){
				    			layer.closeAll('loading');
				    			if(res.state==='ok') {
				    				layer.msg(res.msg, {icon: 1, time: 20000});
				    			}else{
				    				layer.msg(res.msg, {icon: 5, time: 20000});
				    			}
				    		}
				    		,error: function(res){
				    			layer.closeAll('loading');
				    			layer.msg(res.msg, {icon: 5, time: 2000});
				    		}
				    	});
// 					});
				}
				layer.close(loadingIndex);
			}
		});
		table.on('tool(recordTable)',function(obj){
			if(obj.event === 'view'){
				var url = "#(ctxPath)/fina/rechargeRecord/view?id=" + obj.data.id;
				pop_show("查看",url,'', '');
			}else if (obj.event === 'edit') {
				var url = "#(ctxPath)/fina/rechargeRecord/edit?id=" + obj.data.id;
				pop_show("编辑",url,'', '');
			}else if (obj.event === 'download') {
				var url = "#(ctx)/fina/rechargeRecord/templateDownload";
				var eleLink = document.createElement('a');
				eleLink.style.display = 'none';
				eleLink.href = url;
				// 触发点击
				document.body.appendChild(eleLink);
				eleLink.click();
				// 然后移除
				document.body.removeChild(eleLink);
			}else if (obj.event === 'confirm') {
				layer.confirm("确定要充值吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/fina/rechargeRecord/rechargeListConfirm',
						notice: true,
						data: {id:obj.data.id, isRecharge:'1'},
						loadFlag: false,
						success : function(rep){
							layer.close(index);
							if(rep.state=='ok'){
								tableReload('recordTable',null);
							}
						},
						complete : function() {
						}
					});
				});
			}else if (obj.event === 'delete') {
				layer.confirm("确定要作废吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/fina/rechargeRecord/del',
						notice: true,
						data: {id:obj.data.id, delFlag:'1'},
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								tableReload('recordTable',null);
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}
		});
	};
	
	recordLoad({});

	form.on("submit(search)",function(data){
		recordLoad(data.field);
		return false;
	});

	// 添加
	$("#add").click(function(){
		$(this).blur();
		var url = "#(ctxPath)/fina/rechargeRecord/add" ;
		pop_show("新增",url,'','');
	});

});
</script>
#end