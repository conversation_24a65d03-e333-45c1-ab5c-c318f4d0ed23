#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <input id="touristNo" type="hidden" value="#(touristNo??)">
    <div class="layui-row">
        <table id="touristCheckinDetailTable" lay-filter="touristCheckinDetailTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">

</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate = layui.laydate;



        function touristLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'touristCheckinDetailTable'
                ,elem : '#touristCheckinDetailTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-90'
                ,url : '#(ctxPath)/fina/tourist/checkinBaseSummaryList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'memberName', title: '入住人', align: 'center', unresize: true}
                    #for(baseName : baseNameSet),{field:'#(baseName)', title: '#(baseName)', align: 'center', unresize: true,templet:function (d){
                        if(d.#(baseName)>0){
                            return d.#(baseName)
                        }else{
                            return 0;
                        }
                    }}#end
                ]]
                ,page : false
                ,limit : 10
                ,limits : [10,20,30,40]
                ,done:function(res, curr, count) {
                    if(res.count>0){
                        var html=$(".layui-table-main .layui-table").find('tr:last').html();
                        var trObj='<tr>'+html+'</tr>';
                        var tableTd=$(trObj).find("td");
                        $.each(tableTd,function(index,itme){
                            if(index==0){
                                $(itme).find("div").text('合计');
                                $(itme).find("div").css("font-weight","600");
                            }else{
                                var dataField=$(itme).attr("data-field");
                                var total=0;
                                $.each($("td[data-field="+dataField+"]"),function(ix,it){
                                    total+=Number($(it).find("div").text());
                                });
                                $(itme).find("div").text(total);
                                $(itme).find("div").css("font-weight","600");
                            }
                        });
                        $(".layui-table-main .layui-table").append(tableTd);
                    }
                    layer.close(loadingIndex);
                 }
            });

        };

        touristLoad({"touristNo":$("#touristNo").val()});

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#search').click();
                return false
            }
        }

    });
</script>
#end