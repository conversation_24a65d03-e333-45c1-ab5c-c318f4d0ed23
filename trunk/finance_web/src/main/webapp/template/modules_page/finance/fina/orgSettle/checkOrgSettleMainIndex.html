#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构养老费用账单查看#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <div class="layui-row">
        <div class="layui-form-item" style="margin-top: 20px;">
            <div class="layui-inline" >
                <label class="layui-form-label">入住流水号</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="checkinNo" value="#(checkinNo??)" name="chekcinNo" readonly>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">入住人姓名</label>
                <div class="layui-input-inline">
                    <input class="layui-input" id="memberName" value="#(memberName??)" name="memberName" readonly>
                </div>
            </div>
            #shiroHasPermission("finance:orgSettle:printBtn")
            <button class="layui-btn" type="button" id="print">打印退住结算单</button>
            #end
        </div>
    </div>
    <div class="layui-row">
        <div class="layui-collapse" lay-filter="mainColla" lay-accordion id="mainColla">
            #for(main : mainList)
            <div class="layui-colla-item">
                <h2 class="layui-colla-title" data-id="#(main.id??)" data-settleStatus="#(main.settleStatus??)">
                    #(main.mainName??) #if(main.settleStatus=='no_settle')<span class='layui-badge'>未结算</span> #elseif(main.settleStatus=='normal') <span class='layui-badge layui-bg-green'>已结算</span> #end
                </h2>
                <div class="layui-colla-content">
                    <fieldset class="layui-elem-field" id="billFieldset" style="margin-top: 30px;">
                        <legend>费用明细</legend>
                        <div class="layui-row" style="padding: 5px;" >
                            <button class="layui-btn layui-btn-sm" type="button" onclick="checkboxPrint('#(main.id??)')">打印</button>
                        </div>
                        <div style="padding: 0 5px 5px 5px;">
                            <table class="layui-table" id="table-#(main.id??)" lay-filter="table-#(main.id??)"></table>
                        </div>
                    </fieldset>

                    <fieldset class="layui-elem-field" id="couponFieldset" style="margin-top: 30px;">
                        <legend>纸卡记录</legend>
                        <div style="padding: 0 5px 5px 5px;">
                            <table class="layui-table" id="table-coupon-#(main.id??)" lay-filter="table-coupon-#(main.id??)"></table>
                        </div>
                    </fieldset>
                </div>
            </div>
            #end
        </div>
        <div class="layui-row" id="none" style="line-height: 40px;text-align: center;color: #999;#if(mainList.size()==0)display: block;#else display: none; #end">
            无数据
        </div>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">


    #shiroHasPermission("finance:orgSettle:settleDateilBtn")
    #[[
    {{#if(d.status==='settled'){}}
    <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="print">打印</a>
    {{#}}}
    ]]#
    #end


</script>
<script>
    layui.use(['form','layer','table','laydate','element'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate,element=layui.element;

        element.on('collapse(mainColla)', function(data){
            var id=$(this).attr("data-id");
            var settleStatus=$(this).attr("data-settleStatus");
            if(data.show){
                billDetailTableLoad(id);
                couponTableLoad(id);
            }
        });

        $("#print").on('click',function () {
            layer.open({
                type: 2,
                //title: '收藏管理 (考生姓名：张无忌)',
                title: '选择打印', //将姓名设置为红色
                shadeClose: true,           //弹出框之外的地方是否可以点击
                offset: '10%',
                area: ['90%', '80%'],
                content: '#(ctxPath)/fina/orgSettle/printOrgSettle?checkinNo='+$("#checkinNo").val()
            });
        });


        couponTableLoad=function(id){
            table.render({
                id : 'table-coupon-'+id
                ,elem : '#table-coupon-'+id
                ,method : 'POST'
                ,limit : 10
                ,where:{'mainId':id}
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/orgSettle/findMainCashRecordPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'type', title: '纸卡类型',width:140, align: 'center',event:'type', unresize: true,templet:"<div>#[[{{#if(d.type==='1'){}} 金额纸卡{{#}else if(d.type==='2'){}} 天数纸卡 {{#}else if(d.type==='3'){}} 现金{{#}else{}}- -{{#}}}]]#</div>"}
                    ,{field:'amountNum', title: '数量', align: 'center',event:'amountNum', unresize: true}
                    ,{field:'unit', title: '单位', align: 'center', unresize: true,event:'unit',templet:"<div>#[[{{#if(d.type==='1'){}}元{{#}else if(d.type==='2'){}}天{{#}else if(d.type==='3'){}}元{{#}else{}}- -{{#}}}]]#</div>"}
                    ,{field:'receiveName', title: '收款人', align: 'center', unresize: true,event:'receiveName'}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true,event:'remark'}
                ]]
                ,page:false
                ,done:function (res,curr,count) {
                }
            });
        }


        billDetailTableLoad=function (id) {
            table.render({
                id : 'table-'+id
                ,elem : '#table-'+id
                ,method : 'POST'
                ,limit : 10
                ,where:{'mainId':id}
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/orgSettle/findMainBillDetail'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'checkbox', fixed: 'left'}
                    ,{field:'accountNumber', title: '扣费会员卡号',width:150, align: 'center', unresize: true}
                    ,{field:'name', title: '费用名称', align: 'center', unresize: true}
                    ,{field:'chargeHandle', title: '费用处理方式',width:120, align: 'center', unresize: true,templet:"<div>{{d.chargeHandle=='charge'?'<span class='layui-badge layui-bg-green'>收费</span>':d.chargeHandle=='refund'?'<span class='layui-badge'>退费</span>':'- -'}}</div>"}
                    ,{field:'status', title: '状态',width:100, align: 'center', unresize: true,templet:"<div>{{d.status=='settled'?'<span class='layui-badge layui-bg-green'>已结算</span>':d.status=='wait_settlement'?'<span class='layui-badge'>待结算</span>':'- -'}}</div>"}
                    ,{field:'amount', title: '扣除值',width:100, align: 'center', unresize: true}
                    ,{title: '单位', align: 'center',width:80, unresize: true,templet:"<div>{{d.deductType==='deduct_by_money'?'元':d.deductType==='deduct_by_days'?'天':'- -'}}</div>"}
                    ,{field:'integrals', title: '扣除积分',width:100, align: 'center', unresize: true}
                    ,{title: '产生时间', align: 'center', unresize: true,templet:"<div>{{dateFormat(d.startDate,'yyyy-MM-dd HH:mm:ss')}}至{{dateFormat(d.endDate,'yyyy-MM-dd HH:mm:ss')}}</div>"}
                    ,{field:'describe', title: '明细描述', align: 'center', unresize: true,event:'describe'}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 200, align: 'left', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page:false
            });

            checkboxPrint=function (id) {
                var checkStatusData = table.checkStatus('table-'+id).data;
                console.log(checkStatusData);
                console.log(5);
                if(checkStatusData.length==0){
                    layer.msg('打印的条数不能为0条', {icon: 2, offset: 'auto'});
                    return false;
                }

                var flag=false;
                var ids="";
                $.each(checkStatusData,function (index,item) {
                    if(item.status=='wait_settlement'){
                        flag=true;
                        return false;
                    }
                    ids+=item.transactionId+","
                });
                if(flag){
                    layer.msg('打印数据存在未结算的记录', {icon: 2, offset: 'auto'});
                    return false;
                }

                var url = "#(ctxPath)/fina/consumerecord/batchPrintForm?ids=" + ids;
                layerShow("会员费用小票", url, '100%', '100%');

            }

            table.on('tool(table-'+id+')',function (obj) {

                if(obj.event==='del'){
                    layer.confirm('确定要作废该明细吗?',function (index) {
                        $.post('#(ctxPath)/fina/orgSettle/delBillDetail',{'id':obj.data.id},function (res) {
                            if(res.state==='ok'){
                                layer.msg(res.msg, {icon: 1, offset: 'auto'});
                                billDetailTableReload(id);
                            }else{
                                layer.msg(res.msg, {icon: 2, offset: 'auto'});
                            }
                            layer.close(index);
                        })
                    });
                }else if(obj.event==='replace'){
                    layerShow('更换会员卡','#(ctxPath)/fina/orgSettle/billDetaIlReplaceCardIndex?detailId='+obj.data.id+"&mainId="+id,1200,800);
                }else if(obj.event==='settle'){
                    layer.confirm('确定要结算该明细吗?',function (index) {
                        layer.load();
                        loadingIndex = layer.load(1,{shade : [0.1, '#fff']});
                        $.post('#(ctxPath)/fina/orgSettle/memberSettleBillDetail',{'id':obj.data.id},function (res) {
                            if(res.state==='ok'){
                                layer.msg(res.msg, {icon: 1, offset: 'auto'});
                                //billDetailTableReload(id);
                                loadSettleMain();
                                layer.closeAll('loading');
                            }else{
                                layer.msg(res.msg, {icon: 2, offset: 'auto'});
                            }
                            layer.close(loadingIndex);
                            layer.close(index);

                        });
                    });
                }else if(obj.event==='remark' || obj.event==='produceDate' || obj.event==='describe'){
                    var settlestatus=$(".layui-colla-item").find("h2[data-id='"+id+"']").attr("data-settlestatus");
                    if(settlestatus==='no_settle'){
                        layerShow('编辑明细','#(ctxPath)/fina/orgSettle/updateBillDetailRemarkIndex?detailId='+obj.data.id,700,600);
                    }

                }else if(obj.event=='print'){
                    var url = "#(ctxPath)/fina/consumerecord/printForm?id=" + obj.data.transactionId;
                    layerShow("会员费用小票", url, 450, 670);
                }
            });

        }

    });
</script>
#end