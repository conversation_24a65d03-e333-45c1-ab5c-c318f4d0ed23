#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">

            <div class="layui-inline">
                <label class="layui-form-label">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="baseId">
                        <option value="">全部</option>
                        #for(base : baseList)
                        <option value="#(base.id??)">#(base.baseName??)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">业主姓名：</label>
                <div class="layui-input-inline">
                    <input type="text" id="supplierName" placeholder="请输入房间号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline" style="display: none;">
                <label class="layui-form-label">房间号：</label>
                <div class="layui-input-inline">
                    <input type="text" id="roomName" placeholder="请输入房间号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">流程状态</label>
                <div class="layui-input-inline">
                    <select id="status" name="status">
                        <option value="">全部</option>
                        <option value="1">未提交</option>
                        <option value="2">审核中</option>
                        <option value="3">审核通过</option>
                        <option value="5">中止</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <button type="button" id="searchConsumeBtn" class="layui-btn">搜索</button>
            </div>
            #shiroHasPermission("finance:lease:addBtn")
            <button type="button" id="addBtn" class="layui-btn">添加</button>
            #end
        </div>
    </form>
    <div class="layui-row">
        <table id="cardTypeTable" lay-filter="cardTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
        #[[
        {{#if(d.status=='1'){}}
            {{#if(d.isEdit){}}
            <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
            {{#}else{}}
            <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
            {{#}}}
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
        {{#}else{}}
            {{#if(d.isCurrentUser){}}
            <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
            {{#}}}

            {{#if(!d.isCurrentUser){}}
                <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
            {{#}}}
        {{#}}}
        ]]#
    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate'],function(){
        var table = layui.table
            , $ = layui.$
        ;

        cardTypeTableReload=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTable',
                elem:"#cardTypeTable",
                url:'#(ctxPath)/fina/leaseRecordApply/leaseRecordApplyPageList',
                where:data,
                height:'full-90',
                cols:[[
                    {type:'numbers',title:'序号',width:100,unresize:true}
                    ,{field: 'baseName', title: '基地',unresize:true}
                    ,{field: 'rooms',width:260, title: '房间',unresize:true,templet:function (d) {
                        if(d.rooms==undefined){
                            return '';
                        }else{
                            let str=d.rooms;
                            let returnStr="";
                            $.each(str.split("；"),function (index,item) {
                                returnStr+=item+"\n";
                            })
                            return returnStr;
                        }
                    }}
                    ,{field: 'supplierName',width:90, title: '业主',unresize:true}
                    ,{field: 'paymentWayStr',width:120, title: '业主收款方式',unresize:true}
                    ,{field: 'payAccount', title: '业主收款账号',unresize:true}
                    ,{field:'currentStepName', title:'当前步骤' , unresize:true, align:'center'}
                    ,{field: '', width:120, title: '流程状态',unresize:true,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge">未提交</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">审核通过</span>';
                            }else if(d.status=='4'){
                                return '驳回';
                            }else if(d.status=='5'){
                                return '中止';
                            }else if(d.status=='6'){
                                return '撤销';
                            }
                        }}
                    ,{field:'remark',title:'备注',unresize:true}
                    ,{field:'userName', title:'提交人', width:100, unresize:true, align:'center'}
                    ,{title:'操作',width:160, fixed:'right',unresize:true,toolbar:'#toolBar'}
                ]],
                page:true,
                limit:10
                , done: function (res, curr, count) {
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });

                    layer.close(loadingIndex);
                }
            });
            table.on('tool(cardTypeTable)',function(obj){
                if(obj.event === 'set'){
                    pop_show('配置','#(ctxPath)/fina/cardType/cardTypeConfig?id='+obj.data.id,800,500);
                }else if(obj.event === 'edit'){
                    layerShow('编辑','#(ctxPath)/fina/leaseRecordApply/form?id='+obj.data.id,'100%','100%');

                } else if (obj.event === 'del') {
                    layer.confirm("确定要作废吗?",function(index){
                        util.sendAjax({
                            url:"#(ctxPath)/fina/leaseRecordApply/delLeaseRecordApply?id="+obj.data.id,
                            type:'post',
                            data:{"id":obj.data.id},
                            notice:true,
                            success:function(returnData){
                                if(returnData.state==='ok'){
                                    reloadTable();
                                }
                                layer.close(index);
                            }
                        });
                    });
                }
            });
        }

        cardTypeTableReload(null);

        // 搜索消费记录按钮
        $('#searchConsumeBtn').on('click', function(){
            cardTypeTableReload({status:$('#status').val(),baseId:$('#baseId').val(),roomName:$('#roomName').val(),supplierName:$("#supplierName").val()});
        });

        reloadTable=function(){
            cardTypeTableReload({status:$('#status').val(),baseId:$('#baseId').val(),roomName:$('#roomName').val(),supplierName:$("#supplierName").val()});
        }

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#searchConsumeBtn').click();
                return false
            }
        }

        //添加按钮
        $("#addBtn").on('click',function () {
            layerShow('添加','#(ctxPath)/fina/leaseRecordApply/form','100%','100%');
        });
    });
</script>
#end