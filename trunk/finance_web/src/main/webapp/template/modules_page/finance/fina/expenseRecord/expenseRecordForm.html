#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅居消费主记录展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/assess.css" media="all">
#end

#define content()
<div class="layui-collapse">
	<form class="layui-form layui-form-pane" action="" id="form">
	    <div class="layui-row">
			<input type="hidden" id="bedId" value="#(bed.id??)"/>
			<input type="hidden" id="erId" name="id" value="#(er.id??)"/>
			<div class="layui-row">
				<div class="layui-inline">
				    <label class="layui-form-label">基地</label>
				    <div class="layui-inline">
				        <input name="baseName" class="layui-input" value="#(base.baseName??)" readonly>
				    </div>
				</div>
			    <div class="layui-inline">
				    <label class="layui-form-label">业务实体</label>
				    <div class="layui-inline">
				        <input name="entityName" class="layui-input" value="#(businessEntity.entityName??)" readonly>
				    </div>
				</div>
				<div class="layui-inline">
				    <label class="layui-form-label">客户渠道</label>
				    <div class="layui-inline">
				        <input class="layui-input" value="#(customerChannel??)" readonly>
				    </div>
				</div>
				<div class="layui-inline">
				    <label class="layui-form-label">结算状态</label>
				    <div class="layui-inline">
				        <input class="layui-input" value="#(settleStatusName??)" readonly>
				    </div>
				</div>
				<div class="layui-inline">
				    <label class="layui-form-label">入住状态</label>
				    <div class="layui-inline">
				        <input class="layui-input" value="#(checkinStatusName??)" readonly>
				    </div>
				</div>
			    <div class="layui-inline">
				    <label class="layui-form-label">入住号</label>
				    <div class="layui-inline">
						<input id="bookNo" name="bookNo" class="layui-input" value="#(er.bookNo??)" readonly>
				        <input id="checkinNo" name="checkinNo" class="layui-input" value="#(er.checkinNo??)" readonly>
				    </div>
				</div>
			    <div class="layui-inline">
				    <label class="layui-form-label">入住人</label>
				    <div class="layui-inline">
				        <input id="name" name="name" class="layui-input" value="#(er.name??)" readonly>
				    </div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">入住类型</label>
					<div class="layui-inline">
						<input id="checkinType" name="checkinType" class="layui-input" value="#(checkinType??)" readonly>
					</div>
				</div>
			    <div class="layui-inline">
				    <label class="layui-form-label">床位</label>
				    <div class="layui-inline">
				        <input id="bedName" name="bedName" class="layui-input" value="#(bed.bedName??)" readonly>
				    </div>
				</div>
			    <div class="layui-inline">
			    	<input type="button" id="lookBedInfo" class="layui-btn" value="查看床位收费"/>
					<input type="button" id="file" class="layui-btn" value="床位预定附件"/>
			    	#if(er.checkinStatus=='book')
						<button type="button" id="saveRemark" class="layui-btn">保存备注</button>
					#end
			    </div>
			</div>
			<div class="layui-row">
				<div class="layui-inline">
				    <label class="layui-form-label">预订时间</label>
				    <div class="layui-inline">
				        <div class="laydate-input-inline" style="display:inline-block;">
				            <input type="text" id="bookStartDate" name="bookStartTime" autocomplete="off" class="layui-input" placeholder="开始时间" value="#date(er.bookStartTime??,'yyyy-MM-dd HH:mm:ss')" readonly>
				        </div>
				        <div class="span-group-laydate-addon" style="display:inline-block;">至</div>
				        <div class="laydate-input-inline" style="display:inline-block;margin-left:36px;">
				            <input type="text" id="bookEndDate" name="bookEndTime" autocomplete="off" class="layui-input" placeholder="结束时间" value="#date(er.bookEndTime??,'yyyy-MM-dd HH:mm:ss')" readonly>
				        </div>
				    </div>
				</div>
				<div class="layui-inline">
				    <label class="layui-form-label">入住时间</label>
				    <div class="layui-inline">
				        <div class="laydate-input-inline" style="display:inline-block;">
				            <input type="text" id="checkinStartDate" name="checkinStartDate" autocomplete="off" class="layui-input" placeholder="开始时间" value="#date(er.checkinTime??,'yyyy-MM-dd HH:mm:ss')" readonly>
				        </div>
				        <div class="span-group-laydate-addon" style="display:inline-block;">至</div>
				        <div class="laydate-input-inline" style="display:inline-block;margin-left:36px;">
				            <input type="text" id="checkinEndDate" name="checkinEndDate" autocomplete="off" class="layui-input" placeholder="结束时间" value="#date(er.checkoutTime??,'yyyy-MM-dd HH:mm:ss')" readonly>
				        </div>
				    </div>
				</div>
		        #if(er.checkinStatus?? == 'retreat')
				<div class="layui-inline">
			        <label class="layui-form-label">退住时间</label>
			        <div class="laydate-input-inline" style="display:inline-block;">
			            <input type="text" id="realCheckinEndDate" name="realCheckinEndDate" autocomplete="off" class="layui-input" placeholder="真实退住时间" value="#date(er.realCheckoutTime??,'yyyy-MM-dd HH:mm:ss')" readonly>
			        </div>
				</div>
		        #end
				#if(user!=null)
				<div class="layui-inline">
					<label class="layui-form-label">创建人</label>
					<div class="laydate-input-inline" style="display:inline-block;">
						<input type="text"  autocomplete="off" class="layui-input"
							   placeholder="" value="#(user.name??)" readonly>
					</div>
				</div>
				#end
			</div>
	    </div>
		<div class="layui-row">
			<label class="layui-form-label">
				备注
			</label>
			<div class="layui-input-block">
				<textarea id="erRemark" placeholder="请输入内容" class="layui-textarea">#(er.remark)</textarea>
			</div>
		</div>
	</form>
    <div class="layui-row layui-col-space10">
        <div class="layui-form">
            <div class="bill-detail-search" style="margin-left: 0px;padding-bottom: 30px;">
                <div class="layui-form-item d-tab">
                    <div id="dataTab" class="layui-tab layui-tab-card assess" lay-filter="dataTab">
                        <ul class="layui-tab-title" id="idui">
                            <li lay-id="costBill" class="layui-this">费用账单</li>
                            <li lay-id="deductCardTotal">扣卡汇总</li>
                            <li lay-id="stayinRecord">续住记录</li>
                            <li lay-id="changeCardRecord">更换会员卡记录</li>
                            <li lay-id="followRecord">随行人员</li>
                            <li lay-id="cashRecord">现金/纸卡</li>
                        </ul>
                    </div>
                </div>
                <div class="layui-row" id="penalDiv">
                    <div class="layui-collapse" id="costBillDiv" lay-filter="costBillDiv"></div>
                </div>
                <div class="layui-row" id="tabDiv" style="display: none;">
                    <table id="dataTable" lay-filter="dataTable" class="layui-table"></table>
                </div>
                <div class="layui-row" id="deductDiv" style="display: none;">
                    <fieldset class="layui-elem-field layui-field-title site-demo-button">
                        <legend>扣卡总结</legend>
                    </fieldset>
                    <table id="settleMainTable" lay-filter="settleMainTable" class="layui-table"></table>
                    <fieldset class="layui-elem-field layui-field-title site-demo-button" style="margin-top: 30px;">
                        <legend>扣卡明细</legend>
                    </fieldset>
                    <table id="settleDetailTable" lay-filter="settleDetailTable" class="layui-table"></table>
                </div>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="settleStatus">
#[[
    {{#if(d.settleStatus==='0'){}}
    	<span class='layui-badge layui-bg-orange'>未结算</span>
    {{#}else{}}
    	<span class='layui-badge layui-bg-green'>已结算</span>
    {{#}}}
]]#
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看离开记录</a>
</script>
<script type="text/javascript">
    layui.use(['table','layer','form','jquery','element','laydate'],function(){
        var table = layui.table, $ = layui.jquery, element = layui.element,form=layui.form,laydate=layui.laydate;

        //查看床位信息
        $("#lookBedInfo").click(function(){
            var url = "#(ctxPath)/fina/expenseRecord/bedForm?bedId=" + $("#bedId").val();
            layerShow("查看床位价格信息",url,800,300);
        });

        $("#file").click(function () {
			var url = "#(ctxPath)/fina/expenseRecord/bookFile?bookNo=" + $("#bookNo").val();
			layerShow("预定附件",url,1200,500);
		})

        //切换数据行
        changeDataCss = function(dataType){
            if(dataType == 'costBill'){
                $("#penalDiv").css("display","block");
                $("#tabDiv").css("display","none");
                $("#deductDiv").css("display", "none");
            }else if(dataType=='deductCardTotal'){
                $("#deductDiv").css("display", "block");
                $("#penalDiv").css("display","none");
                $("#tabDiv").css("display","none");
            }else{
                $("#penalDiv").css("display","none");
                $("#tabDiv").css("display","block");
                $("#deductDiv").css("display", "none");
            }
        }


        //拼接折叠板标题内容
        appenTr=function(id,baseName,name,bedName,startTime,endTime,type,totalTimes,dictName,cardNumber,fullName,withholdTimes,actualTimes,withholdAmount,
                         actualAmount,withholdPoints,actualPoints,withholdIntegrals,actualIntegrals,settleStatus,isPrivateRoom,officeName,remark,followDeductTypeStr){
            var divStr='<div class="layui-colla-item" id="'+id+'">'
                +'<h2 class="layui-colla-title" style="font-size:10px;height:55px;line-height:25px;">'
                +'<b>'+ baseName +'&nbsp;&nbsp;</b>'
                +'<b style="color: red;">'+ type +'&nbsp;&nbsp;</b>'
                +'<b>入住人：'+ name +'&nbsp;&nbsp;</b>'
                +'<b>床位：'+ bedName +'&nbsp;&nbsp;</b>'
                +'<b>时间：'+ startTime +'至'+ endTime +'&nbsp;&nbsp;</b>'
                +'<b>共入住'+ totalTimes +'天&nbsp;&nbsp;</b>'
                +'<b>入住状态：'+ dictName +'&nbsp;&nbsp;&nbsp;</b>'
                +'<b>包房：'+ isPrivateRoom +'&nbsp;&nbsp;&nbsp;</b><br/>'
                +'<b>扣卡：'+ cardNumber +'('+fullName+ ')&nbsp;&nbsp;</b>'
                +'<b>分公司：'+ officeName +'&nbsp;&nbsp;&nbsp;</b>'
                +'<b>剩余锁定天数：'+ withholdTimes +'天&nbsp;&nbsp;</b>'
                +'<b>已扣天数：'+ actualTimes +'天&nbsp;&nbsp;</b>'
                +'<b>剩余锁定金额：'+ withholdAmount +'元&nbsp;&nbsp;</b>'
                +'<b>已扣金额：'+ actualAmount +'元&nbsp;&nbsp;</b>'
                +'<b>剩余锁定点数：'+ withholdPoints +'&nbsp;&nbsp;</b>'
                +'<b>已扣点数：'+ actualPoints +'&nbsp;&nbsp;</b>'
                +'<b>剩余锁定积分：'+ withholdIntegrals +'&nbsp;&nbsp;</b>'
                +'<b>已扣积分：'+ actualIntegrals +'&nbsp;&nbsp;</b>'
					+'<b>'+ followDeductTypeStr +'&nbsp;&nbsp;</b>'
                +'<b>结算状态：'+ settleStatus +'&nbsp;&nbsp;</b>'
                +'<b style="display:none;" id="id-'+id+'">备注：'+ remark +'</b>'
                +'</h2>'
                +'<div class="layui-colla-content">'
                +'<table id="table-'+id+'" lay-filter="table='+id+'" class="layui-table"></table>'
                +'</div>'
                +'</div>'
            return divStr;
        }

        $("#saveRemark").on('click',function () {
            var id=$("#erId").val();
            var erRemark=$("#erRemark").val();
            util.sendAjax({
                type: 'POST',
                url: '#(ctxPath)/fina/expenseRecord/updateRemark',
                data: {"id":id,"remark":erRemark},
                notice: true,
                loadFlag: true,
                success: function (rep) {

                },
                complete: function () {
                }
            });
        })



        //获取费用账单
        getCostRecord=function(data){

            util.sendAjax({
                url:'#(ctxPath)/fina/expenseRecord/queryParentAndElementRecord',
                type:'post',
                data:data,
                notice:false,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        var list = returnData.data;
                        var costAppend = "";
                        for(var i=0;i<list.length;i++){
                            var baseName = list[i].baseName;
                            var name = list[i].name;
                            var bedName = list[i].bedName;
                            var startTime = dateFormat(list[i].startTime,'yyyy-MM-dd');
                            var endTime = dateFormat(list[i].endTime,'yyyy-MM-dd');
                            var type = list[i].type;//消费类型
                            var dictName = list[i].dictName;
                            var cardNumber = list[i].cardNumber;
                            var fullName = list[i].fullName;
                            var withholdTimes = String(list[i].withholdTimes);
                            var actualTimes = String(list[i].actualTimes);
                            var withholdAmount = String(list[i].withholdAmount);
                            var actualAmount = String(list[i].actualAmount);
                            var withholdPoints = String(list[i].withholdPoints);
                            var actualPoints = String(list[i].actualPoints);
                            var withholdIntegrals = String(list[i].withholdIntegrals);
                            var actualIntegrals = String(list[i].actualIntegrals);
                            var settleStatus = list[i].settleStatus;
                            var totalTimes = String(list[i].totalTimes);
                            var isPrivateRoom = list[i].isPrivateRoom;
                            var officeName = list[i].officeName;
                            var remark = list[i].remark;
							var followDeductType = list[i].followDeductType;
                            if(baseName == null || baseName == ''){
                                baseName = '基地暂不知';
                            }
                            if(name == null || name == ''){
                                name = '- -';
                            }
                            if(bedName == null || bedName == ''){
                                bedName = '- -';
                            }
                            if(startTime == null || startTime == ''){
                                startTime = '- -';
                            }
                            if(endTime == null || endTime == ''){
                                endTime = '- -';
                            }
                            if(officeName == null || officeName == ''){
                                officeName = '- -';
                            }
                            if(remark == null || remark == ''){
                                remark = "- -";
                            }
                            if(type == null || type == ''){
                                type = '- -';
                            }else if(type != null && type != ''){
                                type = type == 'normal_admission'?'正常入住':type == 'continued_residence'?'续住':'随行入住';
                            }
                            if(totalTimes == null || totalTimes == ''){
                                totalTimes = '- -';
                            }
                            if(dictName == null || dictName == ''){
                                dictName = '- -';
                            }
                            if(cardNumber == null || cardNumber == ''){
                                cardNumber = '- -';
                            }
                            if(fullName == null || fullName == ''){
                                fullName = '- -';
                            }
                            if(isPrivateRoom == null || isPrivateRoom == ''){
                                isPrivateRoom = '- -';
                            }else if(isPrivateRoom != null && isPrivateRoom != '') {
                                if (isPrivateRoom == '0') {
                                    isPrivateRoom = '否';
                                } else if (isPrivateRoom == '1') {
                                    isPrivateRoom = '是';
                                }
                            }
                            if(withholdTimes == null || withholdTimes == ''){
                                withholdTimes = '- -';
                            }
                            if(actualTimes == null || actualTimes == ''){
                                actualTimes = '- -';
                            }
                            if(withholdAmount == null || withholdAmount == ''){
                                withholdAmount = '- -';
                            }
                            if(actualAmount == null || actualAmount == ''){
                                actualAmount = '- -';
                            }
                            if(withholdPoints == null || withholdPoints == ''){
                                withholdPoints = '- -';
                            }
                            if(actualPoints == null || actualPoints == ''){
                                actualPoints = '- -';
                            }
                            if(withholdIntegrals == null || withholdIntegrals == ''){
                                withholdIntegrals = '- -';
                            }
                            if(actualIntegrals == null || actualIntegrals == ''){
                                actualIntegrals = '- -';
                            }
                            if(settleStatus == null || settleStatus == ''){
                                settleStatus == '- -';
                            }else if(settleStatus != null && settleStatus != ''){
                                if(settleStatus == '0'){
                                    settleStatus = '未结算';
                                }else if(settleStatus == '1'){
                                    settleStatus = '已结算';
                                }else if(settleStatus == '2'){
                                    settleStatus = '作废';
                                }
                            }

							var followDeductTypeStr='';
							if(followDeductType != null && followDeductType != ''  && typeof(followDeductType)!="undefined"){
								followDeductTypeStr= '随行扣费规则：';
								if(followDeductType=='Whole'){
									followDeductTypeStr+="全额";
								}else if(followDeductType=='Half'){
									followDeductTypeStr+="半价";
								}else if(followDeductType=='Exemption'){
									followDeductTypeStr+="免费";
								}
							}

                            costAppend += appenTr(list[i].id,baseName,name,bedName,startTime,endTime,type,totalTimes,
                            dictName,cardNumber,fullName,withholdTimes,actualTimes,withholdAmount,actualAmount,withholdPoints,actualPoints
                                ,withholdIntegrals,actualIntegrals,settleStatus,isPrivateRoom,officeName,remark,followDeductTypeStr);
                        }
                        $("#costBillDiv").html(costAppend);
                        element.init();
                    }else{
                        layer.msg("获取数据失败");
                    }
                }
            });
        }


        //获取费用明细列表
        detailRecordTable=function(id,tabId){
            if(id == "" || id == null || id == undefined){
                return false;
            }
            table.render({
                id : tabId,
                elem : '#'+ tabId ,
                toolbar: '#toolbarDemo',
                method:'post',
                url : '#(ctxPath)/fina/expenseRecord/queryDetailByParent' ,
                where: {id: id},
                cols : [[
                    {type: 'numbers', width:100, title: '序号',unresize:true,width: 80},
                    {field:'cardNumber',title:'会员卡',unresize:true,templet:function (d) {
                        var str="";
                        if(d.cardNumber != null && d.cardNumber != ''){
                            str = d.cardNumber;
                        }else{
                            str = "--";
                        }
                        str += " ";
                        if(d.fullName != null && d.fullName != null){
                            str += d.fullName;
                        }else{
                            str += "--";
                        }
                        return str;
                    }},
                    {field:'bedName',title:'床位',unresize:true},
                    {field:'startTime',title:'时间',unresize:true,width:'30%',templet:function (d) {
                        var str="";
                        if(d.startTime != null && d.startTime != ''){
                            str = dateFormat(d.startTime,'yyyy-MM-dd HH:mm:ss');
                        }else{
                            str = "--";
                        }
                        str += " 至 ";
                        if(d.endTime != null && d.endTime != null){
                            str += dateFormat(d.endTime,'yyyy-MM-dd HH:mm:ss');
                        }else{
                            str += "--";
                        }
                        return str;
                    }},
                    {field:'times',title:'扣卡天数',unresize:true,templet: "<div>{{ String(d.times)? String(d.times) + '天': '- -' }}</div>"},
                    {field:'amount',title:'扣卡金额',unresize:true,templet: "<div>{{ String(d.amount)? String(d.amount) + '元': '- -' }}</div>"},
                    {field:'points',title:'扣卡点数',unresize:true,templet: function (d) {
                            if(typeof(d.points)==='undefined'){
                                return '0.0';
                            }else{
                                return d.points;
                            }
                        }},
                    {field:'integrals',title:'扣卡积分',unresize:true,templet: function (d) {
                            if(typeof(d.integrals)==='undefined'){
                                return '0.0';
                            }else{
                                return d.integrals;
                            }
                        }},
                    /*{field:'status',title:'状态',unresize:true,templet:"<div>{{ d.status=='0'?'<span class='layui-badge layui-bg-green'>未执行</span>':d.status=='1'?'<span class='layui-badge layui-bg-orange'>已执行</span>':d.status=='2'?'<span class='layui-badge layui-bg'>作废</span>':'- -' }}</div>"},*/
                    {field:'isSettled',title:'是否结算',unresize:true,templet:"<div>{{ d.isSettled=='0'?'<span class='layui-badge layui-bg-green'>未结算</span>':d.isSettled=='1'?'<span class='layui-badge layui-bg-orange'>已结算</span>':'- -' }}</div>"}
                ]]
            }) ;
        }


        //扣卡汇总
        deductDataTabLoad = function(data){
            table.render({
                id : 'settleMainTable'
                ,elem : '#settleMainTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/expenseRecord/deductCardTotal'
                ,cols: [[
                    {field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
                    ,{field:'fullName', title: '卡主', align: 'center',unresize: true}
                    /*,{field:'withholdTimes', title: '锁定', align: 'center',unresize: true,width:250,templet:function (d) {
                            var str = "";
                            if(String(d.withholdTimes) != null && String(d.withholdTimes) != ''){
                                str = String(d.withholdTimes) +"天";
                            }else{
                                str = "- -";
                            }
                            str += " ";
                            if(String(d.withholdAmount) != null && String(d.withholdAmount) != null){
                                str += String(d.withholdAmount) +"元";
                            }else{
                                str += "- -";
                            }
                            str += " ";
                            if(String(d.withholdPoints) != null && String(d.withholdPoints) != null){
                                if(typeof(d.withholdPoints)=='undefined'){
                                    str += '0点数'
                                }else{
                                    str += String(d.withholdPoints) +"点数";
                                }

                            }else{
                                str += "- -";
                            }
                            return str;
                        }}*/
                    /*,{field:'surplusLockTimes', title: '剩余锁定', align: 'center',unresize: true,templet:function (d) {
                            var str = "";
                            if(String(d.surplusLockTimes) != null && String(d.surplusLockTimes) != ''){
                                str = String(d.surplusLockTimes) +"天";
                            }else{
                                str = "- -";
                            }
                            str += " ";
                            if(String(d.surplusLockAmount) != null && String(d.surplusLockAmount) != null){
                                str += String(d.surplusLockAmount) +"元";
                            }else{
                                str += "- -";
                            }
                            return str;
                        }}*/
                    /*,{field:'deductedTimes', title: '已扣', align: 'center',unresize: true,templet:function (d) {
                            var str = "";
                            if(String(d.deductedTimes) != null && String(d.deductedTimes) != ''){
                                if(typeof(d.deductedTimes)=='undefined'){
                                    str = "0天";
                                }else{
                                    str = String(d.deductedTimes) +"天";
                                }
                            }else{
                                str = "- -";
                            }
                            str += " ";
                            if(String(d.deductedAmount) != null && String(d.deductedAmount) != null){
                                if(typeof(d.deductedAmount)=='undefined'){
                                    str += "0元";
                                }else{
                                    str += String(d.deductedAmount) +"元";
                                }
                            }else{
                                str += "- -";
                            }
                            str += " ";
                            if(String(d.deductedPoints) != null && String(d.deductedPoints) != null){
                                if(typeof(d.deductedPoints)=='undefined'){
                                    str += "0点数";
                                }else{
                                    str += String(d.deductedPoints) +"点数";
                                }
                            }else{
                                str += "- -";
                            }
                            return str;
                        }}*/
                    ,{field:'deductedTimes', title: '已扣天数', align: 'center',unresize: true,templet:function (d) {
                            var str = "";
                            if(String(d.deductedTimes) != null && String(d.deductedTimes) != ''){
                                if(typeof(d.deductedTimes)=='undefined'){
                                    str = "0天";
                                }else{
                                    str = String(d.deductedTimes) +"天";
                                }
                            }else{
                                str = "- -";
                            }
                            return str;
                        }}
                    ,{field:'deductedAmount', title: '已扣金额', align: 'center',unresize: true,templet:function (d) {

                            var str = " ";
                            if(String(d.deductedAmount) != null && String(d.deductedAmount) != null){

                                if(typeof(d.deductedAmount)=='undefined'){
                                    str = "0元";
                                }else{
                                    str += String(d.deductedAmount) +"元";
                                }
                            }else{
                                str += "- -";
                            }
                            return str;
                        }}
                    ,{field:'deductedPoints', title: '已扣点数', align: 'center',unresize: true,templet:function (d) {

                            var str = " ";
                            if(String(d.deductedPoints) != null && String(d.deductedPoints) != null){

                                if(typeof(d.deductedPoints)=='undefined'){
                                    str = "0点数";
                                }else{
                                    str += String(d.deductedPoints) +"点数";
                                }
                            }else{
                                str += "- -";
                            }
                            return str;
                        }}
                    ,{field:'deductedIntegrals', title: '已扣积分', align: 'center',unresize: true,templet:function (d) {

                            var str = " ";
                            if(String(d.deductedIntegrals) != null && String(d.deductedIntegrals) != null){

                                if(typeof(d.deductedIntegrals)=='undefined'){
                                    str = "0积分";
                                }else{
                                    str += String(d.deductedIntegrals) +"积分";
                                }
                            }else{
                                str += "- -";
                            }
                            return str;
                        }}
                    /*,{field:'actualTimes', title: '实扣', align: 'center',unresize: true,templet:function (d) {
                        var str = "";
                        if(String(d.actualTimes) != null && String(d.actualTimes) != ''){
                            str = String(d.actualTimes) +"天";
                        }else{
                            str = "- -";
                        }
                        str += " ";
                        if(String(d.actualAmount) != null && String(d.actualAmount) != null){
                            str += String(d.actualAmount) +"元";
                        }else{
                            str += "- -";
                        }
                        return str;
                    }}*/
                ]]
            });

            //扣卡明细
            table.render({
                id : 'settleDetailTable'
                ,elem : '#settleDetailTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/settleDetail/findSettleDetailList'
                ,cols: [[
                    {field:'cardNumber', title: '会员卡', align: 'center', unresize: true,width:180,templet:"<div>{{d.cardNumber}}-{{d.fullName}}</div>"}
                    ,{field:'settleType', title: '结算类型', align: 'center',unresize: true,width:100,templet:function (d) {
                            if(d.settleType==='month_settle'){
                                return "月结算";
                            }else if(d.settleType==='checkout_settle'){
                                return "退住结算";
                            }else{
                                return "- -";
                            }
                        }}
                    ,{field:'yearMonth',title: '账单月份', align: 'center', unresize: true,width:100}
                    ,{field:'date', title: '产生时间', align: 'center', unresize: true,width:220,templet:"<div>{{dateFormat(d.startTime,'yyyy-MM-dd')}}至{{dateFormat(d.endTime,'yyyy-MM-dd')}}</div>"}
                    ,{field:'total', title: '锁定', align: 'center',unresize: true,width:200,templet:function (d) {
                            var str = "";
                            if(String(d.totalTimes) != null && String(d.totalTimes) != ''){
                                if(typeof(d.totalTimes)=='undefined'){
                                    str = "0天";
                                }else{
                                    str = String(d.totalTimes) +"天";
                                }
                            }else{
                                str = "- -";
                            }
                            str += " ";
                            if(String(d.totalAmount) != null && String(d.totalAmount) != null){
                                if(typeof(d.totalAmount)=='undefined'){
                                    str+="0元";
                                }else{
                                    str += String(d.totalAmount) +"元";
                                }
                            }else{
                                str += "- -";
                            }
                            str += " ";
                            if(String(d.totalPoints) != null && String(d.totalPoints) != null){
                                if(typeof(d.totalPoints)=='undefined'){
                                    str+="0点数";
                                }else{
                                    str += String(d.totalPoints) +"点数";
                                }
                            }else{
                                str += "- -";
                            }
							if(String(d.totalIntegrals) != null && String(d.totalIntegrals) != null){
								if(typeof(d.totalIntegrals)=='undefined'){
									str+="0积分";
								}else{
									str += String(d.totalIntegrals) +"积分";
								}
							}else{
								str += "- -";
							}
                            return str;
                        }}
                    ,{field:'actualTimes', title: '扣卡天数', align: 'center',unresize: true,width:130}
                    ,{field:'actualAmount', title: '扣卡金额', align: 'center',unresize: true,width:130}
                    ,{field:'actualPoints', title: '扣卡点数', align: 'center',unresize: true,width:130}
                    ,{field:'actualIntegrals', title: '扣卡积分', align: 'center',unresize: true,width:130}
                    ,{field:'settleStatus', title: '结算状态', align: 'center',unresize: true,width:120,templet:'#settleStatus'}
                    ,{field:'settleRemark', title: '结算备注', align: 'center',unresize: true,templet:"#inputRemarkTpl"}
                ]]
				,done:function () {
					//
					var layerTips;
					$("td").on("mouseenter", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						if (this.offsetWidth < this.firstChild.scrollWidth) {
							var that = this;
							var text = $(this).text();
							layerTips=layer.tips(text, that, {
								tips: 1,
								time: 0
							});
						}
					});
					$("td").on("mouseleave", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						layer.close(layerTips);
					});
					layer.closeAll('loading');
				}
            });
        };


        //续住记录
        stayinDataTabLoad = function(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/expenseRecord/queryStayinRecord'
                ,cols: [[
                    {field:'cardNumber', title: '会员卡', align: 'center', unresize: true}
                    ,{field:'idCard', title: '身份证', align: 'center',unresize: true}
                    ,{field:'name', title: '入住人', align: 'center',unresize: true}
                    ,{field:'bedName', title: '床位', align: 'center',unresize: true}
                    ,{field:'startTime', title: '续住开始时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.startTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'endTime', title: '续住结束时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.endTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ]]
            });
        };


        //会员卡变更记录
        changeCardDataTabLoad = function(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/expenseRecord/newQueryChangeCardRecord'
                ,cols: [[
                    {field:'fullName', title: '入住人', align: 'center', unresize: true}
                    ,{field:'fullName', title: '订单类型', align: 'center', unresize: true,templet:function (d) {
                            if(d.expenseType==='normal_admission'){
                                return '<button type="button" class="layui-btn-xs layui-btn">主记录</button>'
                            }else if(d.expenseType==='continued_residence'){
                                return '<button type="button" class="layui-btn-xs layui-btn layui-btn-normal">续住记录</button>'
                            }else if(d.expenseType==='follow_checkin'){
                                return '<button type="button" class="layui-btn-xs layui-btn" style="background-color: #5FB878;">随行记录</button>'
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'checkinTime', title: '入住时间', align: 'center',unresize: true ,templet:"<div>{{ dateFormat(d.checkinTime,'yyyy-MM-dd') }}</div>"}
                    ,{field:'cardNumber', title: '会员卡', align: 'center', unresize: true}
                    ,{field:'cardName', title: '卡主', align: 'center',unresize: true}
                    ,{field:'type', title: '类型', align: 'center',unresize: true,templet:"<div>{{ d.type=='0'?'<span class='layui-badge layui-bg-blue'>更换会员卡</span>':d.type=='1'?'<span class='layui-badge layui-bg-orange'>转移会员卡</span>':'- -' }}</div>"}
                    ,{field:'startTime', title: '开始时间', align: 'center',unresize: true ,templet:"<div>{{ dateFormat(d.startTime,'yyyy-MM-dd') }}</div>"}
                    ,{field:'endTime', title: '结束时间', align: 'center',unresize: true ,templet:"<div>{{ dateFormat(d.endTime,'yyyy-MM-dd') }}</div>"}
                    ,{field:'isAll', title: '更换类型', align: 'center',unresize: true,templet:"<div>{{ d.isAll=='1'?'<span class='layui-badge'>整单更换</span>':d.isAll=='0'?'<span class='layui-badge layui-btn-normal'>单条更换</span>':'- -' }}</div>"}
                    //,{field:'useFlag', title: '是否使用', align: 'center',unresize: true,templet:"<div>{{ d.useFlag=='1'?'<span class='layui-badge layui-bg-blue'>启用</span>':d.useFlag=='0'?'<span class='layui-badge layui-bg-orange'>停用</span>':'- -' }}</div>"}
                    ,{field:'remark', title: '备注', align: 'center',unresize: true}
                    ,{field:'createTime', title: '更换时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ]]
            });
        };


        //获取随行人员
        followDataTabLoad = function(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/expenseRecord/queryFollowRecord'
                ,cols: [[
                    {field:'name', title: '姓名', align: 'center', unresize: true}
                    ,{field:'idCard', title: '身份证', align: 'center',unresize: true}
                    ,{field:'cardNumber', title: '会员卡', align: 'center',unresize: true}
                    ,{field:'fullName', title: '卡主', align: 'center',unresize: true}
                    ,{field:'bedName', title: '床位', align: 'center',unresize: true}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
            });
        };

        //随行人员列表工具栏
        table.on('tool(dataTable)',function(obj){
            if(obj.event === 'detail'){
                var url = "#(ctxPath)/fina/expenseRecord/followLeaveForm?id=" + obj.data.expenseId;
                layerShow("查看离开记录",url,600,450);
            }
        });


        //获取现金/纸卡记录
        cashDataTabLoad = function(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/expenseRecord/queryCashRecords'
                ,cols: [[
                    {field:'amountNum', title: '数量/金额', align: 'center', unresize: true,width:120}
                    ,{field:'type', title: '类型', align: 'center',unresize: true,width:120,templet:"<div>{{ d.type=='1'?'<span class='layui-badge layui-bg-blue'>金额纸卡</span>':d.type=='2'?'<span class='layui-badge layui-bg-orange'>现金</span>':d.type=='3'?'<span class='layui-badge layui-bg-orange'>天数纸卡</span>':'- -' }}</div>"}
                    ,{field:'receiveName', title: '收款人', align: 'center',unresize: true,width:120}
                    ,{field:'receiveTime', title: '收款时间', align: 'center',unresize: true,width:200,templet:"<div>{{ dateFormat(d.receiveTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'remark', title: '备注', align: 'center',unresize: false}
                ]]
            });
        };


        //点击面板事件
        element.on('collapse(costBillDiv)', function(data){
            if(data.show){
                var id = $(this).parent().attr("id");
                var tabId = $(this).next().children(":first").attr("id");
                detailRecordTable(id,tabId);
                $("#"+id).find(".layui-table-tool").html("<div style='font-size:10px;'>"+$("#id-"+id).text()+"</div>");
            }
        });

        //监听监测类型页签切换方法
        element.on('tab(dataTab)', function(){
            var data = {"id":$("#erId").val()};
            var dataType = $(this).attr("lay-id");
            changeDataCss(dataType);
            if(dataType=='costBill'){
                getCostRecord(data);
            }else if(dataType=='deductCardTotal'){
                deductDataTabLoad(data);
            }else if(dataType=='stayinRecord'){
                stayinDataTabLoad(data);
            }else if(dataType=='changeCardRecord'){
                changeCardDataTabLoad(data);
            }else if(dataType == 'followRecord'){
                followDataTabLoad(data);
            }else if(dataType == 'cashRecord'){
                cashDataTabLoad(data);
            }
            $(".layui-btn-group button").each(function(){
                var queryType = $(this).attr("id");
                if(queryType=='costBill'){
                    if($(this).hasClass("layui-btn-primary")){
                        $(this).removeClass("layui-btn-primary");
                    }
                }else{
                    if(!$(this).hasClass("layui-btn-primary")){
                        $(this).addClass("layui-btn-primary");
                    }
                }
            });
        });

        //页面打开加载
        $(function(){
            if($("#erId").val() != null && $("#erId").val() != ''){
                var data = {id:$("#erId").val()}
                getCostRecord(data);
            }
        });
    });
</script>
#end