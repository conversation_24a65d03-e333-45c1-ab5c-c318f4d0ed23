#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团更换会员卡和更新财务备注页面#end

#define css()
#end

#define js()
<script type="text/javascript">
    layui.use(['form'],function(){
        var form = layui.form;
        var $ = layui.$;


        //校验
        form.verify({
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            },
            checkAmount:function(value){
                if(value != null){
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if(!reg1.test(value) && !reg2.test(value)){
                        return "只能输入整数字和小数点后两位小数";
                    }
                }
            }
        });


        getCardInfo=function(obj){
            layer.load();
            let cardNumber =$(obj).val();
            $.post("#(ctxPath)/fina/cardmanager/getCardByCardNumber?cardNumber="+cardNumber+"&delFlag=0",function (d) {
                if(d.state==='ok'){
                    //$(obj).closest('td').next().text(d.data.fullName);
                    let cardNumberInfo=cardNumber+"；卡主："+d.data.fullName+"；";
                    $("#cardId").val(d.data.id);
                    if(d.data.deductWay=="deduct_by_days"){
                        cardNumberInfo+="可用天数："+d.data.consumeTimes+"；"
                    }
                    if(d.data.deductWay=="deduct_by_money"){
                        cardNumberInfo+="可用余额："+d.data.balance+"；"
                    }
                    if(d.data.isIntegral=="1"){
                        cardNumberInfo+="可用积分："+d.data.cardIntegrals+"；"
                    }

                    if(d.data.deductWay!="deduct_by_days"){
                        //禁用天数框
                        let actualTimes=$("#frm").find("input[name='actualTimes']");
                        actualTimes.prop("readonly",true);
                        actualTimes.addClass("layui-disabled");

                    }
                    if(d.data.deductWay!="deduct_by_money"){
                        //禁用天数框
                        let actualAmountInput=$("#frm").find("input[name='actualAmount']");
                        actualAmountInput.prop("readonly",true);
                        actualAmountInput.addClass("layui-disabled");

                    }
                    if(d.data.isIntegral!="1"){
                        //禁用积分框
                        let actualIntegrals=$("#frm").find("input[name='actualIntegrals']");
                        actualIntegrals.prop("readonly",true);
                        actualIntegrals.addClass("layui-disabled");
                    }

                    if(d.data.deductWay=='deduct_by_days'){

                        let oldActualTimes=$("#oldActualTimes").val();
                        let oldActualIntegrals=$("#oldActualIntegrals").val();
                        let total=Number(oldActualTimes)+Number(oldActualIntegrals);
                        if(d.data.isIntegral=="1"){
                            //积分卡
                            if(d.data.cardIntegrals>total){
                                $("#actualIntegrals").val(total);
                                $("#actualTimes").val(0);
                                $("#actualAmount").val(0);
                            }else{
                                $("#actualIntegrals").val(Math.floor(d.data.cardIntegrals));
                                $("#actualTimes").val(total-Math.floor(d.data.cardIntegrals));
                                $("#actualAmount").val(0);
                            }
                        }else{
                            $("#actualTimes").val(total);
                            $("#actualAmount").val(0);
                            $("#actualIntegrals").val(0);
                        }
                    }else if(d.data.deductWay=='deduct_by_money'){
                        //  oldActualAmount
                        if($("#oldActualAmount").val()>0){
                            $("#actualAmount").val($("#oldActualAmount").val());
                            $("#actualTimes").val(0);
                            $("#actualIntegrals").val(0);
                        }
                    }

                    $("#cardInfoP").text(cardNumberInfo);
                    layer.closeAll('loading');
                }else{
                    layer.msg(d.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll('loading');
                }
            })
        }


        //保存
        form.on('submit(confirmBtn)', function () {

            console.log($("#frm").serialize());
            util.sendAjax({
                type: 'POST',
                url: '#(ctxPath)/fina/tourist/touristChangeCard',
                data: $("#frm").serialize(),
                notice: false,
                loadFlag: true,
                success: function (rep) {
                    if (rep.state == 'ok') {
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete: function () {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse">
    <div class="layui-row">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="frm" style="margin-top:10px;margin-left: 10px;margin-right:10px;">
            <div class="layui-form-item">


                <div class="layui-inline">
                    <label class="layui-form-label">跟团人</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly="readonly" value="#(expenseRecord.name??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 5px;">原会员卡号</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly="readonly" value="#(card.cardNumber??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 5px;">原扣卡值</label>
                    <div class="layui-input-inline">
                        <input type="text" readonly="readonly" value="#(deductStr??)" autocomplete="off" class="layui-input">
                        <input id="oldActualTimes" type="hidden" value="#(touristSettleDetail.getActualTimes()??0)">
                        <input id="oldActualIntegrals" type="hidden" value="#(touristSettleDetail.getActualIntegrals()??0)">
                        <input id="oldActualAmount" type="hidden" value="#(touristSettleDetail.getActualAmount()??0)">
                    </div>
                </div>
                <hr style="color: #9F9F9F;"/>
                &nbsp;
                <p id="cardInfoP" style="padding-left: 110px;margin-bottom: 10px;">&nbsp;</p>
                <div class="layui-block">
                    <label class="layui-form-label">会员卡号</label>
                    <div class="layui-input-block">
                        <input type="text" name="cardNumber" value="#(er.cardNumber??)" placeholder="请输入会员卡号" onblur="getCardInfo(this)" autocomplete="off" class="layui-input">
                        <input type="hidden" name="cardId" id="cardId" value="">
                    </div>
                </div>
                &nbsp;
                <div class="layui-block">
                    <label class="layui-form-label" style=" ">扣卡天数</label>
                    <div class="layui-input-block">
                        <input type="text" id="actualTimes" name="actualTimes" value="" autocomplete="off" class="layui-input" lay-verify="checkAmount">
                    </div>
                </div>
                &nbsp;
                <div class="layui-block">
                    <label class="layui-form-label" style=" ">扣卡积分</label>
                    <div class="layui-input-block">
                        <input type="text" id="actualIntegrals" name="actualIntegrals" value="" autocomplete="off" class="layui-input" lay-verify="checkAmount">
                    </div>
                </div>
                &nbsp;
                <div class="layui-block">
                    <label class="layui-form-label" style=" ">扣卡金额</label>
                    <div class="layui-input-block">
                        <input type="text" id="actualAmount" name="actualAmount" value="" autocomplete="off" class="layui-input" lay-verify="checkAmount">
                    </div>
                </div>
                <!--<div class="layui-block">
                    <label class="layui-form-label">财务备注</label>
                    <div class="layui-input-block">
                        <textarea class="layui-textarea" name="financeRemark">#(er.financeRemark??)</textarea>
                    </div>
                </div>-->
            </div>



            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(touristSettleDetail.id??)"/>
                    <input type="hidden" name="touristNo" value="#(touristNo??)"/>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end