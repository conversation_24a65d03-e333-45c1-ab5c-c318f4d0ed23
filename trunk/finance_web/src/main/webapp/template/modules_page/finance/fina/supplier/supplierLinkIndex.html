#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商联系人查询#end

#define css()
#end

#define content()
<div class="my-btn-box">
   	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
    <div class="layui-row">
        <div class="layui-inline">
	        <label class="layui-form-label">供应商</label>
	        <div class="layui-input-inline">
	        	<select name="supplierId">
	        		<option value="">全部</option>
		            #for(s : suppliersList)
						<option value="#(s.id)">#(s.name)</option>
	                #end
	        	</select>
	        </div>
        </div>
        <div class="layui-inline">
			<label class="layui-form-label">联系人类型</label>
			<div class="layui-input-inline" style="width:150px;">
				<select name="linkType">
					<option value="">全部</option>
					#statusOption(com.cszn.integrated.service.entity.status.LinkType::me(), "")
				</select>
			</div>
		</div>
        <div class="layui-inline">
	        <label class="layui-form-label">联系人名称</label>
	        <div class="layui-input-inline">
	            <input id="linkName" name="linkName" class="layui-input">
	        </div>
        </div>
        <div class="layui-inline">
        	<div class="layui-btn-group">
		        <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		        #shiroHasPermission("main:supplierLink:editBtn")
       				<button class="layui-btn" type="button" id="addBtn">添加</button>
				#end
      			</div>
        </div>
    </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="supplierLinkTable" lay-filter="supplierLinkTable"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
	#shiroHasPermission("main:supplierLink:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("main:supplierLink:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

	supplierLinkTableReload = function (data) {
        table.render({
            id : 'supplierLinkTable'
            ,elem : '#supplierLinkTable'
            ,method : 'POST'
            ,where : data
            ,limit : 10
            ,limits : [10,20,30,40]
            ,url : '#(ctxPath)/wms/supplier/linkPage'
           	,height:'full-90'
            ,cols: [[
            	{field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
				,{field:'supplierName', title: '供应商名称', align: 'center', unresize: true}
				,{field:'', title: '联系人类型', align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.LinkType::me(), "linkType")'}
				,{field:'linkName', title: '联系人名称', align: 'center', unresize: true}
				,{field:'linkPosition', title: '联系人职位', align: 'center', unresize: true}
				,{field:'linkPhone', title: '联系人电话', align: 'center', unresize: true}
				,{field:'linkWechat', title: '联系人微信', align: 'center', unresize: true}
                ,{fixed:'right', title: '操作', width: 230, align: 'center', unresize: true, toolbar: '#toolBar'}
            ]]
            ,page : true
            ,done:function () {
            }
        });
        table.on('tool(supplierLinkTable)',function (obj) {
            if(obj.event==='edit'){
                pop_show('编辑','#(ctxPath)/wms/supplier/supplierLinkForm?id='+obj.data.id,'','');
            }else if(obj.event==='del'){
            	layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/wms/supplier/saveLink',
                        notice: true,
                        data: {id:obj.data.id, delFlag:'1'},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                            	table.reload('supplierLinkTable');
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });
	}
	
	supplierLinkTableReload(null);
	
	form.on("submit(search)",function(data){
		supplierLinkTableReload(data.field);
		return false;
	});

	$("#addBtn").on('click',function () {
	    pop_show('添加','#(ctxPath)/wms/supplier/supplierLinkForm','','');
	});
});
</script>
#end