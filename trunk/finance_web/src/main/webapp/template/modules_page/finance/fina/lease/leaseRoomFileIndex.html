#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
<style>
    .layui-tab  td, .layui-tab th {
        padding: 9px 5px;
    }
</style>
#end

#define content()
<div class="my-btn-box" style="padding: 20px 20px">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">

            <div class="layui-inline" style="float: right;">
                <button type="button" class="layui-btn" id="uploadBtn"><i class="layui-icon"></i>上传文件</button>
            </div>
            <input id="userId" value="#(userId??)" type="hidden">
            <input id="id" value="#(id)" type="hidden">
        </div>
    </form>
    <div class="layui-row">
        <table id="fileTable" lay-filter="fileTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
        {{#
        let fileType=d.file_url.substr(d.file_url.lastIndexOf('.')+1);
        console.log(fileType);
        }}
        #[[
        {{#if(fileType=='jpg' || fileType=='png' || fileType=='pdf'){}}
            <a class="layui-btn layui-btn-xs" lay-event="preview">预览</a>
        {{#}}}
        ]]#
        <a class="layui-btn layui-btn-xs" lay-event="download">下载</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate','upload'],function(){
        var table = layui.table
            , $ = layui.$
            ,upload=layui.upload
        ;
        var laydate=layui.laydate;
        laydate.render({
            elem: '#roomStartDate'
            ,trigger:'click'
            ,range: true
        });
        laydate.render({
            elem: '#roomEndDate'
            ,trigger:'click'
            ,range: true
        });


        cardTypeTableReload=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'fileTable',
                elem:"#fileTable",
                url:'#(ctxPath)/fina/leaseRecordApply/leaseRoomFileList',
                where:data,
                cols:[[
                    {type:'numbers',title:'序号',unresize:true}
                    ,{field: 'file_name', title: '文件名称',unresize:true}
                    ,{title:'操作',width:200, fixed:'right',unresize:true,toolbar:'#toolBar'}
                ]],
                page:false,
                limit:10
                , done: function (res, curr, count) {

                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.close(loadingIndex);
                }
            });
            table.on('tool(fileTable)',function(obj){
                if(obj.event==='download'){
                    window.location.href='#(commonUpload)/downloadFile?src='+obj.data.file_url;
                    return false;
                }else if(obj.event==='del'){
                    layer.confirm("确定要作废吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/fina/leaseRecordApply/delRoomFile',
                            data: {'id':obj.data.id,"userId":$("#userId").val()},
                            notice:true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    cardTypeTableReload({'id':$('#id').val()})
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });
                    return false;
                }else if(obj.event=='preview'){
                    parent.layer.open({
                        type: 2,
                        title:'预览',
                        area: ['800px', '500px'],
                        fixed: false, //不固定
                        maxmin: true,
                        content: obj.data.file_url
                    });
                }
            });
        }

        upload.render({
            elem: '#uploadBtn'
            ,url: '#(commonUpload)/upload?bucket=finaLeaseFile' //此处配置你自己的上传接口即可
            ,accept: 'file' //普通文件
            ,exts:'pdf|jpg|png'
            ,size:1024*10//大小限制10M
            ,before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                layer.load(); //上传loading
            }
            ,done: function(res){
                if(res.state=='ok'){

                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/leaseRecordApply/saveRoomFile',
                        data: {'fileName':res.data.oldName,"fileUrl":res.data.src,"leaseRoomId":$("#id").val()},
                        notice:false,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                layer.msg('上传成功',{icon:1,time:3000});
                                cardTypeTableReload({'id':$('#id').val()})
                            }else {
                                layer.msg('上传失败',{icon:5,time:3000});
                            }
                        },
                        complete : function() {
                        }
                    });

                }else{
                    layer.msg('上传失败',{icon:5,time:3000});
                }
            }
            ,error: function(index, upload){
                layer.closeAll('loading'); //关闭loading
            }
        });

        cardTypeTableReload({'id':$("#id").val()});

        // 搜索消费记录按钮
        $('#searchConsumeBtn').on('click', function(){
            cardTypeTableReload({'id':$("#id").val()});
        });

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#searchConsumeBtn').click();
                return false
            }
        }


    });
</script>
#end