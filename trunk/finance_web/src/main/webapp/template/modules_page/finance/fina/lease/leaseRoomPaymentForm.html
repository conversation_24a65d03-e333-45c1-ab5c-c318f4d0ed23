#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()表单#end

#define css()
<style>
    .layui-form-label {
        width: 110px !important;
        text-align: center !important;
    }
    .layui-input-block {
        margin-left: 110px !important;
    }
    .layui-select-disabled .layui-disabled {
        color: black!important;
        border-color: #eee!important;
    }
    input[type="text"]:disabled {
        background-color: #fff;
    }
    #roomTable .layui-table td, #roomTable .layui-table th {
        position: relative;
        padding: 9px 5px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }
    input[type='text'] {
        width: 100% !important;
    }
</style>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','laytpl','laydate'],function(){
        var form = layui.form ;
        var $ = layui.$ ;
        var laytpl = layui.laytpl;
        var laydate=layui.laydate;


// 		form.render('radio');

        //校验
        form.verify({
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                    if(value.length != 7 && value.length != 8){
                        return "号段固定为7位或8位";
                    }
                }
            },
            checkPrefix:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                    if(value.length != 2){
                        return "卡号前缀固定为2位";
                    }
                }
            }
        });


        form.on('select(deptId)',function (d) {
            let dataStr=$("#deptId option[value='"+d.value+"']").attr("data");
            console.log(dataStr);
            let positionList=JSON.parse(dataStr);
            let positionStr="<option value=''>请选择</option>";
            $.each(positionList,function (index,item) {
                if(positionList.length==1){
                    positionStr+="<option value='"+item.id+"' selected>"+item.positionName+"</option>";
                }else{
                    positionStr+="<option value='"+item.id+"'>"+item.positionName+"</option>";
                }
            });
            console.log(positionStr);
            $("#positionId").html(positionStr);
        });





        $("#proprietorBtn").on('click',function () {
            layerShow('选择业主','#(ctxPath)/fina/leaseRecordApply/proprietorIndex',900,600);
        });

        selectedProprietor=function(data){
            $("#proprietorName").val(data.name);
            $("#supplierId").val(data.id);
            $.post('#(ctxPath)/fina/leaseRecordApply/suppliersPayList',{'supplierId':data.id},function (res) {
                if(res.length==0){
                    layer.msg('该供应商的付款方式为空，请先维护', {icon:5,time: 2000});
                    return false;
                }
                let str="<option value=''>请选择</option>"
                $.each(res,function (index,item) {
                    str+="<option value='"+item.id+"'>"+item.paymentWayStr+"("+item.pay_account+")"+"</option>"
                })
                $("#suppliersPayId").html(str);
                form.render('select');
            });
        }

        $("#addRoom").on('click',function () {
            addRoomTpl();
        });

        addRoomTpl = function () {
            let roomCount=Number($('#roomCount').val())+1;
            $('#roomCount').val(roomCount);
            laytpl(roomTrTpl.innerHTML).render({
                'idx': roomCount
            }, function (html) {
                $("#roomTbody").append(html);
            });

            //时间渲染
            laydate.render({
                elem: '#contractDate' + roomCount
                ,trigger:'click'
                ,range: true
            });
            //渲染

            laydate.render({
                elem: '#paymentDate' + roomCount
                ,trigger:'click'
                ,
            });

            form.render('select');
        };

        delTr=function (trIndex,id) {
            if(id==''){
                $('#' + trIndex).remove();
            }else{
                layer.confirm('是否要作废?', function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/leaseRecordApply/delLeaseRoom',
                        data: {'id':id,'userId':$("#userId").val()},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                $('#' + trIndex).remove();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
            return false;
        }

        selectedRoom=function (trIndex) {
            if($("#baseId").val()==''){
                layer.msg('请先选择基地',{icon:5,time:5000});

                return false;
            }
            layerShow('选择房间','#(ctxPath)/fina/leaseRecordApply/roomTableIndex?baseId='+$("#baseId").val()+"&trIndex="+trIndex,900,600);
        }

        selectedRoomData=function (data,trIndex) {
            $("#roomName"+trIndex).val(data.room_name);
            $("#roomId"+trIndex).val(data.id);
        }


    }) ;
</script>

#end

#define content()
<div class="my-btn-box">
    <form class="layui-form layui-form-pane" action="" id="frm">
        <div class="layui-row">

            <div class="layui-col-md12" style="padding-left: 10px;">


                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>基地</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.baseName??)" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>房间号</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.roomName??)" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>业主</label>
                    <div class="layui-input-block"  >
                        <input type="text" id="proprietorName" name="proprietorName" autocomplete="off"  readonly value="#(model.supplierName??)" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>业主收款方式</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.paymentWayStr??)（#(model.bankType??)：#(model.payAccount??)）" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>合同期限</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#date(model.roomStartDate??,'yyyy-MM-dd') 至 #date(model.roomEndDate??,'yyyy-MM-dd')" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>租金付款期限</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#date(model.contractStartDate??,'yyyy-MM-dd') 至 #date(model.contractEndDate??,'yyyy-MM-dd')" class="layui-input" >
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>合同租金</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.rent??)/月" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>租金涨幅</label>
                    <div class="layui-input-block">
                        #if(model.rentAddYear>0 && model.rentAddRatio>0)
                        <input type="text" name="proprietorName" autocomplete="off"  readonly    class="layui-input"  value="每#(model.rentAddYear)年递增#(model.rentAddRatio)%"    >
                        #else
                        <input type="text" name="proprietorName" autocomplete="off"  readonly    class="layui-input"  value=""   >
                        #end
                    </div>
                </div>

                #if(model.rentAddYear>0 && model.rentAddRatio>0)
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>租金涨幅明细</label>

                    <div class="layui-input-block">
                        <div>
                            <table class="layui-table">
                                <colgroup>
                                    <!--<col width="150">
                                    <col width="200">
                                    <col>-->
                                </colgroup>
                                <thead>
                                <tr>
                                    <th>开始时间</th>
                                    <th>结束时间</th>
                                    <th>租金/月</th>
                                </tr>
                                </thead>
                                <tbody>
                                    #for(rentAdd : model.rentAddList??)
                                    <tr>
                                        <td>#date(rentAdd.startDate,'yyyy-MM-dd')</td>
                                        <td>#date(rentAdd.endDate,'yyyy-MM-dd')</td>
                                        <td>#(rentAdd.rent)</td>
                                    </tr>
                                    #end
                                </tbody>
                            </table>

                        </div>

                    </div>


                </div>
                #end

                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>租金支付方式</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.rentUnit??)" class="layui-input" >
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>押金</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.earnestMoney??)" class="layui-input" >
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>账单编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.orderNo??)" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>账单期数</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.issueNo??)/#(model.totalCount??)" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>账单金额</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#(model.amount??)" class="layui-input" >
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>账单日期</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#date(model.startDate??,'yyyy-MM-dd')至#date(model.endDate??,'yyyy-MM-dd')" class="layui-input" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>期望付款日期</label>
                    <div class="layui-input-block">
                        <input type="text" name="proprietorName" autocomplete="off"  readonly value="#date(model.needPayDate??,'yyyy-MM-dd')" class="layui-input" >
                    </div>
                </div>

                <!--<div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注内容" class="layui-textarea">#(leaseRecordApply.remark??)</textarea>
                    </div>
                </div>-->


            </div>
        </div>

    </form>
</div>
#end