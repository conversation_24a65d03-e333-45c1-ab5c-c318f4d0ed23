#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()添加/编辑会员卡页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<link rel="stylesheet" href="#(ctxPath)/static/css/jquery.magnify.min.css">
<style>
    .layui-form-label {
        width: 130px !important;
        text-align: center !important;
    }

    .layui-input-block {
        margin-left: 130px !important;
    }
</style>
<style>
    div.layui-form-item {
        padding-left: 20px;
    }

    .co {
        color: #ff0000;
    }

    #if(type=='check')
    .layui-disabled, .layui-disabled:hover {
        color: #000000 !important;
        cursor: not-allowed !important;
    }
    .layui-input {
        color: #000;
    }
    #end
</style>
#end

#define content()
<form class="layui-form layui-form-pane" id="cardInfoForm" action="" method="post">
    <input type="hidden" name="card.id" value="#(card.id??)" id="idh"/>
    <input type="hidden" name="card.dataSource" value="#(card.dataSource??'finance')"/>
    <input type="hidden" name="card.memberId" value="#(card.memberId??)"/>
    <input type="hidden" name="card.expireFlag" value="#(card.expireFlag??'0')"/>
    <!--是否赠送一次性费用，0不推送，1推送-->
    <input type="hidden" name="isGive" value="0" id="isGive"/>
    <input type="hidden" id="fileCount" name="fileCount" value="0">
    <input type="hidden" id="collectCount" name="collectCount" value="#(collectList.size()??0)">
    <input type="hidden" id="notFitBase" name="notFitBase" value="#(notFitBases??)">
    <fieldset class="layui-elem-field" style="margin-top:30px;">
        <legend style="font-size: 14px;margin-bottom:30px;"><b>会员卡信息</b></legend>
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="co">*</span>卡类别</label>
            <div class="layui-input-inline" style="width:60%">
<!--                 #if(type=='check'||com.jfinal.kit.StrKit::notBlank(card.id??)) -->
<!--                 <input type="text" class="layui-input" value="#(cardType.cardType??)" readonly="readonly"> -->
<!--                 #else -->
<!--                 #end -->
                <select id="cardTypeId" name="card.cardTypeId" lay-filter="cardTypeFilter" lay-verify="required" lay-search #if(modFlag??) disabled #end>
                    <option value="">请选择卡类别</option>
                    #for(t : typeList)
                    <option value="#(t.id)" isIntegral="#(t.isIntegral)" typeClassify="#(t.typeClassify)" #(card !=null ? (t.id== card.card_type_id?'selected':'') :'')>#(t.cardType)</option>
                    #end
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">开卡时间</label>
            <div class="layui-input-inline">
                <input type="text" id="openTime" name="card.openTime" class="layui-input" autocomplete="off"
                       value="#date(card.openTime??,'yyyy-MM-dd')" placeholder="请输入开卡时间">
            </div>
            <label class="layui-form-label"><span class="co">*</span>会员卡号</label>
            <div class="layui-input-inline">
                <input type="text" id="cardNumber" name="card.cardNumber" class="layui-input" lay-verify="required" 
                	value="#(card.cardNumber??)" placeholder="请输入会员卡号" #if(modFlag??) readonly #end>
            </div>
            <!-- 		<input type="button" class="layui-btn" value="生成卡号" id="ran" #if(card.id??!=null) style="display: none" #end/> -->
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">合同类型</label>
            <div class="layui-input-inline">
                <select id="contractType" name="card.contractType" lay-search>
                    <option value="">请选择合同类型</option>
                    #for(contractType : contractTypeList)
                    <option value="#(contractType.id)" #if(contractType.id==card.contractType??'')selected#end>#(contractType.typeName)</option>
                    #end
                </select>
            </div>
            <label class="layui-form-label">合同编号</label>
            <div class="layui-input-inline">
                <input type="text" id="contractNumber" name="card.contractNumber" class="layui-input" value="#(card.contractNumber??)" placeholder="请输入合同编号">
            </div>
        </div>
        <div class="layui-form-item">
        	<label class="layui-form-label">主卡</label>
        	<div class="layui-input-inline">
        		<select id="cardParentId" name="card.parentId" lay-filter="mainCardSelect" lay-search>
                    <option value="">请输入主卡号选择</option>
                    #for(c : cardList)
                    <option value="#(c.id)" #if(c.id==card.parentId??'')selected#end>#(c.cardNumber)</option>
                    #end
                </select>
        	</div>
            <label class="layui-form-label"><font color="red"></font>销售人员</label>
            <div class="layui-input-inline">
                <input type="hidden" id="operator" name="card.operator" value="#(card.operator??)"/>
                <input type="text" id="operatorName" lay-verify="" class="layui-input" value="#(user.name??)" placeholder="请选择销售人员" readonly>
            </div>
            <input type="button" id="chooseUser" class="layui-btn" value="选择"/>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分公司</label>
            <div class="layui-input-inline">
                <select id="branchOfficeId" name="card.branchOfficeId" lay-search>
                    <option value="">请选择分公司</option>
                    #for(branchOffice : branchOfficeList)
                    <option value="#(branchOffice.id)" #if(branchOffice.id==card.branchOfficeId??'')selected#end>#(branchOffice.shortName??)</option>
                    #end
                </select>
            </div>
            <label class="layui-form-label">销售部门</label>
            <div class="layui-input-inline">
                <input type="hidden" id="salesDeptIdValue" name="card.salesDeptId" value="">
                <div id="salesDeptId" class="xm-select-demo" hiddenTargetId="salesDeptIdValue" dataValue="#(card.salesDeptId??)"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="co">*</span>合同金额</label>
            <div class="layui-input-inline">
                <input type="text" id="cardPurchaseCardBalance" name="card.purchaseCardBalance" class="layui-input"
                       value="#(card.purchaseCardBalance??)" placeholder="请输入合同金额" lay-verify="required|checkAmount">
            </div>
            <label class="layui-form-label"><span class="co">*</span>合同天数</label>
            <div class="layui-input-inline">
                <input type="text" id="contractTimes" name="card.contractTimes" class="layui-input"
                       value="#(card.contractTimes??)" lay-verify="required|checkAmount" placeholder="请输入合同天数" maxlength="6">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="co">*</span>实收金额</label>
            <div class="layui-input-inline">
                <input type="text" id="collectAmount" name="card.collectAmount" class="layui-input"
                       value="#(card.collectAmount??0)" lay-verify="required|checkAmount" placeholder="请输入实收金额">
            </div>

        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">分期赠送方案</label>
            <div class="layui-input-inline" style="width:50%">
                <select id="giveSchemeId" name="card.giveSchemeId" lay-search>
                    <option value="">请选择赠送方案</option>
                    #for(giveScheme : giveSchemeList)
                    <option value="#(giveScheme.id)" #if(giveScheme.id==card.giveSchemeId??'')selected#end>#(giveScheme.name??)</option>
                    #end
                </select>
            </div>
            <input type="button" id="previewGiveScheme" class="layui-btn" value="预览"/>


        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">条件赠送方案</label>
            <div class="layui-input-inline" style="width:50%">
                <select id="conditionRechargeSchemeId" name="card.conditionRechargeSchemeId" lay-search>
                    <option value="">请选择赠送方案</option>
                    #for(giveScheme : conditionRechargeSchemeList)
                    <option value="#(giveScheme.id)" #if(giveScheme.id==card.conditionRechargeSchemeId??'')selected#end>#(giveScheme.name??)</option>
                    #end
                </select>
            </div>


        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">分期充值方案</label>
            <div class="layui-input-inline" style="width:50%">
                <select id="rechargeSchemeId" name="card.rechargeSchemeId" lay-search>
                    <option value="">请选择充值方案</option>
                    #for(giveScheme : rechargeSchemeList)
                    <option value="#(giveScheme.id)" #if(giveScheme.id==card.rechargeSchemeId??'')selected#end>#(giveScheme.name??)</option>
                    #end
                </select>
            </div>
            <input type="button" id="previewGiveScheme2" class="layui-btn" value="预览"/>


        </div>

        <fieldset class="layui-elem-field" style="margin-top:30px;">
            <legend style="font-size: 14px;margin-bottom:30px;"><b>合同赠送</b></legend>
            <div class="layui-form-item">
                <label class="layui-form-label">是否分期赠送</label>
                <div class="layui-input-inline">
                    <input type="radio" name="card.isSubsectionGive" value="0" title="否" #if(card.isSubsectionGive??''==''||card.isSubsectionGive??=='0')checked#end>
                    <input type="radio" name="card.isSubsectionGive" value="1" title="是" #if(card.isSubsectionGive??=='1')checked#end>
                </div>

<!--                <label class="layui-form-label">合同赠送天数</label>-->
<!--                <div class="layui-input-inline">-->
<!--                    <input type="text" id="giveConsumeTimes" name="card.giveConsumeTimes" class="layui-input"-->
<!--                           value="#(card.giveConsumeTimes??0)" autocomplete="off" placeholder="请输入合同赠送天数" maxlength="6">-->
<!--                </div>-->
                <label class="layui-form-label">合同赠送天数</label>
                <div class="layui-input-inline">
                    <input type="text" id="giveRechargeDays" name="#if(card==null)card.giveRechargeDays#end"
                           class="layui-input" value="#(card.giveRechargeDays??0)" placeholder="请输入合同赠送天数">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">分期赠送总天数</label>
                <div class="layui-input-inline">
                    <input type="text" id="subsectionTotalGiveTimes" name="card.subsectionTotalGiveTimes" class="layui-input"
                           value="#(card.subsectionTotalGiveTimes??0)" autocomplete="off" placeholder="请输入分期赠送总天数" maxlength="6">
                </div>

                <label class="layui-form-label">分期已赠送天数</label>
                <div class="layui-input-inline">
                    <input type="text" id="subsectionGiveTimes" name="card.subsectionGiveTimes" class="layui-input"
                           value="#(card.subsectionGiveTimes??0)" autocomplete="off" placeholder="请输入分期已赠送天数" maxlength="6">
                </div>

            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">分期充值总天数</label>
                <div class="layui-input-inline">
                    <input type="text" id="subsectionTotalRechargeTimes" name="card.subsectionTotalRechargeTimes" class="layui-input"
                           value="#(card.subsectionTotalRechargeTimes??0)" autocomplete="off" placeholder="请输入分期充值总天数" maxlength="6">
                </div>

            </div>

        </fieldset>

        <div class="layui-form-item">
            <label class="layui-form-label"><span class="co">*</span>折扣率(%)</label>
            <div class="layui-input-inline">
                <input type="text" id="contractDiscount" name="card.contractDiscount" class="layui-input"
                       value="#(card.contractDiscount??0)" lay-verify="required" placeholder="请输入折扣率(%)">
            </div>
            <label class="layui-form-label">总计天数</label>
            <div class="layui-input-inline">
                <input type="text" id="totalDays" class="layui-input"
                       value="#(card.contractTimes??0+card.giveConsumeTimes??0)" placeholder="请输入总计天数" maxlength="6">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">计划售价</label>
            <div class="layui-input-inline">
                <input type="text" id="price" name="card.price" class="layui-input" value="#(card.price??)"
                       placeholder="请输入计划售价" maxlength="6">
            </div>
            <label class="layui-form-label">实际售价</label>
            <div class="layui-input-inline">
                <input type="text" id="referencePrice" name="card.referencePrice" class="layui-input"
                       value="#(card.referencePrice??)" placeholder="请输入实际售价" maxlength="6">
            </div>
        </div>
        #if(card!=null)
        <div class="layui-form-item">
            <label class="layui-form-label">剩余金额</label>
            <div class="layui-input-inline">
                <input type="text" id="cardBalance"  name="#if(card==null)card.balance#end"  class="layui-input"
                       value="#(card.balance??0)" placeholder="请输入剩余金额" lay-verify="checkAmount" #if(card.id??!=null)
                       readonly #end>
            </div>
            <label class="layui-form-label">剩余天数</label>
            <div class="layui-input-inline">
                <input type="text" id="cardConsumeTimes" name="#if(card==null)card.consumeTimes#end"  class="layui-input"
                       value="#(card.consumeTimes??)" placeholder="请输入剩余天数" lay-verify="checkAmount"
                       #if(card.id??!=null) readonly #end>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">剩余点数</label>
            <div class="layui-input-inline">
                <input type="text" id="cardConsumePoints" name="#if(card==null)card.consumePoints#end"
                       class="layui-input" value="#(card.consumePoints??0)" placeholder="请输入剩余点数"
                       lay-verify="checkAmount" #if(card.id??!=null) readonly #end>
            </div>
            <label class="layui-form-label">剩余积分</label>
            <div class="layui-input-inline">
                <input type="text" id="cardIntegrals" name="#if(card==null)card.cardIntegrals#end" class="layui-input"
                       value="#(card.cardIntegrals??0)" placeholder="请输入剩余积分" #if(card.id??!=null) readonly #end>
            </div>
        </div>
        #end
        <div class="layui-form-item">
<!--            <label class="layui-form-label">剩余豆豆券</label>-->
<!--            <div class="layui-input-inline">-->
<!--                <input type="text" id="beanCoupons" name="#if(card==null)card.beanCoupons#end" class="layui-input"-->
<!--                       value="#(card.beanCoupons??0)" placeholder="请输入剩余豆豆券" #if(card.id??!=null) readonly #end>-->
<!--            </div>-->
            <div id="cardMinIntegralsDiv" #if(cardType.isIntegral??=='1') style="display: block;"#else style="display: none;" #end>
                <label class="layui-form-label">允许最小积分</label>
                <div class="layui-input-inline">
                    <input type="text" id="cardMinIntegrals" name="card.cardMinIntegrals" class="layui-input"
                           value="#(card.cardMinIntegrals??-3)" placeholder="请输入允许最小积分">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">购买金额</label>
            <div class="layui-input-inline">
                <input type="text" id="buyRechargeAmount" name="#if(card==null)card.buyRechargeAmount#end"
                       class="layui-input" value="#(card.buyRechargeAmount??0)" placeholder="请输入购买金额">
            </div>
            <label class="layui-form-label">购买天数</label>
            <div class="layui-input-inline">
                <input type="text" id="buyRechargeDays" name="#if(card==null)card.buyRechargeDays#end"
                       class="layui-input" value="#(card.buyRechargeDays??0)" placeholder="请输入购买天数">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">赠送金额</label>
            <div class="layui-input-inline">
                <input type="text" id="giveRechargeAmount" name="#if(card==null)card.giveRechargeAmount#end"
                       class="layui-input" value="#(card.giveRechargeAmount??0)" placeholder="请输入赠送金额">
            </div>
            <label class="layui-form-label">赠送积分</label>
            <div class="layui-input-inline">
                <input type="text" id="giveRechargeIntegrals" name="#if(card==null)card.giveRechargeIntegrals#end"
                       class="layui-input" value="#(card.giveRechargeIntegrals??0)" placeholder="请输入赠送积分">
            </div>
        </div>
<!--         <div id="cardMinIntegralsDiv" class="layui-form-item" #if(cardType.isIntegral??=='1') style="display: block;" -->
<!--              #else style="display: none;" #end> -->
<!--             <label class="layui-form-label">允许最小积分</label> -->
<!--             <div class="layui-input-inline"> -->
<!--                 <input type="text" id="cardMinIntegrals" name="card.cardMinIntegrals" class="layui-input" -->
<!--                        value="#(card.cardMinIntegrals??-3)" placeholder="请输入允许最小积分"> -->
<!--             </div> -->
<!--         </div> -->
        <div class="layui-form-item">
            <label class="layui-form-label">旅居扣费方案</label>
            <div class="layui-input-inline">
                <select id="deductSchemeId" name="card.deductSchemeId" lay-search>
                    <option value="">请选择旅居扣费方案</option>
                    #for(deduct : sojournSchemeList)
                    <option value="#(deduct.id)" #if(deduct.id==card.deductSchemeId??'')selected#end>#(deduct.name)</option>
                    #end
                </select>
            </div>
            <label class="layui-form-label">长住扣费方案</label>
            <div class="layui-input-inline">
                <select id="longDeductSchemeId" name="card.longDeductSchemeId" lay-search>
                    <option value="">请选择长住扣费方案</option>
                    #for(deduct : longSchemeList)
                    <option value="#(deduct.id)" #if(deduct.id==card.longDeductSchemeId??'')selected#end>#(deduct.name)</option>
                    #end
                </select>
            </div>
            <!-- 			<button class="layui-btn layui-btn-sm" type="button" id="giveSchemeDetail">赠送详情</button> -->
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">年限类型</label>
            <div class="layui-input-inline">
                <select id="cardYearLimit" name="card.yearLimit">
                    <option value="">请选择年限类型</option>
                    #for(cardYearLimit : cardYearLimitList)
                    <option value="#(cardYearLimit.dictValue)" #if(card.yearLimit??==cardYearLimit.dictValue)selected#end>#(cardYearLimit.name)</option>
                    #end
                </select>
            </div>
            <label class="layui-form-label"><span class="co">*</span>使用年限</label>
            <div class="layui-input-inline">
                <input type="text" id="useYears" name="card.useYears" class="layui-input" value="#(card.useYears??)"
                       placeholder="请输入使用年限" maxlength="3" lay-verify="number">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 9px 6px;">最大有效期</label>
            <div class="layui-input-inline">
                <input type="text" name="card.expireDate" id="expireDate" class="layui-input"
                       value="#date(card.expireDate??,'yyyy-MM-dd')" lay-verify="required" readonly="readonly" placeholder="请输入最大有效期">
            </div>
            <label class="layui-form-label">入账公司</label>
            <div class="layui-input-inline">
                <select name="card.entryCompany">
                    <option value="">请选择入账公司</option>
                    #getDictList("entryCompany")
                    <option value="#(key)" #(card !=null ?(card.entryCompany??==key?'selected':''):'')>#(value)</option>
                    #end
                </select>
            </div>
        </div>
        <!-- 		<div class="layui-form-item"> -->
        <!-- 			<label class="layui-form-label">是否收现金</label> -->
        <!-- 			<div class="layui-input-inline"> -->
        <!-- 				<input type="radio" name="card.isCash" value="0" title="否" #if(card.isCash??''==''||card.isCash??=='0')checked#end> -->
        <!-- 				<input type="radio" name="card.isCash" value="1" title="是" #if(card.isCash??=='1')checked#end> -->
        <!-- 			</div> -->
        <!-- 			<label class="layui-form-label">现金金额</label> -->
        <!-- 			<div class="layui-input-inline"> -->
        <!-- 				<input type="text" id="cashMoney" name="card.cashMoney" class="layui-input" value="#(card.cashMoney??)" placeholder="请输入现金金额" maxlength="7"> -->
        <!-- 			</div> -->
        <!-- 		</div> -->
        <!-- 		<div class="layui-form-item"> -->
        <!-- 			<label class="layui-form-label">收钱账户</label> -->
        <!-- 			<div class="layui-input-inline"> -->
        <!-- 				<select id="accountId" name="card.accountId"> -->
        <!-- 					<option value="">请选择收钱账户</option> -->
        <!-- 					#for(account : accountList) -->
        <!-- 						#if(account.payWay??=="1") -->
        <!-- 							#set(payWay = "(现金)") -->
        <!-- 						#else if(account.payWay??=="2") -->
        <!-- 							#set(payWay = "(微信)") -->
        <!-- 						#else if(account.payWay??=="3") -->
        <!-- 							#set(payWay = "(支付宝)") -->
        <!-- 						#else if(account.payWay??=="4") -->
        <!-- 							#set(payWay = "(信用卡)") -->
        <!-- 						#else if(account.payWay??=="5") -->
        <!-- 							#set(payWay = "(会员卡)") -->
        <!-- 						#else if(account.payWay??=="6") -->
        <!-- 							#set(payWay = "(Pos机)") -->
        <!-- 						#else if(account.payWay??=="7") -->
        <!-- 							#set(payWay = "(转账)") -->
        <!-- 						#else if(account.payWay??=="8") -->
        <!-- 							#set(payWay = "(企业微信)") -->
        <!-- 						#end -->
        <!-- 						<option value="#(account.id)" #if(account.id??==card.accountId??)selected="selected"#end>#(payWay)#(account.accountName)</option> -->
        <!-- 					#end -->
        <!-- 				</select> -->
        <!-- 			</div> -->
        <!-- 			<label class="layui-form-label">转账金额</label> -->
        <!-- 			<div class="layui-input-inline"> -->
        <!-- 				<input type="text" id="accountMoney" name="card.accountMoney" class="layui-input" value="#(card.accountMoney??)" placeholder="请输入转账金额" maxlength="7"> -->
        <!-- 			</div> -->
        <!-- 		</div> -->
        <div class="layui-form-item">
            <label class="layui-form-label">是否早餐</label>
            <div class="layui-input-inline">
                <input type="radio" id="isBreakfastNo" name="card.isBreakfast" value="0" title="否" #if(card.isBreakfast??=='0')checked#end>
                <input type="radio" id="isBreakfastYes" name="card.isBreakfast" value="1" title="是" #if(card.isBreakfast??''==''||card.isBreakfast??=='1')checked#end>
            </div>
            <label class="layui-form-label">是否午餐</label>
            <div class="layui-input-inline">
                <input type="radio" id="isLunchNo" name="card.isLunch" value="0" title="否" #if(card.isLunch??=='0')checked#end>
                <input type="radio" id="isLunchYes" name="card.isLunch" value="1" title="是" #if(card.isLunch??''==''||card.isLunch??=='1')checked#end>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否晚餐</label>
            <div class="layui-input-inline">
                <input type="radio" id="isDinnerNo" name="card.isDinner" value="0" title="否" #if(card.isDinner??=='0')checked#end>
                <input type="radio" id="isDinnerYes" name="card.isDinner" value="1" title="是" #if(card.isDinner??''==''||card.isDinner??=='1')checked#end>
            </div>
            <label class="layui-form-label">是否可预订</label>
            <div class="layui-input-inline">
                <input type="radio" id="bookingNo" name="card.isBooking" value="0" title="否" #if(card.isBooking??=='0')checked#end>
                <input type="radio" id="bookingYes" name="card.isBooking" value="1" title="是" #if(card.isBooking??''==''||card.isBooking??=='1')checked#end>
            </div>
        </div>
		<div class="layui-form-item">
            <label class="layui-form-label">档案号</label>
            <div class="layui-input-inline">
                <input type="text" id="archiveNo" name="card.archiveNo" class="layui-input" value="#(card.archiveNo??)" placeholder="请输入档案号">
            </div>
            <label class="layui-form-label">存放位置</label>
            <div class="layui-input-inline">
                <input type="text" id="depositPlace" name="card.depositPlace" class="layui-input" value="#(card.depositPlace??)" placeholder="请输入存放位置">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">是否报备</label>
            <div class="layui-input-inline">
                <input type="radio" id="isReportNo" name="card.isReport" value="0" title="否" #if(card.isReport??''==''||card.isReport??=='0')checked#end>
                <input type="radio" id="isReportYes" name="card.isReport" value="1" title="是" #if(card.isReport??=='1')checked#end>
            </div>
            <label class="layui-form-label">不发验证码</label>
            <div class="layui-input-inline">
                <input type="radio" id="isNotSendSms" name="card.isNotSendSms" value="0" title="否" #if(card.isNotSendSms??''==''||card.isNotSendSms??=='0')checked#end>
                <input type="radio" id="isNotSendSms" name="card.isNotSendSms" value="1" title="是" #if(card.isNotSendSms??=='1')checked#end>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">不适用基地</label>
            <div class="layui-input-block">
                <div id="notFitBaseXmSelectTree" class="xm-select-demo" hiddenTargetId="notFitBase" dataValue="#(notFitBases??)"></div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">卡说明</label>
            <div class="layui-input-block" style="width: 68%;">
                <textarea id="cardDescribe" name="card.describe" class="layui-textarea" rows="5" placeholder="请输入卡说明">#(card.describe??)</textarea>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">收款信息</label>
            <div class="layui-input-block">
                <table class="layui-table">
                    <thead>
                    <tr>
                        <th>收款编号</th>
                        <th>收款日期</th>
                        <th>收款方式</th>
                        <th>收款账号</th>
                        <th>收款金额</th>
                        <th>收据编号</th>
                        <th>收据照片</th>
                        #if(type??!='check')
                        <th>操作</th>
                        #end
                    </tr>
                    </thead>
                    <tbody id="cardCollectList"></tbody>
                </table>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">附件信息</label>
            <div class="layui-input-block">
                <table class="layui-table">
                    <colgroup>
                        <col width="40%">
                        <col width="40%">
                        <col width="20%">
                        <col>
                    </colgroup>
                    <thead>
                    <tr>
                        <th>文件名</th>
                        <th>上传时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody id="upFileList"></tbody>
                </table>
            </div>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field" style="margin-top:30px;">
        <legend style="font-size: 14px;margin-bottom:30px;"><b>会员卡个性化</b></legend>
        <div class="layui-form-item">
            <label class="layui-form-label">入住人限制</label>
            <div class="layui-input-inline">
                <select name="card.checkinType" lay-filter="checkinType">
                    <option value="1" #if(card.checkinType??=='1') selected #end>不限制</option>
                    <option value="2" #if(card.checkinType??=='2') selected #end>仅本人使用</option>
                    <option value="3" #if(card.checkinType??=='3') selected #end>配置使用人</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" id="checkinConfigDiv" #if(card.checkinType??=='3') style="margin-bottom: 30px;"
             #else style="margin-bottom: 30px;display: none;" #end>
            <div class="layui-row">
                <label class="layui-form-label">配置使用人</label>
                <button class="layui-btn" type="button" id="addCheckinConfig">添加</button>
            </div>
            <div class="layui-row">
                <input id="count" name="count" type="hidden" value="#(configList.size()??0)">
                <table class="layui-table">
                    <thead>
                    <tr>
                        <th>使用人姓名</th>
                        <th>使用人身份证号码</th>
                        <th width="10%">操作</th>
                    </tr>
                    </thead>
                    <tbody id="rechargeTbody">
                    #for(config : configList)
                    <tr id="tr-#(for.index+1)" tr-index="#(for.index+1)">
                        <td>
                            <input type="hidden" name="checkinConfig-[#(for.index+1)].id" value="#(config.id)">
                            <input type="text" name="checkinConfig-[#(for.index+1)].name" value="#(config.name)"
                                   required lay-verify="required" placeholder="请输入入住人姓名" autocomplete="off"
                                   class="layui-input">
                        </td>
                        <td>
                            <input type="text" name="checkinConfig-[#(for.index+1)].idcard" value="#(config.idcard)"
                                   lay-verify="required" placeholder="请输入入住人身份证号码" autocomplete="off"
                                   class="layui-input">
                        </td>
                        <td>
                            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button"
                                    onclick="del('#(for.index+1)','#(config.id)')">删除
                            </button>
                        </td>
                    </tr>
                    #end
                    </tbody>
                </table>
            </div>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field" id="baseIdDiv"   #if(card.cardTypeId??=='2070AD12-A04C-487D-B9DF-B0668398F27C') style="margin-top:30px;display: block;" #else style="margin-top:30px;display: none;" #end>
        <legend style="font-size: 14px;margin-bottom:30px;"><b>基地专卡关联基地</b></legend>
        <div class="layui-form-item">
            <label class="layui-form-label">关联基地</label>
            <div class="layui-input-inline">
                <select name="card.baseId"  lay-filter="checkinType" id="baseId" lay-search  #if(card.cardTypeId??=='2070AD12-A04C-487D-B9DF-B0668398F27C') lay-verify="required" #end>
                    <option value="">请选择</option>
                    #for(base : baseList)
                    <option value="#(base.id)" #if(card.baseId??==base.id)selected #end>#(base.baseName)</option>
                    #end
                </select>
            </div>
        </div>
    </fieldset>
    <fieldset class="layui-elem-field" style="margin-top:30px;margin-bottom:60px;">
        <legend style="font-size: 14px;margin-bottom:30px;"><b>开卡人信息</b></legend>
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="co">*</span>姓氏</label>
            <div class="layui-input-inline">
                <input type="text" id="surname" name="member.surname" class="layui-input" lay-verify="required" value="#(member.surname??)" placeholder="请输入姓氏">
            </div>
            <input type="button" class="layui-btn" id="getMemberInfo" value="获取开卡人信息"/>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label"><span class="co">*</span>姓名</label>
            <div class="layui-input-inline">
                <input type="text" id="cardName" name="member.fullName" class="layui-input" lay-verify="required" #if(card.id??!=null) readonly #end value="#(member.fullName??)" placeholder="请输入姓名">
            </div>
            <label class="layui-form-label">性别</label>
            <div class="layui-input-inline">
                <select id="cardGender" name="member.gender">
                    <option value="">请选择性别</option>
                    #getDictList("gender")
                    <option value="#(key)" #(card !=null ?(member.gender??==key?'selected':''):'')>#(value)</option>
                    #end
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline" id="idcardDiv" style="display: none;">
                <label class="layui-form-label"><span class="co">*</span>证件类型</label>
                <div class="layui-input-inline">
                    <select id="idcardType" name="member.idcardType" lay-verify="required">
                        #getDictList("id_card_type")
                        <option value="#(key)" #(card !=null ?(member.idcardType??==key?'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>

                <label class="layui-form-label"><span class="co">*</span>身份证</label>
                <div class="layui-input-inline">
                    <input type="text" id="idcard" name="member.idcard" lay-verify="required" class="layui-input" value="#(member.idcard??)" placeholder="请输入身份证">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div id="telephoneDiv" class="layui-inline" style="display: none;">
                <label class="layui-form-label"><span class="co">*</span>手机号</label>
                <div id="memberLinkDiv" class="layui-input-inline">
                	#if(linkList.size()??0>0)
                	<select id="memberLinkId" name="memberLinkId" lay-verify="required" lay-filter="memberLinkSelect">
						<option value="" linkPhone="">请选择</option>
						#for(l : linkList??)
							<option value="#(l.id)" linkPhone="#(l.linkPhone)" #(card!=null ? (l.linkPhone== card.telephone?'selected':'') :'')>#(l.linkName)(#(l.linkPhone))</option>
		                #end
					</select>
					<input type="hidden" id="cardTelephone" name="card.telephone" value="#(card.telephone??)"/>
                	#else
                    <input type="text" id="cardTelephone" name="card.telephone" lay-verify="required|checkPhone" class="layui-input" value="#(card.telephone??)" placeholder="请输入手机号">
                	#end
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">出生日期</label>
                <div class="layui-input-inline">
                    <input type="text" id="birthday" name="member.birthday"
                           value="#(member != null ? member.birthday:'')" placeholder="请选择出生日期" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">居住区域</label>
            <div class="layui-input-block" style="width:69%;">
                <input type="hidden" id="province" name="member.province" value="#(member.province??)"/>
                <input type="hidden" id="city" name="member.city" value="#(member.city??)"/>
                <input type="hidden" id="town" name="member.town" value="#(member.town??)">
                <input type="hidden" id="street" name="member.street" value="#(member.street??)">
                <input type="hidden" id="regidentProvinceName" value="#(province??)"/>
                <input type="hidden" id="regidentCityName" value="#(city??)"/>
                <input type="hidden" id="regidentCountyName" value="#(town??)">
                <input type="hidden" id="regidentStreetName" value="#(street??)">
                <input type="text" id="regidentAddress" style="width: 80%;display: inline-block;" readonly="readonly"
                       name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
                <button class="layui-btn layui-btn-sm" style="display: inline-block;" type="button" id="areaClear">清空
                </button>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">详细地址</label>
            <div class="layui-input-block" style="width:69%;">
                <input type="text" id="address" name="member.address" value="#(member != null ?member.address:'')"
                       autocomplete="off" placeholder="请输入住址" class="layui-input">
            </div>
        </div>
    </fieldset>
    #if(cardType==null || cardType.typeClassify??=='welfare_card')
    <fieldset class="layui-elem-field" style="margin-bottom:60px;" id="staffDiv">
        <legend style="font-size: 14px;margin-bottom:30px;"><b>绑定员工</b></legend>
        <div class="layui-form-item">
            <label class="layui-form-label"><font color="red" id="staffLable">*</font>员工</label>
            <div class="layui-input-inline">
                <input type="text" id="staffName" name="staffName" class="layui-input" value="#if(emp!=null)#(emp.fullName??)(#(emp.workNum??))#end" placeholder=""#if(cardType==null || cardType.typeClassify??=='welfare_card') lay-verify="required"  #end  >
                <input type="hidden" id="empId" name="empId" value="#(emp.id??)" >
            </div>
            <button class="layui-btn" type="button"  id="choiceStaff">选择员工</button>
        </div>

    </fieldset>
    #end
    #if(type!='check')
    <div class="layui-form-item" style="height:30px;"></div>
    #end
    <div class="layui-form-footer">
        <!-- 		<div class="pull-left"> -->
        <!-- 			<div class="layui-form-mid layui-word-aux"> -->
        <!-- 				说明： -->
        <!-- 				1.前面有<font color="red">*</font>的字段为必填字段。 -->
        <!-- 				2.总计天数=合同天数+赠送天数。 -->
        <!-- 				3.实际售价=实际售价/总计天数。 -->
        <!-- 			</div> -->
        <!-- 		</div> -->
        <div class="pull-right">
            <button type="button" id="uploadContractBtn" class="layui-btn">上传合同相关图片</button>
            <button type="button" id="addCardCollectBtn" class="layui-btn">添加收款信息</button>
            <button type="button" id="searchIDCardBtn" class="layui-btn">扫描身份证</button>
            <button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button type="button" id="saveBtn" class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存
            </button>
        </div>
    </div>
</form>
#end

#define js()
<script id="regidentAreaTpl" type="text/html">
    <div id="regidentArea"
         style="width:700px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
        <div class="tabs clearfix">
            <ul>
                <li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()"
                       class="current">省份</a></li>
                <li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()">城市</a></li>
                <li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
                <li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()">街道/镇/乡</a></li>
            </ul>
        </div>
        <div class="con" style="height:300px;overflow-y: auto;">
            <div class="regidentProvinceAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentCityAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentTownAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentStreetAll">
                <div class="list">

                </div>
            </div>
        </div>
    </div>
</script>
<script id='upFileTrTpl' type='text/html'>
    <tr id="upFile-{{d.idx}}">
        <td>{{d.upFileName}}</td>
        <td>{{d.upDate}}</td>
        <td>
            <input type="hidden" id="upFileId-{{d.idx}}" name="fileList[{{d.idx}}].id" class="layui-input" value="{{d.upFileId}}">
            <input type="hidden" id="upFileRelationId-{{d.idx}}" name="fileList[{{d.idx}}].relationId" class="layui-input" value="{{d.upFileRelationId}}">
            <a data-magnify="gallery" class="layui-btn layui-btn-xs" href="{{d.upFileUrl}}">查看</a>
            #[[
            {{#if(d.formType!='check'){}}
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delCollect('upFile-{{d.idx}}')">作废</a>
            {{#}}}
            ]]#
        </td>
    </tr>
</script>
<script id="rechargeTrTpl" type="text/html">
    <tr id="tr-{{d.idx}}" tr-index="{{d.idx}}">
        <td>
            <input type="text" name="checkinConfig-[{{d.idx}}].name" value="" required lay-verify="required"
                   placeholder="请输入入住人姓名" autocomplete="off" class="layui-input">
        </td>
        <td>
            <input type="text" name="checkinConfig-[{{d.idx}}].idcard" value="" placeholder="请输入入住人身份证号码"
                   autocomplete="off" class="layui-input">
        </td>
        <td>
            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('{{d.idx}}','')">删除</button>
        </td>
    </tr>
</script>
<script id="cardCollectTrTpl" type="text/html">
    <tr id="cardCollect-{{d.idx}}">
        <td><input type="text" name="cardCollectList[{{d.idx}}].collectNumber" class="layui-input"
                   value="{{d.collectNumber}}" placeholder="请输入收款编号" autocomplete="off"></td>
        <td><input type="text" id="collectDate-{{d.idx}}" name="cardCollectList[{{d.idx}}].collectDate"
                   class="layui-input" value="{{d.collectDate}}" placeholder="请输入收款日期" autocomplete="off"></td>
        <td>
            <select id="collectWaySelect-{{d.idx}}" name="cardCollectList[{{d.idx}}].collectWay" lay-verify="required"
                    lay-search="">
                <option value="">请选择</option>
            </select>
        </td>
        <td>
            <select id="collectAccountSelect-{{d.idx}}" name="cardCollectList[{{d.idx}}].collectAccount"
                    lay-filter="collectAccountFilter" lay-search="">
                <option value="">请选择</option>
            </select>
        </td>
        <td><input type="text" name="cardCollectList[{{d.idx}}].collectAmount" class="layui-input"
                   value="{{d.collectAmount}}" placeholder="请输入收款金额" autocomplete="off"></td>
        <td><input type="text" name="cardCollectList[{{d.idx}}].receiptNumber" class="layui-input"
                   value="{{d.receiptNumber}}" placeholder="请输入收据编号" autocomplete="off"></td>
        <td>
			<a data-magnify="gallery" href="{{d.receiptPic}}">
				<img id="receiptImg-{{d.idx}}" src="{{d.receiptPic}}" width="50px;" height="50px;">
			</a>
		</td>
        #[[
        {{#if(d.formType!='check'){}}
        <td>
            <input type="hidden" name="cardCollectList[{{d.idx}}].id" value="{{d.colectId}}">
            <input type="hidden" id="receiptPic-{{d.idx}}" name="cardCollectList[{{d.idx}}].receiptPic" value="{{d.receiptPic}}">
            <button type="button" id="uploadBtn-{{d.idx}}" class="layui-btn layui-btn-xs"><i class="layui-icon"></i>上传收据</button>
            <a class="layui-btn layui-btn-danger layui-btn-xs" onclick="delCollect('cardCollect-{{d.idx}}')">作废</a>
        </td>
        {{#}}}
        ]]#
    </tr>
</script>
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/jquery.magnify.min.js"></script>
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script>
    layui.use(['form', 'laydate', 'layer', 'laytpl', 'upload', 'util','table'], function () {
        var form = layui.form;
        var laytpl = layui.laytpl;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;
        var layer = layui.layer;
        var layuiUtil = layui.util;
        var cardId = '#(card.id??)';
        var table=layui.table;

        form.render('select');

        //开卡时间渲染
        laydate.render({
            elem: '#openTime',
            type: 'date',
            trigger: 'click',
            #if(card == null)
            value:new Date(),
            #end
            done:function () {
                expireDateSetVal();
            }
        });

        //出生日期渲染
        laydate.render({
            elem: '#birthday'
        });
        
//         $('#contractNumber').on('change', function() {
//         	console.log('111');
//         	var cardTypeId = $("#cardTypeId").val();
//         	console.log('cardTypeId==='+cardTypeId);
//             if (cardTypeId=='25934A31-E6EF-4628-89F4-136F6AC33F03') {
//             	console.log('333');
//             	console.log('this.val==='+$(this).val());
//                 $('#cardParentId').val($(this).val());
//                 form.render('select', 'mainCardSelect');
//             }
//         });

        $("#choiceStaff").on('click',function(){
            var url='#(ctxPath)/fina/cardmanager/empTableForm';
            pop_show("员工表",url,900,600);
        });

        setEmpData=function(id,name,workNum){
            $("#staffName").val(name+"("+workNum+")");
            $("#empId").val(id);
        }

        form.on('select(cardTypeFilter)', function (data) {
            var cardTypeId = data.value;
            if ($("#cardTypeId").find('option[value="' + cardTypeId + '"]').attr("isIntegral") == '1') {
                $("#cardMinIntegralsDiv").attr("style", "display: block;");
            } else {
                $("#cardMinIntegralsDiv").attr("style", "display: none;");
            }
            //取消福利卡默认选择要报备，因为除总部其他基地分公司的福利卡允许不用报备
//             if($("#cardTypeId").find('option[value="' + cardTypeId + '"]').attr("typeClassify") == 'welfare_card') {
//             	//默认选择早中晚餐
//             	$("#isBreakfastYes").prop('checked', true);
//             	$("#isLunchYes").prop('checked', true);
//             	$("#isDinnerYes").prop('checked', true);
//             	//福利卡默认要报备
//             	$("#isReportYes").prop('checked', true);
//             }else 
           	if($("#cardTypeId").find('option[value="' + cardTypeId + '"]').attr("typeClassify") == 'owner_card'){
            	//业主卡默认不选择早中晚餐
            	$("#isBreakfastNo").prop('checked', true);
            	$("#isLunchNo").prop('checked', true);
            	$("#isDinnerNo").prop('checked', true);
            	//报备否
//             	$("#isReportNo").prop('checked', true);
            }else{
            	//默认选择早中晚餐
            	$("#isBreakfastYes").prop('checked', true);
            	$("#isLunchYes").prop('checked', true);
            	$("#isDinnerYes").prop('checked', true);
            	//报备否
//             	$("#isReportNo").prop('checked', true);
            }

            if($("#cardTypeId").find('option[value="' + cardTypeId + '"]').attr("typeClassify") == 'welfare_card'){
                //福利卡显示绑定员工

                $("#staffDiv").css('display','block');
                $("#staffName").attr('lay-verify','required');
                $("#staffLable").text("*");
            }else{
                $("#staffDiv").css('display','none');
                $("#staffName").attr('lay-verify','');
                $("#staffLable").text("");
            }

            if (cardTypeId != null && cardTypeId != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/fina/cardType/getCardTypeConfig',
                    data: {id: cardTypeId},
                    notice: false,
                    loadFlag: false,
                    success: function (rep) {
                        var cardTypeConf = rep.cardTypeConf;
                        if (cardTypeConf != null && cardTypeConf != '') {
                            $("#collectAmount").val(cardTypeConf.collectAmount);
                            // $("#cardBalance").val(cardTypeConf.balanceMoney);
                            // $("#cardConsumeTimes").val(cardTypeConf.balanceDays);
                            // $("#cardConsumePoints").val(cardTypeConf.balancePoints);
                            // $("#cardIntegrals").val(cardTypeConf.balanceIntegrals);
                            $("#giveSchemeId").val(cardTypeConf.giveSchemeId);
                            $("#conditionRechargeSchemeId").val(cardTypeConf.conditionRechargeSchemeId);
                            $("#rechargeSchemeId").val(cardTypeConf.rechargeSchemeId);
                            $("#cardPurchaseCardBalance").val(cardTypeConf.buyCardMoney);
                            $("#contractTimes").val(cardTypeConf.contractTimes);
                            $("#contractDiscount").val(cardTypeConf.contractDiscount);
                            $("#buyRechargeAmount").val(cardTypeConf.buyRechargeAmount);
                            $("#buyRechargeDays").val(cardTypeConf.buyRechargeDays);
                            $("#giveRechargeDays").val(cardTypeConf.giveRechargeDays);
                            $("#giveRechargeAmount").val(cardTypeConf.giveRechargeAmount);
                            $("#giveRechargeIntegrals").val(cardTypeConf.giveRechargeIntegrals);
                            $("#deductSchemeId").val(cardTypeConf.deductSchemeId);
                            $("#longDeductSchemeId").val(cardTypeConf.longDeductSchemeId);
                            $("#cardYearLimit").val(cardTypeConf.yearLimit);
                            $("#useYears").val(cardTypeConf.useYears);
                            $("#price").val(cardTypeConf.price);
                            $("#referencePrice").val(cardTypeConf.referencePrice);
                            if(cardTypeConf.isBooking=='0'){                     	
                            	$("#bookingNo").prop('checked', true);
                            }else{
                            	$("#bookingYes").prop('checked', true);
                            }
                            $("#cardDescribe").val(cardTypeConf.remark);
                            if($("#giveSchemeId").val()==''){
                                $("#giveSchemeId").val(cardTypeConf.giveSchemeId);
                            }
                            if($("#rechargeSchemeId").val()){
                                $("#rechargeSchemeId").val(cardTypeConf.rechargeSchemeId);
                            }
                        } else {
                            $("#collectAmount").val('');
                            $("#giveSchemeId").val('');
                            $("#rechargeSchemeId").val('');
                            $("#cardBalance").val('');
                            $("#cardConsumeTimes").val('');
                            $("#cardConsumePoints").val('');
                            $("#cardIntegrals").val('');
                            $("#cardPurchaseCardBalance").val('');
                            $("#contractTimes").val('');
                            $("#contractDiscount").val('');
                            $("#giveConsumeTimes").val('');
                            $("#deductSchemeId").val('');
                            $("#longDeductSchemeId").val('');
                            $("#cardYearLimit").val('');
                            $("#useYears").val('');
                            $("#price").val('');
                            $("#referencePrice").val('');
                            $("#bookingYes").prop('checked', true);
                            $("#cardDescribe").val('');
                        }
                        form.render('select');
                        form.render('radio');
                    },
                    complete: function () {
                    }
                });
            }else{
                $("#collectAmount").val('');
                $("#giveSchemeId").val('');
                $("#rechargeSchemeId").val('');
                $("#cardBalance").val('');
                $("#cardConsumeTimes").val('');
                $("#cardConsumePoints").val('');
                $("#cardIntegrals").val('');
                $("#cardPurchaseCardBalance").val('');
                $("#contractTimes").val('');
                $("#contractDiscount").val('');
                $("#giveConsumeTimes").val('');
                $("#deductSchemeId").val('');
                $("#longDeductSchemeId").val('');
                $("#cardYearLimit").val('');
                $("#useYears").val('');
                $("#price").val('');
                $("#referencePrice").val('');
                $("#bookingYes").prop('checked', true);
                $("#cardDescribe").val('');
                form.render('select');
                form.render('radio');
            }
            if(cardTypeId=='2070AD12-A04C-487D-B9DF-B0668398F27C'){
                $("#baseIdDiv").css('display',"block");
                $("#baseId").attr("lay-verify","required");
            }else{
                $("#baseIdDiv").css('display',"none");
                $("#baseId").attr("lay-verify","");
            }
        });

        form.on('select(collectAccountFilter)', function (data) {
            var collectAccountSelectId = $(data.elem).attr('id');
            var collectAccountSelectArray = collectAccountSelectId.split('-');
            var payWay = $('#' + collectAccountSelectId).find('option:selected').attr('payWay');
            $('#collectWaySelect-' + parseInt(collectAccountSelectArray[1])).val(payWay);
            form.render('select');
        });

        form.on('select(checkinType)', function (data) {
            var value = data.value;
            if (value == '3') {
                $("#checkinConfigDiv").css("display", "block");
            } else {
                $("#checkinConfigDiv").css("display", "none");
            }
        });
        
        form.on('select(memberLinkSelect)', function (data) {
        	var linkId = data.value;
            var linkPhone = $("#memberLinkId").find('option[value="' + linkId + '"]').attr("linkPhone");
            $('#cardTelephone').val(linkPhone);
        });

        $("#addCheckinConfig").on('click', function () {
            laytpl(rechargeTrTpl.innerHTML).render({'idx': (Number($("#count").val()) + 1)}, function (html) {
                $("#count").val((Number($("#count").val()) + 1));
                $("#rechargeTbody").append(html);
                form.render('radio')
            });
        });

        del = function (index, id) {
            if (id == '') {
                $("#tr-" + index).remove();
            } else {
                layer.confirm('确定要作废吗?', function (index) {
                    util.sendAjax({
                        url: "#(ctxPath)/fina/cardmanager/delCardCheckinConfig",
                        type: 'post',
                        data: {'id': id},
                        notice: true,
                        success: function (returnData) {
                            if (returnData.state === 'ok') {
                                $("#tr-" + index).remove();
                                layer.close(index);
                            }
                        }
                    });
                });
            }
        }

        //通过权限处理身份证和手机号码的显示与隐藏
        #shiroHasPermission("finance:idcardSee")
        $("#idcardDiv").css("display", "inline-block");
        #end
        #shiroHasPermission("finance:telephoneSee")
        $("#telephoneDiv").css("display", "inline-block");
        #end
        #if(member == null)
        $("#idcardDiv").css("display", "inline-block");
        $("#telephoneDiv").css("display", "inline-block");
        #end

        $("#useYears").on('keyup', function () {
            expireDateSetVal();
        });

        function expireDateSetVal() {
            var useYears = $("#useYears").val();
            var openTime = $("#openTime").val();
            var reg = new RegExp("^[0-9]*$");
            if (!reg.test(useYears)) {
                $("#useYears").val("");
            }
            if (openTime === '') {
                return false;
            }
            if (Number(useYears) === 0) {
                $("#expireDate").val('');
                return false;
            }
            var date = new Date(openTime);
            date.setFullYear(date.getFullYear() + Number(useYears));
            $("#expireDate").val(date.getFullYear() + "-" + getFormatDate(date.getMonth() + 1) + "-" + getFormatDate(date.getDate()));
        }

        function getFormatDate(arg) {
            if (arg == undefined || arg == '') {
                return '';
            }
            var re = arg + '';
            if (re.length < 2) {
                re = '0' + re;
            }
            return re;
        }

        //赠送详情按钮点击事件
// 		$("#giveSchemeDetail").on('click',function () {
// 			if($("#giveSchemeId").val()===''){
// 				layer.msg('请选择赠送方案',{icon:5,time:5000});
// 				return false;
// 			}
// 			layerShow('赠送详情','#(ctxPath)/fina/cardmanager/giveSchemeDetailIndex?giveSchemeId='+$("#giveSchemeId").val(),800,600);
// 		});

        $("#previewGiveScheme").on('click',function () {
            let giveSchemeId=$("#giveSchemeId").val();
            let openTime=$("#openTime").val();
            if(giveSchemeId==''){
                layer.msg('请先选择分期赠送方案', {icon: 5});
                return false;
            }
            if(openTime==''){
                layer.msg('请先选择开卡时间', {icon: 5});
                return false;
            }
            layerShow('分期赠送详情','#(ctxPath)/fina/cardmanager/giveSchemePreviewIndex?giveSchemeId='+giveSchemeId+"&openTime="+openTime,800,600);
        });

        $("#previewGiveScheme2").on('click',function () {
            let giveSchemeId=$("#rechargeSchemeId").val();
            let openTime=$("#openTime").val();
            if(giveSchemeId==''){
                layer.msg('请先选择分期充值方案', {icon: 5});
                return false;
            }
            if(openTime==''){
                layer.msg('请先选择开卡时间', {icon: 5});
                return false;
            }
            layerShow('分期充值详情','#(ctxPath)/fina/cardmanager/giveSchemePreviewIndex?giveSchemeId='+giveSchemeId+"&openTime="+openTime,800,600);
        });


	deptSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
        var deptSelect = xmSelect.render({
            el: '#'+elem,
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            clickClose:true,
            direction:'down',
            tips:'请选择销售部门',
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                strict: false,
            },
            height: '300px'
            , on:function(data){
                var dataArray = data.arr;//data.arr:  当前多选已选中的数据
                if(data.change){//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                    if(dataArray.length>0){
                        $('#'+hiddenTargetId).val(dataArray[0].id);
                    }else{
                        $('#'+hiddenTargetId).val('');
                    }
                }
            }
        });
        $.post('#(hrmDomain)/api/orgTreeSelect',{},function (res) {
            if (dataValue != null && dataValue != '') {
                deptSelect.update({
                    data:res
                    , initValue:[dataValue]
                });
            }else{
                deptSelect.update({
                    data:res
                });
            }
        });
	};

        getNameAndUserId = function (userId, name, branchOfficeId, deptId) {
            $("#operator").val(userId);
            $("#operatorName").val(name);
            $("#branchOfficeId").val(branchOfficeId);
            $("#salesDeptIdValue").val(deptId);
            if (deptId != '') {
                var salesDeptSelect = xmSelect.get('#salesDeptId', true);
                $.post('#(hrmDomain)/api/orgTreeSelect',{},function (res) {
                    if (deptId != '') {
                        salesDeptSelect.update({
                            data:res
                            , initValue:[deptId]
                        });
                    }else{
                        salesDeptSelect.update({
                            data:res
                        });
                    }
                });
            }
            form.render('select');
        }

        // 选择分公司业务员
        $("#chooseUser").click(function () {
            $(this).blur();
            var url = "#(ctxPath)/fina/cardmanager/chooseUser";
            pop_show("选择分公司业务员", url, 800, 500);
        });

        //校验
        form.verify({
            checkAmount: function (value) {
                if (value != null) {
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if (!reg1.test(value) && !reg2.test(value)) {
                        return "只能输入数字和小数点后两位小数";
                    }
                }
            },
            checkNumber: function (value) {
                if (value != null) {
                    var reg = new RegExp("^[0-9]*$");
                    if (!reg.test(value)) {
                        return "只能输入数字";
                    }
                }
            },
            checkPhone: function (value) {
                if (value != null && value.length > 0) {
                    var reg = new RegExp("^1[3456789]\\d{9}$");
                    var reg1 = new RegExp("^[0][1-9]{2,3}-[0-9]{5,10}$");//座机号带区号
                    var reg2 = new RegExp("^[1-9]{1}[0-9]{5,8}$");//座机号不带区号
                    if (!(reg.test(value) || reg1.test(value) || reg2.test(value))) {
                        return "手机号码格式不正确";
                    }
                }
            }
        });

        //身份证关联出生日期
        function idCardBirthday(psidno) {
            var birthdayno, birthdaytemp;
            if (psidno.length == 18) {
                birthdayno = psidno.substring(6, 14)
            } else if (psidno.length == 15) {
                birthdaytemp = psidno.substring(6, 12);
                birthdayno = "19" + birthdaytemp;
            } else {
                return false
            }
            var birthday = birthdayno.substring(0, 4) + "-" + birthdayno.substring(4, 6) + "-" + birthdayno.substring(6, 8)
            return birthday
        }

        /*$('#idcard').on('keyup', function() {
            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                $('#birthday').val(birthday);
            }
        });
        $('#idcard').on('focus', function() {
            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                $('#birthday').val(birthday);
            }
        });*/

        addFileTpl = function (targetId, addTpl, idx, upFileRelationId, upFileId, upFileName, upFileUrl, upDate) {
            $('#fileCount').val(parseInt(idx) + 1);
            var formType = '#(type??)';
            laytpl(addTpl).render({
                'formType': formType,
                'idx': (parseInt(idx) + 1),
                'upFileRelationId': upFileRelationId,
                'upFileId': upFileId,
                'upFileName': upFileName,
                'upFileUrl': upFileUrl,
                'upDate': upDate
            }, function (html) {
                targetId.append(html);
            });
        };
        
        upload.render({
            elem: '#uploadContractBtn'
            , url: '#(uploadDomain)/fileUpload' //改成您自己的上传接口
            , accept: 'images' //图片
            , data: {
                fileFolder: 'cardContractPic'
                , createBy: '#(userId??)'
            }
            , before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
            }
            , done: function (res) {
                layer.msg('上传成功');
                if (res.state == 'ok') {
                    addFileTpl($('#upFileList'), upFileTrTpl.innerHTML, $('#fileCount').val(), '', res.returnData.fileId, res.returnData.fileName, res.returnData.fileUrl, res.returnData.fileCreateTime);
                } else {
                    layer.msg(res.msg, {icon: 5});
                }
            }
        });

        loadFileList = function (relationId) {
            util.sendAjax({
                type: 'POST'
                , url: '#(uploadDomain)/fileList'
                , data: {relationId: relationId}
                , notice: false
                , success: function (rep) {
                    if (rep.state == 'ok') {
                        if (rep.fileList.length > 0) {
                            $("#fileCount").val(rep.fileList.length);
                        } else {
                            $("#fileCount").val(0);
                        }
                        $.each(rep.fileList, function (i, f) {
                            var upDate = layuiUtil.toDateString(f.createTime, 'yyyy-MM-dd HH:mm:ss');
                            addFileTpl($('#upFileList'), upFileTrTpl.innerHTML, i, f.relationId, f.id, f.fileName, f.fileUrl, upDate);
                        });
                    }
                }
                , complete: function () {
                }
            });
        }

        uploadReceiptPic = function (index) {
            //指定允许上传的文件类型
            upload.render({
                elem: '#uploadBtn-' + index
                , url: '#(uploadDomain)/fileUpload' //改成您自己的上传接口
                , accept: 'images' //图片
                , data: {
                    fileFolder: 'cardCollectReceiptPic'
                    , createBy: '#(userId??)'
                }
                , before: function (obj) { //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                }
                , done: function (res) {
                    layer.msg('上传成功');
                    if (res.state == 'ok') {
                        $('#receiptImg-' + index).attr('src', res.returnData.fileUrl);
                        $('#receiptPic-' + index).val(res.returnData.fileUrl);
                    } else {
                        layer.msg(res.msg, {icon: 5});
                    }
                }
            });
        }

        //添加模板方法
        addCollectTpl = function (targetId, addTpl, idx, colectId, collectNumber, collectDate, collectWay, collectAccount, collectAmount, receiptNumber, receiptPic) {
            $('#collectCount').val(parseInt(idx) + 1);
            var formType = '#(type??)';
            laytpl(addTpl).render({
                'formType': formType,
                'idx': (parseInt(idx) + 1),
                'colectId': colectId,
                'collectNumber': collectNumber,
                'collectDate': collectDate,
                'collectAmount': collectAmount,
                'receiptNumber': receiptNumber,
                'receiptPic': receiptPic
            }, function (html) {
                targetId.append(html);
            });
            #for(payWay : payWayList)
            var selected = '';
            var paymentWayCode = '#(payWay.paymentWayCode??)';
            if (collectWay == paymentWayCode) {
                selected = ' selected="selected"';
            }
            $('#collectWaySelect-' + (parseInt(idx) + 1)).append('<option value="#(payWay.paymentWayCode??)"' + selected + '>#(payWay.name??)</option>');
            #end
            #for(account : accountList)
            var accountSelected = '';
            var accountId = '#(account.id??)';
            if (collectAccount == accountId) {
                accountSelected = ' selected="selected"';
            }
            $('#collectAccountSelect-' + (parseInt(idx) + 1)).append('<option value="#(account.id??)" payWay="#(account.payWay??)"' + accountSelected + '>#(account.accountName??)</option>');
            #end
            //时间渲染
            laydate.render({
                elem: '#collectDate-' + (parseInt(idx) + 1)
            });
            form.render('select');
            uploadReceiptPic((parseInt(idx) + 1));
        };

        delCollect = function (trId) {
            $('#' + trId).remove();
        }

        loadCardCollectList = function () {
            #for(cardCollect : collectList)
                addCollectTpl($('#cardCollectList'), cardCollectTrTpl.innerHTML, '#(for.index)', '#(cardCollect.id??)', '#(cardCollect.collectNumber??)', '#(cardCollect.collectDate??)', '#(cardCollect.collectWay??)', '#(cardCollect.collectAccount??)', '#(cardCollect.collectAmount??)', '#(cardCollect.receiptNumber??)', '#(cardCollect.receiptPic??)');
            #end
        }

        //添加按钮点击事件
        $('#addCardCollectBtn').on('click', function () {
            addCollectTpl($('#cardCollectList'), cardCollectTrTpl.innerHTML, $('#collectCount').val(), '', '', '', '', '', '', '', '');
        });

        if (cardId != null && cardId != '') {
            loadCardCollectList();
            loadFileList(cardId);
        }

        notFitBaseSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
            var notFitBaseXmSelectCheck = xmSelect.render({
                    el: '#' + elem
                    , prop: {
                        name: 'baseName'
                        , value: 'id'
                    }
                    , model: {label: {type: 'text'}}
                    , toolbar: {show: true}
                    , filterable: true
                #if(type == 'check')
                    , disabled: true
                #end
                    , direction: 'down'
                    , tips: '无'
                    , tree: {
                        show:true
                        , strict: false
                    }
                    , height:'300px'
                    , autoRow: true
                    , on: function (data) {
                    var dataArray = data.arr;//data.arr:  当前多选已选中的数据
                    if (data.change) {//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                        if (dataArray.length > 0) {
                            var dArray = new Array();
                            for (var i = 0; i < dataArray.length; i++) {
                                dArray.push(dataArray[i].id);
                            }
                            $('#' + hiddenTargetId).val(dArray.toString());
                        } else {
                            $('#' + hiddenTargetId).val('');
                        }
                    }
                }
            });
            $.post('#(ctxPath)/fina/cardmanager/getAllBase', {}, function (rep) {
                if (rep.state == 'ok') {
                    var initValueArray = null;
                    if (dataValue.indexOf(",") != -1) {
                        initValueArray = dataValue.split(',');
                        notFitBaseXmSelectCheck.update({
                            data: rep.baseList
                            , initValue: initValueArray
                        })
                    } else {
                        notFitBaseXmSelectCheck.update({
                            data: rep.baseList
                            , initValue: [dataValue]
                        })
                    }
                }
            });
        };

        $('div[class="xm-select-demo"]').each(function (i, obj) {
            var targetId = obj.id;
            var hiddenTargetId = $(obj).attr('hiddenTargetId') != null ? $(obj).attr('hiddenTargetId') : '';
            var dataValue = $(obj).attr('dataValue') != null ? $(obj).attr('dataValue') : '';
            if(targetId=='salesDeptId'){
            	deptSelectCheckLoad(targetId, hiddenTargetId, dataValue)
            }else if(targetId=='notFitBaseXmSelectTree'){
            	notFitBaseSelectCheckLoad(targetId, hiddenTargetId, dataValue);
            }
        });

        //保存
        form.on('submit(saveBtn)', function () {
            if ($("#idh").val() != null && $("#idh").val() != '') {
                $("#cardTypeId").attr("disabled", false);
            }
            // util.sendAjax({
            // 	url:'#(ctxPath)/fina/cardmanager/disposableGiveScheme',
            // 	type:'post',
            // 	data:{"cardId":$("#idh").val(),"giveSchemeId":$("#giveSchemeId").val()},
            // 	notice:false,
            // 	success:function(returnData){
            // 		if(returnData.state==='ok'){
            // 			layer.confirm('赠送方案中有'+returnData.data.size+'条一次性的赠送,总金额为:'+returnData.data.totalGiveAmount+',总天数为:'+returnData.data.totalGiveTimes+'是否在保存后自动赠送?',function (index) {
            // 				$("#isGive").val(1);
            // 				save();
            // 				layer.close(index);
            // 			},function () {
            // 				save();
            // 			})
            // 		}else{
            // 			save();
            // 		}
            // 	}
            // });
            var longDeductSchemeId = $("#longDeductSchemeId").val();
            var deductSchemeId = $("#deductSchemeId").val();
            if (longDeductSchemeId != '' && deductSchemeId != '') {
                layer.msg('旅居扣费方案和长住扣费方案只能选一个', {icon: 2, offset: 'auto'});
                return false;
            } else if (longDeductSchemeId == '' && deductSchemeId == '') {
                layer.msg('旅居扣费方案和长住扣费方案至少选一个', {icon: 2, offset: 'auto'});
                return false;
            }

            $.ajax({
                url: '#(ctxPath)/fina/cardmanager/disposableGiveScheme',
                type: 'post',
                data: {"cardId": $("#idh").val(), "giveSchemeId": $("#giveSchemeId").val()},
                dataType: 'json',
                success: function (returnData) {
                    if (returnData.state === 'ok') {
                        layer.confirm('赠送方案中有' + returnData.data.size + '条一次性的赠送,总金额为:' + returnData.data.totalGiveAmount + ',总天数为:' + returnData.data.totalGiveTimes + '是否在保存后自动赠送?', function (index) {
                            $("#isGive").val(1);
                            save();
                            layer.close(index);
                        }, function () {
                            save();
                        })
                    } else {
                        save();
                    }
                }
            });
            return false;
        });

        save = function () {
            $("#saveBtn").attr("disabled", true);
            $("#saveBtn").addClass("layui-btn-disabled");
            util.sendAjax({
                url: '#(ctxPath)/fina/cardmanager/save',
                type: 'post',
                data: $("#cardInfoForm").serialize(),
                notice: true,
                success: function (returnData) {
                    if (returnData.state === 'ok') {
                        pop_close();
                        parent.formOperateReload();
                    }
                },
                complete: function () {
                    $("#saveBtn").attr("disabled", false);
                    $("#saveBtn").removeClass("layui-btn-disabled");
                }
            });
        }

        //扫描身份证
        $("#searchIDCardBtn").on('click', function () {
            util.sendAjax({
                url: 'http://127.0.0.1:1500/Service?type=IDCard&method=GetCardInfo',
                type: 'post',
                data: JSON.stringify({}),
                success: function (result) {
                    var data = result.Data;
                    if (result.Type === 1) {
                        $('#cardName').val(data.name);
                        $('#idcard').val(data.cardID);
                        $('#address').val(data.address);
                        if (data.gender === "男") {
                            $("#cardGender option[value='1']").prop("selected", true);
                        } else {
                            $("#cardGender optoin[value='2']").prop("selected", true);
                        }
                        form.render("select");
                    } else {
                        layer.msg('读取失败', {icon: 5, time: 5000});
                    }
                }
            });

        });

        /*//姓名绑定
        $("#cardName").on("blur",function(){
            checkNameAndIdcard();
        });

        //身份证绑定
        $("#idcard").on("blur",function(){
            checkNameAndIdcard();
        });*/

        function checkNameAndIdcard() {
//             var cardName = $("#cardName").val();
            var idcard = $("#idcard").val();
            if (idcard != null && idcard.trim() != "") {
                util.sendAjax({
                    url: '#(ctxPath)/fina/cardmanager/bindMmsMember',
                    type: 'post',
                    data: {idcard: idcard},
                    notice: false,
                    success: function (returnData) {
                        if (returnData.state === 'ok' && returnData.msg != '无信息') {
                            if(returnData.data.surname!=null && returnData.data.surname!=''){
                            	$("#surname").val(returnData.data.surname);
                            }
                            if(returnData.data.fullName!=null && returnData.data.fullName!=''){
                            	$("#cardName").val(returnData.data.fullName);
                            }
//                             if(returnData.data.telephone!=null && returnData.data.telephone!=''){
// 	                            $('#telephone').val(returnData.data.telephone);
//                             }
							if(returnData.linkList!=null && returnData.linkList.length>0){
								$('#memberLinkDiv').html('');
								var html = '<select id="memberLinkId" name="memberLinkId" lay-verify="required" lay-filter="memberLinkSelect">';
                                html += '<option value="" linkPhone="">请选择</option>';
                                for (var i = 0; i < returnData.linkList.length; i++) {
                                    html += '<option value="'+returnData.linkList[i].id+'" linkPhone="'+returnData.linkList[i].linkPhone+'">'+returnData.linkList[i].linkName+'('+returnData.linkList[i].linkPhone+')</option>';
                                }
                                html += '</select>';
                                html += '<input type="hidden" id="cardTelephone" name="card.telephone" value=""/>';
                                $('#memberLinkDiv').append(html);
							}else{
                                $('#memberLinkDiv').html('');
                                var html = '<input type="text" id="cardTelephone" name="card.telephone" lay-verify="required|checkPhone" class="layui-input" value="" placeholder="请输入手机号">';
                                $('#memberLinkDiv').append(html);
                            }
                            if (returnData.data.birthday != null && returnData.data.birthday != '') {
                                $('#birthday').val(returnData.data.birthday);
                            }
                            //处理区域
                            $("#province").val(returnData.data.province);
                            $("#city").val(returnData.data.city);
                            $("#town").val(returnData.data.town);
                            $("#street").val(returnData.data.street);
                            $("#regidentProvinceName").val(returnData.province);
                            $("#regidentCityName").val(returnData.city);
                            $("#regidentTownName").val(returnData.town);
                            $("#regidentStreetName").val(returnData.street);
                            $('#address').val(returnData.data.address);

                            var addr = "";
                            if (returnData.province != null) {
                                addr += " " + returnData.province;
                            }
                            if (returnData.city != null) {
                                addr += " " + returnData.city;
                            }
                            if (returnData.town != null) {
                                addr += " " + returnData.town;
                            }
                            if (returnData.street != null) {
                                addr += " " + returnData.street;
                            }
                            $("#regidentAddress").val(addr);

                            if (returnData.data.gender == "1") {
                                $("#cardGender option[value='1']").prop("selected", true).siblings().prop("selected", false);
                            } else if (returnData.data.gender == "2") {
                                $("#cardGender option[value='2']").prop("selected", true).siblings().prop("selected", false);
                            } else if (returnData.data.gender == "0") {
                                $("#cardGender option[value='0']").prop("selected", true).siblings().prop("selected", false);
                            } else {
                                $("#cardGender option").prop("selected", false);
                                //$("#cardGender option[value='0']").prop("selected", true).siblings().prop("selected",false);
                            }
                        } else {
                            $("#cardGender option").prop("selected", false);
                            //$("#cardGender option[value='0']").prop("selected", true).siblings().prop("selected",false);
                            layer.msg("没有档案信息", {time: 800});
                        }
                        form.render("select");
                    }
                });

            }else{
            	layer.msg("请填写身份证号", {time: 800});
            }
        }

        //获取会员信息
        $("#getMemberInfo").on("click", function () {
//             $("#surname").val("");
//             $("#cardName").val("");
//             $("#birthday").val("");
//             $("#telephone").val("");
            $("#province").val("");
            $("#city").val("");
            $("#town").val("");
            $("#street").val("");
            $("#regidentProvinceName").val("");
            $("#regidentCityName").val("");
            $("#regidentTownName").val("");
            $("#regidentStreetName").val("");
            $("#regidentAddress").val("");
            $("#address").val("");
            checkNameAndIdcard();
            var idcard = $("#idcard").val();
            if (idcard != null && idcard != '' && idcard != undefined) {
                var newBirthday = idCardBirthday(idcard);
                if (newBirthday != null && newBirthday != '' && newBirthday != undefined) {
                    $("#birthday").val('');
                    $('#birthday').val(newBirthday);
                }
            }
            form.render("select");
        });

        //卡号生成
// 		$("#ran").on("click",function(){
// 			var cardTypeId = $("#cardTypeId").val();
// 			util.sendAjax({
// 				url:'#(ctxPath)/fina/cardmanager/getRandomNumber',
// 				type:'post',
// 				data:{"cardTypeId":cardTypeId},
// 				notice:false,
// 				success:function(returnData){
// 					if(returnData.state==='ok'){
// 						$('#cardNumber').val(returnData.data);
// 					}else{
// 						layer.msg(returnData.msg,{icon:5,time:5000});
// 					}
// 				}
// 			});
// 		});

        //清空区域
        $("#areaClear").on("click", function () {
            $("#province").val("");
            $("#city").val("");
            $("#town").val("");
            $("#street").val("");
            $("#regidentProvinceName").val("");
            $("#regidentCityName").val("");
            $("#regidentCountyName").val("");
            $("#regidentStreetName").val("");
            $("#regidentAddress").val("")
        });

        //--------------------------居住区域begin---------------------------
        $('#regidentAddress').on('click', function () {
            //closeIdArea();

            $('#regidentArea').remove();
            var $this = $(this);
            var getTpl = regidentAreaTpl.innerHTML;
            $this.parent().append(getTpl);
            //event.stopPropagation();

            var street = $("#street").val();
            var regidentStreetName = $("#regidentStreetName").val();
            var town = $("#town").val();
            var regidentCountyName = $("#regidentCountyName").val();
            var city = $("#city").val();
            var regidentCityName = $("#regidentCityName").val();
            var province = $("#province").val();
            var regidentProvinceName = $("#regidentProvinceName").val();
            if (street != '' && regidentStreetName != '') {
                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentStreetLoad(town);
                regidentCountyLoad(city);
                regidentCityLoad(province);
                regidentProvinceLoad();
                $(".con .regidentStreetAll").show().siblings().hide();
                //clickStreet(streetId,streetName);
            } else if (town != '' && regidentCountyName != '') {

                if (town != '') {
                    regidentCityLoad(province);
                    regidentCountyLoad(city);
                    regidentProvinceLoad();
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/area/getAreas',
                        data: {pid: town},
                        notice: false,
                        loadFlag: false,
                        success: function (res) {
                            if (res.state == 'ok') {
                                if (res.data.length > 0) {
                                    $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    var html = "<ul>";
                                    $.each(res.data, function (i, item) {
                                        html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
                                    });
                                    html += "</ul>";
                                    $(".regidentStreetAll .list").append(html);
                                    //viewStreet(countyId,countyName);
                                    $(".con .regidentStreetAll").show().siblings().hide();
                                } else {
                                    //无 街道信息
                                    $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    $(".con .regidentTownAll").show().siblings().hide();
                                }
                            }
                        },
                        complete: function () {
                        }
                    });
                }
            } else if (city != '' && regidentCityName != '') {
                regidentProvinceLoad();
                regidentCityLoad(province);
                viewRegidentCounty(city, regidentCityName);
            } else if (province != '' && regidentProvinceName != '') {
                regidentProvinceLoad();
                viewRegidentCity(province, regidentProvinceName);
            } else {
                regidentProvinceLoad();
            }

            //去除事件冒泡
            var evt = new Object;
            if (typeof (window.event) == "undefined") {//如果是火狐浏览器
                evt = arguments.callee.caller.arguments[0];
            } else {
                evt = event || window.event;
            }
            evt.cancelBubble = true;
            $('#regidentArea').off('click');
            $('#regidentArea').on('click', function () {
                //event.stopPropagation();
                //去除事件冒泡
                var evt = new Object;
                if (typeof (window.event) == "undefined") {//如果是火狐浏览器
                    evt = arguments.callee.caller.arguments[0];
                } else {
                    evt = event || window.event;
                }
                evt.cancelBubble = true;
            })
        });

        regidentProvinceLoad = function () {
            util.sendAjax({
                type: 'POST',
                url: '#(ctxPath)/area/getAreas',
                data: {pid: ''},
                notice: false,
                loadFlag: false,
                success: function (res) {
                    if (res.state == 'ok') {
                        if (res.data.length > 0) {
                            $(".regidentProvinceAll .list").empty();
                            var html = "<ul>";
                            $.each(res.data, function (i, item) {
                                html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCity(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
                            });
                            html += "</ul>";
                            $(".regidentProvinceAll .list").append(html);
                        }
                    }
                },
                complete: function () {
                }
            });
        };

        //点击省事件
        viewRegidentCity = function (province, regidentProvinceName) {
            $("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentCityAll").show().siblings().hide();
            $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            //点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
            $("#province").val(province);
            $("#regidentProvinceName").val(regidentProvinceName);
            $("#city").val("");
            $("#regidentCityName").val("");
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCityLoad(province);
        };

        //加载市
        regidentCityLoad = function (province) {
            if (province != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: province},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $(".regidentCityAll .list").empty();
                                var html = "<ul>";
                                $.each(res.data, function (i, item) {
                                    html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCounty(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
                                });
                                html += "</ul>";
                                $(".regidentCityAll .list").append(html);
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        //点击市事件
        viewRegidentCounty = function (city, RegidentCityName) {
            $("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentTownAll").show().siblings().hide();
            $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#city").val(city);
            $("#regidentCityName").val(RegidentCityName);
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCountyLoad(city);
        };

        //加载区/县
        regidentCountyLoad = function (city) {
            if (city != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: city},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $(".regidentTownAll .list").empty();
                                var html = "<ul>";
                                $.each(res.data, function (i, item) {
                                    html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
                                });
                                html += "</ul>";
                                $(".regidentTownAll .list").append(html);
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        //点击区/县事件
        viewRegidentStreet = function (town, regidentCountyName) {
            $("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentStreetAll").show().siblings().hide();
            $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#town").val(town);
            $("#regidentCountyName").val(regidentCountyName);
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentStreetLoad(town);
        };

        //加载街道/镇/乡
        regidentStreetLoad = function (town) {
            if (town != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: town},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $(".regidentStreetAll .list").empty();
                                var html = "<ul>";
                                $.each(res.data, function (i, item) {
                                    html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
                                });
                                html += "</ul>";
                                $(".regidentStreetAll .list").append(html);
                            } else {
                                //无 街道信息
                                clickRegidentStreet('', '');
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        clickRegidentStreet = function (street, regidentStreetName) {
            $("#street").val(street);
            $("#regidentStreetName").val(regidentStreetName);
            var regidentProvinceName = $("#regidentProvinceName").val();
            var regidentCityName = $("#regidentCityName").val();
            var regidentCountyName = $("#regidentCountyName").val();
            var regidentStreetName = $("#regidentStreetName").val();
            var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
            $("#regidentAddress").val(add);
            $('#regidentArea').remove();
        };

        regidentProvinceAllClick = function () {
            //$(".con .provinceAll").show();
            $("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentProvinceAll").show().siblings().hide();
        };

        regidentCityAllClick = function () {
            // $(".con .cityAll").show();
            if ($("#province").val() != '') {
                $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCityLoad($("#province").val());
                $(".con .regidentCityAll").show().siblings().hide();
            }
        };

        regidentTownAllClick = function () {
            if ($("#city").val() != '') {
                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCountyLoad($("#city").val());
                $(".con .regidentTownAll").show().siblings().hide();
            }
        };

        regidentStreetAllClick = function () {
            if ($("#town").val() != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid: $("#town").val()},
                    notice: false,
                    loadFlag: false,
                    success: function (res) {
                        if (res.state == 'ok') {
                            if (res.data.length > 0) {
                                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                regidentStreetLoad($("#town").val());
                                $(".con .regidentStreetAll").show().siblings().hide();
                            } else {
                                //无 街道信息 显示区/县信息
                                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                //countyLoad(cityId);
                                $(".con .regidentTownAll").show().siblings().hide();
                            }
                        }
                    },
                    complete: function () {
                    }
                });
            }
        };

        $('body').on('click', function () {
            closeRegidentArea();
        });

        //关闭区域选择器
        closeRegidentArea = function () {
            if (typeof ($('#regidentArea').html()) != 'undefined') {
                var regidentProvinceName = $("#regidentProvinceName").val();
                var regidentCityName = $("#regidentCityName").val();
                var regidentCountyName = $("#regidentCountyName").val();
                var regidentStreetName = $("#regidentStreetName").val();
                var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
                $("#regidentAddress").val(add);
            }
            //alert(1);
            $('#regidentArea').remove();
        }
        //-------------------------居住区域end----------------------------
    });
    //修改时间格式
    $(function () {

        /*------------查看将所有input button select设置为只读--------------------*/
        #if(type == 'check')
        $("#cardInfoForm button").prop("disabled", true);
        $("#cardInfoForm button").addClass("layui-btn-disabled")
        //$("#giveSchemeDetail").prop("disabled",false);
        //$("#giveSchemeDetail").removeClass("layui-btn-disabled");
        $("#cardInfoForm .layui-form-footer").hide();
        $("#cardInfoForm").find("input,select,textarea").prop("disabled", true).prop("readonly", true);
        $("#cardInfoForm select").removeAttr("lay-search");
        form.render('select');
        form.render('radio');
        #end

        //图片插件初始化
        $('[data-magnify]').magnify({
            headerToolbar: [
                'close'
            ],
            footerToolbar: [
                'zoomIn',
                'zoomOut',
                'prev',
                'fullscreen',
                'next',
                'actualSize',
                'rotateRight'
            ],
            title: false
        });
    });
</script>
#end
