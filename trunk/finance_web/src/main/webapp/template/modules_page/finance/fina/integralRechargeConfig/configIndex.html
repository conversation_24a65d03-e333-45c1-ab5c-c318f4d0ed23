#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()积分卡充值配置首页#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
						#shiroHasPermission("finance:account:add")
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
						#end
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
    <div class="layui-row" style="padding: 10px;">
        <table id="accountTable" lay-filter="accountTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="accountTableBar">
    #shiroHasPermission("finance:account:edit")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("finance:account:del")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','form','vip_table'],function(){

        // 操作对象
        var layer = layui.layer
            ,form = layui.form
            ,table = layui.table
            ,vipTable = layui.vip_table
            ,$ = layui.jquery
            ,tableId = 'accountTable'
        ;

        // 表格渲染
        pageTableReload = function (data) {
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id: tableId
                , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
                , even: true //开启隔行背景
                , url: '#(ctxPath)/fina/integralRechargeConfig/configPage'
                , where: data
                , method: 'post'
                , height: vipTable.getFullHeight()    //容器高度
                , cols: [[                  //标题栏
                    {field: 'productPrice', title: '产品价格', unresize:true}
                    , {field: 'rechargeDays', title: '天数', unresize:true}
                    , {field: 'integralRatio', title: '对应积分系数', unresize:true}
                    , {field: 'dayIntegral', title: '日积分', unresize:true}
                    , {field: 'monthIntegral', title: '月积分(30天)', unresize:true}
                    , {field: 'yearIntegral', title: '年积分(360天)', unresize:true}
                    , {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#accountTableBar'} //这里的toolbar值是模板元素的选择器
                ]]
                , page: true
                , done: function (res, curr, count) {
                    layer.close(loadingIndex);
                }
            });
            // 表格绑定事件
            table.on('tool('+tableId+')',function (obj) {
                if(obj.event==="edit"){//编辑按钮事件
                    pop_show('编辑','#(ctxPath)/fina/integralRechargeConfig/form?id='+obj.data.id,'500','600');
                }else if(obj.event==="del"){//作废按钮事件
                    //作废操作
                    layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/fina/integralRechargeConfig/saveConfig',
                            data: {id:obj.data.id, delFlag:'1'},
                            loadFlag: true,
                            success : function(rep){
                            },
                            complete : function() {
                                pageTableReload();
                            }
                        });
                        layer.close(index);
                    });
                }
            });
        }

        pageTableReload(null);

        //搜索按钮点击事件
        $('#searchBtn').on('click', function() {
            pageTableReload(null);
        });
      	//回车事件
    	document.onkeydown = function(e){
    		var ev =document.all ? window.event:e;  
    		if(ev.keyCode==13) {
    			$('#searchBtn').click();
    			return false
    		}
    	}
        //添加按钮点击事件
        $('#addBtn').on('click', function() {
            pop_show('添加','#(ctxPath)/fina/integralRechargeConfig/form','500','600');
        });
        // 刷新
        $('#refreshBtn').on('click', function () {
            pageTableReload(null);
        });
    })
</script>
#end