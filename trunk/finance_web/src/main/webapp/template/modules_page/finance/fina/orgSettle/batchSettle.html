#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()批量结算首页#end

#define css()
#end

#define content()
<div class="my-btn-box">
<div class="layui-row">
	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
		<div class="layui-inline">
			<label class="layui-form-label">基地</label>
			<div class="layui-input-inline" style="width:182px;">
			     <select id="baseId" name="baseId" lay-search>
			         <option value="">请选择基地</option>
			         #for(b : baseList)
			         <option value="#(b.id)">#(b.baseName)</option>
			         #end
			     </select>
			 </div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">会员卡号</label>
			<div class="layui-input-inline">
				<div class="layui-inline">
					<input type="text" id="cardNumber" name="cardNumber" class="layui-input"/>
				</div>
			</div>
		</div>
		<div class="layui-inline">
        	<div class="layui-btn-group">
		        <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
<!-- 		        <button type="button" id="settleBtn" class="layui-btn">结算</button> -->
    		</div>
        </div>
	</form>
</div>
<div class="layui-row">
	<table id="settleTable" lay-filter="settleTable"></table>
</div>
</div>
#end

#define js()
<script type="text/html" id="inputNowTpl">
#[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="nowDeductTimes" min="0" value="{{d.actualTimes}}"
                   lay-verify="checkNumber|required" oninput="calculateNowDeductTimes(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_days'){}} readonly  {{#}}}>
        </div>
    </div>
    {{#}else{}}
        {{d.actualTimes}}
    {{#}}}
]]#
</script>
<script type="text/html" id="inputNowATpl">
#[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline">
        <div class="layui-input-inline">
            <input type="text" name="nowDeductAmount" min="0" value="{{d.actualAmount}}"
                   lay-verify="checkAmount|required" oninput="calculateNowDeductAmount(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_money'){}} readonly  {{#}}}>
        </div>
    </div>
    {{#}else{}}
        {{d.actualAmount}}
    {{#}}}
]]#
</script>
<script type="text/html" id="inputNowPTpl">
#[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline">
        <div class="layui-input-inline">
            <input type="text" name="nowDeductPoints" min="0" value="{{d.actualPoints}}"
                   lay-verify="checkAmount|required" oninput="calculateNowDeductPoints(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_points'){}} readonly  {{#}}}>
        </div>
    </div>
    {{#}else{}}
    {{d.actualPoints}}
    {{#}}}
]]#
</script>
<script type="text/html" id="inputNowITpl">
#[[
    {{#if(d.settleStatus==='0'){}}
    <div class="layui-inline">
        <div class="layui-input-inline">
            <input type="text" name="nowDeductIntegrals" min="0" value="{{d.actualIntegrals}}"
                   lay-verify="checkAmount|required" oninput="calculateNowDeductIntegrals(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_days' || d.isIntegral!="1"){}} readonly  {{#}}}>
        </div>
    </div>
    {{#}else{}}
    {{d.actualIntegrals}}
    {{#}}}
]]#
</script>
<script type="text/html" id="inputRemarkTpl">
#[[
	<div class="layui-inline">
		<div class="layui-input-inline">
            {{#if(typeof(d.settleRemark)==='undefined'){}}
                <input type="text" name="settleRemark" min="0" oninput="updateRemark(this)" value="" class="layui-input">
            {{#}else{}}
                <input type="text" name="settleRemark" min="0" oninput="updateRemark(this)" value="{{d.settleRemark}}" class="layui-input">
            {{#}}}
		</div>
    </div>
]]#
</script>
<script type="text/html" id="toolBar">
#[[
    {{#if(d.settleStatus==='0'){}}
        {{#if(d.settleType==='checkout_settle'){}}
        	<button class="layui-btn layui-btn-sm" lay-event="checkoutSettleBtn">退住结算</button>
        {{#}else if(d.settleType==='month_settle'){}}
        	<button class="layui-btn layui-btn-sm" lay-event="monthSettleBtn">月结算</button>
        {{#}}}
    {{#}}}
]]#
</script>
<script type="text/html" id="settleStatus">
    #[[
        {{#if(d.settleStatus==='0'){}}
            <span class='layui-badge layui-bg-orange'>未结算</span>
        {{#}else{}}
            <span class='layui-badge layui-bg-green'>已结算</span>
        {{#}}}
    ]]#
</script>
<script type="text/javascript">
layui.use([ 'table', 'form', 'layer' ], function() {
	var table = layui.table
	, form = layui.form
	, layer = layui.layer
	, $ = layui.jquery
	;
	
	function batchSettleLoad(data){
        //loading层
        var loadingIndex = layer.load(2, { //icon支持传入0-2
            shade: [0.5, 'gray'], //0.5透明度的灰色背景
            content: '加载中...',
            success: function (layero) {
                layero.find('.layui-layer-content').css({
                    'padding-top': '39px',
                    'width': '60px'
                });
            }
        });
		table.render({
			id : 'settleTable'
			,elem : '#settleTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/fina/settleDetail/batchSettlePage'
			,height: 'full-90'
			,cols: [[
// 				{type:'checkbox'}
				{type:'numbers'}
				,{field:'', title: '会员卡', align: 'center', unresize: true,width:180,templet:"<div>{{d.cardNumber}}-{{d.fullName}}</div>"}
                ,{field:'checkinNo',title: '入住号', align: 'center', unresize: true,width:160}
                ,{field:'', title: '结算类型', align: 'center',unresize: true,width:90,templet:function (d) {
                        if(d.settleType==='month_settle'){
                            return "月结算";
                        }else if(d.settleType==='checkout_settle'){
                            return "退住结算";
                        }else{
                            return "- -";
                        }
                    }}
                ,{field:'yearMonth',title: '账单月份', align: 'center', unresize: true,width:90}
                ,{field:'', title: '产生时间', align: 'center', unresize: true,width:193,templet:"<div>{{dateFormat(d.startTime,'yyyy-MM-dd')}}至{{dateFormat(d.endTime,'yyyy-MM-dd')}}</div>"}
                ,{field:'', title: '锁定', align: 'center',unresize: true,width:185,templet:function (d) {
                        var str = "";
                        if(String(d.totalTimes) != null && String(d.totalTimes) != ''){
                            if(typeof(d.totalTimes)=='undefined'){
                                str = "0天";
                            }else{
                                str = String(d.totalTimes) +"天";
                            }

                        }else{
                            str = "- -";
                        }
                        str += " ";
                        if(String(d.totalAmount) != null && String(d.totalAmount) != null){

                            if(typeof(d.totalAmount)=='undefined'){
                                str+="0元";
                            }else{
                                str += String(d.totalAmount) +"元";
                            }
                        }else{
                            str += "- -";
                        }
                        str += " ";
                        if(String(d.totalPoints) != null && String(d.totalPoints) != null){
                            if(typeof(d.totalPoints)=='undefined'){
                                str+="0点数";
                            }else{
                                str += String(d.totalPoints) +"点数";
                            }
                        }else{
                            str += "- -";
                        }
                        if(String(d.totalIntegrals) != null && String(d.totalIntegrals) != null){
                            if(typeof(d.totalIntegrals)=='undefined'){
                                str+="0积分";
                            }else{
                                str += String(d.totalIntegrals) +"积分";
                            }
                        }else{
                            str += "- -";
                        }
                        return str;
                    }}
                ,{field:'actualTimes', title: '扣卡天数',unresize: true,width:100,templet:'#inputNowTpl'}
                ,{field:'actualAmount', title: '扣卡金额',unresize: true,width:100,templet:'#inputNowATpl'}
                ,{field:'actualPoints', title: '扣卡点数',unresize: true,width:100,templet:'#inputNowPTpl'}
                ,{field:'actualIntegrals', title: '扣卡积分',unresize: true,width:100,templet:'#inputNowITpl'}
//                 ,{field:'settleStatus', title: '结算状态', align: 'center',unresize: true,width:110,templet:'#settleStatus'}
                ,{field:'remark', title: '备注', align: 'center',unresize: true,width:200}
                ,{field:'settleRemark', title: '结算备注', align: 'center',unresize: true,width:200,templet:"#inputRemarkTpl"}
                ,{fixed: 'right', title:'操作', toolbar: '#toolBar', width:120}
			]]
			,page : true
			,limit : 15
			,limits : 1
		    , done: function (res, curr, count) {
                layer.close(loadingIndex);
		    }
		});
		table.on('tool(settleTable)',function (obj) {
            var settleData={'expenseId':obj.data.expenseId,'detailId':obj.data.id,'actualTimes':obj.data.actualTimes,'actualAmount':obj.data.actualAmount
                ,'actualPoints':obj.data.actualPoints,'actualIntegrals':obj.data.actualIntegrals,'settleRemark':obj.data.settleRemark}
            if(obj.event==='monthSettleBtn'){
                layer.confirm("确定要结算吗?",{
                    skin : "my-skin"
                },function(index) {
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/fina/settleDetail/settleMonthDetail',
                        data: settleData,
                        notice: true,
                        loadFlag: true,
                        success: function (rep) {
                            if (rep.state == 'ok') {
                            	batchSettleLoad({baseI:$("#baseI").val(),cardNumber:$("#cardNumber").val()});
                            }
                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event==='checkoutSettleBtn'){
                layer.confirm("确定要结算吗?",{
                    skin : "my-skin"
                },function(index) {
                    layer.load();
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/fina/settleDetail/settleCheckoutDetail',
                        data: settleData,
                        notice: true,
                        loadFlag: true,
                        success: function (rep) {
                            if (rep.state == 'ok') {
                                batchSettleLoad({baseI:$("#baseI").val(),cardNumber:$("#cardNumber").val()});
                            }
                        }
                    });
                    layer.close(index);
                });
            }
        });
	};
	
	batchSettleLoad();
	
	form.on("submit(search)",function(data){
		batchSettleLoad(data.field);
		return false;
	});
	
	//批量获取列表选中数据
	getCheckTableData = function(){
		var checkTable = table.checkStatus('settleTable');
		return checkTable.data;
	}
	
	rowUpdate = function(tableId, index, data) {
        layui.$.extend(table.cache[tableId][index], data);
    }
	
    //重新
    updateRemark=function(obj){
        var remark=obj.value;
        var index = $(obj).parents("tr").attr("data-index");
        var data = {settleRemark:remark};
        rowUpdate('settleTable',index,data);
    }
	
	// 结算
	$("#settleBtn").click(function(){
		var dataArray = new Array();
		var tableData = getCheckTableData();
    	if(tableData.length>0){
    		for (var i=0;i<tableData.length;i++){
    			var settleRemark = '';
        		if(typeof(tableData[i].settleRemark)!='undefined'){
        			settleRemark = tableData[i].settleRemark;
        		}
// 	    		console.log('tableData['+i+'].id==='+tableData[i].id);
// 	    		console.log('tableData['+i+'].expenseId==='+tableData[i].expenseId);
// 	    		console.log('tableData['+i+'].settleType==='+tableData[i].settleType);
// 	    		console.log('tableData['+i+'].actualTimes==='+tableData[i].actualTimes);
// 	    		console.log('tableData['+i+'].actualAmount==='+tableData[i].actualAmount);
// 	    		console.log('tableData['+i+'].actualPoints==='+tableData[i].actualPoints);
// 	    		console.log('tableData['+i+'].actualIntegrals==='+tableData[i].actualIntegrals);
// 	    		console.log('settleRemark['+i+']==='+settleRemark);
	    		var dataObj = {
    				'id':tableData[i].id, 
    				'expenseId':tableData[i].expenseId, 
    				'settleType':tableData[i].settleType,
    				'cardNumber':tableData[i].cardNumber,
    				'actualTimes':tableData[i].actualTimes,
    				'actualAmount':tableData[i].actualAmount,
    				'actualPoints':tableData[i].actualPoints,
    				'actualIntegrals':tableData[i].actualIntegrals,
    				'settleRemark':settleRemark
   				};
		    	dataArray.push(dataObj);
    		};
    		if(dataArray.length>0){
    			layer.confirm("确定要结算吗?",function(index){
                    layer.load();
                    util.sendAjax ({
        	            type: 'POST',
        	            url: '#(ctxPath)/fina/settleDetail/batchSettleSave',
        	            data: {batchDatas:JSON.stringify(dataArray)},
        	            notice: true,
        			    loadFlag: true,
        	            success : function(rep){
        	            	if(rep.state=='ok'){
        	            		batchSettleLoad({baseId:$('#baseId').val(), cardNumber:$('#cardNumber').val()});
        	            	}
        	            },
        	            complete : function() {
        			    }
        	        });
                    layer.close(index);
                });
    		}else{
    			layer.msg('请勾选数据', function () {});
    		}
    	}else{
    		layer.msg('请勾选数据', function () {});
    	}
	});
});
</script>
#end