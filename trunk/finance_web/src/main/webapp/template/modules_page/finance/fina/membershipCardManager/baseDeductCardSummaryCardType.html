#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地扣卡卡类别汇总表#end

#define css()
#end

#define js()
<script type="text/javascript">
	var $ ;
    layui.use(['table','form','laydate'],function(){
    	$ = layui.$;
        var table = layui.table
        , form = layui.form
        , laydate = layui.laydate
		;
        
        laydate.render({
            elem:"#deductMonth",
            type:"month",
            range:false,
            format:"yyyy-MM"
        });
        
        summaryDetail = function(baseId, deductMonth, cardTypeId) {
        	var url = "#(ctxPath)/fina/cardmanager/baseDeductCardSummaryDetail?baseId="+baseId+'&deductMonth='+deductMonth+'&cardTypeId='+cardTypeId;
			layerShow("详情",url,'950','');
        }
        
		dailyStatisticsLoad = function () {
			layer.load();
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/cardmanager/baseDeductCardSummaryCardTypeList',
                data: {baseId:$('#baseId').val(), deductMonth:$('#deductMonth').val()},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	$('#baseDeductCardSummaryTypeList').empty();
                    	var html = '';
                    	var totalDays = 0;
                    	var totalMoney = 0;
                    	$.each(rep.data, function(i,deduct){
							var baseId = deduct.baseId!=null && deduct.baseId!=''?deduct.baseId:'';
							var baseName = deduct.baseName!=null && deduct.baseName!=''?deduct.baseName:'';
							var cardTypeId = deduct.cardTypeId!=null && deduct.cardTypeId!=''?deduct.cardTypeId:'';
							var cardTypeName = deduct.cardTypeName!=null && deduct.cardTypeName!=''?deduct.cardTypeName:'';
							var deductMonth = deduct.deductMonth!=null && deduct.deductMonth!=''?deduct.deductMonth:'';
							var totalTimes = deduct.totalTimes!=null && deduct.totalTimes!=''?deduct.totalTimes:0;
							var countMoney = deduct.totalMoney;
							
							html+='<tr>';
							html+='<td>'+(i+1)+'</td>';
							html+='<td>'+baseName+'</td>';
							html+='<td>'+deductMonth+'</td>';
							html+='<td>'+cardTypeName+'</td>';
							html+='<td>'+totalTimes+'</td>';
							html+='<td>'+countMoney+'</td>';
							html+='<td><a class="layui-btn" onclick="summaryDetail(\''+baseId+'\',\''+deductMonth+'\',\''+cardTypeId+'\');">详情</a></td>';
							html+='</tr>';
							totalDays+=totalTimes;
							totalMoney+=countMoney;
                    	});
                    	html+='<tr>';
						html+='<td>合计</td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td>'+totalDays+'</td>';
						html+='<td>'+totalMoney+'</td>';
						html+='<td></td>';
						html+='</tr>';
                    	$('#baseDeductCardSummaryTypeList').append(html);
                    }
                },
                complete : function() {
                	layer.closeAll('loading');
                }
            });
		}
		
		dailyStatisticsLoad();
		
        // 搜索消费记录按钮
        $('#searchRechargeBtn').on('click', function(){
        	dailyStatisticsLoad();
        });

    });

</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="searchForm" class="layui-form layui-form-pane" action="">
	    	<div class="layui-inline">
		        <label class="layui-form-label">基地</label>
		        <div class="layui-input-inline">
		            <select id="baseId" name="baseId" lay-search>
						<option value="">请选择基地</option>
						#for(base : baseList)
							<option value="#(base.id)" #(base.id == baseId?'selected':'')>#(base.baseName)</option>
						#end
					</select>
		        </div>
    		</div>
	    	<div class="layui-inline">
		        <label class="layui-form-label">月份</label>
		        <div class="layui-input-inline">
		            <input type="text" id="deductMonth" name="deductMonth" class="layui-input" placeholder="请选择月份" value="#(deductMonth)" autocomplete="off">
		        </div>
    		</div>
	        <div class="layui-inline">
	        	<div class="layui-input-inline">
	        		<div class="layui-btn-group">
				        <button type="button" id="searchRechargeBtn" class="layui-btn">搜索</button>
				        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
	        		</div>
	        	</div>
    		</div>
		</form>
	</div>
	<div class="layui-row">
		<table class="layui-table">
			<thead>
				<tr>
					<th>序号</th>
					<th>基地名称</th>
					<th>扣卡月份</th>
					<th>卡类别</th>
					<th>扣卡天数</th>
					<th>参考金额</th>
					<th>操作</th>
				</tr> 
			</thead>
			<tbody id="baseDeductCardSummaryTypeList">
			</tbody>
		</table>
	</div>
</div>
#end