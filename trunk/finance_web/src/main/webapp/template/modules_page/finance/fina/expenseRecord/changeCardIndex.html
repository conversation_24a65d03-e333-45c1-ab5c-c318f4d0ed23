#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()分段换卡#end

#define css()
#end

#define content()
<div>
    <form class="layui-form" id="changeForm" style="width:90%;margin: 0 auto;">
        <div class="demoTable layui-row">
            <button class="layui-btn" style="float: left;padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" type="button" id="addBtn">添加时间段</button>
            <button class="layui-btn" style="float: left;padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;"  lay-submit="" id="saveBtn" lay-filter="saveBtn">保存</button>
            <input name="erId" type="hidden" id="erId" value="#(erId??)" >
            <input type="hidden" name="minDate" id="minDate" value="#(minDate??)"/>
            <input type="hidden" name="maxDate" id="maxDate" value="#(maxDate??)"/>
            <input type="hidden" id="showMinDate" value="#(showMinDate??)"/>
            <input type="hidden" id="showMaxDate" value="#(showMaxDate??)"/>
            <input name="count" id="count" type="hidden" value="0" >
            <input name="delIds" id="delIds" type="hidden" value="">
            <div style="width: 500px;float: left;padding-top: 22px;padding-left: 10px;">
                <p style="color: red;display: inline-block;">#(msg??)</p>
            </div>
        </div>
        <table class="layui-table" id="changeTable" >
            <thead>
            <tr>
                <th width="15%">会员卡号</th>
                <th width="15%">所属会员</th>
                <th width="15%">开始时间</th>
                <th width="15%">结束时间</th>
                <th width="30%">备注</th>
                <th width="10%">操作</th>
            </tr>
            </thead>
            <tbody id="changeTbody"></tbody>
        </table>
    </form>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table','laytpl','laydate','element'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laytpl=layui.laytpl,laydate=layui.laydate,element=layui.element;

        /*var delBtnTips;
        $('.delBtn').hover(function(){
            var that = this;
            delBtnTips = layer.tips('作废后保存成功才会生效', this,{tips:[3],time:0}); //在元素的事件回调体中，follow直接赋予this即可
        },function () {
            layer.close(delBtnTips);
        });*/

        layDateRender=function(id,index){
            laydate.render({
                elem:"#"+id,
                format: 'yyyy-MM-dd',
                trigger: 'click',
                done: function(value, date){
                    var nextIndex=$("#changeTr-"+index).next().attr("index");
                    $("#beginDate-"+nextIndex).val(value);
                }
            });
        }

        //循环渲染表格中已经存在行中的时间框，最后一个时间框不渲染
        for(var i=1;i<$("#count").val();i++){
            layDateRender("endDate-"+i,i);
        }


        $("#addBtn").on('click',function () {
            //获取最后一行的index
            var trLastIndex=$("#changeTbody").find("tr:last").attr("index");
            if(trLastIndex===undefined){
                trLastIndex=0;
            }
            var idx=(Number(trLastIndex)+1);
            var beginDate=$("#showMinDate").val();
            var endDate=$("#showMaxDate").val();
            if(idx>1){
                beginDate=$("#endDate-"+trLastIndex).val();
                endDate=$("#showMaxDate").val();
            }
            //table添加一行
            laytpl(changeTpl.innerHTML).render({"idx": idx,"beginDate":beginDate,"endDate":endDate}, function(html){
                $("#changeTbody").append(html);
                $("#count").val(idx);

                //添加一行渲染上一行的时间框
                layDateRender("endDate-"+(idx-1),(idx-1));
                $("#endDate-"+(idx-1)).prop('disabled',false);

            });
            form.render();
            return false;
        });


        del=function(id,trId,index) {
            var trLastIndex=$("#baseAdvanceBookSettingTbody").find("tr:last").attr("index");
            if(Number(index)===1){
                //删除的是第一条
                //见下一条的开始时间设置为01-01
                var nextIndex=$("#"+trId).next().attr("index");
                $("#beginDate-"+nextIndex).val($("#showMinDate").val());

            }else if(Number(index)===Number(trLastIndex)){

                //删除的是最后一条
                //将上一条的结束时间设置为12-31
                var prevIndex=$("#"+trId).prev().attr("index");
                $("#endDate-"+prevIndex).val($("#showMaxDate").val());

                //将上一个的时间框删除
                $("#endDate-"+prevIndex).prop('disabled',true);
            }else{
                //删除的是中间的一条
                //将下一条的开始时间设置为当前条的结束时间
                var beginDate=$("#beginDate-"+index).val();
                var nextIndex=$("#"+trId).next().attr("index");
                $("#beginDate-"+nextIndex).val(beginDate);
            }
            if(id!=''){
                var ids=$("#delIds").val();
                if(ids===''){
                    $("#delIds").val(id);
                }else{
                    $("#delIds").val(ids+","+id);
                }
            }
            $("#"+trId).remove();
        }




        form.on('submit(saveBtn)',function () {
            //无时间段不允许提交
            if($("#count").val() == null || $("#count").val() == undefined || $("#count").val() == 0){
                layer.msg('未添加时间段不允许保存', function () {});
                return false;
            }

            //list数量大于1判断最后一个的时间段是否相等
            var tableTr=$("#changeTbody").find("tr");
            if(tableTr.length>1){
                var tableTrLast=$("#changeTbody").find("tr").last();
                var id=$(tableTrLast).attr("id");
                var index=id.substring(id.indexOf("-")+1);
                var startDate=$("#beginDate-"+index).val();
                var endDate=$("#endDate-"+index).val();

                if(startDate == endDate){
                    layer.msg('最后一行开始时间不能等于结束时间', function () {});
                    return false;
                }
            }

            $("#saveBtn").attr("disabled",true);
            $("#saveBtn").addClass("layui-btn-disabled");
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/expenseRecord/changCard',
                data: $("#changeForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state==='ok'){
                        pop_close();
                    }
                },
                complete : function() {
                    $("#saveBtn").attr("disabled",false);
                    $("#saveBtn").removeClass("layui-btn-disabled");
                }
            });
            return false;
        });
    });
</script>
<script type="text/html" id="changeTpl">
    <tr id="changeTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="changeList[{{d.idx}}].cardNumber" lay-verify="required" class="layui-input" value=""></td>
        <td><input name="changeList[{{d.idx}}].fullName" lay-verify="required" class="layui-input" value=""></td>
        <td><input name="changeList[{{d.idx}}].beginDate" readonly class="layui-input beginDate" lay-verify="required" id="beginDate-{{d.idx}}" lay-verify="beginDate-{{d.idx}}"  value="{{d.beginDate}}"></td>
        <td><input name="changeList[{{d.idx}}].endDate" lay-verify="required"#[[ {{#if(d.idx>0){}} readonly value="{{d.endDate}}" {{#}}} class="layui-input endDate" id="endDate-{{d.idx}}" ]]#></td>
        <td><input name="changeList[{{d.idx}}].remark" class="layui-input" value=""></td>
        <td>
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('','changeTr-{{d.idx}}','{{d.idx}}')" >作废</button>
        </td>
    </tr>
</script>
#end