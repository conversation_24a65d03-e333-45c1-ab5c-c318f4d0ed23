#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()消费卡查询#end

#define css()
<style>
    .layui-table-cell {
        font-size:10px;
        padding:0 5px;
        height:auto;
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        word-break: break-all;
    }
</style>
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;
		layer.load();

        table.render({
            id : 'expendTable',
            elem : "#expendTable" ,
            url : '#(ctxPath)/fina/cardmanager/findExpendPage' ,
            height:'full-110',
            cols : [[
            	{type: 'numbers', width:70, title: '序号',unresize:true}
				,{field: 'cardNumber', title: '会员卡号',unresize:true,width: 120}
				,{field: 'fullName', title: '姓名',unresize:false,width:100}
				,{field: '', title: '合同号',unresize:false,width:100}
				,{field: 'openTime', title: '签订日期',unresize:true,width:170,templet: "<div>{{ dateFormat(d.openTime,'yyyy-MM-dd') }}</div>"}
				,{field: 'cardType', title: '卡类别',unresize:false}
				,{field: 'payAmount', title: '充值金额',unresize:false,width:100}
				,{field:'consumeTimes', title: '卡天数', width:120,align: 'center',unresize: false}
				,{field:'accountName', title: '收款帐号', width:120,align: 'center',unresize: false}
				,{field: 'describe', title: '卡说明',unresize:false,style:"text-align:left;font-size:10px;color:#000;"}
				,{field: '', title: '收据号',unresize:false,width:100}
				,{field: 'operatorName', title: '经办人',unresize:false,width:100}
				,{field: '', title: '备注',unresize:false,width:100}
            ]] ,
            page : true,
            limit : 10,
			done:function () {
				layer.closeAll('loading');
			}
        }) ;
        
		//重载表格
        pageTableReload = function () {
        	var param = {cardNumber:$('#cardNumber').val()};
        	tableReload('expendTable',param);
        }

        // 搜索消费记录按钮
        $('#searchRechargeBtn').on('click', function(){
			layer.load();
        	pageTableReload();
        });

        // 导出按钮
        $('#exportBtn').on('click', function(){
        	var param = {cardNumber:$('#cardNumber').val()};
        	tableReload('expendTable',param);
			if(table.cache.expendTable.length>0){
				var url='#(ctxPath)/fina/cardmanager/exportRecord?cardNumber='+$('#cardNumber').val();
				window.location.href=url;
			}else{
				layer.msg('没有数据,不能导出', {icon: 2, offset: 'auto'});
			}
        });
    });
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
	    <div class="layui-row" style="margin-top: 20px;">
	        <div class="layui-inline">
	            <label class="layui-form-label">会员卡号：</label>
	            <div class="layui-input-inline" style="width:150px;">
	                <input type="text" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
	            </div>
	        </div>
	        <button type="button" id="searchRechargeBtn" class="layui-btn">搜索</button>
	        #shiroHasPermission("finance:expend:export")
		        <button type="button" class="layui-btn" id="exportBtn">导出</button>
			#end
	    </div>
	    <div class="layui-row">
	        <table id="expendTable" lay-filter="expendTable"></table>
	    </div>
	</form>
</div>
#end