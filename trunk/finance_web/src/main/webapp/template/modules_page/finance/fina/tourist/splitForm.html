#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算管理#end

#define css()
<style>
    .my-skin .layui-layer-btn a {
        background-color: #009688;
        border: 1px solid #009688;
        color: #FFF;
    }
    .layui-table-cell {
        height: 28px;
        line-height: 28px;
        padding: 0 5px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <input id="expenseId" type="hidden" value="#(expenseRecord.id??)">
    <div class="layui-row">
        <button class="layui-btn" type="button" id="addBtn">添加</button>
        <p style="display: inline-block;" id="cardInfoP"></p>
    </div>
    <div class="layui-row">
        <table id="touristCheckinDetailTable" lay-filter="touristCheckinDetailTable"></table>
    </div>

    <div class="layui-form-footer" id="footer" #if(type??=='look') style="display: none;" #end>
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button  class="layui-btn" type="button"  id="saveBtn">保存</button>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
    #[[
    {{#if(d.isSettle=='0'){}}
        <a class="layui-btn layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#

</script>
<script type="text/html" id="inputTpl">
    #[[<div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualTimes" min="0" value="{{d.actualTimes}}"
                   lay-verify="checkNumber|required" onkeyup="calculateActualTimes(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_days' || d.isSettle=='1'){}} readonly {{#}}}>
        </div>
    </div>]]#
</script>
<script type="text/html" id="inputATpl">
    #[[<div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualAmount" min="0" value="{{d.actualAmount}}"
                   lay-verify="checkAmount|required" onkeyup="calculateActualAmount(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_money' || d.isSettle=='1'){}} readonly {{#}}}>
        </div>
    </div>]]#
</script>
<script type="text/html" id="inputPTpl">
    #[[<div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualPoints" min="0" value="{{d.actualPoints}}"
                   lay-verify="checkAmount|required" onkeyup="calculateActualPoints(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_points' || d.isSettle=='1'){}} readonly {{#}}}>
        </div>
    </div>]]#
</script>
<script type="text/html" id="inputITpl">
    #[[
    <div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualIntegrals" min="0" value="{{d.actualIntegrals}}"
                   lay-verify="checkAmount|required" onkeyup="calculateActualIntegrals(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_days' || d.isIntegral!='1' || d.isSettle=='1'){}} readonly {{#}}}>
        </div>
    </div>

    ]]#
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate = layui.laydate;



        function touristLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'touristCheckinDetailTable'
                ,elem : '#touristCheckinDetailTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-90'
                ,url : '#(ctxPath)/fina/tourist/touristRecordSettleDetailPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'id', title: 'id', align: 'center', unresize: true}
                    ,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
                    ,{field:'fullName', title: '卡主姓名', align: 'center', unresize: true}
                    ,{field:'withholdTimes', title: '锁定天数', sort: false, align: 'center', unresize: true,width:90,templet: "<div>{{ d.withholdTimes?d.withholdTimes + '天': '- -' }}</div>"}
                    ,{field:'actualTimes', title: '实扣天数', align: 'center',unresize: true,width:90,templet:'#inputTpl'}


                    ,{field:'withholdAmount', title: '锁定金额', sort: false, align: 'center', unresize: true,width:100,templet: "<div>{{ d.withholdAmount?d.withholdAmount + '元': '- -' }}</div>"}
                    ,{field:'actualAmount', title: '实扣金额', align: 'center',unresize: true,width:100,templet:'#inputATpl'}


                    ,{field:'withholdIntegrals', title: '锁定积分', sort: false, align: 'center', unresize: true,width:90,templet: "<div>{{ d.withholdIntegrals?d.withholdIntegrals + '积分': '- -' }}</div>"}
                    ,{field:'actualIntegrals', title: '实扣积分', align: 'center',unresize: true,width:90,templet:'#inputITpl'}
                    ,{field:'settleStatus', title: '状态', align: 'center', unresize: false,width:70,templet:"<div>{{d.isSettle == '0'?'<span class='layui-badge layui-bg-green'>未结算</span>':d.isSettle == '1'?'<span class='layui-badge'>已结算</span>':d.isSettle == '2'?'<span class='layui-badge layui-bg-cyan'>- -</span>':'- -'}}</div>"}

                    ,{fixed: 'right',title: '操作', width: 230, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : false
                ,limit : 10
                ,limits : [10,20,30,40]
                , done: function (res, curr, count) {
                    $("[data-field='id']").css('display', 'none');    //隐藏
                    layer.close(loadingIndex);
                }
            });

        };

        touristLoad({"expenseId":$("#expenseId").val()});

        table.on('tool(touristCheckinDetailTable)',function(obj) {
            if(obj.event === 'del'){
                layer.confirm("确定要作废吗?",{
                    skin : "my-skin"
                },function(index) {
                    layer.load();
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/fina/tourist/touristRecordSettleDetailDel',
                        data: {'id':obj.data.id,'expenseId':$("#expenseId").val()},
                        notice: true,
                        loadFlag: true,
                        success: function (rep) {
                            if (rep.state == 'ok') {
                                touristLoad({"expenseId":$("#expenseId").val()});
                                parent.reloadTable();
                            }
                        },
                        complete: function () {
                            layer.closeAll('loading');
                        }
                    });
                    layer.close(index);
                });
            }
        });

        $("#saveBtn").on('click',function () {

            let flag=false;
            let msg="";
            let data=[];
            $.each($(".layui-table-main tbody tr"),function (index,item) {
                let id;
                let cardNumber;
                let actualTimes;
                let actualAmount;
                let actualIntegrals;
                if($(item).attr("data-index")!=undefined){
                    id=$(item).children('td[data-field="id"]').children("div").text();
                    cardNumber=$(item).children('td[data-field="cardNumber"]').children("div").text();
                    actualTimes=$(item).children('td[data-field="actualTimes"]').find('input[name="actualTimes"]').val();
                    actualAmount=$(item).children('td[data-field="actualAmount"]').find('input[name="actualAmount"]').val();
                    actualIntegrals=$(item).children('td[data-field="actualIntegrals"]').find('input[name="actualIntegrals"]').val();
                }else{
                    id=""
                    cardNumber=$(item).find("input[name='cardNumber']").val();
                    actualTimes=$(item).find('input[name="actualTimes"]').val();
                    actualAmount=$(item).find('input[name="actualAmount"]').val();
                    actualIntegrals=$(item).find('input[name="actualIntegrals"]').val();
                }
                if(cardNumber=='' || cardNumber==undefined){
                    flag=true;
                    msg="会员卡不能为空";
                    return false;
                }
                if((actualTimes=='' || actualTimes==undefined) && (actualAmount=='' || actualAmount==undefined) && (actualIntegrals=='' || actualIntegrals==undefined)){
                    flag=true;
                    msg="请输入实扣值";
                    return false;
                }
                data.push({'id':id,'cardNumber':cardNumber,'actualTimes':actualTimes,'actualAmount':actualAmount,'actualIntegrals':actualIntegrals})
            });
            if(flag){
                layer.msg(msg, {icon: 2, offset: 'auto'});
                return false;
            }

            util.sendAjax({
                type: 'POST',
                url: '#(ctxPath)/fina/tourist/touristRecordSettleDetailSave',
                data: {'expenseId':$("#expenseId").val(),"data":JSON.stringify(data)},
                notice: true,
                loadFlag: true,
                success: function (rep) {
                    if (rep.state == 'ok') {
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete: function () {
                    layer.closeAll('loading');
                }
            });



            return false;
        })

        $("#addBtn").on('click',function () {
            $(".layui-table-main tbody").append("<tr>" +
                "<td><div class=\"layui-input-inline\">\n" +
                "            <input type=\"text\" name=\"cardNumber\" min=\"0\" value=\"\"\n" +
                "                   lay-verify=\"required\" autocomplete=\"off\"  onblur=\"getCardInfo(this)\" class=\"layui-input\"  >\n" +
                "        </div></td>" +
                "<td align='center'></td>" +
                "<td></td>" +
                "<td><div class=\"layui-inline\" >\n" +
                "        <div class=\"layui-input-inline\">\n" +
                "            <input type=\"text\" name=\"actualTimes\" min=\"0\" value=\"\"\n" +
                "                   lay-verify=\"required\" autocomplete=\"off\"  class=\"layui-input\"  >\n" +
                "        </div>\n" +
                "    </div></td>" +
                "<td></td>" +
                "<td><div class=\"layui-inline\" >\n" +
                "        <div class=\"layui-input-inline\">\n" +
                "            <input type=\"text\" name=\"actualAmount\" min=\"0\" value=\"\"\n" +
                "                   lay-verify=\"required\" autocomplete=\"off\"  class=\"layui-input\"  >\n" +
                "        </div>\n" +
                "    </div></td>" +
                "<td></td>" +
                "<td><div class=\"layui-inline\" >\n" +
                "        <div class=\"layui-input-inline\">\n" +
                "            <input type=\"text\" name=\"actualIntegrals\" min=\"0\" value=\"\"\n" +
                "                   lay-verify=\"required\"  autocomplete=\"off\" class=\"layui-input\"  >\n" +
                "        </div>\n" +
                "    </div></td>" +
                "<td></td>" +
                "<td align=\"center\"><a class=\"layui-btn layui-btn-xs\"  onclick='delTr(this)'>作废</a></td>" +
                "</tr>");

        });

        getCardInfo=function(obj){
            layer.load();
            let cardNumber =$(obj).val();
            $.post("#(ctxPath)/fina/cardmanager/getCardByCardNumber?cardNumber="+cardNumber+"&delFlag=0",function (d) {
                if(d.state==='ok'){
                    $(obj).closest('td').next().text(d.data.fullName);
                    let cardNumberInfo=cardNumber;
                    if(d.data.deductWay=="deduct_by_days"){
                        cardNumberInfo+="可用天数："+d.data.consumeTimes+"；"
                    }
                    if(d.data.deductWay=="deduct_by_money"){
                        cardNumberInfo+="可用余额："+d.data.balance+"；"
                    }
                    if(d.data.isIntegral=="1"){
                        cardNumberInfo+="可用积分："+d.data.cardIntegrals+"；"
                    }

                    if(d.data.deductWay!="deduct_by_days"){
                        //禁用天数框
                        let actualTimes=$(obj).closest('tr').find("input[name='actualTimes']");
                        actualTimes.prop("readonly",true);
                        actualTimes.addClass("layui-disabled");

                    }
                    if(d.data.deductWay!="deduct_by_money"){
                        //禁用天数框
                        let actualAmountInput=$(obj).closest('tr').find("input[name='actualAmount']");
                        actualAmountInput.prop("readonly",true);
                        actualAmountInput.addClass("layui-disabled");

                    }
                    if(d.data.isIntegral!="1"){
                        //禁用积分框
                        let actualIntegrals=$(obj).closest('tr').find("input[name='actualIntegrals']");
                        actualIntegrals.prop("readonly",true);
                        actualIntegrals.addClass("layui-disabled");
                    }

                    $("#cardInfoP").text(cardNumberInfo);
                    layer.closeAll('loading');
                }else{
                    layer.msg(d.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll('loading');
                }
            })
        }

        delTr=function(obj){
            $(obj).parents("tr").remove();
        }
       /* //获取所填实扣天数
        calculateActualTimes=function(obj){
            var actualTimesUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualTimes:actualTimesUp};
            rowUpdate('touristExpenseTable',index,data);
        }

        //获取所填实扣金额
        calculateActualAmount=function(obj){
            var actualAmountUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualAmount:actualAmountUp};
            rowUpdate('touristExpenseTable',index,data);
        }
        calculateActualPoints=function(obj){
            var actualPointsUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualPoints:actualPointsUp};
            rowUpdate('touristExpenseTable',index,data);
        }
        calculateActualIntegrals=function(obj){
            var actualIntegralsUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualIntegrals:actualIntegralsUp};
            rowUpdate('touristExpenseTable',index,data);
        }*/

    });
</script>
#end