#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡管理#end

#define css()
<style>
    .layui-inline{
        margin-top: 30px;
    }
    label.layui-form-label{
        width:auto;
    }

    .layui-btn+.layui-btn{
        margin-left: 5px;
    }
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form','upload','laydate'],function(){
        table = layui.table
            , form = layui.form
            , $ = layui.$
            , rotateNum = 0
            ,laydate=layui.laydate
        ;
        var upload = layui.upload;


        //年月选择器
        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
        });

        function managerLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                elem: '#cardTable'
                ,url : '#(ctxPath)/fina/recharge/findCardTransactionsRecordList'
                ,method : 'POST'
                ,where : data
                ,height:'full-90'
                ,cols: [[
                    /*{rowspan: 2,field: 'card_number',align: 'center', title: '会员卡号',unresize:true,width: 120}
                    ,{rowspan: 2,field: '',align: 'center', title: '卡类别类型',unresize:false,width: 100, templet: '#dictTpl("type_category", "type_category")'}
                    ,{rowspan: 2,field: 'card_type',align: 'center', title: '会员卡类别',unresize:false,width: 180}
                    ,{rowspan: 2,field: 'full_name',align: 'center', title: '客户名',unresize:false,width:95}*/
                    {rowspan: 2,field: 'deal_time',align: 'center', title: '操作时间',unresize:false,width:150,templet:'<div>{{dateFormat(d.deal_time,"yyyy-MM-dd HH:mm:ss")}}</div>'}
                    ,{rowspan: 2,field: 'describe',align: 'center', title: '摘要',unresize:false}
                    ,{field:'consumeTimes3', title: '充值数', width:110,align: 'center',unresize: false, colspan: 4}
                    ,{field:'consumeTimes4', title: '消费数', width:110,align: 'center',unresize: false, colspan: 4}
                    ,{field:'consumeTimes5', title: '结存', width:110,align: 'center',unresize: false, colspan: 4}
                ],
                    [

                        { title: '天数', field: 'recharge_times', width:90},
                        { title: '金额', field: 'recharge_amount', width:90},
                        { title: '积分', field: 'recharge_integrals', width:90},
                        { title: '豆豆券', field: 'recharge_bean_coupons', width:90},

                        { title: '天数', field: 'consume_times', width:90},
                        { title: '金额', field: 'consume_amount', width:90},
                        { title: '积分', field: 'consume_integrals', width:90},
                        { title: '豆豆券', field: 'consume_bean_coupons', width:90},

                        { title: '天数', field: 'times_snapshot', width:100},
                        { title: '金额', field: 'amount_snapshot', width:100},
                        { title: '积分', field: 'integrals_snapshot', width:100},
                        { title: '豆豆券', field: 'bean_coupons_snapshot', width:100}
                    ]

                ],
                id:'cardTable',
                page : true,
                limit : 10,
                limits: [10,20,30]
                ,done: function(res, curr, count){
                    layer.close(loadingIndex);
                }
            }) ;
        };
        function managerLoad2(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                elem: '#cardTable'
                ,url : '#(ctxPath)/fina/recharge/findCardTransactionsRecordListByCardType'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.75
                ,cols: [[
                    /*{rowspan: 2,field: 'card_number',align: 'center', title: '会员卡号',unresize:true,width: 120}
                    ,{rowspan: 2,field: '',align: 'center', title: '卡类别类型',unresize:false,width: 100, templet: '#dictTpl("type_category", "type_category")'}
                    ,{rowspan: 2,field: 'card_type',align: 'center', title: '会员卡类别',unresize:false,width: 180}
                    ,{rowspan: 2,field: 'full_name',align: 'center', title: '客户名',unresize:false,width:95}*/
                    { rowspan: 2,title: '会员卡号', field: 'card_number', width:100}
                    ,{ rowspan: 2,title: '持卡人', field: 'full_name', width:90}
                    ,{rowspan: 2,field: 'deal_time',align: 'center', title: '操作时间',unresize:false,width:150,templet:'<div>{{dateFormat(d.deal_time,"yyyy-MM-dd HH:mm:ss")}}</div>'}
                    ,{rowspan: 2,field: 'describe',align: 'center', title: '摘要',unresize:false}
                    ,{field:'consumeTimes3', title: '充值数', width:110,align: 'center',unresize: false, colspan: 4}
                    ,{field:'consumeTimes4', title: '消费数', width:110,align: 'center',unresize: false, colspan: 4}
                    ,{field:'consumeTimes5', title: '结存', width:110,align: 'center',unresize: false, colspan: 4}
                ],
                    [

                        { title: '天数', field: 'recharge_times', width:90},
                        { title: '金额', field: 'recharge_amount', width:90},
                        { title: '积分', field: 'recharge_integrals', width:90},
                        { title: '豆豆券', field: 'recharge_bean_coupons', width:90},

                        { title: '天数', field: 'consume_times', width:90},
                        { title: '金额', field: 'consume_amount', width:90},
                        { title: '积分', field: 'consume_integrals', width:90},
                        { title: '豆豆券', field: 'consume_bean_coupons', width:90},

                        { title: '天数', field: 'times_snapshot', width:100},
                        { title: '金额', field: 'amount_snapshot', width:100},
                        { title: '积分', field: 'integrals_snapshot', width:100},
                        { title: '豆豆券', field: 'bean_coupons_snapshot', width:100}
                    ]

                ],
                id:'cardTable',
                page : true,
                limit : 10,
                limits: [10,20,30]
                ,done: function(res, curr, count){
                    layer.close(loadingIndex);
                }
            }) ;
        };
        function managerLoad3(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                elem: '#cardTable'
                ,url : '#(ctxPath)/fina/recharge/findCardTransactionsRecordListByBaseId'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.75
                ,cols: [[
                    {rowspan: 2,field: 'card_number',align: 'center', title: '会员卡号',unresize:true,width: 120}
                    ,{rowspan: 2,field: 'card_type',align: 'center', title: '会员卡类别',unresize:false,width: 180}
                    ,{rowspan: 2,field: 'full_name',align: 'center', title: '持卡人',unresize:false,width:95}
                    ,{rowspan: 2,field: 'deal_time',align: 'center', title: '操作时间',unresize:false,width:150,templet:'<div>{{dateFormat(d.deal_time,"yyyy-MM-dd HH:mm:ss")}}</div>'}
                    ,{rowspan: 2,field: 'describe',align: 'center', title: '摘要',unresize:false}
                    ,{field:'consumeTimes3', title: '充值数', width:110,align: 'center',unresize: false, colspan: 4}
                    ,{field:'consumeTimes4', title: '消费数', width:110,align: 'center',unresize: false, colspan: 4}
                    ,{field:'consumeTimes5', title: '结存', width:110,align: 'center',unresize: false, colspan: 4}
                ],
                    [

                        { title: '天数', field: 'recharge_times', width:70},
                        { title: '金额', field: 'recharge_amount', width:70},
                        { title: '积分', field: 'recharge_integrals', width:70},
                        { title: '豆豆券', field: 'recharge_bean_coupons', width:70},

                        { title: '天数', field: 'consume_times', width:70},
                        { title: '金额', field: 'consume_amount', width:70},
                        { title: '积分', field: 'consume_integrals', width:70},
                        { title: '豆豆券', field: 'consume_bean_coupons', width:70},

                        { title: '天数', field: 'times_snapshot', width:90},
                        { title: '金额', field: 'amount_snapshot', width:90},
                        { title: '积分', field: 'integrals_snapshot', width:90},
                        { title: '豆豆券', field: 'bean_coupons_snapshot', width:90}
                    ]

                ],
                id:'cardTable',
                page : true,
                limit : 10,
                limits: [10,20,30]
                ,done: function(res, curr, count){
                    layer.close(loadingIndex);
                }
            }) ;
        };

        managerLoad({delFlag:'0'});

        form.on("submit(search)",function(data){
            var queryType=$("#queryType").val();
            if(queryType=="1"){
                var cardNumber=$("#cardNumber").val();
                if(cardNumber==''){
                    layer.msg('请输入要查询的会员卡号', {icon: 2, offset: 'auto'});
                    return false;
                }
                managerLoad({'cardNumber':cardNumber});
            }else if(queryType=="2"){
                var cardTypeId=$("#cardTypeId").val();
                if(cardTypeId==''){
                    layer.msg('请选择要查询的会员卡类别', {icon: 2, offset: 'auto'});
                    return false;
                }
                managerLoad2({'cardTypeId':cardTypeId});
            }else if(queryType=="3"){
                var baseId=$("#baseId").val();
                if(baseId==''){
                    layer.msg('请选择要查询的基地', {icon: 2, offset: 'auto'});
                    return false;
                }
                managerLoad3({'baseId':baseId});
            }
            return false;
        });

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#search').click();
                return false
            }
        }

        $("#export").on("click",function () {
            var url = "#(ctx)/sys/upload/excelExport";
            var eleLink = document.createElement('a');
            eleLink.style.display = 'none';
            eleLink.href = url;
            // 触发点击
            document.body.appendChild(eleLink);
            eleLink.click();
            // 然后移除
            document.body.removeChild(eleLink);
        });

        $("#exportData").on("click",function () {
            var referencePrice = '';
            if($('input[name="referencePrice"]').prop('checked')){
                referencePrice = '0';
            }else{
                referencePrice = '';
            }
            if(table.cache.cardTable.length>0){
                var url='#(ctxPath)/fina/cardmanager/exportData?cardTypeId='+$('#cardTypeId').val()+"&cardNumber="+$('#cardNumber').val()+"&fullName="+$('#name').val()+"&idcard="+$('#idcard').val()+"&orderBy="+$('#orderBy').val()+"&referencePrice="+referencePrice;
                window.location.href=url;
            }else{
                layer.msg('导出的内容为空', {icon: 2, offset: 'auto'});
            }
        });

        $("#exportBtn").on('click',function () {
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '导出中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            var url='#(ctxPath)/fina/cardmanager/cardMonthBalanceExport?cardNumber='+$('#cardNumber').val()+"&yearMonth="+$('#yearMonth').val();
            window.location.href=url;
            setTimeout(function(){
                layer.close(loadingIndex);
            },5000);
        })

        form.on('select(queryType)',function (obj) {
            if(obj.value=="1"){
                $("#cardNumberDiv").css("display","inline-block");
                $("#baseDiv").css("display","none");
                $("#cardTypeDiv").css("display","none");
            }else if(obj.value=="2"){
                $("#cardNumberDiv").css("display","none");
                $("#baseDiv").css("display","none");
                $("#cardTypeDiv").css("display","inline-block");
            }else if(obj.value=="3"){
                $("#cardNumberDiv").css("display","none");
                $("#baseDiv").css("display","inline-block");
                $("#cardTypeDiv").css("display","none");
            }
        })

    });

</script>
<script type="text/html" id="toolBar">

</script>
#end

#define content()
<div class="my-btn-box" style="margin-top:0px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
        <div class="layui-row">
            <div class="layui-inline">
                <label class="layui-form-label">查询类型</label>
                <div class="layui-input-inline">
                    <select id="queryType" lay-filter="queryType">
                        <option value="1">按会员卡号查询</option>
                        <option value="2">按会员卡类型查询</option>
                        <option value="3">按基地查询</option>
                    </select>
                </div>
            </div>
            <!--<div class="layui-inline" >
                <label class="layui-form-label">卡类别类型</label>
                <div class="layui-input-inline">
                    <select id="typeCategory" name="typeCategory">
                        <option value="">全部</option>
                        #dictOption("type_category", type.typeCategory??'', "")
                    </select>
                </div>
            </div>-->
            <div class="layui-inline" id="cardTypeDiv" style="display: none">
                <label class="layui-form-label">会员卡类别</label>
                <div class="layui-input-inline">
                    <select id="cardTypeId" name="cardTypeId" lay-search>
                        <option value="">请选择卡类别</option>
                        #for(t : typeList)
                        <option value="#(t.id)" #(card != null ? (t.id == card.card_type_id?'selected':'') :'')>#(t.cardType)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline" id="cardNumberDiv">
                <label class="layui-form-label">会员卡号</label>
                <div class="layui-input-inline">
                    <input type="text" name="cardNumber" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline" id="baseDiv" style="display: none">
                <label class="layui-form-label">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="baseId" lay-search>
                        <option value="">请选择基地</option>
                        #for(t : baseList)
                        <option value="#(t.id)" >#(t.baseName)</option>
                        #end
                    </select>
                </div>
            </div>
            <!--<div class="layui-inline">
                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline">
                    <input type="text" name="yearMonth" id="yearMonth" value="#(yearMonth??)" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>-->
            <div class="layui-inline">
                <button type="button" id="search" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
                #shiroHasPermission("finance:cardBalance:export")
                <!--<button class="layui-btn" type="button" id="exportBtn" >导出</button>-->
                #end
            </div>
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="cardTable" lay-filter="cardTableFilter"></table>
    </div>
</div>
#getDictLabel("gender")
#end