#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">


        </div>
    </form>
    <div class="layui-row">
        <input type="hidden" id="roomId" value="#(roomId??)">
        <input type="hidden" id="leaseId" value="#(leaseId??)">
        <table id="cardTypeTable" lay-filter="cardTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">

        <a class="layui-btn layui-btn-xs" lay-event="edit">付款单</a>

    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate'],function(){
        var table = layui.table
            , $ = layui.$
        ;

        cardTypeTableReload=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTable',
                elem:"#cardTypeTable",
                url:'#(ctxPath)/fina/leaseRecordApply/leaseRoomPaymentPageList',
                where:data,
                height:'full-90',
                cols:[[
                    {type:'numbers',title:'序号',width:80,unresize:true}
                    ,{field: 'order_no', title: '订单号' ,unresize:true}
                    ,{field: 'amount', title: '金额' ,unresize:true}
                    ,{field:'start_date', title:'开始时间' , unresize:true, align:'center',templet:"<div>{{ dateFormat(d.start_date,'yyyy-MM-dd') }}</div>"}
                    ,{field:'end_date', title:'结束时间'  , unresize:true, align:'center',templet:"<div>{{ dateFormat(d.end_date,'yyyy-MM-dd') }}</div>"}
                    ,{field:'need_pay_date', title:'期望支付日期'  , unresize:true, align:'center',templet:"<div>{{ dateFormat(d.need_pay_date,'yyyy-MM-dd') }}</div>"}
                    ,{field: '' , title: '状态',unresize:true,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge layui-bg-orange">未到付款时间</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">已付款</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge">待付款</span>';
                            }
                        }}
                ]],
                page:false,
                limit:10
                , done: function (res, curr, count) {


                    layer.close(loadingIndex);
                }
            });
            table.on('tool(cardTypeTable)',function(obj){
                if(obj.event === 'set'){
                }else if(obj.event === 'edit'){
                    layerShow('编辑','#(ctxPath)/fina/leaseRecordApply/form?id='+obj.data.id,'100%','100%');

                }
            });
        }

        cardTypeTableReload({'leaseId':$("#leaseId").val(),'roomId':$("#roomId").val()});




    });
</script>
#end