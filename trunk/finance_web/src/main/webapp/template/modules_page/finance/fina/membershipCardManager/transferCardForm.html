#include("/template/common/layout/_page_layout.html") 
#@layout() 

#define pageTitle()转移会员卡功能#end

<!-- 公共Css文件 -->
#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css" />
<style>
.co {
	color: #ff0000;
}
</style>
#end 

#define content()
<div class="my-btn-box">
<form id="transferForm" class="layui-form layui-form-pane" action="" lay-filter="transferForm" method="post">
	<div class="layui-row">
		<fieldset class="layui-elem-field">
			<legend>转移信息</legend>
			<div class="layui-field-box">
				<div class="layui-row">
					<div class="layui-inline">
						<label class="layui-form-label"><span class="co">*</span>转移类型</label>
						<div class="layui-input-inline" style="width:100px;">
							<select id="transferType" name="transfer.type" lay-verify="required" lay-filter="transferTypeFilter">
								<option value="0">过户</option>
								<option value="1">升级</option>
							</select>
						</div>
					</div>
<!-- 					<div id="upgradeCountType" class="layui-inline" style="display:none;"> -->
<!-- 						<label class="layui-form-label"><span class="co">*</span>计算类型</label> -->
<!-- 						<div class="layui-input-inline" style="width:200px;"> -->
<!-- 							<select name="transfer.upgradeCountType"> -->
<!-- 								<option value="0">旧卡和补款数据</option> -->
<!-- 								<option value="1">旧卡、补款和新卡合同数据</option> -->
<!-- 							</select> -->
<!-- 						</div> -->
<!-- 					</div> -->
					<div class="layui-inline">
						<label class="layui-form-label"><span class="co">*</span>过户费</label>
						<div class="layui-input-inline" style="width:100px;">
							<input type="text" id="transferFee" name="transfer.transferFee" value="200.0" lay-verify="required" placeholder="请输入过户费" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label"><span class="co">*</span>遗失费</label>
						<div class="layui-input-inline" style="width:100px;">
							<input type="text" name="transfer.lossFee" value="0.0" lay-verify="required" placeholder="请输入遗失费" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label"><span class="co">*</span>补款金额</label>
						<div class="layui-input-inline" style="width:150px;">
							<input type="text" name="transfer.payAmount" value="0.0" lay-verify="required" placeholder="请输入补款金额" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label"><span class="co">*</span>获得天数</label>
						<div class="layui-input-inline" style="width:100px;">
							<input type="text" name="transfer.getDays" value="0.0" lay-verify="required" placeholder="请输入获得天数" autocomplete="off" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label"><span class="co">*</span>获得金额</label>
						<div class="layui-input-inline" style="width:100px;">
							<input type="text" name="transfer.getAmount" value="0.0" lay-verify="required" placeholder="请输入获得金额" autocomplete="off" class="layui-input">
						</div>
					</div>
<!-- 					<div class="layui-inline"> -->
<!-- 						<label class="layui-form-label"><span class="co">*</span>旧卡金额</label> -->
<!-- 						<div class="layui-input-inline"> -->
<!-- 							<input type="text" name="transfer.oldCardAmount" value="#(oldCard.balance??0.0)" lay-verify="required" placeholder="请输入旧卡金额" autocomplete="off" class="layui-input"> -->
<!-- 						</div> -->
<!-- 					</div> -->
<!-- 					<div class="layui-inline"> -->
<!-- 						<label class="layui-form-label"><span class="co">*</span>旧卡天数</label> -->
<!-- 						<div class="layui-input-inline"> -->
<!-- 							<input type="text" name="transfer.oldCardDays" value="#(oldCard.consumeTimes??0.0)" lay-verify="required" placeholder="请输入旧卡天数" autocomplete="off" class="layui-input"> -->
<!-- 						</div> -->
<!-- 					</div> -->
<!-- 					<div class="layui-inline"> -->
<!-- 						<label class="layui-form-label"><span class="co">*</span>旧卡点数</label> -->
<!-- 						<div class="layui-input-inline"> -->
<!-- 							<input type="text" name="transfer.oldCardPoints" value="#(oldCard.consumePoints??0.0)" lay-verify="required" placeholder="请输入旧卡点数" autocomplete="off" class="layui-input"> -->
<!-- 						</div> -->
<!-- 					</div> -->
<!-- 					<div class="layui-inline"> -->
<!-- 						<label class="layui-form-label"><span class="co">*</span>旧卡积分</label> -->
<!-- 						<div class="layui-input-inline"> -->
<!-- 							<input type="text" name="transfer.oldCardIntegrals" value="#(oldCard.cardIntegrals??0.0)" lay-verify="required" placeholder="请输入旧卡积分" autocomplete="off" class="layui-input"> -->
<!-- 						</div> -->
<!-- 					</div> -->
<!-- 					<div class="layui-inline"> -->
<!-- 						<label class="layui-form-label"><span class="co">*</span>旧卡豆豆券</label> -->
<!-- 						<div class="layui-input-inline"> -->
<!-- 							<input type="text" name="transfer.oldCardPeas" value="#(oldCard.beanCoupons??0.0)" lay-verify="required" placeholder="请输入旧卡豆豆券" autocomplete="off" class="layui-input"> -->
<!-- 						</div> -->
<!-- 					</div> -->
				</div>
				<div class="layui-row">
					<table class="layui-table">
						<thead>
						<tr>
							<th width="8%">旧卡号</th>
							<th width="7%">剩余金额</th>
							<th width="7%">购买金额</th>
							<th width="7%">赠送金额</th>
							<th width="7%">剩余天数</th>
							<th width="7%">购买天数</th>
							<th width="7%">赠送天数</th>
							<th width="7%">剩余积分</th>
							<th width="7%">帐面余额</th>
							<th width="7%">转购买天数</th>
							<th width="7%">转赠送天数</th>
							<th width="7%">转购买金额</th>
							<th width="7%">转赠送金额</th>
							<th width="8%">操作</th>
						</tr>
						</thead>
						<tbody id="transferDetail"></tbody>
					</table>
				</div>
			</div>
		</fieldset>
	</div>
	<div class="layui-row">
		<!--旧卡-->
		<div class="layui-col-md6">
			<fieldset class="layui-elem-field">
				<legend>原会员卡信息</legend>
				<div class="layui-field-box">
					<div id="cardNumberTab" class="layui-tab layui-tab-card" lay-filter="cardNumberTab">
						<ul class="layui-tab-title">
						</ul>
						<div class="layui-tab-content">
						</div>
					</div>
				</div>
			</fieldset>
		</div>
		<div class="layui-col-md6">
			<!--新卡-->
			<div class="layui-row">
				<fieldset class="layui-elem-field">
					<legend>新会员卡信息</legend>
					<div class="layui-field-box">
						<div class="layui-row">
							<label class="layui-form-label"><span class="co">*</span>卡类别</label>
							<div class="layui-input-block">
								<select id="newCardTypeId" name="newCard.cardTypeId" lay-verify="required" lay-filter="cardTypeFilter" lay-search>
									<option value="">请选择卡类别</option> 
									#for(t : typeList)
										<option value="#(t.id)">#(t.cardType)</option> 
									#end
								</select>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>开卡时间</label>
								<div class="layui-input-inline">
									<input type="text" id="newOpenTime" name="newCard.openTime" value="" lay-verify="required" placeholder="请输入开卡时间" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>会员卡号</label>
								<div class="layui-input-inline">
									<input type="text" id="newCardNumber" name="newCard.cardNumber" value="" lay-verify="required" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>合同类型</label>
								<div class="layui-input-inline" style="width:182px;">
									<select id="newContractType" name="newCard.contractType" lay-verify="required">
					                    <option value="">请选择合同类型</option>
					                    #for(contractType : contractTypeList)
					                    <option value="#(contractType.id)">#(contractType.typeName)</option>
					                    #end
					                </select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">合同编号</label>
								<div class="layui-input-inline">
									<input type="text" id="newContractNumber" name="newCard.contractNumber" value="" placeholder="请输入合同编号" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">销售人员</label>
								<div class="layui-input-inline" style="width:410px;">
									<input type="hidden" id="operator" name="newCard.operator" value=""/>
	                				<input type="text" id="operatorName" class="layui-input" value="" placeholder="请选择销售人员" readonly>
								</div>
							</div>
							<div class="layui-inline">
								<div class="layui-input-inline">
	                				<input type="button" id="chooseUser" class="layui-btn" value="选择"/>
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">分公司</label>
					            <div class="layui-input-inline" style="width:182px;">
					                <select id="newBranchOfficeId" name="newCard.branchOfficeId" lay-search>
					                    <option value="">请选择分公司</option>
					                    #for(branchOffice : branchOfficeList)
					                    <option value="#(branchOffice.id)">#(branchOffice.shortName??)</option>
					                    #end
					                </select>
					            </div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">销售部门</label>
					            <div class="layui-input-inline" style="width:182px;">
					                <input type="hidden" id="salesDeptIdValue" name="newCard.salesDeptId" value="">
					                <div id="salesDeptId" class="xm-select-demo" hiddenTargetId="salesDeptIdValue" dataValue="#(oldCard.salesDeptId??)"></div>
					            </div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>合同金额</label>
								<div class="layui-input-inline">
									<input type="text" id="newPurchaseCardBalance" name="newCard.purchaseCardBalance" value="0.0" placeholder="请输入合同金额" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>合同天数</label>
								<div class="layui-input-inline">
									<input type="text" id="newContractTimes" name="newCard.contractTimes" value="0.0" placeholder="请输入合同天数" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>实收金额</label>
								<div class="layui-input-inline">
									<input type="text" id="newCollectAmount" name="newCard.collectAmount" value="0.0" placeholder="请输入实收金额" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>赠送天数</label>
								<div class="layui-input-inline">
									<input type="text" id="giveRechargeDays" name="newCard.giveRechargeDays" value="0.0" placeholder="请输入合同赠送天数" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>折扣率(%)</label>
								<div class="layui-input-inline">
									<input type="text" id="newContractDiscount" name="newCard.contractDiscount" value="0.0" placeholder="请输入折扣率" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co"></span>总计天数</label>
								<div class="layui-input-inline">
									<input type="text" id="newTotalDays" value="" placeholder="请输入总计天数" autocomplete="off" class="layui-input" readonly="readonly">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>计划售价</label>
								<div class="layui-input-inline">
									<input type="text" id="newPrice" name="newCard.price" value="0.0" placeholder="请输入计划售价" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>实际售价</label>
								<div class="layui-input-inline">
									<input type="text" id="newReferencePrice" name="newCard.referencePrice" value="0.0" placeholder="请输入实际售价" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div id="balanceDiv" class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>首次金额</label>
								<div class="layui-input-inline">
									<input type="text" id="newBalance" name="newCard.balance" value="0.0" placeholder="请输入首次金额" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div id="consumeTimesDiv" class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>首次天数</label>
								<div class="layui-input-inline">
									<input type="text" id="newConsumeTimes" name="newCard.consumeTimes" value="0.0" placeholder="请输入首次天数" lay-verify="checkAmount|required" autocomplete="off" class="layui-input">
								</div>
							</div>
						</div>
<!--						<div class="layui-row">-->
<!--							<div class="layui-inline">-->
<!--								<label class="layui-form-label">购买金额</label>-->
<!--								<div class="layui-input-inline">-->
<!--									<input type="text" id="buyRechargeAmount" name="newCard.buyRechargeAmount"-->
<!--										   class="layui-input" value="0.0" placeholder="请输入购买金额">-->
<!--								</div>-->
<!--							</div>-->
<!--							<div class="layui-inline">-->
<!--								<label class="layui-form-label">购买天数</label>-->
<!--								<div class="layui-input-inline">-->
<!--									<input type="text" id="buyRechargeDays" name="newCard.buyRechargeDays"-->
<!--										   class="layui-input" value="0.0" placeholder="请输入购买天数">-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">赠送金额</label>
								<div class="layui-input-inline">
									<input type="text" id="giveRechargeAmount" name="newCard.giveRechargeAmount"
										   class="layui-input" value="0.0" placeholder="请输入赠送金额">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">赠送积分</label>
								<div class="layui-input-inline">
									<input type="text" id="giveRechargeIntegrals" name="newCard.giveRechargeIntegrals"
										   class="layui-input" value="0.0" placeholder="请输入赠送积分">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label" style="padding: 8px 5px;">旅居扣费方案</label>
								<div class="layui-input-inline" style="width:182px;">
									<select id="newDeductSchemeId" name="newCard.deductSchemeId"
										lay-search>
										<option value="">请选择旅居扣费方案</option> 
										#for(deduct : sojournSchemeList)
											<option value="#(deduct.id)" #if(deduct.id==oldCard.deductSchemeId??) selected #end>#(deduct.name)</option> 
										#end
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label" style="padding: 8px 5px;">长住扣费方案</label>
								<div class="layui-input-inline" style="width:182px;">
									<select id="newLongDeductSchemeId" name="newCard.longDeductSchemeId" lay-search>
										<option value="">请选择长住扣费方案</option> 
										#for(deduct : longSchemeList)
											<option value="#(deduct.id)" #if(deduct.id==oldCard.longDeductSchemeId??) selected #end>#(deduct.name)</option> 
										#end
									</select>
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>年限类型</label>
								<div class="layui-input-inline" style="width:182px;">
									<select id="newCardYearLimit" name="newCard.yearLimit" lay-verify="required">
										<option value="">请选择年限类型</option> 
										#for(cardYearLimit : cardYearLimitList)
											<option value="#(cardYearLimit.dictValue)" #if(cardYearLimit.dictValue==oldCard.yearLimitValue??) selected #end>#(cardYearLimit.name)</option>
										#end
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label"><span class="co">*</span>使用年限</label>
								<div class="layui-input-inline">
									<input type="text" id="newUseYears" name="newCard.useYears" value="#(oldCard.useYears??)" placeholder="请输入使用年限" autocomplete="off" class="layui-input" maxlength="3" lay-verify="number">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">最大有效期</label>
								<div class="layui-input-inline">
									<input type="text" readonly="readonly" id="newExpireDate" name="newCard.expireDate" value="#date(oldCard.expireDate??,'yyyy-MM-dd')" placeholder="请输入最大有效期" autocomplete="off" class="layui-input">
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">入账公司</label>
								<div class="layui-input-inline" style="width:182px;">
									<select id="newEntryCompany" name="newCard.entryCompany">
										<option value="">请选择入账公司</option> 
										#getDictList("entryCompany")
											<option value="#(key)" #if(key==oldCard.entryCompany??) selected #end>#(value)</option> 
										#end
									</select>
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">是否收现金</label>
								<div class="layui-input-inline" style="width:182px;">
									<input type="radio" id="isCashNo" name="newCard.isCash" value="0" title="否" #if(oldCard.isCash??''=='0') checked #end> 
									<input type="radio" id="isCashYes" name="newCard.isCash" value="1" title="是" #if(oldCard.isCash??''=='1') checked #end>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">现金金额</label>
								<div class="layui-input-inline">
									<input type="text" id="newCashMoney" name="newCard.cashMoney" class="layui-input" value="#(oldCard.cashMoney??0.0)" placeholder="请输入现金金额" maxlength="6">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">收钱账户</label>
								<div class="layui-input-inline" style="width:182px;">
									<select id="newAccountId" name="newCard.accountId">
										<option value="">请选择收钱账户</option> 
										#for(account : accountList)
											#if(account.payWay??=="1") 
												#set(payWay = "(现金)") 
											#else if(account.payWay??=="2") 
												#set(payWay = "(微信)") 
											#else if(account.payWay??=="3") 
												#set(payWay = "(支付宝)") 
											#else if(account.payWay??=="4") 
												#set(payWay = "(信用卡)") 
											#else if(account.payWay??=="5") 
												#set(payWay = "(会员卡)") 
											#else if(account.payWay??=="6") 
												#set(payWay = "(Pos机)") 
											#else if(account.payWay??=="7") 
												#set(payWay = "(转账)") 
											#else if(account.payWay??=="8") 
												#set(payWay = "(企业微信)") 
											#end
											<option value="#(account.id)" #if(account.id==oldCard.accountId??) selected #end>#(payWay)#(account.accountName)</option>
										#end
									</select>
								</div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">转账金额</label>
								<div class="layui-input-inline">
									<input type="text" id="newAccountMoney" name="newCard.accountMoney" class="layui-input" value="#(oldCard.accountMoney??0.0)" placeholder="请输入转账金额" maxlength="6">
								</div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">是否早餐</label>
					            <div class="layui-input-inline" style="width:182px;">
					                <input type="radio" id="isBreakfast0" name="newCard.isBreakfast" value="0" title="否">
					                <input type="radio" id="isBreakfast1" name="newCard.isBreakfast" value="1" title="是" checked>
					            </div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">是否午餐</label>
					            <div class="layui-input-inline" style="width:182px;">
					                <input type="radio" id="isLunch0" name="newCard.isLunch" value="0" title="否">
					                <input type="radio" id="isLunch1" name="newCard.isLunch" value="1" title="是" checked>
					            </div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">是否晚餐</label>
					            <div class="layui-input-inline" style="width:182px;">
					                <input type="radio" id="isDinner0" name="newCard.isDinner" value="0" title="否">
					                <input type="radio" id="isDinner1" name="newCard.isDinner" value="1" title="是" checked>
					            </div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">是否可预订</label>
					            <div class="layui-input-inline" style="width:182px;">
					                <input type="radio" id="isBooking0" name="newCard.isBooking" value="0" title="否">
					                <input type="radio" id="isBooking1" name="newCard.isBooking" value="1" title="是" checked>
					            </div>
							</div>
						</div>
						<div class="layui-row">
							<div class="layui-inline">
								<label class="layui-form-label">档案号</label>
					            <div class="layui-input-inline">
					                <input type="text" id="archiveNo" name="newCard.archiveNo" class="layui-input" value="" placeholder="请输入档案号">
					            </div>
							</div>
							<div class="layui-inline">
								<label class="layui-form-label">存放位置</label>
					            <div class="layui-input-inline">
					                <input type="text" id="depositPlace" name="newCard.depositPlace" class="layui-input" value="" placeholder="请输入存放位置">
					            </div>
							</div>
						</div>
						<div class="layui-row">
							<label class="layui-form-label">不适用基地</label>
				            <div class="layui-input-block">
				                <div id="notFitBaseXmSelectTree" class="xm-select-demo" hiddenTargetId="notFitBase" dataValue="#(notFitBases??)"></div>
				            </div>
						</div>
						<div class="layui-row">
							<label class="layui-form-label">卡说明</label>
							<div class="layui-input-block">
								<textarea class="layui-textarea" id="newDescribe" name="newCard.describe" placeholder="请输入卡说明">#(oldCard.describe??)</textarea>
							</div>
						</div>
						<fieldset class="layui-elem-field layui-field-title">
							<legend>开卡人信息</legend>
							<div class="layui-field-box">
								<div class="layui-row">
									<div class="layui-inline">
										<label class="layui-form-label"><span class="co">*</span>姓氏</label>
										<div class="layui-input-inline">
											<input type="text" id="newSurname" name="member.surname" value="" placeholder="请输入姓氏" lay-verify="required" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-inline">
										<input type="button" id="getMemberInfo" class="layui-btn" value="获取开卡人信息" />
									</div>
								</div>
								<div class="layui-row">
									<div class="layui-inline">
										<label class="layui-form-label"><span class="co">*</span>姓名</label>
										<div class="layui-input-inline">
											<input type="text" id="newFullName" name="member.fullName" value="" placeholder="请输入姓名" lay-verify="required" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">性别</label>
										<div class="layui-input-inline" style="width:182px;">
											<select id="newCardGender" name="member.gender">
												<option value="">请选择性别</option> 
												#getDictList("gender")
													<option value="#(key)")>#(value)</option> 
												#end
											</select>
										</div>
									</div>
								</div>
								<div class="layui-row">
									<div class="layui-inline">
										<label class="layui-form-label">证件类型</label>
						                <div class="layui-input-inline" style="width:182px;">
						                    <select id="newIdcardType" name="member.idcardType">
						                        #getDictList("id_card_type")
						                        <option value="#(key)">#(value)</option>
						                        #end
						                    </select>
						                </div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">身份证</label>
										<div class="layui-input-inline">
											<input type="text" id="newIdcard" name="member.idcard" value="" placeholder="请输入身份证" autocomplete="off" class="layui-input">
										</div>
									</div>
								</div>
								<div class="layui-row">
									<div class="layui-inline">
										<label class="layui-form-label">手机号</label>
										<div id="memberLinkDiv" class="layui-input-inline" style="width:182px;>
											<input type="text" id="newTelephone" name="newCard.telephone" lay-verify="checkPhone" class="layui-input" value="" placeholder="请输入手机号">
										</div>
									</div>
									<div class="layui-inline">
										<label class="layui-form-label">出生日期</label>
										<div class="layui-input-inline">
											<input type="text" id="newBirthday" name="member.birthday" value="" placeholder="请输入出生日期" autocomplete="off" class="layui-input">
										</div>
									</div>
								</div>
								<div class="layui-row">
									<label class="layui-form-label">居住区域</label>
									<div class="layui-input-block">
										<input type="hidden" id="province" name="member.province" value="#(oldCard.province??)" /> 
										<input type="hidden" id="city" name="member.city" value="#(oldCard.city??)" /> 
										<input type="hidden" id="town" name="member.town" value="#(oldCard.town??)"> 
										<input type="hidden" id="street" name="member.street" value="#(oldCard.street??)">
										<input type="hidden" id="regidentProvinceName" value="#(province??)" /> 
										<input type="hidden" id="regidentCityName" value="#(city??)" /> 
										<input type="hidden" id="regidentCountyName" value="#(town??)">
										<input type="hidden" id="regidentStreetName" value="#(street??)">
										<input type="text" id="regidentAddress" name="member.regidentAddress" class="layui-input" value="" readonly="readonly"  autocomplete="off" placeholder="请选择居住区域">
									</div>
								</div>
								<div class="layui-row">
									<label class="layui-form-label">详细地址</label>
									<div class="layui-input-block">
										<input type="text" id="newAddress" name="member.address" class="layui-input" value="" autocomplete="off" placeholder="请输入详细地址">
									</div>
								</div>
							</div>
						</fieldset>
						<div class="layui-form-footer">
							<div class="pull-right">
<!-- 								<input type="hidden" id="oldCardId" name="oldCardId" value="#(oldCard.id??)" /> -->
<!-- 								<input type="hidden" id="operator" name="newCard.operator" value="" /> -->
								<input type="hidden" id="transferDetailCount" name="transferDetailCount" value="0">
								<input type="hidden" id="notFitBase" name="notFitBase" value="">
								<button type="button" id="addMemberCardBtn" class="layui-btn">添加会员卡</button>
								<button id="confirmBtn" class="layui-btn" lay-submit lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
								<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
							</div>
						</div>
					</div>
				</fieldset>
			</div>
		</div>
		<div class="layui-row" style="margin-bottom: 60px;"></div>
	</div>
</form>
</div>
#end

<!-- 公共JS文件 -->
#define js()
<script id="regidentAreaTpl" type="text/html">
	<div id="regidentArea" style="width:700px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
		<div class="tabs clearfix">
			<ul>
				<li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
				<li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
				<li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
				<li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
			</ul>
		</div>
		<div class="con" style="height:300px;overflow-y: auto;">
			<div class="regidentProvinceAll">
				<div class="list"></div>
			</div>
			<div class="regidentCityAll">
				<div class="list"></div>
			</div>
			<div class="regidentTownAll">
				<div class="list"></div>
			</div>
			<div class="regidentStreetAll">
				<div class="list"></div>
			</div>
		</div>
	</div>
</script>
<script id="transferDetailTrTpl" type="text/html">
	<tr id="tr-{{d.idx}}" tr-index="{{d.idx}}">
		<td>
			<input type="text" id="oldCardNumber-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardNumber" class="layui-input" lay-verify="required" value="" placeholder="输完卡号点击其他地方加载" autocomplete="off">
		</td>
		<td>
			<input type="text" id="oldCardAmount-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardAmount" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡剩余金额" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardBuyAmount-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardBuyAmount" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡购买金额" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardGiveAmount-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardGiveAmount" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡赠送金额" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardDays-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardDays" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡剩余天数" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardBuyDays-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardBuyDays" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡购买天数" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardGiveDays-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardGiveDays" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡赠送天数" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardIntegrals-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardIntegrals" class="layui-input" lay-verify="required" value="0" placeholder="旧卡剩余积分" autocomplete="off" readonly="readonly">
		</td>
		<td>
			<input type="text" id="oldCardAccountBalance-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardAccountBalance" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡账面余额" autocomplete="off">
		</td>
		<td>
			<input type="text" id="oldCardTransferDays-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardTransferDays" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡转换购买天数" autocomplete="off">
		</td>
		<td>
			<input type="text" id="oldCardTransferGiveDays-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardTransferGiveDays" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡转换赠送天数" autocomplete="off">
		</td>
		<td>
			<input type="text" id="oldCardTransferMoney-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardTransferMoney" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡转换购买金额" autocomplete="off">
		</td>
		<td>
			<input type="text" id="oldCardTransferGiveMoney-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardTransferGiveMoney" class="layui-input" lay-verify="required|checkAmount" value="0" placeholder="旧卡转换赠送金额" autocomplete="off">
		</td>
		<td>
			<div class="layui-btn-group">
				<input type="hidden" id="oldCardId-{{d.idx}}" name="transferDetail[{{d.idx}}].oldCardId" value="{{d.oldCardId}}">
				<button type="button" class="layui-btn layui-btn-xs" onclick="recharge('{{d.idx}}')">充值情况</button>
				<button type="button" class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('{{d.idx}}')">删除</button>
			</div>
		</td>
	</tr>
</script>
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
var $;
layui.use([ 'form', 'table', 'element', 'laytpl', 'layer', 'laydate' ], function() {
	$ = layui.$;
	var form = layui.form;
	var table = layui.table;
	var element = layui.element;
	var laytpl = layui.laytpl;
	var layer = layui.layer;
	var laydate = layui.laydate;
	
	form.on('select(transferTypeFilter)', function(data){
		var transferType = data.value;
		if(transferType=='0'){//过户
			// $("#balanceDiv").hide();
			// $("#consumeTimesDiv").hide();
			$("#newBalance").val('0.0');
			$("#newConsumeTimes").val('0.0');
			$("#transferFee").val('200.0');
		}else{//升级
			// $("#balanceDiv").show();
			// $("#consumeTimesDiv").show();
			$("#transferFee").val('0.0');
			
			$("#newCardTypeId").val(null);
			$("#newOpenTime").val(null);
			$("#newContractType").val(null);
			$("#newContractNumber").val(null);
			$("#operator").val(null);
			$("#operatorName").val(null);
			$("#newBranchOfficeId").val(null);
			$("#salesDeptIdValue").val(null);
			deptSelectCheckLoad('salesDeptId', 'salesDeptIdValue', null);
			$('#isBreakfast1').attr('checked','true');
			$('#isLunch1').attr('checked','true');
			$('#isDinner1').attr('checked','true');
			$('#isBooking1').attr('checked','true');
			$("#archiveNo").val(null);
			$("#depositPlace").val(null);
			notFitBaseSelectCheckLoad('notFitBaseXmSelectTree', 'notFitBase', null);
			$("#newPurchaseCardBalance").val(null);
			$("#newContractTimes").val(null);
			$("#newContractDiscount").val(null);
			$("#giveRechargeDays").val(null);
			$("#newCollectAmount").val(null);
			$("#newPrice").val(null);
			$("#newReferencePrice").val(null);
			$("#newDeductSchemeId").val(null);
			$("#newLongDeductSchemeId").val(null);
			$("#newCardYearLimit").val(null);
			$("#newUseYears").val(null);
			$("#newExpireDate").val(null);
			$("#newEntryCompany").val(null);
			$('#isCashNo').attr('checked','true');
			$("#newCashMoney").val(null);
			$("#newAccountId").val(null);
			$("#newAccountMoney").val(null);
			$("#newDescribe").val(null);
			form.render("select");
			form.render("radio");
		}
		form.render('select');
	});

	//新会员卡开卡时间
	laydate.render({
		elem : '#newOpenTime',
		type : 'date',
		trigger : 'click',
		done:function () {
			expireDateSetVal();
		}
	});
	//新会员卡开卡人出生日期
	laydate.render({
		elem : '#newBirthday',
		type : 'date',
		trigger : 'click'
	});

	form.on('select(memberLinkSelect)', function (data) {
		var linkId = data.value;
		var linkPhone = $("#memberLinkId").find('option[value="' + linkId + '"]').attr("linkPhone");
		$('#newTelephone').val(linkPhone);
	});
	
	form.render("select");
	form.render("radio");
	
	deptSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
        var deptSelect = xmSelect.render({
            el: '#'+elem,
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            clickClose:true,
            direction:'down',
            tips:'请选择销售部门',
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                strict: false,
            },
            height: '300px'
            , on:function(data){
                var dataArray = data.arr;//data.arr:  当前多选已选中的数据
                if(data.change){//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                    if(dataArray.length>0){
                        $('#'+hiddenTargetId).val(dataArray[0].id);
                    }else{
                        $('#'+hiddenTargetId).val('');
                    }
                }
            }
        });
        $.post('#(hrmDomain)/api/orgTreeSelect',{},function (res) {
            if (dataValue != null && dataValue != '') {
                deptSelect.update({
                    data:res
                    , initValue:[dataValue]
                });
            }else{
                deptSelect.update({
                    data:res
                });
            }
        });
	};
	
    getNameAndUserId = function (userId, name, branchOfficeId, deptId) {
        $("#operator").val(userId);
        $("#operatorName").val(name);
        $("#branchOfficeId").val(branchOfficeId);
        $("#salesDeptIdValue").val(deptId);
        if (deptId != '') {
            var salesDeptSelect = xmSelect.get('#salesDeptId', true);
            $.post('#(hrmDomain)/api/orgTreeSelect',{},function (res) {
                if (deptId != '') {
                    salesDeptSelect.update({
                        data:res
                        , initValue:[deptId]
                    });
                }else{
                    salesDeptSelect.update({
                        data:res
                    });
                }
            });
        }
        form.render('select');
    }
    
 	// 选择分公司业务员
    $("#chooseUser").click(function () {
        $(this).blur();
        var url = "#(ctxPath)/fina/cardmanager/chooseUser";
        pop_show("选择分公司业务员", url, 800, 500);
    });
 	
    notFitBaseSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
        var notFitBaseXmSelectCheck = xmSelect.render({
                el: '#' + elem
                , prop: {
                    name: 'baseName'
                    , value: 'id'
                }
                , model: {label: {type: 'text'}}
                , toolbar: {show: true}
                , filterable: true
            #if(type == 'check')
                , disabled: true
            #end
                , direction: 'down'
                , tips: '无'
                , tree: {
                    show:true
                    , strict: false
                }
                , height:'300px'
                , autoRow: true
                , on: function (data) {
                var dataArray = data.arr;//data.arr:  当前多选已选中的数据
                if (data.change) {//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                    if (dataArray.length > 0) {
                        var dArray = new Array();
                        for (var i = 0; i < dataArray.length; i++) {
                            dArray.push(dataArray[i].id);
                        }
                        $('#' + hiddenTargetId).val(dArray.toString());
                    } else {
                        $('#' + hiddenTargetId).val('');
                    }
                }
            }
        });
        $.post('#(ctxPath)/fina/cardmanager/getAllBase', {}, function (rep) {
            if (rep.state == 'ok') {
                var initValueArray = null;
                if (dataValue.indexOf(",") != -1) {
                    initValueArray = dataValue.split(',');
                    notFitBaseXmSelectCheck.update({
                        data: rep.baseList
                        , initValue: initValueArray
                    })
                } else {
                    notFitBaseXmSelectCheck.update({
                        data: rep.baseList
                        , initValue: [dataValue]
                    })
                }
            }
        });
    };
    
    $('div[class="xm-select-demo"]').each(function (i, obj) {
        var targetId = obj.id;
        var hiddenTargetId = $(obj).attr('hiddenTargetId') != null ? $(obj).attr('hiddenTargetId') : '';
        var dataValue = $(obj).attr('dataValue') != null ? $(obj).attr('dataValue') : '';
        if(targetId=='salesDeptId'){
        	deptSelectCheckLoad(targetId, hiddenTargetId, dataValue);
        }else if(targetId=='notFitBaseXmSelectTree'){
        	notFitBaseSelectCheckLoad(targetId, hiddenTargetId, dataValue);
        }
    });

	//校验
	form.verify({
		checkAmount : function(value) {
			if (value != null) {
				var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
				var reg2 = new RegExp("^[0-9]*$");
				if (!reg1.test(value) && !reg2.test(value)) {
					return "只能输入数字和小数点后两位小数";
				}
			}
		},
		checkNumber : function(value) {
			if (value != null) {
				var reg = new RegExp("^[0-9]*$");
				if (!reg.test(value)) {
					return "只能输入数字";
				}
			}
		},
		checkPhone : function(value) {
			if (value != null && value.length > 0) {
				var reg = new RegExp("^1[3456789]\\d{9}$");
				if (!reg.test(value)) {
					return "手机号码格式不正确";
				}
			}
		}
	});

	function getFormatDate(arg) {
		if (arg == undefined || arg == '') {
			return '';
		}
		var re = arg + '';
		if (re.length < 2) {
			re = '0' + re;
		}
		return re;
	}
	/*end*/

	//身份证关联出生日期
	function idCardBirthday(psidno) {
		var birthdayno, birthdaytemp;
		if (psidno.length == 18) {
			birthdayno = psidno.substring(6, 14)
		} else if (psidno.length == 15) {
			birthdaytemp = psidno.substring(6, 12);
			birthdayno = "19" + birthdaytemp;
		} else {
			return false
		}
		var birthday = birthdayno.substring(0, 4) + "-" + birthdayno.substring(4, 6) + "-" + birthdayno.substring(6, 8);
		return birthday
	}

	getNameAndUserId = function(userId, name) {
		$("#operatorName").val(name);
		$("#operator").val(userId);
	}

	//旧卡赠送详情按钮点击事件
	$("#giveSchemeDetail").on('click', function() {
		if ($("#gId").val() === '') {
			layer.msg('请选择赠送方案', {icon : 5,time : 5000});
			return false;
		}
		layerShow('赠送详情', '#(ctxPath)/fina/cardmanager/giveSchemeDetailIndex?giveSchemeId=' + $("#gId").val(), 800, 600);
	});

	//新卡赠送详情按钮点击事件
	$("#newGiveSchemeDetail").on('click', function() {
		if ($("#newGiveSchemeId").val() === '') {
			layer.msg('请选择赠送方案', {icon : 5,time : 5000});
			return false;
		}
		layerShow('赠送详情', '#(ctxPath)/fina/cardmanager/giveSchemeDetailIndex?giveSchemeId=' + $("#newGiveSchemeId").val(), 800, 600);
	});

	//卡号生成
	$("#ranCardNumber").on("click", function() {
		var cardTypeId = $("#newCardTypeId").val();
		util.sendAjax({
			url : '#(ctxPath)/fina/cardmanager/getRandomNumber',
			type : 'post',
			data : {
				"cardTypeId" : cardTypeId
			},
			notice : false,
			success : function(returnData) {
				if (returnData.state === 'ok') {
					$('#newCardNumber').val(returnData.data);
				} else {
					layer.msg(returnData.msg,{icon : 5,time : 5000});
				}
			}
		});
	});

	//根据会员姓名和身份证获取会员档案
	function checkNameAndIdcard() {
		// var newFullName = $("#newFullName").val();
		var idcard = $("#newIdcard").val();
		if (idcard != null && idcard.trim() != "") {
			util.sendAjax({
				url : '#(ctxPath)/fina/cardmanager/bindMmsMember',
				type : 'post',
				data : {idcard : idcard},
				notice : false,
				success : function(returnData) {
					if (returnData.state === 'ok' && returnData.msg != '无信息') {
						$("#newSurname").val(returnData.data.surname);
						// $('#newTelephone').val(returnData.data.telephone);
						if(returnData.linkList!=null && returnData.linkList.length>0){
							$('#memberLinkDiv').html('');
							var html = '<select id="memberLinkId" name="memberLinkId" lay-verify="required" lay-filter="memberLinkSelect">';
							html += '<option value="" linkPhone="">请选择</option>';
							for (var i = 0; i < returnData.linkList.length; i++) {
								html += '<option value="'+returnData.linkList[i].id+'" linkPhone="'+returnData.linkList[i].linkPhone+'">'+returnData.linkList[i].linkName+'('+returnData.linkList[i].linkPhone+')</option>';
							}
							html += '</select>';
							html += '<input type="hidden" id="newTelephone" name="newCard.telephone" value=""/>';
							$('#memberLinkDiv').append(html);
						}else{
							$('#memberLinkDiv').html('');
							var html = '<input type="text" id="newTelephone" name="newCard.telephone" lay-verify="checkPhone" class="layui-input" value="" placeholder="请输入手机号">';
							$('#memberLinkDiv').append(html);
						}
						if (returnData.data.birthday != null && returnData.data.birthday != '') {
							$('#newBirthday').val(returnData.data.birthday);
						}
						$('#newAddress').val(returnData.data.address);
						//处理区域
						$("#province").val(returnData.data.province);
						$("#city").val(returnData.data.city);
						$("#town").val(returnData.data.town);
						$("#street").val(returnData.data.street);
						$("#regidentProvinceName").val(returnData.province);
						$("#regidentCityName").val(returnData.city);
						$("#regidentTownName").val(returnData.town);
						$("#regidentStreetName").val(returnData.street);

						var addr = "";
						if (returnData.province != null) {
							addr += " " + returnData.province;
						}
						if (returnData.city != null) {
							addr += " " + returnData.city;
						}
						if (returnData.town != null) {
							addr += " " + returnData.town;
						}
						if (returnData.street != null) {
							addr += " " + returnData.street;
						}
						$("#regidentAddress").val(addr);

						if (returnData.data.gender == "1") {
							$("#newCardGender option[value='1']").prop("selected", true).siblings().prop("selected",false);
						} else if (returnData.data.gender == "2") {
							$("#newCardGender option[value='2']").prop("selected", true).siblings().prop("selected",false);
						} else if (returnData.data.gender == "0") {
							$("#newCardGender option[value='0']").prop("selected",true).siblings().prop("selected",false);
						} else {
							$("#newCardGender option").prop("selected",false);
							//$("#cardGender option[value='0']").prop("selected", true).siblings().prop("selected",false);
						}
					} else {
						$("#newCardGender option").prop("selected", false);
						$('#memberLinkDiv').html('');
						var html = '<input type="text" id="newTelephone" name="newCard.telephone" lay-verify="checkPhone" class="layui-input" value="" placeholder="请输入手机号">';
						$('#memberLinkDiv').append(html);
						//$("#cardGender option[value='0']").prop("selected", true).siblings().prop("selected",false);
						layer.msg("没有档案信息", {
							time : 800
						});
					}
					form.render("select");
				}
			});
		}
	}

	//获取会员信息
	$("#getMemberInfo").on("click", function() {
		$("#newSurname").val("");
		$("#newbirthday").val("");
		$("#newtelephone").val("");
		$("#province").val("");
		$("#city").val("");
		$("#town").val("");
		$("#street").val("");
		$("#regidentProvinceName").val("");
		$("#regidentCityName").val("");
		$("#regidentTownName").val("");
		$("#regidentStreetName").val("");
		$("#regidentAddress").val("");
		$("#newAddress").val("");
		checkNameAndIdcard();
		var idcard = $("#newIdcard").val();
		if (idcard != null && idcard != '' && idcard != undefined) {
			var newBirthday = idCardBirthday(idcard);
			if (newBirthday != null && newBirthday != '' && newBirthday != undefined) {
				$("#newBirthday").val('');
				$('#newBirthday').val(newBirthday);
			}
		}
		form.render("select");
	});
	
	function setNewCardInfo(cardNumber) {
		if (cardNumber == null || cardNumber == '' || cardNumber == undefined) {
			layer.msg("会员卡号不能为空");
			return;
		}
		util.sendAjax({
			url : '#(ctxPath)/fina/cardmanager/getOldCardDataForCardTransfer',
			type : 'post',
			data : {'cardNumber':cardNumber},
			notice : false,
			success : function(returnData) {
				if (returnData.state=='ok') {
					//部分新卡信息
					$("#newCardTypeId").val(returnData.data.cardTypeId);
					$("#newOpenTime").val(returnData.data.openTime);
// 					$("#newBalance").val(returnData.data.balance);
// 					$("#newConsumeTimes").val(returnData.data.consumeTimes);
// 					$("#newConsumePoints").val(returnData.data.consumePoints);
// 					$("#newCardIntegrals").val(returnData.data.cardIntegrals);
					$("#newPurchaseCardBalance").val(returnData.data.purchaseCardBalance!=null?returnData.data.purchaseCardBalance:0.0);
					$("#newContractTimes").val(returnData.data.contractTimes!=null?returnData.data.contractTimes:0.0);
					$("#newContractDiscount").val(returnData.data.contractDiscount!=null?returnData.data.contractDiscount:0.0);
					$("#giveRechargeDays").val(returnData.data.giveConsumeTimes!=null?returnData.data.giveConsumeTimes:0.0);
					$("#newCollectAmount").val(returnData.data.collectAmount!=null?returnData.data.collectAmount:0.0);
					$("#newPrice").val(returnData.data.price!=null?returnData.data.price:0.0);
// 					$("#newCountPrice").val(returnData.data.countPrice);
					$("#newReferencePrice").val(returnData.data.referencePrice!=null?returnData.data.referencePrice:0.0);
					$("#newDeductSchemeId").val(returnData.data.deductSchemeId);
					$("#newLongDeductSchemeId").val(returnData.data.longDeductSchemeId);
					$("#newCardYearLimit").val(returnData.data.yearLimit);
					$("#newUseYears").val(returnData.data.useYears);
					$("#newExpireDate").val(returnData.data.expireDate);
					$("#newEntryCompany").val(returnData.data.entryCompany);
					if (returnData.data.isCash == '1') {
						$('#isCashYes').attr('checked','true');
					}else{
						$('#isCashNo').attr('checked','true');
					}
					$("#newCashMoney").val(returnData.data.cashMoney!=null?returnData.data.cashMoney:0.0);
					$("#newAccountId").val(returnData.data.accountId);
					$("#newAccountMoney").val(returnData.data.cashMoney!=null?returnData.data.cashMoney:0.0);
					$("#newDescribe").val(returnData.data.describe);
					form.render("select");
					form.render("radio");
				}
			}
		});
	}

	//获取旧卡数据
// 	$("#getOldCard").click(function() {
// 		var oldCardNumber = $("#oldCardNumber").val();
// 		setNewCardInfo(oldCardNumber);
// 	});

	function expireDateSetVal() {
		var useYears = $("#newUseYears").val();
		var openTime = $("#newOpenTime").val();
		var reg = new RegExp("^[0-9]*$");
		if (!reg.test(useYears)) {
			$("#newUseYears").val("");
		}
		if (openTime === '') {
			return false;
		}
		if (Number(useYears) === 0) {
			$("#newExpireDate").val('');
			return false;
		}
		if ($("#newExpireDate").val() != null && $("#newExpireDate").val() != '' || $("#newExpireDate").val() != undefined) {
			$("#newExpireDate").val('');
		}
		var date = new Date(openTime);
		date.setFullYear(date.getFullYear()+ Number(useYears));
		$("#newExpireDate").val(date.getFullYear() + "-" + getFormatDate(date.getMonth() + 1) + "-" + getFormatDate(date.getDate()));
	}
	
	//生成最大有效期begin
	$("#newUseYears").on('keyup', function() {
		expireDateSetVal();
	});
	
	form.on('select(cardTypeFilter)', function(data){
		var cardTypeId = data.value;
		if(cardTypeId!=null && cardTypeId!=''){
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/fina/cardType/getCardTypeConfig',
	            data: {id:cardTypeId},
	            notice: false,
			    loadFlag: false,
	            success : function(rep){
	            	var cardTypeConf = rep.cardTypeConf;
	            	if(cardTypeConf!=null && cardTypeConf!=''){
// 		            	$("#newBalance").val(cardTypeConf.balanceMoney);
// 						$("#newConsumeTimes").val(cardTypeConf.balanceDays);
						$("#newPurchaseCardBalance").val(cardTypeConf.buyCardMoney);
						$("#newContractTimes").val(cardTypeConf.contractTimes);
						$("#newContractDiscount").val(cardTypeConf.contractDiscount);
						$("#giveRechargeDays").val(cardTypeConf.giveDays);
						$("#newCollectAmount").val(cardTypeConf.collectAmount);
						$("#newDeductSchemeId").val(cardTypeConf.deductSchemeId);
						$("#newLongDeductSchemeId").val(cardTypeConf.longDeductSchemeId);
						$("#newCardYearLimit").val(cardTypeConf.yearLimit);
		            	$("#newUseYears").val(cardTypeConf.useYears);
		            	$("#newPrice").val(cardTypeConf.price);
		            	$("#newReferencePrice").val(cardTypeConf.referencePrice);
		            	var openTime = $("#newOpenTime").val();
		            	if(openTime!=null){
			            	expireDateSetVal();
		            	}
		            	$("#newDescribe").val(cardTypeConf.remark);
	            	}else{
// 	            		$("#newBalance").val('');
// 						$("#newConsumeTimes").val('');
						$("#newPurchaseCardBalance").val('');
						$("#newContractTimes").val('');
						$("#newContractDiscount").val('');
						$("#giveRechargeDays").val('');
						$("#newCollectAmount").val('');
						$("#newDeductSchemeId").val('');
						$("#newLongDeductSchemeId").val('');
						$("#newCardYearLimit").val('');
		            	$("#newUseYears").val('');
		            	$("#newExpireDate").val('');
		            	$("#newPrice").val('');
		            	$("#newReferencePrice").val('');
		            	$("#newDescribe").val('');
	            	}
	            	form.render('select');
	            },
	            complete : function() {
			    }
	        });
		}
	});

	function getCardInfo(id,cardNumber,flag,delFlag,index){
		util.sendAjax({
			url:"#(ctxPath)/fina/cardmanager/getCardByCardNumber",
			type:'post',
			data:{'id':id,'cardNumber':cardNumber,flag:flag,delFlag:delFlag},
			notice:false,
			success:function(returnData){
				if(returnData.state==='ok'){
					if(returnData.data != null) {
						layer.msg("获取到会员卡信息");
// 						console.log('returnData.data.id==='+returnData.data.id)
// 						console.log('returnData.data.balance==='+returnData.data.balance)
// 						console.log('returnData.data.consumeTimes==='+returnData.data.consumeTimes)
// 						console.log('returnData.data.consumePoints==='+returnData.data.consumePoints)
// 						console.log('returnData.data.cardIntegrals==='+returnData.data.cardIntegrals)
// 						console.log('returnData.data.beanCoupons==='+returnData.data.beanCoupons)
// 						console.log('returnData.data.accountBalance==='+returnData.data.accountBalance)
						var oldCardDays = 0.0;
						if(returnData.data.lockConsumeTimes > 0.0){
							oldCardDays = returnData.data.consumeTimes + returnData.data.lockConsumeTimes;
						}else{
							oldCardDays = returnData.data.consumeTimes;
						}
						$("#oldCardId-"+index).val(returnData.data.id);
						$("#oldCardAmount-"+index).val(returnData.data.balance!=null?returnData.data.balance:0.0);
						$("#oldCardBuyAmount-"+index).val(returnData.data.buyRechargeAmount)!=null?returnData.data.buyRechargeAmount:0.0;
						$("#oldCardGiveAmount-"+index).val(returnData.data.giveRechargeAmount!=null?returnData.data.giveRechargeAmount:0.0);
						$("#oldCardDays-"+index).val(oldCardDays!=null?oldCardDays:0.0);
						$("#oldCardBuyDays-"+index).val(returnData.data.buyRechargeDays!=null?returnData.data.buyRechargeDays:0.0);
						$("#oldCardGiveDays-"+index).val(returnData.data.giveRechargeDays!=null?returnData.data.giveRechargeDays:0.0);
						$("#oldCardIntegrals-"+index).val(returnData.data.cardIntegrals!=null?returnData.data.cardIntegrals:0.0);
						$("#oldCardAccountBalance-"+index).val(returnData.data.accountBalance!=null?returnData.data.accountBalance:0.0);
						$("#oldCardTransferDays-"+index).val(returnData.data.buyRechargeDays!=null?returnData.data.buyRechargeDays:0.0);
						$("#oldCardTransferGiveDays-"+index).val(returnData.data.giveRechargeDays!=null?returnData.data.giveRechargeDays:0.0);
						$("#oldCardTransferMoney-"+index).val(returnData.data.buyRechargeAmount!=null?returnData.data.buyRechargeAmount:0.0);
						$("#oldCardTransferGiveMoney-"+index).val(returnData.data.giveRechargeAmount!=null?returnData.data.giveRechargeAmount:0.0);
						var transferType = $("#transferType").val();
						if(transferType=='0'){
							setNewCardInfo(cardNumber);
						}
						element.tabAdd('cardNumberTab', {
							title: cardNumber
							, content: '<div id="cardNumberForm_'+cardNumber+'"></div>'
							, id: cardNumber
						});
						$('#cardNumberForm_'+cardNumber).load('#(ctxPath)/fina/cardmanager/cardNumberForm?cardNumber='+cardNumber);
						element.tabChange('cardNumberTab', cardNumber);
					}else{
						layer.msg("旧会员卡信息不存在",{time: 500});
						$("#oldCardAmount-"+index).val('');
						$("#oldCardBuyAmount-"+index).val('');
						$("#oldCardGiveAmount-"+index).val('');
						$("#oldCardDays-"+index).val('');
						$("#oldCardBuyDays-"+index).val('');
						$("#oldCardGiveDays-"+index).val('');
						$("#oldCardIntegrals-"+index).val('');
						$("#oldCardAccountBalance-"+index).val('');
						$("#oldCardTransferDays-"+index).val('');
						$("#oldCardTransferGiveDays-"+index).val('');
						$("#oldCardTransferMoney-"+index).val('');
						$("#oldCardTransferGiveMoney-"+index).val('');
						$("#oldCardId-"+index).val('');
					}
				}else{
					layer.msg("旧会员卡信息获取失败",{time: 500});
					$("#oldCardAmount-"+index).val('');
					$("#oldCardBuyAmount-"+index).val('');
					$("#oldCardGiveAmount-"+index).val('');
					$("#oldCardDays-"+index).val('');
					$("#oldCardBuyDays-"+index).val('');
					$("#oldCardGiveDays-"+index).val('');
					$("#oldCardIntegrals-"+index).val('');
					$("#oldCardAccountBalance-"+index).val('');
					$("#oldCardTransferDays-"+index).val('');
					$("#oldCardTransferGiveDays-"+index).val('');
					$("#oldCardTransferMoney-"+index).val('');
					$("#oldCardTransferGiveMoney-"+index).val('');
					$("#oldCardId-"+index).val('');
				}
			}
		});
	}
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		var index = parseInt(idx)+1;
		$("#transferDetailCount").val(index);
		laytpl(addTpl).render({"idx":index}, function(html){
			targetId.append(html);
		});
		//change事件
		$("#oldCardNumber-"+index).on('change',function(){
			var transferType = $("#transferType").val();
			var cardNumber = $('#oldCardNumber-'+index).val();
			if(cardNumber != null && cardNumber != '' && cardNumber != undefined){
				cardNumber = cardNumber.trim();
				//获取会员卡信息
				getCardInfo(null,cardNumber,null,'0',index);
			}else{
				layer.msg('请输入正确的会员卡号!',{icon:5});
			}
		});
	};

	//添加会员卡按钮
	$("#addMemberCardBtn").on('click',function () {
		addTpl($('#transferDetail'), transferDetailTrTpl.innerHTML, $("#transferDetailCount").val());
	});

	//删除会员卡按钮
	del=function(index){
		var cardNumber = $("#oldCardNumber-"+index).val();
		if(cardNumber!=null && cardNumber!=''){
			//删除指定Tab项
			element.tabDelete('cardNumberTab', cardNumber);
		}
		$("#tr-"+index).remove();
	}

	//充值情况按钮
	recharge=function(index){
		var cardNumber = $("#oldCardNumber-"+index).val();
		if(cardNumber!=null && cardNumber!=''){
			layerShow('充值情况','#(ctxPath)/fina/recharge/detail?cardNumber='+cardNumber,null,null);
		}else{
			layer.msg('请选择会员卡号!',{icon:5});
		}
	}

	//转移保存
	form.on('submit(confirmBtn)', function() {
		$("#confirmBtn").attr("disabled", true);
		$("#confirmBtn").addClass("layui-btn-disabled");
		util.sendAjax({
			url : '#(ctxPath)/fina/cardmanager/cardTransfer',
			type : 'post',
			data : $("#transferForm").serialize(),
			notice : true,
			loadFlag : true,
			success : function(returnData) {
				if (returnData.state === 'ok') {
					pop_close();
					parent.formOperateReload();
				}
			},
			complete : function() {
				$("#confirmBtn").attr("disabled",false);
				$("#confirmBtn").removeClass("layui-btn-disabled");
			}
		});
		return false;
	});
	//--------------------------居住区域begin---------------------------
	$('#regidentAddress').on('click', function() {
		//closeIdArea();

		$('#regidentArea').remove();
		var $this = $(this);
		var getTpl = regidentAreaTpl.innerHTML;
		$this.parent().append(getTpl);
		//event.stopPropagation();

		var street = $("#street").val();
		var regidentStreetName = $("#regidentStreetName").val();
		var town = $("#town").val();
		var regidentCountyName = $("#regidentCountyName").val();
		var city = $("#city").val();
		var regidentCityName = $("#regidentCityName").val();
		var province = $("#province").val();
		var regidentProvinceName = $("#regidentProvinceName").val();
		if (street != '' && regidentStreetName != '') {
			$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentStreetLoad(town);
			regidentCountyLoad(city);
			regidentCityLoad(province);
			regidentProvinceLoad();
			$(".con .regidentStreetAll").show().siblings().hide();
			//clickStreet(streetId,streetName);
		} else if (town != '' && regidentCountyName != '') {
			if (town != '') {
				regidentCityLoad(province);
				regidentCountyLoad(city);
				regidentProvinceLoad();
				util.sendAjax({
					type : 'POST',
					url : '#(ctxPath)/area/getAreas',
					data : {
						pid : town
					},
					notice : false,
					loadFlag : false,
					success : function(res) {
						if (res.state == 'ok') {
							if (res.data.length > 0) {
								$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								var html = "<ul>";
								$.each(res.data, function(i, item) {
									html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
								});
								html += "</ul>";
								$(".regidentStreetAll .list").append(html);
								//viewStreet(countyId,countyName);
								$(".con .regidentStreetAll").show().siblings().hide();
							} else {
								//无 街道信息
								$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								$(".con .regidentTownAll").show().siblings().hide();
							}
						}
					},
					complete : function() {
					}
				});
			}
		} else if (city != ''
				&& regidentCityName != '') {
			regidentProvinceLoad();
			regidentCityLoad(province);
			viewRegidentCounty(city,
					regidentCityName);
		} else if (province != ''
				&& regidentProvinceName != '') {
			regidentProvinceLoad();
			viewRegidentCity(province,
					regidentProvinceName);
		} else {
			regidentProvinceLoad();
		}

		//去除事件冒泡
		var evt = new Object;
		if (typeof (window.event) == "undefined") {//如果是火狐浏览器
			evt = arguments.callee.caller.arguments[0];
		} else {
			evt = event || window.event;
		}
		evt.cancelBubble = true;
		$('#regidentArea').off('click');
		$('#regidentArea')
				.on(
						'click',
						function() {
							//event.stopPropagation();
							//去除事件冒泡
							var evt = new Object;
							if (typeof (window.event) == "undefined") {//如果是火狐浏览器
								evt = arguments.callee.caller.arguments[0];
							} else {
								evt = event
										|| window.event;
							}
							evt.cancelBubble = true;
						})
	});

	regidentProvinceLoad = function() {
		util.sendAjax({
			type : 'POST',
			url : '#(ctxPath)/area/getAreas',
			data : {
				pid : ''
			},
			notice : false,
			loadFlag : false,
			success : function(res) {
				if (res.state == 'ok') {
					if (res.data.length > 0) {
						$(".regidentProvinceAll .list").empty();
						var html = "<ul>";
						$.each(res.data, function(i, item) {
							html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCity(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
						});
						html += "</ul>";
						$(".regidentProvinceAll .list").append(html);
					}
				}
			},
			complete : function() {
			}
		});
	};

	//点击省事件
	viewRegidentCity = function(province, regidentProvinceName) {
		$("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentCityAll").show().siblings().hide();
		$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		//点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
		$("#province").val(province);
		$("#regidentProvinceName").val(regidentProvinceName);
		$("#city").val("");
		$("#regidentCityName").val("");
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCityLoad(province);
	};

	//加载市
	regidentCityLoad = function(province) {
		if (province != '') {
			util.sendAjax({
				type : 'POST',
				url : '#(ctxPath)/area/getAreas',
				data : {
					pid : province
				},
				notice : false,
				loadFlag : false,
				success : function(res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentCityAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function(i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCounty(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentCityAll .list").append(html);
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	//点击市事件
	viewRegidentCounty = function(city, RegidentCityName) {
		$("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentTownAll").show().siblings().hide();
		$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#city").val(city);
		$("#regidentCityName").val(RegidentCityName);
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCountyLoad(city);
	};

	//加载区/县
	regidentCountyLoad = function(city) {
		if (city != '') {
			util.sendAjax({
				type : 'POST',
				url : '#(ctxPath)/area/getAreas',
				data : {
					pid : city
				},
				notice : false,
				loadFlag : false,
				success : function(res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentTownAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function(i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentTownAll .list").append(html);
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	//点击区/县事件
	viewRegidentStreet = function(town, regidentCountyName) {
		$("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentStreetAll").show().siblings().hide();
		$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#town").val(town);
		$("#regidentCountyName").val(regidentCountyName);
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentStreetLoad(town);
	};

	//加载街道/镇/乡
	regidentStreetLoad = function(town) {
		if (town != '') {
			util.sendAjax({
				type : 'POST',
				url : '#(ctxPath)/area/getAreas',
				data : {
					pid : town
				},
				notice : false,
				loadFlag : false,
				success : function(res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentStreetAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function(i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentStreetAll .list").append(html);
						} else {
							//无 街道信息
							clickRegidentStreet('', '');
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	clickRegidentStreet = function(street, regidentStreetName) {
		$("#street").val(street);
		$("#regidentStreetName").val(regidentStreetName);
		var regidentProvinceName = $("#regidentProvinceName").val();
		var regidentCityName = $("#regidentCityName").val();
		var regidentCountyName = $("#regidentCountyName").val();
		var regidentStreetName = $("#regidentStreetName").val();
		var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
		$("#regidentAddress").val(add);
		$('#regidentArea').remove();
	};

	regidentProvinceAllClick = function() {
		//$(".con .provinceAll").show();
		$("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentProvinceAll").show().siblings().hide();
	};

	regidentCityAllClick = function() {
		// $(".con .cityAll").show();
		if ($("#province").val() != '') {
			$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCityLoad($("#province").val());
			$(".con .regidentCityAll").show().siblings().hide();
		}
	};

	regidentTownAllClick = function() {
		if ($("#city").val() != '') {
			$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCountyLoad($("#city").val());
			$(".con .regidentTownAll").show().siblings().hide();
		}
	};

	regidentStreetAllClick = function() {
		if ($("#town").val() != '') {
			util.sendAjax({
				type : 'POST',
				url : '#(ctxPath)/area/getAreas',
				data : {
					pid : $("#town").val()
				},
				notice : false,
				loadFlag : false,
				success : function(res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							regidentStreetLoad($("#town").val());
							$(".con .regidentStreetAll").show().siblings().hide();
						} else {
							//无 街道信息 显示区/县信息
							$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							//countyLoad(cityId);
							$(".con .regidentTownAll").show().siblings().hide();
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	$('body').on('click', function() {
		closeRegidentArea();
	});

	//关闭区域选择器
	closeRegidentArea = function() {
		if (typeof ($('#regidentArea').html()) != 'undefined') {
			var regidentProvinceName = $("#regidentProvinceName").val();
			var regidentCityName = $("#regidentCityName").val();
			var regidentCountyName = $("#regidentCountyName").val();
			var regidentStreetName = $("#regidentStreetName").val();
			var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
			$("#regidentAddress").val(add);
		}
		//alert(1);
		$('#regidentArea').remove();
		//console.log($('#regidentArea').html());
	}
	//--------------------------居住区域end---------------------------
});
</script>
#end
