#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()财务扣卡图表数据展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/assess.css" media="all">
#end

#define js()
<script src="#(ctxPath)/static/plugins/highcharts/highcharts.js"></script>
<script src="#(ctxPath)/static/plugins/highcharts/measureDataOptions.js"></script>
<script type="text/javascript">
    layui.use(['table', 'element','layer','form','jquery'],function(){
        var table = layui.table, $ = layui.jquery, element = layui.element,layer=layui.layer , highcharts = null;



        function timeTabLoad(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/dataAnalysis/getTimeDataList'
                ,cols: [[
                    {type: 'numbers',title: '序号',unresize:true}
                    ,{field:'time', title: '时间', align: 'center', unresize: true}
                    ,{field:'deductTimes', title: '扣卡天数', align: 'center', unresize: true}
                    ,{field:'deductAmount', title: '扣卡金额', align: 'center',unresize: true}
                ]]
            });
        };


        function baseTabLoad(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/fina/dataAnalysis/getBaseDataList'
                ,cols: [[
                    {type: 'numbers',title: '序号',unresize:true}
                    ,{field:'baseName', title: '基地', align: 'center', unresize: true}
                    ,{field:'deductTimes', title: '扣卡天数', align: 'center', unresize: true}
                    ,{field:'deductAmount', title: '扣卡金额', align: 'center',unresize: true}
                    ,{field:'time', title: '时间', align: 'center', unresize: true}
                ]]
            });
        };


        //加载监测图表AJAX数据
        loadMonitorAjaxData = function(url,timeType) {
            highcharts.showLoading();
            $.ajax ({
                type: 'POST',
                url: url,
                data: {"timeType":timeType,"urlType":"1"},
                dataType:"json",
                success : function(rep){
                    highcharts.hideLoading();
                    if(rep.flag){
                        if(rep.measureJsonData){
                            if(highcharts.series!=null && highcharts.series.length > 0){
                                highcharts.update({
                                    xAxis: {
                                        categories: rep.measureTimeArray
                                    },
                                    series:rep.measureJsonData
                                });
                            }else{
                                for (var i=0;i<rep.measureJsonData.length;i++) {
                                    highcharts.addSeries(rep.measureJsonData[i]);
                                }
                                highcharts.update({
                                    xAxis: {
                                        categories: rep.measureTimeArray
                                    }
                                });
                            }
                        }else{
                            if(highcharts.series!=null && highcharts.series.length > 0){
                                highcharts.update({
                                    xAxis: {
                                        categories: []
                                    },
                                    series:[{
                                        name:'收缩压',
                                        data:[]
                                    },{
                                        name:'舒张压',
                                        data:[]
                                    }]
                                });
                            }

                        }
                    }else {
                        layer.msg(rep.msg,{icon:5,time:5000});
                    }

                },
                complete : function() {
                }
            });
        };

        //监听监测类型页签切换方法
        element.on('tab(dataTab)', function(data){
            var url = "";
            var dataType = $(this).attr("lay-id");
            if(dataType=='time'){
                //替换按钮上的字
                $(".layui-btn-group button").each(function(){
                    var queryType = $(this).attr("id");
                    if(queryType == 'day'){
                        $(this).text("最近7天");
                    }else if(queryType == 'month'){
                        $(this).text("今年每月");
                    }else if(queryType == 'year'){
                        $(this).text("每年");
                    }
                });
                highcharts = new Highcharts.Chart(timeOption);
                url = "#(ctxPath)/fina/dataAnalysis/getTimeSeries";
            }else if(dataType=='base'){
                //替换按钮上的字
                $(".layui-btn-group button").each(function(){
                    var queryType = $(this).attr("id");
                    if(queryType == 'day'){
                        $(this).text("前一天");
                    }else if(queryType == 'month'){
                        $(this).text("当月");
                    }else if(queryType == 'year'){
                        $(this).text("本年度");
                    }
                });
                highcharts = new Highcharts.Chart(baseOption);
                url = "#(ctxPath)/fina/dataAnalysis/getBaseSeries";
            }
            $(".layui-btn-group button").each(function(){
                var queryType = $(this).attr("id");
                if(queryType=='day'){
                    if($(this).hasClass("layui-btn-primary")){
                        $(this).removeClass("layui-btn-primary");
                    }
                }else{
                    if(!$(this).hasClass("layui-btn-primary")){
                        $(this).addClass("layui-btn-primary");
                    }
                }
            });
            if(dataType == 'time'){
                var data = {"timeType":"day"};
                timeTabLoad(data);
            }else{
                var data = {"timeType":"day"};
                baseTabLoad(data);
            }
            loadMonitorAjaxData(url,"day");
        });


        //切换统计维度
        function recordBtn(timeType){
            var url = "";
            var type = $("#dataTab .layui-tab-title").children(".layui-this").attr("lay-id");
            if(type == 'time'){
                highcharts = new Highcharts.Chart(timeOption);
                url = "#(ctxPath)/fina/dataAnalysis/getTimeSeries";
            }else if(type == 'base'){
                highcharts = new Highcharts.Chart(baseOption);
                url = "#(ctxPath)/fina/dataAnalysis/getBaseSeries";
            }
            $(".layui-btn-group button").each(function(){
                if(!$(this).hasClass("layui-btn-primary")){
                    $(this).addClass("layui-btn-primary");
                }
            });
            if($("#"+timeType).hasClass("layui-btn-primary")){
                $("#"+timeType).removeClass("layui-btn-primary");
            }
            if(type == 'time'){
                var data = {"timeType":timeType};
                timeTabLoad(data);
            }else if(type == 'base'){
                var data = {"timeType":timeType};
                baseTabLoad(data);
            }
            loadMonitorAjaxData(url,timeType);
        }

        //按钮组点击事件
        $("#day").click(function(){
            recordBtn('day');
        });
        $("#month").click(function(){
            recordBtn('month');
        });
        $("#year").click(function(){
            recordBtn('year');
        });

        //页面打开加载
        $(function(){
            var data = {"timeType":"day"};
            timeTabLoad(data);
            highcharts = new Highcharts.Chart(timeOption);
            loadMonitorAjaxData("#(ctxPath)/fina/dataAnalysis/getTimeSeries","day");
        });
    });
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row layui-col-space10">
        <div class="layui-form">
            <div class="bill-detail-search" style="margin-left: 0px;">
                <div class="layui-form-item d-tab">
                    <div id="dataTab" class="layui-tab layui-tab-card assess" lay-filter="dataTab">
                        <ul class="layui-tab-title">
                            <li lay-id="time" class="layui-this">时间</li>
                            <li lay-id="base">基地</li>
                        </ul>
                    </div>
                    <div class="layui-btn-group">
                        <button id="day" class="layui-btn">最近7天</button>
                        <button id="month" class="layui-btn layui-btn-primary">今年每月</button>
                        <button id="year" class="layui-btn layui-btn-primary">每年</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div id="chartContainer"></div>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <table id="dataTable" lay-filter="dataTable" class="layui-table"></table>
        </div>
    </div>
    <!---->
</div>
</div>
#end