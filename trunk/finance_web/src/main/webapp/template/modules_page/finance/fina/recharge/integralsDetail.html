#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡积分明细页面#end

#define css()
<style>
	label.layui-form-label{
		padding-right:0px;
	}
	div.in{
		padding-top: 10px;
	}
</style>
#end

#define content()
<div style="margin: 15px;">
	<div class="layui-row">
		<div class="layui-row">
			<table class="layui-table">
				<colgroup>
					<col width="25%">
					<col width="25%">
					<col width="25%">
					<col width="25%">
					<col>
				</colgroup>
				<thead>
					<tr>
						<th>赠送总积分</th>
						<th>兑换总积分</th>
						<th>充值总积分</th>
						<th>已扣总积分</th>
					</tr> 
				</thead>
				<tbody>
					<tr>
						<td>#(giveCount??)</td>
						<td>#(exchangeCount??)</td>
						<td>#(rechargeIntegrals??)</td>
						<td>#(hadDeductIntegrals??)</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div class="layui-row">
			<table id="integralsDetailTable" class="layui-table" lay-filter="integralsDetailTable"></table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="cardId" name="cardId" value="#(cardId??)">
				<button type="button" id="exportData" class="layui-btn">导出数据</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script>
layui.use(['form','laytpl','layer','table'], function() {
	var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,table=layui.table;

	table.render({
		id : 'integralsDetailTable',
		elem : "#integralsDetailTable" ,
		url : '#(ctxPath)/fina/recharge/cardIntegralsDetail' ,
		where: {cardId: "#(cardId??)"},
		height: 'full-190',
		cols : [[
			{type:'numbers'},
			{field:'',title:'类型',unresize:false,templet:"<div>{{ d.businessType=='system_give'?'系统赠送':d.businessType=='mall_exchange'?'积分兑换':'- -' }}</div>"} ,
			{field:'giveDate',title:'时间',unresize:false},
			{field:'countValue',title:'计算天数',unresize:false},
			{field:'proportionValue',title:'计算比例',unresize:false},
			{field:'integralValue',title:'积分',unresize:false}
		]] ,
		page : true,
		limit : 10
	});
	
	//导出会员卡
	$("#exportData").on("click",function () {
		var cardNumber = '#(cardNumber??)';
		if(table.cache.integralsDetailTable.length > 0){
			var url='#(ctxPath)/fina/recharge/exportData?cardId='+$('#cardId').val()+"&cardNumber="+cardNumber;
			window.location.href=url;
		}else{
			layer.msg('导出的内容为空', {icon: 2, offset: 'auto'});
		}
	});
});
</script>
#end