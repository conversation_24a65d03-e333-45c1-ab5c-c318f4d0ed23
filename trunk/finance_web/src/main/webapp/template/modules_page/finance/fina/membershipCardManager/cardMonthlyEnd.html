#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡收入月结#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<div id="monthlyEndTab" class="layui-tab layui-tab-card" lay-filter="monthlyEndTab">
			<ul class="layui-tab-title">
				<li id="monthlyEndSummary" class="layui-this">月结汇总</li>
				<li id="monthlyEndDetail">月结明细</li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<div class="layui-row">
					<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
						<div class="layui-inline">
							<label class="layui-form-label">月份</label>
							<div class="layui-input-inline" style="width: 100px;">
								<input type="text" id="yearMonth" name="yearMonth" class="layui-input" value="" lay-verify="required" placeholder="请选择月份" autocomplete="off">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-btn-group">
								<button type="button" id="search" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
								<button type="button" id="montylyEnd" class="layui-btn" lay-filter="montylyEnd" lay-submit="">月结</button>
							</div>
						</div>
					</form>
					</div>
					<div class="layui-row">
						<table id="cardMonthlyEndTable" lay-filter="cardMonthlyEndTable"></table>
					</div>
				</div>
				<div class="layui-tab-item">
					<div class="layui-row">
					<form class="layui-form" action="" lay-filter="layform2" id="frm2" method="post">
						<div class="layui-inline">
							<label class="layui-form-label">月份</label>
							<div class="layui-input-inline" style="width: 100px;">
								<input type="text" id="yearMonthDetail" name="yearMonth" class="layui-input" value="#(yearMonth??)" lay-verify="required" placeholder="请选择月份" autocomplete="off">
							</div>
						</div>
						<div class="layui-inline">
							<label class="layui-form-label">会员卡号</label>
							<div class="layui-input-inline">
								<input type="text" id="cardNumber" name="cardNumber" class="layui-input" value="" placeholder="请输入会员卡号" autocomplete="off">
							</div>
						</div>
						<div class="layui-inline">
							<div class="layui-btn-group">
								<button type="button" id="search2" class="layui-btn" lay-filter="search2" lay-submit="">搜索</button>
							</div>
						</div>
					</form>
					</div>
					<div class="layui-row">
						<table id="monthlyEndDetailTable" lay-filter="monthlyEndDetailTable"></table>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
	#shiroHasPermission("finance:cardType:setBtn")
	#end
	<a class="layui-btn layui-btn-xs" lay-event="detail">汇总明细</a>
</div>
</script>
<script type="text/javascript">
    layui.use(['table','form','laydate', 'element'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate=layui.laydate
        , element = layui.element
        ;

        //年月选择器
        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
        });
        
        //年月选择器2
        laydate.render({
            elem: '#yearMonthDetail'
            ,type: 'month'
            ,trigger:'click'
        });
        
        function tableLoad(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            table.render({
            	id:'cardMonthlyEndTable'
            	,elem: '#cardMonthlyEndTable'
                ,url : '#(ctxPath)/fina/cardmanager/cardMonthlyEndPage'
                ,method : 'POST'
                ,where : data
                ,height: 'full-150'
                ,cols: [[
                	{type:'numbers',title: '序号',unresize:false}
                    ,{field: 'yearMonth',title: '月份',unresize:false}
                    ,{field:'consumeTimes', title: '消费天数总数',unresize: false}
                    ,{field:'consumeAmount', title: '消费金额总数',unresize: false}
                    ,{field:'consumePoints', title: '消费点数总数',unresize: false}
                    ,{field:'consumeIntegrals', title: '消费积分总数',unresize: false}
                    ,{field:'consumeBeanCoupons', title: '消费豆豆券总数',unresize: false}
                    ,{field:'timesIncome', title: '总天数收入',unresize: false}
                    ,{field:'amountIncome', title: '总金额收入',unresize: false}
                    ,{field:'totalIncome', title: '总收入金额',unresize: false}
                    ,{title:'操作',width:100, fixed:'right',unresize:true,toolbar:'#toolBar'}
                ]]
                ,page : true
                ,limit : 10
                ,limits: [10,20,30]
                ,done: function(res, curr, count){
					layer.close(loadingIndex);
                }
            });
    		table.on('tool(cardMonthlyEndTable)',function(obj){
    			if(obj.event === 'detail'){
    				layerShow('明细','#(ctxPath)/fina/cardmanager/cardMonthlyEndDetail?yearMonth='+obj.data.yearMonth,'100%','100%');
    			}
    		});
        };
        
        function table2Load(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            table.render({
            	id:'monthlyEndDetailTable'
            	,elem: '#monthlyEndDetailTable'
                ,url : '#(ctxPath)/fina/cardmanager/monthlyEndDetailPage'
                ,method : 'POST'
                ,where : data
                ,height: 'full-150'
                ,cols: [[
                	{type:'numbers',title: '序号',unresize:false}
                    ,{field: 'yearMonth',title: '月份',unresize:false}
                    ,{field:'cardNumber', title: '会员卡号',unresize: false}
                    ,{field:'cardType', title: '卡类别',unresize: false}
                    ,{field:'accountBalance', title: '账面余额',unresize: false}
                    ,{field:'balance', title: '剩余金额',unresize: false}
                    ,{field:'consumeTimes', title: '剩余天数',unresize: false}
                    ,{field:'cardIntegrals', title: '剩余积分',unresize: false}
                    ,{field:'countPrice', title: '收入单价',unresize: false}
                    ,{field:'countDays', title: '收入天数',unresize: false}
                    ,{field:'countIntegrals', title: '收入积分',unresize: false}
                    ,{field:'countAmount', title: '收入金额',unresize: false}
                ]]
                ,page : true
                ,limit : 10
                ,limits: [10,20,30]
                ,done: function(res, curr, count){
                    $(".layui-table-box").css("height", $(".layui-table-box").css("height"));
					layer.close(loadingIndex);
				}
            });
        };

        tableLoad(null);

        table2Load({yearMonth:'#(yearMonth??)'});
        
        form.on("submit(search)",function(data){
            tableLoad(data.field);
            return false;
        });
        
        form.on("submit(search2)",function(data){
        	table2Load(data.field);
            return false;
        });
        
        form.on("submit(montylyEnd)",function(data){
        	$("#montylyEnd").attr("disabled",true);
			$("#montylyEnd").addClass("layui-btn-disabled");
        	util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/fina/cardmanager/cardMonthlyEnd',
				data: $('#frm').serialize(),
				notice:true,
				loadFlag: true,
				success : function(rep){
					console.log(rep);
					if(rep.state=='ok'){
						tableLoad(data.field);
					}
				},
				complete : function() {
					$("#montylyEnd").attr("disabled",false);
					$("#montylyEnd").removeClass("layui-btn-disabled");
				}
			});
            return false;
        });
        
      	//回车事件
// 		document.onkeydown = function(e){
// 			var ev =document.all ? window.event:e;  
// 			if(ev.keyCode==13) {
// 				$('#search').click();
// 				return false
// 			}
// 		}
    });

</script>
#end