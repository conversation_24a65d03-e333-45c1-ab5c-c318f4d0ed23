#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()操作记录页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <table id="operatorRecordTable" lay-filter="operatorRecordTable"></table>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'operatorRecordTable'
            ,elem : '#operatorRecordTable'
            ,method : 'POST'
            ,where : {"dataId": '#(dataId??)'}
            ,url : '#(ctxPath)/operate/pageTable'
            ,cols: [[
                {field: '', title: '操作类型', width:90, align: 'center', unresize:true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.OperateType::me(), "operateType")'}
                ,{field:'operateContent', title: '操作内容', align: 'center', unresize: true}
                ,{field:'', title: '操作时间',width:160, unresize: true, templet:"<div>{{dateFormat(d.operateTime,'yyyy-MM-dd HH:mm:ss')}}</div>"}
                ,{field:'operateByName', title: '操作人', width:100, align: 'center', unresize: true}
            ]]
            ,page : true
        });

    });
</script>
#end