#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()批量作废页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form class="layui-form" id="voidForm" action="">
        <div class="layui-row">
        	<label class="layui-form-label">作废备注</label>
            <div class="layui-input-block">
                <input type="text" id="voidRemark" name="voidRemark" class="layui-input" lay-verify="required" value="" placeholder="请输入作废备注">
            </div>
        </div>
        <div class="layui-row" style="height: 600px;overflow-y: auto;">
	        <table class="layui-table">
	            <thead>
	                <tr>
	                    <th width="12%">会员卡号</th>
	                    <th width="20%">卡类型</th>
	                    <th width="12%">卡主姓名</th>
	                    <th width="12%">剩余金额</th>
	                    <th width="12%">剩余天数</th>
	                    <th width="12%">合同金额</th>
	                    <th width="14%">开卡时间</th>
	                    <th width="6%">操作</th>
	                </tr>
	            </thead>
	            <tbody id="voidTbody">
	            </tbody>
	        </table>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
            	<input id="count" name="count" type="hidden" value="0">
                <button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button type="button" id="choice" class="layui-btn">选择会员卡</button>
                <button class="layui-btn" id="saveBtn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
#end

#define js()
<script id="voidTrTpl" type="text/html">
    <tr id="tr-{{d.idx}}" tr-index="{{d.idx}}">
        <td>
            <input type="text" id="cardNumber-{{d.idx}}" value="{{d.cardNumber}}" readonly class="layui-input">
        </td>
        <td>
            <input type="text" id="cardType-{{d.idx}}" value="{{d.cardType}}" readonly class="layui-input">
        </td>
        <td>
            <input type="text" id="memberName-{{d.idx}}" value="{{d.memberName}}" readonly class="layui-input">
        </td>
        <td>
            <input type="text" id="balance-{{d.idx}}" value="{{d.balance}}" readonly class="layui-input">
        </td>
        <td>
            <input type="text" id="consumeTimes-{{d.idx}}" value="{{d.consumeTimes}}" readonly class="layui-input" >
        </td>
        <td>
            <input type="text" id="contractAmount-{{d.idx}}" value="{{d.contractAmount}}" readonly class="layui-input" >
        </td>
        <td>
            <input type="text" id="openTime-{{d.idx}}" value="{{d.openTime}}" readonly class="layui-input">
        </td>
        <td>
			<input type="hidden" id="cardId-{{d.idx}}" name="void-[{{d.idx}}].id" value="{{d.cardId}}">
            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('{{d.idx}}')" >删除</button>
        </td>
    </tr>
</script>
<script>
layui.use(['form','laytpl','layer','table'], function() {
    var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,table=layui.table;

    addVoidTr=function(cardId,cardNumber,cardType,memberName,balance,consumeTimes,contractAmount,openTime){
        var tr=$("#voidTbody").children("tr");
        var flag=true;
        tr.each(function (i) {
            var id=$(this).attr("id");
            var index=id.substring(id.indexOf("-")+1,id.length);
            var trCardNumber=$("#cardNumber-"+index).val();
            if(trCardNumber===cardNumber){
                flag=false;
                return false;
            }
        })
        if(flag){
            laytpl(voidTrTpl.innerHTML).render({'idx':(Number($("#count").val())+1),'cardId':cardId,'cardNumber':cardNumber,'cardType':cardType,'memberName':memberName,'balance':balance,'consumeTimes':consumeTimes,'contractAmount':contractAmount,'openTime':openTime}, function(html){
                $("#count").val((Number($("#count").val())+1));
                $("#voidTbody").append(html);
                form.render()
            });
        }
        return flag;
    }

    del=function(index){
        $("#tr-"+index).remove();
    }
    
    $("#choice").on('click',function () {
        layerShow('选择会员卡','#(ctxPath)/fina/cardmanager/memberCardTable?actionType=batchVoid',1000,600);
    });

    form.on('submit(saveBtn)',function (obj) {
        var tr = $("#voidTbody").children("tr");
        if(tr.length<1){
            layer.msg('请至少选择一个会员卡再保存', {icon: 2, offset: 'auto'});
            return false;
        }
        $("#saveBtn").attr("disabled",true);
        $("#saveBtn").addClass("layui-btn-disabled");
        util.sendAjax({
            url:"#(ctxPath)/fina/cardmanager/batchVoid",
            type:'post',
            data:$("#voidForm").serialize(),
            notice:true,
            success:function(returnData){
                if(returnData.state==='ok'){
                    pop_close();
                }
            },
            complete :function(){
                $("#saveBtn").attr("disabled",false);
                $("#saveBtn").removeClass("layui-btn-disabled");
            }
        });
        return false;
    });
});
</script>
#end