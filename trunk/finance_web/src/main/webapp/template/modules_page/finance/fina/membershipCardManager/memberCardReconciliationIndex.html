#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()月入住单明细#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 3px;
    }
    /*     .laytable-cell-checkbox .layui-disabled.layui-form-checked i { */
    /*         background: #fff !important; */
    /*     } */
</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            <!--<div class="layui-inline">
                <label class="layui-form-label">查询类型:</label>
                <div class="layui-input-inline">
                    <select id="queryType" name="queryType" lay-filter="queryType">
                        <option value="month">按月</option>
                        <option value="day">按天</option>
                    </select>
                </div>
            </div>-->
            <div class="layui-inline">
                <label class="layui-form-label">基地专卡:</label>
                <div class="layui-input-inline" style="width:200px;">
                    <select name="cardId" id="cardId" lay-filter="cardId">
                        #for(cardRecord : cardRecordList)
                        <option value="#(cardRecord.id)">#(cardRecord.cardNumber)(#(cardRecord.fullName))</option>
                        #end
                    </select>
                </div>
            </div>


            <div class="layui-inline">
                <label class="layui-form-label">扣费单类型</label>
                <div class="layui-input-inline" style="width:200px;">
                    <select name="orderType" id="orderType" lay-filter="orderType">
                        <option value="1">旅居</option>
<!--                        <option value="2">旅游团</option>-->
                        <option value="3">机构</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline" style="width:200px;">
                    <input class="layui-input" name="yearMonth" id="yearMonth" autocomplete="off" >
                </div>
            </div>

            <div class="layui-inline">
                <div class="layui-input-inline">
                    <div class="layui-btn-group">
<!--                        <button type="button" id="search" class="layui-btn">查询</button>-->
                        <button type="button" id="edit" class="layui-btn">对账</button>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <table id="expiredCardTable" lay-filter="expiredCardTable"></table>
</div>
#getDictLabel("checkin_status")
#end

#define js()
<script type="text/html" id="toolBar">
    #[[
    {{#if(d.isReconciliation!='1'){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">异常</a>
    {{#}}}
    ]]#
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
            ,value:'#(yearMonth)'
            ,max:'#(maxYearMonth)'
            ,btns: ['confirm']
            ,done: function(value, date){
                locaTale({"cardId":$("#cardId").val(),"orderType":$("#orderType").val(),"yearMonth":value});
            }
        });


        form.on('select(queryType)', function(data){
            var queryType = data.value;
            if(queryType=='month'){
                $("#yearMonthDiv").show();
                $("#yearMonthDayDiv").hide();
            }else{
                $("#yearMonthDiv").hide();
                $("#yearMonthDayDiv").show();
            }
        });



        locaTale=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            let clos=[];
            if(data.orderType=='1'){
                clos=[
                    {type:'checkbox'}
                    ,{type: 'numbers', width:60, title: '序号',unresize:true}
                    ,{field:'base_name', title: '入住基地', width:180, align: 'center', unresize: true}
                    ,{field:'card_number', title: '会员卡号', width:100, align: 'center', unresize: true}
                    ,{field:'full_name', title: '持卡人', width:90, align: 'center', unresize: true}
                    ,{field:'name', title: '入住人', width:90, align: 'center', unresize: true}
                    ,{field:'followCount', title: '随行数', width:90, align: 'center', unresize: true}
                    ,{field:'checkin_type', title: '入住人类型', width:90, align: 'center', unresize: true,templet:function (d) {
                            if(d.checkin_type=='Member'){
                                return '会员';
                            }else if(d.checkin_type=='Outsider'){
                                return '散客';
                            }else if(d.checkin_type=='Staff'){
                                return '员工';
                            }else if(d.checkin_type=='Gov'){
                                return '领导';
                            }else if(d.checkin_type=='Relation'){
                                return '关系户、老板朋友';
                            }
                        }}
                    ,{field:'checkin_no', title: '入住号', width:140, align: 'center', unresize: true}
                    ,{field:'year_month', title: '费用月份', width:100, align: 'center', unresize: true }
                    ,{field:'start_time', title: '费用开始时间', width:140, align: 'center', unresize: true,templet:"<div>{{dateFormat(d.start_time,'yyyy-MM-dd')}}</div>" }
                    ,{field:'end_time', title: '费用结束时间', width:140, align: 'center', unresize: true,templet:"<div>{{dateFormat(d.end_time,'yyyy-MM-dd')}}</div>" }
                    ,{field:'actual_times', title: '扣卡天数', width:100, align: 'center', unresize: true }
                    ,{field:'actual_amount', title: '扣卡金额', width:100, align: 'center', unresize: true }
                    ,{field:'actual_integrals', title: '扣卡积分', width:100, align: 'center', unresize: true }

                    ,{field:'status', title: '扣卡日期', width:100, align: 'center', unresize: true,templet: function (d) {
                        return dateFormat(d.update_time,"yyyy-MM-dd");
                    }}
                    ,{field:'isReconciliation', width:100, title: '是否已对账', align: 'center', unresize: true,templet: function (d) {
                        if(d.isReconciliation=='1'){
                            return '是';
                        }else{
                            return '否';
                        }
                    }}
                    ,{field:'status', title: '对账结果', align: 'center', width:100, unresize: true,templet: function (d) {
                            if(d.status=='1'){
                                return '正常';
                            }else if(d.status=='0'){
                                return '异常';
                            }else{
                                return ''
                            }
                        }}
                    ,{field:'reconciliationTimes', title: '对账扣卡天数', align: 'center', width:120, unresize: true}
                    ,{field:'reconciliationAmount', title: '对账扣卡金额', align: 'center', width:120, unresize: true}
                    ,{field:'reconciliationIntegrals', title: '对账扣卡积分', align: 'center', width:120, unresize: true}
                    ,{field:'reconciliationRemark', title: '对账备注', align: 'center', width:120, unresize: true}
                    , {fixed: 'right', title: '操作', align: 'center', unresize:true, toolbar: '#toolBar'} //这里的toolbar值是模板元素的选择器

                ]
            }else if(data.orderType=='2'){
                clos=[
                    {type:'checkbox'}
                    ,{type: 'numbers', width:60, title: '序号',unresize:true}
                    ,{field:'tourist_name', title: '旅游线路', align: 'center' , unresize: true}
                    ,{field:'tourist_no', title: '旅游团号', align: 'center', width:140, unresize: true}
                    ,{field:'card_number', title: '会员卡号', align: 'center', unresize: true}
                    ,{field:'full_name', title: '持卡人', align: 'center', unresize: true}
                    ,{field:'name', title: '跟团人', align: 'center', width:140, unresize: true}

                    ,{field:'name', title: '入住人', align: 'center', width:100, unresize: true}
                    ,{field:'checkin_time', title: '开始时间', align: 'center', width:100, unresize: true,templet:"<div>{{dateFormat(d.checkin_time,'yyyy-MM-dd')}}</div>"}
                    ,{field:'checkout_time', title: '结束时间', align: 'center', width:100, unresize: true,templet:"<div>{{dateFormat(d.checkout_time,'yyyy-MM-dd')}}</div>"}
                    ,{field:'actual_times', title: '扣卡天数', align: 'center', width:100, unresize: true}
                    ,{field:'actual_amount', title: '扣卡金额', align: 'center', width:100, unresize: true}
                    ,{field:'actual_integrals', title: '扣卡积分', align: 'center', width:100, unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', width:100, unresize: true}
                    ,{field:'finance_remark', title: '财务扣卡备注', align: 'center', width:100, unresize: true}
                    ,{field:'status', title: '扣卡日期', align: 'center', unresize: true,templet:"<div>{{dateFormat(d.update_time,'yyyy-MM-dd')}}</div>"}
                    ,{field:'isReconciliation', title: '是否已对账', align: 'center', unresize: true,templet: function (d) {
                            if(d.isReconciliation=='1'){
                                return '是';
                            }else{
                                return '否';
                            }
                        }}
                    , {fixed: 'right', title: '操作', align: 'center', unresize:true, toolbar: '#toolBar'} //这里的toolbar值是模板元素的选择器

                ]
            }else if(data.orderType=='3'){
                clos=[
                    {type:'checkbox'}
                    ,{type: 'numbers', width:60, title: '序号',unresize:true}
                    ,{field:'base_name', title: '基地', width:180, align: 'center' , unresize: true}
                    ,{field:'card_number', title: '会员卡号', width:140, align: 'center', unresize: true}
                    ,{field:'full_name', title: '持卡人', width:100, align: 'center', unresize: true}
                    ,{field:'member_name', title: '入住人', width:100, align: 'center', unresize: true}
                    ,{field:'checkin_no', title: '入住号', width:180, align: 'center', width:140, unresize: true}
                    ,{field:'name', title: '扣费项目', align: 'center', width:140, unresize: true}
                    ,{field:'year_month', title: '费用月份', width:100, align: 'center', unresize: true }
                    ,{field:'start_date', title: '扣费项开始时间', align: 'center', width:110, unresize: true,templet:"<div>{{dateFormat(d.start_date,'yyyy-MM-dd')}}</div>"}
                    ,{field:'end_date', title: '扣费项结束时间', align: 'center', width:110, unresize: true,templet:"<div>{{dateFormat(d.end_date,'yyyy-MM-dd')}}</div>"}
                    ,{field:'actual_times', title: '扣卡天数', align: 'center', width:100, unresize: true}
                    ,{field:'actual_amount', title: '扣卡金额', align: 'center', width:100, unresize: true}
                    ,{field:'actual_integrals', title: '扣卡积分', align: 'center', width:100, unresize: true}
                    ,{field:'describe', title: '扣费项描述', align: 'center', width:100, unresize: true}
                    ,{field:'remark', title: '财务扣卡备注', align: 'center', width:100, unresize: true}
                    ,{field:'update_date', title: '扣卡日期', width:110, align: 'center', unresize: true,templet:"<div>{{dateFormat(d.update_date,'yyyy-MM-dd')}}</div>"}
                    ,{field:'isReconciliation', title: '是否已对账', width:110, align: 'center', unresize: true,templet: function (d) {
                            if(d.isReconciliation=='1'){
                                return '是';
                            }else{
                                return '否';
                            }
                        }}
                    ,{field:'status', title: '对账结果', width:110, align: 'center', unresize: true,templet: function (d) {
                            if(d.status=='1'){
                                return '<span>正常</span>';
                            }else if(d.status=='0'){
                                return '<span style="color: red">异常</span>';
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'reconciliationTimes', title: '对账扣卡天数', align: 'center', width:110, unresize: true}
                    ,{field:'reconciliationAmount', title: '对账扣卡金额', align: 'center', width:110, unresize: true}
                    ,{field:'reconciliationIntegrals', title: '对账扣卡积分', align: 'center', width:110, unresize: true}
                    ,{field:'reconciliationRemark', title: '对账备注', align: 'center', width:110, unresize: true}
                    , {fixed: 'right', title: '操作', align: 'center', unresize:true, toolbar: '#toolBar'} //这里的toolbar值是模板元素的选择器

                ]
            }

            table.render({
                id : 'expiredCardTable'
                ,elem : '#expiredCardTable'
                ,method : 'POST'
                ,where:data
                ,url : '#(ctxPath)/fina/cardmanager/memberCardReconciliationPageList'
                ,height: 'full-90'
                ,cellMinWidth: 80
                ,totalRow: true
                ,cols: [clos]
                ,page : false
                ,limit : 10
                ,limits : [10,20,30,40,50]
                ,done:function (){
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });

                    $.each($(".layui-table-body tr"),function (index,item) {
                        let isReconciliation=$(item).find("td[data-field='isReconciliation']").find("div").text();
                        if(isReconciliation=='是'){
                            $(item).find("td[data-field='0']").find("div").css("display","none");
                            $(item).find("td[data-field='0']").append('<div style="width: 48px;"></div>')
                        }
                    });
                    layer.close(loadingIndex);
                }
            });
        }
        table.on('tool(expiredCardTable)',function(obj){
            if (obj.event === 'edit') {
                layer.open({
                    type: 1,
                    title:"锁定会员卡",
                    area:['400px','500px'],
                    btn: ['确定', '取消'],
                    content: `<form class="layui-form layui-form-pane" action="" style="margin-top: 10px;">

                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">*</font>应扣卡天数</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="times" name="times" class="layui-input" lay-verify="required|number" value="0" placeholder="请输入存放位置">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">*</font>应扣卡金额</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="amount" name="amount" class="layui-input" lay-verify="required|number" value="0" placeholder="请输入存放位置">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">*</font>应扣卡积分</label>
                                    <div class="layui-input-inline">
                                        <input type="text" id="integrals" name="integrals" class="layui-input" lay-verify="required|number" value="0" placeholder="请输入存放位置">
                                    </div>
                                </div>
                                <div class="layui-form-item layui-form-text">
                                   <label class="layui-form-label"><font color="red">*</font>备注</label>
                                    <div class="layui-input-block">
                                     <textarea id="remark" name="remark" class="layui-textarea" lay-verify="required" placeholder="请输入内容"></textarea>
                                   </div>
                                 </div>
                               </form>`,
                    yes:function(index,layero){
                        layer.load();
                        let remark = $("#remark").val();
                        let times = $("#times").val();
                        let amount = $("#amount").val();
                        let integrals = $("#integrals").val();

                        if(remark!=null && remark!='' && times!='' && amount!='' && integrals!=''){
                            var data={'recordId':obj.data.id,'status':'0','times':obj.data.actual_times,'amount':obj.data.actual_amount,'integrals':obj.data.actual_integrals,
                                'actualTimes':times,'actualAmount':amount,'actualIntegrals':integrals,'type':$("#orderType").val(),
                                'yearMonth':$("#yearMonth").val(),'cardId':$("#cardId").val(),'remark':remark}
                            util.sendAjax({
                                url:"#(ctxPath)/fina/cardmanager/cardReconciliationExceptionSave",
                                type:'post',
                                data: data,
                                notice:true,
                                success:function(returnData){
                                    if(returnData.state==='ok'){
                                        reloadTable();
                                    }
                                    layer.close(index);
                                    layer.closeAll("loading");
                                },
                                unSuccess:function () {
                                    layer.closeAll("loading");
                                }
                            });
                        }else{
                            layer.closeAll("loading");
                            layer.msg('请填写必填项!',{icon:5});
                        }
                    }
                });
            }
        });


        reloadTable=function(){
            locaTale({"cardId":$("#cardId").val(),"orderType":$("#orderType").val(),"yearMonth":$("#yearMonth").val()});
        }
        reloadTable();

        form.on('select(orderType)',function (obj) {
            reloadTable();
        })
        form.on('select(cardId)',function (obj) {
            reloadTable();
        })

        // 搜索
        $("#search").click(function(){
            locaTale({"cardId":$("#cardId").val(),"orderType":$("#orderType").val()})
        });

        $("#edit").click(function () {
            var checkTable = table.checkStatus('expiredCardTable');
            console.log(checkTable);
            if(checkTable.length<=0){
                layer.msg('请先勾选记录',{icon:5,time:5000});
                return false;
            }
            let ids=[];
            $.each(checkTable.data,function (index,item) {
                if(item.isReconciliation=='0'){
                    ids.push(item.id);
                }
            });
            console.log(ids);
            if(ids.length<=0){
                layer.msg('请勾选需要对账的记录',{icon:5,time:5000});
                return false;
            }
            layer.load();
            util.sendAjax({
                url:'#(ctxPath)/fina/cardmanager/cardReconciliationSave',
                type:'post',
                data:{'type':$("#orderType").val(),'ids':JSON.stringify(ids),"cardId":$("#cardId").val(),"yearMonth":$("#yearMonth").val()},
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        console.log("成功");
                        layer.closeAll('loading');
                        reloadTable();
                    }
                },
                unSuccess:function () {
                    layer.closeAll('loading');
                }
            });

            return false;
        })

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#search').click();
                return false
            }
        }

        $("#export").click(function () {
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '导出中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            var url='#(ctxPath)/fina/expenseRecord/baseMonthCheckinDetailExport?queryType='+$('#queryType').val()+"&checkinType="+$("#checkinType").val()
                +"&baseId="+$('#baseId').val()+"&yearMonth="+$('#yearMonth').val()+"&yearMonthDay="+$('#yearMonthDay').val();
            window.location.href=url;
            setTimeout(function(){
                layer.close(loadingIndex);
            },5000);
        });


    });
</script>
<script type="text/html" id="actionBar">

</script>
#end
