#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡退款审核#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
	    <div class="layui-row">
	        <div class="layui-inline">
	            <label class="layui-form-label">申请编号：</label>
	            <div class="layui-input-inline">
	                <input type="text" id="applyNo" name="applyNo" placeholder="请输入申请编号" autocomplete="off" class="layui-input">
	            </div>
	        </div>
	        <div class="layui-inline">
				<label class="layui-form-label">状态</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="recordStatus" name="recordStatus">
						<option value="" selected="selected">全部</option>
						<option value="wait_submit">待提交</option>
						<option value="wait_examine">待审核</option>
						<option value="wait_pay">待支付</option>
						<option value="completed">已完成</option>
					</select>
				</div>
			</div>
	        <div class="layui-inline">
				<div class="layui-btn-group">
					<button type="button" id="searchBtn" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
					#shiroHasPermission("fina:refundExamine:addBtn")
						<button type="button" id="add" class="layui-btn">申请</button>
					#end
				</div>
			</div>
	    </div>
	    <div class="layui-row">
	        <table id="refundTable" lay-filter="refundTable"></table>
	    </div>
	</form>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
	#shiroHasPermission("fina:refundExamine:editBtn")
	#[[
	{{#if(d.recordStatus=='wait_submit'){}}
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:refundExamine:reviewBtn")
	#[[
	{{#if(d.recordStatus=='wait_examine'){}}
	<a class="layui-btn layui-btn-xs" lay-event="review">审核通过</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:refundExamine:payBtn")
	#[[
	{{#if(d.recordStatus=='wait_pay'){}}
	<a class="layui-btn layui-btn-xs" lay-event="pay">支付完成</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:refundExamine:viewBtn")
	#[[
	{{#if(d.recordStatus!='wait_submit'){}}
	<a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("fina:refundExamine:voidBtn")
	#[[
	{{#if(d.recordStatus=='wait_submit'){}}
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="void">作废</a>
	{{#}}}
	]]#
	#end
</div>
</script>
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
		, layer=layui.layer
        , laydate = layui.laydate
		;

		pageTableReload = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'refundTable',
				elem : "#refundTable" ,
				url : '#(ctxPath)/fina/refund/pageTable' ,
				where: data,
				height:'full-130',
				cols : [[
					{field:'', title:'序号', width:60, align:'center',unresize:true,templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"},
					{field:'applyNo',title:'申请编号',width:160,unresize:true},
					{field:'applyName',title:'申请人',width:160,unresize:true},
					{field:'',title:'申请时间',width:130,unresize:true,templet:"<div>{{dateFormat(d.applyTime,'yyyy-MM-dd')}}</div>"},
					// {field:'departmentName',title:'申请部门',width:160,unresize:true},
					{field:'',title:'状态',width:90,unresize:true,templet:function (d) {
						if(d.recordStatus=='wait_submit'){
							return "<span class='layui-badge layui-bg-blue'>待提交</span>";
						}else if(d.recordStatus=='wait_examine'){
							return "<span class='layui-badge layui-bg-black'>待审核</span>";
						}else if(d.recordStatus=='wait_pay'){
							return "<span class='layui-badge layui-bg-orange'>待支付</span>";
						}else if(d.recordStatus=='completed'){
							return "<span class='layui-badge layui-bg-green'>已完成</span>";
						}else{
							return '- -'
						}
					}},
					{field:'totalAmount',title:'合计总金额',width:100,unresize:true},
					{field:'detailStr',title:'退款明细',unresize:true},
					// {field:'',title:'是否提交',width:90,unresize:false,templet:function (d) {
					// 	if(d.hasSubmit=='0'){
					// 		return '否';
					// 	}else{
					// 		return '是';
					// 	}
					// }},
					// {field:'',title:'是否完成',width:90,unresize:false,templet:function (d) {
					// 	if(d.hasFinish=='0'){
					// 		return '否';
					// 	}else{
					// 		return '是';
					// 	}
					// }},
					{field:'',title:'创建时间',width:160,unresize:true,templet:"<div>{{dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss')}}</div>"},
					{title: '操作', fixed: 'right',toolbar:'#toolBar',unresize:true,width:110}
				]] ,
				page : true,
				limit : 10,
				done:function () {
					layer.close(loadingIndex);
					// 计算合计金额
					var totalAmount = 0;
					$('tbody tr', '.layui-table').each(function(){
						var amount = parseFloat($(this).find('td[data-field="totalAmount"]').text());
						totalAmount += isNaN(amount) ? 0 : amount;
					});
					// 添加合计行
					var $totalRow = $('<tr><td align="center">合计</td><td colspan="4"></td><td align="center">' + totalAmount.toFixed(2) + '</td><td colspan="3"></td></tr>');
					$('.layui-table tbody').append($totalRow);
				}
			}) ;
			table.on('tool(refundTable)',function(obj){
				if (obj.event === 'edit') {
					var url = "#(ctxPath)/fina/refund/edit?id="+obj.data.id;
					layerShow("退款申请",url,'100%','100%');
				}else if (obj.event === 'review') {
					var url = "#(ctxPath)/fina/refund/edit?id="+obj.data.id;
					layerShow("退款审核",url,'100%','100%');
				}else if (obj.event === 'pay') {
					var url = "#(ctxPath)/fina/refund/edit?id="+obj.data.id;
					layerShow("支付完成",url,'100%','100%');
				}else if (obj.event === 'view') {
					var url = "#(ctxPath)/fina/refund/edit?id="+obj.data.id;
					layerShow("退款查看",url,'100%','100%');
				}else if (obj.event === 'void') {
					layer.confirm("确定作废?",function(index){
						//loading层
						var loadingIndex = layer.load(2, { //icon支持传入0-2
							shade: [0.5, 'gray'], //0.5透明度的灰色背景
							content: '作废中...',
							success: function (layero) {
								layero.find('.layui-layer-content').css({
									'padding-top': '39px',
									'width': '60px'
								});
							}
						});
						util.sendAjax({
							url:"#(ctxPath)/fina/refund/updateRefund",
							type:'post',
							data:{id:obj.data.id, delFlag:'1'},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									pageTableReload({applyNo:$('#applyNo').val()});
								}
								layer.close(index);
							},
							complete: function () {
								layer.close(loadingIndex);
							}
						});
					});
				}
			});
		}

		pageTableReload(null);

        // 搜索按钮
		form.on("submit(search)",function(data){
			pageTableReload(data.field);
			return false;
		});

		//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#searchBtn').click();
				return false
			}
		}

		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/fina/refund/add";
			layerShow("退款申请",url,'100%','100%');
		});
    });

</script>
#end