#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()赠送详情页面#end

#define css()

#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'cardGiveRecordTable'
            ,elem : '#cardGiveRecordTable'
            ,method : 'POST'
            ,where : {"cardId":$("#cardId").val()}
            ,limit : 8
            ,limits : [8,20,30,40]
            ,url : '#(ctxPath)/fina/cardmanager/beanCouponsDetailPageList'
            ,cellMinWidth: 80
            ,cols: [[
                {field: 'roll_number', title: '卡券编号',align: 'center',unresize:true}
                ,{field:'roll_value', title: '初始面额值', align: 'center', unresize: true}
                ,{field:'balance_roll_value', title: '剩余可用面额值',sort: true, align: 'center', unresize: true}
                ,{field:'balance_roll_value', title: '是否有效',sort: true, align: 'center', unresize: true,templet:function (d) {
                        if(d.is_enable==='1'){
                            return '<span class="layui-badge layui-bg-red">失效</span>';
                        }else{
                            return '<span class="layui-badge layui-bg-green">有效</span>';
                        }
                    }}
                ,{field:'create_time', title: '创建时间',sort: true, unresize: true,templet:"<div>{{dateFormat(d.create_time,'yyyy-MM-dd')}}</div>"}
            ]]
            ,page : false
        });

        cardGiveRecordTableReload=function () {
            table.reload("cardGiveRecordTable",{"where":{"cardId":$("#cardId").val(),"cycle":$("#cycle").val()}});
        }

        form.on('select(cycle)',function () {
            cardGiveRecordTableReload();
        })

    });
</script>
#end

#define content()
<div class="layui-row" style="padding: 10px;">
    <input type="hidden" id="cardId" name="cardId" value="#(cardId??)">
    <table id="cardGiveRecordTable" lay-filter="cardGiveRecordTable"></table>
</div>
#getDictLabel("give_cycle")
#end