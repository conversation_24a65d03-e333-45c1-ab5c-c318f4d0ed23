#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡扣豆豆券页面#end

#define css()
<style>
	label.layui-form-label{
		padding-right:0px;
	}
	div.in{
		padding-top: 10px;
	}
</style>
#end

#define content()
<div style="margin: 15px;">
	<div class="demoTable">
		<!---->
		<form class="layui-form">
			<div class="layui-form-item">
				<label class="layui-form-label">会员卡号：</label>
				<div class="layui-input-inline in">#(card.cardNumber??)</div>
				<label class="layui-form-label" style="width:100px;">会员卡类型：</label>
				<div class="layui-input-inline in">#(type.cardType??)</div>
			</div>
			<div class="layui-form-item" style="margin-top: -15px;">
				<label class="layui-form-label" style="width:150px;">剩余豆豆券：</label>
				<div class="layui-input-inline in" style="width: 50px;">#(card.beanCoupons)</div>
			</div>
		</form>
		<!---->
		<form class="layui-form layui-form-pane" action="" method="post" id="deductForm">
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>所属基地</label>
				<div class="layui-input-inline">
					<select name="baseId" lay-search lay-verify="required">
						<option value="">请选择所属基地</option>
						#for(b : baseList)
							<option value="#(b.id)">#(b.baseName)</option>
						#end
					</select>
				</div>
				<label class="layui-form-label"><font color="red">*</font>所属应用</label>
				<div class="layui-input-inline">
					<select name="appNo" id="appNo" lay-search lay-verify="required">
						<option value="">请选择所属应用</option>
						#for(a : appList)
							<option value="#(a.appNo)">#(a.appName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>消费开始时间</label>
				<div class="layui-input-inline">
					<input type="text" id="startTime" name="startTime" class="layui-input" lay-verify="required" value="" placeholder="请输入消费开始时间">
				</div>

				<label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>消费结束时间</label>
				<div class="layui-input-inline">
					<input type="text" id="endTime" name="endTime" class="layui-input" lay-verify="required" value="" placeholder="请输入消费结束时间">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>扣除豆豆券</label>
				<div class="layui-input-inline">
					<input type="text" name="deductBeanCoupons" class="layui-input" lay-verify="required|checkAmount" value="" placeholder="请输入扣除豆豆券">
				</div>
				<label class="layui-form-label"><font color="red">*</font>订单号</label>
				<div class="layui-input-inline">
					<input type="text" name="orderNo" class="layui-input" lay-verify="required" value="" placeholder="请输入订单号">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">说明</label>
				<div class="layui-input-block">
					<textarea name="describe" class="layui-textarea" rows="5" placeholder="请输入说明"></textarea>
				</div>
			</div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<input type="hidden" id="cardId" name="cardId" value="#(card.id??)">
					<input type="hidden" id="cardNumber" name="cardNumber" value="#(card.cardNumber??)">
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button class="layui-btn" id="saveBtn" lay-submit="" lay-filter="saveBtn">确认扣卡</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script>
	layui.use(['form','laytpl','layer','laydate'], function() {
		var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,laydate=layui.laydate;

		//日期范围
		laydate.render({
			elem: '#startTime'
			,type:'date'
			,trigger:'click'
		});

		//日期范围
		laydate.render({
			elem: '#endTime'
			,type:'date'
			,trigger:'click'
		});

		//校验
		form.verify({
			checkNumber:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
				}
			},
			checkAmount:function(value){
				if(value != null){
					var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
					var reg2 = new RegExp("^[0-9]*$");
					if(!reg1.test(value) && !reg2.test(value)){
						return "只能输入数字和小数点后两位小数";
					}
				}
			}
		});


		//保存
		form.on('submit(saveBtn)', function(){
			$("#saveBtn").attr("disabled",true);
			$("#saveBtn").addClass("layui-btn-disabled");
			util.sendAjax({
				url:"#(ctxPath)/fina/deduct/save",
				type:'post',
				data:$("#deductForm").serialize(),
				notice:true,
				success:function(returnData){
					if(returnData.state==='ok'){
						pop_close();
// 						parent.reloadCardByCardNumber();
						//parent.findCardInfoById($("#cardId").val());
						/*parent.layui.table.reload('cardTable');
						$("#div1").html(returnData.data.consumeTimes);
						$("#div2").html(returnData.data.balance);*/
					}
				},
				complete :function(){
				$("#saveBtn").attr("disabled",false);
				$("#saveBtn").removeClass("layui-btn-disabled");
			}
			});
			return false;
		});
	});

</script>
#end