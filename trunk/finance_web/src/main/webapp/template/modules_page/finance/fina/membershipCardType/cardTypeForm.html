#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别表单页面#end

#define css()
<style>
</style>
#end

#define js()
<script type="text/javascript">
	layui.use(['form'],function(){
		var form = layui.form ;
		var $ = layui.$ ;

// 		form.render('radio');
		
		//校验
		form.verify({
			checkNumber:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
					if(value.length != 7 && value.length != 8){
						return "号段固定为7位或8位";
					}
				}
			},
			checkPrefix:function(value){
				if(value != null){
					var reg = new RegExp("^[0-9]*$");
					if(!reg.test(value)){
						return "只能输入数字";
					}
					if(value.length != 2){
						return "卡号前缀固定为2位";
					}
				}
			}
		});

		// 保存
		form.on('submit(confirmBtn)',function(){
			util.sendAjax({
				url:"#(ctxPath)/fina/cardType/save",
				type:'post',
				data:$("#frm").serialize(),
				notice:true,
				success:function(returnData){
					if(returnData.state==='ok'){
						pop_close();
						parent.cardTypeTableReload(null);
					}
				}
			});
			return false;
		}) ;
	}) ;
</script>
#end

#define content()
<div class="lay-row" style="margin-top: 15px;">
	<form id="frm" class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><font color="red">*</font>类别类型</label>
			<div class="layui-input-inline">
				<select name="type.typeCategory" lay-verify="required">
					<option value="">请选择</option>
					#dictOption("type_category", type.typeCategory??'', "")
				</select>
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><font color="red">*</font>类别分类</label>
			<div class="layui-input-inline">
				<select name="type.typeClassify"lay-verify="required">
					#statusOption(com.cszn.integrated.service.entity.status.TypeClassify::me(), type.TypeClassify??"")
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><span><font color="red">*</font>类别名称</span></label>
			<div class="layui-input-inline">
				<input type="text" name="type.cardType" autocomplete="off" placeholder="请输入名称" lay-verify="required" value="#(type.cardType??)" class="layui-input">
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><font color="red">*</font>选房费方案</label>
			<div class="layui-input-inline">
				<select name="type.markupSchemeId" lay-verify="required">
					#for(markupScheme : markupSchemeList)
					<option value="#(markupScheme.id)" #if(markupScheme.id==type.markupSchemeId??) selected #end>#(markupScheme.name)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><font color="red">*</font>是否开通金额</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isBalance" value="1" title="是" #(type != null ? ('1' == type.isBalance ? 'checked':''):'')>
				<input type="radio" name="type.isBalance" value="0" title="否" #(type != null ? ('0' == type.isBalance ? 'checked':''):'checked')>
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><font color="red">*</font>是否开通天数</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isConsumeTimes" value="1" title="是" #(type != null ? ('1' == type.isConsumeTimes ? 'checked':''):'')>
				<input type="radio" name="type.isConsumeTimes" value="0" title="否" #(type != null ? ('0' == type.isConsumeTimes ? 'checked':''):'checked')>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><font color="red">*</font>是否开通点数</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isConsumePoints" value="1" title="是" #(type != null ? ('1' == type.isConsumePoints ? 'checked':''):'')>
				<input type="radio" name="type.isConsumePoints" value="0" title="否" #(type != null ? ('0' == type.isConsumePoints ? 'checked':''):'checked')>
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><font color="red">*</font>是否滚动积分</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isIntegral" value="1" title="是" #(type != null ? ('1' == type.isIntegral ? 'checked':''):'')>
				<input type="radio" name="type.isIntegral" value="0" title="否" #(type != null ? ('0' == type.isIntegral ? 'checked':''):'checked')>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><font color="red">*</font>是否可预订</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isBooking" value="1" title="是" #(type != null ? ('1' == type.isBooking ? 'checked':''):'')>
				<input type="radio" name="type.isBooking" value="0" title="否" #(type != null ? ('0' == type.isBooking ? 'checked':''):'checked')>
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><font color="red">*</font>是否充值豆豆</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isRechargeBean" value="1" title="是" #(type != null ? ('1' == type.isRechargeBean ? 'checked':''):'')>
				<input type="radio" name="type.isRechargeBean" value="0" title="否" #(type != null ? ('0' == type.isRechargeBean ? 'checked':''):'checked')>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><font color="red">*</font>不发验证码</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isNotSendSms" value="1" title="是" #(type != null ? ('1' == type.isNotSendSms ? 'checked':''):'')>
				<input type="radio" name="type.isNotSendSms" value="0" title="否" #(type != null ? ('0' == type.isNotSendSms ? 'checked':''):'checked')>
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><font color="red">*</font>不签入住协议</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isNotSignAgreement" value="1" title="是" #(type != null ? ('1' == type.isNotSignAgreement ? 'checked':''):'')>
				<input type="radio" name="type.isNotSignAgreement" value="0" title="否" #(type != null ? ('0' == type.isNotSignAgreement ? 'checked':''):'checked')>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 200px !important;"><font color="red">*</font>是否校验卡天数/积分/金额</label>
			<div class="layui-input-inline">
				<input type="radio" name="type.isVerifyTimes" value="1" title="是" #(type != null ? ('1' == type.isVerifyTimes ? 'checked':''):'checked')>
				<input type="radio" name="type.isVerifyTimes" value="0" title="否" #(type != null ? ('0' == type.isVerifyTimes ? 'checked':''):'')>
			</div>
			<label class="layui-form-label" style="width: 130px !important;"><span><font color="red">*</font>最大入住人数</span></label>
			<div class="layui-input-inline">
				<input type="text" name="type.checkinMax" autocomplete="off" placeholder="请输入最大入住人数" lay-verify="required|number" value="#(type.checkinMax??)" class="layui-input">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">备注</label>
			<div class="layui-input-block">
				<textarea name="type.remark" placeholder="请输入备注内容" class="layui-textarea">#(type.remark??)</textarea>
			</div>
		</div>
		<div class="layui-form-item" style="margin-bottom:60px;"></div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" name="type.id" value="#(type.id??)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button type="button" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end