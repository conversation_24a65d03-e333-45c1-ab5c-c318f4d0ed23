#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()充值名单页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form id="rechargeRecordForm" class="layui-form layui-form-pane" action="" lay-filter="layform" method="post">
		<div class="layui-row" style="margin-top: 10px;">
			<div class="layui-inline">
				<label class="layui-form-label"><font color="red">*</font>充值类别</label>
				<div class="layui-input-inline" style="width:130px">
					<select name="rechargeCategory" lay-verify="required">
						<option value="">请选择充值类别</option>
						#getDictList("recharge_category")
							<option value="#(key)" #(model != null ?(key == model.rechargeCategory ? 'selected':''):'')>#(value)</option>
						#end
					</select>
				</div>
			</div>
		</div>
		<div class="layui-row">
			<label class="layui-form-label"><font color="red">*</font>充值标题</label>
			<div class="layui-input-block">
				<input type="text" name="rechargeTitle" value="#(model.rechargeTitle??'')" class="layui-input" lay-verify="required" placeholder="请输入充值标题" autocomplete="off">
			</div>
		</div>
		<div class="layui-row">
			<label class="layui-form-label">充值备注</label>
			<div class="layui-input-block">
				<input type="text" name="remarks" value="#(model.remarks??'')"  class="layui-input"placeholder="请输入充值备注" autocomplete="off">
			</div>
		</div>
		<div class="layui-row" style="height: 500px;overflow-y: auto;">
			<table class="layui-table">
				<colgroup>
					<col width="18%">
					<col width="18%">
					<col width="18%">
					<col width="18%">
					<col width="18%">
					<col width="10%">
				</colgroup>
				<thead>
                    <th>姓名</th>
                    <th>卡号</th>
                    <th>充值编号</th>
                    <th>充值类型</th>
                    <th>充值额度</th>
                    <th>操作</th>
                </thead>
				<tbody id="rechargeList">
					#for(rList : rechargeList??)
		        	    <tr id="recharge#(for.index+1)">
					        <td>
								<input type="text" id="memberName#(for.index+1)" name="rechargeList[#(for.index+1)].memberName" class="layui-input" value="#(rList.memberName??)" lay-verify="required" placeholder="请输入姓名" autocomplete="off">
					        </td>
					        <td>
					            <input type="text" id="cardNumber#(for.index+1)" name="rechargeList[#(for.index+1)].cardNumber" class="layui-input" value="#(rList.cardNumber??)" lay-verify="required" placeholder="请输入卡号" autocomplete="off">
					        </td>
							<td>
					            <input type="text" id="rechargeNo#(for.index+1)" name="rechargeList[#(for.index+1)].rechargeNo" class="layui-input" value="#(rList.rechargeNo??)" placeholder="请输入充值编号" autocomplete="off">
					        </td>
							<td>
								<select id="rechargeType{{d.idx}}" name="rechargeList[#(for.index+1)].rechargeType" lay-verify="required">
									<option value="">全部</option>
									#statusOption(com.cszn.integrated.service.entity.status.RechargeType::me(), rList.rechargeType??"")
								</select>
							</td>
							<td>
					            <input type="text" id="rechargeValue#(for.index+1)" name="rechargeList[#(for.index+1)].rechargeValue" class="layui-input" value="#(rList.rechargeValue??0)" lay-verify="required" placeholder="请输入充值额度" autocomplete="off">
					        </td>
					        <td>
								<input type="hidden" id="id#(for.index+1)" name="rechargeList[#(for.index+1)].id" value="#(rList.id??)">
					            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('recharge#(for.index+1)')">删除</button>
					        </td>
					    </tr>
			        #end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??)" />
				<input type="hidden" id="rechargeCount" name="rechargeCount" value="#(rechargeList.size()??0)">
<!-- 				20240217暂时关闭，点击会报错 -->
<!-- 				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button> -->
				<button type="button" id="addBtn" class="layui-btn">添加</button>
				<button id="saveBtn" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script id="rechargeListTrTpl" type="text/html">
    <tr id="recharge{{d.idx}}">
        <td>
			<input type="text" id="memberName{{d.idx}}" name="rechargeList[{{d.idx}}].memberName" class="layui-input" value="" lay-verify="required" placeholder="请输入姓名" autocomplete="off">
        </td>
        <td>
            <input type="text" id="cardNumber{{d.idx}}" name="rechargeList[{{d.idx}}].cardNumber" class="layui-input" value="" lay-verify="required" placeholder="请输入卡号" autocomplete="off">
        </td>
		<td>
            <input type="text" id="rechargeNo{{d.idx}}" name="rechargeList[{{d.idx}}].rechargeNo" class="layui-input" value="" placeholder="请输入充值编号" autocomplete="off">
        </td>
		<td>
			<select id="rechargeType{{d.idx}}" name="rechargeList[{{d.idx}}].rechargeType" lay-verify="required">
				<option value="">全部</option>
			</select>
		</td>
		<td>
            <input type="text" id="rechargeValue{{d.idx}}" name="rechargeList[{{d.idx}}].rechargeValue" class="layui-input" value="" lay-verify="required" placeholder="请输入充值额度" autocomplete="off">
        </td>
        <td>
			<input type="hidden" id="id{{d.idx}}" name="rechargeList[{{d.idx}}].id" value="">
            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('recharge{{d.idx}}')">删除</button>
        </td>
    </tr>
</script>
<script type="text/javascript">
layui.use(['form', 'laytpl'],function(){
	var form = layui.form;
	var laytpl = layui.laytpl;
	var modelId = '#(model.id??)';
	var $ = layui.$;
	
	//添加模板方法
    addRechargeListTpl = function (targetId, addTpl, idx) {
        $('#rechargeCount').val(parseInt(idx) + 1);
        laytpl(addTpl).render({
            'idx': (parseInt(idx) + 1)
        }, function (html) {
            targetId.append(html);
        });
        #for(mapObj : rechargeTypeMap??)
            $('#rechargeType' + (parseInt(idx) + 1)).append('<option value="#(mapObj.key??)">#(mapObj.value??)</option>');
        #end
        form.render('select');
    };
    
	//添加按钮点击事件
    $('#addBtn').on('click', function () {
    	addRechargeListTpl($('#rechargeList'), rechargeListTrTpl.innerHTML, $('#rechargeCount').val());
    });
	
    del = function (trId) {
        $('#' + trId).remove();
    }

	//保存
	form.on('submit(saveBtn)', function(){
		var url = "#(ctxPath)/fina/rechargeRecord/save";
		util.sendAjax ({
			type: 'POST',
			url: url,
			data: $("#rechargeRecordForm").serialize(),
			notice: true,
			loadFlag: false,
			success : function(rep){
				if(rep.state=='ok'){
					pop_close();
					parent.lotteryLoad("recordTable",null);
				}
			},
			complete : function() {
			}
		});
		return false;
	});
});
</script>
#end