#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算清单#end

#define css()
<style>
    .my-skin .layui-layer-btn a {
        background-color: #009688;
        border: 1px solid #009688;
        color: #FFF;
    }
    .layui-table-cell {
        height: 28px;
        line-height: 28px;
        padding: 0 5px;
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        box-sizing: border-box;
    }
</style>
#end

#define content()
<div>
    <input type="hidden" id="no" value="#(touristNo??)"/>
    <input type="hidden" id="type" value="#(type)"/>
    <div class="layui-row" style="padding-top: 20px;">
        <div class="layui-inline">
            <label class="layui-form-label" style="width:80px;padding-left:0px;">会员卡号：</label>
            <div class="layui-input-inline">
                <input type="text" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width:80px;padding-left:5px;">所属会员：</label>
            <div class="layui-input-inline">
                <input type="text" id="fullName" name="fullName" placeholder="请输入所属会员" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label" style="width:80px;padding-left:5px;">跟团人：</label>
            <div class="layui-input-inline">
                <input type="text" id="name" name="name" placeholder="请输入所属会员" autocomplete="off" class="layui-input">
            </div>
        </div>
        <button id="searchBtn" class="layui-btn">搜索</button>
        <button id="clearBtn" class="layui-btn">清空</button>
        <button id="printBtn" class="layui-btn">导出</button>
        <button id="changeCardBtn" class="layui-btn">换卡</button>
        <!--<button id="addBtn" class="layui-btn">添加</button>-->
    </div>
    <table id="touristExpenseTable" lay-filter="touristExpenseTable"></table>
    <div class="layui-form-footer" id="footer" #if(type??=='look') style="display: none;" #end>
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">批量结算</button>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="inputTpl">
    #[[<div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualTimes" min="0" value="{{d.actualTimes}}"
                   lay-verify="checkNumber|required" onkeyup="calculateActualTimes(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_days' || d.settleStatus=='1' ){}} readonly {{#}}}>
        </div>
    </div>]]#
</script>
<script type="text/html" id="inputATpl">
    #[[<div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualAmount" min="0" value="{{d.actualAmount}}"
                   lay-verify="checkAmount|required" onkeyup="calculateActualAmount(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_money' || d.settleStatus=='1' ){}} readonly {{#}}}>
        </div>
    </div>]]#
</script>
<script type="text/html" id="inputPTpl">
    #[[<div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualPoints" min="0" value="{{d.actualPoints}}"
                   lay-verify="checkAmount|required" onkeyup="calculateActualPoints(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_points' || d.settleStatus=='1' ){}} readonly {{#}}}>
        </div>
    </div>]]#
</script>
<script type="text/html" id="inputITpl">
    #[[
    <div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="actualIntegrals" min="0" value="{{d.actualIntegrals}}"
                   lay-verify="checkAmount|required" onkeyup="calculateActualIntegrals(this)" class="layui-input" {{#if(d.deductWay!='deduct_by_days' || d.isIntegral!='1' || d.settleStatus=='1' ){}} readonly {{#}}}>
        </div>
    </div>

    ]]#
</script>

<script type="text/html" id="remarkTpl">
    #[[
    <div class="layui-inline" >
        <div class="layui-input-inline">
            <input type="text" name="financeRemark" min="0" value="{{d.financeRemark}}"
                   lay-verify="" onkeyup="" class="layui-input" {{#if( d.settleStatus=='1' ){}} readonly {{#}}}>
        </div>
    </div>

    ]]#
</script>
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
    #[[
    {{#if(d.settleStatus == '0'){}}
    <a class="layui-btn layui-btn-xs" lay-event="split">拆分</a>
    {{#}}}
    ]]#
    #if(type!='look')
    #shiroHasPermission("finance:touristSettle:changeCard")

    #[[
	    {{#if(d.settleStatus == '0'){}}
	    	<a class="layui-btn layui-btn-xs" lay-event="changeCard">换卡</a>
	    {{#}}}
    ]]#

    #end
    #shiroHasPermission("finance:touristSettle:singleSettle")
    #[[
	    {{#if(d.settleStatus == '0'){}}
	    	<a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="singleSettle">单条结算</a>
	    {{#}}}
    ]]#
    #end
    <a class="layui-btn layui-btn-xs" lay-event="cehckinDetail">入住详情</a>
    #shiroHasPermission("finance:touristSettle:singleDel")
    #[[
	    {{#if(d.settleStatus == '0'){}}
			<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="singleDel">单条作废</a>
	    {{#}}}
    ]]#
    #end

    #else
    <a class="layui-btn layui-btn-xs" lay-event="cehckinDetail">入住详情</a>

    #end

</div>
</script>
<script>
    layui.use(['form','layer','table','laytpl'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laytpl=layui.laytpl;

        var map = {};

        //校验
        form.verify({
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            },
            checkAmount:function(value){
                if(value != null){
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if(!reg1.test(value) && !reg2.test(value)){
                        return "只能输入数字和小数点后两位小数";
                    }
                }
            }
        });

        rowUpdate = function(tableId, index, data) {
            layui.$.extend(table.cache[tableId][index], data);
        }

        //获取所填实扣天数
        calculateActualTimes=function(obj){
            var actualTimesUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualTimes:actualTimesUp};
            rowUpdate('touristExpenseTable',index,data);
        }

        //获取所填实扣金额
        calculateActualAmount=function(obj){
            var actualAmountUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualAmount:actualAmountUp};
            rowUpdate('touristExpenseTable',index,data);
        }
        calculateActualPoints=function(obj){
            var actualPointsUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualPoints:actualPointsUp};
            rowUpdate('touristExpenseTable',index,data);
        }
        calculateActualIntegrals=function(obj){
            var actualIntegralsUp = obj.value;
            var index = $(obj).parents("tr").attr("data-index");
            var data = {actualIntegrals:actualIntegralsUp};
            rowUpdate('touristExpenseTable',index,data);
        }


        touristLoad(null);

        /*sd=form.on("submit(search)",function(data){
            touristLoad(data.field);
            return false;
        });*/

        tableReloadNotPage = function(tableId, tableParams){
            if(tableId!=null && tableId!=''){
                layui.table.reload(tableId, {
                    //设定异步数据接口的额外参数
                    where : tableParams
                });
            }
        }

        // 搜索消费记录按钮
        $('#searchBtn').on('click', function(){
            reloadTable();
        });

        reloadTable=function(){
            var param = {cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),name:$("#name").val()};
            tableReloadNotPage('touristExpenseTable',param);
        }

        //清空重新加载
        $("#clearBtn").on('click',function(){
            $("#cardNumber").val('');
            $("#fullName").val('');
            $("#name").val('');
            touristLoad(null);
        });
        
        $("#printBtn").on('click',function () {
            window.location.href='#(ctxPath)/fina/tourist/touristExport?touristNo='+$("#no").val();
        })

        $("#addBtn").on('click',function () {
            var url = "#(ctxPath)/fina/tourist/addForm?touristNo=" + $("#no").val() ;
            pop_show("添加",url,750,450);
        });

        function touristLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'touristExpenseTable'
                ,elem:'#touristExpenseTable'
                ,method:'post'
                ,where:data
                ,height:'full-150'
                ,url:'#(ctxPath)/fina/tourist/findTouristExpenseList?touristNo='+$("#no").val()
                ,cols:[[
                    {type:'checkbox', fixed: 'left'}
                    ,{type: 'numbers', width:100, title: '序号',unresize:true,width: 60}
                    ,{field:'cardNumber', title: '会员卡号信息', align: 'center', unresize: true,width:140,templet:function (d) {
                            var str="";
                            if(d.cardNumber != null && d.cardNumber != ''){
                                str = d.cardNumber;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.fullName != null && d.fullName != null){
                                str += d.fullName;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'cardType', title: '卡类别', align: 'center', unresize: true,width:90}
                    ,{field:'originalCardNumber', title: '原始会员卡号信息', align: 'center', unresize: true,width:140,templet:function (d) {
                            var str="";
                            if(d.originalCardNumber != null && d.originalCardNumber != ''){
                                str = d.originalCardNumber;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.originalMember != null && d.originalMember != null){
                                str += d.originalMember;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'name', title: '跟团人', align: 'center', unresize: true,width:90,templet:function (d) {
                            return d.name;
                        }}
                    ,{field:'officeName', title: '分公司', align: 'center', unresize: true,width:150}
                    ,{field:'', title: '单价', sort: false, align: 'center', unresize: true,width:90,templet: "<div>{{ d.price?d.price + '元': '- -' }}</div>"}
                    ,{field:'withholdTimes', title: '锁定天数', sort: false, align: 'center', unresize: true,width:80,templet: "<div>{{ d.withholdTimes?d.withholdTimes + '天': '- -' }}</div>"}
	        		#if(type=='look')
	                ,{field:'actualTimes', title: '实扣天数', align: 'center',unresize: true,width:80,templet: "<div>{{ d.actualTimes?d.actualTimes + '天': '- -' }}</div>"}
	        		#end
	        		#if(type!='look')
	                ,{field:'actualTimes', title: '实扣天数', align: 'center',unresize: true,width:80,templet:'#inputTpl'}
	        		#end
                	,{field:'withholdAmount', title: '锁定金额', sort: false, align: 'center', unresize: true,width:90,templet: "<div>{{ d.withholdAmount?d.withholdAmount + '元': '- -' }}</div>"}
	        		#if(type=='look')
	                ,{field:'actualAmount', title: '实扣金额', align: 'center',unresize: true,width:95,templet: "<div>{{ d.actualAmount?d.actualAmount + '元': '- -' }}</div>"}
	        		#end
	        		#if(type!='look')
	                ,{field:'actualAmount', title: '实扣金额', align: 'center',unresize: true,width:95,templet:'#inputATpl'}
	        		#end
                	//,{field:'withholdPoints', title: '锁定点数', sort: false, align: 'center', unresize: true,width:80,templet: "<div>{{ d.withholdPoints?d.withholdPoints + '点数': '- -' }}</div>"}
	        		#if(type=='look')
	                //,{field:'actualPoints', title: '实扣点数', align: 'center',unresize: true,width:80,templet: "<div>{{ d.actualPoints?d.actualPoints+ '点数': '- -' }}</div>"}
	        		#end
		        	#if(type!='look')
	                //,{field:'actualPoints', title: '实扣点数', align: 'center',unresize: true,width:80,templet:'#inputPTpl'}
		        	#end
                	,{field:'withholdIntegrals', title: '锁定积分', sort: false, align: 'center', unresize: true,width:80,templet: "<div>{{ d.withholdIntegrals?d.withholdIntegrals + '积分': '- -' }}</div>"}
	        		#if(type=='look')
	                ,{field:'actualIntegrals', title: '实扣积分', align: 'center',unresize: true,width:80,templet: "<div>{{ d.actualIntegrals?d.actualIntegrals + '积分': '- -' }}</div>"}
	        		#end
	        		#if(type!='look')
	                ,{field:'actualIntegrals', title: '实扣积分', align: 'center',unresize: true,width:80,templet:'#inputITpl'}
	        		#end
                	,{field:'remark', title: '备注', align: 'center', unresize: false,style:"font-size:10px;"}
                	,{field:'settleStatusStr', title: '状态', align: 'center', unresize: false,width:70,templet:"<div>{{d.settleStatus == '0'?'<span class='layui-badge layui-bg-green'>未结算</span>':d.settleStatus == '1'?'<span class='layui-badge'>已结算</span>':d.settleStatus == '2'?'<span class='layui-badge layui-bg-cyan'>作废</span>':'- -'}}</div>"}
                	,{field:'financeRemark', title: '财务备注', sort: false, align: 'center', unresize: true,width:200,style:"font-size:10px;",templet:'#remarkTpl'}
               /* 	,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,width:180,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}*/
                    ,{field:'id', title: 'id', align: 'center', unresize: true,width:200,style:"font-size:10px;"}
                    ,{field:'settleStatus', title: 'settleStatus', align: 'center', unresize: true,width:200,style:"font-size:10px;"}
                    #if(type!='look')
	                ,{fixed: 'right',title: '操作', width: 255, align: 'center', unresize: true, toolbar: '#actionBar'}
	                #else
                    ,{fixed: 'right',title: '操作', width: 100, align: 'center', unresize: true, toolbar: '#actionBar'}
	        		#end
        		]],
            	done:function(res, curr, count) {
                    $("[data-field='id']").css('display', 'none');    //隐藏
                    $("[data-field='settleStatus']").css('display', 'none');    //隐藏



                    //合计行
                    var totalPrices=0;
                    var totalLockTime=0;
                    var totalTime=0;
                    var totalLockAmount=0;
                    var totalAmount=0;
                    var totalLockPoint=0;
                    var totalPoint=0;
                    var totalLockIntegral=0;
                    var totalIntegral=0;
                    $.each(res.data,function(index,item){
                        totalPrices = Number(item.price)+totalPrices;
                        totalLockTime=Number(item.withholdTimes)+totalLockTime;
                        totalTime=Number(item.actualTimes)+totalTime;
                        totalLockAmount=Number(item.withholdAmount)+totalLockAmount;
                        totalAmount=Number(item.actualAmount)+totalAmount;
                        totalLockPoint=Number(item.withholdPoints   )+totalLockPoint;
                        totalPoint=Number(item.actualPoints)+totalPoint;
                        totalLockIntegral=Number(item.withholdIntegrals)+totalLockIntegral;
                        totalIntegral=Number(item.actualIntegrals)+totalIntegral;
                    });
                    if(res.count>0){
                        $(".layui-table-main .layui-table").append('<tr data-index="" class="">' +
                            '<td></td>' +
                            '<td data-field=""><div class="layui-table-cell laytable-cell-1-0 laytable-cell-numbers">合计</div></td>' +
                            '<td  align="center" ><div class="layui-table-cell "></div></td>' +
                            '<td align="center" data-content=""><div class="layui-table-cell "></div></td>' +
                            '<td data-field="" align="center"><div class="layui-table-cell "></div></td>' +
                            '<td data-field="" align="center"><div class="layui-table-cell "></div></td>' +
                            '<td data-field="" align="center"><div class="layui-table-cell "></div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell ">'+totalPrices.toFixed(2)+'</div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell ">'+totalLockTime.toFixed(2)+'</div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell "> '+totalTime.toFixed(2)+' </div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell ">'+totalLockAmount.toFixed(2)+'</div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell ">'+totalAmount.toFixed(2)+'</div></td>' +
                            // '<td data-field="" align="center" ><div class="layui-table-cell">'+totalLockPoint.toFixed(2)+'</div></td>' +
                            // '<td data-field="" align="center" ><div class="layui-table-cell ">'+totalPoint.toFixed(2)+'</div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell">'+totalLockIntegral.toFixed(2)+'</div></td>' +
                            '<td data-field="" align="center" ><div class="layui-table-cell">'+totalIntegral.toFixed(2)+'</div></td>' +
                            '<td data-field="" align="center" style="font-size:10px;"></td>' +
                            '<td data-field="" align="center" data-content="0"></td>' +
                            '<td data-field="" align="center" data-off=""></td>' +
                            '<td data-field="" align="center" data-off=""></td>' +
                            '</tr>');

                        $(".layui-table-fixed-r .layui-table-body table tbody").append('<tr data-index="'+res.count+'"><td>' +
                            '<div class="layui-table-cell laytable-cell-1-'+res.count+'"> <div class="layui-btn-group">  ' +
                            /*'<a class="layui-btn layui-btn-xs" lay-event=""></a>   ' +*/
                            '</div> </div></td>' +
                            '</tr>');
                    }
                    layer.close(loadingIndex);

                    //相同列合并
                    merge(res);


                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });





                    $.each(res.data,function(index,item){
                        if(item.settleStatus=='1'){
                            //console.log($(".layui-table-fixed-l .layui-table-body table tbody tr[data-index='"+index+"']").html());
                            //$(".layui-table-fixed-l .layui-table-body table tbody tr[data-index='"+index+"']").find('td').eq(0).find('input[type="checkbox"]').prop('disabled',true);
                            //$(".layui-table-fixed-l .layui-table-body table tbody tr[data-index='"+index+"']").find('td').eq(0).find('.laytable-cell-checkbox').css('display','none');
                            $(".layui-table-fixed-l .layui-table-body table tbody tr[data-index='"+index+"']").find('td').eq(0).find('.laytable-cell-checkbox').html('');
                            form.render("checkbox");
                        }
                    })


                }
        });
        };

        function merge(res) {
            var data = res.data;
            var mergeIndex = 0;//定位需要添加合并属性的行数
            var mark = 1; //这里涉及到简单的运算，mark是计算每次需要合并的格子数
            var columsName = ['name'];//需要合并的列名称
            var columsIndex = [5];//需要合并的列索引值

            for (var k = 0; k < columsName.length; k++) { //这里循环所有要合并的列
                var trArr = $(".layui-table-body>.layui-table").find("tr");//所有行

                for (var i = 1; i < res.data.length; i++) { //这里循环表格当前的数据
                    var tdCurArr = trArr.eq(i).find("td").eq(columsIndex[k]);//获取当前行的当前列
                    var tdPreArr = trArr.eq(mergeIndex).find("td").eq(columsIndex[k]);//获取相同列的第一列
                    if (data[i][columsName[k]] === data[i-1][columsName[k]] && data[i]['expenseId'] === data[i-1]['expenseId']) { //后一行的值与前一行的值做比较，相同就需要合并
                        mark += 1;
                        tdPreArr.each(function () {//相同列的第一列增加rowspan属性
                            $(this).attr("rowspan", mark);
                        });
                        tdCurArr.each(function () {//当前行隐藏
                            $(this).css("display", "none");
                        });
                    }else {
                        mergeIndex = i;
                        mark = 1;//一旦前后两行的值不一样了，那么需要合并的格子数mark就需要重新计算
                    }
                }
                mergeIndex = 0;
                mark = 1;
            }
        }


        $("#changeCardBtn").on('click',function () {

            var tableCheckStatus = table.checkStatus('touristExpenseTable');

            let checkStatus={data:[]};
            $.each(tableCheckStatus.data,function (index,item) {
                if(item.settleStatus=='0'){
                    checkStatus.data.push(item)
                }

            })
            if(checkStatus.data.length==0){
                layer.msg('请先勾选要换卡的记录', {icon: 2, offset: 'auto'});
                return false;
            }

            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "查看详情",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['600px;','300px;'],
                content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                    "            <p id=\"cardInfoP\" style=\"padding-left: 110px;margin-top: 10px;\">&nbsp;</p>" +
                    "            <div class=\"layui-form-item\" style='margin-top: 20px;'>\n" +
                    "                <label class=\"layui-form-label\"><font color='red'>*</font>会员卡号</label>\n" +
                    "                <div class=\"layui-input-block\" >\n" +
                    "                    <input class=\"layui-input\"  value=''  autocomplete=\"off\" onblur=\"cardNumberOnblur(this)\"  >\n" +
                    "                    <input type=\"hidden\" name=\"cardId\" id=\"cardId\" value=\"\">" +
                    "                   <input type=\"hidden\" name=\"\" id=\"consumeTimes\" value=\"\">" +
                    "                   <input type=\"hidden\" name=\"\" id=\"balance\" value=\"\">" +
                    "                   <input type=\"hidden\" name=\"\" id=\"cardIntegrals\" value=\"\">" +
                "                      <input type=\"hidden\" name=\"\" id=\"deductWay\" value=\"\">" +
                    "                   <input type=\"hidden\" name=\"\" id=\"isIntegral\" value=\"\">" +
                    "<input type=\"hidden\" name=\"\" id=\"cardNo\" value=\"\">" +
                    "<input type=\"hidden\" name=\"\" id=\"dataArray\" value=\"[]\">" +
                    "                </div>\n" +
                    "            </div>\n" +
                    "</form>",
                cancel: function(){

                },
                end : function(){

                },
                yes: function(layerIndex, layero){
                    layer.load();
                    //获取
                    let totalTimes=0.0;
                    let totalIntegrals=0.0;
                    let totalAmount=0.0;
                    $.each(checkStatus.data,function (index,item) {
                        totalTimes+=Number(item.actualTimes);
                        totalIntegrals+=Number(item.actualIntegrals);
                        totalAmount+=Number(item.actualAmount);
                        if(item.cardId==$("#cardId").val()){
                            layer.closeAll('loading');
                            layer.msg(item.name+'已是'+item.cardNumber+"会员卡，无需换卡", {icon: 2, offset: 'auto'});
                            return false;
                        }
                    });

                    if("deduct_by_days"==$("#deductWay").val()){
                        if(totalAmount>0){
                            layer.closeAll('loading');
                            layer.msg('勾选的记录存在金额卡记录，所换的卡为天数卡，金额卡记录请单条换卡', {icon: 2, offset: 'auto'});
                            return false;
                        }
                    }else if("deduct_by_money"==$("#deductWay").val()){
                        if(totalTimes>0 || totalIntegrals>0){
                            layer.closeAll('loading');
                            layer.msg('勾选的记录存在天数卡记录，所换的卡为金额卡，天数卡记录请单条换卡', {icon: 2, offset: 'auto'});
                            return false;
                        }
                    }else{
                        layer.closeAll('loading');
                        layer.msg('会员卡未知扣卡类型', {icon: 2, offset: 'auto'});
                        return false;
                    }
                    //获取最新会员卡余额
                    $.post("#(ctxPath)/fina/cardmanager/getCardByCardNumber?cardNumber="+$("#cardNo").val()+"&delFlag=0",{},function (d) {
                        if (d.state === 'ok') {
                            if("deduct_by_days"==d.data.deductWay){
                                if("1"==d.data.isIntegral){
                                    if((totalTimes+totalIntegrals)>(d.data.consumeTimes+d.data.cardIntegrals)){
                                        layer.closeAll('loading');
                                        layer.msg('勾选的记录存在换卡需要'+(totalTimes+totalIntegrals)+'天数，会员卡可用天数为'+d.data.consumeTimes
                                            +"，可用积分为"+d.data.cardIntegrals, {icon: 2, offset: 'auto'});
                                        return false;
                                    }
                                }else{
                                    if((totalTimes+totalIntegrals)>d.data.consumeTimes){
                                        layer.closeAll('loading');
                                        layer.msg('勾选的记录存在换卡需要'+(totalTimes+totalIntegrals)+'天数，会员卡可用天数为'+d.data.consumeTimes, {icon: 2, offset: 'auto'});
                                        return false;
                                    }
                                }

                            }else if("deduct_by_money"==d.data.deductWay){
                                if(totalAmount>d.data.balance){
                                    layer.closeAll('loading');
                                    layer.msg('勾选的记录存在换卡需要'+totalAmount+'金额，会员卡可用金额为'+d.data.balance, {icon: 2, offset: 'auto'});
                                    return false;
                                }
                            }else{
                                layer.closeAll('loading');
                                layer.msg('会员卡未知扣卡类型', {icon: 2, offset: 'auto'});
                                return false;
                            }

                            $.each(checkStatus.data,function (index,item) {

                                $.post('#(ctxPath)/fina/tourist/touristChangeCard',{'id':item.id,'cardId':d.data.id
                                    ,'actualTimes':item.actualTimes,'actualIntegrals':item.actualIntegrals,'actualAmount':item.actualAmount},function (res) {
                                    let dataArray=JSON.parse($("#dataArray").val());
                                    if(res.state=='ok'){
                                        dataArray.push({'name':item.name,'cardNumber':item.cardNumber,'status':'1'});
                                    }else{
                                        dataArray.push({'name':item.name,'cardNumber':item.cardNumber,'status':'0'});
                                    }
                                    $("#dataArray").val(JSON.stringify(dataArray));
                                    if(index==checkStatus.data.length-1){
                                        let flag=false;
                                        let str="";
                                        $.each(dataArray,function (i,it) {
                                            if(it.status=='0'){
                                                if(!flag){
                                                    flag=true;
                                                }
                                                str+=it.name+"、";
                                            }
                                        });
                                        if(flag){
                                            layer.closeAll('loading');
                                            layer.msg(str+'换卡失败，其他成功', {icon: 2,time:10000, offset: 'auto'});
                                        }else{

                                            layer.closeAll('loading');
                                            layer.msg('操作成功', {icon: 1, offset: 'auto'});
                                        }
                                        reloadTable();
                                        layer.close(layerIndex)
                                    }
                                })
                            });


                        }
                    })


                }
            });

        });

        cardNumberOnblur=function(obj){
            let cardNumber =$(obj).val();
            getCardInfo(cardNumber);
        }

        getCardInfo=function(cardNumber){
            layer.load();
            $.post("#(ctxPath)/fina/cardmanager/getCardByCardNumber?cardNumber="+cardNumber+"&delFlag=0",function (d) {
                if(d.state==='ok'){
                    //$(obj).closest('td').next().text(d.data.fullName);
                    let cardNumberInfo=cardNumber+"；卡主："+d.data.fullName+"；";
                    $("#cardId").val(d.data.id);
                    if(d.data.deductWay=="deduct_by_days"){
                        cardNumberInfo+="可用天数："+d.data.consumeTimes+"；"
                    }
                    if(d.data.deductWay=="deduct_by_money"){
                        cardNumberInfo+="可用余额："+d.data.balance+"；"
                    }
                    if(d.data.isIntegral=="1"){
                        cardNumberInfo+="可用积分："+d.data.cardIntegrals+"；"
                    }
                    $("#deductWay").val(d.data.deductWay);
                    $("#isIntegral").val(d.data.isIntegral);
                    $("#consumeTimes").val(d.data.consumeTimes);
                    $("#balance").val(d.data.balance);
                    $("#cardIntegrals").val(d.data.cardIntegrals);
                    $("#cardNo").val(d.data.cardNumber);

                    $("#cardInfoP").text(cardNumberInfo);
                    layer.closeAll('loading');
                }else{
                    layer.msg(d.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll('loading');
                }
            })
        }
        //更换会员卡&修改备注
        table.on('tool(touristExpenseTable)',function(obj) {
            if(obj.event==='split'){
                var url = "#(ctxPath)/fina/tourist/splitForm?expenseId=" + obj.data.expenseId;
                layerShow("拆分",url,1200,500);

            }else if(obj.event === 'changeCard'){
                var index = Number($(this).parents("tr").attr("data-index")) + 1;
                var url = "#(ctxPath)/fina/tourist/changeForm?id=" + obj.data.id +"&touristNo="+ $("#no").val() +"&fullName="+obj.data.fullName+"&index="+index;
                layerShow("变更会员卡&修改备注",url,1000,500);


            }else if(obj.event === 'singleSettle'){//单条结算
                var index = Number($(this).parents("tr").attr("data-index"));
                var data=table.cache.touristExpenseTable[index];
                layer.confirm("确定要单条结算吗?",{
                    skin : "my-skin"
                },function(index) {
                    if(data.settleStatus != '0'){
                        layer.msg('已结算账单不可重复结算', function () {});
                        return false;
                    }
                    layer.load();
                    let tr = obj.tr;
                    let id=obj.data.id;
                    let actualTimes = tr.children('td[data-field="actualTimes"]').find('input[name="actualTimes"]').val();
                    let actualAmount = tr.children('td[data-field="actualAmount"]').find('input[name="actualAmount"]').val();
                    let actualIntegrals = tr.children('td[data-field="actualIntegrals"]').find('input[name="actualIntegrals"]').val();
                    let financeRemark = tr.children('td[data-field="financeRemark"]').find('input[name="financeRemark"]').val();
                    //let settleData={'id':id,'actualTimes':actualTimes,'actualAmount':actualAmount,'actualIntegrals':actualIntegrals,'financeRemark':financeRemark};

                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/fina/tourist/singleTouristSettle',
                        data: {"id":id,'actualTimes':actualTimes,'actualAmount':actualAmount,'actualIntegrals':actualIntegrals,"touristNo":$("#no").val(),"financeRemark":financeRemark},
                        notice: true,
                        loadFlag: true,
                        success: function (rep) {
                            if (rep.state == 'ok') {
                                layui.table.reload('touristExpenseTable');

                                parent.reloadTouristLoad();
                                layer.closeAll('loading');
                            }
                        },
                        complete: function () {
                            layui.$('#layui-layer'+index).find('.layui-layer-btn0').prop('disabled',false).removeClass('layui-disabled');
                        }
                    });

                    /*var sonArr = [];
                    sonArr.push(data.id);
                    sonArr.push(data.cardId);
                    sonArr.push(data.touristNo);
                    sonArr.push(data.cardNumber);
                    sonArr.push(data.name);
                    sonArr.push(data.withholdTimes);
                    sonArr.push(data.actualTimes);
                    sonArr.push(data.withholdAmount);
                    sonArr.push(data.actualAmount);
                    sonArr.push(data.withholdPoints);
                    sonArr.push(data.actualPoints);
                    sonArr.push(data.withholdIntegrals);
                    sonArr.push(data.actualIntegrals);
                    map[0] = sonArr;
                    layui.$('#layui-layer'+index).find('.layui-layer-btn0').prop('disabled',true).addClass('layui-disabled');
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/fina/tourist/touristSettle',
                        data: {"billVal":JSON.stringify(map)},
                        notice: true,
                        loadFlag: true,
                        success: function (rep) {
                            if (rep.state == 'ok') {
                                layui.table.reload('touristExpenseTable');
                                layer.closeAll('loading');
                            }
                        },
                        complete: function () {
                            layui.$('#layui-layer'+index).find('.layui-layer-btn0').prop('disabled',false).removeClass('layui-disabled');
                        }
                    });*/



                    layer.close(index);
                });
                return false;
            }else if(obj.event==='cehckinDetail'){
                var index = Number($(this).parents("tr").attr("data-index")) + 1;
                var url = "#(ctxPath)/fina/tourist/touristCheckinDetail?id=" + obj.data.expenseId;
                layerShow("["+obj.data.name+"]的旅游团入住详情",url,900,550);
            }else if(obj.event === 'singleDel'){
            	layer.confirm("确定要作废吗?",{
                    skin : "my-skin"
                },function(index) {
                    layer.load();
                    util.sendAjax({
                        type: 'POST',
                        url: '#(ctxPath)/fina/tourist/singleDel',
                        data: {'id':obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success: function (rep) {
                            if (rep.state == 'ok') {
                            	var param = {cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),name:$("#name").val()};
                                tableReloadNotPage('touristExpenseTable',param);
                                parent.reloadTouristLoad();
                            }
                        },
                        complete: function () {
                        	layer.closeAll('loading');
                        }
                    });
                    layer.close(index);
                });
            }
        });

        //旅游团结算
        form.on('submit(confirmBtn)', function () {
            //如果搜索条件存在，则不允许结算

            layer.confirm("确定要批量结算吗?",{
                skin : "my-skin"
            },function(index) {

                layer.load();
                let settleData=[]
                $(".layui-table-main tbody tr").each(function (index,tr) {
                    let id = $(tr).children('td[data-field="id"]').find('div').text();

                    let settleStatus = $(tr).children('td[data-field="settleStatus"]').find('div').text();

                    if(id=='' || id==undefined || settleStatus=='1'){
                        return true;
                    }
                    let actualTimes = $(tr).children('td[data-field="actualTimes"]').find('input[name="actualTimes"]').val();
                    let actualAmount = $(tr).children('td[data-field="actualAmount"]').find('input[name="actualAmount"]').val();
                    let actualIntegrals = $(tr).children('td[data-field="actualIntegrals"]').find('input[name="actualIntegrals"]').val();
                    let financeRemark = $(tr).children('td[data-field="financeRemark"]').find('input[name="financeRemark"]').val();

                    settleData.push({'id':id,'actualTimes':actualTimes,'actualAmount':actualAmount,'actualIntegrals':actualIntegrals,'financeRemark':financeRemark});
                })

                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/fina/tourist/settleAllTouristSettleDetail',
                    data: {"data":JSON.stringify(settleData),"touristNo":$("#no").val()},
                    notice: true,
                    loadFlag: true,
                    success: function (rep) {
                        if (rep.state == 'ok') {
                            layui.table.reload('touristExpenseTable');
                            layer.closeAll('loading');
                            parent.reloadTouristLoad();
                        }
                    },
                    complete: function () {
                        layui.$('#layui-layer'+index).find('.layui-layer-btn0').prop('disabled',false).removeClass('layui-disabled');
                    }
                });




                layer.close(index);
            });



            return false;
        });
    });
</script>
#end