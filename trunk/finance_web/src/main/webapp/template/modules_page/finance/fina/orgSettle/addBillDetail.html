#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()费用明细修改备注#end

#define css()
<style>

    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
    .layui-radio-disbaled>i {
        color: #5FB878!important;;
    }
</style>
#end

#define js()
<script type="text/javascript">
    var table,$ ;
    layui.use(['table','laydate','form'],function(){
        table = layui.table;
        $ = layui.$ ;
        var laydate = layui.laydate;
        var form=layui.form;

        laydate.render({
            elem:"#startEndDate",
            type:'datetime',
            trigger:'click',
            range:true,
            done:function (val) {
                if(val!=''){
                    val=val.substr(0,33)+"23:59:59";
                    setTimeout(function () {
                        $("#startEndDate").val(val);
                    }, 50);
                }
            }
        })

        $('#accountNumber').on('blur',function () {
            var cardNumber=$("#accountNumber").val();
            getCardInfo(cardNumber);
        });

        getCardInfo=function(cardNumber){
            layer.load()
            $.post('#(ctxPath)/fina/cardmanager/getCardDeduct',{"cardNumber":cardNumber},function (returnData) {
                if(returnData.state==='ok'){
                    layer.msg(returnData.msg, {icon: 1, offset: 'auto'});
                    $("#radioDiv input[name='deductType'][value='"+returnData.data.deductScheme+"']").prop("checked",true);
                    if("deduct_by_days"===returnData.data.deductScheme){
                        $("#unit").text("天");
                    }else{
                        $("#unit").text("元");
                    }
                    form.render("radio");
                }else{
                    layer.msg(returnData.msg, {icon: 2, offset: 'auto'});
                    $("#radioDiv input[name='deductType']:checked").prop("checked",false);
                    form.render("radio");
                }
                layer.closeAll('loading');
            });
        }

        //选择会员卡按钮点击事件
        $("#choiceCard").on('click',function () {
            layerShow('选择会员卡','#(ctxPath)/fina/orgSettle/saveBillDetaIlReplaceCardIndex',1200,800);
        });

        //自定义验证规则
        form.verify({
            otherReq: function(value,item){
                var $ = layui.$;
                var verifyName=$(item).attr('name')
                    , verifyType=$(item).attr('type')
                    ,formElem=$(item).parents('.layui-form')//获取当前所在的form元素，如果存在的话
                    ,verifyElem=formElem.find('input[name='+verifyName+']')//获取需要校验的元素
                    ,isTrue= verifyElem.is(':checked')//是否命中校验
                    ,focusElem = verifyElem.next().find('i.layui-icon');//焦点元素
                if(!isTrue || !value){
                    //定位焦点
                    focusElem.css(verifyType=='radio'?{"color":"#FF5722"}:{"border-color":"#FF5722"});
                    //对非输入框设置焦点
                    focusElem.first().attr("tabIndex","1").css("outline","0").blur(function() {
                        focusElem.css(verifyType=='radio'?{"color":""}:{"border-color":""});
                    }).focus();
                    return '请输入正确的会员卡';
                }
            }
        });

        //保存
        form.on('submit(saveBtn)', function(obj){

            util.sendAjax({
                url:'#(ctxPath)/fina/orgSettle/addBillSave',
                type:'post',
                data:obj.field,
                notice:true,
                loadFlag:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.billDetailTableReload($("#mainId").val());
                    }
                }
            });
            return false;
        });

    });

</script>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">详情</a>
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row" style="padding-top: 20px;">
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">费用名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" autocomplete="off" value="#(detail.name??)" placeholder="请输入费用名称" class="layui-input" lay-verify="required">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">会员卡号</label>
                <div class="layui-input-block">
                    <input type="text" id="accountNumber" name="accountNumber" placeholder="请输入扣费会员卡号" autocomplete="off" value="#(detail.cardNumber??)" class="layui-input" lay-verify="required">
                    <!--<button class="layui-btn layui-btn-sm" style="display: inline-block;" id="choiceCard" type="button">选择</button>-->
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">扣除方式</label>
                <div class="layui-input-block" id="radioDiv">
                    <input type="radio" name="deductType" disabled lay-verify="otherReq" value="deduct_by_money" title="按金额扣除" >
                    <input type="radio" name="deductType" disabled lay-verify="otherReq" value="deduct_by_days" title="按天数扣除">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">扣除值(<span id="unit">元</span>)</label>
                <div class="layui-input-block">
                    <input type="text" name="amount" autocomplete="off" lay-verify="required|number" placeholder="请输入扣费值" value="#(detail.amount??)"  class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">产生时间</label>
                <div class="layui-input-block">
                    <input type="text" readonly id="startEndDate" name="startEndDate" lay-verify="required" placeholder="请选择费用产生时间" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">明细描述</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入明细描述内容" name="describe" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注内容" name="remark" class="layui-textarea"></textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input id="mainId" name="mainId" type="hidden" value="#(mainId??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end