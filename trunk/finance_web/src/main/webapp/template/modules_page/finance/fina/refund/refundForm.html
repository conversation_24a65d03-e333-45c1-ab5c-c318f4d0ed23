#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()退款申请表单页面#end

#define css()
#end

#define content()
<form id="refundForm" class="layui-form layui-form-pane" action="" lay-filter="layform" method="post">
	<div class="layui-row">
		<div class="layui-inline">
			<label class="layui-form-label">申请编号</label>
			<div class="layui-input-inline">
				<input type="text" id="applyNo" name="applyNo" readonly="readonly" placeholder="请输入申请编号" value="#(model.applyNo??)" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">申请人</label>
			<div class="layui-input-inline">
				<input type="text" id="applyName" name="applyName" readonly="readonly" placeholder="请输入申请人" value="#(model.applyName??)" autocomplete="off" class="layui-input">
			</div>
		</div>
<!--	</div>-->
<!--	<div class="layui-row">-->
		<div class="layui-inline">
			<label class="layui-form-label">申请时间</label>
			<div class="layui-input-inline">
				<input type="text" id="applyTime" name="applyTime" #if(model.recordStatus??!='wait_submit') readonly="readonly" #end placeholder="请输入申请时间" value="#date(model.applyTime??,'yyyy-MM-dd')" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">审核人</label>
			<div class="layui-input-inline">
				<input type="text" id="examineName" name="examineName" readonly="readonly" placeholder="请输入审核人" value="#(model.examineName??)" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">付款人</label>
			<div class="layui-input-inline">
				<input type="text" id="payName" name="payName" readonly="readonly" placeholder="请输入付款人" value="#(model.payName??)" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">合计总金额</label>
			<div class="layui-input-inline">
				<input type="text" id="totalAmount" name="totalAmount" #if(model.recordStatus??!='wait_submit') readonly="readonly" #end placeholder="请输入合计总金额" value="#(model.totalAmount??0)" autocomplete="off" class="layui-input">
			</div>
		</div>
	</div>
<!-- 	<div class="layui-row"> -->
<!-- 		<label class="layui-form-label">备注</label> -->
<!-- 		<div class="layui-input-block"> -->
<!-- 			<textarea class="layui-textarea" id="remarks" name="remarks" #if(model.recordStatus??!='wait_submit') readonly="readonly" #end placeholder="请输入备注">#(model.remarks??)</textarea> -->
<!-- 		</div> -->
<!-- 	</div> -->
	<div class="layui-row" style="width: 100%; height: 500px; overflow: auto;">
		<table class="layui-table">
			<thead>
			<tr>
				<th>序号</th>
				<th>退款金额</th>
				<th>违约比例(%)</th>
				<th>违约金额</th>
				<th>实退金额</th>
				<th>会员卡号</th>
				<th>卡类别</th>
				<th>卡主姓名</th>
				<th>分公司</th>
				<th>业务员</th>
				<th>合同金额</th>
				<th>合同天数</th>
				<th>实收金额</th>
				<th>帐面余额</th>
				<th>剩余金额</th>
				<th>剩余天数</th>
				<th>剩余点数</th>
				<th>剩余积分</th>
				<th>剩余豆豆</th>
				<th>开卡日期</th>
				<th>过期日期</th>
				<th>会员卡说明</th>
				#if(model.recordStatus??=='wait_submit')
				<th>操作</th>
				#end
			</tr>
			</thead>
			<tbody id="detailList">
				#for(detail : model.detailList??)
					<tr id="tr-#(for.index+1)">
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:60px;">
									<input type="text" id="sortNumber-#(for.index+1)" name="detailList-[#(for.index+1)].sortNumber" #if(model.recordStatus??!='wait_submit') readonly #end lay-verify="required|checkAmount" value="#(detail.sortNumber??)" placeholder="序号" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="refundAmount-#(for.index+1)" name="detailList-[#(for.index+1)].refundAmount" #if(model.recordStatus??!='wait_submit') readonly #end lay-verify="required|checkAmount" value="#(detail.refundAmount??)" placeholder="退款金额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="deductProportion-#(for.index+1)" name="detailList-[#(for.index+1)].deductProportion" #if(model.recordStatus??!='wait_submit') readonly #end lay-verify="required|checkAmount" value="#(detail.deductProportion??)" placeholder="违约比例(%)" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="deductMoney-#(for.index+1)" name="detailList-[#(for.index+1)].deductMoney" readonly lay-verify="required|checkAmount" value="#(detail.deductMoney??)" placeholder="违约金额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="realRefundAmount-#(for.index+1)" name="detailList-[#(for.index+1)].realRefundAmount" readonly lay-verify="required|checkAmount" value="#(detail.realRefundAmount??)" placeholder="实退款金额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:100px;">
									<input type="text" id="cardNumber-#(for.index+1)" name="detailList-[#(for.index+1)].cardNumber" #if(model.recordStatus??!='wait_submit') readonly #end value="#(detail.cardNumber??)" lay-verify="required" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:200px;">
									<input type="text" id="cardType-#(for.index+1)" name="detailList-[#(for.index+1)].cardType" readonly value="#(detail.cardType??)" placeholder="会员卡类别" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="memberName-#(for.index+1)" name="detailList-[#(for.index+1)].memberName" readonly value="#(detail.memberName??)" placeholder="卡主姓名" autocomplete="off" class="layui-input" >
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:200px;">
									<input type="text" id="branchOfficeName-#(for.index+1)" name="detailList-[#(for.index+1)].branchOfficeName" readonly value="#(detail.branchOfficeName??)" placeholder="分公司" autocomplete="off" class="layui-input" >
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="salesName-#(for.index+1)" name="detailList-[#(for.index+1)].salesName" readonly value="#(detail.salesName??)" placeholder="业务员" autocomplete="off" class="layui-input" >
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="buyCardAmount-#(for.index+1)" name="detailList-[#(for.index+1)].buyCardAmount" readonly value="#(detail.buyCardAmount??)" placeholder="合同金额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="buyCardDays-#(for.index+1)" name="detailList-[#(for.index+1)].buyCardDays" readonly value="#(detail.buyCardDays??)" placeholder="合同天数" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="collectAmount-#(for.index+1)" name="detailList-[#(for.index+1)].collectAmount" readonly value="#(detail.collectAmount??)" placeholder="实收金额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="accountAmount-#(for.index+1)" name="detailList-[#(for.index+1)].accountAmount" readonly value="#(detail.accountAmount??)" placeholder="帐面余额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="moneyBalance-#(for.index+1)" name="detailList-[#(for.index+1)].moneyBalance" readonly value="#(detail.moneyBalance??)" placeholder="剩余金额" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="daysBalance-#(for.index+1)" name="detailList-[#(for.index+1)].daysBalance" readonly value="#(detail.daysBalance??)" placeholder="剩余天数" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="pointsBalance-#(for.index+1)" name="detailList-[#(for.index+1)].pointsBalance" readonly value="#(detail.pointsBalance??)" placeholder="剩余点数" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="integralsBalance-#(for.index+1)" name="detailList-[#(for.index+1)].integralsBalance" readonly value="#(detail.integralsBalance??)" placeholder="剩余积分" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:80px;">
									<input type="text" id="beanCouponsBalance-#(for.index+1)" name="detailList-[#(for.index+1)].beanCouponsBalance" readonly value="#(detail.beanCouponsBalance??)" placeholder="剩余豆豆" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:90px;">
									<input type="text" id="buyCardDate-#(for.index+1)" name="detailList-[#(for.index+1)].buyCardDate" readonly value="#date(detail.buyCardDate??,'yyyy-MM-dd')" placeholder="开卡日期" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:90px;">
									<input type="text" id="expireDate-#(for.index+1)" name="detailList-[#(for.index+1)].expireDate" readonly value="#date(detail.expireDate??,'yyyy-MM-dd')" placeholder="过期日期" autocomplete="off" class="layui-input">
								</div>
							</div>
						</td>
						<td>
							<div class="layui-inline">
								<div class="layui-input-inline" style="width:300px;">
									<textarea id="cardRemarks-#(for.index+1)" name="detailList-[#(for.index+1)].cardRemarks" class="layui-textarea" rows="3" placeholder="请输入卡说明">#(detail.cardRemarks??)</textarea>
								</div>
							</div>
						</td>
						#if(model.recordStatus??=='wait_submit')
						<td>
							<input type="hidden" id="detailId-#(for.index+1)" name="detailList-[#(for.index+1)].id" value="#(detail.id??)">
							<input type="hidden" id="cardId-#(for.index+1)" name="detailList-[#(for.index+1)].cardId" value="#(detail.cardId??)">
							<input type="hidden" id="cardTypeId-#(for.index+1)" name="detailList-[#(for.index+1)].cardTypeId" value="#(detail.cardTypeId??)">
							<input type="hidden" id="branchOfficeId-#(for.index+1)" name="detailList-[#(for.index+1)].branchOfficeId" value="#(detail.branchOfficeId??)">
							<input type="hidden" id="salesId-#(for.index+1)" name="detailList-[#(for.index+1)].salesId" value="#(detail.salesId??)">
							<button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('tr-#(for.index+1)')" >删除</button>
						</td>
						#end
					</tr>
				#end
			</tbody>
		</table>
	</div>
	<div class="layui-form-footer">
		<div class="pull-right">
			<input type="hidden" id="modelId" name="id" value="#(model.id??)"/>
			<input type="hidden" id="applyId" name="applyId" value="#(model.applyId??)"/>
			<input type="hidden" id="examineId" name="examineId" value="#(model.examineId??)"/>
			<input type="hidden" id="payId" name="payId" value="#(model.payId??)"/>
			<input type="hidden" id="recordStatus" name="recordStatus" value="#(model.recordStatus??)"/>
			<input type="hidden" id="detailCount" name="detailCount" value="#(model.detailList.size()??0)">
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			#if(model.recordStatus??=='wait_submit')
				<button type="button" id="addBtn" class="layui-btn">添加</button>
				<button id="confirmBtn" class="layui-btn" lay-submit="" lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
				<button id="confirmAndSubmitBtn" class="layui-btn" lay-submit="" lay-filter="confirmAndSubmitBtn">保存并提交</button>
			#elseif(model.recordStatus??=='wait_examine')
				<button id="examineBtn" class="layui-btn" lay-submit="" lay-filter="examineBtn">审核通过</button>
			#elseif(model.recordStatus??=='wait_pay')
				<button id="payBtn" class="layui-btn" lay-submit="" lay-filter="payBtn">支付完成</button>
			#end
		</div>
	</div>
</form>
#end

#define js()
<script id="refundTrTpl" type="text/html">
	<tr id="tr-{{d.idx}}">
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:60px;">
					<input type="text" id="sortNumber-{{d.idx}}" name="detailList-[{{d.idx}}].sortNumber" #if(model.recordStatus??!='wait_submit') readonly #end lay-verify="required|checkAmount" value="{{d.trNum}}" placeholder="序号" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="refundAmount-{{d.idx}}" name="detailList-[{{d.idx}}].refundAmount" #if(model.recordStatus??!='wait_submit') readonly #end lay-verify="required|checkAmount" value="0" placeholder="退款金额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="deductProportion-{{d.idx}}" name="detailList-[{{d.idx}}].deductProportion" #if(model.recordStatus??!='wait_submit') readonly #end lay-verify="required|checkAmount" value="0" placeholder="违约比例(%)" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="deductMoney-{{d.idx}}" name="detailList-[{{d.idx}}].deductMoney" readonly lay-verify="required|checkAmount" value="0" placeholder="违约金额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="realRefundAmount-{{d.idx}}" name="detailList-[{{d.idx}}].realRefundAmount" readonly lay-verify="required|checkAmount" value="0" placeholder="实退款金额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:100px;">
					<input type="text" id="cardNumber-{{d.idx}}" name="detailList-[{{d.idx}}].cardNumber" #if(model.recordStatus??!='wait_submit') readonly #end value="" lay-verify="required" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:200px;">
					<input type="text" id="cardType-{{d.idx}}" name="detailList-[{{d.idx}}].cardType" readonly value="" placeholder="会员卡类别" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="memberName-{{d.idx}}" name="detailList-[{{d.idx}}].memberName" readonly value="" placeholder="卡主姓名" autocomplete="off" class="layui-input" >
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:200px;">
					<input type="text" id="branchOfficeName-{{d.idx}}" name="detailList-[{{d.idx}}].branchOfficeName" readonly value="" placeholder="分公司" autocomplete="off" class="layui-input" >
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="salesName-{{d.idx}}" name="detailList-[{{d.idx}}].salesName" readonly value="" placeholder="业务员" autocomplete="off" class="layui-input" >
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="buyCardAmount-{{d.idx}}" name="detailList-[{{d.idx}}].buyCardAmount" readonly value="0" placeholder="合同金额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="buyCardDays-{{d.idx}}" name="detailList-[{{d.idx}}].buyCardDays" readonly value="0" placeholder="合同天数" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="collectAmount-{{d.idx}}" name="detailList-[{{d.idx}}].collectAmount" readonly value="0" placeholder="实收金额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="accountAmount-{{d.idx}}" name="detailList-[{{d.idx}}].accountAmount" readonly value="0" placeholder="帐面余额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="moneyBalance-{{d.idx}}" name="detailList-[{{d.idx}}].moneyBalance" readonly value="0" placeholder="剩余金额" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="daysBalance-{{d.idx}}" name="detailList-[{{d.idx}}].daysBalance" readonly value="0" placeholder="剩余天数" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="pointsBalance-{{d.idx}}" name="detailList-[{{d.idx}}].pointsBalance" readonly value="0" placeholder="剩余点数" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="integralsBalance-{{d.idx}}" name="detailList-[{{d.idx}}].integralsBalance" readonly value="0" placeholder="剩余积分" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:80px;">
					<input type="text" id="beanCouponsBalance-{{d.idx}}" name="detailList-[{{d.idx}}].beanCouponsBalance" readonly value="0" placeholder="剩余豆豆" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:90px;">
					<input type="text" id="buyCardDate-{{d.idx}}" name="detailList-[{{d.idx}}].buyCardDate" readonly value="" placeholder="开卡日期" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:90px;">
					<input type="text" id="expireDate-{{d.idx}}" name="detailList-[{{d.idx}}].expireDate" readonly value="" placeholder="过期日期" autocomplete="off" class="layui-input">
				</div>
			</div>
		</td>
		<td>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:300px;">
					<textarea id="cardRemarks-{{d.idx}}" name="detailList-[{{d.idx}}].cardRemarks" class="layui-textarea" rows="3" placeholder="请输入卡说明"></textarea>
				</div>
			</div>
		</td>
		<td>
			<input type="hidden" id="detailId-{{d.idx}}" name="detailList-[{{d.idx}}].id" value="">
			<input type="hidden" id="cardId-{{d.idx}}" name="detailList-[{{d.idx}}].cardId" value="">
			<input type="hidden" id="cardTypeId-{{d.idx}}" name="detailList-[{{d.idx}}].cardTypeId" value="">
			<input type="hidden" id="branchOfficeId-{{d.idx}}" name="detailList-[{{d.idx}}].branchOfficeId" value="">
			<input type="hidden" id="salesId-{{d.idx}}" name="detailList-[{{d.idx}}].salesId" value="">
			#if(model.recordStatus??=='wait_submit')
				<button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('tr-{{d.idx}}')" >删除</button>
			#end
		</td>
	</tr>
</script>
<script type="text/javascript">
	layui.use(['form', 'laydate','laytpl'],function(){
		var form = layui.form;
		var laytpl = layui.laytpl;
		var $ = layui.$;
		var laydate = layui.laydate;

		#if(model.recordStatus??=='wait_submit')
			//申请时间渲染
			laydate.render({
				elem: '#applyTime',
				type: 'date',
				trigger: 'click',
			});
		#end

		function getCardInfo(id,cardNumber,flag,delFlag,index){
			util.sendAjax({
				url:"#(ctxPath)/fina/cardmanager/getCardByCardNumber",
				type:'post',
				data:{'id':id,'cardNumber':cardNumber,flag:flag,delFlag:delFlag},
				notice:false,
				success:function(returnData){
					if(returnData.state==='ok'){
						if(returnData.data != null) {
							layer.msg("获取到会员卡信息");
// 						console.log('returnData.data.id==='+returnData.data.id)
// 						console.log('returnData.data.balance==='+returnData.data.balance)
// 						console.log('returnData.data.consumeTimes==='+returnData.data.consumeTimes)
// 						console.log('returnData.data.consumePoints==='+returnData.data.consumePoints)
// 						console.log('returnData.data.cardIntegrals==='+returnData.data.cardIntegrals)
// 						console.log('returnData.data.beanCoupons==='+returnData.data.beanCoupons)
// 						console.log('returnData.data.accountBalance==='+returnData.data.accountBalance)
							var daysBalance = 0.0;
							if(returnData.data.lockConsumeTimes > 0.0){
								daysBalance = returnData.data.consumeTimes + returnData.data.lockConsumeTimes;
							}else{
								daysBalance = returnData.data.consumeTimes;
							}
							$("#refundAmount-"+index).val(returnData.data.purchaseCardBalance!=null?returnData.data.purchaseCardBalance:0.0);
							$("#realRefundAmount-"+index).val(returnData.data.purchaseCardBalance!=null?returnData.data.purchaseCardBalance:0.0);
							$("#memberName-"+index).val(returnData.data.fullName!=null?returnData.data.fullName:'');
							$("#cardType-"+index).val(returnData.data.cardType!=null?returnData.data.cardType:'');
							$("#branchOfficeName-"+index).val(returnData.data.shortName!=null?returnData.data.shortName:'');
							$("#salesName-"+index).val(returnData.data.salesName!=null?returnData.data.salesName:'');
							$("#buyCardAmount-"+index).val(returnData.data.purchaseCardBalance!=null?returnData.data.purchaseCardBalance:0.0);
							$("#buyCardDays-"+index).val(returnData.data.contractTimes!=null?returnData.data.contractTimes:0.0);
							$("#collectAmount-"+index).val(returnData.data.collectAmount!=null?returnData.data.collectAmount:0.0);
							$("#moneyBalance-"+index).val(returnData.data.balance!=null?returnData.data.balance:0.0);
							$("#daysBalance-"+index).val(returnData.data.consumeTimes!=null?returnData.data.consumeTimes:0.0);
							$("#pointsBalance-"+index).val(returnData.data.consumePoints!=null?returnData.data.consumePoints:0.0);
							$("#integralsBalance-"+index).val(returnData.data.cardIntegrals!=null?returnData.data.cardIntegrals:0.0);
							$("#beanCouponsBalance-"+index).val(returnData.data.beanCoupons!=null?returnData.data.beanCoupons:0.0);
							$("#accountAmount-"+index).val(returnData.data.accountBalance!=null?returnData.data.accountBalance:0.0);
							$("#buyCardDate-"+index).val(returnData.data.openTime!=null?returnData.data.openTime:'');
							$("#expireDate-"+index).val(returnData.data.expireDate!=null?returnData.data.expireDate:'');
							$("#cardRemarks-"+index).val(returnData.data.describe!=null?returnData.data.describe:'');
							$("#cardId-"+index).val(returnData.data.id!=null?returnData.data.id:'');
							$("#cardTypeId-"+index).val(returnData.data.cardTypeId!=null?returnData.data.cardTypeId:'');
							$("#branchOfficeId-"+index).val(returnData.data.branchOfficeId!=null?returnData.data.branchOfficeId:'');
							$("#salesId-"+index).val(returnData.data.operator!=null?returnData.data.operator:'');
							var totalAmount = $('#totalAmount').val();
							if(returnData.data.purchaseCardBalance!=null && returnData.data.purchaseCardBalance>0){
								totalAmount = Number(totalAmount) + Number(returnData.data.purchaseCardBalance);
								$('#totalAmount').val(Number(totalAmount).toFixed(2));
							}
						}else{
							layer.msg("旧会员卡信息不存在",{time: 500});
							$("#refundAmount-"+index).val(0.0);
							$("#memberName-"+index).val('');
							$("#cardType-"+index).val('');
							$("#branchOfficeName-"+index).val('');
							$("#salesName-"+index).val('');
							$("#buyCardAmount-"+index).val(0.0);
							$("#buyCardDays-"+index).val(0.0);
							$("#collectAmount-"+index).val(0.0);
							$("#moneyBalance-"+index).val(0.0);
							$("#daysBalance-"+index).val(0.0);
							$("#pointsBalance-"+index).val(0.0);
							$("#integralsBalance-"+index).val(0.0);
							$("#beanCouponsBalance-"+index).val(0.0);
							$("#accountAmount-"+index).val(0.0);
							$("#buyCardDate-"+index).val('');
							$("#expireDate-"+index).val('');
							$("#cardRemarks-"+index).val('');
							$("#cardId-"+index).val('');
							$("#cardTypeId-"+index).val('');
							$("#branchOfficeId-"+index).val('');
							$("#salesId-"+index).val('');
						}
					}else{
						layer.msg("旧会员卡信息获取失败",{time: 500});
						$("#refundAmount-"+index).val(0.0);
						$("#memberName-"+index).val('');
						$("#cardType-"+index).val('');
						$("#branchOfficeName-"+index).val('');
						$("#salesName-"+index).val('');
						$("#buyCardAmount-"+index).val(0.0);
						$("#buyCardDays-"+index).val(0.0);
						$("#collectAmount-"+index).val(0.0);
						$("#moneyBalance-"+index).val(0.0);
						$("#daysBalance-"+index).val(0.0);
						$("#pointsBalance-"+index).val(0.0);
						$("#integralsBalance-"+index).val(0.0);
						$("#beanCouponsBalance-"+index).val(0.0);
						$("#accountAmount-"+index).val(0.0);
						$("#buyCardDate-"+index).val('');
						$("#expireDate-"+index).val('');
						$("#cardRemarks-"+index).val('');
						$("#cardId-"+index).val('');
						$("#cardTypeId-"+index).val('');
						$("#branchOfficeId-"+index).val('');
						$("#salesId-"+index).val('');
					}
				}
			});
		}

		//计算违约金额
		function deductRefundAmountSetVal(index) {
			var totalAmount = $('#totalAmount').val();
			var refundAmount = $('#refundAmount-'+index).val();
			var deductProportion = $('#deductProportion-'+index).val();
			var deductMoney = $('#deductMoney-'+index).val();
			var realRefundAmount = $('#realRefundAmount-'+index).val();
			// console.log('index='+index);
			// console.log('refundAmount='+refundAmount);
			// console.log('deductProportion='+deductProportion);
			// console.log('deductMoney='+deductMoney);
			// console.log('realRefundAmount='+realRefundAmount);
			var reg = new RegExp("^[1-9][0-9]*([\\.][0-9]{1,2})?$");
			if (!reg.test(refundAmount)) {
				refundAmount = 0;
				$('#refundAmount-'+index).val(0);
			}
			if (!reg.test(deductProportion)) {
				deductProportion = 0;
				$('#deductProportion-'+index).val(deductProportion);
			}
			if(Number(refundAmount) === 0){
				layer.msg('退款金额必须大于0，请检查!', {icon: 5});
				return false;
			}else{
				//如果实退金额不为0先把原来的实退金额减掉
				if(realRefundAmount > 0){
					totalAmount = Number(totalAmount - realRefundAmount);
				}
				if(Number(deductProportion) === 0.0){
					deductMoney = 0;
					realRefundAmount = Number(refundAmount);
					$('#deductMoney-'+index).val(deductMoney);
					$('#realRefundAmount-'+index).val(realRefundAmount);
				}else{
					deductMoney = Number(refundAmount)*(Number(deductProportion)/100);
					realRefundAmount = Number(refundAmount-deductMoney).toFixed(2);
					$('#deductMoney-'+index).val(deductMoney.toFixed(2));
					$('#realRefundAmount-'+index).val(realRefundAmount);
				}
// 				console.log('totalAmount='+totalAmount);
// 				console.log('realRefundAmount='+realRefundAmount);
				totalAmount = Number(totalAmount) + Number(realRefundAmount);
				$('#totalAmount').val(Number(totalAmount).toFixed(2));
			}
		}

		//添加模板方法
		addTrTpl = function (targetId, addTpl, idx) {
			var index = parseInt(idx)+1;
			$('#detailCount').val(index);
			var trNum = $("#detailList tr").length+1;
			laytpl(addTpl).render({'idx':index, 'trNum':trNum}, function (html) {
				targetId.append(html);
			});
			//change事件
			$("#cardNumber-"+index).on('change',function(){
				var cardNumber = $('#cardNumber-'+index).val();
				if(cardNumber != null && cardNumber != '' && cardNumber != undefined){
					cardNumber = cardNumber.trim();
					//获取会员卡信息
					getCardInfo(null,cardNumber,null,'0',index);
				}else{
					layer.msg('请输入正确的会员卡号!',{icon:5});
				}
			});
			//change事件
			$("#refundAmount-"+index).on('change',function(){
				deductRefundAmountSetVal(index);
			});
			//change事件
			$("#deductProportion-"+index).on('change',function(){
				deductRefundAmountSetVal(index);
			});
		};

		//添加按钮点击事件
		$('#addBtn').on('click', function () {
			addTrTpl($('#detailList'), refundTrTpl.innerHTML, $('#detailCount').val());
		});

		del = function (trId) {
			var trArr = trId.split('-');
// 			console.log('trArr==='+trArr);
			var totalAmount = $('#totalAmount').val();
// 			console.log('totalAmount==='+totalAmount);
			var realRefundAmount = $('#realRefundAmount-'+trArr[1]).val();
			if(realRefundAmount > 0){
				totalAmount = Number(totalAmount - realRefundAmount);
				$('#totalAmount').val(Number(totalAmount).toFixed(2));
			}
			$('#' + trId).remove();
		}

		//保存
		form.on('submit(confirmBtn)', function(){
			util.sendAjax ({
				type: 'POST',
				url: "#(ctxPath)/fina/refund/saveRefund",
				data: $("#refundForm").serialize(),
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
						parent.pageTableReload(null);
					}
				},
				complete : function() {
				}
			});
			return false;
		});

		//保存并提交
		form.on('submit(confirmAndSubmitBtn)', function(){
			$('#recordStatus').val('wait_examine')
			util.sendAjax ({
				type: 'POST',
				url: "#(ctxPath)/fina/refund/saveRefund",
				data: $("#refundForm").serialize(),
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
						parent.pageTableReload(null);
					}
				},
				complete : function() {
				}
			});
			return false;
		});

		//审核
		form.on('submit(examineBtn)', function(){
			util.sendAjax ({
				type: 'POST',
				url: "#(ctxPath)/fina/refund/updateRefund",
				data: {id:'#(model.id??)', recordStatus:'wait_pay'},
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
						parent.pageTableReload(null);
					}
				},
				complete : function() {
				}
			});
			return false;
		});

		//支付
		form.on('submit(payBtn)', function(){
			util.sendAjax ({
				type: 'POST',
				url: "#(ctxPath)/fina/refund/updateRefund",
				data: {id:'#(model.id??)', recordStatus:'completed'},
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
						parent.pageTableReload(null);
					}
				},
				complete : function() {
				}
			});
			return false;
		});

		$(function() {
			$('tbody tr').each(function (i, obj) {
				var index = i +1;
				//change事件
				$("#refundAmount-"+index).on('change',function(){
					deductRefundAmountSetVal(index);
				});
				//change事件
				$("#deductProportion-"+index).on('change',function(){
					deductRefundAmountSetVal(index);
				});
			});
		});
	});
</script>
#end