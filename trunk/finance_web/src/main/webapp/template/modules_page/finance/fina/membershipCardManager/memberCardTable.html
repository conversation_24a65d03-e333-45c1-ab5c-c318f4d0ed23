#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()选择会员卡表格#end

#define css()
<style>
</style>
#end

#define content()
<div class="layui-row" style="padding: 10px;">
    <div class="layui-row">
        <div class="layui-input-inline" style="padding-left: 30px;">
            <input id="nameOrIdcardOrCardNumber" class="layui-input">
        </div>
        <button class="layui-btn" type="button" id="search">搜索</button>
    </div>
    <table class="layui-table" id="memberCardTable" lay-filter="memberCardTable"></table>
</div>
#end

#define js()
<script id="toolBar" type="text/html">
    <button class="layui-btn  layui-btn-xs" lay-event="choice" >选择</button>
</script>
<script>
layui.use(['form', 'table'],function() {
    var form = layui.form;
    var $ = layui.$;
    var laydate = layui.laydate;
    var table = layui.table;
    var actionType = '#(actionType??)';

    table.render({
        id : 'memberCardTable'
        ,elem : '#memberCardTable'
        ,method : 'POST'
        ,url : '#(ctxPath)/fina/cardmanager/findCardPageList'
       	,where : {'actionType':actionType}
        ,height:$(document).height()*0.85
        ,limit : 10
        ,limits : [10,20,30,40,50]
        ,cellMinWidth: 80
        ,cols: [[
            {field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
            ,{field:'memberName', title: '会员名称', align: 'center', unresize: true}
            ,{field:'idcard', title: '会员身份证号', align: 'center', unresize: true}
            ,{field:'cardType', title: '卡类型', align: 'center', unresize: true}
            ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#toolBar'}
        ]]
        ,page : true
    });
    table.on('tool(memberCardTable)',function (obj) {
        if(obj.event==='choice'){
        	var flag = false;
        	if(actionType=='batchRecharge'){//批量充值
            	flag = parent.addRechargeTr(obj.data.id,obj.data.cardNumber,obj.data.idcard===undefined?'':obj.data.idcard,obj.data.memberName===undefined?'':obj.data.memberName);
        	}else if(actionType=='batchVoid'){//批量作废
        		flag = parent.addVoidTr(obj.data.id,obj.data.cardNumber,obj.data.cardType,obj.data.memberName===undefined?'':obj.data.memberName,obj.data.balance,obj.data.consumeTimes,obj.data.purchaseCardBalance,obj.data.openTime);
        	}
            if(!flag){
                layer.msg('该会员卡已经添加过了', {icon: 2, offset: 'auto'});
            }
        }
    });

    memberCardTableReload=function(){
        table.reload('memberCardTable',{'where':{'nameOrIdcardOrCardNumber':$("#nameOrIdcardOrCardNumber").val(), 'actionType':actionType}});
    }

    $("#search").on('click',function () {
        memberCardTableReload();
    });
    
  	//回车事件
	document.onkeydown = function(e){
		var ev =document.all ? window.event:e;  
		if(ev.keyCode==13) {
			memberCardTableReload();
			return false
		}
	}
})
</script>
#end