#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查看充值名单页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form id="lotteryRechargeForm" class="layui-form layui-form-pane" action="" lay-filter="layform" method="post">
		<div class="layui-row" style="margin-top: 10px;">
			<div class="layui-inline">
				<label class="layui-form-label">充值类别</label>
				<div class="layui-input-inline" style="width:130px">
					<select disabled="disabled">
						#getDictList("recharge_category")
							<option value="#(key)" #(model != null ?(key == model.rechargeCategory ? 'selected':''):'')>#(value)</option>
						#end
					</select>
				</div>
			</div>
		</div>
		<div class="layui-row">
			<label class="layui-form-label">充值标题</label>
			<div class="layui-input-block">
				<input type="text" value="#(model.rechargeTitle??'')" class="layui-input" readonly="readonly">
			</div>
		</div>
		<div class="layui-row">
			<label class="layui-form-label">充值备注</label>
			<div class="layui-input-block">
				<input type="text" value="#(model.remarks??'')"  class="layui-input" readonly="readonly">
			</div>
		</div>
		<div class="layui-row">
			<table class="layui-table">
				<colgroup>
					<col width="15%">
					<col width="18%">
					<col width="18%">
					<col width="18%">
					<col width="13%">
					<col width="18%">
				</colgroup>
				<thead>
                    <th>姓名</th>
                    <th>卡号</th>
                    <th>充值编号</th>
                    <th>充值类型</th>
                    <th>充值额度</th>
                    <th>短信是否已发送</th>
                </thead>
				<tbody>
					#for(rList : rechargeList)
					<tr>
				        <td>#(rList.memberName??'')</td>
				        <td>#(rList.cardNumber??'')</td>
				        <td>#(rList.rechargeNo??'')</td>
				        <td>
				        	#if(rList.rechargeType == '1')
								<span class="layui-badge layui-bg-green">充值金额</span>
							#else if(rList.rechargeType == '2')
								<span class="layui-badge layui-bg-orange">充值天数</span>
							#else if(rList.rechargeType == '3')
								<span class="layui-badge layui-bg-black">充值点数</span>
							#else if(rList.rechargeType == '4')
								<span class="layui-badge layui-bg-blue">充值积分</span>
							#else if(rList.rechargeType == '5')
								<span class="layui-badge layui-bg-cyan">充值豆豆券</span>
							#end
				        </td>
				        <td>#(rList.rechargeValue??'')</td>
				        <td>
				        	#if(rList.isSendSuccess == '1')
								<span class="layui-badge layui-bg-green">已发送</span>
							#else
								<span class="layui-badge">未发送</span>
							#end
				        </td>
				    </tr>
				    #end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use(['form'],function(){
	var form = layui.form;
	var $ = layui.$;
	
});
</script>
#end