#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()赠送方案首页#end

#define css()
#end

#define js()
<script type="text/html" id="schemeTableBar">

</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','form','vip_table'],function(){

        // 操作对象
        var layer = layui.layer
            ,form = layui.form
            ,table = layui.table
            ,vipTable = layui.vip_table
            ,$ = layui.jquery
            ,tableId = 'schemeTable'
        ;

        // 表格渲染
        var tableObj = table.render({
            id: tableId
            , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
            , even: true //开启隔行背景
            , url: '#(ctxPath)/fina/cardmanager/giveSchemePreviewTableList?openTime=#(openTime??)&giveSchemeId=#(giveSchemeId??)'
            , method: 'post'
            , height: vipTable.getFullHeight()    //容器高度
            , cols: [[                  //标题栏
                {field: '', title: '序号', align: 'center', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                , {field: 'giveTimes', align: 'center', width: 90, title: '赠送天数', unresize:true}
                , {field: 'giveAmount', align: 'center', width: 90, title: '赠送金额', unresize:true}
                , {field: 'giveIntegrals', align: 'center', width: 90, title: '赠送积分', unresize:true}
                , {field: 'giveDate', align: 'center', width: 120, title: '赠送日期', unresize:true}
                , {field: 'giveRemark', title: '备注', align: 'center', unresize:true }
            ]]
            , page: false
            , loading: true
            , done: function (res, curr, count) {
                var layerTips;
                $("td").on("mouseenter", function() {
                    //js主要利用offsetWidth和scrollWidth判断是否溢出。
                    //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                    if (this.offsetWidth < this.firstChild.scrollWidth) {
                        var that = this;
                        var text = $(this).text();
                        layerTips=layer.tips(text, that, {
                            tips: 1,
                            time: 0
                        });
                    }
                });
                $("td").on("mouseleave", function() {
                    //js主要利用offsetWidth和scrollWidth判断是否溢出。
                    //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                    layer.close(layerTips);
                });
            }
        });

    })
</script>
#end

#define content()
<div class="my-btn-box">

    <div class="layui-row">
        <table id="schemeTable" lay-filter="schemeTable"></table>
    </div>
    <div class="pull-right">
        <button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
    </div>
</div>
#end