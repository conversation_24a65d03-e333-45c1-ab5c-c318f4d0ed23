#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡规则管理页面#end

#define css()

#end

#define js()
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		ruleLoad(null);

		sd=form.on("submit(search)",function(data){
			ruleLoad(data.field);
			return false;
		});

		function ruleLoad(data){
			table.render({
				id : 'ruleTable'
				,elem : '#ruleTable'
				,method : 'POST'
				,where : data
				,limit : 15
				,limits : [15,30,45,50]
				,url : '#(ctxPath)/fina/rule/findListPage'
				,cellMinWidth: 80
				,cols: [[
					{type:'checkbox'},
					{type: 'numbers', width:100, title: '序号',unresize:true}
					,{field:'name', title: '规则名称', align: 'center', unresize: true}
					,{field:'cycle', title: '赠送周期', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.cycle,'give_cycle','- -') }}</div>"}
					,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};

		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/fina/rule/form" ;
			layerShow("新增规则",url,900,500);
		});

		ruleTableReload=function(){
			table.reload('ruleTable')
		}

		table.on('tool(ruleTable)',function(obj){
			if (obj.event === 'del') {
				layer.confirm("确定要作废吗?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/fina/rule/delete?id="+obj.data.id,
						type:'post',
						data:$("#rechargeForm").serialize(),
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('ruleTable') ;
							}
							layer.close(index);
						}
					});
				});
			}else if(obj.event === 'edit'){
				var url = "#(ctxPath)/fina/rule/form?id=" + obj.data.id ;
				layerShow("编辑规则",url,900,500);
			}
		});

		//批量获取被作废数据
		getCheckTableData = function(){
			var ruleCheckStatus = table.checkStatus('ruleTable');
			// 获取选择状态下的数据
			return ruleCheckStatus.data;
		}

		//批量作废
		$("#batchDel").click(function(){
			layer.confirm("确定批量作废吗?",function(index){
				var jsonData=getCheckTableData();
				if(jsonData == null || jsonData == ''){
					layer.msg('请勾选作废数据', function () {});
					return;
				}
				util.sendAjax({
					url:"#(ctxPath)/fina/rule/batchDel",
					type:'post',
					data:{"ruleData":JSON.stringify(jsonData)},
					notice:true,
					success:function(returnData){
						if(returnData.state==='ok'){
							table.reload('ruleTable') ;
						}
						layer.close(index);
					}
				});
			});
		});


	});
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("finance:rule:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("finance:rule:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
#end

#define content()
<div>
	<div class="demoTable">

		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
			规则名称:
			<div class="layui-inline">
				<input id="name" name="name" class="layui-input">
			</div>
			&nbsp;&nbsp;
			赠送周期:
			<div class="layui-inline">
				<select name="cycle" id="cycle" lay-filter="cycle">
					<option value=""></option>
					#getDictList("give_cycle")
					<option value="#(key)" #(rule != null ?(key == rule.cycle ? 'selected':''):'')>#(value)</option>
					#end
				</select>
			</div>
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
		</form>
		#shiroHasPermission("finance:rule:addBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">新增规则</button>
		#end
		#shiroHasPermission("finance:rule:batchDelBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
		#end
	</div>
	<table id="ruleTable" lay-filter="ruleTable"></table>
</div>
#getDictLabel("give_cycle")
#end