#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商联系人表单#end

#define css()
#end

#define content()
<body class="v-theme">
<div class="layui-collapse">
    <div class="layui-row">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="supplierLinkForm">
            <div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>供应商</label>
				<div class="layui-input-block">
					<select id="supplierId" name="supplierId" lay-verify="required" lay-search>
						<option value="">请选择</option>
						#for(s : suppliersList)
							<option value="#(s.id)" #(model!=null ? (s.id== model.supplierId?'selected':'') :'')>#(s.name)</option>
		                #end
					</select>
				</div>
			</div>
            <div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>联系人类型</label>
				<div class="layui-input-block">
					<select id="linkType" name="linkType" lay-verify="required" lay-search>
						<option value="">请选择</option>
						#statusOption(com.cszn.integrated.service.entity.status.LinkType::me(), model.linkType??"")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>联系人名称</label>
				<div class="layui-input-block">
					<input type="text" name="linkName" class="layui-input" lay-verify="required" value="#(model.linkName??'')" placeholder="请输入联系人名称" autocomplete="off">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">联系人职位</label>
				<div class="layui-input-block">
					<input type="text" name="linkPosition" class="layui-input" value="#(model.linkPosition??'')" placeholder="请输入联系人职位" autocomplete="off">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>联系人电话</label>
				<div class="layui-input-block">
					<input type="text" name="linkPhone" class="layui-input" lay-verify="required|phone|number" value="#(model.linkPhone??'')" placeholder="请输入联系人电话" autocomplete="off">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">联系人微信</label>
				<div class="layui-input-block">
					<input type="text" name="linkWechat" class="layui-input" value="#(model.linkWechat??'')" placeholder="请输入联系人微信" autocomplete="off">
				</div>
			</div>
            <div class="layui-form-item" style="height:50px;"></div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(model.id??)"/>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form', 'laytpl', 'jquery'],function(){
        var form = layui.form;
        var laytpl = layui.laytpl;
        var $ = layui.$;
        
        //保存
        form.on('submit(confirmBtn)', function(obj){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/wms/supplier/saveLink',
                data: $("#supplierLinkForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.supplierLinkTableReload({});
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end