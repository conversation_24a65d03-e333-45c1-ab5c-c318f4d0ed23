#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()账户编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, $ = layui.jquery
	;
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/fina/account/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload(null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>支付方式</label>
			<div class="layui-input-block">
				<select name="payWay" id="payWay">
					#for(payWay : payWay)
					<option value="#(payWay.key??)"  #if(payWay.key==model.payWay??) selected #end>#(payWay.value??)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>账户名称</label>
			<div class="layui-input-block">
				<input type="text" name="accountName" class="layui-input" lay-verify="required" value="#(model.accountName??)" maxlength="30" placeholder="请输入账户名称">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">账号</label>
			<div class="layui-input-block">
				<input type="text" name="accountNum" class="layui-input" value="#(model.accountNum??)" maxlength="30" placeholder="请输入账号">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">开户行</label>
			<div class="layui-input-block">
				<input type="text" name="bankName" class="layui-input" value="#(model.bankName??)" maxlength="30" placeholder="请输入开户行">
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.Id??)">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end