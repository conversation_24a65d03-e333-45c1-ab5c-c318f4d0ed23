#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡充值审核#end

#define css()
<style>
	.layui-table-cell {
		height: 28px;
		line-height: 28px;
		padding: 0 8px;
		position: relative;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		box-sizing: border-box;
	}
</style>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
	    <div class="layui-row" style="margin-top: 20px;">
	        <div class="layui-inline">
	            <label class="layui-form-label">会员卡号：</label>
	            <div class="layui-input-inline" style="width:150px;">
	                <input type="text" id="cardNumber" name="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
	            </div>
	        </div>
	        <div class="layui-inline">
	            <label class="layui-form-label">所属会员：</label>
	            <div class="layui-input-inline"style="width:150px;">
	                <input type="text" id="fullName" name="fullName" placeholder="请输入所属会员" autocomplete="off" class="layui-input">
	            </div>
	        </div>
	        <div class="layui-inline">
				<label class="layui-form-label">审核状态</label>
				<div class="layui-input-inline"style="width:100px;">
					<select id="isReview" name="isReview">
						<option value="">全部</option>
						<option value="0" selected="selected">未审核</option>
						<option value="1">已审核</option>
					</select>
				</div>
			</div>
	        <div class="layui-inline">
				<label class="layui-form-label">作废状态</label>
				<div class="layui-input-inline"style="width:100px;">
					<select id="delFlag" name="delFlag">
						<option value="">全部</option>
						<option value="0" selected="selected">未作废</option>
						<option value="1">已作废</option>
					</select>
				</div>
			</div>
	        <div class="layui-inline">
	            <label class="layui-form-label" style="width:80px;padding-left:5px;">创建时间：</label>
	            <div class="layui-input-inline" style="width:100px;">
	                <input type="text" id="startDate" name="startDate" autocomplete="off" class="layui-input" placeholder="开始时间">
	            </div>
	            	至
	            <div class="layui-input-inline"style="width:100px;">
	                <input type="text" id="endDate" name="endDate" autocomplete="off" class="layui-input" placeholder="结束时间">
	            </div>
	        </div>
	        <div class="layui-inline">
				<label class="layui-form-label">排序方式</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="orderBy" name="orderBy">
						<option value="desc" selected="selected">倒序</option>
						<option value="asc">顺序</option>
					</select>
				</div>
			</div>
			<input id="isAuto" value="1" type="hidden" name="isAuto">
	        <button type="button" id="searchRechargeBtn" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
	    </div>
	    <div class="layui-row">
	        <table id="rechargeRecordTable" lay-filter="rechargeRecordTable"></table>
	    </div>
	</form>
</div>
#getDictLabel("recharge_classify")
#end

#define js()
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
#shiroHasPermission("finance:rechargeReview:reviewBtn")
#[[
	{{#if(d.isReview==='0' && d.delFlag==='0'){}}
		<a class="layui-btn layui-btn-xs" lay-event="review">审核</a>
	{{#}}}
]]#
#end
#shiroHasPermission("finance:rechargeReview:voidBtn")
#[[
	{{#if(d.isReview==='0' && d.delFlag==='0'){}}
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="void">作废</a>
	{{#}}}
]]#
#end
</div>
</script>
<script type="text/javascript" src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/jquery.cookie.js"></script>
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
		, layer=layui.layer
        , laydate = layui.laydate
		;
        laydate.render({
            elem:"#startDate",
            type:"date",
            range:false,
            format:"yyyy-MM-dd"
        }) ;

        laydate.render({
            elem:"#endDate",
            type:"date",
            range:false,
            format:"yyyy-MM-dd"
        }) ;

		pageTableReload = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'rechargeRecordTable',
				elem : "#rechargeRecordTable" ,
				url : '#(ctxPath)/fina/recharge/pageTable' ,
				where: data,
				height:'full-130',
				cols : [[
					{field:'', title: '会员卡信息', align: 'left', unresize: false,width:160,templet:function (d) {
							var str="";
							if(d.cardNumber != null && d.cardNumber != ''){
								str = d.cardNumber;
							}else{
								str = "--";
							}
							str += " ";
							if(d.cardMemberName != null && d.cardMemberName != null){
								str += d.cardMemberName;
							}else{
								str += "--";
							}
							return str;
						}},
					{field:'cardTypeName',title:'卡类别',width:160,unresize:false},
					{field:'',title:'充值时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.rechargeTime,'yyyy-MM-dd')}}</div>"},
					{field:'',title:'充值类型',width:90,unresize:false,templet:"<div>{{ d.type=='1'?'<span class='layui-badge layui-bg-green'>金额</span>':d.type=='2'?'<span class='layui-badge layui-bg-orange'>天数</span>':d.type=='3'?'<span class='layui-badge layui-bg-black'>点数</span>':d.type=='4'?'<span class='layui-badge layui-bg-blue'>积分</span>':d.type=='5'?'<span class='layui-badge layui-bg-cyan'>豆豆券</span>':'- -' }}</div>"},
					{field:'',title:'充值分类',width:90,unresize:false,templet: '#dictTpl("recharge_classify", "classify")'},
					{field:'describe',title:'说明',unresize:false,width:140,style:"text-align:left;font-size:10px;color:#000;"},
					{field:'isIncome',title:'收入性',width:90,unresize:false,templet:function (d) {
							if(d.isIncome=='1'){
								return "<span class='layui-badge layui-bg-green'>收入性</span>";
							}else if(d.isIncome=='0'){
								return "<span class='layui-badge layui-bg-orange'>非收入性</span>";
							}else{
								return '- -'
							}
						}},
					{field:'rechargeNo',title:'充值编号',width:120,unresize:false},
					{field:'',title:'是否现金',width:90,unresize:false,templet:function (d) {
							if(d.isCash=='1'){
								return "<span class='layui-badge layui-bg-green'>是</span>";
							}else if(d.isCash=='0'){
								return "<span class='layui-badge layui-bg-orange'>否</span>";
							}else{
								return '- -'
							}
						}},
					{field:'',title:'收钱账户',width:160,unresize:false,templet:function (d) {
							var payWay="";
							var accountName="";
							if(d.accountName!=undefined){
								accountName=d.accountName
							}
							if(d.payWay=="1"){
								payWay="(现金)";
							}else if(d.payWay=="2"){
								payWay="(微信)";
							}else if(d.payWay=="3"){
								payWay="(支付宝)";
							}else if(d.payWay=="4"){
								payWay="(信用卡)";
							}else if(d.payWay=="5"){
								payWay="(会员卡)";
							}else if(d.payWay=="6"){
								payWay="(Pos机)";
							}else if(d.payWay=="7"){
								payWay="(转账)";
							}else if(d.payWay=="8"){
								payWay="(企业微信)";
							}
							return payWay+accountName;
						}},
					{field:'',title:'付款金额',width:90,unresize:false,templet: "<div>{{ String(d.payAmount)?d.payAmount + '元':'- -' }}</div>"},
					{field:'',title:'充值金额',width:90,unresize:false,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
					{field:'',title:'充值天数',width:90,unresize:false,templet: "<div>{{ String(d.consumeTimes)?d.consumeTimes + '天':'- -' }}</div>"},
					{field:'consumePoints',title:'充值点数',width:90,unresize:false},
					{field:'consumeIntegral',title:'充值积分',width:90,unresize:false},
					{field:'consumeBeanCoupons',title:'充值豆豆券',width:90,unresize:false},
					{field:'',title:'加权平均天数',width:120,unresize:false,templet: "<div>{{ String(d.averageDays)?d.averageDays + '天':'- -' }}</div>"},
					{field:'',title:'计算单价',width:90,unresize:false,templet: "<div>{{ String(d.countPrice)?d.countPrice + '元':'- -' }}</div>"},
					{field:'',title:'计算天数',width:90,unresize:false,templet: "<div>{{ String(d.countDays)?d.countDays + '天':'- -' }}</div>"},
					{field:'',title:'创建时间',width:160,unresize:false,templet:"<div>{{dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss')}}</div>"},
					{field:'',title:'审核状态',width:90,unresize:false,templet:function (d) {
							if(d.isReview=='0'){
								return '未审核';
							}else{
								return '已审核';
							}
						}},
					{field:'',title:'作废状态',width:90,unresize:false,templet:function (d) {
							if(d.delFlag=='0'){
								return '未作废';
							}else{
								return '已作废';
							}
						}},
					{title: '操作', fixed: 'right',toolbar:'#toolBar',unresize:true,width:110}
				]] ,
				page : true,
				limit : 10,
				done:function () {
					var layerTips;
					$("td").on("mouseenter", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						if (this.offsetWidth < this.firstChild.scrollWidth) {
							var that = this;
							var text = $(this).text();
							layerTips=layer.tips(text, that, {
								tips: 1,
								time: 0
							});
						}
					});
					$("td").on("mouseleave", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						layer.close(layerTips);
					});
					layer.close(loadingIndex);
				}
			}) ;
			table.on('tool(rechargeRecordTable)',function(obj){
				if (obj.event === 'review') {


					layer.confirm("确定审核?",function(index){

						// var flag=false;
						// if($.cookie('rechargeId')==undefined){
						// 	$.cookie('rechargeId',obj.data.id);
						// }else{
						// 	if($.cookie('rechargeId').indexOf(obj.data.id)!=-1){
						// 		console.log("重复！");
						// 		flag=true;
						// 	}else{
						// 		$.cookie('rechargeId',$.cookie('rechargeId')+obj.data.id);
						// 	}
						// }
						// if(flag){
						// 	layer.msg('请勿重复操作相同记录!刷新页面',{icon:5,time:5000});
						// 	return false;
						// }

						//loading层
						var loadingIndex = layer.load(2, { //icon支持传入0-2
							shade: [0.5, 'gray'], //0.5透明度的灰色背景
							content: '审核中...',
							success: function (layero) {
								layero.find('.layui-layer-content').css({
									'padding-top': '39px',
									'width': '60px'
								});
							}
						});
						util.sendAjax({
							url:"#(ctxPath)/fina/recharge/saveRechargeReview",
							type:'post',
							data:{rechargeId:obj.data.id},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									pageTableReload({cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),isReview:$('#isReview').val(),delFlag:$('#delFlag').val(),startDate:$('#startDate').val(),endDate:$('#endDate').val(), orderBy:$('#orderBy').val(),'isAuto':$("#isAuto").val()});
								}else{

								}
								layer.close(index);
							},
							unSuccess:function(returnData){
								if($.cookie('rechargeId')!=undefined){
									$.cookie('rechargeId',$.cookie('rechargeId').replace(obj.data.id,""));
								}
							},
							complete: function () {
								layer.close(loadingIndex);
							}
						});
					});
				}else if (obj.event === 'void') {
					layer.confirm("确定作废?",function(index){
						//loading层
						var loadingIndex = layer.load(2, { //icon支持传入0-2
							shade: [0.5, 'gray'], //0.5透明度的灰色背景
							content: '作废中...',
							success: function (layero) {
								layero.find('.layui-layer-content').css({
									'padding-top': '39px',
									'width': '60px'
								});
							}
						});
						util.sendAjax({
							url:"#(ctxPath)/fina/recharge/updateRecharge",
							type:'post',
							data:{id:obj.data.id, delFlag:'1'},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									pageTableReload({cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),isReview:$('#isReview').val(),delFlag:$('#delFlag').val(),startDate:$('#startDate').val(),endDate:$('#endDate').val(), orderBy:$('#orderBy').val(),"isAuto":$("#isAuto").val()});
								}
								layer.close(index);
							},
							complete: function () {
								layer.close(loadingIndex);
							}
						});
					});
				}
			});
		}

		pageTableReload({isReview:'0', delFlag:'0', orderBy:'desc','isAuto':"1"});

        // 搜索消费记录按钮
		form.on("submit(search)",function(data){
			pageTableReload(data.field);
			return false;
		});

		//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#searchRechargeBtn').click();
				return false
			}
		}
    });

</script>
#end