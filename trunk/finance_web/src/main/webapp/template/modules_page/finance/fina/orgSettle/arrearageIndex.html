#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()欠费查询#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<table id="arrearageTable" lay-filter="arrearageTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
layui.use(['table','form'],function(){
    var table = layui.table
    , form = layui.form
    , $ = layui.$
	;

	tableLoad = function () {
		//loading层
		var loadingIndex = layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		table.render({
			id : 'arrearageTable'
			,elem : '#arrearageTable'
			,method : 'POST'
			,url : '#(ctxPath)/fina/orgSettle/arrearageList'
			,height:'full-90'
			,cols: [[
				{field:'member_name', title: '姓名', align: 'center',width:120, unresize: true}
				,{field:'idcard', title: '身份证号', align: 'center',width:180, unresize: true}
				,{field:'balanceCard', title: '持有金额卡', align: 'center',unresize: true}
				,{field:'timesCard', title: '持有天数卡', align: 'center',unresize: true}
				,{field:'integralsCard', title: '持有积分卡', align: 'center',unresize: true}
				,{field:'otherBalanceCard', title: '其他金额卡', align: 'center',unresize: true}
				,{field:'otherTimesCard', title: '其他天数卡', align: 'center',unresize: true}
				,{field:'otherIntegralsCard', title: '其他积分卡', align: 'center',unresize: true}
				,{field:'totalBalance', title: '剩余总金额', align: 'center',unresize: true}
				,{field:'totalTimes', title: '剩余总天数', align: 'center',unresize: true}
				,{field:'totalIntegrals', title: '剩余总积分', align: 'center',unresize: true}
				,{field:'totalAmount', title: '欠费金额', align: 'center',unresize: true}
				,{field:'totalDays', title: '欠费天数', align: 'center',unresize: true}
				,{field:'totalJiFens', title: '欠费积分', align: 'center',unresize: true}
			]]
			,page : false
			, done: function (res, curr, count) {
				layer.close(loadingIndex);
			}
		});
	}

	tableLoad();
});
</script>
#end