#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡扣费审核#end

#define css()
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
	    <div class="layui-row" style="margin-top: 20px;">
	        <div class="layui-inline">
	            <label class="layui-form-label">会员卡号：</label>
	            <div class="layui-input-inline" style="width:150px;">
	                <input type="text" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
	            </div>
	        </div>
	        <div class="layui-inline">
				<label class="layui-form-label">是否审核</label>
				<div class="layui-input-inline"style="width:150px;">
					<select id="isReview">
						<option value="">全部</option>
						<option value="0" selected="selected">未审核</option>
						<option value="1">已审核</option>
					</select>
				</div>
			</div>
	        <div class="layui-inline">
				<label class="layui-form-label">是否作废</label>
				<div class="layui-input-inline"style="width:150px;">
					<select id="delFlag">
						<option value="">全部</option>
						<option value="0" selected="selected">未作废</option>
						<option value="1">已作废</option>
					</select>
				</div>
			</div>
	        <button type="button" id="searchRechargeBtn" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
	    </div>
	    <div class="layui-row">
	        <table id="deductRecordTable" lay-filter="deductRecordTable"></table>
	    </div>
	</form>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
<div class="layui-btn-group">
#shiroHasPermission("finance:deductReview:reviewBtn")
#[[
	{{#if(d.isReview==='0' && d.delFlag==='0'){}}
		<a class="layui-btn layui-btn-xs" lay-event="review">审核</a>
	{{#}}}
]]#
#end
#shiroHasPermission("finance:deductReview:voidBtn")
#[[
	{{#if(d.isReview==='0' && d.delFlag==='0'){}}
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="void">作废</a>
	{{#}}}
]]#
#end
</div>
</script>
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;

		pageTableReload = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'deductRecordTable',
				elem : "#deductRecordTable" ,
				url : '#(ctxPath)/fina/deduct/pageTable' ,
				where: data,
				height:'full-90',
				cols : [[
					{field:'', title: '会员卡号', align: 'left', unresize: false,width:150,templet:function (d) {
							var str="";
							if(d.cardNumber != null && d.cardNumber != ''){
								str = d.cardNumber;
							}else{
								str = "--";
							}
//                         str += " ";
//                         if(d.cardMemberName != null && d.cardMemberName != null){
//                             str += d.cardMemberName;
//                         }else{
//                             str += "--";
//                         }
							return str;
						}
					},
//                 {field:'cardTypeName',title:'卡类别',width:150,unresize:false},
					{field:'baseName',title:'基地',width:150,unresize:false},
					{field:'appNo',title:'应用编号',width:150,unresize:false},
					{field:'',title:'消费开始时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.startTime,'yyyy-MM-dd')}}</div>"},
					{field:'',title:'消费结束时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.endTime,'yyyy-MM-dd')}}</div>"},
					{field:'orderNo',title:'订单号',width:120,unresize:false},
					{field:'',title:'扣除金额',width:90,unresize:false,templet: "<div>{{ String(d.deductAmount)?d.deductAmount + '元':'- -' }}</div>"},
					{field:'',title:'扣除天数',width:90,unresize:false,templet: "<div>{{ String(d.deductTimes)?d.deductTimes + '天':'- -' }}</div>"},
					{field:'',title:'扣除点数',width:90,unresize:false,templet: function (d) {
							if(typeof(d.deductPoints)==='undefined'){
								return '0.0';
							}else{
								return d.deductPoints;
							}
						}
					},
					{field:'',title:'扣除积分',width:90,unresize:false,templet: function (d) {
							if(typeof(d.deductIntegral)==='undefined'){
								return '0.0';
							}else{
								return d.deductIntegral;
							}
						}
					},
					{field:'',title:'扣除豆豆券',width:90,unresize:false,templet: function (d) {
							if(typeof(d.deductBeanCoupons)==='undefined'){
								return '0.0';
							}else{
								return d.deductBeanCoupons;
							}
						}
					},
					{field:'',title:'审核状态',width:90,unresize:false,templet:function (d) {
							if(d.isReview=='0'){
								return '未审核';
							}else{
								return '已审核';
							}
						}},
					{field:'',title:'作废状态',width:90,unresize:false,templet:function (d) {
							if(d.delFlag=='0'){
								return '未作废';
							}else{
								return '已作废';
							}
						}},
					{field:'describe',title:'说明',unresize:true,width:140,style:"text-align:left;font-size:10px;color:#000;"},
					{title: '操作', fixed: 'right',toolbar:'#toolBar',unresize:true,width:110}
				]] ,
				page : true,
				limit : 10,
				done:function () {
					layer.closeAll('loading');
				}
			}) ;
			table.on('tool(deductRecordTable)',function(obj){
				if (obj.event === 'review') {
					layer.confirm("确定审核?",function(index){
						util.sendAjax({
							url:"#(ctxPath)/fina/deduct/saveDeductReview",
							type:'post',
							data:{deductId:obj.data.id},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									pageTableReload({cardNumber:$('#cardNumber').val(),isReview:$('#isReview').val(),delFlag:$('#delFlag').val()});
								}
								layer.close(index);
							}
						});
					});
				}else if (obj.event === 'void') {
					layer.confirm("确定作废?",function(index){
						util.sendAjax({
							url:"#(ctxPath)/fina/deduct/update",
							type:'post',
							data:{id:obj.data.id, delFlag:'1'},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									pageTableReload({cardNumber:$('#cardNumber').val(),isReview:$('#isReview').val(),delFlag:$('#delFlag').val()});
								}
								layer.close(index);
							}
						});
					});
				}
			});
		}

		pageTableReload({isReview:'0', delFlag:'0'});

        // 搜索消费记录按钮
		form.on("submit(search)",function(data){
			pageTableReload(data.field);
			return false;
		});
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#searchRechargeBtn').click();
				return false
			}
		}
    });

</script>
#end