#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <input id="uniqueId" type="hidden" value="#(uniqueId??)">
    <div class="layui-row">
        <table id="touristCheckinDetailTable" lay-filter="touristCheckinDetailTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">

</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate = layui.laydate;



        function touristLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'touristCheckinDetailTable'
                ,elem : '#touristCheckinDetailTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-90'
                ,url : '#(ctxPath)/fina/tourist/touristCheckinDetailList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'baseName', title: '基地', align: 'center', unresize: true}
                    ,{field:'bedName', title: '床位', align: 'center', unresize: true}
                    ,{field:'startDate', title: '入住开始结束时间', align: 'center',templet:function (d) {
                            var str="";
                            if(d.startDate != null && d.startDate != ''){
                                str = dateFormat(d.startDate,'yyyy-MM-dd');
                            }else{
                                str = "--";
                            }
                            str += " 至 ";
                            if(d.endDate != null && d.endDate != null){
                                str += dateFormat(d.endDate,'yyyy-MM-dd');
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'checkinDays', title: '入住天数', align: 'center', unresize: true}
                ]]
                ,page : false
                ,limit : 10
                ,limits : [10,20,30,40]
                , done: function (res, curr, count) {
                    layer.close(loadingIndex);
                }
            });

        };

        touristLoad({"uniqueId":$("#uniqueId").val()});



        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#search').click();
                return false
            }
        }

    });
</script>
#end