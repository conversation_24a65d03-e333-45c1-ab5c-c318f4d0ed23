#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()密码设置页面#end

#define css()
#end

#define js()
<script>
layui.use(['form', 'layer'],function(){
	var form = layui.form;
	var $ = layui.$;
	var layer = layui.layer;
	save=function(){
		util.sendAjax({
			url:'#(ctxPath)/fina/cardmanager/updatePwd',
			type:'post',
			data:$("#cardInfoForm").serialize(),
			notice:true,
			success:function(returnData){
				if(returnData.state=='ok'){
					pop_close();
				}
			},
			complete :function(){
			}
		});
	}
	form.on('submit(reSetBtn)', function(){
		$("#password").val('123456');
		save();
		return false;
	});
	form.on('submit(saveBtn)', function(){
		save();
		return false;
	});
});
</script>
#end

#define content()
<form class="layui-form" id="cardInfoForm" action="" method="post">
	<input type="hidden" name="id" value="#(card.id??)"/>
	<div class="layui-form-item">
		<label class="layui-form-label"><span class="co">*</span>会员卡密码</label>
		<div class="layui-input-block">
			<input type="password" id="password" name="password" class="layui-input" lay-verify="required" value="#(card.password??)" placeholder="请输入会员卡密码" #if(card.password??!=null) readonly #end>
		</div>
	</div>
	#if(card.password??!=null)
	<div class="layui-form-item">
		<label></label>
		<div class="layui-input-block">
			<div class="layui-form-mid layui-word-aux">重置密码后是(123456)</div>
		</div>
	</div>
	#end
	<div class="layui-form-footer">
		<div class="pull-right">
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			#if(card.password??!=null)
				<button class="layui-btn" id="reSetBtn" lay-submit="" lay-filter="reSetBtn">重置密码</button>
			#else
				<button class="layui-btn" id="saveBtn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			#end
		</div>
	</div>
</form>
#end