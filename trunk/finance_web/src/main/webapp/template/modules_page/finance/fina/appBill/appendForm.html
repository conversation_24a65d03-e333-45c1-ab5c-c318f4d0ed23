#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()应用消费记录补扣信息#end
<!-- 公共Css文件 -->
#define css()
<link rel="stylesheet" href="#(ctx)/static/css/member.css" media="all" />
<style>
    .layui-form-item{margin-bottom:0;}
</style>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="billForm">
            <div class="layui-row">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">会员卡号</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(card.cardNumber??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">开卡人</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(member.fullName??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">身份证号</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(member.idcard??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">余额</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(card.balance??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">剩余天数</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(card.consumeTimes??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <fieldset class="layui-elem-field">
                    <legend>补扣详情</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">应用订单号</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" name="record.appOrderNo" value="#(record.appOrderNo??)" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">入住基地</label>
                                <div class="layui-input-block">
                                    <select name="record.baseId" lay-search lay-verify="required">
                                        <option value="">请选择入住基地</option>
                                        #for(b : baseList)
                                        <option value="#(b.id)" #(record != null ? (b.id == record.baseId?'selected':'') :'')>#(b.baseName)</option>
                                        #end
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">所属应用</label>
                                <div class="layui-input-block">
                                    <select name="record.appNo" lay-search lay-verify="required">
                                        <option value="">请选择所属应用</option>
                                        #for(a : appList)
                                        <option value="#(a.appNo)" #(record != null ? (a.appNo == record.appNo?'selected':'') :'')>#(a.appName)</option>
                                        #end
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">消费类别</label>
                                <div class="layui-input-block">
                                    <select name="record.consumeType" lay-search lay-verify="required">
                                        <option value="">请选择消费类别</option>
                                        #for(d : dictList)
                                        #if(d.dictValue!='recharge_prestore' && d.dictValue!='give_prestore')
                                        <option value="#(d.dictValue)" #(record != null ? (d.dictValue == record.consumeType?'selected':'') :'')>#(d.dictName)</option>
                                        #end
                                        #end
                                    </select>
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">消费时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" id="takeTime" name="record.takeTime" lay-verify="required" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">补扣金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="record.amount" lay-verify="checkAmount" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left:12px;">
                                <label class="layui-form-label">补扣天数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="record.consumeTimes" lay-verify="checkNumber" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">补扣描述</label>
                            <div class="layui-input-block">
                                <textarea class="layui-textarea" name="record.describe"></textarea>
                            </div>
                        </div>
                        <!--<table id="detailTable" lay-filter="detailTable"></table>-->
                    </div>
                </fieldset>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="cardId" value="#(card != null ? card.id : '')"/>
                    <input type="hidden" name="record.cardNumber" value="#(record.cardNumber??)"/>
                    <input type="hidden" name="record.checkinType" value="#(record.checkinType??)"/>
                    <input type="hidden" name="record.checkinId" value="#(record.checkinId??)"/>
                    <input type="hidden" name="record.refundUnionNo" value="#(record.refundUnionNo??)"/>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">确认扣费</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form','table','layer','laydate'],function(){
        var form = layui.form;
        var $ = layui.$;
        var table = layui.table;
        var layer=layui.layer;
        var laydate = layui.laydate;

        //时间渲染
        laydate.render({
            elem : '#takeTime',
            type: 'datetime',
            trigger: 'click'
        });

        //校验
        form.verify({
            checkAmount:function(value){
                if(value != null){
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if(!reg1.test(value) && !reg2.test(value)){
                        return "只能输入数字和小数点后两位小数";
                    }
                }
            },
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            }
        });

        //补充扣费
        form.on('submit(confirmBtn)', function () {
            layer.confirm("确定要补扣吗?",function(index) {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/fina/appBill/confirmAppend',
                    data: $("#billForm").serialize(),
                    notice: true,
                    loadFlag: true,
                    success: function (rep) {
                        if (rep.state == 'ok') {
                            pop_close();
                            parent.layui.table.reload('billTable');
                        }
                    },
                    complete: function () {
                    }
                });
                layer.close(index);
            });
            return false;
        });

        /*detailLoad({consumeId:$('#recordId').val()});

        function detailLoad(data){
            table.render({
                id : 'detailTable'
                ,elem : '#detailTable'
                ,method : 'POST'
                ,where : data
                ,limit : 5
                ,limits : [5,15,30,45,50]
                ,url : '#(ctxPath)/fina/appBill/details'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'describe', title: '消费编号', align: 'center', unresize: true}
                    ,{field:'takeTime', title: '消费时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.takeTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'amount', title: '消费金额', align: 'center', unresize: true}
                    ,{field:'consumeTimes', title: '扣除天数', align: 'center', unresize: true}
                ]]
                ,page : true
            });
        };*/
    });
</script>
#end