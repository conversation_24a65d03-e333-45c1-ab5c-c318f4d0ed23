#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团信息展示页面#end

#define css()
<style>
    label.layui-form-label{
        padding-right:0px;
    }
    div.in{
        padding-top: 10px;
    }
</style>
#end

#define js()
<script>
    layui.use(['form','laytpl','layer'], function() {
        var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer;
    });
</script>
#end

#define content()
<div style="margin: 15px;">
    <div class="demoTable">
        <form class="layui-form layui-form-pane" action="" method="post" id="deductForm">
            <div class="layui-form-item">
                <label class="layui-form-label">旅游团编号</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" value="#(tourist.touristNo??)">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">旅游团名称</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" value="#(tourist.name??)" >
                </div>
                <label class="layui-form-label">扣除天数</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" value="#(tourist.expenseTimes??)" >
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">开团时间</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" value="#date(tourist.startDate??,'yyyy-MM-dd HH:mm:ss')" >
                </div>
                <label class="layui-form-label">结束时间</label>
                <div class="layui-input-inline">
                    <input type="text" class="layui-input" value="#date(tourist.endDate??,'yyyy-MM-dd HH:mm:ss')" >
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">描述</label>
                <div class="layui-input-block">
                    <textarea class="layui-textarea" rows="5" readonly>#(tourist.route??)</textarea>
                </div>
            </div>
        </form>
    </div>
</div>
#end