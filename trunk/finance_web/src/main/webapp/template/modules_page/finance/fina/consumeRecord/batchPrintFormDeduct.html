#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡消费小票打印页面#end

#define css()
<style>
    table tr td:nth-child(1) {
        width:150px;
        text-align: right;
    }
    table tr td:nth-child(2) {
        text-align: left;
    }
    #aa{
        display: none;
    }
    @media print{
        #printDiv{
            width: 800px;
        }
        .compForm{
            font-size: 8px;
            width:350px;
            height:300px;
            float: left;
        }
        .compForm table{
            border:0px;
            cell-padding:0;
            cell-spacing:0;
        }
        .aa{
            display: block;
            width: 100%;
            height: auto;
            word-wrap:break-word;
            word-break:break-all;
            overflow: hidden;
        }
        .bb{
            display: none;
        }
    }
</style>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/jQuery.print.js"></script>
<script type="text/javascript">
    layui.use(['form','laydate','layer'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var layer=layui.layer;

        $('#printBtn').on('click', function() {
            //$("#aa").text($("#val").val());
            $(".compForm table").removeClass("layui-table");
            $.print('#printDiv');
            pop_close();
        });

    });
</script>
#end

#define content()
<body class="v-theme">
<input type="hidden" id="type" value="#(type??)"/>
<form class="layui-form" id="form" style="margin-top: 10px;">
    <div class="layui-row" id="printDiv">
        #for(record : recordList)
        <div class="compForm" style="text-align: center;">
            <table class="layui-table">
                <tr>
                    <td colspan="2" style="text-align: center;">#(record.baseName??)</td>
                </tr>
                #if(record.appOrderNo?? != null && record.appOrderNo?? != '')
                <tr>
                    <td>单据号：</td>
                    <td>#(record.appOrderNo??)</td>
                </tr>
                #end

                <tr>
                    <td>消费时间：</td>
                    <td>#(record.dealTime??)</td>
                </tr>

                #if(record.tel?? != null && record.tel?? != '')
                <tr>
                    <td>联系电话：</td>
                    <td>#(record.tel??)</td>
                </tr>
                #end

                <tr>
                    <td>操作员：</td>
                    <td>#(record.operator??)</td>
                </tr>
                <tr>
                    <td>业务类型：</td>
                    <td>#(record.serviceType??)</td>
                </tr>

                #if(record.amount?? != null && record.deductWay??=="deduct_by_money")
                <tr>
                    <td>消费金额：</td>
                    <td>#(record.amount??)元</td>
                </tr>
                #end

                #if(record.consumeTimes?? != null && record.deductWay??=="deduct_by_days")
                <tr>
                    <td>消费天数：</td>
                    <td>#(record.consumeTimes??)天</td>
                </tr>
                #end

                <!--#if(record.points?? !=null && record.record?? !=0.00)
                <tr>
                    <td>消费点数：</td>
                    <td>#(record.points??)</td>
                </tr>
                #end-->

                #if(record.integrals?? != null && record.isIntegral?? =="1")
                <tr>
                    <td>消费积分：</td>
                    <td>#(record.integrals??)</td>
                </tr>
                #end

                <tr>
                    <td>会员卡号：</td>
                    <td>#(record.cardNumber??)</td>
                </tr>
                <tr>
                    <td>会员姓名：</td>
                    <td>#(record.fullName??)</td>
                </tr>

                #if(record.balance?? != null && record.deductWay??=="deduct_by_money")
                <tr>
                    <td>剩余余额：</td>
                    <td>#(record.balance??)元</td>
                </tr>
                #end

                #if(record.cardTimes?? != null && record.deductWay??=="deduct_by_days")
                <tr>
                    <td>剩余天数：</td>
                    <td>#(record.cardTimes??)天</td>
                </tr>
                #end

                <!--#if(record.consumePoints?? != null && record.consumePoints?? != 0.00)
                <tr>
                    <td>剩余点数：</td>
                    <td>#(record.consumePoints??)</td>
                </tr>
                #end-->

                #if(record.cardIntegrals?? != null && record.isIntegral?? =="1")
                <tr>
                    <td>剩余积分：</td>
                    <td>#(record.cardIntegrals??)</td>
                </tr>
                #end


                #if(record.lockAmount?? != null && record.deductWay??=="deduct_by_money")
                <tr>
                    <td>锁定金额：</td>
                    <td>#(record.lockAmount??)元</td>
                </tr>
                #end

                #if(record.lockTimes?? != null && record.deductWay??=="deduct_by_days")
                <tr>
                    <td>锁定天数：</td>
                    <td>#(record.lockTimes??)天</td>
                </tr>
                #end

                <!--#if(record.lockPoints?? != null && record.lockPoints?? != 0.00)
                <tr>
                    <td>锁定点数：</td>
                    <td>#(record.lockPoints??)</td>
                </tr>
                #end-->

                #if(record.lockIntegrals?? != null && record.isIntegral?? =="1")
                <tr>
                    <td>锁定积分：</td>
                    <td>#(record.lockIntegrals??)</td>
                </tr>
                #end

                <tr>
                    <td>备注：</td>
                    <td><p class="bb"><textarea class="layui-textarea" rows="3" >#(record.describe??)</textarea></p>
                        <p class="aa">#(record.describe??)</p>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">敬请保留小票!</td>
                </tr>
                <tr>
                    <td colspan="2" style="text-align: center;">打印时间：#(record.printTime??)</td>
                </tr>
            </table>
        </div>
        #end
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关闭</button>
            <button type="button" class="layui-btn" id="printBtn">打印</button>
        </div>
    </div>
</form>
</body>
#end