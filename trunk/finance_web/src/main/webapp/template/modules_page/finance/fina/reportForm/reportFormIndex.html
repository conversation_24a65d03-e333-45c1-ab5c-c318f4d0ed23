#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()扣卡汇总表#end

#define css()

<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div class="layui-collapse">

    <fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
        <legend>扣卡明细</legend>
    </fieldset>
    <div class="layui-row" style="margin-top: 10px;min-height: 500px;">
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId">
                        #for(base : baseList)
                        <option value="#(base.id)">#(base.baseName)</option>
                        #end
                    </select>
                </div>
                <label class="layui-form-label">时间段</label>
                <div class="layui-input-inline">
                    <input id="dateRange" class="layui-input">
                </div>
                <button class="layui-btn" type="button" id="but1">搜索</button>
            </div>
        </form>
        <table id="table1" lay-filter="table1" class="layui-table"></table>
    </div>


    <fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
        <legend>按选择日期查询生成各基地扣卡清单(不包含江高长住)</legend>
    </fieldset>
    <div class="layui-row" style="margin-top: 10px;min-height: 200px;">
        <form class="layui-form">
            <div class="layui-form-item">
                <label class="layui-form-label">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId2">
                        #for(base : baseList)
                        <option value="#(base.id)">#(base.baseName)</option>
                        #end
                    </select>
                </div>
                <label class="layui-form-label">日期</label>
                <div class="layui-input-inline">
                    <input id="date" class="layui-input">
                </div>
                <button class="layui-btn" type="button" id="but2">搜索</button>
            </div>
        </form>
        <table id="table2" lay-filter="table2" class="layui-table"></table>
    </div>
    <fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
        <legend>按月份生成各基地扣卡汇总数</legend>
    </fieldset>
    <div class="layui-row" style="margin-top: 10px;min-height: 500px;">
        <form class="layui-form">
            <div class="layui-form-item">

                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline">
                    <input id="month" class="layui-input">
                </div>
                <button class="layui-btn" type="button" id="but3">搜索</button>
            </div>
        </form>
        <table id="table3" lay-filter="table3" class="layui-table"></table>
    </div>

    <fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
        <legend>年度扣卡汇总表</legend>
    </fieldset>
    <div class="layui-row" style="margin-top: 10px;min-height: 500px;">
        <form class="layui-form">
            <div class="layui-form-item">

                <label class="layui-form-label">年份</label>
                <div class="layui-input-inline">
                    <input id="year" class="layui-input">
                </div>
                <button class="layui-btn" type="button" id="but4">搜索</button>
            </div>
        </form>
        <table id="table4" lay-filter="table4" class="layui-table"></table>
    </div>

    <fieldset class="layui-elem-field site-demo-button" style="margin-top: 30px;">
        <legend>按卡类型生成扣卡汇总表</legend>
    </fieldset>
    <div class="layui-row" style="margin-top: 10px;margin-bottom: 20px;min-height: 500px;">
        <form class="layui-form">
            <div class="layui-form-item">

                <label class="layui-form-label">年份</label>
                <div class="layui-input-inline">
                    <input id="year2" class="layui-input">
                </div>
                <button class="layui-btn" type="button" id="but5">搜索</button>
            </div>
        </form>
        <table id="table5" lay-filter="table5" class="layui-table"></table>
    </div>

</div>
#getDictLabel("punish_type")
#end
<!-- 公共JS文件 -->
#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        var start=new Date();
        var end=new Date(start);
        end.setDate(start.getDate() - 30);

        var sMonth=start.getMonth()+1;
        if(sMonth<10){
            sMonth="0"+sMonth;
        }
        var eMonth=end.getMonth()+1;
        if(eMonth<10){
            eMonth="0"+eMonth;
        }
        var sDay=start.getDate();
        if(sDay<10){
            sDay="0"+start.getDate();
        }

        var eDay=end.getDate();
        if(eDay<10){
            eDay="0"+end.getDate();
        }




        laydate.render({
            elem: '#dateRange'
            ,range:true
            ,trigger:'click'
            ,value:end.getFullYear() + "-" + eMonth + "-" + eDay+' - '+start.getFullYear() + "-" + sMonth+ "-" + sDay
        });

        laydate.render({
           elem:'#date'
           ,value:new Date()
           ,trigger:'click'
           ,format:'yyyy-MM-dd'
        });

        laydate.render({
            elem:'#month'
            ,value:new Date()
            ,trigger:'click'
            ,type:'month'
            ,format:'yyyy-MM'
        });

        laydate.render({
            elem:'#year'
            ,value:new Date()
            ,trigger:'click'
            ,type:'year'
            ,format:'yyyy'
        });

        laydate.render({
            elem:'#year2'
            ,value:new Date()
            ,trigger:'click'
            ,type:'year'
            ,format:'yyyy'

        });

        table1Load({'baseId':$("#baseId").val(),'dateRange':end.getFullYear() + "-" + eMonth + "-" + eDay+' - '+start.getFullYear() + "-" + sMonth+ "-" + sDay});


        $("#but1").on('click',function () {
            table1Load({'baseId':$("#baseId").val(),'dateRange':$("#dateRange").val()});
        });

        function table1Load(data){
            table.render({
                id : 'table1'
                ,elem : '#table1'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/reportForm/loadTable1'
                ,cellMinWidth: 80
                ,cols: [[

                    {field:'checkinName', title: '入住人', align: 'center', unresize: false,width:100}
                    ,{field:'roomName', title: '房间号', align: 'center', unresize: false,width:110}
                    ,{field:'checkinDate', title: '入住时间', align: 'center', unresize: false,width:120,templet:function (d) {
                            if(typeof(d.checkinDate)==='undefined'){
                                return '';
                            }else{
                                return d.checkinDate.substring(0,10);
                            }
                        }}
                    ,{field:'checkoutDate', title: '退住时间', align: 'center', unresize: false,width:120,templet:function (d) {
                            if(typeof(d.checkoutDate)==='undefined'){
                                return '';
                            }else{
                                return d.checkoutDate.substring(0,10);
                            }
                        }}
                    ,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: false,width:110}
                    ,{field:'cardName', title: '持卡人', align: 'center', unresize: false,width:100}
                    ,{field:'cardType', title: '卡类型', align: 'center', unresize: false}
                    ,{field:'settleStartDate', title: '结算开始时间', align: 'center', unresize: false,width:110,templet:function (d) {
                            if(typeof(d.settleStartDate)==='undefined'){
                                return '';
                            }else{
                                return d.settleStartDate.substring(0,10);
                            }
                        }}
                    ,{field:'settleEndDate', title: '结算结束时间', align: 'center', unresize: false,width:110,templet:function (d) {
                            if(typeof(d.settleEndDate)==='undefined'){
                                return '';
                            }else{
                                return d.settleEndDate.substring(0,10);
                            }
                        }}
                    ,{field:'settleDays', title: '结算天数', align: 'center', unresize: false,width:110}
                    ,{field:'settleDays', title: '扣卡情况', align: 'center', unresize: false,width:110,templet:function (d) {
                            if(d.times>0){
                                return d.times+'天';
                            }else if(d.amount>0){
                                return d.amount+'元';
                            }else if(d.points>0){
                                return d.points+'点数';
                            }else{
                                return '0';
                            }
                        }}
                    ,{field:'isPrivateRoom', title: '是否包房', align: 'center', unresize: false,width:90}
                    ,{field:'remark', title: '备注', align: 'center', unresize: false}
                    ,{field:'settleName', title: '结算人', align: 'center', unresize: false,width:110}
                ]]
                ,page : true
                ,done:function (res,curr,count) {

                }
            });
        };


        table2Load({'baseId':$("#baseId2").val(),'date':start.getFullYear() + "-" + sMonth + "-" + sDay});

        $("#but2").on('click',function () {
            table2Load({'baseId':$("#baseId2").val(),'date':$("#date").val()});
        });

        function table2Load(data){
            table.render({
                id : 'table2'
                ,elem : '#table2'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/reportForm/loadTable2'
                ,cellMinWidth: 80
                ,cols: [[

                    /*{field:'checkinName', title: '入住人', align: 'center', unresize: false}*/
                    {field:'count', title: '退住人数', align: 'center', unresize: false}
                    ,{field:'deductedTimes', title: '扣除天数', align: 'center', unresize: false}
                    ,{field:'deductedAmount', title: '扣除金额', align: 'center', unresize: false}
                    ,{field:'deductedPoints', title: '扣除点数', align: 'center', unresize: false}
                ]]
                ,page : false
                ,done:function (res,curr,count) {

                }
            });
        };


        table3Load({'month':start.getFullYear() + "-" + sMonth});

        $("#but3").on('click',function () {
            table3Load({'month':$("#month").val()});
        });

        function table3Load(data){
            table.render({
                id : 'table3'
                ,elem : '#table3'
                ,method : 'POST'
                ,where : data
                ,limit : 20
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/reportForm/loadTable3'
                ,cellMinWidth: 80
                ,cols: [[

                    /*{field:'checkinName', title: '入住人', align: 'center', unresize: false}*/
                    {field:'baseName', title: '基地名称', align: 'center', unresize: false}
                    ,{field:'totalTimes', title: '总扣除天数', align: 'center', unresize: false}
                    ,{field:'totalAmount', title: '总扣除金额', align: 'center', unresize: false}
                    ,{field:'totalPoints', title: '总扣除点数', align: 'center', unresize: false}
                ]]
                ,page : false
                ,done:function (res,curr,count) {

                }
            });
        };

        table4Load({'year':start.getFullYear()});

        $("#but4").on('click',function () {
            table4Load({'year':$("#year").val()});
        });

        function table4Load(data){

            $.ajax({
                url: '#(ctxPath)/fina/reportForm/loadTable4'
                , type : "GET"
                , dataType: "JSON"
                ,data:data
                ,style:'padding: 0 5px;'
                , success: function(res) {
                    var data=res.data;
                    var colArray=[{field: "month", title: "月份",align: 'center',width:100, unresize: true}];
                    for(var d in data[0]){//遍历json对象的每个key/value对,p为key
                        if(d!='month'){
                            var json={field:d,title:d, unresize: true}
                            colArray[colArray.length]=json;
                        }
                    }
                    var col = [colArray];
                    table.render({
                        elem: "#table4"
                        , id: "table4"
                        ,page : false
                        ,limit:13
                        , data: data
                        , cols: col
                    });
                }
            });
        };



        table5Load({'year':start.getFullYear()});

        $("#but5").on('click',function () {
            table5Load({'year':$("#year2").val()});
        });

        function table5Load(data){
            /*table.render({
                id : 'table5'
                ,elem : '#table5'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/reportForm/loadTable5'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'yearLimitName', title: '会员卡类型', align: 'center', unresize: false}
                    ,{field:'01', title: '01', align: 'center', unresize: false}
                    ,{field:'02', title: '02', align: 'center', unresize: false}
                    ,{field:'03', title: '03', align: 'center', unresize: false}
                    ,{field:'04', title: '04', align: 'center', unresize: false}
                    ,{field:'05', title: '05', align: 'center', unresize: false}
                    ,{field:'06', title: '06', align: 'center', unresize: false}
                    ,{field:'07', title: '07', align: 'center', unresize: false}
                    ,{field:'08', title: '08', align: 'center', unresize: false}
                    ,{field:'09', title: '09', align: 'center', unresize: false}
                    ,{field:'10', title: '10', align: 'center', unresize: false}
                    ,{field:'11', title: '11', align: 'center', unresize: false}
                    ,{field:'12', title: '12', align: 'center', unresize: false}
                ]]
                ,page : false
                ,done:function (res,curr,count) {

                }
            });*/

            $.ajax({
                url: '#(ctxPath)/fina/reportForm/loadTable5'
                , type : "GET"
                , dataType: "JSON"
                ,data:data
                , success: function(res) {
                    var data=res.data;
                    var colArray=[{field: "month", title: "月份",align: 'center',width:100, unresize: true}];
                    for(var d in data[0]){//遍历json对象的每个key/value对,p为key
                        if(d!='month'){
                            var json={field:d,title:d, unresize: true}
                            colArray[colArray.length]=json;
                        }
                    }
                    var col = [colArray];
                    table.render({
                        elem: "#table5"
                        , id: "table5"
                        ,page : false
                        ,limit:13
                        , data: data
                        , cols: col
                    });
                }
            });
        };

    });
</script>
#end