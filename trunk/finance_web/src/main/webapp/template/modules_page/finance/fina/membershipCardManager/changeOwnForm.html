#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()更换卡主页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
    div.layui-form-item{
        padding-left:50px;
    }
    .co {
        color: #ff0000;
    }
</style>
#end

#define js()
<script>
    layui.use(['form', 'laydate'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        form.render('select');

        //时间渲染
        laydate.render({
            elem : '#birthday',
            trigger: 'click'
        });
        laydate.render({
            elem : '#openTime',
            trigger: 'click'
        });

        //修改时间格式
        $(function() {
            if ($("#time").val() != null && $("#time").val() != '') {
                var openTime = dateFormat($("#time").val(), 'yyyy-MM-dd');
                $("#openTime").val(openTime);
            }
        });

        getNameAndUserId=function(userId,name){
            $("#operatorName").val(name);
            $("#operator").val(userId);
        }

        // 选择分公司业务员
        $("#chooseUser").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/fina/cardmanager/chooseUser" ;
            pop_show("选择分公司业务员",url,700,480);
        });


        //校验
//         form.verify({
//             checkPhone:function(value){
//                 if(value != null && value.length >0){
//                     var reg = new RegExp("^(13[0-9]|14[0-9]|15[0-9]|18[0-9]|17[0-9])\\d{8}$");
//                     if(!reg.test(value)){
//                         return "手机号码格式不正确";
//                     }
//                 }
//             }
//         });


        //身份证关联出生日期
        function idCardBirthday(psidno){
            var birthdayno,birthdaytemp;
            if (psidno.length == 18) {
                birthdayno=psidno.substring(6,14)
            } else if (psidno.length == 15) {
                birthdaytemp = psidno.substring(6,12);
                birthdayno = "19" + birthdaytemp;
            }else{
                return false
            }
            var birthday = birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8)
            return birthday
        }


        $('#idcard').on('keyup', function() {
            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                $('#birthday').val(birthday);
            }
        });
        $('#idcard').on('focus', function() {
            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                $('#birthday').val(birthday);
            }
        });

        //保存
        form.on('submit(saveBtn)', function(){
            util.sendAjax({
                url:'#(ctxPath)/fina/cardmanager/changeOwn',
                type:'post',
                data:$("#cardInfoForm").serialize(),
                notice:true,
                loadFlag:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.layui.table.reload('cardTable');
                    }
                }
            });
            return false;
        });

        //扫描身份证
        $("#searchIDCardBtn").on('click',function(){
            util.sendAjax({
                url:'http://127.0.0.1:1500/Service?type=IDCard&method=GetCardInfo',
                type:'post',
                data:JSON.stringify({}),
                success : function (result) {
                    var data=result.Data;
                    if(result.Type===1){
                        $('#cardName').val(data.name);
                        $('#idcard').val(data.cardID);
                        $('#address').val(data.address);
                        if(data.gender==="男"){
                            $("#cardGender option[value='1']").prop("selected",true);
                        }else{
                            $("#cardGender optoin[value='2']").prop("selected",true);
                        }
                        form.render("select");
                    }else{
                        layer.msg('读取失败',{icon:5,time:5000});
                    }
                }
            });
        });

        //姓名绑定
        $("#cardName").on("blur",function(){
            checkNameAndIdcard();
        });

        //身份证绑定
        $("#idcard").on("blur",function(){
            checkNameAndIdcard();
        });

        function checkNameAndIdcard(){
            var cardName = $("#cardName").val();
            var idcard = $("#idcard").val();
            if((cardName != null && cardName.trim() != "") && (idcard != null && idcard.trim() != "")){
                util.sendAjax({
                    url:'#(ctxPath)/fina/cardmanager/bindMmsMember',
                    type:'post',
                    data:{'cardName':cardName,idcard:idcard},
                    notice:false,
                    success:function(returnData){
                        if(returnData.state==='ok' && returnData.msg != '无信息'){
                            $('#birthday').val(returnData.data.birthday);
                            $('#telephone').val(returnData.data.telephone);
                            $('#address').val(returnData.data.address);
                            //处理区域
                            $("#province").val(returnData.data.province);
                            $("#city").val(returnData.data.city);
                            $("#town").val(returnData.data.town);
                            $("#street").val(returnData.data.street);
                            $("#regidentProvinceName").val(returnData.province);
                            $("#regidentCityName").val(returnData.city);
                            $("#regidentTownName").val(returnData.town);
                            $("#regidentStreetName").val(returnData.street);

                            var addr = "";
                            if(returnData.province != null){
                                addr += " " + returnData.province;
                            }
                            if(returnData.city != null){
                                addr += " " + returnData.city;
                            }
                            if(returnData.town != null){
                                addr += " " + returnData.town;
                            }
                            if(returnData.street != null){
                                addr += " " + returnData.street;
                            }
                            $("#regidentAddress").val(addr);

                            if (returnData.data.gender == "1") {
                                $("#cardGender option[value='1']").attr("selected", true);
                            } else if (returnData.data.gender == "2") {
                                $("#cardGender option[value='2']").attr("selected", true);
                            } else if (returnData.data.gender == "0") {
                                $("#cardGender option[value='0']").attr("selected", true);
                            }
                            form.render("select");
                        }
                    }
                });
            }
        }

        //--------------------------居住区域begin---------------------------
        $('#regidentAddress').on('click', function() {
            //closeIdArea();

            $('#regidentArea').remove();
            var $this = $(this);
            var getTpl = regidentAreaTpl.innerHTML;
            $this.parent().append(getTpl);
            //event.stopPropagation();

            var street=$("#street").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var town=$("#town").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var city=$("#city").val();
            var regidentCityName=$("#regidentCityName").val();
            var province=$("#province").val();
            var regidentProvinceName=$("#regidentProvinceName").val();
            if(street!='' && regidentStreetName!=''){
                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentStreetLoad(town);
                regidentCountyLoad(city);
                regidentCityLoad(province);
                regidentProvinceLoad();
                $(".con .regidentStreetAll").show().siblings().hide();
                //clickStreet(streetId,streetName);
            }else if(town!='' && regidentCountyName!=''){

                if(town!=''){
                    regidentCityLoad(province);
                    regidentCountyLoad(city);
                    regidentProvinceLoad();
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/area/getAreas',
                        data: {pid:town},
                        notice:false,
                        loadFlag: false,
                        success : function(res){
                            if(res.state=='ok'){
                                if(res.data.length>0){
                                    $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    var html="<ul>";
                                    $.each(res.data, function(i, item){
                                        html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
                                    });
                                    html+="</ul>";
                                    $(".regidentStreetAll .list").append(html);
                                    //viewStreet(countyId,countyName);
                                    $(".con .regidentStreetAll").show().siblings().hide();
                                }else{
                                    //无 街道信息
                                    $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    $(".con .regidentTownAll").show().siblings().hide();
                                }
                            }
                        },
                        complete : function() {
                        }
                    });
                }
            }else if(city!='' && regidentCityName!=''){
                regidentProvinceLoad();
                regidentCityLoad(province);
                viewRegidentCounty(city,regidentCityName);
            }else if(province!='' && regidentProvinceName!=''){
                regidentProvinceLoad();
                viewRegidentCity(province,regidentProvinceName);
            }else{
                regidentProvinceLoad();
            }


            //去除事件冒泡
            var evt =  new Object;
            if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                evt = arguments.callee.caller.arguments[0];
            }else{
                evt = event || window.event;
            }
            evt.cancelBubble = true;
            $('#regidentArea').off('click');
            $('#regidentArea').on('click', function() {
                //event.stopPropagation();
                //去除事件冒泡
                var evt =  new Object;
                if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                    evt = arguments.callee.caller.arguments[0];
                }else{
                    evt = event || window.event;
                }
                evt.cancelBubble = true;
            })
        });

        regidentProvinceLoad=function(){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/area/getAreas',
                data: {pid:''},
                notice:false,
                loadFlag: false,
                success : function(res){
                    if(res.state=='ok'){
                        if(res.data.length>0){
                            $(".regidentProvinceAll .list").empty();
                            var html="<ul>";
                            $.each(res.data, function(i, item){
                                html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCity(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
                            });
                            html+="</ul>";
                            $(".regidentProvinceAll .list").append(html);
                        }
                    }
                },
                complete : function() {
                }
            });
        };

        //点击省事件
        viewRegidentCity=function(province,regidentProvinceName) {
            $("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentCityAll").show().siblings().hide();
            $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            //点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
            $("#province").val(province);
            $("#regidentProvinceName").val(regidentProvinceName);
            $("#city").val("");
            $("#regidentCityName").val("");
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCityLoad(province);
        };

        //加载市
        regidentCityLoad=function(province){
            if(province!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:province},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentCityAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCounty(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentCityAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击市事件
        viewRegidentCounty=function(city,RegidentCityName){
            $("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentTownAll").show().siblings().hide();
            $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#city").val(city);
            $("#regidentCityName").val(RegidentCityName);
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCountyLoad(city);
        };

        //加载区/县
        regidentCountyLoad=function(city){
            if(city!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:city},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentTownAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentStreet(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentTownAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击区/县事件
        viewRegidentStreet=function(town,regidentCountyName){
            $("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentStreetAll").show().siblings().hide();
            $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#town").val(town);
            $("#regidentCountyName").val(regidentCountyName);
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentStreetLoad(town);
        };

        //加载街道/镇/乡
        regidentStreetLoad=function(town){
            if(town!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:town},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentStreetAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentStreetAll .list").append(html);
                            }else{
                                //无 街道信息
                                clickRegidentStreet('','');
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        clickRegidentStreet=function(street,regidentStreetName){
            $("#street").val(street);
            $("#regidentStreetName").val(regidentStreetName);
            var regidentProvinceName=$("#regidentProvinceName").val();
            var regidentCityName=$("#regidentCityName").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
            $("#regidentAddress").val(add);
            $('#regidentArea').remove();
        };

        regidentProvinceAllClick=function(){
            //$(".con .provinceAll").show();
            $("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentProvinceAll").show().siblings().hide();
        };

        regidentCityAllClick=function(){
            // $(".con .cityAll").show();
            if($("#province").val()!=''){
                $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCityLoad($("#province").val());
                $(".con .regidentCityAll").show().siblings().hide();
            }
        };

        regidentTownAllClick=function(){
            if($("#city").val()!=''){
                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCountyLoad($("#city").val());
                $(".con .regidentTownAll").show().siblings().hide();
            }
        };

        regidentStreetAllClick=function(){
            if($("#town").val()!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:$("#town").val()},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                regidentStreetLoad($("#town").val());
                                $(".con .regidentStreetAll").show().siblings().hide();
                            }else{
                                //无 街道信息 显示区/县信息
                                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                //countyLoad(cityId);
                                $(".con .regidentTownAll").show().siblings().hide();
                            }
                        }
                    },
                    complete : function() {
                    }
                });



            }
        };


        $('body').on('click', function() {
            closeRegidentArea();
        });

        //关闭区域选择器
        closeRegidentArea=function(){
            if(typeof($('#regidentArea').html())!='undefined'){
                var regidentProvinceName=$("#regidentProvinceName").val();
                var regidentCityName=$("#regidentCityName").val();
                var regidentCountyName=$("#regidentCountyName").val();
                var regidentStreetName=$("#regidentStreetName").val();
                var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
                $("#regidentAddress").val(add);
            }
            //alert(1);
            $('#regidentArea').remove();
            //console.log($('#regidentArea').html());
        }
        //-------------------------居住区域end----------------------------
    });
</script>
<script id="regidentAreaTpl" type="text/html">
    <div id="regidentArea" style="width:700px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
        <div class="tabs clearfix">
            <ul>
                <li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
                <li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
                <li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
                <li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
            </ul>
        </div>
        <div class="con" style="height:300px;overflow-y: auto;">
            <div class="regidentProvinceAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentCityAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentTownAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentStreetAll">
                <div class="list">

                </div>
            </div>
        </div>
    </div>
</script>
#end

#define content()
<form class="layui-form" id="cardInfoForm" action="" method="post" style="margin-top: 15px;margin-left: -30px;">
    <input type="hidden" name="cardId" value="#(cardId??)"/>
    <input type="hidden" name="memberId" value="#(memberId??)"/>
    <input type="hidden" value="#(card.openTime??)" id="time"/>
    <input type="hidden" name="operator" value="" id="operator"/>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="co">*</span>姓氏</label>
        <div class="layui-input-inline">
            <input type="text" name="member.surname" class="layui-input" lay-verify="required" value="#(member.surname??)" placeholder="请输入姓氏">
        </div>
        <label class="layui-form-label"><span class="co">*</span>姓名</label>
        <div class="layui-input-inline">
            <input type="text" id="cardName" name="member.fullName" class="layui-input" lay-verify="required" value="#(member.fullName??)" placeholder="请输入姓名">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="co">*</span>证件类型</label>
        <div class="layui-input-inline">
            <select id="idcardType" name="member.idcardType" lay-verify="required">
                #getDictList("id_card_type")
                <option value="#(key)" #(card !=null ?(member.idcardType??==key?'selected':''):'')>#(value)</option>
                #end
            </select>
        </div>
        <label class="layui-form-label" style="width:100px;"><span class="co">*</span>身份证</label>
        <div class="layui-input-inline">
            <input type="text" id="idcard" name="member.idcard" class="layui-input" lay-verify="required" value="#(member.idcard??)" placeholder="请输入身份证">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">性别</label>
        <div class="layui-input-inline">
            <select id="cardGender" name="member.gender">
                <option value="">请选择性别</option>
                #getDictList("gender")
                <option value="#(key)" #(card != null ?(key == member.gender?? ? 'selected':''):'')>#(value)</option>
                #end
            </select>
        </div>
        <label class="layui-form-label" style="width:100px;">出生日期</label>
        <div class="layui-input-inline">
            <input type="text" id="birthday" name="member.birthday" value="#(member != null ? member.birthday:'')"  placeholder="请选择出生日期" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">手机号</label>
        <div class="layui-input-inline">
            <input type="text" id="telephone" name="member.telephone" class="layui-input" value="#(member.telephone??)" placeholder="请输入手机号">
        </div>
        <label class="layui-form-label" style="width:100px;">业务员</label>
        <div class="layui-input-inline" style="width:115px;">
            <input type="text" id="operatorName" class="layui-input" value="#(user.name??)" placeholder="请选择业务员" readonly>
        </div>
        <input type="button" class="layui-btn" value="选择" id="chooseUser"/>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">居住区域</label>
        <div class="layui-input-block" style="width:69%;">
            <input type="hidden" id="province" name="member.province" value="#(member.province??)" />
            <input type="hidden" id="city" name="member.city" value="#(member.city??)" />
            <input type="hidden" id="town" name="member.town" value="#(member.town??)">
            <input type="hidden" id="street" name="member.street" value="#(member.street??)">
            <input type="hidden" id="regidentProvinceName" value="#(province??)" />
            <input type="hidden" id="regidentCityName" value="#(city??)" />
            <input type="hidden" id="regidentCountyName" value="#(town??)">
            <input type="hidden" id="regidentStreetName" value="#(street??)">
            <input type="text" id="regidentAddress" readonly="readonly"  name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">详细地址</label>
        <div class="layui-input-block" style="width:69%;">
            <input type="text" id="address" name="member.address" value="#(member != null ?member.address:'')" autocomplete="off" placeholder="请输入住址" class="layui-input">
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <!--<button class="layui-btn" type="button" id="searchIDCardBtn">扫描身份证</button>-->
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">确认更换</button>
        </div>
    </div>
</form>
#end