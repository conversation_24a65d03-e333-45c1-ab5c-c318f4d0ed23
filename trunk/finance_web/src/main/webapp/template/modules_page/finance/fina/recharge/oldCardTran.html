#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()原会员卡转移记录页面#end

#define css()
#end

#define content()
<div class="layui-collapse">
	<div class="layui-row">
		<form class="layui-form">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">转移类型</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" value="#(transferRecord.type??=='0'?'过户':'升级')" readonly="readonly" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">过户费</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" value="#(transferRecord.transfer_fee??)" readonly="readonly" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">遗失费</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" value="#(transferRecord.loss_fee??)" readonly="readonly" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">补款金额</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" value="#(transferRecord.pay_amount??)" readonly="readonly" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">获得天数</label>
					<div class="layui-input-inline">
						<input type="text" class="layui-input" value="#(transferRecord.get_days??)" readonly="readonly" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">获得金额</label>
					<div class="layui-input-inline">
						<input type="hidden" id="transferId" value="#(transferRecord.id??)"/>
						<input type="text" class="layui-input" value="#(transferRecord.get_amount??)" readonly="readonly" autocomplete="off">
					</div>
				</div>
			</div>
		</form>
	</div>
	<div class="layui-row">
		<table id="transferDetailTable" lay-filter="transferDetailTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="barDemo">
	#[[
	{{#if(d.type!='give_prestore' && d.type!='cancel_recharge' && d.type!='cancel_give'){}}
	<a class="layui-btn layui-btn-xs" lay-event="print">打印</a>
	{{#}}}
	]]#
</script>
<script type="text/javascript">
var table,$ ;
layui.use(['table','laydate','layer'],function(){
	table = layui.table;
	$ = layui.$ ;
	var layer = layui.layer;

	//充值扣卡列表
	function loadConsumeRecordTable(transferId){
		if (table != null && table != '') {
			table.render({
				id : 'transferDetailTable',
				elem : "#transferDetailTable" ,
				url : '#(ctxPath)/fina/recharge/transferDetailPage' ,
				where: {transferId: transferId},
				cols : [[
					{type:'numbers',title:'序号',width:60,unresize:true},
					{field:'oldCardNumber',title:'旧卡号',unresize:false},
					{field:'oldCardAmount',title:'旧卡金额',unresize:false},
					{field:'oldCardDays',title:'旧卡天数',unresize:false},
					{field:'oldCardPoints',title:'旧卡点数',unresize:false},
					{field:'oldCardIntegrals',title:'旧卡积分',unresize:false},
					{field:'oldCardPeas',title:'旧卡豆豆券',unresize:false},
					{field:'oldCardAccountBalance',title:'旧卡账面余额',unresize:false},
					{field:'oldCardTransferDays',title:'转换天数',unresize:false},
					{field:'oldCardTransferMoney',title:'转换金额',unresize:false}
// 					{field: '', title: '操作',width:'8%',unresize:false,toolbar:"#barDemo"}
				]] ,
				page : true,
				limit : 10
			}) ;
		}
		//打印小票
// 		table.on('tool(transferDetailTable)',function(obj){
// 			if (obj.event === 'print') {
// 				var id = obj.data.id;
// 				if(id == null || id == '' || id == undefined) {
// 					layer.msg("未获取到交易记录,请重新再试",{time: 1500});
// 				}else{
// 					var url = "#(ctxPath)/fina/consumerecord/printForm?id=" + id;
// 					layerShow("会员费用小票", url, 450, 670);
// 				}
// 			}
// 		});
	}
	
	loadConsumeRecordTable($("#transferId").val());
});
</script>
#end