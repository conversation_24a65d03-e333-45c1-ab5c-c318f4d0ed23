#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()未结算扣卡天数#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
    <div class="layui-row">
		<div class="layui-inline">
       		<label class="layui-form-label">基地</label>
            <div class="layui-input-inline">
                <select name="baseId" id="baseId">
                    #for(base : baseList)
                    <option value="#(base.id)">#(base.baseName)</option>
                    #end
                </select>
            </div>
       	</div>
		<div class="layui-inline">
       		<label class="layui-form-label">是否结算</label>
            <div class="layui-input-inline" style="width:100px;">
                <select name="isSettle" id="isSettle" lay-filter="isSettle">
                    <option value="0">未结算</option>
                    <option value="1">已结算</option>
                    <option value="2">全部</option>
                </select>
            </div>
       	</div>
		<div id="dateRangeDiv" class="layui-inline">
       		<label class="layui-form-label">时间</label>
            <div class="layui-input-inline">
                <input id="dateRange" name="dateRange" class="layui-input" placeholder="">
            </div>
       	</div>
		<div id="yearMonthDiv" class="layui-inline" style="display: none;">
       		<label class="layui-form-label">月份</label>
            <div class="layui-input-inline" style="width:100px;">
                <input id="yearMonth" name="yearMonth" class="layui-input" placeholder="">
            </div>
       	</div>
       	<div class="layui-inline">
            <button type="button" id="search" class="layui-btn" style="padding: 0 10px;border-radius: 5px;">查询</button>
       	</div>
    </div>
    </form>
    <div class="layui-row">
	    <table id="expiredCardTable" lay-filter="expiredCardTable"></table>
    </div>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#dateRange'
            ,range: true
            ,trigger:'click'
            ,value:'#(startDate) - #(endDate)'
        });

        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
            ,value:'#(yearMonth)'
        });

        locaTale=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'expiredCardTable'
                ,elem : '#expiredCardTable'
                ,method : 'POST'
                ,height: 'full-90'
                ,limit : 10
                ,where:data
                ,limits : [10,20,30,40,50]
                ,url : '#(ctxPath)/fina/expenseRecord/monthSettleRecordList'
                ,cellMinWidth: 80
                ,totalRow: true
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'cardType', title: '会员类型名称', align: 'center', unresize: true}
                    ,{field:'sumTimes', title: '天数', align: 'center', unresize: true}
                    ,{field:'sumAmount', title: '金额', align: 'center', unresize: true}
                    ,{field:'amountTime', title: '金额转天数', align: 'center', unresize: true}
                    ,{field:'sumIntegrals', title: '积分', align: 'center', unresize: true}
                    ,{field:'timesUnitPrice', title: '天数计算单价', align: 'center', unresize: true}
                    ,{field:'amountUnitPrice', title: '金额计算单价', align: 'center', unresize: true}
                    ,{field:'integralsUnitPrice', title: '积分计算单价', align: 'center', unresize: true}
                    ,{field:'totalTime', title: '总天数', align: 'center', unresize: true, totalRow: true}
                    ,{field:'totalUnitPrice', title: '总单价', align: 'center', unresize: true, totalRow: true}
                ]]
                ,page : false
                ,done:function (){
                    layer.close(loadingIndex);
                }
            });
        }

        locaTale({"baseId":$("#baseId").val(),"dateRange":$("#dateRange").val(),"isSettle":$("#isSettle").val(),"yearMonth":$("#yearMonth").val()})

        form.on('select(isSettle)',function (obj) {
            if(obj.value=='0'){
                $("#dateRangeDiv").attr("style","display:inline-block");
                $("#yearMonthDiv").attr("style","display:none");
            }else if(obj.value=='1'){
                $("#yearMonthDiv").attr("style","display:inline-block");
                $("#dateRangeDiv").attr("style","display:none");
            }else if(obj.value=='2'){
                $("#yearMonthDiv").attr("style","display:inline-block");
                $("#dateRangeDiv").attr("style","display:none");
            }
        });

        recoveryCardTableReload=function(){
            table.reload('expiredCardTable',{"where":{"nameOrIdcardOrCardNumber":$("#nameOrIdcardOrCardNumber").val()}});
        }

        // 搜索
        $("#search").click(function(){
            locaTale({"baseId":$("#baseId").val(),"dateRange":$("#dateRange").val(),"isSettle":$("#isSettle").val(),"yearMonth":$("#yearMonth").val()})
        });
		
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#search').click();
				return false
			}
		}
    });
</script>
<script type="text/html" id="actionBar">

</script>
#end
