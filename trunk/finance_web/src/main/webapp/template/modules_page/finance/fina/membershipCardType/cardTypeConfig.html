#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡类型配置页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
	div.layui-form-item{
		padding-left:50px;
	}
</style>
#end

#define js()
<script>
layui.use(['form'],function(){
	var form = layui.form;
	var $ = layui.$;
		
	form.on('select(deductSchemeFilter)', function(data){
		var price = $(data.elem[data.elem.selectedIndex]).attr("price");
		if(price!=null && price!=''){
			$("#price").val(price);
		}else{
			$("#price").val(0);
		}
	});
		
	form.on('select(longDeductSchemeFilter)', function(data){
		var price = $(data.elem[data.elem.selectedIndex]).attr("price");
		if(price!=null && price!=''){
			$("#price").val(price);
		}else{
			$("#price").val(0);
		}
	});

	//校验
	form.verify({
		checkAmount:function(value){
			if(value != null){
				var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
				var reg2 = new RegExp("^[0-9]*$");
				if(!reg1.test(value) && !reg2.test(value)){
					return "只能输入数字和小数点后两位小数";
				}
			}
		},
		checkNumber:function(value){
			if(value != null){
				var reg = new RegExp("^[0-9]*$");
				if(!reg.test(value)){
					return "只能输入数字";
				}
			}
		},
		checkPhone:function(value){
			if(value != null && value.length >0){
				var reg = new RegExp("^(13[0-9]|14[0-9]|15[0-9]|18[0-9]|17[0-9])\\d{8}$");
				if(!reg.test(value)){
					return "手机号码格式不正确";
				}
			}
		}
	});
	
	//保存
	form.on('submit(saveBtn)', function(formObj) {
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/fina/cardType/configSave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
               	if(rep.state === 'ok') {
					parent.cardTypeTableReload(null);
				}
				pop_close();
            },
            complete : function() {
		    }
        });
		return false;
	});

});
</script>
#end

#define content()
<div class="layui-form-item" style="margin-top:10px;">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label">类别名称</label>
			<div class="layui-input-block">
				<input type="text" class="layui-input" value="#(cardType.cardType??)" placeholder="请输入类别名称">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">合同金额</label>
			<div class="layui-input-inline">
				<input type="text" name="config.buyCardMoney" class="layui-input" value="#(model.buyCardMoney??)" placeholder="请输入合同金额" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">合同天数</label>
			<div class="layui-input-inline">
				<input type="text" name="config.contractTimes" class="layui-input" value="#(model.contractTimes??)" placeholder="请输入合同天数" lay-verify="checkAmount">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">实收金额</label>
			<div class="layui-input-inline">
				<input type="text" name="config.collectAmount" class="layui-input" value="#(model.collectAmount??)" placeholder="请输入实收金额" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">是否可预订</label>
			<div class="layui-input-inline">
				<input type="radio" name="config.isBooking" value="0" title="否" #if(model.isBooking??=='0')checked#end>
                <input type="radio" name="config.isBooking" value="1" title="是" #if(model.isBooking??''==''||model.isBooking??=='1')checked#end>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">折扣率(%)</label>
			<div class="layui-input-inline">
				<input type="text" name="config.contractDiscount" class="layui-input" value="#(model.contractDiscount??)" placeholder="请输入折扣率(%)" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">赠送天数</label>
			<div class="layui-input-inline">
				<input type="text" name="config.giveDays" class="layui-input" value="#(model.giveDays??)" placeholder="请输入赠送天数" lay-verify="checkAmount">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" >计划售价</label>
			<div class="layui-input-inline">
				<input type="text" id="price" name="config.price" class="layui-input" value="#(model.price??)" placeholder="请输入计划售价" maxlength="6">
			</div>
			<label class="layui-form-label" >实际售价</label>
			<div class="layui-input-inline">
				<input type="text" name="config.referencePrice" class="layui-input" value="#(model.referencePrice??)" placeholder="请输入实际售价" maxlength="6">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">剩余金额</label>
			<div class="layui-input-inline">
				<input type="text" name="config.balanceMoney" class="layui-input" value="#(model.balanceMoney??)" placeholder="请输入剩余金额" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">剩余天数</label>
			<div class="layui-input-inline">
				<input type="text" name="config.balanceDays" class="layui-input" value="#(model.balanceDays??)" placeholder="请输入剩余天数" lay-verify="checkAmount">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">剩余点数</label>
			<div class="layui-input-inline">
				<input type="text" name="config.balancePoints" class="layui-input" value="#(model.balancePoints??)" placeholder="请输入剩余点数" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">剩余积分</label>
			<div class="layui-input-inline">
				<input type="text" name="config.balanceIntegrals" class="layui-input" value="#(model.balanceIntegrals??)" placeholder="请输入剩余积分" lay-verify="checkAmount">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">购买金额</label>
			<div class="layui-input-inline">
				<input type="text" name="config.buyRechargeAmount" class="layui-input" value="#(model.buyRechargeAmount??)" placeholder="请输入购买金额" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">购买天数</label>
			<div class="layui-input-inline">
				<input type="text" name="config.buyRechargeDays" class="layui-input" value="#(model.buyRechargeDays??)" placeholder="请输入购买天数" lay-verify="checkAmount">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">赠送金额</label>
			<div class="layui-input-inline">
				<input type="text" name="config.giveRechargeAmount" class="layui-input" value="#(model.giveRechargeAmount??)" placeholder="请输入赠送金额" lay-verify="checkAmount">
			</div>
			<label class="layui-form-label">赠送积分</label>
			<div class="layui-input-inline">
				<input type="text" name="config.giveRechargeIntegrals" class="layui-input" value="#(model.giveRechargeIntegrals??)" placeholder="请输入赠送积分" lay-verify="checkAmount">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="padding: 8px 5px;">旅居扣费方案</label>
			<div class="layui-input-inline">
				<select id="deductSchemeId" name="config.deductSchemeId" lay-filter="deductSchemeFilter" lay-search>
					<option value="">请选择旅居扣费方案</option>
					#for(deduct : sojournSchemeList)
						<option value="#(deduct.id)" price="#(deduct.price)" #if(deduct.id==model.deductSchemeId??'')selected#end>#(deduct.name)</option>
					#end
				</select>
			</div>
			<label class="layui-form-label" style="padding: 8px 5px;">长住扣费方案</label>
			<div class="layui-input-inline">
				<select id="longDeductSchemeId" name="config.longDeductSchemeId" lay-filter="longDeductSchemeFilter" lay-search>
					<option value="">请选择长住扣费方案</option>
					#for(deduct : longSchemeList)
						<option value="#(deduct.id)" price="#(deduct.price)" #if(deduct.id==model.longDeductSchemeId??'')selected#end>#(deduct.name)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">年限类型</label>
			<div class="layui-input-inline">
				<select name="config.yearLimit">
					<option value="">请选择年限类型</option>
					#for(cardYearLimit : cardYearLimitList)
						<option value="#(cardYearLimit.dictValue)" #if(model.yearLimit??==cardYearLimit.dictValue) selected #end>#(cardYearLimit.name)</option>
					#end
				</select>
			</div>
			<label class="layui-form-label">使用年限</label>
			<div class="layui-input-inline">
				<input type="text" name="config.useYears" id="useYears" class="layui-input" value="#(model.useYears??)" placeholder="请输入会员卡使用年限" maxlength="3" lay-verify="number">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="padding: 8px 5px;">分期赠送方案</label>
			<div class="layui-input-block" >
				<select id="giveSchemeId" name="config.giveSchemeId" lay-search>
					<option value="">请选择赠送方案</option>
					#for(giveScheme : giveSchemeList)
					<option value="#(giveScheme.id)" #if(giveScheme.id==model.giveSchemeId??'')selected#end>#(giveScheme.name??)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="padding: 8px 5px;">条件赠送方案</label>
			<div class="layui-input-block" >
				<select id="conditionRechargeSchemeId" name="config.conditionRechargeSchemeId" lay-search>
					<option value="">请选择赠送方案</option>
					#for(giveScheme : conditionRechargeSchemeList)
					<option value="#(giveScheme.id)" #if(giveScheme.id==model.conditionRechargeSchemeId??'')selected#end>#(giveScheme.name??)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="padding: 8px 5px;width: 140px;">是否开启会员卡对账</label>
			<div class="layui-input-inline">
				<input type="radio" name="config.isReconciliation" value="0" title="否" #if(model.isReconciliation??=='0' || model==null )checked#end>
				<input type="radio" name="config.isReconciliation" value="1" title="是" #if(model.isReconciliation??=='1')checked#end>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 125px;">未对账提醒日</label>
			<div class="layui-input-inline">
				<select name="config.warnDay">
					<option value="">请选择</option>
					#for(i=1; i<=31; i++)
					<option value="#(i)" #if(i==model.warnDay??) selected #end>#(i)</option>
					#end
				</select>
			</div>
			<label class="layui-form-label" style="width: 125px;">未对账禁用日</label>
			<div class="layui-input-inline">
				<select name="config.disabledDay">
					<option value="">请选择</option>
					#for(i=1; i<=31; i++)
					<option value="#(i)" #if(i==model.disabledDay??) selected #end>#(i)</option>
					#end
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label" style="padding: 8px 5px;">分期充值方案</label>
			<div class="layui-input-block" >
				<select id="rechargeSchemeId" name="config.rechargeSchemeId" lay-search>
					<option value="">请选择充值方案</option>
					#for(giveScheme : rechargeSchemeList)
					<option value="#(giveScheme.id)" #if(giveScheme.id==model.rechargeSchemeId??'')selected#end>#(giveScheme.name??)</option>
					#end
				</select>
			</div>

		</div>

		<div class="layui-form-item">
			<label class="layui-form-label">备注</label>
			<div class="layui-input-block">
				<textarea name="type.remark" placeholder="请输入备注内容" class="layui-textarea">#(cardType.remark??)</textarea>
			</div>
		</div>
		<div class="layui-form-item" style="margin-bottom:60px;"></div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" name="config.id" value="#(model.id??)"/>
				<input type="hidden" name="config.cardTypeId" value="#(model.cardTypeId??)"/>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end