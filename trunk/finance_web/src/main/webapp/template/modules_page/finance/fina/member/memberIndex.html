#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
			<div class="layui-inline">
				<label class="layui-form-label">会员姓名</label>
				<div class="layui-input-inline">
					<input type="text" id="fullName" name="fullName" class="layui-input" placeholder="请输入会员姓名" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">会员身份证</label>
				<div class="layui-input-inline">
					<input type="text" id="idcard" name="idcard" class="layui-input" placeholder="请输入会员身份证" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">会员手机</label>
				<div class="layui-input-inline">
					<input type="text" id="telephone" name="telephone" class="layui-input" placeholder="请输入会员手机" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">会员状态</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="memberStatus" name="memberStatus">
						<option value="">全部</option>
						#getDictList("member_status")
						<option value="#(key)">#(value)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">会员等级</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="memberLv" name="memberLv">
						<option value="">全部</option>
						#getDictList("member_lv")
						<option value="#(key)">#(value)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">作废状态</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="delFlag" name="delFlag">
						<option value="">全部</option>
						<option value="0" selected="selected">未作废</option>
						<option value="1">已作废</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-btn-group">
					<button type="button" id="search" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
					#shiroHasPermission("finance:member:addBtn")
						<button type="button" id="add" class="layui-btn">添加</button>
					#end
				</div>
			</div>
		</form>
	</div>
	<div class="layui-row">
		<table id="memberTable" lay-filter="memberTable"></table>
	</div>
</div>
#getDictLabel("gender")
#getDictLabel("member_status")
#getDictLabel("member_lv")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("finance:member:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("finance:telephoneSee")
	<a class="layui-btn layui-btn-xs" lay-event="link">联系人</a>
	#end
	#shiroHasPermission("finance:member:consumeBtn")
	<a class="layui-btn layui-btn-xs" lay-event="consume">消费状况</a>
	#end
	#shiroHasPermission("finance:member:deleteBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="delete">删除</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		function memberLoad(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'memberTable'
				,elem : '#memberTable'
				,method : 'POST'
				,url : '#(ctxPath)/fina/member/pageTable'
				,where : data
				,height:'full-130'
				,cols: [[
					{type: 'numbers', width:100, title: '序号',unresize:true}
					,{field:'fullName', title: '姓名', align: 'center',width:120, unresize: true}
					,{field:'', title: '性别', align: 'center',width:60, unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
					#shiroHasPermission("finance:idcardSee")
					,{field:'idcard', title: '身份证号', align: 'center',width:180, unresize: true}
					#end
					,{field:'birthday', title: '出生日期', align: 'center',width:120, unresize: true}
// 					#shiroHasPermission("finance:telephoneSee")
// 					,{field:'telephone', title: '电话', align: 'center',width:120, unresize: true}
// 					#end
					,{field:'linkName', title: '主要联系人', align: 'center', unresize: true}
					,{field:'', title: '会员状态', align: 'center',width:100, unresize: true,templet:"<div>{{ dictLabel(d.memberStatus,'member_status','- -') }}</div>"}
					,{field:'', title: '会员等级', align: 'center',width:100, unresize: true,templet:"<div>{{ dictLabel(d.memberLv,'member_lv','- -') }}</div>"}
					,{field:'unVoidCards', title: '未作废会员卡', align: 'center', unresize: true}
					,{field:'hadVoidCards', title: '已作废会员卡', align: 'center', unresize: true}
					,{field: '', title: '作废状态', align: 'center',width:90,unresize:false,templet:function (d) {
						if(d.delFlag==='1'){
							return '<span class="layui-badge">已作废</span>';
						}else{
							return '<span class="layui-badge layui-bg-green">未作废</span>';
						}
					}}
// 					,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 190, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 10
				,limits : [10,20,30,40]
				, done: function (res, curr, count) {
					// layer.closeAll('loading');
					layer.close(loadingIndex);
				}
			});
			table.on('tool(memberTable)',function(obj){
				if(obj.event === 'edit'){
					var url = "#(ctxPath)/fina/member/edit?id=" + obj.data.id ;
					pop_show("编辑",url,null,null);
				}else if(obj.event==='link'){
	                pop_show('联系人','#(ctxPath)/fina/member/linkIndex?id='+obj.data.id,'','');
	            }else if(obj.event==='consume'){
	                pop_show('消费状况','#(ctxPath)/fina/member/consumeIndex?id='+obj.data.id,'','');
	            }else if (obj.event === 'delete') {
					layer.confirm("确定要删除吗?删除后不能恢复,请谨慎操作!",function(index){
						util.sendAjax ({
							type: 'POST',
							url: '#(ctxPath)/fina/member/delete',
							notice: true,
							data: {id:obj.data.id},
							loadFlag: true,
							success : function(rep){
								if(rep.state=='ok'){
									tableReload('memberTable',null);
								}
								layer.close(index);
							},
							complete : function() {
							}
						});
					});
				}
			});
		};
		
		memberLoad({delFlag:$('#delFlag').val()});

		form.on("submit(search)",function(data){
			memberLoad(data.field);
			return false;
		});
		
		//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$("#search").click();
				return false
			}
		}

		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/fina/member/add" ;
			pop_show("新增会员档案",url,null,null);
		});

	});
</script>
#end