#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()换卡列表#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>全部更换</legend>
    </fieldset>
    <div class="layui-row">
        <input type="hidden" id="erId" value="#(erId??)"/><!--主账单id-->
        <table id="erTable" lay-filter="erTable"></table>
    </div>
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>单条更换</legend>
    </fieldset>
    <div class="layui-row">
        <table id="erDetailTable" lay-filter="erDetailTable"></table>
    </div>
</div>
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#getDictLabel("gender")
#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        erLoad(null);

        erDetailLoad(null);


        function erLoad(data){
            table.render({
                id : 'erTable'
                ,elem : '#erTable'
                ,method : 'POST'
                ,where : {"id":$("#erId").val()}
                ,height:150
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/expenseRecord/getMainRecordDetail'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'checkinNo', title: '入住人-预订号-入住号', align: 'center', unresize: false,templet:function (d) {
                            var str="";
                            if(d.name != null && d.name != null){
                                str = d.name;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.bookNo != null && d.bookNo != ''){
                                str += d.bookNo;
                            }else{
                                str += "--";
                            }
                            str += " ";
                            if(d.checkinNo != null && d.checkinNo != ''){
                                str += d.checkinNo;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'fullName', title: '会员卡信息', align: 'center', unresize: false,width:180,templet:function (d) {
                            var str="";
                            if(d.cardNumber != null && d.cardNumber != ''){
                                str = d.cardNumber;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.fullName != null && d.fullName != null){
                                str += d.fullName;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'baseName', title: '基地', align: 'center', unresize: false,width:180}
                    ,{field:'type', title: '类型', align: 'center', unresize: false,width:180,templet:"<div>{{d.type == 'normal_admission'?'<span class='layui-badge layui-bg-green'>主记录</span>':d.type == 'follow_checkin'?'<span class='layui-badge'>随行记录</span>':d.type == 'continued_residence'?'<span class='layui-badge'>续住记录</span>':'- -'}}</div>"}
                    ,{field:'bookStartTime', title: '预订时间', align: 'center', unresize: false,width:220,templet:function (d) {
                            var str="";
                            if(d.bookStartTime != null && d.bookStartTime != ''){
                                str = dateFormat(d.bookStartTime,'yyyy-MM-dd');
                            }else{
                                str = "--";
                            }
                            str += " 至 ";
                            if(d.bookEndTime != null && d.bookEndTime != ''){
                                str += dateFormat(d.bookEndTime,'yyyy-MM-dd');
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'checkinTime', title: '入住时间', align: 'center', unresize: false,width:220,templet:"<div>{{ dateFormat(d.checkinTime,'yyyy-MM-dd')}} 至 {{dateFormat(d.checkoutTime,'yyyy-MM-dd')}}</div>"}
                    //,{field:'checkinStatus', title: '入住状态', align: 'center',unresize: false,width:100,templet: "<div>{{ dictLabel(d.checkinStatus,'checkin_status','- -') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        table.on('tool(erTable)',function(obj){
            if(obj.event === 'changeCard'){
                var url = "#(ctxPath)/fina/expenseRecord/changeForm?id=" + obj.data.id;/*主账单id或随行记录id*/
                layui.layer.open({
                    type: 2,
                    area: ['70%', '65%'],
                    fix: true, //不固定
                    maxmin: true,
                    shade:0.4,
                    title: "整单分段换卡",
                    content: url
                });
            }
        });

        function erDetailLoad(data){
            table.render({
                id : 'erDetailTable'
                ,elem : '#erDetailTable'
                ,method : 'POST'
                ,where : {"id":$("#erId").val()}
                ,height:$(document).height()*0.60
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/expenseRecord/getAllRecordDetail'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'checkinNo', title: '入住人-预订号-入住号', align: 'center', unresize: false,templet:function (d) {
                            var str="";
                            if(d.name != null && d.name != null){
                                str = d.name;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.bookNo != null && d.bookNo != ''){
                                str += d.bookNo;
                            }else{
                                str += "--";
                            }
                            str += " ";
                            if(d.checkinNo != null && d.checkinNo != ''){
                                str += d.checkinNo;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'fullName', title: '会员卡信息', align: 'center', unresize: false,width:180,templet:function (d) {
                            var str="";
                            if(d.cardNumber != null && d.cardNumber != ''){
                                str = d.cardNumber;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.fullName != null && d.fullName != null){
                                str += d.fullName;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'baseName', title: '基地', align: 'center', unresize: false,width:180}
                    ,{field:'type', title: '类型', align: 'center', unresize: false,width:180,templet:"<div>{{d.type == 'normal_admission'?'<span class='layui-badge layui-bg-green'>主记录</span>':d.type == 'follow_checkin'?'<span class='layui-badge'>随行记录</span>':d.type == 'continued_residence'?'<span class='layui-badge layui-bg-orange'>续住记录</span>':'- -'}}</div>"}
                    ,{field:'bookStartTime', title: '预订时间', align: 'center', unresize: false,width:220,templet:function (d) {
                            var str="";
                            if(d.bookStartTime != null && d.bookStartTime != ''){
                                str = dateFormat(d.bookStartTime,'yyyy-MM-dd');
                            }else{
                                str = "--";
                            }
                            str += " 至 ";
                            if(d.bookEndTime != null && d.bookEndTime != ''){
                                str += dateFormat(d.bookEndTime,'yyyy-MM-dd');
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'checkinTime', title: '入住时间', align: 'center', unresize: false,width:220,templet:"<div>{{ dateFormat(d.checkinTime,'yyyy-MM-dd')}} 至 {{dateFormat(d.checkoutTime,'yyyy-MM-dd')}}</div>"}
                    //,{field:'checkinStatus', title: '入住状态', align: 'center',unresize: false,width:100,templet: "<div>{{ dictLabel(d.checkinStatus,'checkin_status','- -') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        erDetailReload=function(){
            erDetailLoad(null);
        };

        table.on('tool(erDetailTable)',function(obj){
            if(obj.event === 'changeCard'){
                var url = "#(ctxPath)/fina/expenseRecord/changeSingleForm?id=" + obj.data.id;/*主账单id或随行记录id*/
                layui.layer.open({
                    type: 2,
                    area: ['70%', '65%'],
                    fix: true, //不固定
                    maxmin: true,
                    shade:0.4,
                    title: "单条分段换卡",
                    content: url
                });
            }
        });

    });
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="changeCard">更换会员卡</a>
</script>
#end