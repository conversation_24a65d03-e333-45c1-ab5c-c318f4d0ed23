#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
<style>
    .layui-tab  td, .layui-tab th {
        padding: 9px 5px;
    }

    .layui-table-cell {
        padding: 0 5px;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">

            <div class="layui-inline">
                <label class="layui-form-label" style="width: 60px;">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="baseId" lay-filter="baseId">
                        #for(base : baseList)
                        <option value="#(base.id??)" submitUserId="#(base.lease_submit_user_id??)" checkUserId="#(base.lease_check_user_id??)" approveUserId="#(base.lease_approve_user_id??)" >#(base.base_name??)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"  style="width: 60px;">房间号：</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <input type="text" id="roomName" placeholder="请输入房间号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"  style="width: 80px;">业主姓名：</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <input type="text" id="name" placeholder="请输入业主姓名" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="padding: 9px 5px;width: 100px;">租金开始时间：</label>
                <div class="layui-input-inline">
                    <input type="text" id="dateRange" placeholder="请输入合同租金开始时间" autocomplete="off" class="layui-input">
                </div>
            </div>

            <!--<div class="layui-inline">
                <label class="layui-form-label">合同状态</label>
                <div class="layui-input-inline" style="width: 120px;">
                    <select id="contractStatus" name="contractStatus">
                        <option value="">全部</option>
                        <option value="1">未开始</option>
                        <option value="2">合同期</option>
                        <option value="3">已结束</option>
                    </select>
                </div>
            </div>-->

            <div class="layui-inline">
                <button type="button" id="searchConsumeBtn" class="layui-btn">搜索</button>
            </div>
            <div class="layui-inline">
                <button type="button" id="submitPayApplyBtn"  class="layui-btn">提交付款申请</button>
            </div>
            <div class="layui-inline">
                <button type="button" id="exportBtn"  class="layui-btn">导出</button>
            </div>
        </div>
        <input id="userId" value="#(userId??)" type="hidden">
    </form>
    <div class="layui-row">
        <table id="cardTypeTable" lay-filter="cardTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="rentAdd">查看租金涨幅</a>
        #shiroHasPermission("finance:leaseRoomPayBtn")
        <!--<a class="layui-btn layui-btn-xs" lay-event="edit">付款单</a>-->
        #end
        <a class="layui-btn layui-btn-xs" lay-event="file">附件</a>
        <a class="layui-btn layui-btn-xs" lay-event="see">预览付款单</a>
    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate','layer','form'],function(){
        var table = layui.table
            , $ = layui.$
            ,layer=layui.layer
            ,form=layui.form
        ;
        var laydate=layui.laydate;
        laydate.render({
            elem: '#dateRange'
            ,trigger:'click'
            ,range: true
            ,value:"#(dateRange??)"
        });
        /*laydate.render({
            elem: '#roomEndDate'
            ,trigger:'click'
            ,range: true
        });*/


        cardTypeTableLoad=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTable',
                elem:"#cardTypeTable",
                url:'#(ctxPath)/fina/leaseRecordApply/getWaitPayList',
                where:data,
                height:'full-90',
                cols:[[
                    {type:'checkbox'}
                    ,{type:'numbers',title:'序号',width:70,unresize:true}
                    ,{field: 'base_name', title: '基地', width:180,unresize:true}
                    ,{field:'room_name', title:'房间号', width:110, unresize:true, align:'center'}
                    ,{field:'name', title:'业主', width:100 , unresize:true, align:'center'}
                    ,{field:'rentMonth', title:'租金', width:100 , unresize:true, align:'center',templet:function (d) {
                            let unit='';
                            if(d.rent_unit=='year'){
                                unit="/年";
                            }else if(d.rent_unit=='month'){
                                unit="/月"
                            }else{
                            }
                            return d.rent+unit;
                        }}
                    ,{field:'rentPay', title:'房租支付方式', width:120 , unresize:true, align:'center',templet:function (d) {
                            let unit='';
                            if(d.payment_type=='year') {
                                unit = "年";
                            }else if(d.payment_type=='halfYear'){
                                unit="半年";
                            }else if(d.payment_type=='season'){
                                unit="季";
                            }else if(d.payment_type=='month'){
                                unit="月"
                            }else if(d.payment_type=='disposable'){
                                unit="一次性"
                            }
                            return unit+"付";
                        }}
                    ,{field:'amount', title:'本期租金', width:90 , unresize:true, align:'center'}
                    /*,{field:'status', title:'状态', width:90 , unresize:true, align:'center',templet:function (d) {
                            return '';
                        }}*/
                    ,{field:'date', title:'本期开始结束时间', width:190 , unresize:true, align:'center',templet:function (d) {
                            return dateFormat(d.start_date,"yyyy-MM-dd")+"至"+dateFormat(d.end_date,"yyyy-MM-dd")
                        }}
                    ,{field:'num', title:'当前期数/总期数',width:150 , unresize:true, align:'center',templet:function (d) {
                            return d.issue_no+"/"+d.maxIssueNo
                        }}
                    ,{field:'paymentWayStr', title:'业主收款方式', width:100 , unresize:true, align:'center'}
                    ,{field:'pay_account', title:'账号', width:150 , unresize:true, align:'center'}
                    ,{field:'bank_account_name', title:'开户名', width:90 , unresize:true, align:'center'}
                    ,{field:'bank_source', title:'开户行', width:90 , unresize:true, align:'center'}
                    ,{field:'remark',title:'备注',unresize:true}
                    //,{title:'操作',width:220, fixed:'right',unresize:true,toolbar:'#toolBar'}
                ]],
                page:false,
                limit:10
                , done: function (res, curr, count) {



                    var layerTips;
                    $("tr").on("mouseenter", function() {
                        let trIndex=$(this).attr("data-index");
                        if(trIndex==undefined){
                            return false;
                        }
                        let item;
                        $.each(res.data,function (index,d) {
                            if(index==trIndex){
                                item=d;
                                return false;
                            }
                        })

                        var that = this;
                        let text="<p><label style='width: 85px;display: inline-block;'>押金：</label>"+item.earnest_money+"</p>" +
                            "<p><label style='width: 85px;display: inline-block;'>合同期限：</label>"+ dateFormat(item.room_start_date,"yyyy-MM-dd")+"至"+dateFormat(item.room_end_date,"yyyy-MM-dd")
                            +"</p><p><label style='width: 85px;display: inline-block;'>房租付款期限：</label>"+dateFormat(item.contract_start_date,"yyyy-MM-dd")+"至"+dateFormat(item.contract_end_date,"yyyy-MM-dd")+"</p>";
                        text+="<p><label style='width: 85px;display: inline-block;'>租金涨幅：</label>每"+item.rent_add_year+"涨幅"+item.rent_add_ratio+"%</p>";
                        text+="<p><label style='width: 85px;display: inline-block;'>租金涨幅明细：</label>";
                        $.each(item.rentAddList,function (index,rentAdd) {
                            if(index==0){
                                text+=dateFormat(rentAdd.startDate,"yyyy-MM-dd")+"至"+dateFormat(rentAdd.endDate,"yyyy-MM-dd")+"租金"+rentAdd.rent+"/月</p>";
                            }else{
                                text+="<p style='padding-left: 85px;'>"+dateFormat(rentAdd.startDate,"yyyy-MM-dd")+"至"+dateFormat(rentAdd.endDate,"yyyy-MM-dd")+"租金"+rentAdd.rent+"/月</p>";
                            }

                        })

                        layerTips=layer.tips(text, that, {
                            tips: 1,
                            time: 0,
                            area:['400px','auto']
                        });

                    });
                    $("tr").on("mouseleave", function() {
                        layer.close(layerTips);
                    });
                    layer.close(loadingIndex);
                }
            });
            table.on('tool(cardTypeTable)',function(obj){
                if(obj.event === 'edit'){
                    layerShow('付款单','#(ctxPath)/fina/leaseRecordApply/leaseRoomPaymentIndex?leaseId='+obj.data.leaseId+"&roomId="+obj.data.roomId,1000,600);

                }else if(obj.event==='see'){
                    layerShow('付款单','#(ctxPath)/fina/leaseRecordApply/previewTableIndex?leaseId='+obj.data.leaseId+"&roomId="+obj.data.roomId,1000,600);
                }else if(obj.event==='file'){
                    layerShow(obj.data.baseName+'['+obj.data.roomName+']的附件','#(ctxPath)/fina/leaseRecordApply/leaseRoomFileIndex?id='+obj.data.id,800,600);
                }
            });
        }


        /*form.on('select(baseId)',function (obj) {
            let submituserid=$("#baseId option[value='"+obj.value+"']").attr('submituserid');
            if(submituserid==$("#userId").val()){
                $("#submitPayApplyBtn").css('display','block');
            }else{
                $("#submitPayApplyBtn").css('display','none');
            }
        })*/

        reloadCardTypeTable=function(){
            cardTypeTableLoad({contractStatus:$('#contractStatus').val(),baseId:$('#baseId').val(),roomName:$('#roomName').val()
                ,name:$('#name').val(),dateRange:$("#dateRange").val()});
        }

        $("#exportBtn").on('click',function () {

            layer.load();
            window.location.href='#(ctxPath)/fina/leaseRecordApply/getWaitPayListExport?baseId='+$("#baseId").val()+"&baseId="+$('#baseId').val()
                +"&dateRange="+$('#dateRange').val()+"&roomName="+$('#roomName').val();

            setInterval(function () {
                layer.closeAll('loading');
            },5000);
        });

        //reloadCardTypeTable();

        // 搜索消费记录按钮
        $('#searchConsumeBtn').on('click', function(){
            reloadCardTypeTable();

            let submituserid=$("#baseId option:selected").attr('submituserid');
            if(submituserid==$("#userId").val()){
                $("#submitPayApplyBtn").css('display','block');
            }else{
                $("#submitPayApplyBtn").css('display','none');
            }
        });
        $('#searchConsumeBtn').click();
        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#searchConsumeBtn').click();
                return false
            }
        }

        $("#submitPayApplyBtn").click(function () {
            var checkStatus = table.checkStatus('cardTypeTable');
            if(checkStatus.data.length==0){
                layer.msg('勾选的条数不能为0!',{icon:5});
                return false;
            }

            let dataIds=[];
            $.each(checkStatus.data,function (index,item) {
                dataIds.push(item.id);
            });
            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "查看详情",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['500px;','500px;'],
                content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                    "            <div class=\"layui-form-item\" style='margin-top: 20px;'>\n" +
                    "                <label class=\"layui-form-label\"><font color='red'>*</font>标题</label>\n" +
                    "                <div class=\"layui-input-block\" >\n" +
                    "                    <input class=\"layui-input\" placeholder=\"请输入标题\" value='' name=\"title\" id=\"title\" autocomplete=\"off\">\n" +
                    "                </div>\n" +
                    "            </div>\n" +
                    "\n" +
                    "            <div class=\"layui-form-item\">\n" +
                    "                <label class=\"layui-form-label\"><font color='red'></font>备注</label>\n" +
                    "                <div class=\"layui-input-block\"  >" +
                    "                   <textarea placeholder=\"请输入内容\" class=\"layui-textarea\" name='remark'></textarea>" +
                    "                </div>\n" +
                    "            </div>" +
                    "</form>",
                        cancel: function(){
                    },
                    end : function(){
                    },yes: function(index, layero){


                        if($("#title").val()==''){
                            layer.msg('标题必须填写', {icon: 2, offset: 'auto'});
                            return false;
                        }
                        layer.load();
                        let data={"title":$("#title").val(),"remark":$("#remark").val(),"dataIds":JSON.stringify(dataIds)};
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApply',
                            data: data,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    //pageTableReload();
                                    reloadCardTypeTable();
                                    layer.closeAll("loading");
                                    layer.close(index);
                                }
                            },
                            complete : function() {
                            }
                        });
                        return false;
                    }

                });


        })


    });
</script>
#end