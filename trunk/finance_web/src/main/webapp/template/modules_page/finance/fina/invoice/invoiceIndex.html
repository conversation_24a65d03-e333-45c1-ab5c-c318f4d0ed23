#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()发票管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
    <div class="layui-row">
    	<div class="layui-inline">
       		<label class="layui-form-label">发票抬头</label>
            <div class="layui-input-inline">
                <input id="name" name="name" class="layui-input">
            </div>
       	</div>
    	<div class="layui-inline">
       		<label class="layui-form-label">会员卡号</label>
            <div class="layui-input-inline">
                <input type="text" id="cardNumber" name="cardNumber" autocomplete="off" class="layui-input" >
            </div>
       	</div>
       	<div class="layui-inline">
            <button type="button" id="search" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
       	</div>
    </div>
    </form>
    <div class="layui-row">
	    <table id="invoiceTable" lay-filter="invoiceTable"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("finance:invoiceManager:detailBtn")
    	<a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
    #end
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        function invoiceLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'invoiceTable'
                ,elem : '#invoiceTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-90'
                ,url : '#(ctxPath)/fina/invoice/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:80, title: '序号',unresize:false}
                    ,{field:'applyNo', title: '发票单号', align: 'center', unresize: false}
                    ,{field:'fullName', title: '会员卡信息', align: 'center', unresize: false,templet:function (d) {
                            var str="";
                            if(d.cardNumber != null && d.cardNumber != ''){
                                str = d.cardNumber;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.fullName != null && d.fullName != null){
                                str += d.fullName;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'name', title: '抬头', align: 'center', unresize: false,width: 220}
                    ,{field:'consumeType', title: '消费类型', align: 'center', unresize: false,templet:"<div>{{d.consumeType == 1?'<span class='layui-badge layui-bg-green'>合同</span>':d.consumeType == 2?'<span class='layui-badge'>入住单</span>':'- -'}}</div>"}
                    ,{field:'invoiceType', title: '发票类型', align: 'center', unresize: false,templet:"<div>{{d.invoiceType == 1?'<span class='layui-badge layui-bg-green'>纸质发票</span>':d.invoiceType == 2?'<span class='layui-badge'>电子发票</span>':'- -'}}</div>"}
                    ,{field:'invoiceObj', title: '开票对象', align: 'center', unresize: false,templet:"<div>{{d.invoiceObj == 1?'<span class='layui-badge layui-bg-green'>个人</span>':d.invoiceObj == 2?'<span class='layui-badge'>公司</span>':'- -'}}</div>"}
                    ,{field:'totalPrice', title: '开票总金额', align: 'center', unresize: false,width:100}
                    ,{field:'mobile', title: '手机号码', align: 'center', unresize: false}
                    ,{field:'invoiceTime', title: '开票时间', sort: true, align: 'center', unresize: false}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: false, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,limit : 15
                ,limits : [15,30,45,50]
                , done: function (res, curr, count) {
                    layer.close(loadingIndex);
                }
            });
            table.on('tool(invoiceTable)',function(obj){
                if(obj.event === 'detail'){
                    //var url = "#(ctxPath)/fina/invoice/form?id=" + obj.data.id ;
                    /*注释的未旅居调接口使用*/
                    var url = "#(ctxPath)/fina/invoice/formSojourn?id=" + obj.data.id+"&invoiceCode="+obj.data.invoiceCode+"&invoiceNo="+obj.data.invoiceNo+"&identificationNo="+obj.data.identificationNo
                    +"&bankName="+obj.data.bankName+"&bankCardNo="+obj.data.bankCardNo+"&contractNo="+obj.data.contractNo+"&baseName="+obj.data.baseName+"&checkinDays="+obj.data.checkinDays+"&receiveName="+obj.data.receiveName
                        +"&landlineNo="+obj.data.landlineNo+"&email="+obj.data.email+"&receiveMobile="+obj.data.receiveMobile+"&receiveAddress="+obj.data.receiveAddress+"&remark="+obj.data.remark;
                    layerShow("发票详情",url,900,700);
                }
            });
        };
        
        invoiceLoad(null);

        sd=form.on("submit(search)",function(data){
            invoiceLoad(data.field);
            return false;
        });
        
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#search').click();
				return false
			}
		}
    });
</script>
#end