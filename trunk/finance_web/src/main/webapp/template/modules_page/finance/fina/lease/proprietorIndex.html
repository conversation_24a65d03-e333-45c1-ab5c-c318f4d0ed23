#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商查询#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">
            <div class="layui-inline">
                <label class="layui-form-label">供应商编码</label>
                <div class="layui-input-inline">
                    <input id="supplierNo" name="supplierNo" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">供应商名称</label>
                <div class="layui-input-inline">
                    <input id="name" name="name" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <div class="layui-btn-group">
                    <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>

                </div>
            </div>
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="supplierTable" lay-filter="supplierTable"></table>
    </div>
</div>
#getDictLabel("supplier_level")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
<!--        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>-->
<!--        <a class="layui-btn layui-btn-xs" lay-event="pay">付款信息</a>-->
        <a class="layui-btn layui-btn-xs" lay-event="selected">选择</a>
    </div>
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        supplierTableReload = function (data) {
            table.render({
                id : 'supplierTable'
                ,elem : '#supplierTable'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/fina/leaseRecordApply/supplierPage'
                ,height:'full-90'
                ,cols: [[
                    {field:'supplierNo', title: '编号', align: 'center', width: 150, unresize: true}
                    ,{field:'name', title: '名称', align: 'center', width: 300, unresize: true}
                    ,{field:'type', title: '供应商类型', align: 'center', width: 300, unresize: true,templet:function (d) {
                            if(d.type=='1'){
                                return '企业';
                            }else if(d.type=='2'){
                                return '个人';
                            }
                        }}
                    ,{field:'mainLinkMan', title: '主要联系人', align: 'center', width: 120, unresize: true}
                    ,{field:'postalCode', title: '邮政编码', align: 'center', width: 120, unresize: true}
                    ,{field:'faxNumber', title: '传真号码', align: 'center', width: 120, unresize: true}
                    ,{field:'fixedTelephone', title: '固定电话', align: 'center', width: 120, unresize: true}
                    ,{field:'mailBox', title: '邮箱', align: 'center', width: 150, unresize: true}
                    ,{field:'', title: '开始合作时间', align: 'center', width: 120, unresize: true,templet:"<div>{{ dateFormat(d.startTime,'yyyy-MM-dd') }}</div>"}
                    ,{field:'', title: '是否有效', align: 'center', width: 100, unresize: true,templet:"#[[<div>{{#if(d.isEnabled=='1'){}} <span class='layui-badge layui-bg-green'>是</span> {{#}else{}} <span class='layui-badge'>否</span> {{#}}} </div>]]#"}
                    ,{field:'supplierAddress', title: '地址', align: 'center', width: 300, unresize: true}
                    ,{field:'description', title: '描述', align: 'center', width: 300, unresize: true}
                    ,{fixed:'right', title: '操作', width: 230, align: 'center', unresize: true, toolbar: '#toolBar'}
                ]]
                ,page : true
                ,done:function () {
                }
            });
            table.on('tool(supplierTable)',function (obj) {
                if(obj.event==='edit'){
                    pop_show('编辑','#(ctxPath)/wms/supplier/supplierForm?id='+obj.data.id,'','');
                }else if(obj.event==='pay'){
                    pop_show('付款信息','#(ctxPath)/wms/supplier/payIndex?id='+obj.data.id,'','');
                }else if(obj.event==='link'){
                    pop_show('联系人信息','#(ctxPath)/wms/supplier/linkIndex?id='+obj.data.id,'','');
                }else if(obj.event==='del'){
                    layer.confirm("确定要作废吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/wms/supplier/saveSupplier',
                            notice: true,
                            data: {id:obj.data.id, delFlag:'1'},
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    table.reload('supplierTable');
                                }
                                layer.close(index);
                            },
                            complete : function() {
                            }
                        });
                    });
                }else if(obj.event==='selected'){
                    parent.selectedProprietor(obj.data);
                    pop_close();
                }
            });
        }

        supplierTableReload();

        form.on("submit(search)",function(data){
            supplierTableReload(data.field);
            return false;
        });

        $("#addBtn").on('click',function () {
            pop_show('添加','#(ctxPath)/wms/supplier/supplierForm','','');
        });
    });
</script>

#end