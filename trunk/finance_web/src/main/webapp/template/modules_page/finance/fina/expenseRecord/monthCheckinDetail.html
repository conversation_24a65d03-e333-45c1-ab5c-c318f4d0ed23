#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()月入住单明细#end

#define css()
<style>
     .layui-table-cell{
         padding: 0 3px;
     }
/*     .laytable-cell-checkbox .layui-disabled.layui-form-checked i { */
/*         background: #fff !important; */
/*     } */
</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            <div class="layui-inline">
            	<label class="layui-form-label">查询类型:</label>
	            <div class="layui-input-inline">
	                <select id="queryType" name="queryType" lay-filter="queryType">
	                    <option value="month">按月</option>
	                    <option value="day">按天</option>
	                </select>
	            </div>
            </div>
            <div class="layui-inline">
            	<label class="layui-form-label">基地:</label>
	            <div class="layui-input-inline" style="width:200px;">
	                <select name="baseId" id="baseId">
	                    #for(base : baseList)
	                    <option value="#(base.id)">#(base.baseName)</option>
	                    #end
	                </select>
	            </div>
            </div>
			<div id="yearMonthDiv" class="layui-inline">
				<label class="layui-form-label">月份:</label>
				<div class="layui-input-inline">
					<input id="yearMonth" name="yearMonth" class="layui-input" placeholder="">
				</div>
			</div>
			<div id="yearMonthDayDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">日期:</label>
				<div class="layui-input-inline">
					<input id="yearMonthDay" name="yearMonthDay" class="layui-input" placeholder="">
				</div>
			</div>

			<div class="layui-inline">
				<label class="layui-form-label">入住人类型</label>
				<div class="layui-input-inline" style="width:200px;">
					<select name="checkinType" id="checkinType">
						<option value="">全部</option>
						#for(checkinType : checkinTypes)
						<option value="#(checkinType.key)">#(checkinType.value)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-input-inline">
					<div class="layui-btn-group">
			            <button type="button" id="search" class="layui-btn">查询</button>
			            #shiroHasPermission("finance:monthCheckinDateil:exportBtn")
			            <button type="button" id="export" class="layui-btn">导出</button>
			            #end
					</div>
				</div>
			</div>
        </form>
    </div>
    <table id="expiredCardTable" lay-filter="expiredCardTable"></table>
</div>
#getDictLabel("checkin_status")
#end

#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
            ,value:'#(yearMonth)'
        });

        laydate.render({
            elem: '#yearMonthDay'
            ,trigger:'click'
            ,value:'#(yearMonthDay)'
        });

        form.on('select(queryType)', function(data){
        	var queryType = data.value;
        	if(queryType=='month'){
        		$("#yearMonthDiv").show();
        		$("#yearMonthDayDiv").hide();
        	}else{
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").show();
        	}
       	});

        locaTale=function(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            table.render({
                id : 'expiredCardTable'
                ,elem : '#expiredCardTable'
                ,method : 'POST'
                ,where:data
                ,url : '#(ctxPath)/fina/expenseRecord/baseMonthCheckinDetail'
                ,height: 'full-90'
                ,cellMinWidth: 80
                ,totalRow: true
                ,cols: [[
                    {type: 'numbers', width:60, title: '序号',unresize:true}
                    ,{field:'', title: '客户渠道', align: 'center', width:70, unresize: true,templet: function (d) {
                    	if(d.name!='合计'){
                    		if(d.checkin_status=='MeiTuan'){
                                return '<span class="layui-badge layui-bg-green">美团</span>';
                            }else if(d.checkin_status=='XieCheng'){
                                return  '<span class="layui-badge layui-bg-cyan">携程</span>';
                            }else{
                                return '<span class="layui-badge layui-bg-blue">会员</span>';
                            }
                        }else{
                            return '';
                        }
                    }}
					,{field:'checkinType', title: '入住人类型', align: 'center', width:140, unresize: true}
                    ,{field:'book_no', title: '预定号', align: 'center', width:140, unresize: true}
                    ,{field:'checkin_no', title: '入住号', align: 'center', width:140, unresize: true}
                    ,{field:'name', title: '入住人', align: 'center', width:100, unresize: true}
					,{field:'followCount', title: '随行数', align: 'center', width:55, unresize: true,templet:function (d) {
							if(d.name!='合计'){
								if(typeof(d.followCount)=='undefined'){
									return '0';
								}else{
									return d.followCount;
								}
							}else{
								return '';
							}

						}}
                    ,{field:'status', title: '入住状态', align: 'center', width:70, unresize: true,templet: function (d) {

                    		if(d.checkin_status=='book'){
                                return '<span class="layui-badge layui-bg-green">预定</span>';
                            }else if(d.checkin_status=='stayin'){
                                return  '<span class="layui-badge layui-bg-cyan">在住</span>';
                            }else if(d.checkin_status=='retreat'){

								var changeStr="";
								if(d.is_change_bed=='1'){
									changeStr=' <span class="layui-badge layui-bg-black">换</span>';
								}
                    			return  '<span class="layui-badge layui-bg-blue">退住</span> '+changeStr;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'bedName', title: '床位号', align: 'center', width:80, unresize: true}
                    ,{field:'is_private_room', title: '是否包房', width:80, align: 'center', unresize: true,templet:function (d) {
                            if(d.name!='合计'){
                                if(d.is_private_room=='1'){
                                    return '<span class="layui-badge layui-bg-green">是</span>';
                                }else{
                                    return  '<span class="layui-badge layui-bg-blue">否</span>';
                                }
                            }else{
                                return '';
                            }

                        }}
                    ,{field:'book', title: '预定/入住开始结束时间', width:180, align: 'center', unresize: true,templet:function (d) {
                            if(d.name!='合计'){
                                return dateFormat(d.start,"yyyy-MM-dd","-")+"至"+dateFormat(d.end,"yyyy-MM-dd","-");
                            }else{
                                return  '';
                            }
                        }}
                    /*,{field:'rel_checkout_time', title: '实际退住实际', align: 'center', width:130, unresize: true,templet:function (d) {
                        return dateFormat(d.real_checkout_time,"yyyy-MM-dd","-")
                    }}*/
                    ,{field:'checkinDays', title: '入住天数', align: 'center', width:100, unresize: true}
                    ,{field:'card_number', title: '会员卡', align: 'center', unresize: true}
                    ,{field:'cardType', title: '会员类型', align: 'center', unresize: true}
                    ,{field:'sumTimes', title: '扣卡天数', width:100, align: 'center', unresize: true}
                    ,{field:'sumAmount', title: '扣卡金额', width:100, align: 'center', unresize: true}
                    ,{field:'sumIntegrals', title: '扣卡积分', width:100, align: 'center', unresize: true}
					,{field:'settleDays', title: '结算天数', width:100, align: 'center', unresize: true}
                    ,{field:'settleStatus', title: '结算状态', width:100, align: 'center', unresize: true,templet:function (d) {
                            if(d.name!='合计') {
                                if (d.settleStatus == '1') {
                                    return '<span class="layui-badge layui-bg-green">已结算</span>';
                                    ;
                                } else {
                                    return '<span class="layui-badge">未结算</span>';
                                    ;
                                }
                            }else{
                                return '';
                            }
                        }}
                ]]
                ,page : false
                ,limit : 10
                ,limits : [10,20,30,40,50]
                ,done:function (){
					var layerTips;
					$("td").on("mouseenter", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						if (this.offsetWidth < this.firstChild.scrollWidth) {
							var that = this;
							var text = $(this).text();
							layerTips=layer.tips(text, that, {
								tips: 1,
								time: 0
							});
						}
					});
					$("td").on("mouseleave", function() {
						//js主要利用offsetWidth和scrollWidth判断是否溢出。
						//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
						layer.close(layerTips);
					});
					layer.close(loadingIndex);
                }
            });
        }
        //locaTale({"queryType":$("#queryType").val(),"baseId":$("#baseId").val(),"yearMonth":$("#yearMonth").val(),"yearMonthDay":$("#yearMonthDay").val()})

        // 搜索
        $("#search").click(function(){
            locaTale({"queryType":$("#queryType").val(),"baseId":$("#baseId").val(),"yearMonth":$("#yearMonth").val(),"yearMonthDay":$("#yearMonthDay").val(),"checkinType":$("#checkinType").val()})
        });
        
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#search').click();
				return false
			}
		}

        $("#export").click(function () {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '导出中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            var url='#(ctxPath)/fina/expenseRecord/baseMonthCheckinDetailExport?queryType='+$('#queryType').val()+"&checkinType="+$("#checkinType").val()
					+"&baseId="+$('#baseId').val()+"&yearMonth="+$('#yearMonth').val()+"&yearMonthDay="+$('#yearMonthDay').val();
            window.location.href=url;
            setTimeout(function(){
				layer.close(loadingIndex);
            },5000);
        });


    });
</script>
<script type="text/html" id="actionBar">

</script>
#end
