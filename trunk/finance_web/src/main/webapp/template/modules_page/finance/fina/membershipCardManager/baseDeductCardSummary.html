#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地扣卡汇总表#end

#define css()
#end

#define js()
<script type="text/javascript">
	var $ ;
    layui.use(['table','form','laydate'],function(){
    	$ = layui.$;
        var table = layui.table
        , form = layui.form
//         , $ = layui.$
        , laydate = layui.laydate
		;
        
        summaryDetail = function(baseId, deductMonth) {
        	var url = "#(ctxPath)/fina/cardmanager/baseDeductCardSummaryCardType?baseId="+baseId+'&deductMonth='+deductMonth;
			layerShow("明细",url,'1050','');
        }
        
		dailyStatisticsLoad = function () {
			layer.load();
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/cardmanager/baseDeductCardSummaryList',
                data: {},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	$('#baseDeductCardSummaryList').empty();
                    	var html = '';
                    	var totalDays = 0;
                    	var totalMoney = 0;
                    	$.each(rep.data, function(i,deduct){
							var baseId = deduct.baseId!=null && deduct.baseId!=''?deduct.baseId:'';
							var baseName = deduct.baseName!=null && deduct.baseName!=''?deduct.baseName:'';
							var deductMonth = deduct.deductMonth!=null && deduct.deductMonth!=''?deduct.deductMonth:'';
							var totalTimes = deduct.totalTimes!=null && deduct.totalTimes!=''?deduct.totalTimes:0;
							var countMoney = deduct.totalMoney;
							
							html+='<tr>';
							html+='<td>'+(i+1)+'</td>';
							html+='<td>'+baseName+'</td>';
							html+='<td>'+deductMonth+'</td>';
							html+='<td>'+totalTimes+'</td>';
							html+='<td>'+countMoney+'</td>';
							html+='<td><a class="layui-btn" onclick="summaryDetail(\''+baseId+'\',\''+deductMonth+'\');">明细</a></td>';
							html+='</tr>';
							totalDays+=totalTimes;
							totalMoney+=countMoney;
                    	});
                    	html+='<tr>';
						html+='<td>合计</td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td>'+totalDays+'</td>';
						html+='<td>'+totalMoney+'</td>';
						html+='<td></td>';
						html+='</tr>';
                    	$('#baseDeductCardSummaryList').append(html);
                    }
                },
                complete : function() {
                	layer.closeAll('loading');
                }
            });
		}
		
		dailyStatisticsLoad();
    });

</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<table class="layui-table">
			<thead>
				<tr>
					<th>序号</th>
					<th>基地名称</th>
					<th>扣卡月份</th>
					<th>扣卡天数</th>
					<th>参考金额</th>
					<th>操作</th>
				</tr> 
			</thead>
			<tbody id="baseDeductCardSummaryList">
			</tbody>
		</table>
	</div>
</div>
#end