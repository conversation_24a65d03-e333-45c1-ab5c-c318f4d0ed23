#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡设备展示#end

#define css()
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/fina/recharge/updateRemark";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#updateRemarkForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.findCardInfoById($("#cardId").val());
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="padding-top:20px;" id="updateRemarkForm">
    <input type="hidden" name="cardTran.id" value="#(cardTran.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="cardTran.describe" class="layui-textarea" rows="7" lay-verify="required" placeholder="请输入新备注">#(cardTran.describe??)</textarea>
        </div>
    </div>
    <input type="hidden" id="cardId" value="#(cardTran.cardId??)">
    <input type="hidden" name="cardTran.expenseId"value="#(cardTran.expenseId??)">
    <input type="hidden" name="oldDescribe" value="#(cardTran.describe??)">
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
