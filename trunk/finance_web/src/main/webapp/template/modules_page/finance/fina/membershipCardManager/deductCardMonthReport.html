#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地扣卡月报表#end

#define css()
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;
        
        laydate.render({
            elem:"#deductMonth",
            type:"month",
            range:false,
            format:"yyyy-MM"
        });
        
		dailyStatisticsLoad = function () {
			layer.load();
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/cardmanager/deductCardMonthReportList',
                data: {baseId:$('#baseId').val(), deductMonth:$('#deductMonth').val()},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	$('#deductCardMonthList').empty();
                    	var html = '';
                    	var totalNum = 0;
                    	$.each(rep.data, function(i,deduct){
//                             console.log(i+'==='+val);
							var deductType = '';
							var deductUnit = '';
							var deductNum = 0;
							var referenceMoney = 0;
							var cardNumber = deduct.cardNumber!=null && deduct.cardNumber!=''?deduct.cardNumber:'';
							var fullName = deduct.fullName!=null && deduct.fullName!=''?deduct.fullName:'';
							var cardTypeName = deduct.cardTypeName!=null && deduct.cardTypeName!=''?deduct.cardTypeName:'';
							var name = deduct.name!=null && deduct.name!=''?deduct.name:'';
							var baseName = deduct.baseName!=null && deduct.baseName!=''?deduct.baseName:'';
							var startTime = deduct.startTime!=null && deduct.startTime!=''?deduct.startTime:'';
							var endTime = deduct.endTime!=null && deduct.endTime!=''?deduct.endTime:'';
							var totalAmount = deduct.totalAmount!=null && deduct.totalAmount!=''?deduct.totalAmount:0;
							var totalTimes = deduct.totalTimes!=null && deduct.totalTimes!=''?deduct.totalTimes:0;
							var checkinNo = deduct.checkinNo!=null && deduct.checkinNo!=''?deduct.checkinNo:'';
							var userName = deduct.userName!=null && deduct.userName!=''?deduct.userName:'';
							var settleRemark = deduct.settleRemark!=null && deduct.settleRemark!=''?deduct.settleRemark:'';
							
							if(totalAmount>0){
								deductType = '储值扣费';
								deductUnit = '元';
								deductNum=totalAmount;
							}else if(totalTimes>0){
								deductType = '计次扣费';
								deductUnit = '天';
								deductNum=totalTimes;
							}
							html+='<tr>';
							html+='<td>'+(i+1)+'</td>';
							html+='<td>'+cardNumber+'</td>';
							html+='<td>'+fullName+'</td>';
							html+='<td>'+cardTypeName+'</td>';
							html+='<td>'+name+'</td>';
							html+='<td>'+baseName+'</td>';
							html+='<td>'+startTime+'</td>';
							html+='<td>'+endTime+'</td>';
							html+='<td>'+deductType+'</td>';
							html+='<td>'+deductUnit+'</td>';
							html+='<td>'+deductNum+'</td>';
							html+='<td>'+checkinNo+'</td>';
							html+='<td>'+userName+'</td>';
							html+='<td>'+settleRemark+'</td>';
							html+='</tr>';
							totalNum+=deductNum;
                    	});
                    	html+='<tr>';
						html+='<td>合计</td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td>'+totalNum+'</td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='</tr>';
                    	$('#deductCardMonthList').append(html);
                    }
                },
                complete : function() {
                	layer.closeAll('loading');
                }
            });
		}
		
		dailyStatisticsLoad();
		
        // 搜索消费记录按钮
        $('#searchRechargeBtn').on('click', function(){
        	dailyStatisticsLoad();
        });

    });

</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="searchForm" class="layui-form layui-form-pane" action="">
	    	<div class="layui-inline">
		        <label class="layui-form-label">基地</label>
		        <div class="layui-input-inline">
		            <select id="baseId" name="baseId" lay-search>
						<option value="">请选择基地</option>
						#for(base : baseList)
							<option value="#(base.id)">#(base.baseName)</option>
						#end
					</select>
		        </div>
    		</div>
	    	<div class="layui-inline">
		        <label class="layui-form-label">月份</label>
		        <div class="layui-input-inline">
		            <input type="text" id="deductMonth" name="deductMonth" class="layui-input" placeholder="请选择月份" autocomplete="off">
		        </div>
    		</div>
	        <div class="layui-inline">
	        	<div class="layui-input-inline">
	        		<div class="layui-btn-group">
				        <button type="button" id="searchRechargeBtn" class="layui-btn">搜索</button>
				        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
	        		</div>
	        	</div>
    		</div>
		</form>
	</div>
	<div class="layui-row">
		<table class="layui-table">
			<colgroup>
				<col width="4%">
				<col width="6%">
				<col width="6%">
				<col width="10%">
				<col width="6%">
				<col width="6%">
				<col width="8%">
				<col width="8%">
				<col width="6%">
				<col width="6%">
				<col width="6%">
				<col width="10%">
				<col width="6%">
				<col width="12%">
				<col>
			</colgroup>
			<thead>
				<tr>
					<th>序号</th>
					<th>卡号</th>
					<th>姓名</th>
					<th>类型</th>
					<th>消费姓名</th>
					<th>基地名称</th>
					<th>结算起始日期</th>
					<th>结算截止日期</th>
					<th>扣卡方式</th>
					<th>扣卡单位</th>
					<th>扣卡数量</th>
					<th>入住登记号</th>
					<th>扣卡操作人</th>
					<th>备注</th>
				</tr> 
			</thead>
			<tbody id="deductCardMonthList">
			</tbody>
		</table>
	</div>
</div>
#end