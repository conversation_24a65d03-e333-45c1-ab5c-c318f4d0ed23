#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()月结收入明细页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
		<div class="layui-inline">
			<label class="layui-form-label">会员卡类别</label>
			<div class="layui-input-inline">
				<select id="cardTypeId" name="cardTypeId" lay-search>
					<option value="">请选择卡类别</option>
					#for(t : typeList)
					<option value="#(t.id)" #(card != null ? (t.id == card.card_type_id?'selected':'') :'')>#(t.cardType)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">会员卡号</label>
			<div class="layui-input-inline" style="width:150px;">
				<input type="text" name="cardNumber" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<div class="layui-btn-group">
				<button type="button" id="searchBtn" class="layui-btn" lay-filter="searchBtn" lay-submit="">搜索</button>
			</div>
		</div>
	</form>
	</div>
	<div class="layui-row">
		<table id="cardMonthlyEndDetaiTable" lay-filter="cardMonthlyEndDetaiTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;
        
        layer.load();
        
        table.render({
            id : 'cardMonthlyEndDetaiTable',
            elem : "#cardMonthlyEndDetaiTable" ,
            url : '#(ctxPath)/fina/cardmanager/cardMonthlyEndDetailPage',
            where: {yearMonth:'#(yearMonth??)'},
            height:'full-90',
            cols : [[
                {field:'cardNumber',title:'会员卡号',width:120,unresize:false},
                {field:'fullName',title:'卡主姓名',width:120,unresize:false},
                {field:'cardTypeName',title:'卡类别',width:200,unresize:false},
				{field:'',title:'扣卡类型',width:100,unresize:false,templet: '#dictTpl("account_record_operate_type", "type")'},
                {field:'',title:'开始时间',width:120,unresize:false,templet:"<div>{{dateFormat(d.startDate,'yyyy-MM-dd')}}</div>"},
                {field:'',title:'结束时间',width:120,unresize:false,templet:"<div>{{dateFormat(d.endDate,'yyyy-MM-dd')}}</div>"},
				{field:'',title:'金额(元)',width:100,unresize:false},
                {field:'',title:'天数(天)',width:100,unresize:false},
                {field:'',title:'点数',width:100,unresize:false,templet: function (d) {
	                    if(typeof(d.points)==='undefined'){
	                        return '0.0';
	                    }else{
	                        return d.points;
	                    }
	                }
				},
                {field:'',title:'积分',width:100,unresize:false,templet: function (d) {
	                    if(typeof(d.integrals)==='undefined'){
	                        return '0.0';
	                    }else{
	                        return d.integrals;
	                    }
	                }
				},
                {field:'',title:'豆豆券',width:100,unresize:false,templet: function (d) {
	                    if(typeof(d.beanCoupons)==='undefined'){
	                        return '0.0';
	                    }else{
	                        return d.beanCoupons;
	                    }
	                }
				},
				{field:'countPrice',title:'计算单价',width:100,unresize:false},
				{field:'countDays',title:'计算天数(天)',width:120,unresize:false},
				{field:'countIntegrals',title:'计算积分',width:120,unresize:false},
				{field:'countAmount',title:'收入金额(元)',width:100,unresize:false},
                {field:'',title:'扣卡时间',width:120,unresize:false,templet:"<div>{{dateFormat(d.dealTime,'yyyy-MM-dd')}}</div>"},
                {field:'describe',title:'说明',width:300,fixed: 'right',unresize:true}
            ]] ,
            page : true,
            limit : 10,
			done:function () {
				layer.closeAll('loading');
			}
        });
        
      	//重载表格
        pageTableReload = function () {
        	var param = {yearMonth:'#(yearMonth??)',cardTypeId:$('#cardTypeId').val(),cardNumber:$('#cardNumber').val()};
        	tableReload('cardMonthlyEndDetaiTable',param);
        }
		
        // 搜索消费记录按钮
        $('#searchBtn').on('click', function(){
        	layer.load();
        	pageTableReload();
        });

    });

</script>
#end