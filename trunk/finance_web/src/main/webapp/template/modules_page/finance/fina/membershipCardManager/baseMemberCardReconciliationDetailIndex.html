#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()月入住单明细#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 3px;
    }
    /*     .laytable-cell-checkbox .layui-disabled.layui-form-checked i { */
    /*         background: #fff !important; */
    /*     } */
</style>
#end

#define content()
<div class="my-btn-box">
    <table id="expiredCardTable" lay-filter="expiredCardTable"></table>
</div>
#getDictLabel("checkin_status")
#end

#define js()
<script type="text/html" id="toolBar">
    #[[
    {{#if(d.status=='0'){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">审核</a>
    {{#}}}
    ]]#
    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;


        table.render({
            id : 'expiredCardTable'
            ,elem : '#expiredCardTable'
            ,method : 'POST'
            ,where:{'id':'#(id??)'}
            ,url : '#(ctxPath)/fina/cardmanager/baseMemberCardReconciliationDetailPageList'
            ,height: 'full-90'
            ,cellMinWidth: 80
            ,totalRow: true
            ,cols: [[
                {field:'base_name', title: '扣费基地',width:180, align: 'center', unresize: true}
                ,{field:'full_name', title: '入住人',width:180, align: 'center', unresize: true,templet:function (d) {
                        if("1"==d.type){
                            return d.full_name;
                        }else if("3"==d.type){
                            return d.full_name+"："+d.bill_name
                        }else{
                            return ''
                        }
                    }}
                ,{field:'type', title: '类型', align: 'center', unresize: true ,templet:function (d) {
                        if("1"==d.type){
                            return '旅居';
                        }else if("3"==d.type){
                            return '机构'
                        }else{
                            return ''
                        }
                    }}
                ,{field:'start_time', title: '费用开始时间', width:120, align: 'center', unresize: true,templet:"<div>{{dateFormat(d.start_date,'yyyy-MM-dd')}}</div>" }
                ,{field:'end_time', title: '费用结束时间', width:120, align: 'center', unresize: true,templet:"<div>{{dateFormat(d.end_date,'yyyy-MM-dd')}}</div>" }
                ,{field:'times', title: '扣卡天数', align: 'center', unresize: true }
                ,{field:'amount', title: '扣卡金额', align: 'center', unresize: true }
                ,{field:'integrals', title: '扣卡积分', align: 'center', unresize: true }
                ,{field:'actual_times', title: '对账扣卡天数', width:120, align: 'center', unresize: true }
                ,{field:'actual_amount', title: '对账扣卡金额', width:120, align: 'center', unresize: true }
                ,{field:'actual_integrals', title: '对账扣卡积分', width:120, align: 'center', unresize: true }
                ,{field:'status', title: '对账状态', align: 'center', unresize: true ,templet:function (d) {
                        if(d.status=='0'){
                            return '<span style="color: red">异常</span>'
                        }else{
                            return '<span style="">正常</span>'
                        }
                    }}
                ,{field:'remark', title: '对账备注', align: 'center', unresize: true }
            ]]
            ,page : false
            ,limit : 10
            ,limits : [10,20,30,40,50]
            ,done:function (){
                var layerTips;
                $("td").on("mouseenter", function() {
                    //js主要利用offsetWidth和scrollWidth判断是否溢出。
                    //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                    if (this.offsetWidth < this.firstChild.scrollWidth) {
                        var that = this;
                        var text = $(this).text();
                        layerTips=layer.tips(text, that, {
                            tips: 1,
                            time: 0
                        });
                    }
                });
                $("td").on("mouseleave", function() {
                    //js主要利用offsetWidth和scrollWidth判断是否溢出。
                    //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                    layer.close(layerTips);
                });

                layer.close(loadingIndex);
            }
        });


    });
</script>
<script type="text/html" id="actionBar">

</script>
#end
