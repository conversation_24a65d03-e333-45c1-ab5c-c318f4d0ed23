#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
/* 	.layui-inline{ */
/* 		margin-top: 30px; */
/* 	} */
/* 	label.layui-form-label{ */
/* 		width:auto; */
/* 	} */

/* 	.layui-btn+.layui-btn{ */
/* 		margin-left: 5px; */
/* 	} */
 	.layui-table-cell{
 		padding: 0 3px;
 	}
</style>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
			<div class="layui-inline">
				<label class="layui-form-label">会员卡号</label>
				<div class="layui-input-inline">
					<input type="text" name="cardNumber" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">姓名</label>
				<div class="layui-input-inline">
					<input type="text" name="fullName" id="fullName" placeholder="请输入姓名" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">证件号码</label>
				<div class="layui-input-inline">
					<input type="text" name="idcard" id="idcard" placeholder="请输入证件号码" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">会员卡类别</label>
				<div class="layui-input-inline">
					<select id="cardTypeId" name="cardTypeId" lay-search>
						<option value="">请选择卡类别</option>
						#for(t : typeList)
						<option value="#(t.id)">#(t.cardType)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">会员卡状态</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="delFlag" name="delFlag">
						<option value="">全部</option>
						<option value="0" selected="selected">未作废</option>
						<option value="1">已作废</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">是否锁定</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="isLock" name="isLock">
						<option value="">全部</option>
						<option value="0" selected="selected">未锁定</option>
						<option value="1">已锁定</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">实际售价</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="referencePrice" name="referencePrice">
						<option value="">全部</option>
						<option value="0">为0</option>
						<option value="1">不为0</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">是否制卡</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="isMakeCard" name="isMakeCard">
						<option value="">全部</option>
						<option value="0">未制卡</option>
						<option value="1">已制卡</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">是否过期</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="expireFlag" name="expireFlag">
						<option value="">全部</option>
						<option value="0">未过期</option>
						<option value="1">已过期</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">数据来源</label>
				<div class="layui-input-inline" style="width:100px;">
					<select id="dataSource" name="dataSource">
						<option value="">全部</option>
						#statusOption(com.cszn.integrated.service.entity.status.CardDataSource::me(), "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">所属区域</label>
				<div class="layui-input-block" style="width:300px;">
					<input type="hidden" id="province" name="province" value=""/>
					<input type="hidden" id="city" name="city" value=""/>
					<input type="hidden" id="town" name="town" value="">
					<input type="hidden" id="street" name="street" value="">
					<input type="hidden" id="regidentProvinceName" value=""/>
					<input type="hidden" id="regidentCityName" value=""/>
					<input type="hidden" id="regidentCountyName" value="">
					<input type="hidden" id="regidentStreetName" value="">
					<input type="text" id="regidentAddress" name="regidentAddrs" class="layui-input" value="" style="width:300px;display:inline-block;" readonly="readonly">
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-input-inline">
					<button class="layui-btn layui-btn-sm" style="display: inline-block;" type="button" id="areaClear">清空</button>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">分公司</label>
				<div class="layui-input-inline">
					<select id="branchOfficeId" name="branchOfficeId" lay-search>
						<option value="">请选择分公司</option>
						#for(b : branchOfficeList)
						<option value="#(b.id)">#(b.shortName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">剩余金额≤</label>
				<div class="layui-input-inline" style="width:100px;">
					<input type="text" name="balance" id="balance" placeholder="剩余金额" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">剩余天数≤</label>
				<div class="layui-input-inline" style="width:100px;">
					<input type="text" name="consumeTimes" id="consumeTimes" placeholder="剩余天数" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">剩余积分≤</label>
				<div class="layui-input-inline" style="width:100px;">
					<input type="text" name="cardIntegrals" id="cardIntegrals" placeholder="剩余积分" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">无交易日期</label>
				<div class="layui-inline">
					<div class="layui-input-inline" style="width:100px;">
						<input type="text" id="noDealStartDate" name="noDealStartDate" class="layui-input" placeholder="开始日期" autocomplete="off">
					</div>
					-
					<div class="layui-input-inline" style="width:100px;">
						<input type="text" id="noDealEndDate" name="noDealEndDate" class="layui-input" placeholder="结束日期" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">快过期年份</label>
				<div class="layui-inline">
					<div class="layui-input-inline" style="width:60px;">
						<input type="text" id="nearExpireStartYear" name="nearExpireStartYear" class="layui-input" placeholder="开始年" autocomplete="off">
					</div>
					-
					<div class="layui-input-inline" style="width:60px;">
						<input type="text" id="nearExpireEndYear" name="nearExpireEndYear" class="layui-input" placeholder="结束年" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">排序方式</label>
				<div class="layui-input-inline" style="width:150px;">
					<select id="orderBy" name="orderBy">
						<option value="">请选择排序方式</option>
						<option value="cardNumberAsc">会员卡号升序</option>
						<option value="cardNumberDesc">会员卡号降序</option>
						<option value="openTimeAsc">制卡日期升序</option>
						<option value="openTimeDesc">制卡日期降序</option>
						<option value="createTimeAsc">创建时间升序</option>
						<option value="createTimeDesc">创建时间降序</option>
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">合同号</label>
				<div class="layui-input-inline">
					<input type="text" name="contractNumber" id="contractNumber" placeholder="请输入合同号" autocomplete="off" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-btn-group">
					<button type="button" id="search" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
<!--					#shiroHasPermission("finance:card:exprotBtn")-->
<!--					<button type="button" id="export" class="layui-btn">导出模板</button>-->
<!--					#end-->
<!--					#shiroHasPermission("finance:card:impotBtn")-->
<!--					<button type="button" id="import" class="layui-btn">导入会员卡</button>-->
<!--					#end-->
					#shiroHasPermission("finance:card:exportDataBtn")
					<button type="button" id="exportData" class="layui-btn">导出会员卡</button>
					#end
					#shiroHasPermission("finance:card:cardImpotBtn")
					<button type="button" id="cardImport" class="layui-btn">员工福利卡导入</button>
					#end
					#shiroHasPermission("finance:card:shiJiCardImportBtn")
					<button type="button" id="shiJiCardImport" class="layui-btn">世纪昌松卡导入</button>
					#end

				</div>
			</div>
		</form>
	</div>
	<div class="layui-row" style="margin-top: 10px;">
		<div class="layui-btn-group">
			#shiroHasPermission("finance:card:addBtn")
			<button type="button" id="add" class="layui-btn">添加</button>
			#end
			#shiroHasPermission("finance:card:editBtn")
			<button type="button" id="edit" class="layui-btn">编辑</button>
			#end
			<button type="button" id="check" class="layui-btn">查看</button>
			#shiroHasPermission("finance:card:checkBeanCouponsBtn")
			<button type="button" id="checkBeanCoupons" class="layui-btn">查看豆豆券</button>
			#end
			#shiroHasPermission("finance:card:lockBtn")
			<button type="button" id="lock" class="layui-btn">锁定</button>
			#end
			#shiroHasPermission("finance:card:unlockBtn")
			<button type="button" id="unLock" class="layui-btn">解锁</button>
			#end
			<button type="button" id="cardPwdSet" class="layui-btn">卡密码设置</button>
			<button type="button" id="markMakeCard" class="layui-btn">标识制卡</button>
			<button type="button" id="markRefundCard" class="layui-btn">标识退卡</button>
			#shiroHasPermission("finance:card:transferCard")
			<button type="button" id="transferCard" class="layui-btn">转移</button>
			#end
			#shiroHasPermission("finance:card:changeBtn")
			<button type="button" id="changeOwn" class="layui-btn">更换卡主</button>
			#end
			#shiroHasPermission("finance:card:batchRechargeBtn")
			<button type="button" id="batchRecharge" class="layui-btn">批量充值</button>
			#end
			<button type="button" id="giveRecord" class="layui-btn">赠送记录</button>
			#shiroHasPermission("finance:card:operateRecordBtn")
			<button type="button" id="operateRecord" class="layui-btn">操作记录</button>
			#end
			#shiroHasPermission("finance:card:delBtn")
			<button type="button" id="del" class="layui-btn layui-btn-danger">作废</button>
			#end
			#shiroHasPermission("finance:card:batchDelBtn")
			<button type="button" id="batchVoid" class="layui-btn layui-btn-danger">批量作废</button>
			#end
			#shiroHasPermission("finance:card:deleteBtn")
			<button type="button" id="delete" class="layui-btn layui-btn-danger">删除</button>
			#end
			#shiroHasPermission("finance:card:recoveryCardManager")
			<button type="button" id="recovery" class="layui-btn">作废恢复</button>
			#end
			#shiroHasPermission("finance:card:expiredCardManager")
 			<button type="button" id="expired" class="layui-btn">过期恢复</button>
			#end
		</div>
	</div>
	<div class="layui-row">
		<table class="layui-table" id="cardTable" lay-filter="cardTableFilter"></table>
	</div>
</div>
#getDictLabel("gender")
#end

#define js()
<script id="regidentAreaTpl" type="text/html">
	<div id="regidentArea"
		 style="width:700px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
		<div class="tabs clearfix">
			<ul>
				<li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()"
					   class="current">省份</a></li>
				<li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()">城市</a></li>
				<li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
				<li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()">街道/镇/乡</a></li>
			</ul>
		</div>
		<div class="con" style="height:300px;overflow-y: auto;">
			<div class="regidentProvinceAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentCityAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentTownAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentStreetAll">
				<div class="list">

				</div>
			</div>
		</div>
	</div>
</script>
<script type="text/html" id="isReportTpl">
  <input type="checkbox" name="isReport" value="{{d.id}}" lay-skin="switch" lay-text="是|否" lay-filter="isReportFilter" {{ d.isReport=='1' ? 'checked' : '' }}>
</script>
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
var table,form,$ ;
layui.use(['table','form','laydate','upload'],function(){
	table = layui.table
	, form = layui.form
	, laydate = layui.laydate
	, $ = layui.$
	, rotateNum = 0
	;
	var upload = layui.upload;
	
	form.render('select');

	laydate.render({
		elem:"#noDealStartDate",
		type:"date",
		range:false,
		format:"yyyy-MM-dd"
	});

	laydate.render({
		elem:"#noDealEndDate",
		type:"date",
		range:false,
		format:"yyyy-MM-dd"
	});

	laydate.render({
		elem:"#nearExpireStartYear",
		type:"year",
		range:false,
		format:"yyyy"
	});

	laydate.render({
		elem:"#nearExpireEndYear",
		type:"year",
		range:false,
		format:"yyyy"
	});

	//清空区域
	$("#areaClear").on("click", function () {
		$("#province").val("");
		$("#city").val("");
		$("#town").val("");
		$("#street").val("");
		$("#regidentProvinceName").val("");
		$("#regidentCityName").val("");
		$("#regidentCountyName").val("");
		$("#regidentStreetName").val("");
		$("#regidentAddress").val("")
	});

	//--------------------------居住区域begin---------------------------
	$('#regidentAddress').on('click', function () {
		//closeIdArea();

		$('#regidentArea').remove();
		var $this = $(this);
		var getTpl = regidentAreaTpl.innerHTML;
		$this.parent().append(getTpl);
		//event.stopPropagation();

		var street = $("#street").val();
		var regidentStreetName = $("#regidentStreetName").val();
		var town = $("#town").val();
		var regidentCountyName = $("#regidentCountyName").val();
		var city = $("#city").val();
		var regidentCityName = $("#regidentCityName").val();
		var province = $("#province").val();
		var regidentProvinceName = $("#regidentProvinceName").val();
		if (street != '' && regidentStreetName != '') {
			$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentStreetLoad(town);
			regidentCountyLoad(city);
			regidentCityLoad(province);
			regidentProvinceLoad();
			$(".con .regidentStreetAll").show().siblings().hide();
			//clickStreet(streetId,streetName);
		} else if (town != '' && regidentCountyName != '') {

			if (town != '') {
				regidentCityLoad(province);
				regidentCountyLoad(city);
				regidentProvinceLoad();
				util.sendAjax({
					type: 'POST',
					url: '#(ctxPath)/area/getAreas',
					data: {pid: town},
					notice: false,
					loadFlag: false,
					success: function (res) {
						if (res.state == 'ok') {
							if (res.data.length > 0) {
								$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								var html = "<ul>";
								$.each(res.data, function (i, item) {
									html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
								});
								html += "</ul>";
								$(".regidentStreetAll .list").append(html);
								//viewStreet(countyId,countyName);
								$(".con .regidentStreetAll").show().siblings().hide();
							} else {
								//无 街道信息
								$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								$(".con .regidentTownAll").show().siblings().hide();
							}
						}
					},
					complete: function () {
					}
				});
			}
		} else if (city != '' && regidentCityName != '') {
			regidentProvinceLoad();
			regidentCityLoad(province);
			viewRegidentCounty(city, regidentCityName);
		} else if (province != '' && regidentProvinceName != '') {
			regidentProvinceLoad();
			viewRegidentCity(province, regidentProvinceName);
		} else {
			regidentProvinceLoad();
		}

		//去除事件冒泡
		var evt = new Object;
		if (typeof (window.event) == "undefined") {//如果是火狐浏览器
			evt = arguments.callee.caller.arguments[0];
		} else {
			evt = event || window.event;
		}
		evt.cancelBubble = true;
		$('#regidentArea').off('click');
		$('#regidentArea').on('click', function () {
			//event.stopPropagation();
			//去除事件冒泡
			var evt = new Object;
			if (typeof (window.event) == "undefined") {//如果是火狐浏览器
				evt = arguments.callee.caller.arguments[0];
			} else {
				evt = event || window.event;
			}
			evt.cancelBubble = true;
		})
	});

	regidentProvinceLoad = function () {
		util.sendAjax({
			type: 'POST',
			url: '#(ctxPath)/area/getAreas',
			data: {pid: ''},
			notice: false,
			loadFlag: false,
			success: function (res) {
				if (res.state == 'ok') {
					if (res.data.length > 0) {
						$(".regidentProvinceAll .list").empty();
						var html = "<ul>";
						$.each(res.data, function (i, item) {
							html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCity(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
						});
						html += "</ul>";
						$(".regidentProvinceAll .list").append(html);
					}
				}
			},
			complete: function () {
			}
		});
	};

	//点击省事件
	viewRegidentCity = function (province, regidentProvinceName) {
		$("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentCityAll").show().siblings().hide();
		$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		//点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
		$("#province").val(province);
		$("#regidentProvinceName").val(regidentProvinceName);
		$("#city").val("");
		$("#regidentCityName").val("");
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCityLoad(province);
	};

	//加载市
	regidentCityLoad = function (province) {
		if (province != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: province},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentCityAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function (i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCounty(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentCityAll .list").append(html);
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	//点击市事件
	viewRegidentCounty = function (city, RegidentCityName) {
		$("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentTownAll").show().siblings().hide();
		$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#city").val(city);
		$("#regidentCityName").val(RegidentCityName);
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCountyLoad(city);
	};

	//加载区/县
	regidentCountyLoad = function (city) {
		if (city != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: city},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentTownAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function (i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentTownAll .list").append(html);
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	//点击区/县事件
	viewRegidentStreet = function (town, regidentCountyName) {
		$("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentStreetAll").show().siblings().hide();
		$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#town").val(town);
		$("#regidentCountyName").val(regidentCountyName);
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentStreetLoad(town);
	};

	//加载街道/镇/乡
	regidentStreetLoad = function (town) {
		if (town != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: town},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentStreetAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function (i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentStreetAll .list").append(html);
						} else {
							//无 街道信息
							clickRegidentStreet('', '');
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	clickRegidentStreet = function (street, regidentStreetName) {
		$("#street").val(street);
		$("#regidentStreetName").val(regidentStreetName);
		var regidentProvinceName = $("#regidentProvinceName").val();
		var regidentCityName = $("#regidentCityName").val();
		var regidentCountyName = $("#regidentCountyName").val();
		var regidentStreetName = $("#regidentStreetName").val();
		var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
		$("#regidentAddress").val(add);
		$('#regidentArea').remove();
	};

	regidentProvinceAllClick = function () {
		//$(".con .provinceAll").show();
		$("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentProvinceAll").show().siblings().hide();
	};

	regidentCityAllClick = function () {
		// $(".con .cityAll").show();
		if ($("#province").val() != '') {
			$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCityLoad($("#province").val());
			$(".con .regidentCityAll").show().siblings().hide();
		}
	};

	regidentTownAllClick = function () {
		if ($("#city").val() != '') {
			$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCountyLoad($("#city").val());
			$(".con .regidentTownAll").show().siblings().hide();
		}
	};

	regidentStreetAllClick = function () {
		if ($("#town").val() != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: $("#town").val()},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							regidentStreetLoad($("#town").val());
							$(".con .regidentStreetAll").show().siblings().hide();
						} else {
							//无 街道信息 显示区/县信息
							$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							//countyLoad(cityId);
							$(".con .regidentTownAll").show().siblings().hide();
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	$('body').on('click', function () {
		closeRegidentArea();
	});

	//关闭区域选择器
	closeRegidentArea = function () {
		if (typeof ($('#regidentArea').html()) != 'undefined') {
			var regidentProvinceName = $("#regidentProvinceName").val();
			var regidentCityName = $("#regidentCityName").val();
			var regidentCountyName = $("#regidentCountyName").val();
			var regidentStreetName = $("#regidentStreetName").val();
			var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
			$("#regidentAddress").val(add);
		}
		//alert(1);
		$('#regidentArea').remove();
	}
	//-------------------------居住区域end----------------------------

	layer.load(2, { //icon支持传入0-2
		shade: [0.5, 'gray'], //0.5透明度的灰色背景
		content: '加载中...',
		success: function (layero) {
			layero.find('.layui-layer-content').css({
				'padding-top': '39px',
				'width': '60px'
			});
		}
	});
	table.render({
		id:'cardTable'
		,elem: '#cardTable'
		,url : '#(ctxPath)/fina/cardmanager/findListPage'
		,method : 'POST'
		,where : {delFlag:'0',isLock:'0'}
		,height: 'full-260'    //容器高度
		,cols: [[
			{type:'checkbox'}
			/*{type: 'numbers', width:70, title: '序号',unresize:true}*/
			,{field: '', title: '作废状态',unresize:false,width:90,templet:function (d) {
					if(d.delFlag==='1'){
						var voidByName='';
						if(d.voidByName==undefined){
							voidByName='';
						}else{
							voidByName=d.voidByName
						}
						var voidDate='';
						if(d.voidDate==undefined){
							voidDate='';
						}else{
							voidDate=d.voidDate;
						}
						var voidRemark='';
						if(d.voidRemark==undefined){
							voidRemark='';
						}else{
							voidRemark=d.voidRemark
						}
						var voidUserName='';
						if(d.voidUserName==undefined){
							voidUserName='';
						}else{
							voidUserName=d.voidUserName
						}
						return '<div void-by-name="'+voidByName+'" void-user-name="'+voidUserName+'" void-date="'+dateFormat(voidDate,'yyyy-MM-dd HH:mm:ss')+'" void-remark="'+voidRemark+'" ><span class="layui-badge">已作废</span></div>';
					}else{
						return '<span class="layui-badge layui-bg-green">未作废</span>';
					}
				}}
			,{field: 'cardLock',align: 'center', title: '锁定状态',unresize:false,width: 90,templet:function (d) {
					if(d.isLock==='1'){
						var lockByName='';
						if(d.lockByName==undefined){
							lockByName='';
						}else{
							lockByName=d.lockByName
						}
						var lockDate='';
						if(d.lockDate==undefined){
							lockDate='';
						}else{
							lockDate=d.lockDate;
						}
						var lockRemark='';
						if(d.lockRemark==undefined){
							lockRemark='';
						}else{
							lockRemark=d.lockRemark
						}
						var lockUserName='';
						if(d.lockUserName==undefined){
							lockUserName='';
						}else{
							lockUserName=d.lockUserName
						}
						return '<span class="layui-badge" lock-by-name="'+lockByName+'" lock-user-name="'+lockUserName+'" lock-date="'+dateFormat(lockDate,'yyyy-MM-dd HH:mm:ss')+'" lock-remark="'+lockRemark+'" >已锁定</span>';
					}else{
						return '<span class="layui-badge layui-bg-green">未锁定</span>';
					}
				}}
			,{field: 'cardNumber',align: 'center', title: '会员卡号',unresize:true,width: 120}
			,{field: 'cardType',align: 'center', title: '类别',unresize:false,width: 180}
			,{field: 'fullName',align: 'center', title: '姓名',unresize:false,width:95}
// 				,{field: 'gender',align: 'center', title: '性别',width: 70,unresize:false,templet: "<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
			,{field:'', title: '剩余金额', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.balance != null){
						str = String(d.balance) +"元";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'', title: '剩余天数', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.consumeTimes != null){
						str = String(d.consumeTimes) +"天";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'', title: '剩余点数', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.consumePoints != null){
						str = String(d.consumePoints);
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'cardIntegrals', title: '剩余积分', width:90,align: 'center',unresize: false}
			,{field:'beanCoupons', title: '剩余豆豆券', width:100,align: 'center',unresize: false}
			,{field:'', title: '合同金额', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.purchaseCardBalance != null){
						str = String(d.purchaseCardBalance) +"元";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'', title: '合同天数', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.contractTimes != null){
						str = String(d.contractTimes) +"天";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'', title: '实收金额', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.collectAmount != null){
						str = String(d.collectAmount) +"元";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'', title: '帐面余额', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.accountBalance != null){
						str = String(d.accountBalance) +"元";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field: 'describe', title: '备注',unresize:false,width:200,style:"text-align:left;font-size:10px;color:#000;"}
			,{field: '',align: 'center', title: '数据来源',unresize:true,width: 120, templet:'#statusTpl(com.cszn.integrated.service.entity.status.CardDataSource::me(), "dataSource")'}
			,{field:'', title: '单价', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.price != null){
						str = String(d.price) +"元";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'', title: '赠送天数', width:90,align: 'center',unresize: false,templet:function (d) {
					var str = "";
					if(d.giveConsumeTimes != null){
						str = String(d.giveConsumeTimes) +"天";
					}else{
						str = "- -";
					}
					return str;
				}}
			,{field:'giveBalance', title: '赠送金额', width:90,align: 'center',unresize: false}
			,{field:'giveBeanCoupons', title: '赠送豆豆券', width:100,align: 'center',unresize: false}
			,{field:'', title: '退卡状态', width: 100, align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.ReturnCardStatus::me(), "returnCardStatus")'}
			,{field: 'isMakeCard', title: '制卡状态',unresize:false,width:90,templet:"<div>{{d.isMakeCard == '0'? '<span class='layui-badge'>未制卡</span>':d.isMakeCard == '1'? '<span class='layui-badge layui-bg-green'>已制卡</span>':'- -'}}</div>"}
			,{field: 'openTime', title: '开卡时间',align: 'center',unresize:true,width:110,templet: "<div>{{ dateFormat(d.openTime,'yyyy-MM-dd') }}</div>"}
			,{field: '', title: '过期状态',unresize:false,width:90,templet:"<div>{{d.expireFlag == '0'? '<span class='layui-badge layui-bg-green'>未过期</span>':d.expireFlag == '1'? '<span class='layui-badge layui-bg-red'>已过期</span>':'- -'}}</div>"}
			,{field: '', title: '是否报备',unresize:false,width:90,templet:'#isReportTpl'}
			,{field: '', title: '过期时间',align: 'center',unresize:true,width:110,templet: "<div>{{ dateFormat(d.expireDate,'yyyy-MM-dd') }}</div>"}
			,{field: 'createByName', title: '创建人',align: 'center',unresize:true,width:100}
			,{field: 'createTime', title: '创建时间',align: 'center',unresize:true,width:160,templet: "<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
		]]
		,page : true
		,limit : 10
// 			,limits: [10,20,30]
		,done: function(res, curr, count){
			$('#cardTable').next().find('.layui-table-body').find("table" ).find("tbody").children("tr").on('dblclick',function(){
				var index = JSON.stringify($('#cardTable').next().find('.layui-table-body').find("table").find("tbody").find(".layui-table-hover").data('index'));
				var obj = res.data[index];
				var url = "#(ctxPath)/fina/cardmanager/form?id=" + obj.id+"&type=check";
				layerShow("查看会员卡",url,1200,750);
			});
			var layerTips;
			$("td").on("mouseenter", function() {
				//js主要利用offsetWidth和scrollWidth判断是否溢出。
				//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
				if (this.offsetWidth < this.firstChild.scrollWidth) {
					var that = this;
					var text = $(this).text();
					layerTips=layer.tips(text, that, {
						tips: 1,
						time: 0
					});
				}
			});
			$("td").on("mouseleave", function() {
				//js主要利用offsetWidth和scrollWidth判断是否溢出。
				//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
				layer.close(layerTips);
			});
			layer.closeAll('loading');
			tdTitle();
		}
	});
	//表格重载
	layuiTableReload = function(queryField, currentPage){
		table.reload('cardTable', {
			//分页重新从第 几 页开始
			page:{
				curr:currentPage
			}
			//设定异步数据接口的额外参数
			,where:queryField
		});
	};
	//批量获取列表选中数据
	getCheckTableData = function(){
		var cardCheckStatus = table.checkStatus('cardTable');
		// 获取选择状态下的数据
		return cardCheckStatus.data;
	}
	//监听是否发布操作
	form.on('switch(isReportFilter)', function(obj){
		var id = this.value;
		var isReport = '';
		if(obj.elem.checked){
			isReport = '1';
		}else{
			isReport = '0';
		}
		var data = {id:id, isReport:isReport};
		util.sendAjax({
            url:"#(ctxPath)/fina/cardmanager/update",
            type:'post',
            data:data,
            notice:true,
            success:function(returnData){
                if(returnData.state==='ok'){
					/*$('#search').click();*/
                }
            }
        });
	});
	// 添加
	$("#add").click(function(){
		$(this).blur();
		var url = "#(ctxPath)/fina/cardmanager/form" ;
		layerShow("新增会员卡",url,1200,720);
	});
	// 编辑
	$("#edit").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
	            var url = "#(ctxPath)/fina/cardmanager/form?id=" + tableData[0].id;
				layerShow("编辑",url,1200,720);
    		}else{
    			layer.msg('该会员卡已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 查看
	$("#check").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
			var url = "#(ctxPath)/fina/cardmanager/form?id=" + tableData[0].id + "&type=check";
			layerShow("查看",url,900,700);
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 查看豆豆券
	$("#checkBeanCoupons").click(function(){
		var tableData = getCheckTableData();
		if(tableData.length==1){
			var url = "#(ctxPath)/fina/cardmanager/beanCouponsDetailIndex?id=" + tableData[0].id;
			layerShow("查看豆豆券",url,900,700);
		}else if(tableData.length==0){
			layer.msg('请勾选数据', function () {});
		}else{
			layer.msg('只能勾选一条数据', function () {});
		}
	});


	// 锁定
	$("#lock").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
				if(tableData[0].isLock=='0'){//未锁定状态才能锁定
					layer.open({
						type: 1,
						title:"锁定会员卡",
						area:['500px','300px'],
						btn: ['确定', '取消'],
						content: '<form class="layui-form" action="" style="margin-top: 10px;">' +
								'<div class="layui-form-item layui-form-text">' +
								'    <label class="layui-form-label">备注</label>' +
								'    <div class="layui-input-block">' +
								'      <textarea id="lockRemark" name="lockRemark" class="layui-textarea" lay-verify="required" placeholder="请输入内容"></textarea>' +
								'    </div>' +
								'  </div>',
						yes:function(index,layero){
							var lockRemark = $("#lockRemark").val();
							if(lockRemark!=null && lockRemark!=''){
								util.sendAjax({
									url:"#(ctxPath)/fina/cardmanager/lockCard",
									type:'post',
									data: {id:tableData[0].id,lockRemark:lockRemark},
									notice:true,
									success:function(returnData){
										if(returnData.state==='ok'){
											$('#search').click();
										}
										layer.close(index);
									}
								});
							}else{
								layer.msg('备注不能为空，请输入备注!',{icon:5});
							}
						}
					});
				}else{
					layer.msg('该会员卡已锁定状态，不能锁定!',{icon:5});
				}
    		}else{
    			layer.msg('该会员卡已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 解锁
	$("#unLock").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
				if(tableData[0].isLock=='1'){//锁定状态
					layer.confirm("确定要解锁该会员卡吗?",function(index){
						layer.load(2, { //icon支持传入0-2
							shade: [0.5, 'gray'], //0.5透明度的灰色背景
							content: '加载中...',
							success: function (layero) {
								layero.find('.layui-layer-content').css({
									'padding-top': '39px',
									'width': '60px'
								});
							}
						});
						util.sendAjax({
							url:"#(ctxPath)/fina/cardmanager/unlockCard",
							type:'post',
							data: {id:tableData[0].id},
							notice:true,
							success:function(returnData){
								if(returnData.state==='ok'){
									$('#search').click();
								}
								layer.close(index);
							}
						});
					});
				}else{
					layer.msg('该会员卡未锁定状态，不能解锁!',{icon:5});
				}
    		}else{
    			layer.msg('该会员卡已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 卡密码设置
	$("#cardPwdSet").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
				var url = "#(ctxPath)/fina/cardmanager/passwordSet?id=" + tableData[0].id;
				layerShow("卡密码设置",url,400,200);
    		}else{
    			layer.msg('该数据已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 标识制卡
	$("#markMakeCard").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
	    		layer.confirm("确定标识制卡?",function(index){
					layer.load(2, { //icon支持传入0-2
						shade: [0.5, 'gray'], //0.5透明度的灰色背景
						content: '加载中...',
						success: function (layero) {
							layero.find('.layui-layer-content').css({
								'padding-top': '39px',
								'width': '60px'
							});
						}
					});
					util.sendAjax({
						url:"#(ctxPath)/fina/cardmanager/updatePwd",
						type:'post',
						data:{id:tableData[0].id, isMakeCard:'1'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								$('#search').click();
							}
							layer.close(index);
						}
					});
				});
    		}else{
    			layer.msg('该数据已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 标识退卡
	$("#markRefundCard").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].returnCardStatus=='none'){
	    		layer.confirm("确定标识退卡?",function(index){
					layer.load(2, { //icon支持传入0-2
						shade: [0.5, 'gray'], //0.5透明度的灰色背景
						content: '加载中...',
						success: function (layero) {
							layero.find('.layui-layer-content').css({
								'padding-top': '39px',
								'width': '60px'
							});
						}
					});
					util.sendAjax({
						url:"#(ctxPath)/fina/cardmanager/updatePwd",
						type:'post',
						data:{id:tableData[0].id, returnCardStatus:'finish'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								$('#search').click();
							}
							layer.close(index);
						}
					});
				});
    		}else{
    			layer.msg('不是未退卡状态', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	//转移
	$("#transferCard").click(function () {
		// var tableData = getCheckTableData();
    	// if(tableData.length==1){
    	// 	if(tableData[0].delFlag=='0'){
	    // 		layerShow('转移','#(ctxPath)/fina/cardmanager/cardTransferForm?cardNumber='+tableData[0].cardNumber,'100%','100%');
    	// 	}else{
    	// 		layer.msg('该数据已作废', function () {});
    	// 	}
    	// }else if(tableData.length==0){
    	// 	layer.msg('请勾选数据', function () {});
    	// }else{
    	// 	layer.msg('只能勾选一条数据', function () {});
    	// }
		layerShow('转移','#(ctxPath)/fina/cardmanager/cardTransferForm','100%','100%');
	});
	// 更换卡主
	$("#changeOwn").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
				var url = "#(ctxPath)/fina/cardmanager/form?type=change&cardId=" + tableData[0].id +"&memberId=" + tableData[0].memberId;
				layerShow("更换卡主",url);
    		}else{
    			layer.msg('该数据已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	//批量充值
	$("#batchRecharge").click(function () {
		layerShow('批量充值','#(ctxPath)/fina/cardmanager/bathRechargeIndex','100%','100%');
	});
	// 赠送记录
	$("#giveRecord").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
			var url = "#(ctxPath)/fina/cardmanager/cardGiveRecordIndex?cardId=" + tableData[0].id;
			layerShow("赠送记录",url,750,550);
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 操作记录
	$("#operateRecord").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
			var url = "#(ctxPath)/operate/index?dataId=" + tableData[0].id;
			layerShow("操作记录",url);
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 作废
	$("#del").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
    		if(tableData[0].delFlag=='0'){
	    		layer.open({
					type: 1,
					title:"作废会员卡",
					area:['500px','230px'],
					btn: ['确定', '取消'],
					content: '<form class="layui-form" action="" style="margin-top: 10px;">' +
							'<div class="layui-form-item layui-form-text">' +
							'    <label class="layui-form-label">备注</label>' +
							'    <div class="layui-input-block">' +
							'      <textarea id="voidRemark" name="voidRemark" class="layui-textarea" placeholder="请输入备注"></textarea>' +
							'    </div>' +
							'  </div>',
					yes:function(index,layero){
						var voidRemark = $("#voidRemark").val();
						if(voidRemark!=null && voidRemark!=''){
							util.sendAjax({
								url:"#(ctxPath)/fina/cardmanager/voidCard",
								type:'post',
								data: {id:tableData[0].id, voidRemark:voidRemark},
								notice:true,
								success:function(returnData){
									if(returnData.state==='ok'){
										$('#search').click();
									}
									layer.close(index);
								}
							});
						}else{
							layer.msg('备注不能为空，请输入备注!',{icon:5});
						}
					}
				});
    		}else{
    			layer.msg('该数据已作废', function () {});
    		}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	//批量作废
	$("#batchVoid").click(function(){
		layerShow('批量作废','#(ctxPath)/fina/cardmanager/bathVoidIndex','100%','100%');
	});
	// 删除
	$("#delete").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
			if(tableData[0].delFlag=='1'){//作废状态
				layer.confirm("确定要删除该会员卡?删除后不能恢复,请谨慎操作!",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/fina/cardmanager/deleteCard",
                        type:'post',
                        data:{"id":tableData[0].id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
								$('#search').click();
                            }
                            layer.close(index);
                        }
                    });
                });
			}else{
				layer.msg('未作废状态，不能删除!',{icon:5});
			}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 作废恢复
	$("#recovery").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
			if(tableData[0].delFlag=='1'){//作废状态
				layer.confirm("确定要恢复该会员卡?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/fina/cardmanager/recoveryCard",
                        type:'post',
                        data:{"id":tableData[0].id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
								$('#search').click();
                            }
                            layer.close(index);
                        }
                    });
                });
			}else{
				layer.msg('未作废状态，不能恢复!',{icon:5});
			}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	// 过期恢复
	$("#expired").click(function(){
		var tableData = getCheckTableData();
    	if(tableData.length==1){
			if(tableData[0].delFlag=='0'){
				var url = '#(ctxPath)/fina/cardmanager/expiredCardForm?id='+tableData[0].id+"&expireDate="+dateFormat(tableData[0].expireDate,'yyyy-MM-dd');
				layerShow("过期恢复",url,500,450);
			}else{
				layer.msg('该会员卡已作废', function () {});
			}
    	}else if(tableData.length==0){
    		layer.msg('请勾选数据', function () {});
    	}else{
    		layer.msg('只能勾选一条数据', function () {});
    	}
	});
	//导出模板
	$("#export").on("click",function () {
		var url = "#(ctx)/sys/upload/excelExport";
		var eleLink = document.createElement('a');
		eleLink.style.display = 'none';
		eleLink.href = url;
		// 触发点击
		document.body.appendChild(eleLink);
		eleLink.click();
		// 然后移除
		document.body.removeChild(eleLink);
	});
	//导入会员卡
	var uploadInst = upload.render({
		elem: '#import'
		,url: '#(ctx)/sys/upload/excelUpload'
		,accept: 'file'
		,exts: 'xls|xlsx' //只允许上传xls后缀的文件
		,multiple: false
		,done: function(res){
			if(res.state==='ok') {
				layer.msg(res.msg, {icon: 1, time: 2000}, function () {});
				$('#search').click();
			}else{
				layer.msg(res.msg, {icon: 5, time: 2000});
			}
		}
		,error: function(res){
			layer.msg(res.msg, {icon: 5, time: 2000});
		}
	});
	//导出会员卡
	$("#exportData").on("click",function () {
		var referencePrice = '';
		if($('input[name="referencePrice"]').prop('checked')){
			referencePrice = '0';
		}else{
			referencePrice = '';
		}
		if(table.cache.cardTable.length>0){
// 			var url='#(ctxPath)/fina/cardmanager/exportData?cardTypeId='+$('#cardTypeId').val()+"&cardNumber="+$('#cardNumber').val()+"&fullName="+$('#name').val()+"&idcard="+$('#idcard').val()+"&orderBy="+$('#orderBy').val()+"&referencePrice="+referencePrice;
			var url='#(ctxPath)/fina/cardmanager/exportData?cardTypeId='+$('#cardTypeId').val()+"&cardNumber="+$('#cardNumber').val()+
					"&fullName="+$('#fullName').val()+"&idcard="+$('#idcard').val()+"&delFlag="+$('#delFlag').val()+
					"&isLock="+$('#isLock').val()+"&expireFlag="+$('#expireFlag').val()+"&referencePrice="+referencePrice+
					"&orderBy="+$('#orderBy').val();
			window.location.href=url;
		}else{
			layer.msg('导出的内容为空', {icon: 2, offset: 'auto'});
		}
	});
	//福利卡导入
	var cardImportUpload = upload.render({
		elem: '#cardImport'
		,url: '#(ctxPath)/fina/cardmanager/cardImport'
		,accept: 'file'
		,exts: 'xls|xlsx' //只允许上传xls后缀的文件
		,multiple: false
		,done: function(res){
			if(res.state==='ok') {
				layer.msg(res.msg, {icon: 1, time: 20000}, function () {});
				$('#search').click();
			}else{
				layer.msg(res.msg, {icon: 5, time: 20000});
			}
		}
		,error: function(res){
			layer.msg(res.msg, {icon: 5, time: 2000});
		}
	});
	//世纪昌松卡导入
	var shiJiCardImportUpload = upload.render({
		elem: '#shiJiCardImport'
		,url: '#(ctxPath)/fina/cardmanager/shiJiCardImport'
		,accept: 'file'
		,exts: 'xls|xlsx' //只允许上传xls后缀的文件
		,multiple: false
		,done: function(res){
			if(res.state==='ok') {
				layer.msg(res.msg, {icon: 1, time: 20000}, function () {});
				$('#search').click();
			}else{
				layer.msg(res.msg, {icon: 5, time: 20000});
			}
		}
		,error: function(res){
			layer.msg(res.msg, {icon: 5, time: 20000});
		}
	});
	//已锁定/已作废的列表信息提示
	function tdTitle(){
		/*$('th').each(function(index,element){
			$(element).attr('title',$(element).text());
		});*/
		$('td').each(function(index,element){
			if("已锁定"==$(element).find('span').text()){
				var lockByName=$(element).find('span').attr("lock-by-name");
				var lockDate=$(element).find('span').attr("lock-date");
				var lockRemark=$(element).find('span').attr("lock-remark");
				var lockUserName=$(element).find('span').attr("lock-user-name");
				$(element).attr('title',"锁定人："+lockByName+"("+lockUserName+")"+"\n锁定时间："+lockDate+"\n备注："+lockRemark);
			}else if("已作废"==$(element).find('span').text()){
				var voidByName=$(element).find('span').parent().attr("void-by-name");
				var voidDate=$(element).find('span').parent().attr("void-date");
				var voidRemark=$(element).find('span').parent().attr("void-remark");
				var voidUserName=$(element).find('span').parent().attr("void-user-name");
				$(element).attr('title',"作废人："+voidByName+"("+voidUserName+")"+"\n作废时间："+voidDate+"\n备注："+voidRemark);
			}

		});
	};
	//搜索按钮查询
	form.on("submit(search)",function(data){
		layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		layuiTableReload(data.field, 1);
		return false;
	});
	//回车事件
	document.onkeydown = function(e){
		var ev =document.all ? window.event:e;  
		if(ev.keyCode==13) {
			$('#search').click();
			return false
		}
	}
	//弹窗关闭调用刷新方法
	formOperateReload = function(){
		var currentPage = $(".layui-laypage-skip").find("input").val() //当前页码值
		var queryField = {
			cardNumber:$('#cardNumber').val()
			,fullName:$('#fullName').val()
			,idcard:$('#idcard').val()
			,cardTypeId:$('#cardTypeId').val()
			,delFlag:$('#delFlag').val()
			,isLock:$('#isLock').val()
			,referencePrice:$('#referencePrice').val()
			,isMakeCard:$('#isMakeCard').val()
			,expireFlag:$('#expireFlag').val()
			,dataSource:$('#dataSource').val()
			,province:$('#province').val()
			,city:$('#city').val()
			,town:$('#town').val()
			,street:$('#street').val()
			,branchOfficeId:$('#branchOfficeId').val()
			,balance:$('#balance').val()
			,consumeTimes:$('#consumeTimes').val()
			,cardIntegrals:$('#cardIntegrals').val()
			,noDealStartDate:$('#noDealStartDate').val()
			,noDealEndDate:$('#noDealEndDate').val()
			,nearExpireStartYear:$('#nearExpireStartYear').val()
			,nearExpireEndYear:$('#nearExpireEndYear').val()
			,orderBy:$('#orderBy').val()
		};
		layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		layuiTableReload(queryField, currentPage);
	}
	//作废会员卡管理
// 	$("#recovery").click(function () {
// 		layerShow('恢复会员卡','#(ctxPath)/fina/cardmanager/recoveryCardIndex',1200,700);
// 	});
	//过期会员卡管理
// 	$("#expired").click(function () {
// 		layerShow('过期会员卡','#(ctxPath)/fina/cardmanager/expiredCardIndex',1200,700);
// 	});
});

</script>
#end