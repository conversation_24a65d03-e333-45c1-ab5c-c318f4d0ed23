#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构养老费用账单首页#end

#define css()
#end

#define content()
<div class="my-btn-box">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
            <div class="layui-row">
                <div class="layui-inline">
					<label class="layui-form-label">入住人</label>
					<div class="layui-input-inline">
						<input type="text" id="memberName" name="memberName" class="layui-input" placeholder="请输入入住人名称" autocomplete="off">
					</div>
				</div>
                <div class="layui-inline">
					<label class="layui-form-label">入住流水号</label>
					<div class="layui-input-inline">
						<input type="text" id="checkinNo" name="checkinNo" class="layui-input" placeholder="请输入入住流水号" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<div class="layui-btn-group">
		                <button type="button" class="layui-btn" id="search" lay-submit="" lay-filter="search">查询</button>
		                #shiroHasPermission("finance:orgSettle:arrearsBtn")
		                <button type="button" class="layui-btn" id="arrears">欠费查询</button>
		                #end
		                #shiroHasPermission("finance:orgSettle:batchSettlementBtn")
		                <button type="button" class="layui-btn" id="batchSettlement">批量结算</button>
					</div>
				</div>
                #end
            </div>
        </form>
    <div class="layui-row">
        <table id="mainTable" class="layui-table" lay-filter="mainTable"></table>
    </div>
</div>
#getDictLabel("gender")
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("finance:orgSettle:checkBtn")
    <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="check">查看</a>
    #end

    #shiroHasPermission("finance:orgSettle:settleBtn")
    #[[
    {{#if(d.unsettled>0){}}
    <a class="layui-btn layui-btn-xs" lay-event="settle">结算</a>
    {{#}}}
    ]]#
    #end
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        mainTableReload=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'mainTable'
                ,elem : '#mainTable'
                ,method : 'POST'
                ,height: 'full-110'
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/orgSettle/findSettleMemberPage'
                ,where: data
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'checkinNo', title: '入住流水号', align: 'center', unresize: true}
                    ,{field:'memberName', title: '入住人姓名', align: 'center', unresize: true}
                    ,{field:'checkinDate',title:'入住时间',align:'center',unresize:true,templet:"<div>{{dateFormat(d.checkinDate,'yyyy-MM-dd')}}</div>"}
                    ,{field:'totalSettle', title: '总账单数', align: 'center', unresize: true}
                    ,{field:'unsettled', title: '未结算账单数', align: 'center', unresize: true}
                    ,{field:'settled', title: '已结算账单数', align: 'center', unresize: true}
                    ,{field:'delSettle', title: '作废结算账单数', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                , done: function (res, curr, count) {
                    layer.close(loadingIndex);
                }
            });
            table.on('tool(mainTable)',function(obj){
                if (obj.event === 'check') {
                    layerShow("查看账单","#(ctxPath)/fina/orgSettle/checkSettleMainIndex?checkinNo="+obj.data.checkinNo+"&memberName="+obj.data.memberName,'100%','100%');
                }else if(obj.event === 'settle'){
                    layui.layer.open({
                        type: 2,
                        area: ['100%', '100%'],
                        fix: true, //不固定
                        maxmin: true,
                        shade:0.4,
                        title: "机构养老消费结算",
                        content: "#(ctxPath)/fina/orgSettle/orgSettleMainIndex?checkinNo="+obj.data.checkinNo+"&memberName="+obj.data.memberName,
                        end:function () {
                            table.reload('mainTable',{'where':{'memberName':$("#memberName").val(),'checkinNo':$("#checkinNo").val()}});
                        }
                    });
                }else if(obj.event==='print'){

                }
            });
        }

        mainTableReload(null);

        form.on("submit(search)",function(data){
            mainTableReload(data.field);
            return false;
        });

        $("#arrears").on('click',function () {
            layerShow("欠费查询","#(ctxPath)/fina/orgSettle/arrearsIndex",'100%','100%');
        });

        $("#batchSettlement").on('click',function () {
            layerShow("批量结算","#(ctxPath)/fina/orgSettle/orgBatchSettleIndex",'100%','100%');
        });
        
      	//回车事件
    	document.onkeydown = function(e){
    		var ev =document.all ? window.event:e;  
    		if(ev.keyCode==13) {
    			mainTableReload(null);
    			return false
    		}
    	}
    });
</script>
#end