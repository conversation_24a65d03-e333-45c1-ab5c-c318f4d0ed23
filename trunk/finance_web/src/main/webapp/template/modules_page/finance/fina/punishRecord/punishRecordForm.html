#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅居违约处罚结算#end
<!-- 公共Css文件 -->
#define css()
<link rel="stylesheet" href="#(ctx)/static/css/member.css" media="all" />
<style>
    .layui-form-item{margin-bottom:0;}
    .fo{font-size:10px;}
</style>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="punishForm">
            <div class="layui-row">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">入住人</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(pr.name??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">基地</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(pr.baseName??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">分公司</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(pr.officeName??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">预订号</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(pr.bookNo??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">入住号</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(pr.checkinNo??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">会员卡号</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" name="pr.cardNumber" value="#(pr.cardNumber??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label">持卡人</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(pr.fullName??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <fieldset class="layui-elem-field">
                    <legend>结算详情</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">处罚类型</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#(pr.punishTypeName??)" autocomplete="off" class="layui-input" style="width:115%;">
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left:30px;">
                                <label class="layui-form-label">处理状态</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#(pr.statusName??)" autocomplete="off" class="layui-input" style="width:115%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label fo">预订创建时间</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#date(pr.bookCreateTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input" style="width:115%;">
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left:30px;">
                                <label class="layui-form-label fo">预订取消时间</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#date(pr.bookCancelTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input" style="width:115%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label fo">计划入住时间</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#date(pr.planCheckinTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input" style="width:115%;">
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left:30px;">
                                <label class="layui-form-label fo">实际入住时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#date(pr.actualCheckinTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label fo">计划退住时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#date(pr.planCheckoutTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left:12px;">
                                <label class="layui-form-label fo">实际退住时间</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#date(pr.actualCheckoutTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input" style="width:111%;">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">处罚天数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="pr.times" value="#(pr.times??)" lay-verify="checkAmount|required" autocomplete="off" class="layui-input" style="width:111%;"#if(type=='look') readonly #end  #if(deductWay=='deduct_by_money') readonly #end >
                                </div>
                            </div>
                            <div class="layui-inline" style="margin-left:12px;">
                                <label class="layui-form-label">处罚金额</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="pr.amount" value="#(pr.amount??)" lay-verify="checkAmount|required" autocomplete="off" class="layui-input" style="width:111%;" #if(type=='look') readonly #end  #if(deductWay=='deduct_by_days') readonly  #end>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">处罚点数</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="pr.points" value="#(pr.points??)" lay-verify="checkAmount|required" autocomplete="off" class="layui-input" style="width:111%;"#if(type=='look') readonly #end  #if(deductWay=='deduct_by_points') readonly #end >
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">处罚描述</label>
                            <div class="layui-input-block">
                                <textarea class="layui-textarea" name="pr.describe" #if(type=='look') readonly #end >#(pr.describe??)</textarea>
                            </div>
                        </div>
                        &nbsp;&nbsp;
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">处罚备注</label>
                            <div class="layui-input-block">
                                <textarea class="layui-textarea" name="pr.remark" #if(type=='look') readonly #end >#(pr.remark??)</textarea>
                            </div>
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="layui-form-footer" #if(type=='look') style="display:none;" #end>
                <div class="pull-right">
                    <input type="hidden" name="pr.id" value="#(pr.id??)"/>
                    <input type="hidden" name="pr.baseId" value="#(pr.baseId??)"/>
                    <input type="hidden" name="pr.officeId" value="#(pr.officeId??)"/>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">确认扣费</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form','table','layer','laydate'],function(){
        var form = layui.form;
        var $ = layui.$;
        var table = layui.table;
        var layer=layui.layer;
        var laydate = layui.laydate;


        //校验
        form.verify({
            checkAmount:function(value){
                if(value != null){
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if(!reg1.test(value) && !reg2.test(value)){
                        return "只能输入数字";
                    }
                }
            },
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            }
        });

        //违约结算
        form.on('submit(confirmBtn)', function () {
            layer.confirm("确定要扣费吗?",function(index) {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/fina/punishRecord/settle',
                    data: $("#punishForm").serialize(),
                    notice: true,
                    loadFlag: true,
                    success: function (rep) {
                        if (rep.state == 'ok') {
                            pop_close();
                            parent.prLoad(null);
                        }
                    },
                    complete: function () {
                    }
                });
                layer.close(index);
            });
            return false;
        });
    });
</script>
#end