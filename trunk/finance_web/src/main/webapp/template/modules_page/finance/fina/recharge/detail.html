#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡充值情况#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
	    <div class="layui-row">
	        <table id="rechargeRecordTable" lay-filter="rechargeRecordTable"></table>
	    </div>
	</form>
</div>
#getDictLabel("recharge_classify")
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;
        
		layer.load();
		
        table.render({
            id : 'rechargeRecordTable',
            elem : "#rechargeRecordTable",
            url : '#(ctxPath)/fina/recharge/pageTable',
            where: {cardNumber:'#(cardNumber??)', isReview:'1', delFlag:'0', orderBy:'desc'},
            height:'full-130',
            cols : [[
                {field:'',title:'充值时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.rechargeTime,'yyyy-MM-dd')}}</div>"},
                {field:'',title:'充值类型',width:90,unresize:false,templet:"<div>{{ d.type=='1'?'<span class='layui-badge layui-bg-green'>金额</span>':d.type=='2'?'<span class='layui-badge layui-bg-orange'>天数</span>':d.type=='3'?'<span class='layui-badge layui-bg-black'>点数</span>':d.type=='4'?'<span class='layui-badge layui-bg-blue'>积分</span>':d.type=='5'?'<span class='layui-badge layui-bg-cyan'>豆豆券</span>':'- -' }}</div>"},
				{field:'',title:'充值分类',width:90,unresize:false,templet: '#dictTpl("recharge_classify", "classify")'},
                {field:'describe',title:'说明',unresize:true,width:140,style:"text-align:left;font-size:10px;color:#000;"},
				{field:'isIncome',title:'收入性',width:90,unresize:false,templet:function (d) {
					if(d.isIncome=='1'){
						return "<span class='layui-badge layui-bg-green'>收入性</span>";
					}else if(d.isIncome=='0'){
						return "<span class='layui-badge layui-bg-orange'>非收入性</span>";
					}else{
						return '- -'
					}
				}},
				{field:'rechargeNo',title:'充值编号',width:120,unresize:false},
				{field:'',title:'是否现金',width:90,unresize:false,templet:function (d) {
					if(d.isCash=='1'){
						return "<span class='layui-badge layui-bg-green'>是</span>";
					}else if(d.isCash=='0'){
						return "<span class='layui-badge layui-bg-orange'>否</span>";
					}else{
						return '- -'
					}
				}},
				{field:'',title:'收钱账户',width:160,unresize:false,templet:function (d) {
					var payWay="";
					var accountName="";
					if(d.accountName!=undefined){
						accountName=d.accountName
					}
					if(d.payWay=="1"){
						payWay="(现金)";
					}else if(d.payWay=="2"){
						payWay="(微信)";
					}else if(d.payWay=="3"){
						payWay="(支付宝)";
					}else if(d.payWay=="4"){
						payWay="(信用卡)";
					}else if(d.payWay=="5"){
						payWay="(会员卡)";
					}else if(d.payWay=="6"){
						payWay="(Pos机)";
					}else if(d.payWay=="7"){
						payWay="(转账)";
					}else if(d.payWay=="8"){
						payWay="(企业微信)";
					}
					return payWay+accountName;
				}},
				{field:'',title:'付款金额',width:90,unresize:false,templet: "<div>{{ String(d.payAmount)?d.payAmount + '元':'- -' }}</div>"},
				{field:'',title:'充值金额',width:90,unresize:false,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
                {field:'',title:'充值天数',width:90,unresize:false,templet: "<div>{{ String(d.consumeTimes)?d.consumeTimes + '天':'- -' }}</div>"},
                {field:'consumePoints',title:'充值点数',width:90,unresize:false},
                {field:'consumeIntegral',title:'充值积分',width:90,unresize:false},
                {field:'consumeBeanCoupons',title:'充值豆豆券',width:90,unresize:false},
                {field:'',title:'审核状态',width:90,unresize:false,templet:function (d) {
                    if(d.isReview=='0'){
                        return '未审核';
                    }else{
                        return '已审核';
                    }
                }},
                {field:'',title:'作废状态',width:90,unresize:false,templet:function (d) {
                    if(d.delFlag=='0'){
                        return '未作废';
                    }else{
                        return '已作废';
                    }
                }}
            ]] ,
            page : true,
            limit : 10,
			done:function () {
				layer.closeAll('loading');
			}
        }) ;
    });
</script>
#end