#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()费用明细修改备注#end

#define css()
#end

#define js()
<script type="text/javascript">
    var table,$ ;
    layui.use(['table','laydate','form'],function(){
        table = layui.table;
        $ = layui.$ ;
        var laydate = layui.laydate;
        var form=layui.form;

        laydate.render({
            elem:"#startEndDate",
            type:'datetime',
            trigger:'click',
            range:true,
            done:function (val) {
                if(val!=''){
                    val=val.substr(0,33)+"23:59:59";
                    setTimeout(function () {
                        $("#startEndDate").val(val);
                    }, 50);
                }
            }
        })


        //保存
        form.on('submit(saveBtn)', function(obj){
            console.log(obj.field);
            util.sendAjax({
                url:'#(ctxPath)/fina/orgSettle/billDetailRemarkSave',
                type:'post',
                data:obj.field,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.billDetailTableReload($("#mainId").val());
                    }
                }
            });
            return false;
        });

    });

</script>
<script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs" lay-event="edit">详情</a>
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row" style="padding-top: 20px;">
        <form class="layui-form layui-form-pane" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">费用名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" autocomplete="off" value="#(detail.name??)" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">扣除方式</label>
                <div class="layui-input-block">
                    <input type="radio" name="deductType" value="deduct_by_money" title="按金额扣除" #if(detail.deductType??=='deduct_by_money') checked #end>
                    <input type="radio" name="deductType" value="deduct_by_days" title="按天数扣除" #if(detail.deductType??=='deduct_by_days') checked #end>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">产生时间</label>
                <div class="layui-input-block">
                    <input type="text" readonly id="startEndDate" name="startEndDate" autocomplete="off" value="#date(detail.startDate??,'yyyy-MM-dd HH:mm:ss') - #date(detail.endDate??,'yyyy-MM-dd HH:mm:ss')" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">明细描述</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入明细描述内容" name="describe" class="layui-textarea">#(detail.describe??)</textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注内容" name="remark" class="layui-textarea">#(detail.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input id="detailId" type="hidden" name="id" value="#(detail.id??)" >
                    <input id="mainId" type="hidden" value="#(detail.mainId??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end