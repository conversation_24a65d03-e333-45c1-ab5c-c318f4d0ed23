#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商付款信息页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div id="supplierLinkTab" class="layui-tab layui-tab-brief" lay-filter="supplierLinkTab">
		<ul class="layui-tab-title">
			<li id="list" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<form id="frm" class="layui-form" lay-filter="layform" action="" method="post">
				<div class="layui-row">
					<div class="layui-inline">
						<label class="layui-form-label">联系人类型</label>
						<div class="layui-input-inline" style="width:150px;">
							<select name="linkType">
								<option value="">全部</option>
								#statusOption(com.cszn.integrated.service.entity.status.LinkType::me(), "")
							</select>
						</div>
					</div>
					<div class="layui-inline">
						<label class="layui-form-label">联系人名称</label>
						<div class="layui-input-inline">
							<input id="linkName" name="linkName" class="layui-input">
						</div>
					</div>
					<div class="layui-inline">
						<input type="hidden" id="supplierId" name="supplierId" value="#(suppliersId??)">
						<button type="button" id="search"  class="layui-btn" lay-submit="" lay-filter="search">查询</button>
						<button type="button" id="addBtn" class="layui-btn">添加</button>
					</div>
				</div>
				</form>
				<div class="layui-row">
					<table id="supplierLinkTable" lay-filter="supplierLinkTable"></table>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:delBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</div>
</script>
<script type="text/javascript">
layui.use([ 'table', 'form', 'element' ], function() {
	var table = layui.table
	, form = layui.form
	, element = layui.element
	, $ = layui.jquery
	;
	
	function linkLoad(data){
		table.render({
			id : 'supplierLinkTable'
			,elem : '#supplierLinkTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/wms/supplier/linkPage'
// 			,cellMinWidth: 80
			,height:'full-150'
			,cols: [[
				{field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
				,{field:'', title: '联系人类型', align: 'center', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.LinkType::me(), "linkType")'}
				,{field:'linkName', title: '联系人名称', align: 'center', unresize: true}
				,{field:'linkPosition', title: '联系人职位', align: 'center', unresize: true}
				,{field:'linkPhone', title: '联系人电话', align: 'center', unresize: true}
				,{field:'linkWechat', title: '联系人微信', align: 'center', unresize: true}
				,{fixed:'right', title: '操作', width:120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 15
			,limits : [15,25,35,45]
			, loading: true
		    , done: function (res, curr, count) {
		    }
		});
		table.on('tool(supplierLinkTable)',function(obj){
			if(obj.event==="edit"){//编辑按钮事件
				element.tabAdd('supplierLinkTab', {
					title: '编辑'
					, content: '<div id="linkForm"></div>'
					, id: 'form'
				});
				$('#linkForm').load('#(ctxPath)/wms/supplier/linkForm?id='+obj.data.id);
				element.tabChange('supplierLinkTab', 'form');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm("确定要作废吗?",function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/wms/supplier/saveLink',
	                    notice: true,
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                        if(rep.state=='ok'){
	                        	tableReload("supplierLinkTable",{supplierId:$('#supplierId').val()});
	                        }
	                        layer.close(index);
	                    },
	                    complete : function() {
	                    }
	                });
	            });
	        }
		});
	};
	
	linkLoad({supplierId:$('#supplierId').val()});
	
	form.on("submit(search)",function(data){
		linkLoad(data.field);
		return false;
	});
	
    // 添加
    $("#addBtn").click(function(){
		element.tabAdd('supplierLinkTab', {
			title: '添加'
			, content: '<div id="linkForm"></div>'
			, id: 'form'
		});
		$('#linkForm').load('#(ctxPath)/wms/supplier/linkForm?supplierId='+$('#supplierId').val());
		element.tabChange('supplierLinkTab', 'form');
    });
    
	element.on('tab(supplierLinkTab)', function(data){
		if(data.index==0){
// 			console.log(this); //当前Tab标题所在的原始DOM元素
// 			console.log(data.index); //得到当前Tab的所在下标
// 			console.log(data.elem); //得到当前的Tab大容器
		}
	});
});
</script>
#end