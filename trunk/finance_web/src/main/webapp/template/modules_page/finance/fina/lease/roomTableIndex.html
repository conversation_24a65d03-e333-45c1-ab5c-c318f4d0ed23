#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()供应商查询#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">
            <input id="baseId" name="baseId" value="#(baseId??)" type="hidden" >
            <div class="layui-inline">
                <label class="layui-form-label">楼栋</label>
                <div class="layui-input-inline">
                    <select name="buildingId" id="buildingId" lay-filter="buildingId">
                        <option value="">全部</option>
                        #for(building : buildingList)
                        <option value="#(building.id??)">#(building.buildingName??)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">楼层</label>
                <div class="layui-input-inline">
                    <select name="floorId" id="floorId" lay-filter="floorId">

                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">房间号</label>
                <div class="layui-input-inline">
                    <input id="name" name="name" class="layui-input">
                </div>
            </div>

            <div class="layui-inline">
                <div class="layui-btn-group">
                    <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>

                </div>
            </div>
        </div>
        <input id="type" name="type" type="hidden" value="2">
    </form>
    <input type="hidden" id="trIndex" value="#(trIndex??)" >
    <div class="layui-row">
        <table class="layui-table" id="supplierTable" lay-filter="supplierTable"></table>
    </div>
</div>
#getDictLabel("supplier_level")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
        <!--        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>-->
        <!--        <a class="layui-btn layui-btn-xs" lay-event="pay">付款信息</a>-->
        <a class="layui-btn layui-btn-xs" lay-event="selected">选择</a>
    </div>
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        form.on('select(buildingId)',function (d) {

            $.post('#(ctxPath)/fina/leaseRecordApply/getFloorByBuildingId',{'buildingId':d.value},function (res) {
                let str="<option value=''>全部</option>";
                $.each(res,function (index,item) {
                    str+="<option value='"+item.id+"'>"+item.floorName+"</option>";
                });

                $("#floorId").html(str);
                form.render('select');
            })

        })

        supplierTableReload = function (data) {
            table.render({
                id : 'supplierTable'
                ,elem : '#supplierTable'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/fina/leaseRecordApply/roomPage'
                ,height:'full-90'
                ,cols: [[
                    {field:'building_name', title: '楼栋', align: 'center',   unresize: true}
                    ,{field:'floor_name', title: '楼层', align: 'center',   unresize: true}
                    ,{field:'room_name', title: '房间', align: 'center',   unresize: true}
                    ,{field:'type_name', title: '房间类型', align: 'center',   unresize: true}
                    ,{fixed:'right', title: '操作' , align: 'center', unresize: true, toolbar: '#toolBar'}
                ]]
                ,page : true
                ,done:function () {
                }
            });
            table.on('tool(supplierTable)',function (obj) {
                if(obj.event==='edit'){
                    pop_show('编辑','#(ctxPath)/wms/supplier/supplierForm?id='+obj.data.id,'','');
                }else if(obj.event==='pay'){
                    pop_show('付款信息','#(ctxPath)/wms/supplier/payIndex?id='+obj.data.id,'','');
                }else if(obj.event==='link'){
                    pop_show('联系人信息','#(ctxPath)/wms/supplier/linkIndex?id='+obj.data.id,'','');
                }else if(obj.event==='del'){
                    layer.confirm("确定要作废吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/wms/supplier/saveSupplier',
                            notice: true,
                            data: {id:obj.data.id, delFlag:'1'},
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    table.reload('supplierTable');
                                }
                                layer.close(index);
                            },
                            complete : function() {
                            }
                        });
                    });
                }else if(obj.event==='selected'){
                    parent.selectedRoomData(obj.data,$("#trIndex").val());
                    pop_close();
                }
            });
        }

        supplierTableReload({'baseId':$("#baseId").val()});

        form.on("submit(search)",function(data){
            supplierTableReload(data.field);
            return false;
        });

    });
</script>

#end