#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()发票信息展示#end
<!-- 公共Css文件 -->
#define css()
<link rel="stylesheet" href="#(ctx)/static/css/member.css" media="all" />
<style>
    .layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
    .layui-form-item{margin-bottom:0;}
</style>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="invoiceForm">
            <input name="invoiceId" id="invoiceId" type="hidden" value="#(invoice != null ? invoice.id : '')" />
            <div class="layui-row">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">发票代码</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(invoice.invoiceCode??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">发票号码</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(invoice.invoiceNo??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="font-size: 10px;">纳税人识别号</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(invoice.identificationNo??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <label class="layui-form-label">开户银行</label>
                        <div class="layui-input-block">
                            <input type="text" readonly="readonly" value="#(invoice.bankName??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-inline">
                        <label class="layui-form-label" style="font-size: 10px;">开户银行账号</label>
                        <div class="layui-input-inline">
                            <input type="text" readonly="readonly" value="#(invoice.bankCardNo??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
                <fieldset class="layui-elem-field">
                    <legend>其他信息</legend>
                    <div class="layui-field-box">
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">合同编号</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#(invoice.contractNo??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">所属基地</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#(baseName??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">入住天数</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#(invoice.checkinDays??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">收件人</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#(invoice.receiveName??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label">座机号码</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#(invoice.landlineNo??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">邮箱</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#(invoice.email??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-inline">
                                <label class="layui-form-label" style="font-size: 10px;">收件人手机号</label>
                                <div class="layui-input-block">
                                    <input type="text" readonly="readonly" value="#(invoice.receiveMobile??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-inline">
                                <label class="layui-form-label">收件人地址</label>
                                <div class="layui-input-inline">
                                    <input type="text" readonly="readonly" value="#(invoice.receiveAddress??)" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item layui-form-text">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <textarea class="layui-textarea" readonly="readonly">#(invoice.remark??)</textarea>
                            </div>
                        </div>
                        <table id="detailTable" lay-filter="detailTable"></table>
                    </div>
                </fieldset>
            </div>
            <!--<div class="layui-form-footer">
                <div class="pull-right">
                    <input name="consumeId" id="recordId" type="hidden" value="#(record != null ? record.id : '')" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">确认扣费</button>
                </div>
            </div>-->
        </form>

    </div>
</div>
</body>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload', 'table'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;
        var table = layui.table;

        function detailLoad(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id : 'detailTable'
                ,elem : '#detailTable'
                ,method : 'POST'
                ,where : data
                ,limit : 5
                ,limits : [5,15,30,45,50]
                ,url : '#(ctxPath)/fina/invoice/details'
                ,cellMinWidth: 80
                ,height: 200
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '名称', align: 'center', unresize: true}
                    ,{field:'count', title: '数量', align: 'center', unresize: true}
                    ,{field:'unit', title: '单位', sort: true, align: 'center', unresize: true}
                    ,{field:'unitPrice', title: '单价', align: 'center', unresize: true}
                ]]
                //,page : true
                , done: function (res, curr, count) {
                    layer.close(loadingIndex);
                }
            });
        };

        detailLoad({id:$('#invoiceId').val()});
    });
</script>
#end