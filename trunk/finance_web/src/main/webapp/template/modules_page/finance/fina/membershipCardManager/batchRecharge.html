#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()批量充值页面#end

#define css()
<style>
    .layui-table td, .layui-table th {
        position: relative;
        padding: 9px 5px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }
    .layui-form-radio {
        line-height: 28px;
        margin: 6px 2px 0 0;
        padding-right: 10px;
        cursor: pointer;
        font-size: 0;
    }
</style>
#end

#define js()
<script>
    layui.use(['form','laytpl','layer','table','upload'], function() {
        var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,table=layui.table,upload = layui.upload;

        del=function(index){
            $("#tr-"+index).remove();
        }

        table.on('tool(rechargeTable)',function (obj) {
            if(obj.event==='cancel'){
                layer.confirm('确定要撤销该充值记录吗?',function (index) {
                    util.sendAjax({
                        url:"#(ctxPath)/fina/recharge/cancelRechargeRecord",
                        type:'post',
                        data:{'id':obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                rechargeTableReload();
                                parent.consumeRecordTableReload();
                            }
                        }
                    });
                });
            }
        })

        $("#add").on('click',function () {
            laytpl(rechargeTrTpl.innerHTML).render({'idx':(Number($("#count").val())+1)}, function(html){
                $("#count").val((Number($("#count").val())+1));
                $("#rechargeTbody").append(html);
                form.render('radio')
            });
        });

        $("#choice").on('click',function () {
            layerShow('选择会员卡','#(ctxPath)/fina/cardmanager/memberCardTable?actionType=batchRecharge',1000,600);
        });
        
    	//导入会员卡
    	var uploadInst = upload.render({
    		elem: '#importBtn'
    		,url: '#(ctx)/fina/recharge/rechargeImport'
    		,accept: 'file'
    		,exts: 'xls|xlsx' //只允许上传xls后缀的文件
    		,multiple: false
    		,done: function(res){
    			if(res.state==='ok') {
    				layer.msg(res.msg, {icon: 1, time: 20000}, function () {});
    			}else{
    				layer.msg(res.msg, {icon: 5, time: 20000});
    			}
    		}
    		,error: function(res){
    			layer.msg(res.msg, {icon: 5, time: 2000});
    		}
    	});
        
        findCardNumber=function (cardNumber,index) {
            util.sendAjax({
                url:"#(ctxPath)/fina/cardmanager/findCardInfo",
                type:'post',
                data:{'cardNumber':cardNumber},
                notice:false,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        var data=returnData.data;
                        if(data!=undefined){
                            $("#memberName-"+index).val(data.memberName);
                            $("#idcard-"+index).val(data.idcard);
                            $("#cardId-"+index).val(data.id);
                        }

                    }
                }
            });
        }

        //监听radio
        form.on('radio',function (obj) {
            var name=obj.elem.name;
            var filter=name.substring(name.indexOf(".")+1);
            var index=name.substring(name.indexOf("[")+1,name.indexOf("]"));
            if("type"==filter){
                if(obj.elem.value==='1'){
                    var inputObj=$("#rechargeTypeInput-"+index);
                    inputObj.attr("name","recharge-["+index+"].amount");
                    inputObj.attr("placeholder","充值金额");
                    inputObj.attr("lay-verify","required|checkAmount|maxAmount");

                    /*var givInputObj=$("#givInput-"+index);
                    givInputObj.attr("name","recharge-["+index+"].giveAmount");
                    givInputObj.attr("placeholder","赠送金额");
                    givInputObj.attr("lay-verify","required|checkAmount|maxAmount");*/
                }else if(obj.elem.value==='2'){
                    var inputObj=$("#rechargeTypeInput-"+index);
                    inputObj.attr("name","recharge-["+index+"].consumeTimes");
                    inputObj.attr("placeholder","购买天数");
                    inputObj.attr("lay-verify","required|checkNumber|maxNumber");

                    /*var givInputObj=$("#givInput-"+index);
                    givInputObj.attr("name","recharge-["+index+"].giveConsumeTimes");
                    givInputObj.attr("placeholder","赠送天数");
                    givInputObj.attr("lay-verify","required|checkNumber|maxNumber");*/
                }else if(obj.elem.value==='3'){
                    var inputObj=$("#rechargeTypeInput-"+index);
                    inputObj.attr("name","recharge-["+index+"].consumePoints");
                    inputObj.attr("placeholder","充值点数");
                    inputObj.attr("lay-verify","required|checkNumber|maxNumber");

                    /*var givInputObj=$("#givInput-"+index);
                    givInputObj.attr("name","recharge-["+index+"].giveConsumePoints");
                    givInputObj.attr("placeholder","赠送点数");
                    givInputObj.attr("lay-verify","required|checkNumber|maxNumber");*/
                }else if(obj.elem.value==='4'){
                    var inputObj=$("#rechargeTypeInput-"+index);
                    inputObj.attr("name","recharge-["+index+"].consumeIntegral");
                    inputObj.attr("placeholder","充值积分");
                    inputObj.attr("lay-verify","required|checkNumber|maxNumber");

                    /*var givInputObj=$("#givInput-"+index);
                    givInputObj.attr("name","recharge-["+index+"].giveConsumeIntegral");
                    givInputObj.attr("placeholder","赠送积分");
                    givInputObj.attr("lay-verify","required|checkNumber|maxNumber");*/
                }
            }else if("isCash"==filter){
                var selectObj=$("#rechargeAccountId-"+index);
                if(obj.value=='0'){
                    /*selectObj.attr("lay-verify","required");
                    selectObj.prop("disabled",false);*/
                }else if(obj.value=='1'){
                    selectObj.attr("lay-verify","");
                    selectObj.prop("disabled",true);

                    $("#rechargeAccountId-"+index+" option:first").prop("selected","selected");
                }
                form.render('select');

            }else if("isIncome"==filter){
                var selectObj=$("#rechargeAccountId-"+index);
                if(obj.value=='1'){
                    /*selectObj.attr("lay-verify","required");
                    selectObj.prop("disabled",false);*/

                    $("input[name='recharge-["+index+"].isCash']").prop("disabled",false);

                    $("#cashValue-"+index).prop("disabled",false);
                    $("#accountValue-"+index).prop("disabled",false);
                }else if(obj.value=='0'){
                    selectObj.attr("lay-verify","");
                    selectObj.prop("disabled",true);
                    $("#rechargeAccountId-"+index+" option:first").prop("selected","selected");

                    $("input[name='recharge-["+index+"].isCash'][value='0']").prop("checked","checked");
                    $("input[name='recharge-["+index+"].isCash']").prop("disabled",true);

                    $("#cashValue-"+index).val("0");
                    $("#accountValue-"+index).val("0");

                    $("#cashValue-"+index).prop("disabled",true);
                    $("#accountValue-"+index).prop("disabled",true);
                }
                form.render();
            }

        });

        //自定义验证规则
        form.verify({
            checkAmount:function(value){
                if(value != null){
                    var reg1 = new RegExp("^[0-9]+\\.[0-9]{0,2}$");
                    var reg2 = new RegExp("^[0-9]*$");
                    if(!reg1.test(value) && !reg2.test(value)){
                        return "只能输入数字和小数点后两位小数";
                    }
                }
            },
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                }
            },
            maxAmount:function(value){
                if(value != null){
                    var lenght=0;
                    if(value.indexOf(".")===-1){
                        lenght=value.length;
                    }else{
                        lenght=value.substring(0,value.indexOf(".")).length;
                    }
                    if(value.length>0 && value<=0){
                        return "金额值不能小于等于0";
                    }
                    if(lenght>5){
                        return "金额最大值为99999.99";
                    }
                }
            },
            maxNumber:function (value) {
                if(value != null) {
                    if(value.length>0 && value<=0){
                        return "天数值不能小于等于0";
                    }
                    if (value.length > 4) {
                        return "天数最大值为9999";
                    }
                }
            }
        });

        cardNumberOnkeyup=function (obj,index) {
            var cardNumber=$(obj).val();
            if(cardNumber==='' ||　cardNumber===undefined){
                return false;
            }
            findCardNumber(cardNumber,index);
        }

        addRechargeTr=function(cardId,cardNumber,idcard,memberName){
            var tr=$("#rechargeTbody").children("tr");
            var flag=true;
            tr.each(function (i) {
                var id=$(this).attr("id");
                var index=id.substring(id.indexOf("-")+1,id.length);
                var trCardNumber=$("#cardNumber-"+index).val();
                if(trCardNumber===cardNumber){
                    flag=false;
                    return false;
                }
            })
            if(flag){
                laytpl(rechargeTrTpl.innerHTML).render({'idx':(Number($("#count").val())+1),'cardId':cardId,'cardNumber':cardNumber,'idcard':idcard,'memberName':memberName}, function(html){
                    $("#count").val((Number($("#count").val())+1));
                    $("#rechargeTbody").append(html);
                    form.render()
                });
            }
            return flag;
        }

        form.on('submit(saveBtn)',function (obj) {
            var tr=$("#rechargeTbody").children("tr");
            if(tr.length<1){
                layer.msg('请至少添加一个再保存', {icon: 2, offset: 'auto'});
                return false;
            }
            var flag=false;
            var cardNumber='';
            $.each(tr,function (index,item){
                var trIndex=$(item).attr("tr-index");
                var isIncome=obj.field["recharge-["+trIndex+"].isIncome"];
                var isCash=obj.field["recharge-["+trIndex+"].isCash"];
                var accountId=obj.field["recharge-["+trIndex+"].accountId"];
                cardNumber=$("#cardNumber-"+trIndex).val();

                if(isIncome=='1' && isCash=='0' && accountId==''){
                    flag=true;
                    return false;
                }
            });
            if(flag){
                layer.msg(cardNumber+'收入性充值，请至少选择一项收钱方式!', {icon: 2, offset: 'auto'});
                return false;
            }
            $("#saveBtn").attr("disabled",true);
            $("#saveBtn").addClass("layui-btn-disabled");
            util.sendAjax({
                url:"#(ctxPath)/fina/cardmanager/batchRecharge",
                type:'post',
                data:$("#rechargeForm").serialize(),
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                    }
                },
                complete :function(){
                    $("#saveBtn").attr("disabled",false);
                    $("#saveBtn").removeClass("layui-btn-disabled");
                }
            });
            return false;
        });

        onblurAmount=function(index){
            var cashValue=$("#cashValue-"+index).val();
            var accountValue=$("#accountValue-"+index).val();
            $("#payAmount-"+index).val(Number(cashValue)+Number(accountValue));
        }

        form.on('select(searchCardNumber)',function (obj) {
//             console.log(obj);
        });
        
    });

</script>
<script id="rechargeTrTpl" type="text/html">
    <tr id="tr-{{d.idx}}" tr-index="{{d.idx}}">
        <td>
            <input type="text" id="cardNumber-{{d.idx}}" name="recharge-[{{d.idx}}].cardNumber" readonly onkeyup="cardNumberOnkeyup(this,'{{d.idx}}')" value="{{d.cardNumber}}"  required  lay-verify="required" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
            <input type="hidden" id="cardId-{{d.idx}}" name="recharge-[{{d.idx}}].cardId" value="{{d.cardId}}">
        </td>
        <td>
            <input type="text" id="memberName-{{d.idx}}" name="recharge-[{{d.idx}}].memberName"  value="{{d.memberName}}" readonly  placeholder="会员姓名" autocomplete="off" class="layui-input">
        </td>
        <!--<td>
            &lt;!&ndash;<input type="text" id="idcard-{{d.idx}}" name="recharge-[{{d.idx}}].idcard" value="{{d.idcard}}" readonly placeholder="身份证号" autocomplete="off" class="layui-input">&ndash;&gt;

        </td>-->


        <td>
            <div class="layui-input-block" style="margin-left: 0px;">
                <input type="radio" lay-filter="type" name="recharge-[{{d.idx}}].type" value="1" title="金额" checked>
                <input type="radio" lay-filter="type" name="recharge-[{{d.idx}}].type" value="2" title="天数">
                <input type="radio" lay-filter="type" name="recharge-[{{d.idx}}].type" value="3" title="点数">
                <input type="radio" lay-filter="type" name="recharge-[{{d.idx}}].type" value="4" title="积分">
            </div>
        </td>

        <td>
            <select name="recharge-[{{d.idx}}].classify"  lay-verify="required">
                <option value="">请选择分类</option>
                <option value="money_recharge">金额充值</option>
                <option value="upgrade_recharge">升级充值</option>
                <option value="give_recharge">赠送充值</option>
                <option value="luck_draw_recharge">抽奖充值</option>
                <option value="paper_card_recharge">纸卡充值</option>
            </select>
        </td>

        <td>
            <input type="text"  name="recharge-[{{d.idx}}].rechargeNo" required  lay-verify="required" placeholder="充值编号" autocomplete="off" class="layui-input">
        </td>
        <td>
            <div class="layui-input-block" style="margin-left: 0px;">
                <input type="radio" lay-filter="isIncome" name="recharge-[{{d.idx}}].isIncome" value="1" title="收入性" checked>
                <input type="radio" lay-filter="isIncome" name="recharge-[{{d.idx}}].isIncome" value="0" title="非收入性">
            </div>
        </td>
        <td>
            <div class="layui-input-block" style="margin-left: 0px;">
                <input type="radio" lay-filter="isCash" name="recharge-[{{d.idx}}].isCash" value="0" title="否" checked>
                <input type="radio" lay-filter="isCash" name="recharge-[{{d.idx}}].isCash" value="1" title="是">
            </div>
        </td>
        <td>
            <input type="text" id="cashValue-{{d.idx}}" onblur="onblurAmount({{d.idx}})" name="recharge-[{{d.idx}}].cashValue" lay-verify="required|checkAmount" value="0" placeholder="现金收款金额" autocomplete="off" class="layui-input" >
        </td>
        <td>
            <div class="layui-input-block" style="margin-left: 0px;">
                <select name="recharge-[{{d.idx}}].accountId" id="rechargeAccountId-{{d.idx}}" lay-verify="">
                    <option value="">请选择收钱账号</option>
                    #for(account : accountList)
                        #if(account.payWay??=="1")
                        #set(payWay = "(现金)")
                        #else if(account.payWay??=="2")
                        #set(payWay = "(微信)")
                        #else if(account.payWay??=="3")
                        #set(payWay = "(支付宝)")
                        #else if(account.payWay??=="4")
                        #set(payWay = "(信用卡)")
                        #else if(account.payWay??=="5")
                        #set(payWay = "(会员卡)")
                        #else if(account.payWay??=="6")
                        #set(payWay = "(Pos机)")
                        #else if(account.payWay??=="7")
                        #set(payWay = "(转账)")
                        #else if(account.payWay??=="8")
                        #set(payWay = "(企业微信)")
                        #end
                        <option value="#(account.id)" >#(payWay)#(account.accountName)</option>
                    #end
                </select>
            </div>
        </td>
        <td>
            <input type="text" id="accountValue-{{d.idx}}" onblur="onblurAmount({{d.idx}})" name="recharge-[{{d.idx}}].accountValue" lay-verify="required|checkAmount" value="0" placeholder="账户收款金额" autocomplete="off" class="layui-input" >
        </td>
        <td>
            <input type="text" id="payAmount-{{d.idx}}" name="recharge-[{{d.idx}}].payAmount" lay-verify="required|checkAmount" value="0" placeholder="总缴款金额" autocomplete="off" class="layui-input" >
        </td>
        <td>
            <input type="text" id="rechargeTypeInput-{{d.idx}}" name="recharge-[{{d.idx}}].amount" required  lay-verify="required|checkAmount|maxAmount" placeholder="充值金额" autocomplete="off" class="layui-input">
        </td>

        <!--<td>
            <input type="text" id="givInput-{{d.idx}}" name="recharge-[{{d.idx}}].giveAmount"  lay-verify="checkAmount" placeholder="赠送金额" value="0" autocomplete="off" class="layui-input">
        </td>-->
        <!--<td>
            <input type="text" name="recharge-[{{d.idx}}].giveConsumeTimes"  lay-verify="checkNumber|maxNumber" placeholder="赠送天数" autocomplete="off" class="layui-input">
        </td>-->
        <td>
            <input type="text" name="recharge-[{{d.idx}}].describe" placeholder="请输入说明" autocomplete="off" class="layui-input">
        </td>
        <td>
            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="del('{{d.idx}}')" >删除</button>
        </td>
    </tr>
</script>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" id="rechargeForm" style="margin-top: 50px;">
        <!--<div class="layui-row" style="text-align: center">
            <div class="layui-input-inline" style="width: 300px;">
                <select id="searchCardNumber" lay-filter="searchCardNumber" lay-verify="" lay-search>

                </select>
            </div>
            <button class="layui-btn" type="button" id="search"  >搜索</button>
        </div>-->
        <input id="count" name="count" type="hidden" value="0">
        <table class="layui-table">
            <thead>
                <tr>
                    <th width="8%">会员卡号</th>
                    <th width="6%">姓名</th>
                    <th width="5%">充值类型</th>
                    <th width="6%">充值分类</th>
                    <th width="10%">充值编号</th>
                    <th width="7%">收入性</th>
                    <th width="6%">是否收现金</th>
                    <th width="6%">现金收款金额</th>
                    <th width="13%">收钱账户</th>
                    <th width="6%">账户收款金额</th>
                    <th width="6%">总收款金额</th>
                    <th width="6%">金额/天数/点数/积分</th>
                    <!--<th width="6%">赠送</th>-->
                    <th width="11%">说明</th>
                    <th width="4%">操作</th>
                </tr>
            </thead>
            <tbody id="rechargeTbody">
                
            </tbody>
        </table>
        <div class="layui-form-footer" style="padding-bottom: 20px;">
            <div class="pull-right">
                <button type="button" class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button type="button" id="choice" class="layui-btn">选择会员卡</button>
                <button type="button" id="importBtn" class="layui-btn">模板导入</button>
                <button class="layui-btn" id="saveBtn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
#end