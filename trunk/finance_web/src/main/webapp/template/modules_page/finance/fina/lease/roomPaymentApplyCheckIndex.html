#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
<style>
    .layui-tab  td, .layui-tab th {
        padding: 9px 5px;
    }

    .layui-table-cell {
        padding: 0 5px;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
        <div class="layui-row">

            <div class="layui-inline">
                <label class="layui-form-label" style="width: 60px;">基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="baseId" lay-filter="baseId">
                        #for(base : baseList)
                        <option value="#(base.id??)" submitUserId="#(base.lease_submit_user_id??)" checkUserId="#(base.lease_check_user_id??)" approveUserId="#(base.lease_approve_user_id??)" >#(base.base_name??)</option>
                        #end
                    </select>
                </div>
            </div>


            <div class="layui-input-inline">
                <label class="layui-form-label">付款单状态</label>
                <div class="layui-input-inline" style="float: left;margin-right: 0px;" >
                    <div id="typeSelect" style="width: 350px;"></div>
                </div>
            </div>
            <input id="userId" value="#(userId??)" type="hidden">
            <div class="layui-input-inline">
                <label class="layui-form-label"><font color="red">*</font>业主姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="name" id="name"  autocomplete="off" value="" class="layui-input">
                </div>
            </div>
            <div class="layui-inline">
                <button type="button" id="searchConsumeBtn" class="layui-btn">搜索</button>
            </div>
            <div class="layui-inline">
                <!--<button type="button" id="submitPayApplyBtn" class="layui-btn">提交付款申请</button>-->
            </div>
        </div>
    </form>
    <div class="layui-row">
        <table id="cardTypeTable" lay-filter="cardTypeTable"></table>
    </div>
</div>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">


        {{#
        let submituserid=$("#baseId option:selected").attr("submituserid");
        let checkUserId=$("#baseId option:selected").attr("checkuserid");
        let approveuserid=$("#baseId option:selected").attr("approveuserid");
        let userId=$("#userId").val();
        }}

        #[[
        {{#if(approveuserid==userId && d.status=='2'){}}
        <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
        {{#}else{}}
        <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="view">查看</a>
        {{#}}}
        ]]#
        #[[
        {{#if(checkUserId==userId && d.status=='1'){}}
        <a class="layui-btn layui-btn-xs" lay-event="check">审核</a>

        {{#}}}
        ]]#
        #[[
        {{#if(approveuserid==userId && d.status=='2'){}}
        <a class="layui-btn layui-btn-xs" lay-event="approve">审批</a>

        {{#}}}
        ]]#
        #[[
        {{#if(d.status=='1' && submituserid==userId){}}
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="revocation">撤回</a>
        {{#}}}
        ]]#
        <a class="layui-btn layui-btn-xs" lay-event="export">导出</a>
    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate','layer'],function(){
        var table = layui.table
            , $ = layui.$
            ,layer=layui.layer
        ;
        var laydate=layui.laydate;
        laydate.render({
            elem: '#dateRange'
            ,trigger:'click'
            ,range: true
            ,value:"#(dateRange??)"
        });
        /*laydate.render({
            elem: '#roomEndDate'
            ,trigger:'click'
            ,range: true
        });*/

        var typeSelect = xmSelect.render({
            el: '#typeSelect',
            language: 'zn',
            data: [
                {name: '已提交', value: "1",selected: true},
                {name: '处理中', value: "2",selected: true},
                {name: '已完成', value: "3",selected: true},
                {name: '已撤回', value: "4",selected: true}
            ]
        })


        cardTypeTableLoad=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTable',
                elem:"#cardTypeTable",
                url:'#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyPageList',
                where:data,
                height:'full-90',
                cols:[[
                    {type:'checkbox'}
                    ,{type:'numbers',title:'序号',width:70,unresize:true}
                    ,{field: 'base_name', title: '基地' ,unresize:true}
                    ,{field:'title', title:'付款单标题' , unresize:true, align:'center'}
                    ,{field:'num', title:'数量'  , unresize:true, align:'center'}
                    ,{field:'totalAmount', title:'本期合计'  , unresize:true, align:'center'}
                    ,{field:'apply_time', title:'提交时间'  , unresize:true, align:'center'}
                    ,{field:'submitName', title:'提交人'  , unresize:true, align:'center'}
                    ,{field:'currName', title:'当前待处理人'  , unresize:true, align:'center'}
                    ,{field:'remark',title:'备注',unresize:true}
                    ,{field:'status', title:'状态'  , unresize:true, align:'center',templet:function (d) {
                            if(d.status=='1'){
                                return '已提交'
                            }else if(d.status=='2'){
                                return '处理中';
                            }else if(d.status=='3'){
                                return '已完成';
                            }else if(d.status=='4'){
                                return '已撤回';
                            }
                        }}
                    ,{title:'操作',width:220, fixed:'right',unresize:true,toolbar:'#toolBar'}
                ]],
                page:true,
                limit:10
                , done: function (res, curr, count) {

                    layer.close(loadingIndex);
                }
            });
            table.on('tool(cardTypeTable)',function(obj) {
                if(obj.event==='view'){
                    layerShow('详情','#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetailIndex?id='+obj.data.id+"&type=view",'100%','100%');
                }else if(obj.event === 'detail'){
                    layerShow('详情','#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetailIndex?id='+obj.data.id,'100%','100%');
                }else if(obj.event==='check') {
                    /*layerShow('付款单','#(ctxPath)/fina/leaseRecordApply/previewTableIndex?leaseId='+obj.data.leaseId+"&roomId="+obj.data.roomId,1000,600);*/
                    layer.confirm('您确定要审批吗？', {icon: 3, title: '提示'}, function (index) {
                        //审批
                        util.sendAjax({
                            type: 'POST',
                            url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApplyStatus',
                            data: {id: obj.data.id, status: '2'},
                            loadFlag: true,
                            success: function (rep) {
                                if (rep.state == 'ok') {
                                    reloadCardTypeTable();
                                }
                            },
                            complete: function () {
                            }
                        });

                        layer.close(index);
                    });

                }else if(obj.event==='approve'){

                    layer.confirm('您确定要审核吗？', {icon: 3, title: '提示'}, function (index) {

                        //先判断表单内容是否填完
                        $.post('#(ctxPath)/fina/leaseRecordApply/checkRoomPaymentApplyStatus?id=' + obj.data.id, {}, function (res) {
                            if (res.state == 'ok') {
                                //完成审核
                                util.sendAjax({
                                    type: 'POST',
                                    url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApplyStatus',
                                    data: {id: obj.data.id, status: '3'},
                                    loadFlag: true,
                                    success: function (rep) {
                                        if (rep.state == 'ok') {
                                            reloadCardTypeTable();
                                        }
                                    },
                                    complete: function () {
                                    }
                                });
                            } else {
                                layer.msg(res.msg, {icon: 2, offset: 'auto'});
                                return false;
                            }
                        });


                        layer.close(index);
                    });
                }else if(obj.event==='revocation'){
                    //撤回
                    layer.confirm('您确定要撤回吗？', {icon: 3, title: '提示'}, function (index) {
                        //审批
                        util.sendAjax({
                            type: 'POST',
                            url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApplyStatus',
                            data: {id: obj.data.id, status: '4'},
                            loadFlag: true,
                            success: function (rep) {
                                if (rep.state == 'ok') {
                                    reloadCardTypeTable();
                                }
                            },
                            complete: function () {
                            }
                        });

                        layer.close(index);
                    });
                }else if(obj.event==='export'){
                    layer.confirm('您确定要导出吗？', {icon: 3, title: '提示'}, function (index) {
                        window.location.href="#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetaiListExport?id="+obj.data.id;
                        layer.close(index);
                    })

                }
            });
        }

        reloadCardTypeTable=function(){
            var typeSelectIds=[];
            var array=typeSelect.getValue();
            $.each(array,function (index,item) {
                typeSelectIds.push(item.value)
            })
            cardTypeTableLoad({contractStatus:$('#contractStatus').val(),baseId:$('#baseId').val(),roomName:$('#roomName').val()
                ,name:$('#name').val(),dateRange:$("#dateRange").val(),'status':JSON.stringify(typeSelectIds),'name':$("#name").val()});
        }

        reloadCardTypeTable();

        // 搜索消费记录按钮
        $('#searchConsumeBtn').on('click', function(){
            reloadCardTypeTable();
        });

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#searchConsumeBtn').click();
                return false
            }
        }

        $("#submitPayApplyBtn").click(function () {
            var checkStatus = table.checkStatus('cardTypeTable');
            if(checkStatus.data.length==0){
                layer.msg('勾选的条数不能为0!',{icon:5});
                return false;
            }

            let dataIds=[];
            $.each(checkStatus.data,function (index,item) {
                dataIds.push(item.id);
            });
            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "查看详情",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['500px;','500px;'],
                content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                    "            <div class=\"layui-form-item\" style='margin-top: 20px;'>\n" +
                    "                <label class=\"layui-form-label\"><font color='red'>*</font>标题</label>\n" +
                    "                <div class=\"layui-input-block\" >\n" +
                    "                    <input class=\"layui-input\" placeholder=\"请输入标题\" value='' name=\"title\" id=\"title\" autocomplete=\"off\">\n" +
                    "                </div>\n" +
                    "            </div>\n" +
                    "\n" +
                    "            <div class=\"layui-form-item\">\n" +
                    "                <label class=\"layui-form-label\"><font color='red'></font>备注</label>\n" +
                    "                <div class=\"layui-input-block\"  >" +
                    "                   <textarea placeholder=\"请输入内容\" class=\"layui-textarea\" name='remark'></textarea>" +
                    "                </div>\n" +
                    "            </div>" +
                    "</form>",
                cancel: function(){
                },
                end : function(){
                },yes: function(index, layero){

                    layer.load();
                    if($("#title").val()==''){
                        layer.msg('标题必须填写', {icon: 2, offset: 'auto'});
                        return false;
                    }
                    let data={"title":$("#title").val(),"remark":$("#remark").val(),"dataIds":JSON.stringify(dataIds)};
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApply',
                        data: data,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                //pageTableReload();
                                reloadCardTypeTable();
                                layer.closeAll("loading");
                                layer.close(index);
                            }
                        },
                        complete : function() {
                        }
                    });
                    return false;
                }

            });


        })


    });
</script>

#end