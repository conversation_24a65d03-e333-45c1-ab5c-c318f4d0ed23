#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()退款报表#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="searchForm" class="layui-form layui-form-pane" action="">
	    	<div class="layui-inline">
            	<label class="layui-form-label">查询类型:</label>
	            <div class="layui-input-inline">
	                <select id="queryType" name="queryType" lay-filter="queryType">
	                    <option value="day">按天</option>
	                    <option value="month">按月</option>
	                    <option value="year">按年</option>
	                </select>
	            </div>
            </div>
			<div id="yearMonthDayDiv" class="layui-inline">
				<label class="layui-form-label">日期:</label>
				<div class="layui-input-inline">
					<input id="yearMonthDay" name="yearMonthDay" class="layui-input" value="#(yearMonthDay)" placeholder="">
				</div>
			</div>
	    	<div id="yearMonthDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">月份:</label>
				<div class="layui-input-inline">
					<input id="yearMonth" name="yearMonth" class="layui-input" value="#(yearMonth)" placeholder="">
				</div>
			</div>
	    	<div id="yearDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">年份:</label>
				<div class="layui-input-inline">
					<input id="year" name="year" class="layui-input" value="#(year)" placeholder="">
				</div>
			</div>
	        <div class="layui-inline">
	        	<div class="layui-input-inline">
	        		<div class="layui-btn-group">
						<input type="hidden" id="branchOfficeName" value="">
				        <button type="button" id="searchRechargeBtn" class="layui-btn">搜索</button>
				        #shiroHasPermission("recharge:statistics:export")
<!--					        <button type="button" id="export" class="layui-btn">导出</button>-->
			            #end
	        		</div>
	        	</div>
    		</div>
		</form>
	</div>
	<div class="layui-row layui-col-space10">
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
			<table class="layui-table">
				<colgroup>
					<col width="20%">
					<col width="20%">
					<col width="20%">
					<col width="20%">
					<col width="20%">
					<col>
				</colgroup>
				<thead>
				<tr>
					<th>销售部门</th>
					<th>合同款</th>
					<th>违约金</th>
					<th>赠送金额</th>
					<th>实退金额</th>
				</tr>
				</thead>
				<tbody id="refundStatistics"></tbody>
			</table>
		</div>
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
			<table id="refundListTable" lay-filter="refundListTable"></table>
		</div>
	</div>
</div>
#end

#define js()
<script id="refundStatisticsTrTpl" type="text/html">
	<tr onclick="pageTableReload('{{d.branchOfficeName}}')">
		<td>{{d.branchOfficeName}}</td>
		<td>{{d.contractMoney}}</td>
		<td>{{d.deductMoney}}</td>
		<td>{{d.giveBalance}}</td>
		<td>{{d.refundAmount}}</td>
	</tr>
</script>
<script type="text/javascript">
    layui.use(['table','form', 'laytpl','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , laytpl = layui.laytpl
        , $ = layui.$
        , laydate = layui.laydate
		;
        
        laydate.render({
            elem: '#yearMonthDay'
            ,trigger:'click'
        });
        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
        });
        laydate.render({
            elem: '#year'
            ,type: 'year'
            ,trigger:'click'
        });
        
        form.on('select(queryType)', function(data){
        	var queryType = data.value;
        	if(queryType=='day'){
        		$("#yearDiv").hide();
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").show();
        	}else if(queryType=='month'){
        		$("#yearDiv").hide();
        		$("#yearMonthDiv").show();
        		$("#yearMonthDayDiv").hide();
        	}else{
        		$("#yearDiv").show();
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").hide();
        	}
       	});
        
		addTpl = function (targetId, addTpl, branchOfficeName, contractMoney, deductMoney, giveBalance, refundAmount) {
			laytpl(addTpl).render({
				'branchOfficeName': branchOfficeName,
				'contractMoney': contractMoney,
				'deductMoney': deductMoney,
				'giveBalance': giveBalance,
				'refundAmount': refundAmount
			}, function (html) {
				targetId.append(html);
			});
		};
        
        refundStatisticsLoad = function () {
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/reportForm/refundStatistics',
                data: {queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()},
                notice: false,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
						$('#refundStatistics').empty();
                    	var totalContractMoney = 0;
                    	var totalDeductMoney = 0;
                    	var totalGiveBalance = 0;
                    	var totalRefundAmount = 0;
						$.each(rep.data, function (i, r) {
							totalContractMoney += parseFloat(r.contractMoney).toFixed(2);
							totalDeductMoney += parseFloat(r.deductMoney).toFixed(2);
							totalGiveBalance += parseFloat(r.giveBalance).toFixed(2);
							totalRefundAmount += parseFloat(r.refundAmount).toFixed(2);
							addTpl($('#refundStatistics'), refundStatisticsTrTpl.innerHTML, r.branchOfficeName, r.contractMoney, r.deductMoney, r.giveBalance, r.refundAmount);
						});
						var html = '';
						html+='<tr>';
						html+='<td>合计</td>';
						html+='<td>'+totalContractMoney+'</td>';
						html+='<td>'+totalDeductMoney+'</td>';
						html+='<td>'+totalGiveBalance+'</td>';
						html+='<td>'+totalRefundAmount+'</td>';
						html+='</tr>';
                    }
                },
                complete : function() {
                }
            });
		}
		
        refundStatisticsLoad();

		pageTableReload = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'refundListTable',
				elem : "#refundListTable" ,
				url : '#(ctxPath)/fina/reportForm/refundListPage' ,
				where: data,
				height:'full-200',
				cols : [[
					{field:'branchOfficeName',title:'销售部',width:120,unresize:false},
					{field:'payTime',title:'退款日期',width:120,unresize:false},
					{field:'receiptNumber',title:'收据号',width:120,unresize:false},
					{field:'summary',title:'摘要',width:120,unresize:false},
					{field:'saleName',title:'业务员',width:120,unresize:false},
					{field:'memberName',title:'客户名',width:120,unresize:false},
					{field:'contractType',title:'合同类型',width:120,unresize:false},
					{field:'contractNumber',title:'合同编号',width:120,unresize:false},
					{field:'cardNumber',title:'会员卡号',width:120,unresize:false},
					{field:'unit',title:'单位',width:120,unresize:false},
					{field:'accountBalance',title:'帐面余额',width:90,unresize:false},
					{field:'consumeTimes',title:'剩余天数',width:90,unresize:false},
					{field:'deductMoney',title:'违约金',width:90,unresize:false},
					{field:'giveBalance',title:'赠送金额',width:90,unresize:false},
					{field:'refundAmount',title:'实退金额',width:90,unresize:false},
					{field:'payBy',title:'退款人',width:90,unresize:false}
				]] ,
				page : true,
				limit : 10,
				done:function () {
					layer.close(loadingIndex);
				}
			});
		}

		pageTableReload({queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()});

        // 搜索消费记录按钮
        $('#searchRechargeBtn').on('click', function(){
        	refundStatisticsLoad();
        	pageTableReload({queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val(),branchOfficeName:''});
        });

        $("#export").click(function () {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            var url='#(ctxPath)/fina/recharge/rechargeStatisticsExport?queryType='+$('#queryType').val()+"&yearMonthDay="+$('#yearMonthDay').val()+"&yearMonth="+$('#yearMonth').val()+"&year="+$('#year').val();
            window.location.href=url;
            setTimeout(function(){
				layer.close(loadingIndex);
            },5000);
        });
    });

</script>
#end