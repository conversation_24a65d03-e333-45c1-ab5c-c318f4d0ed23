#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅居入住违约账单管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
            <div class="layui-row">
            	<div class="layui-inline">
	            	<label class="layui-form-label">入住基地</label>
		            <div class="layui-input-inline">
		                <select name="baseId" lay-search>
	                        <option value="">请选择入住基地</option>
	                        #for(b : baseList)
	                        <option value="#(b.id)">#(b.baseName)</option>
	                        #end
	                    </select>
		            </div>
            	</div>
            	<div class="layui-inline">
	            	<label class="layui-form-label">持卡人</label>
		            <div class="layui-input-inline">
		                <input type="text" name="fullName" class="layui-input" placeholder="请输入持卡人姓名" autocomplete="off">
		            </div>
            	</div>
            	<div class="layui-inline">
	            	<label class="layui-form-label">入住号</label>
		            <div class="layui-input-inline">
		                <input type="text" name="checkinNo" class="layui-input" placeholder="请输入入住号" autocomplete="off">
		            </div>
            	</div>
            	<div class="layui-inline">
	            	<label class="layui-form-label">处罚类型</label>
		            <div class="layui-input-inline">
		                <select name="punishType" lay-search>
	                        <option value="">请选择处罚类型</option>
	                        #for(d : dictList)
	                        	<option value="#(d.dictValue)">#(d.dictName)</option>
	                        #end
	                    </select>
		            </div>
            	</div>
            	<div class="layui-inline">
	            	<label class="layui-form-label">处理状态</label>
		            <div class="layui-input-inline">
		                <select name="status" lay-search>
	                        <option value="">请选择处理状态</option>
	                        <option value="0">未处理</option>
	                        <option value="1">已处理</option>
	                        <option value="2">作废</option>
	                    </select>
		            </div>
            	</div>
            	<div class="layui-inline">
	            	<label class="layui-form-label">入住人</label>
		            <div class="layui-input-inline">
		                <input type="text" name="name" class="layui-input" placeholder="请输入入住人姓名" autocomplete="off">
		            </div>
            	</div>
            	<div class="layui-inline">
	            	<label class="layui-form-label">预订号</label>
		            <div class="layui-input-inline">
		                <input type="text" name="bookNo" class="layui-input" placeholder="请输入预订号" autocomplete="off">
		            </div>
            	</div>
            	<div class="layui-inline">
	                <button type="button" id="search" class="layui-btn" lay-submit="" lay-filter="search">查询</button>
            	</div>
            </div>
        </form>
    <div class="layui-row">
        <table id="prTable" lay-filter="prTable"></table>
    </div>
</div>
#getDictLabel("punish_type")
#end
<!-- 公共JS文件 -->
#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        prLoad = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            table.render({
                id : 'prTable'
                ,elem : '#prTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-130'
                ,url : '#(ctxPath)/fina/punishRecord/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'checkinNo', title: '入住人-预订号-入住号', align: 'center', unresize: false,width:220,style:'font-size:10px;',templet:function (d) {
                            var str="";
                            if(d.name != null && d.name != null){
                                str = d.name;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.bookNo != null && d.bookNo != ''){
                                str += d.bookNo;
                            }else{
                                str += "--";
                            }
                            str += " ";
                            if(d.checkinNo != null && d.checkinNo != ''){
                                str += d.checkinNo;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'fullName', title: '会员卡信息', align: 'center', unresize: false,width:180,templet:function (d) {
                            var str="";
                            if(d.cardNumber != null && d.cardNumber != ''){
                                str = d.cardNumber;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.fullName != null && d.fullName != null){
                                str += d.fullName;
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'baseName', title: '基地', align: 'center', unresize: false,width:150}
                    ,{field:'punishType', title: '处罚类型', align: 'center', unresize: false,width:120,templet:"<div>{{d.punishType == 'cancel_book'?'<span class='layui-badge layui-bg-green'>取消预订</span>':d.punishType == 'delay_checkin'?'<span class='layui-badge layui-bg-orange'>推迟入住</span>':d.punishType == 'advance_checkout'?'<span class='layui-badge layui-bg-blue'>提前退住</span>':'- -'}}</div>"}
                    ,{field:'bookCreateTime', title: '预订(创建-取消)时间', align: 'center', unresize: false,width:220,style:'font-size:10px;',templet:function (d) {
                            var str="";
                            if(d.bookCreateTime != null && d.bookCreateTime != ''){
                                str = dateFormat(d.bookCreateTime,'yyyy-MM-dd HH:mm:ss');
                            }else{
                                str = "--";
                            }
                            str += " - ";
                            if(d.bookCancelTime != null && d.bookCancelTime != ''){
                                str += dateFormat(d.bookCancelTime,'yyyy-MM-dd HH:mm:ss');
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'planCheckinTime', title: '入住(计划-实际)时间', align: 'center', unresize: false,width:220,style:'font-size:10px;',templet:function (d) {
                            var str="";
                            if(d.planCheckinTime != null && d.planCheckinTime != ''){
                                str = dateFormat(d.planCheckinTime,'yyyy-MM-dd HH:mm:ss');
                            }else{
                                str = "--";
                            }
                            str += " - ";
                            if(d.actualCheckinTime != null && d.actualCheckinTime != ''){
                                str += dateFormat(d.actualCheckinTime,'yyyy-MM-dd HH:mm:ss');
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'planCheckoutTime', title: '退住(计划-实际)时间', align: 'center', unresize: false,width:220,style:'font-size:10px;',templet:function (d) {
                            var str="";
                            if(d.planCheckoutTime != null && d.planCheckoutTime != ''){
                                str = dateFormat(d.planCheckoutTime,'yyyy-MM-dd HH:mm:ss');
                            }else{
                                str = "--";
                            }
                            str += " - ";
                            if(d.actualCheckoutTime != null && d.actualCheckoutTime != ''){
                                str += dateFormat(d.actualCheckoutTime,'yyyy-MM-dd HH:mm:ss');
                            }else{
                                str += "--";
                            }
                            return str;
                        }}
                    ,{field:'times', title: '处罚天数', align: 'center', unresize: false,width:110,templet: "<div>{{ d.times?d.times + '天':'- -' }}</div>"}
                    ,{field:'amount', title: '处罚金额', align: 'center', unresize: false,width:110,templet: "<div>{{ d.amount?d.amount + '元':'- -' }}</div>"}
                    ,{field:'points', title: '处罚点数', align: 'center', unresize: false,width:110,templet: "<div>{{ d.points?d.points + '点':'- -' }}</div>"}
                    ,{field:'status', title: '处理状态', align: 'center', unresize: true,width:100,templet:"<div>{{d.status == '0'?'<span class='layui-badge layui-bg-green'>未处理</span>':d.status == '1'?'<span class='layui-badge'>已结算</span>':d.status == '2'?'<span class='layui-badge layui-bg-cyan'>作废</span>':'- -'}}</div>"}
                    ,{field:'takeTime', title: '处理时间', sort: true, align: 'center', unresize: true,width:160,templet:"<div>{{ dateFormat(d.takeTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,limit : 10
                ,limits : [20,30,40,50]
                ,done:function (res,curr,count) {
                    $.each(res.data,function (index,item) {
                        if(item.status==='2'){
                            $(".layui-table-body").find("table").find("tr:eq("+index+")").attr("style","background-color:#ffe3e4;");
                        }
                    });
					layer.close(loadingIndex);
                }
            });
            table.on('tool(prTable)',function(obj){
                if (obj.event === 'del') {
                    layer.confirm("确定要作废吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/fina/punishRecord/delete',
                            data: {id:obj.data.id},
                            notice:true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
									prLoad(null);
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });
                }else if(obj.event === 'detail'){
                    var url = "#(ctxPath)/fina/punishRecord/form?id=" + obj.data.id + "&type=look" ;
                    layerShow("旅居违约详情",url,800,650);
                }else if(obj.event === 'settle'){
                    var url = "#(ctxPath)/fina/punishRecord/form?id=" + obj.data.id +"&type=settle";
                    layerShow("旅居违约结算",url,800,650);
                }
            });
        };
        
        prLoad(null);

        sd=form.on("submit(search)",function(data){
            prLoad(data.field);
            return false;
        });
        
      	//回车事件
		document.onkeydown = function(e){
			var ev =document.all ? window.event:e;  
			if(ev.keyCode==13) {
				$('#search').click();
				return false
			}
		}
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("finance:punishSettle:detail")
    <a class="layui-btn layui-btn-xs layui-bg-orange" lay-event="detail">详情</a>
    #end
    #shiroHasPermission("finance:punishSettle:settle")
    #[[
    {{#if(d.status != '1' && d.status != '2'){}}
    <a class="layui-btn layui-btn-xs" lay-event="settle">结算</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("finance:punishSettle:delete")
    #[[
    {{#if(d.status != '1' && d.status != '2'){}}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#
    #end
</script>
#end