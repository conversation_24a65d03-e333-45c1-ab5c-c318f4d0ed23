#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()消费状况页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<table id="memberConsumeTable" lay-filter="memberConsumeTable"></table>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:delBtn")
	#end
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
</div>
</script>
<script type="text/javascript">
layui.use([ 'table', 'form' ], function() {
	var table = layui.table
	, form = layui.form
	, layer=layui.layer
	, $ = layui.jquery
	;
	
	function consumeLoad(data){
		//loading层
		var loadingIndex = layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		table.render({
			id : 'memberConsumeTable'
			,elem : '#memberConsumeTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/fina/member/consumePage'
// 			,cellMinWidth: 80
			,height:'full-150'
			,cols: [[
				{field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
				,{field:'consumName', title: '消费人员', align: 'center', unresize: true}
				,{field:'peopleNum', title: '人数', align: 'center', unresize: true}
				,{field:'consumYear', title: '年度', align: 'center', unresize: true}
				,{field:'actualTimes', title: '消费天数', align: 'center', unresize: true}
				,{field:'actualAmount', title: '消费金额', align: 'center', unresize: true}
				,{field:'actualIntegrals', title: '消费积分', align: 'center', unresize: true}
				,{field:'baseName', title: '基地', align: 'center', unresize: true}
// 				,{fixed:'right', title: '操作', width:120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 15
			,limits : [15,25,35,45]
		    , done: function (res, curr, count) {
				layer.close(loadingIndex);
		    }
		});
	};

	consumeLoad({memberId:'#(memberId??)'});
    
});
</script>
#end