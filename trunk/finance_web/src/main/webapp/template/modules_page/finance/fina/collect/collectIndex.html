#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()收款记录页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
			<div class="layui-inline">
				<label class="layui-form-label">客户姓名</label>
				<div class="layui-input-inline">
					<input type="text" id="customerName" name="customerName" class="layui-input" placeholder="请输入客户姓名" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">客户身份证</label>
				<div class="layui-input-inline">
					<input type="text" id="customerIdcard" name="customerIdcard" class="layui-input" placeholder="请输入客户身份证" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">分公司</label>
				<div class="layui-input-inline">
					<select id="branchOfficeId" name="branchOfficeId" lay-search>
						<option value="">请选择分公司</option>
						#for(b : branchOfficeList)
							<option value="#(b.id)">#(b.shortName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-btn-group">
					<button type="button" id="search" class="layui-btn" lay-filter="search" lay-submit="">搜索</button>
					#shiroHasPermission("finance:collect:addBtn")
						<button type="button" id="add" class="layui-btn">添加</button>
					#end
				</div>
			</div>
		</form>
	</div>
	<div class="layui-row">
		<table id="collectTable" lay-filter="collectTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("finance:collect:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("finance:collect:deleteBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		function collectLoad(data){
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'collectTable'
				,elem : '#collectTable'
				,method : 'POST'
				,url : '#(ctxPath)/fina/collect/pageTable'
				,where : data
				,height:'full-90'
				,cols: [[
					{type: 'numbers', width:100, title: '序号',unresize:true}
					,{field:'', title: '收款日期', align: 'center',width:180, unresize: true, sort: true,templet:"<div>{{ dateFormat(d.collectDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{field:'', title: '收费类型', align: 'center',width:120, unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.CollectType::me(), "collectType")'}
					,{field:'collectAccountName', title: '收款账号', align: 'center',width:200, unresize: true}
					,{field:'', title: '收款方式', align: 'center',width:120, unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.PayWay::me(), "collectWay")'}
					,{field:'collectAmount', title: '收款金额', align: 'center',width:120, unresize: true}
					,{field:'fundStatus', title: '资金状态', align: 'center',width:120, unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.FundStatus::me(), "fundStatus")'}
					,{field:'receiptNumber', title: '收据编号', align: 'center',width:120, unresize: true}
					,{field:'summary', title: '摘要', align: 'center',width:120, unresize: true}
					,{field:'branchOfficeName', title: '分公司', align: 'center',width:120, unresize: true}
					,{field:'salesName', title: '销售人员', align: 'center',width:120, unresize: true}
					,{field:'customerName', title: '客户姓名', align: 'center',width:120, unresize: true}
					,{field:'customerIdcard', title: '客户身份证', align: 'center',width:180, unresize: true}
					,{field:'remark', title: '备注', align: 'center', width: 300,unresize: true}
					,{field:'', title: '创建时间', align: 'center',width:180, unresize: true, sort: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', align: 'center', width: 100, unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 10
				,limits : [10,20,30,40]
				, done: function (res, curr, count) {
					layer.close(loadingIndex);
				}
			});
			table.on('tool(collectTable)',function(obj){
				if(obj.event === 'edit'){
					var url = "#(ctxPath)/fina/collect/edit?id=" + obj.data.id ;
					pop_show("编辑",url,null,null);
				}else if (obj.event === 'del') {
					layer.confirm("确定要作废吗?",function(index){
						util.sendAjax ({
							type: 'POST',
							url: '#(ctxPath)/fina/collect/del',
							notice: true,
							data: {id:obj.data.id,delFlag:'1'},
							loadFlag: true,
							success : function(rep){
								if(rep.state=='ok'){
									tableReload('collectTable',null);
								}
								layer.close(index);
							},
							complete : function() {
							}
						});
					});
				}
			});
		};
		
		collectLoad(null);

		form.on("submit(search)",function(data){
			collectLoad(data.field);
			return false;
		});

		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/fina/collect/add" ;
			pop_show("新增",url,null,null);
		});

	});
</script>
#end