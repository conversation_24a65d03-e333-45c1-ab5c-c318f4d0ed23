#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地扣卡详细汇总表#end

#define css()
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , laydate = layui.laydate
        , $ = layui.$
		;
        
        laydate.render({
            elem:"#deductMonth",
            type:"month",
            range:false,
            format:"yyyy-MM"
        });
        
		dailyStatisticsLoad = function () {
			layer.load();
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/cardmanager/baseDeductCardSummaryDetailList',
                data: {baseId:$('#baseId').val(), deductMonth:$('#deductMonth').val(), cardTypeId:$('#cardTypeId').val()},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	$('#baseDeductCardSummaryDetailList').empty();
                    	var html = '';
                    	var totalDays = 0;
                    	var totalPrice = 0;
                    	var totalMoney = 0;
                    	$.each(rep.data, function(i,deduct){
							var baseName = deduct.baseName!=null && deduct.baseName!=''?deduct.baseName:'';
							var cardTypeName = deduct.cardTypeName!=null && deduct.cardTypeName!=''?deduct.cardTypeName:'';
							var deductMonth = deduct.deductMonth!=null && deduct.deductMonth!=''?deduct.deductMonth:'';
							var totalTimes = deduct.totalTimes!=null && deduct.totalTimes!=''?deduct.totalTimes:0;
							var referencePrice = deduct.referencePrice!=null && deduct.referencePrice!=''?deduct.referencePrice:0;
							var countMoney = deduct.totalMoney;
							
							html+='<tr>';
							html+='<td>'+(i+1)+'</td>';
							html+='<td>'+baseName+'</td>';
							html+='<td>'+deductMonth+'</td>';
							html+='<td>'+cardTypeName+'</td>';
							html+='<td>'+totalTimes+'</td>';
							html+='<td>'+referencePrice+'</td>';
							html+='<td>'+countMoney+'</td>';
							html+='</tr>';
							totalDays+=totalTimes;
							totalPrice+=referencePrice;
							totalMoney+=countMoney;
                    	});
                    	html+='<tr>';
						html+='<td>合计</td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td></td>';
						html+='<td>'+totalDays+'</td>';
						html+='<td>'+totalPrice+'</td>';
						html+='<td>'+totalMoney+'</td>';
						html+='</tr>';
                    	$('#baseDeductCardSummaryDetailList').append(html);
                    }
                },
                complete : function() {
                	layer.closeAll('loading');
                }
            });
		}
		
		dailyStatisticsLoad();
		
        // 搜索消费记录按钮
        $('#searchRechargeBtn').on('click', function(){
        	dailyStatisticsLoad();
        });

    });

</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="searchForm" class="layui-form layui-form-pane" action="">
	    	<div class="layui-inline">
		        <label class="layui-form-label">基地</label>
		        <div class="layui-input-inline">
		            <select id="baseId" name="baseId" lay-search>
						<option value="">请选择基地</option>
						#for(base : baseList)
							<option value="#(base.id)" #(base.id == baseId?'selected':'')>#(base.baseName)</option>
						#end
					</select>
		        </div>
    		</div>
	    	<div class="layui-inline">
		        <label class="layui-form-label">月份</label>
		        <div class="layui-input-inline">
		            <input type="text" id="deductMonth" name="deductMonth" class="layui-input" placeholder="请选择月份" value="#(deductMonth)" autocomplete="off">
		        </div>
    		</div>
    		<div class="layui-inline">
		        <label class="layui-form-label">卡类别</label>
		        <div class="layui-input-inline">
		            <select id="cardTypeId" name="cardTypeId" lay-search>
						<option value="">请选择卡类别</option>
						#for(type : typeList)
							<option value="#(type.id)" #(type.id == cardTypeId?'selected':'')>#(type.cardType)</option>
						#end
					</select>
		        </div>
    		</div>
	        <div class="layui-inline">
	        	<div class="layui-input-inline">
	        		<div class="layui-btn-group">
				        <button type="button" id="searchRechargeBtn" class="layui-btn">搜索</button>
				        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
	        		</div>
	        	</div>
    		</div>
		</form>
	</div>
	<div class="layui-row">
		<table class="layui-table">
			<thead>
				<tr>
					<th>序号</th>
					<th>基地名称</th>
					<th>扣卡月份</th>
					<th>卡类别</th>
					<th>扣卡天数</th>
					<th>参考单价</th>
					<th>参考金额</th>
				</tr> 
			</thead>
			<tbody id="baseDeductCardSummaryDetailList">
			</tbody>
		</table>
	</div>
</div>
#end