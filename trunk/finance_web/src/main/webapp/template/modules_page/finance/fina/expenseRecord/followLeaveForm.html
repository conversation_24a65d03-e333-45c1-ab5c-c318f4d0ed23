#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡规则管理页面#end

#define css()

#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        followLeaveDataTabLoad();


        function followLeaveDataTabLoad(){
            table.render({
                id : 'leaveTable'
                ,elem : '#leaveTable'
                ,method : 'POST'
                ,where : {"id":$("#id").val()}
                ,url : '#(ctxPath)/fina/expenseRecord/queryFollowLeaveRecord'
                ,cols: [[
                    {field:'name', title: '姓名', align: 'center', unresize: true}
                    ,{field:'leaveTime', title: '离开时间', align: 'center',unresize: true}
                    ,{field:'returnTime', title: '返回时间', align: 'center',unresize: true}
                ]]
            });
        };

    });
</script>
#end

#define content()
<div>
    <input type="hidden" id="id" value="#(id)"/>
    <table id="leaveTable" lay-filter="leaveTable"></table>
</div>

#end