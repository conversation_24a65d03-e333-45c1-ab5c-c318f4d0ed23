#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅居消费主记录列表#end

#define css()
#end

#define content()
<div class="layui-collapse">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post" style="margin-left:25px;margin-top:15px;">
            <div class="layui-row">
                入住基地：
                <div class="layui-inline">
                    <select name="baseId" lay-search>
                        <option value="">请选择入住基地</option>
                        #for(b : baseList)
                        <option value="#(b.id)">#(b.baseName)</option>
                        #end
                    </select>
                </div>
                &nbsp;&nbsp;

                <input type="hidden" class="layui-input" name="cardNumber" value="#(cardNumber)" />
                <span style="margin-left: 10px;">&nbsp;&nbsp;</span>

                &nbsp;&nbsp;
                入住人：
                <div class="layui-inline">
                    <input type="text" class="layui-input" name="name"/>
                </div>
                &nbsp;&nbsp;
                入住号：
                <div class="layui-inline">
                    <input type="text" class="layui-input" name="checkinNo"/>
                </div>
            </div>
            &nbsp;&nbsp;
            <div class="layui-row">
                入住状态：
                <div class="layui-inline">
                    <select name="checkinStatus" lay-search>
                        <option value="">请选择入住状态</option>
                        #for(d : dictList)
                        <option value="#(d.dictValue)">#(d.dictName)</option>
                        #end
                    </select>
                </div>
                &nbsp;&nbsp;
                结算状态：
                <div class="layui-inline">
                    <select name="settleStatus" lay-search>
                        <option value="">请选择结算状态</option>
                        <option value="0">未结算</option>
                        <option value="1">已结算</option>
                        <option value="2">作废</option>
                    </select>
                </div>
                &nbsp;&nbsp;
                入住时间：
                <div class="layui-inline">
                    <input type="text" id="checkinTime" name="checkinTime" autocomplete="off" class="layui-input" placeholder="请输入入住时间">
                </div>
                &nbsp;&nbsp;
                预订号：
                <div class="layui-inline">
                    <input type="text" class="layui-input" name="bookNo"/>
                </div>
                &nbsp;&nbsp;
                随行人姓名：
                <div class="layui-inline">
                    <input type="text" class="layui-input" name="followName"/>
                </div>
                <button type="button" id="search" class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            </div>
        </form>
    </div>
    <div class="layui-row">
        <table id="erTable" lay-filter="erTable"></table>
    </div>
</div>
#getDictLabel("checkin_status")
#end
<!-- 公共JS文件 -->
#getDictLabel("gender")
#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        //日期范围
        laydate.render({
            elem:"#checkinTime",
            type:"date",
            format:"yyyy-MM-dd"
        }) ;
        
        function erLoad(data){
            table.render({
                id : 'erTable'
                ,elem : '#erTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.75
                ,limit : 10
                ,limits : [20,30,40,50]
                ,url : '#(ctxPath)/fina/expenseRecord/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'checkinNo', title: '入住人-预订号-入住号', align: 'center', unresize: false,width:220,style:'font-size:10px;',templet:function (d) {
                            var str="";
                            if(d.name != null && d.name != null){
                                str = d.name;
                            }else{
                                str = "--";
                            }
                            str += " ";
                            if(d.bookNo != null && d.bookNo != ''){
                                str += d.bookNo;
                            }else{
                                str += "--";
                            }
                            str += " ";
                            if(d.checkinNo != null && d.checkinNo != ''){
                                str += d.checkinNo;
                            }else{
                                str += "--";
                            }
                            return str;
                      }}
                    ,{field:'fullName', title: '会员卡信息', align: 'center', unresize: false,width:180,templet:function (d) {
                        var str="";
                        if(d.cardNumber != null && d.cardNumber != ''){
                            str = d.cardNumber;
                        }else{
                            str = "--";
                        }
                        str += " ";
                        if(d.fullName != null && d.fullName != null){
                            str += d.fullName;
                        }else{
                            str += "--";
                        }
                        return str;
                    }}
                    ,{field:'baseName', title: '基地', align: 'center', unresize: false,width:180}
                    ,{field:'bookStartTime', title: '预订时间', align: 'center', unresize: false,width:220,templet:function (d) {
                        var str="";
                        if(d.bookStartTime != null && d.bookStartTime != ''){
                            str = dateFormat(d.bookStartTime,'yyyy-MM-dd');
                        }else{
                            str = "--";
                        }
                        str += " 至 ";
                        if(d.bookEndTime != null && d.bookEndTime != ''){
                            str += dateFormat(d.bookEndTime,'yyyy-MM-dd');
                        }else{
                            str += "--";
                        }
                        return str;
                    }}
                    ,{field:'checkinTime', title: '入住时间', align: 'center', unresize: false,width:130,templet:"<div>{{ dateFormat(d.checkinTime,'yyyy-MM-dd') }}</div>"}
                    ,{field:'checkinStatus', title: '入住状态', align: 'center',unresize: false,width:100,templet: "<div>{{ dictLabel(d.checkinStatus,'checkin_status','- -') }}</div>"}
                    ,{field:'withholdTimes', title: '锁定天数', align: 'center', unresize: false,width:110,templet: "<div>{{ d.withholdTimes?d.withholdTimes + '天':'- -' }}</div>"}
                    ,{field:'actualTimes', title: '实扣天数', align: 'center', unresize: true,width:110,templet: "<div>{{ d.actualTimes?d.actualTimes + '天': '- -' }}</div>"}
                    ,{field:'withholdAmount', title: '锁定金额', align: 'center', unresize: false,width:110,templet: "<div>{{ d.withholdAmount?d.withholdAmount + '元':'- -' }}</div>"}
                    ,{field:'actualAmount', title: '实扣金额', align: 'center', unresize: true,width:110,templet: "<div>{{ d.actualAmount?d.actualAmount + '元':'- -' }}</div>"}
                    ,{field:'settleStatus', title: '状态', align: 'center', unresize: true,width:100,templet:"<div>{{d.settleStatus == '0'?'<span class='layui-badge layui-bg-green'>未结算</span>':d.settleStatus == '1'?'<span class='layui-badge'>已结算</span>':d.settleStatus == '2'?'<span class='layui-badge layui-bg-cyan'>作废</span>':'- -'}}</div>"}
                    ,{field:'settleTime', title: '结算时间', sort: true, align: 'center', unresize: true,width:160,templet:"<div>{{ dateFormat(d.settleTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function (res,curr,count) {
                    $.each(res.data,function (index,item) {
                        if(item.checkinStatus==='book' && item.settleStatus==='2'){
                            $(".layui-table-body").find("table").find("tr:eq("+index+")").attr("style","background-color:#ffe3e4;");
                        }
                    })
                }
            });
        };
        table.on('tool(erTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/expenseRecord/delete',
                        data: {id:obj.data.id},
                        notice:true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                table.reload('erTable');
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event === 'detail'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/fina/appBill/cardExist',
                    data: {'cardNumber':obj.data.cardNumber},
                    notice:false,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            var url = "#(ctxPath)/fina/expenseRecord/form?id=" + obj.data.id ;
                            layerShow("旅居消费详情",url,'100%','100%');
                        }else{
                            layer.msg(rep.msg, {icon: 5, offset: 'auto'});
                        }
                    },
                    complete : function() {
                    }
                });
            }else if(obj.event === 'settle'){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/fina/appBill/cardExist',
                    data: {'cardNumber':obj.data.cardNumber},
                    notice:false,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state==='ok'){
                            var url = "#(ctxPath)/fina/expenseRecord/form?id=" + obj.data.id +"&type=settle" ;
                            layerShow("旅居消费结算",url,'100%','100%');
                        }else{
                            layer.msg(rep.msg, {icon: 5, offset: 'auto'});
                        }
                    },
                    complete : function() {
                    }
                });
            }else if(obj.event === 'change'){
                var url = "#(ctxPath)/fina/expenseRecord/changeErForm?id=" + obj.data.id;
                layerShow("账单分段换卡记录",url,'100%','100%');
            }
        });

        erLoad({cardNumber:'#(cardNumber)'});

        sd=form.on("submit(search)",function(data){
            erLoad(data.field);
            return false;
        });

      	//回车事件
    	document.onkeydown = function(e){
    		var ev =document.all ? window.event:e;  
    		if(ev.keyCode==13) {
    			$('#search').click();
    			return false
    		}
    	}
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("finance:sojournCardOrder:detail")
    <a class="layui-btn layui-btn-xs layui-bg-blue" lay-event="detail">查看</a>
    #end
    #shiroHasPermission("finance:sojournCardOrder:settle")
    #[[
    {{#if(d.checkinStatus != 'book' && d.settleStatus != '1' && d.settleStatus != '2'){}}
    <a class="layui-btn layui-btn-xs" lay-event="settle">结算</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("finance:sojournCardOrder:change")
    #[[
    {{#if(d.settleStatus != '1' && d.settleStatus != '2' && d.checkinStatus != 'book'){}}
    <a class="layui-btn layui-bg-orange layui-btn-xs" lay-event="change">换卡</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("finance:sojournCardOrder:delete")
    #[[
    {{#if(d.settleStatus != '1' && d.settleStatus != '2'){}}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#
    #end

</script>
#end