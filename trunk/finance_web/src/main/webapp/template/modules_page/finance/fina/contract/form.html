#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()账户编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, $ = layui.jquery
	;
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/fina/contract/saveContractRecord',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload(null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<form class="layui-form layui-form-pane" style="padding-left: 5px;">

		<div class="layui-form-item" style="margin-top: 10px;">
			<label class="layui-form-label"><font color="red">*</font>合同名称</label>
			<div class="layui-input-block">
				<input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)"  placeholder="请输入合同名称">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>合同类型</label>
			<div class="layui-input-block">
				<select name="contractTypeId" lay-verify="required" >
					<option value="">请选择合同类型</option>
					#for(type : contractTypeList)
					<option #if(type.id==model.contractTypeId??) selected  #end value="#(type.id)">#(type.typeName)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>会员卡类型</label>
			<div class="layui-input-block">
				<select name="cardTypeId" lay-verify="" lay-search >
					<option value="">请选择会员卡类型</option>
					#for(cardType : cardTypeList)
					<option #if(cardType.id==model.cardTypeId??) selected  #end value="#(cardType.id)">#(cardType.cardType)</option>
					#end
				</select>
			</div>
		</div>

		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>排序</label>
			<div class="layui-input-block">
				<input type="text" name="sort" class="layui-input" value="#if(model==null)#(row+1??)#else#(model.sort??)#end" lay-verify="required|number" placeholder="请输入排序">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>是否可用</label>
			<div class="layui-input-block">
				<input type="radio" name="isEnable" value="1" title="是" #if(model==null || model.isEnable??=='1') checked #end >
				<input type="radio" name="isEnable" value="0" title="否" #if(model.isEnable??=='0') checked #end>
			</div>
		</div>
		<div class="layui-row">
			<label class="layui-form-label">备注</label>
			<div class="layui-input-block">
				<textarea id="remark" placeholder="请输入内容" class="layui-textarea">#(model.remark??)</textarea>
			</div>
		</div>

		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.Id??)">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end