#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()赠送详情页面#end

#define css()

#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'giveSchemeDetailTable'
            ,elem : '#giveSchemeDetailTable'
            ,method : 'POST'
            ,where : {"giveSchemeId":$("#giveSchemeId").val()}
            ,limit : 10
            ,limits : [10,20,30,40]
            ,url : '#(ctxPath)/fina/cardmanager/giveSchemeDetailPage'
            ,cellMinWidth: 80
            ,cols: [[
                {type: 'cycle', title: '周期',align: 'center',width:'15%',unresize:true,templet:"<div>{{ dictLabel(d.cycle,'give_cycle','- -') }}</div>"}
                ,{field:'startSeq', title: '开始序',width:'15%', align: 'center', unresize: true}
                ,{field:'endSeq', title: '结束序',width:'15%', align: 'center', unresize: true}
                ,{field:'giveTimes', title: '赠送天数',width:'15%', sort: true, align: 'center', unresize: true}
                ,{field:'giveAmount', title: '赠送金额',width:'15%', sort: true, align: 'center', unresize: true}
                ,{field:'describe', title: '备注',width:'25%', sort: true, unresize: true}
            ]]
            ,page : true
        });

    });
</script>
#end

#define content()
<div class="layui-row" style="padding: 10px;">
    <input type="hidden" id="giveSchemeId" value="#(giveSchemeId??)">
    <table id="giveSchemeDetailTable" lay-filter="giveSchemeDetailTable"></table>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</div>
#getDictLabel("give_cycle")
#end