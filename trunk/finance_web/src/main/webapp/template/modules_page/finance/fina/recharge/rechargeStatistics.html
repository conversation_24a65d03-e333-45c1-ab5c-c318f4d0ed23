#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()充值统计表#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="searchForm" class="layui-form layui-form-pane" action="">
	    	<div class="layui-inline">
            	<label class="layui-form-label">查询类型:</label>
	            <div class="layui-input-inline">
	                <select id="queryType" name="queryType" lay-filter="queryType">
	                    <option value="day">按天</option>
	                    <option value="month">按月</option>
	                    <option value="year">按年</option>
	                </select>
	            </div>
            </div>
			<div id="yearMonthDayDiv" class="layui-inline">
				<label class="layui-form-label">日期:</label>
				<div class="layui-input-inline">
					<input id="yearMonthDay" name="yearMonthDay" class="layui-input" value="#(yearMonthDay)" placeholder="">
				</div>
			</div>
	    	<div id="yearMonthDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">月份:</label>
				<div class="layui-input-inline">
					<input id="yearMonth" name="yearMonth" class="layui-input" value="#(yearMonth)" placeholder="">
				</div>
			</div>
	    	<div id="yearDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">年份:</label>
				<div class="layui-input-inline">
					<input id="year" name="year" class="layui-input" value="#(year)" placeholder="">
				</div>
			</div>
	        <div class="layui-inline">
	        	<div class="layui-input-inline">
	        		<div class="layui-btn-group">
				        <button type="button" id="searchRechargeBtn" class="layui-btn">搜索</button>
				        #shiroHasPermission("recharge:statistics:export")
					        <button type="button" id="export" class="layui-btn">导出</button>
			            #end
	        		</div>
	        	</div>
    		</div>
		</form>
	</div>
	<div class="layui-row">
		<table class="layui-table">
			<colgroup>
				<col width="20%">
				<col width="20%">
				<col width="20%">
				<col width="20%">
				<col width="20%">
				<col>
			</colgroup>
			<thead>
				<tr>
					<th>充值金额</th>
					<th>充值天数</th>
					<th>充值点数</th>
					<th>充值积分</th>
					<th>充值豆豆券</th>
				</tr> 
			</thead>
			<tbody>
				<tr>
					<td id="amount"></td>
					<td id="consumeTimes"></td>
					<td id="consumePoints"></td>
					<td id="consumeIntegral"></td>
					<td id="consumeBeanCoupons"></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="layui-row">
		<table id="rechargeStatisticsTable" lay-filter="rechargeStatisticsTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;
        
        laydate.render({
            elem: '#yearMonthDay'
            ,trigger:'click'
        });
        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
        });
        laydate.render({
            elem: '#year'
            ,type: 'year'
            ,trigger:'click'
        });
        
        form.on('select(queryType)', function(data){
        	var queryType = data.value;
        	if(queryType=='day'){
        		$("#yearDiv").hide();
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").show();
        	}else if(queryType=='month'){
        		$("#yearDiv").hide();
        		$("#yearMonthDiv").show();
        		$("#yearMonthDayDiv").hide();
        	}else{
        		$("#yearDiv").show();
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").hide();
        	}
       	});
        
        rechargeStatisticsLoad = function () {
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/recharge/rechargeStatisticsData',
                data: {queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	$('#amount').html(rep.data.amount);
                    	$('#consumeTimes').html(rep.data.consumeTimes);
                    	$('#consumePoints').html(rep.data.consumePoints);
                    	$('#consumeIntegral').html(rep.data.consumeIntegral);
                    	$('#consumeBeanCoupons').html(rep.data.consumeBeanCoupons);
                    }
                },
                complete : function() {
                }
            });
		}
		
        rechargeStatisticsLoad();

		pageTableReload = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'rechargeStatisticsTable',
				elem : "#rechargeStatisticsTable" ,
				url : '#(ctxPath)/fina/recharge/rechargeStatisticsPage' ,
				where: data,
				height:'full-200',
				cols : [[
					{field:'', title: '会员卡信息', unresize: false,width:160,templet:function (d) {
							var str="";
							if(d.cardNumber != null && d.cardNumber != ''){
								str = d.cardNumber;
							}else{
								str = "--";
							}
							str += " ";
							if(d.cardMemberName != null && d.cardMemberName != null){
								str += d.cardMemberName;
							}else{
								str += "--";
							}
							return str;
						}},
					{field:'cardTypeName',title:'卡类别',width:150,unresize:false},
					{field:'isIncome',title:'收入性',width:90,unresize:false,templet:function (d) {
							if(d.isIncome=='1'){
								return "<span class='layui-badge layui-bg-green'>收入性</span>";
							}else if(d.isIncome=='0'){
								return "<span class='layui-badge layui-bg-orange'>非收入性</span>";
							}else{
								return '- -'
							}
						}},
					{field:'isCash',title:'是否收现金',width:70,unresize:false,templet:function (d) {
							if(d.isCash=='1'){
								return "<span class='layui-badge layui-bg-green'>是</span>";
							}else if(d.isCash=='0'){
								return "<span class='layui-badge layui-bg-orange'>否</span>";
							}else{
								return '- -'
							}
						}},
					{field:'isCash',title:'收钱账户',width:160,unresize:false,templet:function (d) {
							var payWay="";
							var accountName="";
							if(d.accountName!=undefined){
								accountName=d.accountName
							}
							if(d.payWay=="1"){
								payWay="(现金)";
							}else if(d.payWay=="2"){
								payWay="(微信)";
							}else if(d.payWay=="3"){
								payWay="(支付宝)";
							}else if(d.payWay=="4"){
								payWay="(信用卡)";
							}else if(d.payWay=="5"){
								payWay="(会员卡)";
							}else if(d.payWay=="6"){
								payWay="(Pos机)";
							}else if(d.payWay=="7"){
								payWay="(转账)";
							}else if(d.payWay=="8"){
								payWay="(企业微信)";
							}
							return payWay+accountName;
						}},
					{field:'rechargeNo',title:'充值编号',width:120,unresize:false},
					{field:'type',title:'充值类型',width:90,unresize:false,templet:"<div>{{ d.type=='1'?'<span class='layui-badge layui-bg-green'>金额</span>':d.type=='2'?'<span class='layui-badge layui-bg-orange'>天数</span>':d.type=='3'?'<span class='layui-badge layui-bg-black'>点数</span>':d.type=='4'?'<span class='layui-badge layui-bg-blue'>积分</span>':d.type=='5'?'<span class='layui-badge layui-bg-cyan'>豆豆券</span>':'- -' }}</div>"},
					{field:'classify',title:'充值分类',width:100,unresize:false,templet: '#dictTpl("recharge_classify", "classify")'},
					{field:'amount',title:'金额',width:90,unresize:false,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
					{field:'',title:'天数',width:90,unresize:false,templet: "<div>{{ String(d.consumeTimes)?d.consumeTimes + '天':'- -' }}</div>"},
					{field:'',title:'点数',width:90,unresize:false,templet: function (d) {
							if(typeof(d.consumePoints)==='undefined'){
								return '0.0';
							}else{
								return d.consumePoints;
							}
						}
					},
					{field:'',title:'积分',width:90,unresize:false,templet: function (d) {
							if(typeof(d.consumeIntegral)==='undefined'){
								return '0.0';
							}else{
								return d.consumeIntegral;
							}
						}
					},
					{field:'',title:'豆豆券',width:90,unresize:false,templet: function (d) {
							if(typeof(d.consumeBeanCoupons)==='undefined'){
								return '0.0';
							}else{
								return d.consumeBeanCoupons;
							}
						}
					},
					{field:'createTime',title:'充值时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.rechargeTime,'yyyy-MM-dd')}}</div>"},
					{field:'',title:'撤销状态',width:90,unresize:false,templet:function (d) {
							if(d.cancelFlag=='0'){
								return '未撤销';
							}else{
								return '已撤销';
							}
						}},
					{field:'',title:'审核状态',width:90,unresize:false,templet:function (d) {
							if(d.isReview=='0'){
								return '未审核';
							}else{
								return '已审核';
							}
						}},
					{field:'',title:'作废状态',width:90,unresize:false,templet:function (d) {
							if(d.delFlag=='0'){
								return '未作废';
							}else{
								return '已作废';
							}
						}},
					{field:'describe',title:'说明',fixed: 'right',unresize:true,width:500,style:"text-align:left;font-size:10px;color:#000;"}
				]] ,
				page : true,
				limit : 10,
				done:function () {
					layer.close(loadingIndex);
				}
			});
		}

        pageTableReload({queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()});
		
        // 搜索消费记录按钮
        $('#searchRechargeBtn').on('click', function(){
        	rechargeStatisticsLoad();
        	pageTableReload({queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()});
        });

        $("#export").click(function () {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '导出中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            var url='#(ctxPath)/fina/recharge/rechargeStatisticsExport?queryType='+$('#queryType').val()+"&yearMonthDay="+$('#yearMonthDay').val()+"&yearMonth="+$('#yearMonth').val()+"&year="+$('#year').val();
            window.location.href=url;
            setTimeout(function(){
				layer.close(loadingIndex);
            },5000);
        });
    });

</script>
#end