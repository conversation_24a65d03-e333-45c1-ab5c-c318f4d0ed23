#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查看房间床位信息#end

#define css()
<style>
	.layui-form-label {
		width: 130px !important;
		text-align: center !important;
	}
</style>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <form class="layui-form layui-form-pane" action="" id="form">
        <table width="100%">
            <tr>
                <td>
					<label class="layui-form-label">房型</label>
					<div class="layui-input-inline">
					    <input value="#(bed.roomTypeName??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
                <td>
					<label class="layui-form-label">床位编号</label>
					<div class="layui-input-inline">
					    <input value="#(bed.bedNo??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
			</tr>
			<tr>
				<td>
					<label class="layui-form-label">是否大床房</label>
					<div class="layui-input-inline">
						<input value="#if(roomType.isBigBedRoom??=='1')是#elseif(roomType.isBigBedRoom??=='0')否#end" autocomplete="off" class="layui-input" readonly>
					</div>
				</td>
				<td>
					<label class="layui-form-label" style="padding: 8px 5px;">房间扣费天数配置</label>
					<div class="layui-input-inline">
						<input value="#(roomType.privateRoomDays??)" autocomplete="off" class="layui-input" readonly>
					</div>
				</td>

			</tr>
			<tr>
                <td>
					<label class="layui-form-label">床位名称</label>
					<div class="layui-input-inline">
					    <input value="#(bed.bedName??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
                <td>
					<label class="layui-form-label">床位类型</label>
					<div class="layui-input-inline">
					    <input value="#(bed.typeName??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
<!--                 <td> -->
<!--                     <div class="layui-upload" style="text-align: center;height: 160px;width: 200px;"> -->
<!--                         <div class="layui-upload-list"> -->
<!--                             <img class="layui-upload-img" id="profilePhotoImg" style="max-height: 200px;max-width: 200px;" src="#if(bed.bedImage??!=null)#(bed.bedImage??)#end" > -->
<!--                         </div> -->
<!--                     </div> -->
<!--                 </td> -->
            </tr>
            <tr>
                <td>
					<label class="layui-form-label">床位状态</label>
					<div class="layui-input-inline">
					    <input value="#(bed.statusName??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
                <td>
					<label class="layui-form-label">床位是否可用</label>
					<div class="layui-input-inline">
					    <input value="#if(bed.isBedEnable??=='0')可用 #elseif(bed.isBedEnable??=='1') 不可用 #end" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
            </tr>
            <tr>
                <td>
					<label class="layui-form-label">选房费名称</label>
					<div class="layui-input-inline">
					    <input value="#(bed.name??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
                <td>
					<label class="layui-form-label">选房费价格</label>
					<div class="layui-input-inline">
					    <input value="#(bed.amount??)" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
			</tr>
			<tr>
                <td>
					<label class="layui-form-label">选房费是否可用</label>
					<div class="layui-input-inline">
					    <input value="#if(bed.isMarkEnable??=='0') 可用 #elseif(bed.isMarkEnable??=='1') 不可用#end" autocomplete="off" class="layui-input" readonly>
					</div>
                </td>
                <td></td>
            </tr>
        </table>
    </form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
    layui.use(['form'],function(){
        var form = layui.form;
    });
</script>
#end