#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()床垫类型管理#end

#define css()
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            搜索条件:
            <div class="layui-inline" style="width: 210px;">
                <input id="nameOrIdcardOrCardNumber" name="nameOrIdcardOrCardNumber" class="layui-input" placeholder="姓名\身份证号码\会员卡号">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" id="search" type="button">查询</button>
        </form>

    </div>
    <table id="expiredCardTable" lay-filter="expiredCardTable"></table>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        table.render({
            id : 'expiredCardTable'
            ,elem : '#expiredCardTable'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,limit : 10
            ,limits : [10,20,30,40,50]
            ,url : '#(ctxPath)/fina/cardmanager/expiredCardRecordPage'
            ,cellMinWidth: 80
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field:'memberName', title: '会员名称', align: 'center', unresize: true}
            #shiroHasPermission("finance:idcardSee")
            ,{field:'idcard', title: '身份证号码',width:200, align: 'center', unresize: true}
            #end
            ,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
            ,{field:'cardType', title: '卡类型', align: 'center', unresize: true}
            ,{field:'openTime', title: '开卡时间', align: 'center', unresize: true,templet: "<div>{{ dateFormat(d.openTime,'yyyy-MM-dd') }}</div>"}
            ,{field:'expireDate', title: '最大有效期时间', align: 'center', unresize: true,templet: "<div>{{ dateFormat(d.expireDate,'yyyy-MM-dd') }}</div>"}
            ,{fixed:'right', title: '操作', width: 130, align: 'center', unresize: true, toolbar: '#actionBar'}
    ]]
    ,page : true
    });

        table.on('tool(expiredCardTable)',function(obj){
            if(obj.event === 'editExpireDate'){
                /*layer.confirm("确定要恢复该会员卡吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/fina/cardmanager/recoveryCard",
                        type:'post',
                        data:{"id":obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                recoveryCardTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });*/
                layerShow("修改会员卡有效年限",'#(ctxPath)/fina/cardmanager/expiredCardForm?id='+obj.data.id+"&openTime="+dateFormat(obj.data.openTime,'yyyy-MM-dd')+"&expireDate="+dateFormat(obj.data.expireDate,'yyyy-MM-dd')+"&useYears="+obj.data.useYears,500,500);
            }
        });

        recoveryCardTableReload=function(){
            table.reload('expiredCardTable',{"where":{"nameOrIdcardOrCardNumber":$("#nameOrIdcardOrCardNumber").val()}});
        }

        // 搜索
        $("#search").click(function(){
            recoveryCardTableReload();
        });


    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("finance:card:recoveryCard:modifyExpiredBtn")
    <a class="layui-btn layui-btn-xs" lay-event="editExpireDate">修改有效期时间</a>
    #end
</script>
#end
