#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()扣卡统计表#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="searchForm" class="layui-form layui-form-pane" action="">
	    	<div class="layui-inline">
            	<label class="layui-form-label">查询类型:</label>
	            <div class="layui-input-inline">
	                <select id="queryType" name="queryType" lay-filter="queryType">
	                    <option value="day">按天</option>
	                    <option value="month">按月</option>
	                    <option value="year">按年</option>
	                </select>
	            </div>
            </div>
			<div id="yearMonthDayDiv" class="layui-inline">
				<label class="layui-form-label">日期:</label>
				<div class="layui-input-inline">
					<input id="yearMonthDay" name="yearMonthDay" class="layui-input" value="#(yearMonthDay)" placeholder="">
				</div>
			</div>
	    	<div id="yearMonthDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">月份:</label>
				<div class="layui-input-inline">
					<input id="yearMonth" name="yearMonth" class="layui-input" value="#(yearMonth)" placeholder="">
				</div>
			</div>
	    	<div id="yearDiv" class="layui-inline" style="display: none;">
				<label class="layui-form-label">年份:</label>
				<div class="layui-input-inline">
					<input id="year" name="year" class="layui-input" value="#(year)" placeholder="">
				</div>
			</div>
	        <div class="layui-inline">
	        	<div class="layui-input-inline">
	        		<div class="layui-btn-group">
				        <button type="button" id="searchBtn" class="layui-btn">搜索</button>
				        #shiroHasPermission("deduct:statistics:export")
					        <button type="button" id="export" class="layui-btn">导出</button>
			            #end
	        		</div>
	        	</div>
    		</div>
		</form>
	</div>
	<div class="layui-row">
		<table class="layui-table">
			<colgroup>
				<col width="17%">
				<col width="16%">
				<col width="16%">
				<col width="17%">
				<col width="17%">
				<col width="17%">
				<col>
			</colgroup>
			<thead>
				<tr>
					<th>扣卡金额</th>
					<th>扣卡天数</th>
					<th>扣卡点数</th>
					<th>扣卡积分</th>
					<th>扣卡豆豆券</th>
					<th>收入</th>
				</tr> 
			</thead>
			<tbody>
				<tr>
					<td id="amount"></td>
					<td id="deductTimes"></td>
					<td id="deductPoints"></td>
					<td id="deductIntegral"></td>
					<td id="deductBeanCoupons"></td>
					<td id="incomeAmount"></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="layui-row">
		<table id="deductStatisticsTable" lay-filter="deductStatisticsTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['table','form','laydate'],function(){
        var table = layui.table
        , form = layui.form
        , $ = layui.$
        , laydate = layui.laydate
		;
        
        laydate.render({
            elem: '#yearMonthDay'
            ,trigger:'click'
        });
        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
        });
        laydate.render({
            elem: '#year'
            ,type: 'year'
            ,trigger:'click'
        });
        
        form.on('select(queryType)', function(data){
        	var queryType = data.value;
        	if(queryType=='day'){
        		$("#yearDiv").hide();
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").show();
        	}else if(queryType=='month'){
        		$("#yearDiv").hide();
        		$("#yearMonthDiv").show();
        		$("#yearMonthDayDiv").hide();
        	}else{
        		$("#yearDiv").show();
        		$("#yearMonthDiv").hide();
        		$("#yearMonthDayDiv").hide();
        	}
       	});
        
        rechargeStatisticsLoad = function () {
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/cardmanager/deductStatisticsData',
                data: {queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()},
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    	$('#amount').html(rep.data.amount);
                    	$('#deductTimes').html(rep.data.times);
                    	$('#deductPoints').html(rep.data.points);
                    	$('#deductIntegral').html(rep.data.integrals);
                    	$('#deductBeanCoupons').html(rep.data.beanCoupons);
                    	$('#incomeAmount').html(rep.data.incomeAmount);
                    }
                },
                complete : function() {
                }
            });
		}
		
        rechargeStatisticsLoad();

		pageTableReload = function (data) {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '加载中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
			table.render({
				id : 'deductStatisticsTable',
				elem : "#deductStatisticsTable" ,
				url : '#(ctxPath)/fina/cardmanager/deductStatisticsPage' ,
				where: data,
				height:'full-200',
				cols : [[
					{field:'', title: '会员卡信息', unresize: false,width:160,templet:function (d) {
							var str="";
							if(d.cardNumber != null && d.cardNumber != ''){
								str = d.cardNumber;
							}else{
								str = "--";
							}
							str += " ";
							if(d.fullName != null && d.fullName != null){
								str += d.fullName;
							}else{
								str += "--";
							}
							return str;
						}},
					{field:'cardTypeName',title:'卡类别',width:150,unresize:false},
					{field:'',title:'扣卡类型',width:100,unresize:false,templet: '#dictTpl("account_record_operate_type", "type")'},
					{field:'',title:'开始时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.startDate,'yyyy-MM-dd')}}</div>"},
					{field:'',title:'结束时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.endDate,'yyyy-MM-dd')}}</div>"},
					{field:'',title:'金额',width:90,unresize:false,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
					{field:'',title:'天数',width:90,unresize:false,templet: "<div>{{ String(d.times)?d.times + '天':'- -' }}</div>"},
					{field:'',title:'点数',width:90,unresize:false,templet: function (d) {
							if(typeof(d.points)==='undefined'){
								return '0.0';
							}else{
								return d.points;
							}
						}
					},
					{field:'',title:'积分',width:90,unresize:false,templet: function (d) {
							if(typeof(d.integrals)==='undefined'){
								return '0.0';
							}else{
								return d.integrals;
							}
						}
					},
					{field:'',title:'豆豆券',width:90,unresize:false,templet: function (d) {
							if(typeof(d.beanCoupons)==='undefined'){
								return '0.0';
							}else{
								return d.beanCoupons;
							}
						}
					},
					{field:'',title:'收入',width:90,unresize:false,templet: "<div>{{ String(d.incomeAmount)?d.incomeAmount + '元':'- -' }}</div>"},
					{field:'',title:'扣卡时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.dealTime,'yyyy-MM-dd')}}</div>"},
					{field:'describe',title:'说明',fixed: 'right',unresize:true,width:500,style:"text-align:left;font-size:10px;color:#000;"}
				]] ,
				page : true,
				limit : 10,
				done:function () {
					layer.close(loadingIndex);
				}
			});
		}

		pageTableReload({queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()});

        // 搜索消费记录按钮
        $('#searchBtn').on('click', function(){
        	rechargeStatisticsLoad();
        	pageTableReload({queryType:$('#queryType').val(),yearMonthDay:$('#yearMonthDay').val(),yearMonth:$('#yearMonth').val(),year:$('#year').val()});
        });

        $("#export").click(function () {
			//loading层
			var loadingIndex = layer.load(2, { //icon支持传入0-2
				shade: [0.5, 'gray'], //0.5透明度的灰色背景
				content: '导出中...',
				success: function (layero) {
					layero.find('.layui-layer-content').css({
						'padding-top': '39px',
						'width': '60px'
					});
				}
			});
            var url='#(ctxPath)/fina/cardmanager/deductStatisticsExport?queryType='+$('#queryType').val()+"&yearMonthDay="+$('#yearMonthDay').val()+"&yearMonth="+$('#yearMonth').val()+"&year="+$('#year').val();
            window.location.href=url;
            setTimeout(function(){
				layer.close(loadingIndex);
            },5000);
        });
    });

</script>
#end