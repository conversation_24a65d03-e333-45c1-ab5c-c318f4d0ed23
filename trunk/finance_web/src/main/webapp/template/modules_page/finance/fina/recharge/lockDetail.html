#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡锁定明细页面#end

#define css()
<style>
	label.layui-form-label{
		padding-right:0px;
	}
	div.in{
		padding-top: 10px;
	}
</style>
#end

#define js()
<script>
	layui.use(['form','laytpl','layer','table'], function() {
		var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer,table=layui.table;


		table.render({
			id : 'lockTable',
			elem : "#lockTable" ,
			url : '#(ctxPath)/fina/recharge/cardLockDetail' ,
			where: {cardNumber: "#(cardNumber??)"},
			height:495,
			cols : [[
				{field:'type',title:'类型',width:'8%',unresize:false,templet:"<div>{{ d.category=='sojourn_bill'?'旅居账单':d.category=='tourist_bill'?'旅游团账单':d.category=='punish'?'违约账单':'- -' }}</div>"} ,
				{field:'no',title:'旅游团号-预定号-入住号',unresize:false,templet:function (d) {
						var str="";
						if(typeof d.touristNo!='undefined' ){
							str+=""+d.touristNo;
						}else{
							str+=" ";
						}
						str+="-";
						if(typeof d.bookNo!='undefined' ){
							str+=""+d.bookNo;
						}else{
							str+=" ";
						}
						str+="-";
						if(typeof d.checkinNo!='undefined' ){
							str+=""+d.checkinNo;
						}else{
							str+=" ";
						}
						return str;
					}} ,
				{field:'checkinName',title:'入住人',width:200,unresize:false},
				{field:'baseName',title:'入住基地',width:'14%',unresize:false},
				{field:'times',title:'锁定天数',width:'10%',unresize:false,templet: "<div>{{ String(d.lockTimes)?d.lockTimes + '天':'- -' }}</div>"},
				{field:'amount',title:'锁定金额',width:'9%',unresize:false,templet: "<div>{{ String(d.lockAmount)?d.lockAmount + '元':'- -' }}</div>"},
				/*{field:'points',title:'锁定点数',width:'10%',unresize:false,templet: function (d) {
					if(typeof(d.lockPoints)==='undefined'){
						return '0点数';
					}else{
						return d.lockPoints+'点数';
					}

				}},*/
				{field:'lockIntegrals',title:'锁定积分',width:'10%',unresize:false,templet: function (d) {
					if(typeof(d.lockIntegrals)==='undefined'){
						return '0积分';
					}else{
						return d.lockIntegrals+'积分';
					}
				}},
			]] ,
			page : true,
			limit : 10
		}) ;



	});

</script>
#end

#define content()
<div style="margin: 15px;">
	<div class="layui-row">
		<div class="layui-row">
			<table id="lockTable" class="layui-table" lay-filter="lockTable"></table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="cardId" name="cardId" value="#(card.id??)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</div>
</div>
#end