#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()月入住单明细#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 3px;
    }
    /*     .laytable-cell-checkbox .layui-disabled.layui-form-checked i { */
    /*         background: #fff !important; */
    /*     } */
</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            <!--<div class="layui-inline">
                <label class="layui-form-label">查询类型:</label>
                <div class="layui-input-inline">
                    <select id="queryType" name="queryType" lay-filter="queryType">
                        <option value="month">按月</option>
                        <option value="day">按天</option>
                    </select>
                </div>
            </div>-->
            <div class="layui-inline">
                <label class="layui-form-label">基地专卡:</label>
                <div class="layui-input-inline" style="width:200px;">
                    <select name="cardId" id="cardId" lay-filter="cardId">
                        #for(cardRecord : cardList)
                        <option value="#(cardRecord.id)">#(cardRecord.cardNumber)(#(cardRecord.fullName))</option>
                        #end
                    </select>
                </div>
            </div>


            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline" style="width:200px;">
                    <select name="status" id="status" lay-filter="status">
                        <option value="0">未审核</option>
                        <option value="1">已审核</option>
                    </select>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline" style="width:200px;">
                    <input class="layui-input" name="yearMonth" id="yearMonth" autocomplete="off" >
                </div>
            </div>

            <div class="layui-inline">
                <div class="layui-input-inline">
                    <div class="layui-btn-group">
                        <!--                        <button type="button" id="search" class="layui-btn">查询</button>-->
<!--                        <button type="button" id="edit" class="layui-btn">对账</button>-->
                    </div>
                </div>
            </div>
        </form>
    </div>
    <table id="expiredCardTable" lay-filter="expiredCardTable"></table>
</div>
#getDictLabel("checkin_status")
#end

#define js()
<script type="text/html" id="toolBar">
    #[[
    {{#if(d.status=='0'){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">审核</a>
    {{#}}}
    ]]#
    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,trigger:'click'
            ,value:'#(yearMonth)'
            ,max:'#(maxYearMonth)'
            ,btns: ['confirm']
            ,done: function(value, date){
                locaTale({"cardId":$("#cardId").val(),"status":$("#status").val(),"yearMonth":value});
            }
        });


        form.on('select(queryType)', function(data){
            var queryType = data.value;
            if(queryType=='month'){
                $("#yearMonthDiv").show();
                $("#yearMonthDayDiv").hide();
            }else{
                $("#yearMonthDiv").hide();
                $("#yearMonthDayDiv").show();
            }
        });



        locaTale=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });


            table.render({
                id : 'expiredCardTable'
                ,elem : '#expiredCardTable'
                ,method : 'POST'
                ,where:data
                ,url : '#(ctxPath)/fina/cardmanager/baseMemberCardReconciliationPageList'
                ,height: 'full-90'
                ,cellMinWidth: 80
                ,totalRow: true
                ,cols: [[
                    {field:'base_name', title: '基地名称',width:180, align: 'center', unresize: true}
                    ,{field:'card_number', title: '会员卡号', align: 'center', unresize: true}
                    ,{field:'full_name', title: '持卡人', align: 'center', unresize: true}
                    ,{field:'year_month', title: '月份', align: 'center', unresize: true}

                    ,{field:'total_times', title: '总扣卡天数', align: 'center', unresize: true }
                    ,{field:'total_amount', title: '总扣卡金额', align: 'center', unresize: true }
                    ,{field:'total_integrals', title: '总扣卡积分', align: 'center', unresize: true }

                    ,{field:'actual_total_times', title: '对账总扣卡天数', align: 'center', unresize: true }
                    ,{field:'actual_total_amount', title: '对账总扣卡金额', align: 'center', unresize: true }
                    ,{field:'actual_total_integrals', title: '对账总扣卡积分', align: 'center', unresize: true }
                    ,{field:'totalNum', title: '总账单数', align: 'center', unresize: true }
                    ,{field:'normalNum', title: '正常账单数', align: 'center', unresize: true }
                    ,{field:'exceptionNum', title: '异常账单数', align: 'center', unresize: true }
                    ,{field:'status', title: '状态', align: 'center', unresize: true,templet: function (d) {
                            if(d.status=='0'){
                                return '未审核';
                            }else{
                                return '已审核';
                            }
                        }}
                    , {fixed: 'right', title: '操作', align: 'center', unresize:true, toolbar: '#toolBar'}
                ]]
                ,page : false
                ,limit : 10
                ,limits : [10,20,30,40,50]
                ,done:function (){
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });

                    layer.close(loadingIndex);
                }
            });
        }

        table.on('tool(expiredCardTable)',function (obj) {
            if(obj.event=='edit'){
                layer.confirm("确定审核?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/fina/cardmanager/totalRecordReview",
                        type:'post',
                        data:{id:obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                reloadTable();
                            }
                            layer.close(index);
                        }
                    });
                });
            }else if(obj.event=='detail'){
                layerShow('详情','#(ctxPath)/fina/cardmanager/baseMemberCardReconciliationDetailIndex?id='+obj.data.id,'100%','100%');
            }
        })

        reloadTable=function(){
            locaTale({"cardId":$("#cardId").val(),"status":$("#status").val(),"yearMonth":$("#yearMonth").val()});
        }
        reloadTable();

        form.on('select(status)',function (obj) {
            reloadTable();
        })
        form.on('select(cardId)',function (obj) {
            reloadTable();
        })


        // 搜索
        $("#search").click(function(){
            locaTale({"cardId":$("#cardId").val(),"orderType":$("#orderType").val()})
        });

        $("#edit").click(function () {
            var checkTable = table.checkStatus('expiredCardTable');
            console.log(checkTable);
            if(checkTable.length<=0){
                layer.msg('请先勾选记录',{icon:5,time:5000});
                return false;
            }
            let ids=[];
            $.each(checkTable.data,function (index,item) {
                if(item.isReconciliation=='0'){
                    ids.push(item.id);
                }
            });
            console.log(ids);
            if(ids.length<=0){
                layer.msg('请勾选需要对账的记录',{icon:5,time:5000});
                return false;
            }
            layer.load();
            util.sendAjax({
                url:'#(ctxPath)/fina/cardmanager/cardReconciliationSave',
                type:'post',
                data:{'type':$("#orderType").val(),'ids':JSON.stringify(ids),"cardId":$("#cardId").val(),"yearMonth":$("#yearMonth").val()},
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        console.log("成功");
                        layer.closeAll('loading');
                        reloadTable();
                    }
                },
                unSuccess:function () {
                    layer.closeAll('loading');
                }
            });

            return false;
        })



    });
</script>
<script type="text/html" id="actionBar">

</script>
#end
