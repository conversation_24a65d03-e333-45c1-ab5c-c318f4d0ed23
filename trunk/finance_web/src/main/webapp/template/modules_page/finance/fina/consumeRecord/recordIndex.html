#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡消费记录#end

#define css()
<style>
.layui-table-cell {
	font-size:10px;
	padding:0 5px;
	height:auto;
	overflow:visible;
	text-overflow:inherit;
	white-space:normal;
	word-break: break-all;
}
</style>
#end

#define content()
<div class="my-btn-box">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
	<div class="layui-row">
		<div class="layui-inline">
			<label class="layui-form-label">会员卡类别</label>
			<div class="layui-input-inline">
				<select id="cardTypeId" name="cardTypeId" lay-search>
					<option value="">请选择卡类别</option>
					#for(t : typeList)
					<option value="#(t.id)" #(card != null ? (t.id == card.card_type_id?'selected':'') :'')>#(t.cardType)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label" style="width:80px;padding-left:0px;">会员卡号：</label>
			<div class="layui-input-inline">
				<input type="text" id="cardNumber" placeholder="请输入会员卡号" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label" style="width:80px;padding-left:5px;">所属会员：</label>
			<div class="layui-input-inline">
				<input type="text" id="fullName" placeholder="请输入所属会员" autocomplete="off" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label" style="width:80px;padding-left:5px;">扣卡时间：</label>
			<div class="layui-input-inline">
				<input type="text" id="startDate" name="startDate" readonly autocomplete="off" class="layui-input" placeholder="开始时间">
			</div>
			至
			<div class="layui-input-inline">
				<input type="text" id="endDate" name="endDate" readonly autocomplete="off" class="layui-input" placeholder="结束时间">
			</div>
		</div>
		<button type="button" id="searchConsumeBtn" class="layui-btn">搜索</button>
		#shiroHasPermission("finance:consumeRecordManage:exportBtn")
			<button type="button" id="exportBtn" class="layui-btn">导出</button>
		#end
	</div>
	</form>
	<div class="layui-row">
		<table id="consumeRecordTable" lay-filter="consumeRecordTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="barDemo">
	<a class="layui-btn layui-btn-xs" lay-event="edit">详情</a>
</script>
<script type="text/javascript">
// 	var table,$;
layui.use(['table','form','laydate'],function(){
var table = layui.table
	, laydate = layui.laydate
	, form = layui.form
	, $ = layui.$
	;

	form.render('select');

	function getStartFormatDate() {
		var date = new Date();
		var seperator1 = "-";
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var strDate = date.getDate();
		if (month >= 1 && month <= 9) {
			month = "0" + month;
		}
		if (strDate >= 0 && strDate <= 9) {
			strDate = "0" + strDate;
		}
		var currentdate = year + seperator1 + month + seperator1 + "01";
		return currentdate;
	}

	function getEndFormatDate() {
		var date = new Date();
		var seperator1 = "-";
		var year = date.getFullYear();
		var month = date.getMonth() + 1;
		var strDate = date.getDate();
		if (month >= 1 && month <= 9) {
			month = "0" + month;
		}
		if (strDate >= 0 && strDate <= 9) {
			strDate = "0" + strDate;
		}
		var currentdate = year + seperator1 + month + seperator1 + strDate;
		return currentdate;
	}

	laydate.render({
		elem:"#startDate",
		type:"date",
		range:false,
		format:"yyyy-MM-dd",
		value:getStartFormatDate()
		,btns: ['now','confirm']
	}) ;

	laydate.render({
		elem:"#endDate",
		type:"date",
		range:false,
		format:"yyyy-MM-dd",
		value:getEndFormatDate()
		,btns: ['now','confirm']
	}) ;

	//加载会员消费信息
	function loadConsumeRecordTable(data){
		//loading层
		var loadingIndex = layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		table.render({
			id : 'consumeRecordTable',
			elem : "#consumeRecordTable" ,
			url : '#(ctxPath)/fina/consumerecord/getConsumeRecord' ,
			where: data,
			height:'full-90',
			cols : [[
				{field:'dealTime',title:'结算时间',width:130,unresize:false,templet:"<div>{{dateFormat(d.dealTime,'yyyy-MM-dd HH:mm:ss')}}</div>"},
				{field:'describe',title:'说明',unresize:false,style:"text-align:left;font-size:10px;color:#000;"},
				{field:'fullName',title:'卡主姓名',width:80,unresize:false},
				{field:'cardNumber',title:'会员卡号',width:90,unresize:false},
				{field:'',title:'消费渠道',width:150,unresize:false, templet:'#statusTpl(com.cszn.integrated.service.entity.status.ConsumeType::me(), "consumeType")'},
				{field:'cardType',title:'卡类别',width:150,unresize:false},
				{field:'',title:'消费类型',width:80,unresize:false,templet:
							"<div>{{ d.type=='recharge_prestore'?'<span class='layui-badge layui-bg-green'>充值</span>'"+
							":d.type=='give_prestore'?'<span class='layui-badge layui-bg-blue'>赠送</span>'" +
							":d.type=='book_deduction'?'<span class='layui-badge layui-bg-orange'>预订扣除</span>'"+
							":d.type=='checkin_deduction'?'<span class='layui-badge layui-bg'>入住扣除</span>'"+
							":d.type=='daily_deduction'?'<span class='layui-badge layui-bg-cyan'>日常扣除</span>'"+
							":d.type=='cancel_recharge'?'<span class='layui-badge layui-bg'>撤销充值</span>'"+
							":d.type=='cancel_give'?'<span class='layui-badge layui-bg'>撤销赠送</span>'"+
							":d.type=='transfer_deduction'?'<span class='layui-badge layui-bg'>过户扣除</span>'"+
							":d.type=='transfer_recharge'?'<span class='layui-badge layui-bg-green'>过户转移</span>'"+
							":d.type=='upgrade_deduction'?'<span class='layui-badge layui-bg'>升级扣除</span>'"+
							":d.type=='upgrade_recharge'?'<span class='layui-badge layui-bg-green'>升级转移</span>'"+
							":d.type=='return_card_deduction'?'<span class='layui-badge layui-bg'>退卡扣除</span>'"+
							":d.type=='recharge_deduction'?'<span class='layui-badge layui-bg'>充值扣除</span>'"+
							":d.type=='give_deduction'?'<span class='layui-badge layui-bg'>赠送扣除</span>'"+
							":'- -' }}</div>"
				},
				{field:'times',title:'天数',width:76,unresize:false,templet: "<div>{{ String(d.times)?d.times + '天':'- -' }}</div>"},
				{field:'amount',title:'金额',width:76,unresize:false,templet: "<div>{{ String(d.amount)?d.amount + '元':'- -' }}</div>"},
				{field:'points',title:'点数',width:76,unresize:false,templet: function (d) {
						if(typeof(d.points)==='undefined'){
							return '0.0'
						}else{
							return d.points;
						}
					}},
				{field:'integrals',title:'积分',width:76,unresize:false,templet: function (d) {
						if(typeof(d.integrals)==='undefined'){
							return '0.0'
						}else{
							return d.integrals;
						}
					}},
				{field:'beanCoupons',title:'豆豆券',width:76,unresize:false,templet: function (d) {
						if(typeof(d.beanCoupons)==='undefined'){
							return '0.0'
						}else{
							return d.beanCoupons;
						}
					}},
				{field:'timesSnapshot',title:'天数快照',width:80,unresize:false,templet: "<div>{{ String(d.timesSnapshot)?d.timesSnapshot + '天':'- -' }}</div>"},
				{field:'amountSnapshot',title:'金额快照',width:80,unresize:false,templet: "<div>{{ String(d.amountSnapshot)?d.amountSnapshot + '元':'- -' }}</div>"},
				{field:'pointsSnapshot',title:'点数快照',width:80,unresize:false,templet: function (d) {
						if(typeof(d.pointsSnapshot)==='undefined'){
							return '0.0';
						}else{
							return d.pointsSnapshot;
						}
					}},
				{field:'integralsSnapshot',title:'积分快照',width:80,unresize:false,templet: function (d) {
						if(typeof(d.integralsSnapshot)==='undefined'){
							return '0.0';
						}else{
							return d.integralsSnapshot;
						}
					}},
				{field:'beanCouponsSnapshot',title:'豆豆券快照',width:80,unresize:false,templet: function (d) {
						if(typeof(d.beanCouponsSnapshot)==='undefined'){
							return '0.0';
						}else{
							return d.beanCouponsSnapshot;
						}
					}}
			]] ,
			page : true,
			limit : 6
			, done: function (res, curr, count) {
				// layer.closeAll('loading');
				layer.close(loadingIndex);
			}
		}) ;
	}

	loadConsumeRecordTable({inOutFlag:'2',startDate:getStartFormatDate(),endDate:getEndFormatDate()});

	// 搜索消费记录按钮
	$('#searchConsumeBtn').on('click', function(){
		var data = {cardTypeId:$('#cardTypeId').val(),cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),inOutFlag:'2',startDate:$('#startDate').val(),endDate:$('#endDate').val()};
		loadConsumeRecordTable(data);
	});

	//回车事件
	document.onkeydown = function(e){
		var ev =document.all ? window.event:e;
		if(ev.keyCode==13) {
			$('#searchConsumeBtn').click();
			return false
		}
	}

	//导出按钮点击事件
	$("#exportBtn").on('click',function () {
		layer.load();
		var param = {cardTypeId:$('#cardTypeId').val(),cardNumber:$('#cardNumber').val(),fullName:$('#fullName').val(),inOutFlag:'2',startDate:$('#startDate').val(),endDate:$('#endDate').val()};
		/*$.post('#(ctxPath)/fina/consumerecord/getConsumeRecord',param,function (res) {
			if(res.count==0){
				layer.msg('导出的内容为空', {icon: 2, offset: 'auto'});
			}else{
				var url='#(ctxPath)/fina/consumerecord/exportRecord?cardNumber='+$('#cardNumber').val()+"&fullName="+$('#fullName').val()+"&inOutFlag=2&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val();
				window.location.href=url;
			}
		});*/

		tableReload('consumeRecordTable',param);
		if(table.cache.consumeRecordTable.length>0){
			var url='#(ctxPath)/fina/consumerecord/exportRecord?cardTypeId='+$('#cardTypeId').val()+"&cardNumber="+$('#cardNumber').val()+"&fullName="+$('#fullName').val()+"&inOutFlag=2&startDate="+$('#startDate').val()+"&endDate="+$('#endDate').val();
			window.location.href=url;
			setTimeout(function (){
				layer.closeAll('loading');
			},20000);
		}else{
			layer.closeAll('loading');
			layer.msg('导出的内容为空', {icon: 2, offset: 'auto'});
		}

	});
});
</script>
#end