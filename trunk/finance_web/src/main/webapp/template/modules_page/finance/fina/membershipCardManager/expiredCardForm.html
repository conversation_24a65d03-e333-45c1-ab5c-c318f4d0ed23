#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()过期恢复页面#end

#define css()
<style>
    .co {
        color: #ff0000;
    }
</style>
#end

#define content()
<form class="layui-form layui-form-pane" id="cardExpiredForm" action="" method="post">
    <input type="hidden" id="idh" name="id" value="#(id??)"/>
    <input type="hidden" name="expireFlag" value="0"/>
    <div class="layui-form-item" style="margin-top: 10px;">
        <label class="layui-form-label"><span class="co">*</span>最大有效期</label>
        <div class="layui-input-block">
            <input type="text" id="expireDate" name="expireDate" class="layui-input" value="#(expireDate??)" lay-verify="required" autocomplete="off" placeholder="请选择最大有效期">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><span class="co">*</span>延期备注</label>
        <div class="layui-input-block">
            <textarea id="delayRemark" name="delayRemark" class="layui-textarea" rows="5" lay-verify="required" placeholder="请输入延期备注"></textarea>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        </div>
    </div>
</form>
#end

#define js()
<script>
    layui.use(['form', 'laydate'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var oldExpireDate = '#(expireDate??)';

        //时间渲染
        laydate.render({
            elem : '#expireDate',
            trigger: 'click'
        });

        form.on('submit(saveBtn)',function () {
            var newExpireDate = $("#expireDate").val();
            // if(newExpireDate!='' && oldExpireDate!='' && newExpireDate <= oldExpireDate){
            //     layer.msg('设置的最大有效期必须大于原来的最大有效期', {icon: 2, offset: 'auto'})
            //     return false;
            // }
            util.sendAjax({
                url:'#(ctxPath)/fina/cardmanager/expiredCardSave',
                type:'post',
                data:$("#cardExpiredForm").serialize(),
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        pop_close();
                        parent.formOperateReload();
                    }
                }
            });
            return false;
        });
    });
</script>
#end