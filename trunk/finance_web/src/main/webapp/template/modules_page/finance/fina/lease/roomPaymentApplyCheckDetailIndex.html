#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡类别页面#end

#define css()
<style>
    .layui-tab  td, .layui-tab th {
        padding: 9px 5px;
    }

    .layui-table-cell {
        padding: 0 5px;
    }

    .layui-table-cell{
        overflow: visible;
    }
    .layui-layer-tips{
        display: none
    }
    /* 设置下拉框的高度与表格单元相同 */
    td .layui-form-select{
        margin-top: -4px;
        margin-left: -2px;
        margin-right: -3px;
    }

    .layui-disabled, .layui-disabled:hover {
        color: #000000 !important;
        cursor: not-allowed !important;
    }

    @media print {
        #tableDiv{
            display:block;
        }
    }

</style>
#end

#define content()
<div class="my-btn-box">

    <input type="hidden" id="id" value="#(id??)">
    <div class="layui-row">
        <table id="cardTypeTable" lay-filter="cardTypeTable"></table>
    </div>
    <div class="layui-row"  style="display: none;">
        <div class="layui-row" id="tableDiv" >
            <table id="cardTypeTablePrint" lay-filter="cardTypeTablePrint"></table>
        </div>
    </div>
    <div class="layui-form-footer" id="footer">
        <div class="pull-right">
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button class="layui-btn  " type="button" id="printBtn" >打印</button>
            #if(type??!='view')
            <button id="confirmBtn" class="layui-btn" type="button" lay-filter="confirmBtn">保存</button>
            #end
        </div>
    </div>
</div>


#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/js/jQuery.print.js"></script>
<script type="text/html" id="toolBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">移除</a>
    </div>
</script>
<script type="text/javascript">
    layui.use(['table','laydate','layer'],function(){
        var table = layui.table
            , $ = layui.$
            ,layer=layui.layer
        ;
        var laydate=layui.laydate;
        laydate.render({
            elem: '#dateRange'
            ,trigger:'click'
            ,range: true
            ,value:"#(dateRange??)"
        });
        /*laydate.render({
            elem: '#roomEndDate'
            ,trigger:'click'
            ,range: true
        });*/



        cardTypeTableLoad=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTable',
                elem:"#cardTypeTable",
                url:'#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetaiList',
                where:data,
                //toolbar: true,
                height:'full-90',
                cols:[[
                    //{type:'checkbox'}
                    {field:'numbers',title:'序号',width:70, align:'center',unresize:true,templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return d.LAY_INDEX;
                        }}
                    ,{field: 'supplierName', title: '业主', width:100,unresize:true}
                    ,{field:'room_name', title:'房间号', width:90, unresize:true, align:'center'}

                    ,{field:'start_date', title:'本期时间', width:180 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return dateFormat(d.start_date,"yyyy-MM-dd")+"至"+dateFormat(d.end_date,"yyyy-MM-dd")
                        }}
                    ,{field:'status', title:'当前期数/总期数', width:150 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return d.issue_no+"/"+d.maxIssueNo
                        }}
                    ,{field:'amount', title:'本期租金', width:100 , unresize:true, align:'center'}
                    ,{field:'payStatus', title:'支付状态', width:100 , unresize:true, align:'center',templet:function (d) {
                        if(d.room_name==undefined){
                            return '';
                        }
                        let success='';
                        let fail='';

                        let readonlyStr="";
                        #if(type??=='view' && paymentApply.status??=='3')
                            readonlyStr="disabled";
                            if(d.status=='1'){
                                success='selected';
                            }else if(d.status=='0'){
                                fail='selected';
                            }
                        #elseif(type??=='view' && paymentApply.status??!='3')
                            readonlyStr="disabled";
                            success='';
                            fail='';
                        #else
                            if(d.status=='1'){
                                success='selected';
                            }else if(d.status=='0'){
                                fail='selected';
                            }else{
                                success='selected'
                            }
                        #end
                        return '<div class="layui-input-inline" > ' +
                            '<select style="height: 26px;" id="'+d.id+'_payStatus" '+readonlyStr+' >' +
                            '   <option value="">请选择</option>' +
                            '   <option value="1" '+success+'>已付款</option>' +
                            '   <option value="0" '+fail+'>未付款</option>' +
                            '</select> ' +
                            '</div>'
                    }}
                    ,{field:'auAmount', title:'实际支付租金', width:100 , unresize:true,templet:function (d) {
                        if(d.room_name==undefined){
                            return d.pay_amount;
                        }
                        let amount=0.0;

                        let readonlyStr="";

                        #if(type??=='view' && paymentApply.status??=='3')
                            readonlyStr="readonly";
                            amount=d.pay_amount;
                        #elseif(type??=='view' && paymentApply.status??!='3')
                            readonlyStr="readonly";
                            amount='';
                        #else
                            if(d.amount>0){
                                amount=d.amount;
                            }
                            if(d.pay_amount>0){
                                amount=d.pay_amount;
                            }
                        #end

                        return '<div class="layui-input-inline" > ' +
                            '<input type="text"  id="'+d.id+'_payAmount" '+readonlyStr+' style="height: 26px;" min="0" value="'+amount+'" lay-verify="checkNumber|required" onkeyup="calculateActualTimes(this)" class="layui-input"> ' +
                            '</div>';
                    }}
                    ,{field:'auAmount', title:'实际支付方式', width:100 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            let readonlyStr='';
                            let payType=d.pay_type;

                            #if(type??=='view' && paymentApply.status??=='3')
                                readonlyStr="disabled";

                            #elseif(type??=='view' && paymentApply.status??!='3')
                                readonlyStr="disabled";
                                payType='';
                            #else
                                if(d.pay_type==undefined || d.pay_type==''){
                                    payType='7';
                                }
                            #end


                            let str='<div class="layui-input-inline"  > ' +
                                '<select style="height: 26px;" id="'+d.id+'_payType" '+readonlyStr+' >' +
                                '   <option value="">请选择</option>';
                            #for(pay : payWay)
                            if('#(pay.key)'==payType){
                                str+='<option value="#(pay.key)" selected   >#(pay.value)</option>';
                            }else{
                                str+='<option value="#(pay.key)"   >#(pay.value)</option>';
                            }
                            #end
                            str+='</select> ' +
                                '</div>';
                            return str;
                        }}
                    ,{field:'remark', title:'备注' , unresize:true, align:'center',templet:function (d) {
                        if(d.room_name==undefined){
                            return '';
                        }
                        let readonlyStr="";
                        let remark=d.remark;
                        if(d.remark==undefined){
                            remark='';
                        }
                        #if(type??=='view' && paymentApply.status??=='3')
                            readonlyStr="readonly";

                        #elseif(type??=='view' && paymentApply.status??!='3')
                            readonlyStr="readonly";
                            remark='';
                        #else

                        #end

                        return '<div class="layui-input-block" style="margin-left: 0px;" > ' +
                            '<input type="text" name="" id="'+d.id+'_remark" style="height: 26px;" min="0"  '+readonlyStr+'  lay-verify="" value="'+remark+'" class="layui-input"> ' +
                            '</div>';
                    }}
                    #if(type??!='view')
                    ,{title:'操作',width:220, fixed:'right',unresize:true,toolbar:'#toolBar'}
                    #end
                ]],
                page:false
                ,done: function (res, curr, count) {

                    layer.close(loadingIndex);
                }
            });
            table.on('tool(cardTypeTable)',function(obj){
                if(obj.event === 'detail'){
                    layerShow('详情','#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetailIndex?id='+obj.data.id,'100%','100%');
                    //layerShow("旅居消费详情",'#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetailIndex?id='+obj.data.id,'100%','100%');

                }else if(obj.event==='see'){
                    layerShow('付款单','#(ctxPath)/fina/leaseRecordApply/previewTableIndex?leaseId='+obj.data.leaseId+"&roomId="+obj.data.roomId,1000,600);
                }else if(obj.event==='file'){
                    layerShow(obj.data.baseName+'['+obj.data.roomName+']的附件','#(ctxPath)/fina/leaseRecordApply/leaseRoomFileIndex?id='+obj.data.id,800,600);
                }else if(obj.event==='del'){
                    layer.confirm('您确定要移除该记录吗？', {icon: 3, title: '提示'}, function (index) {
                        util.sendAjax({
                            type: 'POST',
                            url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApplyStatus',
                            data: {id: obj.data.id, status: '2'},
                            loadFlag: true,
                            success: function (rep) {
                                if (rep.state == 'ok') {
                                    reloadCardTypeTable();
                                }
                            },
                            complete: function () {
                            }
                        });
                        layer.close(index);
                    });
                }
            });
        }

        cardTypeTableLoadPrint=function(data){
            //loading层
            var loadingIndex = layer.load(2, { //icon支持传入0-2
                shade: [0.5, 'gray'], //0.5透明度的灰色背景
                content: '加载中...',
                success: function (layero) {
                    layero.find('.layui-layer-content').css({
                        'padding-top': '39px',
                        'width': '60px'
                    });
                }
            });
            table.render({
                id:'cardTypeTablePrint',
                elem:"#cardTypeTablePrint",
                url:'#(ctxPath)/fina/leaseRecordApply/roomPaymentApplyCheckDetaiList',
                where:data,
                height:'full-90',
                cols:[[
                    //{type:'checkbox'}
                    {field:'numbers',title:'序号',width:70,unresize:true,templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return d.LAY_INDEX;
                        }}
                    ,{field: 'supplierName', title: '业主', width:100,unresize:true}
                    ,{field:'room_name', title:'房间号', width:90, unresize:true, align:'center'}
                    ,{field:'rent', title:'租金', width:80, unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            let unit='';
                            if(d.rent_unit=='year'){
                                unit="/年";
                            }else if(d.rent_unit=='month'){
                                unit="/月"
                            }else{
                            }
                            return d.rent+unit;

                        }}
                    ,{field:'rentPay1', title:'房租支付方式', width:100 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            let unit='';
                            if(d.payment_type=='year') {
                                unit = "年";
                            }else if(d.payment_type=='halfYear'){
                                unit="半年";
                            }else if(d.payment_type=='season'){
                                unit="季";
                            }else if(d.payment_type=='month'){
                                unit="月"
                            }else if(d.payment_type=='disposable'){
                                unit="一次性"
                            }
                            return unit+"付";
                        }}
                    ,{field:'rentPay', title:'房租付款期限', width:180 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return dateFormat(d.contract_start_date,"yyyy-MM-dd")+"至"+dateFormat(d.contract_end_date,"yyyy-MM-dd");
                        }}
                    ,{field:'start_date', title:'本期时间', width:180 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return dateFormat(d.start_date,"yyyy-MM-dd")+"至"+dateFormat(d.end_date,"yyyy-MM-dd")
                        }}
                    ,{field:'status', title:'当前期数/总期数', width:120 , unresize:true, align:'center',templet:function (d) {
                            if(d.room_name==undefined){
                                return '';
                            }
                            return d.issue_no+"/"+d.maxIssueNo
                        }}
                    ,{field:'amount', title:'本期租金', width:120 , unresize:true, align:'center'}
                ]],
                page:false
                ,done: function (res, curr, count) {

                    layer.close(loadingIndex);
                }
            });

        }

        reloadCardTypeTable=function(){
            cardTypeTableLoad({id:$('#id').val()});
            cardTypeTableLoadPrint({id:$('#id').val()})
        }

        reloadCardTypeTable();

        // 搜索消费记录按钮
        $('#searchConsumeBtn').on('click', function(){
            reloadCardTypeTable();
        });

        //回车事件
        document.onkeydown = function(e){
            var ev =document.all ? window.event:e;
            if(ev.keyCode==13) {
                $('#searchConsumeBtn').click();
                return false
            }
        }

        $("#printBtn").on('click',function () {
            print('cardTypeTableLoadPrint');
        });

        function print(tablelayid) {
            var v = document.createElement("div");
            var f = ["<head>", "<style>", "body{font-size: 12px; color: #666;}",
                "table{width: 100%; border-collapse: collapse; border-spacing: 0;}",
                "th,td{line-height: 20px; padding: 9px 15px; border: 1px solid #ccc; text-align: left; font-size: 12px; color: #666;}",
                "a{color: #666; text-decoration:none;}", "*.layui-hide{display: none}", "</style>", "</head>"
            ].join("");
            $(v).append("<p style='text-align: center;font-weight: bold;font-size: 14px;'>#(base.baseName??)#(paymentApply.title??)</p>");

            let str='';
            #if(submitUser!=null)
                str+='提交人：#(submitUser.name??)'
            #end
            #if(checkUser!=null)
            str+='，审核人：#(checkUser.name??)'
            #end
            #if(approveUser!=null)
            str+='，出纳付款人：#(approveUser.name??)'
            #end
            if(str!=''){
                $(v).append("<p style='text-align: right;font-size: 12px;'>"+str+"</p>");
            }
            $(v).append($("#tableDiv .layui-table-box").find(".layui-table-header").html());
            $(v).find("tr").after($("#tableDiv .layui-table-body.layui-table-main table").html());
            $(v).find("th.layui-table-patch").remove();
            $(v).find(".layui-table-col-special").remove();
            var h = window.open("打印窗口", "_blank");
            h.document.write(f + $(v).prop("outerHTML"));
            h.document.close();
            h.print();
            h.close();
        }


        $("#confirmBtn").click(function () {
            let saveData=[];
            $.each(table.cache.cardTypeTable,function (index,item) {
                let id=item.id;
                let payStatus=$("#"+id+"_payStatus").val();
                let payAmount=$("#"+id+"_payAmount").val();
                let payType=$("#"+id+"_payType").val();
                let remark=$("#"+id+"_remark").val();
                if(id==null || id==undefined || id==''){
                    return true;
                }
                if(remark==undefined){
                    remark='';
                }
                saveData.push({id,payStatus,payAmount,payType,remark})
            });

            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/leaseRecordApply/saveRoomPaymentApplyDetailStatus',
                data: {dataStr:JSON.stringify(saveData),'applyId':$("#id").val()},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        //pageTableReload();
                        reloadCardTypeTable();
                        layer.closeAll("loading");
                    }
                },
                complete : function() {
                }
            });
            return false;
        })

    });
</script>
#end