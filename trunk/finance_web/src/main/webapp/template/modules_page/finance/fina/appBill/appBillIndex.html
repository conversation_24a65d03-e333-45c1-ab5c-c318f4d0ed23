#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()应用消费记录列表#end

#define css()
#end

#define content()
<div class="layui-collapse">
	<div class="layui-row">
		<form id="frm" class="layui-form" action="" lay-filter="layform" method="post" style="margin-left:25px;margin-top:15px;">
			<div class="layui-row">
			入住基地：
			<div class="layui-inline">
				<select name="baseId" lay-search>
					<option value="">请选择入住基地</option>
					#for(b : baseList)
					<option value="#(b.id)">#(b.baseName)</option>
					#end
				</select>
			</div>
			&nbsp;&nbsp;
			会员卡号：
			<div class="layui-inline">
				<input type="text" class="layui-input" name="cardNumber"/>
			</div>
			&nbsp;&nbsp;
			姓名：
			<div class="layui-inline">
				<input type="text" class="layui-input" name="fullName"/>
			</div>
			</div>
			&nbsp;&nbsp;
			<div class="layui-row">
			<div style="margin-left: 12px;float:left;">
			订单号：
			<div class="layui-inline">
				<input id="appOrderNo" name="appOrderNo" class="layui-input" style="width:117%;">
			</div>
			</div>
			<div style="float:left;margin-left: 46px;">
			消费时间：
			<div class="layui-inline">
				<div class="laydate-input-inline" style="display:inline-block;">
					<input type="text" id="startDate" name="startDate" autocomplete="off" class="layui-input" placeholder="开始时间">
				</div>
				<div class="span-group-laydate-addon" style="display:inline-block;">至</div>
				<div class="laydate-input-inline" style="display:inline-block;margin-left:36px;">
					<input type="text" id="endDate" name="endDate" autocomplete="off" class="layui-input" placeholder="结束时间">
				</div>
			</div>
				<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
			</div>
			</div>
		</form>
		<!--
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
		 -->
	</div>
	<div class="layui-row">
		<table id="billTable" lay-filter="billTable"></table>
	</div>
</div>
#getDictLabel("account_record_operate_type")
#end
<!-- 公共JS文件 -->
#getDictLabel("gender")
#define js()
<script>
layui.use(['form','layer','table','laydate'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

	//日期范围
	laydate.render({
        elem:"#startDate",
        type:"date",
        range:false,
        format:"yyyy-MM-dd"    //设置日期呈现的格式
    }) ;

    laydate.render({
        elem:"#endDate",
        type:"date",
        range:false,
        format:"yyyy-MM-dd"    //设置日期呈现的格式
    }) ;
	billLoad(null);
	
	sd=form.on("submit(search)",function(data){
		billLoad(data.field);
		return false;
	});
	
	
	function billLoad(data){
			table.render({
				id : 'billTable'
				,elem : '#billTable'
				,method : 'POST'
				,where : data
				,height:$(document).height()*0.75
				,limit : 10
				,limits : [20,30,40,50]
				,url : '#(ctxPath)/fina/appBill/findListPage'
				,cellMinWidth: 80
				,cols: [[
					/*{type:'checkbox'},
					{type: 'numbers', width:100, title: '序号',unresize:true}*/
					{field:'fullName', title: '客户姓名', align: 'center', unresize: false,width:120}
					,{field:'baseName', title: '入住基地', align: 'center', unresize: false,width:120}
					,{field:'appOrderNo', title: '应用订单号', align: 'center', unresize: false,width:180}
					,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: false,width:120}
					,{field:'consumeType', title: '消费类型', align: 'center',width:100, unresize: false,templet: "<div>{{ dictLabel(d.consumeType,'account_record_operate_type','- -') }}</div>"}
					,{field:'describe', title: '消费描述', align: 'center', unresize: false,style:"text-align:left;font-size:10px;color:#000;"}
					,{field:'consumeTimes', title: '天数', align: 'center', unresize: true,width:60}
					,{field:'amount', title: '金额', align: 'center', unresize: true,width:80}
					,{field:'status', title: '状态', align: 'center', unresize: true,width:80,templet:"<div>{{d.status == '0'?'<span class='layui-badge layui-bg-green'>未处理</span>':d.status == '1'?'<span class='layui-badge'>已处理</span>':d.status == '2'?'<span class='layui-badge layui-bg-gray'>作废</span>':'- -'}}</div>"}
					,{field:'takeTime', title: '消费时间', sort: true, align: 'center', width:160,unresize: true,templet:"<div>{{ dateFormat(d.takeTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
	};

	table.on('tool(billTable)',function(obj){
		if (obj.event === 'del') {
			layer.confirm("确定要作废吗?",function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/fina/appBill/delete',
					data: {id:obj.data.id},
					notice:true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							table.reload('billTable');
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else if(obj.event === 'detail'){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/fina/appBill/cardExist',
				data: {'cardNumber':obj.data.cardNumber},
				notice:false,
				loadFlag: true,
				success : function(rep){
					if(rep.state==='ok'){
						var url = "#(ctxPath)/fina/appBill/form?id=" + obj.data.id ;
						layerShow("订单详情",url,900,800);
					}else{
						layer.msg(rep.msg, {icon: 5, offset: 'auto'});
					}
				},
				complete : function() {
				}
			});
		}else if(obj.event === 'append'){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/fina/appBill/cardExist',
				data: {'cardNumber':obj.data.cardNumber},
				notice:false,
				loadFlag: true,
				success : function(rep){
					if(rep.state==='ok'){
						var url = "#(ctxPath)/fina/appBill/form?id=" + obj.data.id +"&type=append" ;
						layerShow("补扣详情",url,800,650);
					}else{
						layer.msg(rep.msg, {icon: 5, offset: 'auto'});
					}
				},
				complete : function() {
				}
			});
		}
	});

	<!--region Description-->
	/*//批量获取被作废数据
	getCheckTableData = function(){
		var memberCheckStatus = table.checkStatus('billTable');
		// 获取选择状态下的数据
		return memberCheckStatus.data;
	}

	//批量作废
	$("#batchDel").click(function(){
		layer.confirm("确定批量作废吗?",function(index){
			var jsonData=getCheckTableData();
			if(jsonData == null || jsonData == ''){
				layer.msg('请勾选作废数据', function () {});
				return;
			}
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/fina/fina/batchDel',
				data: {memberData:JSON.stringify(jsonData)},
				notice: true,
				loadFlag: true,
				success : function(rep){
					if(rep.state=='ok'){
						table.reload('memberTable') ;
					}
				},
				complete : function() {
				}
			});
			layer.close(index);
		});
	});*/
	<!--endregion-->
});
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("finance:deductions:detailBtn")
	<a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
	#end

	#shiroHasPermission("finance:deductions:appendBtn")
	#[[
	{{#if(d.status=='1' && d.refundFlag != '1'){}}
	<a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="append">补扣</a>
	{{#}}}
	]]#
	#end

	#shiroHasPermission("finance:deductions:delBtn")
	#[[
	{{#if(d.status!='1'){}}
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	{{#}}}
	]]#
	#end
</script>
#end