#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/layui/css/layui.css"  media="all">
<style>
    .layui-form-label {
        width: 130px !important;
        text-align: center !important;
    }
    .layui-input-block {
        margin-left: 130px !important;
    }
    .layui-select-disabled .layui-disabled {
        color: black!important;
        border-color: #eee!important;
    }
    input[type="text"]:disabled {
        background-color: #fff;
    }
    #roomTable  td, #roomTable th {
        padding: 9px 5px;
    }

    /*#roomTable .layui-body{overflow-y: scroll;}*/

    #roomTable .layui-anim {
        position : fixed;
    }
    .layui-form-item{
        margin-bottom: 8px;
    }

    xm-select > .xm-body {
        width: 400% !important;
        left: -200px !important;
    }
</style>
#end

#define js()
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">

    layui.use(['form','laytpl','laydate','upload'],function(){
        var form = layui.form ;
        var $ = layui.$ ;
        var laytpl = layui.laytpl;
        var laydate=layui.laydate;
        var upload=layui.upload;


// 		form.render('radio');

        //校验
        form.verify({
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                    if(value.length != 7 && value.length != 8){
                        return "号段固定为7位或8位";
                    }
                }
            },
            checkPrefix:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                    if(value.length != 2){
                        return "卡号前缀固定为2位";
                    }
                }
            }
        });


        form.on('select(deptId)',function (d) {
            let dataStr=$("#deptId option[value='"+d.value+"']").attr("data");
            console.log(dataStr);
            let positionList=JSON.parse(dataStr);
            let positionStr="<option value=''>请选择</option>";
            $.each(positionList,function (index,item) {
                if(positionList.length==1){
                    positionStr+="<option value='"+item.id+"' selected>"+item.positionName+"</option>";
                }else{
                    positionStr+="<option value='"+item.id+"'>"+item.positionName+"</option>";
                }
            });
            console.log(positionStr);
            $("#positionId").html(positionStr);
            form.render('select')
        });





        $("#proprietorBtn").on('click',function () {
            layerShow('选择业主','#(ctxPath)/fina/leaseRecordApply/proprietorIndex',900,600);
            return false;
        });

        selectedProprietor=function(data){
            $("#proprietorName").val(data.name);
            $("#supplierId").val(data.id);
            $.post('#(ctxPath)/fina/leaseRecordApply/suppliersPayList',{'supplierId':data.id},function (res) {
                if(res.length==0){
                    layer.msg('该供应商的付款方式为空，请先维护', {icon:5,time: 2000});
                    return false;
                }
                let str="<option value=''>请选择</option>"
                $.each(res,function (index,item) {
                    str+="<option value='"+item.id+"'>"+item.paymentWayStr+"("+item.pay_account+")"+"</option>"
                })
                $("#suppliersPayId").html(str);
                form.render('select','suppliersPayIdDiv');
            });
        }

        $("#addRoom").on('click',function () {
            addRoomTpl();

            $("#roomTable .layui-form-select").on('click',function(){
                var scrollTop=$(document).scrollTop();//获取页面滚动的高度
                var width = $(this).width();
                var top = $(this).offset().top;
                var left = $(this).offset().left;
                $(this).find("dl").css({"min-width":width+"px", top:top-scrollTop+40+"px", left:left+"px"});
            });

        });
        $("#roomTable .layui-form-select").on('click',function(){
            var scrollTop=$(document).scrollTop();//获取页面滚动的高度
            var width = $(this).width();
            var top = $(this).offset().top;
            var left = $(this).offset().left;
            $(this).find("dl").css({"min-width":width+"px", top:top-scrollTop+40+"px", left:left+"px"});
        });



        addRoomTpl = function () {
            let roomCount=Number($('#roomCount').val())+1;
            $('#roomCount').val(roomCount);
            laytpl(roomTrTpl.innerHTML).render({
                'idx': roomCount
            }, function (html) {
                $("#roomTbody").append(html);
            });

            //时间渲染
            laydate.render({
                elem: '#contractDate' + roomCount
                ,trigger:'click'
                ,range: true
            });
            laydate.render({
                elem: '#roomDate' + roomCount
                ,trigger:'click'
                ,range: true
            });

            //渲染

            /*laydate.render({
                elem: '#paymentDate' + roomCount
                ,trigger:'click'
                ,
            });*/

            //渲染xs-select
            loadXsSelect(roomCount);

            form.render('select');
        };

        loadXsSelect=function(roomCount){
            var demo1 = xmSelect.render({
                el: '#uploadDiv'+roomCount,
                prop: {
                    name: 'username',
                    value: 'username',
                },
                tips:'点击上传附件',
                content: `<div class="layui-row" style="padding: 20px 20px;">
                            <div class="layui-btn-container">
                              <button type="button" style="float: right;" class="layui-btn" id="uploadBtn`+roomCount+`"><i class="layui-icon"></i>上传文件</button>
                            </div>
                            <table class="layui-table" lay-filter="demo">
                              <thead>
                                <tr>
                                  <th  >文件</th>
                                  <th  >操作</th>
                                </tr>
                              </thead>
                              <tbody id="fileTbody`+roomCount+`">

                              </tbody>
                            </table>
                         </div>
                        `,
                /*style:{'width':500},*/
                height: 'auto',
                done:function () {


                },
                show(){

                    if("1"!=$("#uploadBtn"+roomCount).attr('isRender')){
                        //第一次
                        let initRoomFileStr=$("#roomFile"+roomCount).val();
                        let initRoomFileArray=[];
                        if(initRoomFileStr!=''){
                            initRoomFileArray=JSON.parse(initRoomFileStr);
                            $.each(initRoomFileArray,function (index,item) {
                                let newTrIndex=1;
                                if($("#fileTbody"+roomCount+" tr").length>0){
                                    newTrIndex=Number($("#fileTbody"+roomCount+" tr:last").attr("tr-index"))+1;
                                }
                                $("#fileTbody"+roomCount).append(`<tr tr-index="`+newTrIndex+`">
                                                <td>`+item.fileName+`</td>
                                                <td>
                                                    <button class="layui-btn layui-btn-xs" type="button" onclick="previewFile('`+item.fileUrl+`')">预览</button>
                                                   #if((isSaveHandle || taskId==null) && isEdit)<button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delFile(`+roomCount+`,`+newTrIndex+`,'`+item.id+`','`+item.fileUrl+`')">作废</button>#end
                                                </td>
                                           </tr>`);
                            })
                        }
                        $("#uploadBtn"+roomCount).attr('isRender','1');
                        upload.render({
                            elem: '#uploadBtn'+roomCount
                            ,url: '#(commonUpload)/upload?bucket=finaLeaseFile' //此处配置你自己的上传接口即可
                            ,accept: 'file' //普通文件
                            ,exts:'pdf|jpg|png'
                            ,size:1024*10//大小限制10M
                            ,before: function(obj){ //obj参数包含的信息，跟 choose回调完全一致，可参见上文。
                                layer.load(); //上传loading
                            }
                            ,done: function(res){
                                layer.closeAll('loading');
                                if(res.state=='ok'){
                                    layer.msg('上传成功点击保存生效',{icon:1,time:3000});

                                    let newTrIndex=1;
                                    if($("#fileTbody"+roomCount+" tr").length>0){
                                        newTrIndex=Number($("#fileTbody"+roomCount+" tr:last").attr("tr-index"))+1;
                                    }

                                    $("#fileTbody"+roomCount).append(`<tr tr-index="`+newTrIndex+`">
                                                <td>`+res.data.oldName+`</td>
                                                <td>
                                                    <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="previewFile('`+res.data.src+`')">预览</button>
                                                   <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delFile(`+roomCount+`,`+newTrIndex+`,'','`+res.data.src+`')">作废</button>
                                                </td>
                                           </tr>`);
                                    let roomFileDataStr=$("#roomFile"+roomCount).val();
                                    let fileDataArray=[];
                                    if(roomFileDataStr!=''){
                                        fileDataArray=JSON.parse(roomFileDataStr);
                                    }
                                    fileDataArray.push({'fileName':res.data.oldName,'fileUrl':res.data.src})
                                    $("#roomFile"+roomCount).val(JSON.stringify(fileDataArray));
                                    console.log(fileDataArray);
                                }else{
                                    layer.msg('上传失败',{icon:5,time:3000});
                                }
                            }
                            ,error: function(index, upload){
                                layer.closeAll('loading'); //关闭loading
                            }
                        });
                    }
                },
                hide(){
                    //alert('关闭了')
                }
            });
        }

        previewFile=function(fileUrl){
            layer.open({
                type: 2,
                title:'预览',
                area: ['800px', '500px'],
                fixed: false, //不固定
                maxmin: true,
                content: fileUrl
            });
        }

        delFile=function(tbodyIndex,trIndex,fileId,fileUrl){
            if(fileId==''){
                $("#fileTbody"+tbodyIndex+" tr[tr-index='"+trIndex+"']").remove();
                let roomFileDataStr=$("#roomFile"+tbodyIndex).val();
                let fileDataArray=[];
                if(roomFileDataStr!=''){
                    fileDataArray=JSON.parse(roomFileDataStr);
                }
                $.each(fileDataArray,function (index,item) {
                    if(item.fileUrl==fileUrl){
                        fileDataArray.splice(index,1);
                        return false;
                    }
                });
                $("#roomFile"+tbodyIndex).val(JSON.stringify(fileDataArray));
            }else{
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/leaseRecordApply/delRoomFile',
                        data: {id:fileId,"userId":$("#userId").val()},
                        notice:true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                $("#fileTbody"+tbodyIndex+" tr[tr-index='"+trIndex+"']").remove();
                                let roomFileDataStr=$("#roomFile"+tbodyIndex).val();
                                let fileDataArray=[];
                                if(roomFileDataStr!=''){
                                    fileDataArray=JSON.parse(roomFileDataStr);
                                }
                                $.each(fileDataArray,function (index,item) {
                                    if(item.fileUrl==fileUrl){
                                        fileDataArray.splice(index,1);
                                        return false;
                                    }
                                });
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
        }


        delTr=function (trIndex,id) {
            if(id==''){
                $('#' + trIndex).remove();
            }else{
                layer.confirm('是否要作废?', function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/fina/leaseRecordApply/delLeaseRoom',
                        data: {'id':id,'userId':$("#userId").val()},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                $('#' + trIndex).remove();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
            return false;
        }

        selectedRoom=function (trIndex) {
            if($("#baseId").val()==''){
                layer.msg('请先选择基地',{icon:5,time:5000});

                return false;
            }
            layerShow('选择房间','#(ctxPath)/fina/leaseRecordApply/roomTableIndex?baseId='+$("#baseId").val()+"&trIndex="+trIndex,900,600);
        }

        selectedRoomData=function (data,trIndex) {
            console.log(data,trIndex);
            $("#roomName"+trIndex).val(data.room_name);
            $("#roomId"+trIndex).val(data.id);
        }

        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg,'userId':$("#userId").val()};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/fina/leaseRecordApply/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.cardTypeTableReload(null);
                    }
                },
                complete : function() {
                }
            });
        }

        // 保存并提交
        form.on('submit(confirmBtn)',function(obj){
            layer.load();

            let taskId=$("#taskId").val();
            let saveData=obj.field;
            if(taskId==''){
                saveData.saveType='2';
                util.sendAjax({
                    url:"#(ctxPath)/fina/leaseRecordApply/saveLeaseRecordApply",
                    type:'post',
                    data:saveData,
                    notice:true,
                    success:function(returnData){
                        if(returnData.state==='ok'){
                            layer.closeAll('loading');
                            pop_close();
                            parent.cardTypeTableReload(null);
                        }
                    },
                    unSuccess:function (returnData) {
                        layer.closeAll('loading');
                    }
                });
            }else{
                util.sendAjax({
                    url:"#(ctxPath)/fina/leaseRecordApply/saveLeaseRecordApply",
                    type:'post',
                    data:saveData,
                    notice:true,
                    success:function(returnData){
                        if(returnData.state==='ok'){
                            // layer.closeAll('loading');
                            // pop_close();
                            doTask(5,$("#msg").val());
                            //parent.cardTypeTableReload(null);
                            parent.reloadTable();
                        }
                    },
                    unSuccess:function (returnData) {
                        layer.closeAll('loading');
                    }
                });


            }

            return false;
        }) ;
        //保存
        form.on('submit(saveBtn)',function(obj){
            layer.load();
            let saveData=obj.field;
            saveData.cccc='1';
            //console.log(obj.field);
            //console.log($("#frm").serialize());
            util.sendAjax({
                url:"#(ctxPath)/fina/leaseRecordApply/saveLeaseRecordApply",
                type:'post',
                data:obj.field,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        layer.closeAll('loading');
                        pop_close();
                        //parent.cardTypeTableReload(null);
                        parent.reloadTable();
                    }
                },
                unSuccess:function (returnData) {
                    layer.closeAll('loading');
                }
            });
            return false;
        }) ;




        #for(roomRecord : roomRecordList)
            laydate.render({
                elem: '#contractDate#(for.index+1)'
                ,trigger:'click'
                ,range: true
            });
            laydate.render({
                elem: '#roomDate#(for.index+1)'
                ,trigger:'click'
                ,range: true
            });

            loadXsSelect(#(for.index+1));
        #end

        #if((!isSaveHandle && taskId!=null) || !isEdit)
            $(".layui-col-md7 button").css('display','none');
            $(".layui-col-md7 input").prop('readonly',true);
            $(".layui-col-md7 input").prop('disabled',true);
            selectedRoom=function (trIndex) {

            }
            $(".layui-col-md7 select").prop('disabled',true);
            $(".layui-col-md7 textarea").prop('readonly',true);
            form.render('select')
        #end




    }) ;
</script>
<script id="roomTrTpl" type="text/html">
    <tr id="room-{{d.idx}}">
        <td>
            <input type="text" id="roomName{{d.idx}}" name="room{{d.idx}}.roomName"  lay-verify="required"readonly class="layui-input" value="" style="cursor:pointer;"  onclick="selectedRoom({{d.idx}})" placeholder="点击选择房间" autocomplete="off">
            <input type="hidden" id="roomId{{d.idx}}" name="room{{d.idx}}.roomId" value="">
        </td>

        <td>
            <div class="layui-input-inline" style="width: 90px;margin-right: 2px;">
                <input type="text" name="room{{d.idx}}.rent" class="layui-input" lay-verify="required|number" value="#(roomRecord.rent??)" placeholder="请输入租金" autocomplete="off">

            </div>
            <div class="layui-input-inline" style="width: 90px;;margin-right: 0px;">
                <select name="room{{d.idx}}.rentUnit" lay-verify="required" >
                    <option value="">请选择</option>
                    <option value="month"   >/月</option>
                    <option value="year"   >/年</option>
                </select>
            </div>
        </td>

        <td>
            <input type="text" name="room{{d.idx}}.earnestMoney" class="layui-input" lay-verify="required|number" value="" placeholder="请输入押金" autocomplete="off">
        </td>
        <td>
            <select name="room{{d.idx}}.earnestMoneyPaymentType" lay-verify="required" >
                <option value="">请选择押金支付方式</option>
                #for(type : earnestMoneyPaymentType)
                <option value="#(type.key)">#(type.value)</option>
                #end
            </select>
        </td>

        <td>
            <input type="text" id="roomDate{{d.idx}}" name="room{{d.idx}}.roomDate" lay-verify="required" class="layui-input" value="" placeholder="请输入合同期限" autocomplete="off">
        </td>
        <td>
            <input type="text" id="contractDate{{d.idx}}" name="room{{d.idx}}.contractDate" lay-verify="required" class="layui-input" value="" placeholder="请输入房租付款期限" autocomplete="off">
        </td>

        <td>
            <select id="collectWaySelect-{{d.idx}}" name="room{{d.idx}}.paymentType" lay-verify="required" >
                <option value="">请选择</option>
                <option value="disposable">一次性</option>
                <option value="year">年</option>
                <option value="halfYear">半年</option>
                <option value="season">季</option>
                <option value="month">月</option>
            </select>
        </td>
        <td>
            <div style="display: none;">
                <div class="layui-input-inline" style="width: 80px;">
                    <select id="rentPaymentDateType{{d.idx}}" name="room{{d.idx}}.rentPaymentDateType" lay-verify="required" >
                        <option value="">请选择</option>
                        <option value="1" selected>提前</option>
                        <option value="2">推迟</option>
                    </select>
                </div>
                <div class="layui-input-inline" style="width: 30px;margin-right: 2px;">

                    <input   name="room{{d.idx}}.rentPaymentDateDay" required lay-verify="required|number" value="0" placeholder="" autocomplete="off" class="layui-input">
                </div>
                <div class="layui-form-mid layui-word-aux" style="color: #000!important;margin-right: 0px;">天</div>
            </div>
            <input id="roomFile{{d.idx}}" type="hidden" name="roomFile{{d.idx}}" value="">
            <div id="uploadDiv{{d.idx}}"></div>

        </td>
        <td>
            <div class="layui-form-mid layui-word-aux" style="color: #000!important">每</div>
            <div class="layui-input-inline" style="width: 30px;">
                <input   name="room{{d.idx}}.rentAddYear" required lay-verify="required|number" value="0" placeholder="" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux" style="color: #000!important">年，递增</div>
            <div class="layui-input-inline" style="width: 30px;">
                <input   name="room{{d.idx}}.rentAddRatio" required lay-verify="required|number" value="0" placeholder="" autocomplete="off" class="layui-input">
            </div>
            <div class="layui-form-mid layui-word-aux" style="color: #000!important">%</div>
        </td>
        <td>
            <select name="room{{d.idx}}.signContractUserId" lay-verify="" lay-search >
                <option value="">请选择</option>
                #for(user : userList)
                <option value="#(user.id)" >#(user.name)(#(user.user_name))</option>
                #end

            </select>
        </td>
        <td>
            <input type="hidden"  name="room{{d.idx}}.id" value="">
            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delTr('room-{{d.idx}}','')">作废</button>
        </td>
    </tr>
</script>
#end

#define content()
<div class="my-btn-box">
    <form class="layui-form layui-form-pane" action="" id="frm">
        <div class="layui-row">

        <div class="layui-col-md7" style="padding-right: 10px;padding-left: 10px;" #if(type=='H5') class="layui-col-md12" #else class="layui-col-md7"  #end>
            #if((!isSaveHandle && taskId!=null) || !isEdit)
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>提交人</label>
                    <div class="layui-input-block">
                        <input type="text"  autocomplete="off" readonly value="#(submitUserName??)" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>提交人部门</label>
                    <div class="layui-input-block">
                        <input type="text"  autocomplete="off" readonly value="#(submitDeptName??)" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>提交人职位</label>
                    <div class="layui-input-block">
                        <input type="text"  autocomplete="off" readonly value="#(submitPositionName??)" class="layui-input">
                    </div>
                </div>

            #else
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>提交人</label>
                    <div class="layui-input-block">
                        <input type="text" name="userName" autocomplete="off" readonly value="#(user.name??)" class="layui-input">
                        <input type="hidden" id="applyId" name="applyId" value="#(user.id??)" >
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>提交人部门</label>
                    <div class="layui-input-block">

                        <select name="deptId" lay-verify="required" id="deptId" lay-filter="deptId">
                            <option value="">请选择</option>
                            #for(deptRecord : deptRecordList)
                                <option value="#(deptRecord.deptId??)" data='#(deptRecord.positionListStr??)' #if(deptRecordList.size()??0==1) selected #end>#(deptRecord.deptName??)</option>
                            #end
                        </select>

                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>提交人职位</label>
                    <div class="layui-input-block">

                        <select name="positionId" id="positionId" lay-verify="required">
                            <option value="">请选择</option>
                            #if(deptRecordList.size()??0==1)
                            #for(deptRecord : deptRecordList)
                            #for(position : deptRecord.positionList)
                            <option value="#(position.id??)" #if(deptRecord.positionList.size()??0==1) selected #end>#(position.positionName??)</option>
                            #end
                            #end
                            #end
                        </select>

                    </div>
                </div>
             #end
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>业主</label>
                    <div class="layui-input-inline" style="width: 70%;">
                        <input type="text" id="proprietorName" name="proprietorName" autocomplete="off"  readonly value="#(supplier.name??)" class="layui-input" >
                        <input type="hidden" name="supplierId" id="supplierId" value="#(supplier.id??)">

                    </div>
                    <button class="layui-btn" style="float: left;" type="button" id="proprietorBtn">选择</button>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>业主收款方式</label>
                    <div class="layui-input-block layui-form" lay-filter="suppliersPayIdDiv">

                        <select name="suppliersPayId" id="suppliersPayId" lay-filter="suppliersPayId" lay-verify="required">
                            <option value="">请选择</option>
                            #for(supplierPay : supplierPayList)
                            <option value="#(supplierPay.id??)" #if(leaseRecordApply.suppliersPayId??==supplierPay.id) selected #end>#(supplierPay.paymentWayStr??)(#(supplierPay.pay_account??))</option>
                            #end
                        </select>

                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>基地</label>
                    <div class="layui-input-block">

                        <select name="baseId" id="baseId" lay-verify="required" lay-search>
                            <option value="">请选择</option>
                            #for(base : baseList)
                            <option value="#(base.id??)" #if(leaseRecordApply.baseId??==base.id) selected #end >#(base.baseName??)</option>
                            #end
                        </select>

                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>合同类型</label>
                    <div class="layui-input-block">
                        <select name="type" id="type" lay-verify="required" lay-search>
                            <option value="">请选择</option>
                            <option value="1" #if(leaseRecordApply.type??=='1') selected #end>首签</option>
                            <option value="2" #if(leaseRecordApply.type??=='2') selected #end >续签</option>
                        </select>
                    </div>
                </div>

                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="remark" placeholder="请输入备注内容" class="layui-textarea">#(leaseRecordApply.remark??)</textarea>
                    </div>
                </div>

                <div class="layui-form-item" > <!--style="overflow: auto;"-->
                    <input id="roomCount" name="roomCount" type="hidden" value="#(roomRecordList.size()??0)">
                    <button class="layui-btn" type="button" id="addRoom" style="float: right;">添加房间</button>
                    <table class="layui-table" style="width: 1600px;overflow: auto;"  id="roomTable" >
                        <colgroup>
                            <col width="7%">
                            <col width="12%">
                            <col width="7%">
                            <col width="7%">
                            <col width="12%">
                            <col width="12%">
                            <col width="6%">
                            <col width="10%">
                            <col width="13%">
                            <col width="11%">
                            <col width="3%">
                        </colgroup>
                        <thead>
                            <tr>
                                <th ><font color="red">*</font>房间号</th>
                                <th ><font color="red">*</font>租金</th>
                                <th ><font color="red">*</font>押金</th>
                                <th ><font color="red">*</font>押金支付方式</th>
                                <th ><font color="red">*</font>合同期限</th>
                                <th ><font color="red">*</font>房租付款期限</th>
                                <th ><font color="red">*</font>房租支付方式</th>
                                <th ><font color="red"></font>附件</th>
                                <th ><font color="red">*</font>租金涨幅</th>
                                <th ><font color="red"></font>签约人</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="roomTbody">
                            #for(roomRecord : roomRecordList)
                            <tr id="room-#(for.index+1)">
                                <td>
                                    <input type="text" name="room#(for.index+1).roomName" class="layui-input" lay-verify="required" style="cursor:pointer;"
                                           readonly onclick="selectedRoom(#(for.index+1))" value="#(roomRecord.room_name??)" placeholder="点击选择房间" autocomplete="off">
                                    <input type="hidden" name="room#(for.index+1).roomId" value="#(roomRecord.room_id??)">
                                </td>
                                <td>
                                    <div class="layui-input-inline" style="width: 90px;margin-right: 2px;">
                                        <input type="text" name="room#(for.index+1).rent" class="layui-input" lay-verify="required|number" value="#(roomRecord.rent??)" placeholder="请输入租金" autocomplete="off">

                                    </div>
                                    <div class="layui-input-inline" style="width: 90px;margin-right: 0px;">
                                        <select name="room#(for.index+1).rentUnit" lay-verify="required" >
                                            <option value="">请选择</option>
                                            <option value="month" #if(roomRecord.rent_unit??=='month')selected#end >/月</option>
                                            <option value="year" #if(roomRecord.rent_unit??=='year')selected#end >/年</option>
                                        </select>
                                    </div>
                                </td>

                                <td>
                                    <input type="text" name="room#(for.index+1).earnestMoney" class="layui-input" lay-verify="required|number" value="#(roomRecord.earnest_money??)" placeholder="请输入押金" autocomplete="off">
                                </td>
                                <td>
                                    <select name="room#(for.index+1).earnestMoneyPaymentType" lay-verify="required" >
                                        <option value="">请选择押金支付方式</option>
                                        #for(type : earnestMoneyPaymentType)
                                        <option value="#(type.key)" #if(roomRecord.earnest_money_payment_type??==type.key)selected#end >#(type.value)</option>
                                        #end
                                    </select>
                                </td>
                                <td>
                                    <input type="text" id="roomDate#(for.index+1)" name="room#(for.index+1).roomDate" lay-verify="required" class="layui-input"
                                           value="#date(roomRecord.room_start_date??,'yyyy-MM-dd') - #date(roomRecord.room_end_date??,'yyyy-MM-dd')" placeholder="请输入合同期限" autocomplete="off">
                                </td>
                                <td>
                                    <input type="text" id="contractDate#(for.index+1)" name="room#(for.index+1).contractDate" lay-verify="required" class="layui-input"
                                           value="#date(roomRecord.contract_start_date??,'yyyy-MM-dd') - #date(roomRecord.contract_end_date??,'yyyy-MM-dd')" placeholder="请输入房租付款期限" autocomplete="off">
                                </td>

                                <td>
                                    <select name="room#(for.index+1).paymentType" lay-verify="required" >
                                        <option value="">请选择</option>
                                        <option value="disposable" #if(roomRecord.payment_type??=='disposable') selected #end>一次性</option>
                                        <option value="year" #if(roomRecord.payment_type??=='year') selected #end>年</option>
                                        <option value="halfYear" #if(roomRecord.payment_type??=='halfYear') selected #end>半年</option>
                                        <option value="season" #if(roomRecord.payment_type??=='season') selected #end>季</option>
                                        <option value="month" #if(roomRecord.payment_type??=='month') selected #end>月</option>
                                    </select>
                                </td>
                                <td>
                                    <div style="display: none;">
                                        <div class="layui-input-inline" style="width: 90px;">
                                            <select id="rentPaymentDateType#(for.index+1)" name="room#(for.index+1).rentPaymentDateType" lay-verify="required" >
                                                <option value="">请选择</option>
                                                <option value="1" #if(roomRecord.rent_payment_date_type??=='1') selected #end>提前</option>
                                                <option value="2" #if(roomRecord.rent_payment_date_type??=='2') selected #end>推迟</option>
                                            </select>
                                        </div>
                                        <div class="layui-input-inline" style="width: 30px;margin-right: 2px;">
                                            <input   name="room#(for.index+1).rentPaymentDateDay" required lay-verify="required|number" value="#(roomRecord.rent_payment_date_day)" placeholder="" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-form-mid layui-word-aux" style="color: #000!important;margin-right: 0px;">天</div>
                                    </div>
                                    <input id="roomFile#(for.index+1)" type="hidden" name="roomFile#(for.index+1)" value='#(roomRecord.fileJsonArrayStr??)'>
                                    <div id="uploadDiv#(for.index+1)"></div>
                                </td>
                                <td>
                                    <div class="layui-form-mid layui-word-aux" style="color: #000!important">每</div>
                                    <div class="layui-input-inline" style="width: 30px;">
                                        <input   name="room#(for.index+1).rentAddYear" required lay-verify="required|number" value="#(roomRecord.rent_add_year)" placeholder="" autocomplete="off" class="layui-input">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux" style="color: #000!important">年，递增</div>
                                    <div class="layui-input-inline" style="width: 30px;">
                                        <input   name="room#(for.index+1).rentAddRatio" required lay-verify="required|number" value="#(roomRecord.rent_add_ratio)" placeholder="" autocomplete="off" class="layui-input">
                                    </div>
                                    <div class="layui-form-mid layui-word-aux" style="color: #000!important">%</div>
                                </td>
                                <td>
                                    <select name="room#(for.index+1).signContractUserId" lay-verify="" lay-search >
                                        <option value="">请选择</option>
                                        #for(user : userList)
                                        <option value="#(user.id)" #if(user.id==roomRecord.sign_contract_user_id) selected #end >#(user.name)(#(user.user_name))</option>
                                        #end

                                    </select>
                                </td>
                                <td>
                                    <input name="room#(for.index+1).id" type="hidden" value="#(roomRecord.id??)">
                                    <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delTr('room-#(for.index+1)','#(roomRecord.id??)')">作废</button>
                                </td>
                            </tr>
                            #end
                        </tbody>
                    </table>

                </div>

                <div class="layui-form-item" style="margin-bottom:100px;"></div>

                <input type="hidden" name="id" value="#(leaseRecordApply.id??)">
                <input type="hidden" id="taskId" value="#(leaseRecordApply.taskId??)">
                <input type="hidden" id="userId"  name="userId" value="#(user.id??)" >
        </div>
        <div  #if(type=='H5') style="display: none" #else class="layui-col-md5"  #end>
            #if(stepts.size()??0>0)
            <div class="layui-row" style="overflow: auto;height: 240px;">
                <table class="layui-table" id="steptsTable" style="margin-top: 0px;">
                    <!--<colgroup>
                        <col width="10%">
                        <col width="13%">
                        <col width="30%">
                        <col width="30%">
                        <col width="17%">
                    </colgroup>-->
                    <thead>
                    <tr>
                        <th style="text-align: center;">序号</th>
                        <th style="text-align: center;">节点</th>
                        <th style="text-align: center;">流程</th>
                        <th style="text-align: center;">意见</th>
                        <th style="text-align: center;">操作时间</th>
                        <th style="text-align: center;">操作人</th>
                    </tr>
                    </thead>
                    <tbody>
                    #for(stept:stepts)
                    <tr>
                        <td align="center">#(for.index+1)</td>
                        <td>#(stept.ActivityName)</td>
                        <td align="center">
                            #if(stept.StepState==3)
                            提交
                            #else if(stept.StepState==1)
                            待处理
                            #else if(stept.StepState==0)
                            等待
                            #else if(stept.StepState==2)
                            正处理
                            #else if(stept.StepState==4)
                            撤回
                            #else if(stept.StepState==5)
                            批准
                            #else if(stept.StepState==6)
                            拒绝
                            #else if(stept.StepState==7)
                            转移
                            #else if(stept.StepState==8)
                            失败
                            #else if(stept.StepState==9)
                            跳过
                            #end
                        </td>
                        <td>#(stept.Comment)</td>
                        <td>#(stept.CommentTime)</td>
                        <td align="center">#(stept.CommentUserName)</td>
                    </tr>
                    #end
                    </tbody>
                </table>
            </div>


            <form class="layui-form layui-form-pane" id="taskForm">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label" style="width: 100% !important;text-align: left !important;">意见</label>
                    <div class="layui-input-block" style="margin-left: 0px!important;">
                        <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block pull-right">

                        <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                        <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>
                        <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject) disabled style="display: none;" #end>拒绝</button>
                        <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove) disabled style="display: none;" #end>通过</button>
                    </div>
                </div>
            </form>
            #end
        </div>
    </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                #if(type!='H5')
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                #end

                #if((isSaveHandle || taskId==null) && isEdit)
                <button type="button" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                #end
                #if((isSaveHandle || taskId==null) && isEdit)
                #if(type!='H5')
                <button type="button" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保存并提交</button>
                #end
                #end
            </div>
        </div>
    </form>
</div>
#end