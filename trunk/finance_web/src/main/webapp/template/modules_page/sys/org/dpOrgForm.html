#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	;
	
	var setting = {
		check:{enable:false}
		,view:{selectedMulti:false}
		,data:{simpleData:{enable:true}}
		,async:{enable:true, type:"post", url:"#(ctxPath)/org/orgFormTree"}
		,callback:{
			onClick: function(event, treeId, treeNode, clickFlag) {
				$("#parentId").val(treeNode.id);
				$("#parentName").val(treeNode.name);
			}
		}
   	};
	
	// 初始化树结构
   	var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/org/save',
		    data: $(formObj.form).serialize(),
		    notice: true,
		    loadFlag: true,
		    success : function(rep){
		    	if(rep.state=='ok'){
			    	parent.tableGridReload();
			    	pop_close();
		    	}
		    },
		    complete : function() {
		    }
		});
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row layui-col-space10">
	<div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
		<fieldset class="layui-elem-field layui-field-title" style="display:block;">
			<legend>组织架构</legend>
			<div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
		</fieldset>
	</div>
	<div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
		<div style="margin-bottom: 20px;"></div>
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<label class="layui-form-label">上级机构</label>
				<div class="layui-input-block">
					<input type="text" id="parentName" class="layui-input" value="#(parentOrg.orgName??'顶级机构')" readonly="readonly">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>部门名称</label>
				<div class="layui-input-block">
					<input type="text" name="orgName" class="layui-input" lay-verify="required" value="#(model.orgName??)" placeholder="请输入部门名称">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">类型</label>
				<div class="layui-input-block">
					<select name="orgType" lay-filter="">
						#dictOption("org_type", model.orgType??'', "main_org")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">负责人</label>
				<div class="layui-input-block">
					<input type="text" name="linkMan" class="layui-input" value="#(model.linkMan??)" placeholder="请输入负责人姓名">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">负责人电话</label>
				<div class="layui-input-block">
					<input type="text" name="linkPhone" class="layui-input" value="#(model.linkPhone??)" placeholder="请输入负责人电话">
				</div>
			</div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??)"> 
					<input type="hidden" id="parentId" name="parentId" value="#(model.parentId??parentOrg.id)">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end