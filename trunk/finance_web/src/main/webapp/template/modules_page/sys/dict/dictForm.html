#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()字典编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, $ = layui.jquery
	;
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/dict/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>类型名称</label>
		<div class="layui-input-inline">
			<input type="text" name="dictTypeName" class="layui-input" lay-verify="required" value="#(model.dictTypeName??)" maxlength="50" placeholder="请输入类型名称" #if(model.dictTypeName!=null)readonly="readonly"#end>
		</div>
		<div class="layui-form-mid layui-word-aux">比如：性别</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>字典类型</label>
		<div class="layui-input-inline">
			<input type="text" name="dictType" class="layui-input" lay-verify="required" value="#(model.dictType??)" maxlength="50" placeholder="请输入字典类型" #if(model.dictType!=null)readonly="readonly"#end>
		</div>
		<div class="layui-form-mid layui-word-aux">比如：sex</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>字典名称</label>
		<div class="layui-input-inline">
			<input type="text" name="dictName" class="layui-input" lay-verify="required" value="#(model.dictName??)" maxlength="50" placeholder="请输入字典名称">
		</div>
		<div class="layui-form-mid layui-word-aux">比如：男</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>字典值</label>
		<div class="layui-input-inline">
			<input type="text" name="dictValue" class="layui-input" lay-verify="required" value="#(model.dictValue??)" maxlength="50" placeholder="请输入字典值">
		</div>
		<div class="layui-form-mid layui-word-aux">比如：man</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-inline">
			<input type="text" name="remarks" class="layui-input" value="#(model.remarks??)" placeholder="请输入描述">
		</div>
		<div class="layui-form-mid layui-word-aux">比如：性别</div>
	</div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end