#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()登陆帐号编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	;
	
	//树属性配置
	var setting = {
   		check:{enable:true, chkboxType: {"Y":"ps","N":"s"}, nocheckInherit:true},
   		view:{selectedMulti:false},
   		data:{simpleData:{enable:true}},
		async: {enable:true, type:"post", url:"#(ctxPath)/user/userRolesTree?userId=#(model.id??'')"},
		callback:{
			onClick: function (e, treeId, treeNode, clickFlag) {
				var treeObj = $.fn.zTree.getZTreeObj("zTreeDiv");
				treeObj.checkNode(treeNode, !treeNode.checked, true);
			}
		}
   	};
	
	// 初始化树结构
   	var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var checkedNodes = zTreeObj.getCheckedNodes(); //获取 zTree 当前被勾选中的节点数据集合，返回的类型:Array(JSON)
		var roleIdsArray = new Array(checkedNodes.length);//定义角色ID数组
		for (var i = 0; i < checkedNodes.length; i++) {
			roleIdsArray[i] = checkedNodes[i].id;
        }
		$("#roleIds").val(roleIdsArray.toString());
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/user/saveUser',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload(null);
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
		<form class="layui-form layui-form-pane" action="">
			<div style="margin-bottom:20px;"></div>
			<div class="layui-row">
				<div class="layui-inline">
					<label class="layui-form-label">姓名</label>
					<div class="layui-input-inline" style="width:120px;">
						<input type="text" name="name" class="layui-input" value="#(model.name??'')" disabled="disabled">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">登陆帐号</label>
					<div class="layui-input-inline" style="width:120px;">
						<input type="text" name="userName" class="layui-input" value="#(model.userName??'')" disabled="disabled">
					</div>
				</div>
			</div>
			<div class="layui-row">
				<fieldset class="layui-elem-field layui-field-title" style="display:block;">
					<legend>角色列表</legend>
					<div id="zTreeDiv" class="ztree" style="height:310px;overflow:auto;"></div>
				</fieldset>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??'')">
					<input type="hidden" id="roleIds" name="roleIds" value="">
					<input type="hidden" name="orgId" value="#(model.orgId??'')">
					<input type="hidden" name="employeeId" value="#(model.employeeId??'')">
					<input type="hidden" name="userType" value="#(model.userType??'ordinary_user')">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end