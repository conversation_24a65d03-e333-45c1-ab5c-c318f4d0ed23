#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()系统字典管理首页#end

#define css()
#end

#define js()
<script type="text/html" id="dictTableBar">
	#shiroHasPermission("finance:dict:addKeyValueBtn")
	<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="addKey">添加键值</a>
	#end
	#shiroHasPermission("finance:dict:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("finance:dict:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'dictTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/dict/pageTable'
	    , method: 'post'
	    , height: vipTable.getFullHeight()    //容器高度
	    , cols: [[                  //标题栏
			  {checkbox: true, sort: false, fixed: false, space: false}
			, {field: '', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			, {field: 'dictTypeName', title: '字典类型名称', unresize:true}
			, {field: 'dictType', title: '字典类型', unresize:true}
			, {field: 'dictName', title: '字典名称', unresize:true}
			, {field: 'dictValue', title: '字典值', unresize:true}
			, {field: 'dictSort', title: '排序', width: 60, unresize:true}
			, {field: 'remarks', title: '备注', unresize:true}
			, {fixed: 'right', title: '操作', align: 'center', unresize:true, toolbar: '#dictTableBar'} //这里的toolbar值是模板元素的选择器
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
    table.on('tool('+tableId+')',function (obj) {
        if(obj.event==="addKey"){//添加键值按钮事件
        	pop_show('添加键值','#(ctxPath)/dict/add?dictType='+obj.data.dictType+'&dictTypeName='+obj.data.dictTypeName,'450','400');
        }else if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/dict/edit?id='+obj.data.id,'450','400');
        }else if(obj.event==="del"){//作废按钮事件
        	//作废操作
    		layer.confirm('您确定要作废该的数据？', {icon:3, title:'提示'}, function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/dict/del',
                    data: {id:obj.data.id, delFlag:'1'},
                    loadFlag: true,
                    success : function(rep){
                    	if(rep.state=='ok'){
	                    	pageTableReload();
                    	}
                    },
                    complete : function() {
        		    }
                });
				layer.close(index);
            });
        }
    });
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{dictType:$('#dictType').val(), dictName:$('#dictName').val(), dictValue:$('#dictValue').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		pop_show('添加','#(ctxPath)/dict/add','450','400');
	});
	// 批量作废
    $('#batchDelBtn').on('click', function () {
    	batchDel();
    });
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
	//批量作废方法
	batchDel = function() {
		var checkStatus = table.checkStatus(tableId);
		var dictArray = new Array();//定义ID数组
		console.log('length='+checkStatus.data.length);
		if(checkStatus.data.length>0){
			layer.confirm('您确定要作废选中的数据？', {icon:3, title:'提示'}, function(index) {
				for (var i = 0; i < checkStatus.data.length; i++) {
					dictArray.push({'id':checkStatus.data[i].id, 'delFlag':'1'});
		        }
				if(dictArray.length>0){
					//批量作废操作
					util.sendAjax ({
			            type: 'POST',
			            url: '#(ctxPath)/dict/batchDel',
			            data: {batchDelDatas:JSON.stringify(dictArray)},
			            notice: true,
					    loadFlag: true,
			            success : function(rep){
			            	if(rep.state=='ok'){
				            	pageTableReload();
			            	}
			            },
			            complete : function() {
					    }
			        });
				}
				layer.close(index);
			});
		}else{
			layer.msg('请选择要作废的数据。',{icon:5});
		}
    };
	$(function() {
	});
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
	    		<div class="layui-inline">
			        <label class="layui-form-label">字典类型：</label>
			        <div class="layui-input-inline">
			            <select id="dictType" name="dictType" lay-search="">
			            	<option value="">请选择</option>
							#for(dict : dictTypeList)
								<option value="#(dict.dict_type)">#(dict.dict_type_name)</option>
							#end
						</select>
			        </div>
	    		</div>
		    	<div class="layui-inline">
			        <label class="layui-form-label">字典名称：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="dictName" name="dictName" class="layui-input" placeholder="请输入字典名称" autocomplete="off">
			        </div>
	    		</div>
		    	<div class="layui-inline">
			        <label class="layui-form-label">字典值：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="dictValue" name="dictValue" class="layui-input" placeholder="请输入字典值" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
						#shiroHasPermission("finance:dict:addBtn")
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
						#end
						#shiroHasPermission("finance:dict:batchDelBtn")
				        <a type="button" id="batchDelBtn" class="layui-btn layui-btn-danger radius btn-delect">批量作废</a>
						#end
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
	<div class="layui-row">
		<table id="dictTable" lay-filter="dictTable"></table>
	</div>
</div>
#end