#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script type="text/html" id="empTableBar">
    <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="loginAccountManage">帐号管理</a>
	<a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="del">作废</a>
</script>
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table', 'vip_table'], function() {
	// 操作对象
	var table = layui.table
    , layer = layui.layer
    , vipTable = layui.vip_table
    , tableId = 'employeeTable'
    ;
	
	var setting = {
		check:{enable:false}
		,view:{selectedMulti:false}
		,data:{simpleData:{enable:true}}
		,async:{enable:true, type:"post", url:"#(ctxPath)/orgEmployee/employeeOrgTree"}
		,callback:{
			onClick: function(event, treeId, treeNode, clickFlag) {
				$("#orgId").val(treeNode.id);
				$("#orgType").val(treeNode.type);
				$("#orgParentId").val(treeNode.pId);
				$("#orgParentIds").val(treeNode.pIds);
				pageTableReload();
			}
		}
   	};
	
	// 初始化树结构
   	var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);
	
	// 表格加载渲染
	var tableObj = table.render({
		id : tableId
		,elem : '#'+tableId
		,method : 'POST'
		,url : '#(ctxPath)/orgEmployee/pageTable'
		,height: vipTable.getFullHeight()    //容器高度
		,where : {orgParentIds:$("#orgId").val()}//额外查询条件
		,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
		,cols : [[ 
			{field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'fullName', title:'姓名', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{field:'sex', title:'性别', width:60, align:'center', templet:'#dictTpl("sex", "sex")'}
			,{field:'birthday', title:'出生日期', width:120, align:'center'}
			,{field:'idCard', title:'身份证', align:'center'}
			,{field:'phoneNum', title:'手机号', width:120, align:'center'}
			,{field:'workNum', title:'工号', width:80, align:'center'}
			,{fixed:'right', title:'操作', width:200, align:'center', toolbar:'#empTableBar'}
		]]
		,page : true
	});
	//监听工具条
	table.on('tool('+tableId+')', function(obj) {
		if (obj.event === 'edit') {
			pop_show('修改用户', '#(ctxPath)/orgEmployee/edit/'+obj.data.id, '650', '');
		} else if (obj.event === 'loginAccountManage') {
			pop_show('登陆帐号管理', '#(ctxPath)/orgEmployee/loginAccountManage/'+obj.data.id, '', '');
		} else if (obj.event === 'del') {
			layer.confirm('您确定要作废当前人员？该人员的登陆帐号会一并作废。', function(index) {
				//作废操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/orgEmployee/save',
		            data: {id:obj.data.id, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}
	});
	//重载表格
    pageTableReload = function () {
    	tableReload(tableId,{orgParentIds:$('#orgId').val(), fullName:$('#fullName').val()});
    }
	//搜索按钮点击事件
	$('#btn-search').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#btn-add').on('click', function() {
		var orgId = $("#orgId").val();
		var orgParentId = $("#orgParentId").val();
		var orgParentIds = $("#orgParentIds").val();
		var orgType = $("#orgType").val();
		if(orgId!=null && orgId!=''){
			pop_show('添加', '#(ctxPath)/orgEmployee/add/'+orgId+'-'+orgParentId+'-'+orgParentIds+'-'+orgType, '650', '');
		}else{
			layer.msg('请选择组织架构!',{icon:5});
		}
	});
 	// 批量作废
    $('#btn-delete-all').on('click', function () {
    	batchDel();
    });
	// 刷新
    $('#btn-refresh').on('click', function () {
    	pageTableReload();
    });
	//批量作废方法
	batchDel = function() {
		var checkStatus = table.checkStatus(tableId);
		var empArray = new Array();//定义ID数组
		var userArray = new Array();//定义ID数组
		console.log('length='+checkStatus.data.length);
		if(checkStatus.data.length>0){
			layer.confirm('您确定要作废选中的数据？', {icon:3, title:'提示'}, function(index) {
				for (var i = 0; i < checkStatus.data.length; i++) {
					empArray.push({'id':checkStatus.data[i].id, 'delFlag':'1'});
					userArray.push({'employeeId':checkStatus.data[i].id, 'delFlag':'1'});
		        }
				if(empArray.length>0 && userArray.length>0){
					//批量作废操作
					util.sendAjax ({
			            type: 'POST',
			            url: '#(ctxPath)/orgEmployee/batchDel',
			            data: {batchDelDatas:JSON.stringify(empArray), batchDelDatas1:JSON.stringify(userArray)},
			            notice: true,
					    loadFlag: true,
			            success : function(rep){
			            	if(rep.state=='ok'){
			            		pageTableReload();
			            	}
			            },
			            complete : function() {
					    }
			        });
				}
				layer.close(index);
			});
		}else{
			layer.msg('请选择要作废的数据。',{icon:5});
		}
    };
});
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row layui-col-space10">
		<div class="layui-col-xs2 layui-col-sm2 layui-col-md2">
			<fieldset class="layui-elem-field layui-field-title">
				<legend>组织架构</legend>
				<div id="zTreeDiv" class="ztree" style="overflow:auto;"></div>
			</fieldset>
		</div>
		<div class="layui-col-xs10 layui-col-sm10 layui-col-md10">
			<div class="layui-row">
				<form class="layui-form layui-form-pane" method="" action="">
					<span class="fl">
						<span class="layui-form-label">姓名：</span>
						<div class="layui-input-inline">
							<input type="text" id="fullName" class="layui-input" placeholder="请输入姓名搜索" autocomplete="off">
						</div>
						<input type="hidden" id="orgId" value="#(orgId??'')">
						<input type="hidden" id="orgType" value="#(orgType??'')">
						<input type="hidden" id="orgParentId" value="#(orgParentId??'')">
						<input type="hidden" id="orgParentIds" value="#(orgParentIds??'')">
						<button type="button" id="btn-search" class="layui-btn mgl-20">查询</button>
						<button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
					</span>
					<span class="fr">
						<a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
						<a id="btn-refresh" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
					</span>
				</form>
			</div>
			<div class="layui-row">
				<table id="employeeTable" lay-filter="employeeTable"></table>
			</div>
		</div>
	</div>
</div>
#end