#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()操作日志#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
					<label class="layui-form-label">时间段</label>
					<div class="layui-input-inline" style="width: 100px;">
						<input type="text" id="startDate" placeholder="请选择起始日期" autocomplete="off" class="layui-input">
					</div>
					<i class="layui-icon">-</i>
					<div class="layui-input-inline" style="width: 100px;">
						<input type="text" id="endDate" placeholder="请选择结束日期" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">操作人帐号</label>
					<div class="layui-input-inline">
						<input type="text" id="userAccount" placeholder="请输入操作人帐号" autocomplete="off" class="layui-input">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">操作人姓名</label>
					<div class="layui-input-inline">
						<input type="text" id="userName" placeholder="请输入操作人姓名" autocomplete="off" class="layui-input">
					</div>
				</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
				        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
				        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    </span>
    </div>
	<div class="layui-row">
		<table id="logTable" lay-filter="logTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','laydate','vip_table'],function(){
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,laydate = layui.laydate
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'logTable'
        ;
	
	laydate.render({
	  elem: '#startDate'
	  ,value: '#(startDate)'
	  ,isInitValue: true
	});
	
	laydate.render({
	  elem: '#endDate'
	  ,value: '#(endDate)'
	  ,isInitValue: true
	});
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/log/pageTable'
	    , method: 'post'
	    , height: vipTable.getFullHeight()    //容器高度
	    , where: {startDate:$('#startDate').val(), endDate:$('#endDate').val(), userAccount:$('#userAccount').val(), userName:$('#userName').val()}
	    , cols: [[                  //标题栏
	          {field:'', title:'序号', width:60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
	        , {field:'log_title', title:'操作菜单', width:300, unresize:true}
	        , {field:'user_name', title:'操作人帐号', width:120, unresize:true}
	        , {field:'name', title:'操作人姓名', width:120, unresize:true}
	        , {field:'request_uri', title:'URI', width:200, unresize:true}
	        , {field:'params', title:'参数', unresize:true}
	        , {field:'remote_addr', title:'操作者IP', width:160, unresize:true}
	        , {field:'create_date', title:'操作时间', width:160, unresize:true}
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{startDate:$('#startDate').val(), endDate:$('#endDate').val(), userAccount:$('#userAccount').val(), userName:$('#userName').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
})
</script>
#end