#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构角色首页#end

#define css()
#end

#define js()
<script type="text/html" id="roleTableBar">
    <a class="layui-btn layui-btn-xs" lay-event="sysUser">账号</a>
    #shiroHasPermission("main:role:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','form','vip_table'],function(){

        // 操作对象
        var layer = layui.layer
            ,form = layui.form
            ,table = layui.table
            ,vipTable = layui.vip_table
            ,$ = layui.jquery
            ,tableId = 'userTable'
        ;

        // 表格渲染
        var tableObj = table.render({
            id: tableId
            , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
            , even: true //开启隔行背景
            , url: '#(ctxPath)/role/roleUserPage'
            , method: 'post'
            , where: {'roleId':'#(roleId??)'}
            , height: vipTable.getFullHeight()    //容器高度
            , cols: [[                  //标题栏
                {field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                , {field: 'user_name', title: '账号', unresize:true}
                , {field: 'name', title: '姓名', unresize:true}
            ]]
            , page: true
            , loading: true
            , done: function (res, curr, count) {
            }
        });
        // 表格绑定事件
        table.on('tool('+tableId+')',function (obj) {
            if(obj.event==="edit"){//编辑按钮事件
                pop_show('编辑','#(ctxPath)/role/edit?id='+obj.data.id,'500','');
            }else if(obj.event==="del"){//作废按钮事件
                //作废操作
                layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/role/del',
                        data: {id : data.id},
                        loadFlag: true,
                        success : function(rep){
                        },
                        complete : function() {
                            pageTableReload();
                        }
                    });
                    layer.close(index);
                });
            }else if(pbj.event==='sysUser'){
                pop_show('编辑','#(ctxPath)/role/edit?id='+obj.data.id,'500','');
            }
        });
        //重载表格
        pageTableReload = function () {
            tableReload(tableId,{roleName:$('#roleName').val()});
        }
        //搜索按钮点击事件
        $('#searchBtn').on('click', function() {
            pageTableReload();
        });
        //添加按钮点击事件
        $('#addBtn').on('click', function() {
            var orgId = $('#orgId').val();
            pop_show('添加','#(ctxPath)/role/add','500','');
        });
        // 刷新
        $('#refreshBtn').on('click', function () {
            pageTableReload();
        });
    })
</script>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <table id="userTable" lay-filter="userTable"></table>
    </div>
</div>
#end