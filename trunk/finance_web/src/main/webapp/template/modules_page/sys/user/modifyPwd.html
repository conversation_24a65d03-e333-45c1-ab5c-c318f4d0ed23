#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var $ = layui.jquery
	, form = layui.form
	, layer = layui.layer
	;
	
	//表单自定义验证
	form.verify({
		pwdLength: function(value){
			if(value.length < 5){
				return '密码长度至少为6位';
			}
		},
		repeatPwd: function(value) {
			//获取密码
			var pwd = $("#newPwd").val();
			if(!new RegExp(pwd).test(value)) {
				return '两次输入的密码不一致';
			}
		}
	});
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/user/modifyPwdSave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
	            	pop_close();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div style="margin-bottom:10px;"></div>
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>旧密码</label>
			<div class="layui-input-block">
				<input type="password" name="inputOldPwd" minlength="6" class="layui-input" lay-verify="required|pwdLength" value="" placeholder="请输入旧密码">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>新密码</label>
			<div class="layui-input-block">
				<input type="password" id="newPwd" name="password" minlength="6" class="layui-input" lay-verify="required|pwdLength" value="" placeholder="请输入新密码">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>重复新密码</label>
			<div class="layui-input-block">
				<input type="password" name="confirmNewPwd" minlength="6" class="layui-input" lay-verify="required|pwdLength|repeatPwd" value="" placeholder="请重复输入新密码">
			</div>
		</div>
		<div style="margin-bottom:60px;"></div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.Id??)">
				<input type="hidden" name="userName" value="#(model.userName??)">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end