#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()帐号管理首页#end

#define css()
#end

#define js()
<script type="text/html" id="userTableBar">
	#shiroHasPermission("finance:user:lockBtn")
	#[[
	{{# if((d.userType!='org_manager')){ }}
	{{# if((d.loginFlag=='un_locked')){ }}
	<a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="locked">锁定</a>
	{{# }else{ }}
	<a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="unlock">解锁</a>
	{{# } }}
	{{# } }}
	]]#
	#end

	#shiroHasPermission("finance:role:addBtn")
	<a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
	#end
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'userTable'
        ;
    
	// 表格渲染
	pageTableReload = function (data) {
		//loading层
		var loadingIndex = layer.load(2, { //icon支持传入0-2
			shade: [0.5, 'gray'], //0.5透明度的灰色背景
			content: '加载中...',
			success: function (layero) {
				layero.find('.layui-layer-content').css({
					'padding-top': '39px',
					'width': '60px'
				});
			}
		});
		table.render({
			id: tableId
			, elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
			, even: true //开启隔行背景
			, url: '#(ctxPath)/user/pageTable'
			, method: 'post'
			, height: 'full-90'    //容器高度
			, where: data
			, cols: [[                  //标题栏
				{field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
				,{field:'userType', title:'帐号类型', width:100, align:'center', templet:'#dictTpl("user_type", "userType")'}
				,{field:'userName', title:'登陆帐号', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
				,{field:'loginFlag', title:'登陆状态', align:'center', templet:'#dictTpl("login_flag", "loginFlag")'}
				,{field:'name', title:'姓名', align:'center'}
				,{field:'sex', title:'性别', align:'center', templet:'#dictTpl("gender", "sex")'}
				,{field:'phoneNumber', title:'手机号码', align:'center'}
				,{fixed:'right', title:'操作', width:200, align:'center', toolbar:'#userTableBar'}
			]]
			, page: true
			, loading: true
			, done: function (res, curr, count) {
				layer.close(loadingIndex);
			}
		});
		// 表格绑定事件
		table.on('tool('+tableId+')',function (obj) {
			if (obj.event === 'locked') {
				layer.confirm('您确定要锁定该帐号?', function(index) {
					//锁定操作
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/user/save',
						data: {id:obj.data.id, loginFlag:'locked'},
						notice: true,
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								pageTableReload(null);
							}
						},
						complete : function() {
						}
					});
					layer.close(index);
				});
			}else if (obj.event === 'unlock') {
				layer.confirm('您确定要解锁该帐号?', function(index) {
					//解锁操作
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/user/save',
						data: {id:obj.data.id, loginFlag:'un_locked'},
						notice: true,
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								pageTableReload(null);
							}
						},
						complete : function() {
						}
					});
					layer.close(index);
				});
			}else if (obj.event === 'edit') {
				pop_show('编辑','#(ctxPath)/user/edit?id='+obj.data.id,'500','');
			}
		});
	}

	pageTableReload(null);

	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload({userName:$('#userName').val(),name:$("#name").val()});
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload(null);
    });
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <label class="layui-form-label">登陆帐号：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="userName" name="userName" class="layui-input" placeholder="请输入登陆帐号" autocomplete="off">
			        </div>
	    		</div>
				<div class="layui-inline">
			        <label class="layui-form-label">用户姓名：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="name" name="name" class="layui-input" placeholder="请输入用户姓名" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
	<div class="layui-row">
		<table id="userTable" lay-filter="userTable"></table>
	</div>
</div>
#end