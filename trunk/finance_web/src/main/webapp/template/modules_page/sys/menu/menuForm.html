#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()菜单编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	;
	
	//树属性配置
	var setting = {
   		check:{enable:false}
   		,view:{selectedMulti:false}
   		,data:{simpleData:{enable:true}}
   		,async:{enable:true, type:"post", url:"#(ctxPath)/menu/menuFormTree"}
   		,callback:{
   			onClick: function(event, treeId, treeNode, clickFlag) {
				$("#parentId").val(treeNode.id);
				$("#parentName").val(treeNode.name);
			}
   		}
   	};
	
	// 设置顶层菜单按钮点击事件
   	var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);
	
	// 初始化树结构
	layui.$('#setTopBtn').on('click', function() {
		$("#parentName").val('顶层菜单');
		$("#parentId").val('0');
    });
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/menu/save',
		    data: $(formObj.form).serialize(),
		    notice: true,
		    loadFlag: true,
		    success : function(rep){
		    	if(rep.state=='ok'){
			    	parent.tableGridReload();
			    	pop_close();
		    	}
		    },
		    complete : function() {
		    }
		});
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
	<div class="layui-col-xs5 layui-col-sm5 layui-col-md5">
		<fieldset class="layui-elem-field layui-field-title" style="display:block;">
			<legend>菜单列表</legend>
			<div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
		</fieldset>
	</div>
	<div class="layui-col-xs7 layui-col-sm7 layui-col-md7">
		<div style="margin-bottom:23px;"></div>
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<label class="layui-form-label">上级资源</label>
				<div class="layui-input-block">
					<input type="text" id="parentName" class="layui-input" value="#(parentMenu.menuName??'顶层菜单')" readonly="readonly">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>资源名称</label>
				<div class="layui-input-block">
					<input type="text" name="menuName" class="layui-input" lay-verify="required" value="#(model.menuName??)" placeholder="请输入名称">
				</div>
			</div>
<!-- 			<div class="layui-form-item"> -->
<!-- 				<label class="layui-form-label">图标</label> -->
<!-- 				<div class="layui-input-block"> -->
<!-- 					<input type="text" name="menuIcon" class="layui-input" value="#(model.menuIcon??)" placeholder="请输入图标"> -->
<!-- 				</div> -->
<!-- 			</div> -->
			<div class="layui-form-item">
				<label class="layui-form-label">类型</label>
				<div class="layui-input-block">
					<select name="menuType" lay-filter="">
						#dictOption("menu_type", model.menuType??'', "")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">资源路径</label>
				<div class="layui-input-block">
					<input type="text" name="menuUrl" class="layui-input" value="#(model.menuUrl??)" placeholder="请输入资源路径">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>权限标志</label>
				<div class="layui-input-block">
					<input type="text" name="permission" class="layui-input" lay-verify="required" value="#(model.permission??)" placeholder="请输入权限标志">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>排序</label>
				<div class="layui-input-block">
					<input type="text" name="menuSort" class="layui-input" lay-verify="required|number" value="#(model.menuSort??)" placeholder="请输入排序">
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??'')">
					<input type="hidden" id="parentId" name="parentId" value="#(model.parentId??'0')">
					<button type="button" id="setTopBtn" class="layui-btn layui-btn-normal">设置顶层菜单</button>
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end