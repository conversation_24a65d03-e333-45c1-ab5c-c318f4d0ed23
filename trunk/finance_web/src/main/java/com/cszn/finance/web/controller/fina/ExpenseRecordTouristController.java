package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.*;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.*;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.main.MainTouristRouteDeductConfigService;
import com.cszn.integrated.service.api.main.MainTouristRouteService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.enums.AccountRecordOperateType;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.main.MainTouristRoute;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.status.ConsumeType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;

/**
 * @Description  旅游团账单记录管理
 * <AUTHOR>
 * @Date 2019/8/30
 **/
@RequestMapping(value="/fina/tourist", viewPath="/modules_page/finance/fina/tourist")
public class ExpenseRecordTouristController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private FinaExpenseRecordTouristService finaExpenseRecordTouristService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;
    @Inject
    private FinaExpenseRecordDeductionCardService finaExpenseRecordDeductionCardService;
    @Inject
    private MmsMemberService mmsMemberService;
    @Inject
    private MainTouristRouteService mainTouristRouteService;
    @Inject
    private FinaExpenseRecordTouristDetailService finaExpenseRecordTouristDetailService;
    @Inject
    private FinaExpenseRecordTouristSettleDetailService finaExpenseRecordTouristSettleDetailService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private SmsSendRecordService smsSendRecordService;
    @Inject
    private MainTouristRouteDeductConfigService mainTouristRouteDeductConfigService;


    /**
     * 跳转旅游团界面
     */
    public void index(){
        render("touristExpenseRecordIndex.html");
    }

    /**
     *旅游团信息列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String beginTime = getPara("beginTime");
        String endTime = getPara("endTime");
        FinaExpenseRecordTourist tourist = getBean(FinaExpenseRecordTourist.class,"",true);
        Page<FinaExpenseRecordTourist> page = finaExpenseRecordTouristService.findListPage(getParaToInt("page"),getParaToInt("limit"),beginTime,endTime,tourist);
        renderJson(new DataTable<FinaExpenseRecordTourist>(page));
    }

    /**
     * 旅游团账单记录列表
     */
    @Clear(LogInterceptor.class)
    public void findTouristExpenseList(){
        String fullName = getPara("fullName");
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getTouristExpenseList(er,fullName);
        if(list != null && list.size() > 0){
            for (Record record : list) {
                /*String id = record.getStr("id");
                FinaExpenseRecordDeductionCard fc = finaExpenseRecordDeductionCardService.getOriginalCard(id);
                if(fc != null){
                    MmsMember member = mmsMemberService.findMemberByCardNumber(fc.getCardNumber());
                    record.set("originalCardNumber",fc.getCardNumber());
                    if(member != null)record.set("originalMember",member.getFullName());
                }*/
                if(StrKit.isBlank(record.getStr("deductWay"))){
                    record.set("deductWay",record.getStr("longDeductWay"));
                }
            }
        }
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(list),valueFilter));
    }

	/**
	 * 作废方法
	 */
    public void del() {
    	FinaExpenseRecordTourist model = getBean(FinaExpenseRecordTourist.class, "", true);
    	if(StrKit.isBlank(model.getTouristNo())){
    		renderJson(Ret.fail("msg", "操作失败,团号不能为空！"));
    		return;
    	}
//    	final int settleDataCount = Db.queryInt("select count(id)dataCount from fina_expense_record where del_flag='0' and settle_status='1' and tourist_no=?", model.getTouristNo());
//    	if(settleDataCount>0){
//    		renderJson(Ret.fail("msg", "操作失败,该团下面有已结算数据！"));
//    		return;
//    	}
    	FinaExpenseRecordTourist tourist = new FinaExpenseRecordTourist();
    	tourist.setId(model.getId());
    	tourist.setDelFlag(DelFlag.DELETED);
    	if (finaExpenseRecordTouristService.update(tourist)) {
    		List<Record> delRecordList = Db.find("select * from fina_expense_record where del_flag='0' and settle_status='0' and tourist_no=?", model.getTouristNo());
			for(Record record : delRecordList) {
				final String expenseId = record.getStr("id");
				FinaExpenseRecord expenseRecord = new FinaExpenseRecord();
				expenseRecord.setId(expenseId);
				expenseRecord.setSettleStatus("2");
				expenseRecord.setUpdateBy(AuthUtils.getUserId());
				expenseRecord.setUpdateTime(new Date());
                if(expenseRecord.update()) {
                    List<FinaExpenseRecordTouristSettleDetail> touristSettleDetailList = finaExpenseRecordTouristSettleDetailService.findTouristSettleDetailList(expenseId);
                    for (FinaExpenseRecordTouristSettleDetail touristSettleDetail : touristSettleDetailList) {
                        touristSettleDetail.setDelFlag("1");
                        touristSettleDetail.setUpdateBy(AuthUtils.getUserId());
                        touristSettleDetail.setUpdateTime(new Date());
                        touristSettleDetail.update();
                        Db.update("update fina_expense_record_detail set del_flag='1',update_by=?,update_time=? where expense_id=?", AuthUtils.getUserId(), new Date(), touristSettleDetail.getId());
                    }
                }

			}
        	renderJson(Ret.ok("msg", "操作成功!"));
        } else {
        	renderJson(Ret.fail("msg", "操作失败！"));
        }
    }
    
    /**
     * 单条作废方法
     */
    public void singleDel() {
    	final String id = getPara("id");
    	if(StrKit.isBlank(id)){
    		renderJson(Ret.fail("msg", "操作失败,id不能为空！"));
    		return;
    	}

		FinaExpenseRecordTouristSettleDetail touristSettleDetail=finaExpenseRecordTouristSettleDetailService.findById(id);
		if(touristSettleDetail==null){
            renderJson(Ret.fail("msg", "该记录已不存在，请刷新再重试"));
		    return;
        }
        if("1".equals(touristSettleDetail.getDelFlag())){
            renderJson(Ret.fail("msg", "该记录已是作废状态"));
            return;
        }
        touristSettleDetail.setDelFlag("1");
        touristSettleDetail.setUpdateTime(new Date());

        FinaExpenseRecordDetail expenseRecordDetail = finaExpenseRecordDetailService.findFirstDetailsByExpenseId(touristSettleDetail.getId());
        expenseRecordDetail.setDelFlag("1");
        expenseRecordDetail.setUpdateTime(new Date());

        if(touristSettleDetail.update()){
            expenseRecordDetail.update();

            if(Db.findFirst("select * from fina_expense_record_tourist_settle_detail where del_flag='0' and expense_id=? ",touristSettleDetail.getExpenseId())==null){
                FinaExpenseRecord expenseRecord = finaExpenseRecordService.findById(touristSettleDetail.getExpenseId());
                expenseRecord.setDelFlag("1");
                expenseRecord.setUpdateTime(new Date());
                expenseRecord.update();
            }

            renderJson(Ret.ok("msg", "操作成功!"));
        }else{
            renderJson(Ret.fail("msg", "操作失败"));
        }


    }

    public void touristExport(){
        String touristNo=getPara("touristNo");
        if(StrKit.isBlank(touristNo)){
            renderJson("error");
            return;
        }

        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getTouristExpenseList(er,null);

        FinaExpenseRecordTourist tourist=finaExpenseRecordTouristService.getTouristByNo(touristNo);

        MainTouristRoute touristRoute = mainTouristRouteService.findById(tourist.getRouteId());

        String[] title={"旅游线路名称","入住天数（含非基地入住）","开始时间","结束时间","总锁定天数","总扣卡天数","总锁定金额","总扣卡金额","总锁定点数","总扣卡点数","总锁定积分","总扣卡积分"};
        String fileName=touristNo+touristRoute.getName()+DateUtils.formatDate(tourist.getStartDate(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(tourist.getEndDate(),"yyyy-MM-dd")+".xls";
        String sheetName = touristRoute.getName();

        Double totalLockTime=0.0;
        Double totalTime=0.0;
        Double totalLockAmount=0.0;
        Double totalAmount=0.0;
        Double totalLockPoint=0.0;
        Double totalPoint=0.0;
        Double totalLockIntegral=0.0;
        Double totalIntegral=0.0;

        for(Record record:list){
            totalLockTime+=record.getDouble("withholdTimes");
            totalTime+=record.getDouble("actualTimes");
            totalLockAmount+=record.getDouble("withholdAmount");
            totalAmount+=record.getDouble("actualAmount");
            //totalLockPoint+=record.getDouble("withholdPoints");
            //totalPoint+=record.getDouble("actualPoints");
            totalLockIntegral+=record.getDouble("withholdIntegrals");
            totalIntegral+=record.getDouble("actualIntegrals");
        }


        String[][] content=new String[1][12];

        content[0][0]=touristRoute.getName();
        content[0][1]=touristRoute.getCheckinDays().toString();
        content[0][2]= DateUtils.formatDate(tourist.getStartDate(),"yyyy-MM-dd");
        content[0][3]=DateUtils.formatDate(tourist.getEndDate(),"yyyy-MM-dd");
        content[0][4]=totalLockTime.toString();
        content[0][5]=totalTime.toString();
        content[0][6]=totalLockAmount.toString();
        content[0][7]=totalAmount.toString();
        content[0][8]=totalLockPoint.toString();
        content[0][9]=totalPoint.toString();
        content[0][10]=totalLockIntegral.toString();
        content[0][11]=totalIntegral.toString();

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 跳转旅游团信息界面
      */
    public void form(){
        String id = getPara("id");
        String startDate=getPara("startDate");

        Record record = Db.findFirst("select id,tourist_no as touristNo,name,route,route_id as routeId,start_date as startDate," +
                "end_date as endDate,expense_times as expenseTimes,status,del_flag as delFlag,create_by as createBy," +
                "create_time as createTime,update_by as updateBy,update_time as updateTime from fina_expense_record_tourist " +
                "where id = ?",id);

        //MainTouristRouteDeductConfig routeDeductConfig = mainTouristRouteDeductConfigService.findRouteDeductConfigByCheckinDate(startDate, id);
        //record.set()

        setAttr("tourist",record);
        render("touristExpenseRecordForm.html");
    }

    /**
     * 跳转旅游团结算界面
     */
    public void settleForm(){
        String touristNo = getPara("touristNo");
        String type = getPara("type");
        setAttr("touristNo",touristNo);
        setAttr("type",type);
        render("touristSettle.html");
    }

    public void touristCheckinDetail(){
        String id=getPara("id");
        FinaExpenseRecord record=finaExpenseRecordService.findById(id);
        setAttr("uniqueId",record.getUniqueId());
        render("touristCheckinDetail.html");
    }

    public void touristCheckinDetailList(){
        String uniqueId=getPara("uniqueId");

        List<Record> recordList=finaExpenseRecordTouristDetailService.findTouristDetailListByUniqueId(uniqueId);

        renderJson(new DataTable<Record>(recordList));
    }

    /**
     * 旅游团批量结算
     */
    public void touristSettle(){
        try {
            String billVal = getPara("billVal");
            logger.info("旅游团结算接收参数:[{}]", JSON.toJSONString(billVal));
            if(StringUtils.isEmpty(billVal) || !billVal.contains("0")){ renderJson(Ret.fail("msg", "没有扣卡数据，不可结算"));return; }
            Map<String, JSONArray> map = (Map)JSON.parse(billVal);

            List<Map<String,Object>> settleList = new ArrayList<>();
            for(int i=0;i<map.size();i++){
                String[] arr = map.get(i + "").toString().replace("[","").replace("]","").split(",");
                Map<String,Object> settleMap = new HashMap<>();
                settleMap.put("id",arr[0].substring(1,arr[0].length() -1));
                settleMap.put("cardId",arr[1].substring(1,arr[1].length() -1));
                settleMap.put("touristNo",arr[2].substring(1,arr[2].length() -1));
                settleMap.put("cardNumber",arr[3].substring(1,arr[3].length() -1));
                settleMap.put("name",arr[4].substring(1,arr[4].length() -1));
                settleMap.put("withholdTimes",arr[5] == null ? 0.00:arr[5].substring(1,arr[5].length() -1));
                settleMap.put("actualTimes",arr[6] == null ? 0.00:arr[6].substring(1,arr[6].length() -1));
                settleMap.put("withholdAmount",arr[7] == null ? 0.00:arr[7].substring(1,arr[7].length() -1));
                settleMap.put("actualAmount",arr[8] == null ? 0.00:arr[8].substring(1,arr[8].length() -1));
                settleMap.put("withholdPoints",arr[9] == null ? 0.00:arr[9].substring(1,arr[9].length() -1));
                settleMap.put("actualPoints",arr[10] == null ? 0.00:arr[10].substring(1,arr[10].length() -1));
                settleMap.put("withholdIntegrals",arr[11] == null ? 0.00:arr[11].substring(1,arr[11].length() -1));
                settleMap.put("actualIntegrals",arr[12] == null ? 0.00:arr[12].substring(1,arr[12].length() -1));

                Double actualTimes=Double.valueOf((String) settleMap.get("actualTimes"));
                Double actualAmount=Double.valueOf((String) settleMap.get("actualAmount"));
                Double actualPoints=Double.valueOf((String) settleMap.get("actualPoints"));
                Double actualIntegrals=Double.valueOf((String) settleMap.get("actualIntegrals"));
                if(actualTimes==null){actualTimes=0.0;}
                if(actualAmount==null){actualAmount=0.0;}
                if(actualPoints==null){actualPoints=0.0;}
                if(actualIntegrals==null){actualIntegrals=0.0;}
                if(actualTimes<0 || actualAmount<0 || actualPoints<0 || actualIntegrals<0){
                    renderJson(Ret.fail("msg","扣除值不能为负数"));
                    return;
                }


                settleList.add(settleMap);
            }

            //判断提交的旅居团账单数量是否正确
        /*String touristNo = (String) settleList.get(0).get("touristNo");
        Long count = Db.queryLong("select count(*) from fina_expense_record where del_flag = 0 and category = 'tourist_bill' and settle_status = 0 and tourist_no = ?",touristNo);
        if(count == null){  renderJson(Ret.fail("msg", "该旅游团账单不存在"));return; }
        if(count != settleList.size()){ renderJson(Ret.fail("msg", "请清空搜索后重新提交"));return; }*/


            //批量判断账单中的会员卡是否存在，余额是否可扣
            for (Map<String, Object> item : settleList) {
                String cardNumber = (String)item.get("cardNumber");
                FinaMembershipCard card  = finaMembershipCardService.getCardByNumber(cardNumber);
                if(card == null){ renderJson(Ret.fail("msg", cardNumber + "会员卡系统不存在"));return; }
                //判断会员卡扣卡规则
                MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
                if(scheme==null){
                    scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
                }

                if(scheme == null || StringUtils.isBlank(scheme.getDeductWay()) || scheme.getOnceConsume() == null){
                    renderJson(Ret.fail("msg", cardNumber + "会员卡扣卡规则未定义，无法扣卡"));return;
                }
                if(!DeductType.deductTimes.getKey().equals(scheme.getDeductWay()) && !DeductType.deductAmount.getKey().equals(scheme.getDeductWay())
                    && !DeductType.deductPoints.getKey().equals(scheme.getDeductWay())){
                    renderJson(Ret.fail("msg", "旅游团暂不支持"+ cardNumber + "会员卡扣卡规则"));return;
                }

                /*if(card.getConsumeTimes() == null){card.setConsumeTimes(0.00);}
                if(card.getBalance() == null){card.setBalance(0.00);}
                if(card.getConsumePoints()==null){card.setConsumePoints(0.0);}
                if(card.getCardIntegrals() == null){card.setCardIntegrals(0.00);}
                if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){
                    if(card.getConsumeTimes() < Double.valueOf((String)item.get("actualTimes"))){
                        renderJson(Ret.fail("msg", cardNumber + "会员卡天数不足，无法扣卡"));return;
                    }
                }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())){
                    if(card.getBalance() < Double.valueOf((String)item.get("actualAmount"))){
                        renderJson(Ret.fail("msg", cardNumber + "会员卡金额不足，无法扣卡"));return;
                    }
                }else if(DeductType.deductPoints.getKey().equals(scheme.getDeductWay())) {
                    if(card.getConsumePoints() < Double.valueOf((String)item.get("actualPoints"))){
                        renderJson(Ret.fail("msg", cardNumber + "会员卡点数不足，无法扣卡"));return;
                    }
                }*/
            }

            Map<String,Object> resultMap = finaExpenseRecordTouristService.touristSettle(settleList, AuthUtils.getUserId());
            if(resultMap.containsKey("error")){
                renderJson(Ret.fail("msg", resultMap.get("error")));
            }else if(resultMap.containsKey("suc")){
                renderJson(Ret.ok("msg", resultMap.get("suc")));
            }
        } catch (Exception e) {
            logger.error("旅游团批量单条/批量结算异常:[{}]",e.getMessage());
            e.printStackTrace();
            renderJson(Ret.fail("msg", "旅游团结算异常！"));
        }
    }



    /**
     * 跳转到更换会员卡&修改备注界面
     */
    public void changeForm(){
        String id = getPara("id");
        FinaExpenseRecordTouristSettleDetail touristSettleDetail=finaExpenseRecordTouristSettleDetailService.findById(id);
        FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(touristSettleDetail.getExpenseId());
        FinaMembershipCard card=finaMembershipCardService.findById(touristSettleDetail.getCardId());

        setAttr("touristSettleDetail",touristSettleDetail);
        setAttr("expenseRecord",expenseRecord);
        setAttr("card",card);
        String deductStr="";
        if(touristSettleDetail.getActualTimes()!=null && touristSettleDetail.getActualTimes()>0.0){
            deductStr+="天数："+touristSettleDetail.getActualTimes()+"；";
        }
        if(touristSettleDetail.getActualIntegrals()!=null && touristSettleDetail.getActualIntegrals()>0.0){
            deductStr+="积分："+touristSettleDetail.getActualIntegrals()+"；";
        }
        if(touristSettleDetail.getActualAmount()!=null && touristSettleDetail.getActualAmount()>0.0){
            deductStr+="金额："+touristSettleDetail.getActualAmount()+"；";
        }
        if(StrKit.isBlank(deductStr)){
            deductStr="0";
        }
        setAttr("deductStr",deductStr);
        render("touristExpenseRecordChange.html");
    }


    /**
     * 旅游团换卡，新增财务备注
     */
    public void touristChangeCardAndFinanceRemark(){
        try {
            String id = getPara("id");
            String touristNo = getPara("touristNo");
            String cardNumber = getPara("cardNumber");
            String actualStr = getPara("actual");
            String financeRemark = getPara("financeRemark");

            if(StringUtils.isBlank(cardNumber) && StringUtils.isBlank(financeRemark) && StringUtils.isBlank(actualStr)){ renderJson(Ret.fail("msg", "未填入数据，不可保存"));return; }
            Double actual = null;
            if(StringUtils.isNotBlank(actualStr)){
                actual = Double.parseDouble(actualStr);
            }

            FinaExpenseRecordTourist tourist = finaExpenseRecordTouristService.getTouristByNo(touristNo);
            if(tourist == null){ renderJson(Ret.fail("msg", "旅游团不存在，数据异常"));return; }

            FinaExpenseRecord er = finaExpenseRecordService.get(id);
            if(er == null){ renderJson(Ret.fail("msg", "旅游团账单不存在，数据异常"));return; }
            er.setFinanceRemark(financeRemark);

            Map<String,Object> map = finaExpenseRecordTouristService.dealTouristCardNumberAndFinanceRemark(tourist,er,cardNumber,actual,AuthUtils.getUserId());
            if(map.containsKey("error")){
                renderJson(Ret.fail("msg", map.get("error")));
            }else if(map.containsKey("suc")){
                renderJson(Ret.ok("msg", map.get("suc")));
            }
        } catch (Exception e) {
            logger.error("旅游团换卡，新增财务备注异常:[{}]",e.getMessage());
            renderJson(Ret.fail("msg", "旅游团换卡，新增财务备注异常！"));
        }
    }

    public void touristChangeCard(){
        String id = getPara("id");
        String cardId=getPara("cardId");
        String actualTimesStr = getPara("actualTimes");
        String actualIntegralsStr = getPara("actualIntegrals");
        String actualAmountStr = getPara("actualAmount");

        double actualTimes=0.0;
        double actualIntegrals=0.0;
        double actualAmount=0.0;
        if(StrKit.notBlank(actualTimesStr)){
            actualTimes=Double.valueOf(actualTimesStr);
        }
        if(StrKit.notBlank(actualIntegralsStr)){
            actualIntegrals=Double.valueOf(actualIntegralsStr);
        }
        if(StrKit.notBlank(actualAmountStr)){
            actualAmount=Double.valueOf(actualAmountStr);
        }

        FinaExpenseRecordTouristSettleDetail touristSettleDetail = finaExpenseRecordTouristSettleDetailService.findById(id);
        FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(touristSettleDetail.getExpenseId());
        FinaMembershipCard card=finaMembershipCardService.findById(cardId);
        FinaExpenseRecordDetail oldDetail = finaExpenseRecordDetailService.findFirstDetailsByExpenseId(touristSettleDetail.getId());

        if("1".equals(touristSettleDetail.getIsSettle())){
            renderJson(Ret.fail("msg", "该单为已扣卡状态，操作失败"));
            return;
        }
        if(card==null || "1".equals(card.getDelFlag()) || "1".equals(card.getIsLock()) || "1".equals(card.getExpireFlag())){
            renderJson(Ret.fail("msg", "会员卡状态异常，请检查会员卡是否为作废、锁定、过期等状态"));
            return;
        }
        if(Db.findFirst("select * from fina_expense_record_tourist_settle_detail where expense_id=? and card_id=? " +
                "and is_settle='0' and del_flag='0'",touristSettleDetail.getExpenseId(),cardId)!=null){
            renderJson(Ret.fail("msg", "跟团人["+expenseRecord.getName()+"]已存在1条["+card.getCardNumber()+"]未结算的记录"));
            return;
        }
        MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
        MainCardDeductScheme deductScheme=mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if(deductScheme==null){
            deductScheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        }
        if(deductScheme==null){
            renderJson(Ret.fail("msg", card.getCardNumber()+"会员卡扣卡方式未设置"));
            return;
        }
        Map<String, Double> cardLockInfo = finaMembershipCardService.getCardLockInfo(card.getCardNumber());



        FinaExpenseRecordTouristSettleDetail newTouristSettleDetail=new FinaExpenseRecordTouristSettleDetail();
        newTouristSettleDetail.setId(IdGen.getUUID());
        newTouristSettleDetail.setCardId(card.getId());
        newTouristSettleDetail.setExpenseId(expenseRecord.getId());
        newTouristSettleDetail.setWithholdTimes(0.0);
        newTouristSettleDetail.setActualTimes(0.0);
        newTouristSettleDetail.setWithholdIntegrals(0.0);
        newTouristSettleDetail.setActualIntegrals(0.0);
        newTouristSettleDetail.setWithholdAmount(0.0);
        newTouristSettleDetail.setActualAmount(0.0);
        newTouristSettleDetail.setDelFlag("0");
        newTouristSettleDetail.setIsSettle("0");
        newTouristSettleDetail.setCreateBy(AuthUtils.getUserId());
        newTouristSettleDetail.setCreateTime(new Date());
        newTouristSettleDetail.setUpdateBy(AuthUtils.getUserId());
        newTouristSettleDetail.setUpdateTime(new Date());

        FinaExpenseRecordDetail expenseRecordDetail=new FinaExpenseRecordDetail();
        expenseRecordDetail.setId(IdGen.getUUID());
        expenseRecordDetail.setExpenseId(newTouristSettleDetail.getId());
        expenseRecordDetail.setCardNumber(card.getCardNumber());
        expenseRecordDetail.setIsSettled("0");
        expenseRecordDetail.setDelFlag("0");
        expenseRecordDetail.setStartTime(expenseRecord.getCheckinTime());
        expenseRecordDetail.setEndTime(expenseRecord.getCheckoutTime());
        expenseRecordDetail.setCreateBy(AuthUtils.getUserId());
        expenseRecordDetail.setCreateTime(new Date());
        expenseRecordDetail.setUpdateBy(AuthUtils.getUserId());
        expenseRecordDetail.setUpdateTime(new Date());
        expenseRecordDetail.setTimes(0.0);
        expenseRecordDetail.setAmount(0.0);
        expenseRecordDetail.setIntegrals(0.0);

        if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
            if("1".equals(cardType.getIsIntegral())){
                double times = BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockConsumeTimes"))).subtract(BigDecimal.valueOf(actualTimes)).doubleValue();
                double integrals = BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockIntegrals"))).subtract(BigDecimal.valueOf(actualIntegrals)).doubleValue();
                if(times<0){
                    renderJson(Ret.fail("msg", card.getCardNumber()+"会员卡可用天数不足，请检查"));
                    return;
                }
                if(integrals<0){
                    renderJson(Ret.fail("msg", card.getCardNumber()+"会员卡可用积分不足，请检查"));
                    return;
                }
                expenseRecordDetail.setTimes(actualTimes);
                expenseRecordDetail.setIntegrals(actualIntegrals);

                newTouristSettleDetail.setWithholdTimes(actualTimes);
                newTouristSettleDetail.setActualTimes(actualTimes);
                newTouristSettleDetail.setWithholdIntegrals(actualIntegrals);
                newTouristSettleDetail.setActualIntegrals(actualIntegrals);
            }else{
                double times = BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockConsumeTimes"))).subtract(BigDecimal.valueOf(actualTimes)).doubleValue();
                if(times<0){
                    renderJson(Ret.fail("msg", card.getCardNumber()+"会员卡可用天数不足，请检查"));
                    return;
                }
                expenseRecordDetail.setTimes(actualTimes);

                newTouristSettleDetail.setWithholdTimes(actualTimes);
                newTouristSettleDetail.setActualTimes(actualTimes);
            }
        }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
            double amount = BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockBalance"))).subtract(BigDecimal.valueOf(actualAmount)).doubleValue();
            if(amount<0){
                renderJson(Ret.fail("msg", card.getCardNumber()+"会员卡可用金额不足，请检查"));
                return;
            }
            expenseRecordDetail.setAmount(actualAmount);

            newTouristSettleDetail.setWithholdAmount(actualAmount);
            newTouristSettleDetail.setActualAmount(actualAmount);
        }
        oldDetail.setDelFlag("1");
        oldDetail.setUpdateBy(AuthUtils.getUserId());
        oldDetail.setUpdateTime(new Date());

        touristSettleDetail.setDelFlag("1");
        touristSettleDetail.setUpdateBy(AuthUtils.getUserId());
        touristSettleDetail.setUpdateTime(new Date());

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    return newTouristSettleDetail.save()&&expenseRecordDetail.save()&&oldDetail.update()&&touristSettleDetail.update();
                }catch (Exception e){
                    e.printStackTrace();
                    return false;
                }

            }
        });
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void checkinBaseSummary(){
        String touristNo=getPara("touristNo");

        String sql="select a.`name` as memberName,a.card_number as cardNumber,c.base_name as baseName,(TO_DAYS(b.end_date)-TO_DAYS(b.start_date)) as checkinDays from fina_expense_record a  " +
                "left join fina_expense_record_tourist_detail b on a.unique_id=b.unique_id " +
                "left join main_base c on c.id=b.base_id " +
                "where a.del_flag='0' and a.category='tourist_bill' and a.settle_status in ('0','1') and a.tourist_no=? ";

        List<Record> recordList=Db.find(sql,touristNo);

        Map<String,Map<String,Integer>> map=new HashMap<>();
        Set<String> baseNameSet=new HashSet<>();
        for(Record record:recordList){
            if(StrKit.isBlank(record.getStr("memberName"))){
                continue;
            }
            if(map.containsKey(record.getStr("memberName"))){
                Map<String,Integer> checkinMap=map.get(record.getStr("memberName"));
                if(checkinMap.containsKey(record.getStr("baseName"))){
                    checkinMap.put(record.getStr("baseName"),record.getInt("checkinDays")+checkinMap.get(record.getStr("baseName")));
                }else{
                    checkinMap.put(record.getStr("baseName"),record.getInt("checkinDays"));
                }
            }else{
                Map<String,Integer> checkinMap=new HashMap<>();
                checkinMap.put(record.getStr("baseName"),record.getInt("checkinDays"));
                map.put(record.getStr("memberName"),checkinMap);
            }
        }
        List<Record> returnList=new ArrayList<>();
        for(String key:map.keySet()){
            Record record=new Record();
            record.set("memberName",key);
            Map<String,Integer> checkinMap=map.get(key);
            for(String baseName:checkinMap.keySet()){
                if(StrKit.notBlank(baseName)){
                    baseNameSet.add(baseName);
                }
                record.set(baseName,checkinMap.get(baseName));
            }
            returnList.add(record);
        }

        /*for(String key:map.keySet()){
            Map<String,Integer> checkinMap=map.get(key);
            for(String set:baseNameSet){
                if(!checkinMap.containsKey(set)){
                    checkinMap.put(set,0);
                }
            }
        }*/

        set("map",map);
        set("baseNameSet",baseNameSet);
        set("touristNo",touristNo);
        render("checkinBaseSummary.html");
    }

    public void checkinBaseSummaryList(){
        String touristNo=getPara("touristNo");

        String sql="select a.`name` as memberName,a.card_number as cardNumber,c.base_name as baseName,(TO_DAYS(b.end_date)-TO_DAYS(b.start_date)) as checkinDays from fina_expense_record a  " +
                "left join fina_expense_record_tourist_detail b on a.unique_id=b.unique_id " +
                "left join main_base c on c.id=b.base_id " +
                "where a.del_flag='0' and a.category='tourist_bill' and a.settle_status in ('0','1') and a.tourist_no=? ";

        List<Record> recordList=Db.find(sql,touristNo);

        Map<String,Map<String,Integer>> map=new HashMap<>();
        Set<String> baseNameSet=new HashSet<>();
        for(Record record:recordList){
            if(map.containsKey(record.getStr("memberName"))){
                Map<String,Integer> checkinMap=map.get(record.getStr("memberName"));
                if(checkinMap.containsKey(record.getStr("baseName"))){
                    checkinMap.put(record.getStr("baseName"),record.getInt("checkinDays")+checkinMap.get(record.getStr("baseName")));
                }else{
                    checkinMap.put(record.getStr("baseName"),record.getInt("checkinDays"));
                }
            }else{
                Map<String,Integer> checkinMap=new HashMap<>();
                checkinMap.put(record.getStr("baseName"),record.getInt("checkinDays"));
                map.put(record.getStr("memberName"),checkinMap);
            }
            baseNameSet.add(record.getStr("memberName"));
        }
        List<Record> returnList=new ArrayList<>();
        for(String key:map.keySet()){
            Record record=new Record();
            record.set("memberName",key);
            Map<String,Integer> checkinMap=map.get(key);
            for(String baseName:checkinMap.keySet()){
                record.set(baseName,checkinMap.get(baseName));
            }
            returnList.add(record);
        }
        setAttr("baseNames",baseNameSet);
        renderJson(new DataTable<Record>(returnList));
    }

    public void addForm(){
        String touristNo=getPara("touristNo");

        setAttr("touristNo",touristNo);
        render("addForm.html");
    }

    public void splitForm(){
        String expenseId=getPara("expenseId");
        FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(expenseId);
        setAttr("expenseRecord",expenseRecord);
        render("splitForm.html");
    }

    public void touristRecordSettleDetailPage(){
        String expenseId=getPara("expenseId");

        List<Record> recordList=finaExpenseRecordTouristSettleDetailService.findListByExpenseId(expenseId,"0");

        renderJson(new DataTable<Record>(recordList));
    }

    @Inject
    private FinaExpenseRecordDetailService finaExpenseRecordDetailService;


    public void touristRecordSettleDetailSave(){
        String dataJsonStr=getPara("data");
        String expenseId=getPara("expenseId");

        List<FinaExpenseRecordTouristSettleDetail> settleDetailList=new ArrayList<>();

        JSONArray jsonArray=JSON.parseArray(dataJsonStr);

        Map<String,Record> cardRecordMap=new HashMap<>();

        List<String> cardNumberList=new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            FinaExpenseRecordTouristSettleDetail touristSettleDetail=jsonObject.toJavaObject(FinaExpenseRecordTouristSettleDetail.class);

            if(StrKit.notBlank(touristSettleDetail.getId())){
                FinaExpenseRecordTouristSettleDetail oldTouristSettleDetail=finaExpenseRecordTouristSettleDetailService.findById(touristSettleDetail.getId());
                if(oldTouristSettleDetail!=null && "1".equals(oldTouristSettleDetail.getIsSettle())){
                    continue;
                }
            }

            if(touristSettleDetail.getActualAmount()==null){
                touristSettleDetail.setActualAmount(0.0);
            }
            if(touristSettleDetail.getActualTimes()==null){
                touristSettleDetail.setActualTimes(0.0);
            }
            if(touristSettleDetail.getActualIntegrals()==null){
                touristSettleDetail.setActualIntegrals(0.0);
            }
            String cardNumber=jsonObject.getString("cardNumber");
            FinaMembershipCard card=finaMembershipCardService.findCardByCardNumber(cardNumber);

            if(card==null){
                renderJson(Ret.fail("msg",cardNumber+"会员卡为空"));
                return;
            }
            if(cardNumberList.contains(card.getCardNumber())){
                renderJson(Ret.fail("msg",cardNumber+"同一个会员卡只允许一条记录"));
                return;
            }
            cardNumberList.add(card.getCardNumber());
            touristSettleDetail.setCardId(card.getId());
            touristSettleDetail.setExpenseId(expenseId);
            touristSettleDetail.setWithholdTimes(touristSettleDetail.getActualTimes());
            touristSettleDetail.setWithholdIntegrals(touristSettleDetail.getActualIntegrals());
            touristSettleDetail.setWithholdAmount(touristSettleDetail.getActualAmount());


            if(cardRecordMap.containsKey(card.getCardNumber())){
                Record record=cardRecordMap.get(card.getCardNumber());
                record.set("actualAmount",record.getDouble("actualAmount")+touristSettleDetail.getActualAmount());
                record.set("actualIntegrals",record.getDouble("actualIntegrals")+touristSettleDetail.getActualIntegrals());
                record.set("actualTimes",record.getDouble("actualTimes")+touristSettleDetail.getActualTimes());
            }else{
                Record record=new Record();
                record.set("actualAmount",touristSettleDetail.getActualAmount());
                record.set("actualIntegrals",touristSettleDetail.getActualIntegrals());
                record.set("actualTimes",touristSettleDetail.getActualTimes());
                record.set("card",card);
                cardRecordMap.put(card.getCardNumber(),record);
            }
            settleDetailList.add(touristSettleDetail);
        }

        if(settleDetailList.size()==0){
            renderJson(Ret.ok("msg","操作失败，保存的记录为0条"));
            return;
        }

        //获取当前订单锁定信息
        String expenseLockSql="select a.card_id,SUM(b.times) as lockTimes,SUM(b.amount) as lockAmount,SUM(b.integrals) as lockIntegrals from fina_expense_record_tourist_settle_detail a " +
                "INNER JOIN fina_expense_record_detail b on a.id=b.expense_id where a.del_flag='0' and a.is_settle='0' and b.del_flag='0' and b.is_settled='0' " +
                "and a.expense_id=? GROUP BY a.card_id ";

        List<Record> recordList=Db.find(expenseLockSql,expenseId);

        Map<String,Record> expenseLockMap=new HashMap<>();
        for (Record record : recordList) {
            expenseLockMap.put(record.getStr("card_id"),record);
        }

        for (String key : cardRecordMap.keySet()) {
            Record record=cardRecordMap.get(key);
            FinaMembershipCard card=record.get("card");
            Double actualAmount=record.get("actualAmount");
            Double actualIntegrals=record.get("actualIntegrals");
            Double actualTimes=record.get("actualTimes");
            if(actualAmount==null){
                actualAmount=0.0;
            }
            if(actualIntegrals==null){
                actualIntegrals=0.0;
            }
            if(actualTimes==null){
                actualTimes=0.0;
            }

            double addTimes=0.0;
            double addAmount=0.0;
            double addIntegrals=0.0;
            if(card.getCardIntegrals()==null){
                card.setCardIntegrals(0.0);
            }
            if(card.getConsumeTimes()==null){
                card.setConsumeTimes(0.0);
            }
            if(card.getBalance()==null){
                card.setBalance(0.0);
            }
            if(expenseLockMap.containsKey(card.getId())){
                Record addLockRecord=expenseLockMap.get(card.getId());
                if(addLockRecord.getDouble("lockTimes")!=null){
                    addTimes=addLockRecord.getDouble("lockTimes");
                }
                if(addLockRecord.getDouble("lockAmount")!=null){
                    addAmount=addLockRecord.getDouble("lockAmount");
                }
                if(addLockRecord.getDouble("lockIntegrals")!=null){
                    addIntegrals=addLockRecord.getDouble("lockIntegrals");
                }
            }
            Map<String,Double> cardLockInfo=finaMembershipCardService.getCardLockInfo(card.getCardNumber());
            if(card.getConsumeTimes()-cardLockInfo.get("lockConsumeTimes")+addTimes-actualTimes<0){
                renderJson(Ret.ok("msg",card.getCardNumber()+"会员卡可用天数不足"));
                return;
            }
            if(card.getBalance()-cardLockInfo.get("lockBalance")+addAmount-actualAmount<0){
                renderJson(Ret.ok("msg",card.getCardNumber()+"会员卡可用余额不足"));
                return;
            }
            if(card.getCardIntegrals()-cardLockInfo.get("lockIntegrals")+addIntegrals-actualIntegrals<0){
                renderJson(Ret.ok("msg",card.getCardNumber()+"会员卡可用积分不足"));
                return;
            }
        }

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                for (FinaExpenseRecordTouristSettleDetail touristSettleDetail : settleDetailList) {

                    FinaMembershipCard card=finaMembershipCardService.findById(touristSettleDetail.getCardId());

                    boolean isAdd=StrKit.isBlank(touristSettleDetail.getId());

                    if(finaExpenseRecordTouristSettleDetailService.saveTouristSettleDetail(touristSettleDetail,AuthUtils.getUserId())){
                        if(isAdd){
                            FinaExpenseRecordDetail detail=new FinaExpenseRecordDetail();
                            detail.setId(IdGen.getUUID());
                            detail.setCardNumber(card.getCardNumber());
                            detail.setDelFlag("0");
                            detail.setExpenseId(touristSettleDetail.getId());
                            detail.setIsSettled("0");
                            detail.setCreateTime(new Date());
                            detail.setUpdateTime(new Date());
                            detail.setCreateBy(AuthUtils.getUserId());
                            detail.setUpdateBy(AuthUtils.getUserId());
                            detail.setTimes(touristSettleDetail.getActualTimes());
                            detail.setAmount(touristSettleDetail.getActualAmount());
                            detail.setIntegrals(touristSettleDetail.getActualIntegrals());
                            detail.setPoints(0.0);
                            detail.setBeanCoupons(0.0);
                            if(!detail.save()){
                                return false;
                            }
                        }else{
                            FinaExpenseRecordDetail detail=finaExpenseRecordDetailService.findFirstDetailsByExpenseId(touristSettleDetail.getId());
                            detail.setUpdateTime(new Date());
                            detail.setUpdateBy(AuthUtils.getUserId());
                            detail.setTimes(touristSettleDetail.getActualTimes());
                            detail.setAmount(touristSettleDetail.getActualAmount());
                            detail.setIntegrals(touristSettleDetail.getActualIntegrals());
                            detail.setPoints(0.0);
                            detail.setBeanCoupons(0.0);
                            if(!detail.update()){
                                return false;
                            }
                        }
                    }else{
                        return false;
                    }
                }
                return true;
            }
        });
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void touristRecordSettleDetailDel(){
        String id=getPara("id");
        String expenseId=getPara("expenseId");

        if(Db.findFirst("select * from fina_expense_record_tourist_settle_detail where del_flag='0' and expense_id=? and id<>? ",expenseId,id)==null){
            renderJson(Ret.fail("msg","操作失败，该记录为最后1条"));
            return;
        }

        FinaExpenseRecordTouristSettleDetail touristSettleDetail = finaExpenseRecordTouristSettleDetailService.findById(id);
        touristSettleDetail.setDelFlag("1");
        boolean flag=false;


        if(finaExpenseRecordTouristSettleDetailService.saveTouristSettleDetail(touristSettleDetail,AuthUtils.getUserId())){
            FinaExpenseRecordDetail detail=finaExpenseRecordDetailService.findFirstDetailsByExpenseId(touristSettleDetail.getId());
            detail.setDelFlag("1");
            detail.setUpdateBy(AuthUtils.getUserId());
            detail.setUpdateTime(new Date());
            detail.update();
            flag=true;
        }
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void settleAllTouristSettleDetail(){


        String dataStr=getPara("data");
        String touristNo=getPara("touristNo");
        final String userId = AuthUtils.getUserId();

        JSONArray jsonArray=JSON.parseArray(dataStr);

        if(jsonArray.size()==0){
            renderJson(Ret.fail("msg","结算条数不能为0"));
            return;
        }

        //判断批量结算是否选取到了全部记录
        Integer notSettleNum=Db.queryInt("select COUNT(a.id) from fina_expense_record a " +
                "left join fina_expense_record_tourist_settle_detail b on a.id=b.expense_id " +
                " where a.tourist_no=? and a.del_flag='0' and a.settle_status='0' and b.del_flag='0' and b.is_settle='0' ",touristNo);

        if(jsonArray.size()!=notSettleNum){
            renderJson(Ret.fail("msg","结算记录条数与未结算条数对应不上，请清空查询条件查询一下再提交"));
            return;
        }

        Map<String,Record> cardDeductMap=new HashMap<>();
        Set<String> settleDetailIdSet=new HashSet<>();
        Map<String,FinaMembershipCard> cardMap=new HashMap<>();
        Map<String,MainCardDeductScheme> cardDeductSchemeMapMap=new HashMap<>();
        Map<String, MainMembershipCardType> cardTypeHashMap=new HashMap<>();

        FinaExpenseRecordTourist expenseRecordTourist=finaExpenseRecordTouristService.getTouristByNo(touristNo);

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            Double actualTimes=jsonObject.getDouble("actualTimes");
            Double actualAmount=jsonObject.getDouble("actualAmount");
            Double actualIntegrals=jsonObject.getDouble("actualIntegrals");
            String financeRemark=jsonObject.getString("financeRemark");
            if(actualTimes==null){
                actualTimes=0.0;
            }
            if(actualAmount==null){
                actualAmount=0.0;
            }
            if(actualIntegrals==null){
                actualIntegrals=0.0;
            }

            FinaExpenseRecordTouristSettleDetail settleDetail=finaExpenseRecordTouristSettleDetailService.findById(jsonObject.getString("id"));
            if("1".equals(settleDetail.getIsSettle())){
                renderJson(Ret.fail("msg","提交数据存在已结算记录，请刷新再重试"));
                return;
            }
            if("1".equals(settleDetail.getDelFlag())){
                renderJson(Ret.fail("msg","提交数据存在已作废记录，请刷新再重试"));
                return;
            }
            settleDetailIdSet.add(jsonObject.getString("id"));
            FinaExpenseRecordDetail recordDetail = finaExpenseRecordDetailService.findFirstDetailsByExpenseId(settleDetail.getId());
            if(recordDetail.getTimes()==null){
                recordDetail.setTimes(0.0);
            }
            if(recordDetail.getAmount()==null){
                recordDetail.setAmount(0.0);
            }
            if(recordDetail.getIntegrals()==null){
                recordDetail.setIntegrals(0.0);
            }
            FinaMembershipCard card = finaMembershipCardService.findById(settleDetail.getCardId());
            if(card.getCardIntegrals()==null){
                card.setCardIntegrals(0.0);
            }
            if(card.getConsumeTimes()==null){
                card.setConsumeTimes(0.0);
            }
            if(card.getBalance()==null){
                card.setBalance(0.0);
            }

            FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(settleDetail.getExpenseId());

            if(cardDeductMap.containsKey(card.getId())){
                Record record=cardDeductMap.get(card.getId());
                record.set("actualTimes",actualTimes+record.getDouble("actualTimes"));
                record.set("actualAmount",actualAmount+record.getDouble("actualAmount"));
                record.set("actualIntegrals",actualIntegrals+record.getDouble("actualIntegrals"));
                record.set("addTimes",recordDetail.getTimes()+record.getDouble("addTimes"));
                record.set("addAmount",recordDetail.getAmount()+record.getDouble("addAmount"));
                record.set("addIntegrals",recordDetail.getIntegrals()+record.getDouble("addIntegrals"));

                Set<String> names=record.get("nameSet");
                names.add(expenseRecord.getName());
            }else{
                Set<String> names=new HashSet<>();
                names.add(expenseRecord.getName());

                Record record=new Record();
                record.set("actualTimes",actualTimes);
                record.set("actualAmount",actualAmount);
                record.set("actualIntegrals",actualIntegrals);
                record.set("addTimes",recordDetail.getTimes());
                record.set("addAmount",recordDetail.getAmount());
                record.set("addIntegrals",recordDetail.getIntegrals());
                Map<String, Double> cardLockInfo = finaMembershipCardService.getCardLockInfo(card.getCardNumber());
                record.set("lockTimes",cardLockInfo.get("lockConsumeTimes"));
                record.set("lockAmount",cardLockInfo.get("lockBalance"));
                record.set("lockIntegrals",cardLockInfo.get("lockIntegrals"));
                record.set("card",card);
                record.set("nameSet",names);
                cardDeductMap.put(card.getId(),record);

                MainCardDeductScheme deductScheme=mainCardDeductSchemeService.findById(card.getDeductSchemeId());
                if(deductScheme==null){
                    deductScheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
                }

                cardDeductSchemeMapMap.put(card.getId(),deductScheme);
                cardTypeHashMap.put(card.getId(),mainMembershipCardTypeService.findById(card.getCardTypeId()));

            }
            cardMap.put(card.getId(),card);
        }

        Map<String,Double> cardBeanCouponsMap=new HashMap<>();

        for (String cardId : cardDeductMap.keySet()) {
            Record record=cardDeductMap.get(cardId);
            FinaMembershipCard card=record.get("card");
            if("1".equals(card.getIsLock())){
                renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡已锁定"));
                return;
            }
            if(card.getConsumeTimes()<(record.getDouble("actualTimes")+record.getDouble("lockTimes")-record.getDouble("addTimes"))){
                renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡可用天数不足"));
                return;
            }
            if(card.getBalance()<(record.getDouble("actualAmount")+record.getDouble("lockAmount")-record.getDouble("addAmount"))){
                renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡可用余额不足"));
                return;
            }
            if(card.getCardIntegrals()<(record.getDouble("actualIntegrals")+record.getDouble("lockIntegrals")-record.getDouble("addIntegrals"))){
                renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡可用积分不足"));
                return;
            }
            Double totalBalanceRollValue=Db.queryDouble("select SUM(balance_roll_value) totalBalanceRollValue  from fina_card_roll a " +
                    " INNER JOIN crm_card_roll_record b on a.roll_id=b.id " +
                    " INNER JOIN crm_card_roll c on c.id=b.card_roll_id  " +
                    " where b.del_flag='0' and b.is_enable='0' and a.card_id=? and c.roll_type_id='86F75466-2CD4-455C-9A31-0CFF0846BD7B' ",cardId);
            if(totalBalanceRollValue==null){
                totalBalanceRollValue=0.0;
            }
            cardBeanCouponsMap.put(cardId,totalBalanceRollValue);

        }






        List<FinaExpenseRecordTouristSettleDetail> updateTouristSettleDetailList=new ArrayList<>();
        List<FinaExpenseRecordDetail> updateExpenseRecordDetailList=new ArrayList<>();
        List<FinaCardTransactions> addCardTransactionsList=new ArrayList<>();
        List<FinaMembershipCard> updateCardList=new ArrayList<>();
        List<FinaExpenseRecord> updateExpenseRecordList=new ArrayList<>();
        //生成消费购买、赠送明细列表记录
        List<FinaCardTransactionsDetail> transactionsDetailList = new ArrayList<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Double actualTimes = jsonObject.getDouble("actualTimes");
            Double actualAmount = jsonObject.getDouble("actualAmount");
            Double actualIntegrals = jsonObject.getDouble("actualIntegrals");
            String financeRemark=jsonObject.getString("financeRemark");
            if (actualTimes == null) {
                actualTimes = 0.0;
            }
            if (actualAmount == null) {
                actualAmount = 0.0;
            }
            if (actualIntegrals == null) {
                actualIntegrals = 0.0;
            }
            FinaExpenseRecordTouristSettleDetail settleDetail=finaExpenseRecordTouristSettleDetailService.findById(jsonObject.getString("id"));
            settleDetail.setActualTimes(actualTimes);
            settleDetail.setActualAmount(actualAmount);
            settleDetail.setActualIntegrals(actualIntegrals);
            settleDetail.setFinanceRemark(financeRemark);

            FinaMembershipCard card=cardMap.get(settleDetail.getCardId());
            if(card.getConsumeTimes()==null){
                card.setConsumeTimes(0.0);
            }
            if(card.getBuyRechargeDays() == null){
                card.setBuyRechargeDays(0.00);
            }
            if(card.getGiveRechargeDays() == null){
                card.setGiveRechargeDays(0.00);
            }
            if(card.getBalance()==null){
                card.setBalance(0.0);
            }
            if(card.getBuyRechargeAmount() == null){
                card.setBuyRechargeAmount(0.00);
            }
            if(card.getGiveRechargeAmount() == null){
                card.setGiveRechargeAmount(0.00);
            }
            if(card.getCardIntegrals()==null){
                card.setCardIntegrals(0.0);
            }
            if(card.getBuyRechargeIntegrals() == null){
                card.setBuyRechargeIntegrals(0.00);
            }
            if(card.getGiveRechargeIntegrals() == null){
                card.setGiveRechargeIntegrals(0.00);
            }

            double buyAmount = 0.0;
            double giveAmount = 0.0;
            double buyDays = 0.0;
            double giveDays = 0.0;
            double giveIntegrals = 0.0;

            card.setConsumeTimes(card.getConsumeTimes()-actualTimes);
            //计算消费明细的购买、赠送天数分别是多少
            if(card.getGiveRechargeDays() >= actualTimes){
                giveDays = actualTimes;
                if(card.getGiveRechargeDays() > 0){
                    card.setGiveRechargeDays(card.getGiveRechargeDays()-actualTimes);
                }
            }else{
                giveDays = card.getGiveRechargeDays();
                buyDays = actualTimes-card.getGiveRechargeDays();
                //分别扣减购买天数、赠送天数
                card.setGiveRechargeDays(0.0);
                if(card.getBuyRechargeDays() > 0){
                    card.setBuyRechargeDays(card.getBuyRechargeDays()-buyDays);
                }
            }
            card.setBalance(card.getBalance()-actualAmount);
            //计算消费明细的购买、赠送金额分别是多少
            if(card.getGiveRechargeAmount() >= actualAmount){
                giveAmount = actualAmount;
                if(card.getGiveRechargeAmount() > 0){
                    card.setGiveRechargeAmount(card.getGiveRechargeAmount()-actualAmount);
                }
            }else{
                giveAmount = card.getGiveRechargeAmount();
                buyAmount = actualAmount-card.getGiveRechargeAmount();
                //分别扣减购买金额、赠送金额
                card.setGiveRechargeAmount(0.0);
                if(card.getBuyRechargeAmount() > 0){
                    card.setBuyRechargeAmount(card.getBuyRechargeAmount()-buyAmount);
                }
            }
            card.setCardIntegrals(BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(actualIntegrals)).doubleValue());
            //计算消费明细赠送积分多少
            giveIntegrals = actualIntegrals;
            if(card.getGiveRechargeIntegrals() > 0){
                card.setGiveRechargeIntegrals(BigDecimal.valueOf(card.getGiveRechargeIntegrals()).subtract(BigDecimal.valueOf(actualIntegrals)).doubleValue());
            }
            card.setUpdateTime(new Date());
            updateCardList.add(card);

            FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(settleDetail.getExpenseId());
            boolean isAdd=true;
            for (FinaExpenseRecord finaExpenseRecord : updateExpenseRecordList) {
                if(finaExpenseRecord.getId().equals(expenseRecord.getId())){
                    isAdd=false;
                    break;
                }
            }
            if(isAdd){
                expenseRecord.setSettleStatus("1");
                expenseRecord.setUpdateTime(new Date());
                updateExpenseRecordList.add(expenseRecord);
            }

            MainCardDeductScheme cardDeductScheme=cardDeductSchemeMapMap.get(card.getId());
            MainMembershipCardType cardType=cardTypeHashMap.get(card.getId());

            FinaCardTransactions transactions=new FinaCardTransactions();
            transactions.setId(IdGen.getUUID());
            transactions.setCardId(settleDetail.getCardId());
            transactions.setExpenseId(settleDetail.getExpenseId());
            transactions.setConsumeType(ConsumeType.TOURIST);
            transactions.setType(AccountRecordOperateType.dailyDeduction.getKey());
            transactions.setInOutFlag("2");
            transactions.setStartDate(expenseRecordTourist.getStartDate());
            transactions.setEndDate(expenseRecordTourist.getEndDate());
            transactions.setAmount(actualAmount);
            transactions.setTimes(actualTimes);
            transactions.setIntegrals(actualIntegrals);
            transactions.setPoints(0.0);
            transactions.setBeanCoupons(0.0);
            transactions.setBusinessEntityId(expenseRecord.getBusinessEntityId());
            transactions.setTimesSnapshot(card.getConsumeTimes());
            transactions.setAmountSnapshot(card.getBalance());
            transactions.setIntegralsSnapshot(card.getCardIntegrals());
            transactions.setPointsSnapshot(0.0);
            transactions.setBeanCouponsSnapshot(cardBeanCouponsMap.get(card.getId()));
            transactions.setPoints(0.0);
            transactions.setIsHidden("0");

            String describe="";
            if(DeductType.deductTimes.getKey().equals(cardDeductScheme.getDeductWay())){
                if("1".equals(cardType.getIsIntegral())){
                    describe="["+expenseRecord.getName()+"]使用["+card.getCardNumber()+"]会员卡参与["+expenseRecordTourist.getName()+"]旅游团从" +
                            "["+ TimeUtils.getStrDateByPattern(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+
                            TimeUtils.getStrDateByPattern(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+"]时间段扣卡["+actualTimes+"]天,["+actualIntegrals+"]积分";
                }else{
                    describe="["+expenseRecord.getName()+"]使用["+card.getCardNumber()+"]会员卡参与["+expenseRecordTourist.getName()+"]旅游团从" +
                            "["+ TimeUtils.getStrDateByPattern(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+
                            TimeUtils.getStrDateByPattern(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+"]时间段扣卡["+actualTimes+"]天";
                }
            }else if(DeductType.deductAmount.getKey().equals(cardDeductScheme.getDeductWay())){
                describe="["+expenseRecord.getName()+"]使用["+card.getCardNumber()+"]会员卡参与["+expenseRecordTourist.getName()+"]旅游团从" +
                        "["+ TimeUtils.getStrDateByPattern(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+
                        TimeUtils.getStrDateByPattern(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+"]时间段扣卡["+actualAmount+"]元";

            }
            transactions.setDescribe(describe);
            transactions.setDealTime(new Date());
            addCardTransactionsList.add(transactions);

            if(giveDays > 0){
                FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
                transactionsDetail.setId(IdGen.getUUID());
                transactionsDetail.setTransactionsId(transactions.getId());
                transactionsDetail.setDeductType("give");
                transactionsDetail.setSettleType("days");
                transactionsDetail.setDeductValue(giveDays);
                transactionsDetail.setDelFlag(DelFlag.NORMAL);
                transactionsDetail.setCreateTime(new Date());
                transactionsDetail.setCreateBy(userId);
                transactionsDetailList.add(transactionsDetail);
            }
            if(buyDays > 0){
                FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
                transactionsDetail.setId(IdGen.getUUID());
                transactionsDetail.setTransactionsId(transactions.getId());
                transactionsDetail.setDeductType("buy");
                transactionsDetail.setSettleType("days");
                transactionsDetail.setDeductValue(buyDays);
                transactionsDetail.setDelFlag(DelFlag.NORMAL);
                transactionsDetail.setCreateTime(new Date());
                transactionsDetail.setCreateBy(userId);
                transactionsDetailList.add(transactionsDetail);
            }
            if(giveAmount > 0){
                FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
                transactionsDetail.setId(IdGen.getUUID());
                transactionsDetail.setTransactionsId(transactions.getId());
                transactionsDetail.setDeductType("give");
                transactionsDetail.setSettleType("amount");
                transactionsDetail.setDeductValue(giveAmount);
                transactionsDetail.setDelFlag(DelFlag.NORMAL);
                transactionsDetail.setCreateTime(new Date());
                transactionsDetail.setCreateBy(userId);
                transactionsDetailList.add(transactionsDetail);
            }
            if(buyAmount > 0){
                FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
                transactionsDetail.setId(IdGen.getUUID());
                transactionsDetail.setTransactionsId(transactions.getId());
                transactionsDetail.setDeductType("buy");
                transactionsDetail.setSettleType("amount");
                transactionsDetail.setDeductValue(buyAmount);
                transactionsDetail.setDelFlag(DelFlag.NORMAL);
                transactionsDetail.setCreateTime(new Date());
                transactionsDetail.setCreateBy(userId);
                transactionsDetailList.add(transactionsDetail);
            }
            if(giveIntegrals > 0){
                FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
                transactionsDetail.setId(IdGen.getUUID());
                transactionsDetail.setTransactionsId(transactions.getId());
                transactionsDetail.setDeductType("give");
                transactionsDetail.setSettleType("integrals");
                transactionsDetail.setDeductValue(giveIntegrals);
                transactionsDetail.setDelFlag(DelFlag.NORMAL);
                transactionsDetail.setCreateTime(new Date());
                transactionsDetail.setCreateBy(userId);
                transactionsDetailList.add(transactionsDetail);
            }


            settleDetail.setIsSettle("1");
            settleDetail.setUpdateTime(new Date());
            updateTouristSettleDetailList.add(settleDetail);


            FinaExpenseRecordDetail expenseRecordDetail=finaExpenseRecordDetailService.findFirstDetailsByExpenseId(settleDetail.getId());
            expenseRecordDetail.setIsSettled("1");
            expenseRecordDetail.setUpdateTime(new Date());
            updateExpenseRecordDetailList.add(expenseRecordDetail);
        }

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                try {
                    int[] nums=Db.batchUpdate(updateCardList,updateCardList.size());
                    for (int num : nums) {
                        if(num<=0){
                            return false;
                        }
                    }
                    nums=Db.batchSave(addCardTransactionsList,addCardTransactionsList.size());
                    for (int num : nums) {
                        if(num<=0){
                            return false;
                        }
                    }
                    nums=Db.batchUpdate(updateTouristSettleDetailList,updateTouristSettleDetailList.size());
                    for (int num : nums) {
                        if(num<=0){
                            return false;
                        }
                    }
                    nums=Db.batchUpdate(updateExpenseRecordDetailList,updateExpenseRecordDetailList.size());
                    for (int num : nums) {
                        if(num<=0){
                            return false;
                        }
                    }
                    nums=Db.batchUpdate(updateExpenseRecordList,updateExpenseRecordList.size());
                    for (int num : nums) {
                        if(num<=0){
                            return false;
                        }
                    }
                    //批量生成消费明细记录
                    if(transactionsDetailList!=null && transactionsDetailList.size()>0){
                        Db.batchSave(transactionsDetailList,transactionsDetailList.size());
                    }
                    return true;
                }catch (Exception e){
                    e.printStackTrace();
                }
                return false;
            }
        });

        if(flag){

            for (String cardId : cardDeductMap.keySet()) {
                Record record=cardDeductMap.get(cardId);
                FinaMembershipCard card=finaMembershipCardService.findById(cardId);
                Set<String> names=record.get("nameSet");

                MainCardDeductScheme cardDeductScheme=cardDeductSchemeMapMap.get(card.getId());
                MainMembershipCardType cardType=cardTypeHashMap.get(card.getId());

                Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(card.getCardNumber());

                if(record.getDouble("actualTimes")>0 || record.getDouble("actualAmount")>0 || record.getDouble("actualIntegrals")>0){
                    String usableStr="";
                    if(DeductType.deductTimes.getKey().equals(cardDeductScheme.getDeductWay())){
                        if("1".equals(cardType.getIsIntegral())){

                            double usableTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lock.get("lockConsumeTimes"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                            double timesLeftRemainder = usableTimes % 1;
                            String usableTimesStr="";
                            if(timesLeftRemainder==0){
                                usableTimesStr=((int)usableTimes)+"";
                            }else{
                                usableTimesStr=usableTimes+"";
                            }

                            double usableIntegrals=BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(lock.get("lockIntegrals"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                            double integralsLeftRemainder = usableIntegrals % 1;
                            String usableIntegralsStr="";
                            if(integralsLeftRemainder==0){
                                usableIntegralsStr=((int)usableIntegrals)+"";
                            }else{
                                usableIntegralsStr=usableIntegrals+"";
                            }
                            usableStr=usableTimesStr+"天，"+usableIntegralsStr+"积分。";
                        }else{

                            double usableTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lock.get("lockConsumeTimes"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                            double timesLeftRemainder = usableTimes % 1;
                            String usableTimesStr="";
                            if(timesLeftRemainder==0){
                                usableTimesStr=((int)usableTimes)+"";
                            }else{
                                usableTimesStr=usableTimes+"";
                            }

                            usableStr=usableTimesStr+"天。";
                        }
                    }else if(DeductType.deductAmount.getKey().equals(cardDeductScheme.getDeductWay())){
                        double usableAmount=BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(lock.get("lockBalance"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double amountLeftRemainder = usableAmount % 1;
                        String usableAmountStr="";
                        if(amountLeftRemainder==0){
                            usableAmountStr=((int)usableAmount)+"";
                        }else{
                            usableAmountStr=usableAmount+"";
                        }
                        usableStr=usableAmountStr+"元。";
                    }

                    double useTimes=record.getDouble("actualTimes");
                    double useIntegrals=record.getDouble("actualIntegrals");
                    double useAmount=record.getDouble("actualAmount");

                    String useStr="";
                    if(useTimes>0){
                        double leftRemainder = useTimes % 1;
                        if(leftRemainder==0){
                            useStr+=((int)useTimes)+"天";
                        }else{
                            useStr+=useTimes+"天";
                        }
                        //useStr+=useTimes+"天";
                    }
                    if(useIntegrals>0){
                        double leftRemainder = useIntegrals % 1;
                        if(leftRemainder==0){
                            useStr+=((int)useIntegrals)+"积分";
                        }else{
                            useStr+=useIntegrals+"积分";
                        }
                        //useStr+=useIntegrals+"积分";
                    }
                    if(useAmount>0){
                        double leftRemainder = useAmount % 1;
                        if(leftRemainder==0){
                            useStr+=((int)useAmount)+"元";
                        }else{
                            useStr+=useAmount+"元";
                        }
                        //useStr+=useAmount+"元";
                    }
                    String nameStr="";
                    int nameIndex=0;
                    for (String name : names) {
                        nameStr+=name+"，";
                        if(nameIndex>=2){
                            break;
                        }
                        nameIndex++;
                    }
                    String nameNumStr="";
                    if(nameStr.length()>0){
                        nameStr=nameStr.substring(0,nameStr.length()-1);
                    }
                    if(names.size()>1){
                        nameNumStr="等"+names.size()+"人";
                    }
                    MmsMember member = mmsMemberService.findById(card.getMemberId());

                    boolean isAddLockInfo=false;
                    double lockTimes=lock.get("lockConsumeTimes");
                    double lockAmount=lock.get("lockBalance");
                    double lockIntegrals=lock.get("lockIntegrals");
                    String lockStr="，已订房预扣除";
                    if(lockTimes>0 || lockAmount>0 || lockIntegrals>0){
                        isAddLockInfo=true;
                        if(lockAmount>0){
                            double lockAmountLeftRemainder = lockAmount % 1;
                            if(lockAmountLeftRemainder==0){
                                lockStr+="金额："+((int)lockAmount)+"元";
                            }else{
                                lockStr+="金额："+lockAmount+"元";
                            }

                        }else{
                            //lockStr+="天数：";
                            if(lockTimes>0 && lockIntegrals>0){
                                double lockTimesLeftRemainder = lockTimes % 1;
                                if(lockTimesLeftRemainder==0){
                                    lockStr+="天数："+((int)lockTimes)+"天";
                                }else{
                                    lockStr+="天数："+lockTimes+"天";
                                }

                                double lockIntegralsLeftRemainder = lockIntegrals % 1;
                                if(lockIntegralsLeftRemainder==0){
                                    lockStr+="，"+((int)lockIntegrals)+"积分";
                                }else{
                                    lockStr+="，"+lockIntegrals+"积分";
                                }
                            }else if(lockTimes>0){
                                double lockTimesLeftRemainder = lockTimes % 1;
                                if(lockTimesLeftRemainder==0){
                                    lockStr+="天数："+((int)lockTimes)+"天";
                                }else{
                                    lockStr+="天数："+lockTimes+"天";
                                }
                            }else if(lockIntegrals>0){
                                double lockIntegralsLeftRemainder = lockIntegrals % 1;
                                if(lockIntegralsLeftRemainder==0){
                                    lockStr+="天数："+((int)lockIntegrals)+"积分";
                                }else{
                                    lockStr+="天数："+lockIntegrals+"积分";
                                }
                            }
                        }
                    }
                    if(!isAddLockInfo){
                        lockStr="";
                    }

                    //【昌松集团】张三四卡号666666消费账单:于[2023-10-10至2023-10-11]由[张三四，李四等4人]参加森林湖2天1晚（城市广场）团消费1天和1积分，剩余可用1460天，152积分。如您对账单有异议请于三日内提出，逾期视为无异议。
                    String content=member.getFullName()+"卡号"+card.getCardNumber()+"消费账单："+""+"于["+
                            DateUtils.formatDate(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+
                            "]由["+nameStr+nameNumStr+"]参加"+expenseRecordTourist.getName()+"消费"+useStr+lockStr+"，剩余可用"+usableStr+"如您对账单有异议请于三日内提出，逾期视为无异议。";

                    JSONObject obj=new JSONObject();
                    obj.put("content",content);
                    smsSendRecordService.sendMessage(SendType.customContent,card.getCardNumber(), JSON.toJSONString(obj),null);
                    /*String mameStrs="";
                    for (String name : names) {
                        mameStrs+=name+"，";
                    }
                    mameStrs=mameStrs.substring(0,mameStrs.length()-1);

                    FinaCardDeductionSmsRecord deductionSmsRecord=new FinaCardDeductionSmsRecord();
                    deductionSmsRecord.setId(IdGen.getUUID());
                    deductionSmsRecord.setDelFlag("0");
                    deductionSmsRecord.setType("3");
                    deductionSmsRecord.setDeductTime(new Date());
                    deductionSmsRecord.setStartTime(expenseRecordTourist.getStartDate());
                    deductionSmsRecord.setEndTime(expenseRecordTourist.getEndDate());
                    deductionSmsRecord.setCheckinNames(mameStrs);
                    deductionSmsRecord.setTouristName(expenseRecordTourist.getName());
                    deductionSmsRecord.setCardNumber(card.getCardNumber());
                    deductionSmsRecord.setCardId(card.getId());
                    deductionSmsRecord.setTelephone(card.getTelephone());
                    deductionSmsRecord.setAmount(0.0);
                    deductionSmsRecord.setTimes(0.0);
                    deductionSmsRecord.setIntegrals(0.0);
                    if(useAmount>0){
                        deductionSmsRecord.setAmount(useAmount);
                    }
                    if(useTimes>0){
                        deductionSmsRecord.setTimes(useTimes);
                    }
                    if(useIntegrals>0){
                        deductionSmsRecord.setIntegrals(useIntegrals);
                    }
                    deductionSmsRecord.setStatus("0");
                    deductionSmsRecord.setCreateDate(new Date());
                    deductionSmsRecord.setUpdateDate(new Date());
                    deductionSmsRecord.save();*/
                }

            }

            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }


    }

    public void singleTouristSettle(){
        String id=getPara("id");
        String actualTimesStr=getPara("actualTimes");
        String actualAmountStr=getPara("actualAmount");
        String actualIntegralsStr=getPara("actualIntegrals");
        String financeRemark=getPara("financeRemark");
        String touristNo=getPara("touristNo");
        final String userId = AuthUtils.getUserId();

        FinaExpenseRecordTouristSettleDetail touristSettleDetail = finaExpenseRecordTouristSettleDetailService.findById(id);
        if(touristSettleDetail==null){
            renderJson(Ret.fail("msg","未找到该记录"));
            return;
        }
        if("1".equals(touristSettleDetail.getDelFlag())){
            renderJson(Ret.fail("msg","提交数据存在已作废记录，请刷新再重试"));
            return;
        }


        Double actualTimes=Double.valueOf(actualTimesStr);
        Double actualAmount=Double.valueOf(actualAmountStr);
        Double actualIntegrals=Double.valueOf(actualIntegralsStr);
        if(actualTimes==null){
            actualTimes=0.0;
        }
        if(actualAmount==null){
            actualAmount=0.0;
        }
        if(actualIntegrals==null){
            actualIntegrals=0.0;
        }
        touristSettleDetail.setActualTimes(actualTimes);
        touristSettleDetail.setActualAmount(actualAmount);
        touristSettleDetail.setActualIntegrals(actualIntegrals);
        touristSettleDetail.setIsSettle("1");
        touristSettleDetail.setUpdateTime(new Date());
        touristSettleDetail.setFinanceRemark(financeRemark);

        FinaExpenseRecordTourist expenseRecordTourist=finaExpenseRecordTouristService.getTouristByNo(touristNo);
        FinaMembershipCard card=finaMembershipCardService.findById(touristSettleDetail.getCardId());
        if("1".equals(card.getIsLock())){
            renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡已锁定"));
            return;
        }

        if(card.getConsumeTimes()==null){
            card.setConsumeTimes(0.0);
        }
        if(card.getBuyRechargeDays() == null){
            card.setBuyRechargeDays(0.00);
        }
        if(card.getGiveRechargeDays() == null){
            card.setGiveRechargeDays(0.00);
        }
        if(card.getBalance()==null){
            card.setBalance(0.0);
        }
        if(card.getBuyRechargeAmount() == null){
            card.setBuyRechargeAmount(0.00);
        }
        if(card.getGiveRechargeAmount() == null){
            card.setGiveRechargeAmount(0.00);
        }
        if(card.getCardIntegrals()==null){
            card.setCardIntegrals(0.0);
        }
        if(card.getBuyRechargeIntegrals() == null){
            card.setBuyRechargeIntegrals(0.00);
        }
        if(card.getGiveRechargeIntegrals() == null){
            card.setGiveRechargeIntegrals(0.00);
        }

        double buyAmount = 0.0;
        double giveAmount = 0.0;
        double buyDays = 0.0;
        double giveDays = 0.0;
        double giveIntegrals = 0.0;

        FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(touristSettleDetail.getExpenseId());
        boolean isUpdateExpenseRecord=false;
        if(Db.findFirst("select * from fina_expense_record_tourist_settle_detail where del_flag='0' and is_settle='0' and expense_id=? and id<>? "
                ,touristSettleDetail.getExpenseId(),touristSettleDetail.getId())==null){
            isUpdateExpenseRecord=true;

            expenseRecord.setSettleStatus("1");
            expenseRecord.setUpdateTime(new Date());
        }

        FinaExpenseRecordDetail expenseRecordDetail=finaExpenseRecordDetailService.findFirstDetailsByExpenseId(touristSettleDetail.getId());
        expenseRecordDetail.setIsSettled("1");
        expenseRecordDetail.setUpdateTime(new Date());

        Map<String,Double> cardLockMap=finaMembershipCardService.getCardLockInfo(card.getCardNumber());

        if(card.getConsumeTimes()<(actualTimes+cardLockMap.get("lockConsumeTimes")-expenseRecordDetail.getTimes())){
            renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡可用天数不足"));
            return;
        }
        if(card.getBalance()<(actualAmount+cardLockMap.get("lockBalance")-expenseRecordDetail.getAmount())){
            renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡可用余额不足"));
            return;
        }
        if(card.getCardIntegrals()<(actualIntegrals+cardLockMap.get("lockIntegrals")-expenseRecordDetail.getIntegrals())){
            renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡可用积分不足"));
            return;
        }

        MainCardDeductScheme cardDeductScheme = mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if(cardDeductScheme==null){
            cardDeductScheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        }

        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());

        String describe="";
        if(DeductType.deductTimes.getKey().equals(cardDeductScheme.getDeductWay())){
            if("1".equals(cardType.getIsIntegral())){
                describe="["+expenseRecord.getName()+"]使用["+card.getCardNumber()+"]会员卡参与["+expenseRecordTourist.getName()+"]旅游团从" +
                        "["+ TimeUtils.getStrDateByPattern(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+
                        TimeUtils.getStrDateByPattern(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+"]时间段扣卡["+actualTimes+"]天,["+actualIntegrals+"]积分";
            }else{
                describe="["+expenseRecord.getName()+"]使用["+card.getCardNumber()+"]会员卡参与["+expenseRecordTourist.getName()+"]旅游团从" +
                        "["+ TimeUtils.getStrDateByPattern(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+
                        TimeUtils.getStrDateByPattern(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+"]时间段扣卡["+actualTimes+"]天";
            }
        }else if(DeductType.deductAmount.getKey().equals(cardDeductScheme.getDeductWay())){
            describe="["+expenseRecord.getName()+"]使用["+card.getCardNumber()+"]会员卡参与["+expenseRecordTourist.getName()+"]旅游团从" +
                    "["+ TimeUtils.getStrDateByPattern(expenseRecordTourist.getStartDate(),"yyyy-MM-dd")+"至"+
                    TimeUtils.getStrDateByPattern(expenseRecordTourist.getEndDate(),"yyyy-MM-dd")+"]时间段扣卡["+actualAmount+"]元";

        }

        card.setConsumeTimes(card.getConsumeTimes()-actualTimes);
        //计算消费明细的购买、赠送天数分别是多少
        if(card.getGiveRechargeDays() >= actualTimes){
            giveDays = actualTimes;
            if(card.getGiveRechargeDays() > 0){
                card.setGiveRechargeDays(card.getGiveRechargeDays()-actualTimes);
            }
        }else{
            giveDays = card.getGiveRechargeDays();
            buyDays = actualTimes-card.getGiveRechargeDays();
            //分别扣减购买天数、赠送天数
            card.setGiveRechargeDays(0.0);
            if(card.getBuyRechargeDays() > 0){
                card.setBuyRechargeDays(card.getBuyRechargeDays()-buyDays);
            }
        }
        card.setBalance(card.getBalance()-actualAmount);
        //计算消费明细的购买、赠送金额分别是多少
        if(card.getGiveRechargeAmount() >= actualAmount){
            giveAmount = actualAmount;
            if(card.getGiveRechargeAmount() > 0){
                card.setGiveRechargeAmount(card.getGiveRechargeAmount()-actualAmount);
            }
        }else{
            giveAmount = card.getGiveRechargeAmount();
            buyAmount = actualAmount-card.getGiveRechargeAmount();
            //分别扣减购买金额、赠送金额
            card.setGiveRechargeAmount(0.0);
            if(card.getBuyRechargeAmount() > 0){
                card.setBuyRechargeAmount(card.getBuyRechargeAmount()-buyAmount);
            }
        }
        card.setCardIntegrals(BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(actualIntegrals)).doubleValue());
        //计算消费明细赠送积分多少
        giveIntegrals = actualIntegrals;
        if(card.getGiveRechargeIntegrals() > 0){
            card.setGiveRechargeIntegrals(BigDecimal.valueOf(card.getGiveRechargeIntegrals()).subtract(BigDecimal.valueOf(actualIntegrals)).doubleValue());
        }
        card.setUpdateTime(new Date());

        Double totalBalanceRollValue=Db.queryDouble("select SUM(balance_roll_value) totalBalanceRollValue  from fina_card_roll a " +
                " INNER JOIN crm_card_roll_record b on a.roll_id=b.id " +
                " INNER JOIN crm_card_roll c on c.id=b.card_roll_id  " +
                " where b.del_flag='0' and b.is_enable='0' and a.card_id=? and c.roll_type_id='86F75466-2CD4-455C-9A31-0CFF0846BD7B' ",card.getId());
        if(totalBalanceRollValue==null){
            totalBalanceRollValue=0.0;
        }

        FinaCardTransactions transactions=new FinaCardTransactions();
        transactions.setId(IdGen.getUUID());
        transactions.setCardId(touristSettleDetail.getCardId());
        transactions.setExpenseId(touristSettleDetail.getExpenseId());
        transactions.setConsumeType(ConsumeType.TOURIST);
        transactions.setType(AccountRecordOperateType.dailyDeduction.getKey());
        transactions.setInOutFlag("2");
        transactions.setStartDate(expenseRecordTourist.getStartDate());
        transactions.setEndDate(expenseRecordTourist.getEndDate());
        transactions.setAmount(actualAmount);
        transactions.setTimes(actualTimes);
        transactions.setIntegrals(actualIntegrals);
        transactions.setPoints(0.0);
        transactions.setBeanCoupons(0.0);
        transactions.setBusinessEntityId(expenseRecord.getBusinessEntityId());
        transactions.setTimesSnapshot(card.getConsumeTimes());
        transactions.setAmountSnapshot(card.getBalance());
        transactions.setIntegralsSnapshot(card.getCardIntegrals());
        transactions.setPointsSnapshot(0.0);
        transactions.setBeanCouponsSnapshot(totalBalanceRollValue);
        transactions.setPoints(0.0);
        transactions.setIsHidden("0");
        transactions.setDescribe(describe);
        transactions.setDealTime(new Date());

        //生成消费购买、赠送明细列表记录
        List<FinaCardTransactionsDetail> transactionsDetailList = new ArrayList<>();
        if(giveDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(transactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(giveDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(transactions.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(buyDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(transactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(giveAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(transactions.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(buyAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveIntegrals > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(transactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("integrals");
            transactionsDetail.setDeductValue(giveIntegrals);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }

        boolean finalIsUpdateExpenseRecord=isUpdateExpenseRecord;
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    if(!touristSettleDetail.update()){
                        return false;
                    }
                    if(!expenseRecordDetail.update()){
                        return false;
                    }

                    if(finalIsUpdateExpenseRecord && !expenseRecord.update()){
                        return false;
                    }
                    if(!transactions.save()){
                        return false;
                    }
                    if(!card.update()){
                        return false;
                    }
                    //批量生成消费明细记录
                    if(transactionsDetailList!=null && transactionsDetailList.size()>0){
                        Db.batchSave(transactionsDetailList,transactionsDetailList.size());
                    }

                    return true;
                }catch (Exception e){
                    e.printStackTrace();
                }
                return false;
            }
        });

        if(flag){

            Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(card.getCardNumber());
            //发送短信
            if(transactions.getTimes()>0 || transactions.getAmount()>0 || transactions.getIntegrals()>0){
                String usableStr="";
                if(DeductType.deductTimes.getKey().equals(cardDeductScheme.getDeductWay())){
                    if("1".equals(cardType.getIsIntegral())){

                        double usableTimes=BigDecimal.valueOf(transactions.getTimesSnapshot()).subtract(BigDecimal.valueOf(lock.get("lockConsumeTimes"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double timesLeftRemainder = usableTimes % 1;
                        String usableTimesStr="";
                        if(timesLeftRemainder==0){
                            usableTimesStr=((int)usableTimes)+"";
                        }else{
                            usableTimesStr=usableTimes+"";
                        }

                        double usableIntegrals=BigDecimal.valueOf(transactions.getIntegralsSnapshot()).subtract(BigDecimal.valueOf(lock.get("lockIntegrals"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double integralsLeftRemainder = usableIntegrals % 1;
                        String usableIntegralsStr="";
                        if(integralsLeftRemainder==0){
                            usableIntegralsStr=((int)usableIntegrals)+"";
                        }else{
                            usableIntegralsStr=usableIntegrals+"";
                        }

                        usableStr=usableTimesStr+"天，"+usableIntegralsStr+"积分。";
                    }else{
                        double usableTimes=BigDecimal.valueOf(transactions.getTimesSnapshot()).subtract(BigDecimal.valueOf(lock.get("lockConsumeTimes"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                        double timesLeftRemainder = usableTimes % 1;
                        String usableTimesStr="";
                        if(timesLeftRemainder==0){
                            usableTimesStr=((int)usableTimes)+"";
                        }else{
                            usableTimesStr=usableTimes+"";
                        }
                        usableStr=usableTimesStr+"天。";
                    }
                }else if(DeductType.deductAmount.getKey().equals(cardDeductScheme.getDeductWay())){
                    double usableAmount=BigDecimal.valueOf(transactions.getAmountSnapshot()).subtract(BigDecimal.valueOf(lock.get("lockBalance"))).setScale(1,BigDecimal.ROUND_DOWN).doubleValue();
                    double amountLeftRemainder = usableAmount % 1;
                    String usableAmountStr="";
                    if(amountLeftRemainder==0){
                        usableAmountStr=((int)usableAmount)+"";
                    }else{
                        usableAmountStr=usableAmount+"";
                    }
                    usableStr=usableAmountStr+"元。";
                }
                double useTimes=transactions.getTimes();
                double useIntegrals=transactions.getIntegrals();
                double useAmount=transactions.getAmount();

                String useStr="";
                if(useTimes>0){
                    double leftRemainder = useTimes % 1;
                    if(leftRemainder==0){
                        useStr+=((int)useTimes)+"天";
                    }else{
                        useStr+=useTimes+"天";
                    }
                    //useStr+=useTimes+"天";
                }
                if(useIntegrals>0){
                    double leftRemainder = useIntegrals % 1;
                    if(leftRemainder==0){
                        useStr+=((int)useIntegrals)+"积分";
                    }else{
                        useStr+=useIntegrals+"积分";
                    }
                    //useStr+=useIntegrals+"积分";
                }
                if(useAmount>0){
                    double leftRemainder = useAmount % 1;
                    if(leftRemainder==0){
                        useStr+=((int)useAmount)+"元";
                    }else{
                        useStr+=useAmount+"元";
                    }
                    //useStr+=useAmount+"元";
                }
                MmsMember member = mmsMemberService.findById(card.getMemberId());
                boolean isAddLockInfo=false;
                double lockTimes=lock.get("lockConsumeTimes");
                double lockAmount=lock.get("lockBalance");
                double lockIntegrals=lock.get("lockIntegrals");
                String lockStr="，已订房预扣除";
                if(lockTimes>0 || lockAmount>0 || lockIntegrals>0){
                    isAddLockInfo=true;
                    if(lockAmount>0){
                        double lockAmountLeftRemainder = lockAmount % 1;
                        if(lockAmountLeftRemainder==0){
                            lockStr+="金额："+((int)lockAmount)+"元";
                        }else{
                            lockStr+="金额："+lockAmount+"元";
                        }

                    }else{
                        //lockStr+="天数：";
                        if(lockTimes>0 && lockIntegrals>0){
                            double lockTimesLeftRemainder = lockTimes % 1;
                            if(lockTimesLeftRemainder==0){
                                lockStr+="天数："+((int)lockTimes)+"天";
                            }else{
                                lockStr+="天数："+lockTimes+"天";
                            }

                            double lockIntegralsLeftRemainder = lockIntegrals % 1;
                            if(lockIntegralsLeftRemainder==0){
                                lockStr+="，"+((int)lockIntegrals)+"积分";
                            }else{
                                lockStr+="，"+lockIntegrals+"积分";
                            }
                        }else if(lockTimes>0){
                            double lockTimesLeftRemainder = lockTimes % 1;
                            if(lockTimesLeftRemainder==0){
                                lockStr+="天数："+((int)lockTimes)+"天";
                            }else{
                                lockStr+="天数："+lockTimes+"天";
                            }
                        }else if(lockIntegrals>0){
                            double lockIntegralsLeftRemainder = lockIntegrals % 1;
                            if(lockIntegralsLeftRemainder==0){
                                lockStr+="天数："+((int)lockIntegrals)+"积分";
                            }else{
                                lockStr+="天数："+lockIntegrals+"积分";
                            }
                        }
                    }
                }
                if(!isAddLockInfo){
                    lockStr="";
                }
                //【昌松集团】张三四卡号666666消费账单:于[2023-10-10至2023-10-11]由[张三四，李四等4人]参加森林湖2天1晚（城市广场）团消费1天和1积分，剩余可用1460天，152积分。如您对账单有异议请于三日内提出，逾期视为无异议。
                String content=member.getFullName()+"卡号"+card.getCardNumber()+"消费账单："+""+"于["+
                        DateUtils.formatDate(transactions.getStartDate(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(transactions.getEndDate(),"yyyy-MM-dd")+
                        "]由"+expenseRecord.getName()+"参加"+expenseRecordTourist.getName()+"消费"+useStr+lockStr+"，剩余可用"+usableStr+"如您对账单有异议请于三日内提出，逾期视为无异议。";

                JSONObject obj=new JSONObject();
                obj.put("content",content);
                smsSendRecordService.sendMessage(SendType.customContent,card.getCardNumber(), JSON.toJSONString(obj),null);
                /*FinaCardDeductionSmsRecord deductionSmsRecord=new FinaCardDeductionSmsRecord();
                deductionSmsRecord.setId(IdGen.getUUID());
                deductionSmsRecord.setDelFlag("0");
                deductionSmsRecord.setType("3");
                deductionSmsRecord.setDeductTime(new Date());
                deductionSmsRecord.setStartTime(transactions.getStartDate());
                deductionSmsRecord.setEndTime(transactions.getEndDate());
                deductionSmsRecord.setCheckinNames(expenseRecord.getName());
                deductionSmsRecord.setCardNumber(card.getCardNumber());
                deductionSmsRecord.setCardId(card.getId());
                deductionSmsRecord.setTouristName(expenseRecordTourist.getName());
                deductionSmsRecord.setTouristName(expenseRecordTourist.getName());
                deductionSmsRecord.setTelephone(card.getTelephone());
                deductionSmsRecord.setAmount(0.0);
                deductionSmsRecord.setTimes(0.0);
                deductionSmsRecord.setIntegrals(0.0);
                if(useAmount>0){
                    deductionSmsRecord.setAmount(useAmount);
                }
                if(useTimes>0){
                    deductionSmsRecord.setTimes(useTimes);
                }
                if(useIntegrals>0){
                    deductionSmsRecord.setIntegrals(useIntegrals);
                }
                deductionSmsRecord.setStatus("0");
                deductionSmsRecord.setCreateDate(new Date());
                deductionSmsRecord.setUpdateDate(new Date());
                deductionSmsRecord.save();*/
            }
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

}
