package com.cszn.finance.web.support.give;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainCardGiveRecordService;
import com.cszn.integrated.service.api.main.MainCardGiveSchemeRuleService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainCardGiveRecord;
import com.cszn.integrated.service.entity.main.MainCardGiveSchemeRule;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.aop.annotation.Bean;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;

/**
 * 会员卡生成赠送费用
 */
@Bean
public class GiveRuleGenerate {
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private MainCardGiveSchemeRuleService mainCardGiveSchemeRuleService;
    @Inject
    private MainCardGiveRecordService mainCardGiveRecordService;
    @Inject
    private MmsMemberService mmsMemberService;
    @Inject
    private SmsSendRecordService smsSendRecordService;

    /**
     * 生成周期为日类型的
     */
    public void dayGive(){
        try {
            dayGive(1,100,new Date(),"day");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 生成周期为日类型的
     * @param pageNumber
     * @param pageSize
     * @param date 计算天数时间，也查询比该时间小的会员卡
     * @param cycle 类型
     */
    private void dayGive(Integer pageNumber, Integer pageSize, Date date, String cycle){
        Page<FinaMembershipCard> cardPage=finaMembershipCardService.findDayGiveCardPage(pageNumber,pageSize,date,cycle);
        List<FinaMembershipCard> cardList=cardPage.getList();
        List<MainCardGiveRecord> giveRecordList=new ArrayList<>();
        for(FinaMembershipCard card:cardList){
            List<MainCardGiveSchemeRule> giveSchemeRuleList=mainCardGiveSchemeRuleService.findGiveSchemeRuleList(card.getGiveSchemeId(),cycle);
            if(giveSchemeRuleList.size()==0){
                continue;
            }
            int seq=getDaySeq(card.getOpenTime(),date);
            if(seq<=0){
                continue;
            }
            saveCardBalance(card,giveSchemeRuleList,giveRecordList,cycle,seq);
        }
        if(giveRecordList.size()>0){
            save(cardList,giveRecordList);
        }
        pageNumber++;
        if(pageNumber<=cardPage.getTotalPage()){
            dayGive(pageNumber,pageSize,date,cycle);
        }
    }

    /**
     * 生成为月类型的赠送
     */
    public void monthGive(){
        Date now = new Date();
        Date yesterdate=DateUtils.getNextDay(now,-1);
        Date lastMonth=DateUtils.getNextMonth(yesterdate,-1);
        List<String> genDays=new ArrayList<>();
        int yesterday=Integer.parseInt(DateUtils.getDay(yesterdate));
        genDays.add(Integer.valueOf(DateUtils.getDay(yesterdate)).toString());
        //如果今天是1号，昨天小于31号的话，需要计算昨天号到31的号数
        if(Integer.valueOf(DateUtils.getDay(now))==1){
            while (Integer.valueOf(yesterday)<31){
                ++yesterday;
                genDays.add(yesterday+"");
            }
        }
        try {
            monthGive(1,100,genDays,lastMonth,yesterdate,"mon");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 生成为月类型的赠送
     * @param pageNumber
     * @param pageSize
     * @param days 需要生成的号数
     * @param lastMonth 上个月的今天，用于过滤会员卡
     * @param yesterdate 昨天的时间用于计算序
     * @param cycle
     */
    private void monthGive(Integer pageNumber,Integer pageSize,List<String> days,Date lastMonth,Date yesterdate,String cycle){
        Page<FinaMembershipCard> cardPage= finaMembershipCardService.findMonthGiveCardPage(pageNumber,pageSize,days,lastMonth,cycle);
        List<FinaMembershipCard> cardList=cardPage.getList();
        List<MainCardGiveRecord> giveRecordList=new ArrayList<>();
        for(FinaMembershipCard card:cardList){
            List<MainCardGiveSchemeRule> giveSchemeRuleList=mainCardGiveSchemeRuleService.findGiveSchemeRuleList(card.getGiveSchemeId(),cycle);
            if(giveSchemeRuleList==null || giveSchemeRuleList.size()==0){
                continue;
            }
            int seq=getMonthSeq(yesterdate,card.getOpenTime());
            if(seq<=0){
                continue;
            }
            saveCardBalance(card,giveSchemeRuleList,giveRecordList,cycle,seq);
        }
        if(giveRecordList.size()>0){
            save(cardList,giveRecordList);
        }
        pageNumber++;
        if(pageNumber<=cardPage.getTotalPage()){
            monthGive(pageNumber,pageSize,days,lastMonth,yesterdate,cycle);
        }
    }

    /**
     * 生成季类型的赠送
     */
    public void seasonGive(){
        //今天的时间
        Date today= new Date();
        //昨天
        Date yesterday=DateUtils.getNextDay(today,-1);
        List<String> list=new ArrayList<>();
        list.add(DateUtils.formatDate(yesterday,"MM-dd"));
        list.add(DateUtils.formatDate(DateUtils.getNextMonth(yesterday,-3),"MM-dd"));
        list.add(DateUtils.formatDate(DateUtils.getNextMonth(yesterday,-6),"MM-dd"));
        list.add(DateUtils.formatDate(DateUtils.getNextMonth(yesterday,-9),"MM-dd"));
        System.out.println(list);
        //昨天的日期
        Integer yDay=Integer.parseInt(DateUtils.formatDate(yesterday,"dd"));
        Iterator<String> iterator=list.iterator();
        //这里删除一下已经生成的日期，例如:2019-05-31执行，需要计算的4个日期为[11-30,05-30,02-28,08-30]，而02-28在2019-05-29的时候已经执行过了，所以需要移除小的日期
        while (iterator.hasNext()){
            String item=iterator.next();
            if(yDay>Integer.valueOf(item.substring(3,item.length()))){
                iterator.remove();
            }
        }
        //今天的日期
        Integer tDay=Integer.parseInt(DateUtils.formatDate(today,"dd"));
        //如果定时执行日为1号需要处理下
        if(tDay==1){
            //例如执行日为2019-03-01，最先计算出的4个日期为[05-28,08-28,02-28,11-28]，而5月的最后一天不是28号，需要把5月29，30，31号也加进来
            List<String> newList=new ArrayList<>();
            for(String s:list){
                Integer month=Integer.valueOf(s.substring(0,2));
                Integer day=Integer.valueOf(s.substring(3,s.length()));
                while (!isMaxDay(month,day)){
                    newList.add(s.substring(0,2)+"-"+(++day));
                }
            }
            list.addAll(newList);
        }
        //如果不是闰年则需要在3月1号、5月30、8月30、11月30号添加2月29进去
        String monthDay=DateUtils.formatDate(today,"MM-dd");
        if(!isLeapYear(Integer.valueOf(DateUtils.formatDate(today,"yyyy"))) && (monthDay.equals("03-01") || monthDay.equals("05-30") || monthDay.equals("08-30") ||monthDay.equals("11-30")) ){
            list.add("02-29");
        }
        System.out.println(list);
        try {
            seasonGive(1,100,list,yesterday,DateUtils.getNextMonth(today,-3),"season");
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 生成季类型的赠送
     * @param pageNumber
     * @param pageSize
     * @param monthDaySet 需要生成的日月 MM-dd
     * @param yesterday 昨天的日期用来计算序号
     * @param filterDate 用来过滤查询出来的卡
     * @param cycle
     */
    private void seasonGive(Integer pageNumber,Integer pageSize,List<String> monthDaySet,Date yesterday,Date filterDate,String cycle){
        Page<FinaMembershipCard> cardPage=finaMembershipCardService.findSeasonGiveCardPage(pageNumber,pageSize,monthDaySet,filterDate,cycle);
        List<FinaMembershipCard> cardList=cardPage.getList();
        List<MainCardGiveRecord> giveRecordList=new ArrayList<>();
        for(FinaMembershipCard card:cardList){
            List<MainCardGiveSchemeRule> giveSchemeRuleList=mainCardGiveSchemeRuleService.findGiveSchemeRuleList(card.getGiveSchemeId(),cycle);
            if(giveSchemeRuleList.size()==0){
                continue;
            }
            int seq=getSeasonSeq(yesterday,card.getOpenTime());
            if(seq<=0){
                continue;
            }
            saveCardBalance(card,giveSchemeRuleList,giveRecordList,cycle,seq);
        }
        if(giveRecordList.size()>0){
            save(cardList,giveRecordList);
        }
        pageNumber++;
        if(pageNumber<=cardPage.getTotalPage()){
            monthGive(pageNumber,pageSize,monthDaySet,yesterday,filterDate,cycle);
        }
    }

    /**
     * 生成年类型的赠送
     */
    public void yearGive(){
        Date now=new Date();
        Date lastYear=DateUtils.getNextYear(now,-1);
        String lastMonthDay=DateUtils.formatDate(DateUtils.getNextDay(now,-1),"MM-dd");
        String nowMonthDay=DateUtils.formatDate(now,"MM-dd");
        List<String> monthDayList=new ArrayList<>();
        monthDayList.add(lastMonthDay);
        //如果今天是3月1号，需要判断一下昨天是2月28还是29，如果是28号则需要将29的一起生成
        if(nowMonthDay.equals("03-01") && DateUtils.formatDate(DateUtils.getNextDay(now,-1),"MM-dd").equals("02-28")){
            monthDayList.add("02-29");
        }

        try {
            yearGive(1,100,monthDayList,now,lastYear,"year");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 生成年类型的赠送
     * @param pageNumber
     * @param pageSize
     * @param monthDayList 如要计算的月日MM-DD
     * @param now 计算序
     * @param lastYear 上一年的今天过滤查询出的卡
     * @param cycle
     */
    private void yearGive(Integer pageNumber,Integer pageSize,List<String> monthDayList,Date now,Date lastYear,String cycle){
        Page<FinaMembershipCard> cardPage=finaMembershipCardService.findYearGiveCardPage(pageNumber,pageSize,monthDayList,lastYear,cycle);
        List<FinaMembershipCard> cardList=cardPage.getList();
        List<MainCardGiveRecord> giveRecordList=new ArrayList<>();
        for(FinaMembershipCard card:cardList){
            List<MainCardGiveSchemeRule> giveSchemeRuleList=mainCardGiveSchemeRuleService.findGiveSchemeRuleList(card.getGiveSchemeId(),cycle);
            if(giveSchemeRuleList.size()==0){
                continue;
            }
            int seq=getYearSeq(new Date(),card.getOpenTime());
            if(seq<=0){
                continue;
            }
            saveCardBalance(card,giveSchemeRuleList,giveRecordList,cycle,seq);
        }
        if(giveRecordList.size()>0){
            save(cardList,giveRecordList);
        }
        pageNumber++;
        if(pageNumber<=cardPage.getTotalPage()){
            yearGive(pageNumber,pageSize,monthDayList,now,lastYear,cycle);
        }
    }


    /**
     * 保存会员卡余额和赠送记录
     * @param cardList
     * @param giveRecordList
     */
    public void save(List<FinaMembershipCard> cardList,List<MainCardGiveRecord> giveRecordList){
        if(giveRecordList.size()>0){
            Db.tx(new IAtom() {
                @Override
                public boolean run() throws SQLException {
                    Db.batchUpdate(cardList,cardList.size());
                    Db.batchSave(giveRecordList,giveRecordList.size());
                    return true;
                }
            });
        }
    }

    /**
     * 赠送操作
     * @param card
     * @param giveSchemeRuleList
     * @param giveRecordList
     * @param cycle
     * @param seq
     */
    public void saveCardBalance(FinaMembershipCard card,List<MainCardGiveSchemeRule> giveSchemeRuleList,List<MainCardGiveRecord> giveRecordList,String cycle,Integer seq){
        Double giveBalance=card.getGiveBalance();
        Double giveConsumeTimes=card.getGiveConsumeTimes();

        MmsMember member=mmsMemberService.findMemberByCardNumber(card.getCardNumber());
        String fullName="会员";
        if(member!=null){
            fullName=member.getFullName();
        }
        for(MainCardGiveSchemeRule rule:giveSchemeRuleList){
            if(rule.getStartSeq()>seq || rule.getEndSeq()<seq){
                continue;
            }
            //判断该序是否生成过了
            if(mainCardGiveRecordService.findGiveRecord(card.getId(),rule.getId(),cycle,seq)!=null){
                continue;
            }
            MainCardGiveRecord giveRecord=new MainCardGiveRecord();
            giveRecord.setId(IdGen.getUUID());
            giveRecord.setCardId(card.getId());
            giveRecord.setSchemeId(rule.getSchemeId());
            giveRecord.setRuleId(rule.getId());
            giveRecord.setCycle(cycle);
            giveRecord.setGiveSeq(seq);
            giveRecord.setGiveAmount(rule.getGiveAmount());
            giveRecord.setGiveTimes(rule.getGiveTimes());
            giveRecord.setGiveDate(new Date());
            if(giveBalance==null){
                giveBalance=rule.getGiveAmount();
            }else{
                giveBalance=new BigDecimal(giveBalance).add(new BigDecimal(rule.getGiveAmount())).doubleValue();
            }
            if(giveConsumeTimes==null){
                giveConsumeTimes=rule.getGiveTimes();
            }else{
                giveConsumeTimes=new BigDecimal(giveConsumeTimes).add(new BigDecimal(rule.getGiveTimes())).doubleValue();
            }

            giveRecordList.add(giveRecord);
        }
        card.setGiveBalance(giveBalance);
        card.setGiveConsumeTimes(giveConsumeTimes);
    }

    /**
     * 计算日类型的序
     * @param bigDate
     * @param smallDate
     * @return
     */
    public int getDaySeq(Date bigDate,Date smallDate){
        return new Long(DateUtils.getTwoDateBetweenDays(bigDate,smallDate)).intValue();
    }

    /**
     * 计算月类型序
     * @param bigDate
     * @param smallDate
     * @return
     */
    public int getMonthSeq(Date bigDate,Date smallDate){
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(bigDate);
        c2.setTime(smallDate);
        if(c1.getTimeInMillis() < c2.getTimeInMillis()){
            return 0;
        }
        int year1 = c1.get(Calendar.YEAR);
        int year2 = c2.get(Calendar.YEAR);
        int month1 = c1.get(Calendar.MONTH);
        int month2 = c2.get(Calendar.MONTH);
        int day1 = c1.get(Calendar.DAY_OF_MONTH);
        int day2 = c2.get(Calendar.DAY_OF_MONTH);
        // 获取年的差值 假设 d1 = 2015-8-16 d2 = 2011-9-30
        int yearInterval = year1 - year2;
        // 如果 d1的 月-日 小于 d2的 月-日 那么 yearInterval-- 这样就得到了相差的年数
        if(month1 < month2 || month1 == month2 && day1 < day2){
            yearInterval --;
        }
        // 获取月数差值
        int monthInterval = (month1 + 12) - month2 ;
        /*if(day1 < day2){
            monthInterval --;
        }*/
        monthInterval %= 12;
        return yearInterval * 12 + monthInterval;
    }

    /**
     * 计算季类型序
     * @param bigDate
     * @param smallDate
     * @return
     */
    public int getSeasonSeq(Date bigDate,Date smallDate){
        return getMonthSeq(bigDate,smallDate)/3;
    }

    public int getYearSeq(Date bigDate,Date smallDate){
        Calendar big=Calendar.getInstance();
        big.setTime(bigDate);
        Calendar small=Calendar.getInstance();
        small.setTime(smallDate);
        int bigYear=big.get(Calendar.YEAR);
        int smallYear=small.get(Calendar.YEAR);
        int year=bigYear-smallYear;
        if(big.get(Calendar.MONTH)<small.get(Calendar.MONTH)){
            year--;
        }
        return year;
    }

    //判断是否是这个月的最后一天
    public boolean isMaxDay(Integer month,Integer day){
        Calendar calendar=Calendar.getInstance();
        calendar.set(Calendar.MONTH,month-1);
        calendar.set(Calendar.DAY_OF_MONTH,day);
        String maxDayStr=DateUtils.getMaxMonthDate(DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd"),"yyyy-MM-dd");
        Integer maxDay=Integer.valueOf(maxDayStr.substring(maxDayStr.length()-2,maxDayStr.length()));
        return maxDay.equals(day);
    }

    /**
     * 传入年判断是否是闰年
     * @param year
     * @return
     */
    public boolean isLeapYear(int year){
        Calendar calendar=Calendar.getInstance();
        calendar.set(year,2,1);
        calendar.add(Calendar.DAY_OF_MONTH,-1);
        return calendar.get(Calendar.DAY_OF_MONTH)==29;
    }

}
