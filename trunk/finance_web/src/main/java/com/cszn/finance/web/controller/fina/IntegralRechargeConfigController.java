package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaIntegralRechargeConfigService;
import com.cszn.integrated.service.api.fina.FinaMinIntegralModifyRecordService;
import com.cszn.integrated.service.entity.fina.FinaIntegralRechargeConfig;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.math.BigDecimal;
import java.util.List;

@RequestMapping(value = "/fina/integralRechargeConfig",viewPath = "/modules_page/finance/fina/integralRechargeConfig/")
public class IntegralRechargeConfigController extends BaseController {

    @Inject
    private FinaIntegralRechargeConfigService finaIntegralRechargeConfigService;
    @Inject
    private FinaMinIntegralModifyRecordService finaMinIntegralModifyRecordService;

    public void index(){

        render("configIndex.html");
    }

    public void configPage(){

        Page<FinaIntegralRechargeConfig> configPage=finaIntegralRechargeConfigService.configPage(getParaToInt("page"),getParaToInt("limit"));

        renderJson(new DataTable<FinaIntegralRechargeConfig>(configPage));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            FinaIntegralRechargeConfig config=finaIntegralRechargeConfigService.findById(id);
            config.put("integralRatioStr",new BigDecimal(config.getIntegralRatio() + "").toString());
            config.put("dayIntegralStr",new BigDecimal(config.getDayIntegral() + "").toString());
            config.put("monthIntegralStr",new BigDecimal(config.getMonthIntegral() + "").toString());
            config.put("yearIntegralStr",new BigDecimal(config.getYearIntegral() + "").toString());
            setAttr("model",config);
        }
        render("configForm.html");
    }

    public void saveConfig(){
        FinaIntegralRechargeConfig config=getBean(FinaIntegralRechargeConfig.class,"",true);
        if(StrKit.isBlank(config.getId())){
            Record record= Db.findFirst("select * from fina_integral_recharge_config where del_flag='0' and recharge_days=? ",config.getRechargeDays());
            if(record!=null){
                renderJson(Ret.fail("msg","改充值天数配置记录已存在"));
                return;
            }
        }else{
            Record record= Db.findFirst("select * from fina_integral_recharge_config where del_flag='0' and recharge_days=? and id<>?",config.getRechargeDays(),config.getId());
            if(record!=null){
                renderJson(Ret.fail("msg","改充值天数配置记录已存在"));
                return;
            }
        }
        boolean flag=finaIntegralRechargeConfigService.saveConfig(config, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void getConfigList(){

        List<FinaIntegralRechargeConfig> configList=finaIntegralRechargeConfigService.findAllConfig();
        renderJson(configList);
    }
}
