package com.cszn.finance.web.support.cron;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.service.api.fina.FinaCardIntegralRecordService;
import com.cszn.integrated.service.api.fina.FinaCardRechargeService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainCardTypeConfigService;
import com.cszn.integrated.service.entity.fina.FinaCardIntegralRecord;
import com.cszn.integrated.service.entity.fina.FinaCardRecharge;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainCardTypeConfig;
import com.ibm.icu.text.DecimalFormat;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.components.schedule.annotation.Cron;

/**
 * 会员卡每天积分充值结算定时任务
 */
//@Cron("0 3 * * *")
//@Cron("*/10 * * * *")
public class MemberCardIntegralRechargeSettleTask implements Runnable {

	private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);
	
	@Inject
	private MainCardTypeConfigService mainCardTypeConfigService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private FinaCardIntegralRecordService finaCardIntegralRecordService;
    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private FinaCardRechargeService finaCardRechargeService;

    @Override
    public void run() {
    	cardIntegralsRechargeSettleGen();
    }

    public void cardIntegralsRechargeSettleGen(){
    	DecimalFormat df = new DecimalFormat("#.00000");
    	List<Record> memberCardList = finaMembershipCardService.findListWithCardType();
    	logger.info("积分会员卡记录-充值："+memberCardList.size()+"条。");
		for(Record memberCard : memberCardList) {
			final String memmberCardId = memberCard.getStr("id");//会员卡id
			final String cardNum = memberCard.getStr("card_number");//会员卡号
			final String cardTypeId = memberCard.getStr("card_type_id");//会员卡类型id
			final Double cardIntegrals = memberCard.getDouble("card_integrals")!=null?memberCard.getDouble("card_integrals"):0;//剩余积分
			final String cardCreateTimeStr = DateUtils.formatDate(memberCard.getDate("create_time"));
			logger.info("cardNum-充值="+cardNum);
			double totalJF = new BigDecimal(cardIntegrals).setScale(5, BigDecimal.ROUND_DOWN).doubleValue();
			//查询未计算积分的充值记录
			List<FinaCardRecharge> isNotCountRechargeList = finaCardRechargeService.findIsNotCountList(memmberCardId);
			logger.info("积分会员卡充值记录："+isNotCountRechargeList.size()+"条!!!");
			for(FinaCardRecharge recharge : isNotCountRechargeList) {
				final String rechargeId = recharge.getId();
				Date rechargeTime = recharge.getRechargeTime();
				final Double rechargeConsumeTimes = recharge.getConsumeTimes();
//				final String rechargeCreateTimeStr = DateUtils.formatDate(recharge.getCreateTime());
				final String rechargeUpdateTimeStr = DateUtils.formatDate(recharge.getUpdateTime());
				if(rechargeTime!=null && rechargeConsumeTimes!=null && rechargeConsumeTimes>0) {
					// 开卡创建时间和充值审核更新时间不在同一天，才需要重新计算充值天数积分
					if(!cardCreateTimeStr.equals(rechargeUpdateTimeStr)) {
//						final Date createTime = DateUtils.parseDate(rechargeCreateTimeStr);
						final Date updateTime = DateUtils.parseDate(rechargeUpdateTimeStr);
						//充值时间少于等于当天创建时间的都要把充值天数加入重新计算并更新
						int compareNum = DateUtils.compareDays(rechargeTime, updateTime);
						while (compareNum<0) {
							logger.info("rechargeTime="+DateUtils.formatDate(rechargeTime));
							logger.info("updateTime="+DateUtils.formatDate(updateTime));
							logger.info("compareNum-充值："+compareNum);
							FinaCardIntegralRecord integralRecord = finaCardIntegralRecordService.getByCardIdAndGiveDate(memmberCardId, rechargeTime);
							if(integralRecord!=null) {
								double proportionValue = 0.0;
								final double oldIntegralValue = integralRecord.getIntegralValue();
								final double countValue = integralRecord.getCountValue()+rechargeConsumeTimes;
								List<MainCardTypeConfig> configList = mainCardTypeConfigService.findListByTypeIdAndValue(cardTypeId, countValue);
								if(configList!=null && configList.size()>0) {
									proportionValue = configList.get(0).getProportionValue();
									logger.info("充值计算比例："+df.format(proportionValue)+"充值日期===>>>"+DateUtils.formatDate(rechargeTime));
								}
								final double giveIntegralValue = countValue*proportionValue;
								totalJF -= new BigDecimal(oldIntegralValue).setScale(5, BigDecimal.ROUND_DOWN).doubleValue();//先减去旧积分
								totalJF += new BigDecimal(giveIntegralValue).setScale(5, BigDecimal.ROUND_DOWN).doubleValue();//再加上新计算积分
								
								FinaCardIntegralRecord tempIntegralRecord = new FinaCardIntegralRecord();
								tempIntegralRecord.setId(integralRecord.getId());
								tempIntegralRecord.setCountValue(countValue);
								tempIntegralRecord.setProportionValue(proportionValue);
								tempIntegralRecord.setIntegralValue(new BigDecimal(giveIntegralValue).setScale(5, BigDecimal.ROUND_DOWN).doubleValue());
								tempIntegralRecord.setUpdateTime(new Date());
								final boolean flag = finaCardIntegralRecordService.update(tempIntegralRecord);
								logger.info("会员卡-积分赠送记录-更新标识："+flag);
							}
							rechargeTime = DateUtils.getNextDay(rechargeTime, 1);
							compareNum = DateUtils.compareDays(rechargeTime, updateTime);
							logger.info("rechargeTime222="+DateUtils.formatDate(rechargeTime));
							logger.info("updateTime222="+DateUtils.formatDate(updateTime));
							logger.info("compareNum222-充值："+compareNum);
						}
					}
					FinaCardRecharge tempRecharge = new FinaCardRecharge();
					tempRecharge.setId(rechargeId);
					tempRecharge.setIsCount("1");
					final boolean flag = finaCardRechargeService.update(tempRecharge);
					logger.info("会员卡-充值记录-更新标识："+flag);
				}
			}
			FinaMembershipCard card = new FinaMembershipCard();
			card.setId(memmberCardId);
			card.setCardIntegrals(new BigDecimal(totalJF).setScale(5, BigDecimal.ROUND_DOWN).doubleValue());
			final boolean flag = finaMembershipCardService.update(card);
			logger.info("会员卡更新标识："+flag);
		}
    }

}
