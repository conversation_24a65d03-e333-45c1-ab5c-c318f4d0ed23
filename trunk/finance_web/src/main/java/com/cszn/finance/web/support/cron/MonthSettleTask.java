package com.cszn.finance.web.support.cron;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaSettleDetail;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import io.jboot.db.model.Columns;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 月结算
 */
@Cron("45 1 1 * *")
public class MonthSettleTask implements Runnable {

    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;

    @Override
    public void run() {
        monthSettleGen(1,20);
    }

    /**
     * 生成月结算账单
     */
    public void monthSettleGen(int pageNumber,int pageSize){

        Columns columns=Columns.create();
        columns.add("del_flag","0");
        columns.add("parent_id","0");
        columns.add("category","sojourn_bill");
        columns.add("checkin_status","stayin");
        columns.add("settle_status","0");

        Page<FinaExpenseRecord> mainRecordPage = finaExpenseRecordService.paginateByColumns(pageNumber,pageSize,columns);
        if(mainRecordPage==null || mainRecordPage.getList()==null || mainRecordPage.getList().size()==0 ){
            return;
        }
        Date lastMonthMaxDayDate = null;
        Date lastMonthMinDayDate = null;
        try {
            lastMonthMaxDayDate=DateUtils.getNextDay(DateUtils.parseDate(DateUtils.getLastMaxMonthDate(),"yyyyMMdd"),1);
            lastMonthMinDayDate=DateUtils.parseDate(DateUtils.getLastMonth()+"-01","yyyy-MM-dd");
        }catch (Exception e){
            e.printStackTrace();
        }
        List<FinaExpenseRecord> recordList=mainRecordPage.getList();
        List<FinaSettleDetail> finaSettleDetailList=new ArrayList<>();

        for(FinaExpenseRecord record:recordList){
            String totalSql="select card_number as cardNumber,sum(amount) as totalAmount,sum(times) as totalTimes,sum(points) as totalPoints,sum(integrals) as totalIntegrals" +
                    ",min(start_time) as startDate,max(end_time) as endDate from fina_expense_record_detail " +
                    "where del_flag='0' and is_settled='0' and expense_id in(select id from fina_expense_record " +
                    "where del_flag='0' and settle_status='0' and (id=? or parent_id=?)) and to_days(start_time)>=to_days(?) and to_days(end_time)<=to_days(?) group by card_number ";
            String actualSql="select card_number as cardNumber,sum(amount) as actualAmount,sum(times) as actualTimes,sum(points) as actualPoints,sum(integrals) as actualIntegrals " +
                    "from fina_expense_record_detail where  del_flag='0' and is_settled='0' and expense_id in(select id from fina_expense_record " +
                    "where del_flag='0' and settle_status='0' and (id=? or parent_id=?)) and to_days(start_time)>=to_days(?) and to_days(end_time)<=to_days(?) group by card_number ";
            List<Record> totalRecordList=Db.find(totalSql,record.getId(),record.getId(),lastMonthMinDayDate,lastMonthMaxDayDate);
            List<Record> actualRecordList=Db.find(actualSql,record.getId(),record.getId(),lastMonthMinDayDate,lastMonthMaxDayDate);
            for(Record totalRecord:totalRecordList){
                for(Record actualRecord:actualRecordList){
                    if(totalRecord.getStr("cardNumber").equals(actualRecord.getStr("cardNumber"))){
                        totalRecord.set("actualAmount",actualRecord.getDouble("actualAmount"));
                        totalRecord.set("actualTimes",actualRecord.getDouble("actualTimes"));
                        totalRecord.set("actualPoints",actualRecord.getDouble("actualPoints"));
                        totalRecord.set("actualIntegrals",actualRecord.getDouble("actualIntegrals"));
                        break;
                    }
                }
                FinaSettleDetail settleDetail=new FinaSettleDetail();
                settleDetail.setId(IdGen.getUUID());
                settleDetail.setActualAmount(totalRecord.getDouble("actualAmount"));
                settleDetail.setActualTimes(totalRecord.getDouble("actualTimes"));
                settleDetail.setActualPoints(totalRecord.getDouble("actualPoints"));
                settleDetail.setActualIntegrals(totalRecord.getDouble("actualIntegrals"));
                settleDetail.setTotalAmount(totalRecord.getDouble("totalAmount"));
                settleDetail.setTotalTimes(totalRecord.getDouble("totalTimes"));
                settleDetail.setTotalPoints(totalRecord.getDouble("totalPoints"));
                settleDetail.setTotalIntegrals(totalRecord.getDouble("totalIntegrals"));
                settleDetail.setExpenseId(record.getId());
                settleDetail.setCardNumber(totalRecord.getStr("cardNumber"));
                settleDetail.setStartTime(totalRecord.getDate("startDate"));
                settleDetail.setEndTime(totalRecord.getDate("endDate"));
                settleDetail.setDelFlag("0");
                settleDetail.setSettleType("month_settle");
                settleDetail.setSettleStatus("0");
                settleDetail.setYearMonth(DateUtils.getLastMonth());
                settleDetail.setCreateTime(new Date());
                settleDetail.setUpdateTime(new Date());
                finaSettleDetailList.add(settleDetail);
            }
        }
        if(finaSettleDetailList.size()>0) {
            Db.batchSave(finaSettleDetailList, finaSettleDetailList.size());
        }
        pageNumber++;
        if(pageNumber<=mainRecordPage.getTotalPage()){
            monthSettleGen(pageNumber,pageSize);
        }
    }



}
