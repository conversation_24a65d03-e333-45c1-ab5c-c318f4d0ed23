package com.cszn.finance.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.enums.SendType;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 积分会员卡余额短信发送
 */
@Cron("30 9 1 * *")
public class IntegralsCardTak implements Runnable {

    @Inject
    FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    FinaMembershipCardService finaMembershipCardService;
    @Inject
    SmsSendRecordService smsSendRecordService;

    @Override
    public void run() {
        monthIntegralsQuery(1,10);
    }

    public void monthIntegralsQuery(int pageNumber,int pageSize){

        Page<Record> cardPage = finaMembershipCardService.findIntegralsPage(pageNumber,pageSize);
        if(cardPage==null || cardPage.getList()==null || cardPage.getList().size()==0 ){
            return;
        }
        String regex = "^1[3-9][0-9]\\d{8}$";
        String nowStr=DateUtils.getDate("yyyy-MM-dd");
        for(Record card:cardPage.getList()){
        	final String fullName = card.getStr("fullName");
        	final String cardNumber = card.getStr("cardNumber");
        	final String cardIntegrals = StrKit.notBlank(card.getStr("cardIntegrals"))?card.getStr("cardIntegrals"):"0.0";
        	final String telephone = card.getStr("telephone");
        	
            if(StrKit.isBlank(telephone) || !telephone.matches(regex)){
                continue;
            }
            Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(cardNumber);
            final double lockIntegrals = lock.get("lockIntegrals")!=null?lock.get("lockIntegrals"):0.0;
            double integrals=new BigDecimal(cardIntegrals).subtract(BigDecimal.valueOf(lockIntegrals)).setScale(2,BigDecimal.ROUND_DOWN).doubleValue();
            //获取会员卡锁定

            if(integrals<=0){
                continue;
            }
            double amountLeftRemainder = integrals % 1;
            String usableAmountStr="";
            if(amountLeftRemainder==0){
                usableAmountStr=((int)integrals)+"";
            }else{
                usableAmountStr=integrals+"";
            }

            //【昌松集团】尊敬的黄斐萍会员，您卡号**********截止2023-10-01，积分余额为1。
            String content="尊敬的"+fullName+"会员，您卡号"+cardNumber+"截至"+nowStr+"，剩余积分为"+usableAmountStr;

            JSONObject obj=new JSONObject();
            obj.put("content",content);
            smsSendRecordService.sendMessage(SendType.customContent,cardNumber, JSON.toJSONString(obj),null);

        }

        pageNumber++;
        if(pageNumber<=cardPage.getTotalPage()){
            monthIntegralsQuery(pageNumber,pageSize);
        }
    }
}
