package com.cszn.finance.web.config;

import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.finance.web.validator.CorsInterceptor;
import com.cszn.integrated.base.common.AppInfo;
import com.cszn.integrated.base.web.handler.CustomActionHandler;
import com.cszn.integrated.base.web.render.AppRenderFactory;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.config.Constants;
import com.jfinal.config.Interceptors;
import com.jfinal.config.Routes;
import com.jfinal.ext.handler.ContextPathHandler;
import com.jfinal.json.FastJsonFactory;
import com.jfinal.log.Log4jLogFactory;
import com.jfinal.template.Engine;
import io.jboot.Jboot;
import io.jboot.aop.jfinal.JfinalHandlers;
import io.jboot.aop.jfinal.JfinalPlugins;
import io.jboot.core.listener.JbootAppListenerBase;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class JfinalConfigListener extends JbootAppListenerBase {
	
    @Override
    public void onConstantConfig(Constants constants) {
        constants.setError401View("/template/401.html");
        constants.setError403View("/template/403.html");
        constants.setError404View("/template/404.html");
        constants.setError500View("/template/500.html");
        constants.setJsonFactory(new FastJsonFactory());
        constants.setRenderFactory(new AppRenderFactory());
        constants.setLogFactory(new Log4jLogFactory());
    }

    @Override
    public void onRouteConfig(Routes routes) {
        routes.setBaseViewPath("/template");
    }

    @Override
    public void onEngineConfig(Engine engine) {
        engine.setDevMode(true);
        AppInfo app = Jboot.config(AppInfo.class);
        engine.addSharedObject("APP", app);
        engine.addSharedObject("RESOURCE_HOST", app.getResourceHost());
    }

    @Override
    public void onInterceptorConfig(Interceptors interceptors) {
        interceptors.add(new LogInterceptor());
        interceptors.add(new CorsInterceptor());
//        interceptors.add(new AuthInterceptor());
//        interceptors.add(new NotNullParaInterceptor("/template/exception.html"));
//        interceptors.add(new BusinessExceptionInterceptor("/template/exception.html"));
    }

    @Override
    public void onPluginConfig(JfinalPlugins plugins) {

    }

    @Override
    public void onHandlerConfig(JfinalHandlers handlers) {
    	handlers.setActionHandler(new CustomActionHandler());
        handlers.add(new ContextPathHandler("ctxPath"));
    }

    @Override
    public void onStart() {
    	/** 集群模式下验证码使用 redis 缓存 */
//      CaptchaManager.me().setCaptchaCache(new CaptchaCache());

    	Global.commonUpload = Jboot.configValue("commonUpload");
        Global.fileUrlPrefix = Jboot.configValue("fileUrlPrefix");
        Global.uploadPath = Jboot.configValue("uploadPath");
        Global.excelTemplatePath = Jboot.configValue("excelTemplatePath");
        Global.addCardMsgTemplate = Jboot.configValue("addCardMsgTemplate");
        Global.sendMsgUrl = Jboot.configValue("sendMsgUrl");
        Global.sendMsgCompanyName = Jboot.configValue("sendMsgCompanyName");
        Global.openAddCardMsg = Jboot.configValue("openAddCardMsg");
        Global.openCancelBookDeducted = Jboot.configValue("openCancelBookDeducted");
        Global.sendMessageUrl = Jboot.configValue("sendMessageUrl");
        Global.getMsgUrl = Jboot.configValue("getMsgUrl");
        Global.getMsgCountUrl = Jboot.configValue("getMsgCountUrl");
        Global.signReadUrl = Jboot.configValue("signReadUrl");
        Global.changeCardUrl = Jboot.configValue("changeCardUrl");
        Global.orgUpdateSettleMainUrl = Jboot.configValue("orgUpdateSettleMainUrl");
        Global.orgUpdateBillDateilUrl = Jboot.configValue("orgUpdateBillDateilUrl");
        Global.noticeChangeCardUrl = Jboot.configValue("noticeChangeCardUrl");
        Global.getInvoicePageUrl = Jboot.configValue("getInvoicePageUrl");
        Global.getInvoiceDetailsUrl = Jboot.configValue("getInvoiceDetailsUrl");
        Global.taskCreateUrl=Jboot.configValue("taskCreateUrl");
        Global.taskDaoUrl=Jboot.configValue("taskDaoUrl");
        Global.getMyTasksUrl=Jboot.configValue("getMyTasksUrl");
        Global.getTaskDetailByUser=Jboot.configValue("getTaskDetailByUser");
        Global.getTaskDetail=Jboot.configValue("getTaskDetail");
        Global.getTaskDetailList=Jboot.configValue("getTaskDetailList");


        Global.refundCardProcessNo=Jboot.configValue("refundCardProcessNo");
        Global.refundCardNo=Jboot.configValue("refundCardNo");
        Global.hrmUrl = Jboot.configValue("hrmUrl");
        Global.wmsAppNo=Jboot.configValue("wmsAppNo");
        Global.orgUrl = Jboot.configValue("orgUrl");
        Global.pensionUrl = Jboot.configValue("pensionUrl");
        Global.bmpUrl = Jboot.configValue("bmpUrl");
        Global.payApplyUrl=Jboot.configValue("payApplyUrl");
        Global.leaseRoomPaymentFormUrl=Jboot.configValue("leaseRoomPaymentFormUrl");
        Global.sojournDeductSMSConfigBeforeDays=Integer.valueOf(Jboot.configValue("sojournDeductSMSConfigBeforeDays"));
        Global.sojournDeductSMSConfigAfterDays=Integer.valueOf(Jboot.configValue("sojournDeductSMSConfigAfterDays"));
        Global.bookChangeCardUrl = Jboot.configValue("bookChangeCardUrl");
        Global.financeCheckForExpenseUrl=Jboot.configValue("financeCheckForExpenseUrl");
        Global.checkIdCardUrl=Jboot.configValue("checkIdCardUrl");

        Global.executorService=new ThreadPoolExecutor(10, 50, 0L
                , TimeUnit.MILLISECONDS
                , new LinkedBlockingQueue<>(1024)
                , new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r);
            }
        },new ThreadPoolExecutor.AbortPolicy());
    }

    @Override
    public void onStop() {
    }

	@Override
	public void onStartBefore() {
	}
}
