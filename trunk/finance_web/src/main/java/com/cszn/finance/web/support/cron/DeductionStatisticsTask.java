package com.cszn.finance.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.service.api.fina.FinaDayDeductStatisticService;
import com.cszn.integrated.service.entity.fina.FinaDayDeductStatistic;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Record;
import com.xiaoleilu.hutool.date.DateUtil;
import io.jboot.components.schedule.annotation.Cron;
import org.joda.time.DateTimeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;

/**
 * @Description 扣卡统计定时任务
 * <AUTHOR>
 * @Date 2019/7/22
 **/
@Cron("0 1 * * *")
public class DeductionStatisticsTask implements Runnable{

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private FinaDayDeductStatisticService finaDayDeductStatisticService;


    @Override
    public void run() {
        try {
            getDeductStatistic();
        } catch (Exception e) {
            logger.info("定时计算扣卡数据异常：[{}]",e.getMessage());
        }
    }



    /**
     * 计算扣卡数据
     * 包括已删除记录
     */
    public void getDeductStatistic(){
        logger.info("定时计算扣卡数据begin：[{}]",new Date());

        //获取（上一天）各个基地消费（总金额，总天数）
        String sqlNum = "select base_id,sum(amount) as total_amount ,sum(consume_times) as total_times,sum(points) as total_points from fina_consume_record " +
                "where status = 1 and refund_flag != 1 and consume_type in ('daily_deduction','book_deduction','checkin_deduction') " +
                "and base_id is not null " +
                "group by base_id";
        List<Record> numList = Db.find(sqlNum);
        logger.info("获取各个基地全部消费（总金额，总天数）：[{}]", JSON.toJSONString(numList));
        if(numList != null && numList.size() > 0){
            for (Record item : numList) {
                finaDayDeductStatisticService.dealBaseTotal(item);
            }
        }
        logger.info("获取各个基地上一天消费（总金额，总天数）：[{}]", JSON.toJSONString(numList));

        //插入统计表
        Boolean flag = Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    for (Record item : numList) {
                        FinaDayDeductStatistic statistic = new FinaDayDeductStatistic();
                        statistic.setId(IdGen.getUUID());
                        statistic.setBaseId(item.getStr("base_id"));
                        statistic.setStatisticsTime(TimeUtils.dayReduce(new Date()));
                        statistic.setDeductTimes(item.getInt("deduct_times"));
                        statistic.setDeductAmount(item.getDouble("deduct_amount"));
                        statistic.setDeductPoints(item.getDouble("deduct_points"));
                        statistic.setTotalAmount(item.getDouble("total_amount"));
                        statistic.setTotalTimes(item.getInt("total_times"));
                        statistic.setTotalPoints(item.getDouble("total_points"));

                        statistic.setCreateTime(new Date());
                        statistic.save();
                    }
                }catch (Exception e){
                    logger.info("数据插入统计表异常：[{}]",e.getMessage());
                    return false;
                }
                return true;
            }
        });
        logger.info("定时计算扣卡数据end：[{}]",new Date());
    }
}
