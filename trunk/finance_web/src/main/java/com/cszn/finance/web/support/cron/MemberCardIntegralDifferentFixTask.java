package com.cszn.finance.web.support.cron;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.integrated.service.api.fina.FinaCardIntegralRecordService;
import com.cszn.integrated.service.api.fina.FinaCardRechargeService;
import com.cszn.integrated.service.api.fina.FinaCardTransactionsService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.ibm.icu.text.DecimalFormat;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.components.schedule.annotation.Cron;

/**
 * 会员卡每天积分修正定时任务
 */
//@Cron("0 4 * * *")
public class MemberCardIntegralDifferentFixTask implements Runnable {

	private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);
	
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private FinaCardIntegralRecordService finaCardIntegralRecordService;
    @Inject
    private FinaCardTransactionsService finaCardTransactionsService;
    @Inject
    private FinaCardRechargeService finaCardRechargeService;

    @Override
    public void run() {
    	cardIntegralsDifferrentFixRun();
    }

    public void cardIntegralsDifferrentFixRun(){
    	DecimalFormat df = new DecimalFormat("#.00000");
    	List<Record> memberCardList = finaMembershipCardService.findListWithCardType();
    	logger.info("积分会员卡记录-修正："+memberCardList.size()+"条。");
		for(Record memberCard : memberCardList) {
			final String memmberCardId = memberCard.getStr("id");//会员卡id
			final String cardNum = memberCard.getStr("card_number");//会员卡号
			final Double cardIntegrals = memberCard.getDouble("card_integrals")!=null?memberCard.getDouble("card_integrals"):0;//剩余积分
			logger.info("cardNum-修正="+cardNum);
			//获取赠送记录积分总数
			double totalGiveIntegral = finaCardIntegralRecordService.getTotalGiveIntegralByCardId(memmberCardId);
			//获取扣除积分总数
			double totalDeductIntegral = finaCardTransactionsService.getTotalDeductIntegralsByCardId(memmberCardId);
			//获取充值积分总数
			double totalRechargeIntegral = finaCardRechargeService.getTotalRechargeIntegralsByCardId(memmberCardId);
			logger.info("剩余积分："+cardIntegrals);
			logger.info("赠送记录积分总数："+totalGiveIntegral);
			logger.info("扣除积分总数："+totalDeductIntegral);
			logger.info("充值积分总数："+totalRechargeIntegral);
			
			if(totalGiveIntegral>0) {
				if(totalRechargeIntegral>0) {
					totalGiveIntegral = totalGiveIntegral + totalRechargeIntegral;
				}
				if(totalDeductIntegral>0) {
					totalGiveIntegral = totalGiveIntegral - totalDeductIntegral;
				}
				if(totalGiveIntegral!=cardIntegrals) {
					FinaMembershipCard card = new FinaMembershipCard();
					card.setId(memmberCardId);
					card.setCardIntegrals(totalGiveIntegral);
					final boolean flag = finaMembershipCardService.update(card);
					logger.info("积分不相同需要更新："+flag);
//					if(flag) {
//						break;
//					}
				}else {
					logger.info("积分相同不需要更新");
				}
			}
		}
    }

}
