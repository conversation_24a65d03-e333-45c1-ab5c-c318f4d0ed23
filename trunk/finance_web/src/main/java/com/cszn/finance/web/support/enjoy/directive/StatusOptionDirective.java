package com.cszn.finance.web.support.enjoy.directive;

import com.cszn.integrated.base.common.BaseStatus;
import com.jfinal.template.Env;
import com.jfinal.template.io.Writer;
import com.jfinal.template.stat.ParseException;
import com.jfinal.template.stat.Scope;

import io.jboot.web.directive.annotation.JFinalDirective;
import io.jboot.web.directive.base.JbootDirectiveBase;

/**
 * 状态下拉Option指令
 * <AUTHOR>
 *
 */
@JFinalDirective("statusOption")
public class StatusOptionDirective extends JbootDirectiveBase {

    /** 状态类全路径 例如：com.AbcStatus */
    private BaseStatus status;
    private String value;

    @Override
    public void exec(Env env, Scope scope, Writer writer) {
        if (exprList.length() > 2) {
            throw new ParseException("Wrong number parameter of #statusOption directive, two parameters allowed at most", location);
        }

        status = getPara(0, scope);
        if (status == null) {
            throw new ParseException("status is null", location);
        }

        if (exprList.length() > 1) {
            value = getPara(1, scope, "");
        }

        for (String key : status.all().keySet()) {
            if (value != null && key.equals(value)) {
                write(writer, "<option selected value=\"" + key  + "\">" + status.desc(key) + "</option>");
            } else {
                write(writer, "<option value=\"" + key  + "\">" + status.desc(key) + "</option>");
            }
        }
    }

    @Override
    public void onRender(Env env, Scope scope, Writer writer) {

    }
}
