package com.cszn.finance.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordDetailService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordFollowLeaveService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordDetail;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordFollowLeave;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.aop.annotation.Bean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;

/**
 * @Description 每日2点处理扣卡统计数据
 * <AUTHOR>
 * @Date 2019/9/4
 **/
//@Cron("*/2 * * * *")
//@Cron("30 2 * * *")
@Bean
public class CardSettleTask implements Runnable{

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;

    @Inject
    private FinaExpenseRecordFollowLeaveService finaExpenseRecordFollowLeaveService;

    @Inject
    private FinaExpenseRecordDetailService finaExpenseRecordDetailService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;




    @Override
    public void run() {
        try {
            detailSettle(1,50);
            logger.info("统计消费明细实扣end...[{}]",TimeUtils.getStrDateByPattern(new Date(),"yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            logger.error("每日中午12点处理扣卡统计数据异常：[{}]",e);
        }
        /*try {
            logger.info("testCard start");
            testCard(1,500);
        }catch (Exception e){
            e.printStackTrace();
            logger.info("testCard error");
        }
        logger.info("testCard end");*/
    }

    public void testCard(Integer pageNumber,Integer pageSize){
        String select=" select * ";
        String sql=" from fina_membership_card  ";
        Page<Record> erPage = Db.paginate(pageNumber,pageSize,select,sql);
        if(erPage.getList()==null || erPage.getList().size()==0){
            return;
        }
        for(Record record:erPage.getList()){
            String deduct_scheme_id=record.getStr("deduct_scheme_id");
            String long_deduct_scheme_id=record.getStr("long_deduct_scheme_id");
            String card_type_id=record.getStr("card_type_id");
            String cardNumber=record.getStr("card_number");
            MainCardDeductScheme deductScheme=mainCardDeductSchemeService.findById(deduct_scheme_id);
            if(deductScheme==null){
                deductScheme=mainCardDeductSchemeService.findById(long_deduct_scheme_id);
            }
            if(deductScheme==null){
                logger.info(record.getStr("card_number")+" 会员卡扣卡方式未设置");
                continue;
            }
            MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card_type_id);
            if(cardType==null){
                logger.info(record.getStr("card_number")+" 会员卡类别未设置");
                continue;
            }
            if("1".equals(cardType.getIsIntegral())){
                Map<String,Double> lockInfo=finaMembershipCardService.getCardLockInfo(cardNumber);
                double lockConsumeTimes=lockInfo.get("lockConsumeTimes");
                double lockIntegrals=lockInfo.get("lockIntegrals");
                Double cardTimes=record.getDouble("consume_times");
                Double cardIntegrals=record.getDouble("card_integrals");
                if(cardTimes==null){
                    cardTimes=0.0;
                }
                if(cardIntegrals==null){
                    cardIntegrals=0.0;
                }
                double timesDiff=BigDecimal.valueOf(cardTimes).subtract(BigDecimal.valueOf(lockConsumeTimes)).doubleValue();
                double integralsDiff=BigDecimal.valueOf(cardIntegrals).subtract(BigDecimal.valueOf(lockIntegrals)).doubleValue();
                if(timesDiff<0){
                    logger.info(record.getStr("card_number")+" 锁定:"+lockConsumeTimes+",天数余额:"+cardTimes);
                }
                if(timesDiff<0){
                    logger.info(record.getStr("card_number")+" 锁定:"+lockIntegrals+",积分余额:"+cardIntegrals);
                }
            }else{
                Map<String,Double> lockInfo=finaMembershipCardService.getCardLockInfo(cardNumber);
                if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
                    double lockConsumeTimes=lockInfo.get("lockConsumeTimes");
                    Double cardTimes=record.getDouble("consume_times");
                    if(cardTimes==null){
                        cardTimes=0.0;
                    }
                    double timesDiff=BigDecimal.valueOf(cardTimes).subtract(BigDecimal.valueOf(lockConsumeTimes)).doubleValue();
                    if(timesDiff<0){
                        logger.info(record.getStr("card_number")+" 锁定:"+lockConsumeTimes+",天数余额:"+cardTimes);
                    }
                }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                    double lockBalance=lockInfo.get("lockBalance");
                    Double balance=record.getDouble("balance");
                    if(balance==null){
                        balance=0.0;
                    }
                    double balanceDiff=BigDecimal.valueOf(balance).subtract(BigDecimal.valueOf(lockBalance)).doubleValue();
                    if(balanceDiff<0){
                        logger.info(record.getStr("card_number")+" 锁定:"+lockBalance+",天数余额:"+balance);
                    }
                }
            }

        }

        pageNumber++;//下一页
        if(pageNumber <= erPage.getTotalPage()){
            testCard(pageNumber,pageSize);
        }
    }


    /**
     * 处理账单应扣卡数据
     * 1.查询所有未结算账单
     * 2.查询账单入住人的离开记录并查询应扣费明细
     * 3.有变更会员卡记录则不统计实扣
     * 4.变更明细状态
     * 5.筛选账单中的主账单记录
     * 6.调用扣卡统计方法
     */
    public void detailSettle(Integer pageNumber,Integer pageSize){
        logger.info("统计消费明细实扣begin...[{}]", TimeUtils.getStrDateByPattern(new Date(),"yyyy-MM-dd HH:mm:ss"));
        Page<FinaExpenseRecord> erPage = finaExpenseRecordService.getErPageNotSettle(pageNumber,pageSize);
        if(erPage != null && erPage.getList() != null && erPage.getList().size() > 0) {
            List<FinaExpenseRecord> erList = erPage.getList();
            logger.info("需要今日结算的账单这页[{}]条数据,共[{}]页,每页50条:[{}]", erPage.getPageSize(), erPage.getTotalPage(), JSON.toJSONString(erList));

            Map<String, Object> partMap = getDetailByErListNotSettle(erList);
            List<FinaExpenseRecordDetail> fdList = (List<FinaExpenseRecordDetail>) partMap.get("fdList");
            Map<String, Record> valMap = (Map<String, Record>) partMap.get("valMap");
            if (fdList != null && fdList.size() > 0) {
                updateErActual(valMap, erList);
                List<FinaExpenseRecord> mainList = getMainList(erList);
                if (mainList != null && mainList.size() > 0) {
                    boolean flag = updateDetailsAndErList(fdList, erList);
                    boolean suc = false;
                    if (flag) {
                        suc = finaExpenseRecordService.getCardTotalActual(mainList);
                    }
                    logger.info("未结算账单第" + pageNumber + "页执行是否成功:[{}]", flag && suc);
                }
            }
        }

        pageNumber++;//下一页
        if(pageNumber <= erPage.getTotalPage()){
            logger.info("第"+pageNumber+"页继续...");
            detailSettle(pageNumber,pageSize);
        }
    }


    /**
     * 根据未结算账单获取消费明细
     * 更新明细状态
     * 统计明细实扣
     */
    public Map<String,Object> getDetailByErListNotSettle(List<FinaExpenseRecord> erList){
        Map<String,Object> map = new HashMap<>();
        List<FinaExpenseRecordDetail> fdList = new ArrayList<>();
        Map<String, Record> valMap = new HashMap<>();
        if(erList != null || erList.size() > 0){
            for (FinaExpenseRecord item : erList) {
                Date time = null;
                FinaExpenseRecordFollowLeave fl = finaExpenseRecordFollowLeaveService.getFl(item.getId());//是否有离开记录
                if(fl != null && fl.getLeaveTime() != null)time = fl.getLeaveTime();
                List<FinaExpenseRecordDetail> detailList = finaExpenseRecordDetailService.getDetailsByExpenseIdAndLeaveTime(item,time);
                if(detailList != null && detailList.size() > 0){
                    for (FinaExpenseRecordDetail it : detailList) {
                        it.setStatus("1");
                        it.setUpdateTime(new Date());//更新明细状态
                        if(it.getPoints()==null){
                            it.setPoints(0.00);
                        }
                        if (valMap.containsKey(it.getExpenseId())) {
                            Record record = valMap.get(it.getExpenseId());
                            record.set("amount", BigDecimal.valueOf(record.getDouble("amount")).add(BigDecimal.valueOf(it.getAmount())));
                            record.set("times", BigDecimal.valueOf(record.getDouble("times")).add(BigDecimal.valueOf(it.getTimes())));
                            record.set("points",BigDecimal.valueOf(record.getDouble("points")).add(BigDecimal.valueOf(it.getPoints())));
                        } else {
                            Record record = new Record();
                            record.set("amount", it.getAmount());
                            record.set("times", it.getTimes());
                            record.set("points",it.getPoints());
                            valMap.put(it.getExpenseId(), record);
                        }
                    }
                    fdList.addAll(detailList);
                }
            }
        }
        map.put("fdList",fdList);
        map.put("valMap",valMap);
        return map;
    }



    /**
     * 设置更新未结算账单的实扣
     */
    public void updateErActual(Map<String, Record> valMap,List<FinaExpenseRecord> erList){
        erList.clear();
        Set<String> keys = valMap.keySet();
        Iterator<String> iterator = keys.iterator();
        while (iterator.hasNext()) {
            String id = iterator.next();
            //统计明细的实扣
            Long count = Db.queryLong("select count(*) from fina_expense_record_deduction_card where expense_id = ?",id);//有换卡记录则不统计
            FinaExpenseRecord er = finaExpenseRecordService.get(id);
            if(count <= 0) {
                Record record = valMap.get(id);
                Double times = record.getDouble("times");
                Double amount = record.getDouble("amount");
                Double points = record.getDouble("points");
                if (er.getActualTimes() == null) er.setActualTimes(0.00);
                if (er.getActualAmount() == null) er.setActualAmount(0.00);
                if (er.getActualPoints()==null) er.setActualPoints(0.00);
                er.setActualTimes(BigDecimal.valueOf(er.getActualTimes()).add(BigDecimal.valueOf(times)).doubleValue());
                er.setActualAmount(BigDecimal.valueOf(er.getActualAmount()).add(BigDecimal.valueOf(amount)).doubleValue());
                er.setActualPoints(BigDecimal.valueOf(er.getActualPoints()).add(BigDecimal.valueOf(points)).doubleValue());
                er.setUpdateTime(new Date());
            }
            erList.add(er);
        }
    }




    /**
     *在50条账单数据中筛选出主账单list
     * @return
     */
    public List<FinaExpenseRecord> getMainList(List<FinaExpenseRecord> erList){
        List<FinaExpenseRecord> mainList = Lists.newArrayList();
        for (FinaExpenseRecord item : erList) {
            if ("0".equals(item.getParentId())) {
                mainList.add(item);
            } else {
                FinaExpenseRecord er = finaExpenseRecordService.get(item.getParentId());
                if (er != null) {
                    if (mainList == null || mainList.size() <= 0) {
                        mainList.add(er);
                    } else {
                        int i = 0;
                        for (FinaExpenseRecord it : mainList) {
                            if (it.getId().equals(er.getId())) {
                                i = 1;
                            }
                        }
                        if (i == 0) {
                            mainList.add(er);
                        }
                    }
                }
            }
        }
        return mainList;
    }







    /**
     * 更新明细和账单记录
     * @return
     */
    public boolean updateDetailsAndErList(List<FinaExpenseRecordDetail> fdList,List<FinaExpenseRecord> erList){
        return Db.tx(new IAtom() {
            public boolean run() throws SQLException {
                try {
                    int[] intList = Db.batchUpdate(fdList, fdList.size());
                    for (int i : intList) {
                        if (i < 1) {
                            return false;
                        }
                    }
                    int[] intUp = Db.batchUpdate(erList, erList.size());
                    for (int i : intUp) {
                        if (i < 1) {
                            return false;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
                return true;
            }
        });
    }
}
