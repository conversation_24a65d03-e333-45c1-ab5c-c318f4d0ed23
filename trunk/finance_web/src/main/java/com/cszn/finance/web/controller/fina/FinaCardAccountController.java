/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardAccountService;
import com.cszn.integrated.service.entity.enums.PaymentWay;
import com.cszn.integrated.service.entity.fina.FinaCardAccount;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by LiangHuiLing on 2020年11月9日
 *
 * RoleController
 */
@RequestMapping(value="/fina/account", viewPath="/modules_page/finance/fina/account")
public class FinaCardAccountController extends BaseController {

	@Inject
    private FinaCardAccountService finaCardAccountService;


    private Logger logger= LoggerFactory.getLogger(FinaCardAccountController.class);
	
    public void index() {
        render("accountIndex.html");
    }
    
    /**
     * 角色分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	FinaCardAccount model = getBean(FinaCardAccount.class, "", true);
        Page<FinaCardAccount> modelPage = finaCardAccountService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaCardAccount>(modelPage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	FinaCardAccount model = new FinaCardAccount();
    	Map<String,String> payWay=new HashMap<>();
        for(PaymentWay paymentWay:PaymentWay.values()){
        	payWay.put(paymentWay.getKey(),paymentWay.getValue());
        }
        setAttr("payWay", payWay);
    	setAttr("model", model);
        render("accountForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String id = getPara("id");
        Map<String,String> payWay=new HashMap<>();
        for(PaymentWay paymentWay:PaymentWay.values()){
        	payWay.put(paymentWay.getKey(),paymentWay.getValue());
        }
        setAttr("payWay", payWay);
        setAttr("model", finaCardAccountService.findById(id));
        render("accountForm.html");
    }
    
    public void save() {
    	FinaCardAccount model = getBean(FinaCardAccount.class, "", true);
		renderJson(finaCardAccountService.accountSave(model, AuthUtils.getUserId()));
    }
    
    public void del(){
    	final FinaCardAccount model = getBean(FinaCardAccount.class, "", true);
    	if(model!=null && StrKit.notBlank(model.getId())){
    		if(finaCardAccountService.update(model)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }
    public void monthSettleGen(){
        int pageNumber=getParaToInt("pageNumber");
        int pageSize=getParaToInt("pageSize");
        String date=getPara("date");
        try {
            //cardMonthBalanceTask.monthBalance(pageNumber,pageSize,date);
        }catch (Exception e){
            logger.info(e.getMessage());
        }
        renderJson("ok");
    }

    public void monthSettleGen2(){
        int pageNumber=getParaToInt("pageNumber");
        int pageSize=getParaToInt("pageSize");
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");
        try {
            //cardMonthBalanceTask.monthBalance(pageNumber,pageSize,endDate,startDate);
        }catch (Exception e){
            e.printStackTrace();
            logger.info(e.getMessage());
        }
        renderJson("ok");
    }
}
