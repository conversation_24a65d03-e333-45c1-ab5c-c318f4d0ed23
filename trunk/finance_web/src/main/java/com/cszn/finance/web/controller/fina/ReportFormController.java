package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardCollectService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.fina.FinaRefundCardRecordService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.sms.SmsSendRecord;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


@RequestMapping(value = "/fina/reportForm",viewPath = "/modules_page/finance/fina/reportForm")
public class ReportFormController extends BaseController {

    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private FinaCardCollectService finaCardCollectService;
    @Inject
    private FinaRefundCardRecordService finaRefundCardRecordService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private SmsSendRecordService smsSendRecordService;
    
    private Logger logger= LoggerFactory.getLogger(ReportFormController.class);

    public void index(){
        List<MainBase> baseList=mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        render("reportFormIndex.html");
    }

    /**
     * 收款报表页面
     */
    public void cardCollectIndex(){
        String yearMonthDay=DateUtils.getDate("yyyy-MM-dd");
        String yearMonth=DateUtils.getDate("yyyy-MM");
        String year=DateUtils.getDate("yyyy");
        setAttr("yearMonthDay",yearMonthDay);
        setAttr("yearMonth",yearMonth);
        setAttr("year",year);
        render("cardCollectIndex.html");
    }

    /**
     * 退款报表页面
     */
    public void cardRefundIndex(){
        String yearMonthDay=DateUtils.getDate("yyyy-MM-dd");
        String yearMonth=DateUtils.getDate("yyyy-MM");
        String year=DateUtils.getDate("yyyy");
        setAttr("yearMonthDay",yearMonthDay);
        setAttr("yearMonth",yearMonth);
        setAttr("year",year);
        render("cardRefundIndex.html");
    }


    @Clear(LogInterceptor.class)
    public void loadTable1(){
        String baseId=getPara("baseId");
        String dateRange=getPara("dateRange");
        String[] datesStr=dateRange.split(" - ");
        Page<Record> recordPage=finaExpenseRecordService.findDeductionDetailPage(getParaToInt("page"),getParaToInt("limit"),baseId,datesStr[0]+" 00:00:00",datesStr[1]+" 23:59:59");
        renderJson(new DataTable<Record>(recordPage));
    }


    @Clear(LogInterceptor.class)
    public void loadTable2(){
        String baseId=getPara("baseId");
        String date=getPara("date");

        Record record=finaExpenseRecordService.findCheckoutCount(baseId,date);

        List<Record> recordList=new ArrayList<>();
        recordList.add(record);
        renderJson(new DataTable<Record>(recordList));
    }

    @Clear(LogInterceptor.class)
    public void loadTable3(){
        String month=getPara("month");

        List<Record> recordList=finaExpenseRecordService.findBaseMonthSummary(month);
        renderJson(new DataTable<Record>(recordList));
    }

    @Clear(LogInterceptor.class)
    public void loadTable4(){
        String year=getPara("year");

        List<Record> recordList=finaExpenseRecordService.findBaseYearSummary(year);

        renderJson(new DataTable<Record>(recordList));
    }

    @Clear(LogInterceptor.class)
    public void loadTable5(){
        String year=getPara("year");

        List<Record> recordList=finaExpenseRecordService.findCardTypeYearSummary(year);

        renderJson(new DataTable<Record>(recordList));
    }

    /**
     * 退款统计数据
     */
    @Clear(com.jfinal.ext.interceptor.LogInterceptor.class)
    public void refundStatistics(){
        String queryType = getPara("queryType");
        String yearMonthDay = getPara("yearMonthDay");
        String yearMonth = getPara("yearMonth");
        String year = getPara("year");
        List<Record> list = finaRefundCardRecordService.refundStatistics(queryType, yearMonthDay, yearMonth, year);
        renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }

    /**
     * 退款列表数据
     */
    @Clear(com.jfinal.ext.interceptor.LogInterceptor.class)
    public void refundListPage(){
        String queryType = getPara("queryType");
        String yearMonthDay = getPara("yearMonthDay");
        String yearMonth = getPara("yearMonth");
        String year = getPara("year");
        String branchOfficeName = getPara("branchOfficeName");
        Page<Record> page = finaRefundCardRecordService.refundList(queryType, yearMonthDay, yearMonth, year,branchOfficeName,getParaToInt("page"),getParaToInt("limit"));
        renderJson(new DataTable<Record>(page));
    }

    /**
     * 收款统计数据
     */
    @Clear(com.jfinal.ext.interceptor.LogInterceptor.class)
    public void collectStatistics(){
        String queryType = getPara("queryType");
        String yearMonthDay = getPara("yearMonthDay");
        String yearMonth = getPara("yearMonth");
        String year = getPara("year");
        List<Record> list = finaCardCollectService.collectStatistics(queryType, yearMonthDay, yearMonth, year);
        renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }

    /**
     * 收款列表数据
     */
    @Clear(com.jfinal.ext.interceptor.LogInterceptor.class)
    public void collectListPage(){
        String queryType = getPara("queryType");
        String yearMonthDay = getPara("yearMonthDay");
        String yearMonth = getPara("yearMonth");
        String year = getPara("year");
        String branchOfficeName = getPara("branchOfficeName");
        Page<Record> page = finaCardCollectService.collectList(queryType, yearMonthDay, yearMonth, year,branchOfficeName,getParaToInt("page"),getParaToInt("limit"));
        renderJson(new DataTable<Record>(page));
    }


    public void send(){
    	
    	String cardNumber = getPara("cardNumber");
    	
    	String regex = "^1[3-9][0-9]\\d{8}$";
    	Map<String,String> params=new HashMap<>();
    	List<SmsSendRecord> recordList=new ArrayList<>();
		params.put("tempId", SendType.customContent.getTemplateId());
    	if(StrKit.notBlank(cardNumber)){
    		FinaMembershipCard memberCard = finaMembershipCardService.getByCardNumber(cardNumber);
    		if(memberCard!=null){
    			final String memberId = memberCard.getMemberId();
    			final String memberName = Db.queryStr("select full_name from mms_member where id=?",memberId);
    			final Double cardIntegrals = memberCard.getCardIntegrals();
    			final String phoneNumber = memberCard.getTelephone();
    			if(StrKit.isBlank(phoneNumber) || !phoneNumber.matches(regex)){
    				renderJson(Ret.fail("msg", "手机号码格式不正确!"));
				}else{
					//发送短信
    				params.put("mobile",phoneNumber);
    				params.put("data", "{\"content\":\""+ "尊敬的"+memberName+"会员，由于系统升级2022-11-01 09:30至10:00发送的积分卡账单信息错误" +
    						"，现更正为：您卡号"+cardNumber+"截止2022-11-18积分余额为"+cardIntegrals+"；请以此条短信为准，给您带来不便，敬请谅解！"+"\"}");
    				
    				String resultStr= HttpClientsUtils.httpPostForm("https://bmp.cncsgroup.com/Member/SendByTemp", params,null,"UTF-8");
    				logger.info("短信发送："+JSON.toJSONString(params)+"，结果："+resultStr);
    				if(resultStr.startsWith("{") && resultStr.endsWith("}")){
    					JSONObject object= JSON.parseObject(resultStr);
    					if(object.containsKey("Type") && "1".equals(object.getString("Type"))){
    						
    					}else{
    						//保存一条短信发送失败记录
    						SmsSendRecord record=new SmsSendRecord();
    						record.setId(IdGen.getUUID());
    						record.setSendType(SendType.customContent.getKey());
    						record.setPhoneNum(phoneNumber);
    						record.setSmsParam(JSON.toJSONString(params));
    						record.setSmsTemplateId(SendType.customContent.getTemplateId());
    						record.setSmsSendStatus("send_fail");
    						record.setDelFlag("0");
    						record.setCreateDate(new Date());
    						record.setUpdateDate(new Date());
    						recordList.add(record);
    					}
    				}
				}
    		}else{
    			renderJson(Ret.fail("msg", "会员卡不存在!"));
    		}
    	}else{
//    		final String sql = "\n" +
//				"select c.card_number,c.card_integrals,a.card_id,c.telephone,m.full_name from fina_card_transactions a \n" +
//				"left join fina_membership_card c on c.id=a.card_id\n" +
//				"left join main_membership_card_type t on t.id=c.card_type_id \n" +
//				"left join mms_member m on m.id=c.member_id\n" +
//				"where a.is_hidden=0 and a.type in ('checkin_deduction','daily_deduction') \n" +
//				"and a.deal_time>'2022-08-11 11:00:00' \n" +
//				"and c.del_flag='0' and c.is_lock='0' and t.is_integral='1'\n" +
//				"group by a.card_id\n" +
//				"HAVING count(a.id)>0\n" +
//				"ORDER BY c.card_number\n ";
    		final String sql = "select v1.card_number,c.card_integrals,m.full_name,v1.card_type,v1.telephone from ("
				+ "select c.card_number,t.card_type,c.telephone from fina_membership_card c "
				+ "left join ("
				+ "select card_id,count(id)dataCount from fina_card_transactions "
				+ "where is_hidden=0 and type in ('recharge_prestore') and deal_time>'2022-08-11 11:00:00' and deal_time<'2022-11-01 09:30:00' "
				+ "and IFNULL(integrals,0)>0 group by card_id"
				+ ")v on v.card_id=c.id "
				+ "left join main_membership_card_type t on t.id=c.card_type_id "
				+ "where c.del_flag='0' and c.is_lock='0' and t.is_integral='1' and v.dataCount>0"
				+ ")v1 "
				+ "left join fina_membership_card c on c.card_number=v1.card_number "
				+ "left join mms_member m on m.id=c.member_id "
				+ "where v1.card_number not in ("
				+ "select c.card_number from fina_membership_card c "
				+ "left join ("
				+ "select card_id,count(id)dataCount from fina_card_transactions "
				+ "where is_hidden=0 and type in ('checkin_deduction','daily_deduction') and deal_time>'2022-08-11 11:00:00' "
				+ "and deal_time<'2022-11-01 09:30:00' and IFNULL(integrals,0)>0 group by card_id"
				+ ")v on v.card_id=c.id "
				+ "left join main_membership_card_type t on t.id=c.card_type_id "
				+ "where c.del_flag='0' and c.is_lock='0' and t.is_integral='1' and v.dataCount>0)";
    		List<Record> phoneNumList= Db.find(sql);
    		if(phoneNumList!=null && phoneNumList.size()>0){
    			for(Record recordS:phoneNumList){
    				String phoneNum=recordS.getStr("telephone");
    				if(StrKit.isBlank(phoneNum) || !phoneNum.matches(regex)){
    					continue;
    				}
    				//发送短信
    				params.put("mobile",phoneNum);
    				params.put("data", "{\"content\":\""+ "尊敬的"+recordS.getStr("full_name")+"会员，由于系统升级2022-11-01 09:30至10:00发送的积分卡账单信息错误" +
    						"，现更正为：您卡号"+recordS.getStr("card_number")+"截止2022-11-18积分余额为"+recordS.getStr("card_integrals")+"；请以此条短信为准，给您带来不便，敬请谅解！"+"\"}");
    				
    				String resultStr= HttpClientsUtils.httpPostForm("https://bmp.cncsgroup.com/Member/SendByTemp", params,null,"UTF-8");
    				logger.info("短信发送："+JSON.toJSONString(params)+"，结果："+resultStr);
    				if(resultStr.startsWith("{") && resultStr.endsWith("}")){
    					JSONObject object= JSON.parseObject(resultStr);
    					if(object.containsKey("Type") && "1".equals(object.getString("Type"))){
    						
    					}else{
    						//保存一条短信发送失败记录
    						SmsSendRecord record=new SmsSendRecord();
    						record.setId(IdGen.getUUID());
    						record.setSendType(SendType.customContent.getKey());
    						record.setPhoneNum(phoneNum);
    						record.setSmsParam(JSON.toJSONString(params));
    						record.setSmsTemplateId(SendType.customContent.getTemplateId());
    						record.setSmsSendStatus("send_fail");
    						record.setDelFlag("0");
    						record.setCreateDate(new Date());
    						record.setUpdateDate(new Date());
    						recordList.add(record);
    					}
    				}
    			}
    			
    			if(recordList.size()>0){
    				Db.batchSave(recordList,recordList.size());
    			}
    		}
    	}
    	renderJson(Ret.ok("msg", "发送成功!"));
    }

    public void send2(){
        List<Record> recordList=Db.find("select a.id,a.card_number as cardNumber,a.card_integrals as cardIntegrals,c.full_name as fullName,a.telephone \n" +
                "from fina_membership_card a \n" +
                "\t\t\t\tleft join main_membership_card_type b on a.card_type_id=b.id \n" +
                "\t\t\t\tleft join mms_member c on c.id=a.member_id \n" +
                "\t\t\t\twhere a.del_flag='0' and a.is_lock='0' and a.card_integrals>0 and b.is_integral='1' \n" +
                "\t\t\t\tand a.card_number in (\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********',\n" +
                "'**********','**********'\n" +
                "\t\t\t\t)\n" +
                "\t\t\t\tORDER BY a.create_time");
        if(recordList==null || recordList.size()==0 ){
            return;
        }
        String regex = "^1[3-9][0-9]\\d{8}$";
        String nowStr=DateUtils.getDate("yyyy-MM-dd");
        for(Record card:recordList){
            if(StrKit.isBlank(card.getStr("telephone")) || !card.getStr("telephone").matches(regex)){
                continue;
            }
            JSONObject obj=new JSONObject();
            obj.put("company", Global.sendMsgCompanyName);
            obj.put("fullName",card.getStr("fullName"));
            obj.put("cardNumber",card.getStr("cardNumber"));
            obj.put("time",nowStr);
            obj.put("integrals",card.getStr("cardIntegrals"));
            smsSendRecordService.sendMessage(SendType.monthIntegralsQuery,card.getStr("cardNumber"), JSON.toJSONString(obj),null);
        }
        renderJson(Ret.ok());
    }

}
