package com.cszn.finance.web.support.cron;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.fina.FinaCardIntegralRecordService;
import com.cszn.integrated.service.api.fina.FinaCardRechargeService;
import com.cszn.integrated.service.api.fina.FinaCardTransactionsService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordDetailService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainCardTypeConfigService;
import com.cszn.integrated.service.entity.fina.FinaCardIntegralRecord;
import com.cszn.integrated.service.entity.fina.FinaCardRecharge;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainCardTypeConfig;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.components.schedule.annotation.Cron;

/**
 * 会员卡每天积分结算定时任务
 */
@Cron("20 2 * * *")
//@Cron("*/5 * * * *")
public class MemberCardIntegralSettleTask implements Runnable {

	private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);
	
	@Inject
	private MainCardTypeConfigService mainCardTypeConfigService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private FinaCardIntegralRecordService finaCardIntegralRecordService;
    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private FinaCardRechargeService finaCardRechargeService;
    @Inject
    private FinaCardTransactionsService finaCardTransactionsService;
    @Inject
	private FinaExpenseRecordDetailService finaExpenseRecordDetailService;

    @Override
    public void run() {
    	try {
			cardIntegralsSettleGen();
		}catch (Exception e){
    		e.printStackTrace();
    		logger.error("会员卡每天积分结算定时任务", e);
		}

    	try {
    		//处理有积分的情况下锁定天数的情况
			finaExpenseRecordDetailService.allocationIntegral();
		}catch (Exception e){
    		e.printStackTrace();
    		logger.error("处理有积分的情况下锁定天数的情况", e);
		}

    }

    public void cardIntegralsSettleGen(){
    	List<Record> memberCardList = finaMembershipCardService.findListWithCardType();
    	logger.info("积分会员卡记录-积分："+memberCardList.size()+"条。");
		for(Record memberCard : memberCardList) {
			final String memmberCardId = memberCard.getStr("id");//会员卡id
			final String cardNum = memberCard.getStr("card_number");//会员卡号
			final String cardTypeId = memberCard.getStr("card_type_id");//会员卡类型id
			final Date openTime = memberCard.getDate("open_time");//会员卡开卡时间
			Double consumeTimes = memberCard.getDouble("consume_times");//剩余天数
			Double giveDays = memberCard.getDouble("give_recharge_days");//赠送天数
			final Double cardIntegrals = memberCard.getDouble("card_integrals")!=null?memberCard.getDouble("card_integrals"):0;//剩余积分
			final String cardCreateTimeStr = DateUtils.formatDate(memberCard.getDate("create_time"));
			
			if(openTime!=null) {//有开卡时间才执行
				Date lastDate = new Date();
				double totalCardIntegral = 0.0;
				Date nowDate = DateUtils.parseDate(DateUtils.getDate());
				//把合同赠送天数扣减出来
				if(consumeTimes!=null && consumeTimes>0 && giveDays!=null && giveDays>0){
					consumeTimes = consumeTimes - giveDays;
				}
				FinaCardIntegralRecord cardIntegralRecord = finaCardIntegralRecordService.getByCardIdAndGiveDateDescFirst(memmberCardId);
				logger.info("cardNum-give="+cardNum);
				logger.info("cardIntegrals-give="+cardIntegrals);
				if(cardIntegralRecord!=null) {
					final Date giveDate = cardIntegralRecord.getGiveDate();
					lastDate = DateUtils.getNextDay(giveDate, 1);
				}else {
					lastDate = DateUtils.parseDate(DateUtils.formatDate(openTime));
				}

				int compareNum = DateUtils.compareDays(lastDate, nowDate);
				while (compareNum<0) {
//					logger.info("lastDate-give1="+DateUtils.formatDateTime(lastDate));
//					logger.info("nowDate-give1="+DateUtils.formatDateTime(nowDate));
//					logger.info("compareNum-give1="+compareNum);
					FinaCardIntegralRecord integralRecord = finaCardIntegralRecordService.getByCardIdAndGiveDate(memmberCardId, lastDate);
					if(integralRecord==null) {
						//查找剩余天数所在的计算比例
						double pValue = 0.0;
						double giveIntegralValue5 = 0.0;
						if(consumeTimes!=null && consumeTimes>0) {
							List<MainCardTypeConfig> configList = mainCardTypeConfigService.findListByTypeIdAndValue(cardTypeId, consumeTimes);
							if(configList!=null && configList.size()>0) {
								pValue = configList.get(0).getProportionValue();
//							logger.info("正常计算比例-give："+df.format(pValue));
							}
							final double giveIntegralValue = consumeTimes*pValue;
							giveIntegralValue5 = new BigDecimal(giveIntegralValue).setScale(5, BigDecimal.ROUND_DOWN).doubleValue();
//						logger.info("giveIntegralValue-give="+giveIntegralValue);
//						logger.info("giveIntegralValue(5位)-give="+giveIntegralValue5);
							totalCardIntegral+=giveIntegralValue5;
						}
						FinaCardIntegralRecord tempRecord = new FinaCardIntegralRecord();
						tempRecord.setId(IdGen.getUUID());
						tempRecord.setCardId(memmberCardId);
						tempRecord.setTransactionType("in");
						tempRecord.setBusinessType("system_give");
						tempRecord.setGiveDate(lastDate);
						tempRecord.setCountValue(consumeTimes);
						tempRecord.setProportionValue(pValue);
						tempRecord.setIntegralValue(giveIntegralValue5);
						tempRecord.setDelFlag("0");
						tempRecord.setCreateTime(new Date());
						finaCardIntegralRecordService.save(tempRecord);
//						logger.info("积分记录保存返回ID-give："+returnObj.toString());
					}
					lastDate = DateUtils.getNextDay(lastDate, 1);
					compareNum = DateUtils.compareDays(lastDate, nowDate);
//					logger.info("lastDate-give2="+DateUtils.formatDateTime(lastDate));
//					logger.info("nowDate-give2="+DateUtils.formatDateTime(nowDate));
//					logger.info("compareNum-give2="+compareNum);
				}
				
				//总积分
				double totalJF = cardIntegrals+totalCardIntegral;
//				logger.info("totalJF="+totalJF);
				
				//查询未计算积分的充值记录
				List<FinaCardRecharge> isNotCountRechargeList = finaCardRechargeService.findIsNotCountList(memmberCardId);
				logger.info("积分会员卡充值记录："+isNotCountRechargeList.size()+"条!!!");
				for(FinaCardRecharge recharge : isNotCountRechargeList) {
					final String rechargeId = recharge.getId();
					Date rechargeTime = recharge.getRechargeTime();
					final Double rechargeConsumeTimes = recharge.getConsumeTimes();
					final String rechargeUpdateTimeStr = DateUtils.formatDate(recharge.getUpdateTime(), "yyyy-MM-dd");
					if(rechargeTime!=null && rechargeConsumeTimes!=null && rechargeConsumeTimes>0) {
						// 开卡创建时间和充值审核更新时间不在同一天，才需要重新计算充值天数积分
						if(!cardCreateTimeStr.equals(rechargeUpdateTimeStr)) {
							final Date updateTime = DateUtils.parseDate(rechargeUpdateTimeStr);
							//充值时间少于等于当天创建时间的都要把充值天数加入重新计算并更新
							int compareNumR = DateUtils.compareDays(rechargeTime, updateTime);
							while (compareNumR<0) {
//								logger.info("rechargeTime-recharge="+DateUtils.formatDate(rechargeTime));
//								logger.info("updateTime-recharge="+DateUtils.formatDate(updateTime));
//								logger.info("compareNum-recharge："+compareNumR);
								FinaCardIntegralRecord integralRecord = finaCardIntegralRecordService.getByCardIdAndGiveDate(memmberCardId, rechargeTime);
								if(integralRecord!=null) {
									double proportionValue = 0.0;
									final double oldIntegralValue = integralRecord.getIntegralValue();
//									logger.info("oldIntegralValue-recharge="+oldIntegralValue);
									final double countValue = integralRecord.getCountValue()+rechargeConsumeTimes;
									List<MainCardTypeConfig> configList = mainCardTypeConfigService.findListByTypeIdAndValue(cardTypeId, countValue);
									if(configList!=null && configList.size()>0) {
										proportionValue = configList.get(0).getProportionValue();
//										logger.info("充值计算比例-recharge："+df.format(proportionValue)+"充值日期===>>>"+DateUtils.formatDate(rechargeTime));
									}
									final double giveIntegralValue = countValue*proportionValue;
									final double giveIntegralValue5 = new BigDecimal(giveIntegralValue).setScale(5, BigDecimal.ROUND_DOWN).doubleValue();
//									logger.info("giveIntegralValue-recharge="+giveIntegralValue);
//									logger.info("giveIntegralValue(5位)-recharge="+giveIntegralValue5);
									totalJF -= oldIntegralValue;//先减去旧积分
									totalJF += giveIntegralValue5;//再加上新计算积分
									
									FinaCardIntegralRecord tempIntegralRecord = new FinaCardIntegralRecord();
									tempIntegralRecord.setId(integralRecord.getId());
									tempIntegralRecord.setCountValue(countValue);
									tempIntegralRecord.setProportionValue(proportionValue);
									tempIntegralRecord.setIntegralValue(giveIntegralValue5);
									tempIntegralRecord.setUpdateTime(new Date());
									finaCardIntegralRecordService.update(tempIntegralRecord);
//									logger.info("积分赠送记录-更新标识-recharge："+flag);
								}
								rechargeTime = DateUtils.getNextDay(rechargeTime, 1);
								compareNumR = DateUtils.compareDays(rechargeTime, updateTime);
//								logger.info("rechargeTime-recharge2="+DateUtils.formatDate(rechargeTime));
//								logger.info("updateTime-recharge2="+DateUtils.formatDate(updateTime));
//								logger.info("compareNum-recharge2："+compareNumR);
							}
						}
						FinaCardRecharge tempRecharge = new FinaCardRecharge();
						tempRecharge.setId(rechargeId);
						tempRecharge.setIsCount("1");
						finaCardRechargeService.update(tempRecharge);
//						logger.info("会员卡-充值记录-更新标识："+flag);
					}
				}
				FinaMembershipCard card = new FinaMembershipCard();
				card.setId(memmberCardId);
				card.setCardIntegrals(totalJF);
				finaMembershipCardService.update(card);
//				logger.info("会员卡更新标识："+flag);
			}
		}
    }



}
