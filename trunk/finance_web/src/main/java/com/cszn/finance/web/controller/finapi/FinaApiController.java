package com.cszn.finance.web.controller.finapi;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.finance.web.controller.fina.FinaSettleDetailController;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.cron.CardMonthBalanceTask;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.common.RetApi;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.PicUtils;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.cfs.CfsFileUploadService;
import com.cszn.integrated.service.api.crm.CrmCardRollRecordService;
import com.cszn.integrated.service.api.crm.CrmCardRollService;
import com.cszn.integrated.service.api.fina.FinaCardAccountService;
import com.cszn.integrated.service.api.fina.FinaCardDeductService;
import com.cszn.integrated.service.api.fina.FinaCardRechargeService;
import com.cszn.integrated.service.api.fina.FinaCardTransactionsService;
import com.cszn.integrated.service.api.fina.FinaConsumeRecordService;
import com.cszn.integrated.service.api.fina.FinaContractRecordService;
import com.cszn.integrated.service.api.fina.FinaDayDeductStatisticService;
import com.cszn.integrated.service.api.fina.FinaExpenseCashRecordService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordContinueService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordDeductionCardService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordDetailService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordFollowService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordTouristService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordTouristSettleDetailService;
import com.cszn.integrated.service.api.fina.FinaInvoiceService;
import com.cszn.integrated.service.api.fina.FinaLeaseRecordApplyService;
import com.cszn.integrated.service.api.fina.FinaLeaseRecordRoomPaymentService;
import com.cszn.integrated.service.api.fina.FinaMemberSettleMainService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardTypeConfService;
import com.cszn.integrated.service.api.fina.FinaNoticeCardChangeService;
import com.cszn.integrated.service.api.fina.FinaPunishRecordService;
import com.cszn.integrated.service.api.fina.FinaRefundAccountService;
import com.cszn.integrated.service.api.fina.FinaRefundApplyService;
import com.cszn.integrated.service.api.fina.FinaRefundCardRecordService;
import com.cszn.integrated.service.api.fina.FinaRefundCardService;
import com.cszn.integrated.service.api.fina.FinaSettleDetailService;
import com.cszn.integrated.service.api.fina.FinaTaskService;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.api.main.MainBaseBedService;
import com.cszn.integrated.service.api.main.MainBaseGoodConfigsService;
import com.cszn.integrated.service.api.main.MainBaseRoomService;
import com.cszn.integrated.service.api.main.MainBaseRoomTypeService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.main.MainCardGiveSchemeService;
import com.cszn.integrated.service.api.main.MainCardYearLimitService;
import com.cszn.integrated.service.api.main.MainGoodModelsService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.main.MainRoomTypeService;
import com.cszn.integrated.service.api.member.MmsCustomerFaceService;
import com.cszn.integrated.service.api.member.MmsFaceLiveUploadService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.pers.PersOrgReviewerService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.weixin.WeiXinActivityService;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.crm.CrmCardRoll;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecord;
import com.cszn.integrated.service.entity.enums.AccountRecordOperateType;
import com.cszn.integrated.service.entity.enums.CheckinType;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.enums.FollowDeductType;
import com.cszn.integrated.service.entity.enums.RechargeWay;
import com.cszn.integrated.service.entity.enums.SchemeType;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.fina.FinaBaseGoodOrder;
import com.cszn.integrated.service.entity.fina.FinaBaseGoodOrderItem;
import com.cszn.integrated.service.entity.fina.FinaCardAccount;
import com.cszn.integrated.service.entity.fina.FinaCardCollect;
import com.cszn.integrated.service.entity.fina.FinaCardDeduct;
import com.cszn.integrated.service.entity.fina.FinaCardRecharge;
import com.cszn.integrated.service.entity.fina.FinaCardRoll;
import com.cszn.integrated.service.entity.fina.FinaCardSpendCash;
import com.cszn.integrated.service.entity.fina.FinaCardTransactions;
import com.cszn.integrated.service.entity.fina.FinaCardTransactionsDetail;
import com.cszn.integrated.service.entity.fina.FinaConsumeRecord;
import com.cszn.integrated.service.entity.fina.FinaContractRecord;
import com.cszn.integrated.service.entity.fina.FinaExpenseCashRecord;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordContinue;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordDetail;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordFile;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordFollow;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordRoll;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordTourist;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordTouristDetail;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecordTouristSettleDetail;
import com.cszn.integrated.service.entity.fina.FinaInvoice;
import com.cszn.integrated.service.entity.fina.FinaLeaseRecordApply;
import com.cszn.integrated.service.entity.fina.FinaLeaseRecordRoomPayment;
import com.cszn.integrated.service.entity.fina.FinaMemberBillDetail;
import com.cszn.integrated.service.entity.fina.FinaMemberSettleMain;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaMembershipCardTypeConf;
import com.cszn.integrated.service.entity.fina.FinaPunishRecord;
import com.cszn.integrated.service.entity.fina.FinaRefundAccount;
import com.cszn.integrated.service.entity.fina.FinaRefundApply;
import com.cszn.integrated.service.entity.fina.FinaRefundCard;
import com.cszn.integrated.service.entity.fina.FinaRefundCardRecord;
import com.cszn.integrated.service.entity.fina.FinaSettleDetail;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBaseBed;
import com.cszn.integrated.service.entity.main.MainBaseGoodConfigs;
import com.cszn.integrated.service.entity.main.MainBaseRoom;
import com.cszn.integrated.service.entity.main.MainBaseRoomType;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainCardGiveScheme;
import com.cszn.integrated.service.entity.main.MainCardYearLimit;
import com.cszn.integrated.service.entity.main.MainGoodModels;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgReviewer;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.ReturnCardStatus;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.integrated.service.entity.sys.User;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.xiaoleilu.hutool.date.DatePattern;
import com.xiaoleilu.hutool.date.DateUtil;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import io.jboot.web.cors.EnableCORS;

/**
 * @Description 财务第三方接口Controller
 * <AUTHOR>
 * @Date 2019/6/17
 **/
/**
 * <AUTHOR>
 *
 * 2024年2月20日
 */
@JCors
@RequestMapping(value="/api", viewPath="")
public class FinaApiController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(FinaApiController.class);

    @Inject
    private DictService dictService;
    @Inject
    private FinaConsumeRecordService finaConsumeRecordService;
    @Inject
    private FinaDayDeductStatisticService finaDayDeductStatisticService;
    @Inject
    private MainAppJoinService mainAppJoinService;
    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private FinaExpenseRecordFollowService finaExpenseRecordFollowService;
    @Inject
    private FinaExpenseRecordContinueService finaExpenseRecordContinueService;
    @Inject
    private FinaExpenseRecordDetailService finaExpenseRecordDetailService;
    @Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;
    @Inject
    private FinaExpenseRecordTouristService finaExpenseRecordTouristService;
    @Inject
    private MainCardYearLimitService mainCardYearLimitService;
    @Inject
    private FinaPunishRecordService finaPunishRecordService;
    @Inject
    private FinaMemberSettleMainService finaMemberSettleMainService;
    @Inject
    private MmsFaceLiveUploadService mmsFaceLiveUploadService;
    @Inject
    private FinaExpenseRecordDeductionCardService finaExpenseRecordDeductionCardService;
    @Inject
    private FinaNoticeCardChangeService finaNoticeCardChangeService;
    @Inject
    private FinaCardTransactionsService finaCardTransactionsService;
    @Inject
    private FinaInvoiceService finaInvoiceService;
    @Inject
    private FinaExpenseCashRecordService finaExpenseCashRecordService;
    @Inject
    private MainBaseRoomService mainBaseRoomService;
    @Inject
    private FinaCardAccountService finaCardAccountService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private FinaMembershipCardTypeConfService finaMembershipCardTypeConfService;
    @Inject
    private FinaCardRechargeService finaCardRechargeService;
    @Inject
	private FinaCardDeductService finaCardDeductService;
    @Inject
    private MainBaseBedService mainBaseBedService;
    @Inject
    private MainRoomTypeService mainRoomTypeService;
    @Inject
    private SmsSendRecordService smsSendRecordService;
    @Inject
    private MmsMemberService mmsMemberService;
    @Inject
    private FinaRefundAccountService finaRefundAccountService;
    @Inject
    private FinaRefundApplyService finaRefundApplyService;
    @Inject
    private FinaRefundCardRecordService finaRefundCardRecordService;
    @Inject
    private FinaRefundCardService finaRefundCardService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersOrgReviewerService persOrgReviewerService;
    @Inject
    private CrmCardRollRecordService crmCardRollRecordService;
    @Inject
    private FinaSettleDetailService finaSettleDetailService;
    @Inject
    private CfsFileUploadService cfsFileUploadService;
    @Inject
    private UserService userService;
    @Inject
    private WeiXinActivityService weiXinActivityService;
    @Inject
    private FinaContractRecordService finaContractRecordService;
    @Inject
    private MainBaseRoomTypeService mainBaseRoomTypeService;
    @Inject
    private FinaExpenseRecordTouristSettleDetailService finaExpenseRecordTouristSettleDetailService;
    @Inject
    private FinaTaskService finaTaskService;
    @Inject
    MainCardGiveSchemeService mainCardGiveSchemeService;
    @Inject
    FinaLeaseRecordRoomPaymentService finaLeaseRecordRoomPaymentService;
    @Inject
    private MmsCustomerFaceService mmsCustomerFaceService;
    @Inject
    private CrmCardRollService crmCardRollService;

    /**
     * 会员卡消费
     */
    @Clear(LogInterceptor.class)
    public void cardConsume() {
        logger.info("旅居调用会员卡消费begin...");
        Map<String, Object> result = new HashMap<String, Object>();
        String ipAddr = IdGen.getIpAddress(getRequest());
        String appNo = getPara("appNo");
        String orderNo = getPara("orderNo");
        String baseId = getPara("baseId");//新加
        String cardNumber = getPara("cardNumber");
        String describe = getPara("describe");
        String consumeType = getPara("consumeType");//新加
        String checkinId = getPara("checkinId");//新加
        String checkinType = getPara("checkinType");//新加
        String consumeSource = getPara("consumeSource");
        logger.info("发送请求IP地址：[{}]", ipAddr);
        logger.info("应用编号：[{}],订单编号：[{}],基地ID：[{}],会员卡：[{}],消费描述：[{}],消费类型：[{}],入住ID:[{}]", appNo, orderNo, baseId, cardNumber, describe, consumeType, checkinId);
        logger.info("入住类型：[{}]，消费金额：[{}]，消费天数：[{}]，消费时间：[{}]", getPara("checkinType"), getPara("amount"), getPara("consumeTimes"), getPara("takeTime"));
        if (StringUtils.isBlank(appNo)) {
            result.put("code", "10001");
            result.put("msg", "应用编号不能为空");
            renderJson(result);
            return;
        }
        if (StringUtils.isBlank(orderNo)) {
            result.put("code", "10001");
            result.put("msg", "订单号不能为空");
            renderJson(result);
            return;
        }
        if (StringUtils.isBlank(cardNumber)) {
            result.put("code", "10001");
            result.put("msg", "卡号不能为空");
            renderJson(result);
            return;
        }
        if (StringUtils.isBlank(baseId)) {
            result.put("code", "10001");
            result.put("msg", "基地ID不能为空");
            renderJson(result);
            return;
        }
        if (StringUtils.isBlank(cardNumber)) {
            result.put("code", "10001");
            result.put("msg", "会员卡号不能为空");
            renderJson(result);
            return;
        }
        //判断应用编号
        MainAppJoin appFlag = mainAppJoinService.isHaveAppNo(appNo);
        if (appFlag == null) {
            result.put("code", "10001");
            result.put("msg", "系统内不存在该应用编号或应用编号已禁用");
            renderJson(result);
            return;
        }
        //判断ip地址
        ipAddr = IdGen.dealIpAddr(ipAddr);
        logger.info("处理之后的ip地址：[{}]", ipAddr);
        if (StringUtils.isNotBlank(appFlag.getIpAddr())) {
            String[] arr = appFlag.getIpAddr().split(",");
            logger.info("应用IP数组：[{}]", JSON.toJSONString(arr));
            int flag = 0;
            for (String item : arr) {
                if (ipAddr.equals(item)) {
                    flag = 1;
                }
            }
            if (flag == 0) {
                result.put("code", "10001");
                result.put("msg", "该IP不允许上传数据");
                renderJson(result);
                return;
            }
        }
        //判断基地id
        baseId = baseId.toUpperCase();
        if (!baseId.contains("-")) {
            baseId = baseId.substring(0, 8) + "-" + baseId.substring(8, 12) + "-" + baseId.substring(12, 16) + "-" + baseId.substring(16, 20) + "-" + baseId.substring(20);
        }
        MainBase base = mainBaseService.get(baseId);
        if (base == null) {
            result.put("code", "10001");
            result.put("msg", "系统内不存在该基地");
            renderJson(result);
            return;
        }
        //判断订单
        Long count = Db.queryLong("select count(*) from fina_consume_record where del_flag = 0 and app_order_no = ?", orderNo);
        if (count > 0) {
            result.put("code", "10001");
            result.put("msg", "系统内已存在该订单号");
            renderJson(result);
            return;
        }

        double amount = 0.00;
        if (StringUtils.isNotBlank(getPara("amount"))) {
            amount = Double.valueOf(getPara("amount"));
        }
        Integer consumeTimes = 0;
        if (StringUtils.isNotBlank(getPara("consumeTimes"))) {
            consumeTimes = getParaToInt("consumeTimes");
        }
        String takeTime = getPara("takeTime");
        if (StringUtils.isBlank(takeTime)) {
            result.put("code", "10001");
            result.put("msg", "消费时间不能为空");
            renderJson(result);
            return;
        }
        if (StringUtils.isBlank(consumeType)) {
            result.put("code", "10001");
            result.put("msg", "消费类型不能为空");
            renderJson(result);
            return;
        }
        List<Dict> list = dictService.getListByTypeOnUse("account_record_operate_type");
        if (list != null && list.size() > 0) {
            int flag = 0;
            for (Dict item : list) {
                if (StringUtils.isNotBlank(item.getDictValue()) && consumeType.equals(item.getDictValue())) {
                    flag = 1;
                }
            }
            if (flag == 0) {
                result.put("code", "10001");
                result.put("msg", "系统不存在该消费类型");
                renderJson(result);
                return;
            }
        }
        String details = getPara("details");
        FinaConsumeRecord consume = new FinaConsumeRecord();
        consume.setId(IdGen.getUUID());
        consume.setAppNo(appNo);
        consume.setAppOrderNo(orderNo);
        consume.setBaseId(baseId);
        consume.setCardNumber(cardNumber);
        consume.setDescribe(describe);
        consume.setAmount(amount);
        consume.setConsumeTimes(Double.valueOf(consumeTimes));
        consume.setTakeTime(DateUtil.parse(takeTime));
        consume.setConsumeNo(IdGen.getSerialNumber());
        consume.setConsumeSource(consumeSource);
        consume.setConsumeType(consumeType);//消费类型
        consume.setCheckinId(checkinId);//旅居入住id
        consume.setCheckinType(checkinType);//入住类型
        consume.setStatus("0");//未处理
        consume.setDelFlag("0");

        consume.setCreateTime(DateUtil.date());
        consume.setUpdateTime(DateUtil.date());
        logger.info("旅居调用会员卡待处理数据：[{}]", JSON.toJSONString(consume));

        //region Description
        /*JSONArray detailArr = JSONArray.parseArray(details);
        if(detailArr != null && detailArr.size() > 0) {
            for (int i = 0; i < detailArr.size(); i++) {
                JSONObject json = detailArr.getJSONObject(i);

                double jsonAmount = 0.00;
                if(json.getDouble("amount") != null){
                    jsonAmount = json.getDouble("amount");
                }

                Integer jsonConsumeTimes = 0;
                if(json.getInteger("consumeTimes") != null) {
                    jsonConsumeTimes = json.getInteger("consumeTimes");
                }

                if(StringUtils.isBlank(json.getString("takeTime"))){
                    result.put("code", "10001");
                    result.put("msg", "消费时间不能为空");
                    renderJson(result);
                    return;
                }

                FinaConsumeRecordDetail detail = new FinaConsumeRecordDetail();
                detail.setId(IdGen.getUUID());
                detail.setConsumeId(consume.getId());
                detail.setAppDetailNo(json.getString("detailNo"));
                detail.setDescribe(json.getString("describe"));
                detail.setAmount(jsonAmount);
                detail.setConsumeTimes(jsonConsumeTimes);
                detail.setTakeTime(DateUtil.parse(json.getString("takeTime")));
                detail.setCreateTime(new Date());
                detail.setUpdateTime(new Date());
                detail.save();
            }
        }*/
        //endregion

        Boolean suc = consume.save();
        if (suc) {
            result.put("code", "0");
            result.put("msg", "保存成功");
            result.put("consumeNo", consume.getConsumeNo());
        } else {
            result.put("code", "10001");
            result.put("msg", "保存失败");
        }
        logger.info("旅居调用会员卡消费返回数据：[{}]", JSON.toJSONString(result));
        logger.info("旅居调用会员卡消费end...");
        renderJson(result);
    }


    /**
     * 会员卡退费  未确定
     */
    @Clear(LogInterceptor.class)
    public void cardRefund() {

        String consumeNo = getPara("consumeNo");
        String describe = getPara("describe");
        double amount = 0.00;
        if (StringUtils.isNotBlank(getPara("amount"))) {
            amount = Double.valueOf(getPara("amount"));
        }
        int consumeTimes = 0;
        if (StringUtils.isNotBlank(getPara("consumeTimes"))) {
            consumeTimes = getParaToInt("consumeTimes");
        }
        Map<String, Object> result = new HashMap<String, Object>();
        if (amount == 0.00 && consumeTimes == 0) {
            result.put("code", "10001");
            result.put("msg", "退费金额和天数不能都为空");
            renderJson(result);
            return;
        }

        FinaConsumeRecord consume = new FinaConsumeRecord();
        consume.setId(IdGen.getUUID());
        consume.setConsumeNo(IdGen.getSerialNumber());//退费编号
        consume.setDescribe(describe);
        consume.setAmount(amount);
        consume.setConsumeTimes(Double.valueOf(consumeTimes));
        consume.setRefundFlag("1");
        consume.setRefundUnionNo(consumeNo);
        consume.setTakeTime(new Date());
        consume.setStatus("0");
        consume.setDelFlag("0");
        consume.setCreateTime(new Date());
        consume.setUpdateTime(new Date());
        if (consume.save()) {
            result.put("code", "0");
            result.put("msg", "成功");
            result.put("consumeNo", consume.getConsumeNo());
        } else {
            result.put("code", "10001");
            result.put("msg", "失败");
        }
        renderJson(result);
    }


    /**
     * 按时间获取扣卡统计（用于图表显示）
     */
    @Clear(LogInterceptor.class)
    public void deductionStatistics() {
        String timeType = getPara("timeType");
        Map<String, Object> result = new HashMap<String, Object>();
        if (StringUtils.isBlank(timeType)) {
            result.put("code", "10001");
            result.put("msg", "时间类型为空");
            renderJson(result);
            return;
        }

        Map<String, Object> map = finaDayDeductStatisticService.getChartData(timeType, null);
        if (map == null) {
            result.put("code", "0");
            result.put("msg", "0");
            result.put("timeCount", 0);
            result.put("amountCount", 0);
            result.put("data", new ArrayList<>());
        } else {
            result.put("code", "0");
            result.put("msg", "0");
            result.put("timeCount", map.get("timeCount"));
            result.put("amountCount", map.get("amountCount"));
            result.put("data", map.get("list"));
        }
        renderJson(result);
    }


    /**
     * 获取按时间基地统计的扣卡数据
     */
    @Clear(LogInterceptor.class)
    public void deductionStatisticsForBase() {
        String timeType = getPara("timeType");
        Map<String, Object> result = new HashMap<String, Object>();
        if (StringUtils.isBlank(timeType)) {
            result.put("code", "10001");
            result.put("msg", "时间类型为空");
            renderJson(result);
            return;
        }

        Map<String, Object> map = finaDayDeductStatisticService.getChartDataForBase(timeType, null);
        if (map == null) {
            result.put("code", "0");
            result.put("msg", "0");
            result.put("data", new ArrayList<>());
        } else {
            result.put("code", "0");
            result.put("msg", "0");
            result.put("data", map.get("list"));
        }
        renderJson(result);
    }


    /**
     * 通知财务系统客户预订成功
     * 预订：即新增账单主记录
     */
    @Clear(LogInterceptor.class)
    public void expenseForBook() {

        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String officeId = getPara("officeId");
        String businessEntityId=getPara("businessEntityId");
        String checkinType=getPara("checkinType");
        String customerChannel=getPara("customerChannel");
        String bookData = getPara("bookData");
        String createBy=getPara("createBy");
        logger.info("预订接收参数：应用号：[{}],基地：[{}],分公司id:[{}],预订数组:[{}],业务实体id:[{}],客户渠道:[{}],提交人:[{}]", appNo, baseId, officeId, bookData,businessEntityId,customerChannel,createBy);
        finaExpenseRecordService.saveApiRecord("预订", appNo, baseId, officeId, null, bookData);

        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用编号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地ID为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(bookData)) {
                result.put("code", "10002");
                result.put("msg", "预订数组为空");
                renderJson(result);
                return;
            }
            Map<String, Object> map = finaExpenseRecordService.sojournBook(appNo, baseId, officeId,businessEntityId,checkinType,customerChannel, bookData,createBy);
            logger.info("处理后的返回结果：[{}]", JSON.toJSONString(map));
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }

            List<FinaExpenseRecord> erList = (List<FinaExpenseRecord>) map.get("erList");
            List<FinaExpenseRecordFollow> ffList = (List<FinaExpenseRecordFollow>) map.get("ffList");
            List<FinaExpenseRecordFile> fileList=(List<FinaExpenseRecordFile>)map.get("fileList");
            List<FinaExpenseRecordRoll> rollList=(List<FinaExpenseRecordRoll>)map.get("rollList");

            boolean flag=false;
            synchronized (erList.get(0).getCardNumber().intern()){
                //生成明细
                Map<String, Object> detailMap = finaExpenseRecordService.getDetailsByErListBook(erList, null);
                if (detailMap.containsKey("error")) {
                    result.put("code", "10002");
                    result.put("msg", detailMap.get("error"));
                    renderJson(result);
                    return;
                }
                List<List<FinaExpenseRecordDetail>> sonList = (List<List<FinaExpenseRecordDetail>>) detailMap.get("sonList");
                for (FinaExpenseRecord expenseRecord : erList) {
                    for (List<FinaExpenseRecordDetail> item : sonList) {
                        for (FinaExpenseRecordDetail detail : item) {
                            if(expenseRecord.getId().equalsIgnoreCase(detail.getExpenseId()) && "1".equals(expenseRecord.getIsNotDeductCard())){
                                detail.setTimes(0.0);
                                detail.setAmount(0.0);
                                detail.setIntegrals(0.0);
                            }
                        }
                    }
                }

                flag = Db.tx(new IAtom() {
                    public boolean run() throws SQLException {
                        try {
                            int[] ints = Db.batchSave(erList, erList.size());
                            for (int i : ints) {
                                if (i < 1) {
                                    return false;
                                }
                            }
                        /*for (List<FinaExpenseRecordDetail> item : sonList) {
                            int[] intList = Db.batchSave(item, item.size());
                            for (int i : intList) {
                                if (i < 1) {
                                    return false;
                                }
                            }
                        }*/
                            if(ffList!=null && ffList.size()>0){
                                int[] ffNums=Db.batchSave(ffList,ffList.size());
                                for(int i:ffNums){
                                    if (i < 1) {
                                        return false;
                                    }
                                }
                            }
                            if(fileList!=null && fileList.size()>0){
                                Db.batchSave(fileList,fileList.size());
                            }
                            if(rollList!=null && rollList.size()>0){
                                Db.batchSave(rollList,rollList.size());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            return false;
                        }
                        return true;
                    }
                });

                if(flag){
                    Global.executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            for (List<FinaExpenseRecordDetail> item : sonList) {
                                int[] intList = Db.batchSave(item, item.size());
                            }
                        }
                    });
                }
            }



            //统计会员卡锁定和扣卡汇总
            //boolean sucLock = false;
            /*if (flag) {
                sucLock = finaExpenseRecordService.getCardLock(erList, 1, null);
            }*/

            List<Record> list = new ArrayList<>();
            for (FinaExpenseRecord item : erList) {
                Record record = new Record();
                if (StringUtils.isNotBlank(item.getBookNo())) {
                    record.set("bookNo", item.getBookNo());
                    record.set("result", flag);
                }
                list.add(record);
            }

            if (flag) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            result.put("data", list);
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统客户预订异常:[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户预订发生异常:" + e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 通知财务系统客户入住成功
     * 随行人员
     * 根据预订号更新已预订主账单，预订号无则直接新增主账单记录
     * 直接入住数据无预定开始结束时间？
     * 生成随行记录
     * 若有初始化计费时间则根据该时间计费
     */
    @Clear(LogInterceptor.class)
    public void expenseForCheckin() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String officeId = getPara("officeId");
        String businessEntityId=getPara("businessEntityId");
        String checkinType=getPara("checkinType");
        String customerChannel=getPara("customerChannel");
        String initialDate = getPara("initialDate");//初始化计费时间，用于统一处理已入住系统尚未发布的情况
        String checkinData = getPara("checkinData");
        String createBy=getPara("createBy");
        logger.info("正常入住接收数据：应用号:[{}],基地ID:[{}],分公司id:[{}],计费时间:[{}],入住数组:[{}],业务实体id:[{}],客户渠道id:[{}],提交人:[{}]"
                , appNo, baseId, officeId, initialDate, checkinData,businessEntityId,customerChannel,createBy);
        finaExpenseRecordService.saveApiRecord("入住", appNo, baseId, officeId, initialDate, checkinData);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用编号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地ID为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(checkinData)) {
                result.put("code", "10002");
                result.put("msg", "入住数组为空");
                renderJson(result);
                return;
            }

            //正常入住
            JSONArray jsonArray = JSONArray.parseArray(checkinData);
            synchronized (jsonArray.getJSONObject(0).getString("cardNumber").intern()){
                Map<String, Object> listMap = finaExpenseRecordService
                        .getErSaveAndUpdateListAndFfListByParams(appNo, baseId, officeId, initialDate,businessEntityId,checkinType,customerChannel, jsonArray,createBy);
                logger.info("处理后生成的入住预订随行人员账单数据:[{}]", JSON.toJSONString(listMap));
                if (listMap.containsKey("error")) {
                    result.put("code", "10002");
                    result.put("msg", listMap.get("error"));
                    renderJson(result);
                    return;
                }

                List<FinaExpenseRecord> erList = (List<FinaExpenseRecord>) listMap.get("erList");
                List<FinaExpenseRecord> saveErList = (List<FinaExpenseRecord>) listMap.get("saveErList");
                List<FinaExpenseRecord> updateErList = (List<FinaExpenseRecord>) listMap.get("updateErList");
                List<FinaExpenseRecordFollow> ffList = (List<FinaExpenseRecordFollow>) listMap.get("ffList");
                List<FinaExpenseRecordFollow> delFflist=(List<FinaExpenseRecordFollow>) listMap.get("delFflist");

                //判断随行人员重复
                Map<String, Object> repeatMap = finaExpenseRecordService.dealFollowRepeat(appNo, baseId, ffList);
                if (repeatMap.containsKey("error")) {
                    result.put("code", "10002");
                    result.put("msg", repeatMap.get("error"));
                    renderJson(result);
                    return;
                }
                boolean flag=false;

                //生成明细
                Map<String, Object> detailMap = finaExpenseRecordService.getDetailsByErListCheckin(erList, initialDate);
                if (detailMap.containsKey("error")) {
                    result.put("code", "10002");
                    result.put("msg", detailMap.get("error"));
                    renderJson(result);
                    return;
                }
                List<List<FinaExpenseRecordDetail>> sonList = (List<List<FinaExpenseRecordDetail>>) detailMap.get("sonList");
                //判断会员卡是否扣卡
                for (FinaExpenseRecord expenseRecord : erList) {
                    for (List<FinaExpenseRecordDetail> item : sonList) {
                        for (FinaExpenseRecordDetail detail : item) {
                            if(expenseRecord.getId().equalsIgnoreCase(detail.getExpenseId()) && "1".equals(expenseRecord.getIsNotDeductCard())){
                                detail.setTimes(0.0);
                                detail.setAmount(0.0);
                                detail.setIntegrals(0.0);
                            }
                        }
                    }
                }



                String delDetailSql = (String) listMap.get("delDetailSql");
                List<String> delDetailParam = (List<String>) listMap.get("delDetailParam");

                String delFollowSql=(String) listMap.get("delFollowSql");
                List<String> delFollowParam=(List<String>) listMap.get("delFollowParam");

                flag = Db.tx(new IAtom() {
                    public boolean run() throws SQLException {
                        try {
                            if(delFollowParam.size()>0){
                                int delFollow = Db.delete(delFollowSql, delFollowParam.toArray());
                                if (delFollow < 0) {
                                    return false;
                                }
                            }
                            int[] intUps = Db.batchUpdate(updateErList, updateErList.size());
                            for (int i : intUps) {
                                if (i < 1) {
                                    return false;
                                }
                            }
                            int[] ints = Db.batchSave(saveErList, saveErList.size());
                            for (int i : ints) {
                                if (i < 1) {
                                    return false;
                                }
                            }

                            int[] intFFs = Db.batchSave(ffList, ffList.size());
                            for (int i : intFFs) {
                                if (i < 1) {
                                    return false;
                                }
                            }

                        } catch (Exception e) {
                            e.printStackTrace();
                            return false;
                        }
                        return true;
                    }
                });

                if(flag){
                    Global.executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            for (List<FinaExpenseRecordDetail> item : sonList) {
                                int[] intList = Db.batchSave(item, item.size());
                            }
                        }
                    });
                }
                boolean sucLock = false;
                boolean sucTotal = false;

                List<Record> list = new ArrayList<>();
                for (FinaExpenseRecord item : erList) {
                    Record record = new Record();
                    if (StringUtils.isNotBlank(item.getCheckinNo())) {
                        record.set("checkinNo", item.getCheckinNo());
                        record.set("result", flag /*&& sucLock && sucTotal*/);
                    }
                    list.add(record);
                }
                if (flag /*&& sucLock && sucTotal*/) {
                    result.put("code", "0");
                    result.put("msg", "success");
                } else {
                    result.put("code", "10001");
                    result.put("msg", "fail");
                }
                result.put("data", list);
                renderJson(result);
            }
        } catch (Exception e) {
            logger.error("通知财务系统客户入住异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户入住发生异常:" + e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 通知财务系统客户续住成功
     * 续住通过入住号关联入住主记录
     * 续住携带随行人员
     * 生成续住记录和随行记录
     */
    @Clear(LogInterceptor.class)
    public void expenseForContinue() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String officeId = getPara("officeId");
        String continueData = getPara("continueData");
        logger.info("续住接收数据：应用号:[{}],基地ID:[{}],分公司id:[{}],续住数组:[{}]", appNo, baseId, officeId, continueData);
        finaExpenseRecordService.saveApiRecord("续住", appNo, baseId, officeId, null, continueData);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用编号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地ID为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(continueData)) {
                result.put("code", "10002");
                result.put("msg", "续住数组为空");
                renderJson(result);
                return;
            }

            JSONArray jsonArray = JSONArray.parseArray(continueData);
            Map<String, Object> listMap = finaExpenseRecordService.getErListAndFcAndFfListByParams(appNo, baseId, officeId, jsonArray);
            logger.info("处理后的续住随行主账单数据:[{}]", JSON.toJSONString(listMap));
            if (listMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", listMap.get("error"));
                renderJson(result);
                return;
            }

            List<FinaExpenseRecord> erList = (List<FinaExpenseRecord>) listMap.get("erList");
            List<FinaExpenseRecordContinue> fcList = (List<FinaExpenseRecordContinue>) listMap.get("fcList");
            List<FinaExpenseRecordFollow> ffList = (List<FinaExpenseRecordFollow>) listMap.get("ffList");
            List<FinaExpenseRecord> mainList = (List<FinaExpenseRecord>) listMap.get("mainList");

            //判断续住人员数据重复
            Map<String, Object> repeatCMap = finaExpenseRecordService.dealContinuedRepeat(appNo, baseId, fcList);
            if (repeatCMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", repeatCMap.get("error"));
                renderJson(result);
                return;
            }

            //判断随行人员重复
            Map<String, Object> repeatFMap = finaExpenseRecordService.dealFollowRepeat(appNo, baseId, ffList);
            if (repeatFMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", repeatFMap.get("error"));
                renderJson(result);
                return;
            }

            //生成明细
            Map<String, Object> detailMap = finaExpenseRecordService.getDetailsByErList(erList,"continue");
            logger.info("处理后的消费明细数据:[{}]", JSON.toJSONString(detailMap));
            if (detailMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", detailMap.get("error"));
                renderJson(result);
                return;
            }

            List<List<FinaExpenseRecordDetail>> sonList = (List<List<FinaExpenseRecordDetail>>) detailMap.get("sonList");
            for (FinaExpenseRecord expenseRecord : erList) {
                for (List<FinaExpenseRecordDetail> item : sonList) {
                    for (FinaExpenseRecordDetail detail : item) {
                        if(expenseRecord.getId().equalsIgnoreCase(detail.getExpenseId()) && "1".equals(expenseRecord.getIsNotDeductCard())){
                            detail.setTimes(0.0);
                            detail.setAmount(0.0);
                            detail.setIntegrals(0.0);
                        }
                    }
                }
            }

            boolean flag = Db.tx(new IAtom() {
                public boolean run() throws SQLException {
                    try {
                        int[] ints = Db.batchSave(erList, erList.size());//主记录
                        for (int i : ints) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        int[] fcInts = Db.batchSave(fcList, fcList.size());//续住
                        for (int i : fcInts) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        int[] ffInts = Db.batchSave(ffList, ffList.size());//随行
                        for (int i : ffInts) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        for (List<FinaExpenseRecordDetail> item : sonList) {//明细
                            int[] intList = Db.batchSave(item, item.size());
                            for (int i : intList) {
                                if (i < 1) {
                                    return false;
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return false;
                    }
                    return true;
                }
            });

            boolean sucLock = false;
            boolean sucTotalLock = false;
            boolean sucTotalActual = false;
//            if (flag) {
//                sucLock = finaExpenseRecordService.getCardLock(erList, 1, null);//会员卡锁定
//                if (sucLock) {
//                    sucTotalLock = finaExpenseRecordService.getCardTotalLock(mainList);//汇总锁定
//                    if (sucTotalLock) {
//                        sucTotalActual = finaExpenseRecordService.getCardTotalActual(mainList);//汇总实扣
//                    }
//                }
//            }

            List<Record> recordList = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject json = jsonArray.getJSONObject(i);
                if (StringUtils.isNotBlank(json.getString("checkinNo"))) {
                    Record record = new Record();
                    record.set("checkinNo", json.getString("checkinNo"));
                    record.set("result", flag /*&& sucLock && sucTotalLock && sucTotalActual*/);
                    recordList.add(record);
                }
            }
            if (flag /*&& sucLock && sucTotalLock && sucTotalActual*/) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            result.put("data", recordList);
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统客户续住异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户续住发生异常：" + e.getMessage());
            renderJson(result);
        }
    }

    /**
     * 获取更换会员卡开始时间
     */
    public void getChangeCardStartDate(){
        String appNo=getPara("appNo");
        String baseId=getPara("baseId");
        String checkinNo = getPara("checkinNo");

        Map<String, Object> result = new HashMap<String, Object>();
        result.put("code","10001");

        if(StrKit.isBlank(appNo) || StrKit.isBlank(baseId) || StrKit.isBlank(checkinNo)){
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }
        //获取账单记录，若有续住则获取最大的续住时间
        FinaExpenseRecord er =  finaExpenseRecordService.getErByCheckinNoAndBaseAndApp(appNo,baseId,checkinNo);
        if(er==null){
            result.put("msg","没有查到对应的订单");
            renderJson(result);
            return;
        }
        String minDate="";
        //获取最小时间
        if(StringUtils.isNotBlank(er.getBookNo())){

            Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",er.getId());
            //获取最后一次结算时间
            if(lastSettleDate!=null){
                minDate=TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd");
                result.put("status","1");
            }else {
                //比较预订时间和入住开始时间哪个小
                if(er.getCheckinTime().compareTo(er.getBookStartTime()) >= 0){
                    minDate=TimeUtils.getStrDateByPattern(er.getBookStartTime(),"yyyy-MM-dd");
                }else{
                    minDate=TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd");
                }
                result.put("status","0");
            }

        }else{
            Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",er.getId());
            if(lastSettleDate!=null){
                minDate=TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd");
                result.put("status","1");
            }else{
                minDate=TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd");
                result.put("status","0");
            }

        }
        result.put("code","0");
        result.put("data",minDate);
        result.put("msg","success");
        renderJson(result);
    }


    /**
     * 通知财务系统客户退住成功
     * 具体是退一条记录还是所有？所有
     */
    @Clear(LogInterceptor.class)
    public void expenseForCheckout2() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String checkoutData = getPara("checkoutData");
        logger.info("退住接收参数：应用号:[{}],基地ID:[{}],退住数组:[{}]", appNo, baseId, checkoutData);
        finaExpenseRecordService.saveApiRecord("退住", appNo, baseId, null, null, checkoutData);//日志
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用编号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地ID为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(checkoutData)) {
                result.put("code", "10002");
                result.put("msg", "退住数组为空");
                renderJson(result);
                return;
            }
            boolean flag = false;
            Map<String, Object> map = finaExpenseRecordService.sojournCheckout(appNo, baseId, checkoutData);
            logger.info("处理后的返回结果：[{}]", JSON.toJSONString(map));
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }

            List<Record> list = (List<Record>) map.get("list");
            for (Record item : list) {
                flag = item.getBoolean("result");
                break;
            }
            //统计明细和扣卡汇总
            boolean suc = true;
            if (flag) {
                suc = finaExpenseRecordService.dealCheckoutDetails(list);
            }
            result.put("data", list);
            if (flag && suc) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统客户退住异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户退住发生异常：" + e.getMessage());
            renderJson(result);
        }
    }

    @Clear(LogInterceptor.class)
    public void expenseForCheckout(){
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String checkoutData = getPara("checkoutData");
        logger.info("退住接收参数：应用号:[{}],基地ID:[{}],退住数组:[{}]", appNo, baseId, checkoutData);
        finaExpenseRecordService.saveApiRecord("退住", appNo, baseId, null, null, checkoutData);//日志
        Map<String,Object> resultMap=new HashMap<>();
        try {
            if (StringUtils.isBlank(appNo)) {
                resultMap.put("code", "10002");
                resultMap.put("msg", "应用编号为空");
                renderJson(resultMap);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                resultMap.put("code", "10002");
                resultMap.put("msg", "基地ID为空");
                renderJson(resultMap);
                return;
            }
            if (StringUtils.isBlank(checkoutData)) {
                resultMap.put("code", "10002");
                resultMap.put("msg", "退住数组为空");
                renderJson(resultMap);
                return;
            }
            //获取入住号
            String checkinNo = JSONArray.parseArray(checkoutData).getJSONObject(0).getString("checkinNo");
            synchronized (checkinNo.intern()){
                Map<String,Object> result=finaExpenseRecordService.newCheckout(appNo,baseId,checkoutData);
                if((boolean)result.get("flag")){
                    resultMap.put("code", "0");
                    resultMap.put("msg", "success");
                    resultMap.put("data",result.get("data"));
                    //自动结算
                    try {

                        String expenseId=(String)result.get(checkinNo);
                        List<Record> finaSettleList=finaSettleDetailService.findSettleDetailList(expenseId);
                        for (Record record : finaSettleList) {
                            if("0".equals(record.getStr("settleStatus"))){
                                //获取未结算中创建时间最小的记录
                                String minDateId=Db.queryStr("select id from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' order by end_time limit 1 ",expenseId);
                                if(!minDateId.equals(record.getStr("id"))){
                                    logger.info(checkinNo+"退住自动结算异常：请按照明细生成时间顺序来结算"+record.getStr("id"));
                                    break;
                                }
                                FinaSettleDetail detail=finaSettleDetailService.findById(record.getStr("id"));
                                FinaMembershipCard card = finaMembershipCardService.findCardByCardNumber(detail.getCardNumber());
                                MainCardDeductScheme deductScheme = mainCardDeductSchemeService.findById(card.getDeductSchemeId());
                                if(deductScheme==null){
                                    deductScheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
                                }
                                if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                                    break;
                                }
                                if(detail.getActualAmount()==null){
                                    detail.setActualAmount(0.0);
                                }
                                if(detail.getActualIntegrals()==null){
                                    detail.setActualIntegrals(0.0);
                                }
                                if(detail.getActualTimes()==null){
                                    detail.setActualTimes(0.0);
                                }
                                if(detail.getActualPoints()==null){
                                    detail.setActualPoints(0.0);
                                }
                                FinaExpenseRecord er = finaExpenseRecordService.findById(expenseId);
                                if(!CheckinType.Member.getKey().equals(er.getCheckinType())){
                                    break;
                                }
                                Map<String,Object> map=finaSettleDetailService.settleDateilRecord(
                                        detail,er,record.getStr("settleType"),detail.getActualTimes(),detail.getActualAmount(),
                                        detail.getActualPoints(),detail.getActualIntegrals(),null, "2CFA83C2-CA2E-4B1F-BA1A-1285354C2797"
                                );
                                if((boolean)map.get("flag")){
                                    logger.info(checkinNo+"退住自动结算成功"+record.getStr("id"));
                                }else{
                                    logger.info(checkinNo+"退住自动结算异常"+record.getStr("id")+" "+map.get("msg"));
                                    break;
                                }
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        logger.error(checkinNo+"退住自动结算报错", e);
                    }
                }else{
                    resultMap.put("code", "10001");
                    resultMap.put("msg", (String)result.get("msg"));
                }
            }
        }catch (Exception e){
            logger.error("通知财务系统客户退住异常：[{}]", e);
            resultMap.put("code","10002");
            resultMap.put("msg","通知财务系统客户退住发生异常");
        }
        renderJson(resultMap);
    }


    /**
     * 通知财务系统客户取消预订
     * 已入住不可取消预订
     */
    @Clear(LogInterceptor.class)
    public void expenseForCancelBook() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String bookData = getPara("bookData");
        logger.info("取消预订接收参数：应用号:[{}],基地:[{}],取消预订数组:[{}]", appNo, baseId, bookData);
        finaExpenseRecordService.saveApiRecord("取消预订", appNo, baseId, null, null, bookData);//日志
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用编号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地ID为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(bookData)) {
                result.put("code", "10002");
                result.put("msg", "预订数组为空");
                renderJson(result);
                return;
            }

            Map<String, Object> map = finaExpenseRecordService.sojournCancelBook(appNo, baseId, bookData);
            logger.info("处理后的返回结果:[{}]", JSON.toJSONString(map));
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }

            List<FinaExpenseRecord> erList = (List<FinaExpenseRecord>) map.get("erList");
            List<FinaCardTransactions> tranList = (List<FinaCardTransactions>) map.get("tranList");
            List<Record> recordList = (List<Record>) map.get("recordList");
            List<FinaExpenseRecordDetail> detailList = (List<FinaExpenseRecordDetail>) map.get("detailList");
            Map<String, Record> cardMap = (Map<String, Record>) map.get("cardMap");

            //处理要更新的会员卡
            List<FinaMembershipCard> cardList = new ArrayList<>();
//            Set<String> keys = cardMap.keySet();
//            Iterator<String> cardIterator = keys.iterator();
//            while (cardIterator.hasNext()) {
//                String cardNumber = cardIterator.next();
//                FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);
//                MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
//                if (DeductType.deductTimes.getKey().equals(scheme.getDeductWay())) {
//                    if (card.getConsumeTimes() == null) card.setConsumeTimes(0.00);
//                    if (card.getLockConsumeTimes() == null) card.setLockConsumeTimes(0.00);
//                    Record record = cardMap.get(cardNumber);
//                    card.setConsumeTimes(BigDecimal.valueOf(card.getConsumeTimes()).add(BigDecimal.valueOf(record.getDouble("diffVal"))).doubleValue());
//                    card.setLockConsumeTimes(BigDecimal.valueOf(card.getLockConsumeTimes()).subtract(BigDecimal.valueOf(record.getDouble("lockVal"))).doubleValue());
//                    card.setUpdateTime(new Date());
//                } else if (DeductType.deductAmount.getKey().equals(scheme.getDeductWay())) {
//                    if (card.getBalance() == null) card.setBalance(0.00);
//                    if (card.getLockBalance() == null) card.setLockBalance(0.00);
//                    Record record = cardMap.get(cardNumber);
//                    card.setBalance(BigDecimal.valueOf(card.getBalance()).add(BigDecimal.valueOf(record.getDouble("diffVal"))).doubleValue());
//                    card.setLockBalance(BigDecimal.valueOf(card.getLockBalance()).subtract(BigDecimal.valueOf(record.getDouble("lockVal"))).doubleValue());
//                    card.setUpdateTime(new Date());
//                }
//                cardList.add(card);
//            }

            boolean flag = finaExpenseRecordService.cancelBook(erList, cardList, tranList, detailList);
            for (Record item : recordList) {
                item.set("result", flag);
            }

            if (flag) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            result.put("data", recordList);
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统客户取消预订异常:[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户取消预订发生异常：" + e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 通知财务系统随行人员离开/返回
     */
    @Clear(LogInterceptor.class)
    public void expenseForFollowLeave() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String uniqueId = getPara("uniqueId");
        String checkinNo = getPara("checkinNo");
        String name = getPara("name");
        String idCard = getPara("idCard");
        String type = getPara("type");
        String time = getPara("time");
        logger.info("随行人员离开接收参数:应用号:[{}],基地ID:[{}],uniqueId:[{}],入住号:[{}],姓名:[{}],身份证:[{}],随行人员动作类型:[{}],时间:[{}]",
                appNo, baseId, uniqueId, checkinNo, name, idCard, type, time);
        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if (StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(checkinNo) || StringUtils.isBlank(uniqueId) ||
                    StringUtils.isBlank(name) || StringUtils.isBlank(idCard) || StringUtils.isBlank(type) || StringUtils.isBlank(time)) {
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }
            List<FinaExpenseRecord> erList = finaExpenseRecordService.getFollowListByCheckinNoPart(appNo, baseId, uniqueId, checkinNo, name, idCard);
            if (erList == null || erList.size()==0) {
                result.put("code", "10002");
                result.put("msg", "该随行人员记录不存在");
                renderJson(result);
                return;
            }

            boolean flag = finaExpenseRecordService.sojournFollowLeave(erList, uniqueId, checkinNo, type, name, time);

            if (flag) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统随行人员离开/返回异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统随行人员离开/返回发生异常：" + e.getMessage());
            renderJson(result);
        }
    }



    /**
     * 通知财务系统客户随行人员登记入住
     * 单独新增随行人员
     */
    @Clear(LogInterceptor.class)
    public void expenseForFollowCheckin() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String followData = getPara("followData");
        logger.info("随行人员登记入住接收参数：应用号:[{}],基地ID:[{}],随行人员数组:[{}]", appNo, baseId, followData);
        finaExpenseRecordService.saveApiRecord("随行人员登记入住", appNo, baseId, null, null, followData);//日志
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用编号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地ID为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(followData)) {
                result.put("code", "10002");
                result.put("msg", "随行人员数组为空");
                renderJson(result);
                return;
            }

            //生成随行账单和随行记录
            JSONArray jsonArray = JSONArray.parseArray(followData);
            Map<String, Object> listMap = finaExpenseRecordService.getErListAndFfListByParams(appNo, baseId, jsonArray);
            logger.info("处理后生成的随行账单和随行记录：[{}]", JSON.toJSONString(listMap));
            if (listMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", listMap.get("error"));
                renderJson(result);
                return;
            }

            List<FinaExpenseRecord> erList = (List<FinaExpenseRecord>) listMap.get("erList");
            List<FinaExpenseRecordFollow> ffList = (List<FinaExpenseRecordFollow>) listMap.get("ffList");
            List<FinaExpenseRecord> mainList = (List<FinaExpenseRecord>) listMap.get("mainList");

            //判断随行人员重复
            Map<String, Object> repeatMap = finaExpenseRecordService.dealFollowRepeat(appNo, baseId, ffList);
            if (repeatMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", repeatMap.get("error"));
                renderJson(result);
                return;
            }
            for (FinaExpenseRecord expenseRecord : erList) {
                expenseRecord.setIsPrivateRoom("0");
            }

            //生成消费明细
            Map<String, Object> detailMap = finaExpenseRecordService.getDetailsByErList(erList,"followCheckin");
            logger.info("处理后的消费明细:[{}]", JSON.toJSONString(detailMap));
            if (detailMap.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", (String) detailMap.get("error"));
                renderJson(result);
                return;
            }
            List<List<FinaExpenseRecordDetail>> sonList = (List<List<FinaExpenseRecordDetail>>) detailMap.get("sonList");
            String checkinNo=jsonArray.getJSONObject(0).getString("checkinNo");
            //获取已住人数
            FinaExpenseRecord mainRecord=finaExpenseRecordService.findMainRecordByCheckinNo(checkinNo);
            List<FinaExpenseRecord> followRecordList=finaExpenseRecordService.getFollowRecordListByMainId(mainRecord.getId());

            FinaMembershipCard membershipCard = finaMembershipCardService.findCardByCardNumber(mainRecord.getCardNumber());
            MainMembershipCardType membershipCardType = mainMembershipCardTypeService.findById(membershipCard.getCardTypeId());


            Map<String,MainCardDeductScheme> cardDeductSchemeMap=new HashMap<>();

            List<FinaExpenseRecordDetail> updateDetailList=new ArrayList<>();

            if("1".equals(mainRecord.getIsPrivateRoom())){
                MainBaseBed baseBed=mainBaseBedService.findById(mainRecord.getBedId());
                MainBaseRoom baseRoom= mainBaseRoomService.findById(baseBed.getRoomId());
                MainBaseRoomType roomType=mainBaseRoomTypeService.findById(baseRoom.getBaseRoomType());
                if(!CheckinType.Staff.getKey().equals(mainRecord.getCheckinType()) && !CheckinType.Gov.getKey().equals(mainRecord.getCheckinType())){
                    if(roomType!=null ){
                        //Map<String,Object> bedMap = finaExpenseRecordService.getBedCount(mainRecord.getBedId());

                        double privateRoomDays=0.0;
                        if(roomType.getPrivateRoomDays()!=null && roomType.getPrivateRoomDays()>0){
                            privateRoomDays=roomType.getPrivateRoomDays();
                        }else{
                            privateRoomDays=(double)finaExpenseRecordService.getBedCount(mainRecord.getBedId()).get("totalBeds");
                        }
                        //double bedNum=(Double)bedMap.get("totalBeds");

                        List<Record> roomBedNumList=Db.find("select a.id,CASE  b.is_big_bed WHEN '1' THEN if(d.is_big_bed_room='1',2,1) else 1 end as bedNum  from main_base_bed a  " +
                                "left join main_bed_type b on a.bed_type_id=b.id  " +
                                "inner join main_base_room c on c.id=a.room_id " +
                                "inner join main_base_room_type d on d.id=c.base_room_type  " +
                                "where a.room_id=? and a.del_flag='0' and a.is_enable='0' ",baseBed.getRoomId());
                        Map<String,Integer> roomBedNumMap=new HashMap<>();
                        int roomTotalBedNum=0;
                        for(Record record:roomBedNumList){
                            roomBedNumMap.put(record.getStr("id"),record.getInt("bedNum"));
                            roomTotalBedNum+=record.getInt("bedNum");
                        }
                        double bedNum=(double) roomTotalBedNum;

                    /*if("1".equals(roomType.getIsBigBedRoom())){
                        bedNum=2.0;
                    }*/
                        Integer checkinNum=followRecordList.size()+1;

                        if((checkinNum+jsonArray.size())<=bedNum){
                            for(List<FinaExpenseRecordDetail> listList:sonList){
                                for(FinaExpenseRecordDetail recordDetail:listList){
                                    recordDetail.setTimes(0.0);
                                    recordDetail.setAmount(0.0);
                                    recordDetail.setIntegrals(0.0);
                                    recordDetail.setPoints(0.0);
                                }
                            }
                        }else{
                            int chargeNum=checkinNum+jsonArray.size()-(int) bedNum;
                            int j=0;
                            for(int i=0;i<jsonArray.size();i++){
                                j++;
                                if((checkinNum+j)<=bedNum){
                                    List<FinaExpenseRecordDetail> detailList=sonList.get(i);
                                    for(FinaExpenseRecordDetail recordDetail:detailList){
                                        recordDetail.setTimes(0.0);
                                        recordDetail.setAmount(0.0);
                                        recordDetail.setIntegrals(0.0);
                                        recordDetail.setPoints(0.0);
                                    }
                                }
                            }
                        }
                    }
                }


                /*if(CheckinType.Staff.getKey().equals(mainRecord.getCheckinType()) || CheckinType.Gov.getKey().equals(mainRecord.getCheckinType())){
                    eachBedCost=1.0;
                }*/

            }else{
                MainBaseBed firstBaseBed=mainBaseBedService.findById(mainRecord.getBedId());
                MainBaseRoom firstBaseRoom= mainBaseRoomService.findById(firstBaseBed.getRoomId());
                MainBaseRoomType roomType=mainBaseRoomTypeService.findById(firstBaseRoom.getBaseRoomType());

                List<Record> roomBedNumList=Db.find("select a.id,CASE  b.is_big_bed WHEN '1' THEN if(d.is_big_bed_room='1',2,1) else 1 end as bedNum  from main_base_bed a  " +
                        "left join main_bed_type b on a.bed_type_id=b.id  " +
                        "inner join main_base_room c on c.id=a.room_id " +
                        "inner join main_base_room_type d on d.id=c.base_room_type  " +
                        "where a.room_id=? and a.del_flag='0' and a.is_enable='0' ",firstBaseBed.getRoomId());
                Map<String,Integer> roomBedNumMap=new HashMap<>();
                int roomTotalBedNum=0;
                for(Record record:roomBedNumList){
                    roomBedNumMap.put(record.getStr("id"),record.getInt("bedNum"));
                    roomTotalBedNum+=record.getInt("bedNum");
                }

                //已入住人数
                int oldCheckinCount=followRecordList.size()+1;

                //新增入住人数
                int newCheckinCount=jsonArray.size();

                double privateRoomDays=0.0;
                if(roomType.getPrivateRoomDays()!=null && roomType.getPrivateRoomDays()>0){
                    privateRoomDays=roomType.getPrivateRoomDays();
                }else{
                    privateRoomDays=(double)finaExpenseRecordService.getBedCount(mainRecord.getBedId()).get("totalBeds");
                }
                //使用床位数
                int useBedCount=roomBedNumMap.get(firstBaseBed.getId());
                double eachBedCost=0.0;



                //获取会员卡可用天数、余额等
                Map<String,Record> cardInfoMap=new HashMap<>();
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject json = jsonArray.getJSONObject(i);
                    String cardNumber = json.getString("cardNumber");
                    FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);
                    if(!cardInfoMap.containsKey(card.getCardNumber())){
                        //获取会员卡锁定
                        Map<String,Double> cardLockInfo=finaMembershipCardService.getCardLockInfo(cardNumber);
                        Double consumeTimes = card.getConsumeTimes();
                        Double balance = card.getBalance();
                        Double consumePoint = card.getConsumePoints();
                        Double integrals=card.getCardIntegrals();
                        if(consumeTimes==null){
                            consumeTimes=0.0;
                        }
                        if(balance==null){
                            balance=0.0;
                        }
                        if(consumePoint==null){
                            consumePoint=0.0;
                        }
                        if(integrals==null){
                            integrals=0.0;
                        }
                        consumeTimes = BigDecimal.valueOf(consumeTimes).subtract(BigDecimal.valueOf(cardLockInfo.get("lockConsumeTimes"))).doubleValue();
                        balance = BigDecimal.valueOf(balance).subtract(BigDecimal.valueOf(cardLockInfo.get("lockBalance"))).doubleValue();
                        consumePoint = BigDecimal.valueOf(consumePoint).subtract(BigDecimal.valueOf(cardLockInfo.get("lockPoints"))).doubleValue();
                        integrals = BigDecimal.valueOf(integrals).subtract(BigDecimal.valueOf(cardLockInfo.get("lockIntegrals"))).doubleValue();

                        Record record=new Record();
                        record.set("consumeTimes",consumeTimes);
                        record.set("balance",balance);
                        record.set("consumePoint",consumePoint);
                        record.set("integrals",integrals);

                        cardInfoMap.put(card.getCardNumber(),record);
                    }
                }


                if("1".equals(roomType.getIsBigBedRoom())){
                    //eachBedCost=privateRoomDays*useBedCount/roomTotalBedNum/(oldCheckinCount+newCheckinCount);
                    eachBedCost=privateRoomDays/roomTotalBedNum;
                    //全部改成
                    List<FinaExpenseRecord> followExpenseRecordList=finaExpenseRecordService.findListBySql("select * from fina_expense_record where parent_id=? and del_flag='0' and `type`='follow_checkin' ",mainRecord.getId());

                    List<FinaExpenseRecord> addExpenseRecordList=new ArrayList<>();
                    addExpenseRecordList.add(mainRecord);
                    addExpenseRecordList.addAll(followExpenseRecordList);

                    //员工入住扣1天
                    if(CheckinType.Staff.getKey().equals(mainRecord.getCheckinType()) || CheckinType.Gov.getKey().equals(mainRecord.getCheckinType())){
                        eachBedCost=1.0;
                    }


                    Set<String> cardSet=new HashSet<>();

                    List<String> expenseIds=new ArrayList<>();
                    for (FinaExpenseRecord record : addExpenseRecordList) {
                        FinaMembershipCard card = finaMembershipCardService.getByCardNumber(record.getCardNumber());

                        cardSet.add(card.getCardNumber());
                        expenseIds.add(record.getId());

                        if(!cardInfoMap.containsKey(card.getCardNumber())){
                            //获取会员卡锁定
                            Map<String,Double> cardLockInfo=finaMembershipCardService.getCardLockInfo(record.getCardNumber());
                            Double consumeTimes = card.getConsumeTimes();
                            Double balance = card.getBalance();
                            Double consumePoint = card.getConsumePoints();
                            Double integrals=card.getCardIntegrals();
                            if(consumeTimes==null){
                                consumeTimes=0.0;
                            }
                            if(balance==null){
                                balance=0.0;
                            }
                            if(consumePoint==null){
                                consumePoint=0.0;
                            }
                            if(integrals==null){
                                integrals=0.0;
                            }
                            consumeTimes = BigDecimal.valueOf(consumeTimes).subtract(BigDecimal.valueOf(cardLockInfo.get("lockConsumeTimes"))).doubleValue();
                            balance = BigDecimal.valueOf(balance).subtract(BigDecimal.valueOf(cardLockInfo.get("lockBalance"))).doubleValue();
                            consumePoint = BigDecimal.valueOf(consumePoint).subtract(BigDecimal.valueOf(cardLockInfo.get("lockPoints"))).doubleValue();
                            integrals = BigDecimal.valueOf(integrals).subtract(BigDecimal.valueOf(cardLockInfo.get("lockIntegrals"))).doubleValue();

                            Record cardRecord=new Record();
                            cardRecord.set("consumeTimes",consumeTimes);
                            cardRecord.set("balance",balance);
                            cardRecord.set("consumePoint",consumePoint);
                            cardRecord.set("integrals",integrals);

                            cardInfoMap.put(card.getCardNumber(),cardRecord);
                        }
                    }

                    //添加本单锁定
                    for (String cardStr : cardSet) {
                        Map<String,Double> addLockInfo=finaMembershipCardService.getRecordCardLockInfoByDate(cardStr,expenseIds,null);
                        Record cardRecord=cardInfoMap.get(cardStr);
                        cardRecord.get("consumeTimes");
                        cardRecord.get("balance");
                        cardRecord.get("consumePoint");
                        cardRecord.get("integrals");

                        cardRecord.set("consumeTimes",cardRecord.getDouble("consumeTimes")+addLockInfo.get("lockConsumeTimes"));
                        cardRecord.set("balance",cardRecord.getDouble("balance")+addLockInfo.get("lockBalance"));
                        cardRecord.set("consumePoint",cardRecord.getDouble("consumePoint")+addLockInfo.get("lockPoints"));
                        cardRecord.set("integrals",cardRecord.getDouble("integrals")+addLockInfo.get("lockIntegrals"));


                    }

                    int index=0;
                    for (FinaExpenseRecord record : addExpenseRecordList) {

                        FinaMembershipCard card = finaMembershipCardService.getByCardNumber(record.getCardNumber());
                        MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(card.getCardNumber());
                        if(scheme==null){
                            scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(card.getCardNumber());
                        }

                        String followDeductType=record.getFollowDeductType();

                        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());

                        Record cardInfoRecord=cardInfoMap.get(card.getCardNumber());

                        List<FinaExpenseRecordDetail> detailList=finaExpenseRecordDetailService.getDetailsByExpenseId(record.getId(),null);

                        double forEachBedCost=0.0;
                        if(useBedCount>index){
                            forEachBedCost=eachBedCost;
                        }else{
                            forEachBedCost=1;
                        }
                        index++;

                        if("1".equals(cardType.getIsIntegral())){
                            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){

                                double times=0.0;
                                if(StrKit.isBlank(followDeductType) || FollowDeductType.Whole.getKey().equals(followDeductType)){
                                    //全票
                                    times=forEachBedCost;
                                }else if(FollowDeductType.Half.getKey().equals(followDeductType)){
                                    //半票
                                    times=forEachBedCost/2;
                                }else if(FollowDeductType.Exemption.getKey().equals(followDeductType)){
                                    //免费
                                    times=0.0;
                                }

                                double consumeTimes=cardInfoRecord.getDouble("consumeTimes");
                                double integrals=cardInfoRecord.getDouble("integrals");
                                for (FinaExpenseRecordDetail detail : detailList) {
                                    if(integrals>=1){
                                        if(times>integrals){
                                            detail.setIntegrals(integrals);
                                            detail.setTimes(times-integrals);
                                            integrals=integrals-integrals;
                                        }else{
                                            detail.setIntegrals(times);
                                            detail.setTimes(0.0);
                                            integrals=integrals-times;
                                        }
                                    }else{
                                        detail.setIntegrals(0.0);
                                        detail.setTimes(times);
                                        //integrals=integrals-times;
                                    }
                                    detail.setUpdateTime(new Date());
                                }

                                cardInfoRecord.set("integrals",integrals);

                            }
                        }else{
                            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){

                                double times=0.0;
                                if(StrKit.isBlank(followDeductType) || FollowDeductType.Whole.getKey().equals(followDeductType)){
                                    //全票
                                    times=forEachBedCost;
                                }else if(FollowDeductType.Half.getKey().equals(followDeductType)){
                                    //半票
                                    times=forEachBedCost/2;
                                }else if(FollowDeductType.Exemption.getKey().equals(followDeductType)){
                                    //免费
                                    times=0.0;
                                }
                                if(detailList!=null){
                                    for (FinaExpenseRecordDetail detail : detailList) {
                                        detail.setTimes(times);
                                        detail.setUpdateTime(new Date());
                                    }
                                }

                            }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())){

                                Map<String,Object> deductDetailMap=finaExpenseRecordService.getAmountCardDeductDetail(scheme,record.getCheckinTime(),record.getCheckoutTime(),"0",record.getIdCard(),forEachBedCost,oldCheckinCount+newCheckinCount,firstBaseBed.getId(),false);

                                Map<String,Double> detailAmountMap=(Map<String,Double>)deductDetailMap.get("detailMap");
                                if(detailList!=null){
                                    for(FinaExpenseRecordDetail detail : detailList){
                                        detail.setAmount(0.0);
                                        String detailStartTime=DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd");

                                        if(detailAmountMap.containsKey(detailStartTime)){
                                            detail.setAmount(detailAmountMap.get(detailStartTime));
                                            if(FollowDeductType.Half.getKey().equals(mainRecord.getFollowDeductType())){
                                                //随行半票
                                                detail.setAmount(detail.getAmount()/2);
                                            }else if(FollowDeductType.Exemption.getKey().equals(mainRecord.getFollowDeductType())){
                                                //随行免票
                                                detail.setAmount(0.0);
                                            }
                                        }
                                        detail.setUpdateTime(new Date());
                                    }
                                }
                            }
                        }
                        updateDetailList.addAll(detailList);
                    }

                    for (int i = 0; i < jsonArray.size(); i++) {

                        JSONObject json=jsonArray.getJSONObject(i);
                        String cardNumber=json.getString("cardNumber");
                        Date startTime = DateUtil.parse(json.getString("checkinDate"));
                        Date endTime = DateUtil.parse(json.getString("checkoutDate"));
                        String idCard=json.getString("idCard");
                        Integer x = TimeUtils.getCountByDiffTimes(startTime,endTime);//明细数量

                        String followDeductType=json.getString("followDeductType");


                        MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
                        if(scheme==null){
                            scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
                        }

                        FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);

                        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());

                        String expenseId=null;
                        for (FinaExpenseRecord record : erList) {
                            if(idCard.equalsIgnoreCase(record.getIdCard())){
                                expenseId=record.getId();
                                break;
                            }
                        }
                        List<FinaExpenseRecordDetail> detailList=null;
                        for (List<FinaExpenseRecordDetail> item : sonList) {
                            if(expenseId.equalsIgnoreCase(item.get(0).getExpenseId())){
                                detailList=item;
                                break;
                            }
                        }

                        Record cardInfoRecord=cardInfoMap.get(card.getCardNumber());

                        double forEachBedCost=0.0;
                        if(useBedCount>(index+i)){
                            forEachBedCost=eachBedCost;
                        }else{
                            forEachBedCost=1;
                        }

                        if("1".equals(cardType.getIsIntegral())){
                            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){

                                double times=0.0;
                                if(StrKit.isBlank(followDeductType) || FollowDeductType.Whole.getKey().equals(followDeductType)){
                                    //全票
                                    times=forEachBedCost;
                                }else if(FollowDeductType.Half.getKey().equals(followDeductType)){
                                    //半票
                                    times=forEachBedCost/2;
                                }else if(FollowDeductType.Exemption.getKey().equals(followDeductType)){
                                    //免费
                                    times=0.0;
                                }

                                double consumeTimes=cardInfoRecord.getDouble("consumeTimes");
                                double integrals=cardInfoRecord.getDouble("integrals");
                                for (FinaExpenseRecordDetail detail : detailList) {
                                    if(integrals>=1){
                                        if(times>integrals){
                                            detail.setIntegrals(integrals);
                                            detail.setTimes(times-integrals);
                                            integrals=integrals-integrals;
                                        }else{
                                            detail.setIntegrals(times);
                                            detail.setTimes(0.0);
                                            integrals=integrals-times;
                                        }
                                    }else{
                                        detail.setIntegrals(0.0);
                                        detail.setTimes(times);
                                    }
                                }
                                cardInfoRecord.set("integrals",integrals);

                            }
                        }else{
                            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){

                                double times=0.0;
                                if(StrKit.isBlank(followDeductType) || FollowDeductType.Whole.getKey().equals(followDeductType)){
                                    //全票
                                    times=forEachBedCost;
                                }else if(FollowDeductType.Half.getKey().equals(followDeductType)){
                                    //半票
                                    times=forEachBedCost/2;
                                }else if(FollowDeductType.Exemption.getKey().equals(followDeductType)){
                                    //免费
                                    times=0.0;
                                }
                                if(detailList!=null){
                                    for (FinaExpenseRecordDetail detail : detailList) {
                                        detail.setTimes(times);
                                        detail.setUpdateTime(new Date());
                                    }
                                }

                            }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())){

                                Map<String,Object> deductDetailMap=finaExpenseRecordService.getAmountCardDeductDetail(scheme,startTime,endTime,"0",idCard,forEachBedCost,oldCheckinCount+newCheckinCount,firstBaseBed.getId(),false);

                                Map<String,Double> detailAmountMap=(Map<String,Double>)deductDetailMap.get("detailMap");
                                if(detailList!=null){
                                    for(FinaExpenseRecordDetail detail : detailList){
                                        detail.setAmount(0.0);
                                        String detailStartTime=DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd");

                                        if(detailAmountMap.containsKey(detailStartTime)){
                                            detail.setAmount(detailAmountMap.get(detailStartTime));
                                            if(FollowDeductType.Half.getKey().equals(mainRecord.getFollowDeductType())){
                                                //随行半票
                                                detail.setAmount(detail.getAmount()/2);
                                            }else if(FollowDeductType.Exemption.getKey().equals(mainRecord.getFollowDeductType())){
                                                //随行免票
                                                detail.setAmount(0.0);
                                            }
                                        }
                                    }
                                }
                            }
                        }



                    }

                }else{
                    //eachBedCost=privateRoomDays/roomTotalBedNum;
                    eachBedCost=1;
                    //员工入住扣1天
                    if(CheckinType.Staff.getKey().equals(mainRecord.getCheckinType()) || CheckinType.Gov.getKey().equals(mainRecord.getCheckinType())){
                        eachBedCost=1.0;
                    }



                    for (int i = 0; i < jsonArray.size(); i++) {

                        JSONObject json=jsonArray.getJSONObject(i);
                        String cardNumber=json.getString("cardNumber");
                        String idCard=json.getString("idCard");

                        Date startTime = DateUtil.parse(json.getString("checkinDate"));
                        Date endTime = DateUtil.parse(json.getString("checkoutDate"));
                        Integer x = TimeUtils.getCountByDiffTimes(startTime,endTime);//明细数量

                        String followDeductType=json.getString("followDeductType");

                        FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);
                        MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
                        if(scheme==null){
                            scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
                        }
                        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());


                        String expenseId=null;
                        for (FinaExpenseRecord record : erList) {
                            if(idCard.equalsIgnoreCase(record.getIdCard())){
                                expenseId=record.getId();
                                break;
                            }
                        }
                        List<FinaExpenseRecordDetail> detailList=null;
                        for (List<FinaExpenseRecordDetail> item : sonList) {
                            if(expenseId.equalsIgnoreCase(item.get(0).getExpenseId())){
                                detailList=item;
                                break;
                            }
                        }

                        Record cardInfoRecord=cardInfoMap.get(card.getCardNumber());

                        if("1".equals(cardType.getIsIntegral())){
                            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){

                                double times=0.0;
                                if(StrKit.isBlank(followDeductType) || FollowDeductType.Whole.getKey().equals(followDeductType)){
                                    //全票
                                    times=eachBedCost;
                                }else if(FollowDeductType.Half.getKey().equals(followDeductType)){
                                    //半票
                                    times=eachBedCost/2;
                                }else if(FollowDeductType.Exemption.getKey().equals(followDeductType)){
                                    //免费
                                    times=0.0;
                                }

                                double consumeTimes=cardInfoRecord.getDouble("consumeTimes");
                                double integrals=cardInfoRecord.getDouble("integrals");
                                for (FinaExpenseRecordDetail detail : detailList) {
                                    if(integrals>=1){
                                        if(times>integrals){
                                            detail.setIntegrals(integrals);
                                            detail.setTimes(times-integrals);
                                            integrals=integrals-integrals;
                                        }else{
                                            detail.setIntegrals(times);
                                            detail.setTimes(0.0);
                                            integrals=integrals-times;
                                        }
                                    }else{
                                        detail.setIntegrals(0.0);
                                        detail.setTimes(times);
                                    }
                                }
                                cardInfoRecord.set("integrals",integrals);

                            }
                        }else{
                            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){

                                double times=0.0;
                                if(StrKit.isBlank(followDeductType) || FollowDeductType.Whole.getKey().equals(followDeductType)){
                                    //全票
                                    times=eachBedCost;
                                }else if(FollowDeductType.Half.getKey().equals(followDeductType)){
                                    //半票
                                    times=eachBedCost/2;
                                }else if(FollowDeductType.Exemption.getKey().equals(followDeductType)){
                                    //免费
                                    times=0.0;
                                }
                                if(detailList!=null){
                                    for (FinaExpenseRecordDetail detail : detailList) {
                                        detail.setTimes(times);
                                        detail.setUpdateTime(new Date());
                                    }
                                }

                            }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())){

                                Map<String,Object> deductDetailMap=finaExpenseRecordService.getAmountCardDeductDetail(scheme,startTime,endTime,"0",idCard,eachBedCost,oldCheckinCount+newCheckinCount,firstBaseBed.getId(),false);

                                Map<String,Double> detailAmountMap=(Map<String,Double>)deductDetailMap.get("detailMap");
                                if(detailList!=null){
                                    for(FinaExpenseRecordDetail detail : detailList){
                                        detail.setAmount(0.0);
                                        String detailStartTime=DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd");

                                        if(detailAmountMap.containsKey(detailStartTime)){
                                            detail.setAmount(detailAmountMap.get(detailStartTime));
                                            if(FollowDeductType.Half.getKey().equals(mainRecord.getFollowDeductType())){
                                                //随行半票
                                                detail.setAmount(detail.getAmount()/2);
                                            }else if(FollowDeductType.Exemption.getKey().equals(mainRecord.getFollowDeductType())){
                                                //随行免票
                                                detail.setAmount(0.0);
                                            }
                                        }
                                    }
                                }
                            }
                        }

                    }

                }
            }








            List<Record> checkinFollowList=Db.find("select a.id,a.card_number,a.`name`,a.id_card,b.follow_deduct_type from fina_expense_record a " +
                    " left join fina_expense_record_follow b on a.id=b.expense_id  where parent_id=? " +
                    "and a.del_flag='0' and a.settle_status in ('0','1') and a.checkin_status in ('stayin','book') " +
                    "and a.category='sojourn_bill' and a.type='follow_checkin' order by a.create_time ",mainRecord.getId());

            List<FinaExpenseRecord> expenseRecordList=new ArrayList<>();

            Date followCheckinDate=jsonArray.getJSONObject(0).getDate("checkinDate");
            Date followCheckoutDate=jsonArray.getJSONObject(0).getDate("checkoutDate");

            mainRecord.setCheckinTime(followCheckinDate);
            mainRecord.setCheckoutTime(followCheckoutDate);

            expenseRecordList.add(mainRecord);

            Map<String,List<FinaExpenseRecordDetail>> updateExpenseRecordDetailListMap=new HashMap<>();




            if(checkinFollowList!=null && checkinFollowList.size()>0){
                Map<String, FinaExpenseRecord> checkinFollowMap = new LinkedHashMap<>();
                for (Record record : checkinFollowList) {
                    FinaExpenseRecord expenseRecord=new FinaExpenseRecord();
                    expenseRecord.setId(record.getStr("id"));
                    expenseRecord.setCheckinNo(mainRecord.getCheckinNo());
                    expenseRecord.setCheckinTime(followCheckinDate);
                    expenseRecord.setCheckoutTime(followCheckoutDate);
                    expenseRecord.setBedId(mainRecord.getBedId());
                    expenseRecord.setName(record.get("name"));
                    expenseRecord.setCardNumber(record.getStr("card_number"));
                    expenseRecord.setIdCard(record.getStr("id_card"));
                    expenseRecord.setFollowDeductType(record.getStr("follow_deduct_type"));
                    checkinFollowMap.put(expenseRecord.getIdCard(),expenseRecord);
                }
                for (Map.Entry<String, FinaExpenseRecord> stringFinaExpenseRecordEntry : checkinFollowMap.entrySet()) {
                    expenseRecordList.add(stringFinaExpenseRecordEntry.getValue());
                }
            }

            for (FinaExpenseRecord expenseRecord : expenseRecordList) {
                List<FinaExpenseRecordDetail> expenseRecordDetailList = finaExpenseRecordDetailService.getExpenseRecordDetailListByIdCard(expenseRecord.getId(), expenseRecord.getIdCard(), followCheckinDate, followCheckoutDate);
                updateExpenseRecordDetailListMap.put(expenseRecord.getId(),expenseRecordDetailList);
            }
            expenseRecordList.addAll(erList);

            Map<String, Object> detailsByErListMap = finaExpenseRecordService.getDetailsByErListCheckin(expenseRecordList, null);

            List<FinaExpenseRecordDetail> newExpenseRecordDetailList=new ArrayList<>();

            for (List<FinaExpenseRecordDetail> detailList : (List<List<FinaExpenseRecordDetail>>) detailsByErListMap.get("sonList")) {
                newExpenseRecordDetailList.addAll(detailList);
            }

            List<FinaExpenseRecordDetail> updateExpenseRecordList=new ArrayList<>();
            for (String key : updateExpenseRecordDetailListMap.keySet()) {
                List<FinaExpenseRecordDetail> detailList=updateExpenseRecordDetailListMap.get(key);
                for (FinaExpenseRecordDetail updateDetail : detailList) {
                    for (FinaExpenseRecordDetail newDetail : newExpenseRecordDetailList) {
                        if(key.equalsIgnoreCase(newDetail.getExpenseId()) && DateUtils.formatDate(newDetail.getStartTime(),"yyyy-MM-dd")
                                .equals(DateUtils.formatDate(updateDetail.getStartTime(),"yyyy-MM-dd"))
                                && DateUtils.formatDate(newDetail.getEndTime(),"yyyy-MM-dd")
                                .equals(DateUtils.formatDate(updateDetail.getEndTime(),"yyyy-MM-dd"))
                        ){
                            updateDetail.setTimes(newDetail.getTimes());
                            updateDetail.setAmount(newDetail.getAmount());
                            updateDetail.setIntegrals(newDetail.getIntegrals());
                            updateDetail.setUpdateTime(new Date());
                            updateExpenseRecordList.add(updateDetail);
                        }
                    }
                }
            }
            List<FinaExpenseRecordDetail> addExpenseRecordList=new ArrayList<>();
            for (FinaExpenseRecord expenseRecord : erList) {
                for (FinaExpenseRecordDetail newDetail : newExpenseRecordDetailList) {
                    if(expenseRecord.getId().equalsIgnoreCase(newDetail.getExpenseId())){
                        addExpenseRecordList.add(newDetail);
                    }
                }
            }



            boolean flag = Db.tx(new IAtom() {
                public boolean run() throws SQLException {
                    try {
                        int[] ints = Db.batchSave(erList, erList.size());
                        for (int i : ints) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        Set<String> expenseIds=new HashSet<>();
                        for (FinaExpenseRecordDetail detail : updateExpenseRecordList) {
                            expenseIds.add(detail.getExpenseId());
                        }
                        for (FinaExpenseRecordDetail detail : addExpenseRecordList) {
                            expenseIds.add(detail.getExpenseId());
                        }
                        String str="";
                        for (String expenseId : expenseIds) {
                            str+="?,";
                        }
                        str=str.substring(0,str.length()-1);
                        List<Record> recordList = Db.find("select id,is_not_deduct_card from fina_expense_record where id in (" + str + ") ", expenseIds.toArray());
                        Map<String,String> isNotDeductCardMap=new HashMap<>();
                        for (Record record : recordList) {
                            isNotDeductCardMap.put(record.getStr("id"),record.getStr("is_not_deduct_card"));
                        }
                        for (FinaExpenseRecordDetail detail : addExpenseRecordList) {
                            String isNotDeductCard=isNotDeductCardMap.get(detail.getExpenseId());
                            if("1".equals(isNotDeductCard)){
                                detail.setTimes(0.0);
                                detail.setAmount(0.0);
                                detail.setIntegrals(0.0);
                            }
                        }
                        for (FinaExpenseRecordDetail detail : updateExpenseRecordList) {
                            String isNotDeductCard=isNotDeductCardMap.get(detail.getExpenseId());
                            if("1".equals(isNotDeductCard)){
                                detail.setTimes(0.0);
                                detail.setAmount(0.0);
                                detail.setIntegrals(0.0);
                            }
                        }

                        int[] intFFs = Db.batchSave(addExpenseRecordList, addExpenseRecordList.size());
                        for (int i : intFFs) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        int[] intFFss = Db.batchUpdate(updateExpenseRecordList, updateExpenseRecordList.size());
                        for (int i : intFFss) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        int[] intFFsss = Db.batchSave(ffList, ffList.size());
                        for (int i : intFFsss) {
                            if (i < 1) {
                                return false;
                            }
                        }
                        /*for (List<FinaExpenseRecordDetail> item : sonList) {
                            if(TypeClassify.OWNER_CARD.equals(membershipCardType.getTypeClassify())){
                                for (FinaExpenseRecordDetail finaExpenseRecordDetail : item) {
                                    finaExpenseRecordDetail.setTimes(0.0);
                                    finaExpenseRecordDetail.setPoints(0.0);
                                    finaExpenseRecordDetail.setIntegrals(0.0);
                                    finaExpenseRecordDetail.setAmount(0.0);
                                    finaExpenseRecordDetail.setBeanCoupons(0.0);
                                }
                            }
                            int[] intList = Db.batchSave(item, item.size());
                            for (int i : intList) {
                                if (i < 1) {
                                    return false;
                                }
                            }
                        }
                        if(updateDetailList.size()>0){
                            Db.batchUpdate(updateDetailList,updateDetailList.size());
                        }*/
                    } catch (Exception e) {
                        e.printStackTrace();
                        return false;
                    }
                    return true;
                }
            });

            /*boolean sucLock = false;
            boolean sucTotalLock = false;
            boolean sucTotalActual = false;
            if (flag) {
                sucLock = finaExpenseRecordService.getCardLock(erList, 1, null);
                if (sucLock) {
                    sucTotalLock = finaExpenseRecordService.getCardTotalLock(mainList);
                    if (sucTotalLock) {
                        sucTotalActual = finaExpenseRecordService.getCardTotalActual(mainList);
                    }
                }
            }*/

            if (flag) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统客户随行人员登记入住异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户随行人员登记入住发生异常：" + e.getMessage());
            renderJson(result);
        }
    }

    /**
     * 通知财务系统旅游团组团成功
     * 1.无旅游团是则新增旅游团记录，有则不管
     * 2.录入旅游团账单
     */
    @Clear(LogInterceptor.class)
    public void expenseForTouristGroup() {

        String json=HttpKit.readData(getRequest());

        JSONObject jsonObject=JSON.parseObject(json);

        String appNo = jsonObject.getString("appNo");
        String touristNo = jsonObject.getString("touristNo");
        String touristName = jsonObject.getString("touristName");
        String touristRoute = jsonObject.getString("touristRoute");
        String touristRouteId = jsonObject.getString("touristRouteId");
        String startDate = jsonObject.getString("startDate");
        String endDate = jsonObject.getString("endDate");
        String expenseTimes = jsonObject.getString("expenseTimes");
        String touristData = jsonObject.getString("touristData");
        String businessEntityId=jsonObject.getString("businessEntityId");
        String customerChannel=jsonObject.getString("customerChannel");
        String touristDetail=jsonObject.getString("touristDetail");
        String createBy=getPara("createBy");
        logger.info("旅游团组团接收参数：应用号:[{}],旅游团编号:[{}],旅游团名称:[{}],描述:[{}],线路ID:[{}],开团时间:[{}],结束时间:[{}],扣除天数:[{}],参团数组:[{}],业务实体:[{}],客户渠道:[{}]",
                appNo, touristNo, touristName, touristRoute, touristRouteId, startDate, endDate, expenseTimes, touristData,businessEntityId,customerChannel);



        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if (StringUtils.isBlank(appNo) || StringUtils.isBlank(touristNo) || StringUtils.isBlank(touristName) || StringUtils.isBlank(startDate)
                    || StringUtils.isBlank(endDate) || StringUtils.isBlank(expenseTimes) || StringUtils.isBlank(touristData)
                    ) {
                //||StringUtils.isBlank(businessEntityId)
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }
            Map<String, Object> map = finaExpenseRecordService
                    .dealTouristBill(appNo, touristNo, touristName, touristRoute, touristRouteId, startDate, endDate
                            , expenseTimes,businessEntityId,customerChannel, touristData,touristDetail,createBy);
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }
            if (map.containsKey("suc")) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统旅游团组团异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统旅游团组团发生异常:" + e.getMessage());
            renderJson(result);
        }
    }



    public void expenseForTouristDetailSave(){
        String touristDetailArrayStr=getPara("touristDetail");
        if(StrKit.isBlank(touristDetailArrayStr)){
            renderCodeFailed("参数缺失");
            return;
        }
        List<FinaExpenseRecordTouristDetail> touristDetailList=new ArrayList<>();
        if(StrKit.notBlank(touristDetailArrayStr)){
            JSONArray jsonArray=JSON.parseArray(touristDetailArrayStr);
            if(jsonArray!=null && jsonArray.size()>0){
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject=jsonArray.getJSONObject(i);
                    FinaExpenseRecordTouristDetail touristDetail=jsonObject.toJavaObject(FinaExpenseRecordTouristDetail.class);
                    touristDetail.setId(IdGen.getUUID());
                    touristDetail.setDelFlag("0");
                    touristDetail.setCreateDate(new Date());
                    touristDetail.setUpdateDate(new Date());
                    touristDetailList.add(touristDetail);
                }
            }
        }
        if(touristDetailList.size()>0){
            try {
                Db.batchSave(touristDetailList,touristDetailList.size());
                renderCodeSuccess("success");
                return;
            }catch (Exception e){
                e.printStackTrace();
                renderCodeFailed("操作异常");
                return;
            }


        }else{
            renderCodeFailed("请至少传入1条记录");
        }

    }


    /**
     * 统一校验客户账户剩余天数或金额
     */
    @Clear(LogInterceptor.class)
    public void checkForExpense() {

        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String type = getPara("type");
        String bookNo=getPara("bookNo");
        String checkinNo=getPara("checkinNo");
        String checkData = getPara("checkData");
        String addCheckinNo=getPara("addCheckinNo");
        String addBookNo=getPara("addBookNo");
        String addStartDate=getPara("addStartDate");
        String isLock=getPara("isLock");//是否预锁定 1是，0否
        String checkinType=getPara("checkinType");
        logger.info("统一校验客户账户剩余天数或金额接收参数:应用号:[{}],基地:[{}],校验数组:[{}],,预定号:[{}],入住号:[{}],addCheckinNo:[{}],addBookNo:[{}],addStartDate:[{}],checkinType:[{}]",
                appNo, baseId, checkData,bookNo,checkinNo,addCheckinNo,addBookNo,addStartDate,checkinType);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo)) {
                result.put("code", "10002");
                result.put("msg", "应用号为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(baseId)) {
                result.put("code", "10002");
                result.put("msg", "基地为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(type)) {
                result.put("code", "10002");
                result.put("msg", "业务类型为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(checkData)) {
                result.put("code", "10002");
                result.put("msg", "校验数组为空");
                renderJson(result);
                return;
            }

            Map<String, Object> map = finaExpenseRecordService.isBookAndCheckin(appNo,bookNo,checkinNo, baseId, type, checkData,addCheckinNo,addBookNo,addStartDate,isLock,checkinType);
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }

            Record record = (Record) map.get("msg");
            if (record.getStr("msg").indexOf("卡") != -1) {
                result.put("code", "10001");
                result.put("msg", "fail");
            } else {
                result.put("code", "0");
                result.put("msg", "success");
                result.put("successMsg",map.get("successMsg"));
                result.put("cardArray",map.get("cardArray"));
            }
            result.put("data",  record.get("detailIds"));
            result.put("msg",record.get("msg"));
            renderJson(result);
        } catch (Exception e) {
            logger.error("统一校验客户账户剩余天数或金额异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "统一校验客户账户剩余天数或金额发生异常:" + e.getMessage());
            renderJson(result);
        }
    }

    public void bookUnlock(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderCodeFailed("参数缺失");
            return;
        }

        FinaExpenseRecordDetail detail=finaExpenseRecordDetailService.findById(id);
        if(detail==null){
            renderCodeFailed("通过id查询不到记录");
            return;
        }
        detail.setDelFlag("1");
        detail.setUpdateTime(new Date());
        if(detail.update()){
            renderCodeSuccess("解锁成功");
        }else{
            renderCodeFailed("解锁失败");
        }
    }

    /**
     * 旅游团客户账户校验
     */
    @Clear(LogInterceptor.class)
    public void checkForTourist() {
        String touristRouteId = getPara("touristRouteId");
        String checkData = getPara("checkData");
        String checkinDate=getPara("checkinDate");

        logger.info("旅游团客户账户校验接收参数:旅游团线路id:[{}],开团时间:[{}],校验数组:[{}]",touristRouteId,checkinDate, checkData);
        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if(StrKit.isBlank(touristRouteId)){
                result.put("code", "10002");
                result.put("msg", "旅游团线路为空");
                renderJson(result);
                return;
            }
            if(StrKit.isBlank(checkinDate)){
                result.put("code", "10002");
                result.put("msg", "开团时间不能为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isBlank(checkData)) {
                result.put("code", "10002");
                result.put("msg", "校验数组为空");
                renderJson(result);
                return;
            }
            Map<String, Object> map = finaExpenseRecordService.isCheckinTourist(touristRouteId,checkinDate,checkData);
            logger.info("旅游团校验处理结果:[{}]", JSON.toJSONString(map));
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }

            Record record = (Record) map.get("msg");
            if (record.getStr("msg").indexOf("卡") != -1) {
                result.put("code", "10001");
                result.put("msg", record.getStr("msg"));
            } else {
                result.put("code", "0");
                result.put("msg", "success");
            }
            result.put("data", map.get("msg"));
            renderJson(result);
        } catch (Exception e) {
            logger.error("旅游团客户账户校验异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "旅游团客户账户校验发生异常:" + e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 通知财务系统客户更换会员卡
     */
    public void expenseForChangeCard() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String uniqueId = getPara("uniqueId");
        String bookNo = getPara("bookNo");
        String checkinNo = getPara("checkinNo");
        String name = getPara("name");
        String idCard = getPara("idCard");
        String changeType = getPara("changeType");//切换类型
        String cardNumber = getPara("cardNumber");//新卡号
        logger.info("更换会员卡接收参数:应用号:[{}],基地ID:[{}],uniqueId:[{}],预订号:[{}],入住号:[{}],姓名:[{}],身份证:[{}],切换类型:[{}],会员卡:[{}]",
                appNo, baseId, uniqueId, bookNo, checkinNo, name, idCard, changeType, cardNumber);

        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if (StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(name)
                    || StringUtils.isBlank(idCard) || StringUtils.isBlank(cardNumber) || StringUtils.isBlank(changeType)) {
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }
            if ((StringUtils.isBlank(bookNo) && StringUtils.isBlank(checkinNo)) || (StringUtils.isNotBlank(checkinNo) && StringUtils.isBlank(uniqueId))) {
                result.put("code", "10002");
                result.put("msg", "预订号和入住号至少有一个不为空");
                renderJson(result);
                return;
            }

            FinaExpenseRecord er = null;
            if (StringUtils.isNotBlank(bookNo) && StringUtils.isBlank(checkinNo)) {
                er = finaExpenseRecordService.getRecordByBookNoPart(appNo, baseId, bookNo, name, idCard);
            } else if (StringUtils.isNotBlank(checkinNo) && StringUtils.isBlank(bookNo)) {
                er = finaExpenseRecordService.getRecordByCheckinNoPart(appNo, baseId, uniqueId, checkinNo, name, idCard);
            } else {
                result.put("code", "10002");
                result.put("msg", "预订号和入住号至少有一个为空");
                renderJson(result);
                return;
            }

            if (er == null || !"0".equals(er.getSettleStatus())) {
                result.put("code", "10002");
                result.put("msg", "账单记录不存在或账单处于非未结算状态");
                renderJson(result);
                return;
            }

            Map<String, Object> map = finaExpenseRecordService.sojournChangeCard(er, uniqueId, cardNumber, changeType);
            logger.info("处理消费明细后的返回结果:[{}]", JSON.toJSONString(map));
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }
            boolean flag = (Boolean) map.get("flag");
            boolean sucTotalActual = false;
            if (flag) {
                //统计扣卡明细
                FinaExpenseRecord mainEr = finaExpenseRecordService.getErByCheckinNoPart(appNo, baseId, checkinNo);
                List<FinaExpenseRecord> mainList = new ArrayList<>();
                if (mainEr != null) mainList.add(mainEr);
                sucTotalActual = finaExpenseRecordService.getCardTotalActual(mainList);
            }
            if (flag && sucTotalActual) {
                result.put("code", "0");
                result.put("msg", "success");
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务系统客户更换会员卡异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "通知财务系统客户更换会员卡发生异常：" + e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 【新】通知财务系统客户更换会员卡
     */
    /*public void newExpenseForChangeCard() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String uniqueId = getPara("uniqueId");
        String bookNo = getPara("bookNo");
        String checkinNo = getPara("checkinNo");
        String name = getPara("name");
        String idCard = getPara("idCard");
        String changeType = getPara("changeType");//切换类型
        String changeData = getPara("changeData");//更换数组
        logger.info("更换会员卡接收参数:应用号:[{}],基地ID:[{}],uniqueId:[{}],预订号:[{}],入住号:[{}],姓名:[{}],身份证:[{}],切换类型:[{}],更换数组:[{}]",
                appNo, baseId, uniqueId, bookNo, checkinNo, name, idCard, changeType, changeData);

        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(name)
                    || StringUtils.isBlank(idCard) || StringUtils.isBlank(changeType) || StringUtils.isBlank(changeData)) {
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }
            JSONArray jsonArray = JSONArray.parseArray(changeData);
            if (jsonArray == null || jsonArray.size() <= 0) {
                result.put("code", "10002");
                result.put("msg", "更换数组中无会员卡，不可更换");
                renderJson(result);
                return;
            }
            if ((StringUtils.isBlank(bookNo) && StringUtils.isBlank(checkinNo)) ||
                    (StringUtils.isNotBlank(checkinNo) && StringUtils.isBlank(uniqueId)) ||
                    StringUtils.isNotBlank(bookNo) && StringUtils.isNotBlank(checkinNo)) {
                result.put("code", "10002");
                result.put("msg", "预订号和入住号至少有一个不为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isNotBlank(bookNo) && "1".equals(changeType)) {
                result.put("code", "10002");
                result.put("msg", "预订时不允许分段更换会员卡");
                renderJson(result);
                return;
            }

            FinaExpenseRecord er = null;
            if (StringUtils.isNotBlank(bookNo) && StringUtils.isBlank(checkinNo)) {
                er = finaExpenseRecordService.getRecordByBookNoPart(appNo, baseId, bookNo, name, idCard);
            } else if (StringUtils.isNotBlank(checkinNo) && StringUtils.isBlank(bookNo)) {
                er = finaExpenseRecordService.getRecordByCheckinNoPart(appNo, baseId, uniqueId, checkinNo, name, idCard);
            } else {
                result.put("code", "10002");
                result.put("msg", "预订号和入住号至少有一个为空");
                renderJson(result);
                return;
            }
            if (er == null || !"0".equals(er.getSettleStatus())) {
                result.put("code", "10002");
                result.put("msg", "账单记录不存在或账单处于非未结算状态");
                renderJson(result);
                return;
            }
            Map<String, Object> map = finaExpenseRecordService.sojournChangeCard(er, uniqueId, jsonArray);
            logger.info("处理消费明细后的返回结果:[{}]", JSON.toJSONString(map));
            if (map.containsKey("error")) {
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }
            boolean flag = (Boolean) map.get("flag");
            boolean sucTotalActual = false;
            if (flag) {
                //统计扣卡明细
                FinaExpenseRecord mainEr = finaExpenseRecordService.getErByCheckinNoPart(appNo, baseId, checkinNo);
                List<FinaExpenseRecord> mainList = new ArrayList<>();
                if (mainEr != null) mainList.add(mainEr);
                sucTotalActual = finaExpenseRecordService.getCardTotalActual(mainList);
            }
            if (flag && sucTotalActual) {
                result.put("code", "0");
                result.put("msg", map.get("data"));
            } else {
                result.put("code", "10001");
                result.put("msg", "fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("【新】通知财务系统客户更换会员卡异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "【新】通知财务系统客户更换会员卡发生异常:" + e.getMessage());
            renderJson(result);
        }
    }*/

    /**
     * 【新】通知财务系统客户更换会员卡
     */

    public void newExpenseForChangeCard() {
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String uniqueId = getPara("uniqueId");
        String bookNo = getPara("bookNo");
        String checkinNo = getPara("checkinNo");
        String name = getPara("name");
        String idCard = getPara("idCard");
        String changeType = getPara("changeType");//切换类型
        String changeData = getPara("changeData");//更换数组
        logger.info("更换会员卡接收参数:应用号:[{}],基地ID:[{}],uniqueId:[{}],预订号:[{}],入住号:[{}],姓名:[{}],身份证:[{}],切换类型:[{}],更换数组:[{}]",
                appNo, baseId, uniqueId, bookNo, checkinNo, name, idCard, changeType, changeData);

        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if (StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(name)
                    || StringUtils.isBlank(idCard) || StringUtils.isBlank(changeType) || StringUtils.isBlank(changeData)) {
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }
            JSONArray jsonArray = JSONArray.parseArray(changeData);
            if (jsonArray == null || jsonArray.size() <= 0) {
                result.put("code", "10002");
                result.put("msg", "更换数组中无会员卡，不可更换");
                renderJson(result);
                return;
            }
            if ((StringUtils.isBlank(bookNo) && StringUtils.isBlank(checkinNo)) ||
                    (StringUtils.isNotBlank(checkinNo) && StringUtils.isBlank(uniqueId)) ||
                    StringUtils.isNotBlank(bookNo) && StringUtils.isNotBlank(checkinNo)) {
                result.put("code", "10002");
                result.put("msg", "预订号和入住号至少有一个不为空");
                renderJson(result);
                return;
            }
            if (StringUtils.isNotBlank(bookNo) && "1".equals(changeType)) {
                result.put("code", "10002");
                result.put("msg", "预订时不允许分段更换会员卡");
                renderJson(result);
                return;
            }

            FinaExpenseRecord er = null;
            if (StringUtils.isNotBlank(bookNo) && StringUtils.isBlank(checkinNo)) {
                er = finaExpenseRecordService.getRecordByBookNoPart(appNo, baseId, bookNo, name, idCard);
            } else if (StringUtils.isNotBlank(checkinNo) && StringUtils.isBlank(bookNo)) {
                er = finaExpenseRecordService.getRecordByCheckinNoPart(appNo, baseId, uniqueId, checkinNo, name, idCard);
            } else {
                result.put("code", "10002");
                result.put("msg", "预订号和入住号至少有一个为空");
                renderJson(result);
                return;
            }
            if (er == null || !"0".equals(er.getSettleStatus())) {
                result.put("code", "10002");
                result.put("msg", "账单记录不存在或账单处于非未结算状态");
                renderJson(result);
                return;
            }
            Date minStartDate=null;
            for(int i=0;i<jsonArray.size();i++){
                JSONObject obj=jsonArray.getJSONObject(i);
                if(jsonArray.size()==1){
                    minStartDate= DateUtils.parseDate(obj.getString("beginDate"));
                }else{
                    if(minStartDate==null){
                        minStartDate= DateUtils.parseDate(obj.getString("beginDate"));
                    }else{
                        if(DateUtils.compareDays(minStartDate,DateUtils.parseDate(obj.getString("beginDate")))>0){
                            minStartDate=DateUtils.parseDate(obj.getString("beginDate"));
                        }
                    }
                }
            }
            Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",er.getId());
            if(lastSettleDate!=null && !DateUtils.formatDate(lastSettleDate,"yyyy-MM-dd").equals(DateUtils.formatDate(minStartDate,"yyyy-MM-dd"))){
                result.put("code", "10002");
                result.put("msg", "更换会员卡最小开始时间和最后一次结算结束日期不相等");
                renderJson(result);
                return;
            }
            //
            Map<String,Object> map=finaExpenseRecordService.updateCard2(er,uniqueId,jsonArray);
            if((boolean)map.get("flag")){
                result.put("code", "0");
                result.put("msg","更换会员卡成功");
            }else{
                result.put("code", "10002");
                result.put("msg",(String)map.get("msg"));
            }
            renderJson(result);
        } catch (Exception e){
            logger.error("【新】通知财务系统客户更换会员卡异常：[{}]", e);
            result.put("code", "10002");
            result.put("msg", "【新】通知财务系统客户更换会员卡发生异常:");
            renderJson(result);
        }
    }


    /**
     * 修改预订信息
     */
    public void updateBookInfo(){

        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String bookNo = getPara("bookNo");

        //修改信息
        String officeId = getPara("officeId");
        String name = getPara("name");
        String idCard = getPara("idCard");
        String cardNumber = getPara("cardNumber");//会员卡号若更换则修改锁定
        String telephone = getPara("telephone");
        String businessEntityId=getPara("businessEntityId");
        String customerChannel=getPara("customerChannel");
        String bedId = getPara("bedId");//要校验床位是否存在
        String isPrivateRoom = getPara("isPrivateRoom");//判断是否包房:修改锁定使用
        String followData = getPara("followData");//随行数据
        String remark = getPara("remark");
        String isNotDeductCard=getPara("isNotDeductCard");

        logger.info("应用号：[{}],基地ID:[{}],预订号:[{}],需修改的信息：分公司ID:[{}],姓名:[{}],身份证:[{}],会员卡号:[{}],手机号码:[{}],床位ID:[{}]," +
                        "是否包房:[{}],备注:[{}],随行数据:[{}],业务实体id:[{}],客户渠道:[{}],是否扣卡:[{}]",
                appNo,baseId,bookNo,officeId,name,idCard,cardNumber,telephone,bedId,isPrivateRoom,remark,followData,businessEntityId,customerChannel,isNotDeductCard);

        Map<String, Object> result = new HashMap<String, Object>();


        if(StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(bookNo) ||
                StringUtils.isBlank(name) || StringUtils.isBlank(bedId) || StringUtils.isBlank(isPrivateRoom)){
            result.put("code", "10002");
            result.put("msg", "参数缺失");
            renderJson(result);
            return;
        }

        try {
            FinaExpenseRecord er = finaExpenseRecordService.getErByBookNo(appNo,baseId,bookNo);
            if(er == null){
                result.put("code", "10002");
                result.put("msg", "预订账单不存在");
                renderJson(result);
                return;
            }
            if(StrKit.notBlank(isNotDeductCard)){
                er.setIsNotDeductCard(isNotDeductCard);
            }
            synchronized (cardNumber.intern()){
                Map<String,Object> map = finaExpenseRecordService.updateBookInfo(officeId,name,idCard,cardNumber,telephone,bedId,isPrivateRoom,remark
                        ,businessEntityId,customerChannel,er,followData);
                logger.info("修改预订信息处理后的结果:[{}]",JSON.toJSONString(map));
                if(map.containsKey("error")){
                    result.put("code", "10002");
                    result.put("msg", map.get("error"));
                    renderJson(result);
                    return;
                }
                result.put("code", "0");
                result.put("msg", "success");
                renderJson(result);
            }
        } catch (Exception e) {
            logger.error("修改预订信息异常:[{}]",e);
            result.put("code", "10002");
            result.put("msg", "修改预订信息发生异常:"+e.getMessage());
            renderJson(result);
        }
    }



    /**
     * 为会员卡添加扣卡规则
     */
    @Clear(LogInterceptor.class)
    public void tesSaveRuleForCard() {
        String schemeNoTimes = getPara("schemeNoTimes");
        String schemeNoAmount = getPara("schemeNoAmount");
        String cardStr = getPara("cardStr");
        Map<String, Object> result = new HashMap<String, Object>();

        String[] cardArr = cardStr.split(",");
        List<String> cardList = new ArrayList<>();
        List<FinaMembershipCard> cards = new ArrayList<>();
        int flag = 0;
        for (String item : cardArr) {
            FinaMembershipCard card = finaMembershipCardService.getByCardNumber(item);
            if (card == null) {
                flag = 1;
                cardList.add(item);
            } else {
                cards.add(card);
            }

        }

        if (flag == 1) {
            result.put("code", "10001");
            result.put("msg", "会员卡不存在");
            result.put("data", cardList);
            renderJson(result);
            return;
        }

        List<String> prCard = new ArrayList<>();
        for (FinaMembershipCard item : cards) {
            if (StringUtils.isBlank(item.getDeductSchemeId())) {
                if ((item.getBalance() == null || item.getBalance() == 0.00) && item.getConsumeTimes() != null) {
                    MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeBySchemeNo(schemeNoTimes);
                    item.setDeductSchemeId(scheme.getId());
                    item.setUpdateTime(new Date());
                    item.update();
                } else if ((item.getBalance() != null || item.getBalance() == 0.00) && item.getConsumeTimes() == null) {
                    MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeBySchemeNo(schemeNoAmount);
                    item.setDeductSchemeId(scheme.getId());
                    item.setUpdateTime(new Date());
                    item.update();
                } else {
                    prCard.add(item.getCardNumber());
                }
            }
        }

        result.put("code", "10001");
        result.put("msg", "会员卡不存在");
        result.put("data", prCard);
        renderJson(result);
    }


    /**
     * 通过会员卡号获取会员卡年限类型
     */
    @Clear(LogInterceptor.class)
    public void findCardYearLimit() {
        String cardNumber = getPara("cardNumber");
        Map<String, Object> result = new HashMap<>();
        if (StrKit.isBlank(cardNumber)) {
            result.put("code", "10001");
            result.put("msg", "请输入会员卡号");
            renderJson(result);
            return;
        }
        MainCardYearLimit yearLimit = mainCardYearLimitService.findCardYearLimitByCardNumber(cardNumber);
        result.put("code", "0");
        result.put("msg", "success");
        result.put("data", yearLimit);
        renderJson(result);
    }


    /**
     * 根据会员卡号获取扣卡规则
     */
    @Clear(LogInterceptor.class)
    public void getDeductRuleByCardNumber() {

        String cardNumber = getPara("cardNumber");
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isBlank(cardNumber)) {
            result.put("code", "10002");
            result.put("msg", "请输入会员卡号");
            renderJson(result);
            return;
        }

        Long countCard = Db.queryLong("select count(*) from fina_membership_card where del_flag = 0 and expire_flag = 0 and card_number = ?", cardNumber);
        if (countCard <= 0) {
            result.put("code", "10002");
            result.put("msg", "系统不存在该会员卡");
            renderJson(result);
            return;
        }

        Long countExpense = Db.queryLong("select count(*) from fina_expense_record where del_flag = 0 and card_number = ?", cardNumber);
        Long countDetail = Db.queryLong("select count(*) from fina_expense_record_detail where del_flag = 0 and card_number = ?", cardNumber);
        if (countExpense == 0 && countDetail == 0) {
            MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
            if(scheme==null){
                scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
            }
            if (scheme == null || scheme.getOnceConsume() == null || StringUtils.isBlank(scheme.getDeductWay())) {
                result.put("code", "10002");
                result.put("msg", "[" + cardNumber + "]会员卡扣卡规则未定义");
                renderJson(result);
                return;
            }
            if (!DeductType.deductTimes.getKey().equals(scheme.getDeductWay()) && !DeductType.deductAmount.getKey().equals(scheme.getDeductWay())) {
                result.put("code", "10002");
                result.put("msg", "[" + cardNumber + "]会员卡扣卡规则为[" + scheme.getName() + "],暂不支持使用");
                renderJson(result);
                return;
            }
            result.put("code", "10001");
            result.put("msg", "[" + cardNumber + "]会员卡扣卡规则为[" + scheme.getName() + "],请确认");
            renderJson(result);
            return;
        } else {
            result.put("code", "0");
            result.put("msg", "success");
            renderJson(result);
            return;
        }
    }

    /**
     * 接收机构账单
     */
    public void receiveOrgBill(){
        String dataStr= HttpKit.readData(getRequest());
        Map<String,Object> resultMap=new HashMap<>();
        if(!dataStr.startsWith("{") || !dataStr.endsWith("}")){
            resultMap.put("code", "1001");
            resultMap.put("msg", "参数传入异常");
            renderJson(resultMap);
            return;
        }
        JSONObject jsonObject=JSON.parseObject(dataStr);
        FinaMemberSettleMain settleMain=JSON.toJavaObject(jsonObject,FinaMemberSettleMain.class);
        JSONArray jsonArray=jsonObject.getJSONArray("details");
        List<FinaMemberBillDetail> detailList=new ArrayList<>();
        for(int i=0;i<jsonArray.size();i++){
            JSONObject obj=jsonArray.getJSONObject(i);
            FinaMemberBillDetail detail=JSON.toJavaObject(obj,FinaMemberBillDetail.class);
            detailList.add(detail);
        }
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    if(settleMain.save()){
                        Db.batchSave(detailList,detailList.size());
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
                return false;
            }
        });
        if(flag){
            resultMap.put("code", "0");
            resultMap.put("msg", "success");
        }else{
            resultMap.put("code", "10001");
            resultMap.put("msg", "保存失败");
        }
    }


    /**
     *推送取消预订&推迟入住&提前退住违约数据
     */
    public void getBreakData(){
        //跨域解决调用2次问题
        if(getRequest().getMethod().equals("OPTIONS")) {
            renderJson();
            return;
        }

        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String officeId = getPara("officeId");
        String bookNo = getPara("bookNo");
        String checkinNo = getPara("checkinNo");
        String name = getPara("name");
        String punishType = getPara("punishType");
        String cardNumber = getPara("cardNumber");
        String remark = getPara("remark");
        Date bookCreateTime = null;
        Date bookCancelTime = null;
        Date planCheckinTime = null;
        Date actualCheckinTime = null;
        Date planCheckoutTime = null;
        Date actualCheckoutTime = null;
        if(StringUtils.isNotBlank(getPara("bookCreateTime")))bookCreateTime = DateUtil.parse(getPara("bookCreateTime"));
        if(StringUtils.isNotBlank(getPara("bookCancelTime")))bookCancelTime = DateUtil.parse(getPara("bookCancelTime"));
        if(StringUtils.isNotBlank(getPara("planCheckinTime")))planCheckinTime = DateUtil.parse(getPara("planCheckinTime"));
        if(StringUtils.isNotBlank(getPara("actualCheckinTime")))actualCheckinTime = DateUtil.parse(getPara("actualCheckinTime"));
        if(StringUtils.isNotBlank(getPara("planCheckoutTime")))planCheckoutTime = DateUtil.parse(getPara("planCheckoutTime"));
        if(StringUtils.isNotBlank(getPara("actualCheckoutTime")))actualCheckoutTime = DateUtil.parse(getPara("actualCheckoutTime"));

        logger.info("应用号:[{}],基地id:[{}],分公司id:[{}],预订号:[{}],入住号:[{}],推送类型:[{}],会员卡号:[{}],预订创建时间:[{}],预订取消时间:[{}],计划入住时间:[{}],实际入住时间:[{}]," +
                "计划退住时间:[{}],实际退住时间:[{}]",appNo,baseId,officeId,bookNo,checkinNo,punishType,cardNumber,bookCreateTime,bookCancelTime,planCheckinTime,actualCheckinTime,
                planCheckoutTime,actualCheckoutTime);

        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if(StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) ||
                    StringUtils.isBlank(punishType) || StringUtils.isBlank(cardNumber)){
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }

            FinaPunishRecord pr = finaPunishRecordService.setParams(appNo,baseId,officeId,bookNo,checkinNo,
                    punishType,cardNumber,bookCreateTime,bookCancelTime,planCheckinTime,actualCheckinTime,planCheckoutTime,actualCheckoutTime,name,remark);

            Map<String,Object> map = null;
            if("cancel_book".equals(punishType)){
                /*Long count = finaPunishRecordService.isExist(appNo,baseId,bookNo,null,"cancel_book");
                if(count > 0){
                    result.put("code", "10002");
                    result.put("msg", bookNo + "预订号取消预订违约数据已保存");
                    renderJson(result);
                    return;
                }*/
                map = finaPunishRecordService.dealCancelBookPunish(pr);
            }else if("delay_checkin".equals(punishType)){
                /*Long count = finaPunishRecordService.isExist(appNo,baseId,bookNo,null,"delay_checkin");
                if(count > 0){
                    result.put("code", "10002");
                    result.put("msg", bookNo + "预订号推迟入住违约数据已保存");
                    renderJson(result);
                    return;
                }*/
                map = finaPunishRecordService.dealCancelBookPunish(pr);
            }else{
                result.put("code", "10002");
                result.put("msg", "违约数据类型不存在");
                renderJson(result);
                return;
            }

            /*else if("delay_checkin".equals(punishType)){
                Long count = finaPunishRecordService.isExist(appNo,baseId,null,checkinNo,"delay_checkin");
                if(count > 0){
                    result.put("code", "10002");
                    result.put("msg", checkinNo + "入住号推迟入住违约数据已保存");
                    renderJson(result);
                    return;
                }
                map = finaPunishRecordService.dealDelayCheckin(pr);
            }else if("advance_checkout".equals(punishType)){
                Long count = finaPunishRecordService.isExist(appNo,baseId,null,checkinNo,"advance_checkout");
                if(count > 0){
                    result.put("code", "10002");
                    result.put("msg", checkinNo + "入住号提前退住违约数据已保存");
                    renderJson(result);
                    return;
                }
                map = finaPunishRecordService.dealAdvanceCheckout(pr);
            }*/

            if(map == null){
                result.put("code", "10002");
                result.put("msg", "推送类型有误");
                renderJson(result);
                return;
            }

            if(map.containsKey("error")){
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }

            //处理成功
            result.put("code", "0");
            result.put("deductValue",map.get("deductValue"));
            result.put("deductWay",map.get("deductWay"));
            result.put("msg", "success");
            renderJson(result);
        } catch (Exception e) {
            logger.error("推送取消预订&推迟入住&提前退住违约数据异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "推送取消预订&推迟入住&提前退住违约数据发生异常:"+e.getMessage());
            renderJson(result);
        }
    }

    public void saveBreakData(){
        //跨域解决调用2次问题
        if(getRequest().getMethod().equals("OPTIONS")) {
            renderJson();
            return;
        }
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String officeId = getPara("officeId");
        String bookNo = getPara("bookNo");
        String checkinNo = getPara("checkinNo");
        String name = getPara("name");
        String punishType = getPara("punishType");
        String cardNumber = getPara("cardNumber");
        String deductValue = getPara("deductValue");
        String remark = getPara("remark");
        String describe=getPara("describe");

        Date bookCreateTime = null;
        Date bookCancelTime = null;
        Date planCheckinTime = null;
        Date actualCheckinTime = null;
        Date planCheckoutTime = null;
        Date actualCheckoutTime = null;
        if(StringUtils.isNotBlank(getPara("bookCreateTime")))bookCreateTime = DateUtil.parse(getPara("bookCreateTime"));
        if(StringUtils.isNotBlank(getPara("bookCancelTime")))bookCancelTime = DateUtil.parse(getPara("bookCancelTime"));
        if(StringUtils.isNotBlank(getPara("planCheckinTime")))planCheckinTime = DateUtil.parse(getPara("planCheckinTime"));
        if(StringUtils.isNotBlank(getPara("actualCheckinTime")))actualCheckinTime = DateUtil.parse(getPara("actualCheckinTime"));
        if(StringUtils.isNotBlank(getPara("planCheckoutTime")))planCheckoutTime = DateUtil.parse(getPara("planCheckoutTime"));
        if(StringUtils.isNotBlank(getPara("actualCheckoutTime")))actualCheckoutTime = DateUtil.parse(getPara("actualCheckoutTime"));


        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if (StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) ||
                    StringUtils.isBlank(punishType) || StringUtils.isBlank(cardNumber) || StringUtils.isBlank(deductValue)) {
                result.put("code", "10002");
                result.put("msg", "违约参数缺失，保存失败");
                renderJson(result);
                return;
            }

            FinaPunishRecord pr = finaPunishRecordService.setParams(appNo, baseId, officeId, bookNo, checkinNo,
                    punishType, cardNumber, bookCreateTime, bookCancelTime, planCheckinTime, actualCheckinTime, planCheckoutTime, actualCheckoutTime, name, remark);

            //查找是否保存过
            FinaPunishRecord oldPr=null;
            if("cancel_book".equals(punishType) || "delay_checkin".equals(punishType)){
                oldPr= finaPunishRecordService.findRecord(appNo,baseId,bookNo,null,punishType);
            }else{
                result.put("code", "10002");
                result.put("msg", "违约数据类型不存在，保存失败");
                renderJson(result);
                return;
            }

            MainCardDeductScheme scheme=mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
            if(scheme==null){
                scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
            }
            if(scheme==null){
            	result.put("code", "10002");
                result.put("msg", "会员卡扣卡规则未定义，保存失败");
                renderJson(result);
                return;
            }
            
            FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);
            if(card == null){ 
            	result.put("code", "10002");
                result.put("msg", "会员卡系统不存在，保存失败");
                renderJson(result);
                return;
            }
            
            //获取会员卡锁定
            Map<String,Double> lockMap=finaMembershipCardService.getCardLockInfo(card.getCardNumber());
            if(scheme.getDeductWay().equals(DeductType.deductTimes.getKey())){
                pr.setTimes(Double.valueOf(deductValue));
                pr.setAmount(0.0);
                pr.setPoints(0.0);
                pr.setIntegrals(0.0);
                pr.setBeanCoupons(0.0);
                Double diffVal = BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(pr.getTimes())).subtract(BigDecimal.valueOf(lockMap.get("lockConsumeTimes"))).doubleValue();
                if(card.getConsumeTimes() < 0.00 || diffVal < 0){
                	result.put("code", "10002");
                    result.put("msg", "会员卡剩余天数不足，保存失败");
                    renderJson(result);
                    return;
                }
            }else if(scheme.getDeductWay().equals(DeductType.deductAmount.getKey())){
                pr.setAmount(Double.valueOf(deductValue)*scheme.getPrice());
                pr.setTimes(0.0);
                pr.setPoints(0.0);
                pr.setIntegrals(0.0);
                pr.setBeanCoupons(0.0);
                Double diffVal = BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(pr.getAmount())).subtract(BigDecimal.valueOf(lockMap.get("lockBalance"))).doubleValue();
                if (card.getBalance() < 0.00 || diffVal < 0) {
                    result.put("code", "10002");
                    result.put("msg", "会员卡剩余金额不足，保存失败");
                    renderJson(result);
                    return;
                }
            }else if(scheme.getDeductWay().equals(DeductType.deductPoints.getKey())){
                pr.setPoints(Double.valueOf(deductValue));
                pr.setTimes(0.0);
                pr.setAmount(0.0);
                pr.setIntegrals(0.0);
                pr.setBeanCoupons(0.0);
                Double diffVal = BigDecimal.valueOf(card.getConsumePoints()).subtract(BigDecimal.valueOf(pr.getPoints())).subtract(BigDecimal.valueOf(lockMap.get("lockPoints"))).doubleValue();
                if (card.getConsumePoints() < 0.00 || diffVal < 0) {
                    result.put("code", "10002");
                    result.put("msg", "会员卡剩余点数不足，保存失败");
                    renderJson(result);
                    return;
                }
            }else{
                result.put("code", "10002");
                result.put("msg", "会员卡扣除方式不存在，保存失败");
                renderJson(result);
                return;
            }
            
            if(oldPr!=null){
                oldPr.setTimes(pr.getTimes());
                oldPr.setAmount(pr.getAmount());
                oldPr.setPoints(pr.getPoints());
                oldPr.setUpdateTime(new Date());
                oldPr.setRemark(remark);
                oldPr.setDescribe(describe);
                if(oldPr.update()){
                	FinaExpenseRecordDetail recordDetail = finaExpenseRecordDetailService.findFirstDetailsByExpenseId(oldPr.getId());
                	if(recordDetail!=null){
                		FinaExpenseRecordDetail tempRecordDetail = new FinaExpenseRecordDetail();
                		tempRecordDetail.setId(recordDetail.getId());
                    	if(oldPr.getAmount() != null && oldPr.getAmount() > 0){
                    		tempRecordDetail.setAmount(oldPr.getAmount());
                    		tempRecordDetail.setTimes(0.0);
                    		tempRecordDetail.setPoints(0.0);
                    		tempRecordDetail.setIntegrals(0.0);
                        	tempRecordDetail.setBeanCoupons(0.0);
                    	}
                    	if(oldPr.getTimes() != null && oldPr.getTimes() > 0){
                    		tempRecordDetail.setAmount(0.0);
                    		tempRecordDetail.setTimes(oldPr.getTimes());
                    		tempRecordDetail.setPoints(0.0);
                    		tempRecordDetail.setIntegrals(0.0);
                    		tempRecordDetail.setBeanCoupons(0.0);
                    	}
                    	if(oldPr.getPoints() != null && oldPr.getPoints() > 0){
                    		tempRecordDetail.setAmount(0.0);
                    		tempRecordDetail.setTimes(0.0);
                    		tempRecordDetail.setPoints(oldPr.getPoints());
                    		tempRecordDetail.setIntegrals(0.0);
                    		tempRecordDetail.setBeanCoupons(0.0);
                    	}
                    	if(oldPr.getIntegrals() != null && oldPr.getIntegrals() > 0){
                    		tempRecordDetail.setAmount(0.0);
                    		tempRecordDetail.setTimes(0.0);
                    		tempRecordDetail.setPoints(0.0);
                    		tempRecordDetail.setIntegrals(oldPr.getIntegrals());
                    		tempRecordDetail.setBeanCoupons(0.0);
                    	}
                    	if(oldPr.getBeanCoupons() != null && oldPr.getBeanCoupons() > 0){
                    		tempRecordDetail.setAmount(0.0);
                    		tempRecordDetail.setTimes(0.0);
                    		tempRecordDetail.setPoints(0.0);
                    		tempRecordDetail.setIntegrals(0.0);
                    		tempRecordDetail.setBeanCoupons(oldPr.getBeanCoupons());
                    	}
                    	tempRecordDetail.setExplain(oldPr.getDescribe()+" "+oldPr.getRemark());
                    	tempRecordDetail.setUpdateBy(oldPr.getUpdateBy());
                    	tempRecordDetail.setUpdateTime(new Date());
                    	finaExpenseRecordDetailService.update(tempRecordDetail);
                	}
                    result.put("code", "0");
                    result.put("msg", "success");
                    renderJson(result);
                    return;
                }else{
                    result.put("code", "0");
                    result.put("msg", "财务系统更新违约信息失败");
                    renderJson(result);
                    return;
                }
            }else{
                pr.setDescribe(describe);
                if(pr.save()){
                	FinaExpenseRecordDetail recordDetail = new FinaExpenseRecordDetail();
                	recordDetail.setId(IdGen.getUUID());
                	recordDetail.setExpenseId(pr.getId());
                	recordDetail.setCardNumber(cardNumber);
                	recordDetail.setStartTime(DateUtil.parse(DateUtil.today()+" 00:00:00"));
                	recordDetail.setEndTime(DateUtil.parse(DateUtil.today()+" 23:59:59"));
                	if(pr.getAmount() != null && pr.getAmount() > 0){
                		recordDetail.setAmount(pr.getAmount());
                		recordDetail.setTimes(0.0);
                    	recordDetail.setPoints(0.0);
                    	recordDetail.setIntegrals(0.0);
                    	recordDetail.setBeanCoupons(0.0);
                	}
                	if(pr.getTimes() != null && pr.getTimes() > 0){
                		recordDetail.setAmount(0.0);
                		recordDetail.setTimes(pr.getTimes());
                    	recordDetail.setPoints(0.0);
                    	recordDetail.setIntegrals(0.0);
                    	recordDetail.setBeanCoupons(0.0);
                	}
                	if(pr.getPoints() != null && pr.getPoints() > 0){
                		recordDetail.setAmount(0.0);
                		recordDetail.setTimes(0.0);
                    	recordDetail.setPoints(pr.getPoints());
                    	recordDetail.setIntegrals(0.0);
                    	recordDetail.setBeanCoupons(0.0);
                	}
                	if(pr.getIntegrals() != null && pr.getIntegrals() > 0){
                		recordDetail.setAmount(0.0);
                		recordDetail.setTimes(0.0);
                    	recordDetail.setPoints(0.0);
                    	recordDetail.setIntegrals(pr.getIntegrals());
                    	recordDetail.setBeanCoupons(0.0);
                	}
                	if(pr.getBeanCoupons() != null && pr.getBeanCoupons() > 0){
                		recordDetail.setAmount(0.0);
                		recordDetail.setTimes(0.0);
                    	recordDetail.setPoints(0.0);
                    	recordDetail.setIntegrals(0.0);
                    	recordDetail.setBeanCoupons(pr.getBeanCoupons());
                	}
                	recordDetail.setExplain(pr.getDescribe()+" "+pr.getRemark());
                	recordDetail.setStatus("0");
                	recordDetail.setIsSettled("0");
                	recordDetail.setDelFlag(DelFlag.NORMAL);
                	recordDetail.setCreateBy(pr.getCreateBy());
                	recordDetail.setCreateTime(new Date());
                	finaExpenseRecordDetailService.save(recordDetail);
                    result.put("code", "0");
                    result.put("msg", "success");
                    renderJson(result);
                    return;
                }else{
                    result.put("code", "0");
                    result.put("msg", "财务系统保存违约信息失败");
                    renderJson(result);
                    return;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.put("code", "10002");
            result.put("msg", "财务系统保存违约信息异常");
            renderJson(result);
            return;
        }
    }


    /**
     * 获取变更会员卡记录
     */
    public void getChangeCardRecords(){
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String checkinNo = getPara("checkinNo");
        Map<String, Object> result = new HashMap<String, Object>();

        if(StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(checkinNo)){
            result.put("code", "10002");
            result.put("msg", "参数缺失");
            renderJson(result);
            return;
        }

        List<Record> list = finaExpenseRecordDeductionCardService.getChangeCardListByCheckinNo(appNo,baseId,checkinNo);
        if(list == null){
            result.put("code", "0");
            result.put("msg", "success");
            result.put("data", Lists.newArrayList());
            renderJson(result);
        }else{
            result.put("code", "0");
            result.put("msg", "success");
            result.put("data", list);
            renderJson(result);
        }
    }



    /**
     * 提供给旅居：获取会员卡总的充值消费数据
     */
    public void cardRechargeConsumpe(){
        FinaMembershipCard card = getBean(FinaMembershipCard.class,"",true);
        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if(StringUtils.isBlank(card.getCardNumber())){
                result.put("code", "10002");
                result.put("msg", "会员卡未传入");
                renderJson(result);
                return;
            }

            Map<String,Object> map = finaMembershipCardService.getCardRechargeConsumpe(card);
            if(map.containsKey("error")){
                if(map.get("error").toString().contains("购卡金额")){
                    result.put("code", "10003");
                    result.put("msg", map.get("error"));
                    renderJson(result);
                    return;
                }else{
                    result.put("code", "10002");
                    result.put("msg", "会员卡系统内不存在");
                    renderJson(result);
                    return;
                }
            }
            result.put("code", "0");
            result.put("msg", "success");
            result.put("data", map);
            renderJson(result);
        } catch (Exception e) {
            logger.error("获取会员卡总的充值消费数据异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "获取会员卡总的充值消费数据发生异常:"+e.getMessage());
            renderJson(result);
        }
    }



    /**
     * 机构账单保存
     */
    public void orgBillDetailSave(){
        String dataStr=HttpKit.readData(getRequest());
        Map<String,Object> map=new HashMap<>();
        if(!dataStr.startsWith("{") || !dataStr.endsWith("}")){
            map.put("code","10001");
            map.put("msg","参数格式不对");
            renderJson(map);
            return;
        }
        JSONObject jsonObject=JSON.parseObject(dataStr);
        /*if(!jsonObject.containsKey("data") || jsonObject.getJSONArray("data").size()==0){
            map.put("code","10001");
            map.put("msg","明细不能为空");
            renderJson(map);
            return;
        }*/
        boolean flag=finaMemberSettleMainService.orgBillDetailSave(jsonObject);
        if(flag){
            map.put("code","0");
            map.put("msg","success");
        }else{
            map.put("code","10001");
            map.put("msg","保存失败");
        }
        renderJson(map);
    }



    /**
     * 获取旅居发票数据
     */
    public void getInvoiceData(){
        FinaInvoice invoice = getBean(FinaInvoice.class,"",true);
        String invoiceTimeStr = getPara("invoiceTime");
        String detailData = getPara("detailData");
        logger.info("获取旅居发票数据接收参数:发票对象:[{}],发票时间:[{}],明细数组:[{}]",JSON.toJSONString(invoice),invoiceTimeStr,detailData);

        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if(StringUtils.isBlank(invoice.getUniqueId()) || StringUtils.isBlank(invoice.getApplyNo()) || invoice.getConsumeType() == null ||
                    invoice.getInvoiceType() == null || invoice.getInvoiceObj() == null || invoice.getTotalPrice() == null ||
                    StringUtils.isBlank(invoice.getName()) || StringUtils.isBlank(invoice.getMobile()) || StringUtils.isBlank(invoice.getCardNumber()) ||
                    StringUtils.isBlank(invoice.getInvoiceCode()) || StringUtils.isBlank(invoice.getInvoiceNo()) || StringUtils.isBlank(invoiceTimeStr)){
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }
            //去重
            Long count = Db.queryLong("select count(*) from fina_invoice where unique_id = ?",invoice.getUniqueId());
            if(count > 0){
                result.put("code", "10002");
                result.put("msg", invoice.getUniqueId() + "ID对应发票数据系统已存在");
                renderJson(result);
                return;
            }
            invoice.setId(IdGen.getUUID());
            invoice.setInvoiceTime(DateUtil.parse(invoiceTimeStr));
            invoice.setDelFlag("0");
            invoice.setCreateTime(new Date());
            invoice.setUpdateTime(new Date());

            JSONArray jsonArray = null;
            if(StringUtils.isNotBlank(detailData)){
                jsonArray = JSONArray.parseArray(detailData);
            }

            boolean flag = finaInvoiceService.getInvoices(invoice,jsonArray);
            if(flag){
                result.put("code","0");
                result.put("msg","success");
            }else{
                result.put("code","10001");
                result.put("msg","fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("获取旅居发票数据异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "获取旅居发票数据发生异常:"+e.getMessage());
            renderJson(result);
        }
    }



    /**
     * 旅居使用：通知财务账单纸卡和现金记录
     */
    public void saveBillCashRecord(){
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        FinaExpenseCashRecord ec = getBean(FinaExpenseCashRecord.class,"",true);
        logger.info("通知财务账单纸卡和现金记录:应用号:[{}],基地id:[{}],记录对象:[{}]",appNo,baseId,JSON.toJSONString(ec));
        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if(StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(ec.getCheckinNo())
                    || StringUtils.isBlank(ec.getType()) || ec.getAmountNum() == null || ec.getReceiveTime() == null
            || StringUtils.isBlank(ec.getReceiveName()) || StringUtils.isBlank(ec.getSourceSystem()) || StringUtils.isBlank(ec.getUniqueId())){
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }

            FinaExpenseRecord erExist = finaExpenseRecordService.getErByCheckinNoPart(appNo,baseId,ec.getCheckinNo());
            if(erExist == null){
                result.put("code", "10002");
                result.put("msg", ec.getCheckinNo() + "入住号账单不存在");
                renderJson(result);
                return;
            }
            ec.setId(IdGen.getUUID());
            ec.setExpenseId(erExist.getId());
            ec.setDelFlag("0");
            ec.setCreateTime(new Date());
            ec.setUpdateTime(new Date());

            if(ec.save()){
                logger.info("账单纸卡和现金记录新增成功...");
                result.put("code","0");
                result.put("msg","success");
            }else{
                result.put("code","10001");
                result.put("msg","fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("通知财务账单纸卡和现金记录异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "通知财务账单纸卡和现金记录发生异常:"+e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 旅居使用：修改账单纸卡和现金记录
     */
    public void updateBillCashRecord(){
        FinaExpenseCashRecord ec = getBean(FinaExpenseCashRecord.class,"",true);
        logger.info("修改账单纸卡和现金记录：编辑对象:[{}]",JSON.toJSONString(ec));
        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if(StringUtils.isBlank(ec.getType()) || ec.getAmountNum() == null || ec.getReceiveTime() == null||
            StringUtils.isBlank(ec.getReceiveName()) || StringUtils.isBlank(ec.getUniqueId())){
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }

            FinaExpenseCashRecord ecExist = finaExpenseCashRecordService.getObjByUniqueId(ec.getUniqueId());
            if(ecExist == null){
                result.put("code", "10002");
                result.put("msg", "财务系统不存在该现金纸卡记录");
                renderJson(result);
                return;
            }
            ecExist.setType(ec.getType());
            ecExist.setAmountNum(ec.getAmountNum());
            ecExist.setReceiveName(ec.getReceiveName());
            ecExist.setReceiveTime(ec.getReceiveTime());
            ecExist.setRemark(ec.getRemark());
            ecExist.setUpdateTime(new Date());
            if(ecExist.update()){
                logger.info("账单纸卡和现金记录修改成功...");
                result.put("code","0");
                result.put("msg","success");
            }else{
                result.put("code","10001");
                result.put("msg","fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("修改账单纸卡和现金记录异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "修改账单纸卡和现金记录发生异常:"+e.getMessage());
            renderJson(result);
        }
    }




    /**
     * 旅居使用：删除账单纸卡和现金记录
     */
    public void delBillCashRecord(){
        String uniqueId = getPara("uniqueId");
        logger.info("删除账单纸卡和现金记录：[{}]",uniqueId);
        Map<String, Object> result = new HashMap<String, Object>();

        try {
            FinaExpenseCashRecord ecExist = finaExpenseCashRecordService.getObjByUniqueId(uniqueId);
            if(ecExist == null){
                result.put("code", "10002");
                result.put("msg", "财务系统不存在该现金纸卡记录");
                renderJson(result);
                return;
            }

            ecExist.setDelFlag("1");
            ecExist.setUpdateTime(new Date());
            if(ecExist.update()){
                logger.info("删除账单纸卡和现金记录作废成功...");
                result.put("code","0");
                result.put("msg","success");
            }else{
                result.put("code","10001");
                result.put("msg","fail");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("删除账单纸卡和现金记录异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "删除账单纸卡和现金记录发生异常:"+e.getMessage());
            renderJson(result);
        }
    }


    public void updateBook(){
        String bookNo=getPara("bookNo");
        String bookStartDateStr=getPara("bookStartDate");
        String bookEndDateStr=getPara("bookEndDate");
        Map<String,Object> returnMap=new HashMap<>();
        returnMap.put("code","10002");
        if(StrKit.isBlank(bookNo) || StrKit.isBlank(bookStartDateStr) || StrKit.isBlank(bookEndDateStr)){
            returnMap.put("msg","缺少参数");
            renderJson(returnMap);
            return;
        }
        try {
            Map<String,Object> result=finaExpenseRecordService.updateBook(bookNo,bookStartDateStr,bookEndDateStr);
            if((Boolean) result.get("flag")){
                returnMap.put("code","0");
                returnMap.put("msg","success");
            }else{
                returnMap.put("msg",result.get("msg"));
            }
        }catch (Exception e){
            e.printStackTrace();
            returnMap.put("msg","系统错误");
        }
        renderJson(returnMap);
    }




    /**
     *财务判断是否可以换住校验:预订/入住
     */
    public void checkForChangeCheckin(){
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String type = getPara("type");
        String recordNo = getPara("recordNo");
        String isPrivateRoom = getPara("isPrivateRoom");
        String roomId = getPara("roomId");
        String checkoutDateBefore = getPara("checkoutDate");
        logger.info("财务判断是否可以换住接收参数:应用号:[{}],基地ID:[{}],类型:[{}],预订/入住号:[{}],是否包房:[{}],房间ID:[{}],上一条入住的真实退住时间:[{}]",
                appNo,baseId,type,recordNo,isPrivateRoom,roomId,checkoutDateBefore);

        Map<String, Object> result = new HashMap<String, Object>();

        if(true){
            result.put("code", "0");
            result.put("msg","校验成功");
            renderJson(result);
            return;
        }
        try {
            if(StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(recordNo) || StringUtils.isBlank(type) ||
                    StringUtils.isBlank(isPrivateRoom) || StringUtils.isBlank(roomId) || StringUtils.isBlank(checkoutDateBefore)){
                result.put("code", "10002");
                result.put("msg", "参数缺失");
                renderJson(result);
                return;
            }

            FinaExpenseRecord erExist = null;
            if("0".equals(type)){
                erExist = finaExpenseRecordService.getErByBookNo(appNo,baseId,recordNo);
                if(erExist == null || !"0".equals(erExist.getSettleStatus())){
                    result.put("code", "10002");
                    result.put("msg", recordNo + "预订号对应账单记录不存在或结算状态为非未结算");
                    renderJson(result);
                    return;
                }
            }else{
                erExist = finaExpenseRecordService.getErByCheckinNoPart(appNo,baseId,recordNo);
                if(erExist == null || !"0".equals(erExist.getSettleStatus())){
                    result.put("code", "10002");
                    result.put("msg", recordNo + "入住号对应账单记录不存在或结算状态为非未结算");
                    renderJson(result);
                    return;
                }
            }

            //判断房间及床位数量是否存在
            Map<String,String> bedMap = mainBaseRoomService.isHaveRoomAndBedNum(roomId);
            if(bedMap.containsKey("error")){
                result.put("code", "10002");
                result.put("msg", bedMap.get("error"));
                renderJson(result);
                return;
            }
            Double totalBeds = Double.valueOf(bedMap.get("totalBeds"));//床位数
            String roomName = bedMap.get("roomName");
            Map<String,Object> map = finaExpenseRecordService.isToChangeBookAndCheckin(erExist,type,isPrivateRoom,checkoutDateBefore,totalBeds,roomName);
            if(map.containsKey("error")){
                result.put("code", "10002");
                result.put("msg", map.get("error"));
                renderJson(result);
                return;
            }
            if(map.containsKey("suc")){
                result.put("code", "0");
                result.put("msg","校验成功");
            }else{
                result.put("code", "10001");
                result.put("msg", "校验失败");
            }
            renderJson(result);
        } catch (Exception e) {
            logger.error("判断是否可以换住异常：[{}]",e);
            result.put("code", "10002");
            result.put("msg", "判断是否可以换住发生异常:"+e.getMessage());
            renderJson(result);
        }
    }


    /**
     * 续订：延长预定
     */
    public void renewBook(){
        /*String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String officeId = getPara("officeId");
        String bookData = getPara("bookData");*/
        String parentBookNo=getPara("parentBookNo");
        String parentCheckinNo=getPara("parentCheckinNo");
        String bookNo=getPara("bookNo");
        //String cardNumber=getPara("cardNumber");
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");
        String followBookNos=getPara("followBookNos");
        //String isPrivateRoom=getPara("isPrivateRoom");
        //String bedId=getPara("bedId");


        logger.info("续订接收参数：父预定编号：[{}],父入住编号：[{}],预定号：[{}],开始时间：[{}],结束时间:[{}],随行预定号:[{}]",parentBookNo,parentCheckinNo, bookNo, startDate, endDate,followBookNos);

        Map<String, Object> result = new HashMap<String, Object>();
        try {
            if ( (StringUtils.isBlank(parentBookNo) && StringUtils.isBlank(parentCheckinNo))
                    || StringUtils.isBlank(bookNo) || StringUtils.isBlank(startDate) || StringUtils.isBlank(endDate)) {
                result.put("code", "10002");
                result.put("msg", "缺少参数");
                renderJson(result);
                return;
            }

            Map<String,Object> map=finaExpenseRecordService.renewBook(parentBookNo,parentCheckinNo,bookNo,startDate,endDate,followBookNos);
            if((boolean)map.get("flag")){
                result.put("code","0");
                result.put("msg","续订成功");
                renderJson(result);
            }else{
                result.put("code","10002");
                result.put("msg",map.get("msg"));
                renderJson(result);
            }

        }catch (Exception e){
            e.printStackTrace();
            result.put("code","10002");
            result.put("msg","程序异常");
            renderJson(result);
        }

    }

    /**
     * 续订记录入住
     */
    /*public void renewBookCheckin(){
        String appNo=getPara("appNo");
        String parentCheckinNo=getPara("parentCheckinNo");
        String bookNo=getPara("bookNo");
        String checkinNo=getPara("checkinNo");
        logger.info("续订入住接收参数：应用号:[{}],父入住编号：[{}],续订预定号：[{}],续订入住号：[{}]",appNo,baseId,officeId,parentCheckinNo,checkinNo,bookNo, checkinNo);

        Map<String, Object> result = new HashMap<String, Object>();
        result.put("code","10002");
        if(StrKit.isBlank(checkinNo) || StrKit.isBlank(bookNo) || StrKit.isBlank(appNo) || StrKit.isBlank(parentCheckinNo)){
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }

        try{
            Map<String,Object> map=finaExpenseRecordService.renewBookCheckin(checkinNo,bookNo,checkinDate,checkoutDate,followIds);
            if((boolean)map.get("flag")){
                result.put("code","0");
                result.put("msg","续订入住成功");
                renderJson(result);
            }else{
                result.put("code","10002");
                result.put("msg",map.get("msg"));
                renderJson(result);
            }

        }catch (Exception e){
            e.printStackTrace();
            result.put("code","10002");
            result.put("msg","程序异常");
            renderJson(result);
        }
    }

    public void renewBookCheckin2(){
        String checkinNo=getPara("checkinNo");
        String bookNo=getPara("bookNo");
        String checkinDate=getPara("cehckinDate");
        String checkoutDate=getPara("checkoutDate");
        String followIds=getPara("followIds");
        logger.info("续订入住接收参数：父入住编号：[{}],续订预定号：[{}],入住时间：[{}],预计退住时间：[{}],随行人员ids:[{}]",checkinNo,bookNo, bookNo, checkinDate, checkoutDate,followIds);

        Map<String, Object> result = new HashMap<String, Object>();
        result.put("code","10002");
        if(StrKit.isBlank(checkinNo) || StrKit.isBlank(bookNo) || StrKit.isBlank(checkinDate) || StrKit.isBlank(checkoutDate)){
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }

        try{
            Map<String,Object> map=finaExpenseRecordService.renewBookCheckin(checkinNo,bookNo,checkinDate,checkoutDate,followIds);
            if((boolean)map.get("flag")){
                result.put("code","0");
                result.put("msg","续订入住成功");
                renderJson(result);
            }else{
                result.put("code","10002");
                result.put("msg",map.get("msg"));
                renderJson(result);
            }

        }catch (Exception e){
            e.printStackTrace();
            result.put("code","10002");
            result.put("msg","程序异常");
            renderJson(result);
        }
    }*/

    /**
     * 取消续订
     */
    public void cancelRenewBook(){

        String bookNo=getPara("bookNo");
        String followIds=getPara("followIds");
        //String startDate=getPara("startDate");

        logger.info("取消续订接收参数：预定号：[{}],随行id：[{}]", bookNo, followIds);

        Map<String, Object> result = new HashMap<String, Object>();

        try {
            if (StringUtils.isBlank(bookNo)) {
                result.put("code", "10002");
                result.put("msg", "缺少参数");
                renderJson(result);
                return;
            }

            Map<String,Object> map=finaExpenseRecordService.cancelRenewBook(bookNo,followIds);
            if((boolean)map.get("flag")){
                result.put("code","0");
                result.put("msg","取消续订成功");
                renderJson(result);
            }else{
                result.put("code","10002");
                result.put("msg",map.get("msg"));
                renderJson(result);
            }

        }catch (Exception e){
            e.printStackTrace();
            result.put("code","10002");
            result.put("msg","程序异常");
            renderJson(result);
        }
    }

    /**
     * 获取卡余额
     */
    public void getCardBalance(){
        String cards=getPara("cards");
        Map<String,Object> map=new HashMap<>();
        map.put("code","10001");
        if(StrKit.isBlank(cards)){
            map.put("msg","参数为空");
            renderJson(map);
            return;
        }
        JSONArray cardArray=JSON.parseArray(cards);

        List<FinaMembershipCard> cardList=finaMembershipCardService.getCardBalance(cardArray);
        map.put("code","0");
        map.put("data",cardList);
        renderJson(map);
    }
    
    /**
     * 根据会员卡号获取会员卡信息/余额/锁定/赠送/累计扣费等信息
     */
    @EnableCORS
    @Clear(LogInterceptor.class)
    public void getCardByCardNumber(){
        String cardNumber = getPara("cardNumber");
        if(StringUtils.isBlank(cardNumber)){
            renderCodeFailed("会员卡号不能为空!");
            return;
        }
        Record record = finaMembershipCardService.getCardByCardNumber(null, cardNumber, "0");
        if(record==null){
            renderCodeFailed("该会员卡号不存在!");
            return;
        }
        String returnCardStatus = record.getStr("returnCardStatus");
        Double lockBalance = record.getDouble("lockBalance");
        Double lockConsumeTimes = record.getDouble("lockConsumeTimes");
        Double lockIntegrals = record.getDouble("lockIntegrals");
        if(ReturnCardStatus.APPLY.equals(returnCardStatus)){
            renderCodeFailed("该会员卡在退卡申请中,不可退卡!");
            return;
        }
        if(ReturnCardStatus.FINISH.equals(returnCardStatus)){
            renderCodeFailed("该会员卡已退卡,不可退卡!");
            return;
        }
        if(lockBalance!=null && lockBalance>0) {
        	renderCodeFailed("有锁定金额未释放,不可退卡!");
        	return;
        }
        if(lockConsumeTimes!=null && lockConsumeTimes>0) {
        	renderCodeFailed("有锁定天数未释放,不可退卡!");
        	return;
        }
        if(lockIntegrals!=null && lockIntegrals>0) {
        	renderCodeFailed("有锁定积分未释放,不可退卡!");
        	return;
        }
        renderCodeSuccess("success",record);
    }
    
    /**
     * 废弃：转移会员卡-升级（变更会员卡）
     */
    //region Description
    /*public void cardTransferChangeCard(){
        FinaNoticeCardChange fc = getBean(FinaNoticeCardChange.class,"",true);
        Map<String, Object> result = new HashMap<String, Object>();
        if(StringUtils.isBlank(fc.getType()) || StringUtils.isBlank(fc.getAppNo()) || StringUtils.isBlank(fc.getBaseId()) || StringUtils.isBlank(fc.getRecordNo())){
            result.put("code", "10002");
            result.put("msg", "参数缺失");
            renderJson(result);
            return;
        }

        if("1".equals(fc.getType()) && StringUtils.isBlank(fc.getUniqueId())){
            result.put("code", "10002");
            result.put("msg", "入住数据时旅居ID不能为空");
            renderJson(result);
            return;
        }

        FinaNoticeCardChange fcExist = finaNoticeCardChangeService.getRecordNotNotice(fc);
        result.put("code", "0");
        result.put("msg", "success");
        result.put("data", fcExist);
        renderJson(result);
    }*/
    //endregion


    /**
     * 废弃：转移会员卡-升级（通知财务已变更）
     */
    //region Description
    /*public void noticeCardChange(){
        FinaNoticeCardChange fc = getBean(FinaNoticeCardChange.class,"",true);
        Map<String, Object> result = new HashMap<String, Object>();

        if(StringUtils.isBlank(fc.getType()) || StringUtils.isBlank(fc.getAppNo()) || StringUtils.isBlank(fc.getBaseId()) || StringUtils.isBlank(fc.getRecordNo())){
            result.put("code", "10002");
            result.put("msg", "参数缺失");
            renderJson(result);
            return;
        }

        if("1".equals(fc.getType()) && StringUtils.isBlank(fc.getUniqueId())){
            result.put("code", "10002");
            result.put("msg", "入住数据时旅居ID不能为空");
            renderJson(result);
            return;
        }

        boolean flag = finaNoticeCardChangeService.noticeCardChange(fc);
        if(flag){
            result.put("code", "0");
            result.put("msg", "success");
        }else{
            result.put("code", "10001");
            result.put("msg", "fail");
        }
        renderJson(result);
    }*/
    //endregion




    /**
     * 测试：生成扣卡图表统计数据
     */
    //region Description
    /*@Clear(LogInterceptor.class)
    public void testUpdateStatistic() {
        String str = getPara("str");
        Date createDate = getParaToDate("createDate");
        Map<String, Object> result = new HashMap<String, Object>();


        String sqlNum = "select base_id,sum(amount) as total_amount ,sum(consume_times) as total_times from fina_consume_record " +
                "where status = 1 and refund_flag != 1 and consume_type in ('daily_deduction','book_deduction','checkin_deduction') " +
                "and base_id is not null and date(update_time) <= ? " +
                "group by base_id";

        List<Record> numList = Db.find(sqlNum, str);
        if (numList != null && numList.size() > 0) {
            for (Record item : numList) {
                finaDayDeductStatisticService.dealBaseTotal(item);
            }
        }

        Boolean flag = Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    for (Record item : numList) {
                        FinaDayDeductStatistic statistic = new FinaDayDeductStatistic();
                        statistic.setId(IdGen.getUUID());
                        statistic.setBaseId(item.getStr("base_id"));
                        statistic.setStatisticsTime(TimeUtils.dayReduce(createDate));
                        statistic.setDeductTimes(item.getInt("deduct_times"));
                        statistic.setDeductAmount(item.getDouble("deduct_amount"));
                        statistic.setTotalAmount(item.getDouble("total_amount"));
                        statistic.setTotalTimes(item.getInt("total_times"));
                        statistic.setCreateTime(createDate);
                        statistic.save();
                    }
                } catch (Exception e) {
                    return false;
                }
                return true;
            }
        });
        if (flag) {
            result.put("code", "0");
            result.put("msg", "成功");
        } else {
            result.put("code", "0");
            result.put("msg", "失败");
        }
        renderJson(result);
    }*/
    //endregion


    /**
     * 测试：删除扣卡统计数据
     */
    //region Description
    /*@Clear(LogInterceptor.class)
    public void testDelStatistic() {
        Map<String, Object> result = new HashMap<String, Object>();
        int num = Db.delete("delete from fina_day_deduct_statistic");
        if (num > 0) {
            result.put("code", "0");
            result.put("msg", "成功");
        } else {
            result.put("code", "0");
            result.put("msg", "失败");
        }
        renderJson(result);
    }*/
    //endregion


    /**
     * 测试：初始化扣卡汇总表
     */
    //region Description
    /*@Clear(LogInterceptor.class)
    public void testInitCardTotal() {
        Map<String, Object> result = new HashMap<String, Object>();
        List<FinaExpenseRecord> mainList = finaExpenseRecordService.getMainList();
        boolean flag1 = finaExpenseRecordService.getCardTotalLock(mainList);
        boolean flag2 = finaExpenseRecordService.getCardTotalActual(mainList);
        if (flag1 && flag2) {
            result.put("code", "0");
            result.put("msg", "成功");
        } else {
            result.put("code", "10001");
            result.put("msg", "失败");
        }
        renderJson(result);
    }*/
    //endregion


    /**
     * 测试:批量作废账单
     */
    //region Description
    /*public void batchDelErs(){
        Map<String, Object> result = new HashMap<String, Object>();
        List<String> errors=new ArrayList<>();

        List<FinaExpenseRecord> list = finaExpenseRecordService.findBookAndNotSettleErs();
        if(list != null && list.size() > 0){
            for (FinaExpenseRecord item : list) {
                Map<String, Object> map = finaExpenseRecordService.delBookAndCheckin(item);
                if (map.containsKey("error")) {
                    errors.add("异常:"+ map.get("error") +"，ID为"+item.getId());
                }
                if(map.containsKey("result") && (Boolean) map.get("result") == false){
                    errors.add("异常，ID为"+item.getId());
                }
            }
        }

        result.put("code", "0");
        result.put("msg", "成功");
        result.put("data", errors);
        renderJson(result);
    }*/
    //endregion
    
    /**
     * 会员卡保存
     */
    public void memberCardSave(){

    	Map<String, Object> result = new HashMap<String, Object>();
    	MmsMember member = getBean(MmsMember.class,"",true);
        FinaMembershipCard card = getBean(FinaMembershipCard.class,"",true);
        String ruleId = getPara("ruleId");
//        String isGive=getPara("isGive");
        String contractUrl=getPara("contractUrl");//合同
        String contractUrl1=getPara("contractUrl1");//合同1
        String contractUrl2=getPara("contractUrl2");//合同2
        String receiptUrl=getPara("receiptUrl");//收据
        String idCardUrl=getPara("idCardUrl");//证件-前
        String idCardBackUrl=getPara("idCardBackUrl");//证件-后
        String portraitUrl=getPara("portraitUrl");//制卡头像照
        String submitApprovalUrl=getPara("submitApprovalUrl");//呈批
        String contractUrl3=getPara("contractUrl3");//赠送呈批附件
        String cardCollect = getPara("cardCollect");//会员卡收款信息json格式
        String cardRoll = getPara("cardRoll");//会员卡卷绑定json格式
        String cardRollType = getPara("cardRollType");//会员卡卷绑定json格式
        //String cardRollTypeId = getPara("cardRollTypeId");//会员卡卷绑定json格式
        //Integer cardRollTypeNum = getParaToInt("cardRollTypeNum");//会员卡卷绑定json格式

        logger.info("member-json===", JSON.toJSONString(member));
        logger.info("card-json===", JSON.toJSONString(card));
        logger.info("ruleId===", ruleId);
        logger.info("contractUrl===", contractUrl);
        logger.info("contractUrl1===", contractUrl1);
        logger.info("contractUrl2===", contractUrl2);
        logger.info("receiptUrl===", receiptUrl);
        logger.info("idCardUrl===", idCardUrl);
        logger.info("idCardBackUrl===", idCardBackUrl);
        logger.info("portraitUrl===", portraitUrl);
        logger.info("submitApprovalUrl===", submitApprovalUrl);
        logger.info("contractUrl3===", contractUrl3);
        logger.info("cardCollect===", cardCollect);
        logger.info("cardRoll===", cardRoll);

        if(StrKit.notBlank(member.getTelephone())){
        	member.setTelephone(null);
        }
//        if(StringUtils.isBlank(cardCollect) || !cardCollect.startsWith("[") || !cardCollect.endsWith("]")){
//    		renderCodeFailed("cardCollectJson参数格式不正确!");
//        	return;
//    	}
//        
//        if(StringUtils.isBlank(cardRoll) || !cardRoll.startsWith("[") || !cardRoll.endsWith("]")){
//    		renderCodeFailed("cardRollJson参数格式不正确!");
//        	return;
//    	}
        //附件list
        List<CfsFileUpload> fileList = new ArrayList<CfsFileUpload>();
        //合同登记传过来的附件
        for (int i = 0; i < 8; i++) {
			CfsFileUpload file = null;
			if(i==0 && StrKit.notBlank(contractUrl)) {
				file = new CfsFileUpload();
				file.setFileName("合同1");
				file.setFileSuffix(contractUrl.substring(contractUrl.lastIndexOf(".")));
				file.setFileUrl(contractUrl);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==1 && StrKit.notBlank(contractUrl1)) {
				file = new CfsFileUpload();
				file.setFileName("合同2");
				file.setFileSuffix(contractUrl1.substring(contractUrl1.lastIndexOf(".")));
				file.setFileUrl(contractUrl1);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==2 && StrKit.notBlank(contractUrl2)) {
				file = new CfsFileUpload();
				file.setFileName("合同3");
				file.setFileSuffix(contractUrl2.substring(contractUrl2.lastIndexOf(".")));
				file.setFileUrl(contractUrl2);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==3 && StrKit.notBlank(receiptUrl)) {
				file = new CfsFileUpload();
				file.setFileName("收据");
				file.setFileSuffix(receiptUrl.substring(receiptUrl.lastIndexOf(".")));
				file.setFileUrl(receiptUrl);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==4 && StrKit.notBlank(idCardUrl)) {
				file = new CfsFileUpload();
				file.setFileName("证件");
				file.setFileSuffix(idCardUrl.substring(idCardUrl.lastIndexOf(".")));
				file.setFileUrl(idCardUrl);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==5 && StrKit.notBlank(portraitUrl)) {
				file = new CfsFileUpload();
				file.setFileName("制卡头像");
				file.setFileSuffix(portraitUrl.substring(portraitUrl.lastIndexOf(".")));
				file.setFileUrl(portraitUrl);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==6 && StrKit.notBlank(submitApprovalUrl)) {
				file = new CfsFileUpload();
				file.setFileName("呈批");
				file.setFileSuffix(submitApprovalUrl.substring(submitApprovalUrl.lastIndexOf(".")));
				file.setFileUrl(submitApprovalUrl);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}else if(i==7 && StrKit.notBlank(idCardBackUrl)) {
				file = new CfsFileUpload();
				file.setFileName("证件-后");
				file.setFileSuffix(idCardBackUrl.substring(idCardBackUrl.lastIndexOf(".")));
				file.setFileUrl(idCardBackUrl);
				file.setCreateTime(new Date());
				file.setUpdateTime(new Date());
			}
			if(file!=null) {
				fileList.add(file);
			}
		}
        //电子合同传过来的附件
        if(StrKit.notBlank(contractUrl3)) {
        	CfsFileUpload file = new CfsFileUpload();
			file.setFileName("赠送呈批");
			file.setFileSuffix(contractUrl3.substring(contractUrl3.lastIndexOf(".")));
			file.setFileUrl(contractUrl3);
			file.setCreateTime(new Date());
			file.setUpdateTime(new Date());
			fileList.add(file);
		}
        
        //剩余天数/剩余金额/剩余点数必须三选一
//        if(StrKit.isBlank(card.getId()) &&
//    		card.getConsumeTimes() == null &&
//    		card.getBalance() == null &&
//    		card.getConsumePoints()==null){
//
//            renderCodeFailed("剩余天数、剩余点数与余额必须三选一");
//        	return;
//        }
        //购买天数/购买金额必须二选一
        if(StrKit.isBlank(card.getId()) &&
    		card.getBuyRechargeDays() == null &&
    		card.getBuyRechargeAmount() == null){

            renderCodeFailed("购买天数、购买金额必须二选一");
        	return;
        }
        if(StrKit.isBlank(card.getCardTypeId())) {
            renderCodeFailed("cardTypeId卡类别不能为空");
        	return; 
        }
        if(card.getOpenTime()==null) {
        	renderCodeFailed("openTime开卡时间不能为空");
        	return; 
        }
        if(StrKit.isBlank(card.getCardNumber())) {
            renderCodeFailed("cardNumber会员卡号不能为空");
        	return; 
        }
        if(card.getPurchaseCardBalance()==null) {
        	renderCodeFailed("purchaseCardBalance合同金额不能为空");
        	return; 
        }
//        if(card.getContractTimes()==null) {
//        	renderCodeFailed("contractTimes合同天数不能为空");
//        	return; 
//        }
//        if(card.getContractDiscount()==null) {
//        	renderCodeFailed("contractDiscount折扣不能为空");
//        	return; 
//        }
//        if(card.getGiveConsumeTimes()==null) {
//        	renderCodeFailed("giveConsumeTimes赠送天数不能为空");
//        	return; 
//        }
//        if(card.getCollectAmount()==null) {
//        	renderCodeFailed("collectAmount实收金额不能为空");
//        	return; 
//        }
        if(card.getPrice()==null) {
        	renderCodeFailed("price计划售价不能为空");
        	return; 
        }
        if(card.getReferencePrice()==null) {
        	renderCodeFailed("referencePrice实际售价不能为空");
        	return; 
        }
        //接口传过来这些不用传，后台通过购买+赠送来自动计算赋值(2024-11-14)
//        if(card.getBalance()==null) {
//            renderCodeFailed("剩余金额不能为空");
//        	return;
//        }
//        if(card.getConsumeTimes()==null) {
//        	renderCodeFailed("剩余天数不能为空");
//        	return;
//        }
//        if(card.getConsumePoints()==null) {
//        	renderCodeFailed("剩余点数不能为空");
//        	return;
//        }
//        if(card.getCardIntegrals()==null) {
//        	renderCodeFailed("剩余积分不能为空");
//        	return;
//        }
        if(StrKit.isBlank(card.getDeductSchemeId()) && StrKit.isBlank(card.getLongDeductSchemeId())) {
	    	renderCodeFailed("旅居扣费方案和长住扣费方案必须二选一");
	    	return; 
        }
        if(StrKit.notBlank(card.getDeductSchemeId()) && card.getContractTimes()==null){
            renderCodeFailed("合同天数不能为空或者为0");
            return;
        }
        if(StrKit.isBlank(card.getYearLimit())) {
        	renderCodeFailed("年限类型不能为空");
        	return; 
        }
        if(StrKit.isBlank(card.getUseYears())) {
        	renderCodeFailed("使用年限不能为空");
        	return; 
        }
        if(card.getExpireDate()==null) {
        	renderCodeFailed("最大有效期不能为空");
        	return; 
        }
//        if(StrKit.isBlank(card.getEntryCompany())) {
//        	renderCodeFailed("入账公司不能为空");
//        	return; 
//        }
//        if(StrKit.isBlank(card.getIsCash())) {
//        	renderCodeFailed("是否收现金不能为空");
//        	return; 
//        }
//        if(card.getCashMoney()==null) {
//        	renderCodeFailed("现金金额不能为空");
//        	return; 
//        }
//        if(StrKit.isBlank(card.getAccountId())) {
//        	renderCodeFailed("收钱账户不能为空");
//        	return; 
//        }
//        if(card.getAccountMoney()==null) {
//        	renderCodeFailed("转账金额不能为空");
//        	return; 
//        }
        if(StrKit.isBlank(member.getIdcard())) {
        	renderCodeFailed("身份证号不能为空");
        	return; 
        }else {
        	//如果出生日期为空，将根据身份证生成出生日期
        	if(StrKit.isBlank(member.getBirthday())) {
        		final String pattern = "yyyyMMdd";
                final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                if(member.getIdcard().length()==18) {
                	final LocalDate birthDate = LocalDate.parse(member.getIdcard().substring(6, 14), formatter);
                	member.setBirthday(birthDate.toString());
                }
        	}
        }
        if(StrKit.isBlank(card.getTelephone())) {
        	renderCodeFailed("手机号不能为空");
        	return; 
        }
        if(StrKit.notBlank(cardRoll)) {
            MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
            if(!"consume_card".equals(cardType.getTypeCategory())){
                renderCodeFailed("目前只允许消费卡绑定卡券");
                return;
            }
            /*FinaMembershipCardTypeConf cardTypeConf = finaMembershipCardTypeConfService.getByCardTypeId(cardType.getId());
            if(cardTypeConf!=null){
                if(StrKit.notBlank(cardTypeConf.getGiveSchemeId())){
                    card.setGiveSchemeId(cardTypeConf.getGiveSchemeId());
                }
                if(StrKit.notBlank(cardTypeConf.getDeductSchemeId())){
                    card.setDeductSchemeId(cardTypeConf.getDeductSchemeId());
                }

            }*/

            List<FinaCardRoll> cardCollectList = JSON.parseArray(cardRoll, FinaCardRoll.class);
            for(FinaCardRoll roll : cardCollectList) {
                roll.setBindingTime(roll.getCreateTime());
                Record cRoll=Db.findFirst("select * from fina_card_roll where roll_id=? ",roll.getRollId());
                if(cRoll!=null){
                    CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(roll.getRollId());

                    FinaMembershipCard membershipCard=finaMembershipCardService.getCardByNumber(cRoll.getStr("card_id"));
                    renderCodeFailed("["+rollRecord.getRollNumber()+"]卡券已被会员卡["+membershipCard.getCardNumber()+"]绑定");
                    return;
                }
            }
        }



        Ret returnRet = finaMembershipCardService.saveCard(card,member,ruleId,card.getCreateBy(),cardCollect,cardRoll,fileList);

        if(returnRet.isOk()){
            synchronized (FinaApiController.class){
                List<FinaCardRoll> cardRollList=new ArrayList<>();
                if(StrKit.notBlank(cardRollType)){
                    JSONArray cardRollTypeArray=JSON.parseArray(cardRollType);
                    if(cardRollTypeArray!=null && cardRollTypeArray.size()>0){

                        Map<String,Integer> numMap=new HashMap<>();
                        for (int i = 0; i < cardRollTypeArray.size(); i++) {
                            JSONObject jsonObject=cardRollTypeArray.getJSONObject(i);
                            String cardRollTypeId=jsonObject.getString("cardRollTypeId");
                            Integer cardRollTypeNum = jsonObject.getInteger("cardRollTypeNum");

                            if(numMap.containsKey(cardRollTypeId)){
                                numMap.put(cardRollTypeId,cardRollTypeNum+numMap.get(cardRollTypeId));
                            }else{
                                numMap.put(cardRollTypeId,cardRollTypeNum);
                            }
                        }
                        for (String cardRollTypeId : numMap.keySet()) {
                            Integer cardRollTypeNum = numMap.get(cardRollTypeId);
                            List<String> cardRollRecordIdList=Db.query("select id from crm_card_roll_record where del_flag='0' and is_use='0' and is_enable='0' " +
                                    "and id not in (select roll_id from fina_card_roll)  " +
                                    "and card_roll_id=? limit ? ",cardRollTypeId,cardRollTypeNum);
                            if(cardRollRecordIdList.size()>=cardRollTypeNum){
                                for (String recordId : cardRollRecordIdList) {
                                    FinaCardRoll roll=new FinaCardRoll();
                                    roll.setId(IdGen.getUUID());
                                    roll.setCardId(card.getId());
                                    roll.setRollId(recordId);
                                    roll.setBindingTime(new Date());
                                    roll.setBindChannel("cmp");
                                    roll.setCreateBy(card.getCreateBy());
                                    roll.setCreateTime(new Date());
                                    cardRollList.add(roll);
                                }
                            }else{
                                CrmCardRoll crmCardRoll = crmCardRollService.findById(cardRollTypeId);
                                renderCodeFailed(crmCardRoll.getRollName()+"数量不足");
                                return;
                            }
                        }

                    }
                }
                if(cardRollList.size()>0){
                    Db.batchSave(cardRollList,cardRollList.size());
                }
            }
            renderCodeSuccess(returnRet.getStr("msg"));
        }else{
        	renderCodeFailed(returnRet.getStr("msg"));
        }
    }
    
    /**
     * 会员卡更新
     */
    public void memberCardUpdate(){
    	boolean flag =false;
    	String cardNumber = getPara("cardNumber");
    	String contractUrl=getPara("contractUrl");//合同
    	String contractUrl1=getPara("contractUrl1");//合同1
    	String contractUrl2=getPara("contractUrl2");//合同2
    	String receiptUrl=getPara("receiptUrl");//收据
    	String idCardUrl=getPara("idCardUrl");//证件
    	String idCardBackUrl=getPara("idCardBackUrl");//证件-后
    	String portraitUrl=getPara("portraitUrl");//制卡头像照
    	String submitApprovalUrl=getPara("submitApprovalUrl");//呈批
    	
    	if(StrKit.isBlank(cardNumber)) {
    		renderCodeFailed("cardNumber参数不能为空");
    		return; 
    	}
    	
    	FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);
    	if(card==null){
    		renderCodeFailed(cardNumber+"卡号数据为空");
    		return; 
    	}
    	
    	if(StrKit.notBlank(contractUrl)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "合同1");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(contractUrl);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("合同1");
    			fileUpload.setFileSuffix(contractUrl.substring(contractUrl.lastIndexOf(".")));
    			fileUpload.setFileUrl(contractUrl);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(contractUrl1)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "合同2");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(contractUrl1);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("合同2");
    			fileUpload.setFileSuffix(contractUrl1.substring(contractUrl1.lastIndexOf(".")));
    			fileUpload.setFileUrl(contractUrl1);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(contractUrl2)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "合同3");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(contractUrl2);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("合同3");
    			fileUpload.setFileSuffix(contractUrl2.substring(contractUrl2.lastIndexOf(".")));
    			fileUpload.setFileUrl(contractUrl2);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(receiptUrl)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "收据");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(receiptUrl);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("收据");
    			fileUpload.setFileSuffix(receiptUrl.substring(receiptUrl.lastIndexOf(".")));
    			fileUpload.setFileUrl(receiptUrl);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(idCardUrl)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "证件");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(idCardUrl);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("证件");
    			fileUpload.setFileSuffix(idCardUrl.substring(idCardUrl.lastIndexOf(".")));
    			fileUpload.setFileUrl(idCardUrl);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(portraitUrl)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "制卡头像");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(portraitUrl);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("制卡头像");
    			fileUpload.setFileSuffix(portraitUrl.substring(portraitUrl.lastIndexOf(".")));
    			fileUpload.setFileUrl(portraitUrl);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(submitApprovalUrl)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "呈批");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(submitApprovalUrl);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("呈批");
    			fileUpload.setFileSuffix(submitApprovalUrl.substring(submitApprovalUrl.lastIndexOf(".")));
    			fileUpload.setFileUrl(submitApprovalUrl);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(StrKit.notBlank(idCardBackUrl)){
    		CfsFileUpload fileUpload = cfsFileUploadService.findByIdAndName(card.getId(), "证件-后");
    		if(fileUpload!=null){
    			fileUpload.setFileUrl(idCardBackUrl);
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.update();
    		}else{
    			fileUpload.setId(IdGen.getUUID());
    			fileUpload.setRelationId(card.getId());
    			fileUpload.setFileName("证件-后");
    			fileUpload.setFileSuffix(idCardBackUrl.substring(idCardBackUrl.lastIndexOf(".")));
    			fileUpload.setFileUrl(idCardBackUrl);
    			fileUpload.setCreateTime(new Date());
    			fileUpload.setUpdateTime(new Date());
    			flag = fileUpload.save();
    		}
    	}
    	if(flag){
    		renderCodeSuccess("保存成功");
    	}else{
    		renderCodeFailed("保存失败");
    	}
    }
    
    /**
     * 会员卡更新现金消费金额、天数
     */
    public void memberCardUpdateCashInfo(){
    	String cardNumber = getPara("cardNumber");
    	String cashSpendPrice = getPara("cashSpendPrice");//消费单价
    	String cashSpendAmount = getPara("cashSpendAmount");//消费金额
//    	String createBy = getPara("createBy");
    	
    	if(StrKit.isBlank(cardNumber)) {
    		renderCodeFailed("cardNumber参数不能为空");
    		return; 
    	}
    	
    	if(StrKit.isBlank(cashSpendPrice)) {
    		renderCodeFailed("cashSpendPrice参数不能为空");
    		return; 
    	}
    	
    	if(StrKit.isBlank(cashSpendAmount)) {
    		renderCodeFailed("cashSpendAmount参数不能为空");
    		return; 
    	}
    	
    	FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);
    	if(card==null){
    		renderCodeFailed(cardNumber+"卡号数据为空");
    		return; 
    	}
    	final Double spendDays = Double.valueOf(cashSpendAmount)/Double.valueOf(cashSpendPrice);
    	
    	FinaCardSpendCash spendCash = new FinaCardSpendCash();
    	spendCash.setId(IdGen.getUUID());
    	spendCash.setCardId(card.getId());
    	spendCash.setCardNumber(cardNumber);
    	spendCash.setCardTypeId(card.getCardTypeId());
    	spendCash.setSpendPrice(Double.valueOf(cashSpendPrice));
    	spendCash.setSpendAmount(Double.valueOf(cashSpendAmount));
    	spendCash.setSpendDays(spendDays!=null?spendDays:0.0);
//    	spendCash.setCreateBy(createBy);
    	spendCash.setCreateTime(new Date());
    	
    	FinaMembershipCard model = new FinaMembershipCard();
    	model.setId(card.getId());
    	model.setCashSpendPrice(Double.valueOf(cashSpendPrice));
    	model.setCashSpendCountAmount(model.getCashSpendCountAmount()+Double.valueOf(cashSpendAmount));
    	if(spendDays!=null && spendDays>0) {
    		model.setCashSpendCountDays(model.getCashSpendCountDays()+spendDays);
    	}
    	if(spendCash.save() && finaMembershipCardService.update(card)){
    		renderCodeSuccess("保存成功");
    	}else{
    		renderCodeFailed("保存失败");
    	}
    }

    public void getOldMemberFaceImg() throws IOException {
        final String idcard = getPara("idcard");
        if(StrKit.isBlank(idcard)){
            renderCodeFailed("idcard参数不能为空");
            return;
        }
        String faceUrl = "";
        InputStream inputStream = null;
        FileOutputStream outputStream = null;
        try {
            final MmsMember mmsMember = mmsMemberService.getMemberByIdcard(idcard);
            if(mmsMember!=null){
                final String headPic = mmsMember.getHeadPic();
                final String facePic = mmsMember.getFacePic();
                System.out.println("headPic="+headPic);
                System.out.println("facePic="+facePic);
                //如果有人脸图片
                if(StrKit.notBlank(facePic)){
                    faceUrl = facePic;
                }else{//如果没有人脸图片
                    MmsMember model = new MmsMember();
                    model.setId(mmsMember.getId());
                    final String systemPath = "D:\\upload\\";
                    //再判断有没有头像，有就拿来做人脸图片(头像超过200k，需要压缩到200k以下，生成人脸图片和链接)
                    if(StrKit.notBlank(headPic)){
                        String picPath = headPic.substring(headPic.indexOf("com")+4);
                        if(picPath.contains("/")){
                            picPath = picPath.replace("/", "\\");
                        }
                        final String filePath = systemPath + picPath;
                        System.out.println("systemPath-会员头像="+systemPath);
                        System.out.println("picPath-会员头像="+picPath);
                        System.out.println("filePath-会员头像="+filePath);
                        File picFile = new File(filePath);
                        if(picFile!=null && picFile.exists()){
                            //图片大于200kb
                            if((picFile.length() / 1024) > 200 ){
                                final String picName = UUID.randomUUID().toString().replace("-", "") +".jpg";
                                final String picUrl = "http://oss.cncsgroup.com//faceImage/" + picName;
                                final String outPath = systemPath + "faceImage\\" + picName;
                                System.out.println("picName-会员头像="+picName);
                                System.out.println("picUrl-会员头像="+picUrl);
                                System.out.println("outPath-会员头像="+outPath);
                                outputStream = new FileOutputStream(outPath);
                                outputStream.write(PicUtils.compressPicForScale(PicUtils.compressOfQuality(picFile, 1), 150));
                                model.setFacePic(picUrl);
                                faceUrl = picUrl;
                            }else{
                                model.setFacePic(headPic);
                                faceUrl = headPic;
                            }
                        }
                    }else{//没有头像再去找会员卡制卡头像照片来生成人脸图片(制卡头像超过200k，需要压缩到200k以下，生成人脸图片和链接)
                        final String cardHeadPicUrl = Db.queryStr("select u.file_url from cfs_file_upload u " +
                            "left join fina_membership_card c on c.id=u.relation_id " +
                            "left join mms_member m on m.id=c.member_id " +
                            "where m.idcard=? and u.file_name='制卡头像' order by u.create_time desc limit 1 ", idcard);
                        if(StrKit.notBlank(cardHeadPicUrl)){
                            String picPath = cardHeadPicUrl.substring(cardHeadPicUrl.indexOf("com")+5);
                            if(picPath.contains("/")){
                                picPath = picPath.replace("/", "\\");
                            }
                            final String filePath = systemPath + picPath;
                            System.out.println("systemPath-制卡头像="+systemPath);
                            System.out.println("picPath-制卡头像="+picPath);
                            System.out.println("filePath-制卡头像="+filePath);
                            File picFile = new File(filePath);
                            if(picFile!=null && picFile.exists()){
                                //图片大于200kb
                                if((picFile.length() / 1024) > 200 ){
                                    final String picName = UUID.randomUUID().toString().replace("-", "") +".jpg";
                                    final String picUrl = "http://oss.cncsgroup.com//faceImage/" + picName;
                                    final String outPath = systemPath + "faceImage\\" + picName;
                                    System.out.println("picName-制卡头像="+picName);
                                    System.out.println("picUrl-制卡头像="+picUrl);
                                    System.out.println("outPath-制卡头像="+outPath);
                                    outputStream = new FileOutputStream(outPath);
                                    outputStream.write(PicUtils.compressPicForScale(PicUtils.compressOfQuality(picFile, 1), 150));
                                    model.setFacePic(picUrl);
                                    faceUrl = picUrl;
                                }else{
                                    model.setFacePic(cardHeadPicUrl);
                                    faceUrl = cardHeadPicUrl;
                                }
                            }
                        }
                    }
                    model.update();
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            if(inputStream!=null){
                inputStream.close();
            }
            if(outputStream!=null){
                outputStream.close();
            }
        }
        renderCodeSuccess("success", faceUrl);
    }

    public void batchUpdateContractNumber(){
        final String contractNumber = getPara("contractNumber");
        final String cardNumArray = getPara("cardNumArray");
        final String updateBy = getPara("updateBy");
        logger.info("contractNumber===", contractNumber);
        logger.info("cardNumArray===", cardNumArray);
        logger.info("updateBy===", updateBy);
        if(StrKit.isBlank(contractNumber)) {
    		renderCodeFailed("contractNumber参数不能为空");
    		return; 
    	}
        if(StrKit.isBlank(cardNumArray)) {
    		renderCodeFailed("cardNumArray参数不能为空");
    		return;
    	}else{
            if(!cardNumArray.startsWith("[") || !cardNumArray.endsWith("]")){
                renderCodeFailed("cardNumArray参数格式不正确，请以[\"1\",\"2\",\"3\"]来传输");
            }
        }
        if(StrKit.isBlank(updateBy)) {
    		renderCodeFailed("updateBy参数不能为空");
    		return;
    	}
        final String cardNumStr = cardNumArray.replace("[", "{").replace("]", "}");
        logger.info("cardNumStr===", cardNumStr);
        final List<String> cardNums = Arrays.asList(new String[]{cardNumStr});
        logger.info("cardNums.toString===", cardNums.toString());
    	if(finaMembershipCardService.updateContractNumByCardNum(contractNumber, cardNums, updateBy)){
    		renderCodeSuccess("操作成功!");
    	}else{
    		renderCodeFailed("操作失败!");
    	}
    }

    public void memberCardRemove(){
        final String cardNumber=getPara("cardNumber");
        if(StrKit.isBlank(cardNumber)) {
    		renderCodeFailed("cardNumber参数不能为空");
    		return;
    	}
        FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);
    	if(card==null){
    		renderCodeFailed("找不到该会员卡号数据");
    		return;
    	}
//    	Record record = finaMembershipCardService.getCardByCardNumber(null, cardNumber, "0");
//        Double lockBalance = record.getDouble("lockBalance");
//        Double lockConsumeTimes = record.getDouble("lockConsumeTimes");
//        Double lockConsumePoints = record.getDouble("lockConsumePoints");
//        Double lockIntegrals = record.getDouble("lockIntegrals");
//        Double lockBeanCoupons = record.getDouble("lockBeanCoupons");
//        if(lockBalance!=null && lockBalance>0) {
//        	renderCodeFailed("有锁定金额未释放,操作失败!");
//        	return;
//        }
//        if(lockConsumeTimes!=null && lockConsumeTimes>0) {
//        	renderCodeFailed("有锁定天数未释放,操作失败!");
//        	return;
//        }
//        if(lockConsumePoints!=null && lockConsumePoints>0) {
//        	renderCodeFailed("有锁定点数未释放,操作失败!");
//        	return;
//        }
//        if(lockIntegrals!=null && lockIntegrals>0) {
//        	renderCodeFailed("有锁定积分未释放,操作失败!");
//        	return;
//        }
//        if(lockBeanCoupons!=null && lockBeanCoupons>0) {
//        	renderCodeFailed("有锁定豆豆券未释放,操作失败!");
//        	return;
//        }
        //判断是否有未结算订单存在
		Long countExpense = Db.queryLong("select count(*) from fina_expense_record where card_number = ?", cardNumber);
		Long countPunish = Db.queryLong("select count(*) from fina_punish_record where card_number = ?", cardNumber);
		Long countConsume = Db.queryLong("select count(*) from fina_consume_record where card_number = ?", cardNumber);
		Long countSettleDetail = Db.queryLong("select count(*) from fina_settle_detail where card_number=? ", cardNumber);

		if(countExpense > 0 || countConsume > 0 || countSettleDetail>0){
			renderCodeFailed(cardNumber + "会员卡有消费账单，操作失败!");
        	return;
		}
		if(countPunish > 0){
			renderCodeFailed(cardNumber + "会员卡有违约账单，操作失败!");
        	return;
		}
    	if(finaMembershipCardService.deleteById(card.getId())){
    		renderCodeSuccess("操作成功!");
    	}else{
    		renderCodeFailed("操作失败!");
    	}
    }

    @EnableCORS
    public void getCardByNumberArray(){
        String cardNumberArray=getPara("cardNumberArray");
        if(StrKit.isBlank(cardNumberArray)){
            renderCodeFailed("参数不能为空");
            return;
        }
        if(!cardNumberArray.startsWith("[") || !cardNumberArray.endsWith("]")){
            renderCodeFailed("参数格式不正确");
            return;
        }
        JSONArray array=JSON.parseArray(cardNumberArray);
        if(array.size()==0){
            renderCodeFailed("参数不能为空");
            return;
        }
        String str="";
        for(int i=0;i<array.size();i++){
            str+="?,";
        }
        str=str.substring(0,str.length()-1);

		String sql = "select a.card_number as cardNumber,c.name as yearLimitName,c.dict_value as yearLimit,"
			+ "c.bed_book_limit as bedBookLimit,b.card_type cardTypeName,b.id cardTypeId,a.price,"
			+ "a.reference_price as referencePrice from fina_membership_card a "
			+ "left join main_membership_card_type b on a.card_type_id=b.id "
			+ "left join main_card_year_limit c on c.dict_value=a.year_limit "
			+ "where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' and a.card_number in (" + str + ")";
        List<Record> recordList=Db.find(sql,array.toArray());
        renderCodeSuccess("success",recordList);
    }

    @EnableCORS
    public void getDictListByType(){
        String type=getPara("type");
        if(StrKit.isBlank(type)){
            renderCodeFailed("类型不能为空");
            return;
        }
        List<Dict> dictList=dictService.getListByTypeOnUse(type);
        renderCodeSuccess("success",dictList);
    }

    @EnableCORS
    public void getCardDeductSchemeList(){
        List<MainCardDeductScheme> sojournSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.SOJOURN_SCHEME.getKey());
        renderCodeSuccess("success",sojournSchemeList);
    }

    @EnableCORS
    public void getLongCardDeductSchemeList(){
        List<MainCardDeductScheme> longSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.LONG_SCHEME.getKey());
        renderCodeSuccess("success",longSchemeList);
    }

    @EnableCORS
    public void getCardYearLimitList(){
        List<MainCardYearLimit> cardYearLimitList=mainCardYearLimitService.findCardYearLimitList();
        renderCodeSuccess("success",cardYearLimitList);
    }

    @EnableCORS
    public void getCardAccountList(){
    	final String payWay = getPara("payWay");
        List<FinaCardAccount> accountList = finaCardAccountService.findList(payWay);
        renderCodeSuccess("success",accountList);
    }

    @EnableCORS
    public void getCardTypeList(){
        MainMembershipCardType cardType=getBean(MainMembershipCardType.class,"",true);

        List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(cardType);
        renderCodeSuccess("success",typeList);
    }
    
    @EnableCORS
    public void getCardTypeConfig(){
    	String cardTypeId = getPara("cardTypeId");
    	FinaMembershipCardTypeConf cardTypeConfig = finaMembershipCardTypeConfService.getByCardTypeId(cardTypeId);
        if(cardTypeConfig!=null){
            MainMembershipCardType cardType=mainMembershipCardTypeService.findById(cardTypeConfig.getCardTypeId());
            cardTypeConfig.setRemark(cardType.getRemark());
        }
    	renderCodeSuccess("success",cardTypeConfig);
    }

    /**
     * 根据会员卡号获取会员卡信息
     */
    @EnableCORS
    public void memberCardInfo(){
        final String cardNumber = getPara("cardNumber");
        Map<String, Object> returnData = new HashMap<String, Object>();
        if(StringUtils.isBlank(cardNumber)){
        	renderCodeFailed("会员卡号不能为空");
        	return;
        }
        Record record = finaMembershipCardService.getCardByCardNumber("",cardNumber, "0");
        if(record==null){
            renderCodeFailed("该会员卡号不存在");
            return;
        }
        final String cardId = record.getStr("id");
        final String cardIsIntegral = record.getStr("isIntegral");
        final Double beanCoupons = record.getDouble("beanCoupons");
        final Double waitRechargeBeanCoupons = Db.queryDouble("select sum(consume_bean_coupons)totalRechargeBeanCoupons from fina_card_recharge where del_flag='0' and cancel_flag='0' and is_review='0' and card_id=?", cardId);
        final Double waitDeductBeanCoupons = record.getDouble("lockBeanCoupons");
        returnData.put("cardId", cardId);
        returnData.put("cardIsIntegral", cardIsIntegral);
        returnData.put("beanCoupons", beanCoupons);
        returnData.put("waitRechargeBeanCoupons", waitRechargeBeanCoupons);
        returnData.put("waitDeductBeanCoupons", waitDeductBeanCoupons);
        renderCodeSuccess("success",returnData);
    }
    
    /**
     * 获取会员卡充值审核记录
     */
    @EnableCORS
    public void rechargeReviewPage(){
    	String cardNumber = getPara("cardNumber");
        String fullName = getPara("fullName");
        String isReview = getPara("isReview");
        String delFlag = getPara("delFlag");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        String createBy = getPara("createBy");
        String isAuto=getPara("isAuto");
        Page<Record> page = finaCardRechargeService.rechargePage(cardNumber,fullName,isReview,delFlag,startDate,endDate,createBy,"desc",isAuto,getParaToInt("page"),getParaToInt("limit"));
        renderJson(new DataTable<Record>(page));
    }
    
    /**
     * 获取会员卡扣除审核记录
     */
    @EnableCORS
    public void deductReviewPage(){
    	FinaCardDeduct model = getBean(FinaCardDeduct.class, "", true);
        Page<FinaCardDeduct> modelPage = finaCardDeductService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaCardDeduct>(modelPage));
    }
    
    /**
     * 会员卡充值保存
     */
    @EnableCORS
    public void memberCardRecharge(){
    	final String jsonDataStr = HttpKit.readData(getRequest());
        if(!jsonDataStr.startsWith("{") || !jsonDataStr.endsWith("}")){
            renderCodeFailed("参数格式不正确，应为json格式!");
            return;
        }
    	FinaCardRecharge cardRecharge = JSON.parseObject(jsonDataStr, FinaCardRecharge.class);
        logger.info("会员卡充值入参：[{}]",JSON.toJSONString(cardRecharge));
        
        if("1".equals(cardRecharge.getIsIncome())) {
        	if(StrKit.isBlank(cardRecharge.getAccountId())&&"0".equals(cardRecharge.getIsCash())) {
        		renderCodeFailed("收入性充值，请至少选择一项收钱方式!");
                return;
        	}
        }

        FinaMembershipCard card=finaMembershipCardService.findById(cardRecharge.getCardId());
        if(card==null){
            renderCodeFailed("该会员卡不存在!");
            return;
        }
        if("1".equals(card.getIsLock())){
            renderCodeFailed("该会员卡为锁定状态，充值失败!");
            return;
        }
        if(cardRecharge.getConsumeBeanCoupons()!=null && cardRecharge.getConsumeBeanCoupons()>0) {
        	MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
        	if(cardType!=null) {
        		if(!"1".equals(cardType.getIsIntegral())) {
        			renderCodeFailed("该会员卡不是积分卡，不能充值豆豆券!");
                    return;
        		}
        	}else {
        		renderCodeFailed("该会员卡没有卡类别，充值失败!");
                return;
        	}
        }

        if((cardRecharge.getAmount() != null && cardRecharge.getAmount() == 0 )
                || (cardRecharge.getConsumeTimes() != null && cardRecharge.getConsumeTimes() == 0)
                || (cardRecharge.getConsumePoints() != null && cardRecharge.getConsumePoints() == 0)
                || (cardRecharge.getConsumeIntegral() != null && cardRecharge.getConsumeIntegral() == 0)
                || (cardRecharge.getConsumeBeanCoupons() != null && cardRecharge.getConsumeBeanCoupons() == 0)
        ){
            renderCodeFailed("充值金额、天数、点数、积分或豆豆券不能为0!");
            return;
        }
        if((cardRecharge.getGiveAmount() != null && cardRecharge.getGiveAmount() == 0)
                ||(cardRecharge.getGiveConsumeTimes() != null && cardRecharge.getGiveConsumeTimes() == 0)
                ||(cardRecharge.getGiveConsumePoints() != null && cardRecharge.getGiveConsumePoints() == 0)
                ||(cardRecharge.getGiveConsumeIntegral() != null && cardRecharge.getGiveConsumeIntegral() == 0)
                ||(cardRecharge.getGiveBeanCoupons() != null && cardRecharge.getGiveBeanCoupons() == 0)
        ){
            renderCodeFailed("赠送金额、天数、点数、积分或豆豆券不能为0!");
            return;
        }

        //若金额或天数为空，则置为0/若赠送金额或天数为空，则置为0
        if(cardRecharge.getAmount() == null)cardRecharge.setAmount(0.00);
        if(cardRecharge.getGiveAmount() == null)cardRecharge.setGiveAmount(0.00);
        if(cardRecharge.getConsumeTimes() == null)cardRecharge.setConsumeTimes(0.0);
        if(cardRecharge.getGiveConsumeTimes() == null)cardRecharge.setGiveConsumeTimes(0.0);
        if(cardRecharge.getConsumePoints()==null)cardRecharge.setConsumePoints(0.0);
        if(cardRecharge.getGiveConsumePoints()==null)cardRecharge.setGiveConsumePoints(0.0);
        if(cardRecharge.getConsumeIntegral()==null)cardRecharge.setConsumeIntegral(0.0);
        if(cardRecharge.getGiveConsumeIntegral()==null)cardRecharge.setGiveConsumeIntegral(0.0);
        if(cardRecharge.getConsumeBeanCoupons()==null)cardRecharge.setConsumeBeanCoupons(0.0);
        if(cardRecharge.getGiveBeanCoupons()==null)cardRecharge.setGiveBeanCoupons(0.0);


        cardRecharge.setRechargeWay(RechargeWay.single.getKey());
        boolean flag = finaCardRechargeService.saveRecharge(cardRecharge, cardRecharge.getCreateBy());
        logger.info("会员卡充值serviceImpl处理结果：[{}]",flag);
        if(flag){
            renderCodeSuccess("充值成功");
        }else{
            renderCodeSuccess("充值失败");
        }
    }
    
    /**
     * 豆豆券扣除保存
     */
    @EnableCORS
    public void doudouDeduct(){
    	final String jsonDataStr = HttpKit.readData(getRequest());
        if(!jsonDataStr.startsWith("{") || !jsonDataStr.endsWith("}")){
            renderCodeFailed("参数格式不正确，应为json格式!");
            return;
        }
        FinaCardDeduct model = JSON.parseObject(jsonDataStr, FinaCardDeduct.class);
        //此处为空时，置为0
        if(model.getDeductAmount() == null)model.setDeductAmount(0.00);
        if(model.getDeductTimes() == null)model.setDeductTimes(0.00);
        if(model.getDeductPoints()==null)model.setDeductPoints(0.00);
        if(model.getDeductIntegral()==null)model.setDeductIntegral(0.00);
        FinaMembershipCard card = finaMembershipCardService.findById(model.getCardId());
        if(card!=null) {
        	MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
        	if(cardType!=null) {
        		if("1".equals(cardType.getIsIntegral())) {
        			boolean flag = false;
        			if(StrKit.isBlank(model.getId())) {
        				model.setId(IdGen.getUUID());
        				model.setCancelFlag("0");
        				model.setIsReview("0");
        				model.setDelFlag(DelFlag.NORMAL);
        				model.setCreateTime(new Date());
        				model.setUpdateTime(new Date());
        				flag = model.save();
        			}else {
        				model.setUpdateTime(new Date());
        				flag = model.update();
        			}
        			if (flag) {
        				 renderCodeSuccess("操作成功!");
        			} else {
        				renderCodeFailed("操作失败!");
        			}
        		}else {
        			renderCodeFailed("该会员卡不是积分卡，不能扣除豆豆券!");
        		}
        	}else {
        		renderCodeFailed("该会员卡没有卡类别，扣除失败!");
        	}
        }else {
        	renderCodeFailed("该会员卡不存在，扣除失败!");
        }
    }

    @EnableCORS
    public void memberCardDeduction(){
        String cardNumber= getPara("cardNumber");
        String baseId=getPara("baseId");
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");
        String amountStr=getPara("amount");
        String timesStr=getPara("times");
        String pointsStr=getPara("points");
        String integralsStr=getPara("integrals");
        String beanCouponsStr=getPara("beanCoupons");
        String describe=getPara("describe");
        String expenseId=getPara("expenseId");
        String orderNo=getPara("orderNo");
        if(StrKit.isBlank(cardNumber)){
            renderCodeFailed("会员卡不能为空");
            return;
        }
        logger.info("会员卡扣卡接口参数：cardNumber:"+cardNumber+",baseId:"+baseId+",startDate:"+startDate+",endDate:"+endDate+",amount:"+amountStr+",timesStr:"+timesStr+",pointsStr:"+pointsStr
                +",integralsStr:"+integralsStr+",beanCouponsStr:"+beanCouponsStr+",describe:"+describe+",expenseId:"+expenseId+",orderNo:"+orderNo);
        double amount=0.0;
        double times=0.0;
        double points=0.0;
        double integrals=0.0;
        double beanCoupons=0.0;
        try {
            amount=Double.valueOf(amountStr);
            times=Double.valueOf(timesStr);
            points=Double.valueOf(pointsStr);
            integrals=Double.valueOf(integralsStr);
            beanCoupons=Double.valueOf(beanCouponsStr);
        }catch (Exception e){
            e.printStackTrace();
            renderCodeFailed("请输入正确的扣费值");
            return;
        }
        FinaMembershipCard card=finaMembershipCardService.getCardByNumber(cardNumber);
        if(card==null){
            renderCodeFailed(cardNumber+"会员卡不存在");
            return;
        }
        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
        if(cardType==null){
            renderCodeFailed(cardNumber+"会员卡类型未设置");
            return;
        }
        MainCardDeductScheme deductScheme= mainCardDeductSchemeService.getSchemeByCardNumber(card.getCardNumber());
        if(deductScheme==null){
            deductScheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(card.getCardNumber());
        }
        if(deductScheme==null){
            renderCodeFailed("会员卡扣除方式不存在");
            return;
        }

        double afterConsumeTimes=0.0;
        double afterCardAmount=0.0;
        double afterCardIntegral=0.0;
        double afterCardPoints=0.0;
        double afterCardBeanCoupons=0.0;

        Map<String,Double> lockInfo=finaMembershipCardService.getCardLockInfo(card.getCardNumber());

        FinaCardTransactions cardTransactions=new FinaCardTransactions();
        cardTransactions.setId(IdGen.getUUID());
        cardTransactions.setCardId(card.getId());
        cardTransactions.setTransactionNo(orderNo);
        cardTransactions.setExpenseId(expenseId);
        cardTransactions.setType("daily_deduction");
        cardTransactions.setInOutFlag("2");
        cardTransactions.setStartDate(DateUtils.parseDate(startDate));
        cardTransactions.setEndDate(DateUtils.parseDate(endDate));
        cardTransactions.setAmount(amount);
        cardTransactions.setTimes(times);
        cardTransactions.setPoints(points);
        cardTransactions.setIntegrals(integrals);
        cardTransactions.setBeanCoupons(beanCoupons);
        cardTransactions.setIsHidden("0");
        cardTransactions.setDescribe(describe);
        cardTransactions.setDealTime(new Date());
        cardTransactions.setStartDate(new Date());
        cardTransactions.setEndDate(new Date());


        if(card.getConsumeTimes()==null){
            card.setConsumeTimes(0.0);
        }
        if(card.getBalance()==null){
            card.setBalance(0.0);
        }
        if(card.getConsumePoints()==null){
            card.setConsumePoints(0.0);
        }
        if(card.getCardIntegrals()==null){
            card.setCardIntegrals(0.0);
        }
        if(card.getBeanCoupons()==null){
            card.setBeanCoupons(0.0);
        }

        if("1".equals(cardType.getIsIntegral())){
            if(amount>0.0){
                renderCodeFailed(cardNumber+"积分卡暂时不支持扣除金额");
                return;
            }
            if(points>0.0){
                renderCodeFailed(cardNumber+"积分卡暂时不支持扣除点数");
                return;
            }
            if(integrals>0.0){
                //积分
                afterCardIntegral= BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(integrals)).doubleValue();
                if(BigDecimal.valueOf(afterCardIntegral).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals")))
                        .compareTo(BigDecimal.ZERO)==-1){
                    renderCodeFailed(cardNumber+"会员卡积分不足,本次扣卡积分:"+integrals+",当前可用积分为:"+
                            BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(lockInfo.get("lockIntegrals"))).doubleValue()
                            +"，不可用积分(锁定)："+lockInfo.get("lockIntegrals"));
                    return;
                }
                card.setCardIntegrals(afterCardIntegral);
            }
            if(times>0.0){
                //天数
                afterConsumeTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(times)).doubleValue();
                if(BigDecimal.valueOf(afterConsumeTimes).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).compareTo(BigDecimal.ZERO)==-1){
                    renderCodeFailed(cardNumber+"会员卡剩余天数不足,本次扣卡天数:"+times+",当前可用天数为:"+
                            BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).doubleValue()
                            +"，不可用天数(锁定)："+lockInfo.get("lockConsumeTimes"));
                    return;
                }
                card.setConsumeTimes(afterConsumeTimes);
            }

            if(beanCoupons>0.0){
                //豆豆券
                afterCardBeanCoupons=BigDecimal.valueOf(card.getBeanCoupons()).subtract(BigDecimal.valueOf(beanCoupons)).doubleValue();
                if(BigDecimal.valueOf(afterCardBeanCoupons).subtract(BigDecimal.valueOf(lockInfo.get("lockBeanCoupons"))).compareTo(BigDecimal.ZERO)==-1){
                    renderCodeFailed(cardNumber+"会员卡剩余豆豆券不足,本次扣卡豆豆券:"+beanCoupons+",当前可用豆豆券为:"+
                            BigDecimal.valueOf(card.getBeanCoupons()).subtract(BigDecimal.valueOf(lockInfo.get("lockBeanCoupons"))).doubleValue()
                            +"，不可用豆豆券(锁定)："+lockInfo.get("lockBeanCoupons"));
                    return;
                }
                card.setBeanCoupons(afterCardBeanCoupons);
            }
            cardTransactions.setIntegralsSnapshot(card.getCardIntegrals());
            cardTransactions.setTimesSnapshot(card.getConsumeTimes());
            cardTransactions.setBeanCouponsSnapshot(card.getBeanCoupons());
        }else{
            if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
                if(amount>0.0 || integrals>0.0 || points>0.0 || beanCoupons>0.0){
                    renderCodeFailed(cardNumber+"天数卡暂时不支持扣除金额、积分、点数、豆豆券");
                    return;
                }
                afterConsumeTimes=BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(times)).doubleValue();
                if(BigDecimal.valueOf(afterConsumeTimes).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).compareTo(BigDecimal.ZERO)==-1){
                    renderCodeFailed(cardNumber+"会员卡剩余天数不足,本次扣卡天数:"+times+",当前可用天数为:"+
                            BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lockInfo.get("lockConsumeTimes"))).doubleValue()
                            +"，不可用天数(锁定)："+lockInfo.get("lockConsumeTimes"));
                    return;
                }
                card.setConsumeTimes(afterConsumeTimes);
                cardTransactions.setTimesSnapshot(card.getConsumeTimes());
            }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                if(times>0.0 || integrals>0.0 || points>0.0 || beanCoupons>0.0){
                    renderCodeFailed(cardNumber+"金额卡暂时不支持扣除天数、积分、点数、豆豆券");
                    return;
                }
                afterCardAmount=BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(amount)).doubleValue();
                if(BigDecimal.valueOf(afterCardAmount).subtract(BigDecimal.valueOf(lockInfo.get("lockBalance"))).compareTo(BigDecimal.ZERO)==-1){
                    renderCodeFailed(cardNumber+"会员卡剩余金额不足,本次扣卡金额:"+amount+",当前可用金额为:"+
                            BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(lockInfo.get("lockBalance"))).doubleValue()
                            +"，不可用金额(锁定)："+lockInfo.get("lockBalance"));
                    return;
                }
                card.setBalance(afterCardAmount);
                cardTransactions.setAmountSnapshot(card.getBalance());
            }else if(DeductType.deductPoints.getKey().equals(deductScheme.getDeductWay())){
                if(amount>0.0 || integrals>0.0 || points>0.0 || beanCoupons>0.0){
                    renderCodeFailed(cardNumber+"点数卡暂时不支持扣除金额、积分、天数、豆豆券");
                    return;
                }
                afterCardPoints=BigDecimal.valueOf(card.getConsumePoints()).subtract(BigDecimal.valueOf(points)).doubleValue();
                if(BigDecimal.valueOf(afterCardPoints).subtract(BigDecimal.valueOf(lockInfo.get("lockPoints"))).compareTo(BigDecimal.ZERO)==-1){
                    renderCodeFailed(cardNumber+"会员卡剩余点数不足,本次扣卡点数:"+amount+",当前可用点数为:"+
                            BigDecimal.valueOf(card.getConsumePoints()).subtract(BigDecimal.valueOf(lockInfo.get("lockPoints"))).doubleValue()
                            +"，不可用点数(锁定)："+lockInfo.get("lockPoints"));
                    return;
                }
                card.setConsumePoints(afterCardPoints);
                cardTransactions.setPointsSnapshot(card.getConsumePoints());
            }else{
                renderCodeFailed(cardNumber+"会员卡扣除方式不支持");
                return;
            }
        }
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    if(card.update() && cardTransactions.save()){
                        return true;
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }
                return false;
            }
        });

        if(flag){
            try {
                //发送短信
                JSONObject obj=new JSONObject();
                obj.put("company",Global.sendMsgCompanyName);
                obj.put("cardNumber",card.getCardNumber());
                obj.put("time", DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
                obj.put("baseName","");
                obj.put("costName","付款码付款");
                if("1".equals(cardType.getIsIntegral())){
                    if(integrals>0.0){
                        obj.put("useIntegrals",integrals);
                        obj.put("leftIntegrals",afterCardIntegral);
                        smsSendRecordService.sendMessage(SendType.singleDeductionIntegrals,card.getCardNumber(),JSON.toJSONString(obj),null);
                    }
                    if(beanCoupons>0.0){
                        obj.put("useBeanCoupons",beanCoupons);
                        obj.put("leftBeanCoupons",afterCardBeanCoupons);
                        smsSendRecordService.sendMessage(SendType.singleDeductionBeanCoupons,card.getCardNumber(),JSON.toJSONString(obj),null);
                    }
                }else {
                    if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
                        if(times>0){
                            obj.put("useDays",times);
                            obj.put("leftDays",afterConsumeTimes);
                            smsSendRecordService.sendMessage(SendType.checkinoutDeductionTimes,card.getCardNumber(),JSON.toJSONString(obj),null);
                        }

                    }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                        if(amount>0){
                            obj.put("useAmount",amount);
                            obj.put("leftAmount",afterCardAmount);
                            smsSendRecordService.sendMessage(SendType.checkinoutDeductionAmount,card.getCardNumber(),JSON.toJSONString(obj),null);
                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            logger.info("扣卡成功");
            renderCodeSuccess("success",cardTransactions.getId());
        }else{
            logger.info("扣卡失败");
            renderCodeFailed("财务扣费失败");
        }
    }
    
    /**
     * 抽奖券生成
     */
//    @EnableCORS
//    public void genLuckDrawCoupons(){
//    	Db.delete("delete from pers_org_employee_luck_draw_coupons");
//    	List<Record> recordList = Db.find("select * from pers_org_employee_luck_draw");
//		for(Record record : recordList) {
//			final String fullName = record.getStr("full_name");
//			final String workNum = record.getStr("work_num");
//			Double nums = record.getDouble("nums");
//			if(nums==null || nums==0) {
//				nums = 3.0;
//			}
//			for (int i = 1; i <= nums; i++) {
//				final String luckDrawCoupons = workNum + "-" + i;
//				Record model = new Record();
//				model.set("full_name", fullName);
//				model.set("work_num", workNum);
//				model.set("luck_draw_coupons", luckDrawCoupons);
//				Db.save("pers_org_employee_luck_draw_coupons", model);
//			}
//		}
//		renderCodeSuccess("操作成功!");
//    }

    @EnableCORS
    public void getCardInfoByCardNumber(){
        String cardNumber=getPara("cardNumber");
        if(StrKit.isBlank(cardNumber)){
            renderCodeFailed("会员卡号不能为空");
            return;
        }
        FinaMembershipCard card=finaMembershipCardService.getCardByNumber(cardNumber);
        if(card==null){
            renderCodeFailed("会员卡不存在");
            return;
        }else if("1".equals(card.getIsLock())){
            renderCodeFailed("会员卡已被锁定");
            return;
        }
        Record record = finaMembershipCardService.getCardByCardNumber(null,cardNumber, "0");
        renderCodeSuccess("success",record);
    }

    @EnableCORS
    public void cardRecharge(){
        String userId=getPara("userId");
        FinaCardRecharge cardRecharge=getBean(FinaCardRecharge.class,"",true);
        logger.info("getCardId==="+cardRecharge.getCardId());
        logger.info("getRechargeTime==="+cardRecharge.getRechargeTime());
        logger.info("getType==="+cardRecharge.getType());
        logger.info("getIsIncome==="+cardRecharge.getIsIncome());
        logger.info("getRechargeNo==="+cardRecharge.getRechargeNo());
        logger.info("userId==="+userId);
        if(
    		StrKit.isBlank(cardRecharge.getCardId())
    		||cardRecharge.getRechargeTime()==null
    		||StrKit.isBlank(cardRecharge.getType())
    		||StrKit.isBlank(cardRecharge.getIsIncome())
    		||StrKit.isBlank(cardRecharge.getRechargeNo())
    		|| StrKit.isBlank(userId)
        ){
            renderCodeFailed("参数缺失");
            return;
        }
        if("1".equals(cardRecharge.getType())){
            if(cardRecharge.getAmount()==null || cardRecharge.getAmount()<=0){
                renderCodeFailed("请输入正确的充值金额数");
                return;
            }
        }else if("2".equals(cardRecharge.getType())){
            if(cardRecharge.getConsumeTimes()==null || cardRecharge.getConsumeTimes()<=0){
                renderCodeFailed("请输入正确的充值天数");
                return;
            }
        }else if("3".equals(cardRecharge.getType())){
            if(cardRecharge.getConsumePoints()==null || cardRecharge.getConsumePoints()<=0){
                renderCodeFailed("请输入正确的充值点数");
                return;
            }
        }else if("4".equals(cardRecharge.getType())){
            if(cardRecharge.getConsumeIntegral()==null || cardRecharge.getConsumeIntegral()<=0){
                renderCodeFailed("请输入正确的充值积分数");
                return;
            }
        }else if("5".equals(cardRecharge.getType())){
            if(cardRecharge.getConsumeBeanCoupons()==null || cardRecharge.getConsumeBeanCoupons()<=0){
                renderCodeFailed("请输入正确的充值豆豆券数");
                return;
            }
        }

        if("1".equals(cardRecharge.getIsIncome())){
            if(StrKit.isBlank(cardRecharge.getIsCash()) || StrKit.isBlank(cardRecharge.getAccountId())
            ||cardRecharge.getCashValue()==null || cardRecharge.getAccountValue()==null || cardRecharge.getPayAmount()==null){
                renderCodeFailed("收入性充值参数缺失");
                return;
            }
        }
        cardRecharge.setRechargeWay(RechargeWay.single.getKey());
        cardRecharge.setGiveAmount(0.0);
        cardRecharge.setGiveBeanCoupons(0.0);
        cardRecharge.setGiveConsumeTimes(0.0);
        cardRecharge.setGiveConsumeIntegral(0.0);
        cardRecharge.setGiveConsumePoints(0.0);
        boolean flag = finaCardRechargeService.saveCardRecharge(cardRecharge,userId);
        if(flag){
            System.out.println(cardRecharge.toString());
            flag=finaCardRechargeService.saveReviewRecharge(cardRecharge.getId(),userId);
            if(flag){
                renderCodeSuccess("操作成功");
            }else{
                renderCodeFailed("操作失败");
            }
        }else{
            renderCodeFailed("操作失败");
        }
    }

    @EnableCORS
    public void updateIsMakeCard(){
        final String cardNumber=getPara("cardNumber");
        if(StrKit.isBlank(cardNumber)){
            renderCodeFailed("会员卡号不能为空");
            return;
        }
        FinaMembershipCard card=finaMembershipCardService.getCardByNumber(cardNumber);
        if(card==null){
            renderCodeFailed("会员卡不存在");
            return;
        }else if("1".equals(card.getIsLock())){
            renderCodeFailed("会员卡已被锁定");
            return;
        }
        FinaMembershipCard memberCard = new FinaMembershipCard();
        memberCard.setId(card.getId());
        memberCard.setIsMakeCard("1");
        if(finaMembershipCardService.update(memberCard)) {
        	renderCodeSuccess("操作成功");
        }else {
        	renderCodeFailed("操作失败");
        }
    }

    public void getSettleDetailByCheckinNo(){
        String checkinNos=getPara("checkinNos");
        if(StrKit.isBlank(checkinNos) || !checkinNos.startsWith("[") || !checkinNos.endsWith("]")){
            renderCodeFailed("参数格式不正确");
            return;
        }
        JSONArray jsonArray=JSON.parseArray(checkinNos);
        List<String> checkinNoList=new ArrayList<>();
        String str="";
        for(int i=0;i<jsonArray.size();i++){
            checkinNoList.add(jsonArray.getString(i));
            str+="?,";
        }
        str=str.substring(0,str.length()-1);
        List<Record> settleDetailList=Db.find("select b.expense_id as expenseId,a.checkin_no as checkinNo,b.id,b.card_number as cardNumber,b.settle_type as settleType," +
                "b.start_time as startTime,b.end_time as endTime,b.total_times as totalTimes,b.total_amount as totalAmount" +
                ",b.total_integrals as totalIntegrals,b.actual_times as actualTimes,b.actual_amount as actualAmount,b.actual_integrals as actualIntegrals," +
                "b.settle_status as settleStatus,d.full_name as cardName,e.card_type as cardTypeName,e.id as cardTypeId from fina_expense_record a  " +
                " inner join fina_settle_detail b on a.id=b.expense_id " +
                " inner join fina_membership_card c on c.card_number=b.card_number " +
                " inner join mms_member d on d.id=c.member_id" +
                " inner join main_membership_card_type e on e.id=c.card_type_id " +
                "where a.checkin_no in ("+str+") and a.parent_id='0' and c.del_flag='0' and a.del_flag='0' and a.settle_status in('0','1') and b.del_flag='0' ORDER BY checkin_no,start_time " +
                "   ",checkinNoList.toArray());
        /*Map<String,List<Record>> settleDetailListMap=new HashMap<>();
        for(Record record:settleDetailList){
            if(settleDetailListMap.containsKey(record.getStr("checkinNo"))){
                List<Record> recordList=settleDetailListMap.get(record.getStr("checkinNo"));
                recordList.add(record);
            }else{
                List<Record> recordList=new ArrayList<>();
                recordList.add(record);
                settleDetailListMap.put(record.getStr("checkinNo"),recordList);
            }
        }
        Map<String,List<Record>> checkinNoDetailListMap=new TreeMap<>();
        for(String checkinNo:checkinNoList){
            List<Record> recordList=settleDetailListMap.get(checkinNo);
            if(recordList==null){
                checkinNoDetailListMap.put(checkinNo,new ArrayList<>());
            }else{
                checkinNoDetailListMap.put(checkinNo,recordList);
            }
        }
        renderCodeSuccess("success",checkinNoDetailListMap);*/
        renderCodeSuccess("success",settleDetailList);
    }
    
  //会员卡退款分页列表
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void refundApplyPage(){
    	FinaRefundApply model = getBean(FinaRefundApply.class,"",true);
    	Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
    	if(StrKit.notBlank(model.getApplyNo())) {
    		columns.eq("apply_no", model.getApplyNo());
    	}
    	if(StrKit.notBlank(model.getCreateBy())) {
    		columns.eq("create_by", model.getCreateBy());
    	}
    	Page<FinaRefundApply> modelPage = finaRefundApplyService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
    	renderJson(new DataTable<FinaRefundApply>(modelPage));
    }

    //会员卡退款明细
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void refundApplyDetail(){
    	RetApi returnRet = RetApi.fail("msg", "获取失败");
    	final String applyId= getPara("applyId");
    	if(StrKit.isBlank(applyId)) {
    		returnRet = RetApi.fail("msg", "applyId参数不能为空").set("data", null);
    		return;
    	}
    	FinaRefundApply model = finaRefundApplyService.findById(applyId);
    	if(model!=null) {
    		model.setAccountList(finaRefundAccountService.findByApplyIdList(applyId));
    		List<FinaRefundCard> cardList = finaRefundCardService.findByApplyIdList(applyId);
    		for(FinaRefundCard card : cardList) {
    			List<FinaRefundCardRecord> recordList = finaRefundCardRecordService.findByCardIdList(card.getId());
    			if(recordList!=null && recordList.size()>0) {
    				card.setRecordList(recordList);
    			}else {
    				card.setRecordList(new ArrayList<FinaRefundCardRecord>());
    			}
    			List<CfsFileUpload> fileList = cfsFileUploadService.findListByRelationId(card.getId());
    			if(fileList!=null && fileList.size()>0) {
    				card.setFileList(fileList);
    			}else {
    				card.setFileList(new ArrayList<CfsFileUpload>());
    			}
    		}
    		model.setCardList(cardList);
    		returnRet = RetApi.ok("msg","获取成功").set("data", model);
    	}else {
    		returnRet = RetApi.fail("msg", "获取数据为空").set("data", null);
    	}
    	renderJson(returnRet);
    }

    //会员卡退款保存
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void refundApplySave(){
    	boolean submitTaskFlag = false;
    	RetApi returnRet = RetApi.fail("msg", "操作失败");
    	FinaRefundApply model = getBean(FinaRefundApply.class,"",true);
    	final String accountListJson = getPara("accountList");
    	final String cardListJson = getPara("cardList");
		if(StringUtils.isBlank(accountListJson) || !accountListJson.startsWith("[") || !accountListJson.endsWith("]")){
			renderCodeFailed("accountList参数格式不正确!");
		  	return;
		}
		if(StringUtils.isBlank(cardListJson) || !cardListJson.startsWith("[") || !cardListJson.endsWith("]")){
			renderCodeFailed("cardList参数格式不正确!");
			return;
		}
    	if(StrKit.isBlank(model.getId())){
    		submitTaskFlag = true;
    	}
    	List<FinaRefundAccount> accountList = new ArrayList<FinaRefundAccount>();
    	if(StrKit.notBlank(accountListJson)) {
    		accountList = JSON.parseArray(accountListJson, FinaRefundAccount.class);
    	}
    	List<FinaRefundCard> cardList = new ArrayList<FinaRefundCard>();
    	if(StrKit.notBlank(cardListJson)) {
    		cardList = JSON.parseArray(cardListJson, FinaRefundCard.class);
    	}
    	if(finaRefundApplyService.saveApply(model, accountList, cardList)){
    		returnRet = RetApi.ok("msg","操作成功");
    		if(submitTaskFlag) {
    			Map<String,String> params=new HashMap<>();
    			params.put("processNo", Global.refundCardProcessNo);
    			params.put("userId", model.getCreateBy());
    			params.put("taskId", model.getId());
    			params.put("taskNo", model.getApplyNo());
    			params.put("formNo", Global.refundCardNo);
    			params.put("taskDate", DateUtils.formatDate(model.getApplyTime(), "yyyy-MM-dd"));
    			params.put("title",model.getDepartmentName()+"-"+model.getApplyName()+"的退卡申请");
    			final String resultStr = HttpClientsUtils.httpPostForm(Global.taskCreateUrl,params,null,null);
    			JSONObject returnJson = JSONObject.parseObject(resultStr);
    			final int returnType = returnJson.getIntValue("Type");
    			final String returnMsg = returnJson.getString("Msg");
    			System.out.println("returnType==="+returnType);
    			System.out.println("returnMsg==="+returnMsg);
    			if(returnType!=1) {
    				returnRet = RetApi.fail("msg","操作失败，"+returnMsg);
    			}
    		}
    	}
    	renderJson(returnRet.set("taskId", model.getId()));
    }

    //会员卡退款提交流程
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void refundApplySubmit(){
    	final String taskId= getPara("taskId");
    	final String userId= getPara("userId");
    	RetApi returnRet = RetApi.fail("msg", "操作失败！");
    	FinaRefundApply model = finaRefundApplyService.findById(taskId);
    	if(model!=null) {
    		JSONArray deptArray = new JSONArray();
    		for (int i = 0; i < 2; i++) {
    			JSONObject variableObj = new JSONObject();
    			if(i==0) {
    				if(StrKit.notBlank(model.getDepartmentId())) {
    					final String parentBUHeadId = this.recursionGetBuUserId(model.getDepartmentId());
    					variableObj.put("Name", "ParentBUHeadId");
    					variableObj.put("VariableValue", parentBUHeadId);
    				}else {
    					renderJson(RetApi.fail("msg", "请设置分公司负责人!"));
    					return;
    				}
    			}else {
    				final String accountingUserId = this.recursionGetAccountingUserId(model.getDepartmentId());
    				if(StrKit.notBlank(accountingUserId)) {
    					variableObj.put("Name", "AccountingId");
    					variableObj.put("VariableValue", accountingUserId);
    				}else {
    					renderJson(RetApi.fail("msg", "请设置分公司会计审核人!"));
    					return;
    				}
    			}
    			deptArray.add(variableObj);
    		}
    		Map<String, String> doParams = new HashMap<>();
    		doParams.put("userId", userId);
    		doParams.put("taskId", taskId);
    		doParams.put("stepState", "5");
    		doParams.put("value", deptArray.toJSONString());
    		final String daoResult = HttpClientsUtils.httpPostForm(Global.taskDaoUrl,doParams,null,null);
    		if(StringUtils.isNotBlank(daoResult)) {
    			JSONObject returnJson = JSONObject.parseObject(daoResult);
    			final int returnType = returnJson.getIntValue("Type");
    			final String returnMsg = returnJson.getString("Msg");
    			System.out.println("Type="+returnType);
    			System.out.println("Msg="+returnMsg);
    			if(returnType==1) {
    				FinaRefundApply refundApply = new FinaRefundApply();
    				refundApply.setId(model.getId());
    				refundApply.setHasSubmit(1);
                    finaRefundApplyService.update(refundApply);
    				returnRet = RetApi.ok("msg", "操作成功!");
    			}else {
    				returnRet = RetApi.fail("msg", "提交流程，"+returnMsg);
    			}
    		}
    	}else {
    		returnRet = RetApi.fail("msg", "表单数据为空!");
    	}
    	renderJson(returnRet);
    }

    //会员卡退款作废
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void refundApplyDel(){
        RetApi returnRet = RetApi.fail("msg", "操作失败！");
        FinaRefundApply model = getBean(FinaRefundApply.class,"",true);
        if(StrKit.isBlank(model.getId())) {
            returnRet = RetApi.fail("msg", "id参数不能为空").set("data", null);
            return;
        }
        if(StrKit.isBlank(model.getDelFlag())) {
            model.setDelFlag(DelFlag.DELETED);
        }
        if(model.getUpdateDate()==null){
            model.setUpdateDate(new Date());
        }
    	if(finaRefundApplyService.update(model)) {
            Db.update("update fina_refund_account set del_flag=?,update_by=?,update_date=? where del_flag='0' and apply_id=?", DelFlag.DELETED,model.getUpdateBy(), new Date(), model.getId());
            List<Record> refundCardList = Db.find("select * from fina_refund_card where del_flag='0' and apply_id=?", model.getId());
            for(Record record : refundCardList) {
                final String cardId = record.getStr("id");
                final String memberCardId = record.getStr("card_id");
                FinaRefundCard refundCard = new FinaRefundCard();
                refundCard.setId(cardId);
                refundCard.setDelFlag(DelFlag.DELETED);
                refundCard.setUpdateBy(model.getUpdateBy());
                refundCard.setUpdateDate(new Date());
                if(finaRefundCardService.update(refundCard)) {
                    FinaMembershipCard card = new FinaMembershipCard();
                    card.setId(memberCardId);
                    card.setReturnCardStatus(ReturnCardStatus.NONE);
                    card.update();
                    Db.update("update fina_refund_card_record set del_flag=?,update_by=?,update_date=? where card_id=?", DelFlag.DELETED,model.getUpdateBy(), new Date(), cardId);
                    Db.update("update cfs_file_upload set del_flag=?,update_by=?,update_time=? where relation_id=?", DelFlag.DELETED,model.getUpdateBy(), new Date(), cardId);
                }
            }
            returnRet = RetApi.ok("msg", "操作成功!");
    	}
    	renderJson(returnRet);
    }

    //会员卡退款状态更新
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void refundUpdateStatus(){
        final String jsonStr= getRawData();
        RetApi returnRet = RetApi.fail("msg", "操作失败！");
        if(StringUtils.isBlank(jsonStr) || !jsonStr.startsWith("{") || !jsonStr.endsWith("}")){
            returnRet = RetApi.fail("msg", "参数格式不正确!").set("data", null);
            return;
        }
        JSONObject returnJson = JSONObject.parseObject(jsonStr);
        final String taskId = returnJson.getString("TaskId");
        final String currentStep = returnJson.getString("CurrentStep");
        final String currentStepAlias = returnJson.getString("CurrentStepAlias");
        final String currentUserName = returnJson.getString("CurrentUserName");
        final String currentStatus = returnJson.getString("CurrentStatus");
        FinaRefundApply model = finaRefundApplyService.findById(taskId);
    	if(model!=null) {
    		FinaRefundApply refundApply = new FinaRefundApply();
    		refundApply.setId(model.getId());
    		refundApply.setCurrentNode(currentStep);
    		refundApply.setCurrentStepAlias(currentStepAlias);
    		refundApply.setCurrentUserName(currentUserName);
    		refundApply.setCurrentStatus(currentStatus);
    		if("Completed".equals(currentStatus)) {
    			refundApply.setHasFinish(1);
    		}
    		boolean flag = finaRefundApplyService.update(refundApply);
            if(flag && refundApply.getHasFinish() !=null) {
                List<Record> refundCardList = Db.find("select * from fina_refund_card where del_flag='0' and apply_id=?", model.getId());
                for(Record record : refundCardList) {
                    final String cardId = record.getStr("card_id");
                    FinaMembershipCard card = new FinaMembershipCard();
                    card.setId(cardId);
                    card.setReturnCardStatus(ReturnCardStatus.FINISH);
                    card.update();
                }
                returnRet = RetApi.ok("msg", "操作成功!");
            }
    	}else{
            returnRet = RetApi.fail("msg", "表单数据为空!");
        }
    	renderJson(returnRet);
    }

    private String recursionGetBuUserId(final String deptId) {
    	String buUserId = "";
    	if(StringUtils.isNotBlank(deptId)){
    		PersOrg persOrg = persOrgService.findById(deptId);
    		if(persOrg!=null){
    			buUserId = persOrg.getLinkMan();
    		}
    	}
    	return buUserId;
    }
    
    private String recursionGetAccountingUserId(final String deptId) {
    	String accountingUserId = "";
    	if(StringUtils.isNotBlank(deptId)){
    		PersOrg persOrg = persOrgService.findById(deptId);
    		if(persOrg!=null) {
    			List<PersOrgReviewer> reviewerList = persOrgReviewerService.findList(deptId, "Accounting");
    			if(reviewerList!=null && reviewerList.size()>0){
    				accountingUserId = reviewerList.get(0).getUserId();
    			}
    		}
    	}
    	return accountingUserId;
    }

    private String recursionGetCashierUserId(final String deptId) {
    	String cashierUserId = "";
    	if(StringUtils.isNotBlank(deptId)){
    		PersOrg persOrg = persOrgService.findById(deptId);
    		if(persOrg!=null) {
    			List<PersOrgReviewer> reviewerList = persOrgReviewerService.findList(deptId, "Cashier");
    			if(reviewerList!=null && reviewerList.size()>0){
    				cashierUserId = reviewerList.get(0).getUserId();
    			}
    		}
    	}
    	return cashierUserId;
    }

    public void cardBindRoll(){
        String cardId=getPara("cardId");
        String rollId=getPara("rollId");
        if(StrKit.isBlank(cardId) || StrKit.isBlank(rollId)){
            renderCodeFailed("参数缺失");
            return;
        }
        FinaMembershipCard card=finaMembershipCardService.findById(cardId);
        if(card==null){
            renderCodeFailed("获取会员卡失败");
            return;
        }
        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
        if(cardType==null){
            renderCodeFailed("获取会员卡类型失败");
            return;
        }
        if(!"1".equals(cardType.getIsRechargeBean())){
            renderCodeFailed("该会员卡类型暂未开通豆豆券");
            return;
        }
        if(crmCardRollRecordService.isNotbind(rollId)){
            renderCodeFailed("该券已超过最大可绑定时间");
            return;
        }

        Record record=Db.findFirst("select * from fina_card_roll where roll_id=?",rollId);
        if(record!=null){
            renderCodeFailed("该券已被"+card.getCardNumber()+"会员卡绑定");
            return;
        }
        CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(rollId);
        if(rollRecord==null){
            renderCodeFailed("该券已不存在");
            return;
        }else if("1".equals(rollRecord.getIsEnable())){
            renderCodeFailed("该券已过期");
            return;
        }
        if("1".equals(rollRecord.getDelFlag())){
            renderCodeFailed("该券已被作废");
            return;
        }

        FinaCardRoll cardRoll=new FinaCardRoll();
        cardRoll.setId(IdGen.getUUID());
        cardRoll.setCardId(cardId);
        cardRoll.setRollId(rollId);
        cardRoll.setCreateTime(new Date());
        cardRoll.setBindingTime(new Date());
        if(cardRoll.save()){
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void beanCouponsDetailPageList(){
        String cardId=getPara("cardId");

        String sql="select b.* from fina_card_roll a INNER JOIN crm_card_roll_record b on a.roll_id=b.id " +
                "where a.card_id=? and b.del_flag='0' order by create_time,roll_number ";

        List<Record> recordList=Db.find(sql,cardId);

        renderCodeSuccess("success",recordList);
    }

    public void batchSettleSave(){
        StringBuilder errorInfo = new StringBuilder();
        final String batchDatas = getPara("batchDatas");
        if(StrKit.notBlank(batchDatas)){
            logger.info(JSON.toJSONString(JSONArray.parseArray(batchDatas)));
            List<FinaSettleDetail> settleList = JSONArray.parseArray(batchDatas, FinaSettleDetail.class);
            if(settleList!=null && settleList.size()>0) {

                Map<String,Record> msmLockMap=new HashMap<>();

                boolean flag = Db.tx(new IAtom() {
                    @Override
                    public boolean run() throws SQLException {
                        try {
                            for(FinaSettleDetail settleDetail : settleList) {

                                if("1".equals(settleDetail.getDelFlag())){
                                    errorInfo.append(settleDetail.getId()+"改记录已被作废<br>");
                                    break;
                                }else if("1".equals(settleDetail.getSettleStatus())){
                                    errorInfo.append(settleDetail.getId()+"改记录已被结算<br>");
                                    break;
                                }

                                final String detailId=settleDetail.getId();
                                final String expenseId=settleDetail.getExpenseId();
                                final String cardNumber=settleDetail.getCardNumber();
                                final String settleType=settleDetail.getSettleType();
                                Double actualTimes=settleDetail.getActualTimes();
                                Double actualAmount=settleDetail.getActualAmount();
                                Double actualPoints=settleDetail.getActualPoints();
                                Double actualIntegrals=settleDetail.getActualIntegrals();
                                if(actualTimes==null){
                                    actualTimes=0.0;
                                }
                                if(actualAmount==null){
                                    actualAmount=0.0;
                                }
                                if(actualPoints==null){
                                    actualPoints=0.0;
                                }
                                if(actualIntegrals==null){
                                    actualIntegrals=0.0;
                                }
                                final String settleRemark=settleDetail.getSettleRemark();

                                if("checkout_settle".equals(settleType)) {//退住结算
                                    int count= Db.queryInt("select count(id) from fina_settle_detail "
                                            + "where settle_type='month_settle' and del_flag='0' and settle_status='0' "
                                            + "and expense_id=? ",expenseId);
                                    if(count>0){
                                        errorInfo.append(cardNumber+"请先结算完月结明细再结算退住明细<br>");
                                        break;
                                    }
                                }
                                FinaExpenseRecord er = finaExpenseRecordService.get(expenseId);
                                if (er == null) {
                                    errorInfo.append(cardNumber+"账单记录不存在<br>");
                                    break;
                                }
                                /*if (StringUtils.isNotBlank(er.getSettleStatus()) && !"0".equals(er.getSettleStatus())) {
                                    errorInfo.append(cardNumber+"主账单记录不处于未结算状态，不可结算<br>");
                                    break;
                                }*/
                                if("1".equals(er.getDelFlag()) || "2".equals(er.getSettleStatus())){
                                    errorInfo.append(er.getCheckinNo()+"入住单已被作废");
                                    break;
                                }

                                //获取未结算中创建时间最小的记录
                                String minDateId=Db.queryStr("select id from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' order by end_time limit 1 ",expenseId);
                                if(!minDateId.equalsIgnoreCase(detailId)){
                                    errorInfo.append(cardNumber+"请按照明细生成时间顺序来结算<br>");
                                    break;
                                }

                                FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
                                if("1".equals(detail.getSettleStatus())){
                                    errorInfo.append(cardNumber+"该明细记录已是结算完，请勿重复结算<br>");
                                    break;
                                }
                                //判断本单前面有无关联的换住未结算记录
                                String changeBedExpenseRecordId=Db.queryStr("select id from fina_expense_record where id_card=? and parent_id='0' and base_id=? " +
                                                "and DATE_FORMAT(real_checkout_time,'%Y-%m-%d')=? and is_change_bed='1' and id<>? "
                                        ,er.getIdCard(),er.getBaseId(),DateUtils.formatDate(er.getCheckinTime(),"yyyy-MM-dd"),er.getId());
                                if(StrKit.notBlank(changeBedExpenseRecordId)){
                                    //判断是否有
                                    Record changeBedNotSettleDetail=Db.findFirst("select * from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' " +
                                            "   ORDER BY start_time ",changeBedExpenseRecordId);
                                    if(changeBedNotSettleDetail!=null){
                                        FinaExpenseRecord changeBedExpenseRecord=finaExpenseRecordService.findById(changeBedExpenseRecordId);
                                        errorInfo.append(changeBedExpenseRecord.getName() + "存在未结算的换住订单，请先结算换住订单再结算本记录，换住单号："+changeBedExpenseRecord.getCheckinNo());
                                        break;
                                    }
                                }

                                if(actualTimes==null){
                                    actualTimes=0.0;
                                }
                                if(actualAmount==null){
                                    actualAmount=0.0;
                                }
                                if(actualPoints==null){
                                    actualPoints=0.0;
                                }
                                if(actualIntegrals==null){
                                    actualIntegrals=0.0;
                                }
                                synchronized (FinaSettleDetailController.class){
                                    Map<String,Object> map = finaSettleDetailService.settleDateilRecordNoSms(
                                            detail,er,settleType,actualTimes,actualAmount,actualPoints,
                                            actualIntegrals,settleRemark, AuthUtils.getUserId()
                                    );
                                    if(!(boolean)map.get("flag")){
                                        errorInfo.append(map.get("msg"));
                                        break;
                                    }
                                    FinaMembershipCard card  = finaMembershipCardService.getCardByNumber(detail.getCardNumber());

                                    Map<String, Double> cardLockInfo = finaMembershipCardService.getCardLockInfo(detail.getCardNumber());
                                    if(card.getConsumeTimes()==null){
                                        card.setConsumeTimes(0.0);
                                    }
                                    if(card.getBalance()==null){
                                        card.setBalance(0.0);
                                    }
                                    if(card.getCardIntegrals()==null){
                                        card.setCardIntegrals(0.0);
                                    }

                                    Record smsRecord=new Record();
                                    smsRecord.set("useTimes",actualTimes);
                                    smsRecord.set("useAmount",actualAmount);
                                    smsRecord.set("useIntegrals",actualIntegrals);
                                    smsRecord.set("lockTimes",cardLockInfo.get("lockConsumeTimes"));
                                    smsRecord.set("lockAmount",cardLockInfo.get("lockBalance"));
                                    smsRecord.set("lockIntegrals",cardLockInfo.get("lockIntegrals"));
                                    smsRecord.set("usableTimes",BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockConsumeTimes"))).doubleValue());
                                    smsRecord.set("usableAmount",BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockBalance"))).doubleValue());
                                    smsRecord.set("usableIntegrals",BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(cardLockInfo.get("lockIntegrals"))).doubleValue());
                                    smsRecord.set("changeBedExpenseRecordId",changeBedExpenseRecordId);
                                    //id中的字母转小写
                                    msmLockMap.put(detail.getId().toLowerCase(),smsRecord);

                                }
                            }
                            if(StrKit.notBlank(errorInfo.toString())) {
                                return false;
                            }else {
                                return true;
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                            return false;
                        }

                    }
                });
                logger.info(JSON.toJSONString(msmLockMap));
                if(flag){
                    for (int j = 0; j < settleList.size(); j++) {
                        FinaSettleDetail settleDetail = settleList.get(j);
                        final String detailId=settleDetail.getId();
                        final String expenseId=settleDetail.getExpenseId();
                        final String cardNumber=settleDetail.getCardNumber();
                        Double actualTimes=settleDetail.getActualTimes();
                        Double actualAmount=settleDetail.getActualAmount();
                        Double actualPoints=settleDetail.getActualPoints();
                        Double actualIntegrals=settleDetail.getActualIntegrals();

                        FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);

                        MainCardDeductScheme scheme = mainCardDeductSchemeService.findById(card.getDeductSchemeId());
                        if(scheme==null){
                            scheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
                        }
                        MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());

                        MmsMember member=mmsMemberService.findById(card.getMemberId());

                        FinaExpenseRecord expenseRecord = finaExpenseRecordService.get(expenseId);
                        FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
                        MainBase base=mainBaseService.findById(expenseRecord.getBaseId());
                        Record smsRecord=msmLockMap.get(detail.getId().toLowerCase());
                        String changeBedExpenseRecordId=null;
                        if(smsRecord!=null){
                            changeBedExpenseRecordId=smsRecord.getStr("changeBedExpenseRecordId");
                        }


                        finaSettleDetailService.settleDetailSMSSend(detail,card,expenseRecord,changeBedExpenseRecordId,settleDetail.getSettleType(),base,scheme,cardType);

                        /*FinaCardDeductionSmsRecord deductionSmsRecord=new FinaCardDeductionSmsRecord();
                        deductionSmsRecord.setId(IdGen.getUUID());
                        deductionSmsRecord.setDelFlag("0");
                        deductionSmsRecord.setType("1");
                        deductionSmsRecord.setDeductTime(new Date());
                        deductionSmsRecord.setStartTime(detail.getStartTime());
                        deductionSmsRecord.setEndTime(detail.getEndTime());
                        deductionSmsRecord.setCheckinNames(expenseCheckinName);
                        deductionSmsRecord.setCardNumber(leftCard.getCardNumber());
                        deductionSmsRecord.setCardId(leftCard.getId());
                        //deductionSmsRecord.setBillName((String)smsMap.get("costName"));
                        deductionSmsRecord.setBaseName(base.getBaseName());
                        deductionSmsRecord.setTelephone(leftCard.getTelephone());
                        deductionSmsRecord.setAmount(0.0);
                        deductionSmsRecord.setTimes(0.0);
                        deductionSmsRecord.setIntegrals(0.0);
                        if(actualAmount!=null && actualAmount>0){
                            deductionSmsRecord.setAmount(actualAmount);
                        }
                        if(actualTimes!=null && actualTimes>0){
                            deductionSmsRecord.setTimes(actualTimes);
                        }
                        if(actualIntegrals!=null && actualIntegrals>0){
                            deductionSmsRecord.setIntegrals(actualIntegrals);
                        }
                        deductionSmsRecord.setStatus("0");
                        deductionSmsRecord.setCreateDate(new Date());
                        deductionSmsRecord.setUpdateDate(new Date());
                        deductionSmsRecord.save();*/

                        /*Calendar sendTime = Calendar.getInstance();
                        //设置发送时间间隔1分钟后发送
                        sendTime.add(Calendar.MINUTE,j+1);

                        if(actualIntegrals != null && actualIntegrals != 0){
                            JSONObject obj=new JSONObject();
                            obj.put("company", Global.sendMsgCompanyName);
                            obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
                            obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
                            if(StrKit.notBlank(base.getBaseName())){
                                obj.put("baseName",base.getBaseName());
                            }else{
                                obj.put("baseName","");
                            }
                            obj.put("costName","");
                            obj.put("useDays",actualTimes);
                            obj.put("useIntegrals",actualIntegrals);
                            obj.put("lockDays",lockConsumeTimes);
                            obj.put("lockIntegrals",lockIntegrals);
                            obj.put("leftDays",consumeTimes);
                            obj.put("leftIntegrals",integrals);
                            smsSendRecordService.sendMessage(SendType.checkinoutDeductionTimesIntegral,cardNumber, JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
                        }
                        if(actualTimes != null && actualTimes != 0){
                            JSONObject obj=new JSONObject();
                            obj.put("company", Global.sendMsgCompanyName);
                            obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
                            obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
                            if(StrKit.notBlank(base.getBaseName())){
                                obj.put("baseName",base.getBaseName());
                            }else{
                                obj.put("baseName","");
                            }
                            obj.put("costName","");
                            obj.put("useDays",actualTimes);
                            obj.put("lockDays",lockConsumeTimes);
                            obj.put("leftDays",consumeTimes);
                            smsSendRecordService.sendMessage(SendType.checkinoutDeductionTimes,cardNumber, JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
                        }
                        if(actualAmount != null && actualAmount != 0){
                            JSONObject obj=new JSONObject();
                            obj.put("company",Global.sendMsgCompanyName);
                            obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
                            obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
                            if(StrKit.notBlank(base.getBaseName())){
                                obj.put("baseName",base.getBaseName());
                            }else{
                                obj.put("baseName","");
                            }
                            obj.put("costName","");
                            obj.put("useAmount",actualAmount);
                            obj.put("lockAmount",lockBalance);
                            obj.put("leftAmount",balance);
                            smsSendRecordService.sendMessage(SendType.checkinoutDeductionAmount,cardNumber,JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
                        }
                        if(actualPoints!=null && actualPoints!=0){
                            JSONObject obj=new JSONObject();
                            obj.put("company",Global.sendMsgCompanyName);
                            obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
                            obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
                            if(StrKit.notBlank(base.getBaseName())){
                                obj.put("baseName",base.getBaseName());
                            }else{
                                obj.put("baseName","");
                            }
                            obj.put("costName","");
                            obj.put("usePoints",actualPoints);
                            obj.put("leftPoints",consumePoints);
                            smsSendRecordService.sendMessage(SendType.checkinoutDeductionPoints,cardNumber,JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
                        }*/
                    }
                }
                if(flag) {
                    renderCodeSuccess("结算成功");
                }else {
                    renderCodeFailed(errorInfo.toString());
                }
            }
        }else{
            renderCodeFailed("传入数据为空");
        }
    }

    public void smsSend(){

        FinaSettleDetail settleDetail = finaSettleDetailService.findById(getPara("id"));
        final String detailId=settleDetail.getId();
        final String expenseId=settleDetail.getExpenseId();
        final String cardNumber=settleDetail.getCardNumber();
        Double actualTimes=settleDetail.getActualTimes();
        Double actualAmount=settleDetail.getActualAmount();
        Double actualPoints=settleDetail.getActualPoints();
        Double actualIntegrals=settleDetail.getActualIntegrals();

        FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);

        MainCardDeductScheme scheme = mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if(scheme==null){
            scheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        }
        MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());

        MmsMember member=mmsMemberService.findById(card.getMemberId());

        FinaExpenseRecord expenseRecord = finaExpenseRecordService.get(expenseId);
        FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
        MainBase base=mainBaseService.findById(expenseRecord.getBaseId());
        String changeBedExpenseRecordId=null;
        finaSettleDetailService.settleDetailSMSSend(detail,card,expenseRecord,changeBedExpenseRecordId,settleDetail.getSettleType(),base,scheme,cardType);
        renderJson("ok");
    }

    //会员卡收款保存
    @Clear(LogInterceptor.class)
    @EnableCORS
    public void cardCollectSave(){
        RetApi returnRet = RetApi.fail("msg", "操作失败");
        final String cardCollectJson = getPara("cardCollect");
        if(StringUtils.isBlank(cardCollectJson) || !cardCollectJson.startsWith("{") || !cardCollectJson.endsWith("}")){
            renderCodeFailed("cardCollect参数格式不正确!");
            return;
        }
        JSONObject cardCollect = JSON.parseObject(cardCollectJson);
        final String cardId = cardCollect.getString("cardId");
//        if(StringUtils.isBlank(cardId)){
//            renderCodeFailed("cardId参数不能为空!");
//            return;
//        }
        String userId = "";
        String collectSqlStr="";
        List<String> collectIds=new ArrayList<>();
        JSONArray collectArray = cardCollect.getJSONArray("collectList");
        for(int i=0;i<collectArray.size();i++){
            JSONObject collectObj = collectArray.getJSONObject(i);
            FinaCardCollect model = JSONObject.toJavaObject(collectObj, FinaCardCollect.class);
            if(StrKit.isBlank(model.getId())){
                model.setId(IdGen.getUUID());
                model.setCardId(cardId);
                model.setDelFlag(DelFlag.NORMAL);
                model.setCreateTime(new Date());
                model.setUpdateTime(new Date());
                model.save();
                userId = model.getCreateBy();
            }else{
                model.setUpdateTime(new Date());
                model.update();
                userId = model.getUpdateBy();
                collectSqlStr+="?";
                if(i<(collectArray.size()-1)) {
                    collectSqlStr+=",";
                }
                collectIds.add(model.getId());
            }
        }
        final int collectCount = Db.queryInt("select count(id) as dataCount from fina_card_collect where del_flag=? and card_id=?", DelFlag.NORMAL, cardId);
        if(collectIds!=null && collectIds.size()>0 && collectIds.size()<collectCount) {
            collectIds.add(cardId);
            Db.update("update fina_card_collect set del_flag='1',update_by='"+userId+"',update_time=now() where id not in("+collectSqlStr+") and card_id=?", collectIds.toArray());
        }
        returnRet = RetApi.ok("msg","操作成功");
        renderJson(returnRet);
    }

    public void findRollBindingInfo(){
        String rollId=getPara("rollId");
        if(StrKit.isBlank(rollId)){
            renderCodeFailed("参数确实");
            return;
        }
        CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(rollId);
        if(rollRecord==null){
            renderCodeFailed("该券不存在");
            return;
        }
        Record record=Db.findFirst("select * from fina_card_roll where roll_id=? ",rollRecord.getId());
        if(record==null){
            renderCodeFailed("该券未绑定");
            return;
        }
        FinaMembershipCard membershipCard=finaMembershipCardService.findById(record.getStr("card_id"));
        MmsMember member=mmsMemberService.findById(membershipCard.getMemberId());

        Record returnRecord=new Record();
        returnRecord.set("cardNumber",membershipCard.getCardNumber());
        returnRecord.set("cardMember",member.getFullName());
        if(StrKit.isBlank(record.getStr("create_by"))){
            returnRecord.set("bindingUser","客户");
        }else{
            User user= userService.findById(record.getStr("create_by"));
            returnRecord.set("bindingUser",user.getName());
        }
        returnRecord.set("bindingTime",record.getDate("create_time"));

        renderCodeSuccess("success",returnRecord);
    }

    public void findContractRecord(){
        String cardTypeId=getPara("cardTypeId");
        String name=getPara("name");
        String id=getPara("id");
        List<FinaContractRecord> contractRecordList = finaContractRecordService.findContractRecord(id,cardTypeId,name);

        List<Record> recordList=new ArrayList<>();
        for(FinaContractRecord record:contractRecordList){
            Record r=new Record();
            r.set("Text",record.getName());
            r.set("Value",record.getId());
            recordList.add(r);
        }
        renderCodeSuccess("success",recordList);
    }
    

	/**
	 * 作废旅游团方法
	 */
    public void delTourist() {
    	final String touristNo = getPara("touristNo");
    	final String updateBy = getPara("updateBy");
    	if(StrKit.isBlank(touristNo)){
            renderCodeFailed("操作失败,旅游团号不能为空!");
    		return;
    	}
    	if(Db.findFirst("select * from fina_expense_record where category='tourist_bill' and tourist_no=? and settle_status='1' and del_flag='0' ",touristNo)!=null){
            renderCodeFailed("操作失败,该团已存在结算过的记录!");
    	    return;
        }

    	FinaExpenseRecordTourist model = finaExpenseRecordTouristService.getTouristByNo(touristNo);
    	if(model!=null){
    		FinaExpenseRecordTourist tourist = new FinaExpenseRecordTourist();
    		tourist.setId(model.getId());
    		tourist.setDelFlag(DelFlag.DELETED);
    		tourist.setUpdateBy(updateBy);
    		tourist.setUpdateTime(new Date());
    		if (finaExpenseRecordTouristService.update(tourist)) {
    			List<Record> delRecordList = Db.find("select * from fina_expense_record where del_flag='0' and settle_status='0' and tourist_no=?", model.getTouristNo());
    			for(Record record : delRecordList) {
    				final String expenseId = record.getStr("id");
    				FinaExpenseRecord expenseRecord = new FinaExpenseRecord();
    				expenseRecord.setId(expenseId);
    				expenseRecord.setSettleStatus("2");
    				expenseRecord.setUpdateBy(updateBy);
    				expenseRecord.setUpdateTime(new Date());
    				if(expenseRecord.update()) {
                        List<FinaExpenseRecordTouristSettleDetail> touristSettleDetailList=finaExpenseRecordTouristSettleDetailService.findTouristSettleDetailList(expenseRecord.getId());

                        for (FinaExpenseRecordTouristSettleDetail touristSettleDetail : touristSettleDetailList) {
                            touristSettleDetail.setDelFlag("1");
                            touristSettleDetail.setUpdateBy(updateBy);
                            touristSettleDetail.setUpdateTime(new Date());
                            touristSettleDetail.update();
                            Db.update("update fina_expense_record_detail set del_flag='1',update_by=?,update_time=? where expense_id=?", updateBy, new Date(), touristSettleDetail.getId());
                        }
                        Db.update("update fina_expense_record_tourist_detail set del_flag='1',update_by=?,update_date=? where unique_id=?", updateBy, new Date(), record.getStr("unique_id"));
    				}
    			}
    			renderCodeSuccess("操作成功!");
    		} else {
                renderCodeFailed("操作失败!");
    		}
    	}else{
    		renderCodeFailed("操作失败,找不到该旅游团!");
    	}
    }


    public void getYearMonthSettleDays(){
        String checkinNos=getPara("checkinNos");
        String yearMonth=getPara("yearMonth");
        if(StrKit.isBlank(yearMonth) || StrKit.isBlank(checkinNos)){
            renderCodeFailed("参数缺失");
            return;
        }
        JSONArray checkinNoArray=JSON.parseArray(checkinNos);
        if(checkinNoArray==null || checkinNoArray.size()==0){
            renderCodeFailed("入住号不能为空");
            return;
        }

        String startDate=yearMonth+"-01";
        String endDate=yearMonth+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDate));

        String str="";
        List<Object> params=new ArrayList<>();

        params.add(startDate+" 00:00:00");
        params.add(endDate+" 23:59:59");
        for (int i = 0; i < checkinNoArray.size(); i++) {
            params.add(checkinNoArray.getString(i));
        }
        params.add(yearMonth);
        for (int i = 0; i < checkinNoArray.size(); i++) {
            str+="?,";
            params.add(checkinNoArray.getString(i));
        }
        str=str.substring(0,str.length()-1);

        String sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,"
                + "a.create_time,a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,"
                + "a.checkin_no,a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount,"
                + "SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,'0' as settleStatus," +
                " (select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=a.id and type='follow_checkin' and del_flag='0'  ) as followCount "
                + "from fina_expense_record a \n"
                + "left join fina_expense_record_detail d on d.expense_id=a.id \n"
                + "left join fina_membership_card b on b.id=(select id from fina_membership_card "
                + "where d.card_number=card_number ORDER BY del_flag limit 1)  \n"
                + "left join main_membership_card_type c on b.card_type_id=c.id \n"
                + "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
                + "left join  main_base_bed g on g.id=a.bed_id "
                + "where  a.category='sojourn_bill' and d.del_flag='0' and d.start_time>=? "
                + "and d.start_time<=? and a.checkin_no in ("+str+") and a.settle_status ='0' and a.del_flag='0' and d.is_settled='0' GROUP BY a.id \n"
                + "UNION\n"
                + "select b.is_private_room,b.checkin_status,b.book_no,b.card_number,b.id,b.parent_id,b.create_time,"
                + "b.book_start_time,b.book_end_time,b.checkin_time,b.checkout_time,b.real_checkout_time,b.checkin_no,"
                + "b.`name`,d.card_type as cardType,SUM(a.actual_times) as sumTimes,SUM(a.actual_amount) as sumAmount,"
                + "SUM(a.actual_integrals) as sumIntegrals,SUM(a.actual_points) as sumPoints,g.bed_name as bedName,"
                + "a.settle_status as settleStatus," +
                "(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and type='follow_checkin' and del_flag='0'  ) as followCount " +
                "from fina_settle_detail a \n"
                + "left join fina_expense_record b on a.expense_id=b.id \n"
                + "left join fina_membership_card c on c.id=(select id from fina_membership_card "
                + "where a.card_number=card_number ORDER BY del_flag limit 1) \n"
                + "left join main_membership_card_type d on c.card_type_id=d.id "
                + "left join main_card_deduct_scheme e on e.id=c.long_deduct_scheme_id "
                + "left join  main_base_bed g on g.id=b.bed_id "
                + "where a.`year_month`=? and a.del_flag='0' and a.settle_status='1' and b.del_flag='0' and b.checkin_no in ("+str+") "
                + "and a.settle_status in ('0','1')  GROUP BY b.id\n order by create_time \n";
        List<Record> recordList=Db.find(sql,params.toArray());

        if(recordList.size()==0){
            renderCodeSuccess("success",recordList);
            return;
        }
        Map<String,Record> recordMap=new TreeMap<>();
        for(Record record:recordList){
            if("0".equals(record.getStr("parent_id"))){
                recordMap.put(record.getStr("id"),record);
            }
        }
        for(Record record:recordList){
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    FinaExpenseRecord mainRecord=finaExpenseRecordService.findById(record.getStr("parent_id"));
                    parentRecord=new Record();
                    parentRecord.setColumns(record);
                    parentRecord.set("checkin_status",mainRecord.getCheckinStatus());
                    parentRecord.set("book_no",mainRecord.getBookNo());
                    //parentRecord.set("card_number",mainRecord.getCardNumber());
                    parentRecord.set("book_start_time",mainRecord.getBookStartTime());
                    parentRecord.set("book_end_time",mainRecord.getBookEndTime());
                    parentRecord.set("checkin_time",mainRecord.getCheckinTime());
                    parentRecord.set("checkout_time",mainRecord.getCheckoutTime());
                    parentRecord.set("real_checkout_time",mainRecord.getRealCheckoutTime());
                    parentRecord.set("is_private_room",mainRecord.getIsPrivateRoom());

                    recordMap.put(record.getStr("parent_id"),parentRecord);
                    continue;
                }
                parentRecord.set("sumTimes",parentRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
                parentRecord.set("sumAmount",parentRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
                parentRecord.set("sumIntegrals",parentRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
                parentRecord.set("sumPoints",parentRecord.getDouble("sumPoints")+record.getDouble("sumPoints"));
            }
        }
        List<Record> returnList=new ArrayList<>();

        List<String> continuedParams=new ArrayList<>();
        StringBuffer continuedBuff=new StringBuffer("");
        for(String key:recordMap.keySet()){
            Record record=recordMap.get(key);
            returnList.add(record);
            Double sumTimes=record.getDouble("sumTimes");
            Double sumAmount=record.getDouble("sumAmount");
            Double sumIntegrals=record.getDouble("sumIntegrals");
            if(sumTimes==null){
                sumTimes=0.0;
            }
            if(sumAmount==null){
                sumAmount=0.0;
            }
            if(sumIntegrals==null){
                sumIntegrals=0.0;
            }
            double settleDays=0.0;
            if(sumTimes>=0.0 || sumAmount>0.0 || sumIntegrals>0.0){
                if(sumAmount>0.0){


                }else{
                    settleDays=sumTimes+sumIntegrals;
                }
            }
            record.set("settleDays",settleDays);

            continuedParams.add(key);
            continuedBuff.append("?,");
        }
        String continuedStr=continuedBuff.substring(0,continuedBuff.length()-1);
        String continuedSql="select parent_id,MAX(checkout_time) as maxCheckout from fina_expense_record where type='continued_residence' " +
                "and parent_id in("+continuedStr+") and del_flag='0' and settle_status in ('0','1') GROUP BY parent_id ";
        List<Record> continuedList=Db.find(continuedSql,continuedParams.toArray());
        for(Record continued:continuedList){
            Record record=recordMap.get(continued.getStr("parent_id"));
            record.set("checkout_time",continued.getDate("maxCheckout"));
        }


        if(returnList.size()>0){
            for(String key:recordMap.keySet()){
                Record record=recordMap.get(key);
                if("book".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("book_start_time"));
                    record.set("end",record.getDate("book_end_time"));
                }else if ("stayin".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("checkin_time"));
                    record.set("end",record.getDate("checkout_time"));
                }else if("retreat".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("checkin_time"));
                    record.set("end",record.getDate("real_checkout_time"));
                }
                Date start=record.getDate("start");
                Date end=record.getDate("end");

                Date calStart=null;
                Date calEnd=null;
                if(DateUtils.compareDays(start,DateUtils.parseDate(startDate+" 00:00:00"))<0){
                    calStart=DateUtils.parseDate(startDate+" 00:00:00");
                }else{
                    calStart=start;
                    calStart=DateUtils.parseDate(DateUtils.formatDate(calStart,"yyyy-MM-dd")+" 00:00:00");
                }

                if(DateUtils.compareDays(end,DateUtils.parseDate(endDate+" 23:59:59"))>0){
                    calEnd=DateUtils.parseDate(endDate+" 24:59:59");
                }else{
                    calEnd=end;
                    calEnd=DateUtils.parseDate(DateUtils.formatDate(calEnd,"yyyy-MM-dd")+" 23:59:59");
                }
                long days=(calEnd.getTime()-calStart.getTime())/(86400000);
                record.set("checkinDays",days);

                Double sumTimes=record.getDouble("sumTimes");
                Double sumAmount=record.getDouble("sumAmount");
                Double sumIntegrals=record.getDouble("sumIntegrals");
                if(sumTimes==null){
                    sumTimes=0.0;
                }
                if(sumAmount==null){
                    sumAmount=0.0;
                }
                if(sumIntegrals==null){
                    sumIntegrals=0.0;
                }

                if(record.getDouble("sumAmount")!=null && record.getDouble("sumAmount")>0){
                    double settleDays=days;
                    if("1".equals(record.getStr("is_private_room"))){
                        settleDays=days*2;
                    }
                    if(record.getInt("followCount")!=null && record.getInt("followCount")>0){
                        settleDays+=days*record.getInt("followCount");
                    }
                    record.set("settleDays",settleDays);
                }else if(sumTimes==0 && sumAmount==0.0 && sumIntegrals==0.0){
                    double settleDays=record.getDouble("checkinDays");
                    if("1".equals(record.getStr("is_private_room"))){
                        settleDays=record.getDouble("checkinDays")*2;
                    }
                    if(record.getInt("followCount")!=null && record.getInt("followCount")>0){
                        settleDays+=record.getDouble("checkinDays")*record.getInt("followCount");
                    }
                    record.set("settleDays",settleDays);
                }
            }
        }
        renderCodeSuccess("success",returnList);
    }

    public void getAllSettleDays(){
        String checkinNos=getPara("checkinNos");
        JSONArray checkinNoArray=JSON.parseArray(checkinNos);
        if(checkinNoArray==null || checkinNoArray.size()==0){
            renderCodeFailed("入住号不能为空");
            return;
        }
        String str="";
        List<Object> params=new ArrayList<>();

        for (int i = 0; i < checkinNoArray.size(); i++) {
            params.add(checkinNoArray.getString(i));
        }
        for (int i = 0; i < checkinNoArray.size(); i++) {
            str+="?,";
            params.add(checkinNoArray.getString(i));
        }



        str=str.substring(0,str.length()-1);
        String sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.create_time,a.book_start_time,a.book_end_time,a.checkin_time" +
                ",a.checkout_time,a.real_checkout_time,a.checkin_no" +
                /*",a.`name`,c.card_type as cardType" +*/
                ",SUM(d.times) as sumTimes,SUM(amount) as sumAmount" +
                ",SUM(integrals) as sumIntegrals,SUM(points) as sumPoints" +
                /*",g.bed_name as bedName" +*/
                ",'0' as settleStatus " +
                /*", (select COUNT(DISTINCT id_card) from fina_expense_record " +
                "where parent_id=a.id and type='follow_checkin' and del_flag='0'  ) as followCount " +*/
                "from fina_expense_record a \n" +
                "left join fina_expense_record_detail d on d.expense_id=a.id \n" +
                /*"left join fina_membership_card b on b.id=(select id from fina_membership_card where d.card_number=card_number ORDER BY del_flag limit 1)  \n" +
                "left join main_membership_card_type c on b.card_type_id=c.id \n" +
                "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id left join  main_base_bed g on g.id=a.bed_id " +*/
                "where  a.category='sojourn_bill' and d.del_flag='0'  and a.checkin_no in ("+str+") " +
                "and a.settle_status ='0' and a.del_flag='0' and d.is_settled='0' GROUP BY a.id \n" +
                "UNION\n" +
                "select b.is_private_room,b.checkin_status,b.book_no,b.card_number,b.id,b.parent_id,b.create_time,b.book_start_time,b.book_end_time,b.checkin_time" +
                ",b.checkout_time,b.real_checkout_time,b.checkin_no" +
                /*",b.`name`,d.card_type as cardType" +*/
                ",SUM(a.actual_times) as sumTimes,SUM(a.actual_amount) as sumAmount" +
                ",SUM(a.actual_integrals) as sumIntegrals,SUM(a.actual_points) as sumPoints" +
                /*",g.bed_name as bedName" +*/
                ",a.settle_status as settleStatus " +
                /*",(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and type='follow_checkin' and del_flag='0'  ) as followCount " +*/
                "from fina_settle_detail a \n" +
                "left join fina_expense_record b on a.expense_id=b.id \n" +
                /*"left join fina_membership_card c on c.id=(select id from fina_membership_card where a.card_number=card_number ORDER BY del_flag limit 1) \n" +
                "left join main_membership_card_type d on c.card_type_id=d.id \n" +
                "left join main_card_deduct_scheme e on e.id=c.long_deduct_scheme_id \n" +
                "left join  main_base_bed g on g.id=b.bed_id " +*/
                "where  a.del_flag='0' and a.settle_status='1' and b.del_flag='0' and b.checkin_no in ("+str+") \n" +
                "and a.settle_status in ('0','1')  GROUP BY b.id\n" +
                " order by create_time ";
        List<Record> recordList=Db.find(sql,params.toArray());

        if(recordList.size()==0){
            renderCodeSuccess("success",recordList);
            return;
        }
        Map<String,Record> recordMap=new TreeMap<>();
        for(Record record:recordList){
            if("0".equals(record.getStr("parent_id"))){
                recordMap.put(record.getStr("id"),record);
            }
        }
        for(Record record:recordList){
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    FinaExpenseRecord mainRecord=finaExpenseRecordService.findById(record.getStr("parent_id"));
                    parentRecord=new Record();
                    parentRecord.setColumns(record);
                    parentRecord.set("checkin_status",mainRecord.getCheckinStatus());
                    parentRecord.set("book_no",mainRecord.getBookNo());
                    //parentRecord.set("card_number",mainRecord.getCardNumber());
                    parentRecord.set("book_start_time",mainRecord.getBookStartTime());
                    parentRecord.set("book_end_time",mainRecord.getBookEndTime());
                    parentRecord.set("checkin_time",mainRecord.getCheckinTime());
                    parentRecord.set("checkout_time",mainRecord.getCheckoutTime());
                    parentRecord.set("real_checkout_time",mainRecord.getRealCheckoutTime());
                    parentRecord.set("is_private_room",mainRecord.getIsPrivateRoom());

                    recordMap.put(record.getStr("parent_id"),parentRecord);
                    continue;
                }
                parentRecord.set("sumTimes",parentRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
                parentRecord.set("sumAmount",parentRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
                parentRecord.set("sumIntegrals",parentRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
                parentRecord.set("sumPoints",parentRecord.getDouble("sumPoints")+record.getDouble("sumPoints"));
            }
        }
        List<Record> returnList=new ArrayList<>();

        List<String> continuedParams=new ArrayList<>();
        StringBuffer continuedBuff=new StringBuffer("");
        for(String key:recordMap.keySet()){
            Record record=recordMap.get(key);
            returnList.add(record);
            Double sumTimes=record.getDouble("sumTimes");
            Double sumAmount=record.getDouble("sumAmount");
            Double sumIntegrals=record.getDouble("sumIntegrals");
            if(sumTimes==null){
                sumTimes=0.0;
            }
            if(sumAmount==null){
                sumAmount=0.0;
            }
            if(sumIntegrals==null){
                sumIntegrals=0.0;
            }
            double settleDays=0.0;
            if(sumTimes>=0.0 || sumAmount>0.0 || sumIntegrals>0.0){
                if(sumAmount>0.0){


                }else{
                    settleDays=sumTimes+sumIntegrals;
                }
            }
            record.set("settleDays",settleDays);

            continuedParams.add(key);
            continuedBuff.append("?,");
        }

        String continuedStr=continuedBuff.substring(0,continuedBuff.length()-1);
        String continuedSql="select parent_id,MAX(checkout_time) as maxCheckout from fina_expense_record where type='continued_residence' " +
                "and parent_id in("+continuedStr+") and del_flag='0' and settle_status in ('0','1') GROUP BY parent_id ";
        List<Record> continuedList=Db.find(continuedSql,continuedParams.toArray());
        for(Record continued:continuedList){
            Record record=recordMap.get(continued.getStr("parent_id"));
            record.set("checkout_time",continued.getDate("maxCheckout"));
        }

        if(returnList.size()>0) {
            for (String key : recordMap.keySet()) {
                Record record = recordMap.get(key);
                if ("book".equals(record.getStr("checkin_status"))) {
                    record.set("start", record.getDate("book_start_time"));
                    record.set("end", record.getDate("book_end_time"));
                } else if ("stayin".equals(record.getStr("checkin_status"))) {
                    record.set("start", record.getDate("checkin_time"));
                    record.set("end", record.getDate("checkout_time"));
                } else if ("retreat".equals(record.getStr("checkin_status"))) {
                    record.set("start", record.getDate("checkin_time"));
                    record.set("end", record.getDate("real_checkout_time"));
                }
                Date start = record.getDate("start");
                Date end = record.getDate("end");

                Date calStart = null;
                Date calEnd = null;
                /*if(DateUtils.compareDays(start,DateUtils.parseDate(startDate+" 00:00:00"))<0){
                    calStart=DateUtils.parseDate(startDate+" 00:00:00");
                }else{
                    calStart=start;
                    calStart=DateUtils.parseDate(DateUtils.formatDate(calStart,"yyyy-MM-dd")+" 00:00:00");
                }

                if(DateUtils.compareDays(end,DateUtils.parseDate(endDate+" 23:59:59"))>0){
                    calEnd=DateUtils.parseDate(endDate+" 24:59:59");
                }else{
                    calEnd=end;
                    calEnd=DateUtils.parseDate(DateUtils.formatDate(calEnd,"yyyy-MM-dd")+" 23:59:59");
                }*/
                calStart = start;
                calEnd = end;
                long days = (calEnd.getTime() - calStart.getTime()) / (86400000);
                record.set("checkinDays", days);

                Double sumTimes=record.getDouble("sumTimes");
                Double sumAmount=record.getDouble("sumAmount");
                Double sumIntegrals=record.getDouble("sumIntegrals");
                if(sumTimes==null){
                    sumTimes=0.0;
                }
                if(sumAmount==null){
                    sumAmount=0.0;
                }
                if(sumIntegrals==null){
                    sumIntegrals=0.0;
                }
                if (record.getDouble("sumAmount") != null && record.getDouble("sumAmount") > 0) {
                    double settleDays = days;
                    if ("1".equals(record.getStr("is_private_room"))) {
                        settleDays = days * 2;
                    }
                    if (record.getInt("followCount") != null && record.getInt("followCount") > 0) {
                        settleDays += days * record.getInt("followCount");
                    }
                    record.set("settleDays", settleDays);
                }else if(sumTimes==0 && sumAmount==0.0 && sumIntegrals==0.0){
                    double settleDays=record.getDouble("checkinDays");
                    if("1".equals(record.getStr("is_private_room"))){
                        settleDays=record.getDouble("checkinDays")*2;
                    }
                    if(record.getInt("followCount")!=null && record.getInt("followCount")>0){
                        settleDays+=record.getDouble("checkinDays")*record.getInt("followCount");
                    }
                    record.set("settleDays",settleDays);
                }
            }

        }

        renderCodeSuccess("success",returnList);

    }

    public void getAllSettleDaysJson(){
        String dataStr= HttpKit.readData(getRequest());
        if(StrKit.isBlank(dataStr)){
            renderFailed("参数缺失");
            return;
        }
        JSONArray checkinNoArray=JSON.parseArray(dataStr);
        if(checkinNoArray==null || checkinNoArray.size()==0){
            renderCodeFailed("入住号不能为空");
            return;
        }
        String str="";
        List<Object> params=new ArrayList<>();

        for (int i = 0; i < checkinNoArray.size(); i++) {
            params.add(checkinNoArray.getString(i));
        }
        for (int i = 0; i < checkinNoArray.size(); i++) {
            str+="?,";
            params.add(checkinNoArray.getString(i));
        }

        str=str.substring(0,str.length()-1);
        String sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.create_time,a.book_start_time,a.book_end_time,a.checkin_time" +
                ",a.checkout_time,a.real_checkout_time,a.checkin_no,a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount" +
                ",SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,'0' as settleStatus, (select COUNT(DISTINCT id_card) from fina_expense_record " +
                "where parent_id=a.id and type='follow_checkin' and del_flag='0'  ) as followCount from fina_expense_record a \n" +
                "left join fina_expense_record_detail d on d.expense_id=a.id \n" +
                "left join fina_membership_card b on b.id=(select id from fina_membership_card where d.card_number=card_number ORDER BY del_flag limit 1)  \n" +
                "left join main_membership_card_type c on b.card_type_id=c.id \n" +
                "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id left join  main_base_bed g on g.id=a.bed_id " +
                "where  a.category='sojourn_bill' and d.del_flag='0'  and a.checkin_no in ("+str+") " +
                "and a.settle_status ='0' and a.del_flag='0' and d.is_settled='0' GROUP BY a.id \n" +
                "UNION\n" +
                "select b.is_private_room,b.checkin_status,b.book_no,b.card_number,b.id,b.parent_id,b.create_time,b.book_start_time,b.book_end_time,b.checkin_time" +
                ",b.checkout_time,b.real_checkout_time,b.checkin_no,b.`name`,d.card_type as cardType,SUM(a.actual_times) as sumTimes,SUM(a.actual_amount) as sumAmount" +
                ",SUM(a.actual_integrals) as sumIntegrals,SUM(a.actual_points) as sumPoints,g.bed_name as bedName,a.settle_status as settleStatus" +
                ",(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and type='follow_checkin' and del_flag='0'  ) as followCount from fina_settle_detail a \n" +
                "left join fina_expense_record b on a.expense_id=b.id \n" +
                "left join fina_membership_card c on c.id=(select id from fina_membership_card where a.card_number=card_number ORDER BY del_flag limit 1) \n" +
                "left join main_membership_card_type d on c.card_type_id=d.id \n" +
                "left join main_card_deduct_scheme e on e.id=c.long_deduct_scheme_id \n" +
                "left join  main_base_bed g on g.id=b.bed_id where  a.del_flag='0' and a.settle_status='1' and b.del_flag='0' and b.checkin_no in ("+str+") \n" +
                "and a.settle_status in ('0','1')  GROUP BY b.id\n" +
                " order by create_time ";
        List<Record> recordList=Db.find(sql,params.toArray());

        if(recordList.size()==0){
            renderCodeSuccess("success",recordList);
            return;
        }
        Map<String,Record> recordMap=new TreeMap<>();
        for(Record record:recordList){
            if("0".equals(record.getStr("parent_id"))){
                recordMap.put(record.getStr("id"),record);
            }
        }
        for(Record record:recordList){
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    FinaExpenseRecord mainRecord=finaExpenseRecordService.findById(record.getStr("parent_id"));
                    parentRecord=new Record();
                    parentRecord.setColumns(record);
                    parentRecord.set("checkin_status",mainRecord.getCheckinStatus());
                    parentRecord.set("book_no",mainRecord.getBookNo());
                    //parentRecord.set("card_number",mainRecord.getCardNumber());
                    parentRecord.set("book_start_time",mainRecord.getBookStartTime());
                    parentRecord.set("book_end_time",mainRecord.getBookEndTime());
                    parentRecord.set("checkin_time",mainRecord.getCheckinTime());
                    parentRecord.set("checkout_time",mainRecord.getCheckoutTime());
                    parentRecord.set("real_checkout_time",mainRecord.getRealCheckoutTime());
                    parentRecord.set("is_private_room",mainRecord.getIsPrivateRoom());

                    recordMap.put(record.getStr("parent_id"),parentRecord);
                    continue;
                }
                parentRecord.set("sumTimes",parentRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
                parentRecord.set("sumAmount",parentRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
                parentRecord.set("sumIntegrals",parentRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
                parentRecord.set("sumPoints",parentRecord.getDouble("sumPoints")+record.getDouble("sumPoints"));
            }
        }
        List<Record> returnList=new ArrayList<>();

        List<String> continuedParams=new ArrayList<>();
        StringBuffer continuedBuff=new StringBuffer("");
        for(String key:recordMap.keySet()){
            Record record=recordMap.get(key);
            returnList.add(record);
            Double sumTimes=record.getDouble("sumTimes");
            Double sumAmount=record.getDouble("sumAmount");
            Double sumIntegrals=record.getDouble("sumIntegrals");
            if(sumTimes==null){
                sumTimes=0.0;
            }
            if(sumAmount==null){
                sumAmount=0.0;
            }
            if(sumIntegrals==null){
                sumIntegrals=0.0;
            }
            double settleDays=0.0;
            if(sumTimes>=0.0 || sumAmount>0.0 || sumIntegrals>0.0){
                if(sumAmount>0.0){


                }else{
                    settleDays=sumTimes+sumIntegrals;
                }
            }
            record.set("settleDays",settleDays);

            continuedParams.add(key);
            continuedBuff.append("?,");
        }

        String continuedStr=continuedBuff.substring(0,continuedBuff.length()-1);
        String continuedSql="select parent_id,MAX(checkout_time) as maxCheckout from fina_expense_record where type='continued_residence' " +
                "and parent_id in("+continuedStr+") and del_flag='0' and settle_status in ('0','1') GROUP BY parent_id ";
        List<Record> continuedList=Db.find(continuedSql,continuedParams.toArray());
        for(Record continued:continuedList){
            Record record=recordMap.get(continued.getStr("parent_id"));
            record.set("checkout_time",continued.getDate("maxCheckout"));
        }


        if(returnList.size()>0) {
            for (String key : recordMap.keySet()) {
                Record record = recordMap.get(key);
                if ("book".equals(record.getStr("checkin_status"))) {
                    record.set("start", record.getDate("book_start_time"));
                    record.set("end", record.getDate("book_end_time"));
                } else if ("stayin".equals(record.getStr("checkin_status"))) {
                    record.set("start", record.getDate("checkin_time"));
                    record.set("end", record.getDate("checkout_time"));
                } else if ("retreat".equals(record.getStr("checkin_status"))) {
                    record.set("start", record.getDate("checkin_time"));
                    record.set("end", record.getDate("real_checkout_time"));
                }
                Date start = record.getDate("start");
                Date end = record.getDate("end");

                Date calStart = null;
                Date calEnd = null;
                /*if(DateUtils.compareDays(start,DateUtils.parseDate(startDate+" 00:00:00"))<0){
                    calStart=DateUtils.parseDate(startDate+" 00:00:00");
                }else{
                    calStart=start;
                    calStart=DateUtils.parseDate(DateUtils.formatDate(calStart,"yyyy-MM-dd")+" 00:00:00");
                }

                if(DateUtils.compareDays(end,DateUtils.parseDate(endDate+" 23:59:59"))>0){
                    calEnd=DateUtils.parseDate(endDate+" 24:59:59");
                }else{
                    calEnd=end;
                    calEnd=DateUtils.parseDate(DateUtils.formatDate(calEnd,"yyyy-MM-dd")+" 23:59:59");
                }*/
                calStart = start;
                calEnd = end;
                long days = (calEnd.getTime() - calStart.getTime()) / (86400000);
                record.set("checkinDays", days);

                if (record.getDouble("sumAmount") != null && record.getDouble("sumAmount") > 0) {
                    double settleDays = days;
                    if ("1".equals(record.getStr("is_private_room"))) {
                        settleDays = days * 2;
                    }
                    if (record.getInt("followCount") != null && record.getInt("followCount") > 0) {
                        settleDays += days * record.getInt("followCount");
                    }
                    record.set("settleDays", settleDays);
                }
            }

        }

        renderCodeSuccess("success",returnList);

    }

    public void getTouristTotalTimes(){
        String touristNo=getPara("touristNo");
        if(StrKit.isBlank(touristNo)){
            renderCodeFailed("参数缺失");
            return;
        }
        Record record=Db.findFirst("select SUM(actual_times) as totalTimes,SUM(actual_amount) as totalAmount,SUM(actual_integrals) as totalIntegrals  " +
                "from fina_expense_record where category='tourist_bill' and del_flag='0' and tourist_no=?  and settle_status in ('0','1') ",touristNo);
        renderCodeSuccess("success",record);
    }


    public void test(){
        String sql="";

        List<String> cardIdList=new ArrayList<>();

        for(String cardId:cardIdList){
            Page<FinaCardTransactions> page = finaCardTransactionsService.getTransByParams2(1,10000,cardId,null,null,null,null,"");
            FinaMembershipCard card=finaMembershipCardService.findById(cardId);
            String deductSchemeId=null;
            if(StrKit.notBlank(card.getDeductSchemeId())){
                deductSchemeId=card.getDeductSchemeId();
            }else if(StrKit.notBlank(card.getLongDeductSchemeId())){
                deductSchemeId=card.getLongDeductSchemeId();
            }
            if(StrKit.notBlank(deductSchemeId)){
                continue;
            }
            if(page==null || page.getList()==null || page.getList().size()==0){
                continue;
            }
            List<FinaCardTransactions> updateList=new ArrayList<>();
            MainCardDeductScheme deductScheme=mainCardDeductSchemeService.findById(deductSchemeId);
            if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
                for (int i = 0; i < page.getList().size(); i++) {
                    FinaCardTransactions transactions=page.getList().get(i);
                    if(transactions.getTimesSnapshot()==null){
                        if(i==0){
                            continue;
                        }else{
                            FinaCardTransactions  prevTransactions=page.getList().get(i-1);
                            if("1".equals(prevTransactions.getInOutFlag())){
                                //+
                                transactions.setTimesSnapshot(prevTransactions.getTimesSnapshot()+prevTransactions.getTimes());
                                updateList.add(transactions);
                            }else if("2".equals(prevTransactions.getInOutFlag())){
                                //-
                                transactions.setTimesSnapshot(prevTransactions.getTimesSnapshot()-prevTransactions.getTimes());
                                updateList.add(transactions);
                            }
                        }
                    }
                }
                if(updateList.size()>0){
                    Db.batchUpdate(updateList,updateList.size());
                }
            }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                for (int i = 0; i < page.getList().size(); i++) {
                    FinaCardTransactions transactions=page.getList().get(i);
                    if(transactions.getAmountSnapshot()==null){
                        if(i==0){
                            continue;
                        }else{
                            FinaCardTransactions  prevTransactions=page.getList().get(i-1);
                            if("1".equals(prevTransactions.getInOutFlag())){
                                //+
                                transactions.setAmountSnapshot(prevTransactions.getAmountSnapshot()+prevTransactions.getAmount());
                                updateList.add(transactions);
                            }else if("2".equals(prevTransactions.getInOutFlag())){
                                //-
                                transactions.setAmountSnapshot(prevTransactions.getAmountSnapshot()-prevTransactions.getAmount());
                                updateList.add(transactions);
                            }
                        }
                    }
                }
                if(updateList.size()>0){
                    Db.batchUpdate(updateList,updateList.size());
                }
            }


        }

    }


    public void updateRemarkByCheckinNo(){
        String checkinNo=getPara("checkinNo");
        String remark=getPara("remark");
        if(StrKit.isBlank(checkinNo) || StrKit.isBlank(remark)){
            renderCodeFailed("参数缺失");
            return;
        }
        FinaExpenseRecord record=finaExpenseRecordService.findMainRecordByCheckinNo(checkinNo);
        if(record==null){
            renderCodeFailed("该入住号未查询到入住记录");
            return;
        }
        record.setRemark(record.getRemark()+"\n"+DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss")+"："+remark);
        if(record.update()){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void genBook(){
        String bookNo=getPara("bookNo");
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");
        String isCheckout=getPara("isCheckout");
        FinaExpenseRecord record=finaExpenseRecordService.findMainRecordByBookNo(bookNo);
        if(record==null){
            renderCodeFailed("该预定号未查寻到订单");
            return;
        }

        String totalSql="select card_number as cardNumber,sum(amount) as totalAmount,sum(times) as totalTimes,sum(points) as totalPoints,sum(integrals) as totalIntegrals" +
                ",min(start_time) as startDate,max(end_time) as endDate from fina_expense_record_detail " +
                "where del_flag='0' and is_settled='0' and expense_id in(select id from fina_expense_record " +
                "where del_flag='0' and settle_status='0' and (id=? or parent_id=?)) and to_days(start_time)>=to_days(?) and to_days(end_time)<=to_days(?) group by card_number ";
        List<Record> totalRecordList=Db.find(totalSql,record.getId(),record.getId(),startDate,endDate);
        for(Record totalRecord:totalRecordList){
            FinaSettleDetail settleDetail=new FinaSettleDetail();
            settleDetail.setId(IdGen.getUUID());
            settleDetail.setActualAmount(totalRecord.getDouble("totalAmount"));
            settleDetail.setActualTimes(totalRecord.getDouble("totalTimes"));
            settleDetail.setActualPoints(totalRecord.getDouble("totalPoints"));
            settleDetail.setActualIntegrals(totalRecord.getDouble("totalIntegrals"));
            settleDetail.setTotalAmount(totalRecord.getDouble("totalAmount"));
            settleDetail.setTotalTimes(totalRecord.getDouble("totalTimes"));
            settleDetail.setTotalPoints(totalRecord.getDouble("totalPoints"));
            settleDetail.setTotalIntegrals(totalRecord.getDouble("totalIntegrals"));


            settleDetail.setExpenseId(record.getId());
            settleDetail.setCardNumber(totalRecord.getStr("cardNumber"));
            settleDetail.setStartTime(totalRecord.getDate("startDate"));
            settleDetail.setEndTime(totalRecord.getDate("endDate"));
            settleDetail.setDelFlag("0");
            if("1".equalsIgnoreCase(isCheckout)){
                settleDetail.setSettleType("checkout_settle");
            }else{
                settleDetail.setSettleType("month_settle");
            }
            settleDetail.setSettleStatus("0");
            settleDetail.setYearMonth(DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyy-MM"));
            settleDetail.setCreateTime(new Date());
            settleDetail.setUpdateTime(new Date());
            settleDetail.save();
        }
        renderCodeSuccess("success");
    }

    public void getDeduCardsByCheckinNo(){
        String checkinNo=getPara("checkinNo");
        String endDate=getPara("endDate");
        if(StrKit.isBlank(checkinNo) && StrKit.isBlank(endDate)){
            renderCodeFailed("参数缺失");
            return;
        }
        FinaExpenseRecord mainRecord=finaExpenseRecordService.findMainRecordByCheckinNo(checkinNo);
        if(mainRecord==null){
//            renderCodeFailed("该入住号查询入住单失败");
            renderCodeSuccess("success",new JSONObject());
            return;
        }
        List<String> idList=Db.query(" select id from fina_expense_record where parent_id=? and del_flag='0' ",mainRecord.getId());

        List<Record> recordList=Db.find("select card_number as cardNumber,SUM(actual_times) as times,SUM(actual_integrals) as integrals,SUM(actual_amount) as amount,MAX(end_time) as endTime from fina_settle_detail " +
                "where expense_id=? and del_flag='0' GROUP BY card_number ORDER BY endTime ",mainRecord.getId());

        String startDate=null;


        if(recordList.size()>0){
            startDate=DateUtils.formatDate(recordList.get(recordList.size()-1).getDate("endTime"),"yyyy-MM-dd");
        }
        idList.add(mainRecord.getId());

        List<Object> detailParams=new ArrayList<>();
        String str="";

        for(String id:idList){
            str+="?,";
            detailParams.add(id);
        }
        str=str.substring(0,str.length()-1);

        String detailSql=" select card_number as cardNumber,SUM(times) as times,SUM(integrals) as integrals,SUM(amount) as amount " +
                "from fina_expense_record_detail where expense_id in ("+str+") " +
                "and del_flag='0'  ";
        if(StrKit.notBlank(startDate)){
            detailSql+=" and start_time>=? ";
            detailParams.add(startDate+" 00:00:00");
        }
        if(StrKit.notBlank(endDate)){
            detailSql+=" and start_time<? ";
            detailParams.add(endDate+" 00:00:00");
        }

        detailSql+=" GROUP BY card_number ";

        List<Record> detailList=Db.find(detailSql,detailParams.toArray());


        Map<String,Record> recordMap=new HashMap<>();

        for(Record record:recordList){
            record.set("endTime",null);
            recordMap.put(record.getStr("cardNumber"),record);
        }

        for(Record record:detailList){
            if(recordMap.containsKey(record.getStr("cardNumber"))){
                Record mapRecord=recordMap.get(record.getStr("cardNumber"));
                Double times1=mapRecord.getDouble("times");
                Double times2=record.getDouble("times");

                Double integrals1=mapRecord.getDouble("integrals");
                Double integrals2=record.getDouble("integrals");

                Double amount1=mapRecord.getDouble("amount");
                Double amount2=record.getDouble("amount");

                if(times1==null){
                    times1=0.0;
                }
                if(times2==null){
                    times2=0.0;
                }
                if(integrals1==null){
                    integrals1=0.0;
                }
                if(integrals2==null){
                    integrals2=0.0;
                }
                if(amount1==null){
                    amount1=0.0;
                }
                if(amount2==null){
                    amount2=0.0;
                }

                mapRecord.set("times",times1+times2);
                mapRecord.set("integrals",integrals1+integrals2);
                mapRecord.set("amount",amount1+amount2);

                recordMap.put(record.getStr("cardNumber"),mapRecord);
            }else{
                recordMap.put(record.getStr("cardNumber"),record);
            }
        }

        List<Record> returnList=new ArrayList<>();

        for(String key:recordMap.keySet()){
            returnList.add(recordMap.get(key));
        }

        String sql="select a.year_month as yearMonth,d.deduct_way as deductWay,f.deduct_way as longdeductWay,"
                + "a.settle_remark as settleRemark,c.full_name as fullName,a.id,a.expense_id as expenseId,"
                + "a.start_time as startTime,a.end_time as endTime,a.total_times as totalTimes,"
                + "a.total_amount as totalAmount,IFNULL(a.total_points,0) as totalPoints,"
                + "IFNULL(a.total_integrals,0) as totalIntegrals,a.card_number as cardNumber,a.settle_type as settleType,"
                + "a.settle_status as settleStatus,a.actual_times as actualTimes,a.actual_amount as actualAmount,"
                + "IFNULL(a.actual_points,0) as actualPoints,IFNULL(a.actual_integrals,0) as actualIntegrals,"
                + "e.is_integral as isIntegral from fina_settle_detail a "
                + "left join fina_membership_card b on b.id=(select id from fina_membership_card where card_number=a.card_number order by del_flag limit 1) "
                + "left join mms_member c on c.id=b.member_id "
                + "left join main_card_deduct_scheme d on d.id=b.deduct_scheme_id "
                + "left join main_membership_card_type e on e.id=b.card_type_id "
                + "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
                + "where expense_id=? and a.del_flag='0' "
                + "order by a.card_number,a.end_time";
        List<Record> finaSettleList=Db.find(sql, mainRecord.getId());
        for(Record record:finaSettleList){
            if(StrKit.isBlank(record.getStr("deductWay"))){
                record.set("deductWay",record.getStr("longdeductWay"));
            }
        }

        //List<Record> finaSettleList=finaSettleDetailService.findSettleDetailList(mainRecord.getId());



        Map<String,List<Record>> map=new HashMap<>();
        for(Record record:finaSettleList){
            String cardNumber=record.getStr("cardNumber");
            Date startTime=record.getDate("startTime");
            Date endTime=record.getDate("endTime");
            Double actualTimes=record.getDouble("actualTimes");
            Double actualAmount=record.getDouble("actualAmount");
            Double actualIntegrals=record.getDouble("actualIntegrals");
            if(actualTimes==null){
                actualTimes=0.0;
            }
            if(actualAmount==null){
                actualAmount=0.0;
            }
            if(actualIntegrals==null){
                actualIntegrals=0.0;
            }

            if(map.containsKey(cardNumber)){
                List<Record> cardRecordList=map.get(cardNumber);
                /*if(cardRecordList!=null){

                }*/
                boolean flag=true;
                for (Record cardRecord : cardRecordList) {
                    String cardRecordStartTime=cardRecord.getStr("startTime");
                    String cardRecordEndTime=cardRecord.getStr("endTime");
                    Double cardRecordTimes=cardRecord.getDouble("times");
                    Double cardRecordAmount=cardRecord.getDouble("amount");
                    Double cardRecordIntegrals=cardRecord.getDouble("integrals");
                    if(DateUtils.formatDate(startTime,"yyyy-MM-dd").equals(cardRecordEndTime)){

                        cardRecord.set("endTime",DateUtils.formatDate(endTime,"yyyy-MM-dd"));
                        cardRecord.set("times",actualTimes+cardRecordTimes);
                        cardRecord.set("amount",actualAmount+cardRecordAmount);
                        cardRecord.set("integrals",actualIntegrals+cardRecordIntegrals);
                        if(flag){
                            flag=false;
                        }
                    }
                }
                if(flag){
                    List<Record> newCardRecordList=new ArrayList<>();
                    Record addRecord=new Record();
                    addRecord.set("startTime",DateUtils.formatDate(startTime,"yyyy-MM-dd"));
                    addRecord.set("endTime",DateUtils.formatDate(endTime,"yyyy-MM-dd"));
                    addRecord.set("times",actualTimes);
                    addRecord.set("amount",actualAmount);
                    addRecord.set("integrals",actualIntegrals);
                    newCardRecordList.addAll(cardRecordList);
                    newCardRecordList.add(addRecord);

                    map.put(cardNumber,newCardRecordList);
                }

            }else{
                Record cardRecord=new Record();
                cardRecord.set("startTime",DateUtils.formatDate(startTime,"yyyy-MM-dd"));
                cardRecord.set("endTime",DateUtils.formatDate(endTime,"yyyy-MM-dd"));
                cardRecord.set("times",actualTimes);
                cardRecord.set("amount",actualAmount);
                cardRecord.set("integrals",actualIntegrals);

                List<Record> cardRecordList=new ArrayList<>();
                cardRecordList.add(cardRecord);
                map.put(cardNumber,cardRecordList);
            }
        }

        Map<String,Object> returnMap=new HashMap<>();
        returnMap.put("code","0");
        returnMap.put("msg","success");
        JSONObject jsonObject=new JSONObject();
        jsonObject.put("total",returnList);
        jsonObject.put("detail",map);
        returnMap.put("data",jsonObject);

        //returnMap.put("detail",map);

        //renderCodeSuccess("success",returnList);
        renderJson(returnMap);
    }

    /*public void getAllSettleDetailByCheckinNo(){
        String checkinNo=getPara("checkinNo");
        FinaExpenseRecord record=finaExpenseRecordService.findMainRecordByCheckinNo(checkinNo);
        if(record==null){
            renderCodeSuccess("success",new ArrayList<>());
            return;
        }

        renderCodeSuccess("success",finaSettleList);
    }*/

    public void saveExpenseRolls(){
        String bookNo=getPara("bookNo");
        String checkinNo=getPara("checkinNo");
        String rolls=getPara("rolls");
        String userId=getPara("userId");

        if(StrKit.isBlank(bookNo) && StrKit.isBlank(checkinNo)){
            renderCodeFailed("参数缺失");
            return;
        }
        if(StrKit.isBlank(rolls)){
            renderCodeFailed("参数缺失");
            return;
        }
        String expenseId=null;
        if(StrKit.notBlank(bookNo)){
            expenseId=Db.queryStr("select id from fina_expense_record where book_no=? and parent_id='0' and del_flag='0' limit 1",bookNo);
        }else if(StrKit.notBlank(checkinNo)){
            expenseId=Db.queryStr("select id from fina_expense_record where checkin_no=? and parent_id='0' and del_flag='0' limit 1",bookNo);
        }


        JSONArray jsonArray=JSON.parseArray(rolls);

        Db.update(" update fina_expense_record_roll set del_flag='1',update_date=?,update_by=? where expense_id=? and del_flag='0' ",new Date(),userId,expenseId);

        List<FinaExpenseRecordRoll> rollList=new ArrayList<>();
        if(jsonArray!=null){
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                FinaExpenseRecordRoll roll=new FinaExpenseRecordRoll();
                roll.setId(IdGen.getUUID());
                roll.setExpenseId(expenseId);
                roll.setRollId(jsonObject.getString("id"));
                roll.setCount(jsonObject.getInteger("count"));
                roll.setDelFlag("0");
                roll.setUpdateDate(new Date());
                roll.setCreateDate(new Date());
                roll.setUpdateBy(userId);
                roll.setCreateBy(userId);
                rollList.add(roll);
            }
        }

        if(rollList!=null && rollList.size()>0){
            Db.batchSave(rollList,rollList.size());
        }
        renderCodeSuccess("success");
    }

    @Inject
    CardMonthBalanceTask cardMonthBalanceTask;

    public void genNewMonthBalance(){
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");
        cardMonthBalanceTask.newMonthBalance(1,500,startDate,endDate);
        renderJson("ok");
    }

    public void getSpaDeductByCardNumber(){
        String cardNumber=getPara("cardNumber");
        Integer times=getParaToInt("times");
        String cardMobile=getPara("cardMobile");
        if(StrKit.isBlank(cardNumber) || times==null || StrKit.isBlank(cardMobile)){
            renderCodeFailed("参数缺失");
            return;
        }
        FinaMembershipCard card=finaMembershipCardService.findCardByCardNumber(cardNumber);
        if(card==null){
            renderCodeFailed("该会员卡号查询会员卡失败");
            return;
        }
        if("1".equals(card.getIsLock())){
            renderCodeFailed("该会员卡已被锁定");
            return;
        }
        if(!cardMobile.equals(card.getTelephone())){
            renderCodeFailed("手机号码与会员卡的号码不匹配");
            return;
        }

        MainCardDeductScheme deductScheme=mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if(deductScheme==null){
            deductScheme= mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        }
        if(deductScheme==null){
            renderCodeFailed("该会员卡扣费方案未设置");
            return;
        }

        Map<String,Object> returnMap=new HashMap<>();
        returnMap.put("cardNumber",cardNumber);
        returnMap.put("deductWay",deductScheme.getDeductWay());
        if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
            returnMap.put("deductValue",times);
        }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
            returnMap.put("deductValue",(deductScheme.getPrice()*times));
        }
        renderCodeSuccess("success",returnMap);
    }

    @Inject
    MainBaseGoodConfigsService mainBaseGoodConfigsService;
    @Inject
    MainGoodModelsService mainGoodModelsService;

    public void baseGoodDeductCard(){

        String data=HttpKit.readData(getRequest());

        if(StrKit.isBlank(data) || !data.startsWith("{") || !data.endsWith("}")){
            renderCodeFailed("参数格式不正确");
            return;
        }
        JSONObject dataObject=JSON.parseObject(data);

        String orderNo=dataObject.getString("orderNo");
        String cardNumber=dataObject.getString("cardNumber");
        String orderDetail=dataObject.getString("orderDetail");
        String checkinNo=dataObject.getString("checkinNo");
        String userId=dataObject.getString("userId");

        if(StrKit.isBlank(orderNo) || StrKit.isBlank(cardNumber) ||StrKit.isBlank(orderDetail) ){
            renderCodeFailed("参数缺失");
            return;
        }
        JSONArray orderDetailArray=JSON.parseArray(orderDetail);
        if(orderDetailArray==null || orderDetailArray.size()==0){
            renderCodeFailed("商品数据不能为空");
            return;
        }
        double totalTimes=0.0;
        String baseId=null;

        FinaBaseGoodOrder finaBaseGoodOrder=new FinaBaseGoodOrder();
        finaBaseGoodOrder.setCardNumber(cardNumber);
        finaBaseGoodOrder.setId(IdGen.getUUID());
        finaBaseGoodOrder.setCheckinNo(checkinNo);
        finaBaseGoodOrder.setOrderNo(orderNo);
        finaBaseGoodOrder.setStatus("1");
        finaBaseGoodOrder.setDelFlag("0");
        finaBaseGoodOrder.setCreateBy(userId);
        finaBaseGoodOrder.setUpdateBy(userId);
        finaBaseGoodOrder.setCreateTime(new Date());
        finaBaseGoodOrder.setUpdateTime(new Date());

        List<FinaBaseGoodOrderItem> orderItemList=new ArrayList<>();

        String goodsDescription="";
        int goodNums=0;
        String goodName="";
        //生成消费购买、赠送明细列表记录
        List<FinaCardTransactionsDetail> transactionsDetailList = new ArrayList<>();
        for (int i = 0; i < orderDetailArray.size(); i++) {
            JSONObject orderItemObj=orderDetailArray.getJSONObject(i);
            String goodId=orderItemObj.getString("goodId");
            baseId=orderItemObj.getString("baseId");
            Integer num=orderItemObj.getInteger("num");

            MainBaseGoodConfigs goodConfig=mainBaseGoodConfigsService.getBaseGoodConfig(baseId,goodId);
            double unitTimes=goodConfig.getCardTimes();
            totalTimes+=(unitTimes*num);

            goodNums+=num;

            FinaBaseGoodOrderItem orderItem=new FinaBaseGoodOrderItem();
            orderItem.setId(IdGen.getUUID());
            orderItem.setOrderId(finaBaseGoodOrder.getId());
            orderItem.setGoodId(goodConfig.getGoodModelId());
            orderItem.setNum(num);
            orderItem.setUnitTimes(unitTimes);
            orderItemList.add(orderItem);

            MainGoodModels goodModels=mainGoodModelsService.findById(goodConfig.getGoodModelId());
            goodsDescription+=goodModels.getName()+"*"+num+";";
            if(StrKit.isBlank(goodName)){
                goodName=goodModels.getName();
            }

        }
        FinaMembershipCard card=finaMembershipCardService.findCardByCardNumber(cardNumber);
        MainCardDeductScheme deductScheme=mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if(deductScheme==null){
            deductScheme= mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        }
        if(deductScheme==null){
            renderCodeFailed("会员卡扣费方案未设置");
            return;
        }
        MmsMember member = mmsMemberService.findById(card.getMemberId());
        finaBaseGoodOrder.setBaseId(baseId);
        MainBase mainBase=mainBaseService.findById(baseId);
        Map<String,Double> cardLockMap=finaMembershipCardService.getCardLockInfo(cardNumber);




        Date nowDate=new Date();
        FinaCardTransactions cardTransactions=new FinaCardTransactions();
        cardTransactions.setId(IdGen.getUUID());
        cardTransactions.setCardId(card.getId());
        cardTransactions.setExpenseId(finaBaseGoodOrder.getId());
        cardTransactions.setTimes(0.0);
        cardTransactions.setAmount(0.0);
        cardTransactions.setIntegrals(0.0);
        cardTransactions.setPoints(0.0);
        cardTransactions.setBeanCoupons(0.0);
        cardTransactions.setTimesSnapshot(0.0);
        cardTransactions.setAmountSnapshot(0.0);
        cardTransactions.setIntegralsSnapshot(0.0);
        cardTransactions.setPointsSnapshot(0.0);
        cardTransactions.setBeanCouponsSnapshot(0.0);
        cardTransactions.setTransactionNo(orderNo);
        cardTransactions.setStartDate(nowDate);
        cardTransactions.setEndDate(nowDate);
        cardTransactions.setInOutFlag("2");
        cardTransactions.setIsHidden("0");
        cardTransactions.setDealTime(nowDate);
        cardTransactions.setType(AccountRecordOperateType.dailyDeduction.getKey());


        String useAmount="";
        String leftAmount="";
        double buyAmount = 0.0;
        double giveAmount = 0.0;
        double buyDays = 0.0;
        double giveDays = 0.0;
        double giveIntegrals = 0.0;
        if(DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())){
            MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
            //获取会员卡可用天数、积分
            Double cardTimes=0.0;
            if(card.getConsumeTimes()!=null){
                cardTimes=card.getConsumeTimes();
            }
            if(card.getCardIntegrals()==null){
                card.setCardIntegrals(0.0);
            }
            cardTimes-=cardLockMap.get("lockConsumeTimes");

            Double cardIntegrals=0.0;

            if("1".equals(cardType.getIsIntegral())){
                //积分卡
                if(card.getCardIntegrals()!=null){
                    cardIntegrals=card.getCardIntegrals();
                }
                cardIntegrals-=cardLockMap.get("lockIntegrals");
            }
            if(totalTimes>(cardTimes+cardIntegrals)){
                renderCodeFailed("会员卡可用天数不足，扣卡失败");
                return;
            }

            if(cardIntegrals>=totalTimes){
                card.setCardIntegrals(card.getCardIntegrals()-totalTimes);
                cardTransactions.setIntegrals(totalTimes);
                cardTransactions.setIntegralsSnapshot(card.getCardIntegrals());
                cardTransactions.setTimesSnapshot(card.getConsumeTimes());
                useAmount="已扣卡"+totalTimes+"积分，";
                int usableTimes=((int)(cardTransactions.getTimesSnapshot()-cardLockMap.get("lockConsumeTimes")));
                int usableIntegrals=((int)(cardTransactions.getIntegralsSnapshot()-cardLockMap.get("lockIntegrals")));

                leftAmount+=usableTimes+"天，";
                leftAmount+=usableIntegrals+"积分，";

                goodsDescription+="共扣卡"+totalTimes+"积分";

                //计算消费明细赠送积分多少
                giveIntegrals = totalTimes;
                if(card.getGiveRechargeIntegrals() > 0){
                    card.setGiveRechargeIntegrals(card.getGiveRechargeIntegrals() - totalTimes);
                }
            }else{
                if(cardIntegrals>=1){
                    double surplusTimes=totalTimes;
                    double useTimes=0.0;
                    double useIntegrals=0.0;
                    while (cardIntegrals>=1){
                        card.setCardIntegrals(card.getCardIntegrals()-1);
                        surplusTimes-=1;
                    }
                    useTimes=surplusTimes;
                    useIntegrals=totalTimes-surplusTimes;
                    card.setConsumeTimes(card.getConsumeTimes()-surplusTimes);

                    cardTransactions.setIntegrals(useIntegrals);
                    cardTransactions.setIntegralsSnapshot(card.getCardIntegrals());
                    cardTransactions.setTimes(useTimes);
                    cardTransactions.setTimesSnapshot(card.getConsumeTimes());
                    useAmount="已扣卡"+useTimes+"天，"+useIntegrals+"积分，";
                    int usableTimes=((int)(cardTransactions.getTimesSnapshot()-cardLockMap.get("lockConsumeTimes")));
                    int usableIntegrals=((int)(cardTransactions.getIntegralsSnapshot()-cardLockMap.get("lockIntegrals")));
                    leftAmount+=usableTimes+"天，";
                    leftAmount+=usableIntegrals+"积分，";

                    goodsDescription+="已扣卡"+useTimes+"天数;"+useIntegrals+"积分";

                    //计算消费明细的购买、赠送天数分别是多少
                    if(card.getGiveRechargeDays() >= useTimes){
                        giveDays = useTimes;
                        if(card.getGiveRechargeDays() > 0){
                            card.setGiveRechargeDays(card.getGiveRechargeDays() - useTimes);
                        }
                    }else{
                        giveDays = card.getGiveRechargeDays();
                        buyDays = useTimes - card.getGiveRechargeDays();
                        //分别扣减购买天数、赠送天数
                        card.setGiveRechargeDays(0.0);
                        if(card.getBuyRechargeDays() > 0){
                            card.setBuyRechargeDays(card.getBuyRechargeDays() - buyDays);
                        }
                    }
                    //计算消费明细赠送积分多少
                    giveIntegrals = useIntegrals;
                    if(card.getGiveRechargeIntegrals() > 0){
                        card.setGiveRechargeIntegrals(card.getGiveRechargeIntegrals() - useIntegrals);
                    }
                }else{
                    card.setConsumeTimes(card.getConsumeTimes()-totalTimes);
                    cardTransactions.setTimes(totalTimes);
                    cardTransactions.setTimesSnapshot(card.getConsumeTimes());
                    cardTransactions.setIntegralsSnapshot(card.getCardIntegrals());
                    useAmount="已扣卡"+totalTimes+"天，";
                    int usableTimes=((int)(cardTransactions.getTimesSnapshot()-cardLockMap.get("lockConsumeTimes")));
                    int usableIntegrals=((int)(cardTransactions.getIntegralsSnapshot()-cardLockMap.get("lockIntegrals")));
                    leftAmount+=usableTimes+"天，";
                    leftAmount+=usableIntegrals+"积分，";
                    goodsDescription+="共扣卡"+totalTimes+"天数";

                    //计算消费明细的购买、赠送天数分别是多少
                    if(card.getGiveRechargeDays() >= totalTimes){
                        giveDays = totalTimes;
                        if(card.getGiveRechargeDays() > 0){
                            card.setGiveRechargeDays(card.getGiveRechargeDays() - totalTimes);
                        }
                    }else{
                        giveDays = card.getGiveRechargeDays();
                        buyDays = totalTimes - card.getGiveRechargeDays();
                        //分别扣减购买天数、赠送天数
                        card.setGiveRechargeDays(0.0);
                        if(card.getBuyRechargeDays() > 0){
                            card.setBuyRechargeDays(card.getBuyRechargeDays() - buyDays);
                        }
                    }
                }
            }

        }else if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
            double totalAmount=(deductScheme.getPrice()*totalTimes);
            double cardAmount=card.getBalance()-cardLockMap.get("lockConsumeTimes");
            if(cardAmount<totalAmount){
                renderCodeFailed("会员卡可用余额不足");
                return;
            }
            card.setBalance(card.getBalance()-totalAmount);
            cardTransactions.setAmount(totalAmount);
            cardTransactions.setAmountSnapshot(card.getBalance());

            useAmount="已扣卡"+totalTimes+"元，";
            int usableAmount=(int)(cardTransactions.getAmountSnapshot()-cardLockMap.get("lockConsumeTimes"));
            leftAmount=usableAmount+"元";
            goodsDescription+="共扣卡"+totalAmount+"元";

            //计算消费明细的购买、赠送金额分别是多少
            if(card.getGiveRechargeAmount() >= totalAmount){
                giveAmount = totalAmount;
                if(card.getGiveRechargeAmount() > 0){
                    card.setGiveRechargeAmount(card.getGiveRechargeAmount() - totalAmount);
                }
            }else{
                giveAmount = card.getGiveRechargeAmount();
                buyAmount = totalAmount - card.getGiveRechargeAmount();
                //分别扣减购买金额、赠送金额
                card.setGiveRechargeAmount(0.0);
                if(card.getBuyRechargeAmount() > 0){
                    card.setBuyRechargeAmount(card.getBuyRechargeAmount() - buyAmount);
                }
            }
        }

        cardTransactions.setDescribe(goodsDescription);
        finaBaseGoodOrder.setSettleAmount(cardTransactions.getAmount());
        finaBaseGoodOrder.setSettleIntegrals(cardTransactions.getIntegrals());
        finaBaseGoodOrder.setSettleTimes(cardTransactions.getTimes());

        if(giveDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(giveDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(buyDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(giveAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(buyAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveIntegrals > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("integrals");
            transactionsDetail.setDeductValue(giveIntegrals);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetail.setCreateBy(userId);
            transactionsDetailList.add(transactionsDetail);
        }

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    Db.batchSave(orderItemList,orderItemList.size());
                    //批量生成消费明细记录
                    if(transactionsDetailList!=null && transactionsDetailList.size()>0){
                        Db.batchSave(transactionsDetailList, transactionsDetailList.size());
                    }
                    return card.update()&&cardTransactions.save()&&finaBaseGoodOrder.save();
                }catch (Exception e){
                    e.printStackTrace();
                    return false;
                }
            }
        });

        if(flag){
            //发送短信
            JSONObject obj=new JSONObject();
            /*obj.put("baseName", mainBase.getBaseName());
            obj.put("cardNo",card.getCardNumber());
            if(goodNums>1){
                obj.put("goodName",goodName+"等共"+goodNums+"件");
            }else{
                obj.put("goodName",goodName);
            }

            obj.put("useAmount",useAmount);
            obj.put("leftAmount",leftAmount);*/
            //张四三卡号CS00001398消费账单：在惠州龙门温泉基地购买商品舒乐汤瑶浴（会员）已扣卡1积分，剩余可用2011天，156积分。如您对账单有异议请于三日内提出，逾期视为无异议。

            String content=member.getFullName()+"卡号"+card.getCardNumber()+"消费账单：在"+mainBase.getBaseName()+"购买商品"+goodName;

            if(orderDetailArray.size()>1){
                content+="等";
            }
            if(leftAmount.length()>0){
                leftAmount=leftAmount.substring(0,leftAmount.length()-1);
                leftAmount+="。";
            }

            Map<String, Double> lock = finaMembershipCardService.getCardLockInfo(card.getCardNumber());
            boolean isAddLockInfo=false;
            double lockTimes=lock.get("lockConsumeTimes");
            double lockAmount=lock.get("lockBalance");
            double lockIntegrals=lock.get("lockIntegrals");
            String lockStr="，已订房预扣除";
            if(lockTimes>0 || lockAmount>0 || lockIntegrals>0){
                isAddLockInfo=true;
                if(lockAmount>0){
                    double lockAmountLeftRemainder = lockAmount % 1;
                    if(lockAmountLeftRemainder==0){
                        lockStr+="金额："+((int)lockAmount)+"元";
                    }else{
                        lockStr+="金额："+lockAmount+"元";
                    }

                }else{
                    //lockStr+="天数：";
                    if(lockTimes>0 && lockIntegrals>0){
                        double lockTimesLeftRemainder = lockTimes % 1;
                        if(lockTimesLeftRemainder==0){
                            lockStr+="天数："+((int)lockTimes)+"天";
                        }else{
                            lockStr+="天数："+lockTimes+"天";
                        }

                        double lockIntegralsLeftRemainder = lockIntegrals % 1;
                        if(lockIntegralsLeftRemainder==0){
                            lockStr+="，"+((int)lockIntegrals)+"积分";
                        }else{
                            lockStr+="，"+lockIntegrals+"积分";
                        }
                    }else if(lockTimes>0){
                        double lockTimesLeftRemainder = lockTimes % 1;
                        if(lockTimesLeftRemainder==0){
                            lockStr+="天数："+((int)lockTimes)+"天";
                        }else{
                            lockStr+="天数："+lockTimes+"天";
                        }
                    }else if(lockIntegrals>0){
                        double lockIntegralsLeftRemainder = lockIntegrals % 1;
                        if(lockIntegralsLeftRemainder==0){
                            lockStr+="天数："+((int)lockIntegrals)+"积分";
                        }else{
                            lockStr+="天数："+lockIntegrals+"积分";
                        }
                    }
                }
            }
            if(!isAddLockInfo){
                lockStr="";
            }
            content+="共"+goodNums+"份"+useAmount+lockStr+"剩余可用"+leftAmount+"如您对账单有异议请于三日内提出，逾期视为无异议。";;

            obj.put("content",content);

            smsSendRecordService.sendMessage(SendType.customContent,card.getCardNumber(), JSON.toJSONString(obj),null);

            renderCodeSuccess("success");
        }else{
            renderCodeFailed("扣卡失败");
        }
    }


    public void getSojournCheckinData(){

        List<Record> recordList=Db.find("select a.checkin_no as checkinNo,a.checkin_type as checkinType,a.checkin_time as checkinDate," +
                "                IFNULL(b.checkout_time,a.checkout_time) as checkoutDate," +
                "                if(a.is_private_room='1',e.id,a.bed_id) as bedId,a.card_number as cardNumber,a.book_no as bookNo,a.`name` as memberName,a.office_id as officeId" +
                ",a.is_private_room as isPrivateRoom,a.id_card,a.telephone  " +
                "          from fina_expense_record a " +
                "          left join fina_expense_record b on b.id=(select id from fina_expense_record where a.id=parent_id and `type`='continued_residence' ORDER BY checkin_time desc limit 1) " +
                "          left join main_base_bed c on c.id=a.bed_id " +
                " left join main_base_bed e on c.room_id=e.room_id and a.is_private_room='1' and e.del_flag='0' and e.is_enable='0' " +
                " where a.base_id='27a558b2-c205-4937-b6f4-fed1fa68ac55' " +
                "          and a.del_flag='0' and a.checkin_status='stayin' and a.settle_status in ('0','1') and a.parent_id='0' " +
                " ORDER BY a.checkin_no ");

        renderCodeSuccess("success",recordList);
    }

    @Clear(LogInterceptor.class)
    public void getSojournCheckoutData(){
        String checkinNos=getPara("checkinNos");
        if(StrKit.isBlank(checkinNos)){
            renderCodeSuccess("",new ArrayList<>());
            return;
        }
        String[] checkinNoArray=checkinNos.split(",");
        if(checkinNoArray!=null){
            String str="";
            for (String checkinNo : checkinNoArray) {
                str+="?,";
            }
            str=str.substring(0,str.length()-1);
            List<Record> recordList=Db.find("select checkin_no as checkinNo,real_checkout_time as checkoutDate from fina_expense_record a where " +
                    "a.base_id='27a558b2-c205-4937-b6f4-fed1fa68ac55' and a.del_flag='0' and a.checkin_status='retreat' " +
                    "and a.settle_status in ('0','1') and a.parent_id='0' and a.checkin_no in ("+str+")",checkinNoArray);
            renderCodeSuccess("",new ArrayList<>(recordList));
            return;
        }

        renderCodeSuccess("",new ArrayList<>());
    }

    public void taskCallback() {
        String jsonStr = HttpKit.readData(getRequest());

        if (StrKit.isBlank(jsonStr) || !jsonStr.startsWith("{") || !jsonStr.endsWith("}")) {
            renderCodeFailed("参数不能为空");
            return;
        }
        logger.info("流程回调参数：" + jsonStr);
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        String taskId = jsonObject.getString("TaskId");
        String processNo=jsonObject.getString("ProcessNo");

        boolean flag=false;
        if("已完成".equals(jsonObject.getString("CurrentStep"))){
            flag=finaTaskService.taskCompleted(taskId,processNo);
        }else if("Aborted".equalsIgnoreCase(jsonObject.getString("CurrentStatus"))){
            flag=finaTaskService.taskAborted(taskId,processNo);
        }
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed();
        }
    }

    public void getGiveSchemeList(){

        List<MainCardGiveScheme> cardGiveSchemeList = mainCardGiveSchemeService.findListOnUse("1");
        renderCodeSuccess("success",cardGiveSchemeList);
    }

    public void getRechargeSchemeList(){
        List<MainCardGiveScheme> cardGiveSchemeList = mainCardGiveSchemeService.findListOnUse("2");
        renderCodeSuccess("success",cardGiveSchemeList);
    }

    public void leaseRoomPaySuccess(){
        String orderNo=getPara("orderNo");
        String userId=getPara("userId");
        if(StrKit.isBlank(orderNo) || StrKit.isBlank(userId)){
            renderCodeFailed("参数缺失");
            return;
        }
        FinaLeaseRecordRoomPayment recordRoomPayment=finaLeaseRecordRoomPaymentService.findByOrderNo(orderNo);
        if(recordRoomPayment==null){
            renderCodeFailed("获取付款单失败");
            return;
        }
        recordRoomPayment.setStatus("3");
        recordRoomPayment.setUpdateDate(new Date());
        recordRoomPayment.setUpdateBy(userId);
        if(recordRoomPayment.update()){
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void getBookOrderInfo(){
        String bookNos = getPara("bookNos");
        if(StrKit.isBlank(bookNos)){
            renderCodeFailed("参数缺失");
            return;
        }
        JSONArray jsonArray=JSON.parseArray(bookNos);
        if(jsonArray==null || jsonArray.size()==0){
            renderCodeFailed("参数数组为空");
            return;
        }

        String str="";
        List<String> bookNoList=new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            str+="?,";
            bookNoList.add(jsonArray.getString(i));
        }
        str=str.substring(0,str.length()-1);
        bookNoList.addAll(bookNoList);

        List<Record> recordList=Db.find("select a.book_no,b.id from fina_expense_record a " +
                "inner join fina_expense_record b on a.id=b.parent_id " +
                " where a.book_no in ("+str+") and a.parent_id='0' and a.del_flag='0' and a.settle_status in ('0','1') and b.del_flag='0' and b.settle_status in ('0','1') and b.`type`='follow_checkin' " +
                "UNION  " +
                "select c.book_no,c.id from fina_expense_record c  " +
                "where c.book_no in ("+str+") and c.parent_id='0' and c.del_flag='0' and c.settle_status in ('0','1')   " +
                "ORDER BY book_no ",bookNoList.toArray());

        Map<String,Record> recordMap=new HashMap<>();
        if(recordList.size()>0){
            String str2="";
            List<String> expenseIdList=new ArrayList<>();
            for (Record record : recordList) {
                str2+="?,";
                expenseIdList.add(record.getStr("id"));
            }
            str2=str2.substring(0,str2.length()-1);
            List<Record> timesRecordList=Db.find("select expense_id,card_number,sum(times) as times,sum(integrals) as integrals,sum(amount) as amount from fina_expense_record_detail where expense_id in ("+str2+")" +
                    " and del_flag='0' GROUP BY expense_id,card_number ",expenseIdList.toArray());

            for (Record expenseRecord : recordList) {
                String bookNo=expenseRecord.getStr("book_no");
                Record record=null;
                if(recordMap.containsKey(bookNo)){
                    record=recordMap.get(bookNo);
                }else{
                    record=new Record();
                    recordMap.put(bookNo,record);
                }
                for (Record timesRecord : timesRecordList) {
                    if(timesRecord.getStr("expense_id").equalsIgnoreCase(expenseRecord.getStr("id"))){
                        /*Record r=new Record();
                        r.set("times",timesRecord.get("times"));
                        r.set("integrals",timesRecord.get("integrals"));
                        r.set("amount",timesRecord.get("amount"));
                        record.set(timesRecord.getStr("card_number"),r);*/
                        if(record.getDouble("times")!=null){
                            record.set("times",timesRecord.getDouble("times")+record.getDouble("times"));
                        }else{
                            record.set("times",timesRecord.get("times"));
                        }

                        if(record.getDouble("integrals")!=null){
                            record.set("integrals",timesRecord.getDouble("integrals")+record.getDouble("integrals"));
                        }else{
                            record.set("integrals",timesRecord.get("integrals"));
                        }
                        if(record.getDouble("amount")!=null){
                            record.set("amount",timesRecord.getDouble("amount")+record.getDouble("amount"));
                        }else{
                            record.set("amount",timesRecord.get("amount"));
                        }
                    }
                }
            }

        }
        renderCodeSuccess("success",recordMap);
    }

    public void leaseRoomPaymentSuccess(){


    }

    //获取银行卡卡类型，工商、建设等
    public void getBankCardTypeList(){
        renderCodeSuccess("success",JSON.parseArray("[{\"SRCB\":\"深圳农村商业银行\"},{\"BGB\":\"广西北部湾银行\"},{\"SHRCB\":\"上海农村商业银行\"},{\"BJBANK\":\"北京银行\"},{\"WHCCB\":\"威海市商业银行\"},{\"BOZK\":\"周口银行\"},{\"KORLABANK\":\"库尔勒市商业银行\"},{\"SPABANK\":\"平安银行\"},{\"SDEB\":\"顺德农商银行\"},{\"HURCB\":\"湖北省农村信用社\"},{\"WRCB\":\"无锡农村商业银行\"},{\"BOCY\":\"朝阳银行\"},{\"CZBANK\":\"浙商银行\"},{\"HDBANK\":\"邯郸银行\"},{\"BOC\":\"中国银行\"},{\"BOD\":\"东莞银行\"},{\"CCB\":\"中国建设银行\"},{\"ZYCBANK\":\"遵义市商业银行\"},{\"SXCB\":\"绍兴银行\"},{\"GZRCU\":\"贵州省农村信用社\"},{\"ZJKCCB\":\"张家口市商业银行\"},{\"BOJZ\":\"锦州银行\"},{\"BOP\":\"平顶山银行\"},{\"HKB\":\"汉口银行\"},{\"SPDB\":\"上海浦东发展银行\"},{\"NXRCU\":\"宁夏黄河农村商业银行\"},{\"NYNB\":\"广东南粤银行\"},{\"GRCB\":\"广州农商银行\"},{\"BOSZ\":\"苏州银行\"},{\"HZCB\":\"杭州银行\"},{\"HSBK\":\"衡水银行\"},{\"HBC\":\"湖北银行\"},{\"JXBANK\":\"嘉兴银行\"},{\"HRXJB\":\"华融湘江银行\"},{\"BODD\":\"丹东银行\"},{\"AYCB\":\"安阳银行\"},{\"EGBANK\":\"恒丰银行\"},{\"CDB\":\"国家开发银行\"},{\"TCRCB\":\"江苏太仓农村商业银行\"},{\"NJCB\":\"南京银行\"},{\"ZZBANK\":\"郑州银行\"},{\"DYCB\":\"德阳商业银行\"},{\"YBCCB\":\"宜宾市商业银行\"},{\"SCRCU\":\"四川省农村信用\"},{\"KLB\":\"昆仑银行\"},{\"LSBANK\":\"莱商银行\"},{\"YDRCB\":\"尧都农商行\"},{\"CCQTGB\":\"重庆三峡银行\"},{\"FDB\":\"富滇银行\"},{\"JSRCU\":\"江苏省农村信用联合社\"},{\"JNBANK\":\"济宁银行\"},{\"CMB\":\"招商银行\"},{\"JINCHB\":\"晋城银行JCBANK\"},{\"FXCB\":\"阜新银行\"},{\"WHRCB\":\"武汉农村商业银行\"},{\"HBYCBANK\":\"湖北银行宜昌分行\"},{\"TZCB\":\"台州银行\"},{\"TACCB\":\"泰安市商业银行\"},{\"XCYH\":\"许昌银行\"},{\"CEB\":\"中国光大银行\"},{\"NXBANK\":\"宁夏银行\"},{\"HSBANK\":\"徽商银行\"},{\"JJBANK\":\"九江银行\"},{\"NHQS\":\"农信银清算中心\"},{\"MTBANK\":\"浙江民泰商业银行\"},{\"LANGFB\":\"廊坊银行\"},{\"ASCB\":\"鞍山银行\"},{\"KSRB\":\"昆山农村商业银行\"},{\"YXCCB\":\"玉溪市商业银行\"},{\"DLB\":\"大连银行\"},{\"DRCBCL\":\"东莞农村商业银行\"},{\"GCB\":\"广州银行\"},{\"NBBANK\":\"宁波银行\"},{\"BOYK\":\"营口银行\"},{\"SXRCCU\":\"陕西信合\"},{\"GLBANK\":\"桂林银行\"},{\"BOQH\":\"青海银行\"},{\"CDRCB\":\"成都农商银行\"},{\"QDCCB\":\"青岛银行\"},{\"HKBEA\":\"东亚银行\"},{\"HBHSBANK\":\"湖北银行黄石分行\"},{\"WZCB\":\"温州银行\"},{\"TRCB\":\"天津农商银行\"},{\"QLBANK\":\"齐鲁银行\"},{\"GDRCC\":\"广东省农村信用社联合社\"},{\"ZJTLCB\":\"浙江泰隆商业银行\"},{\"GZB\":\"赣州银行\"},{\"GYCB\":\"贵阳市商业银行\"},{\"CQBANK\":\"重庆银行\"},{\"DAQINGB\":\"龙江银行\"},{\"CGNB\":\"南充市商业银行\"},{\"SCCB\":\"三门峡银行\"},{\"CSRCB\":\"常熟农村商业银行\"},{\"SHBANK\":\"上海银行\"},{\"JLBANK\":\"吉林银行\"},{\"CZRCB\":\"常州农村信用联社\"},{\"BANKWF\":\"潍坊银行\"},{\"ZRCBANK\":\"张家港农村商业银行\"},{\"FJHXBC\":\"福建海峡银行\"},{\"ZJNX\":\"浙江省农村信用社联合社\"},{\"LZYH\":\"兰州银行\"},{\"JSB\":\"晋商银行\"},{\"BOHAIB\":\"渤海银行\"},{\"CZCB\":\"浙江稠州商业银行\"},{\"YQCCB\":\"阳泉银行\"},{\"SJBANK\":\"盛京银行\"},{\"XABANK\":\"西安银行\"},{\"BSB\":\"包商银行\"},{\"JSBANK\":\"江苏银行\"},{\"FSCB\":\"抚顺银行\"},{\"HNRCU\":\"河南省农村信用\"},{\"COMM\":\"交通银行\"},{\"XTB\":\"邢台银行\"},{\"CITIC\":\"中信银行\"},{\"HXBANK\":\"华夏银行\"},{\"HNRCC\":\"湖南省农村信用社\"},{\"DYCCB\":\"东营市商业银行\"},{\"ORBANK\":\"鄂尔多斯银行\"},{\"BJRCB\":\"北京农村商业银行\"},{\"XYBANK\":\"信阳银行\"},{\"ZGCCB\":\"自贡市商业银行\"},{\"CDCB\":\"成都银行\"},{\"HANABANK\":\"韩亚银行\"},{\"CMBC\":\"中国民生银行\"},{\"LYBANK\":\"洛阳银行\"},{\"GDB\":\"广东发展银行\"},{\"ZBCB\":\"齐商银行\"},{\"CBKF\":\"开封市商业银行\"},{\"H3CB\":\"内蒙古银行\"},{\"CIB\":\"兴业银行\"},{\"CRCBANK\":\"重庆农村商业银行\"},{\"SZSBK\":\"石嘴山银行\"},{\"DZBANK\":\"德州银行\"},{\"SRBANK\":\"上饶银行\"},{\"LSCCB\":\"乐山市商业银行\"},{\"JXRCU\":\"江西省农村信用\"},{\"ICBC\":\"中国工商银行\"},{\"JZBANK\":\"晋中市商业银行\"},{\"HZCCB\":\"湖州市商业银行\"},{\"NHB\":\"南海农村信用联社\"},{\"XXBANK\":\"新乡银行\"},{\"JRCB\":\"江苏江阴农村商业银行\"},{\"YNRCC\":\"云南省农村信用社\"},{\"ABC\":\"中国农业银行\"},{\"GXRCU\":\"广西省农村信用\"},{\"PSBC\":\"中国邮政储蓄银行\"},{\"BZMD\":\"驻马店银行\"},{\"ARCU\":\"安徽省农村信用社\"},{\"GSRCU\":\"甘肃省农村信用\"},{\"LYCB\":\"辽阳市商业银行\"},{\"JLRCU\":\"吉林农信\"},{\"URMQCCB\":\"乌鲁木齐市商业银行\"},{\"XLBANK\":\"中山小榄村镇银行\"},{\"CSCB\":\"长沙银行\"},{\"JHBANK\":\"金华银行\"},{\"BHB\":\"河北银行\"},{\"NBYZ\":\"鄞州银行\"},{\"LSBC\":\"临商银行\"},{\"BOCD\":\"承德银行\"},{\"SDRCU\":\"山东农信\"},{\"NCB\":\"南昌银行\"},{\"TCCB\":\"天津银行\"},{\"WJRCB\":\"吴江农商银行\"},{\"CBBQS\":\"城市商业银行资金清算中心\"},{\"HBRCU\":\"河北省农村信用社\"}]"));
    }

    @Inject
    private  FinaLeaseRecordApplyService finaLeaseRecordApplyService;

    public void genRoomPayment(){

        FinaLeaseRecordApply apply=finaLeaseRecordApplyService.findById(getPara("id"));
        boolean flag=false;
        if(apply!=null){
            flag=finaLeaseRecordApplyService.taskCompleted(apply);
        }
        if(flag){
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void getBedDeductTimes(){
        String bedId=getPara("bedId");
        if(StrKit.isBlank(bedId)){
            renderCodeFailed("参数缺失");
            return;
        }
        MainBaseBed bed = mainBaseBedService.findById(bedId);
        if(bed==null){
            renderCodeFailed("该床位不存在");
            return;
        }else if("1".equals(bed.getIsEnable())){
            renderCodeFailed("该床位为不可用状态");
            return;
        }
        MainBaseRoom mainBaseRoom = mainBaseRoomService.findById(bed.getRoomId());
        if(mainBaseRoom==null){
            renderCodeFailed("该房间不存在");
            return;
        }else if("1".equals(mainBaseRoom.getIsEnable())){
            renderCodeFailed("该房间为不可用状态");
            return;
        }
        MainBaseRoomType baseRoomType = mainBaseRoomTypeService.findById(mainBaseRoom.getBaseRoomType());

        List<Record> roomBedNumList=Db.find("select a.id,CASE  b.is_big_bed WHEN '1' THEN if(d.is_big_bed_room='1',2,1) else 1 end as bedNum  from main_base_bed a  " +
                "left join main_bed_type b on a.bed_type_id=b.id  " +
                "inner join main_base_room c on c.id=a.room_id " +
                "inner join main_base_room_type d on d.id=c.base_room_type  " +
                "where a.room_id=? and a.del_flag='0' and a.is_enable='0' ",mainBaseRoom.getId());
        Map<String,Integer> roomBedNumMap=new HashMap<>();
        int roomTotalBedNum=0;
        for(Record record:roomBedNumList){
            roomBedNumMap.put(record.getStr("id"),record.getInt("bedNum"));
            roomTotalBedNum+=record.getInt("bedNum");
        }

        double bedCost=0.0;

        //使用床位的床位数量
        int checkinBedNum=roomBedNumMap.get(bed.getId());

        int mainBaseBedCount=mainBaseBedService.queryBedCountByRoomId(mainBaseRoom.getId());

        double privateRoomDays=0.0;
        if(baseRoomType.getPrivateRoomDays()!=null && baseRoomType.getPrivateRoomDays()>0){
            privateRoomDays=baseRoomType.getPrivateRoomDays();
        }else{
            privateRoomDays=(double)finaExpenseRecordService.getBedCount(bed.getId()).get("totalBeds");
        }

        if("1".equals(baseRoomType.getIsBigBedRoom())){
            bedCost=privateRoomDays*checkinBedNum/roomTotalBedNum;
        }else{
            bedCost=privateRoomDays/mainBaseBedCount;
        }
        Record record=new Record();
        record.set("times",bedCost);
        renderCodeSuccess("success",record);
    }

    
    /**
     * 企业微信绑定豆豆券接口
     */
    public void weComCardBindRoll(){
    	
        final String cardNumber = getPara("cardNumber");//会员卡号
        final Integer sealNum = getInt("sealNum");//印章数量
        final String userId = getPara("userId");//操作人userId
        if(StrKit.isBlank(cardNumber)){
            renderCodeFailed("请输入会员卡号");
            return;
        }
        if(sealNum==null || sealNum<=0){
            renderCodeFailed("请输入正确的印章数量");
            return;
        }
        FinaMembershipCard card = finaMembershipCardService.findCardByCardNumber(cardNumber);
        if(card==null){
            renderCodeFailed(cardNumber+"会员卡不存在");
            return;
        }
        if("1".equals(card.getIsLock())){
        	renderCodeFailed(cardNumber+"会员卡已锁定");
            return;
        }
//        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
//        if(cardType==null){
//            renderCodeFailed("获取会员卡类型失败");
//            return;
//        }
//        if(!"1".equals(cardType.getIsRechargeBean())){
//            renderCodeFailed("该会员卡类型暂未开通豆豆券");
//            return;
//        }
        Record record = Db.findFirst("select * from crm_card_roll where is_enable='0' and deploy_status='0' and del_flag='0' and roll_name like concat('%',?,'%')", "2024元宵豆豆");
        if(record==null){
            renderCodeFailed("找不到豆豆券，未发布或已失效");
            return;
        }
        final int dataCount = Db.queryInt("select count(id)dataCount from fina_card_roll where card_id=? and DATE_FORMAT(create_time,'%Y-%m-%d')=?", card.getId(), DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN));
        if(dataCount>0){
        	renderCodeFailed(cardNumber+"今天已充值过");
            return;
        }
        final String rollId = record.getStr("id");
        if(crmCardRollRecordService.createBindRoll(rollId, card.getId(), sealNum, userId)){
            renderCodeSuccess("操作成功");
        }else{
            renderCodeFailed("操作失败");
        }
    }

    public void genSettleDetail(){
        String month = getPara("month");

        Date lastMonthMaxDayDate = null;
        Date lastMonthMinDayDate = null;
        try {
            lastMonthMinDayDate=DateUtils.parseDate(month+"-01","yyyy-MM-dd");
            int monthLashDay = DateUtils.getMonthLashDay(lastMonthMinDayDate);
            lastMonthMaxDayDate=DateUtils.getNextDay(DateUtils.parseDate(month+"-"+Integer.toString(monthLashDay)),1);
        }catch (Exception e){
            e.printStackTrace();
        }
        List<FinaExpenseRecord> recordList=new ArrayList<>();
        List<FinaSettleDetail> finaSettleDetailList=new ArrayList<>();
        recordList.add(finaExpenseRecordService.findById("798DBDA4-840F-4DF1-B2CD-850922C23978"));
        for(FinaExpenseRecord record:recordList){
            String totalSql="select card_number as cardNumber,sum(amount) as totalAmount,sum(times) as totalTimes,sum(points) as totalPoints,sum(integrals) as totalIntegrals" +
                    ",min(start_time) as startDate,max(end_time) as endDate from fina_expense_record_detail " +
                    "where del_flag='0' and is_settled='0' and expense_id in(select id from fina_expense_record " +
                    "where del_flag='0' and settle_status='0' and (id=? or parent_id=?)) and to_days(start_time)>=to_days(?) and to_days(end_time)<=to_days(?) group by card_number ";
            String actualSql="select card_number as cardNumber,sum(amount) as actualAmount,sum(times) as actualTimes,sum(points) as actualPoints,sum(integrals) as actualIntegrals " +
                    "from fina_expense_record_detail where  del_flag='0' and is_settled='0' and expense_id in(select id from fina_expense_record " +
                    "where del_flag='0' and settle_status='0' and (id=? or parent_id=?)) and to_days(start_time)>=to_days(?) and to_days(end_time)<=to_days(?) group by card_number ";
            List<Record> totalRecordList=Db.find(totalSql,record.getId(),record.getId(),lastMonthMinDayDate,lastMonthMaxDayDate);
            List<Record> actualRecordList=Db.find(actualSql,record.getId(),record.getId(),lastMonthMinDayDate,lastMonthMaxDayDate);
            for(Record totalRecord:totalRecordList){
                for(Record actualRecord:actualRecordList){
                    if(totalRecord.getStr("cardNumber").equals(actualRecord.getStr("cardNumber"))){
                        totalRecord.set("actualAmount",actualRecord.getDouble("actualAmount"));
                        totalRecord.set("actualTimes",actualRecord.getDouble("actualTimes"));
                        totalRecord.set("actualPoints",actualRecord.getDouble("actualPoints"));
                        totalRecord.set("actualIntegrals",actualRecord.getDouble("actualIntegrals"));
                        break;
                    }
                }
                FinaSettleDetail settleDetail=new FinaSettleDetail();
                settleDetail.setId(IdGen.getUUID());
                settleDetail.setActualAmount(totalRecord.getDouble("actualAmount"));
                settleDetail.setActualTimes(totalRecord.getDouble("actualTimes"));
                settleDetail.setActualPoints(totalRecord.getDouble("actualPoints"));
                settleDetail.setActualIntegrals(totalRecord.getDouble("actualIntegrals"));
                settleDetail.setTotalAmount(totalRecord.getDouble("totalAmount"));
                settleDetail.setTotalTimes(totalRecord.getDouble("totalTimes"));
                settleDetail.setTotalPoints(totalRecord.getDouble("totalPoints"));
                settleDetail.setTotalIntegrals(totalRecord.getDouble("totalIntegrals"));
                settleDetail.setExpenseId(record.getId());
                settleDetail.setCardNumber(totalRecord.getStr("cardNumber"));
                settleDetail.setStartTime(totalRecord.getDate("startDate"));
                settleDetail.setEndTime(totalRecord.getDate("endDate"));
                settleDetail.setDelFlag("0");
                settleDetail.setSettleType("month_settle");
                settleDetail.setSettleStatus("0");
                settleDetail.setYearMonth(DateUtils.getLastMonth());
                settleDetail.setCreateTime(new Date());
                settleDetail.setUpdateTime(new Date());
                finaSettleDetailList.add(settleDetail);
            }
        }
        if(finaSettleDetailList.size()>0) {
            Db.batchSave(finaSettleDetailList, finaSettleDetailList.size());
        }
        renderCodeSuccess("success");
    }

    public void POSDeductCard() {
        String baseId = getPara("baseId");
        String orderId = getPara("orderId");
        String cardId = getPara("cardId");
        String remark = getPara("remark");
        String list = getPara("list");
        if (StrKit.isBlank(baseId) || StrKit.isBlank(orderId) || StrKit.isBlank(cardId) ||
                StrKit.isBlank(remark) || StrKit.isBlank(list)) {
            renderCodeFailed("参数缺失");
            return;
        }
        final FinaMembershipCard card = this.finaMembershipCardService.findById(cardId);
        if (card == null || "1".equals(card.getDelFlag())) {
            renderCodeFailed("该会员卡不存在");

            return;
        }
        if ("1".equals(card.getIsLock())) {
            renderCodeFailed("该会员卡已锁定");

            return;
        }
        if ("1".equals(card.getExpireFlag())) {
            renderCodeFailed("该会员卡已过期");

            return;
        }
        MainCardDeductScheme deductScheme = this.mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if (deductScheme == null)
            deductScheme = this.mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        if (deductScheme == null) {
            renderCodeFailed("会员卡扣费方案未设置");

            return;
        }
        MmsMember member = this.mmsMemberService.findById(card.getMemberId());
        MainBase mainBase = this.mainBaseService.findById(baseId);
        Map<String, Double> cardLockMap = this.finaMembershipCardService.getCardLockInfo(card.getCardNumber());
        Date nowDate = new Date();
        double buyAmount = 0.0;
        double giveAmount = 0.0;
        double buyDays = 0.0;
        double giveDays = 0.0;
        double giveIntegrals = 0.0;
        final FinaCardTransactions cardTransactions = new FinaCardTransactions();
        cardTransactions.setId(IdGen.getUUID());
        cardTransactions.setCardId(card.getId());
        cardTransactions.setExpenseId(orderId);
        cardTransactions.setTimes(Double.valueOf(0.0D));
        cardTransactions.setAmount(Double.valueOf(0.0D));
        cardTransactions.setIntegrals(Double.valueOf(0.0D));
        cardTransactions.setPoints(Double.valueOf(0.0D));
        cardTransactions.setBeanCoupons(Double.valueOf(0.0D));
        cardTransactions.setTimesSnapshot(Double.valueOf(0.0D));
        cardTransactions.setAmountSnapshot(Double.valueOf(0.0D));
        cardTransactions.setIntegralsSnapshot(Double.valueOf(0.0D));
        cardTransactions.setPointsSnapshot(Double.valueOf(0.0D));
        cardTransactions.setBeanCouponsSnapshot(Double.valueOf(0.0D));
        cardTransactions.setStartDate(nowDate);
        cardTransactions.setEndDate(nowDate);
        cardTransactions.setInOutFlag("2");
        cardTransactions.setIsHidden("0");
        cardTransactions.setDealTime(nowDate);
        cardTransactions.setType(AccountRecordOperateType.dailyDeduction.getKey());
        JSONArray deductList = JSON.parseArray(list);
        Map<String, Double> deductMap = new HashMap<>();
        for (int i = 0; i < deductList.size(); i++) {
            JSONObject deductObj = deductList.getJSONObject(i);
            deductMap.put(deductObj.getString("type"), deductObj.getDouble("quantity"));
        }
        String useAmount = "";
        String leftAmount = "";
        if (DeductType.deductTimes.getKey().equals(deductScheme.getDeductWay())) {
            MainMembershipCardType cardType = this.mainMembershipCardTypeService.findById(card.getCardTypeId());
            Double cardTimes = Double.valueOf(0.0D);
            if (card.getConsumeTimes() != null)
                cardTimes = card.getConsumeTimes();
            cardTimes = Double.valueOf(cardTimes.doubleValue() - ((Double)cardLockMap.get("lockConsumeTimes")).doubleValue());
            Double cardIntegrals = Double.valueOf(0.0D);
            if ("1".equals(cardType.getIsIntegral())) {
                if (card.getCardIntegrals() != null)
                    cardIntegrals = card.getCardIntegrals();
                cardIntegrals = Double.valueOf(cardIntegrals.doubleValue() - ((Double)cardLockMap.get("lockIntegrals")).doubleValue());
            }
            double totalTimes = ((Double)deductMap.get("times")).doubleValue();
            double totalIntegrals = ((Double)deductMap.get("integrals")).doubleValue();
            if (totalTimes > cardTimes.doubleValue()) {
                renderCodeFailed("会员卡可用天数不足，扣卡失败");

                return;
            }
            if (totalIntegrals > cardIntegrals.doubleValue()) {
                renderCodeFailed("会员卡可用积分不足，扣卡失败");

                return;
            }
            card.setConsumeTimes(Double.valueOf(BigDecimal.valueOf(card.getConsumeTimes().doubleValue()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue()));
            //计算消费明细的购买、赠送天数分别是多少
            if(card.getGiveRechargeDays() >= totalTimes){
                giveDays = totalTimes;
                if(card.getGiveRechargeDays() > 0){
                    card.setGiveRechargeDays(Double.valueOf(BigDecimal.valueOf(card.getGiveRechargeDays().doubleValue()).subtract(BigDecimal.valueOf(totalTimes)).doubleValue()));
                }
            }else{
                giveDays = card.getGiveRechargeDays();
                buyDays = Double.valueOf(BigDecimal.valueOf(totalTimes).subtract(BigDecimal.valueOf(card.getGiveRechargeDays())).doubleValue());
                //分别扣减购买天数、赠送天数
                card.setGiveRechargeDays(0.0);
                if(card.getBuyRechargeDays() > 0){
                    card.setBuyRechargeDays(Double.valueOf(BigDecimal.valueOf(card.getBuyRechargeDays().doubleValue()).subtract(BigDecimal.valueOf(buyDays)).doubleValue()));
                }
            }
            card.setCardIntegrals(Double.valueOf(BigDecimal.valueOf(card.getCardIntegrals().doubleValue()).subtract(BigDecimal.valueOf(totalIntegrals)).doubleValue()));
            //计算消费明细赠送积分多少
            giveIntegrals = totalIntegrals;
            if(card.getGiveRechargeIntegrals() > 0){
                card.setGiveRechargeIntegrals(Double.valueOf(BigDecimal.valueOf(card.getGiveRechargeIntegrals().doubleValue()).subtract(BigDecimal.valueOf(totalIntegrals)).doubleValue()));
            }

            cardTransactions.setIntegrals(Double.valueOf(totalIntegrals));
            cardTransactions.setIntegralsSnapshot(card.getCardIntegrals());
            cardTransactions.setTimes(Double.valueOf(totalTimes));
            cardTransactions.setTimesSnapshot(card.getConsumeTimes());
            useAmount = "已扣卡" + totalTimes + "天，" + totalIntegrals + "积分，";
            int usableTimes = (int)(cardTransactions.getTimesSnapshot().doubleValue() - ((Double)cardLockMap.get("lockConsumeTimes")).doubleValue());
            int usableIntegrals = (int)(cardTransactions.getIntegralsSnapshot().doubleValue() - ((Double)cardLockMap.get("lockIntegrals")).doubleValue());
            leftAmount =+usableTimes + "天，";
            if("1".equals(cardType.getIsIntegral())){
                leftAmount+=usableIntegrals + "积分，";
            }
        } else if (DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())) {
            double totalAmount = ((Double)deductMap.get("amount")).doubleValue();
            double cardAmount = card.getBalance().doubleValue() - ((Double)cardLockMap.get("lockBalance")).doubleValue();
            if (cardAmount < totalAmount) {
                renderCodeFailed("会员卡可用余额不足");
                return;
            }
            card.setBalance(Double.valueOf(card.getBalance().doubleValue() - totalAmount));
            //计算消费明细的购买、赠送金额分别是多少
            if(card.getGiveRechargeAmount() >= totalAmount){
                giveAmount = totalAmount;
                if(card.getGiveRechargeAmount() > 0){
                    card.setGiveRechargeAmount(Double.valueOf(card.getGiveRechargeAmount().doubleValue() - totalAmount));
                }
            }else{
                giveAmount = card.getGiveRechargeAmount();
                buyAmount = Double.valueOf(totalAmount - card.getGiveRechargeAmount().doubleValue());
                //分别扣减购买金额、赠送金额
                card.setGiveRechargeAmount(0.0);
                if(card.getBuyRechargeAmount() > 0){
                    card.setBuyRechargeAmount(Double.valueOf(card.getBuyRechargeAmount().doubleValue() - buyAmount));
                }
            }
            cardTransactions.setAmount(Double.valueOf(totalAmount));
            cardTransactions.setAmountSnapshot(card.getBalance());
            useAmount = "已扣卡" + totalAmount + "元，";
            int usableAmount = (int)(cardTransactions.getAmountSnapshot().doubleValue() - ((Double)cardLockMap.get("lockBalance")).doubleValue());
            leftAmount = usableAmount + "元";

        }
        cardTransactions.setDescribe(remark);

        //生成消费购买、赠送明细列表记录
        List<FinaCardTransactionsDetail> transactionsDetailList = new ArrayList<>();
        if(giveDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(giveDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyDays > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("days");
            transactionsDetail.setDeductValue(buyDays);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(giveAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetailList.add(transactionsDetail);
        }
        if(buyAmount > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("buy");
            transactionsDetail.setSettleType("amount");
            transactionsDetail.setDeductValue(buyAmount);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetailList.add(transactionsDetail);
        }
        if(giveIntegrals > 0){
            FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
            transactionsDetail.setId(IdGen.getUUID());
            transactionsDetail.setTransactionsId(cardTransactions.getId());
            transactionsDetail.setDeductType("give");
            transactionsDetail.setSettleType("integrals");
            transactionsDetail.setDeductValue(giveIntegrals);
            transactionsDetail.setDelFlag(DelFlag.NORMAL);
            transactionsDetail.setCreateTime(new Date());
            transactionsDetailList.add(transactionsDetail);
        }

        boolean flag = Db.tx(new IAtom() {
            public boolean run() throws SQLException {
                try {
                    if(!card.update()){
                        return false;
                    }
                    if(!cardTransactions.save()){
                        return false;
                    }else{
                        //批量生成消费明细记录
                        if(transactionsDetailList!=null && transactionsDetailList.size()>0){
                            Db.batchSave(transactionsDetailList,transactionsDetailList.size());
                        }
                    }
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                    return false;
                }
            }
        });
        if (flag) {
            JSONObject obj = new JSONObject();
            String content = member.getFullName() + "卡号" + card.getCardNumber() + "消费账单：在" + mainBase.getBaseName() + "购买商品";

            Map<String, Double> lock = finaMembershipCardService.getCardLockInfo(card.getCardNumber());
            boolean isAddLockInfo=false;
            double lockTimes=lock.get("lockConsumeTimes");
            double lockAmount=lock.get("lockBalance");
            double lockIntegrals=lock.get("lockIntegrals");
            String lockStr="，已订房预扣除";
            if(lockTimes>0 || lockAmount>0 || lockIntegrals>0){
                isAddLockInfo=true;
                if(lockAmount>0){
                    double lockAmountLeftRemainder = lockAmount % 1;
                    if(lockAmountLeftRemainder==0){
                        lockStr+="金额："+((int)lockAmount)+"元";
                    }else{
                        lockStr+="金额："+lockAmount+"元";
                    }

                }else{
                    //lockStr+="天数：";
                    if(lockTimes>0 && lockIntegrals>0){
                        double lockTimesLeftRemainder = lockTimes % 1;
                        if(lockTimesLeftRemainder==0){
                            lockStr+="天数："+((int)lockTimes)+"天";
                        }else{
                            lockStr+="天数："+lockTimes+"天";
                        }

                        double lockIntegralsLeftRemainder = lockIntegrals % 1;
                        if(lockIntegralsLeftRemainder==0){
                            lockStr+="，"+((int)lockIntegrals)+"积分";
                        }else{
                            lockStr+="，"+lockIntegrals+"积分";
                        }
                    }else if(lockTimes>0){
                        double lockTimesLeftRemainder = lockTimes % 1;
                        if(lockTimesLeftRemainder==0){
                            lockStr+="天数："+((int)lockTimes)+"天";
                        }else{
                            lockStr+="天数："+lockTimes+"天";
                        }
                    }else if(lockIntegrals>0){
                        double lockIntegralsLeftRemainder = lockIntegrals % 1;
                        if(lockIntegralsLeftRemainder==0){
                            lockStr+="天数："+((int)lockIntegrals)+"积分";
                        }else{
                            lockStr+="天数："+lockIntegrals+"积分";
                        }
                    }
                }
            }
            if(!isAddLockInfo){
                lockStr="";
            }
            content+=useAmount+lockStr+"剩余可用"+leftAmount+"如您对账单有异议请于三日内提出，逾期视为无异议。";;

            obj.put("content",content);

            smsSendRecordService.sendMessage(SendType.customContent,card.getCardNumber(), JSON.toJSONString(obj),null);

            renderCodeSuccess("success");
        } else {
            renderCodeFailed("扣卡失败");
        }
    }

    public void cancelPOSDeductCard() {
        String orderId = getPara("orderId");
    }

    //世纪昌松相关卡类别的会员卡积分转天数
//    public void changeSJCSIntegralsToTimes(){
//        final String sql = "select c.id,c.card_number,c.balance,c.consume_times,c.card_integrals,c.`describe` from fina_membership_card c " +
//        "left join main_membership_card_type t on t.id=c.card_type_id " +
//        "where t.card_type in ('大理世纪昌松1万租赁床位赠送卡','大理世纪昌松3万租赁床位赠送卡','大理世纪昌松5万租赁床位赠送卡'," +
//        "'大理世纪昌松10万租赁床位赠送卡','大理世纪昌松1万赠送卡','大理世纪昌松2万赠送卡','大理世纪昌松3万赠送卡')";
//        List<Record> cardList = Db.find(sql);
//        if(cardList!=null && cardList.size()>0){
//            for(Record card : cardList){
//                final String cardId = card.getStr("id");
//                final String cardNumber = card.getStr("card_number");
//                final Double cardIntegrals = card.getDouble("card_integrals");
//                //查询是否有锁定未结算记录
//                List<Record> recordList = Db.find("select * from fina_expense_record_detail where del_flag='0' and is_settled='0' and card_number=?", cardNumber);
//                if(recordList!=null && recordList.size()>0){
//                    for(Record record : recordList){
//                        final String recordId = record.getStr("id");
//                        final Double times = record.getDouble("times")!=null?record.getDouble("times"):0.0;
//                        final Double recordIntegrals = record.getDouble("integrals");
//                        //更新记录
//                        FinaExpenseRecordDetail recordDetail = new FinaExpenseRecordDetail();
//                        recordDetail.setId(recordId);
//                        if(recordIntegrals!=null && recordIntegrals>0) {
//                            recordDetail.setTimes(times+recordIntegrals);
//                            recordDetail.setIntegrals(0.0);
//                        }
//                        recordDetail.update();
//                    }
//                }
//                //查询是否有月结算、退住结算未结算记录
//                List<Record> detailList = Db.find("select * from fina_settle_detail where del_flag='0' and settle_status='0' and card_number=?", cardNumber);
//                if(detailList!=null && detailList.size()>0){
//                    for(Record record : detailList){
//                        final String detailId = record.getStr("id");
//                        final Double totalTimes = record.getDouble("total_times")!=null?record.getDouble("total_times"):0.0;
//                        final Double totalIntegrals = record.getDouble("total_integrals");
//                        final Double actualTimes = record.getDouble("actual_times")!=null?record.getDouble("actual_times"):0.0;
//                        final Double actualIntegrals = record.getDouble("actual_integrals");
//                        //更新记录
//                        FinaSettleDetail detail = new FinaSettleDetail();
//                        detail.setId(detailId);
//                        if(totalIntegrals!=null && totalIntegrals>0){
//                            detail.setTotalTimes(totalTimes+totalIntegrals);
//                            detail.setTotalIntegrals(0.0);
//                        }
//                        if(actualIntegrals!=null && actualIntegrals>0){
//                            detail.setActualTimes(actualTimes+actualIntegrals);
//                            detail.setActualIntegrals(0.0);
//                        }
//                        detail.update();
//                    }
//                }
//                //判断会员卡积分是否大于0，大于0才执行生成和更新操作
//                if(cardIntegrals!=null && cardIntegrals>0){
//                    Date nowDate = new Date();
//                    //生成积分消耗的消费记录
//                    boolean flag = finaCardTransactionsService.saveCardTransactions(
//                        ""
//                        , cardId
//                        , ConsumeType.TRANSFER
//                        , "daily_deduction"
//                        , "2"
//                        , 0.0
//                        , 0.0
//                        , 0.0
//                        , cardIntegrals
//                        , 0.0
//                        , cardNumber + "积分转天数，消费积分："+cardIntegrals
//                        , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 00:00:00")
//                        , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 23:59:59")
//                        , nowDate
//                        , new FinaMembershipCard()
//                    );
//                    if(flag){
//                        //生成充值记录
//                        FinaCardRecharge recharge = new FinaCardRecharge();
//                        recharge.setId(IdGen.getUUID());
//                        recharge.setCardId(cardId);
//                        recharge.setRechargeTime(DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 00:00:00"));
//                        recharge.setType(RechargeType.RECHARGE_DAYS);
//                        recharge.setConsumeTimes(cardIntegrals);//充值获得的天数
//                        recharge.setAmount(0.0);
//                        recharge.setAverageDays(0.0);
//                        recharge.setCountPrice(0.0);
//                        recharge.setCountDays(0.0);
//                        recharge.setIsIncome("0");
//                        recharge.setClassify("give_recharge");
//                        recharge.setPayAmount(0.0);
//                        recharge.setConsumePoints(0.0);//充值获得的点数
//                        recharge.setConsumeIntegral(0.0);//充值获得的积分
//                        recharge.setConsumeBeanCoupons(0.0);//充值获得的豆豆券
//                        recharge.setIsCash("0");
//                        recharge.setCashValue(0.0);
//                        recharge.setAccountValue(0.0);
//                        recharge.setPrice(0.0);
//                        recharge.setGiveAmount(0.0);
//                        recharge.setGiveConsumeTimes(0.0);
//                        recharge.setGiveConsumePoints(0.0);
//                        recharge.setGiveConsumeIntegral(0.0);
//                        recharge.setGiveBeanCoupons(0.0);
//                        recharge.setDescribe(cardNumber + "积分转天数，充值天数："+cardIntegrals);
//                        recharge.setCancelFlag("0");
//                        recharge.setRechargeWay("single");
//                        recharge.setIsReview("1");
//                        recharge.setIsCount("1");
//                        recharge.setDelFlag(DelFlag.NORMAL);
//                        recharge.setCreateTime(new Date());
//                        recharge.setUpdateTime(new Date());
//                        flag = recharge.save();
//                        if(flag){
//                            //创建全员卡对象来生成消费记录并在创建后更新
//                            FinaMembershipCard memberCard = new FinaMembershipCard();
//                            memberCard.setId(cardId);
//                            memberCard.setConsumeTimes(cardIntegrals);
//                            memberCard.setCardIntegrals(0.0);
//                            //生成充值记录的消费记录
//                            flag = finaCardTransactionsService.saveCardTransactions(
//                                 recharge.getId()
//                                ,cardId
//                                ,""
//                                ,"recharge_prestore"
//                                ,"1"
//                                ,0.0
//                                ,recharge.getConsumeTimes()
//                                ,0.0
//                                ,0.0
//                                ,0.0
//                                ,cardNumber + "积分转天数，充值天数："+recharge.getConsumeTimes()
//                                , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 00:00:00")
//                                , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 23:59:59")
//                                ,DateUtils.addSeconds(nowDate, 2)
//                                ,memberCard
//                            );
//                            if(flag){
//                                memberCard.update();
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        renderCodeSuccess("success");
//    }

    //员工福利卡类别的会员卡积分转天数
//    public void changeYGFLIntegralsToTimes(){
//        final String sql = "select c.id,c.card_number,c.balance,c.consume_times,c.card_integrals,c.`describe` from fina_membership_card c " +
//        "left join main_membership_card_type t on t.id=c.card_type_id " +
//        "where t.card_type in ('员工福利积分卡')";
//        List<Record> cardList = Db.find(sql);
//        if(cardList!=null && cardList.size()>0){
//            for(Record card : cardList){
//                final String cardId = card.getStr("id");
//                final String cardNumber = card.getStr("card_number");
//                final Double cardIntegrals = card.getDouble("card_integrals");
//                //查询是否有锁定未结算记录
//                List<Record> recordList = Db.find("select * from fina_expense_record_detail where del_flag='0' and is_settled='0' and card_number=?", cardNumber);
//                if(recordList!=null && recordList.size()>0){
//                    for(Record record : recordList){
//                        final String recordId = record.getStr("id");
//                        final Double times = record.getDouble("times")!=null?record.getDouble("times"):0.0;
//                        final Double recordIntegrals = record.getDouble("integrals");
//                        //更新记录
//                        FinaExpenseRecordDetail recordDetail = new FinaExpenseRecordDetail();
//                        recordDetail.setId(recordId);
//                        if(recordIntegrals!=null && recordIntegrals>0) {
//                            recordDetail.setTimes(times+recordIntegrals);
//                            recordDetail.setIntegrals(0.0);
//                        }
//                        recordDetail.update();
//                    }
//                }
//                //查询是否有月结算、退住结算未结算记录
//                List<Record> detailList = Db.find("select * from fina_settle_detail where del_flag='0' and settle_status='0' and card_number=?", cardNumber);
//                if(detailList!=null && detailList.size()>0){
//                    for(Record record : detailList){
//                        final String detailId = record.getStr("id");
//                        final Double totalTimes = record.getDouble("total_times")!=null?record.getDouble("total_times"):0.0;
//                        final Double totalIntegrals = record.getDouble("total_integrals");
//                        final Double actualTimes = record.getDouble("actual_times")!=null?record.getDouble("actual_times"):0.0;
//                        final Double actualIntegrals = record.getDouble("actual_integrals");
//                        //更新记录
//                        FinaSettleDetail detail = new FinaSettleDetail();
//                        detail.setId(detailId);
//                        if(totalIntegrals!=null && totalIntegrals>0){
//                            detail.setTotalTimes(totalTimes+totalIntegrals);
//                            detail.setTotalIntegrals(0.0);
//                        }
//                        if(actualIntegrals!=null && actualIntegrals>0){
//                            detail.setActualTimes(actualTimes+actualIntegrals);
//                            detail.setActualIntegrals(0.0);
//                        }
//                        detail.update();
//                    }
//                }
//                //判断会员卡积分是否大于0，大于0才执行生成和更新操作
//                if(cardIntegrals!=null && cardIntegrals>0){
//                    Date nowDate = new Date();
//                    //生成积分消耗的消费记录
//                    final String tranId = IdGen.getUUID();
//                    boolean flag = finaCardTransactionsService.saveCardTransactions(
//                         tranId
//                        ,""
//                        , cardId
//                        , ConsumeType.TRANSFER
//                        , "daily_deduction"
//                        , "2"
//                        , 0.0
//                        , 0.0
//                        , 0.0
//                        , cardIntegrals
//                        , 0.0
//                        , cardNumber + "积分转天数，消费积分："+cardIntegrals
//                        , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 00:00:00")
//                        , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 23:59:59")
//                        , nowDate
//                        , new FinaMembershipCard()
//                    );
//                    if(flag){
//                        //生成消费记录明细
//                        FinaCardTransactionsDetail transactionsDetail = new FinaCardTransactionsDetail();
//                        transactionsDetail.setId(IdGen.getUUID());
//                        transactionsDetail.setTransactionsId(tranId);
//                        transactionsDetail.setDeductType("give");
//                        transactionsDetail.setSettleType("integrals");
//                        transactionsDetail.setDeductValue(cardIntegrals);
//                        transactionsDetail.setDelFlag(DelFlag.NORMAL);
//                        transactionsDetail.setCreateTime(new Date());
//                        transactionsDetail.setCreateBy("1");
//                        flag = transactionsDetail.save();
//                        if(flag){
//                            //把积分四舍五入作为新充值的天数
//                            final double rechargeDays = (double) Math.round(cardIntegrals);
//                            //生成充值记录
//                            FinaCardRecharge recharge = new FinaCardRecharge();
//                            recharge.setId(IdGen.getUUID());
//                            recharge.setCardId(cardId);
//                            recharge.setRechargeTime(DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 00:00:00"));
//                            recharge.setType(RechargeType.RECHARGE_DAYS);
//                            recharge.setConsumeTimes(rechargeDays);//充值获得的天数
//                            recharge.setAmount(0.0);
//                            recharge.setAverageDays(0.0);
//                            recharge.setCountPrice(0.0);
//                            recharge.setCountDays(0.0);
//                            recharge.setIsIncome("0");
//                            recharge.setClassify("give_recharge");
//                            recharge.setPayAmount(0.0);
//                            recharge.setConsumePoints(0.0);//充值获得的点数
//                            recharge.setConsumeIntegral(0.0);//充值获得的积分
//                            recharge.setConsumeBeanCoupons(0.0);//充值获得的豆豆券
//                            recharge.setIsCash("0");
//                            recharge.setCashValue(0.0);
//                            recharge.setAccountValue(0.0);
//                            recharge.setPrice(0.0);
//                            recharge.setGiveAmount(0.0);
//                            recharge.setGiveConsumeTimes(0.0);
//                            recharge.setGiveConsumePoints(0.0);
//                            recharge.setGiveConsumeIntegral(0.0);
//                            recharge.setGiveBeanCoupons(0.0);
//                            recharge.setDescribe(cardNumber + "积分转天数，充值天数："+rechargeDays);
//                            recharge.setCancelFlag("0");
//                            recharge.setRechargeWay("single");
//                            recharge.setIsReview("1");
//                            recharge.setIsCount("1");
//                            recharge.setDelFlag(DelFlag.NORMAL);
//                            recharge.setCreateTime(new Date());
//                            recharge.setUpdateTime(new Date());
//                            flag = recharge.save();
//                            if(flag){
//                                //创建全员卡对象来生成消费记录并在创建后更新
//                                FinaMembershipCard oldCard = finaMembershipCardService.get(cardId);
//                                oldCard.setConsumeTimes(oldCard.getConsumeTimes()+recharge.getConsumeTimes());
//                                oldCard.setCardIntegrals(0.0);
//                                oldCard.setGiveRechargeDays(oldCard.getGiveRechargeDays()+recharge.getConsumeTimes());
//                                //生成充值记录的消费记录
//                                flag = finaCardTransactionsService.saveCardTransactions(
//                                        IdGen.getUUID()
//                                        ,recharge.getId()
//                                        ,cardId
//                                        ,""
//                                        ,"recharge_prestore"
//                                        ,"1"
//                                        ,0.0
//                                        ,recharge.getConsumeTimes()
//                                        ,0.0
//                                        ,0.0
//                                        ,0.0
//                                        ,cardNumber + "积分转天数，充值天数："+recharge.getConsumeTimes()
//                                        , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 00:00:00")
//                                        , DateUtils.parseDate(DateUtils.formatDate(nowDate,"yyyy-MM-dd") + " 23:59:59")
//                                        ,DateUtils.addSeconds(nowDate, 2)
//                                        ,oldCard
//                                );
//                                if(flag){
//                                    oldCard.update();
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        renderCodeSuccess("success");
//    }

    /**
     * 压缩人脸图片并生成人脸记录数据
     */
//    public void syncFaceRecord() throws IOException {
//        InputStream inputStream = null;
//        FileOutputStream outputStream = null;
//        try {
//            String[] fields = {"full_name", "idcard","idcard_type","file_url","create_time"};
//            File srcFile = new File("D:\\faceData.xls");
//            InputStream input = new FileInputStream(srcFile);
//            List<Map> excelList = ImportExcelKit.getExcelData(input,"faceData.xls",fields);
//            System.out.println("excelList.size======"+excelList.size());
//            for(Map<String, Object> item : excelList){
//                final String fullName = item.get("full_name")!=null?item.get("full_name").toString():"";
//                final String idcard = item.get("idcard")!=null?item.get("idcard").toString():"";
//                final String idcardType = item.get("idcard_type")!=null?item.get("idcard_type").toString():"";
//                final String fileUrl = item.get("file_url")!=null?item.get("file_url").toString():"";
//                final String fileSuffix = fileUrl.substring(fileUrl.lastIndexOf("."));
//                final String createTime = item.get("create_time")!=null?item.get("create_time").toString():"";
//
//                System.out.println("fullName="+fullName);
//                System.out.println("idcard="+idcard);
//                System.out.println("idcardType="+idcardType);
//                System.out.println("fileUrl="+fileUrl);
//                System.out.println("fileSuffix="+fileSuffix);
//                System.out.println("createTime="+createTime);
//                if(StrKit.notBlank(fileUrl)){
//                    MmsCustomerFace customerFace = mmsCustomerFaceService.findByTypeAndIdcard("face", idcard);
//                    if(customerFace==null){
//                        final String picName = UUID.randomUUID().toString().replace("-", "") +".jpg";
//                        final String picUrl = "http://oss.cncsgroup.com//faceImage/" + picName;
//                        System.out.println("picName="+picName);
//                        System.out.println("picUrl="+picUrl);
//
//                        MmsCustomerFace faceModel = new MmsCustomerFace();
//                        faceModel.setId(IdGen.getUUID());
//                        faceModel.setCustomerName(fullName);
//                        faceModel.setIdcardType("大陆居民身份证");
//                        faceModel.setIdcard(idcard);
//                        faceModel.setPicType("face");
//                        faceModel.setPicUrl(picUrl);
//                        faceModel.setDataSource("BuyCard");
//                        faceModel.setDelFlag(DelFlag.NORMAL);
//                        faceModel.setCreateBy("1");
//                        faceModel.setCreateTime(DateUtils.parseDate(createTime, "yyyy-MM-dd HH:mm:ss"));
//                        faceModel.setUpdateTime(DateUtils.parseDate(createTime, "yyyy-MM-dd HH:mm:ss"));
//                        if(faceModel.save()){
//                            URL url = new URL(fileUrl);
//                            URLConnection conn = url.openConnection();
//                            inputStream = conn.getInputStream();
//                            outputStream = new FileOutputStream("D:\\upload\\faceImage\\"+ picName);
//                            outputStream.write(PicUtils.compressPicForScale(PicUtils.compressOfQuality(inputStream, 1), 100));
//                        }
//                    }
//
//                }
//            }
//        }catch (Exception e){
//            e.printStackTrace();
//        }finally {
//            if(inputStream!=null){
//                inputStream.close();
//            }
//            if(outputStream!=null){
//                outputStream.close();
//            }
//        }
//        renderCodeSuccess("success");
//    }

    /**
     * 校验人脸图片数据
     */
//    public void checkFaceRecord(){
//        List<MmsCustomerFace> customerFaceList = mmsCustomerFaceService.findList(null, null, null);
//        if(customerFaceList!=null && customerFaceList.size()>0){
//            for(MmsCustomerFace mmsCustomerFace : customerFaceList){
//                final String customerName = mmsCustomerFace.getCustomerName();
//                final String idcard = mmsCustomerFace.getIdcard();
//                final String picUrl = mmsCustomerFace.getPicUrl();
//                //调用校验接口
//                JSONObject params = new JSONObject();
//                params.put("name", customerName);
//                params.put("idCardNo", idcard);
//                params.put("faceImgUrl", picUrl);
//                System.out.println("Global.guestFaceUrl==="+Global.guestFaceUrl);
//                System.out.println("params.toJSONString==="+params.toJSONString());
//                final String resultStr = HttpClientsUtils.httpPostRaw("http://10.10.0.18:1501/Service/?method=GuestFace",params.toJSONString(),null,null);
//                System.out.println("resultStr==="+resultStr);
//                JSONObject returnJson = JSONObject.parseObject(resultStr);
//                final int returnType = returnJson.getIntValue("Type");
//                final String returnMsg = returnJson.getString("Msg");
//                System.out.println("校验返回returnType==="+returnType);
//                System.out.println("校验返回returnMsg==="+returnMsg);
//                if(returnType!=1){
//                    MmsCustomerFace customerFace = new MmsCustomerFace();
//                    customerFace.setId(mmsCustomerFace.getId());
//                    customerFace.setDelFlag("1");
//                    customerFace.setUpdateBy("1");
//                    customerFace.setUpdateTime(new Date());
//                    boolean flag = customerFace.update();
//                    System.out.println(customerName+"-"+idcard+"flag==="+flag);
//                }
//            }
//        }
//        renderCodeSuccess("success");
//    }

    @Clear(LogInterceptor.class)
    public void test2(){
        /*String rawData = getRawData();
        JSONObject jsonObject=JSON.parseObject(rawData);
        String cardNumber=jsonObject.getString("cardNumber");
        JSONArray jsonArray=jsonObject.getJSONArray("expenseIds");
        int pageNumber = jsonObject.getIntValue("pageNumber");
        int pageSize=jsonObject.getInteger("pageSize");*/
        String cardNumber=getPara("cardNumber");


        String expenseSql="select expense_id,SUM(times) as totalTimes,SUM(amount) as totalAmount,SUM(integrals) as totalIntegrals from fina_expense_record_detail where del_flag='0' and is_settled='0' and card_number=? GROUP BY expense_id ";
        List<Record> expenseIdRecord=Db.find(expenseSql,cardNumber);
        if(expenseIdRecord.size()==0){
            renderCodeSuccess("success",new ArrayList<>());
            return;
        }
        Map<String,Record> recordMap=new HashMap<>();
        for (Record r:expenseIdRecord) {
            Record record=new Record();
            record.set("expenseId",r.getStr("expense_id"));
            record.set("totalAmount",r.getDouble("totalAmount"));
            record.set("totalTimes",r.getDouble("totalTimes"));
            record.set("totalIntegrals",r.getDouble("totalIntegrals"));
            recordMap.put(record.getStr("expenseId"),record);
        }


        List<String> expenseIds=new ArrayList<>();
        for (int i = 0; i < expenseIdRecord.size(); i++) {
            expenseIds.add(expenseIdRecord.get(i).getStr("expense_id"));
        }


        String str="";
        for(String id:expenseIds){
            str+="?,";
        }
        //mainIds.addAll(mainIds);
        List<String> newMainIds = new ArrayList<>();
        newMainIds.addAll(expenseIds);
        newMainIds.addAll(expenseIds);
        newMainIds.addAll(expenseIds);
        str=str.substring(0,str.length()-1);
        String select="select * " ;

        String sql=" from ( "+
                "select a.id,a.book_start_time as bookStartTime,a.book_end_time as bookEndTime,a.checkin_time as checkinTime,a.checkout_time as checkoutTime,a.parent_id as parentId,b.base_name as baseName,a.category,a.tourist_no as touristNo,a.book_no as bookNo, a.checkin_no as checkinNo,a.name as checkinName,c.bed_name as bedName,"
                + "a.is_private_room as isPrivateRoom,a.checkin_status as checkinStatus from fina_expense_record a "
                + "left join main_base b on b.id=a.base_id "
                + "left join main_base_bed c on c.id=a.bed_id where a.id in("+str+" ) and a.del_flag='0' "
                + "UNION "
                + "select d.id,a.checkin_time as bookStartTime,a.checkout_time as bookEndTime,a.checkin_time as checkinTime,a.checkout_time as checkoutTime,'0' as parentId,b.base_name as baseName,a.category,a.tourist_no as touristNo,a.book_no as bookNo,a.checkin_no as checkinNo,a.name as checkinName,c.bed_name as bedName,"
                + "a.is_private_room as isPrivateRoom,a.checkin_status as checkinStatus from fina_expense_record_tourist_settle_detail d "
                + "left join fina_expense_record a on d.expense_id=a.id "
                + "left join main_base b on b.id=a.base_id "
                + "left join main_base_bed c on c.id=a.bed_id where d.id in("+str+" ) and d.del_flag='0' "
                + "UNION "
                + "select p.id,p.plan_checkin_time as bookStartTime,p.actual_checkin_time as bookEndTime,p.plan_checkin_time as checkinTime,p.plan_checkout_time as checkoutTime,'0' as parentId,b.base_name as baseName,'punish' as category,'' as touristNo,p.book_no as bookNo,p.checkin_no as checkinNo,p.name as checkinName,'' as bedName,"
                + "'' as isPrivateRoom,p.punish_type as checkinStatus from fina_punish_record p "
                + "left join main_base b on b.id=p.base_id where p.id in("+str+") and p.del_flag='0' "
                + ") t ";

        List<Record> recordPage=Db.find(select+sql,newMainIds.toArray());
        Map<String,Record> mainRecordMap=new HashMap<>();
        for(Record record:recordPage){
            if("0".equals(record.getStr("parentId"))){
                Record lockRecord = recordMap.get(record.getStr("id"));

                record.set("lockAmount",lockRecord.getDouble("totalAmount"));
                record.set("lockTimes",lockRecord.getDouble("totalTimes"));
                record.set("lockPoints",0.0);
                record.set("lockIntegrals",lockRecord.getDouble("totalIntegrals"));
                mainRecordMap.put(record.getStr("id"),record);
            }
        }
        List<Record> returnList=new ArrayList<>();
        for(Record record:recordPage){
            //旅游团不为空或者类型为违约
            if(StrKit.notBlank(record.getStr("touristNo")) || "punish".equals(record.getStr("category"))){
                Record lockRecord=Db.findFirst(" select sum(amount) as lockAmount,sum(times) as lockTimes,sum(points) as lockPoints,sum(integrals) as lockIntegrals "
                        + "from fina_expense_record_detail where expense_id =? and del_flag='0' and is_settled='0' and card_number=? ", record.getStr("id"), cardNumber);
                record.set("lockAmount",lockRecord.getDouble("lockAmount"));
                record.set("lockTimes",lockRecord.getDouble("lockTimes"));
                record.set("lockPoints",lockRecord.getDouble("lockPoints"));
                record.set("lockIntegrals",lockRecord.getDouble("lockIntegrals"));
                returnList.add(record);
            }else{
                /*Record lockRecord=Db.findFirst(" select sum(amount) as lockAmount,sum(times) as lockTimes,sum(points) as lockPoints,sum(integrals) as lockIntegrals "
                        + "from fina_expense_record_detail where expense_id in (select id from fina_expense_record where id=?  and del_flag='0' UNION " +
                        " select id from fina_expense_record where parent_id=? and del_flag='0') and del_flag='0' "
                        + "and is_settled='0' and card_number=? ",record.getStr("id"),record.getStr("id"),cardNumber);
                record.set("lockAmount",lockRecord.getDouble("lockAmount"));
                record.set("lockTimes",lockRecord.getDouble("lockTimes"));
                record.set("lockPoints",lockRecord.getDouble("lockPoints"));
                record.set("lockIntegrals",lockRecord.getDouble("lockIntegrals"));

                String followNames=Db.queryStr("select GROUP_CONCAT(`name` SEPARATOR ',') from ( " +
                        "select `name` from fina_expense_record_follow where expense_id in ( " +
                        "select id from fina_expense_record where parent_id=? and `type`='follow_checkin' and del_flag='0' and settle_status in ('0','1') " +
                        ") " +
                        "GROUP BY `name` " +
                        ") t " +
                        "GROUP BY `name` ",record.getStr("id"));
                if(StrKit.isBlank(followNames)){
                    followNames="";
                }else{
                    followNames=","+followNames;
                }
                record.set("checkinName",record.get("checkinName")+followNames);*/
                if(!"0".equals(record.getStr("parentId"))){
                    Record mainRecord=mainRecordMap.get(record.getStr("parentId"));
                    if(mainRecord==null){
                        mainRecord=new Record();
                        mainRecord.setColumns(record.getColumns());
                        mainRecord.set("id",record.getStr("parentId"));
                        mainRecord.set("parentId","0");
                        mainRecord.set("lockAmount",0.0);
                        mainRecord.set("lockTimes",0.0);
                        mainRecord.set("lockPoints",0.0);
                        mainRecord.set("lockIntegrals",0.0);
                        mainRecordMap.put(mainRecord.getStr("id"),mainRecord);
                        returnList.add(mainRecord);
                    }
                    if(mainRecord.getStr("checkinName").indexOf(record.getStr("checkinName"))==-1){
                        mainRecord.set("checkinName",mainRecord.getStr("checkinName")+"、"+record.getStr("checkinName"));
                    }
                    Record lockRecord = recordMap.get(record.getStr("id"));
                    mainRecord.set("lockAmount",lockRecord.getDouble("totalAmount")+mainRecord.getDouble("lockAmount"));
                    mainRecord.set("lockTimes",lockRecord.getDouble("totalTimes")+mainRecord.getDouble("lockTimes"));
                    mainRecord.set("lockPoints",0.0);
                    mainRecord.set("lockIntegrals",lockRecord.getDouble("totalIntegrals")+mainRecord.getDouble("lockIntegrals"));
                }else{
                    returnList.add(record);
                }

            }
        }
        renderCodeSuccess("success",returnList);
    }

    public void test3(){
        String cardNumber = getPara("cardNumber");
        String expenseSql="select expense_id,SUM(times) as totalTimes,SUM(amount) as totalAmount,SUM(integrals) as totalIntegrals from fina_expense_record_detail where del_flag='0' and is_settled='0' and card_number=? GROUP BY expense_id ";
        List<Record> expenseIds=Db.find(expenseSql,cardNumber);
        renderCodeSuccess("success",expenseIds);
    }

    public static void main(String[] args) throws Exception {
        ((LoggerContext) LoggerFactory.getILoggerFactory())
                .getLogger("org.apache.http")
                .setLevel(Level.ERROR);

        File file=new File("D:\\zhuo\\新建 XLSX 工作表 (27).xlsx");
        InputStream inputStream=new FileInputStream(file);
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

        String[] array=new String[]{
                /*"170353","182608",
                "182606",
                "185359",
                "182611",
                "182610",
                "188572",
                "190222",
                "190521",
                "186687",
                "186197",
                "182603",
                "182601",
                "182602",
                "182607",
                "182600",
                "182605",
                "182609",*/
                "182599"};
        for (int i = 0; i < array.length; i++) {
            String cardNumber=array[i];
            XSSFSheet sheet =null;
            try {
                sheet=workbook.getSheetAt(i);
            }catch (IllegalArgumentException e){

            }
            if(sheet==null){
                sheet=workbook.createSheet(cardNumber);
            }

            int pageNumber = 1;
            int pageSize=10;
            long timeMillis=System.currentTimeMillis();
            //String str = HttpClientsUtils.get("http://finance.cncsgroup.com/api/test3?cardNumber=" + cardNumber);
            //System.out.println("1耗时："+(System.currentTimeMillis()-timeMillis)/1000);
            /*JSONObject strObj=JSON.parseObject(str);
            JSONArray dataIds = strObj.getJSONArray("data");
            if(dataIds==null || dataIds.size()==0){
                continue;
            }*/

            /*Map<String,Record> recordMap=new HashMap<>();
            for (int i1 = 0; i1 < dataIds.size(); i1++) {
                JSONObject jsonObject=dataIds.getJSONObject(i1);
                Record record=new Record();
                record.set("expenseId",jsonObject.getString("expense_id"));
                record.set("totalAmount",jsonObject.getDouble("totalAmount"));
                record.set("totalTimes",jsonObject.getDouble("totalTimes"));
                record.set("totalIntegrals",jsonObject.getDouble("totalIntegrals"));
                recordMap.put(record.getStr("expenseId"),record);
            }*/

            JSONObject jsonObject=new JSONObject();
            /*jsonObject.put("cardNumber",cardNumber);
            jsonObject.put("expenseIds",recordMap.keySet());
            jsonObject.put("pageNumber",pageNumber);
            jsonObject.put("pageSize",pageSize);*/
            int maxPage=0;
            int rowIndex=0;
            do{
                long timeMillis2=System.currentTimeMillis();
                String res =null;
                try {
                    res = HttpClientsUtils.get("http://finance.cncsgroup.com/api/test2?cardNumber="+cardNumber);
                }catch (Exception e){
                    //e.printStackTrace();
                    System.out.println(cardNumber+"报错");
                }
                if(res==null){
                    break;
                }
                System.out.println(cardNumber+"2耗时："+(System.currentTimeMillis()-timeMillis2)/1000);
                //System.out.println(res);
                if(res.startsWith("{") && res.endsWith("}")){
                    JSONObject resObj=JSON.parseObject(res);
                    JSONArray dataArray=resObj.getJSONArray("data");
                    if(dataArray!=null && dataArray.size()>0){
                        for (int j = 0; j < dataArray.size(); j++) {
                            JSONObject data = dataArray.getJSONObject(j);
                            XSSFRow row = sheet.getRow(rowIndex);
                            if(row==null){
                                row=sheet.createRow(rowIndex);
                            }
                            if("sojourn_bill".equals(data.getString("category"))){
                                row.createCell(0).setCellValue("旅居订单");
                            }else{
                                row.createCell(0).setCellValue("旅游团订单");
                            }
                            String no="";
                            if(StrKit.notBlank(data.getString("touristNo"))){
                                no+=data.getString("touristNo")+"-";
                            }
                            if(StrKit.notBlank(data.getString("bookNo"))){
                                no+=data.getString("bookNo")+"-";
                            }
                            if(StrKit.notBlank(data.getString("checkinNo"))){
                                no+=data.getString("checkinNo")+"-";
                            }
                            row.createCell(1).setCellValue(no);
                            row.createCell(2).setCellValue(data.getString("checkinName"));
                            row.createCell(3).setCellValue(data.getString("baseName"));
                            if(StrKit.notBlank(data.getString("touristNo"))){
                                row.createCell(4).setCellValue(DateUtils.formatDate(data.getDate("checkinTime"),"yyyy-MM-dd"));
                                row.createCell(5).setCellValue(DateUtils.formatDate(data.getDate("checkoutTime"),"yyyy-MM-dd"));
                            }else{
                                if(StrKit.notBlank(data.getString("checkinNo"))){
                                    row.createCell(4).setCellValue(DateUtils.formatDate(data.getDate("checkinTime"),"yyyy-MM-dd"));
                                    row.createCell(5).setCellValue(DateUtils.formatDate(data.getDate("checkoutTime"),"yyyy-MM-dd"));
                                }else if(StrKit.notBlank(data.getString("bookNo"))){
                                    row.createCell(4).setCellValue(DateUtils.formatDate(data.getDate("bookStartTime"),"yyyy-MM-dd"));
                                    row.createCell(5).setCellValue(DateUtils.formatDate(data.getDate("bookEndTime"),"yyyy-MM-dd"));
                                }
                            }


                            row.createCell(6).setCellValue(data.getString("lockTimes"));
                            row.createCell(7).setCellValue(data.getString("lockAmount"));
                            row.createCell(8).setCellValue(data.getString("lockIntegrals"));
                            rowIndex++;
                        }
                    }
                    //maxPage = resObj.getIntValue("total") % pageSize == 0 ? resObj.getIntValue("total") / pageSize : (resObj.getIntValue("total") / pageSize)+1 ;
                    //System.out.println("页数："+maxPage);
                }
                //pageNumber++;
            }while (maxPage>pageNumber);
        }
        FileOutputStream excelFileOutPutStream = new FileOutputStream(file);
        workbook.write(excelFileOutPutStream);
        excelFileOutPutStream.flush();
        excelFileOutPutStream.close();

    }
}
