package com.cszn.finance.web.support.cron;


import com.cszn.integrated.service.api.fina.FinaCardTransferService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.fina.FinaNoticeCardChangeService;
import com.cszn.integrated.service.entity.fina.FinaCardTransfer;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

//@Cron("*/2 * * * *")
public class NoticeChangeCardTask implements Runnable{

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private FinaNoticeCardChangeService finaNoticeCardChangeService;

    @Inject
    private FinaMembershipCardService finaMembershipCardService;

    @Inject
    private FinaCardTransferService finaCardTransferService;


    @Override
    public void run() {
        try {
            //noticeChangeCard();
        } catch (Exception e) {
            logger.error("变更通知旅居编辑会员卡记录定时任务异常：[{}]",e);
        }
    }


    /**
     * 通知旅居编辑会员卡（变更会员卡）
     */
    public void noticeChangeCard(){
        logger.info("变更通知旅居编辑会员卡记录定时任务begin...");
        List<FinaCardTransfer> list = finaCardTransferService.getIsNotSync();

        if(list != null && list.size() > 0){
            for (FinaCardTransfer item : list) {
                String oldCardId = "";//item.getOldCardId();
                String newCardId = item.getNewCardId();
                FinaMembershipCard oldCard = finaMembershipCardService.get(oldCardId);
                FinaMembershipCard newCard = finaMembershipCardService.get(newCardId);
                if(oldCard != null && newCard != null){
                    finaNoticeCardChangeService.noticeCardChangeBill(oldCard,newCard,item);
                }
            }
        }
        logger.info("变更通知旅居编辑会员卡记录定时任务end...");
    }
}
