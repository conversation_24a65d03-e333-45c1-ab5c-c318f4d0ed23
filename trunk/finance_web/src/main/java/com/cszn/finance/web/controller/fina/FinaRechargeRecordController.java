/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaRechargeListService;
import com.cszn.integrated.service.api.fina.FinaRechargeRecordService;
import com.cszn.integrated.service.entity.fina.FinaRechargeRecord;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.RechargeType;
import com.cszn.integrated.service.entity.fina.FinaRechargeList;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.upload.UploadFile;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2024年01月17日
 *
 * FinaRechargeRecordController
 */
@RequestMapping(value="/fina/rechargeRecord", viewPath="/modules_page/finance/fina/rechargeRecord")
public class FinaRechargeRecordController extends BaseController {

	@Inject
    private FinaRechargeRecordService finaRechargeRecordService;
	@Inject
    private FinaRechargeListService finaRechargeListService;


    private Logger logger= LoggerFactory.getLogger(FinaRechargeRecordController.class);
	
    public void index() {
        render("recordIndex.html");
    }
    
    /**
     * 充值记录分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	FinaRechargeRecord model = getBean(FinaRechargeRecord.class, "", true);
        Page<FinaRechargeRecord> modelPage = finaRechargeRecordService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaRechargeRecord>(modelPage));
    }
    
    /**
     * 充值列表分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void listPageTable() {
    	FinaRechargeList model = getBean(FinaRechargeList.class, "", true);
        Page<FinaRechargeList> modelPage = finaRechargeListService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaRechargeList>(modelPage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	FinaRechargeRecord model = new FinaRechargeRecord();
    	setAttr("model", model);
    	setAttr("rechargeTypeMap", RechargeType.me().all());
        render("recordForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String id = getPara("id");
        FinaRechargeRecord model = finaRechargeRecordService.findById(id);
        List<FinaRechargeList> rechargeList = finaRechargeListService.findList(id);
        setAttr("model", model);
        setAttr("rechargeTypeMap", RechargeType.me().all());
        setAttr("rechargeList", rechargeList);
        render("recordForm.html");
    }

    /**
     * 查看页面方法
     */
    public void view() {
        final String id = getPara("id");
        setAttr("model", finaRechargeRecordService.findById(id));
        setAttr("rechargeList", finaRechargeListService.findList(id));
        render("recordView.html");
    }
    
    public void save() {
    	int rechargeCount = getParaToInt("rechargeCount");
    	FinaRechargeRecord model = getBean(FinaRechargeRecord.class, "", true);
    	List<FinaRechargeList> rechargeList = new ArrayList<FinaRechargeList>();
		for(int i=0;i<=rechargeCount;i++){
			FinaRechargeList lotteryRecharge = getBean(FinaRechargeList.class,"rechargeList["+i+"]",true);
            if(StrKit.notBlank(lotteryRecharge.getCardNumber())){
            	rechargeList.add(lotteryRecharge);
            }
        }
		if(finaRechargeRecordService.saveRechargeRecord(model, rechargeList, AuthUtils.getUserId())){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 充值名单导入
     */
    public void rechargeListImport(){
    	UploadFile uploadFile = getFile();
    	final String id = getPara("id");
    	Ret returnRet = Ret.fail("msg","导入失败");
        if(uploadFile == null){
        	returnRet = Ret.fail("msg","上传文件不存在，请检查!");
        }else{
        	FinaRechargeRecord rechargeRecord = finaRechargeRecordService.findById(id);
        	try{
        		returnRet =  finaRechargeRecordService.importRechargeList(rechargeRecord, uploadFile.getFile(),uploadFile.getFileName(), AuthUtils.getUserId());
        	}catch (Exception e){
        		logger.error("模板批量充值导入失败:[{}],[{}]",e.getMessage(),e);
        		e.printStackTrace();
        	}
        	if(uploadFile.getFile().exists()){
        		uploadFile.getFile().delete();
        	}
        }
       renderJson(returnRet);
    }
    
    /**
     * 充值名单确认发送
     */
    public void rechargeListConfirm(){
    	final String id = getPara("id");
    	FinaRechargeRecord rechargeRecord = finaRechargeRecordService.findById(id);
    	Global.executorService.execute(new Runnable() {
            @Override
            public void run() {
            	try{
            		final Ret returnRet = finaRechargeRecordService.confirmRechargeList(rechargeRecord, AuthUtils.getUserId());
            		final String returnState = returnRet.getStr("state");
            		final String returnMsg = returnRet.getStr("msg");
            		logger.info("returnState==="+returnState+";;;;;"+"returnMsg==="+returnMsg);
            	}catch (Exception e){
            		logger.error("充值名单确认发送异常:[{}],[{}]",e.getMessage(),e);
            		e.printStackTrace();
            	}
            }
    	});
    	renderJson(Ret.ok("msg","操作成功"));
    }
    
    public void del(){
    	final FinaRechargeRecord model = getBean(FinaRechargeRecord.class, "", true);
    	if(model!=null && StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		if(finaRechargeRecordService.update(model)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }
    
    public void templateDownload(){
        String excelTemplatePath = PathKit.getWebRootPath() + "/upload/福利卡批量充值模板.xls";
//        File file = new File(excelTemplatePath);
        String fileName = "福利卡批量充值模板.xls";
        try {
            HSSFWorkbook wb = new HSSFWorkbook(new FileInputStream(new File(excelTemplatePath)));
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
