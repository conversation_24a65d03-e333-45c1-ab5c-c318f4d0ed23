/**
 * 
 */
package com.cszn.finance.web.controller.sys;

import java.util.Date;

import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.LogService;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.xiaoleilu.hutool.date.DateUtil;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2019年9月19日
 *
 * LogController
 */
@RequestMapping(value="/log", viewPath="/modules_page/sys/log")
public class LogController extends BaseController {

	@Inject
    private LogService logService;
	
    public void index() {
    	setAttr("startDate", DateUtil.formatDate(DateUtil.beginOfDay(new Date())));
    	setAttr("endDate", DateUtil.formatDate(DateUtil.endOfDay(new Date())));
        render("logIndex.html");
    }
    
    /**
     * 日志分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
        Page<Record> modelPage = logService.paginateByCondition("finance", getPara("startDate"), getPara("endDate"), getPara("userAccount"), getPara("userName"), getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<Record>(modelPage));
    }
}
