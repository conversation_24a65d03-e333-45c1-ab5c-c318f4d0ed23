package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordDeductionCardService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordDetailService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.enums.CheckinType;
import com.cszn.integrated.service.entity.enums.CustomerChannel;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.enums.SojournBillType;
import com.cszn.integrated.service.entity.fina.ChangeObj;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.integrated.service.entity.sys.Dict;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Description 消费主记录管理
 * <AUTHOR>
 * @Date 2019/8/22
 **/
@RequestMapping(value="/fina/expenseRecord", viewPath="/modules_page/finance/fina/expenseRecord")
public class ExpenseRecordController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(com.jfinal.ext.interceptor.LogInterceptor.class);

    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;

    @Inject
    private MainBaseService mainBaseService;

    @Inject
    private DictService dictService;

    @Inject
    private FinaMembershipCardService finaMembershipCardService;

    @Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;

    @Inject
    private MainBaseBedService mainBaseBedService;

    @Inject
    private FinaExpenseRecordDeductionCardService finaExpenseRecordDeductionCardService;
    @Inject
    private FinaExpenseRecordDetailService finaExpenseRecordDetailService;
    @Inject
    private MainBusinessEntityService mainBusinessEntityService;
    @Inject
    private UserService userService;
    @Inject
    private MainBaseRoomTypeService mainBaseRoomTypeService;
    @Inject
    private MainBaseRoomService mainBaseRoomService;


    /**
     * 跳转到消费主记录界面
     */
    @Clear(LogInterceptor.class)
    public void index(){
        List<MainBase> baseList = mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        List<Dict> dictList = dictService.getListByTypeOnUse("checkin_status");
        setAttr("dictList",dictList);
        setAttr("checkinTypes", CheckinType.values());
        render("expenseRecordIndex.html");
    }

    public void baseIndex(){

        List<MainBase> baseList = mainBaseService.findBaseList();
        List<MainBase> returnBaseList=new ArrayList<>();
        for(MainBase base:baseList){
            if(StrKit.notBlank(base.getBaseFinanceUserIds()) && base.getBaseFinanceUserIds().indexOf(AuthUtils.getUserId())!=-1){
                returnBaseList.add(base);
            }
        }
        setAttr("baseList",returnBaseList);
        List<Dict> dictList = dictService.getListByTypeOnUse("checkin_status");
        setAttr("dictList",dictList);
        render("baseExpenseRecordIndex.html");
    }



    /**
     * 消费主记录分页
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String fullName = getPara("fullName");
        String followName = getPara("followName");
        String notSettle=getPara("notSettle");
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        Page<Record> page = finaExpenseRecordService.findList(getParaToInt("page"),getParaToInt("limit"),fullName,er,followName,notSettle);
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(page),valueFilter));
    }

    /**
     * 消费主记录分页
     */
    @Clear(LogInterceptor.class)
    public void baseFindListPage(){
        String fullName = getPara("fullName");
        String followName = getPara("followName");
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        if(StrKit.isBlank(er.getBaseId())){
            renderJson(new DataTable<Record>(new ArrayList<>()));
            return;
        }
        Page<Record> page = finaExpenseRecordService.findList(getParaToInt("page"),getParaToInt("limit"),fullName,er,followName,null);
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(page),valueFilter));
    }



    /**
     * 作废
     */
    public void delete(){
        String id = getPara("id");
        Map<String,Object> map = finaExpenseRecordService.delExpenseRecord(id, AuthUtils.getUserId());
        if(map.containsKey("error")){
            renderJson(Ret.fail("msg", map.get("error")));
            return;
        }
        if((Boolean)map.get("result")){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }

    /**
     * 更新
     */
    public void update(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        if(StrKit.isBlank(er.getId())){
            renderJson(Ret.fail("msg", "id不能为空!"));
            return;
        }
        if(finaExpenseRecordService.update(er)){
            renderJson(Ret.ok("msg", "操作成功"));
        }else{
            renderJson(Ret.fail("msg", "操作失败"));
        }
    }


    /**
     * 跳转到结算/查看页面
     */
    @Clear(LogInterceptor.class)
    public void form(){
        String id = getPara("id");
        String type = getPara("type");
        FinaExpenseRecord er = finaExpenseRecordService.get(id);
        MainBaseBed bed = mainBaseBedService.findById(er.getBedId());
        MainBase base = mainBaseService.get(er.getBaseId());
        if(er!=null){
            MainBusinessEntity businessEntity=mainBusinessEntityService.findById(er.getBusinessEntityId());
            setAttr("businessEntity",businessEntity);
            if(StrKit.notBlank(er.getCustomerChannel()) && !"0".equals(er.getCustomerChannel())){
                setAttr("customerChannel", CustomerChannel.valueOf(er.getCustomerChannel()).getValue());
            }
        }


        String checkinStatusName = "";
        String settleStatusName = "";
        setAttr("er",er);
        setAttr("bed",bed);
        setAttr("base",base);
        if(StrKit.notBlank(er.getCreateBy())){
            setAttr("user",userService.findById(er.getCreateBy()));
        }
        if(StrKit.notBlank(er.getCheckinType())){
            CheckinType checkinType=CheckinType.getByKey(er.getCheckinType());
            if(checkinType!=null){
                setAttr("checkinType",checkinType.getValue());
            }
        }
        if(StringUtils.isBlank(er.getCheckinStatus())){
            checkinStatusName = "- -";
        }else{
            if("book".equals(er.getCheckinStatus())){
                checkinStatusName = "预订";
            }else if("stayin".equals(er.getCheckinStatus())){
               checkinStatusName = "在住";
            }else{
                checkinStatusName = "退住";
            }
        }

        if(StringUtils.isBlank(er.getSettleStatus())){
            settleStatusName = "- -";
        }else{
            if("0".equals(er.getSettleStatus())){
                settleStatusName = "未结算";
            }else if("1".equals(er.getSettleStatus())){
                settleStatusName = "已结算";
            }else{
                settleStatusName = "作废";
            }
        }
        setAttr("checkinStatusName",checkinStatusName);
        setAttr("settleStatusName",settleStatusName);
        if(StringUtils.isNotBlank(type)){
            render("settleForm.html");
        }else{
            render("expenseRecordForm.html");
        }
    }



    /**
     * 查看床位信息
     */
    public void bedForm(){
        String bedId = getPara("bedId");
        Record record = Db.findFirst("select b.id,b.room_id as roomId,b.bed_no as bedNo,b.bed_name as bedName,"
			+ "b.bed_type_id as bedTypeId,b.bed_status_id as bedStatusId,b.bed_image as bedImage,"
	        + "b.markup_type_id as markupTypeId,b.is_enable as isBedEnable,b.del_flag,r.room_name as roomName,"
	        + "m.name,m.amount,m.is_enable as isMarkEnable,t.type_name as typeName,s.status_name as statusName,"
	        + "rt.type_name as roomTypeName "
	        + "from main_base_bed b "
	        + "left join main_base_room r on b.room_id = r.id "
	        + "left join main_room_type rt on rt.id = r.room_type "
	        + "left join main_bed_type t on b.bed_type_id = t.id "
	        + "left join main_bed_status s on b.bed_status_id = s.id "
	        + "left join main_base_bed_markup_type m on b.markup_type_id = m.id "
	        + "where b.del_flag = 0 and b.id = ?",bedId);


        setAttr("bed",record);
        MainBaseRoom room=mainBaseRoomService.findById(record.getStr("roomId"));
        MainBaseRoomType roomType=mainBaseRoomTypeService.findById(room.getBaseRoomType());
        setAttr("roomType",roomType);
        render("bedForm.html");
    }

    public void bookFile(){
        String bookNo=getPara("bookNo");


        List<Record> recordList=Db.find("select * from fina_expense_record_file where del_flag='0' and book_no=?",bookNo);

        setAttr("recordList",recordList);
        render("bookFile.html");
    }


    /**
     * 费用账单
     * 通过主记录查询出主记录以及主记录下的子记录(主记录表)
     */
    @Clear(LogInterceptor.class)
    public void queryParentAndElementRecord(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getRecordsByParent(er);
        renderJson(Ret.ok("data",list));
    }


    /**
     * 费用账单
     * 获取主记录下的消费明细
     */
    @Clear(LogInterceptor.class)
    public void queryDetailByParent(){
        String id = getPara("id");
        List<Record> list = finaExpenseRecordService.getDetailByParent(id);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }

    public void updateRemark(){
        FinaExpenseRecord record=getBean(FinaExpenseRecord.class,"",true);
        if(record.update()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.ok("msg","操作失败"));
        }
    }


    /**
     * 扣卡汇总
     */
    @Clear(LogInterceptor.class)
    public void deductCardTotal(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getDeductCardTotalByParent(er);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }


    /**
     * 续住记录
     */
    @Clear(LogInterceptor.class)
    public void queryStayinRecord(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getStayinRecord(er);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }


    /**
     * 会员卡变更记录
     */
    @Clear(LogInterceptor.class)
    public void queryChangeCardRecord(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getChangeCardRecord(er);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }


    /**
     * 【新】会员卡变更记录
     */
    @Clear(LogInterceptor.class)
    public void newQueryChangeCardRecord(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordDeductionCardService.getChangeCardListByExpenseId(er.getId());
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }


    /**
     * 随行人员记录
     */
    @Clear(LogInterceptor.class)
    public void queryFollowRecord(){
        FinaExpenseRecord er = getBean(FinaExpenseRecord.class,"",true);
        List<Record> list = finaExpenseRecordService.getFollowRecord(er);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }



    /**
     * 跳转随行人员离开记录界面
     */
    public void followLeaveForm(){
        String id = getPara("id");
        setAttr("id",id);
        render("followLeaveForm.html");
    }



    /**
     * 随行人员的离开记录
     */
    @Clear(LogInterceptor.class)
    public void queryFollowLeaveRecord(){
        String id = getPara("id");
        List<Record> list = finaExpenseRecordService.getFollowLeaveRecord(id);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }


    /**
     * 纸卡/现金收取记录
     */
    @Clear(LogInterceptor.class)
    public void queryCashRecords(){
        String id = getPara("id");
        List<Record> list = Db.find("select id,expense_id as expenseId,checkin_no as checkinNo,type,amount_num as amountNum," +
                "receive_name as receiveName,receive_time as receiveTime,remark,create_time as createTime,source_system as sourceSystem " +
                "from fina_expense_cash_record where del_flag = 0 and expense_id = ?",id);
        Map<String,Object> map = new HashMap<>();
        map.put("code",0);
        map.put("data",list);
        renderJson(map);
    }


    /**
     * 退住结算
     * 旅居账单结算
     */
    public void billSettle(){
        try {
            String billVal = getPara("billVal");
            logger.info("退住结算接收参数:[{}]",JSON.toJSONString(billVal));
            if(StringUtils.isEmpty(billVal)){ renderJson(Ret.fail("msg", "没有扣卡数据，不可结算"));return; }
            Map<String,JSONArray> map = (Map)JSON.parse(billVal);
            //转为固定map
            List<Map<String,Object>> settleList = new ArrayList<>();
            for(int i=0;i<map.size();i++){
                String[] arr = map.get(i + "").toString().replace("[","").replace("]","").split(",");
                Map<String,Object> settleMap = new HashMap<>();
                settleMap.put("erId",arr[0].substring(1,arr[0].length() -1));
                settleMap.put("cardNumber",arr[1].substring(1,arr[1].length() -1));
                settleMap.put("surplusLockTimes",arr[2] == null ? 0.00:arr[2]);
                settleMap.put("surplusLockAmount",arr[3] == null ? 0.00:arr[3]);
                settleMap.put("deductedTimes",arr[4] == null ? 0.00:arr[4]);
                settleMap.put("deductedAmount",arr[5] == null ? 0.00:arr[5]);
                settleMap.put("nowDeductTimes",arr[6] == null ? 0.00:arr[6]);
                settleMap.put("nowDeductAmount",arr[7] == null ? 0.00:arr[7]);
                settleMap.put("settleRemark",arr[8].substring(1,arr[8].length() -1));
                settleList.add(settleMap);
            }
            logger.info("退住结算处理接收参数:[{}]",JSON.toJSONString(settleList));

            //校验：本次扣卡都为0，则不允许结算
            //region Description
        /*int suc = 0;
        for (Map<String, Object> item : settleList) {
            Double actualTimes = Double.parseDouble(item.get("actualTimes").toString());
            Double actualAmount = Double.parseDouble(item.get("actualAmount").toString());
            if(actualTimes != 0.00 || actualAmount != 0.00){
                suc = 1;
            }
        }
        if(suc == 0){ renderJson(Ret.fail("msg", "退住扣卡数据不能都为0"));return; }*/
            //endregion

            //批量判断账单中的会员卡是否存在，余额是否可扣
            for (Map<String, Object> item : settleList) {
                String cardNumber = (String)item.get("cardNumber");
                FinaMembershipCard card  = finaMembershipCardService.getCardByNumber(cardNumber);
                if(card == null){ renderJson(Ret.fail("msg", cardNumber + "会员卡系统不存在"));return; }
                //判断会员卡扣卡规则
                MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
                if(scheme==null){
                    scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
                }
                if(scheme == null || StringUtils.isBlank(scheme.getDeductWay()) || scheme.getOnceConsume() == null){
                    renderJson(Ret.fail("msg", cardNumber + "会员卡扣卡规则未定义，无法扣卡"));return;
                }

                if(card.getConsumeTimes() == null)card.setConsumeTimes(0.00);
                if(card.getBalance() == null)card.setBalance(0.00);
                if(card.getLockConsumeTimes() == null) card.setLockConsumeTimes(0.00);
                if(card.getLockBalance() == null)card.setLockBalance(0.00);
                if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){
                    if(card.getConsumeTimes() <= 0)
                    { renderJson(Ret.fail("msg", cardNumber + "会员卡天数不足，无法扣卡"));return; }
                }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())){
                    if(card.getBalance() <= 0)
                    { renderJson(Ret.fail("msg", cardNumber + "会员卡金额不足，无法扣卡"));return; }
                }else{
                    renderJson(Ret.fail("msg", "系统暂不支持"+ cardNumber + "会员卡扣卡规则扣卡"));return;
                }
            }

            Map<String,Object> resultMap = finaExpenseRecordService.getBillSettle(settleList,AuthUtils.getUserId());
            if(resultMap.containsKey("error")){
                renderJson(Ret.fail("msg", resultMap.get("error")));
            }else if(resultMap.containsKey("suc")){
                renderJson(Ret.ok("msg", resultMap.get("suc")));
            }
        } catch (Exception e) {
            logger.error("旅居账单退住结算异常:[{}]",e);
            renderJson(Ret.fail("msg", "旅居账单退住结算异常!"));
        }
    }



    /**
     * 中途结算
     * 旅居账单结算
     */
    public void billSettleCenter(){
        try {
            String billVal = getPara("billVal");
            logger.info("中途结算接收参数:[{}]",JSON.toJSONString(billVal));
            if(StringUtils.isEmpty(billVal)){ renderJson(Ret.fail("msg", "没有扣卡数据，不可结算"));return; }
            Map<String,JSONArray> map = (Map)JSON.parse(billVal);
            //转为固定map
            List<Map<String,Object>> settleList = new ArrayList<>();
            for(int i=0;i<map.size();i++){
                String[] arr = map.get(i + "").toString().replace("[","").replace("]","").split(",");
                Map<String,Object> settleMap = new HashMap<>();
                settleMap.put("erId",arr[0].substring(1,arr[0].length() -1));
                settleMap.put("cardNumber",arr[1].substring(1,arr[1].length() -1));
                settleMap.put("surplusLockTimes",arr[2] == null ? 0.00:arr[2]);
                settleMap.put("surplusLockAmount",arr[3] == null ? 0.00:arr[3]);
                settleMap.put("deductedTimes",arr[4] == null ? 0.00:arr[4]);
                settleMap.put("deductedAmount",arr[5] == null ? 0.00:arr[5]);
                settleMap.put("nowDeductTimes",arr[6] == null ? 0.00:arr[6]);
                settleMap.put("nowDeductAmount",arr[7] == null ? 0.00:arr[7]);
                settleMap.put("settleRemark",arr[8].substring(1,arr[8].length() -1));
                settleList.add(settleMap);
            }
            logger.info("中途结算处理接收参数:[{}]",JSON.toJSONString(settleList));

            //校验：本次扣卡都为0，则不允许结算
            //region Description
        /*int flag = 0;
        for (Map<String, Object> item : settleList) {
            Double nowDeductTimes = Double.parseDouble(item.get("nowDeductTimes").toString());
            Double nowDeductAmount = Double.parseDouble(item.get("nowDeductAmount").toString());
            if(nowDeductTimes != 0.00 || nowDeductAmount != 0.00){
                flag = 1;
            }
        }
        if(flag == 0){ renderJson(Ret.fail("msg", "本次扣卡数据不能都为0"));return; }*/
            //endregion

            for (Map<String, Object> item : settleList) {
                String cardNumber = (String)item.get("cardNumber");
                FinaMembershipCard card  = finaMembershipCardService.getCardByNumber(cardNumber);
                if(card == null){ renderJson(Ret.fail("msg", cardNumber + "会员卡系统不存在"));return; }
                //判断会员卡扣卡规则
                MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(cardNumber);
                if(scheme==null){
                    scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(cardNumber);
                }
                if(scheme == null || StringUtils.isBlank(scheme.getDeductWay()) || scheme.getOnceConsume() == null){
                    renderJson(Ret.fail("msg", cardNumber + "会员卡扣卡规则未定义，无法扣卡"));return;
                }

                if(card.getConsumeTimes() == null)card.setConsumeTimes(0.00);
                if(card.getBalance() == null)card.setBalance(0.00);
                if(card.getLockConsumeTimes() == null) card.setLockConsumeTimes(0.00);
                if(card.getLockBalance() == null)card.setLockBalance(0.00);
                if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){
                    if(card.getConsumeTimes() <= 0) {
                        renderJson(Ret.fail("msg", cardNumber + "会员卡天数不足，无法扣卡"));
                        return;
                    }
                }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())){
                    if(card.getBalance() <= 0) {
                        renderJson(Ret.fail("msg", cardNumber + "会员卡金额不足，无法扣卡"));
                        return;
                    }
                }else{
                    renderJson(Ret.fail("msg", "系统暂不支持"+ cardNumber + "会员卡扣卡规则扣卡"));return;
                }
            }

            Map<String,Object> resultMap = finaExpenseRecordService.getBillSettleCenter(settleList,AuthUtils.getUserId());
            if(resultMap.containsKey("error")){
                renderJson(Ret.fail("msg", resultMap.get("error")));
            }else if(resultMap.containsKey("suc")){
                renderJson(Ret.ok("msg", resultMap.get("suc")));
            }
        } catch (Exception e) {
            logger.error("旅居账单中途结算:[{}]",e);
            renderJson(Ret.fail("msg", "旅居账单中途结算异常！"));
        }
    }


    /**
     * 月结算
     */
    public void monthSettleDetail(){

    }



    /**
     * 获取不包含续住的账单记录
     */
    public void getErListNotCon(){
        String id = getPara("id");
        List<Record> erList= finaExpenseRecordService.getErListNotCon(id);
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(erList),valueFilter));
    }

    public void getMainRecordDetail(){
        String id = getPara("id");
        Record record= finaExpenseRecordService.getMainRecordDetail(id);
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        List<Record> recordList=new ArrayList<>();
        recordList.add(record);
        renderJson(JSONObject.toJSONString(new DataTable<Record>(recordList),valueFilter));
    }

    public void getAllRecordDetail(){
        String id = getPara("id");
        List recordList= finaExpenseRecordService.getAllRecordDetail(id);
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(recordList),valueFilter));
    }


    /**
     * 跳转换卡记录界面
     */
    public void changeErForm(){
        setAttr("erId",getPara("id"));//主账单id
        render("changeErIndex.html");
    }



    /**
     * 整单分段换卡界面
     */
    public void changeForm(){
        String id = getPara("id");
        //获取账单记录，若有续住则获取最大的续住时间
        FinaExpenseRecord er =  finaExpenseRecordService.get(id);
        if(er != null){
            //获取最小时间
            if(StringUtils.isNotBlank(er.getBookNo())){

                Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",er.getId());
                //获取最后一次结算时间
                if(lastSettleDate!=null){
                    setAttr("showMinDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd"));
                    setAttr("minDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd HH:mm:ss"));
                    setAttr("msg","该单最后一次结算结束时间为["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]只支持["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]开始换卡");
                }else {
                    //比较预订时间和入住开始时间哪个小
                    if(er.getCheckinTime().compareTo(er.getBookStartTime()) >= 0){
                        setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getBookStartTime(),"yyyy-MM-dd"));
                        setAttr("minDate",TimeUtils.getStrDateByPattern(er.getBookStartTime(),"yyyy-MM-dd HH:mm:ss"));
                    }else{
                        setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd"));
                        setAttr("minDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd HH:mm:ss"));
                    }
                }

            }else{
                Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",er.getId());

                //获取最后一次结算时间
                if(lastSettleDate!=null){
                    setAttr("showMinDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd"));
                    setAttr("minDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd HH:mm:ss"));
                    setAttr("msg","该单最后一次结算结束时间为["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]只支持["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]开始换卡");
                }else {
                    setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd"));
                    setAttr("minDate", TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd HH:mm:ss"));
                }
            }

            //获取最大时间
            if(SojournBillType.followCheckin.getKey().equals(er.getType())){
                setAttr("showMaxDate",er.getRealCheckoutTime()
                        == null?TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"):
                        TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd"));
                setAttr("maxDate",er.getRealCheckoutTime()
                        == null?TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd HH:mm:ss"):
                        TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
            }else{
                //获取最大的续住时间
                if("retreat".equals(er.getCheckinStatus())){
                    setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd"));
                    setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                }else{
                    Map<String,String> map = finaExpenseRecordService.getMaxConTime(id);
                    if(map == null){
                        setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                        setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"));
                    }else{
                        setAttr("maxDate",map.get("date"));
                        setAttr("showMaxDate",map.get("dateStr"));
                    }
                }
            }
            setAttr("erId",id);
        }
        render("changeCardIndex.html");
    }


    /**
     * 单条分段换卡界面
     */
    public void changeSingleForm(){
        String id = getPara("id");
        //获取账单记录，若有续住则获取最大的续住时间
        FinaExpenseRecord er =  finaExpenseRecordService.get(id);
        FinaExpenseRecord mainEr=null;
        if(!"0".equals(er.getParentId())){
            mainEr=finaExpenseRecordService.findById(er.getParentId());
        }else{
            mainEr=er;
        }
        if(er != null){
            //获取最小时间
            if(StringUtils.isNotBlank(er.getBookNo())){

                Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",mainEr.getId());
                //获取最后一次结算时间
                if(lastSettleDate!=null){
                    setAttr("showMinDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd"));
                    setAttr("minDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd HH:mm:ss"));
                    setAttr("msg","该单最后一次结算结束时间为["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]只支持["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]开始换卡");
                }else {
                    //比较预订时间和入住开始时间哪个小
                    if(er.getCheckinTime().compareTo(er.getBookStartTime()) >= 0){
                        setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getBookStartTime(),"yyyy-MM-dd"));
                        setAttr("minDate",TimeUtils.getStrDateByPattern(er.getBookStartTime(),"yyyy-MM-dd HH:mm:ss"));
                    }else{
                        setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd"));
                        setAttr("minDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd HH:mm:ss"));
                    }
                }

            }else{
                Date lastSettleDate=Db.queryDate("select max(end_time) from fina_settle_detail where del_flag='0' and settle_status='1' and expense_id=? ",mainEr.getId());

                //获取最后一次结算时间
                if(lastSettleDate!=null){
                    if(er.getCheckinTime().compareTo(lastSettleDate) >= 0){
                        setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd"));
                        setAttr("minDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd HH:mm:ss"));
                        setAttr("msg","");
                    }else{
                        setAttr("showMinDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd"));
                        setAttr("minDate",TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd HH:mm:ss"));
                        setAttr("msg","该单最后一次结算结束时间为["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]只支持["+TimeUtils.getStrDateByPattern(lastSettleDate,"yyyy-MM-dd")+"]开始换卡");
                    }
                }else {
                    setAttr("showMinDate",TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd"));
                    setAttr("minDate", TimeUtils.getStrDateByPattern(er.getCheckinTime(),"yyyy-MM-dd HH:mm:ss"));
                }
            }



            //获取最大时间
            if(SojournBillType.followCheckin.getKey().equals(er.getType())){
                setAttr("showMaxDate",er.getRealCheckoutTime()
                        == null?TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"):
                        TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd"));
                setAttr("maxDate",er.getRealCheckoutTime()
                        == null?TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd HH:mm:ss"):
                        TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                if(er.getRealCheckoutTime()!=null && er.getCheckoutTime().compareTo(er.getRealCheckoutTime()) < 0){
                    setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"));
                    setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"));
                }
            }else{
                //获取最大的续住时间
                if("retreat".equals(er.getCheckinStatus())){
                    if(er.getCheckoutTime().compareTo(er.getRealCheckoutTime()) < 0){
                        setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"));
                        setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                    }else{
                        setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd"));
                        setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getRealCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                    }
                }else{
                    /*Map<String,String> map = finaExpenseRecordService.getMaxConTime(id);
                    if(map == null){
                        setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                        setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"));
                    }else{
                        setAttr("maxDate",map.get("date"));
                        setAttr("showMaxDate",map.get("dateStr"));
                    }*/
                    setAttr("maxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd HH:mm:ss"));
                    setAttr("showMaxDate",TimeUtils.getStrDateByPattern(er.getCheckoutTime(),"yyyy-MM-dd"));
                }
            }
            setAttr("erId",id);
        }
        render("changeSingleCardIndex.html");
    }



    /**
     * 财务更换会员卡
     */
    public void changCard(){
        try {
            String erId = getPara("erId");
            Integer count=getParaToInt("count");
            String minDateStr = getPara("minDate");
            String maxDateStr = getPara("maxDate");

            List<ChangeObj> list = Lists.newArrayList();
            for(int i=1;i<=count;i++){
                ChangeObj record = getBean(ChangeObj.class,"changeList["+i+"]",true);
                if(StringUtils.isNotBlank(record.getBeginDate()) && StringUtils.isNotBlank(record.getEndDate())){
                    record.setBeginDate(record.getBeginDate() + " 00:00:00");
                    record.setEndDate(record.getEndDate() + " 00:00:00");
                    //判断会员是否存在并属于该会员
                    String exist = finaMembershipCardService.confirmCardMember(record.getCardNumber(),getPara("changeList["+i+"].fullName"));
                    if(StringUtils.isNotBlank(exist)){
                        renderJson(Ret.fail("msg", exist));
                        return;
                    }
                    if(i == 1){
                        record.setBeginDate(minDateStr);//替换真正的最小时间
                    }
                    if(i == count){
                        record.setEndDate(maxDateStr);//替换真正的最大时间
                    }

                    //比较大小时间
                    if(TimeUtils.changeTimeCompare(record.getBeginDate(),record.getEndDate())){
                        renderJson(Ret.fail("msg", "会员卡开始时间不能大于等于结束时间"));return;
                    }
                    list.add(record);
                }
            }

            if(list == null || list.size() <= 0){ renderJson(Ret.fail("msg", "未添加时间段不允许保存"));return; }

            //获取账单
            FinaExpenseRecord er = finaExpenseRecordService.get(erId);
            if(er == null){renderJson(Ret.fail("msg", "该账单不存在"));return;}
            Record record = new Record();
            record.set("appNo",er.getAppNo());
            record.set("baseId",er.getBaseId());
            record.set("uniqueId",er.getUniqueId());
            record.set("checkinNo",er.getCheckinNo());
            record.set("name",er.getName());
            record.set("idCard",er.getIdCard());
            record.set("changeType","1");
            record.set("changeData",JSON.toJSONString(list));
            Map<String,Object> resultMap = finaExpenseRecordService.changCard(record);
            if(resultMap.containsKey("error")){
                renderJson(Ret.fail("msg", resultMap.get("error")));
            }else if(resultMap.containsKey("suc")){
                renderJson(Ret.ok("msg", resultMap.get("suc")));
            }
        } catch (Exception e) {
            logger.error("财务更换会员卡异常:[{}]",e);
            renderJson(Ret.fail("msg", "财务更换会员卡异常!"));
        }
    }

    public void changSingleCard(){
        try {
            String erId = getPara("erId");
            Integer count=getParaToInt("count");
            String minDateStr = getPara("minDate");
            String maxDateStr = getPara("maxDate");

            List<ChangeObj> list = Lists.newArrayList();
            for(int i=1;i<=count;i++){
                ChangeObj record = getBean(ChangeObj.class,"changeList["+i+"]",true);
                if(StringUtils.isNotBlank(record.getBeginDate()) && StringUtils.isNotBlank(record.getEndDate())){
                    record.setBeginDate(record.getBeginDate() + " 00:00:00");
                    record.setEndDate(record.getEndDate() + " 00:00:00");
                    //判断会员是否存在并属于该会员
                    String exist = finaMembershipCardService.confirmCardMember(record.getCardNumber(),getPara("changeList["+i+"].fullName"));
                    if(StringUtils.isNotBlank(exist)){
                        renderJson(Ret.fail("msg", exist));
                        return;
                    }
                    if(i == 1){
                        record.setBeginDate(minDateStr);//替换真正的最小时间
                    }
                    if(i == count){
                        record.setEndDate(maxDateStr);//替换真正的最大时间
                    }

                    //比较大小时间
                    if(TimeUtils.changeTimeCompare(record.getBeginDate(),record.getEndDate())){
                        renderJson(Ret.fail("msg", "会员卡开始时间不能大于等于结束时间"));return;
                    }
                    list.add(record);
                }
            }

            if(list == null || list.size() <= 0){ renderJson(Ret.fail("msg", "未添加时间段不允许保存"));return; }

            //获取账单
            FinaExpenseRecord er = finaExpenseRecordService.get(erId);
            if(er == null){renderJson(Ret.fail("msg", "该账单不存在"));return;}
            Record record = new Record();
            record.set("id",er.getId());
            record.set("appNo",er.getAppNo());
            record.set("baseId",er.getBaseId());
            record.set("uniqueId",er.getUniqueId());
            record.set("checkinNo",er.getCheckinNo());
            record.set("name",er.getName());
            record.set("idCard",er.getIdCard());
            record.set("changeType","1");
            record.set("changeData",JSON.toJSONString(list));
            Map<String,Object> resultMap = finaExpenseRecordService.changSingleCard2(record);
            if((Boolean) resultMap.get("flag")){
                renderJson(Ret.ok("msg", "换卡成功"));
            }else{
                renderJson(Ret.fail("msg", resultMap.get("msg")));
            }
        } catch (Exception e) {
            logger.error("财务更换会员卡异常:[{}]",e);
            renderJson(Ret.fail("msg", "财务更换会员卡异常!"));
        }

    }

    public void cardExpenseIndex(){
        render("cardExpense/cardExpenseIndex.html");
    }

    public void cardExpenseRecordIndex(){
        String cardNumber=getPara("cardNumber");
        setAttr("cardNumber",cardNumber);
        render("cardExpense/cardExpenseRecordIndex.html");
    }

    public void findCardExpenseRecordCount(){
        String cardNumber=getPara("cardNumber");
        String fullName=getPara("fullName");
        Page<Record> recordPage=finaExpenseRecordService.findCardExpenseRecordCount(getParaToInt("page"),getParaToInt("limit"),cardNumber,fullName);

        renderJson(new DataTable<Record>(recordPage));
    }

    public void monthSettleRecordIndex(){
        String startDate=DateUtils.getDate("yyyy-MM")+"-01";
        String endDate=DateUtils.getDate("yyyy-MM-dd");
        String yearMonth=DateUtils.getDate("yyyy-MM");

        List<MainBase> baseList=mainBaseService.findBaseList();

        setAttr("baseList",baseList);
        setAttr("startDate",startDate);
        setAttr("endDate",endDate);
        setAttr("yearMonth",yearMonth);
        render("monthSettleRecordIndex.html");
    }

    public void monthSettleRecordList(){
        String dateRange=getPara("dateRange");
        String baseId=getPara("baseId");
        String isSettle=getPara("isSettle");
        String yearMonth=getPara("yearMonth");
        List<Record> recordList=null;
        if("0".equals(isSettle)){
            String startDate=DateUtils.getDate("yyyy-MM")+"-01";
            String endDate=DateUtils.getDate("yyyy-MM-dd");
            if(StrKit.notBlank(dateRange)){
                String[] dateStrArray=dateRange.split(" - ");
                startDate=dateStrArray[0];
                endDate=dateStrArray[1];
            }
            recordList=finaExpenseRecordService.monthUnsettleRecordList(baseId,startDate,endDate,isSettle);
        }else if("1".equals(isSettle)){
            if(StrKit.isBlank(yearMonth)){
                yearMonth=DateUtils.getDate("yyyy-MM");
            }
            recordList=finaExpenseRecordService.monthSettledRecordList(baseId,yearMonth);
        }else if("2".equals(isSettle)){
            if(StrKit.isBlank(yearMonth)){
                yearMonth=DateUtils.getDate("yyyy-MM");
            }
            List<Record> settledRecordList=finaExpenseRecordService.monthSettledRecordList(baseId,yearMonth);

            String startDate=yearMonth+"-01";
            String endDate=DateUtils.getMaxMonthDate(startDate,"yyyy-MM-dd");
            List<Record> unsettleRecordList=finaExpenseRecordService.monthUnsettleRecordList(baseId,startDate,endDate,"0");
            Map<String,Record> map=new HashMap<>();

            for(Record record:settledRecordList){
                map.put(record.getStr("cardType"),record);
            }

            for(Record record:unsettleRecordList){
                if(map.containsKey(record.getStr("cardType"))){
                    Record mapRecord=map.get(record.getStr("cardType"));
                    mapRecord.set("totalTime",(mapRecord.getDouble("totalTime")==null?0:mapRecord.getDouble("totalTime"))+(record.getDouble("totalTime")==null?0:record.getDouble("totalTime")));
                    mapRecord.set("totalUnitPrice",(mapRecord.getDouble("totalUnitPrice")==null?0:mapRecord.getDouble("totalUnitPrice"))+(record.getDouble("totalUnitPrice")==null?0:record.getDouble("totalUnitPrice")));
                    mapRecord.set("integralsUnitPrice",(mapRecord.getDouble("integralsUnitPrice")==null?0:mapRecord.getDouble("integralsUnitPrice"))+(record.getDouble("integralsUnitPrice")==null?0:record.getDouble("integralsUnitPrice")));
                    mapRecord.set("amountUnitPrice",(mapRecord.getDouble("amountUnitPrice")==null?0:mapRecord.getDouble("amountUnitPrice"))+(record.getDouble("amountUnitPrice")==null?0:record.getDouble("amountUnitPrice")));
                    mapRecord.set("timesUnitPrice",(mapRecord.getDouble("timesUnitPrice")==null?0:mapRecord.getDouble("timesUnitPrice"))+(record.getDouble("timesUnitPrice")==null?0:record.getDouble("timesUnitPrice")));
                    mapRecord.set("sumIntegrals",(mapRecord.getDouble("sumIntegrals")==null?0:mapRecord.getDouble("sumIntegrals"))+(record.getDouble("sumIntegrals")==null?0:record.getDouble("sumIntegrals")));
                    mapRecord.set("amountTime",(mapRecord.getDouble("amountTime")==null?0:mapRecord.getDouble("amountTime"))+(record.getDouble("amountTime")==null?0:record.getDouble("amountTime")));
                    mapRecord.set("sumAmount",(mapRecord.getDouble("sumAmount")==null?0:mapRecord.getDouble("sumAmount"))+(record.getDouble("sumAmount")==null?0:record.getDouble("sumAmount")));
                    mapRecord.set("sumTimes",(mapRecord.getDouble("sumTimes")==null?0:mapRecord.getDouble("sumTimes"))+(record.getDouble("sumTimes")==null?0:record.getDouble("sumTimes")));
                }else{
                    map.put(record.getStr("cardType"),record);
                }
            }
            recordList=new ArrayList<>();
            for(String key:map.keySet()){
                recordList.add(map.get(key));
            }

        }
        if(recordList.size()>0){
            Record totalRecord=new Record();
            totalRecord.set("cardType","合计");
            totalRecord.set("totalTime",0.0);
            totalRecord.set("totalUnitPrice",0.0);
            totalRecord.set("integralsUnitPrice",0.0);
            totalRecord.set("amountUnitPrice",0.0);
            totalRecord.set("timesUnitPrice",0.0);
            totalRecord.set("sumIntegrals",0.0);
            totalRecord.set("amountTime",0.0);
            totalRecord.set("sumAmount",0.0);
            totalRecord.set("sumTimes",0.0);

            for(Record record:recordList){
                if(record.getDouble("totalTime")!=null){
                	final Double totalTime = totalRecord.getDouble("totalTime")+record.getDouble("totalTime");
                    totalRecord.set("totalTime",new BigDecimal(totalTime).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("totalUnitPrice")!=null){
                	final Double totalUnitPrice = totalRecord.getDouble("totalUnitPrice")+record.getDouble("totalUnitPrice");
                    totalRecord.set("totalUnitPrice",new BigDecimal(totalUnitPrice).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("integralsUnitPrice")!=null){
                	final Double integralsUnitPrice = totalRecord.getDouble("integralsUnitPrice")+record.getDouble("integralsUnitPrice");
                    totalRecord.set("integralsUnitPrice",new BigDecimal(integralsUnitPrice).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("amountUnitPrice")!=null){
                	final Double amountUnitPrice = totalRecord.getDouble("amountUnitPrice")+record.getDouble("amountUnitPrice");
                    totalRecord.set("amountUnitPrice",new BigDecimal(amountUnitPrice).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("timesUnitPrice")!=null){
                	final Double timesUnitPrice = totalRecord.getDouble("timesUnitPrice")+record.getDouble("timesUnitPrice");
                    totalRecord.set("timesUnitPrice",new BigDecimal(timesUnitPrice).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("sumIntegrals")!=null){
                	final Double sumIntegrals = totalRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals");
                    totalRecord.set("sumIntegrals",new BigDecimal(sumIntegrals).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("amountTime")!=null){
                	final Double amountTime = totalRecord.getDouble("amountTime")+record.getDouble("amountTime");
                    totalRecord.set("amountTime",new BigDecimal(amountTime).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("sumAmount")!=null){
                	final Double sumAmount = totalRecord.getDouble("sumAmount")+record.getDouble("sumAmount");
                    totalRecord.set("sumAmount",new BigDecimal(sumAmount).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
                if(record.getDouble("sumTimes")!=null){
                	final Double sumTimes = totalRecord.getDouble("sumTimes")+record.getDouble("sumTimes");
                    totalRecord.set("sumTimes",new BigDecimal(sumTimes).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
                }
            }
            recordList.add(totalRecord);
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void monthCheckinDetail(){
        String yearMonth=DateUtils.getDate("yyyy-MM");
        String yearMonthDay=DateUtils.getDate("yyyy-MM-dd");

        List<MainBase> baseList=mainBaseService.findBaseList();

        Map<String,String> checkinTypes=new HashMap<>();
        for (CheckinType value : CheckinType.values()) {
            checkinTypes.put(value.getKey(),value.getValue());
        }

        setAttr("baseList",baseList);
        setAttr("yearMonth",yearMonth);
        setAttr("yearMonthDay",yearMonthDay);
        setAttr("checkinTypes",checkinTypes);
        render("monthCheckinDetail.html");
    }

    @Clear(LogInterceptor.class)
    public void baseMonthCheckinDetail(){
    	String queryType=getPara("queryType");
        String baseId=getPara("baseId");
        String yearMonth=getPara("yearMonth");
        String yearMonthDay=getPara("yearMonthDay");
        String checkinType=getPara("checkinType");
        if(StrKit.isBlank(baseId)){
            renderJson(Ret.fail("msg","请选择基地"));
            return;
        }
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.getDate("yyyy-MM");
        }
        String startDate = "";
        String endDate = "";
        List<Object> params=new ArrayList<>();
        String sql = "";
        if("month".equals(queryType)) {
        	startDate=yearMonth+"-01";
        	endDate=yearMonth+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDate));
        	
        	/*params.add(baseId);
            params.add(startDate+" 00:00:00");
            params.add(endDate+" 23:59:59");

            
            sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,"
        		+ "a.create_time,a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,"
        		+ "a.checkin_no,a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount,a.checkin_type as checkinType,"
        		+ "SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,'0' as settleStatus,a.is_change_bed "
                    //" sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=a.id and type='follow_checkin' and del_flag='0' GROUP BY id_card  ) as followCount "
        		+ "from fina_expense_record a \n"
        		+ "left join fina_expense_record_detail d on d.expense_id=a.id \n"
        		+ "left join fina_membership_card b on b.id=(select id from fina_membership_card "
        		+ "where d.card_number=card_number ORDER BY del_flag limit 1)  \n"
        		+ "left join main_membership_card_type c on b.card_type_id=c.id \n"
        		+ "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
        		+ "left join  main_base_bed g on g.id=a.bed_id " +
                    ""
        		+ "where a.base_id=? and a.category='sojourn_bill' and d.del_flag='0' and d.start_time>=? "
        		+ "and d.start_time<=? and a.settle_status ='0' and a.del_flag='0' and d.is_settled='0' ";
            if(StrKit.notBlank(checkinType)){
                sql+=" and a.checkin_type=? ";
                params.add(checkinType);
            }*/
            sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,a.create_time " +
                    ",a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,a.checkin_no,a.`name`,c.card_type as cardType " +
                    ",t.sumTimes,t.sumAmount,a.checkin_type as checkinType,t.sumIntegrals,'0.0' as sumPoints,g.bed_name as bedName,'0' as settleStatus,a.is_change_bed " +
                    "from ( " +
                    /*"select expense_id,SUM(times) as sumTimes,SUM(amount) as sumAmount,SUM(integrals) as sumIntegrals from fina_expense_record_detail rd  " +
                    "where rd.is_settled='0' and rd.del_flag='0' and rd.start_time BETWEEN ? and ? " +
                    "GROUP BY rd.expense_id " +*/
                    "select expense_id,SUM(times) as sumTimes,SUM(amount) as sumAmount,SUM(integrals) as sumIntegrals from fina_expense_record_detail rd  " +
                    "where rd.id in ( select id from fina_expense_record_detail rd  where rd.is_settled='0' and rd.del_flag='0' and rd.start_time BETWEEN ? and ?) " +
                    "GROUP BY rd.expense_id " +

                    ") t inner join fina_expense_record a on a.id=t.expense_id " +
                    "left join fina_membership_card b on b.id=(select id from fina_membership_card where a.card_number=card_number ORDER BY del_flag limit 1) " +
                    "left join main_membership_card_type c on b.card_type_id=c.id " +
                    "left join main_base_bed g on g.id=a.bed_id " +
                    "where a.base_id=? and a.category='sojourn_bill' and a.settle_status ='0' and a.del_flag='0'";
            
            params.add(startDate+" 00:00:00");
            params.add(endDate+" 23:59:59");
            params.add(baseId);
            if(StrKit.notBlank(checkinType)){
                sql+=" and a.checkin_type=? ";
                params.add(checkinType);
            }
        


            params.add(yearMonth);
            params.add(baseId);
            sql +="GROUP BY a.id \n"
        		+ "UNION\n"
        		+ "select b.is_private_room,b.checkin_status,b.book_no,b.card_number,b.id,b.parent_id,b.customer_channel,b.create_time,"
        		+ "b.book_start_time,b.book_end_time,b.checkin_time,b.checkout_time,b.real_checkout_time,b.checkin_no,"
        		+ "b.`name`,d.card_type as cardType,SUM(a.actual_times) as sumTimes,SUM(a.actual_amount) as sumAmount,b.checkin_type as checkinType,"
        		+ "SUM(a.actual_integrals) as sumIntegrals,SUM(a.actual_points) as sumPoints,g.bed_name as bedName,"
        		+ "a.settle_status as settleStatus,b.is_change_bed " +
                    //"sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and type='follow_checkin' and del_flag='0' GROUP BY id_card  ) as followCount " +
                    "from fina_settle_detail a \n"
        		+ "left join fina_expense_record b on a.expense_id=b.id \n"
        		+ "left join fina_membership_card c on c.id=(select id from fina_membership_card "
        		+ "where a.card_number=card_number ORDER BY del_flag limit 1) \n"
        		+ "left join main_membership_card_type d on c.card_type_id=d.id "
        		+ "left join main_card_deduct_scheme e on e.id=c.long_deduct_scheme_id "
        		+ "left join  main_base_bed g on g.id=b.bed_id "
        		+ "where a.`year_month`=? and a.del_flag='0' and a.settle_status='1' and b.del_flag='0' and b.base_id=? "
        		+ "and a.settle_status in ('0','1') ";
            if(StrKit.notBlank(checkinType)){
                sql+=" and b.checkin_type=? ";
                params.add(checkinType);
            }
            sql+=" GROUP BY b.id\n order by create_time \n";
        }else if("year".equals(queryType)){
            String[] yearArray=yearMonth.split("-");
            startDate=yearArray[0]+"-01-01";
            endDate=yearArray[0]+"-12-31";;

            params.add(baseId);
            params.add(startDate+" 00:00:00");
            params.add(endDate+" 23:59:59");


            sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,"
                    + "a.create_time,a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,"
                    + "a.checkin_no,a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount,a.checkin_type as checkinType,"
                    + "SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,'0' as settleStatus,a.is_change_bed "
                    //" sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=a.id and type='follow_checkin' and del_flag='0' GROUP BY id_card  ) as followCount "
                    + "from fina_expense_record a \n"
                    + "left join fina_expense_record_detail d on d.expense_id=a.id \n"
                    + "left join fina_membership_card b on b.id=(select id from fina_membership_card "
                    + "where d.card_number=card_number ORDER BY del_flag limit 1)  \n"
                    + "left join main_membership_card_type c on b.card_type_id=c.id \n"
                    + "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
                    + "left join  main_base_bed g on g.id=a.bed_id " +
                    ""
                    + "where a.base_id=? and a.category='sojourn_bill' and d.del_flag='0' and d.start_time>=? "
                    + "and d.start_time<=? and a.settle_status ='0' and a.del_flag='0' and d.is_settled='0' ";
            if(StrKit.notBlank(checkinType)){
                sql+=" and a.checkin_type=? ";
                params.add(checkinType);
            }
            params.add(yearMonth);
            params.add(baseId);
            sql +="GROUP BY a.id \n"
                    + "UNION\n"
                    + "select b.is_private_room,b.checkin_status,b.book_no,b.card_number,b.id,b.parent_id,b.customer_channel,b.create_time,"
                    + "b.book_start_time,b.book_end_time,b.checkin_time,b.checkout_time,b.real_checkout_time,b.checkin_no,"
                    + "b.`name`,d.card_type as cardType,SUM(a.actual_times) as sumTimes,SUM(a.actual_amount) as sumAmount,b.checkin_type as checkinType,"
                    + "SUM(a.actual_integrals) as sumIntegrals,SUM(a.actual_points) as sumPoints,g.bed_name as bedName,"
                    + "a.settle_status as settleStatus,b.is_change_bed " +
                    //"sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and type='follow_checkin' and del_flag='0' GROUP BY id_card  ) as followCount " +
                    "from fina_settle_detail a \n"
                    + "left join fina_expense_record b on a.expense_id=b.id \n"
                    + "left join fina_membership_card c on c.id=(select id from fina_membership_card "
                    + "where a.card_number=card_number ORDER BY del_flag limit 1) \n"
                    + "left join main_membership_card_type d on c.card_type_id=d.id "
                    + "left join main_card_deduct_scheme e on e.id=c.long_deduct_scheme_id "
                    + "left join  main_base_bed g on g.id=b.bed_id "
                    + "where a.`year_month`=? and a.del_flag='0' and a.settle_status='1' and b.del_flag='0' and b.base_id=? "
                    + "and a.settle_status in ('0','1') ";
            if(StrKit.notBlank(checkinType)){
                sql+=" and b.checkin_type=? ";
                params.add(checkinType);
            }
            sql+=" GROUP BY b.id\n order by create_time \n";
        }else {
        	startDate=yearMonthDay;
        	endDate=yearMonthDay;
        	
        	params.add(baseId);
            params.add(startDate+" 00:00:00");
            params.add(endDate+" 23:59:59");
            
            sql = "select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,a.create_time,"
        		+ "a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,a.checkin_no,"
        		+ "a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount,a.checkin_type as checkinType,"
        		+ "SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,"
        		+ "d.is_settled as settleStatus,a.is_change_bed " +
                    //"sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=a.id and type='follow_checkin' and del_flag='0' GROUP BY id_card ) as followCount " +
                    "from fina_expense_record a "
        		+ "left join fina_expense_record_detail d on d.expense_id=a.id "
        		+ "left join fina_membership_card b on b.id=(select id from fina_membership_card "
        		+ "where d.card_number=card_number ORDER BY del_flag limit 1) "
        		+ "left join main_membership_card_type c on b.card_type_id=c.id "
        		+ "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
        		+ "left join  main_base_bed g on g.id=a.bed_id "
        		+ "where a.base_id=? and a.category='sojourn_bill' and d.del_flag='0' and d.start_time>=? "
        		+ "and d.start_time<=? and a.settle_status ='0' and a.del_flag='0'";
            if(StrKit.notBlank(checkinType)){
                sql+=" and a.checkin_type=? ";
                params.add(checkinType);
            }
            sql+=" group by a.id";
        }
        
        List<Record> recordList=Db.find(sql,params.toArray());

        if(recordList.size()==0){
            renderJson(new DataTable<Record>(recordList));
            return;
        }
        Map<String,Record> recordMap=new TreeMap<>();

        List<String> follParams=new ArrayList<>();
        String follStr="";
        for(Record record:recordList){
            if("0".equals(record.getStr("parent_id"))){
                recordMap.put(record.getStr("id"),record);

                CheckinType type=CheckinType.getByKey(record.getStr("checkinType"));
                if(type!=null){
                    record.set("checkinType",type.getValue());
                }
                follStr+="?,";
                follParams.add(record.getStr("id"));
            }
        }
        follStr=follStr.substring(0,follStr.length()-1);
        String follSql="select sum(num) total,parent_id from (\n" +
                "\n" +
                "select COUNT(DISTINCT id_card) as num,parent_id from fina_expense_record \n" +
                "where parent_id in("+follStr+") " +
                "\n" +
                "and type='follow_checkin' \n" +
                "and del_flag='0' GROUP BY id_card,parent_id\n" +
                "\n" +
                ")t GROUP BY parent_id\n";

        List<Record> follRecordList=Db.find(follSql,follParams.toArray());

        Map<String,Integer> follMap=new HashMap<>();
        for(Record record:follRecordList){
            follMap.put(record.getStr("parent_id"),record.getInt("total"));
        }

        List<String> mainRecordIds=new ArrayList<>();
        String str="";
        for(Record record:recordList){
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    mainRecordIds.add(record.getStr("parent_id"));
                    str+="?,";
                }
            }
        }
        Map<String,FinaExpenseRecord> mainRecordMap=new HashMap<>();
        if(mainRecordIds.size()>0){
            str=str.substring(0,str.length()-1);
            String mainRecordSql="select * from fina_expense_record where id in ("+str+") ";
            List<FinaExpenseRecord> mainRecordList=finaExpenseRecordService.findListBySql(mainRecordSql,mainRecordIds.toArray());
            for (FinaExpenseRecord finaExpenseRecord : mainRecordList) {
                mainRecordMap.put(finaExpenseRecord.getId(),finaExpenseRecord);
            }
        }
        for(Record record:recordList){
            Integer total=follMap.get(record.getStr("id"));
            if(total==null){
                total=0;
            }
            record.set("followCount",total);
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    FinaExpenseRecord mainRecord=mainRecordMap.get(record.getStr("parent_id"));
                    parentRecord=new Record();
                    parentRecord.setColumns(record);
                    parentRecord.set("checkin_status",mainRecord.getCheckinStatus());
                    parentRecord.set("book_no",mainRecord.getBookNo());
                    //parentRecord.set("card_number",mainRecord.getCardNumber());
                    parentRecord.set("book_start_time",mainRecord.getBookStartTime());
                    parentRecord.set("book_end_time",mainRecord.getBookEndTime());
                    parentRecord.set("checkin_time",mainRecord.getCheckinTime());
                    parentRecord.set("checkout_time",mainRecord.getCheckoutTime());
                    parentRecord.set("real_checkout_time",mainRecord.getRealCheckoutTime());
                    parentRecord.set("is_private_room",mainRecord.getIsPrivateRoom());

                    recordMap.put(record.getStr("parent_id"),parentRecord);
                    continue;
                }
                parentRecord.set("sumTimes",parentRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
                parentRecord.set("sumAmount",parentRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
                parentRecord.set("sumIntegrals",parentRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
                parentRecord.set("sumPoints",0.0);
            }
        }
        List<Record> returnList=new ArrayList<>();

        Record totalRecord=new Record();
        totalRecord.set("name","合计");
        totalRecord.set("sumTimes",0.0);
        totalRecord.set("sumAmount",0.0);
        totalRecord.set("sumIntegrals",0.0);
        totalRecord.set("sumPoints",0.0);
        totalRecord.set("checkinDays",0.0);
        totalRecord.set("settleDays",0.0);

        List<String> continuedParams=new ArrayList<>();
        StringBuffer continuedBuff=new StringBuffer("");
        for(String key:recordMap.keySet()){
            Record record=recordMap.get(key);
            returnList.add(record);
            Double sumTimes=record.getDouble("sumTimes");
            Double sumAmount=record.getDouble("sumAmount");
            Double sumIntegrals=record.getDouble("sumIntegrals");
            if(sumTimes==null){
                sumTimes=0.0;
            }
            if(sumAmount==null){
                sumAmount=0.0;
            }
            if(sumIntegrals==null){
                sumIntegrals=0.0;
            }
            double settleDays=0.0;
            if(sumTimes>=0.0 || sumAmount>0.0 || sumIntegrals>0.0){
                if(sumAmount>0.0){


                }else{
                    settleDays=sumTimes+sumIntegrals;
                }
            }
            record.set("settleDays",settleDays);


            totalRecord.set("sumTimes",totalRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
            totalRecord.set("sumAmount",totalRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
            totalRecord.set("sumIntegrals",totalRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
            totalRecord.set("sumPoints",0.0);
            totalRecord.set("settleDays",totalRecord.getDouble("settleDays")+record.getDouble("settleDays"));

            continuedParams.add(key);
            continuedBuff.append("?,");
        }
        String continuedStr=continuedBuff.substring(0,continuedBuff.length()-1);
        String continuedSql="select parent_id,MAX(checkout_time) as maxCheckout from fina_expense_record where type='continued_residence' " +
                "and parent_id in("+continuedStr+") and del_flag='0' and settle_status in ('0','1') GROUP BY parent_id ";
        List<Record> continuedList=Db.find(continuedSql,continuedParams.toArray());
        for(Record continued:continuedList){
            Record record=recordMap.get(continued.getStr("parent_id"));
            record.set("checkout_time",continued.getDate("maxCheckout"));
        }


        if(returnList.size()>0){
            for(String key:recordMap.keySet()){
                Record record=recordMap.get(key);
                if("book".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("book_start_time"));
                    record.set("end",record.getDate("book_end_time"));
                }else if ("stayin".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("checkin_time"));
                    record.set("end",record.getDate("checkout_time"));
                }else if("retreat".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("checkin_time"));
                    record.set("end",record.getDate("real_checkout_time"));
                }
                Date start=record.getDate("start");
                Date end=record.getDate("end");

                Date calStart=null;
                Date calEnd=null;
                if(DateUtils.compareDays(start,DateUtils.parseDate(startDate+" 00:00:00"))<0){
                    calStart=DateUtils.parseDate(startDate+" 00:00:00");
                }else{
                    calStart=start;
                    calStart=DateUtils.parseDate(DateUtils.formatDate(calStart,"yyyy-MM-dd")+" 00:00:00");
                }

                if(DateUtils.compareDays(end,DateUtils.parseDate(endDate+" 23:59:59"))>0){
                    calEnd=DateUtils.parseDate(endDate+" 24:59:59");
                }else{
                    calEnd=end;
                    calEnd=DateUtils.parseDate(DateUtils.formatDate(calEnd,"yyyy-MM-dd")+" 23:59:59");
                }
                if("month".equals(queryType)) {
                	long days=(calEnd.getTime()-calStart.getTime())/(86400000);
                	record.set("checkinDays",days);
                }else {
                	record.set("checkinDays",1);
                }
                Double sumTimes=record.getDouble("sumTimes");
                Double sumAmount=record.getDouble("sumAmount");
                Double sumIntegrals=record.getDouble("sumIntegrals");
                if(sumTimes==null){
                    sumTimes=0.0;
                }
                if(sumAmount==null){
                    sumAmount=0.0;
                }
                if(sumIntegrals==null){
                    sumIntegrals=0.0;
                }
                if(record.getDouble("sumAmount")!=null && record.getDouble("sumAmount")>0){
                    double settleDays=record.getDouble("checkinDays");
                    if("1".equals(record.getStr("is_private_room"))){
                        settleDays=record.getDouble("checkinDays")*2;
                    }
                    if(record.getInt("followCount")!=null && record.getInt("followCount")>0){
                        settleDays+=record.getDouble("checkinDays")*record.getInt("followCount");
                    }
                    record.set("settleDays",settleDays);
                    totalRecord.set("settleDays",totalRecord.getDouble("settleDays")+record.getDouble("settleDays"));
                }else if(sumTimes==0 && sumAmount==0.0 && sumIntegrals==0.0){
                    double settleDays=record.getDouble("checkinDays");
                    if("1".equals(record.getStr("is_private_room"))){
                        settleDays=record.getDouble("checkinDays")*2;
                    }
                    if(record.getInt("followCount")!=null && record.getInt("followCount")>0){
                        settleDays+=record.getDouble("checkinDays")*record.getInt("followCount");
                    }
                    record.set("settleDays",settleDays);
                    totalRecord.set("settleDays",totalRecord.getDouble("settleDays")+record.getDouble("settleDays"));
                }


                totalRecord.set("checkinDays",totalRecord.getDouble("checkinDays")+record.getDouble("checkinDays"));
            }
            returnList.add(totalRecord);
        }

        renderJson(new DataTable<Record>(returnList));
    }

    @Clear(LogInterceptor.class)
    public void baseMonthCheckinDetailExport(){
    	String queryType=getPara("queryType");
        String baseId=getPara("baseId");
        String yearMonth=getPara("yearMonth");
        String yearMonthDay=getPara("yearMonthDay");
        String checkinType=getPara("checkinType");

        if(StrKit.isBlank(baseId)){
            renderJson(Ret.fail("msg","请选择基地"));
            return;
        }
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.getDate("yyyy-MM");
        }
        String startDate = "";
        String endDate = "";
        List<Object> params=new ArrayList<>();
        String sql = "";
        if("month".equals(queryType)) {
        	startDate=yearMonth+"-01";
        	endDate=yearMonth+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDate));
        	
        	/*params.add(baseId);
        	params.add(startDate+"-01"+" 00:00:00");
        	params.add(endDate+" 23:59:59");

        	
        	sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,a.create_time,"
    			+ "a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,a.checkin_no,"
    			+ "a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount,a.checkin_type as checkinType,"
    			+ "SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,'0' as settleStatus,a.is_change_bed " +
                    //"sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=a.id and type='follow_checkin' and del_flag='0' GROUP BY id_card ) as followCount "
    			 "from fina_expense_record a \n"
    			+ "left join fina_expense_record_detail d on d.expense_id=a.id \n"
    			+ "left join fina_membership_card b on b.id=(select id from fina_membership_card "
    			+ "where d.card_number=card_number ORDER BY del_flag limit 1)  \n"
    			+ "left join main_membership_card_type c on b.card_type_id=c.id \n"
    			+ "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
    			+ "left join  main_base_bed g on g.id=a.bed_id "
    			+ "where a.base_id=? and a.category='sojourn_bill' and d.del_flag='0' and d.start_time>=? "
    			+ "and d.start_time<=? and a.settle_status ='0' and a.del_flag='0' and d.is_settled='0'";

        	if(StrKit.notBlank(checkinType)){
        	    sql+=" and a.checkin_type=? ";
        	    params.add(checkinType);
            }*/

            sql="select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,a.create_time " +
                    ",a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,a.checkin_no,a.`name`,c.card_type as cardType " +
                    ",t.sumTimes,t.sumAmount,a.checkin_type as checkinType,t.sumIntegrals,'0.0' as sumPoints,g.bed_name as bedName,'0' as settleStatus,a.is_change_bed " +
                    "from ( " +
                    /*"select expense_id,SUM(times) as sumTimes,SUM(amount) as sumAmount,SUM(integrals) as sumIntegrals from fina_expense_record_detail rd  " +
                    "where rd.is_settled='0' and rd.del_flag='0' and rd.start_time BETWEEN ? and ? " +
                    "GROUP BY rd.expense_id " +*/
                    "select expense_id,SUM(times) as sumTimes,SUM(amount) as sumAmount,SUM(integrals) as sumIntegrals from fina_expense_record_detail rd  " +
                    "where rd.id in ( select id from fina_expense_record_detail rd  where rd.is_settled='0' and rd.del_flag='0' and rd.start_time BETWEEN ? and ?) " +
                    "GROUP BY rd.expense_id " +
                    ") t inner join fina_expense_record a on a.id=t.expense_id " +
                    "left join fina_membership_card b on b.id=(select id from fina_membership_card where a.card_number=card_number ORDER BY del_flag limit 1) " +
                    "left join main_membership_card_type c on b.card_type_id=c.id " +
                    "left join main_base_bed g on g.id=a.bed_id " +
                    "where a.base_id=? and a.category='sojourn_bill' and a.settle_status ='0' and a.del_flag='0'";

            params.add(startDate+" 00:00:00");
            params.add(endDate+" 23:59:59");
            params.add(baseId);
            if(StrKit.notBlank(checkinType)){
                sql+=" and a.checkin_type=? ";
                params.add(checkinType);
            }

            params.add(yearMonth);
            params.add(baseId);
            sql+=" GROUP BY a.id \n"
    			+ "UNION\n"
    			+ "select b.is_private_room,b.checkin_status,b.book_no,b.card_number,b.id,b.parent_id,b.customer_channel,b.create_time,"
    			+ "b.book_start_time,b.book_end_time,b.checkin_time,b.checkout_time,b.real_checkout_time,b.checkin_no,"
    			+ "b.`name`,d.card_type as cardType,SUM(a.actual_times) as sumTimes,SUM(a.actual_amount) as sumAmount,b.checkin_type as checkinType,"
    			+ "SUM(a.actual_integrals) as sumIntegrals,SUM(a.actual_points) as sumPoints,g.bed_name as bedName,"
    			+ "a.settle_status as settleStatus,b.is_change_bed " +
                    //"sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and type='follow_checkin' and del_flag='0' GROUP BY id_card ) as followCount  " +
                    "from fina_settle_detail a \n"
    			+ "left join fina_expense_record b on a.expense_id=b.id \n"
    			+ "left join fina_membership_card c on c.id=(select id from fina_membership_card "
    			+ "where a.card_number=card_number ORDER BY del_flag limit 1)  \n"
    			+ "left join main_membership_card_type d on c.card_type_id=d.id "
    			+ "left join main_card_deduct_scheme e on e.id=c.long_deduct_scheme_id "
    			+ "left join  main_base_bed g on g.id=b.bed_id "
    			+ "where a.`year_month`=? and a.del_flag='0' and a.settle_status='1' and b.del_flag='0' and b.base_id=? ";
        	if(StrKit.notBlank(checkinType)){
                sql+=" and b.checkin_type=? ";
                params.add(checkinType);
            }
        	sql+= "and a.settle_status in ('0','1')  GROUP BY b.id\n order by create_time \n";
        }else {
        	startDate=yearMonthDay;
        	endDate=yearMonthDay;
        	
        	params.add(baseId);
            params.add(startDate+" 00:00:00");
            params.add(endDate+" 23:59:59");
            
            sql = "select a.is_private_room,a.checkin_status,a.book_no,a.card_number,a.id,a.parent_id,a.customer_channel,a.create_time,"
        		+ "a.book_start_time,a.book_end_time,a.checkin_time,a.checkout_time,a.real_checkout_time,a.checkin_no,"
        		+ "a.`name`,c.card_type as cardType,SUM(d.times) as sumTimes,SUM(amount) as sumAmount,a.checkin_type as checkinType,"
        		+ "SUM(integrals) as sumIntegrals,SUM(points) as sumPoints,g.bed_name as bedName,"
        		+ "d.is_settled as settleStatus,a.is_change_bed " +
                    //"sum(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=a.id and type='follow_checkin' and del_flag='0' GROUP BY id_card ) as followCount " +
                    "from fina_expense_record a "
        		+ "left join fina_expense_record_detail d on d.expense_id=a.id "
        		+ "left join fina_membership_card b on b.id=(select id from fina_membership_card "
        		+ "where d.card_number=card_number ORDER BY del_flag limit 1) "
        		+ "left join main_membership_card_type c on b.card_type_id=c.id "
        		+ "left join main_card_deduct_scheme f on f.id=b.long_deduct_scheme_id "
        		+ "left join  main_base_bed g on g.id=a.bed_id "
        		+ "where a.base_id=? and a.category='sojourn_bill' and d.del_flag='0' and d.start_time>=? "
        		+ "and d.start_time<=? ";
            if(StrKit.notBlank(checkinType)){
                sql+=" and a.checkin_type=? ";
                params.add(checkinType);
            }
            sql+="and a.settle_status ='0' and a.del_flag='0' group by a.id";
        }

        List<Record> recordList=Db.find(sql,params.toArray());

        if(recordList.size()==0){
            renderJson(new DataTable<Record>(recordList));
            return;
        }

        List<String> follParams=new ArrayList<>();
        String follStr="";

        Map<String,Record> recordMap=new TreeMap<>();
        for(Record record:recordList){
            if("0".equals(record.getStr("parent_id"))){
                recordMap.put(record.getStr("id"),record);

                CheckinType type=CheckinType.getByKey(record.getStr("checkinType"));
                if(type!=null){
                    record.set("checkinType",type.getValue());
                }
                follStr+="?,";
                follParams.add(record.getStr("id"));
            }
        }

        follStr=follStr.substring(0,follStr.length()-1);
        String follSql="select sum(num) total,parent_id from (\n" +
                "\n" +
                "select COUNT(DISTINCT id_card) as num,parent_id from fina_expense_record \n" +
                "where parent_id in("+follStr+") " +
                "\n" +
                "and type='follow_checkin' \n" +
                "and del_flag='0' GROUP BY id_card,parent_id\n" +
                "\n" +
                ")t GROUP BY parent_id\n";

        List<Record> follRecordList=Db.find(follSql,follParams.toArray());

        Map<String,Integer> follMap=new HashMap<>();
        for(Record record:follRecordList){
            follMap.put(record.getStr("parent_id"),record.getInt("total"));
        }

        List<String> mainRecordIds=new ArrayList<>();
        String str="";
        for(Record record:recordList){
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    mainRecordIds.add(record.getStr("parent_id"));
                    str+="?,";
                }
            }
        }
        Map<String,FinaExpenseRecord> mainRecordMap=new HashMap<>();
        if(mainRecordIds.size()>0){
            str=str.substring(0,str.length()-1);
            String mainRecordSql="select * from fina_expense_record where id in ("+str+") ";
            List<FinaExpenseRecord> mainRecordList=finaExpenseRecordService.findListBySql(mainRecordSql,mainRecordIds.toArray());
            for (FinaExpenseRecord finaExpenseRecord : mainRecordList) {
                mainRecordMap.put(finaExpenseRecord.getId(),finaExpenseRecord);
            }
        }

        for(Record record:recordList){
            Integer total=follMap.get(record.getStr("id"));
            if(total==null){
                total=0;
            }
            record.set("followCount",total);
            if(!"0".equals(record.getStr("parent_id"))){
                Record parentRecord=recordMap.get(record.getStr("parent_id"));
                if(parentRecord==null){
                    FinaExpenseRecord mainRecord=mainRecordMap.get(record.getStr("parent_id"));
                    parentRecord=new Record();
                    parentRecord.setColumns(record);
                    parentRecord.set("checkin_status",mainRecord.getCheckinStatus());
                    parentRecord.set("book_no",mainRecord.getBookNo());
                    //parentRecord.set("card_number",mainRecord.getCardNumber());
                    parentRecord.set("book_start_time",mainRecord.getBookStartTime());
                    parentRecord.set("book_end_time",mainRecord.getBookEndTime());
                    parentRecord.set("checkin_time",mainRecord.getCheckinTime());
                    parentRecord.set("checkout_time",mainRecord.getCheckoutTime());
                    parentRecord.set("real_checkout_time",mainRecord.getRealCheckoutTime());
                    parentRecord.set("is_private_room",mainRecord.getIsPrivateRoom());

                    recordMap.put(record.getStr("parent_id"),parentRecord);
                    continue;
                }
                parentRecord.set("sumTimes",parentRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
                parentRecord.set("sumAmount",parentRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
                parentRecord.set("sumIntegrals",parentRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
                parentRecord.set("sumPoints",0.0);
            }
        }
        List<Record> returnList=new ArrayList<>();

        /*Record totalRecord=new Record();
        totalRecord.set("name","合计");
        totalRecord.set("sumTimes",0.0);
        totalRecord.set("sumAmount",0.0);
        totalRecord.set("sumIntegrals",0.0);
        totalRecord.set("sumPoints",0.0);*/

        List<String> continuedParams=new ArrayList<>();
        StringBuffer continuedBuff=new StringBuffer("");
        for(String key:recordMap.keySet()){
            Record record=recordMap.get(key);
            returnList.add(record);
            /*totalRecord.set("sumTimes",totalRecord.getDouble("sumTimes")+record.getDouble("sumTimes"));
            totalRecord.set("sumAmount",totalRecord.getDouble("sumAmount")+record.getDouble("sumAmount"));
            totalRecord.set("sumIntegrals",totalRecord.getDouble("sumIntegrals")+record.getDouble("sumIntegrals"));
            totalRecord.set("sumPoints",totalRecord.getDouble("sumPoints")+record.getDouble("sumPoints"));*/
            continuedParams.add(key);
            continuedBuff.append("?,");
        }
        String continuedStr=continuedBuff.substring(0,continuedBuff.length()-1);
        String continuedSql="select parent_id,MAX(checkout_time) as maxCheckout from fina_expense_record where type='continued_residence' " +
                "and parent_id in("+continuedStr+") and del_flag='0' and settle_status in ('0','1') GROUP BY parent_id ";
        List<Record> continuedList=Db.find(continuedSql,continuedParams.toArray());
        for(Record continued:continuedList){
            Record record=recordMap.get(continued.getStr("parent_id"));
            record.set("checkout_time",continued.getDate("maxCheckout"));
        }


        if(returnList.size()>0){
            for(String key:recordMap.keySet()){
                Record record=recordMap.get(key);
                if("book".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("book_start_time"));
                    record.set("end",record.getDate("book_end_time"));
                }else if ("stayin".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("checkin_time"));
                    record.set("end",record.getDate("checkout_time"));
                }else if("retreat".equals(record.getStr("checkin_status"))){
                    record.set("start",record.getDate("checkin_time"));
                    record.set("end",record.getDate("real_checkout_time"));
                }
                Date start=record.getDate("start");
                Date end=record.getDate("end");

                Date calStart=null;
                Date calEnd=null;
                if(DateUtils.compareDays(start,DateUtils.parseDate(startDate+" 00:00:00"))<0){
                    calStart=DateUtils.parseDate(startDate+" 00:00:00");
                }else{
                    calStart=start;
                    calStart=DateUtils.parseDate(DateUtils.formatDate(calStart,"yyyy-MM-dd")+" 00:00:00");
                }

                if(DateUtils.compareDays(end,DateUtils.parseDate(endDate+" 23:59:59"))>0){
                    calEnd=DateUtils.parseDate(endDate+" 24:59:59");
                }else{
                    calEnd=end;
                    calEnd=DateUtils.parseDate(DateUtils.formatDate(calEnd,"yyyy-MM-dd")+" 23:59:59");
                }
                if("month".equals(queryType)) {
                	long days=(calEnd.getTime()-calStart.getTime())/(86400000);
                	record.set("checkinDays",days);
                }else {
                	record.set("checkinDays",1);
                }
            }
            //returnList.add(totalRecord);
        }
        MainBase mainBase=mainBaseService.findById(baseId);
        String[] title={"客户渠道","入住人类型","预定号","入住号","入住人","随行人数","入住状态","是否换住","床位号","是否包房","预定/入住开始结束时间","入住天数","会员卡","会员类型名称","扣卡天数","扣卡金额","扣卡积分","结算状态"};
        String fileName=mainBase.getBaseName()+yearMonth+"入住单明细.xls";
        String sheetName = "入住单明细";

        String[][] content=new String[returnList.size()][title.length];
        for(int i=0;i<returnList.size();i++){
            Record record=returnList.get(i);

            if("MeiTuan".equals(record.getStr("customer_channel"))){
                content[i][0]="美团";
            }else if("XieCheng".equals(record.getStr("customer_channel"))){
                content[i][0]="携程";
            }else{
                content[i][0]="会员";
            }
            content[i][1]=record.getStr("checkinType");
            content[i][2]=record.getStr("book_no");
            content[i][3]=record.getStr("checkin_no");
            content[i][4]=record.getStr("name");
            content[i][5]=record.getStr("followCount");
            if("book".equals(record.getStr("checkin_status"))){
                content[i][6]="预定";
            }else if("stayin".equals(record.getStr("checkin_status"))){
                content[i][6]="在住";
            }else if("retreat".equals(record.getStr("checkin_status"))){
                content[i][6]="退住";
            }else{
                content[i][6]="";
            }
            if("1".equals(record.getStr("is_change_bed"))){
                content[i][7]="是";
            }else{
                content[i][7]="否";
            }

            content[i][8]=record.getStr("bedName");
            if("1".equals(record.getStr("is_private_room"))){
                content[i][9]="是";
            }else{
                content[i][9]="否";
            }

            content[i][10]=DateUtils.formatDate(record.getDate("start"),"yyyy-MM-dd")+"至"+DateUtils.formatDate(record.getDate("end"),"yyyy-MM-dd");
            content[i][11]=record.getStr("checkinDays");
            content[i][12]=record.getStr("card_number");
            content[i][13]=record.getStr("cardType");
            content[i][14]=record.getStr("sumTimes");
            content[i][15]=record.getStr("sumAmount");
            content[i][16]=record.getStr("sumIntegrals");
            if("1".equals(record.getStr("settleStatus"))){
                content[i][17]="已结算";
            }else{
                content[i][17]="未结算";
            }

        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void allocationIntegral(){

        finaExpenseRecordDetailService.allocationIntegral();
        renderJson("ok");
    }

}
