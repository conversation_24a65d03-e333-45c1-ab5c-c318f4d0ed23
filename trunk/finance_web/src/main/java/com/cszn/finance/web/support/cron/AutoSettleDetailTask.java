package com.cszn.finance.web.support.cron;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.fina.FinaSettleDetailService;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.entity.enums.CheckinType;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaSettleDetail;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * 自动结算月结账单
 */
@Cron("30 10 1 * *")
public class AutoSettleDetailTask  implements Runnable{

    private static Logger logger = LoggerFactory.getLogger(AutoSettleDetailTask.class);
    @Inject
    FinaSettleDetailService finaSettleDetailService;
    @Inject
    FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    FinaMembershipCardService finaMembershipCardService;
    @Inject
    MainCardDeductSchemeService mainCardDeductSchemeService;

    @Override
    public void run() {
        String dateStr = DateUtils.formatDate(new Date(), "yyyy-MM-dd");

        List<Record> recordList = Db.find("select expense_id from fina_settle_detail where settle_type='month_settle' " +
                "and create_time BETWEEN ? and ? and del_flag='0' and settle_status='0' ", dateStr + " 00:00:00", dateStr + DateUtils.formatDate(new Date(), " HH:mm:ss"));
        Set<String> idSet=new HashSet<>();
        for (Record record : recordList) {
            idSet.add(record.getStr("expense_id"));
        }
        for (String expenseId : idSet) {
            List<Record> finaSettleList=finaSettleDetailService.findSettleDetailList(expenseId);
            FinaExpenseRecord er = finaExpenseRecordService.findById(expenseId);
            if(!CheckinType.Member.getKey().equals(er.getCheckinType())){
                continue;
            }
            try {
                for (Record record : finaSettleList) {
                    if("0".equals(record.getStr("settleStatus"))){
                        //获取未结算中创建时间最小的记录
                        String minDateId=Db.queryStr("select id from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' order by end_time limit 1 ",expenseId);
                        if(!minDateId.equals(record.getStr("id"))){
                            logger.info(er.getCheckinNo()+"月结自动结算异常：请按照明细生成时间顺序来结算"+record.getStr("id"));
                            break;
                        }
                        FinaSettleDetail detail=finaSettleDetailService.findById(record.getStr("id"));
                        FinaMembershipCard card = finaMembershipCardService.findCardByCardNumber(detail.getCardNumber());
                        MainCardDeductScheme deductScheme = mainCardDeductSchemeService.findById(card.getDeductSchemeId());
                        if(deductScheme==null){
                            deductScheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
                        }
                        if(DeductType.deductAmount.getKey().equals(deductScheme.getDeductWay())){
                            break;
                        }
                        if(detail.getActualAmount()==null){
                            detail.setActualAmount(0.0);
                        }
                        if(detail.getActualIntegrals()==null){
                            detail.setActualIntegrals(0.0);
                        }
                        if(detail.getActualTimes()==null){
                            detail.setActualTimes(0.0);
                        }
                        if(detail.getActualPoints()==null){
                            detail.setActualPoints(0.0);
                        }

                        Map<String,Object> map=finaSettleDetailService.settleDateilRecord(
                                detail,er,record.getStr("settleType"),detail.getActualTimes(),detail.getActualAmount(),
                                detail.getActualPoints(),detail.getActualIntegrals(),null, "2CFA83C2-CA2E-4B1F-BA1A-1285354C2797"
                        );
                        if((boolean)map.get("flag")){
                            logger.info(er.getCheckinNo()+"月结自动结算成功"+record.getStr("id"));
                        }else{
                            logger.info(er.getCheckinNo()+"月结自动结算异常"+record.getStr("id")+" "+map.get("msg"));
                            break;
                        }
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
                logger.error(er.getCheckinNo()+"月结自动结算报错", e);
            }
        }
    }

}
