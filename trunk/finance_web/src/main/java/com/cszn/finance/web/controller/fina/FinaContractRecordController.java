package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaContractRecordService;
import com.cszn.integrated.service.api.main.MainContractTypeService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.entity.enums.RechargeWay;
import com.cszn.integrated.service.entity.fina.FinaContractRecord;
import com.cszn.integrated.service.entity.main.MainContractType;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import io.netty.handler.codec.MessageToByteEncoder;

import java.util.List;

@RequestMapping(value="/fina/contract", viewPath="/modules_page/finance/fina/contract")
public class FinaContractRecordController extends BaseController {

    @Inject
    private FinaContractRecordService finaContractRecordService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private MainContractTypeService mainContractTypeService;


    public void index(){
        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            FinaContractRecord contractRecord=finaContractRecordService.findById(id);
            setAttr("model",contractRecord);

        }
        List<MainMembershipCardType> cardTypeList=mainMembershipCardTypeService.findList(new MainMembershipCardType());

        List<MainContractType> contractTypeList= mainContractTypeService.findList();

        Integer row= Db.queryInt("select count(id) from fina_contract_record ");
        if(row==null){
            row=0;
        }

        setAttr("row", row+1);
        setAttr("cardTypeList",cardTypeList);
        setAttr("contractTypeList",contractTypeList);
        render("form.html");
    }

    public void pageList(){
        FinaContractRecord contractRecord= getBean(FinaContractRecord.class,"",true);

        Page<FinaContractRecord> pageList = finaContractRecordService.findPageList(getParaToInt("page"), getParaToInt("limit"), contractRecord);

        renderJson(new DataTable<FinaContractRecord>(pageList));
    }

    public void saveContractRecord(){
        FinaContractRecord contractRecord= getBean(FinaContractRecord.class,"",true);

        boolean flag=finaContractRecordService.saveContractRecord(contractRecord, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
