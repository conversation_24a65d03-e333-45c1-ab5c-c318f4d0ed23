package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaDayDeductStatisticService;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description 财务扣卡统计图表
 * <AUTHOR>
 * @Date 2019/8/6
 **/
@RequestMapping(value="/fina/dataAnalysis", viewPath="/modules_page/finance/fina/dataAnalysis")
public class DataAnalysisController extends BaseController {

    @Inject
    private FinaDayDeductStatisticService finaDayDeductStatisticService;


    /**
     * 跳转财务扣卡统计界面
     */
    public void index(){
        render("dataAnalysisIndex.html");
    }


    /**
     * 获取时间维度列表
     */
    @Clear(LogInterceptor.class)
    public void getTimeDataList(){
        String timeType = getPara("timeType");
        Map<String, Object> result = new HashMap<String, Object>();
        Map<String,Object> map = finaDayDeductStatisticService.getChartData(timeType,null);
        if(map == null){
            result.put("code", "0");
            result.put("msg", "获取成功");
            result.put("data",new ArrayList<>());
        }else{
            result.put("code", "0");
            result.put("msg", "获取成功");
            result.put("data",map.get("list"));
        }
        renderJson(result);
    }


    /**
     * 获取基地维度列表
     */
    @Clear(LogInterceptor.class)
    public void getBaseDataList(){
        String timeType = getPara("timeType");
        Map<String, Object> result = new HashMap<String, Object>();
        Map<String,Object> map = finaDayDeductStatisticService.getChartDataForBase(timeType,null);
        if(map == null){
            result.put("code", "0");
            result.put("msg", "获取成功");
            result.put("data",new ArrayList<>());
        }else{
            result.put("code", "0");
            result.put("msg", "获取成功");
            result.put("data",map.get("list"));
        }
        renderJson(result);
    }


    /**
     * 按时间维度展示扣卡数据
     */
    @Clear(LogInterceptor.class)
    public void getTimeSeries(){
        String timeType = getPara("timeType");
        String urlType = getPara("urlType");//接口类型
        Map<String, Object> datas = new HashMap<String, Object>();
        datas.put("flag", Boolean.TRUE);
        datas.put("msg", "加载成功");
        Map<String,Object> timeMap = null;
        try {
            timeMap = finaDayDeductStatisticService.getChartData(timeType,urlType);
        } catch (Exception e) {
            e.printStackTrace();
            datas.put("result", Boolean.FALSE);
            datas.put("msg", "加载失败");
        }
        datas.put("measureTimeArray", timeMap.get("measureTimeArray"));
        datas.put("measureJsonData", timeMap.get("measureJsonData"));
        renderJson(datas);
    }


    /**
     * 按基地维度展示扣卡数据
     */
    @Clear(LogInterceptor.class)
    public void getBaseSeries(){
        String timeType = getPara("timeType");
        String urlType = getPara("urlType");//接口类型
        Map<String, Object> datas = new HashMap<String, Object>();
        datas.put("flag", Boolean.TRUE);
        datas.put("msg", "加载成功");
        Map<String,Object> baseMap = null;
        try {
            baseMap = finaDayDeductStatisticService.getChartDataForBase(timeType,urlType);
        } catch (Exception e) {
            e.printStackTrace();
            datas.put("result", Boolean.FALSE);
            datas.put("msg", "加载失败");
        }
        datas.put("measureTimeArray", baseMap.get("measureTimeArray"));
        datas.put("measureJsonData", baseMap.get("measureJsonData"));
        renderJson(datas);

    }
}
