/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import java.util.Date;
import java.util.List;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardAccountService;
import com.cszn.integrated.service.api.fina.FinaCardCollectService;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.fina.FinaCardAccount;
import com.cszn.integrated.service.entity.fina.FinaCardCollect;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2022年9月08日
 * 收款记录控制器
 * MemberController
 */
@RequestMapping(value="/fina/collect", viewPath="/modules_page/finance/fina/collect")
public class FinaCollectController extends BaseController {

	@Inject
    private FinaCardCollectService finaCardCollectService;
	@Inject
    private MainBranchOfficeService mainBranchOfficeService;
	@Inject
    private FinaCardAccountService finaCardAccountService;
	@Inject
	private UserService userService;
	
	public void index(){
		List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
		setAttr("branchOfficeList", branchOfficeList);
		render("collectIndex.html");
	}
	
	@Clear(LogInterceptor.class)
	public void pageTable(){
		FinaCardCollect model = getBean(FinaCardCollect.class, "", true);
		Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
//		if(StrKit.notBlank(model.getDelFlag())) {
//			columns.eq("del_flag", model.getDelFlag());
//		}
		if(StrKit.notBlank(model.getCustomerName())) {
			columns.likeAppendPercent("customer_name", model.getCustomerName());
		}
		if(StrKit.notBlank(model.getCustomerIdcard())) {
			columns.eq("customer_idcard", model.getCustomerIdcard());
		}
		if(StrKit.notBlank(model.getBranchOfficeId())) {
			columns.eq("branch_office_id", model.getBranchOfficeId());
		}
		Page<FinaCardCollect> modelPage = finaCardCollectService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_time desc");
		renderJson(new DataTable<FinaCardCollect>(modelPage));
	}
	
	/**
     * 添加页面方法
     */
    public void add() {
    	final FinaCardCollect model = getBean(FinaCardCollect.class, "", true);
    	List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
    	List<FinaCardAccount> accountList = finaCardAccountService.findList(null);
    	List<User> userList = userService.findUnlockUserList();
		setAttr("branchOfficeList", branchOfficeList);
		setAttr("accountList", accountList);
		setAttr("userList", userList);
    	setAttr("commonUpload",Global.commonUpload);
		setAttr("model", model);
        render("collectForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
    	String id = getPara("id");
    	FinaCardCollect model = finaCardCollectService.findById(id);
    	List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
    	List<FinaCardAccount> accountList = finaCardAccountService.findList(null);
    	List<User> userList = userService.findUnlockUserList();
		setAttr("branchOfficeList", branchOfficeList);
		setAttr("accountList", accountList);
		setAttr("userList", userList);
		setAttr("commonUpload",Global.commonUpload);
		setAttr("model", model);
        render("collectForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
    	FinaCardCollect model = getBean(FinaCardCollect.class,"",true);
    	boolean flag = false;
    	if(StrKit.isBlank(model.getId())){
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.save();
    	}else{
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    	}
		if(flag){
			renderJson(Ret.ok("msg", "保存成功"));
		}else{
			renderJson(Ret.fail("msg", "保存失败"));
		}
    }
    
    /**
     * 作废方法
     */
    public void del(){
    	FinaCardCollect model = getBean(FinaCardCollect.class,"",true);
    	model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateTime(new Date());
		if(model.update()){
			renderJson(Ret.ok("msg", "作废成功"));
		}else{
			renderJson(Ret.fail("msg", "作废失败"));
		}
    }
}
