package com.cszn.finance.web.support.cron;

import java.util.List;

import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.components.schedule.annotation.Cron;

@Cron("30 0 * * *")
public class CardExpireTask implements Runnable {

    @Override
    public void run() {
        String sql="select id from fina_membership_card where expire_flag='0' and expire_date<=now() ";
        List<Record> cardList= Db.find(sql);
        if(cardList==null || cardList.size()==0){
            return;
        }
        for(Record card : cardList){
            final String id = card.getStr("id");
            FinaMembershipCard model = new FinaMembershipCard();
            model.setId(id);
            model.setExpireFlag("1");
            model.update();
        }
    }
}
