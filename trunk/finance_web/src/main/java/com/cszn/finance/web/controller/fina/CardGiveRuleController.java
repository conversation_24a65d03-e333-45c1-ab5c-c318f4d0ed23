package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardGiveRuleDetailService;
import com.cszn.integrated.service.api.fina.FinaCardGiveRuleService;
import com.cszn.integrated.service.entity.fina.FinaCardGiveRule;
import com.cszn.integrated.service.entity.fina.FinaCardGiveRuleDetail;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.util.*;


/**
 * @Description 会员卡赠送规则管理
 * <AUTHOR>
 * @Date 2019/5/7
 **/
@RequestMapping(value="/fina/rule", viewPath="/modules_page/finance/fina/ruleManager")
public class CardGiveRuleController extends BaseController {


	@Inject
    private FinaCardGiveRuleService finaCardGiveRuleService;
	@Inject
    private FinaCardGiveRuleDetailService finaCardGiveRuleDetailService;


    /**
     * 跳转会员卡赠送规则界面
     */
    public void index(){
        render("ruleIndex.html");
    }


    /**
     * 根据规则和赠送周期查询规则分页
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        FinaCardGiveRule cardGiveRule = getBean(FinaCardGiveRule.class,"",true);
        Page<FinaCardGiveRule> page = finaCardGiveRuleService.findList(cardGiveRule,getParaToInt("page"),getParaToInt("limit"));
        renderJson(new DataTable<FinaCardGiveRule>(page));
    }


    /**
     * 删除主规则及其规则明细
     */
    public void delete(){

        String id = getPara("id");
        boolean flag = finaCardGiveRuleService.delRuleAndDetail(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 跳转到规则界面 新增/修改
     */
    public void form(){
        String id = getPara("id");

        FinaCardGiveRule rule = null;
        List<FinaCardGiveRuleDetail> detailList = new ArrayList<>();
        if(StringUtils.isNotBlank(id)){
            rule = finaCardGiveRuleService.findById(id);
        }
        setAttr("rule",rule);
        render("ruleForm.html");
    }


    /**
     * 新增/修改主规则及其规则明细
     */
    public void save(){
        final int paramCount = getParaToInt("paramCount");
        FinaCardGiveRule cardGiveRule = getBean(FinaCardGiveRule.class,"rule",true);
        List<FinaCardGiveRuleDetail> detailList = null;
        if(paramCount > 0){
            detailList = new ArrayList<>(paramCount);
            for (int i = 1; i <= paramCount; i++) {
                final FinaCardGiveRuleDetail detail = getBean(FinaCardGiveRuleDetail.class,"itemDetailList["+i+"]");
                if(detail != null && detail.getTimeSeq() != null) {
                    detailList.add(detail);
                }
            }
        }
        boolean flag = finaCardGiveRuleService.saveRuleAndDetail(cardGiveRule,detailList,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     *通过规则名称，周期，规则id查询规则详情
     */
    @Clear(LogInterceptor.class)
    public void getRuleDetailByOption(){

        FinaCardGiveRule cardGiveRule = getBean(FinaCardGiveRule.class,"",true);
        Integer flag = getParaToInt("flag");
        if(flag == null) {
            if (StringUtils.isBlank(cardGiveRule.getName()) || StringUtils.isBlank(cardGiveRule.getCycle())) {
                renderJson(Ret.ok("data", new ArrayList<>()));
                return;
            }
        }
        if(StringUtils.isBlank(cardGiveRule.getId())){
            renderJson(Ret.ok("data", new ArrayList<>()));
            return;
        }
        List<FinaCardGiveRuleDetail> detailList = finaCardGiveRuleDetailService.getRuleDetails(cardGiveRule);
        renderJson(Ret.ok("data",detailList));
    }


    /**
     * 删除规则详情
     */
    public void delRuleDetail(){

        String id = getPara("id");
        boolean flag = finaCardGiveRuleDetailService.delRuleDetail(id);
        if(flag){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 批量删除
     */
    public void batchDel(){
        String ruleData =  getPara("ruleData");
        List<FinaCardGiveRule> list = JSONArray.parseArray(ruleData,FinaCardGiveRule.class);
        boolean flag = finaCardGiveRuleService.batchDelRule(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量作废成功"));
        }else{
            renderJson(Ret.fail("msg", "批量作废失败"));
        }

    }


    public static void main(String[] args) {

        try {
            Map<String,Integer> map=new HashMap<>();
            Map<String,Integer> hanmap=new HashMap<>();
            int j=0;

            OutputStream out = null;
            // 获取总列数
            // 读取Excel文档
            File finalXlsxFile = new File("D:\\backup\\新建 XLSX 工作表.xlsx");
            Workbook workBook = new XSSFWorkbook(new FileInputStream(finalXlsxFile));
            // sheet 对应一个工作页
            Sheet sheet = workBook.getSheetAt(0);
            /**
             * 删除原有数据，除了属性列
             */

            String str="[{\"0FF69098-6246-4B0C-91FE-D11D9330DDD6\":\"亲子套房\"},{\"1E9BAD7A-6735-4286-9E1D-84DFAF0347D8\":\"家庭套房\"},{\"5DC0D788-7FE9-49A8-8F9D-8BDA93E892F6\":\"豪华标单\"},{\"64EF090D-3EED-43B7-A032-28BFDFA1445B\":\"四人房\"},{\"6FCA8276-BEA0-46EC-9688-C9575D9D36D4\":\"标准套房\"},{\"7F5C5D50-3759-418A-803C-B57640E8589C\":\"行政标单\"},{\"81640D0A-DB08-4C23-83C5-DB5E028F858A\":\"工作间\"},{\"92B8C02D-E379-456B-BA22-26B3ECDD5523\":\"豪华标双\"},{\"9C84DD20-7A86-40CB-8BDC-3A55DFEE3833\":\"行政套房\"},{\"A15C3F18-CC6F-4C1D-8BD2-43B8AE18DEF3\":\"行政标双\"},{\"A6284EC7-1500-4EAC-AD7C-5BD7389CFC11\":\"豪华套房\"},{\"A64A031C-2210-4B7C-B869-801AAC1EFEAF\":\"别墅\"},{\"B0759EDA-5D23-4FC7-AF5F-E272BC3589C8\":\"三人房\"},{\"B0E390B5-4C86-4365-9D3B-36808A263ED9\":\"贵宾套房\"},{\"D7F8E02A-F12A-4036-B2FD-5FB8A9C25888\":\"单人房\"}]";
            JSONArray jsonArray= JSON.parseArray(str);
            Map<String,String> erjiMap=new HashMap<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject=jsonArray.getJSONObject(i);
                for(String key:jsonObject.keySet()){
                    erjiMap.put(jsonObject.getString(key),key);
                }

            }

            System.out.println(JSON.toJSONString(erjiMap));

            Set<String> stringSet=new HashSet<>();
            int rowNumber = sheet.getLastRowNum();    // 第一行从0开始算
            for (int i = 1; i <= rowNumber; i++) {
                Row row = sheet.getRow(i);
                Cell cell=row.getCell(1);
                Cell cell2=row.getCell(2);

                stringSet.add(cell.getStringCellValue());
            }
            System.out.println(JSON.toJSONString(stringSet));


            // 创建文件输出流，输出电子表格：这个必须有，否则你在sheet上做的任何操作都不会有效
            out =  new FileOutputStream("D:\\backup\\新建 XLS 工作表 (99).xlsx");
            workBook.write(out);
            try {
                if(out != null){
                    out.flush();
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}
