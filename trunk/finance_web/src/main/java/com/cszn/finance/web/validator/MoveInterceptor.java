package com.cszn.finance.web.validator;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.core.Controller;
import com.jfinal.kit.StrKit;
import io.jboot.Jboot;
import io.jboot.support.redis.JbootRedis;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.Cookie;
import java.util.HashMap;
import java.util.Map;

public class MoveInterceptor implements Interceptor {

    private JbootRedis jbootRedis= Jboot.getRedis();
    Logger logger= LoggerFactory.getLogger(MoveInterceptor.class);

    @Override
    public void intercept(Invocation inv) {
        try {
            Controller controller=inv.getController();
            if(controller!=null){
                //String token=controller.getCookie("token");
                //String header=controller.getHeader("token");
                String token=controller.getPara("token");
                Map<String,Object> errorMap=new HashMap<>();
                errorMap.put("code","10003");
                errorMap.put("msg","请重新登录");
                if(StrKit.isBlank(token)){
                    controller.renderJson(errorMap);
                    return;
                }
                byte[] bytes=jbootRedis.get("token_"+token);
                if(bytes==null || bytes.length==0){
                    controller.renderJson(errorMap);
                    return;
                }
                inv.invoke();
                jbootRedis.expire("token_"+token,60*60*24);
            }else{
                inv.invoke();
            }
        }catch (Exception e){
            logger.error("移动验证登录拦截器异常"+e);
        }
    }
}
