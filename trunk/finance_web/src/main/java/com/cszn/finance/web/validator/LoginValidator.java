package com.cszn.finance.web.validator;

import com.cszn.integrated.base.web.base.JsonValidator;
import com.jfinal.core.Controller;

/**
 * 登录校验
 * <AUTHOR>
 *
 */
public class LoginValidator extends JsonValidator {

    @Override
    protected void validate(Controller c) {
        validateString("loginName", 4, 16, "用户名格式不正确");
        validateString("password", 6, 20, "密码格式不正确");
        validateString("capval", 4, 4, "验证码格式不正确");
        validateCaptcha("capval", "验证码不正确");
    }
}
