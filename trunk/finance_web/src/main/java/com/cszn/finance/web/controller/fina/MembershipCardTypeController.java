package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaConditionRechargeSchemeService;
import com.cszn.integrated.service.api.fina.FinaContractRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardTypeConfService;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.entity.enums.SchemeType;
import com.cszn.integrated.service.entity.fina.FinaMembershipCardTypeConf;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainCardGiveScheme;
import com.cszn.integrated.service.entity.main.MainCardYearLimit;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Descript 会员卡类型
 * <AUTHOR>
 * @Date 2019-04-09
 */
@RequestMapping(value="/fina/cardType", viewPath="/modules_page/finance/fina/membershipCardType")
public class MembershipCardTypeController extends BaseController {

    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private FinaMembershipCardTypeConfService finaMembershipCardTypeConfService;
    @Inject
    private MainCardGiveSchemeService mainCardGiveSchemeService;
    @Inject
	private MainCardDeductSchemeService mainCardDeductSchemeService;
    @Inject
    private MainCardYearLimitService mainCardYearLimitService;
    @Inject
	private FinaContractRecordService finaContractRecordService;
	@Inject
	private MainBaseBedMarkupSchemeService mainBaseBedMarkupSchemeService;
	@Inject
	private FinaConditionRechargeSchemeService finaConditionRechargeSchemeService;

    /**
     * 跳转会员卡类型默认配置界面
     */
    public void cardTypeIndex(){
    	render("cardTypeIndex.html");
    }

    /**
	 * 分页获取会员卡类别列表
	 */
	@Clear(LogInterceptor.class)
	public void cardTypePage(){
		MainMembershipCardType type = getBean(MainMembershipCardType.class,"",true);
		Page<MainMembershipCardType> page = mainMembershipCardTypeService.findListPage(getParaToInt("page"),getParaToInt("limit"),type);
		renderJson(new DataTable<MainMembershipCardType>(page));
	}

    /**
     * 会员卡添加、修改表单页面
     */
	public void cardTypeForm(){
	    String id=getPara("id");
	    MainMembershipCardType type = null;
		if (StringUtils.isNotBlank(id)) {
			type = mainMembershipCardTypeService.get(id);
		}

		setAttr("type", type);
        setAttr("contractRecordList",finaContractRecordService.contractRecordList());
		setAttr("markupSchemeList",mainBaseBedMarkupSchemeService.findAllList());

		render("cardTypeForm.html");
    }

    /**
     * 会员卡类别保存
     */
    public void save(){
    	MainMembershipCardType type = getBean(MainMembershipCardType.class,"type",true);
		if(type.getCardMin() != null && type.getCardMax() != null){
			if(type.getCardMin().length() != type.getCardMax().length()){
				renderJson(Ret.fail("msg", "会员卡号段位数必须一致"));
				return;
			}
            if(Integer.parseInt(type.getCardMin()) >= Integer.parseInt(type.getCardMax())){
				renderJson(Ret.fail("msg", "会员卡号最小值不可大于等于最大值"));
                return;
			}
		}
		if(StrKit.notBlank(type.getCardType()) && mainMembershipCardTypeService.cardTypeIsExist(type.getId(),type.getCardType(),null)!=null){
			renderJson(Ret.fail("msg", "会员卡类别不可重复"));
			return;
		}
		if(StrKit.notBlank(type.getCardPrefix()) && mainMembershipCardTypeService.cardTypeIsExist(type.getId(),null,type.getCardPrefix())!=null){
			renderJson(Ret.fail("msg", "会员卡前缀不可重复"));
			return;
		}
		boolean flag = mainMembershipCardTypeService.saveMainMembershipCardType(type,AuthUtils.getUserId());
		if (flag){
			renderJson(Ret.ok("msg", "保存成功"));
		}else{
			renderJson(Ret.fail("msg", "保存失败"));
		}
    }

    /**
     * 跳转类型配置界面
     */
    public void cardTypeConfig(){
    	final String cardTypeId = getPara("id");
        if(StrKit.notBlank(cardTypeId)){
            FinaMembershipCardTypeConf model = finaMembershipCardTypeConfService.getByCardTypeId(cardTypeId);
            MainMembershipCardType cardType = mainMembershipCardTypeService.findById(cardTypeId);
            if(model==null){
                model = new FinaMembershipCardTypeConf();
                model.setCardTypeId(cardTypeId);
            }
            setAttr("model",model);
            setAttr("cardType",cardType);
        }
        List<MainCardDeductScheme> sojournSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.SOJOURN_SCHEME.getKey());
        List<MainCardDeductScheme> longSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.LONG_SCHEME.getKey());
        List<MainCardYearLimit> cardYearLimitList=mainCardYearLimitService.findCardYearLimitList();
        setAttr("sojournSchemeList",sojournSchemeList);
        setAttr("longSchemeList",longSchemeList);
        setAttr("cardYearLimitList",cardYearLimitList);
		List<MainCardGiveScheme> giveSchemeList = mainCardGiveSchemeService.findListOnUse("1");
		List<MainCardGiveScheme> rechargeSchemeList = mainCardGiveSchemeService.findListOnUse("2");

		setAttr("giveSchemeList",giveSchemeList);
		setAttr("rechargeSchemeList",rechargeSchemeList);
		setAttr("conditionRechargeSchemeList",finaConditionRechargeSchemeService.getConditionRechargeSchemeList());
        render("cardTypeConfig.html");
    }
    
    @Clear(LogInterceptor.class)
    public void getCardTypeConfig(){
    	final String cardTypeId = getPara("id");
        FinaMembershipCardTypeConf model = finaMembershipCardTypeConfService.getByCardTypeId(cardTypeId);
        if(model!=null){
        	MainMembershipCardType cardType = mainMembershipCardTypeService.get(cardTypeId);
        	if(cardType!=null){
        		model.setRemark(cardType.getRemark());
        	}
        }
        renderJson(Ret.ok("msg", "加载成功!").set("cardTypeConf", model));
    }

    /**
     * 类型配置保存
     */
    @Clear(LogInterceptor.class)
    public void configSave(){
    	FinaMembershipCardTypeConf cardTypeConfig = getBean(FinaMembershipCardTypeConf.class, "config", true);
    	if(StrKit.isBlank(cardTypeConfig.getId())){
    		cardTypeConfig.setCreateBy(AuthUtils.getUserId());
            cardTypeConfig.setUpdateBy(AuthUtils.getUserId());
    	}else{
    		cardTypeConfig.setUpdateBy(AuthUtils.getUserId());
    	}
        renderJson(finaMembershipCardTypeConfService.saveConf(cardTypeConfig));
    }

	/**
	 * 删除
	 */
	public void delete(){
		String id = getPara("id");
		String userId = AuthUtils.getUserId();
		boolean bl = mainMembershipCardTypeService.delete(id,userId);
		if (bl) {
			renderJson(Ret.ok("msg", "作废成功"));
		} else {
			renderJson(Ret.fail("msg", "作废失败"));
		}
	}
}
