package com.cszn.finance.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.api.fina.FinaConsumeResultService;
import com.cszn.integrated.service.entity.fina.FinaConsumeResult;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.Jboot;
import io.jboot.components.schedule.annotation.Cron;

import java.util.ArrayList;
import java.util.List;

//@Cron("5 0 * * *")
public class ConsumeTask implements Runnable {

    @Inject
    FinaConsumeResultService finaConsumeResultService;

    //private final static String URL= Jboot.configValue("orgBillPushUrl");

    @Override
    public void run() {
        /*JSONArray array=new JSONArray();
        array.add("201354444");
        array.add("456952145");
        String str = HttpClientsUtils.httpPostRaw("http://localhost:8280/pension/csapi/settleMainConsumeResult", JSON.toJSONString(array),null,"UTF-8");
        System.out.println(str);*/
        //pushConsumeResult(1,100);
    }

    /*public void pushConsumeResult(Integer pageNumber,Integer pageSize){
        Page<FinaConsumeResult> consumeResultPage=finaConsumeResultService.consumeResultPage(pageNumber,pageSize);
        List<FinaConsumeResult> consumeResultList=consumeResultPage.getList();
        if(consumeResultList.size()==0){
            return;
        }
        JSONArray array=new JSONArray();
        for(FinaConsumeResult result:consumeResultList){
            array.add(result.getConsumeNo());
        }

        String resultStr=HttpClientsUtils.httpPostRaw(URL, JSON.toJSONString(array),null,"UTF-8");
        System.out.println(resultStr);
        if(resultStr.startsWith("{") && resultStr.endsWith("}")){
            JSONObject jsonObject=JSON.parseObject(resultStr);
            if(jsonObject.containsKey("code") && "0".equals(jsonObject.getString("code"))){
                JSONArray noArray=jsonObject.getJSONArray("data");
                if(noArray!=null && noArray.size()>0){
                    String str="";
                    List<String> params=new ArrayList<>();
                    for(int i=0;i<noArray.size();i++){
                        str+="?,";
                        params.add(noArray.getString(i));
                    }
                    str=str.substring(0,str.length()-1);
                    String sql="update fina_consume_result set push_status='0' where consume_no in ("+str+") ";

                    Db.update(sql,params.toArray());
                }
            }
        }

        pageNumber++;
        if(pageNumber<=consumeResultPage.getTotalPage()){
            pushConsumeResult(pageNumber,pageSize);
        }

    }*/
}
