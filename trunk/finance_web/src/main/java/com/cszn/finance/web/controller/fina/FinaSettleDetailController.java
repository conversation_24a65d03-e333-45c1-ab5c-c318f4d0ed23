package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.cron.CardSettleTask;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardTransactionsService;
import com.cszn.integrated.service.api.fina.FinaExpenseRecordService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.fina.FinaSettleDetailService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.enums.CheckinType;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.fina.FinaExpenseRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaSettleDetail;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.sql.SQLException;
import java.util.*;

@RequestMapping(value = "/fina/settleDetail",viewPath = "/modules_page/finance/fina/orgSettle/")
public class FinaSettleDetailController extends BaseController {

    @Inject
    private FinaSettleDetailService finaSettleDetailService;
    @Inject
    private FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    private CardSettleTask cardSettleTask;
    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private SmsSendRecordService smsSendRecordService;
    @Inject
    private FinaCardTransactionsService finaCardTransactionsService;
    
    /**
     * 跳转到批量结算页面
     */
    @Clear(LogInterceptor.class)
    public void batchSettle(){
    	List<MainBase> baseList = mainBaseService.findBaseList();
        setAttr("baseList",baseList);
    	render("batchSettle.html");
    }
    
    public void batchSettlePage(){
        String baseId = getPara("baseId");
        String yearMonth=getPara("yearMonth");
        String cardNumber=getPara("cardNumber");
        String settleType=getPara("settleType");
        Page<Record> recordPage=finaSettleDetailService.batchSettlePage(getParaToInt("page"),getParaToInt("limit"),baseId,yearMonth,cardNumber,settleType);
        renderJson(new DataTable<Record>(recordPage));
    }

    @Clear(LogInterceptor.class)
    public void findSettleDetailList(){
        String expenseId=getPara("id");
        List<Record> finaSettleList=finaSettleDetailService.findSettleDetailList(expenseId);
        renderJson(new DataTable<Record>(finaSettleList));
    }

    /**
     * 结算月明细
     */
    public void settleMonthDetail(){
        String expenseId=getPara("expenseId");
        String detailId=getPara("detailId");
        String actualTimes=getPara("actualTimes");
        String actualAmount=getPara("actualAmount");
        String actualPoints=getPara("actualPoints");
        String actualIntegrals=getPara("actualIntegrals");
        String settleRemark=getPara("settleRemark");

        FinaExpenseRecord er = finaExpenseRecordService.get(expenseId);
        if (er == null) {
            renderJson(Ret.fail("msg","账单记录不存在"));
            return;
        }
        /*if (StringUtils.isNotBlank(er.getSettleStatus()) && !"0".equals(er.getSettleStatus())) {
            renderJson(Ret.fail("msg","主账单记录不处于未结算状态，不可结算"));
            return;
        }*/
        if("1".equals(er.getDelFlag()) || "2".equals(er.getSettleStatus())){
            renderJson(Ret.fail("msg",er.getCheckinNo()+"入住单已被作废"));
            return;
        }
        //获取未结算中创建时间最小的记录
        String minDateId=Db.queryStr("select id from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' order by end_time limit 1 ",expenseId);
        if(!minDateId.equals(detailId)){
            renderJson(Ret.fail("msg","请按照明细生成时间顺序来结算"));
            return;
        }
        FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
        if("1".equals(detail.getSettleStatus())){
            renderJson(Ret.fail("msg","该记录已是结算状态，请勿重复结算"));
            return;
        }
        if(!"month_settle".equals(detail.getSettleType())){
            renderJson(Ret.fail("msg","该明细记录不是月结算类型"));
            return;
        }
        if(StrKit.isBlank(actualTimes)){
            actualTimes="0";
        }
        if(StrKit.isBlank(actualAmount)){
            actualAmount="0";
        }
        if(StrKit.isBlank(actualPoints)){
            actualPoints="0";
        }
        if(StrKit.isBlank(actualIntegrals)){
            actualIntegrals="0";
        }
        try {
            Map<String,Object> map=finaSettleDetailService.settleDateilRecord(
        		detail,er,"month_settle",Double.valueOf(actualTimes),Double.valueOf(actualAmount),
        		Double.valueOf(actualPoints),Double.valueOf(actualIntegrals),settleRemark, AuthUtils.getUserId()
    		);
            if((boolean)map.get("flag")){
                renderJson(Ret.ok("msg","结算成功"));
                return;
            }else{
                renderJson(Ret.fail("msg",map.get("msg")));
                return;
            }
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","程序异常"));
        }
    }

    /**
     * 结算退住明细
     */
    public void settleCheckoutDetail(){
        String expenseId=getPara("expenseId");
        String detailId=getPara("detailId");
        String actualTimes=getPara("actualTimes");
        String  actualAmount=getPara("actualAmount");
        String actualPoints=getPara("actualPoints");
        String actualIntegrals=getPara("actualIntegrals");
        String settleRemark=getPara("settleRemark");

        int count= Db.queryInt("select count(id) from fina_settle_detail where settle_type='month_settle' and del_flag='0'" +
                " and settle_status='0' and expense_id=? ",expenseId);
        if(count>0){
            renderJson(Ret.fail("msg","请先结算完月结明细再结算退住明细"));
            return;
        }

        FinaExpenseRecord er = finaExpenseRecordService.get(expenseId);
        if (er == null) {
            renderJson(Ret.fail("msg","账单记录不存在"));
            return;
        }
        /*if (StringUtils.isNotBlank(er.getSettleStatus()) && !"0".equals(er.getSettleStatus())) {
            renderJson(Ret.fail("msg","主账单记录不处于未结算状态，不可结算"));
            return;
        }*/
        if("1".equals(er.getDelFlag()) || "2".equals(er.getSettleStatus())){
            renderJson(Ret.fail("msg",er.getCheckinNo()+"入住单已被作废"));
            return;
        }

        //获取未结算中创建时间最小的记录
        String minDateId=Db.queryStr("select id from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' order by end_time limit 1 ",expenseId);
        if(!minDateId.equals(detailId)){
            renderJson(Ret.fail("msg","请按照明细生成时间顺序来结算"));
            return;
        }

        FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
        if("1".equals(detail.getSettleStatus())){
            renderJson(Ret.fail("msg","该记录为已结算状态，请勿重复结算"));
            return;
        }
        if(!"checkout_settle".equals(detail.getSettleType())){
            renderJson(Ret.fail("msg","该明细记录不是退住结算类型"));
            return;
        }
        if(StrKit.isBlank(actualTimes)){
            actualTimes="0";
        }
        if(StrKit.isBlank(actualAmount)){
            actualAmount="0";
        }
        if(StrKit.isBlank(actualPoints)){
            actualPoints="0";
        }
        if(StrKit.isBlank(actualIntegrals)){
            actualIntegrals="0";
        }
        try {
            Map<String,Object> map=finaSettleDetailService.settleDateilRecord(
        		detail,er,"checkout_settle",Double.valueOf(actualTimes),Double.valueOf(actualAmount),
        		Double.valueOf(actualPoints),Double.valueOf(actualIntegrals),settleRemark, AuthUtils.getUserId()
    		);
            if((boolean)map.get("flag")){
                renderJson(Ret.ok("msg","结算成功").set("expenseSettleStatus",(String)map.get("expenseSettleStatus")));
            }else{
                renderJson(Ret.fail("msg",map.get("msg")));
            }
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","程序异常"));
        }
    }

    
    /**
     * 批量结算保存
     */
    public void batchSettleSave(){
    	StringBuilder errorInfo = new StringBuilder();
    	final String batchDatas = getPara("batchDatas");
    	if(StrKit.notBlank(batchDatas)){
    		List<FinaSettleDetail> settleList = JSONArray.parseArray(batchDatas, FinaSettleDetail.class);
    		if(settleList!=null && settleList.size()>0) {
    		    boolean flag = Db.tx(new IAtom() {
                    @Override
                    public boolean run() throws SQLException {
                        try {
                            for(FinaSettleDetail settleDetail : settleList) {
                                final String detailId=settleDetail.getId();
                                final String expenseId=settleDetail.getExpenseId();
                                final String cardNumber=settleDetail.getCardNumber();
                                final String settleType=settleDetail.getSettleType();
                                Double actualTimes=settleDetail.getActualTimes();
                                Double actualAmount=settleDetail.getActualAmount();
                                Double actualPoints=settleDetail.getActualPoints();
                                Double actualIntegrals=settleDetail.getActualIntegrals();
                                final String settleRemark=settleDetail.getSettleRemark();

                                if("checkout_settle".equals(settleType)) {//退住结算
                                    int count= Db.queryInt("select count(id) from fina_settle_detail "
                                            + "where settle_type='month_settle' and del_flag='0' and settle_status='0' "
                                            + "and expense_id=? ",expenseId);
                                    if(count>0){
                                        errorInfo.append(cardNumber+"请先结算完月结明细再结算退住明细<br>");
                                        break;
                                    }
                                }
                                FinaExpenseRecord er = finaExpenseRecordService.get(expenseId);
                                if (er == null) {
                                    errorInfo.append(cardNumber+"账单记录不存在<br>");
                                    break;
                                }
                                /*if (StringUtils.isNotBlank(er.getSettleStatus()) && !"0".equals(er.getSettleStatus())) {
                                    errorInfo.append(cardNumber+"主账单记录不处于未结算状态，不可结算<br>");
                                    break;
                                }*/
                                if("1".equals(er.getDelFlag()) || "2".equals(er.getSettleStatus())){
                                    errorInfo.append(er.getCheckinNo()+"入住单已被作废");
                                    break;
                                }

                                //获取未结算中创建时间最小的记录
                                String minDateId=Db.queryStr("select id from fina_settle_detail where expense_id=? and del_flag='0' and settle_status='0' order by end_time limit 1 ",expenseId);
                                if(!minDateId.equals(detailId)){
                                    errorInfo.append(cardNumber+"请按照明细生成时间顺序来结算<br>");
                                    break;
                                }

                                FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
                                if("1".equals(detail.getSettleStatus())){
                                    errorInfo.append(cardNumber+"该明细记录已是结算完，请勿重复结算<br>");
                                    break;
                                }

                                if(actualTimes==null){
                                    actualTimes=0.0;
                                }
                                if(actualAmount==null){
                                    actualAmount=0.0;
                                }
                                if(actualPoints==null){
                                    actualPoints=0.0;
                                }
                                if(actualIntegrals==null){
                                    actualIntegrals=0.0;
                                }
                                synchronized (FinaSettleDetailController.class){
                                    Map<String,Object> map = finaSettleDetailService.settleDateilRecordNoSms(
                                        detail,er,settleType,actualTimes,actualAmount,actualPoints,
                                        actualIntegrals,settleRemark, AuthUtils.getUserId()
                                    );
                                    if(!(boolean)map.get("flag")){
                                        errorInfo.append(map.get("msg"));
                                        break;
                                    }
                                }
                            }
                            if(StrKit.notBlank(errorInfo.toString())) {
                            	return false;
                            }else {
                            	return true;
                            }
                        }catch (Exception e){
                            e.printStackTrace();
                            return false;
                        }

                    }
                });
    	        if(flag){
    	        	for (int j = 0; j < settleList.size(); j++) {
    	        		FinaSettleDetail settleDetail = settleList.get(j);
    	        		final String detailId=settleDetail.getId();
    	        		final String expenseId=settleDetail.getExpenseId();
    	        		final String cardNumber=settleDetail.getCardNumber();
    	        		Double actualTimes=settleDetail.getActualTimes();
                        Double actualAmount=settleDetail.getActualAmount();
                        Double actualPoints=settleDetail.getActualPoints();
                        Double actualIntegrals=settleDetail.getActualIntegrals();
    	        		
    	        		FinaMembershipCard leftCard = finaMembershipCardService.getCardByNumber(cardNumber);
    	        		Double consumeTimes = 0.00;
    	        		Double balance = 0.00;
    	        		Double consumePoints = 0.00;
    	        		Double integrals = 0.00;
    	        		if(leftCard != null){
    	        			consumeTimes = leftCard.getConsumeTimes();
    	        			balance = leftCard.getBalance();
    	        			consumePoints = leftCard.getConsumePoints();
    	        			integrals=leftCard.getCardIntegrals();
    	        		}
    	        		FinaExpenseRecord expenseRecord = finaExpenseRecordService.get(expenseId);
    	        		FinaSettleDetail detail=finaSettleDetailService.findById(detailId);
    	        		MainBase base=mainBaseService.findById(expenseRecord.getBaseId());
    	        		String expenseCheckinName=expenseRecord.getName();
    	        		List<String> followNameList=Db.query("select distinct `name` from fina_expense_record_follow "
	        				+ "where expense_id in(select id from fina_expense_record "
	        				+ "where parent_id=? and type='follow_checkin') ",expenseRecord.getId());
    	        		if(followNameList!=null && followNameList.size()>0){
    	        			expenseCheckinName+="带随行:";
    	        			for(String name:followNameList){
    	        				expenseCheckinName+=name+"、";
    	        			}
    	        			expenseCheckinName=expenseCheckinName.substring(0,expenseCheckinName.length()-1);
    	        		}
    	        		
    	        		Calendar sendTime = Calendar.getInstance();
    	        		//设置发送时间间隔1分钟后发送
    	                sendTime.add(Calendar.MINUTE,j+1);
    	        		
	        			if(actualIntegrals != null && actualIntegrals != 0){
	        				JSONObject obj=new JSONObject();
	        				obj.put("company", Global.sendMsgCompanyName);
	        				obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
	        				obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
	        				if(StrKit.notBlank(base.getBaseName())){
	        					obj.put("baseName",base.getBaseName());
	        				}else{
	        					obj.put("baseName","");
	        				}
	        				obj.put("costName","");
	        				obj.put("useDays",actualTimes);
	        				obj.put("leftDays",consumeTimes);
	        				obj.put("useIntegrals",actualIntegrals);
	        				obj.put("leftIntegrals",integrals);
	        				smsSendRecordService.sendMessage(SendType.checkinoutDeductionTimesIntegral,cardNumber, JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
	        			}
	        			if(actualTimes != null && actualTimes != 0){
	        				JSONObject obj=new JSONObject();
	        				obj.put("company", Global.sendMsgCompanyName);
	        				obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
	        				obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
	        				if(StrKit.notBlank(base.getBaseName())){
	        					obj.put("baseName",base.getBaseName());
	        				}else{
	        					obj.put("baseName","");
	        				}
	        				obj.put("costName","");
	        				obj.put("useDays",actualTimes);
	        				obj.put("leftDays",consumeTimes);
	        				smsSendRecordService.sendMessage(SendType.checkinoutDeductionTimes,cardNumber, JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
	        			}
    	        		if(actualAmount != null && actualAmount != 0){
    	        			JSONObject obj=new JSONObject();
    	        			obj.put("company",Global.sendMsgCompanyName);
    	        			obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
    	        			obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
    	        			if(StrKit.notBlank(base.getBaseName())){
    	        				obj.put("baseName",base.getBaseName());
    	        			}else{
    	        				obj.put("baseName","");
    	        			}
    	        			obj.put("costName","");
    	        			obj.put("useAmount",actualAmount);
    	        			obj.put("leftAmount",balance);
    	        			smsSendRecordService.sendMessage(SendType.checkinoutDeductionAmount,cardNumber,JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
    	        		}
    	        		if(actualPoints!=null && actualPoints!=0){
    	        			JSONObject obj=new JSONObject();
    	        			obj.put("company",Global.sendMsgCompanyName);
    	        			obj.put("cardNumber",cardNumber+"由"+expenseCheckinName);
    	        			obj.put("time","["+DateUtils.formatDate(detail.getStartTime(),"yyyy-MM-dd")+"至"+DateUtils.formatDate(detail.getEndTime(),"yyyy-MM-dd")+"]");
    	        			if(StrKit.notBlank(base.getBaseName())){
    	        				obj.put("baseName",base.getBaseName());
    	        			}else{
    	        				obj.put("baseName","");
    	        			}
    	        			obj.put("costName","");
    	        			obj.put("usePoints",actualPoints);
    	        			obj.put("leftPoints",consumePoints);
    	        			smsSendRecordService.sendMessage(SendType.checkinoutDeductionPoints,cardNumber,JSON.toJSONString(obj),DateUtils.formatDate(sendTime.getTime(),"yyyy-MM-dd HH:mm:ss"));
    	        		}
    	        	}
    	        }
    	        if(flag) {
    	        	renderJson(Ret.ok("msg","结算成功"));
    	        }else {
    	        	renderJson(Ret.fail("msg",errorInfo.toString()));
    	        }
    		}
    	}else{
    		renderJson(Ret.fail("msg", "请勾选数据"));
    	}
    }

    public void monthSettleGen(){

        finaSettleDetailService.monthSettleGen(1,20);
        renderText("ok");
    }

    public void cardSettleTask(){
        try {
            cardSettleTask.detailSettle(1,50);
            renderText("ok");
        }catch (Exception e){
            e.printStackTrace();
            renderText("error");
        }

    }

    public void updateCardTransactionsDescribe(){

        finaCardTransactionsService.updateCardTransactionsDescribe(1,1000);

        renderJson(Ret.ok());
    }

    public void newBatchSettle(){
        List<MainBase> baseList = mainBaseService.findBaseList();
        setAttr("baseList",baseList);

        setAttr("checkinTypes", CheckinType.values());
        render("newBatchSettle.html");
    }


    public void newBatchSettlePageList(){
        String baseId = getPara("baseId");
        String checkinType = getPara("checkinType");
        String yearMonth = getPara("yearMonth");
        String cardNumber = getPara("cardNumber");
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }
        String settleDetailSql="select a.id,b.checkin_no as checkinNo,c.card_number as cardNumber,d.full_name as fullName,e.is_integral as isIntegral" +
                ",f.deduct_way as deductWay,g.deduct_way as longDeductWay,a.`year_month` as yearMonth," +
                "a.settle_type as settleType,a.expense_id as expenseId,a.start_time as startTime,a.end_time as endTime,a.total_times as totalTimes,a.total_amount as totalAmount " +
                ",a.total_integrals as totalIntegrals,a.actual_times as actualTimes,a.actual_amount as actualAmount" +
                ",a.actual_integrals as actualIntegrals,b.remark,a.settle_status as settleStatus,b.`name` " +
                " from fina_settle_detail a " +
                "inner join fina_expense_record b on a.expense_id=b.id " +
                "left join fina_membership_card c on c.id=(select id from fina_membership_card where card_number=a.card_number order by del_flag limit 1) " +
                "left join mms_member d on d.id=c.member_id " +
                "left join main_membership_card_type e on e.id=c.card_type_id " +
                "left join main_card_deduct_scheme f on f.id=c.deduct_scheme_id " +
                "left join main_card_deduct_scheme g on g.id=c.long_deduct_scheme_id " +
                "where a.`year_month`=?  " +
                " and a.del_flag='0' and a.settle_status='0' and b.base_id=? and b.checkin_type=? and b.del_flag='0' ";
        List<Object> settleDetailParam=new ArrayList<>();
        settleDetailParam.add(yearMonth);
        settleDetailParam.add(baseId);
        settleDetailParam.add(checkinType);
        if(StrKit.notBlank(cardNumber)){
            settleDetailSql+=" and a.card_number=? ";
            settleDetailParam.add(cardNumber.trim());
        }
        settleDetailSql+=" ORDER BY b.checkin_no,a.start_time ";
        List<Record> settleDetailList=Db.find(settleDetailSql,settleDetailParam.toArray());
        if(settleDetailList==null || settleDetailList.size()==0){
            renderJson(new DataTable<>(new ArrayList<>()));
            return;
        }
        List<String> expenseIds=new ArrayList<>();
        String str="";
        for (Record record : settleDetailList) {
            expenseIds.add(record.getStr("expenseId"));
            str+="?,";
            if(StrKit.isBlank(record.getStr("deductWay"))){
                record.set("deductWay",record.getStr("longDeductWay"));
            }
        }
        str=str.substring(0,str.length()-1);

        List<Record> followRecordList=Db.find("select parent_id,GROUP_CONCAT('、',`name`) as `names` from  " +
                "(select parent_id,`name` from fina_expense_record where parent_id in ("+str+") and `type`='follow_checkin' and del_flag='0' " +
                "GROUP BY parent_id,`name` " +
                ") t GROUP BY parent_id",expenseIds.toArray());

        Map<String,String> followNameMap=new HashMap<>();
        for (Record record : followRecordList) {
            followNameMap.put(record.getStr("parent_id"),record.getStr("names"));
        }
        for (Record record : settleDetailList) {
            if(followNameMap.containsKey(record.getStr("expenseId"))){
                record.set("name",record.getStr("name")+followNameMap.get(record.getStr("expenseId")));
            }
        }

        renderJson(new DataTable<Record>(settleDetailList));
    }



}
