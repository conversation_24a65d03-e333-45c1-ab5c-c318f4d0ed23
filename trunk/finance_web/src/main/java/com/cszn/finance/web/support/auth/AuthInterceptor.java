package com.cszn.finance.web.support.auth;

import java.util.ArrayList;
import java.util.List;

import org.apache.shiro.SecurityUtils;

import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.entity.sys.Menu;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;

import io.jboot.Jboot;

/**
 * 根据url的权限拦截器，具有url权限的角色才允许访问
 * <AUTHOR>
 *
 */
public class AuthInterceptor implements Interceptor {

    /**
     * 获取全部 需要控制的权限
     */
    private static List<String> urls;

    public AuthInterceptor() {

    }

    public static List<String> getUrls() {
        return urls;
    }

    public static void init() {
    	List<String> list = new ArrayList<String>();
    	MenuService menuService = Jboot.service(MenuService.class);
        List<Menu> sysMenuList = menuService.findAll();
    	for (Menu menu : sysMenuList) {
    		list.add(menu.getMenuUrl());
    	}
        urls = list;
    }

    @Override
    public void intercept(Invocation ai) {
        if (urls == null) {
            init();
        }

        String url = ai.getActionKey();
        boolean flag = SecurityUtils.getSubject() != null && SecurityUtils.getSubject().isPermitted(url);

        if (urls.contains(url) && !flag) {
            ai.getController().renderError(403);
        } else {
            ai.invoke();
        }
    }

}
