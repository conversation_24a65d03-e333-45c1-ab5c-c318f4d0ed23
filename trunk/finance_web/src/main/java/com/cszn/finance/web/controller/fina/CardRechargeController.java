package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.*;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.enums.RechargeWay;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.RechargeSource;
import com.cszn.integrated.service.provider.fina.FinaCardRechargeServiceImpl;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description 会员卡充值扣费管理
 * <AUTHOR>
 * @Date 2019/5/7
 **/
@RequestMapping(value="/fina/recharge", viewPath="/modules_page/finance/fina/recharge")
public class CardRechargeController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(CardRechargeController.class);

	@Inject
    private FinaCardRechargeService finaCardRechargeService;
	@Inject
    private FinaMembershipCardService finaMembershipCardService;
	@Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
	@Inject
    private MainAppJoinService mainAppJoinService;
	@Inject
    private MainBaseService mainBaseService;
	@Inject
    private FinaCardTransactionsService finaCardTransactionsService;
	@Inject
    private FinaExpenseRecordDetailService finaExpenseRecordDetailService;
	@Inject
    private FinaCardAccountService finaCardAccountService;
	@Inject
    private DictService dictService;
	@Inject
    private MmsMemberService mmsMemberService;
	@Inject
    private FinaCardIntegralRecordService finaCardIntegralRecordService;
	@Inject
	private FinaCardTransferDetailService finaCardTransferDetailService;

    /**
     * 跳转到会员卡充值扣卡界面
     */
    public void index(){
         render("rechargeIndex.html");
    }
    
    
    /**
     * 跳转到会员卡充值审核界面
     */
    public void rechargeReview(){
    	render("rechargeReview.html");
    }

    public void rechargeReview2(){
        render("rechargeReview2.html");
    }
    
    
    /**
     * 跳转到充值日报界面
     */
    public void dailyStatistics(){
    	render("dailyStatistics.html");
    }
    
    
    /**
     * 跳转到充值统计界面
     */
    public void rechargeStatistics(){
    	String yearMonthDay=DateUtils.getDate("yyyy-MM-dd");
        String yearMonth=DateUtils.getDate("yyyy-MM");
        String year=DateUtils.getDate("yyyy");
        setAttr("yearMonthDay",yearMonthDay);
        setAttr("yearMonth",yearMonth);
        setAttr("year",year);
    	render("rechargeStatistics.html");
    }
    
    
    /**
     * 跳转到会员卡充值明细界面
     */
    public void detail(){
    	final String cardNumber = getPara("cardNumber");
    	setAttr("cardNumber",cardNumber);
    	render("detail.html");
    }
    
    
    /**
     * 充值统计数据
     */
    @Clear(LogInterceptor.class)
    public void rechargeStatisticsData(){
    	String queryType = getPara("queryType");
    	String yearMonthDay = getPara("yearMonthDay");
    	String yearMonth = getPara("yearMonth");
    	String year = getPara("year");
    	List<Record> list = finaCardRechargeService.rechargeStatistics(queryType, yearMonthDay, yearMonth, year);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list.get(0)));
    }
    
    
    /**
     * 充值统计明细分页
     */
    @Clear(LogInterceptor.class)
    public void rechargeStatisticsPage(){
    	String queryType = getPara("queryType");
    	String yearMonthDay = getPara("yearMonthDay");
    	String yearMonth = getPara("yearMonth");
    	String year = getPara("year");
    	Page<Record> page = finaCardRechargeService.rechargeStatisticsPage(queryType, yearMonthDay, yearMonth, year,getParaToInt("page"),getParaToInt("limit"));
    	renderJson(new DataTable<Record>(page));
    }
    
    /**
     * 导出excel
     */
    public void rechargeStatisticsExport(){
    	String queryType = getPara("queryType");
    	String yearMonthDay = getPara("yearMonthDay");
    	String yearMonth = getPara("yearMonth");
    	String year = getPara("year");
        List<Record> recordList=finaCardRechargeService.rechargeStatisticsList(queryType, yearMonthDay, yearMonth, year);
        if(recordList==null || recordList.size()==0){
            renderJson(Ret.fail("msg","未查询到充值记录"));
            return;
        }

        String[] title={"会员卡信息","卡类别","收入性","是否收现金","收钱账户","充值编号","充值类型","充值分类","金额","天数","点数","积分","豆豆券","充值时间","撤销状态","审核状态","作废状态","说明"};
        String fileName="充值记录.xls";
        String sheetName = "充值记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);
            
            String cardInfo = "";
            if(StrKit.notBlank(record.getStr("cardNumber"))) {
            	cardInfo += record.getStr("cardNumber");
            }else {
            	cardInfo += "--";
            }
            cardInfo += " ";
            if(StrKit.notBlank(record.getStr("cardMemberName"))) {
            	cardInfo += record.getStr("cardMemberName");
            }else {
            	cardInfo += "--";
            }

            content[i][0]=cardInfo;
            content[i][1]=record.getStr("cardTypeName");
            String isIncome = "--";
            if(StrKit.notBlank(record.getStr("isIncome"))) {
            	if("1".equals(record.getStr("isIncome"))) {
            		isIncome = "收入性";
            	}else if("0".equals(record.getStr("isIncome"))) {
            		isIncome = "非收入性";
            	}
            }
            content[i][2]=isIncome;
            String isCash = "--";
            if(StrKit.notBlank(record.getStr("isCash"))) {
            	if("1".equals(record.getStr("isCash"))) {
            		isCash = "是";
            	}else if("0".equals(record.getStr("isCash"))) {
            		isCash = "否";
            	}
            }
            content[i][3]=isCash;
            String payWay = "";
            if("1".equals(record.getStr("payWay"))) {
            	payWay = "(现金)";
            }else if("2".equals(record.getStr("payWay"))) {
            	payWay = "(微信)";
            }else if("3".equals(record.getStr("payWay"))) {
            	payWay = "(支付宝)";
            }else if("4".equals(record.getStr("payWay"))) {
            	payWay = "(信用卡)";
            }else if("5".equals(record.getStr("payWay"))) {
            	payWay = "(会员卡)";
            }else if("6".equals(record.getStr("payWay"))) {
            	payWay = "(Pos机)";
            }else if("7".equals(record.getStr("payWay"))) {
            	payWay = "(转账)";
            }else if("8".equals(record.getStr("payWay"))) {
            	payWay = "(企业微信)";
            }
            content[i][4]=payWay+record.getStr("accountName");
            content[i][5]=record.getStr("rechargeNo");
            String typeName = "";
            if("1".equals(record.getStr("type"))) {
            	typeName = "金额";
            }else if("2".equals(record.getStr("payWay"))) {
            	typeName = "天数";
            }else if("3".equals(record.getStr("payWay"))) {
            	typeName = "点数";
            }else if("4".equals(record.getStr("payWay"))) {
            	typeName = "积分";
            }else if("5".equals(record.getStr("payWay"))) {
            	typeName = "豆豆券";
            }
            content[i][6]=typeName;
            String classifyName = "";
            if(StrKit.notBlank(record.getStr("classify"))) {
            	classifyName = dictService.getDictNameByTypeValue("recharge_classify", record.getStr("classify"));
            }
            content[i][7]=classifyName;
            content[i][8]=record.getStr("amount")+"元";
            content[i][9]=record.getStr("consumeTimes")+"天";
            content[i][10]=record.getStr("consumePoints");
            content[i][11]=record.getStr("consumeIntegral");
            content[i][12]=record.getStr("consumeBeanCoupons");
            content[i][13]=record.getStr("rechargeTime");
            String cancelFlag = "";
            if(StrKit.notBlank(record.getStr("cancelFlag"))) {
            	if("1".equals(record.getStr("cancelFlag"))) {
            		cancelFlag = "已撤销";
            	}else if("0".equals(record.getStr("cancelFlag"))) {
            		cancelFlag = "未撤销";
            	}
            }
            content[i][14]=cancelFlag;
            String isReview = "";
            if(StrKit.notBlank(record.getStr("isReview"))) {
            	if("1".equals(record.getStr("isReview"))) {
            		isReview = "已审核";
            	}else if("0".equals(record.getStr("isReview"))) {
            		isReview = "未审核";
            	}
            }
            content[i][15]=isReview;
            String delFlag = "";
            if(StrKit.notBlank(record.getStr("delFlag"))) {
            	if("1".equals(record.getStr("delFlag"))) {
            		delFlag = "已作废";
            	}else if("0".equals(record.getStr("delFlag"))) {
            		delFlag = "未作废";
            	}
            }
            content[i][16]=delFlag;
            content[i][17]=record.getStr("describe");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    
    /**
     * 充值日报列表
     */
    @Clear(LogInterceptor.class)
    public void dailyStatisticsList(){
    	String rechargeDate = getPara("rechargeDate");
    	List<Record> list = finaCardRechargeService.rechargeDailyStatistics(rechargeDate);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }


    /**
     * 充值记录分页
     */
    @Clear(LogInterceptor.class)
    public void pageTable(){
        String cardNumber = getPara("cardNumber");
        String fullName = getPara("fullName");
        String isReview = getPara("isReview");
        String delFlag = getPara("delFlag");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        String orderBy = getPara("orderBy");
        String isAuto=getPara("isAuto");
        Page<Record> page = finaCardRechargeService.rechargePage(cardNumber,fullName,isReview,delFlag,startDate,endDate,"",orderBy,isAuto,getParaToInt("page"),getParaToInt("limit"));
        renderJson(new DataTable<Record>(page));
    }
    
    
    /**
     * 查看原会员卡信息及交易记录
     */
    public void beforeCardForm(){
        String cardId = getPara("cardId");
//        FinaMembershipCard card = new FinaMembershipCard();
//        card.setId(cardId);
//        Record record = finaMembershipCardService.getCardAndMember(card);
//        if(record != null){
//            setAttr("cardId",cardId);
//            setAttr("card",record);
//        }
        Record transferRecord = Db.findFirst("select * from fina_card_transfer where new_card_id=?", cardId);
        setAttr("transferRecord",transferRecord);
        render("oldCardTran.html");
    }

    /**
     * 转移明细分页
     */
    @Clear(LogInterceptor.class)
    public void transferDetailPage(){
    	FinaCardTransferDetail model = getBean(FinaCardTransferDetail.class,"",true);
		Columns columns = Columns.create();
		if(StrKit.notBlank(model.getTransferId())) {
			columns.eq("transfer_id", model.getTransferId());
		}
		Page<FinaCardTransferDetail> modelPage = finaCardTransferDetailService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "old_card_number");
		renderJson(new DataTable<FinaCardTransferDetail>(modelPage));
    }

    /**
     * 跳转充值界面
     */
    public void addRechargeForm(){
        setAttr("cardId",getPara("cardId"));
        setAttr("isIntegral",getPara("isIntegral"));

        List<FinaCardAccount> accountList = finaCardAccountService.findList(null);
        setAttr("accountList",accountList);
        render("addRecharge.html");
    }
    


    /**
     * 会员卡扣卡：记录插入交易记录和消费记录表
     * 更新会员卡余额,剩余天数
     */
    public void saveRechargeReview(){
    	String rechargeId = getPara("rechargeId");
        synchronized (FinaCardRechargeServiceImpl.class){
    	    FinaCardRecharge cardRecharge = finaCardRechargeService.findById(rechargeId);
            if(cardRecharge!=null){
                    if("1".equals(cardRecharge.getIsReview())){
                        renderJson(Ret.fail("msg","该记录已审核过了，请刷新页面"));
                        return;
                    }

                    logger.info("会员充值审核信息==="+JSON.toJSONString(cardRecharge));
                    FinaMembershipCard card = finaMembershipCardService.findById(cardRecharge.getCardId());
                    if(card!=null){
                        if("0".equals(card.getDelFlag()) && "0".equals(card.getIsLock())){
                            if(finaCardRechargeService.saveReviewRecharge(rechargeId, AuthUtils.getUserId())) {
                                renderJson(Ret.ok("msg","审核成功"));
                            }else {
                                renderJson(Ret.fail("msg","审核失败"));
                            }
                        }else if("1".equals(card.getIsLock())){
                            renderJson(Ret.fail("msg","审核失败,"+card.getCardNumber()+"卡已锁定!"));
                        }else if("1".equals(card.getDelFlag())){
                            renderJson(Ret.fail("msg","审核失败,"+card.getCardNumber()+"卡已作废!"));
                        }
                    }


            }else{
                renderJson(Ret.fail("msg","审核失败,审核数据为空!"));
            }
        }
    }



    /**
     * 会员卡充值：插入充值记录表，会员卡交易记录表，是否插入消费记录表？否
     * 更新会员卡表数据：余额，剩余天数,赠送余额，赠送天数
     */
    public void save(){
        FinaCardRecharge cardRecharge = getBean(FinaCardRecharge.class,"recharge",true);
        logger.info("会员卡充值入参：[{}]",JSON.toJSONString(cardRecharge));

        FinaMembershipCard card=finaMembershipCardService.findById(cardRecharge.getCardId());
        if(card==null){
            renderJson(Ret.fail("msg","该会员卡不存在"));
            return;
        }
        if("1".equals(card.getIsLock())){
            renderJson(Ret.fail("msg","该会员卡为锁定状态，充值失败!"));
            return;
        }
        if(cardRecharge.getConsumeBeanCoupons()!=null && cardRecharge.getConsumeBeanCoupons()>0) {
        	MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
        	if(cardType!=null) {
        		if(!"1".equals(cardType.getIsIntegral())) {
        			renderJson(Ret.fail("msg","该会员卡不是积分卡，不能充值豆豆券!"));
                    return;
        		}
        	}else {
        		renderJson(Ret.fail("msg","该会员卡没有卡类别，充值失败!"));
                return;
        	}
        }

        if((cardRecharge.getAmount() != null && cardRecharge.getAmount() == 0 )
                || (cardRecharge.getConsumeTimes() != null && cardRecharge.getConsumeTimes() == 0)
                || (cardRecharge.getConsumePoints() != null && cardRecharge.getConsumePoints() == 0)
                || (cardRecharge.getConsumeIntegral() != null && cardRecharge.getConsumeIntegral() == 0)
                || (cardRecharge.getConsumeBeanCoupons() != null && cardRecharge.getConsumeBeanCoupons() == 0)
        ){
            renderJson(Ret.fail("msg","充值金额、天数、点数、积分或豆豆券不能为0"));
            return;
        }
        if((cardRecharge.getGiveAmount() != null && cardRecharge.getGiveAmount() == 0)
                ||(cardRecharge.getGiveConsumeTimes() != null && cardRecharge.getGiveConsumeTimes() == 0)
                ||(cardRecharge.getGiveConsumePoints() != null && cardRecharge.getGiveConsumePoints() == 0)
                ||(cardRecharge.getGiveConsumeIntegral() != null && cardRecharge.getGiveConsumeIntegral() == 0)
                ||(cardRecharge.getGiveBeanCoupons() != null && cardRecharge.getGiveBeanCoupons() == 0)
        ){
            renderJson(Ret.fail("msg","赠送金额、天数、点数、积分或豆豆券不能为0"));
            return;
        }

        //若金额或天数为空，则置为0/若赠送金额或天数为空，则置为0
        if(cardRecharge.getCashValue() == null)cardRecharge.setCashValue(0.00);
        if(cardRecharge.getAccountValue() == null)cardRecharge.setAccountValue(0.00);
        if(cardRecharge.getAmount() == null)cardRecharge.setAmount(0.00);
        if(cardRecharge.getConsumeTimes() == null)cardRecharge.setConsumeTimes(0.0);
        if(cardRecharge.getConsumePoints()==null)cardRecharge.setConsumePoints(0.0);
        if(cardRecharge.getConsumeIntegral()==null)cardRecharge.setConsumeIntegral(0.0);
        if(cardRecharge.getConsumeBeanCoupons()==null)cardRecharge.setConsumeBeanCoupons(0.0);
        if(cardRecharge.getPrice()==null)cardRecharge.setPrice(0.0);
        if(cardRecharge.getPayAmount()==null)cardRecharge.setPayAmount(0.0);
        if(cardRecharge.getGiveAmount() == null)cardRecharge.setGiveAmount(0.00);
        if(cardRecharge.getGiveConsumeTimes() == null)cardRecharge.setGiveConsumeTimes(0.0);
        if(cardRecharge.getGiveConsumePoints()==null)cardRecharge.setGiveConsumePoints(0.0);
        if(cardRecharge.getGiveConsumeIntegral()==null)cardRecharge.setGiveConsumeIntegral(0.0);
        if(cardRecharge.getGiveBeanCoupons()==null)cardRecharge.setGiveBeanCoupons(0.0);
        cardRecharge.setRechargeWay(RechargeWay.single.getKey());
        if(cardRecharge.getAverageDays()==null)cardRecharge.setAverageDays(0.0);
        if(cardRecharge.getCountPrice()==null)cardRecharge.setCountPrice(0.0);
        if(cardRecharge.getCountDays()==null)cardRecharge.setCountDays(0.0);
        cardRecharge.setRechargeSource(RechargeSource.HEADQUARTERS);
        boolean flag = finaCardRechargeService.saveRecharge(cardRecharge, AuthUtils.getUserId());
        logger.info("会员卡充值serviceImpl处理结果：[{}]",flag);
        if(flag){
            renderJson(Ret.ok("msg","充值成功"));
        }else{
            renderJson(Ret.fail("msg","充值失败"));
        }

    }


    /**
     * 跳转扣卡界面
     */
    public void deductCardForm(){
        String cardId = getPara("cardId");
        List<MainBase> baseList = mainBaseService.findBaseList();
        FinaMembershipCard card = finaMembershipCardService.get(cardId);
        MainMembershipCardType type = null;
        if(card != null && StringUtils.isNotBlank(card.getCardTypeId())){
            type = mainMembershipCardTypeService.findById(card.getCardTypeId());
        }
        List<MainAppJoin> appList =  mainAppJoinService.findAll();
        setAttr("type",type);
        setAttr("card",card);
        setAttr("appList",appList);
        setAttr("baseList",baseList);
        render("deductCard.html");
    }


    /**
     * 会员卡扣卡：记录插入交易记录和消费记录表
     * 更新会员卡余额,剩余天数
     */
    public void deductCard(){

    	String cardId = getPara("cardId");
    	String baseId = getPara("baseId");
        String appNo = getPara("appNo");
        Date startDate=getParaToDate("startDate");
        Date endDate=getParaToDate("endDate");
        String appOrderNo = getPara("appOrderNo");
        String describe = getPara("describe");
        String consumeType = getPara("consumeType");
        logger.info("会员卡扣卡入参：[会员卡ID：[{}]，应用编号：[{}]，应用订单号：[{}]，基地ID：[{}]，描述：[{}]，消费渠道：[{}]]",cardId,appNo,appOrderNo,baseId,describe,consumeType);
        logger.info("会员卡扣卡入参：[消费金额：[{}]，消费天数：[{}]，消费积分：[{}]]",getPara("amount"),getPara("consumeTimes"),getPara("integrals"));

        //订单号不可重复
        /*if(StringUtils.isNotBlank(appOrderNo)){
            Long count = Db.queryLong("select count(*) from fina_consume_record where del_flag = 0 and app_order_no = ?",appOrderNo);
            if(count > 0){ renderJson(Ret.fail("msg","订单号不能重复"));return; }
        }*/

        FinaMembershipCard card=finaMembershipCardService.findById(cardId);
        if(card==null){
            renderJson(Ret.fail("msg","该会员卡不存在"));
            return;
        }
        if(card.getCardMinIntegrals()==null){
            card.setCardMinIntegrals(0.0);
        }
        if("1".equals(card.getIsLock())){
            renderJson(Ret.fail("msg","该会员卡为锁定状态，扣卡失败!"));
            return;
        }

        Double amount = null;
        if(StringUtils.isNotBlank(getPara("amount"))){
            amount = Double.valueOf(getPara("amount"));
        }
        Double consumeTimes = null;
        if(StringUtils.isNotBlank(getPara("consumeTimes"))){
            consumeTimes = Double.valueOf(getPara("consumeTimes"));
        }
        Double points=null;
        if(StringUtils.isNotBlank(getPara("points"))){
            points = Double.valueOf(getPara("points"));
        }
        Double integrals=null;
        if(StringUtils.isNotBlank(getPara("integrals"))){
        	integrals = Double.valueOf(getPara("integrals"));
        }

        if(amount == null && consumeTimes == null && points==null && integrals==null){ renderJson(Ret.fail("msg","扣除金额、天数、点数和积分不能都为空"));return; }
        if(
            (amount != null && amount == 0.00)
            && (consumeTimes != null && consumeTimes ==0.0)
            && (points != null && points ==0)
            && (integrals != null && integrals ==0)
        ){
            renderJson(Ret.fail("msg","扣除金额、天数、点数和积分不能都为0"));return;
        }

        //判断是否可以扣卡
        if(card == null) {
            renderJson(Ret.fail("msg","会员卡不存在，无法扣卡"));
            return;
        }
//        if((card.getBalance() == null || card.getBalance() == 0) && amount != null) {renderJson(Ret.fail("msg","会员卡余额为空，不可扣卡"));return;}
//        if((card.getConsumeTimes() == null || card.getConsumeTimes() == 0) && consumeTimes != null) {renderJson(Ret.fail("msg","会员卡消费天数为空，不可扣卡"));return;}
//        if((card.getConsumePoints()==null || card.getConsumePoints()==0) && points!=null){renderJson(Ret.fail("msg","会员卡点数为空，不可扣卡"));return;}
//        if((card.getCardIntegrals()==null || card.getCardIntegrals()==0) && integrals!=null){renderJson(Ret.fail("msg","会员卡积分为空，不可扣卡"));return;}
        //此处为空时，置为0
        if(amount == null)amount = 0.00;
        if(consumeTimes == null)consumeTimes = 0.0;
        if(points==null)points=0.0;
        if(integrals==null)integrals=0.0;
        if(card.getBalance() == null)card.setBalance(0.00);
        if(card.getConsumeTimes() == null)card.setConsumeTimes(0.00);
        if(card.getConsumePoints()==null)card.setConsumePoints(0.00);
        if(card.getCardIntegrals()==null)card.setCardIntegrals(0.00);
        if(card.getGiveRechargeAmount()==null)card.setGiveRechargeAmount(0.00);
        if(card.getGiveRechargeDays()==null)card.setGiveRechargeDays(0.00);
        if(card.getGiveRechargeIntegrals()==null)card.setGiveRechargeIntegrals(0.00);
        if(card.getBuyRechargeAmount()==null)card.setBuyRechargeAmount(0.00);
        if(card.getBuyRechargeDays()==null)card.setBuyRechargeDays(0.00);
        if(card.getBuyRechargeIntegrals()==null)card.setBuyRechargeIntegrals(0.00);

        if(amount == 0.00 && consumeTimes == 0 && points==0.0 && integrals==0.0){
            renderJson(Ret.fail("msg","扣除金额、天数、点数和积分不能都为空"));
            return;
        }

        //获取会员卡锁定
        Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(card.getCardNumber());

        if(
    		BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(amount)).subtract(new BigDecimal(Double.toString(lock.get("lockBalance")))).doubleValue() < 0
            || BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(consumeTimes)).subtract(new BigDecimal(Double.toString(lock.get("lockConsumeTimes")))).doubleValue() < 0
            || BigDecimal.valueOf(card.getConsumePoints()).subtract(BigDecimal.valueOf(points)).subtract(BigDecimal.valueOf(lock.get("lockPoints"))).doubleValue()<0
            || BigDecimal.valueOf(card.getCardIntegrals()).subtract(BigDecimal.valueOf(integrals)).subtract(BigDecimal.valueOf(lock.get("lockIntegrals"))).compareTo(BigDecimal.valueOf(card.getCardMinIntegrals()))==-1
        ) {
            renderJson(Ret.fail("msg","会员卡余额、消费天数、点数或积分不足，不可扣卡"));
            return;
        }

        boolean flag = finaMembershipCardService.saveDeductCard(
			describe
			,amount
			,consumeTimes
			,points
			,integrals
			,card
			,appNo
			,appOrderNo
			,baseId
			,AuthUtils.getUserId()
			,startDate
			,endDate
            ,consumeType
		);
        logger.info("会员卡扣卡serviceImpl处理结果：[{}]",flag);
        if(flag){
            renderJson(Ret.ok("msg","扣卡成功").set("data",card));
        }else{
            renderJson(Ret.fail("msg","扣卡失败"));
        }
    }

    /**
     * 撤销充值首页
     */
    public void cancelRechargeIndex(){
        String cardId=getPara("cardId");
        setAttr("cardId",cardId);
        render("cancelRechargeIndex.html");
    }

    /**
     * 获取未撤销过的充值记录
     */
    public void cancelRechargePage(){
        int pageNumber=getParaToInt("page");
        int pageSize=getParaToInt("limit");
        String cardId=getPara("cardId");
        Page<FinaCardRecharge> page=finaCardRechargeService.cancelRechargePage(pageNumber,pageSize,cardId);
        renderJson(new DataTable<FinaCardRecharge>(page));
    }

    /**
     * 撤销充值记录
     */
    public void cancelRechargeRecord(){
        String id=getPara("id");
        FinaCardRecharge recharge=finaCardRechargeService.findById(id);
        if(recharge==null || !"0".equals(recharge.getCancelFlag())){
            renderJson(Ret.fail("msg","该充值记录不存在或已被撤销"));
            return;
        }
        FinaMembershipCard memberCard=finaMembershipCardService.findById(recharge.getCardId());
        if(memberCard==null){
            renderJson(Ret.fail("msg","该会员卡不存在"));
            return;
        }
        if("1".equals(memberCard.getIsLock())){
            renderJson(Ret.fail("msg","该会员卡为锁定状态，撤销充值失败!"));
            return;
        }
        Double balance=memberCard.getBalance()==null?0:memberCard.getBalance();
        Double consumeTimes=memberCard.getConsumeTimes()==null?0:memberCard.getConsumeTimes();
        Double cardIntegrals=memberCard.getCardIntegrals()==null?0:memberCard.getCardIntegrals();
        Double beanCoupons=memberCard.getBeanCoupons()==null?0:memberCard.getBeanCoupons();
        Double giveBalance=memberCard.getGiveBalance()==null?0:memberCard.getGiveBalance();
        Double giveConsumeTimes=memberCard.getGiveConsumeTimes()==null?0:memberCard.getGiveConsumeTimes();
        Double giveCardIntegrals=memberCard.getGiveConsumeIntegrals()==null?0:memberCard.getCardIntegrals();
        Double giveBeanCoupons=memberCard.getGiveBeanCoupons()==null?0:memberCard.getGiveBeanCoupons();


        if(balance<(recharge.getAmount()==null?0.0:recharge.getAmount())){
            renderJson(Ret.fail("msg","会员卡余额不足无法撤销"));
            return;
        }
        if(consumeTimes<(recharge.getConsumeTimes()==null?0.0:recharge.getConsumeTimes())){
            renderJson(Ret.fail("msg","会员卡天数不足无法撤销"));
            return;
        }
        if(cardIntegrals<(recharge.getConsumeIntegral()==null?0.0:recharge.getConsumeIntegral())){
        	renderJson(Ret.fail("msg","会员卡积分不足无法撤销"));
        	return;
        }
        if(beanCoupons<(recharge.getConsumeBeanCoupons()==null?0.0:recharge.getConsumeBeanCoupons())){
        	renderJson(Ret.fail("msg","会员卡豆豆券不足无法撤销"));
        	return;
        }
        if(giveBalance<(recharge.getGiveAmount()==null?0.0:recharge.getGiveAmount())){
            renderJson(Ret.fail("msg","赠送余额不足无法撤销"));
            return;
        }
        if(giveConsumeTimes<(recharge.getGiveConsumeTimes()==null?0.0:recharge.getGiveConsumeTimes())){
            renderJson(Ret.fail("msg","赠送天数不足无法撤销"));
            return;
        }
        if(giveCardIntegrals<(recharge.getGiveConsumeIntegral()==null?0.0:recharge.getGiveConsumeIntegral())){
            renderJson(Ret.fail("msg","赠送积分不足无法撤销"));
            return;
        }
        if(giveBeanCoupons<(recharge.getGiveBeanCoupons()==null?0.0:recharge.getGiveBeanCoupons())){
        	renderJson(Ret.fail("msg","赠送豆豆券不足无法撤销"));
        	return;
        }
        boolean flag=finaCardRechargeService.cancelRechargeRecord(recharge,memberCard,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","撤销成功"));
        }else{
            renderJson(Ret.fail("msg","撤销失败"));
        }
    }



    /**
     * 跳转修改备注界面
     */
    public void updateRemarkForm(){
        String id = getPara("id");
        FinaCardTransactions cardTran  = finaCardTransactionsService.findById(id);
        setAttr("cardTran",cardTran);
        render("updateRemarkForm.html");
    }


    /**
     * 修改备注
     */
    public void updateRemark(){
        FinaCardTransactions cardTran = getBean(FinaCardTransactions.class,"cardTran",true);
        String oldDescribe = getPara("oldDescribe");
        if(StringUtils.isBlank(cardTran.getId()) || StringUtils.isBlank(cardTran.getDescribe())){ renderJson(Ret.fail("msg","修改备注缺失参数"));return; }
        if(StringUtils.isNotBlank(oldDescribe) && oldDescribe.equals(cardTran.getDescribe())){ renderJson(Ret.fail("msg","修改备注与原备注一致，不可修改"));return; }

        boolean flag = finaCardTransactionsService.updateRemark(cardTran,oldDescribe,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","修改成功"));
        }else{
            renderJson(Ret.fail("msg","修改失败"));
        }
    }

    public void lockDetailIndex(){
        String cardNumber=getPara("cardNumber");

        setAttr("cardNumber",cardNumber);
        render("lockDetail.html");
    }
    
    public void integralsDetailIndex(){
    	final String cardId = getPara("cardId");
    	final String cardNumber = getPara("cardNumber");
    	Record record = Db.findFirst("select sum(case when business_type='system_give' then integral_value else 0 end)giveCount,"
			+ "sum(case when business_type='mall_exchange' then integral_value else 0 end)exchangeCount from fina_card_integral_record "
			+ "where del_flag='0' and card_id=? group by card_id;", cardId);
    	final double rechargeIntegrals = Db.queryDouble("select IFNULL(sum(consume_integral),0)consumeIntegral from fina_card_recharge where type='4' and del_flag='0' and cancel_flag='0' and card_id=?", cardId);
    	final double hadDeductIntegrals = Db.queryDouble("select IFNULL(sum(integrals),0)integrals from fina_card_transactions where type like '%deduction%' and is_hidden='0' and card_id=?", cardId);
    	setAttr("cardId",cardId);
    	setAttr("cardNumber",cardNumber);
    	setAttr("giveCount",record!=null && record.getDouble("giveCount")!=null?record.getDouble("giveCount"):0.0);
    	setAttr("exchangeCount",record!=null && record.getDouble("exchangeCount")!=null?record.getDouble("exchangeCount"):0.0);
    	setAttr("rechargeIntegrals",rechargeIntegrals);
    	setAttr("hadDeductIntegrals",hadDeductIntegrals);
    	render("integralsDetail.html");
    }

    public void cardLockDetail(){
        String cardNumber=getPara("cardNumber");
        Page<Record> recordPage=finaExpenseRecordDetailService.cardLockDetail(getParaToInt("page"),getParaToInt("limit"),cardNumber);
        renderJson(new DataTable<Record>(recordPage));
    }
    
    public void cardIntegralsDetail(){
    	FinaCardIntegralRecord model = getBean(FinaCardIntegralRecord.class,"",true);
    	Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
    	if(StrKit.notBlank(model.getCardId())) {
    		columns.eq("card_id", model.getCardId());
    	}
    	if(StrKit.notBlank(model.getTransactionType())) {
    		columns.eq("transaction_type", model.getTransactionType());
    	}
    	if(StrKit.notBlank(model.getBusinessType())) {
    		columns.eq("business_type", model.getBusinessType());
    	}
    	Page<FinaCardIntegralRecord> modelPage = finaCardIntegralRecordService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "give_date desc");
    	renderJson(new DataTable<FinaCardIntegralRecord>(modelPage));
    }
    
    
	/**
	 * 更新方法
	 */
    public void updateRecharge() {
    	FinaCardRecharge finaCardRecharge = getBean(FinaCardRecharge.class, "", true);
        if (finaCardRechargeService.update(finaCardRecharge)) {
        	renderJson(Ret.ok("msg", "操作成功!"));
        } else {
        	renderJson(Ret.fail("msg", "操作失败！"));
        }
    }

    public void test() throws  Exception{
        int page=getParaToInt("page");
        int limit=getParaToInt("limit");
        Page<Record> recordList= Db.paginate(page,limit,"select * ","  from fina_card_transactions3 a where is_hidden='0' and a.start_date is null " +
                " ORDER BY deal_time desc ");
        String regex="\\[([^\\]至[\\\\]]*)\\]";
        //String dateRegex="\\d{4}[.]\\d{2}[.]\\d{2}(\\s\\d{2}[:]\\d{2}[:]\\d{2})?[-]\\d{4}[.]\\d{2}[.]\\d{2}(\\s\\d{2}[:]\\d{2}[:]\\d{2})?";
        //String dateRegex="\\d{4}[-]\\d{2}[-]\\d{2}(\\s\\d{2}[:]\\d{2}[:]\\d{2})?";
        //String dateRegex="\\d{4}[-]\\d{2}[-]\\d{2}(\\s\\d{2}[:]\\d{2}[:]\\d{2})?[\\]至\\[]\\d{4}[-]\\d{2}[-]\\d{2}(\\s\\d{2}[:]\\d{2}[:]\\d{2})?";
        //Pattern pattern = Pattern.compile("[0-9]{4}[.][0-9]{1,2}[.][0-9]{1,2}[ ][0-9]{1,2}[:][0-9]{1,2}[:][0-9]{1,2}");
        Pattern pattern = Pattern.compile("[0-9]{4}[年][0-9]{1,2}[月][0-9]{1,2}");
        List<Record> returnList=new ArrayList<>();
        for(Record record :recordList.getList()) {
            String remark = record.getStr("describe");
            if(StrKit.isBlank(remark)){
                continue;
            }
            Matcher matcher = pattern.matcher(remark);
            Date minDate=null;
            Date maxDate=null;
            while (matcher.find()) {
                /*String str = matcher.group(1);
                if (Pattern.matches(dateRegex, str)) {
                    String[] dates = str.split("]至\\[");
                    record.set("start_date", dates[0]);
                    record.set("end_date", dates[1]);
                }*/
                String s=matcher.group();
                Date date=DateUtils.parseDate(s,"yyyy年MM月dd");
                if(minDate==null){
                    minDate=date;
                }else{
                    if(DateUtils.compareDays(minDate,date)>0){
                        minDate=date;
                    }
                }

                if(maxDate==null){
                    maxDate=date;
                }else{
                    if(DateUtils.compareDays(maxDate,date)<0){
                        maxDate=date;
                    }
                }
            }
            if(minDate!=null){
                record.set("start_date",DateUtils.formatDate(minDate,"yyyy-MM-dd"));
                record.set("end_date",DateUtils.formatDate(maxDate,"yyyy-MM-dd"));
            }
            /*if(remark.indexOf("]至[")==-1){
                continue;
            }
            String str="";
            String str2="";
            try {
                str= remark.substring(remark.indexOf("]至[")-11,remark.indexOf("]至["));
                str2= remark.substring(remark.indexOf("]至[")+3,remark.indexOf("]至[")+14);
            }catch (Exception e){
                continue;
            }
            if(str.startsWith("[") && str2.endsWith("]")){
                String dateStr=str.substring(1,str.length());
                String dateStr2=str2.substring(0,str2.length()-1);
                try {
                    System.out.println("update fina_card_transactions set start_date='"+dateStr+"',end_date='"+dateStr2+"' where id='"+record.getStr("id")+"'; ");
                    Record record1=new Record();
                    record1.set("id",record.getStr("id"));
                    record1.set("start_date",dateStr);
                    record1.set("end_date",dateStr);
                    record1.set("describe",record.getStr("describe"));
                    returnList.add(record1);
                    continue;
                }catch (Exception e){
                    e.printStackTrace();
                    continue;
                }
            }else{
                if(true){
                    continue;
                }
            }*/

            if(StrKit.notBlank(record.getStr("start_date"))){
                Record record1=new Record();
                record1.set("id",record.getStr("id"));
                record1.set("start_date",record.getStr("start_date"));
                record1.set("end_date",record.getStr("end_date"));
                record1.set("describe",record.getStr("describe"));
                Date startDate= DateUtils.parseDate(record.getStr("start_date"));
                Date endDate= DateUtils.parseDate(record.getStr("end_date"));
                if(DateUtils.compareDays(endDate,startDate)<0){
                    record1.set("start_date",DateUtils.formatDate(endDate,"yyyy-MM-dd"));
                    record1.set("end_date",DateUtils.formatDate(startDate,"yyyy-MM-dd"));
                    //System.out.print("'"+record.getStr("id")+"',");
                }
                System.out.println("update fina_card_transactions set start_date='"+record1.getStr("start_date")+"',end_date='"+record1.getStr("end_date")+"' where id='"+record1.getStr("id")+"'; ");
                returnList.add(record1);
            }
        }
        renderJson(new DataTable<Record>(returnList));
    }

    public void findCardTransactionsRecordList(){
        String cardNumber=getPara("cardNumber");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        if(StrKit.isBlank(cardNumber)){
            renderJson(new DataTable<FinaCardTransactions>());
            return;
        }
        FinaMembershipCard membershipCard=finaMembershipCardService.getCardByNumber(cardNumber);
        if(membershipCard==null){
            renderJson(new DataTable<FinaCardTransactions>());
            return;
        }
        MainMembershipCardType cardType=mainMembershipCardTypeService.findById(membershipCard.getCardTypeId());
        MmsMember member = mmsMemberService.findById(membershipCard.getMemberId());
        List<FinaCardTransactions> transactionsList=finaCardTransactionsService.findRecordList(membershipCard.getId());

        Page<Record> transactionsPage=finaCardTransactionsService.getTransByParams(pageNumber,pageSize,membershipCard.getId(),null,null,null,null,null);

        if(transactionsPage.getList()!=null && transactionsPage.getList().size()>0){
            for(Record transactionsRecord:transactionsPage.getList()){
                Record record=new Record();
                transactionsRecord.set("deal_time",transactionsRecord.getDate("dealTime"));
                transactionsRecord.set("describe",transactionsRecord.getStr("describe"));
                if("1".equals(transactionsRecord.getStr("inOutFlag"))){
                    //充值
                    transactionsRecord.set("recharge_times",transactionsRecord.getDouble("times"));
                    transactionsRecord.set("recharge_amount",transactionsRecord.getDouble("amount"));
                    transactionsRecord.set("recharge_integrals",transactionsRecord.getDouble("integrals"));
                    transactionsRecord.set("recharge_bean_coupons",transactionsRecord.getDouble("beanCoupons"));

                    transactionsRecord.set("consume_times",0);
                    transactionsRecord.set("consume_amount",0);
                    transactionsRecord.set("consume_integrals",0);
                    transactionsRecord.set("consume_bean_coupons",0);
                }else if("2".equals(transactionsRecord.getStr("inOutFlag"))){
                    //消费
                    transactionsRecord.set("consume_times",transactionsRecord.getDouble("times"));
                    transactionsRecord.set("consume_amount",transactionsRecord.getDouble("amount"));
                    transactionsRecord.set("consume_integrals",transactionsRecord.getDouble("integrals"));
                    transactionsRecord.set("consume_bean_coupons",transactionsRecord.getDouble("beanCoupons"));

                    transactionsRecord.set("recharge_times",0);
                    transactionsRecord.set("recharge_amount",0);
                    transactionsRecord.set("recharge_integrals",0);
                    transactionsRecord.set("recharge_bean_coupons",0);
                }
                transactionsRecord.set("times_snapshot",transactionsRecord.getStr("timesSnapshot"));
                transactionsRecord.set("amount_snapshot",transactionsRecord.getStr("amountSnapshot"));
                transactionsRecord.set("integrals_snapshot",transactionsRecord.getStr("integralsSnapshot"));
                transactionsRecord.set("bean_coupons_snapshot",transactionsRecord.getStr("beanCouponsSnapshot"));
            }
        }
        renderJson(new DataTable<Record>(transactionsPage));
    }

    public void findCardTransactionsRecordListByCardType(){
        String cardTypeId=getPara("cardTypeId");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");

        List<Object> params = new ArrayList<>();
        String sqlSelect = "select a.id,a.card_id as cardId,a.expense_id as expenseId,a.type,a.in_out_flag,"
                + "a.amount,a.times,a.points,a.integrals,a.bean_coupons as beanCoupons,"
                + "IFNULL(a.amount_snapshot,'')as amount_snapshot,IFNULL(a.times_snapshot,'') as times_snapshot,"
                + "IFNULL(a.points_snapshot,'') as pointsSnapshot,IFNULL(a.integrals_snapshot,'')as integrals_snapshot,"
                + "IFNULL(a.bean_coupons_snapshot,'')as bean_coupons_snapshot,a.`describe`,a.is_hidden as isHidden,"
                + "a.deal_time,e.full_name,d.card_number ";

        String sql = "from fina_card_transactions a "
                + " left join fina_membership_card d on d.id=a.card_id" +
                " left join mms_member e on e.id=d.member_id  "
                + "where a.is_hidden = '0' and d.del_flag='0' ";
        if(StringUtils.isNotBlank(cardTypeId)){
            sql += "and d.card_type_id  = ? ";
            params.add(cardTypeId);
        }
        sql += " order by a.deal_time desc,a.times_snapshot asc,a.amount_snapshot asc,a.points_snapshot asc ";
        Page<Record> transactionsPage = Db.paginate(pageNumber,pageSize,sqlSelect,sql,params.toArray());

        if(transactionsPage.getList()!=null && transactionsPage.getList().size()>0){
            for(Record transactionsRecord:transactionsPage.getList()){
                if("1".equals(transactionsRecord.getStr("in_out_flag"))){
                    //充值
                    transactionsRecord.set("recharge_times",transactionsRecord.getDouble("times"));
                    transactionsRecord.set("recharge_amount",transactionsRecord.getDouble("amount"));
                    transactionsRecord.set("recharge_integrals",transactionsRecord.getDouble("integrals"));
                    transactionsRecord.set("recharge_bean_coupons",transactionsRecord.getDouble("beanCoupons"));

                    transactionsRecord.set("consume_times",0);
                    transactionsRecord.set("consume_amount",0);
                    transactionsRecord.set("consume_integrals",0);
                    transactionsRecord.set("consume_bean_coupons",0);
                }else if("2".equals(transactionsRecord.getStr("in_out_flag"))){
                    //消费
                    transactionsRecord.set("consume_times",transactionsRecord.getDouble("times"));
                    transactionsRecord.set("consume_amount",transactionsRecord.getDouble("amount"));
                    transactionsRecord.set("consume_integrals",transactionsRecord.getDouble("integrals"));
                    transactionsRecord.set("consume_bean_coupons",transactionsRecord.getDouble("beanCoupons"));

                    transactionsRecord.set("recharge_times",0);
                    transactionsRecord.set("recharge_amount",0);
                    transactionsRecord.set("recharge_integrals",0);
                    transactionsRecord.set("recharge_bean_coupons",0);
                }
            }
        }
        renderJson(new DataTable<Record>(transactionsPage));

    }

    public void findCardTransactionsRecordListByBaseId(){
        String baseId=getPara("baseId");
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");

        if(StrKit.isBlank(baseId)){
            renderJson(new DataTable<Record>(new ArrayList<>()));
            return;
        }

        Page<Record> transactionsPage=Db.paginate(pageNumber,pageSize,"select * "
                ,"from ( " +
                        " select a.*,mc.card_number,mm.full_name,ct.card_type from fina_card_transactions a INNER JOIN fina_expense_record b on a.expense_id=b.id \n" +
                        "INNER JOIN fina_membership_card mc on mc.id=a.card_id\n" +
                        "INNER JOIN mms_member mm on mm.id=mc.member_id\n" +
                        "INNER JOIN main_membership_card_type ct on ct.id=mc.card_type_id\n" +
                        "where a.is_hidden='0' and b.base_id=?\n" +
                        "UNION\n" +
                        "select c.*,mc2.card_number,mm2.full_name,ct2.card_type from fina_card_transactions c INNER JOIN fina_member_bill_detail d on c.expense_id=d.id INNER JOIN fina_member_settle_main e on e.id=d.main_id \n" +
                        "INNER JOIN fina_membership_card mc2 on mc2.id=c.card_id\n" +
                        "INNER JOIN mms_member mm2 on mm2.id=mc2.member_id\n" +
                        "INNER JOIN main_membership_card_type ct2 on ct2.id=mc2.card_type_id\n" +
                        "where c.is_hidden='0' and e.base_id=?\n" +
                        "UNION\n" +
                        "select f.*,mc3.card_number,mm3.full_name,ct3.card_type from fina_card_transactions f INNER JOIN fina_consume_record g on f.expense_id=g.id \n" +
                        "INNER JOIN fina_membership_card mc3 on mc3.id=f.card_id\n" +
                        "INNER JOIN mms_member mm3 on mm3.id=mc3.member_id\n" +
                        "INNER JOIN main_membership_card_type ct3 on ct3.id=mc3.card_type_id\n" +
                        "where f.is_hidden='0' and g.base_id=?\n" +
                        "order by deal_time desc,times_snapshot asc,integrals_snapshot asc,amount_snapshot asc,bean_coupons_snapshot asc,points_snapshot asc " +
                        " ) t"
                ,baseId,baseId,baseId);
        if(transactionsPage.getList()!=null && transactionsPage.getList().size()>0){
            for(Record transactionsRecord:transactionsPage.getList()){
                if("1".equals(transactionsRecord.getStr("in_out_flag"))){
                    //充值
                    transactionsRecord.set("recharge_times",transactionsRecord.getDouble("times"));
                    transactionsRecord.set("recharge_amount",transactionsRecord.getDouble("amount"));
                    transactionsRecord.set("recharge_integrals",transactionsRecord.getDouble("integrals"));
                    transactionsRecord.set("recharge_bean_coupons",transactionsRecord.getDouble("beanCoupons"));

                    transactionsRecord.set("consume_times",0);
                    transactionsRecord.set("consume_amount",0);
                    transactionsRecord.set("consume_integrals",0);
                    transactionsRecord.set("consume_bean_coupons",0);
                }else if("2".equals(transactionsRecord.getStr("in_out_flag"))){
                    //消费
                    transactionsRecord.set("consume_times",transactionsRecord.getDouble("times"));
                    transactionsRecord.set("consume_amount",transactionsRecord.getDouble("amount"));
                    transactionsRecord.set("consume_integrals",transactionsRecord.getDouble("integrals"));
                    transactionsRecord.set("consume_bean_coupons",transactionsRecord.getDouble("beanCoupons"));

                    transactionsRecord.set("recharge_times",0);
                    transactionsRecord.set("recharge_amount",0);
                    transactionsRecord.set("recharge_integrals",0);
                    transactionsRecord.set("recharge_bean_coupons",0);
                }

            }
        }
        renderJson(new DataTable<Record>(transactionsPage));
    }

    public void cardDetailIndex(){

        List<MainMembershipCardType> typeList=mainMembershipCardTypeService.findList(new MainMembershipCardType());

        List<MainBase> baseList=mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        setAttr("typeList",typeList);
        render("cardDetailIndex.html");
    }

    /**
     * 充值模板导入
     */
    public void rechargeImport(){
        UploadFile uploadFile = getFile();
        Ret returnRet = Ret.fail("msg","导入失败");
        if(uploadFile == null){
        	returnRet = Ret.fail("msg","上传文件不存在，请检查!");
        }
        boolean flag = false;
        try{
        	returnRet =  finaCardRechargeService.importRecharge(uploadFile.getFile(),uploadFile.getFileName(), AuthUtils.getUserId());
        }catch (Exception e){
            logger.error("模板批量充值导入是否成功标识:[{}]",flag);
            logger.error("模板批量充值导入失败:[{}],[{}]",e.getMessage(),e);
            e.printStackTrace();
        }
        if(uploadFile.getFile().exists()){
            uploadFile.getFile().delete();
        }
       renderJson(returnRet);
    }
    
    /**
     * 导出excel
     */
    public void exportData(){
        final String cardId = getPara("cardId");
        final String cardNumber = getPara("cardNumber");
        Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
    	if(StrKit.notBlank(cardId)) {
    		columns.eq("card_id", cardId);
    	}
    	final String sql = "select business_type,give_date,create_time,count_value,CAST(proportion_value as CHAR)proportion_value,"
			+ "integral_value from fina_card_integral_record where del_flag=? and card_id=? order by give_date desc";
    	List<Record> recordList = Db.find(sql, DelFlag.NORMAL, cardId);
        if(recordList==null || recordList.size()==0){
            renderJson(Ret.fail("msg","未查询到积分明细记录"));
            return;
        }

        String[] title={"类型","时间","计算天数","计算比例","积分"};
        String fileName = cardNumber + "积分明细记录.xls";
        String sheetName = cardNumber + "积分明细记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);
            final String businessType = record.getStr("business_type")!=null?record.getStr("business_type"):"无";
            final String giveDate = record.getDate("give_date")!=null?DateUtils.formatDate(record.getDate("give_date"), "yyyy-MM-dd"):"无";
            final String createTime = record.getDate("create_time")!=null?DateUtils.formatDate(record.getDate("create_time"), "yyyy-MM-dd HH:mm:ss"):"无";
            final String countValue = record.getStr("count_value")!=null?record.getStr("count_value"):"0";
            final String proportionValue = record.getStr("proportion_value")!=null?record.getStr("proportion_value"):"0";
            System.out.println("proportionValue==="+proportionValue);
            final String integralValue = record.getStr("integral_value")!=null?record.getStr("integral_value"):"0";
            
            if("system_give".equals(businessType)){
            	content[i][0]="系统赠送";
            	content[i][1]=giveDate;
            }else if("mall_exchange".equals(businessType)){
            	content[i][0]="积分兑换";
            	content[i][1]=createTime;
            }
            content[i][2]=countValue;
            content[i][3]=proportionValue;
        	content[i][4]=integralValue;
        }
        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
