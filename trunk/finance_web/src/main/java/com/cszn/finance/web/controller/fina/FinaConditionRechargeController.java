package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaConditionRechargeSchemeDetailService;
import com.cszn.integrated.service.api.fina.FinaConditionRechargeSchemeService;
import com.cszn.integrated.service.entity.enums.ConditionRechargeType;
import com.cszn.integrated.service.entity.enums.ConditionType;
import com.cszn.integrated.service.entity.fina.FinaConditionRechargeScheme;
import com.cszn.integrated.service.entity.fina.FinaConditionRechargeSchemeDetail;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RequestMapping(value="/fina/conditionRecharge", viewPath="/modules_page/finance/fina/conditionRecharge")
public class FinaConditionRechargeController extends BaseController {

    @Inject
    FinaConditionRechargeSchemeService finaConditionRechargeSchemeService;
    @Inject
    FinaConditionRechargeSchemeDetailService finaConditionRechargeSchemeDetailService;


    public void index(){
        render("schemeIndex.html");
    }

    @Clear(LogInterceptor.class)
    public void pageTable(){
        FinaConditionRechargeScheme rechargeScheme = getBean(FinaConditionRechargeScheme.class, "", true);
        Page<FinaConditionRechargeScheme> modelPage = finaConditionRechargeSchemeService.paginateByCondition(rechargeScheme, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaConditionRechargeScheme>(modelPage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
        setAttr("conditionTypes", ConditionType.values());
        setAttr("conditionRechargeTypes", ConditionRechargeType.values());
        render("schemeForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String schemeId = getPara("id");
        setAttr("model", finaConditionRechargeSchemeService.findById(schemeId));
        setAttr("list",finaConditionRechargeSchemeDetailService.findListBySchemeId(schemeId));

        setAttr("conditionTypes", ConditionType.values());
        setAttr("conditionRechargeTypes", ConditionRechargeType.values());
        render("schemeForm.html");
    }

    /**
     * 保存方法
     */
    public void save() {
        int ruleCount = getParaToInt("ruleCount");
        FinaConditionRechargeScheme rechargeScheme = getBean(FinaConditionRechargeScheme.class, "", true);
        if(StrKit.notBlank(rechargeScheme.getId())){
            rechargeScheme.setUpdateBy(AuthUtils.getUserId());
        }else{
            rechargeScheme.setCreateBy(AuthUtils.getUserId());
        }
        List<FinaConditionRechargeSchemeDetail> detailList = new ArrayList<FinaConditionRechargeSchemeDetail>(ruleCount);
        if(ruleCount>0){
            for (int i = 1; i <= ruleCount; i++) {
                final FinaConditionRechargeSchemeDetail schemeRule = getBean(FinaConditionRechargeSchemeDetail.class, "ruleList["+i+"]");
                if(StrKit.notBlank(schemeRule.getRechargeType())){

                    if(StrKit.notBlank(schemeRule.getId())){
                        schemeRule.setUpdateBy(AuthUtils.getUserId());
                    }else{
                        schemeRule.setCreateBy(AuthUtils.getUserId());
                    }
                    detailList.add(schemeRule);
                }
            }
        }
        if(detailList.size()==0){
            renderJson(Ret.fail("msg","请最少添加一条规则"));
            return;
        }
        boolean flag = finaConditionRechargeSchemeService.saveConditionRechargeScheme(rechargeScheme, detailList);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void del(){
        FinaConditionRechargeScheme rechargeScheme = getBean(FinaConditionRechargeScheme.class, "", true);
        rechargeScheme.setUpdateBy(AuthUtils.getUserId());
        boolean flag = finaConditionRechargeSchemeService.delConditionRechargeScheme(rechargeScheme);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delDetail(){
        FinaConditionRechargeSchemeDetail detail = getBean(FinaConditionRechargeSchemeDetail.class, "", true);
        if(detail!=null && StrKit.notBlank(detail.getId())){
            detail.setUpdateBy(AuthUtils.getUserId());
            detail.setUpdateDate(new Date());
            detail.setDelFlag("1");
            if(detail.update()){
                renderJson(Ret.ok("msg", "操作成功!"));
            }
        }else{
            renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
        }
    }

}
