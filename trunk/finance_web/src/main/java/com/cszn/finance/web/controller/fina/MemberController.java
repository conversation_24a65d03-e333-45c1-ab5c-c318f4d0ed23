/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.api.main.MainAreaService;
import com.cszn.integrated.service.entity.main.MainArea;
import org.apache.commons.lang3.StringUtils;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardCollectService;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.member.MmsMemberLinkService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.member.MmsMemberLink;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by LiangHuiLing on 2022年8月22日
 * 会员档案控制器
 * MemberController
 */
@RequestMapping(value="/fina/member", viewPath="/modules_page/finance/fina/member")
public class MemberController extends BaseController {

	private static Logger logger = LoggerFactory.getLogger(com.jfinal.ext.interceptor.LogInterceptor.class);

	@Inject
	private MmsMemberService mmsMemberService;
	@Inject
	private MmsMemberLinkService mmsMemberLinkService;
	@Inject
	private MainAreaService areaService;
	@Inject
    private UserService userService;
	@Inject
    private FinaCardCollectService finaCardCollectService;
	@Inject
    private MainBranchOfficeService mainBranchOfficeService;
	
	public void index(){
		render("memberIndex.html");
	}
	
	public void linkIndex(){
    	final String memberId = getPara("id");
    	setAttr("memberId", memberId);
    	render("linkIndex.html");
    }
	
	public void consumeIndex(){
		final String memberId = getPara("id");
		setAttr("memberId", memberId);
		render("consumeIndex.html");
	}
	
	@Clear(LogInterceptor.class)
	public void pageTable(){
		MmsMember model = getBean(MmsMember.class, "", true);
    	Page<Record> modelPage = mmsMemberService.archivesPage(getParaToInt("page", 1), getParaToInt("limit", 10), model);
    	renderJson(new DataTable<Record>(modelPage));
	}
	
	@Clear(LogInterceptor.class)
    public void linkPage(){
    	MmsMemberLink model = getBean(MmsMemberLink.class,"",true);
    	Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
    	if(StrKit.notBlank(model.getMemberId())) {
    		columns.eq("member_id", model.getMemberId());
    	}
    	if(StrKit.notBlank(model.getLinkType())) {
    		columns.eq("link_type", model.getLinkType());
    	}
    	if(StrKit.notBlank(model.getLinkName())) {
    		columns.likeAppendPercent("link_name", model.getLinkName());
    	}
    	Page<MmsMemberLink> modelPage = mmsMemberLinkService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_date desc");
    	renderJson(new DataTable<MmsMemberLink>(modelPage));
    }
	
	@Clear(LogInterceptor.class)
	public void consumePage(){
		Page<Record> modelPage = mmsMemberService.consumePage(getParaToInt("page", 1), getParaToInt("limit", 10), getPara("memberId"));
		renderJson(new DataTable<Record>(modelPage));
	}
	
	/**
     * 添加页面方法
     */
    public void add() {
    	final MmsMember member = getBean(MmsMember.class, "", true);
    	List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
    	setAttr("hrmDomain",Global.hrmUrl);
    	setAttr("commonUpload",Global.commonUpload);
		setAttr("member", member);
		setAttr("branchOfficeList",branchOfficeList);
        render("memberForm.html");
    }
    
	/**
     * 添加联系人页面方法
     */
    public void addLink(){
    	MmsMemberLink model = getBean(MmsMemberLink.class,"",true);
    	setAttr("model",model);
    	render("linkForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
    	String id = getPara("id");
    	MmsMember member = mmsMemberService.get(id);
		if(member != null){
			if(StringUtils.isNotBlank(member.getProvince())) {
				MainArea area = areaService.findById(member.getProvince());
				if(area != null)setAttr("province",area.getName());
			}
			if(StringUtils.isNotBlank(member.getCity())) {
				MainArea area = areaService.findById(member.getCity());
				if(area != null)setAttr("city",area.getName());
			}
			if(StringUtils.isNotBlank(member.getTown())) {
				MainArea area = areaService.findById(member.getTown());
				if(area != null)setAttr("town",area.getName());
			}
			if(StringUtils.isNotBlank(member.getStreet())) {
				MainArea area = areaService.findById(member.getStreet());
				if(area != null)setAttr("street",area.getName());
			}
			if(StringUtils.isNotBlank(member.getSalesId())){
                User user = userService.findById(member.getSalesId());
                setAttr("user",user);
            }
			List<MmsMemberLink> linkList = mmsMemberLinkService.findList(id);
			setAttr("linkList",linkList);
		}
		List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
		setAttr("hrmDomain",Global.hrmUrl);
		setAttr("commonUpload",Global.commonUpload);
		setAttr("member", member);
		setAttr("branchOfficeList",branchOfficeList);
        render("memberForm.html");
    }
    
	/**
     * 修改联系人页面方法
     */
    public void editLink(){
    	MmsMemberLink model = getBean(MmsMemberLink.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model = mmsMemberLinkService.findById(model.getId());
    	}
    	setAttr("model",model);
    	render("linkForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
		Ret returnRet = Ret.fail("msg", "保存失败!");
    	final String userId = AuthUtils.getUserId();
    	MmsMember member = getBean(MmsMember.class,"member",true);
		//姓名的姓处理空格
		if(StrKit.notBlank(member.getSurname()) && member.getSurname().contains(" ")){
			member.setSurname(member.getSurname().replaceAll("\\s+", ""));
		}
		//姓名处理空格
		if(StrKit.notBlank(member.getFullName()) && member.getFullName().contains(" ")){
			member.setFullName(member.getFullName().replaceAll("\\s+", ""));
		}
		//身份证处理空格
		if(StrKit.notBlank(member.getIdcard()) && member.getIdcard().contains(" ")){
			member.setIdcard(member.getIdcard().replaceAll("\\s+", ""));
		}
		//调用旅居身份证校验接口
		Map<String,String> params=new HashMap<>();
		params.put("name", member.getFullName());//卡主姓名
		params.put("idCardType", member.getIdcardType());//卡主证件类型
		params.put("idCard", member.getIdcard());//卡主身份证
		params.put("userId", userId);//操作人用户id
		params.put("channel", "AddMemberInfo");//渠道来源
		params.put("key", "40FA2FDE-9B79-40EC-8147-50F1C7EEF7DE");//key
		final String resultStr = HttpClientsUtils.httpPostForm(Global.checkIdCardUrl,params,null,null);
		logger.info("resultStr==="+resultStr);
		JSONObject returnJson = JSONObject.parseObject(resultStr);
		final int returnType = returnJson.getIntValue("Type");
		final String returnMsg = returnJson.getString("Msg");
		logger.info("身份证校验返回returnType==="+returnType);
		logger.info("身份证校验返回returnMsg==="+returnMsg);
		//姓名、身份证校验成功
		if(returnType!=1){
			returnRet = Ret.fail("msg", returnMsg);
			renderJson(returnRet);
		}else{
			String flag = mmsMemberService.saveMmsMember(member, userId);
			if("suc".equals(flag)){
				returnRet = Ret.ok("msg", "保存成功");
			}else if("".equals(flag)){
				returnRet = Ret.fail("msg", "身份证号不可重复");
			}
			renderJson(returnRet);
		}
    }
    
    public void saveLink(){
    	MmsMemberLink model = getBean(MmsMemberLink.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    	}else{
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setCreateBy(AuthUtils.getUserId());
    	}
    	boolean flag=mmsMemberLinkService.saveMemberLink(model);
    	if(flag){
    		renderJson(Ret.ok("msg","操作成功"));
    	}else{
    		renderJson(Ret.fail("msg","操作失败"));
    	}
    }
    
    /**
     * 作废方法
     */
    public void del(){
    	String id = getPara("id");
    	if(StrKit.isBlank(id)){
    		renderJson(Ret.fail("msg", "会员id为空,作废失败"));
    		return;
    	}
		boolean flag = mmsMemberService.delMmsMember(id, AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "作废成功"));
		}else{
			renderJson(Ret.fail("msg", "作废失败"));
		}
    }
    
    /**
     * 删除方法
     */
    public void delete(){
    	String id = getPara("id");
    	if(StrKit.isBlank(id)){
    		renderJson(Ret.fail("msg", "会员id为空,删除失败"));
    		return;
    	}
    	//统计未作废的会员卡数量
    	final int unDelCount = Db.queryInt("select count(id)unDelCount from fina_membership_card where del_flag='0' and member_id=?", id);
    	if(unDelCount>0){
    		renderJson(Ret.fail("msg", "还有未作废的会员卡，删除失败!"));
    		return;
    	}
    	//统计已作废的会员卡数量
    	final int alDelCount = Db.queryInt("select count(id)alDelCount from fina_membership_card where del_flag='1' and member_id=?", id);
    	if(alDelCount>0){
    		renderJson(Ret.fail("msg", "还有已作废的会员卡，删除失败!"));
    		return;
    	}
    	if(mmsMemberService.deleteById(id)){
    		renderJson(Ret.ok("msg", "删除成功"));
    	}else{
    		renderJson(Ret.fail("msg", "删除失败"));
    	}
    }
    
    /**
     * 删除联系人方法
     */
    public void delLink(){
    	MmsMemberLink model = getBean(MmsMemberLink.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateDate(new Date());
    		if(mmsMemberLinkService.update(model)){
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg","操作失败"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","id参数不能为空,操作失败"));
    	}
    }
}
