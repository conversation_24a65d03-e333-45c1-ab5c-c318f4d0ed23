package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.*;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.*;
import com.cszn.integrated.service.api.main.*;
import com.cszn.integrated.service.api.member.MmsBlackListService;
import com.cszn.integrated.service.api.member.MmsMemberLinkService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.enums.RechargeWay;
import com.cszn.integrated.service.entity.enums.SchemeType;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.*;
import com.cszn.integrated.service.entity.member.MmsBlackList;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.member.MmsMemberLink;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.status.*;
import com.cszn.integrated.service.entity.sys.User;
import com.google.common.collect.Maps;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Descript 会员卡管理
 * <AUTHOR>
 * @Date 2019-04-09
 */
@RequestMapping(value="/fina/cardmanager", viewPath="/modules_page/finance/fina/membershipCardManager")
public class MembershipCardManagerController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private FinaCardGiveRuleService finaCardGiveRuleService;
    @Inject
    private MmsMemberService mmsMemberService;
    @Inject
    private FinaCardRuleRelaService finaCardRuleRelaService;
    @Inject
    private UserService userService;
    @Inject
    private MainBranchOfficeService mainBranchOfficeService;
    @Inject
    private MainAreaService areaService;
    @Inject
    private MainCardGiveSchemeService mainCardGiveSchemeService;
    @Inject
	private MainCardDeductSchemeService mainCardDeductSchemeService;
    @Inject
    private MainCardYearLimitService mainCardYearLimitService;
    @Inject
    private MainCardGiveSchemeRuleService mainCardGiveSchemeRuleService;
    @Inject
    private MainCardGiveRecordService mainCardGiveRecordService;
    @Inject
    private FinaCardRechargeService finaCardRechargeService;
    @Inject
    private FinaCardAccountService finaCardAccountService;
    @Inject
    private MainBaseService mainBaseService;
    @Inject
    private FinaSettleDetailService finaSettleDetailService;
    @Inject
    private DictService dictService;
    @Inject
    private FinaCardMonthBalanceService finaCardMonthBalanceService;
    @Inject
    private MmsBlackListService mmsBlackListService;
    @Inject
    private FinaCardTransactionsService finaCardTransactionsService;
    @Inject
    private FinaMembershipCardCheckinConfigService finaMembershipCardCheckinConfigService;
    @Inject
    private MainPaymentWaysService mainPaymentWaysService;
    @Inject
    private FinaCardCollectService finaCardCollectService;
    @Inject
    private MainContractTypeService mainContractTypeService;
    @Inject
    private FinaCardMonthlyEndService finaCardMonthlyEndService;
    @Inject
	private MmsMemberLinkService mmsMemberLinkService;
    @Inject
    MainSyncRecordService mainSyncRecordService;
    @Inject
    FinaExpenseRecordService finaExpenseRecordService;
    @Inject
    FinaMemberBillDetailService finaMemberBillDetailService;
    @Inject
    FinaMemberSettleMainService finaMemberSettleMainService;
    @Inject
    PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    PersOrgService persOrgService;
    @Inject
    FinaConditionRechargeSchemeService finaConditionRechargeSchemeService;

    /**
     * 跳转会员卡管理界面
     */
    public void index(){
        List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
        List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
        setAttr("branchOfficeList",branchOfficeList);
        setAttr("typeList",typeList);
        render("managerIndex.html");
    }

    /**
     * 消费卡查询界面
     */
    public void expendIndex(){
        render("expendIndex.html");
    }
    
    /**
     * 扣卡日报表界面
     */
    public void deductCardDailyReport(){
    	setAttr("baseList",mainBaseService.findBaseList());
    	render("deductCardDailyReport.html");
    }
    
    /**
     * 扣卡月报表界面
     */
    public void deductCardMonthReport(){
    	setAttr("baseList",mainBaseService.findBaseList());
    	render("deductCardMonthReport.html");
    }
    
    /**
     * 基地扣卡汇总表界面
     */
    public void baseDeductCardSummary(){
    	render("baseDeductCardSummary.html");
    }
    
    /**
     * 基地扣卡卡类别汇总表界面
     */
    public void baseDeductCardSummaryCardType(){
    	String baseId = getPara("baseId");
    	String deductMonth = getPara("deductMonth");
    	setAttr("baseId",baseId);
    	setAttr("deductMonth",deductMonth);
    	setAttr("baseList",mainBaseService.findBaseList());
    	render("baseDeductCardSummaryCardType.html");
    }
    
    /**
     * 基地扣卡详细汇总表界面
     */
    public void baseDeductCardSummaryDetail(){
    	String baseId = getPara("baseId");
    	String deductMonth = getPara("deductMonth");
    	String cardTypeId = getPara("cardTypeId");
    	setAttr("baseId",baseId);
    	setAttr("deductMonth",deductMonth);
    	setAttr("cardTypeId",cardTypeId);
    	setAttr("baseList",mainBaseService.findBaseList());
    	setAttr("typeList",mainMembershipCardTypeService.findList(null));
    	render("baseDeductCardSummaryDetail.html");
    }
    
    public void getAllBase(){
        renderJson(Ret.ok("msg", "操作成功!").set("baseList", mainBaseService.findBaseList()));
    }
    
    /**
     * 基地扣卡明细汇总表列表
     */
    @Clear(LogInterceptor.class)
    public void baseDeductCardSummaryDetailList(){
    	String baseId = getPara("baseId");
    	String deductMonth = getPara("deductMonth");
    	String cardTypeId = getPara("cardTypeId");
    	List<Record> list = finaSettleDetailService.baseDeductCardSummaryDetailList(baseId, deductMonth, cardTypeId);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }
    
    /**
     * 基地扣卡卡类别汇总表列表
     */
    @Clear(LogInterceptor.class)
    public void baseDeductCardSummaryCardTypeList(){
    	String baseId = getPara("baseId");
    	String deductMonth = getPara("deductMonth");
    	List<Record> list = finaSettleDetailService.baseDeductCardSummaryCardTypeList(baseId, deductMonth);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }
    
    /**
     * 基地扣卡汇总表列表
     */
    @Clear(LogInterceptor.class)
    public void baseDeductCardSummaryList(){
    	List<Record> list = finaSettleDetailService.baseDeductCardSummaryList();
    	renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }
    
    /**
     * 扣卡月报列表
     */
    @Clear(LogInterceptor.class)
    public void deductCardMonthReportList(){
    	String baseId = getPara("baseId");
    	String deductMonth = getPara("deductMonth");
    	List<Record> list = finaSettleDetailService.deductCardMonthReportList(baseId, deductMonth);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }
    
    
    /**
     * 扣卡日报列表
     */
    @Clear(LogInterceptor.class)
    public void deductCardDailyReportList(){
    	String baseId = getPara("baseId");
    	String deductDate = getPara("deductDate");
    	List<Record> list = finaSettleDetailService.deductCardDailyReportList(baseId, deductDate);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list));
    }
    
    /**
     * 消费卡分页查询
     */
    public void findExpendPage(){
    	FinaMembershipCard card = getBean(FinaMembershipCard.class, "", true);
        Page<Record> expendPage = finaMembershipCardService.findExpendPage(getParaToInt("page", 1), getParaToInt("limit", 10),card);
        renderJson(new DataTable<Record>(expendPage));
    }

    /**
     * 导出excel
     */
    public void exportRecord(){
        String cardNumber = getPara("cardNumber");
        List<Record> recordList=finaMembershipCardService.findExpendList(cardNumber);
        if(recordList==null || recordList.size()==0){
            renderJson(Ret.fail("msg","未查询到消费卡记录"));
            return;
        }

        String[] title={"会员卡号","姓名","合同号","签订日期","卡类别","充值金额","卡天数","收款帐号","卡说明","收据号","经办人","备注"};
        String fileName="消费卡记录.xls";
        String sheetName = "消费卡记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);

            content[i][0]=record.getStr("cardNumber");
            content[i][1]=record.getStr("fullName");
            content[i][2]="";
            content[i][3]=record.getStr("openTime");
            content[i][4]=record.getStr("cardType");
            content[i][5]=record.getStr("payAmount");
            content[i][6]=record.getStr("consumeTimes");
            content[i][7]=record.getStr("accountName");
            content[i][8]=record.getStr("describe");
            content[i][9]="";
            content[i][10]=record.getStr("operatorName");
            content[i][11]="";
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 会员卡列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){

        FinaMembershipCard card = getBean(FinaMembershipCard.class,"",true);
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        String province = getPara("province");
        String city = getPara("city");
        String town = getPara("town");
        String street = getPara("street");
        String noDealStartDate = getPara("noDealStartDate");
        String noDealEndDate = getPara("noDealEndDate");
        String nearExpireStartYear = getPara("nearExpireStartYear");
        String nearExpireEndYear = getPara("nearExpireEndYear");
        String contractNumber = getPara("contractNumber");
        String orderBy = getPara("orderBy");
        Page<Record> page = finaMembershipCardService.findList(getParaToInt("page"),getParaToInt("limit"),card,fullName,idcard,province,city,town,street,noDealStartDate,noDealEndDate,nearExpireStartYear,nearExpireEndYear,orderBy);
        if(page.getList()!=null && page.getList().size()>0){
            for(Record record : page.getList()){
                final String cardId = record.getStr("id");
                final String deductSchemeId = record.getStr("deductSchemeId");
                final String longDeductSchemeId = record.getStr("longDeductSchemeId");
                double price = 0.0;
                if(StrKit.notBlank(deductSchemeId)){
                    final Double tempPrice = Db.queryDouble("select ROUND(SUM(IFNULL(pay_amount,0))/SUM(IFNULL(average_days,0)),2)price from fina_card_recharge " +
                        "where type='2' and is_income='1' and del_flag='0' and cancel_flag='0' and is_review='1' and card_id=?", cardId);
                    if(tempPrice!=null && tempPrice>0){
                        price = tempPrice;
                    }
                }else if(StrKit.notBlank(longDeductSchemeId)){
                    price = Db.queryDouble("select IFNULL(price,0)price from main_card_deduct_scheme where id=?", longDeductSchemeId);
                }
                record.set("price",price);
            }
        }
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value!=null && value instanceof Double){
                    return Double.toString((Double)value);
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(page),valueFilter));
    }


    /**
     * 会员卡作废
     */
    public void delete(){

        String id = getPara("id");
        if(StrKit.isBlank(id)){
        	renderJson(Ret.fail("msg", "id参数为空,作废失败"));
        	return;
        }
        FinaMembershipCard memberCard = finaMembershipCardService.findById(id);
        if(memberCard!=null){
        	//统计旅居结算账单数量
        	final int settleCount = Db.queryInt("select count(id)settleCount from fina_settle_detail where del_flag='0' and settle_status='0' and card_number<=?", memberCard.getCardNumber());
        	if(settleCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的旅居账单，作废失败!"));
        		return;
        	}
        	
        	//统计旅居结算账单数量
        	final int touristCount = Db.queryInt("select count(a.id) touristCount from fina_expense_record_tourist_settle_detail a " +
                    " where a.del_flag='0' and a.is_settle='0' and a.card_id=? ", memberCard.getId());
        	if(touristCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的旅游团账单，作废失败!"));
        		return;
        	}
        	
        	//统计养老长住账单数量
        	final int pensionCount = Db.queryInt("select count(id)pensionCount from fina_member_bill_detail where del_flag='0' and status='wait_settlement' and account_number=?", memberCard.getCardNumber());
        	if(pensionCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的养老长住账单，作废失败!"));
        		return;
        	}
        	
        	//统计旅居违约账单数量
        	final int punishCount = Db.queryInt("select count(id)punishCount from fina_punish_record where del_flag='0' and status='0' and card_number=?", memberCard.getCardNumber());
        	if(punishCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的旅居违约账单，作废失败!"));
        		return;
        	}
        	if(finaMembershipCardService.delFinaMembershipCard(id, AuthUtils.getUserId())){
        		renderJson(Ret.ok("msg", "作废成功"));
        	}else{
        		renderJson(Ret.fail("msg", "作废失败"));
        	}
        }else{
        	renderJson(Ret.fail("msg", "找不到该会员卡,作废失败"));
        	return;
        }
    }


    /**
     * 跳转会员卡 新增/修改界面
     */
    public void form(){

    	boolean modFlag = false;
        String id = getPara("id");
        //如果值是check为查看
        String type=getPara("type");
        FinaMembershipCard card = null;
        List<FinaCardCollect> collectList = new ArrayList<FinaCardCollect>();
        if(StringUtils.isNotBlank(id)){
            card = finaMembershipCardService.findById(id);
            collectList = finaCardCollectService.findList(id);
            //获取会员卡规则id
            if(card!=null){
                String ruleId=finaCardRuleRelaService.findRuleIdByCardId(id);
                setAttr("ruleId",ruleId);
                MmsMember member = mmsMemberService.get(card.getMemberId());
                //处理区域
                if(member != null){
                    if(StringUtils.isNotBlank(member.getProvince())) {
                        MainArea area = areaService.findById(member.getProvince());
                        if(area != null)setAttr("province",area.getName());
                    }
                    if(StringUtils.isNotBlank(member.getCity())) {
                        MainArea area = areaService.findById(member.getCity());
                        if(area != null)setAttr("city",area.getName());
                    }
                    if(StringUtils.isNotBlank(member.getTown())) {
                        MainArea area = areaService.findById(member.getTown());
                        if(area != null)setAttr("town",area.getName());
                    }
                    if(StringUtils.isNotBlank(member.getStreet())) {
                        MainArea area = areaService.findById(member.getStreet());
                        if(area != null)setAttr("street",area.getName());
                    }
                    List<MmsMemberLink> linkList = mmsMemberLinkService.findList(member.getId());
        			setAttr("linkList",linkList);
                }
                setAttr("member",member);
                if(StringUtils.isNotBlank(card.getOperator())){
                    User user = userService.findById(card.getOperator());
                    setAttr("user",user);
                }
                MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
                setAttr("cardType",cardType);
                card.setBeanCoupons(card.getAllBeanCoupons());
                
//                Record record = finaMembershipCardService.getCardByCardNumber(null, card.getCardNumber(), card.getDelFlag());
//                if(record==null){
//                	modFlag = true;
//                }
//                String returnCardStatus = record.getStr("returnCardStatus");
                Map<String,Double> cardLockMap = finaMembershipCardService.getCardLockInfo(card.getCardNumber());
                Double lockBalance = cardLockMap.get("lockBalance");
                Double lockConsumeTimes = cardLockMap.get("lockConsumeTimes");
                Double lockIntegrals = cardLockMap.get("lockIntegrals");
                if(ReturnCardStatus.APPLY.equals(card.getReturnCardStatus())){
                	modFlag = true;
                }
                if(ReturnCardStatus.FINISH.equals(card.getReturnCardStatus())){
                	modFlag = true;
                }
                if(lockBalance!=null && lockBalance>0) {
                	modFlag = true;
                }
                if(lockConsumeTimes!=null && lockConsumeTimes>0) {
                	modFlag = true;
                }
                if(lockIntegrals!=null && lockIntegrals>0) {
                	modFlag = true;
                }
                //
                Record emp = Db.findFirst("select id,full_name as fullName,work_num as workNum from pers_org_employee where staff_card_id=? and del_flag='0' ",card.getId());
                setAttr("emp",emp);
            }

        }
        List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
        List<FinaCardGiveRule> ruleList = finaCardGiveRuleService.getRules();

        List<MainCardDeductScheme> sojournSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.SOJOURN_SCHEME.getKey());
        List<MainCardDeductScheme> longSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.LONG_SCHEME.getKey());
        List<MainCardYearLimit> cardYearLimitList=mainCardYearLimitService.findCardYearLimitList();
        List<FinaCardAccount> accountList = finaCardAccountService.findList(null);
        List<MainPaymentWays> payWayList = mainPaymentWaysService.findList();
        List<MainContractType> contractTypeList = mainContractTypeService.findList();
        List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();

        setAttr("modFlag",modFlag);
        setAttr("card",card);
        setAttr("typeList",typeList);
        setAttr("ruleList",ruleList);
        //分期赠送方案
        List<MainCardGiveScheme> giveSchemeList = mainCardGiveSchemeService.findListOnUse("1");
        setAttr("giveSchemeList",giveSchemeList);
        //分期充值方案
        List<MainCardGiveScheme> rechargeSchemeList = mainCardGiveSchemeService.findListOnUse("2");
        setAttr("rechargeSchemeList",rechargeSchemeList);
        setAttr("conditionRechargeSchemeList",finaConditionRechargeSchemeService.getConditionRechargeSchemeList());

        setAttr("sojournSchemeList",sojournSchemeList);
        setAttr("longSchemeList",longSchemeList);
        setAttr("type",type);
        setAttr("cardYearLimitList",cardYearLimitList);
        setAttr("accountList",accountList);
        setAttr("payWayList",payWayList);
        setAttr("collectList",collectList);
        setAttr("contractTypeList",contractTypeList);
        setAttr("branchOfficeList",branchOfficeList);


        List<FinaMembershipCardCheckinConfig> configList=finaMembershipCardCheckinConfigService.findCheckinConfigListByCardId(id);
        setAttr("configList",configList);

        List<MainBase> baseList=mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        
        List<FinaMembershipCard> cardList=finaMembershipCardService.findCardListByParentId("0");
        setAttr("cardList",cardList);
        
        if("change".equals(type)){
            setAttr("cardId",getPara("cardId"));
            setAttr("memberId",getPara("memberId"));
            render("changeOwnForm.html");
        }else{
            setAttr("userId",AuthUtils.getUserId());
            setAttr("uploadDomain",Global.commonUpload);
            setAttr("hrmDomain",Global.hrmUrl);
            setAttr("notFitBases", Db.queryStr("select group_concat(base_id)baseIds from fina_membership_card_base where card_id=? group by card_id", id));
            render("managerForm.html");
        }
    }

    public void giveSchemePreviewIndex(){
        String openTime=getPara("openTime");
        String giveSchemeId=getPara("giveSchemeId");

        setAttr("openTime",openTime);
        setAttr("giveSchemeId",giveSchemeId);

        render("giveSchemePreview.html");
    }

    public void giveSchemePreviewTableList(){
        Date openTime=getParaToDate("openTime");
        String giveSchemeId=getPara("giveSchemeId");

        List<Record> giveSchemeRecordList=mainCardGiveSchemeRuleService.getCardGiveSchemeRuleResult(openTime,giveSchemeId);
        renderJson(new DataTable<Record>(giveSchemeRecordList));
    }


    /**
     * 通过id获取会员卡信息
     *//*
    public void findCardById(){
        String id=getPara("id");
        FinaMembershipCard card=finaMembershipCardService.findById(id);
        renderJson(Ret.ok("data",card));
    }*/




    /**
     * 根据会员卡号获取会员卡信息
     */
    @Clear(LogInterceptor.class)
    public void getCardByCardNumber(){
        String id = getPara("id");
        String cardNumber = getPara("cardNumber");
        String flag = getPara("flag");
        String delFlag = getPara("delFlag");

        if(StringUtils.isNotBlank(flag)){
            if(StringUtils.isBlank(id)){
                renderJson(Ret.fail("msg","会员信息不存在"));
                return;
            }
        }else{
            if(StringUtils.isBlank(cardNumber)){
                renderJson(Ret.fail("msg","会员卡号不能为空"));
                return;
            }
        }
        Record record = finaMembershipCardService.getCardByCardNumber(id,cardNumber,delFlag);
        if(record==null){
            renderJson(Ret.fail("msg","该会员卡号不存在"));
            return;
        }
        renderJson(Ret.ok("data",record));
    }





    /**
     * 会员卡保存
     */
    public void save(){

        String isGive=getPara("isGive");
        int count=getParaToInt("count");
        String ruleId = getPara("ruleId");
        int collectCount=getParaToInt("collectCount");
        int fileCount=getParaToInt("fileCount");
        final String notFitBase = getPara("notFitBase");
        final String userId = AuthUtils.getUserId();
        MmsMember member = getBean(MmsMember.class,"member",true);
        FinaMembershipCard card = getBean(FinaMembershipCard.class,"card",true);
        MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
        String empId=getPara("empId");
        logger.info("isGive===" + isGive);
        logger.info("count===" + count);
        logger.info("ruleId===" + ruleId);
        logger.info("collectCount===" + collectCount);
        logger.info("fileCount===" + fileCount);
        logger.info("notFitBase===" + notFitBase);
        logger.info("userId===" + userId);
        logger.info("member===" + JSON.toJSONString(member));
        logger.info("card===" + JSON.toJSONString(card));

        //根据身份证判断是否黑名单
        if(StrKit.notBlank(member.getIdcard())) {
        	MmsBlackList blackList = mmsBlackListService.getByIdCard(member.getIdcard());
        	if(blackList!=null) {
        		renderJson(Ret.fail("msg", "该会员已被列入黑名单"));
        		return;
        	}
        }else {
        	renderJson(Ret.fail("msg", "身份证不能为空"));
        	return;
        }

        //剩余天数与剩余金额必须二选一
//		if (StrKit.isBlank(card.getId()) && card.getConsumeTimes() == null && card.getBalance() == null
//				&& card.getConsumePoints() == null) {
//			renderJson(Ret.fail("msg", "剩余天数、剩余点数与余额必须三选一"));
//			return;
//		}

        //购买天数与购买金额必须二选一
		if (
            StrKit.isBlank(card.getId())
            && card.getBuyRechargeDays() == null
            && card.getBuyRechargeAmount() == null
        ) {
			renderJson(Ret.fail("msg", "购买天数与购买金额必须二选一"));
			return;
		}

		boolean deleteBaseFlag = false;
		if(StrKit.notBlank(card.getId())) {
			deleteBaseFlag = true;
			card.setUpdateBy(userId);
		}else {
			card.setCreateBy(userId);
			card.setUpdateBy(userId);
		}
		
		List<FinaCardCollect> cardCollectList = new ArrayList<FinaCardCollect>();
		for(int i=0;i<=collectCount;i++){
			FinaCardCollect cardCollect=getBean(FinaCardCollect.class,"cardCollectList["+i+"]",true);
            if(cardCollect.getCollectAmount()!=null && cardCollect.getCollectAmount()>0){
            	cardCollectList.add(cardCollect);
            }
        }
		final String cardCollect = JSON.toJSONString(cardCollectList);
        logger.info("cardCollect===" + cardCollect);
		List<CfsFileUpload> fileList = new ArrayList<CfsFileUpload>();
		for(int i=1;i<=fileCount;i++){
			CfsFileUpload fileUpload=getBean(CfsFileUpload.class,"fileList["+i+"]",true);
            if(fileUpload.getId()!=null){
            	fileList.add(fileUpload);
            }
        }
        logger.info("fileList===" + JSON.toJSONString(fileList));
        logger.info("deleteBaseFlag===" + deleteBaseFlag);
		boolean invokeFlag = false;
        if(!deleteBaseFlag) {//添加
            logger.info("进入添加===");
            invokeFlag = true;
        }else{//修改
            logger.info("进入修改===");
            final String oldContractNumber = Db.queryStr("select contract_number from fina_membership_card where id=?", card.getId());
            if(StrKit.isBlank(oldContractNumber) && StrKit.notBlank(card.getContractNumber())){
                invokeFlag = true;
            }
            if(StrKit.notBlank(oldContractNumber) && StrKit.notBlank(card.getContractNumber())){
                if(!oldContractNumber.equalsIgnoreCase(card.getContractNumber())){
                    invokeFlag = true;
                }
            }
        }
        logger.info("invokeFlag===" + invokeFlag);
        PersOrgEmployee employee=persOrgEmployeeService.findById(empId);
        if(TypeClassify.WELFARE_CARD.equals(cardType.getTypeClassify()) && employee!=null){
            employee.setStaffCardId(card.getId());
            employee.setUpdateDate(new Date());
        }else{
            employee=null;
        }
        if(invokeFlag && cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
            //更新会员卡号、合同编号、收据号为已使用状态
            Global.executorService.execute(new Runnable() {
                @Override
                public void run() {
                    List<String> nosList = new ArrayList<>();
                    if(StrKit.notBlank(card.getCardNumber())){
                        nosList.add(card.getCardNumber());
                    }
                    if(StrKit.notBlank(card.getContractNumber())){
                        nosList.add(card.getContractNumber());
                    }
                    for(FinaCardCollect collect : cardCollectList){
                        if(StrKit.notBlank(collect.getReceiptNumber())){
                            if(!nosList.contains(collect.getReceiptNumber())){
                                nosList.add(collect.getReceiptNumber());
                            }
                        }
                    }
                    logger.info("nosList===" + JSON.toJSONString(nosList));

                    if(nosList!=null && nosList.size()>0){
                        Map<String,String> paramMap=new HashMap<>();
                        paramMap.put("nos", JSON.toJSONString(nosList));
                        paramMap.put("status","Use");
                        paramMap.put("userId",userId);
                        paramMap.put("description",null);
                        final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Member/UseCardAreaUnpackNo",paramMap,null,null);
                        logger.info("调用更新合同编号接口返回==="+resultStr+"；参数是："+paramMap.toString());
                    }
                }
            });
        }
        Ret returnRet = finaMembershipCardService.saveCard(card,member,ruleId,AuthUtils.getUserId(),cardCollect,null,fileList);
        logger.info("returnRet===" + returnRet);
        if(returnRet.isOk()){
            if(employee!=null){
                employee.update();
            }
            for(int i=0;i<=count;i++){
                FinaMembershipCardCheckinConfig checkinConfig=getBean(FinaMembershipCardCheckinConfig.class
                        ,"checkinConfig-["+i+"]",true);
                if(StrKit.notBlank(checkinConfig.getIdcard())){
                    checkinConfig.setCardId(card.getId());
                    checkinConfig.setIdcard(checkinConfig.getIdcard().trim());
                    checkinConfig.setName(checkinConfig.getName().trim());
                    finaMembershipCardCheckinConfigService.saveCheckinConfig(checkinConfig,AuthUtils.getUserId());
                }
            }

            if(isGive.equals("1")){
                mainCardGiveSchemeRuleService.giveDisposableGiveRule(card.getId(),card.getGiveSchemeId());
            }
            
            if(deleteBaseFlag) {
            	//先删除原来的数据
        		Db.update("delete from fina_membership_card_base where card_id=?", card.getId());
            }
            
            if(StrKit.notBlank(notFitBase)) {
    			final String[] notFitBaseArray = notFitBase.split(",");
    			for(String baseId : notFitBaseArray) {
    				FinaMembershipCardBase cardBase = new FinaMembershipCardBase();
    				cardBase.setId(IdGen.getUUID());
    				cardBase.setCardId(card.getId());
    				cardBase.setBaseId(baseId);
    				cardBase.save();
    			}
    		}
        }
        renderJson(returnRet);
    }

    public void delCardCheckinConfig(){
        String id=getPara("id");
        FinaMembershipCardCheckinConfig checkinConfig=finaMembershipCardCheckinConfigService.findById(id);
        checkinConfig.setDelFlag("1");
        if(finaMembershipCardCheckinConfigService.saveCheckinConfig(checkinConfig,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 关联会员的准备：获取关联会员的信息
     */
    @Clear(LogInterceptor.class)
    public void bindMmsMember(){
//        String cardName = getPara("cardName");
        String idcard = getPara("idcard");

        MmsMember member = mmsMemberService.getMemberByIdcard(idcard);
        if(member != null){
            Ret ret = Ret.ok("msg","操作成功");
            if(StringUtils.isNotBlank(member.getProvince())) {
                MainArea area = areaService.findById(member.getProvince());
                if(area != null)ret.set("province",area.getName());;
            }
            if(StringUtils.isNotBlank(member.getCity())) {
                MainArea area = areaService.findById(member.getCity());
                if(area != null)ret.set("city",area.getName());;
            }
            if(StringUtils.isNotBlank(member.getTown())) {
                MainArea area = areaService.findById(member.getTown());
                if(area != null)ret.set("town",area.getName());
            }
            if(StringUtils.isNotBlank(member.getStreet())) {
                MainArea area = areaService.findById(member.getStreet());
                if(area != null)ret.set("street",area.getName());
            }
            List<MmsMemberLink> linkList = mmsMemberLinkService.findList(member.getId());
            renderJson(ret.set("data",member).set("linkList", linkList));
        }else{
            renderJson(Ret.ok("msg","无信息"));
        }
    }


    public void batchDel(){
        String cardData =  getPara("data");
        String batchVoidRemark =  getPara("batchVoidRemark");
        List<FinaMembershipCard> list = JSONArray.parseArray(cardData,FinaMembershipCard.class);
        final String userId = AuthUtils.getUserId();
        boolean flag = finaMembershipCardService.batchDelCard(list, batchVoidRemark, userId);
        if(flag){
            renderJson(Ret.ok("msg", "批量作废成功"));
            Global.executorService.execute(new Runnable() {
                @Override
                public void run() {
                    for (FinaMembershipCard card : list) {
                        FinaMembershipCard memberCard = finaMembershipCardService.findById(card.getId());
                        MainMembershipCardType cardType = mainMembershipCardTypeService.findById(memberCard.getCardTypeId());
                        if(cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
                            Map<String,String> paramMap=new HashMap<>();
                            List<String> nosList = new ArrayList<>();
                            if(StrKit.notBlank(memberCard.getCardNumber())){
                                nosList.add(memberCard.getCardNumber());
                            }
                            if(StrKit.notBlank(memberCard.getContractNumber())){
                                final String oldContractNumber = Db.queryStr("select contract_number from fina_membership_card where del_flag=? and card_number=? and contract_number=?", DelFlag.NORMAL, memberCard.getCardNumber(), memberCard.getContractNumber());
                                if(StrKit.isBlank(oldContractNumber)){
                                    nosList.add(memberCard.getContractNumber());
                                }
                            }
                            List<FinaCardCollect> cardCollectList = finaCardCollectService.findList(card.getId());
                            for(FinaCardCollect collect : cardCollectList){
                                if(StrKit.notBlank(collect.getReceiptNumber())){
                                    nosList.add(collect.getReceiptNumber());
                                }
                            }
                            paramMap.put("nos", JSON.toJSONString(nosList));
                            paramMap.put("status","Delete");
                            paramMap.put("userId",userId);
                            paramMap.put("description",null);
                            final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Member/UseCardAreaUnpackNo",paramMap,null,null);
                            logger.info("调用更新合同编号接口返回==="+resultStr);

                            //同步到wms
                            JSONObject paramObj = new JSONObject();
                            paramObj.put("id", card.getId());
                            paramObj.put("delFlag", "1");
                            paramObj.put("updateBy", AuthUtils.getUserId());
                            paramObj.put("updateTime", new Date());
                            final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),AuthUtils.getUserId());
                            logger.info("同步到wms标识为：" + retFlag);
                        }
                    }
                }
            });
            
        }else{
            renderJson(Ret.fail("msg", "批量作废失败"));
        }

    }
    
    /**
     * 会员卡批量作废(新2024.4.7)
     */
    public void batchVoid(){
        final int count = getParaToInt("count");
        final String voidRemark = getPara("voidRemark");
        List<FinaMembershipCard> cardList = new ArrayList<>();
        for(int i=1;i<=count;i++){
        	FinaMembershipCard memberCard = getBean(FinaMembershipCard.class, "void-["+i+"]", true);
            if(StrKit.notBlank(memberCard.getId())){
                cardList.add(memberCard);
            }
        }
        final String userId = AuthUtils.getUserId();
        boolean flag = finaMembershipCardService.batchDelCard(cardList, voidRemark, userId);
        logger.info("批量作废标识为：" + flag);
        if(flag){
            renderJson(Ret.ok("msg", "批量作废成功"));
            Global.executorService.execute(new Runnable() {
            	
                @Override
                public void run() {
                    for (FinaMembershipCard card : cardList) {
                        FinaMembershipCard memberCard = finaMembershipCardService.findById(card.getId());
                        MainMembershipCardType cardType = mainMembershipCardTypeService.findById(memberCard.getCardTypeId());
                        if(cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
                            Map<String,String> paramMap=new HashMap<>();
                            List<String> nosList = new ArrayList<>();
                            if(StrKit.notBlank(memberCard.getCardNumber())){
                                nosList.add(memberCard.getCardNumber());
                            }
                            if(StrKit.notBlank(memberCard.getContractNumber())){
                                final String oldContractNumber = Db.queryStr("select contract_number from fina_membership_card where del_flag=? and card_number=? and contract_number=?", DelFlag.NORMAL, memberCard.getCardNumber(), memberCard.getContractNumber());
                                if(StrKit.isBlank(oldContractNumber)){
                                    nosList.add(memberCard.getContractNumber());
                                }
                            }
                            List<FinaCardCollect> cardCollectList = finaCardCollectService.findList(card.getId());
                            for(FinaCardCollect collect : cardCollectList){
                                if(StrKit.notBlank(collect.getReceiptNumber())){
                                    nosList.add(collect.getReceiptNumber());
                                }
                            }
                            paramMap.put("nos", JSON.toJSONString(nosList));
                            paramMap.put("status","Delete");
                            paramMap.put("userId",userId);
                            paramMap.put("description",null);
                            final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Member/UseCardAreaUnpackNo",paramMap,null,null);
                            logger.info("调用更新合同编号接口返回==="+resultStr);

                            //同步到wms
                            JSONObject paramObj = new JSONObject();
                            paramObj.put("id", card.getId());
                            paramObj.put("delFlag", "1");
                            paramObj.put("updateBy", AuthUtils.getUserId());
                            paramObj.put("updateTime", new Date());
                            final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),AuthUtils.getUserId());
                            logger.info("同步到wms标识为：" + retFlag);
                        }
                    }
                }
            });
            
        }else{
            renderJson(Ret.fail("msg", "批量作废失败"));
        }
    }

    public void beanCouponsDetailIndex(){
        String cardId=getPara("id");

        setAttr("cardId",cardId);
        render("beanCouponsDetailIndex.html");
    }

    public void beanCouponsDetailPageList(){
        String cardId=getPara("cardId");

        String sql="select b.* from fina_card_roll a INNER JOIN crm_card_roll_record b on a.roll_id=b.id " +
                "where a.card_id=? and b.del_flag='0' order by create_time,roll_number ";

        List<Record> recordList=Db.find(sql,cardId);

        renderJson(new DataTable<Record>(recordList));
    }

    /**
     * 会员卡号生成
     */
    @Clear(LogInterceptor.class)
    public void getRandomNumber(){
        try {
            String cardTypeId = getPara("cardTypeId");
            Map<String,Object> resultMap = finaMembershipCardService.dealRandomNumber(cardTypeId);
            if(resultMap.containsKey("ok")){
                renderJson(Ret.ok("data", resultMap.get("ok")));
            }else if(resultMap.containsKey("fail")){
                renderJson(Ret.fail("msg", resultMap.get("fail")));
            }else{
                renderJson(Ret.fail("msg", "生成会员卡失败"));
            }
        } catch (Exception e) {
            logger.info("生成会员卡异常：[{}]",e.getMessage());
            renderJson(Ret.fail("msg", "生成会员卡失败"));
        }
    }


    /**
     * 跳转选择分公司经办人界面
     */
    public void chooseUser(){
        List<MainBranchOffice> officeList = mainBranchOfficeService.getUnDelBranchOffice();
        setAttr("officeList",officeList);
        render("userInfo.html");
    }


    /**
     * 用于经办人展示界面
     */
    @Clear(LogInterceptor.class)
    public void findListUser(){
        PersOrgEmployee employee = getBean(PersOrgEmployee.class, "", true);
        String officeId = getPara("officeId");


        Page<Record> userPage = userService.findOfficeUserList(getParaToInt("page", 1), getParaToInt("limit", 10),employee,officeId);
        renderJson(new DataTable<Record>(userPage));
    }


    /**
     * 会员卡易主
     */
    public void changeOwn(){
        String cardId = getPara("cardId");
        String memberId = getPara("memberId");
        String operator = getPara("operator");
        MmsMember member = getBean(MmsMember.class,"member",true);
        final String userId = AuthUtils.getUserId();
        String existFlag = "0";

        if(StringUtils.isBlank(memberId) || "undefined".equals(memberId)){
            renderJson(Ret.fail("msg", "该会员卡没有卡主，不可变更"));
            return;
        }

        //调用旅居身份证校验接口
        Map<String,String> params=new HashMap<>();
        params.put("name", member.getFullName());//卡主姓名
        params.put("idCardType", member.getIdcardType());//卡主证件类型
        params.put("idCard", member.getIdcard());//卡主身份证
        params.put("userId", userId);//操作人用户id
        params.put("channel", "AddMemberInfo");//渠道来源
        params.put("key", "40FA2FDE-9B79-40EC-8147-50F1C7EEF7DE");//key
        final String resultStr = HttpClientsUtils.httpPostForm(Global.checkIdCardUrl,params,null,null);
        logger.info("resultStr==="+resultStr);
        JSONObject returnJson = JSONObject.parseObject(resultStr);
        final int returnType = returnJson.getIntValue("Type");
        final String returnMsg = returnJson.getString("Msg");
        logger.info("身份证校验返回returnType==="+returnType);
        logger.info("身份证校验返回returnMsg==="+returnMsg);
        //姓名、身份证校验成功
        if(returnType!=1){
            renderJson(Ret.fail("msg", returnMsg));
            return;
        }

        MmsMember memberExist = mmsMemberService.getMemberByNameAndIdcard(member.getFullName(),member.getIdcard()) ;
        if(memberExist != null && memberId.equals(memberExist.getId())){
            renderJson(Ret.fail("msg", "变更卡主与原卡主一致，不可变更"));
            return;
        }
        if(memberExist == null){
            existFlag = "1";
            memberExist = new MmsMember();
        }

        FinaMembershipCard card=finaMembershipCardService.findById(cardId);
        if(card==null){
            renderJson(Ret.fail("msg", "会员卡不存在"));
            return;
        }

        Long expenseRecordDetail = Db.queryLong("select count(id) from fina_expense_record_detail where del_flag='0' and is_settled='0' and card_number=? ",card.getCardNumber());
        if(expenseRecordDetail>0){
            renderJson(Ret.fail("msg", "会员卡存在未结算订单，更换卡主失败"));
            return;
        }

        //判断是否有未结算违约单存在
        Long countPunish = Db.queryLong("select count(*) from fina_punish_record where del_flag = '0' and status = '0' and card_number = ?",card.getCardNumber());
        if(countPunish > 0){
            renderJson(Ret.fail("msg", "会员卡存在未结算违约订单，更换卡主失败"));
            return;
        }

        boolean flag = finaMembershipCardService.dealChangeOwn(cardId,memberId,operator,member,memberExist,existFlag,userId);
        if(flag){
            renderJson(Ret.ok("msg", "更换成功"));
        }else{
            renderJson(Ret.fail("msg", "更换失败"));
        }
    }

    /**
     * 恢复会员卡首页
     */
    public void recoveryCardIndex(){
        render("recoveryCardIndex.html");
    }

    /**
     * 作废的会员卡分页
     */
    @Clear(LogInterceptor.class)
    public void cancelCardPageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        String nameOrIdcardOrCardNumber=getPara("nameOrIdcardOrCardNumber");
        Page<Record> page=finaMembershipCardService.cancelCardPageList(pageNumber,pageSize,nameOrIdcardOrCardNumber);
        renderJson(new DataTable<Record>(page));
    }

    /**
     * 过期会员卡首页
     */
    public void expiredCardIndex(){
        render("expiredCardIndex.html");
    }

    /**
     * 修改会员卡过期时间表单
     */
    public void expiredCardForm(){
        String id=getPara("id");
//        String openTime=getPara("openTime");
        String expireDate=getPara("expireDate");
//        String useYears=getPara("useYears");
        setAttr("id",id);
//        setAttr("openTime",openTime);
        setAttr("expireDate",expireDate);
//        setAttr("useYears",useYears);
        render("expiredCardForm.html");
    }


    /**
     * 过期会员卡分页
     */
    public void expiredCardRecordPage(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        String nameOrIdcardOrCardNumber=getPara("nameOrIdcardOrCardNumber");
        Page<Record> page=finaMembershipCardService.expiredCardPageList(pageNumber,pageSize,nameOrIdcardOrCardNumber);
        renderJson(new DataTable<Record>(page));
    }

    /**
     * 修改过期会员卡
     */
    public void expiredCardSave(){
        FinaMembershipCard card = getBean(FinaMembershipCard.class,"",true);
//        Integer originalUseYears=getParaToInt("originalUseYears");
//        if(originalUseYears==0){
//            card.setExpireFlag("0");
//        }else if(originalUseYears<Integer.valueOf(card.getUseYears())){
//            card.setExpireFlag("0");
//        }
        card.setUpdateBy(AuthUtils.getUserId());
        card.setUpdateTime(new Date());
        boolean flag = finaMembershipCardService.expiredCardSave(card);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
    
    /**
     * 删除
     */
    public void deleteCard(){
    	String id = getPara("id");
    	if(StrKit.isBlank(id)){
    		renderJson(Ret.fail("msg","id参数为空,删除失败"));
    		return;
    	}
        if(finaMembershipCardService.deleteById(id)){
            renderJson(Ret.ok("msg", "删除成功"));
        }else{
        	renderJson(Ret.ok("msg", "删除失败"));
        }
    }

    /**
     * 恢复会员卡
     */
    public void recoveryCard(){
        String id=getPara("id");
        FinaMembershipCard card=finaMembershipCardService.findById(id);
        if(card==null || "0".equals(card.getDelFlag())){
            renderJson(Ret.fail("msg","该会员卡不存在或不是作废状态"));
            return;
        }
        if(finaMembershipCardService.findCardByCardNumber(card.getCardNumber())!=null){
            renderJson(Ret.fail("msg","该会员卡号为正常状态不需要恢复"));
            return;
        }
        final String userId = AuthUtils.getUserId();
        boolean flag=finaMembershipCardService.recoveryCard(id, userId);
        if(flag){
            renderJson(Ret.ok("msg","恢复成功"));
            Global.executorService.execute(new Runnable() {
                @Override
                public void run() {
                    Map<String,String> paramMap=new HashMap<>();
                    List<String> nosList = new ArrayList<>();
                    MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
                    if(cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
                        if(StrKit.notBlank(card.getCardNumber())){
                            nosList.add(card.getCardNumber());
                        }
                        if(StrKit.notBlank(card.getContractNumber())){
                            nosList.add(card.getContractNumber());
                        }
                        List<FinaCardCollect> cardCollectList = finaCardCollectService.findList(id);
                        for(FinaCardCollect collect : cardCollectList){
                            if(StrKit.notBlank(collect.getReceiptNumber())){
                                nosList.add(collect.getReceiptNumber());
                            }
                        }
                        paramMap.put("nos", JSON.toJSONString(nosList));
                        paramMap.put("status","NotDelet");
                        paramMap.put("userId",userId);
                        paramMap.put("description",null);
                        final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Member/UseCardAreaUnpackNo",paramMap,null,null);
                        logger.info("调用更新合同编号接口返回==="+resultStr);

                        JSONObject paramObj = new JSONObject();
                        paramObj.put("id", id);
                        paramObj.put("delFlag", "0");
                        paramObj.put("updateBy", AuthUtils.getUserId());
                        paramObj.put("updateTime", new Date());
                        //同步到wms
                        final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),AuthUtils.getUserId());
                        logger.info("同步到wms标识为：" + retFlag);
                    }
                }
            });
        }else{
            renderJson(Ret.fail("msg","恢复失败"));
        }
    }


    /**
     * 赠送详情页面
     */
    public void giveSchemeDetailIndex(){
        String giveSchemeId=getPara("giveSchemeId");
        setAttr("giveSchemeId",giveSchemeId);

        render("giveSchemeDetailIndex.html");
    }

    /**
     * 赠送详情分页数据
     */
    public void giveSchemeDetailPage(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        String giveSchemeId=getPara("giveSchemeId");
        MainCardGiveSchemeRule cardGiveSchemeRule=new MainCardGiveSchemeRule();
        cardGiveSchemeRule.setSchemeId(giveSchemeId);
        Page<MainCardGiveSchemeRule> giveSchemeRulePage=mainCardGiveSchemeRuleService.giveSchemeRulePage(pageNumber,pageSize,cardGiveSchemeRule);
        renderJson(new DataTable<MainCardGiveSchemeRule>(giveSchemeRulePage));
    }

    /**
     * 查询是否有一次性的赠送规则
     */
    public void disposableGiveScheme(){
        String giveSchemeId=getPara("giveSchemeId");
        String cardId=getPara("cardId");
        List<MainCardGiveSchemeRule> giveSchemeRuleList=mainCardGiveSchemeRuleService.findDisposableGiveRule(cardId,giveSchemeId);
        if(giveSchemeRuleList.size()>0){
            Double totalGiveAmount=null;
            Double totalGiveTimes=null;
            for(MainCardGiveSchemeRule rule:giveSchemeRuleList){
                if(totalGiveAmount==null){
                    totalGiveAmount=rule.getGiveAmount();
                }else{
                    totalGiveAmount=new BigDecimal(totalGiveAmount).add(new BigDecimal(rule.getGiveAmount())).doubleValue();
                }
                if(totalGiveTimes==null){
                    totalGiveTimes=rule.getGiveTimes();
                }else{
                    totalGiveTimes=new BigDecimal(totalGiveTimes).add(new BigDecimal(rule.getGiveTimes())).doubleValue();
                }
            }
            JSONObject obj=new JSONObject();
            obj.put("totalGiveAmount",totalGiveAmount);
            obj.put("totalGiveTimes",totalGiveTimes);
            obj.put("size",giveSchemeRuleList.size());
            renderJson(Ret.ok("data",obj));
        }else{
            renderJson(Ret.fail());
        }
    }

    /**
     * 会员卡赠送记录首页
     */
    public void cardGiveRecordIndex(){
        String cardId=getPara("cardId");

        setAttr("cardId",cardId);
        render("cardGiveRecordIndex.html");
    }

    /**
     * 获取赠送记录分页
     */
    public void cardGiveRecordPage(){
        MainCardGiveRecord cardGiveRecord=getBean(MainCardGiveRecord.class,"",true);
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        Page<MainCardGiveRecord> recordPage=mainCardGiveRecordService.findGiveRecordPage(pageNumber,pageSize,cardGiveRecord);
        renderJson(new DataTable<MainCardGiveRecord>(recordPage));
    }

    /**
     * 批量充值页面
     */
    public void bathRechargeIndex(){

        List<FinaCardAccount> accountList = finaCardAccountService.findList(null);
        setAttr("accountList",accountList);
        render("batchRecharge.html");
    }
    
    /**
     * 批量作废页面
     */
    public void bathVoidIndex(){
    	render("batchVoid.html");
    }

    /**
     * 通过会员卡号查询会员卡信息
     */
    public void findCardInfo(){
        String cardNumber=getPara("cardNumber");
        if(StrKit.isBlank(cardNumber)){
            renderJson(Ret.fail("msg","会员卡号不能为空"));
            return;
        }

        List<Record> list=finaMembershipCardService.findCardInfo(cardNumber);
        if(list.size()>0){
            renderJson(Ret.ok("data",list.get(0)));
        }else{
            renderJson(Ret.ok("data",null));
        }
    }

    /**
     * 通过会员卡号身份证号会员姓名模糊查询会员卡
     */
    public void findCardPageList(){
        final String nameOrIdcardOrCardNumber=getPara("nameOrIdcardOrCardNumber");
        final String actionType=getPara("actionType");
        Page<Record> page=finaMembershipCardService.findCardPageList(getParaToInt("page"),getParaToInt("limit"),nameOrIdcardOrCardNumber,actionType);
        renderJson(new DataTable<Record>(page));
    }

    /**
     * 会员卡批量充值
     */
    public void batchRecharge(){
        int count=getParaToInt("count");
        List<FinaCardRecharge> rechargeList=new ArrayList<>();

        Date now=new Date();
        for(int i=1;i<=count;i++){
            FinaCardRecharge recharge=getBean(FinaCardRecharge.class,"recharge-["+i+"]",true);

            if(StrKit.notBlank(recharge.getCardId())){
                FinaMembershipCard card=finaMembershipCardService.findById(recharge.getCardId());
                if("1".equals(card.getIsLock())){
                    renderJson(Ret.fail("msg",card.getCardNumber()+"会员卡为锁定状态，充值失败"));
                    return;
                }
                recharge.setCreateTime(DateUtils.addSeconds(now,-rechargeList.size()));
                if(recharge.getGiveConsumeTimes()==null){
                    recharge.setGiveConsumeTimes(0.0);
                }
                if(recharge.getGiveAmount()==null){
                    recharge.setGiveAmount(0.00);
                }
                if(recharge.getAmount()==null){
                    recharge.setAmount(0.00);
                }
                if(recharge.getConsumeTimes()==null){
                    recharge.setConsumeTimes(0.0);
                }
                if(recharge.getConsumeIntegral()==null){
                    recharge.setConsumeIntegral(0.0);
                }
                if(recharge.getGiveConsumeIntegral()==null){
                    recharge.setGiveConsumeIntegral(0.0);
                }
                if(recharge.getConsumePoints()==null){
                    recharge.setConsumePoints(0.0);
                }
                if(recharge.getGiveConsumePoints()==null){
                    recharge.setGiveConsumePoints(0.0);
                }
                if(recharge.getConsumeBeanCoupons()==null){
                    recharge.setConsumeBeanCoupons(0.0);
                }
                if(recharge.getGiveBeanCoupons()==null){
                    recharge.setGiveBeanCoupons(0.0);
                }

                recharge.setRechargeWay(RechargeWay.batch.getKey());
                recharge.setRechargeTime(new Date());
                rechargeList.add(recharge);
            }
        }
        boolean flag=finaCardRechargeService.batchRecharge(rechargeList,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","充值成功"));
        }else{
            renderJson(Ret.fail("msg","充值失败"));
        }
    }

    public void memberCardTable(){
    	final String actionType = getPara("actionType");
    	setAttr("actionType",actionType);
        render("memberCardTable.html");
    }


    /**
     * 跳转转移会员卡界面
     */
    public void cardTransferForm(){
    	
//    	String cardNumber = getPara("cardNumber");
        FinaMembershipCard card = new FinaMembershipCard();
//        card.setCardNumber(cardNumber);
//        Record oldCard = finaMembershipCardService.getCardAndMember(card);
//        if(oldCard == null){
//        	renderJson(Ret.fail("msg","未获取到会员卡信息"));
//        	return;
//        }
//        if(oldCard != null){
//            if(StringUtils.isNotBlank(oldCard.getStr("province"))) {
//                Area area = areaService.findById(oldCard.getStr("province"));
//                if(area != null)setAttr("province",area.getAreaName());
//            }
//            if(StringUtils.isNotBlank(oldCard.getStr("city"))) {
//                Area area = areaService.findById(oldCard.getStr("city"));
//                if(area != null)setAttr("city",area.getAreaName());
//            }
//            if(StringUtils.isNotBlank(oldCard.getStr("town"))) {
//                Area area = areaService.findById(oldCard.getStr("town"));
//                if(area != null)setAttr("town",area.getAreaName());
//            }
//            if(StringUtils.isNotBlank(oldCard.getStr("street"))) {
//                Area area = areaService.findById(oldCard.getStr("street"));
//                if(area != null)setAttr("street",area.getAreaName());
//            }
//        }
//        if(StringUtils.isNotBlank(oldCard.getStr("operator"))){
//            User user = userService.findById(oldCard.getStr("operator"));
//            setAttr("user",user);
//        }
        List<Record> cardList = Db.find("select id,card_number as cardNumber,balance,consume_times as consumeTimes,consume_points as consumePoints,card_integrals as cardIntegrals,bean_coupons as beanCoupons,account_balance as accountBalance from fina_membership_card where del_flag='0' and return_card_status='none'");
        List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
        List<FinaCardGiveRule> ruleList = finaCardGiveRuleService.getRules();
//        List<MainCardGiveScheme> giveSchemeList = mainCardGiveSchemeService.findListOnUse();
        List<MainCardDeductScheme> sojournSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.SOJOURN_SCHEME.getKey());
        List<MainCardDeductScheme> longSchemeList = mainCardDeductSchemeService.findListOnUse(SchemeType.LONG_SCHEME.getKey());
        List<MainCardYearLimit> cardYearLimitList=mainCardYearLimitService.findCardYearLimitList();
        List<FinaCardAccount> accountList = finaCardAccountService.findList(null);
        List<MainContractType> contractTypeList = mainContractTypeService.findList();
        List<MainBranchOffice> branchOfficeList = mainBranchOfficeService.getUnDelBranchOffice();
//        setAttr("oldCard",oldCard);
        setAttr("cardList",cardList);
        setAttr("typeList",typeList);
        setAttr("ruleList",ruleList);
//        setAttr("giveSchemeList",giveSchemeList);
        setAttr("sojournSchemeList",sojournSchemeList);
        setAttr("longSchemeList",longSchemeList);
        setAttr("cardYearLimitList",cardYearLimitList);
        setAttr("accountList",accountList);
        setAttr("contractTypeList",contractTypeList);
        setAttr("branchOfficeList",branchOfficeList);
        setAttr("userId",AuthUtils.getUserId());
    	setAttr("uploadDomain",Global.commonUpload);
    	setAttr("hrmDomain",Global.hrmUrl);
//    	setAttr("notFitBases", Db.queryStr("select group_concat(base_id)baseIds from fina_membership_card_base where card_id=? group by card_id", id));
        render("transferCardForm.html");
    }

    /**
     * 转移加载会员卡界面
     */
    public void cardNumberForm(){
    	String cardNumber = getPara("cardNumber");
        FinaMembershipCard card = new FinaMembershipCard();
        card.setCardNumber(cardNumber);
        Record oldCard = finaMembershipCardService.getCardAndMember(card);
        if(oldCard == null){
        	renderJson(Ret.fail("msg","未获取到会员卡信息"));
        	return;
        }
        if(oldCard != null){
            if(StringUtils.isNotBlank(oldCard.getStr("province"))) {
                MainArea area = areaService.findById(oldCard.getStr("province"));
                if(area != null)setAttr("province",area.getName());
            }
            if(StringUtils.isNotBlank(oldCard.getStr("city"))) {
                MainArea area = areaService.findById(oldCard.getStr("city"));
                if(area != null)setAttr("city",area.getName());
            }
            if(StringUtils.isNotBlank(oldCard.getStr("town"))) {
                MainArea area = areaService.findById(oldCard.getStr("town"));
                if(area != null)setAttr("town",area.getName());
            }
            if(StringUtils.isNotBlank(oldCard.getStr("street"))) {
                MainArea area = areaService.findById(oldCard.getStr("street"));
                if(area != null)setAttr("street",area.getName());
            }
        }
        if(StringUtils.isNotBlank(oldCard.getStr("operator"))){
            User user = userService.findById(oldCard.getStr("operator"));
            setAttr("user",user);
        }
        setAttr("oldCard",oldCard);
        render("cardNumberForm.html");
    }
    
    public void passwordSet(){
    	String id = getPara("id");
        FinaMembershipCard card = finaMembershipCardService.findById(id);
        setAttr("card",card);
        render("passwordSet.html");
    }
    

    /**
     * 更新会员卡信息
     */
    public void update(){
    	FinaMembershipCard memberCard = getBean(FinaMembershipCard.class,"",true);
    	if(StrKit.notBlank(memberCard.getId())){
    		memberCard.setUpdateBy(AuthUtils.getUserId());
    		memberCard.setUpdateTime(new Date());
    		if(finaMembershipCardService.update(memberCard)){
    			if(StrKit.notBlank(memberCard.getIsReport())){
    				JSONObject paramObj = new JSONObject();
    				paramObj.put("id", memberCard.getId());
    				paramObj.put("isReport", memberCard.getIsReport());
    				paramObj.put("updateBy", memberCard.getUpdateBy());
    				paramObj.put("updateTime", memberCard.getUpdateTime());
    				//同步到wms
    				final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),memberCard.getUpdateBy());
    				logger.info("同步到wms标识为：" + retFlag);
    			}
    			renderJson(Ret.ok("msg","操作成功"));
    		}else{
    			renderJson(Ret.fail("msg","操作失败"));
    		}
    	}else{
    		renderJson(Ret.fail("msg","操作失败,id不能为空"));
    	}
    }

    /**
     * 更新会员卡密码
     */
    public void updatePwd(){
    	FinaMembershipCard memberCard = getBean(FinaMembershipCard.class,"",true);
    	if(StrKit.notBlank(memberCard.getPassword())){
    		String salt = new SecureRandomNumberGenerator().nextBytes().toHex();
            SimpleHash hash = new SimpleHash("md5", memberCard.getPassword(), salt, 2);
            final String pwd = hash.toHex();
            memberCard.setPassword(pwd);
            memberCard.setSalt(salt);
            memberCard.setIsEnablePwd("1");
    	}
    	memberCard.setUpdateBy(AuthUtils.getUserId());
    	memberCard.setUpdateTime(new Date());
        boolean flag=finaMembershipCardService.update(memberCard);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /**
     * 获取旧卡数据：转移卡账户:卖卡
     */
    public void getOldCardDataForCardTransfer(){
        String cardNumber = getPara("cardNumber");
        FinaMembershipCard card = new FinaMembershipCard();
        card.setCardNumber(cardNumber);
        Record oldCard = finaMembershipCardService.getCardAndMember(card);
        if(oldCard == null){ 
        	renderJson(Ret.fail("msg","未获取到会员卡信息"));
        	return; 
        }
        if(oldCard != null){
            String addr = "";
            if(StringUtils.isNotBlank(oldCard.getStr("province"))) {
                MainArea area = areaService.findById(oldCard.getStr("province"));
                if(area != null)addr +=area.getName() +" ";
            }
            if(StringUtils.isNotBlank(oldCard.getStr("city"))) {
                MainArea area = areaService.findById(oldCard.getStr("city"));
                if(area != null)addr +=area.getName() +" ";
            }
            if(StringUtils.isNotBlank(oldCard.getStr("town"))) {
                MainArea area = areaService.findById(oldCard.getStr("town"));
                if(area != null)addr +=area.getName() +" ";
            }
            if(StringUtils.isNotBlank(oldCard.getStr("street"))) {
                MainArea area = areaService.findById(oldCard.getStr("street"));
                if(area != null)addr +=area.getName();
            }
            oldCard.set("regidentAddress",addr);
        }
        renderJson(Ret.ok("msg","获取成功").set("data",oldCard));
    }



    /**
     * 转移卡账户:卖卡
     */
    public void cardTransfer01(){

        String oldCardId = getPara("oldCardId");//旧会员卡id
        MmsMember member = getBean(MmsMember.class,"member",true);
        FinaCardTransfer transfer = getBean(FinaCardTransfer.class,"transfer",true);
        FinaMembershipCard newCard = getBean(FinaMembershipCard.class,"newCard",true);
        FinaMembershipCard oldCard = finaMembershipCardService.get(oldCardId);
        if(StringUtils.isBlank(oldCardId) || oldCard == null || StringUtils.isBlank(newCard.getCardNumber()))
        { renderJson(Ret.fail("msg","转移卡参数缺失"));return; }

        //处理新旧会员卡账户数据为空现象
        if(oldCard.getConsumeTimes() == null)oldCard.setConsumeTimes(0.00);
        if(oldCard.getBalance() == null)oldCard.setBalance(0.00);
        if(oldCard.getGiveConsumeTimes() == null)oldCard.setGiveConsumeTimes(0.00);
        if(oldCard.getGiveBalance() == null)oldCard.setGiveBalance(0.00);
        if(newCard.getConsumeTimes() == null)newCard.setConsumeTimes(0.00);
        if(newCard.getBalance() == null)newCard.setBalance(0.00);

        //判断转移卡账户是否一致
        if(oldCard.getConsumeTimes().compareTo(newCard.getConsumeTimes()) != 0 || oldCard.getBalance().compareTo(newCard.getBalance()) != 0){
            renderJson(Ret.fail("msg","新旧会员卡转移账户数据不一致，不可转移"));return;
        }

        //判断是否有锁定
        if(oldCard.getLockBalance() != null && oldCard.getLockBalance() != 0.00){ renderJson(Ret.fail("msg","旧会员卡有剩余锁定金额未释放,不可转移卡"));return; }
        if(oldCard.getLockConsumeTimes() != null && oldCard.getLockConsumeTimes() != 0.00){ renderJson(Ret.fail("msg","旧会员卡有剩余锁定天数未释放,不可转移卡"));return; }
        //判断是否有未结算订单存在
        Long countExpense = Db.queryLong("select count(*) from fina_expense_record where del_flag = 0 and settle_status = 0 and card_number = ?",oldCard.getCardNumber());
        Long countPunish = Db.queryLong("select count(*) from fina_punish_record where del_flag = 0 and status = 0 and card_number = ?",oldCard.getCardNumber());
        Long countConsume = Db.queryLong("select count(*) from fina_consume_record where del_flag = 0 and status = 0 and card_number = ?",oldCard.getCardNumber());
        if(countExpense > 0 || countPunish > 0 || countConsume > 0){ renderJson(Ret.fail("msg",oldCard.getCardNumber() + "卡还有账单未结算，不可转移卡"));return; }

        boolean flag = true;//finaMembershipCardService.dealCardTransferChangeName(oldCard,newCard,member,AuthUtils.getUserId(),transfer);
        if(flag){
            renderJson(Ret.ok("msg","转移成功"));
        }else{
            renderJson(Ret.fail("msg","转移失败"));
        }
    }


    /**
     * 转移会员卡：更名过户/升级
     */
    public void cardTransfer(){
    	
//    	final String oldCardId = getPara("oldCardId");//旧会员卡id
    	final int transferDetailCount = getParaToInt("transferDetailCount");
    	MmsMember member = getBean(MmsMember.class,"member",true);
    	//transfer.type=0:更名过户  transfer.type=1:升级
    	FinaCardTransfer transfer = getBean(FinaCardTransfer.class,"transfer",true);
        FinaMembershipCard newCard = getBean(FinaMembershipCard.class,"newCard",true);
        final String userId = AuthUtils.getUserId();
//        FinaMembershipCard oldCard = finaMembershipCardService.get(oldCardId);
        if(StringUtils.isBlank(newCard.getCardNumber())){
        	renderJson(Ret.fail("msg","新会员卡号不能为空"));
        	return; 
        }

        if(StringUtils.isBlank(newCard.getDeductSchemeId()) && StringUtils.isBlank(newCard.getLongDeductSchemeId())){
            renderJson(Ret.fail("msg","新卡扣费方案不能为空"));
            return;
        }
        
        List<FinaCardTransferDetail> transferDetailList = new ArrayList<FinaCardTransferDetail>();
		for(int i=1;i<=transferDetailCount;i++){
			FinaCardTransferDetail transferDetail = getBean(FinaCardTransferDetail.class,"transferDetail["+i+"]",true);
            if(StrKit.notBlank(transferDetail.getOldCardNumber())){
            	transferDetailList.add(transferDetail);
            }
        }

        //判断扣卡方式是否一致
//        boolean bo = mainCardDeductSchemeService.isSomeDeductScheme(oldCard.getDeductSchemeId(),newCard.getDeductSchemeId());
//        if(bo == false){ renderJson(Ret.fail("msg","旧卡和新卡扣卡方式不一致"));return; }

        //若升级则不更名
//        if("1".equals(transfer.getType())){
//            boolean boUpgrade = finaMembershipCardService.isUpgrade(oldCard,member);
//            if(boUpgrade == false){ 
//            	renderJson(Ret.fail("msg","会员卡升级要求不变更会员"));
//            	return; 
//            }
//        }

        //处理新旧会员卡账户数据为空现象
//        if(oldCard.getBalance() == null)oldCard.setBalance(0.00);
//        if(oldCard.getConsumeTimes() == null)oldCard.setConsumeTimes(0.00);
//        if(oldCard.getConsumePoints() == null)oldCard.setConsumePoints(0.00);
//        if(oldCard.getCardIntegrals() == null)oldCard.setCardIntegrals(0.00);
//        if(oldCard.getBeanCoupons() == null)oldCard.setBeanCoupons(0.00);
//        if(oldCard.getLockBalance() == null)oldCard.setLockBalance(0.00);
//        if(oldCard.getLockConsumeTimes() == null)oldCard.setLockConsumeTimes(0.00);
//        if(oldCard.getLockConsumePoints() == null)oldCard.setLockConsumePoints(0.00);
//        if(oldCard.getLockConsumeIntegrals() == null)oldCard.setLockConsumeIntegrals(0.00);
//        if(oldCard.getLockBeanCoupons() == null)oldCard.setLockBeanCoupons(0.00);
//        if(oldCard.getGiveBalance() == null)oldCard.setGiveBalance(0.00);
//        if(oldCard.getGiveConsumeTimes() == null)oldCard.setGiveConsumeTimes(0.00);
//        if(oldCard.getGiveConsumePoints() == null)oldCard.setGiveConsumePoints(0.00);
//        if(oldCard.getGiveConsumeIntegrals() == null)oldCard.setGiveConsumeIntegrals(0.00);
//        if(oldCard.getGiveBeanCoupons() == null)oldCard.setGiveBeanCoupons(0.00);

        if(newCard.getBalance() == null)newCard.setBalance(0.00);
        if(newCard.getConsumeTimes() == null)newCard.setConsumeTimes(0.00);
        if(newCard.getConsumePoints() == null)newCard.setConsumePoints(0.00);
        if(newCard.getCardIntegrals() == null)newCard.setCardIntegrals(0.00);
        if(newCard.getBeanCoupons() == null)newCard.setBeanCoupons(0.00);
        if(newCard.getLockBalance() == null)newCard.setLockBalance(0.00);
        if(newCard.getLockConsumeTimes() == null)newCard.setLockConsumeTimes(0.00);
        if(newCard.getLockConsumePoints() == null)newCard.setLockConsumePoints(0.00);
        if(newCard.getLockConsumeIntegrals() == null)newCard.setLockConsumeIntegrals(0.00);
        if(newCard.getLockBeanCoupons() == null)newCard.setLockBeanCoupons(0.00);
        if(newCard.getGiveBalance() == null)newCard.setGiveBalance(0.00);
        if(newCard.getGiveConsumeTimes() == null)newCard.setGiveConsumeTimes(0.00);
        if(newCard.getGiveConsumePoints() == null)newCard.setGiveConsumePoints(0.00);
        if(newCard.getGiveConsumeIntegrals() == null)newCard.setGiveConsumeIntegrals(0.00);
        if(newCard.getGiveBeanCoupons() == null)newCard.setGiveBeanCoupons(0.00);
        if(newCard.getGiveRechargeAmount()==null)newCard.setGiveRechargeAmount(0.00);
        if(newCard.getGiveRechargeDays()==null)newCard.setGiveRechargeDays(0.00);
        if(newCard.getGiveRechargeIntegrals()==null)newCard.setGiveRechargeIntegrals(0.00);
        if(newCard.getBuyRechargeAmount()==null)newCard.setBuyRechargeAmount(0.00);
        if(newCard.getBuyRechargeDays()==null)newCard.setBuyRechargeDays(0.00);
        if(newCard.getBuyRechargeIntegrals()==null)newCard.setBuyRechargeIntegrals(0.00);
        newCard.setParentId("0");

        Map<String,Object> map = Maps.newHashMap();
        //会员卡号处理空格
        if(StrKit.notBlank(newCard.getCardNumber())){
            String cardNumber = newCard.getCardNumber();
            if(cardNumber.contains(" ")){
                cardNumber = cardNumber.replaceAll("\\s+", "");
            }
            if(cardNumber.contains("　")){
                cardNumber = cardNumber.replaceAll("　", "");
            }
            newCard.setCardNumber(cardNumber);
        }
        //会员卡手机号处理空格
        if(StrKit.notBlank(newCard.getTelephone())){
            String telephone = newCard.getTelephone();
            if(telephone.contains(" ")){
                telephone = telephone.replaceAll("\\s+", "");
            }
            if(telephone.contains("　")){
                telephone = telephone.replaceAll("　", "");
            }
            newCard.setTelephone(telephone);
        }
        //会员卡合同号处理空格
        if(StrKit.notBlank(newCard.getContractNumber())){
            String contractNumber = newCard.getContractNumber();
            if(contractNumber.contains(" ")){
                contractNumber = contractNumber.replaceAll("\\s+", "");
            }
            if(contractNumber.contains("　")){
                contractNumber = contractNumber.replaceAll("　", "");
            }
            newCard.setContractNumber(contractNumber);
        }
        //姓名的姓处理空格
        if(StrKit.notBlank(member.getSurname())){
            String surname = member.getSurname();
            if(surname.contains(" ")){
                surname = surname.replaceAll("\\s+", "");
            }
            if(surname.contains("　")){
                surname = surname.replaceAll("　", "");
            }
            member.setSurname(surname);
        }
        //姓名处理空格
        if(StrKit.notBlank(member.getFullName())){
            String fullName = member.getFullName();
            if(fullName.contains(" ")){
                fullName = fullName.replaceAll("\\s+", "");
            }
            if(fullName.contains("　")){
                fullName = fullName.replaceAll("　", "");
            }
            member.setFullName(fullName);
        }
        //身份证处理空格
        if(StrKit.notBlank(member.getIdcard())){
            String idcard = member.getIdcard();
            if(idcard.contains(" ")){
                idcard = idcard.replaceAll("\\s+", "");
            }
            if(idcard.contains("　")){
                idcard = idcard.replaceAll("　", "");
            }
            member.setIdcard(idcard);
        }
        //调用旅居身份证校验接口
        Map<String,String> params=new HashMap<>();
        params.put("name", member.getFullName());//卡主姓名
        params.put("idCardType", member.getIdcardType());//卡主证件类型
        params.put("idCard", member.getIdcard());//卡主身份证
        params.put("userId", userId);//操作人用户id
        params.put("channel", "AddMemberInfo");//渠道来源
        params.put("key", "40FA2FDE-9B79-40EC-8147-50F1C7EEF7DE");//key
        final String resultStr = HttpClientsUtils.httpPostForm(Global.checkIdCardUrl,params,null,null);
        logger.info("resultStr==="+resultStr);
        JSONObject returnJson = JSONObject.parseObject(resultStr);
        final int returnType = returnJson.getIntValue("Type");
        final String returnMsg = returnJson.getString("Msg");
        logger.info("身份证校验返回returnType==="+returnType);
        logger.info("身份证校验返回returnMsg==="+returnMsg);
        //姓名、身份证校验成功
        if(returnType!=1){
            map.put("error", newCard.getCardNumber() + "卡，" + returnMsg);
        }else{
            if("0".equals(transfer.getType())){
                map = finaMembershipCardService.cardTransferChangeName(transferDetailList,newCard,member,userId,transfer);
            }else{
                //renderJson(Ret.fail("msg",map.get("该功能正在调整中")));
                //return;
                map = finaMembershipCardService.cardTransferUpgrade(transferDetailList,newCard,member,userId,transfer);
            }
        }

        if(map.containsKey("error")){
            renderJson(Ret.fail("msg",map.get("error")));
        }else if(map.containsKey("suc")){
            //更新会员卡号、合同编号、收据号为已使用状态
            Global.executorService.execute(new Runnable() {
                @Override
                public void run() {
                    List<String> nosList = new ArrayList<>();
                    MainMembershipCardType cardType = mainMembershipCardTypeService.findById(newCard.getCardTypeId());
                    if(cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
                        if(StrKit.notBlank(newCard.getCardNumber())){
                            nosList.add(newCard.getCardNumber());
                        }
                        if(StrKit.notBlank(newCard.getContractNumber())){
                            nosList.add(newCard.getContractNumber());
                        }
//                    for(FinaCardCollect collect : cardCollectList){
//                        if(StrKit.notBlank(collect.getReceiptNumber())){
//                            if(!nosList.contains(collect.getReceiptNumber())){
//                                nosList.add(collect.getReceiptNumber());
//                            }
//                        }
//                    }
                        logger.info("nosList===" + JSON.toJSONString(nosList));
                        if(nosList!=null && nosList.size()>0){
                            Map<String,String> paramMap=new HashMap<>();
                            paramMap.put("nos", JSON.toJSONString(nosList));
                            paramMap.put("status","Use");
                            paramMap.put("userId",userId);
                            paramMap.put("description",null);
                            final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Member/UseCardAreaUnpackNo",paramMap,null,null);
                            logger.info("调用更新合同编号接口返回==="+resultStr+"；参数是："+paramMap.toString());
                        }
                    }
                }
            });
            renderJson(Ret.ok("msg",map.get("suc")));
        }
    }


    /**
     * 确认会员卡是否属于该会员
     */
    public void confirmCardMember(){
        String cardNumber = getPara("cardNumber");
        String fullName = getPara("fullName");

        String flag = finaMembershipCardService.confirmCardMember(cardNumber,fullName);
        if("".equals(flag)){
            renderJson(Ret.ok());
        }else{
            renderJson(Ret.fail("msg",flag));
        }
    }

    /**
     * 锁定会员卡
     */
    public void lockCard(){
        String id=getPara("id");
        String lockRemark=getPara("lockRemark");
        FinaMembershipCard card=new FinaMembershipCard();
        card.setId(id);
        card.setIsLock("1");
        card.setLockDate(new Date());
        card.setLockBy(AuthUtils.getUserId());
        card.setLockRemark(lockRemark);
        card.setUpdateBy(AuthUtils.getUserId());
        card.setUpdateTime(new Date());
        boolean flag=finaMembershipCardService.update(card);
        if(flag){
			JSONObject paramObj = new JSONObject();
        	paramObj.put("id", id);
        	paramObj.put("isLock", "1");
        	paramObj.put("updateBy", card.getUpdateBy());
        	paramObj.put("updateTime", card.getUpdateTime());
        	//同步到wms
        	final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),card.getUpdateBy());
			logger.info("同步到wms标识为：" + retFlag);
            renderJson(Ret.ok("msg","锁定成功"));
        }else{
            renderJson(Ret.fail("msg","锁定失败"));
        }
    }
    
    /**
     * 作废会员卡
     */
    public void voidCard(){
    	String id = getPara("id");
    	String voidRemark = getPara("voidRemark");
    	final String userId = AuthUtils.getUserId();
        if(StrKit.isBlank(id)){
        	renderJson(Ret.fail("msg", "作废卡id为空,作废失败!"));
        	return;
        }
        if(StrKit.isBlank(voidRemark)){
        	renderJson(Ret.fail("msg", "作废备注为空,作废失败!"));
        	return;
        }
        FinaMembershipCard memberCard = finaMembershipCardService.findById(id);
        if(memberCard!=null){
        	//统计旅居结算账单数量
        	final int settleCount = Db.queryInt("select count(id)settleCount from fina_settle_detail where del_flag='0' and settle_status='0' and card_number=?", memberCard.getCardNumber());
        	if(settleCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的旅居账单，作废失败!"));
        		return;
        	}
        	
        	//统计旅居结算账单数量
            final int touristCount = Db.queryInt("select count(a.id) touristCount from fina_expense_record_tourist_settle_detail a " +
                    " where a.del_flag='0' and a.is_settle='0' and a.card_id=? ", memberCard.getId());
        	if(touristCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的旅游团账单，作废失败!"));
        		return;
        	}
        	
        	//统计养老长住账单数量
        	final int pensionCount = Db.queryInt("select count(id)pensionCount from fina_member_bill_detail where del_flag='0' and status='wait_settlement' and account_number=?", memberCard.getCardNumber());
        	if(pensionCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的养老长住账单，作废失败!"));
        		return;
        	}
        	
        	//统计旅居违约账单数量
        	final int punishCount = Db.queryInt("select count(id)punishCount from fina_punish_record where del_flag='0' and status='0' and card_number=?", memberCard.getCardNumber());
        	if(punishCount>0){
        		renderJson(Ret.fail("msg", "还有未结算的旅居违约账单，作废失败!"));
        		return;
        	}
        	FinaMembershipCard card=new FinaMembershipCard();
        	card.setId(id);
        	card.setDelFlag("1");
        	card.setVoidDate(new Date());
        	card.setVoidBy(userId);
        	card.setVoidRemark(voidRemark);
        	card.setUpdateBy(userId);
        	card.setUpdateTime(new Date());
        	if(finaMembershipCardService.update(card)){
        		renderJson(Ret.ok("msg","作废成功"));
                MainMembershipCardType cardType = mainMembershipCardTypeService.findById(memberCard.getCardTypeId());
                if(cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
                    List<String> nosList = new ArrayList<>();
                    if(StrKit.notBlank(memberCard.getCardNumber())){
                        nosList.add(memberCard.getCardNumber());
                    }
                    if(StrKit.notBlank(memberCard.getContractNumber())){
                        final String oldContractNumber = Db.queryStr("select contract_number from fina_membership_card where del_flag=? and card_number=? and contract_number=?", DelFlag.NORMAL, memberCard.getCardNumber(), memberCard.getContractNumber());
                        if(StrKit.isBlank(oldContractNumber) && StrKit.notBlank(memberCard.getContractNumber())){
                            nosList.add(memberCard.getContractNumber());
                        }
                    }
                    List<FinaCardCollect> cardCollectList = finaCardCollectService.findList(id);
                    for(FinaCardCollect collect : cardCollectList){
                        if(StrKit.notBlank(collect.getReceiptNumber())){
                            nosList.add(collect.getReceiptNumber());
                        }
                    }
                    //更新合同编号为已使用状态
                    Global.executorService.execute(new Runnable() {
                        @Override
                        public void run() {
                            Map<String,String> paramMap=new HashMap<>();
                            paramMap.put("nos", JSON.toJSONString(nosList));
                            paramMap.put("status","Delete");
                            paramMap.put("userId",userId);
                            paramMap.put("description",null);
                            final String resultStr = HttpClientsUtils.httpPostForm(Global.bmpUrl+"/Member/UseCardAreaUnpackNo",paramMap,null,null);
                            logger.info("调用更新合同编号接口返回==="+resultStr);
                        }
                    });
                    //同步到wms
                    JSONObject paramObj = new JSONObject();
                    paramObj.put("id", id);
                    paramObj.put("delFlag", "1");
                    paramObj.put("updateBy", card.getUpdateBy());
                    paramObj.put("updateTime", card.getUpdateTime());
                    final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),card.getUpdateBy());
                    logger.info("同步到wms标识为：" + retFlag);
                }
        	}else{
        		renderJson(Ret.fail("msg","作废失败"));
        	}
        }else{
        	renderJson(Ret.fail("msg", "找不到该会员卡,作废失败"));
        	return;
        }
    }

    /**
     * 解锁会员卡
     */
    public void unlockCard(){
        String id=getPara("id");

        FinaMembershipCard card=new FinaMembershipCard();
        card.setId(id);
        card.setIsLock("0");
        card.setUpdateBy(AuthUtils.getUserId());
        card.setUpdateTime(new Date());
        boolean flag=finaMembershipCardService.update(card);
        if(flag){
            FinaMembershipCard memberCard = finaMembershipCardService.findById(id);
            if(memberCard!=null){
                MainMembershipCardType cardType = mainMembershipCardTypeService.findById(memberCard.getCardTypeId());
                if(cardType!=null && "consume_card".equalsIgnoreCase(cardType.getTypeCategory())){
                    JSONObject paramObj = new JSONObject();
                    paramObj.put("id", id);
                    paramObj.put("isLock", "0");
                    paramObj.put("updateBy", card.getUpdateBy());
                    paramObj.put("updateTime", card.getUpdateTime());
                    //同步到wms
                    final boolean retFlag = mainSyncRecordService.saveSyncRecord(SyncType.finaMembershipCard.getKey(), SyncDataType.UPDATE, paramObj.toJSONString(),card.getUpdateBy());
                    logger.info("同步到wms标识为：" + retFlag);
                }
            }
            renderJson(Ret.ok("msg","解锁成功"));
        }else{
            renderJson(Ret.fail("msg","解锁失败"));
        }
    }

    public void getCardDeduct(){
        String cardNumber=getPara("cardNumber");
        if(StrKit.isBlank(cardNumber)){
            renderJson(Ret.fail("msg","会员卡号不能为空"));
            return;
        }
        FinaMembershipCard card=finaMembershipCardService.getCardByNumber(cardNumber);
        if(card==null){
            renderJson(Ret.fail("msg","该会员卡不存在"));
            return;
        }
        if("1".equals(card.getIsLock())){
            renderJson(Ret.fail("msg","该会员卡为锁定状态"));
            return;
        }
        MainCardDeductScheme scheme=mainCardDeductSchemeService.findById(card.getDeductSchemeId());
        if(scheme==null){
            scheme=mainCardDeductSchemeService.findById(card.getLongDeductSchemeId());
        }
        if(scheme==null){
            renderJson(Ret.fail("msg","该会员卡的扣除类型为空"));
            return;
        }
        Map<String,String> map=new HashMap<>();
        map.put("deductScheme",scheme.getDeductWay());
        renderJson(Ret.ok("data",map).set("msg","获取会员卡信息成功"));
    }
    

    /**
     * 导出excel
     */
    public void exportData(){
    	FinaMembershipCard card = getBean(FinaMembershipCard.class,"",true);
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        String province = getPara("province");
        String city = getPara("city");
        String town = getPara("town");
        String street = getPara("street");
        String noDealStartDate = getPara("noDealStartDate");
        String noDealEndDate = getPara("noDealEndDate");
        String nearExpireStartYear = getPara("nearExpireStartYear");
        String nearExpireEndYear = getPara("nearExpireEndYear");
        String orderBy = getPara("orderBy");
        List<Record> recordList = finaMembershipCardService.findList(card,fullName,idcard,province,city,town,street,noDealStartDate,noDealEndDate,nearExpireStartYear,nearExpireEndYear,orderBy);
        if(recordList==null || recordList.size()==0){
            renderJson(Ret.fail("msg","未查询到会员卡记录"));
            return;
        }

        String[] title={"会员卡号","状态","类别","姓名","性别","身份证号","手机号","剩余天数","剩余金额","剩余点数","剩余积分","备注","开卡时间","作废时间","创建时间"};
        String fileName="会员卡记录.xls";
        String sheetName = "会员卡记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);
            final String isLock = record.getStr("isLock");
            final String lockByName = record.getStr("lockByName")!=null?record.getStr("lockByName"):"无";
            final String lockDate = record.getDate("lockDate")!=null?DateUtils.formatDate(record.getDate("lockDate"), "yyyy-MM-dd HH:mm:ss"):"无";
            final String lockRemark = record.getStr("lockRemark")!=null?record.getStr("lockRemark"):"无";
            final String lockUserName = record.getStr("lockUserName")!=null?record.getStr("lockUserName"):"无";
            final Date openTime = record.getDate("openTime");
            final Date createTime = record.getDate("createTime");
            final Date voidDate = record.getDate("voidDate");
            
            content[i][0]=record.getStr("cardNumber");
            if("1".equals(isLock)) {
            	content[i][1]="已锁定"+";锁定人："+lockByName+";锁定帐号："+lockUserName+";锁定时间："+lockDate+";锁定备注："+lockRemark;
            }else {
            	content[i][1]="未锁定";
            }
            content[i][2]=record.getStr("cardType");
            content[i][3]=record.getStr("fullName");
            final String gender = record.getStr("gender");
            if("1".equals(gender)){
            	content[i][4]="男";
            }else if("2".equals(gender)){
            	content[i][4]="女";
            }else{
            	content[i][4]="未知";
            }
            content[i][5]=record.getStr("idcard");
            content[i][6]=record.getStr("telephone");
            content[i][7]=record.getStr("consumeTimes");
            content[i][8]=record.getStr("balance");
            content[i][9]=record.getStr("consumePoints");
            content[i][10]=record.getStr("cardIntegrals");
            content[i][11]=record.getStr("describe");
            if(openTime!=null){
            	content[i][12]=DateUtils.formatDate(openTime, "yyyy-MM-dd");
            }else{
            	content[i][12]=null;
            }
            if(voidDate!=null){
            	content[i][13]=DateUtils.formatDate(voidDate, "yyyy-MM-dd HH:mm:ss");
            }else{
            	content[i][13]=null;
            }
            if(createTime!=null){
            	content[i][14]=DateUtils.formatDate(createTime, "yyyy-MM-dd HH:mm:ss");
            }else{
            	content[i][14]=null;
            }
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public void cardMonthBalanceIndex(){
        String yearMonth=DateUtils.getLastMonth();
        List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
        setAttr("typeList",typeList);
        setAttr("yearMonth",yearMonth);
        render("cardMonthBalance.html");
    }
    
    public void cardMonthlyEndIndex(){
    	String yearMonth = DateUtils.getLastMonth();
//    	List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
//    	setAttr("typeList",typeList);
    	setAttr("yearMonth",yearMonth);
    	render("cardMonthlyEnd.html");
    }
    
    public void cardMonthlyEndDetail(){
    	final String yearMonth = getPara("yearMonth");
    	List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
    	setAttr("yearMonth",yearMonth);
    	setAttr("typeList",typeList);
    	render("cardMonthlyEndDetail.html");
    }

    public void cardMonthBalancePage(){
        int page=getParaToInt("page");
        int limit=getParaToInt("limit");
        String yearMonth=getPara("yearMonth");
        String cardNumber=getPara("cardNumber");
        String typeCategory=getPara("typeCategory");
        String cardTypeId=getPara("cardTypeId");
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.getLastMonth();
        }
        String sql=" select a.*,c.type_category,c.card_type,d.full_name from fina_card_month_balance_new a " +
                " inner join fina_membership_card b on b.id=(select id from fina_membership_card "
                + "where a.card_number=card_number ORDER BY del_flag asc,create_time desc  limit 1) " +
                " left join main_membership_card_type c on c.id=b.card_type_id " +
                " left join mms_member d on d.id=b.member_id " +
                " where a.del_flag='0' and a.`year_month`=? ";
        /*String totalRowSql="select count(b.id) from fina_card_month_balance_new b "
        		+ "inner join fina_membership_card c on c.card_number=b.card_number "
        		+ "left join main_membership_card_type t on t.id=c.card_type_id "
        		+ "where b.del_flag='0' and b.`year_month`=? ";*/

        String totalRowSql="select count(b.id) from fina_card_month_balance_new b "
                + "inner join fina_membership_card c on c.id=(select id from fina_membership_card where b.card_number=card_number ORDER BY del_flag asc,create_time desc  limit 1) "
                + "left join main_membership_card_type t on t.id=c.card_type_id "
                + "where b.del_flag='0' and b.`year_month`=? ";
        List<String> params=new ArrayList<>();
        params.add(yearMonth);
        if(StrKit.notBlank(typeCategory)){
        	sql+=" and c.type_category=? ";
        	totalRowSql+=" and t.type_category=? ";
        	params.add(typeCategory);
        }
        if(StrKit.notBlank(cardTypeId)){
        	sql+=" and b.card_type_id=? ";
        	totalRowSql+=" and c.card_type_id=? ";
        	params.add(cardTypeId);
        }
        if(StrKit.notBlank(cardNumber)){
            sql+=" and a.card_number=? ";
            totalRowSql+=" and b.card_number=? ";
            params.add(cardNumber);
        }
        Page<Record> recordPage=Db.paginateByFullSql(page,limit,totalRowSql,sql,params.toArray());
        renderJson(new DataTable<Record>(recordPage));
    }
    
    public void cardMonthlyEndPage(){
    	FinaCardMonthlyEnd model = getBean(FinaCardMonthlyEnd.class,"",true);
    	Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
    	if(StrKit.notBlank(model.getYearMonth())) {
    		columns.eq("year_month", model.getYearMonth());
    	}
    	Page<FinaCardMonthlyEnd> modelPage = finaCardMonthlyEndService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "`year_month` desc");
    	renderJson(new DataTable<FinaCardMonthlyEnd>(modelPage));
    }
    
    public void cardMonthlyEndDetailPage(){
    	final String yearMonth = getPara("yearMonth");
    	final String cardTypeId = getPara("cardTypeId");
    	final String cardNumber = getPara("cardNumber");
    	Page<Record> modelPage = finaCardTransactionsService.cardTransactionsPricePage(yearMonth, cardTypeId, cardNumber, getParaToInt("page", 1), getParaToInt("limit", 10));
    	renderJson(new DataTable<Record>(modelPage));
    }
    
    public void monthlyEndDetailPage(){
    	final String yearMonth = getPara("yearMonth");
    	final String cardNumber = getPara("cardNumber");
    	Page<Record> modelPage = finaCardTransactionsService.monthlyEndDetailPage(yearMonth, cardNumber, getParaToInt("page", 1), getParaToInt("limit", 10));
    	renderJson(new DataTable<Record>(modelPage));
    }

    public void cardMonthBalanceExport(){
        String yearMonth=getPara("yearMonth");
        String cardNumber=getPara("cardNumber");
        String typeCategory=getPara("typeCategory");
        String cardTypeId=getPara("cardTypeId");
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.getLastMonth();
        }
        String sql=" select a.*,c.type_category,c.card_type,d.full_name from fina_card_month_balance_new a " +
                " left join fina_membership_card b on b.id=(select id from fina_membership_card "
                + "where a.card_number=card_number ORDER BY del_flag asc,create_time desc  limit 1 ) " +
                " left join main_membership_card_type c on c.id=b.card_type_id  " +
                " left join mms_member d on d.id=b.member_id " +
                " where a.del_flag='0' and a.`year_month`=? ";
        List<String> params=new ArrayList<>();
        params.add(yearMonth);
        if(StrKit.notBlank(typeCategory)){
        	sql+=" and c.type_category=? ";
        	params.add(typeCategory);
        }
        if(StrKit.notBlank(cardTypeId)){
        	sql+=" and b.card_type_id=? ";
        	params.add(cardTypeId);
        }
        if(StrKit.notBlank(cardNumber)){
            sql+=" and a.card_number=? ";
            params.add(cardNumber);
        }
        List<Record> recordList=Db.find(sql,params.toArray());

        String[] title={"会员卡号","卡类别类型","会员卡类别","客户名","期初天数","期初金额","期初积分","期初豆豆券","期初账面余额","充值天数","充值金额","充值积分","充值豆豆券","赠送积分"
                ,"消费天数","消费金额","消费积分","消费豆豆券","过期豆豆券","消费账面余额","期末结存天数","期末结存金额","期末结存积分","期末结存豆豆券","期末结存账面余额"};
        String fileName="会员卡结存"+yearMonth+".xls";
        String sheetName = "会员卡结存"+yearMonth;

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);

            content[i][0]=record.getStr("card_number");
            content[i][1]=record.getStr("type_category");
            content[i][2]=record.getStr("card_type");
            content[i][3]=record.getStr("full_name");
            content[i][4]=record.getStr("last_month_times");
            content[i][5]=record.getStr("last_month_amount");
            content[i][6]=record.getStr("last_month_integrals");
            content[i][7]=record.getStr("last_month_bean_coupons");
            content[i][8]=record.getStr("last_month_account_balance");

            content[i][9]=record.getStr("recharge_times");
            content[i][10]=record.getStr("recharge_amount");
            content[i][11]=record.getStr("recharge_integrals");
            content[i][12]=record.getStr("recharge_bean_coupons");
            content[i][13]=record.getStr("give_integrals");
            content[i][14]=record.getStr("consume_times");
            content[i][15]=record.getStr("consume_amount");
            content[i][16]=record.getStr("consume_integrals");
            content[i][17]=record.getStr("consume_bean_coupons");
            content[i][18]=record.getStr("expiration_bean_coupons");
            content[i][19]=record.getStr("consume_account_balance");


            content[i][20]=record.getStr("times");
            content[i][21]=record.getStr("amount");
            content[i][22]=record.getStr("integrals");
            content[i][23]=record.getStr("bean_coupons");
            content[i][24]=record.getStr("account_balance");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 会员卡收入月结
     */
    @Clear(LogInterceptor.class)
    public void cardMonthlyEnd(){
    	String yearMonth = getPara("yearMonth");
    	if(StrKit.isBlank(yearMonth)){
    		yearMonth = DateUtils.getDate("yyyy-MM");
    	}
    	
    	int settleCount = 0;
    	String checkinNos = "";
    	//统计旅居结算账单数量
    	Record settleRecord = Db.findFirst("select count(d.id)settleCount,GROUP_CONCAT(checkin_no)checkinNos from fina_settle_detail d " +
            "left join fina_expense_record r on r.id=d.expense_id " +
            "left join fina_membership_card c on c.card_number=d.card_number " +
            "left join main_membership_card_type t on t.id=c.card_type_id " +
            "where r.settle_status='0' and r.is_no_monthly_end='0' and r.category='sojourn_bill' and t.type_classify in ('member_card','give_card') and d.del_flag='0' and d.settle_status='0' and d.`year_month`<=?", yearMonth);
    	if(settleRecord!=null){
    		settleCount = settleRecord.getInt("settleCount");
    		checkinNos = settleRecord.getStr("checkinNos");
    	}
    	if(settleCount>0){
    		renderJson(Ret.fail("msg", yearMonth+"月份前面还有"+settleCount+"条未结算的旅居账单(入住号是："+checkinNos+")，请结算完再月结!"));
    		return;
    	}
    	
    	int touristCount = 0;
    	String touristNos = "";
    	//统计旅游团结算账单数量
    	Record touristRecord = Db.findFirst("select count(r.id)touristCount,GROUP_CONCAT(r.tourist_no)touristNos from fina_expense_record r " +
            "left join fina_expense_record_tourist t on t.tourist_no=r.tourist_no " +
            "where t.del_flag='0' and r.del_flag='0' and r.category='tourist_bill' and r.card_number!='182613' and r.settle_status='0' and DATE_FORMAT(r.checkout_time,'%Y-%m')<=?", yearMonth);
    	if(touristRecord!=null){
    		touristCount = touristRecord.getInt("touristCount");
    		touristNos = touristRecord.getStr("touristNos");
    	}
    	if(touristCount>0){
    		renderJson(Ret.fail("msg", yearMonth+"月份前面还有"+touristCount+"条未结算的旅游团账单(旅游团号是："+touristNos+")，请结算完再月结!"));
    		return;
    	}
    	
    	//统计机构结算账单数量
//    	final int orgSettleCount = Db.queryInt("select count(id)orgSettleCount from fina_member_settle_main " +
//                "where settle_status='no_settle' and `year_month`<=?", yearMonth);
//    	if(orgSettleCount>0){
//    		renderJson(Ret.fail("msg", yearMonth+"月份前面还有"+orgSettleCount+"条未结算的机构账单，请结算完再月结!"));
//    		return;
//    	}
    	
    	//统计月结是否存在
    	final int monthlyEndCount = Db.queryInt("select count(id)monthlyEndCount from fina_card_monthly_end where del_flag='0' and `year_month`=?", yearMonth);
    	if(monthlyEndCount>0){
//    		renderJson(Ret.fail("msg", "月结月份已存在，请不要重复月结!"));
//    		return;
    		finaCardMonthlyEndService.cardMonthlyEndVoid(yearMonth, AuthUtils.getUserId());
    	}
    	
    	//统计月结是否存在数据
//    	final int monthlyEndDataCount = Db.queryInt("select count(id)monthlyEndDataCount from fina_card_monthly_end where del_flag='0'");
//    	if(monthlyEndDataCount>0){//月结数据存在正常月结
//    	}else{//月结数据为空，初始化
//    	}
    	if(finaCardMonthlyEndService.cardMonthlyEnd(yearMonth, AuthUtils.getUserId())){
    		renderJson(Ret.ok("msg", "操作成功"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败"));
    	}
    }
    
    /**
     * 跳转到扣卡统计界面
     */
    public void deductStatistics(){
    	String yearMonthDay=DateUtils.getDate("yyyy-MM-dd");
        String yearMonth=DateUtils.getDate("yyyy-MM");
        String year=DateUtils.getDate("yyyy");
        setAttr("yearMonthDay",yearMonthDay);
        setAttr("yearMonth",yearMonth);
        setAttr("year",year);
    	render("deductStatistics.html");
    }
    
    
    /**
     * 扣卡统计数据
     */
    @Clear(LogInterceptor.class)
    public void deductStatisticsData(){
    	String queryType = getPara("queryType");
    	String yearMonthDay = getPara("yearMonthDay");
    	String yearMonth = getPara("yearMonth");
    	String year = getPara("year");
    	List<Record> list = finaCardTransactionsService.deductStatistics(queryType, yearMonthDay, yearMonth, year);
    	renderJson(Ret.ok("msg", "加载成功").set("data", list.get(0)));
    }
    
    
    /**
     * 扣卡统计明细分页
     */
    @Clear(LogInterceptor.class)
    public void deductStatisticsPage(){
    	String queryType = getPara("queryType");
    	String yearMonthDay = getPara("yearMonthDay");
    	String yearMonth = getPara("yearMonth");
    	String year = getPara("year");
    	Page<Record> page = finaCardTransactionsService.deductStatisticsPage(queryType, yearMonthDay, yearMonth, year,getParaToInt("page"),getParaToInt("limit"));
    	renderJson(new DataTable<Record>(page));
    }
    
    /**
     * 导出excel
     */
    public void deductStatisticsExport(){
    	String queryType = getPara("queryType");
    	String yearMonthDay = getPara("yearMonthDay");
    	String yearMonth = getPara("yearMonth");
    	String year = getPara("year");
        List<Record> recordList=finaCardTransactionsService.deductStatisticsList(queryType, yearMonthDay, yearMonth, year);
        if(recordList==null || recordList.size()==0){
            renderJson(Ret.fail("msg","未查询到充值记录"));
            return;
        }

        String[] title={"会员卡信息","卡类别","扣卡类型","金额","天数","点数","积分","豆豆券","收入","扣卡时间","说明"};
        String fileName="扣卡记录.xls";
        String sheetName = "扣卡记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);
            
            String cardInfo = "";
            if(StrKit.notBlank(record.getStr("cardNumber"))) {
            	cardInfo += record.getStr("cardNumber");
            }else {
            	cardInfo += "--";
            }
            cardInfo += " ";
            if(StrKit.notBlank(record.getStr("cardMemberName"))) {
            	cardInfo += record.getStr("cardMemberName");
            }else {
            	cardInfo += "--";
            }

            content[i][0]=cardInfo;
            content[i][1]=record.getStr("cardTypeName");
            String typeName = "";
            if(StrKit.notBlank(record.getStr("type"))) {
            	typeName = dictService.getDictNameByTypeValue("account_record_operate_type", record.getStr("type"));
            }
            content[i][2]=typeName;
            content[i][3]=record.getStr("amount")+"元";
            content[i][4]=record.getStr("times")+"天";
            content[i][5]=record.getStr("points");
            content[i][6]=record.getStr("integrals");
            content[i][7]=record.getStr("beanCoupons");
            content[i][8]=record.getStr("incomeAmount")+"元";
            content[i][9]=record.getStr("dealTime");
            content[i][10]=record.getStr("describe");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    public void memberCardReconciliationIndex(){
        String cardSql="select a.id,a.card_number as cardNumber,b.full_name as fullName from fina_membership_card a " +
                "left join mms_member b on a.member_id=b.id " +
                " where a.card_type_id in ('2070AD12-A04C-487D-B9DF-B0668398F27C') and a.del_flag='0' and a.is_lock='0' ";
        List<Record> cardRecordList = Db.find(cardSql);

        setAttr("cardRecordList",cardRecordList);
        setAttr("yearMonth",DateUtils.getLastMonth());
        setAttr("maxYearMonth",DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(DateUtils.formatDate(new Date(),"yyyy-MM")+"-01"),-1),"yyyy-MM-dd"));
        render("memberCardReconciliationIndex.html");
    }

    public void memberCardReconciliationPageList(){
        String cardId=getPara("cardId");
        String orderType = getPara("orderType");
        String yearMonth=getPara("yearMonth");

        if(StrKit.isBlank(orderType)){
            orderType="1";
        }
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }

        if("1".equals(orderType)){
            List<Object> params=new ArrayList<>();

            String sql="select a.id,a.card_number,a.actual_times,a.actual_amount,a.actual_integrals,b.`name`,a.update_time,b.checkin_type " +
                    ",(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and del_flag='0' and settle_status in ('0','1')) as followCount" +
                    ",b.checkin_no,d.base_name,e.full_name,d.base_name,if(f.id  is null,'0','1') as isReconciliation,a.`year_month`,a.start_time,a.end_time" +
                    ",f.`status`,f.`remark` as reconciliationRemark,f.actual_times as reconciliationTimes,f.actual_amount as reconciliationAmount,f.actual_integrals as reconciliationIntegrals " +
                    " from fina_settle_detail a " +
                    "inner join fina_membership_card c on c.id=(select id from fina_membership_card where card_type_id='2070AD12-A04C-487D-B9DF-B0668398F27C' and card_number=a.card_number ORDER BY del_flag,create_time desc limit 1) " +
                    "inner join fina_expense_record b on a.expense_id=b.id " +
                    " left join main_base d on d.id=b.base_id " +
                    " inner join mms_member e on e.id=c.member_id " +
                    " left join fina_card_reconciliation_record f on f.record_id=a.id " +
                    "where a.del_flag='0' and a.settle_status='1' and b.settle_status in ('0','1') and b.del_flag='0' ";
            if(StrKit.notBlank(cardId)){
                sql+=" and c.id=? ";
                params.add(cardId);
            }
            if(StrKit.notBlank(yearMonth)){
                sql+=" and a.`year_month`=? ";
                params.add(yearMonth);
            }
            sql+=" ORDER BY a.update_time desc ";
            List<Record> recordList=Db.find(sql,params.toArray());
            renderJson(new DataTable<Record>(recordList));
        }else if("2".equals(orderType)){
            List<Object> params=new ArrayList<>();
            String sql=" select a.id,b.card_number,a.actual_times,a.actual_amount,a.actual_integrals,c.`name`,a.update_time,d.full_name,c.tourist_no,e.`name` as tourist_name," +
                    "c.checkin_time,c.checkout_time,c.remark,a.finance_remark,if(f.id  is null,'0','1') as isReconciliation " +
                    "from fina_expense_record_tourist_settle_detail a " +
                    "inner join fina_membership_card b on a.card_id=b.id and b.card_type_id='7B929A68-F940-4F7F-9312-71A414BA0DFD' " +
                    "inner join fina_expense_record c on c.id=a.expense_id inner join mms_member d on d.id=b.member_id " +
                    "inner join fina_expense_record_tourist e on e.tourist_no=c.tourist_no and e.del_flag='0'" +
                    " left join fina_card_reconciliation_record f on f.record_id=a.id " +
                    "where a.del_flag='0' and a.is_settle='1' and c.settle_status in ('0','1') and c.del_flag='0' ";
            if(StrKit.notBlank(cardId)){
                sql+=" and b.id=? ";
                params.add(cardId);
            }

            sql+=" ORDER BY a.update_time ";
            List<Record> recordList=Db.find(sql,params.toArray());
            renderJson(new DataTable<Record>(recordList));
        }else if("3".equals(orderType)){
            List<Object> params=new ArrayList<>();
            String sql=" select a.id,c.card_number,if(a.deduct_type='deduct_by_days',a.amount,0) as actual_times,if(a.deduct_type='deduct_by_money',a.amount,0) as actual_amount," +
                    "a.integrals as actual_integrals,CONCAT(b.`name`,':',a.`name`) as `name`,a.update_date,b.member_name,a.start_date,a.end_date,a.`describe`" +
                    ",a.remark,if(f.id  is null,'0','1') as isReconciliation,b.checkin_no,e.base_name,g.full_name,b.`year_month`,f.`status`,f.`remark` as reconciliationRemark" +
                    ",f.actual_times as reconciliationTimes,f.actual_amount as reconciliationAmount,f.actual_integrals as reconciliationIntegrals " +
                    " from fina_member_bill_detail a inner join fina_member_settle_main b on a.main_id=b.id " +
                    "inner join fina_membership_card c on c.id=(select id from fina_membership_card where card_type_id='2070AD12-A04C-487D-B9DF-B0668398F27C' and card_number=a.account_number ORDER BY del_flag,create_time desc limit 1)" +
                    " inner join mms_member d on d.id=c.member_id left join fina_card_reconciliation_record f on f.record_id=a.id " +
                    " inner join main_base e on e.id=b.base_id inner join mms_member g on g.id=c.member_id " +
                    "where a.`status`='settled' and a.del_flag='0' and b.settle_status in ('normal','no_settle')  ";

            if(StrKit.notBlank(cardId)){
                sql+=" and c.id=? ";
                params.add(cardId);
            }
            if(StrKit.notBlank(yearMonth)){
                sql+=" and b.`year_month`=? ";
                params.add(yearMonth);
            }
            sql+=" ORDER BY a.update_date desc ";
            List<Record> recordList=Db.find(sql,params.toArray());
            renderJson(new DataTable<Record>(recordList));
        }
    }

    public void cardReconciliationSave(){
        String ids = getPara("ids");
        String type=getPara("type");
        String yearMonth=getPara("yearMonth");
        String cardId=getPara("cardId");
        JSONArray jsonArray=JSON.parseArray(ids);
        if(jsonArray.size()==0){
            renderJson(Ret.fail("msg","所选记录条数不能为0"));
            return;
        }

        String sql="select * from fina_card_reconciliation_record where del_flag='0' and record_id=? ";
        List<FinaCardReconciliationRecord> reconciliationRecordList=new ArrayList<>();

        FinaMembershipCard card = finaMembershipCardService.findById(cardId);

        for (int i = 0; i < jsonArray.size(); i++) {
            String id=jsonArray.getString(i);
            if(Db.findFirst(sql,id)==null){
                FinaCardReconciliationRecord reconciliationRecord=new FinaCardReconciliationRecord();
                reconciliationRecord.setId(IdGen.getUUID());
                reconciliationRecord.setRecordId(id);
                reconciliationRecord.setDelFlag("0");
                reconciliationRecord.setType(type);
                reconciliationRecord.setCreateBy(AuthUtils.getUserId());
                reconciliationRecord.setCreateDate(new Date());
                reconciliationRecord.setUpdateBy(AuthUtils.getUserId());
                reconciliationRecord.setUpdateDate(new Date());
                reconciliationRecord.setTimes(0.0);
                reconciliationRecord.setAmount(0.0);
                reconciliationRecord.setIntegrals(0.0);

                reconciliationRecord.setStatus("1");
                reconciliationRecord.setCardId(cardId);
                reconciliationRecord.setBaseId(card.getBaseId());
                reconciliationRecord.setYearMonth(yearMonth);
                if("1".equals(type)){
                    FinaSettleDetail settleDetail = finaSettleDetailService.findById(id);
                    FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(settleDetail.getExpenseId());
                    reconciliationRecord.setFullName(expenseRecord.getName());
                    if(settleDetail.getActualTimes()!=null){
                        reconciliationRecord.setTimes(settleDetail.getActualTimes());
                    }
                    if(settleDetail.getActualAmount()!=null){
                        reconciliationRecord.setAmount(settleDetail.getActualAmount());
                    }
                    if(settleDetail.getActualIntegrals()!=null){
                        reconciliationRecord.setIntegrals(settleDetail.getActualIntegrals());
                    }
                    reconciliationRecord.setBaseId(expenseRecord.getBaseId());
                    reconciliationRecord.setStartDate(settleDetail.getStartTime());
                    reconciliationRecord.setEndDate(settleDetail.getEndTime());
                }else if("3".equals(type)){
                    FinaMemberBillDetail billDetail = finaMemberBillDetailService.findById(id);
                    FinaMemberSettleMain settleMain = finaMemberSettleMainService.findById(billDetail.getMainId());

                    reconciliationRecord.setFullName(settleMain.getMemberName());
                    if(billDetail.getAmount()!=null){

                        if("deduct_by_days".equals(billDetail.getDeductType())){
                            reconciliationRecord.setTimes(billDetail.getAmount());
                        }else if("deduct_by_money".equals(billDetail.getDeductType())){
                            reconciliationRecord.setAmount(billDetail.getAmount());
                        }
                    }

                    if(billDetail.getIntegrals()!=null){
                        reconciliationRecord.setIntegrals(billDetail.getIntegrals());
                    }
                    reconciliationRecord.setBillName(billDetail.getName());
                    reconciliationRecord.setBaseId(settleMain.getBaseId());
                    reconciliationRecord.setStartDate(billDetail.getStartDate());
                    reconciliationRecord.setEndDate(billDetail.getEndDate());
                }
                reconciliationRecord.setActualTimes(reconciliationRecord.getTimes());
                reconciliationRecord.setActualAmount(reconciliationRecord.getAmount());
                reconciliationRecord.setActualIntegrals(reconciliationRecord.getIntegrals());

                reconciliationRecordList.add(reconciliationRecord);
            }
        }
        if(reconciliationRecordList.size()==0){
            renderJson(Ret.fail("msg","操作失败，需要对账的记录为0条"));
            return;
        }
        try {
            Db.batchSave(reconciliationRecordList,reconciliationRecordList.size());
            //
            saveTotalRecord(cardId,yearMonth);

            renderJson(Ret.ok("msg","操作成功"));
            return;
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","操作失败，程序异常"));
            return;
        }

    }

    public void cardReconciliationExceptionSave(){
        FinaCardReconciliationRecord reconciliationRecord=getBean(FinaCardReconciliationRecord.class,"",true);
        System.out.println(reconciliationRecord);

        String sql="select * from fina_card_reconciliation_record where del_flag='0' and record_id=? ";
        if(Db.findFirst(sql,reconciliationRecord.getRecordId())!=null){
            renderJson(Ret.fail("msg","该记录已对账过，请刷新列表"));
            return;
        }
        FinaMembershipCard card = finaMembershipCardService.findById(reconciliationRecord.getCardId());

        reconciliationRecord.setId(IdGen.getUUID());
        reconciliationRecord.setDelFlag("0");
        reconciliationRecord.setCreateBy(AuthUtils.getUserId());
        reconciliationRecord.setCreateDate(new Date());
        reconciliationRecord.setUpdateBy(AuthUtils.getUserId());
        reconciliationRecord.setUpdateDate(new Date());
        reconciliationRecord.setBaseId(card.getBaseId());
        if("1".equals(reconciliationRecord.getType())){
            FinaSettleDetail settleDetail = finaSettleDetailService.findById(reconciliationRecord.getRecordId());
            FinaExpenseRecord expenseRecord=finaExpenseRecordService.findById(settleDetail.getExpenseId());
            reconciliationRecord.setFullName(expenseRecord.getName());
            reconciliationRecord.setBaseId(expenseRecord.getBaseId());
            reconciliationRecord.setStartDate(settleDetail.getStartTime());
            reconciliationRecord.setEndDate(settleDetail.getEndTime());
        }else if("3".equals(reconciliationRecord.getType())){
            FinaMemberBillDetail billDetail = finaMemberBillDetailService.findById(reconciliationRecord.getRecordId());
            FinaMemberSettleMain settleMain = finaMemberSettleMainService.findById(billDetail.getMainId());
            reconciliationRecord.setFullName(settleMain.getMemberName());
            reconciliationRecord.setBillName(billDetail.getName());
            reconciliationRecord.setBaseId(settleMain.getBaseId());
            reconciliationRecord.setStartDate(billDetail.getStartDate());
            reconciliationRecord.setEndDate(billDetail.getEndDate());
        }

        boolean flag=reconciliationRecord.save();
        if(flag){
            saveTotalRecord(reconciliationRecord.getCardId(),reconciliationRecord.getYearMonth());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveTotalRecord(String cardId,String yearMonth){
        FinaMembershipCard card = finaMembershipCardService.findById(cardId);
        List<Object> params=new ArrayList<>();
        String recordSql="select a.id,a.card_number,a.actual_times,a.actual_amount,a.actual_integrals,b.`name`,a.update_time,b.checkin_type " +
                ",(select COUNT(DISTINCT id_card) from fina_expense_record where parent_id=b.id and del_flag='0' and settle_status in ('0','1')) as followCount" +
                ",b.checkin_no,d.base_name,e.full_name,d.base_name,if(f.id  is null,'0','1') as isReconciliation,a.`year_month`,a.start_time,a.end_time " +
                " from fina_settle_detail a " +
                "inner join fina_expense_record b on a.expense_id=b.id " +
                "inner join fina_membership_card c on c.id=(select id from fina_membership_card where card_type_id='2070AD12-A04C-487D-B9DF-B0668398F27C' and card_number=a.card_number ORDER BY del_flag,create_time desc limit 1) " +
                " inner join main_base d on d.id=b.base_id " +
                " inner join mms_member e on e.id=c.member_id " +
                " left join fina_card_reconciliation_record f on f.record_id=a.id " +
                "where a.del_flag='0' and a.settle_status in ('1','0') and b.settle_status in ('0','1') and b.del_flag='0' and f.id is null ";
        if(StrKit.notBlank(cardId)){
            recordSql+=" and c.id=? ";
            params.add(cardId);
        }
        if(StrKit.notBlank(yearMonth)){
            recordSql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        recordSql+=" ORDER BY a.update_time desc ";
        List<Record> recordList=Db.find(recordSql,params.toArray());


        List<Object> pensionParams=new ArrayList<>();
        String pensionSql=" select a.id,c.card_number,if(a.deduct_type='deduct_by_days',a.amount,0) as actual_times,if(a.deduct_type='deduct_by_money',a.amount,0) as actual_amount," +
                "a.integrals as actual_integrals,CONCAT(b.`name`,':',a.`name`) as `name`,a.update_date,b.member_name,a.start_date,a.end_date,a.`describe`" +
                ",a.remark,if(f.id  is null,'0','1') as isReconciliation,b.checkin_no,e.base_name,g.full_name,b.`year_month` " +
                " from fina_member_bill_detail a inner join fina_member_settle_main b on a.main_id=b.id " +
                "inner join fina_membership_card c on c.id=(select id from fina_membership_card where card_type_id='2070AD12-A04C-487D-B9DF-B0668398F27C' and card_number=a.account_number ORDER BY del_flag,create_time desc limit 1)" +
                " inner join mms_member d on d.id=c.member_id left join fina_card_reconciliation_record f on f.record_id=a.id " +
                " inner join main_base e on e.id=b.base_id inner join mms_member g on g.id=c.member_id " +
                "where a.`status` in ('settled','wait_settlement') and a.del_flag='0' and b.settle_status in ('normal','no_settle') and f.id is null  ";

        if(StrKit.notBlank(cardId)){
            pensionSql+=" and c.id=? ";
            pensionParams.add(cardId);
        }
        if(StrKit.notBlank(yearMonth)){
            pensionSql+=" and b.`year_month`=? ";
            pensionParams.add(yearMonth);
        }
        pensionSql+=" ORDER BY a.update_date desc ";
        List<Record> pensionRecordList=Db.find(pensionSql,pensionParams.toArray());

        if(recordList.size()==0 && pensionRecordList.size()==0){
            FinaCardReconciliationTotalRecord cardReconciliationTotalRecord=new FinaCardReconciliationTotalRecord();
            cardReconciliationTotalRecord.setYearMonth(yearMonth);
            cardReconciliationTotalRecord.setId(IdGen.getUUID());
            String totalSql="select * " +
                    "from fina_card_reconciliation_record where del_flag='0' and card_id=? and `year_month`=?";
            List<Record> list=Db.find(totalSql,cardId,yearMonth);

            double totalTimes=0.0;
            double totalAmount=0.0;
            double totalIntegrals=0.0;
            double actualTotalTimes=0.0;
            double actualTotalAmount=0.0;
            double actualTotalIntegrals=0.0;

            String str="";
            List<String> ids=new ArrayList<>();
            ids.add(cardReconciliationTotalRecord.getId());
            for (Record record : list) {
                Double times = record.getDouble("times");
                Double amount = record.getDouble("amount");
                Double integrals = record.getDouble("integrals");
                Double actualTimes = record.getDouble("actual_times");
                Double actualAmount = record.getDouble("actual_amount");
                Double actualIntegrals = record.getDouble("actual_integrals");
                if(times!=null){
                    totalTimes+=times;
                }
                if(amount!=null){
                    totalAmount+=amount;
                }
                if(integrals!=null){
                    totalIntegrals+=integrals;
                }
                if(actualTimes!=null){
                    actualTotalTimes+=actualTimes;
                }
                if(actualAmount!=null){
                    actualTotalAmount+=actualAmount;
                }
                if(actualIntegrals!=null){
                    actualTotalIntegrals+=actualIntegrals;
                }
                str+="?,";
                ids.add(record.getStr("id"));
            }
            str=str.substring(0,str.length()-1);

            cardReconciliationTotalRecord.setDelFlag("0");
            cardReconciliationTotalRecord.setBaseId(card.getBaseId());

            cardReconciliationTotalRecord.setCardId(cardId);
            cardReconciliationTotalRecord.setTotalTimes(0.0);
            cardReconciliationTotalRecord.setTotalAmount(0.0);
            cardReconciliationTotalRecord.setTotalIntegrals(0.0);
            cardReconciliationTotalRecord.setUpdateDate(new Date());
            cardReconciliationTotalRecord.setCreateDate(new Date());
            cardReconciliationTotalRecord.setStatus("0");
            cardReconciliationTotalRecord.setTotalTimes(totalTimes);
            cardReconciliationTotalRecord.setTotalAmount(totalAmount);
            cardReconciliationTotalRecord.setTotalIntegrals(totalIntegrals);
            cardReconciliationTotalRecord.setActualTotalTimes(actualTotalTimes);
            cardReconciliationTotalRecord.setActualTotalAmount(actualTotalAmount);
            cardReconciliationTotalRecord.setActualTotalIntegrals(actualTotalIntegrals);
            cardReconciliationTotalRecord.setBaseId(card.getBaseId());

            boolean flag=cardReconciliationTotalRecord.save();
            if(flag){
                Db.update("update fina_card_reconciliation_record set total_record_id=? where id in("+str+")",ids.toArray());
            }
        }
    }

    public void baseMemberCardReconciliationIndex(){

        List<Record> cardList=Db.find("select b.id,b.card_number as cardNumber,c.full_name as fullName from pers_org a " +
                "inner join fina_membership_card b on a.join_id=b.base_id inner join mms_member c on c.id=b.member_id " +
                " where link_man=? and a.del_flag='0' and b.del_flag='0' GROUP BY b.id",AuthUtils.getUserId());
        setAttr("cardList",cardList);
        setAttr("yearMonth",DateUtils.getLastMonth());
        setAttr("maxYearMonth",DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(DateUtils.formatDate(new Date(),"yyyy-MM")+"-01"),-1),"yyyy-MM-dd"));
        render("baseMemberCardReconciliationIndex.html");
    }

    public void baseMemberCardReconciliationPageList(){
        String cardId=getPara("cardId");
        String yearMonth=getPara("yearMonth");
        String status=getPara("status");
        String select="select a.*,b.base_name,c.card_number,e.full_name ";
        String sql=" from fina_card_reconciliation_total_record a  " +
                "left join main_base b on a.base_id=b.id " +
                "left join fina_membership_card c on c.id=a.card_id " +
                "left join mms_member e on e.id=c.member_id " +
                "left join sys_user d on d.id=a.verifier_id " +
                "where a.del_flag='0' and a.card_id=? and a.year_month=? and a.`status`=? ";
        if(StrKit.isBlank(cardId)){
            renderJson(new DataTable<Record>(new Page<>()));
            return;
        }
        if(StrKit.isBlank(status)){
            status="0";
        }
        List<Object> params=new ArrayList<>();
        params.add(cardId);
        params.add(yearMonth);
        params.add(status);


        Page<Record> recordPage=Db.paginate(getParaToInt("page"),getParaToInt("limit"),select,sql,params.toArray());
        if(recordPage.getList()!=null){
            for (Record record : recordPage.getList()) {
                String id=record.getStr("id");
                Integer totalNum=Db.queryInt("select count(id) from fina_card_reconciliation_record where del_flag='0' and total_record_id=? ",id);
                Integer normalNum=Db.queryInt("select count(id) from fina_card_reconciliation_record where del_flag='0' and total_record_id=? and `status`='1'",id);
                Integer exceptionNum=Db.queryInt("select count(id) from fina_card_reconciliation_record where del_flag='0' and total_record_id=? and `status`='0'",id);
                record.set("totalNum",totalNum);
                record.set("normalNum",normalNum);
                record.set("exceptionNum",exceptionNum);
            }
        }
        renderJson(new DataTable<Record>(recordPage));
    }

    public void totalRecordReview(){
        String id=getPara("id");
        Record record=Db.findById("fina_card_reconciliation_total_record","id",id);
        if(record!=null && "1".equals(record.getStr("status"))){
            renderJson(Ret.fail("msg","该记录已审核过了，请勿重复操作"));
            return;
        }
        int num=Db.update("update fina_card_reconciliation_total_record set `status`='1',update_date=now() where id=? ",id);
        if(num>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败，程序异常"));

        }
    }

    public void baseMemberCardReconciliationDetailIndex(){
        String id = getPara("id");
        setAttr("id",id);
        render("baseMemberCardReconciliationDetailIndex.html");
    }

    public void baseMemberCardReconciliationDetailPageList(){
        String id = getPara("id");

        String sql="select a.*,b.base_name from fina_card_reconciliation_record a  left join main_base b on a.base_id=b.id " +
                "  where a.del_flag='0' and total_record_id=? order by a.status,a.create_date ";
        List<Record> recordList=Db.find(sql,id);
        renderJson(new DataTable<Record>(recordList));
    }
    
    /**
     * 福利积分卡导入并生成档案
     */
    public void cardImport(){
    	UploadFile uploadFile = getFile();
    	Ret returnRet = Ret.fail("msg","导入失败");
        if(uploadFile == null){
        	returnRet = Ret.fail("msg","上传文件不存在，请检查!");
        }else{
        	try{
        		returnRet =  finaMembershipCardService.importCardList(uploadFile.getFile(),uploadFile.getFileName(), AuthUtils.getUserId());
        	}catch (Exception e){
        		logger.error("模板批量充值导入失败:[{}],[{}]",e.getMessage(),e);
        		e.printStackTrace();
        	}
        	if(uploadFile.getFile().exists()){
        		uploadFile.getFile().delete();
        	}
        }
       renderJson(returnRet);
    }

    /**
     * 世纪昌松卡导入并生成档案
     */
    public void shiJiCardImport(){
    	UploadFile uploadFile = getFile();
    	Ret returnRet = Ret.fail("msg","导入失败");
        if(uploadFile == null){
        	returnRet = Ret.fail("msg","上传文件不存在，请检查!");
        }else{
        	try{
        		returnRet =  finaMembershipCardService.importShiJiCardList(uploadFile.getFile(),uploadFile.getFileName(), AuthUtils.getUserId());
        	}catch (Exception e){
        		logger.error("模板批量充值导入失败:[{}],[{}]",e.getMessage(),e);
        		e.printStackTrace();
        	}
        	if(uploadFile.getFile().exists()){
        		uploadFile.getFile().delete();
        	}
        }
       renderJson(returnRet);
    }

    public void empTableForm(){

        render("empTable.html");
    }

    public void empPageTable() {
        PersOrgEmployee orgEmployee = getBean(PersOrgEmployee.class, "", true);
//    	String status=getPara("status");
        String sort=getPara("sort");
        String isContainChildren=getPara("isContainChildren");
        String userId=null;
        /*if(!"system_admin".equals(AuthUtils.getLoginUser().getUserType())){
            userId=AuthUtils.getUserId();
        }*/

        Page<PersOrgEmployee> orgEmployeePage = persOrgEmployeeService
                .paginateByCondition(orgEmployee,userId,sort,isContainChildren,null,null,null,null,null,null,null, getParaToInt("page", 1), getParaToInt("limit", 10));

        renderJson(new DataTable<PersOrgEmployee>(orgEmployeePage));
    }

    public void allOrgDtree(){
        List<ZTree> orgFormTree=null;

        orgFormTree = persOrgService.orgFormTreeByUserId(null,null);
        Map<String,Object> result=new HashMap<>();
        JSONObject obj=new JSONObject();
        obj.put("code",200);
        obj.put("message","操作成功");

        result.put("status",obj);
        result.put("data",orgFormTree);

        renderJson(result);
    }
}
