package com.cszn.finance.web.controller.fina;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.*;
import com.cszn.integrated.service.api.main.MainBaseBuildingService;
import com.cszn.integrated.service.api.main.MainBaseFloorService;
import com.cszn.integrated.service.api.main.MainBaseRoomService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.pers.PersApprovalService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.wms.WmsSupplierService;
import com.cszn.integrated.service.entity.enums.EarnestMoneyPaymentType;
import com.cszn.integrated.service.entity.enums.FinaTaskType;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBaseBuilding;
import com.cszn.integrated.service.entity.main.MainBaseFloor;
import com.cszn.integrated.service.entity.main.MainBaseRoom;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.PayWay;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.wms.WmsSuppliers;
import com.cszn.integrated.service.provider.fina.FinaLeaseRecordApplyServiceImpl;
import com.cszn.integrated.service.provider.fina.FinaLeaseRecordRoomServiceImpl;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;


@RequestMapping(value="/fina/leaseRecordApply", viewPath="/modules_page/finance/fina/lease")
public class FinaLeaseRecordApplyController extends BaseController {

    @Inject
    FinaLeaseRecordApplyService finaLeaseRecordApplyService;
    @Inject
    PersOrgService persOrgService;
    @Inject
    WmsSupplierService wmsSupplierService;
    @Inject
    MainBaseService mainBaseService;
    @Inject
    UserService userService;
    @Inject
    FinaLeaseRecordRoomService finaLeaseRecordRoomService;
    @Inject
    FinaLeaseRecordRoomPaymentService finaLeaseRecordRoomPaymentService;
    @Inject
    MainBaseBuildingService mainBaseBuildingService;
    @Inject
    MainBaseFloorService mainBaseFloorService;
    @Inject
    FinaTaskService finaTaskService;
    @Inject
    PersApprovalService persApprovalService;
    @Inject
    PersPositionService persPositionService;
    @Inject
    FinaLeaseRecordRoomPaymentApplyRelService finaLeaseRecordRoomPaymentApplyRelService;
    @Inject
    FinaLeaseRecordRoomPaymentApplyService finaLeaseRecordRoomPaymentApplyService;
    @Inject
    MainBaseRoomService mainBaseRoomService;



    public void index(){
        setAttr("baseList",mainBaseService.findBaseList());
        render("applyIndex.html");
    }

    public void leaseRecordApplyPageList(){
        FinaLeaseRecordApply leaseRecordApply=getBean(FinaLeaseRecordApply.class,"",true);
        String roomName=getPara("roomName");
        String supplierName=getPara("supplierName");

        Page<Record> recordPage=finaLeaseRecordApplyService.leaseRecordApplyPage(getParaToInt("page"),getParaToInt("limit"),leaseRecordApply,supplierName,roomName);

        if(recordPage.getList()!=null){
            for (Record record : recordPage.getList()) {
                if (StrKit.notBlank(record.getStr("taskId"))) {
                    Map<String,Object> taskDetail=persApprovalService.getTaskDetail(record.getStr("taskId"),AuthUtils.getUserId());
                    record.set("currentStepName",taskDetail.get("currentStepName"));
                    record.set("isCurrentUser",taskDetail.get("isCurrentUser"));
                }
                record.set("isEdit",AuthUtils.getUserId().equalsIgnoreCase(record.getStr("applyId")));


            }
        }

        renderJson(new DataTable<Record>(recordPage));
    }

    public void form(){

        String taskId=getPara("taskId");
        String userId=getPara("userId");
        String id=getPara("id");
        User user =null;
        setAttr("isEdit",true);
        if(StrKit.notBlank(userId)){
            user = userService.findById(userId);
            setAttr("type","H5");
        }else{
            user =AuthUtils.getLoginUser();
        }
        setAttr("user",user);
        String empId= Db.queryStr("select emp_id from pers_emp_user where user_id=? ",user.getId());
        List<Record> deptRecordList=Db.find("select relationship_id as deptId from pers_org_employee_rel  where emp_id=? and relationship_type='dept' group by relationship_id order by is_main desc ",empId);

        for (Record record : deptRecordList) {
            record.set("deptName",persOrgService.getOrgParentNames(record.getStr("deptId")));

            List<Record> positionRecordList=Db.find("select b.id,b.position_name as positionName from pers_org_employee_rel a INNER JOIN pers_position b on a.relationship_id=b.id " +
                    "where relationship_type='position' and a.emp_id=? and b.org_id=? ",empId,record.getStr("deptId"));
            record.set("positionListStr",JSON.toJSONString(positionRecordList));
            record.set("positionList",positionRecordList);
        }
        set("deptRecordList",deptRecordList);
        setAttr("baseList",mainBaseService.findBaseList());
        FinaLeaseRecordApply leaseRecordApply=null;
        if(StrKit.notBlank(taskId)){
            leaseRecordApply=finaLeaseRecordApplyService.findByTaskId(taskId);
        }else if(StrKit.notBlank(id)){
            leaseRecordApply=finaLeaseRecordApplyService.findById(id);
        }
        if(leaseRecordApply!=null){
            String roomSql="select a.*,b.room_name from fina_lease_record_room a inner join main_base_room b on a.room_id=b.id where a.del_flag='0' and lease_id=? ";
            List<Record> recordList=Db.find(roomSql,leaseRecordApply.getId());
            for (Record record : recordList) {
                List<Record> roomFileList = Db.find("select * from fina_lease_record_room_file where del_flag='0' and lease_room_id=? ORDER BY create_date ", record.getStr("id"));
                if(roomFileList!=null && roomFileList.size()>0){
                    JSONArray roomFileArray=new JSONArray();
                    for (Record roomFile : roomFileList) {
                        JSONObject fileObj=new JSONObject();
                        fileObj.put("fileName",roomFile.getStr("file_name"));
                        fileObj.put("fileUrl",roomFile.getStr("file_url"));
                        fileObj.put("id",roomFile.getStr("id"));
                        roomFileArray.add(fileObj);
                    }
                    record.set("fileJsonArrayStr",JSON.toJSONString(roomFileArray));
                }
                record.set("roomFileList",roomFileList);
            }

            List<Record> supplierPayList=Db.find(" select * from wms_suppliers_pay where del_flag='0' and is_enabled='1' and supplier_id=? and payment_way='7' ",leaseRecordApply.getSupplierId());
            for (Record record : supplierPayList) {
                String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
                record.set("paymentWayStr",paymentWayStr);
            }
            setAttr("supplier",wmsSupplierService.findById(leaseRecordApply.getSupplierId()));
            setAttr("supplierPayList",supplierPayList);
            setAttr("roomRecordList",recordList);
            setAttr("leaseRecordApply",leaseRecordApply);

            if(StrKit.notBlank(leaseRecordApply.getTaskId())){
                Map<String,Object> taskDetail=persApprovalService.getTaskDetail(leaseRecordApply.getTaskId(),user.getId());
                setAttr("stepts",taskDetail.get("stepts"));
                setAttr("taskState",taskDetail.get("taskState"));
                setAttr("isSaveHandle",taskDetail.get("isSaveHandle"));
                setAttr("currentStepName",taskDetail.get("currentStepName"));
                setAttr("currentStepAlias",taskDetail.get("currentStepAlias"));
                setAttr("taskId",leaseRecordApply.getTaskId());

                JSONArray currentSteps=(JSONArray)taskDetail.get("currentSteps");
                //批准
                boolean allowApprove=false;
                //拒绝
                boolean allowReject=false;
                //中止
                boolean allowAbort=false;
                //提交
                boolean allowSubmit=false;
                if(currentSteps!=null){
                    for(int i=0;i<currentSteps.size();i++){
                        JSONObject currentStep=currentSteps.getJSONObject(i);
                        boolean flag=false;
                        for(int j=0;j<currentStep.getJSONArray("UserIds").size();j++){
                            if(!flag && user.getId().equalsIgnoreCase(currentStep.getJSONArray("UserIds").getString(j))){
                                flag=true;
                            }
                        }
                        if(flag){
                            allowApprove=currentStep.getBoolean("AllowApprove");
                            allowReject=currentStep.getBoolean("AllowReject");
                            allowSubmit=currentStep.getBoolean("AllowSubmit");
                        }
                        allowAbort=currentStep.getBoolean("AllowAbort");
                    }
                }
                setAttr("allowAbort",allowAbort);
                setAttr("allowSubmit",allowSubmit);
                setAttr("allowReject",allowReject);
                setAttr("allowApprove",allowApprove);

                if(!(boolean)taskDetail.get("isSaveHandle")){
                    setAttr("submitUserName",userService.findById(leaseRecordApply.getApplyId()).getName());
                    setAttr("submitDeptName",persOrgService.getOrgParentNames(leaseRecordApply.getDeptId()));
                    setAttr("submitPositionName", persPositionService.findById(leaseRecordApply.getPositionId()).getPositionName());
                }
            }else{

                if(!AuthUtils.getUserId().equalsIgnoreCase(leaseRecordApply.getApplyId())){
                    setAttr("isEdit",false);
                    setAttr("submitUserName",userService.findById(leaseRecordApply.getApplyId()).getName());
                    setAttr("submitDeptName",persOrgService.getOrgParentNames(leaseRecordApply.getDeptId()));
                    setAttr("submitPositionName", persPositionService.findById(leaseRecordApply.getPositionId()).getPositionName());
                }

            }
        }
        setAttr("earnestMoneyPaymentType",EarnestMoneyPaymentType.values());
        setAttr("userList",userService.findUnlockUserList());
        setAttr("commonUpload", Global.commonUpload);
        render("applyForm.html");
    }

    public void delRoomFile(){
        String id=getPara("id");
        String userId=getPara("userId");
        int update = Db.update("update fina_lease_record_room_file set del_flag='1',update_date=now(),update_by=? where id=? ", userId, id);
        if(update>0){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void leaseRoomFileIndex(){
        setAttr("id",getPara("id"));
        setAttr("commonUpload",Global.commonUpload);
        setAttr("userId",AuthUtils.getUserId());
        render("leaseRoomFileIndex.html");
    }

    public void leaseRoomFileList(){
        String id=getPara("id");
        List<Record> recordList=Db.find("select * from fina_lease_record_room_file where del_flag='0' and lease_room_id=? ORDER BY create_date ",id);

        renderJson(new DataTable<Record>(recordList));
    }

    public void saveRoomFile(){

        FinaLeaseRecordRoomFile recordRoomFile=getBean(FinaLeaseRecordRoomFile.class,"",true);
        recordRoomFile.setId(IdGen.getUUID());
        recordRoomFile.setDelFlag("0");
        recordRoomFile.setCreateBy(AuthUtils.getUserId());
        recordRoomFile.setCreateDate(new Date());
        recordRoomFile.setUpdateBy(AuthUtils.getUserId());
        recordRoomFile.setUpdateDate(new Date());
        if(recordRoomFile.save()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void updateRoomContractendDate(){
        String id=getPara("id");
        String remark=getPara("remark");
        String endDateStr=getPara("endDate");

        Date endDate=DateUtils.parseDate(endDateStr);
        FinaLeaseRecordRoom leaseRecordRoom=finaLeaseRecordRoomService.findById(id);

        FinaLeaseRecordRoomEndDateRecord endDateRecord=new FinaLeaseRecordRoomEndDateRecord();
        endDateRecord.setId(IdGen.getUUID());
        endDateRecord.setRecordRoomId(leaseRecordRoom.getId());
        endDateRecord.setOldEndDate(leaseRecordRoom.getRoomEndDate());
        endDateRecord.setNewEndDate(endDate);
        endDateRecord.setDelFlag("0");
        endDateRecord.setRemark(remark);
        endDateRecord.setUpdateBy(AuthUtils.getUserId());
        endDateRecord.setUpdateDate(new Date());
        endDateRecord.setCreateBy(AuthUtils.getUserId());
        endDateRecord.setCreateDate(new Date());

        leaseRecordRoom.setRoomEndDate(endDate);
        leaseRecordRoom.setContractEndDate(endDate);
        String roomOrderNo=leaseRecordRoom.getOrderNo();



        List<FinaLeaseRecordRoomPayment> roomPaymentList = finaLeaseRecordRoomPaymentService.findListBYRecordRoomId(leaseRecordRoom.getId());
        for (FinaLeaseRecordRoomPayment leaseRecordRoomPayment : roomPaymentList) {
            if(leaseRecordRoomPayment.getStartDate().getTime()<=endDate.getTime()
                    &&
                    leaseRecordRoomPayment.getEndDate().getTime()>=endDate.getTime()
                    && "3".equals(leaseRecordRoomPayment.getStatus())){

                renderJson(Ret.fail("msg",DateUtils.formatDate(leaseRecordRoomPayment.getStartDate(),"yyyy-MM-dd")
                        +"至"+DateUtils.formatDate(leaseRecordRoomPayment.getEndDate(),"yyyy-MM-dd")+"的租金已为支付状态终止合同失败"));
                return;
            }
        }
        List<FinaLeaseRecordRoomPayment> leaseRecordRoomPaymentList = finaLeaseRecordApplyService.genRoomPaymentList(leaseRecordRoom);

        //作废的
        List<FinaLeaseRecordRoomPayment> delRoomPayment=new ArrayList<>();
        String orderNo="";
        for (FinaLeaseRecordRoomPayment leaseRecordRoomPayment : roomPaymentList) {
            if(leaseRecordRoomPayment.getStartDate().getTime()<=endDate.getTime()
                    &&
                    leaseRecordRoomPayment.getEndDate().getTime()>=endDate.getTime() ){
                leaseRecordRoomPayment.setDelFlag("1");
                leaseRecordRoomPayment.setUpdateDate(new Date());
                leaseRecordRoomPayment.setUpdateBy(AuthUtils.getUserId());
                delRoomPayment.add(leaseRecordRoomPayment);

                orderNo=leaseRecordRoomPayment.getOrderNo();
            }else if(leaseRecordRoomPayment.getEndDate().getTime()>=endDate.getTime()){
                leaseRecordRoomPayment.setDelFlag("1");
                leaseRecordRoomPayment.setUpdateDate(new Date());
                leaseRecordRoomPayment.setUpdateBy(AuthUtils.getUserId());
                delRoomPayment.add(leaseRecordRoomPayment);
            }
        }
        FinaLeaseRecordRoomPayment addRoomPayment = leaseRecordRoomPaymentList.get(leaseRecordRoomPaymentList.size() - 1);
        addRoomPayment.setOrderNo(orderNo);
        leaseRecordRoom.setOrderNo(roomOrderNo);

        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {

                try {
                    if(addRoomPayment.save() && leaseRecordRoom.update() && endDateRecord.save()){
                        if(delRoomPayment.size()>0){
                            Db.batchUpdate(delRoomPayment,delRoomPayment.size());
                        }
                        return true;
                    }
                    return false;
                }catch (Exception e){
                    e.printStackTrace();
                    return false;
                }
            }
        });
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void saveLeaseRecordApply(){
        FinaLeaseRecordApply leaseRecordApply=getBean(FinaLeaseRecordApply.class,"",true);
        Integer roomCount = getParaToInt("roomCount");
        String saveType=getPara("saveType");
        String userId=getPara("userId");

        List<FinaLeaseRecordRoom> leaseRecordRoomList=new ArrayList<>();
        Map<String,List<FinaLeaseRecordRoomFile>> recordRoomFileListMap=new HashMap<>();
        for (Integer i = 1; i <= roomCount; i++) {
            FinaLeaseRecordRoom recordRoom=getBean(FinaLeaseRecordRoom.class,"room"+i,true);
            if(StrKit.notBlank(recordRoom.getRoomId())){
                String[] contractDates = getPara("room" + i + ".contractDate").split(" - ");
                String[] roomDates=getPara("room"+i+".roomDate").split(" - ");
                recordRoom.setContractStartDate(DateUtils.parseDate(contractDates[0]));
                recordRoom.setContractEndDate(DateUtils.parseDate(contractDates[1]));
                recordRoom.setRoomStartDate(DateUtils.parseDate(roomDates[0]));
                recordRoom.setRoomEndDate(DateUtils.parseDate(roomDates[1]));
                if(DateUtils.compareDays(recordRoom.getRoomStartDate(),recordRoom.getContractStartDate())>0){
                    renderJson(Ret.fail("msg","房租付款开始时间不能早于合同期限开始时间"));
                    return;
                }
                if(DateUtils.compareDays(recordRoom.getRoomEndDate(),recordRoom.getContractEndDate())<0){
                    renderJson(Ret.fail("msg","房租付款结束时间不能大于合同期限结束时间"));
                    return;
                }
                //获取附件
                String roomFileJsonArrayStr = getPara("roomFile" + i);
                if(StrKit.notBlank(roomFileJsonArrayStr)){
                    JSONArray roomFileJsonArray= JSON.parseArray(roomFileJsonArrayStr);
                    if(roomFileJsonArray!=null && roomFileJsonArray.size()>0){
                        List<FinaLeaseRecordRoomFile> roomFileList=new ArrayList<>();
                        for (int j = 0; j < roomFileJsonArray.size(); j++) {
                            JSONObject jsonObject=roomFileJsonArray.getJSONObject(j);
                            if(StrKit.isBlank(jsonObject.getString("id"))){
                                FinaLeaseRecordRoomFile recordRoomFile=new FinaLeaseRecordRoomFile();
                                recordRoomFile.setId(IdGen.getUUID());
                                recordRoomFile.setFileName(jsonObject.getString("fileName"));
                                recordRoomFile.setFileUrl(jsonObject.getString("fileUrl"));
                                recordRoomFile.setDelFlag("0");
                                recordRoomFile.setCreateBy(userId);
                                recordRoomFile.setCreateDate(new Date());
                                recordRoomFile.setUpdateBy(userId);
                                recordRoomFile.setUpdateDate(new Date());

                                roomFileList.add(recordRoomFile);
                            }
                        }
                        recordRoomFileListMap.put(recordRoom.getRoomId(),roomFileList);

                    }


                }
                leaseRecordRoomList.add(recordRoom);
            }
        }

        boolean isAdd=false;
        if(StrKit.isBlank(leaseRecordApply.getId())){
            leaseRecordApply.setId(IdGen.getUUID());
            leaseRecordApply.setDelFlag("0");
            leaseRecordApply.setStatus("1");
            leaseRecordApply.setCreateBy(userId);
            leaseRecordApply.setCreateDate(new Date());
            leaseRecordApply.setUpdateBy(userId);
            leaseRecordApply.setUpdateDate(new Date());

            isAdd=true;
        }else{
            leaseRecordApply.setUpdateBy(userId);
            leaseRecordApply.setUpdateDate(new Date());
        }
        List<FinaLeaseRecordRoom> addList=new ArrayList<>();
        List<FinaLeaseRecordRoom> updateList=new ArrayList<>();
        for (FinaLeaseRecordRoom leaseRecordRoom : leaseRecordRoomList) {
            if(StrKit.isBlank(leaseRecordRoom.getId())){
                leaseRecordRoom.setId(IdGen.getUUID());
                leaseRecordRoom.setDelFlag("0");
                leaseRecordRoom.setLeaseId(leaseRecordApply.getId());
                leaseRecordRoom.setCreateBy(userId);
                leaseRecordRoom.setCreateDate(new Date());
                leaseRecordRoom.setUpdateBy(userId);
                leaseRecordRoom.setUpdateDate(new Date());
                leaseRecordRoom.setCurrentRent(leaseRecordRoom.getRent());
                addList.add(leaseRecordRoom);

            }else{
                leaseRecordRoom.setCurrentRent(leaseRecordRoom.getRent());
                leaseRecordRoom.setUpdateBy(userId);
                leaseRecordRoom.setUpdateDate(new Date());
                updateList.add(leaseRecordRoom);
            }
            List<FinaLeaseRecordRoomFile> roomFileList = recordRoomFileListMap.get(leaseRecordRoom.getRoomId());
            if(roomFileList!=null){
                for (FinaLeaseRecordRoomFile recordRoomFile : roomFileList) {
                    recordRoomFile.setLeaseRoomId(leaseRecordRoom.getId());
                }
            }
        }
        boolean flag=false;
        if(isAdd){
            flag=leaseRecordApply.save();
        }else{
            flag=leaseRecordApply.update();
        }
        if(flag){
            if(addList.size()>0){
                Db.batchSave(addList,addList.size());
            }
            if(updateList.size()>0){
                Db.batchUpdate(updateList,updateList.size());
            }
            if(recordRoomFileListMap.size()>0){
                for (String key : recordRoomFileListMap.keySet()) {
                    List<FinaLeaseRecordRoomFile> roomFileList = recordRoomFileListMap.get(key);
                    if(roomFileList!=null && roomFileList.size()>0){
                        Db.batchSave(roomFileList,roomFileList.size());
                    }
                }
            }

            if("2".equals(saveType)){
                //保存并提交
                Map<String,Object> resultMap=finaTaskService.createTask(FinaTaskType.lease.getTaskNo(),leaseRecordApply.getId(),leaseRecordApply.getApplyId());
                if((boolean)resultMap.get("flag")){
                    leaseRecordApply.setTaskId((String)resultMap.get("taskId"));
                    leaseRecordApply.setTaskNo(FinaTaskType.lease.getTaskNo());
                    leaseRecordApply.setStatus("2");


                    //leaseRecordApply.setStatus("3");
                    leaseRecordApply.update();
                    //finaLeaseRecordApplyService.taskCompleted(leaseRecordApply);
                }else{
                    renderJson(Ret.ok("msg",resultMap.get("msg")));
                    return;
                }
            }
        }

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    public void proprietorIndex(){
        render("proprietorIndex.html");
    }

    public void supplierPage(){
        WmsSuppliers wmsSupplier=getBean(WmsSuppliers.class,"",true);
        Page<WmsSuppliers> page=wmsSupplierService.findSupplierPageList(getParaToInt("page"),getParaToInt("limit"),wmsSupplier);

        renderJson(new DataTable<WmsSuppliers>(page));
    }

    public void suppliersPayList(){
        String supplierId=getPara("supplierId");

        List<Record> recordList=Db.find(" select * from wms_suppliers_pay where del_flag='0' and is_enabled='1' and supplier_id=? ",supplierId);

        for (Record record : recordList) {
            String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
            record.set("paymentWayStr",paymentWayStr);
        }

        renderJson(recordList);
    }


    public void delLeaseRoom(){
        String id=getPara("id");
        String userId=getPara("userId");

        FinaLeaseRecordRoom leaseRecordRoom =finaLeaseRecordRoomService.findById(id);
        leaseRecordRoom.setDelFlag("1");
        leaseRecordRoom.setUpdateBy(userId);
        leaseRecordRoom.setUpdateDate(new Date());
        if (leaseRecordRoom.update()) {
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void roomTableIndex(){
        String baseId=getPara("baseId");
        String trIndex=getPara("trIndex");

        List<MainBaseBuilding> buildingList=mainBaseBuildingService.getBuildingListByBaseIds(new String[]{baseId});

        setAttr("buildingList",buildingList);
        setAttr("baseId",baseId);
        setAttr("trIndex",trIndex);
        render("roomTableIndex.html");
    }

    public void getFloorByBuildingId(){
        String buildingId=getPara("buildingId");

        List<MainBaseFloor> floorList =mainBaseFloorService.getFloorListByBuildingIds(new String[]{buildingId});
        renderJson(floorList);
    }

    public void roomPage(){
        String baseId=getPara("baseId");
        String floorId = getPara("floorId");
        String buildingId = getPara("buildingId");
        String name = getPara("name");

        String select="select a.id,a.room_name,b.floor_name,c.building_name,d.type_name ";
        String sql=" from main_base_room a INNER JOIN main_base_floor b on a.floor_id=b.id " +
                "INNER JOIN main_base_building c on c.id=b.building_id " +
                "INNER JOIN main_room_type d on d.id=a.room_type where a.del_flag='0' and a.is_enable='0' and b.del_flag='0' and b.is_enable='0' and c.del_flag='0' and c.is_enable='0' ";
        List<String> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and c.base_id=? ";
            params.add(baseId);
        }

        if(StrKit.notBlank(buildingId)){
            sql+=" and c.id=? ";
            params.add(buildingId);
        }

        if(StrKit.notBlank(floorId)){
            sql+=" and b.id=? ";
            params.add(floorId);
        }

        if(StrKit.notBlank(name)){
            sql+=" and a.room_name like concat('%',?,'%') ";
            params.add(name);
        }

        sql+=" order by c.building_name,b.floor_no,a.room_name ";

        Page<Record> recordPage=Db.paginate(getParaToInt("page"),getParaToInt("limit"),select,sql,params.toArray());

        renderJson(new DataTable<Record>(recordPage));
    }

    public void doTask(){
        String taskId=getPara("taskId");
        String taskNo=getPara("taskNo");
        String userId=getPara("userId");
        String msg=getPara("msg");
        String currentStepAlias=getPara("currentStepAlias");
        String stepState=getPara("stepState");
        /*if(StrKit.isBlank(msg)){
            renderJson(Ret.fail("msg","意见不能为空"));
            return;
        }*/
        Map<String,Object> map=persApprovalService.doTask(taskId,taskNo,stepState,currentStepAlias,msg,userId);
        if((Boolean) map.get("flag")){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg",(String)map.get("msg")));
        }
    }

    public void leaseRoomIndex(){
        setAttr("baseList",mainBaseService.findBaseList());
        render("leaseRoomIndex.html");
    }

    public void leaseRoomPageList(){
        String baseId = getPara("baseId");
        String roomName=getPara("roomName");
        String supplierName=getPara("supplierName");
        String roomStartDate=getPara("roomStartDate");
        String roomEndDate=getPara("roomEndDate");
        String contractStatus = getPara("contractStatus");

        Page<Record> recordPage=finaLeaseRecordApplyService.leaseRoomPageList(getParaToInt("page"),getParaToInt("limit"),baseId,roomName,contractStatus,supplierName,roomStartDate,roomEndDate);

        renderJson(new DataTable<Record>(recordPage));
    }

    public void leaseRoomPageListExport(){
        String baseId = getPara("baseId");
        String roomName=getPara("roomName");
        String supplierName=getPara("supplierName");
        String roomStartDate=getPara("roomStartDate");
        String roomEndDate=getPara("roomEndDate");
        String contractStatus = getPara("contractStatus");

        Page<Record> recordPage=finaLeaseRecordApplyService.leaseRoomPageList(1,20000,baseId,roomName,contractStatus,supplierName,roomStartDate,roomEndDate);
        String[] title={"基地","房间","业主姓名","租金","租金单位","支付方式","押金","合同时间","房租付款期限","租金涨幅","已付数量/总付数量","合同状态","备注"};
        String fileName="基地返租房间.xls";
        String sheetName = "基地返租房间";
        String[][] content=new String[recordPage.getList().size()][title.length];
        if (recordPage.getList()!=null && recordPage.getList().size()>0) {
            for (int i = 0; i < recordPage.getList().size(); i++) {
                Record record = recordPage.getList().get(i);
                content[i][0]=record.getStr("baseName");
                content[i][1]=record.getStr("roomName");
                content[i][2]= record.getStr("supplierName");
                content[i][3]=record.getStr("rent");
                if("month".equals(record.getStr("rentUnit"))){
                    content[i][4]="/月";
                }else if("year".equals(record.getStr("rentUnit"))){
                    content[i][4]="/年";
                }
                String unit="";
                if("year".equals(record.getStr("paymentType"))) {
                    unit = "年付";
                }else if("halfYear".equals(record.getStr("paymentType"))){
                    unit="半年付";
                }else if("season".equals(record.getStr("paymentType"))){
                    unit="季付";
                }else if("month".equals(record.getStr("paymentType"))){
                    unit="月付";
                }else if("disposable".equals(record.getStr("paymentType"))){
                    unit="一次性付";
                }
                content[i][5]=unit;
                content[i][6]=record.getStr("earnestMoney");

                content[i][7]=DateUtils.formatDate(record.getDate("roomStartDate"),"yyyy-MM-dd")+"至"
                        +DateUtils.formatDate(record.getDate("roomEndDate"),"yyyy-MM-dd");
                content[i][8]=DateUtils.formatDate(record.getDate("contractStartDate"),"yyyy-MM-dd")+"至"
                        +DateUtils.formatDate(record.getDate("contractEndDate"),"yyyy-MM-dd");
                if(record.getInt("rentAddYear")!=null && record.getInt("rentAddRatio")!=null &&
                        record.getInt("rentAddYear")>0 && record.getInt("rentAddRatio")>0){
                    content[i][9]="每"+record.getInt("rentAddYear")+"年递增"+record.getInt("rentAddRatio")+"%";
                }else{
                    content[i][9]="";
                }
                content[i][10]=record.getStr("payCount")+"/"+record.getStr("totalCount");
                if("1".equals(record.getStr("contractStatus"))){
                    content[i][11]="未开始";
                }else if("2".equals(record.getStr("contractStatus"))){
                    content[i][11]="合同期中";
                }else if("3".equals(record.getStr("contractStatus"))){
                    content[i][11]="已结束";

                }
                if(StrKit.notBlank(record.getStr("remark"))){
                    content[i][12]=record.getStr("remark");
                }else{
                    content[i][12]="";
                }

            }
        }


        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void leaseRoomPaymentIndex(){
        String leaseId = getPara("leaseId");
        String roomId = getPara("roomId");

        setAttr("leaseId",leaseId);
        setAttr("roomId",roomId);
        render("leaseRoomPaymentIndex.html");
    }

    public void leaseRoomPaymentPageList(){
        String leaseId = getPara("leaseId");
        String roomId = getPara("roomId");

        List<Record> recordList=Db.find("select * from fina_lease_record_room_payment where lease_id=? and room_id=? and del_flag='0' ORDER BY issue_no ",leaseId,roomId);
        renderJson(new DataTable<Record>(recordList));
    }

    public void leaseRoomPaymentForm(){
        String id=getPara("id");

        Record record=Db.findFirst("select d.base_name as baseName,e.room_name as roomName,f.`name` as supplierName,b.contract_start_date as contractStartDate," +
                "b.contract_end_date as contractEndDate,b.rent,b.earnest_money as earnestMoney,g.payment_way as paymentWay,g.pay_account as payAccount," +
                "g.bank_account_name as bankAccountName,g.bank_source as bankSource,a.amount,a.start_date as startDate,a.end_date as endDate,case b.payment_type WHEN 'month' THEN '月' WHEN 'year' THEN '年' " +
                "WHEN 'season' THEN '季' else '' end as rentUnit,g.bank_type as bankType,a.issue_no as issueNo,a.record_room_id as recordRoomId," +
                "(select COUNT(id) from fina_lease_record_room_payment where del_flag='0' and record_room_id=a.record_room_id) as totalCount,a.need_pay_date as needPayDate,a.order_no as orderNo" +
                ",b.rent_add_year as rentAddYear,b.rent_add_ratio as rentAddRatio,b.room_start_date as roomStartDate,b.room_end_date as roomEndDate " +
                "from fina_lease_record_room_payment a inner join fina_lease_record_room b on a.record_room_id=b.id " +
                "inner join fina_lease_record_apply c on c.id=b.lease_id inner join main_base d on d.id=c.base_id " +
                "inner join main_base_room e on e.id=b.room_id inner join wms_suppliers f on f.id=c.supplier_id " +
                "inner join wms_suppliers_pay g on g.id=c.suppliers_pay_id where a.del_flag='0' and a.id=? ",id);
        //record.set("paymentWayStr",PayWay.me().desc(record.getStr("paymentWay")));
        record.set("paymentWayStr","转账");
        setAttr("model",record);

        if(record.getInt("rentAddRatio")!=null && record.getInt("rentAddYear")!=null
                && record.getInt("rentAddYear")>0 && record.getInt("rentAddRatio")>0 ){

            List<Record> rentAddList= FinaLeaseRecordRoomServiceImpl.getDateRent(record.getDate("roomStartDate"),record.getDate("roomEndDate")
                    ,record.getInt("rentAddYear"),record.getInt("rentAddRatio"),record.getDouble("rent"));
            record.set("rentAddList",rentAddList);
        }


        render("leaseRoomPaymentForm.html");
    }

    public void previewTableIndex(){
        String leaseId = getPara("leaseId");
        String roomId = getPara("roomId");

        setAttr("leaseId",leaseId);
        setAttr("roomId",roomId);
        render("previewTableIndex.html");
    }

    public void previewTable() {
        String leaseId = getPara("leaseId");
        String roomId = getPara("roomId");
        FinaLeaseRecordApply leaseRecordApply = finaLeaseRecordApplyService.findById(leaseId);

        List<FinaLeaseRecordRoom> leaseRecordRoomList = finaLeaseRecordRoomService.findListByLeaseId(leaseRecordApply.getId());
        List<FinaLeaseRecordRoom> newLeaseRecordRoomList = new ArrayList<>();

        for (FinaLeaseRecordRoom recordRoom : leaseRecordRoomList) {
            if (roomId.equalsIgnoreCase(recordRoom.getRoomId())) {
                newLeaseRecordRoomList.add(recordRoom);
                break;
            }
        }

        List<FinaLeaseRecordRoomPayment> leaseRecordRoomPaymentList = new ArrayList<>();


        Map<String, Integer> nullMaxOrderNoMpa = new HashMap<>();
        for (FinaLeaseRecordRoom leaseRecordRoom : newLeaseRecordRoomList) {
            Date contractStartDate = leaseRecordRoom.getContractStartDate();
            Date contractEndDate = leaseRecordRoom.getContractEndDate();
            Date computeStartDate = contractStartDate;

            String orderNoPrefix = "CSR";
            int no = 1;

            String orderNoMonth = DateUtils.formatDate(contractStartDate, "yyMM");
            String maxOrderNo = Db.queryStr("select max(order_no) from fina_lease_record_room where order_no like concat(?,'%') ", orderNoPrefix + orderNoMonth);

            String orderNo = "";
            if (StrKit.isBlank(maxOrderNo)) {
                Integer i = null;
                if (nullMaxOrderNoMpa.containsKey(orderNoPrefix + orderNoMonth)) {
                    i = nullMaxOrderNoMpa.get(orderNoPrefix + orderNoMonth) + 1;
                    nullMaxOrderNoMpa.put(orderNoPrefix + orderNoMonth, i);
                } else {
                    i = 1;
                    nullMaxOrderNoMpa.put(orderNoPrefix + orderNoMonth, i);
                }
                String numberStr = "";
                if (i < 10) {
                    numberStr = "00" + i;
                } else if (i < 100) {
                    numberStr = "0" + i;
                } else {
                    numberStr = "" + i;
                }
                orderNo = orderNoPrefix + orderNoMonth + numberStr;
            } else {
                Integer i = null;
                if (nullMaxOrderNoMpa.containsKey(orderNoPrefix + orderNoMonth)) {
                    i = nullMaxOrderNoMpa.get(orderNoPrefix + orderNoMonth) + 1;
                    nullMaxOrderNoMpa.put(orderNoPrefix + orderNoMonth, i);
                } else {
                    i = Integer.valueOf(maxOrderNo.replace(orderNoPrefix + orderNoMonth, "")) + 1;
                    nullMaxOrderNoMpa.put(orderNoPrefix + orderNoMonth, i);
                }
                String numberStr = "";
                if (i < 10) {
                    numberStr = "00" + i;
                } else if (i < 100) {
                    numberStr = "0" + i;
                } else {
                    numberStr = "" + i;
                }
                orderNo = orderNoPrefix + orderNoMonth + numberStr;
            }
            leaseRecordRoom.setUpdateDate(new Date());
            leaseRecordRoom.setOrderNo(orderNo);


            double monthRent = 0.0;
            double onceRent = 0.0;
            //double
            if ("year".equals(leaseRecordRoom.getRentUnit())) {
                if ("year".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent();
                } else if ("halfYear".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent() / 2;
                } else if ("season".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent() / 4;
                } else if ("month".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent() / 12;
                }
                monthRent = BigDecimal.valueOf(leaseRecordRoom.getRent()).divide(BigDecimal.valueOf(12.0), 2, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            } else if ("month".equals(leaseRecordRoom.getRentUnit())) {
                if ("year".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent() * 12;
                } else if ("halfYear".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent() * 6;
                } else if ("season".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent() * 3;
                } else if ("month".equals(leaseRecordRoom.getPaymentType())) {
                    onceRent = leaseRecordRoom.getRent();
                }
                monthRent = leaseRecordRoom.getRent();
            }

            //获取合同期内，租金涨幅不同时间不同租金
            List<Record> roomDateRentList = FinaLeaseRecordRoomServiceImpl
                    .getDateRent(leaseRecordRoom.getContractStartDate(), leaseRecordRoom.getContractEndDate(), leaseRecordRoom.getRentAddYear()
                            , leaseRecordRoom.getRentAddRatio(), monthRent);
            if ("disposable".equals(leaseRecordRoom.getPaymentType())) {
                //获取时长
                Map<String, Integer> map = DateUtils.getDateDiffYear(DateUtils.formatDate(leaseRecordRoom.getContractStartDate(), "yyyy-MM-dd")
                        , DateUtils.formatDate(DateUtils.getNextDay(leaseRecordRoom.getContractEndDate(), 1), "yyyy-MM-dd"));
                double money = 0.0;
                if (map.get("year") > 0) {
                    //租金是/年还是/月的单位
                    if ("year".equals(leaseRecordRoom.getRentUnit())) {
                        money = leaseRecordRoom.getRent() * map.get("year");
                    } else if ("month".equals(leaseRecordRoom.getRentUnit())) {
                        money = leaseRecordRoom.getRent() * 12 * map.get("year");
                    }
                }
                if (map.get("month") > 0) {
                    money += monthRent * map.get("month");
                }
                if (map.get("day") > 0) {
                    //获取开始时间月份的最大天数
                    int maxMonthDate = DateUtils.getMonthMaxDay(DateUtils.getNextDay(leaseRecordRoom.getContractEndDate(), -map.get("day")));
                    double dayMoney = BigDecimal.valueOf(map.get("day")).divide(BigDecimal.valueOf(maxMonthDate), 10, BigDecimal.ROUND_HALF_DOWN)
                            .multiply(BigDecimal.valueOf(monthRent))
                            .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                    money += dayMoney;
                }
                Date needPayDate = null;
                //期望付款日期
                if ("1".equals(leaseRecordRoom.getRentPaymentDateType())) {
                    needPayDate = DateUtils.getNextDay(leaseRecordRoom.getContractStartDate(), -leaseRecordRoom.getRentPaymentDateDay());
                } else if ("2".equals(leaseRecordRoom.getRentPaymentDateType())) {
                    needPayDate = DateUtils.getNextDay(leaseRecordRoom.getContractEndDate(), leaseRecordRoom.getRentPaymentDateDay());
                }
                FinaLeaseRecordRoomPayment leaseRecordRoomPayment = new FinaLeaseRecordRoomPayment();
                leaseRecordRoomPayment.setId(IdGen.getUUID());
                leaseRecordRoomPayment.setAmount(money);
                leaseRecordRoomPayment.setStartDate(leaseRecordRoom.getContractStartDate());
                leaseRecordRoomPayment.setNeedPayDate(needPayDate);
                leaseRecordRoomPayment.setEndDate(leaseRecordRoom.getContractEndDate());
                leaseRecordRoomPayment.setIssueNo(no);
                leaseRecordRoomPayment.setRoomId(leaseRecordRoom.getRoomId());
                leaseRecordRoomPayment.setLeaseId(leaseRecordRoom.getLeaseId());
                leaseRecordRoomPayment.setRecordRoomId(leaseRecordRoom.getId());
                leaseRecordRoomPayment.setStatus("1");
                leaseRecordRoomPayment.setDelFlag("0");
                leaseRecordRoomPayment.setSendStatus("0");
                leaseRecordRoomPayment.setSendCount(0);
                leaseRecordRoomPayment.setMonthRent(monthRent);
                leaseRecordRoomPayment.setCreateBy(leaseRecordRoom.getCreateBy());
                leaseRecordRoomPayment.setCreateDate(new Date());
                leaseRecordRoomPayment.setUpdateBy(leaseRecordRoom.getCreateBy());
                leaseRecordRoomPayment.setUpdateDate(new Date());
                if (no < 10) {
                    leaseRecordRoomPayment.setOrderNo(orderNo + "00" + no);
                } else if (no < 100) {
                    leaseRecordRoomPayment.setOrderNo(orderNo + "0" + no);
                } else {
                    leaseRecordRoomPayment.setOrderNo(orderNo + no);
                }
                leaseRecordRoomPaymentList.add(leaseRecordRoomPayment);
            } else {
                do {
                    Date computeEndDate = null;
                    int monthNum = 0;
                    if ("year".equals(leaseRecordRoom.getPaymentType())) {
                        computeEndDate = DateUtils.getNextDay(DateUtils.getNextYear(computeStartDate, 1), -1);
                        monthNum = 12;
                    } else if ("halfYear".equals(leaseRecordRoom.getPaymentType())) {
                        computeEndDate = DateUtils.getNextDay(DateUtils.getNextMonth(computeStartDate, 6), -1);
                        monthNum = 6;
                    } else if ("season".equals(leaseRecordRoom.getPaymentType())) {
                        computeEndDate = DateUtils.getNextDay(DateUtils.getNextMonth(computeStartDate, 3), -1);
                        monthNum = 3;
                    } else if ("month".equals(leaseRecordRoom.getPaymentType())) {
                        computeEndDate = DateUtils.getNextDay(DateUtils.getNextMonth(computeStartDate, 1), -1);
                        monthNum = 1;
                    }

                    Date paymentStartDate = null;
                    Date paymentEndDate = null;
                    double money = 0.00;
                    double currRent = 0.0;
                    if (DateUtils.compareDays(computeEndDate, contractEndDate) > 0) {
                        paymentStartDate = computeStartDate;
                        paymentEndDate = contractEndDate;
                        //总天数
                        long totalDays = DateUtils.getTwoDateBetweenDays(computeStartDate, computeEndDate) + 1;
                        //实际天数
                        long useDays = DateUtils.getTwoDateBetweenDays(paymentStartDate, paymentEndDate) + 1;

                        money = BigDecimal.valueOf(useDays).divide(BigDecimal.valueOf(totalDays), 10, BigDecimal.ROUND_HALF_DOWN)
                                .multiply(BigDecimal.valueOf(onceRent))
                                .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

                        Map<String, Integer> map = DateUtils.getDateDiffYear(DateUtils.formatDate(paymentStartDate, "yyyy-MM-dd"), DateUtils.formatDate(DateUtils.getNextDay(paymentEndDate, 1), "yyyy-MM-dd"));

                        if (map.get("month") > 0 && map.get("day") == 0) {
                            money = onceRent / (monthNum / map.get("month"));
                        } else if (map.get("month") == 0 && map.get("day") > 0) {
                            money = BigDecimal.valueOf(useDays).divide(BigDecimal.valueOf(totalDays), 10, BigDecimal.ROUND_HALF_DOWN)
                                    .multiply(BigDecimal.valueOf(monthRent))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                        } else if (map.get("month") > 0 && map.get("day") > 0) {
                            money = onceRent / (monthNum / map.get("month"));

                            //获取开始时间月份的最大天数
                            int maxMonthDate = DateUtils.getMonthMaxDay(DateUtils.getNextDay(paymentEndDate, -map.get("day")));
                            double dayMoney = BigDecimal.valueOf(map.get("day")).divide(BigDecimal.valueOf(maxMonthDate), 10, BigDecimal.ROUND_HALF_DOWN)
                                    .multiply(BigDecimal.valueOf(monthRent))
                                    .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                            money = money + dayMoney;
                        }

                        computeEndDate = contractEndDate;


                        //如果存在租金涨幅的情况,重新计算租金
                        if (leaseRecordRoom.getRentAddYear() > 0 && leaseRecordRoom.getRentAddRatio() > 0) {
                            boolean isException = true;
                            for (Record record : roomDateRentList) {
                                Date startDate = record.getDate("startDate");
                                Date endDate = record.getDate("endDate");
                                double rent = record.getDouble("rent");
                                if (DateUtils.compareDays(startDate, paymentStartDate) <= 0 && DateUtils.compareDays(endDate, paymentEndDate) >= 0) {
                                    int betweenMonthNum = FinaLeaseRecordApplyServiceImpl.betweenMonth(paymentStartDate, paymentEndDate);
                                /*if(betweenMonthNum==0){
                                    betweenMonthNum=1;
                                }*/
                                    //Map<String,Integer> map=DateUtils.getDateDiffYear(DateUtils.formatDate(paymentStartDate,"yyyy-MM-dd"), DateUtils.formatDate(DateUtils.getNextDay(paymentEndDate,1),"yyyy-MM-dd"));
                                    if (map.get("month") > 0 && map.get("day") == 0) {
                                        money = map.get("month") * rent;
                                    } else if (map.get("month") == 0 && map.get("day") > 0) {
                                        money = BigDecimal.valueOf(useDays).divide(BigDecimal.valueOf(totalDays), 10, BigDecimal.ROUND_HALF_DOWN)
                                                .multiply(BigDecimal.valueOf(rent))
                                                .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                    } else if (map.get("month") > 0 && map.get("day") > 0) {
                                        money = map.get("month") * rent;

                                        //获取开始时间月份的最大天数
                                        Date nextDate = DateUtils.getNextDay(paymentEndDate, -map.get("day"));
                                        int maxMonthDate = DateUtils.getMonthMaxDay(nextDate);
                                        double dayMoney = BigDecimal.valueOf(map.get("day")).divide(BigDecimal.valueOf(maxMonthDate), 10, BigDecimal.ROUND_HALF_DOWN)
                                                .multiply(BigDecimal.valueOf(rent))
                                                .setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                                        money = money + dayMoney;
                                    }

                                    //money=betweenMonthNum*rent;
                                    currRent = rent;
                                    isException = false;
                                    break;
                                }
                            }
                            if (isException) {
                                throw new RuntimeException("未匹配到房租时间区间");
                            }
                        }

                    } else {
                        paymentStartDate = computeStartDate;
                        paymentEndDate = computeEndDate;
                        money = onceRent;


                        //如果存在租金涨幅的情况,重新计算租金
                        if (leaseRecordRoom.getRentAddYear() > 0 && leaseRecordRoom.getRentAddRatio() > 0) {
                            boolean isException = true;
                            int i = 0;
                            for (Record record : roomDateRentList) {
                                Date startDate = record.getDate("startDate");
                                Date endDate = record.getDate("endDate");
                                double rent = record.getDouble("rent");
                                if (DateUtils.compareDays(startDate, paymentStartDate) <= 0 && DateUtils.compareDays(endDate, paymentEndDate) >= 0) {
                                    money = rent * monthNum;
                                    currRent = rent;
                                    isException = false;
                                    break;
                                } else if (DateUtils.compareDays(startDate, paymentStartDate) >= 0 && DateUtils.compareDays(endDate, paymentEndDate) >= 0) {

                                    int betweenMonthNum = FinaLeaseRecordApplyServiceImpl.betweenMonth(paymentStartDate, startDate);
                                    //上一个
                                    Record previousRecord = roomDateRentList.get((i - 1));
                                    money = (betweenMonthNum * previousRecord.getDouble("rent")) + ((monthNum - betweenMonthNum) * rent);
                                    isException = false;
                                    break;
                                }
                                i++;
                            }
                            if (isException) {
                                throw new RuntimeException("未匹配到房租时间区间");
                            }
                        }


                    }


                    Date needPayDate = null;
                    //期望付款日期
                    if ("1".equals(leaseRecordRoom.getRentPaymentDateType())) {
                        needPayDate = DateUtils.getNextDay(paymentStartDate, -leaseRecordRoom.getRentPaymentDateDay());
                    } else if ("2".equals(leaseRecordRoom.getRentPaymentDateType())) {
                        needPayDate = DateUtils.getNextDay(paymentStartDate, leaseRecordRoom.getRentPaymentDateDay());
                    }

                    FinaLeaseRecordRoomPayment leaseRecordRoomPayment = new FinaLeaseRecordRoomPayment();
                    leaseRecordRoomPayment.setId(IdGen.getUUID());
                    leaseRecordRoomPayment.setAmount(money);
                    leaseRecordRoomPayment.setStartDate(paymentStartDate);
                    leaseRecordRoomPayment.setNeedPayDate(needPayDate);
                    leaseRecordRoomPayment.setEndDate(paymentEndDate);
                    leaseRecordRoomPayment.setIssueNo(no);
                    leaseRecordRoomPayment.setRoomId(leaseRecordRoom.getRoomId());
                    leaseRecordRoomPayment.setLeaseId(leaseRecordRoom.getLeaseId());
                    leaseRecordRoomPayment.setRecordRoomId(leaseRecordRoom.getId());
                    leaseRecordRoomPayment.setStatus("1");
                    leaseRecordRoomPayment.setDelFlag("0");
                    leaseRecordRoomPayment.setSendStatus("0");
                    leaseRecordRoomPayment.setSendCount(0);
                    leaseRecordRoomPayment.setMonthRent(currRent);
                    leaseRecordRoomPayment.setCreateBy(leaseRecordRoom.getCreateBy());
                    leaseRecordRoomPayment.setCreateDate(new Date());
                    leaseRecordRoomPayment.setUpdateBy(leaseRecordRoom.getCreateBy());
                    leaseRecordRoomPayment.setUpdateDate(new Date());
                    if (no < 10) {
                        leaseRecordRoomPayment.setOrderNo(orderNo + "00" + no);
                    } else if (no < 100) {
                        leaseRecordRoomPayment.setOrderNo(orderNo + "0" + no);
                    } else {
                        leaseRecordRoomPayment.setOrderNo(orderNo + no);
                    }
                    leaseRecordRoomPaymentList.add(leaseRecordRoomPayment);

                    no++;
                    computeStartDate = DateUtils.getNextDay(computeEndDate, 1);
                } while (DateUtils.compareDays(contractEndDate, computeStartDate) >= 0);
            }

        }
        renderJson(new DataTable<>(leaseRecordRoomPaymentList));
    }

    public void waitPayIndex(){

        setAttr("baseList",Db.find("select * from main_base where id in (select base_id from fina_lease_record_apply where del_flag='0' " +
                "GROUP BY base_id)"));
        setAttr("dateRange",DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay());
        setAttr("userId",AuthUtils.getUserId());
        render("roomPaymentWaitPayIndex.html");
    }

    public void getWaitPayList(){
        String baseId=getPara("baseId");
        String name = getPara("name");
        String roomName = getPara("roomName");
        String dateRange=getPara("dateRange");

        if(StrKit.isBlank(dateRange)){
            dateRange=DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay();
        }

        String sql="select c.id,a.id as record_room_id,e.base_name,d.room_name,f.`name`,g.payment_way,g.pay_account,g.bank_account_name,g.bank_source " +
                ",c.amount,c.start_date,c.end_date,c.`status`,issue_no,c.order_no,a.rent,a.rent_unit" +
                ",a.contract_start_date,a.contract_end_date,a.room_start_date,a.room_end_date,a.rent_add_year,a.rent_add_ratio,a.payment_type,a.earnest_money " +
                " from fina_lease_record_room a " +
                "inner join fina_lease_record_apply b on a.lease_id=b.id and b.`status`='3' " +
                "inner join fina_lease_record_room_payment c on a.id=c.record_room_id " +
                "left join main_base_room d on d.id=a.room_id left join main_base e on e.id=b.base_id " +
                "left join wms_suppliers f on f.id=b.supplier_id left join wms_suppliers_pay g on g.id=b.suppliers_pay_id ";


        sql+="where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' and c.`status` in ('1','2') ";

        sql+=" and c.id not in (" +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('1','2') and b.del_flag='0' " +
                "UNION " +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('3') and b.del_flag='0' and b.`status`='2' " +
                ") ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and b.base_id=? ";
            params.add(baseId);
        }
        if(StrKit.notBlank(name)){
            sql+=" and f.`name` like concat('%',?,'%') ";
            params.add(name);
        }
        if(StrKit.notBlank(roomName)){
            sql+=" and d.room_name like concat('%',?,'%') ";
            params.add(roomName);
        }
        if(StrKit.notBlank(dateRange)){
            String[] datas=dateRange.split(" - ");
            sql+=" and c.start_date BETWEEN ? and ? ";
            params.add(datas[0]+" 00:00:00");
            params.add(datas[1]+" 23:59:59");
        }

        sql+=" order by a.contract_start_date,c.issue_no ";
        List<Record> recordList=Db.find(sql,params.toArray());

        Set<String> stringSet=new HashSet<>();
        for (Record record : recordList) {
            stringSet.add(record.getStr("record_room_id"));
        }
        String str="";
        for (String s : stringSet) {
            str+="?,";
        }
        Map<String,Integer> maxIssueNoMap=new HashMap<>();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);

            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",stringSet.toArray());

            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
        }

        for (Record record : recordList) {
            record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
            String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
            record.set("paymentWayStr",paymentWayStr);

            List<Record> rentAddList=FinaLeaseRecordRoomServiceImpl.getDateRent(record.getDate("contract_start_date"),record.getDate("contract_end_date")
                    ,record.getInt("rent_add_year"),record.getInt("rent_add_ratio"),record.getDouble("rent"));
            record.set("rentAddList",rentAddList);

        }

        renderJson(new DataTable<Record>(recordList));
    }

    public void getWaitPayListExport(){
        String baseId=getPara("baseId");
        String name = getPara("name");
        String roomName = getPara("roomName");
        String dateRange=getPara("dateRange");

        if(StrKit.isBlank(dateRange)){
            dateRange=DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay();
        }

        String sql="select c.id,a.id as record_room_id,e.base_name,d.room_name,f.`name`,g.payment_way,g.pay_account,g.bank_account_name,g.bank_source " +
                ",c.amount,c.start_date,c.end_date,c.`status`,issue_no,c.order_no,a.rent,a.rent_unit" +
                ",a.contract_start_date,a.contract_end_date,a.room_start_date,a.room_end_date,a.rent_add_year,a.rent_add_ratio,a.payment_type,a.earnest_money " +
                " from fina_lease_record_room a " +
                "inner join fina_lease_record_apply b on a.lease_id=b.id and b.`status`='3' " +
                "inner join fina_lease_record_room_payment c on a.id=c.record_room_id " +
                "left join main_base_room d on d.id=a.room_id left join main_base e on e.id=b.base_id " +
                "left join wms_suppliers f on f.id=b.supplier_id left join wms_suppliers_pay g on g.id=b.suppliers_pay_id ";


        sql+="where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' and c.`status` in ('1','2') ";

        sql+=" and c.id not in (" +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('1','2') and b.del_flag='0' " +
                "UNION " +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('3') and b.del_flag='0' and b.`status`='2' " +
                ") ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and b.base_id=? ";
            params.add(baseId);
        }
        if(StrKit.notBlank(name)){
            sql+=" and f.`name` like concat('%',?,'%') ";
            params.add(name);
        }
        if(StrKit.notBlank(roomName)){
            sql+=" and d.room_name like concat('%',?,'%') ";
            params.add(roomName);
        }
        String[] datas=dateRange.split(" - ");
        if(StrKit.notBlank(dateRange)){
            sql+=" and c.start_date BETWEEN ? and ? ";
            params.add(datas[0]+" 00:00:00");
            params.add(datas[1]+" 23:59:59");
        }

        sql+=" order by a.contract_start_date,c.issue_no ";
        List<Record> recordList=Db.find(sql,params.toArray());

        Set<String> stringSet=new HashSet<>();
        for (Record record : recordList) {
            stringSet.add(record.getStr("record_room_id"));
        }
        String str="";
        for (String s : stringSet) {
            str+="?,";
        }
        Map<String,Integer> maxIssueNoMap=new HashMap<>();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);

            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",stringSet.toArray());

            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
        }

        for (Record record : recordList) {
            record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
            String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
            record.set("paymentWayStr",paymentWayStr);

            List<Record> rentAddList=FinaLeaseRecordRoomServiceImpl.getDateRent(record.getDate("contract_start_date"),record.getDate("contract_end_date")
                    ,record.getInt("rent_add_year"),record.getInt("rent_add_ratio"),record.getDouble("rent"));
            record.set("rentAddList",rentAddList);

        }


        String[] title={"基地","房间","业主姓名","租金","租金单位","支付方式","押金","合同时间","房租付款期限","租金涨幅","本期租金","本期开始结束时间","当前期数/总期数","业主收款方式","业主收款账号","开户名","开户行"};
        String fileName="返租待付款单"+datas[0]+"至"+datas[1]+".xls";
        String sheetName = "返租待付款单";
        String[][] content=new String[recordList.size()][title.length];
        if (recordList!=null && recordList.size()>0) {
            for (int i = 0; i < recordList.size(); i++) {
                Record record = recordList.get(i);
                content[i][0]=record.getStr("base_name");
                content[i][1]=record.getStr("room_name");
                content[i][2]= record.getStr("name");
                content[i][3]=record.getStr("rent");
                if("month".equals(record.getStr("rent_unit"))){
                    content[i][4]="/月";
                }else if("year".equals(record.getStr("rent_unit"))){
                    content[i][4]="/年";
                }
                String unit="";
                if("year".equals(record.getStr("payment_type"))) {
                    unit = "年付";
                }else if("halfYear".equals(record.getStr("payment_type"))){
                    unit="半年付";
                }else if("season".equals(record.getStr("payment_type"))){
                    unit="季付";
                }else if("month".equals(record.getStr("payment_type"))){
                    unit="月付";
                }else if("disposable".equals(record.getStr("payment_type"))){
                    unit="一次性付";
                }
                content[i][5]=unit;
                content[i][6]=record.getStr("earnest_money");

                content[i][7]=DateUtils.formatDate(record.getDate("room_start_date"),"yyyy-MM-dd")+"至"
                        +DateUtils.formatDate(record.getDate("room_end_date"),"yyyy-MM-dd");
                content[i][8]=DateUtils.formatDate(record.getDate("contract_start_date"),"yyyy-MM-dd")+"至"
                        +DateUtils.formatDate(record.getDate("contract_end_date"),"yyyy-MM-dd");
                if(record.getInt("rent_add_year")!=null && record.getInt("rent_add_ratio")!=null &&
                        record.getInt("rent_add_year")>0 && record.getInt("rent_add_ratio")>0){
                    content[i][9]="每"+record.getInt("rent_add_year")+"年递增"+record.getInt("rent_add_ratio")+"%";
                }else{
                    content[i][9]="";
                }
                content[i][10]=record.getStr("amount");
                content[i][11]=DateUtils.formatDate(record.getDate("start_date"),"yyyy-MM-dd")+"至"+DateUtils.formatDate(record.getDate("end_date"),"yyyy-MM-dd");
                content[i][12]=record.getStr("issue_no")+"/"+record.getStr("maxIssueNo");
                content[i][13]=record.getStr("paymentWayStr");
                content[i][14]=record.getStr("bank_account_name");
                content[i][15]=record.getStr("bank_source");
            }
        }


        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    public void roomPaymentIndex(){
        setAttr("baseList",Db.find("select * from main_base where id in (select base_id from fina_lease_record_apply where del_flag='0' " +
                "GROUP BY base_id)"));
        setAttr("dateRange",DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay());
        render("roomPaymentIndex.html");
    }

    public void findRoomPaymentPageList(){
        String baseId=getPara("baseId");
        String name=getPara("name");
        String roomName=getPara("roomName");
        String dateRange=getPara("dateRange");
        String payStatus = getPara("payStatus");

        if(StrKit.isBlank(dateRange)){
            dateRange=DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay();
        }

        String sql="select c.id,a.id as record_room_id,e.base_name,d.room_name,f.`name`,g.payment_way,g.pay_account,g.bank_account_name,g.bank_source " +
                ",c.amount,c.start_date,c.end_date,c.`status`,issue_no,c.order_no,a.rent,a.rent_unit" +
                ",a.contract_start_date,a.contract_end_date,a.room_start_date,a.room_end_date,a.rent_add_year,a.rent_add_ratio,a.payment_type,a.earnest_money " +
                " from fina_lease_record_room a " +
                "inner join fina_lease_record_apply b on a.lease_id=b.id and b.`status`='3' " +
                "inner join fina_lease_record_room_payment c on a.id=c.record_room_id " +
                "left join main_base_room d on d.id=a.room_id left join main_base e on e.id=b.base_id " +
                "left join wms_suppliers f on f.id=b.supplier_id left join wms_suppliers_pay g on g.id=b.suppliers_pay_id ";


        sql+="where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' ";

        /*sql+=" and c.id not in (" +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('1','2') and b.del_flag='0' " +
                "UNION " +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('3') and b.del_flag='0' and b.`status`='2' " +
                ") ";*/

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and b.base_id=? ";
            params.add(baseId);
        }

        if(StrKit.notBlank(payStatus)){
            if("1".equals(payStatus)){
                sql+=" and c.`status` in ('1','2') ";
            }else if("2".equals(payStatus)){
                sql+=" and c.`status`='3' ";
            }

        }

        if(StrKit.notBlank(name)){
            sql+=" and f.`name` like concat('%',?,'%') ";
            params.add(name);
        }
        if(StrKit.notBlank(roomName)){
            sql+=" and d.room_name like concat('%',?,'%') ";
            params.add(roomName);
        }
        if(StrKit.notBlank(dateRange)){
            String[] datas=dateRange.split(" - ");
            sql+=" and c.start_date BETWEEN ? and ? ";
            params.add(datas[0]+" 00:00:00");
            params.add(datas[1]+" 23:59:59");
        }

        sql+=" order by a.contract_start_date,c.issue_no ";
        List<Record> recordList=Db.find(sql,params.toArray());

        Set<String> stringSet=new HashSet<>();
        for (Record record : recordList) {
            stringSet.add(record.getStr("record_room_id"));
        }
        String str="";
        for (String s : stringSet) {
            str+="?,";
        }
        Map<String,Integer> maxIssueNoMap=new HashMap<>();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);

            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",stringSet.toArray());

            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
        }

        for (Record record : recordList) {
            record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
            String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
            record.set("paymentWayStr",paymentWayStr);

            List<Record> rentAddList=FinaLeaseRecordRoomServiceImpl.getDateRent(record.getDate("contract_start_date"),record.getDate("contract_end_date")
                    ,record.getInt("rent_add_year"),record.getInt("rent_add_ratio"),record.getDouble("rent"));
            record.set("rentAddList",rentAddList);

        }

        renderJson(new DataTable<Record>(recordList));
    }


    public void findRoomPaymentPageListExport(){
        String baseId=getPara("baseId");
        String name=getPara("name");
        String roomName=getPara("roomName");
        String dateRange=getPara("dateRange");
        String payStatus = getPara("payStatus");

        if(StrKit.isBlank(dateRange)){
            dateRange=DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay();
        }

        String sql="select c.id,a.id as record_room_id,e.base_name,d.room_name,f.`name`,g.payment_way,g.pay_account,g.bank_account_name,g.bank_source " +
                ",c.amount,c.start_date,c.end_date,c.`status`,issue_no,c.order_no,a.rent,a.rent_unit" +
                ",a.contract_start_date,a.contract_end_date,a.room_start_date,a.room_end_date,a.rent_add_year,a.rent_add_ratio,a.payment_type,a.earnest_money " +
                " from fina_lease_record_room a " +
                "inner join fina_lease_record_apply b on a.lease_id=b.id and b.`status`='3' " +
                "inner join fina_lease_record_room_payment c on a.id=c.record_room_id " +
                "left join main_base_room d on d.id=a.room_id left join main_base e on e.id=b.base_id " +
                "left join wms_suppliers f on f.id=b.supplier_id left join wms_suppliers_pay g on g.id=b.suppliers_pay_id ";


        sql+="where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and b.base_id=? ";
            params.add(baseId);
        }

        if(StrKit.notBlank(payStatus)){
            if("1".equals(payStatus)){
                sql+=" and c.`status` in ('1','2') ";
            }else if("2".equals(payStatus)){
                sql+=" and c.`status`='3' ";
            }

        }

        if(StrKit.notBlank(name)){
            sql+=" and f.`name` like concat('%',?,'%') ";
            params.add(name);
        }
        if(StrKit.notBlank(roomName)){
            sql+=" and d.room_name like concat('%',?,'%') ";
            params.add(roomName);
        }
        String[] datas=dateRange.split(" - ");
        if(StrKit.notBlank(dateRange)){
            sql+=" and c.start_date BETWEEN ? and ? ";
            params.add(datas[0]+" 00:00:00");
            params.add(datas[1]+" 23:59:59");
        }

        sql+=" order by a.contract_start_date,c.issue_no ";
        List<Record> recordList=Db.find(sql,params.toArray());

        Set<String> stringSet=new HashSet<>();
        for (Record record : recordList) {
            stringSet.add(record.getStr("record_room_id"));
        }
        String str="";
        for (String s : stringSet) {
            str+="?,";
        }
        Map<String,Integer> maxIssueNoMap=new HashMap<>();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);

            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",stringSet.toArray());

            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
        }

        for (Record record : recordList) {
            record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
            String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
            record.set("paymentWayStr",paymentWayStr);

            List<Record> rentAddList=FinaLeaseRecordRoomServiceImpl.getDateRent(record.getDate("contract_start_date"),record.getDate("contract_end_date")
                    ,record.getInt("rent_add_year"),record.getInt("rent_add_ratio"),record.getDouble("rent"));
            record.set("rentAddList",rentAddList);
        }

        String[] title={"基地","房间","业主姓名","租金","租金单位","支付方式","押金","合同时间","房租付款期限","租金涨幅","本期租金","本期开始结束时间","当前期数/总期数","业主收款方式","业主收款账号","开户名","开户行","支付状态"};
        String fileName="返租付款单"+datas[0]+"至"+datas[1]+".xls";
        String sheetName = "返租付款单";
        String[][] content=new String[recordList.size()][title.length];
        if (recordList!=null && recordList.size()>0) {
            for (int i = 0; i < recordList.size(); i++) {
                Record record = recordList.get(i);
                content[i][0]=record.getStr("base_name");
                content[i][1]=record.getStr("room_name");
                content[i][2]= record.getStr("name");
                content[i][3]=record.getStr("rent");
                if("month".equals(record.getStr("rent_unit"))){
                    content[i][4]="/月";
                }else if("year".equals(record.getStr("rent_unit"))){
                    content[i][4]="/年";
                }
                String unit="";
                if("year".equals(record.getStr("payment_type"))) {
                    unit = "年付";
                }else if("halfYear".equals(record.getStr("payment_type"))){
                    unit="半年付";
                }else if("season".equals(record.getStr("payment_type"))){
                    unit="季付";
                }else if("month".equals(record.getStr("payment_type"))){
                    unit="月付";
                }else if("disposable".equals(record.getStr("payment_type"))){
                    unit="一次性付";
                }
                content[i][5]=unit;
                content[i][6]=record.getStr("earnest_money");

                content[i][7]=DateUtils.formatDate(record.getDate("room_start_date"),"yyyy-MM-dd")+"至"
                        +DateUtils.formatDate(record.getDate("room_end_date"),"yyyy-MM-dd");
                content[i][8]=DateUtils.formatDate(record.getDate("contract_start_date"),"yyyy-MM-dd")+"至"
                        +DateUtils.formatDate(record.getDate("contract_end_date"),"yyyy-MM-dd");
                if(record.getInt("rent_add_year")!=null && record.getInt("rent_add_ratio")!=null &&
                        record.getInt("rent_add_year")>0 && record.getInt("rent_add_ratio")>0){
                    content[i][9]="每"+record.getInt("rent_add_year")+"年递增"+record.getInt("rent_add_ratio")+"%";
                }else{
                    content[i][9]="";
                }
                content[i][10]=record.getStr("amount");
                content[i][11]=DateUtils.formatDate(record.getDate("start_date"),"yyyy-MM-dd")+"至"+DateUtils.formatDate(record.getDate("end_date"),"yyyy-MM-dd");
                content[i][12]=record.getStr("issue_no")+"/"+record.getStr("maxIssueNo");
                content[i][13]=record.getStr("paymentWayStr");
                content[i][14]=record.getStr("pay_account");
                content[i][15]=record.getStr("bank_account_name");
                content[i][16]=record.getStr("bank_source");
                if("3".equals(record.getStr("status"))){
                    content[i][17]="已付款";
                }else{
                    content[i][17]="未付款";
                }


            }
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    public void getRoomPaymentList(){
        String baseId=getPara("baseId");
        String name = getPara("name");
        String roomName = getPara("roomName");
        String dateRange=getPara("dateRange");

        if(StrKit.isBlank(dateRange)){
            dateRange=DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay();
        }

        String sql="select c.id,a.id as record_room_id,e.base_name,d.room_name,f.`name`,g.payment_way,g.pay_account,g.bank_account_name,g.bank_source " +
                ",c.amount,c.start_date,c.end_date,c.`status`,issue_no,c.order_no,a.rent,a.rent_unit" +
                ",a.contract_start_date,a.contract_end_date,a.room_start_date,a.room_end_date,a.rent_add_year,a.rent_add_ratio,a.payment_type,a.earnest_money " +
                " from fina_lease_record_room a " +
                "inner join fina_lease_record_apply b on a.lease_id=b.id and b.`status`='3' " +
                "inner join fina_lease_record_room_payment c on a.id=c.record_room_id " +
                "left join main_base_room d on d.id=a.room_id left join main_base e on e.id=b.base_id " +
                "left join wms_suppliers f on f.id=b.supplier_id left join wms_suppliers_pay g on g.id=b.suppliers_pay_id ";


        sql+="where a.del_flag='0' and b.del_flag='0' and c.del_flag='0' and c.`status` in ('1','2') ";

        sql+=" and c.id not in (" +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('1','2') and b.del_flag='0' " +
                "UNION " +
                "select b.payment_id from fina_lease_record_room_payment_apply a " +
                "inner join fina_lease_record_room_payment_apply_rel b on a.id=b.payment_apply_id " +
                "where a.del_flag='0' and a.`status` in ('3') and b.del_flag='0' and b.`status`='2' " +
                ") ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and b.base_id=? ";
            params.add(baseId);
        }
        if(StrKit.notBlank(name)){
            sql+=" and f.`name` like concat('%',?,'%') ";
            params.add(name);
        }
        if(StrKit.notBlank(roomName)){
            sql+=" and d.room_name like concat('%',?,'%') ";
            params.add(roomName);
        }
        if(StrKit.notBlank(dateRange)){
            String[] datas=dateRange.split(" - ");
            sql+=" and c.start_date BETWEEN ? and ? ";
            params.add(datas[0]+" 00:00:00");
            params.add(datas[1]+" 23:59:59");
        }

        sql+=" order by a.contract_start_date,c.issue_no ";
        List<Record> recordList=Db.find(sql,params.toArray());

        Set<String> stringSet=new HashSet<>();
        for (Record record : recordList) {
            stringSet.add(record.getStr("record_room_id"));
        }
        String str="";
        for (String s : stringSet) {
            str+="?,";
        }
        Map<String,Integer> maxIssueNoMap=new HashMap<>();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);

            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",stringSet.toArray());

            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
        }

        for (Record record : recordList) {
            record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
            String paymentWayStr=PayWay.me().desc(record.getStr("payment_way"));
            record.set("paymentWayStr",paymentWayStr);

            List<Record> rentAddList=FinaLeaseRecordRoomServiceImpl.getDateRent(record.getDate("contract_start_date"),record.getDate("contract_end_date")
                    ,record.getInt("rent_add_year"),record.getInt("rent_add_ratio"),record.getDouble("rent"));
            record.set("rentAddList",rentAddList);

        }

        renderJson(new DataTable<Record>(recordList));
    }

    public void saveRoomPaymentApply(){
        String title = getPara("title");
        String remark = getPara("remark");
        String dataIds = getPara("dataIds");
        FinaLeaseRecordRoomPaymentApply recordRoomPaymentApply=getBean(FinaLeaseRecordRoomPaymentApply.class,"",true);

        JSONArray idArray=JSON.parseArray(dataIds);
        if(idArray==null || idArray.size()==0){
            renderJson(Ret.fail("msg","提交数据条数不能为0"));
            return;
        }
        recordRoomPaymentApply.setId(IdGen.getUUID());
        recordRoomPaymentApply.setSubmitUserId(AuthUtils.getUserId());
        recordRoomPaymentApply.setStatus("1");
        recordRoomPaymentApply.setDelFlag("0");
        recordRoomPaymentApply.setApplyTime(new Date());
        recordRoomPaymentApply.setCreateBy(AuthUtils.getUserId());
        recordRoomPaymentApply.setCreateDate(new Date());
        recordRoomPaymentApply.setUpdateBy(AuthUtils.getUserId());
        recordRoomPaymentApply.setUpdateDate(new Date());
        FinaLeaseRecordApply leaseRecordApply = finaLeaseRecordApplyService.findById(finaLeaseRecordRoomPaymentService.findById(idArray.getString(0)).getLeaseId());
        recordRoomPaymentApply.setBaseId(leaseRecordApply.getBaseId());

        List<FinaLeaseRecordRoomPaymentApplyRel> roomPaymentApplyRelList=new ArrayList<>();
        for (int i = 0; i < idArray.size(); i++) {
            FinaLeaseRecordRoomPaymentApplyRel paymentApplyRel=new FinaLeaseRecordRoomPaymentApplyRel();
            paymentApplyRel.setId(IdGen.getUUID());
            paymentApplyRel.setCreateBy(AuthUtils.getUserId());
            paymentApplyRel.setPaymentApplyId(recordRoomPaymentApply.getId());
            paymentApplyRel.setPaymentId(idArray.getString(i));
            paymentApplyRel.setStatus("1");
            paymentApplyRel.setDelFlag("0");
            paymentApplyRel.setCreateDate(new Date());
            paymentApplyRel.setUpdateBy(AuthUtils.getUserId());
            paymentApplyRel.setUpdateDate(new Date());
            roomPaymentApplyRelList.add(paymentApplyRel);
        }
        boolean flag=recordRoomPaymentApply.save();
        if(flag){
            Db.batchSave(roomPaymentApplyRelList,roomPaymentApplyRelList.size());
        }
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void roomPaymentApplyCheckIndex(){
        setAttr("baseList",Db.find("select * from main_base where id in (select base_id from fina_lease_record_apply where del_flag='0' " +
                "GROUP BY base_id)"));
        setAttr("userId",AuthUtils.getUserId());
        render("roomPaymentApplyCheckIndex.html");
    }

    public void roomPaymentApplyPageList(){
        String baseId = getPara("baseId");
        String status = getPara("status");
        String name=getPara("name");
        String select="select a.id,a.title,b.base_name,a.remark," +
                "(select COUNT(id) from fina_lease_record_room_payment_apply_rel where payment_apply_id=a.id and del_flag='0' ) as num " +
                ",c.`name` as submitName,d.`name` as checkName,e.`name` as approveName,a.`status`,a.apply_time," +
                " case a.status when '1' then g.`name` when '2' then h.`name` else '' end currName,(select SUM(rp.pay_amount) from fina_lease_record_room_payment_apply_rel ar " +
                "inner join fina_lease_record_room_payment rp on ar.payment_id=rp.id where ar.payment_apply_id=a.id and ar.del_flag='0' ) as totalPayAmount" +
                ",(select SUM(rp.amount) from fina_lease_record_room_payment_apply_rel ar " +
                "inner join fina_lease_record_room_payment rp on ar.payment_id=rp.id where ar.payment_apply_id=a.id and ar.del_flag='0' ) as totalAmount," +
                "c.`name` as submitName,d.`name` as checkName,e.`name` as approveName,a.`status`,a.apply_time ";
        String sql=" from fina_lease_record_room_payment_apply a left join main_base b on a.base_id=b.id " +
                "left join sys_user c on c.id=a.submit_user_id " +
                "left join sys_user d on d.id=a.check_user_id " +
                "left join sys_user e on e.id=a.approve_user_id  " +
                "left join sys_user f on f.id=b.lease_submit_user_id " +
                "left join sys_user g on g.id=b.lease_check_user_id " +
                "left join sys_user h on h.id=b.lease_approve_user_id " +
                " where a.del_flag='0' ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(baseId)){
            sql+=" and a.base_id=? ";
            params.add(baseId);
        }
        if(StrKit.notBlank(status)){
            JSONArray jsonArray=JSON.parseArray(status);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }

                if(str.length()>0){
                    str=str.substring(0,str.length()-1);
                }
                sql+=" and a.`status` in ("+str+") ";
            }
        }
        if(StrKit.notBlank(name)){
            sql+=" and exists (select a2.id from " +
                    "fina_lease_record_room_payment_apply_rel a2  " +
                    "inner join fina_lease_record_room_payment b2 on a2.payment_id=b2.id and a2.del_flag='0' " +
                    "inner join fina_lease_record_room c2 on c2.id=b2.record_room_id " +
                    "inner join fina_lease_record_apply d2 on d2.id=c2.lease_id " +
                    "inner join wms_suppliers e2 on e2.id=d2.supplier_id " +
                    "where a2.payment_apply_id=a.id and e2.`name` like concat('%',?,'%') ) ";
            params.add(name);
        }
        sql+=" order by a.apply_time desc ";

        Page<Record> recordPage=Db.paginate(getParaToInt("page"),getParaToInt("limit"),select,sql,params.toArray());
        renderJson(new DataTable<Record>(recordPage));
    }

    public void roomPaymentApplyCheckDetailIndex(){
        String id = getPara("id");
        setAttr("id",id);
        setAttr("payWay",PayWay.me().all());
        setAttr("type",getPara("type"));
        FinaLeaseRecordRoomPaymentApply paymentApply = finaLeaseRecordRoomPaymentApplyService.findById(id);
        setAttr("paymentApply",paymentApply);
        setAttr("base",mainBaseService.findById(paymentApply.getBaseId()));
        if(StrKit.notBlank(paymentApply.getSubmitUserId())){
            setAttr("submitUser",userService.findById(paymentApply.getSubmitUserId()));
        }
        if(StrKit.notBlank(paymentApply.getCheckUserId())){
            setAttr("checkUser",userService.findById(paymentApply.getCheckUserId()));
        }
        if(StrKit.notBlank(paymentApply.getApproveUserId())){
            setAttr("approveUser",userService.findById(paymentApply.getApproveUserId()));
        }
        render("roomPaymentApplyCheckDetailIndex.html");
    }

    public void roomPaymentApplyCheckDetaiList(){
        String id=getPara("id");

        List<Record> recordList=Db.find("select a.id,a.`status`,a.remark,b.id as payment_id,e.`name` as supplierName,b.amount,b.start_date,b.end_date" +
                ",g.room_name,b.issue_no,b.record_room_id,a.remark,b.pay_amount,b.pay_type,c.rent_unit" +
                ",c.rent,c.contract_start_date,c.contract_end_date,c.rent_add_year,c.rent_add_ratio,c.payment_type " +
                "from fina_lease_record_room_payment_apply_rel a " +
                "left join fina_lease_record_room_payment b on a.payment_id=b.id " +
                "left join fina_lease_record_room c on c.id=b.record_room_id " +
                "left join fina_lease_record_apply d on d.id=c.lease_id " +
                "left join wms_suppliers e on e.id=d.supplier_id " +
                "left join wms_suppliers_pay f on f.id=d.suppliers_pay_id " +
                "left join main_base_room g on g.id=c.room_id " +
                "where a.payment_apply_id=? " +
                "and a.del_flag='0' and b.del_flag='0' order by b.start_date ",id);

        if(recordList.size()>0){

            String str="";
            List<String> ids=new ArrayList<>();
            for (Record record : recordList) {
                str+="?,";
                ids.add(record.getStr("record_room_id"));
            }
            str=str.substring(0,str.length()-1);
            HashMap<String, Integer> maxIssueNoMap = new HashMap<>();
            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",ids.toArray());
            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
            double totalAmount=0.0;
            double totalPayAmount=0.0;
            for (Record record : recordList) {
                record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
                if(StrKit.notBlank(record.getStr("pay_type"))){
                    String payTypeStr = PayWay.me().desc(record.getStr("pay_type"));
                    record.set("payTypeStr",payTypeStr);
                }
                Double amount = record.getDouble("amount");
                if(amount!=null){
                    totalAmount+=amount;
                }
                Double payAmount = record.getDouble("pay_amount");
                if(payAmount!=null){
                    totalPayAmount+=payAmount;
                }
            }
            Record record=new Record();
            record.set("supplierName","本期合计");
            record.set("pay_amount",totalPayAmount);
            record.set("amount",totalAmount);
            recordList.add(record);
        }


        renderJson(new DataTable<Record>(recordList));
    }

    public void roomPaymentApplyCheckDetaiListExport(){
        String id=getPara("id");
        FinaLeaseRecordRoomPaymentApply paymentApply=finaLeaseRecordRoomPaymentApplyService.findById(id);
        List<Record> recordList=Db.find("select a.id,a.`status`,a.remark,b.id as payment_id,e.`name` as supplierName,b.amount,b.start_date,b.end_date" +
                ",g.room_name,b.issue_no,b.record_room_id,a.remark,b.pay_amount,b.pay_type,c.rent_unit" +
                ",c.rent,c.contract_start_date,c.contract_end_date,c.rent_add_year,c.rent_add_ratio,c.payment_type " +
                "from fina_lease_record_room_payment_apply_rel a " +
                "left join fina_lease_record_room_payment b on a.payment_id=b.id " +
                "left join fina_lease_record_room c on c.id=b.record_room_id " +
                "left join fina_lease_record_apply d on d.id=c.lease_id " +
                "left join wms_suppliers e on e.id=d.supplier_id " +
                "left join wms_suppliers_pay f on f.id=d.suppliers_pay_id " +
                "left join main_base_room g on g.id=c.room_id " +
                "where a.payment_apply_id=? " +
                "and a.del_flag='0' and b.del_flag='0' order by b.start_date ",id);
        String[] title={"业主","房间号","本期开始时间","本期结束时间","当前期数","总期数","本期租金","支付状态","实际支付租金","实际支付方式","备注"};
        String fileName=paymentApply.getTitle()+"基地返租付款单明细.xls";
        String sheetName = "付款单明细";
        String[][] content=new String[recordList.size()][title.length];
        if(recordList.size()>0){

            String str="";
            List<String> ids=new ArrayList<>();
            for (Record record : recordList) {
                str+="?,";
                ids.add(record.getStr("record_room_id"));
            }
            str=str.substring(0,str.length()-1);
            HashMap<String, Integer> maxIssueNoMap = new HashMap<>();
            List<Record> maxIssueNoList=Db.find("select record_room_id,max(issue_no) as maxIssueNo from fina_lease_record_room_payment where record_room_id in ("+str+") and del_flag='0' GROUP BY record_room_id ",ids.toArray());
            for (Record record : maxIssueNoList) {
                maxIssueNoMap.put(record.getStr("record_room_id"),record.getInt("maxIssueNo"));
            }
            double totalAmount=0.0;
            double totalPayAmount=0.0;
            for (Record record : recordList) {
                record.set("maxIssueNo",maxIssueNoMap.get(record.getStr("record_room_id")));
                if(StrKit.notBlank(record.getStr("pay_type"))){
                    String payTypeStr = PayWay.me().desc(record.getStr("pay_type"));
                    record.set("payTypeStr",payTypeStr);
                }
                Double amount = record.getDouble("amount");
                if(amount!=null){
                    totalAmount+=amount;
                }
                Double payAmount = record.getDouble("pay_amount");
                if(payAmount!=null){
                    totalPayAmount+=payAmount;
                }
            }
            Record record=new Record();
            record.set("supplierName","本期合计");
            record.set("pay_amount",totalPayAmount);
            record.set("amount",totalAmount);
            //recordList.add(record);
        }

        for (int i = 0; i < recordList.size(); i++) {
            content[i][0]=recordList.get(i).getStr("supplierName");
            content[i][1]=recordList.get(i).getStr("room_name");
            content[i][2]=DateUtils.formatDate(recordList.get(i).getDate("start_date"),"yyyy-MM-dd");
            content[i][3]=DateUtils.formatDate(recordList.get(i).getDate("end_date"),"yyyy-MM-dd");
            content[i][4]=recordList.get(i).getStr("issue_no");
            content[i][5]=recordList.get(i).getStr("maxIssueNo");
            content[i][6]=recordList.get(i).getStr("amount");
            content[i][8]="";
            content[i][9]="";
            if("1".equals(recordList.get(i).getStr("status"))){
                content[i][7]="已付款";
                content[i][8]=recordList.get(i).getStr("pay_amount");
                String paymentWayStr=PayWay.me().desc(recordList.get(i).getStr("pay_type"));
                content[i][9]=paymentWayStr;
            }else{
                content[i][7]="未付款";
            }
            content[i][10]=recordList.get(i).getStr("remark");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void saveRoomPaymentApplyStatus(){
        String id = getPara("id");
        String status=getPara("status");
        FinaLeaseRecordRoomPaymentApply recordRoomPaymentApply=finaLeaseRecordRoomPaymentApplyService.findById(id);
        MainBase base = mainBaseService.findById(recordRoomPaymentApply.getBaseId());
        if("2".equals(status)){
            if(!AuthUtils.getUserId().equalsIgnoreCase(base.getLeaseCheckUserId())){
                renderJson(Ret.fail("msg","操作失败，操作人不是你"));
                return;
            }
            recordRoomPaymentApply.setStatus("2");
            recordRoomPaymentApply.setCheckUserId(AuthUtils.getUserId());
            recordRoomPaymentApply.setUpdateDate(new Date());
            recordRoomPaymentApply.setUpdateBy(AuthUtils.getUserId());
            recordRoomPaymentApply.setcheckTime(new Date());
            boolean flag=recordRoomPaymentApply.update();
            if(flag){
                renderJson(Ret.ok("msg","操作成功"));
                return;
            }
        }else if("3".equals(status)){
            if(!AuthUtils.getUserId().equalsIgnoreCase(base.getLeaseApproveUserId())){
                renderJson(Ret.fail("msg","操作失败，操作人不是你"));
                return;
            }
            recordRoomPaymentApply.setStatus("3");
            recordRoomPaymentApply.setCheckUserId(AuthUtils.getUserId());
            recordRoomPaymentApply.setUpdateDate(new Date());
            recordRoomPaymentApply.setUpdateBy(AuthUtils.getUserId());
            recordRoomPaymentApply.setcheckTime(new Date());
            boolean flag=recordRoomPaymentApply.update();
            if(flag){

                List<String> paymentIdList = Db.query("select payment_id from fina_lease_record_room_payment_apply_rel where `status`='1' and  payment_apply_id=? ",id);
                if(paymentIdList.size()>0){
                    String str="";
                    for (String s : paymentIdList) {
                        str+="?,";
                    }
                    str=str.substring(0,str.length()-1);
                    Db.update("update fina_lease_record_room_payment set `status`='3',update_date=now() where id in ("+str+") ",paymentIdList.toArray());
                }

                renderJson(Ret.ok("msg","操作成功"));
                return;
            }

        }else if("4".equals(status)){
            if(!AuthUtils.getUserId().equalsIgnoreCase(base.getLeaseSubmitUserId())){
                renderJson(Ret.fail("msg","操作失败，操作人不是你"));
                return;
            }
            recordRoomPaymentApply.setStatus("4");
            recordRoomPaymentApply.setCheckUserId(AuthUtils.getUserId());
            recordRoomPaymentApply.setUpdateDate(new Date());
            recordRoomPaymentApply.setUpdateBy(AuthUtils.getUserId());
            recordRoomPaymentApply.setcheckTime(new Date());
            boolean flag=recordRoomPaymentApply.update();
            if(flag){
                renderJson(Ret.ok("msg","操作成功"));
                return;
            }
        }else{
            renderJson(Ret.fail("msg","操作失败，未知审核状态码"));
            return;
        }
        renderJson(Ret.ok("msg","操作成功"));
    }

    public void checkRoomPaymentApplyStatus(){
        String id = getPara("id");

        List<Record> recordList=Db.find("select a.*,b.pay_amount,pay_type from fina_lease_record_room_payment_apply_rel a inner join fina_lease_record_room_payment b on a.payment_id=b.id and b.del_flag='0' " +
                " where a.del_flag='0' and payment_apply_id=? ",id);
        for (Record record : recordList) {
            if(StrKit.isBlank(record.getStr("status")) || record.getDouble("pay_amount")==null || StrKit.isBlank(record.getStr("pay_type"))){
                FinaLeaseRecordRoomPayment roomPayment = finaLeaseRecordRoomPaymentService.findById(record.getStr("payment_id"));
                MainBaseRoom baseRoom = mainBaseRoomService.findById(roomPayment.getRoomId());

                renderJson(Ret.fail("msg","请先保存详情中"+baseRoom.getRoomName()+"房间的租金实际支付内容"));
                return;
            }
        }
        renderJson(Ret.ok("msg","success"));
    }

    public void saveRoomPaymentApplyDetailStatus(){
        String dataStr=getPara("dataStr");
        JSONArray jsonArray=JSON.parseArray(dataStr);

        List<FinaLeaseRecordRoomPaymentApplyRel> relList=new ArrayList<>();
        List<FinaLeaseRecordRoomPayment> paymentList=new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            String payStatus = jsonObject.getString("payStatus");
            String payType = jsonObject.getString("payType");
            Double payAmount = jsonObject.getDouble("payAmount");
            String remark = jsonObject.getString("remark");

            FinaLeaseRecordRoomPaymentApplyRel applyRel = finaLeaseRecordRoomPaymentApplyRelService.findById(jsonObject.getString("id"));
            applyRel.setStatus(payStatus);
            applyRel.setRemark(remark);
            applyRel.setUpdateDate(new Date());
            //applyRel.update();

            Db.update("update fina_lease_record_room_payment_apply_rel set `status`=?,remark=?,update_date=?,update_by=? where id=? "
                    ,applyRel.getStatus(),applyRel.getRemark(),applyRel.getUpdateDate(),AuthUtils.getUserId(),applyRel.getId());

            FinaLeaseRecordRoomPayment roomPayment = finaLeaseRecordRoomPaymentService.findById(applyRel.getPaymentId());
            /*if("1".equals(payStatus)){
                roomPayment.setStatus("3");
            }*/
            roomPayment.setUpdateDate(new Date());
            roomPayment.setPayAmount(payAmount);
            roomPayment.setPayType(payType);
            //roomPayment.update();

            Db.update("update fina_lease_record_room_payment set `pay_amount`=?,pay_type=?,update_date=?,update_by=? where id=? "
                    ,roomPayment.getPayAmount(),roomPayment.getPayType(),roomPayment.getUpdateDate(),AuthUtils.getUserId(),roomPayment.getId());
        }
        try {
            //int[] nums=Db.batchUpdate(relList,relList.size());
            //int[] nums2=Db.batchUpdate(paymentList,paymentList.size());
            renderJson(Ret.ok("msg","操作成功"));
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delPaymentApplyRel(){
        String id = getPara("id");

        FinaLeaseRecordRoomPaymentApplyRel applyRel = finaLeaseRecordRoomPaymentApplyRelService.findById(id);
        applyRel.setDelFlag("1");
        applyRel.setUpdateBy(AuthUtils.getUserId());
        applyRel.setUpdateDate(new Date());
        boolean flag=applyRel.update();
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void baseLeaseRentIndex(){
        setAttr("dateRange",DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay());
        render("baseLeaseRentIndex.html");
    }

    public void baseLeaseRentList(){
        String dateRange=getPara("dateRange");
        if(StrKit.isBlank(dateRange)){
            dateRange=DateUtils.formatDate(new Date(),"yyyy-MM")+"-01 - "+DateUtils.formatDate(new Date(),"yyyy-MM-")+DateUtils.getMonthLashDay();
        }

        List<Record> baseList = Db.find("select * from main_base where id in (select base_id from fina_lease_record_apply where del_flag='0' " +
                "GROUP BY base_id)");

        String sql="select b.base_id,a.amount,a.`status` from fina_lease_record_room_payment a " +
                "INNER JOIN fina_lease_record_apply b on a.lease_id=b.id " +
                "where a.del_flag='0' and b.del_flag='0' and b.`status`='3' ";
        List<String> params=new ArrayList<>();
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            sql+=" and a.start_date BETWEEN ? and ? ";
            params.add(dates[0]+" 00:00:00");
            params.add(dates[1]+" 23:59:59");
        }
        Map<String,Record> baseMap=new HashMap<>();
        for (Record record : baseList) {
            record.set("totalAmount",0.0);
            record.set("totalCount",0);

            record.set("payAmount",0.0);
            record.set("payAmountCount",0);


            record.set("notPayAmount",0.0);
            record.set("notPayAmountCount",0);
            baseMap.put(record.getStr("id"),record);
        }

        List<Record> recordList=Db.find(sql,params.toArray());
        for (Record record : recordList) {
            String baseId = record.getStr("base_id");
            Record baseRecord = baseMap.get(baseId);
            if(baseRecord!=null){
                baseRecord.set("totalCount",baseRecord.getInt("totalCount")+1);
                baseRecord.set("totalAmount",baseRecord.getDouble("totalAmount")+record.getDouble("amount"));
                if("3".equals(record.getStr("status"))){
                    Double payAmount=baseRecord.getDouble("payAmount");
                    if(payAmount==null){
                        payAmount=0.0;
                    }
                    baseRecord.set("payAmount",payAmount+record.getDouble("amount"));
                    baseRecord.set("payAmountCount",baseRecord.getInt("payAmountCount")+1);
                }else{
                    Double notPayAmount=baseRecord.getDouble("notPayAmount");
                    if(notPayAmount==null){
                        notPayAmount=0.0;
                    }
                    baseRecord.set("notPayAmount",notPayAmount+record.getDouble("amount"));
                    baseRecord.set("notPayAmountCount",baseRecord.getInt("notPayAmountCount")+1);
                }
            }
        }

        renderJson(new DataTable<Record>(baseList));
    }

    public void baseLeaseRentDetailIndex(){
        String baseId = getPara("baseId");
        String dateRange = getPara("dateRange");

        setAttr("baseId",baseId);
        setAttr("dateRange",dateRange);
        render("baseLeaseRentDetailIndex.html");
    }

    public void delLeaseRecordApply(){
        String id = getPara("id");
        FinaLeaseRecordApply leaseRecordApply = finaLeaseRecordApplyService.findById(id);
        leaseRecordApply.setDelFlag("1");
        leaseRecordApply.setUpdateDate(new Date());
        leaseRecordApply.setUpdateBy(AuthUtils.getUserId());
        if(leaseRecordApply.update()){
            List<FinaLeaseRecordRoom> roomList = finaLeaseRecordRoomService.findListByLeaseId(leaseRecordApply.getId());
            for (FinaLeaseRecordRoom recordRoom : roomList) {
                recordRoom.setDelFlag("1");
                recordRoom.setUpdateDate(new Date());
                recordRoom.setUpdateBy(AuthUtils.getUserId());
                recordRoom.update();
            }
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }
}
