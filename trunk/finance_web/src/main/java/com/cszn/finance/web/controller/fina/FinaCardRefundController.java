/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardRefundDetailService;
import com.cszn.integrated.service.api.fina.FinaCardRefundService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.fina.FinaCardRefund;
import com.cszn.integrated.service.entity.fina.FinaCardRefundDetail;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.ReturnCardStatus;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by LiangHuiLing on 2024年12月6日
 *
 * FinaCardRefundController
 */
@RequestMapping(value="/fina/refund", viewPath="/modules_page/finance/fina/refund")
public class FinaCardRefundController extends BaseController {

    @Inject
    private FinaMembershipCardService finaMembershipCardService;
	@Inject
    private FinaCardRefundService finaCardRefundService;
    @Inject
    FinaCardRefundDetailService finaCardRefundDetailService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private UserService userService;

    public void refundIndex() {
        setAttr("curUserId",AuthUtils.getUserId());
    	render("refundIndex.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	FinaCardRefund model = getBean(FinaCardRefund.class, "", true);
        Page<FinaCardRefund> modelPage = finaCardRefundService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaCardRefund>(modelPage));
    }
    
    public void add(){
        FinaCardRefund model = new FinaCardRefund();
        final int dataCount = Db.queryInt("select count(id)dataCount from fina_card_refund where del_flag='0' and DATE_FORMAT(create_time,'%Y-%m-%d')=?", DateUtils.getDate());
        int recordNum = dataCount+1;
        final String applyNum = String.format("%"+5+"d", recordNum).replace(" ", "0");
        model.setApplyNo(DateUtils.getDate("yyyyMMdd")+applyNum);
        PersOrg persOrg = persOrgService.findByName("headquarters", "财务部");
        if(persOrg!=null){
            model.setDepartmentId(persOrg.getId());
            model.setDepartmentName(persOrg.getOrgName());
            final String linkMan = persOrg.getLinkMan();
            if(StrKit.notBlank(linkMan)){
                model.setExamineId(linkMan);
                User user = userService.findById(linkMan);
                if(user!=null){
                    model.setExamineName(user.getName());
                }
            }
        }
        model.setApplyId(AuthUtils.getUserId());
        model.setApplyName(AuthUtils.getLoginUser().getName());
        model.setApplyTime(new Date());
        //默认阿凤为支付人
        PersOrgEmployee emp = persOrgEmployeeService.getEmployeeByWorkNum("CS12271");
        if(emp!=null){
            User user = userService.getByUserName("CS12271");
            if(user!=null){
                model.setPayId(user.getId());
            }
            model.setPayName(emp.getFullName());
        }
        model.setRecordStatus("wait_submit");
        setAttr("model", model);
        setAttr("hrmDomain", Global.hrmUrl);
        render("refundForm.html");
    }

    public void edit(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
        	FinaCardRefund model = finaCardRefundService.findWithDetailById(id);
            setAttr("model", model);
        }
        setAttr("hrmDomain",Global.hrmUrl);
        render("refundForm.html");
    }

    public void saveRefund(){
        Ret returnRet = Ret.fail("msg","操作失败");
    	FinaCardRefund model = getBean(FinaCardRefund.class,"",true);
    	final int detailCount = getParaToInt("detailCount");
    	List<FinaCardRefundDetail> detailList = new ArrayList<FinaCardRefundDetail>();
    	boolean checkFlag = Boolean.TRUE;
    	if(detailCount>0){
    		for (int i = 1; i <= detailCount; i++) {
    			final FinaCardRefundDetail refundCard = getBean(FinaCardRefundDetail.class, "detailList-["+i+"]");
    			if(refundCard!=null){
    			    final String cardNumber = refundCard.getCardNumber();
                    if(StrKit.notBlank(cardNumber)) {
                        detailList.add(refundCard);
                        //查询是否有未结算订单存在
                        Long expenseRecordDetail = Db.queryLong("select count(id) from fina_expense_record_detail where del_flag='0' and is_settled='0' and card_number=? ", cardNumber);
                        if(expenseRecordDetail>0){
                            checkFlag = Boolean.FALSE;
                            returnRet = Ret.fail("msg", cardNumber + "会员卡还有账单未结算，不可退卡");
                            break;
                        }
                        Map<String,Double> lockInfo = finaMembershipCardService.getCardLockInfo(cardNumber);
                        //判断是否有锁定
                        if(lockInfo.get("lockBalance") != null && lockInfo.get("lockBalance") > 0.00){
                            checkFlag = Boolean.FALSE;
                            returnRet = Ret.fail("msg", cardNumber + "会员卡还有锁定金额未释放，不可退卡");
                            break;
                        }
                        if(lockInfo.get("lockConsumeTimes") != null && lockInfo.get("lockConsumeTimes") > 0.00){
                            checkFlag = Boolean.FALSE;
                            returnRet = Ret.fail("msg", cardNumber + "会员卡还有锁定天数未释放，不可退卡");
                            break;
                        }
                        if(lockInfo.get("lockPoints") != null && lockInfo.get("lockPoints") > 0.00){
                            checkFlag = Boolean.FALSE;
                            returnRet = Ret.fail("msg", cardNumber + "会员卡还有锁定点数未释放，不可退卡");
                            break;
                        }
                        if(lockInfo.get("lockIntegrals") != null && lockInfo.get("lockIntegrals") > 0.00){
                            checkFlag = Boolean.FALSE;
                            returnRet = Ret.fail("msg", cardNumber + "会员卡还有锁定积分未释放，不可退卡");
                            break;
                        }
                        if(lockInfo.get("lockBeanCoupons") != null && lockInfo.get("lockBeanCoupons") > 0.00){
                            checkFlag = Boolean.FALSE;
                            returnRet = Ret.fail("msg", cardNumber + "会员卡还有锁定豆豆券未释放，不可退卡");
                            break;
                        }
                    }
                }
    		}
    	}
    	if(checkFlag){
            if(detailList!=null && detailList.size()>0){
                model.setDetailList(detailList);
            }
            if(StrKit.notBlank(model.getId())){
                model.setUpdateBy(AuthUtils.getUserId());
            }else{
                model.setUpdateBy(AuthUtils.getUserId());
                model.setCreateBy(AuthUtils.getUserId());
            }
            if(finaCardRefundService.saveRefund(model)){
                returnRet = Ret.ok("msg","操作成功");
            }
        }
        renderJson(returnRet);
    }
    
    public void updateRefund(){
    	FinaCardRefund model = getBean(FinaCardRefund.class,"",true);
		model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateTime(new Date());
		if(finaCardRefundService.update(model)) {
            //完成要更新会员卡退卡完成状态
            if("completed".equals(model.getRecordStatus())){
                List<FinaCardRefundDetail> detailList = finaCardRefundDetailService.findListByRefundId(model.getId());
                for(FinaCardRefundDetail detail : detailList){
                    final String cardId = detail.getCardId();
                    FinaMembershipCard memberCard = new FinaMembershipCard();
                    memberCard.setId(cardId);
                    memberCard.setReturnCardStatus(ReturnCardStatus.FINISH);
                    memberCard.setUpdateTime(new Date());
                    memberCard.update();
                }
            }
            renderJson(Ret.ok("msg","操作成功"));
		}else {
			renderJson(Ret.fail("msg","操作失败"));
		}
    }
}
