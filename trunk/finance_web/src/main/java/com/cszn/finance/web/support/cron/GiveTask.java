package com.cszn.finance.web.support.cron;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.cszn.finance.web.support.give.GiveRuleGenerate;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.fina.FinaConditionRechargeSchemeDetailService;
import com.cszn.integrated.service.api.fina.FinaConditionRechargeSchemeService;
import com.cszn.integrated.service.api.fina.FinaLeaseRecordRoomPaymentService;
import com.cszn.integrated.service.api.fina.FinaLeaseRecordRoomService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainCardGiveSchemeRuleService;
import com.cszn.integrated.service.entity.enums.ConditionRechargeType;
import com.cszn.integrated.service.entity.enums.ConditionType;
import com.cszn.integrated.service.entity.fina.FinaCardRecharge;
import com.cszn.integrated.service.entity.fina.FinaConditionRechargeRecord;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.components.schedule.annotation.Cron;

@Cron("10 0 * * *")
public class GiveTask implements Runnable {

    @Inject
    private GiveRuleGenerate giveRuleGenerate;
    @Inject
    private FinaMembershipCardService finaMembershipCardService;
    @Inject
    private MainCardGiveSchemeRuleService mainCardGiveSchemeRuleService;
    @Inject
    private FinaLeaseRecordRoomPaymentService finaLeaseRecordRoomPaymentService;
    @Inject
    private FinaLeaseRecordRoomService finaLeaseRecordRoomService;
    @Inject
    FinaConditionRechargeSchemeService finaConditionRechargeSchemeService;
    @Inject
    FinaConditionRechargeSchemeDetailService finaConditionRechargeSchemeDetailService;

    @Override
    public void run() {
        //giveRuleGenerate.dayGive();
        //giveRuleGenerate.monthGive();
        //giveRuleGenerate.seasonGive();
        //giveRuleGenerate.yearGive();
        //cardExpire();
        try {
            //分期赠送
            cardGive(1,500,"1");
        }catch (Exception e){
            e.printStackTrace();
        }
        try {
            //分期充值
            cardGive(1,500,"2");
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //条件赠送
            conditionRecharge(1,500);
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //房租涨幅
            //finaLeaseRecordRoomService.updateRoomRent();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //推送付款单
            //finaLeaseRecordRoomPaymentService.sendRoomPayment();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 将最大有效期为昨天的会员卡状态改成过期状态
     */
    public void cardExpire(){
        Db.update("update fina_membership_card set expire_flag='1',update_time=NOW() where expire_flag='0' and TO_DAYS(expire_date)=TO_DAYS(DATE_SUB(now(),INTERVAL 1 DAY))");
    }


    public void cardGive(int pageIndex,int pageSize,String type){
        //type=1分期赠送，type=2分期充值

        Page<Record> recordPage=null;
        if("1".equals(type)){
            String sql=" from fina_membership_card where del_flag='0' and is_lock='0' and give_scheme_id is not null ";
            recordPage=Db.paginate(pageIndex,pageSize,"select id,parent_id,card_type_id,open_time,give_scheme_id as scheme_id ",sql);
        }else if("2".equals(type)){
            String sql=" from fina_membership_card where del_flag='0' and is_lock='0' and recharge_scheme_id is not null ";
            recordPage=Db.paginate(pageIndex,pageSize,"select id,parent_id,card_type_id,open_time,recharge_scheme_id as scheme_id ",sql);
        }

        String currDateStr= DateUtils.getDate("yyyy-MM-dd");
        Date currDate=new Date();


        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<FinaCardRecharge> cardRechargeList=new ArrayList<>();
        for (Record card : recordPage.getList()) {
        	final String parentId = card.getStr("parent_id");
        	final String cardTypeId = card.getStr("card_type_id");
        	//如果是38000元第二期花山床位卡赠送卡这个卡类别，需要查询主卡有没有消费记录，有消费记录不需要再赠送，没有消费记录就要继续赠送
        	if("25934A31-E6EF-4628-89F4-136F6AC33F03".equals(cardTypeId)) {
        		FinaMembershipCard memberCard = finaMembershipCardService.get(parentId);
        		if(memberCard!=null) {
        			final String cardNumber = memberCard.getCardNumber();
        			Long count = Db.queryLong("select count(*) from fina_settle_detail where del_flag='0' and card_number=?", cardNumber);
        			//如果查询到主卡有消费记录就跳过
        			if(count!=null && count > 0) {
        				continue;
        			}
        		}
        	}
            List<Record> giveRecordList = mainCardGiveSchemeRuleService.getCardGiveSchemeRuleResult(card.getDate("open_time"),card.getStr("scheme_id"));
            for (Record giveRecord : giveRecordList) {
                if(currDateStr.equals(giveRecord.getStr("giveDate"))){
                    //添加一条赠送的充值记录
                    FinaCardRecharge cardRecharge=new FinaCardRecharge();
                    cardRecharge.setId(IdGen.getUUID());
                    cardRecharge.setCardId(card.getStr("id"));
                    if("1".equals(type)){
                        cardRecharge.setClassify("subsection_give");
                        cardRecharge.setDescribe("分期赠送："+giveRecord.getStr("giveRemark"));
                    }else if("2".equals(type)){
                        cardRecharge.setClassify("subsection_recharge");
                        cardRecharge.setDescribe("分期充值："+giveRecord.getStr("giveRemark"));
                    }

                    cardRecharge.setAmount(0.0);
                    cardRecharge.setConsumeTimes(0.0);
                    cardRecharge.setConsumePoints(0.0);
                    cardRecharge.setConsumeIntegral(0.0);
                    cardRecharge.setAutoRuleId(giveRecord.getStr("ruleId"));
                    cardRecharge.setAutoSeq(giveRecord.getInt("seq"));
                    cardRecharge.setConsumeBeanCoupons(0.0);
                    cardRecharge.setPrice(0.0);
                    cardRecharge.setCancelFlag("0");
                    if(giveRecord.getDouble("giveTimes")>0){
                        cardRecharge.setType("2");
                        cardRecharge.setConsumeTimes(giveRecord.getDouble("giveTimes"));
                    }else if(giveRecord.getDouble("giveAmount")>0){
                        cardRecharge.setType("1");
                        cardRecharge.setConsumeTimes(giveRecord.getDouble("giveAmount"));
                    }else if(giveRecord.getDouble("giveIntegrals")>0){
                        cardRecharge.setType("4");
                        cardRecharge.setConsumeIntegral(giveRecord.getDouble("giveIntegrals"));
                    }
                    cardRecharge.setIsIncome("0");
                    cardRecharge.setIsCash("0");
                    cardRecharge.setRechargeTime(currDate);
                    cardRecharge.setRechargeWay("single");
                    cardRecharge.setIsReview("0");
                    cardRecharge.setIsAuto("1");
                    cardRecharge.setDelFlag("0");
                    cardRecharge.setCreateTime(new Date());
                    cardRecharge.setUpdateTime(new Date());
                    cardRecharge.setIsCount("0");
                    cardRecharge.setAutoType(type);
                    cardRecharge.setAverageDays(0.0);
                    cardRecharge.setCountPrice(0.0);
                    cardRecharge.setCountDays(0.0);

                    cardRechargeList.add(cardRecharge);

                }
            }
        }
        if(cardRechargeList.size()>0){
            Db.batchSave(cardRechargeList,cardRechargeList.size());
        }


        pageIndex++;
        if(pageIndex<=recordPage.getTotalPage()){
            cardGive(pageIndex,pageSize,type);
        }
    }

    public void conditionRecharge(int pageIndex,int pageSize){

        Page<Record> recordPage=null;
        String sql=" from fina_membership_card where del_flag='0' and is_lock='0' and condition_recharge_scheme_id is not null ";
        recordPage=Db.paginate(pageIndex,pageSize,"select id,cash_spend_count_days,spend_give_count_days,condition_recharge_scheme_id ",sql);

        String currDateStr= DateUtils.getDate("yyyy-MM-dd");
        Date currDate=new Date();


        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<FinaCardRecharge> cardRechargeList=new ArrayList<>();
        List<FinaConditionRechargeRecord> rechargeRecordList=new ArrayList<>();
        Map<String,Double> cardSpendGiveCountDaysMap=new HashMap<>();
        for (Record card : recordPage.getList()) {
            List<Record> conditionRechargeList = Db.find("select b.*,condition_type from fina_condition_recharge_scheme a " +
                    "inner join fina_condition_recharge_scheme_detail b on a.id=b.scheme_id " +
                    "where a.id=? and a.del_flag='0' and b.del_flag='0' and b.condition_value<=? " +
                    "and b.id not in (select detail_id from fina_condition_recharge_record where del_flag='0' and card_id=?)"
                    ,card.getStr("condition_recharge_scheme_id"),card.getDouble("cash_spend_count_days"),card.getStr("id"));
            //cardSpendGiveCountDaysMap.put(card.getStr("id"),card.getDouble("spend_give_count_days"));
            for (Record giveRecord : conditionRechargeList) {
                //添加一条赠送的充值记录
                FinaCardRecharge cardRecharge=new FinaCardRecharge();
                cardRecharge.setId(IdGen.getUUID());
                cardRecharge.setCardId(card.getStr("id"));

                ConditionType conditionType=ConditionType.getByKey(giveRecord.getStr("condition_type"));
                ConditionRechargeType conditionRechargeType = ConditionRechargeType.getByKey(giveRecord.getStr("recharge_type"));

                cardRecharge.setClassify("condition_recharge");
                cardRecharge.setDescribe(conditionType.getValue()+"满："+giveRecord.getDouble("condition_value")+"赠送："+giveRecord.getStr("recharge_value")+conditionRechargeType.getValue());

                cardRecharge.setAmount(0.0);
                cardRecharge.setConsumeTimes(0.0);
                cardRecharge.setConsumePoints(0.0);
                cardRecharge.setConsumeIntegral(0.0);
                //cardRecharge.setAutoRuleId(giveRecord.getStr("ruleId"));
                //cardRecharge.setAutoSeq(giveRecord.getInt("seq"));
                cardRecharge.setConsumeBeanCoupons(0.0);
                cardRecharge.setPrice(0.0);
                cardRecharge.setCancelFlag("0");

                if(ConditionRechargeType.times.getKey().equals(conditionRechargeType.getKey())){
                    cardRecharge.setType("2");
                    cardRecharge.setConsumeTimes(giveRecord.getDouble("recharge_value"));
                }else if(ConditionRechargeType.amount.getKey().equals(conditionRechargeType.getKey())){
                    cardRecharge.setType("1");
                    cardRecharge.setConsumeTimes(giveRecord.getDouble("recharge_value"));
                }else if(ConditionRechargeType.integral.getKey().equals(conditionRechargeType.getKey())){
                    cardRecharge.setType("4");
                    cardRecharge.setConsumeIntegral(giveRecord.getDouble("recharge_value"));
                }

                cardRecharge.setIsIncome("0");
                cardRecharge.setIsCash("0");
                cardRecharge.setRechargeTime(currDate);
                cardRecharge.setRechargeWay("single");
                cardRecharge.setIsReview("0");
                cardRecharge.setIsAuto("1");
                cardRecharge.setDelFlag("0");
                cardRecharge.setCreateTime(new Date());
                cardRecharge.setUpdateTime(new Date());
                cardRecharge.setIsCount("0");
                cardRecharge.setAutoType("3");
                cardRecharge.setAverageDays(0.0);
                cardRecharge.setCountPrice(0.0);
                cardRecharge.setCountDays(0.0);
                cardRechargeList.add(cardRecharge);

                FinaConditionRechargeRecord rechargeRecord=new FinaConditionRechargeRecord();
                rechargeRecord.setId(IdGen.getUUID());
                rechargeRecord.setCardId(card.getStr("id"));
                rechargeRecord.setDetailId(giveRecord.getStr("id"));
                rechargeRecord.setRechargeType(giveRecord.getStr("recharge_type"));
                rechargeRecord.setRechargeValue(giveRecord.getDouble("recharge_value"));
                rechargeRecord.setDelFlag("0");
                rechargeRecord.setCreateDate(new Date());
                rechargeRecord.setUpdateDate(new Date());
                rechargeRecordList.add(rechargeRecord);

                /*Double giveCountDays = cardSpendGiveCountDaysMap.get(card.getStr("id"));
                if(giveCountDays==null){
                    giveCountDays=0.0;
                }*/
                //cardSpendGiveCountDaysMap.put(card.getStr("id"), BigDecimal.valueOf(giveCountDays).add(BigDecimal.valueOf()))
            }
        }
        if(cardRechargeList.size()>0){
            Db.batchSave(cardRechargeList,cardRechargeList.size());
        }
        if(rechargeRecordList.size()>0){
            Db.batchSave(rechargeRecordList,rechargeRecordList.size());
        }


        pageIndex++;
        if(pageIndex<=recordPage.getTotalPage()){
            conditionRecharge(pageIndex,pageSize);
        }
    }
}
