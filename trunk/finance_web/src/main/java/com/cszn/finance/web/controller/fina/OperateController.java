/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardCollectService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardOperateService;
import com.cszn.integrated.service.api.main.MainAreaService;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.member.MmsMemberLinkService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.fina.FinaMembershipCardOperate;
import com.cszn.integrated.service.entity.main.MainArea;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.member.MmsMemberLink;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.wms.WmsUnit;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Created by LiangHuiLing on 2024年6月14日
 * 操作记录控制器
 * OperateController
 */
@RequestMapping(value="/operate", viewPath="/modules_page/finance/fina/operate")
public class OperateController extends BaseController {

	@Inject
	private FinaMembershipCardOperateService finaMembershipCardOperateService;
	
	public void index(){
		final String dataId = getPara("dataId");
		setAttr("dataId",dataId);
		render("operateIndex.html");
	}
	
	@Clear(LogInterceptor.class)
	public void pageTable(){
		FinaMembershipCardOperate model = getBean(FinaMembershipCardOperate.class,"",true);
		Columns columns = Columns.create().add("del_flag", DelFlag.NORMAL);
		columns.eq("data_id", model.getDataId());
		Page<FinaMembershipCardOperate> modelPage = finaMembershipCardOperateService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "operate_time desc");
		renderJson(new DataTable<FinaMembershipCardOperate>(modelPage));
	}
}
