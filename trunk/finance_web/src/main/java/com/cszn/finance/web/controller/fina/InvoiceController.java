package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaInvoiceService;
import com.cszn.integrated.service.entity.fina.FinaInvoice;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 发票管理
 */
@RequestMapping(value="/fina/invoice", viewPath="/modules_page/finance/fina/invoice")
public class InvoiceController extends BaseController {

    @Inject
    private FinaInvoiceService finaInvoiceService;


    /**
     * 跳转发票管理页面
     */
    public void index(){
        render("invoiceIndex.html");
    }


    /**
     * 发票列表
     */
    /*@Clear(LogInterceptor.class)
    public void findListPage(){
        FinaInvoice invoice = getBean(FinaInvoice.class,"",true);
        Page<Record> page  = finaInvoiceService.findList(getParaToInt("page"),getParaToInt("limit"),invoice);
        renderJson(new DataTable<Record>(page));
    }*/


    /**
     *旅居接口：发票列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        FinaInvoice invoice = getBean(FinaInvoice.class,"",true);
        Page<Record> page  = finaInvoiceService.finaListSojourn(getParaToInt("page"),getParaToInt("limit"),invoice);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 跳转详情页面
     */
    public void form(){
        String id = getPara("id");
        FinaInvoice invoice = finaInvoiceService.get(id);
        setAttr("invoice",invoice);
        render("invoiceForm.html");
   }


    /**
     * 调用旅居：跳转详情页面
     */
   public void formSojourn(){
       FinaInvoice invoice = getBean(FinaInvoice.class,"",true);
       //处理undefined
       //region Description
       if(StringUtils.isNotBlank(invoice.getInvoiceCode()) && "undefined".equals(invoice.getInvoiceCode()))invoice.setInvoiceCode("");
       if(StringUtils.isNotBlank(invoice.getInvoiceNo()) && "undefined".equals(invoice.getInvoiceNo()))invoice.setInvoiceNo("");
       if(StringUtils.isNotBlank(invoice.getIdentificationNo()) && "undefined".equals(invoice.getIdentificationNo()))invoice.setIdentificationNo("");
       if(StringUtils.isNotBlank(invoice.getBankName()) && "undefined".equals(invoice.getBankName()))invoice.setBankName("");
       if(StringUtils.isNotBlank(invoice.getBankCardNo()) && "undefined".equals(invoice.getBankCardNo()))invoice.setBankCardNo("");
       if(StringUtils.isNotBlank(invoice.getContractNo()) && "undefined".equals(invoice.getContractNo()))invoice.setContractNo("");
       if(StringUtils.isNotBlank(invoice.getBankName()) && "undefined".equals(invoice.getBankName()))invoice.setBankName("");
       if(StringUtils.isNotBlank(invoice.getReceiveName()) && "undefined".equals(invoice.getReceiveName()))invoice.setReceiveName("");
       if(StringUtils.isNotBlank(invoice.getLandlineNo()) && "undefined".equals(invoice.getLandlineNo()))invoice.setLandlineNo("");
       if(StringUtils.isNotBlank(invoice.getEmail()) && "undefined".equals(invoice.getEmail()))invoice.setEmail("");
       if(StringUtils.isNotBlank(invoice.getReceiveMobile()) && "undefined".equals(invoice.getReceiveMobile()))invoice.setReceiveMobile("");
       if(StringUtils.isNotBlank(invoice.getReceiveAddress()) && "undefined".equals(invoice.getReceiveAddress()))invoice.setReceiveAddress("");
       if(StringUtils.isNotBlank(invoice.getInvoiceCode()) && "undefined".equals(invoice.getInvoiceCode()))invoice.setInvoiceCode("");
       if(StringUtils.isNotBlank(invoice.getInvoiceCode()) && "undefined".equals(invoice.getInvoiceCode()))invoice.setInvoiceCode("");
       //endregion
       setAttr("invoice",invoice);
       setAttr("baseName",getPara("baseName"));
       render("invoiceForm.html");
   }


    /**
     * 获取发票明细
     */
   /*public void details(){
       String id = getPara("id");
       List<Record> list = Db.find("select id,invoice_id as invoiceId,unique_id as uniqueId,name," +
               "unit,unit_price as unitPrice,tax_rate as taxRate,tax_amount as taxAmount from fina_invoice_detail where invoice_id = ?",id);
       renderJson(new DataTable<Record>(list));
   }*/



    /**
     * 调用旅居：获取发票明细
     */
    public void details(){
        String id = getPara("id");
        List<Record> list = finaInvoiceService.getDetailsSojourn(id);
        renderJson(new DataTable<Record>(list));
    }
}
