package com.cszn.finance.web.controller.fina;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.fina.FinaPunishRecordService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.enums.DeductType;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.fina.FinaPunishRecord;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.sys.Dict;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 处罚结算管理
 */
@RequestMapping(value="/fina/punishRecord", viewPath="/modules_page/finance/fina/punishRecord")
public class PunishRecordController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private FinaPunishRecordService finaPunishRecordService;

    @Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;

    @Inject
    private MainBaseService mainBaseService;

    @Inject
    private DictService dictService;

    @Inject
    private FinaMembershipCardService finaMembershipCardService;




    /**
     * 跳转处罚结算管理
     */
    public void index(){
        List<MainBase> baseList = mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        List<Dict> dictList = dictService.getListByTypeOnUse("punish_type");
        setAttr("dictList",dictList);
        render("punishRecordIndex.html");
    }


    /**
     * 处罚结算列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String fullName = getPara("fullName");
        FinaPunishRecord pr = getBean(FinaPunishRecord.class,"",true);
        Page<Record> page = finaPunishRecordService.findPage(getParaToInt("page"),getParaToInt("limit"),pr,fullName);
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value==null){
                    return "";
                }
                if(value != null && value instanceof Double){
                    return value+"";
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(page),valueFilter));
    }


    /**
     * 跳转结算界面
     */
    public void form(){
        String id = getPara("id");
        String type = getPara("type");
        Record record = Db.findFirst("select pr.id,pr.app_no as appNo,pr.base_id as baseId,pr.office_id as officeId,pr.book_no as bookNo," +
                "pr.checkin_no as checkinNo,pr.punish_type as punishType,pr.card_number as cardNumber,pr.book_create_time as bookCreateTime," +
                "pr.book_cancel_time as bookCancelTime,pr.plan_checkin_time as planCheckinTime,pr.actual_checkin_time as actualCheckinTime," +
                "pr.plan_checkout_time as planCheckoutTime,pr.actual_checkout_time as actualCheckoutTime,pr.describe,pr.amount,pr.times,pr.points,pr.take_time as takeTime," +
                "pr.status,pr.remark,pr.del_flag as delFlag,pr.create_by as createBy,pr.create_time as createTime,pr.update_by as updateBy,pr.update_time as updateTime," +
                "mb.base_name as baseName,bo.short_name as officeName,mm.full_name as fullName,pr.name from fina_punish_record pr left join main_base mb on pr.base_id = mb.id " +
                "left join main_branch_office bo on pr.office_id = bo.id " +
                "left join fina_membership_card mc on pr.card_number = mc.card_number " +
                "left join mms_member mm on mc.member_id = mm.id " +
                "where pr.del_flag = 0 and mc.del_flag = 0 and pr.id = ?",id);

        //处理处罚类型展示
        if(record != null){
            String punishTypeName = "";
            String punishType = record.getStr("punishType");
            if("cancel_book".equals(punishType)){
                punishTypeName = "取消预订";
            }else if("delay_checkin".equals(punishType)){
                punishTypeName = "推迟入住";
            }else if("advance_checkout".equals(punishType)){
                punishTypeName = "提前退住";
            }
            record.set("punishTypeName",punishTypeName);
        }

        //处理记录状态展示
        if(record != null){
            String statusName = "";
            String status = record.getStr("status");
            if("0".equals(status)){
                statusName = "未处理";
            }else if("1".equals(status)){
                statusName = "已处理";
            }else if("2".equals(status)){
                statusName = "作废";
            }
            record.set("statusName",statusName);
        }

        //获取会员卡扣卡方式
        if(record != null && StringUtils.isNotBlank(record.getStr("cardNumber"))){
            MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(record.getStr("cardNumber"));
            if(scheme==null){
                scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(record.getStr("cardNumber"));
            }
            if(scheme != null && StringUtils.isNotBlank(scheme.getDeductWay())){
                setAttr("deductWay",scheme.getDeductWay());
            }
        }

        setAttr("pr",record);
        setAttr("type",type);
        render("punishRecordForm.html");
    }



    /**
     * 作废
     */
    public void delete(){
        String id = getPara("id");
        boolean flag = finaPunishRecordService.delPunish(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 结算
     */
    public void settle(){
        try {
            FinaPunishRecord pr = getBean(FinaPunishRecord.class,"pr",true);
            if(StringUtils.isBlank(pr.getId()) || StringUtils.isBlank(pr.getCardNumber())){ renderJson(Ret.fail("msg", "结算参数缺失"));return; }

            FinaMembershipCard card = finaMembershipCardService.getCardByNumber(pr.getCardNumber());
            if(card == null){ renderJson(Ret.fail("msg", "会员卡系统不存在"));return; }

            MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(pr.getCardNumber());
            if(scheme==null){
                scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(pr.getCardNumber());
            }
            if(scheme == null || scheme.getOnceConsume() == null || StringUtils.isBlank(scheme.getDeductWay()))
            { renderJson(Ret.fail("msg", "会员卡扣卡规则未定义"));return; }

            if(card.getConsumeTimes() == null)card.setConsumeTimes(0.00);
            if(card.getBalance() == null)card.setBalance(0.00);
            if(card.getConsumePoints() == null)card.setConsumePoints(0.00);
            if(card.getGiveRechargeAmount()==null)card.setGiveRechargeAmount(0.00);
            if(card.getGiveRechargeDays()==null)card.setGiveRechargeDays(0.00);
            if(card.getGiveRechargeIntegrals()==null)card.setGiveRechargeIntegrals(0.00);
            if(card.getBuyRechargeAmount()==null)card.setBuyRechargeAmount(0.00);
            if(card.getBuyRechargeDays()==null)card.setBuyRechargeDays(0.00);
            if(card.getBuyRechargeIntegrals()==null)card.setBuyRechargeIntegrals(0.00);

            //获取会员卡锁定
            Map<String,Double> lockMap=finaMembershipCardService.getCardLockInfo(card.getCardNumber());
            if(DeductType.deductTimes.getKey().equals(scheme.getDeductWay())){
               Double diffVal = BigDecimal.valueOf(card.getConsumeTimes()).subtract(BigDecimal.valueOf(lockMap.get("lockConsumeTimes"))).doubleValue();
               if(card.getConsumeTimes() < 0.00 || diffVal < 0){
                   renderJson(Ret.fail("msg", "会员卡剩余天数不足，无法扣费"));
                   return;
               }
            }else if(DeductType.deductAmount.getKey().equals(scheme.getDeductWay())) {
                Double diffVal = BigDecimal.valueOf(card.getBalance()).subtract(BigDecimal.valueOf(lockMap.get("lockBalance"))).doubleValue();
                if (card.getBalance() < 0.00 || diffVal < 0) {
                    renderJson(Ret.fail("msg", "会员卡剩余金额不足，无法扣费"));
                    return;
                }
            }else if(DeductType.deductPoints.getKey().equals(scheme.getDeductWay())){
                Double diffVal = BigDecimal.valueOf(card.getConsumePoints()).subtract(BigDecimal.valueOf(lockMap.get("lockPoints"))).doubleValue();
                if (card.getConsumePoints() < 0.00 || diffVal < 0) {
                    renderJson(Ret.fail("msg", "会员卡剩余点数不足，无法扣费"));
                    return;
                }
            }else{
                renderJson(Ret.fail("msg", "会员卡扣卡规则不支持"));
                return;
            }

            boolean flag = finaPunishRecordService.punishSettle(pr,card,scheme,AuthUtils.getUserId());
            if(flag){
                renderJson(Ret.ok("msg", "扣费成功"));
            }else{
                renderJson(Ret.fail("msg", "扣费失败"));
            }
        } catch (Exception e) {
            logger.error("财务违约账单结算异常:[{}]",e);
            renderJson(Ret.fail("msg", "财务违约账单结算异常!"));
        }
    }

}
