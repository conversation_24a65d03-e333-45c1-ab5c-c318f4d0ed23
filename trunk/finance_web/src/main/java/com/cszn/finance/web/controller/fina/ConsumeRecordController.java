package com.cszn.finance.web.controller.fina;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.*;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainCardDeductSchemeService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.food.FoodInfo;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainCardDeductScheme;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Descript 会员卡消费管理
 * <AUTHOR>
 * @Date 2019-04-10
 */
@RequestMapping(value="/fina/consumerecord", viewPath="/modules_page/finance/fina/consumeRecord")
public class ConsumeRecordController extends BaseController {

	@Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;

	@Inject
    private FinaConsumeRecordService finaConsumeRecordService;

	@Inject
    private FinaCardTransactionsService finaCardTransactionsService;

	@Inject
    private FinaCardRechargeService finaCardRechargeService;

	@Inject
    private FinaMembershipCardService finaMembershipCardService;

	@Inject
    private MmsMemberService mmsMemberService;

	@Inject
    private MainBaseService mainBaseService;

	@Inject
    private FinaExpenseRecordService finaExpenseRecordService;

	@Inject
    private FinaPunishRecordService finaPunishRecordService;

	@Inject
    private MainCardDeductSchemeService mainCardDeductSchemeService;

	@Inject
    private FinaCardTransactionsDetailService finaCardTransactionsDetailService;

    /**
     * 跳转会员充值/消费记录界面
     */
    public void index(){
        String type = getPara("type");
        if("1".equals(type)){
            render("rechargeRecord.html");
        }else{
        	List<MainMembershipCardType> typeList = mainMembershipCardTypeService.findList(null);
            setAttr("typeList",typeList);
            render("recordIndex.html");
        }
    }

    public void detailIndex(){
        setAttr("tranId",getPara("id"));
        render("detailIndex.html");
    }

    /**
     * 消费扣卡明细分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void detailPageTable() {
        FinaCardTransactionsDetail model = getBean(FinaCardTransactionsDetail.class, "", true);
        Columns columns = Columns.create("del_flag", DelFlag.NORMAL).add("transactions_id", model.getTransactionsId());
        Page<FinaCardTransactionsDetail> modelPage = finaCardTransactionsDetailService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns , "create_time asc");
        renderJson(new DataTable<FinaCardTransactionsDetail>(modelPage));
    }

    /**
     * 会员消费记录列表
     */
    @Clear(LogInterceptor.class)
    public void getConsumeRecordList(){

        String cardNumber = getPara("cardNumber");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");

        Page<FinaConsumeRecord> page = finaConsumeRecordService.getConsumeRecordsByRecord(getParaToInt("page"),getParaToInt("limit"),cardNumber,startDate,endDate);
        renderJson(new DataTable<FinaConsumeRecord>(page));
    }




    /**
     * 展示会员充值/赠送/消费记录
     */
    @Clear(LogInterceptor.class)
    public void getConsumeRecord(){
    	String cardTypeId = getPara("cardTypeId");
        String cardNumber = getPara("cardNumber");
        String fullName = getPara("fullName");
        String inOutFlag = getPara("inOutFlag");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        Page<Record> page = finaCardTransactionsService.getConsumeRecord(cardTypeId, cardNumber,fullName,inOutFlag,startDate,endDate,getParaToInt("page"),getParaToInt("limit"));
        renderJson(new DataTable<Record>(page));
    }




    /**
     * 查询会员所有记录：充值，赠送，所有类型扣卡
     */
    @Clear(LogInterceptor.class)
    public void getAllRecordByCardNumber(){
        String cardNumber = getPara("cardNumber");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        Page<Record> page = finaConsumeRecordService.getAllRecordByCard(getParaToInt("page"),getParaToInt("limit"),cardNumber,startDate,endDate);
        renderJson(new DataTable<Record>(page));
    }




    /**
     * 获取会员卡交易记录
     */
    @Clear(LogInterceptor.class)
    public void getTransByParams(){
        String cardId = getPara("cardId");
        String cardNumber = getPara("cardNumber");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        String flag = getPara("flag");
        if(StringUtils.isBlank(cardId)){
            renderJson(new DataTable<FinaCardTransactions>());
            return;
        }
        Page<Record> page = finaCardTransactionsService.getTransByParams(getParaToInt("page"),getParaToInt("limit"),cardId,cardNumber,startDate,endDate,flag,"");
        if(page == null || page.getList() == null || page.getList().size() <= 0){
            renderJson(new DataTable<Record>(page));
            return;
        }
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value!=null && value instanceof Double){
                    return Double.toString((Double)value);
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(new DataTable<Record>(page),valueFilter));
    }

    @Clear(LogInterceptor.class)
    public void getTransListByParams(){
        String cardId = getPara("cardId");
        String cardNumber = getPara("cardNumber");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        String flag = getPara("flag");
        if(StringUtils.isBlank(cardId)){
            renderJson(new DataTable<FinaCardTransactions>());
            return;
        }
        if(StrKit.notBlank(cardNumber)){
            cardNumber=cardNumber.trim();
        }
        List<Record> list = finaCardTransactionsService.getTransListByParams(cardId,cardNumber,startDate,endDate,flag);
        if(list == null || list.size() <= 0){
            renderJson(list);
            return;
        }
        ValueFilter valueFilter=new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if(value!=null && value instanceof Double){
                    return Double.toString((Double)value);
                }
                return value;
            }
        };
        renderJson(JSONObject.toJSONString(list,valueFilter));
    }



    /**
     * 查看是否有原会员卡记录存在
     */
    public void checkHaveBeforeCard(){
        String cardId = getPara("cardId");
        //获取旧会员卡
        String oldCardId = Db.queryStr("select old_card_id from fina_card_transfer where new_card_id = ? order by create_time desc",cardId);
        if(StringUtils.isNotBlank(oldCardId)){
            renderJson(Ret.ok("data",oldCardId));
        }else{
            renderJson(Ret.fail("msg","没有原会员卡信息"));
        }
    }


    /**
     * 打印会员充值扣费
     */
    public void printForm(){
        String id = getPara("id");
        Record record = new Record();
        record.set("operator", AuthUtils.getLoginUser().getUserName());
        FinaCardTransactions tran = finaCardTransactionsService.findById(id);
        if(tran != null){
            String type = tran.getType();
            String expenseId = tran.getExpenseId();//业务id
            String cardId = tran.getCardId();
            Double times = tran.getTimes();
            Double amount = tran.getAmount();
            Double points = tran.getPoints();
            Double integrals=tran.getIntegrals();
            Double beanCoupons=tran.getBeanCoupons();
            String describe = tran.getDescribe();
            Date dealTime = tran.getDealTime();
            String dealTimeStr = TimeUtils.getStrDateByPattern(dealTime,"yyyy/MM/dd HH:mm:ss");

            if("recharge_prestore".equals(type)){//会员充值
                FinaCardRecharge recharge = finaCardRechargeService.findById(expenseId);
                if(recharge != null) {
                    record.set("consumeTimes", times);
                    record.set("amount", amount);
                    record.set("points",points);
                    record.set("integrals",integrals);
                    record.set("beanCoupons",beanCoupons);
                    record.set("giveAmount", recharge.getGiveAmount());
                    record.set("giveConsumeTimes", recharge.getGiveConsumeTimes());
                    record.set("giveConsumePoints",recharge.getGiveConsumePoints());
                    record.set("giveConsumeIntegral",recharge.getGiveConsumeIntegral());
                    record.set("giveBeanCoupons",recharge.getGiveBeanCoupons());
                    record.set("describe", describe);
                    record.set("serviceType", "会员充值");
                    record.set("dealTime", TimeUtils.getStrDateByPattern(recharge.getRechargeTime(),"yyyy/MM/dd"));
                }
            }else{//会员消费
                FinaConsumeRecord cr = finaConsumeRecordService.get(expenseId);
                if(cr != null){//应用扣费消费表
                    String consumeType = "";
                    if(StringUtils.isNotBlank(cr.getConsumeType())) {
                        consumeType = Db.queryStr("select dict_name from sys_dict where del_flag = 0 and dict_type = 'account_record_operate_type' and dict_value = ?", cr.getConsumeType());
                        if(StringUtils.isBlank(consumeType))consumeType = "消费";
                    }
                    record.set("appOrderNo",cr.getAppOrderNo());
                    record.set("consumeTimes",times);
                    record.set("amount",amount);
                    record.set("points",points);
                    record.set("integrals",integrals);
                    record.set("beanCoupons",beanCoupons);
                    record.set("describe",describe);
                    record.set("serviceType","会员" + consumeType);
                    record.set("dealTime",dealTimeStr);
                    if(StringUtils.isNotBlank(cr.getBaseId())){
                        MainBase base = mainBaseService.get(cr.getBaseId());
                        if(base != null) {
                            record.set("baseName", base.getBaseName());
                            record.set("tel", base.getTel());
                        }
                    }
                }else{//旅居扣费消费表
                    FinaExpenseRecord er = finaExpenseRecordService.get(expenseId);
                    if(er != null) {
                        record.set("appOrderNo", er.getExpenseNo());
                        record.set("amount", amount);
                        record.set("consumeTimes", times);
                        record.set("points",points);
                        record.set("integrals",integrals);
                        record.set("beanCoupons",beanCoupons);
                        record.set("describe", describe);
                        record.set("serviceType", "旅居入住消费");
                        record.set("dealTime", dealTimeStr);
                        if (StringUtils.isNotBlank(er.getBaseId())) {
                            MainBase base = mainBaseService.get(er.getBaseId());
                            if (base != null) {
                                record.set("baseName", base.getBaseName());
                                record.set("tel", base.getTel());
                            }
                        }
                    }else{//违约消费
                        FinaPunishRecord pr = finaPunishRecordService.get(expenseId);
                        if(pr != null){
                            String punishTypeName = "误房";
                            String punishType = pr.getPunishType();
                            if("cancel_book".equals(punishType)){
                                punishTypeName = "取消预订";
                            }else if("delay_checkin".equals(punishType)){
                                punishTypeName = "推迟入住";
                            }else if("advance_checkout".equals(punishType)){
                                punishTypeName = "提前退住";
                            }
                            record.set("punishTypeName",punishTypeName);
                            record.set("serviceType", "旅居" + punishTypeName +"消费");
                            record.set("amount", amount);
                            record.set("consumeTimes", times);
                            record.set("points",points);
                            record.set("integrals",integrals);
                            record.set("beanCoupons",beanCoupons);
                            record.set("dealTime", dealTimeStr);
                            record.set("describe", describe);
                            if (StringUtils.isNotBlank(pr.getBaseId())) {
                                MainBase base = mainBaseService.get(pr.getBaseId());
                                if (base != null) {
                                    record.set("baseName", base.getBaseName());
                                    record.set("tel", base.getTel());
                                }
                            }
                        }else{//转移卡产生的消费
                            record.set("consumeTimes",times);
                            record.set("amount",amount);
                            record.set("points",points);
                            record.set("integrals",integrals);
                            record.set("beanCoupons",beanCoupons);
                            record.set("describe",describe);
                            record.set("serviceType","会员日常消费");
                            record.set("dealTime",dealTimeStr);
                        }
                    }
                }
            }
            //获取会员卡
            if(StringUtils.isNotBlank(cardId)){
                FinaMembershipCard card = finaMembershipCardService.get(cardId);
                if(card != null){
                    Double consumeTimes = card.getConsumeTimes();
                    Double balance = card.getBalance();
                    Double consumePoints = card.getConsumePoints();
                    Double cardIntegrals=card.getCardIntegrals();

                    Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(card.getCardNumber());
                    Double lockTimes = lock.get("lockConsumeTimes");
                    Double lockAmount = lock.get("lockBalance");
                    Double lockPoints = lock.get("lockPoints");
                    Double lockIntegrals=lock.get("lockIntegrals");
                    Double lockBeanCoupons = Db.queryDouble("select sum(deduct_bean_coupons)totalDeductBeanCoupons from fina_card_deduct where del_flag='0' and cancel_flag='0' and is_review='0' and card_number=?", card.getCardNumber());

                    if(consumeTimes == null)consumeTimes = 0.00;
                    if(balance == null)balance = 0.00;

                    /*if(lockTimes != null && lockTimes != 0.00){
                        consumeTimes = BigDecimal.valueOf(consumeTimes).add(BigDecimal.valueOf(lockTimes)).doubleValue();
                    }
                    if(lockAmount != null && lockAmount != 0.00){
                        balance = BigDecimal.valueOf(balance).add(BigDecimal.valueOf(lockAmount)).doubleValue();
                    }*/


                    record.set("cardNumber",card.getCardNumber());
                    record.set("balance",balance);
                    record.set("cardTimes",consumeTimes);//储值剩余天数
                    record.set("consumePoints",consumePoints);
                    record.set("cardIntegrals",cardIntegrals);

                    record.set("lockTimes",lockTimes);
                    record.set("lockAmount",lockAmount);
                    record.set("lockPoints",lockPoints);
                    record.set("lockIntegrals",lockIntegrals);
                    record.set("lockBeanCoupons",lockBeanCoupons);
                    MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(card.getCardNumber());
                    if(scheme==null){
                        scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(card.getCardNumber());
                    }
                    if(scheme != null && StringUtils.isNotBlank(scheme.getDeductWay())){
                        record.set("deductWay",scheme.getDeductWay());
                    }
                    MmsMember member = mmsMemberService.get(card.getMemberId());
                    if(member != null){
                        record.set("fullName",member.getFullName());
                    }
                    MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
                    record.set("isIntegral",cardType.getIsIntegral());

                }
            }
            record.set("printTime", TimeUtils.getStrDateByPattern(new Date(),"yyyy/MM/dd HH:mm:ss"));
            setAttr("type",type);
            setAttr("record",record);

            if("recharge_prestore".equals(type)){
                render("printFormRecharge.html");
            }else{
                render("printFormDeduct.html");
            }
        }else{
            render("printFormDeduct.html");
        }
    }

    public void batchPrintForm(){
        String ids = getPara("ids");


        String[] idArray=ids.split(",");
        List<Record> recordList=new ArrayList<>();
        for (String id : idArray) {
            Record record = new Record();
            record.set("operator", AuthUtils.getLoginUser().getUserName());
            FinaCardTransactions tran = finaCardTransactionsService.findById(id);

            String type = tran.getType();
            String expenseId = tran.getExpenseId();//业务id
            String cardId = tran.getCardId();
            Double times = tran.getTimes();
            Double amount = tran.getAmount();
            Double points = tran.getPoints();
            Double integrals=tran.getIntegrals();
            Double beanCoupons=tran.getBeanCoupons();
            String describe = tran.getDescribe();
            Date dealTime = tran.getDealTime();
            String dealTimeStr = TimeUtils.getStrDateByPattern(dealTime,"yyyy/MM/dd HH:mm:ss");

            FinaConsumeRecord cr = finaConsumeRecordService.get(expenseId);
            if(cr != null){//应用扣费消费表
                String consumeType = "";
                if(StringUtils.isNotBlank(cr.getConsumeType())) {
                    consumeType = Db.queryStr("select dict_name from sys_dict where del_flag = 0 and dict_type = 'account_record_operate_type' and dict_value = ?", cr.getConsumeType());
                    if(StringUtils.isBlank(consumeType))consumeType = "消费";
                }
                record.set("appOrderNo",cr.getAppOrderNo());
                record.set("consumeTimes",times);
                record.set("amount",amount);
                record.set("points",points);
                record.set("integrals",integrals);
                record.set("beanCoupons",beanCoupons);
                record.set("describe",describe);
                record.set("serviceType","会员" + consumeType);
                record.set("dealTime",dealTimeStr);
                if(StringUtils.isNotBlank(cr.getBaseId())){
                    MainBase base = mainBaseService.get(cr.getBaseId());
                    if(base != null) {
                        record.set("baseName", base.getBaseName());
                        record.set("tel", base.getTel());
                    }
                }
            }else{//旅居扣费消费表
                FinaExpenseRecord er = finaExpenseRecordService.get(expenseId);
                if(er != null) {
                    record.set("appOrderNo", er.getExpenseNo());
                    record.set("amount", amount);
                    record.set("consumeTimes", times);
                    record.set("points",points);
                    record.set("integrals",integrals);
                    record.set("beanCoupons",beanCoupons);
                    record.set("describe", describe);
                    record.set("serviceType", "旅居入住消费");
                    record.set("dealTime", dealTimeStr);
                    if (StringUtils.isNotBlank(er.getBaseId())) {
                        MainBase base = mainBaseService.get(er.getBaseId());
                        if (base != null) {
                            record.set("baseName", base.getBaseName());
                            record.set("tel", base.getTel());
                        }
                    }
                }else{//违约消费
                    FinaPunishRecord pr = finaPunishRecordService.get(expenseId);
                    if(pr != null){
                        String punishTypeName = "误房";
                        String punishType = pr.getPunishType();
                        if("cancel_book".equals(punishType)){
                            punishTypeName = "取消预订";
                        }else if("delay_checkin".equals(punishType)){
                            punishTypeName = "推迟入住";
                        }else if("advance_checkout".equals(punishType)){
                            punishTypeName = "提前退住";
                        }
                        record.set("punishTypeName",punishTypeName);
                        record.set("serviceType", "旅居" + punishTypeName +"消费");
                        record.set("amount", amount);
                        record.set("consumeTimes", times);
                        record.set("points",points);
                        record.set("integrals",integrals);
                        record.set("beanCoupons",beanCoupons);
                        record.set("dealTime", dealTimeStr);
                        record.set("describe", describe);
                        if (StringUtils.isNotBlank(pr.getBaseId())) {
                            MainBase base = mainBaseService.get(pr.getBaseId());
                            if (base != null) {
                                record.set("baseName", base.getBaseName());
                                record.set("tel", base.getTel());
                            }
                        }
                    }else{//转移卡产生的消费
                        record.set("consumeTimes",times);
                        record.set("amount",amount);
                        record.set("points",points);
                        record.set("integrals",integrals);
                        record.set("beanCoupons",beanCoupons);
                        record.set("describe",describe);
                        record.set("serviceType","会员日常消费");
                        record.set("dealTime",dealTimeStr);
                    }
                }
            }
            //获取会员卡
            if(StringUtils.isNotBlank(cardId)){
                FinaMembershipCard card = finaMembershipCardService.get(cardId);
                if(card != null){
                    Double consumeTimes = card.getConsumeTimes();
                    Double balance = card.getBalance();
                    Double consumePoints = card.getConsumePoints();
                    Double cardIntegrals=card.getCardIntegrals();

                    Map<String,Double> lock=finaMembershipCardService.getCardLockInfo(card.getCardNumber());
                    Double lockTimes = lock.get("lockConsumeTimes");
                    Double lockAmount = lock.get("lockBalance");
                    Double lockPoints = lock.get("lockPoints");
                    Double lockIntegrals=lock.get("lockIntegrals");
                    Double lockBeanCoupons = Db.queryDouble("select sum(deduct_bean_coupons)totalDeductBeanCoupons from fina_card_deduct where del_flag='0' and cancel_flag='0' and is_review='0' and card_number=?", card.getCardNumber());

                    if(consumeTimes == null)consumeTimes = 0.00;
                    if(balance == null)balance = 0.00;

                    /*if(lockTimes != null && lockTimes != 0.00){
                        consumeTimes = BigDecimal.valueOf(consumeTimes).add(BigDecimal.valueOf(lockTimes)).doubleValue();
                    }
                    if(lockAmount != null && lockAmount != 0.00){
                        balance = BigDecimal.valueOf(balance).add(BigDecimal.valueOf(lockAmount)).doubleValue();
                    }*/


                    record.set("cardNumber",card.getCardNumber());
                    record.set("balance",balance);
                    record.set("cardTimes",consumeTimes);//储值剩余天数
                    record.set("consumePoints",consumePoints);
                    record.set("cardIntegrals",cardIntegrals);

                    record.set("lockTimes",lockTimes);
                    record.set("lockAmount",lockAmount);
                    record.set("lockPoints",lockPoints);
                    record.set("lockIntegrals",lockIntegrals);
                    record.set("lockBeanCoupons",lockBeanCoupons);
                    MainCardDeductScheme scheme = mainCardDeductSchemeService.getSchemeByCardNumber(card.getCardNumber());
                    if(scheme==null){
                        scheme=mainCardDeductSchemeService.getLongSchemeByCardNumber(card.getCardNumber());
                    }
                    if(scheme != null && StringUtils.isNotBlank(scheme.getDeductWay())){
                        record.set("deductWay",scheme.getDeductWay());
                    }
                    MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
                    record.set("isIntegral",cardType.getIsIntegral());
                    MmsMember member = mmsMemberService.get(card.getMemberId());
                    if(member != null){
                        record.set("fullName",member.getFullName());
                    }
                }
            }
            record.set("printTime", TimeUtils.getStrDateByPattern(new Date(),"yyyy/MM/dd HH:mm:ss"));
            setAttr("type",type);
            recordList.add(record);
        }

        setAttr("recordList",recordList);
        render("batchPrintFormDeduct.html");
    }

    /**
     * 导出excel
     */
    public void exportRecord(){
    	String cardTypeId = getPara("cardTypeId");
        String cardNumber = getPara("cardNumber");
        String fullName = getPara("fullName");
        String inOutFlag = getPara("inOutFlag");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        List<Record> recordList=finaCardTransactionsService.getConsumeList(cardTypeId, cardNumber,fullName,inOutFlag,startDate,endDate);
        if(recordList==null || recordList.size()==0){
            renderJson(Ret.fail("msg","未查询到消费记录"));
            return;
        }

        String[] title={"结算时间","说明","卡主姓名","会员卡号","卡类别","消费类型","天数","金额","点数","积分","豆豆券","天数快照","金额快照","点数快照","积分快照","豆豆券快照"};
        String fileName="消费记录.xls";
        String sheetName = "消费记录";

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);

            content[i][0]=record.getStr("dealTime");
            content[i][1]=record.getStr("describe");
            content[i][2]=record.getStr("fullName");
            content[i][3]=record.getStr("cardNumber");
            content[i][4]=record.getStr("cardType");
            content[i][5]=record.getStr("dictName");
            content[i][6]=record.getStr("times");
            content[i][7]=record.getStr("amount");
            content[i][8]=record.getStr("points");
            content[i][9]=record.getStr("integrals");
            content[i][10]=record.getStr("beanCoupons");
            content[i][11]=record.getStr("timesSnapshot");
            content[i][12]=record.getStr("amountSnapshot");
            content[i][13]=record.getStr("pointsSnapshot");
            content[i][14]=record.getStr("integralsSnapshot");
            content[i][15]=record.getStr("beanCouponsSnapshot");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 导出excel
     */
    public void exportConsumeRecord(){
    	
    	String cardId = getPara("cardId");
        String cardNumber = getPara("cardNumber");
        String startDate = getPara("startDate");
        String endDate = getPara("endDate");
        String flag = getPara("flag");
        if(StringUtils.isBlank(cardId)){
        	renderJson(Ret.fail("msg","会员卡id不能为空"));
            return;
        }
        if(StrKit.notBlank(cardNumber)){
            cardNumber=cardNumber.trim();
        }
        List<Record> recordList = finaCardTransactionsService.getTransListByParams2(cardId,cardNumber,startDate,endDate,flag);
        if(recordList==null || recordList.size()==0){
    		renderJson(Ret.fail("msg","未查询到消费记录"));
    		return;
    	}
    	
    	String[] title={"充值/消费时间","类型","说明","结算备注","天数","金额","点数","积分","豆豆券","天数余额","金额余额","点数余额","积分余额","豆豆券余额"};
    	String fileName="消费记录.xls";
    	String sheetName = "消费记录";
    	
    	String[][] content=new String[recordList.size()][title.length];
    	for(int i=0;i<recordList.size();i++){
    		Record record=recordList.get(i);
    		final String type = record.getStr("type");
    		final String inOutFlag = record.getStr("inOutFlag");
    		
    		if("1".equals(inOutFlag)){
    			if("transfer_recharge".equals(type)||"upgrade_recharge".equals(type)){
    				if(record.getDate("dealTime")!=null){
    					content[i][0] = DateUtils.formatDate(record.getDate("dealTime"), "yyyy-MM-dd HH:mm:ss");
    				}else{
    					content[i][0] = "";
    				}
    			}else{
    				if(record.getDate("rechargeTime")!=null){
    					content[i][0] = DateUtils.formatDate(record.getDate("rechargeTime"), "yyyy-MM-dd");
    				}else{
    					content[i][0] = "";
    				}
    			}
    		}else if("2".equals(inOutFlag)){
    			content[i][0]= DateUtils.formatDate(record.getDate("dealTime"), "yyyy-MM-dd HH:mm:ss");
    		}
    		if("recharge_prestore".equals(type)){
    			content[i][1]="充值";
    		}else if("give_prestore".equals(type)){
    			content[i][1]="赠送";
    		}else if("book_deduction".equals(type)){
    			content[i][1]="预订扣除";
    		}else if("checkin_deduction".equals(type)){
    			content[i][1]="入住扣除";
    		}else if("daily_deduction".equals(type)){
    			content[i][1]="日常扣除";
    		}else if("cancel_recharge".equals(type)){
    			content[i][1]="撤销充值";
    		}else if("cancel_give".equals(type)){
    			content[i][1]="撤销赠送";
    		}else if("transfer_deduction".equals(type)){
    			content[i][1]="过户扣除";
    		}else if("transfer_recharge".equals(type)){
    			content[i][1]="过户转移";
    		}else if("upgrade_deduction".equals(type)){
    			content[i][1]="升级扣除";
    		}else if("upgrade_recharge".equals(type)){
    			content[i][1]="升级转移";
    		}else if("return_card_deduction".equals(type)){
    			content[i][1]="退卡扣除";
    		}else if("recharge_deduction".equals(type)){
    			content[i][1]="充值扣除";
    		}else if("give_deduction".equals(type)){
    			content[i][1]="赠送扣除";
    		}else{
    			content[i][1]="- -";
    		}
    		content[i][2]=record.getStr("describe");
    		content[i][3]=record.getStr("settleRemark");
    		content[i][4]=record.getStr("times")+"天";
    		content[i][5]=record.getStr("amount")+"元";
    		content[i][6]=record.getStr("points");
    		content[i][7]=record.getStr("integrals");
    		content[i][8]=record.getStr("beanCoupons");
    		content[i][9]=record.getStr("timesSnapshot")+"天";
    		content[i][10]=record.getStr("amountSnapshot")+"元";
    		content[i][11]=record.getStr("pointsSnapshot");
    		content[i][12]=record.getStr("integralsSnapshot");
    		content[i][13]=record.getStr("beanCouponsSnapshot");
    	}
    	
    	//创建HSSFWorkbook
    	HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
    	//响应到客户端
    	try {
    		ByteArrayOutputStream os = new ByteArrayOutputStream();
    		wb.write(os);
    		render(new StreamRender(fileName, os));
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    }
}
