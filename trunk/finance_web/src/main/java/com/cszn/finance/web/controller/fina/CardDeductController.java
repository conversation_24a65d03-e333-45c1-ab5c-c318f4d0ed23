/**
 * 
 */
package com.cszn.finance.web.controller.fina;

import com.cszn.finance.web.support.auth.AuthUtils;
import com.cszn.finance.web.support.log.LogInterceptor;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaCardDeductService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainAppJoinService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.entity.fina.FinaCardDeduct;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainAppJoin;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by LiangHuiLing on 2021年12月27日
 *
 * DictController
 */
@RequestMapping(value="/fina/deduct", viewPath="/modules_page/finance/fina/deduct")
public class CardDeductController extends BaseController {

	@Inject
	private FinaCardDeductService finaCardDeductService;
	@Inject
    FinaMembershipCardService finaMembershipCardService;
	@Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
	@Inject
    private MainAppJoinService mainAppJoinService;
	@Inject
    private MainBaseService mainBaseService;
	
	public void index(){
		render("deductIndex.html");
	}
	
	@Clear(LogInterceptor.class)
	public void pageTable(){
		FinaCardDeduct model = getBean(FinaCardDeduct.class, "", true);
        Page<FinaCardDeduct> modelPage = finaCardDeductService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<FinaCardDeduct>(modelPage));
	}
	
	/**
     * 添加页面方法
     */
    public void add() {
    	final FinaCardDeduct model = getBean(FinaCardDeduct.class, "", true);
    	FinaMembershipCard card=finaMembershipCardService.findById(model.getCardId());
    	MainMembershipCardType type = null;
        if(card != null && StringUtils.isNotBlank(card.getCardTypeId())){
            type = mainMembershipCardTypeService.findById(card.getCardTypeId());
        }
        List<MainAppJoin> appList =  mainAppJoinService.findAll();
        List<MainBase> baseList = mainBaseService.findBaseList();
    	setAttr("model", model);
    	setAttr("type",type);
        setAttr("card",card);
        setAttr("appList",appList);
        setAttr("baseList",baseList);
        render("deductForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String id = getPara("id");
        setAttr("model", finaCardDeductService.findById(id));
        render("deductForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
        final FinaCardDeduct model = getBean(FinaCardDeduct.class, "", true);
        //此处为空时，置为0
        if(model.getDeductAmount() == null)model.setDeductAmount(0.00);
        if(model.getDeductTimes() == null)model.setDeductTimes(0.00);
        if(model.getDeductPoints()==null)model.setDeductPoints(0.00);
        if(model.getDeductIntegral()==null)model.setDeductIntegral(0.00);
        FinaMembershipCard card = finaMembershipCardService.findById(model.getCardId());
        if(card!=null) {
        	MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
        	if(cardType!=null) {
        		if("1".equals(cardType.getIsIntegral())) {
        			Double lockBeanCoupons = Db.queryDouble("select sum(deduct_bean_coupons)totalDeductBeanCoupons from fina_card_deduct where del_flag='0' and cancel_flag='0' and is_review='0' and card_number=?", model.getCardNumber());
        			if(card.getBeanCoupons()>0) {
        				if(
    						BigDecimal.valueOf(card.getBeanCoupons()).subtract(BigDecimal.valueOf(model.getDeductBeanCoupons())).subtract(BigDecimal.valueOf(lockBeanCoupons)).doubleValue() < 0
    			        ) {
    			            renderJson(Ret.fail("msg","豆豆券不足，扣除失败!"));
    			        }else {
    			        	boolean flag = false;
    			        	if(StrKit.isBlank(model.getId())) {
    			        		model.setId(IdGen.getUUID());
    			        		model.setCancelFlag("0");
    			        		model.setIsReview("0");
    			        		model.setDelFlag(DelFlag.NORMAL);
    			        		model.setCreateBy(AuthUtils.getUserId());
    			        		model.setCreateTime(new Date());
    			        		model.setUpdateBy(AuthUtils.getUserId());
    			        		model.setUpdateTime(new Date());
    			        		flag = model.save();
    			        	}else {
    			        		model.setUpdateBy(AuthUtils.getUserId());
    			        		model.setUpdateTime(new Date());
    			        		flag = model.update();
    			        	}
    			        	if (flag) {
    			        		renderJson(Ret.ok("msg", "操作成功!"));
    			        	} else {
    			        		renderJson(Ret.fail("msg", "操作失败！"));
    			        	}
    			        }
        			}else {
        				renderJson(Ret.fail("msg","豆豆券为0，扣除失败!"));
        			}
        		}else {
        			renderJson(Ret.fail("msg","该会员卡不是积分卡，扣除失败!"));
        		}
        	}else {
        		renderJson(Ret.fail("msg","该会员卡没有卡类别，扣除失败!"));
        	}
        }else {
        	renderJson(Ret.fail("msg","该会员卡不存在，扣除失败!"));
        }
    }
    
    /**
     * 扣卡审核
     */
    public void saveDeductReview(){
    	String deductId = getPara("deductId");
    	Ret returnRet = Ret.fail("msg","审核失败");
    	FinaCardDeduct model = finaCardDeductService.findById(deductId);
    	if(model!=null) {
    		FinaMembershipCard card = finaMembershipCardService.findById(model.getCardId());
    		if(card!=null) {
    			if("1".equals(card.getIsLock())){
    	            returnRet = Ret.fail("msg","审核失败，会员卡为锁定状态");
    	        }else {
    	        	MainMembershipCardType cardType = mainMembershipCardTypeService.findById(card.getCardTypeId());
    	        	if(cardType!=null) {
    	        		if("1".equals(cardType.getIsIntegral())) {
    	        			returnRet = finaCardDeductService.saveDeductCard(model, card, AuthUtils.getUserId());
    	        		}else {
    	        			renderJson(Ret.fail("msg","该会员卡不是积分卡，审核失败!"));
    	        		}
    	        	}else {
    	        		returnRet = Ret.fail("msg","该会员卡没有卡类别，审核失败!");
    	        	}
    	        }
    		}else {
    			returnRet = Ret.fail("msg","会员卡不存在，审核失败!");
    		}
    	}else {
    		returnRet = Ret.fail("msg","数据不存在，审核失败!");
    	}
    	renderJson(returnRet);
    }
    
    
    /**
     * 更新方法
     */
    public void update() {
        final FinaCardDeduct model = getBean(FinaCardDeduct.class, "", true);
    	model.setUpdateBy(AuthUtils.getUserId());
    	model.setUpdateTime(new Date());
        if (finaCardDeductService.update(model)) {
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
}
