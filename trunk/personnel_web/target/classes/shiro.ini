[main]

#cache Manager
shiroCacheManager = com.cszn.integrated.base.plugin.shiro.cache.ShiroCacheManager
securityManager.cacheManager = $shiroCacheManager

#CredentialsMatcher
credentialsMatcher=com.cszn.integrated.base.plugin.shiro.RetryLimitHashedCredentialsMatcher
credentialsMatcher.hashAlgorithmName=md5
credentialsMatcher.hashIterations=2
credentialsMatcher.storedCredentialsHexEncoded=true
#允许的最大密码尝试次数，超过次数以后账户冻结指定时间
credentialsMatcher.allowRetryCount=10
#超过次数以后账户冻结的时间 单位:秒
credentialsMatcher.lockedSeconds=3600

#自定义认证授权
loginAuth=com.cszn.personnel.web.support.auth.LoginAuth

#realm
dbRealm=com.cszn.integrated.base.plugin.shiro.ShiroDbRealm
dbRealm.credentialsMatcher=$credentialsMatcher
dbRealm.authorizationCacheName=shiro-authorizationCache
dbRealm.muitiAuthenticatied=$loginAuth
securityManager.realm=$dbRealm

#session 基于缓存sessionDao，如果缓存已经实现共享，那么session也同样实现共享
sessionDAO=com.cszn.integrated.base.plugin.shiro.SessionDAO
sessionDAO.activeSessionsCacheName=shiro-active-session

#设置sessionCookie
sessionIdCookie=org.apache.shiro.web.servlet.SimpleCookie
sessionIdCookie.name=auid
#sessionIdCookie.domain=127.0.0.1
#sessionIdCookie.path=/
#cookie最大有效期，单位秒，默认30天
sessionIdCookie.maxAge=2592000
sessionIdCookie.httpOnly=true

#设置session会话管理
sessionManager=org.apache.shiro.web.session.mgt.DefaultWebSessionManager
#sessionManager.sessionDAO=$sessionDAO
sessionManager.sessionIdCookie=$sessionIdCookie
sessionManager.sessionIdCookieEnabled=true
sessionManager.sessionIdUrlRewritingEnabled=false
securityManager.sessionManager=$sessionManager
#session过期时间，单位毫秒，默认1天
securityManager.sessionManager.globalSessionTimeout=86400000

webSessionListener=com.cszn.integrated.base.plugin.shiro.WebSessionListener
securityManager.sessionManager.sessionListeners = $webSessionListener

shiro.loginUrl =/login

[filters]
#编码过滤器，处理jboot中使用shiro，post中文乱码问题
character=com.cszn.integrated.base.plugin.shiro.CharacterEncodingFilter

[urls]
/static/**=anon
/captcha=anon
/login=anon
/postLogin=anon
#/favicon.ico=anon

##API
/hystrix.stream = anon
/metrics = anon
/test = anon
/wechat/**  =anon
/api/** = anon
/pers/approval/entryContentH5=anon
/pers/approval/newEntryContentH5=anon
/pers/approval/qualifiedContentH5=anon
/pers/approval/changeDeptContentH5=anon
/pers/approval/quitContentH5=anon
/persOrgEmployee/enclosureIndex=anon
/persOrgEmployee/employeeEnclosurePage=anon
/pers/approval/fillCheckinContentH5=anon
/pers/approval/overTimeContentH5=anon
/pers/approval/leaveRestContentH5=anon
/pers/approval/dispatchContentH5=anon
/pers/approval/changeApplyContentH5=anon
/providentFund/editProvidentFundForm=anon
/socialSecurityApply/editSocialSecurityForm=anon
/liabilityInsurance/editLiabilityInsuranceForm=anon
/pers/approval/businessTripContentH5=anon
/pers/approval/exportDispatchExcel=anon
/pers/approval/exportBusinessTripExcel=anon
/employeeCheckin/empCheckinRecordIndex=anon
/employeeCheckin/empCheckinRecordPage=anon
/pers/approval/orgHeadcountApplyH5=anon
/pers/approval/recruitForm=anon

/**= character,authc
