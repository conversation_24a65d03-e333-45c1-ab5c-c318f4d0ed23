#idArea{
	width: 600px;
	height: 300px;
	position: absolute;
	top: 37px;
	left: 0px;
	z-index: 9999;
	background-color: #F5F5F5;
	border: 1px solid #d6d6d6;
	border-top: none;


}
.tabs {
	height: 28px;
	border-bottom: 2px solid #009688;
	/* background: url(../images/bg_mc_0113_1.png) no-repeat; */
}

.tabs li {
	height: 28px;
	float: left;
}

.tabs li a,.provinceCity .tabs li a:visited {
	display: block;
	height: 28px;
	color: #777777;
	line-height: 28px;
	text-align: center;
	text-decoration: none;
	float: left;
	width: 101px;
	border-right: 1px solid #d6d6d6;
	/* background: url(../images/bg_mc_0113_2.png) right center no-repeat; */
}

.tabs li a.current,.provinceCity .tabs li a.current:visited
{
/* 	url(../static/images/bg_mc_0113_3.png) center center repeat */
	background-color:#009688;
	color: #FFF;
}
.tabs li a:hover{
	cursor:pointer;
}

.list {
	float: left;
	width: 100%;
	margin: auto
}

.list li {
	float: left;
	width: 150px;
	text-align: center;
	overflow: hidden;
	padding: 5px 0
}

.list li a,.list li a:visited
{
	color: #777777
}

.list li a.current,.list li a.current:visited
{
	color: #ff6c00
}
.list li:hover a
{
	color: #fff;
}
.list li:hover
{
	background-color:#009688;
	cursor:pointer;

}
.v-theme .layui-table tbody tr:hover {
    background-color: transparent;
}
.v-theme .layui-table thead tr:hover {
    background-color: #f2f2f2;
}