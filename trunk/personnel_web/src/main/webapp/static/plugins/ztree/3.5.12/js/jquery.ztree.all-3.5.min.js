
/*
 * JQuery zTree core 3.5.12
 * http://zTree.me/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2013-03-11
 */
(function(k){var E,F,G,H,I,J,r={},K={},s={},L={treeId:"",treeObj:null,view:{addDiyDom:null,autoCancelSelected:!0,dblClickExpand:!0,expandSpeed:"fast",fontCss:{},nameIsHTML:!1,selectedMulti:!0,showIcon:!0,showLine:!0,showTitle:!0},data:{key:{children:"children",name:"name",title:"",url:"url"},simpleData:{enable:!1,idKey:"id",pIdKey:"pId",rootPId:null},keep:{parent:!1,leaf:!1}},async:{enable:!1,contentType:"application/x-www-form-urlencoded",type:"post",dataType:"text",url:"",autoParam:[],otherParam:[],
dataFilter:null},callback:{beforeAsync:null,beforeClick:null,beforeDblClick:null,beforeRightClick:null,beforeMouseDown:null,beforeMouseUp:null,beforeExpand:null,beforeCollapse:null,beforeRemove:null,onAsyncError:null,onAsyncSuccess:null,onNodeCreated:null,onClick:null,onDblClick:null,onRightClick:null,onMouseDown:null,onMouseUp:null,onExpand:null,onCollapse:null,onRemove:null}},t=[function(b){var a=b.treeObj,c=e.event;a.bind(c.NODECREATED,function(a,c,g){j.apply(b.callback.onNodeCreated,[a,c,g])});
a.bind(c.CLICK,function(a,c,g,l,h){j.apply(b.callback.onClick,[c,g,l,h])});a.bind(c.EXPAND,function(a,c,g){j.apply(b.callback.onExpand,[a,c,g])});a.bind(c.COLLAPSE,function(a,c,g){j.apply(b.callback.onCollapse,[a,c,g])});a.bind(c.ASYNC_SUCCESS,function(a,c,g,l){j.apply(b.callback.onAsyncSuccess,[a,c,g,l])});a.bind(c.ASYNC_ERROR,function(a,c,g,l,h,e){j.apply(b.callback.onAsyncError,[a,c,g,l,h,e])})}],u=[function(b){var a=e.event;b.treeObj.unbind(a.NODECREATED).unbind(a.CLICK).unbind(a.EXPAND).unbind(a.COLLAPSE).unbind(a.ASYNC_SUCCESS).unbind(a.ASYNC_ERROR)}],
v=[function(b){var a=h.getCache(b);a||(a={},h.setCache(b,a));a.nodes=[];a.doms=[]}],w=[function(b,a,c,d,f,g){if(c){var l=h.getRoot(b),e=b.data.key.children;c.level=a;c.tId=b.treeId+"_"+ ++l.zId;c.parentTId=d?d.tId:null;if(c[e]&&c[e].length>0){if(typeof c.open=="string")c.open=j.eqs(c.open,"true");c.open=!!c.open;c.isParent=!0;c.zAsync=!0}else{c.open=!1;if(typeof c.isParent=="string")c.isParent=j.eqs(c.isParent,"true");c.isParent=!!c.isParent;c.zAsync=!c.isParent}c.isFirstNode=f;c.isLastNode=g;c.getParentNode=
function(){return h.getNodeCache(b,c.parentTId)};c.getPreNode=function(){return h.getPreNode(b,c)};c.getNextNode=function(){return h.getNextNode(b,c)};c.isAjaxing=!1;h.fixPIdKeyValue(b,c)}}],x=[function(b){var a=b.target,c=h.getSetting(b.data.treeId),d="",f=null,g="",l="",i=null,o=null,p=null;if(j.eqs(b.type,"mousedown"))l="mousedown";else if(j.eqs(b.type,"mouseup"))l="mouseup";else if(j.eqs(b.type,"contextmenu"))l="contextmenu";else if(j.eqs(b.type,"click"))if(j.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+
e.id.SWITCH)!==null)d=(k(a).parent("li").get(0)||k(a).parentsUntil("li").parent().get(0)).id,g="switchNode";else{if(p=j.getMDom(c,a,[{tagName:"a",attrName:"treeNode"+e.id.A}]))d=(k(p).parent("li").get(0)||k(p).parentsUntil("li").parent().get(0)).id,g="clickNode"}else if(j.eqs(b.type,"dblclick")&&(l="dblclick",p=j.getMDom(c,a,[{tagName:"a",attrName:"treeNode"+e.id.A}])))d=(k(p).parent("li").get(0)||k(p).parentsUntil("li").parent().get(0)).id,g="switchNode";if(l.length>0&&d.length==0&&(p=j.getMDom(c,
a,[{tagName:"a",attrName:"treeNode"+e.id.A}])))d=(k(p).parent("li").get(0)||k(p).parentsUntil("li").parent().get(0)).id;if(d.length>0)switch(f=h.getNodeCache(c,d),g){case "switchNode":f.isParent?j.eqs(b.type,"click")||j.eqs(b.type,"dblclick")&&j.apply(c.view.dblClickExpand,[c.treeId,f],c.view.dblClickExpand)?i=E:g="":g="";break;case "clickNode":i=F}switch(l){case "mousedown":o=G;break;case "mouseup":o=H;break;case "dblclick":o=I;break;case "contextmenu":o=J}return{stop:!1,node:f,nodeEventType:g,nodeEventCallback:i,
treeEventType:l,treeEventCallback:o}}],y=[function(b){var a=h.getRoot(b);a||(a={},h.setRoot(b,a));a[b.data.key.children]=[];a.expandTriggerFlag=!1;a.curSelectedList=[];a.noSelection=!0;a.createdNodes=[];a.zId=0;a._ver=(new Date).getTime()}],z=[],A=[],B=[],C=[],D=[],h={addNodeCache:function(b,a){h.getCache(b).nodes[h.getNodeCacheId(a.tId)]=a},getNodeCacheId:function(b){return b.substring(b.lastIndexOf("_")+1)},addAfterA:function(b){A.push(b)},addBeforeA:function(b){z.push(b)},addInnerAfterA:function(b){C.push(b)},
addInnerBeforeA:function(b){B.push(b)},addInitBind:function(b){t.push(b)},addInitUnBind:function(b){u.push(b)},addInitCache:function(b){v.push(b)},addInitNode:function(b){w.push(b)},addInitProxy:function(b){x.push(b)},addInitRoot:function(b){y.push(b)},addNodesData:function(b,a,c){var d=b.data.key.children;a[d]||(a[d]=[]);if(a[d].length>0)a[d][a[d].length-1].isLastNode=!1,i.setNodeLineIcos(b,a[d][a[d].length-1]);a.isParent=!0;a[d]=a[d].concat(c)},addSelectedNode:function(b,a){var c=h.getRoot(b);h.isSelectedNode(b,
a)||c.curSelectedList.push(a)},addCreatedNode:function(b,a){(b.callback.onNodeCreated||b.view.addDiyDom)&&h.getRoot(b).createdNodes.push(a)},addZTreeTools:function(b){D.push(b)},exSetting:function(b){k.extend(!0,L,b)},fixPIdKeyValue:function(b,a){b.data.simpleData.enable&&(a[b.data.simpleData.pIdKey]=a.parentTId?a.getParentNode()[b.data.simpleData.idKey]:b.data.simpleData.rootPId)},getAfterA:function(b,a,c){for(var d=0,f=A.length;d<f;d++)A[d].apply(this,arguments)},getBeforeA:function(b,a,c){for(var d=
0,f=z.length;d<f;d++)z[d].apply(this,arguments)},getInnerAfterA:function(b,a,c){for(var d=0,f=C.length;d<f;d++)C[d].apply(this,arguments)},getInnerBeforeA:function(b,a,c){for(var d=0,f=B.length;d<f;d++)B[d].apply(this,arguments)},getCache:function(b){return s[b.treeId]},getNextNode:function(b,a){if(!a)return null;for(var c=b.data.key.children,d=a.parentTId?a.getParentNode():h.getRoot(b),f=0,g=d[c].length-1;f<=g;f++)if(d[c][f]===a)return f==g?null:d[c][f+1];return null},getNodeByParam:function(b,a,
c,d){if(!a||!c)return null;for(var f=b.data.key.children,g=0,l=a.length;g<l;g++){if(a[g][c]==d)return a[g];var e=h.getNodeByParam(b,a[g][f],c,d);if(e)return e}return null},getNodeCache:function(b,a){if(!a)return null;var c=s[b.treeId].nodes[h.getNodeCacheId(a)];return c?c:null},getNodeName:function(b,a){return""+a[b.data.key.name]},getNodeTitle:function(b,a){return""+a[b.data.key.title===""?b.data.key.name:b.data.key.title]},getNodes:function(b){return h.getRoot(b)[b.data.key.children]},getNodesByParam:function(b,
a,c,d){if(!a||!c)return[];for(var f=b.data.key.children,g=[],l=0,e=a.length;l<e;l++)a[l][c]==d&&g.push(a[l]),g=g.concat(h.getNodesByParam(b,a[l][f],c,d));return g},getNodesByParamFuzzy:function(b,a,c,d){if(!a||!c)return[];for(var f=b.data.key.children,g=[],d=d.toLowerCase(),l=0,e=a.length;l<e;l++)typeof a[l][c]=="string"&&a[l][c].toLowerCase().indexOf(d)>-1&&g.push(a[l]),g=g.concat(h.getNodesByParamFuzzy(b,a[l][f],c,d));return g},getNodesByFilter:function(b,a,c,d,f){if(!a)return d?null:[];for(var g=
b.data.key.children,e=d?null:[],i=0,k=a.length;i<k;i++){if(j.apply(c,[a[i],f],!1)){if(d)return a[i];e.push(a[i])}var p=h.getNodesByFilter(b,a[i][g],c,d,f);if(d&&p)return p;e=d?p:e.concat(p)}return e},getPreNode:function(b,a){if(!a)return null;for(var c=b.data.key.children,d=a.parentTId?a.getParentNode():h.getRoot(b),f=0,g=d[c].length;f<g;f++)if(d[c][f]===a)return f==0?null:d[c][f-1];return null},getRoot:function(b){return b?K[b.treeId]:null},getSetting:function(b){return r[b]},getSettings:function(){return r},
getZTreeTools:function(b){return(b=this.getRoot(this.getSetting(b)))?b.treeTools:null},initCache:function(b){for(var a=0,c=v.length;a<c;a++)v[a].apply(this,arguments)},initNode:function(b,a,c,d,f,g){for(var e=0,h=w.length;e<h;e++)w[e].apply(this,arguments)},initRoot:function(b){for(var a=0,c=y.length;a<c;a++)y[a].apply(this,arguments)},isSelectedNode:function(b,a){for(var c=h.getRoot(b),d=0,f=c.curSelectedList.length;d<f;d++)if(a===c.curSelectedList[d])return!0;return!1},removeNodeCache:function(b,
a){var c=b.data.key.children;if(a[c])for(var d=0,f=a[c].length;d<f;d++)arguments.callee(b,a[c][d]);h.getCache(b).nodes[h.getNodeCacheId(a.tId)]=null},removeSelectedNode:function(b,a){for(var c=h.getRoot(b),d=0,f=c.curSelectedList.length;d<f;d++)if(a===c.curSelectedList[d]||!h.getNodeCache(b,c.curSelectedList[d].tId))c.curSelectedList.splice(d,1),d--,f--},setCache:function(b,a){s[b.treeId]=a},setRoot:function(b,a){K[b.treeId]=a},setZTreeTools:function(b,a){for(var c=0,d=D.length;c<d;c++)D[c].apply(this,
arguments)},transformToArrayFormat:function(b,a){if(!a)return[];var c=b.data.key.children,d=[];if(j.isArray(a))for(var f=0,g=a.length;f<g;f++)d.push(a[f]),a[f][c]&&(d=d.concat(h.transformToArrayFormat(b,a[f][c])));else d.push(a),a[c]&&(d=d.concat(h.transformToArrayFormat(b,a[c])));return d},transformTozTreeFormat:function(b,a){var c,d,f=b.data.simpleData.idKey,g=b.data.simpleData.pIdKey,e=b.data.key.children;if(!f||f==""||!a)return[];if(j.isArray(a)){var h=[],i=[];for(c=0,d=a.length;c<d;c++)i[a[c][f]]=
a[c];for(c=0,d=a.length;c<d;c++)i[a[c][g]]&&a[c][f]!=a[c][g]?(i[a[c][g]][e]||(i[a[c][g]][e]=[]),i[a[c][g]][e].push(a[c])):h.push(a[c]);return h}else return[a]}},m={bindEvent:function(b){for(var a=0,c=t.length;a<c;a++)t[a].apply(this,arguments)},unbindEvent:function(b){for(var a=0,c=u.length;a<c;a++)u[a].apply(this,arguments)},bindTree:function(b){var a={treeId:b.treeId},b=b.treeObj;b.bind("selectstart",function(a){a=a.originalEvent.srcElement.nodeName.toLowerCase();return a==="input"||a==="textarea"}).css({"-moz-user-select":"-moz-none"});
b.bind("click",a,m.proxy);b.bind("dblclick",a,m.proxy);b.bind("mouseover",a,m.proxy);b.bind("mouseout",a,m.proxy);b.bind("mousedown",a,m.proxy);b.bind("mouseup",a,m.proxy);b.bind("contextmenu",a,m.proxy)},unbindTree:function(b){b.treeObj.unbind("click",m.proxy).unbind("dblclick",m.proxy).unbind("mouseover",m.proxy).unbind("mouseout",m.proxy).unbind("mousedown",m.proxy).unbind("mouseup",m.proxy).unbind("contextmenu",m.proxy)},doProxy:function(b){for(var a=[],c=0,d=x.length;c<d;c++){var f=x[c].apply(this,
arguments);a.push(f);if(f.stop)break}return a},proxy:function(b){var a=h.getSetting(b.data.treeId);if(!j.uCanDo(a,b))return!0;for(var a=m.doProxy(b),c=!0,d=0,f=a.length;d<f;d++){var g=a[d];g.nodeEventCallback&&(c=g.nodeEventCallback.apply(g,[b,g.node])&&c);g.treeEventCallback&&(c=g.treeEventCallback.apply(g,[b,g.node])&&c)}return c}};E=function(b,a){var c=h.getSetting(b.data.treeId);if(a.open){if(j.apply(c.callback.beforeCollapse,[c.treeId,a],!0)==!1)return!0}else if(j.apply(c.callback.beforeExpand,
[c.treeId,a],!0)==!1)return!0;h.getRoot(c).expandTriggerFlag=!0;i.switchNode(c,a);return!0};F=function(b,a){var c=h.getSetting(b.data.treeId),d=c.view.autoCancelSelected&&b.ctrlKey&&h.isSelectedNode(c,a)?0:c.view.autoCancelSelected&&b.ctrlKey&&c.view.selectedMulti?2:1;if(j.apply(c.callback.beforeClick,[c.treeId,a,d],!0)==!1)return!0;d===0?i.cancelPreSelectedNode(c,a):i.selectNode(c,a,d===2);c.treeObj.trigger(e.event.CLICK,[b,c.treeId,a,d]);return!0};G=function(b,a){var c=h.getSetting(b.data.treeId);
j.apply(c.callback.beforeMouseDown,[c.treeId,a],!0)&&j.apply(c.callback.onMouseDown,[b,c.treeId,a]);return!0};H=function(b,a){var c=h.getSetting(b.data.treeId);j.apply(c.callback.beforeMouseUp,[c.treeId,a],!0)&&j.apply(c.callback.onMouseUp,[b,c.treeId,a]);return!0};I=function(b,a){var c=h.getSetting(b.data.treeId);j.apply(c.callback.beforeDblClick,[c.treeId,a],!0)&&j.apply(c.callback.onDblClick,[b,c.treeId,a]);return!0};J=function(b,a){var c=h.getSetting(b.data.treeId);j.apply(c.callback.beforeRightClick,
[c.treeId,a],!0)&&j.apply(c.callback.onRightClick,[b,c.treeId,a]);return typeof c.callback.onRightClick!="function"};var j={apply:function(b,a,c){return typeof b=="function"?b.apply(M,a?a:[]):c},canAsync:function(b,a){var c=b.data.key.children;return b.async.enable&&a&&a.isParent&&!(a.zAsync||a[c]&&a[c].length>0)},clone:function(b){if(b===null)return null;var a=b.constructor===Array?[]:{},c;for(c in b)a[c]=b[c]instanceof Date?new Date(b[c].getTime()):typeof b[c]==="object"?arguments.callee(b[c]):
b[c];return a},eqs:function(b,a){return b.toLowerCase()===a.toLowerCase()},isArray:function(b){return Object.prototype.toString.apply(b)==="[object Array]"},getMDom:function(b,a,c){if(!a)return null;for(;a&&a.id!==b.treeId;){for(var d=0,f=c.length;a.tagName&&d<f;d++)if(j.eqs(a.tagName,c[d].tagName)&&a.getAttribute(c[d].attrName)!==null)return a;a=a.parentNode}return null},uCanDo:function(){return!0}},i={addNodes:function(b,a,c,d){if(!b.data.keep.leaf||!a||a.isParent)if(j.isArray(c)||(c=[c]),b.data.simpleData.enable&&
(c=h.transformTozTreeFormat(b,c)),a){var f=k("#"+a.tId+e.id.SWITCH),g=k("#"+a.tId+e.id.ICON),l=k("#"+a.tId+e.id.UL);if(!a.open)i.replaceSwitchClass(a,f,e.folder.CLOSE),i.replaceIcoClass(a,g,e.folder.CLOSE),a.open=!1,l.css({display:"none"});h.addNodesData(b,a,c);i.createNodes(b,a.level+1,c,a);d||i.expandCollapseParentNode(b,a,!0)}else h.addNodesData(b,h.getRoot(b),c),i.createNodes(b,0,c,null)},appendNodes:function(b,a,c,d,f,g){if(!c)return[];for(var e=[],j=b.data.key.children,k=0,p=c.length;k<p;k++){var n=
c[k];if(f){var m=(d?d:h.getRoot(b))[j].length==c.length&&k==0;h.initNode(b,a,n,d,m,k==c.length-1,g);h.addNodeCache(b,n)}m=[];n[j]&&n[j].length>0&&(m=i.appendNodes(b,a+1,n[j],n,f,g&&n.open));g&&(i.makeDOMNodeMainBefore(e,b,n),i.makeDOMNodeLine(e,b,n),h.getBeforeA(b,n,e),i.makeDOMNodeNameBefore(e,b,n),h.getInnerBeforeA(b,n,e),i.makeDOMNodeIcon(e,b,n),h.getInnerAfterA(b,n,e),i.makeDOMNodeNameAfter(e,b,n),h.getAfterA(b,n,e),n.isParent&&n.open&&i.makeUlHtml(b,n,e,m.join("")),i.makeDOMNodeMainAfter(e,b,
n),h.addCreatedNode(b,n))}return e},appendParentULDom:function(b,a){var c=[],d=k("#"+a.tId),f=k("#"+a.tId+e.id.UL),g=i.appendNodes(b,a.level+1,a[b.data.key.children],a,!1,!0);i.makeUlHtml(b,a,c,g.join(""));!d.get(0)&&a.parentTId&&(i.appendParentULDom(b,a.getParentNode()),d=k("#"+a.tId));f.get(0)&&f.remove();d.append(c.join(""))},asyncNode:function(b,a,c,d){var f,g;if(a&&!a.isParent)return j.apply(d),!1;else if(a&&a.isAjaxing)return!1;else if(j.apply(b.callback.beforeAsync,[b.treeId,a],!0)==!1)return j.apply(d),
!1;if(a)a.isAjaxing=!0,k("#"+a.tId+e.id.ICON).attr({style:"","class":e.className.BUTTON+" "+e.className.ICO_LOADING});var l={};for(f=0,g=b.async.autoParam.length;a&&f<g;f++){var q=b.async.autoParam[f].split("="),o=q;q.length>1&&(o=q[1],q=q[0]);l[o]=a[q]}if(j.isArray(b.async.otherParam))for(f=0,g=b.async.otherParam.length;f<g;f+=2)l[b.async.otherParam[f]]=b.async.otherParam[f+1];else for(var m in b.async.otherParam)l[m]=b.async.otherParam[m];var n=h.getRoot(b)._ver;k.ajax({contentType:b.async.contentType,
type:b.async.type,url:j.apply(b.async.url,[b.treeId,a],b.async.url),data:l,dataType:b.async.dataType,success:function(f){if(n==h.getRoot(b)._ver){var g=[];try{g=!f||f.length==0?[]:typeof f=="string"?eval("("+f+")"):f}catch(l){g=f}if(a)a.isAjaxing=null,a.zAsync=!0;i.setNodeLineIcos(b,a);g&&g!==""?(g=j.apply(b.async.dataFilter,[b.treeId,a,g],g),i.addNodes(b,a,g?j.clone(g):[],!!c)):i.addNodes(b,a,[],!!c);b.treeObj.trigger(e.event.ASYNC_SUCCESS,[b.treeId,a,f]);j.apply(d)}},error:function(c,d,f){if(n==
h.getRoot(b)._ver){if(a)a.isAjaxing=null;i.setNodeLineIcos(b,a);b.treeObj.trigger(e.event.ASYNC_ERROR,[b.treeId,a,c,d,f])}}});return!0},cancelPreSelectedNode:function(b,a){for(var c=h.getRoot(b).curSelectedList,d=c.length-1;d>=0;d--)if(!a||a===c[d])if(k("#"+c[d].tId+e.id.A).removeClass(e.node.CURSELECTED),a){h.removeSelectedNode(b,a);break}if(!a)h.getRoot(b).curSelectedList=[]},createNodeCallback:function(b){if(b.callback.onNodeCreated||b.view.addDiyDom)for(var a=h.getRoot(b);a.createdNodes.length>
0;){var c=a.createdNodes.shift();j.apply(b.view.addDiyDom,[b.treeId,c]);b.callback.onNodeCreated&&b.treeObj.trigger(e.event.NODECREATED,[b.treeId,c])}},createNodes:function(b,a,c,d){if(c&&c.length!=0){var f=h.getRoot(b),g=b.data.key.children,g=!d||d.open||!!k("#"+d[g][0].tId).get(0);f.createdNodes=[];a=i.appendNodes(b,a,c,d,!0,g);d?(d=k("#"+d.tId+e.id.UL),d.get(0)&&d.append(a.join(""))):b.treeObj.append(a.join(""));i.createNodeCallback(b)}},destroy:function(b){b&&(h.initCache(b),h.initRoot(b),m.unbindTree(b),
m.unbindEvent(b),b.treeObj.empty())},expandCollapseNode:function(b,a,c,d,f){var g=h.getRoot(b),l=b.data.key.children;if(a){if(g.expandTriggerFlag){var q=f,f=function(){q&&q();a.open?b.treeObj.trigger(e.event.EXPAND,[b.treeId,a]):b.treeObj.trigger(e.event.COLLAPSE,[b.treeId,a])};g.expandTriggerFlag=!1}if(!a.open&&a.isParent&&(!k("#"+a.tId+e.id.UL).get(0)||a[l]&&a[l].length>0&&!k("#"+a[l][0].tId).get(0)))i.appendParentULDom(b,a),i.createNodeCallback(b);if(a.open==c)j.apply(f,[]);else{var c=k("#"+a.tId+
e.id.UL),g=k("#"+a.tId+e.id.SWITCH),o=k("#"+a.tId+e.id.ICON);a.isParent?(a.open=!a.open,a.iconOpen&&a.iconClose&&o.attr("style",i.makeNodeIcoStyle(b,a)),a.open?(i.replaceSwitchClass(a,g,e.folder.OPEN),i.replaceIcoClass(a,o,e.folder.OPEN),d==!1||b.view.expandSpeed==""?(c.show(),j.apply(f,[])):a[l]&&a[l].length>0?c.slideDown(b.view.expandSpeed,f):(c.show(),j.apply(f,[]))):(i.replaceSwitchClass(a,g,e.folder.CLOSE),i.replaceIcoClass(a,o,e.folder.CLOSE),d==!1||b.view.expandSpeed==""||!(a[l]&&a[l].length>
0)?(c.hide(),j.apply(f,[])):c.slideUp(b.view.expandSpeed,f))):j.apply(f,[])}}else j.apply(f,[])},expandCollapseParentNode:function(b,a,c,d,f){a&&(a.parentTId?(i.expandCollapseNode(b,a,c,d),a.parentTId&&i.expandCollapseParentNode(b,a.getParentNode(),c,d,f)):i.expandCollapseNode(b,a,c,d,f))},expandCollapseSonNode:function(b,a,c,d,f){var g=h.getRoot(b),e=b.data.key.children,g=a?a[e]:g[e],e=a?!1:d,j=h.getRoot(b).expandTriggerFlag;h.getRoot(b).expandTriggerFlag=!1;if(g)for(var k=0,m=g.length;k<m;k++)g[k]&&
i.expandCollapseSonNode(b,g[k],c,e);h.getRoot(b).expandTriggerFlag=j;i.expandCollapseNode(b,a,c,d,f)},makeDOMNodeIcon:function(b,a,c){var d=h.getNodeName(a,c),d=a.view.nameIsHTML?d:d.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;");b.push("<span id='",c.tId,e.id.ICON,"' title='' treeNode",e.id.ICON," class='",i.makeNodeIcoClass(a,c),"' style='",i.makeNodeIcoStyle(a,c),"'></span><span id='",c.tId,e.id.SPAN,"'>",d,"</span>")},makeDOMNodeLine:function(b,a,c){b.push("<span id='",c.tId,
e.id.SWITCH,"' title='' class='",i.makeNodeLineClass(a,c),"' treeNode",e.id.SWITCH,"></span>")},makeDOMNodeMainAfter:function(b){b.push("</li>")},makeDOMNodeMainBefore:function(b,a,c){b.push("<li id='",c.tId,"' class='",e.className.LEVEL,c.level,"' tabindex='0' hidefocus='true' treenode>")},makeDOMNodeNameAfter:function(b){b.push("</a>")},makeDOMNodeNameBefore:function(b,a,c){var d=h.getNodeTitle(a,c),f=i.makeNodeUrl(a,c),g=i.makeNodeFontCss(a,c),l=[],k;for(k in g)l.push(k,":",g[k],";");b.push("<a id='",
c.tId,e.id.A,"' class='",e.className.LEVEL,c.level,"' treeNode",e.id.A,' onclick="',c.click||"",'" ',f!=null&&f.length>0?"href='"+f+"'":""," target='",i.makeNodeTarget(c),"' style='",l.join(""),"'");j.apply(a.view.showTitle,[a.treeId,c],a.view.showTitle)&&d&&b.push("title='",d.replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;"),"'");b.push(">")},makeNodeFontCss:function(b,a){var c=j.apply(b.view.fontCss,[b.treeId,a],b.view.fontCss);return c&&typeof c!="function"?c:{}},makeNodeIcoClass:function(b,
a){var c=["ico"];a.isAjaxing||(c[0]=(a.iconSkin?a.iconSkin+"_":"")+c[0],a.isParent?c.push(a.open?e.folder.OPEN:e.folder.CLOSE):c.push(e.folder.DOCU));return e.className.BUTTON+" "+c.join("_")},makeNodeIcoStyle:function(b,a){var c=[];if(!a.isAjaxing){var d=a.isParent&&a.iconOpen&&a.iconClose?a.open?a.iconOpen:a.iconClose:a.icon;d&&c.push("background:url(",d,") 0 0 no-repeat;");(b.view.showIcon==!1||!j.apply(b.view.showIcon,[b.treeId,a],!0))&&c.push("width:0px;height:0px;")}return c.join("")},makeNodeLineClass:function(b,
a){var c=[];b.view.showLine?a.level==0&&a.isFirstNode&&a.isLastNode?c.push(e.line.ROOT):a.level==0&&a.isFirstNode?c.push(e.line.ROOTS):a.isLastNode?c.push(e.line.BOTTOM):c.push(e.line.CENTER):c.push(e.line.NOLINE);a.isParent?c.push(a.open?e.folder.OPEN:e.folder.CLOSE):c.push(e.folder.DOCU);return i.makeNodeLineClassEx(a)+c.join("_")},makeNodeLineClassEx:function(b){return e.className.BUTTON+" "+e.className.LEVEL+b.level+" "+e.className.SWITCH+" "},makeNodeTarget:function(b){return b.target||"_blank"},
makeNodeUrl:function(b,a){var c=b.data.key.url;return a[c]?a[c]:null},makeUlHtml:function(b,a,c,d){c.push("<ul id='",a.tId,e.id.UL,"' class='",e.className.LEVEL,a.level," ",i.makeUlLineClass(b,a),"' style='display:",a.open?"block":"none","'>");c.push(d);c.push("</ul>")},makeUlLineClass:function(b,a){return b.view.showLine&&!a.isLastNode?e.line.LINE:""},removeChildNodes:function(b,a){if(a){var c=b.data.key.children,d=a[c];if(d){for(var f=0,g=d.length;f<g;f++)h.removeNodeCache(b,d[f]);h.removeSelectedNode(b);
delete a[c];b.data.keep.parent?k("#"+a.tId+e.id.UL).empty():(a.isParent=!1,a.open=!1,c=k("#"+a.tId+e.id.SWITCH),d=k("#"+a.tId+e.id.ICON),i.replaceSwitchClass(a,c,e.folder.DOCU),i.replaceIcoClass(a,d,e.folder.DOCU),k("#"+a.tId+e.id.UL).remove())}}},setFirstNode:function(b,a){var c=b.data.key.children;if(a[c].length>0)a[c][0].isFirstNode=!0},setLastNode:function(b,a){var c=b.data.key.children,d=a[c].length;if(d>0)a[c][d-1].isLastNode=!0},removeNode:function(b,a){var c=h.getRoot(b),d=b.data.key.children,
f=a.parentTId?a.getParentNode():c;a.isFirstNode=!1;a.isLastNode=!1;a.getPreNode=function(){return null};a.getNextNode=function(){return null};if(h.getNodeCache(b,a.tId)){k("#"+a.tId).remove();h.removeNodeCache(b,a);h.removeSelectedNode(b,a);for(var g=0,l=f[d].length;g<l;g++)if(f[d][g].tId==a.tId){f[d].splice(g,1);break}i.setFirstNode(b,f);i.setLastNode(b,f);var j,g=f[d].length;if(!b.data.keep.parent&&g==0)f.isParent=!1,f.open=!1,g=k("#"+f.tId+e.id.UL),l=k("#"+f.tId+e.id.SWITCH),j=k("#"+f.tId+e.id.ICON),
i.replaceSwitchClass(f,l,e.folder.DOCU),i.replaceIcoClass(f,j,e.folder.DOCU),g.css("display","none");else if(b.view.showLine&&g>0){var o=f[d][g-1],g=k("#"+o.tId+e.id.UL),l=k("#"+o.tId+e.id.SWITCH);j=k("#"+o.tId+e.id.ICON);f==c?f[d].length==1?i.replaceSwitchClass(o,l,e.line.ROOT):(c=k("#"+f[d][0].tId+e.id.SWITCH),i.replaceSwitchClass(f[d][0],c,e.line.ROOTS),i.replaceSwitchClass(o,l,e.line.BOTTOM)):i.replaceSwitchClass(o,l,e.line.BOTTOM);g.removeClass(e.line.LINE)}}},replaceIcoClass:function(b,a,c){if(a&&
!b.isAjaxing&&(b=a.attr("class"),b!=void 0)){b=b.split("_");switch(c){case e.folder.OPEN:case e.folder.CLOSE:case e.folder.DOCU:b[b.length-1]=c}a.attr("class",b.join("_"))}},replaceSwitchClass:function(b,a,c){if(a){var d=a.attr("class");if(d!=void 0){d=d.split("_");switch(c){case e.line.ROOT:case e.line.ROOTS:case e.line.CENTER:case e.line.BOTTOM:case e.line.NOLINE:d[0]=i.makeNodeLineClassEx(b)+c;break;case e.folder.OPEN:case e.folder.CLOSE:case e.folder.DOCU:d[1]=c}a.attr("class",d.join("_"));c!==
e.folder.DOCU?a.removeAttr("disabled"):a.attr("disabled","disabled")}}},selectNode:function(b,a,c){c||i.cancelPreSelectedNode(b);k("#"+a.tId+e.id.A).addClass(e.node.CURSELECTED);h.addSelectedNode(b,a)},setNodeFontCss:function(b,a){var c=k("#"+a.tId+e.id.A),d=i.makeNodeFontCss(b,a);d&&c.css(d)},setNodeLineIcos:function(b,a){if(a){var c=k("#"+a.tId+e.id.SWITCH),d=k("#"+a.tId+e.id.UL),f=k("#"+a.tId+e.id.ICON),g=i.makeUlLineClass(b,a);g.length==0?d.removeClass(e.line.LINE):d.addClass(g);c.attr("class",
i.makeNodeLineClass(b,a));a.isParent?c.removeAttr("disabled"):c.attr("disabled","disabled");f.removeAttr("style");f.attr("style",i.makeNodeIcoStyle(b,a));f.attr("class",i.makeNodeIcoClass(b,a))}},setNodeName:function(b,a){var c=h.getNodeTitle(b,a),d=k("#"+a.tId+e.id.SPAN);d.empty();b.view.nameIsHTML?d.html(h.getNodeName(b,a)):d.text(h.getNodeName(b,a));j.apply(b.view.showTitle,[b.treeId,a],b.view.showTitle)&&k("#"+a.tId+e.id.A).attr("title",!c?"":c)},setNodeTarget:function(b){k("#"+b.tId+e.id.A).attr("target",
i.makeNodeTarget(b))},setNodeUrl:function(b,a){var c=k("#"+a.tId+e.id.A),d=i.makeNodeUrl(b,a);d==null||d.length==0?c.removeAttr("href"):c.attr("href",d)},switchNode:function(b,a){a.open||!j.canAsync(b,a)?i.expandCollapseNode(b,a,!a.open):b.async.enable?i.asyncNode(b,a)||i.expandCollapseNode(b,a,!a.open):a&&i.expandCollapseNode(b,a,!a.open)}};k.fn.zTree={consts:{className:{BUTTON:"button",LEVEL:"level",ICO_LOADING:"ico_loading",SWITCH:"switch"},event:{NODECREATED:"ztree_nodeCreated",CLICK:"ztree_click",
EXPAND:"ztree_expand",COLLAPSE:"ztree_collapse",ASYNC_SUCCESS:"ztree_async_success",ASYNC_ERROR:"ztree_async_error"},id:{A:"_a",ICON:"_ico",SPAN:"_span",SWITCH:"_switch",UL:"_ul"},line:{ROOT:"root",ROOTS:"roots",CENTER:"center",BOTTOM:"bottom",NOLINE:"noline",LINE:"line"},folder:{OPEN:"open",CLOSE:"close",DOCU:"docu"},node:{CURSELECTED:"curSelectedNode"}},_z:{tools:j,view:i,event:m,data:h},getZTreeObj:function(b){return(b=h.getZTreeTools(b))?b:null},destroy:function(b){if(b&&b.length>0)i.destroy(h.getSetting(b));
else for(var a in r)i.destroy(r[a])},init:function(b,a,c){var d=j.clone(L);k.extend(!0,d,a);d.treeId=b.attr("id");d.treeObj=b;d.treeObj.empty();r[d.treeId]=d;if(typeof document.body.style.maxHeight==="undefined")d.view.expandSpeed="";h.initRoot(d);b=h.getRoot(d);a=d.data.key.children;c=c?j.clone(j.isArray(c)?c:[c]):[];b[a]=d.data.simpleData.enable?h.transformTozTreeFormat(d,c):c;h.initCache(d);m.unbindTree(d);m.bindTree(d);m.unbindEvent(d);m.bindEvent(d);c={setting:d,addNodes:function(a,b,c){function e(){i.addNodes(d,
a,h,c==!0)}if(!b)return null;a||(a=null);if(a&&!a.isParent&&d.data.keep.leaf)return null;var h=j.clone(j.isArray(b)?b:[b]);j.canAsync(d,a)?i.asyncNode(d,a,c,e):e();return h},cancelSelectedNode:function(a){i.cancelPreSelectedNode(this.setting,a)},destroy:function(){i.destroy(this.setting)},expandAll:function(a){a=!!a;i.expandCollapseSonNode(this.setting,null,a,!0);return a},expandNode:function(a,b,c,e,m){if(!a||!a.isParent)return null;b!==!0&&b!==!1&&(b=!a.open);if((m=!!m)&&b&&j.apply(d.callback.beforeExpand,
[d.treeId,a],!0)==!1)return null;else if(m&&!b&&j.apply(d.callback.beforeCollapse,[d.treeId,a],!0)==!1)return null;b&&a.parentTId&&i.expandCollapseParentNode(this.setting,a.getParentNode(),b,!1);if(b===a.open&&!c)return null;h.getRoot(d).expandTriggerFlag=m;if(c)i.expandCollapseSonNode(this.setting,a,b,!0,function(){if(e!==!1)try{k("#"+a.tId).focus().blur()}catch(b){}});else if(a.open=!b,i.switchNode(this.setting,a),e!==!1)try{k("#"+a.tId).focus().blur()}catch(p){}return b},getNodes:function(){return h.getNodes(this.setting)},
getNodeByParam:function(a,b,c){return!a?null:h.getNodeByParam(this.setting,c?c[this.setting.data.key.children]:h.getNodes(this.setting),a,b)},getNodeByTId:function(a){return h.getNodeCache(this.setting,a)},getNodesByParam:function(a,b,c){return!a?null:h.getNodesByParam(this.setting,c?c[this.setting.data.key.children]:h.getNodes(this.setting),a,b)},getNodesByParamFuzzy:function(a,b,c){return!a?null:h.getNodesByParamFuzzy(this.setting,c?c[this.setting.data.key.children]:h.getNodes(this.setting),a,b)},
getNodesByFilter:function(a,b,c,d){b=!!b;return!a||typeof a!="function"?b?null:[]:h.getNodesByFilter(this.setting,c?c[this.setting.data.key.children]:h.getNodes(this.setting),a,b,d)},getNodeIndex:function(a){if(!a)return null;for(var b=d.data.key.children,c=a.parentTId?a.getParentNode():h.getRoot(this.setting),e=0,i=c[b].length;e<i;e++)if(c[b][e]==a)return e;return-1},getSelectedNodes:function(){for(var a=[],b=h.getRoot(this.setting).curSelectedList,c=0,d=b.length;c<d;c++)a.push(b[c]);return a},isSelectedNode:function(a){return h.isSelectedNode(this.setting,
a)},reAsyncChildNodes:function(a,b,c){if(this.setting.async.enable){var j=!a;j&&(a=h.getRoot(this.setting));if(b=="refresh"){for(var b=this.setting.data.key.children,m=0,p=a[b]?a[b].length:0;m<p;m++)h.removeNodeCache(d,a[b][m]);h.removeSelectedNode(d);a[b]=[];j?this.setting.treeObj.empty():k("#"+a.tId+e.id.UL).empty()}i.asyncNode(this.setting,j?null:a,!!c)}},refresh:function(){this.setting.treeObj.empty();var a=h.getRoot(this.setting),b=a[this.setting.data.key.children];h.initRoot(this.setting);a[this.setting.data.key.children]=
b;h.initCache(this.setting);i.createNodes(this.setting,0,a[this.setting.data.key.children])},removeChildNodes:function(a){if(!a)return null;var b=a[d.data.key.children];i.removeChildNodes(d,a);return b?b:null},removeNode:function(a,b){a&&(b=!!b,b&&j.apply(d.callback.beforeRemove,[d.treeId,a],!0)==!1||(i.removeNode(d,a),b&&this.setting.treeObj.trigger(e.event.REMOVE,[d.treeId,a])))},selectNode:function(a,b){if(a&&j.uCanDo(this.setting)){b=d.view.selectedMulti&&b;if(a.parentTId)i.expandCollapseParentNode(this.setting,
a.getParentNode(),!0,!1,function(){try{k("#"+a.tId).focus().blur()}catch(b){}});else try{k("#"+a.tId).focus().blur()}catch(c){}i.selectNode(this.setting,a,b)}},transformTozTreeNodes:function(a){return h.transformTozTreeFormat(this.setting,a)},transformToArray:function(a){return h.transformToArrayFormat(this.setting,a)},updateNode:function(a){a&&k("#"+a.tId).get(0)&&j.uCanDo(this.setting)&&(i.setNodeName(this.setting,a),i.setNodeTarget(a),i.setNodeUrl(this.setting,a),i.setNodeLineIcos(this.setting,
a),i.setNodeFontCss(this.setting,a))}};b.treeTools=c;h.setZTreeTools(d,c);b[a]&&b[a].length>0?i.createNodes(d,0,b[a]):d.async.enable&&d.async.url&&d.async.url!==""&&i.asyncNode(d);return c}};var M=k.fn.zTree,e=M.consts})(jQuery);

/*
 * JQuery zTree excheck 3.5.12
 * http://zTree.me/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2013-03-11
 */
(function(m){var p,q,r,n={event:{CHECK:"ztree_check"},id:{CHECK:"_check"},checkbox:{STYLE:"checkbox",DEFAULT:"chk",DISABLED:"disable",FALSE:"false",TRUE:"true",FULL:"full",PART:"part",FOCUS:"focus"},radio:{STYLE:"radio",TYPE_ALL:"all",TYPE_LEVEL:"level"}},v={check:{enable:!1,autoCheckTrigger:!1,chkStyle:n.checkbox.STYLE,nocheckInherit:!1,chkDisabledInherit:!1,radioType:n.radio.TYPE_LEVEL,chkboxType:{Y:"ps",N:"ps"}},data:{key:{checked:"checked"}},callback:{beforeCheck:null,onCheck:null}};p=function(c,
a){if(a.chkDisabled===!0)return!1;var b=g.getSetting(c.data.treeId),d=b.data.key.checked;if(l.apply(b.callback.beforeCheck,[b.treeId,a],!0)==!1)return!0;a[d]=!a[d];e.checkNodeRelation(b,a);d=m("#"+a.tId+i.id.CHECK);e.setChkClass(b,d,a);e.repairParentChkClassWithSelf(b,a);b.treeObj.trigger(i.event.CHECK,[c,b.treeId,a]);return!0};q=function(c,a){if(a.chkDisabled===!0)return!1;var b=g.getSetting(c.data.treeId),d=m("#"+a.tId+i.id.CHECK);a.check_Focus=!0;e.setChkClass(b,d,a);return!0};r=function(c,a){if(a.chkDisabled===
!0)return!1;var b=g.getSetting(c.data.treeId),d=m("#"+a.tId+i.id.CHECK);a.check_Focus=!1;e.setChkClass(b,d,a);return!0};m.extend(!0,m.fn.zTree.consts,n);m.extend(!0,m.fn.zTree._z,{tools:{},view:{checkNodeRelation:function(c,a){var b,d,h,k=c.data.key.children,j=c.data.key.checked;b=i.radio;if(c.check.chkStyle==b.STYLE){var f=g.getRadioCheckedList(c);if(a[j])if(c.check.radioType==b.TYPE_ALL){for(d=f.length-1;d>=0;d--)b=f[d],b[j]=!1,f.splice(d,1),e.setChkClass(c,m("#"+b.tId+i.id.CHECK),b),b.parentTId!=
a.parentTId&&e.repairParentChkClassWithSelf(c,b);f.push(a)}else{f=a.parentTId?a.getParentNode():g.getRoot(c);for(d=0,h=f[k].length;d<h;d++)b=f[k][d],b[j]&&b!=a&&(b[j]=!1,e.setChkClass(c,m("#"+b.tId+i.id.CHECK),b))}else if(c.check.radioType==b.TYPE_ALL)for(d=0,h=f.length;d<h;d++)if(a==f[d]){f.splice(d,1);break}}else a[j]&&(!a[k]||a[k].length==0||c.check.chkboxType.Y.indexOf("s")>-1)&&e.setSonNodeCheckBox(c,a,!0),!a[j]&&(!a[k]||a[k].length==0||c.check.chkboxType.N.indexOf("s")>-1)&&e.setSonNodeCheckBox(c,
a,!1),a[j]&&c.check.chkboxType.Y.indexOf("p")>-1&&e.setParentNodeCheckBox(c,a,!0),!a[j]&&c.check.chkboxType.N.indexOf("p")>-1&&e.setParentNodeCheckBox(c,a,!1)},makeChkClass:function(c,a){var b=c.data.key.checked,d=i.checkbox,h=i.radio,k="",k=a.chkDisabled===!0?d.DISABLED:a.halfCheck?d.PART:c.check.chkStyle==h.STYLE?a.check_Child_State<1?d.FULL:d.PART:a[b]?a.check_Child_State===2||a.check_Child_State===-1?d.FULL:d.PART:a.check_Child_State<1?d.FULL:d.PART,b=c.check.chkStyle+"_"+(a[b]?d.TRUE:d.FALSE)+
"_"+k,b=a.check_Focus&&a.chkDisabled!==!0?b+"_"+d.FOCUS:b;return i.className.BUTTON+" "+d.DEFAULT+" "+b},repairAllChk:function(c,a){if(c.check.enable&&c.check.chkStyle===i.checkbox.STYLE)for(var b=c.data.key.checked,d=c.data.key.children,h=g.getRoot(c),k=0,j=h[d].length;k<j;k++){var f=h[d][k];f.nocheck!==!0&&f.chkDisabled!==!0&&(f[b]=a);e.setSonNodeCheckBox(c,f,a)}},repairChkClass:function(c,a){if(a&&(g.makeChkFlag(c,a),a.nocheck!==!0)){var b=m("#"+a.tId+i.id.CHECK);e.setChkClass(c,b,a)}},repairParentChkClass:function(c,
a){if(a&&a.parentTId){var b=a.getParentNode();e.repairChkClass(c,b);e.repairParentChkClass(c,b)}},repairParentChkClassWithSelf:function(c,a){if(a){var b=c.data.key.children;a[b]&&a[b].length>0?e.repairParentChkClass(c,a[b][0]):e.repairParentChkClass(c,a)}},repairSonChkDisabled:function(c,a,b,d){if(a){var h=c.data.key.children;if(a.chkDisabled!=b)a.chkDisabled=b;e.repairChkClass(c,a);if(a[h]&&d)for(var k=0,j=a[h].length;k<j;k++)e.repairSonChkDisabled(c,a[h][k],b,d)}},repairParentChkDisabled:function(c,
a,b,d){if(a){if(a.chkDisabled!=b&&d)a.chkDisabled=b;e.repairChkClass(c,a);e.repairParentChkDisabled(c,a.getParentNode(),b,d)}},setChkClass:function(c,a,b){a&&(b.nocheck===!0?a.hide():a.show(),a.removeClass(),a.addClass(e.makeChkClass(c,b)))},setParentNodeCheckBox:function(c,a,b,d){var h=c.data.key.children,k=c.data.key.checked,j=m("#"+a.tId+i.id.CHECK);d||(d=a);g.makeChkFlag(c,a);a.nocheck!==!0&&a.chkDisabled!==!0&&(a[k]=b,e.setChkClass(c,j,a),c.check.autoCheckTrigger&&a!=d&&c.treeObj.trigger(i.event.CHECK,
[null,c.treeId,a]));if(a.parentTId){j=!0;if(!b)for(var h=a.getParentNode()[h],f=0,o=h.length;f<o;f++)if(h[f].nocheck!==!0&&h[f].chkDisabled!==!0&&h[f][k]||(h[f].nocheck===!0||h[f].chkDisabled===!0)&&h[f].check_Child_State>0){j=!1;break}j&&e.setParentNodeCheckBox(c,a.getParentNode(),b,d)}},setSonNodeCheckBox:function(c,a,b,d){if(a){var h=c.data.key.children,k=c.data.key.checked,j=m("#"+a.tId+i.id.CHECK);d||(d=a);var f=!1;if(a[h])for(var o=0,l=a[h].length;o<l&&a.chkDisabled!==!0;o++){var n=a[h][o];
e.setSonNodeCheckBox(c,n,b,d);n.chkDisabled===!0&&(f=!0)}if(a!=g.getRoot(c)&&a.chkDisabled!==!0){f&&a.nocheck!==!0&&g.makeChkFlag(c,a);if(a.nocheck!==!0&&a.chkDisabled!==!0){if(a[k]=b,!f)a.check_Child_State=a[h]&&a[h].length>0?b?2:0:-1}else a.check_Child_State=-1;e.setChkClass(c,j,a);c.check.autoCheckTrigger&&a!=d&&a.nocheck!==!0&&a.chkDisabled!==!0&&c.treeObj.trigger(i.event.CHECK,[null,c.treeId,a])}}}},event:{},data:{getRadioCheckedList:function(c){for(var a=g.getRoot(c).radioCheckedList,b=0,d=
a.length;b<d;b++)g.getNodeCache(c,a[b].tId)||(a.splice(b,1),b--,d--);return a},getCheckStatus:function(c,a){if(!c.check.enable||a.nocheck||a.chkDisabled)return null;var b=c.data.key.checked;return{checked:a[b],half:a.halfCheck?a.halfCheck:c.check.chkStyle==i.radio.STYLE?a.check_Child_State===2:a[b]?a.check_Child_State>-1&&a.check_Child_State<2:a.check_Child_State>0}},getTreeCheckedNodes:function(c,a,b,d){if(!a)return[];for(var h=c.data.key.children,k=c.data.key.checked,e=b&&c.check.chkStyle==i.radio.STYLE&&
c.check.radioType==i.radio.TYPE_ALL,d=!d?[]:d,f=0,o=a.length;f<o;f++){if(a[f].nocheck!==!0&&a[f].chkDisabled!==!0&&a[f][k]==b&&(d.push(a[f]),e))break;g.getTreeCheckedNodes(c,a[f][h],b,d);if(e&&d.length>0)break}return d},getTreeChangeCheckedNodes:function(c,a,b){if(!a)return[];for(var d=c.data.key.children,h=c.data.key.checked,b=!b?[]:b,k=0,e=a.length;k<e;k++)a[k].nocheck!==!0&&a[k].chkDisabled!==!0&&a[k][h]!=a[k].checkedOld&&b.push(a[k]),g.getTreeChangeCheckedNodes(c,a[k][d],b);return b},makeChkFlag:function(c,
a){if(a){var b=c.data.key.children,d=c.data.key.checked,h=-1;if(a[b])for(var e=0,j=a[b].length;e<j;e++){var f=a[b][e],g=-1;if(c.check.chkStyle==i.radio.STYLE)if(g=f.nocheck===!0||f.chkDisabled===!0?f.check_Child_State:f.halfCheck===!0?2:f[d]?2:f.check_Child_State>0?2:0,g==2){h=2;break}else g==0&&(h=0);else if(c.check.chkStyle==i.checkbox.STYLE)if(g=f.nocheck===!0||f.chkDisabled===!0?f.check_Child_State:f.halfCheck===!0?1:f[d]?f.check_Child_State===-1||f.check_Child_State===2?2:1:f.check_Child_State>
0?1:0,g===1){h=1;break}else if(g===2&&h>-1&&e>0&&g!==h){h=1;break}else if(h===2&&g>-1&&g<2){h=1;break}else g>-1&&(h=g)}a.check_Child_State=h}}}});var n=m.fn.zTree,l=n._z.tools,i=n.consts,e=n._z.view,g=n._z.data;g.exSetting(v);g.addInitBind(function(c){c.treeObj.bind(i.event.CHECK,function(a,b,d,h){l.apply(c.callback.onCheck,[b?b:a,d,h])})});g.addInitUnBind(function(c){c.treeObj.unbind(i.event.CHECK)});g.addInitCache(function(){});g.addInitNode(function(c,a,b,d){if(b){a=c.data.key.checked;typeof b[a]==
"string"&&(b[a]=l.eqs(b[a],"true"));b[a]=!!b[a];b.checkedOld=b[a];if(typeof b.nocheck=="string")b.nocheck=l.eqs(b.nocheck,"true");b.nocheck=!!b.nocheck||c.check.nocheckInherit&&d&&!!d.nocheck;if(typeof b.chkDisabled=="string")b.chkDisabled=l.eqs(b.chkDisabled,"true");b.chkDisabled=!!b.chkDisabled||c.check.chkDisabledInherit&&d&&!!d.chkDisabled;if(typeof b.halfCheck=="string")b.halfCheck=l.eqs(b.halfCheck,"true");b.halfCheck=!!b.halfCheck;b.check_Child_State=-1;b.check_Focus=!1;b.getCheckStatus=function(){return g.getCheckStatus(c,
b)}}});g.addInitProxy(function(c){var a=c.target,b=g.getSetting(c.data.treeId),d="",h=null,e="",j=null;if(l.eqs(c.type,"mouseover")){if(b.check.enable&&l.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+i.id.CHECK)!==null)d=a.parentNode.id,e="mouseoverCheck"}else if(l.eqs(c.type,"mouseout")){if(b.check.enable&&l.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+i.id.CHECK)!==null)d=a.parentNode.id,e="mouseoutCheck"}else if(l.eqs(c.type,"click")&&b.check.enable&&l.eqs(a.tagName,"span")&&a.getAttribute("treeNode"+
i.id.CHECK)!==null)d=a.parentNode.id,e="checkNode";if(d.length>0)switch(h=g.getNodeCache(b,d),e){case "checkNode":j=p;break;case "mouseoverCheck":j=q;break;case "mouseoutCheck":j=r}return{stop:!1,node:h,nodeEventType:e,nodeEventCallback:j,treeEventType:"",treeEventCallback:null}});g.addInitRoot(function(c){g.getRoot(c).radioCheckedList=[]});g.addBeforeA(function(c,a,b){var d=c.data.key.checked;c.check.enable&&(g.makeChkFlag(c,a),c.check.chkStyle==i.radio.STYLE&&c.check.radioType==i.radio.TYPE_ALL&&
a[d]&&g.getRoot(c).radioCheckedList.push(a),b.push("<span ID='",a.tId,i.id.CHECK,"' class='",e.makeChkClass(c,a),"' treeNode",i.id.CHECK,a.nocheck===!0?" style='display:none;'":"","></span>"))});g.addZTreeTools(function(c,a){a.checkNode=function(a,b,g,j){var f=this.setting.data.key.checked;if(a.chkDisabled!==!0&&(b!==!0&&b!==!1&&(b=!a[f]),j=!!j,(a[f]!==b||g)&&!(j&&l.apply(this.setting.callback.beforeCheck,[this.setting.treeId,a],!0)==!1)&&l.uCanDo(this.setting)&&this.setting.check.enable&&a.nocheck!==
!0))a[f]=b,b=m("#"+a.tId+i.id.CHECK),(g||this.setting.check.chkStyle===i.radio.STYLE)&&e.checkNodeRelation(this.setting,a),e.setChkClass(this.setting,b,a),e.repairParentChkClassWithSelf(this.setting,a),j&&c.treeObj.trigger(i.event.CHECK,[null,c.treeId,a])};a.checkAllNodes=function(a){e.repairAllChk(this.setting,!!a)};a.getCheckedNodes=function(a){var b=this.setting.data.key.children;return g.getTreeCheckedNodes(this.setting,g.getRoot(c)[b],a!==!1)};a.getChangeCheckedNodes=function(){var a=this.setting.data.key.children;
return g.getTreeChangeCheckedNodes(this.setting,g.getRoot(c)[a])};a.setChkDisabled=function(a,b,c,g){b=!!b;c=!!c;e.repairSonChkDisabled(this.setting,a,b,!!g);e.repairParentChkDisabled(this.setting,a.getParentNode(),b,c)};var b=a.updateNode;a.updateNode=function(c,h){b&&b.apply(a,arguments);if(c&&this.setting.check.enable&&m("#"+c.tId).get(0)&&l.uCanDo(this.setting)){var g=m("#"+c.tId+i.id.CHECK);(h==!0||this.setting.check.chkStyle===i.radio.STYLE)&&e.checkNodeRelation(this.setting,c);e.setChkClass(this.setting,
g,c);e.repairParentChkClassWithSelf(this.setting,c)}}});var s=e.createNodes;e.createNodes=function(c,a,b,d){s&&s.apply(e,arguments);b&&e.repairParentChkClassWithSelf(c,d)};var t=e.removeNode;e.removeNode=function(c,a){var b=a.getParentNode();t&&t.apply(e,arguments);a&&b&&(e.repairChkClass(c,b),e.repairParentChkClass(c,b))};var u=e.appendNodes;e.appendNodes=function(c,a,b,d,h,i){var j="";u&&(j=u.apply(e,arguments));d&&g.makeChkFlag(c,d);return j}})(jQuery);

/*
 * JQuery zTree exedit 3.5.12
 * http://zTree.me/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2013-03-11
 */
(function(k){var F={event:{DRAG:"ztree_drag",DROP:"ztree_drop",REMOVE:"ztree_remove",RENAME:"ztree_rename"},id:{EDIT:"_edit",INPUT:"_input",REMOVE:"_remove"},move:{TYPE_INNER:"inner",TYPE_PREV:"prev",TYPE_NEXT:"next"},node:{CURSELECTED_EDIT:"curSelectedNode_Edit",TMPTARGET_TREE:"tmpTargetzTree",TMPTARGET_NODE:"tmpTargetNode"}},D={onHoverOverNode:function(b,a){var c=p.getSetting(b.data.treeId),d=p.getRoot(c);if(d.curHoverNode!=a)D.onHoverOutNode(b);d.curHoverNode=a;e.addHoverDom(c,a)},onHoverOutNode:function(b){var b=
p.getSetting(b.data.treeId),a=p.getRoot(b);if(a.curHoverNode&&!p.isSelectedNode(b,a.curHoverNode))e.removeTreeDom(b,a.curHoverNode),a.curHoverNode=null},onMousedownNode:function(b,a){function c(b){if(z.dragFlag==0&&Math.abs(K-b.clientX)<g.edit.drag.minMoveSize&&Math.abs(L-b.clientY)<g.edit.drag.minMoveSize)return!0;var a,c,f,j,l;l=g.data.key.children;k("body").css("cursor","pointer");if(z.dragFlag==0){if(h.apply(g.callback.beforeDrag,[g.treeId,m],!0)==!1)return q(b),!0;for(a=0,c=m.length;a<c;a++){if(a==
0)z.dragNodeShowBefore=[];f=m[a];f.isParent&&f.open?(e.expandCollapseNode(g,f,!f.open),z.dragNodeShowBefore[f.tId]=!0):z.dragNodeShowBefore[f.tId]=!1}z.dragFlag=1;z.showHoverDom=!1;h.showIfameMask(g,!0);f=!0;j=-1;if(m.length>1){var s=m[0].parentTId?m[0].getParentNode()[l]:p.getNodes(g);l=[];for(a=0,c=s.length;a<c;a++)if(z.dragNodeShowBefore[s[a].tId]!==void 0&&(f&&j>-1&&j+1!==a&&(f=!1),l.push(s[a]),j=a),m.length===l.length){m=l;break}}f&&(D=m[0].getPreNode(),E=m[m.length-1].getNextNode());y=k("<ul class='zTreeDragUL'></ul>");
for(a=0,c=m.length;a<c;a++)if(f=m[a],f.editNameFlag=!1,e.selectNode(g,f,a>0),e.removeTreeDom(g,f),j=k("<li id='"+f.tId+"_tmp'></li>"),j.append(k("#"+f.tId+d.id.A).clone()),j.css("padding","0"),j.children("#"+f.tId+d.id.A).removeClass(d.node.CURSELECTED),y.append(j),a==g.edit.drag.maxShowNodeNum-1){j=k("<li id='"+f.tId+"_moretmp'><a>  ...  </a></li>");y.append(j);break}y.attr("id",m[0].tId+d.id.UL+"_tmp");y.addClass(g.treeObj.attr("class"));y.appendTo("body");t=k("<span class='tmpzTreeMove_arrow'></span>");
t.attr("id","zTreeMove_arrow_tmp");t.appendTo("body");g.treeObj.trigger(d.event.DRAG,[b,g.treeId,m])}if(z.dragFlag==1){r&&t.attr("id")==b.target.id&&u&&b.clientX+x.scrollLeft()+2>k("#"+u+d.id.A,r).offset().left?(f=k("#"+u+d.id.A,r),b.target=f.length>0?f.get(0):b.target):r&&(r.removeClass(d.node.TMPTARGET_TREE),u&&k("#"+u+d.id.A,r).removeClass(d.node.TMPTARGET_NODE+"_"+d.move.TYPE_PREV).removeClass(d.node.TMPTARGET_NODE+"_"+F.move.TYPE_NEXT).removeClass(d.node.TMPTARGET_NODE+"_"+F.move.TYPE_INNER));
u=r=null;G=!1;i=g;f=p.getSettings();for(var B in f)if(f[B].treeId&&f[B].edit.enable&&f[B].treeId!=g.treeId&&(b.target.id==f[B].treeId||k(b.target).parents("#"+f[B].treeId).length>0))G=!0,i=f[B];B=x.scrollTop();j=x.scrollLeft();l=i.treeObj.offset();a=i.treeObj.get(0).scrollHeight;f=i.treeObj.get(0).scrollWidth;c=b.clientY+B-l.top;var o=i.treeObj.height()+l.top-b.clientY-B,n=b.clientX+j-l.left,H=i.treeObj.width()+l.left-b.clientX-j;l=c<g.edit.drag.borderMax&&c>g.edit.drag.borderMin;var s=o<g.edit.drag.borderMax&&
o>g.edit.drag.borderMin,I=n<g.edit.drag.borderMax&&n>g.edit.drag.borderMin,C=H<g.edit.drag.borderMax&&H>g.edit.drag.borderMin,o=c>g.edit.drag.borderMin&&o>g.edit.drag.borderMin&&n>g.edit.drag.borderMin&&H>g.edit.drag.borderMin,n=l&&i.treeObj.scrollTop()<=0,H=s&&i.treeObj.scrollTop()+i.treeObj.height()+10>=a,M=I&&i.treeObj.scrollLeft()<=0,N=C&&i.treeObj.scrollLeft()+i.treeObj.width()+10>=f;if(b.target.id&&i.treeObj.find("#"+b.target.id).length>0){for(var A=b.target;A&&A.tagName&&!h.eqs(A.tagName,"li")&&
A.id!=i.treeId;)A=A.parentNode;var O=!0;for(a=0,c=m.length;a<c;a++)if(f=m[a],A.id===f.tId){O=!1;break}else if(k("#"+f.tId).find("#"+A.id).length>0){O=!1;break}if(O&&b.target.id&&(b.target.id==A.id+d.id.A||k(b.target).parents("#"+A.id+d.id.A).length>0))r=k(A),u=A.id}f=m[0];if(o&&(b.target.id==i.treeId||k(b.target).parents("#"+i.treeId).length>0)){if(!r&&(b.target.id==i.treeId||n||H||M||N)&&(G||!G&&f.parentTId))r=i.treeObj;l?i.treeObj.scrollTop(i.treeObj.scrollTop()-10):s&&i.treeObj.scrollTop(i.treeObj.scrollTop()+
10);I?i.treeObj.scrollLeft(i.treeObj.scrollLeft()-10):C&&i.treeObj.scrollLeft(i.treeObj.scrollLeft()+10);r&&r!=i.treeObj&&r.offset().left<i.treeObj.offset().left&&i.treeObj.scrollLeft(i.treeObj.scrollLeft()+r.offset().left-i.treeObj.offset().left)}y.css({top:b.clientY+B+3+"px",left:b.clientX+j+3+"px"});l=a=0;if(r&&r.attr("id")!=i.treeId){var w=u==null?null:p.getNodeCache(i,u);c=b.ctrlKey&&g.edit.drag.isMove&&g.edit.drag.isCopy||!g.edit.drag.isMove&&g.edit.drag.isCopy;a=!!(D&&u===D.tId);l=!!(E&&u===
E.tId);j=f.parentTId&&f.parentTId==u;f=(c||!l)&&h.apply(i.edit.drag.prev,[i.treeId,m,w],!!i.edit.drag.prev);a=(c||!a)&&h.apply(i.edit.drag.next,[i.treeId,m,w],!!i.edit.drag.next);C=(c||!j)&&!(i.data.keep.leaf&&!w.isParent)&&h.apply(i.edit.drag.inner,[i.treeId,m,w],!!i.edit.drag.inner);if(!f&&!a&&!C){if(r=null,u="",v=d.move.TYPE_INNER,t.css({display:"none"}),window.zTreeMoveTimer)clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null}else{c=k("#"+u+d.id.A,r);l=w.isLastNode?null:k("#"+
w.getNextNode().tId+d.id.A,r.next());s=c.offset().top;j=c.offset().left;I=f?C?0.25:a?0.5:1:-1;C=a?C?0.75:f?0.5:0:-1;b=(b.clientY+B-s)/c.height();(I==1||b<=I&&b>=-0.2)&&f?(a=1-t.width(),l=s-t.height()/2,v=d.move.TYPE_PREV):(C==0||b>=C&&b<=1.2)&&a?(a=1-t.width(),l=l==null||w.isParent&&w.open?s+c.height()-t.height()/2:l.offset().top-t.height()/2,v=d.move.TYPE_NEXT):(a=5-t.width(),l=s,v=d.move.TYPE_INNER);t.css({display:"block",top:l+"px",left:j+a+"px"});c.addClass(d.node.TMPTARGET_NODE+"_"+v);if(P!=
u||Q!=v)J=(new Date).getTime();if(w&&w.isParent&&v==d.move.TYPE_INNER&&(b=!0,window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId!==w.tId?(clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null):window.zTreeMoveTimer&&window.zTreeMoveTargetNodeTId===w.tId&&(b=!1),b))window.zTreeMoveTimer=setTimeout(function(){v==d.move.TYPE_INNER&&w&&w.isParent&&!w.open&&(new Date).getTime()-J>i.edit.drag.autoOpenTime&&h.apply(i.callback.beforeDragOpen,[i.treeId,w],!0)&&(e.switchNode(i,w),i.edit.drag.autoExpandTrigger&&
i.treeObj.trigger(d.event.EXPAND,[i.treeId,w]))},i.edit.drag.autoOpenTime+50),window.zTreeMoveTargetNodeTId=w.tId}}else if(v=d.move.TYPE_INNER,r&&h.apply(i.edit.drag.inner,[i.treeId,m,null],!!i.edit.drag.inner)?r.addClass(d.node.TMPTARGET_TREE):r=null,t.css({display:"none"}),window.zTreeMoveTimer)clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null;P=u;Q=v}return!1}function q(b){if(window.zTreeMoveTimer)clearTimeout(window.zTreeMoveTimer),window.zTreeMoveTargetNodeTId=null;Q=P=null;
x.unbind("mousemove",c);x.unbind("mouseup",q);x.unbind("selectstart",f);k("body").css("cursor","auto");r&&(r.removeClass(d.node.TMPTARGET_TREE),u&&k("#"+u+d.id.A,r).removeClass(d.node.TMPTARGET_NODE+"_"+d.move.TYPE_PREV).removeClass(d.node.TMPTARGET_NODE+"_"+F.move.TYPE_NEXT).removeClass(d.node.TMPTARGET_NODE+"_"+F.move.TYPE_INNER));h.showIfameMask(g,!1);z.showHoverDom=!0;if(z.dragFlag!=0){z.dragFlag=0;var a,l,j;for(a=0,l=m.length;a<l;a++)j=m[a],j.isParent&&z.dragNodeShowBefore[j.tId]&&!j.open&&(e.expandCollapseNode(g,
j,!j.open),delete z.dragNodeShowBefore[j.tId]);y&&y.remove();t&&t.remove();var o=b.ctrlKey&&g.edit.drag.isMove&&g.edit.drag.isCopy||!g.edit.drag.isMove&&g.edit.drag.isCopy;!o&&r&&u&&m[0].parentTId&&u==m[0].parentTId&&v==d.move.TYPE_INNER&&(r=null);if(r){var n=u==null?null:p.getNodeCache(i,u);if(h.apply(g.callback.beforeDrop,[i.treeId,m,n,v,o],!0)!=!1){var s=o?h.clone(m):m;a=function(){if(G){if(!o)for(var a=0,c=m.length;a<c;a++)e.removeNode(g,m[a]);if(v==d.move.TYPE_INNER)e.addNodes(i,n,s);else if(e.addNodes(i,
n.getParentNode(),s),v==d.move.TYPE_PREV)for(a=0,c=s.length;a<c;a++)e.moveNode(i,n,s[a],v,!1);else for(a=-1,c=s.length-1;a<c;c--)e.moveNode(i,n,s[c],v,!1)}else if(o&&v==d.move.TYPE_INNER)e.addNodes(i,n,s);else if(o&&e.addNodes(i,n.getParentNode(),s),v!=d.move.TYPE_NEXT)for(a=0,c=s.length;a<c;a++)e.moveNode(i,n,s[a],v,!1);else for(a=-1,c=s.length-1;a<c;c--)e.moveNode(i,n,s[c],v,!1);for(a=0,c=s.length;a<c;a++)e.selectNode(i,s[a],a>0);k("#"+s[0].tId).focus().blur();g.treeObj.trigger(d.event.DROP,[b,
i.treeId,s,n,v,o])};v==d.move.TYPE_INNER&&h.canAsync(i,n)?e.asyncNode(i,n,!1,a):a()}}else{for(a=0,l=m.length;a<l;a++)e.selectNode(i,m[a],a>0);g.treeObj.trigger(d.event.DROP,[b,g.treeId,m,null,null,null])}}}function f(){return!1}var l,j,g=p.getSetting(b.data.treeId),z=p.getRoot(g);if(b.button==2||!g.edit.enable||!g.edit.drag.isCopy&&!g.edit.drag.isMove)return!0;var o=b.target,n=p.getRoot(g).curSelectedList,m=[];if(p.isSelectedNode(g,a))for(l=0,j=n.length;l<j;l++){if(n[l].editNameFlag&&h.eqs(o.tagName,
"input")&&o.getAttribute("treeNode"+d.id.INPUT)!==null)return!0;m.push(n[l]);if(m[0].parentTId!==n[l].parentTId){m=[a];break}}else m=[a];e.editNodeBlur=!0;e.cancelCurEditNode(g,null,!0);var x=k(document),y,t,r,G=!1,i=g,D,E,P=null,Q=null,u=null,v=d.move.TYPE_INNER,K=b.clientX,L=b.clientY,J=(new Date).getTime();h.uCanDo(g)&&x.bind("mousemove",c);x.bind("mouseup",q);x.bind("selectstart",f);b.preventDefault&&b.preventDefault();return!0}};k.extend(!0,k.fn.zTree.consts,F);k.extend(!0,k.fn.zTree._z,{tools:{getAbs:function(b){b=
b.getBoundingClientRect();return[b.left,b.top]},inputFocus:function(b){b.get(0)&&(b.focus(),h.setCursorPosition(b.get(0),b.val().length))},inputSelect:function(b){b.get(0)&&(b.focus(),b.select())},setCursorPosition:function(b,a){if(b.setSelectionRange)b.focus(),b.setSelectionRange(a,a);else if(b.createTextRange){var c=b.createTextRange();c.collapse(!0);c.moveEnd("character",a);c.moveStart("character",a);c.select()}},showIfameMask:function(b,a){for(var c=p.getRoot(b);c.dragMaskList.length>0;)c.dragMaskList[0].remove(),
c.dragMaskList.shift();if(a)for(var d=k("iframe"),f=0,e=d.length;f<e;f++){var j=d.get(f),g=h.getAbs(j),j=k("<div id='zTreeMask_"+f+"' class='zTreeMask' style='top:"+g[1]+"px; left:"+g[0]+"px; width:"+j.offsetWidth+"px; height:"+j.offsetHeight+"px;'></div>");j.appendTo("body");c.dragMaskList.push(j)}}},view:{addEditBtn:function(b,a){if(!(a.editNameFlag||k("#"+a.tId+d.id.EDIT).length>0)&&h.apply(b.edit.showRenameBtn,[b.treeId,a],b.edit.showRenameBtn)){var c=k("#"+a.tId+d.id.A),q="<span class='"+d.className.BUTTON+
" edit' id='"+a.tId+d.id.EDIT+"' title='"+h.apply(b.edit.renameTitle,[b.treeId,a],b.edit.renameTitle)+"' treeNode"+d.id.EDIT+" style='display:none;'></span>";c.append(q);k("#"+a.tId+d.id.EDIT).bind("click",function(){if(!h.uCanDo(b)||h.apply(b.callback.beforeEditName,[b.treeId,a],!0)==!1)return!1;e.editNode(b,a);return!1}).show()}},addRemoveBtn:function(b,a){if(!(a.editNameFlag||k("#"+a.tId+d.id.REMOVE).length>0)&&h.apply(b.edit.showRemoveBtn,[b.treeId,a],b.edit.showRemoveBtn)){var c=k("#"+a.tId+
d.id.A),q="<span class='"+d.className.BUTTON+" remove' id='"+a.tId+d.id.REMOVE+"' title='"+h.apply(b.edit.removeTitle,[b.treeId,a],b.edit.removeTitle)+"' treeNode"+d.id.REMOVE+" style='display:none;'></span>";c.append(q);k("#"+a.tId+d.id.REMOVE).bind("click",function(){if(!h.uCanDo(b)||h.apply(b.callback.beforeRemove,[b.treeId,a],!0)==!1)return!1;e.removeNode(b,a);b.treeObj.trigger(d.event.REMOVE,[b.treeId,a]);return!1}).bind("mousedown",function(){return!0}).show()}},addHoverDom:function(b,a){if(p.getRoot(b).showHoverDom)a.isHover=
!0,b.edit.enable&&(e.addEditBtn(b,a),e.addRemoveBtn(b,a)),h.apply(b.view.addHoverDom,[b.treeId,a])},cancelCurEditNode:function(b,a){var c=p.getRoot(b),q=b.data.key.name,f=c.curEditNode;if(f){var l=c.curEditInput,j=a?a:l.val();if(!a&&h.apply(b.callback.beforeRename,[b.treeId,f,j],!0)===!1)return!1;else f[q]=j?j:l.val(),a||b.treeObj.trigger(d.event.RENAME,[b.treeId,f]);k("#"+f.tId+d.id.A).removeClass(d.node.CURSELECTED_EDIT);l.unbind();e.setNodeName(b,f);f.editNameFlag=!1;c.curEditNode=null;c.curEditInput=
null;e.selectNode(b,f,!1)}return c.noSelection=!0},editNode:function(b,a){var c=p.getRoot(b);e.editNodeBlur=!1;if(p.isSelectedNode(b,a)&&c.curEditNode==a&&a.editNameFlag)setTimeout(function(){h.inputFocus(c.curEditInput)},0);else{var q=b.data.key.name;a.editNameFlag=!0;e.removeTreeDom(b,a);e.cancelCurEditNode(b);e.selectNode(b,a,!1);k("#"+a.tId+d.id.SPAN).html("<input type=text class='rename' id='"+a.tId+d.id.INPUT+"' treeNode"+d.id.INPUT+" >");var f=k("#"+a.tId+d.id.INPUT);f.attr("value",a[q]);b.edit.editNameSelectAll?
h.inputSelect(f):h.inputFocus(f);f.bind("blur",function(){e.editNodeBlur||e.cancelCurEditNode(b)}).bind("keydown",function(c){c.keyCode=="13"?(e.editNodeBlur=!0,e.cancelCurEditNode(b,null,!0)):c.keyCode=="27"&&e.cancelCurEditNode(b,a[q])}).bind("click",function(){return!1}).bind("dblclick",function(){return!1});k("#"+a.tId+d.id.A).addClass(d.node.CURSELECTED_EDIT);c.curEditInput=f;c.noSelection=!1;c.curEditNode=a}},moveNode:function(b,a,c,q,f,l){var j=p.getRoot(b),g=b.data.key.children;if(a!=c&&(!b.data.keep.leaf||
!a||a.isParent||q!=d.move.TYPE_INNER)){var h=c.parentTId?c.getParentNode():j,o=a===null||a==j;o&&a===null&&(a=j);if(o)q=d.move.TYPE_INNER;j=a.parentTId?a.getParentNode():j;if(q!=d.move.TYPE_PREV&&q!=d.move.TYPE_NEXT)q=d.move.TYPE_INNER;if(q==d.move.TYPE_INNER)if(o)c.parentTId=null;else{if(!a.isParent)a.isParent=!0,a.open=!!a.open,e.setNodeLineIcos(b,a);c.parentTId=a.tId}var n;o?n=o=b.treeObj:(!l&&q==d.move.TYPE_INNER?e.expandCollapseNode(b,a,!0,!1):l||e.expandCollapseNode(b,a.getParentNode(),!0,!1),
o=k("#"+a.tId),n=k("#"+a.tId+d.id.UL),o.get(0)&&!n.get(0)&&(n=[],e.makeUlHtml(b,a,n,""),o.append(n.join(""))),n=k("#"+a.tId+d.id.UL));var m=k("#"+c.tId);m.get(0)?o.get(0)||m.remove():m=e.appendNodes(b,c.level,[c],null,!1,!0).join("");n.get(0)&&q==d.move.TYPE_INNER?n.append(m):o.get(0)&&q==d.move.TYPE_PREV?o.before(m):o.get(0)&&q==d.move.TYPE_NEXT&&o.after(m);var x=-1,y=0,t=null,o=null,r=c.level;if(c.isFirstNode){if(x=0,h[g].length>1)t=h[g][1],t.isFirstNode=!0}else if(c.isLastNode)x=h[g].length-1,
t=h[g][x-1],t.isLastNode=!0;else for(n=0,m=h[g].length;n<m;n++)if(h[g][n].tId==c.tId){x=n;break}x>=0&&h[g].splice(x,1);if(q!=d.move.TYPE_INNER)for(n=0,m=j[g].length;n<m;n++)j[g][n].tId==a.tId&&(y=n);if(q==d.move.TYPE_INNER){a[g]||(a[g]=[]);if(a[g].length>0)o=a[g][a[g].length-1],o.isLastNode=!1;a[g].splice(a[g].length,0,c);c.isLastNode=!0;c.isFirstNode=a[g].length==1}else a.isFirstNode&&q==d.move.TYPE_PREV?(j[g].splice(y,0,c),o=a,o.isFirstNode=!1,c.parentTId=a.parentTId,c.isFirstNode=!0,c.isLastNode=
!1):a.isLastNode&&q==d.move.TYPE_NEXT?(j[g].splice(y+1,0,c),o=a,o.isLastNode=!1,c.parentTId=a.parentTId,c.isFirstNode=!1,c.isLastNode=!0):(q==d.move.TYPE_PREV?j[g].splice(y,0,c):j[g].splice(y+1,0,c),c.parentTId=a.parentTId,c.isFirstNode=!1,c.isLastNode=!1);p.fixPIdKeyValue(b,c);p.setSonNodeLevel(b,c.getParentNode(),c);e.setNodeLineIcos(b,c);e.repairNodeLevelClass(b,c,r);!b.data.keep.parent&&h[g].length<1?(h.isParent=!1,h.open=!1,a=k("#"+h.tId+d.id.UL),q=k("#"+h.tId+d.id.SWITCH),g=k("#"+h.tId+d.id.ICON),
e.replaceSwitchClass(h,q,d.folder.DOCU),e.replaceIcoClass(h,g,d.folder.DOCU),a.css("display","none")):t&&e.setNodeLineIcos(b,t);o&&e.setNodeLineIcos(b,o);b.check&&b.check.enable&&e.repairChkClass&&(e.repairChkClass(b,h),e.repairParentChkClassWithSelf(b,h),h!=c.parent&&e.repairParentChkClassWithSelf(b,c));l||e.expandCollapseParentNode(b,c.getParentNode(),!0,f)}},removeEditBtn:function(b){k("#"+b.tId+d.id.EDIT).unbind().remove()},removeRemoveBtn:function(b){k("#"+b.tId+d.id.REMOVE).unbind().remove()},
removeTreeDom:function(b,a){a.isHover=!1;e.removeEditBtn(a);e.removeRemoveBtn(a);h.apply(b.view.removeHoverDom,[b.treeId,a])},repairNodeLevelClass:function(b,a,c){if(c!==a.level){var b=k("#"+a.tId),e=k("#"+a.tId+d.id.A),f=k("#"+a.tId+d.id.UL),c=d.className.LEVEL+c,a=d.className.LEVEL+a.level;b.removeClass(c);b.addClass(a);e.removeClass(c);e.addClass(a);f.removeClass(c);f.addClass(a)}}},event:{},data:{setSonNodeLevel:function(b,a,c){if(c){var d=b.data.key.children;c.level=a?a.level+1:0;if(c[d])for(var a=
0,f=c[d].length;a<f;a++)c[d][a]&&p.setSonNodeLevel(b,c,c[d][a])}}}});var E=k.fn.zTree,h=E._z.tools,d=E.consts,e=E._z.view,p=E._z.data;p.exSetting({edit:{enable:!1,editNameSelectAll:!1,showRemoveBtn:!0,showRenameBtn:!0,removeTitle:"remove",renameTitle:"rename",drag:{autoExpandTrigger:!1,isCopy:!0,isMove:!0,prev:!0,next:!0,inner:!0,minMoveSize:5,borderMax:10,borderMin:-5,maxShowNodeNum:5,autoOpenTime:500}},view:{addHoverDom:null,removeHoverDom:null},callback:{beforeDrag:null,beforeDragOpen:null,beforeDrop:null,
beforeEditName:null,beforeRename:null,onDrag:null,onDrop:null,onRename:null}});p.addInitBind(function(b){var a=b.treeObj,c=d.event;a.bind(c.RENAME,function(a,c,d){h.apply(b.callback.onRename,[a,c,d])});a.bind(c.REMOVE,function(a,c,d){h.apply(b.callback.onRemove,[a,c,d])});a.bind(c.DRAG,function(a,c,d,e){h.apply(b.callback.onDrag,[c,d,e])});a.bind(c.DROP,function(a,c,d,e,g,k,o){h.apply(b.callback.onDrop,[c,d,e,g,k,o])})});p.addInitUnBind(function(b){var b=b.treeObj,a=d.event;b.unbind(a.RENAME);b.unbind(a.REMOVE);
b.unbind(a.DRAG);b.unbind(a.DROP)});p.addInitCache(function(){});p.addInitNode(function(b,a,c){if(c)c.isHover=!1,c.editNameFlag=!1});p.addInitProxy(function(b){var a=b.target,c=p.getSetting(b.data.treeId),e=b.relatedTarget,f="",l=null,j="",g=null,k=null;if(h.eqs(b.type,"mouseover")){if(k=h.getMDom(c,a,[{tagName:"a",attrName:"treeNode"+d.id.A}]))f=k.parentNode.id,j="hoverOverNode"}else if(h.eqs(b.type,"mouseout"))k=h.getMDom(c,e,[{tagName:"a",attrName:"treeNode"+d.id.A}]),k||(f="remove",j="hoverOutNode");
else if(h.eqs(b.type,"mousedown")&&(k=h.getMDom(c,a,[{tagName:"a",attrName:"treeNode"+d.id.A}])))f=k.parentNode.id,j="mousedownNode";if(f.length>0)switch(l=p.getNodeCache(c,f),j){case "mousedownNode":g=D.onMousedownNode;break;case "hoverOverNode":g=D.onHoverOverNode;break;case "hoverOutNode":g=D.onHoverOutNode}return{stop:!1,node:l,nodeEventType:j,nodeEventCallback:g,treeEventType:"",treeEventCallback:null}});p.addInitRoot(function(b){b=p.getRoot(b);b.curEditNode=null;b.curEditInput=null;b.curHoverNode=
null;b.dragFlag=0;b.dragNodeShowBefore=[];b.dragMaskList=[];b.showHoverDom=!0});p.addZTreeTools(function(b,a){a.cancelEditName=function(a){var d=p.getRoot(b),f=b.data.key.name,h=d.curEditNode;d.curEditNode&&e.cancelCurEditNode(b,a?a:h[f])};a.copyNode=function(a,k,f,l){if(!k)return null;if(a&&!a.isParent&&b.data.keep.leaf&&f===d.move.TYPE_INNER)return null;var j=h.clone(k);if(!a)a=null,f=d.move.TYPE_INNER;f==d.move.TYPE_INNER?(k=function(){e.addNodes(b,a,[j],l)},h.canAsync(b,a)?e.asyncNode(b,a,l,k):
k()):(e.addNodes(b,a.parentNode,[j],l),e.moveNode(b,a,j,f,!1,l));return j};a.editName=function(a){a&&a.tId&&a===p.getNodeCache(b,a.tId)&&(a.parentTId&&e.expandCollapseParentNode(b,a.getParentNode(),!0),e.editNode(b,a))};a.moveNode=function(a,q,f,l){function j(){e.moveNode(b,a,q,f,!1,l)}if(!q)return q;if(a&&!a.isParent&&b.data.keep.leaf&&f===d.move.TYPE_INNER)return null;else if(a&&(q.parentTId==a.tId&&f==d.move.TYPE_INNER||k("#"+q.tId).find("#"+a.tId).length>0))return null;else a||(a=null);h.canAsync(b,
a)&&f===d.move.TYPE_INNER?e.asyncNode(b,a,l,j):j();return q};a.setEditable=function(a){b.edit.enable=a;return this.refresh()}});var K=e.cancelPreSelectedNode;e.cancelPreSelectedNode=function(b,a){for(var c=p.getRoot(b).curSelectedList,d=0,f=c.length;d<f;d++)if(!a||a===c[d])if(e.removeTreeDom(b,c[d]),a)break;K&&K.apply(e,arguments)};var L=e.createNodes;e.createNodes=function(b,a,c,d){L&&L.apply(e,arguments);c&&e.repairParentChkClassWithSelf&&e.repairParentChkClassWithSelf(b,d)};var R=e.makeNodeUrl;
e.makeNodeUrl=function(b,a){return b.edit.enable?null:R.apply(e,arguments)};var J=e.removeNode;e.removeNode=function(b,a){var c=p.getRoot(b);if(c.curEditNode===a)c.curEditNode=null;J&&J.apply(e,arguments)};var M=e.selectNode;e.selectNode=function(b,a,c){var d=p.getRoot(b);if(p.isSelectedNode(b,a)&&d.curEditNode==a&&a.editNameFlag)return!1;M&&M.apply(e,arguments);e.addHoverDom(b,a);return!0};var N=h.uCanDo;h.uCanDo=function(b,a){var c=p.getRoot(b);return a&&(h.eqs(a.type,"mouseover")||h.eqs(a.type,
"mouseout")||h.eqs(a.type,"mousedown")||h.eqs(a.type,"mouseup"))?!0:!c.curEditNode&&(N?N.apply(e,arguments):!0)}})(jQuery);

/*
 * JQuery zTree exHideNodes 3.5.12
 * http://zTree.me/
 *
 * Copyright (c) 2010 Hunter.z
 *
 * Licensed same as jquery - MIT License
 * http://www.opensource.org/licenses/mit-license.php
 *
 * email: <EMAIL>
 * Date: 2013-03-11
 */
(function(i){i.extend(!0,i.fn.zTree._z,{view:{clearOldFirstNode:function(c,a){for(var b=a.getNextNode();b;){if(b.isFirstNode){b.isFirstNode=!1;f.setNodeLineIcos(c,b);break}if(b.isLastNode)break;b=b.getNextNode()}},clearOldLastNode:function(c,a){for(var b=a.getPreNode();b;){if(b.isLastNode){b.isLastNode=!1;f.setNodeLineIcos(c,b);break}if(b.isFirstNode)break;b=b.getPreNode()}},makeDOMNodeMainBefore:function(c,a,b){c.push("<li ",b.isHidden?"style='display:none;' ":"","id='",b.tId,"' class='",l.className.LEVEL,
b.level,"' tabindex='0' hidefocus='true' treenode>")},showNode:function(c,a){a.isHidden=!1;e.initShowForExCheck(c,a);i("#"+a.tId).show()},showNodes:function(c,a,b){if(a&&a.length!=0){var d={},h,j;for(h=0,j=a.length;h<j;h++){var g=a[h];if(!d[g.parentTId]){var i=g.getParentNode();d[g.parentTId]=i===null?e.getRoot(c):g.getParentNode()}f.showNode(c,g,b)}for(var k in d)a=d[k][c.data.key.children],f.setFirstNodeForShow(c,a),f.setLastNodeForShow(c,a)}},hideNode:function(c,a){a.isHidden=!0;a.isFirstNode=
!1;a.isLastNode=!1;e.initHideForExCheck(c,a);f.cancelPreSelectedNode(c,a);i("#"+a.tId).hide()},hideNodes:function(c,a,b){if(a&&a.length!=0){var d={},h,j;for(h=0,j=a.length;h<j;h++){var g=a[h];if((g.isFirstNode||g.isLastNode)&&!d[g.parentTId]){var i=g.getParentNode();d[g.parentTId]=i===null?e.getRoot(c):g.getParentNode()}f.hideNode(c,g,b)}for(var k in d)a=d[k][c.data.key.children],f.setFirstNodeForHide(c,a),f.setLastNodeForHide(c,a)}},setFirstNode:function(c,a){var b=c.data.key.children,d=a[b].length;
d>0&&!a[b][0].isHidden?a[b][0].isFirstNode=!0:d>0&&f.setFirstNodeForHide(c,a[b])},setLastNode:function(c,a){var b=c.data.key.children,d=a[b].length;d>0&&!a[b][0].isHidden?a[b][d-1].isLastNode=!0:d>0&&f.setLastNodeForHide(c,a[b])},setFirstNodeForHide:function(c,a){var b,d,h;for(d=0,h=a.length;d<h;d++){b=a[d];if(b.isFirstNode)break;if(!b.isHidden&&!b.isFirstNode){b.isFirstNode=!0;f.setNodeLineIcos(c,b);break}else b=null}return b},setFirstNodeForShow:function(c,a){var b,d,h,e,g;for(d=0,h=a.length;d<
h;d++)if(b=a[d],!e&&!b.isHidden&&b.isFirstNode){e=b;break}else if(!e&&!b.isHidden&&!b.isFirstNode)b.isFirstNode=!0,e=b,f.setNodeLineIcos(c,b);else if(e&&b.isFirstNode){b.isFirstNode=!1;g=b;f.setNodeLineIcos(c,b);break}return{"new":e,old:g}},setLastNodeForHide:function(c,a){var b,d;for(d=a.length-1;d>=0;d--){b=a[d];if(b.isLastNode)break;if(!b.isHidden&&!b.isLastNode){b.isLastNode=!0;f.setNodeLineIcos(c,b);break}else b=null}return b},setLastNodeForShow:function(c,a){var b,d,e,j;for(d=a.length-1;d>=
0;d--)if(b=a[d],!e&&!b.isHidden&&b.isLastNode){e=b;break}else if(!e&&!b.isHidden&&!b.isLastNode)b.isLastNode=!0,e=b,f.setNodeLineIcos(c,b);else if(e&&b.isLastNode){b.isLastNode=!1;j=b;f.setNodeLineIcos(c,b);break}return{"new":e,old:j}}},data:{initHideForExCheck:function(c,a){if(a.isHidden&&c.check&&c.check.enable){if(typeof a._nocheck=="undefined")a._nocheck=!!a.nocheck,a.nocheck=!0;a.check_Child_State=-1;f.repairParentChkClassWithSelf&&f.repairParentChkClassWithSelf(c,a)}},initShowForExCheck:function(c,
a){if(!a.isHidden&&c.check&&c.check.enable){if(typeof a._nocheck!="undefined")a.nocheck=a._nocheck,delete a._nocheck;if(f.setChkClass){var b=i("#"+a.tId+l.id.CHECK);f.setChkClass(c,b,a)}f.repairParentChkClassWithSelf&&f.repairParentChkClassWithSelf(c,a)}}}});var k=i.fn.zTree,t=k._z.tools,l=k.consts,f=k._z.view,e=k._z.data;e.addInitNode(function(c,a,b){if(typeof b.isHidden=="string")b.isHidden=t.eqs(b.isHidden,"true");b.isHidden=!!b.isHidden;e.initHideForExCheck(c,b)});e.addBeforeA(function(){});e.addZTreeTools(function(c,
a){a.showNodes=function(a,b){f.showNodes(c,a,b)};a.showNode=function(a,b){a&&f.showNodes(c,[a],b)};a.hideNodes=function(a,b){f.hideNodes(c,a,b)};a.hideNode=function(a,b){a&&f.hideNodes(c,[a],b)};var b=a.checkNode;if(b)a.checkNode=function(c,e,f,g){(!c||!c.isHidden)&&b.apply(a,arguments)}});var m=e.initNode;e.tmpHideParent=-1;e.initNode=function(c,a,b,d,h,j,g){if(e.tmpHideParent!==d){e.tmpHideParent=d;var i=(d?d:e.getRoot(c))[c.data.key.children];e.tmpHideFirstNode=f.setFirstNodeForHide(c,i);e.tmpHideLastNode=
f.setLastNodeForHide(c,i);f.setNodeLineIcos(c,e.tmpHideFirstNode);f.setNodeLineIcos(c,e.tmpHideLastNode)}h=e.tmpHideFirstNode===b;j=e.tmpHideLastNode===b;m&&m.apply(e,arguments);j&&f.clearOldLastNode(c,b)};var n=e.makeChkFlag;if(n)e.makeChkFlag=function(c,a){(!a||!a.isHidden)&&n.apply(e,arguments)};var o=e.getTreeCheckedNodes;if(o)e.getTreeCheckedNodes=function(c,a,b,d){if(a&&a.length>0){var f=a[0].getParentNode();if(f&&f.isHidden)return[]}return o.apply(e,arguments)};var p=e.getTreeChangeCheckedNodes;
if(p)e.getTreeChangeCheckedNodes=function(c,a,b){if(a&&a.length>0){var d=a[0].getParentNode();if(d&&d.isHidden)return[]}return p.apply(e,arguments)};var q=f.expandCollapseSonNode;if(q)f.expandCollapseSonNode=function(c,a,b,d,e){(!a||!a.isHidden)&&q.apply(f,arguments)};var r=f.setSonNodeCheckBox;if(r)f.setSonNodeCheckBox=function(c,a,b,d){(!a||!a.isHidden)&&r.apply(f,arguments)};var s=f.repairParentChkClassWithSelf;if(s)f.repairParentChkClassWithSelf=function(c,a){(!a||!a.isHidden)&&s.apply(f,arguments)}})(jQuery);
