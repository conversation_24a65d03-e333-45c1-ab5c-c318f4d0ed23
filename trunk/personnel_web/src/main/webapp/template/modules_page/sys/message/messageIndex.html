#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()我的消息管理#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <button type="button" class="layui-btn layui-bg-cyan layui-btn-xs" onclick="queryMsg('')">全部消息</button>
        <button type="button" class="layui-btn layui-bg-red layui-btn-xs" onclick="queryMsg('0')">未读消息</button>
        <button type="button" class="layui-btn layui-bg-green layui-btn-xs" onclick="queryMsg('1')">已读消息</button>
<!--         <button type="button" class="layui-btn layui-bg-blue layui-btn-xs" id="signRead">标记已读</button> -->
    </div>
    <div class="layui-row">
	    <table id="messageTable" lay-filter="messageTable"></table>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("myMessage:viewBtn")
		<a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
	#end
</div>
</script>
<script>
layui.use(['form','layer','table'], function() {
    var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

    function msgLoad(data){
        table.render({
            id : 'messageTable'
            ,elem : '#messageTable'
            ,method : 'POST'
            ,where : data
//             ,height:$(document).height()*0.8
            ,url : '#(ctxPath)/message/pageTable'
            ,cellMinWidth: 80
            ,cols: [[
                {type:'checkbox'}
                ,{field:'', title: '消息类别', width: 100, align: 'left', unresize: true, templet:'#statusTpl(com.cszn.integrated.service.entity.status.MsgCategory::me(), "msgCategory")'}
                ,{field:'msgTitle', title: '消息标题', align: 'left', unresize: true}
                ,{field:'', title: '是否已读', width: 100, align: 'center', unresize: true,templet:"<div>{{ d.isRead=='0'?'<span class='layui-badge layui-bg-orange'>未读</span>':d.isRead=='1'?'<span class='layui-badge layui-bg-green'>已读</span>':'- -' }}</div>"}
                ,{field:'', title: '创建时间', width: 180,align: 'left', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,done: function(res, curr, count){
            }
            ,page : true
            ,limit : 10
            ,limits : [10,20,30,40]
        });
    };
    table.on('tool(messageTable)',function(obj){
    	if(obj.event === 'view'){
            var url = '#(ctxPath)'+obj.data.msgUrl+'?id='+obj.data.relationId;
            console.log('url==='+url);
    		if(obj.data.msgCategory=='notice'){
	            pop_show('查阅公告',url,1050,680);
    		}
            if(obj.data.isRead=='0'){
	            //标记已读
	            util.sendAjax ({
	                type: 'POST',
	                url: '#(ctxPath)/message/update',
	                notice: false,
	                data: {id:obj.data.id, isRead:'1'},
	                loadFlag: false,
	                success : function(rep){
	                },
	                complete : function() {
	                }
	            });
            }
        }
    });
    
    msgLoad(null);

    queryMsg = function(isRead){
        var data = {'isRead':isRead};
        msgLoad(data);
    }

    form.on("submit(search)",function(data){
        msgLoad(data.field);
        return false;
    });

    //批量获取列表数据
    getCheckTableData = function(){
        var tableCheckStatus = table.checkStatus('messageTable');
        // 获取选择状态下的数据
        return tableCheckStatus.data;
    }
    //批量已读
//     $("#signRead").click(function(){
//         var jsonData = getCheckTableData();
//         if(jsonData == null || jsonData == ''){
//             layer.msg('请勾选数据', function () {});
//             return;
//         }else{
// 	        layer.confirm("确定标记已读吗?",function(index){
// 	            util.sendAjax ({
// 	                type: 'POST',
// 	                url: '#(ctxPath)/message/signRead',
// 	                data: {data:JSON.stringify(jsonData)},
// 	                notice: true,
// 	                loadFlag: true,
// 	                success : function(rep){
// 	                    if(rep.state=='ok'){
// 	                        table.reload('messageTable');
// 	                    }
// 	                },
// 	                complete : function() {
// 	                }
// 	            });
// 	            layer.close(index) ;
// 	        });
//         }
//     });
});
</script>
#end