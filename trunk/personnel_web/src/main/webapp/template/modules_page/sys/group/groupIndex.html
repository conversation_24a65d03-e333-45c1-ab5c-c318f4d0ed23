#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户分组首页#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
			<div class="layui-inline">
				<label class="layui-form-label">分组名称</label>
				<div class="layui-input-inline">
					<input id="groupName" name="groupName" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
				<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
				#shiroHasPermission("pers:groupUser:addBtn")
	        		<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
				#end
			</div>
		</form>
	</div>
	<div class="layui-row">
		<table id="groupTable" lay-filter="groupTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("pers:groupUser:editBtn")
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("pers:groupUser:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		function groupLoad(data){
			table.render({
				id : 'groupTable'
				,elem : '#groupTable'
				,method : 'POST'
				,where : data
				,url : '#(ctxPath)/group/pageTable'
				,cellMinWidth: 80
				,width:$(document).width()
				,height:'full-200'
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field:'groupName', title: '分组名称', align: 'center', unresize: true}
					,{field: '', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge'>不可用</span>':d.isEnable == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':'- -'}}</div>"}
					,{field: '', title: '创建时间', unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 15
				,limits : [15,25,35,45]
			});
		};
		table.on('tool(groupTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/group/edit?id="+obj.data.id;
				pop_show("编辑",url,'','');
			}else if(obj.event === 'del'){
				layer.confirm("确定作废?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/group/del",
						type:'post',
						data:{"id":obj.data.id, delFlag:'1'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('groupTable');
							}
							layer.close(index);
						}
					});
				});
			}
		});
		
		groupLoad(null);
		
		form.on("submit(search)",function(data){
			groupLoad(data.field);
			return false;
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/group/add','','');
		});
		
		
	});
</script>
#end