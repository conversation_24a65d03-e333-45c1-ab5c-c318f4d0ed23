#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构角色首页#end

#define css()
#end

#define js()
<script type="text/html" id="roleTableBar">
	<a class="layui-btn layui-btn-xs" lay-event="sysUser">账号</a>
	#shiroHasPermission("pers:permissionRole:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("pers:permissionRole:delBtn")
	<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
	#end
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'roleTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/role/pageTable'
	    , method: 'post'
	    , height: vipTable.getFullHeight()    //容器高度
	    , where: {orgId:$('#orgId').val(), roleName:$('#roleName').val()}
	    , cols: [[                  //标题栏
	          {field:'', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
	        , {field: 'roleName', title: '角色名称', unresize:true}
	        , {field: 'createDate', title: '创建时间', unresize:true}
	        , {fixed: 'right', title: '操作', width: 200, align: 'center', toolbar: '#roleTableBar'} //这里的toolbar值是模板元素的选择器
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
	// 表格绑定事件
    table.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/role/edit?id='+obj.data.id,'500','');
        }else if(obj.event==="del"){//作废按钮事件
        	//作废操作
    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/role/del',
                    data: {id : obj.data.id},
                    loadFlag: true,
                    success : function(rep){
                    },
                    complete : function() {
                    	pageTableReload();
        		    }
                });
				layer.close(index);
            });
        }else if(obj.event==='sysUser'){
			pop_show('编辑','#(ctxPath)/role/roleUserIndex?roleId='+obj.data.id,'1000','700');
		}
    });
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{roleName:$('#roleName').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		var orgId = $('#orgId').val();
		pop_show('添加','#(ctxPath)/role/add','500','');
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <label class="layui-form-label">角色名称：</label>
			        <div class="layui-input-inline">
			            <input type="text" id="roleName" name="roleName" class="layui-input" placeholder="请输入角色名称" autocomplete="off">
			        </div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
						#shiroHasPermission("pers:permissionRole:addBtn")
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
						#end
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
	<div class="layui-row">
		<table id="roleTable" lay-filter="roleTable"></table>
	</div>
</div>
#end