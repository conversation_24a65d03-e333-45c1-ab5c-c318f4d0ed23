#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构员工编辑页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div style="margin-bottom:23px;"></div>
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><font color="red">*</font>姓名</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" name="fullName" class="layui-input" lay-verify="required" value="#(model.fullName??)" placeholder="请输入姓名">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><font color="red">*</font>性别</label>
				<div class="layui-input-inline" style="width:155px;">
					<select name="sex" lay-verify="required" lay-filter="">
						<option value="">请选择性别</option>
						#dictOption("sex", model.sex??'', "")
					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label"><font color="red">*</font>出生日期</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" id="birthday" name="birthday" class="layui-input" lay-verify="required" value="#(model.birthday??)" autocomplete="off">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label"><font color="red">*</font>身份证号</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" name="idCard" class="layui-input" lay-verify="required|identity" value="#(model.idCard??)" maxlength="19" placeholder="请输入身份证号">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">婚姻状况</label>
				<div class="layui-input-inline" style="width:155px;">
					<select name="maritalStatus" lay-filter="">
						<option value="">请选择婚姻状况</option>
						#dictOption("marital_status", model.maritalStatus??'', "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">民族</label>
				<div class="layui-input-inline" style="width:155px;">
					<select name="nationality" lay-search="">
						<option value="">请选择民族</option>
						#dictOption("nation", model.nationality??'', "")
					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">文化程度</label>
				<div class="layui-input-inline" style="width:155px;">
					<select name="education" lay-filter="">
						<option value="">请选择文化程度</option>
						#dictOption("education", model.education??'', "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">政治面貌</label>
				<div class="layui-input-inline" style="width:155px;">
					<select name="political" lay-filter="">
						<option value="">请选择政治面貌</option>
						#dictOption("political_status", model.political??'', "")
					</select>
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">职位</label>
				<div class="layui-input-inline" style="width:155px;">
					<div class="layui-form" lay-filter="positionSelectFilter">
						<select id="position" name="position" lay-search="">
							<option value="">请选择职位</option>
							#for(position : positionDictList)
								<option value="#(position.dictValue)" #if(model.position==position.dictValue)selected#end>#(position.dictName)</option>
							#end
						</select>
					</div>
				</div>
			</div>
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:265px;">
					<input type="text" id="positionName" class="layui-input" value="" placeholder="没有职位请输入名称回车保存">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">工号</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" name="workNum" class="layui-input" value="#(model.workNum??)" placeholder="请输入工号">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">手机号</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" name="phoneNum" class="layui-input" value="#(model.phoneNum??)" placeholder="请输入本人手机号码">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">紧急联系人</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" name="linkPeople1" class="layui-input" value="#(model.linkPeople1??)" placeholder="紧急联系人姓名">
				</div>
			</div>
			<div class="layui-inline">
				<label class="layui-form-label">联系手机</label>
				<div class="layui-input-inline" style="width:155px;">
					<input type="text" name="linkPhone1" class="layui-input" value="#(model.linkPhone1??)" placeholder="紧急联系人手机">
				</div>
			</div>
		</div>
		<div class="layui-form-item">
			<div class="layui-inline">
				<label class="layui-form-label">与本人关系</label>
				<div class="layui-input-inline" style="width:155px;">
					<select name="relation" lay-filter="">
						<option value="">请选择关系</option>
						#dictOption("relation_type", model.relation??'', "")
					</select>
				</div>
			</div>
			<div class="layui-inline">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">现住址</label>
			<div class="layui-input-block">
				<input type="text" name="residentAddr" class="layui-input" value="#(model.residentAddr??)" placeholder="请输入现住址">
			</div>
		</div>
		<div style="margin-bottom:60px;"></div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.Id??)">
				<input type="hidden" name="orgId" value="#(model.orgId??)">
				<input type="hidden" name="orgCurId" value="#(model.orgCurId??)">
				<input type="hidden" name="orgParentId" value="#(model.orgParentId??)">
				<input type="hidden" name="orgParentIds" value="#(model.orgParentIds??)">
				#if(userOrgId!=null)
					<input type="hidden" name="userType" value="#(model.user!=null?model.user.user_type:'ordinary_user')">
				#end
				<input type="hidden" id="roleIds" name="roleIds" value="">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, layer = layui.layer
	, laydate = layui.laydate
	, $ = layui.$
	;
	
	//出生日期渲染
	laydate.render({
		elem: '#birthday'
		, trigger: 'click'
	});
	
	$('#positionName').keydown(function(e){
		if(e.keyCode==13){
			var positionName = $("#positionName").val();
			if(positionName != null && positionName != ''){
				//提交表单数据
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/dict/save',
		            data: {dictType:'position', dictTypeName:'职位', dictName:positionName, remarks:'职位'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		var dictName = rep.dict.dictName;
							var dictValue = rep.dict.dictValue;
							$("#position").append("<option value='"+dictValue+"'>"+dictName+"</option>");
							form.render('select', 'positionSelectFilter');
				    	}
		            },
		            complete : function() {
				    }
		        });
			}else{
				layer.msg('请输入职位名称!',{icon:5});
			}
			return false;
		}
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/orgEmployee/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
	            	parent.pageTableReload();
	            	pop_close();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end