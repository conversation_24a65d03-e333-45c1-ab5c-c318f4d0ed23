#include("/template/common/layout/_page_layout.html")

#@layout()

#define pageTitle()用户分组编辑页面#end

#define css()

#end

#define content()
<div class="layui-row" style="margin-top:20px;">
	<form id="form" class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
				<label class="layui-form-label"><font color="red">*</font>分组名称</label>
				<div class="layui-input-block">
					<input type="text" id="groupName" name="groupName" class="layui-input" lay-verify="required" value="#(model.groupName??)" placeholder="请输入分组名称" autocomplete="off">
				</div>
			</div>
			<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
				<label class="layui-form-label">是否可用</label>
		        <div class="layui-input-block">
		            <input type="radio" name="isEnable" value="0" title="否" #if(model.isEnable??=='0')checked#end>
	        		<input type="radio" name="isEnable" value="1" title="是" #if(model.isEnable??''==''||model.isEnable??=='1')checked#end>
		        </div>
	        </div>
		</div>
		<div class="layui-form-item">
			<table class="layui-table">
				<thead>
					<tr>
						<th width="10%">序号</th>
						<th width="80%">用户</th>
						<th width="10%">操作</th>
					</tr>
				</thead>
				<tbody id="groupUserList">
					#for(groupUser : groupUserList??)
					<tr id="groupUser-#(for.index+1)">
						<td><input type="text" name="userList[#(for.index+1)].userSort" class="layui-input" value="#(groupUser.userSort??)" lay-verify="required" placeholder="请输入序号" autocomplete="off"></td>
						<td>
							<select id="userSelect-#(for.index+1)" name="userList[#(for.index+1)].userId" lay-verify="required" lay-search="">
								<option value="">直接选择或搜索选择</option>
								#for(user : userList)
									<option value="#(user.user_id)" #if(user.user_id==groupUser.userId)selected="selected"#end>#(user.full_name)</option>
								#end
							</select>
						</td>
						<td>
							<input type="hidden" name="userList[#(for.index+1)].id" value="#(groupUser.id??)">
							<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('groupUser-#(for.index+1)','#(groupUser.id??)')">作废</a>
						</td>
					</tr>
					#end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.id??'')">
				<input type="hidden" id="userCount" name="userCount" value="#(groupUserList.size()??0)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<div id="addBtn" class="layui-btn">添加用户</div>
				<button id="saveBtn" class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script id="groupUserTrTpl" type="text/html">
	<tr id="groupUser-{{d.idx}}">
		<td><input type="text" name="userList[{{d.idx}}].userSort" class="layui-input" value="" lay-verify="required" placeholder="请输入序号" autocomplete="off"></td>
		<td>
			<select id="userSelect-{{d.idx}}" name="userList[{{d.idx}}].userId" lay-verify="required" lay-search="">
				<option value="">直接选择或搜索选择</option>
			</select>
		</td>
		<td>
			<input type="hidden" name="userList[{{d.idx}}].id" value="">
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('groupUser-{{d.idx}}','')">作废</a>
		</td>
	</tr>
</script>
<script>
layui.use(['form','layer','laytpl'], function() {
	var $ = layui.$, form=layui.form,layer=layui.layer, laytpl = layui.laytpl;

	//添加模板方法
	addTpl = function(targetId, addTpl, idx) {
		$('#userCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1)}, function(html){
			targetId.append(html);
		});
		#for(user : userList)
			$('#userSelect-'+(parseInt(idx)+1)).append('<option value="#(user.user_id)")>#(user.full_name)</option>');
		#end
		form.render('select');
    };

	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		addTpl($('#groupUserList'), groupUserTrTpl.innerHTML, $('#userCount').val());
	});
    
	//删除方法
	del = function(trId, dataId) {
		if(dataId!=null && dataId!=''){
			layer.confirm('您确定要作废？', {icon: 3, title:'询问'}, function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/group/groupUserDel',
					data: {id:dataId},
					notice: true,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							$("#"+trId).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}else{
			$("#"+trId).remove();
		}
	};
    
	form.on('submit(saveBtn)',function (obj) {
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/group/save',
			data: $(obj.form).serialize(),
			notice: true,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					parent.tableReload("groupTable",{});
					pop_close();
				}
			},
			complete : function() {
			}
		});
		return false;
	});
});
</script>
#end