#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()行政区域管理首页#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'areaTable'
        ;
	
	//国家下拉选择监听
	form.on('select(country)', function(data) {
		var countryId = data.value;
		$('#parentId').val(countryId);
		$("#provinceId").empty();
		$("#provinceId").prepend("<option value=''>请选择</option>");
		$("#cityId").empty();
		$("#cityId").prepend("<option value=''>请选择</option>");
		$("#townId").empty();
		$("#townId").prepend("<option value=''>请选择</option>");
		form.render('select', 'citySelectFilter');
		form.render('select', 'townSelectFilter');
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:countryId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						$.each(rep.resultData, function(i, data){
							$("#provinceId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
						});
						form.render('select', 'provinceSelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
	//省下拉选择监听
	form.on('select(province)', function(data) {
		var provinceId = data.value;
		$('#parentId').val(provinceId);
		$("#cityId").empty();
		$("#cityId").prepend("<option value=''>请选择</option>");
		$("#townId").empty();
		$("#townId").prepend("<option value=''>请选择</option>");
		form.render('select', 'townSelectFilter');
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:provinceId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						$.each(rep.resultData, function(i, data){
							$("#cityId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
						});
						form.render('select', 'citySelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
	//县/市下拉选择监听
	form.on('select(city)', function(data) {
		var cityId = data.value;
		$('#parentId').val(cityId);
		$("#townId").empty();
		$("#townId").prepend("<option value=''>请选择镇/区</option>");
		form.render('select', 'townSelectFilter');
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:cityId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						$.each(rep.resultData, function(i, data){
							$("#townId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
						});
						form.render('select', 'townSelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
	//镇区下拉选择监听
	form.on('select(town)', function(data) {
		var townId = data.value;
		$('#parentId').val(townId);
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:townId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						var html='<div class="layui-form" lay-filter="streetSelectFilter">';
						html += '<select id="streetId" name="streetId" lay-search="">';
						html += '<option value="">请选择街道</option>';
						html += '</select>';
						html += '</div>';
						$("#streetDiv").html(html);
						if(!$('#streetDiv').is(":empty")){
							$.each(rep.resultData, function(i, data){
								$("#streetId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
							});
						}
						form.render('select', 'streetSelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
    // 初始化表格
    var tableObj=table.render({
        id: tableId
        , elem: '#'+tableId
        , url: '#(ctxPath)/area/areaTable'
    	, method: 'post'
   		, height: vipTable.getFullHeight()    //容器高度
        , cols: [[
        	 {checkbox: true, sort: false, fixed: false, space: false}
        	,{field: '', title: '序号', width: 60, unresize:true, templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'areaName', title:'区域名称', unresize:true}
			,{field:'areaCode', title:'行政代码', unresize:true}
			,{field:'areaLv', title:'级别', unresize:true}
    		,{field:'areaSort', title: '<button id="saveSortBtn" type="button" class="layui-btn layui-btn-xs">保存排序</button>', width:100, align:'center', templet: function(d){
				var sortInput='<input type="text" class="layui-input sortInput" data-id="'+d.id+'" value="'+d.areaSort+'" maxlength="7" autocomplete="off">';
					return sortInput;
				}
    		}
			,{title:'操作', width:200, unresize:true, align:'center', templet: function(d){
					var editBtn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
					var delBtn='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>';
					return editBtn+delBtn;
				}
			}
        ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
    });
	//表格事件绑定
	table.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/area/edit?id='+obj.data.id,'','');
        }else if(obj.event==="del"){//作废按钮事件
        	layer.confirm('你确定作废数据吗？如果存在下级节点则一并作废，此操作不能撤销！', {icon: 3, title:'提示'}, function(index) {
				//作废操作
				util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/del',
                    data: {id:obj.data.id},
                    notice: true,
                    loadFlag: true,
                    success : function(data){
                    	pageTableReload();
                    },
                    complete : function() {
                    	
                    }
                });
				layer.close(index);
			});
        }
    });
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{parentId:$('#parentId').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	//重置按钮点击事件
	$('#reSetBtn').on('click', function() {
		$('#parentId').val('');
	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		var parentId = $('#parentId').val();
		if(parentId!=null && parentId!=''){
			pop_show('添加','#(ctxPath)/area/add?parentId='+parentId,'','');
		}else{
			layer.msg('请选择区域!',{icon:5});
		}
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
	//保存排序按钮点击事件
	$('#saveSortBtn').on('click', function() {
		sortSave();
	});
	sortSave = function() {
		var saveFlag = true;
		var dataArray = new Array();
		$(".sortInput").each(function(i,obj){
			var areaId = $(this).attr('data-id');
			var areaSort = obj.value;
		    if(areaSort==null || areaSort==''){
		    	saveFlag = false;
		    	return false;
		    }else{
		    	var inputObj = {'id':areaId, 'areaSort':areaSort};
		    	dataArray.push(inputObj);
		    }
		});
		if(saveFlag){
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/area/sortBatchSave',
	            data: {sortDatas:JSON.stringify(dataArray)},
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		pageTableReload();
	            	}
	            },
	            complete : function() {
	            	//保存按钮点击事件
					$('#saveSortBtn').on('click', function() {
						sortSave();
					});
			    }
	        });
		}else{
			layer.msg('排序不能为空，请输入排序!',{icon:5});
		}
	}
});
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
	    		<div class="layui-inline">
			        <label class="layui-form-label" style="width:80px;">国家：</label>
			        <div class="layui-input-inline" style="width:150px;">
			            <select name="countryId" lay-filter="country" lay-search="">
							<option value="">请选择</option> 
							#areaOption("0", '')
						</select>
			        </div>
	    		</div>
		    	<div class="layui-inline">
			        <label class="layui-form-label" style="width:80px;">省：</label>
			        <div class="layui-input-inline" style="width:150px;">
			        	<div class="layui-form" lay-filter="provinceSelectFilter">
				        <select id="provinceId" name="provinceId" lay-filter="province" lay-search="">
							<option value="">请选择</option>
						</select>
			        	</div>
			        </div>
				</select>
	    		</div>
		    	<div class="layui-inline">
		    		<label class="layui-form-label" style="width:80px;">县/市：</label>
	    			<div class="layui-input-inline" style="width:150px;">
		    			<div class="layui-form" lay-filter="citySelectFilter">
				        <select id="cityId" name="cityId" lay-filter="city" lay-search="">
							<option value="">请选择</option> 
						</select>
	    				</div>
		    		</div>
	    		</div>
	    		<div class="layui-inline">
	    			<label class="layui-form-label" style="width:80px;">镇/区：</label>
	    			<div class="layui-input-inline" style="width:150px;">
	    				<div class="layui-form" lay-filter="townSelectFilter">
		    			<select id="townId" name="townId" lay-filter="town" lay-search="">
							<option value="">请选择</option> 
						</select>
	    				</div>
	    			</div>
	    		</div>
		        <div class="layui-inline">
		        	<div class="layui-input-inline">
		        		<div class="layui-btn-group">
		        			<input type="hidden" id="parentId" name="parentId" value="">
					        <button type="button" id="searchBtn" class="layui-btn"><i class="layui-icon">&#xe615;</i></button>
					        <button type="reset" id="reSetBtn" class="layui-btn layui-btn-primary btn-reset">重置</button>
		        		</div>
		        	</div>
	    		</div>
			</form>
	    </span>
	    <span class="fr">
	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
<!-- 				        <a type="button" id="batchDelBtn" class="layui-btn layui-btn-danger radius btn-delect">批量作废</a> -->
				        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
	<div class="layui-row">
		<table id="areaTable" lay-filter="areaTable"></table>
	</div>
</div>
#end