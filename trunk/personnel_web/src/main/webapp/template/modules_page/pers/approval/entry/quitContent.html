<table class="layui-table">
    <tbody>
        <tr>
            <input id="currentStepAlias" value="#(currentStepAlias??)" type="hidden">
            <td colspan="6" align="center">员工辞职审批表</td>
        </tr>
        <tr>
            <td>姓名</td>
            <td>#(record.fullName??)</td>
            <td>工号</td>
            <td>#(record.workNum??)</td>
            <td>部门</td>
            <td>#(record.deptName??)</td>
        </tr>
        <tr>
            <td>入职时间</td>
            <td>#date(record.entryTime??,'yyyy-MM-dd')</td>
            <td>转正时间</td>
            <td>#date(record.formalDate??,'yyyy-MM-dd')</td>
            <td>合同有效期至</td>
            <td></td>
        </tr>
        <tr>
            <td>职位</td>
            <td>#(record.positionName??)</td>
            <td>申请日期</td>
            <td>#date(record.create_date??,'yyyy-MM-dd')</td>
            <td>预计离职日期</td>
            <td>#date(record.estimate_quit_date??,'yyyy-MM-dd')</td>
        </tr>
        <tr>
            <td>离职原因</td>
            <td>
                #if(record.quit_type=='dismiss')
                辞退
                #else if(record.quit_type=='autonomy')
                自离
                #else if(record.quit_type=='expel')
                开除
                #else if(record.quit_type=='other')
                其他
                #else if(record.quit_type??=='auto_quit')
                自动离职
                #else if(record.quit_type??=='voluntarily_quit')
                个人离职
                #else if(record.quit_type??=='resign')
                辞职
                #end

            </td>
            <td>交接人</td>
            <td colspan="3">
                #(record.transferWorkNum??)(#(record.transferFullName??))
            </td>
        </tr>
        <tr>
            <td colspan="6">辞职原因</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea   class="layui-textarea" disabled  style="color: #000;">#(record.reason??)</textarea>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="6">离职员工对公司的建议</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea   class="layui-textarea" disabled style="color: #000;">#(record.proposal??)</textarea>
                    </div>
                </div>
            </td>
        </tr>
        #if(stepCode??0>=1)
        <tr>
            <td colspan="6">用人部意见</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="deptOpinion" style="color: #000;" id="deptOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep1!=currentStepAlias?? || !isHandle) disabled #end>#(record.dept_opinion??)</textarea>
                    </div>
                </div>
                <div class="layui-row pull-right">
                    #if(quitStep1==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQuitDeptOpinion" lay-filter="saveQuitDeptOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.deptHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.dept_date??,'yyyy-MM-dd')</label>
                    #end
                </div>

            </td>
        </tr>
        #end

        #if(stepCode??0>=2)
        <tr>
            <td colspan="6">集团HR意见</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="baseOpinion" id="baseOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep2!=currentStepAlias?? || !isHandle) disabled #end>#(record.base_opinion??)</textarea>
                    </div>
                </div>
                <div class="layui-row pull-right">
                    #if(quitStep2==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQuitBaseOpinion" lay-filter="saveQuitBaseOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.baseHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.base_date??,'yyyy-MM-dd')</label>
                    #end
                </div>

            </td>
        </tr>
        #end

        <!--#if(stepCode??0>=3)
        <tr>
            <td colspan="6">行政部经理意见</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="hrOpinion" style="color: #000;" id="hrOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep3!=currentStepAlias?? || !isHandle) disabled #end>#(record.hr_opinion??)</textarea>
                    </div>
                </div>
                <div class="layui-row">
                    是否面谈：
                    <div class="layui-input-inline">
                        <select name="hrUseOpinion" id="hrUseOpinion" #if(quitStep3!=currentStepAlias?? || !isHandle) disabled #end>
                            <option value="">请选择</option>
                            <option value="no_interview" #if(record.hr_use_opinion??=='no_interview') selected #end>未面谈</option>
                            <option value="interviewed" #if(record.hr_use_opinion??=='interviewed') selected #end>已面谈</option>
                        </select>
                    </div>
                    是否同意：
                    <div class="layui-input-inline">
                        <select name="hrIsAgree" id="hrIsAgree" #if(quitStep3!=currentStepAlias?? || !isHandle) disabled #end>
                            <option value="">请选择</option>
                            <option value="1" #if(record.hr_is_agree??=='1') selected #end>是</option>
                            <option value="0" #if(record.hr_is_agree??=='0') selected #end>否</option>
                        </select>
                    </div>
                </div>
                <div class="layui-row pull-right">

                    #if(quitStep3==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQuitHrOpinion" lay-filter="saveQuitHrOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.hrHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.hr_date??,'yyyy-MM-dd')</label>
                    #end
                </div>
            </td>
        </tr>
        #end-->

        <!--#if(stepCode??0>=4)
        <tr>
            <td colspan="6">总经理意见</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="generalManagerOpinion" style="color: #000;" id="generalManagerOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep4!=currentStepAlias?? || !isHandle) disabled #end>#(record.general_manager_opinion??)</textarea>
                    </div>
                </div>
                <div class="layui-row pull-right">
                    #if(quitStep4==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQuitGeneralManagerOpinion" lay-filter="saveQuitGeneralManagerOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.generalManagerHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.general_manager_date??,'yyyy-MM-dd')</label>
                    #end
                </div>

            </td>
        </tr>
        #end-->
        <tr>
            <td colspan="6">备注</td>
        </tr>
        <tr>
            <td colspan="6">
                <div class="layui-row">
                    1、辞退人必须提前__天申请，转正员工须提前   天并填写此表，逐级审批，并在审批后交给行政部，否则按急辞处理。
                </div>
                <div class="layui-row">
                    2、离职当日，按《离职交接表》进行交接
                </div>
                <div class="layui-row">
                    3、此审批表结束后，交行政部存档。
                </div>
            </td>
        </tr>
    </tbody>
</table>