#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
<style>
	/*.layui-form-select dl {
		min-height: 300px;  !* 这里根据实际情况设置相应的下拉弹出选项框的最大高度 *!
	}*/
	.layui-form-select dl {
		max-height: 300px;
	}
</style>
#end

#define content()
<div class="layui-row layui-col-space10">
	<div class="layui-col-xs4 layui-col-sm4 layui-col-md4">
		<fieldset class="layui-elem-field layui-field-title" style="display:block;">
			<legend>组织架构</legend>
			<div id="zTreeDiv" class="ztree" style="height:550px;overflow:auto;"></div>
		</fieldset>
	</div>
	<div class="layui-col-xs8 layui-col-sm8 layui-col-md8">
		<div style="margin-bottom: 20px;"></div>
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<label class="layui-form-label">上级机构</label>
				<div class="layui-input-block">
					<input type="text" id="parentName" class="layui-input" value="#(parentOrg.orgName??'顶级机构')" readonly="readonly">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>部门名称</label>
				<div class="layui-input-block">
					<input type="text" name="orgName" class="layui-input" lay-verify="required" value="#(model.orgName??)" placeholder="请输入部门名称">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>类型</label>
				<div class="layui-input-block">
					<select name="orgType" id="orgType" lay-filter="orgType" lay-verify="required">
						<option value="">请选择类型</option>
						#dictOption("org_type", model.orgType??'', "main_org")
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>是否bu</label>
				<div class="layui-input-block">
					<select name="isBu" lay-filter="" lay-verify="required">
						<option value="">请选择</option>
						<option value="1" #if(model.isBu??=='1') selected #end>是</option>
						<option value="0" #if(model.isBu??=='0') selected #end>否</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">维度</label>
				<div class="layui-input-block">
					<select id="orgCategoryId" name="orgCategoryId">
		                #for(orgCateGoryType : orgCateGoryTypeMap)
		                	<option value="#(orgCateGoryType.key)" #if(orgCateGoryType.key==model.orgCategoryId??) selected #end>#(orgCateGoryType.value)</option>
		                #end
		            </select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label">排序</label>
				<div class="layui-input-block">
					<input type="text" name="orgSort" class="layui-input" value="#(model.orgSort??)" lay-verify="number" placeholder="请输入排序">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>负责人</label>
				<div class="layui-input-block">
					<select id="linkMan" name="linkMan" lay-verify="required" lay-search>
						<option value="">请选择负责人(可搜索)</option>
						#for(user : userList)
						<option value="#(user.id)" #if(user.id==model.linkMan??) selected #end>#(user.name)(#(user.userName))</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>人事专员</label>
				<div class="layui-input-block">
					<select id="hrUserId" name="hrUserId" lay-verify="required" lay-search>
						<option value="">请选择人事专员(可搜索)</option>
						#for(user : userList)
						<option value="#(user.id)" #if(user.id==model.hrUserId??) selected #end>#(user.name)(#(user.userName))</option>
						#end
					</select>
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width: 140px;padding: 8px 5px;"><font color="red"></font>上午工作时长(小时)</label>
				<div class="layui-input-block" style="margin-left: 140px">
					<input type="text" name="amWorkHour" lay-verify="" class="layui-input" value="#(model.amWorkHour??)" autocomplete="off" placeholder="请输入上午工作时长">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="width: 140px;padding: 8px 5px;"><font color="red"></font>下午工作时长(小时)</label>
				<div class="layui-input-block" style="margin-left: 140px">
					<input type="text" name="pmWorkHour" lay-verify="" class="layui-input" value="#(model.pmWorkHour??)" autocomplete="off" placeholder="请输入下午工作时长">
				</div>
			</div>

			<div class="layui-form-item">
				<label class="layui-form-label" style="width: 160px;padding: 8px 5px;"><font color="red"></font>补休假允许最小天数</label>
				<div class="layui-input-block" style="margin-left: 160px">
					<input type="text" name="fillLeaveMinValue" lay-verify="integer" class="layui-input" autocomplete="off" value="#(model.fillLeaveMinValue??)" placeholder="请输入补休假允许最小天数">
				</div>
			</div>

			<div class="layui-form-item" >
				<label class="layui-form-label" style="width: 130px;padding: 8px 5px;"><font color="red">*</font>是否限制员工人数</label>
				<div class="layui-input-block" style="margin-left: 130px">
					<select name="isRestrict" lay-filter="">
						<option value="">请选择</option>
						<option value="1" #if(model.isRestrict??=='1') selected #end>是</option>
						<option value="0" #if(model.isRestrict??=='0') selected #end>否</option>
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label" style="padding: 8px 5px;"><font color="red"></font>编制人数</label>
				<div class="layui-input-block">
					<input type="text" name="headcount" lay-verify="integer" class="layui-input" autocomplete="off" value="#(model.headcount??)" placeholder="请输入编制人数">
				</div>
			</div>

			<div class="layui-form-item" id="joinBase" #if(model.orgType??=='pension_base' || model.orgType??=='sojourn_hotel' ) #else  style="display:none;" #end>
				<label class="layui-form-label"><font color="red"></font>关联基地</label>
				<div class="layui-input-block">
					<select id="joinBaseSelect" name=""  lay-search #if(model.orgType??=='pension_base' || model.orgType??=='sojourn_hotel' ) lay-verify="" #else  lay-verify="" #end>
						<option value="">请选择关联基地(可搜索)</option>
						#for(user : baseList)
						<option value="#(user.id)" #if(user.id==model.joinId??) selected #end>#(user.baseName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-form-item" id="joinBranche" #if(model.orgType??=='branche_office' ) #else  style="display:none;" #end>
				<label class="layui-form-label"><font color="red"></font>关联分公司</label>
				<div class="layui-input-block">
					<select id="joinBranchSelect" name=""  lay-search #if(model.orgType??=='branche_office') lay-verify="" #else  lay-verify="" #end>
						<option value="">请选择关联分公司(可搜索)</option>
						#for(user : branchOfficeList)
						<option value="#(user.id)" #if(user.id==model.joinId??) selected #end>#(user.fullName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-form-item" >
				<label class="layui-form-label">是否订餐</label>
				<div class="layui-input-block">
					<select name="isDine" lay-filter="">
						<option value="">请选择</option>
						<option value="1" #if(model.isDine??=='1') selected #end>是</option>
						<option value="0" #if(model.isDine??=='0') selected #end>否</option>
					</select>
				</div>
			</div>
			<div style="height: 50px;">

			</div>
			<!--<div class="layui-form-item">
				<label class="layui-form-label">负责人电话</label>
				<div class="layui-input-block">
					<input type="text" name="linkPhone" class="layui-input" value="#(model.linkPhone??)" placeholder="请输入负责人电话">
				</div>
			</div>-->
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??)"> 
					<input type="hidden" id="parentId" name="parentId" value="#(model.parentId??parentOrg.id??'')">
					<input type="hidden" name="qiyeId" value="#(model.qiyeId??)">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	;
	
	var setting = {
		check:{enable:false}
		,view:{selectedMulti:false}
		,data:{simpleData:{enable:true}}
		,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTree"}
		,callback:{
			onClick: function(event, treeId, treeNode, clickFlag) {
				$("#parentId").val(treeNode.id);
				$("#parentName").val(treeNode.name);
			}
		}
   	};

	form.verify({
		integer: [/^(\-?)\d+$|^$/, "只能填写整数"]
	})

	form.on('select(orgType)', function(obj) {
		var value=obj.value;
		console.log(obj.value);
		console.log(obj);
		//提交表单数据
		if(value=='branche_office'){
			$("#joinBranche").css("display","block");
			$("#joinBase").css("display","none");
			$("#joinBaseSelect").attr("lay-verify","");
			$("#joinBranchSelect").attr("lay-verify","");
		}else if(value=='pension_base' || value=='sojourn_hotel'){
			$("#joinBranche").css("display","none");
			$("#joinBase").css("display","block");
			$("#joinBaseSelect").attr("lay-verify","");
			$("#joinBranchSelect").attr("lay-verify","");
		}else{
			$("#joinBranche").css("display","none");
			$("#joinBase").css("display","none");
			$("#joinBaseSelect").attr("lay-verify","");
			$("#joinBranchSelect").attr("lay-verify","");
		}
	});

	// 初始化树结构
   	var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var data=$(formObj.form).serialize();
		if($("#orgType").val()=='branche_office'){
			data+="&joinId="+$("#joinBranchSelect").val();
		}else if($("#orgType").val()=='pension_base' || $("#orgType").val()=='sojourn_hotel') {
			data+="&joinId="+$("#joinBaseSelect").val();
		}else{

		}

		//提交表单数据
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/persOrg/save',
		    data: data,
		    notice: true,
		    loadFlag: true,
		    success : function(rep){
		    	if(rep.state=='ok'){
			    	parent.tableGridReload();
			    	pop_close();
		    	}
		    },
		    complete : function() {
		    }
		});
		return false;
	});
});
</script>
#end