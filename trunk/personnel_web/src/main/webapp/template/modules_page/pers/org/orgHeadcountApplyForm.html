#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });




        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(headcountApply!=null)
            deptSelect.setValue(["#(headcountApply.deptId??)"]);
            #end
        });


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            deptId=deptSelect.getValue()[0].id;
            if(deptId==null || deptId==''){
                layer.msg('请选择组织', {icon: 2, offset: 'auto'});
                return false;
            }

            var remark=$("#remark").val();
            if(remark=='' || remark==undefined){
                layer.msg('备注不能为空', {icon: 2, offset: 'auto'});
                return false;
            }

            var data=$("#noticeForm").serialize()+"&deptId="+deptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/persOrg/orgHeadcountApplySave',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        form.on('select(destinationType)',function (obj) {
            if(obj.value=='1'){
                $("#deptDiv").css("display","block");
                $("#deptDiv2").css("display","none");
            }else{
                $("#deptDiv").css("display","none");
                $("#deptDiv2").css("display","block");
            }

        })

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            deptId=deptSelect.getValue()[0].id;
            if(deptId==null || deptId==''){
                layer.msg('请选择组织', {icon: 2, offset: 'auto'});
                return false;
            }

            var remark=$("#remark").val();
            if(remark=='' || remark==undefined){
                layer.msg('备注不能为空', {icon: 2, offset: 'auto'});
                return false;
            }


            var data=$("#noticeForm").serialize()+"&saveType=2&deptId="+deptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/persOrg/orgHeadcountApplySave',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });


        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }

    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div class="layui-col-md6 layui-form" id="content" style="padding-right: 10px;">
            <div class="layui-collapse" >

                <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item" style="display: none;">
                        <label class="layui-form-label"><font color="red">*</font>出差员工</label>
                        <div class="layui-input-inline">

                            <input id="id" name="id" type="hidden" value="#(headcountApply.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(model==null)#(createBy??)#else#(model.createBy??)#end">
                        </div>

                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>

                    #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
                    <!--<fieldset class="layui-elem-field layui-field-title" style="display:block;padding-left: 20px;">
                        <legend>流程提交人信息</legend>-->
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交人姓名</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交人部门</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交人职位</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>

                    <!--</fieldset>-->
                    #end

                    <div class="layui-form-item"  >
                        <label class="layui-form-label"><font color="red">*</font>组织</label>
                        <div class="layui-input-inline" style="width: 387px;">
                            <div id="deptSelect"  name="deptId" style="margin-top: 1px;" >

                            </div>
                        </div>
                        <div class="layui-input-inline" style="width: 387px;">
                           #(deptName??)
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>类型</label>
                        <div class="layui-input-inline">
                            <select id="type" name="type" lay-filter="type" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="1" #if(headcountApply.type??=='1') selected #end >增加</option>
                                <option value="2" #if(headcountApply.type??=='2') selected #end >减少</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>人数</label>
                        <div class="layui-input-inline">
                            <input type="text" name="headcount" id="headcount" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#(headcountApply.headcount??)"  lay-verify="required|number" placeholder="请输入人数" autocomplete="off" class="layui-input">

                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text" style="margin-bottom: 60px;">
                        <label class="layui-form-label"><font color="red">*</font>备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(headcountApply.remark??)</textarea>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="layui-col-md6">
            #if(stepts.size()??0>0)
            <table class="layui-table" id="steptsTable">
                <!--<colgroup>
                    <col width="10%">
                    <col width="13%">
                    <col width="30%">
                    <col width="30%">
                    <col width="17%">
                </colgroup>-->
                <thead>
                <tr>
                    <th style="text-align: center;">序号</th>
                    <th style="text-align: center;">节点</th>
                    <th style="text-align: center;">流程</th>
                    <th style="text-align: center;">意见</th>
                    <th style="text-align: center;">操作时间</th>
                    <th style="text-align: center;">操作人</th>
                </tr>
                </thead>
                <tbody>
                #for(stept:stepts)
                <tr>
                    <td align="center">#(for.index+1)</td>
                    <td>#(stept.ActivityName)</td>
                    <td align="center">
                        #if(stept.StepState==3)
                        提交
                        #else if(stept.StepState==1)
                        待处理
                        #else if(stept.StepState==0)
                        等待
                        #else if(stept.StepState==2)
                        正处理
                        #else if(stept.StepState==4)
                        撤回
                        #else if(stept.StepState==5)
                        批准
                        #else if(stept.StepState==6)
                        拒绝
                        #else if(stept.StepState==7)
                        转移
                        #else if(stept.StepState==8)
                        失败
                        #else if(stept.StepState==9)
                        跳过
                        #end
                    </td>
                    <td>#(stept.Comment)</td>
                    <td>#(stept.CommentTime)</td>
                    <td align="center">#(stept.CommentUserName)</td>
                </tr>
                #end
                </tbody>
            </table>

            <form class="layui-form layui-form-pane" id="taskForm">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">意见</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block pull-right">

                        <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                        <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>
                        <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject) disabled style="display: none;" #end>拒绝</button>
                        <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove) disabled style="display: none;" #end>通过</button>
                    </div>
                </div>
            </form>
            #end
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                #if(isSaveHandle || taskId==null)
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                #end
                #if(isSaveHandle || taskId==null)
                <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
                #end
                <input type="hidden" id="taskId" value="#(taskId??)">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
            <div class="pull-right">
                <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
            </div>

        </div>
    </form>
</div>

</body>
#end