#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公积金购买申请流程#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
    .layui-disabled, .layui-disabled:hover {
        color: #000000 !important;
        cursor: not-allowed !important;
    }
    .layui-input {
        color: #000;
    }
    .layui-col-md8 .layui-table td,.layui-col-md8 .layui-table th {
        position: relative;
        padding: 9px 5px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }
    .layui-btn-red{
        background-color: red;
    }
    .layui-form-item .layui-inline {
        margin-bottom: 5px;
        margin-right: 0px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery','table','laydate'], function(){
        var form = layui.form,$ = layui.jquery,table=layui.table,laydate=layui.laydate;

        laydate.render({
            elem: '#detailDate'
            //,range: true //或 range: '~' 来自定义分割字符
            ,trigger:'click'
        });

        remove=function(empId,type){

            $("#entryApprovalTable3").find("tr[data-id='"+empId+"']").remove();

        }



        $("#saveBtn").on('click',function () {
            var data=[];
            $("#delTbody").find("tr").each(function (index,item) {
                let empId=$(item).attr("data-id");
                let leaveType=$("#leaveType-"+empId).val();
                let type=$("#type-"+empId).val();
                let detailDate=$("#detailDate-"+empId).val();
                let day=$("#day-"+empId).val();
                let hour=$("#hour-"+empId).val();
                let remark=$("#remark-"+empId).val();

                if(!(/^\d+$/.test(String(day)))){
                    layer.msg('天数值请输入正整数', {icon: 5});
                    return false;
                }
                if(!(/^\d+(.(0|5))?$/.test(String(hour)))){
                    layer.msg('小时值请输入正整数或正1位小数点', {icon: 5});
                    return false;
                }

                data.push({empId,leaveType,type,detailDate,day,hour,remark});
            });

            if(data.length<=0){
                layer.msg('操作失败，提交的记录条数必须大于0条',{icon:5});
                return false;
            }
            var taskId=$("#taskId").val();

            var url="#(ctxPath)/employeeLeaveBalance/batchSaveLeaveBalance"

            util.sendAjax ({
                type: 'POST',
                url: url,
                data: {"data":JSON.stringify(data)},
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
        })

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/employeeLeaveBalance/empTable';
            pop_show("选择员工",url,'1300','600');
        });

        form.on('select(leaveType)',function (obj) {
            let selectId=obj.elem.id;
            let empId=obj.elem.id.replace('leaveType-','');
            let unit=$("#"+selectId+" option[value='"+obj.value+"']").attr("unit");
            if(unit=='1'){
                //$("#p-"+empId).css('display','none')
                //$("#hour-"+empId).css('display','block')
                $("#hour-"+empId).val(0);
                $("input[name='hour']").prop('disabled',false);
                //$("input[name='hour']").parent().addClass('layui-disabled');

            }else if(unit=='3'){
                //$("#p-"+empId).css('display','block')
                //$("#hour-"+empId).css('display','none')
                $("#hour-"+empId).val(0);
                $("input[name='hour']").prop('disabled',true);
                //$("input[name='hour']").parent().removeClass('layui-disabled');
            }
        })

        addTr=function(empId,workNum,fullName,sex,deptName,positionName){

            if($("tr[data-id='"+empId+"']").length>0){
                window.top.layer.msg('选择失败，该员工已经选择过了',{icon:5});
                return false;
            }

            let sexStr='';
            if(sex=='female'){
                sexStr='女';
            }else if(sex=='male'){
                sexStr='男';
            }
            let html=`<tr data-id='`+empId+`'>
                        <input type="hidden" id="empId" value="`+empId+`" >
                        <td>`+deptName+`</td>
                        <td>`+workNum+`</td>
                        <td>`+fullName+`</td>
                        <td>`+sexStr+`</td>
                        <td>
                            <div class="layui-input-inline" style="width: 110px;">
                                <select name="leaveType" id="leaveType-`+empId+`"  lay-filter="leaveType" lay-verify="">
                                    <option value="">假期类型</option>
                                    #for(leaveType : leaveTypes)
                                       <option value="#(leaveType.key)" unit="#(leaveType.unit)">#(leaveType.value)</option>
                                    #end
                                </select>
                            </div>
                        </td>
                        <td>
                            <div class="layui-input-inline" style="width: 110px;">
                                <select name="type" id="type-`+empId+`" lay-filter="" lay-verify="">
                                    <option value="">操作类型</option>
                                    <option value="1">增加</option>
                                    <option value="2">扣除</option>
                                    <option value="3">修改</option>
                                </select>
                            </div>
                        </td>
                        <td>
                            <div class="layui-input-inline" style="width: 130px;">
                                <input type="text" name="detailDate" id="detailDate-`+empId+`" class="layui-input" lay-verify="required" autocomplete="off" value="" placeholder="请选择产生日期">
                            </div>
                        </td>
                        <td>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" name="day" id="day-`+empId+`" class="layui-input" lay-verify="required|number" autocomplete="off" value="0" placeholder="请输入天数">
                            </div>
                        </td>
                        <td>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="text" name="hour" id="hour-`+empId+`"  class="layui-input" lay-verify="required|number" autocomplete="off" value="0" placeholder="请输入小时">
                                <p id="p-`+empId+`" style="display: none;">该假期类型最小使用单位为天</p>
                            </div>
                        </td>
                        <td>
                            <div class="layui-input-inline" style="width: 180px;">
                                <input type="text" name="remark" id="remark-`+empId+`"  class="layui-input" lay-verify="required" autocomplete="off" value="" placeholder="请输入备注">
                            </div>
                        </td>
                        <td>
                            <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="remove('`+empId+`')">移除</button>
                        </td>
                    </tr>`;

            $("#entryApprovalTable3").append(html);
            /*$("#maritalStatus"+empId).find("option[value='"+maritalStatus+"']").prop("selected",true);
            $("#isLocal"+empId).find("option[value='"+isLocal+"']").prop("selected",true);*/

            laydate.render({
                elem: '#detailDate-'+empId
                //,range: true //或 range: '~' 来自定义分割字符
                ,trigger:'click'
            });

            form.render();
            window.top.layer.msg('添加成功',{icon:1});
        }





        form.on('select(assignLeaveType)', function(obj){
            let unit=$("#leaveType option[value='"+obj.value+"']").attr("unit");
            if(unit=='1'){
                $("#hour").prop('disabled',false);
            }else if(unit=='3'){
                $("#hour").prop('disabled',true);
            }
        });

        $("#assignBtn").on('click',function () {
            var leaveType=$("#leaveType").val();
            var type=$("#type").val();
            var detailDate=$("#detailDate").val();
            var day=$("#day").val();
            var hour=$("#hour").val();
            var remark=$("#remark").val();
            if(!(/^\d+$/.test(String(day)))){
                layer.msg('天数值请输入正整数', {icon: 5});
                return false;
            }
            if(!(/^\d+(.(0|5))?$/.test(String(hour)))){
                layer.msg('小时值请输入正整数或正1位小数点', {icon: 5});
                return false;
            }

            var unit=$("#leaveType option[value='"+leaveType+"']").attr("unit");

            if(unit=='1'){
                //开启小时输入框
                $("#hour").prop('disabled',false);
            }else if(unit=='3'){
                //禁用小时输入框
                $("#hour").prop('disabled',true);
            }
            $('select[name="leaveType"] option[value="'+leaveType+'"]').prop('selected',true);
            /*
            $.each($('select[name="leaveType"]'),function (index,item) {
                layui.event("form", "select(leaveType)", {value: leaveType});
            })*/


            $('select[name="type"] option[value="'+type+'"]').prop('selected',true);
            $('input[name="detailDate"]').val(detailDate);
            $('input[name="day"]').val(day);
            $('input[name="hour"]').val(hour);
            $('input[name="remark"]').val(remark);
            form.render('select');
        })


        //layuiTableMouseenter();

        /*function layuiTableMouseenter() {
            $(".layui-table").each(function (index,item) {
                var tableId=$(item).attr("id");
                if(tableId.indexOf("entryApprovalTable")!=-1){
                    $(item).find("tbody tr").each(function (trIndex,trItem) {
                        var nameTd=$(trItem).children("td").eq(2);

                        var layerTips;

                        var sex=$(nameTd).attr('sex');
                        var deptName=$(nameTd).attr('deptName');
                        var positionName=$(nameTd).attr('positionName');

                        nameTd.on("mouseenter", function() {
                            /!*if (this.offsetWidth < this.firstChild.scrollWidth) {

                                var text = $(this).text();

                            }*!/
                            var that = this;

                            layerTips=layer.tips('<p>性别：'+sex+'</p><p>部门：'+deptName+'</p><p>职位：'+positionName+'</p>', that, {
                                tips: 1,
                                time: 0,
                                area: ['300px', 'auto']
                            });
                        });

                        nameTd.on("mouseleave", function() {
                            layer.close(layerTips);
                        });


                    })
                }
            });
        }*/



    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left:5px;" id="positionForm">
    <div class="layui-row" style="text-align: right;">
        <button class="layui-btn" id="addBtn" type="button">选择员工</button>
    </div>
    <div class="layui-row">
        <table id="entryApprovalTable3" lay-filter="entryApprovalTable3" class="layui-table">
            <colgroup>

            </colgroup>
            <thead>
            <tr>
                <th>部门</th>
                <th>工号</th>
                <th>姓名</th>
                <th>性别</th>
                <th><font color="red">*</font>假期类型</th>
                <th><font color="red">*</font>操作类型</th>
                <th><font color="red">*</font>产生日期</th>
                <th><font color="red">*</font>天数</th>
                <th><font color="red">*</font>小时</th>
                <th>备注</th>
                <th>操作</th>
            </tr>
            </thead>
            <tbody id="delTbody">

            </tbody>
        </table>
    </div>

    <div class="layui-form-footer">
        <div class="pull-left">
            <div class="layui-form-item" style="margin-bottom: 5px;">
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 90px;"><font color="red"></font>假期类型</label>
                    <div class="layui-input-inline" style="width: 120px;">
                        <select id="leaveType" lay-filter="assignLeaveType">
                            <option value="">假期类型</option>
                            #for(leaveType : leaveTypes)
                            <option value="#(leaveType.key)" unit="#(leaveType.unit)">#(leaveType.value)</option>
                            #end
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 90px;"><font color="red"></font>操作类型</label>
                    <div class="layui-input-inline" style="width: 120px;">
                        <select id="type" lay-filter="type">
                            <option value="">操作类型</option>
                            <option value="1">增加</option>
                            <option value="2">扣除</option>
                            <option value="3">修改</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 90px;"><font color="red"></font>发生日期</label>
                    <div class="layui-input-inline" style="width: 120px;">
                        <input type="text"   id="detailDate" class="layui-input" lay-verify="" autocomplete="off" value="" placeholder="请输入天数">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 90px;"><font color="red"></font>天数</label>
                    <div class="layui-input-inline" style="width: 80px;">
                        <input type="text"   id="day" class="layui-input" lay-verify="" autocomplete="off" value="#(record.day??0)" placeholder="请输入天数">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 90px;"><font color="red"></font>小时</label>
                    <div class="layui-input-inline" style="width: 80px;">
                        <input type="text"   id="hour" class="layui-input" lay-verify="" autocomplete="off" value="#(record.day??0)" placeholder="请输入天数">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="width: 90px;"><font color="red"></font>备注</label>
                    <div class="layui-input-inline" style="width: 140px;">
                        <input type="text"   id="remark" class="layui-input" lay-verify="" autocomplete="off" value="#(record.day??0)" placeholder="请输入天数">
                    </div>
                </div>
            </div>
        </div>
        <div class="pull-right">


                <div class="layui-inline">
                    <button class="layui-btn" type="button" id="assignBtn">统一赋值</button>
                    <button class="layui-btn" type="button" id="saveBtn">保存</button>
                    <button class="layui-btn layui-btn-danger" type="button" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </div>
    </div>
</form>
#end
