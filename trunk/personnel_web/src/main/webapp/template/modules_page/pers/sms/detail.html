#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()

<div class="layui-row">
    <div class="layui-col-md12" style="margin: 10px auto;">
        <input type="hidden" id="id" value="#(id??)">
        <table class="layui-table" id="positionTable" lay-filter="positionTable"></table>
    </div>
</div>

#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        sd=form.on("submit(search)",function(data){
            //positionLoad();
            positionTableReload();
            return false;
        });

        positionLoad({"id":$("#id").val()});

        function positionLoad(data){
            table.render({
                id : 'positionTable'
                ,elem : '#positionTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,height: 'full-100'
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/pers/sms/sendDetailPageList'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'work_num', title: '工号', align: 'center', unresize: true}
                    ,{field:'full_name', title: '姓名', align: 'center', unresize: true}
                    ,{field:'telephone', title: '号码', align: 'center', unresize: true}
                    ,{field:'status', title: '状态', align: 'center', unresize: true,templet:function (d) {
                            if(d.status=='1'){
                                return '发送成功';
                            }else{
                                return '发送失败';
                            }
                        }}

                ]]
                ,page : true
            });
        };

        positionTableReload=function(){
            var data={'positionName':$("#positionName").val(),'orgId':$("#orgId").val()};
            table.reload('positionTable',{'where':data});
        }

        // 添加
        $("#addBtn").click(function(){

            var url = "#(ctxPath)/pers/sms/form";
            pop_show("新增短信",url,600,600);
        });

        table.on('tool(positionTable)',function(obj){
            if (obj.event === 'del') {
                var sendTime=new Date(obj.data.sendTime);
                if(new Date().getTime()>=sendTime){
                    top.layer.msg('该短信发送时间已过，作废失败', {icon: 2, offset: 'auto'});
                    return false;
                }

                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/sms/saveSmsSend?id='+obj.data.id+"&delFlag=1",
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                positionTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/pers/sms/form?id=" + obj.data.id ;
                pop_show("编辑短信",url,600,600);
            }else if(obj.event==='detail'){

            }
        });
    });
</script>
<script type="text/html" id="actionBar">

</script>
#end

