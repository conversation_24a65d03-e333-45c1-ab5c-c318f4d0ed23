#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        noticeTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            noticeTypeLoad(data.field);
            return false;
        });

        reloadTab = function(){
            table.reload('noticeTypeTable');
        }

        function noticeTypeLoad(data){
            table.render({
                id : 'noticeTypeTable'
                ,elem : '#noticeTypeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/pers/noticeType/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'typeName', title: '公告类型', align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/pers/noticeType/add" ;
            pop_show("新增公告类型",url,500,300);
        });

        noticeTypeTableReload=function(){
            table.reload('noticeTypeTable');
        }
        table.on('tool(noticeTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/noticeType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                noticeTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/pers/noticeType/edit?id=" + obj.data.id ;
                pop_show("编辑公告类型",url,500,350);
            }
        });

        <!--region Description-->
        /*//批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('gatewayTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/gatewayTypeTable/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {bedTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("gatewayTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });*/
        <!--endregion-->
    });
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("notice:type:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("notice:type:voidBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            公告类型:
            <div class="layui-inline">
                <input id="typeName" name="typeName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            <!--设备类型:
            <div class="layui-inline">
                <select name="equipmentType" lay-search>
                    <option value="">请选择设备类型</option>
                    #for(b : typeList)
                    <option value="#(b.dictValue)">#(b.dictName)</option>
                    #end
                </select>
            </div>-->
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("notice:type:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
		#end
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table class="layui-table" id="noticeTypeTable" lay-filter="noticeTypeTable"></table>
</div>
#end