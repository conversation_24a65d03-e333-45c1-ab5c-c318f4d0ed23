#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <div class="layui-input-inline">
                <label class="layui-form-label">姓名：</label>
                <div class="layui-input-inline" style="float: left;" >
                    <input class="layui-input" name="fullName" id="fullName" value="" >
                </div>
            </div>
            <!--<label class="layui-form-label">转正时间</label>
            <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
            </div>-->
            <!--<label class="layui-form-label">状态</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="state" name="state" lay-filter="state">
                    <option value="5">全部</option>
                    <option value="0">待处理</option>
                    <option value="1">提交</option>
                    <option value="2">批准</option>
                    <option value="3">完成</option>
                    <option value="4">失败</option>
                </select>
            </div>-->
            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="yearMonth" id="yearMonth" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:leaveRest:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            #shiroHasPermission("emp:leaveRest:exportBtn")
            <button class="layui-btn" id="export" type="button">导出</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>

</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#yearMonth'
            ,type:'month'
            ,trigger: 'click'
            ,value:new Date()
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            reloadTable();
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }



        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }



        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"fullName":$("#name").val(),'yearMonth':$("#yearMonth").val(),'deptId':id});
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/leaveRestForm';
            pop_show("添加请假申请",url,1300,700);
        });

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/empPerformance/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return d.fullName+"("+d.workNum+")";
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true}
                    ,{field:'individualPerformanceTarget', title: '个人业绩达标额',width: 120, align: 'center', unresize: true}
                    ,{field:'individualContractAmount', title: '个人合同款', align: 'center', unresize: true}
                    ,{field:'individualRechargeAmount', title: '个人充值款', align: 'center', unresize: true}
                    ,{field:'teamPerformanceTarget', title: '团队业绩达标额',width: 120, align: 'center', unresize: true}
                    ,{field:'teamPerformanceAmount', title: '团队业绩额', align: 'center', unresize: true}

                    ,{field:'baseSalary', title: '底薪', align: 'center', unresize: true}
                    ,{field:'baseSalaryFloat', title: '底薪浮动', align: 'center', unresize: true}
                    ,{field:'businessPushSalary', title: '业务提成', align: 'center', unresize: true}
                    ,{field:'individualPerformanceSalary', title: '个人业绩奖', align: 'center', unresize: true}
                    ,{field:'teamPerformanceSalary', title: '团队业绩奖', align: 'center', unresize: true}
                    ,{field:'managementSalary', title: '管理奖', align: 'center', unresize: true}
                    ,{field:'socialSecurity', title: '社保扣费', align: 'center', unresize: true}
                    ,{field:'salesChampionSalary', title: '销售冠军奖', align: 'center', unresize: true}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/leaveRestForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }
            })


        };


        $("#export").click(function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var url='#(ctxPath)/empPerformance/export?fullName='+$("#fullName").val()+"&yearMonth="+$("#yearMonth").val()+"&deptId="+id;
            window.location.href=url;
        });


    });
</script>
#end