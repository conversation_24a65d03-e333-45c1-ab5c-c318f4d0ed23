#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
     .laytable-cell-checkbox .layui-disabled.layui-form-checked i {
         background: #fff !important;
     }
</style>
#end

#define content()

<input id="employeeId" value="#(employeeId??)" type="hidden">
<div class="layui-row" style="padding: 10px;">

</div>
<label style="margin-left: 10px;">部门：</label>
<div class="layui-inline"  style="width: 350px;">
    <div id="deptSelect" style="margin: 5px 10px;">

    </div>
</div>
<label>时间：</label>
<div class="layui-inline" style="padding-right: 20px;">
    <input type="text" readonly class="layui-input" id="dateRange">
</div>
<label>姓名：</label>
<div class="layui-inline" style="padding-right: 20px;">
    <input type="text" class="layui-input" id="fullName">
</div>
<label>状态：</label>
<div class="layui-inline layui-form" style="padding-right: 20px;">
    <select id="archiveStatus">
        #dictOption("archive_status", '', "")
    </select>
</div>

<button type="button" class="layui-btn" id="search">搜索</button>
#shiroHasPermission("emp:checkin:summaryExportBtn")
<button type="button" class="layui-btn" id="export">导出</button>
#end
#shiroHasPermission("emp:checkin:examineBtn")
<button type="button" class="layui-btn" id="examine">审核选中项</button>
#end
#shiroHasPermission("emp:checkin:examineAllBtn")
<button type="button" class="layui-btn" id="examineAll">审核全部</button>
#end
<div class="layui-row" style="">
    <p style="color: #666;padding-left: 10px;padding-top: 10px;">缺勤天数：未打上班和未打下班卡出勤天数</p>
    <p style="color: #666;padding-left: 10px;padding-bottom: 10px;">实出勤天数：正常出勤天数+迟到、早退、漏卡出勤天数</p>
    <table id="employeeCheckinTable" lay-filter="employeeCheckinTable"></table>
</div>

#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">

        #shiroHasPermission("emp:archives:changeDepartment:edit")
        <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
        #end
    </div>
</script>
<script type="text/html" id="checkbd">
    #[[{{#  if (d.status != "3"){ }}
    <input type="checkbox" name="siam_one" title="" lay-skin="primary" data-id = "{{ d.id }}">
    {{#  } }}
    ]]#
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeCheckinTable'
            ,laydate=layui.laydate
            ,form=layui.form
        ;

        //日期范围选择
        /*laydate.render({
            elem: '#dateRange'
            //,range: true //或 range: '~' 来自定义分割字符
            ,trigger:'click'
            ,max: '#(month)'
            ,type: 'month'
            ,value:"#(month)"
        });*/
        laydate.render({
            elem: '#dateRange'
            ,range: true //或 range: '~' 来自定义分割字符
            ,trigger:'click'
            ,max: '#(endDateStr)'
            ,value:"#(startDateStr) - #(endDateStr)"
        });
        /*var deptSelect = xmSelect.render({
            // 这里绑定css选择器
            el: '#deptSelect',
            // 渲染的数据
            data: [
            ],
        })*/

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });

        loadTable=function (data) {
            layer.load();
            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/employeeCheckin/empCheckinDaySummaryPage'
                ,height: vipTable.getFullHeight()*0.8    //容器高度
                ,where : data//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {
                        templet: "#checkbd",
                        title: "<input type='checkbox' name='siam_all' title='' lay-skin='primary' lay-filter='siam_all'> ",
                        width: 50, align:'center'
                    }
                    ,{field:'orgName', title:'部门', width:250, align:'center',unresize:true}
                    ,{field:'fullName', title:'姓名', width:120, align:'center',unresize:true,templet:function (d){
                            return d.fullName+"("+d.workNum+")";
                        }}
                    ,{field:'status', title:'审核状态', width:90, align:'center',unresize:true,templet:function (d){
                            if(d.status==='3'){
                                return '<span class="layui-badge layui-bg-green">审核完成</span>'
                            }else if(d.status==='2'){
                                return '<span class="layui-badge layui-bg-orange">审核中</span>'
                            }else{
                                return '<span class="layui-badge">未审核</span>'
                            }
                        }}
                    ,{field:'exceptionCount', title:'缺勤天数', width:80, align:'center',unresize:true}
                    ,{field:'normalCount', title:'实出勤天数', width:80, align:'center',unresize:true}
                    ,{field:'restCount', title:'休息天数', width:80, align:'center',unresize:true}
                    ,{field:'', title:'全天加班', align:'center', width:100,unresize:true,templet:function (d) {
                            var str="";
                            if(d.over_1_days>0){
                                str+="调休:"+d.over_1_days+"天;";
                            }else if(d.over_2_days>0){
                                str+="补休:"+d.over_2_days+"天;";
                            }else if(d.over_3_days>0){
                                str+="计加班工资:"+d.over_3_days+"天;";
                            }else if(d.over_4_days>0){
                                str+="其它:"+d.over_4_days+"天;";
                            }
                            return str;
                        }}
                    ,{field:'', title:'全天请休假', align:'center',unresize:true,templet:function (d) {
                            str="";
                            if(d.leave_1_days>0){
                                str+="事假:"+d.leave_1_days+"天;";
                            }
                            if(d.leave_2_days>0){
                                str+="病假:"+d.leave_2_days+"天;";
                            }
                            if(d.leave_3_days>0){
                                str+="婚假:"+d.leave_3_days+"天;";
                            }
                            if(d.leave_4_days>0){
                                str+="丧假:"+d.leave_4_days+"天;";
                            }
                            if(d.leave_5_days>0){
                                str+="护理假:"+d.leave_5_days+"天;";
                            }
                            if(d.leave_6_days>0){
                                str+="年休假:"+d.leave_6_days+"天;";
                            }
                            if(d.leave_7_days>0){
                                str+="工伤假:"+d.leave_7_days+"天;";
                            }
                            if(d.leave_8_days>0){
                                str+="调休:"+d.leave_8_days+"天;";
                            }
                            if(d.leave_9_days>0){
                                str+="补休:"+d.leave_9_days+"天;";
                            }
                            if(d.leave_10_days>0){
                                str+="哺乳假:"+d.leave_10_days+"天;";
                            }
                            if(d.leave_11_days>0){
                                str+="其它:"+d.leave_11_days+"天;";
                            }
                            return str;

                        }}
                    ,{field:'', title:'工作日加班',  align:'center',unresize:true,templet:function (d) {
                            var str="";
                            if(d.halfOver_1_second>0){
                                var s=formatSeconds(d.halfOver_1_second);
                                str+="调休:"+s+";";
                            }else if(d.halfOver_2_second>0){
                                var s=formatSeconds(d.halfOver_2_second);
                                str+="补休:"+s+";";
                            }else if(d.halfOver_3_second>0){
                                var s=formatSeconds(d.halfOver_3_second);
                                str+="计加班工资:"+s+";";
                            }else if(d.halfOver_4_second>0){
                                var s=formatSeconds(d.halfOver_4_second);
                                str+="其它:"+s+";";
                            }
                            return str;
                        }}
                    ,{field:'', title:'半天请休假',  align:'center',unresize:true,templet:function (d) {
                            str="";
                            if(d.halfLeave_1_days>0){
                                str+="事假:"+d.halfLeave_1_days+"次;";
                            }
                            if(d.halfLeave_2_days>0){
                                str+="病假:"+d.halfLeave_2_days+"次;";
                            }
                            if(d.halfLeave_3_days>0){
                                str+="婚假:"+d.halfLeave_3_days+"次;";
                            }
                            if(d.halfLeave_4_days>0){
                                str+="丧假:"+d.halfLeave_4_days+"次;";
                            }
                            if(d.halfLeave_5_days>0){
                                str+="护理假:"+d.halfLeave_5_days+"次;";
                            }
                            if(d.halfLeave_6_days>0){
                                str+="年休假:"+d.halfLeave_6_days+"次;";
                            }
                            if(d.halfLeave_7_days>0){
                                str+="工伤假:"+d.halfLeave_7_days+"次;";
                            }
                            if(d.halfLeave_8_days>0){
                                str+="调休:"+d.halfLeave_8_days+"次;";
                            }
                            if(d.halfLeave_9_days>0){
                                str+="补休:"+d.halfLeave_9_days+"次;";
                            }
                            if(d.halfLeave_10_days>0){
                                str+="哺乳假:"+d.halfLeave_10_days+"次;";
                            }
                            if(d.halfLeave_11_days>0){
                                str+="其它:"+d.halfLeave_11_days+"次;";
                            }
                            return str;

                        }}
                    ,{field:'exception_1', title:'迟到', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_1==undefined){
                                return 0+"次";
                            }else{
                                var exception_1_d=0;
                                if(d.exception_1_duration!=undefined){
                                    exception_1_d=d.exception_1_duration/60;
                                }
                                return d.exception_1+'次；共'+exception_1_d+"分钟";
                            }
                        }}
                    ,{field:'exception_2', title:'早退', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_2==undefined){
                                return 0+"次";
                            }else{
                                var exception_2_d=0;
                                if(d.exception_2_duration!=undefined){
                                    exception_2_d=d.exception_2_duration/60;
                                }
                                return d.exception_2+'次；共'+exception_2_d+"分钟";
                            }
                        }}
                    ,{field:'exception_4', title:'旷工', width:120, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_4==undefined){
                                return 0+"次";
                            }else{
                                var exception_4_d=0;
                                if(d.exception_4_duration!=undefined){
                                    exception_4_d=d.exception_4_duration/60;
                                }
                                return d.exception_4+'次；共'+exception_4_d+"分钟";
                            }
                        }}
                    ,{field:'exception_3', title:'缺卡', width:70, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_3==undefined){
                                return 0+"次";
                            }else{
                                return d.exception_3+'次';
                            }
                        }}
                    ,{field:'fillCardCount', title:'补卡成功', width:80, align:'center',unresize:true,templet:function (d) {
                            if(d.fillCardCount==undefined){
                                return 0+"次";
                            }else{
                                return d.fillCardCount+'次';
                            }
                        }}

                    ,{title:'操作',fixed: 'right', width:90, unresize:true, align:'center', templet: function(d){
                            var editBtn='';
                            #shiroHasPermission("emp:checkin:detail")
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="detail">明细</a>';
                            #end
                            #shiroHasPermission("emp:checkin:daySummary")
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="daily">日报</a>';
                            #end
                            return editBtn;
                        }
                    }
                ]]
                ,page : true
                ,done:function () {
                    layer.closeAll('loading');
                }
            });
        }
        loadTable2=function (data) {
            layer.load();
            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/employeeCheckin/empCheckinDaySummaryPage'
                ,height: vipTable.getFullHeight()*0.8    //容器高度
                ,where : data//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {
                        templet: "#checkbd",
                        title: "<input type='checkbox' name='siam_all' title='' lay-skin='primary' lay-filter='siam_all'> ",
                        width: 50, align:'center'
                    }
                    ,{field:'orgName', title:'部门', width:250, align:'center',unresize:true}
                    ,{field:'fullName', title:'姓名', width:120, align:'center',unresize:true,templet:function (d){
                            return d.fullName+"("+d.workNum+")";
                        }}
                    ,{field:'status', title:'审核状态', width:80, align:'center',unresize:true,templet:function (d){
                            if(d.status==='3'){
                                return '<span class="layui-badge layui-bg-green">审核完成</span>'
                            }else if(d.status==='2'){
                                return '<span class="layui-badge layui-bg-orange">审核中</span>'
                            }else{
                                return '<span class="layui-badge">未审核</span>'
                            }
                        }}
                    ,{field:'shouldWorkDay', title:'应出勤天数', width:80, align:'center',unresize:true}
                    ,{field:'actualWorkDay', title:'实出勤天数', width:80, align:'center',unresize:true}
                    ,{field:'exceptionCount', title:'缺勤天数', width:80, align:'center',unresize:true}
                    ,{field:'restCount', title:'休息天数', width:80, align:'center',unresize:true}
                    ,{field:'actualWageDays', title:'实算工资天数', width:100, align:'center',unresize:true,templet:function (d) {
                            var moneyOverHour="";
                            if(d.moneyOverSecond>0){
                                moneyOverHour=formatSeconds(d.moneyOverSecond);
                            }
                            return d.actualWageDays+"天"+moneyOverHour;
                        }}
                    ,{field:'yearRest', title:'年休假', width:70, align:'center',unresize:true}
                    ,{field:'marriageHoliday', title:'婚/丧/产/工伤假', width:110, align:'center',unresize:true}
                    ,{field:'thingLear', title:'事假', width:80, align:'center',unresize:true}
                    ,{field:'moneyOverDay', title:'计工资加班', width:90, align:'center',unresize:true,templet:function (d) {
                            var moneyOverHour="";
                            if(d.moneyOverSecond>0){
                                moneyOverHour=formatSeconds(d.moneyOverSecond);
                            }
                            return d.moneyOverDay+"天"+moneyOverHour;
                        }}
                    ,{field:'restOverDay', title:'调休/补休/其他加班', width:130, align:'center',unresize:true,templet:function (d){
                            var restOverHour="";
                            if(d.restOverSecond>0){
                                restOverHour=formatSeconds(d.restOverSecond);
                            }
                            return d.restOverDay+"天"+restOverHour;
                        }}
                    /*,{field:'', title:'全天加班', align:'center', width:100,unresize:true,templet:function (d) {
                            var str="";
                            if(d.over_1_days>0){
                                str+="调休:"+d.over_1_days+"天;";
                            }else if(d.over_2_days>0){
                                str+="补休:"+d.over_2_days+"天;";
                            }else if(d.over_3_days>0){
                                str+="计加班工资:"+d.over_3_days+"天;";
                            }else if(d.over_4_days>0){
                                str+="其它:"+d.over_4_days+"天;";
                            }
                            return str;
                        }}
                    ,{field:'', title:'全天请休假', align:'center',unresize:true,templet:function (d) {
                            str="";
                            if(d.leave_1_days>0){
                                str+="事假:"+d.leave_1_days+"天;";
                            }
                            if(d.leave_2_days>0){
                                str+="病假:"+d.leave_2_days+"天;";
                            }
                            if(d.leave_3_days>0){
                                str+="婚假:"+d.leave_3_days+"天;";
                            }
                            if(d.leave_4_days>0){
                                str+="丧假:"+d.leave_4_days+"天;";
                            }
                            if(d.leave_5_days>0){
                                str+="护理假:"+d.leave_5_days+"天;";
                            }
                            if(d.leave_6_days>0){
                                str+="年假:"+d.leave_6_days+"天;";
                            }
                            if(d.leave_7_days>0){
                                str+="工伤假:"+d.leave_7_days+"天;";
                            }
                            if(d.leave_8_days>0){
                                str+="调休:"+d.leave_8_days+"天;";
                            }
                            if(d.leave_9_days>0){
                                str+="补休:"+d.leave_9_days+"天;";
                            }
                            if(d.leave_10_days>0){
                                str+="哺乳假:"+d.leave_10_days+"天;";
                            }
                            if(d.leave_11_days>0){
                                str+="其它:"+d.leave_11_days+"天;";
                            }
                            return str;

                        }}
                    ,{field:'', title:'工作日加班',  align:'center',unresize:true,templet:function (d) {
                            var str="";
                            if(d.halfOver_1_second>0){
                                var s=formatSeconds(d.halfOver_1_second);
                                str+="调休:"+s+";";
                            }else if(d.halfOver_2_second>0){
                                var s=formatSeconds(d.halfOver_2_second);
                                str+="补休:"+s+";";
                            }else if(d.halfOver_3_second>0){
                                var s=formatSeconds(d.halfOver_3_second);
                                str+="计加班工资:"+s+";";
                            }else if(d.halfOver_4_second>0){
                                var s=formatSeconds(d.halfOver_4_second);
                                str+="其它:"+s+";";
                            }
                            return str;
                        }}
                    ,{field:'', title:'半天请休假',  align:'center',unresize:true,templet:function (d) {
                            str="";
                            if(d.halfLeave_1_days>0){
                                str+="事假:"+d.halfLeave_1_days+"次;";
                            }
                            if(d.halfLeave_2_days>0){
                                str+="病假:"+d.halfLeave_2_days+"次;";
                            }
                            if(d.halfLeave_3_days>0){
                                str+="婚假:"+d.halfLeave_3_days+"次;";
                            }
                            if(d.halfLeave_4_days>0){
                                str+="丧假:"+d.halfLeave_4_days+"次;";
                            }
                            if(d.halfLeave_5_days>0){
                                str+="护理假:"+d.halfLeave_5_days+"次;";
                            }
                            if(d.halfLeave_6_days>0){
                                str+="年假:"+d.halfLeave_6_days+"次;";
                            }
                            if(d.halfLeave_7_days>0){
                                str+="工伤假:"+d.halfLeave_7_days+"次;";
                            }
                            if(d.halfLeave_8_days>0){
                                str+="调休:"+d.halfLeave_8_days+"次;";
                            }
                            if(d.halfLeave_9_days>0){
                                str+="补休:"+d.halfLeave_9_days+"次;";
                            }
                            if(d.halfLeave_10_days>0){
                                str+="哺乳假:"+d.halfLeave_10_days+"次;";
                            }
                            if(d.halfLeave_11_days>0){
                                str+="其它:"+d.halfLeave_11_days+"次;";
                            }
                            return str;

                        }}*/
                    ,{field:'exception_1', title:'迟到', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_1==undefined){
                                return 0+"次";
                            }else{
                                var exception_1_d=0;
                                if(d.exception_1_duration!=undefined){
                                    exception_1_d=d.exception_1_duration/60;
                                }
                                return d.exception_1+'次；共'+exception_1_d+"分钟";
                            }
                        }}
                    ,{field:'exception_2', title:'早退', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_2==undefined){
                                return 0+"次";
                            }else{
                                var exception_2_d=0;
                                if(d.exception_2_duration!=undefined){
                                    exception_2_d=d.exception_2_duration/60;
                                }
                                return d.exception_2+'次；共'+exception_2_d+"分钟";
                            }
                        }}
                    ,{field:'exception_4', title:'旷工', width:120, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_4==undefined){
                                return 0+"次";
                            }else{
                                var exception_4_d=0;
                                if(d.exception_4_duration!=undefined){
                                    exception_4_d=d.exception_4_duration/60;
                                }
                                return d.exception_4+'次；共'+exception_4_d+"分钟";
                            }
                        }}
                    ,{field:'exception_3', title:'缺卡', width:70, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_3==undefined){
                                return 0+"次";
                            }else{
                                return d.exception_3+'次';
                            }
                        }}
                    ,{field:'fillCardCount', title:'补卡成功', width:80, align:'center',unresize:true,templet:function (d) {
                            if(d.fillCardCount==undefined){
                                return 0+"次";
                            }else{
                                return d.fillCardCount+'次';
                            }
                        }}

                    ,{title:'操作',fixed: 'right', width:90, unresize:true, align:'center', templet: function(d){
                            var editBtn='';
                            #shiroHasPermission("emp:checkin:detail")
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="detail">明细</a>';
                            #end
                            #shiroHasPermission("emp:checkin:daySummary")
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="daily">日报</a>';
                            #end
                            return editBtn;
                        }
                    }
                ]]
                ,page : true
                ,done:function () {
                    layer.closeAll('loading');
                }
            });
        }


        form.on("checkbox(siam_all)", function () {
            var status = $(this).prop("checked");
            $.each($("input[name=siam_one]"), function (i, value) {
                $(this).prop("checked", status);
            });
            form.render();
        });

        $("#examine").on('click',function (){
            layer.confirm('确定审核选中记录吗?', {icon: 3, title:'提示'}, function(index){
                //do something
                var ids = [];
                $.each($("input[name=siam_one]:checked"), function (i, value) {
                    ids[i] = $(this).attr("data-id");  // 如果需要获取其他的值 需要在模板中把值放到属性中 然后这里就可以拿到了
                });
                console.log(ids);
                util.sendAjax({
                    url: '#(ctxPath)/employeeCheckin/examineEmpDaySummary',
                    type: 'post',
                    data: {'yearMonth': $('#dateRange').val(),'ids':JSON.stringify(ids)},
                    notice: true,
                    loadFlag:true,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            var ids=[];
                            $.each(deptSelect.getValue(),function (index,item) {
                                ids.push(item.id);
                            });
                            layer.load();
                            loadTable({"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()});
                        }
                    }
                });
                layer.close(index);
            });

        })

        $("#examineAll").on('click',function (){
            layer.confirm('确定审核全部记录吗?', {icon: 3, title:'提示'}, function(index){
                var ids=[];
                $.each(deptSelect.getValue(),function (index,item) {
                    ids.push(item.id);
                });
                util.sendAjax({
                    url: '#(ctxPath)/employeeCheckin/examineEmpDaySummaryAll',
                    type: 'post',
                    data: {'yearMonth': $('#dateRange').val(),'id':ids[0]},
                    notice: true,
                    loadFlag:true,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            var ids=[];
                            $.each(deptSelect.getValue(),function (index,item) {
                                ids.push(item.id);
                            });
                            layer.load();
                            loadTable({"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()});
                        }
                    }
                });
                layer.close(index);
            });

        })

        function formatSeconds(value){
            let result = parseInt(value)
            let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
            let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
            let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));

            let res = '';
            if(h !== '00') res += `${h}时`;
            if(m !== '00') res += `${m}分`;
            return res;
        }
        loadTable({'archiveStatus':$("#archiveStatus").val()});

        table.on('tool('+tableId+')',function (obj) {
            if(obj.event==='daily'){
                pop_show("["+obj.data.fullName+']打卡日报','#(ctxPath)/employeeCheckin/empCheckinDaySummaryDetailIndex?empId='+obj.data.id+"&dateRange="+$("#dateRange").val()+"&fullName="+obj.data.fullName,1400,700);
            }else if(obj.event==='detail'){
                pop_show("["+obj.data.fullName+']打卡明细','#(ctxPath)/employeeCheckin/empCheckinRecordIndex?empId='+obj.data.id+"&dateRange="+$("#dateRange").val()+"&fullName="+obj.data.fullName,1200,700);
            }
        })

        $("#search").on('click',function () {
            var ids=[];
            $.each(deptSelect.getValue(),function (index,item) {
                ids.push(item.id);
            });
            loadTable({"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()});
        })

        $("#export").on('click',function () {
            layer.confirm("确定导出吗?",function(index){
                layer.load();
                var ids=[];
                var names=[];
                $.each(deptSelect.getValue(),function (index,item) {
                    ids.push(item.id);
                    names.push(item.name);
                });
                var data={"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()};
                $.post("#(ctxPath)/employeeCheckin/empCheckinDaySummaryExportCheck?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)+"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()
                    +"&archiveStatus="+$("#archiveStatus").val(), {},function (d) {
                    if (d.state === 'ok') {
                        window.location.href="#(ctxPath)/employeeCheckin/empCheckinDaySummaryExport2?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)
                            +"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()+"&archiveStatus="+$("#archiveStatus").val();
                        layer.closeAll('loading');
                    }else{
                        window.top.layer.msg(d.msg, {icon: 2, offset: 'auto'});
                        layer.closeAll('loading');
                    }
                })



                layer.close(index) ;
            });
        })

    });
</script>
#end