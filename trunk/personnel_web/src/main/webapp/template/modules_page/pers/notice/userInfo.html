#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅人选择#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
#end

#define js()
<script>
layui.config({
	base: '/static/js/extend/',
});
layui.extend({
	dtree: 'layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use(['table','form','dtree','layer'], function() {
    var table = layui.table
    , $ = layui.$
    , form=layui.form
    , dtree = layui.dtree
    , layer=layui.layer
    , tableId = 'userMarketTable'
    ;
    
	$.post('#(ctxPath)/persOrg/orgDtree',{},function (r) {
		var jsonArray=[];
		$.each(r.data,function (index,item) {
			var json={"id":item.id,"title":item.name,"checkArr":"0","parentId":item.pId};
			var flag=isUpdateParentId(json.parentId,r.data);
			if(flag){
				json.parentId='0';
			}
			jsonArray[index]=json;
		});
		// 初始化树
		DemoTree = dtree.renderSelect({
			elem: "#deptId",
			dataFormat: "list",
			data:jsonArray,
			initLevel:10,
			selectCardHeight:"500"
			//url: "#(ctxPath)/persOrg/orgDtree" // 使用url加载（可与data加载同时存在）
		});
	});
	
	function isUpdateParentId(pid,array){
		var flag=true;
		$.each(array,function (i,it) {
			if(pid==it.id){
				flag=false;
				return false;
			}
		});
		return flag;
	}

	// 表格加载渲染
	var tableObj = table.render({
        id : tableId
        ,elem : '#'+tableId
        ,method : 'POST'
        ,limit : 15
        ,limits : [15,30,45,50]
        ,url : '#(ctxPath)/pers/notice/empUserPage'
        ,cellMinWidth: 80
        ,cols: [[
            {field:'fullName', title: '姓名', align: 'center', unresize: true}
            ,{field:'orgName', title: '所属机构', align: 'center', unresize: true}
            ,{field:'deptName', title: '所属部门', align: 'center', unresize: true}
            ,{fixed:'right', title: '操作', width: 90, align: 'center', unresize: true, toolbar: '#actionBar'}
        ]]
        ,page : true
    });
    table.on('tool('+tableId+')',function(obj){
        if(obj.event === 'choose'){
            parent.getUserTr(obj.data.orgId,obj.data.userId,obj.data.fullName);
            pop_close();
        }
    });
	//重载表格，跳转到第一页
    pageTableReload = function () {
    	tableReload(tableId,{fullName:$('#fullName').val(),'deptId':$(".layui-select-title input[dtree-id='deptId']").val()});
    }
	//搜索按钮点击事件
	$('#btn-search').on('click', function() {
		pageTableReload();
	});
});
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="choose">选择</a>
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" id="frm" method="post" style="margin-top: 15px;margin-left: 10px;">
            <div class="layui-input-inline">
				<span class="layui-form-label" style="width:78px;">姓名</span>
				<div class="layui-input-inline">
					<input type="text" id="fullName" class="layui-input" placeholder="请输入姓名搜索" autocomplete="off">
				</div>
			</div>
            &nbsp;
            <div class="layui-input-inline">
				<span class="layui-form-label" style="padding: 8px 5px;">组织架构</span>
				<div class="layui-input-inline" style="width:260px;">
					<ul id="deptId" class="dtree" data-id="0" ></ul>
				</div>
			</div>
            <button type="button" id="btn-search" class="layui-btn" style="padding: 0 10px;border-radius: 5px;">查询</button>
        </form>
    </div>
    <table class="layui-table" id="userMarketTable" lay-filter="userMarketTable"></table>
</div>
#end