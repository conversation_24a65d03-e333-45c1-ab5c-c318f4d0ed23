#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label">月份：</label>
                <div class="layui-input-inline" style="float: left;" >
                    <input class="layui-input" name="yearMonth" id="yearMonth" readonly >
                </div>
            </div>

            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:workAge:exportBtn")
            <button class="layui-btn" id="exportBtn" type="button" style="margin-left: 20px;">导出</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:dispatch:edi2tBtn")
    #[[
    {{#if(d.status=='3'){}}

    <a class="layui-btn layui-btn-xs" lay-event="edit2">编辑</a>

    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:exportBtn")
    #[[
    {{#if(d.taskId!=undefined && d.stepts.length>=1){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="export">导出</a>
    {{#}}}
    ]]#
    #end
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#yearMonth'
            ,trigger: 'click'
            ,type: 'month'
            ,value:new Date()
            ,max:'#(maxMonth)'
            ,btns: [ 'confirm']
        });

        msgLoad({'type':$("#type").val(),"yearMonth":$("#yearMonth").val()});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }


        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"fullName":$("#name").val(),"deptId":id,'type':$("#type").val(),"yearMonth":$("#yearMonth").val()});
        }


        $("#exportBtn").on('click',function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            window.location.href='#(ctxPath)/persOrgEmployee/exportEmployeeWorkAgeList' +
                '?deptId='+id+"&fullName="+$("#name").val()+"&type="+$("#type").val()+"&yearMonth="+$("#yearMonth").val();
        })

        $("#queryBtn").on('click',function () {
            reloadTable();
        });


        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/persOrgEmployee/employeeWorkAgeList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', title: '序号', width: 60, unresize:true}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true,templet:function (d) {
                            if(d.org==undefined || d.org.orgName==undefined){
                                return '';
                            }else{
                                //return '<span title="'+d.org.orgName+'" >'+d.org.orgName+'</span>';
                                return d.org.orgName;
                            }
                        }}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true,templet:function (d) {
                            if(d.org==undefined || d.org.positionName==undefined){
                                return '';
                            }else{
                                return d.org.positionName;
                            }
                        }}
                    ,{field:'workNum',title:'工号', align: 'center', unresize: true},
                    {field:'fullName',title:'姓名', align: 'center', unresize: true},
                    {field:'sex',title:'性别', align: 'center', unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}

                    ,{field:'startTime', title: '入职时间',align: 'center', unresize: true,templet:function (d){
                            return dateFormat(d.entryTime,'yyyy-MM-dd');
                        }}

                    ,{field:'workAge', title: '工龄', align: 'center', unresize: true}
                    ,{field:'workAgeMoney', title: '工龄工资', align: 'center', unresize: true,templet:function (d){
                            return d.workAge*50;
                        }}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/dispatchForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }else if(obj.event==='edit2'){
                    var url='#(ctxPath)/pers/approval/dispatchEditForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }else if(obj.event=='export'){
                    window.location.href='#(ctxPath)/pers/approval/exportChangeApply2?id='+obj.data.id;
                }
            })


        };


    });
</script>
#end