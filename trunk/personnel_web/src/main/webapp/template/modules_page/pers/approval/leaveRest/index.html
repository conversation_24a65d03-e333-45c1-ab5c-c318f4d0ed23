#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .layui-table-cell{
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        height: auto !important;
        word-break: break-all;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <div class="layui-input-inline">
                <label class="layui-form-label">姓名：</label>
                <div class="layui-input-inline" style="float: left;" >
                    <input class="layui-input" name="name" id="name" >
                </div>
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">请休假时间</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:leaveRest:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            <!--<button class="layui-btn" id="addBtn2" type="button">添加2</button>-->
            #shiroHasPermission("emp:leaveRest:exportBtn")
            <button class="layui-btn" id="export" type="button">导出</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

    #if(userType!='system_admin')
    #[[
    {{#if(d.status=='3'){}}
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="cancel">撤销</a>
    {{#}}}
    ]]#
    #end

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        $("#export").on('click',function () {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            window.location.href="#(ctxPath)/pers/approval/leaveRestExport?name="+$("#name").val()+"&date="+$("#date").val()+"&deptId="+id;
            setTimeout(function (){
                layer.closeAll('loading');
            },10000);
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }
        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"name":$("#name").val(),'date':$("#date").val(),'deptId':id});
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/leaveRestNewForm';
            pop_show("添加请假申请",url,'100%','100%');
        });

        $("#addBtn2").on('click',function () {
            var url='#(ctxPath)/pers/approval/leaveRestForm';
            pop_show("添加请假申请",url,'100%','100%');
        });

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/findEmployeeLeaveRestPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                        return d.fullName+"("+d.workNum+")";
                    }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true}
                    ,{field:'leaveRestType', title: '类型',width: 90, align: 'center', unresize: true}
                    ,{field:'startTime', title: '请休假时间',width: 290,align: 'center', unresize: true,templet:function (d){

                            if(d.isNew){
                                let str=''
                                $.each(d.dateList,function (index,item) {
                                    let restStr='';
                                    if(item.leaveRestDateType=='2' && item.restTimes!=undefined){
                                        let restTimesArray=JSON.parse(item.restTimes);
                                        if(restTimesArray!=null && restTimesArray.length>0){
                                            restStr+='，休息时间：';
                                            $.each(restTimesArray,function (ix,it) {
                                                restStr+=it.restStartTime+'至'+it.restEndTime+"；";
                                            })
                                        }

                                    }
                                    str+="<p title='类型："+item.leaveRestTypeLabel+restStr+"'>"+item.startTime+'至'+item.endTime+"</p>";
                                });
                                return str;
                            }else{
                                if("2"==d.leaveRestDateType){
                                    return dateFormat(d.startTime,'yyyy-MM-dd HH:mm:ss')+"至"+dateFormat(d.endTime,'yyyy-MM-dd HH:mm:ss');
                                }else{
                                    var str="";
                                    if(dateFormat(d.startTime,'HH:mm:ss')=='00:00:00'){
                                        str="上午";
                                    }else if(dateFormat(d.startTime,'HH:mm:ss')=='12:00:00'){
                                        str="下午";
                                    }

                                    var str2="";
                                    if(dateFormat(d.endTime,'HH:mm:ss')=='12:00:00'){
                                        str2="上午";
                                    }else if(dateFormat(d.endTime,'HH:mm:ss')=='23:59:59'){
                                        str2="下午";
                                    }

                                    return dateFormat(d.startTime,'yyyy-MM-dd')+" "+str+"至"+dateFormat(d.endTime,'yyyy-MM-dd')+" "+str2;
                                }

                            }
                        }}
                    ,{field:'leaveDays', title: '请假时长',width:100, align: 'center', unresize: true,templet:function (d) {

                        if(d.isNew){
                            return d.leaveDays;
                        }else{
                            if("2"==d.leaveRestDateType){
                                var date3=new Date(d.endTime).getTime()-new Date(d.startTime).getTime();

                                var leave1=date3%(24*3600*1000)    //计算天数后剩余的毫秒数
                                var hours=Math.floor(leave1/(3600*1000))
                                //计算相差分钟数
                                var leave2=leave1%(3600*1000)        //计算⼩时数后剩余的毫秒数
                                var minutes=Math.floor(leave2/(60*1000))
                                //计算相差秒数
                                var leave3=leave2%(60*1000)      //计算分钟数后剩余的毫秒数
                                var seconds=Math.round(leave3/1000)
                                return hours+"⼩时 "+minutes+"分钟";
                            }else{
                                return d.leaveDays+"天";
                            }
                        }
                    }}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center', unresize: true,width:130,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge">未提交</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">审核通过</span>';
                            }else if(d.status=='4'){
                                return '驳回';
                            }else if(d.status=='5'){
                                return '中止';
                            }else if(d.status=='6'){
                                return '撤销';
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/leaveRestForm?id='+obj.data.id;
                    pop_show("处理流程",url,'100%','100%');
                }else if(obj.event=="cancel"){
                    layer.open({//parent表示打开二级弹框
                        type: 1,
                        title: "撤销",
                        shadeClose: false,
                        shade: 0.5,
                        btn: ['确定', '关闭'],
                        maxmin: false, //开启最大化最小化按钮
                        area:['500px;','500px;'],
                        content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                            "            " +
                            "\n" +
                            "            <div class=\"layui-form-item\">\n" +
                            "                <label class=\"layui-form-label\"><font color='red'></font>撤销备注</label>\n" +
                            "                <div class=\"layui-input-block\"  >" +
                            "                   <textarea placeholder=\"请输入内容\" id='remark' class=\"layui-textarea\" name='remark'></textarea>" +
                            "                </div>\n" +
                            "            </div>" +
                            "</form>",
                        cancel: function(){
                        },
                        end : function(){
                        },yes: function(index, layero){
                            if($("#remark").val()==''){
                                layer.msg('撤销备注必须填写', {icon: 2, offset: 'auto'});
                                return false;
                            }
                            layer.load();
                            let data={"id":obj.data.id,"cancelRemark":$("#remark").val(),"taskId":obj.data.taskId,"empId":obj.data.empId};
                            util.sendAjax ({
                                type: 'POST',
                                url: '#(ctxPath)/api/cancelEmpApply',
                                data: data,
                                loadFlag: true,
                                success : function(rep){
                                    if(rep.code=='0'){
                                        reloadTable();
                                        layer.closeAll("loading");
                                        layer.close(index);
                                        layer.msg(rep.msg, {icon: 1, offset: 'auto'});
                                    }else{
                                        layer.msg(rep.msg, {icon: 2, offset: 'auto'});
                                    }
                                },
                                complete : function() {
                                }
                            });
                            return false;
                        }

                    });
                }
            })


        };


    });
</script>
#end