#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript">
    layui.use(['form','laytpl','table','element','layer','laydate'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'datetime',
            trigger: 'click'
            //,min:0
            ,done: function(value, date){
                //alert('你选择的日期是：' + value + '\n获得的对象是' + JSON.stringify(date));
                let endTime=$("#endTime").val();
                if(endTime!='' && endTime!=undefined){
                    getRestTime(value,endTime);
                }
            }
        });

        laydate.render({
            elem : '#endTime',
            type: 'datetime',
            trigger: 'click'
            //,min:0
            ,done: function(value, date){
                //alert('你选择的日期是：' + value + '\n获得的对象是' + JSON.stringify(date));
                let startTime=$("#startTime").val();
                if(startTime!='' && startTime!=undefined){
                    getRestTime(startTime,value);
                }

            }
        });

        function getRestTime(startTime,endTime){
            $.post('#(ctxPath)/api/getEmployeeOverTimeRestTime',{'employeeId':$("#employeeId").val(),'startTime':startTime,'endTime':endTime},function (d) {
                if(d.code=='0'){
                    console.log(Number($("#restCount").val()));
                    if(Number($("#restCount").val())>0){

                        let nums=Number($("#restCount").val());
                        /*for(num in nums){
                            console.log("#restTimeItemDiv-"+num);
                            $("#restTimeItemDiv-"+num).remove();

                        }*/

                        for(let i=nums;i<=nums;i++){
                            $("#restTimeItemDiv-"+i).remove();
                        }

                        $("#restCount").val(0);
                    }

                    $.each(d.data,function (index,item) {

                        let restCount=$("#restCount").val();
                        restCount=Number(restCount)+1;
                        $("#restTimeDiv").append('<div class="layui-input-block" id="restTimeItemDiv-'+restCount+'" style="margin-bottom: 10px;">\n' +
                            '                            <label class="layui-form-label" style="padding: 8px 5px;" >休息开始时间</label>\n' +
                            '                            <div class="layui-input-inline" style="width: 150px;">\n' +
                            '                                <input type="text" name="restStartTime-'+restCount+'" id="restStartTime-'+restCount+'" required value="'+item.restStartTime+'"  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">\n' +
                            '                            </div>\n' +
                            '                            <label class="layui-form-label" style="padding: 8px 5px;">休息结束时间</label>\n' +
                            '                            <div class="layui-input-inline" style="width: 150px;">\n' +
                            '                                <input type="text" name="restEndTime-'+restCount+'" id="restEndTime-'+restCount+'" required value="'+item.restEndTime+'"  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">\n' +
                            '                            </div>' +
                            '                            <button class="layui-btn layui-btn-xs" style="margin-top: 7px;" type="button" onclick="del('+restCount+')">删除</button>\n' +
                            '                        </div>');

                        laydate.render({
                            elem : "#restStartTime-"+restCount,
                            type: 'datetime',
                            trigger: 'click',
                            min:startTime,
                            max:endTime
                        });
                        laydate.render({
                            elem : "#restEndTime-"+restCount,
                            type: 'datetime',
                            trigger: 'click',
                            min:startTime,
                            max:endTime
                        });
                        $("#restCount").val(restCount);

                    })
                }
            })
        }

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);

        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var restCount=Number($("#restCount").val());
            var restArray=[];
            for (var i=1;i<=restCount;i++){
                var startTime=$("#restStartTime-"+i).val();
                var endTime=$("#restEndTime-"+i).val();
                var restObj={};
                if(startTime!=undefined && endTime!=undefined && startTime!='' && endTime!=''){
                    restObj.restStartTime=startTime;
                    restObj.restEndTime=endTime;
                    restArray.push(restObj);
                }
            }
            var data;
            if(restArray.length>0){
                data=$("#noticeForm").serialize()+"&restTimes="+JSON.stringify(restArray);
            }else{
                data=$("#noticeForm").serialize();
            }
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeOverTimeApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {
            var restCount=Number($("#restCount").val());
            var restArray=[];
            for (var i=1;i<=restCount;i++){
                var startTime=$("#restStartTime-"+i).val();
                var endTime=$("#restEndTime-"+i).val();
                var restObj={};
                if(startTime!=undefined && endTime!=undefined && startTime!='' && endTime!=''){
                    restObj.restStartTime=startTime;
                    restObj.restEndTime=endTime;
                    restArray.push(restObj);
                }
            }
            var data;

            if(restArray.length>0){
                data=$("#noticeForm").serialize()+"&saveType=2"+"&restTimes="+JSON.stringify(restArray);
            }else{
                data=$("#noticeForm").serialize()+"&saveType=2";
            }
            //提交表单数据
            /*util.sendAjax ({
                type: 'POST',
                url: '',
                data: data,
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.code=='0'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });*/
            console.log(data);
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeOverTimeApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        $("#restBtn").on('click',function () {
            var startTime=$("#startTime").val();
            var endTime=$("#endTime").val();
            if(startTime=='' || endTime==''){
                layer.msg('请先选择加班开始结束时间', {icon: 2, offset: 'auto'});
                return;
            }
            var restCount=$("#restCount").val();
            restCount=Number(restCount)+1;
            $("#restTimeDiv").append('<div class="layui-input-block" id="restTimeItemDiv-'+restCount+'" style="margin-bottom: 10px;">\n' +
                '                            <label class="layui-form-label" style="padding: 8px 5px;" >休息开始时间</label>\n' +
                '                            <div class="layui-input-inline" style="width: 150px;">\n' +
                '                                <input type="text" name="restStartTime-'+restCount+'" id="restStartTime-'+restCount+'" required value=""  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">\n' +
                '                            </div>\n' +
                '                            <label class="layui-form-label" style="padding: 8px 5px;">休息结束时间</label>\n' +
                '                            <div class="layui-input-inline" style="width: 150px;">\n' +
                '                                <input type="text" name="restEndTime-'+restCount+'" id="restEndTime-'+restCount+'" required value=""  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">\n' +
                '                            </div>' +
                '                            <button class="layui-btn layui-btn-xs" style="margin-top: 7px;" type="button" onclick="del('+restCount+')">删除</button>\n' +
                '                        </div>');

            laydate.render({
                elem : "#restStartTime-"+restCount,
                type: 'datetime',
                trigger: 'click',
                min:startTime,
                max:endTime
            });
            laydate.render({
                elem : "#restEndTime-"+restCount,
                type: 'datetime',
                trigger: 'click',
                min:startTime,
                max:endTime
            });
            $("#restCount").val(restCount);
        })

         del=function(index){
            $("#restTimeItemDiv-"+index).remove()
        }

        #for(restTime : restTimeArray)
            laydate.render({
                elem : "#restStartTime-#(for.index+1)",
                type: 'datetime',
                trigger: 'click',
                min:"#date(overTimeApply.startTime??,'yyyy-MM-dd HH:mm:ss')",
                max:"#date(overTimeApply.endTime??,'yyyy-MM-dd HH:mm:ss')"
            });

            laydate.render({
                elem : "#restEndTime-#(for.index+1)",
                type: 'datetime',
                trigger: 'click',
                min:"#date(overTimeApply.startTime??,'yyyy-MM-dd HH:mm:ss')",
                max:"#date(overTimeApply.endTime??,'yyyy-MM-dd HH:mm:ss')"
            });
        #end
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
    <div class="layui-col-md7 layui-form" id="content" style="padding-right: 10px;">
        <div class="layui-collapse" >

            <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>加班员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(overTimeApply.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(overTimeApply==null)#(createBy??)#else#(overTimeApply.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(overTimeApply!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>

                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>部门</label>
                        <div class="layui-input-inline">
                            <select id="deptId" name="deptId" lay-filter="deptId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==overTimeApply.deptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">职位</label>
                        <div class="layui-input-inline">
                            <select id="positionId" name="positionId" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==overTimeApply.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>类型</label>
                        <div class="layui-input-inline">
                            <select id="overType" name="overType" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择类型</option>
                                #for(type : overTimeTypes)
                                <option value="#(type.value)" #if(type.value==overTimeApply.overType??) selected #end>#(type.name)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>开始时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="startTime" id="startTime" #if(!isSaveHandle && taskId!=null) disabled  #end readonly required value="#date(overTimeApply.startTime??,'yyyy-MM-dd HH:mm:ss')"  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>结束时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="endTime" id="endTime" #if(!isSaveHandle && taskId!=null) disabled  #end readonly required value="#date(overTimeApply.endTime??,'yyyy-MM-dd HH:mm:ss')"  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item" id="restTimeDiv">
                        <label class="layui-form-label">休息时间段</label>
                        <div class="layui-input-block" style="margin-bottom: 10px;">
                            <button class="layui-btn" type="button" id="restBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end>添加</button>
                            <input name="restCount" id="restCount" value="#(restTimeArray.size()??0)" type="hidden">
                        </div>
                        #for(restTime : restTimeArray)
                        <div class="layui-input-block" id="restTimeItemDiv-#(for.index+1)" style="margin-bottom: 10px;">
                            <label class="layui-form-label" style="padding: 8px 5px;" >休息开始时间</label>
                            <div class="layui-input-inline" style="width: 150px;">
                                <input type="text" name="restStartTime-#(for.index+1)" id="restStartTime-#(for.index+1)" #if(!isSaveHandle && taskId!=null) disabled  readonly #end  required value="#(restTime.restStartTime??)"  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 5px;">休息结束时间</label>
                            <div class="layui-input-inline" style="width: 150px;">
                                <input type="text" name="restEndTime-#(for.index+1)" id="restEndTime-#(for.index+1)" #if(!isSaveHandle && taskId!=null) disabled readonly  #end  required value="#(restTime.restEndTime??)"  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">
                            </div>
                            <button class="layui-btn layui-btn-xs" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button" style="margin-top: 7px;" onclick="del(#(for.index+1))">删除</button>
                        </div>
                        #end
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">加班事由</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(overTimeApply.remark??)</textarea>
                        </div>
                    </div>

            </div>
        </div>
    </div>
    <div class="layui-col-md5">
        #if(stepts.size()??0>0)
        <table class="layui-table" id="steptsTable">
            <!--<colgroup>
                <col width="10%">
                <col width="13%">
                <col width="30%">
                <col width="30%">
                <col width="17%">
            </colgroup>-->
            <thead>
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">节点</th>
                <th style="text-align: center;">流程</th>
                <th style="text-align: center;">意见</th>
                <th style="text-align: center;">操作时间</th>
                <th style="text-align: center;">操作人</th>
            </tr>
            </thead>
            <tbody>
            #for(stept:stepts)
            <tr>
                <td align="center">#(for.index+1)</td>
                <td>#(stept.ActivityName)</td>
                <td align="center">
                    #if(stept.StepState==3)
                    提交
                    #else if(stept.StepState==1)
                    待处理
                    #else if(stept.StepState==0)
                    等待
                    #else if(stept.StepState==2)
                    正处理
                    #else if(stept.StepState==4)
                    撤回
                    #else if(stept.StepState==5)
                    批准
                    #else if(stept.StepState==6)
                    拒绝
                    #else if(stept.StepState==7)
                    转移
                    #else if(stept.StepState==8)
                    失败
                    #else if(stept.StepState==9)
                    跳过
                    #end
                </td>
                <td>#(stept.Comment)</td>
                <td>#(stept.CommentTime)</td>
                <td align="center">#(stept.CommentUserName)</td>
            </tr>
            #end
            </tbody>
        </table>

        <form class="layui-form layui-form-pane" id="taskForm">
            <!--<div class="layui-form-item layui-form-text">
                <label class="layui-form-label">意见</label>
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                </div>
            </div>-->
            <div class="layui-form-item">
                <div class="layui-input-block pull-right">

                    <!--<button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort || !isSaveHandle) disabled style="display: none;" #end >中止</button>
                    <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit || !isSaveHandle) disabled style="display: none;" #end>提交</button>
                    <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject || !isSaveHandle) disabled  style="display: none;" #end>拒绝</button>
                    <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove || !isSaveHandle) disabled style="display: none;" #end>通过</button>-->
                </div>
            </div>
        </form>
        #end
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            #end
            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
            #end
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
        <div class="pull-right">
            <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
        </div>

    </div>
    </form>
</div>

</body>
#end