#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告类型展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }

    .layui-table td,.layui-table th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }


    .layui-table{
        color: #000000;
    }
    .layui-btn-disabled{
        background-color: #e6e6e6;
        color: #000000;
    }

    .layui-disabled, .layui-disabled:hover{
        color: #000000!important;
        cursor: not-allowed!important;
    }
    textarea:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }
    input:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }

</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/noticeType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#noticeTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.noticeTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });






        loadContent=function (taskNo,recordId) {
            var url="";
            if(taskNo=='PH001'){
                url="#(ctxPath)/pers/approval/entryContent";
            }else if(taskNo=='PH002'){
                url="#(ctxPath)/pers/approval/qualifiedContent";
            }else if(taskNo=='PH003'){
                url="#(ctxPath)/pers/approval/changeDeptContent";
            }else if(taskNo=='PH004'){
                url="#(ctxPath)/pers/approval/quitContent";
            }else if(taskNo=='PH006'){
                url="#(ctxPath)/pers/approval/overTimeContent";
            }else if(taskNo=='PH007'){
                url="#(ctxPath)/pers/approval/leaveRestContent";
            }else if(taskNo=='PH008'){
                url="#(ctxPath)/pers/approval/dispathContent";
            }else if(taskNo=='PH009'){
                url="#(ctxPath)/pers/approval/changeApplyContent";
            }else if(taskNo=='PH013'){
                url="#(ctxPath)/pers/approval/businessTripContent";
            }
            $.ajax({
                url : url+'?recordId='+recordId+"&taskId="+$("#taskId").val(),
                type : 'GET',
                success : function(data) {
                    $("#content").html(data);
                    if(taskNo=='PH001'){
                        laydate.render({
                            elem:'#hrDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#edDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#gmDate',
                            type:'date',
                            trigger: 'click'
                        });

                    }else if(taskNo=='PH003'){
                        laydate.render({
                            elem:'#hrSalaryMonth',
                            type:'month',
                            trigger: 'click'
                        });
                    }else if(taskNo=='PH002'){

                        laydate.render({
                            elem:'#baseQualifiedDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#hrQualifiedDate',
                            type:'date',
                            trigger: 'click'
                        });
                    }else if(taskNo=='PH004'){
                    }
                    form.render();
                }
            });
            form.render();
        }


        loadContent("#(persTask.taskNo??)","#(persTask.recordId??)");



        saveData=function(url,data){
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    }
                },
                complete : function() {
                }
            });
        }

        //保存员工入职记录意见
        form.on('submit(saveHrBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'hrOpinion':$("#hrOpinion").val(),'hrInterviewers':$("#hrInterviewers").val(),'hrDate':$("#hrDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        })

        form.on('submit(saveEdBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'edOpinion':$("#edOpinion").val(),'edInterviewers':$("#edInterviewers").val(),'edDate':$("#edDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        })

        form.on('submit(saveGmBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'gmOpinion':$("#gmOpinion").val(),'gmInterviewers':$("#gmInterviewers").val(),'gmDate':$("#gmDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        });

        //保存员工调岗意见


        //1.
        form.on('submit(saveOldDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"oldDeptOpinion":$("#oldDeptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //2.
        form.on('submit(saveNewDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"newDeptOpinion":$("#newDeptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //3.
        form.on('submit(saveBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //4.
        form.on('submit(saveHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"salary":$("#salary").val(),"hrSalaryMonth":$("#hrSalaryMonth").val(),"hrOpinion":$("#hrOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //5.
        form.on('submit(saveGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //保存转正意见

        //1.
        form.on('submit(saveQBaseHr)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"morality":$("#morality").val(),"train":$("#train").val()
            ,"lateCount":$("#lateCount").val(),"sickLeaveCount":$("#sickLeaveCount").val(),"thingLeaveCount":$("#thingLeaveCount").val()
                ,"absenteeismCount":$("#absenteeismCount").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //2.
        form.on('submit(saveQDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"deptOpinion":$("#deptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //3.
        form.on('submit(saveQBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val(),"baseUseOpinion":$("#baseUseOpinion").val()
                ,"baseQualifiedDate":$("#baseQualifiedDate").val(),"baseQualifiedSalary":$("#baseQualifiedSalary").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //4.
        form.on('submit(saveQHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"hrOpinion":$("#hrOpinion").val(),"hrUseOpinion":$("#hrUseOpinion").val()
                ,"hrQualifiedDate":$("#hrQualifiedDate").val(),"hrQualifiedSalary":$("#hrQualifiedSalary").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //5.
        form.on('submit(saveQGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })

        //保存辞职意见
        //1.
        form.on('submit(saveQuitDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"deptOpinion":$("#deptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //2.
        form.on('submit(saveQuitBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //3.
        form.on('submit(saveQuitHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"hrOpinion":$("#hrOpinion").val()
                ,"hrUseOpinion":$("#hrUseOpinion").val(),"hrIsAgree":$("#hrIsAgree").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //4.
        form.on('submit(saveQuitGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })


        //保存员工入职记录流程
        doTask=function (stepState,msg) {
            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }



        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        showPhotos=function(images) {
            layer.photos({
                /*area: '400px',*/
                shade: [0.85, '#000'],
                anim: 0,
                photos: {
                    "title": "附件预览",
                    "id": 'showImages',
                    "data": images
                }
            });
        }

        renderImg=function(src) {
            if(src==''){
                return false;
            }
            var json={
                "title": "身份证图片", //相册标题
                "id": 123, //相册id
                "start": 0, //初始显示的图片序号，默认0
                "data": [   //相册包含的图片，数组格式
                    {
                        "alt": "身份证图片",
                        "src": src, //原图地址
                        "thumb": "" //缩略图地址
                    }
                ]
            };
            layui.layer.photos({
                photos: json
                , anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
                ,tab:function () {
                    num=0;
                    $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                        '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                        '\t</div>');
                    $(document).on("click", "#xuanzhuan", function(e) {
                        num = (num+90)%360;
                        $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                        $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                    });

                    $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                        var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                            (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                        var imagep = $(".layui-layer-phimg").parent().parent();
                        var image = $(".layui-layer-phimg").parent();
                        var h = image.height();
                        var w = image.width();
                        if (delta > 0) {
                            if (h < (window.innerHeight)) {
                                h = h * 1.05;
                                w = w * 1.05;
                            }
                        } else if (delta < 0) {
                            if (h > 100) {
                                h = h * 0.95;
                                w = w * 0.95;
                            }
                        }
                        imagep.css("top", (window.innerHeight - h) / 2);
                        imagep.css("left", (window.innerWidth - w) / 2);
                        image.height(h);
                        image.width(w);
                        imagep.height(h);
                        imagep.width(w);
                    });
                }
            });


        }

        enclosure=function(empId){
            pop_show('附件', '#(ctxPath)/persOrgEmployee/enclosureIndex?employeeId=' +empId , 900, 600);
            return false;
        }
    });
</script>
#end

#define content()
<div class="layui-container" style="width: 100%;padding: 10px 20px;">
    <div class="layui-row">
        <input id="taskId" type="hidden" value="#(taskId??)">
        <input id="userId" type="hidden" value="#(userId??)">
        <input id="taskNo" type="hidden" value="#(persTask.taskNo??)">
        <input id="recordId" type="hidden" value="#(persTask.recordId??)">
        <div class="layui-col-md8 layui-form" id="content" style="padding-right: 10px;">

        </div>
        <div class="layui-col-md4">
            <table class="layui-table">
                <!--<colgroup>
                    <col width="10%">
                    <col width="13%">
                    <col width="30%">
                    <col width="30%">
                    <col width="17%">
                </colgroup>-->
                <thead>
                <tr>
                    <th style="text-align: center;">序号</th>
                    <th style="text-align: center;">节点</th>
                    <th style="text-align: center;">流程</th>
                    <th style="text-align: center;">意见</th>
                    <th style="text-align: center;">操作时间</th>
                    <th style="text-align: center;">操作人</th>
                </tr>
                </thead>
                <tbody>
                #for(stept:stepts)
                    <tr>
                        <td align="center">#(for.index+1)</td>
                        <td>#(stept.ActivityName)</td>
                        <td align="center">
                            #if(stept.StepState==3)
                            提交
                            #else if(stept.StepState==1)
                            待处理
                            #else if(stept.StepState==0)
                            等待
                            #else if(stept.StepState==2)
                            正处理
                            #else if(stept.StepState==4)
                            撤回
                            #else if(stept.StepState==5)
                            批准
                            #else if(stept.StepState==6)
                            拒绝
                            #else if(stept.StepState==7)
                            转移
                            #else if(stept.StepState==8)
                            失败
                            #else if(stept.StepState==9)
                            跳过
                            #end
                        </td>
                        <td>#(stept.Comment)</td>
                        <td>#(stept.CommentTime)</td>
                        <td align="center">#(stept.CommentUserName)</td>
                    </tr>
                #end
                </tbody>
            </table>

            <form class="layui-form layui-form-pane" id="taskForm">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">意见</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea id="msg" name="msg" placeholder="请输入内容"  class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block pull-right">

                        <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                        <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>
                        <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject) disabled style="display: none;" #end>拒绝</button>
                        <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove) disabled style="display: none;" #end>通过</button>
                    </div>
                </div>
            </form>

        </div>
    </div>
</div>
#end
