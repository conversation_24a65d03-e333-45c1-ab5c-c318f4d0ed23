#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()离职申请表#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }

    .layui-table td,.layui-table th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }


    .layui-table{
        color: #000000;
    }
    .layui-btn-disabled{
        background-color: #e6e6e6;
        color: #000000;
    }

    .layui-disabled, .layui-disabled:hover{
        color: #000000!important;
        cursor: not-allowed!important;
    }
    textarea:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }
    input:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }

</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/noticeType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#noticeTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.noticeTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });



        loadContent=function (taskNo,recordId) {
            var url="";
            if(taskNo=='PH001'){
                url="#(ctxPath)/pers/approval/entryContent";
            }else if(taskNo=='PH002'){
                url="#(ctxPath)/pers/approval/qualifiedContent";
            }else if(taskNo=='PH003'){
                url="#(ctxPath)/pers/approval/changeDeptContent";
            }else if(taskNo=='PH004'){
                url="#(ctxPath)/pers/approval/quitContent";
            }
            $.ajax({
                url : url+'?recordId='+recordId+"&taskId="+$("#taskId").val(),
                type : 'GET',
                success : function(data) {
                    $("#content").html(data);
                    if(taskNo=='PH001'){
                        laydate.render({
                            elem:'#hrDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#edDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#gmDate',
                            type:'date',
                            trigger: 'click'
                        });

                    }else if(taskNo=='PH003'){
                        laydate.render({
                            elem:'#hrSalaryMonth',
                            type:'month',
                            trigger: 'click'
                        });
                    }else if(taskNo=='PH002'){

                        laydate.render({
                            elem:'#baseQualifiedDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#hrQualifiedDate',
                            type:'date',
                            trigger: 'click'
                        });
                    }else if(taskNo=='PH004'){
                    }
                    form.render();
                }
            });
            form.render();
        }


        //loadContent("#(persTask.taskNo??)","#(persTask.recordId??)");



        saveData=function(url,data){
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    }
                },
                complete : function() {
                }
            });
        }

        //保存员工入职记录意见
        form.on('submit(saveHrBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'hrOpinion':$("#hrOpinion").val(),'hrInterviewers':$("#hrInterviewers").val(),'hrDate':$("#hrDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        })

        form.on('submit(saveEdBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'edOpinion':$("#edOpinion").val(),'edInterviewers':$("#edInterviewers").val(),'edDate':$("#edDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        })

        form.on('submit(saveGmBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'gmOpinion':$("#gmOpinion").val(),'gmInterviewers':$("#gmInterviewers").val(),'gmDate':$("#gmDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        });

        //保存员工调岗意见


        //1.
        form.on('submit(saveOldDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"oldDeptOpinion":$("#oldDeptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //2.
        form.on('submit(saveNewDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"newDeptOpinion":$("#newDeptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //3.
        form.on('submit(saveBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //4.
        form.on('submit(saveHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"salary":$("#salary").val(),"hrSalaryMonth":$("#hrSalaryMonth").val(),"hrOpinion":$("#hrOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //5.
        form.on('submit(saveGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //保存转正意见

        //1.
        form.on('submit(saveQBaseHr)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"morality":$("#morality").val(),"train":$("#train").val()
                ,"lateCount":$("#lateCount").val(),"sickLeaveCount":$("#sickLeaveCount").val(),"thingLeaveCount":$("#thingLeaveCount").val()
                ,"absenteeismCount":$("#absenteeismCount").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //2.
        form.on('submit(saveQDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"deptOpinion":$("#deptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //3.
        form.on('submit(saveQBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val(),"baseUseOpinion":$("#baseUseOpinion").val()
                ,"baseQualifiedDate":$("#baseQualifiedDate").val(),"baseQualifiedSalary":$("#baseQualifiedSalary").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //4.
        form.on('submit(saveQHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"hrOpinion":$("#hrOpinion").val(),"hrUseOpinion":$("#hrUseOpinion").val()
                ,"hrQualifiedDate":$("#hrQualifiedDate").val(),"hrQualifiedSalary":$("#hrQualifiedSalary").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //5.
        form.on('submit(saveQGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })

        //保存辞职意见
        //1.
        form.on('submit(saveQuitDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"deptOpinion":$("#deptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //2.
        form.on('submit(saveQuitBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //3.
        form.on('submit(saveQuitHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"hrOpinion":$("#hrOpinion").val()
                ,"hrUseOpinion":$("#hrUseOpinion").val(),"hrIsAgree":$("#hrIsAgree").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //4.
        form.on('submit(saveQuitGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })


        //保存员工入职记录流程
        doTask=function (stepState,msg) {
            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }



        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        showPhotos=function(images) {
            layer.photos({
                /*area: '400px',*/
                shade: [0.85, '#000'],
                anim: 0,
                photos: {
                    "title": "附件预览",
                    "id": 'showImages',
                    "data": images
                }
            });
        }

        renderImg=function(src) {
            if(src==''){
                return false;
            }
            var json={
                "title": "身份证图片", //相册标题
                "id": 123, //相册id
                "start": 0, //初始显示的图片序号，默认0
                "data": [   //相册包含的图片，数组格式
                    {
                        "alt": "身份证图片",
                        "src": src, //原图地址
                        "thumb": "" //缩略图地址
                    }
                ]
            };
            layui.layer.photos({
                photos: json
                , anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
                ,tab:function () {
                    num=0;
                    $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                        '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                        '\t</div>');
                    $(document).on("click", "#xuanzhuan", function(e) {
                        num = (num+90)%360;
                        $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                        $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                    });

                    $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                        var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                            (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                        var imagep = $(".layui-layer-phimg").parent().parent();
                        var image = $(".layui-layer-phimg").parent();
                        var h = image.height();
                        var w = image.width();
                        if (delta > 0) {
                            if (h < (window.innerHeight)) {
                                h = h * 1.05;
                                w = w * 1.05;
                            }
                        } else if (delta < 0) {
                            if (h > 100) {
                                h = h * 0.95;
                                w = w * 0.95;
                            }
                        }
                        imagep.css("top", (window.innerHeight - h) / 2);
                        imagep.css("left", (window.innerWidth - w) / 2);
                        image.height(h);
                        image.width(w);
                        imagep.height(h);
                        imagep.width(w);
                    });
                }
            });


        }

        enclosure=function(empId){
            pop_show('附件', '#(ctxPath)/persOrgEmployee/enclosureIndex?employeeId=' +empId , 900, 600);
            return false;
        }
    });
</script>
#end

#define content()


<table class="layui-table" style="width: 1000px;height: 926px;">

    <tbody>
    <tr>
        <input id="currentStepAlias" value="#(currentStepAlias??)" type="hidden">
        <td colspan="6" align="center">员工辞职审批表</td>
    </tr>
    <tr>
        <td colspan="6" style="background-color: #e6e6e6;">提交人信息</td>
    </tr>
    <tr>
        <td>提交人姓名</td>
        <td>#(persTask.submitUserName??)</td>
        <td>提交人部门</td>
        <td>#(persTask.submitUserDeptName??)</td>
        <td>提交人职位</td>
        <td>#(persTask.submitUserPositionName??)</td>
    </tr>
    <tr>
        <td>任务编号</td>
        <td>#(persTask.taskNumber??)</td>
        <td>提交时间</td>
        <td colspan="3">#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')</td>
    </tr>
    <tr>
        <td colspan="6" style="background-color: #e6e6e6;">离职人员信息</td>
    </tr>
    <tr>
        <td>姓名</td>
        <td>#(record.fullName??)</td>
        <td>工号</td>
        <td>#(record.workNum??)</td>
        <td>部门</td>
        <td>#(record.deptName??)</td>
    </tr>
    <tr>
        <td>入职时间</td>
        <td>#date(record.entryTime??,'yyyy-MM-dd')</td>
        <td>转正时间</td>
        <td>#date(record.formalDate??,'yyyy-MM-dd')</td>
        <td>合同有效期至</td>
        <td></td>
    </tr>
    <tr>
        <td>职位</td>
        <td>#(record.positionName??)</td>
        <td>申请日期</td>
        <td>#date(record.create_date??,'yyyy-MM-dd')</td>
        <td>预计离职日期</td>
        <td>#date(record.estimate_quit_date??,'yyyy-MM-dd')</td>
    </tr>
    <tr>
        <td>扣除年休假天数</td>
        <td colspan="5">#(record.deduct_annual_leave_days??0)</td>

    </tr>
    <tr>
        <td>离职原因</td>
        <td colspan="">
            #if(record.quit_type??=='dismiss')
            辞退
            #else if(record.quit_type??=='autonomy')
            自离
            #else if(record.quit_type??=='expel')
            开除
            #else if(record.quit_type??=='other')
            其他
            #else if(record.quit_type??=='auto_quit')
            自动离职
            #else if(record.quit_type??=='voluntarily_quit')
            个人离职
            #else if(record.quit_type??=='resign')
            辞职
            #end

        </td>
        <td>交接人</td>
        <td colspan="3">
            #if(record.transferWorkNum!=null)
            #(record.transferWorkNum??)(#(record.transferFullName??))
            #end
        </td>
    </tr>
    <tr>
        <td colspan="6" style="background-color: #e6e6e6;">辞职原因</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea   class="layui-textarea" disabled  style="color: #000;">#(record.reason??)</textarea>
                </div>
            </div>
        </td>
    </tr>
    <tr>
        <td colspan="6" style="background-color: #e6e6e6;">离职员工对公司的建议</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea   class="layui-textarea" disabled style="color: #000;">#(record.proposal??)</textarea>
                </div>
            </div>
        </td>
    </tr>
    #if(stepCode??0>=1)
    <tr>
        <td colspan="6">用人部意见</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea name="deptOpinion" style="color: #000;" id="deptOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep1!=currentStepAlias?? || !isHandle) disabled #end>#(record.dept_opinion??)</textarea>
                </div>
            </div>
            <div class="layui-row pull-right">
                #if(quitStep1==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" id="saveQuitDeptOpinion" lay-filter="saveQuitDeptOpinion">保&nbsp;&nbsp;存</button>
                #else
                <label class="layui-form-label">处理人：#(record.deptHandlerName??)</label>
                <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.dept_date??,'yyyy-MM-dd')</label>
                #end
            </div>

        </td>
    </tr>
    #end

    #if(stepCode??0>=2)
    <tr>
        <td colspan="6">集团HR意见</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea name="baseOpinion" id="baseOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep2!=currentStepAlias?? || !isHandle) disabled #end>#(record.base_opinion??)</textarea>
                </div>
            </div>
            <div class="layui-row pull-right">
                #if(quitStep2==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" id="saveQuitBaseOpinion" lay-filter="saveQuitBaseOpinion">保&nbsp;&nbsp;存</button>
                #else
                <label class="layui-form-label">处理人：#(record.baseHandlerName??)</label>
                <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.base_date??,'yyyy-MM-dd')</label>
                #end
            </div>

        </td>
    </tr>
    #end

    <!--#if(stepCode??0>=3)
    <tr>
        <td colspan="6">行政部经理意见</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea name="hrOpinion" style="color: #000;" id="hrOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep3!=currentStepAlias?? || !isHandle) disabled #end>#(record.hr_opinion??)</textarea>
                </div>
            </div>
            <div class="layui-row">
                是否面谈：
                <div class="layui-input-inline">
                    <select name="hrUseOpinion" id="hrUseOpinion" #if(quitStep3!=currentStepAlias?? || !isHandle) disabled #end>
                        <option value="">请选择</option>
                        <option value="no_interview" #if(record.hr_use_opinion??=='no_interview') selected #end>未面谈</option>
                        <option value="interviewed" #if(record.hr_use_opinion??=='interviewed') selected #end>已面谈</option>
                    </select>
                </div>
                是否同意：
                <div class="layui-input-inline">
                    <select name="hrIsAgree" id="hrIsAgree" #if(quitStep3!=currentStepAlias?? || !isHandle) disabled #end>
                        <option value="">请选择</option>
                        <option value="1" #if(record.hr_is_agree??=='1') selected #end>是</option>
                        <option value="0" #if(record.hr_is_agree??=='0') selected #end>否</option>
                    </select>
                </div>
            </div>
            <div class="layui-row pull-right">

                #if(quitStep3==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" id="saveQuitHrOpinion" lay-filter="saveQuitHrOpinion">保&nbsp;&nbsp;存</button>
                #else
                <label class="layui-form-label">处理人：#(record.hrHandlerName??)</label>
                <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.hr_date??,'yyyy-MM-dd')</label>
                #end
            </div>
        </td>
    </tr>
    #end-->

    <!--#if(stepCode??0>=4)
    <tr>
        <td colspan="6">总经理意见</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea name="generalManagerOpinion" style="color: #000;" id="generalManagerOpinion" placeholder="请输入内容" class="layui-textarea" #if(quitStep4!=currentStepAlias?? || !isHandle) disabled #end>#(record.general_manager_opinion??)</textarea>
                </div>
            </div>
            <div class="layui-row pull-right">
                #if(quitStep4==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" id="saveQuitGeneralManagerOpinion" lay-filter="saveQuitGeneralManagerOpinion">保&nbsp;&nbsp;存</button>
                #else
                <label class="layui-form-label">处理人：#(record.generalManagerHandlerName??)</label>
                <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.general_manager_date??,'yyyy-MM-dd')</label>
                #end
            </div>

        </td>
    </tr>
    #end-->
    <tr>
        <td colspan="6" style="background-color: #e6e6e6;">备注</td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-form-item layui-form-text">
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea   class="layui-textarea" disabled style="color: #000;">#(record.remark??)</textarea>
                </div>
            </div>
        </td>
    </tr>
    <tr>
        <td colspan="6">
            <div class="layui-row">
                1、辞退人必须提前__天申请，转正员工须提前   天并填写此表，逐级审批，并在审批后交给行政部，否则按急辞处理。
            </div>
            <div class="layui-row">
                2、离职当日，按《离职交接表》进行交接
            </div>
            <div class="layui-row">
                3、此审批表结束后，交行政部存档。
            </div>
        </td>
    </tr>
    </tbody>
</table>
#end












