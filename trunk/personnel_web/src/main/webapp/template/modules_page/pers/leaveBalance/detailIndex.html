#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <input type="hidden" id="empId" value="#(empId??)">
    <input type="hidden" id="leaveType" value="#(leaveType??)">

    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>

</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs" lay-event="viewRecord">查看记录</a>
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;


        msgLoad({"empId":$("#empId").val(),"leaveType":$("#leaveType").val()});
        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/employeeLeaveBalance/detailPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    /*{field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return d.fullName+"("+d.workNum+")";
                        }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }},*/
                    {field:'leaveTypeName',width:100, title: '类型', align: 'center', unresize: true,templet:function (d) {
                            if(d.type=='1'){
                                return '<span class="layui-badge layui-bg-green">增加</span>';
                            }else if(d.type=='2'){
                                return '<span class="layui-badge layui-bg-orange">扣除</span>';
                            }else if(d.type=='3'){
                                return '<span class="layui-badge">修改</span>';
                            }
                        }}
                    ,{field:'valueStr',width:180, title: '操作值', align: 'center', unresize: true}
                    ,{field:'afterValueStr',width:180, title: '操作后剩余值', align: 'center', unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{field:'create_date',width:180, title: '操作时间', align: 'center', unresize: true,templet:"<div>{{dateFormat(d.create_date,'yyyy-MM-dd HH:mm:ss')}}</div>"}
                    ,{field:'name',width:120, title: '操作人', align: 'center', unresize: true}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });





        };

        reloadTab=function () {
            msgLoad({"empId":$("#empId").val()});
        }

    });
</script>
#end