<table class="layui-table">
    <tbody>
        <tr>
            <input id="currentStepAlias" value="#(currentStepAlias??)" type="hidden">
            <td colspan="8" align="center">新员工转正表</td>
        </tr>
        <tr>
            <td>姓名</td>
            <td>#(record.fullName??)</td>
            <td>性别</td>
            <td>#if(record.sex??=='male')男#else if(record.sex??=='female') 女 #else 未知 #end</td>
            <td>年龄</td>
            <td>#(record.age??)</td>
            <td>入职时间</td>
            <td>#date(record.entryTime??,'yyyy-MM-dd')</td>
        </tr>
        <tr>
            <td>部门</td>
            <td>#(record.orgName??)(#(record.deptName??))</td>
            <td>职位</td>
            <td>#(record.positionName??)</td>
            <td>试用薪资</td>
            <td>#(record.probationSalary??)</td>
            <td>填表日期</td>
            <td>#date(record.createDate??,'yyyy-MM-dd')</td>
        </tr>
        <tr>
            <td>学历</td>
            <td>
                #if(record.education??=='bo_shi')
                博士
                #else if(record.education??=='shuo_shi')
                硕士
                #else if(record.education??=='ben_ke')
                本科
                #else if(record.education??=='da_zhuan')
                大专
                #else if(record.education??=='zhong_zhuan')
                高中或中专中技
                #else if(record.education??=='chu_zhong')
                初中及以下
                #end
            </td>
            <td>学校</td>
            <td>#(record.graduateSchool??)</td>
            <td>专业</td>
            <td>#(record.schoolMajor??)</td>
            <td>联系号码</td>
            <td>#(record.phoneNum??)</td>
        </tr>
        <tr>
            <td>自我工作评价及规划</td>
            <td colspan="7">
                #(record.self_evaluation??)
            </td>
        </tr>
        #if(stepCode??0>=1)
        <tr>
            <td rowspan="4">
                <!--基地/中心行政部核准-->
                用人部意见
            </td>
            <td colspan="7">
                1、道德品质：
                <div class="layui-input-inline">
                    <select name="morality" id="morality" #if(qualifiedStep1!=currentStepAlias?? || !isHandle) disabled #end lay-verify="required">
                        <option value="">请选择道德品质</option>
                        <option value="excellent" #if(record.morality??=='excellent')selected #end>优秀</option>
                        <option value="fair" #if(record.morality??=='fair')selected #end>尚可</option>
                        <option value="inferior" #if(record.morality??=='inferior')selected #end>劣</option>
                    </select>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan="7">
                2、培训情况：
                <div class="layui-input-inline">
                    <select name="train" id="train" #if(qualifiedStep1!=currentStepAlias?? || !isHandle) disabled #end lay-verify="required" >
                        <option value="">请选择道德品质</option>
                        <option value="excellent" #if(record.train??=='excellent')selected #end>优秀</option>
                        <option value="fair" #if(record.train??=='fair')selected #end>尚可</option>
                        <option value="inferior" #if(record.train??=='inferior')selected #end>劣</option>
                    </select>
                </div>
            </td>
        </tr>
        <tr>
            <td colspan="7">
                <div class="layui-row">
                    3、出勤情况：迟到<div class="layui-input-inline" style="width: 80px;"><input type="text" #if(qualifiedStep1!=currentStepAlias?? || !isHandle) disabled #end value="#(record.late_count??)" name="lateCount" id="lateCount" lay-verify="required|number" autocomplete="off" class="layui-input"></div>次，
                    病假<div class="layui-input-inline" style="width: 80px;"><input type="text" #if(qualifiedStep1!=currentStepAlias?? || !isHandle) disabled #end value="#(record.sick_leave_count??)" name="sickLeaveCount" id="sickLeaveCount" lay-verify="required|number" autocomplete="off" class="layui-input"></div>次，
                    事假<div class="layui-input-inline" style="width: 80px;"><input type="text" #if(qualifiedStep1!=currentStepAlias?? || !isHandle) disabled #end value="#(record.thing_leave_count??)" name="thingLeaveCount" id="thingLeaveCount" lay-verify="required|number" autocomplete="off" class="layui-input"></div>次，
                    旷工<div class="layui-input-inline" style="width: 80px;"><input type="text" #if(qualifiedStep1!=currentStepAlias?? || !isHandle) disabled #end value="#(record.absenteeism_count??)" name="absenteeismCount" id="absenteeismCount" lay-verify="required|number" autocomplete="off" class="layui-input"></div>次。
                </div>



            </td>

        </tr>
        <tr>
            <td colspan="7">
                <div class="layui-row pull-right">
                    #if(qualifiedStep1==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQBaseHr" lay-filter="saveQBaseHr">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.baseHrHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.base_hr_date??,'yyyy-MM-dd')</label>
                    #end
                </div>
            </td>
        </tr>
        #end


        #if(stepCode??0>=2)
        <tr>
            <td>集团HR意见</td>
            <td colspan="7">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="deptOpinion" id="deptOpinion" lay-verify="required" placeholder="请输入内容" class="layui-textarea" #if(qualifiedStep2!=currentStepAlias?? || !isHandle) disabled #end>#(record.dept_opinion??)</textarea>
                    </div>
                </div>

                <div class="layui-row pull-right">
                    #if(qualifiedStep2==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQDeptOpinion" lay-filter="saveQDeptOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.deptHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.dept_date??,'yyyy-MM-dd')</label>
                    #end
                </div>
            </td>
        </tr>
        #end

        <!--#if(stepCode??0>=3)
        <tr>
            <td rowspan="4">
                基地/中心总经理意见
            </td>
            <td colspan="7">
                <div class="layui-row">
                    基地/中心意见：
                </div>
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="baseOpinion" id="baseOpinion" lay-verify="required" placeholder="请输入内容" class="layui-textarea" #if(qualifiedStep3!=currentStepAlias?? || !isHandle) disabled #end>#(record.base_opinion??)</textarea>
                    </div>
                </div>


            </td>
        </tr>
        <tr>

            <td colspan="7">
                任用意见：
                <div class="layui-input-inline">
                    <select name="baseUseOpinion" id="baseUseOpinion" #if(qualifiedStep3!=currentStepAlias?? || !isHandle) disabled #end lay-verify="required">
                        <option value="">请选择任用意见</option>
                        <option value="advance" #if(record.base_use_opinion??=='advance')selected #end>提前转正</option>
                        <option value="on_time" #if(record.base_use_opinion??=='on_time')selected #end>按时转正</option>
                        <option value="extend" #if(record.base_use_opinion??=='extend')selected #end>延长转正</option>
                    </select>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                转正时间
            </td>
            <td colspan="3">
                <div class="layui-input-inline" style="width: 120px;"><input type="text" #if(qualifiedStep3!=currentStepAlias?? || !isHandle) disabled #end value="#date(record.base_qualified_date??,'yyyy-MM-dd')" name="baseQualifiedDate" id="baseQualifiedDate" lay-verify="required" autocomplete="off" class="layui-input"></div>
            </td>
            <td>
                转正薪资
            </td>
            <td colspan="2">
                <div class="layui-input-inline" style="width: 120px;"><input type="text" #if(qualifiedStep3!=currentStepAlias?? || !isHandle) disabled #end value="#(record.base_qualified_salary??)" name="baseQualifiedSalary" id="baseQualifiedSalary" lay-verify="required|number" autocomplete="off" class="layui-input"></div>
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <div class="layui-row pull-right">
                    #if(qualifiedStep3==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQBaseOpinion" lay-filter="saveQBaseOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.baseHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.base_date??,'yyyy-MM-dd')</label>
                    #end
                </div>
            </td>
        </tr>
        #end-->

        <!--#if(stepCode??0>=4)
        <tr>
            <td rowspan="4">
                总部行政部意见
            </td>
            <td colspan="7">
                <div class="layui-row">
                    行政部意见：
                </div>
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="hrOpinion" id="hrOpinion" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(qualifiedStep4!=currentStepAlias?? || !isHandle) disabled #end>#(record.hr_opinion??)</textarea>
                    </div>
                </div>


            </td>
        </tr>
        <tr>

            <td colspan="7">
                任用意见：
                <div class="layui-input-inline">
                    <select name="hrUseOpinion" id="hrUseOpinion" #if(qualifiedStep4!=currentStepAlias?? || !isHandle) disabled #end lay-verify="required">
                        <option value="">请选择道德品质</option>
                        <option value="advance" #if(record.hr_use_opinion??=='advance')selected #end>提前转正</option>
                        <option value="on_time" #if(record.hr_use_opinion??=='on_time')selected #end>按时转正</option>
                        <option value="extend" #if(record.hr_use_opinion??=='extend')selected #end>延长转正</option>
                    </select>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                转正时间
            </td>
            <td colspan="3">
                <div class="layui-input-inline" style="width: 120px;"><input type="text" #if(qualifiedStep4!=currentStepAlias?? || !isHandle) disabled #end value="#date(record.hr_qualified_date??,'yyyy-MM-dd')" name="hrQualifiedDate" id="hrQualifiedDate" lay-verify="required" autocomplete="off" class="layui-input"></div>
            </td>
            <td>
                转正薪资
            </td>
            <td colspan="2">
                <div class="layui-input-inline" style="width: 120px;"><input type="text" #if(qualifiedStep4!=currentStepAlias?? || !isHandle) disabled #end value="#(record.hr_qualified_salary??)" name="hrQualifiedSalary" id="hrQualifiedSalary" lay-verify="required|number" autocomplete="off" class="layui-input"></div>
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <div class="layui-row pull-right">
                    #if(qualifiedStep4==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQHrOpinion" lay-filter="saveQHrOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.hrHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.hr_date??,'yyyy-MM-dd')</label>
                    #end
                </div>
            </td>
        </tr>
        #end-->


        <!--#if(stepCode??0>=5)
        <tr>
            <td>
                总经理审批
            </td>
            <td colspan="7">
                <div class="layui-row">
                    总经理意见：
                </div>
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="generalManagerOpinion" id="generalManagerOpinion" lay-verify="required" placeholder="请输入内容" class="layui-textarea" #if(qualifiedStep5!=currentStepAlias?? || !isHandle) disabled #end>#(record.general_manager_opinion??)</textarea>
                    </div>
                </div>
                <div class="layui-row pull-right">
                    #if(qualifiedStep5==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" id="saveQGeneralManagerOpinion" lay-filter="saveQGeneralManagerOpinion">保&nbsp;&nbsp;存</button>
                    #else
                    <label class="layui-form-label">处理人：#(record.generalManagerHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.general_manager_date??,'yyyy-MM-dd')</label>
                    #end
                </div>

            </td>
        </tr>
        #end-->
        <tr>
            <td rowspan="3">
                备注
            </td>
            <td colspan="7">
                1、各部门意见主要针对专业知识、学习能力、工作能力、工作态度、执行力等方面；
            </td>
        </tr>
        <tr>
            <td colspan="7">
                2、要求各部门负责人填写内容实事求是，并对改员工现在岗位一切工作表现负责；
            </td>
        </tr>
        <tr>
            <td colspan="7">
                3、转正后的工资按转正考核通过日的下个月起计算发放。
            </td>
        </tr>
    </tbody>
</table>