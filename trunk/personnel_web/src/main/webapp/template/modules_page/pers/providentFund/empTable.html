#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
            <div class="layui-row">
                <form class="layui-form layui-form-pane" method="" action="">
                    <div class="layui-row">
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="width:78px;">姓名</span>
                            <div class="layui-input-inline" style="width: 150px;">
                                <input type="text" id="fullName" class="layui-input" placeholder="请输入姓名搜索" autocomplete="off">
                            </div>
                        </div>
                        &nbsp;
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="padding: 8px 5px;">证件号码</span>
                            <div class="layui-input-inline">
                                <input type="text" id="idCard" class="layui-input" placeholder="请输入证件号码搜索" autocomplete="off">
                            </div>
                        </div>
                        &nbsp;
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="padding: 8px 5px;">状态</span>
                            <div class="layui-input-inline" style="width:140px;">
                                <select name="archiveStatus" id="archiveStatus">
                                    #dictOption("archive_status", "incumbency", "")
                                </select>
                            </div>
                        </div>
                        &nbsp;
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="padding: 8px 5px;">组织架构</span>
                            <div class="layui-input-inline" style="width:260px;">
                                <div id="deptSelect" >

                                </div>
                            </div>
                        </div>


                        <div class="layui-btn-group" >
                            <button type="button" id="btn-search" class="layui-btn mgl-20">查询</button>
                        </div>
                    </div>

                </form>
            </div>
            <div class="layui-row">
                <table id="employeeTable" lay-filter="employeeTable"></table>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs " lay-event="add">新增</a>
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="continue">续买</a>
        <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="del">删除</a>
        <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="abandon">弃买</a>
    </div>
</script>


<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });



    layui.use(['table', 'vip_table','form'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeTable'
            ,form=layui.form
        ;

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })



        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        loadTable();

        // 表格加载渲染
        function loadTable() {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var tableObj = table.render({

                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/persOrgEmployee/pageTable'
                ,where : {'deptId':id,"archiveStatus":$("#archiveStatus").val()}//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {field:'workNum', title:'工号',width:85, align:'center'}
                    ,{field:'fullName', title:'姓名',width:90, align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
                    ,{field:'', title:'部门',minWidth:150, align:'center',templet:function (d) {
                            if(d.org==undefined || d.org.orgName==undefined){
                                return '';
                            }else{
                                return d.org.orgName;
                            }
                        }}
                    ,{field:'', title:'职位',minWidth:130, align:'center',templet:function (d) {
                            if(d.org==undefined || d.org.positionName==undefined){
                                return '';
                            }else{
                                return d.org.positionName;
                            }
                        }}
                    ,{field:'', title:'角色',minWidth:120, align:'center',templet:function (d) {
                            if(d.org==undefined || d.org.roleName==undefined){
                                return '';
                            }else{
                                return d.org.roleName;
                            }
                        }}
                    ,{field:'', title:'性别', width:60, align:'center', templet:'#dictTpl("sex", "sex")'}
                    ,{field:'', title:'民族', minWidth:110, align:'center', templet:'#dictTpl("nation", "nationality")'}
                    ,{field:'phoneNum', title:'手机号', minWidth:90, align:'center'}
                    ,{field:'', title:'入职日期', width:90, align:'center',templet:"<div>{{ dateFormat(d.entryTime,'yyyy-MM-dd') }}</div>"}
                    ,{title:'操作', fixed:'right', width:150, align:'center', toolbar:'#empTableBar'}

                ]]
                ,page : true
                ,done:function () {
                    //
                    layer.closeAll('loading');
                }
            });
        }

        //监听工具条
        table.on('tool('+tableId+')', function(obj) {
            var isLocal='';
            var maritalStatus='';
            console.log(obj.data.maritalStatus);
            if(obj.data.maritalStatus!=undefined){
                maritalStatus=obj.data.maritalStatus
                console.log("不是andifai");
            }
            if(obj.data.isLocal!=undefined){
                isLocal=obj.data.isLocal
            }
            var yearMonth=parent.$("#yearMonth").val();

            $.post('#(ctxPath)/providentFund/getLastMonthStatus',{"yearMonth":yearMonth,"empId":obj.data.id},function (res) {
                var sex="";
                if(obj.data.sex=='male'){
                    sex="男";
                }else if(obj.data.sex=='female'){
                    sex="女";
                }
                if(res.state=='ok'){
                    var lastMonthType=res.data.providentFundType;
                    if (obj.event === 'add') {
                        /*if("2"==lastMonthType){
                            //增、续移动到弃
                            layer.msg('上月状态为[续]的本月不能添加至[增]',{icon:5});
                            return false;
                        }*/
                        parent.addTr(obj.data.id,obj.data.workNum,obj.data.fullName,obj.data.idCard,maritalStatus,isLocal,obj.data.phoneNum,"1",res.data.providentFundType,sex,obj.data.org.orgName,obj.data.org.positionName);
                    }else if (obj.event === 'continue') {
                        /*if("3"==lastMonthType || "4"==lastMonthType){
                            //增、续移动到弃
                            layer.msg('上月状态为[删、弃]的本月不能添加至[续]，请先添加至[增]',{icon:5});
                            return false;
                        }*/
                        parent.addTr(obj.data.id,obj.data.workNum,obj.data.fullName,obj.data.idCard,maritalStatus,isLocal,obj.data.phoneNum,"2",res.data.providentFundType,sex,obj.data.org.orgName,obj.data.org.positionName);
                    }else if (obj.event === 'del') {
                        /*if("4"==lastMonthType){
                            //增、续移动到弃
                            layer.msg('上月状态为[弃]的本月不能添加至[删]',{icon:5});
                            return false;
                        }*/
                        parent.addTr(obj.data.id,obj.data.workNum,obj.data.fullName,obj.data.idCard,maritalStatus,isLocal,obj.data.phoneNum,"3",res.data.providentFundType,sex,obj.data.org.orgName,obj.data.org.positionName);
                    }else if(obj.event==='abandon'){
                        /*if("1"==lastMonthType || "2"==lastMonthType){
                            //增、续移动到弃
                            layer.msg('上月状态为[增、续]的本月不能添加至[弃]，请先添加至[删]',{icon:5});
                            return false;
                        }*/
                        parent.addTr(obj.data.id,obj.data.workNum,obj.data.fullName,obj.data.idCard,maritalStatus,isLocal,obj.data.phoneNum,"4",res.data.providentFundType,sex,obj.data.org.orgName,obj.data.org.positionName);
                    }
                }

            });
        });
        //重载表格，跳转到第一页
        pageTableReload = function () {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            tableReload(tableId,{isContainChildren:'1',phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
                'deptId':id,"sort":$("#sort").val(),'position':$("#position").val()});
        }

        //重载表格，跳转当前页面
        pageTableReloadCurrPage = function(){
            layer.load();
            var currPage=$(".layui-laypage-skip").children("input").val();
            table.reload(tableId,{'where':{isContainChildren:'1',phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
                    deptId:$(".layui-select-title input[dtree-id='deptId']").val(),"sort":$("#sort").val(),'position':$("#position").val()},page:{'curr':currPage}});
        }

        showPhotos=function(images) {
            layer.photos({
                /*area: '400px',*/
                shade: [0.85, '#000'],
                anim: 0,
                photos: {
                    "title": "附件预览",
                    "id": 'showImages',
                    "data": images
                }
            });
        }

        //搜索按钮点击事件
        $('#btn-search').on('click', function() {
            pageTableReload();
        });
        //添加按钮点击事件
        $('#btn-add').on('click', function() {
            pop_show('添加', '#(ctxPath)/persOrgEmployee/add', '900', '');
        });
        // 刷新
        $('#btn-refresh').on('click', function () {
            pageTableReload();
        });


    });
</script>
#end