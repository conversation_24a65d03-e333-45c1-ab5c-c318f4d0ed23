<script src="../../../../static/js/jquery-3.3.1.min.js"></script>#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<link rel="stylesheet" href="/static/css/formSelects-v4.css">
<link rel="stylesheet" href="/static/css/mouseRightMenu.css">

<style>

    #empSelect xm-select{
        border-bottom: 0px;
        border-right: 0px;
        border-top: 0px;
    }
    #empSelect .xm-icon{
        display: none;
    }

    #test-n2 ,#test-n2 .layui-laydate-main,#test-n2 .layui-inline,#test-n2 .layui-laydate{
        width: 100%;
    }
    #test-n2 table {
        width: 100%;
        min-height: 600px;
    }
    #test-n2 .layui-laydate-content td {
        border: 1px solid #d2d2d2;
        vertical-align: top;
        padding-top: 15px;
    }
    #test-n2 .layui-form-checkbox[lay-skin=primary] {
        height: 20px !important;
        margin-right: 0;
        padding-right: 0;
        background: 0 0;
    }
    .layui-form-label{
        height: 38px !important;
    }
    #test-n2 .layui-laydate {
        font-size: 26px!important;
    }

</style>
#end

#define js()
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script src="/static/js/jquery-3.3.1.min.js" type="text/javascript" charset="utf-8"></script>
<script src="/static/js/extend/formSelects-v4.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','form', 'vip_table','laydate','laytpl','mouseRightMenu'], function() {
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            ,laydate=layui.laydate
            ,laytpl=layui.laytpl
            ,form=layui.form
            ,formSelects=layui.formSelects
            ,mouseRightMenu=layui.mouseRightMenu
        ;



        var deptSelectInitValue=[];
        #for(deptRuleRange : deptRuleRangeList)
        deptSelectInitValue.push("#(deptRuleRange.checkinId??)");
        #end

        var deptSelect;
        $.post('#(ctxPath)/persOrg/orgXmSelectTree',{},function (res) {



            deptSelect = xmSelect.render({
                el: '#deptSelect',
                autoRow: true,
                filterable: true,
                height: '200px',
                prop: {
                    name: 'name',
                    value: 'id',
                },
                tree: {
                    show: true,
                    showFolderIcon: true,
                    showLine: true,
                    indent: 15,
                    lazy: true,
                    clickExpand: true,
                    strict: false,
                    expandedKeys: ['54325705-FF63-43DB-9723-FA31E94AF8E3'],
                    //点击节点是否选中
                    clickCheck: true,
                    load: function(item, cb){

                    }
                },
                height: 'auto',
                data(){
                    return res;
                },
                initValue: deptSelectInitValue
            });
        });



        var empXmSelectInitValue=[];
        var empXmSelectData=[];
        #for(empRuleRangeRecord : empRuleRangeRecordList)
        empXmSelectInitValue.push({'name':'#(empRuleRangeRecord.name??)','value':'#(empRuleRangeRecord.id??)','orgName':'#(empRuleRangeRecord.orgName??)','selected':true});
        empXmSelectData.push({'name':'#(empRuleRangeRecord.name??)','value':'#(empRuleRangeRecord.id??)','orgName':'#(empRuleRangeRecord.orgName??)','selected':true});
        #end

        var empXmSelect = xmSelect.render({
            el: '#empSelect',
            data: empXmSelectInitValue,
            autoRow: true,
            show(){
                return false;
            },
            on: function(data){
                //arr:  当前多选已选中的数据
                var arr = data.arr;
                //change, 此次选择变化的数据,数组
                var change = data.change;
                //isAdd, 此次操作是新增还是删除
                var isAdd = data.isAdd;
                if(!isAdd){
                    $.each(empXmSelectData,function (index,item) {
                        if(item.value==change[0].value){
                            empXmSelectData.splice(index, 1);
                            return false;
                        }
                    });
                    empXmSelect.update({
                        data:empXmSelectData
                    });
                    //
                    if(scheduleDataArray.length>0){

                        $.each(scheduleDataArray,function (index,item) {
                            var date=new Date(item.date);
                            var selectedArrayEach=item.selectedArray;
                            $.each(selectedArrayEach,function (index2,item2) {
                                if(item2.value==change[0].value){
                                    selectedArrayEach.splice(index2, 1);
                                }
                            })
                        });
                        getSysSchedule();
                    }

                }
            }
        });

        setQuitEmpIdValue=function(id,name,orgName){
            empXmSelectData. push({'name':name,'value':id,'orgName':orgName,selected:true});
            empXmSelect.update({
                data:empXmSelectData
            })
        }

        $("#addEmpBtn").on('click',function () {
            var url='#(ctxPath)/employeeCheckinRule/empTable';
            pop_show("员工表",url,1320,700);
        });

        $("#addDateBtn").on('click',function () {
            var url='#(ctxPath)/employeeCheckinRule/workDaysForm';
            pop_show("打卡时间",url,900,550);
        });

        editWorkDate=function(index){
            var data=$("#checkinDate-"+index).val();
            var url='#(ctxPath)/employeeCheckinRule/workDaysForm?index='+index;
            pop_show("打卡时间",url,900,550);
        }

        getWorkDays=function(excludeIndex){
            var trArray=$("#tbody").find("tr");
            var workDays=[];
            $.each(trArray,function (index,item) {
                var id=$(item).attr("id");
                var trIndex=id.substr(id.indexOf("tr-")+3);
                if(trIndex!=excludeIndex){
                    var trWorkDays=JSON.parse($("#workDays-"+trIndex).val());
                    $.each(trWorkDays,function (index2,item2) {
                        workDays.push(item2);
                    })
                }
            });
            return workDays;
        }

        var hour={"00":"00","01":"01","02":"02","03":"03","04":"04","05":"05","06":"06","07":"07","08":"08","09":"09"
            ,"10":"10","11":"11","12":"12","13":"13","14":"14","15":"15","16":"16","17":"17","18":"18","19":"19","20":"20"
            ,"21":"21","22":"22","23":"23","24":"次日00","25":"次日01","26":"次日02","27":"次日03","28":"次日04","29":"次日05","30":"次日06"
            ,"31":"次日07","32":"次日08","33":"次日09","34":"次日10","35":"次日11","36":"次日12"};

        addTpl=function(data,trIndex) {
            var workDaysCount=$("#workDaysCount").val();
            var idx=Number(workDaysCount)+1;

            var wokrDaysStr="";

            var dataStr={"1":"周一","2":"周二","3":"周三","4":"周四","5":"周五","6":"周六","0":"周日"}
            $.each(data.workDays,function (index,item) {
                var str=dataStr[item];
                if(index==0){
                    wokrDaysStr=str;
                }else{
                    wokrDaysStr+="、"+str;
                }
            });
            var html="";
            $.each(data.checkTimes,function (index,item) {
                var workTime=item.work;
                var offWorkTime=item.offWork;
                var workHour=workTime.substring(0,workTime.indexOf(":"));
                var workMinute=workTime.substring(workTime.indexOf(":")+1);
                var offWorkHour=offWorkTime.substring(0,offWorkTime.indexOf(":"));
                var offWorkMinute=offWorkTime.substring(offWorkTime.indexOf(":")+1);
                html+="<span style='line-height: 30px;'>上班 "+hour[workHour]+":"+workMinute+"——下班 "+hour[offWorkHour]+":"+offWorkMinute+"</span><br>";
            });

            if(trIndex!=''){
                html+="<input type='hidden' id='workDays-"+trIndex+"' value='"+JSON.stringify(data.workDays)+"' >";
                var tplData={'idx':trIndex,"workDays":wokrDaysStr,"workTimes":html,"value":JSON.stringify(data)};
            }else{
                html+="<input type='hidden' id='workDays-"+idx+"' value='"+JSON.stringify(data.workDays)+"' >";
                var tplData={'idx':idx,"workDays":wokrDaysStr,"workTimes":html,"value":JSON.stringify(data)};
            }

            laytpl(workDaysTpl.innerHTML).render(tplData, function(html){
                if(trIndex!=''){

                    $("#tr-"+trIndex).html($(html).html());
                }else{
                    $("#tbody").append(html);
                    $("#workDaysCount").val(idx);
                }

            });
        }

        #if(checkinRule.type??=='1')
        #for(checkinDate : checkinDateList)
        var checkinTimeArray=#(checkinDate.checkinTime);
        var checkinTimeDataArray=[];
        $.each(checkinTimeArray,function (index,item) {
            checkinTimeDataArray.push({"work":item[0],"offWork":item[1]});
        })
        var data={"workDays":#(checkinDate.workDays),"noneedOffwork":#(checkinDate.noneedOffwork),"isManyCheckin":#(checkinDate.isManyCheckin),"checkTimes":checkinTimeDataArray};
        addTpl(data,'');
        #end
        #end

        del=function(index){
            $("#tr-"+index).remove();
            //showHideBtn();
        }

        getcheckinDate=function (index) {
            return $("#checkinDate-"+index).val();
        }
        //添加节假日按钮点击事件
        $("#addHolidaysBtn").on('click',function () {
            pop_show("节假日",'#(ctxPath)/employeeCheckinRule/holidaysIndex',800,700);
        });

        form.on('radio(type_tadio)',function (obj) {
            if(obj.value=='1'){
                $("#checkinDate1").show();
                $("#checkinDate2").hide();
                $("#scheduleDiv").hide();
                $("#checkinDate3").hide();
                $("#isManyCheckin").hide();
            }else if(obj.value=='2'){
                $("#checkinDate1").hide();
                $("#checkinDate2").show();
                $("#scheduleDiv").show();
                $("#checkinDate3").hide();
                $("#isManyCheckin").hide();
            }else if(obj.value=='3'){
                $("#checkinDate1").hide();
                $("#checkinDate2").hide();
                $("#scheduleDiv").hide();
                $("#checkinDate3").show();
                $("#isManyCheckin").show();
            }
        });

        form.on('submit(saveBtn)', function(formObj) {
            //$("#noticeForm")

            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "选择重新生成数据日期",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['600px;','200px;'],
                content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                    "            <div class=\"layui-form-item\" style='margin-top: 0px;'>\n" +
                    "                <label class=\"layui-form-label\" style='width: 180px;'><font color='red'>*</font>重新生成数据日期范围</label>\n" +
                    "                <div class=\"layui-input-block\" style='margin-left: 180px;' >\n" +
                    "                    <input class=\"layui-input\" data-id='yearMonth' value='' name=\"yearMonth\" id=\"yearMonth\" autocomplete=\"off\">\n" +
                    "                </div>\n" +
                    "            </div>\n" +
                    "</form>",
                cancel: function(){
            },
            end : function(){

                return false;
            },yes: function(index, layero){
                    let dateRange=$("#yearMonth").val();
                    if(dateRange.indexOf(" - ")==-1){
                        layer.msg('请选择重新生成数据日期范围', {icon: 2});
                        return false;
                    }
                    var depts=deptSelect.getValue();
                    var emps=empXmSelect.getValue();
                    var deptIds=[];
                    var empIds=[];
                    $.each(depts,function (index,item) {
                        deptIds.push(item.id);
                    })
                    $.each(emps,function (index,item) {
                        empIds.push(item.value);
                    });
                    if(deptIds.length==0 && empIds.length==0){
                        layer.msg('请添加打卡部门或人员', {icon: 2});
                        return false;
                    }
                    var type=$("input:radio[name='type']:checked").val();
                    var workDays=[];
                    var checkinTimeData=[];

                    var noneedOffwork='0';
                    $("input:checkbox[name='noneedOffwork']:checked").each(function(i){
                        noneedOffwork=$(this).val();
                    });

                    var data={'id':$("#id").val(),'type':type,'deptIds':deptIds,'empIds':empIds,'name':$("#name").val(),'noneedOffwork':noneedOffwork};
                    if(type=='1'){
                        var trs=$("#tbody").find("tr");
                        if(trs.length==0){
                            layer.msg('请添加打卡时间', {icon: 2});
                            return false;
                        }
                        $.each(trs,function (index,item) {
                            var id=$(item).attr("id");
                            var trIndex=id.substr(id.indexOf("tr-")+3);
                            checkinTimeData.push(JSON.parse($("#checkinDate-"+trIndex).val()));
                        });
                        data["checkinTimeData"]=checkinTimeData;
                    }else if(type=='2'){
                        var trs=$("#scheduleTbody").find("tr");
                        if(trs.length==0){
                            layer.msg('请添加班次', {icon: 2});
                            return false;
                        }
                        var scheduleArray=[];
                        $.each(trs,function (index,item) {
                            var id=$(item).attr("id");
                            var trIndex=id.substr(id.indexOf("scheduleTr-")+"scheduleTr-".length);
                            scheduleArray.push(JSON.parse($("#schedule-"+trIndex).val()));
                        });
                        if(scheduleArray.length==0){
                            layer.msg('请添加班次', {icon: 2});
                            return false;
                        }
                        data["scheduleArray"]=scheduleArray;
                        data["scheduleDataArray"]=scheduleDataArray;

                    }else if(type=='3'){
                        $("input:checkbox[name='workDays']:checked").each(function(i){
                            workDays.push($(this).val());
                        });
                        if(workDays.length==0){
                            layer.msg('请添加打卡时间', {icon: 2});
                            return false;
                        }
                        data["workDays"]=workDays;
                    }
                    layer.load();
                    //提交表单数据
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/employeeCheckinRule/saveCheckinRule',
                        data: {"data":JSON.stringify(data)},
                        notice: true,
                        loadFlag: false,
                        success : function(rep){
                            if(rep.state=='ok'){

                                window.top.layer.msg('生成打卡记录中...请等待1-2分钟去看考勤数据，无需停留此页面', {icon: 1, offset: 'auto',time: 25000});
                                //生成打卡数据
                                util.sendAjax ({
                                    type: 'POST',
                                    url: '#(ctxPath)/employeeCheckinRule/genRepairScheduleCheckinData',
                                    data: {"id":'#(checkinRule.id??)','startDate':dateRange.split(' - ')[0],'endDate':dateRange.split(' - ')[1]},
                                    notice: true,
                                    loadFlag: false,
                                    success : function(rep){
                                        layer.closeAll('loading');
                                        if(rep.state=='ok'){

                                            parent.reloadTable();
                                            pop_close();
                                        }
                                    },
                                    complete : function() {
                                    }
                                });

                            }
                        },
                        complete : function() {
                        }
                    });
            },success:function () {
                    laydate.render({
                        elem: '#yearMonth'
                        ,trigger: 'click'
                        ,type: 'date'
                        ,range: true
                        ,min:'#(minDate??)'
                        ,max:'#(maxDate??)'
                    });

                }
            });

            return false;
        });

        //Tips
        $('*[lay-tips]').on('mouseenter', function(){
            var content = $(this).attr('lay-tips');

            this.index = layer.tips('<div style="padding: 10px; font-size: 14px; color: #eee;">'+ content + '</div>', this, {
                time: -1
                ,maxWidth: 280
                ,tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function(){
            layer.close(this.index);
        });


































        var sysScheduleArray = [];
        var sysScheduleUserArray = [];

        //页面上的班次
        var scheduleArray=[];
        //页面上的部门下的员工以及选中的打卡人员
        var empArray=[];


        loadScheduleSelect=function(data){
            //班次选择
            formSelects.data('text_book', 'local',{
                arr: data
                ,keyName: 'name',
                keyVal: 'name'
            });
        }

        empFormSelects=xmSelect.render({
            el: '#empXmSelect',
            filterable: false,
            data: empArray
        })

        function addZero(str) {
            let strArray = str.split("-");
            strArray = strArray.map(function(val) {
                if (val.length==1) {
                    return "0"+val;
                } else {
                    return val;
                }
            });
            return strArray.join("-");
        }

        copyContentLoad=function () {
            var copyData= {};
            //右键监听
            $('#schedulingDiv').find("td").bind("contextmenu",function(e){


                var data = {content:$(this).attr("lay-ymd")}
                var menu_data=[
                    {'data':data,'type':1,'title':'复制'},
                    {'data':data,'type':2,'title':'粘贴'},
                    {'data':data,'type':3,'title':'清空'},
                    {'data':data,'type':4,'title':'取消'}
                ]

                var now=addZero($(this).attr("lay-ymd"));



                mouseRightMenu.open(menu_data,false,function(d){
                    if(d.type=='1'){
                        //复制
                        $.each(scheduleDataArray,function (index,item) {
                            if(item.date==now){
                                copyData=item;
                            }
                        });

                        if(copyData.date==undefined){
                            layer.msg('操作失败，复制内容为空', {icon: 2});
                        }
                    }else if(d.type=='2'){
                        //粘贴
                        //scheduleDataArray

                        var currTimestamp = Date.parse(new Date());
                        var objDateTimestamp = Date.parse(now);
                        /*if(currTimestamp<=objDateTimestamp){
                            layer.msg('只允许设置超过今天的日期', {icon: 2});
                            return false;
                        }*/

                        if(copyData.date==undefined){
                            parent.layer.msg('操作失败，粘贴内容为空', {icon: 2});
                        }else{
                            $.each(scheduleDataArray,function (index,item) {
                                if(item.date==now){
                                    scheduleDataArray.splice(index,1);
                                }
                            });

                            var newCopyData=JSON.parse(JSON.stringify(copyData));
                            newCopyData.date=now;
                            scheduleDataArray.push(newCopyData);
                            getSysSchedule();
                        }
                    }else if(d.type=='3'){
                        var currTimestamp = Date.parse(new Date());
                        var objDateTimestamp = Date.parse(now);
                        if(currTimestamp>=objDateTimestamp){
                            layer.msg('只允许设置超过今天的日期', {icon: 2});
                            return false;
                        }
                        $.each(scheduleDataArray,function (index,item) {
                            if(item.date==now){
                                scheduleDataArray.splice(index,1);

                                var date=new Date(now);
                                var choosedate = $('.layui-laydate-content td[lay-ymd='+date.getFullYear()+'-'+(date.getMonth()+1)+'-'+date.getDate()+']');//选中所有显示日期的td
                                choosedate.find("div").remove();
                            }
                        });
                    }


                });

                $(".layui-layer-shade").bind("contextmenu", function (e) {
                    return false;
                });
                //$(".layui-layer-shade").css("z-index","-123123131");
                $(".layer-skin-mouse-right-menu").bind("contextmenu", function (e) {
                    return false;
                });
                //$(".layer-skin-mouse-right-menu").css("z-index","-123123131");
                return false;
            });

            //绑定悬浮提示
            $('#schedulingDiv').find("td").attr('title','右击日期可复制粘贴排班内容');
        }



        //存储值
        var scheduleDataArray=[];


        var new_date = new Date();
        loding_date(new_date);
        //日历插件调用方法
        function loding_date(date_value){
            laydate.render({
                elem: '#test-n2'
                ,theme: '#3982f7'
                ,type: 'date'
                ,min: '#(minDate)'
                ,max: '#(maxDate)'
                ,position: 'static'
                ,value: '#(maxDate)'
                ,range: false
                ,value:date_value
                ,btns:false
                ,ready: function(data){
                    getSysSchedule();
                    return false
                },
                change:function (value, date, endDate){
                    getSysSchedule();
                    return false
                }
                ,done: function(value, date, endDate){
                    date_chose(value);
                    return false
                }
            });

            copyContentLoad();
        }

        //数据回显
        function getSysSchedule(){
            $.each(scheduleDataArray,function (index,item) {
                var date=new Date(item.date);
                var selectedArrayEach=item.selectedArray;
                var choosedate = $('.layui-laydate-content td[lay-ymd='+date.getFullYear()+'-'+(date.getMonth()+1)+'-'+date.getDate()+']');//选中所有显示日期的td
                choosedate.find("div").remove();
                $.each(selectedArrayEach,function (index2,item2) {
                    choosedate.append('<div style="font-size: 14px;">'+ item2.name+'——'+item2.selectedName+'</div>');
                })
            })

        }
        #if(empScheduleDataArray!=null)
        scheduleDataArray=JSON.parse('#(empScheduleDataArray??)');
        #else
        scheduleDataArray=[];
        #end

        getSysSchedule();

        //获取排班数据
        getScheduleData=function () {
            var scheduleTrs=$("#scheduleTbody").find("tr");
            var scheduleData=[];
            $.each(scheduleTrs,function (index,item) {
                var id=$(item).attr("id");
                var trIndex=id.substr(id.indexOf("scheduleTr-")+"scheduleTr-".length);
                var data=JSON.parse($("#schedule-"+trIndex).val());
                scheduleData.push(data.name);
            })
            return scheduleData;
        }

        //获取选中id
        var scheduleIds = [];
        var selectedName = [];
        var selected = [];



        //定义弹出层方法
        function date_chose(obj_date){
            var depts=deptSelect.getValue();
            var emps=empXmSelect.getValue();
            var deptIds=[];
            var empIds=[];
            $.each(depts,function (index,item) {
                deptIds.push(item.id);
            })
            $.each(emps,function (index,item) {
                empIds.push(item.value);
            });
            var currTimestamp = Date.parse(new Date());
            var objDateTimestamp = Date.parse(obj_date);
            /*if(currTimestamp>=objDateTimestamp){
                layer.msg('排班只允许设置超过今天的日期', {icon: 2});
                return false;
            }*/
            if(deptIds.length==0 && empIds.length==0){
                layer.msg('请添加打卡部门或人员', {icon: 2});
                return false;
            }
            //获取班次数据
            scheduleArray=[];
            var scheduleData=getScheduleData();
            $.each(scheduleData,function (index,item) {
                scheduleArray.push({'name':item});
            });
            if(scheduleArray.length==0){
                layer.msg('请添加班次', {icon: 2});
                return false;
            }

            //loadScheduleSelect(scheduleArray);
            empArray=[];

            //获取部门下及子部门的人员
            if(deptIds.length>0){
                $.post('#(ctxPath)/employeeCheckinRule/findEmpByDeptId',{'deptIds':JSON.stringify(deptIds)},function (res){
                    $.each(empXmSelectData,function (index,item) {
                        empArray.push({'value':item.value,'name':item.name,'orgName':item.orgName,'oName':item.name+" ("+item.orgName+")"});
                    });
                    if(res.state=='ok'){
                        $.each(res.data,function (index,item) {
                            var suc=false;
                            $.each(empXmSelectData,function (index2,item2) {
                                if(item2.value==item.id){
                                    suc=true;
                                    return false;
                                }
                            })
                            if(!suc){
                                empArray.push({'value':item.id,'name':item.fullName,'orgName':item.org.orgName,'oName':item.fullName+" ("+item.org.orgName+")"});
                            }

                        });
                        $("#empXmSelectValue").val(JSON.stringify(empArray));
                        /*empFormSelects.update({
                            data: empArray
                        })*/
                    }else{
                        layer.msg(res.msg, {icon: 2, time: 1000});
                    }

                });
            }else{
                $.each(empXmSelectData,function (index,item) {
                    empArray.push({'value':item.value,'name':item.name,'orgName':item.orgName,'oName':item.name+" ("+item.orgName+")"});
                });
                $("#empXmSelectValue").val(JSON.stringify(empArray));
                /*empFormSelects.update({
                    data: empArray
                })*/
            }
            $("#select").empty();

            setTimeout(function () {
                $.each(scheduleArray,function (index,item) {
                    var html='<fieldset data-index="'+index+'" class="layui-elem-field" style="padding-bottom: 10px;margin-top: 10px;">\n' +
                        '                                <legend>'+item.name+'</legend>\n' +
                        '                                <div class="layui-form-item" style="margin: 10px 10px 0;">\n' +
                        '                                    <label class="layui-form-label" ><font color="red">*</font>员工</label>\n' +
                        '                                    <div class="layui-input-block">\n' +
                        '                                        <div id="empXmSelect-'+index+'" class="xm-select"  >\n' +
                        '                                        </div>\n' +
                        '                                    </div>\n' +
                        '                                </div>' +
                        '                                  <input text-name="scheduleName" data-index="'+index+'" name="scheduleName-'+index+'" id="scheduleName-'+index+'" type="hidden" value="'+item.name+'"> ' +
                        '                            </fieldset>';
                    $("#select").append(html);
                    if($("#empXmSelectValue").val()!=''){
                        xmSelect.render({
                            el: '#empXmSelect-'+index,
                            tips: '请选择 '+item.name+' 打卡的员工',
                            filterable: true,
                            autoRow: true,
                            filterMethod: function(val, selectItem, index2, prop){
                                var selectItemName=selectItem.oName;
                                selectItemName+="";
                                if(selectItemName.indexOf(val) != -1){
                                    return true;
                                }
                                return false;//不知道的就不管了
                            },
                            prop: {
                                name: 'oName',
                                value: 'value',
                            },
                            height:250,
                            data: JSON.parse($("#empXmSelectValue").val())
                        })
                    }

                });
            }, 1000);



            /*$("#select").empty();*/

            var index = layer.open({
                type: 1,
                skin:'colorClass',
                title:'排班',
                area: ['800px', '600px'], //宽高
                btn:['确定','删除','取消'],
                content: $('#select')
                ,success:function(){
                    setTimeout(function () {
                        var echoData=[];
                        $.each(scheduleDataArray,function (index,item) {
                            if(item.date==obj_date){
                                var selectedArrayEach=item.selectedArray;
                                var data={};
                                $.each(selectedArrayEach,function (index2,item2) {
                                    var fieIndex=$("input:hidden[value='"+item2.selectedName+"'][text-name='scheduleName']").attr("data-index");
                                    if(data.hasOwnProperty("empXmSelect-"+fieIndex)){
                                        var dataArray = data["empXmSelect-"+fieIndex];
                                        dataArray.push(item2.value);
                                    }else{
                                        data["empXmSelect-"+fieIndex]=[item2.value];
                                    }
                                });
                                $.each(data, function(i) {
                                    var selectEmp=xmSelect.get('#'+i,true);
                                    selectEmp.append(data[i]);
                                });
                            }
                        });
                    }, 1100);
                }
                ,yes:function (){
                    var selectEmpSchedule=[];
                    var fieldsets=$("#select").find("fieldset");

                    var flag=false;
                    var errorName="";
                    $.each(fieldsets,function (index,item) {
                        var fieIndex=$(item).attr("data-index");
                        var selectEmp =xmSelect.get('#empXmSelect-'+fieIndex,true);
                        var scheduleName=$("#scheduleName-"+fieIndex).val();
                        var selectEmpValues=selectEmp.getValue();
                        $.each(selectEmpValues,function (vIndex,vItem) {
                            $.each(selectEmpSchedule,function (sIndex,sItem) {
                                if(sItem.value==vItem.value){
                                    flag=true;
                                    errorName=vItem.name;
                                    return false;
                                }
                            })
                            selectEmpSchedule.push({'value':vItem.value,'name':vItem.name,'selectedName':scheduleName});
                        });
                    });
                    if(flag){
                        layer.msg('一个员工只能选择一个班次，请勿重复选择,重复员工姓名：'+errorName, {icon: 2});
                        return false;
                    }
                    $.each(scheduleDataArray,function (index,item) {
                        //console.log(item.date+"___________"+obj_date);
                        if(item!=null){
                            if(item.date==obj_date){
                                scheduleDataArray.splice(index, 1);
                            }
                        }

                    });

                    scheduleDataArray.push({'selectedArray':selectEmpSchedule,'date':obj_date});
                    chose_moban(obj_date);
                    layer.close(index);

                    return false;
                },btn2:function (){
                    chexiao(obj_date,scheduleIds);
                }
            });
        }
        //定义添加/编辑标注方法
        function chose_moban(obj_date){
            $('#test-n2').html('');//重要！由于插件是嵌套指定容器，再次调用前需要清空原日历控件
            //再次调用日历控件，
            console.log(obj_date+"___________________")
            loding_date(obj_date);//重要！，再标注一个日期后会刷新当前日期变为初始值，所以必须调用当前选定日期。
        }
        //撤销选择
        function chexiao(obj_date,scheduleIds){
            $.each(scheduleDataArray,function (index,item) {
                if(item.date==obj_date){
                    scheduleDataArray.splice(index, 1);
                    var date=new Date(obj_date);
                    var choosedate = $('.layui-laydate-content td[lay-ymd='+date.getFullYear()+'-'+(date.getMonth()+1)+'-'+date.getDate()+']');//选中所有显示日期的td
                    choosedate.find("div").remove();
                }
            });
        }

        // 去除0 参数 日期 如 2020-07-08 返回为 2020-7-8
        function dislodgeZero(str) {
            let strArray = str.split("-");
            strArray = strArray.map(function(val) {
                if (val[0] == "0") {
                    return (val = val.slice(1));
                } else {
                    return val;
                }
            });
            return strArray.join("-");
        }



        $("#addScheduleBtn").on('click',function () {
            pop_show("班次",'#(ctxPath)/employeeCheckinRule/scheduleForm?ruleId='+$("#ruleId").val(),800,500);
        });



        scheduleAddTpl=function(data,trIndex) {
            var scheduleCount=$("#scheduleCount").val();
            var idx=Number(scheduleCount)+1;

            var html="";
            $.each(data.checkTimes,function (index,item) {
                var workTime=item.work;
                var offWorkTime=item.offWork;
                var workHour=workTime.substring(0,workTime.indexOf(":"));
                var workMinute=workTime.substring(workTime.indexOf(":")+1);
                var offWorkHour=offWorkTime.substring(0,offWorkTime.indexOf(":"));
                var offWorkMinute=offWorkTime.substring(offWorkTime.indexOf(":")+1);
                html+="<span style='line-height: 30px;'>上班 "+hour[workHour]+":"+workMinute+"——下班 "+hour[offWorkHour]+":"+offWorkMinute+"</span><br>";
                /*html+="<span style='line-height: 30px;'>上班"+item.work+"——下班"+item.offWork+"</span><br>";*/
            });

            if(trIndex!=''){
                html+="<input type='hidden' id='workDays-"+trIndex+"' value='"+JSON.stringify(data.workDays)+"' >";
                var tplData={'idx':trIndex,"name":data.name,'id':data.id,"workTimes":html,"value":JSON.stringify(data)};
            }else{
                html+="<input type='hidden' id='workDays-"+idx+"' value='"+JSON.stringify(data.workDays)+"' >";
                var tplData={'idx':idx,"name":data.name,'id':data.id,"workTimes":html,"value":JSON.stringify(data)};
            }

            laytpl(scheduleTpl.innerHTML).render(tplData, function(html){
                if(trIndex!=''){
                    $("#scheduleTr-"+trIndex).html($(html).html());
                }else{
                    $("#scheduleTbody").append(html);
                    $("#scheduleCount").val(idx);
                }
            });
        }

        #for(ruleSchedule : ruleScheduleList)
        var checkTimes=JSON.parse('#(ruleSchedule.checkinTime)');
        var checkTimeArray=[];
        $.each(checkTimes,function (index,item) {
            checkTimeArray.push({"work":item[0],"offWork":item[1]});
        });
        var data={'name':'#(ruleSchedule.name)','id':'#(ruleSchedule.id)','checkTimes':checkTimeArray};
        scheduleAddTpl(data,'');
        #end

        editSchedule=function(index){
            var url='#(ctxPath)/employeeCheckinRule/scheduleForm?index='+index;
            pop_show("打卡时间",url,800,500);
        }

        delSchedule=function (index,id) {
            var scheduleName=$("#scheduleTr-"+index).find("td:first").text();
            var flag=false;
            $.each(scheduleDataArray,function (index,item) {
                var date=new Date(item.date);
                var selectedArrayEach=item.selectedArray;
                $.each(selectedArrayEach,function (index2,item2) {
                    if(item2.selectedName==scheduleName){
                        flag=true;
                        return false;
                    }
                })
            });
            if(flag){
                //该班次已添加
                layer.msg('该班次已有设置，无法作废', {icon: 2});
                return false;
            }
            if(id==''){
                $("#scheduleTr-"+index).remove();
                if(scheduleDataArray.length>0){
                    $.each(scheduleDataArray,function (index,item) {
                        var date=new Date(item.date);
                        var selectedArrayEach=item.selectedArray;
                        $.each(selectedArrayEach,function (index2,item2) {
                            if(item2.selectedName==scheduleName){
                                selectedArrayEach.splice(index2, 1);
                            }
                        })
                    });
                    getSysSchedule();
                }
            }else{

                layer.confirm("确定要作废吗?",function(i){
                    util.sendAjax ({
                        type: 'get',
                        url: '#(ctxPath)/employeeCheckinRule/delSchedule?id='+id,
                        notice: true,
                        data: {},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                $("#scheduleTr-"+index).remove();
                                if(scheduleDataArray.length>0){
                                    $.each(scheduleDataArray,function (index,item) {
                                        var date=new Date(item.date);
                                        var selectedArrayEach=item.selectedArray;
                                        $.each(selectedArrayEach,function (index2,item2) {
                                            if(item2.selectedName==scheduleName){
                                                selectedArrayEach.splice(index2, 1);
                                            }
                                        })
                                    });
                                    getSysSchedule();
                                }
                            }
                            layer.close(i);
                        },
                        complete : function() {
                        }
                    });
                });

            }
        }

        getSchedulecheckinDate=function (index) {
            return $("#schedule-"+index).val();
        }

        getAllSchedule=function () {
            var trs=$("#scheduleTbody").find("tr");
            var scheduleNames=[];
            $.each(trs,function (index,item) {
                var id=$(item).attr("id");
                var trIndex=id.substr(id.indexOf("scheduleTr-")+"scheduleTr-".length);
                scheduleNames.push({'name':$(item).find("td:first").text(),'index':trIndex});
            });
            return scheduleNames;
        }
    });

</script>
<script type="text/html" id="workDaysTpl">
    <tr id="tr-{{d.idx}}">
        <td>{{d.workDays}}</td>
        <td>{{d.workTimes}}</td>
        <td>
            <button class="layui-btn layui-btn-xs layui-btn-normal" type="button" id="addTimeBtn-1" onclick="editWorkDate({{d.idx}})">编辑</button>
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" id="delTimeBtn-1" onclick="del({{d.idx}})">作废</button>
            <input id="checkinDate-{{d.idx}}" name="checkinDate-{{d.idx}}" value='{{d.value}}' type="hidden">
        </td>
    </tr>
</script>
<script type="text/html" id="scheduleTpl">
    <tr id="scheduleTr-{{d.idx}}">
        <td>{{d.name}}</td>
        <td>{{d.workTimes}}</td>
        <td>
            <button class="layui-btn layui-btn-xs layui-btn-normal" type="button"  onclick="editSchedule({{d.idx}})">编辑</button>
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button"  onclick="delSchedule({{d.idx}},'{{d.id}}')">作废</button>
            <input id="schedule-{{d.idx}}" name="schedule-{{d.idx}}" value='{{d.value}}' type="hidden">
        </td>
    </tr>
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div class="layui-collapse" style="padding: 20px 20px;">

            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>规则名称</label>
                <div class="layui-input-inline">
                    <input id="id" name="id" type="hidden" value="#(checkinRule.id??)">
                    <input id="name" name="name"   lay-verify="required"  placeholder=""  value="#(checkinRule.name??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;" ><font color="red">*</font>规则类型  <i class="layui-icon alone-tips" lay-tips="1、固定时间上下班：所有人按照相同时间打卡; <br>2、按班次上下班：不同人员按照各自排班打卡; <br>3、自由上下班：所有人无时间限制，可随时打卡，只统计旷工"></i></label>
                <div class="layui-input-block">
                    <input type="radio" name="type" lay-filter="type_tadio" value="1" title="固定时间上下班" #if(checkinRule!=null) disabled #end  #if(checkinRule==null || checkinRule.type??=='1') checked #end>
                    <br>
                    <input type="radio" name="type" lay-filter="type_tadio" value="2" title="按班次上下班" #if(checkinRule!=null) disabled #end #if(checkinRule.type??=='2') checked #end>
                    <br>
                    <input type="radio" name="type" lay-filter="type_tadio" value="3" title="自由上下班" #if(checkinRule!=null) disabled #end #if(checkinRule.type??=='3') checked #end>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">打卡部门  <i class="layui-icon alone-tips" lay-tips="选中部门下的子部门也同时生效，如入子部门有设置打卡规则优先使用子部门的打卡规则"></i></label>
                <div class="layui-input-block">
                    <div id="deptSelect" style="">

                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom: 20px;">
                <label class="layui-form-label">打卡人员  <i class="layui-icon alone-tips" lay-tips="打卡员工的优先级大于打卡部门的优先级"></i></label>
                <div class="layui-input-block">
                    <button class="layui-btn layui-btn-sm layui-btn-normal" type="button" id="addEmpBtn" style="margin-top: 3px;">添加人员</button>
                    <div id="empSelect" style="">

                    </div>
                </div>

            </div>
            <div class="layui-form-item" id="checkinDate1" #if(checkinRule==null || checkinRule.type??=='1') #else style="display: none;" #end>
                <label class="layui-form-label"><font color="red">*</font>打卡时间</label>
                <div class="layui-input-block" >
                    <button class="layui-btn layui-btn-sm layui-btn-normal"  type="button" id="addDateBtn" style="margin-left:5px;margin-top: 3px;">添加</button>
                    <table class="layui-table">
                        <colgroup>
                        </colgroup>
                        <input id="workDaysCount"  value="0" type="hidden">
                        <tbody id="tbody">

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-form-item" id="scheduleDiv" #if(checkinRule!=null && checkinRule.type??=='2')  #else style="display: none;" #end>
                <label class="layui-form-label">班次</label>
                <div class="layui-input-block" >
                    <button class="layui-btn layui-btn-sm layui-btn-normal"  type="button" id="addScheduleBtn" style="margin-left:5px;margin-top: 3px;">添加</button>
                    <table class="layui-table">
                        <colgroup>
                        </colgroup>
                        <input id="scheduleCount"  value="0" type="hidden">
                        <tbody id="scheduleTbody">

                        </tbody>
                    </table>
                </div>
            </div>
            <div class="layui-form-item" id="checkinDate2" #if(checkinRule!=null && checkinRule.type??=='2')  #else style="display: none;" #end>
                <label class="layui-form-label">排班</label>
                <input id="empXmSelectValue" type="hidden" value="">
                <div class="layui-input-block" id="schedulingDiv">
                    <div class="layui-inline" id="test-n2" ></div>
                    <div class="text_box" id="select" style="display: none;">
                        <form class="layui-form layui-form-pane" id="scheduleForm" action="">
                            <div id="empXmSelect" style="display: none;" class="xm-select">
                            </div>


                        </form>
                    </div>
                </div>
            </div>


            <div class="layui-form-item" id="checkinDate3"  #if(checkinRule!=null && checkinRule.type??=='3')  #else style="display: none;" #end>
                <label class="layui-form-label"><font color="red">*</font>打卡时间</label>
                <div class="layui-input-block" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('1')!=-1)checked#end  lay-skin="primary" value="1" title="星期一" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('2')!=-1)checked#end  lay-skin="primary" value="2" title="星期二" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('3')!=-1)checked#end  lay-skin="primary" value="3" title="星期三" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('4')!=-1)checked#end  lay-skin="primary" value="4" title="星期四" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('5')!=-1)checked#end  lay-skin="primary" value="5" title="星期五" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('6')!=-1)checked#end  lay-skin="primary" value="6" title="星期六" >
                    <input type="checkbox" name="workDays" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].workDays.indexOf('0')!=-1)checked#end  lay-skin="primary" value="0" title="星期天" >
                </div>
            </div>
            <div class="layui-form-item" id="isManyCheckin"  #if(checkinRule!=null && checkinRule.type??=='3')  #else style="display: none;" #end>
                <label class="layui-form-label">下班  <i class="layui-icon alone-tips" lay-tips="勾选上则下班不需要打卡，只记录上班打卡"></i></label>
                <div class="layui-input-block" >
                    <input type="checkbox" name="noneedOffwork" lay-verify="required" #if(checkinDateList!=null && checkinRule.type??=='3' && checkinDateList[0].noneedOffwork=="1")checked#end  lay-skin="primary" value="1" title="下班不需要打卡" >
                </div>
            </div>
            <!--<div class="layui-form-item">
                <label class="layui-form-label">节假日</label>
                <div class="layui-input-block" >
                    <button class="layui-btn layui-btn-sm layui-btn-normal"  type="button" id="addHolidaysBtn" style="margin-left:5px;margin-top: 3px;">添加</button>

                </div>
            </div>-->

            <div class="layui-form-footer">
                <div class="pull-right">
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
                <div class="pull-right">
                    <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
                </div>
            </div>
        </div>
    </form>
</div>
</body>
#end