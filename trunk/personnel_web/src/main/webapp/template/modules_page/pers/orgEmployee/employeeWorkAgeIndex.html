#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .layui-form-label {
        float: left;
        display: block;
        font-weight: 400;
        line-height: 20px;
        text-align: right;
        padding: 9px 10px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label" style="width: 50px;">姓名：</label>
            <div class="layui-input-inline" style="float: left;width: 130px;" >
                <input class="layui-input" name="name" id="name" >
            </div>

            <div class="layui-input-inline">
                <label class="layui-form-label" >组织架构：</label>
                <div class="layui-inline"  style="width: 250px;">
                    <div id="deptSelect" >

                    </div>
                </div>
            </div>

            <div class="layui-inline">
                <label class="layui-form-label" style="width: 50px;">月份：</label>
                <div class="layui-input-inline" style="float: left;width: 130px;" >
                    <input class="layui-input" name="yearMonth" id="yearMonth" readonly >
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 50px;">类型：</label>
                <div class="layui-input-inline" style="float: left;width: 130px;" >
                    <select id="type" name="type">
                        <option value="1">全部</option>
                        <option value="2">仅变动</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label" style="width: 70px;">工龄范围：</label>
                <div class="layui-input-inline" style="float: left;width: 70px;" >
                    <select id="rangeType" name="rangeType">
                        <option value="">请选择</option>
                        <option value="1">大于</option>
                        <option value="2">等于</option>
                        <option value="3">小于</option>
                    </select>
                </div>
                <div class="layui-input-inline" style="float: left;width: 60px;" >
                    <input class="layui-input" name="rangeValue" id="rangeValue" placeholder="工龄值"  >
                </div>
                <label class="layui-form-label" style="width: 10px;padding: 9px 3px;">年</label>
            </div>
            <div class="layui-input-inline" style="margin-left: 10px;">
                <div class="layui-input-inline" style="width:160px;">
                    <input type="checkbox" id="isFilterBranchOffice" name="isFilterBranchOffice" title="不查询分公司员工" value="1" lay-skin="primary" checked>
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:workAge:exportBtn")
            <button class="layui-btn" id="exportBtn" type="button" style="margin-left: 20px;">导出</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:dispatch:edi2tBtn")
    #[[
    {{#if(d.status=='3'){}}

    <a class="layui-btn layui-btn-xs" lay-event="edit2">编辑</a>

    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:exportBtn")
    #[[
    {{#if(d.taskId!=undefined && d.stepts.length>=1){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="export">导出</a>
    {{#}}}
    ]]#
    #end
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#yearMonth'
            ,trigger: 'click'
            ,type: 'month'
            ,value:new Date()
            ,max:'#(maxMonth)'
            ,btns: [ 'confirm']
        });

        msgLoad({'type':$("#type").val(),"yearMonth":$("#yearMonth").val(),"isFilterBranchOffice":"1"});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }


        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var isFilterBranchOffice="0";
            if("1"==$("input:checkbox[name='isFilterBranchOffice']:checked").val()){
                isFilterBranchOffice="1";
            }
            let rangeValue=$("#rangeValue").val();
            if(rangeValue!=''){
                if(!(/^\d+$/.test(String(rangeValue)))){
                    layer.msg('工龄范围中的工龄值请输入正整数', {icon: 5});
                    return false;
                }
            }

            msgLoad({"fullName":$("#name").val(),"deptId":id,'type':$("#type").val(),"yearMonth":$("#yearMonth").val()
                ,"isFilterBranchOffice":isFilterBranchOffice,"rangeType":$("#rangeType").val(),"rangeValue":$("#rangeValue").val()});
        }


        $("#exportBtn").on('click',function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var isFilterBranchOffice="0";
            if("1"==$("input:checkbox[name='isFilterBranchOffice']:checked").val()){
                isFilterBranchOffice="1";
            }
            let rangeValue=$("#rangeValue").val();
            if(rangeValue!=''){
                if(!(/^\d+$/.test(String(rangeValue)))){
                    layer.msg('工龄范围中的工龄值请输入正整数', {icon: 5});
                    return false;
                }
            }
            layer.load();
            window.location.href='#(ctxPath)/persOrgEmployee/exportEmployeeWorkAgeList' +
                '?deptId='+id+"&fullName="+$("#name").val()+"&type="+$("#type").val()+"&yearMonth="+$("#yearMonth").val()+"&isFilterBranchOffice="+isFilterBranchOffice+
                '&rangeType='+$("#rangeType").val()+'&rangeValue='+$("#rangeValue").val();
            setTimeout(function (){
                layer.closeAll('loading');
            },10000);
        })

        $("#queryBtn").on('click',function () {
            reloadTable();
        });


        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/persOrgEmployee/employeeWorkAgeList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', title: '序号', width: 60, unresize:true}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true,templet:function (d) {
                            if(d.org==undefined || d.org.orgName==undefined){
                                return '';
                            }else{
                                //return '<span title="'+d.org.orgName+'" >'+d.org.orgName+'</span>';
                                return d.org.orgName;
                            }
                        }}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true,templet:function (d) {
                            if(d.org==undefined || d.org.positionName==undefined){
                                return '';
                            }else{
                                return d.org.positionName;
                            }
                        }}
                    ,{field:'workNum',title:'工号', align: 'center', unresize: true},
                    {field:'fullName',title:'姓名', align: 'center', unresize: true},
                    {field:'sex',title:'性别', align: 'center', unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}

                    ,{field:'startTime', title: '入职时间',align: 'center', unresize: true,templet:function (d){
                            return dateFormat(d.entryTime,'yyyy-MM-dd');
                        }}

                    ,{field:'workAge', title: '工龄', align: 'center', unresize: true}
                    ,{field:'workAgeMoney', title: '工龄工资', align: 'center', unresize: true,templet:function (d){
                            if(d.org!=undefined && d.org.orgType=='branche_office'){
                                return '- -';
                            }else{
                                return d.workAge*50;
                            }
                        }}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/dispatchForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }else if(obj.event==='edit2'){
                    var url='#(ctxPath)/pers/approval/dispatchEditForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }else if(obj.event=='export'){
                    window.location.href='#(ctxPath)/pers/approval/exportChangeApply2?id='+obj.data.id;
                }
            })


        };


    });
</script>
#end