#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-table td, .layui-table th {
        padding: 9px 5px;
        text-align: center;
    }
    .laydate-time-list{padding-bottom:0;overflow:hidden}
    .laydate-time-list>li{width:50%!important;}
    .laydate-time-list>li:last-child { display: none;}
    .laydate-set-ym{
        font-size: 20px;
    }
</style>
#end

#define js()
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table','form', 'vip_table','laydate','laytpl'], function() {
        var laydate=layui.laydate,form=layui.form,laytpl=layui.laytpl,$=layui.$;




        //时间选择器
        laydate.render({
            elem: '#work-1'
            ,type: 'time'
            ,format: 'HH:mm'
            ,trigger:'click'
            ,value:'09:00'
        });

        //时间选择器
        laydate.render({
            elem: '#offWork-1'
            ,type: 'time'
            ,format: 'HH:mm'
            ,trigger:'click'
            ,value:'18:00'
        });

        addTpl=function(index,workTime,offWorkTime) {
            $("#addTimeBtn-"+index).hide();
            var timeCount=$("#timeCount").val();
            var idx=Number(timeCount)+1;
            var data={'idx':idx};
            laytpl(deptTpl.innerHTML).render(data, function(html){
                $("#tbody").append(html);
                form.render();
                if(workTime!=undefined && offWorkTime!=undefined){
                    var workHour=workTime.substring(0,workTime.indexOf(":"));
                    var workMinute=workTime.substring(workTime.indexOf(":")+1);
                    var offWorkHour=offWorkTime.substring(0,offWorkTime.indexOf(":"));
                    var offWorkMinute=offWorkTime.substring(offWorkTime.indexOf(":")+1);
                    $("#workHour-"+idx).find("option[value='"+workHour+"']").prop("selected",true);
                    $("#workMinute-"+idx).find("option[value='"+workMinute+"']").prop("selected",true);
                    $("#offWorkHour-"+idx).find("option[value='"+offWorkHour+"']").prop("selected",true);
                    $("#offWorkMinute-"+idx).find("option[value='"+offWorkMinute+"']").prop("selected",true);
                    form.render('select');
                }
            });
            $("#timeCount").val(idx);
            showHideBtn();
        }

        layDateRender=function(index,workTime,offWorkTime){
            laydate.render({
                elem: '#work-'+index
                ,type: 'time'
                ,format: 'HH:mm'
                ,trigger:'click'
                ,value:workTime
            });

            //时间选择器
            laydate.render({
                elem: '#offWork-'+index
                ,type: 'time'
                ,format: 'HH:mm'
                ,trigger:'click'
                ,value:offWorkTime
            });
        }

        del=function(index){
            $("#tr-"+index).remove();
            showHideBtn();
        }

        showHideBtn=function () {
            var trs=$("#tbody").find("tr");
            $.each(trs,function (index,item) {
                var id=$(item).attr("id");
                var trIndex=id.substr(id.indexOf("tr-")+3);
                if(index==0) {
                    $("#delTimeBtn-" + trIndex).show();
                }
                if(index==trs.length-1){
                    $("#addTimeBtn-"+trIndex).show();
                    if(index==0){
                        $("#delTimeBtn-"+trIndex).hide();
                    }

                }

            })
        }

        getCheckTime=function () {
            var trs=$("#tbody").find("tr");
            var data=[];
            $.each(trs,function (index,item) {
                var id=$(item).attr("id");
                var trIndex=id.substr(id.indexOf("tr-")+3);
                data.push({"work":$("#workHour-"+trIndex).val()+":"+$("#workMinute-"+trIndex).val(),"offWork":$("#offWorkHour-"+trIndex).val()+":"+$("#offWorkMinute-"+trIndex).val()});
            });
            return data;
        }

        function checkWorkTime(workTime,offWorkTime){
            var workTimeArr = workTime.split(':');
            var offWorkTimeArr = offWorkTime.split(':');
            if(workTimeArr[0]>offWorkTimeArr[0]){
                return true;
            }
            if(workTimeArr[0]==offWorkTimeArr[0]){
                if(workTimeArr[1]>=offWorkTimeArr[1]){
                    return true;
                }
            }
            return false;
        }

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var name=$.trim($("#name").val());
            name=name.replace(/\s/g,"");
            var names=parent.getAllSchedule();
            var flag=false;
            $.each(names,function (index,item) {
                if(item.name==name && '#(index??)'!=item.index){
                    flag=true;
                    return false;
                }
            });
            if(flag){
                layer.msg('该名字已存在，请勿重复添加', {icon: 2});
                return false;
            }
            var checkTimes=getCheckTime();

            var suc=false;
            $.each(checkTimes,function (eIndex,eItem) {
                var flag=false;
                flag=checkWorkTime(eItem.work,eItem.offWork);
                if(flag){
                    suc=true;
                    return false;
                }
                if(eIndex>0) {
                    flag=checkWorkTime(checkTimes[eIndex-1].offWork,eItem.work);
                    if(flag){
                        suc=true;
                        return false;
                    }
                }
            });
            if(suc){
                layer.msg('打卡时间存在冲突，请修改！', { icon: 2 });
                return false;
            }

            var data={"checkTimes":checkTimes,'name':name,'id':$("#id").val()};

            parent.scheduleAddTpl(data,'#(index??)')
            pop_close();
            return false;
        });

        showData=function (parentIndex) {
            var data=JSON.parse(parent.getSchedulecheckinDate(parentIndex));
            $("#name").val(data.name);
            $("#id").val(data.id);
            $("#tbody").empty();
            $("#timeCount").val(0);
            $.each(data.checkTimes,function (index,item) {
                addTpl((index+1),item.work,item.offWork);
            });
            form.render();
        }

        #if(index!=null)
        showData(#(index));
        #end


        //Tips
        $('*[lay-tips]').on('mouseenter', function(){
            var content = $(this).attr('lay-tips');

            this.index = layer.tips('<div style="padding: 10px; font-size: 14px; color: #eee;">'+ content + '</div>', this, {
                time: -1
                ,maxWidth: 280
                ,tips: [3, '#3A3D49']
            });
        }).on('mouseleave', function(){
            layer.close(this.index);
        });

    });
</script>
<script type="text/html" id="deptTpl">
    <tr id="tr-{{d.idx}}">
        <td>上班</td>
        <td>
            <div class="layui-input-inline">
                <!--<input id="work-1" name="work-1" readonly  lay-verify="required"  placeholder=""   autocomplete="off" class="layui-input">-->
                <!--<input id="work-1" name="work-1" readonly  lay-verify="required"  placeholder=""   autocomplete="off" class="layui-input">-->
                <div class="layui-input-inline" style="width: 80px;    margin-right: 0px;">
                    <select lay-verify="required" id="workHour-{{d.idx}}" name="workHour-{{d.idx}}" >
                        <option data-index="1" value="00">00</option>
                        <option data-index="2" value="01">01</option>
                        <option data-index="3" value="02">02</option>
                        <option data-index="4" value="03">03</option>
                        <option data-index="5" value="04">04</option>
                        <option data-index="6" value="05">05</option>
                        <option data-index="7" value="06">06</option>
                        <option data-index="8" value="07">07</option>
                        <option data-index="9" value="08">08</option>
                        <option data-index="10" value="09">09</option>
                        <option data-index="11" value="10">10</option>
                        <option data-index="12" value="11">11</option>
                        <option data-index="13" value="12">12</option>
                        <option data-index="14" value="13">13</option>
                        <option data-index="15" value="14">14</option>
                        <option data-index="16" value="15">15</option>
                        <option data-index="17" value="16">16</option>
                        <option data-index="18" value="17">17</option>
                        <option data-index="19" value="18">18</option>
                        <option data-index="20" value="19">19</option>
                        <option data-index="21" value="20">20</option>
                        <option data-index="22" value="21">21</option>
                        <option data-index="23" value="22">22</option>
                        <option data-index="24" value="23">23</option>
                    </select>
                </div>
                <label class="layui-form-label" style="border-width: 0px;width: 10px;padding: 8px 0px;">：</label>
                <div class="layui-input-inline" style="width: 80px;">
                    <select lay-verify="required" id="workMinute-{{d.idx}}" name="workMinute-{{d.idx}}" >
                        <option data-index="1" value="00">00</option>
                        <option data-index="2" value="01">01</option>
                        <option data-index="3" value="02">02</option>
                        <option data-index="4" value="03">03</option>
                        <option data-index="5" value="04">04</option>
                        <option data-index="6" value="05">05</option>
                        <option data-index="7" value="06">06</option>
                        <option data-index="8" value="07">07</option>
                        <option data-index="9" value="08">08</option>
                        <option data-index="10" value="09">09</option>
                        <option data-index="11" value="10">10</option>
                        <option data-index="12" value="11">11</option>
                        <option data-index="13" value="12">12</option>
                        <option data-index="14" value="13">13</option>
                        <option data-index="15" value="14">14</option>
                        <option data-index="16" value="15">15</option>
                        <option data-index="17" value="16">16</option>
                        <option data-index="18" value="17">17</option>
                        <option data-index="19" value="18">18</option>
                        <option data-index="20" value="19">19</option>
                        <option data-index="21" value="20">20</option>
                        <option data-index="22" value="21">21</option>
                        <option data-index="23" value="22">22</option>
                        <option data-index="23" value="23">23</option>
                        <option data-index="24" value="24">24</option>
                        <option data-index="24" value="25">25</option>
                        <option data-index="24" value="26">26</option>
                        <option data-index="24" value="27">27</option>
                        <option data-index="24" value="28">28</option>
                        <option data-index="24" value="29">29</option>
                        <option data-index="24" value="30">30</option>
                        <option data-index="24" value="31">31</option>
                        <option data-index="24" value="32">32</option>
                        <option data-index="24" value="33">33</option>
                        <option data-index="24" value="34">34</option>
                        <option data-index="24" value="35">35</option>
                        <option data-index="24" value="36">36</option>
                        <option data-index="24" value="37">37</option>
                        <option data-index="24" value="38">38</option>
                        <option data-index="24" value="39">39</option>
                        <option data-index="24" value="40">40</option>
                        <option data-index="24" value="41">41</option>
                        <option data-index="24" value="42">42</option>
                        <option data-index="24" value="43">43</option>
                        <option data-index="24" value="44">44</option>
                        <option data-index="24" value="45">45</option>
                        <option data-index="24" value="46">46</option>
                        <option data-index="24" value="47">47</option>
                        <option data-index="24" value="48">48</option>
                        <option data-index="24" value="49">49</option>
                        <option data-index="24" value="50">50</option>
                        <option data-index="24" value="51">51</option>
                        <option data-index="24" value="52">52</option>
                        <option data-index="24" value="53">53</option>
                        <option data-index="24" value="54">54</option>
                        <option data-index="24" value="55">55</option>
                        <option data-index="24" value="56">56</option>
                        <option data-index="24" value="57">57</option>
                        <option data-index="24" value="58">58</option>
                        <option data-index="24" value="59">59</option>
                    </select>
                </div>
            </div>
        </td>
        <td>下班</td>
        <td>
            <div class="layui-input-inline">
                <div class="layui-input-inline" style="width: 90px;    margin-right: 0px;">
                    <select lay-verify="required" id="offWorkHour-{{d.idx}}" name="offWorkHour-{{d.idx}}" >
                        <option data-index="1" value="00">00</option>
                        <option data-index="2" value="01">01</option>
                        <option data-index="3" value="02">02</option>
                        <option data-index="4" value="03">03</option>
                        <option data-index="5" value="04">04</option>
                        <option data-index="6" value="05">05</option>
                        <option data-index="7" value="06">06</option>
                        <option data-index="8" value="07">07</option>
                        <option data-index="9" value="08">08</option>
                        <option data-index="10" value="09">09</option>
                        <option data-index="11" value="10">10</option>
                        <option data-index="12" value="11">11</option>
                        <option data-index="13" value="12">12</option>
                        <option data-index="14" value="13">13</option>
                        <option data-index="15" value="14">14</option>
                        <option data-index="16" value="15">15</option>
                        <option data-index="17" value="16">16</option>
                        <option data-index="18" value="17">17</option>
                        <option data-index="19" value="18">18</option>
                        <option data-index="20" value="19">19</option>
                        <option data-index="21" value="20">20</option>
                        <option data-index="22" value="21">21</option>
                        <option data-index="23" value="22">22</option>
                        <option data-index="24" value="23">23</option>
                        <option data-index="24" value="24">次日00</option>
                        <option data-index="24" value="25">次日01</option>
                        <option data-index="24" value="26">次日02</option>
                        <option data-index="24" value="27">次日03</option>
                        <option data-index="24" value="28">次日04</option>
                        <option data-index="24" value="29">次日05</option>
                        <option data-index="24" value="30">次日06</option>
                        <option data-index="24" value="31">次日07</option>
                        <option data-index="24" value="32">次日08</option>
                        <option data-index="24" value="33">次日09</option>
                        <option data-index="24" value="34">次日10</option>
                        <option data-index="24" value="35">次日11</option>
                        <option data-index="24" value="35">次日12</option>
                    </select>
                </div>
                <label class="layui-form-label" style="border-width: 0px;width: 10px;padding: 8px 0px;">：</label>
                <div class="layui-input-inline" style="width: 80px;">
                    <select lay-verify="required" id="offWorkMinute-{{d.idx}}" name="offWorkMinute-{{d.idx}}" >
                        <option data-index="1" value="00">00</option>
                        <option data-index="2" value="01">01</option>
                        <option data-index="3" value="02">02</option>
                        <option data-index="4" value="03">03</option>
                        <option data-index="5" value="04">04</option>
                        <option data-index="6" value="05">05</option>
                        <option data-index="7" value="06">06</option>
                        <option data-index="8" value="07">07</option>
                        <option data-index="9" value="08">08</option>
                        <option data-index="10" value="09">09</option>
                        <option data-index="11" value="10">10</option>
                        <option data-index="12" value="11">11</option>
                        <option data-index="13" value="12">12</option>
                        <option data-index="14" value="13">13</option>
                        <option data-index="15" value="14">14</option>
                        <option data-index="16" value="15">15</option>
                        <option data-index="17" value="16">16</option>
                        <option data-index="18" value="17">17</option>
                        <option data-index="19" value="18">18</option>
                        <option data-index="20" value="19">19</option>
                        <option data-index="21" value="20">20</option>
                        <option data-index="22" value="21">21</option>
                        <option data-index="23" value="22">22</option>
                        <option data-index="23" value="23">23</option>
                        <option data-index="24" value="24">24</option>
                        <option data-index="24" value="25">25</option>
                        <option data-index="24" value="26">26</option>
                        <option data-index="24" value="27">27</option>
                        <option data-index="24" value="28">28</option>
                        <option data-index="24" value="29">29</option>
                        <option data-index="24" value="30">30</option>
                        <option data-index="24" value="31">31</option>
                        <option data-index="24" value="32">32</option>
                        <option data-index="24" value="33">33</option>
                        <option data-index="24" value="34">34</option>
                        <option data-index="24" value="35">35</option>
                        <option data-index="24" value="36">36</option>
                        <option data-index="24" value="37">37</option>
                        <option data-index="24" value="38">38</option>
                        <option data-index="24" value="39">39</option>
                        <option data-index="24" value="40">40</option>
                        <option data-index="24" value="41">41</option>
                        <option data-index="24" value="42">42</option>
                        <option data-index="24" value="43">43</option>
                        <option data-index="24" value="44">44</option>
                        <option data-index="24" value="45">45</option>
                        <option data-index="24" value="46">46</option>
                        <option data-index="24" value="47">47</option>
                        <option data-index="24" value="48">48</option>
                        <option data-index="24" value="49">49</option>
                        <option data-index="24" value="50">50</option>
                        <option data-index="24" value="51">51</option>
                        <option data-index="24" value="52">52</option>
                        <option data-index="24" value="53">53</option>
                        <option data-index="24" value="54">54</option>
                        <option data-index="24" value="55">55</option>
                        <option data-index="24" value="56">56</option>
                        <option data-index="24" value="57">57</option>
                        <option data-index="24" value="58">58</option>
                        <option data-index="24" value="59">59</option>
                    </select>
                </div>
            </div>
        </td>
        <td align="left" style="text-align: left;">
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" id="delTimeBtn-{{d.idx}}" onclick="del({{d.idx}})">作废</button>
            <button class="layui-btn layui-btn-xs layui-btn-normal" type="button" id="addTimeBtn-{{d.idx}}" onclick="addTpl({{d.idx}},'','')">添加</button>
        </td>
    </tr>
</script>
#end

#define content()
<body class="v-theme" style="">
<form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;margin-left: 20px;">
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">*</font>班次名称</label>
        <div class="layui-input-block" >
            <input id="name" name="name"   lay-verify="required"  placeholder='例如”早班“'  value="#(ruleSchedule.name??)" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>打卡时间 <i class="layui-icon alone-tips" lay-tips="1、下班时间不能小于等于上班时间；<br>2、当前组的上班时间不能小于等于上一组的下班时间。"></i></label>
        <div class="layui-input-block" >
            <table class="layui-table" style="margin-left: 1px;width: 650px;" lay-skin="nob">
                <colgroup>
                    <col width="70">
                    <col width="100">
                    <col width="70">
                    <col width="100">
                    <col width="100">
                </colgroup>
                <tbody id="tbody">
                    <tr id="tr-1">
                        <td>上班</td>
                        <td>
                            <div class="layui-input-inline">
                                <!--<input id="work-1" name="work-1" readonly  lay-verify="required"  placeholder=""   autocomplete="off" class="layui-input">-->
                                <!--<input id="work-1" name="work-1" readonly  lay-verify="required"  placeholder=""   autocomplete="off" class="layui-input">-->
                                <div class="layui-input-inline" style="width: 80px;    margin-right: 0px;">
                                    <select lay-verify="required" id="workHour-1" name="workHour-1" >
                                        <option data-index="1" value="00">00</option>
                                        <option data-index="2" value="01">01</option>
                                        <option data-index="3" value="02">02</option>
                                        <option data-index="4" value="03">03</option>
                                        <option data-index="5" value="04">04</option>
                                        <option data-index="6" value="05">05</option>
                                        <option data-index="7" value="06">06</option>
                                        <option data-index="8" value="07">07</option>
                                        <option data-index="9" value="08">08</option>
                                        <option data-index="10" value="09">09</option>
                                        <option data-index="11" value="10">10</option>
                                        <option data-index="12" value="11">11</option>
                                        <option data-index="13" value="12">12</option>
                                        <option data-index="14" value="13">13</option>
                                        <option data-index="15" value="14">14</option>
                                        <option data-index="16" value="15">15</option>
                                        <option data-index="17" value="16">16</option>
                                        <option data-index="18" value="17">17</option>
                                        <option data-index="19" value="18">18</option>
                                        <option data-index="20" value="19">19</option>
                                        <option data-index="21" value="20">20</option>
                                        <option data-index="22" value="21">21</option>
                                        <option data-index="23" value="22">22</option>
                                        <option data-index="24" value="23">23</option>
                                    </select>
                                </div>
                                <label class="layui-form-label" style="border-width: 0px;width: 10px;padding: 8px 0px;">：</label>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <select lay-verify="required" id="workMinute-1" name="workMinute-1" >
                                        <option data-index="1" value="00">00</option>
                                        <option data-index="2" value="01">01</option>
                                        <option data-index="3" value="02">02</option>
                                        <option data-index="4" value="03">03</option>
                                        <option data-index="5" value="04">04</option>
                                        <option data-index="6" value="05">05</option>
                                        <option data-index="7" value="06">06</option>
                                        <option data-index="8" value="07">07</option>
                                        <option data-index="9" value="08">08</option>
                                        <option data-index="10" value="09">09</option>
                                        <option data-index="11" value="10">10</option>
                                        <option data-index="12" value="11">11</option>
                                        <option data-index="13" value="12">12</option>
                                        <option data-index="14" value="13">13</option>
                                        <option data-index="15" value="14">14</option>
                                        <option data-index="16" value="15">15</option>
                                        <option data-index="17" value="16">16</option>
                                        <option data-index="18" value="17">17</option>
                                        <option data-index="19" value="18">18</option>
                                        <option data-index="20" value="19">19</option>
                                        <option data-index="21" value="20">20</option>
                                        <option data-index="22" value="21">21</option>
                                        <option data-index="23" value="22">22</option>
                                        <option data-index="23" value="23">23</option>
                                        <option data-index="24" value="24">24</option>
                                        <option data-index="24" value="25">25</option>
                                        <option data-index="24" value="26">26</option>
                                        <option data-index="24" value="27">27</option>
                                        <option data-index="24" value="28">28</option>
                                        <option data-index="24" value="29">29</option>
                                        <option data-index="24" value="30">30</option>
                                        <option data-index="24" value="31">31</option>
                                        <option data-index="24" value="32">32</option>
                                        <option data-index="24" value="33">33</option>
                                        <option data-index="24" value="34">34</option>
                                        <option data-index="24" value="35">35</option>
                                        <option data-index="24" value="36">36</option>
                                        <option data-index="24" value="37">37</option>
                                        <option data-index="24" value="38">38</option>
                                        <option data-index="24" value="39">39</option>
                                        <option data-index="24" value="40">40</option>
                                        <option data-index="24" value="41">41</option>
                                        <option data-index="24" value="42">42</option>
                                        <option data-index="24" value="43">43</option>
                                        <option data-index="24" value="44">44</option>
                                        <option data-index="24" value="45">45</option>
                                        <option data-index="24" value="46">46</option>
                                        <option data-index="24" value="47">47</option>
                                        <option data-index="24" value="48">48</option>
                                        <option data-index="24" value="49">49</option>
                                        <option data-index="24" value="50">50</option>
                                        <option data-index="24" value="51">51</option>
                                        <option data-index="24" value="52">52</option>
                                        <option data-index="24" value="53">53</option>
                                        <option data-index="24" value="54">54</option>
                                        <option data-index="24" value="55">55</option>
                                        <option data-index="24" value="56">56</option>
                                        <option data-index="24" value="57">57</option>
                                        <option data-index="24" value="58">58</option>
                                        <option data-index="24" value="59">59</option>
                                    </select>
                                </div>
                            </div>
                        </td>
                        <td>下班</td>
                        <td>
                            <div class="layui-input-inline">
                                <div class="layui-input-inline" style="width: 90px;    margin-right: 0px;">
                                    <select lay-verify="required" id="offWorkHour-1" name="offWorkHour-1" >
                                        <option data-index="1" value="00">00</option>
                                        <option data-index="2" value="01">01</option>
                                        <option data-index="3" value="02">02</option>
                                        <option data-index="4" value="03">03</option>
                                        <option data-index="5" value="04">04</option>
                                        <option data-index="6" value="05">05</option>
                                        <option data-index="7" value="06">06</option>
                                        <option data-index="8" value="07">07</option>
                                        <option data-index="9" value="08">08</option>
                                        <option data-index="10" value="09">09</option>
                                        <option data-index="11" value="10">10</option>
                                        <option data-index="12" value="11">11</option>
                                        <option data-index="13" value="12">12</option>
                                        <option data-index="14" value="13">13</option>
                                        <option data-index="15" value="14">14</option>
                                        <option data-index="16" value="15">15</option>
                                        <option data-index="17" value="16">16</option>
                                        <option data-index="18" value="17">17</option>
                                        <option data-index="19" value="18">18</option>
                                        <option data-index="20" value="19">19</option>
                                        <option data-index="21" value="20">20</option>
                                        <option data-index="22" value="21">21</option>
                                        <option data-index="23" value="22">22</option>
                                        <option data-index="24" value="23">23</option>
                                        <option data-index="24" value="24">次日00</option>
                                        <option data-index="24" value="25">次日01</option>
                                        <option data-index="24" value="26">次日02</option>
                                        <option data-index="24" value="27">次日03</option>
                                        <option data-index="24" value="28">次日04</option>
                                        <option data-index="24" value="29">次日05</option>
                                        <option data-index="24" value="30">次日06</option>
                                        <option data-index="24" value="31">次日07</option>
                                        <option data-index="24" value="32">次日08</option>
                                        <option data-index="24" value="33">次日09</option>
                                        <option data-index="24" value="34">次日10</option>
                                        <option data-index="24" value="35">次日11</option>
                                        <option data-index="24" value="35">次日12</option>
                                    </select>
                                </div>
                                <label class="layui-form-label" style="border-width: 0px;width: 10px;padding: 8px 0px;">：</label>
                                <div class="layui-input-inline" style="width: 80px;">
                                    <select lay-verify="required" id="offWorkMinute-1" name="offWorkMinute-1" >
                                        <option data-index="1" value="00">00</option>
                                        <option data-index="2" value="01">01</option>
                                        <option data-index="3" value="02">02</option>
                                        <option data-index="4" value="03">03</option>
                                        <option data-index="5" value="04">04</option>
                                        <option data-index="6" value="05">05</option>
                                        <option data-index="7" value="06">06</option>
                                        <option data-index="8" value="07">07</option>
                                        <option data-index="9" value="08">08</option>
                                        <option data-index="10" value="09">09</option>
                                        <option data-index="11" value="10">10</option>
                                        <option data-index="12" value="11">11</option>
                                        <option data-index="13" value="12">12</option>
                                        <option data-index="14" value="13">13</option>
                                        <option data-index="15" value="14">14</option>
                                        <option data-index="16" value="15">15</option>
                                        <option data-index="17" value="16">16</option>
                                        <option data-index="18" value="17">17</option>
                                        <option data-index="19" value="18">18</option>
                                        <option data-index="20" value="19">19</option>
                                        <option data-index="21" value="20">20</option>
                                        <option data-index="22" value="21">21</option>
                                        <option data-index="23" value="22">22</option>
                                        <option data-index="23" value="23">23</option>
                                        <option data-index="24" value="24">24</option>
                                        <option data-index="24" value="25">25</option>
                                        <option data-index="24" value="26">26</option>
                                        <option data-index="24" value="27">27</option>
                                        <option data-index="24" value="28">28</option>
                                        <option data-index="24" value="29">29</option>
                                        <option data-index="24" value="30">30</option>
                                        <option data-index="24" value="31">31</option>
                                        <option data-index="24" value="32">32</option>
                                        <option data-index="24" value="33">33</option>
                                        <option data-index="24" value="34">34</option>
                                        <option data-index="24" value="35">35</option>
                                        <option data-index="24" value="36">36</option>
                                        <option data-index="24" value="37">37</option>
                                        <option data-index="24" value="38">38</option>
                                        <option data-index="24" value="39">39</option>
                                        <option data-index="24" value="40">40</option>
                                        <option data-index="24" value="41">41</option>
                                        <option data-index="24" value="42">42</option>
                                        <option data-index="24" value="43">43</option>
                                        <option data-index="24" value="44">44</option>
                                        <option data-index="24" value="45">45</option>
                                        <option data-index="24" value="46">46</option>
                                        <option data-index="24" value="47">47</option>
                                        <option data-index="24" value="48">48</option>
                                        <option data-index="24" value="49">49</option>
                                        <option data-index="24" value="50">50</option>
                                        <option data-index="24" value="51">51</option>
                                        <option data-index="24" value="52">52</option>
                                        <option data-index="24" value="53">53</option>
                                        <option data-index="24" value="54">54</option>
                                        <option data-index="24" value="55">55</option>
                                        <option data-index="24" value="56">56</option>
                                        <option data-index="24" value="57">57</option>
                                        <option data-index="24" value="58">58</option>
                                        <option data-index="24" value="59">59</option>
                                    </select>
                                </div>
                            </div>
                        </td>
                        <td align="left" style="text-align: left;">
                            <button class="layui-btn layui-btn-xs layui-btn-danger" style="display: none;" type="button" id="delTimeBtn-1" onclick="del(1)">作废</button>
                            <button class="layui-btn layui-btn-xs layui-btn-normal" type="button" id="addTimeBtn-1" onclick="addTpl(1)">添加</button>
                        </td>
                    </tr>

                </tbody>
                <input id="timeCount" value="1" type="hidden">
                <input id="id" name="id" value="" type="hidden">
            </table>
            <!--<button class="layui-btn layui-btn-sm layui-btn-normal" type="button" id="addEmpBtn" style="margin-top: 3px;">添加人员</button>-->
        </div>
    </div>
    <!--<div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 1px;">是否多次打卡 <i class="layui-icon alone-tips" lay-tips="1、勾选上：则每一组的上班和下班时间都需要打卡（打卡次数=打卡时间组数*2）；<br>2、没有勾选上：只记录最早上班时间和最晚下班时间（打卡次数=2）"></i></label>
        <div class="layui-input-inline" style="width: 300px;">
            <input type="checkbox" name="isManyCheckin" value="1" lay-skin="primary" title="是" >
        </div>
    </div>-->


    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
        <div class="pull-right">
            <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
        </div>
    </div>

</form>

</body>
#end