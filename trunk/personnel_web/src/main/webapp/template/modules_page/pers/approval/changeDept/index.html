#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>
            <label class="layui-form-label">调动时间</label>
            <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
            </div>
            <!--<label class="layui-form-label">状态</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="state" name="state" lay-filter="state">
                    <option value="5">全部</option>
                    <option value="0">待处理</option>
                    <option value="1">提交</option>
                    <option value="2">批准</option>
                    <option value="3">完成</option>
                    <option value="4">失败</option>
                </select>
            </div>-->
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:changeDept:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:changeDept:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:employeeQuit:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

</script>
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }
        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        reloadTable=function () {
            msgLoad({"name":$("#name").val(),'date':$("#date").val()});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/changeDeptForm';
            pop_show("添加调岗申请",url,1300,700);
        });

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/findEmployeeChangeDeptPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'workNum',title:'工号',width: 80, align: 'center', unresize: true},
                    {field:'fullName',title:'姓名',width: 90, align: 'center', unresize: true},
                    {field:'sex',title:'性别', align: 'center',width: 80, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'oldDeptName', title: '原部门', align: 'center', unresize: true}
                    ,{field:'oldPosition', title: '原职位', align: 'center',width:120, unresize: true}
                    ,{field:'deptName', title: '新部门', align: 'center', unresize: true}
                    ,{field:'position', title: '新职位', align: 'center',width:120, unresize: true}
                    ,{field:'changeType', title: '调岗类型',align: 'center', unresize: true,width:100,templet:function (d){
                            if('personal'==d.changeType){
                                return '个人申请';
                            }else if('internal'==d.changeType){
                                return '内部调动';
                            }else{
                                return '- -';
                            }
                        }
                    }
                    ,{field:'changeDate', title: '生效日期',align: 'center', unresize: true,width:100,templet:function (d){
                            return dateFormat(d.changeDate,'yyyy-MM-dd');
                        }
                    }
                    ,{field:'remark', title: '原因', align: 'center', unresize: true}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center',width:120, unresize: true,templet:function (d) {
                            if(d.taskId==undefined){
                                return '<span class="layui-badge">未提交</span>';
                            }else{
                                if(d.taskState=='0'){
                                    return '<span class="layui-badge layui-bg-orange">处理中</span>';
                                }else if(d.taskState=='1'){
                                    return '<span class="layui-badge layui-bg-green">结束</span>';
                                }else{
                                    return '- -';
                                }
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 100, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/changeDeptForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }
            })


        };


    });
</script>
#end