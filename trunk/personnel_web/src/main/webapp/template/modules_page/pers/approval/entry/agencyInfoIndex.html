#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <div class="layui-input-inline">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-inline" style="float: left;" >
                    <input type="text" name="employeeName" id="employeeName" value="" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-input-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline" style="float: left;" >
                    <select id="status" name="status" >
                        <option value="">全部</option>
                        <option value="0">未提交流程</option>
                        <option value="1">流程中</option>
                        <option value="2">流程已通过</option>
                        <option value="3">流程已中止</option>
                    </select>
                </div>
            </div>
            <button class="layui-btn" id="query" type="button" >查询</button>


        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    #shiroHasPermission("emp:agencyInfoForm:addBtn")
    #[[

    {{#if(d.status=='0' || d.status=='3'){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">提交登记流程</a>
    {{#}}}
    ]]#
    #end



</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });



        msgLoad({'employeeType':'1'});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/supplierEntryInfoForm';
            pop_show("添加信息",url,'100%','100%');
        });

        $("#reportForm").on('click',function (){
            var url='#(ctxPath)/pers/approval/entryInfoReportForm';
            pop_show("报表",url,'100%','100%');
        })




        $("#query").on('click',function (obj) {
            reloadTable();
        })

        reloadTable=function () {
            msgLoad({"employeeName":$("#employeeName").val(),'delFlag':$("#status").val(),'employeeType':'1'});
        }

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/agencyInfoPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'employee_name',title:'姓名', align: 'center', unresize: true}
                    ,{field:'gender', title: '性别',width: 120, align: 'center', unresize: true,templet:function (d) {
                            if(d.gender==='man'){
                                return '男';
                            }else if(d.gender==='woman'){
                                return '女';
                            }
                        }}
                    ,{field:'id_card_type',title:'证件类型', align: 'center', unresize: true}
                    ,{field:'idcard',title:'证件号', align: 'center', unresize: true}
                    ,{field:'birthday', title: '出生日期',align: 'center', unresize: true,width:180,templet:"<div>{{dateFormat(d.birthday,'yyyy-MM-dd')}}</div>"}
                    ,{field:'phone',title:'手机号码', align: 'center', unresize: true}
                    ,{field:'idcard_front_path',title:'身份证正面', align: 'center', unresize: true,templet:function (d) {
                            return '<button type="button" class="layui-btn layui-btn-xs" onclick=\'showPhoto("'+d.idcard_front_path+'","'+d.id+'","身份证正面")\'>查看</button>'
                    }}
                    ,{field:'idcard_contrary_path',title:'身份证反面', align: 'center', unresize: true,templet:function (d) {
                            return '<button type="button" class="layui-btn layui-btn-xs" onclick=\'showPhoto("'+d.idcard_contrary_path+'","'+d.id+'","身份证反面")\'>查看</button>'
                        }}
                    ,{field:'taskStatus', title: '流程状态',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(d.status=='0'){
                                return '未提交流程';
                            }else if(d.status=='1'){
                                return '流程中'
                            }else if(d.status=='2'){
                                return '流程已通过'
                            }else if(d.status=='3'){
                                return '流程已中止';
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });

            showPhoto=function(url,id,title){
                console.log(111);
                /*layer.photos({
                    photos: '#layer-photos-demo' //格式见API文档手册页
                    ,anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机
                });*/

                showPhotos([{"alt":title,"pid":id,"src":url,"thumb":''}])
                return false;
            }

            showPhotos=function(images) {
                layer.photos({
                    /*area: '400px',*/
                    shade: [0.85, '#000'],
                    anim: 0,
                    photos: {
                        "title": "附件预览",
                        "id": 'showImages',
                        "data": images
                    }
                    ,tab:function () {
                        num=0;
                        $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                            '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                            '\t</div>');
                        $(document).on("click", "#xuanzhuan", function(e) {
                            num = (num+90)%360;
                            $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                            $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                        });

                        $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                            var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                                (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                            var imagep = $(".layui-layer-phimg").parent().parent();
                            var image = $(".layui-layer-phimg").parent();
                            var h = image.height();
                            var w = image.width();
                            if (delta > 0) {
                                if (h < (window.innerHeight)) {
                                    h = h * 1.05;
                                    w = w * 1.05;
                                }
                            } else if (delta < 0) {
                                if (h > 100) {
                                    h = h * 0.95;
                                    w = w * 0.95;
                                }
                            }
                            imagep.css("top", (window.innerHeight - h) / 2);
                            imagep.css("left", (window.innerWidth - w) / 2);
                            image.height(h);
                            image.width(w);
                            imagep.height(h);
                            imagep.width(w);
                        });
                    }
                });
            }


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){

                    var url='#(ctxPath)/pers/approval/supplierEntryInfoForm?agencyInfoId='+obj.data.id;
                    pop_show("提交信息",url,'100%','100%');
                }
            })


        };


    });
</script>
#end