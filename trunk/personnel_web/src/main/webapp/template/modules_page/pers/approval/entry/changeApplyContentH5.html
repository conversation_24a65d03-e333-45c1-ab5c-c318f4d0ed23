#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()异动单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
    .layui-form-item {
        margin-bottom: 8px;
        clear: both;
        *zoom: 1;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click',
            min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click',
            min:0
        });

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(dispatchApply!=null)
            deptSelect.setValue(["#(dispatchApply.dispatchDeptId??)"]);
            #end
        });



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&dispatchDeptId="+dispatchDeptId;

            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&saveType=2&dispatchDeptId="+dispatchDeptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" action="" id="formId" style="padding-top: 20px;padding-left: 20px;padding-right: 20px;">


        #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
        <fieldset class="layui-elem-field layui-field-title" style="display:block;padding-left: 20px;">
            <legend>流程提交人信息</legend>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人部门</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人职位</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">任务编号</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                </div>
            </div>
        </fieldset>
        #end
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>异动单信息</legend>
        </fieldset>

        <div class="layui-row" style="padding: 10px 20px;">

            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>异动员工</label>
                <div class="layui-input-inline">
                    <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                    <input id="id" name="id" type="hidden" value="#(changeApply.id??)">
                    <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                    <input id="createBy" name="createBy" type="hidden" value="#if(changeApply==null)#(createBy??)#else#(changeApply.createBy??)#end">
                    <input type="text" name="title" readonly  value="#(employee.fullName??)(#(employee.workNum??))" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>所在部门</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#(deptNames??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>职位</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#(positionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>异动前薪级</label>
                <div class="layui-input-inline">
                    <select id="oldSalaryLv" name="oldSalaryLv" disabled readonly lay-verify="required"   readonly >
                        <option value="">请选择异动前薪级</option>
                        <option value="1" #if(changeApply.old_salary_lv??=='1') selected #end>初级</option>
                        <option value="2" #if(changeApply.old_salary_lv??=='2') selected #end>中级</option>
                        <option value="3" #if(changeApply.old_salary_lv??=='3') selected #end>高级</option>
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动前综合薪资</label>
                <div class="layui-input-inline">
                    <input type="text" name="lodSalary" readonly id="lodSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.lod_salary??)"  lay-verify="required|number" placeholder="请输入异动前综合薪资" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>异动类型</label>
                <div class="layui-input-inline">

                    <input type="text" name="lodSalary" readonly id="" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.changeApplyType??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>异动后单位</label>
                <div class="layui-input-inline" style="font-size: 12px;" >
                    <input type="text" name="lodSalary" readonly  #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.newDeptName??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>异动后职位</label>
                <div class="layui-input-inline">
                    <input type="text" name="lodSalary" readonly  #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.newPositionName??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>异动后薪级</label>
                <div class="layui-input-inline">
                    <select id="newSalaryLv" name="newSalaryLv" disabled readonly lay-verify="required" readonly #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                        <option value="">请选择异动前薪级</option>
                        <option value="1" #if(changeApply.new_salary_lv??=='1') selected #end>初级</option>
                        <option value="2" #if(changeApply.new_salary_lv??=='2') selected #end>中级</option>
                        <option value="3" #if(changeApply.new_salary_lv??=='3') selected #end>高级</option>
                    </select>
                </div>
            </div>
            <!--<div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动后综合薪资</label>
                <div class="layui-input-inline">
                    <input type="text" name="newSalary" readonly id="newSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.new_salary??)"  lay-verify="required|number" placeholder="请输入异动后综合薪资" autocomplete="off" class="layui-input">
                </div>
            </div>-->

            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动后薪资</label>
                <div class="layui-input-block">
                    <div class="layui-row">
                        <label class="layui-form-label">基本工资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="basicSalary" readonly id="basicSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.basic_salary??)"  lay-verify="" placeholder="请输入异动后基本工资" autocomplete="off" class="layui-input">
                        </div>

                    </div>
                    <div class="layui-row" style="margin-top: 5px;">
                        <label class="layui-form-label">绩效工资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="meritSalary" readonly id="meritSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.merit_salary??)"  lay-verify="" placeholder="请输入异动后绩效工资" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-row" style="margin-top: 5px;">
                        <label class="layui-form-label">岗位工资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="postSalary" readonly id="postSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.post_salary??)"  lay-verify="" placeholder="请输入异动后岗位工资" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-row" style="margin-top: 5px;">
                        <label class="layui-form-label">综合工资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="comprehensiveSalary" readonly id="comprehensiveSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.comprehensive_salary??)"  lay-verify="" placeholder="请输入异动后综合工资" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                </div>
            </div>


            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动时间长短</label>
                <div class="layui-input-inline">
                    <select id="changeDateType" readonly disabled name="changeDateType" lay-filter="changeDateType" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                        <option value="1" #if(changeApply.change_date_type??=='1') selected #end>短期</option>
                        <option value="2" #if(changeApply.change_date_type??=='2') selected #end>长期</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item" id="startTimeDiv">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动开始时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#date(changeApply.start_time??,'yyyy-MM-dd') #(changeApply.startDate??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            #if(changeApply.change_date_type??=='1')
            <div class="layui-form-item" id="endTimeDiv">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动结束时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#date(changeApply.end_time??,'yyyy-MM-dd') #(changeApply.endDate??)" autocomplete="off" class="layui-input">
                </div>

            </div>
            #end
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">异动原因</label>
                <div class="layui-input-block">
                    <textarea name="changeReason" id="changeReason" readonly placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end
                              class="layui-textarea">#(changeApply.change_reason??)</textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" id="remark" readonly placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end
                              class="layui-textarea">#(changeApply.remark??)</textarea>
                </div>
            </div>
        </div>


    </form>
</div>

</body>
#end