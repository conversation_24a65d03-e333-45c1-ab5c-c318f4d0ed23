#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
<style>
	.layui-table-cell{
		padding: 0 5px;
	}
	.layui-form-checkbox{
		margin: auto!important;
	}

</style>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<div class="layui-col-xs12 layui-col-sm12 layui-col-md12" id="div">
			<div class="layui-row" id="div2">
				<form class="layui-form layui-form-pane" method="" action="">
					<div class="layui-row">
						<div class="layui-input-inline">
							<span class="layui-form-label" style="width:78px;">姓名</span>
							<div class="layui-input-inline" style="width: 120px;">
								<input type="text" id="fullName" class="layui-input" placeholder="请输入姓名搜索" autocomplete="off">
							</div>
						</div>
						&nbsp;
						<div class="layui-input-inline">
							<span class="layui-form-label" style="padding: 8px 5px;">证件号码</span>
							<div class="layui-input-inline">
								<input type="text" id="idCard" class="layui-input" placeholder="请输入证件号码搜索" autocomplete="off">
							</div>
						</div>
						&nbsp;
						<div class="layui-input-inline">
							<span class="layui-form-label" style="padding: 8px 5px;">手机号码</span>
							<div class="layui-input-inline" style="width: 132px;">
								<input type="text" id="phoneNum" class="layui-input" placeholder="请输入手机号码搜索" autocomplete="off">
							</div>
						</div>
						&nbsp;
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<label style="margin-left: 10px;">组织架构：</label>
								<div class="layui-inline"  style="width: 220px;">
									<div id="deptSelect" style="margin: 5px 10px;">

									</div>
								</div>
							</div>
						</div>
						&nbsp;
						<div class="layui-inline">
							<div class="layui-input-inline" style="width:120px;">
								<input type="checkbox" id="isContainChildren" name="isContainChildren" title="包含子部门" value="1" lay-skin="primary" checked>
							</div>
						</div>
						&nbsp;



					<!--</div>
					<div class="layui-row" style="margin-top: 10px;">-->
						<div class="layui-input-inline" >
							<span class="layui-form-label" style="width:78px;">状态</span>
							<div class="layui-input-inline" style="width:100px;">
								<select name="archiveStatus" id="archiveStatus">
									#dictOption("archive_status", "incumbency", "")
								</select>
							</div>
						</div>
						<div class="layui-input-inline" >
							<span class="layui-form-label" style="width:78px;">排序</span>
							<div class="layui-input-inline" style="width:160px;">
								<select name="sort" id="sort">
									<option value="">请选择排序规则</option>
									<option value="0">姓名首字母升序</option>
									<option value="1">姓名首字母降序</option>
									<option value="2">入职日期升序</option>
									<option value="3">入职日期降序</option>
									<option value="4">合同到期时间升序</option>
								</select>
							</div>
						</div>
						<div class="layui-input-inline">
							<span class="layui-form-label" style="width:78px;">职位</span>
							<div class="layui-input-inline">
								<input type="text" id="position" name="position" class="layui-input" placeholder="请输入职位搜索" autocomplete="off">
							</div>
						</div>
						<div class="layui-input-inline">
							<label class="layui-form-label">性别</label>
							<div class="layui-input-inline" style="width:120px;">
								<select name="sex" id="sex" lay-verify="required" lay-filter="">
									<option value="">请选择性别</option>
									#dictOption("sex", model.sex??'', "")
								</select>
							</div>
						</div>
						<div class="layui-input-inline" style="display: none;">
							<label class="layui-form-label">员工类型</label>
							<div class="layui-input-inline" style="width:120px;">
								<div id="employeeType" class="xm-select-demo"></div>
							</div>
						</div>



						<div class="layui-btn-group" style="float: right;margin-right: 20px;">
							<button type="button" id="btn-search" class="layui-btn mgl-20">查询</button>
							<button type="reset" id="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
							#shiroHasPermission("emp:archives:addBtn")
							<button id="btn-add" type="button" class="layui-btn btn-add btn-default">添加</button>
							#end
							#shiroHasPermission("emp:archives:exportBtn")
							<button id="exportBtn" type="button" class="layui-btn btn-add btn-default">导出</button>
							<button id="importBtn" type="button" class="layui-btn btn-add btn-default">导入</button>
							#end

							<button id="btn-refresh" type="button" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></button>
						</div>
					</div>

					<!--<div class="layui-row" style="margin-top: 10px;">


					</div>-->

				</form>
			</div>
			<div class="layui-row">
				<table id="employeeTable" lay-filter="employeeTable"></table>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">
<div class="layui-btn-group">
	#shiroHasPermission("emp:archives:seeBtn")
	<a class="layui-btn layui-btn-xs" lay-event="see">查看</a>
	#end
	#shiroHasPermission("emp:archives:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	<!--<a class="layui-btn layui-btn-xs" lay-event="checkin">考勤</a>-->

	<!--<a class="layui-btn layui-btn-sm layui-btn-normal" lay-event="orgAssign">部门分配</a>-->
	#shiroHasPermission("emp:archives:enclosureManage")
	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="enclosure">附件</a>
	#end
	#shiroHasPermission("emp:archives:accountRelation")
	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="loginAccountManage">帐号关联</a>
	#end
	#shiroHasPermission("emp:archives:contractManage")
	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="contract">合同</a>
	#end
	#shiroHasPermission("emp:archives:changeDepartmentManage")
	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="changeDepartment">调薪/调岗</a>
	#end
	#shiroHasPermission("emp:archives:rewarDpunishmentManage")
	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="rewarDpunishment">奖罚记录</a>
	#end
	#shiroHasPermission("emp:archives:bankBtn")
	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="bankAccount">银行账户</a>
	#end
	#shiroHasPermission("emp:archives:voidBtn")
	<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
	#end
</div>
</script>

<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});


layui.extend({
	dtree: 'layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use(['dtree','layer','jquery'], function(){
	var dtree = layui.dtree, layer = layui.layer, $ = layui.jquery;

	var DemoTree;

	/*$.post('#(ctxPath)/persOrg/orgDtree',{},function (r) {
		var jsonArray=[];
		$.each(r.data,function (index,item) {
			var json={"id":item.id,"title":item.name,"checkArr":"0","parentId":item.pId};
			var flag=isUpdateParentId(json.parentId,r.data);
			if(flag){
				json.parentId='0';
			}
			jsonArray[index]=json;
		});
		// 初始化树
		DemoTree = dtree.renderSelect({
			elem: "#deptId",
			dataFormat: "list",
			data:jsonArray,
			initLevel:10,
			menubar:{  //菜单栏
				search:""  //搜索
			},
			selectCardHeight:"500"
			//url: "#(ctxPath)/persOrg/orgDtree" // 使用url加载（可与data加载同时存在）
		});
	});*/


	function isUpdateParentId(pid,array){
		var flag=true;
		$.each(array,function (i,it) {
			if(pid==it.id){
				flag=false;
				return false;
			}
		});
		return flag;
	}

	/*$("#reset").on('click',function () {
		$(".layui-select-title input[dtree-id='deptId']").val('');
		DemoTree.cancelNavThis();
	})*/

	// 绑定节点点击
	/*dtree.on("node('deptId')" ,function(obj){

	});*/
});

layui.use(['table', 'vip_table','form','upload'], function() {
	// 操作对象
	var table = layui.table
    , layer = layui.layer
    , vipTable = layui.vip_table
    , $ = layui.$
    , tableId = 'employeeTable'
	,form=layui.form
	,upload=layui.upload
    ;



	var deptSelect = xmSelect.render({
		el: '#deptSelect',
		autoRow: true,
		height: '200px',
		prop: {
			name: 'name',
			value: 'id',
		},
		radio: true,
		filterable: true,//搜索
		tree: {
			show: true,
			expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
			showFolderIcon: true,
			showLine: true,
			indent: 15,
			lazy: true,
			clickExpand: true,
			clickClose: true,
			strict: false,
			//点击节点是否选中
			clickCheck: true,


			load: function(item, cb){

			}
		},
		height: 'auto',
		data(){
			return [];
		}
	})



	$.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
		deptSelect.update({
			data:res
		})
	});

	//
	let employeeTypeArray=[];
	let initValue=[];
	#if(type!=null)
		let employeeTypes = JSON.parse('#(employeeTypeArrayStr)');
		$.each(employeeTypes,function (index,item) {
			if(item.value=='agency'){
				employeeTypeArray.push(item);
				initValue.push(item.value);
			}
		})
	#else
		let employeeTypes = JSON.parse('#(employeeTypeArrayStr)');
		$.each(employeeTypes,function (index,item) {
			if(item.value!='agency'){
				employeeTypeArray.push(item);
				initValue.push(item.value);
			}
		})
	#end
	var employeeTypeSelect = xmSelect.render({
		el: '#employeeType',
		data: employeeTypeArray,
		initValue:initValue
	})


	loadTable();

	// 表格加载渲染
	function loadTable() {
		layer.load();

		var tableObj = table.render({
			id : tableId
			,elem : '#'+tableId
			,method : 'POST'
			,url : '#(ctxPath)/persOrgEmployee/pageTable'
			,height: 'full-134'    //容器高度
			,where : {orgParentIds:$("#orgId").val(),"archiveStatus":"incumbency",'employeeType':JSON.stringify(initValue)}//额外查询条件
			,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
			,cols : [[
				{field:'workNum', title:'工号',width:85, align:'center'}
				#if(type!=null)
				,{field:'agencyName', title:'所属代理商',width:180, align:'center'}
				#end
				,{field:'fullName', title:'姓名',width:85, align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
				,{field:'', title:'部门',minWidth:150, align:'center',templet:function (d) {
						if(d.org==undefined || d.org.orgName==undefined){
							return '';
						}else{
							//return '<span title="'+d.org.orgName+'" >'+d.org.orgName+'</span>';
							return d.org.orgName;
						}
					}}
				,{field:'', title:'职位',width:130, align:'center',templet:function (d) {
						if(d.org==undefined || d.org.positionName==undefined){
							return '';
						}else{
							return d.org.positionName;
						}
					}}
				,{field:'', title:'角色',width:130, align:'center',templet:function (d) {
						if(d.org==undefined || d.org.roleName==undefined){
							return '';
						}else{
							return d.org.roleName;
						}
					}}
				,{field:'', title:'性别', width:60, align:'center', templet:'#dictTpl("sex", "sex")'}
				,{field:'', title:'生日', width:55, align:'center',templet:function (d) {
						if(typeof(d.birthday)==='undefined'){
							return ''
						}else{
							return d.birthday.substring(5,d.birthday.length);
						}

					}}
				#if(type==null)
				,{field:'', title:'民族', width:100, align:'center', templet:'#dictTpl("nation", "nationality")'}
				#end
				,{field:'phoneNum', title:'手机号', width:100, align:'center'}
				/*,{field:'extNum', title:'分机号', width:120, align:'center'}*/
				,{field:'', title:'入职日期', width:90, align:'center',templet:"<div>{{ dateFormat(d.entryTime,'yyyy-MM-dd') }}</div>"}
				,{field:'', title:'状态', width:60, align:'center', templet:'#dictTpl("archive_status", "archiveStatus")'}
				/*,{field:'', title:'剩余年休假', width:80, align:'center', templet:function (d) {
						if( d.org==undefined || d.org.orgType==='branche_office'){
							return '- -';
						}else{
							if(typeof d.annualLeaveDays=="undefined"){
								return "0天";
							}else{
								return d.annualLeaveDays+"天";
							}
						}

					}}*/
				/*,{field:'', title:'重新入职', width:68, align:'center', templet:function (d) {
						if(d.isReemployment=='1'){
							return '是';
						}else if(d.isReemployment=='0'){
							return '否';
						}else{
							return '- -';
						}
					}}*/
				#if(type==null)
				,{field:'', title:'社保', width:60, align:'center', templet:function (d) {
						if(d.socialStatus=='1'){
							return '已缴';
						}else if(d.socialStatus=='2'){
							return '未缴';
						}else if(d.socialStatus=='3'){
							return '放弃'
						}else{
							return '- -';
						}
					}}
				,{field:'contractEndDate', title:'合同到期时间', width:100, align:'center'}
				#end
				,{title:'操作', fixed:'right', width:390, align:'center', toolbar:'#empTableBar'}

			]]
			,page : true
			,done:function () {
				//
				var layerTips;
				$("td").on("mouseenter", function() {
					//js主要利用offsetWidth和scrollWidth判断是否溢出。
					//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
					if (this.offsetWidth < this.firstChild.scrollWidth) {
						var that = this;
						var text = $(this).text();
						layerTips=layer.tips(text, that, {
							tips: 1,
							time: 0
						});
					}
				});
				$("td").on("mouseleave", function() {
					//js主要利用offsetWidth和scrollWidth判断是否溢出。
					//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
					layer.close(layerTips);
				});
				layer.closeAll('loading');
			}
		});
	}

	//监听工具条
	table.on('tool('+tableId+')', function(obj) {
		if(obj.event==='see'){
			pop_show('查看', '#(ctxPath)/persOrgEmployee/edit?id='+obj.data.id+"&handleType=1", '900', '');
		}else if (obj.event === 'edit') {
			pop_show('编辑', '#(ctxPath)/persOrgEmployee/edit?id='+obj.data.id, '900', '');
		} else if (obj.event === 'orgAssign') {
			pop_show('部门分配', '#(ctxPath)/persOrgEmployee/orgAssign?id='+obj.data.id, '', '');
		} else if (obj.event === 'loginAccountManage') {
			pop_show('登陆帐号关联', '#(ctxPath)/persOrgEmployee/loginAccountManage?id='+obj.data.id, '', '');
		} else if (obj.event === 'del') {
			layer.confirm('您确定要作废当前人员？该人员的登陆帐号会一并作废。', function(index) {
				//作废操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/persOrgEmployee/del',
		            data: {id:obj.data.id, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}else if(obj.event==='contract') {
			pop_show("["+obj.data.fullName + ']的合同', '#(ctxPath)/persOrgEmployee/contractIndex?employeeId=' + obj.data.id, '100%', '100%');
		}else if(obj.event==='changeDepartment'){
			pop_show("["+obj.data.fullName + ']的调岗/调薪', '#(ctxPath)/persOrgEmployee/changeDepartmentIndex?employeeId=' + obj.data.id, '100%', '100%');
		}else if(obj.event==='rewarDpunishment'){
			pop_show("["+obj.data.fullName + ']的奖罚', '#(ctxPath)/persOrgEmployee/rewardPunishIndex?employeeId=' + obj.data.id, '100%', '100%');
		}else if(obj.event==='enclosure'){
			pop_show("["+obj.data.fullName + ']的附件', '#(ctxPath)/persOrgEmployee/enclosureIndex?employeeId=' + obj.data.id, '100%', '100%');
		}else if(obj.event==='checkin'){
			pop_show("["+obj.data.fullName + ']的考勤', '#(ctxPath)/employeeCheckin/index?employeeId=' + obj.data.id, '100%', '100%');
		} else if (obj.event === 'bankAccount') {
			pop_show('银行账户', '#(ctxPath)/persOrgEmployee/bankAccount?id='+obj.data.id, '', '');
		}
	});
	//重载表格，跳转到第一页
    pageTableReload = function () {
    	form.render('checkbox');
		layer.load();
		var isContainChildren="0";
		if("1"==$("input:checkbox[name='isContainChildren']:checked").val()){
			isContainChildren="1";
		}
		var id="";
		$.each(deptSelect.getValue(),function (index,item) {
			id=item.id;
		});
    	tableReload(tableId,{isContainChildren:isContainChildren,phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
			'deptId':id,"sort":$("#sort").val(),'position':$("#position").val(),'sex':$("#sex").val(),'employeeType':JSON.stringify(initValue)});
    }

    //重载表格，跳转当前页面
	pageTableReloadCurrPage = function(){
		layer.load();
    	var currPage=$(".layui-laypage-skip").children("input").val();
		var id="";
		$.each(deptSelect.getValue(),function (index,item) {
			id=item.id;
		});
    	table.reload(tableId,{'where':{phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
				deptId:id,"sort":$("#sort").val(),'position':$("#position").val(),'sex':$("#sex").val()},page:{'curr':currPage}});
	}

	showPhotos=function(images) {
		layer.photos({
			/*area: '400px',*/
			shade: [0.85, '#000'],
			anim: 0,
			photos: {
				"title": "附件预览",
				"id": 'showImages',
				"data": images
			}
			,tab:function () {
				num=0;
				$("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
						'\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
						'\t</div>');
				$(document).on("click", "#xuanzhuan", function(e) {
					num = (num+90)%360;
					$(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
					$("#layui-layer-photos").css('transform','rotate('+num+'deg)');

				});

				$(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
					var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
							(e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
					var imagep = $(".layui-layer-phimg").parent().parent();
					var image = $(".layui-layer-phimg").parent();
					var h = image.height();
					var w = image.width();
					if (delta > 0) {
						if (h < (window.innerHeight)) {
							h = h * 1.05;
							w = w * 1.05;
						}
					} else if (delta < 0) {
						if (h > 100) {
							h = h * 0.95;
							w = w * 0.95;
						}
					}
					imagep.css("top", (window.innerHeight - h) / 2);
					imagep.css("left", (window.innerWidth - w) / 2);
					image.height(h);
					image.width(w);
					imagep.height(h);
					imagep.width(w);
				});
			}
		});
	}

	//搜索按钮点击事件
	$('#btn-search').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#btn-add').on('click', function() {
		pop_show('添加', '#(ctxPath)/persOrgEmployee/add', '900', '');
	});
	var demo1;
	$("#exportBtn").on('click',function () {

		layer.open({
			type: 1,
			title: '请选择要导出的数据',
			shadeClose: true,
			shade: 0.8,
			area: ['580px', '500px'],
			shadeClose: false,
			content: '<div class="layui-row"><div id="demo1" class="xm-select-demo" style="margin: 8px;"></div>' +
					'        <button class="layui-btn" style="float: right;    margin-top: 195px;margin-right: 10px;" lay-submit=""  type="button" onclick="onclickf()">导出</button>' +
					'    </div>'
		});
		demo1 = xmSelect.render({
			el: '#demo1',
			filterable: true,
			autoRow: true,
			toolbar:{
				show: true,
			},
			tips: '请选择要导出的数据',
			data: [
				{name: '工号', value: 'workNum', selected: true},
				{name: '部门', value: 'orgName', selected: true},
				{name: '姓名', value: 'fullName', selected: true},
				{name: '职位', value: 'positionName', selected: true},
				{name: '性别', value: 'sex', selected: true},
				{name: '年龄', value: 'age', selected: true},
				{name: '联系方式', value: 'phoneNum', selected: true},
				{name: '入职日期', value: 'entryTime', selected: true},
				{name: '工龄', value: 'workAge', selected: true},
				{name: '试用薪资', value: 'probationSalary', selected: true},
				{name: '转正日期', value: 'formalDate', selected: true},
				{name: '转正薪资', value: 'salary', selected: true},
				{name: '现有薪酬', value: 'nowSalary', selected: true},
				{name: '异动情况', value: 'changeDept', selected: true},
				{name: '身份证号码', value: 'idcard', selected: true},
				{name: '出生日期', value: 'birthday', selected: true},
				{name: '籍贯', value: 'nativePlace', selected: true},
				{name: '民族', value: 'nationality', selected: true},
				{name: '身份证地址', value: 'idCardAddr', selected: true},
				{name: '现住地址', value: 'residentAddr', selected: true},
				{name: '学历', value: 'education', selected: true},
				{name: '紧急联系人', value: 'linkPeople', selected: true},
				{name: '紧急联系人手机', value: 'linkPhone', selected: true},
				{name: '合同期限', value: 'contractYear', selected: true},
				{name: '合同有效期', value: 'contractStartDate', selected: true},
				{name: '合同到期日', value: 'contractEndDate', selected: true},
				{name: '合同类型', value: 'contractType', selected: true},
				{name: '合同归属地', value: 'contractAddr', selected: true},
				{name: '续签记录', value: 'contracts', selected: true},
				{name: '意外险', value: 'accidentInsurance', selected: true},
				{name: '职工社保', value: 'socialStatus', selected: true},
				{name: '社保归属地', value: 'socialAddr', selected: true},
				{name: '银行卡', value: 'card', selected: true},
				{name: '备注', value: 'remark', selected: true},
			]
		})

		return false;


		var id="";
		$.each(deptSelect.getValue(),function (index,item) {
			id=item.id;
		});
		var isContainChildren="0";
		if("1"==$("input:checkbox[name='isContainChildren']:checked").val()){
			isContainChildren="1";
		}
		var data='?isContainChildren='+isContainChildren+"&phoneNum="+$('#phoneNum').val()+'&orgParentIds='+$('#orgId').val()+'&fullName='+$('#fullName').val()
				+'&archiveStatus='+$("#archiveStatus").val()+'&idCard='+$("#idCard").val()+
				'&deptId='+id+"&sort="+$("#sort").val()+'&position='+$("#position").val()+'&sex='+$("#sex").val();

		var url="#(ctxPath)/persOrgEmployee/empCheckinRecordExport"+data;
		window.location.href=url;
	})

	 onclickf=function(){
		 var selectData=demo1.getValue();
		 var selectArray=[];
		 $.each(demo1.getValue(),function (index,item) {
		 	/*var obj={};
		 	obj["value"]=item.value;
		 	obj["name"]=item.name;
		 	selectArray.push(obj);*/
			 selectArray.push(item.value);
		 });
		 if(selectArray.length<=0){
			 layer.msg('请选择要导出的数据!',{icon:5});
		 	return false;
		 }
		 var id="";
		 $.each(deptSelect.getValue(),function (index,item) {
			 id=item.id;
		 });
		 var isContainChildren="0";
		 if("1"==$("input:checkbox[name='isContainChildren']:checked").val()){
			 isContainChildren="1";
		 }
		 var data='?isContainChildren='+isContainChildren+"&phoneNum="+$('#phoneNum').val()+'&orgParentIds='+$('#orgId').val()+'&fullName='+$('#fullName').val()
				 +'&archiveStatus='+$("#archiveStatus").val()+'&idCard='+$("#idCard").val()+
				 '&deptId='+id+"&sort="+$("#sort").val()+'&position='+$("#position").val()+'&sex='+$("#sex").val()+"&selectArray="+JSON.stringify(selectArray);

		 var url="#(ctxPath)/persOrgEmployee/empCheckinRecordExport"+data;
		 window.location.href=url;
	}



	$("#importBtn").on('click',function () {

		layer.open({
			type: 1,
			area: ["500px", "360px"],
			title: "上传文件",
			content: '<div id="file_upload_div" style="    text-align: center;padding-top: 20px;" class="text-center">\n' +
					'    <div class="layui-upload-drag" id="test10">\n' +
					'        <i class="layui-icon"></i>\n' +
					'        <p>点击上传，或将文件拖拽到此处</p>\n' +
					'    </div><div class="mt-2 mb-2" id="file_name"></div>\n' +
					'    <div class="row w-100" style="height: 40px;"></div>\n' +
					'    <button type="button" class="layui-btn mt-2" id="upload">开始上传</button>\n' +
					'</div>'

		});

		//执行实例
		var uploadInst = upload.render({
			elem: '#test10' //绑定元素

			, auto: false
			, bindAction: "#upload"
			, url: '#(ctxPath)/persOrgEmployee/excelUploadEmp' //上传接口
			, choose: function (obj) {
				obj.preview(function (index, file, result) {
					document.getElementById("file_name").innerHTML = file.name;
				})
			}
			,accept:"file"
			,before:function (res) {
				layer.load();
			}
			, done: function (res) {
				layer.closeAll('loading');
				if(res.state=="ok"){
					layer.msg("上传成功")
					pop_close();
				}else{
					layer.msg('上传失败', {icon: 2, offset: 'auto'});
				}

			}
			, error: function () {
				layer.closeAll('loading');
				layer.msg("上传失败，请重新上传")
			}
		});
	})

	// 刷新
    $('#btn-refresh').on('click', function () {
    	pageTableReload();
    });

	//loadOrgSelect();

    //加载组织架构select
	/*function loadOrgSelect() {

		$.post('#(ctxPath)/persOrg/orgFormTreeTableByUserId',{},function (r) {
			var data=r.data;
			var str="<option value=''>全部</option>";
			$.each(data,function (index,item) {
				str+="<option value='"+item.id+"'>"+item.name+"</option>";
			});
			$("#deptId").html(str);
			form.render("select");
		})
	}*/

	/*treeSelect.render({
		// 选择器
		elem: '#deptId',
		// 数据
		data: '#(ctxPath)/persOrg/orgFormTreeByUserId',
		// 异步加载方式：get/post，默认get
		type: 'get',
		// 占位符
		placeholder: '修改默认提示信息',
		// 是否开启搜索功能：true/false，默认false
		search: true,
		// 一些可定制的样式
		style: {
			folder: {
				enable: true
			},
			line: {
				enable: true
			}
		},
		// 点击回调
		click: function(d){
			console.log(d);
		},
		// 加载完成后的回调函数
		success: function (d) {
			console.log(d);
//                选中节点，根据id筛选
			treeSelect.checkNode('tree', 3);
			console.log($('#tree').val());
//                获取zTree对象，可以调用zTree方法
			var treeObj = treeSelect.zTree('tree');
			console.log(treeObj);
//                刷新树结构
			treeSelect.refresh('tree');
		}
	});*/
});
</script>
#end