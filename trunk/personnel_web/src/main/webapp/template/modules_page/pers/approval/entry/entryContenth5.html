#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()入职申请表#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }

    .layui-table td,.layui-table th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }


    .layui-table{
        color: #000000;
    }
    .layui-btn-disabled{
        background-color: #e6e6e6;
        color: #000000;
    }

    .layui-disabled, .layui-disabled:hover{
        color: #000000!important;
        cursor: not-allowed!important;
    }
    textarea:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }
    input:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }

</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/noticeType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#noticeTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.noticeTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });



        loadContent=function (taskNo,recordId) {
            var url="";
            if(taskNo=='PH001'){
                url="#(ctxPath)/pers/approval/entryContent";
            }else if(taskNo=='PH002'){
                url="#(ctxPath)/pers/approval/qualifiedContent";
            }else if(taskNo=='PH003'){
                url="#(ctxPath)/pers/approval/changeDeptContent";
            }else if(taskNo=='PH004'){
                url="#(ctxPath)/pers/approval/quitContent";
            }
            $.ajax({
                url : url+'?recordId='+recordId+"&taskId="+$("#taskId").val(),
                type : 'GET',
                success : function(data) {
                    $("#content").html(data);
                    if(taskNo=='PH001'){
                        laydate.render({
                            elem:'#hrDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#edDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#gmDate',
                            type:'date',
                            trigger: 'click'
                        });

                    }else if(taskNo=='PH003'){
                        laydate.render({
                            elem:'#hrSalaryMonth',
                            type:'month',
                            trigger: 'click'
                        });
                    }else if(taskNo=='PH002'){

                        laydate.render({
                            elem:'#baseQualifiedDate',
                            type:'date',
                            trigger: 'click'
                        });

                        laydate.render({
                            elem:'#hrQualifiedDate',
                            type:'date',
                            trigger: 'click'
                        });
                    }else if(taskNo=='PH004'){
                    }
                    form.render();
                }
            });
            form.render();
        }


        //loadContent("#(persTask.taskNo??)","#(persTask.recordId??)");



        saveData=function(url,data){
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                    }
                },
                complete : function() {
                }
            });
        }

        //保存员工入职记录意见
        form.on('submit(saveHrBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'hrOpinion':$("#hrOpinion").val(),'hrInterviewers':$("#hrInterviewers").val(),'hrDate':$("#hrDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        })

        form.on('submit(saveEdBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'edOpinion':$("#edOpinion").val(),'edInterviewers':$("#edInterviewers").val(),'edDate':$("#edDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        })

        form.on('submit(saveGmBtn)',function (obj) {
            var data={'id':$("#recordId").val(),'gmOpinion':$("#gmOpinion").val(),'gmInterviewers':$("#gmInterviewers").val(),'gmDate':$("#gmDate").val()}
            saveData('#(ctxPath)/pers/approval/saveEntryInfo',data);
            return false;
        });

        //保存员工调岗意见


        //1.
        form.on('submit(saveOldDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"oldDeptOpinion":$("#oldDeptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //2.
        form.on('submit(saveNewDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"newDeptOpinion":$("#newDeptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //3.
        form.on('submit(saveBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //4.
        form.on('submit(saveHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"salary":$("#salary").val(),"hrSalaryMonth":$("#hrSalaryMonth").val(),"hrOpinion":$("#hrOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //5.
        form.on('submit(saveGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveChangeDept',data);
            return false;
        })

        //保存转正意见

        //1.
        form.on('submit(saveQBaseHr)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"morality":$("#morality").val(),"train":$("#train").val()
                ,"lateCount":$("#lateCount").val(),"sickLeaveCount":$("#sickLeaveCount").val(),"thingLeaveCount":$("#thingLeaveCount").val()
                ,"absenteeismCount":$("#absenteeismCount").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //2.
        form.on('submit(saveQDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"deptOpinion":$("#deptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //3.
        form.on('submit(saveQBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val(),"baseUseOpinion":$("#baseUseOpinion").val()
                ,"baseQualifiedDate":$("#baseQualifiedDate").val(),"baseQualifiedSalary":$("#baseQualifiedSalary").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //4.
        form.on('submit(saveQHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"hrOpinion":$("#hrOpinion").val(),"hrUseOpinion":$("#hrUseOpinion").val()
                ,"hrQualifiedDate":$("#hrQualifiedDate").val(),"hrQualifiedSalary":$("#hrQualifiedSalary").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })
        //5.
        form.on('submit(saveQGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQualified',data);
            return false;
        })

        //保存辞职意见
        //1.
        form.on('submit(saveQuitDeptOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"deptOpinion":$("#deptOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //2.
        form.on('submit(saveQuitBaseOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"baseOpinion":$("#baseOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //3.
        form.on('submit(saveQuitHrOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"hrOpinion":$("#hrOpinion").val()
                ,"hrUseOpinion":$("#hrUseOpinion").val(),"hrIsAgree":$("#hrIsAgree").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })

        //4.
        form.on('submit(saveQuitGeneralManagerOpinion)',function (obj) {
            var data={'id':$("#recordId").val(),'currentStepAlias':$("#currentStepAlias").val(),"generalManagerOpinion":$("#generalManagerOpinion").val()};
            saveData('#(ctxPath)/pers/approval/saveQiut',data);
            return false;
        })


        //保存员工入职记录流程
        doTask=function (stepState,msg) {
            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }



        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        showPhotos=function(images) {
            layer.photos({
                /*area: '400px',*/
                shade: [0.85, '#000'],
                anim: 0,
                photos: {
                    "title": "附件预览",
                    "id": 'showImages',
                    "data": images
                }
            });
        }

        renderImg=function(src) {
            if(src==''){
                return false;
            }
            var json={
                "title": "身份证图片", //相册标题
                "id": 123, //相册id
                "start": 0, //初始显示的图片序号，默认0
                "data": [   //相册包含的图片，数组格式
                    {
                        "alt": "身份证图片",
                        "src": src, //原图地址
                        "thumb": "" //缩略图地址
                    }
                ]
            };
            layui.layer.photos({
                photos: json
                , anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
                ,tab:function () {
                    num=0;
                    $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                        '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                        '\t</div>');
                    $(document).on("click", "#xuanzhuan", function(e) {
                        num = (num+90)%360;
                        $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                        $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                    });

                    $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                        var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                            (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                        var imagep = $(".layui-layer-phimg").parent().parent();
                        var image = $(".layui-layer-phimg").parent();
                        var h = image.height();
                        var w = image.width();
                        if (delta > 0) {
                            if (h < (window.innerHeight)) {
                                h = h * 1.05;
                                w = w * 1.05;
                            }
                        } else if (delta < 0) {
                            if (h > 100) {
                                h = h * 0.95;
                                w = w * 0.95;
                            }
                        }
                        imagep.css("top", (window.innerHeight - h) / 2);
                        imagep.css("left", (window.innerWidth - w) / 2);
                        image.height(h);
                        image.width(w);
                        imagep.height(h);
                        imagep.width(w);
                    });
                }
            });


        }

        enclosure=function(empId){
            pop_show('附件', '#(ctxPath)/persOrgEmployee/enclosureIndex?employeeId=' +empId , 900, 600);
            return false;
        }
    });
</script>
#end

#define content()


<table class="layui-table" style="width: 1280px;height: 926px;">
    <tbody>

    <tr>
        <input id="currentStepAlias" value="#(currentStepAlias??)" type="hidden">
        <td colspan="8" align="center">员工入职登记表<button class="layui-btn" id="enclosure" style="float: right;" onclick="enclosure('#(entryInfo.employeeId??)')" type="button">查看附件</button></td>
    </tr>
    #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null)
    <tr>
        <td colspan="8" align="" style="background-color: #e6e6e6;">提交人信息</td>
    </tr>
    <tr>
        <td>提交人姓名</td>
        <td>
            #(persTask.submitUserName??)
        </td>
        <td>
            提交人部门
        </td>
        <td>
            #(persTask.submitUserDeptName??)
        </td>
        <td>
            提交人职位
        </td>
        <td>
            #(persTask.submitUserPositionName??)
        </td>
        <td colspan="2"></td>
    </tr>
    <tr>
        <td>提交时间</td>
        <td>
            #date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')
        </td>
        <td>
            任务编号
        </td>
        <td>
            #(persTask.taskNumber??)
        </td>
        <td colspan="4"></td>
        <!--<td>

        </td>
        <td></td>
        <td></td>
        <td></td>-->
    </tr>
    #end
    <tr>
        <td colspan="8" align="" style="background-color: #e6e6e6;">入职员工信息</td>
    </tr>
    <tr>
        <td colspan="2" align="">填表日期:#date(entryInfo.saveDate,'yyyy-MM-dd')</td>
        <td colspan="2" align="">入职日期:#date(entryInfo.entryTime,'yyyy-MM-dd')</td>
        <td colspan="4" align="">入职部门:#(deptName??)</td>
    </tr>
    <tr>
        <td>申请职位</td>
        <td>#(positionName??)</td>
        <td colspan="3">
            试用期：#(entryInfo.probationSalary??)
        </td>
        <td colspan="3">
            转正后：#(entryInfo.permanentSalary??)
        </td>
    </tr>
    <tr>
        <td>姓名</td>
        <td>#(entryInfo.employeeName??)</td>
        <td>最高学历</td>
        <td>
            #if(entryInfo.highestEducation??=='shuo_shi')
            硕士
            #else if(entryInfo.highestEducation??=='yan_jiu_sheng')
            研究生
            #else if(entryInfo.highestEducation??=='ben_ke')
            本科
            #else if(entryInfo.highestEducation??=='da_zhuan')
            大专
            #else if(entryInfo.highestEducation??=='zhong_zhuan')
            中专或中技
            #else if(entryInfo.highestEducation??=='ji_gong')
            技工学校
            #else if(entryInfo.highestEducation??=='gao_zhong')
            高中
            #else if(entryInfo.highestEducation??=='chu_zhong')
            初中
            #else if(entryInfo.highestEducation??=='xiao_xue')
            小学
            #else if(entryInfo.highestEducation??=='wen_mang')
            文盲或半文盲
            #end

        </td>
        <td>性别</td>
        <td>#if(entryInfo.gender??=='male')男#else if(entryInfo.gender??=='female') 女 #else 未知 #end</td>
        <td colspan="2" rowspan="6">
            <img src="#(entryInfo.headPortrait??)">
        </td>
    </tr>
    <tr>
        <td>出生日期</td>
        <td>#date(entryInfo.birthday??,'yyyy-MM-dd')</td>
        <td>户口所在地</td>
        <td colspan="3">#(entryInfo.residentAddr??)</td>
        <!--<td colspan="2"></td>-->
    </tr>
    <tr>
        <td>身份证号码</td>
        <td colspan="2">#(entryInfo.idcard??)</td>
        <td>婚姻状况</td>
        <td colspan="2">#if(entryInfo.marriageStatus??=='1')已婚#else if(entryInfo.marriageStatus??=='0')未婚 #end</td>
    </tr>
    <tr>
        <td>身份证正面</td>
        <td colspan="2">
            <img style="cursor:pointer;" src="#(entryInfo.idcardFrontPath??)" onclick="renderImg('#(entryInfo.idcardFrontPath??)')">
        </td>
        <td>身份证反面</td>
        <td colspan="2">
            <img style="cursor:pointer;"  src="#(entryInfo.idcardContraryPath??)" onclick="renderImg('#(entryInfo.idcardContraryPath??)')">
        </td>
    </tr>
    <tr>
        <td>社会福利号码</td>
        <td colspan="2">#(entryInfo.socialNumber??)</td>
        <td>个人邮箱</td>
        <td colspan="2">#(entryInfo.email??)</td>

    </tr>
    <tr>
        <td>现住地址</td>
        <td colspan="2">#(entryInfo.liveAddr??)</td>
        <td>联系方式</td>
        <td colspan="2">#(entryInfo.phone??)</td>

    </tr>
    <tr>
        <td>籍贯</td>
        <td >#(entryInfo.nativelace??)</td>
        <td>政治面貌</td>
        <td>#(entryInfo.politicalOutlook??)</td>
        <td>外语水平</td>
        <td colspan="3">#(entryInfo.languageLevel??)</td>
    </tr>

    <tr>
        <td>技术职称证书</td>
        <td colspan="2">#(entryInfo.languageLevel??)</td>
        <td colspan="2">计算机水平</td>
        <td colspan="3">#(entryInfo.languageLevel??)</td>
    </tr>
    <tr>
        <td>试用期薪资</td>
        <td >
            #(entryInfo.probationSalary??)
        </td>
        <td>转正后薪资</td>
        <td>
            #(entryInfo.permanentSalary??)
        </td>
        <td></td>
        <td  colspan="3">

        </td>
    </tr>
    <tr>
        <td>试用期绩效薪资</td>
        <td >
            #(entryInfo.probationPerformanceSalary??)
        </td>
        <td>转正后绩效薪资</td>
        <td>
            #(entryInfo.permanentPerformanceSalary??)
        </td>
        <td></td>
        <td  colspan="3">

        </td>
    </tr>
    <tr>
        <td>薪资描述</td>
        <td colspan="8">
            #(entryInfo.salaryRemark??)
        </td>
    </tr>
    <tr>
        <td>合同类型</td>
        <td >
            #if(entryInfo.contractType??=='1')
            劳动合同
            #else if(entryInfo.contractType??=='2')
            劳务合同
            #else if(entryInfo.contractType??=='4')
            待补
            #else if(entryInfo.contractType??=='5')
            实习协议
            #else if(entryInfo.contractType??=='6')
            临时工合同
            #end
        </td>
        <td>开始时间</td>
        <td>#date(entryInfo.contractStartDate??,'yyyy-MM-dd')</td>
        <td>结束时间</td>
        <td >#date(entryInfo.contractEndDate??,'yyyy-MM-dd')</td>
        <td>合同归属地址</td>
        <td>#(entryInfo.contractAddr??)</td>
    </tr>
    <tr>
        <td>合同图片</td>
        <td>
            <img style="cursor:pointer;" src="#(entryInfo.contractPath1??)" onclick="renderImg('#(entryInfo.contractPath1??)')">
        </td>
        <td>
            <img style="cursor:pointer;"  src="#(entryInfo.contractPath2??)" onclick="renderImg('#(entryInfo.contractPath2??)')">
        </td>
        <td>
            <img style="cursor:pointer;"  src="#(entryInfo.contractPath3??)" onclick="renderImg('#(entryInfo.contractPath3??)')">
        </td>
        <td>
            <img style="cursor:pointer;" src="#(entryInfo.contractPath4??)" onclick="renderImg('#(entryInfo.contractPath4??)')">
        </td>
        <td>
            <img style="cursor:pointer;"  src="#(entryInfo.contractPath5??)" onclick="renderImg('#(entryInfo.contractPath5??)')">
        </td>
        <td>
            <img style="cursor:pointer;"  src="#(entryInfo.contractPath6??)" onclick="renderImg('#(entryInfo.contractPath6??)')">
        </td>
        <td></td>
    </tr>

    <tr>
        <td>紧急联系人</td>
        <td>#(entryInfo.emergencyContact??)</td>
        <td>关系</td>
        <td>#(entryInfo.relation??)</td>
        <td colspan="2">紧急联系电话</td>
        <td colspan="2">#(entryInfo.emergencyPhone??)</td>
    </tr>
    <tr>
        <td colspan="8">个人资料  家庭状况 FAMILY SITUATION</td>
    </tr>
    <tr>
        <td>姓名</td>
        <td>关系</td>
        <td colspan="2">工作单位</td>
        <td colspan="2">联系电话</td>
        <td colspan="2">职位</td>
    </tr>
    #if(familySituation.size()>0)
    #for(family : familySituation)
    <tr>
        <td>#(family.name??)</td>
        <td>#(family.relation??)</td>
        <td colspan="2">#(family.workUnit??)</td>
        <td colspan="2">#(family.phone??)</td>
        <td colspan="2">#(family.position??)</td>
    </tr>
    #end
    #else
    <tr style="height: 39px;">
        <td></td>
        <td></td>
        <td colspan="2"></td>
        <td colspan="2"></td>
        <td colspan="2"></td>
    </tr>
    #end

    <tr>
        <td colspan="8">教育经历 EDUCATIONAL BACKGROUND</td>
    </tr>
    <tr>
        <td>起止时间</td>
        <td colspan="3">所在院校（由高往下开始填写）</td>
        <td colspan="2">所修专业</td>
        <td colspan="2">学位（文凭）</td>
    </tr>
    #if(educationalBackground.size()>0)
    #for(educational : educationalBackground)
    <tr>
        <td>#date(educational.startDate,'yyyy-MM-dd')至#date(educational.endDate,'yyyy-MM-dd')</td>
        <td colspan="3">#(educational.school)</td>
        <td colspan="2">#(educational.major)</td>
        <td colspan="2">#(educational.diploma)</td>
    </tr>
    #end
    #else
    <tr style="height: 39px;">
        <td></td>
        <td colspan="3"></td>
        <td colspan="2"></td>
        <td colspan="2"></td>
    </tr>
    #end
    <tr>
        <td colspan="8">培训经历 TRAINING BACKGROUND</td>
    </tr>
    <tr>
        <td>起止时间</td>
        <td colspan="2">课程名称</td>
        <td colspan="3">学习形式（填写内部、外聘或外派培训等）</td>
        <td colspan="2">所获证书</td>
    </tr>
    #if(trainingBackground.size()>0)
    #for(training : trainingBackground)
    <tr>
        <td>#date(training.startDate??,'yyyy-MM-dd')至#date(training.endDate??,'yyyy-MM-dd')</td>
        <td colspan="2">#(training.course??)</td>
        <td colspan="3">#if(training.learningForm=='inside') 内部 #else if(training.learningForm=='expatriate') 外派 #else if(training.learningForm=='other') 其他  #end</td>
        <td colspan="2">#(training.certificate)</td>
    </tr>
    #end
    #else
    <tr style="height: 39px;">
        <td></td>
        <td colspan="2"></td>
        <td colspan="3"></td>
        <td colspan="2"></td>
    </tr>
    #end
    <tr>
        <td colspan="8">工作履历EMPLOYMENT HISTORY( 请从最近工作履历写起）*若曾在我司任职，有关资料必须详细填写</td>
    </tr>
    <tr>
        <td>起止时间</td>
        <td>公司名称</td>
        <td>职位</td>
        <td>离职原因</td>
        <td>最后薪金（税前）</td>
        <td>原公司人事部证明人</td>
        <td colspan="2">证明人办公电话</td>
    </tr>
    #if(employmentHistory.size()>0)
    #for(employment : employmentHistory)
    <tr>
        <td>#date(employment.startDate,'yyyy-MM-dd')至#date(employment.endDate,'yyyy-MM-dd')</td>
        <td>#(employment.company??)</td>
        <td>#(employment.position??)</td>
        <td>#(employment.quitReason??)</td>
        <td>#(employment.salary??)</td>
        <td>#(employment.witness??)</td>
        <td colspan="2">#(employment.phone??)</td>
    </tr>
    #end
    #else
    <tr style="height: 39px;">
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td colspan="2"></td>
    </tr>
    #end
    <tr>
        <td colspan="8">
            是否服从以下工作地点调动:#if(entryInfo.isWorkAddrTransfer=='1') 是 #else if(entryInfo.isWorkAddrTransfer=='0') 否 #end
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#(entryInfo.workAddrTransfer??)
        </td>
    </tr>
    <tr>
        <td colspan="2">兴趣爱好</td>
        <td colspan="2">#(entryInfo.hobby)</td>
        <td >打字速度</td>
        <td colspan="3">英文 #(entryInfo.englishSpeed) 字/分钟、中文 #(entryInfo.chineseSpeed) 字/分钟</td>
    </tr>
    <tr>
        <td>能否出差</td>
        <td>#if(entryInfo.isOffSiteWork=='1') 是 #else if(entryInfo.isOffSiteWork=='0') 否 #end</td>
        <td>能否岗位调动</td>
        <td>#if(entryInfo.jobTransfer=='1') 是 #else if(entryInfo.jobTransfer=='0') 否 #end</td>
        <td>能否加班</td>
        <td>#if(entryInfo.isWorkOvertime=='1') 是 #else if(entryInfo.isWorkOvertime=='0') 否 #end</td>
        <td>能否吃住公司</td>
        <td>#if(entryInfo.isLiveCompany=='1') 是 #else if(entryInfo.isLiveCompany=='0') 否 #end</td>
    </tr>
    <tr>
        <td>能否出差</td>
        <td>#if(entryInfo.isOffSiteWork=='1') 是 #else if(entryInfo.isOffSiteWork=='0') 否 #end</td>
        <td>能否岗位调动</td>
        <td>#if(entryInfo.jobTransfer=='1') 是 #else if(entryInfo.jobTransfer=='0') 否 #end</td>
        <td>能否加班</td>
        <td>#if(entryInfo.isWorkOvertime=='1') 是 #else if(entryInfo.isWorkOvertime=='0') 否 #end</td>
        <td>能否吃住公司</td>
        <td>#if(entryInfo.isLiveCompany=='1') 是 #else if(entryInfo.isLiveCompany=='0') 否 #end</td>
    </tr>
    <tr>
        <td>招聘渠道</td>
        <td>
            #if(entryInfo.recruitChannel??=='online_recruitment')
            网络招聘
            #else if(entryInfo.recruitChannel??=='on_site_recruitment')
            现场招聘
            #else if(entryInfo.recruitChannel??=='internal_introduction')
            内部介绍
            #else if(entryInfo.recruitChannel??=='retirement_reemployment')
            离职返聘或退休返聘
            #else if(entryInfo.recruitChannel??=='labor_dispatch')
            劳务派遣
            #end

        </td>
        <td>介绍人</td>
        <td>#(entryInfo.introducer??)</td>
        <td>职位</td>
        <td>#(entryInfo.IntroducerPosition??)</td>
        <td>关系</td>
        <td>#(entryInfo.IntroducerRelation??)</td>
    </tr>
    #if(stepCode??0>=1)
    <tr>
        <td colspan="8">
            以下部分用人公司填写：
        </td>
    </tr>
    <tr>
        <td colspan="8">
            <!--人力资源部意见：-->
            用人部意见：
        </td>
    </tr>

    <tr>
        <form class="layui-form" id="hrForm" action="">
            <td colspan="2">
                <div class="layui-inline">
                    <select name="hrOpinion" id="hrOpinion" lay-verify="required" #if(entryInfoStep1!=currentStepAlias?? || !isHandle) disabled #end >
                        <option value="">请选择</option>
                        <option value="1" #if('1'==entryInfo.hrOpinion??) selected #end>拒绝</option>
                        <option value="2" #if('2'==entryInfo.hrOpinion??) selected #end>再沟通</option>
                        <option value="3" #if('3'==entryInfo.hrOpinion??) selected #end>录用</option>
                    </select>
                </div>
            </td>
            <td colspan="2">
                面试人：
                <div class="layui-inline">
                    <select name="hrInterviewers" id="hrInterviewers" lay-verify="required"  lay-search #if(entryInfoStep1!=currentStepAlias?? || !isHandle) disabled #end>
                        <option value="">请选择</option>
                        #for(user : userList)
                        <option value="#(user.id??)" #if(user.id==entryInfo.hrInterviewers??) selected #end>#(user.name)(#(user.userName))</option>
                        #end
                    </select>
                </div>
            </td>
            <td colspan="2">
                日期：
                <div class="layui-input-inline">
                    <input type="text" lay-verify="required" id="hrDate" name="hrDate"  placeholder="" value="#date(entryInfo.hrDate??,'yyyy-MM-dd')" #if(entryInfoStep1!=currentStepAlias?? || !isHandle) disabled #end autocomplete="off" class="layui-input">
                </div>
            </td>
            <td colspan="2">
                #if(entryInfoStep1==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" lay-filter="saveHrBtn">保&nbsp;&nbsp;存</button>
                #end
            </td>
        </form>
    </tr>
    #end

    #if(stepCode??0>=2)
    <tr>
        <td colspan="8">
            <!--用人部门意见：-->
            集团HR意见：
        </td>
    </tr>
    <form class="layui-form" id="edForm">
        <tr>
            <td colspan="2">
                <div class="layui-inline">
                    <select name="edOpinion" id="edOpinion" lay-verify="required" #if(entryInfoStep2!=currentStepAlias?? || !isHandle) disabled #end >
                        <option value="">请选择</option>
                        <option value="1" #if('1'==entryInfo.edOpinion??) selected #end>拒绝</option>
                        <option value="2" #if('2'==entryInfo.edOpinion??) selected #end>再沟通</option>
                        <option value="3" #if('3'==entryInfo.edOpinion??) selected #end>录用</option>
                    </select>
                </div>
            </td>
            <td colspan="2">
                面试人：
                <div class="layui-inline">
                    <select name="edInterviewers" id="edInterviewers" lay-verify="required"  lay-search #if(entryInfoStep2!=currentStepAlias?? || !isHandle) disabled #end>
                        <option value="">请选择</option>
                        #for(user : userList)
                        <option value="#(user.id??)" #if(user.id==entryInfo.edInterviewers??) selected #end>#(user.name)(#(user.userName))</option>
                        #end
                    </select>
                </div>
            </td>
            <td colspan="2">
                日期：
                <div class="layui-input-inline">
                    <input type="text" id="edDate" name="edDate"  placeholder="" value="#date(entryInfo.edDate??,'yyyy-MM-dd')" #if(entryInfoStep2!=currentStepAlias?? || !isHandle) disabled #end autocomplete="off" class="layui-input">
                </div>
            </td>
            <td colspan="2">
                #if(entryInfoStep2==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" lay-filter="saveEdBtn">保&nbsp;&nbsp;存</button>
                #end
            </td>
        </tr>
    </form>
    #end

    <!--#if(stepCode??0>=3)
    <tr>
        <td colspan="8">
            总经理意见：
        </td>
    </tr>
    <form class="layui-form" id="gmForm">
        <tr>
            <td colspan="2">
                <div class="layui-input-inline">
                    <div class="layui-inline">
                        <select name="gmOpinion" id="gmOpinion" lay-verify="required" #if(entryInfoStep3!=currentStepAlias?? || !isHandle) disabled #end >
                            <option value="">请选择</option>
                            <option value="1" #if('1'==entryInfo.gmOpinion??) selected #end>拒绝</option>
                            <option value="2" #if('2'==entryInfo.gmOpinion??) selected #end>再沟通</option>
                            <option value="3" #if('3'==entryInfo.gmOpinion??) selected #end>录用</option>
                        </select>
                    </div>
                </div>
            </td>
            <td colspan="2">
                面试人：
                <div class="layui-inline">
                    <select name="gmInterviewers" id="gmInterviewers" lay-verify="required"  lay-search #if(entryInfoStep3!=currentStepAlias?? || !isHandle) disabled #end>
                    <option value="">请选择</option>
                    #for(user : userList)
                    <option value="#(user.id??)" #if(user.id==entryInfo.gmInterviewers??) selected #end>#(user.name)(#(user.userName))</option>
                    #end
                    </select>
                </div>
            </td>
            <td colspan="2">
                日期：
                <div class="layui-input-inline">
                    <input type="text" id="gmDate" name="gmDate"  placeholder="" value="#date(entryInfo.gmDate??,'yyyy-MM-dd')" #if(entryInfoStep3!=currentStepAlias?? || !isHandle) disabled #end autocomplete="off" class="layui-input">
                </div>
            </td>
            <td colspan="2">
                #if(entryInfoStep3==currentStepAlias?? && isHandle)
                <button class="layui-btn" lay-submit="" lay-filter="saveGmBtn">保&nbsp;&nbsp;存</button>
                #end
            </td>
        </tr>
    </form>
    #end-->
    </tbody>
</table>
#end












