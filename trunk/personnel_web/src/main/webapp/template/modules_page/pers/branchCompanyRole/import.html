#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end
#define content()

<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left:5px;" id="positionForm">
    <input type="hidden" name="id" value="#(model.id??)"/>
    <input type="hidden" id="orgId" name="orgId"  class="layui-input" lay-verify="required" value="#(org.id??)" placeholder="请输入职位名称">
    <div class="layui-form-item">
        <label class="layui-form-label" ><span style="color: red"></span>所属分公司</label>
        <div class="layui-input-block" >
            <input type="text" name="" readonly class="layui-input" lay-verify="required" value="#(org.orgName??)" placeholder="">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" ><span style="color: red">*</span>所属月份</label>
        <div class="layui-input-block" >
            <input type="text" id="yearMonth" name="yearMonth"  class="layui-input" lay-verify="required" value="" placeholder="">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">文件</label>
        <div class="layui-input-block">
            <div class="layui-upload-drag" id="test10">
                <i class="layui-icon"></i>
                <p>点击上传，或将文件拖拽到此处</p>
                <div class="layui-hide" id="uploadDemoView">
                    <hr>
                    <img src="" alt="上传成功后渲染" style="max-width: 196px">
                </div>
            </div>
        </div>
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" type="button" id="saveBtn" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery','laydate','upload', 'element'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate,upload=layui.upload,element=layui.element, $ = layui.$;

        laydate.render({
            elem: '#yearMonth'
            ,type: 'month'
            ,format: 'yyyy-MM'
            ,trigger:'click'
            ,value:new Date()
        });




        var uploadInst = upload.render({
            elem: '#test10'
            ,url: '#(ctxPath)/pers/branchCompanyRole/importFile'
            ,auto:false
            ,accept:'file'
            ,bindAction:'#saveBtn'
            ,before: function(obj){
                layer.load();
                this.data={'orgId':$("#orgId").val(),'yearMonth':$("#yearMonth").val()}
            }
            ,done: function(res){
                //上传失败
                if(res.state=='ok'){
                    layer.msg('操作成功', {icon: 1, offset: 'auto'});
                }else{
                    layer.closeAll('loading');
                    layer.msg('上传失败', {icon: 2, offset: 'auto'})
                }
                layer.closeAll('loading');
                //上传成功，返回的路径：res.filePath

            }
            ,error: function(){
                layer.closeAll('loading');
                //上传失败
                layer.msg('上传失败', {icon: 2, offset: 'auto'})
            }
        });


        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/branchCompanyRole/importFile";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#positionForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //parent.table.reload('positionTable',{'where':{'orgId':$("#orgId").val()}});
                        pop_close();
                        parent.positionTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end

