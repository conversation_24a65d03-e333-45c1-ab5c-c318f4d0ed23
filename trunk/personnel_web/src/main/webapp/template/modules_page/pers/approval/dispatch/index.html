#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .layui-table-cell{
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        height: auto !important;
        word-break: break-all;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>
            <!--<label class="layui-form-label">转正时间</label>
            <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
            </div>-->
            <!--<label class="layui-form-label">状态</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="state" name="state" lay-filter="state">
                    <option value="5">全部</option>
                    <option value="0">待处理</option>
                    <option value="1">提交</option>
                    <option value="2">批准</option>
                    <option value="3">完成</option>
                    <option value="4">失败</option>
                </select>
            </div>-->
            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:dispatch:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end

            #shiroHasPermission("emp:dispatch:exportRecordBtn")
            <button class="layui-btn" id="exportBtn" type="button">导出</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:dispatch:edi2tBtn")
    #[[
    {{#if(d.status=='3'){}}

    <!--<a class="layui-btn layui-btn-xs" lay-event="edit2">编辑</a>-->

    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:exportBtn")
    #[[
    {{#if(d.status=='3'){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="export">导出</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:dispatch:delBtn")
    #[[
    {{#if(d.status=='3'){}}

    <a class="layui-btn layui-btn-xs layui-btn-danger " lay-event="del">作废</a>

    {{#}}}
    ]]#
    #end

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });

        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }
        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"name":$("#name").val(),'date':'','deptId':id});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/dispatchForm';
            pop_show("添加外派申请",url,'100%','100%');
        });

        $("#exportBtn").on('click',function () {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var url='#(ctxPath)/pers/approval/dispatchExport?name='+$("#name").val()+"&deptId="+id;
            window.location.href=url;
            setTimeout(function (){
                layer.closeAll('loading');
            },10000);
        })

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/findEmployeeDispatchPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                        return d.fullName+"("+d.workNum+")";
                    }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true}
                    ,{field:'startTime', title: '派驻单位',width: 450,align: 'center', unresize: true,templet:function (d){
                            if(d.isNew){
                                let str='';
                                $.each(d.dateList,function (index,item) {
                                    str+=`<p>`+item.dispatchDeptName+`：`+item.startTime+'至'+item.endTime+`</p>`
                                })
                                return str;
                            }else{
                                var str="";
                                if(dateFormat(d.startTime,'HH:mm:ss')=='00:00:00'){
                                    str="上午";
                                }else if(dateFormat(d.startTime,'HH:mm:ss')=='12:00:00'){
                                    str="下午";
                                }
                                var str2="";
                                if(dateFormat(d.endTime,'HH:mm:ss')=='12:00:00'){
                                    str2="上午";
                                }else if(dateFormat(d.endTime,'HH:mm:ss')=='23:59:59'){
                                    str2="下午";
                                }
                                return d.dispatchDeptName+"："+dateFormat(d.startTime,'yyyy-MM-dd')+" "+str+"至"+dateFormat(d.endTime,'yyyy-MM-dd')+" "+str2;
                            }

                        }}

                    ,{field:'days', title: '外派天数',width: 90, align: 'center', unresize: true}
                    ,{field:'remark', title: '外派事由',width: 120, align: 'center', unresize: true}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center', unresize: true,width:120,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge">未提交</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">审核通过</span>';
                            }else if(d.status=='4'){
                                return '驳回';
                            }else if(d.status=='5'){
                                return '中止';
                            }else if(d.status=='6'){
                                return '撤销';
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/dispatchForm?id='+obj.data.id;
                    pop_show("处理流程",url,'100%','100%');
                }else if(obj.event==='edit2'){
                    var url='#(ctxPath)/pers/approval/dispatchEditForm?id='+obj.data.id;
                    pop_show("处理流程",url,'100%','100%');
                }else if(obj.event=='export'){
                    window.location.href='#(ctxPath)/pers/approval/exportDispatchExcel?id='+obj.data.id;
                }else if(obj.event=='del'){
                    layer.confirm('你确定要作废吗？', {icon: 3, title:'提示'}, function(index) {
                        $.ajax({
                            type:'post',
                            data: {"id":obj.data.id},
                            url: '#(ctxPath)/pers/approval/delEmployeeDispatchApply',
                            contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                            dataType: 'json',
                            timeout: 30000,
                            beforeSend: function (XMLHttpRequest) {
                                layer.load();
                            },
                            success: function (res) {
                                if (res.state == 'ok') {
                                    layer.msg('操作成功', {icon: 1, offset: 'auto'});
                                    reloadTable();
                                    pop_close();
                                } else {
                                    layer.msg(res.msg, {icon: 2, offset: 'auto'});
                                }
                            }
                            ,complete :function(XMLHttpRequest, TS){
                                layer.closeAll('loading');
                            }
                        });
                        layer.close(index);
                    });

                }
            })


        };


    });
</script>
#end