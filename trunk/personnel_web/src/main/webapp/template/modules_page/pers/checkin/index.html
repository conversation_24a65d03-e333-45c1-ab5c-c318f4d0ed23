#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
#end

#define content()

<input id="employeeId" value="#(employeeId??)" type="hidden">
<div class="layui-row">

    <div class="layui-col-md3" style="margin: 10px auto;">
        <div class="layui-inline" id="calendar">

        </div>
    </div>
    <div class="layui-col-md9">
        <table id="employeeCheckinTable" lay-filter="employeeCheckinTable"></table>
    </div>
</div>

#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">

        #shiroHasPermission("emp:archives:changeDepartment:edit")
        <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
        #end
    </div>
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeCheckinTable'
            ,laydate=layui.laydate
        ;

        //直接嵌套显示
        laydate.render({
            elem: '#calendar'
            ,position: 'static'
            ,done:function (value,date) {
                loadTable({'checkinTime':value,'empId':$("#employeeId").val()});
            }
        });



        loadTable=function (data) {
            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/employeeCheckin/pageTable'
                ,height: vipTable.getFullHeight()*0.8    //容器高度
                ,where : data//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {field:'groupname', title:'打卡规则名称', width:120, align:'center',unresize:true}
                    ,{field:'checkinTime', title:'打卡时间', width:180, align:'center',unresize:true,templet:"<div>{{ dateFormat(d.checkinTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'checkinType', title:'打卡类型', width:120, align:'center',unresize:true}
                    ,{field:'exceptionType', title:'异常类型', align:'center',unresize:true}
                    ,{field:'notes', title:'打卡备注', align:'center',unresize:true}
                    /*,{field:'locationTitle', title:'打卡地址', align:'center',unresize:true}*/
                ]]
                ,page : true
                ,done:function () {
                }
            });
        }

        loadTable({'empId':$("#employeeId").val()});

    });
</script>
#end