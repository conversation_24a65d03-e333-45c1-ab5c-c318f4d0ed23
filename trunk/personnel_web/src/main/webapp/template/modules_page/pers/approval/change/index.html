#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">原组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">新组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="newDeptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:dispatch:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            <button class="layui-btn" id="exportBtn" type="button">导出</button>
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("emp:changeApply:exportBtn")

    #[[
    {{#if(d.taskId!=undefined && d.stepts.length>=1){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="export">导出</a>
    {{#}}}
    ]]#
    #end
</script>
<script>

    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }
        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var newId="";
            $.each(newDeptSelect.getValue(),function (index,item) {
                newId=item.id;
            });
            msgLoad({"name":$("#name").val(),'date':'','deptId':id,'newDeptId':newId});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/changeForm';
            pop_show("添加异动申请",url,1300,700);
        });

        $("#exportBtn").on('click',function () {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var newId="";
            $.each(newDeptSelect.getValue(),function (index,item) {
                newId=item.id;
            });
            window.location.href='#(ctxPath)/pers/approval/exportEmployeeChangeApply?name='+$("#name").val()+'&deptId='+id+'&newDeptId='+newId;
            setInterval(function () {
                layer.closeAll('loading');
            },5000);
        })

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/findEmployeeChangeApplyPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                        return d.fullName+"("+d.workNum+")";
                    }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true}
                    ,{field:'newDeptName', title: '异动后单位', align: 'center', unresize: true}
                    ,{field:'newPositionName', title: '异动后职位', align: 'center', unresize: true}
                    ,{field:'newPositionName', title: '异动时间长短', align: 'center', unresize: true,templet:function (d){
                            var str="";
                            if(d.changeDateType=='1'){
                                str="短期";
                            }else if(d.changeDateType=='2'){
                                str="长期";
                            }
                            return str;
                        }}
                    ,{field:'startTime', title: '开始时间',align: 'center', unresize: true,width:130,templet:function (d){
                            var str="";
                            if(dateFormat(d.startTime,'HH:mm:ss')=='00:00:00'){
                                str="上午";
                            }else if(dateFormat(d.startTime,'HH:mm:ss')=='12:00:00'){
                                str="下午";
                            }
                            return dateFormat(d.startTime,'yyyy-MM-dd')+" "+str;
                        }}
                    ,{field:'endTime', title: '结束时间',align: 'center', unresize: true,width:130,templet:function (d){
                            var str="";
                            if(dateFormat(d.endTime,'HH:mm:ss')=='12:00:00'){
                                str="上午";
                            }else if(dateFormat(d.endTime,'HH:mm:ss')=='23:59:59'){
                                str="下午";
                            }
                            return dateFormat(d.endTime,'yyyy-MM-dd')+" "+str;
                        }}
                    ,{field:'changeDays', title: '异动天数',width: 90, align: 'center', unresize: true}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center', unresize: true,width:120,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge">未提交</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">审核通过</span>';
                            }else if(d.status=='4'){
                                return '驳回';
                            }else if(d.status=='5'){
                                return '中止';
                            }else if(d.status=='6'){
                                return '撤销';
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 110, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/changeForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }else if(obj.event=='export'){
                    window.location.href='#(ctxPath)/pers/approval/exportChangeApply2?id='+obj.data.id;
                }
            })


        };


        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        });

        var newDeptSelect = xmSelect.render({
            el: '#newDeptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        });

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            newDeptSelect.update({
                data:res
            })
        });
    });
</script>
#end