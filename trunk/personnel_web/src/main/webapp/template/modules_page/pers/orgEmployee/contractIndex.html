#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        positionLoad({'empId':$("#empId").val()});


        function positionLoad(data){
            table.render({
                id : 'contractTable'
                ,elem : '#contractTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/persOrgEmployee/contractPage'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'contractStartDate', title: '合同开始时间', align: 'center', unresize: true,templet:"<div>{{dateFormat(d.contractStartDate,'yyyy-MM-dd')}}</div>"}
                    ,{field:'contractEndDate', title: '合同到期时间', align: 'center', unresize: true,templet: function (d) {
                            if(d.dateType=='1'){
                                return dateFormat(d.contractEndDate,'yyyy-MM-dd')
                            }else if(d.dateType=='2'){
                                return '长期'
                            }
                        }}
                    ,{field:'contractType', title: '合同类型', align: 'center', unresize: true,templet:function (d) {
                            if(d.contractType=='1'){
                                return '劳动合同';
                            }else if(d.contractType=='2'){
                                return '劳务合同';
                            }else if(d.contractType=='3'){
                                return '临时合同';
                            }else if(d.contractType=='4'){
                                return '待补';
                            }else if(d.contractType=='5'){
                                return '实习协议';
                            }else if(d.contractType=='6'){
                                return '临时工合同';
                            }
                        }}
                    ,{field:'contractAddr', title: '合同归属地址', align: 'center', unresize: true}
                    ,{field:'description', title: '备注', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        contractTableReload=function(){
            table.reload('contractTable',{'where':{'empId':$("#empId").val()}});
        }

        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/persOrgEmployee/contractForm?employeeId="+$("#empId").val() ;
            pop_show("新增合同",url,1100,600);
        });

        table.on('tool(contractTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrgEmployee/delContract',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                contractTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/persOrgEmployee/contractForm?id=" + obj.data.id+"&employeeId="+$("#empId").val();
                pop_show("编辑合同",url,1100,600);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("emp:archives:contract:edit")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("emp:archives:contract:del")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        #shiroHasPermission("emp:archives:contract:add")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <input id="empId" value="#(employeeId??)" type="hidden">
    <table class="layui-table" id="contractTable" lay-filter="contractTable"></table>

</div>
#end