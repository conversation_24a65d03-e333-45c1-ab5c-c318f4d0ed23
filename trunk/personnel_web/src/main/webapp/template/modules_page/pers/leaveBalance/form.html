#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <input type="hidden" id="empId" value="#(empId??)">
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>

</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="add">增加</a>
    <a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="sub">扣除</a>
    <a class="layui-btn layui-btn-xs" lay-event="viewRecord">查看记录</a>
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;


        msgLoad({"empId":$("#empId").val()});
        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/employeeLeaveBalance/getEmpLeaveBalanceList'
                ,cellMinWidth: 80
                ,cols: [[
                    /*{field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return d.fullName+"("+d.workNum+")";
                        }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }},*/
                    {field:'leaveTypeName', title: '类型', align: 'center', unresize: true}
                    ,{field:'remainingStr', title: '剩余', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 220, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : false
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='';
                    if(obj.data.id==undefined){
                        url='#(ctxPath)/employeeLeaveBalance/editForm?leaveId='+''+"&leaveType="+obj.data.leave_type+"&empId="+$("#empId").val()+"&type=3";
                    }else{
                        url='#(ctxPath)/employeeLeaveBalance/editForm?leaveId='+obj.data.id+"&leaveType="+obj.data.leave_type+"&empId="+obj.data.emp_id+"&type=3";
                    }
                    pop_show("编辑["+obj.data.leaveTypeName+"]",url,500,500);
                }else if(obj.event==='add'){
                    var url='';
                    if(obj.data.id==undefined){
                        url='#(ctxPath)/employeeLeaveBalance/editForm?leaveId='+''+"&leaveType="+obj.data.leave_type+"&empId="+$("#empId").val()+"&type=1";
                    }else{
                        url='#(ctxPath)/employeeLeaveBalance/editForm?leaveId='+obj.data.id+"&leaveType="+obj.data.leave_type+"&empId="+obj.data.emp_id+"&type=1";
                    }
                    pop_show("增加["+obj.data.leaveTypeName+"]",url,500,500);
                }else if(obj.event==='sub'){
                    var url='';
                    if(obj.data.id==undefined){
                        url='#(ctxPath)/employeeLeaveBalance/editForm?leaveId='+''+"&leaveType="+obj.data.leave_type+"&empId="+$("#empId").val()+"&type=2";
                    }else{
                        url='#(ctxPath)/employeeLeaveBalance/editForm?leaveId='+obj.data.id+"&leaveType="+obj.data.leave_type+"&empId="+obj.data.emp_id+"&type=2";
                    }
                    pop_show("扣除["+obj.data.leaveTypeName+"]",url,500,500);
                }else if(obj.event==='viewRecord'){
                    var url='#(ctxPath)/employeeLeaveBalance/detailIndex?empId='+$("#empId").val()+"&leaveType="+obj.data.leave_type;
                    pop_show("查看["+obj.data.leaveTypeName+"]变动记录",url,1300,700);
                }
            })


        };

        reloadTab=function () {
            msgLoad({"empId":$("#empId").val()});
        }

    });
</script>
#end