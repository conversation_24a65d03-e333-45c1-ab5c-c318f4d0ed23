#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()车型管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;">状态:</label>
            <div class="layui-input-inline">
                <select name="remindStatus" id="remindStatus">
                    <option value="">全部</option>
                    <option value="1">未处理</option>
                    <option value="2">处理中</option>
                    <option value="3">已处理</option>
                </select>
            </div>
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="carTypeTable" lay-filter="carTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'carTypeTable',
            elem: '#carTypeTable'
            ,url : '#(ctxPath)/employeeRemind/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {field: 'orgName', title: '部门',unresize:true}
                ,{field: 'fullName', title: '姓名',unresize:true}
                ,{field: 'remindType', title: '类型',unresize:true, templet:'#dictTpl("remind_type", "remindType")'}
                ,{field: 'remindDate', title: '提醒时间',unresize:true,templet:"<div>{{dateFormat(d.remindDate,'yyyy-MM-dd')}}</div>"}
                ,{field: 'remindStatus', title: '状态',unresize:true,templet:function(d){
                        if(d.remindStatus=='1'){
                            return '未处理';
                        }else if(d.remindStatus=='2'){
                            return '处理中';
                        }else if(d.remindStatus=='3'){
                            return '已处理';
                        }
                    }}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(carTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/employeeRemind/form?id=" + obj.data.id ;
                layerShow("编辑提醒",url,550,600);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/employeeRemind/saveRemind",
                        type:'post',
                        data:{"id":obj.data.id,"delFlag":"1"},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                carTypeTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });



        carTypeTableReload=function(){
            var remindStatus=$("#remindStatus").val();
            table.reload('carTypeTable',{'where':{'remindStatus':remindStatus}});
        }

        $("#search").on('click',function () {
            carTypeTableReload();
        });

        $("#add").on('click',function () {

            layerShow("添加类型",url,550,600);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("mainRoleManage:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
</script>
#end