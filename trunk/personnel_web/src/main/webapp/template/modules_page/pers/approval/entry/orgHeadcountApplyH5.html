#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-form-label{
        width:150px;
    }

    .layui-table td,.layui-table th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }


    .layui-table{
        color: #000000;
    }
    .layui-btn-disabled{
        background-color: #e6e6e6;
        color: #000000;
    }

    .layui-disabled, .layui-disabled:hover{
        color: #000000!important;
        cursor: not-allowed!important;
    }
    textarea:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }
    input:disabled {
        color: #000000;
        cursor: default;
        background-color: -internal-light-dark(rgba(239, 239, 239, 0.3), rgba(59, 59, 59, 0.3));
        border-color: rgba(118, 118, 118, 0.3);
    }

</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });




        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(headcountApply!=null)
            deptSelect.setValue(["#(headcountApply.deptId??)"]);
            #end
        });


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            deptId=deptSelect.getValue()[0].id;
            if(deptId==null || deptId==''){
                layer.msg('请选择组织', {icon: 2, offset: 'auto'});
                return false;
            }

            var remark=$("#remark").val();
            if(remark=='' || remark==undefined){
                layer.msg('备注不能为空', {icon: 2, offset: 'auto'});
                return false;
            }

            var data=$("#noticeForm").serialize()+"&deptId="+deptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/persOrg/orgHeadcountApplySave',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        form.on('select(destinationType)',function (obj) {
            if(obj.value=='1'){
                $("#deptDiv").css("display","block");
                $("#deptDiv2").css("display","none");
            }else{
                $("#deptDiv").css("display","none");
                $("#deptDiv2").css("display","block");
            }

        })

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            deptId=deptSelect.getValue()[0].id;
            if(deptId==null || deptId==''){
                layer.msg('请选择组织', {icon: 2, offset: 'auto'});
                return false;
            }

            var remark=$("#remark").val();
            if(remark=='' || remark==undefined){
                layer.msg('备注不能为空', {icon: 2, offset: 'auto'});
                return false;
            }


            var data=$("#noticeForm").serialize()+"&saveType=2&deptId="+deptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/persOrg/orgHeadcountApplySave',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });


        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }

    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div class="layui-col-md12 layui-form" id="content" style="padding-right: 10px;">
            <div class="layui-collapse" >


                <div class="layui-row" style="padding: 10px 20px;">


                    <!--<div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>组织</label>
                        <div class="layui-input-inline">
                            <input type="text" name="headcount" id="" style="word-break:break-all" disabled readonly required  value="#(record.deptName??)#(record.deptName??)"  placeholder="" autocomplete="off" class="layui-input">

                        </div>
                    </div>-->

                    #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
                    <fieldset class="layui-elem-field layui-field-title" style="display:block;padding-left: 20px;">
                        <legend>流程提交人信息</legend>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交人姓名</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交人部门</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交人职位</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">提交时间</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label" style="">任务编号</label>
                            <div class="layui-input-block">
                                <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </fieldset>
                    #end
                    <fieldset class="layui-elem-field layui-field-title" style="display:block;">
                        <legend>组织编制增减单信息</legend>
                    </fieldset>

                    <div class="layui-form-item layui-form-text" >
                        <label class="layui-form-label"><font color="red">*</font>组织</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="" style="min-height: 49px;" placeholder="" lay-verify="" disabled readonly  class="layui-textarea">#(record.deptName??)</textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>类型</label>
                        <div class="layui-input-inline">
                            <select id="type" name="type" lay-filter="type" lay-verify="required" disabled readonly>
                                <option value="1" #if(record.type??=='1') selected #end >增加</option>
                                <option value="2" #if(record.type??=='2') selected #end >减少</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>人数</label>
                        <div class="layui-input-inline">
                            <input type="text" name="headcount" id="headcount" disabled readonly required value="#(record.headcount??)"  lay-verify="required|number" placeholder="请输入人数" autocomplete="off" class="layui-input">

                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text" style="margin-bottom: 60px;">
                        <label class="layui-form-label"><font color="red">*</font>备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" disabled readonly  class="layui-textarea">#(record.remark??)</textarea>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </form>
</div>

</body>
#end