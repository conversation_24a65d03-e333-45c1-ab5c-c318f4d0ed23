#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
#end

#define content()
<div style="margin-top: 20px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <!--<label class="layui-form-label">姓名</label>
            <div class="layui-input-inline" style="float: left;" >
                <input type="text" name="employeeName" id="employeeName" value="" autocomplete="off" class="layui-input">
            </div>-->
            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">离职时间</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <button class="layui-btn" id="query" type="button" >查询</button>
            #shiroHasPermission("emp:quitReport:exportBtn")
            <button class="layui-btn" id="export" type="button" >导出</button>
            #end
        </div>
    </form>
    <div style="margin: 5px;">
        <table id="quitApprovalTable" lay-filter="quitApprovalTable"></table>
    </div>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    #shiroHasPermission("pers:employeeEntry:editBtn")
    #[[
    {{#if(d.isSaveHandle || d.taskId==undefined){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("pers:employeeEntry:editBtn")
    #[[
    {{#if(!d.isSaveHandle && d.taskId!=undefined){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end


</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/entryInfoForm';
            pop_show("添加入职信息",url,'100%','100%');
        });

        $("#reportForm").on('click',function (){
            var url='#(ctxPath)/pers/approval/entryInfoReportForm';
            pop_show("添加入职信息",url,'100%','100%');
        })

        $("#export").on('click',function (){
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var url='#(ctxPath)/pers/approval/quitReportExport';
            window.location.href=url+"?date="+$("#date").val()+"&deptId="+id;
        })

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        $("#query").on('click',function (obj) {
            reloadTable();
        })

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"employeeName":$("#employeeName").val(),'date':$("#date").val(),'deptId':id});
        }

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'quitApprovalTable'
                ,elem : '#quitApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/quitReportPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'deptName',title:'部门', align: 'center', unresize: true}
                    ,{field:'workNum',title:'工号',width: 150, align: 'center', unresize: true}
                    ,{field:'fullName',title:'姓名',width: 150, align: 'center', unresize: true}
                    ,{field:'positionName',title:'职位', align: 'center', unresize: true}
                    ,{field:'gender', title: '性别',width: 120, align: 'center', unresize: true,templet:function (d) {
                            if(d.sex==='male'){
                                return '男';
                            }else if(d.sex==='female'){
                                return '女';
                            }else{
                                return '未知';
                            }
                        }}
                    ,{field:'', title: '离职类型',align: 'center',width: 130, unresize: true,templet:function (d) {
                            if(d.quit_type=='dismiss'){
                                return '辞退';
                            }else if(d.quit_type=='autonomy'){
                                return '自离'
                            }else if(d.quit_type=='expel'){
                                return '开除'
                            }else if(d.quit_type=='other'){
                                return '其他'
                            }else if(d.quit_type=='auto_quit'){
                                return '自动离职'
                            }else if(d.quit_type=='voluntarily_quit'){
                                return '个人离职';
                            }else if(d.quit_type=='resign'){
                                return '辞职';
                            }
                        }}
                    ,{field:'entry_time',title:'入职时间', align: 'center', unresize: true,templet:"<div>{{dateFormat(d.entry_time,'yyyy-MM-dd')}}</div>"}
                    ,{field:'date',title:'离职时间', align: 'center', unresize: true,templet:"<div>{{dateFormat(d.estimate_quit_date,'yyyy-MM-dd')}}</div>"}
                    ,{field:'reason',title:'离职原因', align: 'center', unresize: true}
                    ,{field:'quit_use_annual_leave_days',title:'已休年休假天数', align: 'center', unresize: true}
                ]]
                ,page : true
                ,done:function () {
                    layer.closeAll('loading');
                }
            });



        };


    });
</script>
#end