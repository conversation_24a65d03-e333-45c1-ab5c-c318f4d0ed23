#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()组织架构管理首页#end

#define css()
<style>
    html,body{height:90%}
</style>
#end

#define content()
<div class="my-btn-box" style="height: 100%">
    <div class="layui-row">
        <span class="fl"></span>
        <span class="fr" style="padding-right: 20px;">

			<div class="layui-input-inline">
                <label class="layui-form-label">月份</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>

	    	<div class="layui-inline">
	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
	        			<a id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
                        #shiroHasPermission("emp:liabilityInsurance:exportSummaryBtn")
                        <a id="exportBtn" class="layui-btn btn-add btn-default">导出</a>
                        #end
	    			</div>
	    		</div>
	    	</div>
	    </span>
    </div>
    <div class="layui-row" style="height: 90%">
        <table id="orgTreeGrid" lay-filter="orgTreeGrid"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['treeGrid','form','vip_table','laydate'],function(){

        // 操作对象
        var layer = layui.layer
            ,form = layui.form
            ,treeGrid = layui.treeGrid
            ,vipTable = layui.vip_table
            ,$ = layui.jquery
            ,tableId = 'orgTreeGrid'
            ,sortSaveBtn = ''
			,laydate=layui.laydate
        ;
		laydate.render({
			elem: '#date'
			,trigger: 'click'
			,type: 'month'
			,value:new Date()
		});

		layer.load();

        //初始化表格
        var ptable=treeGrid.render({
            id: tableId
            , elem: '#'+tableId
            , url: '#(ctxPath)/providentFund/liabilityInsuranceSummaryList'
            , method: 'post'
            , idField: 'id'//必須字段
            , treeId: 'id'//树形id字段名称
            , treeUpId: 'pId'//树形父id字段名称
            , treeShowName: 'name'//以树形式显示的字段
            , isOpenDefault: true//节点默认是展开还是折叠【默认展开】
            //,height: 'full-110'
            , cols: [[
                {field:'name', title:'名称', unresize:true}
				,{field:'add', title:'增数', unresize:true, templet: function(d){
					return d.record.add;
				}}
				,{field:'cont', title:'续数', unresize:true, templet: function(d){
						return d.record.cont;
					}}
				,{field:'del', title:'删数', unresize:true, templet: function(d){
						return d.record.del;
					}}
				,{field:'abandon', title:'弃数', unresize:true, templet: function(d){
						return d.record.abandon;
					}}
            ]],done:function (a,b,c) {
                //console.log(treeGrid.checkStatus('EC835ED4-FE0D-418F-BCDA-C7F0AF90906E'));

                var ids=["EC835ED4-FE0D-418F-BCDA-C7F0AF90906E","3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7","83900B0C-3AB6-49B8-AF7B-B557D668078F"
                ,"DC459824-3D35-41E1-B054-3F4FCBBC6B45","CFDC0074-CB8E-46DA-8D35-7013585E4B41","FD0B717B-4712-406C-B14A-434681834055","AABAE85D-D70B-42A3-AFDF-CBCAF53F7EB6"
                ,"84A83CE1-C563-48D1-9F4C-58B1A0C58F7A","DBE7C9DE-D76F-43F9-B2EF-553A7D5F4BF9","5FBE4645-B644-4403-BE85-431E80D59F47","DCC6E73F-19E5-43FE-BE3C-5B029A545266","5E532E9E-369F-4EA7-838B-FFFAD9FBFE02","B188E565-72A2-4CCF-821C-057811CEEB4D","89FE94C9-50E2-4727-94BF-E0FB6A72D2AD","04F4194A-127D-4D64-A42F-1607A11E5A08"
                ,"AD41DBBA-457A-471F-A892-CBDED927A98D","64BFB385-CFC3-4C8E-9A60-2A30F9C4ABC0","0C1B552C-A293-4A47-A5A9-D28EAB16C4A6","41F0E80A-087E-4D2C-872E-893CA7C86ED3"]



                $.each(a.data,function (index,item) {
                    $(".layui-table-main").find('tr').eq(index).attr("data-id",item.id);
                });

                $.each(ids,function (index,item) {
                    $(".layui-table-main").find('tr[data-id="'+item+'"]')
                                        .find('td[data-field="name"]').find('i[class="layui-icon layui-tree-head"]').click();
                })

                layer.closeAll("loading");
            }
        });


        //ptable.treeNodeOpen('EC835ED4-FE0D-418F-BCDA-C7F0AF90906E',false);

        //表格事件绑定
        treeGrid.on('tool('+tableId+')',function (obj) {
            if(obj.event==="edit"){//编辑按钮事件
                pop_show('编辑','#(ctxPath)/persOrg/edit?id='+obj.data.id,'','');
            }else if(obj.event==="reviewer"){//审核人按钮事件
                pop_show('审核人','#(ctxPath)/persOrg/reviewerIndex?id='+obj.data.id,'','');
            }else if(obj.event==="del"){//作废按钮事件
                layer.confirm('你确定作废数据吗？', {icon: 3, title:'提示'}, function(index) {
                    //作废操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrg/del',
                        data: {id:obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success : function(data){
                            tableGridReload();
                        },
                        complete : function() {

                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event==='disable'){
                layer.confirm('你确定禁用吗？', {icon: 3, title:'提示'}, function(index) {
                    //作废操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrg/disable?id='+obj.data.id,
                        data: {id:obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success : function(data){
                            tableGridReload();
                        },
                        complete : function() {

                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event==='enable'){
                layer.confirm('你确定启用吗？', {icon: 3, title:'提示'}, function(index) {
                    //作废操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrg/enable?id='+obj.data.id,
                        data: {id:obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success : function(data){
                            tableGridReload();
                        },
                        complete : function() {

                        }
                    });
                    layer.close(index);
                });
            }
        });
        //表格重载
        tableGridReload = function () {
            layer.load();
            treeGrid.reload(tableId, {'where':{"yearMonth":$("#date").val()}});
        }
        //添加按钮点击事件
        $('#addBtn').on('click', function() {
            pop_show('添加','#(ctxPath)/persOrg/add?formType=dept','','');
        });
        // 刷新
        $('#refreshBtn').on('click', function () {
            tableGridReload();
        });
        //保存排序按钮点击事件
        $('#exportBtn').on('click', function() {
            layer.load();
            var exportIds=[];
            $.each($(".layui-table-main").find('tr'),function (index,item) {
                if($(item).attr("style")==undefined
                    && "54325705-FF63-43DB-9723-FA31E94AF8E3"!=$(item).attr("data-id") && "B97B6F94-140C-4C86-8BC1-6A2BCC86CA3A"!=$(item).attr("data-id")){
                    exportIds.push($(item).attr("data-id"));
                }
            });
            var url='#(ctxPath)/providentFund/exportLiabilityInsuranceSummaryList';
            window.location.href='#(ctxPath)/providentFund/exportLiabilityInsuranceSummaryList?ids='+JSON.stringify(exportIds)+"&yearMonth="+$("#date").val();
            setTimeout(function(){
                layer.closeAll("loading");
            }, 3000);
            /*util.sendAjax ({
                type: 'POST',
                url: url,
                data: {"ids":JSON.stringify(exportIds),"yearMonth":$("#date").val()},
                notice: true,
                loadFlag: true,
                success : function(rep){
                    /!*if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }*!/
                },
                complete : function() {
                }
            });*/

            /*$.ajax({
                type: "POST",
                url: url,
                async: false,
                data: {"ids":JSON.stringify(exportIds),"yearMonth":$("#date").val()},
                success: function(response) {


                }
            });*/

        });

    });
</script>
#end