#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构员工编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<form class="layui-form layui-form-pane" action="" id="employeeForm" style="margin-left:35px;">
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-inline">
                <input type="radio" name="type" value="0" title="奖励" checked>
                <input type="radio" name="type" value="1" title="处罚" >
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <div class="layui-inline">
            <label class="layui-form-label"><font color="red">*</font>奖罚时间</label>
            <div class="layui-input-inline" >
                <input type="text" class="layui-input" id="happenDate" name="happenDate" placeholder="请选择奖罚时间" lay-verify="required" value="#date(record.happenDate??,'yyyy-MM-dd')" autocomplete="off">
            </div>
        </div>
    </div>
    <div class="layui-form-item layui-form-text">
        <label class="layui-form-label">内容</label>
        <div class="layui-input-block">
            <textarea name="remark" placeholder="请输入内容" class="layui-textarea">#(record.remark??)</textarea>
        </div>
    </div>

    <div style="margin-bottom:60px;"></div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id" id="id" value="#(record.Id??)">
            <input type="hidden" name="employeeId" id="employeeId" value="#(employeeId??)">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            <button class="layui-btn layui-btn-primary" type="reset" id="resetBtn" style="display: none;">重置</button>
        </div>
    </div>
</form>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use([ 'form', 'laydate' ], function() {
        var form = layui.form
            , layer = layui.layer
            , laydate = layui.laydate
            , $ = layui.$
        ;

        //出生日期渲染
        laydate.render({
            elem: '#happenDate'
            , trigger: 'click'
        });




        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/persOrgEmployee/saveRewardPunish',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){

                        var id=$("#id").val();
                        if(id==='' || id===null){
                            parent.pageTableReload();
                            $("#resetBtn").click();
                        }else{
                            parent.pageTableReload();
                            pop_close();
                        }
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end