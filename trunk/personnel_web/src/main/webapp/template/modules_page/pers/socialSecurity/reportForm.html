#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">


            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">缴纳月份</label>
                <div class="layui-input-inline" style="float: left;margin-right: 0px;width: 120px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">购买单位</label>
                <div class="layui-input-inline" style="float: left;margin-right: 0px;" >
                    <select id="companyId">
                        <option value="">全部</option>
                        #for(company : companyList)
                        <option value="#(company.id??)">#(company.name??)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">类型</label>
                <div class="layui-input-inline" style="float: left;margin-right: 0px;" >
                    <div id="typeSelect" style="width: 250px;"></div>
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:socialSecurity:exportReportFormBtn")
            <button class="layui-btn" id="exportBtn" type="button" >导出</button>
            #end
        </div>
    </form>

    <div class="layui-row">
        <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    </div>

</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if( d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if( !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,type: 'month'
            ,value:new Date()
        });



        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        $("#exportBtn").on('click',function () {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });

            var typeSelectIds=[];
            var array=typeSelect.getValue();
            $.each(array,function (index,item) {
                typeSelectIds.push(item.value)
            })
            window.location.href='#(ctxPath)/providentFund/exportSocialSecurityReportFormPageList?companyId='+$("#companyId").val()
                +"&date="+$("#date").val()+"&deptId="+id+"&types="+JSON.stringify(typeSelectIds);
            setTimeout(function(){
                layer.closeAll("loading");
            }, 3000);
        })

        var typeSelect = xmSelect.render({
            el: '#typeSelect',
            language: 'zn',
            data: [
                {name: '增', value: "1",selected: true},
                {name: '续', value: "2",selected: true},
                {name: '删', value: "3",selected: true},
                {name: '弃', value: "4",selected: true}
            ]
        })


        $("#export").on('click',function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            window.location.href="#(ctxPath)/pers/approval/leaveRestExport?name="+$("#name").val()+"&date="+$("#date").val()+"&deptId="+id;
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,
                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });



        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });

            var typeSelectIds=[];
            var array=typeSelect.getValue();
            $.each(array,function (index,item) {
                typeSelectIds.push(item.value)
            })
            console.log({"companyId":$("#companyId").val(),'date':$("#date").val(),'deptId':id,'types':JSON.stringify(typeSelectIds)});
            msgLoad({"companyId":$("#companyId").val(),'date':$("#date").val(),'deptId':id,'types':JSON.stringify(typeSelectIds)});
        }

        reloadTable();




        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        updateTitle=function(title){
            $(".layui-layer-title").text(title)
        }

        function timeStamp2String(time){

            var datetime = new Date();

            datetime.setTime(time);

            var year = datetime.getFullYear();

            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;

            return year + "-" + month;
        }

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/providentFund/socialSecurityReportFormPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:80, title: '序号',unresize:true}
                    ,{field:'dept_name',width:300, title: '部门', align: 'center', unresize: true}
                    ,{field:'full_name', title: '姓名', align: 'center', unresize: true}
                    ,{field:'position_name', title: '职位', align: 'center', unresize: true}
                    ,{field:'company_name', title: '购买单位', align: 'center', unresize: true}
                    ,{field:'pay_ratio_name', title: '是否第一次购买', align: 'center', unresize: true,templet:function (d){
                            if(d.is_first=='1'){
                                return '是';
                            }else if(d.is_first=='0'){
                                return '否';
                            }else{
                                return '';
                            }
                    }}
                    ,{field:'', title:'户口类型', width:120, align:'center', templet:'#dictTpl("registered_type", "registered_type")'}
                    ,{field:'type', title: '类型',width: 120, align: 'center', unresize: true,templet:function (d){
                            if(d.type=='1'){
                                return '<span class="layui-badge" style="background-color:#009688;">增</span>';
                            }else if(d.type=='2'){
                                return '<span class="layui-badge layui-bg-normal" style="background-color:#1E9FFF;">续</span>';
                            }else if(d.type=='3'){
                                return '<span class="layui-badge layui-bg-warm" style="background-color:#FFB800;">删</span>';
                            }else if(d.type=='4'){
                                return '<span class="layui-badge layui-bg-danger" style="background-color:#FF5722;">弃</span>';
                            }
                        }}
                    ,{field:'task_status', title: '流程状态',width: 120, align: 'center', unresize: true,templet:function (d){
                            if(d.task_status=='2'){
                                return '<span class="layui-badge" style="background-color:#FF5722;">流程处理中</span>';
                            }else if(d.task_status=='3'){
                                return '<span class="layui-badge" style="background-color:#009688;">流程已通过</span>';
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'status', title: '购买状态',width: 120, align: 'center', unresize: true,templet:function (d){
                            if(d.status=='1'){
                                return '<span class="layui-badge" style="background-color:#009688;">成功</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge" style="background-color:#FF5722;cursor:pointer;" title="'+d.exception_remark+'">异常</span>';
                            }else{
                                return '';
                            }
                        }}

                ]]
                ,page : false
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


        };

        $("#reportForm").on('click',function () {
            var url="#(ctxPath)/providentFund/providentFundReportForm";
            pop_show("公积金报表",url,'100%','100%');
        })

    });
</script>
#end