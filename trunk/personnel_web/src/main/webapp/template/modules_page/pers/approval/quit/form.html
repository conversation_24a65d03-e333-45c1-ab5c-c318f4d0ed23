#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript">
    layui.use(['form','laytpl','table','element','layer','laydate'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#estimateQuitDate',
            type: 'date',
            trigger: 'click'
            ,done: function(value, date){
                layer.load();
                $.post('#(ctxPath)/api/getDeductAnnualLeaveDays?quitDate='+value+"&empId="+$("#employeeId").val(),{},function (res) {
                    console.log(res);
                    if(res.code=='0'){
                        $("#deductAnnualLeaveDays").val(res.data);
                    }
                    layer.closeAll('loading');
                })
            }
        });

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }

            var transferData=parent.getQuitTransferEmpIdValue();
            if(transferData.quitTransferEmpId!=undefined && transferData.quitTransferEmpId!='' && transferData.quitTransferEmpId!=$("#employeeId").val()){
                $("#transferEmpId").val(transferData.quitTransferEmpId);
                $("#transferFullName").val(transferData.transferFullName+"("+transferData.transferWorkNum+")");
            }
        },300);



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        let selectedStr='';
                        if(index==0){
                            selectedStr='selected';
                        }
                        str+='<option value="'+item.id+'"  '+selectedStr+' >'+item.orgName+'</option>';
                    });
                    if(res.data.length>0){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })

        $("#transferChoiceBtn").on('click',function () {
            parent.transferEmpTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var transferEmpId=$("#transferEmpId").val();
            if(transferEmpId===''){
                $.post('#(ctxPath)/pers/approval/isLinkMan',{'employeeId':$('#employeeId').val()},function (res) {
                    if(res.state=='ok'){
                        layer.msg('离职员工为'+res.msg+"负责人，必须选择交接人", {icon: 2, offset: 'auto'});
                    }else{
                        $.ajax({
                            type:'post',
                            data: $("#noticeForm").serialize(),
                            url: '#(ctxPath)/api/saveEmployeeQuit',
                            contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                            dataType: 'json',
                            timeout: 30000,
                            beforeSend: function (XMLHttpRequest) {
                                layer.load();
                            },
                            success: function (res) {
                                if (res.code == '0') {
                                    parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                                    parent.reloadTable();
                                    pop_close();
                                } else {
                                    layer.msg(res.msg, {icon: 2, offset: 'auto'});
                                }
                            }
                            ,complete :function(XMLHttpRequest, TS){
                                layer.closeAll('loading');
                            }
                        });
                    }
                });
            }else{
                $.ajax({
                    type:'post',
                    data: $("#noticeForm").serialize(),
                    url: '#(ctxPath)/api/saveEmployeeQuit',
                    contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                    dataType: 'json',
                    timeout: 30000,
                    beforeSend: function (XMLHttpRequest) {
                        layer.load();
                    },
                    success: function (res) {
                        if (res.code == '0') {
                            parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                            parent.reloadTable();
                            pop_close();
                        } else {
                            layer.msg(res.msg, {icon: 2, offset: 'auto'});
                        }
                    }
                    ,complete :function(XMLHttpRequest, TS){
                        layer.closeAll('loading');
                    }
                });
            }

            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {
            var data=$("#noticeForm").serialize()+"&saveType=2";
            //提交表单数据

            //判断是否需要交接人
            var transferEmpId=$("#transferEmpId").val();
            if(transferEmpId===''){
                $.post('#(ctxPath)/pers/approval/isLinkMan',{'employeeId':$('#employeeId').val()},function (res) {
                    if(res.state=='ok'){
                        layer.msg('离职员工为'+res.msg+"负责人，必须选择交接人", {icon: 2, offset: 'auto'});
                    }else{
                        $.ajax({
                            type:'post',
                            data: data,
                            url: '#(ctxPath)/api/saveEmployeeQuit',
                            contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                            dataType: 'json',
                            timeout: 30000,
                            beforeSend: function (XMLHttpRequest) {
                                layer.load();
                            },
                            success: function (res) {
                                if (res.code == '0') {
                                    parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                                    parent.reloadTable();
                                    parent.setQuitEmpIdValue('','','');
                                    parent.setQuitTransferEmpIdValue('','','');

                                    pop_close();
                                } else {
                                    layer.msg(res.msg, {icon: 2, offset: 'auto'});
                                }
                            }
                            ,complete :function(XMLHttpRequest, TS){
                                layer.closeAll('loading');
                            }
                        });
                    }
                });
            }else{
                $.ajax({
                    type:'post',
                    data: data,
                    url: '#(ctxPath)/api/saveEmployeeQuit',
                    contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                    dataType: 'json',
                    timeout: 30000,
                    beforeSend: function (XMLHttpRequest) {
                        layer.load();
                    },
                    success: function (res) {
                        if (res.code == '0') {
                            parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                            parent.reloadTable();
                            parent.setQuitEmpIdValue('','','');
                            parent.setQuitTransferEmpIdValue('','','');

                            pop_close();
                        } else {
                            layer.msg(res.msg, {icon: 2, offset: 'auto'});
                        }
                    }
                    ,complete :function(XMLHttpRequest, TS){
                        layer.closeAll('loading');
                    }
                });
            }
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
    <div class="layui-col-md6 layui-form" id="content" style="padding-right: 10px;">
        <div class="layui-collapse" >

            <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>离职员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(employeeQuit.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(employeeQuit==null)#(createBy??)#else#(employeeQuit.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(employeeQuit!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>

                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>部门</label>
                        <div class="layui-input-inline">
                            <select id="deptId" name="deptId" lay-filter="deptId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==employeeQuit.deptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>职位</label>
                        <div class="layui-input-inline">
                            <select id="positionId" name="positionId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==employeeQuit.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>生效时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="estimateQuitDate" id="estimateQuitDate" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#date(employeeQuit.estimateQuitDate??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 0px"><font color="red">*</font>扣除年休假天数</label>
                        <div class="layui-input-inline">
                            <input type="text" name="deductAnnualLeaveDays" id="deductAnnualLeaveDays" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#(employeeQuit.deductAnnualLeaveDays??0)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>离职类型</label>
                        <div class="layui-input-inline">
                            <select id="quitType" name="quitType" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择离职类型</option>
                                <option value="dismiss" #if(employeeQuit.quitType??=='dismiss') selected #end>辞退</option>
                                <option value="autonomy" #if(employeeQuit.quitType??=='autonomy') selected #end>自离</option>
                                <option value="expel" #if(employeeQuit.quitType??=='expel') selected #end>开除</option>
                                <option value="auto_quit" #if(employeeQuit.quitType??=='auto_quit') selected #end>自动离职</option>
                                <option value="voluntarily_quit" #if(employeeQuit.quitType??=='voluntarily_quit') selected #end>个人离职</option>
                                <option value="resign" #if(employeeQuit.quitType??=='resign') selected #end>辞职</option>
                                <option value="other" #if(employeeQuit.quitType??=='other') selected #end>其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>交接人</label>
                        <div class="layui-input-inline">

                            <input type="hidden" id="transferEmpId" name="transferEmpId" value="#(transferEmp.id??)">
                            <input id="transferFullName" name="transferFullName" readonly  lay-verify="required"  placeholder="" #if(employeeQuit!=null && transferEmp!=null)value="#(transferEmp.fullName??)(#(transferEmp.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>
                        <button class="layui-btn" id="transferChoiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>离职原因</label>
                        <div class="layui-input-block">
                            <textarea name="reason" placeholder="请输入内容" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(employeeQuit.reason??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" placeholder="请输入内容" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(employeeQuit.remark??)</textarea>
                        </div>
                    </div>
            </div>
        </div>
    </div>
    <div class="layui-col-md6">
        #if(stepts.size()??0>0)
        <table class="layui-table" id="steptsTable">
            <!--<colgroup>
                <col width="10%">
                <col width="13%">
                <col width="30%">
                <col width="30%">
                <col width="17%">
            </colgroup>-->
            <thead>
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">节点</th>
                <th style="text-align: center;">流程</th>
                <th style="text-align: center;">意见</th>
                <th style="text-align: center;">操作时间</th>
                <th style="text-align: center;">操作人</th>
            </tr>
            </thead>
            <tbody>
            #for(stept:stepts)
            <tr>
                <td align="center">#(for.index+1)</td>
                <td>#(stept.ActivityName)</td>
                <td align="center">
                    #if(stept.StepState==3)
                    提交
                    #else if(stept.StepState==1)
                    待处理
                    #else if(stept.StepState==0)
                    等待
                    #else if(stept.StepState==2)
                    正处理
                    #else if(stept.StepState==4)
                    撤回
                    #else if(stept.StepState==5)
                    批准
                    #else if(stept.StepState==6)
                    拒绝
                    #else if(stept.StepState==7)
                    转移
                    #else if(stept.StepState==8)
                    失败
                    #else if(stept.StepState==9)
                    跳过
                    #end
                </td>
                <td>#(stept.Comment)</td>
                <td>#(stept.CommentTime)</td>
                <td align="center">#(stept.CommentUserName)</td>
            </tr>
            #end
            </tbody>
        </table>

        <form class="layui-form layui-form-pane" id="taskForm">
            <!--<div class="layui-form-item layui-form-text">
                <label class="layui-form-label">意见</label>
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                </div>
            </div>-->
            <div class="layui-form-item">
                <div class="layui-input-block pull-right">

                    <!--<button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort || !isSaveHandle) disabled style="display: none;" #end >中止</button>
                    <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit || !isSaveHandle) disabled style="display: none;" #end>提交</button>
                    <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject || !isSaveHandle) disabled  style="display: none;" #end>拒绝</button>
                    <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove || !isSaveHandle) disabled style="display: none;" #end>通过</button>-->
                </div>
            </div>
        </form>
        #end
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">

            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            #end
            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
            #end
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
        <div class="pull-right">
            <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
        </div>

    </div>
    </form>
</div>

</body>
#end