#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(dispatchApply!=null)
            deptSelect.setValue(["#(dispatchApply.dispatchDeptId??)"]);
            #end
        });



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            let tbodyTrs=$("#tbody tr");
            if(tbodyTrs.length==0){
                layer.msg('请先添加外派时间', {icon: 2, offset: 'auto'});
                return false;
            }
            let dateArray=[];
            //let data={dispatchDeptId,dispatchDeptName,dateTypeName,dateType,startTime,endTime}
            $.each(tbodyTrs,function (index,item) {
                let dispatchDeptId=$(item).find('input[name="dispatchDeptId"]').val();
                let startTime=$(item).find('input[name="startTime"]').val();
                let endTime=$(item).find('input[name="endTime"]').val();
                let dateType=$(item).find('input[name="dateType"]').val();
                dateArray.push({dispatchDeptId,startTime,endTime,dateType});

            });


            var data=$("#noticeForm").serialize()+"&dateArray="+JSON.stringify(dateArray);

            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        form.on('select(dispatchType)',function (obj) {
            if(obj.value=='1'){
                $("#deptDiv").css("display","block");
            }else{
                $("#deptDiv").css("display","none");
            }

        })

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            let tbodyTrs=$("#tbody tr");
            if(tbodyTrs.length==0){
                layer.msg('请先添加外派时间', {icon: 2, offset: 'auto'});
                return false;
            }
            let dateArray=[];
            //let data={dispatchDeptId,dispatchDeptName,dateTypeName,dateType,startTime,endTime}
            $.each(tbodyTrs,function (index,item) {
                let dispatchDeptId=$(item).find('input[name="dispatchDeptId"]').val();
                let startTime=$(item).find('input[name="startTime"]').val();
                let endTime=$(item).find('input[name="endTime"]').val();
                let dateType=$(item).find('input[name="dateType"]').val();
                dateArray.push({dispatchDeptId,startTime,endTime,dateType});

            });


            var data=$("#noticeForm").serialize()+"&saveType=2&dateArray="+JSON.stringify(dateArray);
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        del=function(index){
            $("#restTimeItemDiv-"+index).remove()
        }

        $("#addDateBtn").on('click',function () {


            loadForm(null,null);

        });

        loadForm=function(trObj,initData){
            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "查看详情",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['750px;','500px;'],
                content: `
                            <form class="layui-form layui-form-pane" lay-filter="layform" id="itemForm" style='padding: 5px;'>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">*</font>外派单位</label>
                                    <div class="layui-input-block">
                                        <div id="deptSelect"  name="" style="margin-top: 1px;" >

                                        </div>
                                    </div>
                                </div>

                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">*</font>时长类型</label>
                                    <div class="layui-input-block">
                                        <select id="dateType" name="dateType" lay-filter="dateType" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                            <option value="1" #if(leaveRestApply.leaveRestDateType??=="1") selected #end>按天数(全天)</option>
                                            <option value="2" #if(leaveRestApply.leaveRestDateType??=="2") selected #end>按小时</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="layui-row" id="leaveTimeDiv">
                                    #if(leaveRestApply.leaveRestDateType??=='1' || leaveRestApply==null)
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差开始时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="startTime" id="startTime" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#date(leaveRestApply.startTime??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline" style="display:none;">
                                            <select name="startDateTime" lay-filter="startDateTime" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                                <option value=" 00:00:00" #if(startDateTime??=="00:00:00" || leaveRestApply==null) selected #end>上午</option>
                                                <option value=" 12:00:00" #if(startDateTime??=="12:00:00") selected #end>下午</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差结束时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="endTime" id="endTime" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#date(leaveRestApply.endTime??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                                        </div>
                                        <div class="layui-input-inline" style="display:none;" >
                                            <select name="endDateTime" lay-filter="endDateTime" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                                <option value=" 12:00:00" #if(endDateTime??=="12:00:00") selected #end>上午</option>
                                                <option value=" 23:59:59" #if(endDateTime??=="23:59:59" || leaveRestApply==null) selected #end>下午</option>
                                            </select>
                                        </div>
                                    </div>
                                    #else
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差开始时间</label>
                                        <div class="layui-input-block">
                                            <input type="text" name="startTime" id="startTimeInput" readonly  required value="#date(leaveRestApply.startTime??,'yyyy-MM-dd HH:mm')"  lay-verify="required" placeholder="请输入请假开始时间" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差结束时间</label>
                                        <div class="layui-input-inline" id="endTimeInputDiv">
                                            <input type="text" name="endTime" id="endTimeInput" readonly  required value="#date(leaveRestApply.endTime??,'yyyy-MM-dd HH:mm')"  lay-verify="required" placeholder="请输入请假结束时间" autocomplete="off" class="layui-input">
                                        </div>
                                    </div>

                                    #end
                                </div>

                                <!--<div class="layui-form-item" id="restTimeDiv" style="display: none;">
                                    <label class="layui-form-label">休息时间段</label>
                                    <div class="layui-input-block" style="margin-bottom: 10px;">
                                        <button class="layui-btn" type="button" id="restBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end>添加</button>
                                        <input name="restCount" id="restCount" value="#(restTimeArray.size()??0)" type="hidden">
                                    </div>
                                    #for(restTime : restTimeArray)
                                    <div class="layui-input-block" id="restTimeItemDiv-#(for.index+1)" style="margin-bottom: 10px;">
                                        <label class="layui-form-label" style="padding: 8px 5px;" >休息开始时间</label>
                                        <div class="layui-input-inline" style="width: 150px;">
                                            <input type="text" name="restStartTime-#(for.index+1)" id="restStartTime-#(for.index+1)" #if(!isSaveHandle && taskId!=null) disabled  readonly #end  required value="#(restTime.restStartTime??)"  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">
                                        </div>
                                        <label class="layui-form-label" style="padding: 8px 5px;">休息结束时间</label>
                                        <div class="layui-input-inline" style="width: 150px;">
                                            <input type="text" name="restEndTime-#(for.index+1)" id="restEndTime-#(for.index+1)" #if(!isSaveHandle && taskId!=null) disabled readonly  #end  required value="#(restTime.restEndTime??)"  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">
                                        </div>
                                        <button class="layui-btn layui-btn-xs" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button" style="margin-top: 7px;" onclick="del(#(for.index+1))">删除</button>
                                    </div>
                                    #end
                                </div>-->
                            </form>
                        `,
                cancel: function(){
                },
                success: function(layero, index){
                    console.log('layer open success');
                    form.render('select');

                    laydate.render({
                        elem : '#startTime',
                        type: 'date',
                        trigger: 'click'
                        //,min:0
                    });

                    laydate.render({
                        elem : '#endTime',
                        type: 'date',
                        trigger: 'click'
                        //,min:0
                    });

                    var deptSelect = xmSelect.render({
                        el: '#deptSelect',
                        //autoRow: true,
                        height: '100px',
                        prop: {
                            name: 'name',
                            value: 'id',
                        },
                        radio: true,
                        tree: {
                            show: true,
                            expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                            showFolderIcon: true,
                            showLine: true,
                            indent: 15,
                            lazy: true,
                            clickExpand: true,
                            clickClose: true,
                            strict: false,
                            //点击节点是否选中
                            clickCheck: true,

                            load: function(item, cb){

                            }
                        },
                        height: 'auto',
                        data(){
                            return [];
                        }
                    })
                    $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
                        deptSelect.update({
                            data:res
                        });
                    });

                    $("#restBtn").on('click',function () {
                        var startTime=$("#startTimeInput").val();
                        var endTime=$("#endTimeInput").val();
                        if(startTime=='' || endTime==''){
                            layer.msg('请先选择请休假开始结束时间', {icon: 2, offset: 'auto'});
                            return;
                        }
                        startTime+=":00";
                        endTime+=":00";
                        var restCount=$("#restCount").val();
                        restCount=Number(restCount)+1;
                        $("#restTimeDiv").append('<div class="layui-input-block" id="restTimeItemDiv-'+restCount+'" style="margin-bottom: 10px;">\n' +
                            '                            <label class="layui-form-label" style="padding: 8px 5px;" >休息开始时间</label>\n' +
                            '                            <div class="layui-input-inline" style="width: 150px;">\n' +
                            '                                <input type="text" name="restStartTime-'+restCount+'" id="restStartTime-'+restCount+'" required value=""  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">\n' +
                            '                            </div>\n' +
                            '                            <label class="layui-form-label" style="padding: 8px 5px;">休息结束时间</label>\n' +
                            '                            <div class="layui-input-inline" style="width: 150px;">\n' +
                            '                                <input type="text" name="restEndTime-'+restCount+'" id="restEndTime-'+restCount+'" required value=""  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">\n' +
                            '                            </div>' +
                            '                            <button class="layui-btn layui-btn-xs" style="margin-top: 7px;" type="button" onclick="del('+restCount+')">删除</button>\n' +
                            '                        </div>');

                        laydate.render({
                            elem : "#restStartTime-"+restCount,
                            type: 'datetime',
                            trigger: 'click',
                            min:startTime,
                            max:endTime
                        });
                        laydate.render({
                            elem : "#restEndTime-"+restCount,
                            type: 'datetime',
                            trigger: 'click',
                            min:startTime,
                            max:endTime
                        });
                        $("#restCount").val(restCount);
                    });

                    form.on('select(dateType)',function (obj) {
                        console.log(obj.value);
                        if(obj.value=='1'){

                            $("#restTimeDiv").css('display','none');

                            $("#leaveTimeDiv").html('<div class="layui-form-item">\n' +
                                '                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>外派开始时间</label>\n' +
                                '                            <div class="layui-input-block">\n' +
                                '                                <input type="text" name="startTime" id="startTime"  required value=""  lay-verify="required" placeholder="请输入开始日期" autocomplete="off" class="layui-input">\n' +
                                '                            </div>\n' +
                                '                            <div class="layui-input-inline" style="display:none;" >\n' +
                                '                                <select name="startDateTime" lay-filter="startDateTime" lay-verify="required" >\n' +
                                '                                    <option value=" 00:00:00" #if(startDateTime??=="00:00:00") selected #end>上午</option>\n' +
                                '                                    <option value=" 12:00:00" #if(startDateTime??=="12:00:00") selected #end>下午</option>\n' +
                                '                                </select>\n' +
                                '                            </div>\n' +
                                '                        </div>\n' +
                                '                        <div class="layui-form-item">\n' +
                                '                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差结束时间</label>\n' +
                                '                            <div class="layui-input-block">\n' +
                                '                                <input type="text" name="endTime" id="endTime"  required value=""  lay-verify="required" placeholder="请输入结束日期" autocomplete="off" class="layui-input">\n' +
                                '                            </div>\n' +
                                '                            <div class="layui-input-inline" style="display:none;">\n' +
                                '                                <select name="endDateTime" lay-filter="endDateTime" lay-verify="required" >\n' +
                                '                                    <option value=" 12:00:00" #if(endDateTime??=="12:00:00") selected #end>上午</option>\n' +
                                '                                    <option value=" 23:59:59" #if(endDateTime??=="23:59:59" || leaveRestApply==null) selected #end>下午</option>\n' +
                                '                                </select>\n' +
                                '                            </div>\n' +
                                '                        </div>');

                            laydate.render({
                                elem : '#startTime',
                                type: 'date',
                                trigger: 'click'
                                //,min:0
                            });

                            laydate.render({
                                elem : '#endTime',
                                type: 'date',
                                trigger: 'click'
                                //,min:0
                            });

                            form.render('select');
                        }else if(obj.value=='2'){
                            $("#restTimeDiv").css('display','block');

                            $("#leaveTimeDiv").html('<div class="layui-form-item">\n' +
                                '                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>外派开始时间</label>\n' +
                                '                            <div class="layui-input-block">\n' +
                                '                                <input type="text" name="startTime" id="startTimeInput" readonly  required value=""  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">\n' +
                                '                            </div>\n' +
                                '                        </div>\n' +
                                '                        <div class="layui-form-item">\n' +
                                '                            <label class="layui-form-label" style="padding: 8px 5px;><font color="red">*</font>外派结束时间</label>\n' +
                                '                            <div class="layui-input-block" id="endTimeInputDiv">\n' +
                                '                                <input type="text" name="endTime" id="endTimeInput" readonly  required value=""  lay-verify="required" placeholder="请输入结束时间" autocomplete="off" class="layui-input">\n' +
                                '                            </div>\n' +
                                '                        </div>');



                            //时间选择器
                            laydate.render({
                                elem: '#startTimeInput'
                                ,type: 'datetime'
                                ,format: 'yyyy-MM-dd HH:mm:ss'
                                ,done: function(value, date){
                                    var myDate = new Date(value);
                                    myDate.setHours(myDate.getHours()+12);

                                    var maxDate=myDate.getFullYear();
                                    if((myDate.getMonth()+1)>=10){
                                        maxDate+="-"+(myDate.getMonth()+1);
                                    }else{
                                        maxDate+="-0"+(myDate.getMonth()+1);
                                    }
                                    if(myDate.getDate()>=10){
                                        maxDate+="-"+myDate.getDate();
                                    }else{
                                        maxDate+="-0"+myDate.getDate();
                                    }
                                    if(myDate.getHours()>=10){
                                        maxDate+=" "+myDate.getHours();
                                    }else{
                                        maxDate+=" 0"+myDate.getHours();
                                    }
                                    if(myDate.getMinutes()>=10){
                                        maxDate+=":"+myDate.getMinutes()
                                    }else{
                                        maxDate+=":0"+myDate.getMinutes()
                                    }
                                    if(myDate.getSeconds()>=10){
                                        maxDate+=":"+myDate.getSeconds();
                                    }else{
                                        maxDate+=":0"+myDate.getSeconds();
                                    }
                                    $("#endTimeInputDiv").html('<input type="text" name="endTime" id="endTimeInput" readonly  required value=""  lay-verify="required" placeholder="请输入请假结束时间" autocomplete="off" class="layui-input">');

                                    laydate.render({
                                        elem: '#endTimeInput'
                                        ,type: 'datetime'
                                        ,format: 'yyyy-MM-dd HH:mm:ss'
                                        ,min:value
                                        ,max:maxDate
                                    });
                                    form.render('select');
                                }
                            });


                            $("#endTimeInput").on('click',function () {
                                var startTimeInputValue=$("#startTimeInput").val();
                                if(startTimeInputValue==''){
                                    layer.msg('请先选择开始时间', {icon: 2, offset: 'auto'});
                                    return false;
                                }else{

                                }
                            })

                            form.render();

                        }
                    });

                    if(initData!=null && initData!=''){
                        deptSelect.setValue(initData.destinationCity);
                        $("select[name='trafficType']").find("option[value='"+initData.trafficType+"']").prop('selected',true);
                        $("select[name='dateType']").find("option[value='"+initData.dateType+"']").prop('selected',true);
                        $("#startTime").val(initData.startTime);
                        $("#endTime").val(initData.endTime);
                        form.render();
                    }
                    $('select[name="dateType"]').next().find('.layui-anim').children('dd[lay-value="1"]').click();

                    //$("#leaveRestDateType option[value='1']").click();

                    return false;
                },
                end : function(){
                },yes: function(index, layero){
                    form.render();
                    //xmSelect.get('#citySelect');
                    var dispatchDeptId=xmSelect.get('#deptSelect', true).getValue()[0].id;
                    var dispatchDeptName=xmSelect.get('#deptSelect', true).getValue()[0].name;
                    let dateType=$("select[name='dateType']").val();
                    let dateTypeName=$("select[name='dateType']").find("option[value='"+dateType+"']").text();
                    let startTime;
                    let endTime;
                    if("1"==dateType){
                        startTime=$("#startTime").val();
                        endTime=$("#endTime").val();
                    }else if("2"==dateType){
                        startTime=$("#startTimeInput").val();
                        endTime=$("#endTimeInput").val();
                    }
                    if(endTime=='' || startTime=='' || dispatchDeptId==''){
                        layer.msg('请先完成表单内容填写', {icon: 2, offset: 'auto'});
                        return false;
                    }
                    let data={dispatchDeptId,dispatchDeptName,dateTypeName,dateType,startTime,endTime}
                    console.log(data);
                    //return false;
                    addTr(data);
                    if(trObj!=null){
                        //$(trObj).parents('tr').remove();
                    }
                    layer.close(index);
                    return false;
                }

            });
        }

        addTr=function (data) {


            let restStr="";
            /*$.each(data.restArray,function (index,item) {
                restStr+=item.restStartTime+'至'+item.restEndTime+"<br>";
            });*/
            let dataStr=JSON.stringify(data);
            //let data={dispatchDeptId,dispatchDeptName,dateTypeName,dateType,startTime,endTime}
            $("#tbody").append(`
                <tr>
                    <td>`+data.dispatchDeptName+`</td>
                    <td>`+data.dateTypeName+`</td>
                    <td>`+data.startTime+'至'+data.endTime+`</td>
                    <td>
                        <input name="dispatchDeptId" type="hidden" value="`+data.dispatchDeptId+`">
                        <input name="startTime" type="hidden" value="`+data.startTime+`">
                        <input name="endTime" type="hidden" value="`+data.endTime+`">
                        <input name="dateType" type="hidden" value="`+data.dateType+`">

                        <!--<button type="button" class="layui-btn layui-btn-xs" onclick=editTr(this)>编辑</button>-->
                        <button type="button" class="layui-btn layui-btn-xs layui-btn-danger " onclick="delTr(this)">作废</button>
                    </td>
                </tr>
            `);
        }

        delTr=function (obj) {
            $(obj).parents('tr').remove();
        }

        editTr=function (obj) {
            let leaveRestType=$(obj).siblings('input[name="leaveRestType"]').val();
            let leaveRestDateType=$(obj).siblings('input[name="leaveRestDateType"]').val();
            let startTime=$(obj).siblings('input[name="startTime"]').val();
            let endTime=$(obj).siblings('input[name="endTime"]').val();
            let restArray=$(obj).siblings('input[name="restArray"]').val();
            let data={leaveRestType,leaveRestDateType,startTime,endTime,restArray};

            loadForm(obj,data);
        }

    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div class="layui-col-md7 layui-form" id="content" style="padding-right: 10px;">
            <div class="layui-collapse" >

                <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>外派员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(dispatchApply.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(dispatchApply==null)#(createBy??)#else#(dispatchApply.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(dispatchApply!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>

                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>所在部门</label>
                        <div class="layui-input-inline">
                            <select id="deptId" name="deptId" lay-filter="deptId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==dispatchApply.deptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">职位</label>
                        <div class="layui-input-inline">
                            <select id="positionId" name="positionId" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==dispatchApply.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <button class="layui-btn" style="float: right;" id="addDateBtn" #if(!isSaveHandle && taskId!=null) disabled readonly style="display: none;" #end type="button">添加时间</button>
                        <table class="layui-table">

                            <thead>
                            <tr>
                                <th><font>*</font>外派单位</th>
                                <th><font>*</font>时间类型</th>
                                <th><font>*</font>时间</th>
                                <th><font></font>操作</th>
                            </tr>
                            </thead>
                            <tbody id="tbody">
                            #for(dateItem : record.dateList??)
                            <tr>
                                <td>#(dateItem.dispatchDeptName??)</td>
                                <td>#if(dateItem.dateType??=='1')按天数(全天)#elseif(dateItem.dateType??=='2')按小时#end</td>
                                <td>
                                    #if(dateItem.dateType??=='1')
                                    #(dateItem.startTime??)至#(dateItem.endTime??)
                                    #elseif(dateItem.dateType??=='2')
                                    #(dateItem.startTime??)至#(dateItem.endTime??)
                                    #end
                                </td>

                                <td>
                                    <input name="dateType" type="hidden" value="#(dateItem.dateType??)">
                                    <input name="dispatchDeptId" type="hidden" value="#(dateItem.dispatchDeptId??)">
                                    <input name="startTime" type="hidden" value="#(dateItem.startTime??)">
                                    <input name="endTime" type="hidden" value="#(dateItem.endTime??)">
                                    <input name="restArray" type="hidden" value='#(dateItem.restTimesStr??)'>
                                    <button type="button" class="layui-btn layui-btn-xs layui-btn-danger " #if(!isSaveHandle && taskId!=null) disabled readonly style="display: none;" #end onclick="delTr(this)">作废</button>
                                </td>
                            </tr>
                            #end

                            </tbody>
                        </table>

                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">外派事由</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(dispatchApply.remark??)</textarea>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="layui-col-md5">
            #if(stepts.size()??0>0)
            <table class="layui-table" id="steptsTable">
                <!--<colgroup>
                    <col width="10%">
                    <col width="13%">
                    <col width="30%">
                    <col width="30%">
                    <col width="17%">
                </colgroup>-->
                <thead>
                <tr>
                    <th style="text-align: center;">序号</th>
                    <th style="text-align: center;">节点</th>
                    <th style="text-align: center;">流程</th>
                    <th style="text-align: center;">意见</th>
                    <th style="text-align: center;">操作时间</th>
                    <th style="text-align: center;">操作人</th>
                </tr>
                </thead>
                <tbody>
                #for(stept:stepts)
                <tr>
                    <td align="center">#(for.index+1)</td>
                    <td>#(stept.ActivityName)</td>
                    <td align="center">
                        #if(stept.StepState==3)
                        提交
                        #else if(stept.StepState==1)
                        待处理
                        #else if(stept.StepState==0)
                        等待
                        #else if(stept.StepState==2)
                        正处理
                        #else if(stept.StepState==4)
                        撤回
                        #else if(stept.StepState==5)
                        批准
                        #else if(stept.StepState==6)
                        拒绝
                        #else if(stept.StepState==7)
                        转移
                        #else if(stept.StepState==8)
                        失败
                        #else if(stept.StepState==9)
                        跳过
                        #end
                    </td>
                    <td>#(stept.Comment)</td>
                    <td>#(stept.CommentTime)</td>
                    <td align="center">#(stept.CommentUserName)</td>
                </tr>
                #end
                </tbody>
            </table>

            <form class="layui-form layui-form-pane" id="taskForm">
                <!--<div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">意见</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                    </div>
                </div>-->
                <div class="layui-form-item">
                    <div class="layui-input-block pull-right">

                        <!--<button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort || !isSaveHandle) disabled style="display: none;" #end >中止</button>
                        <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit || !isSaveHandle) disabled style="display: none;" #end>提交</button>
                        <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject || !isSaveHandle) disabled  style="display: none;" #end>拒绝</button>
                        <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove || !isSaveHandle) disabled style="display: none;" #end>通过</button>-->
                    </div>
                </div>
            </form>
            #end
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                #if(isSaveHandle || taskId==null)
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                #else
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn" style="display: none;">保&nbsp;&nbsp;存</button>
                #end
                #if(isSaveHandle || taskId==null)
                <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
                #end
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
            <div class="pull-right">
                <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
            </div>

        </div>
    </form>
</div>

</body>
#end