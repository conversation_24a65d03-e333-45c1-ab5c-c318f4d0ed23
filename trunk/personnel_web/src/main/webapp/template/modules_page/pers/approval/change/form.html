#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            direction: 'down',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            },on: function(data){
                //arr:  当前多选已选中的数据
                var arr = data.arr;
                //change, 此次选择变化的数据,数组
                var change = data.change;
                //isAdd, 此次操作是新增还是删除
                var isAdd = data.isAdd;
                if(isAdd){
                    var deptId=arr[0].id;

                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/position/getPositionByOrgId',
                        data: {'orgId':deptId},
                        notice: false,
                        loadFlag: false,
                        success : function(rep){
                            if(rep.state==='ok'){
                                var str='<option value="" role-id="">请选择职位</option>';
                                var data=rep.data;
                                $.each(data,function (index,item) {
                                    str+='<option value="'+item.id+'" role-id="'+item.roleId+'" role-name="'+item.roleName+'">'+item.positionName+'</option>';
                                });
                                $("#newPositionId").html(str);
                                form.render('select');
                            }
                        },
                        complete : function() {
                        }
                    });
                }else{
                    var str='<option value="" role-id="">请选择职位</option>';
                    $("#newPositionId").html(str);
                    form.render('select');
                }
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(changeApply!=null)
            deptSelect.setValue(["#(changeApply.newDeptId??)"]);
            #end
        });



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var newDeptId=deptSelect.getValue()[0].id;
            if(newDeptId==null || newDeptId==''){
                layer.msg('请选择异动单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&newDeptId="+newDeptId;

            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeChangeApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            var newDeptId=deptSelect.getValue()[0].id;
            if(newDeptId==null || newDeptId==''){
                layer.msg('请选择异动单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&saveType=2&newDeptId="+newDeptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeChangeApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        form.on('select(changeDateType)',function (obj) {
            console.log(obj.value);
            if(obj.value=='1'){
                //$("#startTimeDiv").css("display","block");
                //$("#startTime").attr("lay-verify","required");
                $("#endTimeDiv").css("display","block");
                $("#endTime").attr("lay-verify","required");
            }else if(obj.value=='2'){
                //$("#startTimeDiv").css("display","none");
                //$("#startTime").attr("lay-verify","");
                $("#endTimeDiv").css("display","none");
                $("#endTime").attr("lay-verify","");
            }
        })
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div class="layui-col-md6 layui-form" id="content" style="padding-right: 10px;">
            <div class="layui-collapse" >

                <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>异动员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(changeApply.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(changeApply==null)#(createBy??)#else#(changeApply.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(changeApply!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>

                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>所在部门</label>
                        <div class="layui-input-inline">
                            <select id="deptId" name="deptId" lay-filter="deptId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==changeApply.deptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>职位</label>
                        <div class="layui-input-inline">
                            <select id="positionId" name="positionId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==changeApply.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>异动前薪级</label>
                        <div class="layui-input-inline">
                            <select id="oldSalaryLv" name="oldSalaryLv" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择异动前薪级</option>
                                <option value="1" #if(changeApply.oldSalaryLv??=='1') selected #end>初级</option>
                                <option value="2" #if(changeApply.oldSalaryLv??=='2') selected #end>中级</option>
                                <option value="3" #if(changeApply.oldSalaryLv??=='3') selected #end>高级</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动前综合薪资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="lodSalary" id="lodSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.lodSalary??)"  lay-verify="required|number" placeholder="请输入异动前综合薪资" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>异动类型</label>
                        <div class="layui-input-inline">
                            <select id="changeType" name="changeType" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择异动类型</option>
                                #for(changeType : changeTypeMap)
                                <option value="#(changeType.key??)" #if(changeType.key??==changeApply.changeType??) selected #end>#(changeType.value??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>异动后单位</label>
                        <div class="layui-input-inline" style="width: 387px;">
                            <div id="deptSelect"  name="newDeptId" style="margin-top: 1px;" >

                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>异动后职位</label>
                        <div class="layui-input-inline">
                            <select id="newPositionId" name="newPositionId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : newPositionList)
                                <option value="#(position.id??)" #if(position.id??==changeApply.newPositionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>异动后薪级</label>
                        <div class="layui-input-inline">
                            <select id="newSalaryLv" name="newSalaryLv" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择异动前薪级</option>
                                <option value="1" #if(changeApply.newSalaryLv??=='1') selected #end>初级</option>
                                <option value="2" #if(changeApply.newSalaryLv??=='2') selected #end>中级</option>
                                <option value="3" #if(changeApply.newSalaryLv??=='3') selected #end>高级</option>
                            </select>
                        </div>
                    </div>
                    <!--<div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动后综合薪资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="newSalary" id="newSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.newSalary??)"  lay-verify="required|number" placeholder="请输入异动后综合薪资" autocomplete="off" class="layui-input">
                        </div>
                    </div>-->

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动后薪资</label>
                        <div class="layui-input-block">
                            <div class="layui-row">
                                <label class="layui-form-label">基本工资</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="basicSalary" id="basicSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                           value="#(changeApply.basicSalary??)"  lay-verify="" placeholder="请输入异动后基本工资" autocomplete="off" class="layui-input">
                                </div>

                            </div>
                            <div class="layui-row" style="margin-top: 5px;">
                                <label class="layui-form-label">绩效工资</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="meritSalary" id="meritSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                           value="#(changeApply.meritSalary??)"  lay-verify="" placeholder="请输入异动后绩效工资" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-row" style="margin-top: 5px;">
                                <label class="layui-form-label">岗位工资</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="postSalary" id="postSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                           value="#(changeApply.postSalary??)"  lay-verify="" placeholder="请输入异动后岗位工资" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                            <div class="layui-row" style="margin-top: 5px;">
                                <label class="layui-form-label">综合工资</label>
                                <div class="layui-input-inline">
                                    <input type="text" name="comprehensiveSalary" id="comprehensiveSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                           value="#(changeApply.comprehensiveSalary??)"  lay-verify="" placeholder="请输入异动后综合工资" autocomplete="off" class="layui-input">
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动时间长短</label>
                        <div class="layui-input-inline">
                            <select id="changeDateType" name="changeDateType" lay-filter="changeDateType" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="1" #if(changeApply.changeDateType??=='1') selected #end>短期</option>
                                <option value="2" #if(changeApply.changeDateType??=='2') selected #end>长期</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item" id="startTimeDiv">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动开始时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="startTime" id="startTime" #if(!isSaveHandle && taskId!=null) disabled readonly #end
                                   required value="#date(changeApply.startTime??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入异动开始时间" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-input-inline">
                            <select name="startDateTime" lay-filter="startDateTime" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value=" 00:00:00" #if(startDateTime??=="00:00:00") selected #end>上午</option>
                                <option value=" 12:00:00" #if(startDateTime??=="12:00:00") selected #end>下午</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item" id="endTimeDiv" #if(changeApply.changeDateType??=='2') style="display: none;" #end>
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动结束时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="endTime" id="endTime" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#date(changeApply.endTime??,'yyyy-MM-dd')" #if(changeApply.changeDateType??=='2') lay-verify="" #else   lay-verify="required" #end placeholder="请输入异动结束时间" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-input-inline">
                            <select name="endDateTime" lay-filter="endDateTime" #if(changeApply.changeDateType??=='2') lay-verify="" #else   lay-verify="required" #end #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value=" 12:00:00" #if(endDateTime??=="12:00:00") selected #end>上午</option>
                                <option value=" 23:59:59" #if(endDateTime??=="23:59:59") selected #end>下午</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">异动原因</label>
                        <div class="layui-input-block">
                            <textarea name="changeReason" id="changeReason" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(changeApply.changeReason??)</textarea>
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text" style="margin-bottom: 8px;">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end class="layui-textarea">#(changeApply.remark??)</textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div style="height: 45px;"></div>
        </div>
        <div class="layui-col-md6">
            #if(stepts.size()??0>0)
            <table class="layui-table" id="steptsTable">
                <!--<colgroup>
                    <col width="10%">
                    <col width="13%">
                    <col width="30%">
                    <col width="30%">
                    <col width="17%">
                </colgroup>-->
                <thead>
                <tr>
                    <th style="text-align: center;">序号</th>
                    <th style="text-align: center;">节点</th>
                    <th style="text-align: center;">流程</th>
                    <th style="text-align: center;">意见</th>
                    <th style="text-align: center;">操作时间</th>
                    <th style="text-align: center;">操作人</th>
                </tr>
                </thead>
                <tbody>
                #for(stept:stepts)
                <tr>
                    <td align="center">#(for.index+1)</td>
                    <td>#(stept.ActivityName)</td>
                    <td align="center">
                        #if(stept.StepState==3)
                        提交
                        #else if(stept.StepState==1)
                        待处理
                        #else if(stept.StepState==0)
                        等待
                        #else if(stept.StepState==2)
                        正处理
                        #else if(stept.StepState==4)
                        撤回
                        #else if(stept.StepState==5)
                        批准
                        #else if(stept.StepState==6)
                        拒绝
                        #else if(stept.StepState==7)
                        转移
                        #else if(stept.StepState==8)
                        失败
                        #else if(stept.StepState==9)
                        跳过
                        #end
                    </td>
                    <td>#(stept.Comment)</td>
                    <td>#(stept.CommentTime)</td>
                    <td align="center">#(stept.CommentUserName)</td>
                </tr>
                #end
                </tbody>
            </table>

            <form class="layui-form layui-form-pane" id="taskForm">
                <!--<div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">意见</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                    </div>
                </div>-->
                <div class="layui-form-item">
                    <div class="layui-input-block pull-right">

                        <!--<button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort || !isSaveHandle) disabled style="display: none;" #end >中止</button>
                        <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit || !isSaveHandle) disabled style="display: none;" #end>提交</button>
                        <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject || !isSaveHandle) disabled  style="display: none;" #end>拒绝</button>
                        <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove || !isSaveHandle) disabled style="display: none;" #end>通过</button>-->
                    </div>
                </div>
            </form>
            #end
        </div>

        <div class="layui-form-footer">
            <div class="pull-right">
                #if(isSaveHandle || taskId==null)
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                #end
                #if(isSaveHandle || taskId==null)
                <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
                #end
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
            <div class="pull-right">
                <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
            </div>

        </div>
    </form>
</div>

</body>
#end