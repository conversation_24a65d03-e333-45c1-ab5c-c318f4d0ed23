#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click',
            min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click',
            min:0
        });

        // setInterval(function () {
        //     var data=parent.getQuitEmpIdValue();
        //     if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
        //         $("#employeeId").val(data.quitEmpId);
        //         $("#fullName").val(data.fullName+"("+data.workNum+")");
        //         getDept();
        //     }
        // },300);

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(dispatchApply!=null)
            deptSelect.setValue(["#(dispatchApply.dispatchDeptId??)"]);
            #end
        });



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&dispatchDeptId="+dispatchDeptId;

            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&saveType=2&dispatchDeptId="+dispatchDeptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });


        $("#exportBtn").on('click',function () {
            window.location.href='#(ctxPath)/pers/approval/exportDispatchExcel?id='+$("#id").val();
        })
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" action="" id="formId" style="padding-top: 20px;padding-left: 20px;padding-right: 20px;">
        #if(isEnd)
<!--        <button class="layui-btn" type="button" id="exportBtn" style="margin-bottom: 10px;float: right;">导出外派单</button>-->
        #end

        #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
        <fieldset class="layui-elem-field layui-field-title" style="display:block;padding-left: 20px;">
            <legend>流程提交人信息</legend>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人部门</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人职位</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">任务编号</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                </div>
            </div>
        </fieldset>
        #end
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>转正单信息</legend>
        </fieldset>

        <input id="id" type="hidden" value="#(record.id??)">
        <div class="layui-form-item">
            <label class="layui-form-label" style="">姓名</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(employee.fullName??)(#(employee.workNum??))" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">申请提交时间</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#date(record.apply_time??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">所在部门</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(deptNames??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">职位</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(positionName??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        #if(isNew)
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">出差时长</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(newRecord.days??)"  autocomplete="off" class="layui-input">
            </div>
        </div>
        #for(dateItem : newRecord.dateList??)
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>行程#(for.index+1)</legend>
            <div class="layui-form-item">
                <label class="layui-form-label">单位</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(dateItem.dispatchDeptName??)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">外派时长类型</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#if(dateItem.dateType??=='1')按天数(全天)#elseif(dateItem.dateType??=='2')按小时#end" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label layui-form-text">外派时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" #if(dateItem.leaveRestDateType??=='2') style="font-size: 11px;padding-left: 0px;" #end readonly  value="#(dateItem.startTime??)至#(dateItem.endTime??)" autocomplete="off" class="layui-input">
                </div>
            </div>

        </fieldset>
        #end
        #else
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">外派时长</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(record.dispatch_days??)天"  autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">外派单位</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(record.dispatchDeptName??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">开始时间</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#date(record.start_time??,'yyyy-MM-dd') #(record.startDate??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px;">结束时间</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#date(record.end_time??,'yyyy-MM-dd') #(record.endDate??)"  autocomplete="off" class="layui-input">
            </div>
        </div>
        #end


        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">外派事由</label>
            <div class="layui-input-block">
                <textarea name="desc" placeholder="请输入内容" readonly class="layui-textarea">#(record.remark??)</textarea>
            </div>
        </div>

    </form>
</div>

</body>
#end