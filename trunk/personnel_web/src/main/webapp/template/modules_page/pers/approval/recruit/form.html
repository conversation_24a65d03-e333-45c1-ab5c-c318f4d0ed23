#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位招聘申请#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
    .layui-radio-disbaled > i {
        color: #5FB878 !important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#arrivalStartDate',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        laydate.render({
            elem : '#arrivalEndDate',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        //getDept();

        let deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
        #if(!isSaveHandle && taskId!=null) disabled: true, #end
            height: '450px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,
                load: function(item, cb){

                }
            },
            on: function(data){
                console.log(data.arr[0].id);

                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/pers/position/getPositionByOrgId',
                    data: {'orgId':data.arr[0].id},
                    notice: false,
                    loadFlag: false,
                    success : function(rep){
                        if(rep.state==='ok'){
                            var str='<option value="" role-id="">请选择职位</option>';
                            var data=rep.data;
                            $.each(data,function (index,item) {
                                str+='<option value="'+item.id+'" role-id="'+item.roleId+'" role-name="'+item.roleName+'">'+item.positionName+'</option>';
                            });
                            $("#applyPositionId").html(str);
                            form.render('select');
                        }
                    },
                    complete : function() {
                    }
                });
            },
            data(){
                return [];
            }
        })

        let workDeptSelect = xmSelect.render({
            el: '#workDeptSelect',
            autoRow: true,
            height: '450px',
            #if(!isSaveHandle && taskId!=null) disabled: true, #end
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,
                load: function(item, cb){

                }
            },
            on: function(data){

            },
            data(){
                return [];
            }
        })

        #if(model!=null)
        let educationBackgroundValues=JSON.parse('#(model.educationBackground??)');
        #end
        let educationBackgroundSelect=xmSelect.render({
            el: '#educationBackgroundSelect',
            #if(!isSaveHandle && taskId!=null) disabled: true, #end
            data: [
                {name: '初中及以下', value: 'chu_zhong'},
                {name: '高中', value: 'zhong_zhuan'},
                {name: '大专', value: 'da_zhuan'},
                {name: '本科', value: 'ben_ke'},
                {name: '硕士及以上', value: 'shuo_shi'}
            ]
            #if(model!=null)
            ,initValue:educationBackgroundValues
            #end
        })


        $.post('#(ctxPath)/api/getOrgTree',{},function (res) {
            deptSelect.update({
                data:res.data
            });

            workDeptSelect.update({
                data:res.data
            });

            #if(model!=null)
            deptSelect.setValue(["#(model.applyDeptId??)"]);
            #end

            #if(model!=null)
            workDeptSelect.setValue(["#(model.workDeptId??)"]);
            #end
        });





       /* function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    $.each(res.data,function (index,item) {
                        if(index==0){
                            selected="selected";
                        }
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length>0){
                        console.log('获取职位...............');
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }*/

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            let applyDeptId='';
            let workDeptId='';
            applyDeptId=deptSelect.getValue()[0].id;
            workDeptId=workDeptSelect.getValue()[0].id;
            if(applyDeptId==null || applyDeptId==''){
                layer.msg('请选择申请单位', {icon: 2, offset: 'auto'});
                return false;
            }
            if(workDeptId==null || workDeptId==''){
                layer.msg('请选择驻地单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var reason=$("#reason").val();
            /*if(reason=='' || reason==undefined){
                layer.msg('出差事由不能为空', {icon: 2, offset: 'auto'});
                return false;
            }*/
            let educationBackgroundArray=educationBackgroundSelect.getValue('value');
            if(educationBackgroundArray.length==0){
                layer.msg('请选择学历要求', {icon: 2, offset: 'auto'});
                return false;
            }

            var data=$("#noticeForm").serialize()+"&applyDeptId="+applyDeptId+"&workDeptId="+workDeptId+"&educationBackground="+JSON.stringify(educationBackgroundArray);
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveRecruit',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {


            let applyDeptId='';
            let workDeptId='';
            applyDeptId=deptSelect.getValue()[0].id;
            workDeptId=workDeptSelect.getValue()[0].id;
            if(applyDeptId==null || applyDeptId==''){
                layer.msg('请选择申请单位', {icon: 2, offset: 'auto'});
                return false;
            }
            if(workDeptId==null || workDeptId==''){
                layer.msg('请选择驻地单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var params=$("#noticeForm").serialize()+"&saveType=2&applyDeptId="+applyDeptId+"&workDeptId="+workDeptId;
            /*$.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveRecruit',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });*/
            if(taskId==''){
                params+='&saveType=2';
                util.sendAjax({
                    url:"#(ctxPath)/api/saveRecruit",
                    type:'post',
                    data:params,
                    notice:true,
                    success:function(returnData){
                        if(returnData.state==='ok'){
                            parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                            parent.reloadTable();
                            pop_close();
                        }
                    },
                    unSuccess:function (returnData) {
                        layer.closeAll('loading');
                    }
                });
            }else{
                util.sendAjax({
                    url:"#(ctxPath)/api/saveRecruit",
                    type:'post',
                    data:params,
                    notice:true,
                    success:function(returnData){
                        if(returnData.state==='ok'){
                            // layer.closeAll('loading');
                            // pop_close();
                            doTask(5,$("#msg").val());
                            //parent.cardTypeTableReload(null);
                            parent.reloadTable();
                        }
                    },
                    unSuccess:function (returnData) {
                        layer.closeAll('loading');
                    }
                });


            }
            return false;
        });


        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }

        form.on('radio(staffSupplementType)',function (obj) {
            if(obj.value=='4'){
                $("#staffSupplementOtherRemark").css('lay-verify','required');
            }else{
                $("#staffSupplementOtherRemark").css('lay-verify','');
            }

        })

        form.on('select(deptId)',function (d) {
            let dataStr=$("#deptId option[value='"+d.value+"']").attr("data");
            console.log(dataStr);
            let positionList=JSON.parse(dataStr);
            let positionStr="<option value=''>请选择</option>";
            $.each(positionList,function (index,item) {
                if(positionList.length==1){
                    positionStr+="<option value='"+item.id+"' selected>"+item.positionName+"</option>";
                }else{
                    positionStr+="<option value='"+item.id+"'>"+item.positionName+"</option>";
                }
            });
            console.log(positionStr);
            $("#positionId").html(positionStr);
            form.render('select')
        });

    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div #if(type!='H5') class="layui-col-md6 layui-form" style="padding-right: 10px;" #else class="layui-col-md12 layui-form" style="padding-right: 10px;min-width: 850px;"  #end id="content" >
            <div class="layui-collapse" >

                <div class="layui-row" style="padding: 10px 20px;">

                    #if((!isSaveHandle && taskId!=null) || !isEdit)
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>提交人</label>
                        <div class="layui-input-block">
                            <input type="text"  autocomplete="off" readonly value="#(submitUserName??)" class="layui-input"  #if(type=='H5') style="width: 100%" #end>
                            <!--<input type="hidden" name="createBy" value="#(model.createBy??)" >-->
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>提交人部门</label>
                        <div class="layui-input-block">
                            <input type="text"  autocomplete="off" readonly value="#(submitDeptName??)" class="layui-input" #if(type=='H5') style="width: 100%" #end>
                            <!--<input type="hidden" name="deptId"   value="#(model.deptId??)" >-->
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>提交人职位</label>
                        <div class="layui-input-block">
                            <input type="text"  autocomplete="off" readonly value="#(submitPositionName??)" class="layui-input" #if(type=='H5') style="width: 100%" #end>
                            <!--<input type="hidden" name="positionId"   value="#(model.positionId??)" >-->
                        </div>
                    </div>

                    #else
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>提交人</label>
                        <div class="layui-input-block">
                            <input type="text" name="userName" autocomplete="off" readonly value="#(employee.fullName??)" class="layui-input">
                            <input type="hidden" id="createBy" name="applyId" value="#(user.id??)" >
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>提交人部门</label>
                        <div class="layui-input-block">

                            <select name="deptId" lay-verify="required" id="deptId" lay-filter="deptId">
                                <option value="">请选择</option>
                                #for(deptRecord : deptRecordList)
                                <option value="#(deptRecord.deptId??)" data='#(deptRecord.positionListStr??)' #if(deptRecordList.size()??0==1 || model.deptId??==deptRecord.deptId??) selected #end>#(deptRecord.deptName??)</option>
                                #end
                            </select>

                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>提交人职位</label>
                        <div class="layui-input-block">

                            <select name="positionId" id="positionId" lay-verify="required">
                                <option value="">请选择</option>
                                <!--#if(deptRecordList.size()??0==1)
                                #for(deptRecord : deptRecordList)
                                #for(position : deptRecord.positionList)
                                <option value="#(position.id??)" #if(deptRecord.positionList.size()??0==1) selected #end>#(position.positionName??)</option>
                                #end
                                #end
                                #end-->
                                #for(position : submitUsePositionList)
                                <option value="#(position.id??)" #if(position.id==model.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>

                        </div>
                    </div>
                    #end

                    <div class="layui-form-item" style="display: none;">
                        <label class="layui-form-label"><font color="red">*</font>申请类型</label>
                        <div class="layui-input-block">
                            <select id="recruitCause" name="recruitCause" lay-filter="recruitCause" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <!--<option value="1" #if(model.destinationType??=='1') selected #end>公司单位</option>-->
                                <option value="1">增设岗位</option>
                                <option value="2">增加编制</option>
                                <option value="3">补充人员</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item" >
                        <label class="layui-form-label"><font color="red">*</font>申请单位</label>
                        <div class="layui-input-block" style="width: 387px;">
                            <div id="deptSelect"  name="deptSelect" style="margin-top: 1px;" >

                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item"  >
                        <label class="layui-form-label"><font color="red">*</font>职位名称</label>
                        <div class="layui-input-block">
                            <select id="applyPositionId" name="applyPositionId" lay-filter="applyPositionId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                #for(position : positionList)
                                <option value="#(position.id??)">#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>人数</label>
                        <div class="layui-input-block">
                            <input type="text" name="peopleNum" id="peopleNum" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#(model.peopleNum??)"  lay-verify="required|number" placeholder="请输入人数" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>性别</label>
                        <div class="layui-input-block">
                            <select id="gender" name="gender" lay-filter="gender" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <!--<option value="1" #if(model.destinationType??=='1') selected #end>公司单位</option>-->
                                <option value="">请选择</option>
                                <option value="1" #if(model.gender??''=='1') selected #end>男</option>
                                <option value="2" #if(model.gender??''=='2') selected #end>女</option>
                                <option value="3" #if(model.gender??''=='3') selected #end>不限</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>年龄范围</label>
                        <div class="layui-input-inline" style="width: 50px;float: left;margin-left: 0px;">
                            <input id="ageStart" name="ageStart"  lay-verify="required|number"  placeholder="" value="#(model.ageStart??)" autocomplete="off" class="layui-input">

                        </div>
                        <span style="float: left;margin-top: 10px;margin-right: 10px;">至</span>
                        <div class="layui-input-inline" style="width: 50px;float: left;margin-left: 0px;">
                            <input id="ageEnd" name="ageEnd"  lay-verify="required|number"  placeholder="" value="#(model.ageEnd??)" autocomplete="off" class="layui-input">
                        </div>
                        <span style="float: left;margin-top: 10px;margin-right: 10px;">岁</span>
                    </div>
                    <div class="layui-form-item" >
                        <label class="layui-form-label"><font color="red">*</font>驻地</label>
                        <div class="layui-input-inline" style="width: 387px;">
                            <div id="workDeptSelect"  style="margin-top: 1px;" >

                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>学历</label>
                        <div class="layui-input-block">
                            <!--<input type="checkbox" lay-verify="required" name="educationBackground" value="chu_zhong" title="初中及以下" #if(model.educationBackground??''.indexOf('chu_zhong')!=-1)checked#end  >
                            <input type="checkbox" lay-verify="required" name="educationBackground" value="zhong_zhuan" title="高中" #if(model.educationBackground??''.indexOf('zhong_zhuan')!=-1)checked#end  >
                            <input type="checkbox" lay-verify="required" name="educationBackground" value="da_zhuan" title="大专" #if(model.educationBackground??''.indexOf('da_zhuan')!=-1)checked#end >
                            <input type="checkbox" lay-verify="required" name="educationBackground" value="ben_ke" title="本科" #if(model.educationBackground??''.indexOf('ben_ke')!=-1)checked#end  >
                            <input type="checkbox" lay-verify="required" name="educationBackground" value="shuo_shi" title="硕士及以上" #if(model.educationBackground??''.indexOf('shuo_shi')!=-1)checked#end  >
                            -->
                            <div id="educationBackgroundSelect" class="xm-select-demo"></div>

                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>薪资范围</label>
                        <div class="layui-input-inline" style="width: 80px;float: left;margin-left: 0px;">
                            <input id="salaryStart" name="salaryStart"  lay-verify="required|number"  placeholder="" value="#(model.salaryStart??)" autocomplete="off" class="layui-input">

                        </div>
                        <span style="float: left;margin-top: 10px;margin-right: 10px;">至</span>
                        <div class="layui-input-inline" style="width: 80px;float: left;margin-left: 0px;">
                            <input id="salaryEnd" name="salaryEnd"  lay-verify="required|number"  placeholder="" value="#(model.salaryEnd??)" autocomplete="off" class="layui-input">
                        </div>
                        <span style="float: left;margin-top: 10px;margin-right: 10px;"></span>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>希望到岗时间</label>
                        <div class="layui-input-inline" style="width: 120px;float: left;margin-left: 0px;">
                            <input id="arrivalStartDate" name="arrivalStartDate"  lay-verify="required"  placeholder="" value="#date(model.arrivalStartDate??,'yyyy-MM-dd')" autocomplete="off" class="layui-input">

                        </div>
                        <span style="float: left;margin-top: 10px;margin-right: 10px;">至</span>
                        <div class="layui-input-inline" style="width: 120px;float: left;margin-left: 0px;">
                            <input id="arrivalEndDate" name="arrivalEndDate"  lay-verify="required"  placeholder="" value="#date(model.arrivalEndDate??,'yyyy-MM-dd')" autocomplete="off" class="layui-input">
                        </div>
                        <span style="float: left;margin-top: 10px;margin-right: 10px;"></span>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>职责概述</label>
                        <div class="layui-input-block">
                            <textarea name="dutyDescribe" id="dutyDescribe" placeholder="请输入职责概述"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.dutyDescribe??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>工作内容</label>
                        <div class="layui-input-block">
                            <textarea name="workContent" id="workContent" placeholder="请输入工作内容"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.workContent??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>专业要求</label>
                        <div class="layui-input-block">
                            <textarea name="professionRequirements" id="professionRequirements" placeholder="请输入专业要求"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.professionRequirements??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>工作经验</label>
                        <div class="layui-input-block">
                            <textarea name="workExperience" id="workExperience" placeholder="请输入工作经验"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.workExperience??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>专业职称要求</label>
                        <div class="layui-input-block">
                            <textarea name="professionalTitleRequirements" id="professionalTitleRequirements" placeholder="请输入专业职称要求"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.professionalTitleRequirements??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>技能和知识</label>
                        <div class="layui-input-block">
                            <textarea name="skillAndKnowledge" id="skillAndKnowledge" placeholder="请输入技能和知识"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.skillAndKnowledge??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>岗位要素</label>
                        <div class="layui-input-block">
                            <textarea name="postElement" id="postElement" placeholder="请输入岗位要素"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.postElement??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label"><font color="red">*</font>申请增加岗位、编制、补充人员理由</label>
                        <div class="layui-input-block">
                            <textarea name="recruitCauseDescribe" id="recruitCauseDescribe" placeholder="请输入申请增加岗位、编制、补充人员理由"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.recruitCauseDescribe??)</textarea>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>人员补充方式</label>
                        <div class="layui-input-block" >
                            <input type="radio" name="staffSupplementType" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-filter="staffSupplementType" value="1" title="内部调配" lay-verify="required" #if(model.staffSupplementType??=='1') checked #end>
                            <input type="radio" name="staffSupplementType" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-filter="staffSupplementType" value="2" title="内部竞聘" lay-verify="required" #if(model.staffSupplementType??=='2') checked #end>
                            <input type="radio" name="staffSupplementType" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-filter="staffSupplementType" value="3" title="外部招聘" lay-verify="required" #if(model.staffSupplementType??=='3') checked #end>
                            <input type="radio" name="staffSupplementType" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-filter="staffSupplementType" value="4" title="其他" lay-verify="required" #if(model.staffSupplementType??=='4') checked #end>
                            <input id="staffSupplementOtherRemark" name="staffSupplementOtherRemark" #if(!isSaveHandle && taskId!=null) disabled readonly #end style="display: inline-block;width: 200px;" #if(model.staffSupplementType??=='4') lay-verify="required"  #else lay-verify="" #end    placeholder="" value="#(model.staffSupplementOtherRemark??)" autocomplete="off" class="layui-input">
                        </div>
                        <!--<div class="layui-input-inline" style="width: 80px;">
                            <input id="salaryEnd" name="salaryEnd"  lay-verify="required|number"  placeholder="" value="" autocomplete="off" class="layui-input">
                        </div>-->
                    </div>

                    <div class="layui-form-item layui-form-text" style="margin-bottom: 60px;">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(model.remark??)</textarea>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        #if(type!='H5')
        <div class="layui-col-md6">
            #if(stepts.size()??0>0)
            <table class="layui-table" id="steptsTable">
                <!--<colgroup>
                    <col width="10%">
                    <col width="13%">
                    <col width="30%">
                    <col width="30%">
                    <col width="17%">
                </colgroup>-->
                <thead>
                <tr>
                    <th style="text-align: center;">序号</th>
                    <th style="text-align: center;">节点</th>
                    <th style="text-align: center;">流程</th>
                    <th style="text-align: center;">意见</th>
                    <th style="text-align: center;">操作时间</th>
                    <th style="text-align: center;">操作人</th>
                </tr>
                </thead>
                <tbody>
                #for(stept:stepts)
                <tr>
                    <td align="center">#(for.index+1)</td>
                    <td>#(stept.ActivityName)</td>
                    <td align="center">
                        #if(stept.StepState==3)
                        提交
                        #else if(stept.StepState==1)
                        待处理
                        #else if(stept.StepState==0)
                        等待
                        #else if(stept.StepState==2)
                        正处理
                        #else if(stept.StepState==4)
                        撤回
                        #else if(stept.StepState==5)
                        批准
                        #else if(stept.StepState==6)
                        拒绝
                        #else if(stept.StepState==7)
                        转移
                        #else if(stept.StepState==8)
                        失败
                        #else if(stept.StepState==9)
                        跳过
                        #end
                    </td>
                    <td>#(stept.Comment)</td>
                    <td>#(stept.CommentTime)</td>
                    <td align="center">#(stept.CommentUserName)</td>
                </tr>
                #end
                </tbody>
            </table>

            <form class="layui-form layui-form-pane" id="taskForm">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">意见</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-input-block pull-right">

                        <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                        <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>
                        <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject) disabled style="display: none;" #end>拒绝</button>
                        <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove) disabled style="display: none;" #end>通过</button>
                    </div>
                </div>
            </form>
            #end
        </div>
        #end
        <div class="layui-form-footer">
            <div #if(type!='H5') class="pull-right" #else class="pull-left"  #end>
                #if((isSaveHandle || taskId==null) && isEdit)
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                #end
                #if((isSaveHandle || taskId==null) && isEdit)
                #if(type!='H5')
                <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
                #end
                #end

                #if(type!='H5')
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                #end
                <input type="hidden" name="id" value="#(model.id??)">
                <input type="hidden" id="taskId" value="#(taskId??)">
                <input type="hidden" id="userId"  name="userId" value="#(user.id??)" >
            </div>
            <div #if(type!='H5') class="pull-right"  #end >
                <div class="layui-form-mid layui-word-aux" style="margin-left: 10px;">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
            </div>

        </div>
    </form>
</div>

</body>
#end