#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url: '#(ctxPath)/persOrg/orgFormTree'}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    /*layer.confirm('确定要分配所选择的机构？', function(index) {
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/persOrgEmployee/orgEmpSave',
                            data: {empId:'#(empId??)', orgId:treeNode.id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                pageTableReload();
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });*/
                    $("#orgId").val(treeNode.id);
                    var data={'positionName':$("#positionName").val(),'orgId':treeNode.id};
                    positionLoad(data);
                },
                onAsyncSuccess: zTreeOnAsyncSuccess
            }
        };
        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);

        var firstAsyncSuccessFlag = 0;
        function zTreeOnAsyncSuccess(event, treeId, msg) {

            try {
                //调用默认展开第一个结点
                var selectedNode = zTreeObj.getSelectedNodes();
                var nodes = zTreeObj.getNodes();
                zTreeObj.expandNode(nodes[0], true);
                var childNodes = zTreeObj.transformToArray(nodes[0]);
                zTreeObj.expandNode(childNodes[0], true);
                zTreeObj.selectNode(childNodes[0]);
                var childNodes1 = zTreeObj.transformToArray(childNodes[0]);
                zTreeObj.checkNode(childNodes1[0], true, true);
                firstAsyncSuccessFlag = 1;

                var data={'orgId':nodes[0].id};
                $("#orgId").val(nodes[0].id);
                positionLoad(data);
            } catch (err) {

            }

        }




        sd=form.on("submit(search)",function(data){
            //positionLoad();
            positionTableReload();
            return false;
        });

        function positionLoad(data){
            table.render({
                id : 'positionTable'
                ,elem : '#positionTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/pers/branchCompanyRole/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'orgName', title: '所属分公司', align: 'center', unresize: true,templet:function (d) {
                            var orgName=d.org.org_name;
                            if(orgName!=null && orgName!="" && typeof(orgName)!="undefined"){
                                return orgName;
                            }
                            return "- -";
                        }}
                    ,{field:'name', title: '分公司角色名称', align: 'center', unresize: true}
                    ,{field:'createDate', title: '创建时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 250, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        positionTableReload=function(){
            var data={'positionName':$("#positionName").val(),'orgId':$("#orgId").val()};
            table.reload('positionTable',{'where':data});
        }

        // 添加
        $("#add").click(function(){
            var orgId=$("#orgId").val();
            if(orgId=='' || orgId==null){
                layer.msg('请选择所属的组织', {icon: 2, offset: 'auto'});
                return;
            }
            $(this).blur();
            var url = "#(ctxPath)/pers/branchCompanyRole/form?orgId="+orgId ;
            pop_show("新增分公司角色",url,500,350);
        });

        $("#import").click(function () {
            var orgId=$("#orgId").val();
            if(orgId=='' || orgId==null){
                layer.msg('请选择分公司', {icon: 2, offset: 'auto'});
                return;
            }
            $(this).blur();
            var url = "#(ctxPath)/pers/branchCompanyRole/importPerformance?orgId="+orgId ;
            pop_show("导入",url,650,500);

        });

        table.on('tool(positionTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/branchCompanyRole/save',
                        notice: true,
                        data: {id:obj.data.id,"delFlag":"1"},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                positionTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var orgId=$("#orgId").val();
                var url = "#(ctxPath)/pers/branchCompanyRole/form?id=" + obj.data.id+"&orgId="+orgId ;
                pop_show("编辑分公司角色",url,500,350);
            }else if(obj.event==='calculate'){
                var url = "#(ctxPath)/pers/branchCompanyRole/salaryConfigIndex?roleId=" + obj.data.id ;
                pop_show("编辑工资计算公式",url,1000,600);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="calculate">工资计算配置</a>
    #shiroHasPermission("position:manage:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("position:manage:voidBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="layui-col-xs3 layui-col-sm3 layui-col-md3 layui-col-lg3">
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>组织架构</legend>
            <div id="zTreeDiv" class="ztree" style="height:550px;overflow:auto;"></div>
        </fieldset>
    </div>
    <div class="layui-col-xs9 layui-col-sm9 layui-col-md9 layui-col-lg9">
        <div class="layui-row">
            <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
                分公司角色名称:
                <div class="layui-inline">
                    <input id="roleName" name="roleName" class="layui-input">
                </div>
                &nbsp;&nbsp;
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            </form>
            <input type="hidden" name="orgId" id="orgId" value="">
            #shiroHasPermission("position:manage:addBtn")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
            #end
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="import">导入</button>

        </div>
        <table class="layui-table" id="positionTable" lay-filter="positionTable"></table>
    </div>


</div>
#end