#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
	<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>审核人类型</label>
				<div class="layui-input-block">
					<select name="reviewerType" lay-verify="required" lay-search="">
						<option value="">请选择</option>
						#dictOption("reviewer_type", model.reviewerType??'', "")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>审核人</label>
				<div class="layui-input-block">
					<select name="empId" lay-verify="required" lay-search="">
						<option value="">请选择审核人</option>
						#for(empUser : empUserList)
							<option value="#(empUser.id)|#(empUser.user_id)" #(empUser.id?? == model.empId??'' ? 'selected':'')>#(empUser.full_name)</option>
						#end
					</select>
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??'')">
					<input type="hidden" name="orgId" value="#(model.orgId??'')">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="closeEditTab();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	, $ = layui.$
	;
	
	form.render();
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/persOrg/saveOrgReviewer',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		closeEditTab();
            		pageTableReload();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end