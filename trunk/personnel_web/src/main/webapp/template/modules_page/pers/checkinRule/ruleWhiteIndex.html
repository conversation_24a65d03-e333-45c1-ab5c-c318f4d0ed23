#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()车型管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 5px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:right;margin-top:10px;margin-right: 50px;">
        <div class="layui-row" style="">
            <!--<label style="line-height: 40px;width: 140px;padding: 0 20px;">角色名称</label>
            <input class="layui-input" name="roleName" id="roleName" placeholder="" autocomplete="off">
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>-->
            #shiroHasPermission("emp:checkinRule:white:addBtn")
            <button class="layui-btn"  type="button" id="add">添加</button>
            #end

        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="carTypeTable" lay-filter="carTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'carTypeTable',
            elem: '#carTypeTable'
            ,url : '#(ctxPath)/employeeCheckinRule/checkinRuleWhitePage'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {field: 'roleName', title: '角色名称',align:'center',unresize:true}
                ,{field: 'createDate', title: '添加时间',align:'center',unresize:true}
                ,{title: '操作',toolbar:'#toolBar',align:'center',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(carTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/pers/role/form?id=" + obj.data.id ;
                layerShow("编辑类型",url,550,600);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/employeeCheckinRule/saveCheckinRuleWhite",
                        type:'post',
                        data:{"id":obj.data.id,"delFlag":"1"},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                carTypeTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });



        carTypeTableReload=function(){
            var name=$("#name").val();
            table.reload('carTypeTable',{'where':{'name':name}});
        }

        $("#search").on('click',function () {
            carTypeTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/employeeCheckinRule/ruleWhiteForm";
            layerShow("添加类型",url,500,400);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("emp:checkinRule:white:delBtn")
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
    #end

</script>
#end