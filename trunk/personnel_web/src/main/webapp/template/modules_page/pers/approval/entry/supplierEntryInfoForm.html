#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()入职员工信息表#end

#define css()
<style>

    #steptsTable td,#steptsTable th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end


#define js()
<script type="text/html" id="familySituationTpl">
    <tr id="familySituationTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="familySituation[{{d.idx}}].name" class="layui-input" lay-verify="required" autocomplete="off" value=""></td>
        <td><input name="familySituation[{{d.idx}}].relation" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="familySituation[{{d.idx}}].workUnit" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="familySituation[{{d.idx}}].phone" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="familySituation[{{d.idx}}].position" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td align="center">
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('familySituation',{{d.idx}},'')" >作废</button>
        </td>
    </tr>
</script>
<script type="text/html" id="educationalBackgroundTpl">
    <tr id="educationalBackgroundTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="educationalBackground[{{d.idx}}].startDate" id="educationalBackground-{{d.idx}}-startDate" class="layui-input" lay-verify="required" autocomplete="off" value=""></td>
        <td><input name="educationalBackground[{{d.idx}}].endDate" id="educationalBackground-{{d.idx}}-endDate" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="educationalBackground[{{d.idx}}].school" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="educationalBackground[{{d.idx}}].major" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="educationalBackground[{{d.idx}}].diploma" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td align="center">
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('educationalBackground',{{d.idx}},'')" >作废</button>
        </td>
    </tr>
</script>
<script type="text/html" id="trainingBackgroundTpl">
    <tr id="trainingBackgroundTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="trainingBackground[{{d.idx}}].startDate" id="trainingBackground-{{d.idx}}-startDate" class="layui-input" lay-verify="required" autocomplete="off" value=""></td>
        <td><input name="trainingBackground[{{d.idx}}].endDate" id="trainingBackground-{{d.idx}}-endDate" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="trainingBackground[{{d.idx}}].course" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td>
            <select id="learningFormSelect-[{{d.idx}}]" name="trainingBackground[{{d.idx}}].learningForm" lay-verify="required">
                <option value="">请选择学习形式</option>
                #dictOption("learning_form", model.learningForm??'', "")
            </select>
        </td>
        <td><input name="trainingBackground[{{d.idx}}].position" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td align="center">
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('trainingBackground',{{d.idx}},'')" >作废</button>
        </td>
    </tr>
</script>
<script type="text/html" id="employmentHistoryTpl">
    <tr id="employmentHistoryTr-{{d.idx}}" index="{{d.idx}}">
        <td><input name="employmentHistory[{{d.idx}}].startDate" id="employmentHistory-{{d.idx}}-startDate" class="layui-input" lay-verify="required" autocomplete="off" value=""></td>
        <td><input name="employmentHistory[{{d.idx}}].endDate" id="employmentHistory-{{d.idx}}-endDate" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="employmentHistory[{{d.idx}}].company" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="employmentHistory[{{d.idx}}].position" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="employmentHistory[{{d.idx}}].quitReason" lay-verify="required" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="employmentHistory[{{d.idx}}].salary" lay-verify="required|number" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="employmentHistory[{{d.idx}}].witness" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td><input name="employmentHistory[{{d.idx}}].phone" lay-verify="" class="layui-input" value="" autocomplete="off"></td>
        <td align="center">
            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('employmentHistory',{{d.idx}},'')" >作废</button>
        </td>
    </tr>
</script>



<script type="text/javascript">

layui.extend({
    treeSelect: '/static/js/extend/treeSelect'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use(['form','jquery','laydate','laytpl','treeSelect','upload','layer'], function(){
    var form = layui.form,$ = layui.jquery,laydate=layui.laydate,layer=layui.layer,laytpl=layui.laytpl,treeSelect=layui.treeSelect,upload=layui.upload;

    $(function () {
        var select = document.getElementById('employeeType');
        var optionsToHide = ['part_time', 'trainee','full_time','temporary']; // 这里列出你想要隐藏的option的value值
        $.each($(select).find('option'),function (index,item) {
            if (optionsToHide.includes($(this).val())) {
                $('#employeeType option[value="'+$(this).val()+'"]').remove();
            }
        })

        form.render('select')
    })

    form.on('select(employeeType)',function (d) {
        if(d.value=='agency'){
            $("#agentIdDiv").css('display','inline-block');
            $("#agentId").attr("lay-verify","required");
        }else{
            $("#agentIdDiv").css('display','none');
            $("#agentId").attr("lay-verify","");
        }
    })

    laydate.render({
        elem : '#birthday',
        type: 'date',
        trigger: 'click'
    });

    laydate.render({
        elem : '#entryTime',
        type: 'date',
        trigger: 'click'
    });
    laydate.render({
        elem : '#formalDate',
        type: 'date',
        trigger: 'click'
    });

    laydate.render({
        elem: '#contractStartDate'
        , trigger: 'click'
    });

    laydate.render({
        elem: '#contractEndDate'
        , trigger: 'click'
    });

    $('#idcard').on('keyup', function() {
        console.log(1);
        if($("#idCardType").val()==='大陆居民身份证'){
            var idCard=$(this).val();
            if(idCard.length==15 || idCard.length==18){
                var sexNum=idCard.substring(idCard.length-2,idCard.length-1);
                $("#gender").find("option:selected").removeAttr('selected');
                if(sexNum%2==1){
                    $("#gender").find("option[value='male']").prop('selected',true);
                }else{
                    $("#gender").find("option[value='female']").prop('selected',true);
                }
                form.render('select');
            }

            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                setAge(birthday);
                $('#birthday').val(birthday);
            }
        }
    });

    //身份证联动出生日期
    function idCardBirthday(psidno){
        var birthdayno,birthdaytemp;
        if (psidno.length == 18) {
            birthdayno=psidno.substring(6,14)
        } else if (psidno.length == 15) {
            birthdaytemp = psidno.substring(6,12);
            birthdayno = "19" + birthdaytemp;
        }else{
            return false
        }
        var birthday = birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8);
        return birthday
    }

    function setAge(birthday) {
        var returnAge,
            strBirthdayArr=birthday.split("-"),
            birthYear = strBirthdayArr[0],
            birthMonth = strBirthdayArr[1],
            birthDay = strBirthdayArr[2],
            d = new Date(),
            nowYear = d.getFullYear(),
            nowMonth = d.getMonth() + 1,
            nowDay = d.getDate();
        if(nowYear == birthYear){
            returnAge = 0;//同年 则为0周岁
        }else{
            var ageDiff = nowYear - birthYear ; //年之差
            if(ageDiff > 0){
                if(nowMonth == birthMonth) {
                    var dayDiff = nowDay - birthDay;//日之差
                    if(dayDiff < 0) {
                        returnAge = ageDiff - 1;
                    }else {
                        returnAge = ageDiff;
                    }
                }else {
                    var monthDiff = nowMonth - birthMonth;//月之差
                    if(monthDiff < 0) {
                        returnAge = ageDiff - 1;
                    }
                    else {
                        returnAge = ageDiff ;
                    }
                }
            }else {
                returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
            }
        }
        $("#age").val(returnAge);
    }


     showPhotos=function(images) {
        layer.photos({
            /*area: '400px',*/
            shade: [0.85, '#000'],
            anim: 0,
            photos: {
                "title": "附件预览",
                "id": 'showImages',
                "data": images
            }
            ,tab:function () {
                num=0;
                $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                    '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                    '\t</div>');
                $(document).on("click", "#xuanzhuan", function(e) {
                    num = (num+90)%360;
                    $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                    $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                });

                $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                    var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                        (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                    var imagep = $(".layui-layer-phimg").parent().parent();
                    var image = $(".layui-layer-phimg").parent();
                    var h = image.height();
                    var w = image.width();
                    if (delta > 0) {
                        if (h < (window.innerHeight)) {
                            h = h * 1.05;
                            w = w * 1.05;
                        }
                    } else if (delta < 0) {
                        if (h > 100) {
                            h = h * 0.95;
                            w = w * 0.95;
                        }
                    }
                    imagep.css("top", (window.innerHeight - h) / 2);
                    imagep.css("left", (window.innerWidth - w) / 2);
                    image.height(h);
                    image.width(w);
                    imagep.height(h);
                    imagep.width(w);
                });
            }
        });
    }

    laydateRender=function (id) {
        laydate.render({
            elem : '#'+id,
            type: 'date',
            trigger: 'click'
        });
    }



    //校验
    form.verify({
        phoneNumber: function(value){
            if(value!=null && value!=''){
                var mobilePtn = /^1[3456789][0-9]{9}$/;
                var landlinePtn = /^(0\d{2,3}-)?\d{7,8}$/;
                if(!mobilePtn.test(value) ){
                    return '请输入正确的手机号码';
                }
            }
        },
        checkFormalDate:function (value) {
            if(value!=null && value!=''){
                var entryTime=new Date($("#entryTime").val());
                var formalDate=new Date(value);
                if(entryTime>formalDate){
                    //layer.msg('', {icon: 2, offset: 'auto'});
                    return '转正日期不能小于入职日期';
                }
            }
        },
        checkName:function (value) {
            if(value!=null && value!=''){
                var regex = new RegExp("^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z]){1,20}$");
                if(!regex.test(value)){
                    return '只能输入汉字和英文';
                }
            }
        },
        checkIdCard:function (value) {

            if($("#idCardType").val()==='sheng_fen_zheng'){
                var idCard=/(^\d{15}$)|(^\d{17}(x|X|\d)$)/
                if(!idCard.test(value)){
                    return '请输入正确的身份证号';
                }
            }else{
                var idCard=/^[\u4E00-\u9FA5]+$/;
                if(idCard.test(value)){
                    return '请输入正确的港澳台证件号码';
                }
            }

        },
        checkMoney:function (value) {
            if(value!=null && value!=''){
                var money=/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
                if(!money.test(value)){
                    return '请输入正确的金额格式';
                }
            }
        },
        checkEmail:function (value) {
            if(value!=null && value!=''){
                var email=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
                if(!email.test(value)){
                    return '邮箱格式不正确';
                }
            }
        },
        checkDateSize:function (value) {
            if(value!=null && value!=''){
                var entryTime=new Date($("#contractStartDate").val());
                var formalDate=new Date(value);
                if(entryTime>formalDate){
                    //layer.msg('', {icon: 2, offset: 'auto'});
                    return '合同到期日期不能大于签订日期';
                }
            }
        }
    });

    //保存
    form.on('submit(saveBtn)', function(obj){
        layer.load();
        var url = "#(ctxPath)/pers/approval/saveEmployeeEntryInfo";

        $.post(url,$("#formId").serialize(),function (rep) {
            console.log(rep);
            if(rep.state=='ok'){
                pop_close();
                parent.reloadTable();
            }else{
                layer.msg(rep.msg, {icon: 2, offset: 'auto'});
                layer.closeAll("loading");
            }
        });

       /* util.sendAjax ({
            type: 'POST',
            url: url,
            data: $("#formId").serialize(),
            notice: false,
            loadFlag: false,
            success : function(rep){
                console.log(rep);
                if(rep.state=='ok'){
                    pop_close();
                    parent.reloadTable();
                }else{
                    layer.msg(rep.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll("loading");
                }

            },
            complete : function() {
            }
        });*/

        return false;
    });

    //保存员工入职记录流程
    doTask=function (stepState,msg) {
        var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
        util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/pers/approval/doTask',
            data: data,
            notice: true,
            loadFlag: true,
            success : function(rep){
                if(rep.state=='ok'){
                    parent.reloadTable();
                    pop_close();
                }
            },
            complete : function() {
            }
        });
    }
    //附件
    $("#enclosure").on('click',function () {
        pop_show('附件', '#(ctxPath)/persOrgEmployee/enclosureIndex?employeeId=' +$("#employeeId").val() , 900, 600);
        return false;
    });
    form.on('submit(submit)',function (obj) {
        layer.confirm("确定要提交吗?",function(index){
            layer.load();
            doTask(5,$("#msg").val());
            layer.closeAll("loading");
            layer.close(index);
        });
        return false;
    })
    //保存并提交
    form.on('submit(saveSubmitBtn)', function(obj){
        layer.confirm('您确定要保存并提交吗？提交流程后不可修改', function(index) {
            layer.load();
            var url = "#(ctxPath)/pers/approval/saveEmployeeEntryInfo";
            var data=$("#formId").serialize()+"&saveType=2";
            /*if($("#taskId").val()!='' && $("#msg").val()==''){
                layer.msg("请填写流程备注内容", {icon: 2, offset: 'auto'});
                layer.closeAll("loading");
                return false;
            }*/
            data+="&msg="+$("#msg").val();
            $.post(url,data,function (rep) {
//                 console.log(rep);
                if(rep.state=='ok'){
                    pop_close();
                    parent.reloadTable();
                }else{
                    layer.msg(rep.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll("loading");
                }
            });
            /*util.sendAjax ({
                type: 'POST',
                url: url,
                data: data,
                notice: false,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }else{
                        layer.msg(rep.msg, {icon: 2, offset: 'auto'});
                        layer.closeAll('loading');
                    }

                },
                complete : function() {
                }
            });*/

            layer.close(index);
        });

        return false;
    });


    treeSelect.render({
        // 选择器
        elem: "#deptId",
        // 数据
        data: '#(ctxPath)/persOrg/orgXmSelectTree',
        // 异步加载方式：get/post，默认get
        type: 'get',
        // 占位符
        placeholder: '请选择部门',
        // 是否开启搜索功能：true/false，默认false
        search: true,
        // 点击回调
        click: function(d){
            var orgType=d.current.type;

            /*if("branche_office"==orgType){
                $("#contractDiv").hide();
                $("#dateType").attr("lay-verify","");
                $("#contractAddr").attr("lay-verify","");
                $("#contractEndDate").attr("lay-verify","");
                $("#contractStartDate").attr("lay-verify","");
                $("#contractType").attr("lay-verify","");
            }else{
                $("#contractDiv").show();
                $("#contractAddr").attr("lay-verify","required");
                $("#contractEndDate").attr("lay-verify","required");
                $("#contractStartDate").attr("lay-verify","required");
                $("#contractType").attr("lay-verify","required");
                $("#dateType").attr("lay-verify","required");
            }*/

            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/position/getPositionByOrgId',
                data: {'orgId':d.current.id},
                notice: false,
                loadFlag: false,
                success : function(rep){
                    if(rep.state==='ok'){
                        var str='<option value="" role-id="">请选择职位</option>';
                        var data=rep.data;
                        $.each(data,function (index,item) {
                            str+='<option value="'+item.id+'" role-id="'+item.roleId+'" role-name="'+item.roleName+'">'+item.positionName+'</option>';
                        });
                        $("#positionId").html(str);
                        form.render('select');
                    }
                },
                complete : function() {
                }
            });
        },
        // 加载完成后的回调函数
        success: function (d) {
            if('#(model.deptId??)'!=''){
                treeSelect.checkNode('tree', '#(model.deptId??)');
                form.render('select');
            }

        }
    });

    form.on('select(contractType)',function (obj) {
        if("1"==obj.value){
            $("#contract6Div").show();
        }else if("2"==obj.value){
            $("#contract6Div").hide();
        }
    })



    if("branche_office"=="#(dept.orgType??)"){
        setTimeout(function (){
            contractTypeSelect('4',$("#dateType").val());
        },500);
    }

    if("2"=="#(model.contractType??)"){
        $("#contract6Div").hide();
    }else{
        $("#contract6Div").show();
    }

    /*function loadTreeSelect(id,checkId){

    }*/

    form.on('select(positionId)',function (obj) {
        var roleId=$("#positionId").find("option[value='"+obj.value+"']").attr("role-id");
        $("#roleId").val(roleId);

        loadCertificateBoxByPositionId(obj.value);

    });

    loadCertificateBoxByPositionId=function(positionId){
        //加载需要填写的证件类型
        $.post('#(ctxPath)/pers/employeeCertificate/getCertificateTypeByPositionId?positionId='+positionId+"&entryInfoId="+$("#id").val(),'',function (res) {
            $("#certificateDiv").html('');
            if(res.state=='ok' && res.data.length>0){
                let certificateTypeData=[]
                $.each(res.data,function (index,item) {
                    certificateTypeData.push({"id":item.id,"fileCount":item.fileCount,"isEffectiveDate":item.isEffectiveDate,"isEntryRequired":item.isEntryRequired,"name":item.name});
                    let str=`<fieldset class="layui-elem-field" style="margin-top:20px;margin-bottom:40px;">
                                <legend style="font-size: 14px;margin-bottom:10px;"><b>`+item.name+`证件信息</b></legend>
                                <div class="layui-form-item" style="margin-left: 10px;">`
                    let requiredStr='';
                    let requiredLog='';
                    if(item.isEntryRequired=='1'){
                        requiredStr='lay-verify="required"';
                        requiredLog='*';
                    }


                    //是否有有效期
                    if(item.isEffectiveDate=='1'){
                        str+=`<div class="layui-inline">
                                        <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">`+requiredLog+`</font>有效期开始日期</label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="startDate-`+item.id+`" name="startDate-`+item.id+`"  `+requiredStr+`      class="layui-input"  placeholder="请选择`+item.name+`有效开始日期">

                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">`+requiredLog+`</font>有效期结束日期</label>
                                        <div class="layui-input-inline">
                                            <input type="text" id="endDate-`+item.id+`" name="endDate-`+item.id+`" class="layui-input" `+requiredStr+`    placeholder="请选择`+item.name+`有效结束日期">
                                        </div>
                                    </div>`;
                    }
                    str+=`</div>
                        <div class="layui-form-item" style="margin-bottom: 60px;display: flex;align-items: center;flex-wrap: wrap;">
                    `;

                    if(item.fileCount>0){
                        let files=[];
                        if(item.data.files!=undefined){
                            files=JSON.parse(item.data.files);
                        }
                        for (var i=1;i<=item.fileCount;i++){
                            let fileUrl='';
                            if(files.length>=i){
                                fileUrl=files[i-1];
                            }

                            str+=`
                                <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;margin-bottom: 50px;">
                                    <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                        <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                            <img class="layui-upload-img" id="certificateImg_`+item.id+`_`+i+`" style="max-height: 100%;max-width: 100%;cursor:pointer;" src="`+fileUrl+`" >
                                            #if(model==null)
                                            <p >请上传`+item.name+`图片`+i+`</p>
                                            #end
                                            <input type="hidden" id="certificatePath_`+item.id+`_`+i+`" name="certificatePath_`+item.id+`_`+i+`"   value="`+fileUrl+`">
                                        </div>
                                    </div>
                                    <button type="button" class="layui-btn certificateUploadBtn" data-id="`+item.id+`" data-index="`+i+`"  id="certificateUploadBtn_`+item.id+`_`+i+`" style="position:absolute;bottom: -25%;left: 35%;">上传`+item.name+`图片`+i+`</button>
                                </div>
                            `;
                        }
                    }

                    str+=`</div></fieldset>`;

                    $("#certificateDiv").append(str);

                    if(item.isEffectiveDate=='1') {

                        let startDate='';
                        let endDate='';
                        if(item.data.startDate!=undefined){
                            startDate=item.data.startDate;
                        }
                        if(item.data.endDate!=undefined){
                            endDate=item.data.endDate;
                        }

                        laydate.render({
                            elem: '#startDate-'+item.id,
                            type: 'date',
                            trigger: 'click',
                            value:startDate.substring(0,10)
                        });
                        laydate.render({
                            elem: '#endDate-'+item.id,
                            type: 'date',
                            trigger: 'click',
                            value:startDate.substring(0,10)
                        });
                    }
                });
                loadCertificateUpload();
                $("#certificateTypeData").val(JSON.stringify(certificateTypeData));
            }
        })
    }


    #for(educationalBackground : educationalBackgroundList)
    laydateRender('educationalBackground-#(for.index+1)-startDate');
    laydateRender('educationalBackground-#(for.index+1)-endDate');
    #end

    #for(employmentHistory : employmentHistoryList)
    laydateRender('employmentHistory-#(for.index+1)-startDate');
    laydateRender('employmentHistory-#(for.index+1)-endDate');
    #end

    #for(trainingBackground : trainingBackgroundList)
    laydateRender('trainingBackground-#(for.index+1)-startDate');
    laydateRender('trainingBackground-#(for.index+1)-endDate');
    #end

    #if(model!=null)
    loadCertificateBoxByPositionId($("#positionId").val());
    #end

    #if(model!=null && taskId!=null && !isSaveHandle??)
        setTimeout(function () {
            $("#formId button").prop("disabled",true);
            $("#formId button").addClass("layui-btn-disabled")
            // 				$("#giveSchemeDetail").prop("disabled",false);
            // 				$("#giveSchemeDetail").removeClass("layui-btn-disabled");
            $("#formId .layui-form-footer").hide();
            $("#formId").find("input,select,textarea").prop("disabled",true).prop("readonly",true);
            $("#formId select").removeAttr("lay-search");
            form.render('select');
        }, 300);

    #end

        addLaytpl=function(innerHTML,TbodyId,countId){
            var idx=Number($("#"+countId).val())+1;
            //table添加一行
            laytpl(innerHTML).render({"idx": idx}, function(html){
                $("#"+TbodyId).append(html);
                if(TbodyId==='educationalBackgroundTbody'){
                    laydateRender("educationalBackground-"+idx+"-startDate");
                    laydateRender("educationalBackground-"+idx+"-endDate");
                }else if(TbodyId==='trainingBackgroundTbody'){
                    laydateRender("trainingBackground-"+idx+"-startDate");
                    laydateRender("trainingBackground-"+idx+"-endDate");
                }else if(TbodyId==='employmentHistoryTbody'){
                    laydateRender("employmentHistory-"+idx+"-startDate");
                    laydateRender("employmentHistory-"+idx+"-endDate");
                }
            });
            $("#"+countId).val(idx);
        }

        $("#addFamilySituationBtn").on('click',function () {
            addLaytpl(familySituationTpl.innerHTML,"familySituationTbody","familySituationCount");
        })
        $("#addEducationalBackgroundBtn").on('click',function () {
            addLaytpl(educationalBackgroundTpl.innerHTML,"educationalBackgroundTbody","educationalBackgroundCount");
        })
        $("#addTrainingBackgroundBtn").on('click',function () {
            addLaytpl(trainingBackgroundTpl.innerHTML,"trainingBackgroundTbody","trainingBackgroundCount");
            form.render();
        })
        $("#addEmploymentHistoryBtn").on('click',function () {
            addLaytpl(employmentHistoryTpl.innerHTML,"employmentHistoryTbody","employmentHistoryCount");
        })


        del=function(type,index,id){
            if('familySituation'==type){
                if(id==''){
                    $("#familySituationTbody #familySituationTr-"+index).remove();
                }else{
                    layer.confirm('您确定要作废吗？', function(index) {
                        //作废操作
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/pers/approval/delFamilySituation',
                            data: {'id':id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    $("#familySituationTr-"+index).remove();
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });
                }
            }else if('educationalBackground'==type){
                if(id==''){
                    $("#educationalBackgroundTbody #educationalBackgroundTr-"+index).remove();
                }else{
                    layer.confirm('您确定要作废吗？', function(index) {
                        //作废操作
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/pers/approval/delEducationalBackground',
                            data: {'id':id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    $("#educationalBackgroundTr-"+index).remove();
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });
                }
            }else if('trainingBackground'==type){
                if(id==''){
                    $("#trainingBackgroundTbody #trainingBackgroundTr-"+index).remove();
                }else{
                    layer.confirm('您确定要作废吗？', function(index) {
                        //作废操作
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/pers/approval/delTrainingBackground',
                            data: {'id':id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    $("#trainingBackgroundTr-"+index).remove();
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });
                }
            }else if('employmentHistory'==type){
                if(id==''){
                    $("#employmentHistoryTbody #employmentHistoryTr-"+index).remove();
                }else{
                    layer.confirm('您确定要作废吗？', function(index) {
                        //作废操作
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/pers/approval/delEmploymentHistory',
                            data: {'id':id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    $("#employmentHistoryTr-"+index).remove();
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });
                }
            }


        }


    $(".contractUploadBtn").each(function () {
        var dataIndex=$(this).attr("data-index");
        //员工头像上传
        var uploadInst = upload.render({
            elem: this
            ,url: '#(fileUploadUrl)?bucket=employeeContract'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#contractImg'+dataIndex).attr('src', result);
                });
                layer.load();
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){

                    $("#contractPath"+dataIndex).val(res.data.src);
                    $("#fileId").val(res.data.id);
                    layer.msg(res.msg,{icon:1,time:5000});
                }else {
                    return layer.msg('上传失败');
                }
                layer.closeAll("loading");
            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
                layer.closeAll("loading");
            }
        });
    });

    loadCertificateUpload=function(){
        $(".certificateUploadBtn").each(function () {
            var dataIndex=$(this).attr("data-index");
            var dataId=$(this).attr("data-id");
            //员工头像上传
            var uploadInst = upload.render({
                elem: this
                ,url: '#(fileUploadUrl)?bucket=employeeContract'
                ,before: function(obj){
                    $("#confirmBtn").attr("disabled",true);
                    $("#confirmBtn").addClass("layui-btn-disabled");
                    //预读本地文件示例，不支持ie8
                    obj.preview(function(index, file, result){
                        $('#certificateImg_'+dataId+'_'+dataIndex).attr('src', result);
                    });
                    layer.load();
                }
                ,done: function(res){
                    $("#confirmBtn").attr("disabled",false);
                    $("#confirmBtn").removeClass("layui-btn-disabled");
                    //如果上传失败
                    if(res.state == 'ok'){

                        $("#certificatePath_"+dataId+'_'+dataIndex).val(res.data.src);
                        $("#fileId").val(res.data.id);
                        layer.msg(res.msg,{icon:1,time:5000});
                    }else {
                        return layer.msg('上传失败');
                    }
                    layer.closeAll("loading");
                }
                ,error: function(){
                    //演示失败状态，并实现重传
                    var demoText = $('#demoText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function(){
                        uploadInst.upload();
                    });
                    layer.closeAll("loading");
                }
            });
        })
    }


    //身份证正面上传
    upload.render({
        elem: '#basePhoto'
        ,url: '#(fileUploadUrl)?bucket=employeeFace'
        ,before: function(obj){
            $("#confirmBtn").attr("disabled",true);
            $("#confirmBtn").addClass("layui-btn-disabled");
            //预读本地文件示例，不支持ie8
            obj.preview(function(index, file, result){
                $('#profilePhotoImg').attr('src', result);
            });
            layer.load();
        }
        ,done: function(res){
            $("#confirmBtn").attr("disabled",false);
            $("#confirmBtn").removeClass("layui-btn-disabled");
            //如果上传失败
            if(res.state == 'ok'){
                $("#uploadPath").val(res.data.src);
                $("#idcardFrontImg").bind('click',function () {
                    renderImg(res.data.src);
                })
                layer.msg(res.msg,{icon:1,time:5000});
            }else {
                return layer.msg('上传失败');
            }
            layer.closeAll("loading");
        }
        ,error: function(){
            //演示失败状态，并实现重传
            var demoText = $('#demoText');
            demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
            demoText.find('.demo-reload').on('click', function(){
                uploadInst.upload();
            });
            layer.closeAll("loading");
        }
    });

    //身份证正面上传
    upload.render({
        elem: '#idcardFrontBtn'
        ,url: '#(fileUploadUrl)?bucket=employeeFace'
        ,before: function(obj){
            $("#confirmBtn").attr("disabled",true);
            $("#confirmBtn").addClass("layui-btn-disabled");
            //预读本地文件示例，不支持ie8
            obj.preview(function(index, file, result){
                $('#idcardFrontImg').attr('src', result);
            });
            layer.load();
        }
        ,done: function(res){
            $("#confirmBtn").attr("disabled",false);
            $("#confirmBtn").removeClass("layui-btn-disabled");
            //如果上传失败
            if(res.state == 'ok'){
                $("#idcardFrontPath").val(res.data.src);
                $("#idcardFrontImg").bind('click',function () {
                    renderImg(res.data.src);
                })
                layer.msg(res.msg,{icon:1,time:5000});
            }else {
                return layer.msg('上传失败');
            }
            layer.closeAll("loading");
        }
        ,error: function(){
            //演示失败状态，并实现重传
            var demoText = $('#demoText');
            demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
            demoText.find('.demo-reload').on('click', function(){
                uploadInst.upload();
            });
            layer.closeAll("loading");
        }
    });
    //身份证反面上传
    upload.render({
        elem: '#idcardContraryBtn'
        ,url: '#(fileUploadUrl)?bucket=employeeFace'
        ,before: function(obj){
            $("#confirmBtn").attr("disabled",true);
            $("#confirmBtn").addClass("layui-btn-disabled");
            //预读本地文件示例，不支持ie8
            obj.preview(function(index, file, result){
                $('#idcardContraryImg').attr('src', result);
            });
            layer.load();
        }
        ,done: function(res){
            $("#confirmBtn").attr("disabled",false);
            $("#confirmBtn").removeClass("layui-btn-disabled");
            //如果上传失败
            if(res.state == 'ok'){
                $("#idcardContraryPath").val(res.data.src);
                $("#idcardContraryImg").bind('click',function () {
                    renderImg(res.data.src);
                })
                layer.msg(res.msg,{icon:1,time:5000});
            }else {
                return layer.msg('上传失败');
            }
            layer.closeAll("loading");
        }
        ,error: function(){
            //演示失败状态，并实现重传
            var demoText = $('#demoText');
            demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
            demoText.find('.demo-reload').on('click', function(){
                uploadInst.upload();
            });
            layer.closeAll("loading");
        }
    });

    renderImg=function(src) {
        var json={
            "title": "图片", //相册标题
            "id": 123, //相册id
            "start": 0, //初始显示的图片序号，默认0
            "data": [   //相册包含的图片，数组格式
                {
                    "alt": "图片",
                    "src": src, //原图地址
                    "thumb": "" //缩略图地址
                }
            ]
        };
        layui.layer.photos({
            photos: json
            , anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
            ,tab:function () {
                num=0;
                $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                    '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                    '\t</div>');
                $(document).on("click", "#xuanzhuan", function(e) {
                    num = (num+90)%360;
                    $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                    $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                });

                $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                    var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                        (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                    var imagep = $(".layui-layer-phimg").parent().parent();
                    var image = $(".layui-layer-phimg").parent();
                    var h = image.height();
                    var w = image.width();
                    if (delta > 0) {
                        if (h < (window.innerHeight)) {
                            h = h * 1.05;
                            w = w * 1.05;
                        }
                    } else if (delta < 0) {
                        if (h > 100) {
                            h = h * 0.95;
                            w = w * 0.95;
                        }
                    }
                    imagep.css("top", (window.innerHeight - h) / 2);
                    imagep.css("left", (window.innerWidth - w) / 2);
                    image.height(h);
                    image.width(w);
                    imagep.height(h);
                    imagep.width(w);
                });
            }
        });
    }


    form.on('select(contractType)',function (obj) {
        contractTypeSelect(obj.value,$("#dateType").val());
    });

    contractTypeSelect=function(contractType,dateType){
        if(contractType=='4' || contractType==''){
            $("#contractStartDate").attr("lay-verify","");
            $("#contractEndDate").attr("lay-verify","");
            $("#contractAddr").attr("lay-verify","");
            $("#contractAddrFont").text("");
            $("#contractEndDateFont").text("");
            $("#contractStartDateFont").text("");
            $("#dateType").attr('lay-verify','');
            $("#dateTypeFont").text('');

            if(dateType=='1'){
                $("#contractEndDateDiv").css('display','inline-block');
            }else if(dateType=='2'){
                $("#contractEndDateDiv").css('display','none');
            }
        }else{
            $("#contractStartDate").attr("lay-verify","required");
            $("#contractEndDate").attr("lay-verify","required");
            $("#contractAddr").attr("lay-verify","required");
            $("#contractAddrFont").text("*");
            $("#contractEndDateFont").text("*");
            $("#contractStartDateFont").text("*");
            $("#dateType").attr('lay-verify','required');
            $("#dateTypeFont").text('*');
            if(dateType=='1'){
                $("#contractEndDateDiv").css('display','inline-block');
            }else if(dateType=='2'){
                $("#contractEndDateDiv").css('display','none');
                $("#contractEndDate").attr("lay-verify","");
                $("#contractEndDateFont").text("");
            }
        }
    }

    #if(model!=null)
    contractTypeSelect('#(model.contractType??)','#(model.dateType??)');
    #end

    form.on('select(dateType)',function (obj) {
        if(obj.value=='1'){
            //显示结束日期
            $("#contractEndDateDiv").css('display','inline-block');
            if($("#contractType").val()=='4'){
                //待补、只显示不添加必填
                $("#contractEndDate").attr("lay-verify","");
                $("#contractEndDateFont").text("");
            }else{
                //显示添加必填
                $("#contractEndDate").attr("lay-verify","required");
                $("#contractEndDateFont").text("*");
            }
        }else if(obj.value=='2'){
            //隐藏去掉必填
            $("#contractEndDateDiv").css('display','none');
            $("#contractEndDate").attr("lay-verify","");
            $("#contractEndDateFont").text("");
        }
    })


    $("#choiceBtn").on('click',function () {
        var url='#(ctxPath)/pers/approval/empTable';
        pop_show("员工表",url,1320,700);
    })

    setQuitEmpIdValue=function (id,name,workNum) {
        $("#leaderEmpId").val(id);
        $("#leader").val(name+'('+workNum+')');
    }

});

</script>
#end

#define content()

<div class="layui-row">
    <div class="layui-col-md8 layui-form" id="content" style="padding-right: 10px;">
        #if(persTask!=null && persTask.submitUserName!=null && persTask.submitUserDeptName!=null)
        <fieldset class="layui-elem-field layui-field-title layui-form-pane" style="display:block;">
            <legend>提交人信息</legend>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人部门</label>
                <div class="layui-input-block">
                    <input type="text" name="" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人职位</label>
                <div class="layui-input-block">
                    <input type="text" name="" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交时间</label>
                <div class="layui-input-block">
                    <input type="text" name="" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">任务编号</label>
                <div class="layui-input-block">
                    <input type="text" name="" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                </div>
            </div>
        </fieldset>
        #end
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>员工信息-供应商</legend>
        <form class="layui-form layui-form-pane" action="" id="formId" style="padding-top: 10px;padding-left: 10px;min-width: 1000px">
            <div class="layui-inline" style="margin-bottom: 20px;float: left;" id="xxxxxxxxxx">
                <label class="layui-form-label"><font color="red">*</font>姓名</label>
                <div class="layui-input-inline" style="margin-right: 30px;float: left;width: 190px;">
                    <input type="text" name="employeeName" autocomplete="off" class="layui-input" value="#(model.employeeName??)" lay-verify="required">
                </div>

                <label class="layui-form-label"><font color="red">*</font>性别</label>
                <div class="layui-input-inline" style="width: 190px;">
                    <select name="gender" id="gender" lay-verify="required" lay-filter="">
                        <option value="">请选择性别</option>
                        #dictOption("sex", model.gender??'', "")
                    </select>
                </div>
            </div>
            <div class="layui-inline" style="margin-bottom: 20px;float: left;">
                <label class="layui-form-label"><font color="red">*</font>证件类型</label>
                <div class="layui-input-inline" style="margin-right: 30px;float: left;width: 190px;">
                    <select name="idcardType" id="idCardType" lay-filter="idCardType" lay-verify="required">
                        #dictOption("id_card_type", model.idcardType??'', "")
                    </select>
                </div>

                <label class="layui-form-label"><font color="red">*</font>身份证号码</label>
                <div class="layui-input-inline" style="width: 190px;">
                    <input type="text" name="idcard" id="idcard" lay-verify="required|checkIdCard" value="#(model.idcard??)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-inline" style="margin-bottom: 20px;float: left;display: none;">
                <label class="layui-form-label"><font color="red"></font>婚姻状况</label>
                <div class="layui-input-inline" style="margin-right: 30px;float: left;width: 190px;">
                    <select name="marriageStatus" id="marriageStatus" lay-verify="">
                        <option value="">请选择</option>
                        <option value="1" #if(model.marriageStatus??=='1')selected #end>已婚</option>
                        <option value="0" #if(model.marriageStatus??=='0')selected #end>未婚</option>
                        <option value="2" #if(model.marriageStatus??=='2')selected #end>离异</option>
                        <option value="3" #if(model.marriageStatus??=='3')selected #end>丧偶</option>
                    </select>
                </div>

                <label class="layui-form-label" style="padding: 8px 2px;">社会福利号码</label>
                <div class="layui-input-inline" style="width: 190px;">
                    <input type="text" name="socialNumber" id="socialNumber" value="#(model.socialNumber??)" autocomplete="off" class="layui-input">
                </div>
            </div>



            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px">
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                    <div class="layui-upload-list" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;" id="profilePhotoImg" src="#(model.headPortrait??)">
                        <p id="profilePhotoText"></p>
                        <input type="hidden" id="uploadPath" name="headPortrait" value="#(model.headPortrait??)">
                    </div>
                </div>
                <button type="button" class="layui-btn" id="basePhoto" style="position:absolute;bottom: 0;left: 35%;">上传员工图片</button>
            </div>
            <div class="layui-form-item" style="display: none;">
                <div class="layui-inline" style="margin-bottom: 20px;float: left;">
                    <label class="layui-form-label" style="    padding: 8px 5px;display: none;"><font color="red"></font>是否本地户籍</label>
                    <div class="layui-input-inline" style="margin-right: 30px;float: left;width: 190px;">
                        <select name="isLocal" lay-filter="" lay-verify="">
                            <option value="">请选择是否本地户籍</option>
                            <option value="1" #if(model.isLocal??=='1') selected #end>是</option>
                            <option value="0" #if(model.isLocal??=='0') selected #end>否</option>
                        </select>
                    </div>
                </div>
            </div>


            <div class="layui-inline" style="margin-bottom: 20px;float: left;">

                <label class="layui-form-label"><font color="red">*</font>出生年月</label>
                <div class="layui-input-inline" style="margin-right: 30px;float: left;width: 190px;">
                    <input type="text" name="birthday" id="birthday" lay-verify="required" value="#date(model.birthday??,'yyyy-MM-dd')" autocomplete="off" class="layui-input">
                </div>


                <label class="layui-form-label" style="display: none;"><font color="red"></font>最高学历</label>
                <div class="layui-input-inline" style="width: 190px;display: none;">
                    <select name="highestEducation" lay-filter="" lay-verify="">
                        <option value="">请选择文化程度</option>
                        #dictOption("education", model.highestEducation??'', "")
                    </select>
                </div>
            </div>

            <div class="layui-form-item" style="display: none;">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>籍贯</label>
                    <div class="layui-input-inline">
                        <input type="text" name="nativePlace" id="nativePlace" lay-verify="" value="#(model.nativePlace??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline" style="display: none;">
                    <label class="layui-form-label"><font color="red"></font>户口所在地</label>
                    <div class="layui-input-inline" style="width: 515px;">
                        <input type="text" name="residentAddr" autocomplete="off" value="#(model.residentAddr??)" class="layui-input" lay-verify="">
                    </div>
                </div>


            </div>
            <div class="layui-form-item" style="display: none;">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>民族</label>
                    <div class="layui-input-inline" >
                        <select name="nationality" lay-search="" lay-verify="">
                            <option value="">请选择民族</option>
                            #dictOption("nation", model.nationality??'', "")
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>现居住地址</label>
                    <div class="layui-input-inline" style="width: 515px;">
                        <input type="text" name="liveAddr" id="liveAddr" autocomplete="off" value="#(model.liveAddr??)" class="layui-input" lay-verify="">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>联系方式</label>
                    <div class="layui-input-inline">
                        <input type="text" name="phone" id="phone" lay-verify="required|phoneNumber" value="#(model.phone??)" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline" style="display:none;">
                    <label class="layui-form-label"><font color="red"></font>政治面貌</label>
                    <div class="layui-input-inline">
                        <input type="text" name="politicalOutlook" id="politicalOutlook" value="#(model.politicalOutlook??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline" style="display:none;">
                    <label class="layui-form-label">个人邮箱</label>
                    <div class="layui-input-inline">
                        <input type="text" name="email" id="email" lay-verify="checkEmail" value="#(model.email??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="display:none;">

                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>外语水平</label>
                    <div class="layui-input-inline">
                        <input type="text" name="languageLevel" id="languageLevel" lay-verify="" value="#(model.languageLevel??)" autocomplete="off" class="layui-input">
                    </div>
                </div>

                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>技术职称证书</label>
                    <div class="layui-input-inline">
                        <input type="text" name="technicalCertificate" id="technicalCertificate" value="#(model.technicalCertificate??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>计算机水平</label>
                    <div class="layui-input-inline">
                        <input type="text" name="computerLevel" id="computerLevel" lay-verify="" value="#(model.computerLevel??)" autocomplete="off" class="layui-input">
                    </div>
                </div>

            </div>

            <div  class="layui-form-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>紧急联系人</label>
                    <div class="layui-input-inline">
                        <input type="text" name="emergencyContact" id="emergencyContact" value="#(model.emergencyContact??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>紧急联系人电话</label>
                    <div class="layui-input-inline">
                        <input type="text" name="emergencyPhone" id="emergencyPhone" value="#(model.emergencyPhone??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>关系</label>
                    <div class="layui-input-inline">
                        <input type="text" name="relation" id="relation" lay-verify="" value="#(model.relation??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>

            <div class="layui-form-item">

                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>员工类型</label>
                    <div class="layui-input-inline" >
                        <select id="employeeType" name="employeeType" lay-filter="employeeType" lay-search="" lay-verify="required">
                            <option value="">请选择员工类型</option>
                            #dictOption("employee_type", model.employeeType??'', "")
                        </select>
                    </div>
                </div>
                <div class="layui-inline" id="agentIdDiv">
                    <label class="layui-form-label">#if(model.employeeType??=='agency')<font color="red">*</font>#end所属代理商</label>
                    <div class="layui-input-inline" >
                        <select id="agentId" name="agentId" lay-search="" #if(model.employeeType??=='agency')lay-verify="required"#end>
                            <option value="">请选择所属代理商</option>
                            #for(agency : agencyList)
                            <option value="#(agency.id)" #if(agency.id==model.agentId??) selected #end  >#(agency.agencyName)</option>
                            #end
                        </select>
                    </div>
                </div>
            </div>

            <div  class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>入职部门</label>
                    <div class="layui-input-inline">
                        <input type="text" name="deptId" id="deptId" lay-verify="required" lay-filter="tree" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>职位</label>
                    <div class="layui-input-inline">
                        <select name="positionId" id="positionId" lay-filter="positionId" lay-verify="required">
                            <option value="" role-id="">请选择</option>
                            #for(position : positionList)
                            <option value="#(position.id)" role-id="#(position.roleId)" #if(position.id==model.positionId??)selected#end >#(position.positionName)</option>
                            #end
                        </select>
                    </div>
                    <input type="hidden" name="roleId" id="roleId" value="">
                </div>
            </div>

            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red">*</font>招聘渠道</label>
                    <div class="layui-input-inline">
                        <select name="recruitChannel" lay-verify="required">
                            <option value="">请选择</option>
                            #dictOption("entry_channel", model.recruitChannel??'', "")
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>入职日期</label>
                    <div class="layui-input-inline">
                        <input type="text" name="entryTime" id="entryTime" lay-verify="required" value="#date(model.entryTime??,'yyyy-MM-dd')" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline" style="display:none;">
                    <label class="layui-form-label"><font color="red"></font>转正日期</label>
                    <div class="layui-input-inline">
                        <input type="text" name="formalDate" id="formalDate" lay-verify="" value="#date(model.formalDate??,'yyyy-MM-dd')" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>试用期薪资</label>
                    <div class="layui-input-inline">
                        <input type="text" name="probationSalary" id="probationSalary" lay-verify="checkMoney" value="#(model.probationSalary??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><span color="red"></span>转正后薪资</label>
                    <div class="layui-input-inline">
                        <input type="text" name="permanentSalary" id="permanentSalary" lay-verify="checkMoney" value="#(model.permanentSalary??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label"><span color="red"></span>上级领导</label>
                    <div class="layui-input-inline">
                        <input type="text" name="leader" id="leader" readonly lay-verify="" autocomplete="off" class="layui-input" #if(leaderEmp!=null) value="#(leaderEmp.fullName??)(#(leaderEmp.workNum??))" #end>
                        <input type="hidden" id="leaderEmpId" name="leaderEmpId" value="#(model.leaderEmpId??)"  >
                    </div>
                    <button class="layui-btn" id="choiceBtn" type="button">选择</button>

                </div>
            </div>
            <div class="layui-form-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red"></font>试用期绩效薪资</label>
                    <div class="layui-input-inline">
                        <input type="text" name="probationPerformanceSalary" id="probationPerformanceSalary" lay-verify="checkMoney" value="#(model.probationPerformanceSalary??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 5px;"><span color="red"></span>转正后绩效薪资</label>
                    <div class="layui-input-inline">
                        <input type="text" name="permanentPerformanceSalary" id="permanentPerformanceSalary" lay-verify="checkMoney" value="#(model.permanentPerformanceSalary??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-form-item" >
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red"  ></font>薪资描述</label>
                    <div class="layui-input-block">
                        <input type="text" id="salaryRemark" name="salaryRemark" class="layui-input" lay-verify="" value="#(model.salaryRemark??)" placeholder="请输入薪资描述">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="margin-bottom: 60px;">
                <div class="layui-col-xs6">
                    <label class="layui-form-label" onclick="aaaa()"><font color="red">*</font>身份证正面</label>
                    <div class="layui-input-inline" style="padding: 10px;width: 50%;height: 190px;float: left;margin-left: 0px;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.idcardFrontPath??''!='') onclick="renderImg('#(model.idcardFrontPath??)')" #end  id="idcardFrontImg" src="#(model.idcardFrontPath??)">
                                #if(model==null)
                                <p id="idcardFrontText">请上传身份证正面图片</p>
                                #end
                                <input type="hidden" id="idcardFrontPath" name="idcardFrontPath"   value="#(model.idcardFrontPath??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn" id="idcardFrontBtn" style="position:absolute;bottom: -25%;left: 35%;">上传身份证正面图片</button>
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <label class="layui-form-label" style="margin-left: 50px;"><font color="red">*</font>身份证反面</label>
                    <div class="layui-input-inline" style="padding: 10px;width: 50%;height: 190px;float: left;margin-left: 0px;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.idcardContraryPath??''!='') onclick="renderImg('#(model.idcardContraryPath??)')" #end  id="idcardContraryImg" src="#(model.idcardContraryPath??)">
                                #if(model==null)
                                <p id="idcardContraryText">请上传身份证反面图片</p>
                                #end
                                <input type="hidden" id="idcardContraryPath"  name="idcardContraryPath" value="#(model.idcardContraryPath??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn" id="idcardContraryBtn" style="position:absolute;bottom: -25%;left: 35%;">上传身份证反面图片</button>
                    </div>
                </div>
                <!--<div class="layui-upload-drag" id="idcardFront" style="float: left;">
                    <i class="layui-icon">&#xe67c;</i>
                    <p>点击上传图片，或将图片拖拽至此处</p>
                    <div class="layui-hide" id="idcardFrontView">
                        <hr>
                        <img src="" alt="上传成功后渲染" style="max-width: 196px">
                    </div>
                </div>
                <label class="layui-form-label" style="margin-left: 20px;"><font color="red">*</font>身份证反面</label>
                <div class="layui-upload-drag" id="idcardContrary" style="float: left;">
                    <i class="layui-icon">&#xe67c;</i>
                    <p>点击上传图片，或将图片拖拽至此处</p>
                    <div class="layui-hide" id="idcardContraryView">
                        <hr>
                        <img src="" alt="上传成功后渲染" style="max-width: 196px">
                    </div>
                </div>-->
            </div>
            <div id="contractDiv" #if(dept.orgType??=="branche_office") style="display: none;" #else style="display: block;" #end>
            <div class="layui-form-item" style="display: none;">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red"></font>合同类型</label>
                    <div class="layui-input-inline" style="width: 140px;">
                        <select name="contractType" id="contractType" lay-filter="contractType" lay-verify="" >
                            <option value="">请选择类型</option>
                            <option value="1" #if(model.contractType??=='1') selected #end>劳动合同</option>
                            <option value="2" #if(model.contractType??=='2') selected #end>劳务合同</option>
                            <!--<option value="3" #if(contract.contractType??=='3') selected #end>临时合同</option>-->
                            <option value="5" #if(model.contractType??=='5') selected #end>实习协议</option>
                            <option value="6" #if(model.contractType??=='6') selected #end>临时工合同</option>
                            <option value="4" #if(model.contractType??=='4') selected #end>待补</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 5px; "><font color="red" id="dateTypeFont"></font>合同时间类型</label>
                    <div class="layui-input-inline" style="width: 140px;">
                        <select name="dateType" lay-verify="" lay-filter="dateType" id="dateType" >
                            <option value="">请选择类型</option>
                            <option value="1" #if(model.dateType??=='1') selected #end>指定时长</option>
                            <option value="2" #if(model.dateType??=='2') selected #end>长期合同</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractStartDateFont"></font>合同开始时间</label>
                    <div class="layui-input-inline" style="width: 145px;">
                        <input type="text" id="contractStartDate" name="contractStartDate" readonly     value="#date(model.contractStartDate??,'yyyy-MM-dd')"  class="layui-input" lay-verify=""  placeholder="请选择合同签订时间">

                    </div>
                </div>
                <div class="layui-inline" id="contractEndDateDiv">
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractEndDateFont"></font>合同结束时间</label>
                    <div class="layui-input-inline" style="width: 145px;">
                        <input type="text" id="contractEndDate" name="contractEndDate" readonly class="layui-input" lay-verify="" value="#date(model.contractEndDate??,'yyyy-MM-dd')" placeholder="请选择合同到期时间">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="display: none;">
                <div class="layui-form-item" >
                    <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractAddrFont"></font>合同归属地址</label>
                    <div class="layui-input-block">
                        <input type="text" id="contractAddr" name="contractAddr" class="layui-input" lay-verify="" value="#(model.contractAddr??)" placeholder="请输入合同地址">
                    </div>
                </div>
            </div>
            </div>






            <fieldset class="layui-elem-field" style="margin-top:20px;margin-bottom:40px;display: none;">
                <legend style="font-size: 14px;margin-bottom:10px;"><b>合同图片</b></legend>

                <div class="layui-form-item" style="margin-bottom: 60px;display: flex;justify-content: center;align-items: center;">
                    <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath1??''!='') onclick="renderImg('#(model.contractPath1??)')" #end  id="contractImg1" src="#(model.contractPath1??)">
                                #if(model==null)
                                <p >请上传合同图片一</p>
                                #end
                                <input type="hidden" id="contractPath1" name="contractPath1"   value="#(model.contractPath1??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn contractUploadBtn" data-index="1" id="contractBtn1" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片一</button>
                    </div>
                    <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath2??''!='') onclick="renderImg('#(model.contractPath2??)')" #end  id="contractImg2" src="#(model.contractPath2??)">
                                #if(model==null)
                                <p >请上传合同图片二</p>
                                #end
                                <input type="hidden" id="contractPath2"  name="contractPath2" value="#(model.contractPath2??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn contractUploadBtn" data-index="2" id="contractBtn2" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片二</button>
                    </div>
                    <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                #if(model==null)
                                <p >请上传合同图片三</p>
                                #end
                                <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                    </div>
                </div>
                <div class="layui-form-item" style="margin-bottom: 60px;display: flex;justify-content: center;align-items: center;">
                    <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath4??''!='') onclick="renderImg('#(model.contractPath4??)')" #end  id="contractImg4" src="#(model.contractPath4??)">
                                #if(model==null)
                                <p >请上传合同图片四</p>
                                #end
                                <input type="hidden" id="contractPath4" name="contractPath4"   value="#(model.contractPath4??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn contractUploadBtn" data-index="4" id="contractBtn4" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片四</button>
                    </div>
                    <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;">
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath5??''!='') onclick="renderImg('#(model.contractPath5??)')" #end  id="contractImg5" src="#(model.contractPath5??)">
                                #if(model==null)
                                <p >请上传合同图片五</p>
                                #end
                                <input type="hidden" id="contractPath5"  name="contractPath5" value="#(model.contractPath5??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn contractUploadBtn" data-index="5" id="contractBtn5" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片五</button>
                    </div>
                    <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;" id="contract6Div" >
                        <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;" >
                            <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath6??''!='') onclick="renderImg('#(model.contractPath6??)')" #end  id="contractImg6" src="#(model.contractPath6??)">
                                #if(model==null)
                                <p >请上传合同图片六</p>
                                #end
                                <input type="hidden" id="contractPath6"  name="contractPath6" value="#(model.contractPath6??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn contractUploadBtn" data-index="6" id="contractBtn6" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片六</button>
                    </div>
                </div>
            </fieldset>
            <div class="layui-row" id="certificateDiv">
                <!--<fieldset class="layui-elem-field" style="margin-top:20px;margin-bottom:40px;">
                    <legend style="font-size: 14px;margin-bottom:10px;"><b>个人资料  家庭状况 FAMILY SITUATION</b></legend>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractStartDateFont"></font>合同开始时间</label>
                            <div class="layui-input-inline">
                                <input type="text" id="contractStartDate" name="contractStartDate" readonly     value="#date(model.contractStartDate??,'yyyy-MM-dd')"  class="layui-input" lay-verify=""  placeholder="请选择合同签订时间">

                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractEndDateFont"></font>合同结束时间</label>
                            <div class="layui-input-inline">
                                <input type="text" id="contractEndDate" name="contractEndDate" readonly class="layui-input" lay-verify="" value="#date(model.contractEndDate??,'yyyy-MM-dd')" placeholder="请选择合同到期时间">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-bottom: 60px;display: flex;align-items: center;flex-wrap: wrap;">
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath1??''!='') onclick="renderImg('#(model.contractPath1??)')" #end  id="contractImg1" src="#(model.contractPath1??)">
                                    #if(model==null)
                                    <p >请上传合同图片一</p>
                                    #end
                                    <input type="hidden" id="contractPath1" name="contractPath1"   value="#(model.contractPath1??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="1" id="contractBtn1" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片一</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath2??''!='') onclick="renderImg('#(model.contractPath2??)')" #end  id="contractImg2" src="#(model.contractPath2??)">
                                    #if(model==null)
                                    <p >请上传合同图片二</p>
                                    #end
                                    <input type="hidden" id="contractPath2"  name="contractPath2" value="#(model.contractPath2??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="2" id="contractBtn2" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片二</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                    #if(model==null)
                                    <p >请上传合同图片三</p>
                                    #end
                                    <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                    #if(model==null)
                                    <p >请上传合同图片三</p>
                                    #end
                                    <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                    #if(model==null)
                                    <p >请上传合同图片三</p>
                                    #end
                                    <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                        </div>
                    </div>
                </fieldset>
                <fieldset class="layui-elem-field" style="margin-top:20px;margin-bottom:40px;">
                    <legend style="font-size: 14px;margin-bottom:10px;"><b>个人资料  家庭状况 FAMILY SITUATION</b></legend>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractStartDateFont"></font>合同开始时间</label>
                            <div class="layui-input-inline">
                                <input type="text" id="contractStartDate" name="contractStartDate" readonly     value="#date(model.contractStartDate??,'yyyy-MM-dd')"  class="layui-input" lay-verify=""  placeholder="请选择合同签订时间">

                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red" id="contractEndDateFont"></font>合同结束时间</label>
                            <div class="layui-input-inline">
                                <input type="text" id="contractEndDate" name="contractEndDate" readonly class="layui-input" lay-verify="" value="#date(model.contractEndDate??,'yyyy-MM-dd')" placeholder="请选择合同到期时间">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-bottom: 60px;display: flex;align-items: center;flex-wrap: wrap;">
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath1??''!='') onclick="renderImg('#(model.contractPath1??)')" #end  id="contractImg1" src="#(model.contractPath1??)">
                                    #if(model==null)
                                    <p >请上传合同图片一</p>
                                    #end
                                    <input type="hidden" id="contractPath1" name="contractPath1"   value="#(model.contractPath1??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="1" id="contractBtn1" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片一</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath2??''!='') onclick="renderImg('#(model.contractPath2??)')" #end  id="contractImg2" src="#(model.contractPath2??)">
                                    #if(model==null)
                                    <p >请上传合同图片二</p>
                                    #end
                                    <input type="hidden" id="contractPath2"  name="contractPath2" value="#(model.contractPath2??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="2" id="contractBtn2" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片二</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                    #if(model==null)
                                    <p >请上传合同图片三</p>
                                    #end
                                    <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                    #if(model==null)
                                    <p >请上传合同图片三</p>
                                    #end
                                    <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                        </div>
                        <div class="layui-inline" style="padding: 10px;width: 30%;height: 190px;float: left;    margin-bottom: 50px;">
                            <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                                <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 85%;">
                                    <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(model.contractPath3??''!='') onclick="renderImg('#(model.contractPath3??)')" #end  id="contractImg3" src="#(model.contractPath3??)">
                                    #if(model==null)
                                    <p >请上传合同图片三</p>
                                    #end
                                    <input type="hidden" id="contractPath3"  name="contractPath3" value="#(model.contractPath3??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
                        </div>
                    </div>
                </fieldset>-->
            </div>
            <fieldset class="layui-elem-field" style="margin-top:30px;margin-bottom:60px;display:none;">
                <legend style="font-size: 14px;margin-bottom:10px;"><b>个人资料  家庭状况 FAMILY SITUATION</b></legend>
                <div class="layui-row">
                    #if(model==null || taskId==null || isSaveHandle??)
                    <button class="layui-btn" type="button" id="addFamilySituationBtn">添加</button>
                    #end
                    <input type="hidden" id="familySituationCount" name="familySituationCount" value="#(familySituationList.size()??0)">
                </div>
                <table class="layui-table" id="familySituationTable" >
                    <thead>
                    <tr>
                        <th>姓名</th>
                        <th>关系</th>
                        <th>工作单位</th>
                        <th>联系电话</th>
                        <th>职位</th>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="familySituationTbody">
                    #for(familySituation : familySituationList)
                    <tr id="familySituationTr-#(for.index+1)" index="#(for.index+1)">
                        <td><input name="familySituation[#(for.index+1)].name" class="layui-input" lay-verify="required" autocomplete="off" value="#(familySituation.name??)"></td>
                        <td><input name="familySituation[#(for.index+1)].relation" lay-verify="required" class="layui-input" value="#(familySituation.relation??)" autocomplete="off"></td>
                        <td><input name="familySituation[#(for.index+1)].workUnit" lay-verify="" class="layui-input" value="#(familySituation.workUnit??)" autocomplete="off"></td>
                        <td><input name="familySituation[#(for.index+1)].phone" lay-verify="required" class="layui-input" value="#(familySituation.phone??)" autocomplete="off"></td>
                        <td><input name="familySituation[#(for.index+1)].position" lay-verify="" class="layui-input" value="#(familySituation.position??)" autocomplete="off"></td>
                        <td align="center">
                            <input name="familySituation[#(for.index+1)].id" type="hidden" value="#(familySituation.id??)" autocomplete="off">
                            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('familySituation',#(for.index+1),'#(familySituation.id??)')" >作废</button>
                        </td>
                    </tr>
                    #end
                    </tbody>
                </table>

            </fieldset>
            <fieldset class="layui-elem-field" style="margin-top:30px;margin-bottom:60px;display:none;">
                <legend style="font-size: 14px;margin-bottom:10px;"><b>教育经历 EDUCATIONAL BACKGROUND</b></legend>

                <div class="layui-row">
                    #if(model==null || taskId==null || isSaveHandle??)
                    <button class="layui-btn" type="button" id="addEducationalBackgroundBtn">添加</button>
                    #end
                    <input type="hidden" id="educationalBackgroundCount" name="educationalBackgroundCount" value="#(educationalBackgroundList.size()??0)">
                </div>
                <table class="layui-table" id="educationalBackgroundTable" >
                    <thead>
                    <tr>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>所在院校</th>
                        <th>专业</th>
                        <th>文凭</th>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="educationalBackgroundTbody">
                    #for(educationalBackground : educationalBackgroundList)
                    <tr id="educationalBackgroundTr-#(for.index+1)" index="#(for.index+1)">
                        <td><input name="educationalBackground[#(for.index+1)].startDate" id="educationalBackground-#(for.index+1)-startDate" class="layui-input" lay-verify="required" autocomplete="off" value="#date(educationalBackground.startDate??,'yyyy-MM-dd')"></td>
                        <td><input name="educationalBackground[#(for.index+1)].endDate" id="educationalBackground-#(for.index+1)-endDate" lay-verify="required" class="layui-input" value="#date(educationalBackground.endDate??,'yyyy-MM-dd')" autocomplete="off"></td>
                        <td><input name="educationalBackground[#(for.index+1)].school" lay-verify="" class="layui-input" value="#(educationalBackground.school??)" autocomplete="off"></td>
                        <td><input name="educationalBackground[#(for.index+1)].major" lay-verify="required" class="layui-input" value="#(educationalBackground.major??)" autocomplete="off"></td>
                        <td><input name="educationalBackground[#(for.index+1)].diploma" lay-verify="" class="layui-input" value="#(educationalBackground.diploma??)" autocomplete="off"></td>
                        <td align="center">
                            <input name="educationalBackground[#(for.index+1)].id" type="hidden" value="#(educationalBackground.id??)" autocomplete="off">
                            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('educationalBackground',#(for.index+1),'#(educationalBackground.id??)')" >作废</button>
                        </td>
                    </tr>
                    #end
                    </tbody>
                </table>
            </fieldset>
            <fieldset class="layui-elem-field" style="margin-top:30px;margin-bottom:60px;display:none;">
                <legend style="font-size: 14px;margin-bottom:10px;"><b>培训经历 TRAINING BACKGROUND</b></legend>
                <div class="layui-row">
                    #if(model==null || taskId==null || isSaveHandle??)
                    <button class="layui-btn" type="button" id="addTrainingBackgroundBtn">添加</button>
                    #end
                    <input type="hidden" id="trainingBackgroundCount" name="trainingBackgroundCount" value="#(trainingBackgroundList.size()??0)">
                </div>
                <table class="layui-table" id="trainingBackgroundTable" >
                    <thead>
                    <tr>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>课程名称</th>
                        <th>学习形式</th>
                        <th>所获证书</th>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="trainingBackgroundTbody">
                    #for(trainingBackground : trainingBackgroundList)
                    <tr id="trainingBackgroundTr-#(for.index+1)" index="#(for.index+1)">
                        <td><input name="trainingBackground[#(for.index+1)].startDate" id="trainingBackground-#(for.index+1)-startDate" class="layui-input" lay-verify="required" autocomplete="off" value="#date(trainingBackground.startDate??,'yyyy-MM-dd')"></td>
                        <td><input name="trainingBackground[#(for.index+1)].endDate" id="trainingBackground-#(for.index+1)-endDate" lay-verify="required" class="layui-input" value="#date(trainingBackground.endDate??,'yyyy-MM-dd')" autocomplete="off"></td>
                        <td><input name="trainingBackground[#(for.index+1)].course" lay-verify="" class="layui-input" value="#(trainingBackground.course??)" autocomplete="off"></td>
                        <td>
                            <select id="learningFormSelect-[#(for.index+1)]" name="trainingBackground[#(for.index+1)].learningForm">
                                <option value="">请选择学习形式</option>
                                #dictOption("learning_form", trainingBackground.learningForm??'', "")
                            </select>
                        </td>
                        <td><input name="trainingBackground[#(for.index+1)].certificate" lay-verify="" class="layui-input" value="#(trainingBackground.certificate??)" autocomplete="off"></td>
                        <td align="center">
                            <input name="trainingBackground[#(for.index+1)].id" type="hidden" value="#(trainingBackground.id??)" autocomplete="off">
                            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('trainingBackground',#(for.index+1),'#(trainingBackground.id??)')" >作废</button>
                        </td>
                    </tr>
                    #end
                    </tbody>
                </table>

            </fieldset>
            <fieldset class="layui-elem-field" style="margin-top:30px;margin-bottom:60px;display:none;">
                <legend style="font-size: 14px;margin-bottom:10px;"><b>工作履历EMPLOYMENT HISTORY( 请从最近工作履历写起）*若曾在我司任职，有关资料必须详细填写</b></legend>
                <div class="layui-row">
                    #if(model==null || taskId==null || isSaveHandle??)
                    <button class="layui-btn" type="button" id="addEmploymentHistoryBtn">添加</button>
                    #end
                    <input type="hidden" id="employmentHistoryCount" name="employmentHistoryCount" value="#(employmentHistoryList.size()??0)">
                </div>
                <table class="layui-table" id="employmentHistoryTable" >
                    <thead>
                    <tr>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>公司名称</th>
                        <th>职位</th>
                        <th>离职原因</th>
                        <th>最后薪资(税前)</th>
                        <th>原公司人事部证明人</th>
                        <th>证明人办公电话</th>
                        <td>操作</td>
                    </tr>
                    </thead>
                    <tbody id="employmentHistoryTbody">
                    #for(employmentHistory : employmentHistoryList)
                    <tr id="employmentHistoryTr-#(for.index+1)" index="#(for.index+1)">
                        <td><input name="employmentHistory[#(for.index+1)].startDate" id="employmentHistory-#(for.index+1)-startDate" class="layui-input" lay-verify="required" autocomplete="off" value="#date(employmentHistory.startDate??,'yyyy-MM-dd')"></td>
                        <td><input name="employmentHistory[#(for.index+1)].endDate" id="employmentHistory-#(for.index+1)-endDate" lay-verify="required" class="layui-input" value="#date(employmentHistory.endDate??,'yyyy-MM-dd')" autocomplete="off"></td>
                        <td><input name="employmentHistory[#(for.index+1)].company" lay-verify="required" class="layui-input" value="#(employmentHistory.company??)" autocomplete="off"></td>
                        <td><input name="employmentHistory[#(for.index+1)].position" lay-verify="required" class="layui-input" value="#(employmentHistory.position??)" autocomplete="off"></td>
                        <td><input name="employmentHistory[#(for.index+1)].quitReason" lay-verify="required" class="layui-input" value="#(employmentHistory.quitReason??)" autocomplete="off"></td>
                        <td><input name="employmentHistory[#(for.index+1)].salary" lay-verify="required|number" class="layui-input" value="#(employmentHistory.salary??)" autocomplete="off"></td>
                        <td><input name="employmentHistory[#(for.index+1)].witness" lay-verify="" class="layui-input" value="#(employmentHistory.witness??)" autocomplete="off"></td>
                        <td><input name="employmentHistory[#(for.index+1)].phone" lay-verify="" class="layui-input" value="#(employmentHistory.phone??)" autocomplete="off"></td>
                        <td align="center">
                            <input name="employmentHistory[#(for.index+1)].id" type="hidden" value="#(employmentHistory.id??)" autocomplete="off">
                            <button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del('employmentHistory',#(for.index+1),'#(employmentHistory.id??)')" >作废</button>
                        </td>
                    </tr>
                    #end
                    </tbody>
                </table>

            </fieldset>

            <div class="layui-form-item" style="display:none;">
                <div class="layui-inline" >
                    <label class="layui-form-label" style="padding: 8px 2px;width: 182px;"><font color="red"></font>是否服从以下工作地点调动</label>
                    <div class="layui-input-block" style="width: 210px;margin-left: 182px;">
                        <select name="isWorkAddrTransfer">
                            <option value="">请选择</option>
                            <option value="1" #if(model.isWorkAddrTransfer??=='1') selected #end>是</option>
                            <option value="0" #if(model.isWorkAddrTransfer??=='0') selected #end>否</option>
                        </select>
                    </div>

                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>工作地点</label>
                    <div class="layui-input-inline" style="width: 300px;">
                        <input type="text" name="workAddrTransfer" id="workAddrTransfer" value="#(model.workAddrTransfer??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>特长及兴趣爱好</label>
                    <div class="layui-input-inline" style="width: 283px;">
                        <input type="text" name="hobby" id="hobby" lay-verify="" value="#(model.hobby??)" autocomplete="off" class="layui-input">
                    </div>

                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px;width: 180px;"><font color="red"></font>英文打字速度(字/分钟)</label>
                    <div class="layui-input-block" style="width: 230px;margin-left: 180px;">
                        <input type="text" name="englishSpeed" id="englishSpeed" lay-verify="" value="#(model.englishSpeed??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px;width: 180px;"><font color="red"></font>中文打字速度(字/分钟)</label>
                    <div class="layui-input-block" style="width: 213px;margin-left: 180px;">
                        <input type="text" name="chineseSpeed" id="chineseSpeed" lay-verify="" value="#(model.chineseSpeed??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>是否能出差</label>
                    <div class="layui-input-inline" >
                        <select name="isOffSiteWork">
                            <option value="">请选择</option>
                            <option value="1" #if(model.isOffSiteWork??=='1') selected #end>是</option>
                            <option value="0" #if(model.isOffSiteWork??=='0') selected #end>否</option>
                        </select>
                    </div>
                </div>


            </div>
            <div class="layui-form-item" style="display:none;">

                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>能否岗位调动</label>
                    <div class="layui-input-inline" >
                        <select name="jobTransfer">
                            <option value="">请选择</option>
                            <option value="1" #if(model.jobTransfer??=='1') selected #end>是</option>
                            <option value="0" #if(model.jobTransfer??=='0') selected #end>否</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>能否加班</label>
                    <div class="layui-input-inline" >
                        <select name="isWorkOvertime">
                            <option value="">请选择</option>
                            <option value="1" #if(model.isWorkOvertime??=='1') selected #end>是</option>
                            <option value="0" #if(model.isWorkOvertime??=='0') selected #end>否</option>
                        </select>
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>能否吃住公司</label>
                    <div class="layui-input-inline" >
                        <select name="isLiveCompany">
                            <option value="">请选择</option>
                            <option value="1" #if(model.isLiveCompany??=='1') selected #end>是</option>
                            <option value="0" #if(model.isLiveCompany??=='0') selected #end>否</option>
                        </select>
                    </div>
                </div>

            </div>


            <div class="layui-form-item" style="display:none;">
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>介绍人</label>
                    <div class="layui-input-inline">
                        <input type="text" name="introducer" id="introducer" lay-verify="" value="#(model.introducer??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>职位</label>
                    <div class="layui-input-inline">
                        <input type="text" name="introducerPosition" id="introducerPosition" value="#(model.introducerPosition??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-inline">
                    <label class="layui-form-label" style="padding: 8px 2px"><font color="red"></font>关系</label>
                    <div class="layui-input-inline">
                        <input type="text" name="introducerRelation" id="introducerRelation" value="#(model.introducerRelation??)" lay-verify="" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </div>
            <div class="layui-form-item layui-form-text" style="padding-bottom: 100px;">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注内容" name="remark" id="remark" class="layui-textarea">#(model.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer" >
                <div class="pull-right" style="padding-bottom: 10px;">
                    <div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
                    <input type="hidden" name="certificateTypeData" id="certificateTypeData" value="">
                    <input type="hidden" name="id" id="id" value="#(model.Id??)">
                    <input type="hidden" name="employeeId" id="employeeId" value="#if(model!=null)#(model.employeeId??)#else#(empId??)#end">
                    <input type="hidden" name="taskId" id="taskId" value="#(taskId??)">
                    <input type="hidden" name="taskNo" id="taskNo" value="#(persTask.taskNo??)">
                    <button class="layui-btn" type="button" id="enclosure" >附件</button>
                    #if(model==null || taskId==null || isSaveHandle??)
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    #end

                    #if(model==null || taskId==null || isSaveHandle??)
                    <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
                    #end

                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </form>
        </fieldset>
    </div>
    <div class="layui-col-md4">
        #if(stepts.size()??0>0)
        <table class="layui-table" id="steptsTable">
            <!--<colgroup>
                <col width="10%">
                <col width="13%">
                <col width="30%">
                <col width="30%">
                <col width="17%">
            </colgroup>-->
            <thead>
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">节点</th>
                <th style="text-align: center;">流程</th>
                <th style="text-align: center;">意见</th>
                <th style="text-align: center;">操作时间</th>
                <th style="text-align: center;">操作人</th>
            </tr>
            </thead>
            <tbody>
            #for(stept:stepts)
            <tr>
                <td align="center">#(for.index+1)</td>
                <td>#(stept.ActivityName)</td>
                <td align="center">
                    #if(stept.StepState==3)
                    提交
                    #else if(stept.StepState==1)
                    待处理
                    #else if(stept.StepState==0)
                    等待
                    #else if(stept.StepState==2)
                    正处理
                    #else if(stept.StepState==4)
                    撤回
                    #else if(stept.StepState==5)
                    批准
                    #else if(stept.StepState==6)
                    拒绝
                    #else if(stept.StepState==7)
                    转移
                    #else if(stept.StepState==8)
                    失败
                    #else if(stept.StepState==9)
                    跳过
                    #end
                </td>
                <td>#(stept.Comment)</td>
                <td>#(stept.CommentTime)</td>
                <td align="center">#(stept.CommentUserName)</td>
            </tr>
            #end
            </tbody>
        </table>

        <form class="layui-form layui-form-pane" id="taskForm">
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">意见</label>
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block pull-right">

                    <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort || !isSaveHandle) disabled style="display: none;" #end >中止</button>
                    <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit || !isSaveHandle) disabled style="display: none;" #end>提交</button>
                    <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject || !isSaveHandle) disabled  style="display: none;" #end>拒绝</button>
                    <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove || !isSaveHandle) disabled style="display: none;" #end>通过</button>
                </div>
            </div>
        </form>
        #end
    </div>
</div>


#end
