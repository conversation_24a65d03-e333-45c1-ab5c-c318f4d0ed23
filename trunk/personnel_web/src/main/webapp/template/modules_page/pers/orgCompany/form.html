#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;


        /*var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#orgId").val(treeNode.id);
                    $("#orgName").val(treeNode.name);
                }
            }
        };

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);*/

        //自定义验证规则
        form.verify({
            otherReq: function(value,item){
                var $ = layui.$;
                var verifyName=$(item).attr('name')
                    , verifyType=$(item).attr('type')
                    ,formElem=$(item).parents('.layui-form')//获取当前所在的form元素，如果存在的话
                    ,verifyElem=formElem.find('input[name='+verifyName+']')//获取需要校验的元素
                    ,isTrue= verifyElem.is(':checked')//是否命中校验
                    ,focusElem = verifyElem.next().find('i.layui-icon');//焦点元素
                if(!isTrue || !value){
                    //定位焦点
                    focusElem.css(verifyType=='radio'?{"color":"#FF5722"}:{"border-color":"#FF5722"});
                    //对非输入框设置焦点
                    focusElem.first().attr("tabIndex","").css("outline","").blur(function() {
                        focusElem.css(verifyType=='radio'?{"color":""}:{"border-color":""});
                    }).focus();
                    return '必填项不能为空';
                }
            }
        });




            //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/orgCompany/companySave";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#positionForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //parent.table.reload('positionTable',{'where':{'orgId':$("#orgId").val()}});
                        pop_close();
                        parent.positionTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        form.on('radio(isProvidentFund)',function (obj) {
            console.log(obj.value+"  公积金");
            if(obj.value==='1'){
                $("input[name='providentFundAccount']").attr("lay-verify","required");
                $("#socialSecurityUserId").attr("lay-verify","required");
                $("#providentFundAccountLabel").html("<span style=\"color: red\">*</span>公积金账号");
                $("#providentFundUserIdLabel").html("<span style=\"color: red\">*</span>公积金经办人");
            }else if(obj.value==='0'){
                $("input[name='providentFundAccount']").attr("lay-verify","");
                $("#providentFundUserId").attr("lay-verify","");
                $("#providentFundAccountLabel").html("<span style=\"color: red\">*</span>公积金账号");
                $("#providentFundUserIdLabel").html("<span style=\"color: red\">*</span>公积金经办人");
            }
            form.render();
        })
        form.on('radio(isSocialSecurity)',function (obj) {
            if(obj.value==='1'){
                $("input[name='socialSecurityAccount']").attr("lay-verify","required");
                $("#socialSecurityUserId").attr("lay-verify","required");
                $("#socialSecurityAccountLabel").html("<span style=\"color: red\">*</span>社保账号");
                $("#socialSecurityUserIdLabel").html("<span style=\"color: red\">*</span>社保经办人");
            }else if(obj.value==='0'){
                $("input[name='socialSecurityAccount']").attr("lay-verify","");
                $("#socialSecurityUserId").attr("lay-verify","");
                $("#socialSecurityAccountLabel").html("<span style=\"color: red\"></span>社保账号");
                $("#socialSecurityUserIdLabel").html("<span style=\"color: red\"></span>社保经办人");

            }
            form.render();
        })


    });
</script>
#end

#define content()

<!--<div class="layui-col-xs3 layui-col-sm3 layui-col-md3 layui-col-lg3">
    <fieldset class="layui-elem-field layui-field-title" style="display:block;">
        <legend>组织架构</legend>
        <div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
    </fieldset>
</div>
<div class="layui-col-xs9 layui-col-sm9 layui-col-md9 layui-col-lg9">

</div>-->
<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left:5px;" id="positionForm">
    <input type="hidden" name="id" value="#(model.id??)"/>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="width: 130px;"><span style="color: red">*</span>单位名称</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" placeholder="请输入社保地址名称">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"><span style="color: red">*</span>是否有社保账号</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="radio" name="isSocialSecurity" lay-filter="isSocialSecurity" lay-verify="required" value="1" title="是" #if(model==null || model.isSocialSecurity??=='1') checked #end>
            <input type="radio" name="isSocialSecurity" lay-filter="isSocialSecurity" lay-verify="required" value="0" title="否" #if(model.isSocialSecurity??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="width: 130px;" id="socialSecurityAccountLabel"><span style="color: red">#if(model.isSocialSecurity??=='1')*#end</span>社保账号</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="socialSecurityAccount" class="layui-input" #if(model.isSocialSecurity??=='1') lay-verify="required" #end value="#(model.socialSecurityAccount??)" placeholder="请输入社保账号">
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"  id="socialSecurityUserIdLabel"><span style="color: red">#if(model.isSocialSecurity??=='1')*#end</span>社保经办人</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <select id="socialSecurityUserId" name="socialSecurityUserId" #if(model.isSocialSecurity??=='1') lay-verify="required" #end  lay-search>
                <option value="">请选择负责人(可搜索)</option>
                #for(user : userList)
                <option value="#(user.id)" #if(user.id==model.socialSecurityUserId??) selected #end>#(user.name)(#(user.userName))</option>
                #end
            </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;"><span style="color: red">*</span>是否有公积金账号</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="radio" name="isProvidentFund" lay-filter="isProvidentFund" lay-verify="required" value="1" title="是" #if(model==null || model.isProvidentFund??=='1') checked #end>
            <input type="radio" name="isProvidentFund" lay-filter="isProvidentFund" lay-verify="required" value="0" title="否" #if(model.isProvidentFund??=='0') checked #end>
        </div>
    </div>



    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="width: 130px;" id="providentFundAccountLabel"><span style="color: red">#if(model.isProvidentFund??=='1')*#end</span>公积金账号</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="providentFundAccount" class="layui-input" #if(model.isProvidentFund??=='1') lay-verify="required" #end value="#(model.providentFundAccount??)" placeholder="请输入公积金账号">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px;" id="providentFundUserIdLabel"><span style="color: red">#if(model.isProvidentFund??=='1')*#end</span>公积金经办人</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <select id="providentFundUserId" name="providentFundUserId" #if(model.isProvidentFund??=='1') lay-verify="required" #end lay-search>
                <option value="">请选择负责人(可搜索)</option>
                #for(user : userList)
                <option value="#(user.id)" #if(user.id==model.providentFundUserId??) selected #end>#(user.name)(#(user.userName))</option>
                #end
            </select>
        </div>
    </div>


    <div class="layui-form-item">
        <label class="layui-form-label" style="padding: 8px 5px;width: 130px"><span style="color: red">*</span>是否可用</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="radio" name="isEnabled" lay-verify="required" value="1" title="是" #if(model==null || model.isEnabled??=='1') checked #end>
            <input type="radio" name="isEnabled" lay-verify="required" value="0" title="否" #if(model.isEnabled??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="width: 130px;"><span style="color: red">*</span>排序</label>
        <div class="layui-input-block" style="margin-left: 130px;">
            <input type="text" name="sort" class="layui-input" lay-verify="required|number" value="#(model.sort??)" placeholder="请输入排序">
        </div>
    </div>
    <div class="layui-form-item layui-form-text" style="padding-bottom: 50px;">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="remark" placeholder="请输入内容" class="layui-textarea">#(model.remark??)</textarea>
        </div>
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
