#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .layui-table-cell{
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        height: auto !important;
        word-break: break-all;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">


            <div class="layui-input-inline">
                <label class="layui-form-label">姓名：</label>
                <div class="layui-inline" style="float: left;" >
                    <input class="layui-input" name="name" id="name" >
                </div>
            </div>

            <!--<div class="layui-input-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-inline" style="float: left;" >
                    <select id="status" name="status" lay-filter="status">
                        <option value="">全部</option>
                        <option value="1">待提交</option>
                        <option value="2">审核中</option>
                        <option value="3">审核通过</option>
                        <option value="5">中止</option>
                    </select>
                </div>
            </div>-->
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:recruit:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            <input id="recruitId" name="recruitId" value="#(recruitId??)" type="hidden">
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    #[[
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="see">查看</a>

    ]]#

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad({"recruitId":$("#recruitId").val()});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }


        reloadTable=function () {

            msgLoad({"recruitId":$("#recruitId").val(),'name':$("#name").val()});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/recruitForm';
            pop_show("添加招聘需求申请",url,'100%','100%');
        });

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/getInterviewRecordPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'employeeName',title:'姓名', align: 'center', unresize: true}
                    ,{field:'gender',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.gender=='male'){
                                return '男';
                            }else if(d.gender=='female'){
                                return '女';
                            }
                        }}
                    ,{field:'age', title: '年龄', align: 'center', unresize: true,templet:function (d) {
                            return calculateAge(dateFormat(d.birthday,'yyyy-MM-dd'));
                        }}
                    ,{field:'channel', title: '招聘渠道', align: 'center', unresize: true,templet:function (d) {
                            if(d.channel=='1'){
                                return '最佳东方';
                            }else if(d.channel=='2'){
                                return 'BOSS';
                            }else if(d.channel=='3'){
                                return '前程无忧';
                            }else if(d.channel=='4'){
                                return '现场招聘会';
                            }else if(d.channel=='5'){
                                return '内部推荐';
                            }else if(d.channel=='6'){
                                return '其他';
                            }


                        }}
                    ,{field:'entry', title: '可入职日期', align: 'center', unresize: true,templet:function (d) {
                            return dateFormat(d.entry,'yyyy-MM-dd');
                        }}


                    ,{field:'createDate', title: '创建时间',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss')
                        }}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });

            function calculateAge(birthday) {
                const today = new Date();
                const birthDate = new Date(birthday);
                let age = today.getFullYear() - birthDate.getFullYear();
                const m = today.getMonth() - birthDate.getMonth();
                if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                    age--;
                }
                return age;
            }

            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='see'){
                    var url='#(ctxPath)/pers/approval/interviewRecordForm?id='+obj.data.id;
                    pop_show("查看",url,'100%','100%');
                }
            })


        };


    });
</script>
#end