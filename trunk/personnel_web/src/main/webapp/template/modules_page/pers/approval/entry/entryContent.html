<table class="layui-table">
    <tbody>
        <tr>
            <input id="currentStepAlias" value="#(currentStepAlias??)" type="hidden">
            <td colspan="8" align="center">员工入职登记表<button class="layui-btn" id="enclosure" style="float: right;" onclick="enclosure('#(entryInfo.employeeId??)')" type="button">查看附件</button></td>
        </tr>
        <tr>
            <td colspan="2" align="">填表日期:#date(entryInfo.saveDate,'yyyy-MM-dd')</td>
            <td colspan="2" align="">入职日期:#date(entryInfo.entryTime,'yyyy-MM-dd')</td>
            <td colspan="4" align="">入职部门:#(deptName??)</td>
        </tr>
        <tr>
            <td>申请职位</td>
            <td>#(positionName??)</td>
            <td colspan="3">
                试用期：#(entryInfo.probationSalary??)
            </td>
            <td colspan="3">
                转正后：#(entryInfo.permanentSalary??)
            </td>
        </tr>
        <tr>
            <td>姓名</td>
            <td>#(entryInfo.employeeName??)</td>
            <td>最高学历</td>
            <td>
                #if(entryInfo.highestEducation??=='shuo_shi')
                硕士
                #else if(entryInfo.highestEducation??=='yan_jiu_sheng')
                研究生
                #else if(entryInfo.highestEducation??=='ben_ke')
                本科
                #else if(entryInfo.highestEducation??=='da_zhuan')
                大专
                #else if(entryInfo.highestEducation??=='zhong_zhuan')
                中专或中技
                #else if(entryInfo.highestEducation??=='ji_gong')
                技工学校
                #else if(entryInfo.highestEducation??=='gao_zhong')
                高中
                #else if(entryInfo.highestEducation??=='chu_zhong')
                初中
                #else if(entryInfo.highestEducation??=='xiao_xue')
                小学
                #else if(entryInfo.highestEducation??=='wen_mang')
                文盲或半文盲
                #end

            </td>
            <td>性别</td>
            <td>#if(entryInfo.gender??=='male')男#else if(entryInfo.gender??=='female') 女 #else 未知 #end</td>
            <td colspan="2" rowspan="6">
                <img src="#(entryInfo.headPortrait??)">
            </td>
        </tr>
        <tr>
            <td>出生日期</td>
            <td>#date(entryInfo.birthday??,'yyyy-MM-dd')</td>
            <td>户口所在地</td>
            <td colspan="3">#(entryInfo.residentAddr??)</td>
            <!--<td colspan="2"></td>-->
        </tr>
        <tr>
            <td>身份证号码</td>
            <td colspan="2">#(entryInfo.idcard??)</td>
            <td>婚姻状况</td>
            <td colspan="2">#if(entryInfo.marriageStatus??=='1')已婚#else if(entryInfo.marriageStatus??=='0')未婚 #end</td>
        </tr>
        <tr>
            <td>身份证正面</td>
            <td colspan="2">
                <img style="cursor:pointer;" src="#(entryInfo.idcardFrontPath??)" onclick="renderImg('#(entryInfo.idcardFrontPath??)')">
            </td>
            <td>身份证反面</td>
            <td colspan="2">
                <img style="cursor:pointer;"  src="#(entryInfo.idcardContraryPath??)" onclick="renderImg('#(entryInfo.idcardContraryPath??)')">
            </td>
        </tr>
        <tr>
            <td>社会福利号码</td>
            <td colspan="2">#(entryInfo.socialNumber??)</td>
            <td>个人邮箱</td>
            <td colspan="2">#(entryInfo.email??)</td>

        </tr>
        <tr>
            <td>现住地址</td>
            <td colspan="2">#(entryInfo.liveAddr??)</td>
            <td>联系方式</td>
            <td colspan="2">#(entryInfo.phone??)</td>

        </tr>
        <tr>
            <td>籍贯</td>
            <td >#(entryInfo.nativelace??)</td>
            <td>政治面貌</td>
            <td>#(entryInfo.politicalOutlook??)</td>
            <td>外语水平</td>
            <td colspan="3">#(entryInfo.languageLevel??)</td>
        </tr>

        <tr>
            <td>技术职称证书</td>
            <td colspan="2">#(entryInfo.languageLevel??)</td>
            <td colspan="2">计算机水平</td>
            <td colspan="3">#(entryInfo.languageLevel??)</td>
        </tr>
        <tr>
            <td>合同类型</td>
            <td >
                #if(entryInfo.contractType??=='1')
                劳动合同
                #else if(entryInfo.contractType??=='2')
                劳务合同
                #else if(entryInfo.contractType??=='4')
                待补
                #else if(entryInfo.contractType??=='5')
                实习协议
                #else if(entryInfo.contractType??=='6')
                临时工合同
                #end
            </td>
            <td>开始时间</td>
            <td>#date(entryInfo.contractStartDate??,'yyyy-MM-dd')</td>
            <td>结束时间</td>
            <td >#date(entryInfo.contractEndDate??,'yyyy-MM-dd')</td>
            <td>合同归属地址</td>
            <td>#(entryInfo.contractAddr??)</td>
        </tr>
        <tr>
            <td>合同图片</td>
            <td>
                <img style="cursor:pointer;" src="#(entryInfo.contractPath1??)" onclick="renderImg('#(entryInfo.contractPath1??)')">
            </td>
            <td>
                <img style="cursor:pointer;"  src="#(entryInfo.contractPath2??)" onclick="renderImg('#(entryInfo.contractPath2??)')">
            </td>
            <td>
                <img style="cursor:pointer;"  src="#(entryInfo.contractPath3??)" onclick="renderImg('#(entryInfo.contractPath3??)')">
            </td>
            <td>
                <img style="cursor:pointer;" src="#(entryInfo.contractPath4??)" onclick="renderImg('#(entryInfo.contractPath4??)')">
            </td>
            <td>
                <img style="cursor:pointer;"  src="#(entryInfo.contractPath5??)" onclick="renderImg('#(entryInfo.contractPath5??)')">
            </td>
            <td>
                <img style="cursor:pointer;"  src="#(entryInfo.contractPath6??)" onclick="renderImg('#(entryInfo.contractPath6??)')">
            </td>
            <td></td>
        </tr>










        <tr>
            <td>紧急联系人</td>
            <td>#(entryInfo.emergencyContact??)</td>
            <td>关系</td>
            <td>#(entryInfo.relation??)</td>
            <td colspan="2">紧急联系电话</td>
            <td colspan="2">#(entryInfo.emergencyPhone??)</td>
        </tr>
        <tr>
            <td colspan="8">个人资料  家庭状况 FAMILY SITUATION</td>
        </tr>
        <tr>
            <td>姓名</td>
            <td>关系</td>
            <td colspan="2">工作单位</td>
            <td colspan="2">联系电话</td>
            <td colspan="2">职位</td>
        </tr>
        #if(familySituation.size()>0)
        #for(family : familySituation)
        <tr>
            <td>#(family.name??)</td>
            <td>#(family.relation??)</td>
            <td colspan="2">#(family.workUnit??)</td>
            <td colspan="2">#(family.phone??)</td>
            <td colspan="2">#(family.position??)</td>
        </tr>
        #end
        #else
        <tr style="height: 39px;">
            <td></td>
            <td></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
        </tr>
        #end

        <tr>
            <td colspan="8">教育经历 EDUCATIONAL BACKGROUND</td>
        </tr>
        <tr>
            <td>起止时间</td>
            <td colspan="3">所在院校（由高往下开始填写）</td>
            <td colspan="2">所修专业</td>
            <td colspan="2">学位（文凭）</td>
        </tr>
        #if(educationalBackground.size()>0)
        #for(educational : educationalBackground)
        <tr>
            <td>#date(educational.startDate,'yyyy-MM-dd')至#date(educational.endDate,'yyyy-MM-dd')</td>
            <td colspan="3">#(educational.school)</td>
            <td colspan="2">#(educational.major)</td>
            <td colspan="2">#(educational.diploma)</td>
        </tr>
        #end
        #else
        <tr style="height: 39px;">
            <td></td>
            <td colspan="3"></td>
            <td colspan="2"></td>
            <td colspan="2"></td>
        </tr>
        #end
        <tr>
            <td colspan="8">培训经历 TRAINING BACKGROUND</td>
        </tr>
        <tr>
            <td>起止时间</td>
            <td colspan="2">课程名称</td>
            <td colspan="3">学习形式（填写内部、外聘或外派培训等）</td>
            <td colspan="2">所获证书</td>
        </tr>
        #if(trainingBackground.size()>0)
        #for(training : trainingBackground)
        <tr>
            <td>#date(training.startDate??,'yyyy-MM-dd')至#date(training.endDate??,'yyyy-MM-dd')</td>
            <td colspan="2">#(training.course??)</td>
            <td colspan="3">#if(training.learningForm=='inside') 内部 #else if(training.learningForm=='expatriate') 外派 #else if(training.learningForm=='other') 其他  #end</td>
            <td colspan="2">#(training.certificate)</td>
        </tr>
        #end
        #else
        <tr style="height: 39px;">
            <td></td>
            <td colspan="2"></td>
            <td colspan="3"></td>
            <td colspan="2"></td>
        </tr>
        #end
        <tr>
            <td colspan="8">工作履历EMPLOYMENT HISTORY( 请从最近工作履历写起）*若曾在我司任职，有关资料必须详细填写</td>
        </tr>
        <tr>
            <td>起止时间</td>
            <td>公司名称</td>
            <td>职位</td>
            <td>离职原因</td>
            <td>最后薪金（税前）</td>
            <td>原公司人事部证明人</td>
            <td colspan="2">证明人办公电话</td>
        </tr>
        #if(employmentHistory.size()>0)
        #for(employment : employmentHistory)
        <tr>
            <td>#date(employment.startDate,'yyyy-MM-dd')至#date(employment.endDate,'yyyy-MM-dd')</td>
            <td>#(employment.company??)</td>
            <td>#(employment.position??)</td>
            <td>#(employment.quitReason??)</td>
            <td>#(employment.salary??)</td>
            <td>#(employment.witness??)</td>
            <td colspan="2">#(employment.phone??)</td>
        </tr>
        #end
        #else
        <tr style="height: 39px;">
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td colspan="2"></td>
        </tr>
        #end
        <tr>
            <td colspan="8">
                是否服从以下工作地点调动:#if(entryInfo.isWorkAddrTransfer=='1') 是 #else if(entryInfo.isWorkAddrTransfer=='0') 否 #end
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;#(entryInfo.workAddrTransfer??)
            </td>
        </tr>
        <tr>
            <td colspan="2">兴趣爱好</td>
            <td colspan="2">#(entryInfo.hobby)</td>
            <td >打字速度</td>
            <td colspan="3">英文 #(entryInfo.englishSpeed) 字/分钟、中文 #(entryInfo.chineseSpeed) 字/分钟</td>
        </tr>
        <tr>
            <td>能否出差</td>
            <td>#if(entryInfo.isOffSiteWork=='1') 是 #else if(entryInfo.isOffSiteWork=='0') 否 #end</td>
            <td>能否岗位调动</td>
            <td>#if(entryInfo.jobTransfer=='1') 是 #else if(entryInfo.jobTransfer=='0') 否 #end</td>
            <td>能否加班</td>
            <td>#if(entryInfo.isWorkOvertime=='1') 是 #else if(entryInfo.isWorkOvertime=='0') 否 #end</td>
            <td>能否吃住公司</td>
            <td>#if(entryInfo.isLiveCompany=='1') 是 #else if(entryInfo.isLiveCompany=='0') 否 #end</td>
        </tr>
        <tr>
            <td>能否出差</td>
            <td>#if(entryInfo.isOffSiteWork=='1') 是 #else if(entryInfo.isOffSiteWork=='0') 否 #end</td>
            <td>能否岗位调动</td>
            <td>#if(entryInfo.jobTransfer=='1') 是 #else if(entryInfo.jobTransfer=='0') 否 #end</td>
            <td>能否加班</td>
            <td>#if(entryInfo.isWorkOvertime=='1') 是 #else if(entryInfo.isWorkOvertime=='0') 否 #end</td>
            <td>能否吃住公司</td>
            <td>#if(entryInfo.isLiveCompany=='1') 是 #else if(entryInfo.isLiveCompany=='0') 否 #end</td>
        </tr>
        <tr>
            <td>招聘渠道</td>
            <td>
                #if(entryInfo.recruitChannel??=='online_recruitment')
                网络招聘
                #else if(entryInfo.recruitChannel??=='on_site_recruitment')
                现场招聘
                #else if(entryInfo.recruitChannel??=='internal_introduction')
                内部介绍
                #else if(entryInfo.recruitChannel??=='retirement_reemployment')
                离职返聘或退休返聘
                #else if(entryInfo.recruitChannel??=='labor_dispatch')
                劳务派遣
                #end

            </td>
            <td>介绍人</td>
            <td>#(entryInfo.introducer??)</td>
            <td>职位</td>
            <td>#(entryInfo.IntroducerPosition??)</td>
            <td>关系</td>
            <td>#(entryInfo.IntroducerRelation??)</td>
        </tr>
        #if(stepCode??0>=1)
        <tr>
            <td colspan="8">
                以下部分用人公司填写：
            </td>
        </tr>
        <tr>
            <td colspan="8">
                <!--人力资源部意见：-->
                用人部意见：
            </td>
        </tr>

        <tr>
            <form class="layui-form" id="hrForm" action="">
                <td colspan="2">
                    <div class="layui-inline">
                        <select name="hrOpinion" id="hrOpinion" lay-verify="required" #if(entryInfoStep1!=currentStepAlias?? || !isHandle) disabled #end >
                            <option value="">请选择</option>
                            <option value="1" #if('1'==entryInfo.hrOpinion??) selected #end>拒绝</option>
                            <option value="2" #if('2'==entryInfo.hrOpinion??) selected #end>再沟通</option>
                            <option value="3" #if('3'==entryInfo.hrOpinion??) selected #end>录用</option>
                        </select>
                    </div>
                </td>
                <td colspan="2">
                    面试人：
                    <div class="layui-inline">
                        <select name="hrInterviewers" id="hrInterviewers" lay-verify="required"  lay-search #if(entryInfoStep1!=currentStepAlias?? || !isHandle) disabled #end>
                            <option value="">请选择</option>
                            #for(user : userList)
                            <option value="#(user.id??)" #if(user.id==entryInfo.hrInterviewers??) selected #end>#(user.name)(#(user.userName))</option>
                            #end
                        </select>
                    </div>
                </td>
                <td colspan="2">
                    日期：
                    <div class="layui-input-inline">
                        <input type="text" lay-verify="required" id="hrDate" name="hrDate"  placeholder="" value="#date(entryInfo.hrDate??,'yyyy-MM-dd')" #if(entryInfoStep1!=currentStepAlias?? || !isHandle) disabled #end autocomplete="off" class="layui-input">
                    </div>
                </td>
                <td colspan="2">
                    #if(entryInfoStep1==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" lay-filter="saveHrBtn">保&nbsp;&nbsp;存</button>
                    #end
                </td>
            </form>
        </tr>
        #end

        #if(stepCode??0>=2)
        <tr>
            <td colspan="8">
                <!--用人部门意见：-->
                集团HR意见：
            </td>
        </tr>
        <form class="layui-form" id="edForm">
            <tr>
                <td colspan="2">
                    <div class="layui-inline">
                        <select name="edOpinion" id="edOpinion" lay-verify="required" #if(entryInfoStep2!=currentStepAlias?? || !isHandle) disabled #end >
                            <option value="">请选择</option>
                            <option value="1" #if('1'==entryInfo.edOpinion??) selected #end>拒绝</option>
                            <option value="2" #if('2'==entryInfo.edOpinion??) selected #end>再沟通</option>
                            <option value="3" #if('3'==entryInfo.edOpinion??) selected #end>录用</option>
                        </select>
                    </div>
                </td>
                <td colspan="2">
                    面试人：
                    <div class="layui-inline">
                        <select name="edInterviewers" id="edInterviewers" lay-verify="required"  lay-search #if(entryInfoStep2!=currentStepAlias?? || !isHandle) disabled #end>
                        <option value="">请选择</option>
                        #for(user : userList)
                        <option value="#(user.id??)" #if(user.id==entryInfo.edInterviewers??) selected #end>#(user.name)(#(user.userName))</option>
                        #end
                        </select>
                    </div>
                </td>
                <td colspan="2">
                    日期：
                    <div class="layui-input-inline">
                        <input type="text" id="edDate" name="edDate"  placeholder="" value="#date(entryInfo.edDate??,'yyyy-MM-dd')" #if(entryInfoStep2!=currentStepAlias?? || !isHandle) disabled #end autocomplete="off" class="layui-input">
                    </div>
                </td>
                <td colspan="2">
                    #if(entryInfoStep2==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" lay-filter="saveEdBtn">保&nbsp;&nbsp;存</button>
                    #end
                </td>
            </tr>
        </form>
        #end

        <!--#if(stepCode??0>=3)
        <tr>
            <td colspan="8">
                总经理意见：
            </td>
        </tr>
        <form class="layui-form" id="gmForm">
            <tr>
                <td colspan="2">
                    <div class="layui-input-inline">
                        <div class="layui-inline">
                            <select name="gmOpinion" id="gmOpinion" lay-verify="required" #if(entryInfoStep3!=currentStepAlias?? || !isHandle) disabled #end >
                                <option value="">请选择</option>
                                <option value="1" #if('1'==entryInfo.gmOpinion??) selected #end>拒绝</option>
                                <option value="2" #if('2'==entryInfo.gmOpinion??) selected #end>再沟通</option>
                                <option value="3" #if('3'==entryInfo.gmOpinion??) selected #end>录用</option>
                            </select>
                        </div>
                    </div>
                </td>
                <td colspan="2">
                    面试人：
                    <div class="layui-inline">
                        <select name="gmInterviewers" id="gmInterviewers" lay-verify="required"  lay-search #if(entryInfoStep3!=currentStepAlias?? || !isHandle) disabled #end>
                        <option value="">请选择</option>
                        #for(user : userList)
                        <option value="#(user.id??)" #if(user.id==entryInfo.gmInterviewers??) selected #end>#(user.name)(#(user.userName))</option>
                        #end
                        </select>
                    </div>
                </td>
                <td colspan="2">
                    日期：
                    <div class="layui-input-inline">
                        <input type="text" id="gmDate" name="gmDate"  placeholder="" value="#date(entryInfo.gmDate??,'yyyy-MM-dd')" #if(entryInfoStep3!=currentStepAlias?? || !isHandle) disabled #end autocomplete="off" class="layui-input">
                    </div>
                </td>
                <td colspan="2">
                    #if(entryInfoStep3==currentStepAlias?? && isHandle)
                    <button class="layui-btn" lay-submit="" lay-filter="saveGmBtn">保&nbsp;&nbsp;存</button>
                    #end
                </td>
            </tr>
        </form>
        #end-->
    </tbody>
</table>