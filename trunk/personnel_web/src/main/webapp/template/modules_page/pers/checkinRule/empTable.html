#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
            <div class="layui-row">
                <form class="layui-form layui-form-pane" method="" action="">
                    <div class="layui-row">
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="width:78px;width: 87px;">姓名</span>
                            <div class="layui-input-inline" style="width: 120px;">
                                <input type="text" id="fullName" class="layui-input" placeholder="请输入姓名搜索" autocomplete="off">
                            </div>
                        </div>
                        &nbsp;
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="padding: 8px 5px;width: 87px;">证件号码</span>
                            <div class="layui-input-inline">
                                <input type="text" id="idCard" class="layui-input" placeholder="请输入证件号码搜索" autocomplete="off">
                            </div>
                        </div>
                        &nbsp;
                        <!--<div class="layui-input-inline">
                            <span class="layui-form-label" style="padding: 8px 5px;width: 87px;">手机号码</span>
                            <div class="layui-input-inline" style="width: 180px;">
                                <input type="text" id="phoneNum" class="layui-input" placeholder="请输入手机号码搜索" autocomplete="off">
                            </div>
                        </div>-->
                        &nbsp;
                        <div class="layui-input-inline">
                            <span class="layui-form-label" style="padding: 8px 5px;width: 87px;">组织架构</span>
                            <div class="layui-input-inline" style="width:260px;">
                                <ul id="deptId" class="dtree" data-id="0" ></ul>
                            </div>
                        </div>
                        <div class="layui-input-inline" >
                            <span class="layui-form-label" style="width:78px;">状态</span>
                            <div class="layui-input-inline" style="width:140px;">
                                <select name="archiveStatus" id="archiveStatus">
                                    #dictOption("archive_status", "incumbency", "")
                                </select>
                            </div>
                        </div>

                        <div class="layui-btn-group" >
                            <button class="layui-btn" type="reset" id="resetBtn" style="margin-left: 20px;">重置</button>
                            <button type="button" id="btn-search" class="layui-btn mgl-20">查询</button>
                        </div>
                    </div>

                </form>
            </div>
            <div class="layui-row">
                <table id="employeeTable" lay-filter="employeeTable"></table>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="choice">选择</a>

    </div>
</script>


<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });


    layui.extend({
        dtree: 'layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['dtree','layer','jquery'], function(){
        var dtree = layui.dtree, layer = layui.layer, $ = layui.jquery;

        var DemoTree;

        $.post('#(ctxPath)/persOrg/orgDtree',{},function (r) {
            var jsonArray=[];
            $.each(r.data,function (index,item) {
                var json={"id":item.id,"title":item.name,"checkArr":"0","parentId":item.pId};
                var flag=isUpdateParentId(json.parentId,r.data);
                if(flag){
                    json.parentId='0';
                }
                jsonArray[index]=json;
            });
            // 初始化树
            DemoTree = dtree.renderSelect({
                elem: "#deptId",
                dataFormat: "list",
                data:jsonArray,
                initLevel:10,
                selectCardHeight:"500"
                //url: "#(ctxPath)/persOrg/orgDtree" // 使用url加载（可与data加载同时存在）
            });

            $("#resetBtn").on('click',function () {

                DemoTree.menubarMethod().refreshTree();
            })
        });

        function isUpdateParentId(pid,array){
            var flag=true;
            $.each(array,function (i,it) {
                if(pid==it.id){
                    flag=false;
                    return false;
                }
            });
            return flag;
        }

        $("#reset").on('click',function () {
            $(".layui-select-title input[dtree-id='deptId']").val('');
            DemoTree.cancelNavThis();
        })

        // 绑定节点点击
        dtree.on("node('deptId')" ,function(obj){

        });
    });

    layui.use(['table', 'vip_table','form'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeTable'
            ,form=layui.form
        ;

        loadTable();

        // 表格加载渲染
        function loadTable() {
            layer.load();

            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/persOrgEmployee/pageTable'
                ,where : {orgParentIds:$("#orgId").val(),"archiveStatus":"incumbency"}//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {field:'workNum', title:'工号',width:85, align:'center'}
                    ,{field:'fullName', title:'姓名',width:90, align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
                    ,{field:'', title:'部门',minWidth:150, align:'center',templet:function (d) {
                            if(d.org==undefined || d.org.orgName==undefined){
                                return '';
                            }else{
                                return d.org.orgName;
                            }
                        }}
                    ,{field:'', title:'职位',minWidth:130, align:'center',templet:function (d) {
                            if(d.org==undefined || d.org.positionName==undefined){
                                return '';
                            }else{
                                return d.org.positionName;
                            }
                        }}
                    ,{field:'', title:'角色',minWidth:120, align:'center',templet:function (d) {
                            if(d.org==undefined || d.org.roleName==undefined){
                                return '';
                            }else{
                                return d.org.roleName;
                            }
                        }}
                    ,{field:'', title:'性别', width:60, align:'center', templet:'#dictTpl("sex", "sex")'}
                    ,{field:'', title:'民族', minWidth:110, align:'center', templet:'#dictTpl("nation", "nationality")'}
                    ,{field:'phoneNum', title:'手机号', minWidth:90, align:'center'}
                    ,{field:'', title:'入职日期', width:90, align:'center',templet:"<div>{{ dateFormat(d.entryTime,'yyyy-MM-dd') }}</div>"}
                    ,{title:'操作', fixed:'right', width:50, align:'center', toolbar:'#empTableBar'}

                ]]
                ,page : true
                ,done:function () {
                    //
                    layer.closeAll('loading');
                }
            });
        }

        //监听工具条
        table.on('tool('+tableId+')', function(obj) {
            if (obj.event === 'choice') {
                parent.setQuitEmpIdValue(obj.data.id,obj.data.fullName,obj.data.org.orgName);
                layer.msg('选择成功', {icon: 1, offset: 'auto'});
            }
        });
        //重载表格，跳转到第一页
        pageTableReload = function () {
            layer.load();
            tableReload(tableId,{phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
                'deptId':$(".layui-select-title input[dtree-id='deptId']").val(),"sort":$("#sort").val(),'position':$("#position").val()});
        }

        //重载表格，跳转当前页面
        pageTableReloadCurrPage = function(){
            layer.load();
            var currPage=$(".layui-laypage-skip").children("input").val();
            table.reload(tableId,{'where':{phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
                    deptId:$(".layui-select-title input[dtree-id='deptId']").val(),"sort":$("#sort").val(),'position':$("#position").val()},page:{'curr':currPage}});
        }

        showPhotos=function(images) {
            layer.photos({
                /*area: '400px',*/
                shade: [0.85, '#000'],
                anim: 0,
                photos: {
                    "title": "附件预览",
                    "id": 'showImages',
                    "data": images
                }
            });
        }

        //搜索按钮点击事件
        $('#btn-search').on('click', function() {
            pageTableReload();
        });
        //添加按钮点击事件
        $('#btn-add').on('click', function() {
            pop_show('添加', '#(ctxPath)/persOrgEmployee/add', '900', '');
        });
        // 刷新
        $('#btn-refresh').on('click', function () {
            pageTableReload();
        });


    });
</script>
#end