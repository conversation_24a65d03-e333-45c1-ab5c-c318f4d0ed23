#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算管理#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
#end

#define js()
<script>
    layui.extend({
        dtree: '/static/js/extend/layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['dtree','layer','jquery','table','laydate','form'], function(){
        var dtree = layui.dtree, layer = layui.layer;
        var table = layui.table, $ = layui.$, form=layui.form,laydate = layui.laydate;

        var date=new Date();
        var month=date.getMonth()+1;
        var day=date.getDate();
        if(month<10){
            $("#monthSelect").find("option[value='0"+month+"']").prop("selected",true);
            $("#endMonthSelect").find("option[value='0"+month+"']").prop("selected",true);
            form.render("select");
            //loadMemberTable({'month':"0"+month});

            setDaysSelect("0"+month);
            setEndDaysSelect("0"+month);


        }else{
            $("#monthSelect").find("option[value='"+month+"']").prop("selected",true);
            $("#endMonthSelect").find("option[value='"+month+"']").prop("selected",true);
            form.render("select");
            //loadMemberTable({'month':month});
            setDaysSelect(month);
            setEndDaysSelect(month);

        }
        if(day<10){
            $("#daySelect").children().eq(0).prop("selected",true);
            $("#endDaySelect").children("option:last-child").prop("selected",true);
            form.render('select');
        }else{
            $("#daySelect").children().eq(0).prop("selected",true);
            $("#endDaySelect").children("option:last-child").prop("selected",true);
            form.render('select');
        }


        reloadMemberTable();

        $.post('#(ctxPath)/persOrg/orgDtree',{},function (r) {
            var jsonArray=[];
            $.each(r.data,function (index,item) {
                var json={"id":item.id,"title":item.name,"checkArr":"0","parentId":item.pId};
                var flag=isUpdateParentId(json.parentId,r.data);
                if(flag){
                    json.parentId='0';
                }
                jsonArray[index]=json;
            });
            // 初始化树
            DemoTree = dtree.renderSelect({
                elem: "#deptId",
                dataFormat: "list",
                data:jsonArray,
                initLevel:10,
                selectCardHeight:"500"
                //url: "#(ctxPath)/persOrg/orgDtree" // 使用url加载（可与data加载同时存在）
            });
        });

        // 绑定节点点击
        dtree.on("node('deptId')" ,function(obj){
            //loadMemberTable({"month":$("#monthSelect").val(),'deptId':obj.param.nodeId});
        });

        function getDays(month) {
            var dates=['01','02','03','04','05','06','07','08','09','10','11','12','13','14','15','16','17','18','19','20','21','22','23','24','25','26','27','28','29']
            if("01"==month||"03"==month||"05"==month||"07"==month||"08"==month||"10"==month||"12"==month){
                dates.push("30")
                dates.push("31")
            }else if("02"==month){

            }else{
                dates.push("30")
            }
            return dates;
        }

        function isUpdateParentId(pid,array){
            var flag=true;
            $.each(array,function (i,it) {
                if(pid==it.id){
                    flag=false;
                    return false;
                }
            });
            return flag;
        }

        $("#reset").on('click',function () {
            $(".layui-select-title input[dtree-id='deptId']").val('');
            DemoTree.cancelNavThis();
        })

        $("#query").on('click',function () {

            var start= Number($("#monthSelect").val()+$("#daySelect").val());
            var end= Number($("#endMonthSelect").val()+$("#endDaySelect").val());

            loadMemberTable({"start":start,'end':end,'deptId':$(".layui-select-title input[dtree-id='deptId']").val()});
        });

        $("#export").on('click',function () {
            var start= Number($("#monthSelect").val()+$("#daySelect").val());
            var end= Number($("#endMonthSelect").val()+$("#endDaySelect").val());
            layer.load();
            var url='#(ctxPath)/persOrgEmployee/exportBirthdayEmployeeList?start='+start+"&end="+end+"&deptId="+$(".layui-select-title input[dtree-id='deptId']").val();
            window.location.href=url;

            setTimeout(function(){
                layer.closeAll('loading');
            },1500);

        })


        form.on('select(monthSelect)',function (obj) {
            setDaysSelect(obj.value);
            //loadMemberTable({"month":obj.value,'deptId':$(".layui-select-title input[dtree-id='deptId']").val()});
        })
        function setDaysSelect(value){
            var dates=getDays(value);
            var str="";
            $.each(dates,function (index,item) {
                str+="<option value='"+item+"'>"+item+"日</option>"
            })
            $("#daySelect").html(str);
            form.render('select');
        }

        form.on('select(endMonthSelect)',function (obj) {
            setEndDaysSelect(obj.value);
        })

        function setEndDaysSelect(value){
            var dates=getDays(value);
            var str="";
            $.each(dates,function (index,item) {
                str+="<option value='"+item+"'>"+item+"日</option>"
            })
            $("#endDaySelect").html(str);
            form.render('select');
        }

        function reloadMemberTable() {
            var start= Number($("#monthSelect").val()+$("#daySelect").val());
            var end= Number($("#endMonthSelect").val()+$("#endDaySelect").val());

            loadMemberTable({"start":start,'end':end,'deptId':$(".layui-select-title input[dtree-id='deptId']").val()});
        }

        function loadMemberTable(data){
            table.render({
                id : 'memberTable'
                ,elem : '#memberTable'
                ,method : 'POST'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/persOrgEmployee/findBirthdayEmployeePage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'numbers', title: '序号', align: 'center', unresize: true}
                    ,{field:'orgName', title: '部门', align: 'center', unresize: true,templet:"#[[<div>{{ d.org.orgName }}</div>]]#"}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true,templet:"#[[<div>{{d.org.positionName}}</div>]]#"}
                    ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                    ,{field:'gender', title: '性别', align: 'center',templet:"#[[<div>{{#if(d.sex==='male'){}}男{{#}else if(d.sex==='female'){}}女{{#}else{}}未知{{#}}}</div>]]#"}
                    ,{field:'birthday', title:'生日', align:'center',templet:function (d) {
                            if(typeof(d.birthday)==='undefined'){
                                return ''
                            }else{
                                return d.birthday.substring(5,d.birthday.length);
                            }
                        }}
                ]]
                ,page : true
            });
        }
    });
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" style="margin-top: 20px;">
            <label class="layui-form-label">开始日期：</label>
            <div class="layui-inline" style="width: 70px;float: left;">
                <select id="monthSelect" lay-filter="monthSelect">
                    <option value="01">1月</option>
                    <option value="02">2月</option>
                    <option value="03">3月</option>
                    <option value="04">4月</option>
                    <option value="05">5月</option>
                    <option value="06">6月</option>
                    <option value="07">7月</option>
                    <option value="08">8月</option>
                    <option value="09">9月</option>
                    <option value="10">10月</option>
                    <option value="11">11月</option>
                    <option value="12">12月</option>
                </select>
            </div>
            <div class="layui-inline" style="float: left">
                <div class="layui-inline" style="width: 70px;float: left;">
                    <select id="daySelect" lay-filter="daySelect">


                    </select>
                </div>
            </div>
            <label class="layui-form-label">结束日期：</label>
            <div class="layui-inline" style="width: 70px;float: left;">
                <select id="endMonthSelect" lay-filter="endMonthSelect">
                    <option value="01">1月</option>
                    <option value="02">2月</option>
                    <option value="03">3月</option>
                    <option value="04">4月</option>
                    <option value="05">5月</option>
                    <option value="06">6月</option>
                    <option value="07">7月</option>
                    <option value="08">8月</option>
                    <option value="09">9月</option>
                    <option value="10">10月</option>
                    <option value="11">11月</option>
                    <option value="12">12月</option>
                </select>
            </div>
            <div class="layui-inline" style="float: left">
                <div class="layui-inline" style="width: 70px;float: left;">
                    <select id="endDaySelect" lay-filter="daySelect">


                    </select>
                </div>
            </div>
            <div class="layui-input-inline">
                <span class="layui-form-label" style="padding: 8px 5px;">组织架构</span>
                <div class="layui-input-inline" style="width:260px;">
                    <ul id="deptId" class="dtree" data-id="0" ></ul>
                </div>
            </div>

            <button class="layui-btn" type="button" id="query" >查询</button>
            <button class="layui-btn" type="button" id="export" >导出</button>
        </form>
    </div>
    <div class="layui-row" style="padding-left: 10px;">
        <table id="memberTable" lay-filter="memberTable"></table>
    </div>
</div>
#end