#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()车型管理#end

#define css()
<style>

</style>
#end


#define content()
<div class="layui-collapse " style="padding-top: 20px;">
    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
        <div class="layui-row" style="display: inline-flex;">
            <label style="line-height: 40px;width: 140px;padding: 0 20px;">角色名称</label>
            <input class="layui-input" name="roleName" id="roleName" placeholder="" autocomplete="off">
            <button class="layui-btn" type="button" id="search" style="margin-left: 10px;" >搜索</button>
            #shiroHasPermission("mainRoleManage:addBtn")
            <button class="layui-btn" type="button" id="add">添加</button>
            #end

        </div>
    </form>
    <div class="layui-row">
        <table class="layui-table" id="carTypeTable" lay-filter="carTypeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    var table,form,$ ;
    layui.use(['table','form'],function(){
        table = layui.table ,
            form = layui.form
        $ = layui.$ ;

        table.render({
            id:'carTypeTable',
            elem: '#carTypeTable'
            ,url : '#(ctxPath)/pers/role/pageList'
            ,method : 'POST'
            ,height:$(document).height()*0.85
            ,cols: [[
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field: 'roleName', title: '角色名称',unresize:true}
                ,{field: 'remark', title: '描述',unresize:true}
                ,{field: 'isEnable', title: '是否可用',unresize:true,templet:"<div>{{d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled == '0'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                ,{field: 'isEnable', title: '是否总部管理人员',unresize:true,templet:"<div>{{d.isGroupManager == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isGroupManager == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                ,{field: 'isEnable', title: '是否基地管理人员',unresize:true,templet:"<div>{{d.isBaseManager == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isBaseManager == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                ,{field: 'isEnable', title: '是否市场管理人员',unresize:true,templet:"<div>{{d.isMarketManager == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isMarketManager == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                ,{field: 'isEnable', title: '是否餐饮人员',unresize:true,templet:"<div>{{d.isCateringStaff == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isCateringStaff == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                ,{field: 'isEnable', title: '是否财务人员',unresize:true,templet:"<div>{{d.isFinanceStaff == '1'? '<span class='layui-badge layui-bg-green'>是</span>':d.isFinanceStaff == '0'? '<span class='layui-badge'>否</span>':'- -'}}</div>"}
                ,{title: '操作',toolbar:'#toolBar',unresize:true}
            ]],
            page : true,
            limit : 15,
            limits: [15,20,25]
        });



        table.on('tool(carTypeTable)',function(obj){
            if (obj.event === 'edit') {
                var url = "#(ctxPath)/pers/role/form?id=" + obj.data.id ;
                layerShow("编辑角色",url,550,600);
            } else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/pers/role/del?"+obj.data.id,
                        type:'post',
                        data:{"id":obj.data.id},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                carTypeTableReload();
                            }
                            layer.close(index);
                        }
                    });
                });
            }
        });



        carTypeTableReload=function(){
            var roleName=$("#roleName").val();
            table.reload('carTypeTable',{'where':{'roleName':roleName}});
        }

        $("#search").on('click',function () {
            carTypeTableReload();
        });

        $("#add").on('click',function () {
            var url = "#(ctxPath)/pers/role/form";
            layerShow("添加角色",url,550,600);
        });


    }) ;
</script>
<script type="text/html" id="toolBar">
    #shiroHasPermission("mainRoleManage:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
</script>
#end