#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()通知公告表单页面#end

#define css()
<style>

</style>
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form id="noticeForm" class="layui-form layui-form-pane" lay-filter="layform">
            <div class="layui-row layui-col-space10">
            	<div class="layui-col-xs5 layui-col-sm5 layui-col-md5 layui-col-lg5">
                    <div class="layui-form-item">
<!--                         <input type="button" value="选择查阅人员" id="chooseUser" class="layui-btn layui-btn-radius"/> -->
                        <div class="layui-form-item">
							<label class="layui-form-label">组织</label>
							<div class="layui-input-block">
								<div id="noticeOrgXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                        <div class="layui-form-item">
							<label class="layui-form-label">角色</label>
							<div class="layui-input-block">
								<div id="noticeRoleXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                        <div class="layui-form-item">
							<label class="layui-form-label">分组</label>
							<div class="layui-input-block">
								<div id="noticeGroupXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                        <div class="layui-form-item">
							<label class="layui-form-label">员工</label>
							<div class="layui-input-block">
								<div id="noticeUserXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
<!--                         <div class="layui-row"> -->
<!-- 	                        <table class="layui-table"> -->
<!-- 	                            <thead> -->
<!-- 	                            <tr> -->
<!-- 	                                <th>类型</th> -->
<!-- 	                                <th>名称</th> -->
<!-- 	                                <th>操作</th> -->
<!-- 	                            </tr> -->
<!-- 	                            </thead> -->
<!-- 	                            <tbody id="relTable"></tbody> -->
<!-- 	                        </table> -->
<!--                         </div> -->
                    </div>
                </div>
                <div class="layui-col-xs7 layui-col-sm7 layui-col-md7 layui-col-lg7">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>公告类型</label>
                        <div class="layui-input-block">
                            <select name="notice.noticeTypeId" lay-verify="required">
                                <option value="">请选择公告类型</option>
                                #for(t : typeList)
                                	<option value="#(t.id)" #(notice != null ? (t.id == notice.noticeTypeId?'selected':'') :'')>#(t.typeName)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>公告标题</label>
                        <div class="layui-input-block">
                            <input type="text" name="notice.noticeTitle" autocomplete="off" class="layui-input" value="#(notice.noticeTitle??)" lay-verify="required">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>公告内容</label>
                        <div class="layui-input-block">
                            <textarea id="textA" rows="7" class="layui-textarea"></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">
							<button id="uploadFile" type="button" class="layui-btn layui-btn-xs">上传文件</button>
                        </label>
                        <div class="layui-input-block">
                            <div class="layui-upload">
                                <div class="layui-upload-list">
                                    <table class="layui-table">
                                        <thead>
	                                        <tr>
	                                            <th style="width:50px !important;">文件名</th>
	                                            <th>大小</th>
	                                            <th style="width:90px !important;">状态</th>
	                                            <th>操作</th>
	                                        </tr>
                                        </thead>
                                        <tbody id="demoList"></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                	<input type="hidden" id="orgIds" name="orgIds" value="#(orgIds??)">
                	<input type="hidden" id="roleIds" name="roleIds" value="#(roleIds??)">
                	<input type="hidden" id="groupIds" name="groupIds" value="#(groupIds??)">
                	<input type="hidden" id="userIds" name="userIds" value="#(userIds??)">
                    <input type="hidden" id="commonUpload" value="#(commonUpload??)"/>
<!--                     <input type="hidden" id="paramCount" name="paramCount" value="0"/> -->
                    <input type="hidden" id="fileUrl" name="fileUrl" value=""/>
                    <input type="hidden" id="fileIds" name="fileIds" value=""/>
                    <input type="hidden" id="showContent" value="#(notice.noticeContent??)"/>
                    <input type="hidden" id="content" name="notice.noticeContent"/>
                    <input type="hidden" id="releaseStatus" name="notice.releaseStatus" value=""/>
                    <input type="hidden" id="noticeId" name="notice.id" value="#(notice.id??)"/>
                    <button id="saveUpload" type="button" class="layui-btn">保存上传</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                   	<button id="confirmBtn1" class="layui-btn" lay-submit=""  lay-filter="confirmBtn1">存为草稿</button>
                    <button id="confirmBtn2" class="layui-btn" lay-submit=""  lay-filter="confirmBtn2">发&nbsp;&nbsp;布</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/xm-select.js"></script>
<script type="text/javascript">
    layui.use(['form','layedit','upload','laytpl','table'],function(){
        var form = layui.form;
        var $ = layui.$;
        var layedit = layui.layedit;
        var upload = layui.upload;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table=layui.table;
        var userArr= [];//新增时用
        var fileArr=[];//文件修改时用
        var fileIdArr=[];//文件修改时用
        
		//建立编辑器
        var index = layedit.build('textA',{
            height: 180,
            tool: [
                'strong' //加粗
                ,'italic' //斜体
                ,'underline' //下划线
                ,'del' //作废线
                ,'|' //分割线
                ,'left' //左对齐
                ,'center' //居中对齐
                ,'right' //右对齐
            ]
        });
        
    	var noticeOrgXmSelect = xmSelect.render({
    		el:'#noticeOrgXmSelect'
   			, model:{label:{type:'text'}}
    		, toolbar: { show: true }
    		, filterable:true
    		, direction: 'down'
    		, tips:'请选择组织'
    		, tree:{
    			show:true
    			, strict:false
    			, expandedKeys:true
    		}
    		, height:'400px'
   			, autoRow:true
    		, on:function(data){
    			var dataArray = data.arr;//data.arr:  当前多选已选中的数据
    			if(dataArray.length>0){
    				var selectIdsArray = new Array();
    				for (var i = 0; i < dataArray.length; i++) {
    					selectIdsArray.push(dataArray[i].value);
    				}
    				$('#orgIds').val(selectIdsArray.toString());
    			}else{
    				$('#orgIds').val('');
    			}
    		}
    	});
    	$.post('#(ctxPath)/persOrg/persOrgXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var initValueArray = null;
    			var selectIds = $('#orgIds').val();
				if(selectIds.indexOf(",")!=-1){
					initValueArray = selectIds.split(',');
					noticeOrgXmSelect.update({
	    				data:rep.persOrgSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
					noticeOrgXmSelect.update({
	    				data:rep.persOrgSelectTree
	    				, initValue:[selectIds]
	    			});
				}
    		}
    	});
    	
    	var noticeRoleXmSelect = xmSelect.render({
    		el:'#noticeRoleXmSelect'
   			, model:{label:{type:'text'}}
    		, toolbar: { show: true }
    		, filterable:true
    		, direction: 'down'
    		, tips:'请选择角色'
    		, tree:{
    			show:true
    			, strict:false
    		}
   			, autoRow:true
    		, on:function(data){
    			var dataArray = data.arr;//data.arr:  当前多选已选中的数据
    			if(dataArray.length>0){
    				var selectIdsArray = new Array();
    				for (var i = 0; i < dataArray.length; i++) {
    					selectIdsArray.push(dataArray[i].value);
    				}
    				$('#roleIds').val(selectIdsArray.toString());
    			}else{
    				$('#roleIds').val('');
    			}
    		}
    	});
    	$.post('#(ctxPath)/pers/role/mainRoleXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var initValueArray = null;
    			var selectIds = $('#roleIds').val();
				if(selectIds.indexOf(",")!=-1){
					initValueArray = selectIds.split(',');
					noticeRoleXmSelect.update({
	    				data:rep.mainRoleSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
	    			noticeRoleXmSelect.update({
	    				data:rep.mainRoleSelectTree
	    				, initValue:[selectIds]
	    			});
				}
    		}
    	});
    	
    	var noticeGroupXmSelect = xmSelect.render({
    		el:'#noticeGroupXmSelect'
   			, model:{label:{type:'text'}}
    		, toolbar: { show: true }
    		, filterable:true
    		, direction: 'down'
    		, tips:'请选择分组'
    		, tree:{
    			show:true
    			, strict:false
    		}
    		, height:'200px'
   			, autoRow:true
    		, on:function(data){
    			var dataArray = data.arr;//data.arr:  当前多选已选中的数据
    			if(dataArray.length>0){
    				var selectIdsArray = new Array();
    				for (var i = 0; i < dataArray.length; i++) {
    					selectIdsArray.push(dataArray[i].value);
    				}
    				$('#groupIds').val(selectIdsArray.toString());
    			}else{
    				$('#groupIds').val('');
    			}
    		}
    	});
    	$.post('#(ctxPath)/group/groupXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var initValueArray = null;
    			var selectIds = $('#groupIds').val();
				if(selectIds.indexOf(",")!=-1){
					initValueArray = selectIds.split(',');
					noticeGroupXmSelect.update({
	    				data:rep.groupSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
	    			noticeGroupXmSelect.update({
	    				data:rep.groupSelectTree
	    				, initValue:[selectIds]
	    			});
				}
    		}
    	});
    	
    	var noticeUserXmSelect = xmSelect.render({
    		el:'#noticeUserXmSelect'
   			, model:{label:{type:'text'}}
    		, toolbar: { show: true }
    		, filterable:true
    		, direction: 'down'
    		, tips:'请选择用户'
    		, tree:{
    			show:true
    			, strict:false
    		}
    		, height:'200px'
   			, autoRow:true
    		, on:function(data){
    			var dataArray = data.arr;//data.arr:  当前多选已选中的数据
    			if(dataArray.length>0){
    				var selectIdsArray = new Array();
    				for (var i = 0; i < dataArray.length; i++) {
    					selectIdsArray.push(dataArray[i].value);
    				}
    				$('#userIds').val(selectIdsArray.toString());
    			}else{
    				$('#userIds').val('');
    			}
    		}
    	});
    	$.post('#(ctxPath)/user/userXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var initValueArray = null;
    			var selectIds = $('#userIds').val();
				if(selectIds.indexOf(",")!=-1){
					initValueArray = selectIds.split(',');
					noticeUserXmSelect.update({
	    				data:rep.userSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
	    			noticeUserXmSelect.update({
	    				data:rep.userSelectTree
	    				, initValue:[selectIds]
	    			});
				}
    		}
    	});

        // 获取用户信息
//         $("#chooseUser").click(function(){
//             $(this).blur();
//             var url = "#(ctxPath)/pers/notice/chooseUser" ;
//             pop_show("选择用户信息",url,800,500);
//         });


        //获取查阅用户(新增查阅用户tr)
//         getUserTr=function(orgId,userId,name){
//             var paramCount = Number($("#paramCount").val()) + 1;
//             for(var i=0;i<userArr.length;i++){
//                 if(userArr[i] == userId){
//                     layer.msg(name + '已添加', function () {});
//                     return;
//                 }
//             }
//             userArr.push(userId);
//             $("#paramCount").val(paramCount);
//             $("#userTable").append(saveUserTpl(orgId,userId,name,paramCount));

//         }


        //新增时查阅用户模板
//         saveUserTpl=function(orgId,userId,name,index){
//             var trStr='<tr id="itemDetail-'+index+'">'
//                 +'<td>'+ name+'</td>'
//                 +'<td>'+'未查阅'+'</td>'
//                 +'<td>' +'- -'+'</td>'
//                 +'<td>'
//                 +'<input type="hidden" name="itemDetailList['+index+'].userId" value="'+userId+'">'
//                 +'<input type="hidden" name="itemDetailList['+index+'].orgId" value="'+orgId+'">'
//                 +'<a class="layui-btn layui-btn-danger layui-btn-xs" onclick=\'delTpl("itemDetail-'+index+'","'+userId+'")\'>作废</a>'
//                 +'</td>'
//                 +'</tr>'
//             return trStr;
//         }


        //修改时文件展示模板
        updateFileTpl=function(index,urlB,url,fileId){
            var trStr='<tr id="upload-'+ index +'">'
                +'<td>'+ url +'</td>'
                +'<td>'+'- -'+'</td>'
                +'<td>' +'已上传'+'</td>'
                +'<td>'
                +'<button class="layui-btn layui-btn-xs layui-btn-danger" onclick=\'delFile("'+urlB+'","'+index+'","'+fileId+'")\'>作废</button>'
                +'</td>'
                +'</tr>'
            return trStr;
        }


        //修改时作废文件
        delFile=function(url,index,fileId){
            for(var i=0;i<fileArr.length;i++){
                if(fileArr[i] == url){
                    fileArr.splice($.inArray(fileArr[i],fileArr),1);
                    break;
                }
            }
            for(var i=0;i<fileIdArr.length;i++){
                if(fileIdArr[i] == fileId){
                    fileIdArr.splice($.inArray(fileIdArr[i],fileIdArr),1);
                    break;
                }
            }
            $("#upload-"+index).remove();
        }



        //修改时查阅人展示模板
        updateUserTpl=function(orgId,userId,name,isReadName,isReadTime,index){
            var trStr='<tr id="itemDetail-'+index+'">'
                +'<td>'+ name+'</td>'
                +'<td>'+isReadName+'</td>'
                +'<td>' +isReadTime+'</td>'
                +'<td>'
                +'<input type="hidden" name="itemDetailList['+index+'].userId" value="'+userId+'">'
                +'<input type="hidden" name="itemDetailList['+index+'].orgId" value="'+orgId+'">'
                +'<a class="layui-btn layui-btn-danger layui-btn-xs" onclick=\'delTpl("itemDetail-'+index+'","'+userId+'")\'>作废</a>'
                +'</td>'
                +'</tr>'
            return trStr;
        }


        //作废用户 tr标签
        delTpl=function(itemDetailTrId,userId){
            for(var i=0;i<userArr.length;i++){
                if(userArr[i] == userId){
                    userArr.splice($.inArray(userArr[i],userArr),1);
                    break;
                }
            }
            $("#"+itemDetailTrId).remove();
        }


        //获取文件表格和查阅人数据
        function loadFilesAndUsers(){
            if($('#noticeId').val() != null && $('#noticeId').val() != '') {
                util.sendAjax({
                    url: '#(ctxPath)/pers/notice/getFilesAndUsers',
                    type: 'post',
                    data: {'id': $('#noticeId').val()},
                    notice: false,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            //处理文件
                            var fileList = returnData.fileList;
                            var userList = returnData.userList;
                            $("#paramCount").val(userList.length);
                            for(var i=0;i<fileList.length;i++){
                                //处理文件表格回显
                                var title = fileList[i].url.substring(fileList[i].url.lastIndexOf("/") + 1,fileList[i].url.length);
                                if(title != null && title.length > 15){
                                    title = "..."+title.substring(title.length-15,title.length);
                                }
                                $("#demoList").append(updateFileTpl(i,fileList[i].url,title,fileList[i].fileId));
                                fileArr.push(fileList[i].url);
                                fileIdArr.push(fileList[i].fileId);
                            }
//                             for(var i=0;i<userList.length;i++){
//                                 userArr.push(userList[i].userId);
//                                 var isRead = userList[i].isRead;
//                                 var isReadName = '- -';
//                                 if(isRead == '0'){
//                                     isReadName = "未读";
//                                 }else if(isRead == '1'){
//                                     isReadName = "已读";
//                                 }
//                                 //处理查阅时间
//                                 var isReadTime = userList[i].isReadTime;
//                                 if(isReadTime == null || isReadTime == ''){
//                                     isReadTime = '- -';
//                                 }
//                                 $("#userTable").append(updateUserTpl(userList[i].orgId,userList[i].userId,userList[i].name,
//                                     isReadName,isReadTime,i + 1));
//                             }
                        }
                    }
                });
            }
        }


        //多文件列表示例
        var demoListView = $('#demoList')
            ,uploadListIns = upload.render({
            elem: '#uploadFile'
            ,url: $("#commonUpload").val() +'/upload?bucket=notice'
            ,accept: 'file'
            ,multiple: true
            ,auto: false
            ,bindAction: '#saveUpload'
            ,choose: function(obj){
                var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                //读取本地文件
                obj.preview(function(index, file, result){
                    var fileName = file.name;
                    if(file.name !='' && file.name.length > 15){
                        fileName = "..."+file.name.substring(file.name.length-15,file.name.length);
                    }
                    var tr = $(['<tr id="upload-'+ index +'">'
                        ,'<td>'+ fileName +'</td>'
                        ,'<td>'+ (file.size/1014).toFixed(1) +'kb</td>'
                        ,'<td>等待上传</td>'
                        ,'<td>'
                        ,'<button class="layui-btn layui-btn-xs demo-reload layui-hide">重传</button>'
                        ,'<button class="layui-btn layui-btn-xs layui-btn-danger demo-delete">作废</button>'
                        ,'</td>'
                        ,'</tr>'].join(''));

                    //单个重传
                    tr.find('.demo-reload').on('click', function(){
                        obj.upload(index, file);
                    });

                    //作废
                    tr.find('.demo-delete').on('click', function(){
                        delete files[index]; //作废对应的文件
                        tr.remove();
                        uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免作废后出现同名文件不可选
                    });

                    demoListView.append(tr);
                });
            }
            ,done: function(res, index, upload){
                if(res.state == 'ok'){ //上传成功
                    var tr = demoListView.find('tr#upload-'+ index)
                        ,tds = tr.children();
                    var title = res.data.title.substring(res.data.title.lastIndexOf("/"),res.data.title.length);
                    if(title.length > 15){
                        title = "..."+title.substring(title.length-15,title.length);
                    }
                    tds.eq(0).html(title);
                    tds.eq(2).html('<span style="color: #5FB878;">上传成功</span>');
                    tds.eq(3).html(''); //清空操作
                    var valData = "";
                    var idData = "";
                    var fileUrl = $("#fileUrl").val();
                    var fileIds = $("#fileIds").val();
                    if (fileUrl) {
                        valData = fileUrl + "," + res.data.src;
                        idData = fileIds + "," +res.data.id;
                    } else {
                        valData = res.data.src;
                        idData = res.data.id;
                    }
                    $("#fileUrl").val(valData);//文件路径放入input框
                    $("#fileIds").val(idData);
                    return delete this.files[index]; //作废文件队列已经上传成功的文件
                }
                this.error(index, upload);
            }
            ,error: function(index, upload){
                var tr = demoListView.find('tr#upload-'+ index)
                    ,tds = tr.children();
                tds.eq(2).html('<span style="color: #FF5722;">上传失败</span>');
                tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
            }
        });



        //触发保存草稿
        form.on('submit(confirmBtn1)', function(){
            //处理修改时文件
            if($("#noticeId").val() != null && $("#noticeId").val() != ''){
                for(var i=0;i<fileArr.length;i++){
                    var valData = "";
                    var fileUrl = $("#fileUrl").val();
                    if (fileUrl) {
                        valData = fileUrl + "," + fileArr[i];
                    } else {
                        valData = fileArr[i];
                    }
                    $("#fileUrl").val(valData);
                }
                for(var i=0;i<fileIdArr.length;i++){
                    var idData = "";
                    var fileIds = $("#fileIds").val();
                    if (fileIds) {
                        idData = fileIds + "," + fileIdArr[i];
                    } else {
                        idData = fileIdArr[i];
                    }
                    $("#fileIds").val(idData);
                }
            }
            $("#releaseStatus").val('draft');//草稿
            var base = new Base64();
            $("#content").val(base.encode(layedit.getContent(index)));
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/notice/save',
                data: $("#noticeForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.layui.table.reload('noticeTable');
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        //触发保存发布
        form.on('submit(confirmBtn2)', function(){
            //处理修改时文件
            if($("#noticeId").val() != null && $("#noticeId").val() != ''){
                for(var i=0;i<fileArr.length;i++){
                    var valData = "";
                    var fileUrl = $("#fileUrl").val();
                    if (fileUrl) {
                        valData = fileUrl + "," + fileArr[i];
                    } else {
                        valData = fileArr[i];
                    }
                    $("#fileUrl").val(valData);
                }
                for(var i=0;i<fileIdArr.length;i++){
                    var idData = "";
                    var fileIds = $("#fileIds").val();
                    if (fileIds) {
                        idData = fileIds + "," + fileIdArr[i];
                    } else {
                        idData = fileIdArr[i];
                    }
                    $("#fileIds").val(idData);
                }
            }
            $("#releaseStatus").val('release');//发布
            var base = new Base64();
            $("#content").val(base.encode(layedit.getContent(index)));
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/notice/save',
                data: $("#noticeForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.layui.table.reload('noticeTable');
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        $(function(){
            loadFilesAndUsers();
	        //编辑器回显文本
            if($("#noticeId").val() != null && $("#noticeId").val() != ''){
                var base = new Base64();
                $("#textA").html(base.decode($("#showContent").val()));
                index = layedit.build('textA',{
                    height: 180,
                    tool: [
                        'strong' //加粗
                        ,'italic' //斜体
                        ,'underline' //下划线
                        ,'del' //作废线
                        ,'|' //分割线
                        ,'left' //左对齐
                        ,'center' //居中对齐
                        ,'right' //右对齐
                    ]
                });
            }
        });
    });
</script>
#end