#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅公告页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    /*.bcard{
        overflow-y:auto;
    }
    #fileShow{
        overflow-y:auto;
    }*/
</style>
#end

#define content()
<div class="layui-collapse" style="border: none;background-color: #F2F2F2;">
	<input type="hidden" id="orgIds" name="orgIds" value="#(orgIds??)">
   	<input type="hidden" id="roleIds" name="roleIds" value="#(roleIds??)">
   	<input type="hidden" id="groupIds" name="groupIds" value="#(groupIds??)">
   	<input type="hidden" id="userIds" name="userIds" value="#(userIds??)">
    <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
    <input type="hidden" id="noticeId" name="notice.id" value="#(notice.id??)"/>
    <input type="hidden" id="content" name="notice.noticeContent" value="#(notice.noticeContent??)"/>
    <div class="layui-row">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
            <div class="layui-row layui-col-space10">
                <div class="layui-col-xs5 layui-col-sm5 layui-col-md5 layui-col-lg5">
                    <div class="layui-form-item">
<!--                         <table class="layui-table"> -->
<!--                             <thead> -->
<!--                             <tr style="background-color: #fff;"> -->
<!--                                 <th>姓名</th> -->
<!--                                 <th>是否查阅</th> -->
<!--                                 <th>查阅时间</th> -->
<!--                             </tr> -->
<!--                             </thead> -->
<!--                             <tbody id="userTable"></tbody> -->
<!--                         </table> -->
						<div class="layui-form-item">
							<label class="layui-form-label">组织</label>
							<div class="layui-input-block">
								<div id="noticeOrgXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                        <div class="layui-form-item">
							<label class="layui-form-label">角色</label>
							<div class="layui-input-block">
								<div id="noticeRoleXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                        <div class="layui-form-item">
							<label class="layui-form-label">分组</label>
							<div class="layui-input-block">
								<div id="noticeGroupXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                        <div class="layui-form-item">
							<label class="layui-form-label">员工</label>
							<div class="layui-input-block">
								<div id="noticeUserXmSelect" class="xm-select-demo"></div>
							</div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs7 layui-col-sm7 layui-col-md7 layui-col-lg7">
                    <div class="layui-card">
                        <div class="layui-card-header" style="height:30% !important;">
                            <div style="text-align: center;"><h2>#(notice.noticeTitle??)</h2></div>
                            <div style="text-align: center;">#date(notice.releaseTime??,'yyyy-MM-dd HH:mm:ss')</div>
                        </div>
                    </div>
                    <hr>
                    <div class="layui-card bcard">
                        <div class="layui-card-body" style="min-height:250px;" id="showContent"></div>
                    </div>
                    <div class="layui-card">
                        <div class="layui-card-header">附件</div>
                        <div class="layui-card-body" id="fileShow" style="min-height:100px;display: flex;flex-wrap:wrap;"></div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript" src="#(ctxPath)/static/js/xm-select.js"></script>
<script type="text/javascript">
    layui.use(['form','laytpl','table','element','layer'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;

    	var noticeOrgXmSelect = xmSelect.render({
    		el:'#noticeOrgXmSelect'
   			, model:{label:{type:'text'}}
    		, tips:'请选择组织'
   			, tree:{
       			show:true
       			, strict:false
       			, expandedKeys:true
       		}
    		, disabled:true
    		, autoRow:true
    		, on:function(data){
    			
    		}
    	});
    	$.post('#(ctxPath)/persOrg/persOrgXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var selectIds = $('#orgIds').val();
				if(selectIds.indexOf(",")!=-1){
					var initValueArray = selectIds.split(',');
					noticeOrgXmSelect.update({
	    				data:rep.persOrgSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
					if(selectIds!=null && selectIds!=''){
						noticeOrgXmSelect.update({
		    				data:rep.persOrgSelectTree
		    				, initValue:[selectIds]
		    			});
					}
				}
    		}
    	});
    	
    	var noticeRoleXmSelect = xmSelect.render({
    		el:'#noticeRoleXmSelect'
   			, model:{label:{type:'text'}}
    		, tips:'请选择角色'
   			, disabled:true
    		, on:function(data){
    			
    		}
    	});
    	$.post('#(ctxPath)/pers/role/mainRoleXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var selectIds = $('#roleIds').val();
				if(selectIds.indexOf(",")!=-1){
					var initValueArray = selectIds.split(',');
					noticeRoleXmSelect.update({
	    				data:rep.mainRoleSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
					if(selectIds!=null && selectIds!=''){
		    			noticeRoleXmSelect.update({
		    				data:rep.mainRoleSelectTree
		    				, initValue:[selectIds]
		    			});
					}
				}
    		}
    	});
    	
    	var noticeGroupXmSelect = xmSelect.render({
    		el:'#noticeGroupXmSelect'
   			, model:{label:{type:'text'}}
    		, tips:'请选择分组'
    		, disabled:true
    		, on:function(data){
    			
    		}
    	});
    	$.post('#(ctxPath)/group/groupXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var selectIds = $('#groupIds').val();
				if(selectIds.indexOf(",")!=-1){
					var initValueArray = selectIds.split(',');
					noticeGroupXmSelect.update({
	    				data:rep.groupSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
					if(selectIds!=null && selectIds!=''){
		    			noticeGroupXmSelect.update({
		    				data:rep.groupSelectTree
		    				, initValue:[selectIds]
		    			});
					}
				}
    		}
    	});
    	
    	var noticeUserXmSelect = xmSelect.render({
    		el:'#noticeUserXmSelect'
   			, model:{label:{type:'text'}}
    		, tips:'请选择用户'
   			, disabled:true
    		, on:function(data){
    			
    		}
    	});
    	$.post('#(ctxPath)/user/userXmSelectTree', {}, function(rep) {
    		if(rep.state=='ok'){
    			var selectIds = $('#userIds').val();
				if(selectIds.indexOf(",")!=-1){
					var initValueArray = selectIds.split(',');
					noticeUserXmSelect.update({
	    				data:rep.userSelectTree
	    				, initValue:initValueArray
	    			});
				}else{
					if(selectIds!=null && selectIds!=''){
		    			noticeUserXmSelect.update({
		    				data:rep.userSelectTree
		    				, initValue:[selectIds]
		    			});
					}
				}
    		}
    	});
        
        downFile=function(filePath) {
            var url = $("#commonUpload").val() + "/downFileByUrl?src=" + filePath;
            var eleLink = document.createElement('a');
            eleLink.style.display = 'none';
            eleLink.href = url;
            // 触发点击
            document.body.appendChild(eleLink);
            eleLink.click();
            // 然后移除
            document.body.removeChild(eleLink);
        }


        //展示文件模板
        showFileTpl = function (filePath, type, url) {
            var divStr =
                '<div style="width: 150px;height: 100px;margin: 5px;" onclick=\'downFile("' + filePath + '")\'>'
                + '<div style="text-align: center;"><i class="' + type + '"></i></div>'
                + '<div><span>' + url + '</span></div>'
                + '</div>'
            return divStr;
        }

        //展示查阅人模板
//         showUserTpl = function (name, isReadName, isReadTime, index) {
//             var trStr = '<tr id="itemDetail-' + index + '">'
//                 + '<td>' + name + '</td>'
//                 + '<td>' + isReadName + '</td>'
//                 + '<td>' + isReadTime + '</td>'
//                 + '</tr>'
//             return trStr;
//         }

        //获取文件表格和查阅人数据
        function loadFilesAndUsers() {
            if ($('#noticeId').val() != null && $('#noticeId').val() != '') {
                util.sendAjax({
                    url: '#(ctxPath)/pers/notice/getFilesAndUsers',
                    type: 'post',
                    data: {'id': $('#noticeId').val()},
                    notice: false,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            //处理文件
                            var fileList = returnData.fileList;
                            var userList = returnData.userList;
                            $("#paramCount").val(userList.length);
                            for (var i = 0; i < fileList.length; i++) {
                                var suffix = fileList[i].url.substring(fileList[i].url.indexOf("."), fileList[i].url.length);
                                var type = '';
                                var regImg = /(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/;//图片
                                var regExcel = /(.*)\.(xlsx|xls|xltx|xlt|xlsm|xlsb|xltm|csv)$/;//excel
                                var regWord = /(.*)\.(docx|doc)$/;//word
                                var regPdf = /(.*)\.(pdf|PDF)$/;//pdf
                                var regVideo = /(.*)\.(mp4|rmvb|flv|mpeg|avi)$/;//视频
                                var regZip = /(.*)\.(zip|rar)$/;//压缩包
                                if (regImg.test(suffix)) {
                                    type = 'fa fa-file-image-o fa-lg';
                                } else if (regExcel.test(suffix)) {
                                    type = 'fa fa-file-excel-o fa-lg';
                                } else if (regWord.test(suffix)) {
                                    type = 'fa fa-file-word-o fa-lg';
                                } else if (regPdf.test(suffix)) {
                                    type = 'fa fa-file-pdf-o fa-lg';
                                } else if (regVideo.test(suffix)) {
                                    type = 'fa fa-file-video-o fa-lg';
                                } else if (regZip.test(suffix)) {
                                    type = 'fa fa-file-archive-o fa-lg';
                                } else {
                                    type = 'fa fa-sticky-note-o fa-lg';
                                }
                                var title = fileList[i].url.substring(fileList[i].url.lastIndexOf("/") + 1,fileList[i].url.length);
                                if(title != null && title.length > 15){
                                    title = "..."+title.substring(title.length-15,title.length);
                                }
                                $("#fileShow").append(showFileTpl(fileList[i].url, type, title));
                            }
//                             for (var i = 0; i < userList.length; i++) {
//                                 var isRead = userList[i].isRead;
//                                 var isReadName = '- -';
//                                 if (isRead == '0') {
//                                     isReadName = "未读";
//                                 } else if (isRead == '1') {
//                                     isReadName = "已读";
//                                 }
//                                 //处理查阅时间
//                                 var isReadTime = userList[i].isReadTime;
//                                 if(isReadTime == null || isReadTime == ''){
//                                     isReadTime = '- -';
//                                 }
//                                 $("#userTable").append(showUserTpl(userList[i].name,
//                                     isReadName, isReadTime, i + 1));
//                             }
                        }
                    }
                });
            }
        }

        $(function () {
            loadFilesAndUsers();
            var base = new Base64();
            $("#showContent").html(base.decode($("#content").val()));
        });
});
</script>
#end