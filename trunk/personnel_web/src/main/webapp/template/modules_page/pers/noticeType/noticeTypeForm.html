#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告类型展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/noticeType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#noticeTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.noticeTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form" style="margin-top: 20px;" id="noticeTypeForm">
    <input type="hidden" name="noticeType.id" value="#(noticeType.id??)"/>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label">公告类型</label>
        <div class="layui-input-inline">
            <input type="text" name="noticeType.typeName" class="layui-input" lay-verify="required" value="#(noticeType.typeName??)" placeholder="请输入公告类型">
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
        <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
