#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构员工编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()

<div class="layui-col-xs12 layui-col-sm12 layui-col-md12 layui-col-lg12">

    <div class="layui-row">
        <div style="margin-bottom:23px;"></div>
        <form class="layui-form layui-form-pane" action="" id="employeeForm" style="margin-left:35px;">
            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color: red;">*</span>附件标题</label>
                <div class="layui-input-block">
                    <input type="text" id="fileTitle" name="fileTitle" lay-verify="required" class="layui-input"   >
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" id="remark" placeholder="请输入内容" class="layui-textarea"></textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">文件</label>
                <div class="layui-input-block" >
                    <div class="layui-upload">
                        <button type="button" class="layui-btn" id="uploadBtn">选择附件</button>
                        <div class="layui-upload-list">
                            <p id="demoText"></p>
                        </div>
                    </div>
                </div>
            </div>

            <div style="margin-bottom:60px;"></div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="employeeId" id="employeeId" value="#(employeeId??)">
                    <button class="layui-btn" id="saveBtn" type="button" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </form>
    </div>
</div>

#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use([ 'form', 'laydate','upload' ], function() {
        var form = layui.form
            , layer = layui.layer
            , laydate = layui.laydate
            ,upload=layui.upload
            , $ = layui.$
        ;

        var uploadInst = upload.render({
            elem: '#uploadBtn'
            ,url: '#(ctxPath)/persOrgEmployee/saveEnclosure'
            ,auto:false
            ,accept:'file'
            ,bindAction:'#saveBtn'
            ,before: function(obj){
                layer.load();
                this.data={'fileTitle':$("#fileTitle").val(),'remark':$("#remark").val(),'employeeId':$("#employeeId").val()}
            }
            ,done: function(res){
                //上传失败
                if(res.state=='ok'){
                    parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                    parent.pageTableReload();
                    pop_close();
                }else{
                    layer.closeAll('loading');
                    layer.msg('上传失败', {icon: 2, offset: 'auto'})
                }
                //上传成功，返回的路径：res.filePath

            }
            ,error: function(){
                layer.closeAll('loading');
                //上传失败
                layer.msg('上传失败', {icon: 2, offset: 'auto'})
            }
        });


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/persOrgEmployee/saveChangeDepartment',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){

                        var id=$("#id").val();
                        if(id==='' || id===null){
                            parent.pageTableReload();
                            $("#resetBtn").click();
                        }else{
                            parent.pageTableReload();
                            pop_close();
                        }
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end