#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;


        /*var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#orgId").val(treeNode.id);
                    $("#orgName").val(treeNode.name);
                }
            }
        };

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);*/

        //自定义验证规则
        form.verify({
            otherReq: function(value,item){
                var $ = layui.$;
                var verifyName=$(item).attr('name')
                    , verifyType=$(item).attr('type')
                    ,formElem=$(item).parents('.layui-form')//获取当前所在的form元素，如果存在的话
                    ,verifyElem=formElem.find('input[name='+verifyName+']')//获取需要校验的元素
                    ,isTrue= verifyElem.is(':checked')//是否命中校验
                    ,focusElem = verifyElem.next().find('i.layui-icon');//焦点元素
                if(!isTrue || !value){
                    //定位焦点
                    focusElem.css(verifyType=='radio'?{"color":"#FF5722"}:{"border-color":"#FF5722"});
                    //对非输入框设置焦点
                    focusElem.first().attr("tabIndex","").css("outline","").blur(function() {
                        focusElem.css(verifyType=='radio'?{"color":""}:{"border-color":""});
                    }).focus();
                    return '必填项不能为空';
                }
            }
        });




        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/branchCompanyRole/saveConfig";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#positionForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //parent.table.reload('positionTable',{'where':{'orgId':$("#orgId").val()}});
                        pop_close();
                        parent.positionTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()

<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left:5px;" id="positionForm">
    <div class="layui-form-item" style="margin-bottom: 65px;">
        <table class="layui-table">
            <colgroup>
                <col width="20%">
                <col width="40%">
                <col width="40%">
            </colgroup>
            <thead>
            <tr>
                <th style="text-align: center;">名称</th>
                <th style="text-align: center;"><font color="red"></font>计算公式</th>
                <th style="text-align: center;">备注</th>
            </tr>
            </thead>
            <input id="count" name="count" value="#(recordList.size()??0)" type="hidden">
            <input id="roleId" name="roleId" value="#(roleId??)" type="hidden">
            <tbody id="deptBody">
            #for(record:recordList)
            <tr id="tr-#(for.index+1)">
                <td align="center">
                    <div class="">
                        <input type="text" readonly name="record[#(for.index+1)].name" class="layui-input" lay-verify="required" value="#(record.name??)" autocomplete="off">
                    </div>
                </td>

                <td align="center">
                    <div class="">
                        <input name="record[#(for.index+1)].fieldName" value="#(record.field??)" type="hidden">
                        <input type="text"  name="record[#(for.index+1)].calculateConfig" class="layui-input" lay-verify="" value="#(record.value??)" autocomplete="off">
                    </div>
                </td>
                <td align="center">
                    <div class="">
                        <input type="text" name="record[#(for.index+1)].remark" class="layui-input" lay-verify="" value="#(record.remark??)" autocomplete="off">
                    </div>
                </td>

            </tr>
            #end
            </tbody>
        </table>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
