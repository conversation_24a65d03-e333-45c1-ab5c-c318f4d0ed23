#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告类型展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/employeeCertificate/saveType";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#noticeTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.noticeTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left: 30px;" id="noticeTypeForm">

    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>证件类型名称</label>
        <div class="layui-input-inline">
            <input type="text" name="name" class="layui-input" lay-verify="required" value="#(model.name??)" autocomplete="off" placeholder="请输入证件类型名称" >
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>有无有效日期</label>
        <div class="layui-input-inline">
            <input type="radio" name="isEffectiveDate" lay-filter="type_tadio" value="1" title="有"   #if(model==null || model.isEffectiveDate??=='1') checked #end>
            <input type="radio" name="isEffectiveDate" lay-filter="type_tadio" value="0" title="无"   #if(model.isEffectiveDate??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>入职是否必要</label>
        <div class="layui-input-inline">
            <input type="radio" name="isEntryRequired" lay-filter="type_tadio" value="1" title="是"   #if(model==null || model.isEntryRequired??=='1') checked #end>
            <input type="radio" name="isEntryRequired" lay-filter="type_tadio" value="0" title="否"   #if(model.isEntryRequired??=='0') checked #end>
        </div>
    </div>
    <div  class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>证件附件数量</label>
        <div class="layui-input-inline">
            <input type="text" name="fileCount" class="layui-input" lay-verify="required|number" value="#(model.fileCount??)" autocomplete="off" placeholder="请输入证件附件数量">
        </div>
    </div>

    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>是否可用</label>
        <div class="layui-input-inline">
            <input type="radio" name="isEnable" lay-filter="type_tadio" value="1" title="是"   #if(model==null || model.isEnable??=='1') checked #end>
            <input type="radio" name="isEnable" lay-filter="type_tadio" value="0" title="否"   #if(model.isEnable??=='0') checked #end>
        </div>
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id" value="#(model.id??)"/>
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
