#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告类型展示#end

#define css()
<style>

    #steptsTable td,#steptsTable th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end


#define js()


<script type="text/javascript">
    layui.extend({
        treeSelect: '/static/js/extend/treeSelect'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['form','jquery','laydate','laytpl','treeSelect','upload','layer'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate,layer=layui.layer,laytpl=layui.laytpl,treeSelect=layui.treeSelect,upload=layui.upload;

        //保存员工入职记录流程
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }


        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });


        $(function () {
            $("#big_img").click(function(){
                console.log(1);
                $("#big_img").hide();
                return false;
            });
            $("#small_img").click(function(){
                console.log(2);
                $("#big_img").show();
                return false;
            });
        })

        renderImg=function(src) {
            var json = {
                "title": "图片", //相册标题
                "id": 123, //相册id
                "start": 0, //初始显示的图片序号，默认0
                "data": [   //相册包含的图片，数组格式
                    {
                        "alt": "图片",
                        "src": src, //原图地址
                        "thumb": "" //缩略图地址
                    }
                ]
            };
            layui.layer.photos({
                photos: json
                , anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
                , tab: function () {
                    num = 0;
                    $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                        '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>' +
                        /*'<button id="fangda" class="layui-btn layui-btn-normal layui-btn-radius"  >放大\t</button>\n' +*/
                        '\t</div>');
                    $(document).on("click", "#xuanzhuan", function (e) {
                        num = (num + 90) % 360;
                        $(".layui-layer.layui-layer-page.layui-layer-photos").css('background', 'black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                        $("#layui-layer-photos").css('transform', 'rotate(' + num + 'deg)');

                    });
                    /*$(document).on("click", "#fangda", function (e) {
                        $('#layui-layer4').onscroll();

                    });

                    $('#layui-layer4').mousewhell(function(event,data){
                        console.log(1);
                    })*/
                    $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                        var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                            (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                        var imagep = $(".layui-layer-phimg").parent().parent();
                        var image = $(".layui-layer-phimg").parent();
                        var h = image.height();
                        var w = image.width();
                        if (delta > 0) {
                            if (h < (window.innerHeight)) {
                                h = h * 1.05;
                                w = w * 1.05;
                            }
                        } else if (delta < 0) {
                            if (h > 100) {
                                h = h * 0.95;
                                w = w * 0.95;
                            }
                        }
                        imagep.css("top", (window.innerHeight - h) / 2);
                        imagep.css("left", (window.innerWidth - w) / 2);
                        image.height(h);
                        image.width(w);
                        imagep.height(h);
                        imagep.width(w);
                    });
                }
            });
        }

        aClick=function (url) {
            window.location.href=url;
            return false;
        }

    });
</script>
#end

#define content()

<div class="layui-row">
    <div class="layui-col-md6 layui-form" id="content" style="padding-right: 10px;">
        <form class="layui-form layui-form-pane" action="" id="formId" style="padding-top: 20px;padding-left: 20px;">

            <div class="layui-form-item">
                <label class="layui-form-label" style="">姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(employee.fullName??)(#(employee.workNum??))" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">审批提交时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(fillCard.createDate??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">所在部门</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(deptNames??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">打卡类型</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(checkinRecord.checkinType??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">标准打卡时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(checkinRecord.schCheckinTime??,'yyyy-MM-dd HH:mm')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">原打卡时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  #if('未打卡'==fillCard.exceptions??) value="" #else value="#date(fillCard.oldCheckinTime??,'yyyy-MM-dd HH:mm')" #end autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">异常状态</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  #if(checkinRecord!=null && '未打卡'!=fillCard.exceptions??) value="#(fillCard.exceptions??)" #else value="#(fillCard.exceptions??)" #end autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">补卡时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly value="#date(fillCard.fillCardTime??,'yyyy-MM-dd HH:mm')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">补卡事由</label>
                <div class="layui-input-block">
                    <textarea name="desc" placeholder="请输入内容" readonly class="layui-textarea">#(fillCard.fillCardRemark??)</textarea>
                </div>
            </div>

            #if(fileType!=null)
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">凭证文件</label>
                <div class="layui-input-block"  style="margin-top: 3px;">
                    #if(fileType??=='png' || fileType??=='jpeg' || fileType??=='jpg')
                    <img id="fillFile" src="#(fillCard.fileUrl??)" width="100%" height="100%" onclick="renderImg('#(fillCard.fileUrl??)')" />
                    #elseif(fileType??=='mp4' || fileType??=='avi' || fileType??=='wmv' || fileType??=='mov' || fileType??=='mpg' || fileType??=='mpeg' || fileType??=='ram'
                    || fileType??=='rm' || fileType??=='swf' || fileType??=='flv')
                    <video src="#(fillCard.fileUrl??)" controls="controls" width="100%" height="100%" >
                        您的浏览器不支持 video 标签。
                    </video>
                    #else
                    <div style="margin-top: 15px;margin-left: 5px;text-align: center;font-size: 16px;">
                        <a href="javascript:void(0)" onclick="aClick('#(fillCard.fileUrl??)')" style="text-decoration:underline;">点击下载#(fileName??)</a>
                    </div>
                    #end
                </div>
            </div>
            #end

        </form>
    </div>
    <div class="layui-col-md6" style="padding-top: 10px;">
        #if(stepts.size()??0>0)
        <table class="layui-table" id="steptsTable">
            <!--<colgroup>
                <col width="10%">
                <col width="13%">
                <col width="30%">
                <col width="30%">
                <col width="17%">
            </colgroup>-->
            <thead>
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">节点</th>
                <th style="text-align: center;">流程</th>
                <th style="text-align: center;">意见</th>
                <th style="text-align: center;">操作时间</th>
                <th style="text-align: center;">操作人</th>
            </tr>
            </thead>
            <tbody>
            #for(stept:stepts)
            <tr>
                <td align="center">#(for.index+1)</td>
                <td>#(stept.ActivityName)</td>
                <td align="center">
                    #if(stept.StepState==3)
                    提交
                    #else if(stept.StepState==1)
                    待处理
                    #else if(stept.StepState==0)
                    等待
                    #else if(stept.StepState==2)
                    正处理
                    #else if(stept.StepState==4)
                    撤回
                    #else if(stept.StepState==5)
                    批准
                    #else if(stept.StepState==6)
                    拒绝
                    #else if(stept.StepState==7)
                    转移
                    #else if(stept.StepState==8)
                    失败
                    #else if(stept.StepState==9)
                    跳过
                    #end
                </td>
                <td>#(stept.Comment)</td>
                <td>#(stept.CommentTime)</td>
                <td align="center">#(stept.CommentUserName)</td>
            </tr>
            #end
            </tbody>
        </table>

        <form class="layui-form layui-form-pane" id="taskForm">
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">意见</label>
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit  ) disabled #end></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block pull-right">
                    <input id="taskId" type="hidden" value="#(persTask.taskId??)">
                    <input id="taskNo" type="hidden" value="#(persTask.taskNo??)">
                    <input id="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                    <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                    <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>
                    <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject  ) disabled  style="display: none;" #end>拒绝</button>
                    <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove  ) disabled style="display: none;" #end>通过</button>
                </div>
            </div>
        </form>
        #end
    </div>
</div>


#end
