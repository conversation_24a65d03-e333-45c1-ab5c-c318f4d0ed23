#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构员工查看页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
<style>

</style>
#end

#define content()
<div class="layui-col-xs12">

	<div class="layui-row">
		<div style="margin-bottom:23px;"></div>
		<form class="layui-form layui-form-pane" action="" id="employeeForm" style="margin-left:35px;margin-right:35px;">
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>姓名</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="fullName" name="fullName" class="layui-input" value="#(model.fullName??)" readonly placeholder="请输入姓名">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">工龄</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="workAge" class="layui-input" readonly value="" placeholder="">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">年龄</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="age" class="layui-input" readonly value="" placeholder="">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>民族</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="nationality" disabled>
							<option value="">请选择民族</option>
							#dictOption("nation", model.nationality??'', "")
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">籍贯</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="nativePlace" class="layui-input" readonly value="#(model.nativePlace??)" placeholder="">
					</div>
				</div>
				<!--<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>年休假</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="annualLeaveDays" class="layui-input" readonly value="#(model.annualLeaveDays??0)" placeholder="">
					</div>
				</div>-->
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>证件类型</label>
				<div class="layui-input-block">
					<select name="idCardType" id="idCardType" disabled>
						#dictOption("id_card_type", model.idCardType??'', "")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>证件号码</label>
				<div class="layui-input-block">
					<input type="text" name="idCard" id="idCard" readonly class="layui-input" value="#(model.idCard??)" maxlength="19" placeholder="请输入证件号码">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>身份证地址</label>
				<div class="layui-input-block">
					<input type="text" name="idCardAddr"  id="idCardAddr" readonly class="layui-input" value="#(model.idCardAddr??)" placeholder="请输入身份证地址">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>常住地址</label>
				<div class="layui-input-block">
					<input type="text" name="residentAddr" readonly class="layui-input" value="#(model.residentAddr??)" placeholder="请输入常住地址">
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">性别</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="sex" id="sex" disabled>
							<option value="">请选择性别</option>
							#dictOption("sex", model.sex??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>出生日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="birthday" name="birthday" class="layui-input" readonly value="#(model.birthday??)" autocomplete="off">
					</div>
				</div>
			</div>
			<fieldset class="layui-elem-field" style="margin-top: 30px;margin-bottom: 30px;padding-left: 5px;padding-right: 5px;">
				<legend>组织机构</legend>
				<table class="layui-table">
					<colgroup>
                        <col width="40%">
                        <col width="30%">
                        <col width="30%">
					</colgroup>
					<thead>
					<tr>
						<th style="text-align: center;">部门</th>
						<th style="text-align: center;">职位</th>
						<th style="text-align: center;">角色</th>
					</tr>
					</thead>
					<input id="count" name="count" value="#(recordList.size()??0)" type="hidden">
					<tbody id="deptBody">
						#for(record:recordList)
						<tr id="tr-#(for.index+1)">
							<td align="center">
								<div class="layui-input-inline" style="width: 100%;" >
									<input  name="relDeptId-#(for.index+1)" value="#(record.relDeptId??)" type="hidden" >
									<input  id="deptIdValue-#(for.index+1)" value="#(record.deptId??)" type="hidden">
									<input  name="deptId-#(for.index+1)" lay-filter="deptId-#(for.index+1)" data-index="#(for.index+1)" id="deptId-#(for.index+1)" class="layui-input" readonly>
								</div>
							</td>

							<td align="center">
								<div class="layui-input-inline" style="width: 100%;" >
									<input  name="relPositionId-#(for.index+1)" value="#(record.relPositionId??)" type="hidden">
									<select name="positionId-#(for.index+1)" id="positionId-#(for.index+1)" data-index="#(for.index+1)" lay-filter="positionSelect" disabled>
										<option value="" >请选择职位</option>
										#for(position : record.persPositionList)
										<option value="#(position.id)" role-id="#(position.roleId??)" role-name="#(position.roleName)" #if(record.positionId??==position.id) selected #end >#(position.positionName)</option>
										#end
									</select>
								</div>
							</td>
							<td align="center">
								<input id="relRoleId-#(for.index+1)"  name="relRoleId-#(for.index+1)" value="#(record.relRoleId??)" type="hidden">
								<input id="roleId-#(for.index+1)"  name="roleId-#(for.index+1)" value="#(record.roleId??)" type="hidden">
								<span id="roleName-#(for.index+1)">#(record.mainRole.roleName??)</span>
							</td>
						</tr>
						#end
					</tbody>
				</table>
			</fieldset>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>员工类型</label>
					<div class="layui-input-inline" style="width:155px;">
						<select id="employeeType" name="employeeType" disabled>
							<option value="">请选择员工类型</option>
							#dictOption("employee_type", model.employeeType??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>入职日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="entryTime" name="entryTime" class="layui-input" readonly value="#date(model.entryTime??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>手机号码</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="phoneNum" name="phoneNum" class="layui-input" readonly value="#(model.phoneNum??)" placeholder="请输入手机号码">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">分机号码</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="extNum" name="extNum" class="layui-input" readonly value="#(model.extNum??)" placeholder="请输入分机号码">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>最高学历</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="education" disabled>
							<option value="">请选择文化程度</option>
							#dictOption("education", model.education??'', "")
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">

				<label class="layui-form-label">常用邮箱</label>
				<div class="layui-input-block" >
					<input type="text" id="emailAddr" name="emailAddr" readonly class="layui-input" autocomplete="off" value="#(model.emailAddr??)" placeholder="请输入常用邮箱">
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>是否住宿</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="isStaySleep" name="isStaySleep" value="0" title="否" #if(model==null || model.isStaySleep??=='0') checked #end readonly>
						<input type="radio" lay-filter="isStaySleep" name="isStaySleep" value="1" title="是" #if(model.isStaySleep??=='1') checked #end readonly>
					</div>
				</div>
				<div class="layui-inline" id="roomNumDiv" #if(model.isStaySleep??=='1') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label"><font color="red">*</font>房间号码</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="roomNum" name="roomNum" readonly class="layui-input" value="#(model.roomNum??)" placeholder="请输入住宿房间号码">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>转正日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="formalDate" name="formalDate" class="layui-input" readonly value="#date(model.formalDate??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red"></font>社保缴纳状态</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="socialStatus" disabled>
							<option value=""></option>
							<option value="1" #if(model.socialStatus??=='1') selected #end>已缴</option>
							<option value="2" #if(model.socialStatus??=='2') selected #end>未缴</option>
							<option value="3" #if(model.socialStatus??=='3') selected #end>放弃</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;font-size: 13px;"><font color="red"></font>社保缴纳地址(旧)</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="socialAddr" name="socialAddr" class="layui-input" readonly value="#(model.socialAddr??)" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;font-size: 13px;"><font color="red"></font>社保缴纳地址(新)</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="socialAddrId" disabled>
							<option value="">请选择社保缴纳地址</option>
							#for(addr : socialAddresses)
							<option value="#(addr.id??)" #if(addr.id==model.socialAddrId??) selected #end>#(addr.name??)</option>
							#end
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red"></font>意外险缴纳状态</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="accidentInsurance" disabled>
							<option value=""></option>
							<option value="1" #if(model.accidentInsurance??=='1') selected #end>已缴</option>
							<option value="2" #if(model.accidentInsurance??=='2') selected #end>未缴</option>
							<option value="3" #if(model.accidentInsurance??=='3') selected #end>放弃</option>
						</select>
					</div>
				</div>
			</div>
			#if(userType!='system_admin')
			#shiroHasPermission("emp:archives:salary")
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>试用薪资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="probationSalary" name="probationSalary" maxlength="11" class="layui-input" readonly value="#(model.probationSalary??)" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>转正薪资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="salary" name="salary" class="layui-input" maxlength="11" readonly value="#(model.salary??)" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>现有薪资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="nowSalary" name="salary" class="layui-input" maxlength="11" readonly value="#(model.nowSalary??)" autocomplete="off">
					</div>
				</div>
			</div>
			#end
			#end
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>状态</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="archiveStatus" id="archiveStatus" disabled lay-filter="archiveStatus" lay-verify="required">
							#dictOption("archive_status", model.archiveStatus??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline" id="quitTimeDiv" #if(model.archiveStatus??=='quit') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label"><font color="red">*</font>离职日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="quitTime" name="quitTime" class="layui-input" readonly value="#date(model.quitTime??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline" id="quitUseAnnualLeaveDaysDiv" #if(model.archiveStatus??=='quit') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>休年休假数</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="" name="" class="layui-input" readonly value="#(model.quitUseAnnualLeaveDays??)" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 3px;"><font color="red">*</font>是否再次入职</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="isReemployment" name="isReemployment" value="0" title="否" #if(model==null || model.isReemployment??=='0') checked #end readonly>
						<input type="radio" lay-filter="isReemployment" name="isReemployment" value="1" title="是" #if(model.isReemployment??=='1') checked #end readonly>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>毕业院校</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="graduateSchool" onblur="onblurNull(this,'请输入毕业院校')" readonly class="layui-input" value="#(model.graduateSchool??)" placeholder="请输入毕业院校">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>专业</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="schoolMajor" readonly class="layui-input" value="#(model.schoolMajor??)" placeholder="请输入专业">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>职称/证书</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="certificate" name="certificate" value="0" title="无" #if(model==null || model.certificate??=='0') checked #end readonly>
						<input type="radio" lay-filter="certificate" name="certificate" value="1" title="有" #if(model.certificate??=='1') checked #end readonly>
					</div>
				</div>
				<div class="layui-inline" id="certificateNameDiv" #if(model.certificate??=='1') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>职称/证书名称</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="certificateName" name="certificateName" readonly class="layui-input" value="#(model.certificateName??)" placeholder="请输入职称/证书名称">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">爱好特长</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="hobby" class="layui-input" readonly value="#(model.hobby??)" placeholder="请输入爱好特长">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>紧急联系人</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="linkPeople" class="layui-input" readonly value="#(model.linkPeople??)" placeholder="请输入紧急联系人">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>紧急联系人手机</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="linkPhone" name="linkPhone" class="layui-input" readonly value="#(model.linkPhone??)" placeholder="请输入紧急联系人手机">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>入职渠道</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="entryChannel" disabled>
							<option value="">请选择入职渠道</option>
							#dictOption("entry_channel", model.entryChannel??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">是否残疾</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" name="isDisability" value="0" title="否" checked readonly>
						<input type="radio" name="isDisability" value="1" title="是" readonly>
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<textarea name="remark" placeholder="请输入内容" maxlength="255" class="layui-textarea" readonly>#(model.remark??)</textarea>
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button class="layui-btn layui-btn-primary" type="reset" id="resetBtn" style="display: none;">重置</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script src="#(ctxPath)/static/js/emailAutoComplete.js"></script>
<script type="text/javascript">
layui.extend({
	treeSelect: '/static/js/extend/treeSelect'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use([ 'form', 'laydate','treeSelect','laytpl' ], function() {
	var form = layui.form
	, layer = layui.layer
	, laydate = layui.laydate
	, $ = layui.$
	,treeSelect=layui.treeSelect
	,laytpl=layui.laytpl
	;

	#for(record : recordList)
		loadTreeSelect("deptId-#(for.index+1)","#(record.deptId)");
	#end

	 function loadTreeSelect(id,checkId){
		treeSelect.render({
			// 选择器
			elem: "#"+id,
			// 数据
			data: '#(ctxPath)/persOrg/orgTreeSelect',
			// 异步加载方式：get/post，默认get
			type: 'get',
			// 占位符
			placeholder: '请选择部门',
			// 是否开启搜索功能：true/false，默认false
			search: true,
			// 点击回调
			click: function(d){
				var index=$("#"+id).attr("data-index");
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/pers/position/getPositionByOrgId',
					data: {'orgId':d.current.id},
					notice: false,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							var str='<option value="">请选择职位</option>';
							var data=rep.data;
							$.each(data,function (index,item) {
								str+='<option value="'+item.id+'" role-id="'+item.roleId+'" role-name="'+item.roleName+'">'+item.positionName+'</option>';
							});
							$("#positionId-"+index).html(str);
							form.render('select');
						}
					},
					complete : function() {
					}
				});
			},
			// 加载完成后的回调函数
			success: function (d) {
				if(checkId!=''){
					treeSelect.checkNode(id, checkId);
				}
			}
		});
	}

	form.on('select(positionSelect)', function(data){
		var index=data.elem.getAttribute("data-index");
		var roleId=$("#positionId-"+index).find("option[value='"+data.value+"']").attr("role-id");
		var roleName=$("#positionId-"+index).find("option[value='"+data.value+"']").attr("role-name");
		if(roleId==undefined){
			$("#roleId-"+index).val('');
		}else{
			$("#roleId-"+index).val(roleId);
		}

		if(roleName==undefined){
			$("#roleName-"+index).html('');
		}else{
			$("#roleName-"+index).html(roleName);
		}
	});

	//出生日期渲染
	laydate.render({
		elem: '#birthday'
		, trigger: 'click'
		,done:function (value,date) {
			setAge(value);
		}
	});

	laydate.render({
		elem:'#entryTime',
		trigger:'click',
		done:function (value,date) {
			setWorkAge(value);
		}
	});
	laydate.render({
		elem:'#formalDate',
		trigger:'click',
		done: function(value, date){
			var entryTime=new Date($("#entryTime").val());
			var formalDate=new Date(value);
			if(entryTime>formalDate){
				layer.msg('转正日期不能小于入职日期', {icon: 2, offset: 'auto'});
				$("#formalDate").attr("style","border-color: #FF5722!important;");
			}else{
				$("#formalDate").attr("style","border-color: #C9C9C9!important;");
			}
		}
	});
	laydate.render({
		elem:'#quitTime',
		trigger:'click',
		done:function (value, date) {
			/*layer.confirm('选择离职日期代表该员工已离职，只能从离职类别查找，确认要选择吗？', {
				btn: ['确定','取消'] //按钮
			}, function(index){
				layer.close(index);
			}, function(){
				$("#quitTime").val('');
			});*/
		}
	});
	laydate.render({
		elem:'#socialTime',
		trigger:'click'
	});

	#if(model!=null)
		setWorkAge("#date(model.entryTime??,'yyyy-MM-dd')");
		setAge("#(model.birthday??)");
	#end

	function setWorkAge(entryTime) {
		var startDate=new Date(entryTime);
		var endDate=new Date();

		var diffDate=new Date(endDate-startDate);
		$("#workAge").val(diffDate.getFullYear()-1970+"年"+diffDate.getMonth()+"个月"+(diffDate.getDate()-1)+"天");
	}


	function setAge(birthday) {
		var returnAge,
				strBirthdayArr=birthday.split("-"),
				birthYear = strBirthdayArr[0],
				birthMonth = strBirthdayArr[1],
				birthDay = strBirthdayArr[2],
				d = new Date(),
				nowYear = d.getFullYear(),
				nowMonth = d.getMonth() + 1,
				nowDay = d.getDate();
		if(nowYear == birthYear){
			returnAge = 0;//同年 则为0周岁
		}else{
			var ageDiff = nowYear - birthYear ; //年之差
			if(ageDiff > 0){
				if(nowMonth == birthMonth) {
					var dayDiff = nowDay - birthDay;//日之差
					if(dayDiff < 0) {
						returnAge = ageDiff - 1;
					}else {
						returnAge = ageDiff;
					}
				}else {
					var monthDiff = nowMonth - birthMonth;//月之差
					if(monthDiff < 0) {
						returnAge = ageDiff - 1;
					}
					else {
						returnAge = ageDiff ;
					}
				}
			}else {
				returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
			}
		}
		$("#age").val(returnAge);
	}

	var setting = {
		check:{enable:false}
		,view:{selectedMulti:false}
		,data:{simpleData:{enable:true}}
		,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTreeByUserId"}
		,callback:{
			onClick: function(event, treeId, treeNode, clickFlag) {
				/*layer.confirm('确定要分配所选择的机构？', function(index) {
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrgEmployee/orgEmpSave',
                        data: {empId:'#(empId??)', orgId:treeNode.id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            pageTableReload();
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });*/
				$("#orgId").val(treeNode.id);
				$("#orgName").val(treeNode.name);
				//var data={'positionName':$("#positionName").val(),'orgId':treeNode.id};
				//通过id获取部门、小组
				//提交表单数据
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/persOrg/findDepartment',
					data: {'orgId':treeNode.id},
					notice: false,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							var str='<option value="">请选择组织部门</option>';
							var data=rep.data;
							$.each(data,function (index,item) {
								str+='<option value="'+item.id+'">'+item.orgName+'</option>';
							});
							$("#political").html(str);
							form.render('select');
						}
					},
					complete : function() {
					}
				});

				//通过id获取职位
				//提交表单数据
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/pers/position/findPositionByOrgId',
					data: {'orgId':treeNode.id},
					notice: false,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							var str='<option value="">请选择职位</option>';
							var data=rep.data;
							$.each(data,function (index,item) {
								if(item.id==''||typeof(item.id)=='undefined'){

								}else{
									str+='<option value="'+item.id+'">'+item.positionName+'</option>';
								}

							});
							$("#position").html(str);
							form.render('select');
						}
					},
					complete : function() {
					}
				});
			}
		}
	};
});
</script>
#end