#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#startTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        laydate.render({
            elem : '#endTime',
            type: 'date',
            trigger: 'click'
            //,min:0
        });

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(dispatchApply!=null)
            deptSelect.setValue(["#(dispatchApply.dispatchDeptId??)"]);
            #end
        });



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&dispatchDeptId="+dispatchDeptId;

            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        form.on('select(dispatchType)',function (obj) {
            if(obj.value=='1'){
                $("#deptDiv").css("display","block");
            }else{
                $("#deptDiv").css("display","none");
            }

        })

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            var dispatchType=$("#dispatchType").val();
            var dispatchDeptId='';
            if(dispatchType=='1'){
                dispatchDeptId=deptSelect.getValue()[0].id;
                if(dispatchDeptId==null || dispatchDeptId==''){
                    layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                    return false;
                }
            }else{
                dispatchDeptId='';
            }

            var data=$("#noticeForm").serialize()+"&saveType=2&dispatchDeptId="+dispatchDeptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
        <div class="layui-col-md6 layui-form" id="content" style="padding-right: 10px;">
            <div class="layui-collapse" >

                <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>外派员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(dispatchApply.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(dispatchApply==null)#(createBy??)#else#(dispatchApply.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(dispatchApply!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>

                        <!--<button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled  readonly #end type="button">选择</button>-->
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>所在部门</label>
                        <div class="layui-input-inline">
                            <select id="deptId" name="deptId" lay-filter="deptId" lay-verify="required" #if(!isSaveHandle && taskId!=null)  readonly #end>
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==dispatchApply.deptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">职位</label>
                        <div class="layui-input-inline">
                            <select id="positionId" name="positionId" lay-verify="" #if(!isSaveHandle && taskId!=null)  readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==dispatchApply.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label">外派类型</label>
                        <div class="layui-input-inline">
                            <select id="dispatchType" name="dispatchType" lay-filter="dispatchType" lay-verify="required" #if(!isSaveHandle && taskId!=null)  readonly #end>
                                <option value="1">公司单位</option>
                                <!--<option value="2">政府部门</option>
                                <option value="3">银行</option>
                                <option value="4">其他</option>-->
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item" id="deptDiv">
                        <label class="layui-form-label"><font color="red">*</font>外派单位</label>
                        <div class="layui-input-inline" style="width: 387px;">
                            <div id="deptSelect"  name="dispatchDeptId" style="margin-top: 1px;" >

                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>外派开始时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="startTime" id="startTime" #if(!isSaveHandle && taskId!=null)  readonly #end required value="#date(dispatchApply.startTime??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-input-inline">
                            <select name="startDateTime" lay-filter="startDateTime" lay-verify="required" #if(!isSaveHandle && taskId!=null)  readonly #end>
                                <option value=" 00:00:00" #if(startDateTime??=="00:00:00") selected #end>上午</option>
                                <option value=" 12:00:00" #if(startDateTime??=="12:00:00") selected #end>下午</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>外派结束时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="endTime" id="endTime" #if(!isSaveHandle && taskId!=null)  readonly #end required value="#date(dispatchApply.endTime??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-input-inline">
                            <select name="endDateTime" lay-filter="endDateTime" lay-verify="required" #if(!isSaveHandle && taskId!=null)  readonly #end>
                                <option value=" 12:00:00" #if(endDateTime??=="12:00:00") selected #end>上午</option>
                                <option value=" 23:59:59" #if(endDateTime??=="23:59:59") selected #end>下午</option>
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">外派事由</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null)  readonly #end  class="layui-textarea">#(dispatchApply.remark??)</textarea>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="layui-col-md6">

        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
            <div class="pull-right">
                <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
            </div>

        </div>
    </form>
</div>

</body>
#end