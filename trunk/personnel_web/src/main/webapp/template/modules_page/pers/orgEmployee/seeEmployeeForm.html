#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构员工编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">

<style>

	.emailAutoComplete-ul {
		z-index: 999;
	}
	.emailAutoComplete-li {
		line-height: 23px;
		color: #333;
		padding: 2px 5px;
	}
	.layui-disabled{
		color: #0C0C0C!important;
	}
</style>
#end

#define content()
<!--<div class="layui-col-xs3 layui-col-sm3 layui-col-md3 layui-col-lg3">
	<fieldset class="layui-elem-field layui-field-title" style="display:block;">
		<legend>组织架构</legend>
		<div id="zTreeDiv" class="ztree" style="height:500px;overflow:auto;"></div>
	</fieldset>
</div>-->
<div class="layui-col-xs12">

	<div class="layui-row">
		<div style="margin-bottom:23px;"></div>
		<form class="layui-form layui-form-pane" action="" id="employeeForm" style="margin-left:35px;margin-right:35px;">
			<div class="layui-form-item">

				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>姓名</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="fullName" readonly onblur="onblurNull(this,'请输入姓名')" name="fullName" class="layui-input" lay-verify="required" value="#(model.fullName??)" placeholder="请输入姓名">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">工龄</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="workAge" class="layui-input" readonly value="" placeholder="">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">年龄</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="age" class="layui-input" readonly value="" placeholder="">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>民族</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="nationality" lay-search="" lay-verify="required">
							<option value="">请选择民族</option>
							#dictOption("nation", model.nationality??'', "")
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">籍贯</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="nativePlace" class="layui-input"  value="#(model.nativePlace??)" placeholder="">
					</div>
				</div>
				<!--<div class="layui-inline" #if(model.org.orgType??=='branche_office') style="display: none;" #end>
					<label class="layui-form-label"><font color="red">*</font>年休假</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="annualLeaveDays" class="layui-input" lay-verify="required|number" value="#(model.annualLeaveDays??0)" placeholder="">
					</div>
				</div>-->
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">婚姻状况</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="maritalStatus" lay-verify="required">
							<option value="">请选择婚姻状况</option>
							#dictOption("marital_status", model.maritalStatus??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label" style="    padding: 8px 5px;"><font color="red">*</font>是否本地户籍</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="isLocal" lay-verify="required">
							<option value="">请选择是否本地户籍</option>
							<option value="1" #if(model.isLocal??=='1') selected #end>是</option>
							<option value="0" #if(model.isLocal??=='0') selected #end>否</option>
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>证件类型</label>
				<div class="layui-input-block">
					<!--
                                        <input type="text" name="idCardType" id="idCardType" class="layui-input" lay-verify="required|identity" value="#(model.idCard??)" maxlength="19" placeholder="请输入身份证号">
                    -->
					<select name="idCardType" id="idCardType" lay-filter="idCardType" lay-verify="required">
						#dictOption("id_card_type", model.idCardType??'', "")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>证件号码</label>
				<div class="layui-input-block">
					<input type="text" name="idCard" id="idCard" readonly class="layui-input" lay-verify="required|checkIdCard" value="#(model.idCard??)" maxlength="19" placeholder="请输入证件号码">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>身份证地址</label>
				<div class="layui-input-block">
					<input type="text" name="idCardAddr"  id="idCardAddr" onblur="onblurNull(this,'请输入身份证地址')" lay-verify="required" class="layui-input" value="#(model.idCardAddr??)" placeholder="请输入身份证地址">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>常住地址</label>
				<div class="layui-input-block">
					<input type="text" name="residentAddr" onblur="onblurNull(this,'请输入常住地址')" lay-verify="required" class="layui-input" value="#(model.residentAddr??)" placeholder="请输入常住地址">
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">性别</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="sex" id="sex" lay-verify="required" lay-filter="">
							<option value="">请选择性别</option>
							#dictOption("sex", model.sex??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>出生日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="birthday" name="birthday" class="layui-input" lay-verify="required" value="#(model.birthday??)" autocomplete="off">
					</div>
				</div>
			</div>
			<fieldset class="layui-elem-field" style="margin-top: 30px;margin-bottom: 30px;padding-left: 5px;padding-right: 5px;">
				<legend>组织机构</legend>
				<div class="layui-row" >
					<button class="layui-btn" style="float: right;margin-right: 10px;" type="button" onclick="addTpl()" >添加</button>
				</div>
				<table class="layui-table">
					<colgroup>
						<col width="30%">
						<col width="15%">
						<col width="15%">
						<col width="13%">
						<col width="18%">
					</colgroup>
					<thead>
					<tr>
						<th style="text-align: center;">部门</th>
						<th style="text-align: center;">职位</th>
						<th style="text-align: center;">角色</th>
						<th style="text-align: center;">是否主部门</th>
						<th style="text-align: center;">操作</th>
					</tr>
					</thead>
					<input id="count" name="count" value="#(recordList.size()??0)" type="hidden">
					<tbody id="deptBody">
					#for(record:recordList)
					<tr id="tr-#(for.index+1)">
						<td align="center">
							<div class="layui-input-inline" style="width: 100%;" >
								<input  name="relDeptId-#(for.index+1)" value="#(record.relDeptId??)" type="hidden" >
								<input  id="deptIdValue-#(for.index+1)" value="#(record.deptId??)" type="hidden">
								<input  name="deptId-#(for.index+1)" lay-filter="deptId-#(for.index+1)" data-index="#(for.index+1)" id="deptId-#(for.index+1)" class="layui-input" lay-verify="required">
							</div>
						</td>

						<td align="center">
							<div class="layui-input-inline" style="width: 100%;" >
								<input  name="relPositionId-#(for.index+1)" value="#(record.relPositionId??)" type="hidden">
								<select name="positionId-#(for.index+1)" id="positionId-#(for.index+1)" data-index="#(for.index+1)" lay-filter="positionSelect" lay-verify="required">
									<option value="" >请选择职位</option>
									#for(position : record.persPositionList)
									<option value="#(position.id)" role-id="#(position.roleId??)" role-name="#(position.roleName)" #if(record.positionId??==position.id) selected #end >#(position.positionName)</option>
									#end
								</select>
							</div>
						</td>
						<td align="center">
							<input id="relRoleId-#(for.index+1)"  name="relRoleId-#(for.index+1)" value="#(record.relRoleId??)" type="hidden">
							<input id="roleId-#(for.index+1)"  name="roleId-#(for.index+1)" value="#(record.roleId??)" type="hidden">
							<span id="roleName-#(for.index+1)">#(record.mainRole.roleName??)</span>
						</td>
						<td align="center">
							<input id="isMain-#(for.index+1)"  name="isMain-#(for.index+1)" value="#(record.isMain??)" type="hidden">
							#if(record.isMain=="1")
							<span class="layui-badge layui-bg-green" id="mainSpan-#(for.index+1)">主</span>
							#else
							<span class="layui-badge layui-bg-blue" id="mainSpan-#(for.index+1)">兼</span>
							#end
						</td>
						<td align="center">
							<div class="layui-btn-group">
								<button class="layui-btn layui-btn-xs layui-btn-green" #if(record.isMain=="1") style="display: none;" #else style="display: inline-block;" #end type="button" id="setMainBtn-#(for.index+1)" onclick="setMain(#(for.index+1))" >设为主部门</button>
								<button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del(#(for.index+1),'#(record.relDeptId??)','#(record.relPositionId??)','#(record.relRoleId??)','#(model.Id??)')" >作废</button>
							</div>
						</td>
					</tr>
					#end
					</tbody>
				</table>
			</fieldset>


			<!--<div class="layui-inline">
                <label class="layui-form-label"><font color="red">*</font>所属组织</label>
                <div class="layui-input-inline" style="width:155px;">
                    <input type="text" id="orgName" name="orgName" readonly class="layui-input" lay-verify="required" value="#(orgName??)" autocomplete="off">
                    <input type="hidden" id="orgId" name="orgId" value="#(model.orgId??)">
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label"><font color="red">*</font>组织部门</label>
                <div class="layui-input-inline" style="width:155px;">
                    <select name="deptId" id="political" lay-filter="" lay-verify="required">
                        <option value="">请选择组织部门</option>
                        #if(model!=null)
                        #for(dept:deptList)
                        <option value="#(dept.id)" #if(dept.id==model.deptId??) selected #end>#(dept.orgName)</option>
                        #end
                        #end
                    </select>
                </div>
            </div>-->
			<div class="layui-form-item">

				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>员工类型</label>
					<div class="layui-input-inline" style="width:155px;">
						<select id="employeeType" name="employeeType" lay-search="" lay-verify="required">
							<option value="">请选择员工类型</option>
							#dictOption("employee_type", model.employeeType??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>入职日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="entryTime" name="entryTime" class="layui-input" lay-verify="required" value="#date(model.entryTime??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
				<!--<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>职位</label>
					<div class="layui-input-inline" style="width:155px;">
						<div class="layui-form" lay-filter="positionSelectFilter">
							<select id="position" name="position" lay-search="" lay-verify="required">
								<option value="">请选择职位</option>
								#if(model!=null)
								#for(position:positionList)
								<option value="#(position.id)" #if(position.id==model.position??) selected #end>#(position.positionName)</option>
								#end
								#end
							</select>
						</div>
					</div>
				</div>-->
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>手机号码</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="phoneNum" name="phoneNum" class="layui-input" lay-verify="required|phoneNumber" value="#(model.phoneNum??)" placeholder="请输入手机号码">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">分机号码</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="extNum" name="extNum" class="layui-input" lay-verify="phoneNumber" value="#(model.extNum??)" placeholder="请输入分机号码">
					</div>
				</div>
			</div>

			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>最高学历</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="education" lay-filter="" lay-verify="required">
							<option value="">请选择文化程度</option>
							#dictOption("education", model.education??'', "")
						</select>
					</div>
				</div>
			</div>

			<div class="layui-form-item">

				<label class="layui-form-label">常用邮箱</label>
				<div class="layui-input-block" >
					<input type="text" id="emailAddr" name="emailAddr" lay-verify="checkEmail" class="layui-input" autocomplete="off" value="#(model.emailAddr??)" placeholder="请输入常用邮箱">
				</div>
			</div>

			<!--<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">缴纳社保</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="isSocial" name="isSocial" value="0" title="否" checked>
						<input type="radio" lay-filter="isSocial" name="isSocial" value="1" title="是">
					</div>
				</div>
				<div class="layui-inline" id="socialTimeDiv" style="display:none;">
					<label class="layui-form-label"><font color="red">*</font>缴纳时间</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="socialTime" name="socialTime" class="layui-input" value="#date(model.socialTime??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
			</div>-->
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>是否住宿</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="isStaySleep" name="isStaySleep" value="0" title="否" #if(model==null || model.isStaySleep??=='0') checked #end>
						<input type="radio" lay-filter="isStaySleep" name="isStaySleep" value="1" title="是" #if(model.isStaySleep??=='1') checked #end>
					</div>
				</div>
				<div class="layui-inline" id="roomNumDiv" #if(model.isStaySleep??=='1') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label"><font color="red">*</font>房间号码</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="roomNum" name="roomNum" onblur="onblurNull(this,'请输入房间号码')" class="layui-input" #if(model.isStaySleep??=='1') lay-verify="required" #end value="#(model.roomNum??)" placeholder="请输入住宿房间号码">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>转正日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="formalDate" name="formalDate" class="layui-input" lay-verify="required|checkFormalDate" value="#date(model.formalDate??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red"></font>社保缴纳状态</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="socialStatus" lay-verify="">
							<option value=""></option>
							<option value="1" #if(model.socialStatus??=='1') selected #end>已缴</option>
							<option value="2" #if(model.socialStatus??=='2') selected #end>未缴</option>
							<option value="3" #if(model.socialStatus??=='3') selected #end>放弃</option>
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;font-size: 13px;"><font color="red"></font>社保缴纳地址(旧)</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="socialAddr" name="socialAddr" class="layui-input" lay-verify="" value="#(model.socialAddr??)" autocomplete="off">
					</div>
				</div>

			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;font-size: 13px;"><font color="red"></font>社保缴纳地址(新)</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="socialAddrId" lay-verify="">
							<option value="">请选择社保缴纳地址</option>
							#for(addr : socialAddresses)
							<option value="#(addr.id??)" #if(addr.id==model.socialAddrId??) selected #end>#(addr.name??)</option>
							#end
						</select>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red"></font>意外险缴纳状态</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="accidentInsurance" lay-verify="">
							<option value=""></option>
							<option value="1" #if(model.accidentInsurance??=='1') selected #end>已缴</option>
							<option value="2" #if(model.accidentInsurance??=='2') selected #end>未缴</option>
							<option value="3" #if(model.accidentInsurance??=='3') selected #end>放弃</option>
						</select>
					</div>
				</div>
			</div>
			#if(userType!='system_admin')
			#shiroHasPermission("emp:archives:salary")
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>试用薪资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="probationSalary" name="probationSalary" maxlength="11" class="layui-input" lay-verify="checkMoney" value="#(model.probationSalary??)" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>转正薪资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="salary" name="salary" class="layui-input" maxlength="11" lay-verify="checkMoney" value="#(model.salary??)" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>现有薪资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="nowSalary" name="nowSalary" class="layui-input" maxlength="11" lay-verify="checkMoney" value="#(model.nowSalary??)" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red"></font>基本工资</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="baseSalary" name="baseSalary" class="layui-input" maxlength="11" lay-verify="checkMoney" value="#(model.baseSalary??)" autocomplete="off">
					</div>
				</div>
			</div>
			#end
			#end
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>状态</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="archiveStatus" id="archiveStatus" disabled lay-filter="archiveStatus" lay-verify="required">
							#dictOption("archive_status", model.archiveStatus??'', "")
						</select>
					</div>
				</div>

				<div class="layui-inline" id="quitTimeDiv" #if(model.archiveStatus??=='quit') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label"><font color="red">*</font>离职日期</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="quitTime" name="quitTime" class="layui-input" #if(model.archiveStatus??=='quit') lay-verify="required" #end value="#date(model.quitTime??,'yyyy-MM-dd')" autocomplete="off">
					</div>
				</div>
				<div class="layui-inline" id="quitUseAnnualLeaveDaysDiv" #if(model.archiveStatus??=='quit') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>休年休假数</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="" name="" class="layui-input" #if(model.archiveStatus??=='quit')  #end value="#(model.quitUseAnnualLeaveDays??)" autocomplete="off">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 3px;"><font color="red">*</font>是否再次入职</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="isReemployment" name="isReemployment" value="0" title="否" #if(model==null || model.isReemployment??=='0') checked #end>
						<input type="radio" lay-filter="isReemployment" name="isReemployment" value="1" title="是" #if(model.isReemployment??=='1') checked #end>
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>毕业院校</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="graduateSchool" onblur="onblurNull(this,'请输入毕业院校')" lay-verify="required" class="layui-input" value="#(model.graduateSchool??)" placeholder="请输入毕业院校">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>专业</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="schoolMajor" lay-verify="required" onblur="onblurNull(this,'请输入专业')" class="layui-input" value="#(model.schoolMajor??)" placeholder="请输入专业">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>职称/证书</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" lay-filter="certificate" name="certificate" value="0" title="无" #if(model==null || model.certificate??=='0') checked #end>
						<input type="radio" lay-filter="certificate" name="certificate" value="1" title="有" #if(model.certificate??=='1') checked #end>
					</div>
				</div>
				<div class="layui-inline" id="certificateNameDiv" #if(model.certificate??=='1') style="display: inline-block;" #else style="display: none;" #end>
					<label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>职称/证书名称</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="certificateName" onblur="onblurNull(this,'请输入职称/证书名称')" name="certificateName" #if(model.certificate??=='1') lay-verify="required" #end class="layui-input" value="#(model.certificateName??)" placeholder="请输入职称/证书名称">
					</div>
				</div>

			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label">爱好特长</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="hobby" class="layui-input" value="#(model.hobby??)" placeholder="请输入爱好特长">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>紧急联系人</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" name="linkPeople" class="layui-input" onblur="onblurNull(this,'请输入紧急联系人')" lay-verify="required|checkName" value="#(model.linkPeople??)" placeholder="请输入紧急联系人">
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>紧急联系人手机</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="text" id="linkPhone" name="linkPhone" class="layui-input"  lay-verify="required|phoneNumber" value="#(model.linkPhone??)" placeholder="请输入紧急联系人手机">
					</div>
				</div>
			</div>
			<div class="layui-form-item">
				<div class="layui-inline">
					<label class="layui-form-label"><font color="red">*</font>入职渠道</label>
					<div class="layui-input-inline" style="width:155px;">
						<select name="entryChannel" lay-filter="" lay-verify="required">
							<option value="">请选择入职渠道</option>
							#dictOption("entry_channel", model.entryChannel??'', "")
						</select>
					</div>
				</div>
				<div class="layui-inline">
					<label class="layui-form-label">是否残疾</label>
					<div class="layui-input-inline" style="width:155px;">
						<input type="radio" name="isDisability" value="0" title="否" checked>
						<input type="radio" name="isDisability" value="1" title="是">
					</div>
				</div>
			</div>
			<div class="layui-form-item layui-form-text">
				<label class="layui-form-label">备注</label>
				<div class="layui-input-block">
					<textarea name="remark" placeholder="请输入内容" maxlength="255" class="layui-textarea">#(model.remark??)</textarea>
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" id="id" value="#(model.Id??)">
					<!--<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>-->
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button class="layui-btn layui-btn-primary" type="reset" id="resetBtn" style="display: none;">重置</button>
				</div>
			</div>
		</form>
	</div>
</div>

#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script src="#(ctxPath)/static/js/emailAutoComplete.js"></script>

<script type="text/html" id="deptTpl">
	<tr id="tr-{{d.idx}}">
		<td align="center">
			<div class="layui-input-inline" style="width: 100%;" >
				<input  name="relDeptId-{{d.idx}}" value="" type="hidden" >
				<input  id="deptIdValue-{{d.idx}}" value="" type="hidden">
				<input  name="deptId-{{d.idx}}" lay-filter="deptId-{{d.idx}}" data-index="{{d.idx}}" id="deptId-{{d.idx}}" class="layui-input" lay-verify="required">
			</div>
		</td>

		<td align="center">
			<div class="layui-input-inline" style="width: 100%;" >
				<input  name="relPositionId-{{d.idx}}" value="#(record.relPositionId??)" type="hidden">
				<select name="positionId-{{d.idx}}" id="positionId-{{d.idx}}" data-index="{{d.idx}}" lay-filter="positionSelect" lay-verify="required">
					<option value="" >请选择职位</option>
				</select>
			</div>
		</td>
		<td align="center">
			<input id="relRoleId-{{d.idx}}"  name="relRoleId-{{d.idx}}" value="#(record.relRoleId??)" type="hidden">
			<input id="roleId-{{d.idx}}"  name="roleId-{{d.idx}}" value="#(record.roleId??)" type="hidden">
			<span id="roleName-{{d.idx}}">#(record.mainRole.roleName??)</span>
		</td>
		<td align="center">
			<!--<button class="layui-btn layui-btn-xs layui-btn-danger" type="button" onclick="del({{d.idx}},'','','','')" >作废</button>-->
		</td>
	</tr>
</script>

<script type="text/javascript">
layui.extend({
	treeSelect: '/static/js/extend/treeSelect'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use([ 'form', 'laydate','treeSelect','laytpl' ], function() {
	var form = layui.form
	, layer = layui.layer
	, laydate = layui.laydate
	, $ = layui.$
	,treeSelect=layui.treeSelect
	,laytpl=layui.laytpl
	;

	#for(record : recordList)
		loadTreeSelect("deptId-#(for.index+1)","#(record.deptId)");
	#end


	addTpl=function() {
		var idx=Number($("#count").val())+1;
		var data={'idx':idx};
		laytpl(deptTpl.innerHTML).render(data, function(html){
			$("#deptBody").append(html);
			loadTreeSelect('deptId-'+idx,'');
			form.render('select');
		});
		$("#count").val(idx);
	}

	 function loadTreeSelect(id,checkId){
		treeSelect.render({
			// 选择器
			elem: "#"+id,
			// 数据
			data: '#(ctxPath)/persOrg/orgTreeSelect',
			// 异步加载方式：get/post，默认get
			type: 'get',
			// 占位符
			placeholder: '请选择部门',
			// 是否开启搜索功能：true/false，默认false
			search: true,
			// 点击回调
			click: function(d){
				var index=$("#"+id).attr("data-index");
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/pers/position/getPositionByOrgId',
					data: {'orgId':d.current.id},
					notice: false,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							var str='<option value="">请选择职位</option>';
							var data=rep.data;
							$.each(data,function (index,item) {
								str+='<option value="'+item.id+'" role-id="'+item.roleId+'" role-name="'+item.roleName+'">'+item.positionName+'</option>';
							});
							$("#positionId-"+index).html(str);
							form.render('select');


						}
					},
					complete : function() {
					}
				});
			},
			// 加载完成后的回调函数
			success: function (d) {
				if(checkId!=''){
					treeSelect.checkNode(id, checkId);
				}
			}
		});
	}

	form.on('select(positionSelect)', function(data){
		var index=data.elem.getAttribute("data-index");
		var roleId=$("#positionId-"+index).find("option[value='"+data.value+"']").attr("role-id");
		var roleName=$("#positionId-"+index).find("option[value='"+data.value+"']").attr("role-name");
		if(roleId==undefined){
			$("#roleId-"+index).val('');
		}else{
			$("#roleId-"+index).val(roleId);
		}

		if(roleName==undefined){
			$("#roleName-"+index).html('');
		}else{
			$("#roleName-"+index).html(roleName);
		}

	});

	#shiroHasPermission("emp:entryInfoForm:update")
	$("#fullName").removeAttr("readonly");
	$("#idCard").removeAttr("readonly");
	#end

	#shiroHasPermission("emp:entryInfoForm:quitBtn")
	$("#archiveStatus").removeAttr("disabled");
	var options=$("#archiveStatus").find("option");
	options.each(function (index,item){
		if($(item).attr("value")!='quit'){
			$(item).prop("disabled",true);
		}
	});
	form.render('select');
	#end


	del=function(trIndex,relDeptId,relPositionId,relRoleId,empId){
		if(relDeptId=='' && relPositionId==''&&relRoleId==''){
			$("#tr-"+trIndex).remove();
		}else{
			layer.confirm('您确定要作废吗？', function(index) {
				//作废操作
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/persOrgEmployee/delEmployeeRel',
					data: {'relDeptId':relDeptId, 'relPositionId':relPositionId,'relRoleId':relRoleId,'empId':empId},
					notice: true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							$("#tr-"+trIndex).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}

	}



	$('#emailAddr').emailAutoComplete({
		postfix: ['cncsgroup.com','qq.com', '163.com', 'sina.com', 'gmail.com', 'sohu.com']
	});
	
	//出生日期渲染
	/*laydate.render({
		elem: '#birthday'
		, trigger: 'click'
		,done:function (value,date) {
			setAge(value);
		}
	});

	laydate.render({
		elem:'#entryTime',
		trigger:'click',
		done:function (value,date) {
			setWorkAge(value);
		}
	});
	laydate.render({
		elem:'#formalDate',
		trigger:'click',
		done: function(value, date){
			var entryTime=new Date($("#entryTime").val());
			var formalDate=new Date(value);
			if(entryTime>formalDate){
				layer.msg('转正日期不能小于入职日期', {icon: 2, offset: 'auto'});
				$("#formalDate").attr("style","border-color: #FF5722!important;");
			}else{
				$("#formalDate").attr("style","border-color: #C9C9C9!important;");
			}
		}
	});
	laydate.render({
		elem:'#quitTime',
		trigger:'click',
		done:function (value, date) {
			/!*layer.confirm('选择离职日期代表该员工已离职，只能从离职类别查找，确认要选择吗？', {
				btn: ['确定','取消'] //按钮
			}, function(index){
				layer.close(index);
			}, function(){
				$("#quitTime").val('');
			});*!/
		}
	});
	laydate.render({
		elem:'#socialTime',
		trigger:'click'
	});*/


	//监听状态select
	form.on('select(archiveStatus)',function (obj) {
		if(obj.value==='quit'){
			$("#quitTimeDiv").css('display','inline-block');
			$("#quitTime").attr("lay-verify","required");
			$("#quitUseAnnualLeaveDaysDiv").css('display','inline-block');
		}else{
			$("#quitTimeDiv").css('display','none');
			$("#quitTime").attr("lay-verify","");
			$("#quitTime").attr("value","");
			$("#quitUseAnnualLeaveDaysDiv").css('display','none');

		}
	});

	#if(model!=null)
		#if(model.archiveStatus=='quit')
		setQuitWorkAge("#date(model.entryTime??,'yyyy-MM-dd')","#date(model.quitTime??,'yyyy-MM-dd')");
		#else
		setWorkAge("#date(model.entryTime??,'yyyy-MM-dd')");
		#end
		setAge("#(model.birthday??)");
	#end

	function setWorkAge(entryTime) {
		var startDate=new Date(entryTime);
		var endDate=new Date();

		var diffDate=new Date(endDate-startDate);
		$("#workAge").val(diffDate.getFullYear()-1970+"年"+diffDate.getMonth()+"个月"+(diffDate.getDate()-1)+"天");
	}

	function setQuitWorkAge(entryTime,quitTime) {
		var startDate=new Date(entryTime);
		var endDate=new Date(quitTime);

		var diffDate=new Date(endDate-startDate);
		$("#workAge").val(diffDate.getFullYear()-1970+"年"+diffDate.getMonth()+"个月"+(diffDate.getDate()-1)+"天");
	}


	function setAge(birthday) {
		var returnAge,
				strBirthdayArr=birthday.split("-"),
				birthYear = strBirthdayArr[0],
				birthMonth = strBirthdayArr[1],
				birthDay = strBirthdayArr[2],
				d = new Date(),
				nowYear = d.getFullYear(),
				nowMonth = d.getMonth() + 1,
				nowDay = d.getDate();
		if(nowYear == birthYear){
			returnAge = 0;//同年 则为0周岁
		}else{
			var ageDiff = nowYear - birthYear ; //年之差
			if(ageDiff > 0){
				if(nowMonth == birthMonth) {
					var dayDiff = nowDay - birthDay;//日之差
					if(dayDiff < 0) {
						returnAge = ageDiff - 1;
					}else {
						returnAge = ageDiff;
					}
				}else {
					var monthDiff = nowMonth - birthMonth;//月之差
					if(monthDiff < 0) {
						returnAge = ageDiff - 1;
					}
					else {
						returnAge = ageDiff ;
					}
				}
			}else {
				returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
			}
		}
		$("#age").val(returnAge);
	}

	var setting = {
		check:{enable:false}
		,view:{selectedMulti:false}
		,data:{simpleData:{enable:true}}
		,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTreeByUserId"}
		,callback:{
			onClick: function(event, treeId, treeNode, clickFlag) {
				/*layer.confirm('确定要分配所选择的机构？', function(index) {
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrgEmployee/orgEmpSave',
                        data: {empId:'#(empId??)', orgId:treeNode.id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            pageTableReload();
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });*/
				$("#orgId").val(treeNode.id);
				$("#orgName").val(treeNode.name);
				//var data={'positionName':$("#positionName").val(),'orgId':treeNode.id};
				//通过id获取部门、小组
				//提交表单数据
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/persOrg/findDepartment',
					data: {'orgId':treeNode.id},
					notice: false,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							var str='<option value="">请选择组织部门</option>';
							var data=rep.data;
							$.each(data,function (index,item) {
								str+='<option value="'+item.id+'">'+item.orgName+'</option>';
							});
							$("#political").html(str);
							form.render('select');
						}
					},
					complete : function() {
					}
				});

				//通过id获取职位
				//提交表单数据
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/pers/position/findPositionByOrgId',
					data: {'orgId':treeNode.id},
					notice: false,
					loadFlag: false,
					success : function(rep){
						if(rep.state==='ok'){
							var str='<option value="">请选择职位</option>';
							var data=rep.data;
							$.each(data,function (index,item) {
								if(item.id==''||typeof(item.id)=='undefined'){

								}else{
									str+='<option value="'+item.id+'">'+item.positionName+'</option>';
								}

							});
							$("#position").html(str);
							form.render('select');
						}
					},
					complete : function() {
					}
				});
			}
		}
	};

	// 初始化树结构
	//var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);


	//自定义验证规则
	form.verify({
		phoneNumber: function(value){
			if(value!=null && value!=''){
				var mobilePtn = /^1[3456789][0-9]{9}$/;
				var landlinePtn = /^(0\d{2,3}-)?\d{7,8}$/;
				if(!mobilePtn.test(value) && !landlinePtn.test(value)){
					return '请输入正确的手机号码或座机格式';
				}
			}
		},
		checkFormalDate:function (value) {
			if(value!=null && value!=''){
				var entryTime=new Date($("#entryTime").val());
				var formalDate=new Date(value);
				if(entryTime>formalDate){
					//layer.msg('', {icon: 2, offset: 'auto'});
					return '转正日期不能小于入职日期';
				}
			}
		},
		checkName:function (value) {
			if(value!=null && value!=''){
				var regex = new RegExp("^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z]){1,20}$");
				if(!regex.test(value)){
					return '只能输入汉字和英文';
				}
			}
		},
		checkIdCard:function (value) {

			if($("#idCardType").val()==='sheng_fen_zheng'){
				var idCard=/(^\d{15}$)|(^\d{17}(x|X|\d)$)/
				if(!idCard.test(value)){
					return '请输入正确的身份证号';
				}
			}else{
				var idCard=/^[\u4E00-\u9FA5]+$/;
				if(idCard.test(value)){
					return '请输入正确的港澳台证件号码';
				}
			}

		},
		checkMoney:function (value) {
			if(value!=null && value!=''){
				var money=/(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
				if(!money.test(value)){
					return '请输入正确的金额格式';
				}
			}
		},
		checkEmail:function (value) {
			if(value!=null && value!=''){
				var email=/^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
				if(!email.test(value)){
					return '邮箱格式不正确';
				}
			}
		}
	});

	//转正日期失去光标事件
	$("#formalDate").on('blur',function () {
		checkFormalDate();
	});

	function checkFormalDate(){
		var entryTime=new Date($("#entryTime").val());
		var formalDate=new Date($("#formalDate").val());
		if(entryTime>formalDate){
			layer.msg('转正日期不能小于入职日期', {icon: 2, offset: 'auto'});
			$("#formalDate").attr("style","border-color: #FF5722!important;");
		}else{
			$("#formalDate").attr("style","border-color: #C9C9C9!important;");
		}
	}

	$("#phoneNum").on('blur',function () {
		var value=$("#phoneNum").val();
		var mobilePtn = /^1[3456789][0-9]{9}$/;
		var landlinePtn = /^(0\d{2,3}-)?\d{7,8}$/;
		if(!mobilePtn.test(value) && !landlinePtn.test(value)){
			layer.msg('请输入正确的手机号码或座机格式\'', {icon: 2, offset: 'auto'});
			$("#phoneNum").attr("style","border-color: #FF5722!important;");
		}else{
			$("#phoneNum").attr("style","border-color: #C9C9C9!important;");
		}
	});

	$("#extNum").on('blur',function () {
		var value=$("#extNum").val();
		if(value!='' && value!=null){
			var mobilePtn = /^1[3456789][0-9]{9}$/;
			var landlinePtn = /^(0\d{2,3}-)?\d{7,8}$/;
			if(!mobilePtn.test(value) && !landlinePtn.test(value)){
				layer.msg('请输入正确的手机号码或座机格式\'', {icon: 2, offset: 'auto'});
				$("#extNum").attr("style","border-color: #FF5722!important;");
			}else{
				$("#extNum").attr("style","border-color: #C9C9C9!important;");
			}
		}

	});

	$("#linkPhone").on('blur',function () {
		var value=$("#linkPhone").val();
		var mobilePtn = /^1[3456789][0-9]{9}$/;
		var landlinePtn = /^(0\d{2,3}-)?\d{7,8}$/;
		if(!mobilePtn.test(value) && !landlinePtn.test(value)){
			layer.msg('请输入正确的手机号码或座机格式\'', {icon: 2, offset: 'auto'});
			$("#linkPhone").attr("style","border-color: #FF5722!important;");
		}else{
			$("#linkPhone").attr("style","border-color: #C9C9C9!important;");
		}
	})

	$("#idCard").on('blur',function () {
		var value=$("#idCard").val();
		if($("#idCardType").val()==='sheng_fen_zheng'){
			var idCard=/(^\d{15}$)|(^\d{17}(x|X|\d)$)/
			if(!idCard.test(value)){
				layer.msg('请输入正确的身份证号', {icon: 2, offset: 'auto'});
				$("#idCard").attr("style","border-color: #FF5722!important;");
			}else{
				$("#idCard").attr("style","border-color: #C9C9C9!important;");
			}
		}else{
			var idCard=/^[\u4E00-\u9FA5]+$/;
			if(value==''|| idCard.test(value)){
				layer.msg('请输入正确的港澳台证件号码', {icon: 2, offset: 'auto'});
				$("#idCard").attr("style","border-color: #FF5722!important;");
			}else{
				$("#idCard").attr("style","border-color: #C9C9C9!important;");
			}
		}
	});

	onblurNull=function(obj,msg){
		var value=$(obj).val();
		if($(obj).attr("name")==='fullName' || $(obj).attr("name")==='linkPeople') {
			var regex = new RegExp("^([\u4E00-\uFA29]|[\uE7C7-\uE7F3]|[a-zA-Z]){1,20}$");
			console.log(regex.test(value));
			if(!regex.test(value)){
				layer.msg('请输入汉字和英文', {icon: 2, offset: 'auto'});
				$(obj).attr("style","border-color: #FF5722!important;");
			}else{
				$(obj).attr("style","border-color: #C9C9C9!important;");
			}
		}else{

			if(value=='' || value==null || typeof(value)=='undefined' ){
				layer.msg(msg, {icon: 2, offset: 'auto'});
				$(obj).attr("style","border-color: #FF5722!important;");
			}else{
				$(obj).attr("style","border-color: #C9C9C9!important;");
			}
		}
	}



	//监听是否缴纳社保radio
	form.on('radio(isSocial)',function (obj) {
		if(obj.value==='1'){
			$("#socialTimeDiv").css("display","inline-block");
			$("#socialTime").attr("lay-verify","required");
		}else{
			$("#socialTimeDiv").css("display","none");
			$("#socialTime").attr("lay-verify","");
			$("#socialTime").attr("value","");
		}
	});

	//监听是否住宿radio
	form.on('radio(isStaySleep)',function (obj) {
		if(obj.value==='1'){
			$("#roomNumDiv").css("display","inline-block");
			$("#roomNum").attr("lay-verify","required");
		}else{
			$("#roomNumDiv").css("display","none");
			$("#roomNum").attr("lay-verify","");
			$("#roomNum").attr("value","");
		}
	});

	//监听是否有职称radio
	form.on('radio(certificate)',function (obj) {
		if(obj.value==='1'){
			$("#certificateNameDiv").css("display","inline-block");
			$("#certificateName").attr("lay-verify","required");
		}else{
			$("#certificateNameDiv").css("display","none");
			$("#certificateName").attr("lay-verify","");
			$("#certificateName").attr("value","");
		}
	});


	/*form.on('select(political)',function (obj) {
		//通过id获取部门、小组
		//提交表单数据
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/persOrg/findDepartment',
			data: {'orgId':obj.value},
			notice: false,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					var str='<option value="">请选择组织部门</option>';
					var data=rep.data;
					$.each(data,function (index,item) {
						str+='<option value="'+item.id+'">'+item.orgName+'</option>';
					});
					$("#political").html(str);
					form.render('select');
				}
			},
			complete : function() {
			}
		});

		//通过id获取职位
		//提交表单数据
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/pers/position/findPositionByOrgId',
			data: {'orgId':obj.value},
			notice: false,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					var str='<option value="">请选择职位</option>';
					var data=rep.data;
					$.each(data,function (index,item) {
						if(item.id==''||typeof(item.id)=='undefined'){

						}else{
							str+='<option value="'+item.id+'">'+item.positionName+'</option>';
						}

					});
					$("#position").html(str);
					form.render('select');
				}
			},
			complete : function() {
			}
		});
	});*/

	//身份证联动出生日期
	function idCardBirthday(psidno){
		var birthdayno,birthdaytemp;
		if (psidno.length == 18) {
			birthdayno=psidno.substring(6,14)
		} else if (psidno.length == 15) {
			birthdaytemp = psidno.substring(6,12);
			birthdayno = "19" + birthdaytemp;
		}else{
			return false
		}
		var birthday = birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8);
		return birthday
	}


	$('#idCard').on('keyup', function() {

		if($("#idCardType").val()==='sheng_fen_zheng'){
			var idCard=$(this).val();
			if(idCard.length==15 || idCard.length==18){
				var sexNum=idCard.substring(idCard.length-2,idCard.length-1);
				$("#sex").find("option:selected").removeAttr('selected');
				if(sexNum%2==1){
					$("#sex").find("option[value='male']").prop('selected',true);
				}else{
					$("#sex").find("option[value='female']").prop('selected',true);
				}
				form.render('select');
			}

			var birthday = idCardBirthday($(this).val());
			if (birthday) {
				setAge(birthday);
				$('#birthday').val(birthday);
			}
		}
	});
	
	$('#positionName').keydown(function(e){
		if(e.keyCode==13){
			var positionName = $("#positionName").val();
			if(positionName != null && positionName != ''){

				//提交表单数据
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/dict/save',
		            data: {dictType:'position', dictTypeName:'职位', dictName:positionName, remarks:'职位'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		var dictName = rep.dict.dictName;
							var dictValue = rep.dict.dictValue;
							$("#position").append("<option value='"+dictValue+"'>"+dictName+"</option>");
							form.render('select', 'positionSelectFilter');
				    	}
		            },
		            complete : function() {
				    }
		        });
			}else{
				layer.msg('请输入职位名称!',{icon:5});
			}
			return false;
		}
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {

		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/persOrgEmployee/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){

					var id=$("#id").val();
					if(id==='' || id===null){
						parent.pageTableReload();
						$("#resetBtn").click();
					}else{
						parent.pageTableReloadCurrPage();
						pop_close();
					}
					pop_close();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});

	$("#employeeForm button").prop("disabled", true);
	$("#employeeForm button").addClass("layui-btn-disabled")
	//$("#giveSchemeDetail").prop("disabled",false);
	//$("#giveSchemeDetail").removeClass("layui-btn-disabled");
	$("#employeeForm .layui-form-footer").hide();
	//$("#employeeForm").find("input,select,textarea").prop("disabled", true).prop("readonly", true);
	$("#employeeForm").find("input,select,textarea").prop("readonly", true);
	$("#employeeForm").find("select").prop("disabled", true);
	$("#employeeForm select").removeAttr("lay-search");
	form.render('select');
	form.render('radio');

});
</script>
#end