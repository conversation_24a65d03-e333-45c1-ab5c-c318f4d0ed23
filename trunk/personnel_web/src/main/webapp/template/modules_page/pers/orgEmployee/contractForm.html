#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery','laydate','upload'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate,upload=layui.upload;

        laydate.render({
            elem: '#contractStartDate'
            , trigger: 'click'
        });

        laydate.render({
            elem: '#contractEndDate'
            , trigger: 'click'
        });

        laydate.render({
            elem:'#socialTime',
            trigger:'click'
        });

        //监听是否缴纳社保radio
        form.on('radio(isSocial)',function (obj) {
            if(obj.value==='1'){
                $("#socialTimeDiv").css("display","inline-block");
                $("#socialTime").attr("lay-verify","required");
            }else{
                $("#socialTimeDiv").css("display","none");
                $("#socialTime").attr("lay-verify","");
                $("#socialTime").attr("value","");
            }
        });

        //自定义验证规则
        form.verify({

            checkDateSize:function (value) {
                if(value!=null && value!=''){
                    var entryTime=new Date($("#contractStartDate").val());
                    var formalDate=new Date(value);
                    if(entryTime>formalDate){
                        //layer.msg('', {icon: 2, offset: 'auto'});
                        return '合同到期日期不能大于签订日期';
                    }
                }
            },
            checkSalarySize:function (value) {
                if(value!=null && value!=''){
                    var testSalary=$("#testSalary").val();
                    var formalSalary=value;
                    if(testSalary>formalSalary){
                        //layer.msg('', {icon: 2, offset: 'auto'});
                        return '转正薪资不能小于使用期薪资';
                    }
                }
            }
        });




        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/persOrgEmployee/saveContract";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#contractForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.contractTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        form.on('select(dateType)',function (obj) {
            if(obj.value=='1'){
                $("#contractEndDateDiv").css('display','block');
                $("#contractEndDate").attr('lay-verify','required|checkDateSize');
            }else if(obj.value=='2'){
                $("#contractEndDateDiv").css('display','none');
                $("#contractEndDate").attr('lay-verify','');
            }

        })

        $(".contractUploadBtn").each(function () {
            var dataIndex=$(this).attr("data-index");
            //员工头像上传
            var uploadInst = upload.render({
                elem: this
                ,url: '#(fileUploadUrl)?bucket=employeeContract'
                ,before: function(obj){
                    $("#confirmBtn").attr("disabled",true);
                    $("#confirmBtn").addClass("layui-btn-disabled");
                    //预读本地文件示例，不支持ie8
                    obj.preview(function(index, file, result){
                        $('#contractImg'+dataIndex).attr('src', result);
                    });
                    layer.load();
                }
                ,done: function(res){
                    $("#confirmBtn").attr("disabled",false);
                    $("#confirmBtn").removeClass("layui-btn-disabled");
                    //如果上传失败
                    if(res.state == 'ok'){

                        $("#contractPath"+dataIndex).val(res.data.src);
                        $("#fileId").val(res.data.id);
                        layer.msg(res.msg,{icon:1,time:5000});
                    }else {
                        return layer.msg('上传失败');
                    }
                    layer.closeAll("loading");
                }
                ,error: function(){
                    //演示失败状态，并实现重传
                    var demoText = $('#demoText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function(){
                        uploadInst.upload();
                    });
                    layer.closeAll("loading");
                }
            });
        })

        renderImg=function(src) {
            var json={
                "title": "图片", //相册标题
                "id": 123, //相册id
                "start": 0, //初始显示的图片序号，默认0
                "data": [   //相册包含的图片，数组格式
                    {
                        "alt": "图片",
                        "src": src, //原图地址
                        "thumb": "" //缩略图地址
                    }
                ]
            };
            layui.layer.photos({
                photos: json
                , anim: 5 //0-6的选择，指定弹出图片动画类型，默认随机（请注意，3.0之前的版本用shift参数）
                ,tab:function () {
                    num=0;
                    $("#layui-layer-photos").parent().append('<div style="position:relative;width:100%;text-align:center;cursor:pointer;">\n' +
                        '\t\t<button id="xuanzhuan" class="layui-btn layui-btn-normal layui-btn-radius"  >旋转图片\t</button>\n' +
                        '\t</div>');
                    $(document).on("click", "#xuanzhuan", function(e) {
                        num = (num+90)%360;
                        $(".layui-layer.layui-layer-page.layui-layer-photos").css('background','black');//旋转之后背景色设置为黑色，不然在旋转长方形图片时会留下白色空白
                        $("#layui-layer-photos").css('transform','rotate('+num+'deg)');

                    });

                    $(document).on("mousewheel DOMMouseScroll", ".layui-layer-phimg", function (e) {
                        var delta = (e.originalEvent.wheelDelta && (e.originalEvent.wheelDelta > 0 ? 1 : -1)) || // chrome & ie
                            (e.originalEvent.detail && (e.originalEvent.detail > 0 ? -1 : 1)); // firefox
                        var imagep = $(".layui-layer-phimg").parent().parent();
                        var image = $(".layui-layer-phimg").parent();
                        var h = image.height();
                        var w = image.width();
                        if (delta > 0) {
                            if (h < (window.innerHeight)) {
                                h = h * 1.05;
                                w = w * 1.05;
                            }
                        } else if (delta < 0) {
                            if (h > 100) {
                                h = h * 0.95;
                                w = w * 0.95;
                            }
                        }
                        imagep.css("top", (window.innerHeight - h) / 2);
                        imagep.css("left", (window.innerWidth - w) / 2);
                        image.height(h);
                        image.width(w);
                        imagep.height(h);
                        imagep.width(w);
                    });
                }
            });
        }
    });
</script>
#end

#define content()

    <form class="layui-form layui-form-pane" style="margin-top: 20px;" id="contractForm">
        <input type="hidden" name="id" value="#(contract.id??)"/>
        <input type="hidden" name="empId" value="#(empId??)"/>
        <div class="layui-form-item" >
            <label class="layui-form-label" style="padding: 8px 5px; "><font color="red">*</font>合同时间类型</label>
            <div class="layui-input-block">
                <select name="dateType" lay-verify="required" lay-filter="dateType" >
                    <option value="">请选择类型</option>
                    <option value="1" #if(contract.dateType??=='1') selected #end>指定时长</option>
                    <option value="2" #if(contract.dateType??=='2') selected #end>长期合同</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="padding: 8px 5px; "><font color="red">*</font>合同开始时间</label>
            <div class="layui-input-block">
                <input type="text" id="contractStartDate" name="contractStartDate" readonly #if(contract==null) value="#date(employee.entryTime??,'yyyy-MM-dd')"  #else value="#date(contract.contractStartDate??,'yyyy-MM-dd')" #end class="layui-input" lay-verify="required"  placeholder="请选择合同签订时间">
            </div>
        </div>
        <div class="layui-form-item" id="contractEndDateDiv" #if(contract.dateType??=='2') style="display: none" #end>
            <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>合同到期时间</label>
            <div class="layui-input-block">
                <input type="text" id="contractEndDate" name="contractEndDate" class="layui-input" #if(contract.dateType??=='2') #else lay-verify="required|checkDateSize" #end  value="#date(contract.contractEndDate??,'yyyy-MM-dd')" placeholder="请选择合同到期时间">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label"><font color="red">*</font>合同类型</label>
            <div class="layui-input-block">
                <select name="contractType" lay-verify="required" >
                    <option value="">请选择类型</option>
                    <option value="1" #if(contract.contractType??=='1') selected #end>劳动合同</option>
                    <option value="2" #if(contract.contractType??=='2') selected #end>劳务合同</option>
                    <option value="3" #if(contract.contractType??=='3') selected #end>临时合同</option>
                    <option value="5" #if(contract.contractType??=='5') selected #end>实习协议</option>
                    <option value="6" #if(contract.contractType??=='6') selected #end>临时工合同</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label" style="padding: 8px 5px; "><font color="red">*</font>合同归属地址</label>
            <div class="layui-input-block">
                <input type="text" id="contractAddr" name="contractAddr" class="layui-input" lay-verify="required" value="#(contract.contractAddr??)" placeholder="请输入合同地址">
            </div>
        </div>
        <div class="layui-form-item" >
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <textarea name="description" placeholder="请输入内容" class="layui-textarea">#(contract.description??)</textarea>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 60px;">
            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px;float: left;">
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                    <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(contract.contractPath1??''!='') onclick="renderImg('#(contract.contractPath1??)')" #end  id="contractImg1" src="#(contract.contractPath1??)">
                        #if(model==null)
                        <p >请上传合同图片一</p>
                        #end
                        <input type="hidden" id="contractPath1" name="contractPath1"   value="#(contract.contractPath1??)">
                    </div>
                </div>
                <button type="button" class="layui-btn contractUploadBtn" data-index="1" id="contractBtn1" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片一</button>
            </div>
            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px;float: left;">
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                    <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(contract.contractPath2??''!='') onclick="renderImg('#(contract.contractPath2??)')" #end  id="contractImg2" src="#(contract.contractPath2??)">
                        #if(model==null)
                        <p >请上传合同图片二</p>
                        #end
                        <input type="hidden" id="contractPath2"  name="contractPath2" value="#(contract.contractPath2??)">
                    </div>
                </div>
                <button type="button" class="layui-btn contractUploadBtn" data-index="2" id="contractBtn2" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片二</button>
            </div>
            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px;float: left;">
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                    <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(contract.contractPath3??''!='') onclick="renderImg('#(contract.contractPath3??)')" #end  id="contractImg3" src="#(contract.contractPath3??)">
                        #if(model==null)
                        <p >请上传合同图片三</p>
                        #end
                        <input type="hidden" id="contractPath3"  name="contractPath3" value="#(contract.contractPath3??)">
                    </div>
                </div>
                <button type="button" class="layui-btn contractUploadBtn" data-index="3" id="contractBtn3" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片三</button>
            </div>
        </div>
        <div class="layui-form-item" style="margin-bottom: 60px;">
            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px;float: left;">
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                    <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(contract.contractPath4??''!='') onclick="renderImg('#(contract.contractPath4??)')" #end  id="contractImg4" src="#(contract.contractPath4??)">
                        #if(model==null)
                        <p >请上传合同图片四</p>
                        #end
                        <input type="hidden" id="contractPath4" name="contractPath4"   value="#(contract.contractPath4??)">
                    </div>
                </div>
                <button type="button" class="layui-btn contractUploadBtn" data-index="4" id="contractBtn4" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片四</button>
            </div>
            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px;float: left;">
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;">
                    <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(contract.contractPath5??''!='') onclick="renderImg('#(contract.contractPath5??)')" #end  id="contractImg5" src="#(contract.contractPath5??)">
                        #if(model==null)
                        <p >请上传合同图片五</p>
                        #end
                        <input type="hidden" id="contractPath5"  name="contractPath5" value="#(contract.contractPath5??)">
                    </div>
                </div>
                <button type="button" class="layui-btn contractUploadBtn" data-index="5" id="contractBtn5" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片五</button>
            </div>
            <div class="layui-inline" style="padding: 10px;width: 300px;height: 190px;float: left;" id="contract6Div" >
                <div class="layui-upload" style="text-align: center;height: 100%;width: 100%;" >
                    <div class="layui-upload-drag" style="margin-left: 0px;height: 75%;width: 100%;">
                        <img class="layui-upload-img"  style="max-height: 100%;max-width: 100%;cursor:pointer;" #if(contract.contractPath6??''!='') onclick="renderImg('#(contract.contractPath6??)')" #end  id="contractImg6" src="#(contract.contractPath6??)">
                        #if(model==null)
                        <p >请上传合同图片六</p>
                        #end
                        <input type="hidden" id="contractPath6"  name="contractPath6" value="#(contract.contractPath6??)">
                    </div>
                </div>
                <button type="button" class="layui-btn contractUploadBtn" data-index="6" id="contractBtn6" style="position:absolute;bottom: -25%;left: 35%;">上传合同图片六</button>
            </div>
        </div>
        <div style="height: 100px;"></div>
        <div class="layui-form-footer" >
            <div class="pull-right">
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
            </div>
        </div>
    </form>
#end
