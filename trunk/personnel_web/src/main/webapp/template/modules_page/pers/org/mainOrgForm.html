#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构编辑页面#end

#define css()
#end

#define content()
<div class="layui-row layui-col-space10">
	<form class="layui-form layui-form-pane" action="">
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>机构名称</label>
			<div class="layui-input-block">
				<input type="text" name="orgName" class="layui-input" lay-verify="required" value="#(model.orgName??)" placeholder="请输入机构名称">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">传真</label>
			<div class="layui-input-block">
				<input type="text" name="fax" class="layui-input" value="#(model.fax??)" placeholder="请输入传真">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">邮箱</label>
			<div class="layui-input-block">
				<input type="text" name="email" class="layui-input" value="#(model.email??)" placeholder="请输入邮箱">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">邮政编码</label>
			<div class="layui-input-block">
				<input type="text" name="postalCode" class="layui-input" value="#(model.postalCode??)" placeholder="请输入邮政编码">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label"><font color="red">*</font>负责人</label>
			<div class="layui-input-block">
				<select id="linkMan" name="linkMan" lay-verify="required" lay-search>
					<option value="">请选择负责人(可搜索)</option>
					#for(user : userList)
					<option value="#(user.id)" #if(user.id==model.linkMan??) selected #end>#(user.name)(#(user.userName))</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">负责人电话</label>
			<div class="layui-input-block">
				<input type="text" name="linkPhone" class="layui-input" value="#(model.linkPhone??)" placeholder="请输入负责人电话">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 140px;padding: 8px 5px;"><font color="red">*</font>上午工作时长(小时)</label>
			<div class="layui-input-block" style="margin-left: 140px">
				<input type="text" name="amWorkHour" lay-verify="required|number" class="layui-input" value="#(model.amWorkHour??)" placeholder="请输入上午工作时长">
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label" style="width: 140px;padding: 8px 5px;"><font color="red">*</font>下午工作时长(小时)</label>
			<div class="layui-input-block" style="margin-left: 140px">
				<input type="text" name="pmWorkHour" lay-verify="required|number" class="layui-input" value="#(model.pmWorkHour??)" placeholder="请输入下午工作时长">
			</div>
		</div>


		<div class="layui-form-item">
			<label class="layui-form-label">地区</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="provinceId" lay-filter="province" lay-search="">
					<option value="">请选择省</option> 
					#areaOption("1", model.provinceId??'')
				</select>
			</div>
			<div class="layui-input-inline" style="width:100px;">
				<div class="layui-form" lay-filter="citySelectFilter">
					<select id="cityId" name="cityId" lay-filter="city" lay-search="">
						<option value="">请选择县/市</option> 
						#areaOption(model.provinceId??'', model.cityId??'')
					</select>
				</div>
			</div>
			<div class="layui-input-inline" style="width:100px;">
				<div class="layui-form" lay-filter="townSelectFilter">
					<select id="townId" name="townId" lay-filter="town" lay-search="">
						<option value="">请选择镇/区</option> 
						#areaOption(model.cityId??'', model.townId??'')
					</select>
				</div>
			</div>
			<div id="streetDiv" class="layui-input-inline" style="width:100px;">
				#if(model.streetId)
				<div class="layui-form" lay-filter="streetSelectFilter">
					<select id="streetId" name="streetId" lay-search="">
						<option value="">请选择街道</option> 
						#areaOption(model.townId??'', model.streetId??'')
					</select>
				</div>
				#end
			</div>
		</div>
		<div class="layui-form-item">
			<label class="layui-form-label">详细地址</label>
			<div class="layui-input-block">
				<input type="text" name="address" class="layui-input" value="#(model.address??)" placeholder="请输入详细地址">
			</div>
		</div>
		<div class="layui-form-footer">
			<div class="pull-left">
				<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
			</div>
			<div class="pull-right">
				<input type="hidden" name="id" value="#(model.Id??)"> 
				<input type="hidden" name="parentId" value="#(model.parentId??'0')">
				<input type="hidden" name="orgType" value="#(model.orgType??'main_org')">
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
			</div>
		</div>
	</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	, $ = layui.jquery
	;
	//省下拉选择监听
	form.on('select(province)', function(data) {
		$("#cityId").empty();
		$("#cityId").prepend("<option value=''>请选择县/市</option>");
		$("#townId").empty();
		$("#townId").prepend("<option value=''>请选择镇/区</option>");
		form.render('select', 'townSelectFilter');
		$("#streetDiv").empty();
		var provinceId = data.value;
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:provinceId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						$.each(rep.resultData, function(i, data){
							$("#cityId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
						});
						form.render('select', 'citySelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
	//县/市下拉选择监听
	form.on('select(city)', function(data) {
		$("#townId").empty();
		$("#townId").prepend("<option value=''>请选择镇/区</option>");
		form.render('select', 'townSelectFilter');
		var cityId = data.value;
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:cityId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						$.each(rep.resultData, function(i, data){
							$("#townId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
						});
						form.render('select', 'townSelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
	//镇/区下拉选择监听
	form.on('select(town)', function(data) {
		var townId = data.value;
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/area/getAreaByParentId',
		    data: {parentId:townId},
		    notice: false,
		    success : function(rep){
		    	if (rep.state=='ok') {
					if(rep.resultData.length>0){
						var html='<div class="layui-form" lay-filter="streetSelectFilter">';
						html += '<select id="streetId" name="streetId" lay-search="">';
						html += '<option value="">请选择街道</option>';
						html += '</select>';
						html += '</div>';
						$("#streetDiv").html(html);
						if(!$('#streetDiv').is(":empty")){
							$.each(rep.resultData, function(i, data){
								$("#streetId").append("<option value='"+data.id+"'>"+data.areaName+"</option>");
							});
						}
						form.render('select', 'streetSelectFilter');
					}
				} else {
					layer.msg(rep.msg, {icon : 5});
				}
		    },
		    complete : function() {
		    }
		});
	});
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
		    type: 'POST',
		    url: '#(ctxPath)/persOrg/save',
		    data: $(formObj.form).serialize(),
		    notice: true,
		    loadFlag: true,
		    success : function(rep){
		    	if(rep.state=='ok'){
			    	parent.tableGridReload();
			    	pop_close();
		    	}
		    },
		    complete : function() {
		    }
		});
		return false;
	});
});
</script>
#end
