#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()我的下属页面#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
<style>
	.layui-table-cell{
		padding: 0 5px;
	}
	.layui-form-checkbox{
		margin: auto!important;
	}

</style>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
		<div class="layui-col-xs12 layui-col-sm12 layui-col-md12" id="div">
			<div class="layui-row" id="div2">
				<form class="layui-form layui-form-pane" method="" action="">
					<div class="layui-row">
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<label style="margin-left: 10px;">组织架构：</label>
								<div class="layui-inline"  style="width: 220px;">
									<div id="deptSelect" style="margin: 5px 10px;">

									</div>
								</div>
							</div>
						</div>
						<div class="layui-input-inline">
							<div class="layui-input-inline" style="width:120px;">
								<input type="checkbox" id="isContainChildren" name="isContainChildren" title="包含子部门" value="1" lay-skin="primary" checked>
							</div>
						</div>
						<div class="layui-input-inline">
							<span class="layui-form-label" style="width:78px;">姓名</span>
							<div class="layui-input-inline" style="width: 120px;">
								<input type="text" id="fullName" class="layui-input" placeholder="请输入姓名" autocomplete="off">
							</div>
						</div>
						<div class="layui-input-inline">
							<label class="layui-form-label">性别</label>
							<div class="layui-input-inline" style="width:120px;">
								<select name="sex" id="sex" lay-verify="required" lay-filter="">
									<option value="">请选择性别</option>
									#dictOption("sex", model.sex??'', "")
								</select>
							</div>
						</div>
						<div class="layui-input-inline">
							<span class="layui-form-label" style="padding: 8px 5px;">证件号码</span>
							<div class="layui-input-inline">
								<input type="text" id="idCard" class="layui-input" placeholder="请输入证件号码" autocomplete="off">
							</div>
						</div>
						<div class="layui-input-inline">
							<span class="layui-form-label" style="padding: 8px 5px;">手机号码</span>
							<div class="layui-input-inline" style="width: 132px;">
								<input type="text" id="phoneNum" class="layui-input" placeholder="请输入手机号码" autocomplete="off">
							</div>
						</div>
						<div class="layui-input-inline">
							<span class="layui-form-label" style="width:78px;">职位</span>
							<div class="layui-input-inline">
								<input type="text" id="position" name="position" class="layui-input" placeholder="请输入职位" autocomplete="off">
							</div>
						</div>
						<div class="layui-input-inline" >
							<span class="layui-form-label" style="width:78px;">状态</span>
							<div class="layui-input-inline" style="width:100px;">
								<select name="archiveStatus" id="archiveStatus">
									#dictOption("archive_status", "incumbency", "")
								</select>
							</div>
						</div>
						<div class="layui-input-inline" >
							<span class="layui-form-label" style="width:78px;">排序</span>
							<div class="layui-input-inline" style="width:160px;">
								<select name="sort" id="sort">
									<option value="">请选择排序</option>
									<option value="0">姓名首字母升序</option>
									<option value="1">姓名首字母降序</option>
									<option value="2">入职日期升序</option>
									<option value="3">入职日期降序</option>
									<option value="4">合同到期时间升序</option>
								</select>
							</div>
						</div>
						<div class="layui-btn-group" style="float: right;margin-right: 20px;">
							<button type="button" id="btn-search" class="layui-btn mgl-20">查询</button>
							<button type="reset" id="reset" class="layui-btn layui-btn-primary btn-reset">重置</button>
<!-- 							<button id="btn-refresh" type="button" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></button> -->
						</div>
					</div>
				</form>
			</div>
			<div class="layui-row">
				<table id="employeeTable" lay-filter="employeeTable"></table>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">
<div class="layui-btn-group">
	#shiroHasPermission("emp:subordinate:viewBtn")
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
	#end
</div>
</script>

<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.extend({
	dtree: 'layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
}).use(['dtree','layer','jquery'], function(){
	var dtree = layui.dtree, layer = layui.layer, $ = layui.jquery;

	var DemoTree;

	/*$.post('#(ctxPath)/persOrg/orgDtree',{},function (r) {
		var jsonArray=[];
		$.each(r.data,function (index,item) {
			var json={"id":item.id,"title":item.name,"checkArr":"0","parentId":item.pId};
			var flag=isUpdateParentId(json.parentId,r.data);
			if(flag){
				json.parentId='0';
			}
			jsonArray[index]=json;
		});
		// 初始化树
		DemoTree = dtree.renderSelect({
			elem: "#deptId",
			dataFormat: "list",
			data:jsonArray,
			initLevel:10,
			menubar:{  //菜单栏
				search:""  //搜索
			},
			selectCardHeight:"500"
			//url: "#(ctxPath)/persOrg/orgDtree" // 使用url加载（可与data加载同时存在）
		});
	});*/


	function isUpdateParentId(pid,array){
		var flag=true;
		$.each(array,function (i,it) {
			if(pid==it.id){
				flag=false;
				return false;
			}
		});
		return flag;
	}

	/*$("#reset").on('click',function () {
		$(".layui-select-title input[dtree-id='deptId']").val('');
		DemoTree.cancelNavThis();
	})*/

	// 绑定节点点击
	/*dtree.on("node('deptId')" ,function(obj){

	});*/
});

layui.use(['table', 'vip_table','form','upload'], function() {
	// 操作对象
	var table = layui.table
    , layer = layui.layer
    , vipTable = layui.vip_table
    , $ = layui.$
    , tableId = 'employeeTable'
	, form=layui.form
	, upload=layui.upload
    ;

	loadTable();

	var deptSelect = xmSelect.render({
		el: '#deptSelect',
		autoRow: true,
		height: '200px',
		prop: {
			name: 'name',
			value: 'id',
		},
		radio: true,
		filterable: true,//搜索
		tree: {
			show: true,
			expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
			showFolderIcon: true,
			showLine: true,
			indent: 15,
			lazy: true,
			clickExpand: true,
			clickClose: true,
			strict: false,
			//点击节点是否选中
			clickCheck: true,
			load: function(item, cb){

			}
		},
		height: 'auto',
		data(){
			return [];
		}
	})
	$.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
		deptSelect.update({
			data:res
		})
	});
	
	// 表格加载渲染
	function loadTable() {
		layer.load();
		var tableObj = table.render({
			id : tableId
			,elem : '#'+tableId
			,method : 'POST'
			,url : '#(ctxPath)/persOrgEmployee/pageTable'
			,height: 'full-134'    //容器高度
			,where : {orgParentIds:$("#orgId").val(),"archiveStatus":"incumbency"}//额外查询条件
			,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
			,cols : [[
				{type:'numbers', title:'序号', width:60, align:'center'}
				,{field:'workNum', title:'工号',width:85, align:'center'}
				,{field:'fullName', title:'姓名',width:85, align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
				,{field:'', title:'部门',minWidth:150, align:'center',templet:function (d) {
						if(d.org==undefined || d.org.orgName==undefined){
							return '';
						}else{
							//return '<span title="'+d.org.orgName+'" >'+d.org.orgName+'</span>';
							return d.org.orgName;
						}
					}}
				,{field:'', title:'职位',width:130, align:'center',templet:function (d) {
						if(d.org==undefined || d.org.positionName==undefined){
							return '';
						}else{
							return d.org.positionName;
						}
					}}
				,{field:'', title:'角色',width:130, align:'center',templet:function (d) {
						if(d.org==undefined || d.org.roleName==undefined){
							return '';
						}else{
							return d.org.roleName;
						}
					}}
				,{field:'', title:'性别', width:60, align:'center', templet:'#dictTpl("sex", "sex")'}
				,{field:'', title:'生日', width:55, align:'center',templet:function (d) {
						if(typeof(d.birthday)==='undefined'){
							return ''
						}else{
							return d.birthday.substring(5,d.birthday.length);
						}

					}}
				,{field:'', title:'民族', width:100, align:'center', templet:'#dictTpl("nation", "nationality")'}
				,{field:'phoneNum', title:'手机号', width:100, align:'center'}
				/*,{field:'extNum', title:'分机号', width:120, align:'center'}*/
				,{field:'', title:'入职日期', width:90, align:'center',templet:"<div>{{ dateFormat(d.entryTime,'yyyy-MM-dd') }}</div>"}
				,{field:'', title:'状态', width:60, align:'center', templet:'#dictTpl("archive_status", "archiveStatus")'}
				,{field:'', title:'剩余年休假', width:80, align:'center', templet:function (d) {
						if(typeof d.annualLeaveDays=="undefined"){
							return "0天";
						}else{
							return d.annualLeaveDays+"天";
						}
					}}
				/*,{field:'', title:'重新入职', width:68, align:'center', templet:function (d) {
						if(d.isReemployment=='1'){
							return '是';
						}else if(d.isReemployment=='0'){
							return '否';
						}else{
							return '- -';
						}
					}}*/
				,{field:'', title:'社保', width:60, align:'center', templet:function (d) {
						if(d.socialStatus=='1'){
							return '已缴';
						}else if(d.socialStatus=='2'){
							return '未缴';
						}else if(d.socialStatus=='3'){
							return '放弃'
						}else{
							return '- -';
						}
					}}
				,{field:'contractEndDate', title:'合同到期时间', width:100, align:'center'}
				,{title:'操作', fixed:'right', width:60, align:'center', toolbar:'#empTableBar'}

			]]
			,page : true
			,done:function () {
				//
				var layerTips;
				$("td").on("mouseenter", function() {
					//js主要利用offsetWidth和scrollWidth判断是否溢出。
					//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
					if (this.offsetWidth < this.firstChild.scrollWidth) {
						var that = this;
						var text = $(this).text();
						layerTips=layer.tips(text, that, {
							tips: 1,
							time: 0
						});
					}
				});
				$("td").on("mouseleave", function() {
					//js主要利用offsetWidth和scrollWidth判断是否溢出。
					//在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
					layer.close(layerTips);
				});
				layer.closeAll('loading');
			}
		});
	}
	//监听工具条
	table.on('tool('+tableId+')', function(obj) {
		if (obj.event === 'view') {
			pop_show('查看', '#(ctxPath)/persOrgEmployee/view?id='+obj.data.id, '900', '');
		}
	});
	//重载表格，跳转到第一页
    pageTableReload = function () {
    	form.render('checkbox');
		layer.load();
		var isContainChildren="0";
		if("1"==$("input:checkbox[name='isContainChildren']:checked").val()){
			isContainChildren="1";
		}
		var id="";
		$.each(deptSelect.getValue(),function (index,item) {
			id=item.id;
		});
    	tableReload(tableId,{isContainChildren:isContainChildren,phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
			'deptId':id,"sort":$("#sort").val(),'position':$("#position").val(),'sex':$("#sex").val()});
    }

    //重载表格，跳转当前页面
	pageTableReloadCurrPage = function(){
		layer.load();
    	var currPage=$(".layui-laypage-skip").children("input").val();
		var id="";
		$.each(deptSelect.getValue(),function (index,item) {
			id=item.id;
		});
    	table.reload(tableId,{'where':{phoneNum:$('#phoneNum').val(),orgParentIds:$('#orgId').val(), fullName:$('#fullName').val(),archiveStatus:$("#archiveStatus").val(),idCard:$("#idCard").val(),
				deptId:id,"sort":$("#sort").val(),'position':$("#position").val(),'sex':$("#sex").val()},page:{'curr':currPage}});
	}

	//搜索按钮点击事件
	$('#btn-search').on('click', function() {
		pageTableReload();
	});
	// 刷新
    $('#btn-refresh').on('click', function () {
    	pageTableReload();
    });
});
</script>
#end