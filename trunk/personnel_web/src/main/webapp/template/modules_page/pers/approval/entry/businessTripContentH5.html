#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }

</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form','laytpl','layer'], function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var laydate=layui.laydate;



        /*setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);*/

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })
        $.post('#(ctxPath)/persOrg/orgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            });
            #if(dispatchApply!=null)
            deptSelect.setValue(["#(dispatchApply.dispatchDeptId??)"]);
            #end
        });



        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&dispatchDeptId="+dispatchDeptId;

            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {

            var dispatchDeptId=deptSelect.getValue()[0].id;
            if(dispatchDeptId==null || dispatchDeptId==''){
                layer.msg('请选择外派单位', {icon: 2, offset: 'auto'});
                return false;
            }
            var data=$("#noticeForm").serialize()+"&saveType=2&dispatchDeptId="+dispatchDeptId;
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeDispatchApply',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        $("#exportBtn").on('click',function () {
            window.location.href='#(ctxPath)/pers/approval/exportBusinessTripExcel?id='+$("#id").val();
        })

    });
</script>
#end

#define content()
<body class="v-theme">

    <div class="layui-row" style="padding: 10px 20px;">
        <form class="layui-form layui-form-pane" action="" id="formId"  >
            #if(isEnd)
            <div class="layui-form-item">
              <button class="layui-btn" type="button" id="exportBtn" style="margin-bottom: 10px;float: right;">导出出差单</button>
            </div>
            #end
            <!--<div class="layui-form-item">
                <div class="layui-input-inline">
                    <button class="layui-btn" type="button" id="exportBtn">导出文件</button>

                </div>
            </div>-->

            #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
            <fieldset class="layui-elem-field layui-field-title" style="display:block;">
                <legend>流程提交人信息</legend>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交人姓名</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交人部门</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交人职位</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交时间</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">任务编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </fieldset>
            #end
            <fieldset class="layui-elem-field layui-field-title" style="display:block;">
                <legend>出差单信息</legend>
            </fieldset>

            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>出差员工</label>
                <div class="layui-input-inline">
                    <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                    <input id="id" name="id" type="hidden" value="#(model.id??)">
                    <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                    <input id="createBy" name="createBy" type="hidden" value="#if(model==null)#(createBy??)#else#(model.createBy??)#end">
                    <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(model!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                </div>

            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>所在部门</label>
                <div class="layui-input-inline">
                    <input type="text" name="startTime"  style="font-size: 10px;"  readonly required value="#(deptNames??)"    placeholder="" autocomplete="off" class="layui-input">

                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">职位</label>
                <div class="layui-input-inline">
                    <input type="text" name="startTime"    readonly required value="#(positionName??)"    placeholder="" autocomplete="off" class="layui-input">

                </div>
            </div>

           <!-- <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>目的地类型</label>
                <div class="layui-input-inline">
                    <select id="destinationType" name="destinationType" lay-filter="destinationType" lay-verify="required" disabled readonly>
                        <option value="1" #if(model.destination_type??=='1') selected #end>公司单位</option>
                        <option value="2" #if(model.destination_type??=='2') selected #end>非公司单位</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item" id="deptDiv" #if(model.destinationType??=='2') style="display: none;" #else style="display: block;"  #end>
                <label class="layui-form-label"><font color="red">*</font>出差单位</label>
                <div class="layui-input-inline" >
                    <input type="text" name="startTime"  style="font-size: 10px;"  readonly required value="#(model.destinationDeptName??)"    placeholder="" autocomplete="off" class="layui-input">

                </div>
            </div>
-->
            #if(isNew)
                <div class="layui-form-item">
                    <label class="layui-form-label" style="padding: 8px 5px;">出差时长</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(newRecord.days??)"  autocomplete="off" class="layui-input">
                    </div>
                </div>
                #for(dateItem : newRecord.dateList??)
                <fieldset class="layui-elem-field layui-field-title" style="display:block;">
                    <legend>行程#(for.index+1)</legend>
                    <div class="layui-form-item">
                        <label class="layui-form-label">城市</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" readonly  value="#(dateItem.destinationCityName??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">交通方式</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" readonly  value="#(dateItem.trafficTypeLabel??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;">出差时长类型</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" readonly  value="#if(dateItem.dateType??=='1')按天数(全天)#elseif(dateItem.dateType??=='2')按小时#end" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label layui-form-text">出差时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" #if(dateItem.leaveRestDateType??=='2') style="font-size: 11px;padding-left: 0px;" #end readonly  value="#(dateItem.startTime??)至#(dateItem.endTime??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                </fieldset>
                #end
            #else
            <div class="layui-form-item" id="deptDiv2" >
                <label class="layui-form-label"><font color="red">*</font>出差城市</label>
                <div class="layui-input-inline"  >
                    <input type="text" name="startTime"    readonly required value="#(model.destination_city_name??)"    placeholder="" autocomplete="off" class="layui-input">

                </div>

            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>交通工具</label>
                <div class="layui-input-inline">
                    <select name="trafficType" lay-filter="trafficType" lay-verify="required"  readonly>
                        <option value="">请选择交通工具</option>
                        #for(trafficType : trafficTypeList)
                        <option value="#(trafficType.key)" #if(trafficType.key==model.traffic_type??) selected #end>#(trafficType.value)</option>
                        #end

                    </select>
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label"><font color="red"></font>出行描述</label>
                <div class="layui-input-block">
                            <textarea name="tripRemark" id="tripRemark" placeholder="请输入内容"  style="height: 60px;"
                                      lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end lay-verify="required" class="layui-textarea">#(model.trip_remark??)</textarea>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差开始时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="startTime" id="startTime"  readonly required value="#date(model.start_time??,'yyyy-MM-dd') #(record.startDate??)"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                </div>

            </div>

            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>出差结束时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="endTime" id="endTime"  readonly required value="#date(model.end_time??,'yyyy-MM-dd') #(record.endDate??)"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                </div>

            </div>
            #end

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label"><font color="red">*</font>出差事由</label>
                <div class="layui-input-block">
                    <textarea name="reason" id="reason" placeholder="请输入内容" lay-verify=""  readonly lay-verify="required" class="layui-textarea">#(model.reason??)</textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text" style="margin-bottom: 60px;">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify=""  readonly  class="layui-textarea">#(model.remark??)</textarea>
                </div>
            </div>

        </form>
    </div>

</body>
#end