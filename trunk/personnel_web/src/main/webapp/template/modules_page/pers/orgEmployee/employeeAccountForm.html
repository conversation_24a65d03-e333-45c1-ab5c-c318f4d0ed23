#include("/template/common/layout/_part_layout.html")
#@part()

#define content()
<div class="layui-row">
	<div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
		<form class="layui-form layui-form-pane" action="">
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>银行</label>
				<div class="layui-input-block">
					<select name="bankId" lay-verify="required" lay-search="">
						#for(bank : bankList)
							<option value="#(bank.id)">#(bank.bankName)</option>
						#end
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>开户支行</label>
				<div class="layui-input-block">
					<input type="text" name="subBranch" class="layui-input" lay-verify="required" value="#(model.subBranch??)" placeholder="请输入开户支行">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>账户类型</label>
				<div class="layui-input-block">
					<select name="accountType" lay-verify="required">
						<option value="">请选择账户类型</option>
						#dictOption("account_type", model.accountType??'', "")
					</select>
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>银行账号</label>
				<div class="layui-input-block">
					<input type="text" name="bankAccount" class="layui-input" lay-verify="required" value="#(model.bankAccount??)" placeholder="请输入银行账号">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>持卡人姓名</label>
				<div class="layui-input-block">
					<input type="text" name="holderName" class="layui-input" lay-verify="required" value="#(model.holderName??)" placeholder="请输入持卡人姓名">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>预留手机号</label>
				<div class="layui-input-block">
					<input type="text" name="stayPhoneNum" class="layui-input" lay-verify="required" value="#(model.stayPhoneNum??)" placeholder="请输入预留手机号">
				</div>
			</div>
			<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>是否默认</label>
				<div class="layui-input-block">
					<input type="radio" name="isDefault" value="0" title="否" #if(model==null || model.isDefault??=='0') checked #end>
					<input type="radio" name="isDefault" value="1" title="是" #if(model.isDefault??=='1') checked #end>
				</div>
			</div>
			<div style="margin-bottom:60px;"></div>
			<div class="layui-form-footer">
				<div class="pull-left">
					<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
				</div>
				<div class="pull-right">
					<input type="hidden" name="id" value="#(model.Id??'')">
					<input type="hidden" name="empId" value="#(model.empId??'')">
					<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
					<button class="layui-btn layui-btn-danger" onclick="closeEditTab();">关&nbsp;&nbsp;闭</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, layer = layui.layer
	, $ = layui.$
	;
	
	form.render();
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/persOrgEmployee/saveEmpAccount',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		closeEditTab();
            		pageTableReload();
            		parent.pageTableReload();
		    	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end