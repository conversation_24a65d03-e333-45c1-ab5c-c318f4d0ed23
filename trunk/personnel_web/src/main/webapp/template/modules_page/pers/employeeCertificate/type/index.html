#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        noticeTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            noticeTypeLoad(data.field);
            return false;
        });

        reloadTab = function(){
            $("#search").click();
        }

        function noticeTypeLoad(data){
            table.render({
                id : 'noticeTypeTable'
                ,elem : '#noticeTypeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/pers/employeeCertificate/typePageList'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '证件类型名称', align: 'center', unresize: true}
                    ,{field:'isEffectiveDate', title: '有无有效日期', align: 'center', unresize: true,templet:"<div>{{d.isEffectiveDate=='1'?'有':'无'}}</div>"}
                    ,{field:'isEntryRequired', title: '入职是否必要', align: 'center', unresize: true,templet:"<div>{{d.isEntryRequired=='1'?'必要':'非必要'}}</div>"}
                    ,{field:'fileCount', title: '附件数量', sort: false, align: 'center', unresize: true}
                    ,{field:'isEnable', title: '是否可用', align: 'center', unresize: true,templet:"<div>{{d.isEnable=='1'?'可用':'不可用'}}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/pers/employeeCertificate/typeForm" ;
            pop_show("新增证件类型",url,500,400);
        });

        noticeTypeTableReload=function(){
            $("#search").click();
        }
        table.on('tool(noticeTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/noticeType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                noticeTypeTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/pers/employeeCertificate/typeForm?id=" + obj.data.id ;
                pop_show("编辑证件类型",url,500,400);
            }
        });

    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("notice:type:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("notice:type:voidBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            类型名称:
            <div class="layui-inline">
                <input id="name" name="name" autocomplete="off" class="layui-input">
            </div>
            &nbsp;&nbsp;
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" id="search" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("notice:type:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table class="layui-table" id="noticeTypeTable" lay-filter="noticeTypeTable"></table>
</div>
#end