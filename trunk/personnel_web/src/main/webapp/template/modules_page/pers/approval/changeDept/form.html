#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript">
    layui.extend({
        treeSelect: '/static/js/extend/treeSelect'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['form','laytpl','table','element','layer','laydate','treeSelect'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;
        var laydate=layui.laydate;
        var treeSelect=layui.treeSelect;

        laydate.render({
            elem : '#changeDate',
            type: 'date',
            trigger: 'click'
        });

        setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                getDept();
            }
        },300);

        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#oldDeptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#oldPosition").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(oldDeptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })

        treeSelect.render({
            // 选择器
            elem: "#deptId",
            // 数据
            data: '#(ctxPath)/persOrg/orgXmSelectTree',
            // 异步加载方式：get/post，默认get
            type: 'get',
            // 占位符
            placeholder: '请选择部门',
            // 是否开启搜索功能：true/false，默认false
            search: true,
            // 点击回调
            click: function(d){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/pers/position/getPositionByOrgId',
                    data: {'orgId':d.current.id},
                    notice: false,
                    loadFlag: false,
                    success : function(rep){
                        if(rep.state==='ok'){
                            var str='<option value="" role-id="">请选择职位</option>';
                            var data=rep.data;
                            $.each(data,function (index,item) {
                                str+='<option value="'+item.id+'" role-id="'+item.roleId+'" role-name="'+item.roleName+'">'+item.positionName+'</option>';
                            });
                            $("#position").html(str);
                            form.render('select');
                        }
                    },
                    complete : function() {
                    }
                });
            },
            // 加载完成后的回调函数
            success: function (d) {
                if('#(employeeChange.deptId??)'!=''){
                    treeSelect.checkNode('tree', '#(employeeChange.deptId??)');
                    form.render('select');
                }

            }
        });

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            $.ajax({
                type:'post',
                data: $("#noticeForm").serialize(),
                url: '#(ctxPath)/api/saveEmployeeChangeDept',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {
            var data=$("#noticeForm").serialize()+"&saveType=2";
            //提交表单数据
            /*util.sendAjax ({
                type: 'POST',
                url: '',
                data: data,
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.code=='0'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });*/
            console.log(data);
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeChangeDept',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-row">
    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">
    <div class="layui-col-md6 layui-form" id="content" style="padding-right: 10px;">
        <div class="layui-collapse" >

            <div class="layui-row" style="padding: 10px 20px;">

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>调岗员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(employeeChange.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(employeeChange==null)#(createBy??)#else#(employeeChange.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(employeeChange!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>

                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>原部门</label>
                        <div class="layui-input-inline">
                            <select id="oldDeptId" name="oldDeptId" lay-filter="oldDeptId" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==employeeChange.oldDeptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">原职位</label>
                        <div class="layui-input-inline">
                            <select id="oldPosition" name="oldPosition" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==employeeChange.oldPosition??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>新部门</label>
                        <div class="layui-input-inline">
                            <input type="text" name="deptId" id="deptId" lay-verify="required" lay-filter="tree" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">新职位</label>
                        <div class="layui-input-inline">
                            <select id="position" name="position" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择职位</option>
                                #for(position : newPositionList)
                                <option value="#(position.id??)" #if(position.id??==employeeChange.position??) selected #end>#(position.position_name??)</option>
                                #end
                            </select>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>调动类型</label>
                        <div class="layui-input-inline">
                            <select id="changeType" name="changeType" lay-verify="required" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                                <option value="">请选择调动类型</option>
                                <option value="personal" #if(employeeChange.changeType??=='personal') selected #end>个人申请</option>
                                <option value="internal" #if(employeeChange.changeType??=='internal') selected #end>内部调动</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>调动时间</label>
                        <div class="layui-input-inline">
                            <input type="text" name="changeDate" id="changeDate" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#date(employeeChange.changeDate??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入调动时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">原因</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(employeeChange.remark??)</textarea>
                        </div>
                    </div>

            </div>
        </div>
    </div>
    <div class="layui-col-md6">
        #if(stepts.size()??0>0)
        <table class="layui-table" id="steptsTable">
            <!--<colgroup>
                <col width="10%">
                <col width="13%">
                <col width="30%">
                <col width="30%">
                <col width="17%">
            </colgroup>-->
            <thead>
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">节点</th>
                <th style="text-align: center;">流程</th>
                <th style="text-align: center;">意见</th>
                <th style="text-align: center;">操作时间</th>
                <th style="text-align: center;">操作人</th>
            </tr>
            </thead>
            <tbody>
            #for(stept:stepts)
            <tr>
                <td align="center">#(for.index+1)</td>
                <td>#(stept.ActivityName)</td>
                <td align="center">
                    #if(stept.StepState==3)
                    提交
                    #else if(stept.StepState==1)
                    待处理
                    #else if(stept.StepState==0)
                    等待
                    #else if(stept.StepState==2)
                    正处理
                    #else if(stept.StepState==4)
                    撤回
                    #else if(stept.StepState==5)
                    批准
                    #else if(stept.StepState==6)
                    拒绝
                    #else if(stept.StepState==7)
                    转移
                    #else if(stept.StepState==8)
                    失败
                    #else if(stept.StepState==9)
                    跳过
                    #end
                </td>
                <td>#(stept.Comment)</td>
                <td>#(stept.CommentTime)</td>
                <td align="center">#(stept.CommentUserName)</td>
            </tr>
            #end
            </tbody>
        </table>

        <form class="layui-form layui-form-pane" id="taskForm">
            <!--<div class="layui-form-item layui-form-text">
                <label class="layui-form-label">意见</label>
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="required" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                </div>
            </div>-->
            <div class="layui-form-item">
                <div class="layui-input-block pull-right">

                    <!--<button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort || !isSaveHandle) disabled style="display: none;" #end >中止</button>
                    <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit || !isSaveHandle) disabled style="display: none;" #end>提交</button>
                    <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject || !isSaveHandle) disabled  style="display: none;" #end>拒绝</button>
                    <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove || !isSaveHandle) disabled style="display: none;" #end>通过</button>-->
                </div>
            </div>
        </form>
        #end
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            #end
            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" lay-submit="" lay-filter="saveSubmitBtn">保存并提交</button>
            #end
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
        <div class="pull-right">
            <div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>
        </div>

    </div>
    </form>
</div>

</body>
#end