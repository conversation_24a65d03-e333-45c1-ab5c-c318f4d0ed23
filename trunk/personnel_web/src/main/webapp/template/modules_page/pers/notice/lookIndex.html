#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告查阅页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
        	<div class="layui-inline">
            	<label class="layui-form-label">公告类型</label>
				<div class="layui-input-inline">
	                <select id="noticeTypeId" name="noticeTypeId">
	                    <option value="">请选择公告类型</option>
	                    #for(t : typeList)
	                    	<option value="#(t.id)">#(t.typeName)</option>
	                    #end
	                </select>
                </div>
            </div>
            <div class="layui-inline">
				<label class="layui-form-label">公告标题</label>
				<div class="layui-input-inline">
					<input id="noticeTitle" name="noticeTitle" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
	            <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			</div>
        </form>
    </div>
    <div class="layui-row">
    	<table class="layui-table" id="noticeTable" lay-filter="noticeTable"></table>
    </div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
	#shiroHasPermission("notice:query:lookBtn")
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
	#end
</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        function noticeLoad(data){
            table.render({
                id : 'noticeTable'
                ,elem : '#noticeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/pers/notice/noticeLook'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'typeName', title: '公告类型', align: 'center', unresize: true}
                    ,{field:'noticeTitle', title: '公告标题', align: 'center', unresize: true}
//                     ,{field:'zhiding', title: '置顶', align: 'center', unresize: true}
                    ,{field:'releaseTime', title: '发布时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.releaseTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        table.on('tool(noticeTable)',function(obj){
            if(obj.event === 'detail'){
//                 util.sendAjax ({
//                     type: 'POST',
//                     url: '#(ctxPath)/pers/notice/isPowerForLook',
//                     data: {'id':obj.data.id},
//                     notice:false,
//                     loadFlag: true,
//                     success : function(rep){
//                         if(rep.state==='ok'){
//                             var url = "#(ctxPath)/pers/notice/look?id=" + obj.data.id;
//                             pop_show("查阅公告",url,1050,680);
//                         }else{
//                             layer.msg(rep.msg, {icon: 5, offset: 'auto'});
//                         }
//                     },
//                     complete : function() {
//                     }
//                 });
            	var url = "#(ctxPath)/pers/notice/look?id=" + obj.data.id;
                pop_show("查阅公告",url,1050,680);
            }
        });
        
        noticeLoad({releaseStatus:"release"});

        form.on("submit(search)",function(data){
            noticeLoad(data.field);
            return false;
        });

    });
</script>
#end