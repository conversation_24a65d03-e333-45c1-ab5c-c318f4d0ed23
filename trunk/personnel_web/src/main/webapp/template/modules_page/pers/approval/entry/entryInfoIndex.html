#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <div class="layui-input-inline">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-inline" style="float: left;" >
                    <input type="text" name="employeeName" id="employeeName" value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">入职时间</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <button class="layui-btn" id="query" type="button" >查询</button>
            #shiroHasPermission("pers:employeeEntry:addBtn")
            <button class="layui-btn" id="addBtn" type="button" >添加</button>
            #end

            #shiroHasPermission("emp:employeeEntry:reportForm")
            <button class="layui-btn" id="reportForm" type="button" >入职报表</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    #shiroHasPermission("pers:employeeEntry:editBtn")
    #[[
    {{#if(d.isSaveHandle || d.taskId==undefined){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("pers:employeeEntry:editBtn")
    #[[
    {{#if(!d.isSaveHandle && d.taskId!=undefined){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end


</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/entryInfoForm';
            pop_show("添加入职信息",url,'100%','100%');
        });

        $("#reportForm").on('click',function (){
            var url='#(ctxPath)/pers/approval/entryInfoReportForm';
            pop_show("入职报表",url,'100%','100%');
        })




        $("#query").on('click',function (obj) {
            reloadTable();
        })

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"employeeName":$("#employeeName").val(),'date':$("#date").val(),'deptId':id});
        }

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/entryInfoPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'workNum',title:'工号',width: 150, align: 'center', unresize: true}
                    ,{field:'employeeName',title:'姓名', align: 'center', unresize: true}
                    ,{field:'gender', title: '性别',width: 120, align: 'center', unresize: true,templet:function (d) {
                            if(d.gender==='male'){
                                return '男';
                            }else if(d.gender==='female'){
                                return '女';
                            }else{
                                return '未知';
                            }
                        }}
                    ,{field:'deptName',title:'部门', align: 'center', unresize: true}
                    ,{field:'positionName',title:'职位', align: 'center', unresize: true}
                    ,{field:'saveDate', title: '创建时间',align: 'center', unresize: true,width:180,templet:"<div>{{dateFormat(d.saveDate,'yyyy-MM-dd')}}</div>"}
                    ,{field:'taskStatus', title: '流程状态',align: 'center', unresize: true,width:180,templet:function (d) {
                        if(d.taskId==undefined){
                            return '未开始';
                        }else{
                            if(d.taskState=='1'){
                                return '已结束';
                            }else if(d.taskState=='4') {
                                return '已中止';
                            }else if(d.taskState=='2'){
                                return '已中止';
                            }else{
                                return '处理中';
                            }
                        }

                        }}
                    ,{field:'currentStepName', title: '当前步骤',align: 'center', unresize: true,width:180}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var taskId='';
                    if(obj.data.taskId!=undefined){
                        taskId=obj.data.taskId;
                    }
                    var url='#(ctxPath)/pers/approval/entryInfoForm?id='+obj.data.id+"&taskId="+taskId;
                    pop_show("编辑入职信息",url,'100%','100%');
                }
            })


        };


    });
</script>
#end