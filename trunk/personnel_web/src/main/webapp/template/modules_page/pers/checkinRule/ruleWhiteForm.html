#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置编辑#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/employeeCheckinRule/saveCheckinRuleWhite";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#form").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.carTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 0px;margin-top: 0px;" id="form">
    <div class="layui-form-item" style="margin-top: 30px;">
        <label class="layui-form-label" style="padding: 8px 5px;">角色名称</label>
        <div class="layui-input-block">
            <select id="roleId" name="roleId" lay-verify="required" lay-search>
                <option value="">请选择</option>
                #for(role : roleList)
                <option value="#(role.id??)">#(role.roleName)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id"  value="#(record.id??)">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
