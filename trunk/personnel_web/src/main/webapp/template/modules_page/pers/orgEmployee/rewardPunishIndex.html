#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
            <div class="layui-row">
                <form class="layui-form layui-form-pane" method="" action="">

                    <span class="fr">
                        #shiroHasPermission("emp:archives:rewarDpunishment:add")
                        <a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
                        #end
						<a id="btn-refresh" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
					</span>
                </form>
            </div>
            <div class="layui-row">
                <input type="hidden" id="employeeId" value="#(employeeId??)">
                <table id="employeeTable" lay-filter="employeeTable"></table>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">
        #shiroHasPermission("emp:archives:rewarDpunishment:edit")
        <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
        #end
        #shiroHasPermission("emp:archives:rewarDpunishment:del")
        <a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="del">作废</a>
        #end
    </div>
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeTable'
        ;

        // 表格加载渲染
        var tableObj = table.render({
            id : tableId
            ,elem : '#'+tableId
            ,method : 'POST'
            ,url : '#(ctxPath)/persOrgEmployee/rewardPunishPage'
            ,height: vipTable.getFullHeight()    //容器高度
            ,where : {'employeeId':$("#employeeId").val()}//额外查询条件
            ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
            ,cols : [[
                {field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                ,{field:'type', title:'类型', width:100, align:'center',templet:"<div>#[[ {{#if(d.type=='0'){}} 奖励 {{#}else{}} 处罚  {{#}}} ]]#</div>"}
                ,{field:'', title:'奖罚时间', width:120, align:'center',templet:"<div>{{ dateFormat(d.happenDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'remark', title:'描述', align:'center'}
                ,{title:'操作', fixed:'right', width:120, align:'center', toolbar:'#empTableBar'}

            ]]
            ,page : true
        });
        //监听工具条
        table.on('tool('+tableId+')', function(obj) {
            if (obj.event === 'edit') {
                pop_show('编辑', '#(ctxPath)/persOrgEmployee/rewardPunishForm?id='+obj.data.id+"&employeeId="+$("#employeeId").val(), '500', '600');
            }else if(obj.event === 'del'){
                layer.confirm('您确定要作废吗?', function(index) {
                    //作废操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrgEmployee/delRewardPunish',
                        data: {id:obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pageTableReload();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
        });
        //重载表格，跳转到第一页
        pageTableReload = function () {
            tableReload(tableId,{'employeeId':$('#employeeId').val()});
        }


        //添加按钮点击事件
        $('#btn-add').on('click', function() {
            pop_show('添加', '#(ctxPath)/persOrgEmployee/rewardPunishForm?employeeId='+$("#employeeId").val(), '500', '600');
        });

    });
</script>
#end