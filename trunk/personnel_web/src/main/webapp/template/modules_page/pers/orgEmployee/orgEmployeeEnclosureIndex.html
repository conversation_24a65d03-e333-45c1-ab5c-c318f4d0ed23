#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
            <div class="layui-row">
                <form class="layui-form layui-form-pane" method="" action="">
                        #shiroHasPermission("emp:archives:enclosure:uploadBtn")
                        <a id="btn-add" class="layui-btn btn-add btn-default" style="margin-left: 20px;">上传</a>
                        #end
                </form>
            </div>
            <div class="layui-row">
                <input type="hidden" id="employeeId" value="#(employeeId??)">
                <table id="employeeTable" lay-filter="employeeTable"></table>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">

</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeTable'
        ;

        // 表格加载渲染
        var tableObj = table.render({
            id : tableId
            ,elem : '#'+tableId
            ,method : 'POST'
            ,url : '#(ctxPath)/persOrgEmployee/employeeEnclosurePage'
            ,height: vipTable.getFullHeight()    //容器高度
            ,where : {'employeeId':$("#employeeId").val()}//额外查询条件
            ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
            ,cols : [[
                {field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
                ,{field:'fileTitle', title:'附件标题', align:'center'}
                ,{field:'remark', title:'备注', align:'center'}
                ,{title:'操作', fixed:'right', width:190, align:'center', templet:function (d) {
                        var btn='';
                        var fileUrl=d.fileUrl;
                        var fileType=fileUrl.substr(fileUrl.lastIndexOf(".")+1);
                        if('jpg'==fileType || 'gif'==fileType|| 'bmp'==fileType|| 'png'==fileType|| 'ico'==fileType|| 'JPEG'==fileType){
                            btn+='<button type="button" class="layui-btn layui-btn-sm" lay-event="preview">预览</button>';
                        }
                    #shiroHasPermission("emp:archives:enclosure:downloadBtn")
                        btn+='<button type="button" class="layui-btn layui-btn-sm" lay-event="download">下载</button>';
                    #end
                    #shiroHasPermission("emp:archives:enclosure:delBtn")
                        btn+='<a class="layui-btn layui-btn-sm layui-btn-danger" lay-event="del">作废</a>';
                    #end

                    return btn;
                }}

            ]]
            ,page : true
        });
        //监听工具条
        table.on('tool('+tableId+')', function(obj) {
            if (obj.event === 'download') {
                window.location.href="#(ctxPath)/persOrgEmployee/downloadEnclosure?id="+obj.data.id;
            }else if(obj.event==='del'){
                layer.confirm('确定要作废该文件吗?', function(index) {
                    //作废操作
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrgEmployee/delPersOrgEmployeeFile',
                        data: {id:obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                pageTableReload();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }else if(obj.event==='preview'){
                var json={"title":"","id":123,"start":0,"data":[{"alt":obj.data.fileTitle,"pid":obj.data.id,"src":obj.data.fileUrl,"thumb":''}]};
                //相册层
                //parent.layerPhotos(json);
                parent.showPhotos([{"alt":obj.data.fileTitle,"pid":obj.data.id,"src":obj.data.fileUrl,"thumb":''}]);
            }
        });

        function download() {
            var url = 'download/?filename=aaa.txt';
            var xhr = new XMLHttpRequest();
            xhr.open('GET', url, true);        // 也可以使用POST方式，根据接口
            xhr.responseType = "blob";    // 返回类型blob
            // 定义请求完成的处理函数，请求前也可以增加加载框/禁用下载按钮逻辑
            xhr.onload = function () {
                // 请求完成
                if (this.status === 200) {
                    // 返回200
                    var blob = this.response;
                    var reader = new FileReader();
                    reader.readAsDataURL(blob);    // 转换为base64，可以直接放入a表情href
                    reader.onload = function (e) {
                        // 转换完成，创建一个a标签用于下载
                        var a = document.createElement('a');
                        a.download = 'data.xlsx';
                        a.href = e.target.result;
                        $("body").append(a);    // 修复firefox中无法触发click
                        a.click();
                        $(a).remove();
                    }
                }
            };
            // 发送ajax请求
            xhr.send()
        }

        function BrowseFolder() {
            var saveFolder = "";
            var Message = "请选择保存目录";
            var Shell = new ActiveXObject("Shell.Application");
            var Folder = Shell.BrowseForFolder(0, Message, 0x0000, 0);
            if (Folder != null) {
                if (Folder == "桌面") {
                    saveFolder = new ActiveXObject("wscript.shell")
                        .SpecialFolders("Desktop");
                } else {
                    Folder = Folder ? Folder.items().item().Path : '';
                    saveFolder = (/^\w:/.test(Folder)) ? Folder : '';
                }
                saveFolder.replace("%20", " ")//把路径中的20%还原为空格""
                alert("您保存路径为:" + saveFolder);
            }
            return saveFolder;
        }

        //重载表格，跳转到第一页
        pageTableReload = function () {
            tableReload(tableId,{'employeeId':$('#employeeId').val()});
        }


        //添加按钮点击事件
        $('#btn-add').on('click', function() {
            pop_show('添加', '#(ctxPath)/persOrgEmployee/enclosureForm?employeeId='+$("#employeeId").val(), '600', '');
        });

    });
</script>
#end