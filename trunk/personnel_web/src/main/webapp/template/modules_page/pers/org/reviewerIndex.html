#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()组织审核人管理页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div class="layui-tab layui-tab-card" lay-filter="reviewerTab">
		<ul class="layui-tab-title">
			<li lay-id="reviewerList" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<div class="layui-row">
					<form class="layui-form layui-form-pane" action="">
						<div class="layui-input-inline">
							<span class="layui-form-label">类型</span>
							<div class="layui-input-inline" style="width: 150px;">
								<select id="reviewerType" name="reviewerType" lay-search="">
									<option value="">请选择</option>
									#dictOption("reviewer_type", model.reviewerType??'', "")
								</select>
							</div>
						</div>
						<div class="layui-input-inline">
							<span class="layui-form-label">姓名</span>
							<div class="layui-input-inline" style="width: 150px;">
								<select id="empId" name="empId" lay-search="">
									<option value="">请选择</option>
									#for(empUser : empUserList)
										<option value="#(empUser.id)">#(empUser.full_name)</option>
									#end
								</select>
							</div>
						</div>
						<div class="layui-input-inline">
							<div class="layui-input-inline">
								<input type="hidden" id="orgId" value="#(orgId??'')">
								<button type="button" id="btn-search" class="layui-btn">查询</button>
								<a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
							</div>
						</div>
					</form>
				</div>
				<div class="layui-row">
					<table id="reviewerTable" lay-filter="reviewerTable"></table>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="reviewerTableBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table', 'form', 'element', 'vip_table'], function() {
	// 操作对象
	var table = layui.table
	, form = layui.form
    , layer = layui.layer
    , element = layui.element
	, $ = layui.$
    , vipTable = layui.vip_table
    , tableId = 'reviewerTable'
    ;
	
	// 表格加载渲染
	var tableObj = table.render({
		id : tableId
		,elem : '#'+tableId
		,method : 'POST'
		,url : '#(ctxPath)/persOrg/reviewerPage'
		,where : {orgId:$('#orgId').val()}//额外查询条件
		,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
		,cols : [[ 
			{field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'reviewerType', title:'审核人类型编码', width:200, align:'center'}
			,{field:'', title:'审核人类型', width:200, align:'center', templet:'#dictTpl("reviewer_type", "reviewerType")'}
			,{field:'empName', title:'姓名', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{fixed:'right', title:'操作', width:120, align:'center', toolbar:'#reviewerTableBar'}
		]]
		,page : true
	});
	//监听工具条
	table.on('tool('+tableId+')', function(obj) {
		if (obj.event === 'edit') {
			editTab('编辑','#(ctxPath)/persOrg/editOrgReviewer', {id:obj.data.id});
		} else if (obj.event === 'del') {
			layer.confirm('您确定要作废?', function(index) {
				//作废操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/persOrg/saveOrgReviewer',
		            data: {id:obj.data.id, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}
	});
	//重载表格
    pageTableReload = function () {
    	tableReload(tableId,{orgId:$('#orgId').val(),reviewerType:$('#reviewerType').val(),empId:$('#empId').val()});
    }
	//添加/编辑页签方法
	editTab = function(title,url,data) {
		$("#reviewerTab li").removeAttr("class");
		element.tabAdd('reviewerTab', {
			title: title
			,content: '<div id="edit_content"></div>' //支持传入html
			,id: 'reviewerEdit'
		});
		$('#edit_content').load(url, data, function(response,status,xhr){
			if(status=='success'){
				form.render('select', '');
				element.tabChange('reviewerTab', 'reviewerEdit');
			}else{
				layer.msg("添加失败!",{icon:5});
			}
		});
    };
	//关闭编辑页签
    closeEditTab = function () {
    	//作废指定Tab项
		element.tabDelete('reviewerTab', 'reviewerEdit');
    }
	element.on('tab(reviewerTab)', function(data){
		var tabId = $(this).attr("lay-id");//当前Tab标题所在的原始DOM元素的lay-id属性值
		if(tabId=='reviewerList'){
			closeEditTab();
		}
	});
	//添加按钮点击事件
	$('#btn-add').on('click', function() {
		editTab('添加','#(ctxPath)/persOrg/editOrgReviewer', {orgId:$("#orgId").val()});
	});
	// 查询
    $('#btn-search').on('click', function () {
    	pageTableReload();
    });
});
</script>
#end