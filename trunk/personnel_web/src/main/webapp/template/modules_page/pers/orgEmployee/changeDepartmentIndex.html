#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12">
            <div class="layui-row">
                <form class="layui-form layui-form-pane" method="" action="">

                    <span class="fr">
                        #shiroHasPermission("emp:archives:changeDepartment:add")
                        <a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
                        #end
						<a id="btn-refresh" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
					</span>
                </form>
            </div>
            <div class="layui-row">
                <input type="hidden" id="employeeId" value="#(employeeId??)">
                <table id="employeeTable" lay-filter="employeeTable"></table>
            </div>
        </div>
    </div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">

        #shiroHasPermission("emp:archives:changeDepartment:edit")
        <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
        #end
    </div>
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeTable'
        ;

        // 表格加载渲染
        var tableObj = table.render({
            id : tableId
            ,elem : '#'+tableId
            ,method : 'POST'
            ,url : '#(ctxPath)/persOrgEmployee/changeDepartmentPage'
            ,height: vipTable.getFullHeight()    //容器高度
            ,where : {'employeeId':$("#employeeId").val()}//额外查询条件
            ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
            ,cols : [[
                {field:'oldOrgName', title:'原组织', align:'center'}
                ,{field:'oldDeptName', title:'原部门', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
                ,{field:'oldPositionName', title:'原职位', align:'center'}
                ,{field:'oldSalary', title:'原薪资', align:'center'}
                ,{field:'newOrgName', title:'新组织', align:'center'}
                ,{field:'newDeptName', title:'新部门', align:'center'}
                ,{field:'newPositionName', title:'新职位', align:'center'}
                ,{field:'salary', title:'新薪资', align:'center'}
                ,{field:'', title:'变更时间', width:120, align:'center',templet:"<div>{{ dateFormat(d.changeDate,'yyyy-MM-dd') }}</div>"}
                ,{field:'changeType', title:'类型', align:'center',templet:function(d){
                        if(d.changeType=='personal'){
                            return '个人申请';
                        }else if(d.changeType=='internal'){
                            return '内部调动';
                        }else{
                            return '- -';
                        }
                    }}
                ,{field:'remark', title:'原因', align:'center'}
                ,{title:'操作', fixed:'right', width:120, align:'center', toolbar:'#empTableBar'}

            ]]
            ,page : true
        });
        //监听工具条
        table.on('tool('+tableId+')', function(obj) {
            if (obj.event === 'edit') {
                pop_show('编辑', '#(ctxPath)/persOrgEmployee/changeDepartmentForm?id='+obj.data.id+"&employeeId="+$("#employeeId").val(), '600', '');
            }
        });
        //重载表格，跳转到第一页
        pageTableReload = function () {
            tableReload(tableId,{'employeeId':$('#employeeId').val()});
        }


        //添加按钮点击事件
        $('#btn-add').on('click', function() {
            pop_show('添加', '#(ctxPath)/persOrgEmployee/changeDepartmentForm?employeeId='+$("#employeeId").val(), '600', '');
        });

    });
</script>
#end