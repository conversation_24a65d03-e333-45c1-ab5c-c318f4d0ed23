<div class="layui-row">
    <form class="layui-form layui-form-pane" action="" id="formId" style="padding-top: 20px;padding-left: 20px;padding-right: 20px;">
        <div class="layui-row" style="padding: 10px 20px;">

            <div class="layui-form-item" style="margin-bottom: 8px;">
                <label class="layui-form-label"><font color="red">*</font>异动员工</label>
                <div class="layui-input-inline">
                    <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                    <input id="id" name="id" type="hidden" value="#(changeApply.id??)">
                    <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                    <input id="createBy" name="createBy" type="hidden" value="#if(changeApply==null)#(createBy??)#else#(changeApply.createBy??)#end">
                    <input type="text" name="title" readonly  value="#(employee.fullName??)(#(employee.workNum??))" autocomplete="off" class="layui-input">
                </div>

                <label class="layui-form-label"><font color="red">*</font>所在部门</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#(deptNames??)" autocomplete="off" class="layui-input">
                </div>

                <label class="layui-form-label"><font color="red">*</font>职位</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#(positionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item" style="margin-bottom: 8px;">
                <label class="layui-form-label"><font color="red">*</font>异动前薪级</label>
                <div class="layui-input-inline">
                    <select id="oldSalaryLv" name="oldSalaryLv" disabled readonly lay-verify="required"   readonly >
                        <option value="">请选择异动前薪级</option>
                        <option value="1" #if(changeApply.old_salary_lv??=='1') selected #end>初级</option>
                        <option value="2" #if(changeApply.old_salary_lv??=='2') selected #end>中级</option>
                        <option value="3" #if(changeApply.old_salary_lv??=='3') selected #end>高级</option>
                    </select>
                </div>

                <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动前综合薪资</label>
                <div class="layui-input-inline">
                    <input type="text" name="lodSalary" readonly id="lodSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.lod_salary??)"  lay-verify="required|number" placeholder="请输入异动前综合薪资" autocomplete="off" class="layui-input">
                </div>

                <label class="layui-form-label"><font color="red">*</font>异动类型</label>
                <div class="layui-input-inline">

                    <input type="text" name="lodSalary" readonly id="" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.changeApplyType??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item" style="margin-bottom: 8px;">
                <label class="layui-form-label"><font color="red">*</font>异动后单位</label>
                <div class="layui-input-inline" style="width: 500px;">
                    <input type="text" name="lodSalary" readonly  #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.newDeptName??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>

                <label class="layui-form-label"><font color="red">*</font>异动后职位</label>
                <div class="layui-input-inline">
                    <input type="text" name="lodSalary" readonly  #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.newPositionName??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item" style="margin-bottom: 8px;">
                <label class="layui-form-label"><font color="red">*</font>异动后薪级</label>
                <div class="layui-input-inline">
                    <select id="newSalaryLv" name="newSalaryLv" disabled readonly lay-verify="required" readonly #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                        <option value="">请选择异动前薪级</option>
                        <option value="1" #if(changeApply.new_salary_lv??=='1') selected #end>初级</option>
                        <option value="2" #if(changeApply.new_salary_lv??=='2') selected #end>中级</option>
                        <option value="3" #if(changeApply.new_salary_lv??=='3') selected #end>高级</option>
                    </select>
                </div>

                <!--<label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动后综合薪资</label>
                <div class="layui-input-inline">
                    <input type="text" name="newSalary" readonly id="newSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                           value="#(changeApply.new_salary??)"  lay-verify="required|number" placeholder="请输入异动后综合薪资" autocomplete="off" class="layui-input">
                </div>-->
            </div>

            <div class="layui-form-item" style="margin-bottom: 8px;">
                <label class="layui-form-label" style="padding: 8px 0px;"><font color="red">*</font>异动后薪资</label>
                <div class="layui-input-block">
                    <div class="layui-row">
                        <label class="layui-form-label">基本工资</label>
                        <div class="layui-input-inline"  style="    width: 80px;">
                            <input type="text" name="basicSalary" readonly id="basicSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.basic_salary??)"  lay-verify="" placeholder="请输入异动后基本工资" autocomplete="off" class="layui-input">
                        </div>

                        <label class="layui-form-label">绩效工资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="meritSalary" readonly id="meritSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.merit_salary??)"  lay-verify="" placeholder="请输入异动后绩效工资" autocomplete="off" class="layui-input">
                        </div>

                    </div>

                    <div class="layui-row" style="margin-top: 5px;">
                        <label class="layui-form-label">岗位工资</label>
                        <div class="layui-input-inline" style="    width: 80px;">
                            <input type="text" name="postSalary" readonly id="postSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.post_salary??)"  lay-verify="" placeholder="请输入异动后岗位工资" autocomplete="off" class="layui-input">
                        </div>

                        <label class="layui-form-label">综合工资</label>
                        <div class="layui-input-inline">
                            <input type="text" name="comprehensiveSalary" readonly id="comprehensiveSalary" #if(!isSaveHandle && taskId!=null) disabled readonly #end required
                                   value="#(changeApply.comprehensive_salary??)"  lay-verify="" placeholder="请输入异动后综合工资" autocomplete="off" class="layui-input">
                        </div>
                    </div>

                </div>
            </div>


            <div class="layui-form-item" style="margin-bottom: 8px;">
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动时间长短</label>
                <div class="layui-input-inline">
                    <select id="changeDateType" readonly disabled name="changeDateType" lay-filter="changeDateType" #if(!isSaveHandle && taskId!=null) disabled readonly #end>
                        <option value="1" #if(changeApply.change_date_type??=='1') selected #end>短期</option>
                        <option value="2" #if(changeApply.change_date_type??=='2') selected #end>长期</option>
                    </select>
                </div>
                #if(changeApply.change_date_type??=='1')
                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动开始时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#date(changeApply.start_time??,'yyyy-MM-dd') #(changeApply.startDate??)" autocomplete="off" class="layui-input">
                </div>

                <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>异动结束时间</label>
                <div class="layui-input-inline">
                    <input type="text" name="title" readonly  value="#date(changeApply.end_time??,'yyyy-MM-dd') #(changeApply.endDate??)" autocomplete="off" class="layui-input">
                </div>
                #end
            </div>


            <div class="layui-form-item layui-form-text" style="margin-bottom: 8px;">
                <label class="layui-form-label">异动原因</label>
                <div class="layui-input-block">
                    <textarea name="changeReason" id="changeReason" readonly placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end
                              class="layui-textarea">#(changeApply.change_reason??)</textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text" style="margin-bottom: 8px;">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" id="remark" readonly placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end
                              class="layui-textarea">#(changeApply.remark??)</textarea>
                </div>
            </div>
        </div>

    </form>

</div>