#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;




        sd=form.on("submit(search)",function(data){
            //positionLoad();
            positionTableReload();
            return false;
        });

        positionLoad(null);

        function positionLoad(data){
            table.render({
                id : 'positionTable'
                ,elem : '#positionTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/pers/socialAddress/pageList'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field: 'name', title: '社保地址名称',unresize:true}
                    ,{field: 'sort', title: '排序',unresize:true}
                    ,{field: 'remark', title: '备注',unresize:true}
                    ,{field: 'isEnabled', title: '是否可用',unresize:true,templet:"<div>{{d.isEnabled == '1'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnabled == '0'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
                    ,{title: '操作',toolbar:'#actionBar',unresize:true}
                ]]
                ,page : true
            });
        };

        positionTableReload=function(){
            var data={'name':$("#positionName").val()};
            table.reload('positionTable',{'where':data});
        }

        // 添加
        $("#add").click(function(){

            var url = "#(ctxPath)/pers/socialAddress/form" ;
            pop_show("新增社保地址",url,500,500);
        });

        table.on('tool(positionTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/position/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                positionTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/pers/socialAddress/form?id=" + obj.data.id ;
                pop_show("编辑社保地址",url,500,500);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
	#shiroHasPermission("position:manage:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("position:manage:voidBtn")
    <!--<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>-->
	#end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            社保地址名称:
            <div class="layui-inline">
                <input id="positionName" name="positionName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("position:manage:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table class="layui-table" id="positionTable" lay-filter="positionTable"></table>

</div>
#end