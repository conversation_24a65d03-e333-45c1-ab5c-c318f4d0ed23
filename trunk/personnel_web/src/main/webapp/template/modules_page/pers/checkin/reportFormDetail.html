#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
#shiroHasPermission("emp:checkin:daySummaryExport")
<div class="layui-row" style="float: right;padding-right: 30px;">
    <button type="button" class="layui-btn" id="export">导出</button>
</div>
#end
<div class="layui-row" style="">
    <table id="detailTable" lay-filter="detailTable"></table>
</div>

#end

#define js()
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'detailTable'
            ,laydate=layui.laydate
        ;

        loadTable=function (data) {
            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/employeeCheckin/empCheckinDaySummaryDetailList'
                ,where : data//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {field:'summary_date', title:'日期', width:90, align:'center',unresize:true,templet:"<div>{{ dateFormat(d.summary_date,'yyyy-MM-dd') }}</div>"}
                    ,{field:'summary_date', title:'日报类型', width:110, align:'center',unresize:true,templet:function (d) {
                            if(d.day_type=='0'){
                                return '<span class="layui-badge layui-bg-green">工作日日报</span>';
                            }else if(d.day_type=='1'){
                                return '<span class="layui-badge layui-bg-cyan">休息日日报</span>';
                            }else if(d.day_type=='2'){
                                return '<span class="layui-badge layui-bg-blue">加班日报</span>';
                            }else if(d.day_type=='3'){
                                return '<span class="layui-badge layui-bg-orange">请休假日报</span>';
                            }else if(d.day_type=='4'){
                                return '<span class="layui-badge layui-bg-red">外派日报</span>';
                            }else if(d.day_type=='5'){
                                return '<span class="layui-badge layui-bg-blue">加班半天日报</span>';
                            }else if(d.day_type=='6'){
                                return '<span class="layui-badge">出差日报</span>';
                            }else if(d.day_type=='7'){
                                return '<span class="layui-badge">半天出差日报</span>';

                            }
                        }}
                    ,{field:'checkin_count', title:'打卡次数', width:70, align:'center',unresize:true}
                    ,{field:'earliest_time', title:'最早打卡时间', width:160, align:'center',unresize:true,templet:"<div>{{ dateFormat(d.earliest_time,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'lastest_time', title:'最晚打卡时间', width:160, align:'center',unresize:true,templet:"<div>{{ dateFormat(d.lastest_time,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'regular_work_sec', title:'实际工作时长',width:120, align:'center',unresize:true,templet:function (d) {
                            if(d.regular_work_sec==undefined){
                                return '0分钟';
                            }else{
                                return d.regular_work_sec+'分钟';
                            }
                        }}
                    ,{field:'standard_work_sec', title:'标准工作时长',width:120, align:'center',unresize:true,templet:function (d) {
                            if(d.standard_work_sec==undefined){
                                return '0分钟';
                            }else{
                                return d.standard_work_sec+'分钟';
                            }
                        }}
                    ,{field:'exception_1', title:'迟到',width:90, align:'center',unresize:true,templet:function (d) {

                            var exception_1_d=0;
                            if(d.exception_1_duration==undefined){
                                return '0分钟';
                            }else{
                                exception_1_d=d.exception_1_duration/60;
                                return '<span style="color: red;">'+exception_1_d+'分钟</span>';

                            }
                        }}
                    ,{field:'exception_2', title:'早退',width:90, align:'center',unresize:true,templet:function (d) {
                            var exception_2_d=0;
                            if(d.exception_2_duration==undefined){
                                return '0分钟';
                            }else{
                                exception_2_d=d.exception_2_duration/60;
                                return '<span style="color: red;">'+exception_2_d+'分钟</span>';
                            }
                        }}
                    ,{field:'exception_4', title:'旷工',width:90, align:'center',unresize:true,templet:function (d) {
                            var exception_4_d=0;
                            if(d.exception_4_duration==undefined){
                                return '0分钟';
                            }else{
                                exception_4_d=d.exception_4_duration/60;
                                return '<span style="color: red;">'+exception_4_d+'分钟</span>';
                            }
                        }}
                    ,{field:'exception_3', title:'缺卡',width:70, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_3==undefined){
                                return 0+"次";
                            }else{
                                return '<span style="color: red;">'+d.exception_3+'次</span>';
                            }
                        }}

                    ,{field:'exception_5', title:'地点异常',width:90, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_5==undefined){
                                return 0+"次";
                            }else{
                                return '<span style="color: red;">'+d.exception_5+'次</span>';
                            }
                        }}
                    ,{field:'exception_6', title:'设备异常',width:90, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_6==undefined){
                                return 0+"次";
                            }else{
                                return '<span style="color: red;">'+d.exception_6+'次</span>';
                            }
                        }}
                    #if(user.userName??=='Admin')
                    ,{rowspan: 2,title:'操作',fixed: 'right', width:100, unresize:true, align:'center', templet: function(d){
                            var editBtn='';
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="del">作废</a>';
                            return editBtn;
                        }
                    }
                    #end
                ]]
                ,page : false
                ,done:function () {
                }
            });
        }
        loadTable({"empId":'#(empId??)',"dateRange":'#(dateRange??)'});

        table.on('tool('+tableId+')',function (obj) {
            if(obj.event==='del'){
                layer.confirm("确定导出吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/employeeCheckin/delEmpCheckinDaySummary',
                        data: {'id':obj.data.id},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                loadTable({"empId":'#(empId??)',"dateRange":'#(dateRange??)'});
                            }
                        },
                        complete : function() {
                        }
                    });

                    layer.close(index) ;
                });
            }
        })

        $("#export").on('click',function () {
            layer.confirm("确定导出吗?",function(index){
                window.location.href="#(ctxPath)/employeeCheckin/empCheckinDaySummaryRecordExport?empId=#(empId??)&fullName=#(fullName??)&dateRange=#(dateRange??)";
                layer.close(index) ;
            });
        })

    });
</script>
#end