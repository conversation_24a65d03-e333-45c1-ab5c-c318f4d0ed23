#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告查阅名单页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
        	<div class="layui-inline">
            	<label class="layui-form-label">是否已读</label>
				<div class="layui-input-inline">
	                <select id="isRead" name="isRead">
	                    <option value="">全部</option>
                    	<option value="0">未读</option>
                    	<option value="1">已读</option>
	                </select>
                </div>
            </div>
			<div class="layui-inline">
				<input type="hidden" id="noticeId" name="noticeId" value="#(noticeId??)"/>
	            <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
	            <button type="button" id="remindBtn" class="layui-btn">提醒</button>
			</div>
        </form>
    </div>
    <div class="layui-row">
    	<table class="layui-table" id="lookNameTable" lay-filter="lookNameTable"></table>
    </div>
</div>
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        function lookNameLoad(data){
            table.render({
                id : 'lookNameTable'
                ,elem : '#lookNameTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/pers/notice/noticeLookName'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'}
                    ,{type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '名称', align: 'center', unresize: true}
                    ,{field:'', title: '是否已读', align: 'center', unresize: true,templet:"<div>{{ d.isRead=='0'?'<span class='layui-badge layui-bg-orange'>未读</span>':d.isRead=='1'?'<span class='layui-badge layui-bg-green'>已读</span>':'- -' }}</div>"}
                    ,{field:'', title: '阅读时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.isReadTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ]]
//                 ,limit : 15
//                 ,limits : [15,30,45,50]
                ,page : false
            });
        };
        
        lookNameLoad({noticeId:'#(noticeId??)'});

        form.on("submit(search)",function(data){
            lookNameLoad(data.field);
            return false;
        });
        
        //批量获取列表数据
        getCheckTableData = function(){
            var tableCheckStatus = table.checkStatus('lookNameTable');
            // 获取选择状态下的数据
            return tableCheckStatus.data;
        }

        //提醒按钮
        $("#remindBtn").click(function(){
        	var selectData = getCheckTableData();
//         	console.log('selectData==='+JSON.stringify(selectData));
			if(selectData!=null && selectData!=''){
	        	var dataArray = new Array();
	    		$(selectData).each(function(i,obj){
	    			var noticeId = obj.noticeId;
	    			var userId = obj.userId;
	   		    	var inputObj = {'noticeId':noticeId, 'userId':userId};
	   		    	dataArray.push(inputObj);
	    		});
//     			console.log('dataArray==='+JSON.stringify(dataArray));
				util.sendAjax ({
	                type: 'POST',
	                url: '#(ctxPath)/pers/notice/noticeRemind',
	                data: {selectDatas:JSON.stringify(dataArray)},
	                notice: true,
	                loadFlag: true,
	                success : function(rep){
	                    if(rep.state=='ok'){
	                        
	                    }
	                },
	                complete : function() {
	                }
	            });
			}else{
				layer.msg('请勾选数据', function () {});
				return;
			}
        });
    });
</script>
#end