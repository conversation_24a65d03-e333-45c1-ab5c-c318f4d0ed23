#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()查阅消息#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript">
    layui.use(['form','laytpl','table','element','layer','laydate'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;
        var laydate=layui.laydate;


        laydate.render({
            elem: '#detailDate'
            //,range: true //或 range: '~' 来自定义分割字符
            ,trigger:'click'
        });


        form.on('select(orgType)', function(obj) {
            var value=obj.value;
            console.log(obj.value);
            console.log(obj);
            //提交表单数据
            if(value=='branche_office'){
                $("#joinBranche").css("display","block");
                $("#joinBase").css("display","none");
                $("#joinBaseSelect").attr("lay-verify","");
                $("#joinBranchSelect").attr("lay-verify","");
            }else if(value=='pension_base' || value=='sojourn_hotel'){
                $("#joinBranche").css("display","none");
                $("#joinBase").css("display","block");
                $("#joinBaseSelect").attr("lay-verify","");
                $("#joinBranchSelect").attr("lay-verify","");
            }else{
                $("#joinBranche").css("display","none");
                $("#joinBase").css("display","none");
                $("#joinBaseSelect").attr("lay-verify","");
                $("#joinBranchSelect").attr("lay-verify","");
            }
        });

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {
            var data=$(formObj.form).serialize();
            if($("#remark").val()=='' || $("#remark").val()==undefined){
                layer.msg('请填写备注信息', {icon: 2});
                return false;
            }

            if($("#unit").val()=='1'){
                let rangeValue=$("#day").val();
                if(rangeValue!=''){
                    if(!(/^\d+$/.test(String(rangeValue)))){
                        layer.msg('天数值请输入正整数', {icon: 5});
                        return false;
                    }
                }

                let hourRangeValue=$("#hour").val();
                if(hourRangeValue!=''){

                    if(!(/^\d+(.(0|5))?$/.test(String(hourRangeValue)))){
                        layer.msg('小时值请输入正整数或正1位小数点', {icon: 5});
                        return false;
                    }
                }

                let minuteRangeValue=$("#minute").val();
                if(minuteRangeValue!=''){
                    if(!(/^\d+$/.test(String(minuteRangeValue)))){
                        layer.msg('分钟值请输入正整数', {icon: 5});
                        return false;
                    }
                }
                if(rangeValue==0 && hourRangeValue==0 && minuteRangeValue==0){
                    layer.msg('操作的值不能都为0', {icon: 5});
                    return false;
                }
            }else if($("#unit").val()=='3'){
                let rangeValue=$("#day").val();
                console.log(rangeValue)
                if(rangeValue!=''){
                    if(!(/^\d+$/.test(String(rangeValue)))){
                        layer.msg('天数值请输入正整数', {icon: 5});
                        return false;
                    }
                    if(rangeValue==0){
                        layer.msg('操作的值不能为0', {icon: 5});
                        return false;
                    }
                }
            }

            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/employeeLeaveBalance/saveLeaveBalance',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTab();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="border: none;background-color: #F2F2F2;">
    <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
    <input type="hidden"   name="leaveType" value="#(record.leave_type??)"/>
    <div class="layui-row">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:10px;">

            <div class="layui-form-item" style="margin-bottom: 5px;">
                <label class="layui-form-label"><font color="red">*</font>发生日期</label>
                <div class="layui-input-block">
                    <input type="text" name="detailDate" id="detailDate" class="layui-input" lay-verify="required" autocomplete="off" value="" placeholder="请选择发生日期">
                </div>
            </div>
            #if(record.unit??=='1')
            <div class="layui-form-item" style="margin-bottom: 5px;">
                <label class="layui-form-label"><font color="red">*</font>天数</label>
                <div class="layui-input-block">
                    <input type="text" name="day" id="day" class="layui-input" lay-verify="required|number" autocomplete="off" value="#(record.day??0)" placeholder="请输入天数">
                </div>
            </div>

            <div class="layui-form-item" style="margin-bottom: 5px;">
                <label class="layui-form-label"><font color="red">*</font>小时</label>
                <div class="layui-input-block">
                    <input type="text" name="hour" id="hour" class="layui-input" lay-verify="required|number" autocomplete="off" value="#(record.hour??0)" placeholder="请输入小时">
                </div>
            </div>

            <div class="layui-form-item" style="margin-bottom: 5px;display: none;">
                <label class="layui-form-label"><font color="red">*</font>分钟</label>
                <div class="layui-input-block">
                    <input type="text" name="minute" id="minute" class="layui-input" lay-verify="required|number" autocomplete="off" value="#(record.minute??0)" placeholder="请输入分钟">
                </div>
            </div>

            #else if(record.unit??=='2')
            <!--<div class="layui-form-item">
                <label class="layui-form-label"><font color="red">*</font>部门名称</label>
                <div class="layui-input-block">
                    <input type="text" name="orgName" class="layui-input" lay-verify="required" value="#(model.orgName??)" placeholder="请输入部门名称">
                </div>
            </div>-->
            #else if(record.unit??=='3')
            <div class="layui-form-item" style="margin-bottom: 5px;">
                <label class="layui-form-label"><font color="red">*</font>天数</label>
                <div class="layui-input-block">
                    <input type="text" name="day" id="day" class="layui-input" lay-verify="required" autocomplete="off" value="#(record.day??0)" placeholder="请输入天数">
                </div>
            </div>
            #end

            <div class="layui-form-item layui-form-text" style="margin-bottom: 5px;">
                <label class="layui-form-label"><font color="red">*</font>备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" id="remark" placeholder="请输入内容" class="layui-textarea"></textarea>
                </div>
            </div>
            <input name="leaveType" type="hidden" value="#(leaveType??)">
            <input name="empId" type="hidden" value="#(empId??)">
            <input name="leaveId" type="hidden" value="#(record.id??)">
            <input name="type" type="hidden" value="#(type??)">
            <input name="unit" id="unit" type="hidden" value="#(record.unit??)">
            <div class="layui-form-footer">
                <div class="pull-right">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end