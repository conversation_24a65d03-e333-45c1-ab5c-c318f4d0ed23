<table class="layui-table">
    <tbody>
        <tr>
            <input id="currentStepAlias" value="#(currentStepAlias??)" type="hidden">
            <td colspan="4" align="center">员工岗位调动申请表</td>
        </tr>
        <tr>
            <td>姓名</td>
            <td>#(record.fullName??)</td>
            <td>入职日期</td>
            <td>#date(record.entryTime??,'yyyy-MM-dd')</td>
        </tr>
        <tr>
            <td>原部门/基地</td>
            <td>#(record.oldDeptName)</td>
            <td>原职位</td>
            <td>#(record.oldPosition??)</td>
        </tr>
        <tr>
            <td>调入部门/基地</td>
            <td>#(record.deptName)</td>
            <td>调入职位</td>
            <td>#(record.positionName??)</td>
        </tr>
        <tr>
            <td colspan="4" align="">调动生效时间：#date(record.changeDate??,'yyyy-MM-dd')</td>
        </tr>
        <tr>
            <td colspan="4" align="">一、调动原因类型：#if(record.changeType??=='personal') 个人申请 #else if(record.changeType??=='internal') 内部调动 #end</td>
        </tr>
        <tr>
            <td colspan="4" align="">调动原因描述:
                #(record.remark??)
            </td>
        </tr>
        <tr>
            #if(stepCode??0>=1)
            <td #if(stepCode??0==1) colspan="4" #else colspan="2" #end width="50%">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label" style="width: 100px;">原部门意见：</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="oldDeptOpinion" id="oldDeptOpinion" placeholder="请输入内容" class="layui-textarea" #if(changeDeptStep1!=currentStepAlias?? || !isHandle) disabled #end>#(record.oldDeptOpinion??)</textarea>
                    </div>
                </div>

                #if(changeDeptStep1==currentStepAlias?? && isHandle)
                <div class="layui-row pull-right">
                    <button class="layui-btn" lay-submit="" id="saveOldDeptOpinion" lay-filter="saveOldDeptOpinion">保&nbsp;&nbsp;存</button>
                </div>
                #else
                <div class="layui-row">
                    <label class="layui-form-label">处理人：#(record.oldDeptHandlerName??)</label>
                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.oldDeptDate??,'yyyy-MM-dd')</label>
                </div>
                #end
            </td>
            #end

            #if(stepCode??0>=2)
            <td colspan="2" width="50%">
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label" style="width: 115px;">调入部门意见：</label>
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="newDeptOpinion" id="newDeptOpinion" placeholder="请输入内容" class="layui-textarea" #if(changeDeptStep2!=currentStepAlias?? || !isHandle) disabled #end>#(record.newDeptOpinion??)</textarea>
                    </div>
                </div>

                #if(changeDeptStep2==currentStepAlias?? && isHandle)
                <div class="layui-row pull-right">
                    <button class="layui-btn" lay-submit="" id="saveNewDeptOpinion" lay-filter="saveNewDeptOpinion">保&nbsp;&nbsp;存</button>
                </div>
                #else
                <div class="layui-row">
                    <label class="layui-form-label">处理人：#(record.newDeptHandlerName??)</label>

                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.newDeptDate??,'yyyy-MM-dd')</label>
                </div>
                #end
            </td>
            #end
        </tr>

        #if(stepCode??0>=3)
        <tr>
            <td colspan="4">集团HR意见：</td>
        </tr>
        <tr>
            <td colspan="4">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="baseOpinion" id="baseOpinion" placeholder="请输入内容" class="layui-textarea" #if(changeDeptStep3!=currentStepAlias?? || !isHandle) disabled #end>#(record.baseOpinion??)</textarea>
                    </div>
                </div>

                #if(changeDeptStep3==currentStepAlias?? && isHandle)
                <div class="layui-row pull-right">
                    <button class="layui-btn" lay-submit="" id="saveBaseOpinion" lay-filter="saveBaseOpinion">保&nbsp;&nbsp;存</button>
                </div>

                #else
                <div class="layui-row">
                    <label class="layui-form-label">处理人：#(record.baseHandlerName??)</label>

                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.baseDate??,'yyyy-MM-dd')</label>
                </div>
                #end
            </td>
        </tr>
        #end

        <!--#if(stepCode??0>=4)
        <tr>
            <td colspan="4">总部行政部意见</td>
        </tr>
        <tr>
            <td colspan="4">
                <div class="layui-row" style="margin-bottom: 5px;">
                    1、同意你部/中心 #(record.oldOrgName??) 从 #(record.oldDeptName??) 部门 #(record.oldPosition??) 职位调入到#(record.orgName??) #(record.deptName??)部门/中心 #(record.position??) 职位。
                </div>
                <div class="layui-row" style="margin-bottom: 5px;">
                    2、薪金调整为<div class="layui-input-inline" style="width: 100px;margin: 0px 5px;"><input type="text" #if(changeDeptStep4!=currentStepAlias?? || !isHandle) disabled #end id="salary" name="salary" placeholder="" value="#(record.salary??)" autocomplete="off" class="layui-input"></div>元/月
                    ，从<div class="layui-input-inline" style="width: 100px;margin: 0px 5px;"><input type="text" id="hrSalaryMonth" #if(changeDeptStep4!=currentStepAlias?? || !isHandle) disabled #end name="hrSalaryMonth" placeholder="" value="#(record.hrSalaryMonth??)" autocomplete="off" class="layui-input"></div>月份计发。
                </div>
                <div class="layui-row" style="margin-bottom: 5px;">
                    意见内容：
                </div>
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="hrOpinion" id="hrOpinion" placeholder="请输入内容" class="layui-textarea" #if(changeDeptStep4!=currentStepAlias?? || !isHandle) disabled #end>#(record.hrOpinion??)</textarea>
                    </div>
                </div>

                #if(changeDeptStep4==currentStepAlias?? && isHandle)
                <div class="layui-row pull-right">
                    <button class="layui-btn" lay-submit="" id="saveHrOpinion" lay-filter="saveHrOpinion">保&nbsp;&nbsp;存</button>
                </div>

                #else
                <div class="layui-row pull-right">
                    <label class="layui-form-label">处理人：#(record.hrHandlerName??)</label>

                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.hrDate??,'yyyy-MM-dd')</label>
                </div>
                #end


            </td>
        </tr>
        #end-->

        <!--#if(stepCode??0>=5)
        <tr>
            <td colspan="4">
                总经理意见：
            </td>
        </tr>
        <tr>
            <td colspan="4">
                <div class="layui-form-item layui-form-text">
                    <div class="layui-input-block" style="margin-left: 0px;">
                        <textarea name="generalManagerOpinion" id="generalManagerOpinion" placeholder="请输入内容" class="layui-textarea" #if(changeDeptStep5!=currentStepAlias?? || !isHandle) disabled #end>#(record.generalManagerOpinion??)</textarea>
                    </div>
                </div>
                #if(changeDeptStep5==currentStepAlias?? && isHandle)
                <div class="layui-row pull-right">
                    <button class="layui-btn" lay-submit="" id="saveGeneralManagerOpinion" lay-filter="saveGeneralManagerOpinion">保&nbsp;&nbsp;存</button>
                </div>
                #else
                <div class="layui-row pull-right">
                    <label class="layui-form-label">处理人：#(record.generalManagerHandlerName??)</label>

                    <label class="layui-form-label" style="padding-left: 100px;">日期：#date(record.generalManagerDate??,'yyyy-MM-dd')</label>
                </div>
                #end
            </td>
        </tr>
        #end-->
    </tbody>
</table>