#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()转正单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }


    .layui-radio-disbaled>i {
        color: #5FB878!important;
    }
    .layui-form-radio * {
        font-size: 12px;
    }
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/base64.js"></script>
<script type="text/javascript">
    layui.use(['form','laytpl','table','element','layer','laydate'],function() {
        var form = layui.form;
        var $ = layui.$;
        var laytpl = layui.laytpl;
        var layer = layui.layer;
        var table = layui.table;
        var element = layui.element;
        var layer = layui.layer;
        var laydate=layui.laydate;

        laydate.render({
            elem : '#qualifiedDate',
            type: 'date',
            trigger: 'click'
        });

        /*setInterval(function () {
            var data=parent.getQuitEmpIdValue();
            if(data.quitEmpId!=undefined && data.quitEmpId!='' && data.quitEmpId!=$("#employeeId").val()){
                $("#employeeId").val(data.quitEmpId);
                $("#fullName").val(data.fullName+"("+data.workNum+")");
                layer.load();
                $.post('#(ctxPath)/persOrgEmployee/getEmpById',{'id':data.quitEmpId},function (obj) {
                    if(obj.state=='ok'){
                        $("#entryTime").val(dateFormat(obj.data.entryTime,'yyyy-MM-dd'));
                        if(obj.data.sex=='male'){
                            $("#sex").val('男');
                        }else if(obj.data.sex=='female'){
                            $("#sex").val('女');
                        }else{
                            $("#sex").val('未知');
                        }

                        $("#education").find('option[value='+obj.data.education+']').prop('selected',true);
                        form.render('select');
                    }
                    layer.closeAll('loading');
                });


                getDept();
            }
        },400);*/


        function getDept(){
            $.post('#(ctxPath)/api/getEmpDeptList?empId='+$("#employeeId").val(),{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择部门</option>';
                    var selected="";
                    if(res.data.length==1){
                        selected="selected";
                    }
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.orgName+'</option>';
                    });
                    if(res.data.length==1){
                        getPosition(res.data[0].id);
                    }
                    $("#deptId").html(str);
                    form.render('select');
                }
            })
        }
        function getPosition(deptId){
            $.post('#(ctxPath)/api/getEmpPositionList?empId='+$("#employeeId").val()+"&deptId="+deptId,{},function (res) {
                if(res.code=='0'){
                    var str='<option value="">请选择职位</option>';
                    $.each(res.data,function (index,item) {
                        str+='<option value="'+item.id+'" selected>'+item.positionName+'</option>';
                    })
                    $("#positionId").html(str);
                    form.render('select');
                }
            })
        }

        form.on('select(deptId)',function (obj) {
            getPosition(obj.value);
        })

        $("#choiceBtn").on('click',function () {
            parent.empTable();
        })


        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            $.ajax({
                type:'post',
                data: $("#noticeForm").serialize(),
                url: '#(ctxPath)/api/saveEmployeeQualified',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });

        //监听表单提交
        form.on('submit(saveSubmitBtn)', function(formObj) {
            var data=$("#noticeForm").serialize()+"&saveType=2";
            //提交表单数据
            /*util.sendAjax ({
                type: 'POST',
                url: '',
                data: data,
                notice: false,
                loadFlag: true,
                success : function(rep){
                    if(rep.code=='0'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });*/
            console.log(data);
            $.ajax({
                type:'post',
                data: data,
                url: '#(ctxPath)/api/saveEmployeeQualified',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        parent.reloadTable();
                        pop_close();
                    } else {
                        layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    layer.closeAll('loading');
                }
            });
            return false;
        });


        //自定义验证规则
        form.verify({
            otherReq: function(value,item){
                var $ = layui.$;
                var verifyName=$(item).attr('name'),
                    verifyType=$(item).attr('type'),
                    formElem=$(item).parents('.layui-form'),//获取当前所在的form元素，如果存在的话
                    verifyElem=formElem.find('input[name='+verifyName+']'),//获取需要校验的元素
                    isTrue= verifyElem.is(':checked'),//是否命中校验
                    focusElem = verifyElem.next().find('i.layui-icon');//焦点元素
                if(!isTrue || !value){
                    //定位焦点
                    focusElem.css(verifyType=='radio'?{"color":"#FF5722"}:{"border-color":"#FF5722"});
                    //对非输入框设置焦点
                    focusElem.first().attr("tabIndex","1").css("outline","0").blur(function() {
                        focusElem.css(verifyType=='radio'?{"color":""}:{"border-color":""});
                    }).focus();
                    return '必填项不能为空';
                }
            }
        });


        //保存员工入职记录流程
        doTask=function (stepState,msg) {

            var data={"taskId":'#(taskId??)',"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })
        saveFormData=function(){
            $.ajax({
                type:'post',
                data: $("#noticeForm").serialize(),
                url: '#(ctxPath)/api/saveEmployeeQualified',
                contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                dataType: 'json',
                timeout: 30000,
                beforeSend: function (XMLHttpRequest) {
                    //layer.load();
                },
                success: function (res) {
                    if (res.code == '0') {
                        //parent.layer.msg('操作成功', {icon: 1, offset: 'auto'});
                        //parent.reloadTable();
                        //pop_close();
                    } else {
                        //layer.msg(res.msg, {icon: 2, offset: 'auto'});
                    }
                }
                ,complete :function(XMLHttpRequest, TS){
                    //layer.closeAll('loading');
                }
            });
            return false;
        }
        #if(isSaveHandle || isBUHead || isHRSpecialist)
            let countNum=0
            setInterval(function () {
                if(countNum<=1000){
                    countNum++;
                }
            }, 2500);
        #end

    });
</script>
#end

#define content()
<body class="v-theme">
<form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style="margin-top:30px;">

    #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
    <fieldset class="layui-elem-field layui-field-title" style="display:block;padding-left: 20px;">
        <legend>流程提交人信息</legend>
        <div class="layui-form-item">
            <label class="layui-form-label" style="">提交人姓名</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="">提交人部门</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="">提交人职位</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="">提交时间</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label" style="">任务编号</label>
            <div class="layui-input-block">
                <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
            </div>
        </div>
    </fieldset>
    #end
    <fieldset class="layui-elem-field layui-field-title" style="display:block;">
        <legend>外派单信息</legend>
    </fieldset>
    <div class="layui-row">
        <div class="layui-col-md12 layui-form" id="content" style="padding-right: 10px;">
            <div class="layui-collapse" >

                <div class="layui-row" style="padding: 10px 20px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>转正员工</label>
                        <div class="layui-input-inline">
                            <input id="employeeId" name="employeeId" type="hidden" value="#(employee.id??)">
                            <input id="id" name="id" type="hidden" value="#(employeeQualified.id??)">
                            <input id="currentStepAlias" name="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                            <input id="createBy" name="createBy" type="hidden" value="#if(employeeQualified==null)#(createBy??)#else#(employeeQualified.createBy??)#end">
                            <input id="fullName" name="fullName" readonly  lay-verify="required"  placeholder="" #if(employeeQualified!=null)value="#(employee.fullName??)(#(employee.workNum??))" #end autocomplete="off" class="layui-input">
                        </div>
                        #if(employeeQualified==null)
                        <button class="layui-btn" id="choiceBtn" #if(!isSaveHandle && taskId!=null) disabled readonly #end type="button">选择</button>
                        #end
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><font color="red">*</font>入职日期</label>
                            <div class="layui-input-inline">
                                <input id="entryTime" readonly  lay-verify="required"  placeholder="" value="#date(employee.entryTime??,'yyyy-MM-dd')"  autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><font color="red">*</font>性别</label>
                            <div class="layui-input-inline">
                                <input id="sex" readonly  lay-verify="required"  placeholder="" value="#if(employee!=null)#if(employee.sex=='male')男#elseif(employee.sex=='female')女#else未知#end#end"  autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label"><font color="red">*</font>文化程度</label>
                            <div class="layui-input-inline">
                                <select id="education" lay-filter="" lay-verify="required">
                                    <option value="">请选择文化程度</option>
                                    #dictOption("education", employee.education??'', "")
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">*</font>部门</label>
                        <div class="layui-input-inline">
                            #if(!isSaveHandle && taskId!=null)
                            #for(dept : deptList)
                            #if(dept.id??==employeeQualified.deptId??)
                            <input disabled  lay-verify="required"  placeholder="" value="#(dept.orgName??)" autocomplete="off" class="layui-input">
                            <input value="#(dept.id??)" name="deptId" type="hidden">
                            #end
                            #end
                            #else
                            <select id="deptId" name="deptId" lay-filter="deptId" lay-verify="required"  >
                                <option value="">请选择部门</option>
                                #for(dept : deptList)
                                <option value="#(dept.id??)" #if(dept.id??==employeeQualified.deptId??) selected #end>#(dept.orgName??)</option>
                                #end
                            </select>
                            #end
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">职位</label>
                        <div class="layui-input-inline">

                            #if(!isSaveHandle && taskId!=null)
                            #for(position : positionList)
                            #if(position.id??==employeeQualified.positionId??)
                            <input disabled  lay-verify="required"  placeholder="" value="#(position.positionName??)" autocomplete="off" class="layui-input">
                            <input value="#(position.id??)" name="positionId" type="hidden">
                            #end
                            #end
                            #else
                            <select id="positionId" name="positionId" lay-verify="" >
                                <option value="">请选择职位</option>
                                #for(position : positionList)
                                <option value="#(position.id??)" #if(position.id??==employeeQualified.positionId??) selected #end>#(position.positionName??)</option>
                                #end
                            </select>
                            #end
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">个人工作总结及职业发展规划</label>
                        <div class="layui-input-block">
                            <textarea name="reason" id="reason" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(employeeQualified.reason??)</textarea>
                        </div>
                    </div>
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="remark" id="remark" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(employeeQualified.remark??)</textarea>
                        </div>
                    </div>
                    #if(employeeQualified!=null)

                    #if(isHRSpecialist || isBUHead || isHR)
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                        <legend>总部/各分部人事专员综合反馈：</legend>
                    </fieldset>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>知识技能培训</label>
                        <div class="layui-input-block">
                            <input type="radio" name="skillsTraining" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="1" #if(employeeQualified==null || employeeQualified.skillsTraining??=='1') checked #end title="已参加培训考核合格">
                            <input type="radio" name="skillsTraining" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="2" #if(employeeQualified.skillsTraining??=='2') checked #end title="已参加培训考核暂不合格">
                            <input type="radio" name="skillsTraining" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="3" #if(employeeQualified.skillsTraining??=='3') checked #end title="未参加培训考核">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 2px;"><font color="red">*</font>试用期出勤状况</label>
                        <div class="layui-input-block">
                            <input type="radio" name="attendanceStatus" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="1" #if(employeeQualified==null || employeeQualified.attendanceStatus??=='1') checked #end title="满勤无违纪">
                            <input type="radio" name="attendanceStatus" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="2" #if(employeeQualified.attendanceStatus??=='2') checked #end title="有违纪">
                        </div>
                        <div class="layui-input-block">
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>迟到次数</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="lateCount" id="lateCount" #if(!isHRSpecialist && taskId!=null) disabled readonly #end required value="#(employeeQualified.lateCount??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>早退次数</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="leaveEarlyCount" id="leaveEarlyCount" #if(!isHRSpecialist && taskId!=null) disabled readonly #end required value="#(employeeQualified.leaveEarlyCount??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>事、病假天数</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="sickLeaveCount" id="sickLeaveCount" #if(!isHRSpecialist && taskId!=null) disabled readonly #end required value="#(employeeQualified.sickLeaveCount??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>旷工天数</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="absenteeismCount" id="absenteeismCount" #if(!isHRSpecialist && taskId!=null) disabled readonly #end required value="#(employeeQualified.absenteeismCount??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 2px;width: 130px;font-size: 12px;"><font color="red">*</font>试用期制度执行情况</label>
                        <div class="layui-input-block">
                            <input type="radio" style="float: left;" name="institutionStatus" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="1" #if(employeeQualified==null || employeeQualified.institutionStatus??=='1') checked #end title="无违纪行为">
                            <input type="radio" style="float: left;" name="institutionStatus" #if(!isHRSpecialist && taskId!=null) disabled readonly #end lay-verify="otherReq" value="2" #if(employeeQualified.institutionStatus??=='2') checked #end title="有违纪">

                        </div>
                        <div class="layui-input-block">
                            <label class="layui-form-label" style="padding: 8px 2px;margin-left: 23px;"><font color="red"></font>违纪次数</label>
                            <div class="layui-input-inline" style="margin-left: 133px;">
                                <input type="text" name="institutionViolateCount"   id="institutionViolateCount" #if(!isHRSpecialist && taskId!=null) disabled readonly #end required value="#(employeeQualified.institutionViolateCount??)"  lay-verify="required" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    #end

                    #if(isBUHead || isHR)
                    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
                        <legend>总部/部门负责人评估意见：</legend>
                    </fieldset>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>转正意见</label>
                        <div class="layui-input-block">
                            <input type="radio" style="float: left;" #if(!isBUHead && taskId!=null) disabled readonly #end name="qualifiedOpinion" lay-verify="otherReq" value="1" #if(employeeQualified==null || employeeQualified.qualifiedOpinion??=='1') checked #end title="同意转正">
                            <input type="radio" style="float: left;" #if(!isBUHead && taskId!=null) disabled readonly #end name="qualifiedOpinion" lay-verify="otherReq" value="2" #if(employeeQualified.qualifiedOpinion??=='2') checked #end title="延长试用期">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>试用期薪资</label>
                        <div class="layui-input-block">
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>基本工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="pBasicSalary" id="pBasicSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.pBasicSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>绩效工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="pMeritsSalary" id="pMeritsSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.pMeritsSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>岗位工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="pPostSalary" id="pPostSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.pPostSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>其他</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="pOtherSalary" id="pOtherSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.pOtherSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>综合工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="pSalary" id="pSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.pSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;"><font color="red">*</font>转正后薪资</label>
                        <div class="layui-input-block">
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>基本工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="qBasicSalary" id="qBasicSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.qBasicSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>绩效工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="qMeritsSalary" id="qMeritsSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.qMeritsSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>岗位工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="qPostSalary" id="qPostSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.qPostSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>其他</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="qOtherSalary" id="qOtherSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.qOtherSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                            <label class="layui-form-label" style="padding: 8px 2px;"><font color="red"></font>综合工资</label>
                            <div class="layui-input-inline" style="width: 100px">
                                <input type="text" name="qSalary" id="qSalary" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#(employeeQualified.qSalary??)"  lay-verify="required|number" placeholder="" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="padding: 8px 5px;width: 135px;"><font color="red">*</font>建议转正生效日期</label>
                        <div class="layui-input-block" style="margin-left: 135px;">
                            <input type="text" name="qualifiedDate" id="qualifiedDate" #if(!isBUHead && taskId!=null) disabled readonly #end required value="#date(employeeQualified.qualifiedDate??,'yyyy-MM-dd')"  lay-verify="required" placeholder="请输入生效时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    #end
                    #end


                    <!--<div class="layui-form-item layui-form-text">
                        <label class="layui-form-label">备注</label>
                        <div class="layui-input-block">
                            <textarea name="reason" id="reason" placeholder="请输入内容" lay-verify="" #if(!isSaveHandle && taskId!=null) disabled readonly #end  class="layui-textarea">#(employeeQualified.reason??)</textarea>
                        </div>
                    </div>-->
                    <div style="height: 50px;">

                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-left" style="text-align: center;float: none !important;">
                <input name="userId" value="#(userId??)" type="hidden">
                #if(isSaveHandle || taskId==null || isBUHead || isHRSpecialist)
                <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                #end
            </div>
           <!-- <div class="pull-right">
                &lt;!&ndash;<div class="layui-form-mid layui-word-aux" >说明：前面有<font color="red">*</font>的字段为必填字段。</div>&ndash;&gt;
            </div>-->

        </div>

    </div>
</form>
</body>
#end