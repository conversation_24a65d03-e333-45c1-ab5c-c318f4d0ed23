#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()组织架构管理首页#end

#define css()
<style>
	html,body{height:90%}
</style>
#end

#define content()
<div class="my-btn-box" style="height: 100%">
	<div class="layui-row">
		<form class="layui-form">
			<span class="fl"></span>
			<span class="fr">
	    	<div class="layui-inline">

				<div class="layui-input-inline" style="margin-right: 20px;">
					<label class="layui-form-label">状态：</label>
					<div class="layui-input-inline"  >
						<select name="status" id="status" lay-filter="status">
							<option value="2" >全部</option>
							<option value="1" selected>启用</option>
							<!--<option value="1">禁用</option>-->
						</select>
					</div>
				</div>

	    		<div class="layui-input-inline">
	    			<div class="layui-btn-group">
	    				#shiroHasPermission("org:maintain:addBtn")
				        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
						#end
	        			<a id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
	    			</div>
	    		</div>
	    	</div>
	    </span>
		</form>
	</div>
	<div class="layui-row" style="height: 90%">
		<table id="orgTreeGrid" lay-filter="orgTreeGrid"></table>
	</div>
</div>
#end

#define js()
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['treeGrid','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,treeGrid = layui.treeGrid
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'orgTreeGrid'
        ,sortSaveBtn = ''
        ;
	
    #shiroHasPermission('org:maintain:editBtn')
		sortSaveBtn='<button id="saveSortBtn" type="button" onclick="saveSort()" class="layui-btn layui-btn-xs">保存排序</button>';
	#end



    //初始化表格
	loadTableTree=function(){
		var ptable=treeGrid.render({
			id: tableId
			, elem: '#'+tableId
			, url: '#(ctxPath)/persOrg/orgTreeGrid?status='+$('#status').val()
			, method: 'post'
			, idField: 'id'//必須字段
			, treeId: 'id'//树形id字段名称
			, treeUpId: 'pId'//树形父id字段名称
			, treeShowName: 'name'//以树形式显示的字段
			, isOpenDefault: true//节点默认是展开还是折叠【默认展开】
			//,height: 'full-110'
			, cols: [[
				{field:'sort', title: sortSaveBtn, width:100, align:'center', templet: function(d){
						if(d.type=='main_org'){
							return d.sort;
						}else{
							return sortInput='<input type="text" class="layui-input sortInput" data-id="'+d.id+'" value="'+d.sort+'" maxlength="7" autocomplete="off">';
						}
					}
				}
				,{field:'name', title:'名称', unresize:true}
				,{field:'isRestrict', title:'是否限制员工人数', unresize:true, templet:function (d) {
						if(d.record.isRestrict=='1'){
							return '<span class="layui-badge layui-bg-blue">是</span>';
						}else if(d.record.isRestrict=='0'){
							return '<span class="layui-badge">否</span>';
						}else{
							return '- -';
						}
					}}
				,{field:'headcount', title:'编制人数（不含子级）', unresize:true, templet:function (d) {
						if(d.record.headcount==undefined){
							return '';
						}
						return d.record.headcount;
					}}
				,{field:'totalHeadcount', title:'总编制人数（含子级）', unresize:true}
				,{field:'staffCount', title:'员工数（不含子级）', unresize:true, templet:function (d) {
						if(d.record.staffCount==undefined){
							return '';
						}
						return d.record.staffCount;
					}}
				,{field:'totalStaffCount', title:'总员工人数（含子级）', unresize:true}
				,{field:'type', title:'是否可用', width:100, unresize:true, align:'center', templet:function (d) {
						if(d.type=='1'){
							return '<span class="layui-badge layui-bg-blue">可用</span>';
						}else if(d.type=='0'){
							return '<span class="layui-badge">不可用</span>';
						}
					}}
				,{field:'qiyeChatStatus', title:'是否已创建企业微信部门群', width:200, unresize:true, align:'center', templet:function (d) {
						if(d.record.qiyeChatid==undefined){
							return '<span class="layui-badge">未创建</span>';
						}else{
							return '<span class="layui-badge layui-bg-blue">已创建</span>';
						}
					}}
				,{title:'操作', width:350, unresize:true, align:'right', templet: function(d){
						var editBtn = '';
						var reviewerBtn = '';
						var delBtn = '';
						#shiroHasPermission('org:maintain:editBtn')
						editBtn='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
						#end
						#shiroHasPermission('org:maintain:reviewerBtn')
						reviewerBtn='<a class="layui-btn layui-btn-xs" lay-event="reviewer">审核人</a>';
						#end
						var enable='';
						#shiroHasPermission('org:maintain:enableBtn')
						if(d.type=='1'){
							enable='<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="disable">禁用</a>';
						}else if(d.type=='0'){
							enable='<a class="layui-btn layui-btn-xs" lay-event="enable">启用</a>';
						}
						#end
						#shiroHasPermission('org:maintain:voidBtn')
						delBtn='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>';
						#end

						var createChat='';

						if(d.record.isBU=='1' && d.record.qiyeChatid==undefined){
							createChat='<a class="layui-btn layui-btn-xs" lay-event="del">创建企业微信部门群</a>';
						}
						if(d.type=='main_org'){
							return editBtn;
						}else{
							return createChat+editBtn+reviewerBtn+enable+delBtn;
						}
					}
				}
			]],
			done:function (a,b,c) {
				var data=a.data;

				$.each(data,function (index,item) {
					let obj={'value':0,'totalStaffCount':0};
					updateTreeDataAdd(item,obj);
					let currValue=0;
					let currStaffCount=0;

					if(item.record.headcount!=undefined){
						currValue=item.record.headcount;
					}
					if(item.record.staffCount!=undefined){
						currStaffCount=item.record.staffCount;
					}


					//$("table tr[u_id='"+item.id+"']").find("td[data-field='totalHeadcount']").find('p').text(obj.value);
					$("input[data-id='"+item.id+"']").parents("tr").find("td[data-field='totalHeadcount']").find('p').text(obj.value+currValue);
					$("input[data-id='"+item.id+"']").parents("tr").find("td[data-field='totalStaffCount']").find('p').text(Number(obj.totalStaffCount)+Number(currStaffCount));

				})

			}

		});
	}

	function updateTreeDataAdd(node,obj){
		$.each(node.children,function (index,item) {

			if(item.record.headcount!=undefined){
				let newValue=obj.value+item.record.headcount;
				obj.value=newValue;
			}
			if(item.record.staffCount!=undefined){
				let newTotalStaffCount=obj.totalStaffCount+item.record.staffCount;
				obj.totalStaffCount=newTotalStaffCount;
			}
			if(item.children.length>0){
				updateTreeDataAdd(item,obj);
			}
		})
	}

	loadTableTree();

	form.on('select(status)',function (obj) {
		loadTableTree();
	})

    //表格事件绑定
    treeGrid.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/persOrg/edit?id='+obj.data.id,'','');
        }else if(obj.event==="reviewer"){//审核人按钮事件
        	pop_show('审核人','#(ctxPath)/persOrg/reviewerIndex?id='+obj.data.id,'','');
        }else if(obj.event==="del"){//作废按钮事件
        	layer.confirm('你确定作废数据吗？', {icon: 3, title:'提示'}, function(index) {
				//作废操作
				util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/persOrg/del',
                    data: {id:obj.data.id},
                    notice: true,
                    loadFlag: true,
                    success : function(data){
                    	tableGridReload();
                    },
                    complete : function() {
                    	
                    }
                });
				layer.close(index);
			});
        }else if(obj.event==='disable'){
			layer.confirm('你确定禁用吗？', {icon: 3, title:'提示'}, function(index) {
				//作废操作
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/persOrg/disable?id='+obj.data.id,
					data: {id:obj.data.id},
					notice: true,
					loadFlag: true,
					success : function(data){
						tableGridReload();
					},
					complete : function() {

					}
				});
				layer.close(index);
			});
		}else if(obj.event==='enable'){
			layer.confirm('你确定启用吗？', {icon: 3, title:'提示'}, function(index) {
				//作废操作
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/persOrg/enable?id='+obj.data.id,
					data: {id:obj.data.id},
					notice: true,
					loadFlag: true,
					success : function(data){
						tableGridReload();
					},
					complete : function() {

					}
				});
				layer.close(index);
			});
		}
    });
    //表格重载
    tableGridReload = function () {
    	//console.log($("#status").val());
    	//treeGrid.reload(tableId, {});
		loadTableTree();
    }
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		pop_show('添加','#(ctxPath)/persOrg/add?formType=dept','','');
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	tableGridReload();
    });
	//保存排序按钮点击事件
	/*$('#saveSortBtn').on('click', function() {
		saveSort();
	});*/
	saveSort = function() {
		var saveFlag = true;
		var dataArray = new Array();
		$(".sortInput").each(function(i,obj){
			var orgId = $(this).attr('data-id');
			var orgSort = obj.value;
		    if(orgSort==null || orgSort==''){
		    	saveFlag = false;
		    	return false;
		    }else{
		    	var inputObj = {'id':orgId, 'orgSort':orgSort};
		    	dataArray.push(inputObj);
		    }
		});
		if(saveFlag){
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/persOrg/sortBatchSave',
	            data: {sortDatas:JSON.stringify(dataArray)},
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		tableGridReload();
	            	}
	            },
	            complete : function() {
	            	//保存按钮点击事件
					$('#saveSortBtn').on('click', function() {
						saveSort();
					});
			    }
	        });
		}else{
			layer.msg('排序不能为空，请输入排序!',{icon:5});
		}
	}
});
</script>
#end