#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .layui-table-cell {
        height: auto;
    }

</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">规则名称：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>
            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>

            <label class="layui-form-label">打卡类型：</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="type" name="type">
                    <option value="">全部</option>
                    <option value="1">固定时间上下班</option>
                    <option value="2">按班次上下班</option>
                    <option value="3">自由上下班</option>
                </select>
            </div>
            <button class="layui-btn" type="reset" id="resetBtn" style="margin-left: 20px;">重置</button>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:checkinRule:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            #shiroHasPermission("emp:checkinRule:whiteBtn")
            <button class="layui-btn" id="whiteBtn" type="button">白名单</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    <div class="layui-btn-group">
    #shiroHasPermission("emp:checkinRule:holidaysBtn")
    #[[
    {{#if(d.type=='1' || d.type=='3'){}}
    <a class="layui-btn layui-btn-xs " lay-event="holidays">节假日</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("emp:checkinRule:repairScheduleBtn")
    #[[
    {{#if(d.type=='2'){}}
    <a class="layui-btn layui-btn-xs " lay-event="repairSchedule">补排班</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("emp:checkinRule:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("emp:checkinRule:delBtn")
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
    #end
    </div>
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.extend({
        dtree: 'layui_ext/dtree/dtree'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['dtree','form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,dtree=layui.dtree;

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }
        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"name":$("#name").val(),'type':$("#type").val(),'deptId':id});
        }


        $("#queryBtn").on('click',function () {
            reloadTable();
        });



        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/employeeCheckinRule/form';
            pop_show("添加规则",url,'100%','100%');
        });
        $("#whiteBtn").on('click',function () {
            var url='#(ctxPath)/employeeCheckinRule/ruleWhiteIndex';
            pop_show("白名单",url,950,650);
        });

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        /*$.post('#(ctxPath)/persOrg/orgDtree',{},function (r) {
            var jsonArray=[];
            $.each(r.data,function (index,item) {
                var json={"id":item.id,"title":item.name,"checkArr":"0","parentId":item.pId};
                var flag=isUpdateParentId(json.parentId,r.data);
                if(flag){
                    json.parentId='0';
                }
                jsonArray[index]=json;
            });
            // 初始化树
            var DemoTree = dtree.renderSelect({
                elem: "#deptId",
                dataFormat: "list",
                data:jsonArray,
                initLevel:10,
                menubar:{  //菜单栏
                    search:""  //搜索
                },
                selectCardHeight:"500"
                //url: "#(ctxPath)/persOrg/orgDtree" // 使用url加载（可与data加载同时存在）
            });

            $("#resetBtn").on('click',function () {

                DemoTree.menubarMethod().refreshTree();
            })
        });*/

        function isUpdateParentId(pid,array){
            var flag=true;
            $.each(array,function (i,it) {
                if(pid==it.id){
                    flag=false;
                    return false;
                }
            });
            return flag;
        }

        var dataStr={"1":"周一","2":"周二","3":"周三","4":"周四","5":"周五","6":"周六","0":"周日"}
        var hour={"00":"00","01":"01","02":"02","03":"03","04":"04","05":"05","06":"06","07":"07","08":"08","09":"09"
            ,"10":"10","11":"11","12":"12","13":"13","14":"14","15":"15","16":"16","17":"17","18":"18","19":"19","20":"20"
            ,"21":"21","22":"22","23":"23","24":"次日00","25":"次日01","26":"次日02","27":"次日03","28":"次日04","29":"次日05","30":"次日06"
            ,"31":"次日07","32":"次日08","33":"次日09","34":"次日10","35":"次日11","36":"次日12"};

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/employeeCheckinRule/checkinRulePageList'
                ,cols: [[
                    {field:'name',title:'规则名称',width: 200, align: 'center', unresize: true},
                    {field:'typeName',title:'打卡类型',width: 180, align: 'center', unresize: true,templet:function (d) {
                            if(d.type=='1'){
                                return '固定时间上下班';
                            }else if(d.type=='2'){
                                return '按班次上下班';
                            }else if(d.type=='3'){
                                return '自由上下班';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'fullName',title:'打卡时间', unresize: true,templet:function (d) {


                            if(d.type=='1'){
                                var workDays="";
                                $.each(d.checkinDate,function (index,item) {
                                    var workDayStr="";
                                    var workDaysArray=JSON.parse(item.workDays);
                                    $.each(workDaysArray,function (index2,item2) {
                                        workDayStr+=dataStr[item2]+"、";
                                    });
                                    wokrDaysStr="<div style='color: #000;'>"+workDayStr.substring(0,workDayStr.length-1)+"</div>";
                                    var checkinTimeArray=JSON.parse(item.checkinTime);
                                    $.each(checkinTimeArray,function (index2,item2) {
                                        var workTime=item2[0];
                                        var offWorkTime=item2[1];
                                        var workHour=workTime.substring(0,workTime.indexOf(":"));
                                        var workMinute=workTime.substring(workTime.indexOf(":")+1);
                                        var offWorkHour=offWorkTime.substring(0,offWorkTime.indexOf(":"));
                                        var offWorkMinute=offWorkTime.substring(offWorkTime.indexOf(":")+1);

                                        var checkTime="上班："+hour[workHour]+":"+workMinute+" —— 下班："+hour[offWorkHour]+":"+offWorkMinute;
                                        wokrDaysStr+="<div style='padding-left: 10px;'>"+checkTime+"</div>";
                                    });
                                    workDays+=wokrDaysStr;
                                });
                                return workDays;
                            }else if(d.type=='2'){
                                return '按班次上下班';
                            }else if(d.type=='3'){
                                var wokrDaysStr="";
                                $.each(d.checkinDate,function (index,item) {
                                    var workDaysArray=JSON.parse(item.workDays);
                                    $.each(workDaysArray,function (index2,item2) {
                                        var str=dataStr[item2];
                                        wokrDaysStr+=str+"、";
                                    })
                                });
                                return '<div style="color: #000;">'+wokrDaysStr.substring(0,wokrDaysStr.length-1)+'</div>';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptNames',title:'打卡部门', align: 'center', unresize: true,templet:function (d) {

                            var reg = new RegExp("；","g");
                            return "<div>"+d.deptNames.replace(reg,";<br>")+"</div>";
                        }}
                    ,{field:'empNames',title:'打卡员工', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done: function(res, curr, count){
                    $(".layui-table-main tr").each(function (index, val) {
                        $($(".layui-table-fixed-l .layui-table-body tbody tr")[index]).height($(val).height());
                        $($(".layui-table-fixed-r .layui-table-body tbody tr")[index]).height($(val).height());
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/employeeCheckinRule/form?id='+obj.data.id;
                    pop_show("编辑规则",url,'100%','100%');
                }else if(obj.event==='del'){
                    layer.confirm("确定作废吗?",function(index){
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/employeeCheckinRule/delCheckinRule',
                            data: {'id':obj.data.id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                if(rep.state=='ok'){
                                    reloadTable();
                                }
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index) ;
                    });
                }else if(obj.event==='holidays'){
                    pop_show("节假日",'#(ctxPath)/employeeCheckinRule/holidaysIndex?id='+obj.data.id,700,600);
                }else if(obj.event==='repairSchedule'){
                    var url='#(ctxPath)/employeeCheckinRule/repairScheduleForm?id='+obj.data.id;
                    pop_show("补排班",url,'100%','100%');
                }
            })


        };


    });
</script>
#end