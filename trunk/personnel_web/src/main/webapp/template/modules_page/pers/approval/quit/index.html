#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <div class="layui-input-inline">
                <label class="layui-form-label">姓名：</label>
                <div class="layui-inline" style="float: left;" >
                    <input class="layui-input" name="name" id="name" >
                </div>
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">离职时间</label>
                <div class="layui-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <!--<label class="layui-form-label">状态</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="state" name="state" lay-filter="state">
                    <option value="5">全部</option>
                    <option value="0">待处理</option>
                    <option value="1">提交</option>
                    <option value="2">批准</option>
                    <option value="3">完成</option>
                    <option value="4">失败</option>
                </select>
            </div>-->
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:employeeQuit:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            #shiroHasPermission("emp:employeeQuit:reportForm")
            <button class="layui-btn" id="reportForm" type="button" >离职报表</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
    <input id="quitTransferEmpId" name="quitTransferEmpId" type="hidden" value="">
    <input id="transferFullName" name="transferFullName" type="hidden" value="">
    <input id="transferWorkNum" name="transferWorkNum" type="hidden" value="" >

</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:employeeQuit:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:employeeQuit:editBtn")
    #[[
    {{#if(d.taskId!=undefined && !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

    #[[
    {{#if(d.taskState=='1'){}}
    <a class="layui-btn layui-btn-xs" lay-event="export">导出</a>
    {{#}}}
    ]]#

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        transferEmpTable=function(){
            var url='#(ctxPath)/pers/approval/transferEmpTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }

        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        setQuitTransferEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitTransferEmpId").val(quitEmpId);
            $("#transferFullName").val(fullName);
            $("#transferWorkNum").val(workNum);
        }

        getQuitTransferEmpIdValue=function(){
            return {"quitTransferEmpId":$("#quitTransferEmpId").val(),"transferFullName":$("#transferFullName").val(),"transferWorkNum":$("#transferWorkNum").val()};
        }



        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"name":$("#name").val(),'date':$("#date").val(),'deptId':id});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/employeeQuitForm';
            pop_show("添加离职申请",url,1300,700);
        });
        $("#reportForm").on('click',function (){
            var url='#(ctxPath)/pers/approval/quitReportForm';
            pop_show("离职报表",url,'100%','100%');
        })

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/findEmployeeQuitPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'workNum',title:'工号',width: 80, align: 'center', unresize: true},
                    {field:'fullName',title:'姓名',width: 90, align: 'center', unresize: true},
                    {field:'sex',title:'性别', align: 'center',width: 80, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true}
                    ,{field:'estimate_quit_date', title: '离职时间',align: 'center', unresize: true,width:100}
                    ,{field:'', title: '离职类型',align: 'center',width: 80, unresize: true,templet:function (d) {
                            if(d.quit_type=='dismiss'){
                                return '辞退';
                            }else if(d.quit_type=='autonomy'){
                                return '自离'
                            }else if(d.quit_type=='expel'){
                                return '开除'
                            }else if(d.quit_type=='other'){
                                return '其他'
                            }else if(d.quit_type=='auto_quit'){
                                return '自动离职';
                            }else if(d.quit_type=='voluntarily_quit'){
                                return '个人离职';
                            }else if(d.quit_type=='resign'){
                                return '辞职';
                            }
                        }}
                    ,{field:'reason', title: '离职原因',align: 'center', unresize: true,width:180}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(d.taskId==undefined){
                                return '<span class="layui-badge">未提交</span>';
                            }else{
                                if(d.taskState=='0'){
                                    return '<span class="layui-badge layui-bg-orange">处理中</span>';
                                }else if(d.taskState=='1'){
                                    return '<span class="layui-badge layui-bg-green">结束</span>';
                                }else{
                                    return '中止';
                                }
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/employeeQuitForm?id='+obj.data.id;
                    pop_show("处理流程",url,1300,700);
                }else if(obj.event==='export'){
                    window.location.href='#(ctxPath)/pers/approval/exportChangeApply2?id='+obj.data.id;
                }
            })


        };


    });
</script>
#end