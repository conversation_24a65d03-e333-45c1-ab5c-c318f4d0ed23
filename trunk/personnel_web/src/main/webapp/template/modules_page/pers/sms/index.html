#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位管理#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()

<div class="layui-row">
    <div class="layui-row">
        #shiroHasPermission("notice:sms:addBtn")
        <button class="layui-btn" id="addBtn"  type="button">添加</button>
        #end
    </div>
    <div class="layui-col-md12" style="margin: 10px auto;">
        <table class="layui-table" id="positionTable" lay-filter="positionTable"></table>
    </div>
</div>

#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        sd=form.on("submit(search)",function(data){
            //positionLoad();
            positionTableReload();
            return false;
        });

        positionLoad({});

        function positionLoad(data){
            table.render({
                id : 'positionTable'
                ,elem : '#positionTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,height: 'full-100'
                ,url : '#(ctxPath)/pers/sms/pageList'
                ,cellMinWidth: 80
                ,cols: [[
//                     {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'title', title: '短信标题', align: 'center', unresize: true}
                    ,{field:'content', title: '短信内容', align: 'center', unresize: true}
                    ,{field:'roleName', title: '发送时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.sendTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'status', title: '状态', align: 'center', unresize: true,templet:function (d) {
                            if(new Date()>=new Date(d.sendTime)){
                                return '已发送';
                            }else{
                                return '待发送';
                            }
                        }}
                    ,{field:'createDate', title: '创建时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 150, align: 'center', unresize: true,templet: function (d) {

                            if(new Date()>=new Date(d.sendTime)){
                                var str="";
                                #shiroHasPermission("notice:sms:detailBtn")
                                str+='<a class="layui-btn layui-btn-xs" lay-event="detail">发送详情</a>';
                                #end
                                return str;
                            }else{
                                var str="";
                                #shiroHasPermission("notice:sms:editBtn")
                                str+='<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>';
                                #end
                                #shiroHasPermission("notice:sms:delBtn")
                                str+='<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>'
                                #end
                                return str;
                            }

                        }}
                ]]
                ,page : true
            });
        };

        positionTableReload=function(){
            var data={'positionName':$("#positionName").val(),'orgId':$("#orgId").val()};
            table.reload('positionTable',{'where':data});
        }

        // 添加
        $("#addBtn").click(function(){

            var url = "#(ctxPath)/pers/sms/form";
            pop_show("新增短信",url,600,600);
        });

        table.on('tool(positionTable)',function(obj){
            if (obj.event === 'del') {
                var sendTime=new Date(obj.data.sendTime);
                if(new Date().getTime()>=sendTime){
                    top.layer.msg('该短信发送时间已过，作废失败', {icon: 2, offset: 'auto'});
                    return false;
                }

                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/sms/saveSmsSend?id='+obj.data.id+"&delFlag=1",
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                positionTableReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/pers/sms/form?id=" + obj.data.id ;
                pop_show("编辑短信",url,600,600);
            }else if(obj.event==='detail'){
                var url = "#(ctxPath)/pers/sms/sendDetailIndex?id=" + obj.data.id ;
                pop_show("发送详情",url,800,600);
            }
        });
    });
</script>
<script type="text/html" id="actionBar">

</script>
#end

