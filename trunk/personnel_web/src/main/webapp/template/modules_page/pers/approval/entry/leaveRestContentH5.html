


#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()请休假表单#end

#define css()
<style>

    #steptsTable td,#steptsTable th{
        font-size: 15px;
        border-color: #000000;
        min-height: 39px;
        padding: 9px 5px;
    }
    .layui-disabled, .layui-disabled:hover {
        color: #000000!important;
        cursor: not-allowed!important;
    }
    .layui-form-item{
        margin-bottom: 5px;
    }
</style>
#end


#define js()


<script type="text/javascript">
    layui.extend({
        treeSelect: '/static/js/extend/treeSelect'   // {/}的意思即代表采用自有路径，即不跟随 base 路径
    }).use(['form','jquery','laydate','laytpl','treeSelect','upload','layer'], function(){
        var form = layui.form,$ = layui.jquery,laydate=layui.laydate,layer=layui.layer,laytpl=layui.laytpl,treeSelect=layui.treeSelect,upload=layui.upload;

        //保存员工入职记录流程
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }


        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

    });
</script>
#end

#define content()

<div class="layui-row">
    <form class="layui-form layui-form-pane" action="" id="formId" style="padding-top: 20px;padding-left: 20px;padding-right: 20px;">
        #if(persTask.submitUserName!=null && persTask.submitUserDeptName!=null )
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>流程提交人信息</legend>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人部门</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交人职位</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">提交时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">任务编号</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                </div>
            </div>
        </fieldset>
        #end
        <fieldset class="layui-elem-field layui-field-title" style="display:block;">
            <legend>请休假员工信息</legend>
            <div class="layui-form-item">
                <label class="layui-form-label" style="">姓名</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(employee.fullName??)(#(employee.workNum??))" autocomplete="off" class="layui-input">
                </div>
            </div>
            #if(persTask.submitUserName==null || persTask.submitUserDeptName==null )
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">申请提交时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(record.apply_time??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                </div>
            </div>
            #end
            <div class="layui-form-item">
                <label class="layui-form-label">所在部门</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(deptNames??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">职位</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(positionName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
        </fieldset>
        #if(isNew)
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">请假时长</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(newRecord.leaveDays??)"  autocomplete="off" class="layui-input">
                </div>
            </div>
            #for(dateItem : dateList??)
                <fieldset class="layui-elem-field layui-field-title" style="display:block;">
                    <legend>时间段#(for.index+1)</legend>
                    <div class="layui-form-item">
                        <label class="layui-form-label">请休假类型</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" readonly  value="#(dateItem.leaveRestTypeLabel??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">请时长类型</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" readonly  value="#if(dateItem.leaveRestDateType??=='1')按天数(全天)#elseif(dateItem.leaveRestDateType??=='2')按小时#end" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label layui-form-text">请假时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="title" #if(dateItem.leaveRestDateType??=='2') style="font-size: 11px;padding-left: 0px;" #end readonly  value="#(dateItem.startTime??)至#(dateItem.endTime??)" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    #if(dateItem.leaveRestDateType??=='2')
                    <div class="layui-form-item">
                        <label class="layui-form-label layui-form-text" style="padding: 8px 5px;">休息时间段</label>

                        <div class="layui-input-block" >
                            #for(restTime : dateItem.restTimes??)
                            <input type="text" style="padding-left: 0px;font-size: 11px;" readonly id="restStartTime-#(for.index+1)" #if(!isSaveHandle && taskId!=null) disabled readonly #end required value="#(restTime.restStartTime??)至#(restTime.restEndTime??)"  lay-verify="required" placeholder="请输入开始时间" autocomplete="off" class="layui-input">
                            #end
                        </div>
                    </div>
                    #end
                </fieldset>
            #end
        #else
            <div class="layui-form-item">
                <label class="layui-form-label">请休假类型</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(record.leaveRestTypeName??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">开始时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(record.start_time??,'yyyy-MM-dd') #(record.startDate??)" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">结束时间</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#date(record.end_time??,'yyyy-MM-dd') #(record.endDate??)"  autocomplete="off" class="layui-input">
                </div>
            </div>
            #if(record.leave_rest_date_type=='2')
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">请假时长</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(record.leaveRestHour??)"  autocomplete="off" class="layui-input">
                </div>
            </div>
            #else
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 5px;">请假时长</label>
                <div class="layui-input-block">
                    <input type="text" name="title" readonly  value="#(record.leave_days??)天"  autocomplete="off" class="layui-input">
                </div>
            </div>
            #end
        #end

        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">请假事由</label>
            <div class="layui-input-block">
                <textarea name="desc" placeholder="请输入内容" readonly class="layui-textarea">#(record.remark??)</textarea>
            </div>
        </div>
        #if(fileType!=null)
        <div class="layui-form-item layui-form-text">
            <label class="layui-form-label">凭证文件</label>
            <div class="layui-input-block"  style="margin-top: 3px;">
                #if(fileType??=='png' || fileType??=='jpeg' || fileType??=='jpg')
                <img id="fillFile" src="#(record.file_url??)" width="100%" height="100%" onclick="renderImg('#(record.file_url??)')" />
                #elseif(fileType??=='mp4' || fileType??=='avi' || fileType??=='wmv' || fileType??=='mov' || fileType??=='mpg' || fileType??=='mpeg' || fileType??=='ram'
                || fileType??=='rm' || fileType??=='swf' || fileType??=='flv')
                <video src="#(record.file_url??)" controls="controls" width="100%" height="100%" >
                    您的浏览器不支持 video 标签。
                </video>
                #else
                <div style="margin-top: 15px;margin-left: 5px;text-align: center;font-size: 16px;">
                    <a href="javascript:void(0)" onclick="aClick('#(record.file_url??)')" style="text-decoration:underline;">点击下载#(fileName??)</a>
                </div>
                #end
            </div>
        </div>
        #end
    </form>

</div>


#end








