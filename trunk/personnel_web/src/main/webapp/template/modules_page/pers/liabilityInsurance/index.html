#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">


            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">缴纳月份</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-input-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                    <select name="status" id="status">
                        <option value="">全部</option>
                        <option value="1">未提交</option>
                        <option value="2" selected>处理中</option>
                        <option value="3">审核通过</option>
                        <option value="5">中止</option>
                    </select>
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:liabilityInsurance:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
            #shiroHasPermission("emp:leaveRest:exportBtn")
            <!--<button class="layui-btn" id="export" type="button">导出</button>-->
            #end
            #shiroHasPermission("emp:liabilityInsurance:reportFormBtn")
            <button class="layui-btn" id="reportForm" type="button">报表</button>
            #end
            #shiroHasPermission("emp:liabilityInsurance:summaryBtn")
            <button class="layui-btn" id="summary" type="button">汇总</button>
            #end
        </div>
    </form>

    <div class="layui-row">
        <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    </div>

</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if( d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("emp:leaveRest:editBtn")
    #[[
    {{#if( !d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,type: 'month'
        });

        $("#reportForm").on('click',function () {
            var url="#(ctxPath)/providentFund/liabilityInsuranceReportFormIndex";
            pop_show("雇主责任险报表",url,'100%','100%');
        });

        $("#summary").on('click',function () {
            var url="#(ctxPath)/providentFund/liabilityInsuranceSummaryIndex";
            pop_show("雇主责任险汇总报表",url,'100%','100%');
        })

        msgLoad({"status":"2"});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        $("#export").on('click',function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            window.location.href="#(ctxPath)/pers/approval/leaveRestExport?name="+$("#name").val()+"&date="+$("#date").val()+"&deptId="+id;
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }

        setQuitEmpIdValue=function(quitEmpId,fullName,workNum){
            $("#quitEmpId").val(quitEmpId);
            $("#fullName").val(fullName);
            $("#workNum").val(workNum);
        }
        getQuitEmpIdValue=function(){
            return {"quitEmpId":$("#quitEmpId").val(),"fullName":$("#fullName").val(),"workNum":$("#workNum").val()};
        }

        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"status":$("#status").val(),'date':$("#date").val(),'deptId':id});
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,
                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        updateTitle=function(title){
            $(".layui-layer-title").text(title)
        }

        $("#addBtn").on('click',function () {
            var deptSelect2;
            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "查看详情",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['600px;','600px;'],
                content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
                    "            <div class=\"layui-form-item\" style='margin-top: 20px;'>\n" +
                    "                <label class=\"layui-form-label\"><font color='red'>*</font>缴纳月份</label>\n" +
                    "                <div class=\"layui-input-block\" >\n" +
                    "                    <input class=\"layui-input\" data-id='yearMonth' value='' name=\"yearMonth\" id=\"yearMonth\" autocomplete=\"off\">\n" +
                    "                </div>\n" +
                    "            </div>\n" +
                    "\n" +
                    "            <div class=\"layui-form-item\">\n" +
                    "                <label class=\"layui-form-label\"><font color='red'>*</font>组织架构</label>\n" +
                    "                <div class=\"layui-input-block\"  >\n" +
                    "                    <div id=\"deptSelect2\" >\n" +
                    "\n" +
                    "                    </div>\n" +
                    "                </div>\n" +
                    "            </div>" +
                    "            <div class=\"layui-form-item\">\n" +
                    "                <label class=\"layui-form-label\"><font color='red'>*</font>购买单位</label>\n" +
                    "                <div class=\"layui-input-block\"  >\n" +
                    "<select id='companyId'>" +
                    "                   <option value=''>请选择购买单位</option>" +
                    #for(orgCompany : orgCompanyList)
            "                   <option value='#(orgCompany.id)'>#(orgCompany.name)</option>" +
            #end
            "                </select>"+
            "                </div>\n" +
            "            </div>" +
            "</form>",
                cancel: function(){
            },
            end : function(){
            },yes: function(index, layero){
                var id="";
                $.each(deptSelect2.getValue(),function (index,item) {
                    id=item.id;
                });
                if($("#yearMonth").val()=='' || id=='' || $("#companyId").val()==''){
                    layer.msg('缴纳月份、组织机构、购买单位必须填写', {icon: 2, offset: 'auto'});
                    return false;
                }
                var url="#(ctxPath)/liabilityInsurance/liabilityInsuranceForm?deptId="+id+"&yearMonth="+$("#yearMonth").val()+"&companyId="+$("#companyId").val();
                pop_show("添加雇主责任险购买记录",url,'100%','100%');

                layer.close(index);
                return false;
            }

        });
            form.render();
            laydate.render({
                elem: '#yearMonth'
                ,trigger: 'click'
                ,type: 'month'
                ,min:timeStamp2String(new Date())
                //,max:getNextMonth(new Date())
                //,value:getNextMonth(new Date())
            });


            deptSelect2 = xmSelect.render({
                el: '#deptSelect2',
                autoRow: true,
                height: '200px',
                prop: {
                    name: 'name',
                    value: 'id',
                },
                radio: true,
                filterable: true,//搜索
                tree: {
                    show: true,
                    expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                    showFolderIcon: true,
                    showLine: true,
                    indent: 15,
                    lazy: true,
                    clickExpand: true,
                    clickClose: true,
                    strict: false,
                    //点击节点是否选中
                    clickCheck: true,


                    load: function(item, cb){

                    }
                },
                height: 'auto',
                data(){
                    return [];
                }
            })

            $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
                deptSelect2.update({
                    data:res
                })
            });


        });

        function timeStamp2String(time){

            var datetime = new Date();

            datetime.setTime(time);

            var year = datetime.getFullYear();

            var month = datetime.getMonth() + 1 < 10 ? "0" + (datetime.getMonth() + 1) : datetime.getMonth() + 1;

            /*var date = datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();

            var hour = datetime.getHours()< 10 ? "0" + datetime.getHours() : datetime.getHours();

            var minute = datetime.getMinutes()< 10 ? "0" + datetime.getMinutes() : datetime.getMinutes();

            var second = datetime.getSeconds()< 10 ? "0" + datetime.getSeconds() : datetime.getSeconds();

            return year + "-" + month + "-" + date+" "+hour+":"+minute+":"+second;*/
            return year + "-" + month;
        }

        function getNextMonth(date) {
            var now=date;
            var year = now.getFullYear(); //获取当前日期的年份
            var month = now.getMonth()+1; //获取当前日期的月份
            var day = now.getDate(); //获取当前日期的日
            var days = new Date(year, month, 0);
            days = days.getDate(); //获取当前日期中的月的天数
            var year2 = year;
            var month2 = parseInt(month) + 1;
            if (month2 == 13) {
                year2 = parseInt(year2) + 1;
                month2 = 1;
            }
            var day2 = day;
            var days2 = new Date(year2, month2, 0);
            days2 = days2.getDate();
            if (day2 > days2) {
                day2 = days2;
            }
            if (month2 < 10) {
                month2 = '0' + month2;
            }

            //var t2 = year2 + '-' + month2 + '-' + day2;
            var t2 = year2 + '-' + month2;
            return t2;
        }

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/liabilityInsurance/liabilityInsuranceApplyPageList'
                ,cellMinWidth: 80
                ,cols: [[

                    {field:'dept_name', title: '部门', align: 'center', unresize: true}
                    ,{field:'year_month', title: '缴纳月份', align: 'center', unresize: true}
                    ,{field:'company', title: '购买单位', align: 'center', unresize: true,templet:function (d){
                            return d.company.name
                        }}
                    ,{field:'apply_time', title: '申请日期', align: 'center', unresize: true,templet:function (d){
                            return dateFormat(d.apply_time,"yyyy-MM-dd")
                        }}
                    ,{field:'apply_time', title: '提交人', align: 'center', unresize: true,templet:function (d){
                            return d.apply_user.name+"("+d.apply_user.userName+")"
                        }}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center', unresize: true,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge">未提交</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">审核通过</span>';
                            }else if(d.status=='4'){
                                return '驳回';
                            }else if(d.status=='5'){
                                return '中止';
                            }else if(d.status=='6'){
                                return '撤销';
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url="#(ctxPath)/liabilityInsurance/editLiabilityInsuranceForm?deptId="+obj.data.dept_id+"&yearMonth="+obj.data.year_month+"&companyId="+obj.data.company_id+"&id="+obj.data.id;
                    pop_show("处理雇主责任险买记录流程",url,'100%','100%');

                }
            })


        };


    });
</script>
#end