#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()职位展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
<link rel="stylesheet" href="/static/css/formSelects-v4.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script src="/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery,formSelects=layui.formSelects,laydate=layui.laydate;

        //时间选择器
        laydate.render({
            elem: '#sendTime'
            ,type: 'datetime'
            ,trigger:'click'
        });

        /*var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTree"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    $("#orgId").val(treeNode.id);
                    $("#orgName").val(treeNode.name);
                }
            }
        };

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);*/

        //自定义验证规则
        form.verify({
            otherReq: function(value,item){
                var $ = layui.$;
                var verifyName=$(item).attr('name')
                    , verifyType=$(item).attr('type')
                    ,formElem=$(item).parents('.layui-form')//获取当前所在的form元素，如果存在的话
                    ,verifyElem=formElem.find('input[name='+verifyName+']')//获取需要校验的元素
                    ,isTrue= verifyElem.is(':checked')//是否命中校验
                    ,focusElem = verifyElem.next().find('i.layui-icon');//焦点元素
                if(!isTrue || !value){
                    //定位焦点
                    focusElem.css(verifyType=='radio'?{"color":"#FF5722"}:{"border-color":"#FF5722"});
                    //对非输入框设置焦点
                    focusElem.first().attr("tabIndex","").css("outline","").blur(function() {
                        focusElem.css(verifyType=='radio'?{"color":""}:{"border-color":""});
                    }).focus();
                    return '必填项不能为空';
                }
            }
        });


            //保存
        form.on('submit(saveBtn)', function(){

            var depts=deptSelect.getValue();
            var deptIds=[];
            $.each(depts,function (index,item) {
                deptIds.push(item.id);
            })
            if(deptIds.length==0){
                layer.msg('请添加接收组织机构', {icon: 2});
                return false;
            }

            var url = "#(ctxPath)/pers/sms/saveSmsSend";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#positionForm").serialize()+"&deptIds="+deptIds,
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        //parent.table.reload('positionTable',{'where':{'orgId':$("#orgId").val()}});
                        pop_close();
                        parent.positionTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });



        var deptSelectInitValue=[];
        #for(deptId : deptIdsList)
        deptSelectInitValue.push("#(deptId??)");
        #end

        var deptSelect;
        $.post('#(ctxPath)/persOrg/orgXmSelectTree',{},function (res) {
            deptSelect = xmSelect.render({
                el: '#deptSelect',
                autoRow: true,
                filterable: true,
                height: '200px',
                prop: {
                    name: 'name',
                    value: 'id',
                },
                tree: {
                    show: true,
                    showFolderIcon: true,
                    showLine: true,
                    indent: 15,
                    lazy: true,
                    clickExpand: true,
                    strict: false,
                    expandedKeys: ['54325705-FF63-43DB-9723-FA31E94AF8E3'],
                    //点击节点是否选中
                    clickCheck: true,
                    load: function(item, cb){

                    }
                },
                height: 'auto',
                data(){
                    return res;
                },
                initValue: deptSelectInitValue
            });
        });

    });
</script>
#end

#define content()

<!--<div class="layui-col-xs3 layui-col-sm3 layui-col-md3 layui-col-lg3">
    <fieldset class="layui-elem-field layui-field-title" style="display:block;">
        <legend>组织架构</legend>
        <div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
    </fieldset>
</div>
<div class="layui-col-xs9 layui-col-sm9 layui-col-md9 layui-col-lg9">

</div>-->
<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left:5px;" id="positionForm">
    <input type="hidden" name="id" value="#(model.id??)"/>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label"><span style="color: red">*</span>短信标题</label>
        <div class="layui-input-block">
            <input type="text" name="title" class="layui-input" lay-verify="required" autocomplete="off" value="#(model.title??)" placeholder="请输入短信标题">
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label"><span style="color: red">*</span>发送时间</label>
        <div class="layui-input-block">
            <input type="text" id="sendTime" name="sendTime" class="layui-input" lay-verify="required" autocomplete="off" value="#date(model.sendTime??,'yyyy-MM-dd HH:mm:ss')" placeholder="请选择发送时间">
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label"><span style="color: red">*</span>短信内容</label>
        <div class="layui-input-block">
            <textarea placeholder="请输入短信内容" name="content" autocomplete="off" class="layui-textarea">#(model.content??)</textarea>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label" style="padding: 8px 5px;"><span style="color: red">*</span>接收组织机构</label>
        <div class="layui-input-block">
            <div id="deptSelect" style="">

            </div>
        </div>
    </div>

    <div class="layui-form-footer">
        <div class="pull-right">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
