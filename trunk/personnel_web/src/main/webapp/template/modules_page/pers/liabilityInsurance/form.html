#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公积金购买申请流程#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
    .layui-disabled, .layui-disabled:hover {
        color: #000000 !important;
        cursor: not-allowed !important;
    }
    .layui-input {
        color: #000;
    }
    .layui-col-md8 .layui-table td,.layui-col-md8 .layui-table th {
        position: relative;
        padding: 9px 5px;
        min-height: 20px;
        line-height: 20px;
        font-size: 14px;
    }
    .layui-btn-red{
        background-color: red;
    }
</style>
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end


#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery','table'], function(){
        var form = layui.form,$ = layui.jquery,table=layui.table;
        #if(type??=='edit')

        #else
        parent.updateTitle("添加[#(deptName??)]#(yearMonth??)雇主责任险购买记录");

        #end

        #if(isSaveHandle || taskId==null)

        #else
        /*$(".layui-col-md8 button").css("","");*/
        $(".layui-col-md8 button").prop("disabled", true);
        $(".layui-col-md8 button").addClass("layui-btn-disabled")
        $(".layui-col-md8").find("input,select,textarea").prop("disabled", true).prop("readonly", true);
        $(".layui-btn-red").attr("class","layui-btn layui-btn-sm layui-btn-disabled");
        //$(".layui-col-md8 select").removeAttr("lay-search");
        $(".layui-col-md8 .exportBtn").prop("disabled", false);
        $(".layui-col-md8 .exportBtn").removeClass("layui-btn-disabled");

        #if(allowApprove && currentStepAlias=='financeHandle')
        $(".financeHandleClass").prop("disabled", false).prop("readonly", false);
        $(".financeHandleClass").removeClass("layui-btn-disabled");
        #end

        form.render('select');
        form.render('radio');
        #end

        remove=function(empId,type){
            var tbodyId="";
            if(type=='1'){
                tbodyId="addTbody";
            }else if(type=='2'){
                tbodyId="continueTbody";
            }else if(type=='3'){
                tbodyId="delTbody";
            }else if(type=='4'){
                tbodyId="abandonTbody";
            }
            $("#"+tbodyId).find("tr[data-id='"+empId+"']").remove();

            $("#"+tbodyId).find("tr").each(function (index,item) {
                $(item).find("td:first").text(index+1);
            });
        }

        move=function(empId,payRatio,type,oldType) {
            var oldTbodyId="";
            var newTbodyId="";
            if(oldType=='1'){
                oldTbodyId="addTbody";
            }else if(oldType=='2'){
                oldTbodyId="continueTbody";
            }else if(oldType=='3'){
                oldTbodyId="delTbody";
            }else if(oldType=='4'){
                oldTbodyId="abandonTbody";
            }

            var lastMonthType=$("#lastMonthType"+empId).val();

            /*if(("1"==lastMonthType || "2"==lastMonthType) && type=="4"){
                //增、续移动到弃
                layer.msg('上月状态为[增、续]的本月不能移动至[弃]，请先移动至[删]',{icon:5});
                return false;
            }

            if(("3"==lastMonthType || "4"==lastMonthType) && type=="2"){
                //增、续移动到弃
                layer.msg('上月状态为[删、弃]的本月不能移动至[续]，请先移动至[增]',{icon:5});
                return false;
            }

            if(("2"==lastMonthType) && type=="1"){
                //增、续移动到弃
                layer.msg('上月状态为[续]的本月不能移动至[增]',{icon:5});
                return false;
            }

            if(("4"==lastMonthType) && type=="3"){
                //增、续移动到弃
                layer.msg('上月状态为[弃]的本月不能移动至[删]',{icon:5});
                return false;
            }*/

            var buttonHtml="";
            if(type=='1'){
                newTbodyId="addTbody";
                buttonHtml='<div class="layui-btn-group"> <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move(\''+empId+'\',\'\',\'2\',\'1\')">续买</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move(\''+empId+'\',\'\',\'3\',\'1\')">删除</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move(\''+empId+'\',\'\',\'4\',\'1\')">弃买</button></div>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'1\')">移除</button>';
            }else if(type=='2'){
                newTbodyId="continueTbody";
                buttonHtml='<div class="layui-btn-group"> <button type="button" class="layui-btn layui-btn-sm" onclick="move(\''+empId+'\',\'\',\'1\',\'2\')">新增</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move(\''+empId+'\',\'\',\'3\',\'2\')">删除</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move(\''+empId+'\',\'\',\'4\',\'2\')">弃买</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'2\')">移除</button></div>';
            }else if(type=='3'){
                newTbodyId="delTbody";
                buttonHtml=' <div class="layui-btn-group"><button type="button" class="layui-btn layui-btn-sm" onclick="move(\''+empId+'\',\'\',\'1\',\'3\')">新增</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move(\''+empId+'\',\'\',\'2\',\'3\')">续买</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move(\''+empId+'\',\'\',\'4\',\'3\')">弃买</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'3\')">移除</button></div>';
            }else if(type=='4'){
                newTbodyId="abandonTbody";
                buttonHtml=' <div class="layui-btn-group"><button type="button" class="layui-btn layui-btn-sm" onclick="move(\''+empId+'\',\'\',\'1\',\'4\')">新增</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move(\''+empId+'\',\'\',\'2\',\'4\')">续买</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move(\''+empId+'\',\'\',\'3\',\'4\')">删除</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'4\')">移除</button></div>';
            }
            var html=$("#"+oldTbodyId).find("tr[data-id='"+empId+"']").prop("outerHTML");;
            $("#"+oldTbodyId).find("tr[data-id='"+empId+"']").remove();
            $("#"+newTbodyId).append(html);
            $("#"+newTbodyId).find("tr[data-id='"+empId+"']").find("td:last").html(buttonHtml);
            $("#"+oldTbodyId).find("tr").each(function (index,item) {
                $(item).find("td:first").text(index+1);
            });
            $("#"+newTbodyId).find("tr").each(function (index,item) {
                $(item).find("td:first").text(index+1);
            });
            form.render();
        }

        $("#saveBtn").on('click',function () {
            var data=[];
            $("#addTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var age=$("#age"+empId).val();
                data.push({"empId":empId,"age":age,"type":"1","companyId":$("#companySelect"+empId).val(),"id":id});
            });
            $("#continueTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var age=$("#age"+empId).val();
                data.push({"empId":empId,"age":age,"type":"2","companyId":$("#companySelect"+empId).val(),"id":id});
            });
            $("#delTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var age=$("#age"+empId).val();
                data.push({"empId":empId,"age":age,"type":"3","companyId":$("#companySelect"+empId).val(),"id":id});
            });
            $("#abandonTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var age=$("#age"+empId).val();
                data.push({"empId":empId,"age":age,"type":"4","companyId":$("#companySelect"+empId).val(),"id":id});
            });
            if(data.length<=0){
                layer.msg('操作失败，提交的记录条数必须大于0条',{icon:5});
                return false;
            }
            var taskId=$("#taskId").val();

            var url=""
            if(taskId=='') {
                url = "#(ctxPath)/liabilityInsurance/liabilityInsuranceApplySave";
            }else{
                url = "#(ctxPath)/liabilityInsurance/liabilityInsuranceApplyEdit";
            }

            util.sendAjax ({
                type: 'POST',
                url: url,
                data: {"data":JSON.stringify(data),"deptId":$("#deptId").val(),"yearMonth":$("#yearMonth").val(),"companyId":$("#companyId").val(),"id":$("#id").val(),"taskId":$("#taskId").val(),"remark":$("#remark").val()},
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });
        })


        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        });

        addTr=function(empId,workNum,fullName,idcard,maritalStatus,isLocal,phoneNum,type,idCardAddr,age,lastMonthType,sex,deptName,positionName){
            var tbodyId="";
            if(type=='1'){
                tbodyId="addTbody";
            }else if(type=='2'){
                tbodyId="continueTbody";
            }else if(type=='3'){
                tbodyId="delTbody";
            }else if(type=='4'){
                tbodyId="abandonTbody";
            }
            var flag=false;
            $("#addTbody").find("tr").each(function (index,item) {
                var trEmpId=$(item).attr("data-id");
                if(empId==trEmpId){
                    flag=true;
                    return false;
                }
            });
            if(flag){
                window.top.layer.msg('添加失败，该员工已经在新增表格中',{icon:5});
                return false;
            }
            $("#continueTbody").find("tr").each(function (index,item) {
                var trEmpId=$(item).attr("data-id");
                if(empId==trEmpId){
                    flag=true;
                    return false;
                }
            });
            if(flag){
                window.top.layer.msg('添加失败，该员工已经在续买表格中',{icon:5});
                return false;
            }
            $("#delTbody").find("tr").each(function (index,item) {
                var trEmpId=$(item).attr("data-id");
                if(empId==trEmpId){
                    flag=true;
                    return false;
                }
            });
            if(flag){
                window.top.layer.msg('添加失败，该员工已经在删除表格中',{icon:5});
                return false;
            }
            $("#abandonTbody").find("tr").each(function (index,item) {
                var trEmpId=$(item).attr("data-id");
                if(empId==trEmpId){
                    flag=true;
                    return false;
                }
            });
            if(flag){
                window.top.layer.msg('添加失败，该员工已经在弃买表格中',{icon:5});
                return false;
            }

            var btnStr="";
            if(type=='1'){
                btnStr= '<div class="layui-btn-group"><button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'2\',\'1\')">续买</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'3\',\'1\')">删除</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'4\',\'1\')">弃买</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'1\')">移除</button>' +
                    '</div>' ;
            }else if(type=='2'){
                btnStr='<div class="layui-btn-group"><button type="button" class="layui-btn layui-btn-sm" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'1\',\'2\')">新增</button>' +

                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'3\',\'2\')">删除</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'4\',\'2\')">弃买</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'2\')">移除</button>' +
                    '</div>' ;
            }else if(type=='3'){
                btnStr='<div class="layui-btn-group"><button type="button" class="layui-btn layui-btn-sm" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'1\',\'3\')">新增</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'2\',\'3\')">续买</button>\n' +
                    '' +

                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'4\',\'3\')">弃买</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'3\')">移除</button>' +
                    '</div>' ;
            }else if(type=='4'){
                btnStr='<div class="layui-btn-group"><button type="button" class="layui-btn layui-btn-sm" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'1\',\'4\')">新增</button>' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'2\',\'4\')">续买</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move(\''+empId+'\',\'#(add.payRatio??)\',\'3\',\'4\')">删除</button>\n' +
                    '<button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove(\''+empId+'\',\'4\')">移除</button>' +
                    '</div>'  ;
            }
            var trNum=$("#"+tbodyId).find('tr:last').find('td:first').text();
            var newNum=Number(trNum)+1;

            var lastMonthTypeStr='<td><input type="hidden" value="'+lastMonthType+'" id="lastMonthType'+empId+'">';

            if(lastMonthType=='1'){
                lastMonthTypeStr+='<span class="layui-badge" style="background-color:#009688;">增</span>';
            }else if(lastMonthType=='2'){
                lastMonthTypeStr+= '<span class="layui-badge layui-bg-normal" style="background-color:#1E9FFF;">续</span>';
            }else if(lastMonthType=='3'){
                lastMonthTypeStr+= '<span class="layui-badge layui-bg-warm" style="background-color:#FFB800;">删</span>';
            }else if(lastMonthType=='4'){
                lastMonthTypeStr+= '<span class="layui-badge layui-bg-danger" style="background-color:#FF5722;">弃</span>';
            }
            lastMonthTypeStr+='</td>'

            var html='<tr data-id="'+empId+'" id="">\n' +
                '<td style="text-align: center;">'+newNum+'</td>\n' +
                '                            <td>'+workNum+'</td>\n' +
                '                            <td sex="'+sex+'" deptName="'+deptName+'"  positionName="'+positionName+'" >'+fullName+'</td>\n' +
                '                            <td>'+idcard+'</td>\n' +
                '                            <td><div class="layui-input-inline" style="width: 240px;" >\n' +
                '                       <input type="text" id="age'+empId+'" placeholder="年龄"  value="'+age+'" autocomplete="off" class="layui-input">\n '+
                '                           </div>\n' +
                '                            </td>\n' +

                '                            <td>\n' +
                '                                <div class="layui-input-inline" style="width: 300px;">\n' +
                '                                    <select id="companySelect'+empId+'">\n' +
                '                                        #for(orgCompany : orgCompanyList)\n' +
                '                                        <option value="#(orgCompany.id)" #if(companyId??==orgCompany.id) selected #end >#(orgCompany.name)</option>\n' +
                '                                        #end\n' +
                '                                    </select>\n' +
                '                                </div>\n' +
                '                            </td>\n' +

                /*'                            <td>'+phoneNum+'</td>\n' +*/
                lastMonthTypeStr+
                '                            <td>\n' +
                btnStr+
                '                            </td>\n' +
                '                        </tr>';



            $("#"+tbodyId).append(html);
            console.log(maritalStatus);
            console.log(isLocal)
            $("#maritalStatus"+empId).find("option[value='"+maritalStatus+"']").prop("selected",true);
            $("#isLocal"+empId).find("option[value='"+isLocal+"']").prop("selected",true);
            layuiTableMouseenter();

            form.render();
            window.top.layer.msg('添加成功',{icon:1});
        }

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });

        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/approval/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        parent.reloadTable();
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
        }

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/liabilityInsurance/empTable';
            pop_show("员工表",url,1320,700);
        });

        $("#exportBtn").on('click',function () {
            layer.load();
            var url='#(ctxPath)/api/liabilityInsuranceExport?taskId='+$("#taskId").val();
            window.location.href=url;
            setTimeout(function (){
                layer.closeAll("loading");
            },2000);
        })

        /*form.render('select(status)',function (obj) {
            console.log(obj);
        })*/


        form.on('select(filter)', function(data){
            var empId=$(data.elem).attr("data-id");
            if(data.value=='1'){
                $("#exceptionRemark"+empId).css("display","none");
            }else if(data.value=='0'){
                $("#exceptionRemark"+empId).css("display","block");
            }
        });

        $("#saveStatus").on('click',function () {
            var data=[];
            $("#addTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var status=$("#status"+empId).val()
                var exceptionRemark=$("#exceptionRemark"+empId).val();
                data.push({"empId":empId,"status":status,"exceptionRemark":exceptionRemark});
            });
            $("#continueTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var status=$("#status"+empId).val()
                var exceptionRemark=$("#exceptionRemark"+empId).val();
                data.push({"empId":empId,"status":status,"exceptionRemark":exceptionRemark});
            });
            /*$("#delTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var status=$("#status"+empId).val()
                var exceptionRemark=$("#exceptionRemark"+empId).val();
                data.push({"empId":empId,"status":status,"exceptionRemark":exceptionRemark});
            });
            $("#abandonTbody").find("tr").each(function (index,item) {
                var empId=$(item).attr("data-id");
                var id=$(item).attr("id");
                var status=$("#status"+empId).val()
                var exceptionRemark=$("#exceptionRemark"+empId).val();
                data.push({"empId":empId,"status":status,"exceptionRemark":exceptionRemark});
            });*/
            var empName="";
            var flag=false;
            var flag2=false;
            $.each(data,function (index,item) {
                empName=$("tbody").find("tr[data-id='"+item.empId+"']").children().eq(1).text();
                if(item.status==''){
                    flag2=true;
                    return false;
                }

                if(item.status=='0'){
                    if(item.exceptionRemark==undefined || item.exceptionRemark==''){
                        flag=true;
                        empName=$("tbody").find("tr[data-id='"+item.empId+"']").children().eq(1).text();
                        return false;
                    }
                }
            });
            if(flag2){
                layer.msg('请选择['+empName+']的购买情况',{icon:5});
                return false;
            }
            if(flag){
                layer.msg('['+empName+']购买失败的原因必须要填写',{icon:5});
                return false;
            }

            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/api/liabilityInsuranceSaveStatus',
                data: {"data":JSON.stringify(data),"taskId":$("#taskId").val()},
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        //pop_close();
                        //parent.reloadTable();
                    }
                },
                complete : function() {
                }
            });

        })

        layuiTableMouseenter();

        function layuiTableMouseenter() {
            $(".layui-table").each(function (index,item) {
                var tableId=$(item).attr("id");
                if(tableId.indexOf("entryApprovalTable")!=-1){
                    $(item).find("tbody tr").each(function (trIndex,trItem) {
                        var nameTd=$(trItem).children("td").eq(2);

                        var layerTips;

                        var sex=$(nameTd).attr('sex');
                        var deptName=$(nameTd).attr('deptName');
                        var positionName=$(nameTd).attr('positionName');

                        nameTd.on("mouseenter", function() {
                            /*if (this.offsetWidth < this.firstChild.scrollWidth) {

                                var text = $(this).text();

                            }*/
                            var that = this;

                            layerTips=layer.tips('<p>性别：'+sex+'</p><p>部门：'+deptName+'</p><p>职位：'+positionName+'</p>', that, {
                                tips: 1,
                                time: 0,
                                area: ['300px', 'auto']
                            });
                        });

                        nameTd.on("mouseleave", function() {
                            layer.close(layerTips);
                        });


                    })
                }
            });
        }


    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-top: 20px;margin-left:5px;" id="positionForm">
    <input id="deptId" type="hidden" value="#(deptId??)">
    <input id="deptName" type="hidden" value="#(deptName??)">
    <input id="yearMonth" type="hidden" value="#(yearMonth??)">
    <input id="companyId" type="hidden" value="#(companyId??)">
    <input id="taskId" type="hidden" value="#(taskId??)">
    <input id="id" type="hidden" value="#(id??)">

    <div class="layui-col-md8" #if(device??=='mobile') style="min-width: 1100px;"#else style="min-width: 1100px;" #end >
        <div class="layui-row">
            #if(device??=='mobile' && persTask.submitUserName!=null)
            <fieldset class="layui-elem-field layui-field-title" style="display:block;">
                <legend>流程提交人信息</legend>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交人姓名</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.submitUserName??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交人部门</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.submitUserDeptName??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交人职位</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.submitUserPositionName??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">提交时间</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#date(persTask.applyTime??,'yyyy-MM-dd HH:mm:ss')" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="">任务编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="title" readonly  value="#(persTask.taskNumber??)" autocomplete="off" class="layui-input">
                    </div>
                </div>
            </fieldset>

            #end
        </div>
        <div class="layui-row">
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注内容" name="remark" id="remark" class="layui-textarea">#(applyRecord.remark??)</textarea>
                </div>
            </div>
        </div>
        <div class="layui-row">
            <button type="button" style="float: right;margin-right: 10px;" class="layui-btn layui-btn-sm" id="addBtn">添加记录</button>
            #if(allowApprove)
            <button type="button" style="float: right;margin-right: 10px;" class="layui-btn layui-btn-sm exportBtn" id="exportBtn">导出</button>
            #end
        </div>
        <div class="layui-row" style="padding-right: 2px;">
            <fieldset class="layui-elem-field layui-field-title" style="display: block;">
                <legend>1.增</legend>
                <div class="layui-field-box">
                    <table id="entryApprovalTable" lay-filter="entryApprovalTable" class="layui-table">
                        <colgroup>
                            <!--<col width="150">
                            <col width="200">
                            <col>-->
                        </colgroup>
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>工号</th>
                            <th>姓名</th>
                            <th>证件号码</th>
                            <th>年龄</th>
                            <!--<th>是否本地户籍</th>-->
                            <th>购买单位</th>
                            <!--<th>是否第一次购买</th>-->
                            <!--<th>手机号码</th>-->
                            <th>上月类型</th>
                            #if(currentStepAlias??=="submit" || taskId==null)
                            <th>操作</th>
                            #else
                            <th>购买情况</th>
                            #end
                        </tr>
                        </thead>
                        <tbody id="addTbody">
                        #for(add : addList)
                        <tr data-id="#(add.empId)" id="#(add.id??)">
                            <td style="text-align: center;">#(for.index+1)</td>
                            <td>#(add.workNum??)</td>
                            <td sex="#(add.sex??)" positionName="#(add.positionName??)" deptName="#(add.deptName??)" >#(add.fullName??)</td>
                            <td>#(add.idcard??)</td>

                            <td>
                                <div class="layui-input-inline" >
                                    <input type="text" id="age#(add.empId)" placeholder="年龄"  value="#(add.age??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <select name="isLocal" id="isLocal#(add.empId)" lay-verify="">
                                        <option value="">户籍</option>
                                        <option value="1" #if(add.isLocal??=='1') selected #end>是</option>
                                        <option value="0" #if(add.isLocal??=='0') selected #end>否</option>
                                    </select>
                                </div>
                            </td>-->
                            <td>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <select id="companySelect#(add.empId)">
                                        #for(orgCompany : orgCompanyList)
                                        <option value="#(orgCompany.id)" #if(add.companyId??==orgCompany.id) selected #elseif(orgCompany.id==companyId??) selected #end >#(orgCompany.name)</option>
                                        #end
                                    </select>
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="isFirst#(add.empId)">
                                        <option value="0" #if(add.isFirst=='0') selected #end>否</option>
                                        <option value="1" #if(add.isFirst=='1') selected #end>是</option>
                                    </select>
                                </div>
                            </td>-->
                            <!--<td>#(add.phoneNum??)</td>-->
                            <td>
                                <input type="hidden" value="#(add.lastMonthType??)" id="lastMonthType#(add.empId??)">
                                #if(add.lastMonthType=='1')
                                <span class="layui-badge" style="background-color:#009688;">增</span>
                                #else if(add.lastMonthType=='2')
                                <span class="layui-badge layui-bg-normal" style="background-color:#1E9FFF;">续</span>
                                #else if(add.lastMonthType=='3')
                                <span class="layui-badge layui-bg-warm" style="background-color:#FFB800;">删</span>
                                #else if(add.lastMonthType=='4')
                                <span class="layui-badge layui-bg-danger" style="background-color:#FF5722;">弃</span>
                                #end
                            </td>
                            <td>
                                #if(currentStepAlias??=="submit" || taskId==null)
                                <div class="layui-btn-group">
                                    <!--<button type="button" class="layui-btn layui-btn-sm" onclick="move('#(add.empId??)','#(add.payRatio??)','1','')">新增</button>-->
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move('#(add.empId??)','#(add.payRatio??)','2','1')">续买</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move('#(add.empId??)','#(add.payRatio??)','3','1')">删除</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move('#(add.empId??)','#(add.payRatio??)','4','1')">弃买</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove('#(add.empId??)','1')">移除</button>
                                </div>
                                #else
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="status#(add.empId)" data-id="#(add.empId)" class="financeHandleClass" lay-filter="filter">
                                        <option value="">选择</option>
                                        <option value="1" #if(add.status??=='1' || (add.status==null && currentStepAlias??=='financeHandle' && allowApprove)) selected #end >成功</option>
                                        <option value="0" #if(add.status??=='0') selected #end >失败</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline"  >
                                    <input type="text" #if(add.status??=='0') style="display: block" #else style="display: none" #end id="exceptionRemark#(add.empId)" placeholder="请输入原因"  value="#(add.exceptionRemark??)" autocomplete="off" class="layui-input financeHandleClass">
                                </div>
                                #end
                            </td>
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>

        <div class="layui-row">
            <fieldset class="layui-elem-field layui-field-title" style="display: block;">
                <legend>2.续</legend>
                <div class="layui-field-box">
                    <table id="entryApprovalTable2" lay-filter="entryApprovalTable2" class="layui-table">
                        <colgroup>
                            <!--<col width="150">
                            <col width="200">
                            <col>-->
                        </colgroup>
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>工号</th>
                            <th>姓名</th>
                            <th>证件号码</th>
                            <th>年龄</th>
                            <!--<th>是否本地户籍</th>-->
                            <th>购买单位</th>
                            <!--<th>是否第一次购买</th>-->
                            <!--<th>手机号码</th>-->
                            <th>上月类型</th>
                            #if(currentStepAlias??=="submit" || taskId==null)
                            <th>操作</th>
                            #else
                            <th>购买情况</th>
                            #end
                        </tr>
                        </thead>
                        <tbody id="continueTbody">
                        #for(add : continueList)
                        <tr data-id="#(add.empId)" id="#(add.id??)">
                            <td style="text-align: center;">#(for.index+1)</td>
                            <td>#(add.workNum??)</td>
                            <td sex="#(add.sex??)" positionName="#(add.positionName??)" deptName="#(add.deptName??)" >#(add.fullName??)</td>
                            <td>#(add.idcard??)</td>
                            <td>
                                <div class="layui-input-inline" >
                                    <input type="text" id="age#(add.empId)" placeholder="年龄"  value="#(add.age??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 240px;" >
                                    <input type="text" id="idCardAddr#(add.empId)" placeholder="户口"  value="#(add.idCardAddr??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>-->
                            <!--<td>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <select name="isLocal" id="isLocal#(add.empId)" lay-verify="">
                                        <option value="">户籍</option>
                                        <option value="1" #if(add.isLocal??=='1') selected #end>是</option>
                                        <option value="0" #if(add.isLocal??=='0') selected #end>否</option>
                                    </select>
                                </div>
                            </td>-->
                            <td>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <select id="companySelect#(add.empId)">
                                        #for(orgCompany : orgCompanyList)
                                        <option value="#(orgCompany.id)" #if(add.companyId??==orgCompany.id) selected #elseif(orgCompany.id==companyId??) selected #end >#(orgCompany.name)</option>
                                        #end
                                    </select>
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="isFirst#(add.empId)">
                                        <option value="0" #if(add.isFirst=='0') selected #end>否</option>
                                        <option value="1" #if(add.isFirst=='1') selected #end>是</option>
                                    </select>
                                </div>
                            </td>-->
                            <!--<td>#(add.phoneNum??)</td>-->
                            <td>
                                <input type="hidden" value="#(add.lastMonthType??)" id="lastMonthType#(add.empId??)">
                                #if(add.lastMonthType=='1')
                                <span class="layui-badge" style="background-color:#009688;">增</span>
                                #else if(add.lastMonthType=='2')
                                <span class="layui-badge layui-bg-normal" style="background-color:#1E9FFF;">续</span>
                                #else if(add.lastMonthType=='3')
                                <span class="layui-badge layui-bg-warm" style="background-color:#FFB800;">删</span>
                                #else if(add.lastMonthType=='4')
                                <span class="layui-badge layui-bg-danger" style="background-color:#FF5722;">弃</span>
                                #end
                            </td>
                            <td>
                                #if(currentStepAlias??=="submit" || taskId==null)
                                <div class="layui-btn-group">
                                    <button type="button" class="layui-btn layui-btn-sm" onclick="move('#(add.empId??)','#(add.payRatio??)','1','2')">新增</button>
                                    <!--<button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move('#(add.empId??)','#(add.payRatio??)','2','1')">续买</button>-->
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move('#(add.empId??)','#(add.payRatio??)','3','2')">删除</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move('#(add.empId??)','#(add.payRatio??)','4','2')">弃买</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove('#(add.empId??)','2')">移除</button>
                                </div>
                                #else
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="status#(add.empId)" data-id="#(add.empId)" class="financeHandleClass" lay-filter="filter">
                                        <option value="">选择</option>
                                        <option value="1" #if(add.status??=='1' || (add.status==null && currentStepAlias??=='financeHandle' && allowApprove)) selected #end >成功</option>
                                        <option value="0" #if(add.status??=='0') selected #end >失败</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline"  >
                                    <input type="text" #if(add.status??=='0') style="display: block" #else style="display: none" #end id="exceptionRemark#(add.empId)" placeholder="请输入原因"  value="#(add.exceptionRemark??)" autocomplete="off" class="layui-input financeHandleClass">
                                </div>
                                #end

                            </td>
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>

        <div class="layui-row">
            <fieldset class="layui-elem-field layui-field-title" style="display: block;">
                <legend>3.删</legend>
                <div class="layui-field-box">
                    <table id="entryApprovalTable3" lay-filter="entryApprovalTable3" class="layui-table">
                        <colgroup>
                            <!--<col width="150">
                            <col width="200">
                            <col>-->
                        </colgroup>
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>工号</th>
                            <th>姓名</th>
                            <th>证件号码</th>
                            <th>年龄</th>
                            <!--<th>是否本地户籍</th>-->
                            <th>购买单位</th>
                            <!--<th>是否第一次购买</th>-->
                            <!--<th>手机号码</th>-->
                            <th>上月类型</th>
                            #if(currentStepAlias??=="submit" || taskId==null)
                            <th>操作</th>
                            #else
                            <th>购买情况</th>
                            #end
                        </tr>
                        </thead>
                        <tbody id="delTbody">
                        #for(add : delList)
                        <tr data-id="#(add.empId)" id="#(add.id??)">
                            <td style="text-align: center;">#(for.index+1)</td>
                            <td>#(add.workNum??)</td>
                            <td sex="#(add.sex??)" positionName="#(add.positionName??)" deptName="#(add.deptName??)" >#(add.fullName??)</td>
                            <td>#(add.idcard??)</td>
                            <td>
                                <div class="layui-input-inline" >
                                    <input type="text" id="age#(add.empId)" placeholder="年龄"  value="#(add.age??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 240px;" >
                                    <input type="text" id="idCardAddr#(add.empId)" placeholder="户口"  value="#(add.idCardAddr??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>-->
                            <!--<td>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <select name="isLocal" id="isLocal#(add.empId)" lay-verify="">
                                        <option value="">户籍</option>
                                        <option value="1" #if(add.isLocal??=='1') selected #end>是</option>
                                        <option value="0" #if(add.isLocal??=='0') selected #end>否</option>
                                    </select>
                                </div>
                            </td>-->
                            <td>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <select id="companySelect#(add.empId)">
                                        #for(orgCompany : orgCompanyList)
                                        <option value="#(orgCompany.id)" #if(add.companyId??==orgCompany.id) selected #elseif(orgCompany.id==companyId??) selected #end >#(orgCompany.name)</option>
                                        #end
                                    </select>
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="isFirst#(add.empId)">
                                        <option value="0" #if(add.isFirst=='0') selected #end>否</option>
                                        <option value="1" #if(add.isFirst=='1') selected #end>是</option>
                                    </select>
                                </div>
                            </td>-->
                            <!--<td>#(add.phoneNum??)</td>-->
                            <td>
                                <input type="hidden" value="#(add.lastMonthType??)" id="lastMonthType#(add.empId??)">
                                #if(add.lastMonthType=='1')
                                <span class="layui-badge" style="background-color:#009688;">增</span>
                                #else if(add.lastMonthType=='2')
                                <span class="layui-badge layui-bg-normal" style="background-color:#1E9FFF;">续</span>
                                #else if(add.lastMonthType=='3')
                                <span class="layui-badge layui-bg-warm" style="background-color:#FFB800;">删</span>
                                #else if(add.lastMonthType=='4')
                                <span class="layui-badge layui-bg-danger" style="background-color:#FF5722;">弃</span>
                                #end
                            </td>
                            <td>
                                #if(currentStepAlias??=="submit" || taskId==null)
                                <div class="layui-btn-group">
                                    <button type="button" class="layui-btn layui-btn-sm" onclick="move('#(add.empId??)','#(add.payRatio??)','1','')">新增</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move('#(add.empId??)','#(add.payRatio??)','2','3')">续买</button>
                                    <!--<button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move('#(add.empId??)','#(add.payRatio??)','3','3')">删除</button>-->
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move('#(add.empId??)','#(add.payRatio??)','4','3')">弃买</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove('#(add.empId??)','3')">移除</button>
                                </div>

                                #else
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="status#(add.empId)" data-id="#(add.empId)" class="financeHandleClass" lay-filter="filter">
                                        <option value="">选择</option>
                                        <option value="1" #if(add.status??=='1' || (add.status==null && currentStepAlias??=='financeHandle' && allowApprove)) selected #end >成功</option>
                                        <option value="0" #if(add.status??=='0') selected #end >失败</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline"  >
                                    <input type="text" #if(add.status??=='0') style="display: block" #else style="display: none" #end id="exceptionRemark#(add.empId)" placeholder="请输入原因"  value="#(add.exceptionRemark??)" autocomplete="off" class="layui-input financeHandleClass">
                                </div>
                                #end
                            </td>
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>

        <div class="layui-row" style="margin-bottom: 50px;">
            <fieldset class="layui-elem-field layui-field-title" style="display: block;">
                <legend>4.弃</legend>
                <div class="layui-field-box">
                    <table id="entryApprovalTable4" lay-filter="entryApprovalTable4" class="layui-table">
                        <colgroup>
                            <!--<col width="150">
                            <col width="200">
                            <col>-->
                        </colgroup>
                        <thead>
                        <tr>
                            <th>序号</th>
                            <th>工号</th>
                            <th>姓名</th>
                            <th>证件号码</th>
                            <th>年龄</th>
                            <!--<th>是否本地户籍</th>-->
                            <th>购买单位</th>
                            <!--<th>是否第一次购买</th>-->
                            <!--<th>手机号码</th>-->
                            <th>上月类型</th>
                            #if(currentStepAlias??=="submit" || taskId==null)
                            <th>操作</th>
                            #else
                            <th>购买情况</th>
                            #end
                        </tr>
                        </thead>
                        <tbody id="abandonTbody">
                        #for(add : abandonList)
                        <tr data-id="#(add.empId)" id="#(add.id??)">
                            <td style="text-align: center;">#(for.index+1)</td>
                            <td>#(add.workNum??)</td>
                            <td sex="#(add.sex??)" positionName="#(add.positionName??)" deptName="#(add.deptName??)" >#(add.fullName??)</td>
                            <td>#(add.idcard??)</td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 240px;" >
                                    <input type="text" id="idCardAddr#(add.empId)" placeholder="户口"  value="#(add.idCardAddr??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>-->
                            <td>
                                <div class="layui-input-inline" >
                                    <input type="text" id="age#(add.empId)" placeholder="年龄"  value="#(add.age??)" autocomplete="off" class="layui-input">
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 100px;">
                                    <select name="isLocal" id="isLocal#(add.empId)" lay-verify="">
                                        <option value="">户籍</option>
                                        <option value="1" #if(add.isLocal??=='1') selected #end>是</option>
                                        <option value="0" #if(add.isLocal??=='0') selected #end>否</option>
                                    </select>
                                </div>
                            </td>-->
                            <td>
                                <div class="layui-input-inline" style="width: 300px;">
                                    <select id="companySelect#(add.empId)">
                                        #for(orgCompany : orgCompanyList)
                                        <option value="#(orgCompany.id)" #if(add.companyId??==orgCompany.id) selected #elseif(orgCompany.id==companyId??) selected #end >#(orgCompany.name)</option>
                                        #end
                                    </select>
                                </div>
                            </td>
                            <!--<td>
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="isFirst#(add.empId)">
                                        <option value="0" #if(add.isFirst=='0') selected #end>否</option>
                                        <option value="1" #if(add.isFirst=='1') selected #end>是</option>
                                    </select>
                                </div>
                            </td>-->
                            <!--<td>#(add.phoneNum??)</td>-->
                            <td>
                                <input type="hidden" value="#(add.lastMonthType??)" id="lastMonthType#(add.empId??)">
                                #if(add.lastMonthType=='1')
                                <span class="layui-badge" style="background-color:#009688;">增</span>
                                #else if(add.lastMonthType=='2')
                                <span class="layui-badge layui-bg-normal" style="background-color:#1E9FFF;">续</span>
                                #else if(add.lastMonthType=='3')
                                <span class="layui-badge layui-bg-warm" style="background-color:#FFB800;">删</span>
                                #else if(add.lastMonthType=='4')
                                <span class="layui-badge layui-bg-danger" style="background-color:#FF5722;">弃</span>
                                #end
                            </td>
                            <td>
                                #if(currentStepAlias??=="submit" || taskId==null)
                                <div class="layui-btn-group">
                                    <button type="button" class="layui-btn layui-btn-sm" onclick="move('#(add.empId??)','#(add.payRatio??)','1','4')">新增</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" onclick="move('#(add.empId??)','#(add.payRatio??)','2','4')">续买</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-warm" onclick="move('#(add.empId??)','#(add.payRatio??)','3','4')">删除</button>
                                    <button type="button" class="layui-btn layui-btn-sm layui-btn-red"  onclick="remove('#(add.empId??)','4')">移除</button>
                                    <!--<button type="button" class="layui-btn layui-btn-sm layui-btn-danger" onclick="move('#(add.empId??)','#(add.payRatio??)','4','1')">弃买</button>-->
                                </div>
                                #else
                                <div class="layui-input-inline" style="width: 70px;">
                                    <select id="status#(add.empId)" data-id="#(add.empId)" class="financeHandleClass" lay-filter="filter">
                                        <option value="">选择</option>
                                        <option value="1" #if(add.status??=='1' || (add.status==null && currentStepAlias??=='financeHandle' && allowApprove)) selected #end >成功</option>
                                        <option value="0" #if(add.status??=='0') selected #end >失败</option>
                                    </select>
                                </div>
                                <div class="layui-input-inline"  >
                                    <input type="text" #if(add.status??=='0') style="display: block" #else style="display: none" #end id="exceptionRemark#(add.empId)" placeholder="请输入原因"  value="#(add.exceptionRemark??)" autocomplete="off" class="layui-input financeHandleClass">
                                </div>
                                #end
                            </td>
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>
            </fieldset>
        </div>
    </div>
    <div class="layui-col-md4" #if(device??=='mobile') style="padding-top: 46px;padding-bottom: 50px;display: none;" #else style="padding-top: 46px;padding-bottom: 50px;"  #end>

        #if(stepts.size()??0>0)
        <table class="layui-table" id="steptsTable">
            <!--<colgroup>
                <col width="10%">
                <col width="13%">
                <col width="30%">
                <col width="30%">
                <col width="17%">
            </colgroup>-->
            <thead>
            <tr>
                <th style="text-align: center;">序号</th>
                <th style="text-align: center;">节点</th>
                <th style="text-align: center;">流程</th>
                <th style="text-align: center;">意见</th>
                <th style="text-align: center;">操作时间</th>
                <th style="text-align: center;">操作人</th>
            </tr>
            </thead>
            <tbody>
            #for(stept:stepts)
            <tr>
                <td align="center">#(for.index+1)</td>
                <td>#(stept.ActivityName)</td>
                <td align="center">
                    #if(stept.StepState==3)
                    提交
                    #else if(stept.StepState==1)
                    待处理
                    #else if(stept.StepState==0)
                    等待
                    #else if(stept.StepState==2)
                    正处理
                    #else if(stept.StepState==4)
                    撤回
                    #else if(stept.StepState==5)
                    批准
                    #else if(stept.StepState==6)
                    拒绝
                    #else if(stept.StepState==7)
                    转移
                    #else if(stept.StepState==8)
                    失败
                    #else if(stept.StepState==9)
                    跳过
                    #end
                </td>
                <td>#(stept.Comment)</td>
                <td>#(stept.CommentTime)</td>
                <td align="center">#(stept.CommentUserName)</td>
            </tr>
            #end
            </tbody>
        </table>


        <form class="layui-form layui-form-pane" id="taskForm">
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">意见</label>
                <div class="layui-input-block" style="margin-left: 0px;">
                    <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit  ) disabled #end></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block pull-right">
                    <!--<input id="taskId" type="hidden" value="#(persTask.taskId??)">-->
                    <input id="taskNo" type="hidden" value="#(persTask.taskNo??)">
                    <input id="currentStepAlias" type="hidden" value="#(currentStepAlias??)">
                    <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                    <!--<button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>-->
                    <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject  ) disabled  style="display: none;" #end>拒绝</button>
                    <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove  ) disabled style="display: none;" #end>通过</button>
                </div>
            </div>
        </form>
        #end
    </div>


    <div class="layui-form-footer">
        <div class="pull-right">
            #if(allowApprove && currentStepAlias=='financeHandle')
            <button class="layui-btn" type="button" id="saveStatus">保存购买情况</button>
            #end
            #if(isSaveHandle || taskId==null)
            <button class="layui-btn" type="button" #if(device??=='mobile') style="display: none;" #end id="saveBtn">保存并提交</button>
            #end
            <button class="layui-btn layui-btn-danger" type="button" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
