#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
#end

#define content()
<div style="margin: 15px;">
    <!--<div class="demoTable">

        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            昵称:
            <div class="layui-inline">
                <input id="nickName" name="nickName" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
    </div>-->
    <div class="layui-row">
        <button type="button" class="layui-btn layui-btn-xs" onclick="queryMsg(0)">未读消息</button>
        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="queryMsg(1)">已读消息</button>
        <button type="button" class="layui-btn layui-btn-primary layui-btn-xs" onclick="queryMsg()">全部消息</button>
        <button type="button" class="layui-btn layui-bg-orange layui-btn-xs" id="signRead">标记已读</button>
    </div>
    <table id="msgTable" lay-filter="msgTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script>
layui.use(['form','layer','table'], function() {
    var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

    function msgLoad(data){
        table.render({
            id : 'msgTable'
            ,elem : '#msgTable'
            ,method : 'POST'
            ,where : data
            ,height:$(document).height()*0.8
            ,limit : 10
            ,limits : [10,20,30,40]
            ,url : '#(ctxPath)/pers/msg/findListPage'
            ,cellMinWidth: 80
            ,cols: [[
                {type:'checkbox'}
                ,{field:'title', title: '标题内容', align: 'left', unresize: true,templet:function (d) {
                    var str = '';
                        str += '<div style="float:left;">'+d.title+'</div>';
                    if(d.label != undefined && d.label != ''){
                        str += '<div style="float: right;">';
                        var array=d.label.split(",");
                        for(var i=0;i<array.length;i++){
                            str += '<span class="layui-badge layui-bg-gray">'+array[i]+'</span>&nbsp;'
                        }
                        str += '</div>';
                        return str;
                    }else{
                        return d.title;
                    }
                }}
                ,{field:'createTime', title: '时间',align: 'left', unresize: true,width:180,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                /*,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}*/
            ]]
            ,done: function(res, curr, count){
                $('#msgTable').next().find('.layui-table-body').find("table" ).find("tbody").children("tr").on('dblclick',function(){
                    var index = JSON.stringify($('#msgTable').next().find('.layui-table-body').find("table").find("tbody").find(".layui-table-hover").data('index'));
                    var obj = res.data[index];
                    var url = "#(ctxPath)/pers/msg/form?id=" + obj.id + "&title="+obj.title +"&content="+obj.content+"&createTime="+obj.createTime;
                    layerShow("查阅消息",url,'100%','100%');
                })
            }
            ,page : true
        });
    };
    
    msgLoad(null);

    queryMsg = function(readFlag){
        var data = {"readFlag":readFlag};
        msgLoad(data);
    }

    form.on("submit(search)",function(data){
        msgLoad(data.field);
        return false;
    });

    //批量获取被作废数据
    getCheckTableData = function(){
        var wxarticleCheckStatus = table.checkStatus('msgTable');
        // 获取选择状态下的数据
        return wxarticleCheckStatus.data;
    }
    //批量已读
    $("#signRead").click(function(){
        layer.confirm("确定批量标记吗?",function(index){
            var jsonData=getCheckTableData();
            if(jsonData == null || jsonData == ''){
                layer.msg('请勾选标记数据', function () {});
                return;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/pers/msg/signRead',
                data: {data:JSON.stringify(jsonData)},
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        table.reload('msgTable');
                    }
                },
                complete : function() {
                }
            });
            layer.close(index) ;
        });
    });
});
</script>
#end