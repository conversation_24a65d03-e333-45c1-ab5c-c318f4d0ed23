#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()公告发布页面#end

#define css()
#end

#define content()
<div class="my-btn-box">
    <div class="layui-row">
        <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
            <div class="layui-inline">
            	<label class="layui-form-label">公告类型</label>
				<div class="layui-input-inline">
	                <select id="noticeTypeId" name="noticeTypeId">
	                    <option value="">请选择公告类型</option>
	                    #for(t : typeList)
	                    	<option value="#(t.id)">#(t.typeName)</option>
	                    #end
	                </select>
                </div>
            </div>
            <div class="layui-inline">
				<label class="layui-form-label">公告标题</label>
				<div class="layui-input-inline">
					<input id="noticeTitle" name="noticeTitle" class="layui-input">
				</div>
			</div>
			<div class="layui-inline">
            	<label class="layui-form-label">发布人</label>
				<div class="layui-input-inline">
	                <select id="createBy" name="createBy" lay-search>
	                    <option value="">请选择发布人</option>
	                    #for(e : empList)
	                    	<option value="#(e.user_id)">#(e.full_name)</option>
	                    #end
	                </select>
                </div>
            </div>
            <div class="layui-inline">
				<label class="layui-form-label">发布时间</label>
				<div class="layui-input-inline">
					<input id="releaseTime" name="releaseTime" class="layui-input">
				</div>
			</div>
            <div class="layui-inline">
	            <button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		        #shiroHasPermission("notice:release:addBtn")
		        	<button type="button" id="add" class="layui-btn">添加</button>
				#end
            </div>
        </form>
    </div>
	<div class="layui-row">
		<table class="layui-table" id="noticeTable" lay-filter="noticeTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
    #shiroHasPermission("notice:release:lookBtn")
    #[[
    {{#if(d.releaseStatus=='release'){}}
    	<a class="layui-btn layui-btn-xs layui-btn-warm" lay-event="detail">查阅</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("notice:release:lookNameListBtn")
	#[[
    {{#if(d.releaseStatus=='release'){}}
		<a class="layui-btn layui-btn-xs" lay-event="lookNameList">查阅名单</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("notice:release:editBtn")
    #[[
    {{#if(d.releaseStatus=='draft'){}}
    	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("notice:release:releaseBtn")
    #[[
    {{#if(d.releaseStatus=='draft'){}}
    	<a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="release">发布</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("notice:release:withdrawBtn")
    #[[
    {{#if(d.releaseStatus=='release'){}}
    	<a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="withdraw">撤回</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("notice:release:voidBtn")
    #[[
    {{#if(d.releaseStatus=='draft'){}}
    	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#
    #end
</div>
</script>
<script>
layui.use(['form','layer','table', 'laydate'], function() {
    var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate = layui.laydate;
    
	//时间渲染
	laydate.render({
		elem: '#releaseTime'
		, format: 'yyyy-MM-dd'
	});

    function noticeLoad(data){
        table.render({
            id : 'noticeTable'
            ,elem : '#noticeTable'
            ,method : 'POST'
            ,where : data
            ,limit : 15
            ,limits : [15,30,45,50]
            ,url : '#(ctxPath)/pers/notice/findListPage'
            ,cellMinWidth: 80
            ,cols: [[
//                 {type:'checkbox'},
                {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field:'noticeTypeName', title: '公告类型', align: 'center', unresize: true}
                ,{field:'noticeTitle', title: '公告标题', align: 'center', unresize: true}
//                 ,{field:'zhiding', title: '置顶', align: 'center', unresize: true}
                ,{field:'releaseTime', title: '发布时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.releaseTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{field:'releaseStatus', title: '状态', align: 'center', unresize: true,templet:"<div>{{ d.releaseStatus=='draft'?'<span class='layui-badge layui-bg-orange'>草稿</span>':d.releaseStatus=='release'?'<span class='layui-badge layui-bg-green'>已发布</span>':d.releaseStatus=='withdraw'?'<span class='layui-badge layui-bg-gray'>已撤回</span>':'- -' }}</div>"}
                ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });
    };
    table.on('tool(noticeTable)',function(obj){
    	if(obj.event === 'detail'){
            var url = "#(ctxPath)/pers/notice/detail?id=" + obj.data.id;
            pop_show("查阅公告",url,1050,680);
        }else if(obj.event === 'lookNameList'){
            var url = "#(ctxPath)/pers/notice/lookNameIndex?id=" + obj.data.id;
            pop_show("查阅名单",url, '' ,'');
        }else if(obj.event === 'edit'){
            var url = "#(ctxPath)/pers/notice/edit?id=" + obj.data.id ;
            pop_show("编辑公告",url,1050,750);
        }else if(obj.event === 'release'){
        	layer.confirm("确定要发布吗?",function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/pers/notice/release',
                    notice: true,
                    data: {id:obj.data.id},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            noticeTableReload();
                        }
                        layer.close(index);
                    },
                    complete : function() {
                    }
                });
            });
        }else if(obj.event === 'withdraw'){
            layer.confirm("确定要撤回吗?",function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/pers/notice/withdraw',
                    notice: true,
                    data: {id:obj.data.id},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            noticeTableReload();
                        }
                        layer.close(index);
                    },
                    complete : function() {
                    }
                });
            });
        }else if (obj.event === 'del') {
            layer.confirm("确定要作废吗?",function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/pers/notice/delete',
                    notice: true,
                    data: {id:obj.data.id},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            noticeTableReload();
                        }
                        layer.close(index);
                    },
                    complete : function() {
                    }
                });
            });
        }
    });

    noticeLoad(null);

    form.on("submit(search)",function(data){
        noticeLoad(data.field);
        return false;
    });

    noticeTableReload=function(){
        table.reload('noticeTable');
    }
    
    // 添加
    $("#add").click(function(){
        $(this).blur();
        pop_show("发布公告",'#(ctxPath)/pers/notice/add',1050,750);
    });
});
</script>
#end