#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
    .layui-table-cell{
        overflow:visible;
        text-overflow:inherit;
        white-space:normal;
        height: auto !important;
        word-break: break-all;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <!--<label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>-->
            <!--<label class="layui-form-label">转正时间</label>
            <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
            </div>-->

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">申请单位：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>

            <div class="layui-input-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-inline" style="float: left;" >
                    <select id="status" name="status" lay-filter="status">
                        <option value="">全部</option>
                        <option value="1">待提交</option>
                        <option value="2">审核中</option>
                        <option value="3">审核通过</option>
                        <option value="5">中止</option>
                    </select>
                </div>
            </div>
            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:recruit:addBtn")
            <button class="layui-btn" id="addBtn" type="button">添加</button>
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">

    #[[
    {{#if(d.status=='1'){}}
        {{#if(d.isEdit){}}
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
        {{#}else{}}
        <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
        {{#}}}
    {{#}}}


    {{#if(d.isCurrentUser){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}

    {{#if(!d.isCurrentUser){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}

    {{#if(d.status=='3'){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="interviewRecord">查看简历</a>
    {{#}}}
    {{#if(d.status=='3'){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="formUrl">简历登记二维码</a>
    {{#}}}

    ]]#

</script>
<script src="/static/js/jquery-3.3.1.min.js" type="text/javascript" charset="utf-8"></script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script src="/static/js/jquery.qrcode.min.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad(null);

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });


        empTable=function(){
            var url='#(ctxPath)/pers/approval/empTable';
            pop_show("员工表",url,1320,700);
        }


        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"status":$("#status").val(),'applyDeptId':id});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/recruitForm';
            pop_show("添加招聘需求申请",url,'100%','100%');
        });

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/recruitPageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'applyDeptName',title:'申请单位', align: 'center', unresize: true,templet:function (d) {
                            return d.applyDeptName;
                        }},
                    {field:'applyPositionName',title:'招聘职位', align: 'center', unresize: true,templet:function (d) {
                            return d.applyPositionName;
                        }}
                    ,{field:'recruitedPeopleNum', title: '已招聘人数', align: 'center',width: 80, unresize: true}
                    ,{field:'peopleNum', title: '需要招聘人数', align: 'center',width: 95, unresize: true}
                    ,{field:'applyType',title:'申请类型', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.recruitCause=='1'){
                                return '增设岗位';
                            }else if(d.recruitCause=='2'){
                                return '增加编制';
                            }else if(d.recruitCause=='3'){
                                return '补充人员';
                            }
                        }}
                    ,{field:'workDeptName', title: '驻地单位', align: 'center', unresize: true}
                    ,{field:'applyTime', title: '提交时间',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return dateFormat(d.applyTime,'yyyy-MM-dd HH:mm:ss')
                        }}
                    ,{field:'recruitCauseDescribe', title: '申请理由',width: 120, align: 'center', unresize: true}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180,templet:function (d) {
                            if(typeof(d.currentStepName)!='undefined'){
                                return d.currentStepName;
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'status', title: '流程状态',align: 'center', unresize: true,width:120,templet:function (d) {
                            if(d.status=='1'){
                                return '<span class="layui-badge">未提交</span>';
                            }else if(d.status=='2'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.status=='3'){
                                return '<span class="layui-badge layui-bg-green">审核通过</span>';
                            }else if(d.status=='4'){
                                return '驳回';
                            }else if(d.status=='5'){
                                return '中止';
                            }else if(d.status=='6'){
                                return '撤销';
                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 230, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/pers/approval/recruitForm?id='+obj.data.id;
                    pop_show("处理流程",url,'100%','100%');
                }else if(obj.event==='edit2'){
                    var url='#(ctxPath)/pers/approval/dispatchEditForm?id='+obj.data.id;
                    pop_show("处理流程",url,'100%','100%');
                }else if(obj.event=='export'){
                    window.location.href='#(ctxPath)/pers/approval/exportBusinessTripExcel?id='+obj.data.id;
                }else if(obj.event=='del'){
                    layer.confirm('你确定要作废吗？', {icon: 3, title:'提示'}, function(index) {
                        $.ajax({
                            type:'post',
                            data: {"id":obj.data.id},
                            url: '#(ctxPath)/pers/approval/delEmployeeDispatchApply',
                            contentType: "application/x-www-form-urlencoded;charset=UTF-8",
                            dataType: 'json',
                            timeout: 30000,
                            beforeSend: function (XMLHttpRequest) {
                                layer.load();
                            },
                            success: function (res) {
                                if (res.state == 'ok') {
                                    layer.msg('操作成功', {icon: 1, offset: 'auto'});
                                    reloadTable();
                                    pop_close();
                                } else {
                                    layer.msg(res.msg, {icon: 2, offset: 'auto'});
                                }
                            }
                            ,complete :function(XMLHttpRequest, TS){
                                layer.closeAll('loading');
                            }
                        });
                        layer.close(index);
                    });

                }else if(obj.event=='interviewRecord'){
                    var url='#(ctxPath)/pers/approval/interviewRecordIndex?recruitId='+obj.data.id;
                    pop_show("查看简历",url,'100%','100%');
                }else if(obj.event=='formUrl'){
                    /*var url='#(ctxPath)/pers/approval/interviewRecordIndex?recruitId='+obj.data.id;
                    pop_show("查看简历",url,'100%','100%');*/
                    layer.open({
                        type: 1,
                        skin: 'layui-layer-rim', //加上边框
                        area: ['400px', '400px'], //宽高
                        content: '<div id="qrcode" style="display: grid;place-items: center;"></div>'
                        ,success: function(layero, index){
                            $('#qrcode').qrcode({
                                render: "canvas",  // 渲染方式（可选：canvas 或 table，默认 canvas）‌:ml-citation{ref="1,4" data="citationList"}
                                width: 200,       // 宽度（单位：像素）
                                height: 200,      // 高度（单位：像素）
                                text: "#(interviewRecordFormUrl??)?recruit="+obj.data.id+"&positionName="+encodeURIComponent(obj.data.applyPositionName),  // 目标链接‌:ml-citation{ref="1,3" data="citationList"}
                                correctLevel: 1,  // 纠错等级（0~3，值越大容错率越高）‌:ml-citation{ref="3" data="citationList"}
                                foreground: "#000",  // 二维码颜色（默认黑色）‌:ml-citation{ref="4" data="citationList"}
                                background: "#fff"   // 背景颜色（默认白色）‌:ml-citation{ref="4" data="citationList"}
                            });
                        }
                    });

                }
            })


        };


    });
</script>
#end