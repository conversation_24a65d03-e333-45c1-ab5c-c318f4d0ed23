#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">类型</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="processNo" name="processNo" lay-filter="processNo">
                    <option value="">全部</option>
                    #for(type:taskType)
                    <option value="#(type.key)">#(type.value)</option>
                    #end
                </select>
            </div>

            <label class="layui-form-label">状态</label>
            <div class="layui-input-inline" style="float: left;" >
                <select id="state" name="state" lay-filter="state">
                    <option value="5">全部</option>
                    <option value="0">待处理</option>
                    <option value="1">提交</option>
                    <option value="2">批准</option>
                    <option value="3">完成</option>
                    <option value="4">失败</option>
                    <option value="6">抄送</option>
                </select>
            </div>
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("pers:task:edit")
    #[[
    {{#if(d.StepState==='Pending'){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">处理</a>
    {{#}}}
    ]]#

    #end

    #shiroHasPermission("pers:task:see")
    #[[
    {{#if(d.StepState!='Pending'){}}
    <a class="layui-btn layui-btn-xs layui-btn-primary" lay-event="edit">查看</a>
    {{#}}}
    ]]#
    #end

</script>
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        msgLoad({"processNo":$("#processNo").val(),"state":$("#state").val()});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }


        form.on('select(state)',function (obj) {
            loadTable();
        })

        reloadTable=function () {
            msgLoad({"state":$("#state").val(),"processNo":$("#processNo").val()});
        }

        function loadTable(){
            msgLoad({"state":$("#state").val(),"processNo":$("#processNo").val()});
        }

        form.on('select(processNo)',function (obj) {
            loadTable();
        })

        function msgLoad(data){
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/pers/approval/getMyTasks'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'TaskNo',title:'流程编号', align: 'center',width:150, unresize: true,}
                    ,{field:'ProcessName', title: '流程类型',width:140, align: 'center', unresize: true}
                    ,{field:'Title', title: '流程名称', align: 'center', unresize: true}
                    ,{field:'CreatedTime', title: '提交时间', align: 'center',width:180, unresize: true}
                    ,{field:'CreatorName', title: '创建者',align: 'center',width:100, unresize: true}
                    ,{field:'CurrentSteps', title: '当前环节',align: 'center', unresize: true,width:180}
                    ,{field:'StepState', title: '我的状态',align: 'center',width:120, unresize: true,width:180,templet:function (d) {
                            if(d.StepState==='Waiting'){
                                return '等待';
                            }else if(d.StepState==='Pending'){
                                return '<span class="layui-badge layui-bg-orange">待处理</span>';
                            }else if(d.StepState==='Active'){
                                return '处理中';
                            }else if(d.StepState==='Submitted'){
                                return '已提交'
                            }else if(d.StepState==='Withdrew'){
                                return '撤回';
                            }else if(d.StepState==='Approved'){
                                return '<span class="layui-badge layui-bg-green">批准</span>';
                            }else if(d.StepState==='Rejected'){
                                return '<span class="layui-badge">拒绝</span>';
                            }else if(d.StepState==='Transferred'){
                                return '转移';
                            }else if(d.StepState==='Aborted'){
                                return '失败';
                            }else if(d.StepState==='Skipped'){
                                return '跳过';
                            }
                        }}
                    ,{field:'TaskState', title: '流程状态',align: 'center', unresize: true,width:120,templet:function (d) {
                            if(d.TaskState==='Waiting'){
                                return '等待';
                            }else if(d.TaskState==='Pending'){
                                return '待处理';
                            }else if(d.TaskState==='Active'){
                                return '<span class="layui-badge layui-bg-orange">处理中</span>';
                            }else if(d.TaskState==='Submitted'){
                                return '已提交'
                            }else if(d.TaskState==='Withdrew'){
                                return '撤回';
                            }else if(d.TaskState==='Approved'){
                                return '批准';
                            }else if(d.TaskState==='Rejected'){
                                return '拒绝';
                            }else if(d.TaskState==='Transferred'){
                                return '转移';
                            }else if(d.TaskState==='Aborted'){
                                return '失败';
                            }else if(d.TaskState==='Skipped'){
                                return '跳过';
                            }else if(d.TaskState==='Completed'){
                                return '<span class="layui-badge layui-bg-green">结束</span>';
                            }
                        }}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    if(obj.data.ProcessNo==='PH005'){
                        var url='#(ctxPath)/employeeCheckin/fillCheckinForm?taskId='+obj.data.TaskId;
                        pop_show("处理流程",url,'100%','100%');
                    }else if(obj.data.ProcessNo==='PH002') {
                        var url = '#(ctxPath)/pers/approval/qualifiedForm?taskId=' + obj.data.TaskId;
                        pop_show("处理流程", url, '100%', '100%');
                    }else if(obj.data.ProcessNo==='PH010'){
                        var url="#(ctxPath)/providentFund/editProvidentFundForm?taskId="+obj.data.TaskId;
                        pop_show("处理公积金购买申请流程",url,'100%','100%');
                    }else if(obj.data.ProcessNo==='PH011'){
                        var url="#(ctxPath)/socialSecurityApply/editSocialSecurityForm?taskId="+obj.data.TaskId;
                        pop_show("处理社保购买申请流程",url,'100%','100%');
                    }else if(obj.data.ProcessNo==='PH012'){
                        var url='#(ctxPath)/liabilityInsurance/editLiabilityInsuranceForm?taskId='+obj.data.TaskId;
                        pop_show("处理雇主责任险流程",url,'100%','100%');
                    }else if(obj.data.ProcessNo==='PH013'){
                        var url='#(ctxPath)/pers/approval/businessTripForm?taskId='+obj.data.TaskId;
                        pop_show("处理流程",url,'100%','100%');
                    }else if(obj.data.ProcessNo==='PH014'){
                        var url='#(ctxPath)/persOrg/orgHeadcountApplyForm?taskId='+obj.data.TaskId;
                        pop_show("处理流程",url,'100%','100%');
                    }else{
                        var url='#(ctxPath)/pers/approval/entryApprovalForm?taskId='+obj.data.TaskId;
                        pop_show("处理流程",url,'100%','100%');
                    }

                }
            })


        };


    });
</script>
#end