#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
     .laytable-cell-checkbox .layui-disabled.layui-form-checked i {
         background: #fff !important;
     }
</style>
#end

#define content()

<input id="employeeId" value="#(employeeId??)" type="hidden">
<div class="layui-row" style="padding: 10px;">

</div>
<label style="margin-left: 10px;">部门：</label>
<div class="layui-inline"  style="width: 350px;">
    <div id="deptSelect" style="margin: 5px 10px;">

    </div>
</div>
<label>时间：</label>
<div class="layui-inline" style="padding-right: 20px;">
    <input type="text" readonly class="layui-input" id="dateRange">
</div>
<label>姓名：</label>
<div class="layui-inline" style="padding-right: 20px;">
    <input type="text" class="layui-input" id="fullName" style="width: 120px;">
</div>
<label>状态：</label>
<div class="layui-inline layui-form" style="padding-right: 20px;width: 120px;">
    <select id="archiveStatus">
        #dictOption("archive_status", '', "")
    </select>
</div>
<div class="layui-btn-group">
    <button type="button" class="layui-btn" id="search">搜索</button>
    <button type="button" class="layui-btn" id="export2">导出(新)</button>
    <button type="button" class="layui-btn" id="exportByPage">分批导出(新)</button>
    #shiroHasPermission("emp:checkin:summaryExportBtn")
    <button type="button" class="layui-btn" id="export">导出(旧)</button>
    #end
    <!--<button type="button" class="layui-btn" id="export3">导出(测试)</button>-->
    #shiroHasPermission("emp:checkin:examineBtn")
    <button type="button" class="layui-btn" id="examine">审核选中项</button>
    #end
    #shiroHasPermission("emp:checkin:examineAllBtn")
    <button type="button" class="layui-btn" id="examineAll">审核全部</button>
    #end
</div>

<!--<a href="#(ctxPath)/employeeCheckin/reportFormIndex" target="_blank" style="color: #01AAED;">旧版本>>></a>-->
<div class="layui-row" style="">
    <p style="color: #666;padding-left: 10px;padding-top: 5px;font-size: 12px;">应出勤天数：日历工日数-公休假</p>
    <p style="color: #666;padding-left: 10px;padding-top: 5px;font-size: 12px;">实出勤天数：正常出勤天数+迟到、早退、漏卡出勤天数+加班天数</p>
    <p style="color: #666;padding-left: 10px;padding-top: 5px;font-size: 12px;">缺勤天数(2024-03-01之前)：旷工+请休假</p>
    <p style="color: #666;padding-left: 10px;padding-top: 5px;font-size: 12px;">缺勤天数(2024-03-01及之后)：旷工</p>
    <p style="color: #666;padding-left: 10px;padding-top: 5px;font-size: 12px;">休息天数：公休假天数-公休天加班天数</p>
    <!--<p style="color: #666;padding-left: 10px;padding-top: 10px;">实算工资天数：实出勤天数+年假+婚/丧/产/工伤假-事/病假-调休加班天数+补休请休假</p>-->
    <p style="color: #666;padding-left: 10px;padding-top: 5px;font-size: 12px;">实算工资天数：实出勤天数+年休假+婚/丧/产/工伤假-调休加班天数+补休请休假</p>
    <table id="employeeCheckinTable" lay-filter="employeeCheckinTable"></table>
</div>

#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">

        #shiroHasPermission("emp:archives:changeDepartment:edit")
        <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
        #end
    </div>
</script>
<script type="text/html" id="checkbd">
    #[[{{#  if (d.status != "3"){ }}
    <input type="checkbox" name="siam_one" title="" lay-skin="primary" data-id = "{{ d.id }}">
    {{#  } }}
    ]]#
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate','form'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeCheckinTable'
            ,laydate=layui.laydate
            ,form=layui.form
        ;

        //日期范围选择
        /*laydate.render({
            elem: '#dateRange'
            //,range: true //或 range: '~' 来自定义分割字符
            ,trigger:'click'
            ,max: '#(month)'
            ,type: 'month'
            ,value:"#(month)"
        });*/
        laydate.render({
            elem: '#dateRange'
            ,range: true //或 range: '~' 来自定义分割字符
            ,trigger:'click'
            ,value:"#(startDateStr) - #(endDateStr)"
            #if(user.userName??!='Admin')
            ,max: '#(endDateStr)'
            #end
        });
        /*var deptSelect = xmSelect.render({
            // 这里绑定css选择器
            el: '#deptSelect',
            // 渲染的数据
            data: [
            ],
        })*/

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,

                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });

        loadTable=function (data) {
            layer.load();

            var dateRange=data.dateRange;
            if(dateRange==undefined){
                dateRange="#(startDateStr) - #(endDateStr)";
            }
            var dates=dateRange.split(" - ");
            var startDate=new Date(dates[0]);
            var endDate=new Date(dates[1]);
            var standardDate=new Date('2024-03-01');

            var url;
            var colsArray;
            if(startDate.getTime()>=standardDate.getTime() && endDate.getTime()>=standardDate.getTime()){


                //新
                url='#(ctxPath)/employeeCheckin/newEmpCheckinDaySummaryPage';
                colsArray=[[
                    {
                        rowspan: 2,
                        templet: "#checkbd",
                        title: "<input type='checkbox' name='siam_all' title='' lay-skin='primary' lay-filter='siam_all'> ",
                        width: 50, align:'center'
                        ,fixed: 'left'
                    }
                    ,{rowspan: 2,field:'orgName',fixed: 'left', title:'部门', width:250, align:'center',unresize:true}
                    ,{rowspan: 2,field:'fullName',fixed: 'left', title:'姓名', width:120, align:'center',unresize:true,templet:function (d){
                            return d.fullName+"("+d.workNum+")";
                        }}
                    /*,{rowspan: 2,field:'status', title:'审核状态',style:'cursor:pointer;text-decoration:none;', width:95, align:'center',unresize:true,templet:function (d){
                            var title="";
                            var daySummaryExamineStatusMap=d.daySummaryExamineStatusMap;
                            for(key in daySummaryExamineStatusMap){
                                title+=key+":"+daySummaryExamineStatusMap[key]+"\n";
                            }

                            if(d.notSubmitted==0 && d.notApproved==0 && d.approved>0 ){
                                return '<span class="layui-badge layui-bg-green" title="'+title+'">全审核完成</span>'
                            }else if(d.notSubmitted>0 && d.notApproved==0 && d.approved==0){
                                return '<span class="layui-badge" title="'+title+'">全未提交</span>'

                            }else if(d.notSubmitted==0 && d.approved==0){
                                return '<span class="layui-badge layui-bg-orange" title="'+title+'">已提交未审核</span>';
                            }else{
                                return '<span class="layui-badge layui-bg-orange" title="'+title+'">部分提交审核</span>';
                            }
                        }}*/
                    ,{rowspan: 2,field:'shouldWorkDayHourStr', title:'应出勤天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'actualWorkDayHourStr', title:'实出勤天数', width:95, align:'center',unresize:true}
                    ,{rowspan: 2,field:'absenteeismDayHourStr', title:'旷工天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'restDayHourStr', title:'休息天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'actualSalaryDayHourStr', title:'实算工资天数', width:100, align:'center',unresize:true}
                    ,{colspan: 4,field:'restCount', title:'请休假', width:80, align:'center',unresize:true}
                    ,{colspan: 2,field:' ', title:'加班', width:80, align:'center',unresize:true}
                    ,{colspan: 3,field:' ', title:'余假', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'exception_1', title:'迟到', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_1==undefined){
                                return 0+"次";
                            }else{
                                var exception_1_d=0;
                                if(d.exception_1_duration!=undefined){
                                    exception_1_d=d.exception_1_duration/60;
                                }
                                return d.exception_1+'次；共'+exception_1_d+"分钟";
                            }
                        }}
                    ,{rowspan: 2,field:'exception_2', title:'早退', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_2==undefined){
                                return 0+"次";
                            }else{
                                var exception_2_d=0;
                                if(d.exception_2_duration!=undefined){
                                    exception_2_d=d.exception_2_duration/60;
                                }
                                return d.exception_2+'次；共'+exception_2_d+"分钟";
                            }
                        }}
                    ,{rowspan: 2,field:'exception_4', title:'旷工', width:120, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_4==undefined){
                                return 0+"次";
                            }else{
                                var exception_4_d=0;
                                if(d.exception_4_duration!=undefined){
                                    exception_4_d=d.exception_4_duration/60;
                                }
                                return d.exception_4+'次；共'+exception_4_d+"分钟";
                            }
                        }}
                    ,{rowspan: 2,field:'exception_3', title:'缺卡', width:70, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_3==undefined){
                                return 0+"次";
                            }else{
                                return d.exception_3+'次';
                            }
                        }}
                    ,{rowspan: 2,field:'fillCardCount', title:'补卡成功', width:80, align:'center',unresize:true,templet:function (d) {
                            if(d.fillCardCount==undefined){
                                return 0+"次";
                            }else{
                                return d.fillCardCount+'次';
                            }
                        }}

                ],[
                    {field:'notDeductLease', title:'婚/丧/产/工伤假等', width:130, align:'center',unresize:true,templet:function (d) {
                            return d.marryNursingLeaveDay+"天"+d.marryNursingLeaveHour+"小时";
                        }}
                    ,{field:'affairIllnessLeaveDay', title:'事/病假', width:95, align:'center',unresize:true,templet:function (d) {
                            return d.affairIllnessLeaveDay+"天"+d.affairIllnessLeaveHour+"小时";
                        }}
                    ,{field:'deductLeaseDays', title:'补休假', width:95, align:'center',unresize:true,templet:function (d) {
                            return d.fillLeaveDay+"天"+d.fillLeaveHour+"小时";
                        }}
                    ,{field:'yearLeaveDay', title:'年假', width:70, align:'center',unresize:true}
                    ,{field:'salaryOvertimeDays', title:'计工资', width:95, align:'center',unresize:true,templet:function (d) {
                            return d.salaryOvertimeDay+"天"+d.salaryOvertimeHour+"小时";
                        }}
                    ,{field:'restOvertimeDays', title:'调休', width:95, align:'center',unresize:true,templet:function (d) {
                            return d.restOvertimeDay+"天"+d.restOvertimeHour+"小时";
                        }}
                    ,{field:'lastMonthUsableFillLeave', title:'上月余假', width:95, align:'center',unresize:true,templet:function (d) {
                            //return d.restOvertimeDay+"天"+d.restOvertimeHour+"小时";
                            return '- -';
                    }}
                    ,{field:'thisMonthHoliday', title:'本月调休/积假', width:100, align:'center',unresize:true,templet:function (d) {
                            let symbol="";
                            let thisMonthUsableFillLeaveDay=Number(d.thisMonthUsableFillLeaveDay);
                            let thisMonthUsableFillLeaveHour=Number(d.thisMonthUsableFillLeaveHour);
                            if(thisMonthUsableFillLeaveDay<0 || thisMonthUsableFillLeaveHour<0){
                                symbol="-";
                            }
                            return symbol+""+ Math.abs(thisMonthUsableFillLeaveDay)+"天"+Math.abs(thisMonthUsableFillLeaveHour)+"小时";
                        }}
                    ,{field:'totalUsableFillLeave', title:'月末余假', width:95, align:'center',unresize:true,templet:function (d) {
                            //return d.restOvertimeDay+"天"+d.restOvertimeHour+"小时";
                            return '- -';
                    }}


                ]];

                var dataStrArray=getAllDate(dates[0],dates[1]);
                var weekArray=["周日","周一","周二","周三","周四","周五","周六"]
                $.each(dataStrArray,function (index,item) {
                    let title=item.substring(5);
                    let itemDate=new Date(item);

                    colsArray[0][16+index]={rowspan: 2,field:item, title:title+"\n"+weekArray[itemDate.getDay()], width:100, align:'center',unresize:true,templet:function (d) {
                            let dayLog='';
                            let dayRemark='';
                            if(d[item]!=undefined && d[item].dayLog!=undefined){
                                dayLog=d[item].dayLog;
                            }
                            if(d[item]!=undefined && d[item].dayRemark!=undefined){
                                dayRemark=d[item].dayRemark;
                            }
                            let dot='';
                            console.log(dayRemark.indexOf("出差记录"));
                            if(d[item]!=undefined && d[item].isShowDot!=undefined && d[item].isShowDot){
                                dot='<span class="layui-badge-dot layui-bg-orange" style="left: 25px;top: -2px;"></span>';
                            }

                            return '<div title="'+dayRemark+'" style="position: absolute;top: 0;bottom: 0;right: 0;left: 0;margin: auto;">'+ dayLog+dot+'</div>'
                        }};
                })

                colsArray[0][colsArray[0].length]={rowspan: 2,title:'操作',fixed: 'right', width:150, unresize:true, align:'center', templet: function(d){
                        var editBtn='';
                        #shiroHasPermission("emp:checkin:detail")
                        editBtn+='<a class="layui-btn layui-btn-xs" lay-event="detail">明细</a>';
                        #end
                        #shiroHasPermission("emp:checkin:daySummary")
                        editBtn+='<a class="layui-btn layui-btn-xs" lay-event="daily">日报</a>';
                        #end
                        #if(user.userName??=='Admin')
                        editBtn+='<a class="layui-btn layui-btn-xs" lay-event="newDaily">新日报</a>';
                        #end
                        return editBtn;
                    }
                }
                console.log(colsArray);


            }else if(startDate.getTime()<standardDate.getTime() && endDate.getTime()<standardDate.getTime()){
                //旧
                console.log("旧数据");
                url='#(ctxPath)/employeeCheckin/empCheckinDaySummaryPage';
                colsArray=[[
                    {
                        rowspan: 2,
                        templet: "#checkbd",
                        title: "<input type='checkbox' name='siam_all' title='' lay-skin='primary' lay-filter='siam_all'> ",
                        width: 50, align:'center'
                        ,fixed: 'left'
                    }
                    ,{rowspan: 2,field:'orgName',fixed: 'left', title:'部门', width:250, align:'center',unresize:true}
                    ,{rowspan: 2,field:'fullName',fixed: 'left', title:'姓名', width:120, align:'center',unresize:true,templet:function (d){
                            return d.fullName+"("+d.workNum+")";
                        }}
                    ,{rowspan: 2,field:'status', title:'审核状态',style:'cursor:pointer;text-decoration:none;', width:95, align:'center',unresize:true,templet:function (d){
                            var title="";
                            var daySummaryExamineStatusMap=d.daySummaryExamineStatusMap;
                            for(key in daySummaryExamineStatusMap){
                                title+=key+":"+daySummaryExamineStatusMap[key]+"\n";
                            }

                            if(d.notSubmitted==0 && d.notApproved==0 && d.approved>0 ){
                                return '<span class="layui-badge layui-bg-green" title="'+title+'">全审核完成</span>'
                            }else if(d.notSubmitted>0 && d.notApproved==0 && d.approved==0){
                                return '<span class="layui-badge" title="'+title+'">全未提交</span>'

                            }else if(d.notSubmitted==0 && d.approved==0){
                                return '<span class="layui-badge layui-bg-orange" title="'+title+'">已提交未审核</span>';
                            }else{
                                return '<span class="layui-badge layui-bg-orange" title="'+title+'">部分提交审核</span>';
                            }
                        }}
                    ,{rowspan: 2,field:'shouldWorkDay', title:'应出勤天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'actualWorkDay', title:'实出勤天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'absenceCount', title:'缺勤天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'restCount', title:'休息天数', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'actualWageDays', title:'实算工资天数', width:100, align:'center',unresize:true,templet:function (d) {
                            var moneyOverHour="";
                            if(d.moneyOverSecond>0){
                                moneyOverHour=formatSeconds(d.moneyOverSecond);
                            }
                            return d.actualWageDays+"天"+moneyOverHour;
                        }}
                    ,{colspan: 4,field:'restCount', title:'请休假', width:80, align:'center',unresize:true}
                    ,{colspan: 2,field:' ', title:'加班', width:80, align:'center',unresize:true}
                    ,{rowspan: 2,field:'exception_1', title:'迟到', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_1==undefined){
                                return 0+"次";
                            }else{
                                var exception_1_d=0;
                                if(d.exception_1_duration!=undefined){
                                    exception_1_d=d.exception_1_duration/60;
                                }
                                return d.exception_1+'次；共'+exception_1_d+"分钟";
                            }
                        }}
                    ,{rowspan: 2,field:'exception_2', title:'早退', align:'center', width:120,unresize:true,templet:function (d) {
                            if(d.exception_2==undefined){
                                return 0+"次";
                            }else{
                                var exception_2_d=0;
                                if(d.exception_2_duration!=undefined){
                                    exception_2_d=d.exception_2_duration/60;
                                }
                                return d.exception_2+'次；共'+exception_2_d+"分钟";
                            }
                        }}
                    ,{rowspan: 2,field:'exception_4', title:'旷工', width:120, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_4==undefined){
                                return 0+"次";
                            }else{
                                var exception_4_d=0;
                                if(d.exception_4_duration!=undefined){
                                    exception_4_d=d.exception_4_duration/60;
                                }
                                return d.exception_4+'次；共'+exception_4_d+"分钟";
                            }
                        }}
                    ,{rowspan: 2,field:'exception_3', title:'缺卡', width:70, align:'center',unresize:true,templet:function (d) {
                            if(d.exception_3==undefined){
                                return 0+"次";
                            }else{
                                return d.exception_3+'次';
                            }
                        }}
                    ,{rowspan: 2,field:'fillCardCount', title:'补卡成功', width:80, align:'center',unresize:true,templet:function (d) {
                            if(d.fillCardCount==undefined){
                                return 0+"次";
                            }else{
                                return d.fillCardCount+'次';
                            }
                        }}

                    ,{rowspan: 2,title:'操作',fixed: 'right', width:150, unresize:true, align:'center', templet: function(d){
                            var editBtn='';
                            #shiroHasPermission("emp:checkin:detail")
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="detail">明细</a>';
                            #end
                            #shiroHasPermission("emp:checkin:daySummary")
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="daily">日报</a>';
                            #end
                            #if(user.userName??=='Admin')
                            editBtn+='<a class="layui-btn layui-btn-xs" lay-event="newDaily">新日报</a>';
                            #end
                            return editBtn;
                        }
                    }
                ],[
                    {field:'yearRest', title:'年休假', width:70, align:'center',unresize:true}
                    ,{field:'marriageHoliday', title:'婚/丧/产/工伤假等', width:120, align:'center',unresize:true}
                    ,{field:'thingLear', title:'事假', width:70, align:'center',unresize:true}
                    ,{field: 'learRest',title: '补休', width:70, align:'center',unresize:true}

                    ,{field:'moneyOverDay', title:'计工资', width:90, align:'center',unresize:true,templet:function (d) {
                            var moneyOverHour="";
                            if(d.moneyOverSecond>0){
                                moneyOverHour=formatSeconds(d.moneyOverSecond);
                            }
                            return d.moneyOverDay+"天"+moneyOverHour;
                        }}
                    ,{field:'restOverDay', title:'调休', width:130, align:'center',unresize:true,templet:function (d){
                            var restOverHour="";
                            if(d.restOverSecond>0){
                                restOverHour=formatSeconds(d.restOverSecond);
                            }
                            return d.restOverDay+"天"+restOverHour;
                        }}
                ]];
            }else{
                //异常
                layer.msg('2024-03-01为新版本记录，查询时间要么全部小于2024-03-01，要么全部大于等于2024-03-01', {icon: 5});
                layer.closeAll('loading');
                return false;
            }
            console.log(url);
            console.log(colsArray);

            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url :url
                ,height: 'full-250'    //容器高度
                ,where : data//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols :colsArray
                ,page : true
                ,limits:[10,20,30,40,50,100,2000]
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });
        }

        Date.prototype.format = function() {
            var s = '';
            var mouth = (this.getMonth() + 1)>=10?(this.getMonth() + 1):('0'+(this.getMonth() + 1));
            var day = this.getDate()>=10?this.getDate():('0'+this.getDate());
            s += this.getFullYear() + '-'; // 获取年份。
            s += mouth + "-"; // 获取月份。
            s += day; // 获取日。
            return (s); // 返回日期。
        }

        function getAllDate(begin, end) {
            var arr = [];
            var ab = begin.split("-");
            var ae = end.split("-");
            var db = new Date();
            db.setUTCFullYear(ab[0], ab[1] - 1, ab[2]);
            var de = new Date();
            de.setUTCFullYear(ae[0], ae[1] - 1, ae[2]);
            var unixDb = db.getTime() - 24 * 60 * 60 * 1000;
            var unixDe = de.getTime() - 24 * 60 * 60 * 1000;
            for (var k = unixDb; k <= unixDe;) {
                //console.log((new Date(parseInt(k))).format());
                k = k + 24 * 60 * 60 * 1000;
                arr.push((new Date(parseInt(k))).format());
            }
            return arr;
        }

        form.on("checkbox(siam_all)", function () {
            var status = $(this).prop("checked");
            $.each($("input[name=siam_one]"), function (i, value) {
                $(this).prop("checked", status);
            });
            form.render();
        });

        $("#examine").on('click',function (){
            layer.confirm('确定审核选中记录吗?', {icon: 3, title:'提示'}, function(index){
                //do something
                var ids = [];
                $.each($("input[name=siam_one]:checked"), function (i, value) {
                    ids[i] = $(this).attr("data-id");  // 如果需要获取其他的值 需要在模板中把值放到属性中 然后这里就可以拿到了
                });
                console.log(ids);
                util.sendAjax({
                    url: '#(ctxPath)/employeeCheckin/examineEmpDaySummary',
                    type: 'post',
                    data: {'dateRange': $('#dateRange').val(),'ids':JSON.stringify(ids)},
                    notice: true,
                    loadFlag:true,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            var ids=[];
                            $.each(deptSelect.getValue(),function (index,item) {
                                ids.push(item.id);
                            });
                            layer.load();
                            loadTable({"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()});
                        }
                    }
                });
                layer.close(index);
            });

        })

        $("#examineAll").on('click',function (){
            layer.confirm('确定审核全部记录吗?', {icon: 3, title:'提示'}, function(index){
                var ids=[];
                $.each(deptSelect.getValue(),function (index,item) {
                    ids.push(item.id);
                });
                util.sendAjax({
                    url: '#(ctxPath)/employeeCheckin/examineEmpDaySummaryAll',
                    type: 'post',
                    data: {'dateRange': $('#dateRange').val(),'id':ids[0]},
                    notice: true,
                    loadFlag:true,
                    success: function (returnData) {
                        if (returnData.state === 'ok') {
                            var ids=[];
                            $.each(deptSelect.getValue(),function (index,item) {
                                ids.push(item.id);
                            });
                            layer.load();
                            loadTable({"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()});
                        }
                    }
                });
                layer.close(index);
            });

        })

        function formatSeconds(value){
            let result = parseInt(value)
            let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600);
            let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60));
            let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60));

            let res = '';
            if(h !== '00') res += `${h}时`;
            if(m !== '00') res += `${m}分`;
            return res;
        }
        loadTable({'archiveStatus':$("#archiveStatus").val()});

        table.on('tool('+tableId+')',function (obj) {
            if(obj.event==='daily'){
                pop_show("["+obj.data.fullName+']打卡日报','#(ctxPath)/employeeCheckin/empCheckinDaySummaryDetailIndex?empId='+obj.data.id+"&dateRange="+$("#dateRange").val()+"&fullName="+obj.data.fullName,1400,700);
            }else if(obj.event==='detail') {
                pop_show("[" + obj.data.fullName + ']打卡明细', '#(ctxPath)/employeeCheckin/empCheckinRecordIndex?empId=' + obj.data.id + "&dateRange=" + $("#dateRange").val() + "&fullName=" + obj.data.fullName, 1200, 700);
            }else if(obj.event==='newDaily'){
                pop_show("["+obj.data.fullName+']新打卡日报','#(ctxPath)/employeeCheckin/empCheckinDaySummaryDetailIndexNew?empId='+obj.data.id+"&dateRange="+$("#dateRange").val()+"&fullName="+obj.data.fullName,1400,700);
            }
        })

        $("#search").on('click',function () {
            var ids=[];
            $.each(deptSelect.getValue(),function (index,item) {
                ids.push(item.id);
            });
            loadTable({"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()});
        })

        $("#export").on('click',function () {
            layer.confirm("确定导出吗?",function(index){
                layer.load();
                var ids=[];
                var names=[];
                $.each(deptSelect.getValue(),function (index,item) {
                    ids.push(item.id);
                    names.push(item.name);
                });
                var data={"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()};
                /*$.post("#(ctxPath)/employeeCheckin/empCheckinDaySummaryExportCheck?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)+"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()
                    +"&archiveStatus="+$("#archiveStatus").val(), {},function (d) {
                    if (d.state === 'ok') {*/
                        window.location.href="#(ctxPath)/employeeCheckin/empCheckinDaySummaryExport3?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)
                            +"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()+"&archiveStatus="+$("#archiveStatus").val();
                        //layer.closeAll('loading');
                    /*}else{
                        window.top.layer.msg(d.msg, {icon: 2, offset: 'auto'});
                        layer.closeAll('loading');
                    }
                })*/

                setTimeout(function (){
                    layer.closeAll('loading');
                },10000);


                layer.close(index) ;
            });
        })

        function DateDiff(sDate, eDate) { //sDate和eDate是yyyy-MM-dd格式
            var date1 = new Date(sDate);
            var date2 = new Date(eDate);
            var date3=date2.getTime()-date1.getTime();
            var days=Math.floor(date3/(24*3600*1000));
            return days;
        }

        $("#export2").on('click',function () {
            var array=$("#dateRange").val().split(" - ");
            var days=DateDiff(array[0],array[1]);
            if(days>30){
                layer.msg('一次导出的天数不能超过31天', {icon: 5});
                return false;
            }
            layer.confirm("确定导出吗?",function(index){
                layer.load();
                var ids=[];
                var names=[];
                $.each(deptSelect.getValue(),function (index,item) {
                    ids.push(item.id);
                    names.push(item.name);
                });


                var data={"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()};
                /*$.post("#(ctxPath)/employeeCheckin/empCheckinDaySummaryExportCheck?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)+"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()
                    +"&archiveStatus="+$("#archiveStatus").val(), {},function (d) {
                    if (d.state === 'ok') {*/
                window.location.href="#(ctxPath)/employeeCheckin/empCheckinDaySummaryExport6?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)
                    +"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()+"&archiveStatus="+$("#archiveStatus").val();
                /*}else{
                    window.top.layer.msg(d.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll('loading');
                }
            })*/
                setTimeout(function (){
                    layer.closeAll('loading');
                },10000);


                layer.close(index) ;
            });
        })

        $("#exportByPage").on('click',function () {
            var array=$("#dateRange").val().split(" - ");
            var days=DateDiff(array[0],array[1]);
            if(days>30){
                layer.msg('一次导出的天数不能超过31天', {icon: 5});
                return false;
            }
            layer.open({//parent表示打开二级弹框
                type: 1,
                title: "查看详情",
                shadeClose: false,
                shade: 0.5,
                btn: ['确定', '关闭'],
                maxmin: false, //开启最大化最小化按钮
                area:['400px;','400px;'],
                content: `
                    <form class="layui-form layui-form-pane" lay-filter="layform" id="noticeForm" style='padding: 5px;'>
                        <div class="layui-form-item">
                            <label class="layui-form-label"><font color='red'>*</font>导出批次</label>
                            <div class="layui-input-block"  >
                            <select id='page'>
                                 <option value=''>请选择</option>
                                 <option value='1'>第1到第200名员工</option>
                                 <option value='2'>第201到第400名员工</option>
                            </select>
                            </div>
                        </div>
                    </form>
                `,
                cancel: function(){
                },
                end : function(){
                },yes: function(index, layero){

                    if($("#page").val()==''){
                        layer.msg('导出批次必须选择', {icon: 2, offset: 'auto'});
                        return false;
                    }
                    layer.load();
                    var ids=[];
                    var names=[];
                    $.each(deptSelect.getValue(),function (index,item) {
                        ids.push(item.id);
                        names.push(item.name);
                    });
                    window.location.href="#(ctxPath)/employeeCheckin/empCheckinDaySummaryExport6?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)
                        +"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()+"&archiveStatus="+$("#archiveStatus").val()+"&page="+$("#page").val();

                    layer.close(index);
                    setTimeout(function (){
                        layer.closeAll('loading');
                    },10000);

                    return false;
                }
            })
            form.render();


        })

        $("#export3").on('click',function () {
            var array=$("#dateRange").val().split(" - ");
            var days=DateDiff(array[0],array[1]);
            if(days>30){
                layer.msg('一次导出的天数不能超过31天', {icon: 5});
                return false;
            }
            layer.confirm("确定导出吗?",function(index){
                layer.load();
                var ids=[];
                var names=[];
                $.each(deptSelect.getValue(),function (index,item) {
                    ids.push(item.id);
                    names.push(item.name);
                });


                var data={"deptIds":JSON.stringify(ids),"fullName":$("#fullName").val(),"dateRange":$("#dateRange").val(),'archiveStatus':$("#archiveStatus").val()};
                /*$.post("#(ctxPath)/employeeCheckin/empCheckinDaySummaryExportCheck?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)+"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()
                    +"&archiveStatus="+$("#archiveStatus").val(), {},function (d) {
                    if (d.state === 'ok') {*/
                window.location.href="#(ctxPath)/employeeCheckin/empCheckinDaySummaryExport6?deptIds="+JSON.stringify(ids)+"&deptNames="+JSON.stringify(names)
                    +"&fullName="+$("#fullName").val()+"&dateRange="+$("#dateRange").val()+"&archiveStatus="+$("#archiveStatus").val();
                /*}else{
                    window.top.layer.msg(d.msg, {icon: 2, offset: 'auto'});
                    layer.closeAll('loading');
                }
            })*/
                setTimeout(function (){
                    layer.closeAll('loading');
                },10000);


                layer.close(index) ;
            });
        })

    });
</script>
#end