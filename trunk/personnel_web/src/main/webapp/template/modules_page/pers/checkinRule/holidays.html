#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/dtree.css">
<link rel="stylesheet" href="/static/js/extend/layui_ext/dtree/font/dtreefont.css">
<link rel="stylesheet" href="/static/css/formSelects-v4.css">
<style>
    #test-n2 ,#test-n2 .layui-laydate-main,#test-n2 .layui-inline,#test-n2 .layui-laydate{
        width: 100%;
    }
    #test-n2 table {
        width: 100%;
        height: 420px;
    }
    #test-n2 .layui-laydate-content td {
        border: 1px solid #d2d2d2;
    }
    .layui-layer {
        border-radius: 10px !important;
    }
    .layui-layer-page .layui-layer-content{
        overflow: visible !important;
    }
    #select{
        display: none;
    }
    .colorClass .layui-layer-btn1{
        background-color: #FF5722;
        color: #fff;
    }
    .colorClass .layui-layer-btn a{
        border-radius: 4px;
    }
</style>
#end

#define content()
<div class="layui-fluid" >
    <input type="hidden" name="ruleId" id="ruleId" value="#(id??)">
    <div class="">
        <div class="layui-inline" id="test-n2" ></div>
    </div>
    <div class="text_box" id="select">
        <form class="layui-form" action="">
            <div class="layui-form-item layui-form-text" style="margin: 10px 10px 0;">
                <select id="text_book" name="text_book"  xm-select="text_book" xm-select-radio="" >
                </select>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">
        <a class="layui-btn layui-btn-xs" lay-event="choice">选择</a>
    </div>
</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>
<script src="/static/js/extend/formSelects-v4.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'laydate','layer','form'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , laydate = layui.laydate
            , $ = layui.$
            ,form=layui.form
            ,formSelects=layui.formSelects
        ;

        var sysScheduleArray = [];
        var sysScheduleUserArray = [];
        //人员选择
        formSelects.data('text_book', 'local',{
            arr: [
                {"userName": "上班", "userId": 1},
                {"userName": "休息", "userId": 2}
            ],keyName: 'userName',
            keyVal: 'userId'
        });

        /*admin.req('sysScheduleUser/page', function (res) {
            sysScheduleUserArray = res.data;
        }, {async: false});*/

        //定义json
        var userNames = [];
        var new_date = new Date();
        loding_date(new_date);
        //日历插件调用方法
        function loding_date(date_value){
            laydate.render({
                elem: '#test-n2'
                ,theme: '#3982f7'
                ,type: 'date'
                ,max: '2099-06-16 23:59:59'
                ,position: 'static'
                ,range: false
                ,value:date_value
                ,btns:false
                ,ready: function(data){
                    getSysSchedule();
                },
                change:function (value, date, endDate){
                    getSysSchedule();
                }
                ,done: function(value, date, endDate){
                    date_chose(value);
                }
            });
        }
        //数据回显
        function getSysSchedule(){
            /*admin.req('sysSchedule/page', function (res) {
                sysScheduleArray = res.data;
            }, {async: false});*/
            sysScheduleUserArray=[{"userName": "上班", "userId": 1},
                {"userName": "休息", "userId": 2}];
            sysScheduleArray=[]
            $.post('#(ctxPath)/employeeCheckinRule/getHolidaysByRule',{'ruleId':$("#ruleId").val()},function (res){
                if(res.state=='ok'){
                    if(res.data.workDays!=undefined && res.data.workDays!=null && res.data.workDays!='' && typeof(res.data.workDays)!='undefined' ){
                        var workDays=JSON.parse(res.data.workDays);
                        console.log(workDays);
                        if(workDays.length>0){
                            $.each(workDays,function (index,item){
                                sysScheduleArray.push({"userId":1,"scheduleDate":item});
                            });
                        }
                    }

                    if(res.data.restDays!=undefined && res.data.restDays!=null && res.data.restDays!='' && typeof(res.data.restDays)!='undefined' ) {
                        var restDays = JSON.parse(res.data.restDays);
                        if (restDays.length > 0) {
                            $.each(restDays, function (index, item) {
                                sysScheduleArray.push({"userId": 2, "scheduleDate": item});
                            });
                        }
                    }
                    sysScheduleArray.forEach(item=>{
                        var itemDate=new Date(item.scheduleDate);
                        console.log(itemDate.getFullYear()+'-'+(itemDate.getMonth()+1)+'-'+itemDate.getDate());
                        var choosedate = $('.layui-laydate-content td[lay-ymd='+itemDate.getFullYear()+'-'+(itemDate.getMonth()+1)+'-'+itemDate.getDate()+']');//选中所有显示日期的td
                        if(item.userId=='1'){
                            choosedate.append('<div>上班</div>');
                        }else if(item.userId=='2'){
                            choosedate.append('<div>休息</div>');
                        }
                    })
                    /*sysScheduleUserArray.forEach(item1=>{

                    })*/
                }
            });


        }
        //获取选中id
        var scheduleIds = [];
        var selectedName = [];
        var selected = [];
        //定义弹出层方法
        function date_chose(obj_date){
            var index = layer.open({
                type: 1,
                skin:'colorClass',
                title:'编辑节假日',
                area: ['400px', '200px'], //宽高
                btn:['确定','删除','取消'],
                content: $('#select')
                ,success:function(){
                    //选中值回显
                    selectedName = [];
                    scheduleIds = [];
                    selected = [];
                    sysScheduleUserArray.forEach(item1=>{
                        sysScheduleArray.forEach(item=>{
                            item.scheduleDate = item.scheduleDate.substring(0,10);
                            if(item1.userId ==item.userId && item.scheduleDate == obj_date){
                                selectedName.push(item1.userId);
                                scheduleIds.push(item1.userId);
                                selected.push(item);
                            }
                        })
                    })
                    formSelects.value('text_book',selectedName);
                }
                ,yes:function (){
                    userNames = [];
                    let selectedArray = formSelects.value('text_book');
                    let isHas = '';
                    //调用添加/编辑标注方法
                    if(selectedArray.length > 0){
                        selectedArray.forEach((item,index)=>{
                            isHas = selectedName.findIndex(function (d){ return  item.userId == d});
                            if(isHas == -1){
                                userNames.push(item.userId);
                                let form = {};
                                form.userId = item.userId;
                                form.scheduleDate = obj_date;
                                console.log(JSON.stringify(form));
                                //saveHolidaysByRule
                                $.post('#(ctxPath)/employeeCheckinRule/saveHolidaysByRule',{'ruleId':$("#ruleId").val(),"type":item.userId,"date":obj_date},function (res){
                                    if(res.state=='ok'){
                                        layer.msg(res.msg, {icon: 1, time: 1000});
                                    }else{
                                        layer.msg(res.msg, {icon: 2, time: 1000});
                                    }

                                });
                            }else{
                                selected = selected.filter(function (d){return item.userId != d.userId})
                            }
                        })
                        if(isHas != -1){
                            var ids = selected.map(function (d) {
                                return {"scheduleId": d.scheduleId};
                            });
                            admin.req('sysSchedule/delete', JSON.stringify(ids), function(res){
                                layer.msg(res.message, {icon: 1, time: 1000});
                                //原理同添加一致
                                $('#test-n2').html('');
                                loding_date(obj_date,userNames);
                            }, 'post');
                        }
                        setTimeout(function () { chose_moban(obj_date,userNames); }, 300);

                        layer.close(index);
                    }else{
                        layer.msg('不能为空', {icon: 2});
                    }
                },btn2:function (){
                    chexiao(obj_date,scheduleIds);
                }
            });
        }
        //定义添加/编辑标注方法
        function chose_moban(obj_date){
            $('#test-n2').html('');//重要！由于插件是嵌套指定容器，再次调用前需要清空原日历控件
            //再次调用日历控件，
            loding_date(obj_date);//重要！，再标注一个日期后会刷新当前日期变为初始值，所以必须调用当前选定日期。
        }
        //撤销选择
        function chexiao(obj_date,scheduleIds){
            if (scheduleIds.length === 0) {
                layer.msg('该日期未添加数据', {icon: 2});
                return;
            }
            layer.confirm('确定要作废该数据吗？', {
                skin: 'layui-layer-admin',
                shade: .1
            }, function () {
                var ids = scheduleIds.map(function (d) {
                    return {"scheduleId": d};
                });
                $.post('#(ctxPath)/employeeCheckinRule/delHolidaysDate',{'ruleId':$("#ruleId").val(),"type":scheduleIds[0],"date":obj_date},function (res){
                    if(res.state=='ok'){
                        $('#test-n2').html('');
                        loding_date(obj_date,userNames);
                        layer.msg(res.msg, {icon: 1, time: 1000});
                    }else{
                        layer.msg(res.msg, {icon: 2, time: 1000});
                    }

                });
                /*admin.req('sysSchedule/delete', JSON.stringify(ids), function(res){
                    layer.msg(res.message, {icon: 1, time: 1000});
                    //原理同添加一致
                    $('#test-n2').html('');
                    loding_date(obj_date,userNames);
                }, 'post');*/
            });
        }

        // 去除0 参数 日期 如 2020-07-08 返回为 2020-7-8
        function dislodgeZero(str) {
            let strArray = str.split("-");
            strArray = strArray.map(function(val) {
                if (val[0] == "0") {
                    return (val = val.slice(1));
                } else {
                    return val;
                }
            });
            return strArray.join("-");
        }


    });
</script>
#end