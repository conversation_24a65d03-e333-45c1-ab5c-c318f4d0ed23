#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row">
            <label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 5px 10px;">

                    </div>
                </div>
            </div>

            <div class="layui-input-inline">
                <label style="margin-left: 10px;">在职状态：</label>
                <div class="layui-inline" >
                    <select name="archiveStatus" id="archiveStatus">
                        #dictOption("archive_status", "incumbency", "")
                    </select>
                </div>
            </div>

            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:dispatch:addBtn")
            <button class="layui-btn" id="batchBtn" type="button">批量操作</button>
            #end
            #shiroHasPermission("emp:dispatch:addBtn")
            <button class="layui-btn" id="exportBtn" type="button">导出</button>
            #end

        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:dispatch:editBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}

    {{#}}}
    ]]#

    #end

    <a class="layui-btn layui-btn-xs" lay-event="edit">查看</a>

</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });



        sd=form.on("submit(search)",function(data){
            reloadTable();
            return false;
        });



        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });



        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"fullName":$("#name").val(),'archiveStatus':$("#archiveStatus").val(),'deptId':id});
        }

        reloadTable();

        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#batchBtn").on('click',function () {
            var url='#(ctxPath)/employeeLeaveBalance/batchForm';
            pop_show("批量操作员工余假",url,'100%','100%');
        });

        $("#exportBtn").on('click',function () {
            layer.load();
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            var url='#(ctxPath)/employeeLeaveBalance/exportList?fullName='+$('#name').val()+"&archiveStatus="+$('#archiveStatus').val()+"&deptId="+id;
            window.location.href=url;
            setTimeout(function (){
                layer.closeAll('loading');
            },20000);
        })

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/employeeLeaveBalance/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'fullName',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return d.fullName+"("+d.workNum+")";
                        }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true}
                    ,{field:'positionName', title: '职位', align: 'center', unresize: true}
                    ,{field: 'dayWorkTotalHour',width: 140,title: '一个工作日几小时', align: 'center', unresize: true}
                    ,{field:'affairLeave', title: '事假', align: 'center', unresize: true}
                    ,{field:'illnessLeave', title: '病假', align: 'center', unresize: true}
                    ,{field:'marryLeave', title: '婚假', align: 'center', unresize: true}
                    ,{field:'FuneralLeave', title: '丧假', align: 'center', unresize: true}
                    ,{field:'nursingLeave', title: '护理假', align: 'center', unresize: true}
                    ,{field:'yearLeave', title: '年休假', align: 'center', unresize: true}
                    ,{field:'industrialInjury', title: '工伤假', align: 'center', unresize: true}
                    ,{field:'fillLeave', title: '补休', align: 'center', unresize: true}
                    ,{field:'lactationLeave', title: '哺乳假', align: 'center', unresize: true}
                    ,{field:'maternityLeave', title: '产假', align: 'center', unresize: true}
                    ,{field:'accompanyMaternityLeave',title: '陪产假', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 100, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){
                    var url='#(ctxPath)/employeeLeaveBalance/form?empId='+obj.data.empId;
                    pop_show("["+obj.data.deptName+"："+obj.data.fullName+"]的假期",url,1300,700);
                }
            })


        };


    });
</script>
#end