#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()功能配置编辑#end

#define css()
<link rel="stylesheet" type="text/css" href="#(ctxPath)/static/css/formSelects-v4.css"/>
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/formSelects-v4.js"></script>
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/pers/role/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#form").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.carTypeTableReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form layui-form-pane" style="margin-left: 0px;margin-top: 0px;" id="form">
    <div class="layui-form-item" style="margin-top: 10px;" >
        <label class="layui-form-label" style="width: 150px;">角色名称</label>
        <div class="layui-input-block" style="margin-left: 150px;">
            <input type="text" name="roleName" lay-verify="required" value="#(record.roleName??)" placeholder="请输入角色名称" autocomplete="off" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item"  >
        <label class="layui-form-label" style="padding: 8px 5px;">分组</label>
        <div class="layui-input-block">
            <select xm-select="groupIds" name="groupIds" id="groupIds">
                #for(userGroup : userGroupList)
                <option value="#(userGroup.id??)"  #if(groupIds??!=null && groupIds.indexOf(userGroup.id??)!=-1) selected #end >#(userGroup.groupName??)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px;">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnabled" value="1" title="是" #if(record==null || record.isEnabled??=='1') checked #end >
            <input type="radio" name="isEnabled" value="0" title="否" #if(record.isEnabled??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left:-10px;">
        <label class="layui-form-label"><span style="color: red">*</span>角色级别</label>
        <div class="layui-input-block">
            <select id="lv" name="lv" lay-verify="required" lay-search>
                <option value="">请选择职位级别</option>
                #for(lv : positionLvs)
                <option value="#(lv.key)" #if(lv.key==record.lv??) selected #end>#(lv.value)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px;">是否总部管理人员</label>
        <div class="layui-input-block">
            <input type="radio" name="isGroupManager" value="1" title="是" #if(record.isGroupManager??=='1') checked #end >
            <input type="radio" name="isGroupManager" value="0" title="否" #if(record==null || record.isGroupManager??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px;">是否基地管理人员</label>
        <div class="layui-input-block">
            <input type="radio" name="isBaseManager" value="1" title="是" #if(record.isBaseManager??=='1') checked #end >
            <input type="radio" name="isBaseManager" value="0" title="否" #if(record==null || record.isBaseManager??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px;">是否市场管理人员</label>
        <div class="layui-input-block">
            <input type="radio" name="isMarketManager" value="1" title="是" #if(record.isMarketManager??=='1') checked #end >
            <input type="radio" name="isMarketManager" value="0" title="否" #if(record==null || record.isMarketManager??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px;">是否餐饮人员</label>
        <div class="layui-input-block">
            <input type="radio" name="isCateringStaff" value="1" title="是" #if(record.isCateringStaff??=='1') checked #end >
            <input type="radio" name="isCateringStaff" value="0" title="否" #if(record==null || record.isCateringStaff??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px;">是否财务人员</label>
        <div class="layui-input-block">
            <input type="radio" name="isFinanceStaff" value="1" title="是" #if(record.isFinanceStaff??=='1') checked #end >
            <input type="radio" name="isFinanceStaff" value="0" title="否" #if(record==null || record.isFinanceStaff??=='0') checked #end>
        </div>
    </div>
    <div class="layui-form-item layui-form-text" style="margin-bottom: 60px;">
        <label class="layui-form-label">备注</label>
        <div class="layui-input-block">
            <textarea name="remark" placeholder="请输入内容" class="layui-textarea">#(record.remark??)</textarea>
        </div>
    </div>
    <div class="layui-form-footer">
        <div class="pull-right">
            <input type="hidden" name="id"  value="#(record.id??)">
            <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
            <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
        </div>
    </div>
</form>
#end
