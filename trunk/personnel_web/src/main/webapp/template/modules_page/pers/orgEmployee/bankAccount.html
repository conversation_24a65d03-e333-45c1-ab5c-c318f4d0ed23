#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()银行账户管理页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<div class="layui-tab layui-tab-card" lay-filter="accountTab">
		<ul class="layui-tab-title">
			<li lay-id="accountList" class="layui-this">列表</li>
		</ul>
		<div class="layui-tab-content">
			<div class="layui-tab-item layui-show">
				<div class="layui-row">
					<input type="hidden" id="empId" value="#(empId??'')">
					<a id="btn-add" class="layui-btn btn-add btn-default">添加</a>
					<a id="btn-refresh" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
				</div>
				<div class="layui-row">
					<table id="accountTable" lay-filter="accountTable"></table>
				</div>
			</div>
		</div>
	</div>
</div>
#end

#define js()
<script type="text/html" id="accountTableBar">
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	<a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="del">作废</a>
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table', 'form', 'element', 'vip_table'], function() {
	// 操作对象
	var table = layui.table
	, form = layui.form
    , layer = layui.layer
    , element = layui.element
	, $ = layui.$
    , vipTable = layui.vip_table
    , tableId = 'accountTable'
    ;
	
	// 表格加载渲染
	var tableObj = table.render({
		id : tableId
		,elem : '#'+tableId
		,method : 'POST'
		,url : '#(ctxPath)/persOrgEmployee/accountPage'
		,where : {empId:$('#empId').val()}//额外查询条件
		,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
		,cols : [[ 
			{field:'', title:'序号', width:60, align:'center', templet:"<div>{{d.LAY_TABLE_INDEX+1}}</div>"}
			,{field:'bankName', title:'银行名称', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{field:'subBranch', title:'开户支行', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{field:'accountType', title:'账户类型', width:100, align:'center', templet:'#dictTpl("account_type", "account_type")'}
			,{field:'bankAccount', title:'银行账号', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{field:'holderName', title:'持卡人姓名', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{field:'stayPhoneNum', title:'预留手机号', align:'center'}//width 支持：数字、百分比和不填写。你还可以通过 minWidth 参数局部定义当前单元格的最小宽度，layui 2.2.1 新增
			,{fixed:'right', title:'操作', width:100, align:'center', toolbar:'#accountTableBar'}
		]]
		,page : true
	});
	//监听工具条
	table.on('tool('+tableId+')', function(obj) {
		if (obj.event === 'edit') {
			editTab('编辑','#(ctxPath)/persOrgEmployee/editEmpAccount', {id:obj.data.id});
		} else if (obj.event === 'del') {
			layer.confirm('您确定要作废当前登陆帐号?', function(index) {
				//作废操作
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/persOrgEmployee/saveEmpAccount',
		            data: {id:obj.data.id, delFlag:'1'},
		            notice: true,
				    loadFlag: true,
		            success : function(rep){
		            	if(rep.state=='ok'){
		            		pageTableReload();
				    	}
		            },
		            complete : function() {
				    }
		        });
				layer.close(index);
			});
		}
	});
	//重载表格
    pageTableReload = function () {
    	tableReload(tableId,{empId:$('#empId').val()});
    }
	//添加/编辑页签方法
	editTab = function(title,url,data) {
		$("#accountTab li").removeAttr("class");
		element.tabAdd('accountTab', {
			title: title
			,content: '<div id="edit_content"></div>' //支持传入html
			,id: 'accountEdit'
		});
		$('#edit_content').load(url, data, function(response,status,xhr){
			if(status=='success'){
				form.render('select', '');
				element.tabChange('accountTab', 'accountEdit');
			}else{
				layer.msg("添加失败!",{icon:5});
			}
		});
    };
	//关闭编辑页签
    closeEditTab = function () {
    	//作废指定Tab项
		element.tabDelete('accountTab', 'accountEdit');
    }
	element.on('tab(accountTab)', function(data){
		var tabId = $(this).attr("lay-id");//当前Tab标题所在的原始DOM元素的lay-id属性值
		if(tabId=='accountList'){
			closeEditTab();
		}
	});
	//添加按钮点击事件
	$('#btn-add').on('click', function() {
		editTab('添加','#(ctxPath)/persOrgEmployee/editEmpAccount', {empId:$("#empId").val()});
	});
	// 刷新
    $('#btn-refresh').on('click', function () {
    	pageTableReload();
    });
});
</script>
#end