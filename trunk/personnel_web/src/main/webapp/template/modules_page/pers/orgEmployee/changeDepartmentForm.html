#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()机构员工编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/ztree/3.5.12/css/zTreeStyle/zTreeStyle.min.css">
#end

#define content()
<div class="layui-col-xs3 layui-col-sm3 layui-col-md3 layui-col-lg3">
    <fieldset class="layui-elem-field layui-field-title" style="display:block;">
        <legend>组织架构</legend>
        <div id="zTreeDiv" class="ztree" style="height:330px;overflow:auto;"></div>
    </fieldset>
</div>
<div class="layui-col-xs9 layui-col-sm9 layui-col-md9 layui-col-lg9">

    <div class="layui-row">
        <div style="margin-bottom:23px;"></div>
        <form class="layui-form layui-form-pane" action="" id="employeeForm" style="margin-left:35px;">
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">原组织</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" lay-verify="required" class="layui-input" value="#(record.oldOrgName??)"  readonly>
                        <input type="hidden" name="oldOrgId" lay-verify="required" class="layui-input" value="#(record.oldOrgId??)"  readonly>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">原部门</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" id="idCard" class="layui-input" lay-verify="required" value="#(record.oldDeptName??)" readonly>
                        <input type="hidden" name="oldDeptId" lay-verify="required" class="layui-input" value="#(record.oldDeptId??)" >
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">原职位</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" class="layui-input" lay-verify="required" value="#(record.oldPositionName??)" autocomplete="off" readonly>
                        <input type="hidden" name="oldPosition" lay-verify="required" class="layui-input" value="#(record.oldPosition??)" >
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label">原薪资</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" class="layui-input"   value="#(record.oldSalary??)" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>新组织</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" id="orgName" name="orgName" readonly class="layui-input" lay-verify="required" placeholder="请选择新组织" value="#(orgName??)" autocomplete="off">
                        <input type="hidden" id="orgId" name="orgId" value="#(record.orgId??)">
                    </div>
                </div>

            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>新组织部门</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <select name="deptId" id="political" lay-filter="" lay-verify="required">
                            <option value="">请选择组织部门</option>
                            #if(record!=null)
                            #for(dept:deptList)
                            <option value="#(dept.id)" #if(dept.id==record.deptId??) selected #end>#(dept.orgName)</option>
                            #end
                            #end
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>新职位</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <div class="layui-form" lay-filter="positionSelectFilter">
                            <select id="position" name="position" lay-search="" lay-verify="required">
                                <option value="">请选择职位</option>
                                #if(record!=null)
                                #for(position:positionList)
                                <option value="#(position.id)" #if(position.id==record.position??) selected #end>#(position.positionName)</option>
                                #end
                                #end
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>新薪资</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" class="layui-input" id="salary" name="salary" placeholder="请输入新薪资" lay-verify="required|number" value="#(record.salary??)" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>变更时间</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="text" class="layui-input" id="changeDate" name="changeDate" placeholder="请选择变更时间" lay-verify="required" value="#date(record.changeDate??,'yyyy-MM-dd')" autocomplete="off">
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-inline">
                    <label class="layui-form-label"><font color="red">*</font>变更类型</label>
                    <div class="layui-input-inline" style="width:155px;">
                        <input type="radio" name="changeType" value="personal" title="个人申请" checked>
                        <input type="radio" name="changeType" value="internal" title="内部调用" >
                    </div>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">原因</label>
                <div class="layui-input-block">
                    <textarea name="remark" lay-verify="required" placeholder="请输入内容" class="layui-textarea">#(record.remark??)</textarea>
                </div>
            </div>

            <div style="margin-bottom:60px;"></div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="id" id="id" value="#(record.id??)">
                    <input type="hidden" name="employeeId" id="employeeId" value="#(employeeId??)">
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn layui-btn-primary" type="reset" id="resetBtn" style="display: none;">重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

#end

#define js()
<script src="#(ctxPath)/static/js/jquery-3.3.1.min.js"></script>
<script src="#(ctxPath)/static/plugins/ztree/3.5.12/js/jquery.ztree.all-3.5.min.js"></script>
<script type="text/javascript">
    layui.use([ 'form', 'laydate' ], function() {
        var form = layui.form
            , layer = layui.layer
            , laydate = layui.laydate
            , $ = layui.$
        ;

        //出生日期渲染
        laydate.render({
            elem: '#changeDate'
            , trigger: 'click'
        });


        var setting = {
            check:{enable:false}
            ,view:{selectedMulti:false}
            ,data:{simpleData:{enable:true}}
            ,async:{enable:true, type:"post", url:"#(ctxPath)/persOrg/orgFormTreeByUserId"}
            ,callback:{
                onClick: function(event, treeId, treeNode, clickFlag) {
                    /*layer.confirm('确定要分配所选择的机构？', function(index) {
                        util.sendAjax ({
                            type: 'POST',
                            url: '#(ctxPath)/persOrgEmployee/orgEmpSave',
                            data: {empId:'#(empId??)', orgId:treeNode.id},
                            notice: true,
                            loadFlag: true,
                            success : function(rep){
                                pageTableReload();
                            },
                            complete : function() {
                            }
                        });
                        layer.close(index);
                    });*/
                    $("#orgId").val(treeNode.id);
                    $("#orgName").val(treeNode.name);
                    //var data={'positionName':$("#positionName").val(),'orgId':treeNode.id};
                    //通过id获取部门、小组
                    //提交表单数据
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/persOrg/findDepartment',
                        data: {'orgId':treeNode.id},
                        notice: false,
                        loadFlag: false,
                        success : function(rep){
                            if(rep.state==='ok'){
                                var str='<option value="">请选择组织部门</option>';
                                var data=rep.data;
                                $.each(data,function (index,item) {
                                    str+='<option value="'+item.id+'">'+item.orgName+'</option>';
                                });
                                $("#political").html(str);
                                form.render('select');
                            }
                        },
                        complete : function() {
                        }
                    });

                    //通过id获取职位
                    //提交表单数据
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/pers/position/findPositionByOrgId',
                        data: {'orgId':treeNode.id},
                        notice: false,
                        loadFlag: false,
                        success : function(rep){
                            if(rep.state==='ok'){
                                var str='<option value="">请选择职位</option>';
                                var data=rep.data;
                                $.each(data,function (index,item) {
                                    if(item.id==''||typeof(item.id)=='undefined'){

                                    }else{
                                        str+='<option value="'+item.id+'">'+item.positionName+'</option>';
                                    }

                                });
                                $("#position").html(str);
                                form.render('select');
                            }
                        },
                        complete : function() {
                        }
                    });
                }
            }
        };

        // 初始化树结构
        var zTreeObj = $.fn.zTree.init($("#zTreeDiv"), setting);


        //自定义验证规则
        form.verify({
            phoneNumber: function(value){
                if(value!=null && value!=''){
                    var mobilePtn = /^1[3456789][0-9]{9}$/;
                    var landlinePtn = /^(0\d{2,3}-)?\d{7,8}$/;
                    if(!mobilePtn.test(value) && !landlinePtn.test(value)){
                        return '请输入正确的手机号码或座机格式';
                    }
                }
            },
            checkFormalDate:function (value) {
                if(value!=null && value!=''){
                    var entryTime=new Date($("#entryTime").val());
                    var formalDate=new Date(value);
                    if(entryTime>formalDate){
                        //layer.msg('', {icon: 2, offset: 'auto'});
                        return '转正日期不能小于入职日期';
                    }
                }
            }
        });





        $('#positionName').keydown(function(e){
            if(e.keyCode==13){
                var positionName = $("#positionName").val();
                if(positionName != null && positionName != ''){

                    //提交表单数据
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/dict/save',
                        data: {dictType:'position', dictTypeName:'职位', dictName:positionName, remarks:'职位'},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                var dictName = rep.dict.dictName;
                                var dictValue = rep.dict.dictValue;
                                $("#position").append("<option value='"+dictValue+"'>"+dictName+"</option>");
                                form.render('select', 'positionSelectFilter');
                            }
                        },
                        complete : function() {
                        }
                    });
                }else{
                    layer.msg('请输入职位名称!',{icon:5});
                }
                return false;
            }
        });

        //监听表单提交
        form.on('submit(saveBtn)', function(formObj) {

            //提交表单数据
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/persOrgEmployee/saveChangeDepartment',
                data: $(formObj.form).serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){

                        var id=$("#id").val();
                        if(id==='' || id===null){
                            parent.pageTableReload();
                            $("#resetBtn").click();
                        }else{
                            parent.pageTableReload();
                            pop_close();
                        }
                        pop_close();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end