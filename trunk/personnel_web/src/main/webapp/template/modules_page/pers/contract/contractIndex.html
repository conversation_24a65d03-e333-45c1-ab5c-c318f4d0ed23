#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()用户消息管理#end

#define css()
<style>
    .layui-table-cell{
        padding: 0 5px;
    }
</style>
#end

#define content()
<div style="margin: 15px;">
    <form class="layui-form" lay-filter="layform" id="noticeForm">
        <div class="layui-row" style="margin-bottom: 10px;">
            <label class="layui-form-label">姓名：</label>
            <div class="layui-input-inline" style="float: left;" >
                <input class="layui-input" name="name" id="name" >
            </div>
            <!--<label class="layui-form-label">转正时间</label>
            <div class="layui-input-inline" style="float: left;margin-right: 20px;" >
                <input type="text" name="date" id="date" readonly value="" autocomplete="off" class="layui-input">
            </div>-->

            <div class="layui-input-inline" style="float: left;">
                <label style="margin-left: 10px;">组织架构：</label>
                <div class="layui-inline"  style="width: 350px;">
                    <div id="deptSelect" style="margin: 0px 10px;">

                    </div>
                </div>
            </div>

            <label class="layui-form-label">员工状态</label>
            <div class="layui-input-inline" style="float: left;width: 115px;" >
                <select id="archiveStatus" name="archiveStatus" lay-filter="archiveStatus">
                    <option value="incumbency">在职</option>
                    <option value="quit">离职</option>
                </select>
            </div>

            <label class="layui-form-label">合同类型</label>
            <div class="layui-input-inline" style="float: left;width: 115px;" >
                <select id="contractType" name="contractType" lay-filter="contractType">
                    <option value="">全部</option>
                    <option value="1">劳动合同</option>
                    <option value="2">劳务合同</option>
                    <option value="3">临时合同</option>
                    <option value="5">实习协议</option>
                    <option value="6">临时工合同</option>
                </select>
            </div>

            <label class="layui-form-label">合同状态</label>
            <div class="layui-input-inline" style="float: left;width: 115px;" >
                <select id="contractStatus" name="contractStatus" lay-filter="contractStatus">
                    <option value="">全部</option>
                    <option value="1">无合同</option>
                    <option value="2">即将到期</option>
                    <option value="3">已到期</option>
                </select>
            </div>

            <button class="layui-btn" id="queryBtn" type="button" style="margin-left: 20px;">查询</button>
            #shiroHasPermission("emp:dispatch:addBtn")
<!--            <button class="layui-btn" id="addBtn" type="button">添加</button>-->
            #end
        </div>
    </form>
    <table id="entryApprovalTable" lay-filter="entryApprovalTable"></table>
    <input id="quitEmpId" name="quitEmpId" type="hidden" value="">
    <input id="fullName" name="fullName" type="hidden" value="">
    <input id="workNum" name="workNum" type="hidden" value="" >
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/html" id="actionBar">
    #shiroHasPermission("emp:contract:contractBtn")
    #[[
    {{#if(d.taskId==undefined || d.isSaveHandle){}}
    <a class="layui-btn layui-btn-xs" lay-event="edit">合同</a>
    {{#}}}
    ]]#

    #end


</script>
<script src="/static/js//xm-select.js" type="text/javascript" charset="utf-8"></script>

<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate=layui.laydate;

        laydate.render({
            elem: '#date'
            ,trigger: 'click'
            ,range: true
        });

        msgLoad({'archiveStatus':'incumbency'});

        sd=form.on("submit(search)",function(data){
            msgLoad(data.field);
            return false;
        });

        queryMsg = function(readFlag){
            var data = {"readFlag":readFlag};
            msgLoad(data);
        }

        var deptSelect = xmSelect.render({
            el: '#deptSelect',
            autoRow: true,
            height: '200px',
            prop: {
                name: 'name',
                value: 'id',
            },
            radio: true,
            filterable: true,//搜索
            tree: {
                show: true,
                expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                showFolderIcon: true,
                showLine: true,
                indent: 15,
                lazy: true,
                clickExpand: true,
                clickClose: true,
                strict: false,
                //点击节点是否选中
                clickCheck: true,


                load: function(item, cb){

                }
            },
            height: 'auto',
            data(){
                return [];
            }
        })

        $.post('#(ctxPath)/persOrg/permissionOrgTreeSelect',{},function (res) {
            deptSelect.update({
                data:res
            })
        });



        reloadTable=function () {
            var id="";
            $.each(deptSelect.getValue(),function (index,item) {
                id=item.id;
            });
            msgLoad({"name":$("#name").val(),'date':'','deptId':id,'archiveStatus':$("#archiveStatus").val(),'contractType':$("#contractType").val(),"contractStatus":$("#contractStatus").val()});
        }



        $("#queryBtn").on('click',function () {
            reloadTable();
        });

        $("#addBtn").on('click',function () {
            var url='#(ctxPath)/pers/approval/businessTripForm';
            pop_show("添加出差申请",url,1300,700);
        });

        function msgLoad(data){
            layer.load();
            table.render({
                id : 'entryApprovalTable'
                ,elem : '#entryApprovalTable'
                ,method : 'get'
                ,where : data
                ,height: 'full-150'
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/employeeContract/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'full_name',title:'姓名',width: 140, align: 'center', unresize: true,templet:function (d) {
                            return d.full_name+"("+d.work_num+")";
                        }},
                    {field:'sex',title:'性别', align: 'center',width: 70, unresize: true,templet:function (d) {
                            if(d.sex=='male'){
                                return '男';
                            }else if(d.sex=='female'){
                                return '女';
                            }else{
                                return '- -';
                            }
                        }}
                    ,{field:'deptName', title: '部门', align: 'center', unresize: true,templet:function (d) {

                            if(d.org==undefined || d.org.orgName==undefined){
                                return '';
                            }else{
                                return d.org.orgName;
                            }
                        }}

                    ,{field:'positionName', title: '职位', align: 'center', unresize: true,templet:function (d) {

                            if(d.org==undefined || d.org.positionName==undefined){
                                return '';
                            }else{
                                return d.org.positionName;
                            }
                        }}
                    ,{field:'entry_time', title: '入职日期', align: 'center', unresize: true,templet:function (d) {
                            return dateFormat(d.entry_time,'yyyy-MM-dd')
                        }}
                    ,{field:'employeeContractCount', title: '合同数量', align: 'center', unresize: true}
                    ,{field:'employeeContractCount', title: '合同类型', align: 'center', unresize: true,templet:function (d) {
                            if(d.employeeContract==undefined){
                                return '';
                            }else{
                                if(d.employeeContract.contractType=='1'){
                                    return '劳动合同';
                                }else if(d.employeeContract.contractType=='2'){
                                    return '劳务合同';
                                }else if(d.employeeContract.contractType=='3'){
                                    return '临时合同';
                                }else if(d.employeeContract.contractType=='5'){
                                    return '实习协议';
                                }else if(d.employeeContract.contractType=='6'){
                                    return '临时工合同';
                                }
                            }

                        }}
                    ,{field:'employeeContractCount', title: '合同开始结束日期', align: 'center', unresize: true,templet:function (d) {
                            if(d.employeeContract==undefined){
                                return '';
                            }else{
                                if(d.employeeContract.dateType=='1'){
                                    return dateFormat(d.employeeContract.contractStartDate,'yyyy-MM-dd')+' 至 '+dateFormat(d.employeeContract.contractEndDate,'yyyy-MM-dd')
                                }else if(d.employeeContract.dateType=='2'){
                                    return dateFormat(d.employeeContract.contractStartDate,'yyyy-MM-dd')+' 至 长期';
                                }

                            }

                        }}
                    ,{field:'employeeContractCount', title: '距离到期天数', align: 'center', unresize: true,templet:function (d) {
                            if(d.dateBetweenDays==undefined){
                                return '';
                            }else{
                                if("1"==d.employeeContract.dateType){
                                    if(d.dateBetweenDays>=0){
                                        return d.dateBetweenDays;
                                    }else{
                                        return '已到期';
                                    }
                                }else{
                                    return '- -';
                                }

                            }

                        }}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                    layer.closeAll('loading');
                }
            });


            table.on('tool(entryApprovalTable)',function (obj) {
                if(obj.event==='edit'){

                    pop_show("["+obj.data.full_name + ']的合同', '#(ctxPath)/persOrgEmployee/contractIndex?employeeId=' + obj.data.id, '100%', '100%');

                }
            })


        };


    });
</script>
#end