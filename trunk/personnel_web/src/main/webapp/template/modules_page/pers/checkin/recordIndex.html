#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()员工档案页面#end

#define css()
<style>
    .layui-table-cell {
        padding: 0 5px;
    }
</style>
#end

#define content()
#shiroHasPermission("emp:checkin:detailExport")
<div class="layui-row" style="float: right;padding-right: 30px;">
    <button type="button" class="layui-btn" id="export">导出</button>
</div>
#end
<div class="layui-row">

    <table id="employeeCheckinTable" lay-filter="employeeCheckinTable"></table>

</div>
#end

#define js()
<script type="text/html" id="empTableBar">
    <div class="layui-btn-group">
        #shiroHasPermission("emp:archives:changeDepartment:edit")
        <a class="layui-btn layui-btn-sm" lay-event="edit">编辑</a>
        #end
    </div>
</script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['table', 'vip_table','laydate'], function() {
        // 操作对象
        var table = layui.table
            , layer = layui.layer
            , vipTable = layui.vip_table
            , $ = layui.$
            , tableId = 'employeeCheckinTable'
            ,laydate=layui.laydate
        ;


        loadTable=function (data) {
            var tableObj = table.render({
                id : tableId
                ,elem : '#'+tableId
                ,method : 'POST'
                ,url : '#(ctxPath)/employeeCheckin/empCheckinRecordPage'
                ,height: vipTable.getFullHeight()*0.8    //容器高度
                ,where : data//额外查询条件
                ,cellMinWidth : 60 //全局定义常规单元格的最小宽度，layui 2.2.1 新增
                ,cols : [[
                    {field:'groupname', title:'打卡规则名称', width:150, align:'center',unresize:true}
                    ,{field:'checkinTime', title:'打卡时间', width:170, align:'',unresize:true,templet:function (d) {
                            if("未打卡"==d.exceptionType){
                                return dateFormat(d.checkinTime,'yyyy-MM-dd')
                            }else{
                                return dateFormat(d.checkinTime,'yyyy-MM-dd HH:mm:ss')
                            }
                        }}
                    ,{field:'checkinType', title:'打卡类型', width:110, align:'center',unresize:true,templet:function (d) {
                            var str=d.checkinType+"  ";
                            if(d.status=='1'){
                                str+="<span class=\"layui-badge layui-bg-orange\">补卡审批中</span>"
                            }else if(d.status=='2'){
                                str+="<span class=\"layui-badge layui-bg-green\">补卡成功</span>";
                            }else if(d.status=='3'){
                                str+="<span class=\"layui-badge\">补卡中止</span>"
                            }
                            return str;
                        }}
                    ,{field:'exceptionType', title:'异常类型', width:110, align:'center',unresize:true}
                    ,{field:'schCheckinTime', title:'标准打卡时间', width:165, align:'center',unresize:true,templet:function (d){
                            return dateFormat(d.schCheckinTime,'yyyy-MM-dd HH:mm:ss')
                        }}
                    ,{field:'notes', title:'打卡备注', width:80, align:'center',unresize:true}
                    /*,{field:'locationTitle', title:'打卡地址', align:'center',unresize:true}*/
                ]]
                ,page : true
                ,done:function () {
                    //
                    var layerTips;
                    $("td").on("mouseenter", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        if (this.offsetWidth < this.firstChild.scrollWidth) {
                            var that = this;
                            var text = $(this).text();
                            layerTips=layer.tips(text, that, {
                                tips: 1,
                                time: 0
                            });
                        }
                    });
                    $("td").on("mouseleave", function() {
                        //js主要利用offsetWidth和scrollWidth判断是否溢出。
                        //在这里scrollWidth是包含内容的完全高度，offsetWidth是当前表格单元格的宽度。
                        layer.close(layerTips);
                    });
                }
            });
        }

        loadTable({'empId':'#(empId??)','dateRange':'#(dateRange??)'});

        $("#export").on('click',function () {
            layer.confirm("确定导出吗?",function(index){
                window.location.href="#(ctxPath)/employeeCheckin/empCheckinRecordExport?empId=#(empId??)&fullName=#(fullName??)&dateRange=#(dateRange)";
                layer.close(index) ;
            });
        })

    });
</script>
#end