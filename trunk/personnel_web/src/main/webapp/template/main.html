#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#(APP.name)#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
	.layui-nav .layui-badge, .layui-nav .layui-badge-dot {
		position: absolute;
		right: 18px;
		top: 29px;
	}
</style>
#end

#define content()
<div class="layui-layout layui-layout-admin">
	<!-- 添加skin-1类可手动修改主题为纯白，添加skin-2类可手动修改主题为蓝白 -->
	<!-- header -->
	<div class="layui-header my-header">
		<a href="#(ctxPath)">
			<div class="my-header-logo">#(APP.name)</div>
		</a>
		<div class="my-header-btn">
			<button id="showHideBtn" class="layui-btn layui-btn-small btn-nav">
				<i class="layui-icon">&#xe65f;</i>
			</button>
		</div>
		
		<!-- 顶部左侧添加选项卡监听 -->
		<ul class="layui-nav" lay-filter="side-top-left"></ul>
		
		<!-- 顶部右侧添加选项卡监听 -->
		<ul class="layui-nav my-header-user-nav" lay-filter="side-top-right">
<!-- 			<li class="layui-nav-item" style="margin-right:10px;"> -->
<!-- 				<a class="name" href="javascript:;"> -->
<!-- 					<i class="layui-icon">&#xe629;</i>主题 -->
<!-- 				</a> -->
<!-- 				<dl class="layui-nav-child"> -->
<!-- 					<dd data-skin="0"> -->
<!-- 						<a href="javascript:;">默认</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="1"> -->
<!-- 						<a href="javascript:;">纯白</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="2"> -->
<!-- 						<a href="javascript:;">蓝白</a> -->
<!-- 					</dd> -->
<!-- 				</dl> -->
<!-- 			</li> -->
			#shiroHasPermission("pers:myMessage")
			<li class="layui-nav-item" style="margin-right: 10px;">
				<a href="javascript:;" href-url="#(ctxPath)/message/index">
					<i class="layui-icon">&#xe667;</i>
					<span id="unReadMessageCount" style="position: absolute;right: -5px;top: 29px;"></span>
				</a>
			</li>
			#end
			<li class="layui-nav-item" style="margin-right:10px;">
				<a class="name" href="javascript:;">
					<img class="layui-circle" src="#(ctxPath)/static/img/user.png" alt="logo"> 
					#shiroPrincipal() 
						#(principal) 
					#end 
				</a>
				<dl class="layui-nav-child">
					<dd>
						<a id="infoBtn" href="javascript:;" href-url="">个人资料</a>
					</dd>
					<dd>
						<a id="modPwdBtn" href="javascript:;" href-url="">修改密码</a>
					</dd>
				</dl>
			</li>
			<li class="layui-nav-item"><a href="#(ctxPath)/logout" class="layui-btn layui-btn-danger layui-btn-small btn-nav" href-url="">退出</a></li>
		</ul>
	</div>
	
	<!-- side -->
	<div class="layui-side my-side">
		<div class="layui-side-scroll">
			<!-- 左侧主菜单添加选项卡监听 -->
			<ul class="layui-nav layui-nav-tree" lay-filter="side-main">
				#shiroHasPermission("org:manage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-sitemap"></i>组织管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("org:maintain:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrg/index">组织维护</a>
						</dd>
						#end
						#shiroHasPermission("mainRole:manage:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/role/index">员工角色管理</a>
						</dd>
						#end
						#shiroHasPermission("mainRole:manage:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/branchCompanyRole/index">分公司角色管理</a>
						</dd>
						#end
						#shiroHasPermission("mainRole:manage:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/empPerformance/index">分公司业绩</a>
						</dd>
						#end
						#shiroHasPermission("position:manage:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/position/index">职位管理</a>
						</dd>
						#end
						#shiroHasPermission("position:userOrgMange:mange")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/persUserOrg/index">账号组织权限</a>
						</dd>
						#end
						<!--<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/socialAddress/index">社保地址管理</a>
						</dd>-->
						#shiroHasPermission("position:unit:mange")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/orgCompany/index">社保单位</a>
						</dd>
						#end
						#shiroHasPermission("position:unit:mange")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrg/orgHeadcountApplyIndex">组织编制申请</a>
						</dd>
						#end
						#shiroHasPermission("position:unit:mange")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/employeeCertificate/typeIndex">证件类型管理</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("pm:manage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-users"></i>人事管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("emp:recruitForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/recruitIndex">招聘需求申请</a>
						</dd>
						#end
						#shiroHasPermission("emp:entryInfoForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/entryInfoIndex">员工入职登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:agencyInfoForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/agencyInfoIndex">代理商员工预登记信息</a>
						</dd>
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/supplierEntryInfoIndex">供应商员工登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:archives:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrgEmployee/index">员工档案</a>
						</dd>
						#end
						#shiroHasPermission("emp:agencyArchives:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrgEmployee/index?type=1">代理商员工档案</a>
						</dd>
						#end
						#shiroHasPermission("emp:contract:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeContract/index">员工合同管理</a>
						</dd>
						#end

						#shiroHasPermission("emp:qualifiedForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/qualifiedIndex">员工转正登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:overTimeForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/overTimeIndex">员工加班登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:leaveRestForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/leaveRestIndex">员工请休假登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:dispatchForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/dispatchIndex">员工外派登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:dispatchForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/businessTripIndex">员工出差登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:changeForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/changeApplyIndex">员工异动登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:changeDeptForm:menu")
						<!--<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/changeDeptIndex">员工调岗登记</a>
						</dd>-->
						#end
						#shiroHasPermission("emp:employeeQuit:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/employeeQuitIndex">员工离职登记</a>
						</dd>
						#end
						#shiroHasPermission("emp:checkinRule:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeCheckinRule/index">员工打卡规则管理</a>
						</dd>
						#end
						#shiroHasPermission("emp:checkin:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeCheckin/reportFormIndex2">员工考勤统计</a>
						</dd>
						#end
						#shiroHasPermission("emp:checkin:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeLeaveBalance/index">员工余假</a>
						</dd>
						#end
						#shiroHasPermission("emp:checkinstatus:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeCheckin/dayCheckinStatusIndex">每日在岗人数查询</a>
						</dd>
						#end
						#shiroHasPermission("emp:fillCheckin:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeCheckin/fillCheckinIndex">员工补卡审批</a>
						</dd>
						#end
						#shiroHasPermission("emp:birthdayEmployee:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrgEmployee/birthdayEmployeeIndex">员工生日</a>
						</dd>
						#end
						#shiroHasPermission("emp:employeeWorkAge:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrgEmployee/employeeWorkAgeIndex">员工工龄</a>
						</dd>
						#end
						#shiroHasPermission("emp:employeeWorkAge:menuxx")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/socialSecurity/index">员工社保</a>
						</dd>
						#end
						#shiroHasPermission("emp:subordinate:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrgEmployee/subEmployeeIndex">我的下属</a>
						</dd>
						#end
						#shiroHasPermission("emp:providentFund:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/providentFund/providentFundIndex">员工公积金</a>
						</dd>
						#end
						#shiroHasPermission("emp:providentFundReportForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/providentFund/providentFundReportFormIndex2">公积金报表（经办人）</a>
						</dd>
						#end
						#shiroHasPermission("emp:socialSecurity:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/socialSecurityApply/index">员工社保</a>
						</dd>
						#end
						#shiroHasPermission("emp:socialSecurityReportForm:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/providentFund/socialSecurityReportFormIndex2">社保报表（经办人）</a>
						</dd>
						#end
						#shiroHasPermission("emp:liabilityInsurance:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/liabilityInsurance/liabilityInsuranceIndex">员工雇主责任险</a>
						</dd>
						#end

<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">入职申请</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">转正申请</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">调岗申请</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">离职申请</a> -->
<!-- 						</dd> -->
					</dl>
				</li>
				#end
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>考勤管理</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">考勤记录</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
				<!--<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-cogs"></i>合同管理</a>
					<dl class="layui-nav-child">
&lt;!&ndash; 						<dd> &ndash;&gt;
&lt;!&ndash; 							<a href="javascript:;" href-url="">合同维护</a> &ndash;&gt;
&lt;!&ndash; 						</dd> &ndash;&gt;
						<dd>
							<a href="javascript:;" href-url="">合同档案</a>
						</dd>
&lt;!&ndash; 						<dd> &ndash;&gt;
&lt;!&ndash; 							<a href="javascript:;" href-url="">合同续签</a> &ndash;&gt;
&lt;!&ndash; 						</dd> &ndash;&gt;
					</dl>
				</li>-->
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>绩效管理</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">绩效公式维护</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">绩效模板维护</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">绩效考核记录</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>薪酬管理</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">薪酬公式</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">薪酬模板</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">薪酬记录</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>社保管理</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">社保公式</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">社保档案</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>培训管理</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">培训模板</a> -->
<!-- 							<a href="javascript:;" href-url="">培训记录</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>招聘管理</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">招聘模板</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">招聘计划</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">应聘记录</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
				#shiroHasPermission("notice:manage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe667;</i>通知公告</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("notice:type:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/noticeType/index">公告类型设置</a>
						</dd>
						#end
						#shiroHasPermission("notice:release:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/notice/index">发布公告</a>
						</dd>
						#end
						#shiroHasPermission("notice:query:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/notice/lookIndex">公告查阅</a>
						</dd>
						#end
						#shiroHasPermission("notice:remind:menu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/employeeRemind/index">提醒</a>
						</dd>
						#end
						#shiroHasPermission("notice:sms:menu")
<!--						<dd>-->
<!--							<a href="javascript:;" href-url="#(ctxPath)/pers/sms/index">短信管理</a>-->
<!--						</dd>-->
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("pers:taskMenu")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-check-square-o"></i>审批</a>
					#shiroHasPermission("pers:myTaskManage")
					<dl class="layui-nav-child">
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/pers/approval/entryApprovalIndex">我的审批</a>
						</dd>
					</dl>
					#end
				</li>
				#end
				#shiroHasPermission("pers:myMessage")
				<li class="layui-nav-item">
					<a href="javascript:;" href-url="#(ctxPath)/message/index"><i class="fa fa-commenting-o"></i>我的消息</a>
				</li>
				#end
<!-- 				<li class="layui-nav-item"> -->
<!-- 					<a href="javascript:;"><i class="fa fa-cogs"></i>预警功能</a> -->
<!-- 					<dl class="layui-nav-child"> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">合同期满提醒</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">员工生日提醒</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">合同续签提醒</a> -->
<!-- 						</dd> -->
<!-- 						<dd> -->
<!-- 							<a href="javascript:;" href-url="">员工转正提醒</a> -->
<!-- 						</dd> -->
<!-- 					</dl> -->
<!-- 				</li> -->
				#shiroHasPermission("pers:systemSet")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-cogs"></i>系统设置</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("pers:permissionMenuManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/menu/index">菜单权限</a>
						</dd>
						#end
						#shiroHasPermission("pers:RoleManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/role/index">角色管理</a>
						</dd>
						#end
						#shiroHasPermission("pers:userManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/user/index">帐号管理</a>
						</dd>
						#end
						#shiroHasPermission("pers:groupUserManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/group/index">用户组管理</a>
						</dd>
						#end
						#if(userType??''=='system_admin')
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/persOrg/manageIndex">主机构管理</a>
						</dd>
<!--						<dd>-->
<!--							<a href="javascript:;" href-url="#(ctxPath)/dict/index"><i class="fa fa-tasks"></i>系统字典</a>-->
<!--						</dd>-->
						#end
					</dl>
				</li>
				#end

			</ul>
		</div>
	</div>
	
	<!-- body -->
	<div class="layui-body my-body">
		<div class="layui-tab layui-tab-card my-tab" lay-filter="card" lay-allowClose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" lay-id="1"><span><i class="layui-icon">&#xe68e;</i>首页</span></li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<iframe id="iframe" src="#(ctxPath)/welcome" frameborder="0"></iframe>
				</div>
			</div>
		</div>
	</div>
	
	<!-- footer -->
	<div class="layui-footer my-footer" style="height: 30px;">
		<p style="line-height: 30px;">
			<!--<a href="#(APP.orgWebsite)" target="_blank">#(APP.org)</a>
			&nbsp;&nbsp;&&nbsp;&nbsp;
			<a href="javascript:;" target="_blank">#(APP.name)</a>-->
			&nbsp;&nbsp;&nbsp;Copyright #(thisYear??) #(APP.copyRight)
		</p>
		<!--		<p></p>-->
	</div>
</div>

<!-- 右键菜单 -->
<div class="my-dblclick-box none">
	<table class="layui-tab dblclick-tab">
		<tr class="card-refresh">
			<td><i class="layui-icon">&#x1002;</i>刷新当前标签</td>
		</tr>
		<tr class="card-close">
			<td><i class="layui-icon">&#x1006;</i>关闭当前标签</td>
		</tr>
		<tr class="card-close-all">
			<td><i class="layui-icon">&#x1006;</i>关闭所有标签</td>
		</tr>
	</table>
</div>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/vip_comm.js"></script>
<script type="text/javascript">
layui.use(['layer','vip_nav'], function () {
	// 操作对象
	var layer = layui.layer
		,vipNav = layui.vip_nav
	    ,$ = layui.jquery
	    ;
//	 	顶部左侧菜单生成 [请求地址,过滤ID,是否展开,携带参数]
//	     vipNav.top_left('#(ctxPath)/system/res/menuTop','side-top-left',false);
//	     you code ...

		$('#infoBtn').on('click', function() {
			pop_show('个人资料', '#(ctxPath)/persOrgEmployee/info', 650, 600);
		});
		$('#modPwdBtn').on('click', function() {
			pop_show('修改密码', '#(ctxPath)/user/modifyPwd', 450, 300);
		});

	//定时器推送未读的消息
	getUnReadMessageCount = function(){
		util.sendAjax({
			url:'#(ctxPath)/message/getNotReadMsgCount',
			type:'post',
			data:{},
			notice:false,
			success : function (result) {
				if(result.state == 'ok' && result.notReadMsgCount > 0){
					$("#unReadMessageCount").html(result.notReadMsgCount);
					$("#unReadMessageCount").addClass("layui-badge");
				}else{
					if($("#unReadMessageCount").hasClass("layui-badge")){
						$("#unReadMessageCount").removeClass("layui-badge");
					}
				}
			}
		});
		/*$.post('#(ctxPath)/message/getNotReadMsgCount',{},function (result) {
			if(result.state == 'ok' && result.notReadMsgCount > 0){
				$("#unReadMessageCount").html(result.notReadMsgCount);
				$("#unReadMessageCount").addClass("layui-badge");
			}else{
				if($("#unReadMessageCount").hasClass("layui-badge")){
					$("#unReadMessageCount").removeClass("layui-badge");
				}
			}
		});*/


	}

	$(function(){
		getUnReadMessageCount();
	});

	setInterval("getUnReadMessageCount();",15000);
});
</script>
#end