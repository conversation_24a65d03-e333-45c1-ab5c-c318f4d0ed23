#---------------------------------------------------------------------------------#
# app info
jboot.admin.app.name=HRM\u4EBA\u529B\u8D44\u6E90\u7BA1\u7406
jboot.admin.app.org=cszn
jboot.admin.app.orgWebsite=www.cszn.com
jboot.admin.app.resourceHost
jboot.admin.app.copyRight=\u5E7F\u5DDE\u5E02\u660C\u677E\u667A\u80FD\u79D1\u6280\u5F00\u53D1\u6709\u9650\u516C\u53F8 All rights reserved
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#jboot\u7684\u5F00\u53D1\u6A21\u5F0F
jboot.mode=dev
jboot.bannerEnable=true
jboot.bannerFile=banner.txt
jboot.cron4jEnable=false
jboot.cron4jFile=cron4j.properties
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
#type = local (support:local,motan,dubbo)
#use local
jboot.rpc.type = local
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# mysql config
jboot.datasource.type=mysql
jboot.datasource.url=****************************************************************************
jboot.datasource.user=root
jboot.datasource.password=root
jboot.datasource.maximumPoolSize = 5
jboot.datasource.sqlTemplatePath=
jboot.datasource.sqlTemplate=
jboot.datasource.table=
jboot.datasource.excludeTable=
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
jboot.model.idCacheEnable=false
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# cache config : type default ehcache (support:ehcache,redis,ehredis)
#jboot.cache.type=redis
#jboot.cache.redis.host=127.0.0.1
#jboot.cache.redis.password=123456
#jboot.cache.redis.database=0
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# jwt config
jboot.web.jwt.httpHeaderName=jwttoken
jboot.web.jwt.secret=21934e3ccf3c19a6c3299cbc2e33b2d848e6ef9ea4e484b19b07bea82888a7a7
# 60 * 60 * 10
jboot.web.jwt.validityPeriod=36000
#---------------------------------------------------------------------------------#

#---------------------------------------------------------------------------------#
# shiro config
jboot.shiro.ini = shiro.ini
jboot.shiro.loginUrl=/login
jboot.shiro.successUrl
jboot.shiro.unauthorizedUrl=/login
#---------------------------------------------------------------------------------#

#--------------------------------------\u4E0A\u4F20\u4E0B\u8F7D\u53C2\u6570begin---------------------------#
uploadPath=E:\\virtualDir\\filePath.war\\
uploadPath.linux=/mnt/virtualDir/filePath.war/
excelTemplatePath=E:\\virtualDir\\filePath.war\\excelTemplate\\cardExcel.xls
fileUrlPrefix=http://192.168.4.8:8887/
#--------------------------------------\u4E0A\u4F20\u4E0B\u8F7D\u53C2\u6570end-----------------------------#
mpAppId=wxc6a8c7c3575ce6e3
mpAppSecret=3f260430000665a2eea4c6e7c130d683
sojournUrl=https://bmp.csitd.com/
#---------------\u4F01\u4E1A\u5FAE\u4FE1---------------
corpid=wwec07ceed335b98c4
corpsecret=Vb83Q8kpYX054RzaQjSq2L0ST8np7Y7OOY1udTXekOM
checkinCorpsecret=8KdPd4rMTwUZTqVQJOvo4uAgDTOuHe49GJdnDIk2wvg
agentIdSecretJson={"1000014":"vHl8S8-22qq0ijkfZ9vY67BwNqNtkE2KyP5bOWEzNCs"}
#\u4F01\u4E1A\u5FAE\u4FE1\u540C\u6B65\u5F00\u51730\u5F00\u542F1\u5173\u95ED
qiyeOn=1

#\u9ED8\u8BA4\u5BC6\u7801
defaultPassword=password

#\u65C5\u5C45\u7CFB\u7EDF\u5E94\u7528\u53F7
sojournAppNo=9100001
#\u673A\u6784\u517B\u8001\u7CFB\u7EDF\u5E94\u7528\u53F7
orgAppNo=9100002
#WMS\u7CFB\u7EDF\u5E94\u7528\u53F7
wmsAppNo=9100003

#\u77ED\u4FE1\u53D1\u9001\u5730\u5740
sendMessageUrl=https://bmp.csitd.com/Member/SendByTemp
#\u5458\u5DE5\u751F\u65E5\u77ED\u4FE1\u53D1\u9001\u5185\u5BB9
employeeBirthdaySmsContent=\u660C\u677E\u662F\u4E2A\u6709\u7231\u6709\u6E29\u5EA6\u7684\u5927\u5BB6\u5EAD\uFF0C\u5728\u8FD9\u91CC\u4F60\u662F\u552F\u4E00\u4E0D\u53EF\u66FF\u4EE3\u7684\uFF0C\u660C\u677E\u56E0\u4F60\u66F4\u7CBE\u5F69\uFF0C\u5728\u8FD9\u7279\u522B\u7684\u65E5\u5B50\uFF0C\u6211\u4EEC\u4E00\u8D77\u795D\u60A8\uFF1A\u751F\u65E5\u5FEB\u4E50\uFF01\u5DE5\u4F5C\u5F00\u5FC3\uFF01

#\u6587\u4EF6\u4E0A\u4F20\u5730\u5740
fileUploadUrl=http://10.10.4.203:8885/api/upload
orgUrl=http://org.csitd.com

#taskCreateUrl=https://bmp.cncsgroup.com/Task/Create
#taskDaoUrl=https://bmp.cncsgroup.com/Task/Do
#getMyTasksUrl=https://bmp.cncsgroup.com/Task/GetMyTasks
#getTaskDetail=https://bmp.cncsgroup.com/Task/GetTaskDetail

#\u9ED8\u8BA4\u4F01\u4E1A\u5FAE\u4FE1\u90AE\u7BB1\u540E\u7F00
defaultQiYeEmailSuffix=@cncsgroup.com

taskCreateUrl=http://10.10.4.200:8088/Task/Create
taskDaoUrl=http://10.10.4.200:8088/Task/Do
getMyTasksUrl=http://10.10.4.200:8088/Task/GetMyTasks
getTaskDetail=http://10.10.4.200:8088/Task/GetTaskDetail

entryInfoStep1=currentDeptOpinion
entryInfoStep2=groupHrOpinion
entryInfoStep3=

changeDeptStep1=oldDeptOpinion
changeDeptStep2=newDeptOpinion
changeDeptStep3=groupHrOpinion
changeDeptStep4=
changeDeptStep5=

qualifiedStep1=currentDeptOpinion
qualifiedStep2=groupHrOpinion
qualifiedStep3=
qualifiedStep4=
qualifiedStep5=

quitStep1=currentDeptOpinion
quitStep2=groupHrOpinion
quitStep3=
quitStep4=

fillCardStep1=currentDeptOpinion
fillCardStep2=groupHrOpinion