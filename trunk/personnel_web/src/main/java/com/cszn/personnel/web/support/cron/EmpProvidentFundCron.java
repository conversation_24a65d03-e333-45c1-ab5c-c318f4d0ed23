package com.cszn.personnel.web.support.cron;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.entity.pers.*;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.components.schedule.annotation.Cron;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Cron("0 3 1 * *")
public class EmpProvidentFundCron implements Runnable {

    @Inject
    private PersOrgEmployeeLiabilityInsuranceService persOrgEmployeeLiabilityInsuranceService;
    @Inject
    private PersOrgEmployeeProvidentFundService persOrgEmployeeProvidentFundService;
    @Inject
    private PersOrgEmployeeSocialSecurityService persOrgEmployeeSocialSecurityService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private PersPositionService persPositionService;
    @Inject
    private PersOrgService persOrgService;


    @Override
    public void run(){
        //雇主责任险
        try {
            genLiabilityInsurance();
        }catch (Exception e){
            e.printStackTrace();
        }
        //社保
        try {
            genSocialSecurity();
        }catch (Exception e){
            e.printStackTrace();
        }
        //公积金
        try {
            genProvidentFund();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void genLiabilityInsurance(){
        List<PersOrgEmployeeLiabilityInsurance> liabilityInsuranceList =persOrgEmployeeLiabilityInsuranceService.findEmployeeLiabilityInsuranceList(DateUtils.getLastMonth(),null);

        List<PersOrgEmployeeLiabilityInsurance> addLiabilityInsuranceList=new ArrayList<>();

        String yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");

        for (PersOrgEmployeeLiabilityInsurance liabilityInsurance : liabilityInsuranceList) {
            PersOrgEmployee employee=persOrgEmployeeService.findById(liabilityInsurance.getEmpId());

            PersOrgEmployeeLiabilityInsurance newLiabilityInsurance=new PersOrgEmployeeLiabilityInsurance();
            newLiabilityInsurance.setId(IdGen.getUUID());
            newLiabilityInsurance.setEmpId(liabilityInsurance.getEmpId());
            newLiabilityInsurance.setCreateDate(new Date());
            newLiabilityInsurance.setUpdateDate(new Date());
            newLiabilityInsurance.setYearMonth(yearMonth);
            newLiabilityInsurance.setDelFlag("0");

            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                PersOrg org =persOrgService.findById(id);
                if(org!=null && "0".equals(org.getDelFlag()) && "1".equals(org.getIsEnable())){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }
            newLiabilityInsurance.setDeptId(empDeptId);
            newLiabilityInsurance.setPositionId(empPositionId);
            newLiabilityInsurance.setIdcard(employee.getIdCard());
            newLiabilityInsurance.setCompanyId(liabilityInsurance.getCompanyId());
            if(StrKit.notBlank(employee.getBirthday())){
                newLiabilityInsurance.setAge(DateUtils.getAge(DateUtils.parseDate(employee.getBirthday())));
            }


            if("1".equals(liabilityInsurance.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("3");
                }

            }else if("2".equals(liabilityInsurance.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("3");
                }
            }else if("3".equals(liabilityInsurance.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("3");
                }
            }else if("4".equals(liabilityInsurance.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newLiabilityInsurance.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){

                }
            }
            if(StrKit.notBlank(newLiabilityInsurance.getType())){
                List<String> empIds=new ArrayList<>();
                empIds.add(newLiabilityInsurance.getEmpId());
                List<PersOrgEmployeeLiabilityInsurance> liabilityInsurances=persOrgEmployeeLiabilityInsuranceService.findEmployeeLiabilityInsuranceList(yearMonth,empIds);
                if(liabilityInsurances.size()<=0){
                    addLiabilityInsuranceList.add(newLiabilityInsurance);
                }
            }
        }

        if(addLiabilityInsuranceList.size()>0){
            Db.batchSave(addLiabilityInsuranceList,addLiabilityInsuranceList.size());
        }
    }

    public void genSocialSecurity(){
        List<PersOrgEmployeeSocialSecurity> socialSecurityList =persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityList(DateUtils.getLastMonth(),null);

        List<PersOrgEmployeeSocialSecurity> addsocialSecurityList=new ArrayList<>();

        String yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");

        for (PersOrgEmployeeSocialSecurity socialSecurity : socialSecurityList) {
            if("2".equals(socialSecurity.getStatus())){
                continue;
            }
            PersOrgEmployee employee=persOrgEmployeeService.findById(socialSecurity.getEmpId());

            PersOrgEmployeeSocialSecurity newSocialSecurity=new PersOrgEmployeeSocialSecurity();
            newSocialSecurity.setId(IdGen.getUUID());
            newSocialSecurity.setEmpId(socialSecurity.getEmpId());
            newSocialSecurity.setCreateDate(new Date());
            newSocialSecurity.setUpdateDate(new Date());
            newSocialSecurity.setYearMonth(yearMonth);
            newSocialSecurity.setDelFlag("0");

            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                PersOrg org =persOrgService.findById(id);
                if(org!=null && "0".equals(org.getDelFlag()) && "1".equals(org.getIsEnable())){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }
            newSocialSecurity.setDeptId(empDeptId);
            newSocialSecurity.setPositionId(empPositionId);
            newSocialSecurity.setIdcard(employee.getIdCard());
            newSocialSecurity.setCompanyId(socialSecurity.getCompanyId());
            newSocialSecurity.setPhoneNum(employee.getPhoneNum());
            newSocialSecurity.setIdcardAddr(employee.getIdCardAddr());
            newSocialSecurity.setIsFirst("0");


            if("1".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("3");
                }

            }else if("2".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("3");
                }
            }else if("3".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("3");
                }
            }else if("4".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){

                }
            }
            if(StrKit.notBlank(newSocialSecurity.getType())){
                List<String> empIds=new ArrayList<>();
                empIds.add(newSocialSecurity.getEmpId());
                List<PersOrgEmployeeSocialSecurity> socialSecurities=persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityList(yearMonth,empIds);
                if(socialSecurities.size()<=0){
                    addsocialSecurityList.add(newSocialSecurity);
                }
            }
        }

        if(addsocialSecurityList.size()>0){
            Db.batchSave(addsocialSecurityList,addsocialSecurityList.size());
        }
    }

    public void genProvidentFund(){
        List<PersOrgEmployeeProvidentFund> providentFundList =persOrgEmployeeProvidentFundService.findEmployeeProvidentFundList(DateUtils.getLastMonth(),null);

        List<PersOrgEmployeeProvidentFund> addProvidentFundList=new ArrayList<>();

        String yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");

        for (PersOrgEmployeeProvidentFund providentFund : providentFundList) {
            if("2".equals(providentFund.getStatus())){
                continue;
            }
            PersOrgEmployee employee=persOrgEmployeeService.findById(providentFund.getEmpId());

            PersOrgEmployeeProvidentFund newProvidentFund=new PersOrgEmployeeProvidentFund();
            newProvidentFund.setId(IdGen.getUUID());
            newProvidentFund.setEmpId(providentFund.getEmpId());
            newProvidentFund.setCreateDate(new Date());
            newProvidentFund.setUpdateDate(new Date());
            newProvidentFund.setYearMonth(yearMonth);
            newProvidentFund.setDelFlag("0");

            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                PersOrg org =persOrgService.findById(id);
                if(org!=null && "0".equals(org.getDelFlag()) && "1".equals(org.getIsEnable())){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }
            newProvidentFund.setDeptId(empDeptId);
            newProvidentFund.setPositionId(empPositionId);
            newProvidentFund.setIdcard(employee.getIdCard());
            newProvidentFund.setCompanyId(providentFund.getCompanyId());
            newProvidentFund.setMaritalStatus(employee.getMaritalStatus());
            newProvidentFund.setPhoneNum(employee.getPhoneNum());
            newProvidentFund.setPayRatio(providentFund.getPayRatio());


            if("1".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("3");
                }

            }else if("2".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("3");
                }
            }else if("3".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("3");
                }
            }else if("4".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){

                }
            }
            if(StrKit.notBlank(newProvidentFund.getType())){
                List<String> empIds=new ArrayList<>();
                empIds.add(newProvidentFund.getEmpId());
                List<PersOrgEmployeeProvidentFund> providentFunds=persOrgEmployeeProvidentFundService.findEmployeeProvidentFundList(yearMonth,empIds);
                if(providentFunds.size()<=0){
                    addProvidentFundList.add(newProvidentFund);
                }
            }
        }

        if(addProvidentFundList.size()>0){
            Db.batchSave(addProvidentFundList,addProvidentFundList.size());
        }
    }
}
