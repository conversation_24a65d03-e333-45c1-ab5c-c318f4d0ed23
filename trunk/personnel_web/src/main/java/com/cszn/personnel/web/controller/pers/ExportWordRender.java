package com.cszn.personnel.web.controller.pers;

import com.jfinal.kit.Kv;
import com.jfinal.kit.PathKit;
import com.jfinal.render.Render;
import com.jfinal.template.Engine;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ExportWordRender extends Render {

    private static Engine engine;
    private String exportWordName;
    private Kv data;


    public ExportWordRender(String template, Kv data, String exportWordName) {
        this.view = template;
        this.data = data;
        this.exportWordName = exportWordName;
    }


    protected Engine getEngine(){
        if(engine != null){
            return engine;
        }
        engine = Engine.create(ExportWordRender.class.getName());
        engine.setDevMode(true);

        engine.setBaseTemplatePath(PathKit.getWebRootPath());
        return engine;
    }
    /***
     ⽂件名称⾃动加上时间戳
     *
     * */
    public ExportWordRender setDate(){
        SimpleDateFormat dateFormat =new SimpleDateFormat("yyyy-MM-dd_HH-mm_");
        String dateStr = dateFormat.format(new Date());
        exportWordName = dateStr.concat(exportWordName);
        return this;
    }
    /***
     设置服务器端编码以及兼容性
     *
     * */
    @Override
    public void render(){
        response.reset();
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Content-disposition","attachment; "+encodeFileName(request, exportWordName));
        response.setContentType("application/msword;charset=utf-8");
        outputRender();
    }
    /**
     输出
     *
     * **/
    protected void outputRender(){


        try{
            getEngine().getTemplate(view).render(data, response.getOutputStream());
        }catch(IOException e){
            e.printStackTrace();
        }
    }
    /**
     依据浏览器判断编码规则
     *
     */
    protected String encodeFileName(HttpServletRequest request, String fileName) {
        String userAgent = request.getHeader("User-Agent");
        try {
            String encodedFileName = URLEncoder.encode(fileName, "UTF8");

            if (userAgent == null) {
                return "filename=\"" + encodedFileName + "\"";
            }
            userAgent = userAgent.toLowerCase();

            if (userAgent.indexOf("msie") != -1) {
                return "filename=\"" + encodedFileName + "\"";
            }

            if (userAgent.indexOf("opera") != -1) {
                return "filename*=UTF-8''" + encodedFileName;
            }

            if (userAgent.indexOf("safari") != -1 || userAgent.indexOf("applewebkit") != -1 || userAgent.indexOf("chrome") != -1) {
                //return "filename=\"" + new String(fileName.getBytes("UTF-8"), "ISO8859-1") + "\"";
                return "filename*=UTF-8''" + encodedFileName;
            }

            if (userAgent.indexOf("mozilla") != -1) {
                return "filename*=UTF-8''" + encodedFileName;
            }
            return "filename=\"" + encodedFileName + "\"";
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException (e);
        }
    }
}
