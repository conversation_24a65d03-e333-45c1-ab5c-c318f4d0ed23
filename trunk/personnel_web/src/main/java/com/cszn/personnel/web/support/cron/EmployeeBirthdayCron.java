package com.cszn.personnel.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeRelService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.enums.RemindType;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.entity.sms.SmsSendRecord;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Cron("00 10 * * *")
public class EmployeeBirthdayCron implements Runnable {

    @Inject
    PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    UserService userService;
    @Inject
    PersOrgEmployeeRelService persOrgEmployeeRelService;
    @Inject
    PersPositionService persPositionService;
    @Inject
    PersOrgService persOrgService;
    private Logger logger= LoggerFactory.getLogger(EmployeeBirthdayCron.class);

    @Override
    public void run() {
        try {
            //更新调岗员工信息
            updateChangeDept();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //员工转正前7天添加到提醒中
            addRemind();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //员工生日祝福
            employeeBirthday();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //合同即将到期提醒
            employeeContractMsg();
        }catch (Exception e){
            e.printStackTrace();
        }




    }


    public void employee(int pageNumber,int pageSize){
        Page<Record> recordPage=Db.paginate(pageNumber,pageSize,"select phone_num  ",
                " from pers_org_employee where del_flag='0' and archive_status='incumbency'  GROUP BY phone_num ");
        if(recordPage.getList()==null || recordPage.getList().size()==0 ){
            return;
        }
        List<Record> recordList=recordPage.getList();
        String regex = "^1[3-9][0-9]\\d{8}$";

        Map<String,String> params=new HashMap<>();
        params.put("tempId", SendType.customContent.getTemplateId());
        List<SmsSendRecord> sendRecordList=new ArrayList<>();
        for(Record record:recordList){
            String phoneNum=record.getStr("phone_num");
            if(StrKit.isBlank(phoneNum) || !phoneNum.matches(regex)){
                continue;
            }
            //发送短信
            params.put("mobile",phoneNum);
            params.put("data", "{\"content\":\""+"月圆人团圆，共享中秋月，五湖四海来相聚，百年昌松与卿行，值此中秋佳节，昌松集团祝福全体员工及家人中秋快乐，阖家幸福！"+"\"}");

            String resultStr= HttpClientsUtils.httpPostForm(Global.sendMessageUrl, params,null,"UTF-8");

            if(resultStr.startsWith("{") && resultStr.endsWith("}")){
                JSONObject object= JSON.parseObject(resultStr);
                if(object.containsKey("Type") && "1".equals(object.getString("Type"))){

                }else{
                    //保存一条短信发送失败记录
                    SmsSendRecord sendRecord=new SmsSendRecord();
                    sendRecord.setId(IdGen.getUUID());
                    sendRecord.setSendType(SendType.customContent.getKey());
                    sendRecord.setPhoneNum(phoneNum);
                    sendRecord.setSmsParam(JSON.toJSONString(params));
                    sendRecord.setSmsTemplateId(SendType.customContent.getTemplateId());
                    sendRecord.setSmsSendStatus("send_fail");
                    sendRecord.setDelFlag("0");
                    sendRecord.setCreateDate(new Date());
                    sendRecord.setUpdateDate(new Date());
                    sendRecordList.add(sendRecord);
                }
            }
        }
        if(sendRecordList.size()>0){
            Db.batchSave(sendRecordList,sendRecordList.size());
        }
        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            employee(pageNumber,pageSize);
        }
    }

    public void updateChangeDept(){
        String sql="select a.* from pers_org_employee_change_apply a " +
                "LEFT JOIN pers_task b on b.record_id=a.id " +
                "where a.del_flag='0' and TO_DAYS(a.start_time)=TO_DAYS(now()) and a.status='3' ";
        List<Record> recordList=Db.find(sql);
        if(recordList!=null && recordList.size()>0){
            for(Record record:recordList){
                PersOrgEmployee persOrgEmployee=persOrgEmployeeService.findById(record.getStr("emp_id"));
                if(persOrgEmployee!=null){
                    String oldDeptId=record.getStr("dept_id");
                    String oldPositionId=record.getStr("position_id");

                    String newDeptId=record.getStr("new_dept_id");
                    String newPositionId=record.getStr("new_position_id");

                    List<String> delRelParams=new ArrayList<>();
                    PersOrgEmployeeRel oldDeptRel=persOrgEmployeeRelService.getRel(persOrgEmployee.getId(),"dept",oldDeptId);
                    PersOrgEmployeeRel oldPositionRel=persOrgEmployeeRelService.getRel(persOrgEmployee.getId(),"position",oldPositionId);

                    if(oldDeptRel!=null){
                        delRelParams.add(oldDeptRel.getId());
                    }
                    if(oldPositionRel!=null){
                        delRelParams.add(oldPositionRel.getId());
                        PersPosition oldPosition=persPositionService.findById(oldPositionRel.getRelationshipId());
                        if(oldPosition!=null){
                            PersOrgEmployeeRel oldRoleRel=persOrgEmployeeRelService.getRel(persOrgEmployee.getId(),"role",oldPosition.getRoleId());
                            if(oldRoleRel!=null){
                                delRelParams.add(oldRoleRel.getId());
                            }
                        }
                    }
                    persOrgEmployeeRelService.deleteRelByIds(delRelParams,record.getStr("create_by"));



                    PersOrgEmployeeRel deptRel=new PersOrgEmployeeRel();
                    deptRel.setId(IdGen.getUUID());
                    deptRel.setEmpId(persOrgEmployee.getId());
                    deptRel.setRelationshipType("dept");
                    deptRel.setRelationshipId(newDeptId);

                    PersOrgEmployeeRel positionRel=new PersOrgEmployeeRel();
                    positionRel.setId(IdGen.getUUID());
                    positionRel.setEmpId(persOrgEmployee.getId());
                    positionRel.setRelationshipType("position");
                    positionRel.setRelationshipId(newPositionId);

                    PersPosition newPosition=persPositionService.findById(newPositionId);

                    List<PersOrgEmployeeRel> addRelList=new ArrayList<>();
                    addRelList.add(deptRel);
                    addRelList.add(positionRel);
                    if(newPosition!=null && StrKit.notBlank(newPosition.getRoleId())){
                        PersOrgEmployeeRel roleRel=new PersOrgEmployeeRel();
                        roleRel.setId(IdGen.getUUID());
                        roleRel.setEmpId(persOrgEmployee.getId());
                        roleRel.setRelationshipType("role");
                        roleRel.setRelationshipId(newPosition.getRoleId());
                        addRelList.add(roleRel);
                    }
                    User user=userService.getUserByEmpId(persOrgEmployee.getId());
                    persOrgEmployeeRelService.addRel(addRelList,user.getId(),record.getStr("create_by"));
                    persOrgEmployeeService.updateQiyeUserId(persOrgEmployee.getId());


                    /*persOrgEmployee.setOrgId(record.getStr("org_id"));
                    persOrgEmployee.setDeptId(record.getStr("dept_id"));
                    persOrgEmployee.setUpdateDate(new Date());
                    persOrgEmployee.setUpdateBy(record.getStr("create_by"));

                    userService.updateSaveUser(persOrgEmployee,record.getStr("create_by"));*/
                }
            }
        }
    }

    public void addRemind(){
        String sql="select * from pers_org_employee where  archive_status='incumbency' and del_flag='0' " +
                "and TO_DAYS(formal_date)-TO_DAYS(now())=30 ";
        List<Record> recordList=Db.find(sql);
        if(recordList==null || recordList.size()==0){
            return;
        }
        List<PersOrgEmployeeRemind> employeeRemindList=new ArrayList<>();
        Map<String,String> map=new HashMap<>();
        for(Record record:recordList){
            PersOrgEmployeeRemind remind=new PersOrgEmployeeRemind();
            remind.setId(IdGen.getUUID());
            remind.setRemindType(RemindType.qualified_remind.getKey());
            remind.setEmpId(record.getStr("id"));
            remind.setRemindDate(record.getDate(""));
            remind.setRemindStatus("1");
            remind.setDelFlag("0");
            remind.setCreateDate(new Date());
            remind.setUpdateDate(new Date());
            employeeRemindList.add(remind);
            PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("id"));
            String depeId=employee.getFirstDepeId();
            PersOrg org=persOrgService.findById(depeId);
            String orgName=persOrgService.getOrgParentNames(depeId);
            if(StrKit.isBlank(org.getHrUserId())){
                continue;
            }
            orgName=orgName.replace("昌松集团/","");
            if(map.containsKey(org.getHrUserId())){
                String content=map.get(org.getHrUserId());
                content+="\n"+orgName+"：("+employee.getWorkNum()+")"+employee.getFullName();
                map.put(org.getHrUserId(),content);
            }else{
                String content="\n"+orgName+"：("+employee.getWorkNum()+")"+employee.getFullName();
                map.put(org.getHrUserId(),content);
            }

        }
        for(String key:map.keySet()){
            Map<String,String> params=new HashMap<>();
            params.put("toUser","[\""+key+"\"]");
            params.put("agentId","1000017");
            params.put("msgType","text");
            params.put("content","以下员工30天后转正："+map.get(key));
            params.put("safe","1");
            String res=HttpClientsUtils.httpPostForm(Global.qyMessageSendUrl,params,null,null);
            logger.info("转正提醒："+JSON.toJSONString(params)+"，结果："+res);
        }

        Db.batchSave(employeeRemindList,employeeRemindList.size());

    }

    public void employeeBirthday(){
        List<String> phoneNumList= Db.query("select phone_num from pers_org_employee where del_flag='0' and archive_status='incumbency' and right(birthday, 5)=DATE_FORMAT(now(),'%m-%d') GROUP BY phone_num ");
        if(phoneNumList!=null && phoneNumList.size()>0){
            String regex = "^1[3-9][0-9]\\d{8}$";

            Map<String,String> params=new HashMap<>();
            params.put("tempId", SendType.customContent.getTemplateId());
            List<SmsSendRecord> recordList=new ArrayList<>();
            for(String phoneNum:phoneNumList){
                if(StrKit.isBlank(phoneNum) || !phoneNum.matches(regex)){
                    continue;
                }
                //发送短信
                params.put("mobile",phoneNum);
                params.put("data", "{\"content\":\""+Global.employeeBirthdaySmsContent+"\"}");

                String resultStr= HttpClientsUtils.httpPostForm(Global.sendMessageUrl, params,null,"UTF-8");

                if(resultStr.startsWith("{") && resultStr.endsWith("}")){
                    JSONObject object= JSON.parseObject(resultStr);
                    if(object.containsKey("Type") && "1".equals(object.getString("Type"))){

                    }else{
                        //保存一条短信发送失败记录
                        SmsSendRecord record=new SmsSendRecord();
                        record.setId(IdGen.getUUID());
                        record.setSendType(SendType.customContent.getKey());
                        record.setPhoneNum(phoneNum);
                        record.setSmsParam(JSON.toJSONString(params));
                        record.setSmsTemplateId(SendType.customContent.getTemplateId());
                        record.setSmsSendStatus("send_fail");
                        record.setDelFlag("0");
                        record.setCreateDate(new Date());
                        record.setUpdateDate(new Date());
                        recordList.add(record);
                    }
                }
            }

            if(recordList.size()>0){
                Db.batchSave(recordList,recordList.size());
            }
        }
    }

    public void employeeContractMsg(){
        String sql=" select a.emp_id as empId,b.full_name as fullName,b.qiye_userid as qiyeUserid,max(contract_end_date) as endDate,c.user_id as userId " +
                "from pers_org_employee_contract a " +
                " left join pers_org_employee b on a.emp_id=b.id left join pers_emp_user c on b.id=c.emp_id " +
                "where a.del_flag='0' and b.del_flag='0' and b.archive_status='incumbency' and a.date_type='1' " +
                " and TO_DAYS(a.contract_end_date)=TO_DAYS(DATE_ADD(CURDATE(), INTERVAL 30 DAY))  GROUP BY  a.emp_id ";
        List<Record> recordList=Db.find(sql);
        if(recordList.size()==0){
            return;
        }

        Map<String,List<String>> hrUserEmpMap=new HashMap<>();

        Set<String> msgUserIdSet=new HashSet<>();

        if(Global.qyMessageSendUrl.indexOf("csitd.com")<0){
            return;
        }

        for (Record record : recordList) {
            String empId=record.getStr("empId");
            String fullName=record.getStr("fullName");
            String qiyeUserid=record.getStr("qiyeUserid");
            String userId=record.getStr("userId");

            msgUserIdSet.add(userId);

            List<String> deptIds=Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",empId);
            for (String deptId : deptIds) {
                PersOrg org=persOrgService.findById(deptId);
                if(StrKit.notBlank(org.getHrUserId())){
                    String deptName=persOrgService.getOrgParentNames(org.getId());
                    if(hrUserEmpMap.containsKey(org.getHrUserId())){
                        hrUserEmpMap.get(org.getHrUserId()).add(deptName+"："+fullName);
                    }else{
                        List<String> stringList=new ArrayList<>();
                        stringList.add(deptName+"："+fullName);
                        hrUserEmpMap.put(org.getHrUserId(),stringList);
                    }
                }
            }

            //发送给HR
            for (String hrUserId : hrUserEmpMap.keySet()) {

                List<String> stringList=hrUserEmpMap.get(hrUserId);

                Map<String,String> params=new HashMap<>();
                params.put("toUser","[\""+hrUserId+"\"]");
                params.put("agentId","1000017");
                params.put("msgType","text");

                String msgContent="员工合同到期提醒(点击看详情)\n\n";
                for (String s : stringList) {
                    msgContent+=s+"\n";
                }
                params.put("content",msgContent);
                params.put("safe","1");
                HttpClientsUtils.httpPostForm(Global.qyMessageSendUrl,params,null,null);
            }


            //发送给员工
            Map<String,String> params=new HashMap<>();
            params.put("toUser",JSON.toJSONString(msgUserIdSet));
            params.put("agentId","1000017");
            params.put("msgType","text");
            params.put("content","您的合同还有30就要到期，请及时与人事联系并处理");
            params.put("safe","1");
            HttpClientsUtils.httpPostForm(Global.qyMessageSendUrl,params,null,null);

        }
    }

    public static void main(String[] args) {
        Map<String,String> params=new HashMap<>();
        params.put("toUser","[\"7B9D2239-5813-4BBE-B7C4-3794BF6C2CDB\"]");
        params.put("agentId","1000017");
        params.put("msgType","text");

        //params.put("content","员工合同到期提醒(点击看详情)\n\n昌松集团/科技公司/研发部：刘万良\n昌松集团/科技公司/研发部：张三");
        params.put("content","您的合同还有30就要到期，请及时与人事联系并处理");
        params.put("safe","1");

        System.out.println();
        //HttpClientsUtils.httpPostForm("http://hrm.csitd.com/api/messageSend",params,null,null);


        Set<String> stringSet=new HashSet<>();
        stringSet.add("xxxxxx");
        stringSet.add("bbb");
        System.out.println(JSON.toJSONString(stringSet));
    }
}
