/**
 * 
 */
package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.*;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBankService;
import com.cszn.integrated.service.api.main.MainRoleService;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.weixin.QiYeWeiXinService;
import com.cszn.integrated.service.entity.main.MainRole;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.weixin.QiYeUser;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.sql.SQLException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Base64.Decoder;
import java.util.Base64.Encoder;

/**
 * Created by LiangHuiLing on 2019年7月2日
 *
 * PersOrgEmployeeController
 */
@RequestMapping(value="/persOrgEmployee", viewPath="/modules_page/pers/orgEmployee")
public class PersOrgEmployeeController extends BaseController {

	@Inject
    private PersOrgService persOrgService;
	@Inject
	private PersOrgEmployeeService persOrgEmployeeService;
	@Inject
	private PersOrgEmployeeRelService persOrgEmployeeRelService;
	@Inject
	private UserService userService;
	@Inject
	private PersPositionService persPositionService;
	@Inject
	private QiYeWeiXinService qiYeWeiXinService;
	@Inject
	private PersOrgEmployeeContractService persOrgEmployeeContractService;
	@Inject
	private PersOrgEmployeeChangeDeptService persOrgEmployeeChangeDeptService;
	@Inject
	private PersOrgEmployeeRewardPunishService persOrgEmployeeRewardPunishService;
	@Inject
	private PersOrgEmployeeFileService persOrgEmployeeFileService;
	@Inject
	private MainRoleService mainRoleService;
	@Inject
	private PersOrgEmployeeDomainSyncRecordService persOrgEmployeeDomainSyncRecordService;
	@Inject
	private PersOrgEmployeeEntryInfoService persOrgEmployeeEntryInfoService;
	@Inject
	private MainBankService mainBankService;
	@Inject
	private PersOrgEmployeeAccountService persOrgEmployeeAccountService;
	@Inject
	private DictService dictService;
	@Inject
	private PersSocialAddressService persSocialAddressService;
	@Inject
	private PersOrgCompanyService persOrgCompanyService;
	
    public void index() {
		String type = getPara("type");
		final String orgId = AuthUtils.getOrgId();
    	if(StrKit.notBlank(orgId)){
    		PersOrg org = persOrgService.findById(orgId);
    		if(org!=null){
    			setAttr("orgType", org.getOrgType());
    			setAttr("orgParentId", org.getParentId());
    			setAttr("orgParentIds", org.getParentIds());
    		}
    	}
    	setAttr("orgId", orgId);
		List<Dict> employeeTypeList = dictService.getListByTypeOnUse("employee_type");

		JSONArray employeeTypeArray=new JSONArray();
		for (Dict dict : employeeTypeList) {
			JSONObject jsonObject=new JSONObject();
			jsonObject.put("name",dict.getDictName());
			jsonObject.put("value",dict.getDictValue());
			employeeTypeArray.add(jsonObject);
		}
		setAttr("employeeTypeArrayStr",JSON.toJSONString(employeeTypeArray));
		setAttr("type",type);
		render("orgEmployeeIndex.html");
    }
    
    public void subEmployeeIndex() {
    	final String orgId = AuthUtils.getOrgId();
    	if(StrKit.notBlank(orgId)){
    		PersOrg org = persOrgService.findById(orgId);
    		if(org!=null){
    			setAttr("orgType", org.getOrgType());
    			setAttr("orgParentId", org.getParentId());
    			setAttr("orgParentIds", org.getParentIds());
    		}
    	}
    	setAttr("orgId", orgId);
    	render("subEmployeeIndex.html");
    }

	@Clear(LogInterceptor.class)
    public void employeeOrgTree() {
    	String orgId = AuthUtils.getOrgId();
        orgId=null;
    	List<ZTree> orgFormTree = persOrgService.orgFormTree(orgId);
        renderJson(orgFormTree);
    }
	
//	@Clear(LogInterceptor.class)
//	public Ret parentOrgEmployeeXmSelectTree() {
//		renderJson(Ret.ok("msg", "加载成功!").set("xmSelectTree", persOrgEmployeeService.parentOrgEmployeeXmSelectTree(""));
//	}
    
    /**
     * 机构员工分页表格数据
     */
	@Clear(LogInterceptor.class)
    public void pageTable() {
    	PersOrgEmployee orgEmployee = getBean(PersOrgEmployee.class, "", true);
//    	String status=getPara("status");
    	String sort=getPara("sort");
    	String isContainChildren=getPara("isContainChildren");
		String userId=null;
		if(!"system_admin".equals(AuthUtils.getLoginUser().getUserType())){
			userId=AuthUtils.getUserId();
		}

        Page<PersOrgEmployee> orgEmployeePage = persOrgEmployeeService
				.paginateByCondition(orgEmployee,userId,sort,isContainChildren,null,null,null,null,null,null,null, getParaToInt("page", 1), getParaToInt("limit", 10));
		String res = HttpClientsUtils.get(Global.agencyListUrl);
		Map<String,String> agencyMap=new HashMap<>();
		if(res.startsWith("{") && res.endsWith("}")){
			JSONObject data=JSONObject.parseObject(res);
			if("0".equals(data.getString("code"))){
				for (int i = 0; i < data.getJSONArray("data").size(); i++) {
					JSONObject item=data.getJSONArray("data").getJSONObject(i);
					agencyMap.put(item.getString("id"),item.getString("agencyName"));
				}
			}
		}
		if(orgEmployeePage.getList()!=null && orgEmployeePage.getList().size()>0){
			String str="";
			List<String> params=new ArrayList<>();
			for (PersOrgEmployee employee : orgEmployeePage.getList()) {
				str+="?,";
				params.add(employee.getId());
			}
			str=str.substring(0,str.length()-1);
			List<Record> recordList = Db.find("select employee_id,agent_id from pers_org_employee_entry_info where employee_id in(" + str + ")", params.toArray());
			Map<String,String> empAgentIdMap=new HashMap<>();
			for (Record record : recordList) {
				empAgentIdMap.put(record.getStr("employee_id"),record.getStr("agent_id"));
			}

			for (PersOrgEmployee employee : orgEmployeePage.getList()) {
				Record record = employee.getOrg();
				if(record==null){
					record=new Record();
				}
				String agentId = empAgentIdMap.get(employee.getId());
				String agencyName = agencyMap.get(agentId);
				employee.setAgencyName(agencyName);
			}

		}
		renderJson(new DataTable<PersOrgEmployee>(orgEmployeePage));
    }
	
	/**
	 * 账户分页
	 */
	@Clear(LogInterceptor.class)
	public void accountPage() {
		PersOrgEmployeeAccount model = getBean(PersOrgEmployeeAccount.class, "", true);
		Page<PersOrgEmployeeAccount> modelPage = persOrgEmployeeAccountService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
		renderJson(new DataTable<PersOrgEmployeeAccount>(modelPage));
	}

	@Clear(LogInterceptor.class)
	public void empCheckinRecordExport(){
		PersOrgEmployee orgEmployee = getBean(PersOrgEmployee.class, "", true);
//    	String status=getPara("status");
		String isContainChildren=getPara("isContainChildren");
		String sort=getPara("sort");
		String selectArray=getPara("selectArray");
		String userId=null;
		if(!"system_admin".equals(AuthUtils.getLoginUser().getUserType())){
			userId=AuthUtils.getUserId();
		}

		Page<PersOrgEmployee> orgEmployeePage = persOrgEmployeeService.paginateByCondition(orgEmployee,userId,sort,isContainChildren,null,null,null,null,null,null,null, 1, 20000);

		if(orgEmployeePage.getList().size()==0){
			renderJson(Ret.fail());
			return;
		}
		List<PersOrgEmployee> recordList=orgEmployeePage.getList();
		String fileName="员工信息.xls";
		String sheetName = "员工信息";
		if(StrKit.notBlank(orgEmployee.getDeptId())){
			String deptName=persOrgService.getOrgParentNames(orgEmployee.getDeptId());
			fileName=deptName+".xls";
			sheetName=deptName.replace("/","_");
		}
		String[] title={"工号","部门","姓名","职位","性别","年龄","联系方式","入职日期","工龄","试用薪资","转正日期","转正薪资","现有薪酬","异动情况"
				,"身份证号码","出生日期","籍贯","民族","身份证地址","现住地址","学历","紧急联系人","紧急联系人手机","合同期限","合同有效期","合同到期日","合同类型","合同归属地","续签记录","意外险","职工社保","社保归属地","银行卡","备注"};


		String[][] content=new String[orgEmployeePage.getList().size()][title.length];
		List<Record> recordList2=new ArrayList<>();
		if(recordList.size()>0){

			List<Dict> dictList=dictService.getListByTypeOnUse("nation");
			Map<String,String> nationMap=new HashMap<>();
			List<Dict> educationList=dictService.getListByTypeOnUse("education");
			Map<String,String> educationMap=new HashMap<>();
			for(Dict dict:dictList){
				nationMap.put(dict.getDictValue(),dict.getDictName());
			}
			for(Dict dict:educationList){
				educationMap.put(dict.getDictValue(),dict.getDictName());
			}

			for(int i=0;i<recordList.size();i++){
				PersOrgEmployee record=recordList.get(i);
				PersOrgEmployeeEntryInfo employeeEntryInfo=persOrgEmployeeEntryInfoService.getEntryInfo(record.getId());

				Record record1=new Record();



				content[i][0]=record.getWorkNum();
				content[i][1]= record.getOrg().getStr("orgName");
				content[i][2]=record.getFullName();
				content[i][3]=record.getOrg().getStr("positionName");

				record1.set("workNum",content[i][0]);
				record1.set("orgName",content[i][1]);
				record1.set("fullName",content[i][2]);
				record1.set("positionName",content[i][3]);
				if("male".equals(record.getSex())){
					content[i][4]="男";
				}else if("female".equals(record.getSex())){
					content[i][4]="女";
				}
				record1.set("sex",content[i][4]);
				if(StrKit.notBlank(record.getBirthday())){
					Date birthday=null;
					try {
						birthday=DateUtils.parseDate(record.getBirthday());
					}catch (Exception e){
					}
					if(birthday!=null){
						try {
							int age=DateUtils.getAge(birthday);
							content[i][5]=age+"";

						}catch (Exception e){
						}
					}
				}
				record1.set("age",content[i][5]);
				content[i][6]=record.getPhoneNum();
				record1.set("phoneNum",content[i][6]);
				if(record.getEntryTime()!=null){
					content[i][7]=DateUtils.formatDate(record.getEntryTime(),"yyyy-MM-dd");
				}
				record1.set("entryTime",content[i][7]);
				try {
					int workAge=DateUtils.getAge(record.getEntryTime());
					content[i][8]=workAge+"";

				}catch (Exception e){
				}
				record1.set("workAge",content[i][8]);
				if(record.getProbationSalary()==null){
					content[i][9]="";
				}else{
					content[i][9]=record.getProbationSalary()+"";
				}
				record1.set("probationSalary",content[i][9]);

				if(record.getFormalDate()!=null){
					content[i][10]=DateUtils.formatDate(record.getFormalDate(),"yyyy-MM-dd");
				}else{
					content[i][10]="";
				}
				record1.set("formalDate",content[i][10]);
				if(record.getSalary()==null){
					content[i][11]="";
				}else{
					content[i][11]=record.getSalary()+"";
				}
				record1.set("salary",content[i][11]);

				if(record.getNowSalary()==null){
					content[i][12]="";
				}else{
					content[i][12]=record.getNowSalary().toString();
				}

				content[i][13]="";
				content[i][14]=record.getIdCard();
				content[i][15]=record.getBirthday();

				record1.set("salary",content[i][11]);
				record1.set("nowSalary",content[i][12]);
				record1.set("changeDept","");
				record1.set("idcard",content[i][14]);
				record1.set("birthday",content[i][15]);

				if(employeeEntryInfo!=null){
					content[i][16]=employeeEntryInfo.getNativePlace();
				}else{
					content[i][16]="";
				}
				record1.set("nativePlace",content[i][16]);

				content[i][17]=nationMap.get(record.getNationality());
				content[i][18]=record.getIdCardAddr();
				content[i][19]=record.getResidentAddr();
				content[i][20]=educationMap.get(record.getEducation());
				content[i][21]=record.getLinkPeople();
				content[i][22]=record.getLinkPhone();

				record1.set("nationality",content[i][17]);
				record1.set("idCardAddr",content[i][18]);
				record1.set("residentAddr",content[i][19]);
				record1.set("education",content[i][20]);
				record1.set("linkPeople",content[i][21]);
				record1.set("linkPhone",content[i][22]);


				//获取最后一条合同
				PersOrgEmployeeContract contract=new PersOrgEmployeeContract();
				contract.setEmpId(record.getId());
				Page<PersOrgEmployeeContract> contractPage=persOrgEmployeeContractService.employeeContractPage(1,1000,contract);
				if(contractPage.getList()!=null && contractPage.getList().size()>0){
					PersOrgEmployeeContract maxContract=contractPage.getList().get(contractPage.getList().size()-1);
					if(maxContract.getContractEndDate()!=null && maxContract.getContractStartDate()!=null){
						List<String> yearList=findYearsStr(DateUtils.formatDate(maxContract.getContractStartDate(),"yyyy-MM-dd"),DateUtils.formatDate(maxContract.getContractEndDate(),"yyyy-MM-dd"));
						if(yearList!=null && yearList.size()>0){
							content[i][23]=(yearList.size()-1)+"";
							record1.set("contractYear",content[i][23]);
						}
						content[i][24]=DateUtils.formatDate(maxContract.getContractStartDate(),"yyyy-MM-dd");
						content[i][25]=DateUtils.formatDate(maxContract.getContractEndDate(),"yyyy-MM-dd");
						record1.set("contractStartDate",content[i][24]);
						record1.set("contractEndDate",content[i][25]);
					}
					String contractType="";
					if("1".equals(maxContract.getContractType())){
						contractType="劳动合同";
					}else if("2".equals(maxContract.getContractType())){
						contractType="劳务合同";
					}else if("3".equals(maxContract.getContractType())){
						contractType="临时合同";
					}else if("4".equals(maxContract.getContractType())){
						contractType="待补";
					}else if("5".equals(maxContract.getContractType())){
						contractType="实习协议";
					}else if("6".equals(maxContract.getContractType())){
						contractType="临时工合同";
					}
					content[i][26]=contractType;
					content[i][27]=contract.getContractAddr();

					record1.set("contractType",content[i][26]);
					record1.set("contractAddr",content[i][27]);

					String contracts="";
					for(PersOrgEmployeeContract employeeContract:contractPage.getList()){
						if(employeeContract.getContractEndDate()!=null && employeeContract.getContractStartDate()!=null) {
							contracts += DateUtils.formatDate(employeeContract.getContractStartDate(), "yyyy-MM-dd")
									+ "-" + DateUtils.formatDate(employeeContract.getContractEndDate(), "yyyy-MM-dd") + "\n";
						}
					}
					content[i][28]=contracts;
					record1.set("contracts",content[i][28]);
				}
				String accidentInsurance="";
				if("1".equals(record.getAccidentInsurance())){
					accidentInsurance="已缴";
				}else if("2".equals(record.getAccidentInsurance())){
					accidentInsurance="未缴";
				}else if("3".equals(record.getAccidentInsurance())){
					accidentInsurance="放弃";
				}
				content[i][29]=accidentInsurance;
				record1.set("accidentInsurance",content[i][29]);
				String socialStatus="";
				if("1".equals(record.getSocialStatus())){
					socialStatus="已缴";
				}else if("2".equals(record.getSocialStatus())){
					socialStatus="未缴";
				}else if("3".equals(record.getSocialStatus())){
					socialStatus="放弃";
				}
				content[i][30]=socialStatus;
				content[i][31]=record.getSocialAddr();
				content[i][32]="";
				content[i][33]=record.getRemark();

				record1.set("socialStatus",content[i][30]);
				record1.set("socialAddr",content[i][31]);
				record1.set("card",content[i][32]);
				record1.set("remark",content[i][33]);





				recordList2.add(record1);


			}
		}
		JSONArray jsonArray2=JSON.parseArray("[{name: '工号', value: 'workNum', selected: true},\n" +
				"\t\t\t\t{name: '部门', value: 'orgName', selected: true},\n" +
				"\t\t\t\t{name: '姓名', value: 'fullName', selected: true},\n" +
				"\t\t\t\t{name: '职位', value: 'positionName', selected: true},\n" +
				"\t\t\t\t{name: '性别', value: 'sex', selected: true},\n" +
				"\t\t\t\t{name: '年龄', value: 'age', selected: true},\n" +
				"\t\t\t\t{name: '联系方式', value: 'phoneNum', selected: true},\n" +
				"\t\t\t\t{name: '入职日期', value: 'entryTime', selected: true},\n" +
				"\t\t\t\t{name: '工龄', value: 'workAge', selected: true},\n" +
				"\t\t\t\t{name: '试用薪资', value: 'probationSalary', selected: true},\n" +
				"\t\t\t\t{name: '转正日期', value: 'formalDate', selected: true},\n" +
				"\t\t\t\t{name: '转正薪资', value: 'salary', selected: true},\n" +
				"\t\t\t\t{name: '现有薪酬', value: 'nowSalary', selected: true},\n" +
				"\t\t\t\t{name: '异动情况', value: 'changeDept', selected: true},\n" +
				"\t\t\t\t{name: '身份证号码', value: 'idcard', selected: true},\n" +
				"\t\t\t\t{name: '出生日期', value: 'birthday', selected: true},\n" +
				"\t\t\t\t{name: '籍贯', value: 'nativePlace', selected: true},\n" +
				"\t\t\t\t{name: '民族', value: 'nationality', selected: true},\n" +
				"\t\t\t\t{name: '身份证地址', value: 'idCardAddr', selected: true},\n" +
				"\t\t\t\t{name: '现住地址', value: 'residentAddr', selected: true},\n" +
				"\t\t\t\t{name: '学历', value: 'education', selected: true},\n" +
				"\t\t\t\t{name: '紧急联系人', value: 'linkPeople', selected: true},\n" +
				"\t\t\t\t{name: '紧急联系人手机', value: 'linkPhone', selected: true},\n" +
				"\t\t\t\t{name: '合同期限', value: 'contractYear', selected: true},\n" +
				"\t\t\t\t{name: '合同有效期', value: 'contractStartDate', selected: true},\n" +
				"\t\t\t\t{name: '合同到期日', value: 'contractEndDate', selected: true},\n" +
				"\t\t\t\t{name: '合同类型', value: 'contractType', selected: true},\n" +
				"\t\t\t\t{name: '合同归属地', value: 'contractAddr', selected: true},\n" +
				"\t\t\t\t{name: '续签记录', value: 'contracts', selected: true},\n" +
				"\t\t\t\t{name: '意外险', value: 'accidentInsurance', selected: true},\n" +
				"\t\t\t\t{name: '职工社保', value: 'socialStatus', selected: true},\n" +
				"\t\t\t\t{name: '社保归属地', value: 'socialAddr', selected: true},\n" +
				"\t\t\t\t{name: '银行卡', value: 'card', selected: true},\n" +
				"\t\t\t\t{name: '备注', value: 'remark', selected: true}]");

		Map<String,String> map=new HashMap<>();
		for (int i = 0; i < jsonArray2.size(); i++) {
			JSONObject jsonObject=jsonArray2.getJSONObject(i);
			map.put(jsonObject.getString("value"),jsonObject.getString("name"));
		}

		JSONArray jsonArray=JSON.parseArray(selectArray);
		String[] title2=new String[jsonArray.size()];
		for(int i=0;i<jsonArray.size();i++){
			//JSONObject jsonObject=jsonArray.getJSONObject(i);
			String value=jsonArray.getString(i);
			title2[i]=map.get(value);
		}
		String[][] content2=new String[orgEmployeePage.getList().size()][title2.length];
		if(recordList2.size()>0){
			for(int i=0;i<recordList2.size();i++){
				Record record=recordList2.get(i);
				for(int j=0;j<jsonArray.size();j++){
					//JSONObject jsonObject=jsonArray.getJSONObject(j);
					//content2[i][j]=record.get(jsonObject.getString("value"));
					content2[i][j]=record.get(jsonArray.getString(j));
				}
			}
		}

		//创建HSSFWorkbook
		HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title2, content2, null);
		//响应到客户端
		try {
			ByteArrayOutputStream os = new ByteArrayOutputStream();
			wb.write(os);
			render(new StreamRender(fileName, os));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public static List<String> findYearsStr(String beginTime, String endTime) {
		List<String> yearsStrList = new ArrayList<>();
		beginTime = beginTime.substring(0, 4);
		endTime = endTime.substring(0, 4);
		if (beginTime.equals(endTime)) {
			yearsStrList.add(beginTime);
		} else {
			yearsStrList.add(beginTime);
			for (int i = 1; i <= Integer.parseInt(endTime) - Integer.parseInt(beginTime); i++) {
				yearsStrList.add(String.valueOf(Integer.parseInt(beginTime) + i));
			}
		}
		return yearsStrList;
	}

	
	/**
	 * 机构员工关联分页表格数据
	 */
	@Clear(LogInterceptor.class)
	public void orgEmpPageTable() {
		PersOrgEmployeeRel orgEmployeeRel = getBean(PersOrgEmployeeRel.class, "", true);
		Page<PersOrgEmployeeRel> orgEmployeeRelPage = persOrgEmployeeRelService.paginateByCondition(orgEmployeeRel, getParaToInt("page", 1), getParaToInt("limit", 10));
		renderJson(new DataTable<PersOrgEmployeeRel>(orgEmployeeRelPage));
	}
    
    /**
     * 添加页面方法
     */
    public void add() {
		//List<PersPosition> positionList = persPositionService.getUnDelList();
    	//setAttr("model", new PersOrgEmployee());
    	//setAttr("positionList",positionList);
		List<ZTree> zTreeList=persOrgService.allOrgTree();

    	List<PersOrg> mainOrg=persOrgService.findMainOrg();
		setAttr("mainOrg",mainOrg);
    	setAttr("zTreeList", JSON.toJSONString(zTreeList));
        render("employeeForm.html");
    }
    
    /**
     * 修改页面方法
     */
    public void edit() {
		String handleType = getPara("handleType");
		final PersOrgEmployee model = persOrgEmployeeService.findById(getPara("id"));
		setAttr("model", model);
		List<PersOrg> mainOrg=persOrgService.findMainOrg();
		setAttr("mainOrg",mainOrg);

		List<ZTree> zTreeList=persOrgService.allOrgTree();
		setAttr("zTreeList", JSON.toJSONString(zTreeList));

		PersSocialAddress socialAddress=new PersSocialAddress();
		socialAddress.setIsEnabled("1");
		List<PersOrgCompany> socialAddresses=persOrgCompanyService.orgCompanyList();
		setAttr("socialAddresses", socialAddresses);
		if(model!=null){
			/*PersOrg org=persOrgService.findById(model.getOrgId());
			if(org!=null){
				setAttr("orgName",org.getOrgName());
			}*/
			//获取部门
			List<PersOrg> deptList=persOrgService.findDepartment(model.getOrgId());
			//获取职位
			List<PersPosition> positionList=persPositionService.findPositionByOrgId(model.getOrgId());
			setAttr("deptList",deptList);
			setAttr("positionList",positionList);

			List<PersOrgEmployeeRel> employeeRelList=persOrgEmployeeRelService.getRelList(model.getId(),null);
			List<Record> recordList=new ArrayList<>();
			for(PersOrgEmployeeRel rel:employeeRelList){


				if("position".equals(rel.getRelationshipType())){

					PersPosition persPosition=persPositionService.findById(rel.getRelationshipId());

					PersOrg org=persOrgService.findById(persPosition.getOrgId());
					Record record=new Record();

					record.set("relPositionId",rel.getId());
					record.set("positionId",rel.getRelationshipId());
					record.set("isMain",rel.getIsMain());
					MainRole mainRole = mainRoleService.findById(persPosition.getRoleId());
					if(mainRole!=null){
						PersOrgEmployeeRel roleRel=persOrgEmployeeRelService.getRelByList(employeeRelList,mainRole.getId(),null);
						if(roleRel!=null){
							record.set("relRoleId",roleRel.getId());
							record.set("roleId",roleRel.getRelationshipId());
							record.set("mainRole",mainRole);
							record.set("isMain",roleRel.getIsMain());
						}
					}
					List<String> excludeIds=new ArrayList<>();
					for(Record deptRecord:recordList){
						if(deptRecord.getStr("deptId").equals(org.getId())){
							excludeIds.add(deptRecord.getStr("relDeptId"));
						}
					}
					PersOrgEmployeeRel deptRel=persOrgEmployeeRelService.getRelByList(employeeRelList,org.getId(),excludeIds);
					if(deptRel!=null){
						record.set("relDeptId",deptRel.getId());
						record.set("deptId",deptRel.getRelationshipId());
						record.set("isMain",deptRel.getIsMain());
					}


					List<Record> persPositionList=persPositionService.getPositionByOrgId(org.getId());
					record.set("persPositionList",persPositionList);

					recordList.add(record);
				}
			}
			setAttr("recordList",recordList);

			if(StrKit.notBlank(model.getLeaderEmpId())){
				PersOrgEmployee leaderEmp = persOrgEmployeeService.findById(model.getLeaderEmpId());
				setAttr("leaderEmp",leaderEmp);
			}
		}
		if(StrKit.notBlank(handleType)){
			render("seeEmployeeForm.html");
		}else{
			render("employeeForm.html");
		}

    }
    
    /**
     * 查看页面方法
     */
    public void view() {
    	final PersOrgEmployee model = persOrgEmployeeService.findById(getPara("id"));
    	setAttr("model", model);
    	List<PersOrg> mainOrg=persOrgService.findMainOrg();
    	setAttr("mainOrg",mainOrg);
    	
    	List<ZTree> zTreeList=persOrgService.allOrgTree();
    	setAttr("zTreeList", JSON.toJSONString(zTreeList));
    	
    	PersSocialAddress socialAddress=new PersSocialAddress();
    	socialAddress.setIsEnabled("1");
		List<PersOrgCompany> socialAddresses=persOrgCompanyService.orgCompanyList();
    	setAttr("socialAddresses", socialAddresses);
    	if(model!=null){
    		//获取部门
    		List<PersOrg> deptList=persOrgService.findDepartment(model.getOrgId());
    		//获取职位
    		List<PersPosition> positionList=persPositionService.findPositionByOrgId(model.getOrgId());
    		setAttr("deptList",deptList);
    		setAttr("positionList",positionList);
    		
    		List<PersOrgEmployeeRel> employeeRelList=persOrgEmployeeRelService.getRelList(model.getId(),null);
    		List<Record> recordList=new ArrayList<>();
    		for(PersOrgEmployeeRel rel:employeeRelList){
    			if("position".equals(rel.getRelationshipType())){
    				
    				PersPosition persPosition=persPositionService.findById(rel.getRelationshipId());
    				
    				PersOrg org=persOrgService.findById(persPosition.getOrgId());
    				Record record=new Record();
    				
    				record.set("relPositionId",rel.getId());
    				record.set("positionId",rel.getRelationshipId());
    				MainRole mainRole = mainRoleService.findById(persPosition.getRoleId());
    				if(mainRole!=null){
    					PersOrgEmployeeRel roleRel=persOrgEmployeeRelService.getRelByList(employeeRelList,mainRole.getId(),null);
    					if(roleRel!=null){
    						record.set("relRoleId",roleRel.getId());
    						record.set("roleId",roleRel.getRelationshipId());
    						record.set("mainRole",mainRole);
    					}
    				}
    				List<String> excludeIds=new ArrayList<>();
    				for(Record deptRecord:recordList){
    					if(deptRecord.getStr("deptId").equals(org.getId())){
    						excludeIds.add(deptRecord.getStr("relDeptId"));
    					}
    				}
    				PersOrgEmployeeRel deptRel=persOrgEmployeeRelService.getRelByList(employeeRelList,org.getId(),excludeIds);
    				if(deptRel!=null){
    					record.set("relDeptId",deptRel.getId());
    					record.set("deptId",deptRel.getRelationshipId());
    				}
    				
    				
    				List<Record> persPositionList=persPositionService.getPositionByOrgId(org.getId());
    				record.set("persPositionList",persPositionList);
    				
    				recordList.add(record);
    			}
    		}
    		setAttr("recordList",recordList);
			if(StrKit.notBlank(model.getLeaderEmpId())){
				PersOrgEmployee leaderEmp = persOrgEmployeeService.findById(model.getLeaderEmpId());
				setAttr("leaderEmp",leaderEmp);
			}
    	}
    	
    	render("employeeView.html");
    }
    
    /**
     * 登陆帐号管理页面方法
     */
    public void loginAccountManage() {
    	final String empId = getPara("id");
    	setAttr("empId", empId);
    	render("loginAccountManage.html");
    }
    
    /**
     * 银行账户页面方法
     */
    public void bankAccount() {
    	final String empId = getPara("id");
    	setAttr("empId", empId);
    	render("bankAccount.html");
    }
    
    /**
     * 部门分配页面方法
     */
    public void orgAssign() {
    	final String empId = getPara("id");
    	setAttr("empId", empId);
    	render("orgAssign.html");
    }
    
    /**
     * 添加用户页面方法
     */
    public void addEmpUser() {
		final String empId = getPara("id");
		PersEmpUser model = new PersEmpUser();
		model.setEmpId(empId);
//		model.setOrgId(userOrgId);
    	setAttr("model", model);
    	setAttr("userList", userService.findUserList());
        render("employeeUserForm.html");
    }
    
    /**
     * 添加账户页面方法
     */
    public void editEmpAccount() {
    	PersOrgEmployeeAccount model = getBean(PersOrgEmployeeAccount.class, "", true);
    	if(StrKit.notBlank(model.getId())) {
    		model = persOrgEmployeeAccountService.findById(model.getId());
    	}
    	setAttr("model", model);
    	setAttr("bankList", mainBankService.findList());
    	render("employeeAccountForm.html");
    }
    
    
    /**
     * 编辑用户页面方法
     */
//    public void editEmpUser() {
//		final String empUserId = getPara(0);
//		Record model = persOrgEmployeeService.getEmpUser(empUserId);
//    	setAttr("model", model);
//    	setAttr("userList", userService.findUserList());
//        render("employeeUserForm.html");
//    }
    
    /**
     * 个人资料页面方法
     */
    public void info() {
    	final String empId = AuthUtils.getLoginUser().getEmployeeId();
    	final PersOrgEmployee model = persOrgEmployeeService.findById(empId);
    	setAttr("model", model);
    	render("employeeInfo.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
        final PersOrgEmployee orgEmp = getBean(PersOrgEmployee.class, "", true);
		int conut=getParaToInt("count");
        List<PersOrgEmployeeRel> employeeRelAddList=new ArrayList<>();
		List<PersOrgEmployeeRel> employeeRelUpdateList=new ArrayList<>();
		List<String> positionIds=new ArrayList<>();
        for(int i=1;i<=conut;i++){
        	String relDeptId=getPara("relDeptId-"+i);
        	String deptId=getPara("deptId-"+i);
        	String relPositionId=getPara("relPositionId-"+i);
			String positionId=getPara("positionId-"+i);
			String relRoleId=getPara("relRoleId-"+i);
			String roleId=getPara("roleId-"+i);
			String isMain=getPara("isMain-"+i);
			if(StrKit.isBlank(deptId)){
				continue;
			}
			if(positionIds.contains(positionId)){
				renderJson(Ret.fail("msg", "请勿重复添加相同职位！"));
				return;
			}
			positionIds.add(positionId);


			PersOrgEmployeeRel deptRel=new PersOrgEmployeeRel();
			deptRel.setRelationshipType("dept");
			deptRel.setRelationshipId(deptId);
			deptRel.setId(relDeptId);
			deptRel.setIsMain(isMain);
			if(StrKit.isBlank(orgEmp.getDeptId())){
				orgEmp.setDeptId(deptId);
			}

			PersOrgEmployeeRel positionRel=new PersOrgEmployeeRel();
			positionRel.setRelationshipType("position");
			positionRel.setRelationshipId(positionId);
			positionRel.setId(relPositionId);
			positionRel.setIsMain(isMain);
			PersOrgEmployeeRel roleRel=null;
			if(!"undefined".equalsIgnoreCase(roleId)){
				roleRel=new PersOrgEmployeeRel();
				roleRel.setRelationshipType("role");
				roleRel.setRelationshipId(roleId);
				roleRel.setId(relRoleId);
				roleRel.setIsMain(isMain);
			}

			if(StrKit.isBlank(deptRel.getId())){
				if(StrKit.notBlank(deptRel.getRelationshipId())){
					employeeRelAddList.add(deptRel);
				}
				if(StrKit.notBlank(positionRel.getRelationshipId())) {
					employeeRelAddList.add(positionRel);
				}
				if(roleRel!=null && StrKit.notBlank(roleRel.getRelationshipId())) {
					employeeRelAddList.add(roleRel);
				}
			}else{
				if(StrKit.notBlank(deptRel.getRelationshipId())){
					employeeRelUpdateList.add(deptRel);
				}
				if(StrKit.isBlank(positionRel.getId())){
                    if(StrKit.notBlank(positionRel.getRelationshipId())) {
                        employeeRelAddList.add(positionRel);
                    }
                }else{
                    if(StrKit.notBlank(positionRel.getRelationshipId())) {
                        employeeRelUpdateList.add(positionRel);
                    }
                }

				if(roleRel!=null && StrKit.isBlank(roleRel.getId())){
                    if(roleRel!=null && StrKit.notBlank(roleRel.getRelationshipId())) {
                        employeeRelAddList.add(roleRel);
                    }
                }else{
                    if(roleRel!=null && StrKit.notBlank(roleRel.getRelationshipId())) {
                        employeeRelUpdateList.add(roleRel);
                    }
                }
			}
		}
        if(employeeRelAddList.size()==0 && employeeRelUpdateList.size()==0){
			renderJson(Ret.fail("msg", "请至少添加一条部门记录！"));
        	return;
		}


        if(StrKit.notBlank(orgEmp.getId())){
			/*if(persOrgEmployeeService.employeeContractExist(orgEmp.getId(),orgEmp.getFullName(),null,null,"incumbency")){
				renderJson(Ret.fail("msg", "该名字已有一条在职记录存在,请检查！"));
				return;
			}*/
        	if(persOrgEmployeeService.employeeContractExist(orgEmp.getId(),null,orgEmp.getIdCard(),null,"incumbency")){
				renderJson(Ret.fail("msg", "该证件号已有一条在职记录存在,请检查！"));
				return;
			}
        	if(persOrgEmployeeService.employeeContractExist(orgEmp.getId(),null,null,orgEmp.getPhoneNum(),"incumbency")){
				renderJson(Ret.fail("msg", "该手机号已有一条在职记录存在,请检查！"));
				return;
			}
		}else{
			/*if(persOrgEmployeeService.employeeContractExist(null,orgEmp.getFullName(),null,null,"incumbency")){
				renderJson(Ret.fail("msg", "该名字已有一条在职记录存在,请检查！"));
				return;
			}*/
            if(persOrgEmployeeService.employeeContractExist(null,null,orgEmp.getIdCard(),null,"incumbency")){
                renderJson(Ret.fail("msg", "该证件号已有一条在职记录存在,请检查！"));
                return;
            }
            if(persOrgEmployeeService.employeeContractExist(null,null,null,orgEmp.getPhoneNum(),"incumbency")){
                renderJson(Ret.fail("msg", "该手机号已有一条在职记录存在,请检查！"));
                return;
            }
            if(persOrgEmployeeService.employeeContractExist(null,null,orgEmp.getIdCard(),null,"quit")){
                //renderJson(Ret.fail("msg", "该证件号已存在！"));
                //return;
                orgEmp.setIsReemployment("1");
            }else{
                orgEmp.setIsReemployment("0");
            }
		}
		//校验身份证
		String checkIdCardResult = HttpClientsUtils.get(Global.checkIdCardUrl+"&name="+orgEmp.getFullName()
				+"&idCard="+orgEmp.getIdCard()+"&userId="+AuthUtils.getUserId()+"&channel=AddStaffInfo&idCardType="+orgEmp.getIdCardType());
		if(!checkIdCardResult.startsWith("{") || !checkIdCardResult.endsWith("}")){
			renderJson(Ret.fail("msg","校验身份证接口异常:"+checkIdCardResult));
			return;
		}
		JSONObject checkIdCardObj=JSON.parseObject(checkIdCardResult);
		if(!"1".equals(checkIdCardObj.getString("Type"))){
			renderJson(Ret.fail("msg",checkIdCardObj.getString("Msg")));
			return;
		}

		orgEmp.setUpdateBy(AuthUtils.getUserId());

        if (persOrgEmployeeService.saveOrgEmployee(orgEmp,employeeRelAddList,employeeRelUpdateList)) {
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /*public void orgEmpSave() {
    	final String empId = getPara("empId");
    	final String orgId = getPara("orgId");
    	if(StringUtils.isNotBlank(orgId)){
    		PersOrg persOrg = persOrgService.findById(orgId);
    		if(persOrg!=null){
    			final String parentId = persOrg.getParentId();
    			final String orgType = persOrg.getOrgType();
    			if((OrgType.DEPARTMENT).equals(orgType)){
    				//判断是否添加过这个
					if(persOrgEmployeeRelService.findByDeptId(empId,orgId)!=null){
						renderJson(Ret.fail("msg", "已添加过该部门，请勿重复添加!"));
						return;
					}
    				PersOrgEmployeeRel orgEmp = new PersOrgEmployeeRel();
    				orgEmp.setEmpId(empId);
    				orgEmp.setOrgId(parentId);
    				orgEmp.setDeptId(orgId);
    				orgEmp.setIsMain("no");
    				renderJson(persOrgEmployeeRelService.empOrgSave(orgEmp));
    			}else if((OrgType.GROUP).equals(orgType)){
					//判断是否添加过这个
					if(persOrgEmployeeRelService.findByGroupId(empId,orgId)!=null){
						renderJson(Ret.fail("msg", "已添加过该小组，请勿重复添加!"));
						return;
					}
    				PersOrg pOrg = persOrgService.findById(parentId);
    				PersOrgEmployeeRel orgEmp = new PersOrgEmployeeRel();
    				orgEmp.setEmpId(empId);
    				orgEmp.setOrgId(pOrg.getParentId());
    				orgEmp.setDeptId(parentId);
    				orgEmp.setGroupId(orgId);
    				orgEmp.setIsMain("no");
    				renderJson(persOrgEmployeeRelService.empOrgSave(orgEmp));
    			}else{
    				renderJson(Ret.fail("msg", "请选择部门或小组！"));
    			}
    		}else{
    			renderJson(Ret.fail("msg", "机构不存在！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "请选择数据！"));
    	}
    }*/
    
    /*public void empOrgAssignSave() {
    	final int quantityCount = getParaToInt("quantityCount");
        List<PersOrgEmployeeRel> empOrgList = new ArrayList<PersOrgEmployeeRel>();
        if(quantityCount>0){
        	for (int i = 1; i <= quantityCount; i++) {
        		final PersOrgEmployeeRel empOrg = getBean(PersOrgEmployeeRel.class, "empOrgList["+i+"]");
        		if(StrKit.notBlank(empOrg.getDeptId())){
        			empOrgList.add(empOrg);
        		}
        	}
        }
        renderJson(persOrgEmployeeRelService.empOrgSave(empOrgList));
    }*/
    
    /*public void empOrgUpdate() {
    	final String orgEmpId = getPara("orgEmpId");
    	final PersOrgEmployeeRel orgEmpRel = getBean(PersOrgEmployeeRel.class, "", true);
    	if(orgEmpRel!=null && StrKit.notBlank(orgEmpRel.getId())){
    		List<PersOrgEmployeeRel> orgEmpList = persOrgEmployeeRelService.findListByEmpId(orgEmpId, "yes");
    		if("yes".equals(orgEmpRel.getIsMain()) && orgEmpList!=null && orgEmpList.size()>0){
    			renderJson(Ret.fail("msg", "只能设置一个主部门！"));
    		}else{
    			if(persOrgEmployeeRelService.update(orgEmpRel)){
    				renderJson(Ret.ok("msg", "操作成功！"));
    			}else{
    				renderJson(Ret.fail("msg", "操作失败！"));
    			}
    		}
    	}else{
    		renderJson(Ret.fail("msg", "操作失败,ID为空！"));
    	}
    }*/
    
    public void empOrgDel() {
    	final String empOrgId = getPara("id");
    	if(StrKit.notBlank(empOrgId)){
    		PersOrgEmployeeRel persOrgEmployeeRel=persOrgEmployeeRelService.findById(empOrgId);
    		if(persOrgEmployeeRelService.deleteById(empOrgId)){

    			//更新企业微信用户的部门
				int[] ids=persOrgEmployeeService.findOrgQiYeId(persOrgEmployeeRel.getEmpId());
				String userName= Db.queryStr("select u.user_name from pers_emp_user e left join sys_user u on e.user_id=u.id where e.emp_id=?",persOrgEmployeeRel.getEmpId());

				if(ids!=null && ids.length>0 && StrKit.notBlank(userName)){
					QiYeUser user=new QiYeUser();
					user.setUserId(userName);
					user.setDepartment(ids);
					qiYeWeiXinService.updateUser(user);
				}
    			renderJson(Ret.ok("msg", "操作成功！"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "操作失败,ID为空！"));
    	}
    }

    public void quitByWorkNum(){
    	String workNums=getPara("workNums");
    	if(StrKit.isBlank(workNums)){
    		renderJson(Ret.fail());
    		return;
		}
    	String[] workArray=null;
    	if(workNums.indexOf(",")>0){
			workArray=workNums.split(",");
		}else{
			workArray=new String[]{workNums};
		}
    	Map<String,Boolean> map=new HashMap<>();
    	for(String workNum:workArray){
    		String sql="select id from pers_org_employee where work_num=? and del_flag='0' and archive_status='incumbency'";
    		String id=Db.queryStr(sql,workNum);
			PersOrgEmployee employee = persOrgEmployeeService.findById(id);
			if(employee!=null){
				employee.setArchiveStatus("quit");
				employee.setUpdateBy(AuthUtils.getUserId());
				employee.setUpdateDate(new Date());
				//离职
				boolean flag = persOrgEmployeeService.saveOrgEmployee(employee, new ArrayList<>(), new ArrayList<>());
				map.put(workNum,flag);
			}else{
				map.put(workNum,false);
			}

		}
		renderJson(JSON.toJSONString(map));
	}
    
    /**
     * 保存员工与帐号方法
     */
    public void saveEmpUser() {
    	final PersEmpUser empUser = getBean(PersEmpUser.class, "", true);
		renderJson(persOrgEmployeeService.saveEmpUser(empUser));
    }
    
    /**
     * 保存员工账户方法
     */
    public void saveEmpAccount() {
    	PersOrgEmployeeAccount model = getBean(PersOrgEmployeeAccount.class, "", true);
    	boolean flag = false;
    	if(StrKit.isBlank(model.getId())) {
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateDate(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateDate(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateDate(new Date());
    		flag = model.update();
    	}
    	if(flag) {
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else {
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    
    public void delEmpUser() {
    	final PersEmpUser empUser = getBean(PersEmpUser.class, "", true);
    	if (persOrgEmployeeService.delEmpUser(empUser)) {
    		renderJson(Ret.ok("msg", "操作成功!"));
    	} else {
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
    
    
    /**
     * 保存方法
     */
    public void del() {
        final PersOrgEmployee orgEmp = getBean(PersOrgEmployee.class, "", true);
			renderJson(persOrgEmployeeService.del(orgEmp));
    }
    
    /**
     * 删除当前数据及其帐号数据
     */
    public void batchDel() {
    	final String batchDelDatas = getPara("batchDelDatas");
    	if(StrKit.notBlank(batchDelDatas)){
    		List<PersOrgEmployee> empList = JSONArray.parseArray(batchDelDatas, PersOrgEmployee.class);
    		if (persOrgEmployeeService.batchDel(empList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }

    public void createQiYeUser(){


	}

	public void birthdayEmployeeIndex(){
    	render("employeeBirthdayIndex.html");
	}

	public void findBirthdayEmployeePage(){
    	String start=getPara("start");
		String end=getPara("end");
    	String deptId=getPara("deptId");

		PersOrgEmployee employee=new PersOrgEmployee();
		employee.setDeptId(deptId);

    	Page<PersOrgEmployee> employeePage=persOrgEmployeeService.birthdayPaginateByCondition(employee,AuthUtils.getUserId(),start,end,getParaToInt("page"),getParaToInt("limit"));

    	renderJson(new DataTable<PersOrgEmployee>(employeePage));
	}

	public void exportBirthdayEmployeeList(){
		String start=getPara("start");
		String end=getPara("end");
		String deptId=getPara("deptId");

		PersOrgEmployee employee=new PersOrgEmployee();
		employee.setDeptId(deptId);

		Page<PersOrgEmployee> employeePage=persOrgEmployeeService.birthdayPaginateByCondition(employee,AuthUtils.getUserId(),start,end,1,20000);

		String[] title={"部门","职位","姓名","性别","生日"};
		String fileName="员工生日表.xls";
		String sheetName = "员工生日表";
		String[][] content=new String[1][title.length];
		if(employeePage.getList()!=null && employeePage.getList().size()>0){
			content=new String[employeePage.getList().size()][title.length];
			for(int i=0;i<employeePage.getList().size();i++){
				PersOrgEmployee record=employeePage.getList().get(i);

				content[i][0]=record.getOrg().getStr("orgName");
				content[i][1]=record.getOrg().getStr("positionName");
				content[i][2]=record.getFullName();
				if("male".equals(record.getSex())){
					content[i][3]="男";
				}else if("female".equals(record.getSex())){
					content[i][3]="女";
				}else{
					content[i][3]="未知";
				}
				content[i][4]=DateUtils.formatDate(DateUtils.parseDate(record.getBirthday()),"MM-dd");
			}
		}

		//创建HSSFWorkbook
		HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
		//响应到客户端
		try {
			ByteArrayOutputStream os = new ByteArrayOutputStream();
			wb.write(os);
			render(new StreamRender(fileName, os));
		} catch (Exception e) {
			e.printStackTrace();
		}


	}

	public void contractIndex(){
    	String employeeId=getPara("employeeId");
    	setAttr("employeeId",employeeId);
    	render("contractIndex.html");
	}

	public void contractPage(){
		PersOrgEmployeeContract contract=getBean(PersOrgEmployeeContract.class,"",true);

		Page<PersOrgEmployeeContract> page=persOrgEmployeeContractService.employeeContractPage(getParaToInt("page"),getParaToInt("limit"),contract);

		renderJson(new DataTable<PersOrgEmployeeContract>(page));
	}

	public void contractForm(){
		String employeeId=getPara("employeeId");
		String id=getPara("id");
		PersOrgEmployee employee=persOrgEmployeeService.findById(employeeId);

		if(StrKit.notBlank(id)){
			PersOrgEmployeeContract contract=persOrgEmployeeContractService.findById(id);
			contract.put("social",contract.getIsSocialg());
			setAttr("contract",contract);
		}
		setAttr("empId",employeeId);
		setAttr("employee",employee);
		setAttr("fileUploadUrl", Global.fileUploadUrl);
		render("contractForm.html");
	}

	public void saveContract(){
		PersOrgEmployeeContract contract=getBean(PersOrgEmployeeContract.class,"",true);
		if(StrKit.notBlank(contract.getEmpId())){
			PersOrgEmployee employee=persOrgEmployeeService.findById(contract.getEmpId());
			contract.setDeptId(employee.getDeptId());
			contract.setPosition(employee.getPosition());
		}
		//判断电话号码是否存在

		boolean flag=persOrgEmployeeContractService.saveEmployeeContract(contract,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}

	public void delContract(){
    	String id=getPara("id");
		PersOrgEmployeeContract contract=new PersOrgEmployeeContract();
		contract.setId(id);
		contract.setDelFlag("1");
		boolean flag=persOrgEmployeeContractService.saveEmployeeContract(contract,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}


	/**
	 * 调岗/调薪 首页
	 */
	public void changeDepartmentIndex(){
		String employeeId=getPara("employeeId");

		setAttr("employeeId",employeeId);
    	render("changeDepartmentIndex.html");
	}


	public void changeDepartmentPage(){
		String employeeId=getPara("employeeId");

		Page<Record> page=persOrgEmployeeChangeDeptService.findChangeDeptPage(getParaToInt("page"),getParaToInt("limit"),employeeId);

		renderJson(new DataTable<Record>(page));
	}

	public void excelUploadEmp(){
		UploadFile uploadFile = getFile();
		if(uploadFile == null){
			renderJson(Ret.fail("msg","文件不存在"));
			return;
		}
		try {
			/*XSSFWorkbook workbook=new XSSFWorkbook(uploadFile.getFile());
			XSSFSheet sheet = workbook.getSheetAt(0);

			int lastRowNum = sheet.getLastRowNum();


			// 创建样式
			XSSFCellStyle style = workbook.createCellStyle();
			style.setDataFormat(workbook.createDataFormat().getFormat(
					"yyyy-MM-dd"));*/
			FileInputStream inputStream=new FileInputStream(uploadFile.getFile());
			List<Record> recordList=new ArrayList<>();
			HSSFWorkbook hssfWorkbook=new HSSFWorkbook(inputStream);
			HSSFSheet sheet = hssfWorkbook.getSheetAt(0);

			int lastRowNum = sheet.getLastRowNum();

			HSSFCellStyle style = hssfWorkbook.createCellStyle();
			style.setDataFormat(hssfWorkbook.createDataFormat().getFormat(
					"yyyy-MM-dd"));

			for (int j = 0; j <= lastRowNum; j++) {
				// 获取当前行
				HSSFRow row = sheet.getRow(j);
				//获取第一行表头
				HSSFRow row1 = sheet.getRow(0);
				if (row != null) {
					// 获取当前行最后一个单元格
					short cellNum = row.getLastCellNum();

					Record record=new Record();
					// 从第一个单元格开始遍历，直到最后一个单元格
					for (int k = 0; k < cellNum; k++) {
						HSSFCell cell=row.getCell(k);
						if(cell!=null){
							if (cell.getCellType()==Cell.CELL_TYPE_NUMERIC){
								if(HSSFDateUtil.isCellDateFormatted(cell)){
									//用于转化为日期格式
									Date d = cell.getDateCellValue();
									DateFormat formater = new SimpleDateFormat("yyyy-MM-dd");
									record.set(row1.getCell(k).getStringCellValue(),formater.format(d));
								}
							}else if (cell.getCellType()==Cell.CELL_TYPE_STRING){
								record.set(row1.getCell(k).getStringCellValue(),cell.getStringCellValue());
							}
						}else{
							System.out.print("null"+"  ");
						}

					}
					recordList.add(record);
				}
				System.out.println();
			}

			List<PersOrgEmployee> employeeList=new ArrayList<>();
			List<PersOrgEmployeeContract> employeeContractList=new ArrayList<>();

			List<PersOrgEmployeeRel> addRelList=new ArrayList<>();
			List<String> delIds=new ArrayList<>();

			for(Record record:recordList){
				String workNum=record.getStr("工号");
				PersOrgEmployee employee=persOrgEmployeeService.getEmployeeByWorkNum(workNum);
				if(employee!=null){
					String fullName=record.getStr("姓名");
					if(StrKit.notBlank(fullName.replace(" ",""))){
						employee.setFullName(fullName);
					}
					String sex=record.getStr("性别");
					if(StrKit.notBlank(sex)){
						if("男".equals(sex)){
							employee.setSex("male");
						}else if("女".equals(sex)){
							employee.setSex("female");
						}
						employee.setFullName(fullName);
					}
					String phoneNum=record.getStr("联系方式");
					employee.setPhoneNum(phoneNum);
					if(StrKit.notBlank(record.getStr("入职日期"))){
						employee.setEntryTime(DateUtils.parseDate(record.getStr("入职日期")));
					}
					if(StrKit.notBlank(record.getStr("转正日期"))){
						employee.setFormalDate(DateUtils.parseDate(record.getStr("转正日期")));
					}



					/*String deptId=Db.queryStr("select relationship_id from pers_org_employee_rel where emp_id=? and relationship_type='dept' ",employee.getId());
					if(StrKit.isBlank(deptId)){
						renderText(employee.getId()+"部门为null");
						return;
					}*/
					//String zhiwei=record.getStr("职位");
					//获取职位
					//Record zhiweiRecord=Db.findFirst("select * from pers_position where org_id=? and position_name=? and del_flag='0'",deptId,zhiwei);
					//获取员工的
					/*if(zhiweiRecord==null){
						renderText("职位查询不到："+deptId+":"+zhiwei);
						return;
					}
					if(StrKit.isBlank(zhiweiRecord.getStr("role_id"))){
						renderText("职位角色未配置："+deptId+":"+zhiwei);
						return;
					}
					//获取部门关系
					PersOrgEmployeeRel deptRel=persOrgEmployeeRelService.getRel(employee.getId(),"dept",deptId);
					if(deptRel==null){
						PersOrgEmployeeRel newDeptRel=new PersOrgEmployeeRel();
						newDeptRel.setId(IdGen.getUUID());
						newDeptRel.setEmpId(employee.getId());
						newDeptRel.setRelationshipType("dept");
						newDeptRel.setRelationshipId(deptId);

						PersOrgEmployeeRel newPositionRel=new PersOrgEmployeeRel();
						newPositionRel.setId(IdGen.getUUID());
						newPositionRel.setEmpId(employee.getId());
						newPositionRel.setRelationshipType("position");
						newPositionRel.setRelationshipId(zhiweiRecord.getStr("id"));

						PersOrgEmployeeRel newRoleRel=new PersOrgEmployeeRel();
						newRoleRel.setId(IdGen.getUUID());
						newRoleRel.setEmpId(employee.getId());
						newRoleRel.setRelationshipType("role");
						newRoleRel.setRelationshipId(zhiweiRecord.getStr("role_id"));
						addRelList.add(newRoleRel);
						addRelList.add(newPositionRel);
						addRelList.add(newDeptRel);

						List<String> ids=Db.query("select id from pers_org_employee_rel where emp_id=? ",employee.getId());
						if(ids.size()>0){
							delIds.addAll(ids);
						}
					}else{
						PersOrgEmployeeRel positionRel=persOrgEmployeeRelService.getRel(employee.getId(),"position",zhiweiRecord.getStr("id"));
						PersOrgEmployeeRel roleRel=persOrgEmployeeRelService.getRel(employee.getId(),"role",zhiweiRecord.getStr("role_id"));
						if(positionRel==null){
							PersOrgEmployeeRel newPositionRel=new PersOrgEmployeeRel();
							newPositionRel.setId(IdGen.getUUID());
							newPositionRel.setEmpId(employee.getId());
							newPositionRel.setRelationshipType("position");
							newPositionRel.setRelationshipId(zhiweiRecord.getStr("id"));
							addRelList.add(newPositionRel);

							List<String> ids=Db.query("select id from pers_org_employee_rel where emp_id=? and relationship_type='position' ",employee.getId());
							if(ids.size()>0){
								delIds.addAll(ids);
							}
						}
						if(roleRel==null){
							PersOrgEmployeeRel newRoleRel=new PersOrgEmployeeRel();
							newRoleRel.setId(IdGen.getUUID());
							newRoleRel.setEmpId(employee.getId());
							newRoleRel.setRelationshipType("position");
							newRoleRel.setRelationshipId(zhiweiRecord.getStr("id"));
							addRelList.add(newRoleRel);

							List<String> ids=Db.query("select id from pers_org_employee_rel where emp_id=? and relationship_type='role' ",employee.getId());
							if(ids.size()>0){
								delIds.addAll(ids);
							}
						}

					}*/

					String syxz=record.getStr("试用薪资");
					if(StrKit.notBlank(syxz)){
						employee.setProbationSalary(Double.valueOf(syxz.toString()));
					}

					String zzxz=record.getStr("转正薪资");
					if(StrKit.notBlank(zzxz)){
						employee.setSalary(Double.valueOf(zzxz.toString()));
					}

					String xyxz=record.getStr("现有薪酬");
					if(StrKit.notBlank(xyxz)){
						employee.setNowSalary(Double.valueOf(xyxz.toString()));
					}

					employee.setIdCard(record.getStr("身份证号码"));
					employee.setNativePlace(record.getStr("籍贯"));
					employee.setBirthday(record.getStr("出生日期"));
					employee.setIdCardAddr(record.getStr("身份证地址"));
					employee.setResidentAddr(record.getStr("现住地址"));
					employee.setLinkPeople(record.getStr("紧急联系人"));
					employee.setLinkPhone(record.getStr("紧急联系人手机"));
					employee.setUpdateDate(new Date());
					String yiwaixian=record.getStr("意外险");
					if(StrKit.notBlank(yiwaixian)){
						if("已缴".equals(yiwaixian)){
							employee.setAccidentInsurance("1");
						}else if("放弃".equals(yiwaixian)){
							employee.setAccidentInsurance("3");
						}else if("未缴".equals(yiwaixian)){
							employee.setAccidentInsurance("2");
						}
					}

					String shebao=record.getStr("职工社保");
					employee.setSocialAddr(record.getStr("社保归属地"));
					if(StrKit.notBlank(shebao)){
						if("已缴".equals(shebao)){
							employee.setSocialStatus("1");
						}else if("放弃".equals(shebao)){
							employee.setSocialStatus("3");
						}else if("未缴".equals(shebao)){
							employee.setSocialStatus("2");
						}
					}



					String contractDates=record.getStr("合同期限");
					String contractType=record.getStr("合同类型");
					String contractAddr=record.getStr("合同归属地");
					/*if(StrKit.notBlank(contractDates)){
						String[] dates=contractDates.split("-");
						if(dates!=null){
							PersOrgEmployeeContract contract=new PersOrgEmployeeContract();
							contract.setId(IdGen.getUUID());
							contract.setEmpId(employee.getId());
							contract.setContractStartDate(DateUtils.parseDate(dates[0]));
							contract.setContractEndDate(DateUtils.parseDate(dates[1]));
							contract.setDelFlag("0");
							contract.setCreateDate(new Date());
							contract.setUpdateDate(new Date());
							contract.setContractAddr(contractAddr);
							if("劳动合同".equals(contractType)){
								contract.setContractType("1");
							}else if("劳务合同".equals(contractType)){
								contract.setContractType("2");
							}else if("临时合同".equals(contractType)){
								contract.setContractType("3");
							}
							employeeContractList.add(contract);
						}
					}*/
					String hetongyouxiaoqi=record.getStr("合同有效期");
					String hetongdaoqiri=record.getStr("合同到期日");

					Date hetongyouxiaoqiDate=null;
					Date hetongdaoqiriDate=null;
					try {
						hetongyouxiaoqiDate=DateUtils.parseDate(hetongyouxiaoqi);
						hetongdaoqiriDate=DateUtils.parseDate(hetongdaoqiri);
						if(hetongyouxiaoqiDate!=null && hetongdaoqiriDate!=null){
							PersOrgEmployeeContract contract2=new PersOrgEmployeeContract();
							contract2.setId(IdGen.getUUID());
							contract2.setEmpId(employee.getId());
							contract2.setContractStartDate(hetongyouxiaoqiDate);
							contract2.setContractEndDate(hetongdaoqiriDate);
							contract2.setDelFlag("0");
							contract2.setCreateDate(new Date());
							contract2.setUpdateDate(new Date());
							contract2.setContractAddr(contractAddr);
							if("劳动合同".equals(contractType)){
								contract2.setContractType("1");
							}else if("劳务合同".equals(contractType)){
								contract2.setContractType("2");
							}else if("临时合同".equals(contractType)){
								contract2.setContractType("3");
							}
							employeeContractList.add(contract2);
						}
					}catch (Exception e){
						renderJson(Ret.fail("msg","合同有效期或合同到期日格式错误"));
						e.printStackTrace();
						return;
					}

					String xuqian=record.getStr("续签记录");

					if(StrKit.notBlank(xuqian) && !"暂无".equals(xuqian)){
						String[] dates=xuqian.split("-");
						if(dates!=null){
							Date start=DateUtils.parseDate(dates[0]);
							Date end=DateUtils.parseDate(dates[1]);

							PersOrgEmployeeContract contract=new PersOrgEmployeeContract();
							contract.setId(IdGen.getUUID());
							contract.setEmpId(employee.getId());
							contract.setContractStartDate(start);
							contract.setContractEndDate(end);
							contract.setDelFlag("0");
							contract.setCreateDate(new Date());
							contract.setUpdateDate(new Date());
							contract.setContractAddr(contractAddr);
							if("劳动合同".equals(contractType)){
								contract.setContractType("1");
							}else if("劳务合同".equals(contractType)){
								contract.setContractType("2");
							}else if("临时合同".equals(contractType)){
								contract.setContractType("3");
							}
							employeeContractList.add(contract);
						}
					}
					employeeList.add(employee);
				}
			}
			boolean flag=Db.tx(new IAtom() {
				@Override
				public boolean run() throws SQLException {

					try {
						Db.batchUpdate(employeeList,employeeList.size());
						Db.batchSave(employeeContractList,employeeContractList.size());
						/*if(addRelList.size()>0){
							Db.batchSave(addRelList,addRelList.size());
						}
						if(delIds.size()>0){
							String str="";
							for(String id:delIds){
								str+="?,";
							}
							str=str.substring(0,str.length()-1);
							String sql="delete from pers_org_employee_rel where id in("+str+") ";
							Db.update(sql,delIds.toArray());
						}*/

						return true;
					}catch (Exception e){
						e.printStackTrace();
						renderJson(Ret.fail("msg","error"+e.getMessage()));
						return false;

					}

				}
			});
			if(uploadFile.getFile().exists()){
				uploadFile.getFile().delete();
			}
		}catch (Exception e){
			renderJson(Ret.fail("msg","error"+e.getMessage()));
			e.printStackTrace();
			return;
		}
		renderJson(Ret.ok("msg","操作成功"));
	}


	public void changeDepartmentForm(){
		String employeeId=getPara("employeeId");
		String id=getPara("id");
		if(StrKit.notBlank(id)){
			Record record=persOrgEmployeeChangeDeptService.findChangeDeptById(id);
			set("record",record);

			PersOrg org=persOrgService.findById(record.getStr("orgId"));
			if(org!=null){
				setAttr("orgName",org.getOrgName());
			}
			//获取部门
			List<PersOrg> deptList=persOrgService.findDepartment(record.getStr("orgId"));
			//获取职位
			List<PersPosition> positionList=persPositionService.findPositionByOrgId(record.getStr("orgId"));
			setAttr("deptList",deptList);
			setAttr("positionList",positionList);


		}else{
			PersOrgEmployee employee=persOrgEmployeeService.findById(employeeId);
			PersOrg org=persOrgService.findById(employee.getOrgId());
			PersOrg dept=persOrgService.findById(employee.getDeptId());
			PersPosition persPosition=persPositionService.findById(employee.getPosition());

			Record record=new Record();
			record.set("oldOrgId",org.getId());
			record.set("oldOrgName",org.getOrgName());
			record.set("oldDeptId",dept.getId());
			record.set("oldDeptName",dept.getOrgName());
			record.set("oldPosition",persPosition.getId());
			record.set("oldPositionName",persPosition.getPositionName());
			setAttr("record",record);
		}
		setAttr("employeeId",employeeId);
		render("changeDepartmentForm.html");
	}

	public void saveChangeDepartment(){
		PersOrgEmployeeChangeDept changeDept=getBean(PersOrgEmployeeChangeDept.class,"",true);

		if(Db.findFirst("select a.id from pers_org_employee_change_dept a left join pers_task b on a.id=b.record_id " +
				" where a.employee_id=? and a.del_flag='0' and b.is_end='0' and b.del_flag='0' ",changeDept.getEmployeeId())!=null){
			renderCodeFailed("该员工存在流程未结束的记录，请勿重复申请");
			return;
		}
		boolean flag=persOrgEmployeeChangeDeptService.saveChangeDept(changeDept,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}

	public void rewardPunishIndex(){
		String employeeId=getPara("employeeId");

		setAttr("employeeId",employeeId);
		render("rewardPunishIndex.html");
	}

	public void rewardPunishPage(){
		String employeeId=getPara("employeeId");
		Page<PersOrgEmployeeRewardPunish> page=persOrgEmployeeRewardPunishService.findRewardPunish(getParaToInt("page"),getParaToInt("limit"),employeeId);
		renderJson(new DataTable<PersOrgEmployeeRewardPunish>(page));
	}

	public void rewardPunishForm(){
		String employeeId=getPara("employeeId");
		String id=getPara("id");
		if(StrKit.notBlank(id)){
			PersOrgEmployeeRewardPunish record=persOrgEmployeeRewardPunishService.findById(id);
			setAttr("record",record);
		}
		setAttr("employeeId",employeeId);
		render("rewardPunishForm.html");
	}

	public void saveRewardPunish(){
		PersOrgEmployeeRewardPunish rewardPunish=getBean(PersOrgEmployeeRewardPunish.class,"",true);

		boolean flag=persOrgEmployeeRewardPunishService.saveRewardPunish(rewardPunish,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}

	public void delRewardPunish(){
		String id=getPara("id");
		PersOrgEmployeeRewardPunish rewardPunish=new PersOrgEmployeeRewardPunish();
		rewardPunish.setId(id);
		rewardPunish.setDelFlag("1");
		boolean flag=persOrgEmployeeRewardPunishService.saveRewardPunish(rewardPunish,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
	}


	public void enclosureIndex(){
		String employeeId=getPara("employeeId");

		setAttr("employeeId",employeeId);
		render("orgEmployeeEnclosureIndex.html");
	}

	public void enclosureForm(){
		String employeeId=getPara("employeeId");

		setAttr("employeeId",employeeId);
		render("orgEmployeeEnclosureForm.html");
	}

	public void employeeEnclosurePage(){
		PersOrgEmployeeFile file=getBean(PersOrgEmployeeFile.class,"",true);

		Page<PersOrgEmployeeFile> filePage=persOrgEmployeeFileService.employeeEnclosurePage(getParaToInt("page"),getParaToInt("limit"),file);
		renderJson(new DataTable<PersOrgEmployeeFile>(filePage));
	}

	public void saveEnclosure(){
		PersOrgEmployeeFile employeeFile=getBean(PersOrgEmployeeFile.class,"",true);
		UploadFile uploadFile=getFile("file");
		String fileTitle=getPara("fileTitle");
		String remark=getPara("remark");
		String employeeId=getPara("employeeId");
		employeeFile.setFileTitle(fileTitle);
		employeeFile.setRemark(remark);
		employeeFile.setEmployeeId(employeeId);
		if(StrKit.isBlank(employeeFile.getFileTitle())){
			renderJson(Ret.fail("msg","附件标题不能为空"));
			return;
		}
		if(uploadFile==null){
			renderJson(Ret.fail("msg","附件不能为空"));
			return;
		}
		//判断是否是图片
		String fileSuffix=uploadFile.getFileName().substring(uploadFile.getFileName().lastIndexOf("."));
		File file=uploadFile.getFile();
		File upFile=null;
		if(".jpg".equals(fileSuffix) || ".png".equals(fileSuffix)){
			String base64=null;
			InputStream newInputStream=null;
			ByteArrayInputStream stream=null;
			InputStream inputStream=null;
			try {
				inputStream=new FileInputStream(file);
				byte[] buff=new byte[(int)file.length()];
				inputStream.read(buff);
				Encoder encoder = Base64.getEncoder();
				base64 = encoder.encodeToString(buff);
				//判断是否压缩过
				base64=FileUploadKit.resizeImageTo800(base64);

				Decoder decoder = Base64.getDecoder();
				byte[] bytes1 = decoder.decode(base64);
				stream = new ByteArrayInputStream(bytes1);

				BufferedImage image = ImageIO.read(stream);

				final String osName = System.getProperty("os.name");
				String newPath="";
				if(osName.toLowerCase().indexOf("linux") > -1 || osName.toLowerCase().indexOf("centos") > -1){
					newPath = file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf("/")).concat("/"+IdGen.getUUID()+file.getName().substring(file.getName().lastIndexOf("."),file.getName().length()));
				}else{
					newPath = file.getAbsolutePath().substring(0, file.getAbsolutePath().lastIndexOf("\\")).concat("\\"+IdGen.getUUID()+file.getName().substring(file.getName().lastIndexOf("."),file.getName().length()));
				}
				upFile=new File(newPath);
				ImageIO.write(image,"jpg",upFile);

			}catch (Exception e){
				e.printStackTrace();
			}finally {
				try {
					if(newInputStream!=null){
						newInputStream.close();
					}
					if(stream!=null){
						stream.close();
					}
					if(inputStream!=null){
						inputStream.close();
					}
				}catch (Exception e){
					e.printStackTrace();
				}
			}
		}else{
			upFile=file;
		}
		long fileMaxSize=20971520L;
		if(uploadFile.getFile().length()>fileMaxSize){
			renderJson(Ret.fail("msg","文件大小不能超过20M"));
			return;
		}
		employeeFile.setCreateBy(AuthUtils.getUserId());
		Map<String,Object> map=persOrgEmployeeFileService.saveEmployeeEnclosure(employeeFile,upFile);
		if(upFile.exists() && upFile.length()>0){
			file.delete();
		}
		if(upFile.exists()){
			upFile.delete();
		}
		if((boolean)map.get("flag")){
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg",map.get("msg")));
		}
	}

	public void downloadEnclosure(){
		String id=getPara("id");
		PersOrgEmployeeFile employeeFile=persOrgEmployeeFileService.findById(id);
		PersOrgEmployee employee=persOrgEmployeeService.findById(employeeFile.getEmployeeId());
		//renderFile(FileUploadKit.downNetworkFile(employeeFile.getFileUrl(),employee.getFullName()+"-"+employeeFile.getFileTitle()));
		HttpServletResponse response=getResponse();
		//response.setContentType("application/x-download");

		String dwonloadFileName=employee.getFullName()+"-"+employeeFile.getFileTitle();
		String suffix = employeeFile.getFileUrl().substring(employeeFile.getFileUrl().lastIndexOf("."));
		InputStream in = null;
		OutputStream outputStream=null;
		try{
			outputStream=response.getOutputStream();
			response.addHeader("Content-Disposition", "attachment;filename="+new String(dwonloadFileName.getBytes("utf-8"), "ISO8859-1" )+suffix);

			URL fileURL = new URL(employeeFile.getFileUrl());
			URLConnection con = fileURL.openConnection();
			// 设置请求超时为5s
			con.setConnectTimeout(5 * 1000);
			in = con.getInputStream();
			response.setHeader("Content-Length", String.valueOf(con.getContentLength()));
			response.setContentType(con.getHeaderField("Content-Type"));
			response.setHeader("Accept-Ranges","bytes");
			byte[] buffer = new byte[1024];
			int len = 0;
			while ((len = in.read(buffer)) != -1) {
				outputStream.write(buffer, 0, len);
				outputStream.flush();
			}

			//response.flushBuffer();
		}catch (IOException e){
			e.printStackTrace();
		}catch (Exception e){
			e.printStackTrace();
		}finally {
			try {
				if(in!=null){
					in.close();
				}

				if(outputStream!=null){
					outputStream.close();
				}
			}catch (Exception e){
				e.printStackTrace();
			}
		}
		renderNull();
	}

	public void delPersOrgEmployeeFile(){
		String id=getPara("id");
		PersOrgEmployeeFile file=new PersOrgEmployeeFile();
		file.setId(id);
		file.setDelFlag("1");
		file.setUpdateBy(AuthUtils.getUserId());
		file.setUpdateDate(new Date());

		if(file.update()){
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg","操作失败"));
		}
	}

	public void createWiYeUser(){
		String eId=getPara("eId");
		PersOrgEmployee employee=persOrgEmployeeService.findById(eId);
		boolean flag=false;
		if(employee!=null){

			//PersOrg org=persOrgService.findById(employee.getOrgId());
			List<Integer> ids=Db.query("select qiye_id from pers_org where id in( " +
					"select relationship_id from pers_org_employee_rel where emp_id=? and relationship_type='dept' )",employee.getId());
			if(ids.size()==0){
				renderCodeFailed("获取员工所在部门企业微信id为空");
				return;
			}
			int[] ints=new int[ids.size()];
			for(int i=0;i<ids.size();i++){
				ints[i]=ids.get(i);
			}
			QiYeUser qiYeUser=new QiYeUser();
			qiYeUser.setUserId(employee.getWorkNum());
			if("male".equals(employee.getSex())){
				qiYeUser.setGender("1");
			}else if("female".equals(employee.getSex())){
				qiYeUser.setGender("2");
			}
            qiYeUser.setEmail(employee.getEmailAddr());
			qiYeUser.setName(employee.getFullName());
			qiYeUser.setMobile(employee.getPhoneNum());
			qiYeUser.setDepartment(ints);
			flag=qiYeWeiXinService.createUser(qiYeUser);
		}
		if(flag){
			employee.setQiyeUserid(employee.getWorkNum());
			employee.setUpdateDate(new Date());
			employee.update();
			renderCodeSuccess("success");
		}else{
			renderCodeFailed("error");
		}
	}

	public void updateWiYeUser(){
		String eId=getPara("eId");
		String newUserId=getPara("newUserId");
		PersOrgEmployee employee=persOrgEmployeeService.findById(eId);
		boolean flag=false;
		if(employee!=null){

			//PersOrg org=persOrgService.findById(employee.getOrgId());
			List<Integer> ids=Db.query("select qiye_id from pers_org where id in( " +
					"select relationship_id from pers_org_employee_rel where emp_id=? and relationship_type='dept' )",employee.getId());
			if(ids.size()==0){
				renderCodeFailed("获取员工所在部门企业微信id为空");
				return;
			}
			int[] ints=new int[ids.size()];
			for(int i=0;i<ids.size();i++){
				ints[i]=ids.get(i);
			}
			QiYeUser qiYeUser=new QiYeUser();
			qiYeUser.setUserId(employee.getWorkNum());
			if("male".equals(employee.getSex())){
				qiYeUser.setGender("1");
			}else if("female".equals(employee.getSex())){
				qiYeUser.setGender("2");
			}
			qiYeUser.setEmail(employee.getEmailAddr());
			qiYeUser.setName(employee.getFullName());
			qiYeUser.setMobile(employee.getPhoneNum());
			qiYeUser.setDepartment(ints);
			if(StrKit.notBlank(newUserId)){
				qiYeUser.setNewUserId(employee.getWorkNum());
			}

			flag=qiYeWeiXinService.updateUser(qiYeUser);
		}
		if(flag){
			employee.setQiyeUserid(employee.getWorkNum());
			employee.setUpdateDate(new Date());
			employee.update();
			renderCodeSuccess("success");
		}else{
			renderCodeFailed("error");
		}
	}

	public void deleteWiYeUser(){
		String userId=getPara("userId");
		boolean flag=false;

		flag=qiYeWeiXinService.delUser(userId);

		if(flag){
			String id=Db.queryStr("select id from pers_org_employee where qiye_userid=? ",userId);
			if(StrKit.notBlank(id)){
				Db.update("update pers_org_employee set qiye_userid=NULL where id=? ",id);
			}
			renderCodeSuccess("success");
		}else{
			renderCodeFailed("error");
		}
	}

	public void delEmployeeRel(){
		String relDeptId=getPara("relDeptId");
		String relPositionId=getPara("relPositionId");
		String relRoleId=getPara("relRoleId");
		String empId=getPara("empId");
		if(StrKit.isBlank(relDeptId) || StrKit.isBlank(empId)){
			renderJson(Ret.fail("msg","操作失败"));
			return;
		}
		String sql="select * from pers_org_employee_rel where emp_id=? and id<>? and relationship_type='dept' ";
		if(Db.findFirst(sql,empId,relDeptId)==null){
			renderJson(Ret.fail("msg","至少保留一条部门数据，删除失败"));
			return;
		}

		boolean flag=persOrgEmployeeRelService.deleteRel(empId,relDeptId,relPositionId,relRoleId,AuthUtils.getUserId());
		if(flag){
			PersOrgEmployee orgEmployee=persOrgEmployeeService.findById(empId);
			orgEmployee.setUpdateBy(AuthUtils.getUserId());
			if("quit".equals(orgEmployee.getArchiveStatus()) || "retire".equals(orgEmployee.getArchiveStatus())){

				List<Integer> ids=Db.query("select b.qiye_id from pers_org_employee_rel a " +
						"inner join pers_org b on a.relationship_id=b.id " +
						"where emp_id=? and relationship_type='dept'",orgEmployee.getId());


				if(ids.size()>0){
					int[] qiyeIds=new int[ids.size()];
					//ids.toArray(qiyeIds);
					for (int i = 0; i < ids.size(); i++) {
						qiyeIds[i]=ids.get(i);
					}

					String positionName="";

					List<String> positionList=Db.query("select position_name from pers_org_employee_rel a " +
							"inner join pers_position b on a.relationship_id=b.id " +
							"where emp_id=? and relationship_type='position' ",empId);
					for (String s : positionList) {
						positionName+=s+"、";
					}
					if(positionName.length()>0){
						positionName=positionName.substring(0,positionName.length()-1);
					}
					userService.updateUser(orgEmployee,orgEmployee.getUpdateBy());

					if(qiyeIds!=null){
						//更新企业微信账号
						QiYeUser qiYeUser=new QiYeUser();
						qiYeUser.setUserId(orgEmployee.getQiyeUserid());
						qiYeUser.setName(orgEmployee.getFullName());
						if("male".equals(orgEmployee.getSex())){
							qiYeUser.setGender("1");
						}else if("female".equals(orgEmployee.getSex())){
							qiYeUser.setGender("2");
						}
						qiYeUser.setMobile(orgEmployee.getPhoneNum());
						qiYeUser.setDepartment(qiyeIds);
						if(StrKit.notBlank(positionName)){
							qiYeUser.setPosition(positionName);
						}
						if(StrKit.notBlank(orgEmployee.getEmailAddr())){
							qiYeUser.setEmail(orgEmployee.getEmailAddr());
						}

						if(StrKit.isBlank(qiYeWeiXinService.getUser(qiYeUser.getUserId()))){
							qiYeUser.setUserId(orgEmployee.getWorkNum());
							boolean createQiYeUserflag=qiYeWeiXinService.createUser(qiYeUser);
							if(createQiYeUserflag){
								PersOrgEmployee updateQiYeIdEmployee=new PersOrgEmployee();
								updateQiYeIdEmployee.setId(orgEmployee.getId());
								updateQiYeIdEmployee.setQiyeUserid(qiYeUser.getUserId());
								updateQiYeIdEmployee.setUpdateDate(new Date());
								updateQiYeIdEmployee.update();
							}

						}else{
							qiYeWeiXinService.updateUser(qiYeUser);
						}

					}
				}


			}
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg","操作失败"));
		}
	}

	public void checkWeiXinEmp(){
		int page=getParaToInt("page");
		int limit=getParaToInt("limit");
		Page<Record> recordPage=Db.paginate(page,limit,"select * ","from pers_org_employee where archive_status='incumbency' and del_flag='0'");
		List<Record> recordList=recordPage.getList();
		List<String> userIdIsNull=new ArrayList<>();
		String token=qiYeWeiXinService.getAccessToken();
		for(Record record:recordList){
			String userId=record.getStr("qiye_userid");

			if(StrKit.isBlank(userId)){
				userIdIsNull.add(record.getStr("id"));
				return;
			}
			String resultStr=HttpClientsUtils.get("https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token="+token+"&userid="+userId);
			JSONObject jsonObject=JSON.parseObject(resultStr);
			if(jsonObject.getIntValue("errcode")==0){
				
			}else{
				userIdIsNull.add(record.getStr("id"));
			}
		}
		renderJson(userIdIsNull);
	}

	public void delDept(){
		String id=getPara("id");
		String accessToken=qiYeWeiXinService.getAccessToken();
		if(StrKit.isBlank(accessToken)){
			renderJson("error");
			return;
		}
		String url="https://qyapi.weixin.qq.com/cgi-bin/department/delete?access_token="+accessToken+"&id="+id;
		String resultData= HttpKit.post(url,null);
		renderJson(resultData);
	}

	public void updateUserId(){
		String userId=getPara("userId");
		String newUserId=getPara("newUserId");
		String accessToken=qiYeWeiXinService.getAccessToken();
		if(StrKit.isBlank(accessToken)){
			renderJson("error");
			return;
		}
		String url="https://qyapi.weixin.qq.com/cgi-bin/user/update?access_token="+accessToken;
		String dataStr="{\"userid\":\""+userId+"\",\"new_userid\":\""+newUserId+"\"}";

		String resultData= HttpKit.post(url,dataStr);
		renderJson(resultData);
	}

	public void yukong(){
		String sql="select * from pers_org_employee where del_flag='0' and archive_status='incumbency'  ";

		List<Record> employeeList=Db.find(sql);

		List<String> params=new ArrayList<>();
		for(Record record:employeeList){

			try {
				List<PersOrgEmployeeRel> relList=persOrgEmployeeRelService.getRelList(record.getStr("id"),"dept");
				if(relList==null || relList.size()==0){
					continue;
				}
				//添加域控
				PersOrg org=persOrgService.findById(relList.get(0).getRelationshipId());

				String orgIdStr=org.getParentIds()+org.getId();
				String[] orgIdArray=null;
				if(orgIdStr.indexOf(",")==-1){
					orgIdArray=new String[]{orgIdStr};
				}else{
					orgIdArray=orgIdStr.split(",");
				}
				if(orgIdArray!=null && orgIdStr.length()>0){
					System.out.println(orgIdStr);
					String str="";
					List<String> ids=new ArrayList<>();
					for(String id:orgIdArray){
						str+="?,";
						ids.add(id);
					}
					ids.addAll(ids);
					str=str.substring(0,str.length()-1);
					String sql2="select org_name from pers_org where id in ("+str+") ORDER BY FIELD(`id`,"+str+") ";
					List<Record> orgList=Db.find(sql2,ids.toArray());
					String orgNames="";
					for(Record record2:orgList){
						orgNames+=record2.getStr("org_name")+",";
					}
					orgNames=orgNames.substring(0,orgNames.length()-1);

					User saveUser =userService.getUserByEmpId(record.getStr("id"));

					PersOrgEmployeeDomainSyncRecord domainSyncRecord=new PersOrgEmployeeDomainSyncRecord();
					domainSyncRecord.setUserId(saveUser.getId());
					domainSyncRecord.setUserName(saveUser.getUserName());
					domainSyncRecord.setFullName(saveUser.getName());
					domainSyncRecord.setOrgNames(orgNames);
					domainSyncRecord.setType("add");
					persOrgEmployeeDomainSyncRecordService.saveEmployeeDomainSyncRecord(domainSyncRecord,null);
				}
			}catch (Exception e){
				params.add(record.getStr("id"));
				e.printStackTrace();
			}


		}

		renderJson(params);

	}

	public void employeeWorkAgeIndex(){
		setAttr("maxMonth",DateUtils.formatDate(new Date(),"yyyy-MM"));
		render("employeeWorkAgeIndex.html");
	}

	public void employeeWorkAgeList() throws Exception{
		String worgAgeType=getPara("type");
		String yearMonth=getPara("yearMonth");
		String isFilterBranchOffice=getPara("isFilterBranchOffice");
		String rangeType=getPara("rangeType");
		String rangeValue=getPara("rangeValue");
		if(StrKit.isBlank(yearMonth)){
			yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
		}

		PersOrgEmployee employee=getBean(PersOrgEmployee.class,"",true);
		employee.setArchiveStatus("incumbency");
		Page<PersOrgEmployee> orgEmployeePage = persOrgEmployeeService
				.paginateByCondition(employee,AuthUtils.getUserId(),"2","1"
				,worgAgeType,yearMonth,null,null,isFilterBranchOffice,rangeType,rangeValue, getParaToInt("page", 1), getParaToInt("limit", 10));
		if(orgEmployeePage!=null && orgEmployeePage.getList()!=null){
			Date date=DateUtils.parseDate(yearMonth+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(yearMonth+"-01")));
			for (PersOrgEmployee orgEmployee : orgEmployeePage.getList()) {

				int workAge=DateUtils.getAge(orgEmployee.getEntryTime(),date);
				orgEmployee.setWorkAge(workAge);
			}
		}
		renderJson(new DataTable<PersOrgEmployee>(orgEmployeePage));
	}


	public void exportEmployeeWorkAgeList(){
		String worgAgeType=getPara("type");
		String yearMonth=getPara("yearMonth");
		String isFilterBranchOffice=getPara("isFilterBranchOffice");
		String rangeType=getPara("rangeType");
		String rangeValue=getPara("rangeValue");
		try {
			if(StrKit.isBlank(yearMonth)){
				yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
			}
			PersOrgEmployee employee=getBean(PersOrgEmployee.class,"",true);
			employee.setArchiveStatus("incumbency");
			Page<PersOrgEmployee> orgEmployeePage = persOrgEmployeeService.paginateByCondition(employee,AuthUtils.getUserId(),"2","1"
					,worgAgeType,yearMonth,null,null,isFilterBranchOffice,rangeType,rangeValue, 1, 5000);

			String deptName="";
			if(StrKit.notBlank(employee.getDeptId())){
				deptName=persOrgService.getOrgParentNames(employee.getDeptId());
			}
			String fileName="";
			if(StrKit.notBlank(deptName)){
				fileName=deptName+"员工"+DateUtils.formatDate(new Date(),"yyyy-MM")+"月份"+"工龄统计表.xlsx";
			}else{
				fileName="员工"+DateUtils.formatDate(new Date(),"yyyy-MM")+"月份"+"工龄统计表.xlsx";
			}
			InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\gongling.xlsx");
			XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
			inputStream.close();
			XSSFSheet sheet = workbook.getSheetAt(0);

			sheet.getRow(1).getCell(0).setCellValue(fileName.replace(".xlsx",""));
			if(orgEmployeePage!=null && orgEmployeePage.getList()!=null && orgEmployeePage.getList().size()>0){

				List<PersOrgEmployee> recordList=orgEmployeePage.getList();
				if(recordList.size()>1){
					//创建行数
					PersOrgEmployeeCheckinController.insertRow(workbook,sheet,3,recordList.size()-1);
				}
				Date date=DateUtils.parseDate(yearMonth+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(yearMonth+"-01")));
				for (int i = 0; i < recordList.size(); i++) {
					PersOrgEmployee orgEmployee=recordList.get(i);


					int workAge=DateUtils.getAge(orgEmployee.getEntryTime(),date);
					orgEmployee.setWorkAge(workAge);

					XSSFRow row=sheet.getRow(i+3);
					row.getCell(0).setCellValue(Integer.valueOf(i+1).toString());
					row.getCell(1).setCellValue(orgEmployee.getOrg().getStr("orgName"));
					row.getCell(2).setCellValue(orgEmployee.getOrg().getStr("positionName"));
					row.getCell(3).setCellValue(orgEmployee.getFullName());
					row.getCell(4).setCellValue(DateUtils.formatDate(orgEmployee.getEntryTime(),"yyyy-MM-dd"));
					row.getCell(5).setCellValue(Integer.valueOf(orgEmployee.getWorkAge()).toString());
					if(orgEmployee.getOrg()!=null && "branche_office".equals(orgEmployee.getOrg().getStr("orgType"))){

					}else{
						row.getCell(6).setCellValue(Integer.valueOf(orgEmployee.getWorkAge()*50).toString());
					}

				}
			}
			ByteArrayOutputStream os = new ByteArrayOutputStream();
			workbook.write(os);
			render(new StreamRender(fileName, os));
			workbook.close();
			os.close();
		}catch (Exception e){
			e.printStackTrace();
		}


	}

	public void getEmpById(){
		String id=getPara("id");

		renderJson(Ret.ok("data",persOrgEmployeeService.findById(id)));
	}

	public void empSocialSecurityIndex(){

		render("empSocialSecurityIndex.html");
	}

	/*public static void main(String[] args) throws Exception {
		//Workbook workbook = new Workbook("Book1.xlsx");
		//workbook.save("Excel-to-PDF.pdf", SaveFormat.PDF);


		InputStream inputStream=new FileInputStream("D:\\waipai.xlsx");
		XSSFWorkbook workbook = new XSSFWorkbook(inputStream);

		inputStream.close();
		XSSFSheet sheet = workbook.getSheetAt(0);
		//所在单位
		sheet.getRow(2).getCell(1).setCellValue("xxxxxxxxxxxx");
		//派驻单位
		sheet.getRow(2).getCell(3).setCellValue("xxxxxxxxxxxx");
		//填单时间
		sheet.getRow(2).getCell(5).setCellValue("xxxxxxxxxxxx");
		//部门
		sheet.getRow(3).getCell(1).setCellValue("xxxxxxxxxxxx");
		//姓名
		sheet.getRow(3).getCell(3).setCellValue("xxxxxxxxxxxx");
		//职位
		sheet.getRow(3).getCell(5).setCellValue("xxxxxxxxxxxx");

		ByteArrayOutputStream os = new ByteArrayOutputStream();
		workbook.write(os);


		byte[] dataByte=os.toByteArray();

		File file=new File(UUID.randomUUID().toString()+".xlsx");
		FileOutputStream fileOutputStream = new FileOutputStream(file);
		fileOutputStream.write(dataByte);

		workbook.close();
		os.close();

		Workbook wb = new Workbook();
		wb.loadFromFile(file.getCanonicalPath());
		wb.saveToFile("D:\\ToPDF.pdf", FileFormat.PDF);


	}*/

}
