package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainAreaService;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.api.weixin.QiYeWeiXinService;
import com.cszn.integrated.service.entity.enums.PersLeaveRestType;
import com.cszn.integrated.service.entity.enums.PersOverTimeType;
import com.cszn.integrated.service.entity.main.MainArea;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.provider.pers.PersOrgEmployeeCheckinRuleServiceImpl;
import com.cszn.integrated.service.provider.pers.PersOrgEmployeeLeaveRestApplyServiceImpl;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.*;

@RequestMapping(value="/employeeCheckin", viewPath="/modules_page/pers/checkin")
public class PersOrgEmployeeCheckinController extends BaseController {

    @Inject
    private PersOrgEmployeeCheckinRecordService persOrgEmployeeCheckinRecordService;
    @Inject
    private QiYeWeiXinService qiYeWeiXinService;
    @Inject
    private PersOrgEmployeeCheckinDaySummaryService persOrgEmployeeCheckinDaySummaryService;
    @Inject
    private PersTaskService persTaskService;
    @Inject
    private PersApprovalService persApprovalService;
    @Inject
    private PersOrgEmployeeCheckinFillCardService persOrgEmployeeCheckinFillCardService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersOrgEmployeeCheckinRuleService persOrgEmployeeCheckinRuleService;
    @Inject
    private PersOrgEmployeeCheckinWxRecordService persOrgEmployeeCheckinWxRecordService;
    @Inject
    private PersOrgEmployeeCheckinMonthSummaryService persOrgEmployeeCheckinMonthSummaryService;
    @Inject
    private PersOrgEmployeeCheckinExceptionService persOrgEmployeeCheckinExceptionService;
    @Inject
    private PersOrgEmployeeCheckinItemsService persOrgEmployeeCheckinItemsService;
    @Inject
    private PersOrgEmployeeEntryInfoService persOrgEmployeeEntryInfoService;
    @Inject
    private PersOrgEmployeeEducationalBackgroundService persOrgEmployeeEducationalBackgroundService;
    @Inject
    private PersOrgEmployeeCheckinMonthExamineService persOrgEmployeeCheckinMonthExamineService;
    @Inject
    private PersOrgEmployeeCheckinDaySummaryExamineService persOrgEmployeeCheckinDaySummaryExamineService;
    @Inject
    private PersOrgEmployeeOverTimeApplyService persOrgEmployeeOverTimeApplyService;
    @Inject
    private PersOrgEmployeeLeaveRestApplyService persOrgEmployeeLeaveRestApplyService;
    @Inject
    private PersOrgEmployeeBusinessTripApplyService persOrgEmployeeBusinessTripApplyService;
    @Inject
    private MainAreaService mainAreaService;

    Logger logger= LoggerFactory.getLogger(PersOrgEmployeeCheckinController.class);

    public void index(){
        String employeeId=getPara("employeeId");
        setAttr("employeeId",employeeId);
        render("index.html");
    }

    public void pageTable(){
        PersOrgEmployeeCheckinRecord checkinRecord=getBean(PersOrgEmployeeCheckinRecord.class,"",true);
        if(checkinRecord.getCheckinTime()==null){
            checkinRecord.setCheckinTime(new Date());
        }

        Page<Record> page=persOrgEmployeeCheckinRecordService.pageList(getParaToInt("page"),getParaToInt("limit"),checkinRecord);

        renderJson(new DataTable<Record>(page));
    }

    public void getList(){

        renderJson(persOrgEmployeeCheckinRecordService.findAll());
    }


    public void data(){
        String userId=getPara("userId");
        long startTime=getParaToLong("startTime");
        long endTime=getParaToLong("endTime");

        String str=qiYeWeiXinService.getCheckinData(new Object[]{userId},startTime,endTime);
        renderJson(str);
    }

    public void day(){
        String userId=getPara("userId");
        long startTime=getParaToLong("startTime");
        long endTime=getParaToLong("endTime");

        String str=qiYeWeiXinService.getCheckinDayData(new Object[]{userId},startTime,endTime);
        renderJson(str);
    }

    public void month(){
        String userId=getPara("userId");
        long startTime=getParaToLong("startTime");
        long endTime=getParaToLong("endTime");

        String str=qiYeWeiXinService.getCheckinMonthData(new Object[]{userId},startTime,endTime);
        renderJson(str);
    }


    public void reportFormIndex(){
        if("01".equals(DateUtils.getDate("dd"))){
            String startDateStr= DateUtils.getLastMonth()+"-01";
            String endDateStr= DateUtils.getLastMonth()+"-"+DateUtils.getLastMaxMonthDay();
            setAttr("startDateStr",startDateStr);
            setAttr("endDateStr",endDateStr);
            //setAttr("month",startDateStr);
        }else{
            String startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            String endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
            setAttr("startDateStr",startDateStr);
            setAttr("endDateStr",endDateStr);
            setAttr("month",DateUtils.getDate("yyyy-MM"));
        }

        render("reportForm.html");
    }

    public void reportFormIndex2(){
        if("01".equals(DateUtils.getDate("dd"))){
            String startDateStr= DateUtils.getLastMonth()+"-01";
            String endDateStr= DateUtils.getLastMonth()+"-"+DateUtils.getLastMaxMonthDay();
            setAttr("startDateStr",startDateStr);
            setAttr("endDateStr",endDateStr);
            //setAttr("month",startDateStr);
        }else{
            String startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            String endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
            setAttr("startDateStr",startDateStr);
            setAttr("endDateStr",endDateStr);
            //setAttr("month",DateUtils.getDate("yyyy-MM"));
        }
        setAttr("user", AuthUtils.getLoginUser());

        render("reportForm2.html");
    }

    public void empCheckinDaySummaryPage(){
        int page=getParaToInt("page");
        int limit=getParaToInt("limit");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr= dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            if("01".equals(DateUtils.getDate("dd"))){
                startDateStr= DateUtils.getLastMonth()+"-01";
                endDateStr= DateUtils.getLastMonth()+"-"+DateUtils.getLastMaxMonthDay();
            }else{
                startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
                endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
            }

        }
        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }
        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList2(page,limit,null,fullName,startDateStr,endDateStr,archiveStatus,ids,AuthUtils.getUserId());
        renderJson(new DataTable<Record>(pageList));
    }
    public void newEmpCheckinDaySummaryPage(){
        int page=getParaToInt("page");
        int limit=getParaToInt("limit");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr= dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            if("01".equals(DateUtils.getDate("dd"))){
                startDateStr= DateUtils.getLastMonth()+"-01";
                endDateStr= DateUtils.getLastMonth()+"-"+DateUtils.getLastMaxMonthDay();
            }else{
                startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
                endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
            }

        }
        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }
        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageListNew(page,limit,null,fullName,startDateStr,endDateStr,archiveStatus,ids,AuthUtils.getUserId(),null);
        renderJson(new DataTable<Record>(pageList));
    }


    public void empCheckinDaySummaryExport(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList(1,10000,null,fullName,startDateStr,endDateStr,archiveStatus,ids);
        List<Record> recordList=pageList.getList();

        Date startDate=DateUtils.parseDate(startDateStr);
        Date endDate=DateUtils.parseDate(endDateStr);
        List<Date> dateList=DateUtils.getBetweenDates(startDate,endDate);

        List<String> titleList=new ArrayList<>();
        titleList.add("部门");
        titleList.add("姓名");
        titleList.add("缺勤天数（未打上班和未打下班卡出勤天数）");
        titleList.add("实出勤天数（正常出勤天数+迟到、早退、漏卡出勤天数）");
        titleList.add("休息天数");
        titleList.add("实际工作时长");
        titleList.add("标准工作时长");
        titleList.add("迟到");
        titleList.add("早退");
        titleList.add("旷工");
        titleList.add("缺卡");
        titleList.add("补卡成功");
        titleList.add("地点异常");
        titleList.add("设备异常");

        for(Date date:dateList){
            DateUtils.getWeek();
            titleList.add(DateUtils.formatDate(date,"MM-dd ")+DateUtils.formatDate(date,"E"));
        }
        String[] title=new String[titleList.size()];
        titleList.toArray(title);
        String fileName="员工考勤"+startDateStr+"至"+endDateStr+"汇总.xls";
        String sheetName = "员工考勤汇总";

        String[][] content=new String[recordList.size()][title.length];
        /*content[0][0]="缺勤天数：未打上班和未打下班卡出勤天数\n实出勤天数：正常出勤天数+迟到、早退、漏卡出勤天数";*/
        if(recordList!=null && recordList.size()>0){
            for(int i=0;i<recordList.size();i++){
                Record record=recordList.get(i);
                String empId=record.getStr("id");

                content[i][0]=record.getStr("orgName");
                content[i][1]=record.getStr("fullName");
                content[i][2]=record.getStr("exceptionCount");
                content[i][3]=record.getStr("normalCount");
                content[i][4]=record.getStr("restCount");
                if(record.getDouble("regularWorkSecStr")==null){
                    content[i][5]="0小时";
                }else{
                    content[i][5]=record.getDouble("regularWorkSecStr")+"小时";
                }

                if(record.getDouble("standardWorkSecStr")==null){
                    content[i][6]="0小时";
                }else{
                    content[i][6]=record.getDouble("standardWorkSecStr")+"小时";
                }
                String exception_1="";
                if(record.getInt("exception_1")==null){
                    exception_1="0次";
                }else{
                    exception_1=record.getInt("exception_1")+"次";
                    if(record.getInt("exception_1_duration")!=null){
                        exception_1+=record.getInt("exception_1_duration")/60+"分钟";
                    }
                }
                content[i][7]=exception_1;
                String exception_2="";
                if(record.getInt("exception_2")==null){
                    exception_2="0次";
                }else{
                    exception_2=record.getInt("exception_2")+"次";
                    if(record.getInt("exception_2_duration")!=null){
                        exception_2+=record.getInt("exception_2_duration")/60+"分钟";
                    }
                }
                content[i][8]=exception_2;
                String exception_4="";
                if(record.getInt("exception_4")==null){
                    exception_4="0次";
                }else{
                    exception_4=record.getInt("exception_4")+"次";
                    if(record.getInt("exception_4_duration")!=null){
                        exception_4+=record.getInt("exception_4_duration")/60+"分钟";
                    }
                }
                content[i][9]=record.getStr("exception_4");

                if(record.getInt("exception_3")==null){
                    content[i][10]="0次";
                }else{
                    content[i][10]=record.getInt("exception_3")+"次";
                }
                if(record.getInt("fillCardCount")==null){
                    content[i][11]="0次";
                }else{
                    content[i][11]=record.getInt("fillCardCount")+"次";
                }

                if(record.getInt("exception_5")==null){
                    content[i][12]="0次";
                }else{
                    content[i][12]=record.getInt("exception_5")+"次";
                }
                if(record.getInt("exception_6")==null){
                    content[i][13]="0次";
                }else{
                    content[i][13]=record.getInt("exception_6")+"次";
                }

                for(int j=0;j<dateList.size();j++){
                    PersOrgEmployeeCheckinDaySummary daySummary=persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId,dateList.get(j));
                    List<Record> checkinRecordList=persOrgEmployeeCheckinRecordService.getRecordListByDate(empId,dateList.get(j));
                    if(daySummary!=null && 1==daySummary.getDayType()){
                        content[i][14+j]="休息";
                    }else{
                        if(checkinRecordList!=null && checkinRecordList.size()>0){
                            String str="";
                            for(Record checkinRecord : checkinRecordList){

                                String type="";
                                if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="上：";
                                }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="下：";
                                }
                                String fillCardStatus="";
                                if("2".equals(checkinRecord.getStr("status"))){
                                    fillCardStatus="(补卡成功)";
                                }
                                if(StrKit.notBlank(checkinRecord.getStr("exceptionType"))){
                                    if("未打卡".equals(checkinRecord.getStr("exceptionType"))){
                                        str+=type+"未打卡\n";
                                    }else{
                                        String timeException="";
                                        if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算迟到
                                            int minute= PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("checkinTime"),checkinRecord.getDate("schCheckinTime"));
                                            if(minute>0){
                                                timeException="迟到:"+(minute)+"分钟";
                                            }
                                        }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算早退
                                            int minute=PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("schCheckinTime"),checkinRecord.getDate("checkinTime"));
                                            if(minute>0){
                                                timeException="早退:"+(minute)+"分钟";
                                            }
                                        }
                                        str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+"("+timeException+")"+fillCardStatus+"\n";
                                    }
                                }else{
                                    str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n";
                                }
                            }
                            content[i][14+j]=str;
                        }
                    }
                }
            }
        }

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        HSSFWorkbook wb  = new HSSFWorkbook();

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();



        style.setWrapText(true);

        //声明列对象
        HSSFCell cell = null;

        HSSFFont redFont = wb.createFont();
        redFont.setFontHeightInPoints((short) 16);

        style.setFont(redFont);

        //创建标题
        for(int i=0;i<title.length;i++){
            cell = row.createCell(i);
            cell.setCellStyle(style);
            cell.setCellValue(new HSSFRichTextString(title[i]));
        }




        //创建内容
        for(int i=0;i<content.length;i++){

            row = sheet.createRow(i + 1);
            for(int j=0;j<content[i].length;j++){
                if(i==content.length-1){
                    if(j==0){
                        sheet.setColumnWidth(j,60*256);
                    }else if(j>1 && j<14){
                        sheet.setColumnWidth(j,20*256);
                    }else{
                        sheet.setColumnWidth(j,35*256);
                    }
                }

                String txt=content[i][j];
                if( StrKit.isBlank(txt)|| txt.indexOf("未打卡")==-1){
                    //将内容按顺序赋给对应的列对象
                    HSSFCell rowCell=row.createCell(j);
                    rowCell.setCellStyle(style);
                    rowCell.setCellValue(new HSSFRichTextString(txt));
                }else{
                    HSSFRichTextString text = new HSSFRichTextString(txt);

                    text.applyFont(txt.indexOf("未打卡"),txt.indexOf("未打卡")+3,redFont);
                    HSSFCell rowCell=row.createCell(j);
                    rowCell.setCellStyle(style);
                    //将内容按顺序赋给对应的列对象
                    rowCell.setCellValue(new HSSFRichTextString(text.getString()));
                }

            }
        }

        //style.setAlignment(HSSFCellStyle);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
            wb.close();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    private static String secondToTime(long second){
        long days = second / 86400;            //转换天数
        second = second % 86400;            //剩余秒数
        long hours = second / 3600;            //转换小时
        second = second % 3600;                //剩余秒数
        long minutes = second /60;            //转换分钟
        second = second % 60;                //剩余秒数
        if(days>0){
            return (days*24) + hours + "小时" + minutes + "分";
        }else if(hours>0){
            return hours + "小时" + minutes + "分";
        }else{
            return minutes + "分";
        }
    }

    public void empCheckinDaySummaryExportCheck(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
           // startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList2(1,10000,null,fullName,startDateStr,endDateStr,archiveStatus,ids,AuthUtils.getUserId());
        List<Record> recordList=pageList.getList();

        String msg="";
        if(recordList!=null && recordList.size()>0){
            List<Object> examineParams=new ArrayList<>();
            String examineStr="";
            for(int i=0;i<recordList.size();i++){
                String empId=recordList.get(i).getStr("id");
                examineStr+="?,";
                examineParams.add(empId);
            }
            examineStr=examineStr.substring(0,examineStr.length()-1);

            examineParams.add(dateRange);
            for(Record record:recordList){
                Map<String,Object> dayTypeMap=record.get("dayTypeMap");
                Set<String> keys=dayTypeMap.keySet();

                List<PersOrgEmployeeCheckinDaySummaryExamine> daySummaryExamineList=persOrgEmployeeCheckinDaySummaryExamineService.findEmpExamineList(record.getStr("id"),"3",startDateStr,endDateStr);
                Map<String,PersOrgEmployeeCheckinDaySummaryExamine> empDayExamineMap=new HashMap<>();
                for(PersOrgEmployeeCheckinDaySummaryExamine examine:daySummaryExamineList){
                    empDayExamineMap.put(DateUtils.formatDate(examine.getDayDate(),"yyyy-MM-dd"),examine);
                }
                for(String key:keys){
                    PersOrgEmployeeCheckinDaySummaryExamine examine=empDayExamineMap.get(key);
                    if(examine==null){
                        renderJson(Ret.fail("msg","导出失败，存在未审核完成的记录"));
                        return;
                    }
                }
            }
        }else{
            renderJson(Ret.fail("msg","导出的数据条数为0"));
            return;
        }
        renderJson(Ret.ok("msg","操作成功"));
    }

    public void empCheckinDaySummaryExport2(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList2(1,10000,null,fullName,startDateStr,endDateStr,archiveStatus,ids,AuthUtils.getUserId());
        List<Record> recordList=pageList.getList();

        Date startDate=DateUtils.parseDate(startDateStr);
        Date endDate=DateUtils.parseDate(endDateStr);
        List<Date> dateList=DateUtils.getBetweenDates(startDate,endDate);

        List<String> titleList=new ArrayList<>();
        titleList.add("部门");
        titleList.add("姓名");
        titleList.add("缺勤天数（未打上班和未打下班卡出勤天数）");
        titleList.add("实出勤天数（正常出勤天数+迟到、早退、漏卡出勤天数）");
        titleList.add("休息天数");
        titleList.add("全天加班");
        titleList.add("全天请休假");
        titleList.add("工作日加班");
        titleList.add("半天请休假");
        titleList.add("实际工作时长");
        titleList.add("标准工作时长");
        titleList.add("迟到");
        titleList.add("早退");
        titleList.add("旷工");
        titleList.add("缺卡");
        titleList.add("补卡成功");
        titleList.add("地点异常");
        titleList.add("设备异常");

        PersOverTimeType[] persOverTimeTypes=PersOverTimeType.values();
        PersLeaveRestType[] persLeaveRestTypes=PersLeaveRestType.values();

        for(PersOverTimeType overTimeType:persOverTimeTypes){
            titleList.add("全天加班："+overTimeType.getValue());
        }
        for(PersOverTimeType overTimeType:persOverTimeTypes){
            titleList.add("工作日加班："+overTimeType.getValue());
        }
        for(PersLeaveRestType leaveRestType:persLeaveRestTypes){
            titleList.add("全天请休假："+leaveRestType.getValue());
        }
        for(PersLeaveRestType leaveRestType:persLeaveRestTypes){
            titleList.add("半天请休假："+leaveRestType.getValue());
        }

        for(Date date:dateList){
            DateUtils.getWeek();
            titleList.add(DateUtils.formatDate(date,"MM-dd ")+DateUtils.formatDate(date,"E"));
        }
        String[] title=new String[titleList.size()];
        titleList.toArray(title);
        String fileName="员工考勤"+startDateStr+"至"+endDateStr+"汇总.xls";
        String sheetName = "员工考勤汇总";

        String[][] content=new String[recordList.size()][title.length];
        /*content[0][0]="缺勤天数：未打上班和未打下班卡出勤天数\n实出勤天数：正常出勤天数+迟到、早退、漏卡出勤天数";*/


        if(recordList!=null && recordList.size()>0){
            List<Object> params=new ArrayList<>();
            String s="";
            for(int i=0;i<recordList.size();i++){
                String empId=recordList.get(i).getStr("id");
                params.add(empId);
                s+="?,";
            }
            s=s.substring(0,s.length()-1);
            params.add(startDateStr+" 00:00:00");
            params.add(endDateStr+" 23:59:59");


            String daySummarySql=" select * from pers_org_employee_checkin_day_summary a where a.emp_id in ("+s+") and a.del_flag='0'" +
                    "and a.summary_date between ? and ? order by emp_id,summary_date";
            List<PersOrgEmployeeCheckinDaySummary> daySummaryList=persOrgEmployeeCheckinDaySummaryService.findBySql(daySummarySql,params.toArray());
            Map<String,List<PersOrgEmployeeCheckinDaySummary>> empDaySunnary=new HashMap<>();
            for(PersOrgEmployeeCheckinDaySummary daySummary:daySummaryList){
                if(empDaySunnary.containsKey(daySummary.getEmpId())){
                    List<PersOrgEmployeeCheckinDaySummary> summaryList=empDaySunnary.get(daySummary.getEmpId());
                    summaryList.add(daySummary);
                }else{
                    List<PersOrgEmployeeCheckinDaySummary> summaryList=new ArrayList<>();
                    summaryList.add(daySummary);
                    empDaySunnary.put(daySummary.getEmpId(),summaryList);
                }
            }

            String checkinRecordSql="select a.emp_id as empId,a.id,a.checkin_type as checkinType,a.exception_type as exceptionType ,a.checkin_time as checkinTime,a.sch_checkin_time as schCheckinTime,b.`status` " +
                    "from pers_org_employee_checkin_record a left join   pers_org_employee_checkin_fill_card b on a.id=b.checkin_record_id and b.`status`='2' " +
                    "where a.emp_id in ("+s+") and a.checkin_time between ? and ? and a.del_flag='0' order by a.emp_id,a.checkin_time ";
            List<Record> allCheckinRecordList=Db.find(checkinRecordSql,params.toArray());
            Map<String,List<Record>> empCheckinMap=new HashMap<>();
            for(Record record:allCheckinRecordList){
                if(empCheckinMap.containsKey(record.getStr("empId"))){
                    List<Record> records=empCheckinMap.get(record.getStr("empId"));
                    records.add(record);
                }else{
                    List<Record> records=new ArrayList<>();
                    records.add(record);
                    empCheckinMap.put(record.getStr("empId"),records);
                }
            }

            for(int i=0;i<recordList.size();i++){
                Record record=recordList.get(i);
                String empId=record.getStr("id");

                content[i][0]=record.getStr("orgName");
                content[i][1]=record.getStr("fullName");
                content[i][2]=record.getStr("exceptionCount");
                content[i][3]=record.getStr("normalCount");
                content[i][4]=record.getStr("restCount");
                content[i][5]=record.getStr("overDays");
                content[i][6]=record.getStr("leavDays");
                if(record.getInt("halfOverCount")==null || record.getInt("halfOverCount")==0){
                    content[i][7]="";
                }else{
                    content[i][7]=record.getInt("halfOverCount")+"次;"+secondToTime(record.getLong("halfOversecond"));
                }
                if(record.getInt("halfLeavCount")==null || record.getInt("halfLeavCount")==0){
                    content[i][8]="";
                }else{
                    content[i][8]=record.getInt("halfLeavCount")+"次";
                }


                if(record.getDouble("regularWorkSecStr")==null){
                    content[i][9]="0小时";
                }else{
                    content[i][9]=record.getDouble("regularWorkSecStr")+"小时";
                }

                if(record.getDouble("standardWorkSecStr")==null){
                    content[i][10]="0小时";
                }else{
                    content[i][10]=record.getDouble("standardWorkSecStr")+"小时";
                }
                String exception_1="";
                if(record.getInt("exception_1")==null){
                    exception_1="0次";
                }else{
                    exception_1=record.getInt("exception_1")+"次";
                    if(record.getInt("exception_1_duration")!=null){
                        exception_1+=record.getInt("exception_1_duration")/60+"分钟";
                    }
                }
                content[i][11]=exception_1;
                String exception_2="";
                if(record.getInt("exception_2")==null){
                    exception_2="0次";
                }else{
                    exception_2=record.getInt("exception_2")+"次";
                    if(record.getInt("exception_2_duration")!=null){
                        exception_2+=record.getInt("exception_2_duration")/60+"分钟";
                    }
                }
                content[i][12]=exception_2;
                String exception_4="";
                if(record.getInt("exception_4")==null){
                    exception_4="0次";
                }else{
                    exception_4=record.getInt("exception_4")+"次";
                    if(record.getInt("exception_4_duration")!=null){
                        exception_4+=record.getInt("exception_4_duration")/60+"分钟";
                    }
                }
                content[i][13]=record.getStr("exception_4");

                if(record.getInt("exception_3")==null){
                    content[i][14]="0次";
                }else{
                    content[i][14]=record.getInt("exception_3")+"次";
                }
                if(record.getInt("fillCardCount")==null){
                    content[i][15]="0次";
                }else{
                    content[i][15]=record.getInt("fillCardCount")+"次";
                }

                if(record.getInt("exception_5")==null){
                    content[i][16]="0次";
                }else{
                    content[i][16]=record.getInt("exception_5")+"次";
                }
                if(record.getInt("exception_6")==null){
                    content[i][17]="0次";
                }else{
                    content[i][17]=record.getInt("exception_6")+"次";
                }
                int oi=0;
                for (PersOverTimeType overTimeType :persOverTimeTypes){
                    if(record.getInt("over_"+overTimeType.getKey()+"_days")!=null && record.getInt("over_"+overTimeType.getKey()+"_days")>0){
                        content[i][18+oi]=record.getInt("over_"+overTimeType.getKey()+"_days")+"天";
                    }else{
                        content[i][18+oi]="0";
                    }
                    oi++;
                }
                int oi2=0;
                for (PersOverTimeType overTimeType :persOverTimeTypes){
                    if(record.getInt("halfOver_"+overTimeType.getKey()+"_second")!=null && record.getInt("halfOver_"+overTimeType.getKey()+"_second")>0){
                        content[i][18+oi+oi2]=secondToTime(record.getLong("halfOver_"+overTimeType.getKey()+"_second"));
                    }else{
                        content[i][18+oi+oi2]="0";
                    }
                    oi2++;
                }
                int li=0;
                for (PersLeaveRestType leaveRestType :persLeaveRestTypes){
                    if(record.getInt("leave_"+leaveRestType.getKey()+"_days")!=null && record.getInt("leave_"+leaveRestType.getKey()+"_days")>0){
                        content[i][18+oi+oi2+li]=record.getInt("leave_"+leaveRestType.getKey()+"_days")+"天";
                    }else{
                        content[i][18+oi+oi2+li]="0";
                    }
                    li++;
                }
                int li2=0;
                for (PersLeaveRestType leaveRestType :persLeaveRestTypes){
                    if(record.getInt("halfLeave_"+leaveRestType.getKey()+"_days")!=null && record.getInt("halfLeave_"+leaveRestType.getKey()+"_days")>0){
                        content[i][18+oi+oi2+li+li2]=record.getInt("halfLeave_"+leaveRestType.getKey()+"_days")+"次";
                    }else{
                        content[i][18+oi+oi2+li+li2]="0";
                    }
                    li++;
                }



                for(int j=0;j<dateList.size();j++){
                    List<PersOrgEmployeeCheckinDaySummary> daySummaries=empDaySunnary.get(empId);
                    PersOrgEmployeeCheckinDaySummary daySummary=null;
                    for(PersOrgEmployeeCheckinDaySummary summary:daySummaries){
                        if(DateUtils.formatDate(dateList.get(j),"yyyy-MM-dd").equals(DateUtils.formatDate(summary.getSummaryDate(),"yyyy-MM-dd"))){
                            daySummary=summary;
                        }
                    }
                    List<Record> empAllCheckinRecordList=empCheckinMap.get(empId);
                    List<Record> checkinRecordList=new ArrayList<>();
                    if(empAllCheckinRecordList!=null){
                        for(Record checkinRecord:empAllCheckinRecordList){
                            if(DateUtils.formatDate(dateList.get(j),"yyyy-MM-dd").equals(DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"yyyy-MM-dd"))){
                                checkinRecordList.add(checkinRecord);
                            }
                        }
                    }

                    Map<String,String> typeMap=record.get("typeMap");
                    //persOrgEmployeeCheckinRecordService.getRecordListByDate(empId,dateList.get(j));

                    Map<String,Integer> dayTypeMap=record.get("dayTypeMap");
                    String dateStr=DateUtils.formatDate(dateList.get(j),"yyyy-MM-dd");
                    if(dayTypeMap.get(dateStr)!=null && 1==dayTypeMap.get(dateStr)) {
                        content[i][18+oi+oi2+li+li2 + j] = "休息";
                    }else if(dayTypeMap.get(dateStr)!=null && 3==dayTypeMap.get(dateStr)){
                        content[i][18+oi+oi2+li+li2+j]="全天请休假："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"));
                    }else{
                        if(checkinRecordList!=null && checkinRecordList.size()>0){
                            String str="";

                            if(dayTypeMap.get(dateStr)!=null && 2==dayTypeMap.get(dateStr)){
                                str+="全天加班："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"))+"\n";
                            }

                            for(Record checkinRecord : checkinRecordList){

                                String type="";
                                if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="上：";
                                }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="下：";
                                }
                                String fillCardStatus="";
                                if("2".equals(checkinRecord.getStr("status"))){
                                    fillCardStatus="(补卡成功)";
                                }
                                if(StrKit.notBlank(checkinRecord.getStr("exceptionType"))){
                                    if("未打卡".equals(checkinRecord.getStr("exceptionType"))){
                                        str+=type+"未打卡\n";
                                    }else{
                                        String timeException="";
                                        if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算迟到
                                            int minute= PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("checkinTime"),checkinRecord.getDate("schCheckinTime"));
                                            if(minute>0){
                                                timeException="迟到:"+(minute)+"分钟";
                                            }
                                        }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算早退
                                            int minute=PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("schCheckinTime"),checkinRecord.getDate("checkinTime"));
                                            if(minute>0){
                                                timeException="早退:"+(minute)+"分钟";
                                            }
                                        }
                                        str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+"("+timeException+")"+fillCardStatus+"\n";
                                    }
                                }else{
                                    str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n";
                                }
                            }
                            content[i][18+oi+oi2+li+li2+j]=str;
                        }
                    }
                    /*if(daySummary!=null && 1==daySummary.getDayType()) {
                        content[i][18 + j] = "休息";
                    }else if(daySummary!=null && 3==daySummary.getDayType()){
                        content[i][18+j]="请休假："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"));
                    }else{
                        if(checkinRecordList!=null && checkinRecordList.size()>0){
                            String str="";

                            if(daySummary!=null && 2==daySummary.getDayType()){
                                str+="加班："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"))+"\n";
                            }

                            for(Record checkinRecord : checkinRecordList){

                                String type="";
                                if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="上：";
                                }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="下：";
                                }
                                String fillCardStatus="";
                                if("2".equals(checkinRecord.getStr("status"))){
                                    fillCardStatus="(补卡成功)";
                                }
                                if(StrKit.notBlank(checkinRecord.getStr("exceptionType"))){
                                    if("未打卡".equals(checkinRecord.getStr("exceptionType"))){
                                        str+=type+"未打卡\n";
                                    }else{
                                        String timeException="";
                                        if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算迟到
                                            int minute= PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("checkinTime"),checkinRecord.getDate("schCheckinTime"));
                                            if(minute>0){
                                                timeException="迟到:"+(minute)+"分钟";
                                            }
                                        }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算早退
                                            int minute=PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("schCheckinTime"),checkinRecord.getDate("checkinTime"));
                                            if(minute>0){
                                                timeException="早退:"+(minute)+"分钟";
                                            }
                                        }
                                        str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+"("+timeException+")"+fillCardStatus+"\n";
                                    }
                                }else{
                                    str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n";
                                }
                            }
                            content[i][18+j]=str;
                        }
                    }*/
                }
            }
        }

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        HSSFWorkbook wb  = new HSSFWorkbook();

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();



        style.setWrapText(true);

        //声明列对象
        HSSFCell cell = null;

        HSSFFont redFont = wb.createFont();
        redFont.setFontHeightInPoints((short) 16);

        style.setFont(redFont);

        //创建标题
        for(int i=0;i<title.length;i++){
            cell = row.createCell(i);
            cell.setCellStyle(style);
            cell.setCellValue(new HSSFRichTextString(title[i]));
        }




        //创建内容
        for(int i=0;i<content.length;i++){

            row = sheet.createRow(i + 1);
            for(int j=0;j<content[i].length;j++){
                if(i==content.length-1){
                    if(j==0){
                        sheet.setColumnWidth(j,60*256);
                    }else if(j>1 && j<14){
                        sheet.setColumnWidth(j,20*256);
                    }else{
                        sheet.setColumnWidth(j,35*256);
                    }
                }

                String txt=content[i][j];
                if( StrKit.isBlank(txt)|| txt.indexOf("未打卡")==-1){
                    //将内容按顺序赋给对应的列对象
                    HSSFCell rowCell=row.createCell(j);
                    rowCell.setCellStyle(style);
                    rowCell.setCellValue(new HSSFRichTextString(txt));
                }else{
                    HSSFRichTextString text = new HSSFRichTextString(txt);

                    text.applyFont(txt.indexOf("未打卡"),txt.indexOf("未打卡")+3,redFont);
                    HSSFCell rowCell=row.createCell(j);
                    rowCell.setCellStyle(style);
                    //将内容按顺序赋给对应的列对象
                    rowCell.setCellValue(new HSSFRichTextString(text.getString()));
                }

            }
        }

        //style.setAlignment(HSSFCellStyle);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
            wb.close();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void empCheckinRecordExport(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }
        List<PersOrgEmployeeCheckinRecord> recordList=persOrgEmployeeCheckinRecordService.getCheckinList(empId,startDateStr,endDateStr);
        String[] title={"打卡规则名称","打卡时间","打卡类型","标准打卡时间","异常类型","打卡备注"};
        String fileName=""+fullName+"-"+startDateStr+"至"+endDateStr+" 打卡记录.xls";
        String sheetName = ""+fullName+"-打卡记录";

        String[][] content=new String[recordList.size()][title.length];
        if(recordList.size()>0){
            for(int i=0;i<recordList.size();i++){
                PersOrgEmployeeCheckinRecord record=recordList.get(i);
                content[i][0]=record.getGroupname();
                String checkinTime=DateUtils.formatDate(record.getCheckinTime(),"yyyy-MM-dd HH:mm:ss");
                if("上班打卡".equals(record.getCheckinType())){
                    if(record.getSchCheckinTime()!=null){
                        int diffMinute = DateUtils.getTimeDiffMinute(record.getSchCheckinTime(), record.getCheckinTime());
                        if(diffMinute>20){
                            checkinTime=DateUtils.formatDate(record.getSchCheckinTime(),"yyyy-MM-dd HH:mm:ss");
                        }
                    }

                }else if("下班打卡".equals(record.getCheckinType())){
                    if(record.getSchCheckinTime()!=null){
                        int diffMinute = DateUtils.getTimeDiffMinute(record.getCheckinTime(), record.getSchCheckinTime());
                        if(diffMinute>20){
                            checkinTime=DateUtils.formatDate(record.getSchCheckinTime(),"yyyy-MM-dd HH:mm:ss");
                        }
                    }
                }
                if("未打卡".equals(record.getExceptionType())){
                    checkinTime="";
                }
                content[i][1]=checkinTime;
                content[i][2]=record.getCheckinType();
                if(record.getSchCheckinTime()!=null){
                    content[i][3]=DateUtils.formatDate(record.getSchCheckinTime(),"yyyy-MM-dd HH:mm:ss");
                }else{
                    content[i][3]="";
                }
                content[i][4]=record.getExceptionType();
                content[i][5]=record.getNotes();

                /*content[i][5]=record.getLocationTitle();
                content[i][6]=record.getLocationDetail();*/
            }
        }
        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
            wb.close();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void empCheckinDaySummaryExport3(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList2(1,10000,null,fullName,startDateStr,endDateStr,archiveStatus,ids,AuthUtils.getUserId());
        List<Record> recordList=pageList.getList();

        Date startDate=DateUtils.parseDate(startDateStr);
        Date endDate=DateUtils.parseDate(endDateStr);
        List<Date> dateList=DateUtils.getBetweenDates(startDate,endDate);

        List<String> titleList=new ArrayList<>();
        titleList.add("部门");
        titleList.add("姓名");
        titleList.add("员工签名");
        titleList.add("应出勤天数");
        titleList.add("实出勤天数");
        titleList.add("缺勤天数");
        titleList.add("休息天数");
        titleList.add("实算工资天数");
        titleList.add("年休假");
        titleList.add("婚/丧/产/工伤假等");
        titleList.add("事假");
        titleList.add("补休");
        titleList.add("计工资加班");
        titleList.add("调休加班");
        titleList.add("迟到");
        titleList.add("早退");
        titleList.add("旷工");
        titleList.add("缺卡");
        titleList.add("补卡成功");

        PersOverTimeType[] persOverTimeTypes=PersOverTimeType.values();
        PersLeaveRestType[] persLeaveRestTypes=PersLeaveRestType.values();

        /*for(PersOverTimeType overTimeType:persOverTimeTypes){
            titleList.add("全天加班："+overTimeType.getValue());
        }
        for(PersOverTimeType overTimeType:persOverTimeTypes){
            titleList.add("工作日加班："+overTimeType.getValue());
        }
        for(PersLeaveRestType leaveRestType:persLeaveRestTypes){
            titleList.add("全天请休假："+leaveRestType.getValue());
        }
        for(PersLeaveRestType leaveRestType:persLeaveRestTypes){
            titleList.add("半天请休假："+leaveRestType.getValue());
        }*/

        for(Date date:dateList){
            DateUtils.getWeek();
            titleList.add(DateUtils.formatDate(date,"MM-dd ")+DateUtils.formatDate(date,"E"));
        }
        String[] title=new String[titleList.size()];
        titleList.toArray(title);
        String fileName="员工考勤"+startDateStr+"至"+endDateStr+"汇总.xls";
        String sheetName = "员工考勤汇总";

        String[][] content=new String[recordList.size()][title.length];
        /*content[0][0]="缺勤天数：未打上班和未打下班卡出勤天数\n实出勤天数：正常出勤天数+迟到、早退、漏卡出勤天数";*/

        //显示打卡地址的组织机构id
        List<String> showAddDeptIsList=new ArrayList<>();
        showAddDeptIsList.add("AD41DBBA-457A-471F-A892-CBDED927A98D");
        //showAddDeptIsList.add("BC1E1034-8D36-41CF-B49C-C54B3A107342");
        //showAddDeptIsList.add("3D86915E-A6B1-401E-8C25-299E7BBCFE08");

        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,"AD41DBBA-457A-471F-A892-CBDED927A98D");
        //persOrgService.findChildren(zTreeList,orgList,"BC1E1034-8D36-41CF-B49C-C54B3A107342");
        //persOrgService.findChildren(zTreeList,orgList,"3D86915E-A6B1-401E-8C25-299E7BBCFE08");

        for (PersOrg org : orgList) {
            showAddDeptIsList.add(org.getId());
        }

        if(recordList!=null && recordList.size()>0){
            List<Object> params=new ArrayList<>();
            String s="";
            for(int i=0;i<recordList.size();i++){
                String empId=recordList.get(i).getStr("id");
                params.add(empId);
                s+="?,";
            }
            s=s.substring(0,s.length()-1);
            params.add(startDateStr+" 00:00:00");
            params.add(endDateStr+" 23:59:59");


            String daySummarySql=" select * from pers_org_employee_checkin_day_summary a where a.emp_id in ("+s+") and a.del_flag='0'" +
                    "and a.summary_date between ? and ? order by emp_id,summary_date";
            List<PersOrgEmployeeCheckinDaySummary> daySummaryList=persOrgEmployeeCheckinDaySummaryService.findBySql(daySummarySql,params.toArray());
            Map<String,List<PersOrgEmployeeCheckinDaySummary>> empDaySunnary=new HashMap<>();
            for(PersOrgEmployeeCheckinDaySummary daySummary:daySummaryList){
                if(empDaySunnary.containsKey(daySummary.getEmpId())){
                    List<PersOrgEmployeeCheckinDaySummary> summaryList=empDaySunnary.get(daySummary.getEmpId());
                    summaryList.add(daySummary);
                }else{
                    List<PersOrgEmployeeCheckinDaySummary> summaryList=new ArrayList<>();
                    summaryList.add(daySummary);
                    empDaySunnary.put(daySummary.getEmpId(),summaryList);
                }
            }

            String checkinRecordSql="select a.emp_id as empId,a.id,a.checkin_type as checkinType,a.exception_type as exceptionType " +
                    ",a.checkin_time as checkinTime,a.sch_checkin_time as schCheckinTime,b.`status`,a.location_title as locationTitle " +
                    "from pers_org_employee_checkin_record a left join   pers_org_employee_checkin_fill_card b on a.id=b.checkin_record_id and b.`status`='2' " +
                    "where a.emp_id in ("+s+") and a.checkin_time between ? and ? and a.del_flag='0' order by a.emp_id,a.checkin_time ";
            List<Record> allCheckinRecordList=Db.find(checkinRecordSql,params.toArray());
            Map<String,List<Record>> empCheckinMap=new HashMap<>();
            for(Record record:allCheckinRecordList){
                if(empCheckinMap.containsKey(record.getStr("empId"))){
                    List<Record> records=empCheckinMap.get(record.getStr("empId"));
                    records.add(record);
                }else{
                    List<Record> records=new ArrayList<>();
                    records.add(record);
                    empCheckinMap.put(record.getStr("empId"),records);
                }
            }

            for(int i=0;i<recordList.size();i++){
                Record record=recordList.get(i);
                String empId=record.getStr("id");

                content[i][0]=record.getStr("orgName");
                content[i][1]=record.getStr("fullName");
                content[i][2]="";
                content[i][3]=record.getStr("shouldWorkDay");
                content[i][4]=record.getStr("actualWorkDay");
                content[i][5]=record.getStr("absenceCount");
                content[i][6]=record.getStr("restCount");
                String hour="";
                if(record.getLong("moneyOverSecond")>0){
                    hour=secondToTime(record.getLong("moneyOverSecond"));
                }

                content[i][7]=record.getStr("actualWageDays")+hour;
                content[i][8]=record.getStr("yearRest");
                content[i][9]=record.getStr("marriageHoliday");
                content[i][10]=record.getStr("thingLear");
                content[i][11]=record.getStr("learRest");
                content[i][12]=record.getStr("moneyOverDay")+hour;
                content[i][13]=record.getStr("restOverDay");

                String deptId=persOrgEmployeeService.findById(empId).getFirstDepeId();

                String exception_1="";
                if(record.getInt("exception_1")==null){
                    exception_1="0次";
                }else{
                    exception_1=record.getInt("exception_1")+"次";
                    if(record.getInt("exception_1_duration")!=null){
                        exception_1+=record.getInt("exception_1_duration")/60+"分钟";
                    }
                }
                content[i][14]=exception_1;
                String exception_2="";
                if(record.getInt("exception_2")==null){
                    exception_2="0次";
                }else{
                    exception_2=record.getInt("exception_2")+"次";
                    if(record.getInt("exception_2_duration")!=null){
                        exception_2+=record.getInt("exception_2_duration")/60+"分钟";
                    }
                }
                content[i][15]=exception_2;
                String exception_4="";
                if(record.getInt("exception_4")==null){
                    exception_4="0次";
                }else{
                    exception_4=record.getInt("exception_4")+"次";
                    if(record.getInt("exception_4_duration")!=null){
                        exception_4+=record.getInt("exception_4_duration")/60+"分钟";
                    }
                }
                content[i][16]=record.getStr("exception_4");

                if(record.getInt("exception_3")==null){
                    content[i][17]="0次";
                }else{
                    content[i][17]=record.getInt("exception_3")+"次";
                }
                if(record.getInt("fillCardCount")==null){
                    content[i][18]="0次";
                }else{
                    content[i][18]=record.getInt("fillCardCount")+"次";
                }

                /*if(record.getInt("exception_5")==null){
                    content[i][16]="0次";
                }else{
                    content[i][16]=record.getInt("exception_5")+"次";
                }
                if(record.getInt("exception_6")==null){
                    content[i][17]="0次";
                }else{
                    content[i][17]=record.getInt("exception_6")+"次";
                }*/



                for(int j=0;j<dateList.size();j++){
                    List<PersOrgEmployeeCheckinDaySummary> daySummaries=empDaySunnary.get(empId);
                    PersOrgEmployeeCheckinDaySummary daySummary=null;
                    for(PersOrgEmployeeCheckinDaySummary summary:daySummaries){
                        if(DateUtils.formatDate(dateList.get(j),"yyyy-MM-dd").equals(DateUtils.formatDate(summary.getSummaryDate(),"yyyy-MM-dd"))){
                            daySummary=summary;
                        }
                    }
                    List<Record> empAllCheckinRecordList=empCheckinMap.get(empId);
                    List<Record> checkinRecordList=new ArrayList<>();
                    if(empAllCheckinRecordList!=null){
                        for(Record checkinRecord:empAllCheckinRecordList){
                            if(DateUtils.formatDate(dateList.get(j),"yyyy-MM-dd").equals(DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"yyyy-MM-dd"))){
                                checkinRecordList.add(checkinRecord);
                            }
                        }
                    }

                    Map<String,String> typeMap=record.get("typeMap");
                    //persOrgEmployeeCheckinRecordService.getRecordListByDate(empId,dateList.get(j));

                    Map<String,Integer> dayTypeMap=record.get("dayTypeMap");
                    String dateStr=DateUtils.formatDate(dateList.get(j),"yyyy-MM-dd");
                    if(dayTypeMap.get(dateStr)!=null && 1==dayTypeMap.get(dateStr)) {
                        content[i][19+j] = "休息";
                    }else if(dayTypeMap.get(dateStr)!=null && 3==dayTypeMap.get(dateStr)){
                        content[i][19+j]="全天请休假："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"));
                    }else if(dayTypeMap.get(dateStr)!=null && 4==dayTypeMap.get(dateStr)){
                        content[i][19+j] = "外派";
                    }else{
                        if(checkinRecordList!=null && checkinRecordList.size()>0){
                            String str="";

                            if(dayTypeMap.get(dateStr)!=null && 2==dayTypeMap.get(dateStr)){
                                str+="全天加班："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"))+"\n";
                            }

                            for(Record checkinRecord : checkinRecordList){

                                String type="";
                                if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="上：";
                                }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="下：";
                                }
                                String fillCardStatus="";
                                if("2".equals(checkinRecord.getStr("status"))){
                                    fillCardStatus="(补卡成功)";
                                }
                                if(StrKit.notBlank(checkinRecord.getStr("exceptionType"))){
                                    if("未打卡".equals(checkinRecord.getStr("exceptionType"))){
                                        str+=type+"未打卡\n";
                                    }else{
                                        String timeException="";
                                        if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算迟到
                                            int minute= PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("checkinTime"),checkinRecord.getDate("schCheckinTime"));
                                            if(minute>0){
                                                timeException="迟到:"+(minute)+"分钟";
                                            }
                                        }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算早退
                                            int minute=PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("schCheckinTime"),checkinRecord.getDate("checkinTime"));
                                            if(minute>0){
                                                timeException="早退:"+(minute)+"分钟";
                                            }
                                        }
                                        if(showAddDeptIsList.contains(deptId)){
                                            str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")
                                                    +"("+timeException+")"+fillCardStatus+"\n"+checkinRecord.getStr("locationTitle")+"\n";
                                        }else{
                                            str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+"("+timeException+")"+fillCardStatus+"\n";
                                        }

                                    }
                                }else{
                                    if(showAddDeptIsList.contains(deptId)){
                                        str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n"+
                                                checkinRecord.getStr("locationTitle")+"\n";
                                    }else{
                                        str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n";
                                    }

                                }
                            }
                            content[i][19+j]=str;
                        }
                    }
                    /*if(daySummary!=null && 1==daySummary.getDayType()) {
                        content[i][18 + j] = "休息";
                    }else if(daySummary!=null && 3==daySummary.getDayType()){
                        content[i][18+j]="请休假："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"));
                    }else{
                        if(checkinRecordList!=null && checkinRecordList.size()>0){
                            String str="";

                            if(daySummary!=null && 2==daySummary.getDayType()){
                                str+="加班："+typeMap.get(DateUtils.formatDate(daySummary.getSummaryDate(),"yyyy-MM-dd"))+"\n";
                            }

                            for(Record checkinRecord : checkinRecordList){

                                String type="";
                                if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="上：";
                                }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                    type="下：";
                                }
                                String fillCardStatus="";
                                if("2".equals(checkinRecord.getStr("status"))){
                                    fillCardStatus="(补卡成功)";
                                }
                                if(StrKit.notBlank(checkinRecord.getStr("exceptionType"))){
                                    if("未打卡".equals(checkinRecord.getStr("exceptionType"))){
                                        str+=type+"未打卡\n";
                                    }else{
                                        String timeException="";
                                        if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算迟到
                                            int minute= PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("checkinTime"),checkinRecord.getDate("schCheckinTime"));
                                            if(minute>0){
                                                timeException="迟到:"+(minute)+"分钟";
                                            }
                                        }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            //计算早退
                                            int minute=PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("schCheckinTime"),checkinRecord.getDate("checkinTime"));
                                            if(minute>0){
                                                timeException="早退:"+(minute)+"分钟";
                                            }
                                        }
                                        str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+"("+timeException+")"+fillCardStatus+"\n";
                                    }
                                }else{
                                    str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n";
                                }
                            }
                            content[i][18+j]=str;
                        }
                    }*/
                }
            }
        }

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        HSSFWorkbook wb  = new HSSFWorkbook();

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        HSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        HSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        HSSFCellStyle style = wb.createCellStyle();



        style.setWrapText(true);

        //声明列对象
        HSSFCell cell = null;

        HSSFFont redFont = wb.createFont();
        redFont.setFontHeightInPoints((short) 16);

        style.setFont(redFont);

        //创建标题
        for(int i=0;i<title.length;i++){
            cell = row.createCell(i);
            cell.setCellStyle(style);
            cell.setCellValue(new HSSFRichTextString(title[i]));
        }




        //创建内容
        for(int i=0;i<content.length;i++){

            row = sheet.createRow(i + 1);
            for(int j=0;j<content[i].length;j++){
                if(i==content.length-1){
                    if(j==0){
                        sheet.setColumnWidth(j,60*256);
                    }else if(j>1 && j<14){
                        sheet.setColumnWidth(j,20*256);
                    }else{
                        sheet.setColumnWidth(j,35*256);
                    }
                }

                String txt=content[i][j];
                if( StrKit.isBlank(txt)|| txt.indexOf("未打卡")==-1){
                    //将内容按顺序赋给对应的列对象
                    HSSFCell rowCell=row.createCell(j);
                    rowCell.setCellStyle(style);
                    rowCell.setCellValue(new HSSFRichTextString(txt));
                }else{
                    HSSFRichTextString text = new HSSFRichTextString(txt);

                    text.applyFont(txt.indexOf("未打卡"),txt.indexOf("未打卡")+3,redFont);
                    HSSFCell rowCell=row.createCell(j);
                    rowCell.setCellStyle(style);
                    //将内容按顺序赋给对应的列对象
                    rowCell.setCellValue(new HSSFRichTextString(text.getString()));
                }

            }
        }

        //style.setAlignment(HSSFCellStyle);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
            wb.close();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void examineEmpDaySummary(){
        String dateRange=getPara("dateRange");
        String idsStr=getPara("ids");
        if(StrKit.isBlank(dateRange)){
            renderJson(Ret.fail("msg","时间不能为空"));
            return;
        }
        if(StrKit.isBlank(idsStr)){
            renderJson(Ret.fail("msg","请至少选择一条"));
            return;
        }
        JSONArray jsonArray=JSON.parseArray(idsStr);
        if(jsonArray.size()==0){
            renderJson(Ret.fail("msg","请至少选择一条"));
            return;
        }
        String[] dates=dateRange.split(" - ");
        String startDate=dates[0];
        String endDate=dates[1];

        String empStr="";
        List<Object> empParmas=new ArrayList<>();
        for(int i=0;i<jsonArray.size();i++){
            empStr+="?,";
            empParmas.add(jsonArray.getString(i));
        }
        empStr=empStr.substring(0,empStr.length()-1);
        empParmas.add(startDate+" 00:00:00");
        empParmas.add(endDate+" 23:59:59");

        String leaveSql="select * from pers_org_employee_leave_rest_apply where del_flag='0' and `status` in ('1','2') " +
                "and emp_id in ("+empStr+") and not  ( (end_time < ?) OR (start_time > ?) ) ";
        List<Record> leaveRecordList=Db.find(leaveSql,empParmas.toArray());
        String fillCardSql="select * from pers_org_employee_checkin_fill_card where del_flag='0' and `status` in ('0','1') " +
                " and emp_id in ("+empStr+") and fill_card_time BETWEEN ? and ? ";
        List<Record> fillCardRecordList=Db.find(fillCardSql,empParmas.toArray());
        String overTimeSql="select * from pers_org_employee_over_time_apply where del_flag='0' and `status` in ('1','2') " +
                "and emp_id in ("+empStr+") and start_time BETWEEN ? and ? ;";
        List<Record> overTimeRecordList=Db.find(overTimeSql,empParmas.toArray());

        List<String> msgList=new ArrayList<>();
        String msg="";
        if(leaveRecordList.size()>0){
            for(Record record:leaveRecordList){
                PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("emp_id"));
                msg+=employee.getFullName()+"、";
            }
            msg=msg.substring(0,msg.length()-1);
            msg+="员工存在未完成的请休假流程";
        }
        if(fillCardRecordList.size()>0){
            for(Record record:fillCardRecordList){
                PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("emp_id"));
                msg+=employee.getFullName()+"、";
            }
            msg=msg.substring(0,msg.length()-1);
            msg+="员工存在未完成的补卡流程";
        }
        if(overTimeRecordList.size()>0){
            for(Record record:overTimeRecordList){
                PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("emp_id"));
                msg+=employee.getFullName()+"、";
            }
            msg=msg.substring(0,msg.length()-1);
            msg+="员工存在未完成的加班流程";
        }
        if(StrKit.notBlank(msg)){
            renderJson(Ret.fail("msg",msg));
            return;
        }

        if(Integer.valueOf(DateUtils.getDate("yyyyMM"))<Integer.valueOf(DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyyMM"))){
            renderJson(Ret.fail("msg","操作失败,最大审核时间为:"+DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd")));
            return;
        }
        PersOrg persOrg=persOrgService.findById("0C1B552C-A293-4A47-A5A9-D28EAB16C4A6");
        List<PersOrg> orgList=new ArrayList<>();
        orgList.add(persOrg);
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,persOrg.getId());

        //分公司
        PersOrg branchCompany=persOrgService.findById("AD41DBBA-457A-471F-A892-CBDED927A98D");
        List<PersOrg> branchCompanyOrgList=new ArrayList<>();
        branchCompanyOrgList.add(branchCompany);
        persOrgService.findChildren(zTreeList,branchCompanyOrgList,branchCompany.getId());

        //旅居基地
        PersOrg sojourn=persOrgService.findById("B97B6F94-140C-4C86-8BC1-6A2BCC86CA3A");
        List<PersOrg> sojournOrgList=new ArrayList<>();
        sojournOrgList.add(sojourn);
        persOrgService.findChildren(zTreeList,sojournOrgList,sojourn.getId());

        //康养中心
        PersOrg pension=persOrgService.findById("3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7");
        List<PersOrg> pensionOrgList=new ArrayList<>();
        pensionOrgList.add(pension);
        persOrgService.findChildren(zTreeList,pensionOrgList,pension.getId());


        List<PersOrgEmployeeCheckinDaySummaryExamine> addList=new ArrayList<>();
        List<PersOrgEmployeeCheckinDaySummaryExamine> updateList=new ArrayList<>();
        List<Date> dateList=DateUtils.getBetweenDates(DateUtils.parseDate(startDate),DateUtils.parseDate(endDate));
        for(int i=0;i<jsonArray.size();i++){
            //判断是否是科技公司下面的员工
            PersOrgEmployee employee=persOrgEmployeeService.findById(jsonArray.getString(i));
            List<String> deptList=Db.query("select relationship_id from pers_org_employee_rel where emp_id=? and relationship_type='dept'",employee.getId());
            boolean flag=false;
            for(PersOrg org:orgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(org.getId())){
                        flag=true;
                        break;
                    }
                }
                if(flag){
                    break;
                }
            }

            boolean branchCompanyFlag=false;
            for(PersOrg org:branchCompanyOrgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(org.getId())){
                        branchCompanyFlag=true;
                        break;
                    }
                }
                if(branchCompanyFlag){
                    break;
                }
            }

            boolean sojournFlag=false;
            for(PersOrg o:sojournOrgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(o.getId())){
                        sojournFlag=true;
                        break;
                    }
                }
                if(sojournFlag){
                    break;
                }
            }

            boolean pensionFlag=false;
            for(PersOrg o:pensionOrgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(o.getId())){
                        pensionFlag=true;
                        break;
                    }
                }
                if(pensionFlag){
                    break;
                }
            }

            //判断 最小时间是否有未提交的记录
            for(Date date:dateList){
                PersOrgEmployeeCheckinDaySummaryExamine checkinMonthExamine=persOrgEmployeeCheckinDaySummaryExamineService
                        .findEmpExamineByDate(employee.getId(),DateUtils.formatDate(date,"yyyy-MM-dd"));
                if(flag){
                    //科技公司员工

                    if(AuthUtils.getUserId().equalsIgnoreCase(persOrg.getLinkMan())){
                        if(checkinMonthExamine!=null && StrKit.notBlank(checkinMonthExamine.getReviewer2())){
                            renderJson(Ret.fail("msg",employee.getFullName()+" "+DateUtils.formatDate(date,"yyyy-MM-dd")+"已被审核完成提交失败"));
                            return;
                        }
                    }else if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") ||
                            AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                        if(checkinMonthExamine==null){
                            renderJson(Ret.fail("msg","科技公司有部门负责人未审核的记录"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人，请勿操作"));
                        return;
                    }
                    if(checkinMonthExamine==null){
                        if(persOrg.getLinkMan().equalsIgnoreCase(AuthUtils.getUserId())){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setStatus("2");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            //科技公司
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }
                    }else{
                        if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") ||AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                            checkinMonthExamine.setReviewer2(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            updateList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }

                    }

                }else if(sojournFlag){
                    String deptId=employee.getFirstDepeId();
                    PersOrg empDept=persOrgService.findById(deptId);
                    String buHandler=null;
                    if("1".equals(empDept.getIsBu())){
                        buHandler=empDept.getLinkMan();
                    }else{
                        PersOrg buOrg=persOrgService.getBUOrg(empDept.getParentId());
                        if(buOrg!=null){
                            buHandler=buOrg.getLinkMan();
                        }
                    }
                    if(StrKit.isBlank(buHandler)){
                        renderJson(Ret.fail("msg","获取BU处理人失败"));
                        return;
                    }
                    //旅居基地
                    if(AuthUtils.getUserId().equalsIgnoreCase(buHandler)){
                        if(checkinMonthExamine!=null){
                            continue;
                        }
                    }else if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                        if(checkinMonthExamine==null){
                            renderJson(Ret.fail("msg","有基地负责人未审核的记录"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人，请勿操作"));
                        return;
                    }
                    if(checkinMonthExamine==null){
                        if(buHandler.equalsIgnoreCase(AuthUtils.getUserId())){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setStatus("2");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            //科技公司
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }
                    }else{
                        if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                            checkinMonthExamine.setReviewer2(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            updateList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }

                    }
                }else if(pensionFlag){
                    PersOrg org=persOrgService.findById("3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7");
                    if(AuthUtils.getUserId().equalsIgnoreCase(org.getLinkMan())){
                        if(checkinMonthExamine!=null){
                            continue;
                        }
                    }else if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08") ){

                        if(checkinMonthExamine!=null && StrKit.notBlank(checkinMonthExamine.getReviewer2())){
                            renderJson(Ret.fail("msg",employee.getFullName()+" "+DateUtils.formatDate(date,"yyyy-MM-dd")+"已被审核完成提交失败"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人，请勿操作"));
                        return;
                    }
                    if(checkinMonthExamine==null){
                        if(org.getLinkMan().equalsIgnoreCase(AuthUtils.getUserId())){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setStatus("2");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }
                    }else{
                        if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                            checkinMonthExamine.setReviewer2(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            updateList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }

                    }
                }else if(branchCompanyFlag){

                    if(AuthUtils.getUserId().equalsIgnoreCase("16503FC2-6F40-443C-8DB5-638100D88B4E")){

                        if(checkinMonthExamine==null){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","请勿重复操作"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人,请勿操作"));
                        return;
                    }

                }else{
                    if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){

                        if(checkinMonthExamine==null){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","请勿重复操作"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人,请勿操作"));
                        return;
                    }
                }
            }
        }
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    Db.batchSave(addList,addList.size());
                    Db.batchUpdate(updateList,updateList.size());
                    return true;
                }catch (Exception e){
                    e.printStackTrace();
                }
                return false;
            }
        });
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void examineEmpDaySummaryAll(){
        String dateRange=getPara("dateRange");
        String id=getPara("id");
        if(StrKit.isBlank(dateRange)){
            renderJson(Ret.fail("msg","时间不能为空"));
            return;
        }
        if(StrKit.isBlank(id)){
            renderJson(Ret.fail("msg","请选择要审核的部门"));
            return;
        }

        PersOrg org=persOrgService.findById(id);
        List<PersOrg> oList=new ArrayList<>();
        oList.add(org);
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,oList,org.getId());
        String str="";
        List<String> params=new ArrayList<>();
        for(PersOrg o:oList){
            params.add(o.getId());
            str+="?,";
        }
        str=str.substring(0,str.length()-1);
        List<String> empIds=Db.query("select DISTINCT a.emp_id from pers_org_employee_rel a left join pers_org_employee " +
                " b on a.emp_id=b.id where  a.relationship_type='dept' and b.archive_status='incumbency' and a.relationship_id in ("+str+") " +
                "  ",params.toArray());
        if(empIds.size()==0){
            renderJson(Ret.fail("msg","该部门没有需要审核的记录"));
            return;
        }

        String[] dates=dateRange.split(" - ");
        String startDate=dates[0];
        String endDate=dates[1];

        if(Integer.valueOf(DateUtils.getDate("yyyyMM"))<Integer.valueOf(DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyyMM"))){
            renderJson(Ret.fail("msg","操作失败,最大审核时间为:"+DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd")));
            return;
        }


        String empStr="";
        List<Object> empParmas=new ArrayList<>();
        for(String empId:empIds){
            empStr+="?,";
            empParmas.add(empId);
        }
        empStr=empStr.substring(0,empStr.length()-1);
        empParmas.add(startDate+" 00:00:00");
        empParmas.add(endDate+" 23:59:59");

        String leaveSql="select * from pers_org_employee_leave_rest_apply where del_flag='0' and `status` in ('1','2') " +
                "and emp_id in ("+empStr+") and not  ( (end_time < ?) OR (start_time > ?) ) ";
        List<Record> leaveRecordList=Db.find(leaveSql,empParmas.toArray());
        String fillCardSql="select * from pers_org_employee_checkin_fill_card where del_flag='0' and `status` in ('0','1') " +
                " and emp_id in ("+empStr+") and fill_card_time BETWEEN ? and ? ";
        List<Record> fillCardRecordList=Db.find(fillCardSql,empParmas.toArray());
        String overTimeSql="select * from pers_org_employee_over_time_apply where del_flag='0' and `status` in ('1','2') " +
                "and emp_id in ("+empStr+") and start_time BETWEEN ? and ? ;";
        List<Record> overTimeRecordList=Db.find(overTimeSql,empParmas.toArray());
        if(leaveRecordList.size()>0){
            Record record=leaveRecordList.get(0);
            PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("emp_id"));
            renderJson(Ret.fail("msg",employee.getFullName()+"员工存在未完成的请休假流程"));
            return;
        }
        if(fillCardRecordList.size()>0){
            Record record=fillCardRecordList.get(0);
            PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("emp_id"));
            renderJson(Ret.fail("msg",employee.getFullName()+"员工存在未完成的补卡流程"));
            return;
        }
        if(overTimeRecordList.size()>0){
            Record record=overTimeRecordList.get(0);
            PersOrgEmployee employee=persOrgEmployeeService.findById(record.getStr("emp_id"));
            renderJson(Ret.fail("msg",employee.getFullName()+"员工存在未完成的加班流程"));
            return;
        }


        //科技公司
        PersOrg persOrg=persOrgService.findById("0C1B552C-A293-4A47-A5A9-D28EAB16C4A6");
        List<PersOrg> orgList=new ArrayList<>();
        orgList.add(persOrg);

        persOrgService.findChildren(zTreeList,orgList,persOrg.getId());

        //分公司
        PersOrg branchCompany=persOrgService.findById("AD41DBBA-457A-471F-A892-CBDED927A98D");
        List<PersOrg> branchCompanyOrgList=new ArrayList<>();
        branchCompanyOrgList.add(branchCompany);
        persOrgService.findChildren(zTreeList,branchCompanyOrgList,branchCompany.getId());

        //旅居基地
        PersOrg sojourn=persOrgService.findById("B97B6F94-140C-4C86-8BC1-6A2BCC86CA3A");
        List<PersOrg> sojournOrgList=new ArrayList<>();
        sojournOrgList.add(sojourn);
        persOrgService.findChildren(zTreeList,sojournOrgList,sojourn.getId());

        //康养中心
        PersOrg pension=persOrgService.findById("3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7");
        List<PersOrg> pensionOrgList=new ArrayList<>();
        pensionOrgList.add(pension);
        persOrgService.findChildren(zTreeList,pensionOrgList,pension.getId());

        List<PersOrgEmployeeCheckinDaySummaryExamine> addList=new ArrayList<>();
        List<PersOrgEmployeeCheckinDaySummaryExamine> updateList=new ArrayList<>();
        List<Date> dateList=DateUtils.getBetweenDates(DateUtils.parseDate(startDate),DateUtils.parseDate(endDate));
        for(String empId:empIds){
            //判断是否是科技公司下面的员工
            PersOrgEmployee employee=persOrgEmployeeService.findById(empId);
            List<String> deptList=Db.query("select relationship_id from pers_org_employee_rel where emp_id=? and relationship_type='dept'",employee.getId());
            boolean flag=false;
            for(PersOrg o:orgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(o.getId())){
                        flag=true;
                        break;
                    }
                }
                if(flag){
                    break;
                }
            }
            boolean branchCompanyFlag=false;
            for(PersOrg o:branchCompanyOrgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(o.getId())){
                        branchCompanyFlag=true;
                        break;
                    }
                }
                if(branchCompanyFlag){
                    break;
                }
            }

            boolean sojournFlag=false;
            for(PersOrg o:sojournOrgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(o.getId())){
                        sojournFlag=true;
                        break;
                    }
                }
                if(sojournFlag){
                    break;
                }
            }

            boolean pensionFlag=false;
            for(PersOrg o:pensionOrgList){
                for(String deptId:deptList){
                    if(deptId.equalsIgnoreCase(o.getId())){
                        pensionFlag=true;
                        break;
                    }
                }
                if(pensionFlag){
                    break;
                }
            }

            //判断 最小时间是否有未提交的记录
            for(Date date:dateList){
                PersOrgEmployeeCheckinDaySummaryExamine checkinMonthExamine=persOrgEmployeeCheckinDaySummaryExamineService
                        .findEmpExamineByDate(employee.getId(),DateUtils.formatDate(date,"yyyy-MM-dd"));
                if(flag){
                    //科技公司员工

                    if(AuthUtils.getUserId().equalsIgnoreCase(persOrg.getLinkMan())){
                        if(checkinMonthExamine!=null && StrKit.notBlank(checkinMonthExamine.getReviewer2())){
                            renderJson(Ret.fail("msg",employee.getFullName()+" "+DateUtils.formatDate(date,"yyyy-MM-dd")+"已被审核完成提交失败"));
                            return;
                        }
                    }else if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                        if(checkinMonthExamine==null){
                            renderJson(Ret.fail("msg","科技公司有部门负责人未审核的记录"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人，请勿操作"));
                        return;
                    }
                    if(checkinMonthExamine==null){
                        if(persOrg.getLinkMan().equalsIgnoreCase(AuthUtils.getUserId())){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setStatus("2");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            //科技公司
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }
                    }else{
                        if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                            checkinMonthExamine.setReviewer2(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            updateList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }

                    }

                }else if(sojournFlag){
                    String deptId=employee.getFirstDepeId();
                    PersOrg empDept=persOrgService.findById(deptId);
                    String buHandler=null;
                    if("1".equals(empDept.getIsBu())){
                        buHandler=empDept.getLinkMan();
                    }else{
                        PersOrg buOrg=persOrgService.getBUOrg(empDept.getParentId());
                        if(buOrg!=null){
                            buHandler=buOrg.getLinkMan();
                        }
                    }
                    if(StrKit.isBlank(buHandler)){
                        renderJson(Ret.fail("msg","获取BU处理人失败"));
                        return;
                    }
                    //旅居基地
                    if(AuthUtils.getUserId().equalsIgnoreCase(buHandler)){
                        if(checkinMonthExamine!=null){
                            continue;
                        }
                    }else if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                        if(checkinMonthExamine==null){
                            renderJson(Ret.fail("msg","有基地负责人未审核的记录"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人，请勿操作"));
                        return;
                    }
                    if(checkinMonthExamine==null){
                        if(buHandler.equalsIgnoreCase(AuthUtils.getUserId())){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setStatus("2");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            //科技公司
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }
                    }else{
                        if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                            checkinMonthExamine.setReviewer2(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            updateList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }

                    }
                }else if(pensionFlag){
                    PersOrg org2=persOrgService.findById("3A41EFC3-B683-4D2E-8F14-FC1BFA9A06F7");
                    if(AuthUtils.getUserId().equalsIgnoreCase(org2.getLinkMan())){
                        if(checkinMonthExamine!=null){
                            continue;
                        }
                    }else if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){

                        if(checkinMonthExamine!=null && StrKit.notBlank(checkinMonthExamine.getReviewer2())){
                            renderJson(Ret.fail("msg",employee.getFullName()+" "+DateUtils.formatDate(date,"yyyy-MM-dd")+"已被审核完成提交失败"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人，请勿操作"));
                        return;
                    }
                    if(checkinMonthExamine==null){
                        if(org2.getLinkMan().equalsIgnoreCase(AuthUtils.getUserId())){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setStatus("2");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }
                    }else{
                        if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){
                            checkinMonthExamine.setReviewer2(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            updateList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","不是审核人"));
                            return;
                        }

                    }
                }else if(branchCompanyFlag){

                    if(AuthUtils.getUserId().equalsIgnoreCase("16503FC2-6F40-443C-8DB5-638100D88B4E")){

                        if(checkinMonthExamine==null){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","请勿重复操作"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人,请勿操作"));
                        return;
                    }

                }else{
                    if(AuthUtils.getUserId().equalsIgnoreCase("DCAB8C7F-4C64-446D-8C81-8B00CB8BD40F") || AuthUtils.getUserId().equalsIgnoreCase("530F35B4-F753-412E-8583-0DF4D9C7EF08")){

                        if(checkinMonthExamine==null){
                            checkinMonthExamine=new PersOrgEmployeeCheckinDaySummaryExamine();
                            checkinMonthExamine.setId(IdGen.getUUID());
                            checkinMonthExamine.setEmpId(employee.getId());
                            checkinMonthExamine.setDayDate(date);
                            checkinMonthExamine.setReviewer1(AuthUtils.getUserId());
                            checkinMonthExamine.setStatus("3");
                            checkinMonthExamine.setDelFlag("0");
                            checkinMonthExamine.setCreateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setCreateDate(new Date());
                            checkinMonthExamine.setUpdateBy(AuthUtils.getUserId());
                            checkinMonthExamine.setUpdateDate(new Date());
                            addList.add(checkinMonthExamine);
                        }else{
                            renderJson(Ret.fail("msg","请勿重复操作"));
                            return;
                        }
                    }else{
                        renderJson(Ret.fail("msg","不是审核人,请勿操作"));
                        return;
                    }
                }
            }


        }
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    Db.batchSave(addList,addList.size());
                    Db.batchUpdate(updateList,updateList.size());
                    return true;
                }catch (Exception e){
                    e.printStackTrace();
                }
                return false;
            }
        });
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    /*public void revokeExamineEmpDaySummaryAll() {
        String yearMonth = getPara("yearMonth");
        String id = getPara("id");
        if (StrKit.isBlank(yearMonth)) {
            renderJson(Ret.fail("msg", "时间不能为空"));
            return;
        }
        if (StrKit.isBlank(id)) {
            renderJson(Ret.fail("msg", "请选择要撤销的部门"));
            return;
        }

        PersOrg org = persOrgService.findById(id);
        List<PersOrg> oList = new ArrayList<>();
        oList.add(org);
        persOrgService.findChildren(oList, org.getId());
        String str = "";
        List<String> params = new ArrayList<>();
        for (PersOrg o : oList) {
            params.add(o.getId());
            str += "?,";
        }
        str = str.substring(0, str.length() - 1);
        List<String> empIds = Db.query("select DISTINCT emp_id from pers_org_employee_rel where  relationship_type='dept' and relationship_id in (" + str + ") " +
                "  ", params.toArray());
        if (empIds.size() == 0) {
            renderJson(Ret.fail("msg", "该部门没有需要撤销的记录"));
            return;
        }
        List<String> examineIds=Db.query("select id from pers_org_employee_rel where del_flag='0'  ", params.toArray());

    }*/

    public void empCheckinDaySummaryRecordExport(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");

        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            /*String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];*/
            startDateStr=dateRange+"-01";
            endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }
        List<Record> recordList=persOrgEmployeeCheckinDaySummaryService.empCheckinDaySummaryDetailList(empId,startDateStr,endDateStr);

        String[] title={"日期","日报类型","打卡次数","最早打卡时间","最早打卡时间","实际工作时长","标准工作时长","迟到","早退","旷工","缺卡","地点异常","设备异常"};
        String fileName=""+fullName+"-"+startDateStr+"至"+endDateStr+"考勤日报.xls";
        String sheetName = ""+fullName+"-考勤日报";

        String[][] content=new String[recordList.size()][title.length];

        if(recordList!=null && recordList.size()>0){
            for(int i=0;i<recordList.size();i++){
                Record record=recordList.get(i);
                content[i][0]=DateUtils.formatDate(record.getDate("summary_date"),"yyyy-MM-dd");
                if(0==record.getInt("day_type")){
                    content[i][1]="工作日日报";
                }else if(1==record.getInt("day_type")){
                    content[i][1]="休息日日报";
                }
                content[i][2]=record.getStr("checkin_count");
                if(record.getDate("earliest_time")!=null){
                    content[i][3]=DateUtils.formatDate(record.getDate("earliest_time"),"yyyy-MM-dd");
                }
                if(record.getDate("lastest_time")!=null){
                    content[i][4]=DateUtils.formatDate(record.getDate("lastest_time"),"yyyy-MM-dd");
                }
                if(record.getDouble("regularWorkSecStr")==null){
                    content[i][5]="0小时";
                }else{
                    content[i][5]=record.getDouble("regularWorkSecStr")+"小时";
                }

                if(record.getDouble("standardWorkSecStr")==null){
                    content[i][6]="0小时";
                }else{
                    content[i][6]=record.getDouble("standardWorkSecStr")+"小时";
                }
                String exception_1="";
                if(record.getInt("exception_1")==null){
                    exception_1="0次";
                }else{
                    exception_1=record.getInt("exception_1")+"次";
                    if(record.getInt("exception_1_duration")!=null){
                        exception_1+=record.getInt("exception_1_duration")/60+"分钟";
                    }
                }
                content[i][7]=exception_1;
                String exception_2="";
                if(record.getInt("exception_2")==null){
                    exception_2="0次";
                }else{
                    exception_2=record.getInt("exception_2")+"次";
                    if(record.getInt("exception_2_duration")!=null){
                        exception_2+=record.getInt("exception_2_duration")/60+"分钟";
                    }
                }
                content[i][8]=exception_2;
                String exception_4="";
                if(record.getInt("exception_4")==null){
                    exception_4="0次";
                }else{
                    exception_4=record.getInt("exception_4")+"次";
                    if(record.getInt("exception_4_duration")!=null){
                        exception_4+=record.getInt("exception_4_duration")/60+"分钟";
                    }
                }
                content[i][9]=exception_4;

                if(record.getInt("exception_3")==null){
                    content[i][10]="0次";
                }else{
                    content[i][10]=record.getInt("exception_3")+"次";
                }
                if(record.getInt("exception_5")==null){
                    content[i][11]="0次";
                }else{
                    content[i][11]=record.getInt("exception_5")+"次";
                }
                if(record.getInt("exception_6")==null){
                    content[i][11]="0次";
                }else{
                    content[i][11]=record.getInt("exception_6")+"次";
                }
            }
        }
        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
            wb.close();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void empCheckinDaySummaryDetailIndex(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");

        setAttr("empId",empId);
        setAttr("dateRange",dateRange);
        setAttr("fullName",fullName);
        setAttr("user",AuthUtils.getLoginUser());
        render("reportFormDetail.html");
    }

    public void empCheckinDaySummaryDetailList(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");

        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            /*startDateStr=dateRange+"-01";
            endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));*/
        }
        List<Record> recordList=persOrgEmployeeCheckinDaySummaryService.empCheckinDaySummaryDetailList(empId,startDateStr,endDateStr);
        renderJson(new DataTable<Record>(recordList));
    }

    public void empCheckinDaySummaryDetailIndexNew(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");

        setAttr("empId",empId);
        setAttr("dateRange",dateRange);
        setAttr("fullName",fullName);
        render("reportFormDetailNew.html");
    }

    public void empCheckinDaySummaryDetailListNew(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");

        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            /*startDateStr=dateRange+"-01";
            endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));*/
        }
        List<Record> recordList=persOrgEmployeeCheckinDaySummaryService.empCheckinDaySummaryDetailListNew(empId,startDateStr,endDateStr);
        renderJson(new DataTable<Record>(recordList));
    }

    public void empCheckinRecordIndex(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");

        setAttr("empId",empId);
        setAttr("dateRange",dateRange);
        setAttr("fullName",fullName);
        render("recordIndex.html");
    }

    public void empCheckinRecordPage(){
        String empId=getPara("empId");
        String dateRange=getPara("dateRange");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }
        if(DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd").equals(endDateStr)){
            endDateStr=DateUtils.formatDate(new Date(),"yyyy-MM-dd");
        }
        Page<Record> page=persOrgEmployeeCheckinRecordService.pageList(getParaToInt("page"),getParaToInt("limit"),empId,startDateStr,endDateStr);
        renderJson(new DataTable<Record>(page));
    }

    public void test(){
        int count=getParaToInt("count");
        try {
            /*for(int i=1;i<=count;i++){*/
                getCheckinDayData(1,50,count);
                Thread.sleep(5000);
            /*}*/
        }catch (Exception e){
            e.printStackTrace();
        }
        renderJson("ok");
    }

    private void getCheckinDayData(int pageNumber,int pageSize,int aa){
        String sql="  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
        Page<Record> recordPage = Db.paginate(pageNumber,pageSize,"select id,qiye_userid ",sql);
        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<Record> recordList=recordPage.getList();
        List<String> qiyeIds=new ArrayList<>();

        Map<String,String> empIdMap=new HashMap<>();

        for(Record record:recordList){
            empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
            qiyeIds.add(record.getStr("qiye_userid"));
        }
        String accessToken=qiYeWeiXinService.getCheckinAccessToken();

        Date summaryDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.getNextDay(new Date(),-aa),"yyyy-MM-dd")+" 00:00:00");
        long startTime=summaryDate.getTime()/1000;
        long endTime=startTime;
        if(StrKit.isBlank(accessToken)){
            return;
        }
        String params="{\"starttime\":"+startTime+",\"endtime\":"+endTime+",\"useridlist\":"+JSON.toJSONString(qiyeIds)+"}";
        String resultStr= HttpKit.post(" https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata?access_token="+accessToken,params);
        System.out.println(resultStr);
        if(StrKit.isBlank(resultStr) || !resultStr.startsWith("{") || !resultStr.endsWith("}")){
            return;
        }
        JSONObject jsonObject= JSON.parseObject(resultStr);

        if(jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode")==0 ){
            JSONArray datas=jsonObject.getJSONArray("datas");

            List<PersOrgEmployeeCheckinDaySummary> checkinDaySummaryAddList=new ArrayList<>();

            List<PersOrgEmployeeCheckinException> checkinExceptionAddList=new ArrayList<>();

            List<PersOrgEmployeeCheckinItems> checkinItemsAddList=new ArrayList<>();

            for(int i=0;i<datas.size();i++){

                JSONObject data=datas.getJSONObject(i);
                JSONObject baseInfo=data.getJSONObject("base_info");
                JSONObject ruleInfo=baseInfo.getJSONObject("rule_info");
                JSONArray checkintimeArray=ruleInfo.getJSONArray("checkintime");

                String userid=baseInfo.getString("acctid");
                String empId=empIdMap.get(userid);
                JSONObject summaryInfo=data.getJSONObject("summary_info");
                JSONObject overworkInfo=data.getJSONObject("overwork_info");

                JSONArray exceptionInfos=data.getJSONArray("exception_infos");
                JSONArray spItems=data.getJSONArray("sp_items");


                PersOrgEmployeeCheckinDaySummary daySummary=new PersOrgEmployeeCheckinDaySummary();
                daySummary.setId(IdGen.getUUID());
                daySummary.setSummaryDate(summaryDate);
                daySummary.setEmpId(empId);
                daySummary.setId(IdGen.getUUID());
                daySummary.setDelFlag("0");
                daySummary.setCreateDate(new Date());
                if(checkintimeArray!=null && checkintimeArray.size()>0){
                    JSONObject checkintime=checkintimeArray.getJSONObject(0);
                    int workSec=checkintime.getInteger("work_sec");
                    int offWorkSec=checkintime.getInteger("off_work_sec");
                    Calendar workSecCalendar=Calendar.getInstance();
                    workSecCalendar.setTime(summaryDate);
                    workSecCalendar.add(Calendar.SECOND,workSec);

                    Calendar offWorkSecCalendar=Calendar.getInstance();
                    offWorkSecCalendar.setTime(summaryDate);
                    offWorkSecCalendar.add(Calendar.SECOND,offWorkSec);

                    daySummary.setWorkSec(workSecCalendar.getTime());
                    daySummary.setOffWorkSec(offWorkSecCalendar.getTime());
                }
                daySummary.setDayType(baseInfo.getInteger("day_type"));
                daySummary.setCheckinCount(summaryInfo.getInteger("checkin_count"));
                daySummary.setRegularWorkSec(summaryInfo.getInteger("regular_work_sec"));
                daySummary.setStandardWorkSec(summaryInfo.getInteger("standard_work_sec"));
                Integer earliestTime=summaryInfo.getInteger("earliest_time");
                Integer lastestTime = summaryInfo.getInteger("lastest_time");
                if(earliestTime!=null){
                    Calendar earliestTimeCalendar=Calendar.getInstance();
                    earliestTimeCalendar.setTime(summaryDate);
                    earliestTimeCalendar.add(Calendar.SECOND,earliestTime);
                    daySummary.setEarliestTime(earliestTimeCalendar.getTime());
                }
                if(lastestTime!=null){
                    Calendar lastestTimeCalendar=Calendar.getInstance();
                    lastestTimeCalendar.setTime(summaryDate);
                    lastestTimeCalendar.add(Calendar.SECOND,lastestTime);
                    daySummary.setLastestTime(lastestTimeCalendar.getTime());
                }

                daySummary.setRecordType(baseInfo.getInteger("record_type"));
                daySummary.setGroupId(ruleInfo.getString("groupid"));
                daySummary.setGroupName(ruleInfo.getString("groupname"));
                if(exceptionInfos!=null && exceptionInfos.size()>0){
                    daySummary.setIsException("1");
                }else{
                    daySummary.setIsException("0");
                }
                daySummary.setUpdateDate(new Date());
                checkinDaySummaryAddList.add(daySummary);
                if(exceptionInfos!=null && exceptionInfos.size()>0){
                    for(int j=0;j<exceptionInfos.size();j++){
                        JSONObject exceptionInfo=exceptionInfos.getJSONObject(j);
                        int exception=exceptionInfo.getIntValue("exception");
                        PersOrgEmployeeCheckinException checkinException=new PersOrgEmployeeCheckinException();
                        checkinException.setId(IdGen.getUUID());
                        checkinException.setType("day");
                        checkinException.setEmpId(empId);
                        checkinException.setSummaryId(daySummary.getId());
                        checkinException.setException(exception);
                        checkinException.setDelFlag("0");
                        checkinException.setCreateDate(new Date());
                        checkinException.setCount(exceptionInfo.getInteger("count"));
                        checkinException.setDuration(exceptionInfo.getInteger("duration"));
                        checkinException.setUpdateDate(new Date());
                        checkinExceptionAddList.add(checkinException);
                    }

                }

                if(spItems!=null && spItems.size()>0){
                    for(int j=0;j<spItems.size();j++){
                        JSONObject spItem=spItems.getJSONObject(j);
                        int itemsType=spItem.getIntValue("type");
                        int vacationId=spItem.getIntValue("vacation_id");

                        PersOrgEmployeeCheckinItems checkinItems=new PersOrgEmployeeCheckinItems();
                        checkinItems.setId(IdGen.getUUID());
                        checkinItems.setSummaryId(daySummary.getId());
                        checkinItems.setType("day");
                        checkinItems.setEmpId(empId);
                        checkinItems.setItemsType(itemsType);
                        checkinItems.setVacationId(vacationId);
                        checkinItems.setDelFlag("0");
                        checkinItems.setCreateDate(new Date());
                        checkinItems.setCount(spItem.getInteger("count"));
                        checkinItems.setDuration(spItem.getInteger("duration"));
                        checkinItems.setTimeType(spItem.getInteger("time_type"));
                        checkinItemsAddList.add(checkinItems);
                    }
                }
            }

            if(checkinDaySummaryAddList.size()>0){
                Db.batchSave(checkinDaySummaryAddList,checkinDaySummaryAddList.size());
            }
            if(checkinExceptionAddList.size()>0){
                Db.batchSave(checkinExceptionAddList,checkinExceptionAddList.size());
            }

            if(checkinItemsAddList.size()>0){
                Db.batchSave(checkinItemsAddList,checkinItemsAddList.size());
            }
        }

        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            getCheckinDayData(pageNumber,pageSize,aa);
        }

    }

    public void fillCheckinIndex(){
        render("fillCheckinTaskIndex.html");
    }

    public void fillCheckinForm(){
        String taskId=getPara("taskId");
        if(StrKit.notBlank(taskId)){
            PersTask persTask=persTaskService.findByTaskId(taskId);
            PersOrgEmployeeCheckinFillCard fillCard= persOrgEmployeeCheckinFillCardService.findById(persTask.getRecordId());
            setAttr("fillCard",fillCard);
            String deptNames=persOrgService.getOrgParentNames(fillCard.getCheckDeptId());
            setAttr("deptNames",deptNames);
            PersOrgEmployee employee=persOrgEmployeeService.findById(fillCard.getEmpId());
            setAttr("employee",employee);
            if(StrKit.notBlank(fillCard.getCheckinRecordId())){
                PersOrgEmployeeCheckinRecord checkinRecord=persOrgEmployeeCheckinRecordService.findById(fillCard.getCheckinRecordId());
                setAttr("checkinRecord",checkinRecord);
            }
            /*else if("未打卡".equals(fillCard.getExceptions())){
                Calendar startWorkCheckinTimecalendar=Calendar.getInstance();
                startWorkCheckinTimecalendar.setTime(fillCard.getFillCardTime());
                startWorkCheckinTimecalendar.set(Calendar.HOUR_OF_DAY,0);
                startWorkCheckinTimecalendar.set(Calendar.SECOND,1);
                PersOrgEmployeeCheckinRecord checkinRecord=new PersOrgEmployeeCheckinRecord();
                checkinRecord.setCheckinTime(startWorkCheckinTimecalendar.getTime());
                checkinRecord.setCheckinType("上班打卡");
                checkinRecord.setExceptionType("未打卡");
                setAttr("checkinRecord",checkinRecord);
            }*/
            Map<String,Object> taskDetail=persApprovalService.getTaskDetail(taskId, AuthUtils.getUserId());
            JSONArray currentSteps=(JSONArray)taskDetail.get("currentSteps");
            //批准
            boolean allowApprove=false;
            //拒绝
            boolean allowReject=false;
            //中止
            boolean allowAbort=false;
            //提交
            boolean allowSubmit=false;
            if(currentSteps!=null){
                for(int i=0;i<currentSteps.size();i++){
                    JSONObject currentStep=currentSteps.getJSONObject(i);
                    boolean flag=false;
                    for(int j=0;j<currentStep.getJSONArray("UserIds").size();j++){
                        if(!flag && AuthUtils.getUserId().equalsIgnoreCase(currentStep.getJSONArray("UserIds").getString(j))){
                            flag=true;
                        }
                    }
                    if(flag){
                        allowApprove=currentStep.getBoolean("AllowApprove");
                        allowReject=currentStep.getBoolean("AllowReject");

                        allowSubmit=currentStep.getBoolean("AllowSubmit");
                    }
                    allowAbort=currentStep.getBoolean("AllowAbort");
                }
            }
            setAttr("isCurrentDeptOpinion",false);
            setAttr("isGroupHrOpinion",false);
            if(Global.fillCardStep1.equals((String)taskDetail.get("currentStepAlias"))){
                setAttr("isCurrentDeptOpinion",true);
            }else if(Global.fillCardStep2.equals((String)taskDetail.get("currentStepAlias"))){
                setAttr("isGroupHrOpinion",true);
            }
            setAttr("persTask",persTask);
            setAttr("stepts",taskDetail.get("stepts"));
            setAttr("taskId",taskId);
            setAttr("allowSubmit",allowSubmit);
            setAttr("allowApprove",allowApprove);
            setAttr("allowReject",allowReject);
            setAttr("allowAbort",allowAbort);
            if(fillCard == null || StrKit.notBlank(fillCard.getFileUrl())){
                setAttr("fileType",fillCard.getFileUrl().substring(fillCard.getFileUrl().lastIndexOf(".")+1));
                setAttr("fileName",fillCard.getFileUrl().substring(fillCard.getFileUrl().lastIndexOf("/")+1));
            }
            setAttr("currentStepAlias",(String)taskDetail.get("currentStepAlias"));
        }
        render("fillCheckinTaskForm.html");
    }

    public void getDayS(){
        String date=getPara("date");
        if(StrKit.isBlank(date)){
            renderJson(Ret.fail());
            return;
        }
        try {
            getCheckinDayData(1,80,DateUtils.parseDate(date));
            renderJson(Ret.ok("msg","success"));
            return;
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","执行异常"));
            return;
        }
    }

    public void getCheckinDayData(int pageNumber,int pageSize,Date date){
        String sql="  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
        Page<Record> recordPage = Db.paginate(pageNumber,pageSize,"select id,qiye_userid ",sql);
        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<Record> recordList=recordPage.getList();
        List<String> qiyeIds=new ArrayList<>();

        Map<String,String> empIdMap=new HashMap<>();

        for(Record record:recordList){
            empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
            qiyeIds.add(record.getStr("qiye_userid"));
        }
        String accessToken=qiYeWeiXinService.getCheckinAccessToken();

        Date summaryDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.getNextDay(date,0),"yyyy-MM-dd")+" 00:00:00");
        long startTime=summaryDate.getTime()/1000;
        long endTime=startTime;
        if(StrKit.isBlank(accessToken)){
            return;
        }
        String params="{\"starttime\":"+startTime+",\"endtime\":"+endTime+",\"useridlist\":"+JSON.toJSONString(qiyeIds)+"}";
        String resultStr= HttpKit.post(" https://qyapi.weixin.qq.com/cgi-bin/checkin/getcheckin_daydata?access_token="+accessToken,params);
        //logger.info("获取打卡日报："+resultStr);
        if(StrKit.isBlank(resultStr) || !resultStr.startsWith("{") || !resultStr.endsWith("}")){
            return;
        }
        JSONObject jsonObject= JSON.parseObject(resultStr);

        if(jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode")==0 ){
            JSONArray datas=jsonObject.getJSONArray("datas");

            List<PersOrgEmployeeCheckinDaySummary> checkinDaySummaryAddList=new ArrayList<>();

            List<PersOrgEmployeeCheckinException> checkinExceptionAddList=new ArrayList<>();

            List<PersOrgEmployeeCheckinItems> checkinItemsAddList=new ArrayList<>();

            for(int i=0;i<datas.size();i++){

                JSONObject data=datas.getJSONObject(i);
                JSONObject baseInfo=data.getJSONObject("base_info");
                JSONObject ruleInfo=baseInfo.getJSONObject("rule_info");
                JSONArray checkintimeArray=ruleInfo.getJSONArray("checkintime");

                String userid=baseInfo.getString("acctid");
                String empId=empIdMap.get(userid);
                JSONObject summaryInfo=data.getJSONObject("summary_info");
                JSONObject overworkInfo=data.getJSONObject("overwork_info");

                JSONArray exceptionInfos=data.getJSONArray("exception_infos");
                JSONArray spItems=data.getJSONArray("sp_items");

                //判断是否已添加
                PersOrgEmployeeCheckinDaySummary summary=persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId,summaryDate);
                if(summary!=null){
                    continue;
                }
                PersOrgEmployeeCheckinDaySummary daySummary=new PersOrgEmployeeCheckinDaySummary();
                daySummary.setId(IdGen.getUUID());
                daySummary.setSummaryDate(summaryDate);
                daySummary.setEmpId(empId);
                daySummary.setId(IdGen.getUUID());
                daySummary.setDelFlag("0");
                daySummary.setCreateDate(new Date());
                if(checkintimeArray!=null && checkintimeArray.size()>0){
                    JSONObject checkintime=checkintimeArray.getJSONObject(0);
                    int workSec=checkintime.getInteger("work_sec");
                    int offWorkSec=checkintime.getInteger("off_work_sec");
                    Calendar workSecCalendar=Calendar.getInstance();
                    workSecCalendar.setTime(summaryDate);
                    workSecCalendar.add(Calendar.SECOND,workSec);

                    Calendar offWorkSecCalendar=Calendar.getInstance();
                    offWorkSecCalendar.setTime(summaryDate);
                    offWorkSecCalendar.add(Calendar.SECOND,offWorkSec);

                    daySummary.setWorkSec(workSecCalendar.getTime());
                    daySummary.setOffWorkSec(offWorkSecCalendar.getTime());
                }
                daySummary.setDayType(baseInfo.getInteger("day_type"));
                daySummary.setCheckinCount(summaryInfo.getInteger("checkin_count"));
                daySummary.setRegularWorkSec(summaryInfo.getInteger("regular_work_sec"));
                daySummary.setStandardWorkSec(summaryInfo.getInteger("standard_work_sec"));
                Integer earliestTime=summaryInfo.getInteger("earliest_time");
                Integer lastestTime = summaryInfo.getInteger("lastest_time");
                if(earliestTime!=null){
                    Calendar earliestTimeCalendar=Calendar.getInstance();
                    earliestTimeCalendar.setTime(summaryDate);
                    earliestTimeCalendar.add(Calendar.SECOND,earliestTime);
                    daySummary.setEarliestTime(earliestTimeCalendar.getTime());
                }
                if(lastestTime!=null){
                    Calendar lastestTimeCalendar=Calendar.getInstance();
                    lastestTimeCalendar.setTime(summaryDate);
                    lastestTimeCalendar.add(Calendar.SECOND,lastestTime);
                    daySummary.setLastestTime(lastestTimeCalendar.getTime());
                }

                daySummary.setRecordType(baseInfo.getInteger("record_type"));
                daySummary.setGroupId(ruleInfo.getString("groupid"));
                daySummary.setGroupName(ruleInfo.getString("groupname"));
                if(exceptionInfos!=null && exceptionInfos.size()>0){
                    daySummary.setIsException("1");
                }else{
                    daySummary.setIsException("0");
                }
                daySummary.setUpdateDate(new Date());
                checkinDaySummaryAddList.add(daySummary);
                if(exceptionInfos!=null && exceptionInfos.size()>0){
                    for(int j=0;j<exceptionInfos.size();j++){
                        JSONObject exceptionInfo=exceptionInfos.getJSONObject(j);
                        int exception=exceptionInfo.getIntValue("exception");
                        PersOrgEmployeeCheckinException checkinException=new PersOrgEmployeeCheckinException();
                        checkinException.setId(IdGen.getUUID());
                        checkinException.setType("day");
                        checkinException.setEmpId(empId);
                        checkinException.setSummaryId(daySummary.getId());
                        checkinException.setException(exception);
                        checkinException.setDelFlag("0");
                        checkinException.setCreateDate(new Date());
                        checkinException.setCount(exceptionInfo.getInteger("count"));
                        checkinException.setDuration(exceptionInfo.getInteger("duration"));
                        checkinException.setUpdateDate(new Date());
                        checkinExceptionAddList.add(checkinException);
                    }

                }

                if(spItems!=null && spItems.size()>0){
                    for(int j=0;j<spItems.size();j++){
                        JSONObject spItem=spItems.getJSONObject(j);
                        int itemsType=spItem.getIntValue("type");
                        int vacationId=spItem.getIntValue("vacation_id");

                        PersOrgEmployeeCheckinItems checkinItems=new PersOrgEmployeeCheckinItems();
                        checkinItems.setId(IdGen.getUUID());
                        checkinItems.setSummaryId(daySummary.getId());
                        checkinItems.setType("day");
                        checkinItems.setEmpId(empId);
                        checkinItems.setItemsType(itemsType);
                        checkinItems.setVacationId(vacationId);
                        checkinItems.setDelFlag("0");
                        checkinItems.setCreateDate(new Date());
                        checkinItems.setCount(spItem.getInteger("count"));
                        checkinItems.setDuration(spItem.getInteger("duration"));
                        checkinItems.setTimeType(spItem.getInteger("time_type"));
                        checkinItemsAddList.add(checkinItems);
                    }
                }
            }

            if(checkinDaySummaryAddList.size()>0){
                Db.batchSave(checkinDaySummaryAddList,checkinDaySummaryAddList.size());
            }
            if(checkinExceptionAddList.size()>0){
                Db.batchSave(checkinExceptionAddList,checkinExceptionAddList.size());
            }

            if(checkinItemsAddList.size()>0){
                Db.batchSave(checkinItemsAddList,checkinItemsAddList.size());
            }
        }

        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            getCheckinDayData(pageNumber,pageSize,date);
        }

    }

    public void getCheckin(){
        String date=getPara("date");
        if(StrKit.isBlank(date)){
            renderJson(Ret.fail());
            return;
        }
        try {
            Date startTime=DateUtils.parseDate(date+" 00:00:00");
            Date endTime=DateUtils.parseDate(date+" 23:59:59");
            long lastTime=startTime.getTime();
            long currTime=endTime.getTime();
            getWorkWeiXinCheckinData(1,85,lastTime/1000,currTime/1000);
            renderJson(Ret.ok("msg","success"));
            return;
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail("msg","执行异常"));
            return;
        }

    }
    public void getWorkWeiXinCheckinData(int pageNumber,int pageSize,long startTime,long endTime){
        String sql="  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
        Page<Record> recordPage = Db.paginate(pageNumber,pageSize,"select id,qiye_userid ",sql);
        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<Record> recordList=recordPage.getList();
        List<String> qiyeIds=new ArrayList<>();

        Map<String,String> empIdMap=new HashMap<>();

        for(Record record:recordList){
            empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
            qiyeIds.add(record.getStr("qiye_userid"));
        }
        String resultStr=qiYeWeiXinService.getCheckinData(qiyeIds.toArray(),startTime,endTime);
        System.out.println("数据："+resultStr);
        if(StrKit.isBlank(resultStr) || !resultStr.startsWith("{") || !resultStr.endsWith("}")){
            logger.error("获取企业微信打卡记录失败"+resultStr);
            return;
        }
        JSONObject jsonObject= JSON.parseObject(resultStr);
        if(jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode")==0 ){
            JSONArray checkinDataArray=jsonObject.getJSONArray("checkindata");
            if(checkinDataArray!=null && checkinDataArray.size()>0){
                List<PersOrgEmployeeCheckinRecord> employeeCheckinRecordList=new ArrayList<>();
                String isRepeatSql="select * from pers_org_employee_checkin_record where del_flag='0' and emp_id=? and checkin_type=? and checkin_time=? ";
                for(int i=0;i<checkinDataArray.size();i++){
                    JSONObject checkinData=checkinDataArray.getJSONObject(i);

                    long checkinTime=checkinData.getLongValue("checkin_time");
                    //查询是否重复
                    Record repeatRecord=Db.findFirst(isRepeatSql,empIdMap.get(checkinData.getString("userid"))
                            ,checkinData.getString("checkin_type"),new Date(checkinTime*1000));
                    if(repeatRecord!=null){
                        //重复
                        continue;
                        /*if("上班打卡".equals(checkinData.getString("checkin_type")) || "外出打卡".equals(checkinData.getString("checkin_type")) ){
                            continue;
                        }else if("下班打卡".equals(checkinData.getString("checkin_type"))){
                            repeatRecord.setDelFlag("1");
                            repeatRecord.setUpdateDate(new Date());
                            employeeCheckinRecordUpdateList.add(repeatRecord);
                        }*/
                    }
                    PersOrgEmployeeCheckinRecord checkinRecord=new PersOrgEmployeeCheckinRecord();
                    checkinRecord.setId(IdGen.getUUID());
                    checkinRecord.setEmpId(empIdMap.get(checkinData.getString("userid")));
                    checkinRecord.setGroupname(checkinData.getString("groupname"));
                    checkinRecord.setCheckinType(checkinData.getString("checkin_type"));
                    checkinRecord.setExceptionType(checkinData.getString("exception_type"));

                    checkinRecord.setCheckinTime(new Date(checkinTime*1000));
                    checkinRecord.setLocationTitle(checkinData.getString("location_title"));
                    checkinRecord.setLocationDetail(checkinData.getString("location_detail"));
                    checkinRecord.setWifiname(checkinData.getString("wifiname"));
                    /*checkinRecord.setNotes(checkinData.getString("notes"));*/
                    checkinRecord.setWifimac(checkinData.getString("wifimac"));
                    checkinRecord.setMediaids(checkinData.getString("mediaids"));
                    checkinRecord.setLat(checkinData.getString("lat"));
                    checkinRecord.setLng(checkinData.getString("lng"));
                    checkinRecord.setDeviceid(checkinData.getString("deviceid"));
                    long schCheckinTime=checkinData.getLongValue("sch_checkin_time");
                    checkinRecord.setSchCheckinTime(new Date(schCheckinTime*1000));
                    checkinRecord.setGroupid(checkinData.getString("groupid"));
                    checkinRecord.setScheduleId(checkinData.getString("schedule_id"));
                    checkinRecord.setTimelineId(checkinData.getString("timeline_id"));
                    checkinRecord.setDelFlag("0");
                    checkinRecord.setCreateBy(AuthUtils.getUserId());
                    checkinRecord.setCreateDate(new Date());
                    checkinRecord.setUpdateBy(AuthUtils.getUserId());
                    checkinRecord.setUpdateDate(new Date());
                    employeeCheckinRecordList.add(checkinRecord);

                    /*try {
                        persOrgEmployeeCheckinRuleService.getEmpCheckinRuleConfig(checkinWxRecord.getEmpId(),checkinWxRecord);
                    }catch (Exception e){
                        logger.info(checkinWxRecord.getId()+"打卡记录计算异常"+e.getMessage());
                    }*/
                }
                Db.batchSave(employeeCheckinRecordList,employeeCheckinRecordList.size());
            }
        }else{
            logger.error("获取企业微信打卡记录失败"+resultStr);
            return;
        }
        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            getWorkWeiXinCheckinData(pageNumber,pageSize,startTime,endTime);
        }
    }

    public void genDaySumma(){
        String empId=getPara("empId");
        String date=getPara("date");
        try {
            persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(empId,DateUtils.parseDate(date));
            renderJson(Ret.ok());
        }catch (Exception e){
            e.printStackTrace();
            renderJson();
        }

    }

    public void regenerateCheckinRecord(){
        String wxRecordId=getPara("wxRecordId");
        if(StrKit.isBlank(wxRecordId)){
            renderJson(Ret.fail());
            return;
        }
        try {
            PersOrgEmployeeCheckinWxRecord record = persOrgEmployeeCheckinWxRecordService.findById(wxRecordId);
            persOrgEmployeeCheckinRuleService.getEmpCheckinRuleConfig(record.getEmpId(),record);
            renderJson(Ret.ok());
        }catch (Exception e){
            renderJson(Ret.fail("msg",e.toString()));
        }
    }

    public void regenerateCheckinDaySumm(){
        String empId=getPara("empId");
        String date=getPara("date");
        if(StrKit.isBlank(empId) || StrKit.isBlank(date)){
            renderJson(Ret.fail());
            return;
        }
        try {
            Date summaryDate=DateUtils.parseDate(date+" 00:00:00");
            PersOrgEmployeeCheckinDaySummary daySummary=persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId,summaryDate);
            if(daySummary!=null){
                daySummary.setDelFlag("1");
                daySummary.setUpdateBy(AuthUtils.getUserId());
                daySummary.setUpdateDate(new Date());
                daySummary.update();
            }
            persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(empId,summaryDate);
            renderJson(Ret.ok());
        }catch (Exception e){
            renderJson(Ret.fail("msg",e.toString()));
        }
    }


    public void genEmp(){
        String id=getPara("id");
        PersOrgEmployeeEntryInfo entryInfo=persOrgEmployeeEntryInfoService.findById(id);
        List<PersOrgEmployeeEducationalBackground> educationalBackgroundList=persOrgEmployeeEducationalBackgroundService.getEducationalBackgroundList(entryInfo.getEmployeeId());
        PersOrgEmployee employee=new PersOrgEmployee();
        employee.setId(entryInfo.getEmployeeId());
        employee.setIdCard(entryInfo.getIdcard());
        employee.setSex(entryInfo.getGender());
        if(entryInfo.getBirthday()!=null){
            employee.setBirthday(DateUtils.formatDate(entryInfo.getBirthday(),"yyyy-MM-dd"));
        }
        employee.setPhoneNum(entryInfo.getPhone());
        employee.setIdCardType(entryInfo.getIdcardType());
        employee.setNationality(entryInfo.getNationality());
        employee.setIdCardAddr(entryInfo.getResidentAddr());
        employee.setResidentAddr(entryInfo.getLiveAddr());
        employee.setEmployeeType(entryInfo.getEmployeeType());
        employee.setEntryTime(entryInfo.getEntryTime());
        employee.setEducation(entryInfo.getHighestEducation());
        employee.setIsStaySleep(entryInfo.getIsLiveCompany());
        employee.setFullName(entryInfo.getEmployeeName());
        employee.setArchiveStatus("incumbency");
        employee.setLinkPeople(entryInfo.getEmergencyContact());
        employee.setFormalDate(entryInfo.getFormalDate());
        employee.setLinkPhone(entryInfo.getEmergencyPhone());
        employee.setProbationSalary(entryInfo.getProbationSalary());
        employee.setSalary(entryInfo.getPermanentSalary());
        employee.setEntryChannel(entryInfo.getRecruitChannel());
        if(StrKit.notBlank(entryInfo.getIsLiveCompany())){
            employee.setIsStaySleep(entryInfo.getIsLiveCompany());
        }else{
            employee.setIsStaySleep("0");
        }
        if(educationalBackgroundList.size()>0){
            PersOrgEmployeeEducationalBackground educationalBackground=educationalBackgroundList.get(educationalBackgroundList.size()-1);
            employee.setGraduateSchool(educationalBackground.getSchool());
            employee.setSchoolMajor(educationalBackground.getMajor());
        }
        if(StrKit.notBlank(entryInfo.getTechnicalCertificate())){
            employee.setCertificate("1");
        }else{
            employee.setCertificate("0");
        }
        if(persOrgEmployeeService.employeeContractExist(null,null,entryInfo.getIdcard(),null,"quit")){
            employee.setIsReemployment("1");
        }else{
            employee.setIsReemployment("0");
        }


        List<PersOrgEmployeeRel> relList=new ArrayList<>();
        if(StrKit.notBlank(entryInfo.getDeptId())){
            PersOrgEmployeeRel deptRel=new PersOrgEmployeeRel();
            deptRel.setId(IdGen.getUUID());
            deptRel.setEmpId(employee.getId());
            deptRel.setRelationshipId(entryInfo.getDeptId());
            deptRel.setRelationshipType("dept");
            relList.add(deptRel);
            employee.setDeptId(entryInfo.getDeptId());
        }
        if(StrKit.notBlank(entryInfo.getPositionId())){
            PersOrgEmployeeRel positionRel=new PersOrgEmployeeRel();
            positionRel.setId(IdGen.getUUID());
            positionRel.setEmpId(employee.getId());
            positionRel.setRelationshipId(entryInfo.getPositionId());
            positionRel.setRelationshipType("position");
            relList.add(positionRel);
        }
        if(StrKit.notBlank(entryInfo.getRoleId())){
            PersOrgEmployeeRel roleRel=new PersOrgEmployeeRel();
            roleRel.setId(IdGen.getUUID());
            roleRel.setEmpId(employee.getId());
            roleRel.setRelationshipId(entryInfo.getRoleId());
            roleRel.setRelationshipType("role");
            relList.add(roleRel);
        }

        //创建员工账号
        boolean flag=persOrgEmployeeService.saveOrgEmployee(employee,relList,new ArrayList<>());
        if(flag){
            renderJson(Ret.ok());
        }else{
            renderJson(Ret.fail());
        }
    }

    public void getEmpCheckinRuleConfig(){
        String empId=getPara("empId");
        String date=getPara("date");
        if(StrKit.isBlank(empId) || StrKit.isBlank(date)){
            renderJson(Ret.fail());
            return;
        }
        PersOrgEmployeeCheckinWxRecord wxRecord=persOrgEmployeeCheckinWxRecordService.findById("0046AEC3-074E-489C-8B39-78DB65E819B6");
        wxRecord.setEmpId(empId);
        wxRecord.setCheckinTime(DateUtils.parseDate(date));
        try {
            persOrgEmployeeCheckinRuleService.getEmpCheckinRuleConfig(wxRecord.getEmpId(),wxRecord);
            renderJson(Ret.ok());
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail());
        }
    }

    public void testCheckin(){

        String empId=getPara("empId");
        String date=getPara("date");
        if(StrKit.isBlank(empId) || StrKit.isBlank(date)){
            renderJson(Ret.fail());
            return;
        }
        PersOrgEmployeeCheckinWxRecord wxRecord=persOrgEmployeeCheckinWxRecordService.getWxCheckinRecord(empId);
        wxRecord.setEmpId(empId);
        wxRecord.setCheckinTime(DateUtils.parseDate(date));
        try {
            persOrgEmployeeCheckinRuleService.getEmpDelayCheckinRuleConfig(wxRecord.getEmpId(),wxRecord);
            renderJson(Ret.ok());
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail());
        }
    }

    public void employeeRestOverTimeCheckin(){


    }


    public void delayCheckin(){

        List<PersOrgEmployeeCheckinWxRecord> employeeCheckinWxRecordList=new ArrayList<>();
        try {
            String empId=getPara("empId");
            String date=getPara("date");
            Calendar startCalendar=Calendar.getInstance();
            startCalendar.setTime(DateUtils.parseDate(date+" 00:00:00"));
            Calendar endCalendar=Calendar.getInstance();
            endCalendar.setTime(DateUtils.parseDate(date+" 23:59:59"));
            long startTime=startCalendar.getTime().getTime()/1000;
            long endTime=endCalendar.getTime().getTime()/1000;


            String sql="select id,qiye_userid  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null and id =? ";
            List<Record> recordList = Db.find(sql,empId);
            if(recordList==null || recordList.size()==0){
                return;
            }
            List<String> qiyeIds=new ArrayList<>();

            Map<String,String> empIdMap=new HashMap<>();

            for(Record record:recordList){
                empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
                qiyeIds.add(record.getStr("qiye_userid"));
            }
            String resultStr=qiYeWeiXinService.getCheckinData(qiyeIds.toArray(),startTime,endTime);
            System.out.println("数据："+resultStr);
            if(StrKit.isBlank(resultStr) || !resultStr.startsWith("{") || !resultStr.endsWith("}")){
                logger.error("获取企业微信打卡记录失败"+resultStr);
                return;
            }
            JSONObject jsonObject= JSON.parseObject(resultStr);
            if(jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode")==0 ){
                JSONArray checkinDataArray=jsonObject.getJSONArray("checkindata");
                if(checkinDataArray!=null && checkinDataArray.size()>0){

                    String isRepeatSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_type=? and checkin_time=? ";
                    for(int i=0;i<checkinDataArray.size();i++){
                        JSONObject checkinData=checkinDataArray.getJSONObject(i);

                        long checkinTime=checkinData.getLongValue("checkin_time");
                        //查询是否重复
                        Record repeatRecord=Db.findFirst(isRepeatSql,empIdMap.get(checkinData.getString("userid"))
                                ,checkinData.getString("checkin_type"),new Date(checkinTime*1000));
                        if("未打卡".equals(checkinData.getString("exception_type"))){
                            continue;
                        }
                        if(repeatRecord!=null){
                            //重复
                            continue;
                        }
                        PersOrgEmployeeCheckinWxRecord checkinWxRecord=new PersOrgEmployeeCheckinWxRecord();
                        checkinWxRecord.setId(IdGen.getUUID());
                        checkinWxRecord.setEmpId(empIdMap.get(checkinData.getString("userid")));
                        checkinWxRecord.setGroupname(checkinData.getString("groupname"));
                        checkinWxRecord.setCheckinType(checkinData.getString("checkin_type"));
                        checkinWxRecord.setExceptionType(checkinData.getString("exception_type"));

                        checkinWxRecord.setCheckinTime(new Date(checkinTime*1000));
                        checkinWxRecord.setLocationTitle(checkinData.getString("location_title"));
                        checkinWxRecord.setLocationDetail(checkinData.getString("location_detail"));
                        checkinWxRecord.setWifiname(checkinData.getString("wifiname"));
                        /*checkinWxRecord.setNotes(checkinData.getString("notes"));*/
                        checkinWxRecord.setWifimac(checkinData.getString("wifimac"));
                        checkinWxRecord.setMediaids(checkinData.getString("mediaids"));
                        checkinWxRecord.setLat(checkinData.getString("lat"));
                        checkinWxRecord.setLng(checkinData.getString("lng"));
                        checkinWxRecord.setDeviceid(checkinData.getString("deviceid"));
                        long schCheckinTime=checkinData.getLongValue("sch_checkin_time");
                        checkinWxRecord.setSchCheckinTime(new Date(schCheckinTime*1000));
                        checkinWxRecord.setGroupid(checkinData.getString("groupid"));
                        checkinWxRecord.setScheduleId(checkinData.getIntValue("schedule_id"));
                        checkinWxRecord.setTimelineId(checkinData.getIntValue("timeline_id"));
                        checkinWxRecord.setDelFlag("0");
                        checkinWxRecord.setCreateDate(new Date());
                        checkinWxRecord.setUpdateDate(new Date());
                        employeeCheckinWxRecordList.add(checkinWxRecord);

                        try {
                            logger.info(checkinWxRecord.getId()+"断网打卡记录");
                            persOrgEmployeeCheckinRuleService.getEmpDelayCheckinRuleConfig(checkinWxRecord.getEmpId(),checkinWxRecord);
                        }catch (Exception e){
                            e.printStackTrace();
                            logger.info(checkinWxRecord.getId()+"打卡记录计算异常"+e.getMessage());
                        }
                    }
                    Db.batchSave(employeeCheckinWxRecordList,employeeCheckinWxRecordList.size());
                }
            }else{
                logger.error("获取企业微信打卡记录失败"+resultStr);
                return;
            }
            renderJson(Ret.ok("data",employeeCheckinWxRecordList));
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail());
        }

    }

    public static void main(String[] args) {
        String str="[\"42BCC1C3-5830-4193-88CC-675190BC3E6D\",\n" +
                "\"52991767-9D6F-4F72-A45B-1712D1C88742\",\n" +
                "\"A0701F18-B155-4625-A16A-227569B8470D\",\n" +
                "\"C76E0740-5E1A-495E-87F8-D88945092CDD\",\n" +
                "\"54493AEB-4E8B-4BEE-83F8-2ADF633B9211\",\n" +
                "\"83A981BA-A5EB-4CB8-AD1C-0C468268671F\",\n" +
                "\"63C876F2-8776-454B-B66F-4919798F6B13\",\n" +
                "\"4615443C-8BC3-4F3F-9B71-796320608215\",\n" +
                "\"BC47060C-2536-4E55-97D3-2E8C0EF6E64D\",\n" +
                "\"F4F6AD2E-9639-4B63-8D39-69069667C0A7\",\n" +
                "\"FD29B259-0097-4DD1-AA14-31E6958C5651\",\n" +
                "\"046EF007-E415-4096-9A4A-A30ACEADE73F\",\n" +
                "\"53F8D556-2B37-4AB6-9DA5-F1EFBF24BFE3\",\n" +
                "\"12C00CAA-29CA-4A50-A161-D9D3623F7314\",\n" +
                "\"92FCE833-181C-409A-B29C-1355CB3E3151\",\n" +
                "\"E882C83B-2FD4-4F05-91AE-CB4C054AEBFC\",\n" +
                "\"0F9C8158-3144-459F-9208-DA38ED881045\",\n" +
                "\"FB7EAAED-B295-4F5F-9F39-C0BF5270E0F5\",\n" +
                "\"A18C2AD2-65A0-4781-9855-90A9EAEC39B1\",\n" +
                "\"052315D1-241C-4CF2-89D7-F4A3C659B94F\",\n" +
                "\"898F3BB1-C645-4218-88DB-8988F42D0FD3\",\n" +
                "\"2EED082E-761C-4EFF-A1C1-93456F5C07BD\",\n" +
                "\"E035F0B2-F387-4BC1-8866-4181BDB695B2\"]";

        JSONArray jsonArray=JSON.parseArray(str);
        for (int i = 0; i < jsonArray.size(); i++) {
            String empId=jsonArray.getString(i);
            System.out.println("http://hrm.cncsgroup.com/employeeCheckin/delayCheckin?empId="+empId+"&date=2023-10-04");

        }

    }

    public void aaaaaa(){
        try {
            String date=getPara("date");
            String isGenSumm=getPara("isGenSumm");
            Calendar startCalendar=Calendar.getInstance();
            startCalendar.setTime(DateUtils.parseDate(date+" 00:00:00"));
            Calendar endCalendar=Calendar.getInstance();
            endCalendar.setTime(DateUtils.parseDate(date+" 23:59:59"));
            persOrgEmployeeCheckinWxRecordService.getWorkWeiXinCheckinData(1,95,startCalendar.getTime().getTime()/1000,endCalendar.getTime().getTime()/1000);
            if("1".equals(isGenSumm)){
                vvvvv(1,80,startCalendar.getTime());
            }
            renderJson(Ret.ok());
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail());
        }
    }

    public void vvvvv(int pageNumber,int pageSize,Date date){
        String sql="  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
        Page<Record> recordPage = Db.paginate(pageNumber,pageSize,"select id,qiye_userid ",sql);
        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<Record> recordList=recordPage.getList();
        List<String> qiyeIds=new ArrayList<>();

        Map<String,String> empIdMap=new HashMap<>();

        for(Record record:recordList){
            empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
            qiyeIds.add(record.getStr("qiye_userid"));
        }
        String accessToken=qiYeWeiXinService.getCheckinAccessToken();

        for(Record record:recordPage.getList()){
            persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(record.getStr("id"),date);
        }

        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            vvvvv(pageNumber,pageSize,date);
        }
    }

    public void testRule(){
        String empId=getPara("empId");
        String date=getPara("date");
        PersOrgEmployeeCheckinRule checkinRule =persOrgEmployeeCheckinRuleService.getEmpTopCheckinRuleByDate(empId,DateUtils.parseDate(date));
        renderJson(checkinRule);
    }

    public void aaaaaa2(){
        try {
            String empIds=getPara("empIds");
            String date=getPara("date");
            Calendar startCalendar=Calendar.getInstance();
            startCalendar.setTime(DateUtils.parseDate(date+" 00:00:00"));
            Calendar endCalendar=Calendar.getInstance();
            endCalendar.setTime(DateUtils.parseDate(date+" 23:59:59"));
            long startTime=startCalendar.getTime().getTime()/1000;
            long endTime=endCalendar.getTime().getTime()/1000;

            List<Object> params=new ArrayList<>();
            if(StrKit.isBlank(empIds)){
                renderJson("id不能为空");
                return;
            }
            String[] ids=null;
            if(empIds.indexOf(",")==-1){
                ids=new String[]{empIds};
            }else{
                ids=empIds.split(",");
            }
            String str="";
            for(String id:ids){
                str+="?,";
            }
            str=str.substring(0,str.length()-1);

            String sql="select id,qiye_userid  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null and id in ("+str+") ";
            List<Record> recordList = Db.find(sql,ids);
            if(recordList==null || recordList.size()==0){
                return;
            }
            List<String> qiyeIds=new ArrayList<>();

            Map<String,String> empIdMap=new HashMap<>();

            for(Record record:recordList){
                empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
                qiyeIds.add(record.getStr("qiye_userid"));
            }
            String resultStr=qiYeWeiXinService.getCheckinData(qiyeIds.toArray(),startTime,endTime);
            System.out.println("数据："+resultStr);
            if(StrKit.isBlank(resultStr) || !resultStr.startsWith("{") || !resultStr.endsWith("}")){
                logger.error("获取企业微信打卡记录失败"+resultStr);
                return;
            }
            JSONObject jsonObject= JSON.parseObject(resultStr);
            if(jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode")==0 ){
                JSONArray checkinDataArray=jsonObject.getJSONArray("checkindata");
                if(checkinDataArray!=null && checkinDataArray.size()>0){
                    List<PersOrgEmployeeCheckinWxRecord> employeeCheckinWxRecordList=new ArrayList<>();
                    String isRepeatSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_type=? and checkin_time=? ";
                    for(int i=0;i<checkinDataArray.size();i++){
                        JSONObject checkinData=checkinDataArray.getJSONObject(i);

                        long checkinTime=checkinData.getLongValue("checkin_time");
                        //查询是否重复
                        Record repeatRecord=Db.findFirst(isRepeatSql,empIdMap.get(checkinData.getString("userid"))
                                ,checkinData.getString("checkin_type"),new Date(checkinTime*1000));
                        if("未打卡".equals(checkinData.getString("exception_type"))){
                            continue;
                        }
                        if(repeatRecord!=null){
                            //重复
                            continue;
                        }
                        PersOrgEmployeeCheckinWxRecord checkinWxRecord=new PersOrgEmployeeCheckinWxRecord();
                        checkinWxRecord.setId(IdGen.getUUID());
                        checkinWxRecord.setEmpId(empIdMap.get(checkinData.getString("userid")));
                        checkinWxRecord.setGroupname(checkinData.getString("groupname"));
                        checkinWxRecord.setCheckinType(checkinData.getString("checkin_type"));
                        checkinWxRecord.setExceptionType(checkinData.getString("exception_type"));

                        checkinWxRecord.setCheckinTime(new Date(checkinTime*1000));
                        checkinWxRecord.setLocationTitle(checkinData.getString("location_title"));
                        checkinWxRecord.setLocationDetail(checkinData.getString("location_detail"));
                        checkinWxRecord.setWifiname(checkinData.getString("wifiname"));
                        /*checkinWxRecord.setNotes(checkinData.getString("notes"));*/
                        checkinWxRecord.setWifimac(checkinData.getString("wifimac"));
                        checkinWxRecord.setMediaids(checkinData.getString("mediaids"));
                        checkinWxRecord.setLat(checkinData.getString("lat"));
                        checkinWxRecord.setLng(checkinData.getString("lng"));
                        checkinWxRecord.setDeviceid(checkinData.getString("deviceid"));
                        long schCheckinTime=checkinData.getLongValue("sch_checkin_time");
                        checkinWxRecord.setSchCheckinTime(new Date(schCheckinTime*1000));
                        checkinWxRecord.setGroupid(checkinData.getString("groupid"));
                        checkinWxRecord.setScheduleId(checkinData.getIntValue("schedule_id"));
                        checkinWxRecord.setTimelineId(checkinData.getIntValue("timeline_id"));
                        checkinWxRecord.setDelFlag("0");
                        checkinWxRecord.setCreateDate(new Date());
                        checkinWxRecord.setUpdateDate(new Date());
                        employeeCheckinWxRecordList.add(checkinWxRecord);

                        try {
                            persOrgEmployeeCheckinRuleService.getEmpCheckinRuleConfig(checkinWxRecord.getEmpId(),checkinWxRecord);
                        }catch (Exception e){
                            e.printStackTrace();
                            logger.info(checkinWxRecord.getId()+"打卡记录计算异常"+e.getMessage());
                        }
                    }
                    Db.batchSave(employeeCheckinWxRecordList,employeeCheckinWxRecordList.size());
                }
            }else{
                logger.error("获取企业微信打卡记录失败"+resultStr);
                return;
            }


            if(!"12-01".equals(DateUtils.formatDate(startCalendar.getTime(),"MM-dd"))){
                for(Record record:recordList){
                    persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(record.getStr("id"),startCalendar.getTime());
                }
            }
            renderJson(Ret.ok());
        }catch (Exception e){
            e.printStackTrace();
            renderJson(Ret.fail());
        }
    }

    public void genOverDaySumm(){
        String sql="select * from pers_org_employee_over_time_apply where del_flag='0' order by start_time ";
        List<Record> recordList=Db.find(sql);
        String str="usc,";
        for(Record record:recordList){
            String empId=record.getStr("emp_id");
            Date date=record.getDate("start_time");
            try {

                PersOrgEmployeeCheckinDaySummary daySummary=persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(empId,date);
                if(daySummary==null){
                    persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(empId,date);
                }
            }catch (Exception e){
                e.printStackTrace();
                str+="empId:"+empId+",date:"+DateUtils.formatDate(date,"yyyy-MM-dd");
            }
        }
        renderJson(str);
    }

    public void tests(){
        String startDateS=getPara("startDate");
        String endDateS=getPara("endDate");
        Date startDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.parseDate(startDateS),"yyyy-MM-dd")+" 00:00:00");
        Date endDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.parseDate(endDateS),"yyyy-MM-dd")+" 00:00:00");
        long curr=System.currentTimeMillis();
        persOrgEmployeeCheckinWxRecordService.getWorkWeiXinDelayCheckinData(1,90,startDate.getTime()/1000,endDate.getTime()/1000);
        renderJson("ok");
    }


    public void empCheckinDaySummaryExport4(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String exportType=getPara("exportType");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        Date startDate=DateUtils.parseDate(startDateStr);
        Date endDate=DateUtils.parseDate(endDateStr);

        List<Date> dateList=DateUtils.getBetweenDates(startDate,endDate);
        if(StrKit.isBlank(exportType)){
            if(dateList.size()>31){
                renderJson(Ret.fail("msg","导出时间不能超过31天"));
                return;
            }
        }



        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }
        String deptName="";
        if(ids.size()>0){
            deptName=persOrgService.getOrgParentNames(ids.get(0));
            if(deptName.indexOf("昌松集团/旅居运营部/")!=-1){
                deptName=deptName.replace("昌松集团/旅居运营部/","");
            }else if(deptName.indexOf("昌松集团/市场部/")!=-1){
                deptName=deptName.replace("昌松集团/市场部/","");
            }else if(deptName.indexOf("昌松集团/")!=-1){
                deptName=deptName.replace("昌松集团/","");
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList2(1,10000,null,fullName,startDateStr,endDateStr,archiveStatus,ids,AuthUtils.getUserId());
        List<Record> recordList=pageList.getList();

        List<Object> params=new ArrayList<>();
        String s="";
        for(int i=0;i<recordList.size();i++){
            String empId=recordList.get(i).getStr("id");
            params.add(empId);
            s+="?,";
        }
        s=s.substring(0,s.length()-1);
        params.add(startDateStr+" 00:00:00");
        params.add(endDateStr+" 23:59:59");

        String checkinRecordSql="select a.emp_id as empId,a.id,a.checkin_type as checkinType,a.exception_type as exceptionType " +
                ",a.checkin_time as checkinTime,a.sch_checkin_time as schCheckinTime,b.`status`,a.location_title as locationTitle " +
                "from pers_org_employee_checkin_record a left join   pers_org_employee_checkin_fill_card b on a.id=b.checkin_record_id and b.`status`='2' " +
                "where a.emp_id in ("+s+") and a.checkin_time between ? and ? and a.del_flag='0' order by a.emp_id,a.checkin_time ";
        List<Record> allCheckinRecordList=Db.find(checkinRecordSql,params.toArray());


        /*String overTimeApplySql="select * from  pers_org_employee_over_time_apply where del_flag='0' and emp_id in ("+s+") and  start_time BETWEEN ? and ?   ";
        String leaveRestApplySql="select * from  pers_org_employee_leave_rest_apply where del_flag='0' and emp_id in ("+s+") and NOT ( (end_time < ?) OR (start_time > ?) ) ";

        List<PersOrgEmployeeOverTimeApply> allOverTimeApplyList=persOrgEmployeeOverTimeApplyService.findBySql(overTimeApplySql,params.toArray());
        List<PersOrgEmployeeLeaveRestApply> allLeaveRestApplyList=persOrgEmployeeLeaveRestApplyService.findBySql(leaveRestApplySql,params.toArray());

        Map<String,List<PersOrgEmployeeOverTimeApply>> allOverTimeApplyMap=new HashMap<>();
        Map<String,List<PersOrgEmployeeLeaveRestApply>> allLeaveRestApplyMap=new HashMap<>();

        for (PersOrgEmployeeOverTimeApply overTimeApply : allOverTimeApplyList) {
            if(allOverTimeApplyMap.containsKey(overTimeApply.getEmpId())){
                allOverTimeApplyMap.get(overTimeApply.getEmpId()).add(overTimeApply);
            }else{
                List<PersOrgEmployeeOverTimeApply> overTimeApplyList=new ArrayList<>();
                overTimeApplyList.add(overTimeApply);
                allOverTimeApplyMap.put(overTimeApply.getEmpId(),overTimeApplyList);
            }

        }
        for (PersOrgEmployeeLeaveRestApply leaveRestApply : allLeaveRestApplyList) {
            if(allLeaveRestApplyMap.containsKey(leaveRestApply.getEmpId())){
                allLeaveRestApplyMap.get(leaveRestApply.getEmpId()).add(leaveRestApply);
            }else{
                List<PersOrgEmployeeLeaveRestApply> leaveRestApplyList=new ArrayList<>();
                leaveRestApplyList.add(leaveRestApply);
                allLeaveRestApplyMap.put(leaveRestApply.getEmpId(),leaveRestApplyList);
            }

        }*/


        Map<String,List<Record>> empCheckinMap=new HashMap<>();
        for(Record record:allCheckinRecordList){
            if(empCheckinMap.containsKey(record.getStr("empId"))){
                List<Record> records=empCheckinMap.get(record.getStr("empId"));
                records.add(record);
            }else{
                List<Record> records=new ArrayList<>();
                records.add(record);
                empCheckinMap.put(record.getStr("empId"),records);
            }
        }
        String fileName="";
        if(StrKit.notBlank(deptName)){
            fileName=deptName+"员工考勤"+DateUtils.formatDate(startDate,"yyyy-MM")+"月份"+"汇总.xlsx";
        }else{
            fileName="员工考勤"+DateUtils.formatDate(startDate,"yyyy-MM")+"月份"+"汇总.xlsx";
        }


        try {
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\kaoqintongji.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            //System.gc();
            XSSFSheet sheet = workbook.getSheet("考勤汇总");
            int dateSize=dateList.size()+5;
            int cellSize=35;//最后一个日期列index
            while (dateSize<=cellSize){
                //删除列
                deleteColumn(sheet,cellSize,recordList.size()+20);
                cellSize--;
            }
            if(recordList.size()>1){
                //创建行数
                insertRow(workbook,sheet,4,recordList.size()-1);
            }
            if(StrKit.notBlank(deptName)){
                XSSFRow row0=sheet.getRow(0);
                XSSFCell row0Cell1=row0.getCell(1);
                row0Cell1.setCellValue(deptName+"员工"+DateUtils.formatDate(startDate,"MM")+"月考勤统计汇总表");
            }else{
                XSSFRow row0=sheet.getRow(0);
                XSSFCell row0Cell1=row0.getCell(1);
                row0Cell1.setCellValue("员工"+DateUtils.formatDate(startDate,"MM")+"月考勤统计汇总表");
            }

            XSSFRow row1=sheet.getRow(1);
            XSSFCell row1Cell1=row1.getCell(1);
            String row1Cell1Str=row1Cell1.getStringCellValue();
            if(StrKit.notBlank(row1Cell1Str)){
                row1Cell1.setCellValue(row1Cell1Str.replace("${date}",DateUtils.formatDate(startDate,"yyyy年MM月份")));
            }



            XSSFRow row2=sheet.getRow(2);
            XSSFRow row3=sheet.getRow(3);
            String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
            //处理日期、星期
            for(int i=5;i<=cellSize;i++){
                Date date=dateList.get(i-5);
                XSSFCell dayCell=row2.getCell(i);
                XSSFCell weekCell=row3.getCell(i);
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(date);
                dayCell.setCellValue(Integer.valueOf(calendar.get(Calendar.DAY_OF_MONTH)));
                weekCell.setCellValue(weekDays[calendar.get(Calendar.DAY_OF_WEEK)-1]);
            }




            int rowIndex=3;
            for(Record record:recordList){
                rowIndex++;

                String pontionName=Db.queryStr("select position_name from pers_position p where id in (select relationship_id from pers_org_employee_rel where relationship_type='position'" +
                        "and emp_id=? ) and org_id=? ",record.getStr("id"),record.getStr("orgId"));


                String empId=record.getStr("id");
                XSSFRow row=sheet.getRow(rowIndex);
                XSSFCell cell0=row.getCell(0);
                cell0.setCellValue(rowIndex-3);
                row.getCell(1).setCellValue(record.getStr("orgName"));
                row.getCell(2).setCellValue(pontionName);
                row.getCell(3).setCellValue(record.getStr("fullName"));
                if("male".equals(record.getStr("sex"))){
                    row.getCell(4).setCellValue("男");
                }else if("female".equals(record.getStr("sex"))){
                    row.getCell(4).setCellValue("女");
                }else{
                    row.getCell(4).setCellValue("");
                }
                List<Record> checkinRecordList=empCheckinMap.get(empId);

                Map<String,Integer> dayTypeMap=(Map<String,Integer>)record.get("dayTypeMap");
                Map<String,String> typeMap=(Map<String,String>)record.get("typeMap");
                Map<String,String> exceptionDayType=record.get("exceptionDayType");
                int i=4;
                PersOrgEmployee employee=persOrgEmployeeService.findById(empId);
                String deptId=employee.getFirstDepeId();
                boolean flag=true;
                if(StrKit.notBlank(deptId)){
                    PersOrg org=persOrgService.findById(deptId);
                    if("branche_office".equals(org.getOrgType())){
                        flag=true;
                    }
                }
                for(Date date:dateList){
                    i++;
                    String dateStr=DateUtils.formatDate(date,"yyyy-MM-dd");
                    Integer type=dayTypeMap.get(dateStr);

                    String checkinStr="";
                    if(type!=null){
                        if(type==0 || type==4 || type==2 || type==6){
                            if(checkinRecordList!=null && checkinRecordList.size()>0){
                                if(type!=null && 2==type){
                                    checkinStr+="全天加班："+typeMap.get(DateUtils.formatDate(date,"yyyy-MM-dd"))+"\n";
                                }
                                if(type!=null && 4==type){
                                    checkinStr+="外派："+typeMap.get(DateUtils.formatDate(date,"yyyy-MM-dd"))+"\n";
                                }
                                if(type!=null && 6==type){
                                    checkinStr+="出差："+typeMap.get(DateUtils.formatDate(date,"yyyy-MM-dd"))+"\n";
                                }

                                for(Record checkinRecord : checkinRecordList){
                                    if(DateUtils.formatDate(checkinRecord.getDate("checkinTime")).equals(DateUtils.formatDate(date,"yyyy-MM-dd"))){
                                        String checkinType="";
                                        if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            checkinType="上：";
                                        }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                            checkinType="下：";
                                        }
                                        String fillCardStatus="";
                                        if("2".equals(checkinRecord.getStr("status"))){
                                            fillCardStatus="(补卡成功)";
                                        }
                                        if(StrKit.notBlank(checkinRecord.getStr("exceptionType"))){
                                            if("未打卡".equals(checkinRecord.getStr("exceptionType"))){
                                                checkinStr+=checkinType+"未打卡\n";
                                            }else{
                                                String timeException="";
                                                if("上班打卡".equals(checkinRecord.getStr("checkinType"))){
                                                    //计算迟到
                                                    int minute= PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("checkinTime"),checkinRecord.getDate("schCheckinTime"));
                                                    if(minute>0){
                                                        timeException="迟到:"+(minute)+"分钟";
                                                    }
                                                }else if("下班打卡".equals(checkinRecord.getStr("checkinType"))){
                                                    //计算早退
                                                    int minute=PersOrgEmployeeCheckinRuleServiceImpl.getDatePoor(checkinRecord.getDate("schCheckinTime"),checkinRecord.getDate("checkinTime"));
                                                    if(minute>0){
                                                        timeException="早退:"+(minute)+"分钟";
                                                    }
                                                }
                                            /*if(showAddDeptIsList.contains(deptId)){
                                                str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")
                                                        +"("+timeException+")"+fillCardStatus+"\n"+checkinRecord.getStr("locationTitle")+"\n";
                                            }else{*/
                                                checkinStr+=checkinType+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+"("+timeException+")"+fillCardStatus+"\n";
                                                /*}*/

                                            }
                                        }else{
                                        /*if(showAddDeptIsList.contains(deptId)){
                                            str+=type+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n"+
                                                    checkinRecord.getStr("locationTitle")+"\n";
                                        }else{*/
                                            checkinStr+=checkinType+DateUtils.formatDate(checkinRecord.getDate("checkinTime"),"HH:mm")+fillCardStatus+"\n";
                                            /*}*/

                                        }
                                    }
                                }

                            }
                        }
                        if(type==0 || type==4){
                            //工作日
                            row.getCell(i).setCellValue("√");
                            String exceptionType=exceptionDayType.get(dateStr);
                            if(StrKit.notBlank(exceptionType)){
                                if("迟到".equals(exceptionType)){
                                    row.getCell(i).setCellValue("C");
                                }else if("早退".equals(exceptionType)){
                                    row.getCell(i).setCellValue("Z");
                                }else if("旷工".equals(exceptionType)){
                                    row.getCell(i).setCellValue("K");
                                }
                            }

                            //获取加班记录
                            PersOrgEmployeeOverTimeApply overTimeApply=null;
                            if(flag){
                                overTimeApply=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordByStatus(empId,date,new String[]{"3"});
                            }

                            String commentStr="";
                            if(overTimeApply!=null){
                                PersOverTimeType overTimeType=PersOverTimeType.getTypeByKey(overTimeApply.getOverType());
                                commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                        +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")
                                        +"加班"+secondToTime(overTimeApply.getOverSecond())+"，类型："+overTimeType.getValue();
                            }
                            /*List<PersOrgEmployeeOverTimeApply> overTimeApplyList=persOrgEmployeeOverTimeApplyService.getOverTimeApplyListByMap(allOverTimeApplyMap,empId,date);
                            String commentStr="";
                            for (PersOrgEmployeeOverTimeApply overTimeApply : overTimeApplyList) {
                                PersOverTimeType overTimeType=PersOverTimeType.getTypeByKey(overTimeApply.getOverType());
                                commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                        +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")
                                        +"加班"+secondToTime(overTimeApply.getOverSecond())+"，类型："+overTimeType.getValue();
                                commentStr+="\n";
                            }
*/

                            //获取请休假记录
                            List<PersOrgEmployeeLeaveRestApply> leaveRestApplyList=null;
                            if(flag){
                                leaveRestApplyList=persOrgEmployeeLeaveRestApplyService
                                        .getEmpLeaveRestApplyList(empId,DateUtils.formatDate(date,"yyyy-MM-dd HH:mm:ss"));
                                //leaveRestApplyList=persOrgEmployeeLeaveRestApplyService.getLeaveRestApplyListByMap(allLeaveRestApplyMap,empId,date);
                            }

                            if(leaveRestApplyList!=null && leaveRestApplyList.size()>0){
                                if(leaveRestApplyList.size()>1) {
                                    boolean isMorningLeaveRest=false;
                                    boolean isAfternoonLeaveRest=false;
                                    PersLeaveRestType restType=null;

                                    PersOrgEmployeeLeaveRestApply morningLeaveRest=null;
                                    PersOrgEmployeeLeaveRestApply afternoonLeaveRest=null;
                                    for(PersOrgEmployeeLeaveRestApply leaveRestApply:leaveRestApplyList){
                                        if("1".equals(leaveRestApply.getLeaveRestDateType())){
                                            String[] array= PersOrgEmployeeLeaveRestApplyServiceImpl.getLeaveRestApplyInfo(leaveRestApply,date);
                                            if("1".equals(array[0])){
                                                isMorningLeaveRest=true;
                                                isAfternoonLeaveRest=true;
                                                break;
                                            }else{
                                                if("3".equals(array[1])){
                                                    isMorningLeaveRest=true;
                                                    restType=PersLeaveRestType.getTypeByKey(leaveRestApply.getLeaveRestType());
                                                    morningLeaveRest=leaveRestApply;
                                                }else if("4".equals(array[1])){
                                                    isAfternoonLeaveRest=true;
                                                    restType=PersLeaveRestType.getTypeByKey(leaveRestApply.getLeaveRestType());
                                                    afternoonLeaveRest=leaveRestApply;
                                                }
                                            }
                                        }
                                    }
                                    if(isMorningLeaveRest && isAfternoonLeaveRest){
                                        row.getCell(i).setCellValue("2*BT");
                                        if(morningLeaveRest!=null && morningLeaveRest!=null){
                                            commentStr+="上午请休假半天，类型："+PersLeaveRestType.getTypeByKey(morningLeaveRest.getLeaveRestType()).getValue()
                                                    +";下午请休假半天，类型："+PersLeaveRestType.getTypeByKey(afternoonLeaveRest.getLeaveRestType()).getValue();
                                        }
                                    }else if(isMorningLeaveRest || isAfternoonLeaveRest){
                                        String halfStr="";
                                        if(isMorningLeaveRest){
                                            halfStr="上午";
                                        }
                                        if(isAfternoonLeaveRest){
                                            halfStr="下午";
                                        }
                                        if(restType.getKey().equals(PersLeaveRestType.affairLeave.getKey())){
                                            row.getCell(i).setCellValue("BS");
                                        }else if(restType.getKey().equals(PersLeaveRestType.illnessLeave.getKey())){
                                            row.getCell(i).setCellValue("BB");
                                        }else{
                                            row.getCell(i).setCellValue("BT");
                                        }


                                        commentStr+=halfStr+"请休假半天，类型："+restType.getValue();
                                    }
                                }else{
                                    PersOrgEmployeeLeaveRestApply leaveRestApply=leaveRestApplyList.get(0);
                                    if("1".equals(leaveRestApply.getLeaveRestDateType())){
                                        String[] array= PersOrgEmployeeLeaveRestApplyServiceImpl.getLeaveRestApplyInfo(leaveRestApply,date);
                                        String halfStr="";
                                        if("3".equals(array[1])){
                                            halfStr="上午";
                                        }else if("4".equals(array[1])){
                                            halfStr="下午";
                                        }
                                        PersLeaveRestType leaveRestType=PersLeaveRestType.getTypeByKey(leaveRestApply.getLeaveRestType());

                                        if(leaveRestType.getKey().equals(PersLeaveRestType.affairLeave.getKey())){
                                            row.getCell(i).setCellValue("BS");
                                        }else if(leaveRestType.getKey().equals(PersLeaveRestType.illnessLeave.getKey())){
                                            row.getCell(i).setCellValue("BB");
                                        }else{
                                            row.getCell(i).setCellValue("BT");
                                        }

                                        commentStr+=halfStr+"请休假半天，类型："+leaveRestType.getValue();
                                    }
                                }

                            }

                            List<PersOrgEmployeeLeaveRestApply> empNewLeaveRestApplyList=persOrgEmployeeLeaveRestApplyService.getEmpNewLeaveRestApplyList(empId,DateUtils.formatDate(date,"yyyy-MM-dd HH:mm:ss"));
                            //获取按小时请休假的记录
                            for (PersOrgEmployeeLeaveRestApply leaveRestApply : empNewLeaveRestApplyList) {
                                if("2".equals(leaveRestApply.getLeaveRestDateType())){
                                    PersLeaveRestType restType=PersLeaveRestType.getTypeByKey(leaveRestApply.getLeaveRestType());
                                    if(StrKit.notBlank(commentStr)){
                                        commentStr+="\n";
                                    }
                                    if(StrKit.isBlank(leaveRestApply.getId())){
                                        //旧
                                        commentStr+=DateUtils.formatDate(leaveRestApply.getStartTime(),"HH:mm:ss")+"至"+
                                                DateUtils.formatDate(leaveRestApply.getEndTime(),"HH:mm:ss")+"请休假"
                                                +secondToTime((leaveRestApply.getEndTime().getTime()-leaveRestApply.getStartTime().getTime())/1000)+"，类型："+restType.getValue();
                                    }else{
                                        //新
                                        commentStr+=DateUtils.formatDate(leaveRestApply.getStartTime(),"HH:mm:ss")+"至"+
                                                DateUtils.formatDate(leaveRestApply.getEndTime(),"HH:mm:ss")+"请休假"
                                                +secondToTime((long)(leaveRestApply.getLeaveMinute()*60))+"，类型："+restType.getValue();
                                    }

                                }
                            }
                            //按小时外派
                            //persOrgEmployeed
                            //按小时出差

                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                            Cell cell = row.getCell(i);
                            comment.setString(new XSSFRichTextString(commentStr+"\n"+checkinStr));
                            cell.setCellComment(comment);
                            Comment cellComment = cell.getCellComment();
                            cellComment.setVisible(false);//设置批注默认不显示

                        }else if(type==1){
                            //休息
                            row.getCell(i).setCellValue("X");
                        }else if(type==2){
                            //加班
                            String commentStr="";
                            //获取加班记录
                            PersOrgEmployeeOverTimeApply overTimeApply=null;
                            //List<PersOrgEmployeeOverTimeApply> overTimeApplyList=persOrgEmployeeOverTimeApplyService.getOverTimeApplyListByMap(allOverTimeApplyMap,empId,date);

                            if(flag){
                                overTimeApply=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordByStatus(empId,date,new String[]{"3"});
                                /*if(overTimeApplyList.size()>0){
                                    overTimeApply=overTimeApplyList.get(0);
                                }*/
                            }

                            if(PersOverTimeType.exchangeLeave.getKey().equals(overTimeApply.getOverType())){
                                //调休加班
                                row.getCell(i).setCellValue("T");
                            }else if(PersOverTimeType.workOvertime.getKey().equals(overTimeApply.getOverType())){
                                //计工资加班
                                row.getCell(i).setCellValue("J");
                            }
                            if(overTimeApply!=null){
                               /* for (PersOrgEmployeeOverTimeApply persOrgEmployeeOverTimeApply : overTimeApplyList) {

                                    commentStr+="\n";
                                }*/

                                commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                        +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());

                            }
                            // 创建绘图对象
                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                            // 输入批注信息
                            Cell cell = row.getCell(i);
                            comment.setString(new XSSFRichTextString(commentStr+="\n"+checkinStr));
                            cell.setCellComment(comment);
                            Comment cellComment = cell.getCellComment();
                            cellComment.setVisible(false);//设置批注默认不显示
                        }else if(type==3) {
                            String leaveType = typeMap.get(DateUtils.formatDate(date, "yyyy-MM-dd"));
                            //请休假
                            if ("事假".equals(leaveType)) {
                                row.getCell(i).setCellValue("S");
                            } else if ("病假".equals(leaveType)) {
                                row.getCell(i).setCellValue("B");
                            } else if ("年休假".equals(leaveType)) {
                                row.getCell(i).setCellValue("N");
                            } else if ("补休".equals(leaveType)) {
                                row.getCell(i).setCellValue("BX");
                            } else {
                                row.getCell(i).setCellValue("H");
                            }

                        }else if(type==5){
                            //加班
                            String commentStr="";
                            //获取加班记录
                            PersOrgEmployeeOverTimeApply overTimeApply=null;
                            //List<PersOrgEmployeeOverTimeApply> overTimeApplyList=persOrgEmployeeOverTimeApplyService.getOverTimeApplyListByMap(allOverTimeApplyMap,empId,date);
                            if(flag){
                                overTimeApply=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordByStatus(empId,date,new String[]{"3"});
                                /*if(overTimeApplyList.size()>0){
                                    overTimeApply=overTimeApplyList.get(0);
                                }*/
                            }

                            if(PersOverTimeType.exchangeLeave.getKey().equals(overTimeApply.getOverType())){
                                //调休加班
                                row.getCell(i).setCellValue("t");
                            }else if(PersOverTimeType.workOvertime.getKey().equals(overTimeApply.getOverType())){
                                //计工资加班
                                row.getCell(i).setCellValue("j");
                            }
                            if(overTimeApply!=null){
                                /*for (PersOrgEmployeeOverTimeApply persOrgEmployeeOverTimeApply : overTimeApplyList) {
                                    commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                            +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
                                    commentStr+="\n";
                                }*/
                                commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                        +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
                            }
                            // 创建绘图对象
                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                            // 输入批注信息
                            Cell cell = row.getCell(i);
                            comment.setString(new XSSFRichTextString(commentStr+="\n"+checkinStr));
                            cell.setCellComment(comment);
                            Comment cellComment = cell.getCellComment();
                            cellComment.setVisible(false);//设置批注默认不显示
                        }else if(type==6){
                            row.getCell(i).setCellValue("√");

                            PersOrgEmployeeBusinessTripApply  businessTripApply=persOrgEmployeeBusinessTripApplyService.findEmpBusinessTripApplyByStatus(empId,date,new String[]{"3"});
                            String commentStr="";
                            if(businessTripApply!=null){
                                /*for (PersOrgEmployeeOverTimeApply persOrgEmployeeOverTimeApply : overTimeApplyList) {
                                    commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                            +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
                                    commentStr+="\n";
                                }*/
                                MainArea area =mainAreaService.findById(businessTripApply.getDestinationCity());
                                commentStr+=DateUtils.formatDate(businessTripApply.getStartTime(),"HH:mm")+"至"
                                        +DateUtils.formatDate(businessTripApply.getEndTime(),"HH:mm")+area.getShortName()+"出差，事由："+businessTripApply.getReason();
                            }
                            // 创建绘图对象
                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                            // 输入批注信息
                            Cell cell = row.getCell(i);
                            comment.setString(new XSSFRichTextString(commentStr+="\n"+checkinStr));
                            cell.setCellComment(comment);
                            Comment cellComment = cell.getCellComment();
                            cellComment.setVisible(false);//设置批注默认不显示
                        }else if(type==7){
//                            row.getCell(i).setCellValue("v");
//
//
//                            PersOrgEmployeeBusinessTripApply  businessTripApply=persOrgEmployeeBusinessTripApplyService.findEmpBusinessTripApplyByStatus(empId,date,new String[]{"3"});
//                            String commentStr="";
//                            if(businessTripApply!=null){
//                                /*for (PersOrgEmployeeOverTimeApply persOrgEmployeeOverTimeApply : overTimeApplyList) {
//                                    commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
//                                            +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
//                                    commentStr+="\n";
//                                }*/
//                                MainArea area =mainAreaService.findById(businessTripApply.getDestinationCity());
//                                commentStr+=DateUtils.formatDate(businessTripApply.getStartTime(),"HH:mm")+"至"
//                                        +DateUtils.formatDate(businessTripApply.getEndTime(),"HH:mm")+area.getShortName()+"出差，事由："+businessTripApply.getReason();
//                            }
//                            // 创建绘图对象
//                            Drawing<?> drawing = sheet.createDrawingPatriarch();
//                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
//                            // 输入批注信息
//                            Cell cell = row.getCell(i);
//                            comment.setString(new XSSFRichTextString(commentStr+="\n"+checkinStr));
//                            cell.setCellComment(comment);
//                            Comment cellComment = cell.getCellComment();
//                            cellComment.setVisible(false);//设置批注默认不显示


                            //加班
                            String commentStr="";
                            //获取加班记录
                            PersOrgEmployeeOverTimeApply overTimeApply=null;
                            //List<PersOrgEmployeeOverTimeApply> overTimeApplyList=persOrgEmployeeOverTimeApplyService.getOverTimeApplyListByMap(allOverTimeApplyMap,empId,date);
                            if(flag){
                                overTimeApply=persOrgEmployeeOverTimeApplyService.findEmpRestOverTimeRecordByStatus(empId,date,new String[]{"3"});
                                /*if(overTimeApplyList.size()>0){
                                    overTimeApply=overTimeApplyList.get(0);
                                }*/
                            }

                            if(PersOverTimeType.exchangeLeave.getKey().equals(overTimeApply.getOverType())){
                                //调休加班
                                row.getCell(i).setCellValue("t");
                            }else if(PersOverTimeType.workOvertime.getKey().equals(overTimeApply.getOverType())){
                                //计工资加班
                                row.getCell(i).setCellValue("j");
                            }
                            if(overTimeApply!=null){
                                /*for (PersOrgEmployeeOverTimeApply persOrgEmployeeOverTimeApply : overTimeApplyList) {
                                    commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                            +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
                                    commentStr+="\n";
                                }*/
                                commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                        +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
                            }
                            PersOrgEmployeeBusinessTripApply  businessTripApply=persOrgEmployeeBusinessTripApplyService.findEmpBusinessTripApplyByStatus(empId,date,new String[]{"3"});
                            if(businessTripApply!=null){
                                /*for (PersOrgEmployeeOverTimeApply persOrgEmployeeOverTimeApply : overTimeApplyList) {
                                    commentStr+=DateUtils.formatDate(overTimeApply.getStartTime(),"HH:mm")+"至"
                                            +DateUtils.formatDate(overTimeApply.getEndTime(),"HH:mm")+"加班"+secondToTime(overTimeApply.getOverSecond());
                                    commentStr+="\n";
                                }*/
                                MainArea area =mainAreaService.findById(businessTripApply.getDestinationCity());
                                commentStr+=area.getShortName()+"出差，事由："+businessTripApply.getReason();
                            }

                            // 创建绘图对象
                            Drawing<?> drawing = sheet.createDrawingPatriarch();
                            Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                            // 输入批注信息
                            Cell cell = row.getCell(i);
                            comment.setString(new XSSFRichTextString(commentStr+="\n"+checkinStr));
                            cell.setCellComment(comment);
                            Comment cellComment = cell.getCellComment();
                            cellComment.setVisible(false);//设置批注默认不显示
                        }

                    }
                }
                //应出勤
                row.getCell(cellSize+1).setCellValue(record.getStr("shouldWorkDay"));
                //实出勤
                row.getCell(cellSize+2).setCellValue(record.getStr("actualWorkDay"));
                //实算工资天数
                row.getCell(cellSize+3).setCellValue(record.getStr("actualWageDays"));
                //法定假期
                row.getCell(cellSize+4).setCellValue(record.getStr("monthRestDay"));
                //婚/丧/产/工伤 假
                row.getCell(cellSize+5).setCellValue(record.getStr("marriageHoliday"));
                //事假
                row.getCell(cellSize+6).setCellValue(record.getStr("thingLear"));

                //本月未休假
                row.getCell(cellSize+8).setCellValue(record.getStr("restOverDay"));
                //本月补休
                row.getCell(cellSize+9).setCellValue(record.getStr("learRest"));


                //修年假天数
                row.getCell(cellSize+11).setCellValue(record.getStr("yearRest"));

                //迟到次数
                int exception1=0;
                if(record.getInt("exception_1")!=null){
                    exception1=record.getInt("exception_1");
                }
                row.getCell(cellSize+13).setCellValue(exception1);
                //早退次数
                int exception2=0;
                if(record.getInt("exception_2")!=null){
                    exception2=record.getInt("exception_2");
                }
                row.getCell(cellSize+14).setCellValue(exception2);
                //旷工天数
                row.getCell(cellSize+15).setCellValue(record.getStr("exceptionCount"));
                //补卡次数
                row.getCell(cellSize+16).setCellValue(record.getStr("fillCardCount"));
                if(record.getLong("halfOversecond")>0){
                    //计小时加班
                    row.getCell(cellSize+17).setCellValue(secondToTime(record.getLong("restOverSecond")));
                }
                if(record.getLong("hourRestSecond")>0){
                    //计小时请休假
                    row.getCell(cellSize+18).setCellValue(secondToTime(record.getLong("hourRestSecond")));
                }
                record=null;
            }
            inputStream=null;
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            workbook=null;
            recordList=null;
            //System.gc();
            StreamRender streamRender=new StreamRender(fileName, os);
            render(streamRender);
            os.flush();
            os.close();
            os=null;

            //streamRender=null;
        }catch (Exception e){
            e.printStackTrace();
        }
    }



    public static void insertRow(XSSFWorkbook wb, XSSFSheet sheet, int starRow,int rows) {

        sheet.shiftRows(starRow + 1, sheet.getLastRowNum(), rows,true,false);

        starRow = starRow - 1;

        for (int i = 0; i < rows; i++) {

            XSSFRow sourceRow = null;
            XSSFRow targetRow = null;
            XSSFCell sourceCell = null;
            XSSFCell targetCell = null;
            short m;

            starRow = starRow + 1;
            sourceRow = sheet.getRow(starRow);
            targetRow = sheet.createRow(starRow + 1);
            targetRow.setHeight(sourceRow.getHeight());

            for (m = sourceRow.getFirstCellNum(); m < sourceRow.getLastCellNum(); m++) {

                sourceCell = sourceRow.getCell(m);
                targetCell = targetRow.createCell(m);
                targetCell.setCellStyle(sourceCell.getCellStyle());
                targetCell.setCellType(sourceCell.getCellType());

            }
        }

    }

    public static void removeColumn(XSSFSheet sheet, int removeColumnNum, int removeColumnTotal){
        if(sheet == null){
            return;
        }
        for (Iterator<Row> rowIterator = sheet.rowIterator(); rowIterator.hasNext();) {
            XSSFRow row = (XSSFRow)rowIterator.next();
            XSSFCell cell = row.getCell(removeColumnNum);
            if(cell == null){
                continue;
            }
            row.removeCell(cell);

        }
    }

    public static void deleteColumn(Sheet sheet, int columnToDelete,int brackRowIndex) {
        for (int rId = 0; rId <= sheet.getLastRowNum(); rId++) {
            Row row = sheet.getRow(rId);
            if(rId>brackRowIndex){
                break;
            }
            for (int cID = columnToDelete; cID <= row.getLastCellNum(); cID++) {
                if(cID>60){
                    break;
                }
                Cell cOld = row.getCell(cID);
                if (cOld != null) {
                    row.removeCell(cOld);
                }
                Cell cNext = row.getCell(cID + 1);
                if (cNext != null) {
                    Cell cNew = row.createCell(cID, cNext.getCellTypeEnum());
                    cloneCell(cNew, cNext);
                    if (rId == 0) {
                        sheet.setColumnWidth(cID, sheet.getColumnWidth(cID + 1));
                    }
                }
            }
        }
    }

    private static void cloneCell(Cell cNew, Cell cOld) {
        cNew.setCellComment(cOld.getCellComment());
        cNew.setCellStyle(cOld.getCellStyle());

        if (CellType.BOOLEAN == cNew.getCellTypeEnum()) {
            cNew.setCellValue(cOld.getBooleanCellValue());
        } else if (CellType.NUMERIC == cNew.getCellTypeEnum()) {
            cNew.setCellValue(cOld.getNumericCellValue());
        } else if (CellType.STRING == cNew.getCellTypeEnum()) {
            cNew.setCellValue(cOld.getStringCellValue());
        } else if (CellType.ERROR == cNew.getCellTypeEnum()) {
            cNew.setCellValue(cOld.getErrorCellValue());
        } else if (CellType.FORMULA == cNew.getCellTypeEnum()) {
            cNew.setCellValue(cOld.getCellFormula());
        }
    }
    public void delEmpCheckinDaySummary(){
        String id=getPara("id");

        PersOrgEmployeeCheckinDaySummary daySummary=persOrgEmployeeCheckinDaySummaryService.findById(id);
        daySummary.setDelFlag("1");
        daySummary.setUpdateDate(new Date());
        if(daySummary.update()){
            renderJson(Ret.ok("msg","操作超过"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void getQualifiedEmpCheckinInfo(){
        String empId=getPara("empId");
        String qualifiedDate=getPara("qualifiedDate");
        PersOrgEmployee employee=persOrgEmployeeService.findById(empId);

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageList2(1,1,empId,null
                ,DateUtils.formatDate(employee.getEntryTime(),"yyyy-MM-dd"),qualifiedDate,"incumbency"
                ,new ArrayList<>(), AuthUtils.getUserId());

        if(pageList.getList()==null || pageList.getList().size()==0){
            renderCodeSuccess("success",new Object());
        }else{
            Record record=pageList.getList().get(0);
            if(record!=null){
                /*if(record.getInt("exception_1_duration")!=null){
                    record.set("exception_1_duration",record.getInt("exception_1_duration")/60);
                }
                if(record.getInt("exception_2_duration")!=null){
                    record.set("exception_2_duration",record.getInt("exception_2_duration")/60);
                }
                if(record.getInt("exception_3_duration")!=null){
                    record.set("exception_3_duration",record.getInt("exception_3_duration")/60);
                }*/


            }
            renderJson(Ret.ok("data",record));
        }
    }


    public void dayCheckinStatusIndex(){

        render("dayCheckinStatusIndex.html");
    }

    public void dayCheckinStatusPageList(){

        String date=getPara("date");
        String fullName=getPara("fullName");
        String deptIds=getPara("deptIds");
        if(StrKit.isBlank(date)){
            date=DateUtils.formatDate(new Date(),"yyyy-MM-dd");
        }
        String deptId=null;
        if(StrKit.notBlank(deptIds)){
            JSONArray jsonArray=JSON.parseArray(deptIds);
            if(jsonArray!=null && jsonArray.size()>0){
                deptId=jsonArray.getString(0);
            }
        }
        Page<Record> recordList=persOrgEmployeeCheckinRecordService.dayCheckinStatusPageList(date,deptId,fullName,AuthUtils.getUserId(),getParaToInt("page"),getParaToInt("limit"));
        //persOrgEmployeeCheckinRuleService.getEmpTopCheckinRule();
        if(recordList.getList()!=null && recordList.getList().size()>0){
            Map<String,String> deptNameMap=new HashMap<>();
            for (Record record : recordList.getList()) {
                String empDeptId=record.getStr("dept_id");
                if(!deptNameMap.containsKey(empDeptId)){
                    String orgName=persOrgService.getOrgParentNames(empDeptId);
                    deptNameMap.put(empDeptId,orgName);
                }
                record.set("orgName",deptNameMap.get(empDeptId));
            }
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void empCheckinDaySummaryExport5(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String exportType=getPara("exportType");
        Integer page=getParaToInt("page");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        Date startDate=DateUtils.parseDate(startDateStr);
        Date endDate=DateUtils.parseDate(endDateStr);

        List<Date> dateList=DateUtils.getBetweenDates(startDate,endDate);
        if(StrKit.isBlank(exportType)){
            if(dateList.size()>31){
                renderJson(Ret.fail("msg","导出时间不能超过31天"));
                return;
            }
        }



        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }
        String deptName="";
        if(ids.size()>0){
            deptName=persOrgService.getOrgParentNames(ids.get(0));
            if(deptName.indexOf("昌松集团/旅居运营部/")!=-1){
                deptName=deptName.replace("昌松集团/旅居运营部/","");
            }else if(deptName.indexOf("昌松集团/市场部/")!=-1){
                deptName=deptName.replace("昌松集团/市场部/","");
            }else if(deptName.indexOf("昌松集团/")!=-1){
                deptName=deptName.replace("昌松集团/","");
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageListNew(1,10000,null,fullName,startDateStr,endDateStr
                ,archiveStatus,ids,AuthUtils.getUserId(),page);
        List<Record> recordList=pageList.getList();

        String fileName="";
        if(StrKit.notBlank(deptName)){
            fileName=deptName+"员工考勤"+DateUtils.formatDate(startDate,"yyyy-MM")+"月份"+"汇总.xlsx";
        }else{
            fileName="员工考勤"+DateUtils.formatDate(startDate,"yyyy-MM")+"月份"+"汇总.xlsx";
        }


        try {
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\kaoqingmuban2.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            //System.gc();
            XSSFSheet sheet = workbook.getSheet("考勤汇总");
            int dateSize=dateList.size()+4;
            int cellSize=34;//最后一个日期列index
            while (dateSize<=cellSize){
                //删除列
                deleteColumn(sheet,cellSize,recordList.size()+30);
                cellSize--;
            }
            if(recordList.size()>1){
                //创建行数
                insertRow(workbook,sheet,4,recordList.size()-1);
            }
            if(StrKit.notBlank(deptName)){
                XSSFRow row0=sheet.getRow(0);
                XSSFCell row0Cell1=row0.getCell(0);
                row0Cell1.setCellValue(deptName+"员工"+DateUtils.formatDate(startDate,"MM")+"月考勤统计汇总表");
            }else{
                XSSFRow row0=sheet.getRow(0);
                XSSFCell row0Cell1=row0.getCell(0);
                row0Cell1.setCellValue("员工"+DateUtils.formatDate(startDate,"MM")+"月考勤统计汇总表");
            }

            XSSFRow row1=sheet.getRow(1);
            XSSFCell row1Cell1=row1.getCell(2);
            String row1Cell1Str=row1Cell1.getStringCellValue();
            if(StrKit.notBlank(row1Cell1Str)){
                row1Cell1.setCellValue(row1Cell1Str.replace("${date}",DateUtils.formatDate(startDate,"yyyy年MM月份")));
            }



            XSSFRow row2=sheet.getRow(2);
            XSSFRow row3=sheet.getRow(3);
            String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
            //处理日期、星期
            for(int i=4;i<=cellSize;i++){
                Date date=dateList.get(i-4);
                XSSFCell dayCell=row2.getCell(i);
                XSSFCell weekCell=row3.getCell(i);
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(date);
                dayCell.setCellValue(Integer.valueOf(calendar.get(Calendar.DAY_OF_MONTH)));
                weekCell.setCellValue(weekDays[calendar.get(Calendar.DAY_OF_WEEK)-1]);
            }


            int rowIndex=3;
            for(Record record:recordList){
                rowIndex++;

                /*String pontionName=Db.queryStr("select b.position_name from pers_org_employee_rel a inner join pers_position b on a.relationship_id=b.id" +
                        " where a.relationship_type='position' and a.emp_id=? and b.org_id=? ",record.getStr("id"),record.getStr("orgId"));*/


                String empId=record.getStr("id");
                XSSFRow row=sheet.getRow(rowIndex);
                XSSFCell cell0=row.getCell(0);
                cell0.setCellValue(rowIndex-3);
                row.getCell(1).setCellValue(record.getStr("orgName"));
                /*row.getCell(2).setCellValue(pontionName);*/
                row.getCell(2).setCellValue(record.getStr("fullName"));
                if("male".equals(record.getStr("sex"))){
                    row.getCell(3).setCellValue("男");
                }else if("female".equals(record.getStr("sex"))){
                    row.getCell(3).setCellValue("女");
                }else{
                    row.getCell(3).setCellValue("");
                }
                int cellIndex=3;
                //使用年假总天数
                int yearLeaveDays=0;
                //使用补休假总天数
                double fillLeaveDays=0;
                //使用补休假总分钟
                int fillLeaveMinutes=0;
                //调休加班总分钟
                int exchangeLeaveOvertimeMinute=0;
                //调休加班总天数
                double exchangeLeaveOvertimeDays=0;
                //1天标准上班分钟
                int dayStandardMinutes=0;
                for(Date date:dateList){
                    cellIndex++;
                    String dateStr=DateUtils.formatDate(date,"yyyy-MM-dd");
                    Record dayRecord= record.get(dateStr);
                    if(dayRecord!=null){
                        if(dayStandardMinutes==0 && dayRecord.getInt("dayStandardMinutes")!=null){
                            dayStandardMinutes=dayRecord.getInt("dayStandardMinutes");
                        }
                        String dayLog=dayRecord.getStr("dayLog");
                        String dayRemark=dayRecord.getStr("dayRemark");
                        // 创建绘图对象
                        Drawing<?> drawing = sheet.createDrawingPatriarch();
                        Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                        // 输入批注信息
                        Cell cell = row.getCell(cellIndex);
                        comment.setString(new XSSFRichTextString(dayRemark));
                        cell.setCellComment(comment);
                        Comment cellComment = cell.getCellComment();
                        cellComment.setVisible(false);//设置批注默认不显示

                        row.getCell(cellIndex).setCellValue(dayLog);

                        //获取年假
                        List<Record> leaveRecordList=dayRecord.get("leaveRecordList");
                        if(leaveRecordList!=null){
                            for (Record leaveRecord : leaveRecordList) {
                                if(PersLeaveRestType.yearLeave.getKey().equals(leaveRecord.getStr("leaveType"))){
                                    yearLeaveDays+=leaveRecord.getInt("leaveTypeDays");
                                }else if(PersLeaveRestType.fillLeave.getKey().equals(leaveRecord.getStr("leaveType"))){
                                    fillLeaveMinutes+=leaveRecord.getInt("leaveMinute");
                                    fillLeaveDays= BigDecimal.valueOf(fillLeaveDays).add(BigDecimal.valueOf(leaveRecord.getDouble("leaveTypeDays"))).doubleValue();
                                }
                            }
                        }

                        //获取补休加班的时长
                        List<Record> overtimeRecordList=dayRecord.get("overtimeRecordList");
                        if(overtimeRecordList!=null){
                            for (Record overtimeRecord : overtimeRecordList) {
                                if(PersOverTimeType.exchangeLeave.getKey().equals(overtimeRecord.getStr("overtimeType"))){
                                    exchangeLeaveOvertimeMinute+=overtimeRecord.getInt("overtimeMinute");

                                    exchangeLeaveOvertimeDays= BigDecimal.valueOf(exchangeLeaveOvertimeDays)
                                            .add(BigDecimal.valueOf(overtimeRecord.getDouble("overtimeTypeDays"))).doubleValue();
                                }
                            }
                        }
                    }
                    //System.out.println(dayRecord);
                }


                //使用补休整天数
                int fillLeaveIntDays=0;
                //使用补休小时
                double fillLeaveIntHour=0;
                //double fillLeaveDaysRemainder = fillLeaveDays % 1;
                /*if(fillLeaveDaysRemainder==0){
                    if(fillLeaveDays>0){
                        fillLeaveIntDays=(int)fillLeaveDays;
                    }
                }else{
                    fillLeaveIntHour=BigDecimal.valueOf(dayStandardMinutes).divide(BigDecimal.valueOf(60),2,BigDecimal.ROUND_HALF_EVEN).multiply(BigDecimal.valueOf(fillLeaveDaysRemainder)).doubleValue();
                }*/
                if(fillLeaveMinutes>0){
                    fillLeaveIntDays=(int)fillLeaveMinutes/dayStandardMinutes;
                    fillLeaveIntHour=BigDecimal.valueOf((fillLeaveMinutes%dayStandardMinutes)).divide(BigDecimal.valueOf(60),2,BigDecimal.ROUND_HALF_EVEN).doubleValue();
                }

                //本月补休天数
                row.getCell(cellIndex+11).setCellValue(fillLeaveIntDays);
                //本月补休小时
                row.getCell(cellIndex+12).setCellValue(fillLeaveIntHour);

                //未休假
                int notUseFillLeaveMinute=0;
                if(exchangeLeaveOvertimeMinute>fillLeaveMinutes){
                    //notUseFillLeaveMinute=exchangeLeaveOvertimeMinute-fillLeaveMinutes;
                    notUseFillLeaveMinute=exchangeLeaveOvertimeMinute;
                    //未休补休整天数
                    int notUseFillLeaveIntDays=0;
                    //未休补休小时
                    double notUseFillLeaveHour=0.0;

                    notUseFillLeaveIntDays=(int)(notUseFillLeaveMinute/dayStandardMinutes);
                    notUseFillLeaveHour=BigDecimal.valueOf((notUseFillLeaveMinute%dayStandardMinutes)).divide(BigDecimal.valueOf(60),2,BigDecimal.ROUND_HALF_EVEN).doubleValue();

                    //本月未休假天数
                    row.getCell(cellIndex+9).setCellValue(notUseFillLeaveIntDays);
                    //本月未休假小时
                    row.getCell(cellIndex+10).setCellValue(notUseFillLeaveHour);

                }


                //应出勤天
                row.getCell(cellIndex+1).setCellValue(record.getDouble("shouldWorkDays"));
                //实出勤天
                row.getCell(cellIndex+2).setCellValue(record.getDouble("actualWorkDays"));
                //实算工资天
                row.getCell(cellIndex+3).setCellValue(record.getDouble("actualSalaryDays"));
                //公休
                row.getCell(cellIndex+4).setCellValue(record.getDouble("restDays"));
                //婚/丧/产/工伤假（不扣工资的请休假）
                row.getCell(cellIndex+5).setCellValue(
                        BigDecimal.valueOf(record.getDouble("notDeductLease")).subtract(BigDecimal.valueOf(yearLeaveDays)).subtract(BigDecimal.valueOf(fillLeaveDays)).doubleValue()
                );
                //事/病假（扣工资的请休假）
                row.getCell(cellIndex+6).setCellValue(record.getDouble("deductLeaseDays"));





                if(yearLeaveDays>0){
                    row.getCell(cellIndex+15).setCellValue(yearLeaveDays);
                }
                //迟到
                if(record.getInt("exception_1")!=null){
                    row.getCell(cellIndex+16).setCellValue(record.getInt("exception_1"));
                }
                //早退
                if(record.getInt("exception_2")!=null){
                    row.getCell(cellIndex+17).setCellValue(record.getInt("exception_2"));
                }
                //补卡次数
                if(record.getInt("fillCardCount")!=null){
                    row.getCell(cellIndex+19).setCellValue(record.getInt("fillCardCount"));
                }
            }
            inputStream=null;
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            workbook=null;
            recordList=null;
            //System.gc();
            StreamRender streamRender=new StreamRender(fileName, os);
            render(streamRender);
            os.flush();
            os.close();
            os=null;

            //streamRender=null;
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void empCheckinDaySummaryExport6(){
        String dateRange=getPara("dateRange");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        String exportType=getPara("exportType");
        Integer page=getParaToInt("page");
        String startDateStr=null;
        String endDateStr=null;
        if(StrKit.notBlank(dateRange)){
            String[] dates=dateRange.split(" - ");
            startDateStr=dates[0];
            endDateStr=dates[1];
            //startDateStr=dateRange+"-01";
            //endDateStr=dateRange+"-"+DateUtils.getMonthLashDay(DateUtils.parseDate(startDateStr));
        }else{
            startDateStr= DateUtils.getDate("yyyy-MM")+"-01";
            endDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        }
        Date startDate=DateUtils.parseDate(startDateStr);
        Date endDate=DateUtils.parseDate(endDateStr);

        List<Date> dateList=DateUtils.getBetweenDates(startDate,endDate);
        if(StrKit.isBlank(exportType)){
            if(dateList.size()>31){
                renderJson(Ret.fail("msg","导出时间不能超过31天"));
                return;
            }
        }



        String deptIds=getPara("deptIds");
        List<String> ids=new ArrayList<>();
        if(StrKit.notBlank(deptIds)){
            JSONArray array= JSON.parseArray(deptIds);
            for(int i=0;i<array.size();i++){
                ids.add(array.getString(i));
            }
        }
        String deptName="";
        if(ids.size()>0){
            deptName=persOrgService.getOrgParentNames(ids.get(0));
            if(deptName.indexOf("昌松集团/旅居运营部/")!=-1){
                deptName=deptName.replace("昌松集团/旅居运营部/","");
            }else if(deptName.indexOf("昌松集团/市场部/")!=-1){
                deptName=deptName.replace("昌松集团/市场部/","");
            }else if(deptName.indexOf("昌松集团/")!=-1){
                deptName=deptName.replace("昌松集团/","");
            }
        }

        Page<Record> pageList=persOrgEmployeeCheckinDaySummaryService.pageListNew(1,10000,null,fullName,startDateStr,endDateStr
                ,archiveStatus,ids,AuthUtils.getUserId(),page);
        List<Record> recordList=pageList.getList();

        String fileName="";
        if(StrKit.notBlank(deptName)){
            fileName=deptName+"员工考勤"+DateUtils.formatDate(startDate,"yyyy-MM")+"月份"+"汇总.xlsx";
        }else{
            fileName="员工考勤"+DateUtils.formatDate(startDate,"yyyy-MM")+"月份"+"汇总.xlsx";
        }


        try {
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\kaoqingmuban3.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            //System.gc();
            XSSFSheet sheet = workbook.getSheetAt(0);
            int dateSize=dateList.size()+4;
            int cellSize=34;//最后一个日期列index
            while (dateSize<=cellSize){
                //删除列
                deleteColumn(sheet,cellSize,recordList.size()+30);
                cellSize--;
            }
            if(recordList.size()>1){
                //创建行数
                insertRow(workbook,sheet,4,recordList.size()-1);
            }
            if(StrKit.notBlank(deptName)){
                XSSFRow row0=sheet.getRow(0);
                XSSFCell row0Cell1=row0.getCell(1);
                row0Cell1.setCellValue("员工"+DateUtils.formatDate(startDate,"yyyy年MM")+"月考勤统计汇总表");
            }else{
                XSSFRow row0=sheet.getRow(0);
                XSSFCell row0Cell1=row0.getCell(1);
                row0Cell1.setCellValue("员工"+DateUtils.formatDate(startDate,"yyyy年MM")+"月考勤统计汇总表");
            }

            XSSFRow row1=sheet.getRow(1);
            XSSFCell row1Cell1=row1.getCell(1);
            String row1Cell1Str=row1Cell1.getStringCellValue();
            if(StrKit.notBlank(row1Cell1Str)){
                row1Cell1Str=row1Cell1Str.replace("${deptName}",deptName);
                row1Cell1Str=row1Cell1Str.replace("${date}",DateUtils.formatDate(startDate,"yyyy年MM月份"));
                row1Cell1.setCellValue(row1Cell1Str);
            }



            XSSFRow row2=sheet.getRow(2);
            XSSFRow row3=sheet.getRow(3);
            String[] weekDays = {"日", "一", "二", "三", "四", "五", "六"};
            //处理日期、星期
            for(int i=4;i<=cellSize;i++){
                Date date=dateList.get(i-4);
                XSSFCell dayCell=row2.getCell(i);
                XSSFCell weekCell=row3.getCell(i);
                Calendar calendar=Calendar.getInstance();
                calendar.setTime(date);
                dayCell.setCellValue(Integer.valueOf(calendar.get(Calendar.DAY_OF_MONTH)));
                weekCell.setCellValue(weekDays[calendar.get(Calendar.DAY_OF_WEEK)-1]);
            }


            int rowIndex=3;
            for(Record record:recordList){
                rowIndex++;

                /*String pontionName=Db.queryStr("select b.position_name from pers_org_employee_rel a inner join pers_position b on a.relationship_id=b.id" +
                        " where a.relationship_type='position' and a.emp_id=? and b.org_id=? ",record.getStr("id"),record.getStr("orgId"));*/


                String empId=record.getStr("id");
                XSSFRow row=sheet.getRow(rowIndex);
                XSSFCell cell0=row.getCell(0);
                cell0.setCellValue(rowIndex-3);

                String empDeptName=record.getStr("orgName");
                if(empDeptName.indexOf("昌松集团/旅居运营部/")!=-1){
                    empDeptName=empDeptName.replace("昌松集团/旅居运营部/","");
                }else if(empDeptName.indexOf("昌松集团/市场部/")!=-1){
                    empDeptName=empDeptName.replace("昌松集团/市场部/","");
                }else if(empDeptName.indexOf("昌松集团/")!=-1){
                    empDeptName=empDeptName.replace("昌松集团/","");
                }

                row.getCell(1).setCellValue(empDeptName);
                /*row.getCell(2).setCellValue(pontionName);*/
                row.getCell(2).setCellValue(record.getStr("fullName"));
                if("male".equals(record.getStr("sex"))){
                    row.getCell(3).setCellValue("男");
                }else if("female".equals(record.getStr("sex"))){
                    row.getCell(3).setCellValue("女");
                }else{
                    row.getCell(3).setCellValue("");
                }
                int cellIndex=3;

                for(Date date:dateList){
                    cellIndex++;
                    String dateStr=DateUtils.formatDate(date,"yyyy-MM-dd");
                    Record dayRecord= record.get(dateStr);
                    if(dayRecord!=null){

                        String dayLog=dayRecord.getStr("dayLog");
                        String dayRemark=dayRecord.getStr("dayRemark");
                        // 创建绘图对象
                        Drawing<?> drawing = sheet.createDrawingPatriarch();
                        Comment comment = drawing.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, (short) 5, 2, (short) 5, 4));
                        // 输入批注信息
                        Cell cell = row.getCell(cellIndex);
                        comment.setString(new XSSFRichTextString(dayRemark));
                        cell.setCellComment(comment);
                        Comment cellComment = cell.getCellComment();
                        cellComment.setVisible(false);//设置批注默认不显示

                        row.getCell(cellIndex).setCellValue(dayLog);

                    }
                    //System.out.println(dayRecord);
                }

                //应出勤天
                row.getCell(cellIndex+1).setCellValue(record.getStr("shouldWorkDayHourStr"));
                //实出勤天
                row.getCell(cellIndex+2).setCellValue(record.getStr("actualWorkDayHourStr"));
                //实算工资天
                row.getCell(cellIndex+3).setCellValue(record.getStr("actualSalaryDayHourStr"));
                //婚/丧/产/工伤 假
                Integer marryNursingLeaveDay = Integer.valueOf(record.getStr("marryNursingLeaveDay"));
                Double marryNursingLeaveHour = Double.valueOf(record.getStr("marryNursingLeaveHour"));
                String marryNursingLeaveStr="";
                if(marryNursingLeaveDay>0){
                    marryNursingLeaveStr+=marryNursingLeaveDay+"天";
                }
                if(marryNursingLeaveHour>0){
                    marryNursingLeaveStr+=marryNursingLeaveHour+"小时";
                }
                row.getCell(cellIndex+4).setCellValue(marryNursingLeaveStr);
                //事/病假
                Integer affairIllnessLeaveDay = Integer.valueOf(record.getStr("affairIllnessLeaveDay"));
                Double affairIllnessLeaveHour = Double.valueOf(record.getStr("affairIllnessLeaveHour"));
                String affairIllnessLeaveStr="";
                if(affairIllnessLeaveDay>0){
                    affairIllnessLeaveStr+=affairIllnessLeaveDay+"天";
                }
                if(affairIllnessLeaveHour!=null && affairIllnessLeaveHour>0){
                    affairIllnessLeaveStr+=affairIllnessLeaveHour+"小时";
                }
                row.getCell(cellIndex+5).setCellValue(affairIllnessLeaveStr);
                //上月余假（天）
                row.getCell(cellIndex+6).setCellValue("");
                //上月余假（小时）
                row.getCell(cellIndex+7).setCellValue("");
                //本月调休/积假（天）
                row.getCell(cellIndex+8).setCellValue(record.getStr("thisMonthUsableFillLeaveDay"));
                //本月调休/积假（天）
                row.getCell(cellIndex+9).setCellValue(record.getStr("thisMonthUsableFillLeaveHour"));

                /*int thisMonthUsableFillLeaveDay=record.getInt("thisMonthUsableFillLeaveDay");
                int thisMonthUsableFillLeaveHour=record.getInt("thisMonthUsableFillLeaveHour");
                if(thisMonthUsableFillLeaveDay!=0){
                    String symbol="";
                    if(thisMonthUsableFillLeaveDay<0){
                        symbol="-";
                    }
                    //本月调休/积假（天）
                    row.getCell(cellIndex+8).setCellValue(Math.abs(thisMonthUsableFillLeaveDay));
                }
                if(thisMonthUsableFillLeaveHour!=0){
                    //本月调休/积假（小时）
                    row.getCell(cellIndex+9).setCellValue(Math.abs(thisMonthUsableFillLeaveHour));
                }*/


                //月末余假（天）
                row.getCell(cellIndex+10).setCellValue("");
                //月末余假（小时）
                row.getCell(cellIndex+11).setCellValue("");
                //本月休年假
                row.getCell(cellIndex+12).setCellValue(record.getStr("yearLeaveDay"));
                //夜班
                row.getCell(cellIndex+13).setCellValue("");
                //迟到
                if(record.getInt("exception_1")!=null){
                    row.getCell(cellIndex+14).setCellValue(record.getInt("exception_1"));
                }
                //早退
                if(record.getInt("exception_2")!=null){
                    row.getCell(cellIndex+15).setCellValue(record.getInt("exception_2"));
                }
                //旷工
                row.getCell(cellIndex+16).setCellValue(record.getStr("absenteeismDayHourStr"));
                /*//补卡次数
                if(record.getInt("fillCardCount")!=null){
                    row.getCell(cellIndex+19).setCellValue(record.getInt("fillCardCount"));
                }*/
            }

            //相同部门的合并行
            int dataStartRowIndex=3;
            int mergeStartRowIndex=0;
            String lastOrgName=null;
            int i=0;
            for (Record record : recordList) {
                i++;
                dataStartRowIndex++;
                String orgName = record.getStr("orgName");
                if(orgName.indexOf("昌松集团/旅居运营部/")!=-1){
                    orgName=orgName.replace("昌松集团/旅居运营部/","");
                }else if(orgName.indexOf("昌松集团/市场部/")!=-1){
                    orgName=orgName.replace("昌松集团/市场部/","");
                }else if(orgName.indexOf("昌松集团/")!=-1){
                    orgName=orgName.replace("昌松集团/","");
                }
                if(mergeStartRowIndex==0){
                    mergeStartRowIndex=dataStartRowIndex;
                }
                if(lastOrgName==null){
                    lastOrgName=orgName;
                }
                if(!orgName.equals(lastOrgName)){
                    if(mergeStartRowIndex<(dataStartRowIndex-1)){
                        CellRangeAddress region = new CellRangeAddress(mergeStartRowIndex, dataStartRowIndex-1, 1, 1);
                        sheet.addMergedRegion(region);
                    }
                    lastOrgName=orgName;
                    mergeStartRowIndex=dataStartRowIndex;
                }else if(i==recordList.size()){
                    if(mergeStartRowIndex<dataStartRowIndex) {
                        CellRangeAddress region = new CellRangeAddress(mergeStartRowIndex, dataStartRowIndex, 1, 1);
                        sheet.addMergedRegion(region);
                    }
                    lastOrgName=orgName;
                    mergeStartRowIndex=dataStartRowIndex;
                }

            }

            //合并列头
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 3+dateList.size()+6, 3+dateList.size()+7));
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 3+dateList.size()+8, 3+dateList.size()+9));
            sheet.addMergedRegion(new CellRangeAddress(2, 2, 3+dateList.size()+10, 3+dateList.size()+11));

            inputStream=null;
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            workbook.close();
            workbook=null;
            recordList=null;
            //System.gc();
            StreamRender streamRender=new StreamRender(fileName, os);
            render(streamRender);
            os.flush();
            os.close();
            os=null;

            //streamRender=null;
        }catch (Exception e){
            e.printStackTrace();
        }
    }

}

