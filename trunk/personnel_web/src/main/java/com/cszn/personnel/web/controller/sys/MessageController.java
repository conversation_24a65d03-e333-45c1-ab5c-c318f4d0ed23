/**
 * 
 */
package com.cszn.personnel.web.controller.sys;

import java.util.Date;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.SysMessageService;
import com.cszn.integrated.service.entity.msg.MsgMessage;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.SysMessage;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2021年11月8日
 *
 * GroupController
 */
@RequestMapping(value="/message", viewPath="/modules_page/sys/message")
public class MessageController extends BaseController {

	@Inject
    private SysMessageService sysMessageService;
	
    public void index() {
        render("messageIndex.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	SysMessage model = getBean(SysMessage.class, "", true);
    	model.setUserId(AuthUtils.getUserId());
    	model.setBelongSystem(SystemType.PERSONNEL);
        Page<SysMessage> modelPage = sysMessageService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<SysMessage>(modelPage));
    }
    
    /**
     * 获取用户未读信息
     */
    public void getNotReadMsgCount(){
        Long notReadMsgCount = Db.queryLong("select count(id)notReadMsgCount from sys_message where belong_system=? and del_flag=? and is_read='0' and user_id=?", SystemType.PERSONNEL, DelFlag.NORMAL, AuthUtils.getUserId());
        renderJson(Ret.ok().set("notReadMsgCount",notReadMsgCount));
    }
	
    public void add(){
    	SysMessage model = getBean(SysMessage.class, "", true);
    	setAttr("model",model);
    	render("messageForm.html");
    }
    
    public void edit(){
        final String id=getPara("id");
        setAttr("model",sysMessageService.findById(id));
        render("messageForm.html");
    }
    
    /**
     * 标记已读
     */
    public void signRead(){
        String msgData = getPara("data");
        List<SysMessage> msgList = JSONArray.parseArray(msgData, SysMessage.class);
        if(sysMessageService.signRead(msgList, AuthUtils.getUserId())){
            renderJson(Ret.ok("msg", "操作成功"));
        }else{
            renderJson(Ret.fail("msg", "操作失败"));
        }
    }

    public void save(){
    	boolean flag = false;
    	SysMessage model = getBean(SysMessage.class,"",true);
    	if(StrKit.notBlank(model.getId())){
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateDate(new Date());
    		flag = sysMessageService.update(model);
    	}else{
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateDate(new Date());
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateDate(new Date());
    		flag = model.save();
    	}
    	if(flag){
    		renderJson(Ret.ok("msg","操作成功"));
    	}else{
    		renderJson(Ret.fail("msg","操作失败"));
    	}
    }
    
    public void update(){
    	SysMessage model = getBean(SysMessage.class,"",true);
		model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateDate(new Date());
		if("1".equals(model.getIsRead())) {
			model.setIsReadTime(new Date());
		}
		if(sysMessageService.update(model)) {
			renderJson(Ret.ok("msg","操作成功"));
		}else {
			renderJson(Ret.fail("msg","操作失败"));
		}
    }
    
}
