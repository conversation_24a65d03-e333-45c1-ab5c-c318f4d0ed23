package com.cszn.personnel.web.support.cron;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeLeaveBalanceService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeRelService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeRel;
import com.cszn.integrated.service.entity.pers.PersPosition;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Cron("00 23 * * *")
public class EmployeeQuitCron  implements Runnable {

    @Inject
    PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    PersOrgEmployeeRelService persOrgEmployeeRelService;
    @Inject
    PersPositionService persPositionService;
    @Inject
    UserService userService;
    @Inject
    PersOrgEmployeeLeaveBalanceService persOrgEmployeeLeaveBalanceService;

    @Override
    public void run(){
        try {
            //更新离职员工信息
            updateQuit();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            //更新调岗结束信息
            updateChangeDept();
        }catch (Exception e){
            e.printStackTrace();
        }

        //入职满一年年假
        try {

            List<String> params=new ArrayList<>();
            params.add(DateUtils.formatDate(new Date(),"MM-dd"));
            if("02-28".equals(DateUtils.formatDate(new Date(),"MM-dd")) && "03-01".equals(DateUtils.formatDate(DateUtils.getNextDay(new Date(),1),"MM-dd")) ){
                params.add("02-29");
            }
            String str="";
            for(String date:params){
                str+="?,";
            }
            str=str.substring(0,str.length()-1);
            String sql="select * from pers_org_employee where del_flag='0' and archive_status='incumbency' and DATE_FORMAT(entry_time,'%Y')=(DATE_FORMAT(now(),'%Y')-1)" +
                    " and DATE_FORMAT(entry_time,'%m-%d') in ("+str+") and employee_type='full_time' ";
            List<Record> recordList=Db.find(sql,params.toArray());
            if(recordList.size()>0){
                for(Record record:recordList){
                   /* Date entryTime=record.getDate("entry_time");
                    int entryMonth=Integer.valueOf(DateUtils.formatDate(entryTime,"MM"));
                    double days=(12-entryMonth)/2.4;
                    //int leaveDays=(int) new BigDecimal(days).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
                    int leaveDays=(int)days;
                    Db.update("update pers_org_employee set annual_leave_days="+leaveDays+",update_date=now() " +
                            "where id=?",record.getStr("id"));*/
                    Date currDate=DateUtils.parseDate(DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
                    Date yearMaxDate=DateUtils.parseDate(DateUtils.getYear()+"-12"+"-31");
                    int day= (int)(DateUtils.getDistanceOfTwoDate(currDate,yearMaxDate)+1);
                    int yearLeaveDay=day/73;
                    persOrgEmployeeLeaveBalanceService.resetEmpYearLeave(record.getStr("id"),yearLeaveDay);

                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        /*Date entryTime=DateUtils.parseDate("2022-07-22");
        int entryMonth=Integer.valueOf(DateUtils.formatDate(entryTime,"MM"));
        double days=(12-entryMonth)/2.4;
        //int leaveDays=(int) new BigDecimal(days).setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
        int leaveDays=(int)days;
        System.out.println(leaveDays);*/

        Date currDate=DateUtils.parseDate("2024-01-04");
        Date yearMaxDate=DateUtils.parseDate(DateUtils.getYear()+"-12"+"-31");
        int day= (int)(DateUtils.getDistanceOfTwoDate(currDate,yearMaxDate)+1);
        System.out.println(day);
        int yearLeaveDay=day/73;
        System.out.println(yearLeaveDay);
    }

    public void updateQuit(){
        String sql="select a.* from pers_org_employee_quit a " +
                "LEFT JOIN pers_task b on b.record_id=a.id " +
                "where a.del_flag='0' and TO_DAYS(a.estimate_quit_date)=TO_DAYS(now()) and b.is_end='1' and a.`status`='3' ";
        List<Record> recordList= Db.find(sql);
        if(recordList!=null && recordList.size()>0){
            for(Record record:recordList){
                PersOrgEmployee persOrgEmployee=persOrgEmployeeService.findById(record.getStr("employee_id"));
                if(persOrgEmployee!=null){
                    persOrgEmployee.setArchiveStatus("quit");
                    persOrgEmployee.setUpdateDate(new Date());
                    persOrgEmployee.setQuitTime(new Date());

                    boolean flag=persOrgEmployeeService.saveOrgEmployee(persOrgEmployee,new ArrayList<>(),new ArrayList<>());
                    if(!flag || StrKit.notBlank(record.getStr("transfer_emp_id"))){
                        //修改组织机构负责人为交接人
                        String transferEmpId=record.getStr("transfer_emp_id");
                        String transferUserId=Db.queryStr("select user_id from pers_emp_user where emp_id=? limit 1 ",transferEmpId);
                        String quitUserId=Db.queryStr("select user_id from pers_emp_user where emp_id=? limit 1 ",persOrgEmployee.getId());
                        Db.update("update pers_org set link_man=?,update_date=NOW() where del_flag='0'  and link_man=? ",transferUserId,quitUserId);
                    }
                }
            }
        }
    }

    public void updateChangeDept(){
        String sql="select a.* from pers_org_employee_change_apply a " +
                "LEFT JOIN pers_task b on b.record_id=a.id " +
                "where a.del_flag='0' and TO_DAYS(a.end_time)=TO_DAYS(now()) and a.status='3' and a.change_date_type='1' ";
        List<Record> recordList=Db.find(sql);
        if(recordList!=null && recordList.size()>0){
            for(Record record:recordList){
                PersOrgEmployee persOrgEmployee=persOrgEmployeeService.findById(record.getStr("emp_id"));
                if(persOrgEmployee!=null){
                    String oldDeptId=record.getStr("new_dept_id");
                    String oldPositionId=record.getStr("new_position_id");

                    String newDeptId=record.getStr("dept_id");
                    String newPositionId=record.getStr("position_id");

                    List<String> delRelParams=new ArrayList<>();
                    PersOrgEmployeeRel oldDeptRel=persOrgEmployeeRelService.getRel(persOrgEmployee.getId(),"dept",oldDeptId);
                    PersOrgEmployeeRel oldPositionRel=persOrgEmployeeRelService.getRel(persOrgEmployee.getId(),"position",oldPositionId);

                    if(oldDeptRel!=null){
                        delRelParams.add(oldDeptRel.getId());
                    }
                    if(oldPositionRel!=null){
                        delRelParams.add(oldPositionRel.getId());
                        PersPosition oldPosition=persPositionService.findById(oldPositionRel.getRelationshipId());
                        if(oldPosition!=null){
                            PersOrgEmployeeRel oldRoleRel=persOrgEmployeeRelService.getRel(persOrgEmployee.getId(),"role",oldPosition.getRoleId());
                            if(oldRoleRel!=null){
                                delRelParams.add(oldRoleRel.getId());
                            }
                        }
                    }
                    persOrgEmployeeRelService.deleteRelByIds(delRelParams,record.getStr("create_by"));



                    PersOrgEmployeeRel deptRel=new PersOrgEmployeeRel();
                    deptRel.setId(IdGen.getUUID());
                    deptRel.setEmpId(persOrgEmployee.getId());
                    deptRel.setRelationshipType("dept");
                    deptRel.setIsMain("1");
                    deptRel.setRelationshipId(newDeptId);

                    PersOrgEmployeeRel positionRel=new PersOrgEmployeeRel();
                    positionRel.setId(IdGen.getUUID());
                    positionRel.setEmpId(persOrgEmployee.getId());
                    positionRel.setRelationshipType("position");
                    positionRel.setRelationshipId(newPositionId);
                    positionRel.setIsMain("1");

                    PersPosition newPosition=persPositionService.findById(newPositionId);

                    List<PersOrgEmployeeRel> addRelList=new ArrayList<>();
                    addRelList.add(deptRel);
                    addRelList.add(positionRel);
                    if(newPosition!=null && StrKit.notBlank(newPosition.getRoleId())){
                        PersOrgEmployeeRel roleRel=new PersOrgEmployeeRel();
                        roleRel.setId(IdGen.getUUID());
                        roleRel.setEmpId(persOrgEmployee.getId());
                        roleRel.setRelationshipType("role");
                        roleRel.setRelationshipId(newPosition.getRoleId());
                        roleRel.setIsMain("1");
                        addRelList.add(roleRel);
                    }
                    User user=userService.getUserByEmpId(persOrgEmployee.getId());
                    persOrgEmployeeRelService.addRel(addRelList,user.getId(),record.getStr("create_by"));
                    persOrgEmployeeService.updateQiyeUserId(persOrgEmployee.getId());


                    /*persOrgEmployee.setOrgId(record.getStr("org_id"));
                    persOrgEmployee.setDeptId(record.getStr("dept_id"));
                    persOrgEmployee.setUpdateDate(new Date());
                    persOrgEmployee.setUpdateBy(record.getStr("create_by"));

                    userService.updateSaveUser(persOrgEmployee,record.getStr("create_by"));*/
                }
            }
        }
    }
}
