package com.cszn.personnel.web.controller.notice;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersNoticeTypeService;
import com.cszn.integrated.service.entity.pers.PersNoticeType;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * @Description 公告类型管理
 * <AUTHOR>
 * @Date 2019/7/16
 **/
@RequestMapping(value="/pers/noticeType", viewPath="/modules_page/pers/noticeType")
public class NoticeTypeController extends BaseController {

    @Inject
    private PersNoticeTypeService persNoticeTypeService;


    /**
     * 跳转公告类型界面
     */
    public void index(){
       render("noticeTypeIndex.html");
    }


    /**
     * 公告类型列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        PersNoticeType noticeType = getBean(PersNoticeType.class,"",true);
        Page<PersNoticeType> page = persNoticeTypeService.findListPage(getParaToInt("page"), getParaToInt("limit"),noticeType);
        renderJson(new DataTable<PersNoticeType>(page));
    }

    /**
     * 公告类型删除
     */
    public void delete(){
        String id = getPara("id");
        PersNoticeType noticeType = persNoticeTypeService.get(id);
        if(noticeType != null){
            boolean flag = persNoticeTypeService.delNoticeType(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "删除成功"));
            } else {
                renderJson(Ret.fail("msg", "删除失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "删除失败"));
        }
    }
    
    
    /**
     * 跳转新增界面
     */
    public void add(){
    	setAttr("noticeType",new PersNoticeType());
    	render("noticeTypeForm.html");
    }


    /**
     * 跳转修改界面
     */
    public void edit(){
        PersNoticeType noticeType = persNoticeTypeService.get(getPara("id"));
        setAttr("noticeType",noticeType);
        render("noticeTypeForm.html");
    }

    /**
     * 保存
     */
    public void save(){
        PersNoticeType noticeType = getBean(PersNoticeType.class,"noticeType",true);
        if(noticeType == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        if(StringUtils.isNotBlank(noticeType.getId())){
            PersNoticeType noticeTypeExist = persNoticeTypeService.get(noticeType.getId());
            if(noticeTypeExist == null){ renderJson(Ret.fail("msg", "保存失败")); return; }
        }
        String flag = persNoticeTypeService.saveNoticeType(noticeType,AuthUtils.getUserId());
        if("suc".equals(flag)){
            renderJson(Ret.ok("msg", "保存成功"));
        }else if("".equals(flag)){
            renderJson(Ret.fail("msg", "公告类型不能重复"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }
}
