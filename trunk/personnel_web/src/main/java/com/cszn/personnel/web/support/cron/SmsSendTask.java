package com.cszn.personnel.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersSmsSendDetail;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;

import java.util.*;

//@Cron("*/3 * * * *")
public class SmsSendTask implements Runnable {

    @Inject
    private PersOrgService persOrgService;

    @Override
    public void run() {
        String sql="select * from pers_sms_send where `status`='0' and del_flag='0' and send_time<=now() ";
        List<Record> smsSendList= Db.find(sql);
        if(smsSendList==null || smsSendList.size()==0){
            return;
        }

        Map<String,String> sendParams=new HashMap<>();
        sendParams.put("tempId", SendType.customContent.getTemplateId());
        for(Record smsSend:smsSendList){
            Db.update("update pers_sms_send set `status`='1',update_date=? where id=?",new Date(),smsSend.getStr("id"));
            List<String> sendDeptIdList=Db.query("select dept_id from pers_sms_send_dept_rel where sms_id=? ",smsSend.getStr("id"));
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            for (String deptId : sendDeptIdList) {
                orgList.add(persOrgService.findById(deptId));
                persOrgService.findChildren(zTreeList,orgList,deptId);
            }
            List<String> allDeptIdList=new ArrayList<>();
            String str="";
            for(PersOrg org:orgList){
                allDeptIdList.add(org.getId());
                str+="?,";
            }
            str=str.substring(0,str.length()-1);

            List<Record> empList=Db.find("select a.id,a.phone_num from pers_org_employee a inner join pers_org_employee_rel b on a.id=b.emp_id and relationship_type='dept' " +
                    " where a.del_flag='0' and archive_status='incumbency' and b.relationship_id in ("+str+") ",allDeptIdList.toArray());
            Set<String> set=new HashSet<>();

            List<PersSmsSendDetail> smsSendDetailList=new ArrayList<>();
            for(Record emp:empList){
                String id=emp.getStr("id");
                String phoneNum=emp.getStr("phone_num");
                if(set.contains(phoneNum)){
                    continue;
                }
                PersSmsSendDetail smsSendDetail=new PersSmsSendDetail();
                smsSendDetail.setId(IdGen.getUUID());
                smsSendDetail.setSmsId(smsSend.getStr("id"));
                smsSendDetail.setEmpId(id);
                smsSendDetail.setTelephone(phoneNum);
                smsSendDetail.setDelFlag("0");
                smsSendDetail.setCreateDate(new Date());
                smsSendDetail.setUpdateDate(new Date());


                //发送短信
                sendParams.put("mobile",phoneNum);
                sendParams.put("data", "{\"content\":\""+ smsSend.getStr("content")+"\"}");

                String resultStr= HttpClientsUtils.httpPostForm(Global.sendMessageUrl, sendParams,null,"UTF-8");
                smsSendDetail.setStatus("2");
                if(resultStr.startsWith("{") && resultStr.endsWith("}")) {
                    JSONObject object = JSON.parseObject(resultStr);
                    if (object.containsKey("Type") && "1".equals(object.getString("Type"))) {
                        smsSendDetail.setStatus("1");
                    }
                }



                smsSendDetailList.add(smsSendDetail);
                set.add(phoneNum);


            }
            Db.batchSave(smsSendDetailList,smsSendDetailList.size());
        }
    }
}
