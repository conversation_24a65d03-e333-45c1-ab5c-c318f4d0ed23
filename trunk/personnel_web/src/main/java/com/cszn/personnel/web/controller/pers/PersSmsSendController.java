package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersSmsSendService;
import com.cszn.integrated.service.entity.pers.PersSmsSend;
import com.cszn.integrated.service.entity.pers.PersSmsSendDeptRel;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

@RequestMapping(value="/pers/sms", viewPath="/modules_page/pers/sms/")
public class PersSmsSendController extends BaseController {

    @Inject
    PersSmsSendService persSmsSendService;

    public void index(){
        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            PersSmsSend smsSend=persSmsSendService.findById(id);
            List<String> deptIdsList= Db.query(" select dept_id from pers_sms_send_dept_rel where sms_id=? ",id);
            setAttr("model",smsSend);
            setAttr("deptIdsList",deptIdsList);
        }
        render("form.html");
    }

    public void pageList(){
        PersSmsSend smsSend=getBean(PersSmsSend.class,"",true);
        Page<PersSmsSend> smsSendPage=persSmsSendService
                .findPageList(getParaToInt("page"),getParaToInt("limit"),smsSend);

        renderJson(new DataTable<PersSmsSend>(smsSendPage));
    }

    public void saveSmsSend(){
        String deptIds=getPara("deptIds");
        PersSmsSend smsSend=getBean(PersSmsSend.class,"",true);
        boolean flag=persSmsSendService.saveSmsSend(smsSend, AuthUtils.getUserId());
        if(flag){
            Db.update("delete from pers_sms_send_dept_rel where sms_id=? ",smsSend.getId());
            String[] idArray=null;
            if(deptIds.indexOf(",")!=-1) {
                idArray=deptIds.split(",");
            }else{
                idArray=new String[]{deptIds};
            }
            List<PersSmsSendDeptRel> smsSendDeptRelList=new ArrayList<>();
            for (int i = 0; i < idArray.length; i++) {
                PersSmsSendDeptRel rel=new PersSmsSendDeptRel();
                rel.setId(IdGen.getUUID());
                rel.setSmsId(smsSend.getId());
                rel.setDeptId(idArray[i]);
                smsSendDeptRelList.add(rel);
            }
            Db.batchSave(smsSendDeptRelList,smsSendDeptRelList.size());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void sendDetailIndex(){
        String id=getPara("id");

        setAttr("id",id);
        render("detail.html");
    }

    public void sendDetailPageList(){
        String id=getPara("id");
        String select="select a.telephone,a.`status`,b.work_num,b.full_name  ";
        String sql=" from pers_sms_send_detail a left join pers_org_employee b on a.emp_id=b.id " +
                "where a.del_flag='0' and sms_id=? ";
        Page<Record> recordPage=Db.paginate(getParaToInt("page"),getParaToInt("limit"),select,sql,id);

        renderJson(new DataTable<Record>(recordPage));
    }
}
