package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.enums.PayRatioType;
import com.cszn.integrated.service.entity.enums.PersTaskType;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.integrated.service.provider.pers.PersOrgEmployeeCheckinDaySummaryServiceImpl;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.xssf.usermodel.*;

import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;


@RequestMapping(value="/providentFund", viewPath="/modules_page/pers/")
public class persOrgEmployeeProvidentFundController extends BaseController {

    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersProvidentFundApplyRecordService persProvidentFundApplyRecordService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private PersOrgEmployeeProvidentFundService persOrgEmployeeProvidentFundService;
    @Inject
    private PersOrgCompanyService persOrgCompanyService;
    @Inject
    private PersApprovalService persApprovalService;
    @Inject
    private PersTaskService persTaskService;
    @Inject
    private PersPositionService persPositionService;
    @Inject
    private UserService userService;
    @Inject
    private DictService dictService;


    public void providentFundIndex(){

        List<PersOrgCompany> orgCompanyList=persOrgCompanyService.orgCompanyList();

        setAttr("orgCompanyList",orgCompanyList);
        render("providentFund/index.html");
    }

    public void providentFundForm(){
        String deptId=getPara("deptId");
        String yearMonth=getPara("yearMonth");
        String companyId=getPara("companyId");

        String name=persOrgService.getOrgParentNames(deptId);
        setAttr("deptId",deptId);
        setAttr("deptName",name);
        setAttr("yearMonth",yearMonth);
        setAttr("companyId",companyId);


        Date date =DateUtils.parseDate(yearMonth);
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);

        String lastYearMonth=DateUtils.formatDate(calendar.getTime(),"yyyy-MM");

        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,deptId);

        List<String> deptIdList=new ArrayList<>();
        deptIdList.add(deptId);

        for (PersOrg org : orgList) {
            deptIdList.add(org.getId());
        }

        //先获取上个月购买的
        List<String> lastMonthEmpIds=Db.query(" select b.emp_id from pers_provident_fund_apply_record a  " +
                "INNER join pers_org_employee_provident_fund b on a.id=b.apply_record_id " +
                "where a.`year_month`=? and a.company_id=? and a.dept_id=? and a.`status` in ('2','3') and b.del_flag='0'",lastYearMonth,companyId,deptId);
        List<PersOrgEmployee> allEmployeeList=null;
        if(lastMonthEmpIds.size()>0){
            allEmployeeList=new ArrayList<>();
            for (String lastMonthEmpId : lastMonthEmpIds) {
                allEmployeeList.add(persOrgEmployeeService.findById(lastMonthEmpId));
            }

        }else{
            allEmployeeList=persOrgEmployeeService.findEmpListByDepts(deptIdList);
        }



        List<String> empIds=Db.query("select a.emp_id from pers_org_employee_provident_fund a left join pers_org_employee b on b.id=a.emp_id " +
                "left join pers_provident_fund_apply_record c on a.apply_record_id=c.id " +
                "where a.`year_month`=? and a.del_flag='0' and c.`status` in ('2','3')  ",yearMonth);
        //排除已买的员工
        List<PersOrgEmployee> employeeList=new ArrayList<>();
        for (PersOrgEmployee employee : allEmployeeList) {
            if(!empIds.contains(employee.getId())){
                employeeList.add(employee);
            }
        }


        List<String> empIdList=new ArrayList<>();
        for (PersOrgEmployee employee : employeeList) {
            empIdList.add(employee.getId());
        }
        //查询上个月的记录
        List<PersOrgEmployeeProvidentFund> employeeProvidentFundList=persOrgEmployeeProvidentFundService.findEmployeeProvidentFundList(lastYearMonth,empIdList);

        Map<String,PersOrgEmployeeProvidentFund> employeeProvidentFundMap=new HashMap<>();

        for (PersOrgEmployeeProvidentFund persOrgEmployeeProvidentFund : employeeProvidentFundList) {
            employeeProvidentFundMap.put(persOrgEmployeeProvidentFund.getEmpId(),persOrgEmployeeProvidentFund);
        }

        List<Record> recordList=new ArrayList<>();


        if(lastMonthEmpIds.size()>0){
            //如果上个月有记录，则使用上个月的数据
            for (PersOrgEmployee employee : employeeList) {
                Record record=new Record();
                record.set("empId",employee.getId());
                record.set("workNum",employee.getWorkNum());
                record.set("fullName",employee.getFullName());
                record.set("idcard",employee.getIdCard());
                record.set("sex",employee.getSex());
                record.set("maritalStatus",employee.getMaritalStatus());
                record.set("isLocal",employee.getIsLocal());
                record.set("phoneNum",employee.getPhoneNum());

                PersOrgEmployeeProvidentFund employeeProvidentFund=employeeProvidentFundMap.get(employee.getId());
                record.set("type",employeeProvidentFund.getType());
                record.set("payRatio",employeeProvidentFund.getPayRatio());
                recordList.add(record);
            }

        }else{
            for (PersOrgEmployee employee : employeeList) {
                Record record=new Record();
                record.set("empId",employee.getId());
                record.set("workNum",employee.getWorkNum());
                record.set("fullName",employee.getFullName());
                record.set("idcard",employee.getIdCard());
                record.set("sex",employee.getSex());
                record.set("maritalStatus",employee.getMaritalStatus());
                record.set("isLocal",employee.getIsLocal());
                record.set("phoneNum",employee.getPhoneNum());

                PersOrgEmployeeProvidentFund employeeProvidentFund=employeeProvidentFundMap.get(employee.getId());
                if(employeeProvidentFund!=null){
                    if("1".equals(employeeProvidentFund.getType()) || "2".equalsIgnoreCase(employeeProvidentFund.getType())){
                        record.set("type","2");
                    }else if("3".equals(employeeProvidentFund.getType()) || "4".equalsIgnoreCase(employeeProvidentFund.getType())){
                        record.set("type","4");
                    }
                    record.set("payRatio",employeeProvidentFund.getPayRatio());
                }else{
                    //初始化
                    //record.set("type","1");
                    record.set("type","1");
                }
                recordList.add(record);
            }

            //上个月有增、续，且离职的
            for(String key:employeeProvidentFundMap.keySet()){
                if(!empIdList.contains(key)){
                    PersOrgEmployee employee=persOrgEmployeeService.findById(key);
                    if("quit".equals(employee.getArchiveStatus())){

                        Record record=new Record();
                        record.set("empId",employee.getId());
                        record.set("workNum",employee.getWorkNum());
                        record.set("fullName",employee.getFullName());
                        record.set("idcard",employee.getIdCard());
                        record.set("sex",employee.getSex());
                        record.set("maritalStatus",employee.getMaritalStatus());
                        record.set("isLocal",employee.getIsLocal());
                        record.set("phoneNum",employee.getPhoneNum());
                        record.set("payRatio",employeeProvidentFundMap.get(key).getPayRatio());
                        record.set("type","3");

                        recordList.add(record);
                    }
                }
            }
        }


        List<Record> addList=new ArrayList<>();
        List<Record> continueList=new ArrayList<>();
        List<Record> delList=new ArrayList<>();
        List<Record> abandonList=new ArrayList<>();

        for (Record record : recordList) {
            if("1".equals(record.getStr("type"))){
                addList.add(record);
            }else if("2".equals(record.getStr("type"))){
                continueList.add(record);
            }else if("3".equals(record.getStr("type"))){
                delList.add(record);
            }else if("4".equals(record.getStr("type"))){
                abandonList.add(record);
            }
            PersOrgEmployeeProvidentFund lastProvidentFund=employeeProvidentFundMap.get(record.getStr("empId"));
            record.set("lastMonthType","");
            if(lastProvidentFund!=null){
                record.set("lastMonthType",lastProvidentFund.getType());
            }
        }
        List<PersOrgCompany> orgCompanyList=persOrgCompanyService.orgCompanyList();
        setAttr("orgCompanyList",orgCompanyList);
        setAttr("payRatioTypes", PayRatioType.values());
        /*setAttr("addList",addList);
        setAttr("continueList",continueList);
        setAttr("delList",delList);
        setAttr("abandonList",abandonList);*/
        render("providentFund/form.html");
    }

    public void getLastMonthStatus(){
        String empId=getPara("empId");
        String yearMonth=getPara("yearMonth");
        Date date =DateUtils.parseDate(yearMonth);
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);

        String lastYearMonth=DateUtils.formatDate(calendar.getTime(),"yyyy-MM");
        String liabilityInsuranceType="";
        String providentFundType="";
        String socialSecurityType="";

        liabilityInsuranceType=Db.queryStr("select `type` from pers_org_employee_liability_insurance where del_flag='0' and emp_id=? and `year_month`=? limit 1",empId,lastYearMonth);
        providentFundType=Db.queryStr("select `type` from pers_org_employee_provident_fund where del_flag='0' and emp_id=? and `year_month`=? limit 1",empId,lastYearMonth);
        socialSecurityType=Db.queryStr("select `type` from pers_org_employee_social_security where del_flag='0' and emp_id=? and `year_month`=? limit 1",empId,lastYearMonth);

        Record record=new Record();
        record.set("liabilityInsuranceType",liabilityInsuranceType);
        record.set("providentFundType",providentFundType);
        record.set("socialSecurityType",socialSecurityType);
        renderJson(Ret.ok("data",record));
    }

    public void editProvidentFundForm(){
        String id=getPara("id");
        String taskId=getPara("taskId");
        String userId=getPara("userId");

        if(StrKit.isBlank(id)){
            PersTask task=persTaskService.findByTaskId(taskId);
            id=task.getRecordId();
        }
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }

        PersProvidentFundApplyRecord applyRecord=persProvidentFundApplyRecordService.findById(id);
        setAttr("yearMonth",applyRecord.getYearMonth());
        Date date =DateUtils.parseDate(applyRecord.getYearMonth());
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);

        String lastYearMonth=DateUtils.formatDate(calendar.getTime(),"yyyy-MM");



        List<Record> addList=new ArrayList<>();
        List<Record> continueList=new ArrayList<>();
        List<Record> delList=new ArrayList<>();
        List<Record> abandonList=new ArrayList<>();

        List<Record> recordList=Db.find("select a.id,a.emp_id as empId,b.work_num as workNum,b.full_name as fullName,b.id_card as idcard ,a.phone_num as phoneNum" +
                ",a.pay_ratio as payRatio,a.type,a.company_id as companyId,a.marital_status as maritalStatus,a.is_local as isLocal,a.status,a.exception_remark as exceptionRemark" +
                ",b.sex,a.dept_id,a.position_id " +
                "from pers_org_employee_provident_fund a  left join pers_org_employee b on a.emp_id=b.id " +
                " where a.apply_record_id=? and a.del_flag='0' order by b.work_num ",applyRecord.getId());


        List<String> empIdList=new ArrayList<>();

        for (Record record : recordList) {
            if("1".equals(record.getStr("type"))){
                addList.add(record);
            }else if("2".equals(record.getStr("type"))){
                continueList.add(record);
            }else if("3".equals(record.getStr("type"))){
                delList.add(record);
            }else if("4".equals(record.getStr("type"))){
                abandonList.add(record);
            }

            if("male".equals(record.getStr("sex"))){
                record.set("sex","男");
            }else if("female".equals(record.getStr("sex"))){
                record.set("sex","女");
            }
            //record.set("positionName",persPositionService.findById(record.getStr("position_id")).getPositionName());
            PersPosition persPosition=persPositionService.findById(record.getStr("position_id"));
            if(persPosition!=null){
                record.set("positionName",persPosition.getPositionName());
            }else{
                record.set("positionName","");
            }
            record.set("deptName",persOrgService.getOrgParentNames(record.getStr("dept_id")));

            empIdList.add(record.getStr("empId"));

            /*PersOrgEmployeeProvidentFund lastProvidentFund=employeeProvidentFundMap.get(record.getStr("empId"));
            record.set("lastMonthType","");
            if(lastProvidentFund!=null){
                record.set("lastMonthType",lastProvidentFund.getType());
            }*/
        }
        //查询上个月的记录
        List<PersOrgEmployeeProvidentFund> employeeProvidentFundList=persOrgEmployeeProvidentFundService.findEmployeeProvidentFundList(lastYearMonth,empIdList);
        Map<String,PersOrgEmployeeProvidentFund> employeeProvidentFundMap=new HashMap<>();

        for (PersOrgEmployeeProvidentFund persOrgEmployeeProvidentFund : employeeProvidentFundList) {
            employeeProvidentFundMap.put(persOrgEmployeeProvidentFund.getEmpId(),persOrgEmployeeProvidentFund);
        }
        for (Record record : recordList) {

            PersOrgEmployeeProvidentFund lastProvidentFund=employeeProvidentFundMap.get(record.getStr("empId"));
            record.set("lastMonthType","");
            if(lastProvidentFund!=null){
                record.set("lastMonthType",lastProvidentFund.getType());
            }
        }



        PersTask persTask=persTaskService.findByRecordId(applyRecord.getId());
        if(persTask!=null){
            Map<String,Object> taskDetail=persApprovalService.getTaskDetail(persTask.getTaskId(),userId);
            setAttr("stepts",taskDetail.get("stepts"));
            setAttr("taskState",taskDetail.get("taskState"));
            setAttr("isSaveHandle",taskDetail.get("isSaveHandle"));
            setAttr("currentStepName",taskDetail.get("currentStepName"));
            setAttr("currentStepAlias",taskDetail.get("currentStepAlias"));

            JSONArray currentSteps=(JSONArray)taskDetail.get("currentSteps");
            //批准
            boolean allowApprove=false;
            //拒绝
            boolean allowReject=false;
            //中止
            boolean allowAbort=false;
            //提交
            boolean allowSubmit=false;
            if(currentSteps!=null){
                for(int i=0;i<currentSteps.size();i++){
                    JSONObject currentStep=currentSteps.getJSONObject(i);
                    boolean flag=false;
                    for(int j=0;j<currentStep.getJSONArray("UserIds").size();j++){
                        if(!flag && userId.equalsIgnoreCase(currentStep.getJSONArray("UserIds").getString(j))){
                            flag=true;
                        }
                    }
                    if(flag){
                        allowApprove=currentStep.getBoolean("AllowApprove");
                        allowReject=currentStep.getBoolean("AllowReject");

                        allowSubmit=currentStep.getBoolean("AllowSubmit");
                    }
                    allowAbort=currentStep.getBoolean("AllowAbort");
                }
            }


            setAttr("allowApprove",allowApprove);
            setAttr("allowReject",allowReject);
            setAttr("allowAbort",allowAbort);
            setAttr("allowSubmit",allowSubmit);
            setAttr("taskId",persTask.getTaskId());
        }

        List<PersOrgCompany> orgCompanyList=persOrgCompanyService.orgCompanyList();
        setAttr("orgCompanyList",orgCompanyList);
        setAttr("payRatioTypes", PayRatioType.values());
        setAttr("addList",addList);
        setAttr("continueList",continueList);
        setAttr("delList",delList);
        setAttr("abandonList",abandonList);
        setAttr("type","edit");
        setAttr("id",id);
        setAttr("applyRecord",applyRecord);
        persTaskService.getSubmitInfo(persTask);
        if(!AuthUtils.isLogin()){
            setAttr("device","mobile");
        }
        setAttr("persTask",persTask);
        render("providentFund/form.html");
    }

    public void providentFundApplySave(){
        String deptId=getPara("deptId");
        String yearMonth=getPara("yearMonth");
        String dataArray=getPara("data");
        String companyId=getPara("companyId");
        String remark=getPara("remark");
        if(StrKit.isBlank(deptId) || StrKit.isBlank(yearMonth) || StrKit.isBlank(dataArray)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        JSONArray jsonArray= JSON.parseArray(dataArray);
        if(jsonArray==null || jsonArray.size()==0){
            renderJson(Ret.fail("msg","提交的记录不能为0条"));
            return;
        }

        PersOrgCompany orgCompany = persOrgCompanyService.findById(companyId);
        if(StrKit.isBlank(orgCompany.getProvidentFundUserId())){
            renderJson(Ret.fail("msg","购买单位公积金经办人未设置"));
            return;
        }


        List<String> deptIds=new ArrayList<>();
        deptIds.add(deptId);
        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,deptId);

        for (PersOrg org : orgList) {
            deptIds.add(org.getId());
        }


        PersProvidentFundApplyRecord applyRecord=new PersProvidentFundApplyRecord();
        applyRecord.setId(IdGen.getUUID());
        applyRecord.setYearMonth(yearMonth);
        applyRecord.setDeptId(deptId);
        applyRecord.setCompanyId(companyId);
        applyRecord.setDelFlag("0");
        applyRecord.setStatus("1");
        applyRecord.setRemark(remark);
        applyRecord.setApplyUserId(AuthUtils.getUserId());
        applyRecord.setApplyTime(new Date());
        applyRecord.setCreateBy(AuthUtils.getUserId());
        applyRecord.setCreateDate(new Date());
        applyRecord.setUpdateBy(AuthUtils.getUserId());
        applyRecord.setUpdateDate(new Date());

        List<PersOrgEmployeeProvidentFund> providentFundList=new ArrayList<>();

        String str="";
        List<String> parmas=new ArrayList<>();
        parmas.add(yearMonth);
        for (int i = 0; i < jsonArray.size(); i++) {

            JSONObject jsonObject=jsonArray.getJSONObject(i);

            str+="?,";
            parmas.add(jsonObject.getString("empId"));
            PersOrgEmployee employee=persOrgEmployeeService.findById(jsonObject.getString("empId"));
            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                if(deptIds.contains(empDeptId)){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }

            PersOrgEmployeeProvidentFund employeeProvidentFund=new PersOrgEmployeeProvidentFund();
            employeeProvidentFund.setId(IdGen.getUUID());
            employeeProvidentFund.setApplyRecordId(applyRecord.getId());
            employeeProvidentFund.setIdcard(employee.getIdCard());
            employeeProvidentFund.setDeptId(empDeptId);
            employeeProvidentFund.setPositionId(empPositionId);
            employeeProvidentFund.setEmpId(jsonObject.getString("empId"));
            employeeProvidentFund.setType(jsonObject.getString("type"));
            employeeProvidentFund.setPayRatio(jsonObject.getString("payRatio"));
            employeeProvidentFund.setCompanyId(jsonObject.getString("companyId"));
            employeeProvidentFund.setIsLocal("");
            employeeProvidentFund.setMaritalStatus("");
            employeeProvidentFund.setPhoneNum(employee.getPhoneNum());
            //employeeProvidentFund.setIsLocal(jsonObject.getString("isLocal"));
            employeeProvidentFund.setMaritalStatus(jsonObject.getString("maritalStatus"));
            employeeProvidentFund.setYearMonth(yearMonth);
            employeeProvidentFund.setDelFlag("0");
            employeeProvidentFund.setCreateBy(AuthUtils.getUserId());
            employeeProvidentFund.setCreateDate(new Date());
            employeeProvidentFund.setUpdateBy(AuthUtils.getUserId());
            employeeProvidentFund.setUpdateDate(new Date());

            /*if(StrKit.isBlank(employeeProvidentFund.getIsLocal())){
                renderJson(Ret.fail("msg","是否本地户籍必须要全部填写"));
                return;
            }*/
            if(StrKit.isBlank(employeeProvidentFund.getMaritalStatus())){
                renderJson(Ret.fail("msg","婚姻状态必须要全部填写"));
                return;
            }


            providentFundList.add(employeeProvidentFund);
        }
        str=str.substring(0,str.length()-1);

        List<String> empNames=Db.query("select b.full_name from pers_org_employee_provident_fund a left join pers_org_employee b on b.id=a.emp_id " +
                "left join pers_provident_fund_apply_record c on a.apply_record_id=c.id " +
                "where a.`year_month`=? and a.del_flag='0' and c.`status` in ('2','3') and a.emp_id in ("+str+") ",parmas.toArray());
        /*if(empNames.size()>0){
            renderJson(Ret.fail("msg",JSON.toJSONString(empNames)+"该月已存在记录"));
            return;
        }*/

        if(applyRecord.save()){
            Db.batchSave(providentFundList,providentFundList.size());

            for (PersOrgEmployeeProvidentFund employeeProvidentFund : providentFundList) {
                PersOrgEmployee employee=persOrgEmployeeService.findById(employeeProvidentFund.getEmpId());
                //employee.setIsLocal(employeeProvidentFund.getIsLocal());
                employee.setMaritalStatus(employeeProvidentFund.getMaritalStatus());
                employee.update();
            }

            PersTask task=new PersTask();
            task.setId(IdGen.getUUID());
            task.setTaskNo(PersTaskType.providentFund.getTaskNo());
            task.setRecordId(applyRecord.getId());
            task.setIsEnd("0");
            task.setDelFlag("0");
            task.setCreateBy(applyRecord.getCreateBy());
            task.setCreateDate(new Date());
            task.setUpdateBy(AuthUtils.getUserId());
            task.setUpdateDate(new Date());
            applyRecord.setStatus("2");
            applyRecord.update();
            Map<String,Object> resultMap=persTaskService.httpCreateTask(task);
            /*if((boolean) resultMap.get("flag")){

            }else{
                renderJson(Ret.fail("msg",resultMap.get("msg")));
            }*/
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            Ret.fail("msg","操作失败");
        }
    }

    public void providentFundApplyEdit(){
        String id=getPara("id");
        String taskId=getPara("taskId");
        String dataArray=getPara("data");
        String userId=getPara("userId");
        String remark=getPara("remark");
        if(StrKit.isBlank(taskId) || StrKit.isBlank(id) || StrKit.isBlank(dataArray)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        PersProvidentFundApplyRecord applyRecord=persProvidentFundApplyRecordService.findById(id);
        JSONArray jsonArray= JSON.parseArray(dataArray);
        if(jsonArray==null || jsonArray.size()==0){
            renderJson(Ret.fail("msg","提交的记录不能为0条"));
            return;
        }
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }
        List<String> deptIds=new ArrayList<>();
        deptIds.add(applyRecord.getDeptId());
        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,applyRecord.getDeptId());

        for (PersOrg org : orgList) {
            deptIds.add(org.getId());
        }



        List<PersOrgEmployeeProvidentFund> addProvidentFundList=new ArrayList<>();

        List<PersOrgEmployeeProvidentFund> updateProvidentFundList=new ArrayList<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);

            if(StrKit.isBlank(jsonObject.getString("id"))){
                PersOrgEmployee employee=persOrgEmployeeService.findById(jsonObject.getString("empId"));
                String empDeptId=null;
                List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
                for (String e : empDeptIds) {
                    if(deptIds.contains(empDeptId)){
                        empDeptId =e;
                        break;
                    }
                }
                if(empDeptId==null){
                    empDeptId=empDeptIds.get(0);
                }
                String empPositionId=null;
                List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
                for (String e : empPositionIds) {
                    PersPosition persPosition=persPositionService.findById(e);
                    if(empDeptId.equals(persPosition.getOrgId())){
                        empPositionId=e;
                        break;
                    }
                }
                PersOrgEmployeeProvidentFund employeeProvidentFund=new PersOrgEmployeeProvidentFund();
                employeeProvidentFund.setId(IdGen.getUUID());
                employeeProvidentFund.setApplyRecordId(applyRecord.getId());
                employeeProvidentFund.setIdcard(employee.getIdCard());
                employeeProvidentFund.setDeptId(empDeptId);
                employeeProvidentFund.setPositionId(empPositionId);
                employeeProvidentFund.setEmpId(jsonObject.getString("empId"));
                employeeProvidentFund.setType(jsonObject.getString("type"));
                employeeProvidentFund.setPayRatio(jsonObject.getString("payRatio"));
                employeeProvidentFund.setCompanyId(jsonObject.getString("companyId"));
                //employeeProvidentFund.setIsLocal(jsonObject.getString("isLocal"));
                employeeProvidentFund.setMaritalStatus(jsonObject.getString("maritalStatus"));
                employeeProvidentFund.setYearMonth(applyRecord.getYearMonth());
                employeeProvidentFund.setPhoneNum(employee.getPhoneNum());
                employeeProvidentFund.setDelFlag("0");
                employeeProvidentFund.setCreateBy(userId);
                employeeProvidentFund.setCreateDate(new Date());
                employeeProvidentFund.setUpdateBy(userId);
                employeeProvidentFund.setUpdateDate(new Date());
                addProvidentFundList.add(employeeProvidentFund);
            }else{
                PersOrgEmployeeProvidentFund employeeProvidentFund=persOrgEmployeeProvidentFundService.findById(jsonObject.getString("id"));
                employeeProvidentFund.setType(jsonObject.getString("type"));
                employeeProvidentFund.setPayRatio(jsonObject.getString("payRatio"));
                employeeProvidentFund.setCompanyId(jsonObject.getString("companyId"));
                //employeeProvidentFund.setIsLocal(jsonObject.getString("isLocal"));
                employeeProvidentFund.setMaritalStatus(jsonObject.getString("maritalStatus"));
                employeeProvidentFund.setUpdateDate(new Date());
                employeeProvidentFund.setUpdateBy(userId);
                updateProvidentFundList.add(employeeProvidentFund);

            }

        }

        List<String> parmas=new ArrayList<>();
        parmas.add(applyRecord.getYearMonth());
        String str="";

        List<String> delParams=new ArrayList<>();
        delParams.add(applyRecord.getId());
        String delStr="";
        for (PersOrgEmployeeProvidentFund employeeProvidentFund : addProvidentFundList) {

            /*if(StrKit.isBlank(employeeProvidentFund.getIsLocal())){
                renderJson(Ret.fail("msg","是否本地户籍必须要全部填写"));
                return;
            }*/
            if(StrKit.isBlank(employeeProvidentFund.getMaritalStatus())){
                renderJson(Ret.fail("msg","婚姻状态必须要全部填写"));
                return;
            }

            parmas.add(employeeProvidentFund.getEmpId());
            delParams.add(employeeProvidentFund.getEmpId());
            str+="?,";
            delStr+="?,";
        }
        if(addProvidentFundList.size()>0){
            str=str.substring(0,str.length()-1);
            List<String> empNames=Db.query("select b.full_name from pers_org_employee_provident_fund a left join pers_org_employee b on b.id=a.emp_id " +
                    "left join pers_provident_fund_apply_record c on a.apply_record_id=c.id " +
                    "where a.`year_month`=? and a.del_flag='0' and c.`status` in ('2','3') and a.emp_id in ("+str+") ",parmas.toArray());
            /*if(empNames.size()>0){
                renderJson(Ret.fail("msg",JSON.toJSONString(empNames)+"该月已存在记录"));
                return;
            }*/
        }


        for (PersOrgEmployeeProvidentFund employeeProvidentFund : updateProvidentFundList) {

            /*if(StrKit.isBlank(employeeProvidentFund.getIsLocal())){
                renderJson(Ret.fail("msg","是否本地户籍必须要全部填写"));
                return;
            }*/
            if(StrKit.isBlank(employeeProvidentFund.getMaritalStatus())){
                renderJson(Ret.fail("msg","婚姻状态必须要全部填写"));
                return;
            }

            delStr+="?,";
            delParams.add(employeeProvidentFund.getEmpId());
        }
        delStr=delStr.substring(0,delStr.length()-1);
        List<String> delIds=Db.query("select a.id from pers_org_employee_provident_fund a left join pers_org_employee b on b.id=a.emp_id left join pers_provident_fund_apply_record c on a.apply_record_id=c.id " +
                "where c.id=? and a.emp_id not in ("+delStr+")  and a.del_flag='0' ",delParams.toArray());



        applyRecord.setUpdateBy(userId);
        applyRecord.setUpdateDate(new Date());
        applyRecord.setRemark(remark);
        if(applyRecord.update()){
            if(addProvidentFundList.size()>0){
                Db.batchSave(addProvidentFundList,addProvidentFundList.size());
                for (PersOrgEmployeeProvidentFund employeeProvidentFund : addProvidentFundList) {
                    PersOrgEmployee employee=persOrgEmployeeService.findById(employeeProvidentFund.getEmpId());
                    //employee.setIsLocal(employeeProvidentFund.getIsLocal());
                    employee.setMaritalStatus(employeeProvidentFund.getMaritalStatus());

                    employee.update();
                }
            }
            if(updateProvidentFundList.size()>0){
                Db.batchUpdate(updateProvidentFundList,updateProvidentFundList.size());
                for (PersOrgEmployeeProvidentFund employeeProvidentFund : updateProvidentFundList) {
                    PersOrgEmployee employee=persOrgEmployeeService.findById(employeeProvidentFund.getEmpId());
                    //employee.setIsLocal(employeeProvidentFund.getIsLocal());
                    employee.setMaritalStatus(employeeProvidentFund.getMaritalStatus());
                    employee.update();
                }
            }
            if(delIds.size()>0){

                for (String delId : delIds) {
                    Db.update("update pers_org_employee_provident_fund set del_flag='1',update_by=?,update_date=? where id=?",userId,new Date(),delId);
                }

            }

            applyRecord.setUpdateDate(new Date());
            applyRecord.update();

            Map<String,Object> taskDetailMap=persApprovalService.getTaskDetail(taskId,userId);

            Map<String,Object> resultMap=persApprovalService.doTask(taskId,PersTaskType.providentFund.getTaskNo(),"5",null,"",userId);
            /*if((boolean) resultMap.get("flag")){

            }else{
                renderJson(Ret.fail("msg",resultMap.get("msg")));
            }*/
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void providentFundApplyPageList(){
        PersProvidentFundApplyRecord applyRecord=getBean(PersProvidentFundApplyRecord.class,"",true);
        applyRecord.setYearMonth(getPara("date"));

        Page<Record> applyRecordPage=persProvidentFundApplyRecordService.pageList(getParaToInt("page"),getParaToInt("limit"),applyRecord, AuthUtils.getUserId());
        if(applyRecordPage.getList()!=null && applyRecordPage.getList().size()>0){
            for (Record record : applyRecordPage.getList()) {
                record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
                record.set("apply_user",userService.findById(record.getStr("apply_user_id")));
                record.set("company",persOrgCompanyService.findById(record.getStr("company_id")));
                PersTask persTask=persTaskService.findByRecordId(record.getStr("id"));
                if(persTask!=null){
                    Map<String,Object> taskDetail=persApprovalService.getTaskDetail(persTask.getTaskId(),AuthUtils.getUserId());
                    record.set("stepts",taskDetail.get("stepts"));
                    record.set("taskState",taskDetail.get("taskState"));
                    record.set("isSaveHandle",taskDetail.get("isSaveHandle"));
                    record.set("currentStepName",taskDetail.get("currentStepName"));
                    record.set("allowApprove",taskDetail.get("AllowApprove"));
                    record.set("allowReject",taskDetail.get("AllowReject"));
                    record.set("allowAbort",taskDetail.get("AllowAbort"));
                    record.set("allowSubmit",taskDetail.get("AllowSubmit"));
                    record.set("taskId",persTask.getTaskId());
                }
            }
        }
        renderJson(new DataTable<Record>(applyRecordPage));
    }

    public void providentFundPageList(){
        String deptId=getPara("deptId");
        String yearMonth=getPara("yearMonth");
        String status=getPara("status");


    }

    public void providentFundSave(){
        String empId=getPara("empId");
        String yearMonth=getPara("yearMonth");
        String companyId=getPara("companyId");

        PersOrgEmployeeProvidentFund employeeMaxProvidentFund=persOrgEmployeeProvidentFundService.findEmployeeProvidentFundMaxMonth(empId);

        PersOrgEmployee employee=persOrgEmployeeService.findById(empId);

        List<String> deptIds=new ArrayList<>();

        String empDeptId=null;
        List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
        for (String e : empDeptIds) {
            if(deptIds.contains(empDeptId)){
                empDeptId =e;
                break;
            }
        }
        if(empDeptId==null){
            empDeptId=empDeptIds.get(0);
        }
        String empPositionId=null;
        List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
        for (String e : empPositionIds) {
            PersPosition persPosition=persPositionService.findById(e);
            if(empDeptId.equals(persPosition.getOrgId())){
                empPositionId=e;
                break;
            }
        }
        PersOrgEmployeeProvidentFund employeeProvidentFund=new PersOrgEmployeeProvidentFund();
        employeeProvidentFund.setId(IdGen.getUUID());
        employeeProvidentFund.setApplyRecordId(null);
        employeeProvidentFund.setIdcard(employee.getIdCard());
        employeeProvidentFund.setDeptId(empDeptId);
        employeeProvidentFund.setPositionId(empPositionId);
        employeeProvidentFund.setEmpId(employee.getId());
        employeeProvidentFund.setType("2");
        employeeProvidentFund.setCompanyId(companyId);
        if(employeeMaxProvidentFund!=null){
            employeeProvidentFund.setPayRatio(employeeMaxProvidentFund.getPayRatio());
        }else{
            employeeProvidentFund.setPayRatio(PayRatioType.five.getKey());
        }
        employeeProvidentFund.setMaritalStatus(employee.getMaritalStatus());
        employeeProvidentFund.setYearMonth(yearMonth);
        employeeProvidentFund.setPhoneNum(employee.getPhoneNum());
        employeeProvidentFund.setDelFlag("0");
        employeeProvidentFund.setCreateBy(null);
        employeeProvidentFund.setCreateDate(new Date());
        employeeProvidentFund.setUpdateBy(null);
        employeeProvidentFund.setUpdateDate(new Date());
        if(employeeMaxProvidentFund.getYearMonth().equals(employeeProvidentFund.getYearMonth())){
            renderCodeFailed("已存在");
            return;
        }
        if(employeeProvidentFund.save()){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed();
        }
    }

    public void empTable(){
        render("providentFund/empTable.html");
    }

    public void providentFundReportFormIndex(){

        List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList();
        setAttr("companyList",companyList);
        render("providentFund/reportForm.html");
    }

    public void providentFundReportFormIndex2(){
        List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList(AuthUtils.getUserId(),null);
        setAttr("companyList",companyList);
        render("providentFund/reportForm2.html");
    }

    public void providentFundReportFormPageList(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.pay_ratio  ";
        String sql=" from pers_org_employee_provident_fund a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_provident_fund_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' ";


        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(deptId)){
            PersOrg org=persOrgService.findById(deptId);
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            persOrgService.findChildren(zTreeList,orgList,org.getId());
            orgList.add(org);

            String str="";
            for(PersOrg o:orgList){
                str+="?,";
                params.add(o.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.dept_id in ("+str+") ";
        }else{
            String empIdsSql="select  a.org_id from pers_user_org a where a.user_id=? ";
            List<String> empIds= Db.query(empIdsSql,AuthUtils.getUserId());
            if(empIds!=null && empIds.size()>0){
                String str="";
                for(String id:empIds){
                    str+="?,";
                    params.add(id);
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.dept_id in ("+str+") ";
            }else{
                renderJson(new DataTable<>(new Page<>()));
                return;
            }
        }

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        Map<String,String> payRatioTypeMap=new HashMap<>();
        for (PayRatioType value : PayRatioType.values()) {
            payRatioTypeMap.put(value.getKey(),value.getValue());
        }

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
            record.set("pay_ratio_name",payRatioTypeMap.get(record.getStr("pay_ratio")));
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void providentFundReportFormPageList2(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.pay_ratio  ";
        String sql=" from pers_org_employee_provident_fund a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_provident_fund_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' ";


        List<Object> params=new ArrayList<>();

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }else{
            List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList(AuthUtils.getUserId(),null);
            if(companyList.size()<=0){
                renderJson(new DataTable<Record>(new ArrayList<>()));
                return;
            }

            String str="";
            for (PersOrgCompany company : companyList) {
                str+="?,";
                params.add(company.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.company_id in("+str+") ";

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        Map<String,String> payRatioTypeMap=new HashMap<>();
        for (PayRatioType value : PayRatioType.values()) {
            payRatioTypeMap.put(value.getKey(),value.getValue());
        }

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
            record.set("pay_ratio_name",payRatioTypeMap.get(record.getStr("pay_ratio")));
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void socialSecurityReportFormIndex(){

        List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList();
        setAttr("companyList",companyList);
        render("socialSecurity/reportForm.html");
    }

    public void socialSecurityReportFormIndex2(){

        List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList(null,AuthUtils.getUserId());
        setAttr("companyList",companyList);
        render("socialSecurity/reportForm2.html");
    }

    public void socialSecurityReportFormPageList(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.is_first,a.registered_type  ";
        String sql=" from pers_org_employee_social_security a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_social_security_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0'  ";


        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(deptId)){
            PersOrg org=persOrgService.findById(deptId);
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            persOrgService.findChildren(zTreeList,orgList,org.getId());
            orgList.add(org);

            String str="";
            for(PersOrg o:orgList){
                str+="?,";
                params.add(o.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.dept_id in ("+str+") ";
        }else{
            String empIdsSql="select  a.org_id from pers_user_org a where a.user_id=? ";
            List<String> empIds= Db.query(empIdsSql,AuthUtils.getUserId());
            if(empIds!=null && empIds.size()>0){
                String str="";
                for(String id:empIds){
                    str+="?,";
                    params.add(id);
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.dept_id in ("+str+") ";
            }else{
                renderJson(new DataTable<>(new Page<>()));
                return;
            }
        }

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        /*Map<String,String> payRatioTypeMap=new HashMap<>();
        for (PayRatioType value : PayRatioType.values()) {
            payRatioTypeMap.put(value.getKey(),value.getValue());
        }*/

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
            //record.set("pay_ratio_name",payRatioTypeMap.get(record.getStr("pay_ratio")));
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void socialSecurityReportFormPageList2(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.is_first,a.phone_num  ";
        String sql=" from pers_org_employee_social_security a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_social_security_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' ";


        List<Object> params=new ArrayList<>();


        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }

        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }else{
            List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList(null,AuthUtils.getUserId());
            if(companyList.size()<=0){
                renderJson(new DataTable<Record>(new ArrayList<>()));
                return;
            }

            String str="";
            for (PersOrgCompany company : companyList) {
                str+="?,";
                params.add(company.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.company_id in("+str+") ";

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        /*Map<String,String> payRatioTypeMap=new HashMap<>();
        for (PayRatioType value : PayRatioType.values()) {
            payRatioTypeMap.put(value.getKey(),value.getValue());
        }*/

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
            //record.set("pay_ratio_name",payRatioTypeMap.get(record.getStr("pay_ratio")));
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void liabilityInsuranceReportFormIndex(){

        List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList();
        setAttr("companyList",companyList);
        render("liabilityInsurance/reportForm.html");
    }

    public void liabilityInsuranceReportFormPageList(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.age,b.sex  ";
        String sql=" from pers_org_employee_liability_insurance a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_liability_insurance_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' ";


        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(deptId)){
            PersOrg org=persOrgService.findById(deptId);
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            persOrgService.findChildren(zTreeList,orgList,org.getId());
            orgList.add(org);

            String str="";
            for(PersOrg o:orgList){
                str+="?,";
                params.add(o.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.dept_id in ("+str+") ";
        }else{
            String empIdsSql="select  a.org_id from pers_user_org a where a.user_id=? ";
            List<String> empIds= Db.query(empIdsSql,AuthUtils.getUserId());
            if(empIds!=null && empIds.size()>0){
                String str="";
                for(String id:empIds){
                    str+="?,";
                    params.add(id);
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.dept_id in ("+str+") ";
            }else{
                renderJson(new DataTable<>(new Page<>()));
                return;
            }
        }

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        /*Map<String,String> payRatioTypeMap=new HashMap<>();
        for (PayRatioType value : PayRatioType.values()) {
            payRatioTypeMap.put(value.getKey(),value.getValue());
        }*/

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
            //record.set("pay_ratio_name",payRatioTypeMap.get(record.getStr("pay_ratio")));
        }
        renderJson(new DataTable<Record>(recordList));
    }

    public void exportLiabilityInsuranceReportFormPageList(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.age,b.sex,b.id_card  ";
        String sql=" from pers_org_employee_liability_insurance a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_liability_insurance_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0'   ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(deptId)){
            PersOrg org=persOrgService.findById(deptId);
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            persOrgService.findChildren(zTreeList,orgList,org.getId());
            orgList.add(org);

            String str="";
            for(PersOrg o:orgList){
                str+="?,";
                params.add(o.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.dept_id in ("+str+") ";
        }else{
            String empIdsSql="select  a.org_id from pers_user_org a where a.user_id=? ";
            List<String> empIds= Db.query(empIdsSql,AuthUtils.getUserId());
            if(empIds!=null && empIds.size()>0){
                String str="";
                for(String id:empIds){
                    str+="?,";
                    params.add(id);
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.dept_id in ("+str+") ";
            }else{
                renderJson(new DataTable<>(new Page<>()));
                return;
            }
        }

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
        }
        try {
            String str="";
            if(types.indexOf("1")!=-1){
                str+="增";
            }
            if(types.indexOf("2")!=-1){
                str+="续";
            }
            if(types.indexOf("3")!=-1){
                str+="删";
            }
            if(types.indexOf("4")!=-1){
                str+="弃";
            }
            String fileName=yearMonth+"雇主责任险"+str+"员清单.xlsx";
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\guzhuzerenxianqingdan.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            XSSFSheet sheet = workbook.getSheetAt(0);
            workbook.setSheetName(0,"清单");

            String str2=sheet.getRow(0).getCell(0).getStringCellValue();
            sheet.getRow(0).getCell(0).setCellValue(str2.replace("{month}",yearMonth).replace("{type}",str));
            PersOrgEmployeeCheckinController.insertRow(workbook,sheet,2,recordList.size()-1);
            for (int i = 0; i < recordList.size(); i++) {
                Record record=recordList.get(i);
                XSSFRow row=sheet.getRow(i+2);
                row.getCell(0).setCellValue(i+1);
                row.getCell(1).setCellValue(record.getStr("full_name"));
                row.getCell(2).setCellValue(record.getStr("id_card"));
                if("female".equals(record.getStr("sex"))){
                    row.getCell(3).setCellValue("女");
                }else{
                    row.getCell(3).setCellValue("男");
                }
                row.getCell(4).setCellValue(record.getStr("age"));
                row.getCell(5).setCellValue(record.getStr("position_name"));
                row.getCell(6).setCellValue(record.getStr("dept_name"));
                row.getCell(7).setCellValue(record.getStr("company_name"));
                if("1".equals(record.getStr("type"))){
                    row.getCell(8).setCellValue("增");
                }else if("2".equals(record.getStr("type"))){
                    row.getCell(8).setCellValue("续");
                }else if("3".equals(record.getStr("type"))){
                    row.getCell(8).setCellValue("删");
                }else if("4".equals(record.getStr("type"))){
                    row.getCell(8).setCellValue("弃");
                }

            }



            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            render(new StreamRender(fileName, os));
            workbook.close();
            os.close();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void exportProvidentFundReportFormPageList(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        String select="  select a.*,b.full_name,c.`name` as company_name,e.`status` as task_status,d.position_name,b.sex,b.id_card,g.dict_name as marital_status_name,f.dict_name as id_card_type  ";
        String sql=" from pers_org_employee_provident_fund a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left join sys_dict f on f.dict_type='id_card_type' and f.dict_value=b.id_card_type " +
                "left join sys_dict g on g.dict_type='marital_status' and g.dict_value=a.marital_status " +
                "left JOIN pers_provident_fund_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' and (a.`status`='1' or a.`status` is null )  ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(deptId)){
            PersOrg org=persOrgService.findById(deptId);
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            persOrgService.findChildren(zTreeList,orgList,org.getId());
            orgList.add(org);

            String str="";
            for(PersOrg o:orgList){
                str+="?,";
                params.add(o.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.dept_id in ("+str+") ";
        }else{
            String empIdsSql="select  a.org_id from pers_user_org a where a.user_id=? ";
            List<String> empIds= Db.query(empIdsSql,AuthUtils.getUserId());
            if(empIds!=null && empIds.size()>0){
                String str="";
                for(String id:empIds){
                    str+="?,";
                    params.add(id);
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.dept_id in ("+str+") ";
            }else{
                renderJson(new DataTable<>(new Page<>()));
                return;
            }
        }

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
        }
        try {
            String str="";
            if(types.indexOf("1")!=-1){
                str+="增";
            }
            if(types.indexOf("2")!=-1){
                str+="续";
            }
            if(types.indexOf("3")!=-1){
                str+="删";
            }
            if(types.indexOf("4")!=-1){
                str+="弃";
            }
            String fileName=yearMonth+"公积金"+str+"员清单.xlsx";
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\gongjijinqingdan.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            XSSFSheet sheet = workbook.getSheetAt(0);
            workbook.setSheetName(0,"清单");

            Map<String,String> payRatioTypeMap=new HashMap<>();
            for (PayRatioType value : PayRatioType.values()) {
                payRatioTypeMap.put(value.getKey(),value.getValue());
            }

            String str2=sheet.getRow(0).getCell(0).getStringCellValue();
            sheet.getRow(0).getCell(0).setCellValue(str2.replace("{month}",yearMonth).replace("{type}",str));
            PersOrgEmployeeCheckinController.insertRow(workbook,sheet,2,recordList.size()-1);
            for (int i = 0; i < recordList.size(); i++) {
                Record record=recordList.get(i);
                XSSFRow row=sheet.getRow(i+2);
                row.getCell(0).setCellValue(i+1);
                row.getCell(1).setCellValue(record.getStr("full_name"));


                if("female".equals(record.getStr("sex"))){
                    row.getCell(2).setCellValue("女");
                }else{
                    row.getCell(2).setCellValue("男");
                }
                row.getCell(3).setCellValue(record.getStr("dept_name"));
                row.getCell(4).setCellValue(record.getStr("position_name"));
                row.getCell(5).setCellValue(record.getStr("id_card_type"));
                row.getCell(6).setCellValue(record.getStr("idcard"));
                row.getCell(7).setCellValue(record.getStr("marital_status_name"));


                row.getCell(8).setCellValue(payRatioTypeMap.get(record.getStr("pay_ratio")));
                row.getCell(9).setCellValue(record.getStr("phone_num"));
                if("1".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("增");
                }else if("2".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("续");
                }else if("3".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("删");
                }else if("4".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("弃");
                }
                row.getCell(11).setCellValue(record.getStr("company_name"));
            }



            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            render(new StreamRender(fileName, os));
            workbook.close();
            os.close();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void exportProvidentFundReportFormPageList2(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.id,b.full_name,a.dept_id,a.type,a.`status`,a.exception_remark,c.`name` as company_name,e.`status` as task_status,d.position_name,a.pay_ratio  ";
        String sql=" from pers_org_employee_provident_fund a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_provident_fund_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' and (a.`status`='1' or a.`status` is null ) ";


        List<Object> params=new ArrayList<>();

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }else{
            List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList(AuthUtils.getUserId(),null);
            if(companyList.size()<=0){
                renderJson(new DataTable<Record>(new ArrayList<>()));
                return;
            }

            String str="";
            for (PersOrgCompany company : companyList) {
                str+="?,";
                params.add(company.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.company_id in("+str+") ";

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
        }
        try {
            String str="";
            if(types.indexOf("1")!=-1){
                str+="增";
            }
            if(types.indexOf("2")!=-1){
                str+="续";
            }
            if(types.indexOf("3")!=-1){
                str+="删";
            }
            if(types.indexOf("4")!=-1){
                str+="弃";
            }
            String fileName=yearMonth+"公积金"+str+"员清单.xlsx";
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\gongjijinqingdan.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            XSSFSheet sheet = workbook.getSheetAt(0);
            workbook.setSheetName(0,"清单");

            Map<String,String> payRatioTypeMap=new HashMap<>();
            for (PayRatioType value : PayRatioType.values()) {
                payRatioTypeMap.put(value.getKey(),value.getValue());
            }

            String str2=sheet.getRow(0).getCell(0).getStringCellValue();
            sheet.getRow(0).getCell(0).setCellValue(str2.replace("{month}",yearMonth).replace("{type}",str));
            PersOrgEmployeeCheckinController.insertRow(workbook,sheet,2,recordList.size()-1);
            for (int i = 0; i < recordList.size(); i++) {
                Record record=recordList.get(i);
                XSSFRow row=sheet.getRow(i+2);
                row.getCell(0).setCellValue(i+1);
                row.getCell(1).setCellValue(record.getStr("full_name"));


                if("female".equals(record.getStr("sex"))){
                    row.getCell(2).setCellValue("女");
                }else{
                    row.getCell(2).setCellValue("男");
                }
                row.getCell(3).setCellValue(record.getStr("dept_name"));
                row.getCell(4).setCellValue(record.getStr("position_name"));
                row.getCell(5).setCellValue(record.getStr("id_card_type"));
                row.getCell(6).setCellValue(record.getStr("idcard"));
                row.getCell(7).setCellValue(record.getStr("marital_status_name"));


                row.getCell(8).setCellValue(payRatioTypeMap.get(record.getStr("pay_ratio")));
                row.getCell(9).setCellValue(record.getStr("phone_num"));
                if("1".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("增");
                }else if("2".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("续");
                }else if("3".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("删");
                }else if("4".equals(record.getStr("type"))){
                    row.getCell(10).setCellValue("弃");
                }
                row.getCell(11).setCellValue(record.getStr("company_name"));
            }



            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            render(new StreamRender(fileName, os));
            workbook.close();
            os.close();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void exportSocialSecurityReportFormPageList(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        String select="  select a.*,b.full_name,c.`name` as company_name,e.`status` as task_status,d.position_name,b.sex,b.id_card,b.entry_time,b.formal_date  ";
        String sql=" from pers_org_employee_social_security a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_social_security_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' and (a.`status`='1' or a.`status` is null ) ";

        List<Object> params=new ArrayList<>();
        if(StrKit.notBlank(deptId)){
            PersOrg org=persOrgService.findById(deptId);
            List<PersOrg> orgList=new ArrayList<>();
            List<ZTree> zTreeList=persOrgService.allOrgTree();
            persOrgService.findChildren(zTreeList,orgList,org.getId());
            orgList.add(org);

            String str="";
            for(PersOrg o:orgList){
                str+="?,";
                params.add(o.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.dept_id in ("+str+") ";
        }else{
            String empIdsSql="select  a.org_id from pers_user_org a where a.user_id=? ";
            List<String> empIds= Db.query(empIdsSql,AuthUtils.getUserId());
            if(empIds!=null && empIds.size()>0){
                String str="";
                for(String id:empIds){
                    str+="?,";
                    params.add(id);
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.dept_id in ("+str+") ";
            }else{
                renderJson(new DataTable<>(new Page<>()));
                return;
            }
        }

        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }
        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
        }
        try {
            String str="";
            if(types.indexOf("1")!=-1){
                str+="增";
            }
            if(types.indexOf("2")!=-1){
                str+="续";
            }
            if(types.indexOf("3")!=-1){
                str+="删";
            }
            if(types.indexOf("4")!=-1){
                str+="弃";
            }
            String fileName=yearMonth+"社保"+str+"员清单.xlsx";
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\shebaoqingdan.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            XSSFSheet sheet = workbook.getSheetAt(0);
            workbook.setSheetName(0,"清单");

            Map<String,String> payRatioTypeMap=new HashMap<>();
            for (PayRatioType value : PayRatioType.values()) {
                payRatioTypeMap.put(value.getKey(),value.getValue());
            }

            String str2=sheet.getRow(0).getCell(0).getStringCellValue();
            sheet.getRow(0).getCell(0).setCellValue(str2.replace("{month}",yearMonth).replace("{type}",str));
            PersOrgEmployeeCheckinController.insertRow(workbook,sheet,3,recordList.size()-1);
            Map<String, String> dictMap = dictService.getMapByTypeOnUse("registered_type");
            for (int i = 0; i < recordList.size(); i++) {
                Record record=recordList.get(i);
                XSSFRow row=sheet.getRow(i+3);
                row.getCell(0).setCellValue(i+1);
                row.getCell(1).setCellValue(record.getStr("full_name"));


                if("female".equals(record.getStr("sex"))){
                    row.getCell(2).setCellValue("女");
                }else{
                    row.getCell(2).setCellValue("男");
                }
                row.getCell(3).setCellValue(record.getStr("dept_name"));
                row.getCell(4).setCellValue(record.getStr("position_name"));



                row.getCell(5).setCellValue(DateUtils.formatDate(record.getDate("entry_time"),"yyyy-MM-dd"));
                if(record.getDate("formal_date")!=null){
                    if(DateUtils.compareDays(record.getDate("formal_date"),new Date())>1){
                        row.getCell(6).setCellValue("否");
                    }else{
                        row.getCell(6).setCellValue("是");
                    }
                }
                row.getCell(7).setCellValue(record.getStr("idcard"));
                row.getCell(8).setCellValue(record.getStr("phone_num"));
                row.getCell(9).setCellValue(record.getStr("idcard_addr"));

                String registeredTypeName = dictMap.get(record.getStr("registered_type"));

                if(StrKit.notBlank(registeredTypeName)){
                    row.getCell(10).setCellValue(registeredTypeName);
                }else{
                    row.getCell(10).setCellValue("");
                }
                if("1".equals(record.getStr("is_first"))){
                    row.getCell(11).setCellValue("是");
                }else if("0".equals(record.getStr("is_first"))){
                    row.getCell(11).setCellValue("否");
                }


                if("1".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("增");
                }else if("2".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("续");
                }else if("3".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("删");
                }else if("4".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("弃");
                }
                row.getCell(13).setCellValue(record.getStr("company_name"));
            }



            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            render(new StreamRender(fileName, os));
            workbook.close();
            os.close();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void exportSocialSecurityReportFormPageList2(){
        String deptId=getPara("deptId");
        String companyId=getPara("companyId");
        String yearMonth=getPara("date");
        String types=getPara("types");

        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }


        String select="  select a.*,b.full_name,c.`name` as company_name,e.`status` as task_status,d.position_name,b.sex,b.id_card,b.entry_time,b.formal_date  ";
        String sql=" from pers_org_employee_social_security a left join pers_org_employee b on a.emp_id=b.id " +
                " left join pers_org_company c on a.company_id=c.id left join pers_position d on d.id=a.position_id " +
                "left JOIN pers_social_security_apply_record e on e.id=a.apply_record_id and e.`status` in ('2','3') and e.del_flag='0' " +
                "where a.del_flag='0' and (a.`status`='1' or a.`status` is null ) ";


        List<Object> params=new ArrayList<>();


        if(StrKit.notBlank(yearMonth)){
            sql+=" and a.`year_month`=? ";
            params.add(yearMonth);
        }

        if(StrKit.notBlank(types)){
            JSONArray jsonArray=JSON.parseArray(types);
            if(jsonArray!=null && jsonArray.size()>0){
                String str="";
                for (int i = 0; i < jsonArray.size(); i++) {
                    str+="?,";
                    params.add(jsonArray.getString(i));
                }
                str=str.substring(0,str.length()-1);
                sql+=" and a.`type` in ("+str+") ";
            }
        }

        if(StrKit.notBlank(companyId)){
            sql+=" and a.company_id=? ";
            params.add(companyId);

        }else{
            List<PersOrgCompany> companyList=persOrgCompanyService.orgCompanyList(null,AuthUtils.getUserId());
            if(companyList.size()<=0){
                renderJson(new DataTable<Record>(new ArrayList<>()));
                return;
            }

            String str="";
            for (PersOrgCompany company : companyList) {
                str+="?,";
                params.add(company.getId());
            }
            str=str.substring(0,str.length()-1);
            sql+=" and a.company_id in("+str+") ";

        }

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        List<String> sortOrgIds=new ArrayList<>();
        PersOrgEmployeeCheckinDaySummaryServiceImpl.recursionZtree(zTreeList,sortOrgIds);


        sql+=" order by FIELD(a.`dept_id`,";
        for(String id:sortOrgIds){
            sql+="'"+id+"',";
        }
        sql=sql.substring(0,sql.length()-1);
        sql+="),b.work_num";

        List<Record> recordList=Db.find(select+sql,params.toArray());

        for (Record record : recordList) {
            record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
        }
        try {
            String str="";
            if(types.indexOf("1")!=-1){
                str+="增";
            }
            if(types.indexOf("2")!=-1){
                str+="续";
            }
            if(types.indexOf("3")!=-1){
                str+="删";
            }
            if(types.indexOf("4")!=-1){
                str+="弃";
            }
            String fileName=yearMonth+"社保"+str+"员清单.xlsx";
            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\shebaoqingdan.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            XSSFSheet sheet = workbook.getSheetAt(0);
            workbook.setSheetName(0,"清单");

            Map<String,String> payRatioTypeMap=new HashMap<>();
            for (PayRatioType value : PayRatioType.values()) {
                payRatioTypeMap.put(value.getKey(),value.getValue());
            }

            String str2=sheet.getRow(0).getCell(0).getStringCellValue();
            sheet.getRow(0).getCell(0).setCellValue(str2.replace("{month}",yearMonth).replace("{type}",str));
            PersOrgEmployeeCheckinController.insertRow(workbook,sheet,3,recordList.size()-1);

            Map<String, String> dictMap = dictService.getMapByTypeOnUse("registered_type");
            for (int i = 0; i < recordList.size(); i++) {
                Record record=recordList.get(i);
                XSSFRow row=sheet.getRow(i+3);
                row.getCell(0).setCellValue(i+1);
                row.getCell(1).setCellValue(record.getStr("full_name"));


                if("female".equals(record.getStr("sex"))){
                    row.getCell(2).setCellValue("女");
                }else{
                    row.getCell(2).setCellValue("男");
                }
                row.getCell(3).setCellValue(record.getStr("dept_name"));
                row.getCell(4).setCellValue(record.getStr("position_name"));



                row.getCell(5).setCellValue(DateUtils.formatDate(record.getDate("entry_time"),"yyyy-MM-dd"));
                if(record.getDate("formal_date")!=null){
                    if(DateUtils.compareDays(record.getDate("formal_date"),new Date())>1){
                        row.getCell(6).setCellValue("否");
                    }else{
                        row.getCell(6).setCellValue("是");
                    }
                }
                row.getCell(7).setCellValue(record.getStr("idcard"));
                row.getCell(8).setCellValue(record.getStr("phone_num"));
                row.getCell(9).setCellValue(record.getStr("idcard_addr"));
                String registeredTypeName = dictMap.get(record.getStr("registered_type"));

                if(StrKit.notBlank(registeredTypeName)){
                    row.getCell(10).setCellValue(registeredTypeName);
                }else{
                    row.getCell(10).setCellValue("");
                }
                if("1".equals(record.getStr("is_first"))){
                    row.getCell(11).setCellValue("是");
                }else if("0".equals(record.getStr("is_first"))){
                    row.getCell(11).setCellValue("否");
                }


                if("1".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("增");
                }else if("2".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("续");
                }else if("3".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("删");
                }else if("4".equals(record.getStr("type"))){
                    row.getCell(12).setCellValue("弃");
                }
                row.getCell(13).setCellValue(record.getStr("company_name"));
            }



            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            render(new StreamRender(fileName, os));
            workbook.close();
            os.close();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void liabilityInsuranceSummaryIndex(){

        render("liabilityInsurance/summaryIndex.html");
    }

    public void liabilityInsuranceSummaryList(){
        String yearMonth=getPara("yearMonth");
        String orgId=null;
        List<ZTree> zTreeAllList=persOrgService.orgTreeTable(orgId);
        List<ZTree> zTreeList=new ArrayList<>();
        for (ZTree zTree : zTreeAllList) {
            if("1".equals(zTree.getIsEnabled())){
                zTreeList.add(zTree);
            }
        }
        if(StrKit.isBlank(yearMonth)){
            yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
        }

        List<ZTree> returnZtree=new ArrayList<>();

        Set<String> idSet=new TreeSet<>();
        for(ZTree zTree:zTreeList){
            for(ZTree z:zTreeList){
                if(z.getpId().equalsIgnoreCase(zTree.getId())){
                    idSet.add(z.getId());
                    if(zTree.getChildren()==null){
                        List<ZTree> childrenZtreeList=new ArrayList<>();
                        childrenZtreeList.add(z);
                        zTree.setChildren(childrenZtreeList);
                    }else{
                        zTree.getChildren().add(z);
                    }
                }
            }
        }
        for(ZTree zTree:zTreeList){
            if(!idSet.contains(zTree.getId())){
                returnZtree.add(zTree);
            }
        }

        /*for (ZTree zTree : zTreeList) {
            List<ZTree> allChildrenList=new ArrayList<>();
        }*/
        List<Record> recordList=Db.find("select a.dept_id,a.type,COUNT(a.id) as count from pers_org_employee_liability_insurance a inner join pers_liability_insurance_apply_record b  on b.id=a.apply_record_id and b.`status`='3' and b.del_flag='0'" +
                "where a.del_flag='0' and a.`year_month`=?  GROUP BY a.dept_id,a.type ",yearMonth);
        Map<String,Integer> numMap=new HashMap<>();
        for (Record record : recordList) {
            numMap.put(record.getStr("dept_id")+"_"+record.getStr("type"),record.getInt("count"));
        }
        getAllChildrenList(returnZtree,numMap);
        getAllChildrenList2(returnZtree);
        List<ZTree> newZtreeList=new ArrayList<>();
        getAllChildrenList4(returnZtree,newZtreeList);

        /*HashMap<String,Object> map=new HashMap<>();
        map.put("data",newZtreeList);
        map.put("code",0);
        map.put("count",newZtreeList.size());
        map.put("data",newZtreeList);*/

        //renderJson(map);
        renderJson(new DataTable<ZTree>(newZtreeList));
    }

    public void exportLiabilityInsuranceSummaryList(){
        String ids=getPara("ids");
        String yearMonth=getPara("yearMonth");
        JSONArray jsonArray=JSON.parseArray(ids);
        String lastYearMonth=DateUtils.formatDate(DateUtils.getNextDay(DateUtils.parseDate(yearMonth+"-01"),-1),"yyyy-MM");
        try {
            String fileName=yearMonth+"雇主责任险汇总.xlsx";

            List<ZTree> zTreeAllList=persOrgService.orgTreeTable(null);
            List<ZTree> zTreeList=new ArrayList<>();
            for (ZTree zTree : zTreeAllList) {
                if("1".equals(zTree.getIsEnabled())){
                    zTreeList.add(zTree);
                }
            }
            if(StrKit.isBlank(yearMonth)){
                yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
            }

            List<ZTree> returnZtree=new ArrayList<>();

            Set<String> idSet=new TreeSet<>();
            for(ZTree zTree:zTreeList){
                for(ZTree z:zTreeList){
                    if(z.getpId().equalsIgnoreCase(zTree.getId())){
                        idSet.add(z.getId());
                        if(zTree.getChildren()==null){
                            List<ZTree> childrenZtreeList=new ArrayList<>();
                            childrenZtreeList.add(z);
                            zTree.setChildren(childrenZtreeList);
                        }else{
                            zTree.getChildren().add(z);
                        }
                    }
                }
            }
            for(ZTree zTree:zTreeList){
                if(!idSet.contains(zTree.getId())){
                    returnZtree.add(zTree);
                }
            }

            List<Record> recordList=Db.find("select a.dept_id,a.type,COUNT(a.id) as count from pers_org_employee_liability_insurance a inner join pers_liability_insurance_apply_record b  on b.id=a.apply_record_id and b.`status`='3' and b.del_flag='0'" +
                    "where a.del_flag='0' and a.`year_month`=?  GROUP BY a.dept_id,a.type ",yearMonth);
            Map<String,Integer> numMap=new HashMap<>();
            for (Record record : recordList) {
                numMap.put(record.getStr("dept_id")+"_"+record.getStr("type"),record.getInt("count"));
            }
            getAllChildrenList(returnZtree,numMap);
            getAllChildrenList2(returnZtree);
            List<ZTree> newZtreeList=new ArrayList<>();
            getAllChildrenList4(returnZtree,newZtreeList);

            List<ZTree> exportZTreeList=new ArrayList<>();
            for (ZTree zTree : newZtreeList) {
                if(jsonArray.contains(zTree.getId())){
                    exportZTreeList.add(zTree);
                }
            }

            InputStream inputStream=new FileInputStream(PathKit.getWebRootPath()+"\\upload\\guzhuzerenxianhuizo.xlsx");
            XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
            inputStream.close();
            XSSFSheet sheet = workbook.getSheetAt(0);
            workbook.setSheetName(0,"汇总");

            String str=sheet.getRow(0).getCell(0).getStringCellValue();
            sheet.getRow(0).getCell(0).setCellValue(str.replace("{month}",yearMonth));
            PersOrgEmployeeCheckinController.insertRow(workbook,sheet,1,exportZTreeList.size()-1);


            List<ZTree> zTreeAllList2=persOrgService.orgTreeTable(null);
            List<ZTree> zTreeList2=new ArrayList<>();
            for (ZTree zTree : zTreeAllList2) {
                if("1".equals(zTree.getIsEnabled())){
                    zTreeList2.add(zTree);
                }
            }
            if(StrKit.isBlank(yearMonth)){
                yearMonth=DateUtils.formatDate(new Date(),"yyyy-MM");
            }

            List<ZTree> returnZtree2=new ArrayList<>();
            Set<String> idSet2=new TreeSet<>();
            for(ZTree zTree:zTreeList2){
                for(ZTree z:zTreeList2){
                    if(z.getpId().equalsIgnoreCase(zTree.getId())){
                        idSet2.add(z.getId());
                        if(zTree.getChildren()==null){
                            List<ZTree> childrenZtreeList=new ArrayList<>();
                            childrenZtreeList.add(z);
                            zTree.setChildren(childrenZtreeList);
                        }else{
                            zTree.getChildren().add(z);
                        }
                    }
                }
            }
            for(ZTree zTree:zTreeList2){
                if(!idSet2.contains(zTree.getId())){
                    returnZtree2.add(zTree);
                }
            }

            List<Record> recordList2=Db.find("select a.dept_id,a.type,COUNT(a.id) as count from pers_org_employee_liability_insurance a inner join pers_liability_insurance_apply_record b  on b.id=a.apply_record_id and b.`status`='3' and b.del_flag='0'" +
                    "where a.del_flag='0' and a.`year_month`=?  GROUP BY a.dept_id,a.type ",lastYearMonth);
            Map<String,Integer> numMap2=new HashMap<>();
            for (Record record : recordList2) {
                numMap2.put(record.getStr("dept_id")+"_"+record.getStr("type"),record.getInt("count"));
            }
            getAllChildrenList(returnZtree2,numMap2);
            getAllChildrenList2(returnZtree2);
            List<ZTree> newZtreeList2=new ArrayList<>();
            getAllChildrenList4(returnZtree2,newZtreeList2);

            Map<String,Record> lastMap=new HashMap<>();
            for (ZTree zTree : newZtreeList2) {
                lastMap.put(zTree.getId(),zTree.getRecord());
            }

            int totalLastNum=0;
            int totalAddNum=0;
            int totalDelNum=0;
            int totalCurrNum=0;
            for (int i = 0; i < exportZTreeList.size(); i++) {
                ZTree zTree=exportZTreeList.get(i);
                XSSFRow row=sheet.getRow(i+2);
                row.getCell(0).setCellValue(i+1);
                row.getCell(1).setCellValue(zTree.getName());
                Record lastRecord=lastMap.get(zTree.getId());

                row.getCell(2).setCellValue(lastRecord.getInt("add")+lastRecord.getInt("cont"));
                totalLastNum+=(lastRecord.getInt("add")+lastRecord.getInt("cont"));
                Record record=zTree.getRecord();
                row.getCell(3).setCellValue(record.getInt("add"));
                totalAddNum+=record.getInt("add");
                row.getCell(4).setCellValue(record.getInt("del"));
                totalDelNum+=record.getInt("del");
                row.getCell(5).setCellValue(record.getInt("add")+record.getInt("cont"));
                totalCurrNum+=(record.getInt("add")+record.getInt("cont"));
            }
            //合计行
            XSSFRow row=sheet.getRow(exportZTreeList.size()+2);
            row.getCell(2).setCellValue(totalLastNum);
            row.getCell(3).setCellValue(totalAddNum);
            row.getCell(4).setCellValue(totalDelNum);
            row.getCell(5).setCellValue(totalCurrNum);


            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            render(new StreamRender(fileName, os));
            workbook.close();
            os.close();
        }catch (Exception e){
            e.printStackTrace();
        }



    }

    public void getAllChildrenList(List<ZTree> returnZtree,Map<String,Integer> numMap){

        for (ZTree zTree : returnZtree) {
            Integer add=numMap.get(zTree.getId()+"_1");
            Integer cont=numMap.get(zTree.getId()+"_2");
            Integer del=numMap.get(zTree.getId()+"_3");
            Integer abandon=numMap.get(zTree.getId()+"_4");
            if(add==null){
                add=0;
            }
            if(cont==null){
                cont=0;
            }
            if(del==null){
                del=0;
            }
            if(abandon==null){
                abandon=0;
            }
            Record record=new Record();
            record.set("add",add);
            record.set("cont",cont);
            record.set("del",del);
            record.set("abandon",abandon);

            zTree.setRecord(record);
            if(zTree.getChildren()!=null && zTree.getChildren().size()>0){
                getAllChildrenList(zTree.getChildren(),numMap);
            }

        }
    }

    public void getAllChildrenList2(List<ZTree> returnZtree){
        for (ZTree zTree : returnZtree) {
            Record record=zTree.getRecord();
            record.set("name",zTree.getName());
            if(zTree.getChildren()!=null && zTree.getChildren().size()>0 ){
                getAllChildrenList3(zTree.getChildren(),record);
                getAllChildrenList2(zTree.getChildren());
            }
        }
    }

    public void getAllChildrenList3(List<ZTree> returnZtree,Record record){

        for (ZTree zTree : returnZtree) {
            Integer add=zTree.getRecord().get("add");
            Integer cont=zTree.getRecord().get("cont");
            Integer del=zTree.getRecord().get("del");
            Integer abandon=zTree.getRecord().get("abandon");
            zTree.setChecked(true);
            if(add==null){
                add=0;
            }
            if(cont==null){
                cont=0;
            }
            if(del==null){
                del=0;
            }
            if(abandon==null){
                abandon=0;
            }
            record.set("add",add+record.getInt("add"));
            record.set("cont",cont+record.getInt("cont"));
            record.set("del",del+record.getInt("del"));
            record.set("abandon",abandon+record.getInt("abandon"));

            if(zTree.getChildren()!=null && zTree.getChildren().size()>0){
                getAllChildrenList3(zTree.getChildren(),record);
            }

        }
    }

    public void getAllChildrenList4(List<ZTree> returnZtree,List<ZTree> newZtreeList){

        for (ZTree zTree : returnZtree) {
            ZTree zTreeNode = new ZTree(zTree.getId(), zTree.getName(), zTree.getType(), zTree.getpId(), zTree.getpIds());
            zTreeNode.setRecord(zTree.getRecord());
            zTreeNode.setLay_is_open(false);
            zTreeNode.setOpen(false);
            newZtreeList.add(zTreeNode);
            if(zTree.getChildren()!=null && zTree.getChildren().size()>0){
                getAllChildrenList4(zTree.getChildren(),newZtreeList);
            }

        }
    }

    public void genProvidentFund(){
        String yearMonth=getPara("yearMonth");

        Date date=DateUtils.getNextDay(DateUtils.parseDate(yearMonth+"-01"),-1);
        List<PersOrgEmployeeProvidentFund> providentFundList =persOrgEmployeeProvidentFundService.findEmployeeProvidentFundList(DateUtils.formatDate(date,"yyyy-MM"),null);

        List<PersOrgEmployeeProvidentFund> addProvidentFundList=new ArrayList<>();

        for (PersOrgEmployeeProvidentFund providentFund : providentFundList) {
            PersOrgEmployee employee=persOrgEmployeeService.findById(providentFund.getEmpId());

            PersOrgEmployeeProvidentFund newProvidentFund=new PersOrgEmployeeProvidentFund();
            newProvidentFund.setId(IdGen.getUUID());
            newProvidentFund.setEmpId(providentFund.getEmpId());
            newProvidentFund.setCreateDate(new Date());
            newProvidentFund.setUpdateDate(new Date());
            newProvidentFund.setYearMonth(yearMonth);
            newProvidentFund.setDelFlag("0");

            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                PersOrg org =persOrgService.findById(id);
                if(org!=null && "0".equals(org.getDelFlag()) && "1".equals(org.getIsEnable())){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }
            newProvidentFund.setDeptId(empDeptId);
            newProvidentFund.setPositionId(empPositionId);
            newProvidentFund.setIdcard(employee.getIdCard());
            newProvidentFund.setCompanyId(providentFund.getCompanyId());
            newProvidentFund.setMaritalStatus(employee.getMaritalStatus());
            newProvidentFund.setPhoneNum(employee.getPhoneNum());
            newProvidentFund.setPayRatio(providentFund.getPayRatio());


            if("1".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("3");
                }

            }else if("2".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("3");
                }
            }else if("3".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("3");
                }
            }else if("4".equals(providentFund.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newProvidentFund.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){

                }
            }
            if(StrKit.notBlank(newProvidentFund.getType())){
                List<String> empIds=new ArrayList<>();
                empIds.add(newProvidentFund.getEmpId());
                List<PersOrgEmployeeProvidentFund> providentFunds=persOrgEmployeeProvidentFundService.findEmployeeProvidentFundList(yearMonth,empIds);
                if(providentFunds.size()<=0){
                    addProvidentFundList.add(newProvidentFund);
                }
            }
        }

        if(addProvidentFundList.size()>0){
            Db.batchSave(addProvidentFundList,addProvidentFundList.size());
        }
        renderJson(addProvidentFundList.size());
    }
}
