/**
 * 
 */
package com.cszn.personnel.web.controller.sys;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.Menu;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * Created by LiangHuiLing on 2019年2月12日
 *
 * MenuController
 */
@RequestMapping(value="/menu", viewPath="/modules_page/sys/menu")
public class MenuController extends BaseController {
	
	@Inject
    private MenuService menuService;
	
    public void index() {
        render("menuIndex.html");
    }

    /**
     * 树表格方法
     */
    @Clear(LogInterceptor.class)
    public void menuTreeGrid() {
        renderJson(new DataTable<ZTree>(menuService.menuTreeTable(SystemType.PERSONNEL)));
    }
    
    /**
     * 添加页面方法
     */
    public void add() {
    	setAttr("parentMenu", new Menu());
    	setAttr("model", new Menu());
        render("menuForm.html");
    }
    
    /**
     * 修改页面方法
     */
    public void edit() {
    	Menu model = menuService.findById(getPara("id"));
    	if(model!=null && StrKit.notBlank(model.getParentId())){
    		setAttr("parentMenu", menuService.findById(model.getParentId()));
    	}else{
    		setAttr("parentMenu", new Menu());
    	}
    	setAttr("model", model);
    	render("menuForm.html");
    }
    
    /**
     * 上级菜单树数据
     */
    @Clear(LogInterceptor.class)
    public void menuFormTree() {
        List<ZTree> treeNodeList = menuService.menuFormTree(SystemType.PERSONNEL);
        renderJson(treeNodeList);
    }
    
    /**
     * 批量更新菜单排序
     */
    public void sortBatchSave(){
    	final String sortDatas = getPara("sortDatas");
    	if(StrKit.notBlank(sortDatas)){
    		List<Menu> menuList = JSONArray.parseArray(sortDatas, Menu.class);
    		if (menuService.sortBatchSave(menuList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }
    
	/**
	 * 保存方法
	 */
    public void save() {
        Menu sysMenu = getBean(Menu.class, "", true);
        sysMenu.setMenuSystem(SystemType.PERSONNEL);
        if (menuService.menuSave(sysMenu)) {
        	renderJson(Ret.ok("msg", "操作成功!"));
        } else {
        	renderJson(Ret.fail("msg", "操作失败！"));
        }
    }

    /**
     * 删除当前数据及其子数据
     */
    public void del() {
    	if(menuService.menuDel(getPara("id"))){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }
}
