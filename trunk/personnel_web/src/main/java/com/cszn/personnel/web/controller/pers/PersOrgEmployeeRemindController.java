package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeRemindService;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeRemind;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.yaml.snakeyaml.constructor.BaseConstructor;

@RequestMapping(value="/employeeRemind", viewPath="/modules_page/pers/remind")
public class PersOrgEmployeeRemindController  extends BaseController {

    @Inject
    PersOrgEmployeeRemindService persOrgEmployeeRemindService;

    public void index(){

        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            PersOrgEmployeeRemind employeeRemind=persOrgEmployeeRemindService.findById(id);

            setAttr("remind",employeeRemind);
        }

        render("form.html");
    }

    public void pageList(){
        PersOrgEmployeeRemind remind=getBean(PersOrgEmployeeRemind.class,"",true);

        Page<Record> page=persOrgEmployeeRemindService.pageList(getParaToInt("page"),getParaToInt("limit"),remind);

        renderJson(new DataTable<Record>(page));
    }

    public void saveRemind(){
        PersOrgEmployeeRemind remind=getBean(PersOrgEmployeeRemind.class,"",true);

        boolean flag=persOrgEmployeeRemindService.saveRemind(remind, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

}
