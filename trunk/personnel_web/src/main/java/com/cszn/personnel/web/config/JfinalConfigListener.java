package com.cszn.personnel.web.config;

import com.cszn.integrated.base.common.AppInfo;
import com.cszn.integrated.base.web.handler.CustomActionHandler;
import com.cszn.integrated.base.web.render.AppRenderFactory;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.personnel.web.support.crossorigin.CrossInterceptor;
import com.jfinal.config.Constants;
import com.jfinal.config.Interceptors;
import com.jfinal.config.Routes;
import com.jfinal.ext.handler.ContextPathHandler;
import com.jfinal.json.FastJsonFactory;
import com.jfinal.log.Log4jLogFactory;
import com.jfinal.template.Engine;
import io.jboot.Jboot;
import io.jboot.aop.jfinal.JfinalHandlers;
import io.jboot.aop.jfinal.JfinalPlugins;
import io.jboot.core.listener.JbootAppListenerBase;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class JfinalConfigListener extends JbootAppListenerBase {
	
    @Override
    public void onConstantConfig(Constants constants) {
        constants.setError401View("/template/401.html");
        constants.setError403View("/template/403.html");
        constants.setError404View("/template/404.html");
        constants.setError500View("/template/500.html");
        constants.setJsonFactory(new FastJsonFactory());
        constants.setRenderFactory(new AppRenderFactory());
        constants.setLogFactory(new Log4jLogFactory());
    }

    @Override
    public void onRouteConfig(Routes routes) {
        routes.setBaseViewPath("/template");
    }

    @Override
    public void onEngineConfig(Engine engine) {
        engine.setDevMode(true);
        AppInfo app = Jboot.config(AppInfo.class);
        engine.addSharedObject("APP", app);
        engine.addSharedObject("RESOURCE_HOST", app.getResourceHost());
    }

    @Override
    public void onInterceptorConfig(Interceptors interceptors) {
    	interceptors.add(new CrossInterceptor());
//        interceptors.add(new LogInterceptor());
//        interceptors.add(new AuthInterceptor());
//        interceptors.add(new NotNullParaInterceptor("/template/exception.html"));
//        interceptors.add(new BusinessExceptionInterceptor("/template/exception.html"));
    }

    @Override
    public void onPluginConfig(JfinalPlugins plugins) {

    }

    @Override
    public void onHandlerConfig(JfinalHandlers handlers) {
        handlers.setActionHandler(new CustomActionHandler());
        handlers.add(new ContextPathHandler("ctxPath"));
    }

    @Override
    public void onStartBefore() {

    }

    @Override
    public void onStop() {
    }

    @Override
    public void onStart() {
        /** 集群模式下验证码使用 redis 缓存 */
//        CaptchaManager.me().setCaptchaCache(new CaptchaCache());
    	Global.mpAppid = Jboot.configValue("mpAppId");
    	Global.mpAppSecret = Jboot.configValue("mpAppSecret");
    	Global.sojournUrl = Jboot.configValue("sojournUrl");
    	Global.fileUrlPrefix = Jboot.configValue("fileUrlPrefix");
    	Global.uploadPath = Jboot.configValue("uploadPath");
    	Global.excelTemplatePath = Jboot.configValue("excelTemplatePath");
    	Global.uploadPathLinux = Jboot.configValue("uploadPath.linux");
        Global.commonUpload = Jboot.configValue("commonUpload");
        Global.getMsgUrl = Jboot.configValue("getMsgUrl");
        Global.getMsgCountUrl = Jboot.configValue("getMsgCountUrl");
        Global.signReadUrl = Jboot.configValue("signReadUrl");
        Global.defaultPassword=Jboot.configValue("defaultPassword");
        Global.wmsAppNo=Jboot.configValue("wmsAppNo");
        Global.sojournAppNo=Jboot.configValue("sojournAppNo");
        Global.orgAppNo=Jboot.configValue("orgAppNo");
        Global.sendMessageUrl=Jboot.configValue("sendMessageUrl");
        Global.employeeBirthdaySmsContent=Jboot.configValue("employeeBirthdaySmsContent");
        Global.fileUploadUrl=Jboot.configValue("fileUploadUrl");
        Global.taskCreateUrl=Jboot.configValue("taskCreateUrl");
        Global.taskDaoUrl=Jboot.configValue("taskDaoUrl");
        Global.getMyTasksUrl=Jboot.configValue("getMyTasksUrl");
        Global.getTaskDetail=Jboot.configValue("getTaskDetail");
        Global.getTaskDetailByUser=Jboot.configValue("getTaskDetailByUser");
        Global.getTaskDetailList=Jboot.configValue("getTaskDetailList");
        Global.entryInfoStep1=Jboot.configValue("entryInfoStep1");
        Global.entryInfoStep2=Jboot.configValue("entryInfoStep2");
        Global.entryInfoStep3=Jboot.configValue("entryInfoStep3");
        Global.changeDeptStep1=Jboot.configValue("changeDeptStep1");
        Global.changeDeptStep2=Jboot.configValue("changeDeptStep2");
        Global.changeDeptStep3=Jboot.configValue("changeDeptStep3");
        Global.changeDeptStep4=Jboot.configValue("changeDeptStep4");
        Global.changeDeptStep5=Jboot.configValue("changeDeptStep5");
        Global.qualifiedStep1=Jboot.configValue("qualifiedStep1");
        Global.qualifiedStep2=Jboot.configValue("qualifiedStep2");
        Global.qualifiedStep3=Jboot.configValue("qualifiedStep3");
        Global.qualifiedStep4=Jboot.configValue("qualifiedStep4");
        Global.qualifiedStep5=Jboot.configValue("qualifiedStep5");
        Global.quitStep1=Jboot.configValue("quitStep1");
        Global.quitStep2=Jboot.configValue("quitStep2");
        Global.quitStep3=Jboot.configValue("quitStep3");
        Global.quitStep4=Jboot.configValue("quitStep4");
        Global.fillCardStep1=Jboot.configValue("fillCardStep1");
        Global.fillCardStep2=Jboot.configValue("fillCardStep2");
        Global.overTimeStep1=Jboot.configValue("overTimeStep1");
        Global.overTimeStep2=Jboot.configValue("overTimeStep2");
        Global.overTimeStep1=Jboot.configValue("leaveRestStep1");
        Global.leaveRestStep2=Jboot.configValue("leaveRestStep2");
        Global.defaultQiYeEmailSuffix=Jboot.configValue("defaultQiYeEmailSuffix");
        Global.agentIdSecretJson=Jboot.configValue("agentIdSecretJson");
        Global.dispatchStep1=Jboot.configValue("dispatchStep1");
        Global.dispatchStep2=Jboot.configValue("dispatchStep2");
        Global.orgUrl = Jboot.configValue("orgUrl");
        Global.qyMessageSendUrl=Jboot.configValue("qyMessageSendUrl");
        Global.checkIdCardUrl=Jboot.configValue("checkIdCardUrl");
        Global.agencyListUrl=Jboot.configValue("agencyListUrl");
        Global.authorityAutoSaveUrl=Jboot.configValue("authorityAutoSaveUrl");
        Global.interviewRecordFormUrl=Jboot.configValue("interviewRecordFormUrl");

        Global.executorService=new ThreadPoolExecutor(10, 50, 0L
                , TimeUnit.MILLISECONDS
                , new LinkedBlockingQueue<>(1024)
                , new ThreadFactory() {


            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r);
            }
        },new ThreadPoolExecutor.AbortPolicy());
    }
}
