package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersOrgEmployeePerformanceService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.util.Date;
import java.util.List;

@RequestMapping(value="/empPerformance", viewPath="/modules_page/pers/empPerformance")
public class PersOrgEmployeePerformanceController extends BaseController {

    @Inject
    PersOrgEmployeePerformanceService persOrgEmployeePerformanceService;
    @Inject
    private PersOrgService persOrgService;

    public void index(){

        render("index.html");
    }

    public void pageList(){
        String deptId=getPara("deptId");
        String fullName=getPara("fullName");
        String yearMonth=getPara("yearMonth");

        if(StrKit.isBlank(yearMonth)){
            yearMonth= DateUtils.formatDate(new Date(),"yyyy-MM");
        }

        Page<Record> recordPage=persOrgEmployeePerformanceService.pageList(getParaToInt("page"),getParaToInt("limit"),deptId,yearMonth,fullName, AuthUtils.getUserId());

        if(recordPage.getList()!=null && recordPage.getList().size()>0){
            for (Record record : recordPage.getList()) {
                record.set("deptName",persOrgService.getOrgParentNames(record.getStr("deptId")));
            }
        }
        renderJson(new DataTable<Record>(recordPage));
    }

    public void export(){
        String deptId=getPara("deptId");
        String fullName=getPara("fullName");
        String yearMonth=getPara("yearMonth");

        if(StrKit.isBlank(yearMonth)){
            yearMonth= DateUtils.formatDate(new Date(),"yyyy-MM");
        }
        String exportDeptName="";
        if(StrKit.notBlank(deptId)){
            exportDeptName=persOrgService.getOrgParentNames(deptId);
        }

        Page<Record> recordPage=persOrgEmployeePerformanceService.pageList(1,20000,deptId,yearMonth,fullName, AuthUtils.getUserId());

        if(recordPage.getList()!=null && recordPage.getList().size()>0){
            for (Record record : recordPage.getList()) {
                record.set("deptName",persOrgService.getOrgParentNames(record.getStr("deptId")));
            }
        }
        String[] title={"工号","姓名","部门","职位","个人业绩达标额","个人合同款","个人充值款","团队业绩达标额","团队业绩额","底薪","底薪浮动","业务提成","个人业绩奖","团队业绩奖","管理奖","销售冠军奖","社保扣费"};
        String fileName=exportDeptName+yearMonth+"工资计算结果.xls";
        String sheetName = "工资计算结果";

        List<Record> recordList=recordPage.getList();

        String[][] content=new String[recordList.size()][title.length];
        for(int i=0;i<recordList.size();i++){
            Record record=recordList.get(i);
            content[i][0]=record.getStr("workNum");
            content[i][1]=record.getStr("fullName");
            content[i][2]=record.getStr("deptName");
            content[i][3]=record.getStr("positionName");
            content[i][4]=record.getStr("individualPerformanceTarget");
            content[i][5]=record.getStr("individualContractAmount");
            content[i][6]=record.getStr("individualRechargeAmount");
            content[i][7]=record.getStr("teamPerformanceTarget");
            content[i][8]=record.getStr("teamPerformanceAmount");

            content[i][9]=record.getStr("baseSalary");
            content[i][10]=record.getStr("baseSalaryFloat");
            content[i][11]=record.getStr("businessPushSalary");
            content[i][12]=record.getStr("individualPerformanceSalary");
            content[i][13]=record.getStr("teamPerformanceSalary");
            content[i][14]=record.getStr("managementSalary");
            content[i][15]=record.getStr("socialSecurity");
            content[i][16]=record.getStr("salesChampionSalary");
        }

        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}
