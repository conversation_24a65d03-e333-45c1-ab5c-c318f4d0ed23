package com.cszn.personnel.web.support.crossorigin;

import javax.servlet.http.HttpServletResponse;

import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;

public class CrossInterceptor implements Interceptor {
	
	@Override
	public void intercept(Invocation inv) {
		
		CrossOrigin cross = inv.getController().getClass().getAnnotation(CrossOrigin.class);
		if (cross != null) {
			handler(inv.getController().getResponse());
			inv.invoke();
			return;
		}
		cross = inv.getMethod().getAnnotation(CrossOrigin.class);
		if (cross != null) {
			handler(inv.getController().getResponse());
			//解决跨域调用2次
			String method=inv.getController().getRequest().getMethod();
			if("OPTIONS".equals(method)){
				inv.getController().renderJson();
				return;
			}
			inv.invoke();
			return;
		}
		inv.invoke();
	}

	private void handler(HttpServletResponse response) {
		response.setHeader("Access-Control-Allow-Origin", "*");
		response.setHeader("Access-Control-Allow-Credentials", "true");
		response.setHeader("Access-Control-Allow-Methods", "*");
		response.setHeader("Access-Control-Max-Age", "3600");
		response.setHeader("Access-Control-Allow-Headers", "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, Access-Token");
		response.setHeader("Access-Control-Expose-Headers", "*");
	}
}
