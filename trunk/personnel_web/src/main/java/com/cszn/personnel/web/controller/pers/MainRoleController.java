package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainRoleService;
import com.cszn.integrated.service.api.main.MainUserGroupService;
import com.cszn.integrated.service.entity.enums.PositionLv;
import com.cszn.integrated.service.entity.main.MainRole;
import com.cszn.integrated.service.entity.main.MainRoleGroupRel;
import com.cszn.integrated.service.entity.main.MainUserGroup;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

@RequestMapping(value = "/pers/role",viewPath = "/modules_page/pers/role/")
public class MainRoleController extends BaseController {

    @Inject
    MainRoleService mainRoleService;
    @Inject
    MainUserGroupService mainUserGroupService;

    public void index(){

        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MainRole mainRole=mainRoleService.findById(id);
            set("record",mainRole);
            setAttr("groupIds",Db.queryStr("select GROUP_CONCAT(user_group_id) from main_role_group_rel where user_role_id=?",id));
        }

        List<MainUserGroup> userGroupList = mainUserGroupService.getUserGroupList();
        setAttr("userGroupList",userGroupList);
        setAttr("positionLvs", PositionLv.values());
        render("form.html");
    }

    public void pageList(){
        MainRole mainRole=getBean(MainRole.class,"",true);
        Columns columns=Columns.create();
        if(StrKit.notBlank(mainRole.getRoleName())){
            columns.like("role_name","%"+mainRole.getRoleName()+"%");
        }
        columns.add("del_flag","0");
        Page<MainRole> page=mainRoleService.paginateByColumns(getParaToInt("page"),getParaToInt("limit"),columns);
        renderJson(new DataTable<MainRole>(page));
    }
	
	@Clear(LogInterceptor.class)
	public void mainRoleXmSelectTree() {
		renderJson(Ret.ok("msg", "加载成功!").set("mainRoleSelectTree", mainRoleService.mainRoleXmSelectTree()));
	}

    public void save(){
        String groupIds = getPara("groupIds");
        MainRole mainRole=getBean(MainRole.class,"",true);
        String sql="select * from main_role where role_name=? and del_flag='0' ";
        List<Object> params=new ArrayList<>();
        params.add(mainRole.getRoleName());

        if(StrKit.notBlank(mainRole.getId())){
            sql+=" and id<>? ";
            params.add(mainRole.getId());
        }
        if(Db.findFirst(sql,params.toArray())!=null){
            renderJson(Ret.fail("msg","该角色已存在，请勿重复添加"));
            return;
        }
        String[] groupIdArray=null;
        if(StrKit.notBlank(groupIds)){
            groupIdArray=groupIds.split(",");
        }
        boolean flag=mainRoleService.saveMainRole(mainRole, AuthUtils.getUserId());
        if(flag){
            //删除现有的关系记录
            Db.delete(" delete from main_role_group_rel where user_role_id=? ",mainRole.getId());
            if(groupIdArray!=null){
                List<MainRoleGroupRel> saveRoleGroupRelList=new ArrayList<>();
                for (String groupId : groupIdArray) {
                    MainRoleGroupRel roleGroupRel=new MainRoleGroupRel();
                    roleGroupRel.setId(IdGen.getUUID());
                    roleGroupRel.setUserGroupId(groupId);
                    roleGroupRel.setUserRoleId(mainRole.getId());
                    saveRoleGroupRelList.add(roleGroupRel);
                }
                Db.batchSave(saveRoleGroupRelList,saveRoleGroupRelList.size());

            }
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void del(){
        String id=getPara("id");
        MainRole mainRole=mainRoleService.findById(id);
        mainRole.setDelFlag("1");
        boolean flag=mainRoleService.saveMainRole(mainRole, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
