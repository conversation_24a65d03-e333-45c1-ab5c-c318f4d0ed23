package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.enums.PayRatioType;
import com.cszn.integrated.service.entity.enums.PersTaskType;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;

@RequestMapping(value="/socialSecurityApply", viewPath="/modules_page/pers/socialSecurity")
public class PersOrgEmployeeSocialSecurityController extends BaseController {

    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersOrgCompanyService persOrgCompanyService;
    @Inject
    private PersTaskService persTaskService;
    @Inject
    private UserService userService;
    @Inject
    private PersApprovalService persApprovalService;
    @Inject
    private PersSocialSecurityApplyRecordService persSocialSecurityApplyRecordService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    PersOrgEmployeeSocialSecurityService persOrgEmployeeSocialSecurityService;
    @Inject
    private PersPositionService persPositionService;

    public void index(){
        List<PersOrgCompany> orgCompanyList=persOrgCompanyService.orgCompanyList();

        setAttr("orgCompanyList",orgCompanyList);
        render("index.html");
    }

    public void form(){

        render("form.html");
    }

    public void pageList(){
        PersSocialSecurityApplyRecord applyRecord=getBean(PersSocialSecurityApplyRecord.class,"",true);
        applyRecord.setYearMonth(getPara("date"));

        Page<Record> applyRecordPage=persSocialSecurityApplyRecordService.pageList(getParaToInt("page"),getParaToInt("limit"),applyRecord, AuthUtils.getUserId());
        if(applyRecordPage.getList()!=null && applyRecordPage.getList().size()>0){
            for (Record record : applyRecordPage.getList()) {
                record.set("dept_name",persOrgService.getOrgParentNames(record.getStr("dept_id")));
                record.set("apply_user",userService.findById(record.getStr("apply_user_id")));
                record.set("company",persOrgCompanyService.findById(record.getStr("company_id")));
                PersTask persTask=persTaskService.findByRecordId(record.getStr("id"));
                if(persTask!=null){
                    Map<String,Object> taskDetail=persApprovalService.getTaskDetail(persTask.getTaskId(),AuthUtils.getUserId());
                    record.set("stepts",taskDetail.get("stepts"));
                    record.set("taskState",taskDetail.get("taskState"));
                    record.set("isSaveHandle",taskDetail.get("isSaveHandle"));
                    record.set("currentStepName",taskDetail.get("currentStepName"));
                    record.set("allowApprove",taskDetail.get("AllowApprove"));
                    record.set("allowReject",taskDetail.get("AllowReject"));
                    record.set("allowAbort",taskDetail.get("AllowAbort"));
                    record.set("allowSubmit",taskDetail.get("AllowSubmit"));
                    record.set("taskId",persTask.getTaskId());
                }
            }
        }
        renderJson(new DataTable<Record>(applyRecordPage));
    }

    public void socialSecurityForm(){
        String deptId=getPara("deptId");
        String yearMonth=getPara("yearMonth");
        String companyId=getPara("companyId");

        String name=persOrgService.getOrgParentNames(deptId);
        setAttr("deptId",deptId);
        setAttr("deptName",name);
        setAttr("yearMonth",yearMonth);
        setAttr("companyId",companyId);


        Date date = DateUtils.parseDate(yearMonth);
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);

        String lastYearMonth=DateUtils.formatDate(calendar.getTime(),"yyyy-MM");

        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,deptId);

        List<String> deptIdList=new ArrayList<>();
        deptIdList.add(deptId);

        for (PersOrg org : orgList) {
            deptIdList.add(org.getId());
        }

        //先获取上个月购买的
        List<String> lastMonthEmpIds=Db.query(" select b.emp_id from pers_social_security_apply_record a  " +
                "INNER join pers_org_employee_social_security b on a.id=b.apply_record_id " +
                "where a.`year_month`=? and a.company_id=? and a.dept_id=? and a.`status` in ('2','3') and b.del_flag='0'",lastYearMonth,companyId,deptId);
        List<PersOrgEmployee> allEmployeeList=null;
        if(lastMonthEmpIds.size()>0){
            allEmployeeList=new ArrayList<>();
            for (String lastMonthEmpId : lastMonthEmpIds) {
                allEmployeeList.add(persOrgEmployeeService.findById(lastMonthEmpId));
            }
        }else{
            allEmployeeList=persOrgEmployeeService.findEmpListByDepts(deptIdList);
        }

        List<String> empIds=Db.query("select a.emp_id from pers_org_employee_social_security a left join pers_org_employee b on b.id=a.emp_id " +
                "left join pers_social_security_apply_record c on a.apply_record_id=c.id " +
                "where a.`year_month`=? and a.del_flag='0' and c.`status` in ('2','3')  ",yearMonth);
        //排除已买的员工
        List<PersOrgEmployee> employeeList=new ArrayList<>();
        for (PersOrgEmployee employee : allEmployeeList) {
            if(!empIds.contains(employee.getId())){
                employeeList.add(employee);
            }
        }


        List<String> empIdList=new ArrayList<>();
        for (PersOrgEmployee employee : employeeList) {
            empIdList.add(employee.getId());
        }
        //查询上个月的记录
        List<PersOrgEmployeeSocialSecurity> employeeSocialSecurityList=persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityList(lastYearMonth,empIdList);

        Map<String,PersOrgEmployeeSocialSecurity> employeeProvidentFundMap=new HashMap<>();

        for (PersOrgEmployeeSocialSecurity socialSecurity : employeeSocialSecurityList) {
            employeeProvidentFundMap.put(socialSecurity.getEmpId(),socialSecurity);
        }

        List<Record> recordList=new ArrayList<>();


        if(lastMonthEmpIds.size()>0){
            for (PersOrgEmployee employee : employeeList) {
                Record record=new Record();
                record.set("empId",employee.getId());
                record.set("workNum",employee.getWorkNum());
                record.set("fullName",employee.getFullName());
                record.set("idcard",employee.getIdCard());
                record.set("sex",employee.getSex());
                record.set("maritalStatus",employee.getMaritalStatus());
                record.set("phoneNum",employee.getPhoneNum());
                record.set("idCardAddr",employee.getIdCardAddr());
                PersOrgEmployeeSocialSecurity employeeSocialSecurity=employeeProvidentFundMap.get(employee.getId());
                record.set("type",employeeSocialSecurity.getType());

                recordList.add(record);
            }

        }else{
            for (PersOrgEmployee employee : employeeList) {
                Record record=new Record();
                record.set("empId",employee.getId());
                record.set("workNum",employee.getWorkNum());
                record.set("fullName",employee.getFullName());
                record.set("idcard",employee.getIdCard());
                record.set("sex",employee.getSex());
                record.set("maritalStatus",employee.getMaritalStatus());
                record.set("phoneNum",employee.getPhoneNum());
                record.set("idCardAddr",employee.getIdCardAddr());
                PersOrgEmployeeSocialSecurity employeeSocialSecurity=employeeProvidentFundMap.get(employee.getId());
                if(employeeSocialSecurity!=null){
                    if("1".equals(employeeSocialSecurity.getType()) || "2".equalsIgnoreCase(employeeSocialSecurity.getType())){
                        record.set("type","2");
                    }else if("3".equals(employeeSocialSecurity.getType()) || "4".equalsIgnoreCase(employeeSocialSecurity.getType())){
                        record.set("type","4");
                    }
                    //record.set("payRatio",employeeSocialSecurity.getPayRatio());
                }else{
                    //初始化
                    record.set("type","1");
                }
                recordList.add(record);
            }

            //上个月有增、续，且离职的
            for(String key:employeeProvidentFundMap.keySet()){
                if(!empIdList.contains(key)){
                    PersOrgEmployee employee=persOrgEmployeeService.findById(key);
                    if("quit".equals(employee.getArchiveStatus())){

                        Record record=new Record();
                        record.set("empId",employee.getId());
                        record.set("workNum",employee.getWorkNum());
                        record.set("fullName",employee.getFullName());
                        record.set("idcard",employee.getIdCard());
                        record.set("sex",employee.getSex());
                        record.set("maritalStatus",employee.getMaritalStatus());
                        record.set("isLocal",employee.getIsLocal());
                        record.set("phoneNum",employee.getPhoneNum());
                        record.set("idCardAddr",employee.getIdCardAddr());
                        //record.set("payRatio",employeeProvidentFundMap.get(key).getPayRatio());
                        record.set("type","3");

                        recordList.add(record);
                    }
                }
            }
        }



        List<Record> addList=new ArrayList<>();
        List<Record> continueList=new ArrayList<>();
        List<Record> delList=new ArrayList<>();
        List<Record> abandonList=new ArrayList<>();

        for (Record record : recordList) {
            if("1".equals(record.getStr("type"))){
                addList.add(record);
            }else if("2".equals(record.getStr("type"))){
                continueList.add(record);
            }else if("3".equals(record.getStr("type"))){
                delList.add(record);
            }else if("4".equals(record.getStr("type"))){
                abandonList.add(record);
            }
            PersOrgEmployeeSocialSecurity socialSecurity=employeeProvidentFundMap.get(record.getStr("empId"));
            record.set("lastMonthType","");
            if(socialSecurity!=null){
                record.set("lastMonthType",socialSecurity.getType());
            }
        }
        List<PersOrgCompany> orgCompanyList=persOrgCompanyService.orgCompanyList();
        setAttr("orgCompanyList",orgCompanyList);
        setAttr("payRatioTypes", PayRatioType.values());
        /*setAttr("addList",addList);
        setAttr("continueList",continueList);
        setAttr("delList",delList);
        setAttr("abandonList",abandonList);*/
        render("form.html");
    }

    public void editSocialSecurityForm(){
        String id=getPara("id");
        String taskId=getPara("taskId");
        String userId=getPara("userId");

        if(StrKit.isBlank(id)){
            PersTask task=persTaskService.findByTaskId(taskId);
            id=task.getRecordId();
            setAttr("H5",true);
        }
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }

        PersSocialSecurityApplyRecord applyRecord=persSocialSecurityApplyRecordService.findById(id);
        setAttr("yearMonth",applyRecord.getYearMonth());

        Date date =DateUtils.parseDate(applyRecord.getYearMonth());
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH,-1);

        String lastYearMonth=DateUtils.formatDate(calendar.getTime(),"yyyy-MM");


        List<Record> addList=new ArrayList<>();
        List<Record> continueList=new ArrayList<>();
        List<Record> delList=new ArrayList<>();
        List<Record> abandonList=new ArrayList<>();

        List<Record> recordList=Db.find("select a.id,a.emp_id as empId,b.work_num as workNum,b.full_name as fullName,b.id_card as idcard ,a.phone_num as phoneNum" +
                ",a.idcard_addr as idCardAddr,a.type,a.company_id as companyId,a.is_first as isFirst,a.registered_type as registeredType,a.status,a.exception_remark as exceptionRemark,b.sex,a.dept_id,a.position_id " +
                "from pers_org_employee_social_security a  left join pers_org_employee b on a.emp_id=b.id " +
                " where a.apply_record_id=? and a.del_flag='0' order by b.work_num ",applyRecord.getId());

        List<String> empIdList=new ArrayList<>();

        for (Record record : recordList) {
            if("1".equals(record.getStr("type"))){
                addList.add(record);
            }else if("2".equals(record.getStr("type"))){
                continueList.add(record);
            }else if("3".equals(record.getStr("type"))){
                delList.add(record);
            }else if("4".equals(record.getStr("type"))){
                abandonList.add(record);
            }
            if("male".equals(record.getStr("sex"))){
                record.set("sex","男");
            }else if("female".equals(record.getStr("sex"))){
                record.set("sex","女");
            }
            PersPosition persPosition=persPositionService.findById(record.getStr("position_id"));
            if(persPosition!=null){
                record.set("positionName",persPosition.getPositionName());
            }else{
                record.set("positionName","");
            }

            record.set("deptName",persOrgService.getOrgParentNames(record.getStr("dept_id")));
            empIdList.add(record.getStr("empId"));
        }

        //查询上个月的记录
        List<PersOrgEmployeeSocialSecurity> employeeProvidentFundList=persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityList(lastYearMonth,empIdList);
        Map<String,PersOrgEmployeeSocialSecurity> employeeProvidentFundMap=new HashMap<>();

        for (PersOrgEmployeeSocialSecurity persOrgEmployeeProvidentFund : employeeProvidentFundList) {
            employeeProvidentFundMap.put(persOrgEmployeeProvidentFund.getEmpId(),persOrgEmployeeProvidentFund);
        }

        for (Record record : recordList) {
            PersOrgEmployeeSocialSecurity lastProvidentFund=employeeProvidentFundMap.get(record.getStr("empId"));
            record.set("lastMonthType","");
            if(lastProvidentFund!=null){
                record.set("lastMonthType",lastProvidentFund.getType());
            }
        }

        PersTask persTask=persTaskService.findByRecordId(applyRecord.getId());
        if(persTask!=null){
            Map<String,Object> taskDetail=persApprovalService.getTaskDetail(persTask.getTaskId(),userId);
            setAttr("stepts",taskDetail.get("stepts"));
            setAttr("taskState",taskDetail.get("taskState"));
            setAttr("isSaveHandle",taskDetail.get("isSaveHandle"));
            setAttr("currentStepName",taskDetail.get("currentStepName"));
            setAttr("currentStepAlias",taskDetail.get("currentStepAlias"));

            JSONArray currentSteps=(JSONArray)taskDetail.get("currentSteps");
            //批准
            boolean allowApprove=false;
            //拒绝
            boolean allowReject=false;
            //中止
            boolean allowAbort=false;
            //提交
            boolean allowSubmit=false;
            if(currentSteps!=null){
                for(int i=0;i<currentSteps.size();i++){
                    JSONObject currentStep=currentSteps.getJSONObject(i);
                    boolean flag=false;
                    for(int j=0;j<currentStep.getJSONArray("UserIds").size();j++){
                        if(!flag && userId.equalsIgnoreCase(currentStep.getJSONArray("UserIds").getString(j))){
                            flag=true;
                        }
                    }
                    if(flag){
                        allowApprove=currentStep.getBoolean("AllowApprove");
                        allowReject=currentStep.getBoolean("AllowReject");

                        allowSubmit=currentStep.getBoolean("AllowSubmit");
                    }
                    allowAbort=currentStep.getBoolean("AllowAbort");
                }
            }


            setAttr("allowApprove",allowApprove);
            setAttr("allowReject",allowReject);
            setAttr("allowAbort",allowAbort);
            setAttr("allowSubmit",allowSubmit);
            setAttr("taskId",persTask.getTaskId());
        }

        setAttr("applyRecord",applyRecord);
        List<PersOrgCompany> orgCompanyList=persOrgCompanyService.orgCompanyList();
        setAttr("orgCompanyList",orgCompanyList);
        setAttr("payRatioTypes", PayRatioType.values());
        setAttr("addList",addList);
        setAttr("continueList",continueList);
        setAttr("delList",delList);
        setAttr("abandonList",abandonList);
        setAttr("type","edit");
        setAttr("id",id);
        persTaskService.getSubmitInfo(persTask);
        setAttr("persTask",persTask);
        if(!AuthUtils.isLogin()){
            setAttr("device","mobile");
        }
        render("form.html");
    }

    public void applySave(){
        String deptId=getPara("deptId");
        String yearMonth=getPara("yearMonth");
        String dataArray=getPara("data");
        String companyId=getPara("companyId");
        String remark=getPara("remark");
        if(StrKit.isBlank(deptId) || StrKit.isBlank(yearMonth) || StrKit.isBlank(dataArray)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        JSONArray jsonArray= JSON.parseArray(dataArray);
        if(jsonArray==null || jsonArray.size()==0){
            renderJson(Ret.fail("msg","提交的记录不能为0条"));
            return;
        }

        PersOrgCompany orgCompany = persOrgCompanyService.findById(companyId);
        if(StrKit.isBlank(orgCompany.getSocialSecurityUserId())){
            renderJson(Ret.fail("msg","购买单位社保经办人未设置"));
            return;
        }


        List<String> deptIds=new ArrayList<>();
        deptIds.add(deptId);
        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,deptId);

        for (PersOrg org : orgList) {
            deptIds.add(org.getId());
        }


        PersSocialSecurityApplyRecord applyRecord=new PersSocialSecurityApplyRecord();
        applyRecord.setId(IdGen.getUUID());
        applyRecord.setYearMonth(yearMonth);
        applyRecord.setDeptId(deptId);
        applyRecord.setCompanyId(companyId);
        applyRecord.setDelFlag("0");
        applyRecord.setStatus("1");
        applyRecord.setRemark(remark);
        applyRecord.setApplyUserId(AuthUtils.getUserId());
        applyRecord.setApplyTime(new Date());
        applyRecord.setCreateBy(AuthUtils.getUserId());
        applyRecord.setCreateDate(new Date());
        applyRecord.setUpdateBy(AuthUtils.getUserId());
        applyRecord.setUpdateDate(new Date());

        List<PersOrgEmployeeSocialSecurity> socialSecurityList=new ArrayList<>();

        String str="";
        List<String> parmas=new ArrayList<>();
        parmas.add(yearMonth);
        for (int i = 0; i < jsonArray.size(); i++) {

            JSONObject jsonObject=jsonArray.getJSONObject(i);

            str+="?,";
            parmas.add(jsonObject.getString("empId"));
            PersOrgEmployee employee=persOrgEmployeeService.findById(jsonObject.getString("empId"));
            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                if(deptIds.contains(empDeptId)){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }

            PersOrgEmployeeSocialSecurity socialSecurity=new PersOrgEmployeeSocialSecurity();
            socialSecurity.setId(IdGen.getUUID());
            socialSecurity.setApplyRecordId(applyRecord.getId());
            socialSecurity.setIdcard(employee.getIdCard());
            socialSecurity.setDeptId(empDeptId);
            socialSecurity.setPositionId(empPositionId);
            socialSecurity.setEmpId(jsonObject.getString("empId"));
            socialSecurity.setType(jsonObject.getString("type"));
            socialSecurity.setIdcardAddr(jsonObject.getString("idCardAddr"));
            socialSecurity.setCompanyId(jsonObject.getString("companyId"));
            socialSecurity.setIsFirst(jsonObject.getString("isFirst"));
            socialSecurity.setRegisteredType(jsonObject.getString("registeredType"));
            socialSecurity.setPhoneNum(employee.getPhoneNum());
            socialSecurity.setYearMonth(yearMonth);
            socialSecurity.setDelFlag("0");
            socialSecurity.setCreateBy(AuthUtils.getUserId());
            socialSecurity.setCreateDate(new Date());
            socialSecurity.setUpdateBy(AuthUtils.getUserId());
            socialSecurity.setUpdateDate(new Date());

            socialSecurityList.add(socialSecurity);
        }
        str=str.substring(0,str.length()-1);

        List<String> empNames=Db.query("select b.full_name from pers_org_employee_social_security a left join pers_org_employee b on b.id=a.emp_id " +
                "left join pers_social_security_apply_record c on a.apply_record_id=c.id " +
                "where a.`year_month`=? and a.del_flag='0' and c.`status` in ('2','3') and a.emp_id in ("+str+") ",parmas.toArray());
        /*if(empNames.size()>0){
            renderJson(Ret.fail("msg",JSON.toJSONString(empNames)+"该月已存在记录"));
            return;
        }*/

        if(applyRecord.save()){
            Db.batchSave(socialSecurityList,socialSecurityList.size());

            PersTask task=new PersTask();
            task.setId(IdGen.getUUID());
            task.setTaskNo(PersTaskType.socialSecurity.getTaskNo());
            task.setRecordId(applyRecord.getId());
            task.setIsEnd("0");
            task.setDelFlag("0");
            task.setCreateBy(applyRecord.getCreateBy());
            task.setCreateDate(new Date());
            task.setUpdateBy(AuthUtils.getUserId());
            task.setUpdateDate(new Date());
            applyRecord.setStatus("2");
            applyRecord.update();
            Map<String,Object> resultMap=persTaskService.httpCreateTask(task);
            /*if((boolean) resultMap.get("flag")){

            }else{
                renderJson(Ret.fail("msg",resultMap.get("msg")));
            }*/
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            Ret.fail("msg","操作失败");
        }

    }

    public void applyEdit(){
        String id=getPara("id");
        String taskId=getPara("taskId");
        String dataArray=getPara("data");
        String userId=getPara("userId");
        String remark=getPara("remark");
        if(StrKit.isBlank(taskId) || StrKit.isBlank(id) || StrKit.isBlank(dataArray)){
            renderJson(Ret.fail("msg","参数缺失"));
            return;
        }
        PersSocialSecurityApplyRecord applyRecord=persSocialSecurityApplyRecordService.findById(id);
        JSONArray jsonArray= JSON.parseArray(dataArray);
        if(jsonArray==null || jsonArray.size()==0){
            renderJson(Ret.fail("msg","提交的记录不能为0条"));
            return;
        }
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }
        List<String> deptIds=new ArrayList<>();
        deptIds.add(applyRecord.getDeptId());
        List<PersOrg> orgList=new ArrayList<>();
        List<ZTree> zTreeList=persOrgService.allOrgTree();
        persOrgService.findChildren(zTreeList,orgList,applyRecord.getDeptId());

        for (PersOrg org : orgList) {
            deptIds.add(org.getId());
        }



        List<PersOrgEmployeeSocialSecurity> addSocialSecurityList=new ArrayList<>();

        List<PersOrgEmployeeSocialSecurity> updateSocialSecurityList=new ArrayList<>();

        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);

            if(StrKit.isBlank(jsonObject.getString("id"))){
                PersOrgEmployee employee=persOrgEmployeeService.findById(jsonObject.getString("empId"));
                String empDeptId=null;
                List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
                for (String e : empDeptIds) {
                    if(deptIds.contains(empDeptId)){
                        empDeptId =e;
                        break;
                    }
                }
                if(empDeptId==null){
                    empDeptId=empDeptIds.get(0);
                }
                String empPositionId=null;
                List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
                for (String e : empPositionIds) {
                    PersPosition persPosition=persPositionService.findById(e);
                    if(empDeptId.equals(persPosition.getOrgId())){
                        empPositionId=e;
                        break;
                    }
                }
                PersOrgEmployeeSocialSecurity socialSecurity=new PersOrgEmployeeSocialSecurity();
                socialSecurity.setId(IdGen.getUUID());
                socialSecurity.setApplyRecordId(applyRecord.getId());
                socialSecurity.setIdcard(employee.getIdCard());
                socialSecurity.setDeptId(empDeptId);
                socialSecurity.setPositionId(empPositionId);
                socialSecurity.setEmpId(jsonObject.getString("empId"));
                socialSecurity.setType(jsonObject.getString("type"));
                socialSecurity.setIdcardAddr(jsonObject.getString("idCardAddr"));
                socialSecurity.setCompanyId(jsonObject.getString("companyId"));
                socialSecurity.setIsFirst(jsonObject.getString("isFirst"));
                socialSecurity.setRegisteredType(jsonObject.getString("registeredType"));
                socialSecurity.setYearMonth(applyRecord.getYearMonth());
                socialSecurity.setPhoneNum(employee.getPhoneNum());
                socialSecurity.setDelFlag("0");
                socialSecurity.setCreateBy(userId);
                socialSecurity.setCreateDate(new Date());
                socialSecurity.setUpdateBy(userId);
                socialSecurity.setUpdateDate(new Date());
                addSocialSecurityList.add(socialSecurity);
            }else{
                PersOrgEmployeeSocialSecurity socialSecurity=persOrgEmployeeSocialSecurityService.findById(jsonObject.getString("id"));
                socialSecurity.setType(jsonObject.getString("type"));
                socialSecurity.setIdcardAddr(jsonObject.getString("idCardAddr"));
                socialSecurity.setCompanyId(jsonObject.getString("companyId"));
                socialSecurity.setIsFirst(jsonObject.getString("isFirst"));
                socialSecurity.setUpdateDate(new Date());
                socialSecurity.setUpdateBy(userId);
                updateSocialSecurityList.add(socialSecurity);

            }

        }

        List<String> parmas=new ArrayList<>();
        parmas.add(applyRecord.getYearMonth());
        String str="";

        List<String> delParams=new ArrayList<>();
        delParams.add(applyRecord.getId());
        String delStr="";
        for (PersOrgEmployeeSocialSecurity socialSecurity : addSocialSecurityList) {


            parmas.add(socialSecurity.getEmpId());
            delParams.add(socialSecurity.getEmpId());
            str+="?,";
            delStr+="?,";
        }
        if(addSocialSecurityList.size()>0){
            str=str.substring(0,str.length()-1);
            List<String> empNames=Db.query("select b.full_name from pers_org_employee_social_security a left join pers_org_employee b on b.id=a.emp_id " +
                    "left join pers_social_security_apply_record c on a.apply_record_id=c.id " +
                    "where a.`year_month`=? and a.del_flag='0' and c.`status` in ('2','3') and a.emp_id in ("+str+") ",parmas.toArray());
            /*if(empNames.size()>0){
                renderJson(Ret.fail("msg",JSON.toJSONString(empNames)+"该月已存在记录"));
                return;
            }*/
        }


        for (PersOrgEmployeeSocialSecurity employeeSocialSecurity : updateSocialSecurityList) {
            delStr+="?,";
            delParams.add(employeeSocialSecurity.getEmpId());
        }
        delStr=delStr.substring(0,delStr.length()-1);
        List<String> delIds=Db.query("select a.id from pers_org_employee_provident_fund a left join pers_org_employee b on b.id=a.emp_id left join pers_provident_fund_apply_record c on a.apply_record_id=c.id " +
                "where c.id=? and a.emp_id not in ("+delStr+")  and a.del_flag='0' ",delParams.toArray());


        applyRecord.setRemark(remark);
        applyRecord.setUpdateBy(userId);
        applyRecord.setUpdateDate(new Date());
        if(applyRecord.update()){
            if(addSocialSecurityList.size()>0){
                Db.batchSave(addSocialSecurityList,addSocialSecurityList.size());
                /*for (PersOrgEmployeeProvidentFund employeeProvidentFund : addProvidentFundList) {
                    PersOrgEmployee employee=persOrgEmployeeService.findById(employeeProvidentFund.getEmpId());
                    employee.setIsLocal(employeeProvidentFund.getIsLocal());
                    employee.setMaritalStatus(employeeProvidentFund.getMaritalStatus());

                    employee.update();
                }*/
            }
            if(updateSocialSecurityList.size()>0){
                Db.batchUpdate(updateSocialSecurityList,updateSocialSecurityList.size());
                /*for (PersOrgEmployeeProvidentFund employeeProvidentFund : updateProvidentFundList) {
                    PersOrgEmployee employee=persOrgEmployeeService.findById(employeeProvidentFund.getEmpId());
                    employee.setIsLocal(employeeProvidentFund.getIsLocal());
                    employee.setMaritalStatus(employeeProvidentFund.getMaritalStatus());
                    employee.update();
                }*/
            }
            if(delIds.size()>0){

                for (String delId : delIds) {
                    Db.update("update pers_org_employee_social_security set del_flag='1',update_by=?,update_date=? where id=?",userId,new Date(),delId);
                }

            }

            applyRecord.setUpdateDate(new Date());
            applyRecord.update();

            Map<String,Object> taskDetailMap=persApprovalService.getTaskDetail(taskId,userId);

            Map<String,Object> resultMap=persApprovalService.doTask(taskId,PersTaskType.socialSecurity.getTaskNo(),"5",null,"",userId);
            /*if((boolean) resultMap.get("flag")){

            }else{
                renderJson(Ret.fail("msg",resultMap.get("msg")));
            }*/
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void empTable(){
        render("empTable.html");
    }

    public void genSocialSecurity(){
        String yearMonth=getPara("yearMonth");
        Date date=DateUtils.getNextDay(DateUtils.parseDate(yearMonth+"-01"),-1);
        List<PersOrgEmployeeSocialSecurity> socialSecurityList =persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityList(DateUtils.formatDate(date,"yyyy-MM"),null);

        List<PersOrgEmployeeSocialSecurity> addsocialSecurityList=new ArrayList<>();


        for (PersOrgEmployeeSocialSecurity socialSecurity : socialSecurityList) {
            PersOrgEmployee employee=persOrgEmployeeService.findById(socialSecurity.getEmpId());

            PersOrgEmployeeSocialSecurity newSocialSecurity=new PersOrgEmployeeSocialSecurity();
            newSocialSecurity.setId(IdGen.getUUID());
            newSocialSecurity.setEmpId(socialSecurity.getEmpId());
            newSocialSecurity.setCreateDate(new Date());
            newSocialSecurity.setUpdateDate(new Date());
            newSocialSecurity.setYearMonth(yearMonth);
            newSocialSecurity.setDelFlag("0");

            String empDeptId=null;
            List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
            for (String id : empDeptIds) {
                PersOrg org =persOrgService.findById(id);
                if(org!=null && "0".equals(org.getDelFlag()) && "1".equals(org.getIsEnable())){
                    empDeptId =id;
                    break;
                }
            }
            if(empDeptId==null){
                empDeptId=empDeptIds.get(0);
            }
            String empPositionId=null;
            List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
            for (String id : empPositionIds) {
                PersPosition persPosition=persPositionService.findById(id);
                if(empDeptId.equals(persPosition.getOrgId())){
                    empPositionId=id;
                    break;
                }
            }
            newSocialSecurity.setDeptId(empDeptId);
            newSocialSecurity.setPositionId(empPositionId);
            newSocialSecurity.setIdcard(employee.getIdCard());
            newSocialSecurity.setCompanyId(socialSecurity.getCompanyId());
            newSocialSecurity.setPhoneNum(employee.getPhoneNum());
            newSocialSecurity.setIdcardAddr(employee.getIdCardAddr());
            newSocialSecurity.setIsFirst("0");


            if("1".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("3");
                }

            }else if("2".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("2");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("3");
                }
            }else if("3".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("3");
                }
            }else if("4".equals(socialSecurity.getType())){
                if("incumbency".equals(employee.getArchiveStatus())){
                    newSocialSecurity.setType("4");
                }else if("quit".equals(employee.getArchiveStatus())){

                }
            }
            if(StrKit.notBlank(newSocialSecurity.getType())){
                List<String> empIds=new ArrayList<>();
                empIds.add(newSocialSecurity.getEmpId());
                List<PersOrgEmployeeSocialSecurity> socialSecurities=persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityList(yearMonth,empIds);
                if(socialSecurities.size()<=0){
                    addsocialSecurityList.add(newSocialSecurity);
                }
            }
        }

        if(addsocialSecurityList.size()>0){
            Db.batchSave(addsocialSecurityList,addsocialSecurityList.size());
        }
        renderJson(addsocialSecurityList.size());
    }

    public void socialSecuritySave(){
        String empId=getPara("empId");
        String yearMonth=getPara("yearMonth");
        String companyId=getPara("companyId");

        PersOrgEmployeeSocialSecurity socialSecurityMaxMonth=persOrgEmployeeSocialSecurityService.findEmployeeSocialSecurityMaxMonth(empId);

        PersOrgEmployee employee=persOrgEmployeeService.findById(empId);

        List<String> deptIds=new ArrayList<>();

        String empDeptId=null;
        List<String> empDeptIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='dept' and emp_id=? ",employee.getId());
        for (String e : empDeptIds) {
            if(deptIds.contains(empDeptId)){
                empDeptId =e;
                break;
            }
        }
        if(empDeptId==null){
            empDeptId=empDeptIds.get(0);
        }
        String empPositionId=null;
        List<String> empPositionIds= Db.query("select relationship_id from pers_org_employee_rel where relationship_type='position' and emp_id=? ",employee.getId());
        for (String e : empPositionIds) {
            PersPosition persPosition=persPositionService.findById(e);
            if(empDeptId.equals(persPosition.getOrgId())){
                empPositionId=e;
                break;
            }
        }
        PersOrgEmployeeSocialSecurity socialSecurity=new PersOrgEmployeeSocialSecurity();
        socialSecurity.setId(IdGen.getUUID());
        socialSecurity.setApplyRecordId(null);
        socialSecurity.setIdcard(employee.getIdCard());
        socialSecurity.setDeptId(empDeptId);
        socialSecurity.setPositionId(empPositionId);
        socialSecurity.setEmpId(empId);
        socialSecurity.setType("2");
        socialSecurity.setIdcardAddr(employee.getIdCardAddr());
        socialSecurity.setCompanyId(companyId);
        socialSecurity.setIsFirst("0");
        socialSecurity.setYearMonth(yearMonth);
        socialSecurity.setPhoneNum(employee.getPhoneNum());
        socialSecurity.setDelFlag("0");
        socialSecurity.setCreateBy(null);
        socialSecurity.setCreateDate(new Date());
        socialSecurity.setUpdateBy(null);
        socialSecurity.setUpdateDate(new Date());

        if(socialSecurityMaxMonth.getYearMonth().equals(socialSecurity.getYearMonth())){
            renderCodeFailed("已存在");
            return;
        }

        if(socialSecurity.save()){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed();
        }

    }

}
