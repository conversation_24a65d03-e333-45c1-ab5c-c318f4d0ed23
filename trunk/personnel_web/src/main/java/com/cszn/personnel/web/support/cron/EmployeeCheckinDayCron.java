package com.cszn.personnel.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.api.weixin.QiYeWeiXinService;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.entity.weixin.QiYeUser;
import com.cszn.integrated.service.provider.pers.PersOrgEmployeeLeaveRestApplyServiceImpl;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Cron("20 3 * * *")
public class EmployeeCheckinDayCron implements Runnable{

    @Inject
    private QiYeWeiXinService qiYeWeiXinService;
    @Inject
    private PersOrgEmployeeCheckinMonthSummaryService persOrgEmployeeCheckinMonthSummaryService;
    @Inject
    private PersOrgEmployeeCheckinExceptionService persOrgEmployeeCheckinExceptionService;
    @Inject
    private PersOrgEmployeeCheckinItemsService persOrgEmployeeCheckinItemsService;
    @Inject
    private PersOrgEmployeeCheckinDaySummaryService persOrgEmployeeCheckinDaySummaryService;
    @Inject
    private PersOrgEmployeeLeaveRestApplyService persOrgEmployeeLeaveRestApplyService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private PersOrgEmployeeDispatchApplyService persOrgEmployeeDispatchApplyService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersOrgEmployeeOverTimeApplyService persOrgEmployeeOverTimeApplyService;
    @Inject
    private PersOrgEmployeeLeaveBalanceService persOrgEmployeeLeaveBalanceService;
    @Inject
    private PersUserOrgService persUserOrgService;

    Logger logger= LoggerFactory.getLogger(EmployeeCheckinDayCron.class);


    @Override
    public void run(){


        try {
            //persOrgEmployeeLeaveRestApplyService.findAllHalfLeaveRestApply();
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            dispatchApplyEnd();
        }catch (Exception e){
            logger.info("修改外派部门单异常",e);
            e.printStackTrace();
        }

        try {
            //入职2年及以上年假设置
            if("01-01".equals(DateUtils.formatDate(new Date(),"MM-dd"))){
                String sql="select * from pers_org_employee where del_flag='0' and archive_status='incumbency' and ((DATE_FORMAT(now(),'%Y'))-DATE_FORMAT(entry_time,'%Y'))>1" +
                        " and ((DATE_FORMAT(now(),'%Y'))-DATE_FORMAT(entry_time,'%Y'))<10 and employee_type='full_time' ";
                List<Record> recordList=Db.find(sql);
                if(recordList.size()>0){
                    for(Record record:recordList){
                        //Db.update("update pers_org_employee set annual_leave_days='5',update_date=now() " +"where id=?",record.getStr("id"));
                        //重置5天年假
                        persOrgEmployeeLeaveBalanceService.resetEmpYearLeave(record.getStr("id"),5);
                    }
                }

                String sql2="select * from pers_org_employee where del_flag='0' and archive_status='incumbency' and ((DATE_FORMAT(now(),'%Y'))-DATE_FORMAT(entry_time,'%Y'))>=10 " +
                        "and employee_type='full_time'" +
                        "  ";
                List<Record> recordList2=Db.find(sql2);
                if(recordList2.size()>0){
                    for(Record record:recordList2){
                        //Db.update("update pers_org_employee set annual_leave_days='10',update_date=now() " +"where id=?",record.getStr("id"));
                        //重置10天年假
                        persOrgEmployeeLeaveBalanceService.resetEmpYearLeave(record.getStr("id"),10);
                    }
                }

            }
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            Date yesterdayDate=DateUtils.getNextDay(new Date(),-1);
            //处理提前申请的加班余假发放
            List<PersOrgEmployeeOverTimeApply> empOverTimeNotAddLeaveBalanceList = persOrgEmployeeOverTimeApplyService.findEmpOverTimeNotAddLeaveBalanceList(yesterdayDate, yesterdayDate);
            for (PersOrgEmployeeOverTimeApply overTimeApply : empOverTimeNotAddLeaveBalanceList) {
                persOrgEmployeeLeaveBalanceService.addLeaveBalance(overTimeApply);
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        try {
            //处理组织机构添加子集权限问题
            persUserOrgService.autoAddOrgChildrenRecord();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {

    }

    public void getCheckinDayData(int pageNumber,int pageSize){
        String sql="  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
        List<Record> recordList = Db.find("select id,qiye_userid "+sql);
        if(recordList.size()==0){
            return;
        }

        Date summaryDate=DateUtils.parseDate(DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd")+" 00:00:00");
        for(Record record:recordList){
            try {
                persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(record.getStr("id"),summaryDate);
            }catch (Exception e){
                logger.info("生成打卡日报异常");
                logger.info(record.getStr("id"));
                logger.info( "failed!", e.fillInStackTrace());
            }

        }
    }

    public void dispatchApplyEnd(){

        String dispatchApplySql="select a.id,a.emp_id,b.dispatch_dept_id,b.start_time,b.end_time from pers_org_employee_dispatch_apply a " +
                " inner join pers_org_employee_dispatch_apply_date b on a.id=b.apply_id  where a.del_flag='0' and (TO_DAYS(?)-TO_DAYS(b.end_time)) BETWEEN -999999 and 7 and a.`status`='3' " +
                " UNION " +
                " select e.id,e.emp_id,null,null,null from pers_org_employee_dispatch_apply e where e. `status`='5' and (TO_DAYS(?)-TO_DAYS(e.update_date)) BETWEEN 0 and 2  ";

        /*String dispatchApplySql="select id,id as emp_id from pers_org_employee where del_flag='0' and archive_status='incumbency' ";*/

        List<PersOrgEmployeeDispatchApply> dispatchApplyList=persOrgEmployeeDispatchApplyService.findBySql(dispatchApplySql
                ,new Object[]{DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd"),DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd")});

        /*List<PersOrgEmployeeDispatchApply> dispatchApplyList=persOrgEmployeeDispatchApplyService.findBySql(dispatchApplySql
                ,new Object[]{});*/

        String sql="select b.qiye_id from pers_org_employee_rel a left join pers_org b on a.relationship_id=b.id where a.emp_id=? and a.relationship_type='dept' ";
        for(PersOrgEmployeeDispatchApply dispatchApply:dispatchApplyList){
            Record apply=Db.findFirst("select a.emp_id,b.dispatch_dept_id,b.start_time,b.end_time from pers_org_employee_dispatch_apply a " +
                    " inner join pers_org_employee_dispatch_apply_date b on a.id=b.apply_id  where a.del_flag='0' and ? BETWEEN b.start_time and b.end_time " +
                    "and a.`status` in ('2','3') and a.emp_id=? ",new Date(),dispatchApply.getEmpId());

            /*if(apply!=null){
                continue;
            }*/
            if("c1afc6ce-d535-11ea-9d15-00163e08de15".equalsIgnoreCase(dispatchApply.getEmpId())){
                continue;
            }

            List<Integer> orgQiyeIds=Db.query(sql,dispatchApply.getEmpId());
            for(Integer qiyeId:orgQiyeIds){
                if(qiyeId==null){
                    orgQiyeIds.remove(qiyeId);
                }
            }
            if(apply!=null){
                PersOrg dispatchOrg=persOrgService.findById(apply.getStr("dispatch_dept_id"));
                if(dispatchOrg!=null && dispatchOrg.getQiyeId()!=null){
                    orgQiyeIds.add(dispatchOrg.getQiyeId());
                }
            }
            PersOrgEmployee orgEmployee=persOrgEmployeeService.findById(dispatchApply.getEmpId());

            int[] qiyeIdArray=new int[orgQiyeIds.size()];
            for(int i=0;i<orgQiyeIds.size();i++){
                qiyeIdArray[i]=orgQiyeIds.get(i);
            }

            if(qiyeIdArray.length>0) {
                //更新企业微信账号
                QiYeUser qiYeUser = new QiYeUser();
                qiYeUser.setUserId(orgEmployee.getQiyeUserid());
                qiYeUser.setDepartment(qiyeIdArray);
                qiYeUser.setName(orgEmployee.getFullName());
                if("male".equals(orgEmployee.getSex())){
                    qiYeUser.setGender("1");
                }else if("female".equals(orgEmployee.getSex())){
                    qiYeUser.setGender("2");
                }
                qiYeUser.setMobile(orgEmployee.getPhoneNum());
                if(StrKit.notBlank(orgEmployee.getEmailAddr())){
                    qiYeUser.setEmail(orgEmployee.getEmailAddr());
                }


                boolean suc=qiYeWeiXinService.updateUser(qiYeUser);
                logger.info(orgEmployee.getFullName()+"外派单结束"+dispatchApply.getId()+" 更新企业微信部门参数"+JSON.toJSONString(qiYeUser));
                if(suc){

                }else{
                    logger.info(orgEmployee.getFullName()+"外派单结束"+dispatchApply.getId()+" 更新企业微信部门异常");
                }
            }else{
                logger.info(orgEmployee.getFullName()+"外派单结束"+dispatchApply.getId()+" 无企业微信部门");
            }
        }


        /*String dispatchApplySql2="select a.emp_id,b.dispatch_dept_id from pers_org_employee_dispatch_apply a " +
                "left join pers_org_employee_dispatch_apply_date b on a.id=b.apply_id where a.del_flag='0' " +
                "and TO_DAYS(?) BETWEEN TO_DAYS(b.start_time) and TO_DAYS(b.end_time) and a.`status` in ('2','3')  ";


        List<PersOrgEmployeeDispatchApply> dispatchApplyList2=persOrgEmployeeDispatchApplyService.findBySql(dispatchApplySql2
                ,new Object[]{DateUtils.formatDate(new Date(),"yyyy-MM-dd")});
        String sql2="select b.qiye_id from pers_org_employee_rel a left join pers_org b on a.relationship_id=b.id where a.emp_id=? and a.relationship_type='dept' ";
        for(PersOrgEmployeeDispatchApply dispatchApply:dispatchApplyList2){

            List<Integer> orgQiyeIds=Db.query(sql,dispatchApply.getEmpId());
            for(Integer qiyeId:orgQiyeIds){
                if(qiyeId==null){
                    orgQiyeIds.remove(qiyeId);
                }
            }
            PersOrg persOrg=persOrgService.findById(dispatchApply.getDispatchDeptId());
            orgQiyeIds.add(persOrg.getQiyeId());
            PersOrgEmployee orgEmployee=persOrgEmployeeService.findById(dispatchApply.getEmpId());

            int[] qiyeIdArray=new int[orgQiyeIds.size()];
            for(int i=0;i<orgQiyeIds.size();i++){
                qiyeIdArray[i]=orgQiyeIds.get(i);
            }

            if(qiyeIdArray.length>0) {
                //更新企业微信账号
                QiYeUser qiYeUser = new QiYeUser();
                qiYeUser.setUserId(orgEmployee.getQiyeUserid());
                qiYeUser.setDepartment(qiyeIdArray);
                qiYeUser.setName(orgEmployee.getFullName());
                if("male".equals(orgEmployee.getSex())){
                    qiYeUser.setGender("1");
                }else if("female".equals(orgEmployee.getSex())){
                    qiYeUser.setGender("2");
                }
                qiYeUser.setMobile(orgEmployee.getPhoneNum());
                if(StrKit.notBlank(orgEmployee.getEmailAddr())){
                    qiYeUser.setEmail(orgEmployee.getEmailAddr());
                }
                boolean suc=qiYeWeiXinService.updateUser(qiYeUser);

                if(suc){

                }else{
                }
            }else{
            }
        }*/

    }

    public void leaveRest(int pageNumber,int pageSize){
        String sql="  from pers_org_employee where del_flag='0' and (archive_status='incumbency' or (archive_status='quit' and (to_days(now())-to_days(quit_time))<6)) and qiye_userid is not null ";
        Page<Record> recordPage = Db.paginate(pageNumber,pageSize,"select id,qiye_userid ",sql);
        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<Date> dateList=new ArrayList<>();
        dateList.add(DateUtils.getNextDay(new Date(),-2));
        dateList.add(DateUtils.getNextDay(new Date(),-1));
        for(Date date:dateList){
            for(Record record:recordPage.getList()){
                List<PersOrgEmployeeLeaveRestApply> leaveRestApplyList=persOrgEmployeeLeaveRestApplyService
                        .getEmpLeaveRestApplyList(record.getStr("id"),DateUtils.formatDate(date,"yyyy-MM-dd"));
                if(leaveRestApplyList==null || leaveRestApplyList.size()<1){
                    continue;
                }
                boolean isMorningLeaveRest=false;
                boolean isAfternoonLeaveRest=false;
                for(PersOrgEmployeeLeaveRestApply leaveRestApply:leaveRestApplyList){
                    String[] array= PersOrgEmployeeLeaveRestApplyServiceImpl.getLeaveRestApplyInfo(leaveRestApply,date);
                    if("1".equals(array[0])){
                        isMorningLeaveRest=true;
                        isAfternoonLeaveRest=true;
                        break;
                    }else{
                        if("3".equals(array[1])){
                            isMorningLeaveRest=true;
                        }else if("4".equals(array[1])){
                            isAfternoonLeaveRest=true;
                        }
                    }
                    if(isMorningLeaveRest && isAfternoonLeaveRest){
                        
                    }

                }
            }
        }
        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            leaveRest(pageNumber,pageSize);
        }

    }

}
