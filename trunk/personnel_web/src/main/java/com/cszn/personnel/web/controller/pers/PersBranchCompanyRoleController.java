package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.entity.enums.PersPerformance;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import io.jboot.web.controller.annotation.RequestMapping;

import java.io.FileInputStream;
import java.sql.SQLException;
import java.util.*;

@RequestMapping(value="/pers/branchCompanyRole", viewPath="/modules_page/pers/branchCompanyRole/")
public class PersBranchCompanyRoleController extends BaseController {

    @Inject
    private PersBranchCompanyRoleService persBranchCompanyRoleService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersBranchCompanyRoleSalaryConfigService persBranchCompanyRoleSalaryConfigService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private PersOrgEmployeeSalaryService persOrgEmployeeSalaryService;
    @Inject
    private PersOrgEmployeePerformanceService persOrgEmployeePerformanceService;

    public void index(){

        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        String orgId=getPara("orgId");
        if(StrKit.notBlank(id)){
            PersBranchCompanyRole branchCompanyRole=persBranchCompanyRoleService.findById(id);
            setAttr("model",branchCompanyRole);
        }
        setAttr("orgId",orgId);
        setAttr("org",persOrgService.findById(orgId));
        render("form.html");
    }

    public void findListPage(){
        PersBranchCompanyRole branchCompanyRole=getBean(PersBranchCompanyRole.class,"",true);

        Page<PersBranchCompanyRole> page=persBranchCompanyRoleService.pageList(getParaToInt("page"),getParaToInt("limit"),branchCompanyRole);
        renderJson(new DataTable<PersBranchCompanyRole>(page));
    }

    public void save(){
        PersBranchCompanyRole branchCompanyRole=getBean(PersBranchCompanyRole.class,"",true);
        boolean flag=persBranchCompanyRoleService.saveBranchCompanyRole(branchCompanyRole, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void salaryConfigIndex(){
        String roleId=getPara("roleId");

        List<Record> recordList=new ArrayList<>();

        List<PersBranchCompanyRoleSalaryConfig> configList=persBranchCompanyRoleSalaryConfigService.findListByRoleId(roleId);
        for (PersPerformance persPerformance : PersPerformance.getSalaryList()) {
            Record record=new Record();
            record.set("code",persPerformance.getCode());
            record.set("name",persPerformance.getName());
            record.set("field", persPerformance.getField());
            for (PersBranchCompanyRoleSalaryConfig config : configList) {
                if(persPerformance.getField().equals(config.getFieldName())){
                    record.set("value",config.getCalculateConfig());
                    record.set("remark",config.getRemark());
                    break;
                }
            }
            recordList.add(record);
        }
        setAttr("recordList",recordList);
        setAttr("roleId",roleId);
        render("salaryConfigIndex.html");
    }


    public void saveConfig(){
        Integer count=getParaToInt("count");
        String roleId=getPara("roleId");
        List<PersBranchCompanyRoleSalaryConfig> configList=new ArrayList<>();

        for(int i=1;i<=count;i++){
            PersBranchCompanyRoleSalaryConfig config=getBean(PersBranchCompanyRoleSalaryConfig.class,"record["+i+"]",true);
            configList.add(config);
        }

        boolean flag=persBranchCompanyRoleSalaryConfigService.saveConfig(configList,roleId, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void importPerformance(){
        String orgId=getPara("orgId");
        setAttr("org",persOrgService.findById(orgId));

        render("import.html");
    }

    public void importFile() throws Exception{
        UploadFile uploadFile =getFile("file");
        String orgId=getPara("orgId");
        String yearMonth=getPara("yearMonth");
        System.out.println(orgId);
        System.out.println(yearMonth);
        List<ZTree> zTreeList=persOrgService.orgFormTree(orgId);


        List<ZTree> returnZtree=new ArrayList<>();

        Set<String> idSet=new TreeSet<>();
        for(ZTree zTree:zTreeList){
            for(ZTree z:zTreeList){
                if(z.getpId().equalsIgnoreCase(zTree.getId())){
                    idSet.add(z.getId());
                    if(zTree.getChildren()==null){
                        List<ZTree> childrenZtreeList=new ArrayList<>();
                        childrenZtreeList.add(z);
                        zTree.setChildren(childrenZtreeList);
                    }else{
                        zTree.getChildren().add(z);
                    }
                }
            }
        }
        for(ZTree zTree:zTreeList){
            if(!idSet.contains(zTree.getId())){
                returnZtree.add(zTree);
            }
        }


        String[] titles=new String[]{"工号","姓名","个人业绩达标额","个人合同款","个人充值款","团队业绩达标额","业绩奖","业务提成","销售冠军奖","扣款额"};
        List<Map> dataList=ImportExcelKit.getExcelData(new FileInputStream(uploadFile.getFile()),uploadFile.getFileName(),titles);
        PersPerformance.values();


        List<PersOrgEmployeePerformance> performanceList=new ArrayList<>();

        List<PersOrgEmployeePerformance> updatePerformanceList=new ArrayList<>();
        for (Map<String,String> map : dataList) {
            String workNum=(String)map.get("工号");
            String fullName=(String)map.get("姓名");

            Double individualPerformanceTarget=map.get("个人业绩达标额")==null?0.0:Double.valueOf(map.get("个人业绩达标额"));
            Double individualContractAmount=map.get("个人合同款")==null?0.0:Double.valueOf(map.get("个人合同款"));
            Double individualRechargeAmount=map.get("个人充值款")==null?0.0:Double.valueOf(map.get("个人充值款"));
            Double teamPerformanceTarget=map.get("团队业绩达标额")==null?0.0:Double.valueOf(map.get("团队业绩达标额"));
            Double teamPerformanceAmount=map.get("团队业绩额")==null?0.0:Double.valueOf(map.get("团队业绩额"));
            String isHalvePushSalary="0";
            if("是".equals(map.get("是否减半业务提成"))){
                isHalvePushSalary="1";
            }
            PersOrgEmployee employee =persOrgEmployeeService.getEmployeeByWorkNumName(workNum,fullName);
            if(employee==null){
                renderJson(Ret.fail("msg",workNum+" "+fullName+"该记录未查询到员工档案"));
                return;
            }
            PersOrgEmployeePerformance performance=new PersOrgEmployeePerformance();
            performance.setId(IdGen.getUUID());
            performance.setEmpId(employee.getId());
            performance.setYearMonth(yearMonth);
            performance.setDeptId(employee.getOrg().getStr("deptId"));
            performance.setPositionId(employee.getOrg().getStr("positionId"));
            performance.setYearMonth(yearMonth);
            performance.setIndividualPerformanceTarget(individualPerformanceTarget);
            performance.setIndividualRechargeAmount(individualRechargeAmount);
            performance.setIndividualContractAmount(individualContractAmount);
            performance.setTeamPerformanceTarget(teamPerformanceTarget);
            performance.setIsHalvePushSalary(isHalvePushSalary);
            performance.setTeamPerformanceAmount(0.0);
            performance.setUnderlingPerformanceTotalAmount(0.0);
            //判断个人业绩是否达标
            if((performance.getIndividualRechargeAmount()+performance.getIndividualContractAmount())>=performance.getIndividualPerformanceTarget()){
                performance.setIsSelfIndividualPerformancePass("1");
            }else{
                performance.setIsSelfIndividualPerformancePass("0");
            }



            performance.setDelFlag("0");
            performance.setCreateBy(AuthUtils.getUserId());
            performance.setCreateDate(new Date());
            performance.setUpdateBy(AuthUtils.getUserId());
            performance.setUpdateDate(new Date());
            performanceList.add(performance);
            //计算业绩
            PersOrgEmployeePerformance oldPerformance=persOrgEmployeePerformanceService.findEmpYearMonthPerformance(employee.getId(),yearMonth);
            if(oldPerformance!=null){
                updatePerformanceList.add(oldPerformance);
            }
        }

        Map<String,Set<String>> stringSetMap=new HashMap<>();

        //计算团队业绩
        persBranchCompanyRoleService.calculationTeamPerformance(returnZtree,yearMonth,performanceList,stringSetMap);
        //计算下属个人业绩总和
        persBranchCompanyRoleService.calculationUnderlingPerformanceTotalAmount(returnZtree,performanceList);


        Map<String,Double> empTeamAmountMap=new HashMap<>();

        for(String key:stringSetMap.keySet()){

            //需要添加自己的
            Set<String> stringSet=stringSetMap.get(key);

            List<String> empIds=new ArrayList<>();

            Double totalTeamAmount=0.0;
            for (Object str : stringSet.toArray()) {
                String empId=(String)str;
                empIds.add(empId);
            }
            //empIds.add(key);

            for (String empId : empIds) {
                for (PersOrgEmployeePerformance performance : performanceList) {
                    if(empId.equals(performance.getEmpId())){
                        totalTeamAmount+=(performance.getIndividualContractAmount()+performance.getIndividualRechargeAmount());
                        break;
                    }
                }
            }

            if(empTeamAmountMap.containsKey(key)){
                empTeamAmountMap.put(key,empTeamAmountMap.get(key)+totalTeamAmount);
            }else{
                empTeamAmountMap.put(key,totalTeamAmount);
            }


        }

        for(String key:empTeamAmountMap.keySet()){
            for (PersOrgEmployeePerformance performance : performanceList) {
                if(key.contains(performance.getEmpId())){
                    performance.setTeamPerformanceAmount(empTeamAmountMap.get(key));
                    break;
                }
            }
        }

        for (PersOrgEmployeePerformance persOrgEmployeePerformance : updatePerformanceList) {
            persOrgEmployeePerformance.setDelFlag("1");
            persOrgEmployeePerformance.setUpdateBy(AuthUtils.getUserId());
            persOrgEmployeePerformance.setUpdateDate(new Date());
        }


        //计算工资
        List<PersOrgEmployeeSalary> employeeSalaryList=persOrgEmployeeSalaryService.genEmployeeSalary(performanceList,AuthUtils.getUserId());
        List<PersOrgEmployeeSalary> updateEmployeeSalaryList=new ArrayList<>();
        for (PersOrgEmployeeSalary salary : employeeSalaryList) {
            PersOrgEmployeeSalary oldSalary=persOrgEmployeeSalaryService.getEmpSalaryByYearMonth(salary.getEmpId(),salary.getYearMonth());
            if(oldSalary!=null){
                oldSalary.setDelFlag("1");
                oldSalary.setUpdateBy(AuthUtils.getUserId());
                oldSalary.setUpdateDate(new Date());
                updateEmployeeSalaryList.add(oldSalary);
            }

        }

        boolean flag= Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                int[] nums=Db.batchSave(performanceList,performanceList.size());
                for (int num : nums) {
                    if(num<1){
                        return false;
                    }
                }
                int[] nums2=Db.batchSave(employeeSalaryList,employeeSalaryList.size());
                for (int num : nums2) {
                    if(num<1){
                        return false;
                    }
                }
                if(updateEmployeeSalaryList.size()>0){
                    Db.batchUpdate(updateEmployeeSalaryList,updateEmployeeSalaryList.size());
                }
                if(updatePerformanceList.size()>0){
                    Db.batchUpdate(updatePerformanceList,updatePerformanceList.size());
                }
                return true;
            }
        });

        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void test(){



    }

    public static void main(String[] args) throws Exception {
        ExpressRunner runner = new ExpressRunner();
        DefaultContext<String, Object> context = new DefaultContext<String, Object>();
        context.put("I",1000);
        context.put("J",2000);
        context.put("H",3000);
        /*String express = "(b-a)*0.2+(c-a)*0.2/2";
        Object r = runner.execute(express, context, null, true, false);
        System.out.println(r);*/

        String express = "if(I+J>=H){return 1000;}else{return 0;}";
        Object string=runner.execute(express, context, null, false, false, null);
        System.out.println(string);
    }
}
