package com.cszn.personnel.web.support.cron;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCheckinDaySummaryService;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinDaySummary;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Cron("0 5,13,19 * * *")
public class EmployeeCheckinDaySummaryGenCron  implements Runnable{


    @Inject
    PersOrgEmployeeCheckinDaySummaryService persOrgEmployeeCheckinDaySummaryService;
    @Inject
    EmployeeDelayCheckinCron employeeDelayCheckinCron;

    Logger logger= LoggerFactory.getLogger(EmployeeCheckinDaySummaryGenCron.class);


    @Override
    public void run() {
        try {
            //生成考勤日报
            logger.info("生成考勤日报");
            getCheckinDayData();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void getCheckinDayData(){
        String genDateStr=DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        String sql="  select id from pers_org_employee where del_flag='0' and archive_status='incumbency' and entry_time<=? " +
                " UNION " +
                " select id from pers_org_employee where del_flag='0' and archive_status='quit' and quit_time BETWEEN ? and ? ";
        List<Record> recordList = Db.find(sql,genDateStr+" 23:59:59",genDateStr+" 00:00:00",genDateStr+" 23:59:59");
        if(recordList.size()==0){
            return;
        }

        Date summaryDate= DateUtils.parseDate(genDateStr+" 00:00:00");
        Date nowDate=new Date();
        for(Record record:recordList){
            try {
                //persOrgEmployeeCheckinDaySummaryService.genEmployeeCheckinDaySummary(record.getStr("id"),summaryDate);
                PersOrgEmployeeCheckinDaySummary daySummary = persOrgEmployeeCheckinDaySummaryService.getEmpDaySummary(record.getStr("id"), summaryDate);
                if(daySummary!=null){
                    continue;
                }
                //employeeDelayCheckinCron
                Map<String,Object> map=employeeDelayCheckinCron.getEmpOverTime(record.getStr("id"),summaryDate,new String[]{"3"});
                Date todayMaxCheckinTime = (Date) map.get("todayMaxCheckinTime");
                if(nowDate.getTime()>=todayMaxCheckinTime.getTime()){
                    employeeDelayCheckinCron.employeeCheckinDaySummary(map);
                }
            }catch (Exception e){
                logger.info(record.getStr("id")+"生成打卡日报异常");
                logger.info( "failed!", e.fillInStackTrace());
            }

        }
    }

}
