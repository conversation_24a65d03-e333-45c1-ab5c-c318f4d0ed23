package com.cszn.personnel.web.controller.sys;


import com.cszn.integrated.base.utils.FileUploadKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FileDealService;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.kit.Ret;
import com.jfinal.upload.UploadFile;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @Description 文件处理controller
 * <AUTHOR>
 * @Date 2019/4/12
 **/
@RequestMapping(value = "/sys/upload",viewPath = "")
public class FileDealController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    FileDealService fileDealService;


    /**
     * 单文件上传
     */
    public void index() {
        UploadFile file = getFile();
        String folder = getPara("folder");// 上传的文件夹
        Map<String, Object> data = FileUploadKit.uploadFile(file, folder);
        renderJson(Ret.ok("msg","上传成功！").set("data",data));
    }


    /**
     * 多文件上传
     */
    public void multiDocumentUpload(){
        try {
            List<UploadFile> files = getFiles();
            String folder = getPara("folder");
            List<Map<String,Object>> list = Lists.newArrayList();
            for (UploadFile file : files) {
                Map<String, Object> data = FileUploadKit.uploadFile(file, folder);
                list.add(data);
            }
            renderJson(Ret.ok("msg","上传成功！").put("data",list));
        } catch (Exception e) {
            logger.info("多文件上传失败：[{}]",e.getMessage());
            renderJson(Ret.fail("msg","上传失败！"));
        }
    }


    /**
     * excel上传
     */
    public void excelUpload(){

        UploadFile uploadFile = getFile();
        if(uploadFile == null){
           renderJson(Ret.fail("msg","文件不存在"));
           return;
        }
        boolean flag = false;
        try {
            flag = fileDealService.dealExcel(uploadFile.getFile(),uploadFile.getFileName(), AuthUtils.getUserId());
        } catch (Exception e) {
            //e.printStackTrace();
            logger.error("会员卡导入异常：" + e.getMessage(),e);
        }
        if(flag){
           renderJson(Ret.ok("msg","导入成功"));
        }else{
            renderJson(Ret.fail("msg","导入失败"));
        }
    }


    /**
     * excel下载
     */
    public void excelExport(){
        String excelTemplatePath = Global.excelTemplatePath;

        File file = new File(excelTemplatePath);
        String fileName = "会员信息导入模板.xls";

        try {
            HSSFWorkbook wb = new HSSFWorkbook(new FileInputStream(new File(excelTemplatePath)));
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 文件下载
     */
    public void downFile(){
        String filePath = getPara("filePath");
        String downPath = Global.uploadPath;
        System.out.println(downPath + filePath);
        File file = new File(downPath + filePath);
        renderFile(file);
    }
}
