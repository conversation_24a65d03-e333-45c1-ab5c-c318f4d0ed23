package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeContractService;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value="/employeeContract", viewPath="/modules_page/pers/contract")
public class PersOrgEmployeeContractController extends BaseController {

    @Inject
    PersOrgEmployeeContractService persOrgEmployeeContractService;

    public void index(){
        render("contractIndex.html");
    }

    public void pageList(){
        String deptId=getPara("deptId");
        String name=getPara("name");
        String archiveStatus=getPara("archiveStatus");
        String contractType=getPara("contractType");
        String contractStatus=getPara("contractStatus");
        if(StrKit.isBlank(archiveStatus)){
            archiveStatus="incumbency";
        }
        Page<Record> recordPage=persOrgEmployeeContractService.employeeContractPageList(getParaToInt("page"),getParaToInt("limit"),deptId,name,archiveStatus,contractType, contractStatus, AuthUtils.getUserId());

        renderJson(new DataTable<Record>(recordPage));
    }



}
