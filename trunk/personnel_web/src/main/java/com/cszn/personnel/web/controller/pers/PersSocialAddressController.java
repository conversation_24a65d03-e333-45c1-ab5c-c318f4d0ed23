package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersSocialAddressService;
import com.cszn.integrated.service.entity.pers.PersSocialAddress;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value="/pers/socialAddress", viewPath="/modules_page/pers/socialAddress")
public class PersSocialAddressController extends BaseController {

    @Inject
    PersSocialAddressService persSocialAddressService;

    public void index(){
        render("index.html");
    }

    public void pageList(){
        PersSocialAddress socialAddress=getBean(PersSocialAddress.class,"",true);
        Page<PersSocialAddress> pageList=persSocialAddressService
                .findSocialAddressPageList(getParaToInt("page"),getParaToInt("limit"),socialAddress);
        renderJson(new DataTable<PersSocialAddress>(pageList));
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            PersSocialAddress socialAddress=persSocialAddressService.findById(id);

            setAttr("model",socialAddress);
        }
        render("form.html");
    }

    public void saveSocialAddress(){
        PersSocialAddress socialAddress=getBean(PersSocialAddress.class,"",true);
        if(persSocialAddressService.saveSocialAddress(socialAddress, AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

}
