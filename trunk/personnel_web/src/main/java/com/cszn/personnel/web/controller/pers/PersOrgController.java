/**
 * 
 */
package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.main.MainOrgCategoryService;
import com.cszn.integrated.service.api.main.MainSyncRecordService;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.api.weixin.QiYeWeiXinService;
import com.cszn.integrated.service.entity.enums.PersOrgCateGoryType;
import com.cszn.integrated.service.entity.enums.PersTaskType;
import com.cszn.integrated.service.entity.enums.SyncType;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgHeadcountApply;
import com.cszn.integrated.service.entity.pers.PersOrgReviewer;
import com.cszn.integrated.service.entity.pers.PersTask;
import com.cszn.integrated.service.entity.status.OrgType;
import com.cszn.integrated.service.entity.status.SyncDataType;
import com.cszn.integrated.service.entity.status.UserType;
import com.cszn.integrated.service.entity.sys.Org;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.integrated.service.entity.weixin.Department;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

import java.util.*;

/**
 * Created by LiangHuiLing on 2019年7月2日
 *
 * OrgController
 */
@RequestMapping(value="/persOrg", viewPath="/modules_page/pers/org")
public class PersOrgController extends BaseController {

	@Inject
    private PersOrgService persOrgService;
	@Inject
	private PersOrgEmployeeService persOrgEmployeeService;
	@Inject
	private PersOrgReviewerService persOrgReviewerService;
	@Inject
    private MainOrgCategoryService mainOrgCategoryService;
	@Inject
	private QiYeWeiXinService qiYeWeiXinService;
	@Inject
	private UserService userService;
	@Inject
	private MainSyncRecordService mainSyncRecordService;
	@Inject
	private MainBaseService mainBaseService;
	@Inject
	private MainBranchOfficeService mainBranchOfficeService;
	@Inject
	PersTaskService persTaskService;
	@Inject
	PersApprovalService persApprovalService;
	
    public void index() {
        render("orgIndex.html");
    }
    
    public void manageIndex() {
    	render("manageIndex.html");
    }
    
    public void reviewerIndex() {
    	final String orgId = getPara("id");
    	setAttr("orgId", orgId);
    	setAttr("empUserList", persOrgEmployeeService.empUserList(null));
    	render("reviewerIndex.html");
    }
    
	/**
	 * 组织审核人分页
	 */
	@Clear(LogInterceptor.class)
	public void reviewerPage() {
		PersOrgReviewer model = getBean(PersOrgReviewer.class, "", true);
		Page<PersOrgReviewer> modelPage = persOrgReviewerService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
		renderJson(new DataTable<PersOrgReviewer>(modelPage));
	}
    
    /**
     * 树表格方法
     */
	@Clear(LogInterceptor.class)
    public void orgTreeGrid() {
		String status=getPara("status");
		if(StrKit.isBlank(status)){
			status="1";
		}
		Subject subject = SecurityUtils.getSubject();
		String orgId=null;
		//如果没有查看所有组织机构的权限，则只能查看自己组织架构下的
		/*if(!subject.isPermitted("org:check:allOrg")){

			PersOrgEmployee employee =persOrgEmployeeService.getEmployeeByAccountId(AuthUtils.getUserId());
			if(employee==null){
				renderJson(new DataTable<ZTree>());
				return;
			}
			orgId = employee.getOrgId();
		}*/

		String sql="select * from pers_org where del_flag='0'  ";
		List<Object> params=new ArrayList<>();
		if(StrKit.notBlank(status) && "1".equals(status)){
			sql+=" and is_enable=? ";
			params.add(status);
		}
		sql+=" order by org_sort asc ";
		List<PersOrg> orgList = persOrgService.findBySql(sql,params);
		List<ZTree> zTreeList=new ArrayList<>();

		List<String> countParams=new ArrayList<>();
		String str="";
		for (PersOrg org : orgList) {
			ZTree zTree = new ZTree(org.getId(), org.getOrgName(), org.getParentId(), org.getIsEnable(), null, null, org.getOrgSort());
			Record record=new Record();
			record.set("isRestrict",org.getIsRestrict());
			record.set("headcount",org.getHeadcount());
			record.set("isBU",org.getIsBu());
			record.set("qiyeChatid",org.getQiyeChatid());

			zTree.setRecord(record);
			zTreeList.add(zTree);

			str+="?,";
			countParams.add(org.getId());
		}
		str=str.substring(0,str.length()-1);

		List<Record> recordList=Db.find("select count(a.id) as count,b.relationship_id from pers_org_employee a inner join pers_org_employee_rel b on a.id=b.emp_id and relationship_type='dept' and is_main='1' " +
				" where a.del_flag='0' and a.archive_status='incumbency' and b.relationship_id in("+str+") GROUP BY b.relationship_id ",countParams.toArray());
		Map<String,Integer> countMap=new HashMap<>();
		for (Record record : recordList) {
			countMap.put(record.getStr("relationship_id"),record.getInt("count"));
		}
		for (ZTree zTree : zTreeList) {
			Integer count = countMap.get(zTree.getId());
			if(count==null){
				count=0;
			}
			zTree.getRecord().set("staffCount",count);
		}

        renderJson(new DataTable<ZTree>(zTreeList));
    }

	@Clear(LogInterceptor.class)
	public void orgTreeSelect() {

		List<ZTree> orgFormTree = persOrgService.organizationZTree();

		List<ZTree> returnZtree=new ArrayList<>();
		for(ZTree zTree:orgFormTree){
			if("00000000-0000-0000-0000-000000000000".equalsIgnoreCase(zTree.getpId())){
				returnZtree.add(zTree);
			}
			for(ZTree z:orgFormTree){
				if(z.getpId().equalsIgnoreCase(zTree.getId())){
					if(zTree.getChildren()==null){
						List<ZTree> childrenZtreeList=new ArrayList<>();
						childrenZtreeList.add(z);
						zTree.setChildren(childrenZtreeList);
					}else{
						zTree.getChildren().add(z);
					}
				}
			}
		}
		renderJson(returnZtree);
	}

	@Clear(LogInterceptor.class)
	public void permissionOrgTreeSelect() {

		List<ZTree> orgFormTree = persOrgService.permissionZTree(AuthUtils.getUserId());

		List<ZTree> returnZtree=new ArrayList<>();

		Set<String> idSet=new TreeSet<>();
		for(ZTree zTree:orgFormTree){
			for(ZTree z:orgFormTree){
				if(z.getpId().equalsIgnoreCase(zTree.getId())){
					idSet.add(z.getId());
					if(zTree.getChildren()==null){
						List<ZTree> childrenZtreeList=new ArrayList<>();
						childrenZtreeList.add(z);
						zTree.setChildren(childrenZtreeList);
					}else{
						zTree.getChildren().add(z);
					}
				}
			}
		}
		for(ZTree zTree:orgFormTree){
			if(!idSet.contains(zTree.getId())){
				returnZtree.add(zTree);
			}
		}
		renderJson(returnZtree);
	}
	
	@Clear(LogInterceptor.class)
	public void persOrgXmSelectTree() {
		renderJson(Ret.ok("msg", "加载成功!").set("persOrgSelectTree", persOrgService.orgXmSelectTree("")));
	}

    /**
     * 添加页面方法
     */
    public void add() {
    	String viewPath = "dpOrgForm.html";
    	final String formType = getPara("formType");
    	if(formType.equals("main")){
    		viewPath = "mainOrgForm.html";
    	}
    	final String orgId = AuthUtils.getOrgId();
    	PersOrg parentOrg = new PersOrg();
    	if(StrKit.notBlank(orgId)){
    		parentOrg = persOrgService.findById(orgId);
    	}
		List<User> userList=userService.findUnlockUserList();
		setAttr("userList", userList);
		Map<String,String> orgCateGoryTypeMap=new HashMap<>();
		for(PersOrgCateGoryType cateGoryType:PersOrgCateGoryType.values()){
			orgCateGoryTypeMap.put(cateGoryType.name(),cateGoryType.getValue());
		}
		List<MainBase> baseList=mainBaseService.findBaseList();
		List<MainBranchOffice> branchOfficeList=mainBranchOfficeService.getUnDelBranchOffice();

		setAttr("baseList",baseList);
		setAttr("branchOfficeList",branchOfficeList);
		setAttr("orgCateGoryTypeMap", orgCateGoryTypeMap);
    	setAttr("parentOrg", parentOrg);
    	setAttr("model", new Org());
    	setAttr("orgCategoryList", mainOrgCategoryService.findList());
        render(viewPath);
    }
    
    /**
     * 编辑组织审核人页面方法
     */
    public void editOrgReviewer() {
    	PersOrgReviewer model = getBean(PersOrgReviewer.class, "", true);
    	if(StrKit.notBlank(model.getId())) {
    		model = persOrgReviewerService.findById(model.getId());
    	}
    	setAttr("model", model);
    	setAttr("empUserList", persOrgEmployeeService.empUserList(null));
    	render("orgReviewerForm.html");
    }
    
    /**
     * 修改页面方法
     */
    public void edit() {
    	String viewPath = "dpOrgForm.html";
    	final String orgId = getPara("id");//获取机构ID
    	PersOrg model = persOrgService.findById(orgId);
    	if(model!=null && StrKit.notBlank(model.getOrgType())){
    		if(!model.getOrgType().equals(OrgType.mainOrg)){
    			if(StrKit.notBlank(model.getParentIds())){
    	    		final String[] parentIds = model.getParentIds().split(",");
    	    		if(parentIds.length>1){
    	    			setAttr("parentOrg", persOrgService.findById(model.getParentId()));
    	    		}
    	    	}else{
    	    		setAttr("parentOrg", new Org());
    	    	}
    		}else{
    			viewPath = "mainOrgForm.html";
    		}
    	}
		Map<String,String> orgCateGoryTypeMap=new HashMap<>();
		for(PersOrgCateGoryType cateGoryType:PersOrgCateGoryType.values()){
			orgCateGoryTypeMap.put(cateGoryType.name(),cateGoryType.getValue());
		}
		List<MainBase> baseList=mainBaseService.findBaseList();
		List<MainBranchOffice> branchOfficeList=mainBranchOfficeService.getUnDelBranchOffice();

		setAttr("baseList",baseList);
		setAttr("branchOfficeList",branchOfficeList);
    	List<User> userList=userService.findUnlockUserList();
    	setAttr("model", model);
		setAttr("userList", userList);
		setAttr("orgCateGoryTypeMap", orgCateGoryTypeMap);
    	setAttr("orgCategoryList", mainOrgCategoryService.findList());
    	render(viewPath);
    }
    
    /**
     * 机构树数据
     */
	@Clear(LogInterceptor.class)
    public void orgFormTree() {

		//如果没有查看所有组织机构的权限，则只能查看自己组织架构下的
		/*if(!subject.isPermitted("org:check:allOrg")){

			PersOrgEmployee employee =persOrgEmployeeService.getEmployeeByAccountId(AuthUtils.getUserId());
			if(employee==null){
				renderJson(new DataTable<ZTree>());
				return;
			}
			orgId = employee.getOrgId();
		}*/
        List<ZTree> orgFormTree = persOrgService.orgFormTreeByUserId(AuthUtils.getUserId(),null);
        renderJson(orgFormTree);
    }

	@Clear(LogInterceptor.class)
	public void orgXmSelectTree() {
		List<Record> orgList=null;
		if(UserType.SYSTEM_ADMIN.equals(AuthUtils.getLoginUser().getUserType())){
			String sql="select * from pers_org b  " +
					"where  b.del_flag='0' and b.is_enable='1' ";
			orgList=Db.find(sql);
		}else{
			String sql="select b.* from pers_user_org a " +
					"INNER JOIN pers_org b on a.org_id=b.id " +
					"where a.user_id=? and b.del_flag='0' and b.is_enable='1' ";
			orgList=Db.find(sql,AuthUtils.getUserId());
		}

		if(orgList==null || orgList.size()==0){
			renderJson(new Object());
			return;
		}
		Map<String,ZTree> zTreeMap=new HashMap<>();
		for(Record record:orgList){
			ZTree zTree=new ZTree(record.getStr("id"),record.getStr("org_name"),record.getStr("org_type"),record.getStr("parent_id"),record.getStr("parent_ids"));
			zTreeMap.put(record.getStr("id"),zTree);
		}
		List<ZTree> returnZtree=new ArrayList<>();

		for(Record record:orgList ){
			if("00000000-0000-0000-0000-000000000000".equals(record.getStr("parent_id"))){
				//第一级直接添加
				returnZtree.add(zTreeMap.get(record.getStr("id")));
			}else{
				// 不是第一级，帮它找到parent，并把它自己设置到parent里，如果没有parent则自己为最高级
				ZTree parent=zTreeMap.get(record.getStr("parent_id"));
				if(parent==null){
					returnZtree.add(zTreeMap.get(record.getStr("id")));
				}else{
					List<ZTree> children=parent.getChildren();
					if(children==null){
						parent.setChildren(new ArrayList<>());
					}
					parent.getChildren().add(zTreeMap.get(record.getStr("id")));
				}
			}
		}


		renderJson(returnZtree);
	}

    public void disable(){
		String id=getPara("id");
		PersOrg org = persOrgService.findById(id);
		if(org==null){
			renderJson(Ret.fail("msg","error"));
			return;
		}
		String orgSql=" select * from pers_org where parent_id=? and del_flag='0' and is_enable='1' ";
		if(Db.findFirst(orgSql,org.getId())!=null){
			renderJson(Ret.fail("msg","存在可用的子部门,不可禁用"));
			return;
		}
		String sql="select * from pers_org_employee_rel a left join pers_org_employee b on a.emp_id=b.id " +
				"where a. relationship_type='dept' and relationship_id=? and b.del_flag='0' and b.archive_status='incumbency' ";
		if(Db.findFirst(sql,org.getId())!=null){
			renderJson(Ret.fail("msg","该部门有在职员工,不可禁用"));
			return;
		}
		org.setIsEnable("0");
		org.setUpdateDate(new Date());
		org.setUpdateBy(AuthUtils.getUserId());
		if(org.update()){
			qiYeWeiXinService.deleteDepartment(org.getQiyeId());

			PersOrg persOrg=persOrgService.findById(org.getId());
			mainSyncRecordService.saveSyncRecord(SyncType.persOrg.getKey(), SyncDataType.UPDATE, JSON.toJSONString(persOrg),AuthUtils.getUserId());
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg","操作失败"));
		}
	}

	public void enable(){
		String id=getPara("id");
		PersOrg org = persOrgService.findById(id);
		if(org==null){
			renderJson(Ret.fail("msg","error"));
			return;
		}
		PersOrg parentOrg=persOrgService.findById(org.getParentId());
		if(parentOrg==null || "0".equals(parentOrg.getIsEnable())){
			renderJson(Ret.fail("msg","父部门未启用,不可启用"));
			return;
		}
		org.setIsEnable("1");
		org.setUpdateDate(new Date());
		org.setUpdateBy(AuthUtils.getUserId());
		if(org.update()){
			Department department=new Department();
			department.setParentId(parentOrg.getQiyeId());
			department.setName(org.getOrgName());
			department.setOrder(org.getOrgSort());
			Integer qiyeId=qiYeWeiXinService.createDepartment(department);
			if(qiyeId!=null){
				org.setQiyeId(qiyeId);
				org.setUpdateDate(new Date());
				org.update();
			}
			PersOrg persOrg=persOrgService.findById(org.getId());
			mainSyncRecordService.saveSyncRecord(SyncType.persOrg.getKey(), SyncDataType.UPDATE, JSON.toJSONString(persOrg),AuthUtils.getUserId());
			renderJson(Ret.ok("msg","操作成功"));
		}else{
			renderJson(Ret.fail("msg","操作失败"));
		}
	}

	public void orgFormTreeByUserId() {
		List<ZTree> orgFormTree=null;
		/*if("system_admin".equals(AuthUtils.getLoginUser().getUserType())){
			orgFormTree = persOrgService.orgFormTreeByUserId(null);
		}else{

		}*/
		orgFormTree = persOrgService.orgFormTreeByUserId(AuthUtils.getUserId(),null);

		renderJson(orgFormTree);
	}

	public void orgDtree(){
		List<ZTree> orgFormTree=null;
		/*if("system_admin".equals(AuthUtils.getLoginUser().getUserType())){
			orgFormTree = persOrgService.orgFormTreeByUserId(null);
		}else{
			orgFormTree = persOrgService.orgFormTreeByUserId(AuthUtils.getUserId());
		}*/
		orgFormTree = persOrgService.orgFormTreeByUserId(AuthUtils.getUserId(),null);
		Map<String,Object> result=new HashMap<>();
		JSONObject obj=new JSONObject();
		obj.put("code",200);
		obj.put("message","操作成功");

		result.put("status",obj);
		result.put("data",orgFormTree);

		renderJson(result);
	}

	public void allOrgDtree(){
		List<ZTree> orgFormTree=null;

		orgFormTree = persOrgService.orgFormTreeByUserId(null,null);
		Map<String,Object> result=new HashMap<>();
		JSONObject obj=new JSONObject();
		obj.put("code",200);
		obj.put("message","操作成功");

		result.put("status",obj);
		result.put("data",orgFormTree);

		renderJson(result);
	}

	public void orgFormTreeTableByUserId() {
		List<ZTree> orgFormTree=null;
		if("system_admin".equals(AuthUtils.getLoginUser().getUserType())){
			orgFormTree = persOrgService.orgFormTreeByUserId(null,null);
		}else{
			orgFormTree = persOrgService.orgFormTreeByUserId(AuthUtils.getUserId(),null);
		}

		renderJson(new DataTable<ZTree>(orgFormTree));
	}

	@Clear(LogInterceptor.class)
	public void userOrgFormTree() {
		String userId=getPara("userId");
		List<ZTree> orgFormTree = persOrgService.userOrgFormTree(userId);
		renderJson(orgFormTree);
	}
    
	@Clear(LogInterceptor.class)
    public void getOrgByParentId() {
    	final String parentId = getPara("parentId");
    	final String orgType = getPara("orgType");
    	List<PersOrg> orgList = new ArrayList<PersOrg>();
    	Ret restRsult = Ret.ok("msg", "加载机构成功!");
    	try {
			orgList = persOrgService.getOrgListByType(parentId, orgType);
		} catch (Exception e) {
			e.printStackTrace();
			restRsult = Ret.fail("msg", "加载区域失败！");
		}
    	restRsult.set("resultData", orgList);
    	renderJson(restRsult);
    }
	
    /**
     * 批量更新排序
     */
    public void sortBatchSave(){
    	final String sortDatas = getPara("sortDatas");
    	if(StrKit.notBlank(sortDatas)){
    		List<PersOrg> orgList = JSONArray.parseArray(sortDatas, PersOrg.class);
    		if (persOrgService.sortBatchSave(orgList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }
    
	/**
	 * 保存方法
	 */
    public void save() {
    	PersOrg org = getBean(PersOrg.class, "", true);
		org.setUpdateBy(AuthUtils.getUserId());

		if(StrKit.notBlank(org.getId())){
			if(org.getId().equals(org.getParentId())){
				renderJson(Ret.fail("msg","不能设置自己为自己的父级"));
				return;
			}
			PersOrg parentOrg=persOrgService.findById(org.getParentId());
			if(!"00000000-0000-0000-0000-000000000000".equals(org.getParentId()) && parentOrg.getParentIds().contains(org.getId())){
				renderJson(Ret.fail("msg","不能设置自己的子级为自己的父级"));
				return;
			}
		}

        if (persOrgService.orgSave(org)) {
        	renderJson(Ret.ok("msg", "操作成功!"));
        } else {
        	renderJson(Ret.fail("msg", "操作失败！"));
        }
    }
    
    /**
     * 保存组织审核人方法
     */
    public void saveOrgReviewer() {
    	List<Record> list = null;
    	PersOrgReviewer model = getBean(PersOrgReviewer.class, "", true);
    	String empId = "";
		if(StrKit.notBlank(model.getEmpId())) {
			final String[] empUserArray = model.getEmpId().split("\\|");
			empId = empUserArray[0];
		}
    	if(StrKit.notBlank(model.getId())){
			model.setUpdateBy(AuthUtils.getUserId());
			list = Db.find("select * from pers_org_reviewer where del_flag='0' and org_id=? and reviewer_type=? and emp_id=? and id!=?",model.getOrgId(),model.getReviewerType(),empId,model.getId());
		}else{
    		model.setCreateBy(AuthUtils.getUserId());
			model.setUpdateBy(AuthUtils.getUserId());
			list = Db.find("select * from pers_org_reviewer where del_flag='0' and org_id=? and reviewer_type=? and emp_id=?",model.getOrgId(),model.getReviewerType(),empId);
		}
    	if((list != null && list.size() >0 )){
    		renderJson(Ret.fail("msg", "该审核人对应的审核类型已存在！"));
    	}else{
    		if(persOrgService.orgReviewerSave(model)) {
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else {
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}
    }
    
    /**
     * 删除当前数据及其子数据
     */
    public void del() {
    	String orgId=getPara("id");
    	List<PersOrg> orgList=persOrgService.findChildrenById(orgId);
    	if(orgList!=null && orgList.size()>0){
			renderJson(Ret.fail("msg", "该机构还存在子机构，请先删除子机构"));
			return;
		}
		String sql="select * from pers_org_employee_rel a left join pers_org_employee b on a.emp_id=b.id " +
				"where a. relationship_type='dept' and relationship_id=? and b.del_flag='0' and b.archive_status='incumbency' ";
		if(Db.findFirst(sql,orgId)!=null){
			renderJson(Ret.fail("msg","该部门有在职员工,不可作废"));
			return;
		}
    	if(persOrgService.orgDel(orgId,AuthUtils.getUserId())){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", "操作失败！"));
    	}
    }

    public void createQiYeOrg(){
		persOrgService.createQiYeOrg();
		renderJson("{\"msg\":\"success\"}");
    }

    public void findDepartment(){
    	String orgId=getPara("orgId");
    	List<PersOrg> orgList=persOrgService.findDepartment(orgId);
    	renderJson(Ret.ok("msg", "").set("data",orgList));

	}


	public void test(){

    	List<String> list=qiYeWeiXinService.test();

    	renderJson(list);
	}

	public void test2(){
		List<String> list=qiYeWeiXinService.test2();

		renderJson(list);
    }

    public void afaf(){
		String sql=" select id,qiye_userid from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
		List<Record> recordList=Db.find(sql);
		List<String> params=new ArrayList<>();
		for(Record record : recordList){
			try {
				String userId=record.getStr("qiye_userid");
				String result=qiYeWeiXinService.getUser(userId);
				if(StrKit.isBlank(result)){
					Db.update(" update pers_org_employee set qiye_userid=NULL where id=? ",record.getStr("id"));
				}
				Thread.sleep(500);
			}catch (Exception e){
				params.add(record.getStr("qiye_userid"));
				e.printStackTrace();
			}
		}
		renderJson(params);
    }

	public void orgHeadcountApplyIndex(){

		render("orgHeadcountApplyIndex.html");
	}

	public void orgHeadcountApplyForm(){
		String id=getPara("id");
		if(StrKit.isBlank(id)){
			String taskId=getPara("taskId");
			PersTask persTask=persTaskService.findByTaskId(taskId);
			if(persTask!=null){
				id=persTask.getRecordId();
			}
		}
		setAttr("isSaveHandle",false);
		if(StrKit.isBlank(id)){
			String taskId=getPara("taskId");
			PersTask persTask=persTaskService.findByTaskId(taskId);
			if(persTask!=null){
				id=persTask.getRecordId();
			}
		}
		if(StrKit.notBlank(id)){
			PersOrgHeadcountApply headcountApply=persOrgHeadcountApplySetvice.findById(id);

			PersTask persTask=persTaskService.findByRecordId(headcountApply.getId());
			persTaskService.getSubmitInfo(persTask);
			setAttr("persTask",persTask);
			if(persTask!=null){
				Map<String,Object> taskDetail=persApprovalService.getTaskDetail(persTask.getTaskId(),AuthUtils.getUserId());
				setAttr("stepts",taskDetail.get("stepts"));
				setAttr("taskState",taskDetail.get("taskState"));
				setAttr("isSaveHandle",taskDetail.get("isSaveHandle"));
				setAttr("currentStepName",taskDetail.get("currentStepName"));
				setAttr("currentStepAlias",taskDetail.get("currentStepAlias"));
				setAttr("taskId",persTask.getTaskId());

				JSONArray currentSteps=(JSONArray)taskDetail.get("currentSteps");
				//批准
				boolean allowApprove=false;
				//拒绝
				boolean allowReject=false;
				//中止
				boolean allowAbort=false;
				//提交
				boolean allowSubmit=false;
				if(currentSteps!=null){
					for(int i=0;i<currentSteps.size();i++){
						JSONObject currentStep=currentSteps.getJSONObject(i);
						boolean flag=false;
						for(int j=0;j<currentStep.getJSONArray("UserIds").size();j++){
							if(!flag && AuthUtils.getUserId().equalsIgnoreCase(currentStep.getJSONArray("UserIds").getString(j))){
								flag=true;
							}
						}
						if(flag){
							allowApprove=currentStep.getBoolean("AllowApprove");
							allowReject=currentStep.getBoolean("AllowReject");
							allowSubmit=currentStep.getBoolean("AllowSubmit");
						}
						allowAbort=currentStep.getBoolean("AllowAbort");
					}
				}
				setAttr("allowAbort",allowAbort);
				setAttr("allowSubmit",allowSubmit);
				setAttr("allowReject",allowReject);
				setAttr("allowApprove",allowApprove);
			}
			setAttr("headcountApply",headcountApply);
			setAttr("deptName",persOrgService.getOrgParentNames(headcountApply.getDeptId()));

		}else{
			PersTask persTask=new PersTask();
			persTask.setCreateBy(AuthUtils.getUserId());
			try {
				Record subUserInfoRecord=Db.findFirst("select a.user_id as userId,a.emp_id as empId,b.relationship_id as deptId,c.relationship_id as positionId from pers_emp_user a " +
						"inner join pers_org_employee_rel b on b.id=(select id from pers_org_employee_rel where emp_id=a.emp_id and relationship_type='dept' order by is_main desc limit 1) " +
						"inner join pers_org_employee_rel c on c.id =(select id from pers_org_employee_rel where emp_id=a.emp_id and relationship_type='position' order by is_main desc limit 1) " +
						"where a.user_id=?",persTask.getCreateBy());
				persTask.setSubmitUserId(persTask.getCreateBy());
				persTask.setApplyTime(new Date());
				if(subUserInfoRecord!=null){
					persTask.setDeptId(subUserInfoRecord.getStr("deptId"));
					persTask.setPositionId(subUserInfoRecord.getStr("positionId"));
				}

			}catch (Exception e){
				e.printStackTrace();
			}
			persTaskService.getSubmitInfo(persTask);
			setAttr("persTask",persTask);
		}

		//setAttr("trafficTypeList",DestinationType.values());

		setAttr("createBy",AuthUtils.getUserId());

		render("orgHeadcountApplyForm.html");
	}

	@Inject
	PersOrgHeadcountApplySetvice persOrgHeadcountApplySetvice;

	public void orgHeadcountApplyPage(){
		String deptId=getPara("deptId");
		Integer page = getParaToInt("page");
		Integer limit = getParaToInt("limit");

		Page<Record> recordPage=persOrgHeadcountApplySetvice.findPageList(page,limit,deptId);
		if(recordPage.getList()!=null && recordPage.getList().size()>0){
			for(Record record:recordPage.getList()){

				String dId=record.getStr("dept_id");
				String deptName=persOrgService.getOrgParentNames(dId);
				record.set("deptName",deptName);

				PersTask persTask=persTaskService.findByRecordId(record.getStr("id"));
				if(persTask!=null){
					Map<String,Object> taskDetail=persApprovalService.getTaskDetail(persTask.getTaskId(),AuthUtils.getUserId());
					record.set("stepts",taskDetail.get("stepts"));
					record.set("taskState",taskDetail.get("taskState"));
					record.set("isSaveHandle",taskDetail.get("isSaveHandle"));
					record.set("currentStepName",taskDetail.get("currentStepName"));
					record.set("taskId",persTask.getTaskId());
				}
			}
		}

		renderJson(new DataTable<Record>(recordPage));

	}

	public void orgHeadcountApplySave(){
		String saveType=getPara("saveType");
		String msg=getPara("msg");
		PersOrgHeadcountApply headcountApply=getBean(PersOrgHeadcountApply.class,"",true);
		if(StrKit.isBlank(headcountApply.getDeptId()) || headcountApply.getHeadcount()==null){
			renderCodeFailed("缺少参数");
			return;
		}

		boolean isInsert=false;
		boolean flag=false;
		if(StrKit.isBlank(headcountApply.getId())){
			isInsert=true;
			headcountApply.setId(IdGen.getUUID());
			headcountApply.setCreateDate(new Date());
			headcountApply.setDelFlag("0");
			headcountApply.setApplyTime(new Date());
			headcountApply.setUpdateBy(AuthUtils.getUserId());
			headcountApply.setCreateBy(AuthUtils.getUserId());
			headcountApply.setApplyTime(new Date());
			if("2".equals(saveType)){
				headcountApply.setStatus("2");
			}else{
				headcountApply.setStatus("1");
			}
			flag=headcountApply.save();
		}else{
			headcountApply.setUpdateBy(AuthUtils.getUserId());
			headcountApply.setCreateDate(new Date());
			flag=headcountApply.update();
		}

		if(flag){
			//判断创建流程没有
			String sql="select * from pers_task where record_id=? and del_flag='0' ";
			Record taskRecord=Db.findFirst(sql,headcountApply.getId());
			boolean createTaskFlag=taskRecord==null;
			if(createTaskFlag && "2".equals(saveType)){
				PersTask persTask=new PersTask();
				persTask.setId(IdGen.getUUID());
				persTask.setDelFlag("0");
				persTask.setCreateBy(headcountApply.getCreateBy());
				persTask.setCreateDate(new Date());
				persTask.setUpdateBy(headcountApply.getCreateBy());
				persTask.setUpdateDate(new Date());
				persTask.setTaskNo(PersTaskType.headcount.getTaskNo());
				persTask.setRecordId(headcountApply.getId());
				persTask.setIsEnd("0");
				persTaskService.httpCreateTask(persTask);

			}else if(!createTaskFlag && "2".equals(saveType)){
				//获取流程步骤
				Map<String,Object> taskDetailMap=persApprovalService.getTaskDetail(taskRecord.getStr("task_id"),taskRecord.getStr("create_by"));

				Map<String,Object> doTaskMap=persApprovalService.doTask(taskRecord.getStr("task_id"),taskRecord.getStr("task_no")
						,"5",(String)taskDetailMap.get("currentStepAlias"),msg,AuthUtils.getUserId());
				if((Boolean) doTaskMap.get("flag")){
					renderCodeSuccess("success");
					return;
				}else{
					renderCodeFailed((String)doTaskMap.get("msg"));
					return;
				}
			}
			renderCodeSuccess("success",headcountApply.getId());
		}else{
			renderCodeFailed("操作失败");
		}

	}

	public static void main(String[] args) {
		String str="[\"E30724E8-729F-408E-9775-1437A64FA9D0\",\n" +
				"\"530F35B4-F753-412E-8583-0DF4D9C7EF08\",\n" +
				"\"BF76AC3A-58E4-4EAA-A3DC-F3E1267AA859\",\n" +
				"\"438E8425-6F6E-433E-9A59-50BAE7411F01\",\n" +
				"\"48E21970-B47D-4612-A4E6-62590B856E2F\",\n" +
				"\"4B9B3170-F245-43CF-A099-138C2342BF46\",\n" +
				"\"86C0EF76-FE80-4AE5-9BC4-63D70D0B3CA0\",\n" +
				"\"728DEE8E-36D8-4EBC-A9E1-E4780C066B1F\",\n" +
				"\"16503FC2-6F40-443C-8DB5-638100D88B4E\",\n" +
				"\"58BA9AE5-AED5-4F00-BDFB-C5A2F7178D5A\",\n" +
				"\"07826706-C013-4710-BEA4-B71A2EFD614B\",\n" +
				"\"E5CCB1F1-1A6A-41AF-8264-8F0FC6C4C56B\",\n" +
				"\"279F5D8D-F200-44B3-9156-BE89D91B00DA\",\n" +
				"\"93A869E7-09F4-4A5F-A036-192E9E64B2C7\",\n" +
				"\"44E2E61E-8B2A-4203-BAE2-51257C80C419\",\n" +
				"\"355C9824-1C80-4408-A46E-1BD52D18D781\",\n" +
				"\"5F97BD10-7F5C-4280-92B2-51EB9ABED87A\",\n" +
				"\"B4FB84A4-FD5F-4EA5-A951-3C6F37ECAC58\",\n" +
				"\"145CD91D-8683-493D-908F-7E05630796FA\",\n" +
				"\"5BC91C12-4E9B-4400-8F10-5D39B17FBB56\",\n" +
				"\"B8CF348B-4BFD-4FE9-9287-1196EBF7B92A\",\n" +
				"\"BE6F3E8A-2D5A-4D13-B80F-9B3B4E2CC498\",\n" +
				"\"09F6E63B-8F98-4E13-8144-3FF233273780\",\n" +
				"\"1B9A4022-4868-439F-86CD-0724FFC74032\",\n" +
				"\"CB088118-6CF5-494A-99E2-31BCC8FD9058\",\n" +
				"\"7701DE4D-F416-491E-91CA-E6BE8805DEF3\",\n" +
				"\"062A640B-B335-470D-8DCA-463DB49BAA38\",\n" +
				"\"2DE4E173-CE3E-4299-94E1-558E1BAD899A\",\n" +
				"\"A8B18609-AC3F-4CA3-904D-C3C5F6FA811C\",\n" +
				"\"31D56134-7D9A-40E8-ABCE-E2CBDE625A72\",\n" +
				"\"5CA50334-FC1C-487A-965D-F3DDE8DF2EE2\",\n" +
				"\"48621511-2B6A-4B8C-9DFD-E088C8AEA6C6\",\n" +
				"\"0CA19C1E-31CA-46E3-863D-06886C7A83B4\",\n" +
				"\"1160C1EC-BED1-40BC-B1BF-1EB06CFECBA0\",\n" +
				"\"C0448168-C18F-485D-83B6-85400DB49B2C\",\n" +
				"\"84362DEB-C4B9-4E5E-96AF-D5A7ECD3F6CB\",\n" +
				"\"1651CC87-A535-44C3-8904-DA5D08F4F0C2\",\n" +
				"\"360722F2-486E-433F-BABA-1252DE55A9BC\",\n" +
				"\"A099D492-A97E-48EF-8601-E3867D20E9D4\",\n" +
				"\"AF208D86-DFC3-4FE8-BD49-E51CB95F8884\",\n" +
				"\"5F27C0BD-95BE-40BF-B2AA-0E5C1DA97C6B\",\n" +
				"\"F35C7F67-5884-4198-B3D2-39380E1CFA4F\",\n" +
				"\"0622585F-3A25-4944-8051-B18ECE886AD2\",\n" +
				"\"45BC133D-6043-495B-B6E3-BB9E1F8A240E\",\n" +
				"\"7B9D2239-5813-4BBE-B7C4-3794BF6C2CDB\",\n" +
				"\"82ACFF2E-0009-4A40-9B19-9D8ACFC9AE68\",\n" +
				"\"3595E75A-5701-4950-8E01-52386B30CF4A\",\n" +
				"\"80CD8B28-1993-4235-B9F0-9E8562956999\",\n" +
				"\"A406F78F-27CE-4C02-B6A0-96A4B7E560A3\",\n" +
				"\"D807E043-DB35-4C3B-B9AA-BF857040764C\",\n" +
				"\"E58E71D8-0B7B-4C42-AC99-C3FAE3E0ABE3\",\n" +
				"\"B12B15D2-C70A-4191-B267-DEAB604BCF1A\",\n" +
				"\"71A0ABAB-1372-4433-8B19-4AAB630EA636\",\n" +
				"\"56F20898-1AE9-45BC-93A3-BD28AC45C6D5\",\n" +
				"\"78FCED99-7AD0-4DD7-97B0-193E54120B88\",\n" +
				"\"95F5D501-1093-41D1-9AF7-BCAFBD470579\",\n" +
				"\"55A617EC-EBD9-4F1E-9C39-20B657BF07E4\",\n" +
				"\"6DC9B26C-4011-40D9-B757-9B955F6ECFEC\",\n" +
				"\"63177D4D-03ED-437A-AA04-C821B7626297\",\n" +
				"\"E9004FA8-202D-4576-88C5-D43C77B18F56\",\n" +
				"\"3BD412FD-C902-45E8-8003-CDB48ACBE7EA\",\n" +
				"\"A6A2773E-5C20-48C4-986B-99A0F5E15A9D\",\n" +
				"\"5458866C-C1D6-4D63-96DE-37EEA24C980F\",\n" +
				"\"1BC36AC3-9587-4271-B3B5-C97DB9124E85\",\n" +
				"\"E3078A98-081A-4F3D-8ED8-B32AE4F614D6\",\n" +
				"\"63109500-D82C-4B57-AD14-5B4B1F459404\",\n" +
				"\"92A50AF6-3AA7-423F-80D2-1F03ECF37CE4\",\n" +
				"\"91508065-9E2A-4B0D-A4DB-70FC39A52E80\",\n" +
				"\"4ED5236A-6B6C-4B88-A0F7-88C92979503A\",\n" +
				"\"770C0231-7BFB-4C55-BA01-95378B94A623\",\n" +
				"\"0A7FCE63-80E4-425C-B1D9-A84CA10BE740\",\n" +
				"\"472BD7DB-5D77-40DD-A6B4-B0D03E1A228C\",\n" +
				"\"592FC4D8-F2F4-4496-9284-9A76ECD55209\"]";

		JSONArray jsonArray=JSON.parseArray(str);
		for (int i = 0; i < jsonArray.size(); i++) {

			String s = HttpKit.get("http://hrm.cncsgroup.com/api/getUserDataOrg?userId=" + jsonArray.getString(i));
			System.out.println(s);
		}

	}

}
