package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainRoleService;
import com.cszn.integrated.service.api.pers.PersBranchCompanyRoleService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCertificateTypeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.entity.enums.PositionLv;
import com.cszn.integrated.service.entity.main.MainRole;
import com.cszn.integrated.service.entity.pers.PersBranchCompanyRole;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCertificateType;
import com.cszn.integrated.service.entity.pers.PersPosition;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 职位管理
 * <AUTHOR>
 * @Date 2019/7/18
 **/
@RequestMapping(value="/pers/position", viewPath="/modules_page/pers/position")
public class PersPositionController extends BaseController {

    @Inject
    private PersPositionService persPositionService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private MainRoleService mainRoleService;
    @Inject
    private PersBranchCompanyRoleService persBranchCompanyRoleService;
    @Inject
    private PersOrgEmployeeCertificateTypeService persOrgEmployeeCertificateTypeService;

    /**
     * 跳转职位管理界面
     */
    public void index(){
        render("positionIndex.html");
    }


    /**
     * 职位列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        PersPosition position = getBean(PersPosition.class,"",true);
        //position.setOrgId(AuthUtils.getOrgId());
        Page<Record> page = persPositionService.findListPage(getParaToInt("page"), getParaToInt("limit"),position);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 职位删除
     */
    public void delete(){
        String id = getPara("id");
        PersPosition position = persPositionService.get(id);
        if(position != null){
            boolean flag = persPositionService.delPosition(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "删除成功"));
            } else {
                renderJson(Ret.fail("msg", "删除失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "删除失败"));
        }
    }
    
    
    /**
     * 跳转新增界面
     */
    public void add(){
    	/*setAttr("position",new PersPosition());*/
    	setAttr("orgId",getPara("orgId"));

    	PersOrg org=persOrgService.findById(getPara("orgId"));
    	if(org!=null){

            List<PersBranchCompanyRole> branchCompanyRoleList=new ArrayList<>();
            List<PersOrg> orgList=new ArrayList<>();
            orgList.add(org);
            persOrgService.getAllParent(orgList,org.getParentId());

            for (PersOrg persOrg : orgList) {
                List<PersBranchCompanyRole> list=persBranchCompanyRoleService.getBranchCompanyRoleByOrgId(persOrg.getId());
                branchCompanyRoleList.addAll(list);
            }
            setAttr("branchCompanyRoleList",branchCompanyRoleList);

        }

        List<MainRole> roleList=mainRoleService.findEnabledRole();

    	setAttr("positionLvs",PositionLv.values());
        setAttr("roleList",roleList);

        List<PersOrgEmployeeCertificateType> certificateTypeList = persOrgEmployeeCertificateTypeService.findCertificateTypeList("1");
        setAttr("certificateTypeList",certificateTypeList);
        render("positionForm.html");
    }


    /**
     * 跳转修改界面
     */
    public void edit(){
        PersPosition position = persPositionService.get(getPara("id"));
        setAttr("position",position);
        PersOrg org=persOrgService.findById(position.getOrgId());

        List<MainRole> roleList=mainRoleService.findEnabledRole();
        setAttr("roleList",roleList);
        if(org!=null){
            setAttr("positionName",org.getOrgName());

            List<PersBranchCompanyRole> branchCompanyRoleList=new ArrayList<>();
            List<PersOrg> orgList=new ArrayList<>();
            orgList.add(org);
            persOrgService.getAllParent(orgList,org.getParentId());

            for (PersOrg persOrg : orgList) {
                List<PersBranchCompanyRole> list=persBranchCompanyRoleService.getBranchCompanyRoleByOrgId(persOrg.getId());
                branchCompanyRoleList.addAll(list);
            }
            setAttr("branchCompanyRoleList",branchCompanyRoleList);
        }
        setAttr("positionLvs",PositionLv.values());
        List<PersOrgEmployeeCertificateType> certificateTypeList = persOrgEmployeeCertificateTypeService.findCertificateTypeList("1");
        setAttr("certificateTypeList",certificateTypeList);
        render("positionForm.html");
    }


    /**
     * 保存
     */
    public void save(){
        PersPosition position = getBean(PersPosition.class,"",true);
        if(position == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        if(StringUtils.isNotBlank(position.getId())){
            PersPosition positionExist = persPositionService.get(position.getId());
            if(positionExist == null){ renderJson(Ret.fail("msg", "保存失败")); return; }
        }
        //position.setOrgId(AuthUtils.getOrgId());
        String flag = persPositionService.savePosition(position,AuthUtils.getUserId());
        if("suc".equals(flag)){
            renderJson(Ret.ok("msg", "保存成功"));
        }else if("".equals(flag)){
            renderJson(Ret.fail("msg", "职位名称不能重复"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }

    public void findPositionByOrgId(){
        String orgId=getPara("orgId");
        List<PersPosition> positionList=persPositionService.findPositionByOrgId(orgId);
        renderJson(Ret.ok("msg", "").set("data",positionList));
    }

    public void getPositionByOrgId(){
        String orgId=getPara("orgId");
        List<Record> positionList=persPositionService.getPositionByOrgId(orgId);
        renderJson(Ret.ok("msg", "").set("data",positionList));
    }
}
