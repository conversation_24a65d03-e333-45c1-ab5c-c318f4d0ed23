package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCertificateRecordService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCertificateTypeService;
import com.cszn.integrated.service.api.pers.PersPositionService;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCertificateRecord;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCertificateType;
import com.cszn.integrated.service.entity.pers.PersPosition;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

@RequestMapping(value="/pers/employeeCertificate", viewPath="/modules_page/pers/employeeCertificate")
public class PersOrgEmployeeCertificateController extends BaseController {

    @Inject
    PersOrgEmployeeCertificateTypeService persOrgEmployeeCertificateTypeService;
    @Inject
    PersOrgEmployeeCertificateRecordService persOrgEmployeeCertificateRecordService;
    @Inject
    PersPositionService persPositionService;

    public void typeIndex(){
        render("type/index.html");
    }

    public void typeForm(){
        String id = getPara("id");
        if(StrKit.notBlank(id)){
            PersOrgEmployeeCertificateType certificateType=persOrgEmployeeCertificateTypeService.findById(id);
            setAttr("model",certificateType);
        }
        render("type/form.html");
    }

    public void typePageList(){
        PersOrgEmployeeCertificateType certificateType=getBean(PersOrgEmployeeCertificateType.class,"",true);

        Page<PersOrgEmployeeCertificateType> certificateTypePage=persOrgEmployeeCertificateTypeService.findPageList(getParaToInt("page"),getParaToInt("limit"),certificateType);
        renderJson(new DataTable<PersOrgEmployeeCertificateType>(certificateTypePage));
    }

    public void saveType(){
        PersOrgEmployeeCertificateType certificateType=getBean(PersOrgEmployeeCertificateType.class,"",true);
        boolean flag=persOrgEmployeeCertificateTypeService.saveCertificateType(certificateType, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void getCertificateTypeByPositionId(){
        String positionId=getPara("positionId");
        String entryInfoId=getPara("entryInfoId");
        PersPosition position = persPositionService.findById(positionId);
        List<PersOrgEmployeeCertificateType> certificateTypeList=new ArrayList<>();
        List<Record> recordList=new ArrayList<>();
        if(position!=null && StrKit.notBlank(position.getCertificateTypeIds())){
            String[] certificateTypeIdArray = position.getCertificateTypeIds().split(",");
            if(certificateTypeIdArray!=null && certificateTypeIdArray.length>0){
                for (String certificateTypeId : certificateTypeIdArray) {
                    PersOrgEmployeeCertificateType certificateType = persOrgEmployeeCertificateTypeService.findById(certificateTypeId);
                    if("1".equals(certificateType.getIsEnable())){
                        Record record=new Record();
                        record.set("id",certificateType.getId());
                        record.set("name",certificateType.getName());
                        record.set("isEffectiveDate",certificateType.getIsEffectiveDate());
                        record.set("isEntryRequired",certificateType.getIsEntryRequired());
                        record.set("fileCount",certificateType.getFileCount());
                        if(StrKit.notBlank(entryInfoId)){
                            PersOrgEmployeeCertificateRecord certificateRecord = persOrgEmployeeCertificateRecordService.findEntryInfoCertificateRecordByTypeId(entryInfoId, certificateType.getId());
                            if(certificateRecord!=null){
                                record.set("data",certificateRecord);
                            }
                        }
                        recordList.add(record);
                    }
                }
            }
        }
        renderJson(Ret.ok("data",recordList));
    }

}
