package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainRoleService;
import com.cszn.integrated.service.api.pers.*;
import com.cszn.integrated.service.entity.main.MainRole;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.cron.EmployeeDelayCheckinCron;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.sql.SQLException;
import java.util.*;

@RequestMapping(value="/employeeCheckinRule", viewPath="/modules_page/pers/checkinRule")
public class PersOrgEmployeeCheckinRuleController extends BaseController {

    @Inject
    private PersOrgEmployeeCheckinRuleService persOrgEmployeeCheckinRuleService;
    @Inject
    private PersOrgEmployeeCheckinRuleRangeService persOrgEmployeeCheckinRuleRangeService;
    @Inject
    private PersOrgEmployeeCheckinRuleCheckinDateService persOrgEmployeeCheckinRuleCheckinDateService;
    @Inject
    private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    private PersOrgEmployeeCheckinRuleHolidaysService persOrgEmployeeCheckinRuleHolidaysService;
    @Inject
    private PersOrgEmployeeCheckinWxRecordService persOrgEmployeeCheckinWxRecordService;
    @Inject
    private PersOrgService persOrgService;
    @Inject
    private PersOrgEmployeeCheckinRuleScheduleService persOrgEmployeeCheckinRuleScheduleService;
    @Inject
    private PersOrgEmployeeCheckinRuleEmpScheduleService persOrgEmployeeCheckinRuleEmpScheduleService;
    @Inject
    private PersOrgEmployeeCheckinRuleWhiteService persOrgEmployeeCheckinRuleWhiteService;
    @Inject
    private MainRoleService mainRoleService;
    @Inject
    private PersOrgEmployeeCheckinDaySummaryService persOrgEmployeeCheckinDaySummaryService;
    @Inject
    private PersOrgEmployeeOverTimeApplyService persOrgEmployeeOverTimeApplyService;
    @Inject
    private PersOrgEmployeeLeaveRestApplyService persOrgEmployeeLeaveRestApplyService;
    @Inject
    private PersOrgEmployeeCheckinFillCardService persOrgEmployeeCheckinFillCardService;
    @Inject
    private PersOrgEmployeeCheckinRecordService persOrgEmployeeCheckinRecordService;
    @Inject
    EmployeeDelayCheckinCron employeeDelayCheckinCron;

    public void index(){
        render("index.html");
    }

    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            PersOrgEmployeeCheckinRule checkinRule=persOrgEmployeeCheckinRuleService.findById(id);
            List<PersOrgEmployeeCheckinRuleRange> deptRuleRangeList=persOrgEmployeeCheckinRuleRangeService.getDeptRangeByRuleId(id);
            List<PersOrgEmployeeCheckinRuleRange> empRuleRangeList=persOrgEmployeeCheckinRuleRangeService.getEmpRangeByRuleId(id);

            List<PersOrgEmployeeCheckinRuleCheckinDate> checkinDateList=persOrgEmployeeCheckinRuleCheckinDateService.getCheckinDateByRuleId(id);

            List<Record> empRuleRangeRecordList=new ArrayList<>();
            for(PersOrgEmployeeCheckinRuleRange empRuleRange :empRuleRangeList){
                PersOrgEmployee employee=persOrgEmployeeService.findById(empRuleRange.getCheckinId());
                if(employee!=null && "incumbency".equals(employee.getArchiveStatus())){
                    Record record=new Record();
                    record.set("id",empRuleRange.getCheckinId());
                    record.set("name",employee.getFullName());
                    record.set("orgName",employee.getOrg().getStr("orgName"));
                    empRuleRangeRecordList.add(record);
                }
            }
            List<PersOrgEmployeeCheckinRuleSchedule> ruleScheduleList=persOrgEmployeeCheckinRuleScheduleService.getRuleScheduleList(id);
            List<Record> empScheduleList=persOrgEmployeeCheckinRuleEmpScheduleService.getEmpScheduleList(id);

            JSONObject empScheduleJsonObj=new JSONObject();
            Set<String> idSet=new HashSet<>();
            if(empScheduleList!=null && empScheduleList.size()>0){
                for(Record  empSchedule : empScheduleList){
                    JSONObject jsonObject=JSON.parseObject(empSchedule.getStr("scheduleConfig"));
                    for(String key:jsonObject.keySet()){
                        JSONArray jsonArray=null;
                        if(empScheduleJsonObj.getString(key)==null){
                            jsonArray=new JSONArray();
                            empScheduleJsonObj.put(key,jsonArray);
                        }else{
                            jsonArray=empScheduleJsonObj.getJSONArray(key);
                        }
                        JSONObject json=new JSONObject();
                        json.put("value",empSchedule.getStr("empId"));
                        json.put("name",empSchedule.getStr("fullName"));
                        json.put("scheduleId",jsonObject.getString(key));
                        idSet.add(jsonObject.getString(key));
                        jsonArray.add(json);
                    }
                }
            }
            JSONArray empScheduleDataArray=new JSONArray();
            if(empScheduleJsonObj.size()>0){
                List<PersOrgEmployeeCheckinRuleSchedule> scheduleList=persOrgEmployeeCheckinRuleScheduleService.getRuleScheduleList(idSet);
                Map<String,PersOrgEmployeeCheckinRuleSchedule> scheduleMap=new HashMap<>();
                for(PersOrgEmployeeCheckinRuleSchedule schedule:scheduleList){
                    scheduleMap.put(schedule.getId(),schedule);
                }
                for(String key:empScheduleJsonObj.keySet()){
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("date",key);

                    JSONArray jsonArray=empScheduleJsonObj.getJSONArray(key);
                    for(int i=0;i<jsonArray.size();i++){
                        JSONObject object=jsonArray.getJSONObject(i);
                        object.put("selectedName",scheduleMap.get(object.get("scheduleId")).getName());
                        object.remove("scheduleId");
                    }
                    jsonObject.put("selectedArray",jsonArray);
                    empScheduleDataArray.add(jsonObject);
                }
            }
            setAttr("empScheduleDataArray",empScheduleDataArray);
            setAttr("checkinRule",checkinRule);
            setAttr("deptRuleRangeList",deptRuleRangeList);
            setAttr("ruleScheduleList",ruleScheduleList);
            setAttr("empScheduleList",empScheduleList);
            setAttr("empRuleRangeRecordList",empRuleRangeRecordList);
            setAttr("checkinDateList",checkinDateList);
        }
        render("form.html");
    }

    public void repairScheduleForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            PersOrgEmployeeCheckinRule checkinRule=persOrgEmployeeCheckinRuleService.findById(id);
            List<PersOrgEmployeeCheckinRuleRange> deptRuleRangeList=persOrgEmployeeCheckinRuleRangeService.getDeptRangeByRuleId(id);
            List<PersOrgEmployeeCheckinRuleRange> empRuleRangeList=persOrgEmployeeCheckinRuleRangeService.getEmpRangeByRuleId(id);

            List<PersOrgEmployeeCheckinRuleCheckinDate> checkinDateList=persOrgEmployeeCheckinRuleCheckinDateService.getCheckinDateByRuleId(id);

            List<Record> empRuleRangeRecordList=new ArrayList<>();
            for(PersOrgEmployeeCheckinRuleRange empRuleRange :empRuleRangeList){
                PersOrgEmployee employee=persOrgEmployeeService.findById(empRuleRange.getCheckinId());
                if(employee!=null && "incumbency".equals(employee.getArchiveStatus())){
                    Record record=new Record();
                    record.set("id",empRuleRange.getCheckinId());
                    record.set("name",employee.getFullName());
                    record.set("orgName",employee.getOrg().getStr("orgName"));
                    empRuleRangeRecordList.add(record);
                }
            }
            List<PersOrgEmployeeCheckinRuleSchedule> ruleScheduleList=persOrgEmployeeCheckinRuleScheduleService.getRuleScheduleList(id);
            List<Record> empScheduleList=persOrgEmployeeCheckinRuleEmpScheduleService.getEmpScheduleList(id);

            JSONObject empScheduleJsonObj=new JSONObject();
            Set<String> idSet=new HashSet<>();
            if(empScheduleList!=null && empScheduleList.size()>0){
                for(Record  empSchedule : empScheduleList){
                    JSONObject jsonObject=JSON.parseObject(empSchedule.getStr("scheduleConfig"));
                    for(String key:jsonObject.keySet()){
                        JSONArray jsonArray=null;
                        if(empScheduleJsonObj.getString(key)==null){
                            jsonArray=new JSONArray();
                            empScheduleJsonObj.put(key,jsonArray);
                        }else{
                            jsonArray=empScheduleJsonObj.getJSONArray(key);
                        }
                        JSONObject json=new JSONObject();
                        json.put("value",empSchedule.getStr("empId"));
                        json.put("name",empSchedule.getStr("fullName"));
                        json.put("scheduleId",jsonObject.getString(key));
                        idSet.add(jsonObject.getString(key));
                        jsonArray.add(json);
                    }
                }
            }
            JSONArray empScheduleDataArray=new JSONArray();
            if(empScheduleJsonObj.size()>0){
                List<PersOrgEmployeeCheckinRuleSchedule> scheduleList=persOrgEmployeeCheckinRuleScheduleService.getRuleScheduleList(idSet);
                Map<String,PersOrgEmployeeCheckinRuleSchedule> scheduleMap=new HashMap<>();
                for(PersOrgEmployeeCheckinRuleSchedule schedule:scheduleList){
                    scheduleMap.put(schedule.getId(),schedule);
                }
                for(String key:empScheduleJsonObj.keySet()){
                    JSONObject jsonObject=new JSONObject();
                    jsonObject.put("date",key);

                    JSONArray jsonArray=empScheduleJsonObj.getJSONArray(key);
                    for(int i=0;i<jsonArray.size();i++){
                        JSONObject object=jsonArray.getJSONObject(i);
                        object.put("selectedName",scheduleMap.get(object.get("scheduleId")).getName());
                        object.remove("scheduleId");
                    }
                    jsonObject.put("selectedArray",jsonArray);
                    empScheduleDataArray.add(jsonObject);
                }
            }
            setAttr("empScheduleDataArray",empScheduleDataArray);
            setAttr("checkinRule",checkinRule);
            setAttr("deptRuleRangeList",deptRuleRangeList);
            setAttr("ruleScheduleList",ruleScheduleList);
            setAttr("empScheduleList",empScheduleList);
            setAttr("empRuleRangeRecordList",empRuleRangeRecordList);
            setAttr("checkinDateList",checkinDateList);
        }
        String startDateStr= DateUtils.getLastMonth()+"-01";
        //String endDateStr= DateUtils.getLastMonth()+"-"+DateUtils.getLastMaxMonthDay();
        String endDateStr= DateUtils.formatDate(DateUtils.getNextDay(new Date(),-1),"yyyy-MM-dd");
        setAttr("minDate",startDateStr);
        setAttr("maxDate",endDateStr);
        render("repairSchedule.html");
    }

    public void checkinRulePageList(){
        PersOrgEmployeeCheckinRule checkinRule=getBean(PersOrgEmployeeCheckinRule.class,"",true);
        String deptId=getPara("deptId");

        Page<PersOrgEmployeeCheckinRule> checkinRulePage=persOrgEmployeeCheckinRuleService.checkinRulePageList(getParaToInt("page"),getParaToInt("limit"),checkinRule,AuthUtils.getUserId(),deptId);
        List<Record> recordList=new ArrayList<>();
        Page<Record> recordPage=new Page<>();
        recordPage.setList(recordList);
        recordPage.setPageNumber(checkinRulePage.getPageNumber());
        recordPage.setPageSize(checkinRulePage.getPageSize());
        recordPage.setTotalRow(checkinRulePage.getTotalRow());
        recordPage.setTotalPage(checkinRulePage.getTotalPage());
        if(checkinRulePage.getList()!=null && checkinRulePage.getList().size()>0){
            for(PersOrgEmployeeCheckinRule rule:checkinRulePage.getList()){
                Record record=new Record();
                record.setColumns(rule);
                List<PersOrgEmployeeCheckinRuleRange> deptRuleRangeList=persOrgEmployeeCheckinRuleRangeService.getDeptRangeByRuleId(rule.getId());
                List<PersOrgEmployeeCheckinRuleRange> empRuleRangeList=persOrgEmployeeCheckinRuleRangeService.getEmpRangeByRuleId(rule.getId());
                List<PersOrgEmployeeCheckinRuleCheckinDate> checkinDateList=persOrgEmployeeCheckinRuleCheckinDateService.getCheckinDateByRuleId(rule.getId());
                String deptNames="";
                if(deptRuleRangeList!=null && deptRuleRangeList.size()>0){
                    for(PersOrgEmployeeCheckinRuleRange deptRange:deptRuleRangeList){
                        deptNames+=persOrgService.getOrgParentNames(deptRange.getCheckinId())+"；";
                    }
                }
                String empNames="";
                if(empRuleRangeList!=null && empRuleRangeList.size()>0){
                    for(PersOrgEmployeeCheckinRuleRange empRange:empRuleRangeList){
                        PersOrgEmployee employee=persOrgEmployeeService.findById(empRange.getCheckinId());
                        if(employee==null){
                            continue;
                        }
                        empNames+=employee.getFullName()+"；";
                    }
                }
                record.set("checkinDate",checkinDateList);
                record.set("deptNames",deptNames);
                record.set("empNames",empNames);
                recordList.add(record);
            }
        }
        renderJson(new DataTable<Record>(recordPage));
    }

    public void delCheckinRule(){
        String id=getPara("id");

        PersOrgEmployeeCheckinRule checkinRule=new PersOrgEmployeeCheckinRule();
        checkinRule.setId(id);
        checkinRule.setDelFlag("1");
        if(persOrgEmployeeCheckinRuleService.saveCheckinRule(checkinRule, AuthUtils.getUserId())){
            PersOrgEmployeeCheckinRule rule=persOrgEmployeeCheckinRuleService.findById(id);
            if("1".equals(rule.getType()) || "3".equals(rule.getType())){
                Db.update("update pers_org_employee_checkin_rule_checkin_date set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0' "
                        ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                Db.update("update pers_org_employee_checkin_rule_range set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                        ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                Db.update("update pers_org_employee_checkin_rule_holidays set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                        ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());

            }else{
                Db.update("update pers_org_employee_checkin_rule_schedule set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                        ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                Db.update("update pers_org_employee_checkin_rule_range set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                        ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                Db.update("update pers_org_employee_checkin_rule_emp_schedule set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                        ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());

            }

            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void allDelCHeckinRule(){
        List<PersOrgEmployeeCheckinRule> ruleList=persOrgEmployeeCheckinRuleService.findAll();
        for(PersOrgEmployeeCheckinRule rule:ruleList){
            if("1".equals(rule.getDelFlag())){
                if("1".equals(rule.getType()) || "3".equals(rule.getType())){
                    Db.update("update pers_org_employee_checkin_rule_checkin_date set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0' "
                            ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                    Db.update("update pers_org_employee_checkin_rule_range set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                            ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                    Db.update("update pers_org_employee_checkin_rule_holidays set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                            ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());

                }else{
                    Db.update("update pers_org_employee_checkin_rule_schedule set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                            ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                    Db.update("update pers_org_employee_checkin_rule_range set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                            ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());
                    Db.update("update pers_org_employee_checkin_rule_emp_schedule set del_flag='1',update_by=?,update_date=? where rule_id=? and del_flag='0'"
                            ,AuthUtils.getUserId(),rule.getUpdateDate(),rule.getId());

                }
            }

        }
        renderJson(Ret.ok("msg","操作成功"));
    }

    public void empTable(){
        render("empTable.html");
    }

    public void workDaysForm(){
        String index=getPara("index");
        setAttr("index",index);
        render("workDaysForm.html");
    }

    public void holidaysIndex(){
        String id=getPara("id");
        setAttr("id",id);
        render("holidays.html");
    }

    public void getHolidaysByRule(){
        String ruleId=getPara("ruleId");

        PersOrgEmployeeCheckinRuleHolidays ruleHolidays=persOrgEmployeeCheckinRuleHolidaysService.getRuleHolidaysByRuleId(ruleId);
        if(ruleHolidays==null){
            renderJson(Ret.fail());
            return;
        }
        renderJson(Ret.ok("data",ruleHolidays));
    }

    public void saveHolidaysByRule(){
        String ruleId=getPara("ruleId");
        //1上班，2休息
        String type=getPara("type");
        String date=getPara("date");
        PersOrgEmployeeCheckinRuleHolidays ruleHolidays=persOrgEmployeeCheckinRuleHolidaysService.getRuleHolidaysByRuleId(ruleId);
        boolean flag=false;
        if(ruleHolidays==null){
            ruleHolidays=new PersOrgEmployeeCheckinRuleHolidays();
            ruleHolidays.setId(IdGen.getUUID());
            ruleHolidays.setRuleId(ruleId);
            ruleHolidays.setDelFlag("0");
            if("1".equals(type)){
                ruleHolidays.setWorkDays("[\""+date+"\"]");
            }else if("2".equals(type)){
                ruleHolidays.setRestDays("[\""+date+"\"]");
            }
            ruleHolidays.setCreateBy(AuthUtils.getUserId());
            ruleHolidays.setCreateDate(new Date());
            ruleHolidays.setUpdateBy(AuthUtils.getUserId());
            ruleHolidays.setUpdateDate(new Date());
            flag=ruleHolidays.save();
        }else{
            PersOrgEmployeeCheckinRuleHolidays newRuleHolidays=new PersOrgEmployeeCheckinRuleHolidays();
            newRuleHolidays.setId(IdGen.getUUID());
            newRuleHolidays.setRuleId(ruleId);
            newRuleHolidays.setDelFlag("0");
            if("1".equals(type)){
                if(StrKit.notBlank(ruleHolidays.getWorkDays())){
                    JSONArray workArray=JSON.parseArray(ruleHolidays.getWorkDays());
                    workArray.add(date);
                    newRuleHolidays.setWorkDays(JSON.toJSONString(workArray));
                }else{
                    newRuleHolidays.setWorkDays("[\""+date+"\"]");
                }

                if(StrKit.notBlank(ruleHolidays.getRestDays())){
                    JSONArray restArray=JSON.parseArray(ruleHolidays.getRestDays());
                    restArray.remove(date);
                    newRuleHolidays.setRestDays(JSON.toJSONString(restArray));
                }
               /* newRuleHolidays.setRestDays(ruleHolidays.getRestDays());*/
            }else if("2".equals(type)){
                if(StrKit.notBlank(ruleHolidays.getRestDays())){
                    JSONArray restArray=JSON.parseArray(ruleHolidays.getRestDays());
                    restArray.add(date);
                    newRuleHolidays.setRestDays(JSON.toJSONString(restArray));
                }else{
                    newRuleHolidays.setRestDays("[\""+date+"\"]");
                }

                if(StrKit.notBlank(ruleHolidays.getWorkDays())){
                    JSONArray workArray=JSON.parseArray(ruleHolidays.getWorkDays());
                    workArray.remove(date);
                    newRuleHolidays.setWorkDays(JSON.toJSONString(workArray));
                }

                /*newRuleHolidays.setWorkDays(ruleHolidays.getWorkDays());*/
            }
            newRuleHolidays.setCreateBy(AuthUtils.getUserId());
            newRuleHolidays.setCreateDate(new Date());
            newRuleHolidays.setUpdateBy(AuthUtils.getUserId());
            newRuleHolidays.setUpdateDate(new Date());
            flag=newRuleHolidays.save();
            if(flag){
                ruleHolidays.setUpdateBy(AuthUtils.getUserId());
                ruleHolidays.setUpdateDate(new Date());
                ruleHolidays.setDelFlag("1");
                ruleHolidays.update();
            }
        }
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delHolidaysDate(){
        String ruleId=getPara("ruleId");
        //1上班，2休息
        String type=getPara("type");
        String date=getPara("date");
        PersOrgEmployeeCheckinRuleHolidays ruleHolidays=persOrgEmployeeCheckinRuleHolidaysService.getRuleHolidaysByRuleId(ruleId);
        if("1".equals(type)){
            if(StrKit.notBlank(ruleHolidays.getWorkDays())){
                JSONArray workArray=JSON.parseArray(ruleHolidays.getWorkDays());
                workArray.remove(date);
                ruleHolidays.setWorkDays(JSON.toJSONString(workArray));
            }
        }else if("2".equals(type)){
            if(StrKit.notBlank(ruleHolidays.getRestDays())){
                JSONArray restArray=JSON.parseArray(ruleHolidays.getRestDays());
                restArray.remove(date);
                ruleHolidays.setRestDays(JSON.toJSONString(restArray));
            }
        }
        ruleHolidays.setUpdateBy(AuthUtils.getUserId());
        ruleHolidays.setUpdateDate(new Date());
        if(ruleHolidays.update()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    public void saveCheckinRule(){
        String data=getPara("data");
        if(StrKit.isBlank(data) || !data.startsWith("{") || !data.endsWith("}")){
            renderJson(Ret.fail("msg","参数格式不正确"));
            return;
        }
        JSONObject dataObj=JSON.parseObject(data);
        PersOrgEmployeeCheckinRule checkinRule=new PersOrgEmployeeCheckinRule();
        boolean isInsert=false;

        JSONArray deptIdArray=dataObj.getJSONArray("deptIds");
        JSONArray empIdArray=dataObj.getJSONArray("empIds");
        if(StrKit.notBlank(dataObj.getString("id"))){
            checkinRule.setId(dataObj.getString("id"));

            if(Db.findFirst("select * from pers_org_employee_checkin_rule where del_flag='0' and " +
                    " id<>? and `name`=? ",dataObj.getString("id"),dataObj.getString("name"))!=null){
                renderJson(Ret.fail("msg","该规则名称已存在，请修改规则名称"));
                return;
            }

        }else{
            isInsert=true;
            checkinRule.setId(IdGen.getUUID());

            if(Db.findFirst("select * from pers_org_employee_checkin_rule where del_flag='0' and " +
                    " `name`=? ",dataObj.getString("name"))!=null){
                renderJson(Ret.fail("msg","该规则名称已存在，请修改规则名称"));
                return;
            }
        }

        //判断部门id、员工id是否已添加
        if(deptIdArray!=null && deptIdArray.size()>0){
            String str="";
            List<String> params=new ArrayList<>();
            for(int i=0;i<deptIdArray.size();i++){
                str+="?,";
                params.add(deptIdArray.getString(i));
            }
            str=str.substring(0,str.length()-1);
            String sql="select c.org_name as orgName  from pers_org_employee_checkin_rule a INNER JOIN " +
                    " pers_org_employee_checkin_rule_range b on a.id=b.rule_id INNER JOIN pers_org c on c.id=b.checkin_id " +
                    " where b.checkin_id in ("+str+") and a.del_flag='0' and b.del_flag='0' and b.`type`='1' ";
            if(!isInsert){
                sql+=" and a.id<> ?";
                params.add(checkinRule.getId());
            }
            List<Record> deptRangeList=Db.find(sql,params.toArray());
            if(deptRangeList!=null && deptRangeList.size()>0){
                renderJson(Ret.fail("msg",deptRangeList.get(0).getStr("orgName")+" 已经存在打卡规则，请不要重复添加"));
                return;
            }
        }

        if(empIdArray!=null && empIdArray.size()>0){
            String str="";
            List<String> params=new ArrayList<>();
            for(int i=0;i<empIdArray.size();i++){
                str+="?,";
                params.add(empIdArray.getString(i));
            }
            str=str.substring(0,str.length()-1);
            String sql="select c.full_name as fullName  from pers_org_employee_checkin_rule a INNER JOIN " +
                    " pers_org_employee_checkin_rule_range b on a.id=b.rule_id INNER JOIN pers_org_employee c on c.id=b.checkin_id " +
                    " where b.checkin_id in ("+str+") and a.del_flag='0' and b.del_flag='0' and b.`type`='2' ";
            if(!isInsert){
                sql+=" and a.id<> ?";
                params.add(checkinRule.getId());
            }
            List<Record> empRangeList=Db.find(sql,params.toArray());
            if(empRangeList!=null && empRangeList.size()>0){
                renderJson(Ret.fail("msg",empRangeList.get(0).getStr("fullName")+" 已经存在打卡规则，请不要重复添加"));
                return;
            }
        }

        checkinRule.setName(dataObj.getString("name"));
        checkinRule.setType(dataObj.getString("type"));
        if(isInsert){
            checkinRule.setDelFlag("0");
            checkinRule.setCreateBy(AuthUtils.getUserId());
            checkinRule.setCreateDate(new Date());
        }
        checkinRule.setUpdateBy(AuthUtils.getUserId());
        checkinRule.setUpdateDate(new Date());



        List<PersOrgEmployeeCheckinRuleRange> checkinRuleRangeList=new ArrayList<>();
        List<PersOrgEmployeeCheckinRuleCheckinDate> checkinDateList=new ArrayList<>();

        List<PersOrgEmployeeCheckinRuleSchedule> addScheduleList=new ArrayList<>();
        List<PersOrgEmployeeCheckinRuleSchedule> updateScheduleList=new ArrayList<>();
        List<PersOrgEmployeeCheckinRuleEmpSchedule> saveRuleEmpScheduleList=new ArrayList<>();
        List<PersOrgEmployeeCheckinRuleEmpSchedule> updateRuleEmpScheduleList=new ArrayList<>();


        if(deptIdArray!=null && deptIdArray.size()>0){
            for(int i=0;i<deptIdArray.size();i++){
                PersOrgEmployeeCheckinRuleRange checkinRuleRange=new PersOrgEmployeeCheckinRuleRange();
                checkinRuleRange.setId(IdGen.getUUID());
                checkinRuleRange.setRuleId(checkinRule.getId());
                checkinRuleRange.setType("1");
                checkinRuleRange.setCheckinId(deptIdArray.getString(i));
                checkinRuleRange.setCreateBy(AuthUtils.getUserId());
                checkinRuleRange.setCreateDate(new Date());
                checkinRuleRange.setUpdateBy(AuthUtils.getUserId());
                checkinRuleRange.setUpdateDate(new Date());
                checkinRuleRange.setDelFlag("0");
                checkinRuleRangeList.add(checkinRuleRange);
            }
        }
        if(empIdArray!=null && empIdArray.size()>0){
            for(int i=0;i<empIdArray.size();i++){
                PersOrgEmployeeCheckinRuleRange checkinRuleRange=new PersOrgEmployeeCheckinRuleRange();
                checkinRuleRange.setId(IdGen.getUUID());
                checkinRuleRange.setRuleId(checkinRule.getId());
                checkinRuleRange.setType("2");
                checkinRuleRange.setCheckinId(empIdArray.getString(i));
                checkinRuleRange.setCreateBy(AuthUtils.getUserId());
                checkinRuleRange.setCreateDate(new Date());
                checkinRuleRange.setUpdateBy(AuthUtils.getUserId());
                checkinRuleRange.setUpdateDate(new Date());
                checkinRuleRange.setDelFlag("0");
                checkinRuleRangeList.add(checkinRuleRange);
            }
        }
        if("1".equals(checkinRule.getType())){
            JSONArray checkinTimeDataArray=dataObj.getJSONArray("checkinTimeData");
            if(checkinTimeDataArray!=null && checkinTimeDataArray.size()>0){
                for(int i=0;i<checkinTimeDataArray.size();i++){
                    PersOrgEmployeeCheckinRuleCheckinDate checkinDate=new PersOrgEmployeeCheckinRuleCheckinDate();
                    checkinDate.setId(IdGen.getUUID());
                    checkinDate.setWorkDays(JSON.toJSONString(checkinTimeDataArray.getJSONObject(i).getJSONArray("workDays")));
                    checkinDate.setNoneedOffwork(checkinTimeDataArray.getJSONObject(i).getString("noneedOffwork"));
                    checkinDate.setIsManyCheckin(checkinTimeDataArray.getJSONObject(i).getString("isManyCheckin"));
                    checkinDate.setRuleId(checkinRule.getId());
                    checkinDate.setDelFlag("0");
                    checkinDate.setCreateBy(AuthUtils.getUserId());
                    checkinDate.setCreateDate(new Date());
                    checkinDate.setUpdateBy(AuthUtils.getUserId());
                    checkinDate.setUpdateDate(new Date());
                    JSONArray checkTimeArray=new JSONArray();
                    JSONArray checkTimes=checkinTimeDataArray.getJSONObject(i).getJSONArray("checkTimes");
                    for(int j=0;j<checkTimes.size();j++){
                        JSONObject checkTime=checkTimes.getJSONObject(j);
                        JSONArray timeArray=new JSONArray();
                        timeArray.add(checkTime.getString("work"));
                        timeArray.add(checkTime.getString("offWork"));
                        checkTimeArray.add(timeArray);
                    }
                    checkinDate.setCheckinTime(JSON.toJSONString(checkTimeArray));
                    checkinDateList.add(checkinDate);
                }
            }
        }else if("2".equals(checkinRule.getType())){
            JSONArray scheduleArray=dataObj.getJSONArray("scheduleArray");
            JSONArray scheduleDataArray=dataObj.getJSONArray("scheduleDataArray");
            Map<String,PersOrgEmployeeCheckinRuleSchedule> scheduleMap=new HashMap<>();
            if(scheduleArray!=null && scheduleArray.size()>0){
                for(int i=0;i<scheduleArray.size();i++){
                    JSONObject schedule=scheduleArray.getJSONObject(i);
                    PersOrgEmployeeCheckinRuleSchedule ruleSchedule=new PersOrgEmployeeCheckinRuleSchedule();
                    JSONArray checkTimes=schedule.getJSONArray("checkTimes");
                    JSONArray saveCheckTimes=new JSONArray();
                    for(int j=0;j<checkTimes.size();j++){
                        JSONObject checkTimeObj=checkTimes.getJSONObject(j);
                        JSONArray saveCheckTimeArray=new JSONArray();
                        saveCheckTimeArray.add(0,checkTimeObj.getString("work"));
                        saveCheckTimeArray.add(1,checkTimeObj.getString("offWork"));
                        saveCheckTimes.add(saveCheckTimeArray);
                    }
                    ruleSchedule.setId(IdGen.getUUID());
                    ruleSchedule.setRuleId(checkinRule.getId());
                    ruleSchedule.setDelFlag("0");
                    ruleSchedule.setIsManyCheckin("0");
                    ruleSchedule.setName(schedule.getString("name"));
                    ruleSchedule.setCheckinTime(JSON.toJSONString(saveCheckTimes));
                    ruleSchedule.setCreateBy(AuthUtils.getUserId());
                    ruleSchedule.setCreateDate(new Date());
                    ruleSchedule.setUpdateBy(AuthUtils.getUserId());
                    ruleSchedule.setUpdateDate(new Date());
                    addScheduleList.add(ruleSchedule);
                    scheduleMap.put(ruleSchedule.getName(),ruleSchedule);
                }

            }
            JSONObject saveEmpScheduleData=new JSONObject();
            if(scheduleDataArray!=null && scheduleDataArray.size()>0){
                for(int i=0;i<scheduleDataArray.size();i++){
                    JSONObject scheduleData=scheduleDataArray.getJSONObject(i);
                    String date=scheduleData.getString("date");
                    JSONArray selectedArray=scheduleData.getJSONArray("selectedArray");
                    for(int j=0;j<selectedArray.size();j++){
                        JSONObject selectedData=selectedArray.getJSONObject(j);
                        String empId=selectedData.getString("value");
                        if(saveEmpScheduleData.containsKey(empId)){
                            JSONObject empSchedule=saveEmpScheduleData.getJSONObject(empId);
                            empSchedule.put(date,scheduleMap.get(selectedData.getString("selectedName")).getId());
                        }else{
                            JSONObject empSchedule=new JSONObject();
                            empSchedule.put(date,scheduleMap.get(selectedData.getString("selectedName")).getId());
                            saveEmpScheduleData.put(empId,empSchedule);
                        }
                    }
                }
            }

            if(saveEmpScheduleData.size()>0){
                for(String key:saveEmpScheduleData.keySet()){
                    JSONObject empScheduleObj=saveEmpScheduleData.getJSONObject(key);
                    PersOrgEmployeeCheckinRuleEmpSchedule empSchedule=new PersOrgEmployeeCheckinRuleEmpSchedule();
                    empSchedule.setId(IdGen.getUUID());
                    empSchedule.setRuleId(checkinRule.getId());
                    empSchedule.setEmpId(key);
                    empSchedule.setScheduleConfig(JSON.toJSONString(empScheduleObj));
                    empSchedule.setDelFlag("0");
                    empSchedule.setCreateBy(AuthUtils.getUserId());
                    empSchedule.setCreateDate(new Date());
                    empSchedule.setUpdateBy(AuthUtils.getUserId());
                    empSchedule.setUpdateDate(new Date());
                    saveRuleEmpScheduleList.add(empSchedule);
                }
            }
        }else if("3".equals(checkinRule.getType())){
            JSONArray workDays=dataObj.getJSONArray("workDays");
            PersOrgEmployeeCheckinRuleCheckinDate checkinDate=new PersOrgEmployeeCheckinRuleCheckinDate();
            checkinDate.setId(IdGen.getUUID());
            checkinDate.setWorkDays(JSON.toJSONString(workDays));
            checkinDate.setNoneedOffwork(dataObj.getString("noneedOffwork"));
            checkinDate.setRuleId(checkinRule.getId());
            checkinDate.setDelFlag("0");
            checkinDate.setCreateBy(AuthUtils.getUserId());
            checkinDate.setCreateDate(new Date());
            checkinDate.setUpdateBy(AuthUtils.getUserId());
            checkinDate.setUpdateDate(new Date());
            checkinDateList.add(checkinDate);
        }
        boolean finalIsInsert=isInsert;
        boolean flag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    boolean suc=false;
                    if(finalIsInsert){
                        suc=checkinRule.save();
                    }else{
                        suc=checkinRule.update();
                    }
                    if(suc){
                        //作废之前的数据
                        if(!finalIsInsert) {
                            persOrgEmployeeCheckinRuleCheckinDateService.delCheckinDateByRuleId(checkinRule.getId(),AuthUtils.getUserId());
                            persOrgEmployeeCheckinRuleRangeService.delRangeByRuleId(checkinRule.getId(),AuthUtils.getUserId());
                            persOrgEmployeeCheckinRuleEmpScheduleService.deltEmpScheduleByRuleId(checkinRule.getId(),AuthUtils.getUserId());
                            persOrgEmployeeCheckinRuleScheduleService.delRuleScheduleByRuleId(checkinRule.getId(),AuthUtils.getUserId());
                        }
                        Db.batchSave(checkinRuleRangeList,checkinRuleRangeList.size());
                        Db.batchSave(checkinDateList,checkinDateList.size());
                        Db.batchSave(addScheduleList,addScheduleList.size());
                        Db.batchSave(saveRuleEmpScheduleList,saveRuleEmpScheduleList.size());
                        return suc;
                    }else{
                        return suc;
                    }
                }catch (Exception e){
                    e.printStackTrace();
                    return false;
                }
            }
        });
        if(flag){
            renderJson(Ret.ok("msg","保存成功"));
        }else{
            renderJson(Ret.fail("msg","保存失败"));
        }
    }

    public void genRepairScheduleCheckinData(){
        String id=getPara("id");
        String startDate=getPara("startDate");
        String endDate=getPara("endDate");

        if(StrKit.isBlank(id) || StrKit.isBlank(startDate)  || StrKit.isBlank(endDate)){
            renderJson(Ret.fail("msg","id不能为空"));
            return;
        }
        List<Record> ruleRangeList=Db.find("select  * from pers_org_employee_checkin_rule_range where rule_id=? and del_flag='0' ",id);
        List<String> ruleEmpIdList=new ArrayList<>();
        List<String> deptIdList=new ArrayList<>();
        String str="";
        for(Record record:ruleRangeList){
            if("1".equals(record.getStr("type"))){
                str+="?,";
                deptIdList.add(record.getStr("checkin_id"));
            }else{
                ruleEmpIdList.add(record.getStr("checkin_id"));
            }
        }
        if(deptIdList.size()>0){
            str=str.substring(0,str.length()-1);
            List<String> deptEmpIdList=Db.query("select emp_id from pers_org_employee_rel where relationship_type='dept' and relationship_id in ("+str+")",deptIdList.toArray());
            ruleEmpIdList.addAll(deptEmpIdList);
        }

        List<Date> dateList=DateUtils.getBetweenDates(DateUtils.parseDate(startDate),DateUtils.parseDate(endDate));
        Date delDate=new Date();
        for(String empId:ruleEmpIdList){
            PersOrgEmployee employee = persOrgEmployeeService.findById(empId);
            if("quit".equals(employee.getArchiveStatus())){
                if(employee.getQuitTime()==null || Integer.valueOf(DateUtils.formatDate(DateUtils.parseDate(startDate),"yyyyMMdd"))>Integer.valueOf(DateUtils.formatDate(employee.getQuitTime(),"yyyyMMdd"))){
                    continue;
                }
            }

            Map<String,Object> startDateMap=employeeDelayCheckinCron.getEmpOverTimeNow(empId,dateList.get(0),new String[]{"3"});
            Map<String,Object> endDateMap=employeeDelayCheckinCron.getEmpOverTimeNow(empId,dateList.get(dateList.size()-1),new String[]{"3"});
            Date delMinCheckinTime = (Date) startDateMap.get("todayMinCheckinTime");
            Date delMaxCheckinTime = (Date) endDateMap.get("todayMaxCheckinTime");

            Db.update("update pers_org_employee_checkin_record set del_flag='1',update_date=? where emp_id=? and " +
                    "checkin_time BETWEEN ? and ? and del_flag='0'",delDate,empId,delMinCheckinTime,delMaxCheckinTime);
            //作废日报
            Db.update("update pers_org_employee_checkin_day_summary set del_flag='1',update_date=? where emp_id=? and " +
                    "summary_date BETWEEN ? and ? and del_flag='0'",delDate,empId,startDate+" 00:00:00",endDate+" 23:59:59");
            List<PersOrgEmployeeCheckinWxRecord> checkinWxRecordList=persOrgEmployeeCheckinWxRecordService.getEmpWxCheckinRecordList(empId,startDate,endDate);
            for(PersOrgEmployeeCheckinWxRecord wxRecord:checkinWxRecordList){
                try {
                    employeeDelayCheckinCron.empCheckinNow(wxRecord,wxRecord.getCheckinTime());
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
            List<PersOrgEmployeeCheckinFillCard> fillCardList=persOrgEmployeeCheckinFillCardService
                    .getEmpCheckinFillCardList(empId,DateUtils.formatDate(delMinCheckinTime,"yyyy-MM-dd HH:mm:ss"),DateUtils.formatDate(delMaxCheckinTime,"yyyy-MM-dd HH:mm:ss"));

            for (PersOrgEmployeeCheckinFillCard fillCard : fillCardList) {
                PersOrgEmployeeCheckinWxRecord wxRecord=persOrgEmployeeCheckinWxRecordService.getWxCheckinRecord(fillCard.getEmpId());
                wxRecord.setEmpId(fillCard.getEmpId());
                wxRecord.setCheckinTime(fillCard.getFillCardTime());
                employeeDelayCheckinCron.empCheckinNow(wxRecord,wxRecord.getCheckinTime());
            }



            for (Date date : dateList) {

                if("quit".equals(employee.getArchiveStatus())){
                    if(Integer.valueOf(DateUtils.formatDate(date,"yyyyMMdd"))>Integer.valueOf(DateUtils.formatDate(employee.getQuitTime(),"yyyyMMdd"))){
                        continue;
                    }
                }
                Map<String,Object> map=employeeDelayCheckinCron.getEmpOverTimeNow(empId,date,new String[]{"3"});
                employeeDelayCheckinCron.employeeCheckinDaySummary(map);
            }
        }

        renderJson(Ret.ok("msg","操作成功"));
    }

    public static void main(String[] args) {
        Date date=DateUtils.parseDate("2022-03-31");
        //前一天最后一次下班时间
        Calendar sameDayLastOffWorkCalendar=Calendar.getInstance();
        sameDayLastOffWorkCalendar.setTime(DateUtils.getNextDay(date,-1));
        sameDayLastOffWorkCalendar.set(Calendar.HOUR_OF_DAY,30);
        sameDayLastOffWorkCalendar.set(Calendar.MINUTE,00);
        sameDayLastOffWorkCalendar.set(Calendar.SECOND,0);

        //当天的首次上班时间
        Calendar nextDayFirstWorkCalendar=Calendar.getInstance();
        nextDayFirstWorkCalendar.setTime(date);
        nextDayFirstWorkCalendar.set(Calendar.HOUR_OF_DAY,12);
        nextDayFirstWorkCalendar.set(Calendar.MINUTE,0);
        nextDayFirstWorkCalendar.set(Calendar.SECOND,0);

        System.out.println(DateUtils.formatDate(sameDayLastOffWorkCalendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
        System.out.println(DateUtils.formatDate(nextDayFirstWorkCalendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
    }

    public void scheduleForm(){
        /*String id=getPara("id");
        String ruleId=getPara("ruleId");
        if(StrKit.notBlank(id)){
            PersOrgEmployeeCheckinRuleSchedule ruleSchedule=persOrgEmployeeCheckinRuleScheduleService.findById(id);
            setAttr("ruleSchedule",ruleSchedule);
        }
        setAttr("ruleId",ruleId);*/
        String index=getPara("index");
        setAttr("index",index);
        render("scheduleForm.html");
    }

    public void delSchedule(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderJson(Ret.fail("msg","操作失败"));
            return;
        }
        PersOrgEmployeeCheckinRuleSchedule ruleSchedule=new PersOrgEmployeeCheckinRuleSchedule();
        ruleSchedule.setId(id);
        ruleSchedule.setDelFlag("1");
        ruleSchedule.setUpdateBy(AuthUtils.getUserId());
        ruleSchedule.setUpdateDate(new Date());
        if(ruleSchedule.update()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void findEmpByDeptId(){
        String deptIds=getPara("deptIds");
        if(StrKit.isBlank(deptIds) || !deptIds.startsWith("[") || !deptIds.endsWith("]")){
            renderJson(Ret.fail("msg","部门不能为空"));
            return;
        }
        JSONArray deptIdArray=JSON.parseArray(deptIds);
        if(deptIdArray.size()==0){
            renderJson(Ret.fail("msg","部门不能为空"));
            return;
        }
        List<PersOrg> orgList=new ArrayList<>();
        List<String> deptIdList=new ArrayList<>();

        List<ZTree> zTreeList=persOrgService.allOrgTree();
        for(int i=0;i<deptIdArray.size();i++){
            deptIdList.add(deptIdArray.getString(i));
            persOrgService.findChildren(zTreeList,orgList,deptIdArray.getString(i));
        }

        for(PersOrg org:orgList){
            deptIdList.add(org.getId());
        }
        List<PersOrgEmployee> employeeList=persOrgEmployeeService.findEmpListByDepts(deptIdList);
        renderJson(Ret.ok("data",employeeList));
    }

    public void saveSchedule(){
        PersOrgEmployeeCheckinRuleSchedule ruleSchedule=getBean(PersOrgEmployeeCheckinRuleSchedule.class,"",true);
        ruleSchedule.setName(ruleSchedule.getName().replace(" ",""));
        if(StrKit.isBlank(ruleSchedule.getId())){
            String sql="select * from pers_org_employee_checkin_rule_schedule where rule_id=? and del_flag='0' and `name`=? ";
            if(Db.query(sql,ruleSchedule.getRuleId(),ruleSchedule.getName())!=null){
                renderJson(Ret.fail("msg","该名字已存在，请勿重复添加"));
                return;
            }
        }else{
            String sql="select * from pers_org_employee_checkin_rule_schedule where rule_id=? and del_flag='0' and `name`=? and id<>? ";
            if(Db.query(sql,ruleSchedule.getRuleId(),ruleSchedule.getName(),ruleSchedule.getId())!=null){
                renderJson(Ret.fail("msg","该名字已存在，请勿重复添加"));
                return;
            }
        }
        boolean flag=persOrgEmployeeCheckinRuleScheduleService.saveSchedule(ruleSchedule,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.fail("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }

    public void checkinRuleWhitePage(){
        int pageNumber=getParaToInt("page");
        int pageSize=getParaToInt("limit");
        PersOrgEmployeeCheckinRuleWhite ruleWhite=getBean(PersOrgEmployeeCheckinRuleWhite.class,"",true);

        Page<Record> page=persOrgEmployeeCheckinRuleWhiteService.findCheckinRuleWhitePage(pageNumber,pageSize,ruleWhite);
        renderJson(new DataTable<Record>(page));
    }

    public void saveCheckinRuleWhite(){
        PersOrgEmployeeCheckinRuleWhite ruleWhite=getBean(PersOrgEmployeeCheckinRuleWhite.class,"",true);
        if(StrKit.isBlank(ruleWhite.getId())){
            if(Db.findFirst("select * from pers_org_employee_checkin_rule_white where role_id=? and del_flag='0'",ruleWhite.getRoleId())!=null){
                renderJson(Ret.fail("msg","该角色已添加过了，请勿重复添加"));
                return;
            }
        }else{
            if(Db.findFirst("select * from pers_org_employee_checkin_rule_white where role_id=? and del_flag='0' and id<>?",ruleWhite.getRoleId(),ruleWhite.getId())!=null){
                renderJson(Ret.fail("msg","该角色已添加过了，请勿重复添加"));
                return;
            }
        }

        boolean flag=persOrgEmployeeCheckinRuleWhiteService.saveCheckinRuleWhite(ruleWhite,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void ruleWhiteIndex(){
        render("ruleWhiteIndex.html");
    }

    public void ruleWhiteForm(){
        List<MainRole> roleList=mainRoleService.findEnabledRole();
        setAttr("roleList",roleList);
        render("ruleWhiteForm.html");
    }
}
