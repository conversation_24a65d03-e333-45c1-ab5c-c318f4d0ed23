package com.cszn.personnel.web.controller.notice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.main.MainRoleService;
import com.cszn.integrated.service.api.pers.PersNoticeReceiverService;
import com.cszn.integrated.service.api.pers.PersNoticeService;
import com.cszn.integrated.service.api.pers.PersNoticeTypeService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.pers.*;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.MsgCategory;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.SysMessage;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Description 公告管理
 * <AUTHOR>
 * @Date 2019/7/17
 **/
@RequestMapping(value="/pers/notice", viewPath="/modules_page/pers/notice")
public class NoticeController extends BaseController {

    @Inject
    private PersNoticeService persNoticeService;
    @Inject
    private PersNoticeTypeService persNoticeTypeService;
    @Inject
    private PersNoticeReceiverService persNoticeReceiverService;
    @Inject
    private MainBranchOfficeService mainBranchOfficeService;
    @Inject
	private PersOrgEmployeeService persOrgEmployeeService;
    @Inject
    MainRoleService mainRoleService;

    /**
     * 公告发布页面
     */
    public void index(){
    	List<PersNoticeType> typeList = persNoticeTypeService.getNuDelList();
    	setAttr("typeList",typeList);
    	setAttr("empList", Db.find("select e.full_name,u.user_id from pers_org_employee e inner join pers_emp_user u on u.emp_id=e.id where e.del_flag='0' and e.archive_status='incumbency' order by e.create_date"));
        render("noticeIndex.html");
    }

    /**
     * 公告查阅页面
     */
    public void lookIndex(){
    	List<PersNoticeType> typeList = persNoticeTypeService.getNuDelList();
    	setAttr("typeList",typeList);
        render("lookIndex.html");
    }
    
    /**
     * 公告查阅名单页面
     */
    public void lookNameIndex(){
    	String noticeId = getPara("id");
    	setAttr("noticeId",noticeId);
    	render("lookNameIndex.html");
    }

    /**
     * 发布公告列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        PersNotice notice = getBean(PersNotice.class,"",true);
        Page<PersNotice> page = persNoticeService.findNoticePage(getParaToInt("page"), getParaToInt("limit"),notice);
        renderJson(new DataTable<PersNotice>(page));
    }

    /**
     * 公告查阅列表
     */
    @Clear(LogInterceptor.class)
    public void noticeLook(){
        PersNotice notice = getBean(PersNotice.class,"",true);
        notice.setReleaseStatus("release");
        Page<Record> page = persNoticeService.lookNoticePage(getParaToInt("page"), getParaToInt("limit"), notice, AuthUtils.getUserId());
        renderJson(new DataTable<Record>(page));
    }
    
    /**
     * 公告查阅名单列表
     */
    @Clear(LogInterceptor.class)
    public void noticeLookName(){
    	Page<Record> page = persNoticeService.lookNamePageList(getParaToInt("page",1), getParaToInt("limit",999999999), getPara("noticeId"), getPara("isRead"));
    	renderJson(new DataTable<Record>(page));
    }
    
    /**
     * 跳转新增 界面
     */
    public void add(){
    	List<PersNoticeType> typeList = persNoticeTypeService.getNuDelList();
    	setAttr("typeList",typeList);
    	setAttr("notice",new PersNotice());
    	setAttr("commonUpload", Global.commonUpload);
		render("noticeForm.html");
    }


    /**
     * 跳转修改 界面
     */
    public void edit(){
        String id = getPara("id");
        PersNotice notice = persNoticeService.get(id);
        if(notice != null){
            PersNoticeType noticeType = persNoticeTypeService.get(notice.getNoticeTypeId());
            setAttr("noticeType",noticeType);
        }
        List<PersNoticeType> typeList = persNoticeTypeService.getNuDelList();
        setAttr("notice",notice);
        setAttr("typeList",typeList);
        setAttr("commonUpload", Global.commonUpload);
        setAttr("orgIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='org' and notice_id=? group by notice_id", id));
        setAttr("roleIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='role' and notice_id=? group by notice_id", id));
        setAttr("groupIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='group' and notice_id=? group by notice_id", id));
        setAttr("userIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='user' and notice_id=? group by notice_id", id));
        render("noticeForm.html");
    }
    
    
    /**
     * 跳转明细 界面
     */
    public void detail(){
    	final String id = getPara("id");
    	setAttr("notice",persNoticeService.get(id));
    	setAttr("commonUpload", Global.commonUpload);
    	setAttr("orgIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='org' and notice_id=? group by notice_id", id));
        setAttr("roleIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='role' and notice_id=? group by notice_id", id));
        setAttr("groupIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='group' and notice_id=? group by notice_id", id));
        setAttr("userIds", Db.queryStr("select group_concat(relation_id)relationIds from pers_notice_relation where relation_type='user' and notice_id=? group by notice_id", id));
        render("noticeDetail.html");
    }
    
    
    /**
     * 跳转查看 界面
     */
    public void look(){
    	final String id = getPara("id");
    	PersNotice notice = persNoticeService.get(id);
    	if(notice!=null) {
    		PersNoticeReceiver receiver = persNoticeReceiverService.findByNoticeIdAndUserId(id, AuthUtils.getUserId());
    		if(receiver!=null) {
    			if("0".equals(receiver.getIsRead())) {
    				PersNoticeReceiver model = new PersNoticeReceiver();
    				model.setId(receiver.getId());
    				model.setIsRead("1");
    				model.setIsReadTime(new Date());
    				model.update();
    			}
    		}else {
    			PersNoticeReceiver model = new PersNoticeReceiver();
    			model.setId(IdGen.getUUID());
    			model.setNoticeId(id);
    			model.setUserId(AuthUtils.getUserId());
    			model.setIsRead("1");
    			model.setIsReadTime(new Date());
    			model.save();
    		}
    	}
    	setAttr("notice",notice);
    	setAttr("commonUpload", Global.commonUpload);
		render("lookForm.html");
    }


    /**
     * 获取文件和查阅人
     */
    @Clear(LogInterceptor.class)
    public void getFilesAndUsers(){
        String id = getPara("id");
        PersNotice notice = persNoticeService.get(id);
        if(notice != null){
            //获取附件
            List<Record> fileList = Db.find("select id,relation_id as relationId,file_id as fileId,url,create_time as createTime " +
                    "from pers_notice_enclosure where relation_id = ?",notice.getId());
            //获取查阅人
            List<Record> userList = Db.find("select r.id,r.notice_id as noticeId,r.org_id as orgId,r.dep_id as depId," +
                    "r.user_id as userId,u.name,r.is_read as isRead,r.is_read_time as isReadTime from pers_notice_receiver r " +
                    "left join sys_user u on r.user_id = u.id where r.notice_id = ?",notice.getId());
            renderJson(Ret.ok("msg", "获取成功").set("fileList",fileList).set("userList",userList));
        }else{
            renderJson(Ret.ok("msg", "获取失败"));
        }
    }


    /**
     * 保存
     */
    public void save(){
    	String orgIds = getPara("orgIds");
    	String roleIds = getPara("roleIds");
    	String groupIds = getPara("groupIds");
    	String userIds = getPara("userIds");
        String fileUrl = getPara("fileUrl");
        String fileIds = getPara("fileIds");
        PersNotice notice = getBean(PersNotice.class,"notice",true);
//        final int paramCount = getParaToInt("paramCount");
        if(notice == null){ 
        	renderJson(Ret.fail("msg", "公告参数未传入")); 
        	return;
        }
        //处理查阅人
//        List<PersNoticeReceiver> receiverList = null;
//        if(paramCount > 0){
//            receiverList = new ArrayList<>(paramCount);
//            for (int i = 1; i <= paramCount; i++) {
//                final PersNoticeReceiver detail = getBean(PersNoticeReceiver.class,"itemDetailList["+i+"]");
//                if(detail != null && detail.getUserId() != null) {
//                    receiverList.add(detail);
//                }
//            }
//        }
        //处理关联数据
        List<PersNoticeRelation> relationList = new ArrayList<PersNoticeRelation>();
        if(StringUtils.isNotBlank(orgIds)) {
        	List<String> orgIdList = new ArrayList<String>(Arrays.asList(orgIds.split(",")));
        	for(String relationId : orgIdList) {
        		PersNoticeRelation model = new PersNoticeRelation();
        		model.setRelationType("org");
        		model.setRelationId(relationId);
//        		MainRole role = mainRoleService.findById(relationId);
//        		if(role!=null) {
//        			model.setRelationName(role.getRoleName());
//        		}
        		relationList.add(model);
        	}
        }
        if(StringUtils.isNotBlank(roleIds)) {
        	List<String> roleIdList = new ArrayList<String>(Arrays.asList(roleIds.split(",")));
        	for(String relationId : roleIdList) {
        		PersNoticeRelation model = new PersNoticeRelation();
        		model.setRelationType("role");
        		model.setRelationId(relationId);
//        		MainRole role = mainRoleService.findById(relationId);
//        		if(role!=null) {
//        			model.setRelationName(role.getRoleName());
//        		}
        		relationList.add(model);
        	}
        }
        if(StringUtils.isNotBlank(groupIds)) {
        	List<String> groupIdList = new ArrayList<String>(Arrays.asList(groupIds.split(",")));
        	for(String relationId : groupIdList) {
        		PersNoticeRelation model = new PersNoticeRelation();
        		model.setRelationType("group");
        		model.setRelationId(relationId);
//        		MainRole role = mainRoleService.findById(relationId);
//        		if(role!=null) {
//        			model.setRelationName(role.getRoleName());
//        		}
        		relationList.add(model);
        	}
        }
        if(StringUtils.isNotBlank(userIds)) {
        	List<String> userIdList = new ArrayList<String>(Arrays.asList(userIds.split(",")));
        	for(String relationId : userIdList) {
        		PersNoticeRelation model = new PersNoticeRelation();
        		model.setRelationType("user");
        		model.setRelationId(relationId);
//        		MainRole role = mainRoleService.findById(relationId);
//        		if(role!=null) {
//        			model.setRelationName(role.getRoleName());
//        		}
        		relationList.add(model);
        	}
        }
        //处理附件
        List<String> fileUrls = null;
        if(StringUtils.isNotBlank(fileUrl)) {
            fileUrls = new ArrayList<String>(Arrays.asList(fileUrl.split(",")));
        }
        //处理附件id
        List<String> fileIdList = null;
        if(StringUtils.isNotBlank(fileIds)){
            fileIdList = new ArrayList<>(Arrays.asList(fileIds.split(",")));
        }
        if(StrKit.isBlank(notice.getId())) {
        	if(StrKit.isBlank(notice.getOrgId())) {
        		notice.setOrgId(AuthUtils.getOrgId());
            }
        }
        String flag = persNoticeService.saveNotice(notice,fileUrls,fileIdList,relationList,AuthUtils.getUserId());
        if("suc".equals(flag)){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }

    /**
     * 发布
     */
    public void release(){
        String id = getPara("id");
        PersNotice notice = persNoticeService.get(id);
        if(notice == null){ renderJson(
    		Ret.fail("msg", "公告不存在，不可发布")); return; 
        }
        notice.setReleaseStatus("release");
        notice.setUpdateBy(AuthUtils.getUserId());
        notice.setReleaseTime(new Date());
        notice.setUpdateDate(new Date());
        if(notice.update()){
        	List<Record> noticeUserList = persNoticeService.noticeUserList(notice.getId());
        	for(Record record : noticeUserList) {
        		final String noticeId = record.getStr("notice_id");
        		final String userId = record.getStr("user_id");
        		SysMessage msg = new SysMessage();
        		msg.setId(IdGen.getUUID());
        		msg.setUserId(userId);
        		msg.setRelationId(noticeId);
        		msg.setBelongSystem(SystemType.PERSONNEL);
        		msg.setMsgCategory(MsgCategory.NOTICE);
        		msg.setMsgUrl("/pers/notice/look");
        		msg.setMsgTitle(notice.getNoticeTitle());
        		msg.setIsRead("0");
        		msg.setDelFlag(DelFlag.NORMAL);
        		msg.setCreateBy(userId);
        		msg.setCreateDate(new Date());
        		msg.setUpdateBy(userId);
        		msg.setUpdateDate(new Date());
        		msg.save();
        	}
            renderJson(Ret.ok("msg", "发布成功"));
        }else{
            renderJson(Ret.fail("msg", "发布失败"));
        }
    }

    /**
     * 撤回
     */
    public void withdraw(){
        String id = getPara("id");
        PersNotice notice = persNoticeService.get(id);
        if(notice == null){ renderJson(
    		Ret.fail("msg", "公告不存在，撤回失败")); return; 
        }
        notice.setReleaseStatus("draft");
        notice.setUpdateBy(AuthUtils.getUserId());
        notice.setReleaseTime(null);//发布时间置为空
        notice.setUpdateDate(new Date());
        if(notice.update()){
        	Db.update("update sys_message set del_flag='1' where relation_id = ?",notice.getId());
            renderJson(Ret.ok("msg", "撤回成功"));
        }else{
            renderJson(Ret.fail("msg", "撤回失败"));
        }
    }
    
    /**
     * 作废公告
     */
    public void delete(){
        String id = getPara("id");
        PersNotice notice = persNoticeService.get(id);
        if(notice != null){
            boolean flag = persNoticeService.delNotice(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }

    /**
     * 判断是否可查阅公告
     */
    public void isPowerForLook(){
        String id = getPara("id");
        PersNotice notice = persNoticeService.get(id);
        if(notice != null){
            //确定是否查阅
            List<Record> receiverList = Db.find("select user_id as userId from pers_notice_receiver where notice_id = ?",notice.getId());
            List<String> userList = new ArrayList<>();
            if(receiverList != null && receiverList.size() > 0){
                for (Record item : receiverList) {
                    userList.add(item.getStr("userId"));
                }
            }
            //判断此人是否位于查阅人之中
            int flag = 0;
            if(userList != null && userList.size() > 0) {
                for (String item : userList) {
                    if (item.equals(AuthUtils.getUserId())) {
                        Db.update("update pers_notice_receiver set is_read = ?,is_read_time = ? where user_id = ? and notice_id = ?",
                                "1",new Date(),AuthUtils.getUserId(),notice.getId());
                        flag = 1;
                        break;
                    }
                }
            }
            if(notice.getCreateBy() == null || notice.getCreateBy().equals(AuthUtils.getUserId())){//公告创建人查阅
                flag = 1;
            }
            if(flag == 0){
                renderJson(Ret.fail("msg", "您没有权限查阅此公告"));
            }else{
                renderJson(Ret.ok("msg", "可查阅"));
            }
        }
    }


    /**
     * 跳转选择分公司查阅人界面
     */
    public void chooseUser(){
        List<MainBranchOffice> officeList = mainBranchOfficeService.getUnDelBranchOffice();
        setAttr("officeList",officeList);
        render("userInfo.html");
    }


    /**
     * 用于查阅人展示界面
     */
    @Clear(LogInterceptor.class)
    public void empUserPage(){
    	PersOrgEmployee orgEmployee = getBean(PersOrgEmployee.class, "", true);
        String userId=null;
		if(!"system_admin".equals(AuthUtils.getLoginUser().getUserType())){
			userId=AuthUtils.getUserId();
		}
        Page<Record> userPage = persOrgEmployeeService.empUserPage(orgEmployee,userId,getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<Record>(userPage));
    }
    
    /**
     * 批量提醒
     */
    public void noticeRemind(){
    	final String selectDatas = getPara("selectDatas");
    	if(StrKit.notBlank(selectDatas)){
    		JSONArray selectDataArray = JSONArray.parseArray(selectDatas);
    		if (selectDataArray!=null && selectDataArray.size()>0) {
    			PersNotice notice = null;
    			for (int i = 0; i < selectDataArray.size(); i++) {
    				JSONObject jsonObj = selectDataArray.getJSONObject(i);
					final String noticeId = jsonObj.getString("noticeId");
					final String userId = jsonObj.getString("userId");
					
					if(notice==null) {
						notice = persNoticeService.get(noticeId);
					}
					
					SysMessage msg = new SysMessage();
	        		msg.setId(IdGen.getUUID());
	        		msg.setUserId(userId);
	        		msg.setRelationId(noticeId);
	        		msg.setBelongSystem(SystemType.PERSONNEL);
	        		msg.setMsgCategory(MsgCategory.NOTICE);
	        		msg.setMsgUrl("/pers/notice/look");
	        		msg.setMsgTitle(notice.getNoticeTitle());
	        		msg.setIsRead("0");
	        		msg.setDelFlag(DelFlag.NORMAL);
	        		msg.setCreateBy(userId);
	        		msg.setCreateDate(new Date());
	        		msg.setUpdateBy(userId);
	        		msg.setUpdateDate(new Date());
	        		msg.save();
				}
    			renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "请勾选数据"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "请勾选数据"));
    	}
    }

    public static void main(String[] args) {
        String str="[{\"CSB201910005903\":\"59449015-6da1-44a9-a082-c30d8ecc293e\"},\n" +
                "{\"CSB201910005930\":\"df929d71-c10a-4e1a-befe-4240d616426d\"},\n" +
                "{\"CSB202206004230\":\"cd5974e2-fa5b-42aa-81bc-b2acf2bd6e10\"},\n" +
                "{\"CSB202208008235\":\"54865119-71da-468d-ad59-d85accfc5d7d\"}]";
        JSONArray jsonArray= JSON.parseArray(str);



        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject=jsonArray.getJSONObject(i);
            Set<String> stringSet=jsonObject.keySet();

            Map<String,String> map=new HashMap<>();
            map.put("appNo","CS10001");
            map.put("baseId",jsonObject.getString((String)stringSet.toArray()[0]));
            map.put("bookData","[{\"remark\": \"\",\"bookNo\":\""+(String)stringSet.toArray()[0]+"\"}]");
            String res=HttpClientsUtils.httpPostForm("http://finance.cncsgroup.com/api/expenseForCancelBook",map,null,null);
            JSONObject json=JSON.parseObject(res);
            System.out.println(jsonArray.getString(i)+"："+res);
        }

    }


}
