/**
 * 
 */
package com.cszn.personnel.web.controller.sys;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.sys.OrgEmployeeService;
import com.cszn.integrated.service.api.sys.OrgService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.status.OrgType;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.integrated.service.entity.sys.Org;
import com.cszn.integrated.service.entity.sys.OrgEmployee;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.cszn.personnel.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * Created by LiangHuiLing on 2019年4月24日
 *
 * OrgEmployeeController
 */
@RequestMapping(value="/orgEmployee", viewPath="/modules_page/sys/orgEmployee")
public class OrgEmployeeController extends BaseController {

	@Inject
    private OrgService orgService;
	@Inject
	private OrgEmployeeService orgEmployeeService;
	@Inject
	private UserService userService;
	@Inject
	private DictService dictService;
	
    public void index() {
    	final String orgId = AuthUtils.getOrgId();
    	if(StrKit.notBlank(orgId)){
    		Org org = orgService.findById(orgId);
    		if(org!=null){
    			setAttr("orgType", org.getOrgType());
    			setAttr("orgParentId", org.getParentId());
    			setAttr("orgParentIds", org.getParentIds());
    		}
    	}
    	setAttr("orgId", orgId);
        render("orgEmployeeIndex.html");
    }

	@Clear(LogInterceptor.class)
    public void employeeOrgTree() {
    	String orgId = AuthUtils.getOrgId();
        orgId=null;
    	List<ZTree> orgFormTree = orgService.orgFormTree(orgId);
        renderJson(orgFormTree);
    }
    
    /**
     * 机构员工分页表格数据
     */
	@Clear(LogInterceptor.class)
    public void pageTable() {
    	OrgEmployee orgEmployee = getBean(OrgEmployee.class, "", true);
        Page<OrgEmployee> orgEmployeePage = orgEmployeeService.paginateByCondition(orgEmployee, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<OrgEmployee>(orgEmployeePage));
    }
    
    /**
     * 添加页面方法
     */
    public void add() {
		final String orgId = getPara(0);
		String orgParentId = getPara(1);
		String orgParentIds = getPara(2);
		final String orgType = getPara(3);
		final String userOrgId = AuthUtils.getOrgId();
		OrgEmployee model = new OrgEmployee();
		if(orgType.equals(OrgType.mainOrg)){
			model.setOrgId(orgId);
			model.setOrgParentIds(orgId+",");
		}else{
			if(StrKit.notBlank(orgParentIds)){
				orgParentIds = orgParentIds.replace("0,", "");
				orgParentIds = orgParentIds + orgId + ",";
				final String[] idsArray = orgParentIds.split(",");
				if(idsArray!=null && idsArray.length>0){
					model.setOrgId(idsArray[0]);
				}
				model.setOrgParentIds(orgParentIds);
			}
		}
		model.setOrgCurId(orgId);
		model.setOrgParentId(orgParentId);
		final List<Dict> positionDictList = dictService.getListByTypeOnUse("position");
    	setAttr("model", model);
    	setAttr("userOrgId", userOrgId);
    	setAttr("positionDictList", positionDictList);
        render("employeeForm.html");
    }
    
    /**
     * 修改页面方法
     */
    public void edit() {
    	final String empId = getPara(0);
    	final String userOrgId = AuthUtils.getOrgId();
		final OrgEmployee model = orgEmployeeService.findById(empId);
		final List<Dict> positionDictList = dictService.getListByTypeOnUse("position");
		setAttr("model", model);
		setAttr("userOrgId", userOrgId);
		setAttr("positionDictList", positionDictList);
        render("employeeForm.html");
    }
    
    /**
     * 登陆帐号管理页面方法
     */
    public void loginAccountManage() {
    	final String empId = getPara(0);
    	setAttr("empId", empId);
    	render("loginAccountManage.html");
    }
    
    /**
     * 添加用户页面方法
     */
    public void addEmpUser() {
		final String empId = getPara(0);
		final String userOrgId = AuthUtils.getOrgId();
		User model = new User();
		model.setEmployeeId(empId);
		model.setOrgId(userOrgId);
    	setAttr("model", model);
        render("employeeUserForm.html");
    }
    
    /**
     * 编辑用户页面方法
     */
    public void editEmpUser() {
		final String userId = getPara(0);
		User model = userService.findById(userId);
    	setAttr("model", model);
        render("employeeUserForm.html");
    }
    
    /**
     * 个人资料页面方法
     */
    public void info() {
    	final String empId = AuthUtils.getLoginUser().getEmployeeId();
    	final OrgEmployee model = orgEmployeeService.findById(empId);
    	setAttr("model", model);
    	render("employeeInfo.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
        final OrgEmployee orgEmp = getBean(OrgEmployee.class, "", true);
        if (orgEmployeeService.saveOrgEmployee(orgEmp)) {
			renderJson(Ret.ok("msg", "操作成功!"));
		} else {
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    /**
     * 删除当前数据及其帐号数据
     */
    public void batchDel() {
    	final String batchDelDatas = getPara("batchDelDatas");
    	final String batchDelDatas1 = getPara("batchDelDatas1");
    	if(StrKit.notBlank(batchDelDatas) && StrKit.notBlank(batchDelDatas1)){
    		List<OrgEmployee> empList = JSONArray.parseArray(batchDelDatas, OrgEmployee.class);
    		List<User> userList = JSONArray.parseArray(batchDelDatas1, User.class);
    		if (orgEmployeeService.batchDel(empList, userList)) {
            	renderJson(Ret.ok("msg", "操作成功!"));
            } else {
            	renderJson(Ret.fail("msg", "操作失败！"));
            }
    	}else{
    		renderJson(Ret.fail("msg", "数据不能为空！"));
    	}
    }
}
