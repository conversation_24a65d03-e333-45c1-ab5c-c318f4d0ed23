package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.entity.enums.PersTaskType;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RequestMapping(value="/pers/task", viewPath="/modules_page/pers/task")
public class PersTaskController extends BaseController {

    public void index(){
        render("taskIndex.html");
    }


    public void form(){

        render("taskForm.html");
    }

    public void getTaskPage(){
        String pageIndex=getPara("page");
        String pageSize=getPara("limit");
        String state=getPara("state");

        HashMap<String,String> params=new HashMap<>();
        params.put("userId", AuthUtils.getUserId());
        params.put("processNo", PersTaskType.entry.getTaskNo());
        params.put("state",state);
        params.put("pageIndex",pageIndex);
        params.put("pageSize",pageSize);

        String resultStr= HttpClientsUtils.httpPostForm(Global.getMyTasksUrl,params,null,null);

        Page<Record> page=new Page<>();
        page.setPageNumber(Integer.valueOf(pageIndex));
        page.setPageSize(Integer.valueOf(pageSize));

        List<Record> recordList=new ArrayList<>();
        page.setList(recordList);
        if(resultStr.startsWith("{") && resultStr.endsWith("}")){
            JSONObject jsonObject= JSON.parseObject(resultStr);
            if(jsonObject.containsKey("Type") && "1".equals(jsonObject.getString("Type"))){
                JSONObject data=jsonObject.getJSONObject("Data");
                page.setTotalRow(2);
                JSONObject rows=data.getJSONObject("rows");
                if(rows!=null){
                    JSONArray array=rows.getJSONArray("Table1");
                    if(array!=null && array.size()>0){
                        for(int i=0;i<array.size();i++){
                            JSONObject item=array.getJSONObject(i);
                            Record record=new Record();
                            for(String key:item.keySet()){
                                record.set(key,item.get(key));
                            }
                            recordList.add(record);
                        }
                    }
                }

            }

        }

        renderJson(new DataTable<Record>(page));
    }


}
