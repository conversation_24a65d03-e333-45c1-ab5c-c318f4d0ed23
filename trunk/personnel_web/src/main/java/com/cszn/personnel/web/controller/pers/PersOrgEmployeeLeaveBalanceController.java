package com.cszn.personnel.web.controller.pers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.ImportExcelKit;
import com.cszn.integrated.base.utils.StreamRender;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeLeaveBalanceService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeLeaveRestApplyService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeOverTimeApplyService;
import com.cszn.integrated.service.entity.enums.PersLeaveRestType;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeLeaveRestApply;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeOverTimeApply;
import com.cszn.personnel.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.sql.SQLException;
import java.util.List;

@RequestMapping(value="/employeeLeaveBalance", viewPath="/modules_page/pers/leaveBalance")
public class PersOrgEmployeeLeaveBalanceController extends BaseController {

    @Inject
    PersOrgEmployeeLeaveBalanceService persOrgEmployeeLeaveBalanceService;
    @Inject
    PersOrgEmployeeLeaveRestApplyService persOrgEmployeeLeaveRestApplyService;
    @Inject
    PersOrgEmployeeOverTimeApplyService persOrgEmployeeOverTimeApplyService;

    public void index(){

        render("index.html");
    }

    public void pageList(){
        String deptId=getPara("deptId");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");

        /*if(StrKit.isBlank(archiveStatus)){
            archiveStatus="incumbency";
        }*/

        Page<Record> recordPage=persOrgEmployeeLeaveBalanceService.findEmployeeLeaveBalancePage(getParaToInt("page"),getParaToInt("limit"),deptId,fullName,archiveStatus, AuthUtils.getUserId());

        renderJson(new DataTable<Record>(recordPage));

    }

    public void exportList(){
        String deptId=getPara("deptId");
        String fullName=getPara("fullName");
        String archiveStatus=getPara("archiveStatus");
        Page<Record> recordPage=persOrgEmployeeLeaveBalanceService.findEmployeeLeaveBalancePage(1,20000,deptId,fullName,archiveStatus, AuthUtils.getUserId());

        String[] title={"工号","姓名","部门","职位","剩余年假"};
        String fileName="员工剩余年假"+ DateUtils.getDate("yyyy-MM-dd HH:mm:ss") +".xls";
        String sheetName = "剩余年假";
        String[][] content=new String[recordPage.getList().size()][title.length];
        if(recordPage!=null && recordPage.getList().size()>0){

            int i=0;
            for(Record record:recordPage.getList()){
                content[i][0]=record.getStr("workNum");
                content[i][1]=record.getStr("fullName");
                content[i][2]=record.getStr("deptName");
                content[i][3]=record.getStr("positionName");
                content[i][4]=record.getStr("yearLeave");
                i++;
            }
        }
        //创建HSSFWorkbook
        HSSFWorkbook wb = ImportExcelKit.getHSSFWorkbook(sheetName, title, content, null);
        //响应到客户端
        try {
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            wb.write(os);
            render(new StreamRender(fileName, os));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void form(){
        String empId=getPara("empId");
        setAttr("empId",empId);
        render("form.html");
    }

    public void getEmpLeaveBalanceList(){
        String empId=getPara("empId");

        List<Record> recordList=persOrgEmployeeLeaveBalanceService.getEmpLeaveBalanceList(empId);

        renderJson(new DataTable<Record>(recordList));
    }

    public void empTable(){
        render("empTable.html");
    }

    public void batchForm(){

        setAttr("leaveTypes",PersLeaveRestType.values());
        render("batchForm.html");
    }

    public void editForm(){
        String empId=getPara("empId");
        String leaveId=getPara("leaveId");
        String leaveType=getPara("leaveType");
        String type=getPara("type");
        Record record=null;
        if("3".equals(type)){
            record=persOrgEmployeeLeaveBalanceService.getEmpLeaveBalanceRecord(leaveId);
        }

        if(record==null){
            PersLeaveRestType leaveRestType=PersLeaveRestType.getTypeByKey(leaveType);
            record=new Record();
            record.set("unit",leaveRestType.getUnit());
            record.set("day",0);
            record.set("hour",0);
            record.set("minute",0);
            record.set("leaveTypeName",leaveRestType.getValue());
        }

        setAttr("leaveType",leaveType);
        setAttr("record",record);
        setAttr("empId",empId);
        setAttr("leaveId",leaveId);
        setAttr("type",type);
        render("editForm.html");
    }

    public void saveLeaveBalance(){
        String leaveId=getPara("leaveId");
        Integer day=getParaToInt("day");
        String hour=getPara("hour");
        Integer minute=getParaToInt("minute");
        String leaveKey=getPara("leaveType");
        String remark=getPara("remark");
        String empId=getPara("empId");
        String type=getPara("type");
        String detailDate=getPara("detailDate");
        String userId=getPara("userId");

        if(day==null){
            day=0;
        }
        if(hour==null){
            hour="0";
        }
        if(minute==null){
            minute=0;
        }
        if(StrKit.isBlank(userId)){
            userId=AuthUtils.getUserId();
        }
        boolean flag=false;
        if("3".equals(type)){
            flag=persOrgEmployeeLeaveBalanceService.saveLeaveBalance(empId,leaveKey,day,Double.valueOf(hour),minute,remark, DateUtils.parseDate(detailDate),userId);
        }else if("1".equals(type)){
            flag=persOrgEmployeeLeaveBalanceService.addSubLeaveBalance(empId,leaveKey,Double.valueOf(day),Double.valueOf(hour),Double.valueOf(minute),type,remark,DateUtils.parseDate(detailDate),userId);
        }else if("2".equals(type)){
            flag=persOrgEmployeeLeaveBalanceService.addSubLeaveBalance(empId,leaveKey,Double.valueOf(day),Double.valueOf(hour),Double.valueOf(minute),type,remark,DateUtils.parseDate(detailDate),userId);
        }

        if (flag) {
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void aaaaaa() throws Exception{
        String empId=getPara("empId");

        String sql="select a.id from pers_org_employee_leave_rest_apply a\n" +
                "inner join pers_org_employee_leave_rest_apply_date b on b.leave_rest_appply_id=a.id\n" +
                "where a.del_flag='0' and a.`status` in ('2','3') and b.end_time>'2024-04-30 23:59:59' and b.leave_rest_type='9'\n" +
                "and a.emp_id=? ORDER BY a.start_time";
        List<String> ids=Db.query(sql,empId);
        for (String id : ids) {
            PersOrgEmployeeLeaveRestApply leaveRestApply = persOrgEmployeeLeaveRestApplyService.findById(id);
            persOrgEmployeeLeaveBalanceService.deductLeaveBalance2(leaveRestApply);
            Thread.sleep(1000);
        }
        renderCodeSuccess("");
    }

    public void bbbbbb() throws Exception{
        String empId=getPara("empId");
        String sql="select id from pers_org_employee_over_time_apply where `status`='3' and start_time>'2024-04-30 23:59:59' and emp_id=? ";
        List<String> ids=Db.query(sql,empId);
        for (String id : ids) {
            PersOrgEmployeeOverTimeApply overTimeApply = persOrgEmployeeOverTimeApplyService.findById(id);
            persOrgEmployeeLeaveBalanceService.addLeaveBalance(overTimeApply);
            Thread.sleep(1000);
        }
        renderCodeSuccess("");
    }


    public void batchSaveLeaveBalance(){
        String data=getPara("data");
        if(StrKit.isBlank(data)){
            renderJson(Ret.fail("msg","数据不能为空"));
            return;
        }
        JSONArray dataArray= JSON.parseArray(data);
        if(dataArray==null && dataArray.size()==0){
            renderJson(Ret.fail("msg","数据不能为空"));
            return;
        }

        boolean returnFlag=Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    for (int i = 0; i < dataArray.size(); i++) {
                        JSONObject jsonObject=dataArray.getJSONObject(i);
                        String empId = jsonObject.getString("empId");
                        String leaveType = jsonObject.getString("leaveType");
                        String type = jsonObject.getString("type");
                        String detailDate = jsonObject.getString("detailDate");
                        Integer day = jsonObject.getInteger("day");
                        Double hour = jsonObject.getDouble("hour");
                        String remark = jsonObject.getString("remark");
                        boolean flag=false;
                        if("3".equals(type)){
                            flag=persOrgEmployeeLeaveBalanceService.saveLeaveBalance(empId,leaveType,day,hour,0,remark, DateUtils.parseDate(detailDate),AuthUtils.getUserId());
                        }else if("1".equals(type)){
                            flag=persOrgEmployeeLeaveBalanceService.addSubLeaveBalance(empId,leaveType,Double.valueOf(day),hour,0,type,remark,DateUtils.parseDate(detailDate),AuthUtils.getUserId());
                        }else if("2".equals(type)){
                            flag=persOrgEmployeeLeaveBalanceService.addSubLeaveBalance(empId,leaveType,Double.valueOf(day),hour,0,type,remark,DateUtils.parseDate(detailDate),AuthUtils.getUserId());
                        }
                        if(!flag){
                            return false;
                        }
                    }
                    return true;
                }catch (Exception e){
                    e.printStackTrace();
                    return false;
                }
            }
        });
        if (returnFlag) {
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void detailIndex(){
        String empId=getPara("empId");
        String leaveType=getPara("leaveType");

        setAttr("leaveType",leaveType);
        setAttr("empId",empId);
        render("detailIndex.html");
    }


    public void detailPageList(){
        String empId=getPara("empId");
        String leaveType=getPara("leaveType");

        Page<Record> recordPage=persOrgEmployeeLeaveBalanceService.detailPageList(getParaToInt("page"),getParaToInt("limit"),empId,leaveType);

        renderJson(new DataTable<Record>(recordPage));
    }


}
