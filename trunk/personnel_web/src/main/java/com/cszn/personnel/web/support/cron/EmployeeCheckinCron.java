package com.cszn.personnel.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCheckinRecordService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeCheckinWxRecordService;
import com.cszn.integrated.service.api.weixin.QiYeWeiXinService;
import com.cszn.integrated.service.entity.pers.PersOrgEmployeeCheckinWxRecord;
import com.cszn.integrated.service.provider.pers.PersOrgEmployeeCheckinWxRecordServiceImpl;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@Cron("*/20 * * * *")
public class EmployeeCheckinCron implements Runnable {

    @Inject
    PersOrgEmployeeCheckinRecordService persOrgEmployeeCheckinRecordService;
    @Inject
    PersOrgEmployeeCheckinWxRecordService persOrgEmployeeCheckinWxRecordService;
    @Inject
    QiYeWeiXinService qiYeWeiXinService;
    @Inject
    EmployeeDelayCheckinCron employeeDelayCheckinCron;

    private static Long lastTime=null;
    private Logger logger= LoggerFactory.getLogger(EmployeeCheckinCron.class);

    @Override
    public void run() {
        Calendar calendar=Calendar.getInstance();
        long currTime=calendar.getTimeInMillis();
        if(lastTime==null){
            calendar.add(Calendar.MINUTE,-21);
            lastTime=calendar.getTimeInMillis();
        }
        //persOrgEmployeeCheckinRecordService.getWorkWeiXinCheckinData(1,50,lastTime/1000,currTime/1000);
        long curr=System.currentTimeMillis();
        //persOrgEmployeeCheckinWxRecordService.getWorkWeiXinCheckinData(1,90,lastTime/1000,currTime/1000);
        getWorkWeiXinCheckinData(1,90,lastTime/1000,currTime/1000);
        logger.info("耗时："+(System.currentTimeMillis()-curr));
        lastTime=currTime;
    }


    public void getWorkWeiXinCheckinData(int pageNumber,int pageSize,long startTime,long endTime){
        String sql="  from pers_org_employee where del_flag='0' and archive_status='incumbency' and qiye_userid is not null ";
        Page<Record> recordPage = Db.paginate(pageNumber,pageSize,"select id,qiye_userid ",sql);
        if(recordPage==null || recordPage.getList()==null || recordPage.getList().size()==0){
            return;
        }
        List<Record> recordList=recordPage.getList();
        List<String> qiyeIds=new ArrayList<>();

        Map<String,String> empIdMap=new HashMap<>();

        for(Record record:recordList){
            empIdMap.put(record.getStr("qiye_userid"),record.getStr("id"));
            qiyeIds.add(record.getStr("qiye_userid"));
        }
        String resultStr=qiYeWeiXinService.getCheckinData(qiyeIds.toArray(),startTime,endTime);
        System.out.println("数据："+resultStr);
        if(StrKit.isBlank(resultStr) || !resultStr.startsWith("{") || !resultStr.endsWith("}")){
            logger.error("获取企业微信打卡记录失败"+resultStr);
            return;
        }
        JSONObject jsonObject= JSON.parseObject(resultStr);
        if(jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode")==0 ){
            JSONArray checkinDataArray=jsonObject.getJSONArray("checkindata");
            if(checkinDataArray!=null && checkinDataArray.size()>0){
                List<PersOrgEmployeeCheckinWxRecord> employeeCheckinWxRecordList=new ArrayList<>();
                String isRepeatSql="select * from pers_org_employee_checkin_wx_record where del_flag='0' and emp_id=? and checkin_type=? and checkin_time=? ";
                for(int i=0;i<checkinDataArray.size();i++){
                    JSONObject checkinData=checkinDataArray.getJSONObject(i);

                    long checkinTime=checkinData.getLongValue("checkin_time");
                    //查询是否重复
                    Record repeatRecord=Db.findFirst(isRepeatSql,empIdMap.get(checkinData.getString("userid"))
                            ,checkinData.getString("checkin_type"),new Date(checkinTime*1000));
                    if("未打卡".equals(checkinData.getString("exception_type"))){
                        continue;
                    }
                    if(repeatRecord!=null){
                        //再判断是否出现在打卡记录表中

                        //重复
                        continue;
                        /*if("上班打卡".equals(checkinData.getString("checkin_type")) || "外出打卡".equals(checkinData.getString("checkin_type")) ){
                            continue;
                        }else if("下班打卡".equals(checkinData.getString("checkin_type"))){
                            repeatRecord.setDelFlag("1");
                            repeatRecord.setUpdateDate(new Date());
                            employeeCheckinRecordUpdateList.add(repeatRecord);
                        }*/
                    }
                    PersOrgEmployeeCheckinWxRecord checkinWxRecord=new PersOrgEmployeeCheckinWxRecord();
                    checkinWxRecord.setId(IdGen.getUUID());
                    checkinWxRecord.setEmpId(empIdMap.get(checkinData.getString("userid")));
                    checkinWxRecord.setGroupname(checkinData.getString("groupname"));
                    checkinWxRecord.setCheckinType(checkinData.getString("checkin_type"));
                    checkinWxRecord.setExceptionType(checkinData.getString("exception_type"));

                    checkinWxRecord.setCheckinTime(new Date(checkinTime*1000));
                    checkinWxRecord.setLocationTitle(checkinData.getString("location_title"));
                    checkinWxRecord.setLocationDetail(checkinData.getString("location_detail"));
                    /*checkinWxRecord.setWifiname(checkinData.getString("wifiname"));*/
                    /*checkinWxRecord.setNotes(checkinData.getString("notes"));*/
                    checkinWxRecord.setWifimac(checkinData.getString("wifimac"));
                    checkinWxRecord.setMediaids(checkinData.getString("mediaids"));
                    checkinWxRecord.setLat(checkinData.getString("lat"));
                    checkinWxRecord.setLng(checkinData.getString("lng"));
                    checkinWxRecord.setDeviceid(checkinData.getString("deviceid"));
                    long schCheckinTime=checkinData.getLongValue("sch_checkin_time");
                    checkinWxRecord.setSchCheckinTime(new Date(schCheckinTime*1000));
                    checkinWxRecord.setGroupid(checkinData.getString("groupid"));
                    checkinWxRecord.setScheduleId(checkinData.getIntValue("schedule_id"));
                    checkinWxRecord.setTimelineId(checkinData.getIntValue("timeline_id"));
                    checkinWxRecord.setDelFlag("0");
                    checkinWxRecord.setCreateDate(new Date());
                    checkinWxRecord.setUpdateDate(new Date());
                    employeeCheckinWxRecordList.add(checkinWxRecord);
                    if("外出打卡".equals(checkinWxRecord.getCheckinType())){
                        continue;
                    }
                    try {
                        synchronized (PersOrgEmployeeCheckinWxRecordServiceImpl.class){
                            //persOrgEmployeeCheckinRuleService.getEmpCheckinRuleConfig(checkinWxRecord.getEmpId(),checkinWxRecord);
                            employeeDelayCheckinCron.empCheckin(checkinWxRecord,checkinWxRecord.getCheckinTime());
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        logger.info(checkinWxRecord.getId()+"打卡记录计算异常"+e.getMessage());
                    }
                }
                Db.batchSave(employeeCheckinWxRecordList,employeeCheckinWxRecordList.size());
            }
        }else{
            logger.error("获取企业微信打卡记录失败"+resultStr);
            return;
        }
        pageNumber++;
        if(pageNumber<=recordPage.getTotalPage()){
            getWorkWeiXinCheckinData(pageNumber,pageSize,startTime,endTime);
        }
    }

}
