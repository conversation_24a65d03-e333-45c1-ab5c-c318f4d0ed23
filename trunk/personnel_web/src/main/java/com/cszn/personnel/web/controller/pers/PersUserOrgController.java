package com.cszn.personnel.web.controller.pers;

import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.pers.PersUserOrgService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value="/pers/persUserOrg", viewPath="/modules_page/pers/userOrgConfig")
public class PersUserOrgController extends BaseController {

    @Inject
    private UserService userService;
    @Inject
    private PersUserOrgService persUserOrgService;

    public void index(){

        render("userConfigIndex.html");
    }

    public void form(){
        User model = userService.findById(getPara("id"));
        setAttr("model", model);
        render("userConfigForm.html");
    }

    public void save(){
        final String roleIds = getPara("roleIds");
        User user = getBean(User.class, "", true);

        boolean flag=persUserOrgService.saveUserOrg(user,roleIds);
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }

    }
}
