
2023-02-15 09:32:41
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 09:32:48
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardSettleTask to class com.cszn.finance.web.support.cron.CardMonthBalanceTask

2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:53
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:54
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 09:32:56
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 09:32:56
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 10:10:37
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:10:46
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:10:53
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 10:10:53
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 10:11:14
[WARN]-[Thread: XNIO-1 task-4]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-passwordRetryCache], using default.

2023-02-15 10:11:14
[WARN]-[Thread: XNIO-1 task-6]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-authorizationCache], using default.

2023-02-15 10:15:29
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:15:38
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:43
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:15:44
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 10:15:44
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 10:25:04
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:25:10
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:15
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:25:16
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 10:25:16
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 10:28:25
[WARN]-[Thread: XNIO-1 task-7]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-passwordRetryCache], using default.

2023-02-15 10:28:25
[WARN]-[Thread: XNIO-1 task-9]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-authorizationCache], using default.

2023-02-15 10:44:41
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:44:50
[ERROR]-[Thread: main]-[com.jfinal.core.Config.startPlugins()]: Plugin start error: com.jfinal.plugin.activerecord.ActiveRecordPlugin. 
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
com.jfinal.plugin.activerecord.ActiveRecordException: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:55)
	at com.jfinal.plugin.activerecord.ActiveRecordPlugin.start(ActiveRecordPlugin.java:226)
	at com.jfinal.core.Config.startPlugins(Config.java:128)
	at com.jfinal.core.Config.configPluginWithOrder(Config.java:71)
	at com.jfinal.core.Config.configJFinal(Config.java:59)
	at com.jfinal.core.JFinal.init(JFinal.java:61)
	at com.jfinal.core.JFinalFilter.init(JFinalFilter.java:63)
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:111)
	at io.undertow.servlet.core.ManagedFilter.createFilter(ManagedFilter.java:80)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:589)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:554)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.core.DeploymentManagerImpl.start(DeploymentManagerImpl.java:596)
	at com.jfinal.server.undertow.UndertowServer.configHttp(UndertowServer.java:284)
	at com.jfinal.server.undertow.UndertowServer.doStart(UndertowServer.java:262)
	at com.jfinal.server.undertow.UndertowServer.start(UndertowServer.java:159)
	at io.jboot.app.JbootApplication.start(JbootApplication.java:40)
	at io.jboot.app.JbootApplication.run(JbootApplication.java:36)
	at com.cszn.personnel.web.run.Application.main(Application.java:12)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.Util.getInstance(Util.java:408)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:944)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3978)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3914)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2683)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2491)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2449)
	at com.mysql.jdbc.StatementImpl.executeQuery(StatementImpl.java:1381)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:111)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at com.jfinal.plugin.activerecord.TableBuilder.doBuild(TableBuilder.java:71)
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:47)
	... 19 more

2023-02-15 10:45:07
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:45:16
[ERROR]-[Thread: main]-[com.jfinal.core.Config.startPlugins()]: Plugin start error: com.jfinal.plugin.activerecord.ActiveRecordPlugin. 
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
com.jfinal.plugin.activerecord.ActiveRecordException: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:55)
	at com.jfinal.plugin.activerecord.ActiveRecordPlugin.start(ActiveRecordPlugin.java:226)
	at com.jfinal.core.Config.startPlugins(Config.java:128)
	at com.jfinal.core.Config.configPluginWithOrder(Config.java:71)
	at com.jfinal.core.Config.configJFinal(Config.java:59)
	at com.jfinal.core.JFinal.init(JFinal.java:61)
	at com.jfinal.core.JFinalFilter.init(JFinalFilter.java:63)
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:111)
	at io.undertow.servlet.core.ManagedFilter.createFilter(ManagedFilter.java:80)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:589)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:554)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.core.DeploymentManagerImpl.start(DeploymentManagerImpl.java:596)
	at com.jfinal.server.undertow.UndertowServer.configHttp(UndertowServer.java:284)
	at com.jfinal.server.undertow.UndertowServer.doStart(UndertowServer.java:262)
	at com.jfinal.server.undertow.UndertowServer.start(UndertowServer.java:159)
	at io.jboot.app.JbootApplication.start(JbootApplication.java:40)
	at io.jboot.app.JbootApplication.run(JbootApplication.java:36)
	at com.cszn.personnel.web.run.Application.main(Application.java:12)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.Util.getInstance(Util.java:408)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:944)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3978)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3914)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2683)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2491)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2449)
	at com.mysql.jdbc.StatementImpl.executeQuery(StatementImpl.java:1381)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:111)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at com.jfinal.plugin.activerecord.TableBuilder.doBuild(TableBuilder.java:71)
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:47)
	... 19 more

2023-02-15 10:46:49
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:46:57
[ERROR]-[Thread: main]-[com.jfinal.core.Config.startPlugins()]: Plugin start error: com.jfinal.plugin.activerecord.ActiveRecordPlugin. 
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
com.jfinal.plugin.activerecord.ActiveRecordException: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:55)
	at com.jfinal.plugin.activerecord.ActiveRecordPlugin.start(ActiveRecordPlugin.java:226)
	at com.jfinal.core.Config.startPlugins(Config.java:128)
	at com.jfinal.core.Config.configPluginWithOrder(Config.java:71)
	at com.jfinal.core.Config.configJFinal(Config.java:59)
	at com.jfinal.core.JFinal.init(JFinal.java:61)
	at com.jfinal.core.JFinalFilter.init(JFinalFilter.java:63)
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:111)
	at io.undertow.servlet.core.ManagedFilter.createFilter(ManagedFilter.java:80)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:589)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:554)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.core.DeploymentManagerImpl.start(DeploymentManagerImpl.java:596)
	at com.jfinal.server.undertow.UndertowServer.configHttp(UndertowServer.java:284)
	at com.jfinal.server.undertow.UndertowServer.doStart(UndertowServer.java:262)
	at com.jfinal.server.undertow.UndertowServer.start(UndertowServer.java:159)
	at io.jboot.app.JbootApplication.start(JbootApplication.java:40)
	at io.jboot.app.JbootApplication.run(JbootApplication.java:36)
	at com.cszn.personnel.web.run.Application.main(Application.java:12)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.Util.getInstance(Util.java:408)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:944)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3978)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3914)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2683)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2491)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2449)
	at com.mysql.jdbc.StatementImpl.executeQuery(StatementImpl.java:1381)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:111)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at com.jfinal.plugin.activerecord.TableBuilder.doBuild(TableBuilder.java:71)
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:47)
	... 19 more

2023-02-15 10:47:13
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:47:22
[ERROR]-[Thread: main]-[com.jfinal.core.Config.startPlugins()]: Plugin start error: com.jfinal.plugin.activerecord.ActiveRecordPlugin. 
com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
com.jfinal.plugin.activerecord.ActiveRecordException: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:55)
	at com.jfinal.plugin.activerecord.ActiveRecordPlugin.start(ActiveRecordPlugin.java:226)
	at com.jfinal.core.Config.startPlugins(Config.java:128)
	at com.jfinal.core.Config.configPluginWithOrder(Config.java:71)
	at com.jfinal.core.Config.configJFinal(Config.java:59)
	at com.jfinal.core.JFinal.init(JFinal.java:61)
	at com.jfinal.core.JFinalFilter.init(JFinalFilter.java:63)
	at io.undertow.servlet.core.LifecyleInterceptorInvocation.proceed(LifecyleInterceptorInvocation.java:111)
	at io.undertow.servlet.core.ManagedFilter.createFilter(ManagedFilter.java:80)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:589)
	at io.undertow.servlet.core.DeploymentManagerImpl$2.call(DeploymentManagerImpl.java:554)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:42)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.core.DeploymentManagerImpl.start(DeploymentManagerImpl.java:596)
	at com.jfinal.server.undertow.UndertowServer.configHttp(UndertowServer.java:284)
	at com.jfinal.server.undertow.UndertowServer.doStart(UndertowServer.java:262)
	at com.jfinal.server.undertow.UndertowServer.start(UndertowServer.java:159)
	at io.jboot.app.JbootApplication.start(JbootApplication.java:40)
	at io.jboot.app.JbootApplication.run(JbootApplication.java:36)
	at com.cszn.personnel.web.run.Application.main(Application.java:12)
Caused by: com.mysql.jdbc.exceptions.jdbc4.MySQLSyntaxErrorException: Table 'cs_integrated.sms_condition_list' doesn't exist
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at com.mysql.jdbc.Util.handleNewInstance(Util.java:425)
	at com.mysql.jdbc.Util.getInstance(Util.java:408)
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:944)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3978)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:3914)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:2530)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:2683)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2491)
	at com.mysql.jdbc.ConnectionImpl.execSQL(ConnectionImpl.java:2449)
	at com.mysql.jdbc.StatementImpl.executeQuery(StatementImpl.java:1381)
	at com.zaxxer.hikari.pool.ProxyStatement.executeQuery(ProxyStatement.java:111)
	at com.zaxxer.hikari.pool.HikariProxyStatement.executeQuery(HikariProxyStatement.java)
	at com.jfinal.plugin.activerecord.TableBuilder.doBuild(TableBuilder.java:71)
	at com.jfinal.plugin.activerecord.TableBuilder.build(TableBuilder.java:47)
	... 19 more

2023-02-15 10:47:41
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersBranchCompanyRoleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.api.PersApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeController.findYearsStr(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeController.findYearsStr(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:52
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.notice.NoticeController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:47:53
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 10:47:53
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 10:48:13
[WARN]-[Thread: XNIO-1 task-5]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-passwordRetryCache], using default.

2023-02-15 10:48:13
[WARN]-[Thread: XNIO-1 task-7]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-authorizationCache], using default.

2023-02-15 10:55:33
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersBranchCompanyRoleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.api.PersApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeController.findYearsStr(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeController.findYearsStr(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:44
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.notice.NoticeController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 10:55:45
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 10:55:45
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 14:16:11
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersBranchCompanyRoleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.api.PersApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeController.findYearsStr(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeController.findYearsStr(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getCheckinDayData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.getWorkWeiXinCheckinData(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.insertRow(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.vvvvv(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.removeColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.pers.PersOrgEmployeeCheckinController.deleteColumn(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:23
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.personnel.web.controller.notice.NoticeController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 14:16:24
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 14:16:24
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 14:16:58
[WARN]-[Thread: XNIO-1 task-7]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-passwordRetryCache], using default.

2023-02-15 14:16:58
[WARN]-[Thread: XNIO-1 task-9]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-authorizationCache], using default.

2023-02-15 15:24:54
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 15:25:03
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:09
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:25:10
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 15:25:10
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 15:35:42
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 15:35:51
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:57
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:35:58
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 15:35:58
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 15:39:43
[ERROR]-[Thread: XNIO-1 task-1]-[io.undertow.server.HttpServerExchange.endExchange()]: UT005090: Unexpected failure
java.util.concurrent.RejectedExecutionException: XNIO007007: Thread is terminating
	at org.xnio.nio.WorkerThread.execute(WorkerThread.java:590)
	at org.xnio.channels.EmptyStreamSourceChannel.shutdownReads(EmptyStreamSourceChannel.java:184)
	at io.undertow.servlet.spec.ServletInputStreamImpl.close(ServletInputStreamImpl.java:269)
	at io.undertow.servlet.spec.HttpServletRequestImpl.closeAndDrainRequest(HttpServletRequestImpl.java:682)
	at io.undertow.servlet.core.ServletBlockingHttpExchange.close(ServletBlockingHttpExchange.java:89)
	at io.undertow.server.HttpServerExchange.endExchange(HttpServerExchange.java:1624)
	at io.undertow.server.AbstractServerConnection$CloseSetter.handleEvent(AbstractServerConnection.java:316)
	at io.undertow.server.AbstractServerConnection$CloseSetter.handleEvent(AbstractServerConnection.java:296)
	at org.xnio.ChannelListeners.invokeChannelListener(ChannelListeners.java:92)
	at org.xnio.StreamConnection.invokeCloseListener(StreamConnection.java:80)
	at org.xnio.Connection.close(Connection.java:142)
	at io.undertow.server.AbstractServerConnection.close(AbstractServerConnection.java:159)
	at org.xnio.IoUtils.safeClose(IoUtils.java:134)
	at io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:616)
	at io.undertow.conduits.AbstractFixedLengthStreamSinkConduit.write(AbstractFixedLengthStreamSinkConduit.java:106)
	at org.xnio.conduits.Conduits.writeFinalBasic(Conduits.java:132)
	at io.undertow.conduits.AbstractFixedLengthStreamSinkConduit.writeFinal(AbstractFixedLengthStreamSinkConduit.java:175)
	at org.xnio.conduits.ConduitStreamSinkChannel.writeFinal(ConduitStreamSinkChannel.java:104)
	at io.undertow.channels.DetachableStreamSinkChannel.writeFinal(DetachableStreamSinkChannel.java:195)
	at io.undertow.server.HttpServerExchange$WriteDispatchChannel.writeFinal(HttpServerExchange.java:2082)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.writeBufferBlocking(ServletOutputStreamImpl.java:572)
	at io.undertow.servlet.spec.ServletOutputStreamImpl.close(ServletOutputStreamImpl.java:609)
	at io.undertow.servlet.spec.ServletPrintWriter.close(ServletPrintWriter.java:117)
	at io.undertow.servlet.spec.ServletPrintWriterDelegate.close(ServletPrintWriterDelegate.java:81)
	at io.undertow.servlet.spec.HttpServletResponseImpl.closeStreamAndWriter(HttpServletResponseImpl.java:480)
	at io.undertow.servlet.spec.HttpServletResponseImpl.responseDone(HttpServletResponseImpl.java:575)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:351)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:81)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:138)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:272)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:81)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:104)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:364)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:830)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)

2023-02-15 15:39:51
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 15:39:57
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:02
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:03
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 15:40:03
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 15:40:09
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 15:40:16
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:21
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 15:40:22
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 15:40:22
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 16:23:38
[INFO]-[Thread: main]-[io.undertow.servlet.spec.ServletContextImpl.log()]: Initializing Shiro environment

2023-02-15 16:23:45
[WARN]-[Thread: main]-[com.jfinal.kit.LogKit.warn()]: Aop Class[interface java.lang.Runnable] mapping changed from  class com.cszn.finance.web.support.cron.CardMonthBalanceTask to class com.cszn.finance.web.support.cron.CardSettleTask

2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.FinaApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance2(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.finapi.MoveApiController.monthBalance3(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.CardGiveRuleController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[WARN]-[Thread: main]-[com.jfinal.core.paragetter.ParaProcessorBuilder.createParaGetter()]: You should config compiler argument "-parameters" for parameter injection of action : com.cszn.finance.web.controller.fina.FinaMemberSettleMainController.main(...) 
Visit http://www.jfinal.com/doc/3-3 for details 


2023-02-15 16:23:51
[INFO]-[Thread: main]-[org.xnio.Xnio.<clinit>()]: XNIO version 3.3.8.Final

2023-02-15 16:23:51
[INFO]-[Thread: main]-[org.xnio.nio.NioXnio.<clinit>()]: XNIO NIO Implementation Version 3.3.8.Final

2023-02-15 16:24:20
[WARN]-[Thread: XNIO-1 task-3]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-passwordRetryCache], using default.

2023-02-15 16:24:20
[WARN]-[Thread: XNIO-1 task-5]-[io.jboot.components.cache.ehcache.JbootEhcacheImpl.getOrAddCache()]: Could not find cache config [shiro-authorizationCache], using default.
