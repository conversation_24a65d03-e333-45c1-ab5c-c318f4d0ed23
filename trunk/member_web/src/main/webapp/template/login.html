#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()CRM客户关系管理#end

#define css()
<style>

	.login-container{
		width: 100vw;
		height: 100vh;
		background-image: url('/static/img/login_bg.png');
		background-repeat:no-repeat;
		background-size:100% 60%;
		background-color: #f2f2f2;
	}

	.container-content{
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		margin: auto;
		height: 500px;
		width: 400px;
	}

	.login-form{
		height: 440px;
		width: 400px;
		background:rgba(255,255,255,0.5);
		box-shadow:0px 3px 3px #e0e0e0;
	}
	.input-box,.button-box{
		width: 334px;
		margin: 0 auto;
		margin-bottom: 30px;
		position: relative;
	}

	.login-input{
		height: 46px;
		line-height: 1.3;
		line-height: 38px;
		border-width: 1px;
		border-style: solid;
		border-color: #ffffff;
		border-radius: 2px;
		width: 100%;
		font-size: 16px;
		background-color: #ffffff;
		text-indent:54px;
		outline:none;
	}

	.captcha-input{
		width: 220px;
		display: inline-block;
		margin-right: 8px;
		background-image: url("/static/img/yzm.png");
		background-repeat:no-repeat;
		background-position:20px center;
	}
	.title-box{
		text-align: center;
		margin-bottom: 30px;
	}
	.title-content{
		color: #ffffff;
		font-size: 28px;
		font-family: "Microsoft YaHei";
		font-weight: bold;
		background-image: url("/static/img/login_log.png");
		background-repeat:no-repeat;
		display: inline;
		padding-left: 70px;
		padding-bottom: 4px;
	}
	.captcha-img{
		display: inline-block;
		width: 104px;
		height: 48px;
		border: none;
		vertical-align: top;
		cursor: pointer;
	}
	.reset-btn{
		background-color: #e6e6e6;
		color: #999999;
	}
	.reset-btn:hover{
		background-color: #e6e6e6;
		color: #c9c9c9;
	}
	.username-icon{
		width: 24px;
		height: 24px;
		background-image: url("/static/img/zh.png");
		background-repeat:no-repeat;
		position: absolute;
		left: 20px;
		top: 10px;
		z-index:9;
	}
	.password-icon{
		width: 24px;
		height: 24px;
		background-image: url("/static/img/mm.png");
		background-repeat:no-repeat;
		position: absolute;
		left: 20px;
		top: 10px;
		z-index:9;
	}
</style>
#end

#define js()
<script type="text/javascript">
layui.use(['layer','form'], function () {
    // 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,$ = layui.jquery
        ;
    
    $(".body").addClass("login-body");
    
    // 提交监听
    form.on('submit(sub)', function (data) {
        util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/postLogin',
            data: $.param(data.field),
            notice: false,
		    loadFlag: true,
            success : function(data) {
                window.location.href = "/";
            },
            complete : function() {
                // 刷新验证码
                $("#captchaImg").attr('src', '#(ctxPath)/captcha?'+Math.random());
            }
        });
        return false;
    });


    $(function() {
		$("#loginName").focus();
		$('#loginName').keydown(function(e){
			if(e.keyCode==13){
				$("#password").focus();
			}
		});
		$('#password').keydown(function(e){
			if(e.keyCode==13){
				$("#capval").focus();
			}
		});
		$('#capval').keydown(function(e){
			if(e.keyCode==13){
				$("#loginBtn").click();
			}
		});
	});
})
</script>
#end

#define content()
<div class="login-container">
	<div class="container-content">
		<div class="title-box">
			<p class="title-content">#(APP.name)</p>
		</div>
		<div class="login-form">
			<form class="layui-form" >
				<div style="padding-top: 50px;"></div>
				<div class="input-box" >
					<div class="username-icon"></div>
					<input type="text" id="loginName" name="loginName" class="login-input username-input" style="outline:none;" lay-verify="required"  placeholder="请输入账号">
				</div>
				<div class="input-box">
					<div class="password-icon"></div>
					<input type="password" id="password" name="password" class="login-input password-input" style="outline:none;" lay-verify="required" placeholder="请输入密码">
				</div>
				<div class="input-box">
					<input type="text" id="capval" name="capval" autocomplete="off" class="login-input captcha-input" lay-verify="required" placeholder="请输入验证码" maxlength="4" minlength="4"><img id="captchaImg" class="captcha-img" alt="验证码" src="#(ctxPath)/captcha" onclick = "this.src='#(ctxPath)/captcha?'+Math.random()"/>
				</div>
				<div class="button-box">
					<button type="button" id="loginBtn" style="width: 100%;" class="layui-btn layui-btn-lg" lay-submit lay-filter="sub">登录</button>
				</div>
				<div class="button-box">
					<button type="reset"  style="width: 100%;" class="layui-btn layui-btn-lg reset-btn" >重置</button>
				</div>
			</form>
		</div>
	</div>
</div>
#end