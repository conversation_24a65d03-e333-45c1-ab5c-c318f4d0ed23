#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()营销二维码活动管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        qrcodeLoad(null);

        sd=form.on("submit(search)",function(data){
            qrcodeLoad(data.field);
            return false;
        });

        function qrcodeLoad(data){
            table.render({
                id : 'qrcodeTable'
                ,elem : '#qrcodeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/activityQrcode/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'qrcode', title: 'code', align: 'center', unresize: true}
                    ,{field:'name', title: '营销人', align: 'center', unresize: true}
                    ,{field:'fullName', title: '分公司', align: 'center', unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/activityQrcode/form" ;
            pop_show("新增活动二维码",url,900,500);
        });

        table.on('tool(qrcodeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/activityQrcode/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('qrcodeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/activityQrcode/form?id=" + obj.data.id ;
                pop_show("查看活动二维码",url,900,500);
            }
        });

        <!--region Description-->
        //批量获取被作废数据
        /*getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('qrcodeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }*/
        //批量作废
        /*$("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/mms/activityQrcode/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {memberData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("qrcodeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });*/
        <!--endregion-->
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:marketQrcode:checkBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">查看</a>
    #end
    #shiroHasPermission("member:marketQrcode:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
<!--图片展示替换模板-->
<script type="text/html" id="imgTpl">
    <img src="#(imgPath){{ d.headPic }}" height="30px">
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">

        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            营销人:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            &nbsp;&nbsp;
            所属分公司:
            <div class="layui-inline">
                <select id="officeId" name="officeId" lay-search>
                    <option value="">请选择所属分公司</option>
                    #for(o : officeList)
                    <option value="#(o.id)">#(o.fullName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:marketQrcode:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加二维码</button>
        #end
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table id="qrcodeTable" lay-filter="qrcodeTable"></table>
</div>
#end