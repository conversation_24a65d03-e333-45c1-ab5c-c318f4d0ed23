#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户登记编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, $ = layui.jquery
	, laydate = layui.laydate
	;
	
	//时间渲染
	laydate.render({
		elem: '#registerDate'
		, format: 'yyyy-MM-dd HH:mm:ss'
	});
	
	form.on('select(memberFilter)', function(data){
		var memberId = data.value;
		if(memberId!=null && memberId!=''){
			var memberName = $("#memberId").find('option[value="'+memberId+'"]').text();
			$("#memberName").val(memberName);
			$("#customerName").val(memberName);
			if(memberId!=null && memberId!=''){
				util.sendAjax ({
		            type: 'POST',
		            url: '#(ctxPath)/mms/member/getMemberInfo',
		            data: {memberId:memberId},
		            notice: false,
				    loadFlag: false,
		            success : function(rep){
		            	if(rep.state=='ok'){
			            	$("#cardNumber").val(rep.memberCard);
							$("#phoneNumber").val(rep.memberPhone);
							$("#customerArea").val(rep.memberArea);
		            	}else{
		            		$("#cardNumber").val("");
							$("#phoneNumber").val("");
							$("#customerArea").val("");
		            	}
		            	form.render('select');
		            },
		            complete : function() {
				    }
		        });
			}
		}else{
			$("#memberName").val('');
		}
	});
	
	//校验
	form.verify({
		checkPhone:function(value){
			if(value != null && value.length >0){
				var reg = new RegExp("^(13[0-9]|14[0-9]|15[0-9]|18[0-9]|17[0-9])\\d{8}$");
				if(!reg.test(value)){
					return "手机号码格式不正确";
				}
			}
		}
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/consult/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("consultRegisterTable",null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>日期</label>
		<div class="layui-input-block">
			<input type="text" id="registerDate" name="registerDate" class="layui-input" lay-verify="required" value="#date(model.registerDate??'', 'yyyy-MM-dd HH:mm:ss')" placeholder="请输入日期" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">会员姓名</label>
		<div class="layui-input-block">
			<select id="memberId" name="memberId" lay-filter="memberFilter" lay-search>
				<option value="">请选择会员</option>
				#for(m : memberList)
					<option value="#(m.id)" #(m.id == model.memberId?'selected':'')>#(m.fullName)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>客户姓名</label>
		<div class="layui-input-block">
			<input type="text" id="customerName" name="customerName" class="layui-input" lay-verify="required" value="#(model.customerName??)" placeholder="请输入客户姓名" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">卡号</label>
		<div class="layui-input-block">
			<input type="text" id="cardNumber" name="cardNumber" class="layui-input" value="#(model.cardNumber??)" placeholder="请输入卡号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>电话号码</label>
		<div class="layui-input-block">
			<input type="text" id="phoneNumber" name="phoneNumber" class="layui-input" lay-verify="checkPhone" value="#(model.phoneNumber??)" placeholder="请输入电话号码" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">客户所在地区</label>
		<div class="layui-input-block">
			<input type="text" id="customerArea" name="customerArea" class="layui-input" value="#(model.customerArea??)" placeholder="请输入客户所在地区" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">所属部门</label>
		<div class="layui-input-block">
			<input type="text" name="belongBranchOffice" class="layui-input" value="#(model.belongBranchOffice??)" placeholder="请输入所属部门" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>客户诉求类型</label>
		<div class="layui-input-block">
			<select id="appealType" name="appealType" lay-verify="required">
				<option value="">请选择客户诉求类型</option>
				#getDictList("appealType")
					<option value="#(key)" #(model != null ?(key == model.appealType ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">咨询受理内容</label>
		<div class="layui-input-block">
			<input type="text" name="consultContent" class="layui-input" value="#(model.consultContent??)" placeholder="请输入咨询受理内容" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">接待人员</label>
		<div class="layui-input-block">
			<input type="text" name="receptionPeople" class="layui-input" value="#(model.receptionPeople??)" placeholder="请输入接待人员" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">解决方案</label>
		<div class="layui-input-block">
			<input type="text" name="solution" class="layui-input" value="#(model.solution??)" placeholder="请输入解决方案" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remarks" class="layui-textarea" placeholder="请输入备注">#(model.remarks??)</textarea>
		</div>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<input type="hidden" id="memberName" name="memberName" value="#(model.memberName??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end