#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡设备展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/cardEquipment/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#cardEquipmentForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("cardEquipmentTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form" style="margin-left: 10px;margin-top: 20px;" id="cardEquipmentForm">
    <input type="hidden" id="cardEquipmentId" name="cardEquipment.id" value="#(cardEquipment.id??)"/>
    <div class="layui-form-item">
        <label class="layui-form-label">会员卡号</label>
        <div class="layui-input-inline">
            <input type="text" name="cardNumber" class="layui-input" lay-verify="required" value="#(card.cardNumber??)" placeholder="请输入会员卡号">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">设备型号/类型</label>
        <div class="layui-input-inline">
            <select name="cardEquipment.equipmentTypeId" lay-search lay-verify="required">
                <option value="">请选择设备型号/类型</option>
                #for(t : typeList)
                <option value="#(t.id)" #(cardEquipment != null ? (t.id == cardEquipment.equipmentTypeId?'selected':'') :'')>#(t.equipmentModel) / #(t.equipmentTypeName)</option>
                #end
            </select>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">设备编号</label>
        <div class="layui-input-inline">
            <input type="text" name="cardEquipment.equipmentNo" class="layui-input" lay-verify="required" value="#(cardEquipment.equipmentNo??)" placeholder="请输入设备编号">
        </div>
    </div>
    <div class="layui-form-item" style="margin-left: 150px; margin-top: 70px;">
        <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
    </div>
</form>
#getDictLabel("equipment_type")
#end
