#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()微信文章展示#end

#define css()
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
<div class="layui-row" style="margin-bottom:50px;">
    <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="articleInfoForm">
        <div class="layui-row">
            <table class="layui-table" lay-skin="nob">
                <colgroup>
                    <col width="40%">
                    <col width="40%">
                    <col width="20%">
                </colgroup>
                <tbody>
                <tr>
                    <td colspan="2">
                        <label class="layui-form-label"><span>*</span>文章标题</label>
                        <div class="layui-input-block">
                            <input type="text" id="title" name="wxArticle.title" value="#(wxArticle != null ?wxArticle.title:'')" autocomplete="off" placeholder="请输入微信文章标题" class="layui-input" lay-verify="required">
                        </div>
                    </td>
                    <td rowspan="3" align="center" valign="bottom">
                        <div class="layui-upload">
                            <div class="layui-upload-list">
                                <img class="layui-upload-img" id="profilePhotoImg" width="100px" src="#(wxArticle ? (wxArticle.coverPic ? wxArticle.coverPic :'') : '')">
                                <p id="profilePhotoText"></p>
                                <input type="hidden" id="uploadPath" name="wxArticle.coverPic" value="#(wxArticle.coverPic ??)">
                            </div>
                        </div>
                        <button type="button" class="layui-btn" id="profilePhoto">上传文章封面</button>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <label class="layui-form-label"><span>*</span>文章访问路径</label>
                        <div class="layui-input-block">
                            <input type="text" id="url" name="wxArticle.url" value="#(wxArticle != null ? wxArticle.url:'')"  placeholder="请输入微信文章访问路径" class="layui-input" lay-verify="required">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <label class="layui-form-label">文章摘要</label>
                        <div class="layui-input-block">
                            <textarea placeholder="请输入微信文章摘要" id="digest" name="wxArticle.digest" rows="5" class="layui-textarea">#(wxArticle != null ? wxArticle.digest:'')</textarea>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
                <input type="hidden" id="fileId" name="wxArticle.fileId" value="#(wxArticle.fileId??)"/>
                <input name="wxArticle.id" id="id" type="hidden" value="#(wxArticle != null ? wxArticle.id : '')" />
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
            </div>
        </div>
    </form>
</div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script type="text/javascript">
    layui.use(['form','upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var upload = layui.upload;


        //保存
        form.on('submit(confirmBtn)', function(){
            var url = "#(ctx)/mms/wxarticle/save";
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/wxarticle/save',
                data: $("#articleInfoForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.layui.table.reload('articleTable');
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


        //普通图片上传
        var uploadInst = upload.render({
            elem: '#profilePhoto'
            ,url:$("#commonUpload").val() +'/upload?bucket=profilePhoto'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#profilePhotoImg').attr('src', result);
                });
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){
                    $("#uploadPath").val(res.data.src);
                    $("#fileId").val(res.data.id);
                    layer.msg(res.msg,{icon:1,time:5000});
                }else {
                    return layer.msg('上传失败');
                }
            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });

});
</script>
#end