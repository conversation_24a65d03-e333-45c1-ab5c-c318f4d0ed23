#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()现场抽奖活动首页#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <div class="layui-input-inline">
			            <div class="layui-btn-group">
							#shiroHasPermission("member:activity:addBtn")
					        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
							#end
					        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
		    			</div>
			        </div>
	    		</div>
			</form>
	    </span>
    </div>
	<div class="layui-row">
		<table id="activityTable" lay-filter="activityTable"></table>
	</div>
</div>
#end

#define js()
<script type="text/html" id="activityTableBar">
<div class="layui-btn-group">
	#shiroHasPermission("member:activity:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:activity:downloadQrcodeBtn")
	<a class="layui-btn layui-btn-xs" lay-event="createQrcode">下载二维码</a>
	#end
	#shiroHasPermission("member:activity:detailBtn")
	<a class="layui-btn layui-btn-xs" lay-event="detail">抽奖名单</a>
	#end
	#shiroHasPermission("member:activity:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																	
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'activityTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/mms/activity/pageTable'
	    , method: 'post'
// 	    , height: vipTable.getFullHeight()    //容器高度
	    , where: {}
	    , cols: [[                  //标题栏
	          {type: 'numbers', title: '序号', width: 60, unresize:true}
	        , {field: 'baseName', title: '基地名称', unresize:true}
	        , {field: 'activityName', title: '活动名称', unresize:true}
	        , {field: '', title: '开始时间', unresize:true,templet:"<div>{{ dateFormat(d.startTime,'yyyy-MM-dd HH:mm') }}</div>"}
	        , {field: '', title: '结束时间', unresize:true,templet:"<div>{{ dateFormat(d.endTime,'yyyy-MM-dd HH:mm') }}</div>"}
	        , {field: '', title: '是否失效',unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge layui-bg-green'>有效</span>':d.isEnable == '1'? '<span class='layui-badge'>失效</span>':'- -'}}</div>"}
	        , {field: 'activityQrcode', title: '活动二维码', unresize:true}
	        , {field: 'remark', title: '备注', unresize:true}
	        , {fixed: 'right', title: '操作', width: 230, align: 'center', toolbar: '#activityTableBar'} //这里的toolbar值是模板元素的选择器
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
	// 表格绑定事件
    table.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/mms/activity/edit?id='+obj.data.id,'500','600');
		}else if(obj.event==='createQrcode'){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/mms/activity/createQrcode?id='+obj.data.id,
				loadFlag: true,
				success : function(rep){
					if(rep.state==='ok'){
						downloadFileByBase64(rep.imgName,obj.data.activityName+"_二维码");
					}
				},
				complete : function() {
				}
			});

        }else if(obj.event==="detail") {//名单按钮事件

        	//名单操作
        	pop_show('抽奖名单','#(ctxPath)/mms/activity/detail?id='+obj.data.id,'','');
        }else if(obj.event==="del"){//作废按钮事件
        	//作废操作
    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mms/activity/del',
                    data: {id:obj.data.id, delFlag:'1'},
                    loadFlag: true,
                    success : function(rep){
                    	pageTableReload();
                    },
                    complete : function() {
        		    }
                });
				layer.close(index);
            });
        }
    });

	function dataURLtoBlob(dataurl) {
		var arr = dataurl.split(','), mime = arr[0].match(/:(.*?);/)[1],
				bstr = atob(arr[1]), n = bstr.length, u8arr = new Uint8Array(n);
		while (n--) {
			u8arr[n] = bstr.charCodeAt(n);
		}
		return new Blob([u8arr], { type: mime });
	}

	function downloadFile(url,name='What\'s the fuvk'){
		var a = document.createElement("a")
		a.setAttribute("href",url)
		a.setAttribute("download",name)
		a.setAttribute("target","_blank")
		let clickEvent = document.createEvent("MouseEvents");
		clickEvent.initEvent("click", true, true);
		a.dispatchEvent(clickEvent);
	}

	function downloadFileByBase64(base64,name){
		var myBlob = dataURLtoBlob(base64)
		var myUrl = URL.createObjectURL(myBlob)
		downloadFile(myUrl,name)
	}

    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		pop_show('添加','#(ctxPath)/mms/activity/add','500','600');
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
})
</script>
#end