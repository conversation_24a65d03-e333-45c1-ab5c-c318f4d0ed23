#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
    .layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
    img{
        width:100px;
        max-height:110px;
    }
</style>
#end

#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;

        //保存
        form.on('submit(confirmBtn)', function(){
            var url = "#(ctxPath)/mms/smsType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#typeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.ssmReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

        form.on('radio(sendingMethod)',function (d) {
            if(d.value==='everyDay'){
                $("#typeName").val("生日祝福");
                $("#typeName").prop("readonly",true);
                $("#everyDaySendContentDiv").css('display','block');
                $("#everyDaySendContent").attr("lay-verify","required");
            }else{
                $("#everyDaySendContent").val('');
                $("#everyDaySendContentDiv").css('display','none');
                $("#everyDaySendContent").attr("lay-verify","");
                $("#typeName").val("");
                $("#typeName").prop("readonly",false);
            }
            return false;
        })
    });
</script>

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="typeForm">

            <div class="layui-form-item">
                <label class="layui-form-label">类型名称</label>
                <div class="layui-input-block">
                    <input type="text" name="typeName" id="typeName" lay-verify="required" placeholder="请输入类型名称" autocomplete="off" value="#(type.typeName??)" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">发送方式</label>
                <div class="layui-input-block">
                    #getDictList("sending_method")
                    <input type="radio" name="sendingMethod" lay-filter="sendingMethod" lay-verify="required" title="#(value)" value="#(key)" #if((type==null && key=='fixed') || (type!=null && key==type.sendingMethod)) checked #end #>
                    #end
                </div>
            </div>
            <div class="layui-form-item layui-form-text" id="everyDaySendContentDiv" #if(type==null || (type!=null && type.sendingMethod!="everyDay"))style="display: none;" #end >
                <label class="layui-form-label">每天发送方式短信内容</label>
                <div class="layui-input-block">
                    <textarea name="everyDaySendContent" id="everyDaySendContent" lay-verify="required" placeholder="请输入每天发送方式内容" maxlength="255" class="layui-textarea">#(type.everyDaySendContent??)</textarea>
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入内容" maxlength="255" class="layui-textarea">#(type.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" id="id" type="hidden" value="#(type.id??)" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>

#end