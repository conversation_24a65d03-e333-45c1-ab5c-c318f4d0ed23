#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户信息新增统计首页#end

#define css()
#end

#define content()
<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
	<div class="layui-row">
		<div class="layui-inline">
			<label class="layui-form-label">开始时间</label>
			<div class="layui-input-inline">
				<input id="startDate" name="startDate" class="layui-input" value="#(startDate??)" placeholder="请选择创建开始日期">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">结束时间</label>
			<div class="layui-input-inline">
				<input id="endDate" name="endDate" class="layui-input" value="#(endDate??)" placeholder="请选择创建结束日期">
			</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		</div>
	</div>
</form>
<div class="layui-row">
<table class="layui-table">
	<colgroup>
		<col width="10%">
		<col width="60%">
		<col width="30%">
		<col>
	</colgroup>
	<thead>
		<tr>
			<th>序号</th>
			<th>所属组织</th>
			<th>数量</th>
		</tr> 
	</thead>
	<tbody id="newCountList">
	</tbody>
</table>
</div>
#end

#define js()
<script id="newCountTrTpl" type="text/html">
	<tr>
		<td>{{d.idx}}</td>
		<td>{{d.orgName}}</td>
		<td>{{d.newCount}}</td>
	</tr>
</script>
<script id="newCountNoneTpl" type="text/html">
	<tr>
		<td align="center" colspan="3">无数据</td>
	</tr>
</script>
<script>
	layui.use(['form','layer','table', 'laydate', 'laytpl'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate, laytpl = layui.laytpl;

		customerLoad();
		
		//时间渲染
		laydate.render({
			elem: '#startDate'
			, format: 'yyyy-MM-dd'
		});
		
		//时间渲染
		laydate.render({
			elem: '#endDate'
			, format: 'yyyy-MM-dd'
		});
		
		function customerLoad(){
			var startDate = $('#startDate').val();
			var endDate = $('#endDate').val();
			$('#newCountList').empty();
			$.post("#(ctx)/mms/customer/newCountList", {startDate:startDate, endDate:endDate}, function(newCountList) {
				if(newCountList!=null && newCountList.length>0){
					$.each(newCountList, function(i, newObj){
						laytpl(newCountTrTpl.innerHTML).render({"idx":(i+1), "orgName":newObj.orgName, "newCount":newObj.newCount}, function(html){
							$('#newCountList').append(html);
						});
					});
				}else{
					laytpl(newCountNoneTpl.innerHTML).render({}, function(html){
						$('#newCountList').append(html);
					});
				}
			});
		};
		
		form.on("submit(search)",function(data){
			customerLoad();
			return false;
		});
	});
</script>
#end