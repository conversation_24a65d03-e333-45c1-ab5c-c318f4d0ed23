#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券核销首页#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
		<div class="layui-inline">
			<label class="layui-form-label">卡券编号</label>
			<div class="layui-input-inline">
				<input id="rollNumber" name="rollNumber" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<input type="hidden" name="createBy" class="layui-input" value="#(userId??)">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("crm:cardRollVerify:addBtn")
	        	<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
			#end
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="verifyTable" lay-filter="verifyTable"></table>
</div>
#getDictLabel("gender")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRollVerify:editBtn")
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("crm:cardRollVerify:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		verifyLoad(null);

		function verifyLoad(data){
			table.render({
				id : 'verifyTable'
				,elem : '#verifyTable'
				,method : 'POST'
				,where : data
				,url : '#(ctxPath)/mms/cardRoll/verifyPageTable'
				,cellMinWidth: 80
				,width:$(document).width()
				,height:'full-200'
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field:'rollNumber', title: '卡券编号', align: 'center', unresize: true}
					,{field: '', title: '核销时间', unresize:true,templet:"<div>{{ dateFormat(d.verifyTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{field:'activityName', title: '活动名称', align: 'center', unresize: true}
					,{field:'fullName', title: '姓名', align: 'center', unresize: true}
					,{field:'idcard', title: '身份证号', align: 'center', unresize: true}
					,{field:'phoneNumber', title: '手机号', align: 'center', unresize: true}
					,{field: '', title: '是否会员',unresize:true,templet:"<div>{{d.isMember == '0'? '<span class='layui-badge'>否</span>':d.isMember == '1'? '<span class='layui-badge layui-bg-green'>是</span>':'- -'}}</div>"}
					,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
					,{field: '', title: '核销状态',unresize:true,templet:"<div>{{d.verifyStatus == '0'? '<span class='layui-badge'>未核销</span>':d.verifyStatus == '1'? '<span class='layui-badge layui-bg-green'>已核销</span>':'- -'}}</div>"}
					,{field: '', title: '创建时间', unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 15
				,limits : [15,25,35,45]
			});
		};
		table.on('tool(verifyTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/cardRoll/verifyEdit?id="+obj.data.id;
				pop_show("编辑",url,'','');
			}else if(obj.event === 'del'){
				layer.confirm("确定作废?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/mms/cardRoll/verifyDelete",
						type:'post',
						data:{"id":obj.data.id, delFlag:'1'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('verifyTable');
							}
							layer.close(index);
						}
					});
				});
			}
		});
		
		//批量获取被选中的数据，返回id
		getCheckTableData = function(){
			return '';
		}
		
		form.on("submit(search)",function(data){
			verifyLoad(data.field);
			return false;
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/mms/cardRoll/verifyAdd','','');
		});
		
		
	});
</script>
#end