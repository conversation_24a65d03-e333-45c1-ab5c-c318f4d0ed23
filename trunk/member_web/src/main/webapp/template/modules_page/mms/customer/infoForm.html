#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户信息编辑页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
	.layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
	img{
		width:100px;
		max-height:110px;
	}
</style>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label">会员选择</label>
		<div class="layui-input-block">
			<select id="memberId" name="memberId" lay-filter="memberFilter" lay-search>
				<option value="">请选择会员</option>
				#for(m : memberList)
					<option value="#(m.id)" #(m.id == model.memberId?'selected':'')>#(m.fullName)(#(m.idcard))</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>客户类别</label>
		<div class="layui-input-block">
			<select id="customerType" name="customerType" lay-verify="required">
				<option value="not_member" >非会员</option>
				<option value="member" >会员</option>
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>客户姓名</label>
		<div class="layui-input-block">
			<input type="text" id="memberName" name="memberName" class="layui-input" lay-verify="required" value="#(model.memberName??)" placeholder="请输入客户姓名" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>性别</label>
		<div class="layui-input-block">
			<select id="gender" name="gender" lay-verify="required">
				<option value="">请选择</option>
				#getDictList("gender")
					<option value="#(key)" model != null ?(key == model.gender ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>出生日期</label>
		<div class="layui-input-block">
			<input type="text" id="birthday" name="birthday" class="layui-input" value="#(model.birthday??'')" placeholder="请输入出生日期" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>身份证号</label>
		<div class="layui-input-block">
			<input type="text" id="idcard" name="idcard" value="#(model.idcard??'')" class="layui-input" lay-verify="required|identity" placeholder="请输入身份证号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>电话号码</label>
		<div class="layui-input-block">
			<input type="text" id="phoneNumber" name="phoneNumber" class="layui-input" lay-verify="required|phone" value="#(model.phoneNumber??)" placeholder="请输入电话号码" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">居住区域</label>
		<div class="layui-input-block">
			<input type="hidden" id="province" name="province" value="#(model.province??)" />
			<input type="hidden" id="city" name="city" value="#(model.city??)" />
			<input type="hidden" id="town" name="town" value="#(model.town??)">
			<input type="hidden" id="street" name="street" value="#(model.street??)">
			<input type="hidden" id="regidentProvinceName" value="#(province??)" />
			<input type="hidden" id="regidentCityName" value="#(city??)" />
			<input type="hidden" id="regidentCountyName" value="#(town??)">
			<input type="hidden" id="regidentStreetName" value="#(street??)">
			<input type="text" id="regidentAddress" readonly="readonly"  name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">详细住址</label>
		<div class="layui-input-block">
			<input type="text" id="address" name="address" value="#(model.address??'')" autocomplete="off" placeholder="请输入详细住址" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remarks" class="layui-textarea" placeholder="请输入备注">#(model.remarks??)</textarea>
		</div>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script id="regidentAreaTpl" type="text/html">
	<div id="regidentArea" style="width:700px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
		<div class="tabs clearfix">
			<ul>
				<li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
				<li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
				<li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
				<li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
			</ul>
		</div>
		<div class="con" style="height:300px;overflow-y: auto;">
			<div class="regidentProvinceAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentCityAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentTownAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentStreetAll">
				<div class="list">

				</div>
			</div>
		</div>
	</div>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, $ = layui.jquery
	, laydate = layui.laydate
	;
	
	//时间渲染
	laydate.render({
		elem: '#birthday'
		, format: 'yyyy-MM-dd'
	});
	
	form.on('select(memberFilter)', function(data){
		var memberId = data.value;
		if(memberId!=null && memberId!=''){
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/mms/member/getMemberInfo',
	            data: {memberId:memberId},
	            notice: false,
			    loadFlag: false,
	            success : function(rep){
	            	if(rep.state=='ok'){
						$("#customerType").val('member');
						$("#memberName").val(rep.member.fullName);
						$("#gender").val(rep.member.gender);
						$("#birthday").val(rep.member.birthday);
						$("#idcard").val(rep.member.idcard);
						$("#phoneNumber").val(rep.member.telephone);
						$("#province").val(rep.member.province);
						$("#city").val(rep.member.city);
						$("#town").val(rep.member.town);
						$("#street").val(rep.member.street);
						$("#regidentProvinceName").val(rep.province);
						$("#regidentCityName").val(rep.city);
						$("#regidentCountyName").val(rep.town);
						$("#regidentStreetName").val(rep.street);
						$("#regidentAddress").val(rep.province+rep.city+rep.town+rep.street);
						$("#address").val(rep.member.address);
	            	}
	            },
	            complete : function() {
			    }
	        });
		}else{
			$("#customerType").val('not_member');
			$("#memberName").val('');
			$("#gender").val('');
			$("#birthday").val('');
			$("#idcard").val('');
			$("#phoneNumber").val('');
			$("#province").val('');
			$("#city").val('');
			$("#town").val('');
			$("#street").val('');
			$("#regidentProvinceName").val('');
			$("#regidentCityName").val('');
			$("#regidentCountyName").val('');
			$("#regidentStreetName").val('');
			$("#regidentAddress").val('');
			$("#address").val('');
		}
		form.render('select');
	});
	
	//身份证联动出生日期
	function idCardBirthday(psidno){
		var birthdayno,birthdaytemp;
		if (psidno.length == 18) {
			birthdayno=psidno.substring(6,14)
		} else if (psidno.length == 15) {
			birthdaytemp = psidno.substring(6,12);
			birthdayno = "19" + birthdaytemp;
		}else{
			return false
		}
		var birthday = birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8)
		return birthday
	}

	$('#idcard').on('keyup', function() {
		var birthday = idCardBirthday($(this).val());
		if (birthday) {
			$('#birthday').val(birthday);
		}
	});
	$('#idcard').on('blur', function() {
		var birthday = idCardBirthday($(this).val());
		if (birthday) {
			$('#birthday').val(birthday);
		}
	});
	
	//--------------------------居住区域begin---------------------------
	$('#regidentAddress').on('click', function() {
		//closeIdArea();

		$('#regidentArea').remove();
		var $this = $(this);
		var getTpl = regidentAreaTpl.innerHTML;
		$this.parent().append(getTpl);
		//event.stopPropagation();

		var street=$("#street").val();
		var regidentStreetName=$("#regidentStreetName").val();
		var town=$("#town").val();
		var regidentCountyName=$("#regidentCountyName").val();
		var city=$("#city").val();
		var regidentCityName=$("#regidentCityName").val();
		var province=$("#province").val();
		var regidentProvinceName=$("#regidentProvinceName").val();
		if(street!='' && regidentStreetName!=''){
			$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentStreetLoad(town);
			regidentCountyLoad(city);
			regidentCityLoad(province);
			regidentProvinceLoad();
			$(".con .regidentStreetAll").show().siblings().hide();
			//clickStreet(streetId,streetName);
		}else if(town!='' && regidentCountyName!=''){

			if(town!=''){
				regidentCityLoad(province);
				regidentCountyLoad(city);
				regidentProvinceLoad();
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/area/getAreas',
					data: {pid:town},
					notice:false,
					loadFlag: false,
					success : function(res){
						if(res.state=='ok'){
							if(res.data.length>0){
								$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								var html="<ul>";
								$.each(res.data, function(i, item){
									html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
								});
								html+="</ul>";
								$(".regidentStreetAll .list").append(html);
								//viewStreet(countyId,countyName);
								$(".con .regidentStreetAll").show().siblings().hide();
							}else{
								//无 街道信息
								$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								$(".con .regidentTownAll").show().siblings().hide();
							}
						}
					},
					complete : function() {
					}
				});
			}
		}else if(city!='' && regidentCityName!=''){
			regidentProvinceLoad();
			regidentCityLoad(province);
			viewRegidentCounty(city,regidentCityName);
		}else if(province!='' && regidentProvinceName!=''){
			regidentProvinceLoad();
			viewRegidentCity(province,regidentProvinceName);
		}else{
			regidentProvinceLoad();
		}





		//去除事件冒泡
		var evt =  new Object;
		if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
			evt = arguments.callee.caller.arguments[0];
		}else{
			evt = event || window.event;
		}
		evt.cancelBubble = true;
		$('#regidentArea').off('click');
		$('#regidentArea').on('click', function() {
			//event.stopPropagation();
			//去除事件冒泡
			var evt =  new Object;
			if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
				evt = arguments.callee.caller.arguments[0];
			}else{
				evt = event || window.event;
			}
			evt.cancelBubble = true;
		})
	});

	regidentProvinceLoad=function(){
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/area/getAreas',
			data: {pid:''},
			notice:false,
			loadFlag: false,
			success : function(res){
				if(res.state=='ok'){
					if(res.data.length>0){
						$(".regidentProvinceAll .list").empty();
						var html="<ul>";
						$.each(res.data, function(i, item){
							html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCity(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
						});
						html+="</ul>";
						$(".regidentProvinceAll .list").append(html);
					}
				}
			},
			complete : function() {
			}
		});
	};

	//点击省事件
	viewRegidentCity=function(province,regidentProvinceName) {
		$("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentCityAll").show().siblings().hide();
		$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		//点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
		$("#province").val(province);
		$("#regidentProvinceName").val(regidentProvinceName);
		$("#city").val("");
		$("#regidentCityName").val("");
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCityLoad(province);
	};

	//加载市
	regidentCityLoad=function(province){
		if(province!=''){
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/area/getAreas',
			data: {pid:province},
			notice:false,
			loadFlag: false,
			success : function(res){
				if(res.state=='ok'){
					if(res.data.length>0){
						$(".regidentCityAll .list").empty();
						var html="<ul>";
						$.each(res.data, function(i, item){
							html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCounty(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
						});
						html+="</ul>";
						$(".regidentCityAll .list").append(html);
					}
				}
			},
			complete : function() {
			}
		});
		}
	};

	//点击市事件
	viewRegidentCounty=function(city,RegidentCityName){
		$("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentTownAll").show().siblings().hide();
		$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#city").val(city);
		$("#regidentCityName").val(RegidentCityName);
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCountyLoad(city);
	};

	//加载区/县
	regidentCountyLoad=function(city){
		if(city!=''){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid:city},
				notice:false,
				loadFlag: false,
				success : function(res){
					if(res.state=='ok'){
						if(res.data.length>0){
							$(".regidentTownAll .list").empty();
							var html="<ul>";
							$.each(res.data, function(i, item){
								html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentStreet(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
							});
							html+="</ul>";
							$(".regidentTownAll .list").append(html);
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	//点击区/县事件
	viewRegidentStreet=function(town,regidentCountyName){
		$("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentStreetAll").show().siblings().hide();
		$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#town").val(town);
		$("#regidentCountyName").val(regidentCountyName);
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentStreetLoad(town);
	};

	//加载街道/镇/乡
	regidentStreetLoad=function(town){
		if(town!=''){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid:town},
				notice:false,
				loadFlag: false,
				success : function(res){
					if(res.state=='ok'){
						if(res.data.length>0){
							$(".regidentStreetAll .list").empty();
							var html="<ul>";
							$.each(res.data, function(i, item){
								html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.name+'\')">'+item.name+'</a></li>';
							});
							html+="</ul>";
							$(".regidentStreetAll .list").append(html);
						}else{
							//无 街道信息
							clickRegidentStreet('','');
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	clickRegidentStreet=function(street,regidentStreetName){
		$("#street").val(street);
		$("#regidentStreetName").val(regidentStreetName);
		var regidentProvinceName=$("#regidentProvinceName").val();
		var regidentCityName=$("#regidentCityName").val();
		var regidentCountyName=$("#regidentCountyName").val();
		var regidentStreetName=$("#regidentStreetName").val();
		var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
		$("#regidentAddress").val(add);
		$('#regidentArea').remove();
	};

	regidentProvinceAllClick=function(){
		//$(".con .provinceAll").show();
		$("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentProvinceAll").show().siblings().hide();
	};

	regidentCityAllClick=function(){
		// $(".con .cityAll").show();
		if($("#province").val()!=''){
			$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCityLoad($("#province").val());
			$(".con .regidentCityAll").show().siblings().hide();
		}
	};

	regidentTownAllClick=function(){
		if($("#city").val()!=''){
			$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCountyLoad($("#city").val());
			$(".con .regidentTownAll").show().siblings().hide();
		}
	};

	regidentStreetAllClick=function(){
		if($("#town").val()!=''){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid:$("#town").val()},
				notice:false,
				loadFlag: false,
				success : function(res){
					if(res.state=='ok'){
						if(res.data.length>0){
							$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							regidentStreetLoad($("#town").val());
							$(".con .regidentStreetAll").show().siblings().hide();
						}else{
							//无 街道信息 显示区/县信息
							$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							//countyLoad(cityId);
							$(".con .regidentTownAll").show().siblings().hide();
						}
					}
				},
				complete : function() {
				}
			});



		}
	};

	//关闭区域选择器
	closeRegidentArea=function(){
		if(typeof($('#regidentArea').html())!='undefined'){
			var regidentProvinceName=$("#regidentProvinceName").val();
			var regidentCityName=$("#regidentCityName").val();
			var regidentCountyName=$("#regidentCountyName").val();
			var regidentStreetName=$("#regidentStreetName").val();
			var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
			$("#regidentAddress").val(add);
		}
		//alert(1);
		$('#regidentArea').remove();
		//console.log($('#regidentArea').html());
	}

	$('body').on('click', function() {
		closeRegidentArea();
	});

	//-------------------------居住区域end----------------------------
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/customer/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("infoTable",null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end