#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户回访页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
		<div class="layui-inline">
			<label class="layui-form-label">姓名</label>
			<div class="layui-input-inline">
				<input id="memberName" name="memberName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">身份证</label>
			<div class="layui-input-inline">
				<input id="idcard" name="idcard" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">手机号</label>
			<div class="layui-input-inline">
				<input id="phoneNumber" name="phoneNumber" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">客户类别</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="customerType">
					<option value="">请选择</option>
					<option value="member" >会员</option>
					<option value="not_member" >非会员</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			#if(lacksPermission("member:visit:assignBtn"))
				<input type="hidden" id="assignUserId" name="createBy" value="#(assignUserId??)">
			#end
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		</div>
		#shiroHasPermission("member:visit:assignBtn")
		<div class="layui-inline">
			<label class="layui-form-label">分配人</label>
			<div class="layui-input-inline" style="width:100px;">
				<select id="assignUserId" name="createBy" lay-search>
					<option value="">请选择</option>
					#for(u : empUserList)
						<option value="#(u.user_id)">#(u.full_name)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<button type="button" id="assignBtn" class="layui-btn">分配</button>
		</div>
		#end
	</form>
</div>
<div class="layui-row layui-col-space10"">
	<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
		<fieldset class="layui-elem-field">
			<legend>客户列表</legend>
			<table id="consultRegisterTable" lay-filter="consultRegisterTable"></table>
		</fieldset>
	</div>
	<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
		<fieldset class="layui-elem-field">
			<legend>回访列表</legend>
			<table id="visitTable" lay-filter="visitTable"></table>
		</fieldset>
	</div>
</div>
#getDictLabel("gender","appealType","visitType","followResult","handleResult")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("member:visit:addBtn")
	<a class="layui-btn layui-btn-xs" lay-event="add">添加</a>
	#end
</div>
</script>
<script type="text/html" id="visitTableBar">
	#shiroHasPermission("member:visit:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:visit:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

	#if(lacksPermission("member:visit:assignBtn"))
		consultLoad({createBy:'#(assignUserId??)'});
	#else
		consultLoad(null);
	#end

	form.on("submit(search)",function(data){
		consultLoad(data.field);
		return false;
	});

	function consultLoad(data){
		table.render({
			id : 'consultRegisterTable'
			,elem : '#consultRegisterTable'
			,method : 'POST'
			,where : data
			,height:$(document).height()*0.8
			,url : '#(ctxPath)/mms/consult/pageTable'
			,cellMinWidth: 80
			,cols: [[
				{type:'checkbox'}
				,{type: 'numbers', title: '序号', width: 60, unresize:true}
				,{field:'member_name', title: '姓名', align: 'center', unresize: true}
				,{field:'', title: '性别', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
				,{field:'birthday', title: '出生日期', align: 'center', unresize: true}
				,{field:'phone_number', title: '手机号', align: 'center', unresize: true}
				#shiroHasPermission("member:visit:assignBtn")
				,{field:'fullName', title: '分配人', align: 'center', unresize: true}
				#end
				,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			,page : true
			,limit : 10
			,limits : [10,20,30,40]
			, loading: true
		    , done: function (res, curr, count) {
		    	// 单击行触发回调函数
                   clickRow(this , table , function(data) {
                       var registerId = data.id;
                       visitLoad(registerId);
                   });
		    }
		});
		table.on('tool(consultRegisterTable)',function(obj){
			if(obj.event === 'add'){
				pop_show('添加','#(ctxPath)/mms/consult/visitAdd?registerId='+obj.data.id,'','');
			}else if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/consult/form?id="+obj.data.id;
				pop_show("编辑",url,800,600);
			}else if(obj.event === 'cancelAssign'){
				layer.confirm("确定取消?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/mms/consult/cancelAssign",
						type:'post',
						data:{"assignId":obj.data.assignId},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('consultRegisterTable');
							}
							layer.close(index);
						}
					});
				});
			}else if(obj.event === 'visit'){
				var url = "#(ctxPath)/mms/consult/viewVisit?registerId="+obj.data.id;
				pop_show("跟进情况",url,800,600);
			}
		});
	};
	
	function visitLoad(registerId){
		table.render({
		    id: 'visitTable'
		    , elem: '#visitTable'                  //指定原始表格元素选择器（推荐id选择器）
		    , even: true //开启隔行背景
		    , url: '#(ctxPath)/mms/consult/visitTable'
		    , method: 'post'
			, height:$(document).height()*0.8
		    , where: {registerId:registerId}
			, cellMinWidth: 80
		    , cols: [[                  //标题栏
		          {type: 'numbers', title: '序号', width: 60, unresize:true}
				, {field: '', title: '回访时间', unresize:true,templet:"<div>{{ dateFormat(d.visitDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
		        , {field: '', title: '回访类型', unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
		        , {field: '', title: '诉求类型', unresize:true,templet:"<div>{{ dictLabel(d.appealType,'appealType','- -') }}</div>"}
		        , {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#visitTableBar'} //这里的toolbar值是模板元素的选择器
		    ]]
		    , page: true
		    , limit : 10
			, limits : [10,20,30,40]
		    , loading: true
		    , done: function (res, curr, count) {
		    }
		});
		// 表格绑定事件
	    table.on('tool(visitTable)',function (obj) {
	        if(obj.event==="edit"){//编辑按钮事件
	        	pop_show('编辑','#(ctxPath)/mms/consult/visitEdit?id='+obj.data.id,'','');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/mms/consult/visitDel',
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                    	tableReload("visitTable",{registerId:obj.data.registerId});
	                    },
	                    complete : function() {
	        		    }
	                });
					layer.close(index);
	            });
	        }
	    });
	};
	
	// 添加
	$("#add").click(function(){
		$(this).blur();
		var url = "#(ctxPath)/mms/consult/form" ;
		pop_show("新增",url,800,600);
	});
	
	//批量获取被选中的数据，返回id
	getCheckTableData = function(){
		var cardCheckStatus = table.checkStatus('consultRegisterTable');
		// 获取选择状态下的数据
		return cardCheckStatus.data;
	}
	
	//批量分配
	$("#assignBtn").click(function(){
		var jsonData = getCheckTableData();
		var assignUserId = $('#assignUserId').val();
		if(jsonData == null || jsonData == ''){
			layer.msg('请勾选数据', function () {});
			return;
		}
		if(assignUserId == null || assignUserId == ''){
			layer.msg('请选择分配人员', function () {});
			return;
		}
		layer.confirm("确定分配?",function(index){
			util.sendAjax({
				url:"#(ctxPath)/mms/consult/assignSave",
				type:'post',
				data:{"registerData":JSON.stringify(jsonData), "assignUserId":assignUserId},
				notice:true,
				success:function(returnData){
					if(returnData.state==='ok'){
						table.reload('consultRegisterTable');
					}
					layer.close(index);
				}
			});
		});
	});
});
</script>
#end