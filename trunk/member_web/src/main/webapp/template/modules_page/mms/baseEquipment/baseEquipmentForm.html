#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地测量设备信息展示#end

#define css()

#end

#define content()
<div style="margin: 15px;">
    <div class="demoTable">
        <form class="layui-form layui-form-pane" action="" method="post" id="baseEquipmentForm" style="margin-left:25px;">
            <div class="layui-form-item">
                <label class="layui-form-label">设备编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="baseEquipment.equipmentNo" class="layui-input" lay-verify="required" value="#(baseEquipment.equipmentNo??)" placeholder="请输入设备编号">
                </div>
                <label class="layui-form-label">设备型号/类型</label>
                <div class="layui-input-inline">
                    <select name="baseEquipment.equipmentTypeId" lay-search lay-verify="required">
                        <option value="">请选择设备型号/类型</option>
                        #for(t : typeList)
                        <option value="#(t.id)" #(baseEquipment != null ? (t.id == baseEquipment.equipmentTypeId?'selected':'') :'')>#(t.equipmentModel) / #(t.equipmentTypeName)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">所属基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="baseEquipment.baseId" lay-filter="base" lay-search lay-verify="required">
                        <option value="">请选择所属基地</option>
                        #for(b : baseList)
                        <option value="#(b.id)" #(baseEquipment != null ? (b.id == baseEquipment.baseId?'selected':'') :'')>#(b.baseName)</option>
                        #end
                    </select>
                </div>
                <label class="layui-form-label">所属楼栋</label>
                <div class="layui-input-inline">
                    <div class="layui-form" lay-filter="buildingSelectFilter">
                        <select id="buildingId" name="baseEquipment.buildingId" lay-filter="building" lay-search lay-verify="required">
                            <option value="">请选择所属楼栋</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">所属楼层</label>
                <div class="layui-input-inline">
                    <div class="layui-form" lay-filter="floorSelectFilter">
                        <select id="floorId" name="baseEquipment.floorId" lay-filter="floorId" lay-search lay-verify="required">
                            <option value="">请选择所属楼层</option>
                        </select>
                    </div>
                </div>
                <label class="layui-form-label">场景类型</label>
                <div class="layui-input-inline">
                    <select name="baseEquipment.partType" lay-search lay-verify="required">
                        <option value="">请选择场景类型</option>
                        #getDictList("part_type")
                        <option value="#(key)" #(baseEquipment != null ?(key == baseEquipment.partType ? 'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">位置</label>
                <div class="layui-input-inline">
                    <select name="baseEquipment.position" lay-search lay-verify="required">
                        <option value="">请选择位置</option>
                        #getDictList("gateway_position")
                        <option value="#(key)" #(baseEquipment != null ?(key == baseEquipment.position ? 'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>
                <label class="layui-form-label">摆放方式</label>
                <div class="layui-input-inline">
                    <select name="baseEquipment.putWay" lay-search lay-verify="required">
                        <option value="">请选择摆放方式</option>
                        #getDictList("put_way")
                        <option value="#(key)" #(baseEquipment != null ?(key == baseEquipment.putWay ? 'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" id="baseEquipmentId" name="baseEquipment.id" value="#(baseEquipment.id??)">
                    <input type="hidden" id="baseIdItem" value="#(baseEquipment.baseId??)">
                    <input type="hidden" id="dId" value="#(baseEquipment.buildingId??)">
                    <input type="hidden" id="fId" value="#(baseEquipment.floorId??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;


        $(function(){
            if($("#baseEquipmentId").val() != null && $("#baseEquipmentId").val() != ''){
                var baseId = $("#baseId").val();
                var buildingId = $("#dId").val();
                var floorId = $("#fId").val();
                if(baseId != null && baseId != ''){
                    getBuildings(baseId,buildingId);
                }
                if(buildingId != null && buildingId != ''){
                    getFloors(buildingId,floorId);
                }
            }
        });

        //楼栋下拉框
        form.on('select(base)', function(data) {
            getBuildings(data.value,null);
        });
        //楼层下拉框
        form.on('select(building)', function(data) {
            getFloors(data.value,null)
        });

        //获取所有楼栋
        function getBuildings(data,selectedVal){
            $("#buildingId").empty();
            $("#buildingId").prepend("<option value=''>请选择所属楼栋</option>");
            $("#floorId").empty();
            $("#floorId").prepend("<option value=''>请选择所属楼层</option>");
            if(data == null || data == ''){
                form.render('select', 'buildingSelectFilter');
                form.render('select', 'floorSelectFilter');
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/baseEquipment/getBuildings',
                data: {id:data},
                notice: false,
                success : function(rep){
                    if (rep.state=='ok') {
                        if(rep.resultData.length>0){
                            $.each(rep.resultData, function(i, item){
                                if(selectedVal != null && selectedVal != '' && selectedVal == item.id){
                                    $("#buildingId").append("<option value='"+item.id+"' selected>"+item.buildingName+"</option>");
                                }else{
                                    $("#buildingId").append("<option value='"+item.id+"'>"+item.buildingName+"</option>");
                                }
                            });
                        }
                        form.render('select', 'buildingSelectFilter');
                        form.render('select', 'floorSelectFilter');
                    } else {
                        layer.msg(rep.msg, {icon : 5});
                    }
                },
                complete : function() {
                }
            });
        }

        //获取所有楼层
        function getFloors(data,selectedVal){
            $("#floorId").empty();
            $("#floorId").prepend("<option value=''>请选择所属楼层</option>");
            if(data == null || data == ''){
                form.render('select', 'floorSelectFilter');
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/baseEquipment/getFloors',
                data: {id:data},
                notice: false,
                success : function(rep){
                    if (rep.state=='ok') {
                        if(rep.resultData.length>0){
                            $.each(rep.resultData, function(i, item){
                                if(selectedVal != null && selectedVal != '' && selectedVal == item.id){
                                    $("#floorId").append("<option value='"+item.id+"' selected>"+item.floorName+"</option>");
                                }else{
                                    $("#floorId").append("<option value='"+item.id+"'>"+item.floorName+"</option>");
                                }
                            });
                        }
                        form.render('select', 'floorSelectFilter');
                    } else {
                        layer.msg(rep.msg, {icon : 5});
                    }
                },
                complete : function() {
                }
            });
        }

        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/baseEquipment/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#baseEquipmentForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("baseEquipmentTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
