#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人脸模板管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        smsLoad(null);

        //监听select
        /*form.on('select(smsType)',function (obj) {
            smsLoad({'smsType':obj.value});
        });*/

        $("#search").click(function () {
            ssmReload();
        });

        ssmReload=function(data){
            smsLoad({'smsType':$("#smsType").val()});
        }

        // 添加会员档案&人脸
        $("#add").click(function(){
            var url = "#(ctxPath)/mms/sms/form";
            pop_show("添加",url,1000,700);
        });

        function smsLoad(data){
            table.render({
                id : 'smsTable'
                ,elem : '#smsTable'
                ,method : 'POST'
                ,where : data
                ,height:$(window).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/sms/pageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true,width:90}
                    ,{field:'typeName', title: '短信类型', align: 'center', unresize: true}
                    ,{field:'name', title: '短信名称', align: 'center', unresize: true}
                    ,{field:'messageContent', title: '短信内容', align: 'center', unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{field:'sendTime', title: '短信发送时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.sendTime,'yyyy-MM-dd HH:mm:ss','- -') }}</div>"}
                    ,{field:'examineStatus', title: '审核状态', align: 'center', unresize: true,width:100,templet:function (d) {
                            if(d.examineStatus==='1'){
                                return '<span class="layui-badge layui-bg-blue">已审核</span>';
                            }else{
                                return '<span class="layui-badge">未审核</span>';
                            }
                        }}
                    ,{field:'examineTime', title: '审核时间', align: 'center', unresize: true,width:180,templet:"<div>{{ dateFormat(d.examineTime,'yyyy-MM-dd HH:mm:ss','- -') }}</div>"}
                    ,{field:'examineBy', title: '审核人', align: 'center', unresize: true,width:120}
                    ,{fixed:'right', title: '操作', align: 'center',width: 300, unresize: true, toolbar: '#actionBar'}
        ]]
            ,page : true
        });
        };


        table.on('tool(smsTable)',function(obj) {
            if(obj.event==='edit'){
                var url = "#(ctxPath)/mms/sms/form?id="+obj.data.id;
                pop_show("编辑",url,1000,700);
            }else if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/sms/del',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                ssmReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'examine'){
                layer.confirm("确定要审核吗?",function(index){
                    layer.load();
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/sms/examine',
                        notice: true,
                        data: {id:obj.data.id,examineStatus:'1'},
                        loadFlag: false,
                        success : function(rep){
                            if(rep.state=='ok'){
                                ssmReload();
                            }else{
                            }
                            layer.close(index);

                        },
                        complete : function() {
                            setTimeout(function(){
                                layer.closeAll('loading');
                            },300);
                        }
                    });

                });

            }else if(obj.event==='cardTypeManage'){
                var url = "#(ctxPath)/mms/sms/smsCardTypeIndex?smsId="+obj.data.id+"&examineStatus="+obj.data.examineStatus;
                layerShow("发送会员卡类型管理",url,'100%','100%');
            }else if(obj.event==='detail'){
                var url = "#(ctxPath)/mms/sms/detailIndex?smsId="+obj.data.id;
                layerShow("发送详情",url,'100%','100%');
            }
        });


    })
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:sms:cardTypeBtn")
    <a class="layui-btn layui-btn-xs" lay-event="cardTypeManage">会员卡类型</a>
    #end

    #shiroHasPermission("member:sms:editBtn")
    #[[
    {{#if(d.examineStatus=='0'){}}
        <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("member:sms:examineBtn")
    #[[
    {{#if(d.examineStatus=='0'){}}
    <a class="layui-btn layui-btn-xs" lay-event="examine">审核</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("member:sms:smsDetailBtn")
    #[[
    {{#if(d.examineStatus=='1'){}}
        <a class="layui-btn layui-btn-xs" lay-event="detail">发送详情</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("member:sms:delBtn")
    #[[
    {{#if((d.delFlag && d.examineStatus=='1') || d.examineStatus=='0'){}}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#
    #end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <div class="demoTable">
            <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
                短信类型:
                <div class="layui-inline">
                    <select name="smsType" id="smsType" lay-filter="smsType" >
                        <option value="">全部</option>
                        #for(type:typeList)
                        <option value="#(type.id)">#(type.typeName)</option>
                        #end
                    </select>
                </div>
                <!--&nbsp;
                会员卡类型:
                <div class="layui-inline">
                    <input id="cardTypeName" name="cardTypeName" class="layui-input">
                </div>-->
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" type="button" id="search">查询</button>
            </form>
            #shiroHasPermission("member:sms:addBtn")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
            #end
        </div>
    </div>
    <table id="smsTable" lay-filter="smsTable"></table>
</div>
#end