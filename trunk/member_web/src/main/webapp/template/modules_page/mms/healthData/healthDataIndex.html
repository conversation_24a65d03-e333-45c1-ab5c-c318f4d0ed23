#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员健康数据展示#end

#define css()
<link rel="stylesheet" href="#(ctx)/static/css/assess.css" media="all">
<style>
    div.laydate-input-inline-item{
        width: 450px !important;
    }
</style>
#end

#define js()
<script src="#(ctx)/static/plugins/highcharts/highcharts.js"></script>
<script src="#(ctx)/static/plugins/highcharts/measureDataOptions.js"></script>
<script type="text/javascript">
    layui.use(['table', 'element','layer','form','jquery'],function(){
        var table = layui.table, $ = layui.jquery, element = layui.element,layer=layui.layer , highcharts = null;

        loadMemberTable(null);

        // 会员搜索按钮
        $('#searchMember').on('click', function(){
            $(".layui-btn-group button").each(function(){
                if(!$(this).hasClass("layui-btn-primary")){
                    $(this).addClass("layui-btn-primary");
                }
            });
            if($("#tenRecord").hasClass("layui-btn-primary")){
                $("#tenRecord").removeClass("layui-btn-primary");
            }
            var data = {"fullName":$("#fullName").val()}
            loadMemberTable(data);
            return false;
        });

        //加载监测图表AJAX数据
        loadMonitorAjaxData = function(url, memberId, dataRecord) {
            highcharts.showLoading();
            $.ajax ({
                type: 'POST',
                url: url,
                data: {"memberId":memberId,"dataRecord":dataRecord},
                dataType:"json",
                success : function(rep){
                    highcharts.hideLoading();
                    if(rep.flag){
                        if(rep.measureJsonData){
                            if(highcharts.series!=null && highcharts.series.length > 0){
                                highcharts.update({
                                    xAxis: {
                                        categories: rep.measureTimeArray
                                    },
                                    series:rep.measureJsonData
                                });
                            }else{
                                for (var i=0;i<rep.measureJsonData.length;i++) {
                                    highcharts.addSeries(rep.measureJsonData[i]);
                                }
                                highcharts.update({
                                    xAxis: {
                                        categories: rep.measureTimeArray
                                    }
                                });
                            }
                        }else{
                            if(highcharts.series!=null && highcharts.series.length > 0){
                                highcharts.update({
                                    xAxis: {
                                        categories: []
                                    },
                                    series:[{
                                        name:'收缩压',
                                        data:[]
                                    },{
                                        name:'舒张压',
                                        data:[]
                                    }]
                                });
                            }

                        }
                    }else {
                        layer.msg(rep.msg,{icon:5,time:5000});
                    }

                },
                complete : function() {
                }
            });
        };

        //监听监测类型页签切换方法
        element.on('tab(healthTab)', function(data){
            var memberId = $("#memberId").val();
            var url = "";
            var healthType = $(this).attr("lay-id");
            if(healthType=='bp'){
                highcharts = new Highcharts.Chart(bpOptions);
                url = "#(ctx)/mms/healthDataManage/getBloodPressureSeries";
            }else if(healthType=='hr'){
                highcharts = new Highcharts.Chart(heartRateOptions);
                url = "#(ctx)/mms/healthDataManage/getHeartRateSeries";
            }else if(healthType=='steps'){
                highcharts = new Highcharts.Chart(stepCountOptions);
                url = "#(ctx)/mms/healthDataManage/getStepsSeries";
            }
            $(".layui-btn-group button").each(function(){
                var queryType = $(this).attr("id");
                if(queryType=='tenRecord'){
                    if($(this).hasClass("layui-btn-primary")){
                        $(this).removeClass("layui-btn-primary");
                    }
                }else{
                    if(!$(this).hasClass("layui-btn-primary")){
                        $(this).addClass("layui-btn-primary");
                    }
                }
            });
            loadMonitorAjaxData(url,memberId,"tenRecord");
        });


        function recordBtn(recordType){
            var memberId = $("#memberId").val();
            var url = "";
            var type = $("#healthTab .layui-tab-title").children(".layui-this").attr("lay-id");
            if(type == 'bp'){
                url = "#(ctx)/mms/healthDataManage/getBloodPressureSeries";
            }else if(type == 'hr'){
                url = "#(ctx)/mms/healthDataManage/getHeartRateSeries";
            }else if(type == 'steps'){
                url = "#(ctx)/mms/healthDataManage/getStepsSeries";
            }
            $(".layui-btn-group button").each(function(){
                if(!$(this).hasClass("layui-btn-primary")){
                    $(this).addClass("layui-btn-primary");
                }
            });
            if($("#"+recordType).hasClass("layui-btn-primary")){
                $("#"+recordType).removeClass("layui-btn-primary");
            }
            loadMonitorAjaxData(url,memberId,recordType);
        }

        //按钮组点击事件
        $("#tenRecord").click(function(){
            recordBtn('tenRecord');
        });
        $("#7DayRecord").click(function(){
            recordBtn('7DayRecord');
        });
        $("#30DayRecord").click(function(){
            recordBtn('30DayRecord');
        });
        $("#monRecord").click(function(){
            recordBtn('monRecord');
        });




        //加载左侧会员信息
        function loadMemberTable(data) {
            table.render({
                id : 'memberTable' ,
                elem : '#memberTable' ,
                url : '#(ctxPath)/mms/member/findListPage' ,
                method:'post',
                where : data,
                height:510,
                cols : [[
                    {field:'fullName', title: '姓名', align: 'center',minWidth:162, unresize: true}
                    ,{field:'gender', title: '性别', align: 'center', minWidth:70,unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
                    #shiroHasPermission("member:idcardSee")
                    ,{field:'idcard', title: '身份证号', align: 'center',width:180, unresize: true}
                    #end
                ]] ,
                limit : 10,
                page: {
                    layout: ['page','prev', 'next',"skip",'count'],
                    curr: 1,
                    groups: 2,
                },
                done : function (res, curr, count){
                    // 单击行触发回调函数
                    clickRow(this , table , function(data) {
                        var memberId = data.id;
                        $("#memberId").val(memberId);
                        var healthType = $("#healthTab .layui-tab-title").children(".layui-this").attr("lay-id");
                        if(healthType!='bp'){
                            //切换到指定Tab项
                            element.tabChange('healthTab', 'bp'); //切换到：血压
                        }
                        $(".layui-btn-group button").each(function(){
                            if(!$(this).hasClass("layui-btn-primary")){
                                $(this).addClass("layui-btn-primary");
                            }
                        });
                        if($("#tenRecord").hasClass("layui-btn-primary")){
                            $("#tenRecord").removeClass("layui-btn-primary");
                        }
                        highcharts = new Highcharts.Chart(bpOptions);
                        loadMonitorAjaxData('#(ctx)/mms/healthDataManage/getBloodPressureSeries',memberId,'tenRecord');
                    });
                }
            });
        }
    });
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row layui-col-space10">
        <div class="layui-col-md3">
            <div class="layui-row" style="padding-top: 20px;">
                <div class="layui-inline" style="float:left;">
                    <input type="hidden" id="memberId" value=""/>
                    <label class="layui-form-label" style="width:60px;padding-left:0px;">姓名</label>
                    <div class="layui-input-inline">
                        <input type="text" name="fullName" id="fullName" placeholder="请输入会员姓名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                <div class="layui-row" style="float:left;padding-left:5px;">
                    <button class="layui-btn" lay-filter="searchMember" id="searchMember">搜索</button>
                </div>
            </div>
            <table id="memberTable" lay-filter="memberTable"></table>
        </div>
        <div class="layui-col-md9">
            <div class="layui-form">
            <div class="bill-detail-search" style="margin-left: 0px;">
                <div class="layui-form-item d-tab">
                    <div id="healthTab" class="layui-tab layui-tab-card assess" lay-filter="healthTab">
                        <ul class="layui-tab-title">
                            <li lay-id="bp" class="layui-this">血压</li>
                            <li lay-id="hr">心率</li>
                            <li lay-id="steps">步数</li>
                        </ul>
                    </div>
                    <div class="layui-btn-group">
                        <button id="tenRecord" class="layui-btn">最近10条数据</button>
                        <button id="7DayRecord" class="layui-btn layui-btn-primary">最近7天</button>
                        <button id="30DayRecord" class="layui-btn layui-btn-primary">最近30天</button>
                        <button id="monRecord" class="layui-btn layui-btn-primary">本月</button>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div id="chartContainer"></div>
                </div>
            </div>
        </div>
        </div>
</div>
        <!---->
</div>
</div>
#getDictLabel("gender")
#end