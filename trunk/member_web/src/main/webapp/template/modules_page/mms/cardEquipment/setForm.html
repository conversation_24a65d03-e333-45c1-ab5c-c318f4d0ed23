#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡设备设置展示#end

#define css()
<style>
    .layui-form-label{
        width:150px;
    }
    .laydate-time-list{padding-bottom:0;overflow:hidden}
    .laydate-time-list>li{width:50%!important;}
    .laydate-time-list>li:last-child { display: none;}

</style>
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery,laydate = layui.laydate;

        laydate.render({
            elem: '#times'
            ,type: 'time'
            ,min: '01:00:00'
            ,max: '23:59:00'
            ,format:"HH:mm"
            ,trigger: 'click'
            ,range: true
        });


        //下拉框联动
        form.on('select(step)', function(data) {
            changeDiv(data.value);
        });

        function changeDiv(data){
            if(data == '0'){
                $("#times").attr("lay-verify","");
                $("#times").val(null);
                $("#divTime").css("display","none");
            }else{
                $("#times").attr("lay-verify","required");
                $("#divTime").css("display","block");
            }
        }



        //保存
        form.on('submit(saveBtn)', function(){
            if($("#step").val() != null && $("#step").val() == '1') {
                var arr = $("#times").val().split(" - ");
                var startTime = arr[0].substring(0, 2);
                var endTime = arr[1].substring(0, 2);
                if (Number(endTime) < Number(startTime)) {
                    layer.msg('结束时间不能小于开始时间');
                    $('#times').val('');
                    return false;
                }
                if (Number(endTime) == Number(startTime)) {
                    layer.msg('结束时间不能等于开始时间');
                    $('#times').val('');
                    return false;
                }
            }
            var url = "#(ctxPath)/mms/cardEquipment/cardEqSet";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#setForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("cardEquipmentTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form" style="margin-left: 10px;margin-top: 20px;" id="setForm">
    <input type="hidden" name="equipmentNo" value="#(cardEquipment.equipmentNo)"/>
    <input type="hidden" name="equipmentId" value="#(cardEquipment.id)"/>
    <div class="layui-form-item">
        <label class="layui-form-label"><span>*</span>计步是否开启</label>
        <div class="layui-input-inline">
            <select id="step" name="stepSwitch" lay-search lay-verify="required" lay-filter="step">
                <option value="1">开启</option>
                <option value="0">关闭</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item" id="divTime">
        <label class="layui-form-label"><span>*</span>计步时间段</label>
        <div class="layui-input-inline">
            <input type="text" class="layui-input"  name="times" id="times" placeholder="请输入计步时间段">
        </div>
    </div>
    <div class="layui-form-item" style="margin-left: 190px; margin-top: 70px;">
        <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
    </div>
</form>
#getDictLabel("equipment_type")
#end
