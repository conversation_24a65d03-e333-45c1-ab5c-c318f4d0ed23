#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        smsCardTypeTableLoad({"smsId":$("#smsId").val()});

        $("#select").click(function () {
            smsCardTypeTableReLoad();
        });

        smsCardTypeTableReLoad=function(data){

            table.reload('smsCardTypeTable',{'where':{'smsId':$("#smsId").val(),'cardTypeName':$("#cardTypeName").val(),'typeClassify':$("#typeClassify").val()}});
        }


        function smsCardTypeTableLoad(data){
            table.render({
                id : 'smsCardTypeTable'
                ,elem : '#smsCardTypeTable'
                ,method : 'POST'
                ,where : data
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/sms/cardTypeTablePage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'checkbox', fixed: 'left'}
                    ,{field:'cardTypeName', title: '会员卡类型', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        $("#batchAdd").click(function () {
            var checkStatus = table.checkStatus('smsCardTypeTable');
            if(checkStatus.data.length<1){
                layer.msg('请勾选要添加的会员卡类型', {icon: 1, offset: 'auto'});
                return false;
            }
            var typeIds="";
            $.each(checkStatus.data,function (index,item) {

                if(index===checkStatus.data.length-1){
                    typeIds+=item.id;
                }else{
                    typeIds+=item.id+",";
                }
            })
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/sms/smsCardTypeBatchSave',
                notice: true,
                data: {'cardTypeIds':typeIds,'smsId':$("#smsId").val()},
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        smsCardTypeTableReLoad();
                        parent.smsCardTypeTableReLoad();
                    }
                    layer.close();
                },
                complete : function() {
                }
            });
        });
        table.on('tool(smsCardTypeTable)',function(obj) {
            if (obj.event === 'add') {
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mms/sms/smsCardTypeSave',
                    notice: true,
                    data: {'cardTypeId':obj.data.id,'smsId':$("#smsId").val()},
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            smsCardTypeTableReLoad();
                            parent.smsCardTypeTableReLoad();
                        }
                        layer.close();
                    },
                    complete : function() {
                    }
                });
            }
        });


    })
</script>
<script type="text/html" id="actionBar">
    #[[
    <a class="layui-btn layui-btn-xs" lay-event="add">添加</a>
    ]]#
</script>
#end

#define content()
<div class="my-btn-box">
    <form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
    <div class="layui-row">
    	<div class="layui-inline">
            <label class="layui-form-label">类别分类</label>
			<div class="layui-input-inline">
				<select id="typeClassify" name="typeClassify">
					<option value="">全部</option>
					#dictOption("type_classify", type.typeClassify??'', "")
				</select>
			</div>
		</div>
		<div class="layui-inline">
            <label class="layui-form-label" style="width: 104px;">会员卡类型名称</label>
            <div class="layui-input-inline">
                <input class="layui-input" id="cardTypeName" >
            </div>
        </div>
        <div class="layui-inline">
			<input type="hidden" id="smsId" value="#(smsId??)">
            <button class="layui-btn" type="button" id="select">搜索</button>
            <button class="layui-btn" type="button" id="batchAdd">批量添加</button>
        </div>
    </div>
    </form>
    <div class="layui-row">
	    <table id="smsCardTypeTable" lay-filter="smsCardTypeTable"></table>
    </div>
</div>
#end