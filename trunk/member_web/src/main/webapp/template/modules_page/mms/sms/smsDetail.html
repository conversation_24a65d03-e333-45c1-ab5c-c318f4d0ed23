#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人脸模板管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        smsCardTypeTableLoad({"smsId":$("#smsId").val()});


        smsCardTypeTableReLoad=function(){
            smsCardTypeTableLoad({"smsId":$("#smsId").val(),"sendStatus":$("#sendStatus").val()});
        }

        $("#select").click(function () {
            smsCardTypeTableReLoad();
        })

        function smsCardTypeTableLoad(data){
            table.render({
                id : 'smsCardTypeTable'
                ,elem : '#smsCardTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(window).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/sms/detailTablePage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'phoneNum', title: '电话号码', align: 'center', unresize: true}
                    ,{field:'status', title: '发送状态', align: 'center', unresize: true,templet:function (d) {
                        if(d.sendStatus==='0'){
                            return '<span class="layui-badge layui-bg-blue">待发送</span>';
                        }else if(d.sendStatus==='1'){
                            return '<span class="layui-badge">已发送</span>';
                        }else if(d.sendStatus==='2'){
                            return '<span class="layui-badge">发送失败</span>';
                        }
                    }}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };


        table.on('tool(smsCardTypeTable)',function(obj) {
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/sms/detailDel',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                smsCardTypeTableReLoad();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:sms:smsDetailDelBtn")
    #[[
    {{#if(d.sendStatus==='0'){}}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#
    #end
</script>
#end

#define content()
<div>
    <form class="layui-form">
        <div class="layui-row" style="margin-top: 10px;">
            <label class="layui-form-label">发送状态</label>
            <div class="layui-input-inline">
                <select id="sendStatus">
                    <option value="">全部</option>
                    <option value="0">待发送</option>
                    <option value="1">已发送</option>
                    <option value="2">发送失败</option>
                </select>
            </div>
            <button class="layui-btn" type="button" id="select" >查询</button>
        </div>
    </form>
    <input type="hidden" id="smsId" value="#(smsId??)">
    <table id="smsCardTypeTable" lay-filter="smsCardTypeTable"></table>
</div>
#end