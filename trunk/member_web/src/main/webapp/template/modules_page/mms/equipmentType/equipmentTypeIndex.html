#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()测量设备类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        equipmentTypeLoad(null);

        sd=form.on("submit(search)",function(data){
            equipmentTypeLoad(data.field);
            return false;
        });

        function equipmentTypeLoad(data){
            table.render({
                id : 'equipmentTypeTable'
                ,elem : '#equipmentTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/equipmentType/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'equipmentModel', title: '设备型号', align: 'center', unresize: true}
                    ,{field:'equipmentType', title: '设备类型', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.equipmentType,'equipment_type','- -') }}</div>"}
                    ,{field:'equipmentClassify', title: '设备分类', align: 'center', unresize: true,templet:"<div>{{ d.equipmentClassify=='0'?'个人':'多人' }}</div>"}
                    ,{field:'manufactor', title: '厂家', align: 'center', unresize: true}
                    ,{field:'equipmentColor', title: '设备颜色', align: 'center', unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/equipmentType/form" ;
            pop_show("新增测量设备类型",url,700,500);
        });

        table.on('tool(equipmentTypeTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/equipmentType/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('equipmentTypeTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/equipmentType/form?id=" + obj.data.id ;
                pop_show("编辑测量设备类型",url,700,500);
            }
        });

        <!--region Description-->
        /*//批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('gatewayTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/gatewayTypeTable/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {bedTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("gatewayTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });*/
        <!--endregion-->
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:equipmentType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("member:equipmentType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            设备型号:
            <div class="layui-inline">
                <input id="equipmentModel" name="equipmentModel" class="layui-input">
            </div>
            &nbsp;&nbsp;
            设备类型:
            <div class="layui-inline">
                <select name="equipmentType" lay-search>
                    <option value="">请选择设备类型</option>
                    #getDictList("equipment_type")
                    <option value="#(key)">#(value)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:equipmentType:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table id="equipmentTypeTable" lay-filter="equipmentTypeTable"></table>
</div>
#getDictLabel("equipment_type")
#end