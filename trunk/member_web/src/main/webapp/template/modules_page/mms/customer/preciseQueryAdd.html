#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()精确查询客户添加页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
	.layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
	img{
		width:100px;
		max-height:110px;
	}
</style>
#end

#define js()
<script id="regidentAreaTpl" type="text/html">
	<div id="regidentArea" style="width:600px;height:300px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
		<div class="tabs clearfix">
			<ul>
				<li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
				<li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
				<li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
				<li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
			</ul>
		</div>
		<div class="con">
			<div class="regidentProvinceAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentCityAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentTownAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentStreetAll">
				<div class="list">

				</div>
			</div>
		</div>
	</div>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//--------------------------居住区域begin---------------------------
	$('#regidentAddress').on('click', function() {
		//closeIdArea();

		$('#regidentArea').remove();
		var $this = $(this);
		var getTpl = regidentAreaTpl.innerHTML;
		$this.parent().append(getTpl);
		//event.stopPropagation();

		var street=$("#street").val();
		var regidentStreetName=$("#regidentStreetName").val();
		var town=$("#town").val();
		var regidentCountyName=$("#regidentCountyName").val();
		var city=$("#city").val();
		var regidentCityName=$("#regidentCityName").val();
		var province=$("#province").val();
		var regidentProvinceName=$("#regidentProvinceName").val();
		if(street!='' && regidentStreetName!=''){
			$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentStreetLoad(town);
			regidentCountyLoad(city);
			regidentCityLoad(province);
			regidentProvinceLoad();
			$(".con .regidentStreetAll").show().siblings().hide();
			//clickStreet(streetId,streetName);
		}else if(town!='' && regidentCountyName!=''){

			if(town!=''){
				regidentCityLoad(province);
				regidentCountyLoad(city);
				regidentProvinceLoad();
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/area/getAreas',
					data: {pid:town},
					notice:false,
					loadFlag: false,
					success : function(res){
						if(res.state=='ok'){
							if(res.data.length>0){
								$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								var html="<ul>";
								$.each(res.data, function(i, item){
									html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
								});
								html+="</ul>";
								$(".regidentStreetAll .list").append(html);
								//viewStreet(countyId,countyName);
								$(".con .regidentStreetAll").show().siblings().hide();
							}else{
								//无 街道信息
								$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								$(".con .regidentTownAll").show().siblings().hide();
							}
						}
					},
					complete : function() {
					}
				});
			}
		}else if(city!='' && regidentCityName!=''){
			regidentProvinceLoad();
			regidentCityLoad(province);
			viewRegidentCounty(city,regidentCityName);
		}else if(province!='' && regidentProvinceName!=''){
			regidentProvinceLoad();
			viewRegidentCity(province,regidentProvinceName);
		}else{
			regidentProvinceLoad();
		}





		//去除事件冒泡
		var evt =  new Object;
		if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
			evt = arguments.callee.caller.arguments[0];
		}else{
			evt = event || window.event;
		}
		evt.cancelBubble = true;
		$('#regidentArea').off('click');
		$('#regidentArea').on('click', function() {
			//event.stopPropagation();
			//去除事件冒泡
			var evt =  new Object;
			if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
				evt = arguments.callee.caller.arguments[0];
			}else{
				evt = event || window.event;
			}
			evt.cancelBubble = true;
		})
	});

	regidentProvinceLoad=function(){
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/area/getAreas',
			data: {pid:''},
			notice:false,
			loadFlag: false,
			success : function(res){
				if(res.state=='ok'){
					if(res.data.length>0){
						$(".regidentProvinceAll .list").empty();
						var html="<ul>";
						$.each(res.data, function(i, item){
							html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCity(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
						});
						html+="</ul>";
						$(".regidentProvinceAll .list").append(html);
					}
				}
			},
			complete : function() {
			}
		});
	};

	//点击省事件
	viewRegidentCity=function(province,regidentProvinceName) {
		$("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentCityAll").show().siblings().hide();
		$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		//点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
		$("#province").val(province);
		$("#regidentProvinceName").val(regidentProvinceName);
		$("#city").val("");
		$("#regidentCityName").val("");
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCityLoad(province);
	};

	//加载市
	regidentCityLoad=function(province){
		if(province!=''){
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/area/getAreas',
			data: {pid:province},
			notice:false,
			loadFlag: false,
			success : function(res){
				if(res.state=='ok'){
					if(res.data.length>0){
						$(".regidentCityAll .list").empty();
						var html="<ul>";
						$.each(res.data, function(i, item){
							html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCounty(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
						});
						html+="</ul>";
						$(".regidentCityAll .list").append(html);
					}
				}
			},
			complete : function() {
			}
		});
		}
	};

	//点击市事件
	viewRegidentCounty=function(city,RegidentCityName){
		$("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentTownAll").show().siblings().hide();
		$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#city").val(city);
		$("#regidentCityName").val(RegidentCityName);
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCountyLoad(city);
	};

	//加载区/县
	regidentCountyLoad=function(city){
		if(city!=''){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid:city},
				notice:false,
				loadFlag: false,
				success : function(res){
					if(res.state=='ok'){
						if(res.data.length>0){
							$(".regidentTownAll .list").empty();
							var html="<ul>";
							$.each(res.data, function(i, item){
								html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
							});
							html+="</ul>";
							$(".regidentTownAll .list").append(html);
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	//点击区/县事件
	viewRegidentStreet=function(town,regidentCountyName){
		$("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentStreetAll").show().siblings().hide();
		$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#town").val(town);
		$("#regidentCountyName").val(regidentCountyName);
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentStreetLoad(town);
	};

	//加载街道/镇/乡
	regidentStreetLoad=function(town){
		if(town!=''){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid:town},
				notice:false,
				loadFlag: false,
				success : function(res){
					if(res.state=='ok'){
						if(res.data.length>0){
							$(".regidentStreetAll .list").empty();
							var html="<ul>";
							$.each(res.data, function(i, item){
								html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
							});
							html+="</ul>";
							$(".regidentStreetAll .list").append(html);
						}else{
							//无 街道信息
							clickRegidentStreet('','');
						}
					}
				},
				complete : function() {
				}
			});
		}
	};

	clickRegidentStreet=function(street,regidentStreetName){
		$("#street").val(street);
		$("#regidentStreetName").val(regidentStreetName);
		var regidentProvinceName=$("#regidentProvinceName").val();
		var regidentCityName=$("#regidentCityName").val();
		var regidentCountyName=$("#regidentCountyName").val();
		var regidentStreetName=$("#regidentStreetName").val();
		var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
		$("#regidentAddress").val(add);
		$('#regidentArea').remove();
	};

	regidentProvinceAllClick=function(){
		//$(".con .provinceAll").show();
		$("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentProvinceAll").show().siblings().hide();
	};

	regidentCityAllClick=function(){
		// $(".con .cityAll").show();
		if($("#province").val()!=''){
			$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCityLoad($("#province").val());
			$(".con .regidentCityAll").show().siblings().hide();
		}
	};

	regidentTownAllClick=function(){
		if($("#city").val()!=''){
			$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCountyLoad($("#city").val());
			$(".con .regidentTownAll").show().siblings().hide();
		}
	};

	regidentStreetAllClick=function(){
		if($("#town").val()!=''){
			util.sendAjax ({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid:$("#town").val()},
				notice:false,
				loadFlag: false,
				success : function(res){
					if(res.state=='ok'){
						if(res.data.length>0){
							$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							regidentStreetLoad($("#town").val());
							$(".con .regidentStreetAll").show().siblings().hide();
						}else{
							//无 街道信息 显示区/县信息
							$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							//countyLoad(cityId);
							$(".con .regidentTownAll").show().siblings().hide();
						}
					}
				},
				complete : function() {
				}
			});



		}
	};

	//关闭区域选择器
	closeRegidentArea=function(){
		if(typeof($('#regidentArea').html())!='undefined'){
			var regidentProvinceName=$("#regidentProvinceName").val();
			var regidentCityName=$("#regidentCityName").val();
			var regidentCountyName=$("#regidentCountyName").val();
			var regidentStreetName=$("#regidentStreetName").val();
			var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
			$("#regidentAddress").val(add);
		}
		//alert(1);
		$('#regidentArea').remove();
		//console.log($('#regidentArea').html());
	}

	$('body').on('click', function() {
		closeRegidentArea();
	});

	//-------------------------居住区域end----------------------------
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/customer/preciseQueryInfoSave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("customerInfoTable",{});
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>客户姓名</label>
		<div class="layui-input-block">
			<input type="text" name="info.memberName" class="layui-input" lay-verify="required" value="" placeholder="请输入客户姓名" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">身份证号</label>
		<div class="layui-input-block">
			<input type="text" name="info.idcard" value="" class="layui-input" placeholder="请输入身份证号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">电话号码</label>
		<div class="layui-input-block">
			<input type="text" name="info.phoneNumber" class="layui-input" value="" placeholder="请输入电话号码" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">居住区域</label>
		<div class="layui-input-block">
			<input type="hidden" id="province" name="info.province" value="" />
			<input type="hidden" id="city" name="info.city" value="" />
			<input type="hidden" id="town" name="info.town" value="">
			<input type="hidden" id="street" name="info.street" value="">
			<input type="hidden" id="regidentProvinceName" value="" />
			<input type="hidden" id="regidentCityName" value="" />
			<input type="hidden" id="regidentCountyName" value="">
			<input type="hidden" id="regidentStreetName" value="">
			<input type="text" id="regidentAddress" readonly="readonly"  name="regidentAddrs" class="layui-input" value="">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">详细住址</label>
		<div class="layui-input-block">
			<input type="text" id="address" name="info.address" value="" autocomplete="off" placeholder="请输入详细住址" class="layui-input">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>诉求类型</label>
		<div class="layui-input-block">
			<select name="visit.appealType" lay-verify="required">
				<option value="">请选择</option>
				#getDictList("appealType")
					<option value="#(key)">#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">来电事由</label>
		<div class="layui-input-block">
			<textarea name="visit.remarks" class="layui-textarea" maxlength="1000" placeholder="请输入来电事由"></textarea>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>跟进结果</label>
		<div class="layui-input-block">
			<select name="visit.followResult" lay-verify="required">
				<option value="">请选择</option>
				#getDictList("followResult")
					<option value="#(key)">#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">客户满意度</label>
		<div class="layui-input-block">
			<select name="visit.handleResult">
				<option value="">请选择处理结果</option>
				#getDictList("handleResult")
					<option value="#(key)">#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end