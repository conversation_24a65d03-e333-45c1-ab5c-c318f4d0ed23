#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
    .layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
    img{
        width:100px;
        max-height:110px;
    }
</style>
#end

#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;

        var now=new Date();
        laydate.render({
            elem: '#sendTime'
            ,type: 'datetime'
            ,min:now.getFullYear()+'-'+(now.getMonth()+1)+'-'+now.getDate()+' '+now.getHours()+':'+now.getMinutes()+':00'
            ,tigger:'click'
        });

        //保存
        form.on('submit(confirmBtn)', function(){
            var url = "#(ctxPath)/mms/sms/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#typeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.ssmReload();
                    }
                },
                complete : function() {
                }
            });
            return false;
        });

    });
</script>

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="typeForm">
            <div class="layui-form-item">
                <label class="layui-form-label">短信类型</label>
                <div class="layui-input-block">
                    <select name="typeId" lay-verify="required" >
                        <option value="">请选择短信类型</option>
                        #for(type:typeList)
                        <option value="#(type.id??)" #if(type.id==model.typeId??) selected #end>#(type.typeName??)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">短信名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name"  lay-verify="required" placeholder="请输入短信名称" autocomplete="off" value="#(model.name??)" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label" style="padding: 8px 10px;">短信发送时间</label>
                <div class="layui-input-block">
                    <input type="text" name="sendTime" id="sendTime"  lay-verify="required" placeholder="请输入短信发送时间" autocomplete="off" value="#date(model.sendTime??,'yyyy-MM-dd HH:mm:ss')" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">短信内容</label>
                <div class="layui-input-block">
                    <textarea name="messageContent" placeholder="请输入内容" maxlength="255" class="layui-textarea">#(model.messageContent??)</textarea>
                </div>
            </div>

            <div class="layui-form-item layui-form-text">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="remark" placeholder="请输入内容" maxlength="255" class="layui-textarea">#(model.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input name="id" id="id" type="hidden" value="#(model.id??)" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end