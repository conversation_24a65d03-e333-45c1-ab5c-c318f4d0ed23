#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()新增活动二维码#end

#define css()
#end

#define js()
<script type="text/javascript">
    layui.use(['form'],function(){
        var form = layui.form;
        var $ = layui.$;

        $(function(){
            if($("#detailImg").val() != null && $("#detailImg").val() != ''){
                $("#profilePhotoImg").attr("src",$("#detailImg").val());
            }
        });

        getNameAndUserId=function(userId,name){
            $("#name").val(name);
            $("#userId").val(userId);
        }

        // 获取分公司营销人信息
        $("#chooseUserMarket").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/activityQrcode/chooseUserMarket" ;
            pop_show("选择分公司营销人信息",url,850,470);
        });

        //生成二维码
        $("#createQrcode").click(function(){
            if($("#name").val() != null && $("#name").val() != '') {
                util.sendAjax({
                    type: 'POST',
                    url: '#(ctxPath)/mms/activityQrcode/createQrcode',
                    notice: false,
                    loadFlag: false,
                    success: function (rep) {
                        if (rep.state == 'ok') {
                            $("#imgName").val(rep.imgName);
                            $("#uuidStr").val(rep.uuidStr);
                            $("#profilePhotoImg").attr("src",rep.imgName)
                        }
                    },
                    complete: function () {
                    }
                });
                return false;
            }else{
                layer.msg('请选择营销人', function () {});
            }
        });


        //保存
        form.on('submit(confirmBtn)', function(){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/activityQrcode/save',
                data: $("#qrcodeForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.layui.table.reload('qrcodeTable');
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="qrcodeForm">
            <div class="layui-row">
                <div class="layui-col-xs8" style="padding-right: 20px;">
                <div class="layui-form-item">
                    <label class="layui-form-label"><span>*</span>营销人</label>
                    <div class="layui-input-inline">
                        <input type="text" id="name" name="name" autocomplete="off" class="layui-input" value="#(user != null ? user.name:'')" lay-verify="required" readonly>
                    </div>
                    <div #if(qrcode.id??!=null) style="display: none" #end>
                        <input type="button" class="layui-btn" id="chooseUserMarket" value="选择营销人">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-inline" style="width:305px;">
                        <textarea id="textA" placeholder="请输入备注" name="qrcode.remark" rows="5" class="layui-textarea" #if(qrcode.id??!=null) readonly #end>#(qrcode != null ? qrcode.remark:'')</textarea>
                    </div>
                </div>
                </div>
                <div class="layui-col-xs4">
                <div class="layui-form-item">
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" id="profilePhotoImg" width="200px">
                    </div>
                </div>
            </div>
            </div>

            <div class="layui-form-footer" #if(qrcode.id??!=null) style="display: none" #end>
                <div class="pull-right">
                    <input type="hidden" id="userId" name="qrcode.userId"/>
                    <input type="hidden" id="id" name="qrcode.id" value="#(qrcode.id??)"/>
                    <input type="hidden" id="imgName" name="imgName"/>
                    <input type="hidden" id="uuidStr" name="qrcode.qrcode"/>
                    <input type="hidden" id="detailImg" value="#(imgName??)">
                    <button type="button" class="layui-btn" id="createQrcode">生成二维码</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end