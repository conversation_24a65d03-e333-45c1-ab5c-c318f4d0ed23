#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
    .layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
    img{
        width:100px;
        max-height:110px;
    }
</style>
#end

#define js()
<script type="text/javascript">
    layui.use(['form', 'laydate', 'upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var laydate = layui.laydate;
        var upload = layui.upload;

        //时间渲染
        laydate.render({
            elem : '#birthday'
        });

        //校验
        form.verify({
            checkPhone:function(value){
                if(value != null && value.length >0){
                    var reg = new RegExp("^(13[0-9]|14[0-9]|15[0-9]|18[0-9]|17[0-9])\\d{8}$");
                    if(!reg.test(value)){
                        return "手机号码格式不正确";
                    }
                }
            }
        });

        //身份证联动出生日期
        function idCardBirthday(psidno){
            var birthdayno,birthdaytemp;
            if (psidno.length == 18) {
                birthdayno=psidno.substring(6,14)
            } else if (psidno.length == 15) {
                birthdaytemp = psidno.substring(6,12);
                birthdayno = "19" + birthdaytemp;
            }else{
                return false
            }
            var birthday = birthdayno.substring(0,4)+"-"+birthdayno.substring(4,6)+"-"+birthdayno.substring(6,8)
            return birthday
        }


        $('#memberIdcard').on('keyup', function() {
            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                $('#birthday').val(birthday);
            }
        });
        $('#memberIdcard').on('blur', function() {
            var birthday = idCardBirthday($(this).val());
            if (birthday) {
                $('#birthday').val(birthday);
            }
        });

        //保存
        form.on('submit(confirmBtn)', function(){
            var url = "#(ctxPath)/mms/memberface/saveMember";
            $("#confirmBtn").attr("disabled",true);
            $("#confirmBtn").addClass("layui-btn-disabled");
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#memberInfoForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("faceTable",null);
                    }
                },
                complete : function() {
                    $("#confirmBtn").attr("disabled",false);
                    $("#confirmBtn").removeClass("layui-btn-disabled");
                }
            });
            return false;
        });

        //普通图片上传
        var uploadInst = upload.render({
            elem: '#profilePhoto'
            ,url:$("#commonUpload").val() + '/upload?bucket=profilePhoto'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#profilePhotoImg').attr('src', result);
                });
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){
                    $("#uploadPath").val(res.data.src);
                    $("#fileId").val(res.data.id);
                    layer.msg(res.msg,{icon:1,time:5000});

                }else {
                    return layer.msg('上传失败');
                }

            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });


        //--------------------------居住区域begin---------------------------
        $('#regidentAddress').on('click', function() {
            //closeIdArea();

            $('#regidentArea').remove();
            var $this = $(this);
            var getTpl = regidentAreaTpl.innerHTML;
            $this.parent().append(getTpl);
            //event.stopPropagation();

            var street=$("#street").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var town=$("#town").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var city=$("#city").val();
            var regidentCityName=$("#regidentCityName").val();
            var province=$("#province").val();
            var regidentProvinceName=$("#regidentProvinceName").val();
            if(street!='' && regidentStreetName!=''){
                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentStreetLoad(town);
                regidentCountyLoad(city);
                regidentCityLoad(province);
                regidentProvinceLoad();
                $(".con .regidentStreetAll").show().siblings().hide();
                //clickStreet(streetId,streetName);
            }else if(town!='' && regidentCountyName!=''){

                if(town!=''){
                    regidentCityLoad(province);
                    regidentCountyLoad(city);
                    regidentProvinceLoad();
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/area/getAreas',
                        data: {pid:town},
                        notice:false,
                        loadFlag: false,
                        success : function(res){
                            if(res.state=='ok'){
                                if(res.data.length>0){
                                    $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    var html="<ul>";
                                    $.each(res.data, function(i, item){
                                        html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                    });
                                    html+="</ul>";
                                    $(".regidentStreetAll .list").append(html);
                                    //viewStreet(countyId,countyName);
                                    $(".con .regidentStreetAll").show().siblings().hide();
                                }else{
                                    //无 街道信息
                                    $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                    $(".con .regidentTownAll").show().siblings().hide();
                                }
                            }
                        },
                        complete : function() {
                        }
                    });
                }
            }else if(city!='' && regidentCityName!=''){
                regidentProvinceLoad();
                regidentCityLoad(province);
                viewRegidentCounty(city,regidentCityName);
            }else if(province!='' && regidentProvinceName!=''){
                regidentProvinceLoad();
                viewRegidentCity(province,regidentProvinceName);
            }else{
                regidentProvinceLoad();
            }





            //去除事件冒泡
            var evt =  new Object;
            if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                evt = arguments.callee.caller.arguments[0];
            }else{
                evt = event || window.event;
            }
            evt.cancelBubble = true;
            $('#regidentArea').off('click');
            $('#regidentArea').on('click', function() {
                //event.stopPropagation();
                //去除事件冒泡
                var evt =  new Object;
                if ( typeof(window.event) == "undefined" ){//如果是火狐浏览器
                    evt = arguments.callee.caller.arguments[0];
                }else{
                    evt = event || window.event;
                }
                evt.cancelBubble = true;
            })
        });

        regidentProvinceLoad=function(){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/area/getAreas',
                data: {pid:''},
                notice:false,
                loadFlag: false,
                success : function(res){
                    if(res.state=='ok'){
                        if(res.data.length>0){
                            $(".regidentProvinceAll .list").empty();
                            var html="<ul>";
                            $.each(res.data, function(i, item){
                                html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCity(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                            });
                            html+="</ul>";
                            $(".regidentProvinceAll .list").append(html);
                        }
                    }
                },
                complete : function() {
                }
            });
        };

        //点击省事件
        viewRegidentCity=function(province,regidentProvinceName) {
            $("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentCityAll").show().siblings().hide();
            $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            //点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
            $("#province").val(province);
            $("#regidentProvinceName").val(regidentProvinceName);
            $("#city").val("");
            $("#regidentCityName").val("");
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCityLoad(province);
        };

        //加载市
        regidentCityLoad=function(province){
            if(province!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:province},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentCityAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentCounty(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentCityAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击市事件
        viewRegidentCounty=function(city,RegidentCityName){
            $("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentTownAll").show().siblings().hide();
            $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#city").val(city);
            $("#regidentCityName").val(RegidentCityName);
            $("#town").val("");
            $("#regidentCountyName").val("");
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentCountyLoad(city);
        };

        //加载区/县
        regidentCountyLoad=function(city){
            if(city!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:city},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentTownAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="viewRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentTownAll .list").append(html);
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        //点击区/县事件
        viewRegidentStreet=function(town,regidentCountyName){
            $("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentStreetAll").show().siblings().hide();
            $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $("#town").val(town);
            $("#regidentCountyName").val(regidentCountyName);
            $("#street").val("");
            $("#regidentStreetName").val("");
            regidentStreetLoad(town);
        };

        //加载街道/镇/乡
        regidentStreetLoad=function(town){
            if(town!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:town},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $(".regidentStreetAll .list").empty();
                                var html="<ul>";
                                $.each(res.data, function(i, item){
                                    html+='<li><a href="javascript:void(0)" id="'+item.id+'" onclick="clickRegidentStreet(\''+item.id+'\',\''+item.areaName+'\')">'+item.areaName+'</a></li>';
                                });
                                html+="</ul>";
                                $(".regidentStreetAll .list").append(html);
                            }else{
                                //无 街道信息
                                clickRegidentStreet('','');
                            }
                        }
                    },
                    complete : function() {
                    }
                });
            }
        };

        clickRegidentStreet=function(street,regidentStreetName){
            $("#street").val(street);
            $("#regidentStreetName").val(regidentStreetName);
            var regidentProvinceName=$("#regidentProvinceName").val();
            var regidentCityName=$("#regidentCityName").val();
            var regidentCountyName=$("#regidentCountyName").val();
            var regidentStreetName=$("#regidentStreetName").val();
            var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
            $("#regidentAddress").val(add);
            $('#regidentArea').remove();
        };

        regidentProvinceAllClick=function(){
            //$(".con .provinceAll").show();
            $("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
            $(".con .regidentProvinceAll").show().siblings().hide();
        };

        regidentCityAllClick=function(){
            // $(".con .cityAll").show();
            if($("#province").val()!=''){
                $("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCityLoad($("#province").val());
                $(".con .regidentCityAll").show().siblings().hide();
            }
        };

        regidentTownAllClick=function(){
            if($("#city").val()!=''){
                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                regidentCountyLoad($("#city").val());
                $(".con .regidentTownAll").show().siblings().hide();
            }
        };

        regidentStreetAllClick=function(){
            if($("#town").val()!=''){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/area/getAreas',
                    data: {pid:$("#town").val()},
                    notice:false,
                    loadFlag: false,
                    success : function(res){
                        if(res.state=='ok'){
                            if(res.data.length>0){
                                $("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                regidentStreetLoad($("#town").val());
                                $(".con .regidentStreetAll").show().siblings().hide();
                            }else{
                                //无 街道信息 显示区/县信息
                                $("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
                                //countyLoad(cityId);
                                $(".con .regidentTownAll").show().siblings().hide();
                            }
                        }
                    },
                    complete : function() {
                    }
                });



            }
        };


        $('body').on('click', function() {
            closeRegidentArea();
        });

        //关闭区域选择器
        closeRegidentArea=function(){
            if(typeof($('#regidentArea').html())!='undefined'){
                var regidentProvinceName=$("#regidentProvinceName").val();
                var regidentCityName=$("#regidentCityName").val();
                var regidentCountyName=$("#regidentCountyName").val();
                var regidentStreetName=$("#regidentStreetName").val();
                var add=regidentProvinceName+" "+regidentCityName+" "+regidentCountyName+" "+regidentStreetName;
                $("#regidentAddress").val(add);
            }
            //alert(1);
            $('#regidentArea').remove();
            //console.log($('#regidentArea').html());
        }

        //-------------------------居住区域end----------------------------

    });
</script>
<script id="regidentAreaTpl" type="text/html">
    <div id="regidentArea" style="width:600px;height:300px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
        <div class="tabs clearfix">
            <ul>
                <li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()" class="current">省份</a></li>
                <li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()" >城市</a></li>
                <li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
                <li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()" >街道/镇/乡</a></li>
            </ul>
        </div>
        <div class="con">
            <div class="regidentProvinceAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentCityAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentTownAll">
                <div class="list">

                </div>
            </div>
            <div class="regidentStreetAll">
                <div class="list">

                </div>
            </div>
        </div>
    </div>
</script>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="memberInfoForm">
            <div class="layui-row">
                <table class="layui-table" lay-skin="nob">
                    <colgroup>
                        <col width="40%">
                        <col width="40%">
                        <col width="20%">
                    </colgroup>
                    <tbody>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label"><span>*</span>姓氏</label>
                            <div class="layui-input-inline" style="width:22%;">
                                <input type="text" name="member.surname" value="#(member != null ?member.surname:'')" autocomplete="off" placeholder="请输入姓氏" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label"><span>*</span>姓名</label>
                            <div class="layui-input-block">
                                <input type="text" id="memberName" name="member.fullName" value="#(member != null ?member.fullName:'')" autocomplete="off" placeholder="请输入姓名" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><span>*</span>性别</label>
                            <div class="layui-input-block">
                                <select id="memberGender" name="member.gender" lay-verify="required">
                                    <option value="">请选择性别</option>
                                    #getDictList("gender")
                                    <option value="#(key)" #(member != null ?(key == member.gender ? 'selected':''):'')>#(value)</option>
                                    #end
                                </select>
                            </div>
                        </td>
                        <td rowspan="3" text-align="center" valign="bottom">
                            <div class="layui-upload" align="center">
                                <div class="layui-upload-list">
                                    <img class="layui-upload-img" id="profilePhotoImg" src="#(member ? (member.headPic ? member.headPic :'') : '')">
                                    <p id="profilePhotoText"></p>
                                    <input type="hidden" id="uploadPath" name="member.headPic" value="#(member.headPic ??)">
                                </div>
                            </div>
                            <button type="button" class="layui-btn" id="profilePhoto">上传头像</button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label"><span>*</span>出生日期</label>
                            <div class="layui-input-block">
                                <input type="text" id="birthday" name="member.birthday" value="#(member != null ? member.birthday:'')"  placeholder="请选择出生日期" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label"><span>*</span>身份证号</label>
                            <div class="layui-input-block">
                                <input type="text" id="memberIdcard" name="member.idcard" value="#(member != null ?member.idcard :'')" autocomplete="off" placeholder="请输入身份证号" class="layui-input" lay-verify="required">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="layui-form-label">职业</label>
                            <div class="layui-input-block">
                                <input type="text" name="member.occupation" value="#(member != null ?member.occupation :'')" autocomplete="off" placeholder="请输入职业" class="layui-input" >
                            </div>
                        </td>
                        <td>
                            <label class="layui-form-label">手机号码</label>
                            <div class="layui-input-block">
                                <input type="text" name="member.telephone" value="#(member != null ?member.telephone :'')" autocomplete="off" placeholder="请输入手机号码" class="layui-input" lay-verify="checkPhone">
                            </div>
                        </td>

                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label">居住区域</label>
                            <div class="layui-input-block">
                                <input type="hidden" id="province" name="member.province" value="#(member.province??)" />
                                <input type="hidden" id="city" name="member.city" value="#(member.city??)" />
                                <input type="hidden" id="town" name="member.town" value="#(member.town??)">
                                <input type="hidden" id="street" name="member.street" value="#(member.street??)">
                                <input type="hidden" id="regidentProvinceName" value="#(province??)" />
                                <input type="hidden" id="regidentCityName" value="#(city??)" />
                                <input type="hidden" id="regidentCountyName" value="#(town??)">
                                <input type="hidden" id="regidentStreetName" value="#(street??)">
                                <input type="text" id="regidentAddress" readonly="readonly"  name="regidentAddrs" class="layui-input" value="#(province??) #(city??) #(town??) #(street??)">
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3">
                            <label class="layui-form-label">详细住址</label>
                            <div class="layui-input-block">
                                <input type="text" name="member.address" value="#(member != null ?member.address:'')" autocomplete="off" placeholder="请输入具体住址" class="layui-input">
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" id="commonUpload" value="#(commonUpload)"/>
                    <input type="hidden" id="fileId" name="member.fileId" value="#(member.fileId??)"/>
                    <input name="member.id" id="memberId" type="hidden" value="#(member != null ? member.id : '')" />
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end