#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()测量设备类型信息展示#end

#define css()

#end

#define content()
<div style="margin: 0px;">
    <div class="demoTable">
        <form class="layui-form layui-form-pane" action="" method="post" id="optionForm" style="margin-left:10px;margin-right:10px;">
            <div class="layui-form-item" style="padding-top: 10px;">
                <label class="layui-form-label"><span style="color: red;">*</span>选项名称</label>
                <div class="layui-input-block">
                    <input type="text" name="name" class="layui-input" value="#(option.name??)" lay-verify="required" placeholder="请输入名称">
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label"><span style="color: red;">*</span>排序</label>
                <div class="layui-input-block">
                    <input type="text" name="sort" class="layui-input" value="#(option.sort??)" lay-verify="required|number" placeholder="请输入排序">
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="id" value="#(option.id??)">
                    <input type="hidden" id="questionId" name="questionId" value="#(questionId??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/questionnaire/saveOption";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#optionForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("optionTable",{'questionId':'#(questionId??)'});
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
