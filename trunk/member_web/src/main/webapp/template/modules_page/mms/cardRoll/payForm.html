#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券收款登记编辑页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>卡券编号</label>
		<div class="layui-input-block">
			<input type="text" id="rollNumber" name="rollNumber" class="layui-input" lay-verify="required" value="#(model.rollNumber??)" placeholder="请输入卡券编号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>收款人</label>
		<div class="layui-input-block">
			<input type="text" id="collectName" name="collectName" class="layui-input" lay-verify="required" value="#(model.collectName??)" placeholder="请输入收款人" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>收款时间</label>
		<div class="layui-input-block">
			#if(model.collectTime??'' == '')
				<input type="text" id="collectTime" name="collectTime" class="layui-input" lay-verify="required" value="#(model.collectTime??'')" placeholder="请选择收款时间" autocomplete="off">
			#else
				<input type="text" id="collectTime" name="collectTime" class="layui-input" lay-verify="required" value="#date(model.collectTime??'', 'yyyy-MM-dd HH:mm:ss')" placeholder="请选择收款时间" autocomplete="off">
			#end
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>收款金额</label>
		<div class="layui-input-block">
			<input type="text" id="collectMoney" name="collectMoney" class="layui-input" lay-verify="required" value="#(model.collectMoney??)" placeholder="请输入收款金额" autocomplete="off">
		</div>
	</div>
<!-- 	<div class="layui-form-item"> -->
<!-- 		<label class="layui-form-label"><font color="red">*</font>收款渠道</label> -->
<!-- 		<div class="layui-input-block"> -->
<!-- 			<input type="text" id="collectChannel" name="collectChannel" class="layui-input" lay-verify="required" value="#(model.collectChannel??)" placeholder="请输入收款渠道" autocomplete="off"> -->
<!-- 		</div> -->
<!-- 	</div> -->
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>付款方式</label>
		<div class="layui-input-block">
			<select id="payWay" name="payWay">
				#for(payWay : payWay)
				<option value="#(payWay.key??)"  #if(payWay.key==model.payWay??) selected #end>#(payWay.value??)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>付款人姓名</label>
		<div class="layui-input-block">
			<input type="text" id="payName" name="payName" class="layui-input" lay-verify="required" value="#(model.payName??)" placeholder="请输入付款人姓名" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">付款人手机</label>
		<div class="layui-input-block">
			<input type="text" id="payPhone" name="payPhone" class="layui-input" value="#(model.payPhone??)" placeholder="请输入付款人手机" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">付款人银行</label>
		<div class="layui-input-block">
			<input type="text" id="payBank" name="payBank" class="layui-input" value="#(model.payBank??)" placeholder="请输入付款人银行" autocomplete="off">
		</div>
	</div>
    <div class="layui-form-item">
		<label class="layui-form-label">付款账号</label>
		<div class="layui-input-block">
			<input type="text" id="payAccount" name="payAccount" class="layui-input" value="#(model.payAccount??)" placeholder="请输入付款账号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
        <label class="layui-form-label">付款状态</label>
        <div class="layui-input-block">
            <input type="radio" name="payStatus" value="0" title="未付款" #if(model!=null && model.payStatus=='0') checked #elseif(model==null) checked #end>
            <input type="radio" name="payStatus" value="1" title="已付款" #if(model.payStatus??=='1') checked #end>
        </div>
    </div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//时间渲染
	laydate.render({
		elem: '#collectTime'
		, format: 'yyyy-MM-dd HH:mm:ss'
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/cardRoll/paySave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("payTable",null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end