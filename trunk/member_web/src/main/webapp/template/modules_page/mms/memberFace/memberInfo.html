#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案选择#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        memberLoad(null);

        sd=form.on("submit(search)",function(data){
            memberLoad(data.field);
            return false;
        });

        function memberLoad(data){
            table.render({
                id : 'memberTable'
                ,elem : '#memberTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/member/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'fullName', title: '姓名', align: 'center', unresize: true}
                    #shiroHasPermission("member:idcardSee")
                    ,{field:'idcard', title: '身份证号', align: 'center', unresize: true}
                    #end
                    ,{field:'birthday', title: '出生日期', align: 'center', unresize: true}
                    #shiroHasPermission("member:telephoneSee")
                    ,{field:'telephone', title: '电话', align: 'center', unresize: true}
                    #end
                    ,{field:'address', title: '地址', align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 90, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        // 选择
        table.on('tool(memberTable)',function(obj){
            if(obj.event === 'choose'){
                parent.getNameAndIdcard(obj.data.id,obj.data.fullName,obj.data.idcard);
                pop_close();
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="choose">选择</a>
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            姓名:
            <div class="layui-inline">
                <input id="fullName" name="fullName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            身份证:
            <div class="layui-inline">
                <input id="idcard" name="idcard" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
    </div>
    <table id="memberTable" lay-filter="memberTable"></table>
</div>
#getDictLabel("gender")
#end