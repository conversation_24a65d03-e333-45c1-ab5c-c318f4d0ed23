#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()回访统计首页#end

#define css()
#end

#define content()
<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
	<div class="layui-row">
		<div class="layui-inline">
			<label class="layui-form-label">开始时间</label>
			<div class="layui-input-inline">
				<input id="startDate" name="startDate" class="layui-input" value="#(startDate??)" placeholder="请选择回访开始时间">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">结束时间</label>
			<div class="layui-input-inline">
				<input id="endDate" name="endDate" class="layui-input" value="#(endDate??)" placeholder="请选择回访开始时间">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">接待人</label>
			<div class="layui-input-inline" style="width:100px;">
				<select id="receptionUserId" name="receptionUserId" lay-search>
					<option value="">请选择</option>
					#for(u : empUserList)
						<option value="#(u.user_id)">#(u.full_name)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">接待类型</label>
			<div class="layui-input-inline" style="width:100px;">
				<select id="visitType" name="visitType">
				<option value="">请选择</option>
				#getDictList("visitType")
					<option value="#(key)">#(value)</option>
				#end
			</select>
			</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		</div>
	</div>
</form>
<div id="visitStatistics" class="layui-row"></div>
#end

#define js()
<script id="visitStatisticsTpl" type="text/html">
<table class="layui-table">
	<colgroup>
		<col width="20%">
		<col width="80%">
		<col>
	</colgroup>
	<tbody>
		<tr>
			<td align="center">电话接通情况</td>
			<td>
				<table class="layui-table">
					<colgroup>
						<col width="45%">
						<col width="45%">
						<col width="10%">
						<col>
					</colgroup>
					<thead>
						<tr>
							<th>接通数量</th>
							<th>未接通数量</th>
							<th>总数</th>
						</tr> 
					</thead>
					<tbody>
						<tr>
							<td><a actionType="callPass" style="cursor:pointer;">{{d.callPass.passCount}}</a></td>
							<td><a actionType="callNoPass" style="cursor:pointer;">{{d.callPass.noPassCount}}</a></td>
							<td><a actionType="callTotal" style="cursor:pointer;">{{d.callPass.passCount+d.callPass.noPassCount}}</a></td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr>
			<td align="center">客户来电事项</td>
			<td>
				<table class="layui-table">
					<colgroup>
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="9%">
						<col width="10%">
						<col>
					</colgroup>
					<thead>
						<tr>
							<th>咨询数量</th>
							<th>投诉数量</th>
							<th>退款数量</th>
							<th>换房数量</th>
							<th>购卡数量</th>
							<th>查卡数量</th>
							<th>转卡数量</th>
							<th>更改行程数量</th>
							<th>取消行程数量</th>
							<th>其他数量</th>
							<th>总数</th>
						</tr> 
					</thead>
					<tbody>
						<tr>
							<td><a actionType="appealTypeConsult" style="cursor:pointer;">{{d.appealType.consultCount}}</a></td>
							<td><a actionType="appealTypeComplaint" style="cursor:pointer;">{{d.appealType.complaintCount}}</a></td>
							<td><a actionType="appealTypeRefund" style="cursor:pointer;">{{d.appealType.refundCount}}</a></td>
							<td><a actionType="appealTypeChangeRoom" style="cursor:pointer;">{{d.appealType.changeRoomCount}}</a></td>
							<td><a actionType="appealTypeBuyCard" style="cursor:pointer;">{{d.appealType.buyCardCount}}</a></td>
							<td><a actionType="appealTypeCheckCard" style="cursor:pointer;">{{d.appealType.checkCardCount}}</a></td>
							<td><a actionType="appealTypeTransferCard" style="cursor:pointer;">{{d.appealType.transferCardCount}}</a></td>
							<td><a actionType="appealTypeChangeTrip" style="cursor:pointer;">{{d.appealType.changeTripCount}}</a></td>
							<td><a actionType="appealTypeCancelTrip" style="cursor:pointer;">{{d.appealType.cancelTripCount}}</a></td>
							<td><a actionType="appealTypeOther" style="cursor:pointer;">{{d.appealType.otherCount}}</a></td>
							<td>
							<a actionType="appealTypeTotal" style="cursor:pointer;">
							{{
								d.appealType.consultCount+d.appealType.complaintCount+d.appealType.refundCount+
								d.appealType.changeRoomCount+d.appealType.buyCardCount+d.appealType.checkCardCount+
								d.appealType.transferCardCount+d.appealType.changeTripCount+d.appealType.cancelTripCount+d.appealType.otherCount
							}}
							</a>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr>
			<td align="center">客户满意度</td>
			<td>
				<table class="layui-table">
					<colgroup>
						<col width="45%">
						<col width="45%">
						<col width="10%">
						<col>
					</colgroup>
					<thead>
						<tr>
							<th>满意数量</th>
							<th>不满意数量</th>
							<th>总数</th>
						</tr> 
					</thead>
					<tbody>
						<tr>
							<td><a actionType="satisfied" style="cursor:pointer;">{{d.satisfied.satisfiedCount}}</a></td>
							<td><a actionType="noSatisfied" style="cursor:pointer;">{{d.satisfied.notSatisfiedCount}}</a></td>
							<td><a actionType="satisfiedTotal" style="cursor:pointer;">{{d.satisfied.satisfiedCount+d.satisfied.notSatisfiedCount}}</a></td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
	</tbody>
</table>
</script>
<script>
layui.use(['form','layer','table', 'laydate', 'laytpl'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate, laytpl = layui.laytpl;

	customerLoad();
	
	//时间渲染
	laydate.render({
		elem: '#startDate'
		, format: 'yyyy-MM-dd'
	});
	
	//时间渲染
	laydate.render({
		elem: '#endDate'
		, format: 'yyyy-MM-dd'
	});
	
	function customerLoad(){
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		var receptionUserId = $('#receptionUserId').val();
		var visitType = $('#visitType').val();
		$('#visitStatistics').empty();
		$.post("#(ctx)/mms/customer/visitStatisticsLoad", {startDate:startDate, endDate:endDate, receptionUserId:receptionUserId, visitType:visitType}, function(rep) {
			var callPass = rep.callPass;
			var appealType = rep.appealType;
			var satisfied = rep.satisfied;
			if(JSON.stringify(callPass)==="{}"){
				callPass = {"passCount":0,"noPassCount":0};
			}
			if(JSON.stringify(appealType)==="{}"){
				appealType = {"consultCount":0,"complaintCount":0,"refundCount":0,"changeRoomCount":0,"buyCardCount":0,"checkCardCount":0,"transferCardCount":0,"changeTripCount":0,"cancelTripCount":0,"otherCount":0};
			}
			if(JSON.stringify(satisfied)==="{}"){
				satisfied = {"satisfiedCount":0,"notSatisfiedCount":0};
			}
			laytpl(visitStatisticsTpl.innerHTML).render({"callPass":callPass, "appealType":appealType, "satisfied":satisfied}, function(html){
				$('#visitStatistics').append(html);
			});
			$('a').each(function(i,obj){
				var actionType = $(obj).attr('actionType')!=null?$(obj).attr('actionType'):'';
				if(actionType!=null && actionType!=''){
					$(obj).on('click', function() {
						visitRecordLoad(actionType);
					});
				}
			});
		});
	};
	
	form.on("submit(search)",function(data){
		customerLoad();
		return false;
	});
	
	function visitRecordLoad(actionType){
		var startDate = $('#startDate').val();
		var endDate = $('#endDate').val();
		var receptionUserId = $('#receptionUserId').val();
		var visitType = $('#visitType').val();
		pop_show('回访记录','#(ctxPath)/mms/customer/visitStatisticsRecordIndex?startDate='+startDate+'&endDate='+endDate+'&receptionUserId='+receptionUserId+'&visitType='+visitType+'&actionType='+actionType,'','');
	};
});
</script>
#end