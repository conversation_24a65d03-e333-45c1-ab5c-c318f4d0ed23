#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()精确查询首页#end

#define css()
#end

#define content()
<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
	<div class="layui-form-item">
		<div class="layui-inline">
			<label class="layui-form-label">姓名</label>
			<div class="layui-input-inline">
				<input id="memberName" name="memberName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">身份证</label>
			<div class="layui-input-inline">
				<input id="idcard" name="idcard" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">手机号</label>
			<div class="layui-input-inline">
				<input id="phoneNumber" name="phoneNumber" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">是否精确查询添加</label>
			<div class="layui-input-inline" style="width:150px;">
				<select name="isPreciseQuery">
					<option value="">全部</option>
					<option value="0">否</option>
					<option value="1">是</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("member:visit:preciseQueryAddBtn")
				<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
			#end
		</div>
	</div>
</form>
<div class="layui-row">
	<table id="customerInfoTable" lay-filter="customerInfoTable"></table>
</div>
#getDictLabel("gender")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	<a class="layui-btn layui-btn-xs" lay-event="add">回访</a>
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		function customerLoad(data){
			table.render({
				id : 'customerInfoTable'
				,elem : '#customerInfoTable'
				,method : 'POST'
				,where : data
				,height:$(document).height()*0.8
				,url : '#(ctxPath)/mms/customer/preciseQueryTable'
				,cellMinWidth: 80
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field:'memberName', title: '姓名', align: 'center', unresize: true}
					,{field:'', title: '性别', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
					,{field:'birthday', title: '出生日期', align: 'center', unresize: true}
					,{field:'phoneNumber', title: '手机号', align: 'center', unresize: true}
					#shiroHasPermission("member:visit:assignBtn")
					,{field:'fullName', title: '分配人', align: 'center', unresize: true}
					#end
					,{field:'provinceName', title: '省', align: 'center', unresize: true}
					,{field:'cityName', title: '市/县', align: 'center', unresize: true}
					,{field:'townName', title: '镇/区', align: 'center', unresize: true}
					,{field:'streetName', title: '街道', align: 'center', unresize: true}
					,{field:'address', title: '地址', align: 'center', unresize: true}
					,{field:'belongOrgName', title: '分公司', align: 'center', unresize: true}
					,{field: '', title: '是否精确查询添加', width:150,unresize:true,templet:"<div>{{d.isPreciseQuery == '0'? '<span class='layui-badge'>否</span>':d.isPreciseQuery == '1'? '<span class='layui-badge layui-bg-green'>是</span>':'- -'}}</div>"}
					,{fixed:'right', title: '操作', width:80, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page:true
				,limit:10
				,limits:[10,20,30,40]
			});
			table.on('tool(customerInfoTable)',function(obj){
				if(obj.event === 'add'){
					pop_show('回访','#(ctxPath)/mms/customer/visitAdd?customerId='+obj.data.id,'','');
				}
			});
		};

		form.on("submit(search)",function(data){
			var memberName = $("#memberName").val();
			var idcard = $("#idcard").val();
			var phoneNumber = $("#phoneNumber").val();
			if(memberName==''&&idcard==''&&phoneNumber==''){
				layer.msg('请输入查询条件!',{icon:5});
			}else{
				customerLoad(data.field);
			}
			return false;
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/mms/customer/preciseQueryAdd','','');
		});
	});
</script>
#end