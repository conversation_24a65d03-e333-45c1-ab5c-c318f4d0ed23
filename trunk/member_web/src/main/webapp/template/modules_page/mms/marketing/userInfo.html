#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()分公司营销人选择#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        userMarketLoad(null);

        sd=form.on("submit(search)",function(data){
            userMarketLoad(data.field);
            return false;
        });

        function userMarketLoad(data){
            table.render({
                id : 'userMarketTable'
                ,elem : '#userMarketTable'
                ,method : 'POST'
                ,where : data
                ,limit : 15
                ,limits : [15,30,45,50]
                ,url : '#(ctxPath)/mms/activityQrcode/findListMarket'
                ,cellMinWidth: 80
                ,cols: [[
                    {field:'name', title: '营销人', align: 'center', unresize: true}
                    ,{field:'userName', title: '账号', align: 'center', unresize: true}
                    ,{field:'fullName', title: '分公司', align: 'center', unresize: true}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 90, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };

        // 选择
        table.on('tool(userMarketTable)',function(obj){
            if(obj.event === 'choose'){
                parent.getNameAndUserId(obj.data.id,obj.data.name);
                pop_close();
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    <a class="layui-btn layui-btn-xs" lay-event="choose">选择</a>
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            营销人:
            <div class="layui-inline">
                <input id="name" name="name" class="layui-input">
            </div>
            &nbsp;&nbsp;
            所属分公司:
            <div class="layui-inline">
                <select id="officeId" name="officeId" lay-search>
                    <option value="">请选择所属分公司</option>
                    #for(o : officeList)
                    <option value="#(o.id)">#(o.fullName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
    </div>
    <table id="userMarketTable" lay-filter="userMarketTable"></table>
</div>
#end