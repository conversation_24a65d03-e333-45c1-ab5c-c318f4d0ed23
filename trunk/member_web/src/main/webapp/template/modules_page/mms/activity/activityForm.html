#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()现场抽奖活动编辑页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>基地</label>
		<div class="layui-input-block">
			<select id="baseId" name="baseId" lay-verify="required">
				<option value="">请选择基地</option>
				#for(b : baseList)
					<option value="#(b.id)" #(b.id == model.baseId?'selected':'')>#(b.baseName)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>活动名称</label>
		<div class="layui-input-block">
			<input type="text" name="activityName" class="layui-input" lay-verify="required" value="#(model.activityName??)" placeholder="请输入活动名称" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>开始时间</label>
		<div class="layui-input-block">
			<input type="text" id="startTime" name="startTime" class="layui-input" lay-verify="required" value="#date(model.startTime??, 'yyyy-MM-dd HH:mm')" placeholder="请选择开始时间" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>结束时间</label>
		<div class="layui-input-block">
			<input type="text" id="endTime" name="endTime" class="layui-input" lay-verify="required" value="#date(model.endTime??, 'yyyy-MM-dd HH:mm')" placeholder="请选择结束时间" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>有效状态</label>
		<div class="layui-input-block">
			<select id="isEnable" name="isEnable" lay-verify="required">
	            <option value="">请选择有效状态</option>
	            <option value="0" #(model.isEnable?? == '0' ? 'selected':'')>有效</option>
	            <option value="1" #(model.isEnable?? == '1' ? 'selected':'')>失效</option>
	        </select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remark" class="layui-textarea" placeholder="请输入备注">#(model.remark??)</textarea>
		</div>
	</div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//时间渲染
	laydate.render({
		elem: '#startTime'
		, format: 'yyyy-MM-dd HH:mm'
	});
	
	//时间渲染
	laydate.render({
		elem: '#endTime'
		, format: 'yyyy-MM-dd HH:mm'
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/activity/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.pageTableReload();
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end