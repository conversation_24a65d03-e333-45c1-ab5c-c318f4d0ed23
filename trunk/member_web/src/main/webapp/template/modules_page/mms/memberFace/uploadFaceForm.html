#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()上传人脸界面#end

#define css()
<style>
    img{
        max-height: 300px;
    }
</style>

#end

#define js()
<script type="text/javascript">
    layui.use(['form','upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var upload = layui.upload;

        getNameAndIdcard=function(memberId,fullName,idcard){
            $("#fullName").val(fullName);
            $("#idcard").val(idcard);
            $("#memberId").val(memberId);
        }

        // 获取会员
        $("#chooseMember").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/memberface/chooseMember" ;
            pop_show("选择会员信息",url,850,450);
        });


        //保存
        form.on('submit(confirmBtn)', function(){
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/memberface/save',
                data: $("#faceInfoForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.layui.table.reload('faceTable');
                    }
                },
                complete : function() {
                }
            });
            return false;
        });


        //普通图片上传
        var uploadInst = upload.render({
            elem: '#profilePhoto'
            ,url:$("#commonUpload").val() +'/upload?bucket=face'
            ,before: function(obj){
                $("#confirmBtn").attr("disabled",true);
                $("#confirmBtn").addClass("layui-btn-disabled");
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#profilePhotoImg').attr('src', result);
                    $('#imgBase64').val(result.substring(result.indexOf(",") + 1));
                });
            }
            ,done: function(res){
                $("#confirmBtn").attr("disabled",false);
                $("#confirmBtn").removeClass("layui-btn-disabled");
                //如果上传失败
                if(res.state == 'ok'){
                    $("#uploadPath").val($('#imgBase64').val());
                    layer.msg(res.msg,{icon:1,time:5000});
                }else {
                    return layer.msg('上传失败');
                }
            }
            ,error: function(){
                //演示失败状态，并实现重传
                var demoText = $('#demoText');
                demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-mini demo-reload">重试</a>');
                demoText.find('.demo-reload').on('click', function(){
                    uploadInst.upload();
                });
            }
        });

    });
</script>
#end

#define content()
<body class="v-theme">
<input type="hidden" id="imgBase64" value=""/>
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="faceInfoForm">
            <div class="layui-row">
                <div class="layui-col-xs6" style="padding-right: 20px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>姓名</label>
                        <div class="layui-input-inline">
                            <input type="text" id="fullName" autocomplete="off" class="layui-input" value="#(member != null ?member.fullName:'')" readonly>
                        </div>
                        <div>
                            <input type="button" class="layui-btn" id="chooseMember" value="选择会员">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>身份证</label>
                        <div class="layui-input-block">
                            <input type="text" id="idcard" class="layui-input" value="#(member != null ? member.idcard:'')" readonly>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs6">
                <div class="layui-upload" style="text-align: center;">
                    <div class="layui-upload-list">
                        <img class="layui-upload-img" id="profilePhotoImg" width="200px" src="#(faceId ? (picture ? base + picture :'') : '')">
                        <p id="profilePhotoText"></p>
                        <input type="hidden" id="uploadPath" name="picturePath" value="">
                    </div>
                </div>
                </div>
            </div>

            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" id="commonUpload" value="#(commonUpload??)"/>
                    <input type="hidden" id="memberId" name="face.memberId" value="#(memberId??)"/>
                    <input type="hidden" id="id" name="faceId" value="#(face.id??)"/>
                    <button type="button" class="layui-btn" id="profilePhoto" #if(faceId??!=null) style="display: none;" #end>上传人脸照片</button>
                    &nbsp;&nbsp;
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn" #if(faceId??!=null) style="display: none;" #end>保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end