#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()咨询登记跟进情况首页#end

#define css()
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <div class="layui-input-inline">
			            <div class="layui-btn-group">
							<input type="hidden" id="registerId" value="#(registerId)">
					        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
		    			</div>
			        </div>
	    		</div>
			</form>
	    </span>
    </div>
	<div class="layui-row">
		<table id="visitTable" lay-filter="visitTable"></table>
	</div>
</div>
#getDictLabel("visitType")
#end

#define js()
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'visitTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/mms/consult/visitTable'
	    , method: 'post'
// 	    , height: vipTable.getFullHeight()    //容器高度
	    , where: {registerId:$('#registerId').val()}
	    , cols: [[                  //标题栏
	          {type: 'numbers', title: '序号', width: 60, unresize:true}
	        , {field: '', title: '回访类型', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
	        , {field: 'visitContent', title: '回访内容', unresize:true}
	        , {field: '', title: '创建时间', width: 160, unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
// 	        , {fixed: 'right', title: '操作', width: 120, align: 'center'} //这里的toolbar值是模板元素的选择器
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{registerId:$('#registerId').val()});
    }
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
})
</script>
#end
