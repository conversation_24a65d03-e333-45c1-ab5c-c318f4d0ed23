#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()微信文章管理#end

#define css()
#end

#define content()
<div>
    <div class="demoTable">

        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            标题:
            <div class="layui-inline">
                <input id="title" name="title" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:weiXinArticle:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        #shiroHasPermission("member:weiXinArticle:batchDelBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
        #end
    </div>
    <table id="articleTable" lay-filter="articleTable"></table>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
layui.use(['form','layer','table'], function() {
    var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

    articleLoad(null);

    sd=form.on("submit(search)",function(data){
        articleLoad(data.field);
        return false;
    });

    // 添加
    $("#add").click(function(){
        $(this).blur();
        var url = "#(ctxPath)/mms/wxarticle/form" ;
        pop_show("新增微信文章",url,900,500);
    });

    function articleLoad(data){
        table.render({
            id : 'articleTable'
            ,elem : '#articleTable'
            ,method : 'POST'
            ,where : data
            ,height:$(document).height()*0.8
            ,limit : 10
            ,limits : [10,20,30,40]
            ,url : '#(ctxPath)/mms/wxarticle/findListPage'
            ,cellMinWidth: 80
            ,cols: [[
                 {type:'checkbox'},
                 {type: 'numbers', width:100, title: '序号',unresize:true}
                ,{field:'title', title: '标题', align: 'center', unresize: true}
                ,{field:'coverPic', title: '封面', align: 'center', unresize: true,templet:function (d) {
                    var str="- -";
                    var imgUrl=d.coverPic;
                    if(imgUrl!=null && imgUrl!="" && typeof(imgUrl)!="undefined"){
                        var img=imgUrl;
                        var str='<img src="'+img+'" height="30px"/>';
                    }
                    return str;
                }}
                ,{field:'digest', title: '摘要', align: 'center', unresize: true}
                ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
            ]]
            ,page : true
        });
    };

    table.on('tool(articleTable)',function(obj){
        if (obj.event === 'del') {
            layer.confirm("确定要作废吗?",function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mms/wxarticle/delete',
                    data: {id:obj.data.id},
                    notice:true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            table.reload('articleTable');
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        }else if(obj.event === 'edit'){
            var url = "#(ctxPath)/mms/wxarticle/form?id=" + obj.data.id ;
            layerShow("编辑微信文章",url,900,500);
        }
    });

    //批量获取被作废数据
    getCheckTableData = function(){
        var wxarticleCheckStatus = table.checkStatus('articleTable');
        // 获取选择状态下的数据
        return wxarticleCheckStatus.data;
    }
    //批量作废
    $("#batchDel").click(function(){
        layer.confirm("确定批量作废吗?",function(index){
            var jsonData=getCheckTableData();
            if(jsonData == null || jsonData == ''){
                layer.msg('请勾选作废数据', function () {});
                return;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/wxarticle/batchDel',
                data: {wxarticleData:JSON.stringify(jsonData)},
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        table.reload('articleTable');
                    }
                },
                complete : function() {
                }
            });
            layer.close(index) ;
        });
    });
});
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:weiXinArticle:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("member:weiXinArticle:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end