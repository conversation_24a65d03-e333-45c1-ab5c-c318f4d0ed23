#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员黑名单管理#end

#define css()
#end

#define js()
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		blackListLoad(null);

		form.on("submit(search)",function(data){
			blackListLoad(data.field);
			return false;
		});

		function blackListLoad(data){
			table.render({
				id : 'blackListTable'
				,elem : '#blackListTable'
				,method : 'POST'
				,where : data
				,height:$(document).height()*0.8
				,limit : 10
				,limits : [10,20,30,40]
				,url : '#(ctxPath)/mms/blackList/pageTable'
				,cellMinWidth: 80
				,cols: [[
					{type: 'numbers', width:100, title: '序号',unresize:true}
					,{field:'fullName', title: '姓名', align: 'center', unresize: true}
					#shiroHasPermission("member:idcardSee")
					,{field:'idcard', title: '身份证号', align: 'center', unresize: true}
					#end
					#shiroHasPermission("member:telephoneSee")
					,{field:'telephone', title: '电话', align: 'center', unresize: true}
					#end
					,{field:'', title: '是否有效', align: 'center', unresize: true,width:100,templet:function (d) {
						if(d.isEnable==='1'){
						    return '<span class="layui-badge layui-bg-red">无效</span>';
						}else{
						    return '<span class="layui-badge layui-bg-green">有效</span>';
						}
					}}
					,{field:'remarks', title: '备注', align: 'center', unresize: true}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};
		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/mms/blackList/form" ;
			pop_show("新增黑名单",url,800,350);
		});

		table.on('tool(blackListTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/blackList/form?id="+obj.data.id ;
				pop_show("编辑黑名单",url,800,350);
			}else if(obj.event === 'record'){
				var url = "#(ctxPath)/mms/blackList/recordIndex?id="+obj.data.id ;
				pop_show("操作记录",url,800,500);
			}
		});
	});
</script>
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("member:blackList:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:blackList:recordBtn")
	<a class="layui-btn layui-btn-xs" lay-event="record">操作记录</a>
	#end
</div>
</script>
#end

#define content()
<div>
	<div class="demoTable">
		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
			姓名:
			<div class="layui-inline">
				<input id="fullName" name="fullName" class="layui-input">
			</div>
<!-- 			&nbsp;&nbsp; -->
<!-- 			身份证: -->
<!-- 			<div class="layui-inline"> -->
<!-- 				<input id="idcard" name="idcard" class="layui-input"> -->
<!-- 			</div> -->
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
		</form>
		#shiroHasPermission("member:blackList:addBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
		#end
	</div>
	<table id="blackListTable" lay-filter="blackListTable"></table>
</div>
#getDictLabel("gender")
#end