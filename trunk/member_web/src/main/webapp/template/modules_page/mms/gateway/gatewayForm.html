#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()网关设备信息展示#end

#define css()

#end

#define content()
<div style="margin: 15px;">
    <div class="demoTable">
        <form class="layui-form layui-form-pane" action="" method="post" id="gatewayForm" style="margin-left:25px;">
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>网关名称</label>
                <div class="layui-input-inline">
                    <input type="text" name="gateway.gatewayName" class="layui-input" lay-verify="required" value="#(gateway.gatewayName??)" placeholder="请输入网关名称">
                </div>
                <label class="layui-form-label"><span>*</span>网关编号</label>
                <div class="layui-input-inline">
                    <input type="text" name="gateway.gatewayNo" class="layui-input" lay-verify="required" value="#(gateway.gatewayNo??)" placeholder="请输入网关编号">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>网关型号</label>
                <div class="layui-input-inline">
                    <input type="text" name="gateway.gatewayModel" class="layui-input" lay-verify="required" value="#(gateway.gatewayModel??)" placeholder="请输入网关型号">
                </div>
                <label class="layui-form-label"><span>*</span>厂家</label>
                <div class="layui-input-inline">
                    <input type="text" name="gateway.manufactor" class="layui-input" value="#(gateway.manufactor??)" lay-verify="required" placeholder="请输入厂家">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>所属基地</label>
                <div class="layui-input-inline">
                    <select id="baseId" name="gateway.baseId" lay-filter="base" lay-search lay-verify="required">
                        <option value="">请选择所属基地</option>
                        #for(b : baseList)
                        <option value="#(b.id)" #(gateway != null ? (b.id == gateway.baseId?'selected':'') :'')>#(b.baseName)</option>
                        #end
                    </select>
                </div>
                <label class="layui-form-label"><span>*</span>所属楼栋</label>
                <div class="layui-input-inline">
                    <div class="layui-form" lay-filter="buildingSelectFilter">
                    <select id="buildingId" name="gateway.buildingId" lay-filter="building" lay-search lay-verify="required">
                        <option value="">请选择所属楼栋</option>
                    </select>
                    </div>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>所属楼层</label>
                <div class="layui-input-inline">
                    <div class="layui-form" lay-filter="floorSelectFilter">
                    <select id="floorId" name="gateway.floorId" lay-filter="floorId" lay-search lay-verify="required">
                        <option value="">请选择所属楼层</option>
                    </select>
                    </div>
                </div>
                <label class="layui-form-label"><span>*</span>场景类型</label>
                <div class="layui-input-inline">
                    <select name="gateway.partType" lay-search lay-verify="required">
                        <option value="">请选择场景类型</option>
                        #getDictList("part_type")
                        <option value="#(key)" #(gateway != null ?(key == gateway.partType ? 'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label"><span>*</span>网关位置</label>
                <div class="layui-input-inline">
                    <select name="gateway.position" lay-search lay-verify="required">
                        <option value="">请选择位置</option>
                        #getDictList("gateway_position")
                        <option value="#(key)" #(gateway != null ?(key == gateway.position ? 'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>
                <label class="layui-form-label"><span>*</span>摆放方式</label>
                <div class="layui-input-inline">
                    <select name="gateway.putWay" lay-search lay-verify="required">
                        <option value="">请选择摆放方式</option>
                        #getDictList("put_way")
                        <option value="#(key)" #(gateway != null ?(key == gateway.putWay ? 'selected':''):'')>#(value)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="gateway.remark" class="layui-textarea" rows="5" placeholder="请输入备注">#(gateway.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" id="gatewayId" name="gateway.id" value="#(gateway.id??)">
                    <input type="hidden" id="dId" value="#(gateway.buildingId??)">
                    <input type="hidden" id="fId" value="#(gateway.floorId??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;


        $(function(){
            if($("#gatewayId").val() != null && $("#gatewayId").val() != ''){
                var baseId = $("#baseId").val();
                var buildingId = $("#dId").val();
                var floorId = $("#fId").val();
                if(baseId != null && baseId != ''){
                    getBuildings(baseId,buildingId);
                }
                if(buildingId != null && buildingId != ''){
                    getFloors(buildingId,floorId);
                }
            }
        });

        //楼栋下拉框
        form.on('select(base)', function(data) {
            getBuildings(data.value,null);
        });

        //获取所有楼栋
        function getBuildings(data,selectedVal){
            $("#buildingId").empty();
            $("#buildingId").prepend("<option value=''>请选择所属楼栋</option>");
            $("#floorId").empty();
            $("#floorId").prepend("<option value=''>请选择所属楼层</option>");
            if(data == null || data == ''){
                form.render('select', 'buildingSelectFilter');
                form.render('select', 'floorSelectFilter');
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/gateway/getBuildings',
                data: {id:data},
                notice: false,
                success : function(rep){
                    if (rep.state=='ok') {
                        if(rep.resultData.length>0){
                            $.each(rep.resultData, function(i, item){
                                if(selectedVal != null && selectedVal != '' && selectedVal == item.id){
                                    $("#buildingId").append("<option value='"+item.id+"' selected>"+item.buildingName+"</option>");
                                }else{
                                    $("#buildingId").append("<option value='"+item.id+"'>"+item.buildingName+"</option>");
                                }
                            });
                        }
                        form.render('select', 'buildingSelectFilter');
                        form.render('select', 'floorSelectFilter');
                    } else {
                        layer.msg(rep.msg, {icon : 5});
                    }
                },
                complete : function() {
                }
            });
        }

        //楼层下拉框
        form.on('select(building)', function(data) {
            getFloors(data.value,null)
        });

        //获取所有楼层
        function getFloors(data,selectedVal){
            $("#floorId").empty();
            $("#floorId").prepend("<option value=''>请选择所属楼层</option>");
            if(data == null || data == ''){
                form.render('select', 'floorSelectFilter');
                return false;
            }
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/gateway/getFloors',
                data: {id:data},
                notice: false,
                success : function(rep){
                    if (rep.state=='ok') {
                        if(rep.resultData.length>0){
                            $.each(rep.resultData, function(i, item){
                                if(selectedVal != null && selectedVal != '' && selectedVal == item.id){
                                    $("#floorId").append("<option value='"+item.id+"' selected>"+item.floorName+"</option>");
                                }else{
                                    $("#floorId").append("<option value='"+item.id+"'>"+item.floorName+"</option>");
                                }
                            });
                        }
                        form.render('select', 'floorSelectFilter');
                    } else {
                        layer.msg(rep.msg, {icon : 5});
                    }
                },
                complete : function() {
                }
            });
        }

        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/gateway/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#gatewayForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("gatewayTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
