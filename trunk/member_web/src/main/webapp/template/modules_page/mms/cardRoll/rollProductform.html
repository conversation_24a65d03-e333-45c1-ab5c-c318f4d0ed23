#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/layui/css/layui.css"  media="all">
<style>
    .layui-form-label {
        width: 130px !important;
        text-align: center !important;
    }
    .layui-input-block {
        margin-left: 130px !important;
    }
    .layui-select-disabled .layui-disabled {
        color: black!important;
        border-color: #eee!important;
    }
    input[type="text"]:disabled {
        background-color: #fff;
    }
    #roomTable  td, #roomTable th {
        padding: 9px 5px;
    }

    /*#roomTable .layui-body{overflow-y: scroll;}*/

    #roomTable .layui-anim {
        position : fixed;
    }
    .layui-form-item{
        margin-bottom: 8px;
    }

    /*xm-select > .xm-body {
        width: 400% !important;
        left: -200px !important;
    }*/
</style>
#end

#define js()
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
    layui.config({
        base: '/static/js/extend/',
    });
    layui.use(['form','laytpl','laydate','upload','table'],function(){
        var form = layui.form ;
        var $ = layui.$ ;
        var laytpl = layui.laytpl;
        var laydate=layui.laydate;
        var upload=layui.upload;
        var table=layui.table;


// 		form.render('radio');

        //校验
        form.verify({
            checkNumber:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                    if(value.length != 7 && value.length != 8){
                        return "号段固定为7位或8位";
                    }
                }
            },
            checkPrefix:function(value){
                if(value != null){
                    var reg = new RegExp("^[0-9]*$");
                    if(!reg.test(value)){
                        return "只能输入数字";
                    }
                    if(value.length != 2){
                        return "卡号前缀固定为2位";
                    }
                }
            }
        });


        form.on('select(deptId)',function (d) {
            let dataStr=$("#deptId option[value='"+d.value+"']").attr("data");
            console.log(dataStr);
            let positionList=JSON.parse(dataStr);
            let positionStr="<option value=''>请选择</option>";
            $.each(positionList,function (index,item) {
                if(positionList.length==1){
                    positionStr+="<option value='"+item.id+"' selected>"+item.positionName+"</option>";
                }else{
                    positionStr+="<option value='"+item.id+"'>"+item.positionName+"</option>";
                }
            });
            console.log(positionStr);
            $("#positionId").html(positionStr);
            form.render('select')
        });





        $("#proprietorBtn").on('click',function () {
            layerShow('选择业主','#(ctxPath)/fina/leaseRecordApply/proprietorIndex',900,600);
            return false;
        });

        selectedProprietor=function(data){
            $("#proprietorName").val(data.name);
            $("#supplierId").val(data.id);
            $.post('#(ctxPath)/fina/leaseRecordApply/suppliersPayList',{'supplierId':data.id},function (res) {
                if(res.length==0){
                    layer.msg('该供应商的付款方式为空，请先维护', {icon:5,time: 2000});
                    return false;
                }
                let str="<option value=''>请选择</option>"
                $.each(res,function (index,item) {
                    str+="<option value='"+item.id+"'>"+item.paymentWayStr+"("+item.pay_account+")"+"</option>"
                })
                $("#suppliersPayId").html(str);
                form.render('select','suppliersPayIdDiv');
            });
        }

        $("#addRoom").on('click',function () {
            addRoomTpl();

            $("#roomTable .layui-form-select").on('click',function(){
                var scrollTop=$(document).scrollTop();//获取页面滚动的高度
                var width = $(this).width();
                var top = $(this).offset().top;
                var left = $(this).offset().left;
                $(this).find("dl").css({"min-width":width+"px", top:top-scrollTop+40+"px", left:left+"px"});
            });

        });
        $("#roomTable .layui-form-select").on('click',function(){
            var scrollTop=$(document).scrollTop();//获取页面滚动的高度
            var width = $(this).width();
            var top = $(this).offset().top;
            var left = $(this).offset().left;
            $(this).find("dl").css({"min-width":width+"px", top:top-scrollTop+40+"px", left:left+"px"});
        });



        addRoomTpl = function () {
            let roomCount=Number($('#roomCount').val())+1;
            $('#roomCount').val(roomCount);
            laytpl(roomTrTpl.innerHTML).render({
                'idx': roomCount
            }, function (html) {
                $("#roomTbody").append(html);
            });

            //时间渲染
            laydate.render({
                elem: '#contractDate' + roomCount
                ,trigger:'click'
                ,range: true
            });
            laydate.render({
                elem: '#roomDate' + roomCount
                ,trigger:'click'
                ,range: true
            });

            form.render('select');
        };



        delTr=function (trIndex,id) {
            if(id==''){
                $('#' + trIndex).remove();
            }else{
                layer.confirm('是否要作废?', function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/cardRoll/delRel',
                        data: {'id':id,'userId':$("#userId").val()},
                        notice: true,
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                $('#' + trIndex).remove();
                            }
                        },
                        complete : function() {
                        }
                    });
                    layer.close(index);
                });
            }
            return false;
        }

        selectedRoom=function (trIndex) {

            layerShow('选择商品','#(ctxPath)/mms/cardRoll/stockModelIndex?trIndex='+trIndex,1500,650);
        }

        selectedRoomData=function (data,trIndex) {
            console.log(data,trIndex);
            $("#modelName"+trIndex).val(data.name);
            $("#modelId"+trIndex).val(data.id);
            $("#standard"+trIndex).text(data.standard);





        }

        checkRoomData=function (id) {
            let isExist=false;
            $.each($("#roomTbody").find('tr'),function (index,item) {
                let trId= $(item).attr('id');
                let trIndex = trId.replace('model-','');
                if($("#modelId"+trIndex).val()==id){
                    isExist=true;
                    return false;
                }

            });
            return isExist;
        }

        form.on('submit(submit)',function (obj) {
            layer.confirm("确定要提交吗?",function(index){

                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(abort)',function (obj) {
            layer.confirm("确定要中止吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写中止原因。',{icon:5});
                    return false;
                }
                doTask(8,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(reject)',function (obj) {
            layer.confirm("确定要拒绝吗?",function(index){
                if($("#msg").val()==''){
                    layer.msg('请填写拒绝原因。',{icon:5});
                    return false;
                }
                doTask(6,$("#msg").val());
                layer.close(index);
            });
            return false;
        })

        form.on('submit(approve)',function (obj) {
            layer.confirm("确定要通过吗?",function(index){
                doTask(5,$("#msg").val());
                layer.close(index);
            });
            return false;
        });
        doTask=function (stepState,msg) {

            var data={"taskId":$("#taskId").val(),"taskNo":$("#taskNo").val(),"currentStepAlias":$("#currentStepAlias").val(),"stepState":stepState,"msg":msg,'userId':$("#userId").val()};
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mall/productSaleApply/doTask',
                data: data,
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.cardTypeTableReload(null);
                    }
                },
                complete : function() {
                }
            });
        }

        // 保存并提交
        form.on('submit(confirmBtn)',function(obj){
            layer.load();

            let taskId=$("#taskId").val();

            let params=$("#frm").serialize();

            let flag=false;
            let typeParamStr='';
            let typeNullName='';
            if($("#roomTbody").find('tr').length==0){
                layer.msg('上架商品不能为空', {icon:5,time: 2000});
                return false;
            }
            $.each($("#roomTbody").find('tr'),function (index,item) {
                let trId= $(item).attr('id');
                let trIndex = trId.replace('model-','');

                if(xmSelect.get("#typeTree"+trIndex,true).getValue().length==0){
                    typeNullName=$('#modelName'+trIndex).val();
                    typeParamStr='';
                    flag=true;
                    return false;
                }
                let typeTreeId=xmSelect.get("#typeTree"+trIndex,true).getValue()[0].id;
                typeParamStr+='&model'+trIndex+'.typeId='+typeTreeId;

            });
            if(flag){
                layer.msg('['+typeNullName+']的商品分类不能为空', {icon:5,time: 2000});
                return false;
            }
            params+=typeParamStr;



            if(taskId==''){
                params+='&saveType=2';
                util.sendAjax({
                    url:"#(ctxPath)/mall/productSaleApply/savProductSaleApply",
                    type:'post',
                    data:params,
                    notice:true,
                    success:function(returnData){
                        if(returnData.state==='ok'){
                            layer.closeAll('loading');
                            pop_close();
                            parent.cardTypeTableReload(null);
                        }
                    },
                    unSuccess:function (returnData) {
                        layer.closeAll('loading');
                    }
                });
            }else{
                util.sendAjax({
                    url:"#(ctxPath)/mall/productSaleApply/savProductSaleApply",
                    type:'post',
                    data:params,
                    notice:true,
                    success:function(returnData){
                        if(returnData.state==='ok'){
                            // layer.closeAll('loading');
                            // pop_close();
                            doTask(5,$("#msg").val());
                            //parent.cardTypeTableReload(null);
                            parent.reloadTable();
                        }
                    },
                    unSuccess:function (returnData) {
                        layer.closeAll('loading');
                    }
                });


            }

            return false;
        }) ;
        //保存
        form.on('submit(saveBtn)',function(obj){
            layer.load();


            let params=$("#frm").serialize();


            console.log(params);

            util.sendAjax({
                url:"#(ctxPath)/mms/cardRoll/saveRollTypeProduct",
                type:'post',
                data:params,
                notice:true,
                success:function(returnData){
                    if(returnData.state==='ok'){
                        layer.closeAll('loading');
                        pop_close();
                        //parent.cardTypeTableReload(null);
                        parent.reloadTable();
                    }
                },
                unSuccess:function (returnData) {
                    layer.closeAll('loading');
                }
            });
            return false;
        }) ;




        #for(roomRecord : itemList)
            let typeTree#(for.index+1)=xmSelect.render({
                el: '#typeTree#(for.index+1)',
                autoRow: true,
                height: '200px',
                prop: {
                    name: 'name',
                    value: 'id',
                },
                radio: true,
                tree: {
                    show: true,
                    expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
                    showFolderIcon: true,
                    showLine: true,
                    indent: 15,
                    lazy: true,
                    clickExpand: true,
                    clickClose: true,
                    strict: false,
                    //点击节点是否选中
                    clickCheck: true,

                    load: function(item, cb){

                    }
                },
                height: 'auto',
                width:'auto',
                data(){
                    return [];
                }
            })
            $.post('#(ctxPath)/mallManageApi/getMallProductTypeTree',{},function (res) {
                typeTree#(for.index+1).update({
                    data:res.data
                });
                typeTree#(for.index+1).setValue(["#(roomRecord.typeId??)"]);
            });
        #end

        #if((!isSaveHandle && taskId!=null) || !isEdit)
        $(".layui-col-md7 button").css('display','none');
        $(".layui-col-md7 input").prop('readonly',true);
        $(".layui-col-md7 input").prop('disabled',true);

        $(".layui-col-md7 select").prop('disabled',true);
        $(".layui-col-md7 textarea").prop('readonly',true);
        form.render('select');
        #else if(model!=null)
            layui.event("form", "select(deptId)", {'value':'#(model.deptId??)'});
        #end




    }) ;
</script>
<script id="roomTrTpl" type="text/html">
    <tr id="model-{{d.idx}}">

        <td>
            <div class="layui-row" style="width: 200px;">
                <input type="text" id="modelName{{d.idx}}" name="model{{d.idx}}.modelName"  lay-verify="required"readonly class="layui-input" value="" style="cursor:pointer;"  onclick="selectedRoom({{d.idx}})" placeholder="点击选择商品" autocomplete="off">
                <input type="hidden" id="modelId{{d.idx}}" name="model{{d.idx}}.modelId" value="">
            </div>
        </td>
        <td id="standard{{d.idx}}">

        </td>

        <td id="numTd{{d.idx}}">
            <input type="text" id="num{{d.idx}}" name="model{{d.idx}}.num"  lay-verify="required" class="layui-input" value=""   placeholder="输入数量" autocomplete="off">
        </td>


        <td>
            <input type="hidden"  name="room{{d.idx}}.id" value="">
            <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delTr('model-{{d.idx}}','')">作废</button>
        </td>
    </tr>
</script>
#end

#define content()
<div class="my-btn-box">
    <form class="layui-form layui-form-pane" action="" id="frm">
        <div class="layui-row">


                <div class="layui-form-item" > <!--style="overflow: auto;"-->

                    <button class="layui-btn" type="button" id="addRoom" style="float: right;">添加商品</button>
                    <table class="layui-table" style="overflow: auto;"  id="roomTable" >
                        <!--<col width="30%">
                        <col width="60%">
                        <col width="10%">-->
                        <thead>
                        <tr>
                            <th ><font color="red">*</font>商品</th>
                            <th ><font color="red"></font>规格</th>
                            <th ><font color="red"></font>兑换数量</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody id="roomTbody">
                        <input id="typeId" name="typeId" type="hidden" value="#(typeId??)">
                        <input id="roomCount" name="roomCount" type="hidden" value="#(recordList.size()??0)">
                        #for(roomRecord : recordList)
                        <tr id="model-#(for.index+1)">

                            <td>
                                <div class="layui-row" style="width: 200px;">
                                    <input type="text" id="modelName#(for.index+1)" name="model#(for.index+1).modelName"  lay-verify="required"readonly class="layui-input" value="#(roomRecord.modelName??)" style="cursor:pointer;"  onclick="selectedRoom(#(for.index+1))" placeholder="点击选择商品" autocomplete="off">
                                    <input type="hidden" id="modelId#(for.index+1)" name="model#(for.index+1).modelId" value="#(roomRecord.modelId??)">
                                    <input type="hidden"   name="model#(for.index+1).id" value="#(roomRecord.id??)">

                                </div>
                            </td>



                            <td id="standard#(for.index+1)">
                                #(roomRecord.standard??)
                            </td>
                            <td id="numTd#(for.index+1)">
                                <input type="text" id="num#(for.index+1)" name="model#(for.index+1).num"  lay-verify="required" class="layui-input" value="#(roomRecord.num??)"   placeholder="输入数量" autocomplete="off">
                            </td>


                            <td>
                                #if((isSaveHandle || taskId==null) && isEdit)
                                <button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delTr('model-#(for.index+1)','#(roomRecord.id??)')">作废</button>
                                #end
                            </td>
                        </tr>
                        #end
                        </tbody>
                    </table>

                </div>

                <div class="layui-form-item" style="margin-bottom:100px;"></div>

                <input type="hidden" name="id" value="#(model.id??)">
                <input type="hidden" id="taskId" value="#(model.taskId??)">
                <input type="hidden" id="userId"  name="userId" value="#(user.id??)" >
            </div>
            <div  #if(type=='H5') style="display: none" #else class="layui-col-md4"  #end>
                #if(stepts.size()??0>0)
                <div class="layui-row" style="overflow: auto;height: 240px;">
                    <table class="layui-table" id="steptsTable" style="margin-top: 0px;">
                        <!--<colgroup>
                            <col width="10%">
                            <col width="13%">
                            <col width="30%">
                            <col width="30%">
                            <col width="17%">
                        </colgroup>-->
                        <thead>
                        <tr>
                            <th style="text-align: center;">序号</th>
                            <th style="text-align: center;">节点</th>
                            <th style="text-align: center;">流程</th>
                            <th style="text-align: center;">意见</th>
                            <th style="text-align: center;">操作时间</th>
                            <th style="text-align: center;">操作人</th>
                        </tr>
                        </thead>
                        <tbody>
                        #for(stept:stepts)
                        <tr>
                            <td align="center">#(for.index+1)</td>
                            <td>#(stept.ActivityName)</td>
                            <td align="center">
                                #if(stept.StepState==3)
                                提交
                                #else if(stept.StepState==1)
                                待处理
                                #else if(stept.StepState==0)
                                等待
                                #else if(stept.StepState==2)
                                正处理
                                #else if(stept.StepState==4)
                                撤回
                                #else if(stept.StepState==5)
                                批准
                                #else if(stept.StepState==6)
                                拒绝
                                #else if(stept.StepState==7)
                                转移
                                #else if(stept.StepState==8)
                                失败
                                #else if(stept.StepState==9)
                                跳过
                                #end
                            </td>
                            <td>#(stept.Comment)</td>
                            <td>#(stept.CommentTime)</td>
                            <td align="center">#(stept.CommentUserName)</td>
                        </tr>
                        #end
                        </tbody>
                    </table>
                </div>


                <form class="layui-form layui-form-pane" id="taskForm">
                    <div class="layui-form-item layui-form-text">
                        <label class="layui-form-label" style="width: 100% !important;text-align: left !important;">意见</label>
                        <div class="layui-input-block" style="margin-left: 0px!important;">
                            <textarea id="msg" name="msg" placeholder="请输入内容" lay-verify="" class="layui-textarea" #if(!allowAbort && !allowReject && !allowApprove && !allowSubmit ) disabled #end></textarea>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block pull-right">

                            <button class="layui-btn layui-border-red#if(!allowAbort) layui-btn-disabled#end" lay-submit lay-filter="abort" #if(!allowAbort) disabled style="display: none;" #end >中止</button>
                            <button class="layui-btn #if(!allowSubmit) layui-btn-disabled#end" lay-submit lay-filter="submit" #if(!allowSubmit) disabled style="display: none;" #end>提交</button>
                            <button class="layui-btn layui-border-orange#if(!allowReject) layui-btn-disabled#end" lay-submit lay-filter="reject" #if(!allowReject) disabled style="display: none;" #end>拒绝</button>
                            <button class="layui-btn#if(!allowApprove) layui-btn-disabled#end" lay-submit lay-filter="approve" #if(!allowApprove) disabled style="display: none;" #end>通过</button>
                        </div>
                    </div>
                </form>
                #end
            </div>
        </div>
        <div class="layui-form-footer">
            <div class="pull-right">
                <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                <button type="button" class="layui-btn" lay-submit=""  lay-filter="saveBtn">保&nbsp;&nbsp;存</button>

            </div>
        </div>
    </form>
</div>
#end