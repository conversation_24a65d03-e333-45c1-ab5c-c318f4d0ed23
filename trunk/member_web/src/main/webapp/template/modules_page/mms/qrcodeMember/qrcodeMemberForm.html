#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()活动会员展示#end

#define css()
#end


#define js()
<script type="text/javascript">
    layui.use(['form','jquery','laydate'], function(){
        var form = layui.form,$ = layui.jquery;var laydate = layui.laydate;

        //时间渲染
        laydate.render({
            elem : '#prizeTime',
            type:'datetime',
            trigger: 'click'
        });

        $(function(){
            changeDiv($("#prizeFlag").val());
        });

        //下拉框联动
        form.on('select(prizeFlag)', function(data) {
            changeDiv(data.value);
        });

        function changeDiv(data){
            if(data == '0'){
                $("#prizeTime").attr("lay-verify","");
                $("#prizeTime").val(null);
                $("#divTime").css("display","none");
            }else{
                $("#prizeTime").attr("lay-verify","required");
                $("#prizeTime").val(dateFormat($("#time").val(), 'yyyy-MM-dd HH:mm:ss'));
                $("#divTime").css("display","block");
            }
        }

        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/activityMember/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#memberForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("memberTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<form class="layui-form" id="memberForm" style="margin-top: 15px;">
    <input type="hidden" id="memberId" name="member.id" value="#(member.id??)"/>
    <input type="hidden" value="#(member.prizeTime??)" id="time"/>
    <div class="layui-form-item">
        <label class="layui-form-label">是否发奖</label>
        <div class="layui-input-inline">
            <select id="prizeFlag" name="member.prizeFlag" lay-verify="required" lay-filter="prizeFlag">
                <option value="0" #(member != null ?(member.prizeFlag == '0' ? 'selected':''):'')>未发奖</option>
                <option value="1" #(member != null ?(member.prizeFlag == '1' ? 'selected':''):'')>已发奖</option>
            </select>
        </div>
    </div>
    <div class="layui-form-item" id="divTime">
        <label class="layui-form-label">发奖时间</label>
        <div class="layui-input-inline">
            <input type="text" id="prizeTime" name="member.prizeTime" value=""  placeholder="请选择发奖时间" class="layui-input">
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label">备注说明</label>
        <div class="layui-input-block">
            <textarea name="member.remark" class="layui-textarea" rows="4" placeholder="请输入备注说明">#(member.remark??)</textarea>
        </div>
    </div>
    <div class="layui-form-item" style="margin-left: 230px; margin-top: 70px;">
        <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
        <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
    </div>
</form>
#end
