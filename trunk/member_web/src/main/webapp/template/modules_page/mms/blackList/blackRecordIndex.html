#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()黑名单操作记录首页#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'recordTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/mms/blackList/recordTable'
	    , method: 'post'
// 	    , height: vipTable.getFullHeight()    //容器高度
	    , where: {blackListId:$('#blackListId').val()}
	    , cols: [[
	          {type: 'numbers', title: '序号', width: 60, unresize:true}
	        , {field: 'dataType', title: '操作类型', unresize:true}
	        ,{field:'', title: '是否有效', align: 'center', unresize: true,width:100,templet:function (d) {
				if(d.isEnable==='1'){
				    return '<span class="layui-badge layui-bg-red">无效</span>';
				}else{
				    return '<span class="layui-badge layui-bg-green">有效</span>';
				}
			}}
// 	        , {field: 'syncData', title: '操作数据', unresize:true}
	        , {field: 'remarks', title: '备注', unresize:true}
	        , {field: '', title: '创建时间', unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{blackListId:$('#blackListId').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <div class="layui-input-inline">
			            <div class="layui-btn-group">
							<input type="hidden" id="blackListId" value="#(blackListId)">
		    			</div>
			        </div>
	    		</div>
			</form>
	    </span>
    </div>
	<div class="layui-row">
		<table id="recordTable" lay-filter="recordTable"></table>
	</div>
</div>
#end