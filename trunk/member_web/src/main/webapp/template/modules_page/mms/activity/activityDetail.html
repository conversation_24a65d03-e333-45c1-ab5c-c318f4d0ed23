#include("/template/common/layout/_page_layout.html")

#@layout()

#define pageTitle()现场抽奖活动名单页面#end

#define css()

#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<form class="layui-form layui-form-pane" action="" id="form">
		<div class="layui-form-item">
			<table class="layui-table" lay-size="sm">
				<thead>
					<tr>
						<th width="8%">序号</th>
						<th width="10%">参加人</th>
						<th width="10%">手机号码</th>
						<th width="10%">抽奖号码</th>
						<th width="14%">是否中奖</th>
						<th width="14%">是否发奖</th>
						<th width="34%">备注</th>
					</tr>
				</thead>
				<tbody id="detailList">
					#for(detail : detailList)
					<tr id="detail-#(for.index+1)">
						<td>
							#(for.index+1)
						</td>
						<td align="left">#(detail.fullName??)</td>
						<td align="left">#(detail.phoneNum??)</td>
						<td align="left">#(detail.prizeNum??)</td>
						<td>
							<select name="detailList[#(for.index+1)].prizeFlag" lay-verify="required">
								<option value="0" #(detail.prizeFlag?? == '0' ? 'selected':'')>未中奖</option>
	            				<option value="1" #(detail.prizeFlag?? == '1' ? 'selected':'')>已中奖</option>
							</select>
						</td>
						<td>
							<select name="detailList[#(for.index+1)].sendFlag" lay-verify="required">
								<option value="0" #(detail.sendFlag?? == '0' ? 'selected':'')>未发奖</option>
	            				<option value="1" #(detail.sendFlag?? == '1' ? 'selected':'')>已发奖</option>
							</select>
						</td>
						<td>
							<input type="hidden" name="detailList[#(for.index+1)].id" value="#(detail.id??)">
							<input type="hidden" name="detailList[#(for.index+1)].activityId" value="#(detail.activityId??activityId)">
							<textarea name="detailList[#(for.index+1)].remark" class="layui-textarea" placeholder="请输入备注">#(detail.remark??)</textarea>
						</td>
					</tr>
					#end
				</tbody>
			</table>
		</div>
		<div class="layui-form-footer">
			<div class="pull-right">
				<input type="hidden" id="detailCount" name="detailCount" value="#(detailList.size()??)">
				<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
				<button type="button" id="saveBtn" class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
				<a id="expBtn" href="#(ctxPath)/mms/activity/export?activityId=#(activityId??)" class="layui-btn">导出抽奖号码</a>
			</div>
		</div>
	</form>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
layui.use(['form','layer','laytpl'], function() {
	var $ = layui.$, form=layui.form,layer=layui.layer, laytpl = layui.laytpl;

	form.on('submit(saveBtn)',function (obj) {
		util.sendAjax ({
			type: 'POST',
			url: '#(ctxPath)/mms/activity/saveDetail',
			data: $(obj.form).serialize(),
			notice: true,
			loadFlag: false,
			success : function(rep){
				if(rep.state==='ok'){
					pop_close();
				}
			},
			complete : function() {
			}
		});
		return false;
	});
});
</script>
#end