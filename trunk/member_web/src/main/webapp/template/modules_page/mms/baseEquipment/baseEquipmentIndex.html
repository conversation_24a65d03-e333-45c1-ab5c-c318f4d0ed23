#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()基地测量设备管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        baseEquipmentLoad(null);

        sd=form.on("submit(search)",function(data){
            baseEquipmentLoad(data.field);
            return false;
        });

        function baseEquipmentLoad(data){
            table.render({
                id : 'baseEquipmentTable'
                ,elem : '#baseEquipmentTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/baseEquipment/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'equipmentNo', title: '设备编号', align: 'center', unresize: true}
                    ,{field:'equipmentType', title: '设备种类', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.equipmentType,'equipment_type','- -') }}</div>"}
                    ,{field:'baseName', title: '基地', align: 'center', unresize: true}
                    ,{field:'buildingName', title: '位置', align: 'center', unresize: true,templet:function (d) {
                            var str="";
                            var buildingName = d.buildingName;
                            var floorName = d.floorName;
                            if(buildingName != null && buildingName != '' && typeof(buildingName)!="undefined"){
                                str += buildingName;
                            }
                            if(floorName != null && floorName != '' && typeof(floorName)!="undefined"){
                                str += floorName;
                            }
                            if(str == ""){
                                return "- -";
                            }else{
                                return str;
                            }
                        }}
                    ,{field:'createDate', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/baseEquipment/form" ;
            pop_show("新增基地测量设备",url,750,550);
        });

        table.on('tool(baseEquipmentTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/baseEquipment/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('baseEquipmentTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/baseEquipment/form?id=" + obj.data.id ;
                pop_show("编辑基地测量设备",url,750,550);
            }
        });

        <!--region Description-->
        /*//批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('gatewayTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/gatewayTypeTable/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {bedTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("gatewayTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });*/
        <!--endregion-->
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:equipmentBase:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("member:equipmentBase:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            设备编号:
            <div class="layui-inline">
                <input id="equipmentNo" name="equipmentNo" class="layui-input">
            </div>
            &nbsp;&nbsp;
            所属基地:
            <div class="layui-inline">
                <select id="baseId" name="baseId" lay-filter="base" lay-search>
                    <option value="">请选择所属基地</option>
                    #for(b : baseList)
                    <option value="#(b.id)">#(b.baseName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:equipmentBase:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table id="baseEquipmentTable" lay-filter="baseEquipmentTable"></table>
</div>
#getDictLabel("equipment_type")
#end