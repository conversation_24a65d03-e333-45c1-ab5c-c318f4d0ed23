#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券记录编辑页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>卡券编号</label>
		<div class="layui-input-block">
			<input type="text" id="rollNumber" name="rollNumber" class="layui-input" lay-verify="required" value="#(model.rollNumber??)" readonly="readonly" placeholder="请输入卡券编号" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
        <label class="layui-form-label">是否有效</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="0" title="是" #if(model!=null && model.isEnable=='0') checked #elseif(model==null) checked #end>
            <input type="radio" name="isEnable" value="1" title="否" #if(model.isEnable??=='1') checked #end>
        </div>
    </div>
	<div class="layui-form-item">
        <label class="layui-form-label">是否使用</label>
        <div class="layui-input-block">
            <input type="radio" name="isUse" value="0" title="否" #if(model!=null && model.isUse=='0') checked #elseif(model==null) checked #end>
            <input type="radio" name="isUse" value="1" title="是" #if(model.isUse??=='1') checked #end>
        </div>
    </div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//时间渲染
	laydate.render({
		elem: '#verifyTime'
		, format: 'yyyy-MM-dd HH:mm:ss'
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/cardRoll/recordSave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("recordTable",{cardRollId:'#(model.cardRollId??)'});
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end