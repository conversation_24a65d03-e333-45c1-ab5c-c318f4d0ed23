#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户回访页面#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
#end

#define content()
<div class="my-btn-box">
<div class="layui-row">
	<form id="frm" class="layui-form" action="" lay-filter="layform" method="post">
		<div class="layui-inline">
			<label class="layui-form-label">所属组织</label>
			<div class="layui-input-inline" style="width:200px;">
				<div id="belongOrgXmSelectTree" class="xm-select-demo"></div>
				<input type="hidden" id="belongOrgId" name="belongOrgId" class="layui-input" value="">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">所属区域</label>
			<div class="layui-input-block" style="width:300px;">
				<input type="hidden" id="province" name="province" value=""/>
				<input type="hidden" id="city" name="city" value=""/>
				<input type="hidden" id="town" name="town" value="">
				<input type="hidden" id="street" name="street" value="">
				<input type="hidden" id="regidentProvinceName" value=""/>
				<input type="hidden" id="regidentCityName" value=""/>
				<input type="hidden" id="regidentCountyName" value="">
				<input type="hidden" id="regidentStreetName" value="">
				<input type="text" id="regidentAddress" name="regidentAddrs" class="layui-input" value="" style="width:300px;display:inline-block;" readonly="readonly">
			</div>
		</div>
		<div class="layui-inline">
			<div class="layui-input-inline">
				<button class="layui-btn layui-btn-sm" style="display: inline-block;" type="button" id="areaClear">清空</button>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">姓名</label>
			<div class="layui-input-inline" style="width:100px;">
				<input id="memberName" name="memberName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">身份证</label>
			<div class="layui-input-inline">
				<input id="idcard" name="idcard" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">手机号</label>
			<div class="layui-input-inline" style="width:120px;">
				<input id="phoneNumber" name="phoneNumber" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">是否会员</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="customerType">
					<option value="">请选择</option>
					<option value="member">会员</option>
					<option value="not_member">非会员</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">是否员工</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="staffType">
					<option value="">请选择</option>
					<option value="staff">员工</option>
					<option value="not_staff">非员工</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">是否参过团</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="touristType">
					<option value="">请选择</option>
					<option value="join">参加过</option>
					<option value="not_join">没参加过</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">接电话情况</label>
			<div class="layui-input-inline">
				<select name="callPass">
					<option value="">请选择</option>
					#getDictList("callPass")
					<option value="#(key)">#(value)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">跟进结果</label>
			<div class="layui-input-inline">
				<select name="followResult">
					<option value="">请选择</option>
					#getDictList("followResult")
					<option value="#(key)">#(value)</option>
					#end
				</select>
			</div>
		</div>
		#shiroHasPermission("member:visit:assignBtn")
		<div class="layui-inline">
			<label class="layui-form-label">分配人</label>
			<div class="layui-input-inline" style="width:100px;">
				<select id="assignUserId" name="createBy" lay-search>
					<option value="">请选择</option>
					#for(u : empUserList)
						<option value="#(u.user_id)">#(u.full_name)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<div class="layui-input-inline" style="width:60px;">
				<button type="button" id="assignBtn" class="layui-btn">分配</button>
			</div>
		</div>
		#end
		<div class="layui-inline">
			<label class="layui-form-label">分配人是否回访</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="isAssignVisit">
					<option value="">请选择</option>
					<option value=yes>是</option>
					<option value="no">否</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">是否回访过</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="isVisit">
					<option value="">请选择</option>
					<option value=yes>是</option>
					<option value="no">否</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">年龄大于</label>
			<div class="layui-input-inline" style="width:100px;">
				<input id="memberAge" name="memberAge" class="layui-input" lay-verify="number" value="0">
			</div>
		</div>
		<div class="layui-inline">
			#if(lacksPermission("member:visit:assignBtn"))
			<div class="layui-input-inline">
				<input type="hidden" id="assignUserId" name="createBy" value="#(assignUserId??)">
			</div>
			#end
			<div class="layui-input-inline">
				<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			</div>
			#shiroHasPermission("member:visit:preciseQueryBtn")
			<div class="layui-input-inline">
				<button type="button" id="preciseQueryBtn" class="layui-btn">精确查询</button>
			</div>
			#end
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="infoTable" lay-filter="infoTable"></table>
</div>
</div>
#getDictLabel("gender","appealType","visitType","followResult","handleResult")
#end

#define js()
<script type="text/html" id="actionBar">
	<a class="layui-btn layui-btn-xs" lay-event="visitRecord">回访记录</a>
	#[[
	{{# if((d.touristType=='join')){ }}
	<a class="layui-btn layui-btn-xs" lay-event="touristRecord">参团记录</a>
	{{# } }}
	]]#
</script>
<script id="regidentAreaTpl" type="text/html">
	<div id="regidentArea"
		 style="width:700px;position:absolute;top:35px;left:0px;z-index:9999;background-color:#F5F5F5;">
		<div class="tabs clearfix">
			<ul>
				<li><a tb="provinceAll" id="regidentProvinceAll" onclick="regidentProvinceAllClick()"
					   class="current">省份</a></li>
				<li><a tb="cityAll" id="regidentCityAll" onclick="regidentCityAllClick()">城市</a></li>
				<li><a tb="countyAll" id="regidentTownAll" onclick="regidentTownAllClick()">区/县</a></li>
				<li><a tb="streetAll" id="regidentStreetAll" onclick="regidentStreetAllClick()">街道/镇/乡</a></li>
			</ul>
		</div>
		<div class="con" style="height:300px;overflow-y: auto;">
			<div class="regidentProvinceAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentCityAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentTownAll">
				<div class="list">

				</div>
			</div>
			<div class="regidentStreetAll">
				<div class="list">

				</div>
			</div>
		</div>
	</div>
</script>
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

	var deptSelect = xmSelect.render({
        el: '#belongOrgXmSelectTree',
        prop: {
            name: 'name',
            value: 'id',
        },
        radio: true,
        filterable: true,//搜索
        clickClose:true,
        direction:'down',
        tips:'请选择所属组织',
        tree: {
            show: true,
            expandedKeys:["54325705-FF63-43DB-9723-FA31E94AF8E3"],
            showFolderIcon: true,
            showLine: true,
            indent: 15,
            lazy: true,
            clickExpand: true,
            strict: false,
        },
        height: '300px'
        , on:function(data){
            var dataArray = data.arr;//data.arr:  当前多选已选中的数据
            if(data.change){//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
                if(dataArray.length>0){
                    $('#belongOrgId').val(dataArray[0].id);
                }else{
                    $('#belongOrgId').val('');
                }
            }
        }
    });
    $.post('#(hrmDomain)/api/customerServiceSalesOrgTreeSelect',{},function (res) {
    	deptSelect.update({
            data:res
        });
    });
    
	//清空区域
	$("#areaClear").on("click", function () {
		$("#province").val("");
		$("#city").val("");
		$("#town").val("");
		$("#street").val("");
		$("#regidentProvinceName").val("");
		$("#regidentCityName").val("");
		$("#regidentCountyName").val("");
		$("#regidentStreetName").val("");
		$("#regidentAddress").val("")
	});
	
	//--------------------------居住区域begin---------------------------
	$('#regidentAddress').on('click', function () {
		//closeIdArea();

		$('#regidentArea').remove();
		var $this = $(this);
		var getTpl = regidentAreaTpl.innerHTML;
		$this.parent().append(getTpl);
		//event.stopPropagation();

		var street = $("#street").val();
		var regidentStreetName = $("#regidentStreetName").val();
		var town = $("#town").val();
		var regidentCountyName = $("#regidentCountyName").val();
		var city = $("#city").val();
		var regidentCityName = $("#regidentCityName").val();
		var province = $("#province").val();
		var regidentProvinceName = $("#regidentProvinceName").val();
		if (street != '' && regidentStreetName != '') {
			$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentStreetLoad(town);
			regidentCountyLoad(city);
			regidentCityLoad(province);
			regidentProvinceLoad();
			$(".con .regidentStreetAll").show().siblings().hide();
			//clickStreet(streetId,streetName);
		} else if (town != '' && regidentCountyName != '') {

			if (town != '') {
				regidentCityLoad(province);
				regidentCountyLoad(city);
				regidentProvinceLoad();
				util.sendAjax({
					type: 'POST',
					url: '#(ctxPath)/area/getAreas',
					data: {pid: town},
					notice: false,
					loadFlag: false,
					success: function (res) {
						if (res.state == 'ok') {
							if (res.data.length > 0) {
								$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								var html = "<ul>";
								$.each(res.data, function (i, item) {
									html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
								});
								html += "</ul>";
								$(".regidentStreetAll .list").append(html);
								//viewStreet(countyId,countyName);
								$(".con .regidentStreetAll").show().siblings().hide();
							} else {
								//无 街道信息
								$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
								$(".con .regidentTownAll").show().siblings().hide();
							}
						}
					},
					complete: function () {
					}
				});
			}
		} else if (city != '' && regidentCityName != '') {
			regidentProvinceLoad();
			regidentCityLoad(province);
			viewRegidentCounty(city, regidentCityName);
		} else if (province != '' && regidentProvinceName != '') {
			regidentProvinceLoad();
			viewRegidentCity(province, regidentProvinceName);
		} else {
			regidentProvinceLoad();
		}

		//去除事件冒泡
		var evt = new Object;
		if (typeof (window.event) == "undefined") {//如果是火狐浏览器
			evt = arguments.callee.caller.arguments[0];
		} else {
			evt = event || window.event;
		}
		evt.cancelBubble = true;
		$('#regidentArea').off('click');
		$('#regidentArea').on('click', function () {
			//event.stopPropagation();
			//去除事件冒泡
			var evt = new Object;
			if (typeof (window.event) == "undefined") {//如果是火狐浏览器
				evt = arguments.callee.caller.arguments[0];
			} else {
				evt = event || window.event;
			}
			evt.cancelBubble = true;
		})
	});

	regidentProvinceLoad = function () {
		util.sendAjax({
			type: 'POST',
			url: '#(ctxPath)/area/getAreas',
			data: {pid: ''},
			notice: false,
			loadFlag: false,
			success: function (res) {
				if (res.state == 'ok') {
					if (res.data.length > 0) {
						$(".regidentProvinceAll .list").empty();
						var html = "<ul>";
						$.each(res.data, function (i, item) {
							html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCity(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
						});
						html += "</ul>";
						$(".regidentProvinceAll .list").append(html);
					}
				}
			},
			complete: function () {
			}
		});
	};

	//点击省事件
	viewRegidentCity = function (province, regidentProvinceName) {
		$("#" + province).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentCityAll").show().siblings().hide();
		$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		//点击省 为省隐藏域赋值 同时清空 市、区、街道等隐藏域的值
		$("#province").val(province);
		$("#regidentProvinceName").val(regidentProvinceName);
		$("#city").val("");
		$("#regidentCityName").val("");
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCityLoad(province);
	};

	//加载市
	regidentCityLoad = function (province) {
		if (province != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: province},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentCityAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function (i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentCounty(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentCityAll .list").append(html);
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	//点击市事件
	viewRegidentCounty = function (city, RegidentCityName) {
		$("#" + city).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentTownAll").show().siblings().hide();
		$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#city").val(city);
		$("#regidentCityName").val(RegidentCityName);
		$("#town").val("");
		$("#regidentCountyName").val("");
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentCountyLoad(city);
	};

	//加载区/县
	regidentCountyLoad = function (city) {
		if (city != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: city},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentTownAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function (i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="viewRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentTownAll .list").append(html);
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	//点击区/县事件
	viewRegidentStreet = function (town, regidentCountyName) {
		$("#" + town).addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentStreetAll").show().siblings().hide();
		$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$("#town").val(town);
		$("#regidentCountyName").val(regidentCountyName);
		$("#street").val("");
		$("#regidentStreetName").val("");
		regidentStreetLoad(town);
	};

	//加载街道/镇/乡
	regidentStreetLoad = function (town) {
		if (town != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: town},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$(".regidentStreetAll .list").empty();
							var html = "<ul>";
							$.each(res.data, function (i, item) {
								html += '<li><a href="javascript:void(0)" id="' + item.id + '" onclick="clickRegidentStreet(\'' + item.id + '\',\'' + item.name + '\')">' + item.name + '</a></li>';
							});
							html += "</ul>";
							$(".regidentStreetAll .list").append(html);
						} else {
							//无 街道信息
							clickRegidentStreet('', '');
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	clickRegidentStreet = function (street, regidentStreetName) {
		$("#street").val(street);
		$("#regidentStreetName").val(regidentStreetName);
		var regidentProvinceName = $("#regidentProvinceName").val();
		var regidentCityName = $("#regidentCityName").val();
		var regidentCountyName = $("#regidentCountyName").val();
		var regidentStreetName = $("#regidentStreetName").val();
		var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
		$("#regidentAddress").val(add);
		$('#regidentArea').remove();
	};

	regidentProvinceAllClick = function () {
		//$(".con .provinceAll").show();
		$("#regidentProvinceAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
		$(".con .regidentProvinceAll").show().siblings().hide();
	};

	regidentCityAllClick = function () {
		// $(".con .cityAll").show();
		if ($("#province").val() != '') {
			$("#regidentCityAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCityLoad($("#province").val());
			$(".con .regidentCityAll").show().siblings().hide();
		}
	};

	regidentTownAllClick = function () {
		if ($("#city").val() != '') {
			$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
			regidentCountyLoad($("#city").val());
			$(".con .regidentTownAll").show().siblings().hide();
		}
	};

	regidentStreetAllClick = function () {
		if ($("#town").val() != '') {
			util.sendAjax({
				type: 'POST',
				url: '#(ctxPath)/area/getAreas',
				data: {pid: $("#town").val()},
				notice: false,
				loadFlag: false,
				success: function (res) {
					if (res.state == 'ok') {
						if (res.data.length > 0) {
							$("#regidentStreetAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							regidentStreetLoad($("#town").val());
							$(".con .regidentStreetAll").show().siblings().hide();
						} else {
							//无 街道信息 显示区/县信息
							$("#regidentTownAll").addClass("current").closest("li").siblings("li").find("a").removeClass("current");
							//countyLoad(cityId);
							$(".con .regidentTownAll").show().siblings().hide();
						}
					}
				},
				complete: function () {
				}
			});
		}
	};

	$('body').on('click', function () {
		closeRegidentArea();
	});

	//关闭区域选择器
	closeRegidentArea = function () {
		if (typeof ($('#regidentArea').html()) != 'undefined') {
			var regidentProvinceName = $("#regidentProvinceName").val();
			var regidentCityName = $("#regidentCityName").val();
			var regidentCountyName = $("#regidentCountyName").val();
			var regidentStreetName = $("#regidentStreetName").val();
			var add = regidentProvinceName + " " + regidentCityName + " " + regidentCountyName + " " + regidentStreetName;
			$("#regidentAddress").val(add);
		}
		//alert(1);
		$('#regidentArea').remove();
	}
	//-------------------------居住区域end----------------------------
	
	function consultLoad(data){
		table.render({
			id : 'infoTable'
			,elem : '#infoTable'
			,method : 'POST'
			,where : data
			,url : '#(ctxPath)/mms/customer/pageTable'
			,height: 'full-170'    //容器高度
			,cols: [[
				{type:'checkbox'}
				,{type: 'numbers', title: '序号', width: 60, unresize:true}
				,{field:'',title:'客户类别', align: 'center',width:90,unresize:true,templet:
					"<div>{{ d.customerType=='member'?'<span class='layui-badge layui-bg-green'>会员</span>'"+
					":d.customerType=='not_member'?'<span class='layui-badge layui-bg-cyan'>非会员</span>'"+
					":'- -' }}</div>"
				}
				,{field:'',title:'员工类别', align: 'center',width:90,unresize:true,templet:
					"<div>{{ d.staffType=='staff'?'<span class='layui-badge layui-bg-green'>员工</span>'"+
					":d.staffType=='not_staff'?'<span class='layui-badge layui-bg-cyan'>非员工</span>'"+
					":'- -' }}</div>"
				}
				,{field:'memberName', title: '姓名', align: 'center', width: 100, unresize: true}
				,{field:'', title: '性别', align: 'center', width: 60, unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
				,{field:'birthday', title: '出生日期', align: 'center', width: 120, unresize: true}
				,{field:'phoneNumber', title: '手机号', align: 'center', width: 120, unresize: true}
				#shiroHasPermission("member:visit:assignBtn")
				,{field:'assignName', title: '分配人', align: 'center', width: 100, unresize: true}
				,{field:'assignTime', title: '分配时间', align: 'center', width: 160, unresize: true}
				#end
				,{field:'assignVisitCount', title: '分配人回访次数', align: 'center', width: 130, unresize: true}
				,{field:'visitCount', title: '回访总次数', align: 'center', width: 100, unresize: true}
				,{field:'belongOrgName', title: '所属组织', align: 'center', width: 200, unresize: true}
				,{field:'provinceName', title: '省', align: 'center', width: 100, unresize: true}
				,{field:'cityName', title: '市/县', align: 'center', width: 100, unresize: true}
				,{field:'townName', title: '镇/区', align: 'center', width: 100, unresize: true}
				,{field:'streetName', title: '街道', align: 'center', width: 100, unresize: true}
				,{field:'address', title: '地址', align: 'center', width: 200, unresize: true}
				,{fixed:'right', title: '操作', width:200, align: 'center', unresize: true, toolbar: '#actionBar'}
			]]
			, page : true
			, limit : 10
			, limits : [10,20,30,40]
			, loading: true
		    , done: function (res, curr, count) {
		    }
		});
		// 表格绑定事件
	    table.on('tool(infoTable)',function (obj) {
	        if(obj.event==="visitRecord"){//回访记录按钮事件
	        	pop_show('回访记录','#(ctxPath)/mms/customer/visitCustomer?id='+obj.data.id,'','');
	        }else if(obj.event==="touristRecord"){//参团记录按钮事件
				pop_show('参团记录','#(ctxPath)/mms/customer/touristRecord?idcard='+obj.data.idcard,'','');
			}
	    });
	};
	
	#if(lacksPermission("member:visit:assignBtn"))
		consultLoad({createBy:'#(assignUserId??)'});
	#else
		consultLoad(null);
	#end
	
	// 精确查询
	$("#preciseQueryBtn").click(function(){
		$(this).blur();
		var url = "#(ctxPath)/mms/customer/preciseQueryIndex";
		layerShow("精确查询",url,'100%','100%');
	});
	
	//批量获取被选中的数据，返回id
	getCheckTableData = function(){
		var cardCheckStatus = table.checkStatus('infoTable');
		// 获取选择状态下的数据
		return cardCheckStatus.data;
	}
	
	//批量分配
	$("#assignBtn").click(function(){
		var jsonData = getCheckTableData();
		var assignUserId = $('#assignUserId').val();
		if(jsonData == null || jsonData == ''){
			layer.msg('请勾选数据', function () {});
			return;
		}
		if(assignUserId == null || assignUserId == ''){
			layer.msg('请选择分配人员', function () {});
			return;
		}
		layer.confirm("确定分配?",function(index){
			util.sendAjax({
				url:"#(ctxPath)/mms/customer/assignSave",
				type:'post',
				data:{"registerData":JSON.stringify(jsonData), "assignUserId":assignUserId},
				notice:true,
				success:function(returnData){
					if(returnData.state==='ok'){
						table.reload('infoTable');
					}
					layer.close(index);
				}
			});
		});
	});

	form.on("submit(search)",function(data){
		consultLoad(data.field);
		return false;
	});
});
</script>
#end