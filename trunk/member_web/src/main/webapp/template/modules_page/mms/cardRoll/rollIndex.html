#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券首页#end

#define css()
<style>
	.layui-table-cell {
		padding: 0 3px;
	}
</style>
#end

#define content()
<div class="layui-form-item">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="margin-top:15px;">
	<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
		<div class="layui-inline">
			<label class="layui-form-label">卡券编号</label>
			<div class="layui-input-inline">
				<input id="rollCode" name="rollCode" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">卡券名称</label>
			<div class="layui-input-inline">
				<input id="rollName" name="rollName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("crm:cardRoll:addBtn")
				<a type="button" id="addBtn" class="layui-btn btn-default">添加</a>
			#end
		</div>
	</div>
	</form>
	<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
		<div class="layui-inline">
			<label class="layui-form-label" style="width: 90px;">卡券记录编号</label>
			<div class="layui-input-inline">
				<input id="rollNumber" name="rollNumber" class="layui-input">
			</div>
		</div>

 		<div class="layui-inline">
 			<input type="hidden" id="cardRollId" value="">
 			<button type="button" id="search2" class="layui-btn">查询</button>
 		</div>
	</div>
</div>
<div class="layui-row layui-col-space10">
	<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
		<fieldset class="layui-elem-field">
			<legend>卡券列表</legend>
			<table id="rollTable" lay-filter="rollTable"></table>
		</fieldset>
	</div>
	<div class="layui-col-xs6 layui-col-sm6 layui-col-md6 layui-col-lg6">
		<fieldset class="layui-elem-field">
			<legend>记录列表</legend>
			<table id="recordTable" lay-filter="recordTable"></table>
		</fieldset>
	</div>
</div>
#getDictLabel("gender")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRoll:releaseBtn")
	#[[
	{{#if(d.deployStatus==='1'){}}
		<a class="layui-btn layui-btn-xs" lay-event="release">发布</a>
	{{#}}}
	]]#
	#end

	#[[
	{{#if(d.typeCode==='goods_type'){}}
	<!--<a class="layui-btn layui-btn-xs" lay-event="addProduct">兑换商品</a>-->
	{{#}}}
	]]#

	#shiroHasPermission("crm:cardRoll:editBtn")
	#[[
	{{#if(d.deployStatus==='1'){}}
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	{{#}else{}}
		<a class="layui-btn layui-btn-xs" lay-event="see">查看</a>
		<a class="layui-btn layui-btn-xs" lay-event="addNum">追加数量</a>
	{{#}}}
	]]#
	#end
	#shiroHasPermission("crm:cardRoll:printBtn")
	#[[
	{{#if(d.deployStatus==='0'){}}
		<a class="layui-btn layui-btn-xs" lay-event="print">打印</a>
	{{#}}}
	]]#
	#end

	#shiroHasPermission("crm:cardRoll:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end


</div>
</script>
<script type="text/html" id="recordTableBar">
	<div class="layui-btn-group">
		<a class="layui-btn layui-btn-xs" lay-event="startPrintBtn">以此开始打印</a>
		<a class="layui-btn layui-btn-xs" lay-event="printBtn">打印</a>
		#shiroHasPermission("crm:cardRoll:recordPrintBtn")
		<a class="layui-btn layui-btn-xs" lay-event="print">打印记录</a>
		#end
		#shiroHasPermission("crm:cardRoll:recordEditBtn")
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
		#end
		#shiroHasPermission("crm:cardRoll:recordDelBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
		#end
	</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		function cardRollLoad(data){
			table.render({
				id : 'rollTable'
				,elem : '#rollTable'
				,method : 'POST'
				,where : data
				,url : '#(ctxPath)/mms/cardRoll/pageTable'
				,cellMinWidth: 80
				,width:($(document).width()/2)-5
				,height:$(document).height()*0.8
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field:'typeName', title: '类型', align: 'center', width: 130, unresize: true}
					,{field:'rollCode', title: '编号', align: 'center', width: 100, unresize: true}
					,{field:'rollName', title: '名称', align: 'center', width: 200, unresize: true}
					,{field:'rollQuantity', title: '数量', align: 'center', width: 90, unresize: true}
					,{field:'rollValue', title: '面额值', align: 'center', width: 90, unresize: true}
					,{field:'isBingUse', title: '是否需要绑定使用', align: 'center', width: 120, unresize: true,templet:function (d) {
							if(d.isBingUse=='1'){
								return '是';
							}else if(d.isBingUse=='0'){
								return '否';
							}
						}}
					,{field:'validType', title: '有效期类型', align: 'center', width: 310, unresize: true,templet:function (d) {
							if(d.validType=='1'){
								return '指定有效期时间范围';
							}else if(d.validType=='2'){
								return '绑定开始'+d.validDays+'天有效'+'，最大有效日期：'+dateFormat(d.finalValidDate,'yyyy-MM-dd');
							}
						}}
					,{field:'', title: '有效开始日期', align: 'center', width: 110, unresize: true, templet:function (d) {
							if(d.validType=='1'){
								return dateFormat(d.validStartDate,'yyyy-MM-dd');
							}else{
								return '';
							}
						}}
					,{field:'', title: '有效结束日期', align: 'center', width: 110, unresize: true, templet:function (d) {
							if(d.validType=='1'){
								return dateFormat(d.validEndDate,'yyyy-MM-dd');
							}else{
								return '';
							}
						}}
					,{field: '', title: '是否有效', width: 80,unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge layui-bg-green'>有效</span>':d.isEnable == '1'? '<span class='layui-badge'>无效</span>':'- -'}}</div>"}
					,{field:'deployTime', title: '发布时间', align: 'center', width: 170, unresize: true}
					,{field:'', title: '发布状态', align: 'center', width: 100, unresize: true,templet:"<div>{{d.deployStatus == '0'? '<span class='layui-badge layui-bg-green'>已发布</span>':d.deployStatus == '1'? '<span class='layui-badge'>未发布</span>':'- -'}}</div>"}
					,{fixed:'right', title: '操作', width:210, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 15
				,limits : [15,25,35,45]
				, loading: true
			    , done: function (res, curr, count) {
					// 单击行触发回调函数
					clickRow(this , table , function(data) {
						$('#cardRollId').val(data.id);

						recordLoad(data.id);
					});
			    }
			});
			table.on('tool(rollTable)',function(obj){
				if(obj.event==="release"){//发布按钮事件
		        	//发布操作
		    		layer.confirm('确认发布？', {icon:3, title:'提示'}, function(index){
		                util.sendAjax ({
		                    type: 'POST',
		                    url: '#(ctxPath)/mms/cardRoll/save',
		                    data: {id:obj.data.id, deployStatus:'0', rollCode:obj.data.rollCode, rollQuantity:obj.data.rollQuantity},
		                    loadFlag: true,
		                    success : function(rep){
		                    	tableReload("rollTable",null);
		                    },
		                    complete : function() {
		        		    }
		                });
						layer.close(index);
		            });
		        }else if(obj.event==="edit"){//编辑按钮事件
					layerShow('编辑','#(ctxPath)/mms/cardRoll/edit?id='+obj.data.id,'100%','100%');
				}else if(obj.event==="see"){//编辑按钮事件
					layerShow('编辑','#(ctxPath)/mms/cardRoll/edit?id='+obj.data.id+"&formType=see",'100%','100%');
		        }else if(obj.event==="print"){//打印按钮事件
		        	//打印操作
		    		layer.confirm('确认打印?', {icon:3, title:'提示'}, function(index){
		                util.sendAjax ({
		                    type: 'POST',
		                    url: '#(ctxPath)/mms/cardRoll/print',
		                    data: {id:obj.data.id},
		                    loadFlag: true,
		                    notice: false,
		                    success : function(rep){
		                    	if(rep.state=='ok'){
		                    		if(rep.templateName!=null && rep.templateName!=''){
		                    			if(rep.printData!=null && rep.printData!=''){
				                    		print(rep.templateName, rep.printData);
			                    		}else{
			                    			layer.msg('打印参数不能为空!',{icon:5});
			                    		}
		                    		}else{
		                    			layer.msg('模板名称不能为空!',{icon:5});
		                    		}
		                    	}
		                    },
		                    complete : function() {
		        		    }
		                });
						layer.close(index);
		            });

				}else if(obj.event==="del"){//作废按钮事件
		        	//作废操作
		    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
		                util.sendAjax ({
		                    type: 'POST',
		                    url: '#(ctxPath)/mms/cardRoll/delete',
		                    data: {id:obj.data.id, delFlag:'1'},
		                    loadFlag: true,
		                    success : function(rep){
		                    	tableReload("rollTable",null);
		                    },
		                    complete : function() {
		        		    }
		                });
						layer.close(index);
		            });
		        }else if(obj.event==='addProduct'){
					layerShow('设置兑换商品','#(ctxPath)/mms/cardRoll/rollProductform?typeId='+obj.data.id,'100%','100%');
				}else if(obj.event==='addNum'){
					layer.open({//parent表示打开二级弹框
						type: 1,
						title: "查看详情",
						shadeClose: false,
						shade: 0.5,
						btn: ['确定', '关闭'],
						maxmin: false, //开启最大化最小化按钮
						area:['600px;','400px;'],
						content: "<form class=\"layui-form layui-form-pane\" lay-filter=\"layform\" id=\"noticeForm\" style='padding: 5px;'>\n" +
										"            <div class=\"layui-form-item\" style='margin-top: 20px;'>\n" +
										"                <label class=\"layui-form-label\"><font color='red'>*</font>追加数量</label>\n" +
										"                <div class=\"layui-input-block\" >\n" +
										"                    <input class=\"layui-input\"  value='' name=\"num\" id=\"num\" autocomplete=\"off\">\n" +
										"                </div>\n" +
										"            </div>\n" +
								"</form>",
								cancel: function(){
						},
						end : function(){
						},yes: function(index, layero){
							let num=$("#num").val();
							if(num==''){
								layer.msg('追加数量必须填写', {icon: 2, offset: 'auto'});
								return false;
							}
							var r = /^\+?[1-9][0-9]*$/;　　//正整数
							if(!r.test(num)){
								layer.msg('追加数量请填写正整数', {icon: 2, offset: 'auto'})
								return false;
							}
							layer.load();

							util.sendAjax ({
								type: 'POST',
								url: '#(ctxPath)/mms/cardRoll/addRollRecord',
								data: {'rollId':obj.data.id, 'num':num},
								loadFlag: true,
								success : function(rep){
									recordLoad(obj.data.id);
									layer.closeAll('loading');
								},
								complete : function() {
								},
								unSuccess:function () {
									layer.closeAll('loading');
								}
							});

							layer.close(index);
							return false;
						}

					});
				}
			});
		};

		
		function recordLoad(cardRollId){
			table.render({
			    id: 'recordTable'
			    , elem: '#recordTable'                  //指定原始表格元素选择器（推荐id选择器）
//	 		    , even: true //开启隔行背景
			    , url: '#(ctxPath)/mms/cardRoll/recordPageTable'
			    , method: 'post'
			    , where: {cardRollId:cardRollId}
// 			    , where: {cardRollId:cardRollId,minValue:$('#minValue').val(),maxValue:$('#maxValue').val()}
				, cellMinWidth: 80
				, width:($(document).width()/2)-5
				, height:$(document).height()*0.8
			    , cols: [[                  //标题栏
// 			    	{type:'checkbox'}
			        {type: 'numbers', title: '序号', width: 50, unresize:true}
					, {field: 'rollNumber', title: '卡券编号', unresize:true}
					, {field: 'rollValue', title: '面额值', width: 60, unresize:true}
					, {field: 'balanceRollValue', title: '剩余面额值', width: 80, unresize:true}
					, {field: '', title: '是否有效', width: 70,unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge layui-bg-green'>有效</span>':d.isEnable == '1'? '<span class='layui-badge'>无效</span>':'- -'}}</div>"}
					, {field: '', title: '是否使用', width: 70,unresize:true,templet:"<div>{{d.isUse == '0'? '<span class='layui-badge layui-bg-green'>未使用</span>':d.isUse == '1'? '<span class='layui-badge'>已使用</span>':'- -'}}</div>"}
					, {field: 'bindingInfo', title: '是否绑定', width: 70, unresize:true,templet:function (d) {
							if(d.bindingInfo==null || d.bindingInfo==undefined){
								return "<span class='layui-badge'>未绑定</span>";
							}else{
								var title='绑定会员卡：'+d.bindingInfo.cardNumber+'，会员卡持有人：'+d.bindingInfo.cardMember+'，绑定时间：'+dateFormat(d.bindingInfo.bindingTime,"yyyy-MM-dd HH:mm:ss")+'，绑定人：'+d.bindingInfo.bindingUser;
								return "<span class='layui-badge layui-bg-green' style='cursor:pointer;' title='"+title+"'>已绑定</span>";
							}
						}}
					, {field: 'printNum', title: '打印次数', width: 65, unresize:true}
			        , {fixed: 'right', title: '操作', width: 270, align: 'center', toolbar: '#recordTableBar'} //这里的toolbar值是模板元素的选择器
			    ]]
			    , page: true
			    , limit : 15
				, limits : [15,25,35,45]
			    , loading: true
			    , done: function (res, curr, count) {
			    }
			});
			// 表格绑定事件
		    table.on('tool(recordTable)',function (obj) {
		    	if(obj.event==="print"){//打印记录按钮事件
		        	pop_show('打印记录','#(ctxPath)/mms/cardRoll/printRecord?id='+obj.data.id,'400','400');
				}else if(obj.event==="startPrintBtn"){//
					/*layer.confirm('确认从此处开始打印?', {icon:3, title:'提示'}, function(index){
						util.sendAjax ({
							type: 'POST',
							url: '#(ctxPath)/mms/cardRoll/startPrint',
							data: {id:obj.data.cardRollId,"rollNumber":obj.data.rollNumber},
							loadFlag: true,
							notice: false,
							success : function(rep){
								if(rep.state=='ok'){
									if(rep.templateName!=null && rep.templateName!=''){
										if(rep.printData!=null && rep.printData!=''){
											print(rep.templateName, rep.printData);
										}else{
											layer.msg('打印参数不能为空!',{icon:5});
										}
									}else{
										layer.msg('模板名称不能为空!',{icon:5});
									}
								}
							},
							complete : function() {
							}
						});
						layer.close(index);
					});*/
					/*layer.prompt({title: '请输入结束打印序号，并确认',value: 'xx', formType: 0}, function(text, index){
						console.log(text);
						var reg=/^[1-9][0-9]+$/gi;
						if(!reg.test(text)){
							layer.msg('请输入数字!',{icon:5});
							return false;
						}
						return false
						util.sendAjax ({
							type: 'POST',
							url: '#(ctxPath)/mms/cardRoll/startPrint',
							data: {id:obj.data.cardRollId,"rollNumber":obj.data.rollNumber,"endNum":text},
							loadFlag: true,
							notice: false,
							success : function(rep){
								if(rep.state=='ok'){
									if(rep.templateName!=null && rep.templateName!=''){
										if(rep.printData!=null && rep.printData!=''){
											print(rep.templateName, rep.printData);
										}else{
											layer.msg('打印参数不能为空!',{icon:5});
										}
									}else{
										layer.msg('模板名称不能为空!',{icon:5});
									}
								}
							},
							complete : function() {
							}
						});
						layer.close(index);
					});*/

					layer.prompt({

						formType: 0,

						title: '请输入结束打印序号，并确认',

						//area: ['500px', '150px'],

						//btnAlign: 'c',

						yes: function(index, layero){

							// 获取文本框输入的值

							var text = layero.find(".layui-layer-input").val();
							if (text) {

								if(!/^[0-9]*$/.test(text)){
									layer.msg('请输入数字!',{icon:5});
									return false;
								}

							}
							util.sendAjax ({
								type: 'POST',
								url: '#(ctxPath)/mms/cardRoll/startPrint',
								data: {id:obj.data.cardRollId,"rollNumber":obj.data.rollNumber,"endNum":text},
								loadFlag: true,
								notice: false,
								success : function(rep){
									if(rep.state=='ok'){
										if(rep.templateName!=null && rep.templateName!=''){
											if(rep.printData!=null && rep.printData!=''){
												print(rep.templateName, rep.printData);
											}else{
												layer.msg('打印参数不能为空!',{icon:5});
											}
										}else{
											layer.msg('模板名称不能为空!',{icon:5});
										}
									}
								},
								complete : function() {
								}
							});
							layer.close(index);

						}

					});

		    	}else if(obj.event==="edit"){//编辑按钮事件
		        	pop_show('编辑','#(ctxPath)/mms/cardRoll/recordEdit?id='+obj.data.id,'400','300');
		        }else if(obj.event==="del"){//作废按钮事件
		        	//作废操作
		    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
		                util.sendAjax ({
		                    type: 'POST',
		                    url: '#(ctxPath)/mms/cardRoll/recordDelete',
		                    data: {id:obj.data.id, delFlag:'1'},
		                    loadFlag: true,
		                    success : function(rep){
		                    	tableReload("recordTable",{cardRollId:obj.data.cardRollId});
// 		                    	tableReload("recordTable",{cardRollId:obj.data.cardRollId,minValue:$('#minValue').val(),maxValue:$('#maxValue').val()});
		                    },
		                    complete : function() {
		        		    }
		                });
						layer.close(index);
		            });
				}else if(obj.event==="printBtn"){//作废按钮事件
					//打印操作
					layer.confirm('确认打印?', {icon:3, title:'提示'}, function(index){
						util.sendAjax ({
							type: 'POST',
							url: '#(ctxPath)/mms/cardRoll/printRollRecord',
							data: {id:obj.data.id},
							loadFlag: true,
							notice: false,
							success : function(rep){
								if(rep.state=='ok'){
									if(rep.templateName!=null && rep.templateName!=''){
										if(rep.printData!=null && rep.printData!=''){
											print(rep.templateName, rep.printData);
										}else{
											layer.msg('打印参数不能为空!',{icon:5});
										}
									}else{
										layer.msg('模板名称不能为空!',{icon:5});
									}
								}
							},
							complete : function() {
							}
						});
						layer.close(index);
					});
				}
		    });
		};
		
		//批量获取被选中的数据，返回id
		getCheckTableData = function(){
			var checkStatus = table.checkStatus('recordTable');
			// 获取选择状态下的数据
			return checkStatus.data;
		}
		
		//交易数量只能输入正整数
		valueKeyupFun = function(obj) {
		    if(obj.value.length == 1) {
		        obj.value = obj.value.replace(/[^1-9]/g, '')
		    } else {
		        obj.value = obj.value.replace(/\D/g, '')
		    }
		}
		
		valueKeydownFun = function(obj) {
		    if(obj.value.length == 1) {
		        obj.value = obj.value.replace(/[^1-9]/g, '')
		    } else {
		        obj.value = obj.value.replace(/\D/g, '')
		    }
		}

		valuePasteFun = function(obj) {
		    if(obj.value.length == 1) {
		        obj.value = obj.value.replace(/[^1-9]/g, '');
		    } else {
		        obj.value = obj.value.replace(/\D/g, '');
		    }
		}
		
		cardRollLoad(null);
		
		$("#minValue").keyup(function(){
			valueKeyupFun(this);
		});

		$("#minValue").keydown(function(){
			valueKeydownFun(this);
		});

		$("#minValue").bind("paste",function(){
			valuePasteFun(this);
		});
		
		$("#maxValue").keyup(function(){
			valueKeyupFun(this);
		});

		$("#maxValue").keydown(function(){
			valueKeydownFun(this);
		});

		$("#maxValue").bind("paste",function(){
			valuePasteFun(this);
		});
		
		form.on("submit(search)",function(data){
			cardRollLoad(data.field);
			return false;
		});
		
		//记录查询按钮点击事件
		$('#search2').on('click', function() {
			tableReload("recordTable",{cardRollId:$('#cardRollId').val(),"rollNumber":$("#rollNumber").val()});
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			layerShow('添加','#(ctxPath)/mms/cardRoll/add','100%','100%');
		});
		
		//批量核销按钮点击事件
		$('#batchVerifyBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/mms/cardRoll/verifyAdd','','');
		});
		
		//批量登记按钮点击事件
		$('#batchPayBtn').on('click', function() {
			
		});

        print=function(templateName,data){
//         	console.log('data==='+data);
            var param={"TemplateName":templateName,"PrinterName":"","Data":data};
            layer.load();
            $.ajax({
                url: "http://127.0.0.1:8050/LabelPrint?Type=LabelPrint",
                type: "POST",
                dataType: "json",
                data: JSON.stringify(param),
                contentType: 'application/json',
                success: function (result) {
                    layer.closeAll('loading');
                },
                error: function (e) {
                    layer.closeAll('loading');
                    layer.msg( e.responseText == null ? "未启动打印服务" : e.responseText, {icon: 2, offset: 'auto'});
                }
            });
        }
	});
</script>
#end