#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案管理#end

#define css()
#end

#define js()
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		memberLoad(null);

		sd=form.on("submit(search)",function(data){
			memberLoad(data.field);
			return false;
		});

		function memberLoad(data){
			table.render({
				id : 'memberTable'
				,elem : '#memberTable'
				,method : 'POST'
				,where : data
				,height:$(document).height()*0.8
				,limit : 10
				,limits : [10,20,30,40]
				,url : '#(ctxPath)/mms/member/findListPage'
				,cellMinWidth: 80
				,cols: [[
					{type:'checkbox'},
					{type: 'numbers', width:100, title: '序号',unresize:true}
					,{field:'fullName', title: '姓名', align: 'center', unresize: true}
					,{field:'gender', title: '性别', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
					#shiroHasPermission("member:idcardSee")
					,{field:'idcard', title: '身份证号', align: 'center', unresize: true}
					#end
					,{field:'birthday', title: '出生日期', align: 'center', unresize: true}
					#shiroHasPermission("member:telephoneSee")
					,{field:'telephone', title: '电话', align: 'center', unresize: true}
					#end
					,{field:'occupation', title: '职位', align: 'center', unresize: true}
					,{field:'address', title: '地址', align: 'center', unresize: true}
					,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};
		// 添加
		$("#add").click(function(){
			$(this).blur();
			var url = "#(ctxPath)/mms/member/form" ;
			pop_show("新增会员档案",url,800,500);
		});

		table.on('tool(memberTable)',function(obj){
			if (obj.event === 'del') {
				layer.confirm("确定要作废吗?",function(index){
					util.sendAjax ({
						type: 'POST',
						url: '#(ctxPath)/mms/member/delete',
						notice: true,
						data: {id:obj.data.id},
						loadFlag: true,
						success : function(rep){
							if(rep.state=='ok'){
								tableReload('memberTable',null);
							}
							layer.close(index);
						},
						complete : function() {
						}
					});
				});
			}else if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/member/form?id=" + obj.data.id ;
				pop_show("编辑会员档案",url,800,500);
			}
// 			else if(obj.event === 'visit'){
// 				var url = "#(ctxPath)/mms/memberVisit/index?memberId=" + obj.data.id ;
// 				pop_show("会员回访",url,'','');
// 			}
		});

		//批量获取被作废数据
		getCheckTableData = function(){
			var memberCheckStatus = table.checkStatus('memberTable');
			// 获取选择状态下的数据
			return memberCheckStatus.data;
		}

		//批量作废
		$("#batchDel").click(function(){
			layer.confirm("确定批量作废吗?",function(index){
				var jsonData=getCheckTableData();
				if(jsonData == null || jsonData == ''){
					layer.msg('请勾选作废数据', function () {});
					return;
				}
				var url = "#(ctxPath)/mms/member/batchDel";
				util.sendAjax ({
					type: 'POST',
					url: url,
					data: {memberData:JSON.stringify(jsonData)},
					notice: true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							tableReload("memberTable",null);
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		});
	});
</script>
#shiroHasPermission("member:archive:visitBtn")
<!-- <a class="layui-btn layui-btn-xs" lay-event="visit">回访</a> -->
#end
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("member:archive:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:archive:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<!--图片展示替换模板-->
<script type="text/html" id="imgTpl">
	<img src="#(imgPath){{ d.headPic }}" height="30px">
</script>
#end

#define content()
<div>
	<div class="demoTable">

		<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
			姓名:
			<div class="layui-inline">
				<input id="fullName" name="fullName" class="layui-input">
			</div>
			&nbsp;&nbsp;
			身份证:
			<div class="layui-inline">
				<input id="idcard" name="idcard" class="layui-input">
			</div>
			<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
		</form>
		#shiroHasPermission("member:archive:addBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
		#end
		#shiroHasPermission("member:archive:batchDelBtn")
		<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>
		#end
	</div>
	<table id="memberTable" lay-filter="memberTable"></table>
</div>
#getDictLabel("gender")
#end