#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人脸模板审核#end

#define css()
<style>
    img{
        max-height: 300px;
    }
</style>

#end

#define js()
<script type="text/javascript">
    layui.use(['form','upload'],function(){
        var form = layui.form;
        var $ = layui.$;
        var upload = layui.upload;

        //审核
        form.on('submit(confirmBtn)', function(){
            $("#confirmBtn").attr("disabled",true);
            $("#confirmBtn").addClass("layui-btn-disabled");
            util.sendAjax ({
                type: 'POST',
                url: '#(ctxPath)/mms/memberface/examine',
                data: $("#faceInfoForm").serialize(),
                notice: true,
                loadFlag: true,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.layui.table.reload('faceTable');
                    }
                },
                complete : function() {
                    $("#confirmBtn").attr("disabled",false);
                    $("#confirmBtn").removeClass("layui-btn-disabled");
                }
            });
            return false;
        });
    });
</script>
#end

#define content()
<body class="v-theme">
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
    <div class="layui-row" style="margin-bottom:50px;">
        <form class="layui-form layui-form-pane" lay-filter="layform" id="faceInfoForm">
            <div class="layui-row">
                <div class="layui-col-xs6" style="padding-right: 20px;">
                    <div class="layui-form-item">
                        <label class="layui-form-label">姓名</label>
                        <div class="layui-input-block">
                            <input type="text" autocomplete="off" class="layui-input" value="#(member != null ?member.fullName:'')" readonly>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">身份证</label>
                        <div class="layui-input-block">
                            <input type="text" class="layui-input" value="#(member != null ? member.idcard:'')" readonly>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><span>*</span>人脸审核</label>
                        <div class="layui-input-block">
                            <select name="face.verifyFlag" lay-verify="required">
                                <option value="">请选择审核状态</option>
                                <option value="1" #(face != null ?(face.verifyFlag == '1' ? 'selected':''):'')>审核通过</option>
                                <option value="2" #(face != null ?(face.verifyFlag == '2' ? 'selected':''):'')>审核不通过</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">审核说明</label>
                        <div class="layui-input-block">
                            <textarea placeholder="请输入审核说明" name="face.verifyDescribe" rows="5" class="layui-textarea">#(face != null ? face.verifyDescribe:'')</textarea>
                        </div>
                    </div>
                </div>
                <div class="layui-col-xs6">
                    <div class="layui-upload" style="text-align: center;">
                        <div class="layui-upload-list">
                            <img class="layui-upload-img" id="profilePhotoImg" width="200px" src="#(face ? (picture ? base + picture :'') : '')">
                            <p id="profilePhotoText"></p>
                            <input type="hidden" id="uploadPath" name="picturePath" value="">
                        </div>
                    </div>
                </div>
            </div>

            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="face.id" value="#(face.id??)"/>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
</body>
#end