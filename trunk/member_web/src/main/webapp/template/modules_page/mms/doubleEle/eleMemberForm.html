#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()微信用户详情#end

#define css()
#end

#define content()
<div style="margin: 15px;" >
    <div class="layui-contents">
        <form class="layui-form" action="" method="post" id="memberPrizeForm">
            <input type="hidden" name="memberPrize.id" value="#(memberPrize.id??)"/>
            <div class="layui-form-item">
                <label class="layui-form-label">姓名</label>
                <div class="layui-input-block">
                    <input class="layui-input" type="text" value="#(memberPrize.fullName??)" readonly="readonly" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">会员卡号</label>
                <div class="layui-input-block">
                    <input class="layui-input" type="text" value="#(memberPrize.cardNumber??)" readonly="readonly" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">手机号码</label>
                <div class="layui-input-block">
                    <input class="layui-input" type="text" value="#(memberPrize.telephone??)" readonly="readonly" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">是否领取</label>
                <div class="layui-input-block">
                    <select name="memberPrize.isReceived" lay-verify="required" lay-filter="isReceived">
                        <option value="0" #(memberPrize != null ?(memberPrize.isReceived == '0' ? 'selected':''):'')>未领取</option>
                        <option value="1" #(memberPrize != null ?(memberPrize.isReceived == '1' ? 'selected':''):'')>已领取</option>
                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea placeholder="请输入备注" name="memberPrize.remark" rows="5" class="layui-textarea">#(memberPrize != null ? memberPrize.remark:'')</textarea>
                </div>
            </div>

            <div class="layui-form-footer">
                <div class="pull-right">
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
    layui.use(['form','laytpl','layer'], function() {
        var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer;

        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/doubleEleMember/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#memberPrizeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("prizeMemberTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end