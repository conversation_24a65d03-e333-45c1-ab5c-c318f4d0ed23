#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()测量设备类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        questionTableLoad({'questionnaireId':$("#questionnaireId").val()});

        sd=form.on("submit(search)",function(data){
            questionTableLoad(data.field);
            return false;
        });

        function questionTableLoad(data){
            table.render({
                id : 'questionTable'
                ,elem : '#questionTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/questionnaire/questionPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'title', title: '问题名称', align: 'center', unresize: true}
                    ,{field:'type', title: '类型', align: 'center', unresize: true,templet:function (d) {
                            if(d.type==='radio'){
                                return '单选框';
                            }else if(d.type==='checkbox'){
                                return '多选框';
                            }else if(d.type==='text'){
                                return '文本框';
                            }else{
                                return '';
                            }
                        }}
                    ,{field:'isRequired', title: '是否必填', sort: true, align: 'center', unresize: true,templet:"<div>#[[ {{#if(d.isRequired==='1'){}}是{{#}else{}}否{{#}}}]]#</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/questionnaire/questionForm?questionnaireId="+$("#questionnaireId").val() ;
            layerShow("新增问题",url,500,450);
        });

        table.on('tool(questionTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/questionnaire/delQuestion',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('questionTable',{'questionnaireId':$("#questionnaireId").val()});
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/questionnaire/questionForm?id=" + obj.data.id+"&questionnaireId="+$("#questionnaireId").val() ;
                layerShow("编辑问题",url,500,450);
            }else if(obj.event==='option'){
                var url = "#(ctxPath)/mms/questionnaire/optionIndex?questionId=" + obj.data.id ;
                layerShow("选项",url,'100%','100%')
            }
        });

    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:question:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end

    #shiroHasPermission("member:optionManage")
    #[[
    {{#if(d.type==='radio' || d.type==='checkbox'){}}
    <a class="layui-btn layui-btn-xs" lay-event="option">选项</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("member:question:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            问题名称:
            <div class="layui-inline">
                <input id="title" name="title" class="layui-input">
            </div>
            <input type="hidden" id="questionnaireId" name="questionnaireId" value="#(questionnaireId??)">
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:question:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="questionTable" lay-filter="questionTable"></table>
</div>
#end