#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户回访编辑页面#end

#define css()
#end

#define js()
<script type="text/javascript">
layui.use([ 'form', 'laydate' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//时间渲染
	laydate.render({
		elem: '#visitDate'
		, format: 'yyyy-MM-dd HH:mm:ss'
	});
	
	form.on('select(callPass)', function(data){
// 		console.log(data.elem); //得到select原始DOM对象
// 		console.log(data.value); //得到被选中的值
// 		console.log(data.othis); //得到美化后的DOM对象
		if(data.value!=null && data.value!=''){
			$('#visitDate').removeAttr('lay-verify');
			$('#appealType').removeAttr('lay-verify');
			$('#consultContent').removeAttr('lay-verify');
			$('#solution').removeAttr('lay-verify');
			$('#followResult').removeAttr('lay-verify');
		}else{
			$('#visitDate').attr('lay-verify','required');
			$('#appealType').attr('lay-verify','required');
			$('#consultContent').attr('lay-verify','required');
			$('#solution').attr('lay-verify','required');
			$('#followResult').attr('lay-verify','required');
		}
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/customer/visitSave',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("visitTable",{customerId:'#(model.customerId??)'});
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>接待日期</label>
		<div class="layui-input-block">
			#if(model.visitDate??'' == '')
				<input type="text" id="visitDate" name="visitDate" class="layui-input" #(model.callPass??''!=''?'':'lay-verify="required"') value="#(model.visitDate??'')" placeholder="请选择回访日期" autocomplete="off">
			#else
				<input type="text" id="visitDate" name="visitDate" class="layui-input" #(model.callPass??''!=''?'':'lay-verify="required"') value="#date(model.visitDate??'', 'yyyy-MM-dd HH:mm:ss')" placeholder="请选择回访日期" autocomplete="off">
			#end
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>接待类型</label>
		<div class="layui-input-block">
			<select id="visitType" name="visitType" lay-verify="required">
				<option value="">请选择</option>
				#getDictList("visitType")
					<option value="#(key)" #(model != null ?(key == model.visitType ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>诉求类型</label>
		<div class="layui-input-block">
			<select id="appealType" name="appealType" #(model.callPass??''!=''?'':'lay-verify="required"')>
				<option value="">请选择</option>
				#getDictList("appealType")
					<option value="#(key)" #(model != null ?(key == model.appealType ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">电话接通情况</label>
		<div class="layui-input-block">
			<select id="callPass" name="callPass" lay-filter="callPass">
				<option value="">请选择</option>
				#getDictList("callPass")
					<option value="#(key)" #(model != null ?(key == model.callPass ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>咨询受理内容</label>
		<div class="layui-input-block">
			<textarea id="consultContent" name="consultContent" class="layui-textarea" #(model.callPass??''!=''?'':'lay-verify="required"') maxlength="255" placeholder="请输入咨询受理内容">#(model.consultContent??)</textarea>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>处理方式或方案</label>
		<div class="layui-input-block">
			<textarea id="solution" name="solution" class="layui-textarea" #(model.callPass??''!=''?'':'lay-verify="required"') maxlength="255" placeholder="请输入处理方式或方案">#(model.solution??)</textarea>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>跟进结果</label>
		<div class="layui-input-block">
			<select id="followResult" name="followResult" #(model.callPass??''!=''?'':'lay-verify="required"')>
				<option value="">请选择</option>
				#getDictList("followResult")
					<option value="#(key)" #(model != null ?(key == model.followResult ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">客户满意度</label>
		<div class="layui-input-inline">
			<select id="baseId" name="baseId">
				<option value="">请选择基地</option>
				#for(b : baseList)
					<option value="#(b.id)" #(b.id == model.baseId?'selected':'')>#(b.baseName)</option>
				#end
			</select>
		</div>
		<div class="layui-input-inline">
			<select id="handleResult" name="handleResult">
				<option value="">请选择处理结果</option>
				#getDictList("handleResult")
					<option value="#(key)" #(model != null ?(key == model.handleResult ? 'selected':''):'')>#(value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">接待人</label>
		<div class="layui-input-block">
			<select id="receptionUserId" name="receptionUserId">
				<option value="">请选择</option>
				#for(u : empUserList)
					<option value="#(u.user_id)" #(model != null ?(u.user_id == model.receptionUserId ? 'selected':''):'')>#(u.full_name)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remarks" class="layui-textarea" maxlength="1000" placeholder="请输入备注">#(model.remarks??)</textarea>
		</div>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			#if(model.Id??'' == '')
				<input type="hidden" name="customerId" value="#(model.customerId??)">
			#end
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end