#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员卡设备管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        cardEquipmentLoad(null);

        sd=form.on("submit(search)",function(data){
            cardEquipmentLoad(data.field);
            return false;
        });

        function cardEquipmentLoad(data){
            table.render({
                id : 'cardEquipmentTable'
                ,elem : '#cardEquipmentTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/cardEquipment/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
                    ,{field:'equipmentNo', title: '设备编号', align: 'center', unresize: true}
                    ,{field:'equipmentType', title: '设备种类', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.equipmentType,'equipment_type','- -') }}</div>"}
                    ,{field:'stepSet', title: '计步设置', align: 'center', unresize: true,templet:"<div>{{ d.stepSet=='1'?'<span class='layui-badge layui-bg-green'>已开启</span>':d.stepSet=='0'?'<span class='layui-badge'>已关闭</span>':d.stepSet=='3'?'<span class='layui-badge layui-bg-blue'>设置中</span>':'- -' }}</div>"}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 180, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/cardEquipment/form" ;
            pop_show("新增会员卡测量设备",url,500,350);
        });

        table.on('tool(cardEquipmentTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/cardEquipment/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('cardEquipmentTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/cardEquipment/form?id=" + obj.data.id ;
                pop_show("编辑会员卡测量设备",url,500,350);
            }else if(obj.event === 'set'){
                var url = "#(ctxPath)/mms/cardEquipment/form?id=" + obj.data.id +"&type=set" ;
                pop_show("会员卡测量设备设置",url,600,450);
            }
        });

        <!--region Description-->
        /*//批量获取被作废数据
        getCheckTableData = function(){
            var memberCheckStatus = table.checkStatus('gatewayTypeTable');
            // 获取选择状态下的数据
            return memberCheckStatus.data;
        }

        //批量作废
        $("#batchDel").click(function(){
            layer.confirm("确定批量作废吗?",function(index){
                var jsonData=getCheckTableData();
                if(jsonData == null || jsonData == ''){
                    layer.msg('请勾选作废数据', function () {});
                    return;
                }
                var url = "#(ctxPath)/main/gatewayTypeTable/batchDel";
                util.sendAjax ({
                    type: 'POST',
                    url: url,
                    data: {bedTypeData:JSON.stringify(jsonData)},
                    notice: true,
                    loadFlag: true,
                    success : function(rep){
                        if(rep.state=='ok'){
                            tableReload("gatewayTypeTable",null);
                        }
                    },
                    complete : function() {
                    }
                });
                layer.close(index);
            });
        });*/
        <!--endregion-->
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:equipmentCard:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("finance:deductions:setBtn")
    #[[
    {{#if(d.stepSet != '3'){}}
    <a class="layui-btn layui-btn-xs" lay-event="set">设备设置</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("member:equipmentCard:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            会员卡号:
            <div class="layui-inline">
                <input id="cardNumber" name="cardNumber" class="layui-input">
            </div>
            &nbsp;&nbsp;
            设备类型:
            <div class="layui-inline">
                <select name="equipmentType" lay-search>
                    <option value="">请选择设备类型</option>
                    #for(b : typeList)
                    <option value="#(b.dictValue)">#(b.dictName)</option>
                    #end
                </select>
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:equipmentCard:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="batchDel">批量作废</button>-->
    </div>
    <table id="cardEquipmentTable" lay-filter="cardEquipmentTable"></table>
</div>
#getDictLabel("equipment_type")
#end