#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()我的客户登记首页#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
		<div class="layui-inline">
			<label class="layui-form-label">客户姓名</label>
			<div class="layui-input-inline">
				<input id="customerName" name="customerName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">会员姓名</label>
			<div class="layui-input-inline">
				<input id="memberName" name="memberName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">诉求类型</label>
				<div class="layui-input-inline">
					<select name="appealType">
						<option value="">请选择诉求类型</option>
						#getDictList("appealType")
							<option value="#(key)" >#(value)</option>
						#end
					</select>
				</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="consultRegisterTable" lay-filter="consultRegisterTable"></table>
</div>
#getDictLabel("appealType")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("member:myRegister:recordBtn")
	<a class="layui-btn layui-btn-xs" lay-event="visit">跟进情况</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		consultLoad(null);

		form.on("submit(search)",function(data){
			consultLoad(data.field);
			return false;
		});

		function consultLoad(data){
			table.render({
				id : 'consultRegisterTable'
				,elem : '#consultRegisterTable'
				,method : 'POST'
				,where : data
				,height:$(document).height()*0.8
				,limit : 10
				,limits : [10,20,30,40]
				,url : '#(ctxPath)/mms/consult/myRegisterTable'
				,cellMinWidth: 80
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field: '', title: '日期', unresize:true,templet:"<div>{{ dateFormat(d.register_date,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{field:'customer_name', title: '客户姓名', align: 'center', unresize: true}
					,{field:'member_name', title: '会员姓名', align: 'center', unresize: true}
					,{field:'card_number', title: '卡号', align: 'center', unresize: true}
					#shiroHasPermission("member:telephoneSee")
					,{field:'phone_number', title: '电话号码', align: 'center', unresize: true}
					#end
					,{field:'customer_area', title: '客户地区', align: 'center', unresize: true}
					,{field:'belong_branch_office', title: '所属部门', align: 'center', unresize: true}
					, {field: '', title: '客户诉求类型', unresize:true,templet:"<div>{{ dictLabel(d.appeal_type,'appealType','- -') }}</div>"}
					,{field:'consult_content', title: '咨询受理内容', align: 'center', unresize: true}
					,{field:'reception_people', title: '接待人员', align: 'center', unresize: true}
					,{field:'solution', title: '解决方案', align: 'center', unresize: true}
					,{field:'remarks', title: '备注', align: 'center', unresize: true}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
			});
		};

		table.on('tool(consultRegisterTable)',function(obj){
			if(obj.event === 'visit'){
				var url = "#(ctxPath)/mms/consult/visitIndex?registerId="+obj.data.id ;
				pop_show("跟进情况",url,800,600);
			}
		});
		
	});
</script>
#end