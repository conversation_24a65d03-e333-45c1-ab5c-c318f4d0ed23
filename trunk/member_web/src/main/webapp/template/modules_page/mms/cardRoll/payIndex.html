#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券收款登记首页#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
		<div class="layui-inline">
			<label class="layui-form-label">卡券编号</label>
			<div class="layui-input-inline">
				<input id="rollNumber" name="rollNumber" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<input type="hidden" name="createBy" class="layui-input" value="#(userId??)">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("crm:cardRollCollect:addBtn")
	        	<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
			#end
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="payTable" lay-filter="payTable"></table>
</div>
#getDictLabel("gender")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRollCollect:editBtn")
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("crm:cardRollCollect:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		verifyLoad(null);

		function verifyLoad(data){
			table.render({
				id : 'payTable'
				,elem : '#payTable'
				,method : 'POST'
				,where : data
				,url : '#(ctxPath)/mms/cardRoll/payPageTable'
				,cellMinWidth: 80
				,width:$(document).width()
				,height:'full-200'
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field:'rollNumber', title: '卡券编号', align: 'center', unresize: true}
					,{field:'collectName', title: '收款人', align: 'center', unresize: true}
					,{field: '', title: '收款时间', unresize:true,templet:"<div>{{ dateFormat(d.collectTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{field:'collectMoney', title: '收款金额', align: 'center', unresize: true}
// 					,{field:'collectChannel', title: '收款渠道', align: 'center', unresize: true}
					,{field:'payWay', title: '付款方式', align: 'center', unresize: true, templet:function (d) {
						var str="";
						if(d.payWay == '1'){
						    str = '现金';
						}else if(d.payWay == '2'){
						    str = '微信';
						}else if(d.payWay == '3'){
						    str = '支付宝';
						}else if(d.payWay == '4'){
						    str = '信用卡';
						}else if(d.payWay == '5'){
						    str = '会员卡';
						}else if(d.payWay == '6'){
						    str = 'Pos机';
						}else if(d.payWay == '7'){
						    str = '转账';
						}else if(d.payWay == '8'){
						    str = '企业微信';
						}
						return str;
					}}
					,{field:'payName', title: '付款人姓名', align: 'center', unresize: true}
					,{field:'payPhone', title: '付款人手机', align: 'center', unresize: true}
					,{field:'payBank', title: '付款银行', align: 'center', unresize: true}
					,{field:'payAccount', title: '付款账号', align: 'center', unresize: true}
					,{field: '', title: '付款状态',unresize:true,templet:"<div>{{d.payStatus == '0'? '<span class='layui-badge'>未付款</span>':d.payStatus == '1'? '<span class='layui-badge layui-bg-green'>已付款</span>':'- -'}}</div>"}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 15
				,limits : [15,25,35,45]
			});
		};
		table.on('tool(payTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/cardRoll/payEdit?id="+obj.data.id;
				pop_show("编辑",url,'','');
			}else if(obj.event === 'del'){
				layer.confirm("确定作废?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/mms/cardRoll/payDelete",
						type:'post',
						data:{"id":obj.data.id, delFlag:'1'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('payTable');
							}
							layer.close(index);
						}
					});
				});
			}
		});
		
		form.on("submit(search)",function(data){
			verifyLoad(data.field);
			return false;
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/mms/cardRoll/payAdd','','');
		});
		
		
	});
</script>
#end