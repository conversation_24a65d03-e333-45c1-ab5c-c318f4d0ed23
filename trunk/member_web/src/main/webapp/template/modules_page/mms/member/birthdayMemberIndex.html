#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()旅游团结算管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table','laydate'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer,laydate = layui.laydate;

        var date=new Date();
        var month=date.getMonth()+1;
        if(month<10){
            $("#monthSelect").find("option[value='0"+month+"']").prop("selected",true);
            form.render("select");
            loadMemberTable("0"+month);
        }else{
            $("#monthSelect").find("option[value='"+month+"']").prop("selected",true);
            form.render("select");
            loadMemberTable(month);
        }


        form.on('select(monthSelect)',function (obj) {
            loadMemberTable(obj.value);
        })

        function loadMemberTable(month){
            table.render({
                id : 'memberTable'
                ,elem : '#memberTable'
                ,method : 'POST'
                ,where : {"month":month}
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/member/findBirthdayMemberPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'numbers', title: '序号', align: 'center', unresize: true}
                    ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                    ,{field:'gender', title: '性别', align: 'center',templet:"#[[<div>{{#if(d.gender==='1'){}}男{{#}else if(d.gender==='2'){}}女{{#}else{}}未知{{#}}}</div>]]#"}
                    ,{field:'birthday', title: '出生日期', sort: true, align: 'center', unresize: true}
                ]]
                ,page : true
            });
        }
    });
</script>
#end

#define content()
<div>
    <div class="layui-row">
       <form class="layui-form" style="margin-top: 20px;">
           <label class="layui-form-label">月份：</label>
           <div class="layui-inline">
               <select id="monthSelect" lay-filter="monthSelect">
                   <option value="01">1月</option>
                   <option value="02">2月</option>
                   <option value="03">3月</option>
                   <option value="04">4月</option>
                   <option value="05">5月</option>
                   <option value="06">6月</option>
                   <option value="07">7月</option>
                   <option value="08">8月</option>
                   <option value="09">9月</option>
                   <option value="10">10月</option>
                   <option value="11">11月</option>
                   <option value="12">12月</option>
               </select>
           </div>
       </form>
    </div>
    <div class="layui-row" style="padding-left: 10px;">
        <table id="memberTable" lay-filter="memberTable"></table>
    </div>
</div>
#end