#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户回访页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="margin-top:15px;">
		<div class="layui-inline">
			<label class="layui-form-label">开始时间</label>
			<div class="layui-input-inline">
				<input id="startDate" name="startDate" class="layui-input" placeholder="请选择开始日期">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">结束时间</label>
			<div class="layui-input-inline">
				<input id="endDate" name="endDate" class="layui-input" placeholder="请选择结束日期">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">接待人</label>
			<div class="layui-input-inline" style="width:100px;">
				<select id="userId" name="userId" lay-search>
					<option value="">请选择</option>
					#for(u : empUserList)
						<option value="#(u.user_id)">#(u.full_name)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="visitTable" lay-filter="visitTable"></table>
</div>
#getDictLabel("visitType","appealType","callPass","followResult","handleResult")
#end

#define js()
<script>
layui.use(['form','layer','table', 'laydate'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate;

	//时间渲染
	laydate.render({
		elem: '#startDate'
		, format: 'yyyy-MM-dd'
	});
	
	//时间渲染
	laydate.render({
		elem: '#endDate'
		, format: 'yyyy-MM-dd'
	});
	
	function visitLoad(data){
		table.render({
		    id: 'visitTable'
		    , elem: '#visitTable'                  //指定原始表格元素选择器（推荐id选择器）
		    , even: true //开启隔行背景
		    , url: '#(ctxPath)/mms/customer/visitRecordTable'
		    , method: 'post'
	    	, where : data
			, cellMinWidth: 80
			, width:$(document).width()
			, height:$(document).height()*0.8
		    , cols: [[                  //标题栏
		          {type: 'numbers', title: '序号', width: 60, unresize:true}
		        , {field: 'memberName', title: '客户姓名', unresize:true}
				, {field: '', title: '回访时间', unresize:true,templet:"<div>{{ dateFormat(d.visitDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
		        , {field: '', title: '回访类型', unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
		        , {field: '', title: '诉求类型', unresize:true,templet:"<div>{{ dictLabel(d.appealType,'appealType','- -') }}</div>"}
		        , {field: '', title: '接通情况', unresize:true,templet:"<div>{{ dictLabel(d.callPass,'callPass','- -') }}</div>"}
		        , {field: 'consultContent', title: '咨询受理内容', unresize:true}
		        , {field: 'solution', title: '处理方式或方案', unresize:true}
		        , {field: '', title: '跟进结果', unresize:true,templet:"<div>{{ dictLabel(d.followResult,'followResult','- -') }}</div>"}
// 		        , {field: 'visitContent', title: '回访内容', unresize:true}
		        , {field: '', title: '客户满意度', unresize:true,templet:"<div>{{d.baseName}}-{{ dictLabel(d.handleResult,'handleResult','- -') }}</div>"}
		        , {field: 'receptionUserName', title: '接待人', unresize:true}
		        , {field: 'remarks', title: '备注', unresize:true}
		    ]]
		    , page: true
		    , limit : 15
			, limits : [15,25,35,45]
		    , loading: true
		    , done: function (res, curr, count) {
		    }
		});
	};
	
	visitLoad(null);
	
	form.on("submit(search)",function(data){
		visitLoad(data.field);
		return false;
	});
});
</script>
#end