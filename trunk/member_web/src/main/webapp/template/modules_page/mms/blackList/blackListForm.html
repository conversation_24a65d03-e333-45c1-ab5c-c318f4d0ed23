#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()黑名单表单#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/member.css"/>
<style>
	.layui-upload{display: -webkit-flex;display: flex;align-items: flex-end;}
	img{
		width:100px;
		max-height:110px;
	}
</style>
#end

#define js()
<script type="text/javascript">
	layui.use(['form'],function(){
		var form = layui.form;
		var $ = layui.$;

		//校验
		form.verify({
			checkPhone:function(value){
				if(value != null && value.length >0){
					var reg = new RegExp("^(13[0-9]|14[0-9]|15[0-9]|18[0-9]|17[0-9])\\d{8}$");
					if(!reg.test(value)){
						return "手机号码格式不正确";
					}
				}
			}
		});

		//保存
		form.on('submit(confirmBtn)', function(){
			var url = "#(ctxPath)/mms/blackList/save";
			util.sendAjax ({
				type: 'POST',
				url: url,
				data: $("#blackListForm").serialize(),
				notice: true,
				loadFlag: false,
				success : function(rep){
					if(rep.state=='ok'){
						pop_close();
						parent.tableReload("blackListTable",null);
					}
				},
				complete : function() {
				}
			});
			return false;
		});
	});
</script>
#end

#define content()
<div class="layui-collapse" style="padding:15px;border-bottom: none;">
	<div class="layui-row" style="margin-bottom:50px;">
		<form class="layui-form layui-form-pane" action="" lay-filter="layform" method="post" id="blackListForm">
			<div class="layui-row">
				<table class="layui-table" lay-skin="nob">
					<colgroup>
						<col width="50%">
						<col width="50%">
					</colgroup>
					<tbody>
					<tr>
						<td>
							<label class="layui-form-label"><span>*</span>姓名</label>
							<div class="layui-input-block">
								<input type="text" name="fullName" value="#(blackList.fullName??)" autocomplete="off" placeholder="请输入姓名" class="layui-input" lay-verify="required">
							</div>
						</td>
						<td>
							<label class="layui-form-label"><span>*</span>身份证号</label>
							<div class="layui-input-block">
								<input type="text" name="idcard" value="#(blackList.idcard??)" autocomplete="off" placeholder="请输入身份证号" class="layui-input" lay-verify="required">
							</div>
						</td>
					</tr>
					<tr>
						<td>
							<label class="layui-form-label">手机号码</label>
							<div class="layui-input-block">
								<input type="text" name="telephone" value="#(blackList.telephone??)" autocomplete="off" placeholder="请输入手机号码" class="layui-input" lay-verify="checkPhone">
							</div>
						</td>
						<td>
							<label class="layui-form-label">是否有效</label>
							<div class="layui-input-block">
								<select name="isEnable" lay-verify="required">
									<option value="0" #(blackList.isEnable??=='0'?'selected':'')>有效</option>
									<option value="1" #(blackList.isEnable??=='1'?'selected':'')>无效</option>
								</select>
							</div>
						</td>
					</tr>
					<tr>
                        <td colspan="2">
                            <label class="layui-form-label">备注</label>
                            <div class="layui-input-block">
                                <input type="text" name="remarks" value="#(blackList.remarks??)" autocomplete="off" placeholder="请输入备注" class="layui-input">
                            </div>
                        </td>
                    </tr>
					</tbody>
				</table>
			</div>
			<div class="layui-row" style="margin-bottom:50px;"></div>
			<div class="layui-form-footer">
				<div class="pull-right">
					<input type="hidden" name="id" value="#(blackList.id??)" />
					<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
					<button id="confirmBtn" class="layui-btn" lay-submit=""  lay-filter="confirmBtn">保&nbsp;&nbsp;存</button>
				</div>
			</div>
		</form>
	</div>
</div>
#end