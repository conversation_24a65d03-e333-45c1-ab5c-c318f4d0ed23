#include("/template/common/layout/_page_layout.html")
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="viewport" content="initial-scale=1.0, user-scalable=no" />
#@layout()

#define pageTitle()手环定位#end

#define css()
<style type="text/css">
    #allmap {
        width: 100%;
        height:100%;
        overflow: hidden;
        font-family:"微软雅黑";
        border:2px solid #c0c0c0;
        margin-top:10px;
    }
</style>
#end

#define js()
<script type="text/javascript" src="http://api.map.baidu.com/api?v=2.0&ak=iD2gwtGfo1p98lPenidUyx8h"></script>
#end

#define content()
<div>
    <input type="hidden" id="lng" value="#(lng)"/>
    <input type="hidden" id="lat" value="#(lat)"/>
    <div id="allmap"></div>
</div>
#end
<script type="text/javascript">
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;
        var map = new BMap.Map("allmap");

        //回调函数，处理转换之后的坐标
        translateCallback = function (data){
            if(data.status === 0) {
                map.centerAndZoom(data.points[0],17);
                var marker = new BMap.Marker(data.points[0]);        // 创建标注
                map.addOverlay(marker);
            }
        }

        $(function(){
            var convertor = new BMap.Convertor();
            var pointArr = [];
            var point = new BMap.Point($("#lng").val(),$("#lat").val());
            pointArr.push(point);
            convertor.translate(pointArr, 1, 5, translateCallback)
            map.enableScrollWheelZoom();
            map.enableContinuousZoom();
        });
    })
</script>