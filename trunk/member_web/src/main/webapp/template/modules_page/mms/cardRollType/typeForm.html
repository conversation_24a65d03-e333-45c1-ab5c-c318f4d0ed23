#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券类型编辑页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane" >
	<div class="layui-form-item" style="margin-top: 10px;">
		<label class="layui-form-label"><font color="red">*</font>类型编号</label>
		<div class="layui-input-block">
			<select id="typeCode" name="typeCode" lay-filter="typeCode">
				<option value="">请选择</option>
				#for(rollTypeCode : rollTypeCodes)
				<option value="#(rollTypeCode.key)" #if(rollTypeCode.key==model.typeCode??) selected #end>#(rollTypeCode.value)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>类型名称</label>
		<div class="layui-input-block">
			<input type="text" id="typeName" name="typeName" class="layui-input" lay-verify="required" value="#(model.typeName??)" placeholder="请输入类型名称" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="0" title="可用" #if(model!=null && model.isEnable=='0') checked #elseif(model==null) checked #end>
            <input type="radio" name="isEnable" value="1" title="不可用" #if(model.isEnable??=='1') checked #end>
        </div>
    </div>
    <div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>模板名称</label>
		<div class="layui-input-block">
			<input type="text" id="templateName" name="templateName" class="layui-input" lay-verify="required" value="#(model.templateName??)" placeholder="请输入模板名称" autocomplete="off">
		</div>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script type="text/javascript">
layui.use([ 'form' ], function() {
	var form = layui.form
	, $ = layui.jquery
	;
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/cardRollType/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("typeTable",null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
});
</script>
#end