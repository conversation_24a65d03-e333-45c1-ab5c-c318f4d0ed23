#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户回访页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="margin-top:15px;">
		<div class="layui-inline">
			<input type="hidden" name="customerId" value="#(customerId??)">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("member:visit:addBtn")
	        	<a type="button" id="addBtn" class="layui-btn btn-default">回访</a>
			#end
			
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="visitTable" lay-filter="visitTable"></table>
</div>
#getDictLabel("visitType","appealType","callPass","followResult","handleResult")
#end

#define js()
<script type="text/html" id="visitTableBar">
	#shiroHasPermission("member:visit:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:visit:delBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script>
layui.use(['form','layer','table', 'laydate'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate;

	function visitLoad(data){
		table.render({
		    id: 'visitTable'
		    , elem: '#visitTable'                  //指定原始表格元素选择器（推荐id选择器）
		    , even: true //开启隔行背景
		    , url: '#(ctxPath)/mms/customer/visitTable'
		    , method: 'post'
	    	, where : data
			, height:$(document).height()*0.8
		    , cols: [[                  //标题栏
		          {type: 'numbers', title: '序号', width: 60, unresize:true}
		        , {field: 'memberName', title: '客户姓名', width: 120, unresize:true}
				, {field: '', title: '回访时间', width: 120, unresize:true,templet:"<div>{{ dateFormat(d.visitDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
		        , {field: '', title: '回访类型', width: 120, unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
		        , {field: '', title: '诉求类型', width: 120, unresize:true,templet:"<div>{{ dictLabel(d.appealType,'appealType','- -') }}</div>"}
		        , {field: '', title: '接通情况', width: 120, unresize:true,templet:"<div>{{ dictLabel(d.callPass,'callPass','- -') }}</div>"}
		        , {field: 'consultContent', title: '咨询受理内容', width: 200, unresize:true}
		        , {field: 'solution', title: '处理方式或方案', width: 200, unresize:true}
		        , {field: '', title: '跟进结果', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.followResult,'followResult','- -') }}</div>"}
// 		        , {field: 'visitContent', title: '回访内容', unresize:true}
		        , {field: '', title: '客户满意度', width: 120, unresize:true,templet:"<div>{{d.baseName}}-{{ dictLabel(d.handleResult,'handleResult','- -') }}</div>"}
		        , {field: 'receptionUserName', title: '接待人', width: 120, unresize:true}
		        , {field: 'remarks', title: '备注', width: 300, unresize:true}
		        , {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#visitTableBar'} //这里的toolbar值是模板元素的选择器
		    ]]
		    , page: true
		    , limit : 15
			, limits : [15,25,35,45]
		    , loading: true
		    , done: function (res, curr, count) {
		    }
		});
		// 表格绑定事件
	    table.on('tool(visitTable)',function (obj) {
	        if(obj.event==="edit"){//编辑按钮事件
	        	pop_show('编辑','#(ctxPath)/mms/customer/visitEdit?id='+obj.data.id,'','');
	        }else if(obj.event==="del"){//作废按钮事件
	        	//作废操作
	    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
	                util.sendAjax ({
	                    type: 'POST',
	                    url: '#(ctxPath)/mms/customer/visitDel',
	                    data: {id:obj.data.id, delFlag:'1'},
	                    loadFlag: true,
	                    success : function(rep){
	                    	tableReload("visitTable",{registerId:obj.data.customerId});
	                    },
	                    complete : function() {
	        		    }
	                });
					layer.close(index);
	            });
	        }
	    });
	};
	
	visitLoad({customerId:'#(customerId??)'});
	
	form.on("submit(search)",function(data){
		visitLoad(data.field);
		return false;
	});
	
	// 精确查询
	$("#addBtn").click(function(){
		pop_show('回访','#(ctxPath)/mms/customer/visitAdd?customerId=#(customerId??)','','');
	});
});
</script>
#end