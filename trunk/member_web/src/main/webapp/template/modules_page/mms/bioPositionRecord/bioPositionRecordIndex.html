#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()手环终端数据展示#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/css/assess.css" media="all">
#end

#define js()
<script type="text/javascript">
    layui.use(['table','layer','form','jquery','element'],function(){
        var table = layui.table, $ = layui.jquery, element = layui.element,form=layui.form;

        sd=form.on("submit(search)",function(data){
            var type = '';
            $("#idui li").each(function(){
               if($(this).hasClass("layui-this")){
                   type = $(this).attr("lay-id");
               }
            });

            if(type == 'daily'){
                dailyTabLoad(data.field);
            }else{
                alarmTabLoad(data.field);
            }
            return false;
        });

        function dailyTabLoad(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/mms/bioPositionRecord/findListPageByLocation'
                ,cols: [[
                    {type: 'numbers',title: '序号',unresize:true}
                    ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                    ,{field:'idcard', title: '身份证', align: 'center',unresize: true}
                    ,{field:'equipmentNo', title: '设备号', align: 'center',unresize: true}
                    ,{field:'reportTime', title: '测量时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.reportTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'longitude', title: '经度', align: 'center',unresize: true}
                    ,{field:'latitude', title: '纬度', align: 'center',unresize: true}
                    ,{field:'createDate', title: '创建时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };


        function alarmTabLoad(data){
            table.render({
                id : 'dataTable'
                ,elem : '#dataTable'
                ,method : 'POST'
                ,where : data
                ,url : '#(ctxPath)/mms/bioPositionRecord/findListPageBySos'
                ,cols: [[
                    {type: 'numbers',title: '序号',unresize:true}
                    ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                    ,{field:'idcard', title: '身份证', align: 'center',unresize: true}
                    ,{field:'equipmentNo', title: '设备号', align: 'center',unresize: true}
                    ,{field:'reportTime', title: '测量时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.reportTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'longitude', title: '经度', align: 'center',unresize: true}
                    ,{field:'latitude', title: '纬度', align: 'center',unresize: true}
                    ,{field:'createDate', title: '创建时间', align: 'center',unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };


        table.on('tool(dataTable)',function(obj){
            var type = '';
            $("#idui li").each(function(){
                if($(this).hasClass("layui-this")){
                    type = $(this).attr("lay-id");
                }
            });

            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax({
                        url:"#(ctxPath)/mms/bioPositionRecord/delete?id="+obj.data.id +"&type="+type,
                        type:'post',
                        data:{},
                        notice:true,
                        success:function(returnData){
                            if(returnData.state==='ok'){
                                table.reload('dataTable') ;
                            }
                            layer.close(index);
                        }
                    });
                });
            }else if(obj.event === 'detail'){
                var url = "#(ctxPath)/mms/bioPositionRecord/bioMap?lng=" + obj.data.longitude +"&lat="+ obj.data.latitude;
                layerShow("手环定位",url,700,550);
            }
        });


        //监听监测类型页签切换方法
        element.on('tab(dataTab)', function(){
            $("#fullName").val('');
            $("#idcard").val('');
            var data = {"fullName":$("#fullName").val(),"idcard":$("#idcard").val()};
            var dataType = $(this).attr("lay-id");
            if(dataType=='daily'){
                dailyTabLoad(data);
            }else if(dataType=='alarm'){
                alarmTabLoad(data);
            }
            $(".layui-btn-group button").each(function(){
                var queryType = $(this).attr("id");
                if(queryType=='daily'){
                    if($(this).hasClass("layui-btn-primary")){
                        $(this).removeClass("layui-btn-primary");
                    }
                }else{
                    if(!$(this).hasClass("layui-btn-primary")){
                        $(this).addClass("layui-btn-primary");
                    }
                }
            });
        });

        //页面打开加载
        $(function(){
            dailyTabLoad(null);
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:bioPosition:detailBtn")
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
    #end
    #shiroHasPermission("member:bioPosition:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div class="layui-collapse" style="border-bottom: none;">
    <div class="layui-row layui-col-space10">
        <div class="layui-form">
            <div class="bill-detail-search" style="margin-left: 0px;">
                <div class="layui-form-item d-tab">
                    <div id="dataTab" class="layui-tab layui-tab-card assess" lay-filter="dataTab">
                        <ul class="layui-tab-title" id="idui">
                            <li lay-id="daily" class="layui-this">日常定位</li>
                            <li lay-id="alarm">报警定位</li>
                        </ul>
                    </div>
                </div>
                <div class="demoTable layui-row">
                    <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
                        姓名:
                        <div class="layui-inline">
                            <input id="fullName" name="fullName" class="layui-input">
                        </div>
                        &nbsp;&nbsp;
                        身份证:
                        <div class="layui-inline">
                            <input id="idcard" name="idcard" class="layui-input">
                        </div>
                        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
                    </form>
                </div>
                <div class="layui-row">
                    <table id="dataTable" lay-filter="dataTable" class="layui-table"></table>
                </div>
            </div>
        </div>
    </div>
    <!---->
</div>
</div>
#end