#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()双十一营销会员管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        prizeMemberLoad(null);

        sd=form.on("submit(search)",function(data){
            prizeMemberLoad(data.field);
            return false;
        });

        function prizeMemberLoad(data){
            table.render({
                id : 'prizeMemberTable'
                ,elem : '#prizeMemberTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/doubleEleMember/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                  {type: 'numbers', width:100, title: '序号',unresize:true}
                  ,{field:'cardNumber', title: '会员卡号', align: 'center', unresize: true}
                  ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                  #shiroHasPermission("member:telephoneSee")
                       ,{field:'telephone', title: '手机号码', align: 'center', unresize: true}
                  #end
                ,{field:'prize', title: '奖品', align: 'center', unresize: true}
                ,{field:'isReceived', title: '是否领取', align: 'center', templet:"<div>{{d.isReceived == '0'?'<span class='layui-badge layui-bg-green'>未领取</span>':d.isReceived == '1'?'<span class='layui-badge'>已领取</span>':'- -'}}</div>"}
                ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
        ]]
        ,page : true
        });
        };
        table.on('tool(prizeMemberTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/doubleEleMember/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('prizeMemberTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/doubleEleMember/form?id=" + obj.data.id ;
                pop_show("编辑营销会员信息",url,540,550);
            }

            return false;
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:eleMember:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("member:eleMember:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable">
        <div class="layui-row">
            <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
                姓名:
                <div class="layui-inline">
                    <input id="fullName" name="fullName" class="layui-input">
                </div>
                &nbsp;&nbsp;
                会员卡号:
                <div class="layui-inline">
                    <input id="cardNumber" name="cardNumber" class="layui-input">
                </div>
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            </form>
        </div>
    </div>
    <table id="prizeMemberTable" lay-filter="prizeMemberTable"></table>
</div>
#end