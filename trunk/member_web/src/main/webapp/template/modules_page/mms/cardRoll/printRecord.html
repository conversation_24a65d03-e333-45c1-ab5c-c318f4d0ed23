#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()打印记录页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<table id="printTable" lay-filter="printTable"></table>
</div>
#end

#define js()
<script>
layui.use(['form','layer','table'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;
	
	table.render({
	    id: 'printTable'
	    , elem: '#printTable'                  //指定原始表格元素选择器（推荐id选择器）
//		    , even: true //开启隔行背景
	    , url: '#(ctxPath)/mms/cardRoll/printTable'
	    , method: 'post'
	    , where: {recordId:'#(recordId??)'}
		, cellMinWidth: 80
		, height:'full-200'
	    , cols: [[                  //标题栏
	          {type: 'numbers', title: '序号', width: 60, unresize:true}
			, {field: '', title: '打印时间', align: 'center', unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
	    ]]
	    , page: true
	    , limit : 5
		, limits : [5,15,25,35]
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
});
</script>
#end