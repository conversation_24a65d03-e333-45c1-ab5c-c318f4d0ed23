#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()回访统计回访记录首页#end

#define css()
#end

#define content()
<div class="layui-row">
	<table id="visitTable" lay-filter="visitTable"></table>
</div>
#getDictLabel("visitType","appealType","callPass","followResult","handleResult")
#end

#define js()
<script>
layui.use(['form','layer','table', 'laydate'], function() {
	
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate;
	
	table.render({
	    id: 'visitTable'
	    , elem: '#visitTable'                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/mms/customer/visitStatisticsRecordPage'
	    , method: 'post'
    	, where : {startDate:'#(startDate??)', endDate:'#(endDate??)', receptionUserId:'#(receptionUserId??)', visitType:'#(visitType??)', actionType:'#(actionType??)'}
		, cellMinWidth: 80
		, width:$(document).width()
		, height:$(document).height()*0.8
	    , cols: [[                  //标题栏
	          {type: 'numbers', title: '序号', width: 60, unresize:true}
	        , {field: 'memberName', title: '客户姓名', width: 100, unresize:true}
			, {field: '', title: '回访时间', width: 160, unresize:true,templet:"<div>{{ dateFormat(d.visitDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
	        , {field: '', title: '回访类型', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
	        , {field: '', title: '诉求类型', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.appealType,'appealType','- -') }}</div>"}
	        , {field: '', title: '接通情况', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.callPass,'callPass','- -') }}</div>"}
	        , {field: 'consultContent', title: '咨询受理内容', width: 200, unresize:true}
	        , {field: 'solution', title: '处理方式或方案', width: 200, unresize:true}
	        , {field: '', title: '跟进结果', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.followResult,'followResult','- -') }}</div>"}
	        , {field: '', title: '客户满意度', width: 100, unresize:true,templet:"<div>{{d.baseName}}-{{ dictLabel(d.handleResult,'handleResult','- -') }}</div>"}
	        , {field: 'receptionUserName', title: '接待人', width: 100, unresize:true}
	        , {field: 'remarks', title: '备注', width: 200, unresize:true}
	    ]]
	    , page: true
	    , limit : 15
		, limits : [15,25,35,45]
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
});
</script>
#end