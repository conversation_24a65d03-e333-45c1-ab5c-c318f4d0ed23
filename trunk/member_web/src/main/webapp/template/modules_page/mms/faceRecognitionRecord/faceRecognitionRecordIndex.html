#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()终端识别数据管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        faceRecordLoad(null);

        sd=form.on("submit(search)",function(data){
            faceRecordLoad(data.field);
            return false;
        });

        <!--region Description-->
        // 添加
        /*$("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/memberface/formAdd" ;
            pop_show("上传人脸照片",url,900,500);
        });*/
        <!--endregion-->

        function faceRecordLoad(data){
            table.render({
                id : 'faceRecordTable'
                ,elem : '#faceRecordTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/faceRecognitionRecord/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                    #shiroHasPermission("member:idcardSee")
                    ,{field:'idcard', title: '身份证号', align: 'center', unresize: true}
                    #end
                    ,{field:'baseName', title: '基地', align: 'center', unresize: true}
                    ,{field:'deviceType', title: '设备类型', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.deviceType,'face_device_type','- -') }}</div>"}
                    ,{field:'deviceKey', title: '设备标识码', align: 'center', unresize: true}
                    ,{field:'actionType', title: '动作类型', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.actionType,'face_action_type','- -') }}</div>"}
                    ,{field:'time', title: '识别时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.time,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'type', title: '识别方式', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.type,'face_recognition_record_type','- -') }}</div>"}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };


        table.on('tool(faceRecordTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/faceRecognitionRecord/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('faceRecordTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'detail'){
                var url = "#(ctxPath)/mms/faceRecognitionRecord/form?id=" + obj.data.id ;
                pop_show("查询人脸照片",url,600,500);
            }
        });
    })
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:faceRecognition:detail")
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
    #end
    #shiroHasPermission("member:faceRecognition:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            姓名:
            <div class="layui-inline">
                <input id="fullName" name="fullName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            身份证:
            <div class="layui-inline">
                <input id="idcard" name="idcard" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        <!--<button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">上传人脸照片</button>-->
    </div>
    <table id="faceRecordTable" lay-filter="faceRecordTable"></table>
</div>
#getDictLabel("face_recognition_record_type")
#getDictLabel("face_device_type")
#getDictLabel("face_action_type")
#end