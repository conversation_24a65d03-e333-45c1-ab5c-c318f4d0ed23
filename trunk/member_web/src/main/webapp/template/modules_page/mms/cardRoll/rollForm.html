#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券编辑页面#end

#define css()
<select>
	.layui-disabled, .layui-disabled:hover {
		color: #000 !important;
		cursor: not-allowed !important;
	}
	#if(formType=='see')
		.layui-disabled, .layui-disabled:hover {
			color: #000000 !important;
			cursor: not-allowed !important;
		}

		.layui-input {
			color: #000;
		}
	#end
</select>
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane" id="rollForm">

	<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
		<legend>基础设置</legend>
	</fieldset>

	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>类型选择</label>
		<div class="layui-input-block">
			<select id="rollTypeId" name="rollTypeId" lay-filter="rollTypeId" lay-verify="required">
				<option value="">请选择</option>
				#for(t : typeList)
					<option value="#(t.id)" data-code="#(t.typeCode)" #(t.id == model.rollTypeId?'selected':'')>#(t.typeName)</option>
				#end
			</select>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>编号</label>
		<div class="layui-input-block">
			<input type="text" id="rollCode" name="rollCode" class="layui-input" lay-verify="required" value="#(model.rollCode??)" placeholder="请输入编号"   autocomplete="off">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label" style="width: 170px;">是否自定义卡券后缀</label>
		<div class="layui-input-block">
			<input type="radio" name="isSuffix" value="1" lay-filter="isSuffix" title="是" #if(model.rollCodeSuffix!=null) checked #end>
			<input type="radio" name="isSuffix" value="0" lay-filter="isSuffix" title="否" #if(model.isBingUse==null || model.rollCodeSuffix==null) checked #end>
		</div>
	</div>
	<div class="layui-form-item" id="rollCodeSuffixDiv" #if(model.rollCodeSuffix!=null) style="display: block;" #else style="display: none;" #end  >
		<label class="layui-form-label"><font color="red"></font>编号后缀</label>
		<div class="layui-input-block">
			<input type="text" id="rollCodeSuffix" name="rollCodeSuffix" class="layui-input"  value="#(model.rollCodeSuffix??)" placeholder="请输入编号后缀"   autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>名称</label>
		<div class="layui-input-block">
			<input type="text" id="rollName" name="rollName" class="layui-input" lay-verify="required" value="#(model.rollName??)" placeholder="请输入名称" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">适用分公司</label>
		<div class="layui-input-block">
			<div id="fitBranchOfficeXmSelectTree" class="xm-select-demo" hiddenTargetId="fitBranchOffice" dataValue="#(fitBranchOffices??)"></div>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label">适用基地</label>
		<div class="layui-input-block">
			<div id="fitBaseXmSelectTree" class="xm-select-demo" hiddenTargetId="fitBase" dataValue="#(fitBases??)"></div>
		</div>
	</div>
<!-- 	<div class="layui-form-item"> -->
<!-- 		<label class="layui-form-label"><font color="red">*</font>适用客户类型</label> -->
<!-- 		<div class="layui-input-block"> -->
<!-- 		</div> -->
<!-- 	</div> -->
<!-- 	<div class="layui-form-item"> -->
<!-- 		<label class="layui-form-label"><font color="red">*</font>适用客户区域</label> -->
<!-- 		<div class="layui-input-block"> -->
<!-- 		</div> -->
<!-- 	</div> -->
	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>数量</label>
		<div class="layui-input-block">
			<input type="text" id="rollQuantity" name="rollQuantity" class="layui-input" lay-verify="required" value="#(model.rollQuantity??)" placeholder="请输入数量" autocomplete="off">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label" style="width: 150px;">是否绑定才能使用</label>
		<div class="layui-input-block">
			<input type="radio" name="isBingUse" value="1" lay-filter="isBingUse" title="是" #if(model.isBingUse=='1') checked #end>
			<input type="radio" name="isBingUse" value="0" lay-filter="isBingUse" title="否" #if(model.isBingUse==null || model.isBingUse??=='0') checked #end>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label" style="width: 150px;">是否限制绑定时间</label>
		<div class="layui-input-block">
			<input type="radio" name="isAstrictBind" #if(model==null || model.isBingUse??=='0') disabled #end  value="1" lay-filter="isAstrictBind" title="是" #if(model.isAstrictBind=='1') checked #end>
			<input type="radio" name="isAstrictBind" value="0" lay-filter="isAstrictBind" title="否" #if(model.isAstrictBind==null || model.isAstrictBind??=='0') checked #end>
		</div>
	</div>
	<div class="layui-form-item">
		<label class="layui-form-label" style="width: 150px;">是否一次性使用</label>
		<div class="layui-input-block">
			<input type="radio" name="useTimes"  value="ONE" lay-filter="useTimes" title="是" #if(model.useTimes??=='ONE') checked #end>
			<input type="radio" name="useTimes" value="MANY" lay-filter="useTimes" title="否" #if(model==null || model.useTimes??=='MANY') checked #end>
		</div>
	</div>
	<div class="layui-form-item"  id="maxBindTimeDiv" #if(model.isAstrictBind??=='1') style="display: block;" #else style="display: none;" #end >
		<label class="layui-form-label" style="width: 150px;"><font color="red">*</font>#(model.isAstrictBind??)最大可绑定日期</label>
		<div class="layui-input-block" style="margin-left: 150px;">
			<input type="text" id="maxBindTime" name="maxBindTime" class="layui-input" #if(model.isAstrictBind??=='1') lay-verify="required" #else lay-verify="" #end  value="#date(model.maxBindTime??,'yyyy-MM-dd')" placeholder="请输入最大可绑定时间" autocomplete="off">
		</div>
	</div>

	<div class="layui-form-item">
		<label class="layui-form-label"><font color="red">*</font>有效期类型</label>
		<div class="layui-input-block">
			<select id="validType" name="validType" lay-filter="validType" >
				<option value="1" #if(model.validType=='1') selected #end>指定有效时间范围</option>
				<option value="2" #if(model.validType=='2') selected #end #if(model==null || model.isBingUse??=='0') disabled #end >绑定开始指定有效时长</option>
			</select>
		</div>
	</div>

	<div class="layui-form-item" id="div1" #if(model.validType=='2') style="display: none;" #end>
		<label class="layui-form-label"><font color="red">*</font>开始时间</label>
		<div class="layui-input-block">
			#if(model.validStartDate??'' == '')
				<input type="text" id="validStartDate" name="validStartDate" class="layui-input" #if(model.validType=='1') lay-verify="required" #end value="#(model.validStartDate??'')" placeholder="请选择开始时间" autocomplete="off">
			#else
				<input type="text" id="validStartDate" name="validStartDate" class="layui-input" #if(model.validType=='1') lay-verify="required" #end  value="#date(model.validStartDate??'', 'yyyy-MM-dd')" placeholder="请选择开始时间" autocomplete="off">
			#end
		</div>
	</div>
	<div class="layui-form-item" id="div2" #if(model.validType=='2') style="display: none;" #end>
		<label class="layui-form-label"><font color="red">*</font>结束时间</label>
		<div class="layui-input-block">
			#if(model.validEndDate??'' == '')
				<input type="text" id="validEndDate" name="validEndDate" class="layui-input" #if(model.validType=='1') lay-verify="required" #end value="#(model.validEndDate??'')" placeholder="请选择结束时间" autocomplete="off">
			#else
				<input type="text" id="validEndDate" name="validEndDate" class="layui-input" #if(model.validType=='1') lay-verify="required" #end value="#date(model.validEndDate??'', 'yyyy-MM-dd')" placeholder="请选择结束时间" autocomplete="off">
			#end
		</div>
	</div>
	<div class="layui-form-item" id="div3" #if(model.validType=='2') style="display: block;" #else  style="display: none;" #end>
		<label class="layui-form-label" style="width: 160px;    padding: 9px 0px;"><font color="red">*</font>绑定开始有效期时长(天)</label>
		<div class="layui-input-block" style="margin-left: 160px;">
			<input type="text" id="validDays" name="validDays" class="layui-input"  #if(model.validType=='2') lay-verify="required|number" #end value="#(model.validDays??'')" placeholder="绑定开始有效期时长(天)" autocomplete="off">
		</div>
	</div>
	<div class="layui-form-item" id="div4" #if(model.validType=='2') style="display: block;" #else  style="display: none;" #end>
		<label class="layui-form-label" style="width: 160px;    padding: 9px 0px;"><font color="red">*</font>最大有效日期</label>
		<div class="layui-input-block" style="margin-left: 160px;">
			<input type="text" id="finalValidDate" name="finalValidDate" class="layui-input"  #if(model.validType=='2') lay-verify="required" #end value="#date(model.finalValidDate??,'yyyy-MM-dd')" placeholder="最大有效日期" autocomplete="off">
		</div>
	</div>

	<div class="layui-form-item">
        <label class="layui-form-label">是否可用</label>
        <div class="layui-input-block">
            <input type="radio" name="isEnable" value="0" title="可用" #if(model!=null && model.isEnable=='0') checked #elseif(model.isEnable==null) checked #end>
            <input type="radio" name="isEnable" value="1" title="不可用" #if(model.isEnable??=='1') checked #end>
        </div>
    </div>

    <div class="layui-form-item">
		<label class="layui-form-label">备注</label>
		<div class="layui-input-block">
			<textarea name="remarks" class="layui-textarea" maxlength="1000" placeholder="请输入备注">#(model.remarks??)</textarea>
		</div>
	</div>
	<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
		<legend>类型配置：<span id="configSpan">无</span></legend>
	</fieldset>

	<div class="layui-row" id="goodsConfigDiv">
		


	</div>


	<div class="layui-row" style="margin-bottom:100px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<input type="hidden" id="fitBranchOffice" name="fitBranchOffice" value="#(fitBranchOffices??)">
			<input type="hidden" id="fitBase" name="fitBase" value="#(fitBases??)">
			<input type="hidden" id="deployStatus" name="deployStatus" value="#(model.deployStatus??'1')">
			#if(model.deployStatus??'1' == '1')
				<!--<button class="layui-btn" lay-submit="" lay-filter="releaseBtn">发&nbsp;&nbsp;布</button>-->
				<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			#end
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script src="#(ctxPath)/static/js/xm-select.js" type="text/javascript" charset="utf-8"></script>
<script id="roomTrTpl" type="text/html">
	<tr id="model-{{d.idx}}">

		<td>
			<div class="layui-row" style="width: 200px;">
				<input type="text" id="modelName{{d.idx}}" name="model{{d.idx}}.modelName"  lay-verify="required"readonly class="layui-input" value="" style="cursor:pointer;"  onclick="selectedRoom({{d.idx}})" placeholder="点击选择商品" autocomplete="off">
				<input type="hidden" id="modelId{{d.idx}}" name="model{{d.idx}}.modelId" value="">
			</div>
		</td>
		<td id="standard{{d.idx}}">

		</td>

		<td id="numTd{{d.idx}}">
			<input type="text" id="num{{d.idx}}" name="model{{d.idx}}.num"  lay-verify="required" class="layui-input" value=""   placeholder="输入数量" autocomplete="off">
		</td>


		<td>
			<input type="hidden"  name="room{{d.idx}}.id" value="">
			<button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delTr('model-{{d.idx}}','')">作废</button>
		</td>
	</tr>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate','laytpl','table' ], function() {
	var form = layui.form
	, laydate = layui.laydate
	, $ = layui.jquery,laytpl=layui.laytpl,table=layui.table
	;
	
	//时间渲染
	laydate.render({
		elem: '#validStartDate'
		, format: 'yyyy-MM-dd'
	});
	
	//时间渲染
	laydate.render({
		elem: '#validEndDate'
		, format: 'yyyy-MM-dd'
	});

	laydate.render({
		elem: '#finalValidDate'
		, format: 'yyyy-MM-dd'
	});
	laydate.render({
		elem: '#maxBindTime'
		, format: 'yyyy-MM-dd'
		,trigger:'click'
	});





	form.on('select(rollTypeId)',function (d) {
		let typeCode = $("#rollTypeId").find('option[value="'+d.value+'"]').attr("data-code");
		if(typeCode=='goods_type'){
			//物品券
			$("#configSpan").text('兑换商品');

			let html=`<div class="layui-form-item" >

						<button class="layui-btn" type="button" id="addRoom" style="float: right;">添加商品</button>
						<table class="layui-table" style="overflow: auto;"  id="roomTable" >
						<!--<col width="30%">
						<col width="60%">
						<col width="10%">-->
						<thead>
						<tr>
						<th ><font color="red">*</font>商品</th>
						<th ><font color="red"></font>规格</th>
						<th ><font color="red"></font>兑换数量</th>
						<th>操作</th>
						</tr>
						</thead>
						<tbody id="roomTbody">
						<input id="typeId" name="typeId" type="hidden" value="#(typeId??)">
						<input id="roomCount" name="roomCount" type="hidden" value="#(recordList.size()??0)">
						#for(roomRecord : recordList)
						<tr id="model-#(for.index+1)">

						<td>
						<div class="layui-row" style="width: 200px;">
						<input type="text" id="modelName#(for.index+1)" name="model#(for.index+1).modelName"  lay-verify="required"readonly class="layui-input" value="#(roomRecord.modelName??)" style="cursor:pointer;"  onclick="selectedRoom(#(for.index+1))" placeholder="点击选择商品" autocomplete="off">
						<input type="hidden" id="modelId#(for.index+1)" name="model#(for.index+1).modelId" value="#(roomRecord.modelId??)">
						<input type="hidden"   name="model#(for.index+1).id" value="#(roomRecord.id??)">

						</div>
						</td>



						<td id="standard#(for.index+1)">
						#(roomRecord.standard??)
						</td>
						<td id="numTd#(for.index+1)">
						<input type="text" id="num#(for.index+1)" name="model#(for.index+1).num"  lay-verify="required" class="layui-input" value="#(roomRecord.num??)"   placeholder="输入数量" autocomplete="off">
						</td>


						<td>
						#if((isSaveHandle || taskId==null) && isEdit)
						<button class="layui-btn layui-btn-danger layui-btn-xs" type="button" onclick="delTr('model-#(for.index+1)','#(roomRecord.id??)')">作废</button>
						#end
						</td>
						</tr>
						#end
						</tbody>
						</table>

						</div>
						`
			$("#goodsConfigDiv").empty().html(html);
			form.render();
		}else if(typeCode=='face_value_type'){
			//面额券
			$("#configSpan").text('面额值');
			let html=`
				<div class="layui-form-item">
				<label class="layui-form-label"><font color="red">*</font>面额值</label>
				<div class="layui-input-block">
				<input type="text" id="rollValue" name="rollValue" class="layui-input" lay-verify="required|number" value="#(model.rollValue??)" placeholder="请输入面额值" autocomplete="off">
				</div>
				</div>
			`;
			$("#goodsConfigDiv").empty().append(html);
			form.render();
		}else{
			//configSpan
			$("#configSpan").text('无');

		}

	})

	#if(model!=null)
		layui.event("form", "select(rollTypeId)", {value: '#(model.rollTypeId??)'});
	#end

	$("#goodsConfigDiv").on('click','#addRoom',function () {
		addRoomTpl();

		$("#roomTable .layui-form-select").on('click',function(){
			var scrollTop=$(document).scrollTop();//获取页面滚动的高度
			var width = $(this).width();
			var top = $(this).offset().top;
			var left = $(this).offset().left;
			$(this).find("dl").css({"min-width":width+"px", top:top-scrollTop+40+"px", left:left+"px"});
		});

	});

	addRoomTpl = function () {
		let roomCount=Number($('#roomCount').val())+1;
		$('#roomCount').val(roomCount);
		laytpl(roomTrTpl.innerHTML).render({
			'idx': roomCount
		}, function (html) {
			$("#roomTbody").append(html);
		});

		//时间渲染
		laydate.render({
			elem: '#contractDate' + roomCount
			,trigger:'click'
			,range: true
		});
		laydate.render({
			elem: '#roomDate' + roomCount
			,trigger:'click'
			,range: true
		});

		form.render('select');
	};



	delTr=function (trIndex,id) {
		if(id==''){
			$('#' + trIndex).remove();
		}else{
			layer.confirm('是否要作废?', function(index){
				util.sendAjax ({
					type: 'POST',
					url: '#(ctxPath)/mms/cardRoll/delRel',
					data: {'id':id,'userId':$("#userId").val()},
					notice: true,
					loadFlag: true,
					success : function(rep){
						if(rep.state=='ok'){
							$('#' + trIndex).remove();
						}
					},
					complete : function() {
					}
				});
				layer.close(index);
			});
		}
		return false;
	}

	selectedRoom=function (trIndex) {

		layerShow('选择商品','#(ctxPath)/mms/cardRoll/stockModelIndex?trIndex='+trIndex,1500,650);
	}

	checkRoomData=function (id) {
		let isExist=false;
		$.each($("#roomTbody").find('tr'),function (index,item) {
			let trId= $(item).attr('id');
			let trIndex = trId.replace('model-','');
			if($("#modelId"+trIndex).val()==id){
				isExist=true;
				return false;
			}

		});
		return isExist;
	}

	selectedRoomData=function (data,trIndex) {
		$("#modelName"+trIndex).val(data.name);
		$("#modelId"+trIndex).val(data.id);
		$("#standard"+trIndex).text(data.standard);
	}


	form.on('radio(isBingUse)',function (obj) {
		if(obj.value=='1'){
			$("input[name='isAstrictBind'][value='1']").prop('disabled',false);
			$('#validType').find("option[value='2']").prop('disabled',false);
			form.render('select');
			form.render('radio');
		}else if(obj.value=='0'){
			$("input[name='isAstrictBind'][value='0']").prop('check',true);
			$('#validType option[value="1"]').prop('selected',true);
			layui.event("form", "select(validType)", {value: '1'});
			layui.event("form", "radio(isAstrictBind)", {value: '0'});

			$("input[name='isAstrictBind'][value='1']").prop('disabled',true);
			$('#validType option[value="2"]').prop('disabled',true);
			form.render('select');
			form.render('radio');
		}

	})


	form.on('radio(isAstrictBind)',function (data) {
		if(data.value=='1'){
			$("#maxBindTimeDiv").css('display','block');
			$("#maxBindTime").attr('lay-verify','required');
		}else if(data.value=='0'){
			$("#maxBindTime").val('')
			$("#maxBindTimeDiv").css('display','none');
			$("#maxBindTime").attr('lay-verify','');
		}
	})

	form.on('radio(isSuffix)',function (data) {
		if(data.value=='1'){
			$("#rollCodeSuffixDiv").css('display','block');
		}else if(data.value=='0'){
			$("#rollCodeSuffix").val('')
			$("#rollCodeSuffixDiv").css('display','none');
		}
	})

	form.render('radio');

	form.on('select(validType)',function (d) {
		if(d.value=='1'){
			$("#div1").css("display","block");
			$("#div2").css("display","block");
			$("#div3").css("display","none");
			$("#div4").css("display","none");


			$("#validStartDate").attr("lay-verify","required");
			$("#validEndDate").attr("lay-verify","required");
			$("#validDays").attr("lay-verify","");
			$("#finalValidDate").attr("lay-verify","");

		}else if(d.value=='2'){
			$("#div1").css("display","none");
			$("#div2").css("display","none");
			$("#div3").css("display","block");
			$("#div4").css("display","block");

			$("#validStartDate").attr("lay-verify","");
			$("#validEndDate").attr("lay-verify","");
			$("#validDays").attr("lay-verify","required|number");
			$("#finalValidDate").attr("lay-verify","required");
		}

	})


	
    fitBranchOfficeSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
    	var fitBranchOfficeXmSelectCheck = xmSelect.render({
    		el:'#'+elem 
    		, prop: {
				name: 'shortName'
				, value: 'id'
			}
    		, model:{label:{type:'text'}}
    		, toolbar: { show: true }
    		, filterable:true
    		, direction: 'down'
    		, tips:'请选择分公司'
    		, tree:{
    			show:true
    			, strict:false
    		}
    		, height:'300px'
   			, autoRow:true
    		, on:function(data){
    			var dataArray = data.arr;//data.arr:  当前多选已选中的数据
    			if(data.change){//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
//     				console.log('data.change'+dataArray.length);
    				if(dataArray.length>0){
    					var dArray = new Array();
	    				for (var i = 0; i < dataArray.length; i++) {
	    					dArray.push(dataArray[i].id);
	    				}
	    				$('#'+hiddenTargetId).val(dArray.toString());
    				}else{
    					$('#'+hiddenTargetId).val('');
    				}
    			}
    		}
    	});
    	$.post('#(ctxPath)/mms/cardRoll/getAllBranchOffice', {}, function(rep) {
			if(rep.state=='ok'){
				var initValueArray = null;
				if(dataValue.indexOf(",")!=-1){
					initValueArray = dataValue.split(',');
					fitBranchOfficeXmSelectCheck.update({
						data:rep.branchOfficeList
						, initValue:initValueArray
					})
				}else{
					fitBranchOfficeXmSelectCheck.update({
						data:rep.branchOfficeList
						, initValue:[dataValue]
					})
				}
			}
		});
    };
	
    fitBaseSelectCheckLoad = function (elem, hiddenTargetId, dataValue) {
    	var fitBaseXmSelectCheck = xmSelect.render({
    		el:'#'+elem 
    		, prop: {
				name: 'baseName'
				, value: 'id'
			}
    		, model:{label:{type:'text'}}
    		, toolbar: { show: true }
    		, filterable:true
    		, direction: 'down'
    		, tips:'请选择基地'
    		, tree:{
    			show:true
    			, strict:false
    		}
    		, height:'300px'
   			, autoRow:true
    		, on:function(data){
    			var dataArray = data.arr;//data.arr:  当前多选已选中的数据
    			if(data.change){//data.change, 此次选择变化的数据,数组;//data.isAdd, 此次操作是新增还是删除
//     				console.log('data.change'+dataArray.length);
    				if(dataArray.length>0){
    					var dArray = new Array();
	    				for (var i = 0; i < dataArray.length; i++) {
	    					dArray.push(dataArray[i].id);
	    				}
	    				$('#'+hiddenTargetId).val(dArray.toString());
    				}else{
    					$('#'+hiddenTargetId).val('');
    				}
    			}
    		}
    	});
    	$.post('#(ctxPath)/mms/cardRoll/getAllBase', {}, function(rep) {
			if(rep.state=='ok'){
				var initValueArray = null;
				if(dataValue.indexOf(",")!=-1){
					initValueArray = dataValue.split(',');
					fitBaseXmSelectCheck.update({
						data:rep.baseList
						, initValue:initValueArray
					})
				}else{
					fitBaseXmSelectCheck.update({
						data:rep.baseList
						, initValue:[dataValue]
					})
				}
			}
		});
    };
    
	$('div[class="xm-select-demo"]').each(function(i,obj){
		var targetId = obj.id;
		var hiddenTargetId = $(obj).attr('hiddenTargetId')!=null?$(obj).attr('hiddenTargetId'):'';
		var dataValue = $(obj).attr('dataValue')!=null?$(obj).attr('dataValue'):'';
		if(targetId=='fitBranchOfficeXmSelectTree'){//适用分公司
			fitBranchOfficeSelectCheckLoad(targetId, hiddenTargetId, dataValue);
		}else if(targetId=='fitBaseXmSelectTree'){//适用基地
			fitBaseSelectCheckLoad(targetId, hiddenTargetId, dataValue);
		}
	});
	
	//监听表单提交
	form.on('submit(releaseBtn)', function(formObj) {
		$("#deployStatus").val('0');
		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/cardRoll/save',
            data: $(formObj.form).serialize(),
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("rollTable",null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		let typeCode = $("#rollTypeId").find('option[value="'+$("#rollTypeId").val()+'"]').attr("data-code");

		//提交表单数据
		util.sendAjax ({
            type: 'POST',
            url: '#(ctxPath)/mms/cardRoll/save',
            data: $(formObj.form).serialize()+"&typeCode="+typeCode,
            notice: true,
		    loadFlag: true,
            success : function(rep){
            	if(rep.state=='ok'){
            		pop_close();
            		parent.tableReload("rollTable",null);
            	}
            },
            complete : function() {
		    }
        });
		return false;
	});


	#if(formType=='see')
	$("#rollForm button").prop("disabled", true);
	$("#rollForm button").addClass("layui-btn-disabled")
	//$("#giveSchemeDetail").prop("disabled",false);
	//$("#giveSchemeDetail").removeClass("layui-btn-disabled");
	$("#rollForm .layui-form-footer").hide();
	$("#rollForm").find("input,select,textarea").prop("disabled", true).prop("readonly", true);
	$("#rollForm select").removeAttr("lay-search");
	form.render('select');
	form.render('radio');
	#end



});
</script>
#end