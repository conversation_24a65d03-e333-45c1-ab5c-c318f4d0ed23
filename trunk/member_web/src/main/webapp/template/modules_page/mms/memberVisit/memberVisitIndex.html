#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()会员档案回访首页#end

#define css()
#end

#define js()
<script type="text/html" id="visitTableBar">
	#shiroHasPermission("member:archive:visitEditBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:archive:visitDelBtn")
	<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</script>
<script type="text/javascript">
layui.config({
	base: '/static/js/extend/',
});
layui.use(['table','form','vip_table'],function(){
    
	// 操作对象
    var layer = layui.layer
        ,form = layui.form
        ,table = layui.table
        ,vipTable = layui.vip_table
        ,$ = layui.jquery
        ,tableId = 'visitTable'
        ;
    
	// 表格渲染
	var tableObj = table.render({
	    id: tableId
	    , elem: '#'+tableId                  //指定原始表格元素选择器（推荐id选择器）
	    , even: true //开启隔行背景
	    , url: '#(ctxPath)/mms/memberVisit/pageTable'
	    , method: 'post'
// 	    , height: vipTable.getFullHeight()    //容器高度
	    , where: {memberId:$('#memberId').val()}
	    , cols: [[                  //标题栏
	          {type: 'numbers', title: '序号', width: 60, unresize:true}
	        , {field: '', title: '回访类型', unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
	        , {field: 'visitContent', title: '回访内容', unresize:true}
	        , {field: '', title: '创建时间', unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
	        , {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#visitTableBar'} //这里的toolbar值是模板元素的选择器
	    ]]
	    , page: true
	    , loading: true
	    , done: function (res, curr, count) {
	    }
	});
	// 表格绑定事件
    table.on('tool('+tableId+')',function (obj) {
        if(obj.event==="edit"){//编辑按钮事件
        	pop_show('编辑','#(ctxPath)/mms/memberVisit/edit?id='+obj.data.id,'500','400');
        }else if(obj.event==="del"){//作废按钮事件
        	//作废操作
    		layer.confirm('确认作废？作废后不能恢复。', {icon:3, title:'提示'}, function(index){
                util.sendAjax ({
                    type: 'POST',
                    url: '#(ctxPath)/mms/memberVisit/del',
                    data: {id:obj.data.id, delFlag:'1'},
                    loadFlag: true,
                    success : function(rep){
                    	pageTableReload();
                    },
                    complete : function() {
        		    }
                });
				layer.close(index);
            });
        }
    });
    //重载表格
    pageTableReload = function () {
    	tableReload(tableId,{memberId:$('#memberId').val()});
    }
	//搜索按钮点击事件
	$('#searchBtn').on('click', function() {
		pageTableReload();
	});
	//添加按钮点击事件
	$('#addBtn').on('click', function() {
		var memberId = $('#memberId').val();
		pop_show('添加','#(ctxPath)/mms/memberVisit/add?memberId='+memberId,'500','400');
	});
	// 刷新
    $('#refreshBtn').on('click', function () {
    	pageTableReload();
    });
})
</script>
#end

#define content()
<div class="my-btn-box">
	<div class="layui-row">
	    <span class="fl">
			<form id="searchForm" class="layui-form layui-form-pane" action="">
		    	<div class="layui-inline">
			        <div class="layui-input-inline">
			            <div class="layui-btn-group">
							<input type="hidden" id="memberId" value="#(memberId)">
							#shiroHasPermission("member:archive:visitAddBtn")
						        <a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
							#end
					        <a type="button" id="refreshBtn" class="layui-btn btn-add btn-default"><i class="layui-icon">&#x1002;</i></a>
		    			</div>
			        </div>
	    		</div>
			</form>
	    </span>
    </div>
	<div class="layui-row">
		<table id="visitTable" lay-filter="visitTable"></table>
	</div>
</div>
#getDictLabel("visitType")
#end