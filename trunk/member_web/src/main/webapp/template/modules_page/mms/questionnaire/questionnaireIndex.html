#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()测量设备类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        questionnaireTableLoad(null);

        sd=form.on("submit(search)",function(data){
            questionnaireTableLoad(data.field);
            return false;
        });

        function questionnaireTableLoad(data){
            table.render({
                id : 'questionnaireTable'
                ,elem : '#questionnaireTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/questionnaire/questionnairePageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '问卷名称', align: 'center', unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{field:'createTime', title: '创建时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.createDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/questionnaire/questionnaireForm" ;
            layerShow("新增问卷",url,500,400);
        });

        table.on('tool(questionnaireTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/questionnaire/delQuestionnaire',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('questionnaireTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/questionnaire/questionnaireForm?id=" + obj.data.id ;
                layerShow("编辑问卷",url,500,400);
            }else if(obj.event==='question'){
                var url = "#(ctxPath)/mms/questionnaire/questionIndex?questionnaireId=" + obj.data.id ;
                layerShow("问卷问题",url,'100%','100%')
            }
        });

    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:questionnaire:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("member:questionManage")
    <a class="layui-btn layui-btn-xs" lay-event="question">问题</a>
    #end
    #shiroHasPermission("member:questionnaire:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            问卷名称:
            <div class="layui-inline">
                <input id="questionnaireName" name="name" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
        #shiroHasPermission("member:questionnaire:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="questionnaireTable" lay-filter="questionnaireTable"></table>
</div>
#getDictLabel("equipment_type")
#end