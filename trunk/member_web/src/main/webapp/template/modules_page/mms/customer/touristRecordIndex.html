#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()参团记录页面#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="margin-top:15px;">
		<div class="layui-inline">
			<input type="hidden" name="idcard" value="#(idcard??)">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="touristTable" lay-filter="touristTable"></table>
</div>
#end

#define js()
<script>
layui.use(['form','layer','table', 'laydate'], function() {
	var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate;

	function touristLoad(data){
		table.render({
		    id: 'touristTable'
		    , elem: '#touristTable'                  //指定原始表格元素选择器（推荐id选择器）
		    , even: true //开启隔行背景
		    , url: '#(ctxPath)/mms/customer/touristTable'
		    , method: 'post'
	    	, where : data
			, height:$(document).height()*0.8
		    , cols: [[                  //标题栏
		          {type: 'numbers', title: '序号', width: 60, unresize:true}
		        , {field: 'memberName', title: '客户姓名', width: 120, unresize:true}
				, {field: '', title: '回访时间', width: 120, unresize:true,templet:"<div>{{ dateFormat(d.visitDate,'yyyy-MM-dd HH:mm:ss') }}</div>"}
		        , {field: '', title: '回访类型', width: 120, unresize:true,templet:"<div>{{ dictLabel(d.visitType,'visitType','- -') }}</div>"}
		        , {field: '', title: '诉求类型', width: 120, unresize:true,templet:"<div>{{ dictLabel(d.appealType,'appealType','- -') }}</div>"}
		        , {field: '', title: '接通情况', width: 120, unresize:true,templet:"<div>{{ dictLabel(d.callPass,'callPass','- -') }}</div>"}
		        , {field: 'consultContent', title: '咨询受理内容', width: 200, unresize:true}
		        , {field: 'solution', title: '处理方式或方案', width: 200, unresize:true}
		        , {field: '', title: '跟进结果', width: 100, unresize:true,templet:"<div>{{ dictLabel(d.followResult,'followResult','- -') }}</div>"}
// 		        , {field: 'visitContent', title: '回访内容', unresize:true}
		        , {field: '', title: '客户满意度', width: 120, unresize:true,templet:"<div>{{d.baseName}}-{{ dictLabel(d.handleResult,'handleResult','- -') }}</div>"}
		        , {field: 'receptionUserName', title: '接待人', width: 120, unresize:true}
		        , {field: 'remarks', title: '备注', width: 300, unresize:true}
		        , {fixed: 'right', title: '操作', width: 120, align: 'center', toolbar: '#visitTableBar'} //这里的toolbar值是模板元素的选择器
		    ]]
		    , page: true
		    , limit : 10
			, limits : [10,20,30,40]
		    , loading: true
		    , done: function (res, curr, count) {
		    }
		});
	};
	
	touristLoad({idcard:'#(idcard??)'});
	
	form.on("submit(search)",function(data){
		touristLoad(data.field);
		return false;
	});
});
</script>
#end