#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人脸模板管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;


        smsLoad(null);

        $("#search").click(function () {
            ssmReload();
        });

        ssmReload=function(){
            smsLoad({'typeName':$("#typeName").val()});
        }

        // 添加会员档案&人脸
        $("#add").click(function(){
            var url = "#(ctxPath)/mms/smsType/form";
            pop_show("添加",url,600,500);
        });

        function smsLoad(data){
            table.render({
                id : 'smsTable'
                ,elem : '#smsTable'
                ,method : 'POST'
                ,where : data
                ,height:$(window).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/smsType/tablePage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'typeName', title: '短信类型名称', align: 'center', unresize: true}
                    ,{field:'sendingMethod', title: '发送方式', align: 'center', unresize: true,templet:"<div>{{ dictLabel(d.sendingMethod,'sending_method','- -') }}</div>"}
                    ,{field:'everyDaySendContent', title: '每天发送方式短信内容', align: 'center', unresize: true}
                    ,{field:'remark', title: '备注', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
        ]]
            //,page : true
        });
        };


        table.on('tool(smsTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/smsType/del',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                ssmReload();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/smsType/form?id="+obj.data.id;
                pop_show("编辑",url,600,500);
            }
        });


    })
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:smsType:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end
    #shiroHasPermission("member:smsType:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <div class="demoTable">
            <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
                类型名称:
                <div class="layui-inline">
                    <input class="layui-input" id="typeName" >
                </div>
                <!--&nbsp;
                会员卡类型:
                <div class="layui-inline">
                    <input id="cardTypeName" name="cardTypeName" class="layui-input">
                </div>-->
                <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" type="button" id="search">查询</button>
            </form>
            #shiroHasPermission("member:smsType:addBtn")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
            #end
        </div>
    </div>
    <table id="smsTable" lay-filter="smsTable"></table>
</div>
#getDictLabel("sending_method")
#end