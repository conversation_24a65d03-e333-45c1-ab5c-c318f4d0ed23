#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人脸模板管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        faceLoad(null);

        sd=form.on("submit(search)",function(data){
            faceLoad(data.field);
            return false;
        });


        // 上传人脸
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/memberface/uploadFaceForm" ;
            pop_show("上传人脸照片",url,900,500);
        });

        function faceLoad(data){
            table.render({
                id : 'faceTable'
                ,elem : '#faceTable'
                ,method : 'POST'
                ,where : data
                ,height:$(window).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/memberface/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type:'checkbox'},
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'fullName', title: '姓名', align: 'center', unresize: true}
                    #shiroHasPermission("member:idcardSee")
                    ,{field:'idcard', title: '身份证号', align: 'center', unresize: true}
                    #end
                    ,{field:'verifyFlag', title: '审核状态', align: 'center', unresize: true,templet:"<div>{{d.verifyFlag == '0'?'<span class='layui-badge layui-bg-gray'>未审核</span>':d.verifyFlag == '1'?'<span class='layui-badge layui-bg-green'>审核通过</span>':d.verifyFlag == '2'?'<span class='layui-badge'>审核不通过</span>':'未上传'}}</div>"}
                    ,{field:'collectTime', title: '提交时间', align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.collectTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{field:'verifyDescribe', title: '审核说明', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };


        table.on('tool(faceTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废人脸吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/memberface/delete',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('faceTable',null);
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'detail'){
                var url = "#(ctxPath)/mms/memberface/faceForm?memberId=" + obj.data.memberId +"&faceId="+ obj.data.id;
                pop_show("查看人脸照片",url,700,500);
            }else if(obj.event === 'uploadFace'){
                var url = "#(ctxPath)/mms/memberface/faceForm?memberId=" + obj.data.memberId;
                pop_show("上传人脸照片",url,700,500);
            }else if(obj.event === 'manualAudit'){
                var url = "#(ctxPath)/mms/memberface/examineForm?id=" + obj.data.id ;
                pop_show("人工审核",url,900,500);
            }
        });


        //扫描身份证
        $("#idcardSearch").on('click',function(){
            util.sendAjax({
                url:'http://127.0.0.1:1500/Service?type=IDCard&method=GetCardInfo',
                type:'post',
                data:JSON.stringify({}),
                success : function (result) {
                    var data=result.Data;
                    if(result.Type===1){
                        var data = {"fullName":data.name,"idcard":data.cardID};
                        faceLoad(data);
                    }else{
                        layer.msg('读取失败',{icon:5,time:5000});
                    }
                }
            });
            return false;
        });
    })
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:faceExamine:examine")
    #[[
    {{#if(d.verifyFlag == '0' || d.verifyFlag == '2'){}}
    <a class="layui-btn layui-btn-xs" lay-event="manualAudit">人工审核</a>
    {{#}}}
    ]]#
    #end

    #shiroHasPermission("member:faceExamine:check")
    #[[
    {{#if(d.verifyFlag != undefined){}}
    <a class="layui-btn layui-btn-xs" lay-event="detail">查看</a>
    {{#}}}
    ]]#
    #end
    #shiroHasPermission("member:faceExamine:del")
    #[[
    {{#if(d.verifyFlag != undefined){}}
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    {{#}}}
    ]]#
    #end
</script>
#end

#define content()
<div>
  <div class="layui-row">
    <div class="demoTable">
        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            姓名:
            <div class="layui-inline">
                <input id="fullName" name="fullName" class="layui-input">
            </div>
            &nbsp;&nbsp;
            身份证:
            <div class="layui-inline">
                <input id="idcard" name="idcard" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
            <button class="layui-btn" type="button" style="padding: 0 10px;border-radius: 5px;" id="idcardSearch">刷身份证查询</button>
        </form>
        #shiroHasPermission("member:faceExamine:uploadFace")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">上传人脸</button>
        #end
    </div>
  </div>
    <table id="faceTable" lay-filter="faceTable"></table>
</div>
#end