#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()人脸模板管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        smsCardTypeTableLoad({"smsId":$("#smsId").val()});


        smsCardTypeTableReLoad=function(data){
            smsCardTypeTableLoad({"smsId":$("#smsId").val()});
        }

        $("#add").click(function(){
            var url = "#(ctxPath)/mms/sms/smsCardTypeForm?smsId="+$("#smsId").val();
            pop_show("添加",url,1000,600);
        });

        function smsCardTypeTableLoad(data){
            table.render({
                id : 'smsCardTypeTable'
                ,elem : '#smsCardTypeTable'
                ,method : 'POST'
                ,where : data
                ,height:$(window).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/sms/smsCardTypePageList'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'cardTypeName', title: '会员卡类型', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };


        table.on('tool(smsCardTypeTable)',function(obj) {
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/sms/smsCardTypeDel',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                smsCardTypeTableReLoad();
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }
        });
    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:sms:cardTypeDelBtn")
    #if(examineStatus=="0")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
    #end
</script>
#end

#define content()
<div>
    <div class="layui-row">
        <div class="demoTable">
            <input type="hidden" id="smsId" value="#(smsId??)">
            #shiroHasPermission("member:sms:cardTypeAddBtn")
            #if(examineStatus=="0")
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加会员卡类型</button>
            #end
            #end
        </div>
    </div>
    <table id="smsCardTypeTable" lay-filter="smsCardTypeTable"></table>
</div>
#end