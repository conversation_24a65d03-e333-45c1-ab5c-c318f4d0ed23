#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()客户信息首页#end

#define css()
#end

#define content()
<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
	<div class="layui-row">
		<div class="layui-inline">
			<label class="layui-form-label">姓名</label>
			<div class="layui-input-inline">
				<input id="memberName" name="memberName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">身份证</label>
			<div class="layui-input-inline">
				<input id="idcard" name="idcard" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">手机号</label>
			<div class="layui-input-inline">
				<input id="phoneNumber" name="phoneNumber" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">客户类别</label>
			<div class="layui-input-inline" style="width:100px;">
				<select name="customerType">
					<option value="">请选择</option>
					<option value="member" >会员</option>
					<option value="not_member" >非会员</option>
				</select>
			</div>
		</div>
	</div>
	<div class="layui-row" style="margin-top:5px;">
		<div class="layui-inline">
			<label class="layui-form-label">开始时间</label>
			<div class="layui-input-inline">
				<input id="startDate" name="startDate" class="layui-input" placeholder="请选择创建开始日期">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">结束时间</label>
			<div class="layui-input-inline">
				<input id="endDate" name="endDate" class="layui-input" placeholder="请选择创建结束日期">
			</div>
		</div>
		<div class="layui-inline">
			<label class="layui-form-label">是否精确查询添加</label>
			<div class="layui-input-inline" style="width:150px;">
				<select name="isPreciseQuery">
					<option value="">全部</option>
					<option value="0">否</option>
					<option value="1">是</option>
				</select>
			</div>
		</div>
		<div class="layui-inline">
			#shiroHasPermission("member:info:queryMyData")
	        	<input type="hidden" name="updateBy" class="layui-input" value="#(userId??)">
			#end
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("member:info:addBtn")
	        	<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
			#end
			#shiroHasPermission("member:info:newCountBtn")
				<button type="button" id="newBtn" class="layui-btn">昨日新增<span class="layui-badge">#(newCount??0)</span></button>
			#end
		</div>
	</div>
</form>
<div class="layui-row">
	<table id="infoTable" lay-filter="infoTable"></table>
</div>
#getDictLabel("gender")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("member:info:editBtn")
	<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("member:info:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table', 'laydate'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer, laydate = layui.laydate,queryData={};

		#shiroHasPermission("member:info:queryMyData")
	    	queryData={updateBy:'#(userId??)'};
		#end
		customerLoad(queryData);
		
		//时间渲染
		laydate.render({
			elem: '#startDate'
			, format: 'yyyy-MM-dd'
		});
		
		//时间渲染
		laydate.render({
			elem: '#endDate'
			, format: 'yyyy-MM-dd'
		});

		function customerLoad(data){
			table.render({
				id : 'infoTable'
				,elem : '#infoTable'
				,method : 'POST'
				,where : data
				,url : '#(ctxPath)/mms/customer/pageTable'
				,cellMinWidth: 80
				,height:'full-200'
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field:'memberName', title: '客户姓名', align: 'center', width: 100, unresize: true}
					,{field:'', title: '性别', align: 'center', width: 100, unresize: true,templet:"<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
					,{field:'birthday', title: '出生日期', align: 'center', width: 120, unresize: true}
					,{field:'idcard', title: '身份证号', align: 'center', width: 180, unresize: true}
					#shiroHasPermission("member:telephoneSee")
					,{field:'phoneNumber', title: '电话号码', align: 'center', width: 120, unresize: true}
					#end
					,{field:'provinceName', title: '省', align: 'center', width: 100, unresize: true}
					,{field:'cityName', title: '市/县', align: 'center', width: 100, unresize: true}
					,{field:'townName', title: '镇/区', align: 'center', width: 100, unresize: true}
					,{field:'streetName', title: '街道', align: 'center', width: 100, unresize: true}
					,{field:'address', title: '地址', align: 'center', width: 200, unresize: true}
					,{field:'belongOrgName', title: '所属组织', align: 'center', width: 100, unresize: true}
					#if(lacksPermission("member:info:queryMyData"))
					,{field:'createName', title: '创建人', align: 'center', width: 100, unresize: true}
					#end
					,{field: '', title: '是否精确查询添加', width:150,unresize:true,templet:"<div>{{d.isPreciseQuery == '0'? '<span class='layui-badge'>否</span>':d.isPreciseQuery == '1'? '<span class='layui-badge layui-bg-green'>是</span>':'- -'}}</div>"}
					,{field: '', title: '创建时间', width: 180, unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 15
				,limits : [15,25,35,45]
			});
		};
		table.on('tool(infoTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/customer/edit?id="+obj.data.id;
				pop_show("编辑",url,'','');
			}else if(obj.event === 'del'){
				layer.confirm("确定作废?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/mms/customer/delete",
						type:'post',
						data:{"id":obj.data.id, delFlag:'1'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('infoTable');
							}
							layer.close(index);
						}
					});
				});
			}
		});
		
		form.on("submit(search)",function(data){
			customerLoad(data.field);
			return false;
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/mms/customer/add','','');
		});
		
		
		//昨日新增按钮点击事件
		$('#newBtn').on('click', function() {
			pop_show('新增统计','#(ctxPath)/mms/customer/newCount','','');
		});
	});
</script>
#end