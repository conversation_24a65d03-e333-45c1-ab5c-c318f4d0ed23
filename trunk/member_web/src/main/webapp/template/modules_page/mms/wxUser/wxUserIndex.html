#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()微信用户管理#end

#define css()
#end

#define content()
<div style="margin: 15px;">
    <div class="demoTable">

        <form class="layui-form" action="" lay-filter="layform" id="frm" method="post">
            昵称:
            <div class="layui-inline">
                <input id="nickName" name="nickName" class="layui-input">
            </div>
            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>
    </div>
    <table id="wxUserTable" lay-filter="wxUserTable"></table>
</div>
#getDictLabel("gender")
#end
<!-- 公共JS文件 -->
#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        wxUserLoad(null);

        sd=form.on("submit(search)",function(data){
            wxUserLoad(data.field);
            return false;
        });


        function wxUserLoad(data){
            table.render({
                id : 'wxUserTable'
                ,elem : '#wxUserTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/wxuser/findListPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'openid', title: 'OPENID', align: 'center', unresize: true}
                    ,{field:'avatarUrl', title: '头像', align: 'center', unresize: true,templet:function (d) {
                            var str="- -";
                            var imgUrl=d.avatarUrl;
                            if(imgUrl!=null && imgUrl!="" && typeof(imgUrl)!="undefined"){
                                var img=imgUrl;
                                var str='<img src="'+img+'" height="30px"/>';
                            }
                            return str;
                        }}
                    ,{field:'nickName', title: '昵称', align: 'center', unresize: true}
                    ,{field:'gender', title: '性别', align: 'center', unresize: true,templet: "<div>{{ dictLabel(d.gender,'gender','- -') }}</div>"}
                    ,{field:'country', title: '地区', align: 'center', unresize: true, templet: "<div>{{ splicingArea(d) }}</div>"}
                    ,{field:'regTime', title: '注册时间', sort: true, align: 'center', unresize: true,templet:"<div>{{ dateFormat(d.regTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
                    ,{fixed:'right', title: '操作', width: 120, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });

        };
        // 监听操作栏目
        table.on('tool(wxUserTable)',function(obj){
            if (obj.event === 'view') { // 编辑
                pop_show("查看微信用户信息" ,"#(ctxPath)/mms/wxuser/form?id="+obj.data.id ,'500' ,'600') ;
            }
        });

        //拼接地区
        splicingArea = function(d){
            var area = "";
            if(d.country != null){
                area += d.country;
            }
            if(d.province != null){
                area += d.province;
            }
            if(d.city != null){
                area += d.city;
            }
            return area;
        }
    });
</script>
<!-- 操作栏替换模板 -->
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:weiXinUser:check")
    <a class="layui-btn layui-btn-xs" lay-event="view">查看</a>
    #end
</script>
#end