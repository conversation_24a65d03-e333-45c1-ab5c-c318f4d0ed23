#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券类型首页#end

#define css()
#end

#define content()
<div class="layui-row">
	<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
		<div class="layui-inline">
			<label class="layui-form-label">类型名称</label>
			<div class="layui-input-inline">
				<input id="typeName" name="typeName" class="layui-input">
			</div>
		</div>
		<div class="layui-inline">
			<button class="layui-btn" lay-submit="" lay-filter="search">查询</button>
			#shiroHasPermission("crm:cardRollType:addBtn")
        		<a type="button" id="addBtn" class="layui-btn btn-add btn-default">添加</a>
			#end
		</div>
	</form>
</div>
<div class="layui-row">
	<table id="typeTable" lay-filter="typeTable"></table>
</div>
#getDictLabel("gender")
#end

#define js()
<script type="text/html" id="actionBar">
<div class="layui-btn-group">
	#shiroHasPermission("crm:cardRollType:editBtn")
		<a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
	#end
	#shiroHasPermission("crm:cardRollType:delBtn")
		<a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
	#end
</div>
</script>
<script>
	layui.use(['form','layer','table'], function() {
		var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

		typeLoad(null);

		function typeLoad(data){
			table.render({
				id : 'typeTable'
				,elem : '#typeTable'
				,method : 'POST'
				,where : data
				,url : '#(ctxPath)/mms/cardRollType/pageTable'
				,cellMinWidth: 80
				,width:$(document).width()
				,height:'full-200'
				,cols: [[
					{type: 'numbers', title: '序号', width: 60, unresize:true}
					,{field: 'rollTypeEnum', title: '类型名称', align: 'center', unresize: true,templet:function (d) {
							if(d.rollTypeEnumName==null || d.rollTypeEnumName==undefined){
								return '';
							}
							return d.rollTypeEnumName;
						}}
					,{field:'typeName', title: '类型名称', align: 'center', unresize: true}
					,{field: '', title: '是否可用',unresize:true,templet:"<div>{{d.isEnable == '0'? '<span class='layui-badge layui-bg-green'>可用</span>':d.isEnable == '1'? '<span class='layui-badge'>不可用</span>':'- -'}}</div>"}
					,{field: '', title: '创建时间', unresize:true,templet:"<div>{{ dateFormat(d.createTime,'yyyy-MM-dd HH:mm:ss') }}</div>"}
					,{fixed:'right', title: '操作', width: 140, align: 'center', unresize: true, toolbar: '#actionBar'}
				]]
				,page : true
				,limit : 15
				,limits : [15,25,35,45]
			});
		};
		table.on('tool(typeTable)',function(obj){
			if(obj.event === 'edit'){
				var url = "#(ctxPath)/mms/cardRollType/edit?id="+obj.data.id;
				pop_show("编辑",url,'400','400');
			}else if(obj.event === 'del'){
				layer.confirm("确定作废?",function(index){
					util.sendAjax({
						url:"#(ctxPath)/mms/cardRollType/delete",
						type:'post',
						data:{"id":obj.data.id, delFlag:'1'},
						notice:true,
						success:function(returnData){
							if(returnData.state==='ok'){
								table.reload('typeTable');
							}
							layer.close(index);
						}
					});
				});
			}
		});
		
		form.on("submit(search)",function(data){
			typeLoad(data.field);
			return false;
		});
		
		//添加按钮点击事件
		$('#addBtn').on('click', function() {
			pop_show('添加','#(ctxPath)/mms/cardRollType/add','400','400');
		});
		
		
	});
</script>
#end