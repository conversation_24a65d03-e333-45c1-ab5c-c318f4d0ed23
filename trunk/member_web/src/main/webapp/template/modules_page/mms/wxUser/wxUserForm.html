#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()微信用户详情#end

#define css()
#end

#define content()
<div style="margin: 15px;" >
    <div class="layui-contents">
        <form class="layui-form" action="" method="post">

            <div class="layui-form-item">
                <label class="layui-form-label">头像</label>
                <div class="layui-input-block">
                    <img src="#(wxUser.avatarUrl)"  height="80" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">昵称</label>
                <div class="layui-input-block">
                    <input class="layui-input" type="text" value="#(wxUser.nickName??)" readonly="readonly" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">性别</label>
                <div class="layui-input-block">
                    <input class="layui-input" type="text" value="#(wxUser.gender=='0'?'未知':(wxUser.gender=='1'?'男':'女'))" readonly="readonly" />
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label">地区</label>
                <div class="layui-input-block">
                    <input class="layui-input" type="text" id="area" value="#if(wxUser.country!=null)#(wxUser.country) #end #if(wxUser.province!=null)#(wxUser.province) #end #if(wxUser.city!=null)#(wxUser.city) #end" readonly="readonly" />
                </div>
            </div>

            <div class="layui-form-footer">
                <div class="pull-right">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end
<!-- 公共JS文件 -->
#define js()
<script>
    layui.use(['form','laytpl','layer'], function() {
        var $ = layui.$, form=layui.form, laytpl=layui.laytpl,layer=layui.layer;
    });
</script>
#end