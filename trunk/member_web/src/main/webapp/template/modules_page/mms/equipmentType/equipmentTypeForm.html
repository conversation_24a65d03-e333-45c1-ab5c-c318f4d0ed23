#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()测量设备类型信息展示#end

#define css()

#end

#define content()
<div style="margin: 15px;">
    <div class="demoTable">
        <form class="layui-form layui-form-pane" action="" method="post" id="equipmentTypeForm" style="margin-left:10px;">
            <div class="layui-form-item">
                <label class="layui-form-label">厂家</label>
                <div class="layui-input-inline">
                    <input type="text" name="et.manufactor" class="layui-input" value="#(et.manufactor??)" lay-verify="required" placeholder="请输入厂家">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">设备型号</label>
                <div class="layui-input-inline">
                    <input type="text" name="et.equipmentModel" class="layui-input" lay-verify="required" value="#(et.equipmentModel??)" placeholder="请输入设备型号">
                </div>
                <label class="layui-form-label">设备类型</label>
                <div class="layui-input-inline">
                    <select name="et.equipmentType" lay-search lay-verify="required">
                        <option value="">请选择设备类型</option>
                        #for(b : typeList)
                        <option value="#(b.dictValue)" #(et != null ? (b.dictValue == et.equipmentType?'selected':'') :'')>#(b.dictName)</option>
                        #end
                    </select>
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">设备分类</label>
                <div class="layui-input-inline">
                    <select name="et.equipmentClassify" lay-verify="required">
                        <option value="0" #(et != null ?(et.equipmentClassify == '0' ? 'selected':''):'selected')>个人</option>
                        <option value="1" #(et != null ?(et.equipmentClassify == '1' ? 'selected':''):'')>多人</option>
                    </select>
                </div>
                <label class="layui-form-label">设备颜色</label>
                <div class="layui-input-inline">
                    <input type="text" name="et.equipmentColor" class="layui-input" lay-verify="required" value="#(et.equipmentColor??)" placeholder="请输入设备颜色">
                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">备注</label>
                <div class="layui-input-block">
                    <textarea name="et.remark" class="layui-textarea" rows="5" placeholder="请输入备注">#(et.remark??)</textarea>
                </div>
            </div>
            <div class="layui-form-footer">
                <div class="pull-right">
                    <input type="hidden" name="et.id" value="#(et.id??)">
                    <button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
                    <button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
                </div>
            </div>
        </form>
    </div>
</div>
#end

#define js()
<script type="text/javascript">
    layui.use(['form','jquery'], function(){
        var form = layui.form,$ = layui.jquery;
        //保存
        form.on('submit(saveBtn)', function(){
            var url = "#(ctxPath)/mms/equipmentType/save";
            util.sendAjax ({
                type: 'POST',
                url: url,
                data: $("#equipmentTypeForm").serialize(),
                notice: true,
                loadFlag: false,
                success : function(rep){
                    if(rep.state=='ok'){
                        pop_close();
                        parent.tableReload("equipmentTypeTable",null);
                    }
                },
                complete : function() {
                }
            });
            return false;
        });
    });
</script>
#end
