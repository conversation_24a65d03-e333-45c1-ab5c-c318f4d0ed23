#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()卡券核销添加页面#end

#define css()
#end

#define content()
<div class="layui-row">
<form class="layui-form layui-form-pane">
	<div class="layui-form-item">
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label"><font color="red">*</font>核销日期</label>
			<div class="layui-input-block">
				#if(model.verifyTime??'' == '')
					<input type="text" id="verifyTime" name="verifyTime" class="layui-input" lay-verify="required" value="#(model.verifyTime??'')" placeholder="请选择核销日期" autocomplete="off">
				#else
					<input type="text" id="verifyTime" name="verifyTime" class="layui-input" lay-verify="required" value="#date(model.verifyTime??'', 'yyyy-MM-dd HH:mm:ss')" placeholder="请选择核销日期" autocomplete="off">
				#end
			</div>
		</div>
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label">核销状态</label>
	        <div class="layui-input-block">
	            <input type="radio" name="verifyStatus" value="0" title="未核销" #if(model!=null && model.verifyStatus=='0') checked #elseif(model==null) checked #end>
	            <input type="radio" name="verifyStatus" value="1" title="已核销" #if(model.verifyStatus??=='1') checked #end>
	        </div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label"><font color="red">*</font>活动名称</label>
			<div class="layui-input-block">
				<input type="text" id="activityName" name="activityName" class="layui-input" lay-verify="required" value="#(model.activityName??)" placeholder="请输入活动名称" autocomplete="off">
			</div>
		</div>
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label"><font color="red">*</font>姓名</label>
			<div class="layui-input-block">
				<input type="text" id="fullName" name="fullName" class="layui-input" lay-verify="required" value="#(model.fullName??)" placeholder="请输入姓名" autocomplete="off">
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label"><font color="red">*</font>身份证号</label>
			<div class="layui-input-block">
				<input type="text" id="idcard" name="idcard" class="layui-input" lay-verify="required|identity" value="#(model.idcard??)" placeholder="请输入身份证号" autocomplete="off">
			</div>
		</div>
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label"><font color="red">*</font>手机号码</label>
			<div class="layui-input-block">
				<input type="text" id="phoneNumber" name="phoneNumber" class="layui-input" lay-verify="required|phone" value="#(model.phoneNumber??)" placeholder="请输入手机号码" autocomplete="off">
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label">分公司</label>
			<div class="layui-input-block">
				<select id="branchOfficeId" name="branchOfficeId" lay-search>
					<option value="">请选择</option>
					#for(b : boList)
						<option value="#(b.id)" #(b.id == model.branchOfficeId?'selected':'')>#(b.shortName)</option>
					#end
				</select>
			</div>
		</div>
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label">基地</label>
			<div class="layui-input-block">
				<select id="baseId" name="baseId" lay-search>
					<option value="">请选择</option>
					#for(b : baseList)
						<option value="#(b.id)" #(b.id == model.baseId?'selected':'')>#(b.baseName)</option>
					#end
				</select>
			</div>
		</div>
	</div>
	<div class="layui-form-item">
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
	        <label class="layui-form-label">是否会员</label>
	        <div class="layui-input-block">
	            <input type="radio" name="isMember" value="0" title="否" #if(model!=null && model.isMember=='0') checked #elseif(model==null) checked #end>
	            <input type="radio" name="isMember" value="1" title="是" #if(model.isMember??=='1') checked #end>
	        </div>
		</div>
		<div class="layui-col-xs6 layui-col-sm6 layui-col-md4">
			<label class="layui-form-label">会员卡号</label>
			<div class="layui-input-block">
				<input type="text" id="cardNumber" name="cardNumber" class="layui-input" value="#(model.cardNumber??)" placeholder="请输入会员卡号" autocomplete="off">
			</div>
		</div>
	</div>
    <div class="layui-form-item">
    	<table class="layui-table" lay-size="sm">
			<thead>
				<tr>
					<th width="10%">序号</th>
					<th width="80%">卡券编号</th>
					<th width="10%">操作</th>
				</tr>
			</thead>
			<tbody id="numberList">
			</tbody>
		</table>
	</div>
	<div class="layui-row" style="margin-bottom:60px;"></div>
	<div class="layui-form-footer">
		<div class="pull-left">
			<div class="layui-form-mid layui-word-aux">说明：前面有<font color="red">*</font>的字段为必填字段。</div>
		</div>
		<div class="pull-right">
			<input type="hidden" name="id" value="#(model.Id??)">
			<input type="hidden" id="rollNumberCount" name="rollNumberCount" value="0">
			<div id="addRollNumberBtn" class="layui-btn">添加卡券</div>
			<button class="layui-btn" lay-submit="" lay-filter="saveBtn">保&nbsp;&nbsp;存</button>
			<button class="layui-btn layui-btn-danger" onclick="pop_close();">关&nbsp;&nbsp;闭</button>
		</div>
	</div>
</form>
</div>
#end

#define js()
<script id="rollNumberTrTpl" type="text/html">
	<tr id="rollNumber-{{d.idx}}">
		<td>{{d.idx}}</td>
		<td><input type="text" name="rollNumberList[{{d.idx}}].rollNumber" class="layui-input" value="{{d.rollNumber}}" lay-verify="required" placeholder="请输入卡券编号" autocomplete="off"></td>
		<td>
			<a class="layui-btn layui-btn-danger layui-btn-xs" onclick="del('rollNumber-{{d.idx}}')">作废</a>
		</td>
	</tr>
</script>
<script type="text/javascript">
layui.use([ 'form', 'laydate','laytpl' ], function() {
	var form = layui.form
	, laytpl = layui.laytpl
	, laydate = layui.laydate
	, $ = layui.jquery
	;
	
	//时间渲染
	laydate.render({
		elem: '#verifyTime'
		, format: 'yyyy-MM-dd HH:mm:ss'
	});
	
	//添加模板方法
	addTpl = function(targetId, addTpl, idx, rollNumber) {
		$('#rollNumberCount').val(parseInt(idx)+1);
		laytpl(addTpl).render({"idx":(parseInt(idx)+1), "rollNumber":rollNumber}, function(html){
			targetId.append(html);
		});
    };
    
    var rollNumberJson = parent.getCheckTableData();
    if(rollNumberJson!=null && rollNumberJson!=''){
    	var rollNumberArray = $.parseJSON(JSON.stringify(rollNumberJson));
    	$.each(rollNumberArray, function (i,r){
    		addTpl($('#numberList'), rollNumberTrTpl.innerHTML, $('#rollNumberCount').val(),r.rollNumber);
		});
    }
    

	//添加按钮点击事件
	$('#addRollNumberBtn').on('click', function() {
		addTpl($('#numberList'), rollNumberTrTpl.innerHTML, $('#rollNumberCount').val(),'');
	});
	
	//删除方法
	del = function(trId) {
		$("#"+trId).remove();
	};
	
	//监听表单提交
	form.on('submit(saveBtn)', function(formObj) {
		var trLength = $('#numberList tr').length;
		if(trLength>0){
			//提交表单数据
			util.sendAjax ({
	            type: 'POST',
	            url: '#(ctxPath)/mms/cardRoll/verifySave',
	            data: $(formObj.form).serialize(),
	            notice: true,
			    loadFlag: true,
	            success : function(rep){
	            	if(rep.state=='ok'){
	            		pop_close();
	            		parent.tableReload("verifyTable",null);
	            	}
	            },
	            complete : function() {
			    }
	        });
		}else{
			layer.msg('请添加卡券!',{icon:5});
		}
		return false;
	});
});
</script>
#end