#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()测量设备类型管理#end

#define css()
#end

#define js()
<script>
    layui.use(['form','layer','table'], function() {
        var table = layui.table, $ = layui.$, form=layui.form,layer=layui.layer;

        optionTableLoad({'questionId':$("#questionId").val()});

        sd=form.on("submit(search)",function(data){
            optionTableLoad(data.field);
            return false;
        });

        function optionTableLoad(data){
            table.render({
                id : 'optionTable'
                ,elem : '#optionTable'
                ,method : 'POST'
                ,where : data
                ,height:$(document).height()*0.8
                ,limit : 10
                ,limits : [10,20,30,40]
                ,url : '#(ctxPath)/mms/questionnaire/optionPage'
                ,cellMinWidth: 80
                ,cols: [[
                    {type: 'numbers', width:100, title: '序号',unresize:true}
                    ,{field:'name', title: '选项名称', align: 'center', unresize: true}
                    ,{fixed:'right', title: '操作', width: 200, align: 'center', unresize: true, toolbar: '#actionBar'}
                ]]
                ,page : true
            });
        };
        // 添加
        $("#add").click(function(){
            $(this).blur();
            var url = "#(ctxPath)/mms/questionnaire/optionForm?questionId="+$("#questionId").val() ;
            layerShow("新增选项",url,500,450);
        });

        table.on('tool(optionTable)',function(obj){
            if (obj.event === 'del') {
                layer.confirm("确定要作废吗?",function(index){
                    util.sendAjax ({
                        type: 'POST',
                        url: '#(ctxPath)/mms/questionnaire/delOption',
                        notice: true,
                        data: {id:obj.data.id},
                        loadFlag: true,
                        success : function(rep){
                            if(rep.state=='ok'){
                                tableReload('optionTable',{'questionId':$("#questionId").val()});
                            }
                            layer.close(index);
                        },
                        complete : function() {
                        }
                    });
                });
            }else if(obj.event === 'edit'){
                var url = "#(ctxPath)/mms/questionnaire/optionForm?id=" + obj.data.id+"&questionId="+$("#questionId").val() ;
                layerShow("编辑选项",url,500,450);
            }
        });

    });
</script>
<script type="text/html" id="actionBar">
    #shiroHasPermission("member:option:editBtn")
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    #end

    #shiroHasPermission("member:option:delBtn")
    <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del">作废</a>
    #end
</script>
#end

#define content()
<div>
    <div class="demoTable layui-row">
        <!--<form class="layui-form" action="" lay-filter="layform" id="frm" method="post" style="float:left;margin-top:15px;margin-left: 10px;">
            问题名称:
            <div class="layui-inline">
                <input id="title" name="title" class="layui-input">
            </div>

            <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;" lay-submit="" lay-filter="search">查询</button>
        </form>-->
        <input type="hidden" id="questionId" name="questionId" value="#(questionId??)">
        #shiroHasPermission("member:option:addBtn")
        <button class="layui-btn" style="padding: 0 10px;border-radius: 5px;margin-left: 10px;margin-top:15px;" id="add">添加</button>
        #end
    </div>
    <table id="optionTable" lay-filter="optionTable"></table>
</div>
#getDictLabel("equipment_type")
#end