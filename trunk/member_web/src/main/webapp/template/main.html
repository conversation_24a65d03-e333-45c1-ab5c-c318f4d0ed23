#include("/template/common/layout/_page_layout.html")
#@layout()

#define pageTitle()#(APP.name)#end

#define css()
<link rel="stylesheet" href="#(ctxPath)/static/plugins/font-awesome/css/font-awesome.min.css"/>
<style>
	.layui-nav .layui-badge, .layui-nav .layui-badge-dot {
		position: absolute;
		right: 18px;
		top: 29px;
	}
</style>
#end

#define js()
<script type="text/javascript" src="#(ctxPath)/static/js/vip_comm.js"></script>
<script type="text/javascript">
layui.use(['layer','vip_nav'], function () {
	// 操作对象
	var layer = layui.layer
		,vipNav = layui.vip_nav
	    ,$ = layui.jquery
	    ;
//	 	顶部左侧菜单生成 [请求地址,过滤ID,是否展开,携带参数]
//	     vipNav.top_left('#(ctxPath)/system/res/menuTop','side-top-left',false);
//	     you code ...

		$('#infoBtn').on('click', function() {
			pop_show('个人资料', '#(ctxPath)/orgEmployee/info', 650, 600);
		});
		$('#modPwdBtn').on('click', function() {
			pop_show('修改密码', '#(ctxPath)/user/modifyPwd', 450, 300);
		});

	//定时器推送未读的消息
	getUnReadMessageCount = function(){
		util.sendAjax({
			url:'#(ctxPath)/mms/msg/getNotReadMsgCount',
			type:'post',
			data:JSON.stringify({}),
			notice:false,
			success : function (result) {
				if(result.state == 'ok' && result.notReadMsgCount > 0){
					//$("#unReadMessageCount").html(result.notReadMsgCount);
					$("#unReadMessageCount").addClass("layui-badge-dot");
				}else{
					if($("#unReadMessageCount").hasClass("layui-badge-dot")){
						$("#unReadMessageCount").removeClass("layui-badge-dot");
					}
				}
			}
		});
	}

	$(function(){
		//getUnReadMessageCount();
	});

	//setInterval("getUnReadMessageCount();",10000);
	});
</script>
#end

#define content()
<div class="layui-layout layui-layout-admin">
	<!-- 添加skin-1类可手动修改主题为纯白，添加skin-2类可手动修改主题为蓝白 -->
	<!-- header -->
	<div class="layui-header my-header">
		<a href="#(ctxPath)">
			<div class="my-header-logo">#(APP.name)</div>
		</a>
		<div class="my-header-btn">
			<button id="showHideBtn" class="layui-btn layui-btn-small btn-nav">
				<i class="layui-icon">&#xe65f;</i>
			</button>
		</div>
		
		<!-- 顶部左侧添加选项卡监听 -->
		<ul class="layui-nav" lay-filter="side-top-left"></ul>
		
		<!-- 顶部右侧添加选项卡监听 -->
		<ul class="layui-nav my-header-user-nav" lay-filter="side-top-right">
<!-- 			<li class="layui-nav-item" style="margin-right:10px;"> -->
<!-- 				<a class="name" href="javascript:;"> -->
<!-- 					<i class="layui-icon">&#xe629;</i>主题 -->
<!-- 				</a> -->
<!-- 				<dl class="layui-nav-child"> -->
<!-- 					<dd data-skin="0"> -->
<!-- 						<a href="javascript:;">默认</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="1"> -->
<!-- 						<a href="javascript:;">纯白</a> -->
<!-- 					</dd> -->
<!-- 					<dd data-skin="2"> -->
<!-- 						<a href="javascript:;">蓝白</a> -->
<!-- 					</dd> -->
<!-- 				</dl> -->
<!-- 			</li> -->
			<li class="layui-nav-item" style="margin-right: 15px;margin-top: 5px;">
				<a href="javascript:;" href-url="#(ctxPath)/mms/msg/index">
					<i class="layui-icon">&#xe667;</i><span id="unReadMessageCount"></span>
				</a>
			</li>
			<li class="layui-nav-item" style="margin-right:10px;">
				<a class="name" href="javascript:;">
					<img class="layui-circle" src="#(ctxPath)/static/img/user.png" alt="logo"> 
					#shiroPrincipal() 
						#(principal) 
					#end 
				</a>
				<dl class="layui-nav-child">
					<dd>
						<a id="infoBtn" href="javascript:;" href-url="">个人资料</a>
					</dd>
					<dd>
						<a id="modPwdBtn" href="javascript:;" href-url="">修改密码</a>
					</dd>
				</dl>
			</li>
			<li class="layui-nav-item"><a href="#(ctxPath)/logout" class="layui-btn layui-btn-danger layui-btn-small btn-nav" href-url="">退出</a></li>
		</ul>
	</div>
	
	<!-- side -->
	<div class="layui-side my-side">
		<div class="layui-side-scroll">
			<!-- 左侧主菜单添加选项卡监听 -->
			<ul class="layui-nav layui-nav-tree" lay-filter="side-main">
				#shiroHasPermission("member:weiXinBackManage")
				<li class="layui-nav-item">
                    <a class="" href="javascript:;"><i class="fa fa-weixin"></i><span> 微信后台管理</span></a>
                    <dl class="layui-nav-child">
						#shiroHasPermission("member:weiXinUserQuery")
                        <dd>
                            <a href="javascript:;" href-url="#(ctxPath)/mms/wxuser/index"><i class="fa fa-user-circle"></i><span> 微信用户查询</span></a>
                        </dd>
						#end
						#shiroHasPermission("member:weiXinArticleManage")
                        <dd>
                            <a href="javascript:;" href-url="#(ctxPath)/mms/wxarticle/index"><i class="fa fa-tasks"></i><span> 微信文章管理</span></a>
                        </dd>
						#end
                    </dl>
                </li>
				#end

				#shiroHasPermission("member:memberHealthManage")
				<!--healthDataManage-->
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-user-o"></i>会员健康管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:memberHealthData")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/healthDataManage/index"><i class="fa fa-user-circle"></i><span> 会员健康数据</span></a>
						</dd>
						#end
						#shiroHasPermission("member:memberHealthPresentation")
<!--						<dd>-->
<!--							<a href="javascript:;" href-url="#(ctxPath)"><i class="fa fa-tasks"></i><span>会员健康报告</span></a>-->
<!--						</dd>-->
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("member:memberManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-user-o"></i>会员管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:birthdayManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/member/birthdayMemberIndex"><i class="fa fa-credit-card-alt"></i>会员生日管理</a>
						</dd>
						#end
						#shiroHasPermission("member:archiveManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/member/index"><i class="fa fa-address-book-o"></i>会员档案管理</a>
						</dd>
						#end
						#shiroHasPermission("member:faceManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/memberface/index"><i class="fa fa-address-card-o"></i>人脸模板管理</a>
						</dd>
						#end
						#shiroHasPermission("member:faceManageExamine")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/memberface/examineIndex"><i class="fa fa-address-card-o"></i>人脸模板审核</a>
						</dd>
						#end
						#shiroHasPermission("member:faceRecognitionData")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/faceRecognitionRecord/index"><i class="fa fa-sort-amount-asc"></i>人脸终端识别数据</a>
						</dd>
						#end
						#shiroHasPermission("member:bioPositionRecordData")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/bioPositionRecord/index"><i class="fa fa-sort-amount-asc"></i>手环终端定位数据</a>
						</dd>
						#end
						#shiroHasPermission("member:blackList")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/blackList/index"><i class="fa fa-list-alt"></i>黑名单管理</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("member:customerManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-user-o"></i>客户管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:customerInfo")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/customer/infoIndex"><i class="fa fa-credit-card-alt"></i>客户信息</a>
						</dd>
						#end
						#shiroHasPermission("member:customerVisit")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/customer/visitIndex"><i class="fa fa-credit-card-alt"></i>客户回访</a>
						</dd>
						#end
						#shiroHasPermission("member:customerVisitRecord")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/customer/visitRecordIndex"><i class="fa fa-credit-card-alt"></i>回访记录</a>
						</dd>
						#end
						#shiroHasPermission("member:visitStatistics")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/customer/visitStatisticsIndex"><i class="fa fa-credit-card-alt"></i>回访统计</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("crm:cardRollManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="fa fa-user-o"></i>卡券管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("crm:cardRollType")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/cardRollType/index"><i class="fa fa-credit-card-alt"></i>卡券分类管理</a>
						</dd>
						#end
						#shiroHasPermission("crm:cardRoll")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/cardRoll/index"><i class="fa fa-credit-card-alt"></i>卡券维护</a>
						</dd>
						#end
						#shiroHasPermission("crm:cardRollVerify")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/cardRoll/verifyIndex"><i class="fa fa-credit-card-alt"></i>卡券核销</a>
						</dd>
						#end
						#shiroHasPermission("crm:cardRollCollect")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/cardRoll/payIndex"><i class="fa fa-credit-card-alt"></i>卡券收款登记</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("member:equipmentManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>设备管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:gatewayManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/gateway/index"><i class="fa fa-sort-amount-asc"></i>网关管理</a>
						</dd>
						#end
						#shiroHasPermission("member:equipmentTypeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/equipmentType/index"><i class="fa fa-sort-amount-asc"></i>测量设备种类管理</a>
						</dd>
						#end
						#shiroHasPermission("member:equipmentCardManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/cardEquipment/index"><i class="fa fa-sort-amount-asc"></i>会员卡设备管理</a>
						</dd>
						#end
						#shiroHasPermission("member:equipmentBaseManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/baseEquipment/index"><i class="fa fa-sort-amount-asc"></i>基地设备管理</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("member:activityManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>活动管理</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:marketQrcodeManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/activityQrcode/index"><i class="fa fa-sort-amount-asc"></i>营销二维码活动</a>
						</dd>
						#end
						#shiroHasPermission("member:qrcodeMemberManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/activityMember/index"><i class="fa fa-sort-amount-asc"></i>二维码活动会员</a>
						</dd>
						#end
						#shiroHasPermission("member:eleMemberManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/doubleEleMember/index"><i class="fa fa-sort-amount-asc"></i>双十一营销会员</a>
						</dd>
						#end
						#shiroHasPermission("member:activityManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/activity/index"><i class="fa fa-sort-amount-asc"></i>现场抽奖活动</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("member:humanisticConcern")
<!--				<li class="layui-nav-item">-->
<!--					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>人文关怀</a>-->
<!--					<dl class="layui-nav-child">-->
						#shiroHasPermission("member:smsTypeManage")
<!--						<dd>-->
<!--							<a href="javascript:;" href-url="#(ctxPath)/mms/smsType/index"><i class="fa fa-sort-amount-asc"></i>短信类型管理</a>-->
<!--						</dd>-->
						#end
						#shiroHasPermission("member:smsManage")
<!--						<dd>-->
<!--							<a href="javascript:;" href-url="#(ctxPath)/mms/sms/index"><i class="fa fa-sort-amount-asc"></i>短信管理</a>-->
<!--						</dd>-->
						#end
<!--					</dl>-->
<!--				</li>-->
				#end
				#shiroHasPermission("member:questionnaireManage")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>问卷调查</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:questionnaireManageMenu")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/mms/questionnaire/questionnaireIndex"><i class="fa fa-sort-amount-asc"></i>问卷管理</a>
						</dd>
						#end
					</dl>
				</li>
				#end
				#shiroHasPermission("member:systemSet")
				<li class="layui-nav-item">
					<a href="javascript:;"><i class="layui-icon">&#xe620;</i>系统设置</a>
					<dl class="layui-nav-child">
						#shiroHasPermission("member:permissionMenuManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/menu/index"><i class="fa fa-list-ul"></i>菜单权限</a>
						</dd>
						#end
						#shiroHasPermission("member:RoleManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/role/index"><i class="fa fa-user-circle"></i>角色管理</a>
						</dd>

						#end
						#shiroHasPermission("member:userManage")
						<dd>
							<a href="javascript:;" href-url="#(ctxPath)/user/index"><i class="fa fa-sliders"></i>帐号管理</a>
						</dd>
						#end
						#shiroHasPermission("member:dictManage")
<!--						<dd>-->
<!--							<a href="javascript:;" href-url="#(ctxPath)/dict/index"><i class="fa fa-tasks"></i>系统字典</a>-->
<!--						</dd>-->
						#end
					</dl>
				</li>
				#end
			</ul>
		</div>
	</div>
	
	<!-- body -->
	<div class="layui-body my-body">
		<div class="layui-tab layui-tab-card my-tab" lay-filter="card" lay-allowClose="true">
			<ul class="layui-tab-title">
				<li class="layui-this" lay-id="1"><span><i class="layui-icon">&#xe68e;</i>首页</span></li>
			</ul>
			<div class="layui-tab-content">
				<div class="layui-tab-item layui-show">
					<iframe id="iframe" src="#(ctxPath)/welcome" frameborder="0"></iframe>
				</div>
			</div>
		</div>
	</div>
	
	<!-- footer -->
	<div class="layui-footer my-footer" style="height: 30px;">
		<p style="line-height: 30px;">
			<!--<a href="#(APP.orgWebsite)" target="_blank">#(APP.org)</a>
			&nbsp;&nbsp;&&nbsp;&nbsp;
			<a href="javascript:;" target="_blank">#(APP.name)</a>-->
			&nbsp;&nbsp;&nbsp;Copyright #(thisYear??) #(APP.copyRight)
		</p>
<!--		<p></p>-->
	</div>
</div>

<!-- 右键菜单 -->
<div class="my-dblclick-box none">
	<table class="layui-tab dblclick-tab">
		<tr class="card-refresh">
			<td><i class="layui-icon">&#x1002;</i>刷新当前标签</td>
		</tr>
		<tr class="card-close">
			<td><i class="layui-icon">&#x1006;</i>关闭当前标签</td>
		</tr>
		<tr class="card-close-all">
			<td><i class="layui-icon">&#x1006;</i>关闭所有标签</td>
		</tr>
	</table>
</div>
#end