//血压Highcharts选项的变量
var bpOptions = {
    chart: {
        renderTo: 'chartContainer',
        type: 'spline'
    },
    title: {
        text:'血压曲线图'
    },
    credits: {
        enabled: false
    },
    subtitle: {
    	text:'正常参考范围(mmHg)：收缩压 90～139 , 舒张压 60 ～ 89'
    },
    xAxis: {
    },
    yAxis: {
        title: {
            text:'(mmHg)'
        },
        min: 0,
        max: 230,
        tickPositions: [0, 30, 60, 90, 120, 140, 170, 200, 230],
        minorGridLineWidth: 1,
        gridLineWidth: 1,
        alternateGridColor: null,
        plotBands: [{
            from: 140,
            to: 230,
            color: 'rgba(255, 0, 0, 0.1)',
            label: {
                text: '高压',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 90,
            to: 139,
            color: 'rgba(0, 178, 49, 0.1)',
            label: {
                text: '正常收缩压',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 60,
            to: 89,
            color: 'rgba(0, 178, 49, 0.1)',
            label: {
                text: '正常舒张压',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 0,
            to: 59,
            color: 'rgba(255, 0, 0, 0.1)',
            label: {
                text: '低压',
                style: {
                    color: '#606060'
                }
            }
        }]
    },
    tooltip: {
        shared: true,
        valueSuffix: '(mmHg)'
    },
    series:[]
};
//空腹血糖Highcharts选项的变量
var bgBeforeMealOptions = {
    chart: {
        renderTo: 'chartContainer',
        type: 'spline'
    },
    title: {
        text:'空腹血糖曲线图'
    },
    credits: {
        enabled: false
    },
    subtitle: {
    	text:'正常参考范围(mmol/L)：3.9～6.1'
    },
    xAxis: {
    },
    yAxis: {
        title: {
            text:'(mmol/L)'
        },
        min: 0,
        max: 30,
        tickPositions: [0, 5, 10, 15, 20, 25, 30],
        minorGridLineWidth: 1,
        gridLineWidth: 1,
        alternateGridColor: null,
        plotBands: [{
            from: 6.11,
            to: 30,
            color: 'rgba(255, 0, 0, 0.1)',
            label: {
                text: '高血糖',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 3.9,
            to: 6.1,
            color: 'rgba(0, 178, 49, 0.1)',
            label: {
                text: '正常血糖',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 0,
            to: 3.89,
            color: 'rgba(255, 0, 0, 0.1)',
            label: {
                text: '低血糖',
                style: {
                    color: '#606060'
                }
            }
        }]
    },
    tooltip: {
        shared: true,
        valueSuffix: '(mmol/L)'
    },
    series:[]
};
//餐后2H血糖Highcharts选项的变量
var bgAfterMealOptions = {
    chart: {
        renderTo: 'chartContainer',
        type: 'spline'
    },
    title: {
        text:'餐后2H血糖曲线图'
    },
    credits: {
        enabled: false
    },
    subtitle: {
    	text:'正常参考范围(mmol/L)：6.7～9.4'
    },
    xAxis: {
    },
    yAxis: {
        title: {
            text:'(mmol/L)'
        },
        min: 0,
        max: 30,
        tickPositions: [0, 5, 10, 15, 20, 25, 30],
        minorGridLineWidth: 1,
        gridLineWidth: 1,
        alternateGridColor: null,
        plotBands: [{
            from: 9.41,
            to: 30,
            color: 'rgba(255, 0, 0, 0.1)',
            label: {
                text: '高血糖',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 6.7,
            to: 9.4,
            color: 'rgba(0, 178, 49, 0.1)',
            label: {
                text: '正常血糖',
                style: {
                    color: '#606060'
                }
            }
        },{
            from: 0,
            to: 6.69,
            color: 'rgba(255, 0, 0, 0.1)',
            label: {
                text: '低血糖',
                style: {
                    color: '#606060'
                }
            }
        }]
    },
    tooltip: {
        shared: true,
        valueSuffix: '(mmol/L)'
    },
    series:[]
};
//脉搏Highcharts选项的变量
var pulseOptions = {
		chart: {
			renderTo: 'chartContainer',
			type: 'spline'
		},
		title: {
			text:'脉搏曲线图'
		},
		credits: {
	        enabled: false
	    },
		subtitle: {
			text:'正常参考范围（bpm）：60-100'
		},
		xAxis: {
		},
		yAxis: {
			title: {
				text:'（bpm）'
			},
			min: 0,
			max: 160,
			tickPositions: [0, 40, 80, 120, 160],
			minorGridLineWidth: 1,
			gridLineWidth: 1,
			alternateGridColor: null,
			plotBands: [{
				from: 100.01,
				to: 160,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '心动过速',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 60,
				to: 100,
				color: 'rgba(0, 178, 49, 0.1)',
				label: {
					text: '正常',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 0,
				to: 59.99,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '心动过缓',
					style: {
						color: '#606060'
					}
				}
			}]
		},
		tooltip: {
			shared: true,
			valueSuffix: '（bpm）'
		},
		series:[]
};
//血氧Highcharts选项的变量
var oxygenOptions = {
		chart: {
			renderTo: 'chartContainer',
			type: 'spline'
		},
		title: {
			text:'血氧曲线图'
		},
		credits: {
	        enabled: false
	    },
		subtitle: {
			text:'正常参考范围：90%~100%'
		},
		xAxis: {
		},
		yAxis: {
			title: {
				text:'%'
			},
			min: 0,
			max: 100,
			tickPositions: [0, 20, 40, 60, 80, 100],
			minorGridLineWidth: 1,
			gridLineWidth: 1,
			alternateGridColor: null,
			plotBands: [{
				from: 90,
				to: 100,
				color: 'rgba(0, 178, 49, 0.1)',
				label: {
					text: '正常',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 80,
				to: 89.99,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '低血氧',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 0,
				to: 79.99,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '重度低血氧',
					style: {
						color: '#606060'
					}
				}
			}]
		},
		tooltip: {
			shared: true,
			valueSuffix: '%'
		},
		series:[]
};
//体温Highcharts选项的变量
var temperatureOptions = {
		chart: {
			renderTo: 'chartContainer',
			type: 'spline'
		},
		title: {
			text:'体温曲线图'
		},
		credits: {
	        enabled: false
	    },
		subtitle: {
			text:'正常参考范围（℃）：35～37.3'
		},
		xAxis: {
		},
		yAxis: {
			title: {
				text:'（℃）'
			},
			min: 0,
			max: 50,
			tickPositions: [0, 5, 10, 15, 20, 25, 30, 35, 37.3, 40, 45 ,50],
			minorGridLineWidth: 1,
			gridLineWidth: 1,
			alternateGridColor: null,
			plotBands: [{
				from: 39.01,
				to: 50,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '高热',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 38.01,
				to: 39,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '中热',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 37.31,
				to: 38,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '低热',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 35,
				to: 37.3,
				color: 'rgba(0, 178, 49, 0.1)',
				label: {
					text: '低热',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 0,
				to: 34.99,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '低热',
					style: {
						color: '#606060'
					}
				}
			}]
		},
		tooltip: {
			shared: true,
			valueSuffix: '（℃）'
		},
		series:[]
};
//步数Highcharts选项的变量
var stepCountOptions = {
		chart: {
			renderTo: 'chartContainer',
			type: 'spline'
		},
		title: {
			text:'步数曲线图'
		},
		credits: {
	        enabled: false
	    },
		subtitle: {
			text:'正常参考范围（步数）：6000-10000'
		},
		xAxis: {
		},
		yAxis: {
			title: {
				text:'（步）'
			},
			min: 0,
			max: 16000,
			tickPositions: [0, 1000, 3000, 6000, 12000],
			minorGridLineWidth: 1,
			gridLineWidth: 1,
			alternateGridColor: null,
			plotBands: [{
				from: 10001,
				to: 12000,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '运动过量',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 6000,
				to: 10000,
				color: 'rgba(0, 178, 49, 0.1)',
				label: {
					text: '正常',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 0,
				to: 5999,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '运动量低',
					style: {
						color: '#606060'
					}
				}
			}]
		},
	    series:[],
		tooltip: {
			shared: true,
			valueSuffix: '（步）'
		}
};
//心率Highcharts选项的变量
var heartRateOptions= {
		chart: {
			renderTo: 'chartContainer',
			type: 'spline'
		},
		title: {
			text:'心率曲线图'
		},
		credits: {
	        enabled: false
	    },
		subtitle: {
			text:'正常参考范围（bpm）：60-100'
		},
		xAxis: {
		},
		yAxis: {
			title: {
				text:'（bpm）'
			},
			min: 0,
			max: 160,
			tickPositions: [0, 40, 80, 120, 160],
			minorGridLineWidth: 1,
			gridLineWidth: 1,
			alternateGridColor: null,
			plotBands: [{
				from: 100.01,
				to: 160,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '心动过速',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 60,
				to: 100,
				color: 'rgba(0, 178, 49, 0.1)',
				label: {
					text: '正常',
					style: {
						color: '#606060'
					}
				}
			},{
				from: 0,
				to: 59.99,
				color: 'rgba(255, 0, 0, 0.1)',
				label: {
					text: '心动过缓',
					style: {
						color: '#606060'
					}
				}
			}]
		},
		tooltip: {
			shared: true,
			valueSuffix: '（bpm）'
		},
		series:[]
};