
.layui-tab-card{
	margin-top: 3px;
	border: none;
}
.layui-tab-card > .layui-tab-title {
    background-color: #f5f7fc;
	height: 45px;
	border: none;
}
.layui-tab-card > .layui-tab-title li {
	margin: 5px;
	border-radius: 2px;
}
.layui-tab-card > .layui-tab-title .layui-this {

    background-color: #009688;
    color: #fff;

}
.layui-tab-title li {
    line-height: 35px;
	background: #e4e7f0;
	color: #666;
}
.layui-tab-title .layui-this::after {
	border: none;
}
.layui-tab-content {
	margin-top: 10px;
    padding: 0px;
}
.assess .layui-table-view{
	margin: 0;
}

/*去除tab样式*/
.d-tab .layui-tab-title .layui-this:after{height: 36px;}
.d-tab .layui-tab-title li {background: transparent;}