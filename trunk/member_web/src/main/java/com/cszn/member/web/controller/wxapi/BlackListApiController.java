package com.cszn.member.web.controller.wxapi;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsBlackListService;
import com.cszn.integrated.service.entity.member.MmsBlackList;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;

import io.jboot.web.controller.annotation.RequestMapping;

@JCors
@RequestMapping(value = "/blackListApi",viewPath = "")
@Clear(LogInterceptor.class)
public class BlackListApiController extends BaseController {

    @Inject
    private MmsBlackListService mmsBlackListService;

    private static Logger logger = LoggerFactory.getLogger(BlackListApiController.class);

    public void saveBlackList(){
    	MmsBlackList blackList = getBean(MmsBlackList.class,"",true);
    	if(StrKit.isBlank(blackList.getFullName())) {
    		renderJson(Ret.fail("msg", "姓名不能为空!"));
    	}
    	if(StrKit.isBlank(blackList.getIdcard())) {
    		renderJson(Ret.fail("msg", "身份证不能为空!"));
    	}
    	if(StrKit.isBlank(blackList.getTelephone())) {
    		renderJson(Ret.fail("msg", "手机号不能为空!"));
    	}
    	if(StrKit.isBlank(blackList.getIsEnable())) {
    		renderJson(Ret.fail("msg", "是否有效不能为空!"));
    	}
    	MmsBlackList tempBlackList = mmsBlackListService.getByCondition(blackList.getFullName(), blackList.getIdcard(), blackList.getTelephone());
    	if(tempBlackList!=null) {
    		blackList.setId(tempBlackList.getId());
    	}
    	if(mmsBlackListService.saveBlackList(blackList)) {
    		renderJson(Ret.ok("msg", "保存成功"));
    	}else {
    		renderJson(Ret.fail("msg", "保存失败"));
    	}
    }

    public void checkBlackList(){
    	MmsBlackList blackList = getBean(MmsBlackList.class,"",true);
    	if(StrKit.isBlank(blackList.getFullName())) {
    		renderJson(Ret.fail("msg", "姓名不能为空!"));
    	}
    	if(StrKit.isBlank(blackList.getIdcard())) {
    		renderJson(Ret.fail("msg", "身份证不能为空!"));
    	}
    	if(StrKit.isBlank(blackList.getTelephone())) {
    		renderJson(Ret.fail("msg", "手机号不能为空!"));
    	}
    	MmsBlackList tempBlackList = mmsBlackListService.getByCondition(blackList.getFullName(), blackList.getIdcard(), blackList.getTelephone());
    	if(tempBlackList!=null) {
    		if("0".equals(tempBlackList.getIsEnable())) {
    			renderJson(Ret.fail("msg", "是黑名单,校验不通过!"));
    		}else {
    			renderJson(Ret.ok("msg", "不是黑名单,校验通过!"));
    		}
    	}else {
    		renderJson(Ret.ok("msg", "不是黑名单,校验通过!"));
    	}
    }
}
