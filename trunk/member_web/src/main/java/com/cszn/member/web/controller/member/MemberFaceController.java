package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsMemberFaceService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.member.MmsMemberFace;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * @Description 人脸模板管理
 * <AUTHOR>
 * @Date 2019/6/13
 **/
@RequestMapping(value="/mms/memberface", viewPath="/modules_page/mms/memberFace")
public class MemberFaceController extends BaseController {


    @Inject
    private MmsMemberFaceService mmsMemberFaceService;

    @Inject
    private MmsMemberService mmsMemberService;


    /**
     * 跳转到人脸模板界面--列表02
     */
    public void index(){
        render("memberFaceIndex.html");
    }



    /**
     * 跳转到人脸模板界面--列表
     */
    public void examineIndex(){
        render("memberFaceExamineIndex.html");
    }


    /**
     * 人脸模板列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        Page<Record> page = mmsMemberFaceService.findList(getParaToInt("page"), getParaToInt("limit"),fullName,idcard);
        renderJson(new DataTable<Record>(page));
    }




    /**
     * 人脸模板列表-02
     */
    @Clear(LogInterceptor.class)
    public void findList(){
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        if(StringUtils.isBlank(fullName) && StringUtils.isBlank(idcard)){
            renderJson(new DataTable<Record>(Lists.newArrayList()));
            return;
        }
        List<Record> list = mmsMemberFaceService.findList(fullName,idcard);
        renderJson(new DataTable<Record>(list));
    }




    /**
     * 人脸模板删除
     */
    public void delete(){
        String id = getPara("id");
        MmsMemberFace face = mmsMemberFaceService.get(id);
        if(face == null){ renderJson(Ret.ok("msg", "人脸模板不存在"));return; }
        if(mmsMemberFaceService.deleteById(id)){
            renderJson(Ret.ok("msg", "删除成功"));
        }else{
            renderJson(Ret.fail("msg", "删除失败"));
        }
    }


    /**
     * 跳转上传人脸照片页面
     */
    public void formAdd(){
        setAttr("commonUpload", Global.commonUpload);
        render("memberFaceForm.html");
    }


    /**
     * 跳转选择会员页面
     */
    public void chooseMember(){
        render("memberInfo.html");
    }


    /**
     * 跳转查看详情页面
     */
    public void form(){
        String id = getPara("id");
        String type = getPara("type");
        MmsMemberFace face = mmsMemberFaceService.get(id);
        if(face != null){
            if(StringUtils.isNotBlank(face.getMemberId())) {
                MmsMember member = mmsMemberService.get(face.getMemberId());
                setAttr("member",member);
            }
            if(face.getPicture() != null && face.getPicture().length > 0) {
                setAttr("picture", new String(face.getPicture()));
            }
            setAttr("base","data:image/jpg;base64,");
        }
        setAttr("face",face);
        if("examine".equals(type)){
            render("memberFaceExamine.html");
        }else{
            render("memberFaceForm.html");
        }
    }


    /**
     * 跳转到上传人脸页面
     */
    public void faceForm(){
        String faceId = getPara("faceId");
        setAttr("memberId",getPara("memberId"));
        setAttr("faceId",getPara("faceId"));
        setAttr("commonUpload", Global.commonUpload);
        MmsMemberFace face = mmsMemberFaceService.get(faceId);
        if(face != null){
            if(face.getPicture() != null && face.getPicture().length > 0) {
                setAttr("picture", new String(face.getPicture()));
            }
            setAttr("base","data:image/jpg;base64,");
        }
        render("memberFaceForm.html");
    }


    /**
     * 跳转到人工审核页面
     */
    public void examineForm(){
        String id = getPara("id");
        MmsMemberFace face = mmsMemberFaceService.get(id);
        if(face != null){
            if(StringUtils.isNotBlank(face.getMemberId())) {
                MmsMember member = mmsMemberService.get(face.getMemberId());
                setAttr("member",member);
            }
            if(face.getPicture() != null && face.getPicture().length > 0) {
                setAttr("picture", new String(face.getPicture()));
            }
            setAttr("base","data:image/jpg;base64,");
        }
        setAttr("face",face);
        render("memberFaceExamine.html");
    }



    /**
     * 跳转新增会员档案界面
     */
    public void memberForm(){
        setAttr("commonUpload", Global.commonUpload);
        render("memberForm.html");
    }


    /**
     * 跳转到上传人脸界面
     */
    public void uploadFaceForm(){
        setAttr("commonUpload", Global.commonUpload);
        render("uploadFaceForm.html");
    }




    /**
     * 保存：后台人脸模板审核
     */
    @Clear(LogInterceptor.class)
    public void save(){
        MmsMemberFace face = getBean(MmsMemberFace.class,"face",true);
        String picturePath = getPara("picturePath");
        if(StringUtils.isBlank(face.getMemberId())){ renderJson(Ret.fail("msg", "请上传会员信息"));return; }
        if(StringUtils.isBlank(picturePath)){ renderJson(Ret.fail("msg", "请上传人脸图片"));return; }

        //去重
        Long count = Db.queryLong("SELECT COUNT(*) FROM mms_member_face WHERE verify_flag in (0,1) and member_id = ?",face.getMemberId());
        if(count > 0){ renderJson(Ret.fail("msg", "该会员已上传过人脸图片")); return; }

        //查询是否有身份证
        MmsMember member = mmsMemberService.get(face.getMemberId());
        if(member == null || StringUtils.isBlank(member.getIdcard())){ renderJson(Ret.fail("msg", "会员档案不存在或身份证不能为空")); return; }

        boolean flag = mmsMemberFaceService.saveMemberFace(face,picturePath);
        if(flag){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 人工审核
     */
    public void examine(){
        MmsMemberFace face = getBean(MmsMemberFace.class,"face",true);
        if(StringUtils.isBlank(face.getId()) || StringUtils.isBlank(face.getVerifyFlag()))
        { renderJson(Ret.fail("msg", "审核参数未传入"));return; }

        //不通过要写说明
        if("2".equals(face.getVerifyFlag()) && StringUtils.isBlank(face.getVerifyDescribe()))
        { renderJson(Ret.fail("msg", "审核不通过须说明原因"));return; }

        if(mmsMemberFaceService.update(face)){
            renderJson(Ret.ok("msg", "已保存审核记录"));
        }else{
            renderJson(Ret.fail("msg", "审核失败"));
        }
    }


    /**
     * 保存会员档案/并保存人脸
     */
    public void saveMember(){
        MmsMember member = getBean(MmsMember.class,"member",true);
        String flag = mmsMemberService.saveMmsMember(member, AuthUtils.getUserId());
        if("suc".equals(flag)){
            //获取人脸
            MmsMember mmsMemberExist = mmsMemberService.getMemberByNameAndIdcard(member.getFullName(),member.getIdcard());
            if(mmsMemberExist != null){
                boolean faceFlag = mmsMemberFaceService.generateMemberFace(mmsMemberExist.getId(),member.getHeadPic());
            }
            renderJson(Ret.ok("msg", "保存成功"));
        }else if("".equals(flag)){
            renderJson(Ret.fail("msg", "身份证号不可重复"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }
}
