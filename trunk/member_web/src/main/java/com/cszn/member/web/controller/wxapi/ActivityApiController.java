package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.RetApi;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.entity.cfs.CfsFileUpload;
import com.cszn.integrated.service.entity.fina.*;
import com.cszn.integrated.service.entity.member.MmsBlackList;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.ReturnCardStatus;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.db.model.Columns;
import io.jboot.web.cors.EnableCORS;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsActivityDetailService;
import com.cszn.integrated.service.api.member.MmsActivityService;
import com.cszn.integrated.service.entity.member.MmsActivity;
import com.cszn.integrated.service.entity.member.MmsActivityDetail;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;

@JCors
@RequestMapping(value = "/activityApi",viewPath = "")
@Clear(LogInterceptor.class)
public class ActivityApiController extends BaseController {

	@Inject
    private MmsActivityService mmsActivityService;
	@Inject
	private MmsActivityDetailService mmsActivityDetailService;

    private static Logger logger = LoggerFactory.getLogger(ActivityApiController.class);

    @Clear(LogInterceptor.class)
    public void activityCurrentDateRecordPage(){
		Page<MmsActivity> page = mmsActivityService.paginateByCondition(DateUtils.getDate(), getParaToInt("page", 1), getParaToInt("limit", 10));
		renderJson(new DataTable<MmsActivity>(page));
    }

    @Clear(LogInterceptor.class)
    public void myPrizeCurrentDateRecordPage() {
    	final String userId = getPara("userId");
        Page<MmsActivityDetail> modelPage = mmsActivityDetailService.paginateByCondition(userId, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MmsActivityDetail>(modelPage));
    }

    /**
     * 活动申请审核通过回调地址允许跨域
     */
    @Clear(LogInterceptor.class)
    public void activityApplyCallBack(){
        Long beginTime = System.currentTimeMillis();
        logger.info("接收开始时间==="+beginTime);
        final String jsonStr = getRawData();
        JSONObject returnJson = JSONObject.parseObject(jsonStr);
        final String taskId = returnJson.getString("TaskId");
        final String currentStep = returnJson.getString("CurrentStep");
        final String currentStepAlias = returnJson.getString("CurrentStepAlias");
        final String currentUserName = returnJson.getString("CurrentUserName");
        final String currentStatus = returnJson.getString("CurrentStatus");
        logger.info("taskId==="+taskId);
        logger.info("currentStep==="+currentStep);
        logger.info("currentStepAlias==="+currentStepAlias);
        logger.info("currentUserName==="+currentUserName);
        logger.info("currentStatus==="+currentStatus);
        Global.executorService.execute(new Runnable() {
            @Override
            public void run() {
                if("Completed".equals(currentStatus)) {
                    mmsActivityService.bmpInvokeGetForm(taskId);
                }
            }
        });
        logger.info("接收结束时间==="+System.currentTimeMillis());
        Long opetime = System.currentTimeMillis() - beginTime;
        logger.info("耗时==="+opetime);
        renderCodeSuccess("success");
    }
}
