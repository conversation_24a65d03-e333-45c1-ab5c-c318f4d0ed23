/**
 * 
 */
package com.cszn.member.web.controller.member;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ZXingCode;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.member.MmsActivityDetailService;
import com.cszn.integrated.service.api.member.MmsActivityService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.member.MmsActivity;
import com.cszn.integrated.service.entity.member.MmsActivityDetail;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2020年12月11日
 *
 * MmsMemberVisitController
 */
@RequestMapping(value="/mms/activity", viewPath="/modules_page/mms/activity")
public class MmsActivityController extends BaseController {

	@Inject
    private MmsActivityService mmsActivityService;
	@Inject
	private MmsActivityDetailService mmsActivityDetailService;
	@Inject
	private MainBaseService mainBaseService;
	
    public void index() {
        render("activityIndex.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	MmsActivity model = getBean(MmsActivity.class, "", true);
    	model.setCreateBy(AuthUtils.getUserId());
        Page<MmsActivity> modelPage = mmsActivityService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MmsActivity>(modelPage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	MmsActivity model = getBean(MmsActivity.class, "", true);
    	List<MainBase> baseList = mainBaseService.findBaseList();
    	setAttr("model", model);
    	setAttr("baseList", baseList);
        render("activityForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String modelId = getPara("id");
        List<MainBase> baseList = mainBaseService.findBaseList();
		setAttr("baseList", baseList);
        setAttr("model", mmsActivityService.findById(modelId));
        render("activityForm.html");
    }
    
    /**
     * 名单页面方法
     */
    public void detail() {
    	final String modelId = getPara("id");
    	setAttr("activityId", modelId);
    	setAttr("detailList", mmsActivityDetailService.findListByActivityId(modelId));
    	render("activityDetail.html");
    }
    
    public void save() {
    	MmsActivity model = getBean(MmsActivity.class, "", true);
    	boolean flag = false;
    	if(StrKit.isBlank(model.getId())) {
			model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
			model.setActivityQrcode(IdGen.getUUID().replace("-",""));
    		model.setCreateTime(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    	}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    public void saveDetail() {
    	boolean flag = false;
    	final int detailCount = getParaToInt("detailCount");
		if(detailCount>0){
			for (int i = 1; i <= detailCount; i++) {
				final MmsActivityDetail detail = getBean(MmsActivityDetail.class, "detailList["+i+"]");
				if(StrKit.isBlank(detail.getId())) {
					detail.setId(IdGen.getUUID());
					detail.setDelFlag(DelFlag.NORMAL);
					detail.setCreateBy(AuthUtils.getUserId());
					detail.setCreateTime(new Date());
		    		flag = detail.save();
		    	}else {
		    		detail.setUpdateBy(AuthUtils.getUserId());
		    		detail.setUpdateTime(new Date());
		    		flag = detail.update();
		    	}
				if(!flag) {
					break;
				}
			}
		}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    public void export() {
    	
    	final String activityId = getPara("activityId");
    	final String filePath = PathKit.getWebRootPath()+"\\download\\activity\\"+DateUtils.getDate()+"\\";
    	final String fileName = "抽奖号码_"+activityId+".txt";
    	File newFile = null;
    	BufferedWriter out = null;
    	String downloadFileName = "";
    	try {
        	File fileDir = new File(filePath);
        	if (!fileDir.exists()) {
        		fileDir.mkdirs();
        	}
        	newFile = new File(filePath+fileName);
        	if(!newFile.exists()) {
        		newFile.createNewFile();
        	}
        	out = new BufferedWriter(new FileWriter(newFile));
            List<MmsActivityDetail> detailList = mmsActivityDetailService.findListByActivityId(activityId);
            for (int i = 0; i < detailList.size(); i++) {
           	 MmsActivityDetail detail = detailList.get(i);
                out.write(detail.getPrizeNum()+"\r\n");
            }
            out.flush(); // 把缓存区内容压入文件
            out.close(); // 最后记得关闭文件
//            this.getResponse().setCharacterEncoding("UTF-8");
//            this.getResponse().setContentType("text/plain");
            downloadFileName = URLEncoder.encode("抽奖号码.txt", "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
    	renderFile(newFile, downloadFileName);
    }
    
    public void del(){
    	final MmsActivity model = getBean(MmsActivity.class, "", true);
    	if(model!=null && StrKit.notBlank(model.getId())){
    		if(mmsActivityService.update(model)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else {
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }

    public void createQrcode(){
		String id=getPara("id");
		MmsActivity activity = mmsActivityService.findById(id);
		File logoFile = new File(PathKit.getWebRootPath() + Global.logoImg);
		String base64Img = "";
		if(DateUtils.compareDays(new Date(),activity.getEndTime())>0){
			renderJson(Ret.fail("msg", "该活动已结束"));
			return;
		}
		try {
			base64Img = ZXingCode.drawLogoQRCode(logoFile,Global.activityQrcodeUrl.replace("{1}",activity.getActivityQrcode()), "");
		} catch (Exception e) {
			e.printStackTrace();
			renderJson(Ret.fail("msg", "生成二维码失败"));
			//logger.info("二维码工具类生成异常：" +e);
			return;
		}
		renderJson(Ret.ok("msg", "生成二维码成功").set("imgName",base64Img));
	}

	public static void main(String[] args) throws Exception{
		System.out.println("https://open.weixin.qq.com/connect/oauth2/authorize?appid=wxd91b34cbe3ab4445&redirect_uri=https%3A%2F%2Fbmp.cncsgroup.com%2FAshx%2FBMPWC.ashx&response_type=code&scope=snsapi_base&state=LuckyDraw-{1}#wechat_redirect".replace("{1}","xxx"));
	}
}
