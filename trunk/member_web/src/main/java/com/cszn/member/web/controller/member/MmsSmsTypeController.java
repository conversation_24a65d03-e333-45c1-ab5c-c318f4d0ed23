package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsSmsTypeService;
import com.cszn.integrated.service.entity.member.MmsSmsType;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

@RequestMapping(value = "/mms/smsType",viewPath = "/modules_page/mms/smsType")
public class MmsSmsTypeController extends BaseController {

    @Inject
    private MmsSmsTypeService mmsSmsTypeService;

    @Clear(LogInterceptor.class)
    public void index(){
        render("index.html");
    }

    @Clear(LogInterceptor.class)
    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MmsSmsType type=mmsSmsTypeService.findById(id);
            setAttr("type",type);
        }
        render("form.html");
    }

    @Clear(LogInterceptor.class)
    public void tablePage(){
        MmsSmsType type=getBean(MmsSmsType.class,"",true);

        Page<MmsSmsType> typePage=mmsSmsTypeService.tablePage(getParaToInt("page"),getParaToInt("limit"),type);
        renderJson(new DataTable<MmsSmsType>(typePage));
    }

    public void save(){
        MmsSmsType type=getBean(MmsSmsType.class,"",true);
        if(StrKit.notBlank(type.getId())){
            Integer i=Db.queryInt("select count(id) from mms_sms_type where sending_method='everyDay' and del_flag='0' and id<>? ",type.getId());
            if(i!=null && i>0){
                renderJson(Ret.fail("msg","请勿重新添加生日祝福类型"));
                return;
            }
        }else{
            Integer i=Db.queryInt("select count(id) from mms_sms_type where sending_method='everyDay' and del_flag='0' ");
            if(i!=null && i>0){
                renderJson(Ret.fail("msg","请勿重新添加生日祝福类型"));
                return;
            }
        }
        boolean flag=mmsSmsTypeService.saveType(type, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void del(){
        String id=getPara("id");
        MmsSmsType type=new MmsSmsType();
        type.setId(id);
        type.setDelFlag("1");
        boolean flag=mmsSmsTypeService.saveType(type, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
