package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.member.MmsSmsCardTypeService;
import com.cszn.integrated.service.api.member.MmsSmsDetailService;
import com.cszn.integrated.service.api.member.MmsSmsService;
import com.cszn.integrated.service.api.member.MmsSmsTypeService;
import com.cszn.integrated.service.entity.enums.FestivalSendType;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.member.MmsSms;
import com.cszn.integrated.service.entity.member.MmsSmsCardType;
import com.cszn.integrated.service.entity.member.MmsSmsDetail;
import com.cszn.integrated.service.entity.member.MmsSmsType;
import com.cszn.member.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.*;

@RequestMapping(value = "/mms/sms",viewPath = "/modules_page/mms/sms")
public class MmsSmsController extends BaseController {

    @Inject
    private MmsSmsService mmsSmsService;
    @Inject
    private MainMembershipCardTypeService mainMembershipCardTypeService;
    @Inject
    private MmsSmsTypeService mmsSmsTypeService;
    @Inject
    private MmsSmsCardTypeService mmsSmsCardTypeService;
    @Inject
    private MmsSmsDetailService mmsSmsDetailService;

    public void index(){

        List<MmsSmsType> typeList=mmsSmsTypeService.findMmsSmsType();
        setAttr("typeList",typeList);
        render("festivalIndex.html");
    }


    public void pageList(){
        Integer pageNumber=getParaToInt("page");
        Integer pageSize=getParaToInt("limit");
        String typeId=getPara("typeId");

        Page<Record> recordPage=mmsSmsService.findPageList(pageNumber,pageSize,typeId);
        Date now=new Date();
        for(Record record:recordPage.getList()){
            if(DateUtils.compareDays(record.getDate("sendTime"),now)==-1){
                record.set("delFlag",false);
            }else{
                record.set("delFlag",true);
            }
        }
        renderJson(new DataTable<Record>(recordPage));
    }


    public void form(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MmsSms model=mmsSmsService.findById(id);
            setAttr("model",model);
        }

        List<MmsSmsType> typeList=mmsSmsTypeService.findMmsSmsType();
        setAttr("typeList",typeList);
        render("festivalForm.html");
    }

    public void findAddCardTypeList(){
        String templateId=getPara("templateId");
        List<MainMembershipCardType> typeList=mainMembershipCardTypeService.findCardTypeList(templateId);

        renderJson(new DataTable<MainMembershipCardType>(typeList));
    }


    public void save(){
        MmsSms sms=getBean(MmsSms.class,"",true);

        if(StrKit.notBlank(sms.getId())){
            MmsSms mmsSms=mmsSmsService.findById(sms.getId());
            if("1".equals(mmsSms.getExamineStatus())){
                renderJson(Ret.fail("msg","该记录已为审核状态，保存失败"));
                return;
            }

        }
        boolean flag=mmsSmsService.saveSms(sms, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void examine(){
        String id=getPara("id");
        String examineStatus=getPara("examineStatus");
        
        final int smsCardTypeCount = Db.queryInt("select count(id)cardTypeCount from where sms_id=?", id);
        if(smsCardTypeCount<=0){
        	renderJson(Ret.fail("msg","会员卡类型为空，审核失败!"));
            return;
        }
        
        MmsSms sms=new MmsSms();
        sms.setId(id);
        sms.setExamineStatus(examineStatus);

        MmsSms mmsSms=mmsSmsService.findById(id);
        if(DateUtils.compareDays(mmsSms.getSendTime(),new Date())==-1){
            renderJson(Ret.fail("msg","发送时间已过期，审核失败!"));
            return;
        }
        if("1".equals(examineStatus)){
            sms.setExamineTime(new Date());
            sms.setExamineBy(AuthUtils.getUserId());
        }else{
            sms.setExamineTime(null);
        }
        boolean flag=mmsSmsService.saveSms(sms, AuthUtils.getUserId());

        if(flag){
            //添加
            mmsSmsDetailService.saveSmsDetail(id,AuthUtils.getUserId());
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void del(){
        String id=getPara("id");
        MmsSms sms=new MmsSms();
        sms.setId(id);
        sms.setDelFlag("1");

        MmsSms mmsSms=mmsSmsService.findById(id);
        if(mmsSms!=null && "1".equals(mmsSms.getExamineStatus()) && DateUtils.compareDays(mmsSms.getSendTime(),new Date())==-1){
            renderJson(Ret.fail("msg","该短信已发送，不允许作废"));
            return;
        }

        boolean flag=mmsSmsService.saveSms(sms, AuthUtils.getUserId());
        if(flag){
            List<String> ids=Db.query("select id from mms_sms_detail where  sms_id=? and del_flag='0' and send_status='0' ",id);
            if(ids.size()>0){
                String str="";
                for(String i:ids){
                    str+="?,";
                }
                str=str.substring(0,str.length()-1);
                ids.add(0,AuthUtils.getUserId());
                Db.update("update mms_sms_detail set del_flag='1',update_by=?,update_date=now() where id in("+str+") ",ids.toArray());
            }
        }
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void smsCardTypePageList(){
        String smsId=getPara("smsId");
        Page<Record> recordPage=mmsSmsCardTypeService.tablePage(getParaToInt("page"),getParaToInt("limit"),smsId);

        renderJson(new DataTable<Record>(recordPage));
    }

    public void smsCardTypeSave(){
        MmsSmsCardType type=getBean(MmsSmsCardType.class,"",true);
        type.setId(IdGen.getUUID());

        MmsSms sms=mmsSmsService.findById(type.getSmsId());
        if("1".equals(sms.getExamineStatus())){
            renderJson(Ret.fail("msg","该记录为已审核状态，添加失败"));
            return;
        }

        if(type.save()){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void smsCardTypeDel(){
        String id=getPara("id");
        if(mmsSmsCardTypeService.deleteById(id)){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void smsCardTypeIndex(){
        String smsId=getPara("smsId");
        String examineStatus=getPara("examineStatus");

        setAttr("smsId",smsId);
        setAttr("examineStatus",examineStatus);
        render("smsCardTypeIndex.html");
    }

    public void smsCardTypeForm(){
        String smsId=getPara("smsId");

        setAttr("smsId",smsId);
        render("smsCardTypeForm.html");
    }

    public void cardTypeTablePage(){
        String smsId=getPara("smsId");
        String typeClassify=getPara("typeClassify");
        String cardTypeName=getPara("cardTypeName");
        Page<Record> page=mmsSmsCardTypeService.cardTypeTablePage(getParaToInt("page"),getParaToInt("limit"),smsId,typeClassify,cardTypeName);

        renderJson(new DataTable<Record>(page));
    }

    public void detailIndex(){
        String smsId=getPara("smsId");

        setAttr("smsId",smsId);
        render("smsDetail.html");
    }

    public void detailDel(){
        String id=getPara("id");
        boolean flag=mmsSmsDetailService.detailDel(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void detailTablePage(){
        MmsSmsDetail detail=getBean(MmsSmsDetail.class,"",true);
        Page<MmsSmsDetail> page=mmsSmsDetailService.tablePage(getParaToInt("page"),getParaToInt("limit"),detail);
        renderJson(new DataTable<MmsSmsDetail>(page));
    }

    public void smsCardTypeBatchSave(){
        String cardTypeIds=getPara("cardTypeIds");
        String smsId=getPara("smsId");
        String[] cardTypeIdArray=null;
        if(cardTypeIds.contains(",")){
            cardTypeIdArray=cardTypeIds.split(",");
        }else{
            cardTypeIdArray=new String[]{cardTypeIds};
        }
        List<MmsSmsCardType> list=new ArrayList<>();
        for(String id:cardTypeIdArray){
            MmsSmsCardType type=new MmsSmsCardType();
            type.setId(IdGen.getUUID());
            type.setSmsId(smsId);
            type.setCardTypeId(id);
            list.add(type);
        }
        int[] nums=Db.batchSave(list,list.size());
        for(int i:nums){
            if(i<1){
                renderJson(Ret.fail("msg","操作失败"));
                return;
            }
        }
        renderJson(Ret.ok("msg","操作成功"));
    }
}
