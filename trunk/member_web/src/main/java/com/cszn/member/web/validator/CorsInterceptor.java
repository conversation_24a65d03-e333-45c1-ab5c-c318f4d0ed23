package com.cszn.member.web.validator;


import com.cszn.integrated.base.interceptor.JCors;
import com.jfinal.aop.Interceptor;
import com.jfinal.aop.Invocation;
import com.jfinal.render.JsonRender;
import com.jfinal.render.Render;
import com.jfinal.render.RenderManager;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 设置允许跨域
 */
public class CorsInterceptor implements Interceptor {
    @Override
    public void intercept(Invocation inv) {
        //获取Controller上的@JCors注解，如果有则设置允许跨域内容。
        JCors jCors=inv.getController().getClass().getAnnotation(JCors.class);
        if(jCors==null){
            //如果类上没有获取方法上的
            jCors=jCors=inv.getMethod().getAnnotation(JCors.class);
        }
        if(jCors!=null){
            handler(inv.getController().getRequest(),inv.getController().getResponse());
            //解决跨域调用2次
            String method=inv.getController().getRequest().getMethod();
            if("OPTIONS".equals(method)){
                inv.getController().renderJson();
                return;
            }
            inv.invoke();
            return;
        }
        //如果都没则不做任何操作
        inv.invoke();
    }

    private void handler(HttpServletRequest request, HttpServletResponse response){
        String origin = request.getHeader("origin");// 获取源站
        String requestHeaders = request.getHeader("Access-Control-Request-Headers");
        if (StringUtils.isEmpty(requestHeaders)) {
            requestHeaders = "";
        }
        response.setHeader("Access-Control-Allow-Origin", origin);
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "HEAD,PUT,DELETE,POST,GET");
        response.setHeader("Access-Control-Allow-Headers", "Accept, Origin, XRequestedWith, Content-Type, LastModified," + requestHeaders);

    }

}
