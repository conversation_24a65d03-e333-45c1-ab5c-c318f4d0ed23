package com.cszn.member.web.controller.member;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.provider.member.MmsCustomerVisitServiceImpl;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.member.MmsCustomerAssignService;
import com.cszn.integrated.service.api.member.MmsCustomerInfoService;
import com.cszn.integrated.service.api.member.MmsCustomerVisitService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.pers.PersEmpUserService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.api.pers.PersOrgService;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.member.MmsCustomerInfo;
import com.cszn.integrated.service.entity.member.MmsCustomerVisit;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.pers.PersOrg;
import com.cszn.integrated.service.entity.pers.PersOrgEmployee;
import com.cszn.integrated.service.entity.status.DataSource;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.OrgType;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;

import io.jboot.web.controller.annotation.RequestMapping;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@RequestMapping(value="/mms/customer", viewPath="/modules_page/mms/customer")
public class MmsCustomerController extends BaseController {

	private static Logger logger = LoggerFactory.getLogger(MmsCustomerController.class);

	@Inject
	private MmsMemberService mmsMemberService;
	@Inject
    private MmsCustomerInfoService mmsCustomerInfoService;
	@Inject
	private MmsCustomerAssignService mmsCustomerAssignService;
	@Inject
	private MmsCustomerVisitService mmsCustomerVisitService;
	@Inject
	private PersOrgService persOrgService;
	@Inject
	private PersOrgEmployeeService persOrgEmployeeService;
	@Inject
	private PersEmpUserService persEmpUserService;
	@Inject
	private MainBaseService mainBaseService;
	@Inject
	private AreaService areaService;
	
	/**
	 * 客户信息首页界面
	 */
	public void infoIndex(){
		final String startDate = DateUtils.formatDate(DateUtils.getNextDay(new Date(), -1))+" 00:00:00";
		final String endDate = DateUtils.formatDate(DateUtils.getNextDay(new Date(), -1))+" 23:59:59";
		setAttr("userId", AuthUtils.getUserId());
		setAttr("newCount", mmsCustomerInfoService.yesterdayNewCount(startDate, endDate));
		render("infoIndex.html");
	}
	
	/**
	 * 精确查询首页界面
	 */
	public void preciseQueryIndex(){
		render("preciseQueryIndex.html");
	}

	/**
	 * 客户回访首页界面
	 */
	public void visitIndex(){
		List<Record> empUserList = new ArrayList<Record>();
		PersOrg persOrg = persOrgService.findByName(OrgType.headquarters, "客服");
		if(persOrg!=null) {
			empUserList = persOrgEmployeeService.empUserList(persOrg.getId());
		}
		setAttr("assignUserId", AuthUtils.getUserId());
		setAttr("empUserList", empUserList);
		setAttr("hrmDomain",Global.hrmUrl);
		render("visitIndex.html");
	}
	
	/**
	 * 回访记录首页界面
	 */
	public void visitRecordIndex(){
		List<Record> empUserList = new ArrayList<Record>();
		PersOrg persOrg = persOrgService.findByName(OrgType.headquarters, "客服");
		if(persOrg!=null) {
			empUserList = persOrgEmployeeService.empUserList(persOrg.getId());
		}
		setAttr("empUserList", empUserList);
		render("visitRecordIndex.html");
	}
	
	/**
	 * 回访统计首页界面
	 */
	public void visitStatisticsIndex(){
		List<Record> empUserList = new ArrayList<Record>();
		PersOrg persOrg = persOrgService.findByName(OrgType.headquarters, "客服");
		if(persOrg!=null) {
			empUserList = persOrgEmployeeService.empUserList(persOrg.getId());
		}
		setAttr("empUserList", empUserList);
		render("visitStatisticsIndex.html");
	}
	
	/**
	 * 回访统计回访记录界面
	 */
	public void visitStatisticsRecordIndex(){
		final String startDate = getPara("startDate");
		final String endDate = getPara("endDate");
		final String receptionUserId = getPara("receptionUserId");
		final String visitType = getPara("visitType");
		final String actionType = getPara("actionType");
		setAttr("startDate", startDate);
		setAttr("endDate", endDate);
		setAttr("receptionUserId", receptionUserId);
		setAttr("visitType", visitType);
		setAttr("actionType", actionType);
		render("visitStatisticsRecordIndex.html");
	}
	
	/**
	 * 客户信息分页列表
	 */
	@Clear(LogInterceptor.class)
	public void pageTable(){
		MmsCustomerInfo model = getBean(MmsCustomerInfo.class,"",true);
		Page<MmsCustomerInfo> page = mmsCustomerInfoService.pageTable(getParaToInt("page"), getParaToInt("limit"), model, getPara("isAssignVisit"), getPara("isVisit"), getPara("startDate"), getPara("endDate"), getPara("callPass"), getPara("followResult"), getInt("memberAge"));
//		if(page.getList()!=null && page.getList().size()>0){
//			for (int i = 0; i < page.getList().size(); i++) {
//				MmsCustomerInfo customer = page.getList().get(i);
//				final String idcard = customer.getIdcard();
//				if("not_member".equals(customer.getCustomerType())){
//					MmsMember member = mmsMemberService.getMemberByIdcard(idcard);
//					if(member!=null){
//						customer.setCustomerType("member");
//					}
//				}
//				if("not_staff".equals(customer.getStaffType())){
//					PersOrgEmployee emp = persOrgEmployeeService.getByEmpIdcard(idcard);
//					if(emp!=null){
//						customer.setStaffType("staff");
//					}else{
//						customer.setStaffType("not_staff");
//					}
//				}
//			}
//		}
		renderJson(new DataTable<MmsCustomerInfo>(page));
	}
	
	/**
	 * 精确查询分页列表
	 */
	@Clear(LogInterceptor.class)
	public void preciseQueryTable(){
		MmsCustomerInfo model = getBean(MmsCustomerInfo.class,"",true);
		Page<MmsCustomerInfo> page = mmsCustomerInfoService.preciseQueryTable(getParaToInt("page"), getParaToInt("limit"), model);
		renderJson(new DataTable<MmsCustomerInfo>(page));
	}
	
    /**
     * 回访分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void visitTable() {
    	MmsCustomerVisit model = getBean(MmsCustomerVisit.class, "", true);
        Page<MmsCustomerVisit> modelPage = mmsCustomerVisitService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MmsCustomerVisit>(modelPage));
    }

    /**
     * 旅游参团分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void touristTable() {
		final String idcard = getPara("idcard");
		final String userId = AuthUtils.getUserId();
		final int page = getParaToInt("page", 1);
		final int limit = getParaToInt("limit", 10);
		//调用旅居旅游团参团分页接口
		Map<String,String> params = new HashMap<>();
		params.put("UserId", userId);
		params.put("configNo", "CSWO2024082800001");
		params.put("paramStr", "[{\"ParamName\":\"IdCardNo\",\"Value\":\""+idcard+"\"}]");
		params.put("PageSize", String.valueOf(limit));
		params.put("PageIndex", String.valueOf(page));
		logger.info("params.toString==="+params.toString());
		final String resultStr = HttpClientsUtils.httpPostForm(Global.getDataBySIConfigUrl,params,null,null);
		logger.info("resultStr==="+resultStr);
		JSONObject returnJson = JSONObject.parseObject(resultStr);
		final int returnType = returnJson.getIntValue("Type");
		final String returnMsg = returnJson.getString("Msg");
		logger.info("身份证校验返回returnType==="+returnType);
		logger.info("身份证校验返回returnMsg==="+returnMsg);
		Page<JSONObject> modelPage = null;
		if(returnType==1){
			final JSONObject returnData = returnJson.getJSONObject("Data");
			if(returnData!=null && !returnData.isEmpty()) {
				final int totalRows = returnData.getIntValue("total");
				final JSONArray dataArray = returnData.getJSONArray("rows");
				modelPage.setPageNumber(page);
				modelPage.setPageSize(limit);
//				modelPage.setList();
//				modelPage.setTotalPage();
				modelPage.setTotalRow(totalRows);
			}
		}
        renderJson(new DataTable<JSONObject>(modelPage));
    }

    /**
     * 回访记录分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void visitRecordTable() {
    	final String startDate = getPara("startDate");
    	final String endDate = getPara("endDate");
    	final String userId = getPara("userId");
    	Page<MmsCustomerVisit> modelPage = mmsCustomerVisitService.paginateByCondition(startDate, endDate, userId, getParaToInt("page", 1), getParaToInt("limit", 10));
    	renderJson(new DataTable<MmsCustomerVisit>(modelPage));
    }
    
    /**
     * 回访记录分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void visitStatisticsRecordPage() {
    	final String startDate = getPara("startDate");
		final String endDate = getPara("endDate");
		final String receptionUserId = getPara("receptionUserId");
		final String visitType = getPara("visitType");
		final String actionType = getPara("actionType");
		Page<MmsCustomerVisit> visitPage = mmsCustomerVisitService.visitRecordPage(getParaToInt("page"), getParaToInt("limit"), startDate, endDate, receptionUserId, visitType, actionType);
    	renderJson(new DataTable<MmsCustomerVisit>(visitPage));
    }

	/**
	 * 客户信息 新增页面方法
	 */
	public void add(){
		MmsCustomerInfo customerInfo = getBean(MmsCustomerInfo.class, "", true);
		List<MmsMember> memberList = mmsMemberService.findList();
		setAttr("model", customerInfo);
		setAttr("memberList", memberList);
		render("infoForm.html");
	}
	
	/**
	 * 客户信息 修改页面方法
	 */
	public void edit(){
		String id = getPara("id");
		MmsCustomerInfo customerInfo = mmsCustomerInfoService.findById(id);
		if(customerInfo!=null) {
			if(StringUtils.isNotBlank(customerInfo.getProvince())) {
				Area area = areaService.findById(customerInfo.getProvince());
				if(area != null)setAttr("province",area.getAreaName());
			}
			if(StringUtils.isNotBlank(customerInfo.getCity())) {
				Area area = areaService.findById(customerInfo.getCity());
				if(area != null)setAttr("city",area.getAreaName());
			}
			if(StringUtils.isNotBlank(customerInfo.getTown())) {
				Area area = areaService.findById(customerInfo.getTown());
				if(area != null)setAttr("town",area.getAreaName());
			}
			if(StringUtils.isNotBlank(customerInfo.getStreet())) {
				Area area = areaService.findById(customerInfo.getStreet());
				if(area != null)setAttr("street",area.getAreaName());
			}
		}
		List<MmsMember> memberList = mmsMemberService.findList();
		setAttr("model", customerInfo);
		setAttr("memberList", memberList);
		render("infoForm.html");
	}
	
    /**
     * 跟进情况添加页面方法
     */
    public void visitAdd() {
    	MmsCustomerVisit model = getBean(MmsCustomerVisit.class, "", true);
    	model.setVisitDate(DateUtils.parseDate(DateUtils.getDateTime()));
    	model.setReceptionUserId(AuthUtils.getUserId());
    	List<Record> empUserList = new ArrayList<Record>();
		PersOrg persOrg = persOrgService.findByName(OrgType.headquarters, "客服");
		if(persOrg!=null) {
			empUserList = persOrgEmployeeService.empUserList(persOrg.getId());
		}
		List<MainBase> baseList = mainBaseService.findBaseList();
		setAttr("empUserList", empUserList);
		setAttr("baseList", baseList);
    	setAttr("model", model);
        render("visitForm.html");
    }

    /**
     * 跟进情况修改页面方法
     */
    public void visitEdit() {
        final String modelId = getPara("id");
        MmsCustomerVisit model = mmsCustomerVisitService.findById(modelId);
        List<Record> empUserList = new ArrayList<Record>();
		PersOrg persOrg = persOrgService.findByName(OrgType.headquarters, "客服");
		if(persOrg!=null) {
			empUserList = persOrgEmployeeService.empUserList(persOrg.getId());
		}
		List<MainBase> baseList = mainBaseService.findBaseList();
		setAttr("empUserList", empUserList);
		setAttr("baseList", baseList);
        setAttr("model", model);
        render("visitForm.html");
    }

	/**
	 * 客户信息 新增统计页面方法
	 */
	public void newCount(){
		final String startDate = DateUtils.formatDate(DateUtils.getNextDay(new Date(), -1));
		final String endDate = DateUtils.formatDate(DateUtils.getNextDay(new Date(), -1));
		setAttr("startDate", startDate);
		setAttr("endDate", endDate);
		render("newCount.html");
	}
	

    /**
     * 客户回访页面弹窗查询回访记录页面方法
     */
    public void visitCustomer() {
        final String customerId = getPara("id");
        setAttr("customerId", customerId);
        render("visitCustomerIndex.html");
    }


    /**
     * 客户回访页面弹窗查询参团记录页面方法
     */
    public void touristRecord() {
        final String idcard = getPara("idcard");
        setAttr("idcard", idcard);
        render("touristRecordIndex.html");
    }

	
    /**
     * 精确查询添加页面方法
     */
    public void preciseQueryAdd() {
        render("preciseQueryAdd.html");
    }
	
	/**
	 * 客户信息 新增统计列表方法
	 */
	public void newCountList(){
		final String startDate = getPara("startDate");
		final String endDate = getPara("endDate");
		List<Record> newCountList = mmsCustomerInfoService.newCountList(startDate+" 00:00:00", endDate+" 23:59:59");
		renderJson(newCountList);
	}
	
	/**
	 * 客户回访 统计方法
	 */
	public void visitStatisticsLoad(){
		Map<String, Object> result = new HashMap<String, Object>();
		final String startDate = getPara("startDate");
		final String endDate = getPara("endDate");
		final String receptionUserId = getPara("receptionUserId");
		final String visitType = getPara("visitType");
		Record callPass = mmsCustomerVisitService.getCallPassStatistics(startDate, endDate, receptionUserId, visitType);
		Record appealType = mmsCustomerVisitService.getAppealTypeStatistics(startDate, endDate, receptionUserId, visitType);
		Record satisfied = mmsCustomerVisitService.getSatisfiedStatistics(startDate, endDate, receptionUserId, visitType);
		result.put("callPass", callPass);
		result.put("appealType", appealType);
		result.put("satisfied", satisfied);
		renderJson(result);
	}

	/**
	 * 客户信息保存
	 */
	public void save(){
		MmsCustomerInfo model = getBean(MmsCustomerInfo.class,"",true);
		if(StrKit.isBlank(model.getId())) {
			Record customerRecord = Db.findFirst("select * from mms_customer_info where del_flag='0' and phone_number=?", model.getPhoneNumber().trim());
    		if(customerRecord!=null) {
    			renderJson(Ret.fail("msg", "保存失败，手机号已存在!"));
    		}else {
    			model.setId(IdGen.getUUID());
    			final String deptId = persEmpUserService.getDeptIdByUserId(AuthUtils.getUserId());
    			if(StrKit.notBlank(deptId)) {
    				model.setBelongOrgId(deptId);
    				model.setBelongDeptId(deptId);
    			}
    			final String deptName = persEmpUserService.getDeptNameByUserId(AuthUtils.getUserId());
    			if(StrKit.notBlank(deptName)) {
    				if(deptName.contains("客服")) {
    					model.setBelongTo("customer_service");
    				}else if(deptName.contains("分公司")) {
    					model.setBelongTo("sale");
    				}else {
    					model.setBelongTo("customer");
    				}
    			}
    			model.setDataSource(DataSource.CRM);
    			model.setDelFlag(DelFlag.NORMAL);
    			model.setCreateBy(AuthUtils.getUserId());
    			model.setCreateTime(new Date());
    			model.setUpdateTime(new Date());
    			if(model.save()){
    				renderJson(Ret.ok("msg", "操作成功"));
    			}else{
    				renderJson(Ret.fail("msg", "操作失败"));
    			}
    		}
		}else {
			model.setUpdateBy(AuthUtils.getUserId());
			model.setUpdateTime(new Date());
			if(model.update()){
				renderJson(Ret.ok("msg", "操作成功"));
			}else{
				renderJson(Ret.fail("msg", "操作失败"));
			}
		}
	}
	

	/**
	 * 客户登记跟进情况保存
	 */
    public void visitSave() {
    	MmsCustomerVisit model = getBean(MmsCustomerVisit.class, "", true);
    	boolean flag = false;
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    	}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
	
    public void assignSave(){
    	String registerData =  getPara("registerData");
        String assignUserId =  getPara("assignUserId");
        List<MmsCustomerInfo> list = JSONArray.parseArray(registerData, MmsCustomerInfo.class);
        boolean flag = mmsCustomerInfoService.assignSave(list, assignUserId, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "操作成功"));
        }else{
            renderJson(Ret.fail("msg", "操作失败"));
        }
    }
    
	
    public void cancelAssign(){
    	String assignId =  getPara("assignId");
        boolean flag = mmsCustomerAssignService.deleteById(assignId);
        if(flag){
            renderJson(Ret.ok("msg", "操作成功"));
        }else{
            renderJson(Ret.fail("msg", "操作失败"));
        }
    }

	/**
	 * 客户信息删除
	 */
	public void delete(){
		MmsCustomerInfo model = getBean(MmsCustomerInfo.class,"",true);
		if(mmsCustomerInfoService.update(model)){
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}

    public void visitDel(){
    	final MmsCustomerVisit model = getBean(MmsCustomerVisit.class, "", true);
    	if(model!=null && StrKit.notBlank(model.getId())){
    		if(mmsCustomerVisitService.update(model)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else {
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }

	/**
	 * 精确查询客户新增保存
	 */
    public void preciseQueryInfoSave() {
    	boolean flag = false;
    	MmsCustomerInfo info = getBean(MmsCustomerInfo.class,"info",true);
    	info.setId(IdGen.getUUID());
    	final String deptId = persEmpUserService.getDeptIdByUserId(AuthUtils.getUserId());
		if(StrKit.notBlank(deptId)) {
			info.setBelongOrgId(deptId);
			info.setBelongDeptId(deptId);
		}
		final String deptName = persEmpUserService.getDeptNameByUserId(AuthUtils.getUserId());
		if(StrKit.notBlank(deptName)) {
			if(deptName.contains("客服")) {
				info.setBelongTo("customer_service");
			}else if(deptName.contains("分公司")) {
				info.setBelongTo("sale");
			}else {
				info.setBelongTo("customer");
			}
		}
		info.setDataSource(DataSource.CRM);
		info.setIsPreciseQuery("1");
		info.setDelFlag(DelFlag.NORMAL);
		info.setCreateBy(AuthUtils.getUserId());
		info.setCreateTime(new Date());
		info.setUpdateTime(new Date());
		flag = info.save();
		if(flag){
			MmsCustomerVisit visit = getBean(MmsCustomerVisit.class, "visit", true);
			visit.setId(IdGen.getUUID());
			visit.setCustomerId(info.getId());
			visit.setVisitDate(new Date());
			visit.setVisitType("customer_call");
			visit.setReceptionUserId(AuthUtils.getUserId());
			visit.setDelFlag(DelFlag.NORMAL);
			visit.setCreateBy(AuthUtils.getUserId());
			visit.setCreateTime(new Date());
			flag = visit.save();
		}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
}
