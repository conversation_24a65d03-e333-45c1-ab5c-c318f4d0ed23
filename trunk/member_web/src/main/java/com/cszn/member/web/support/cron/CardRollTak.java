package com.cszn.member.web.support.cron;

import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.service.entity.crm.CrmCardRoll;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;

import java.util.Date;
import java.util.List;

/**
 * 卡券失效任务
 */
@Cron("00 0 * * *")
public class CardRollTak implements Runnable {


    @Override
    public void run() {
    	cardRollEnableSet();
    }

    public void cardRollEnableSet(){
    	
    	List<Record> cardRollList = Db.find("select * from crm_card_roll where del_flag='0' and is_enable='0' and valid_type='1' and deploy_status='0' and valid_end_date<?"
				, DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
    	if(cardRollList!=null && cardRollList.size()>0) {
    		for(Record record : cardRollList) {
    			CrmCardRoll cardRoll = new CrmCardRoll();
    			cardRoll.setId(record.getStr("id"));
    			cardRoll.setIsEnable("1");
    			if(cardRoll.update()) {
    				//失效子表数据
    	    		Db.update("update crm_card_roll_record set is_enable='1',expiration_time=? where card_roll_id=? and is_use='0' and is_enable='0' "
							,DateUtils.formatDate(record.getDate("valid_end_date"),"yyyy-MM-dd")+" 23:59:59", record.getStr("id"));
    			}
    		}
    	}

		List<Record> bindingCardRollList = Db.find("select * from crm_card_roll where del_flag='0' and is_enable='0' " +
				"and valid_type='2' and deploy_status='0' and final_valid_date<?",DateUtils.formatDate(new Date(),"yyyy-MM-dd"));
		if(bindingCardRollList!=null && bindingCardRollList.size()>0) {
			for(Record record : bindingCardRollList) {
				CrmCardRoll cardRoll = new CrmCardRoll();
				cardRoll.setId(record.getStr("id"));
				cardRoll.setIsEnable("1");
				if(cardRoll.update()) {
					//失效子表数据
					Db.update("update crm_card_roll_record set is_enable='1',expiration_time=? where card_roll_id=? and is_use='0' and is_enable='0' "
							,DateUtils.formatDate(record.getDate("final_valid_date"),"yyyy-MM-dd")+" 23:59:59", record.getStr("id"));
				}
			}
		}
    }
}
