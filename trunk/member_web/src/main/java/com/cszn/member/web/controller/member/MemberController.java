package com.cszn.member.web.controller.member;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sys.AreaService;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.Area;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;


@RequestMapping(value="/mms/member", viewPath="/modules_page/mms/member")
public class MemberController extends BaseController {
	
	@Inject
    private FinaMembershipCardService finaMembershipCardService;
	@Inject
	private MmsMemberService mmsMemberService;
	@Inject
	private AreaService areaService;



	/**
	 * 跳转会员档案界面
	 */
	public void index(){
		render("memberIndex.html");
	}


	/**
	 * 会员档案列表
	 */
	@Clear(LogInterceptor.class)
	public void findListPage(){

		MmsMember member = getBean(MmsMember.class,"",true);
		Page<MmsMember> page = mmsMemberService.findList(getParaToInt("page"), getParaToInt("limit"),member);
		renderJson(new DataTable<MmsMember>(page));
	}
    
    @Clear(LogInterceptor.class)
    public void getMemberInfo(){
    	final String memberId = getPara("memberId");
    	MmsMember model = mmsMemberService.findById(memberId);
    	if(model!=null) {
    		String province = "";
    		String city = "";
    		String town = "";
    		String street = "";
    		if(StrKit.notBlank(model.getProvince())) {
    			Area area = areaService.findById(model.getProvince());
    			province+=area.getAreaName();
    		}
    		if(StrKit.notBlank(model.getCity())) {
    			Area area = areaService.findById(model.getCity());
    			city+=area.getAreaName();
    		}
    		if(StrKit.notBlank(model.getTown())) {
    			Area area = areaService.findById(model.getTown());
    			town+=area.getAreaName();
    		}
    		if(StrKit.notBlank(model.getStreet())) {
    			Area area = areaService.findById(model.getStreet());
    			street+=area.getAreaName();
    		}
    		renderJson(Ret.ok("msg", "加载成功!").set("member", model).set("province", province).set("city", city).set("town", town).set("street", street));
    	}else {
    		renderJson(Ret.fail("msg", "加载失败!"));
    	}
    }


	/**
	 * 会员档案删除
	 */
	public void delete(){
		String id = getPara("id");
		boolean flag = mmsMemberService.delMmsMember(id, AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "作废成功"));
		}else{
			renderJson(Ret.fail("msg", "作废失败"));
		}
	}


	/**
	 * 跳转会员档案 新增/修改界面
	 */
	public void form(){
		String id = getPara("id");
		MmsMember member = mmsMemberService.get(id);
		if(member != null){
			if(StringUtils.isNotBlank(member.getProvince())) {
				Area area = areaService.findById(member.getProvince());
				if(area != null)setAttr("province",area.getAreaName());
			}
			if(StringUtils.isNotBlank(member.getCity())) {
				Area area = areaService.findById(member.getCity());
				if(area != null)setAttr("city",area.getAreaName());
			}
			if(StringUtils.isNotBlank(member.getTown())) {
				Area area = areaService.findById(member.getTown());
				if(area != null)setAttr("town",area.getAreaName());
			}
			if(StringUtils.isNotBlank(member.getStreet())) {
				Area area = areaService.findById(member.getStreet());
				if(area != null)setAttr("street",area.getAreaName());
			}
		}
		setAttr("commonUpload",Global.commonUpload);
		setAttr("member", member);
		render("memberForm.html");
	}


	/**
	 * 会员档案保存
	 */
	public void save(){
		MmsMember member = getBean(MmsMember.class,"member",true);
		String flag = mmsMemberService.saveMmsMember(member,AuthUtils.getUserId());
		if("suc".equals(flag)){
			renderJson(Ret.ok("msg", "保存成功"));
		}else if("".equals(flag)){
			renderJson(Ret.fail("msg", "身份证号不可重复"));
		}else{
			renderJson(Ret.fail("msg", "保存失败"));
		}
	}


	/**
	 * 批量删除
	 */
	public void batchDel(){
		String memberData =  getPara("memberData");
		List<MmsMember> list = JSONArray.parseArray(memberData,MmsMember.class);
		boolean flag = mmsMemberService.batchDelMember(list,AuthUtils.getUserId());
		if(flag){
			renderJson(Ret.ok("msg", "批量作废成功"));
		}else{
			renderJson(Ret.fail("msg", "批量作废失败"));
		}
	}

	/**
	 * 会员生日管理
	 */
	public void birthdayMemberIndex(){
		render("birthdayMemberIndex.html");
	}
	/**
	 * 获取生日的会员
	 */
	public void findBirthdayMemberPage(){
		String month=getPara("month");
		Page<MmsMember> memberPage=mmsMemberService.findBirthdayMemberList(getParaToInt("page"),getParaToInt("limit"),month);
		renderJson(new DataTable<MmsMember>(memberPage));
	}
}
