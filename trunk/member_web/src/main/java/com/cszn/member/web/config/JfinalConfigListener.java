package com.cszn.member.web.config;

import com.cszn.integrated.base.common.AppInfo;
import com.cszn.integrated.base.plugin.shiro.ShiroUtils;
import com.cszn.integrated.base.web.handler.CustomActionHandler;
import com.cszn.integrated.base.web.render.AppRenderFactory;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.member.web.support.log.LogInterceptor;
import com.cszn.member.web.validator.CorsInterceptor;
import com.jfinal.config.Constants;
import com.jfinal.config.Interceptors;
import com.jfinal.config.Routes;
import com.jfinal.ext.handler.ContextPathHandler;
import com.jfinal.json.FastJsonFactory;
import com.jfinal.log.Log4jLogFactory;
import com.jfinal.template.Engine;
import io.jboot.Jboot;
import io.jboot.aop.jfinal.JfinalHandlers;
import io.jboot.aop.jfinal.JfinalPlugins;
import io.jboot.core.listener.JbootAppListenerBase;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class JfinalConfigListener extends JbootAppListenerBase {
	
    @Override
    public void onConstantConfig(Constants constants) {
        constants.setError401View("/template/401.html");
        constants.setError403View("/template/403.html");
        constants.setError404View("/template/404.html");
        constants.setError500View("/template/500.html");
        constants.setJsonFactory(new FastJsonFactory());
        constants.setRenderFactory(new AppRenderFactory());
        constants.setLogFactory(new Log4jLogFactory());

    }

    @Override
    public void onRouteConfig(Routes routes) {
        routes.setBaseViewPath("/template");
    }

    @Override
    public void onEngineConfig(Engine engine) {
        engine.setDevMode(true);
        AppInfo app = Jboot.config(AppInfo.class);
        engine.addSharedObject("APP", app);
        engine.addSharedObject("RESOURCE_HOST", app.getResourceHost());
        engine.addSharedMethod(new ShiroUtils());
    }

    @Override
    public void onInterceptorConfig(Interceptors interceptors) {
        interceptors.add(new LogInterceptor());
        interceptors.add(new CorsInterceptor());
//        interceptors.add(new AuthInterceptor());
//        interceptors.add(new NotNullParaInterceptor("/template/exception.html"));
//        interceptors.add(new BusinessExceptionInterceptor("/template/exception.html"));
    }

    @Override
    public void onPluginConfig(JfinalPlugins plugins) {

    }

    @Override
    public void onHandlerConfig(JfinalHandlers handlers) {
        handlers.setActionHandler(new CustomActionHandler());
        handlers.add(new ContextPathHandler("ctxPath"));
    }

    @Override
    public void onStartBefore() {

    }

    @Override
    public void onStop() {
    }

    @Override
    public void onStart() {
        /** 集群模式下验证码使用 redis 缓存 */
//        CaptchaManager.me().setCaptchaCache(new CaptchaCache());
    	Global.mpAppid = Jboot.configValue("mpAppId");
    	Global.mpAppSecret = Jboot.configValue("mpAppSecret");
    	Global.sojournUrl = Jboot.configValue("sojournUrl");
    	Global.fileUrlPrefix = Jboot.configValue("fileUrlPrefix");
    	Global.uploadPath = Jboot.configValue("uploadPath");
    	Global.excelTemplatePath = Jboot.configValue("excelTemplatePath");
    	Global.uploadPathLinux = Jboot.configValue("uploadPath.linux");
    	Global.msgTemplate = Jboot.configValue("msgTemplate");
    	Global.sendMsgUrl = Jboot.configValue("sendMsgUrl");
    	Global.templateKeyWord = Jboot.configValue("templateKeyWord");
    	Global.logoImg = Jboot.configValue("logoImg");
    	Global.qrcodeUrl = Jboot.configValue("qrcodeUrl");
    	Global.commonUpload = Jboot.configValue("commonUpload");
    	Global.msgOpen = Jboot.configValue("msgOpen");
    	Global.bioHead = Jboot.configValue("bioHead");
        Global.faceComplianceUrl = Jboot.configValue("faceComplianceUrl");
        Global.faceComplianceOpen = Jboot.configValue("faceComplianceOpen");
        Global.sendMsgCompanyName = Jboot.configValue("sendMsgCompanyName");
        Global.getMsgUrl = Jboot.configValue("getMsgUrl");
        Global.getMsgCountUrl = Jboot.configValue("getMsgCountUrl");
        Global.signReadUrl = Jboot.configValue("signReadUrl");
        Global.sendMessageUrl=Jboot.configValue("sendMsgUrl");
        Global.getStockModelCountUrl=Jboot.configValue("getStockModelCountUrl");
        Global.warehouseOutToStockUrl=Jboot.configValue("warehouseOutToStockUrl");
        Global.getWXAccessTokenUrl=Jboot.configValue("getWXAccessTokenUrl");
        Global.activityQrcodeUrl=Jboot.configValue("activityQrcodeUrl");
        Global.formNo=Jboot.configValue("formNo");
        Global.codeGenerateUrl=Jboot.configValue("codeGenerateUrl");
        Global.releaseCodeUrl=Jboot.configValue("releaseCodeUrl");
        Global.baseCardNumbers= Jboot.configValue("baseCardNumbers");
        Global.paymentQRCodeScanUrl=Jboot.configValue("paymentQRCodeScanUrl");
        Global.GetCardNoByCodeUrl=Jboot.configValue("GetCardNoByCodeUrl");
        Global.orgUrl = Jboot.configValue("orgUrl");
        Global.hrmUrl = Jboot.configValue("hrmUrl");
        Global.createPayOrderUrl=Jboot.configValue("createPayOrderUrl");
        Global.payBtoCUrl=Jboot.configValue("payBtoCUrl");
        Global.getPayOrderStateUrl=Jboot.configValue("getPayOrderStateUrl");
        Global.orderTypeId=Jboot.configValue("orderTypeId");
        Global.getOperationDetailsUrl=Jboot.configValue("getOperationDetailsUrl");
        Global.checkIdCardUrl=Jboot.configValue("checkIdCardUrl");
        Global.getDataBySIConfigUrl=Jboot.configValue("getDataBySIConfigUrl");

        Global.executorService=new ThreadPoolExecutor(10, 50, 0L
                , TimeUnit.MILLISECONDS
                , new LinkedBlockingQueue<>(1024)
                , new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r);
            }
        },new ThreadPoolExecutor.AbortPolicy());
//    	BioServer bioServer = new BioServer();
//    	bioServer.start();
    }
}
