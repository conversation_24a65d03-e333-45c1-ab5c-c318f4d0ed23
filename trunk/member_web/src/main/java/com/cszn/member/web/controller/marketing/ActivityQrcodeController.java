package com.cszn.member.web.controller.marketing;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.ZXingCode;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.member.MmsActivityQrcodeService;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.main.MainBranchOffice;
import com.cszn.integrated.service.entity.member.MmsActivityQrcode;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.User;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.PathKit;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * @Description 营销二维码活动管理
 * <AUTHOR>
 * @Date 2019/7/9
 **/
@RequestMapping(value="/mms/activityQrcode", viewPath="/modules_page/mms/marketing")
public class ActivityQrcodeController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(ActivityQrcodeController.class);


    @Inject
    private MmsActivityQrcodeService mmsActivityQrcodeService;

    @Inject
    private MainBranchOfficeService mainBranchOfficeService;

    @Inject
    private UserService userService;



    /**
     * 跳转营销二维码活动界面
     */
    public void index(){
        List<MainBranchOffice> officeList = mainBranchOfficeService.getUnDelBranchOffice();
        setAttr("officeList",officeList);
        render("marketingIndex.html");
    }


    /**
     * 二维码活动列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String name = getPara("name");
        String officeId = getPara("officeId");
        Page<Record> page = mmsActivityQrcodeService.findList(getParaToInt("page"), getParaToInt("limit"),name,officeId);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 删除二维码
     */
    public void delete(){
        String id = getPara("id");
        boolean flag = mmsActivityQrcodeService.delActivityQrcode(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "删除成功"));
        }else{
            renderJson(Ret.fail("msg", "删除失败"));
        }
    }



    /**
     * 跳转 新增/查看界面
     */
    public void form(){
        String id = getPara("id");
        MmsActivityQrcode qrcode = mmsActivityQrcodeService.get(id);
        if(qrcode != null) {
            //获取用户
            User user = userService.findById(qrcode.getUserId());
            setAttr("user",user);
            //获取二维码
            File logoFile = new File(PathKit.getWebRootPath() + Global.logoImg);
            String base64Img = "";
            try {
                base64Img = ZXingCode.drawLogoQRCode(logoFile, String.format(Global.qrcodeUrl, qrcode.getQrcode()), "");
            } catch (Exception e) {
                e.printStackTrace();
            }
            setAttr("imgName",base64Img);
        }
        setAttr("qrcode",qrcode);
        render("marketingForm.html");
    }



    /**
     * 保存
     */
    public void save(){
        MmsActivityQrcode qrcode = getBean(MmsActivityQrcode.class,"qrcode",true);
        String imgName = getPara("imgName");
        if(StringUtils.isBlank(qrcode.getUserId())){ renderJson(Ret.fail("msg", "营销人不能为空")); return; }
        if(StringUtils.isBlank(qrcode.getQrcode())){ renderJson(Ret.fail("msg", "未生成二维码,不可保存")); return; }
        if(StringUtils.isBlank(imgName)){ renderJson(Ret.fail("msg", "未生成二维码,不可保存")); return; }
        //去重
        Long count = Db.queryLong("select count(*) from mms_activity_qrcode where del_flag = 0 and user_id = ?",qrcode.getUserId());
        if(count > 0 ){ renderJson(Ret.fail("msg", "此营销人已有保存记录")); return; }

        qrcode.setId(IdGen.getUUID());
        qrcode.setDelFlag("0");
        qrcode.setCreateBy(AuthUtils.getUserId());
        qrcode.setUpdateBy(AuthUtils.getUserId());
        qrcode.setCreateTime(new Date());
        qrcode.setUpdateTime(new Date());
        if(qrcode.save()){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }

    }


    /**
     * 跳转选择分公司营销人界面
     */
    public void chooseUserMarket(){
        List<MainBranchOffice> officeList = mainBranchOfficeService.getUnDelBranchOffice();
        setAttr("officeList",officeList);
        render("userInfo.html");
    }


    /**
     * 用于营销活动二维码界面
     */
    @Clear(LogInterceptor.class)
    public void findListMarket(){
        User user = getBean(User.class, "", true);
        String officeId = getPara("officeId");
        Page<Record> userPage = userService.findList(getParaToInt("page", 1), getParaToInt("limit", 10),user,officeId);
        renderJson(new DataTable<Record>(userPage));
    }




    /**
     * 生成二维码
     */
    public void createQrcode(){
        String uuidStr = IdGen.getUUID();
        File logoFile = new File(PathKit.getWebRootPath() + Global.logoImg);
        String base64Img = "";

        try {
            base64Img = ZXingCode.drawLogoQRCode(logoFile,String.format(Global.qrcodeUrl,uuidStr), "");
        } catch (Exception e) {
            e.printStackTrace();
            renderJson(Ret.fail("msg", "生成二维码失败"));
            logger.info("二维码工具类生成异常：" +e);
            return;
        }
        renderJson(Ret.ok("msg", "生成二维码成功").set("uuidStr",uuidStr).set("imgName",base64Img));
    }
}
