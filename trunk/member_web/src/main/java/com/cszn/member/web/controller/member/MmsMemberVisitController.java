/**
 * 
 */
package com.cszn.member.web.controller.member;

import java.util.Date;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsMemberVisitService;
import com.cszn.integrated.service.entity.member.MmsMemberVisit;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * Created by LiangHuiLing on 2020年12月11日
 *
 * MmsMemberVisitController
 */
@RequestMapping(value="/mms/memberVisit", viewPath="/modules_page/mms/memberVisit")
public class MmsMemberVisitController extends BaseController {

	@Inject
    private MmsMemberVisitService mmsMemberVisitService;
	
    public void index() {
    	final String memberId = getPara("memberId");
    	setAttr("memberId", memberId);
        render("memberVisitIndex.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	MmsMemberVisit model = getBean(MmsMemberVisit.class, "", true);
        Page<MmsMemberVisit> modelPage = mmsMemberVisitService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MmsMemberVisit>(modelPage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	MmsMemberVisit model = getBean(MmsMemberVisit.class, "", true);
    	setAttr("model", model);
        render("memberVisitForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String modelId = getPara("id");
        setAttr("model", mmsMemberVisitService.findById(modelId));
        render("memberVisitForm.html");
    }
    
    public void save() {
    	MmsMemberVisit model = getBean(MmsMemberVisit.class, "", true);
    	boolean flag = false;
    	if(StrKit.isBlank(model.getId())) {
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		flag = model.update();
    	}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }
    
    public void del(){
    	final MmsMemberVisit model = getBean(MmsMemberVisit.class, "", true);
    	if(model!=null && StrKit.notBlank(model.getId())){
    		if(mmsMemberVisitService.update(model)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else {
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}else{
    		renderJson(Ret.fail("msg", "缺少参数，操作失败！"));
    	}
    }
}
