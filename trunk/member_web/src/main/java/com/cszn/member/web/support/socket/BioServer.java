package com.cszn.member.web.support.socket;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import com.cszn.integrated.service.api.member.BioApiService;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import io.jboot.Jboot;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class BioServer extends Thread {
	
	private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    private BioApiService bioApiService = Jboot.service(BioApiService.class);

	
	@Override
    public void run() {
		logger.info("-----------Socket Start---------");
        int port = 8001;
        ServerSocket serverSocket = null;
        try {
            serverSocket = new ServerSocket(port);
            Socket socket = null;
            while (true) {
                socket = serverSocket.accept();
                new Thread(new BioServerHandler(socket,bioApiService)).start();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (serverSocket != null) {
                    serverSocket.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
