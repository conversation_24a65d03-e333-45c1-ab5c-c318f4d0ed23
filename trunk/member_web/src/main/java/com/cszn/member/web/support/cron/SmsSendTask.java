package com.cszn.member.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.api.member.MmsSmsDetailService;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.member.MmsSmsDetail;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

//@Cron("*/3 * * * *")
public class SmsSendTask implements Runnable {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);
    @Inject
    private MmsSmsDetailService mmsSmsDetailService;

    @Override
    public void run() {
        //短信发送
        try {
            smsSend(500);
        } catch (Exception e) {
            logger.error("人文关怀短信发送失败:[{}]",e);
        }
    }

    public void smsSend(int size){

        List<MmsSmsDetail> findNotSentDetail=mmsSmsDetailService.findNotSentDetail(size);
        if(findNotSentDetail==null || findNotSentDetail.size()==0){
            return;
        }
        Map<String,String> params=new HashMap<>();
        params.put("tempId", SendType.customContent.getTemplateId());

        Map<String,Object> map=new HashMap<>();
        for(MmsSmsDetail detail:findNotSentDetail){

            if(map.containsKey(detail.getSmsId())){
                Map<String,String> sendMap=(Map<String,String>)map.get(detail.getSmsId());
                sendMap.put("mobiles",sendMap.get("mobiles")+detail.getPhoneNum()+",");
            }else{
                Map<String,String> sendMap=new HashMap<>();
                sendMap.put("sendContent",detail.getSendContent());
                sendMap.put("mobiles",detail.getPhoneNum()+",");

                map.put(detail.getSmsId(),sendMap);
            }
        }

        for(String key:map.keySet()){
            Map<String,String> sendMap=(Map<String,String>)map.get(key);
            String mobiles=sendMap.get("mobiles");
            mobiles=mobiles.substring(0,mobiles.length()-1);
            params.put("mobile",mobiles);
            params.put("data", "{\"content\":\""+sendMap.get("sendContent")+"\"}");

            String resultStr= HttpClientsUtils.httpPostForm(Global.sendMessageUrl, params,null,"UTF-8");
            List<MmsSmsDetail> updateDetailList=new ArrayList<>();
            if(resultStr.startsWith("{") && resultStr.endsWith("}")){
                JSONObject object= JSON.parseObject(resultStr);
                if(object.containsKey("Type") && "1".equals(object.getString("Type"))){
                    for(MmsSmsDetail detail:findNotSentDetail){
                        if(key.equals(detail.getSmsId())){
                            detail.setSendStatus("1");
                            detail.setUpdateDate(new Date());
                            updateDetailList.add(detail);
                        }
                    }
                }else{
                    for(MmsSmsDetail detail:findNotSentDetail){
                        if(key.equals(detail.getSmsId())){
                            detail.setSendStatus("2");
                            detail.setErrorMsg(resultStr);
                            detail.setUpdateDate(new Date());
                            updateDetailList.add(detail);
                        }
                    }

                }

            }
            if(updateDetailList.size()>0){
                Db.batchUpdate(updateDetailList,updateDetailList.size());
            }

        }

    }

}
