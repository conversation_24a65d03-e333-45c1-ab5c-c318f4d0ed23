package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsMeasureBpService;
import com.cszn.integrated.service.api.member.MmsMeasureHrService;
import com.cszn.integrated.service.api.member.MmsMeasureStepsService;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.Map;

@RequestMapping(value = "/mms/healthDataManage",viewPath = "/modules_page/mms/healthData")
public class HealthDataManageController extends BaseController {

    @Inject
    private MmsMeasureBpService mmsMeasureBpService;

    @Inject
    private MmsMeasureHrService mmsMeasureHrService;

    @Inject
    private MmsMeasureStepsService mmsMeasureStepsService;


    /**
     * 跳转会员健康数据界面
     */
    public void index(){
        render("healthDataIndex.html");
    }


    /**
     * 获取血压图表
     */
    @Clear(LogInterceptor.class)
    public void getBloodPressureSeries(){
        String dataRecord = getPara("dataRecord");
        String memberId = getPara("memberId");
        Map<String, Object> datas = new HashMap<String, Object>();
        datas.put("flag", Boolean.TRUE);
        datas.put("msg", "加载成功");
        Map<String,Object> bpMap = null;
        try {
            bpMap = mmsMeasureBpService.getbpData(memberId,dataRecord);
        } catch (Exception e) {
            e.printStackTrace();
            datas.put("result", Boolean.FALSE);
            datas.put("msg", "加载失败");
        }
        datas.put("measureTimeArray", bpMap.get("measureTimeArray"));
        datas.put("measureJsonData", bpMap.get("measureJsonData"));
        renderJson(datas);
    }


    /**
     * 获取心率图表
     */
    @Clear(LogInterceptor.class)
    public void getHeartRateSeries(){
        String dataRecord = getPara("dataRecord");
        String memberId = getPara("memberId");
        Map<String, Object> datas = new HashMap<String, Object>();
        datas.put("flag", Boolean.TRUE);
        datas.put("msg", "加载成功");
        Map<String,Object> hrMap = null;
        try {
            hrMap = mmsMeasureHrService.gethrData(memberId,dataRecord);
        } catch (Exception e) {
            e.printStackTrace();
            datas.put("result", Boolean.FALSE);
            datas.put("msg", "加载失败");
        }
        datas.put("measureTimeArray", hrMap.get("measureTimeArray"));
        datas.put("measureJsonData", hrMap.get("measureJsonData"));
        renderJson(datas);
    }


    /**
     * 获取步数，里程数，卡路里
     */
    @Clear(LogInterceptor.class)
    public void getStepsSeries(){
        String dataRecord = getPara("dataRecord");
        String memberId = getPara("memberId");
        Map<String, Object> datas = new HashMap<String, Object>();
        datas.put("flag", Boolean.TRUE);
        datas.put("msg", "加载成功");
        Map<String,Object> stepsMap = null;
        try {
            stepsMap = mmsMeasureStepsService.getStepsData(memberId,dataRecord);
        } catch (Exception e) {
            e.printStackTrace();
            datas.put("result", Boolean.FALSE);
            datas.put("msg", "加载失败");
        }
        datas.put("measureTimeArray", stepsMap.get("measureTimeArray"));
        datas.put("measureJsonData", stepsMap.get("measureJsonData"));
        renderJson(datas);
    }
}
