package com.cszn.member.web.controller.wxBackstage;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsWxUserService;
import com.cszn.integrated.service.entity.member.MmsWxUser;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

/**
 * @Description 微信用户Controller
 * <AUTHOR>
 * @Date 2019/4/11
 **/

@RequestMapping(value="/mms/wxuser", viewPath="/modules_page/mms/wxUser")
public class WxUserController extends BaseController {

	@Inject
    private MmsWxUserService mmsWxUserService;


    /**
     * 跳转微信用户界面
     */
    public void index(){
        render("wxUserIndex.html");
    }


    /**
     * 微信用户列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){

        MmsWxUser wxUser = getBean(MmsWxUser.class,"",true);
       Page<MmsWxUser> page = mmsWxUserService.findList(getParaToInt("page"),getParaToInt("limit"),wxUser);
        renderJson(new DataTable<MmsWxUser>(page));
    }


    /**
     * 跳转微信用户详情界面
     */
    public void form(){
        String id = getPara("id");
        MmsWxUser wxUser = mmsWxUserService.findById(id);
        setAttr("wxUser",wxUser);
        render("wxUserForm.html");
    }

}
