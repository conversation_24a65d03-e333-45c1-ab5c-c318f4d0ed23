package com.cszn.member.web.controller.equipment;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.BioHelper;
import com.cszn.integrated.base.utils.SendBioHelper;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.member.MmsCardEquipmentService;
import com.cszn.integrated.service.api.member.MmsEquipmentTypeService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.member.MmsCardEquipment;
import com.cszn.integrated.service.entity.member.MmsEquipmentType;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @Description 会员卡设备管理
 * <AUTHOR>
 * @Date 2019/6/27
 **/
@RequestMapping(value="/mms/cardEquipment", viewPath="/modules_page/mms/cardEquipment")
public class CardEquipmentController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(CardEquipmentController.class);

    @Inject
    private MmsCardEquipmentService mmsCardEquipmentService;

    @Inject
    private FinaMembershipCardService finaMembershipCardService;

    @Inject
    private MmsEquipmentTypeService mmsEquipmentTypeService;


    @Inject
    private DictService dictService;


    /**
     * 跳转会员卡设备界面
     */
    public void index(){
        List<Dict> typeList = dictService.getListByTypeOnUse("equipment_type");
        setAttr("typeList",typeList);
       render("cardEquipmentIndex.html");
    }


    /**
     * 会员卡设备列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String cardNumber = getPara("cardNumber");
        String equipmentNo = getPara("equipmentNo");
        String equipmentType = getPara("equipmentType");
        Page<Record> page = mmsCardEquipmentService.findListPage(getParaToInt("page"), getParaToInt("limit"),cardNumber,equipmentNo,equipmentType);
        renderJson(new DataTable<Record>(page));
    }



    /**
     * 删除
     */
    public void delete(){
        String id = getPara("id");
        MmsCardEquipment cardEquipment = mmsCardEquipmentService.get(id);
        if(cardEquipment != null){
            boolean flag = mmsCardEquipmentService.delCardEquipment(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }



    /**
     * 跳转新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        String type = getPara("type");
        List<Record> typeList = mmsEquipmentTypeService.getEquipmentTypeList();
        MmsCardEquipment cardEquipment = mmsCardEquipmentService.get(id);
        FinaMembershipCard card = null;
        if(cardEquipment != null){
            card = finaMembershipCardService.get(cardEquipment.getCardId());
        }
        setAttr("typeList",typeList);
        setAttr("cardEquipment",cardEquipment);
        setAttr("card",card);
        if(StringUtils.isBlank(type)){
            render("cardEquipmentForm.html");
        }else{
            render("setForm.html");
        }
    }


    /**
     * 保存
     */
    public void save(){
        MmsCardEquipment cardEquipment = getBean(MmsCardEquipment.class,"cardEquipment",true);
        String cardNumber = getPara("cardNumber");
        if(cardEquipment == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        FinaMembershipCard card = finaMembershipCardService.getByCardNumber(cardNumber);
        if(card == null || StringUtils.isBlank(card.getId())){ renderJson(Ret.fail("msg", "系统不存在该会员卡")); return; }

        MmsEquipmentType type = mmsEquipmentTypeService.findById(cardEquipment.getEquipmentTypeId());
        if(type == null || StringUtils.isBlank(type.getId())){ renderJson(Ret.fail("msg", "系统不存在该设备类型")); return; }

        if(StringUtils.isNotBlank(cardEquipment.getId())){
            MmsCardEquipment cardEqExist = mmsCardEquipmentService.get(cardEquipment.getId());
            if(cardEqExist == null){ renderJson(Ret.fail("msg", "保存失败")); return; }
        }
        cardEquipment.setCardId(card.getId());
        cardEquipment.setEquipmentTypeId(type.getId());
        String flag = mmsCardEquipmentService.saveCardEquipment(cardEquipment,AuthUtils.getUserId());
        if("suc".equals(flag)){
            renderJson(Ret.ok("msg", "保存成功"));
        }else if("".equals(flag)){
            renderJson(Ret.fail("msg", "该会员卡已绑定该型号测量设备"));
        } else {
                renderJson(Ret.fail("msg", "保存失败"));
        }
    }



    /**
     *会员卡bio手环设备设置
     */
    public void cardEqSet(){
        String id = getPara("equipmentId");
        String equipmentNo = getPara("equipmentNo");
        String stepSwitch = getPara("stepSwitch");
        String times = null;
        if(StringUtils.isNotBlank(getPara("times"))) {
            times = TimeUtils.dealTimeRange(getPara("times"));
        }

        //计步设置
        String commandStep = BioHelper.dealSendCommand(Global.bioHead,equipmentNo,"0004","PEDO",new String[]{stepSwitch});
        logger.info("计步开关命令：[{}]",commandStep);
        //计步时间段
        String commandTimes = null;
        if(StringUtils.isNotBlank(times)){
            commandTimes = BioHelper.dealSendCommand(Global.bioHead,equipmentNo,"002A","WALKTIME",new String[]{times});
            logger.info("计步时间命令：[{}]",commandTimes);
        }

        String flag1 = SendBioHelper.sendBioData(equipmentNo,commandStep);
        logger.info("计步开关：[{}]",flag1);
        String flag2 = "";
        if(StringUtils.isNotBlank(commandTimes)){
            flag2 = SendBioHelper.sendBioData(equipmentNo,commandTimes);
            logger.info("计步时间：[{}]",flag2);
        }
        if(StringUtils.isNotBlank(commandTimes)){
            if("suc".equals(flag1) && "suc".equals(flag2)){
                mmsCardEquipmentService.updateSet(id,"3");
                renderJson(Ret.ok("msg", "已设置，请等待"));
            }else if("null".equals(flag1) || "null".equals(flag2)){
                renderJson(Ret.fail("msg", "设备不在线，请稍后再试"));
            }else{
                renderJson(Ret.fail("msg", "设置失败"));
            }

        }else{
            if("suc".equals(flag1)){
                mmsCardEquipmentService.updateSet(id,"3");
                renderJson(Ret.ok("msg", "已设置，请等待"));
            }else if("null".equals(flag1)){
                renderJson(Ret.fail("msg", "设备不在线，请稍后再试"));
            }else{
                renderJson(Ret.fail("msg", "设置失败"));
            }
        }
    }
}
