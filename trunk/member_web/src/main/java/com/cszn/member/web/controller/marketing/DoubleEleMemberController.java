package com.cszn.member.web.controller.marketing;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsMemberPrizeService;
import com.cszn.integrated.service.entity.member.MmsMemberPrize;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;


@RequestMapping(value="/mms/doubleEleMember", viewPath="/modules_page/mms/doubleEle")
public class DoubleEleMemberController extends BaseController {

    @Inject
    private MmsMemberPrizeService mmsMemberPrizeService;


    /**
     * 跳转双十一营销会员界面
     */
    public void index(){
        render("eleMemberIndex.html");
    }


    /**
     * 分页
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MmsMemberPrize memberPrize = getBean(MmsMemberPrize.class,"",true);
        Page<Record> page = mmsMemberPrizeService.findPage(getParaToInt("page"), getParaToInt("limit"),memberPrize);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 删除
     */
    public void delete(){
        String id = getPara("id");
        boolean flag = mmsMemberPrizeService.delEleMember(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }



    /**
     * 跳转编辑页面
     */
    public void form(){
        String id = getPara("id");
        Record record = mmsMemberPrizeService.getMemberPrize(id);
        setAttr("memberPrize",record);
        render("eleMemberForm.html");
    }


    /**
     * 编辑保存
     */
    public void save(){
        MmsMemberPrize memberPrize = getBean(MmsMemberPrize.class,"memberPrize",true);
        if(StringUtils.isBlank(memberPrize.getId())){ renderJson(Ret.fail("msg", "此记录标识为空，不可保存")); return;}
        memberPrize.setUpdateBy(AuthUtils.getUserId());
        memberPrize.setUpdateTime(new Date());
        if(memberPrize.update()){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }
}
