package com.cszn.member.web.controller.wxapi;

import java.util.*;

import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.api.crm.*;
import com.cszn.integrated.service.entity.fina.FinaCardRoll;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.cors.EnableCORS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecord;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecordPayment;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecordUse;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecordVerify;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.member.web.support.log.LogInterceptor;
import com.cszn.member.web.validator.CorsInterceptor;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;

import io.jboot.web.controller.annotation.RequestMapping;

@JCors
@RequestMapping(value = "/cardRollApi",viewPath = "")
@Clear(LogInterceptor.class)
public class CardRollApiController extends BaseController {

	@Inject
	private CrmCardRollService crmCardRollService;
	@Inject
	private CrmCardRollBranchOfficeService crmCardRollBranchOfficeService;
	@Inject
	private CrmCardRollBaseService crmCardRollBaseService;
	@Inject
	private CrmCardRollRecordService crmCardRollRecordService;
	@Inject
	private CrmCardRollRecordVerifyService crmCardRollRecordVerifyService;
	@Inject
	private CrmCardRollRecordPaymentService crmCardRollRecordPaymentService;
	@Inject
	private CrmCardRollRecordUseService crmCardRollRecordUseService;
	@Inject
    SmsSendRecordService smsSendRecordService;

    private static Logger logger = LoggerFactory.getLogger(CardRollApiController.class);

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void myRollList(){
		Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "1");
		result.put("msg", "失败");
		result.put("data", new ArrayList<>());
    	CrmCardRollRecordVerify model = getBean(CrmCardRollRecordVerify.class,"",true);
    	if(StrKit.isBlank(model.getFullName())&&StrKit.isBlank(model.getIdcard())&&StrKit.isBlank(model.getPhoneNumber())) {
    		result.put("msg", "姓名/身份证/手机号请至少传入一个");
    	}else {
    		result.put("code", "0");
    		result.put("msg", "成功");
    		result.put("data", crmCardRollRecordVerifyService.findCustomerRollList(model));
    	}
    	renderJson(result);
    }
    
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void scanCardRoll(){
    	Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "1");
		result.put("msg", "失败");
		result.put("data", null);
		
    	final String rollRecordId = getPara("id");
    	if(StrKit.isBlank(rollRecordId)) {
    		result.put("msg", "id不能为空");
    	}else {
    		CrmCardRollRecord rollRecord = crmCardRollRecordService.findById(rollRecordId);
    		if(rollRecord!=null) {
    			JSONObject jsonObj = new JSONObject();
    			jsonObj.put("typeName", Db.queryStr("select t.type_name from crm_card_roll r left join crm_card_roll_type t on t.id=r.roll_type_id where r.del_flag='0' and r.id=?", rollRecord.getCardRollId()));
    			jsonObj.put("rollNumber", rollRecord.getRollNumber());
    			jsonObj.put("isEnable", rollRecord.getIsEnable());
    			jsonObj.put("isUse", rollRecord.getIsUse());
    			CrmCardRollRecordVerify recordVerify = crmCardRollRecordVerifyService.findByRecordId(rollRecordId);
    			if(recordVerify!=null) {
    				jsonObj.put("isVerify", recordVerify.getVerifyStatus());
    				jsonObj.put("activityName", recordVerify.getActivityName());
    			}else {
    				jsonObj.put("isVerify", "0");
    				jsonObj.put("activityName", null);
    			}
    			jsonObj.put("branchOfficeArray", Db.find("select ro.branch_office_id as id,mo.short_name as name from crm_card_roll_branch_office ro left join main_branch_office mo on mo.id=ro.branch_office_id where ro.card_roll_id=?", rollRecord.getCardRollId()));
    			jsonObj.put("baseArray", Db.find("select rb.base_id as id,mb.base_name as name from crm_card_roll_base rb left join main_base mb on mb.id=rb.base_id where rb.card_roll_id=?", rollRecord.getCardRollId()));
    			result.put("code", "0");
        		result.put("msg", "成功");
    			result.put("data", jsonObj);
    		}else {
    			result.put("msg", "卡券不存在");
    		}
    	}
    	renderJson(result);
    }
    
    @Clear(LogInterceptor.class)
    public void cardRollVerify() {
    	Map<String, Object> result = new HashMap<String, Object>();
    	result.put("code", "1");
    	result.put("msg", "失败");
    	result.put("data", null);
    	CrmCardRollRecordVerify model = getBean(CrmCardRollRecordVerify.class, "", true);
    	if(StrKit.isBlank(model.getRollNumber())) {
    		result.put("msg", "rollNumber不能为空");
    	}else {
    		boolean returnFlag = false;
    		CrmCardRollRecord rollRecord = crmCardRollRecordService.findByRollNumber(model.getRollNumber());
    		if(StrKit.isBlank(model.getId())) {
    			if(rollRecord!=null) {
    				if(rollRecord.getIsEnable().equals("0")) {
    					if(rollRecord.getIsUse().equals("0")) {
    						CrmCardRollRecordVerify verify = crmCardRollRecordVerifyService.findByRollNumber(model.getRollNumber());
    	    				if(verify!=null) {
    	    					result.put("msg", "卡号已存在");
    	    				}else {
    	    					model.setRecordId(rollRecord.getId());
    	    					model.setDelFlag(DelFlag.NORMAL);
    	    					model.setCreateTime(new Date());
    	    					returnFlag = model.save();
    	    				}
    					}else {
    						result.put("msg", "卡券已使用");
    					}
    				}else {
    					result.put("msg", "卡券已失效");
    				}
    			}else {
    				result.put("msg", "卡券不存在");
    			}
    		}else {
    			model.setUpdateTime(new Date());
    			returnFlag = model.update();
    		}
    		if(returnFlag) {
    			if(model.getVerifyStatus().equals("1")) {
        			JSONObject obj=new JSONObject();
        			obj.put("cardRollNum",model.getRollNumber());
        			smsSendRecordService.sendMsgByPhoneNumber(SendType.cardRollVerify,model.getPhoneNumber(), JSON.toJSONString(obj),null);
        		}
    			result.put("code", "0");
    			result.put("msg", "成功");
    		}
    	}
    	renderJson(result);
    }
    
    @Clear(LogInterceptor.class)
    public void cardRollPay() {
    	Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "1");
		result.put("msg", "失败");
		result.put("data", null);
		CrmCardRollRecordPayment model = getBean(CrmCardRollRecordPayment.class, "", true);
    	if(StrKit.isBlank(model.getRollNumber())) {
    		result.put("msg", "rollNumber不能为空");
    	}else {
    		boolean returnFlag = false;
    		CrmCardRollRecord rollRecord = crmCardRollRecordService.findByRollNumber(model.getRollNumber());
    		if(StrKit.isBlank(model.getId())) {
    			if(rollRecord!=null) {
    				if(rollRecord.getIsEnable().equals("0")) {
    					if(rollRecord.getIsUse().equals("0")) {
    						CrmCardRollRecordVerify recordVerify = crmCardRollRecordVerifyService.findByRecordId(rollRecord.getId());
    						if(recordVerify!=null) {
    							if(recordVerify.getVerifyStatus().equals("1")) {
    								model.setRecordId(rollRecord.getId());
    								model.setDelFlag(DelFlag.NORMAL);
    								model.setCreateTime(new Date());
    								returnFlag = model.save();
    							}else {
    								result.put("msg", "卡券未核销");
    							}
    						}else {
    							result.put("msg", "卡券未核销");
    						}
    					}else {
    						result.put("msg", "卡券已使用");
        				}
    				}else {
    					result.put("msg", "卡券已失效");
    				}
    			}else {
    				result.put("msg", "卡券不存在");
    			}
    		}else {
    			model.setUpdateTime(new Date());
    			returnFlag = model.update();
    		}
    		if(returnFlag) {
				result.put("code", "0");
				result.put("msg", "成功");
    		}
    	}
		renderJson(result);
    }
    
    @Clear(LogInterceptor.class)
    public void cardRollUse() {
    	Map<String, Object> result = new HashMap<String, Object>();
		result.put("code", "1");
		result.put("msg", "失败");
		result.put("data", null);
		
    	CrmCardRollRecordUse model = getBean(CrmCardRollRecordUse.class, "", true);
    	if(StrKit.isBlank(model.getRecordId())) {
    		result.put("msg", "recordId不能为空");
    	}else {
    		boolean returnFlag = false;
    		CrmCardRollRecord rollRecord = crmCardRollRecordService.findById(model.getRecordId());
    		if(StrKit.isBlank(model.getId())) {
    			if(rollRecord!=null) {
    				if(rollRecord.getIsEnable().equals("0")) {
    					if(rollRecord.getIsUse().equals("0")) {
    						CrmCardRollRecordVerify recordVerify = crmCardRollRecordVerifyService.findByRecordId(rollRecord.getId());
    						if(recordVerify!=null) {
    							if(recordVerify.getVerifyStatus().equals("1")) {
    								model.setRecordId(rollRecord.getId());
    	    						model.setDelFlag(DelFlag.NORMAL);
    	    						model.setCreateTime(new Date());
    	    						returnFlag = model.save();
    							}else {
    								result.put("msg", "卡券未核销");
    							}
    						}else {
    							result.put("msg", "卡券未核销");
    						}
    					}else {
    						result.put("msg", "卡券已使用");
        				}
    				}else {
    					result.put("msg", "卡券已失效");
    				}
    			}else {
    				result.put("msg", "卡券不存在");
    			}
    		}else {
    			model.setUpdateTime(new Date());
    			returnFlag = model.update();
    		}
    		if(returnFlag) {
    			CrmCardRollRecord record = new CrmCardRollRecord();
    			record.setId(rollRecord.getId());
    			record.setIsUse("1");
    			if(record.update()) {
    				result.put("code", "0");
    				result.put("msg", "成功");
    			}
    		}
    	}
		renderJson(result);
    }

	@JCors
	@Clear(LogInterceptor.class)
	public void rollRecordList(){
		String formId=getPara("formId");
		if(StrKit.isBlank(formId)){
			renderCodeFailed("formId参数缺失");
			return;
		}
		final String sql="select t.type_name as 'typeName',t.template_name as 'templateName',cr.roll_name as 'rollName'," +
				"cr.valid_start_date as 'validStartDate',cr.valid_end_date as 'validEndDate',rr.id as 'rollRecordId'," +
				"rr.card_roll_id as 'cardRollId',rr.roll_number as 'rollNumber',rr.roll_value as 'rollValue'," +
				"rr.is_enable as 'isEnable',rr.is_use as 'isUse' from crm_card_roll_record rr " +
				"left join crm_card_roll cr on cr.id=rr.card_roll_id " +
				"left join crm_card_roll_type t on t.id=cr.roll_type_id " +
				"where cr.del_flag='0' and cr.card_coupon_apply_id=? order by cr.roll_code,rr.roll_number ";
		List<Record> recordList = Db.find(sql,formId);
		renderCodeSuccess("success",recordList);
	}

	/**
	 * 卡券申请审核通过回调地址允许跨域
	 */
	@JCors
	@Clear(LogInterceptor.class)
	public void rollApplyCallBack(){
		Long beginTime = System.currentTimeMillis();
		logger.info("接收开始时间==="+beginTime);
		final String jsonStr = getRawData();
		JSONObject returnJson = JSONObject.parseObject(jsonStr);
		final String taskId = returnJson.getString("TaskId");
		final String currentStep = returnJson.getString("CurrentStep");
		final String currentStepAlias = returnJson.getString("CurrentStepAlias");
		final String currentUserName = returnJson.getString("CurrentUserName");
		final String currentStatus = returnJson.getString("CurrentStatus");
		logger.info("taskId==="+taskId);
		logger.info("currentStep==="+currentStep);
		logger.info("currentStepAlias==="+currentStepAlias);
		logger.info("currentUserName==="+currentUserName);
		logger.info("currentStatus==="+currentStatus);
		Global.executorService.execute(new Runnable() {
			@Override
			public void run() {
				if("发起人申请".equals(currentStep)||"Aborted".equals(currentStatus)) {
					crmCardRollService.cardRollVoid(taskId);
				}else if("卡劵制发".equals(currentStep)) {//卡券制发生成卡券数据
					crmCardRollService.bmpInvokeGetForm(taskId);
				}
			}
		});
		logger.info("接收结束时间==="+System.currentTimeMillis());
		Long opetime = System.currentTimeMillis() - beginTime;
		logger.info("耗时==="+opetime);
		renderCodeSuccess("success");
	}
}
