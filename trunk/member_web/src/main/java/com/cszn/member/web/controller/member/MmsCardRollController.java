/**
 * 
 */
package com.cszn.member.web.controller.member;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.crm.*;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.main.MainBranchOfficeService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.api.wms.WmsStockModelService;
import com.cszn.integrated.service.api.wms.WmsStockTypeService;
import com.cszn.integrated.service.entity.crm.*;
import com.cszn.integrated.service.entity.enums.PaymentWay;
import com.cszn.integrated.service.entity.enums.RollType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.wms.WmsStockModels;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by LiangHuiLing on 2021年7月26日
 *
 * RoleController
 */
@RequestMapping(value="/mms/cardRoll", viewPath="/modules_page/mms/cardRoll")
public class MmsCardRollController extends BaseController {

	@Inject
    private CrmCardRollTypeService crmCardRollTypeService;
	@Inject
    private CrmCardRollService crmCardRollService;
	@Inject
	private CrmCardRollBranchOfficeService crmCardRollBranchOfficeService;
	@Inject
	private CrmCardRollBaseService crmCardRollBaseService;
	@Inject
	private CrmCardRollRecordService crmCardRollRecordService;
	@Inject
	private CrmCardRollRecordVerifyService crmCardRollRecordVerifyService;
	@Inject
	private CrmCardRollRecordPaymentService crmCardRollRecordPaymentService;
	@Inject
	private CrmCardRollRecordPrintService crmCardRollRecordPrintService;
	@Inject
	private MainBaseService mainBaseService;
	@Inject
	private MainBranchOfficeService mainBranchOfficeService;
    @Inject
    SmsSendRecordService smsSendRecordService;
	@Inject
	WmsStockTypeService wmsStockTypeService;
	@Inject
	WmsStockModelService wmsStockModelService;
	
    public void index() {
        render("rollIndex.html");
    }
    
    public void verifyIndex() {
    	setAttr("userId", AuthUtils.getUserId());
    	render("verifyIndex.html");
    }
    
    public void payIndex() {
    	setAttr("userId", AuthUtils.getUserId());
    	render("payIndex.html");
    }
    
    public void printRecord() {
    	setAttr("recordId", getPara("id"));
    	render("printRecord.html");
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	CrmCardRoll model = getBean(CrmCardRoll.class, "", true);
        Page<CrmCardRoll> page = crmCardRollService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<CrmCardRoll>(page));
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void recordPageTable() {
    	CrmCardRollRecord model = getBean(CrmCardRollRecord.class, "", true);
    	Page<CrmCardRollRecord> page = crmCardRollRecordService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10), getParaToInt("minValue", null), getParaToInt("maxValue", null));
    	renderJson(new DataTable<CrmCardRollRecord>(page));
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void verifyPageTable() {
    	CrmCardRollRecordVerify model = getBean(CrmCardRollRecordVerify.class, "", true);
    	Page<CrmCardRollRecordVerify> page = crmCardRollRecordVerifyService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10));
    	renderJson(new DataTable<CrmCardRollRecordVerify>(page));
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void payPageTable() {
    	CrmCardRollRecordPayment model = getBean(CrmCardRollRecordPayment.class, "", true);
    	Page<CrmCardRollRecordPayment> page = crmCardRollRecordPaymentService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10));
    	renderJson(new DataTable<CrmCardRollRecordPayment>(page));
    }
    
    /**
     * 分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void printTable() {
    	CrmCardRollRecordPrint model = getBean(CrmCardRollRecordPrint.class, "", true);
    	Page<CrmCardRollRecordPrint> page = crmCardRollRecordPrintService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10));
    	renderJson(new DataTable<CrmCardRollRecordPrint>(page));
    }
    
    public void getAllBranchOffice(){
    	renderJson(Ret.ok("msg", "操作成功!").set("branchOfficeList", mainBranchOfficeService.getUnDelBranchOffice()));
    }
    
    public void getAllBase(){
        renderJson(Ret.ok("msg", "操作成功!").set("baseList", mainBaseService.findBaseList()));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	CrmCardRoll model = getBean(CrmCardRoll.class, "", true);
    	Map<String,String> params=new HashMap<>();
    	params.put("userId",AuthUtils.getUserId());
		params.put("formNo",Global.formNo);
    	final String resultStr = HttpClientsUtils.httpPostForm(Global.codeGenerateUrl,params,null,null);
    	if(StrKit.notBlank(resultStr)) {
    		JSONObject returnJson = JSONObject.parseObject(resultStr);
			final int returnType = returnJson.getIntValue("Type");
			final String returnMsg = returnJson.getString("Msg");
			System.out.println("Type="+returnType);
			System.out.println("Msg="+returnMsg);
			if(returnType==1) {
				final String formNo = returnJson.getString("Data");
				model.setRollCode(formNo);
			}
    	}
//    	model.setRollCode(DateUtils.getDate("yyyyMMddHHmmssSSS"));
    	model.setValidStartDate(DateUtils.parseDate(DateUtils.getDate("yyyy-MM")+"-01"));
    	model.setValidEndDate(DateUtils.parseDate(DateUtils.getDate("yyyy-MM")+"-"+DateUtils.getMaxMonthDay()));
    	setAttr("model", model);
    	setAttr("typeList", crmCardRollTypeService.findList());
    	setAttr("fitBranchOffices", null);
    	setAttr("fitBases", null);
        render("rollForm.html");
    }
    
    /**
     * 添加页面方法
     */
    public void verifyAdd() {
    	CrmCardRollRecordVerify model = getBean(CrmCardRollRecordVerify.class, "", true);
    	model.setVerifyTime(new Date());
    	setAttr("model", model);
    	setAttr("baseList", mainBaseService.findBaseList());
    	setAttr("boList", mainBranchOfficeService.getUnDelBranchOffice());
    	render("verifyAdd.html");
    }
    
    /**
     * 添加页面方法
     */
    public void payAdd() {
    	CrmCardRollRecordPayment model = getBean(CrmCardRollRecordPayment.class, "", true);
    	model.setCollectTime(new Date());
    	Map<String,String> payWay=new HashMap<>();
        for(PaymentWay paymentWay:PaymentWay.values()){
        	payWay.put(paymentWay.getKey(),paymentWay.getValue());
        }
        setAttr("payWay", payWay);
    	setAttr("model", model);
    	render("payForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
		String formType = getPara("formType");
		setAttr("model", crmCardRollService.findById(getPara("id")));
        setAttr("typeList", crmCardRollTypeService.findList());
        setAttr("fitBranchOffices", Db.queryStr("select group_concat(branch_office_id)branchOfficeIds from crm_card_roll_branch_office where card_roll_id=? group by card_roll_id", getPara("id")));
    	setAttr("fitBases", Db.queryStr("select group_concat(base_id)baseIds from crm_card_roll_base where card_roll_id=? group by card_roll_id", getPara("id")));
		List<Record> recordList = Db.find("select a.id,b.id as modelId,b.`name` as modelName,b.standard,a.num from crm_card_roll_model_rel a " +
				"inner join wms_stock_models b on a.model_id=b.id where a.roll_id=? ", getPara("id"));
		setAttr("formType",formType);
		setAttr("recordList",recordList);

    	render("rollForm.html");
    }

    /**
     * 打印方法
     */
    public void print() {
    	CrmCardRoll model = crmCardRollService.findById(getPara("id"));
    	CrmCardRollType rollType = crmCardRollTypeService.findById(model.getRollTypeId());
    	List<CrmCardRollRecord> recordList =crmCardRollRecordService.findListByRollId(getPara("id"));
    	String type=getPara("type");

    	String validType=model.getValidType();


    	StringBuilder sb = new StringBuilder();
    	for(CrmCardRollRecord record : recordList) {
    		sb.append(model.getRollName()).append("\t");
    		sb.append(record.getId()).append("\t");
    		if("2".equals(validType)){
				sb.append("绑定开始"+model.getValidDays()+"天有效"+",最大有效期:"+DateUtils.formatDate(model.getFinalValidDate(),"yyyy-MM-dd")).append("\t");
			}else{
				sb.append(DateUtils.formatDate(model.getValidStartDate(), "yyyy-MM-dd")+"   至   "+DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
			}

    		//sb.append(DateUtils.formatDate(model.getValidStartDate(), "yyyy-MM-dd")).append("\t");
    		//sb.append(DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
			//sb.append("").append("\t");
			sb.append(record.getRollNumber()).append("\t");
			String rollValue="";
			if(record.getRollValue()!=null){
				if(record.getRollValue()>0){
					rollValue=Double.toString(record.getRollValue());
					
					BigDecimal bd = new BigDecimal(String.valueOf(record.getRollValue()));
					String[] ss = bd.toString().split("\\.");
					if (ss.length <= 1){
						
					}else{
						int a=Integer.valueOf(ss[1]);
						if(a>0){
							rollValue=Double.toString(record.getRollValue());
						}else{
							rollValue=Integer.toString(record.getRollValue().intValue());
						}
					}
				}
			}
			if("0".equals(rollValue)){
				rollValue="";
			}

			sb.append(rollValue).append("\t");
			if("1".equals(model.getIsAstrictBind())){
				sb.append("在此日期前充值:"+DateUtils.formatDate(model.getMaxBindTime(),"yyyy-MM-dd")).append("\t\r\n");
			}else{
				sb.append("").append("\t\r\n");
			}
    		if(StrKit.notBlank(rollType.getTemplateName())) {
    			CrmCardRollRecordPrint print = new CrmCardRollRecordPrint();
    			print.setId(IdGen.getUUID());
    			print.setRecordId(record.getId());
    			print.setRollNumber(record.getRollNumber());
    			print.setDelFlag(DelFlag.NORMAL);
    			print.setCreateBy(AuthUtils.getUserId());
    			print.setCreateTime(new Date());
    			print.save();
    		}
    	}
    	renderJson(Ret.ok("msg", "返回成功!").set("templateName", rollType.getTemplateName()).set("printData", sb.toString()));
    }

	public void startPrint() {
		CrmCardRoll model = crmCardRollService.findById(getPara("id"));
		CrmCardRollType rollType = crmCardRollTypeService.findById(model.getRollTypeId());

		Integer endNum=getParaToInt("endNum");

		CrmCardRollRecord rollRecord=new CrmCardRollRecord();
		rollRecord.setCardRollId(model.getId());
		rollRecord.setRollNumber(getPara("rollNumber"));
		List<CrmCardRollRecord> recordList=crmCardRollRecordService.findListByRollId(rollRecord,endNum);

		String validType=model.getValidType();

		StringBuilder sb = new StringBuilder();
		for(CrmCardRollRecord record : recordList) {
			sb.append(model.getRollName()).append("\t");
			sb.append(record.getId()).append("\t");
			if("2".equals(validType)){
				sb.append("绑定开始"+model.getValidDays()+"天有效"+",最大有效期:"+DateUtils.formatDate(model.getFinalValidDate(),"yyyy-MM-dd")).append("\t");
			}else{
				sb.append(DateUtils.formatDate(model.getValidStartDate(), "yyyy-MM-dd")+"   至   "+DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
			}
			//sb.append(DateUtils.formatDate(model.getValidStartDate(), "yyyy-MM-dd")+"   至   "+DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
			//sb.append(DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
			//sb.append("").append("\t");
			sb.append(record.getRollNumber()).append("\t");
			String rollValue="";
			if(record.getRollValue()!=null){
				rollValue=Double.toString(record.getRollValue());

				BigDecimal bd = new BigDecimal(String.valueOf(record.getRollValue()));
				String[] ss = bd.toString().split("\\.");
				if (ss.length <= 1){

				}else{
					int a=Integer.valueOf(ss[1]);
					if(a>0){
						rollValue=Double.toString(record.getRollValue());
					}else{
						rollValue=Integer.toString(record.getRollValue().intValue());
					}
				}
			}
			if("0".equals(rollValue)){
				rollValue="";
			}

			sb.append(rollValue).append("\t");

			if("1".equals(model.getIsAstrictBind())){
				sb.append("在此日期前充值:"+DateUtils.formatDate(model.getMaxBindTime(),"yyyy-MM-dd")).append("\t\r\n");
			}else{
				sb.append("").append("\t\r\n");
			}
			if(StrKit.notBlank(rollType.getTemplateName())) {
				CrmCardRollRecordPrint print = new CrmCardRollRecordPrint();
				print.setId(IdGen.getUUID());
				print.setRecordId(record.getId());
				print.setRollNumber(record.getRollNumber());
				print.setDelFlag(DelFlag.NORMAL);
				print.setCreateBy(AuthUtils.getUserId());
				print.setCreateTime(new Date());
				print.save();
			}
		}
		System.out.println(sb.toString());
		renderJson(Ret.ok("msg", "返回成功!").set("templateName", rollType.getTemplateName()).set("printData", sb.toString()));
	}

	public void printRollRecord() {
		CrmCardRollRecord record=crmCardRollRecordService.findById(getPara("id"));
		CrmCardRoll model = crmCardRollService.findById(record.getCardRollId());
		CrmCardRollType rollType = crmCardRollTypeService.findById(model.getRollTypeId());
		StringBuilder sb = new StringBuilder();
		sb.append(model.getRollName()).append("\t");
		sb.append(record.getId()).append("\t");
		if("2".equals(model.getValidType())){
			sb.append("绑定开始"+model.getValidDays()+"天有效"+",最大有效期:"+DateUtils.formatDate(model.getFinalValidDate(),"yyyy-MM-dd")).append("\t");
		}else{
			sb.append(DateUtils.formatDate(model.getValidStartDate(), "yyyy-MM-dd")+"   至   "+DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
		}
		//sb.append(DateUtils.formatDate(model.getValidStartDate(), "yyyy-MM-dd")).append("\t");
		//sb.append(DateUtils.formatDate(model.getValidEndDate(), "yyyy-MM-dd")).append("\t");
		sb.append(record.getRollNumber()).append("\t");
		String rollValue="";
		if(record.getRollValue()!=null){
			if(record.getRollValue()>0){
				rollValue=Double.toString(record.getRollValue());

				BigDecimal bd = new BigDecimal(String.valueOf(record.getRollValue()));
				String[] ss = bd.toString().split("\\.");
				if (ss.length <= 1){

				}else{
					int a=Integer.valueOf(ss[1]);
					if(a>0){
						rollValue=Double.toString(record.getRollValue());
					}else{
						rollValue=Integer.toString(record.getRollValue().intValue());
					}
				}
			}
		}


		sb.append(rollValue).append("\t");
		if("1".equals(model.getIsAstrictBind())){
			sb.append("在此日期前充值:"+DateUtils.formatDate(model.getMaxBindTime(),"yyyy-MM-dd")).append("\t\r\n");
		}else{
			sb.append("").append("\t\r\n");
		}
		if(StrKit.notBlank(rollType.getTemplateName())) {
			CrmCardRollRecordPrint print = new CrmCardRollRecordPrint();
			print.setId(IdGen.getUUID());
			print.setRecordId(record.getId());
			print.setRollNumber(record.getRollNumber());
			print.setDelFlag(DelFlag.NORMAL);
			print.setCreateBy(AuthUtils.getUserId());
			print.setCreateTime(new Date());
			print.save();
		}
		renderJson(Ret.ok("msg", "返回成功!").set("templateName", rollType.getTemplateName()).set("printData", sb.toString()));
	}

    
    /**
     * 修改页面方法
     */
    public void recordEdit() {
    	setAttr("model", crmCardRollRecordService.findById(getPara("id")));
    	render("recordForm.html");
    }
    
    /**
     * 修改页面方法
     */
    public void verifyEdit() {
    	setAttr("model", crmCardRollRecordVerifyService.findById(getPara("id")));
    	setAttr("baseList", mainBaseService.findBaseList());
    	setAttr("boList", mainBranchOfficeService.getUnDelBranchOffice());
    	render("verifyEdit.html");
    }
    
    /**
     * 修改页面方法
     */
    public void payEdit() {
    	Map<String,String> payWay=new HashMap<>();
        for(PaymentWay paymentWay:PaymentWay.values()){
        	payWay.put(paymentWay.getKey(),paymentWay.getValue());
        }
        setAttr("payWay", payWay);
    	setAttr("model", crmCardRollRecordPaymentService.findById(getPara("id")));
    	render("payForm.html");
    }
    

    /**
     * 保存方法
     */
    public void save() {
    	CrmCardRoll model = getBean(CrmCardRoll.class, "", true);
    	final String fitBranchOffice = getPara("fitBranchOffice");
    	final String fitBase = getPara("fitBase");
    	boolean returnFlag = false;
    	String returnMsg = "操作失败！";
    	if(model.getDeployStatus().equals("0")) {//发布状态
			model.setDeployTime(new Date());
		}
		String typeCode = getPara("typeCode");
		List<CrmCardRollModelRel> addRel=new ArrayList<>();
		List<CrmCardRollModelRel> updateRel=new ArrayList<>();
		if(RollType.goodsType.getKey().equals(typeCode)){
			Integer roomCount = getParaToInt("roomCount");
			for (Integer i = 1; i <= roomCount; i++) {
				CrmCardRollModelRel rollModelRel=new CrmCardRollModelRel();
				String id = getPara("model" + i + ".id");
				String modelId = getPara("model" + i + ".modelId");
				Integer num = getParaToInt("model" + i + ".num");
				if(StrKit.isBlank(modelId)){
					continue;
				}
				if(StrKit.isBlank(id)){
					rollModelRel.setId(IdGen.getUUID());
					rollModelRel.setModelId(modelId);
					rollModelRel.setNum(num);
					rollModelRel.setDelFlag("0");
					rollModelRel.setCreateBy(AuthUtils.getUserId());
					rollModelRel.setCreateDate(new Date());
					rollModelRel.setUpdateBy(AuthUtils.getUserId());
					rollModelRel.setUpdateDate(new Date());
					addRel.add(rollModelRel);
				}else{
					rollModelRel.setId(id);
					rollModelRel.setModelId(modelId);
					rollModelRel.setNum(num);
					rollModelRel.setUpdateBy(AuthUtils.getUserId());
					rollModelRel.setUpdateDate(new Date());
					updateRel.add(rollModelRel);
				}
			}

			if(addRel.size()==0 && updateRel.size()==0){
				renderJson(Ret.fail("msg", RollType.goodsType.getValue()+"请添加一条可兑换的物品信息"));
				return;
			}
		}


    	if(StrKit.isBlank(model.getId())) {
    		final String resultStr = HttpClientsUtils.get(Global.releaseCodeUrl +"?userId="+AuthUtils.getUserId()+"&formSubmitNo="+model.getRollCode()+"&formNo="+Global.formNo);
        	if(StrKit.notBlank(resultStr)) {
        		JSONObject returnJson = JSONObject.parseObject(resultStr);
    			final int rType = returnJson.getIntValue("Type");
    			final String rMsg = returnJson.getString("Msg");
    			System.out.println("rType="+rType);
    			System.out.println("rMsg="+rMsg);
    			if(rType==1) {
    				model.setId(IdGen.getUUID());
    				model.setDelFlag(DelFlag.NORMAL);
    				model.setCreateBy(AuthUtils.getUserId());
    				model.setCreateTime(new Date());
    				returnFlag = model.save();
    				returnMsg = "操作成功!";
    			}else {
    				
    			}
        	}
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		returnFlag = model.update();
    		returnMsg = "操作成功!";
    		if(returnFlag) {
    			//先删除原来的数据
        		Db.update("delete from crm_card_roll_branch_office where card_roll_id=?", model.getId());
        		Db.update("delete from crm_card_roll_base where card_roll_id=?", model.getId());
    		}
    	}
    	if(returnFlag){
			for (CrmCardRollModelRel rollModelRel : addRel) {
				rollModelRel.setRollId(model.getId());
			}
			for (CrmCardRollModelRel rollModelRel : updateRel) {
				rollModelRel.setRollId(model.getId());
			}
			if(addRel.size()>0){
				Db.batchSave(addRel,addRel.size());
			}
			if(updateRel.size()>0){
				Db.batchUpdate(updateRel,updateRel.size());
			}
		}
    	if(returnFlag){
    		if(StrKit.notBlank(fitBranchOffice)) {
    			final String[] fitBranchOfficeArray = fitBranchOffice.split(",");
    			for(String branchOfficeId : fitBranchOfficeArray) {
    				CrmCardRollBranchOffice crmCardRollBranchOffice = new CrmCardRollBranchOffice();
    				crmCardRollBranchOffice.setId(IdGen.getUUID());
    				crmCardRollBranchOffice.setCardRollId(model.getId());
    				crmCardRollBranchOffice.setBranchOfficeId(branchOfficeId);
    				crmCardRollBranchOffice.save();
    			}
    		}
    		if(StrKit.notBlank(fitBase)) {
    			final String[] fitBaseArray = fitBase.split(",");
    			for(String baseId : fitBaseArray) {
    				CrmCardRollBase crmCardRollBase = new CrmCardRollBase();
    				crmCardRollBase.setId(IdGen.getUUID());
    				crmCardRollBase.setCardRollId(model.getId());
    				crmCardRollBase.setBaseId(baseId);
    				crmCardRollBase.save();
    			}
    		}
    		if(model.getDeployStatus().equals("0")) {//发布状态
    			if(model.getRollQuantity()!=null && model.getRollQuantity()>0) {
    				int digit = 5;
    				if(model.getRollQuantity()>=10000 && model.getRollQuantity()<100000) {
    					digit = 6;
    				}else if(model.getRollQuantity()>=100000 && model.getRollQuantity()<1000000) {
    					digit = 7;
    				}else if(model.getRollQuantity()>=1000000 && model.getRollQuantity()<10000000) {
    					digit = 8;
    				}
					CrmCardRoll roll=crmCardRollService.findById(model.getId());
					String rollCodeSuffix="";
					if(StrKit.notBlank(roll.getRollCodeSuffix())){
						rollCodeSuffix=roll.getRollCodeSuffix();
					}
    				for (int i = 1; i <= model.getRollQuantity(); i++) {

    					final String rollNumber = String.format("%"+digit+"d", i).replace(" ", "0");
    					CrmCardRollRecord crmCardRollRecord = new CrmCardRollRecord();
    					crmCardRollRecord.setId(IdGen.getUUID());
    					crmCardRollRecord.setCardRollId(model.getId());
						crmCardRollRecord.setRollValue(roll.getRollValue());
						crmCardRollRecord.setBalanceRollValue(roll.getRollValue());
    					crmCardRollRecord.setRollNumber(model.getRollCode()+rollNumber+rollCodeSuffix);
    					crmCardRollRecord.setIsEnable("0");
    					crmCardRollRecord.setDelFlag(DelFlag.NORMAL);
    					crmCardRollRecord.setCreateBy(AuthUtils.getUserId());
    					crmCardRollRecord.setCreateTime(new Date());
    					returnFlag = crmCardRollRecord.save();
    					if(!returnFlag) {
    						break;
    					}
					}
    			}
    		}
    		if(returnFlag) {
    			renderJson(Ret.ok("msg", returnMsg));
    		}else {
    			renderJson(Ret.fail("msg", "生成记录失败！"));
    		}
		}else{
			renderJson(Ret.fail("msg", returnMsg));
		}
    }
    
    /**
     * 保存方法
     */
    public void recordSave() {
    	CrmCardRollRecord model = getBean(CrmCardRollRecord.class, "", true);
    	boolean returnFlag = false;
    	String returnMsg = "操作失败！";
		model.setUpdateBy(AuthUtils.getUserId());
		model.setUpdateTime(new Date());
		returnFlag = model.update();
    	if(returnFlag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", returnMsg));
    	}
    }
    
    /**
     * 保存方法
     */
    public void verifySave() {
    	CrmCardRollRecordVerify model = getBean(CrmCardRollRecordVerify.class, "", true);

		boolean returnFlag = false;
    	String returnMsg = "操作失败！";
    	StringBuilder sb = new StringBuilder();
    	if(StrKit.isBlank(model.getId())) {
    		final int detailCount = getParaToInt("rollNumberCount");
    		if(detailCount>0){
    			for (int i = 1; i <= detailCount; i++) {
    				final CrmCardRollRecordVerify detail = getBean(CrmCardRollRecordVerify.class, "rollNumberList["+i+"]");
    				if(StrKit.notBlank(detail.getRollNumber())) {
    					model.setRollNumber(detail.getRollNumber());
    					CrmCardRollRecord rollRecord = crmCardRollRecordService.findByRollNumber(model.getRollNumber());
    					if(rollRecord!=null) {
    						if(rollRecord.getIsEnable().equals("0")) {
    							CrmCardRollRecordVerify verify = crmCardRollRecordVerifyService.findByRollNumber(model.getRollNumber());
    							if(verify!=null) {
//    								returnMsg = "操作失败，卡号已存在！";
    								sb.append(model.getRollNumber()+"已存在！");
    							}else {
    								model.setId(IdGen.getUUID());
    								model.setRecordId(rollRecord.getId());
    								model.setDelFlag(DelFlag.NORMAL);
    								model.setCreateBy(AuthUtils.getUserId());
    								model.setCreateTime(new Date());
    								returnFlag = model.save();
    							}
    						}else {
//    							returnMsg = "操作失败，卡券已失效！";
    							sb.append(model.getRollNumber()+"已失效！");
    						}
    					}else {
//    						returnMsg = "操作失败，卡券不存在！";
    						sb.append(model.getRollNumber()+"不存在！");
    					}
    		    	}
    			}
    		}
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		returnFlag = model.update();
    	}
    	if(returnFlag){
//    		if(model.getVerifyStatus().equals("1")) {
//    			JSONObject obj=new JSONObject();
//    			obj.put("cardRollNum",model.getRollNumber());
//    			smsSendRecordService.sendMsgByPhoneNumber(SendType.cardRollVerify,model.getPhoneNumber(), JSON.toJSONString(obj),null);
//    		}
    		returnMsg = "操作成功！";
    		if(sb.length()>0) {
    			returnMsg=returnMsg+sb.toString();
    		}
			renderJson(Ret.ok("msg", returnMsg));
		}else{
			if(sb.length()>0) {
    			returnMsg=returnMsg+sb.toString();
    		}
			renderJson(Ret.fail("msg", returnMsg));
		}
    }
    
    /**
     * 保存方法
     */
    public void paySave() {
    	CrmCardRollRecordPayment model = getBean(CrmCardRollRecordPayment.class, "", true);
    	boolean returnFlag = false;
    	String returnMsg = "操作失败！";
    	if(StrKit.isBlank(model.getId())) {
    		CrmCardRollRecord rollRecord = crmCardRollRecordService.findByRollNumber(model.getRollNumber());
    		if(rollRecord!=null) {
    			if(rollRecord.getIsEnable().equals("0")) {
    				model.setId(IdGen.getUUID());
    				model.setRecordId(rollRecord.getId());
    				model.setDelFlag(DelFlag.NORMAL);
    				model.setCreateBy(AuthUtils.getUserId());
    				model.setCreateTime(new Date());
    				returnFlag = model.save();
    			}else {
    				returnMsg = "操作失败，卡券已失效！";
    			}
    		}else {
    			returnMsg = "操作失败，卡券不存在！";
    		}
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		returnFlag = model.update();
    	}
    	if(returnFlag){
    		renderJson(Ret.ok("msg", "操作成功!"));
    	}else{
    		renderJson(Ret.fail("msg", returnMsg));
    	}
    }
    
	/**
	 * 删除方法
	 */
	public void delete(){
		CrmCardRoll model = getBean(CrmCardRoll.class, "", true);
		if(crmCardRollService.update(model)){
			//删除子表数据
    		Db.update("update crm_card_roll_record set del_flag='1' where card_roll_id=?", model.getId());
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}
	
	/**
	 * 删除方法
	 */
	public void recordDelete(){
		CrmCardRollRecord model = getBean(CrmCardRollRecord.class, "", true);
		if(crmCardRollRecordService.update(model)){
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}
	
	/**
	 * 删除方法
	 */
	public void verifyDelete(){
		CrmCardRollRecordVerify model = getBean(CrmCardRollRecordVerify.class, "", true);
		if(crmCardRollRecordVerifyService.update(model)){
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}
	
	/**
	 * 删除方法
	 */
	public void payDelete(){
		CrmCardRollRecordPayment model = getBean(CrmCardRollRecordPayment.class, "", true);
		if(crmCardRollRecordPaymentService.update(model)){
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}

	public void rollProductform(){
		String typeId=getPara("typeId");

		List<Record> recordList = Db.find("select a.id,b.id as modelId,b.`name` as modelName,b.standard,a.num from crm_card_roll_model_rel a " +
				"inner join wms_stock_models b on a.model_id=b.id where a.roll_id=? ", typeId);

		setAttr("typeId",typeId);
		setAttr("recordList",recordList);
		render("rollProductform.html");
	}

	public void stockModelIndex(){
		setAttr("trIndex",getPara("trIndex"));
		render("stockModelIndex.html");
	}

	public void stockTypeTree(){
		List<ZTree> zTrees=wmsStockTypeService.findStockTypeTree("1");
		renderJson(zTrees);
	}

	public void findStockModelPageList(){
		String startDate = getPara("startDate");
		String endDate = getPara("endDate");
		WmsStockModels model=getBean(WmsStockModels.class,"",true);

		Page<WmsStockModels> page=wmsStockModelService.findStockModelPageList(getParaToInt("page"),getParaToInt("limit"),model,startDate,endDate);
		renderJson(new DataTable<WmsStockModels>(page));
	}

	public void saveRollTypeProduct(){
		Integer roomCount = getParaToInt("roomCount");
		String typeId = getPara("typeId");
		List<CrmCardRollModelRel> addRel=new ArrayList<>();
		List<CrmCardRollModelRel> updateRel=new ArrayList<>();
		for (Integer i = 1; i <= roomCount; i++) {
			CrmCardRollModelRel rollModelRel=new CrmCardRollModelRel();
			String id = getPara("model" + i + ".id");
			String modelId = getPara("model" + i + ".modelId");
			Integer num = getParaToInt("model" + i + ".num");
			if(StrKit.isBlank(modelId)){
				continue;
			}
			if(StrKit.isBlank(id)){
				rollModelRel.setId(IdGen.getUUID());
				rollModelRel.setModelId(modelId);
				rollModelRel.setNum(num);
				rollModelRel.setRollId(typeId);
				rollModelRel.setDelFlag("0");
				rollModelRel.setCreateBy(AuthUtils.getUserId());
				rollModelRel.setCreateDate(new Date());
				rollModelRel.setUpdateBy(AuthUtils.getUserId());
				rollModelRel.setUpdateDate(new Date());
				addRel.add(rollModelRel);
			}else{
				rollModelRel.setId(id);
				rollModelRel.setModelId(modelId);
				rollModelRel.setRollId(typeId);
				rollModelRel.setNum(num);
				rollModelRel.setUpdateBy(AuthUtils.getUserId());
				rollModelRel.setUpdateDate(new Date());
				updateRel.add(rollModelRel);
			}
		}
		if(addRel.size()>0){
			Db.batchSave(addRel,addRel.size());
		}
		if(updateRel.size()>0){
			Db.batchUpdate(updateRel,updateRel.size());
		}
		renderJson(Ret.ok("msg", "操作成功"));
	}

	public void delRel(){
		String id = getPara("id");

		if(Db.update("update crm_card_roll_model_rel set del_flag='1',update_date=now(),update_by=? where id=? ",AuthUtils.getUserId(),id)>0){
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}

	}

	public void addRollRecord(){
		String id=getPara("rollId");
		Integer num = getParaToInt("num");
		CrmCardRoll crmCardRoll=crmCardRollService.findById(id);
		if("1".equals(crmCardRoll.getIsEnable())){
			renderJson(Ret.fail("msg", "操作失败该卡券已失效"));
			return;
		}
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("addBy",AuthUtils.getUserId());
		jsonObject.put("addDate",DateUtils.formatDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
		jsonObject.put("addNum",num);

		JSONArray addRecords=null;
		if(StrKit.notBlank(crmCardRoll.getAddRecords())){
			addRecords=JSONArray.parseArray(crmCardRoll.getAddRecords());
		}else{
			addRecords=new JSONArray();
		}
		addRecords.add(jsonObject);
		crmCardRoll.setAddRecords(JSON.toJSONString(addRecords));
		crmCardRoll.setRollQuantity(crmCardRoll.getRollQuantity()+num);
		crmCardRoll.setUpdateBy(AuthUtils.getUserId());
		crmCardRoll.setUpdateTime(new Date());

		//获取最后一张的券
		String maxRollNumber=Db.queryStr("select roll_number from crm_card_roll_record where card_roll_id=? and del_flag='0' " +
				"order by roll_number desc limit 1 ",crmCardRoll.getId());
		String rollCodeSuffix="";
		if(StrKit.notBlank(crmCardRoll.getRollCodeSuffix())){
			rollCodeSuffix=crmCardRoll.getRollCodeSuffix();
		}
		Long maxNumber=Long.valueOf(maxRollNumber.replace(crmCardRoll.getRollCode(),"").replace(rollCodeSuffix,""));
		List<CrmCardRollRecord> addList=new ArrayList<>();
		int length=maxRollNumber.length()-crmCardRoll.getRollCode().length()-rollCodeSuffix.length();
		//FN0000
		//**********
		//4


		for (Integer i = 0; i < num; i++) {
			maxNumber+=1;
			CrmCardRollRecord crmCardRollRecord = new CrmCardRollRecord();
			crmCardRollRecord.setId(IdGen.getUUID());
			crmCardRollRecord.setCardRollId(crmCardRoll.getId());
			crmCardRollRecord.setRollValue(crmCardRoll.getRollValue());
			crmCardRollRecord.setBalanceRollValue(crmCardRoll.getRollValue());
			int addStrNum=length-String.valueOf(maxNumber).length();
			String addStr="";
			if(addStrNum>0){
				for (int j = 0; j < addStrNum; j++) {
					addStr+="0";
				}
			}
			crmCardRollRecord.setRollNumber(crmCardRoll.getRollCode()+addStr+maxNumber+rollCodeSuffix);
			crmCardRollRecord.setIsEnable("0");
			crmCardRollRecord.setDelFlag(DelFlag.NORMAL);
			crmCardRollRecord.setCreateBy(AuthUtils.getUserId());
			crmCardRollRecord.setCreateTime(new Date());
			addList.add(crmCardRollRecord);
		}
		if(crmCardRoll.update()){
			Db.batchSave(addList,addList.size());
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}
}
