package com.cszn.member.web.controller.equipment;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsEquipmentTypeService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.member.MmsEquipmentType;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Description 上传数据设备信息管理
 * <AUTHOR>
 * @Date 2019/6/26
 **/
@RequestMapping(value="/mms/equipmentType", viewPath="/modules_page/mms/equipmentType")
public class EquipmentTypeController extends BaseController {

    @Inject
    private MmsEquipmentTypeService mmsEquipmentTypeService;

    @Inject
    private DictService dictService;


    /**
     * 跳转上传数据设备信息界面
     */
    public void index(){
        render("equipmentTypeIndex.html");
    }


    /**
     * 上传数据设备信息列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MmsEquipmentType equipmentType = getBean(MmsEquipmentType.class,"",true);
        Page<MmsEquipmentType> page = mmsEquipmentTypeService.findListPage(getParaToInt("page"), getParaToInt("limit"),equipmentType);
        renderJson(new DataTable<MmsEquipmentType>(page));
    }


    /**
     * 跳转新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MmsEquipmentType equipmentType = mmsEquipmentTypeService.get(id);
        List<Dict> typeList = dictService.getListByTypeOnUse("equipment_type");
        setAttr("typeList",typeList);
        setAttr("et",equipmentType);
        render("equipmentTypeForm.html");
    }


    /**
     * 保存
     */
    public void save(){
        MmsEquipmentType equipmentType = getBean(MmsEquipmentType.class,"et",true);
        if(equipmentType == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        if(StringUtils.isNotBlank(equipmentType.getId())){
            MmsEquipmentType typeExist = mmsEquipmentTypeService.get(equipmentType.getId());
            if(typeExist == null){ renderJson(Ret.fail("msg", "保存失败")); return; }
        }
        String flag = mmsEquipmentTypeService.saveEquipmentType(equipmentType,AuthUtils.getUserId());
        if("suc".equals(flag)){
            renderJson(Ret.ok("msg", "保存成功"));
        }else if("".equals(flag)){
            renderJson(Ret.fail("msg", "设备型号不可重复"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 删除
     */
    public void delete(){
        String id = getPara("id");
        MmsEquipmentType equipmentType = mmsEquipmentTypeService.get(id);
        if(equipmentType != null){
            boolean flag = mmsEquipmentTypeService.delEquipmentType(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }

    }
}
