package com.cszn.member.web.controller.member;

import org.apache.commons.lang3.StringUtils;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsBlackListService;
import com.cszn.integrated.service.api.member.MmsBlackRecordService;
import com.cszn.integrated.service.entity.member.MmsBlackList;
import com.cszn.integrated.service.entity.member.MmsBlackRecord;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;


@RequestMapping(value="/mms/blackList", viewPath="/modules_page/mms/blackList")
public class MmsBlackListController extends BaseController {
	
	@Inject
    private MmsBlackListService mmsBlackListService;
	@Inject
	private MmsBlackRecordService mmsBlackRecordService;

	/**
	 * 黑名单首页界面
	 */
	public void index(){
		render("blackListIndex.html");
	}
	
	/**
	 * 黑名单操作记录首页界面
	 */
	public void recordIndex(){
		String id = getPara("id");
		setAttr("blackListId", id);
		render("blackRecordIndex.html");
	}

	/**
	 * 黑名单分页列表
	 */
	@Clear(LogInterceptor.class)
	public void pageTable(){
		MmsBlackList blackList = getBean(MmsBlackList.class,"",true);
		Page<MmsBlackList> page = mmsBlackListService.pageTable(getParaToInt("page"), getParaToInt("limit"), blackList);
		renderJson(new DataTable<MmsBlackList>(page));
	}
	
	/**
	 * 黑名单操作记录分页列表
	 */
	@Clear(LogInterceptor.class)
	public void recordTable(){
		MmsBlackRecord blackRecord = getBean(MmsBlackRecord.class,"",true);
		Page<MmsBlackRecord> page = mmsBlackRecordService.pageTable(getParaToInt("page"), getParaToInt("limit"), blackRecord);
		renderJson(new DataTable<MmsBlackRecord>(page));
	}

	/**
	 * 跳转黑名单 新增/修改界面
	 */
	public void form(){
		String id = getPara("id");
		MmsBlackList blackList = mmsBlackListService.findById(id);
		setAttr("blackList", blackList);
		render("blackListForm.html");
	}

	/**
	 * 黑名单保存
	 */
	public void save(){
		MmsBlackList blackList = getBean(MmsBlackList.class,"",true);
		if(StringUtils.isBlank(blackList.getCreateBy())) {
			blackList.setCreateBy(AuthUtils.getUserId());
		}
		if(StringUtils.isBlank(blackList.getUpdateBy())) {
			blackList.setUpdateBy(AuthUtils.getUserId());
		}
		if(mmsBlackListService.saveBlackList(blackList)){
			renderJson(Ret.ok("msg", "保存成功"));
		}else{
			renderJson(Ret.fail("msg", "保存失败"));
		}
	}

	/**
	 * 黑名单删除
	 */
//	public void delete(){
//		String id = getPara("id");
//		boolean flag = mmsMemberService.delMmsMember(id, AuthUtils.getUserId());
//		if(flag){
//			renderJson(Ret.ok("msg", "作废成功"));
//		}else{
//			renderJson(Ret.fail("msg", "作废失败"));
//		}
//	}

	/**
	 * 批量删除
	 */
//	public void batchDel(){
//		String memberData =  getPara("memberData");
//		List<MmsMember> list = JSONArray.parseArray(memberData,MmsMember.class);
//		boolean flag = mmsMemberService.batchDelMember(list,AuthUtils.getUserId());
//		if(flag){
//			renderJson(Ret.ok("msg", "批量作废成功"));
//		}else{
//			renderJson(Ret.fail("msg", "批量作废失败"));
//		}
//	}
}
