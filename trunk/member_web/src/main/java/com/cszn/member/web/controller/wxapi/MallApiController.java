package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.mall.MallProductCategoryService;
import com.cszn.integrated.service.api.mall.MallProductService;
import com.cszn.integrated.service.api.mall.MallShopingCarService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.entity.mall.MallProductCategory;
import com.cszn.integrated.service.entity.mall.MallShopingCar;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping(value="/oldMallApi", viewPath="")
public class MallApiController extends BaseController {

    @Inject
    private MmsMemberService mmsMemberService;
    @Inject
    private MallProductCategoryService mallProductCategoryService;
    @Inject
    private MallProductService mallProductService;
    @Inject
    private MallShopingCarService mallShopingCarService;

    /**
     * 通过userId获取所有的积分会员卡
     */
    public void getCardByUserId(){
        String userId=getPara("userId");


        Map<String,Object> result=new HashMap<>();
        if(StrKit.isBlank(userId)){
            result.put("code","10001");
            result.put("msg","用户id不能为空");
            return;
        }
        List<Record> cardList=null;
        try {
            cardList=mmsMemberService.getIntegralCardByUserId(userId);
        }catch (Exception e){
            e.printStackTrace();
            result.put("code","10001");
            result.put("msg","获取会员卡异常");
            renderJson(result);
            return;
        }
        result.put("code","0");
        result.put("msg","success");
        result.put("data",cardList);
        renderJson(result);
    }


    /**
     * 获取所有的类别
     */
    public void getAllCategory(){

        Map<String,Object> result=new HashMap<>();
        List<MallProductCategory> categoryList=mallProductCategoryService.findAllCategory();
        result.put("code","0");
        result.put("msg","success");
        result.put("data",categoryList);
        renderJson(result);
    }


    /**
     * 通过商品类型获取商品分页
     */
    public void getProductByCategoryId(){
        Integer pageNumber=getParaToInt("pageNumber");
        Integer pageSize=getParaToInt("pageSize");
        String categoryId=getPara("categoryId");
        Map<String,Object> result=new HashMap<>();
        if(pageNumber==null || pageSize==null || StrKit.isBlank(categoryId)){
            result.put("code","10001");
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }
        Page<Record> productList=mallProductService.getProductByCategory(pageNumber,pageSize,categoryId);

        renderJson(new DataTable<>(productList));
    }


    /**
     * 通过id获取商品详细信息
     */
    public void getProductById(){
        String id=getPara("id");
        Map<String,Object> result=new HashMap<>();
        if(StrKit.isBlank(id)){
            result.put("code","10001");
            result.put("msg","商品id不能为空");
            renderJson(result);
            return;
        }
        Record record=mallProductService.getProductById(id);
        result.put("code","0");
        result.put("msg","success");
        result.put("data",record);
        renderJson(result);
    }

    public void getUserShopingCar(){
        String userId=getPara("userId");
        Map<String,Object> result=new HashMap<>();
        if(StrKit.isBlank(userId)){
            result.put("code","10001");
            result.put("msg","useId不能为空");
            renderJson(result);
            return;
        }
        List<Record> recordList=mallShopingCarService.getUserShopingCard(userId);
        result.put("code","0");
        result.put("msg","success");
        result.put("data",recordList);
        renderJson(result);
    }

    public void saveShopingCarRecord(){
        MallShopingCar shopingCar=getBean(MallShopingCar.class,"",true);
        Map<String,Object> result=new HashMap<>();
        if(StrKit.isBlank(shopingCar.getUserId()) || StrKit.isBlank(shopingCar.getProductId()) || shopingCar.getBuyNum()==null){
            result.put("code","10001");
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }
        boolean flag=mallShopingCarService.saveShopingCardRecord(shopingCar);

        if(flag){
            result.put("code","0");
            result.put("msg","success");
        }else{
            result.put("code","10001");
            result.put("msg","保存失败");
        }
        renderJson(result);
    }

    public void delShopingCarRecord(){
        String id=getPara("id");
        Map<String,Object> result=new HashMap<>();
        if(StrKit.isBlank(id)){
            result.put("code","10001");
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }
        boolean flag=mallShopingCarService.delShopingCardRecord(id);
        if(flag){
            result.put("code","0");
            result.put("msg","success");
        }else{
            result.put("code","10001");
            result.put("msg","作废失败");
        }
        renderJson(result);
    }

}
