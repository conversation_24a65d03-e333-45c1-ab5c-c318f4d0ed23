package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsActivityDetailService;
import com.cszn.integrated.service.api.member.MmsActivityService;
import com.cszn.integrated.service.api.weixin.WeiXinActivityService;
import com.cszn.integrated.service.entity.member.MmsActivity;
import com.cszn.integrated.service.entity.member.MmsActivityDetail;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@JCors
@RequestMapping(value="/weiXinActivity", viewPath="")
public class WeiXinActivityController extends BaseController {

    @Inject
    WeiXinActivityService weiXinActivityService;
    @Inject
    MmsActivityService mmsActivityService;
    @Inject
    MmsActivityDetailService mmsActivityDetailService;

    public void test() throws Exception{
        String activityId=getPara("activityId");
        if(StrKit.isBlank(activityId)){
            renderJson(Ret.fail("msg","活动id不能为空"));
            return;
        }
        MmsActivity mmsActivity = mmsActivityService.findById(activityId);
        if(mmsActivity==null){
            renderJson(Ret.fail("msg","获取活动失败"));
            return;
        }
        //获取活动结束时间
        Date date=new Date();;
        if(DateUtils.compareDays(date,mmsActivity.getEndTime())>0){
            renderJson(Ret.fail("msg","该活动已结束"));
            return;
        }
        long expireSeconds=(mmsActivity.getEndTime().getTime()-date.getTime())/1000;
        if(expireSeconds>2592000){
            expireSeconds=2592000;
        }
        Map<String,Object> resultMap=weiXinActivityService.getQrcodeTicket(mmsActivity.getActivityQrcode(),(int)expireSeconds,"QR_STR_SCENE");

        if((boolean)resultMap.get("flag")){
            //
            File file =weiXinActivityService.getQrcodeImgFile((String)resultMap.get("ticket"));
            renderFile(file);
        }else{
            renderJson(Ret.fail("msg",(String)resultMap.get("msg")));
        }

    }


    /**
     * 发送验证码
     */
    public void sendRandomCode(){
        String phone=getPara("phone");
        String activityQrcode=getPara("activityQrcode");
        MmsActivity activity=mmsActivityService.getActivityByQrcode(activityQrcode);
        if(activity==null){
            renderCodeFailed("获取活动失败");
            return;
        }
        if("1".equals(activity.getIsEnable())){
            renderCodeFailed("活动暂时无效");
            return;
        }
        if(DateUtils.compareDays(new Date(),activity.getEndTime())>0){
            renderCodeFailed("该活动已结束");
            return;
        }

        if(StrKit.isBlank(phone)){
            renderCodeFailed("手机号码不能为空");
            return;
        }
        if(!phone.matches("^1[3-9][0-9]\\d{8}$")){
            renderCodeFailed("手机号码格式不正确");
            return;
        }
        String sql="select * from mms_activity_detail where activity_id=? and phone_num=? and del_flag='0' ";
        Record record=Db.findFirst(sql,activity.getId(),phone);
        if(record!=null){
            renderCodeFailed("已参加过该活动请勿重复参加");
            return;
        }

        Map<String,Object> map=weiXinActivityService.sendRandomCode(phone);
        if((boolean)map.get("flag")){
            renderCodeSuccess("发送成功");
        }else{
            renderCodeFailed((String)map.get("msg"));
        }
    }

    /**
     * 校验验证码
     */
    public void saveActivityDetail(){
        String fullName=getPara("fullName");
        String phone=getPara("phone");
        String code=getPara("code");
        String userId=getPara("userId");
        String activityQrcode=getPara("activityQrcode");
        if(StrKit.isBlank(phone)){
            renderCodeFailed("手机号码不能为空");
            return;
        }
        if(StrKit.isBlank(userId)){
            renderCodeFailed("userId不能为空");
            return;
        }
        if(StrKit.isBlank(activityQrcode)){
            renderCodeFailed("活动编号不能为空");
            return;
        }
        MmsActivity activity=mmsActivityService.getActivityByQrcode(activityQrcode);
        if(activity==null){
            renderCodeFailed("通过活动编号获取活动失败");
            return;
        }
        if("1".equals(activity.getIsEnable())){
            renderCodeFailed("活动暂时无效");
            return;
        }
        if(StrKit.isBlank(code) || code.length()!=6){
            renderCodeFailed("请输入6位验证码");
            return;
        }

        if(DateUtils.compareDays(new Date(),activity.getEndTime())>0){
            renderCodeFailed("该活动已结束");
            return;
        }

        if(mmsActivityDetailService.getMmsActivityDetailByPhone(activity.getId(),phone)!=null){
            renderCodeFailed("已参加过该活动请勿重复参加");
            return;
        }


        boolean flag=weiXinActivityService.checkCode(phone,code);
        if(flag){
            //保存

            MmsActivityDetail mmsActivityDetail=new MmsActivityDetail();
            mmsActivityDetail.setPhoneNum(phone);
            mmsActivityDetail.setUserId(userId);
            mmsActivityDetail.setActivityId(activity.getId());
            mmsActivityDetail.setFullName(fullName);

            mmsActivityDetailService.saveMmsActivityDetail(mmsActivityDetail);
        }
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }

    }




    public void getActivity(){
        String activityQrcode=getPara("activityQrcode");
        if(StrKit.isBlank(activityQrcode)){
            renderCodeFailed("参数缺失");
            return;
        }
        MmsActivity activity=mmsActivityService.getActivityByQrcode(activityQrcode);
        renderCodeSuccess("success",activity);
    }

    public void getPrizeInfo(){
        String userId=getPara("userId");
        String activityQrcode=getPara("activityQrcode");

        if(StrKit.isBlank(userId) || StrKit.isBlank(activityQrcode)){
            renderCodeFailed("参数缺失");
            return;
        }
        MmsActivity activity=mmsActivityService.getActivityByQrcode(activityQrcode);
        if(activity==null){
            renderCodeFailed("对应活动不存在");
            return;
        }

        MmsActivityDetail mmsActivityDetail=mmsActivityDetailService.getMmsActivityDetailByUserId(activity.getId(),userId);
        if(mmsActivityDetail==null){
            renderCodeSuccess("success",null);
        }else{
            mmsActivityDetail.setDelFlag(null);
            mmsActivityDetail.setCreateTime(null);
            mmsActivityDetail.setUpdateTime(null);
            renderCodeSuccess("success",mmsActivityDetail);
        }


    }

    public void testa(){
        MmsActivityDetail mmsActivityDetail=getBean(MmsActivityDetail.class,"",true);
        boolean flag=mmsActivityDetailService.saveMmsActivityDetail(mmsActivityDetail);
        if(flag){
            renderCodeSuccess("success");
        }else{
            renderCodeFailed("error");
        }
    }


    public static void main(String[] args) {
        String str="20210327141057002," +
                "20210313112701001," +
                "20210524121951011," +
                "20200612091449001," +
                "20200510095421004," +
                "20201013111243005," +
                "20190110111738080," +
                "20200514144249002," +
                "20210524121834008," +
                "20190109175304071," +
                "20210429095700008," +
                "20190110131136130," +
                "20190108155810005," +
                "20200517102839004," +
                "20200725133931002," +
                "20191107103456005," +
                "20201024114547002," +
                "20200612125459004," +
                "20210401112958003," +
                "20190109141443014," +
                "20191103123157023," +
                "20191213162217002," +
                "20191026102755004," +
                "20190729105037002," +
                "20191017151903006," +
                "20190620140455002," +
                "20200402161452001," +
                "20210424111827002," +
                "20191025150852077," +
                "20190524204850004," +
                "20200511123312001," +
                "20190908143541002," +
                "20200626105327002," +
                "20210328163414002," +
                "20190110085333010," +
                "20210323122110001," +
                "20190109150344018," +
                "20210524123019014," +
                "20210325112126002," +
                "20190110111010074," +
                "20191025143100058," +
                "20190909113925002," +
                "20201107141133002," +
                "20191101111047002," +
                "20190110111511078," +
                "20210524160529017," +
                "20201009112227002," +
                "20200512112929005," +
                "20190909113928003," +
                "20191020112627002," +
                "20200530123115002," +
                "20210405151715002," +
                "20200606114444002," +
                "20201024152802007," +
                "20191004115715002," +
                "20210510112901005," +
                "20210130112330002," +
                "20191122145151043," +
                "20200429104255002," +
                "20200824124006002," +
                "20201115131542001," +
                "20200619112446004," +
                "20200726103818002," +
                "20191025123326033," +
                "20210301164059002," +
                "20190927111612002," +
                "20210123162014002," +
                "20191105140048002," +
                "20200619113127006," +
                "20200620135245002," +
                "20191104102930020," +
                "20200807121221005," +
                "20191022115852004," +
                "20190110112302084," +
                "20210524115958005," +
                "20190109174253063," +
                "20190110125045120," +
                "20190927141118007," +
                "20190915143205002," +
                "20210524170010026," +
                "20201205133852005," +
                "20210306140920005," +
                "20190110100647050," +
                "20190110085714012," +
                "20191025141432047," +
                "20200524150241002," +
                "20190109164419045," +
                "20200105100816002," +
                "20201203122643001," +
                "20190110102920060," +
                "20190110115828104," +
                "20210401135621006," +
                "20190109172824055," +
                "20201021084803002," +
                "20210609115013005," +
                "20191207114826002," +
                "20200624165705002," +
                "20191025141844051," +
                "20210323122420006," +
                "20191104095956017," +
                "20210429091413002," +
                "20210328163819005," +
                "20191025143407060," +
                "20200803131803003," +
                "20191011140031002," +
                "20190729150621004," +
                "20200816100351002," +
                "20190930162210002," +
                "20190109162627037," +
                "20201101105743002," +
                "20210401110828001," +
                "20210228122952001," +
                "20191025125351043," +
                "20200626185821004," +
                "20190919115539008," +
                "20190110113057094," +
                "20200824124412005," +
                "20200517095505001," +
                "20210228123328003," +
                "20200121120652002," +
                "20200807114408002," +
                "20190110085910014," +
                "20190110092208024," +
                "20190926130201002," +
                "20190110090155016," +
                "20190427153029002," +
                "20190109175845075," +
                "20200619110836002," +
                "20210107104219002," +
                "20210429095948014," +
                "20200525101748002," +
                "20200531163728002," +
                "20210416154444001," +
                "20190110112102082," +
                "20190109174611065," +
                "20191025123612035," +
                "20190425113323002," +
                "20200717145517002," +
                "20201019183204005," +
                "20190110124724118," +
                "20191103124431029," +
                "20190316133443006," +
                "20191022115030002," +
                "20210510105741002," +
                "20190720161004002," +
                "20191025091432009," +
                "20191025145702076," +
                "20201128112045002," +
                "20190109163414039," +
                "20201009113020005," +
                "20190110095927048," +
                "20190109115102012," +
                "20210522135731001," +
                "20200712132819002," +
                "20190109174926067," +
                "20200709150314002," +
                "20210430165512001," +
                "20200621102014002," +
                "20191103122037019," +
                "20210302142004007," +
                "20190821152555002," +
                "20190218150702005," +
                "20210128164253002," +
                "20200510132724014," +
                "20191227141445002," +
                "20191205103856002," +
                "20190109175118069," +
                "20191103130549035," +
                "20210524164603020," +
                "20190108142438003," +
                "20200510095638006," +
                "20201231184320002," +
                "20210131153542001," +
                "20210413115212002," +
                "20190110092626028," +
                "20190110092813030," +
                "20190109154700023," +
                "20200708131651005," +
                "20191026102642002," +
                "20191104085631011," +
                "20210303121402005," +
                "20190109174035061," +
                "20190109164157043," +
                "20210221151843002," +
                "20191119110433032," +
                "20200110111933002," +
                "20200919130207002," +
                "20190922113149002," +
                "20190916162801002," +
                "20210623185910002," +
                "20200806172017002," +
                "20210524170352029," +
                "20190928124359002," +
                "20190408105233004," +
                "20200821114207002," +
                "20200831154420002," +
                "20190525135627008," +
                "20200630105130002," +
                "20190920115720002," +
                "20190508154523002," +
                "20190420114636002," +
                "20191113162900051," +
                "20210404111018002," +
                "20210416154855003," +
                "20210524104205002," +
                "20190911150131002," +
                "20190110113230096," +
                "20201013110509002," +
                "20210512112515001," +
                "20210403152355002," +
                "20200512112109002," +
                "20190110084230004," +
                "20190316133029004," +
                "20190424152110002," +
                "20210410133402002," +
                "20190110093914038," +
                "20190110115943106," +
                "20200826103315002," +
                "20191025144543066," +
                "20210516092224005," +
                "20190110130029124," +
                "20200708131350002," +
                "20201112152910002," +
                "20190110095217044," +
                "20191103200041043," +
                "20190227142634002," +
                "20190908143827004," +
                "20190109095216002," +
                "20200511142910004," +
                "20210609112248002," +
                "20200510112412012," +
                "20190110120125108," +
                "20190226140400002," +
                "20210118154004002," +
                "20190926130801004," +
                "20191203141605018," +
                "20201108130534001," +
                "20200517125441007," +
                "20190110112413086," +
                "20210429095838011," +
                "20200610153615002," +
                "20190110093401034," +
                "20200914095557001," +
                "20191103122759021," +
                "20200424104616002," +
                "20190923130112002," +
                "20210306140538002," +
                "20210516092023002," +
                "20190110113527100," +
                "20190110083545002," +
                "20210401140043011," +
                "20201201133059002," +
                "20210513155451002," +
                "20190318145928010," +
                "20210323122627009," +
                "20210516092500008," +
                "20210429095437005," +
                "20210222095627002," +
                "20210516092653011," +
                "20190110130442126," +
                "20190919115410006," +
                "20200806172606005," +
                "20190612153242002," +
                "20191025101623027," +
                "20210131153825003";
        String[] array=str.split(",");
        List<String> list=new ArrayList<>();
        String str2="20190108142438003," +
                "20190108142438003," +
                "20190108155810005," +
                "20190109095216002," +
                "20190109095216002," +
                "20190109095216002," +
                "20190109112414010," +
                "20190109112414010," +
                "20190109115102012," +
                "20190109141443014," +
                "20190109150344018," +
                "20190109150344018," +
                "20190109154700023," +
                "20190109162627037," +
                "20190109163414039," +
                "20190109164157043," +
                "20190109164157043," +
                "20190109164419045," +
                "20190109164419045," +
                "20190109172824055," +
                "20190109174035061," +
                "20190109174253063," +
                "20190109174611065," +
                "20190109174611065," +
                "20190109174926067," +
                "20190109174926067," +
                "20190109175118069," +
                "20190109175118069," +
                "20190109175304071," +
                "20190109175304071," +
                "20190109175845075," +
                "20190109175845075," +
                "20190110083545002," +
                "20190110083545002," +
                "20190110084230004," +
                "20190110084230004," +
                "20190110085333010," +
                "20190110085714012," +
                "20190110085714012," +
                "20190110085910014," +
                "20190110085910014," +
                "20190110090155016," +
                "20190110090155016," +
                "20190110092208024," +
                "20190110092208024," +
                "20190110092626028," +
                "20190110092813030," +
                "20190110093401034," +
                "20190110093914038," +
                "20190110093914038," +
                "20190110095024042," +
                "20190110095024042," +
                "20190110095217044," +
                "20190110095217044," +
                "20190110095927048," +
                "20190110095927048," +
                "20190110100647050," +
                "20190110100647050," +
                "20190110102920060," +
                "20190110102920060," +
                "20190110111010074," +
                "20190110111010074," +
                "20190110111511078," +
                "20190110111511078," +
                "20190110111511078," +
                "20190110111738080," +
                "20190110111738080," +
                "20190110112102082," +
                "20190110112302084," +
                "20190110112302084," +
                "20190110112413086," +
                "20190110112413086," +
                "20190110113057094," +
                "20190110113057094," +
                "20190110113230096," +
                "20190110113230096," +
                "20190110113527100," +
                "20190110113527100," +
                "20190110115828104," +
                "20190110115828104," +
                "20190110115943106," +
                "20190110120125108," +
                "20190110120537112," +
                "20190110120537112," +
                "20190110124724118," +
                "20190110124724118," +
                "20190110125045120," +
                "20190110125045120," +
                "20190110130029124," +
                "20190110130029124," +
                "20190110130442126," +
                "20190110130442126," +
                "20190110131136130," +
                "20190114212741012," +
                "20190114212741012," +
                "20190115111719022," +
                "20190115111719022," +
                "20190218150702005," +
                "20190226140400002," +
                "20190227142634002," +
                "20190227142634002," +
                "20190313100048004," +
                "20190313100048004," +
                "20190316133029004," +
                "20190316133029004," +
                "20190316133443006," +
                "20190318145928010," +
                "20190318145928010," +
                "20190408105233004," +
                "20190408105233004," +
                "20190420114636002," +
                "20190420114636002," +
                "20190424152110002," +
                "20190425113323002," +
                "20190425113323002," +
                "20190425113323002," +
                "20190427153029002," +
                "20190506131755002," +
                "20190524204850004," +
                "20190525133731002," +
                "20190525134009004," +
                "20190525135627008," +
                "20190525135627008," +
                "20190530133011002," +
                "20190530133011002," +
                "20190612153242002," +
                "20190620140455002," +
                "20190710110128002," +
                "20190710110339004," +
                "20190720161004002," +
                "20190729105037002," +
                "20190729150621004," +
                "20190821152555002," +
                "20190908143541002," +
                "20190908143827004," +
                "20190909113925002," +
                "20190909113928003," +
                "20190911150131002," +
                "20190915143205002," +
                "20190916162801002," +
                "20190916162801002," +
                "20190916162801002," +
                "20190919115410006," +
                "20190919115539008," +
                "20190920115720002," +
                "20190922113149002," +
                "20190922113149002," +
                "20190923130112002," +
                "20190926130201002," +
                "20190926130801004," +
                "20190927111612002," +
                "20190927141118007," +
                "20190928124359002," +
                "20190930162210002," +
                "20191004115715002," +
                "20191004115715002," +
                "20191005144545002," +
                "20191011140031002," +
                "20191020112627002," +
                "20191020112627002," +
                "20191022115030002," +
                "20191022115030002," +
                "20191022115030002," +
                "20191022115852004," +
                "20191025091432009," +
                "20191025101623027," +
                "20191025123326033," +
                "20191025123612035," +
                "20191025125351043," +
                "20191025141432047," +
                "20191025141844051," +
                "20191025143100058," +
                "20191025143407060," +
                "20191025144543066," +
                "20191025145702076," +
                "20191025150852077," +
                "20191026102642002," +
                "20191026102755004," +
                "20191031110830002," +
                "20191031110830002," +
                "20191101111047002," +
                "20191103122037019," +
                "20191103122037019," +
                "20191103122759021," +
                "20191103123157023," +
                "20191103124431029," +
                "20191103130549035," +
                "20191103130549035," +
                "20191103133602041," +
                "20191103200041043," +
                "20191103200041043," +
                "20191104085631011," +
                "20191104085945014," +
                "20191104095956017," +
                "20191104095956017," +
                "20191104102930020," +
                "20191105140048002," +
                "20191105140048002," +
                "20191107103456005," +
                "20191107103456005," +
                "20191107103456005," +
                "20191113162900051," +
                "20191113162900051," +
                "20191122145151043," +
                "20191203141605018," +
                "20191205103856002," +
                "20191207114826002," +
                "20191209133514002," +
                "20191209133514002," +
                "20191213162217002," +
                "20191227141445002," +
                "20191227141445002," +
                "20200103094734002," +
                "20200105100816002," +
                "20200110111933002," +
                "20200116112656005," +
                "20200116112922008," +
                "20200121120652002," +
                "20200402161452001," +
                "20200402161452001," +
                "20200424104616002," +
                "20200429104255002," +
                "20200510075504002," +
                "20200510095421004," +
                "20200510095638006," +
                "20200510112412012," +
                "20200510132724014," +
                "20200510132724014," +
                "20200511123312001," +
                "20200511142910004," +
                "20200512112109002," +
                "20200512112109002," +
                "20200512112929005," +
                "20200512112929005," +
                "20200512112929005," +
                "20200514144249002," +
                "20200517095505001," +
                "20200517102839004," +
                "20200517125441007," +
                "20200523104706002," +
                "20200523104706002," +
                "20200523104706002," +
                "20200524150241002," +
                "20200524150241002," +
                "20200525101748002," +
                "20200530123115002," +
                "20200530123115002," +
                "20200531163728002," +
                "20200606114444002," +
                "20200606114444002," +
                "20200609131323002," +
                "20200609131323002," +
                "20200610153615002," +
                "20200610153615002," +
                "20200612091449001," +
                "20200612125459004," +
                "20200613105907002," +
                "20200613161454004," +
                "20200616110737001," +
                "20200619110836002," +
                "20200619110836002," +
                "20200619110836002," +
                "20200619112446004," +
                "20200619113127006," +
                "20200620135245002," +
                "20200620135245002," +
                "20200621102014002," +
                "20200624165705002," +
                "20200624165705002," +
                "20200626105327002," +
                "20200626185821004," +
                "20200626185821004," +
                "20200630105130002," +
                "20200708131350002," +
                "20200708131651005," +
                "20200709150314002," +
                "20200712132819002," +
                "20200712132819002," +
                "20200712132819002," +
                "20200714100851002," +
                "20200717145517002," +
                "20200725133931002," +
                "20200725133931002," +
                "20200726103818002," +
                "20200803131803003," +
                "20200806172017002," +
                "20200806172606005," +
                "20200807114408002," +
                "20200807121221005," +
                "20200807121221005," +
                "20200816100351002," +
                "20200816100351002," +
                "20200816100351002," +
                "20200821114207002," +
                "20200824124006002," +
                "20200824124412005," +
                "20200826103315002," +
                "20200826103315002," +
                "20200831154420002," +
                "20200914095557001," +
                "20200919130207002," +
                "20200919130207002," +
                "20200919130207002," +
                "20200919130207002," +
                "20200919130207002," +
                "20200919130207002," +
                "20201009112227002," +
                "20201009113020005," +
                "20201009113020005," +
                "20201013110509002," +
                "20201013111243005," +
                "20201019141754002," +
                "20201019183204005," +
                "20201021084803002," +
                "20201024114547002," +
                "20201024114547002," +
                "20201024152802007," +
                "20201024152802007," +
                "20201028122629002," +
                "20201101105743002," +
                "20201101105743002," +
                "20201101105743002," +
                "20201103133852001," +
                "20201103134136003," +
                "20201107141133002," +
                "20201108130534001," +
                "20201112152910002," +
                "20201115131542001," +
                "20201119144424001," +
                "20201128112045002," +
                "20201128112045002," +
                "20201128112045002," +
                "20201129111934002," +
                "20201129112156005," +
                "20201201133059002," +
                "20201203122643001," +
                "20201205133852005," +
                "20201205133852005," +
                "20201213154947002," +
                "20201231184320002," +
                "20210118154004002," +
                "20210118154004002," +
                "20210123162014002," +
                "20210123162014002," +
                "20210130112330002," +
                "20210131153542001," +
                "20210131153825003," +
                "20210201114345002," +
                "20210202185120002," +
                "20210301164059002," +
                "20210313112701001," +
                "20210313135449004," +
                "20210313140210007," +
                "20210323122110001," +
                "20210323122332004," +
                "20210323122420006," +
                "20210325112126002," +
                "20210328163414002," +
                "20210328163819005," +
                "20210401112958003," +
                "20210401135621006," +
                "20210401135621006," +
                "20210401135621006," +
                "20210401140043011," +
                "20210401140043011," +
                "20210401140043011," +
                "20210403152355002," +
                "20210404111018002," +
                "20210410133402002," +
                "20210413115212002," +
                "20210424111827002," +
                "20210429091413002," +
                "20210429095437005," +
                "20210429095700008," +
                "20210429095838011," +
                "20210429095948014," +
                "20210430165512001," +
                "20210510105741002," +
                "20210510112901005," +
                "20210512112515001," +
                "20210512112515001," +
                "20210513155451002," +
                "20210516092023002," +
                "20210516092224005," +
                "20210516092500008," +
                "20210516092653011," +
                "20210522135731001," +
                "20210524104205002," +
                "20210524115958005," +
                "20210524121834008," +
                "20210524121951011," +
                "20210524123019014," +
                "20210524160529017," +
                "20210524164603020," +
                "20210524170010026," +
                "20210524170352029," +
                "20210609112248002," +
                "20210609115013005," +
                "20210623185910002";

        String[] array2=str2.split(",");
        
        for(int i=0;i<array2.length;i++){
            list.add(array2[i]);
        }

        for(int i=0;i<array.length;i++){
            if(!list.contains(array[i])){
                System.out.println(array[i]);
            }
        }
    }


}
