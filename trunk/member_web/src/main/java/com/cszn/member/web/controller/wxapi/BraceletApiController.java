package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.BraceletApiService;
import com.jfinal.aop.Inject;
import com.jfinal.kit.HttpKit;
import io.jboot.web.controller.annotation.RequestMapping;


@RequestMapping(value = "/braceletapi")
public class BraceletApiController extends BaseController {

    @Inject
    private BraceletApiService braceletApiService;

    public void braceletData(){
        String str= HttpKit.readData(getRequest());
        if(str.startsWith("[") && str.endsWith("]")){
            JSONArray array= JSON.parseArray(str);
            if(array!=null && array.size()>0){
                braceletApiService.analysisData(array);
            }
        }
        renderJson("{\"suc\":true}");
    }
}
