package com.cszn.member.web.controller.marketing;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsActivityMemberService;
import com.cszn.integrated.service.entity.member.MmsActivityMember;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

/**
 * @Description 二维码活动会员管理
 * <AUTHOR>
 * @Date 2019/7/9
 **/
@RequestMapping(value="/mms/activityMember", viewPath="/modules_page/mms/qrcodeMember")
public class ActivityMemberController extends BaseController {

    @Inject
    private MmsActivityMemberService mmsActivityMemberService;



    /**
     * 跳转二维码活动会员界面
      */
    public void index(){
        render("qrcodeMemberIndex.html");
    }


    /**
     * 活动会员列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MmsActivityMember member = getBean(MmsActivityMember.class,"",true);
        Page<Record> page = mmsActivityMemberService.findList(getParaToInt("page"), getParaToInt("limit"),member);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 删除二维码
     */
    public void delete(){
        String id = getPara("id");
        boolean flag = mmsActivityMemberService.delActivityMember(id, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "删除成功"));
        }else{
            renderJson(Ret.fail("msg", "删除失败"));
        }
    }


    /**
     * 跳转 编辑页面
     */
    public void activityMemberForm(){
        String id = getPara("id");
        MmsActivityMember member = mmsActivityMemberService.get(id);
        setAttr("member",member);
        render("qrcodeMemberForm.html");
    }


    /**
     * 保存:此处为编辑
     */
    public void save(){
        MmsActivityMember member = getBean(MmsActivityMember.class,"member",true);
        if(StringUtils.isBlank(member.getId())){ renderJson(Ret.fail("msg", "此记录标识为空，不可保存")); return;}
        if(member.update()){
            renderJson(Ret.ok("msg", "保存成功"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }
}
