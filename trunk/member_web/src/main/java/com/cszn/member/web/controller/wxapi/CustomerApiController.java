package com.cszn.member.web.controller.wxapi;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsCustomerAssignService;
import com.cszn.integrated.service.api.member.MmsCustomerFaceDataService;
import com.cszn.integrated.service.api.member.MmsCustomerFaceService;
import com.cszn.integrated.service.api.member.MmsCustomerInfoService;
import com.cszn.integrated.service.api.member.MmsCustomerVisitService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.pers.PersEmpUserService;
import com.cszn.integrated.service.api.pers.PersOrgEmployeeService;
import com.cszn.integrated.service.entity.member.MmsCustomerFace;
import com.cszn.integrated.service.entity.member.MmsCustomerFaceData;
import com.cszn.integrated.service.entity.member.MmsCustomerInfo;
import com.cszn.integrated.service.entity.member.MmsCustomerVisit;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.member.web.support.log.LogInterceptor;
import com.cszn.member.web.validator.CorsInterceptor;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.web.controller.annotation.RequestMapping;

@JCors
@RequestMapping(value = "/customerApi",viewPath = "")
@Clear(LogInterceptor.class)
public class CustomerApiController extends BaseController {

	@Inject
	private MmsMemberService mmsMemberService;
	@Inject
    private MmsCustomerInfoService mmsCustomerInfoService;
	@Inject
	private MmsCustomerAssignService mmsCustomerAssignService;
	@Inject
	private MmsCustomerVisitService mmsCustomerVisitService;
	@Inject
    private MmsCustomerFaceService mmsCustomerFaceService;
	@Inject
	private MmsCustomerFaceDataService mmsCustomerFaceDataService;
	@Inject
	private PersEmpUserService persEmpUserService;
	@Inject
	private PersOrgEmployeeService persOrgEmployeeService;

    private static Logger logger = LoggerFactory.getLogger(CustomerApiController.class);

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void customerPage(){
    	MmsCustomerInfo customerInfo = getBean(MmsCustomerInfo.class,"",true);
		Page<MmsCustomerInfo> page = mmsCustomerInfoService.customerPage(getParaToInt("page",1), getParaToInt("limit",10), customerInfo);
		renderJson(new DataTable<MmsCustomerInfo>(page));
	}

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void myPage(){
    	MmsCustomerInfo customerInfo = getBean(MmsCustomerInfo.class,"",true);
    	if(StrKit.isBlank(customerInfo.getCreateBy())) {
    		DataTable<MmsCustomerInfo> page = new DataTable<MmsCustomerInfo>(new Page<MmsCustomerInfo>());
    		page.setCode(1);
			page.setMsg("createBy参数不能为空!");
			renderJson(page);
    	}else {
    		Page<MmsCustomerInfo> page = mmsCustomerInfoService.myPageTable(getParaToInt("page",1), getParaToInt("limit",10), customerInfo);
    		renderJson(new DataTable<MmsCustomerInfo>(page));
    	}
    }

    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void salesPage(){
    	MmsCustomerInfo customerInfo = getBean(MmsCustomerInfo.class,"",true);
    	if(StrKit.isBlank(customerInfo.getCreateBy())) {
    		DataTable<MmsCustomerInfo> page = new DataTable<MmsCustomerInfo>(new Page<MmsCustomerInfo>());
    		page.setCode(1);
			page.setMsg("createBy参数不能为空!");
			renderJson(page);
    	}else {
    		Page<MmsCustomerInfo> page = mmsCustomerInfoService.salesPageTable(getParaToInt("page",1), getParaToInt("limit",10), customerInfo);
    		renderJson(new DataTable<MmsCustomerInfo>(page));
    	}
    }
    
	/**
	 * 精确查询分页列表
	 */
    @JCors
    @Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
	public void preciseQueryTable(){
		MmsCustomerInfo model = getBean(MmsCustomerInfo.class,"",true);
		if(StrKit.isBlank(model.getMemberName())&&StrKit.isBlank(model.getIdcard())&&StrKit.isBlank(model.getPhoneNumber())) {
			DataTable<MmsCustomerInfo> page = new DataTable<MmsCustomerInfo>(new Page<MmsCustomerInfo>());
			page.setCode(1);
			page.setMsg("查询条件不能全部为空!");
			renderJson(page);
    	}else {
    		Page<MmsCustomerInfo> page = mmsCustomerInfoService.preciseQueryTable(getParaToInt("page",1), getParaToInt("limit",10), model);
    		renderJson(new DataTable<MmsCustomerInfo>(page));
    	}
	}

	@JCors
	@Before(CorsInterceptor.class)
	@Clear(LogInterceptor.class)
	public void checkPhoneNumber() {
		final String phoneNumber = getPara("phoneNumber");
		if(StrKit.isBlank(phoneNumber)) {
			renderCodeFailed("phoneNumber参数不能为空!");
			return;
		}
		if(!phoneNumber.matches("^1[3-9][0-9]\\d{8}$")){
			renderCodeFailed("手机号码格式不正确");
			return;
		}
		MmsCustomerInfo customerInfo = mmsCustomerInfoService.getByPhoneNumber(phoneNumber);
		if(customerInfo!=null){
			renderCodeFailed(phoneNumber + "手机号已存在,请换一个!");
		}else{
			renderCodeSuccess("校验通过");
		}
	}

	@JCors
	@Before(CorsInterceptor.class)
	@Clear(LogInterceptor.class)
	public void customerDetail() {
		final String wxUserId = getPara("wxUserId");
		if(StrKit.isBlank(wxUserId)) {
			renderCodeFailed("wxUserId参数不能为空!");
			return;
		}
		MmsCustomerInfo customerInfo = mmsCustomerInfoService.getByWxUserId(wxUserId);
		if(customerInfo==null){
			customerInfo = new MmsCustomerInfo();
		}
		renderCodeSuccess("获取成功", customerInfo);
	}

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void memberSave(){
		Ret returnRet = Ret.fail("msg", "保存失败");
		final String verifyCode = getPara("verifyCode");
		MmsCustomerInfo model = getBean(MmsCustomerInfo.class,"",true);
		if("bmp".equalsIgnoreCase(model.getDataSource())){
			returnRet = mmsCustomerInfoService.customerSave(model, null);
		}else{
			//调用旅居身份证校验接口
			Map<String,String> params=new HashMap<>();
			params.put("name", model.getMemberName());//客户姓名
			params.put("idCardType", model.getIdcardType());//客户证件类型
			params.put("idCard", model.getIdcard());//卡主身份证
			params.put("userId", model.getCreateBy());//操作人用户id
			params.put("channel", "FaceRecognition");//渠道来源
			params.put("key", "40FA2FDE-9B79-40EC-8147-50F1C7EEF7DE");//key
			final String resultStr = HttpClientsUtils.httpPostForm(Global.checkIdCardUrl,params,null,null);
			logger.info("resultStr==="+resultStr);
			JSONObject returnJson = JSONObject.parseObject(resultStr);
			final int returnType = returnJson.getIntValue("Type");
			final String returnMsg = returnJson.getString("Msg");
			logger.info("身份证校验返回returnType==="+returnType);
			logger.info("身份证校验返回returnMsg==="+returnMsg);
			//姓名、身份证校验成功
			if(returnType==1){
				returnRet = mmsCustomerInfoService.customerSave(model, verifyCode);
			}else{
				returnRet = Ret.fail("msg", returnMsg);
			}
		}
		renderJson(returnRet);
	}

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void visitTable() {
    	MmsCustomerVisit model = getBean(MmsCustomerVisit.class, "", true);
        Page<MmsCustomerVisit> modelPage = mmsCustomerVisitService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<MmsCustomerVisit>(modelPage));
    }

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void visitEdit() {
    	final String modelId = getPara("id");
    	renderJson(mmsCustomerVisitService.findById(modelId));
    }

	@JCors
	@Before(CorsInterceptor.class)
	@Clear(LogInterceptor.class)
    public void visitSave() {
    	MmsCustomerVisit model = getBean(MmsCustomerVisit.class, "", true);
    	boolean flag = false;
    	if(StrKit.isBlank(model.getId())) {
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateTime(new Date());
    		model.setUpdateTime(new Date());
    		flag = model.save();
    	}else {
    		model.setUpdateTime(new Date());
    		flag = model.update();
    	}
		if(flag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void faceTable() {
    	MmsCustomerFace model = getBean(MmsCustomerFace.class, "", true);
        Page<MmsCustomerFace> modelPage = mmsCustomerFaceService.paginateByCondition(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderCodeSuccess("操作成功", new DataTable<MmsCustomerFace>(modelPage));
    }

	@JCors
	@Before(CorsInterceptor.class)
	@Clear(LogInterceptor.class)
	public void checkFaceIsHave() {
		final String idCardNos = getPara("IdCardNos");
		if(StrKit.isBlank(idCardNos)) {
			renderCodeFailed("IdCardNos参数不能为空!");
			return;
		}
		final String[] idCardNoArray = idCardNos.split(",");
		JSONArray returnArray = new JSONArray();
		for(String idCardNo : idCardNoArray){
			JSONObject returnObj = new JSONObject();
			returnObj.put("IdCardNo", idCardNo);
			MmsCustomerFace customerFace = mmsCustomerFaceService.findByIdcardAndPicType(idCardNo, "face");
			if(customerFace!=null){
				returnObj.put("HasFace", 1);
			}else{
				returnObj.put("HasFace", 0);
			}
			returnArray.add(returnObj);
		}
		renderCodeSuccess("返回成功", returnArray);
	}

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void faceList() {
    	final String customerName = getPara("customerName");
    	final String idcard = getPara("idcard");
    	final String picType = getPara("picType");
    	final List<MmsCustomerFace> faceList = mmsCustomerFaceService.findList(customerName, idcard, picType);
    	renderCodeSuccess("操作成功", faceList);
    }

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void faceDataList() {
    	final String faceId = getPara("faceId");
    	if(StrKit.isBlank(faceId)) {
    		renderCodeFailed("faceId参数不能为空!");
    		return;
    	}
    	final List<MmsCustomerFaceData> faceDataList = mmsCustomerFaceDataService.findList(faceId);
    	renderCodeSuccess("操作成功", faceDataList);
    }

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void getFace(){
    	final String id = getPara("id");
    	if(StrKit.isBlank(id)) {
    		renderCodeFailed("id参数不能为空!");
    		return;
    	}
		logger.info("id==="+id);
		MmsCustomerFace customerFace = mmsCustomerFaceService.findById(id);
		if(customerFace!=null){
			renderCodeSuccess("操作成功", customerFace);
		}else{
			renderCodeFailed("数据不存在");
		}
    }

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void faceSave(){
    	MmsCustomerFace model = getBean(MmsCustomerFace.class,"",true);
    	logger.info("记录id==="+model.getId());
    	logger.info("客户姓名==="+model.getCustomerName());
		logger.info("证件类型==="+model.getIdcardType());
		logger.info("身份证号==="+model.getIdcard());
		logger.info("图片类型==="+model.getPicType());
		logger.info("图片url==="+model.getPicUrl());
		logger.info("数据来源==="+model.getDataSource());
		
		//新增
		if(StrKit.isBlank(model.getId())){
			if(StrKit.isBlank(model.getCustomerName())) {
	    		renderCodeFailed("customerName客户姓名不能为空!");
	    		return;
	    	}
	    	if(StrKit.isBlank(model.getIdcard())) {
	    		renderCodeFailed("idcard身份证号不能为空!");
	    		return;
	    	}
	    	if(StrKit.isBlank(model.getPicType())) {
	    		renderCodeFailed("picType图片类型不能为空!");
	    		return;
	    	}
	    	if(StrKit.isBlank(model.getPicUrl())) {
	    		renderCodeFailed("picUrl图片url不能为空!");
	    		return;
	    	}
	    	if(StrKit.isBlank(model.getDataSource())) {
	    		renderCodeFailed("dataSource数据来源不能为空!");
	    		return;
	    	}
			MmsCustomerFace customerFace = mmsCustomerFaceService.findByTypeAndIdcard(model.getPicType(), model.getIdcard());
			if(customerFace==null){
				//调用旅居身份证校验接口
				Map<String,String> params=new HashMap<>();
				params.put("name", model.getCustomerName());//卡主姓名
				params.put("idCardType", model.getIdcardType());//卡主证件类型
				params.put("idCard", model.getIdcard());//卡主身份证
				params.put("userId", model.getCreateBy());//操作人用户id
				params.put("channel", "FaceRecognition");//渠道来源
				params.put("key", "40FA2FDE-9B79-40EC-8147-50F1C7EEF7DE");//key
				final String resultStr = HttpClientsUtils.httpPostForm(Global.checkIdCardUrl,params,null,null);
				logger.info("resultStr==="+resultStr);
				JSONObject returnJson = JSONObject.parseObject(resultStr);
				final int returnType = returnJson.getIntValue("Type");
				final String returnMsg = returnJson.getString("Msg");
				logger.info("身份证校验返回returnType==="+returnType);
				logger.info("身份证校验返回returnMsg==="+returnMsg);
				//姓名、身份证校验成功
				if(returnType==1){
					model.setId(IdGen.getUUID());
					model.setDelFlag(DelFlag.NORMAL);
					model.setCreateTime(new Date());
					model.setUpdateTime(new Date());
					boolean returnFlag = model.save();
					if(returnFlag) {
						renderCodeSuccess("操作成功");
					}else {
						renderCodeFailed("操作失败");
					}
				}else{
					renderCodeFailed(returnMsg);
				}
			}else{
				renderCodeFailed("数据已存在，请不要重复添加!");
				return;
			}
		//修改
		}else {
			if(StrKit.isBlank(model.getId())) {
	    		renderCodeFailed("id记录id不能为空!");
	    		return;
	    	}
			if(StrKit.isBlank(model.getPicUrl())) {
	    		renderCodeFailed("picUrl图片url不能为空!");
	    		return;
	    	}
			MmsCustomerFace face = new MmsCustomerFace();
			face.setId(model.getId());
			face.setPicUrl(model.getPicUrl());
			face.setUpdateTime(new Date());
    		boolean returnFlag = face.update();
    		if(returnFlag) {
    			renderCodeSuccess("操作成功");
        	}else {
        		renderCodeFailed("操作失败");
        	}
		}
    }

	@JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void faceDataSave(){
    	MmsCustomerFaceData model = getBean(MmsCustomerFaceData.class,"",true);
    	logger.info("记录id==="+model.getId());
    	logger.info("人脸信息id==="+model.getFaceId());
    	logger.info("设备id==="+model.getDeviceId());
    	logger.info("人脸数据==="+model.getFaceData());
    	//新增
    	if(StrKit.isBlank(model.getId())){
    		if(StrKit.isBlank(model.getFaceId())) {
    			renderCodeFailed("faceId人脸信息id不能为空!");
    			return;
    		}
    		if(StrKit.isBlank(model.getDeviceId())) {
    			renderCodeFailed("deviceId设备id不能为空!");
    			return;
    		}
    		if(StrKit.isBlank(model.getFaceData())) {
    			renderCodeFailed("faceData人脸数据不能为空!");
    			return;
    		}
			model.setId(IdGen.getUUID());
			model.setDelFlag(DelFlag.NORMAL);
			model.setCreateTime(new Date());
			model.setUpdateTime(new Date());
			boolean returnFlag = model.save();
			if(returnFlag) {
				renderCodeSuccess("操作成功");
			}else {
				renderCodeFailed("操作失败");
			}
    		//修改
    	}else {
    		if(StrKit.isBlank(model.getFaceData())) {
    			renderCodeFailed("faceData人脸数据不能为空!");
    			return;
    		}
    		MmsCustomerFaceData face = new MmsCustomerFaceData();
    		face.setId(model.getId());
    		face.setFaceData(model.getFaceData());
    		face.setUpdateTime(new Date());
    		boolean returnFlag = face.update();
    		if(returnFlag) {
    			renderCodeSuccess("操作成功");
    		}else {
    			renderCodeFailed("操作失败");
    		}
    	}
    }
}
