package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.cszn.integrated.base.utils.MiniProgramUtils;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsFaceLiveUploadService;
import com.cszn.integrated.service.api.member.MmsFaceRecognitionRecordService;
import com.cszn.integrated.service.api.member.MmsMemberFaceService;
import com.cszn.integrated.service.api.member.MmsMemberService;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.entity.member.MmsFaceLiveUpload;
import com.cszn.integrated.service.entity.member.MmsFaceRecognitionRecord;
import com.cszn.integrated.service.entity.member.MmsMember;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.member.web.support.log.LogInterceptor;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequestMapping(value="/faceapi", viewPath="")
public class FaceApiController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(FaceApiController.class);

    @Inject
    MmsFaceLiveUploadService mmsFaceLiveUploadService;

    @Inject
    MmsFaceRecognitionRecordService mmsFaceRecognitionRecordService;

    @Inject
    MmsMemberService mmsMemberService;

    @Inject
    DictService dictService;

    @Inject
    private MmsMemberFaceService mmsMemberFaceService;





    /**
     * 基地提交入住、退住用户身份信息
     */
    public void uploadLiveFace(){
        logger.info("基地提交入住、退住用户身份信息begin...");
        String appNo=getPara("appNo");
        String baseId = getPara("baseId");
        String liveFlag=getPara("liveFlag");
        String times=getPara("times");
        String recordData = getPara("recordData");
        logger.info("基地提交入住、退住用户身份信息接收入参：应用编号：[{}]，基地ID：[{}],入退住标识：[{}]，入退住时间：[{}],记录数组:[{}]",
                appNo,baseId,liveFlag,times,recordData);

        Map<String,Object> result=new HashMap<>();
        if(StringUtils.isBlank(appNo)){
            result.put("code","10001");
            result.put("msg","应用编号不能为空");
            renderJson(result);
            return;
        }else if(StringUtils.isBlank(liveFlag)){
            result.put("code","10001");
            result.put("msg","入住退住标记不能为空");
            renderJson(result);
            return;
        }else if(StringUtils.isBlank(times)){
            result.put("code","10001");
            result.put("msg","入住时间或退住时间不能为空");
            renderJson(result);
            return;
        }else if(StringUtils.isBlank(baseId)){
            result.put("code","10001");
            result.put("msg","基地ID不能为空");
            renderJson(result);
            return;
        }else if(StringUtils.isBlank(recordData)){
            result.put("code","10001");
            result.put("msg","记录数组不能为空");
            renderJson(result);
            return;
        }


        Map<String,Object> map=mmsFaceLiveUploadService.uploadLiveFace(appNo,baseId,liveFlag,times,recordData);
        logger.info("基地提交入住、退住用户身份信息serviceImpl处理结果：[{}]",JSON.toJSONString(map));
        if(map.containsKey("error")){
            result.put("code","10001");
            result.put("msg",map.get("error"));
            renderJson(result);
            return;
        }
        map.remove("flag");
        result.put("code", "0");
        result.put("data", map);
        logger.info("基地提交入住、退住用户身份信息end...");
        renderJson(result);
    }




    /**
     * 获取当天入住人脸数据
     */
    @Clear(LogInterceptor.class)
    public void getCheckinPersonFace(){
        logger.info("获取当天入住人脸数据begin...");
        String appNo=getPara("appNo");
        String baseId = getPara("baseId");
        logger.info("获取当天入住人脸数据接收入参：应用编号：[{}]，基地ID：[{}]",appNo,baseId);
        Map<String,Object> result=new HashMap<>();
        if(StringUtils.isBlank(appNo)){
            result.put("code", "10001");
            result.put("msg", "应用编号不能为空");
            renderJson(result);
            return;
        }
        List<Map<String,Object>> list=mmsFaceLiveUploadService.getPersonFace(appNo,baseId,"1");

        //打印日志时过滤base64字符串
        PropertyFilter profilter = new PropertyFilter(){
            @Override
            public boolean apply(Object object, String name, Object value) {
                if(name.equalsIgnoreCase("imgBase64")){
                    return false;
                }
                return true;
            }
        };

        logger.info("获取当天入住人脸数据出库处理数据：[{}]",JSON.toJSONString(list,profilter));
        result.put("code","0");
        result.put("msg","success");
        result.put("data",list);
        logger.info("获取当天入住人脸数据end...");
        renderJson(result);
    }




    /**
     * 获取当天退住人脸数据
     */
    @Clear(LogInterceptor.class)
    public void getCheckoutPersonFace(){
        logger.info("获取当天退住人脸数据begin...");
        String appNo=getPara("appNo");
        String baseId = getPara("baseId");
        logger.info("获取当天退住人脸数据接收入参：应用编号：[{}]，基地ID：[{}]",appNo,baseId);
        Map<String,Object> result=new HashMap<>();
        if(StringUtils.isBlank(appNo)){
            result.put("code", "10001");
            result.put("msg", "商户号不能为空");
            renderJson(result);
            return;
        }
        List<Map<String,Object>> list=mmsFaceLiveUploadService.getPersonFace(appNo,baseId,"2");

        //打印日志时过滤base64字符串
        PropertyFilter profilter = new PropertyFilter(){
            @Override
            public boolean apply(Object object, String name, Object value) {
                if(name.equalsIgnoreCase("imgBase64")){
                    return false;
                }
                return true;
            }
        };

        logger.info("获取当天退住人脸数据出库处理数据：[{}]",JSON.toJSONString(list,profilter));
        result.put("code","0");
        result.put("msg","success");
        result.put("data",list);
        logger.info("获取当天退住人脸数据end...");
        renderJson(result);
    }


    /**
     * 通知服务器人脸信息是否成功
     */
    public void notifyPersonFaceStatus(){
         logger.info("通知服务器人脸信息是否成功begin...");
        String appNo = getPara("appNo");
        String idsParms = getPara("ids");
        String status = getPara("status");
        logger.info("通知服务器人脸信息是否成功接收入参：应用编号：[{}]，身份证字符串：[{}]，成功状态：[{}]",appNo,idsParms,status);

        Map<String,Object> result = new HashMap<>();

        if(StringUtils.isBlank(idsParms)){
            result.put("code","10001");
            result.put("msg","人脸记录ID不能为空");
            renderJson(result);
            return;
        }

        if(StringUtils.isBlank(status)){
            result.put("code","10001");
            result.put("msg","人脸状态值为空");
            renderJson(result);
            return;
        }
        String[] ids=null;
        if(idsParms.indexOf(",")!=-1){
            ids=idsParms.split(",");
        }else{
            ids=new String[]{idsParms};
        }
        logger.info("人脸信息身份证处理结果数组：[{}]",JSON.toJSONString(ids));

        List<MmsFaceLiveUpload> list = Lists.newArrayList();
        if(ids != null && ids.length > 0) {
            for (String id : ids) {
                MmsFaceLiveUpload liveExist = mmsFaceLiveUploadService.findById(id);
                if (liveExist == null) {
                    result.put("code", "10001");
                    result.put("msg", "人脸记录不存在");
                    renderJson(result);
                    return;
                }

                MmsFaceLiveUpload live = new MmsFaceLiveUpload();
                live.setId(id);
                live.setStatus(status);
                list.add(live);
            }
        }
        logger.info("人脸记录待处理数据：[{}]",JSON.toJSONString(list));

        boolean flag = Db.tx(new IAtom() {
            @Override
            public boolean run() throws SQLException {
                try {
                    Db.batchUpdate(list,list.size());
                }catch (Exception e){
                    e.printStackTrace();
                    return false;
                }
                return true;
            }
        });

        if(flag){
            result.put("code","0");
            result.put("msg","success");
        }else{
            result.put("code","100001");
            result.put("msg","fail");
        }
        logger.info("通知服务器人脸信息是否成功返回数据：[{}]",JSON.toJSONString(result));
        logger.info("通知服务器人脸信息是否成功end...");
        renderJson(result);
    }



    /**
     * 上传人脸识别记录
     */
    public void faceRecognition(){
        logger.info("上传人脸识别记录begin...");
        MmsFaceRecognitionRecord faceRecord = getBean(MmsFaceRecognitionRecord.class,"",true);
        String imgBase64 = getPara("imgBase64");
        String time = getPara("time");
        Map<String,Object> result = new HashMap<>();

        if(faceRecord == null){
            result.put("code","10001");
            result.put("msg","人脸识别信息不能为空");
            renderJson(result);
            return;
        }
        logger.info("上传人脸识别记录接收入参：人脸记录对象：[应用编号：[{}]，设备标识码：[{}],会员ID：[{}]，识别类型：[{}]]，识别时间：[{}]",
                faceRecord.getAppNo(),faceRecord.getDeviceKey(),faceRecord.getMemberId(),faceRecord.getType(),time);

        if(StringUtils.isBlank(faceRecord.getAppNo())){
            result.put("code","10001");
            result.put("msg","应用编号不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(faceRecord.getBaseId())){
            result.put("code","10001");
            result.put("msg","基地ID不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(faceRecord.getMemberId())){//非会员不上传
            result.put("code","10001");
            result.put("msg","会员id不能为空");
            renderJson(result);
            return;
        }
        MmsMember member = mmsMemberService.get(faceRecord.getMemberId());
        if(member == null){
            result.put("code","10001");
            result.put("msg","系统不存在该会员信息");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(faceRecord.getDeviceType())){
            result.put("code","10001");
            result.put("msg","设备类型不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(faceRecord.getDeviceKey())){
            result.put("code","10001");
            result.put("msg","设备唯一标识码不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(faceRecord.getActionType())){
            result.put("code","10001");
            result.put("msg","动作类型不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(faceRecord.getType())){
            result.put("code","10001");
            result.put("msg","识别类型不存在");
            renderJson(result);
            return;
        }
        List<Dict> list = dictService.getListByTypeOnUse("face_recognition_record_type");
        if(list != null && list.size() > 0){
            int flag = 0;
            for (Dict item : list) {
                if(StringUtils.isNotBlank(item.getDictValue()) && faceRecord.getType().equals(item.getDictValue())){
                    flag = 1;
                }
            }
            if(flag == 0){
                result.put("code","10001");
                result.put("msg","系统不存在该识别类型");
                renderJson(result);
                return;
            }
        }

        if(StringUtils.isBlank(imgBase64)){
            result.put("code","10001");
            result.put("msg","识别图片base编码不存在");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(time)){
            result.put("code","10001");
            result.put("msg","识别时间不存在");
            renderJson(result);
            return;
        }
        //处理时间
        Date date = TimeUtils.isParseDate(time,"yyyy-MM-dd HH:mm:ss");
        if(date == null){
            result.put("code","10001");
            result.put("msg","识别时间格式错误");
            renderJson(result);
            return;
        }

        if(imgBase64.contains(",")){
            imgBase64 = imgBase64.substring(imgBase64.indexOf(",") + 1,imgBase64.length());
        }
        faceRecord.setImgBase64(imgBase64.getBytes());
        faceRecord.setTime(date);

        if(faceRecord.save()){
            result.put("code","0");
            result.put("msg","success");
        }else{
            result.put("code","10001");
            result.put("msg","上传人脸识别记录失败");
        }
        logger.info("上传人脸识别记录返回数据：[{}]",JSON.toJSONString(result));
        logger.info("上传人脸识别记录end...");
        renderJson(result);
    }




    /**
     * 通知取消预订忽略上传人脸到设备
     */
    public void ignoreCancelBookFace(){
        String appNo = getPara("appNo");
        String baseId = getPara("baseId");
        String bookNo = getPara("bookNo");
        Map<String,Object> result = new HashMap<>();

        if(StringUtils.isBlank(appNo) || StringUtils.isBlank(baseId) || StringUtils.isBlank(bookNo)){
            result.put("code","10001");
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }

        boolean flag = mmsFaceLiveUploadService.ignoreUpload(appNo,baseId,bookNo);
        if(flag){
            result.put("code","0");
            result.put("msg","success");
        }else{
            result.put("code","10001");
            result.put("msg","fail");
        }
        renderJson(result);
    }


    /**
     * 获取临时人脸
     */
    public void getTempFace(){
        MmsFaceLiveUpload fu = getBean(MmsFaceLiveUpload.class,"",true);

        Map<String,Object> result = new HashMap<>();

        if(StringUtils.isBlank(fu.getBookNo()) && StringUtils.isBlank(fu.getIdcard())){
            result.put("code","10002");
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }

        Record record = mmsFaceLiveUploadService.getFaceByBookNo(fu);
        if(record == null){
            result.put("code","10002");
            result.put("msg","人脸不存在");
            renderJson(result);
            return;
        }
        //处理字段为null的现象
        List<Record> list = Lists.newArrayList();
        list.add(record);
        MiniProgramUtils.dealRecordNull(list);

        result.put("code","0");
        result.put("msg","success");
        result.put("data",list.get(0));
        renderJson(result);
    }


    /**
     * 根据身份证临时获取人脸
     */
    public void getTempFaceByIdcard(){
        String idcard = getPara("idcard");

        Map<String,Object> result = new HashMap<>();
        if(StringUtils.isBlank(idcard)){
            result.put("code","10002");
            result.put("msg","参数缺失");
            renderJson(result);
            return;
        }

        Record reocrd = mmsMemberFaceService.getTempFaceByIdCard(idcard);
        if(reocrd == null){
            result.put("code","10002");
            result.put("msg","人脸不存在");
            renderJson(result);
            return;
        }
        //处理字段为null的现象
        List<Record> list = Lists.newArrayList();
        list.add(reocrd);
        MiniProgramUtils.dealRecordNull(list);

        if(list.get(0).getBytes("picture") != null){
            list.get(0).set("picture",new String(list.get(0).getBytes("picture")));
        }

        result.put("code","0");
        result.put("msg","success");
        result.put("data",list.get(0));
        renderJson(result);
    }




    /**
     * 测试：查询人脸上传信息
     */
    public void queryLiveUpload(){
        MmsFaceLiveUpload fu = getBean(MmsFaceLiveUpload.class,"",true);
        String date = getPara("date");
        Map<String,Object> result = new HashMap<>();

        List<MmsFaceLiveUpload> list = mmsFaceLiveUploadService.getQuery(fu,date);
        result.put("code","0");
        result.put("msg","success");
        result.put("data",list);
        renderJson(result);
    }
}
