package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsQuestionnaireQuestionOptionService;
import com.cszn.integrated.service.api.member.MmsQuestionnaireQuestionService;
import com.cszn.integrated.service.api.member.MmsQuestionnaireService;
import com.cszn.integrated.service.entity.member.MmsQuestionnaire;
import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestion;
import com.cszn.integrated.service.entity.member.MmsQuestionnaireQuestionOption;
import com.cszn.member.web.support.auth.AuthUtils;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.List;

@RequestMapping(value = "/mms/questionnaire",viewPath = "/modules_page/mms/questionnaire")
public class MmsQuestionnaireController extends BaseController {

    @Inject
    private MmsQuestionnaireService mmsQuestionnaireService;
    @Inject
    private MmsQuestionnaireQuestionService mmsQuestionnaireQuestionService;
    @Inject
    private MmsQuestionnaireQuestionOptionService mmsQuestionnaireQuestionOptionService;

    public void questionnaireIndex(){

        render("questionnaireIndex.html");
    }


    public void questionnaireForm(){
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MmsQuestionnaire questionnaire=mmsQuestionnaireService.findById(id);
            setAttr("questionnaire",questionnaire);
        }
        render("questionnaireForm.html");
    }

    public void questionnairePageList(){
        MmsQuestionnaire questionnaire=getBean(MmsQuestionnaire.class,"",false);

        Page<MmsQuestionnaire> page=mmsQuestionnaireService.pageList(getParaToInt("page"),getParaToInt("limit"),questionnaire);

        renderJson(new DataTable<MmsQuestionnaire>(page));
    }


    public void saveQuestionnaire(){
        MmsQuestionnaire questionnaire=getBean(MmsQuestionnaire.class,"",false);

        if(mmsQuestionnaireService.saveQuestionnaire(questionnaire, AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作成功"));
        }
    }

    public void delQuestionnaire(){
        String id=getPara("id");
        MmsQuestionnaire questionnaire= new MmsQuestionnaire();
        questionnaire.setId(id);
        questionnaire.setDelFlag("1");
        if(mmsQuestionnaireService.saveQuestionnaire(questionnaire,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }


    public void questionIndex(){
        String questionnaireId=getPara("questionnaireId");

        setAttr("questionnaireId",questionnaireId);
        render("questionIndex.html");
    }

    public void questionForm(){
        String id=getPara("id");
        String questionnaireId=getPara("questionnaireId");
        if(StrKit.notBlank(id)){
            MmsQuestionnaireQuestion question=mmsQuestionnaireQuestionService.findById(id);
            setAttr("question",question);
        }
        setAttr("questionnaireId",questionnaireId);
        render("questionForm.html");
    }

    public void questionPage(){
        MmsQuestionnaireQuestion question=getBean(MmsQuestionnaireQuestion.class,"", true);
        Page<MmsQuestionnaireQuestion> questionPage=mmsQuestionnaireQuestionService.pageList(getParaToInt("page"),getParaToInt("limit"),question);
        renderJson(new DataTable<MmsQuestionnaireQuestion>(questionPage));
    }

    public void saveQuestion(){
        MmsQuestionnaireQuestion question=getBean(MmsQuestionnaireQuestion.class,"",true);

        if(mmsQuestionnaireQuestionService.saveQuestion(question,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void delQuestion(){
        String id=getPara("id");
        MmsQuestionnaireQuestion question = new MmsQuestionnaireQuestion();
        question.setId(id);
        question.setDelFlag("1");
        if(mmsQuestionnaireQuestionService.saveQuestion(question,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void optionIndex(){
        String questionId=getPara("questionId");
        setAttr("questionId",questionId);
        render("optionIndex.html");
    }

    public void optionForm(){
        String questionId=getPara("questionId");
        String id=getPara("id");
        if(StrKit.notBlank(id)){
            MmsQuestionnaireQuestionOption option =mmsQuestionnaireQuestionOptionService.findById(id);
            setAttr("option",option);
        }
        setAttr("questionId",questionId);
        render("optionForm.html");
    }

    public void saveOption(){
        MmsQuestionnaireQuestionOption option=getBean(MmsQuestionnaireQuestionOption.class,"",true);

        if(mmsQuestionnaireQuestionOptionService.saveOption(option,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }

    public void optionPage(){
        MmsQuestionnaireQuestionOption option=getBean(MmsQuestionnaireQuestionOption.class,"",true);
        List<String> ids=new ArrayList<>();
        ids.add(option.getQuestionId());
        List<MmsQuestionnaireQuestionOption> list=mmsQuestionnaireQuestionOptionService.findOptionListByQuestionIds(ids);
        renderJson(new DataTable<MmsQuestionnaireQuestionOption>(list));
    }

    public void delOption(){
        String id=getPara("id");
        MmsQuestionnaireQuestionOption option=new MmsQuestionnaireQuestionOption();
        option.setId(id);
        option.setDelFlag("1");
        if(mmsQuestionnaireQuestionOptionService.saveOption(option,AuthUtils.getUserId())){
            renderJson(Ret.ok("msg","操作成功"));
        }else{
            renderJson(Ret.fail("msg","操作失败"));
        }
    }
}
