/**
 * 
 */
package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.crm.CrmCardRollTypeService;
import com.cszn.integrated.service.entity.crm.CrmCardRollType;
import com.cszn.integrated.service.entity.enums.RollType;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.Date;

/**
 * Created by LiangHuiLing on 2021年7月26日
 *
 * MmsCardRollTypeController
 */
@RequestMapping(value="/mms/cardRollType", viewPath="/modules_page/mms/cardRollType")
public class MmsCardRollTypeController extends BaseController {

	@Inject
    private CrmCardRollTypeService crmCardRollTypeService;
	
    public void index() {
        render("typeIndex.html");
    }
    
    /**
     * 分页表格方法
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	CrmCardRollType model = getBean(CrmCardRollType.class, "", true);
        Page<CrmCardRollType> page = crmCardRollTypeService.paginate(model, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<CrmCardRollType>(page));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	CrmCardRollType model = getBean(CrmCardRollType.class, "", true);
    	setAttr("model", model);
		setAttr("rollTypeCodes", RollType.values());
        render("typeForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        setAttr("model", crmCardRollTypeService.findById(getPara("id")));
        setAttr("rollTypeCodes", RollType.values());
        render("typeForm.html");
    }
    
    /**
     * 保存方法
     */
    public void save() {
    	CrmCardRollType model = getBean(CrmCardRollType.class, "", true);
    	boolean returnFlag = false;
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
    		model.setDelFlag(DelFlag.NORMAL);
    		model.setCreateBy(AuthUtils.getUserId());
    		model.setCreateTime(new Date());
    		returnFlag = model.save();
    	}else {
    		model.setUpdateBy(AuthUtils.getUserId());
    		model.setUpdateTime(new Date());
    		returnFlag = model.update();
    	}
    	if(returnFlag){
			renderJson(Ret.ok("msg", "操作成功!"));
		}else{
			renderJson(Ret.fail("msg", "操作失败！"));
		}
    }

	/**
	 * 删除方法
	 */
	public void delete(){
		CrmCardRollType model = getBean(CrmCardRollType.class, "", true);
		if(crmCardRollTypeService.update(model)){
			renderJson(Ret.ok("msg", "操作成功"));
		}else{
			renderJson(Ret.fail("msg", "操作失败"));
		}
	}
}
