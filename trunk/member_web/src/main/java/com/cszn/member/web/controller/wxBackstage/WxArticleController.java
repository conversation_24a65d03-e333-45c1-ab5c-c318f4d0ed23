package com.cszn.member.web.controller.wxBackstage;

import com.alibaba.fastjson.JSONArray;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsWxArticleService;
import com.cszn.integrated.service.entity.member.MmsWxArticle;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


/**
 * @Description 微信文章管理
 * <AUTHOR>
 * @Date 2019/4/17
 **/
@RequestMapping(value="/mms/wxarticle", viewPath="/modules_page/mms/wxArticle")
public class WxArticleController extends BaseController {

	@Inject
    private MmsWxArticleService mmsWxArticleService;



    /**
     * 跳转微信文章管理界面
      */
    public void index(){
        render("articleIndex.html");
    }


    /**
     * 查询微信文章列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){

        MmsWxArticle wxArticle = getBean(MmsWxArticle.class,"",true);
        Page<MmsWxArticle> page = mmsWxArticleService.findList(getParaToInt("page"),getParaToInt("limit"),wxArticle);
        renderJson(new DataTable<MmsWxArticle>(page));
    }


    /**
     * 微信文章删除
     */
    public void delete(){

        String id = getPara("id");
        boolean flag = mmsWxArticleService.delWxArticle(id,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }

    }


    /**
     * 跳转微信文章 新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MmsWxArticle wxArticle = mmsWxArticleService.get(id);
        if(wxArticle != null){
            if(StringUtils.isNotBlank(wxArticle.getCoverPic())){
                String coverPic = Db.queryStr("select src from cfs_upload_file where id = ?",wxArticle.getCoverPic());
                setAttr("coverPic",coverPic);
            }
        }
        setAttr("wxArticle",wxArticle);
        setAttr("commonUpload",Global.commonUpload);
        setAttr("imgPath", Global.fileUrlPrefix);
        render("articleForm.html");
    }


    /**
     * 微信文章保存
     */
    public void save(){
        MmsWxArticle wxArticle = getBean(MmsWxArticle.class,"wxArticle",true);
        boolean flag = mmsWxArticleService.saveWxArticle(wxArticle, AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg","保存成功"));
        }else{
            renderJson(Ret.fail("msg","保存失败"));
        }
    }


    /**
     * 批量删除
     */
    public void batchDel(){

        String articleData =  getPara("wxarticleData");
        List<MmsWxArticle> list = JSONArray.parseArray(articleData,MmsWxArticle.class);
        boolean flag = mmsWxArticleService.batchDelArticle(list,AuthUtils.getUserId());
        if(flag){
            renderJson(Ret.ok("msg", "批量作废成功"));
        }else{
            renderJson(Ret.fail("msg", "批量作废失败"));
        }
    }
}
