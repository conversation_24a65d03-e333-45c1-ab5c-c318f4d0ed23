package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.BioApiService;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

/**
 * @Description 手环终端定位数据
 * <AUTHOR>
 * @Date 2019/8/19
 **/
@RequestMapping(value="/mms/bioPositionRecord", viewPath="/modules_page/mms/bioPositionRecord")
public class BioPositionRecordController extends BaseController {

    @Inject
    private BioApiService bioApiService;


    public void index(){
        render("bioPositionRecordIndex.html");
    }



    /**
     * 手环日常定位数据
     */
    @Clear(LogInterceptor.class)
    public void findListPageByLocation(){
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        Page<Record> page = bioApiService.findListPage(getParaToInt("page"), getParaToInt("limit"),fullName,idcard);
        renderJson(new DataTable<Record>(page));
    }




    /**
     * 手环报警定位数据
     */
    @Clear(LogInterceptor.class)
    public void findListPageBySos(){
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        Page<Record> page = bioApiService.findList(getParaToInt("page"), getParaToInt("limit"),fullName,idcard);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 删除
     */
    public void delete(){
        String id = getPara("id");
        String type = getPara("type");
        boolean flag = bioApiService.delLocation(id,type, AuthUtils.getUserId());
        if (flag) {
            renderJson(Ret.ok("msg", "作废成功"));
        } else {
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


    /**
     * 跳转定位地图
     */
    @Clear(LogInterceptor.class)
    public void bioMap(){
        String lng = getPara("lng");
        String lat = getPara("lat");
        setAttr("lng",lng);
        setAttr("lat",lat);
        render("bioMap.html");
    }
}
