package com.cszn.member.web.support.auth;

import org.apache.shiro.SecurityUtils;

import com.cszn.integrated.base.common.Consts;
import com.cszn.integrated.base.plugin.shiro.ShiroUtils;
import com.cszn.integrated.service.entity.sys.User;

/**
 * 授权认证工具类
 */
public class AuthUtils {

    /**
     * 是否登录
     * @return
     */
    public static boolean isLogin() {
        return ShiroUtils.isAuthenticated();
    }
    
    /**
     * 获取用户机构ID
     * @return
     */
    public static String getOrgId(){
    	if(getLoginUser()!=null){
    		return getLoginUser().getOrgId();
    	}else{
    		return null;
    	}
    }
    
    /**
     * 获取用户ID
     * @return
     */
    public static String getUserId(){
    	if(getLoginUser()!=null){
    		return getLoginUser().getId();
    	}else{
    		return null;
    	}
    }
    
    /**
     * 获取用户姓名
     * @return
     */
    public static String getUserFullName(){
    	if(getLoginUser()!=null){
    		return getLoginUser().getName();
    	}else{
    		return null;
    	}
    }

    /**
     * 获取平台登录用户
     * @return
     */
    public static User getLoginUser() {
        User user = new User();
        if (ShiroUtils.isAuthenticated()) {
            user = (User) SecurityUtils.getSubject().getSession().getAttribute(Consts.SESSION_USER);
        }
        return user;
    }

    /**
     * 校验用户登录密码
     * @param newPwd 新未加密的密码
     * @param oldPwd 旧加密后的密码
     * @param oldSalt2 旧加密盐
     * @return true-校验一致 否则 false
     */
    public static boolean checkPwd(String newPwd, String oldPwd, String oldSalt2) {
        return ShiroUtils.checkPwd(newPwd, oldPwd, oldSalt2);
    }
}
