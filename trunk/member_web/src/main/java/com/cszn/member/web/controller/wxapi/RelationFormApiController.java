package com.cszn.member.web.controller.wxapi;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.crm.CrmRelationFormService;
import com.cszn.integrated.service.entity.crm.CrmRelationForm;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.member.web.support.log.LogInterceptor;
import com.cszn.member.web.validator.CorsInterceptor;
import com.jfinal.aop.Before;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Page;

import io.jboot.db.model.Columns;
import io.jboot.web.controller.annotation.RequestMapping;

@JCors
@RequestMapping(value = "/relationFormApi",viewPath = "")
@Clear(LogInterceptor.class)
public class RelationFormApiController extends BaseController {

	@Inject
    private CrmRelationFormService crmRelationFormService;

    private static Logger logger = LoggerFactory.getLogger(RelationFormApiController.class);

    @Clear(LogInterceptor.class)
    public void relationFormPage(){
    	CrmRelationForm model = getBean(CrmRelationForm.class, "", true);
    	Columns columns = Columns.create("del_flag", DelFlag.NORMAL);
    	if (StrKit.notBlank(model.getRelationId())) {
			columns.eq("relation_id", model.getRelationId());
		}
    	if (StrKit.notBlank(model.getRelationType())) {
    		columns.eq("relation_type", model.getRelationType());
    	}
    	if (StrKit.notBlank(model.getRelationBy())) {
    		columns.eq("relation_by", model.getRelationBy());
    	}
    	if (StrKit.notBlank(model.getFormId())) {
    		columns.eq("form_id", model.getFormId());
    	}
    	if (StrKit.notBlank(model.getFormNo())) {
    		columns.eq("form_no", model.getFormNo());
    	}
		Page<CrmRelationForm> modelPage = crmRelationFormService.paginateByColumns(getParaToInt("page", 1), getParaToInt("limit", 10), columns, "create_time desc");
		renderCodeSuccess("操作成功", new DataTable<CrmRelationForm>(modelPage));
    }
    

    @Clear(LogInterceptor.class)
    public void relationFormList(){
    	CrmRelationForm model = getBean(CrmRelationForm.class, "", true);
    	final List<CrmRelationForm> formList = crmRelationFormService.findList(model);
    	renderCodeSuccess("操作成功", formList);
    }

    @JCors
	@Before(CorsInterceptor.class)
    @Clear(LogInterceptor.class)
    public void relationFormSave(){
    	CrmRelationForm model = getBean(CrmRelationForm.class, "", true);
    	//新增
    	if(StrKit.isBlank(model.getId())) {
    		model.setId(IdGen.getUUID());
			model.setDelFlag(DelFlag.NORMAL);
			model.setCreateTime(new Date());
			model.setUpdateTime(new Date());
    	}else {//修改
    		model.setUpdateTime(new Date());
    	}
    	if(crmRelationFormService.saveOrUpdate(model)!=null) {    		
    		renderCodeSuccess("操作成功");
    	}else {
    		renderCodeFailed("操作失败");
    	}
    }
}
