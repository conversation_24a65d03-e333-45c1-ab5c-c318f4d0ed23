package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainFunctionSwitchService;
import com.cszn.integrated.service.api.member.BioApiService;
import com.cszn.integrated.service.api.member.MmsActivityMemberService;
import com.cszn.integrated.service.api.member.MmsMemberPrizeService;
import com.cszn.integrated.service.api.member.MmsPrizeService;
import com.cszn.integrated.service.api.sms.SmsSendRecordService;
import com.cszn.integrated.service.entity.enums.FunctionSwitch;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.enums.SwitchType;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.member.MmsActivityMember;
import com.cszn.integrated.service.entity.member.MmsCustomerCode;
import com.cszn.integrated.service.entity.member.MmsMemberPrize;
import com.cszn.integrated.service.entity.member.MmsPrize;
import com.cszn.integrated.service.entity.send.PrizeSend;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.member.web.support.log.LogInterceptor;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 营销活动Controller
 * <AUTHOR>
 * @Date 2019/7/8
 **/
@JCors
@RequestMapping(value="/marketingapi", viewPath="")
public class MarketingController extends BaseController{

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private MmsActivityMemberService mmsActivityMemberService;

    @Inject
    private MmsPrizeService mmsPrizeService;

    @Inject
    private MmsMemberPrizeService mmsMemberPrizeService;

    @Inject
    private FinaMembershipCardService finaMembershipCardService;

    @Inject
    private SmsSendRecordService smsSendRecordService;

    @Inject
    private MainFunctionSwitchService mainFunctionSwitchService;

    @Inject
    private BioApiService bioApiService;


    /*公众号H5吸粉赠送礼物begin*/

    /**
     * 发送短信
     * 1.生成验证码，调用旅居发送短信接口
     * 验证码需要设置有效时间吗？暂未设置
     * 暂用数据库存储
     */
    @Clear(LogInterceptor.class)
    public void sendMsg(){
        logger.info("发送短信验证码begin...");

        String telephone = getPara("telephone");
        String verifyCode = IdGen.getVerifyCode();

        Map<String, Object> result = new HashMap<String, Object>();
        String swicthStr = mainFunctionSwitchService.getSwitch(FunctionSwitch.h5Gift.getKey());
        //短信验证码开关
        if(SwitchType.close.getKey().equals(swicthStr)){
            result.put("code", "10001");
            result.put("msg", "系统维护中...");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(telephone)){
            result.put("code", "10001");
            result.put("msg", "手机号码不能为空");
            renderJson(result);
            return;
        }

        JSONObject json = new JSONObject();
        //json.put("userId","86832f08966a44abacbcfa840f97fd0a");
        json.put("tempId",Global.msgTemplate);
        json.put("mobile",telephone);
        Map<String,Object> params = new HashMap<>();
        params.put(Global.templateKeyWord,verifyCode);
        String parmTT = JSON.toJSONString(params);
        json.put("data",parmTT);
        String jsonStr = JSON.toJSONString(json);
        logger.info("旅居发送短信接口入参："+ jsonStr);

        String content = HttpClientsUtils.httpPostRaw(Global.sendMsgUrl,jsonStr,null,null);
        logger.info("旅居发送短信接口响应内容：" + content);
        if(StringUtils.isBlank(content)){
            result.put("code", "10001");
            result.put("msg", "验证码发送失败");
            renderJson(result);
            return;
        }
        JSONObject jsonObject = JSONObject.parseObject(content);
        if(jsonObject == null ||jsonObject.getInteger("Type") == null|| jsonObject.getInteger("Type") != 1){
            result.put("code", "10001");
            result.put("msg", "验证码发送失败");
            renderJson(result);
            return;
        }
        //暂存数据库
        MmsCustomerCode code = new MmsCustomerCode();
        code.setId(IdGen.getUUID());
        code.setPhone(telephone);
        code.setVerifyCode(verifyCode);
        code.setCreateTime(new Date());
        if(code.save()){
            result.put("code", "0");
            result.put("msg", "发送成功！");
        }else{
            result.put("code", "0");
            result.put("msg", "验证码发送失败");
            logger.info("验证码存库失败！");
        }
        logger.info("发送短信验证码end...");
        renderJson(result);
    }


    /**
     * 保存分公司潜在客户
     * 1.所有字段必须填写
     * 2.保存时判断是否关注公众号,暂不判断（未关注:跳转关注??? ；已关注：直接保存）
     * 问题：1.后台如何获取验证码  2.如何判断是否关注了公众号
     */
    @Clear(LogInterceptor.class)
    public void savePotentialCustomers(){
        logger.info("保存分公司潜在客户begin...");
        MmsActivityMember act = getBean(MmsActivityMember.class,"",true);
        String verifyCode = getPara("verifyCode");//验证码
        logger.info("保存分公司潜在客户接收入参：二维码会员信息：[{}]，验证码：[{}]",JSON.toJSONString(act),verifyCode);
        Map<String, Object> result = new HashMap<String, Object>();

        if(StringUtils.isBlank(act.getQrcode())){
            result.put("code", "10001");
            result.put("msg", "二维码标识不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(act.getName())){
            result.put("code", "10001");
            result.put("msg", "姓名不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(act.getTelephone())){
            result.put("code", "10001");
            result.put("msg", "手机号码不能为空");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(verifyCode)){
            result.put("code", "10001");
            result.put("msg", "验证码不能为空");
            renderJson(result);
            return;
        }
        //验证码校验
        String verifyCodeLocal = Db.queryStr("select verify_code from mms_customer_code where phone = ? order by create_time desc limit 1",act.getTelephone());
        if(StringUtils.isBlank(verifyCodeLocal) || !verifyCode.equals(verifyCodeLocal)){
            result.put("code", "10001");
            result.put("msg", "验证码输入错误");
            renderJson(result);
            return;
        }
        //二维码标识系统内必存在
        Long count = Db.queryLong("select count(*) from mms_activity_qrcode where del_flag = 0 and qrcode = ?",act.getQrcode());
        if(count <= 0){
            result.put("code", "10001");
            result.put("msg", "二维码无效");
            renderJson(result);
            return;
        }
        //根据手机号码去重
        List<MmsActivityMember> list = mmsActivityMemberService.getActByPhone(act.getTelephone());
        if(list != null && list.size() > 0){
            result.put("code", "10001");
            result.put("msg", "您已经领取过啦~");
            renderJson(result);
            return;
        }

        act.setId(IdGen.getUUID());
        act.setPrizeFlag("0");
        act.setCreateTime(new Date());
        if(act.save()){
            result.put("code", "0");
            result.put("msg", "保存成功！");
        }else{
            result.put("code", "10001");
            result.put("msg", "保存失败！");
        }
        logger.info("保存分公司潜在客户end...");
        renderJson(result);
    }

    /*公众号H5吸粉赠送礼物end*/



    /*双十一营销活动begin*/

    /**
     * 获取所有奖品
     * 展示奖品
     */
    @Clear(LogInterceptor.class)
    public void getPrizes(){

        Map<String, Object> result = new HashMap<String, Object>();
        List<MmsPrize> list = mmsPrizeService.getPrizes();
        if(list == null){
            list = Lists.newArrayList();
        }
        result.put("code", "0");
        result.put("msg", "获取成功");
        result.put("data",list);
        renderJson(result);
    }


    /**
     * 获取中奖的会员
     * 轮播
     */
    @Clear(LogInterceptor.class)
    public void getPrizeMembers(){

        Map<String, Object> result = new HashMap<String, Object>();
        List<Record> list = mmsMemberPrizeService.getMemberPrizes();
        if(list == null){
            list = Lists.newArrayList();
        }
        if(list != null && list.size() > 0){
            for (Record item : list) {
                String telephone  = item.getStr("telephone");
                if(StringUtils.isNotBlank(telephone)){
                    telephone = telephone.replaceAll("(\\d{3})\\d{4}(\\d{4})","$1****$2");
                    item.set("telephone",telephone);
                }
            }
        }
        result.put("code", "0");
        result.put("msg", "获取成功");
        result.put("data",list);
        renderJson(result);
    }


    /**
     * 保存中奖会员信息
     */
    @Clear(LogInterceptor.class)
    public void winPrizeByCardNumber(){
        String cardNumber = getPara("cardNumber");
        String fullName = getPara("fullName");
        String telephone = getPara("telephone");
        Map<String, Object> result = new HashMap<String, Object>();

        String switchStr = mainFunctionSwitchService.getSwitch(FunctionSwitch.doubleEle.getKey());
        if(SwitchType.close.getKey().equals(switchStr)){
            result.put("code", "10001");
            result.put("msg", "双十一活动已结束，敬请期待下次活动!");
            renderJson(result);
            return;
        }

        if(StringUtils.isBlank(cardNumber)){
            result.put("code", "10001");
            result.put("msg", "未输入会员卡");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(fullName)){
            result.put("code", "10001");
            result.put("msg", "未输入姓名");
            renderJson(result);
            return;
        }
        if(StringUtils.isBlank(telephone)){
            result.put("code", "10001");
            result.put("msg", "未输入手机号码");
            renderJson(result);
            return;
        }

        FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);
        if(card == null){
            result.put("code", "10001");
            result.put("msg", "会员卡不存在，无法参与");
            renderJson(result);
            return;
        }

        //同一卡号不能中多次奖
        Long count = Db.queryLong("select count(*) from mms_member_prize where del_flag = 0 and card_number = ?",cardNumber);
        if(count >= 1){
            result.put("code", "10001");
            result.put("msg", "该卡号已参与过活动");
            renderJson(result);
            return;
        }

        //随机中奖
        int num = (int)(Math.random()*10);
        MmsPrize prize = mmsPrizeService.getPrizeByRan(num);
        if(prize == null){
            result.put("code", "10001");
            result.put("msg", "参与失败，请重新再试！");
            renderJson(result);
            return;
        }

        MmsMemberPrize mp = new MmsMemberPrize();
        mp.setId(IdGen.getUUID());
        mp.setCardNumber(cardNumber);
        mp.setFullName(fullName);
        mp.setTelephone(telephone);
        mp.setPrizeId(prize.getId());
        mp.setIsReceived("0");
        mp.setDelFlag("0");
        mp.setCreateTime(new Date());
        mp.setUpdateTime(new Date());
        if(mp.save()){
            result.put("code", "0");
            result.put("msg", "中奖成功");
            result.put("data", prize);
            //发送短信
            PrizeSend ps = new PrizeSend(mp.getFullName(),"",mp.getCardNumber(),Global.sendMsgCompanyName,prize.getPrize());
            smsSendRecordService.sendMsgByPrize(SendType.prizeSend,mp.getCardNumber(),mp.getTelephone(),JSON.toJSONString(ps),null);
        }else{
            result.put("code", "10001");
            result.put("msg", "参与失败，请重新再试！");
        }
        renderJson(result);

    }
    /*双十一营销活动end*/




    @Clear(LogInterceptor.class)
    public void testBioData(){
        String str = getPara("str");
        Map<String, Object> result = new HashMap<String, Object>();
        String mgs = bioApiService.dealBioData(str);
        result.put("code", "0");
        result.put("msg", "保存成功！");
        result.put("data", mgs);
        renderJson(result);

    }
}
