package com.cszn.member.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.FaceUtils;
import com.cszn.integrated.base.utils.TimeUtils;
import com.cszn.integrated.service.api.crm.CrmCardRollRecordService;
import com.cszn.integrated.service.api.main.MainFunctionSwitchService;
import com.cszn.integrated.service.api.member.MmsMemberFaceService;
import com.cszn.integrated.service.entity.crm.CrmCardRollRecord;
import com.cszn.integrated.service.entity.enums.FunctionSwitch;
import com.cszn.integrated.service.entity.enums.SwitchType;
import com.cszn.integrated.service.entity.member.MmsMemberFace;
import com.cszn.integrated.service.entity.status.Global;
import com.google.common.collect.Lists;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.IAtom;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.components.schedule.annotation.Cron;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

//@Cron("*/4 * * * *")
public class FaceTask implements Runnable{

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    @Inject
    private MmsMemberFaceService mmsMemberFaceService;

    @Inject
    private MainFunctionSwitchService mainFunctionSwitchService;
    @Inject
    private CrmCardRollRecordService crmCardRollRecordService;



    @Override
    public void run() {
        String swicthStr = mainFunctionSwitchService.getSwitch(FunctionSwitch.faceCompliance.getKey());
        try {
            if(SwitchType.open.getKey().equals(swicthStr)) {
                faceComplianceCron();
            }else{
                logger.info("人脸检测接口已关闭...");
            }
        } catch (Exception e) {
            //logger.error("人脸检测定时任务异常:[{}]",e);
        }

        try {
            List<Record> cardRollList2 = Db.find("select * from crm_card_roll where del_flag='0' and is_enable='0' and valid_type='2' and deploy_status='0'");
            Date now=new Date();
            if(cardRollList2!=null && cardRollList2.size()>0) {
                for(Record record:cardRollList2){
                    List<CrmCardRollRecord> rollRecordList=crmCardRollRecordService.findEnableListByRollId(record.getStr("id"));

                    for(CrmCardRollRecord rollRecord:rollRecordList){
                        Record bindingRecord=rollRecord.getBindingInfo();
                        if(bindingRecord!=null && record.getInt("valid_days")!=null){
                            Date bindingTime=bindingRecord.getDate("bindingTime");
                            if(bindingTime!=null){
                                Calendar calendar=Calendar.getInstance();
                                calendar.setTime(bindingTime);
                                calendar.add(Calendar.DATE,record.getInt("valid_days"));
                                if(DateUtils.compareDays(now,calendar.getTime())>=0){
                                    rollRecord.setIsEnable("1");
                                    rollRecord.setUpdateTime(new Date());
                                    rollRecord.setExpirationTime(calendar.getTime());
                                    rollRecord.update();
                                }
                            }
                        }
                    }
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }

    }



    /**
     * 处理未审核(即审核失败，接口自身原因)的人脸
     */
    public void faceComplianceCron(){
        logger.info("处理未审核人脸begin...[{}]", TimeUtils.getStrDateByPattern(new Date(),"yyyy-MM-dd HH:mm:ss"));
        List<MmsMemberFace> list = mmsMemberFaceService.getListByVerifyFlag("0");
        logger.info("获取的未审核人脸数据数量：[{}]",list.size());
        List<MmsMemberFace> updateList = Lists.newArrayList();
        if(list != null && list.size() > 0){
            for (MmsMemberFace item : list) {
                Map<String,Object> map = FaceUtils.faceCompliance(Global.faceComplianceUrl,new String(item.getPicture()),"1");
                if(map.containsKey("error")) continue;

                if((Boolean)map.get("flag") == false){
                    item.setVerifyFlag("2");
                }else{
                    item.setVerifyFlag("1");
                }
                item.setVerifyDescribe((String)map.get("msg"));
                updateList.add(item);
            }
            ValueFilter valueFilter=new ValueFilter() {
                @Override
                public Object process(Object object, String name, Object value) {
                    if("picture".equals(name)){
                        return null;
                    }
                    return value;
                }
            };
            logger.info("成功被修改的人脸数据:[{}]", JSON.toJSONString(updateList,valueFilter));

            boolean flag = Db.tx(new IAtom(){
                public boolean run() throws SQLException {
                    try {
                        if(updateList != null && updateList.size() > 0) {
                            int[] ints = Db.batchUpdate(updateList, updateList.size());
                            for (int i : ints) {
                                if (i < 1) {
                                    return false;
                                }
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                        return false;
                    }
                    return true;
                }
            });
            logger.info("处理未审核人脸end...[{}],处理结果:[{}]", TimeUtils.getStrDateByPattern(new Date(),"yyyy-MM-dd HH:mm:ss"),flag);
        }
    }
}
