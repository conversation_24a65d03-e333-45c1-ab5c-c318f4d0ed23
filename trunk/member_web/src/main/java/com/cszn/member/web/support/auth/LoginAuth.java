package com.cszn.member.web.support.auth;

import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.api.sys.RoleService;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.status.UserType;
import com.cszn.integrated.service.entity.sys.Role;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.SimpleAuthenticationInfo;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.subject.PrincipalCollection;
import org.apache.shiro.util.ByteSource;

import com.alibaba.fastjson.JSON;
import com.cszn.integrated.base.plugin.shiro.auth.MuitiAuthenticatied;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.service.api.sys.UserService;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.status.LoginFlag;
import com.cszn.integrated.service.entity.sys.User;
import com.jfinal.kit.Ret;

import io.jboot.Jboot;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 管理端认证授权
 * <AUTHOR>
 *
 */
public class LoginAuth implements MuitiAuthenticatied {

    @Override
    public boolean hasToken(AuthenticationToken authenticationToken) {
    	String loginName = authenticationToken.getPrincipal().toString();
        UserService sysUserApi = Jboot.service(UserService.class);
        User sysUser = sysUserApi.getByUserName(loginName);
        return sysUser!=null?true:false;
    }

    @Override
    public boolean wasLocked(AuthenticationToken authenticationToken) {
    	boolean resultFlag = false;
    	String loginName = authenticationToken.getPrincipal().toString();
        UserService sysUserApi = Jboot.service(UserService.class);
        User sysUser = sysUserApi.getByUserName(loginName);
        if(sysUser!=null && sysUser.getLoginFlag().equals(LoginFlag.LOCKED)){
        	resultFlag = true;
        }
        return resultFlag;
    }

    @Override
    public AuthenticationInfo buildAuthenticationInfo(AuthenticationToken authenticationToken) {
    	UsernamePasswordToken token= (UsernamePasswordToken) authenticationToken;
    	final String userName = token.getUsername();
        final String password = String.valueOf(token.getPassword());

        Map<String,String> params=new HashMap<>();
		params.put("userName", userName);
		params.put("password", password);
		final String resultStr = HttpClientsUtils.httpPostForm(Global.orgUrl+"/api/login",params,null,null);
		Ret returnRet = JSON.parseObject(resultStr, Ret.class);
		System.out.println("returnRet="+returnRet);
		final String returnMsg = returnRet.getStr("msg");
		
		SimpleAuthenticationInfo info = null;
		if(returnRet.isOk()) {
//			final String userId = returnRet.getStr("id");
//        	final String userType = new String(Base64.getDecoder().decode(returnRet.getStr("userType")), Charset.forName("UTF-8"));
        	final String uName = new String(Base64.getDecoder().decode(returnRet.getStr("userName")), Charset.forName("UTF-8"));
        	final String pwd = new String(Base64.getDecoder().decode(returnRet.getStr("password")), Charset.forName("UTF-8"));
        	final String salt = new String(Base64.getDecoder().decode(returnRet.getStr("salt")), Charset.forName("UTF-8"));
//        	final String name = new String(Base64.getDecoder().decode(returnRet.getStr("name")), Charset.forName("UTF-8"));
        	
        	info = new SimpleAuthenticationInfo(uName, pwd, ByteSource.Util.bytes(salt), "ShiroDbRealm");
		}else{
			info = new SimpleAuthenticationInfo();
			if(returnMsg.contains("不正确")){
				throw new IncorrectCredentialsException(returnMsg);
			}else if(returnMsg.contains("冻结")){
				throw new LockedAccountException(returnMsg);
			}
		}
		return info;
    }

    @Override
    public AuthorizationInfo buildAuthorizationInfo(PrincipalCollection principals) {
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        String loginName = (String) principals.fromRealm("ShiroDbRealm").iterator().next();
        MenuService menuService = Jboot.service(MenuService.class);
        //如果登录的用户是system_admin类型的用户，则将所有的权限加上
        if(AuthUtils.getLoginUser().getUserType().equals(UserType.SYSTEM_ADMIN)){
            Set<String> allPermissions=menuService.findSystemAllMenu(SystemType.MEMBER);
            info.addStringPermissions(allPermissions);
            return info;
        }
        RoleService sysRoleApi = Jboot.service(RoleService.class);
        List<Role> sysRoleList = sysRoleApi.findRoleByName(loginName,SystemType.MEMBER);
        List<String> roleNameList = new ArrayList<>();
        for (Role sysRole : sysRoleList) {
            roleNameList.add(sysRole.getRoleName());
        }
        Set<String> permissions=menuService.findPermissionByRole(sysRoleList);
        if(roleNameList!=null && roleNameList.size()>0) {
        	info.addRoles(roleNameList);
        }
        if(permissions!=null && !permissions.isEmpty()) {
        	info.addStringPermissions(permissions);
        }
        return info;
    }
}
