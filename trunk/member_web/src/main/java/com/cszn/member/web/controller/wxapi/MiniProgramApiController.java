package com.cszn.member.web.controller.wxapi;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.cszn.integrated.base.common.RetApi;
import com.cszn.integrated.base.interceptor.JCors;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.utils.DateUtils;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.base.utils.MiniProgramUtils;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.crm.CrmCardRollRecordService;
import com.cszn.integrated.service.api.crm.CrmCardRollRecordUseService;
import com.cszn.integrated.service.api.crm.CrmCardRollService;
import com.cszn.integrated.service.api.crm.CrmCardRollTypeService;
import com.cszn.integrated.service.api.fina.FinaCardRollService;
import com.cszn.integrated.service.api.fina.FinaCardTransactionsService;
import com.cszn.integrated.service.api.fina.FinaMembershipCardService;
import com.cszn.integrated.service.api.main.MainMembershipCardTypeService;
import com.cszn.integrated.service.api.member.*;
import com.cszn.integrated.service.api.sys.DictService;
import com.cszn.integrated.service.api.weixin.WeiXinActivityService;
import com.cszn.integrated.service.entity.crm.*;
import com.cszn.integrated.service.entity.fina.FinaCardRoll;
import com.cszn.integrated.service.entity.fina.FinaMembershipCard;
import com.cszn.integrated.service.entity.main.MainMembershipCardType;
import com.cszn.integrated.service.entity.member.*;
import com.cszn.integrated.service.entity.status.DelFlag;
import com.cszn.integrated.service.entity.status.Global;
import com.cszn.integrated.service.entity.sys.Dict;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import com.jfinal.upload.UploadFile;
import com.xiaoleilu.hutool.date.DateUtil;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.crypto.SecureRandomNumberGenerator;
import org.apache.shiro.crypto.hash.SimpleHash;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小程序接口
 */
@RequestMapping(value="/mpapi", viewPath="")
@JCors
@Clear(LogInterceptor.class)
public class MiniProgramApiController extends BaseController {

	private static Logger logger = LoggerFactory.getLogger(MiniProgramApiController.class);

	@Inject
	private MmsWxUserService mmsWxUserService;

	@Inject
	private MmsMemberService mmsMemberService;

	@Inject
	private FinaMembershipCardService finaMembershipCardService;

	@Inject
	private MmsWxArticleService mmsWxArticleService;

	@Inject
	private MmsWxMomentsService mmsWxMomentsService;

	@Inject
	private MmsWxRepliesService mmsWxRepliesService;

	@Inject
	private MmsWxThumbsupService mmsWxThumbsupService;

	@Inject
	private MmsWxSubscriptionsService mmsWxSubscriptionsService;

	@Inject
	private FinaCardTransactionsService finaCardTransactionsService;

	@Inject
	private MmsWxBookPersonService mmsWxBookPersonService;

	@Inject
	private MmsMemberFaceService mmsMemberFaceService;

	@Inject
	private DictService dictService;

	@Inject
	private MmsMeasureStepsService mmsMeasureStepsService;

	@Inject
	private MmsMeasureHrService mmsMeasureHrService;

	@Inject
	private MmsMeasureBpService mmsMeasureBpService;

	@Inject
	private MmsWxUserCardRelaService mmsWxUserCardRelaService;
	@Inject
	private MmsBlackListService mmsBlackListService;
	@Inject
	private CrmCardRollRecordService crmCardRollRecordService;
	@Inject
	private CrmCardRollService crmCardRollService;
	@Inject
	private FinaCardRollService finaCardRollService;
	@Inject
	private WeiXinActivityService weiXinActivityService;
	@Inject
	private CrmCardRollTypeService crmCardRollTypeService;
	@Inject
	private MainMembershipCardTypeService mainMembershipCardTypeService;
	@Inject
	private CrmCardRollRecordUseService crmCardRollRecordUseService;



	/**
	 * 微信用户登录
	 */
	public void weixinLogin() {
		logger.info("微信用户登录begin...");
		String code = getPara("code");
		logger.info("微信登录code：[{}]",code);
		JSONObject json = MiniProgramUtils.code2Session(Global.mpAppid, Global.mpAppSecret, code);
		logger.info("微信小程序登录返回数据(第三方)：[{}]",JSON.toJSONString(json));
		Map<String, Object> result = new HashMap<String, Object>();
		if (json.containsKey("openid")) {
			String unionid = json.getString("unionid");
			String openid = json.getString("openid");
			MmsWxUser wxUser = mmsWxUserService.getWxUserByOpenid(openid);
			Boolean suc = false;
			if (null == wxUser) {
				wxUser = new MmsWxUser();
				String memberId = IdGen.getUUID();
				wxUser.setId(memberId);
				wxUser.setUnionid(unionid);
				wxUser.setOpenid(openid);
				wxUser.setRegTime(DateUtil.date());
				suc = wxUser.save();
			} else {
				if(StrKit.isBlank(wxUser.getUnionid())){
					logger.info("微信小程序登录unionid为空："+JSON.toJSONString(json));
					wxUser.setUnionid(unionid);
					boolean flag=wxUser.update();
					logger.info("更新结果："+flag+"___"+unionid);
				}
				suc = true;
			}
			if (suc) {
				result.put("code", "0");
				result.put("msg", "登录成功");
				Map<String, Object> data = new HashMap<String, Object>();
				data.put("memberId", wxUser.getId());
				data.put("openid", wxUser.getOpenid());
				data.put("unionid", wxUser.getUnionid());
				result.put("data", data);
			} else {
				result.put("code", "10001");
				result.put("msg", "登录失败");
			}
		} else {
			result.put("code", "10001");
			result.put("msg", "登录失败");
		}
		logger.info("微信用户登录返回数据：[{}]",JSON.toJSONString(result));
		logger.info("微信用户登录end...");
		renderJson(result);
	}



	/**
	 * 保存微信用户信息
	 */
	public void saveWxUser() {
		logger.info("保存微信用户信息begin...");
		String id = getPara("id");
		String nickName = getPara("nickName");
		String avatarUrl = getPara("avatarUrl");
		String gender = getPara("gender");
		String country = getPara("country");
		String province = getPara("province");
		String city = getPara("city");
		Integer subscribe = getParaToInt("subscribe");
		String subscribeTime = getPara("subscribeTime");
		String language = getPara("language");

		
		MmsWxUser wxUser = mmsWxUserService.findById(id);
		Boolean suc = false;
		if (null != wxUser) {
			wxUser.setNickName(nickName);
			wxUser.setAvatarUrl(avatarUrl);
			wxUser.setGender(gender);
			wxUser.setCountry(country);
			wxUser.setProvince(province);
			wxUser.setCity(city);
			wxUser.setSubscribe(subscribe);
			wxUser.setSubscribeTime(subscribeTime);
			wxUser.setLanguage(language);
			suc = wxUser.update();
		}
		logger.info("修改微信用户成功标识：[{}]",suc);
		Map<String, Object> result = new HashMap<String, Object>();
		boolean flag=false;
		if(suc){
			flag=mmsWxUserService.CheckMember(wxUser.getId());
		}
		logger.info("旅居同步成功标识：[{}]",flag);
		if (suc && flag) {
			result.put("code", "0");
			result.put("msg", "保存成功");
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("id", wxUser.getId());
			data.put("openid", wxUser.getOpenid());
			data.put("unionid", wxUser.getUnionid());
			result.put("data", data);
		}else if(suc && !flag){
			result.put("code", "10001");
			result.put("msg", "旅居同步信息失败");
		}else {
			result.put("code", "10001");
			result.put("msg", "保存失败");
		}
		logger.info("保存微信用户信息返回数据：[{}]",JSON.toJSONString(result));
		logger.info("保存微信用户信息end...");
		renderJson(result);
	}
	
	/**
	 * 绑定会员卡
	 */
	public void bindUserCard() {
		logger.info("绑定会员卡begin...");
		String userId = getPara("userId");
		String cardNumber = getPara("cardNumber");
		String name = getPara("name");
		String idcard = getPara("idcard");
		logger.info("用户ID：[{}]，会员卡号：[{}]，姓名：[{}]，身份证：[{}]，",userId,cardNumber,name,idcard);
		
		Map<String, Object> result = new HashMap<String, Object>();

		MmsWxUser wxUser = mmsWxUserService.findById(userId);
		logger.info("用户ID对应微信用户：[{}]",JSON.toJSONString(wxUser));
		if (null == wxUser) {
			result.put("code", "10001");
			result.put("msg", "微信用户不存在，绑定失败，请重新登录小程序");
			renderJson(result);
			return;
		}
		// 查找会员档案
		MmsMember member = mmsMemberService.findMemberByCardInfo(cardNumber,name,idcard);
		logger.info("身份证对应会员档案：[{}]",JSON.toJSONString(member));
		if (null == member) {
			result.put("code", "10001");
			result.put("msg", "会员卡、姓名、身份证对应会员档案不存在");
			renderJson(result);
			return;
		}
		if (!member.getFullName().equals(name) && !MiniProgramUtils.dealFullNameMatching(member.getFullName(),name)) {
			result.put("code", "10001");
			result.put("msg", "会员姓名不匹配，绑定失败");
			renderJson(result);
			return;
		}
		
		FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);
		logger.info("会员卡号对应会员卡：[{}]",JSON.toJSONString(card));
		if (null == card) {
			result.put("code", "10001");
			result.put("msg", "会员卡不存在，绑定失败");
			renderJson(result);
			return;
		}
		if (!card.getMemberId().equalsIgnoreCase(member.getId())) {
			result.put("code", "10001");
			result.put("msg", "会员信息不匹配，绑定失败");
			renderJson(result);
			return;
		}
		
		String flag = mmsWxUserService.bindMemberCard(userId, card.getId());
		if ("suc".equals(flag)) {
			result.put("code", "0");
			result.put("msg", "绑定成功");
		}else if("".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "您已绑定此卡");
		}else if("exist".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "该会员卡已被绑定，操作失败");
		}else{
			result.put("code", "10001");
			result.put("msg", "绑定失败");
		}
		logger.info("绑定会员卡返回数据：[{}]",JSON.toJSONString(result));
		logger.info("绑定会员卡end...");
		renderJson(result);
	}
	
	/**
	 * 绑定会员卡
	 */
	public void bindUserCardWithPwd() {
		logger.info("绑定会员卡(WithPwd)begin...");
		String userId = getPara("userId");
		String cardNumber = getPara("cardNumber");
		String name = getPara("name");
		String idcard = getPara("idcard");
		String password = getPara("password");
		logger.info("用户ID：[{}]，会员卡号：[{}]，姓名：[{}]，身份证：[{}]，密码：[{}]，",userId,cardNumber,name,idcard,password);
		
		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(userId) || StrKit.isBlank(cardNumber) || StrKit.isBlank(name) || StrKit.isBlank(idcard) || StrKit.isBlank(password)){
			result.put("code", "10001");
			result.put("msg", "参数缺失");
			renderJson(result);
			return;
		}
		
		MmsWxUser wxUser = mmsWxUserService.findById(userId);
		logger.info("用户ID对应微信用户：[{}]",JSON.toJSONString(wxUser));
		if (null == wxUser) {
			result.put("code", "10001");
			result.put("msg", "微信用户不存在，绑定失败，请重新登录小程序");
			renderJson(result);
			return;
		}
		// 查找会员档案
		//MmsMember member = mmsMemberService.getMemberByIdcard(idcard);
		MmsMember member = mmsMemberService.findMemberByCardInfo(cardNumber,name,idcard);
		logger.info("身份证对应会员档案：[{}]",JSON.toJSONString(member));
		if (null == member) {
			result.put("code", "10001");
			result.put("msg", "会员卡、姓名、身份证对应会员档案不存在");
			renderJson(result);
			return;
		}
		if (!member.getFullName().equals(name) && !MiniProgramUtils.dealFullNameMatching(member.getFullName(),name)) {
			result.put("code", "10001");
			result.put("msg", "会员姓名不匹配，绑定失败");
			renderJson(result);
			return;
		}
		
		FinaMembershipCard card = finaMembershipCardService.getCardByNumber(cardNumber);
		logger.info("会员卡号对应会员卡：[{}]",JSON.toJSONString(card));
		if (null == card) {
			result.put("code", "10001");
			result.put("msg", "会员卡不存在，绑定失败");
			renderJson(result);
			return;
		}
		
		SimpleHash cardHash = new SimpleHash("md5", password, card.getSalt(), 2);
		if (card.getPassword().equals(cardHash.toHex()) == false) {
			result.put("code", "10001");
			result.put("msg", "密码输入不正确");
			renderJson(result);
			return;
        }
		
		if (!card.getMemberId().equals(member.getId())) {
			result.put("code", "10001");
			result.put("msg", "会员信息不匹配，绑定失败");
			renderJson(result);
			return;
		}
		
		String flag = mmsWxUserService.bindMemberCard(userId, card.getId());
		if ("suc".equals(flag)) {
			result.put("code", "0");
			result.put("msg", "绑定成功");
		}else if("".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "您已绑定此卡");
		}else if("exist".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "该会员卡已被绑定，操作失败");
		}else{
			result.put("code", "10001");
			result.put("msg", "绑定失败");
		}
		logger.info("绑定会员卡返回数据：[{}]",JSON.toJSONString(result));
		logger.info("绑定会员卡end...");
		renderJson(result);
	}

	public void getUserPromotionByPhone(){
		
		final String phone = getPara("phone");
		if(StrKit.isBlank(phone)){
			renderCodeFailed("phone参数不能为空!");
			return;
		}
		Record record = Db.findFirst("select c.id as 'cardId',c.card_number as 'cardNumber',DATE_FORMAT(c.open_time,'%Y-%m-%d') as 'openTime',"
			+ "wu.unionid,wu.openid,eu.emp_id as 'salesEmpId' from fina_membership_card c "
			+ "left join mms_wx_user_card_rela cr on cr.card_id=c.id "
			+ "left join mms_wx_user wu on wu.id=cr.user_id "
			+ "join pers_emp_user eu on eu.user_id=c.operator "
			+ "where c.del_flag='0' and c.is_lock='0' and c.telephone=? order by open_time limit 1;", phone);

		renderCodeSuccess("获取成功", record);
	}
	
	public void getUserPromotionByCardNumber(){
		
		final String cardNumber = getPara("cardNumber");
		if(StrKit.isBlank(cardNumber)){
			renderCodeFailed("cardNumber参数不能为空!");
			return;
		}
		Record record = Db.findFirst("select c.id as 'cardId',c.card_number as 'cardNumber',DATE_FORMAT(c.open_time,'%Y-%m-%d') as 'openTime',"
			+ "wu.unionid,wu.openid,eu.emp_id as 'salesEmpId' from fina_membership_card c "
			+ "left join mms_wx_user_card_rela cr on cr.card_id=c.id "
			+ "left join mms_wx_user wu on wu.id=cr.user_id "
			+ "join pers_emp_user eu on eu.user_id=c.operator "
			+ "where c.del_flag='0' and c.is_lock='0' and c.card_number=?", cardNumber);
		
		renderCodeSuccess("获取成功", record);
	}

	/**
	 * 获取用户绑定的会员卡
	 */
	@Clear(LogInterceptor.class)
	public void getUserCards(){

		String userId = getPara("userId");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(userId)){
			result.put("code", "10001");
			result.put("msg", "获取失败");
		}else{
			List<Record> list = mmsMemberService.getCardsByMember(userId);
			MiniProgramUtils.dealRecordNull(list);
			result.put("code", "0");
			result.put("msg","获取成功");
			result.put("data",list);
		}
		renderJson(result);
	}

	public void sendRandomCode(){
		final String phone = getPara("phone");
		if(StrKit.isBlank(phone)){
			renderCodeFailed("手机号不能为空");
			return;
		}
		if(!phone.matches("^1[3-9][0-9]\\d{8}$")){
			renderCodeFailed("手机号码格式不正确");
			return;
		}
		Map<String,Object> resultMap = weiXinActivityService.sendRandomCode(phone);
		if((Boolean) resultMap.get("flag")){
			renderCodeSuccess("验证码发送成功");
		}else{
			renderCodeFailed((String)resultMap.get("msg"));
		}
	}

	public void bindingCardSendRandomCode(){
		String phone=getPara("phone");
		String userId = getPara("userId");
		if(StrKit.isBlank(phone) || StrKit.isBlank(userId)){
			renderCodeFailed("参数缺失");
			return;
		}
		/*List<Record> recordList=Db.find("select a.id,b.full_name as fullName,a.card_number as cardNumber,c.card_type cardTypeName from fina_membership_card a " +
				"left join mms_member b on b.id=a.member_id left join main_membership_card_type c on c.id=a.card_type_id where a.del_flag='0' and a.telephone=? " +
				" and a.id not in ( select card_id from mms_wx_user_card_rela where user_id=? ) ",phone,userId);*/

		List<Record> recordList=Db.find("select a.id,b.full_name as fullName,a.card_number as cardNumber,c.card_type cardTypeName from fina_membership_card a " +
				"left join mms_member b on b.id=a.member_id left join main_membership_card_type c on c.id=a.card_type_id where a.del_flag='0' and a.telephone=? " +
				"  ",phone);

		List<String> bindingCardIdList=Db.query("select card_id from mms_wx_user_card_rela where user_id=? and del_flag='0' ",userId);



		if(recordList==null || recordList.size()==0){
			renderCodeFailed("该号码未查询到有对应的会员卡");
			return;
		}
		List<Record> unBindingCardList=new ArrayList<>();
		for(Record record:recordList){
			if(bindingCardIdList.contains(record.getStr("id"))){
				continue;
			}
			unBindingCardList.add(record);
		}
		if(unBindingCardList.size()==0){
			renderCodeFailed("您的"+recordList.size()+"张会员卡已全部绑定，无其他可绑定的会员卡");
			return;
		}

		Map<String,Object> resultMap=weiXinActivityService.sendRandomCode(phone);
		if((Boolean) resultMap.get("flag")){
			renderCodeSuccess("验证码发送成功",unBindingCardList);
		}else{
			renderCodeFailed((String)resultMap.get("msg"));
		}
	}

	public void bindingCardCheckCode(){
		String phone=getPara("phone");
		String code=getPara("code");
		String userId=getPara("userId");
		if(StrKit.isBlank(phone) || StrKit.isBlank(code)){
			renderCodeFailed("参数缺失");
			return;
		}

		List<Record> recordList=Db.find("select a.id,b.full_name as fullName,a.card_number as cardNumber,c.card_type cardTypeName from fina_membership_card a " +
				"left join mms_member b on b.id=a.member_id left join main_membership_card_type c on c.id=a.card_type_id where a.del_flag='0' and a.telephone=? " +
				"  ",phone);

		List<String> bindingCardIdList=Db.query("select card_id from mms_wx_user_card_rela where user_id=? and del_flag='0' ",userId);



		if(recordList==null || recordList.size()==0){
			renderCodeFailed("该号码未查询到有对应的会员卡");
			return;
		}
		List<Record> unBindingCardList=new ArrayList<>();
		for(Record record:recordList){
			if(bindingCardIdList.contains(record.getStr("id"))){
				continue;
			}
			unBindingCardList.add(record);
		}
		if(unBindingCardList.size()==0){
			renderCodeFailed("您的"+recordList.size()+"张会员卡已全部绑定，无其他可绑定的会员卡");
			return;
		}

		boolean flag=weiXinActivityService.checkCode(phone,code);
		if(flag){
			renderCodeSuccess("验证码正确",unBindingCardList);
		}else{
			renderCodeFailed("验证码错误");
		}
	}

	public void bindingCardByCode(){
		String userId=getPara("userId");
		String cardIds=getPara("cardIds");
		if(StrKit.isBlank(userId) || StrKit.isBlank(cardIds)){
			renderCodeFailed("参数缺失");
			return;
		}
		JSONArray jsonArray=JSON.parseArray(cardIds);
		if(jsonArray== null || jsonArray.size()==0){
			renderCodeFailed("请至少选择一个要绑定的会员卡");
			return;
		}

		List<MmsWxUserCardRela> cardRelaList=new ArrayList<>();
		for (int i = 0; i < jsonArray.size(); i++) {
			String cardId=jsonArray.getString(i);
			MmsWxUserCardRela wxUserCardRela = new MmsWxUserCardRela();
			wxUserCardRela.setId(IdGen.getUUID());
			wxUserCardRela.setUserId(userId);
			wxUserCardRela.setCardId(cardId);
			wxUserCardRela.setDelFlag("0");
			wxUserCardRela.setCreateTime(new Date());
			cardRelaList.add(wxUserCardRela);
		}
		try {
			Db.batchSave(cardRelaList,cardRelaList.size());
			renderCodeSuccess("绑定成功");
		}catch (Exception e){
			e.printStackTrace();
			renderCodeFailed("绑定异常");
		}
	}




	/**
	 * 获取微信用户信息接口
	 */
	@Clear(LogInterceptor.class)
	public void getUser(){

		String userId = getPara("userId");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(userId)){
			result.put("code", "10001");
			result.put("msg", "获取失败");

		}else{
			MmsWxUser wxUser = mmsWxUserService.findById(userId);
			if(wxUser == null){
				result.put("code", "10001");
				result.put("msg", "获取失败");
			}else{
				result.put("code", "0");
				result.put("data",wxUser);
			}
		}
		renderJson(result);
	}





	/**
	 *通过会员卡号获取会员卡信息
	 */
	@Clear(LogInterceptor.class)
	public void getMemberCard(){

		String cardNumber = getPara("cardNumber");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "卡号未传入");
		}else{
			Record card = finaMembershipCardService.getMembershipByCardNumber(cardNumber);
			if(card == null){
				result.put("code", "10001");
				result.put("msg", "系统内不存在该会员卡");
			}else{
				if("1".equals(card.getStr("isLock"))){
					result.put("code", "10001");
					result.put("msg", "该会员卡为锁定状态，请联系财务");
					renderJson(result);
					return;
				}
				/*if("1".equals(card.getStr("expire_flag"))){
					result.put("code", "10001");
					result.put("msg", "该会员卡为为过期状态");
					renderJson(result);
					return;
				}*/
				result.put("code", "0");
				result.put("msg","success");
				result.put("data",card);
			}
		}
		renderJson(result);
	}

	public void getMemberCardByNumber(){

		String cardNumber = getPara("cardNumber");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "卡号未传入");
		}else{
			Record card = finaMembershipCardService.getMembershipByCardNumber(cardNumber);
			if(card == null){
				result.put("code", "10001");
				result.put("msg", "系统内不存在该会员卡");
			}else{
				/*if("1".equals(card.getStr("isLock"))){
					result.put("code", "10001");
					result.put("msg", "该会员卡为锁定状态，请联系财务");
					renderJson(result);
					return;
				}*/
				/*if("1".equals(card.getStr("expire_flag"))){
					result.put("code", "10001");
					result.put("msg", "该会员卡为为过期状态");
					renderJson(result);
					return;
				}*/
				result.put("code", "0");
				result.put("msg","success");
				result.put("data",card);
			}
		}
		renderJson(result);
	}

	/**
	 * 通过
	 */
	@JCors
	@Clear(LogInterceptor.class)
	public void getMemberCardInfo() throws Exception{
		String name=getPara("name");
		String idcard=getPara("idcard");
		String telephone=getPara("telephone");
		String carNumber=getPara("carNumber");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(name) && StrKit.isBlank(idcard) && StrKit.isBlank(telephone) ){
			result.put("code", "10001");
			result.put("msg", "请至少传一个参数");
			renderJson(result);
			return;
		}
		List<Map<String,Object>> list=finaMembershipCardService.getMemberCardInfo(name,idcard,telephone,carNumber);
		for (Map<String, Object> map : list) {
			/*if("1".equals(map.get("expireFlag"))){
				result.put("code", "10001");
				result.put("msg", map.get("cardNumber")+"会员卡已过期");
				renderJson(result);
				return;
			}*/
		}
		result.put("code", "0");
		result.put("msg","success");
		result.put("data",list);
		renderJson(result);
	}

	@JCors
	public void getIntegralCardByNumber(){
		//跨域解决调用2次问题
		if(getRequest().getMethod().equals("OPTIONS")) {
			renderJson();
			return;
		}
		String cardNumber=getPara("cardNumber");
		if(StrKit.isBlank(cardNumber)){
			renderCodeFailed("会员卡号未传入");
			return;
		}
		Record card = finaMembershipCardService.getIntegralCardByNumber(cardNumber);
		if(card==null){
			renderCodeFailed("该会员卡号不存在");
			return;
		}
		if(!"1".equals(card.getStr("isIntegral")) && StrKit.isBlank(card.getStr("deductWay"))){
			renderCodeFailed("该会员卡扣除方式不存在");
			return;
		}
		if("1".equals(card.getStr("isLock"))){
			renderCodeFailed("该会员卡已被锁定，请联系财务");
			return;
		}
		renderCodeSuccess("success",card);
	}

	/**
	 * 会员卡交易明细
	 */
	@Clear(LogInterceptor.class)
	public void memberCardTransactions(){

		String cardId = getPara("cardId");
		String date = getPara("date");
		String type = getPara("type");
		Integer pageNumber = getParaToInt("pageNumber");
		Integer pageSize = getParaToInt("pageSize");

		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(cardId) || StringUtils.isBlank(date)){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}
		if(pageNumber == null || pageSize == null){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}

		Page<Record> page = finaCardTransactionsService.getPage(cardId,date,type,pageNumber,pageSize);
		result.put("code", "0");
		result.put("msg", "成功");
		if (null != page) {
			result.put("data", page.getList());
			result.put("pageNumber", page.getPageNumber());
			result.put("pageSize", page.getPageSize());
			result.put("totalPage", page.getTotalPage());
		} else {
			result.put("data", new ArrayList<>());
			result.put("pageNumber", 1);
			result.put("pageSize", pageSize);
			result.put("totalPage", 0);
		}
		renderJson(result);
	}

	/**
	 * 获取会员卡最近一次交易的年月
	 */
	public void memberCardLastDealMonth(){
		String cardId = getPara("cardId");
		String type = getPara("type");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StringUtils.isBlank(cardId)){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}

		String sql="select DATE_FORMAT(max(deal_time),'%Y-%m') from fina_card_transactions where card_id=?  ";

		List<Object> params=new ArrayList<>();
		params.add(cardId);
		if(StringUtils.isNotBlank(type)){
			sql += " AND `type` = ? ";
			params.add(type);
		}
		String month=Db.queryStr(sql,params.toArray());
		if(StrKit.notBlank(month)){
			result.put("month",month);
		}else{
			result.put("month","");
		}

		renderJson(result);
	}



    /**
     * 获取字典值：现用于交易类型
     */
	@Clear(LogInterceptor.class)
	public void getDictType(){
	    String dictType = getPara("dictType");
        Map<String, Object> result = new HashMap<String, Object>();

	    if(StringUtils.isBlank(dictType)){
            result.put("code", "10001");
            result.put("msg", "类型参数未传入");
            renderJson(result);
            return;
        }

        List<Dict> dictList =  dictService.getListByTypeOnUse(dictType);
	    if(dictList == null) dictList = new ArrayList<>();
        result.put("code", "0");
        result.put("msg", "成功");
        result.put("data", dictList);
        renderJson(result);
    }


	/**
	 * 会员卡月交易统计接口
	 */
	@Clear(LogInterceptor.class)
	public void cardTransMonthStatistics(){
		String cardId = getPara("cardId");
		String type = getPara("type");
		String date = getPara("date");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(cardId) || StringUtils.isBlank(date)){
			result.put("code", "10001");
			result.put("msg", "参数未传入");
			renderJson(result);
			return;
		}

		Map<String,Object> count = finaCardTransactionsService.getCardTransMonthStatistics(cardId,type,date);
		if(count == null)count = new HashMap<>();
		result.put("code", "0");
		result.put("msg", "成功");
		result.put("data", count);
		renderJson(result);
	}



	/**
	 * 获取微信文章接口
	 */
	@Clear(LogInterceptor.class)
	public void getWxArticle(){

		Integer pageNumber = getParaToInt("pageNumber");
		Integer pageSize = getParaToInt("pageSize");
		Map<String, Object> result = new HashMap<String, Object>();

		if(pageNumber == null || pageSize == null){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}

		Page<MmsWxArticle> page = mmsWxArticleService.getWxArticleList(pageNumber,pageSize);
		result.put("code", "0");
		result.put("msg", "成功");
		result.put("imgPath", Global.fileUrlPrefix);
		if (null != page) {
			result.put("data", page.getList());
			result.put("pageNumber", page.getPageNumber());
			result.put("pageSize", page.getPageSize());
			result.put("totalPage", page.getTotalPage());
			result.put("totalRow", page.getTotalRow());
		} else {
			result.put("data", new ArrayList<MmsWxArticle>());
			result.put("pageNumber", 1);
			result.put("pageSize", pageSize);
			result.put("totalPage", 0);
			result.put("totalRow", 0);
		}
		renderJson(result);
	}



	/**
	 * 上传图片
	 */
	public void uploadImg(){
        logger.info("小程序上传文件begin...");
		UploadFile file = getFile();
		Map<String, Object> result = new HashMap<String, Object>();

		if(file == null){
			result.put("code", "10001");
			result.put("msg", "图片不存在");
			renderJson(result);
			return;
		}
		Map<String, String> params = new HashMap<>();
		params.put("bucket","wechat");
		String content = HttpClientsUtils.httpPostFormMultipart(Global.commonUpload + "/upload",params,file.getFile(),null,null);
		logger.info("upload工程返回数据：[{}]",content);
		if(StringUtils.isBlank(content)){
			result.put("code", "10001");
			result.put("msg", "上传失败");
			renderJson(result);
			return;
		}

        JSONObject jsonObject = JSON.parseObject(content);
        if(jsonObject == null ||(jsonObject.getString("state") == null|| !"ok".equals(jsonObject.getString("state")))){
            result.put("code", "10001");
            result.put("msg", "上传失败");
            renderJson(result);
            return;
        }
		result.put("code", "0");
		result.put("msg", "0");
		result.put("data",jsonObject.getJSONObject("data"));
        logger.info("小程序上传文件end...");
		renderJson(result);
	}



	/**
	 * 删除上传图片
	 */
	public void delImg(){

		String fileName = getPara("fileName");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StringUtils.isBlank(fileName)){
			result.put("code", "10001");
			result.put("msg", "删除失败");
			renderJson(result);
			return;
		}

		String uploadPath = "";
		//判断系统
		final String osName = System.getProperty("os.name");
		if(osName.toLowerCase().indexOf("linux") > -1 || osName.toLowerCase().indexOf("centos") > -1){
			uploadPath = Global.uploadPathLinux;
		}else{
			uploadPath = Global.uploadPath;
		}
		File file = new File(uploadPath+"wechat\\"+fileName);
		if(!file.exists()){
			result.put("code", "10001");
			result.put("msg", "删除失败");
			renderJson(result);
			return;
		}
		if(file.delete()) {
			result.put("code", "0");
			result.put("msg", "成功");
			renderJson(result);
		}else{
			result.put("code", "10001");
			result.put("msg", "删除失败");
			renderJson(result);
		}
	}



	/**
	 *用户发布图文消息
	 */
	public void postMoments(){
		MmsWxMoments wxMoments = getBean(MmsWxMoments.class,"",true);
		Map<String, Object> result = new HashMap<String, Object>();

		//校验字符串
		if(StringUtils.isNotBlank(wxMoments.getImages())){
			try {
				JSONArray jsonArray = JSONArray.parseArray(wxMoments.getImages());
			} catch (Exception e) {
				//e.printStackTrace();
				result.put("code", "10001");
				result.put("msg", "字符串格式异常");
				renderJson(result);
				return;
			}
		}

        String id = mmsWxMomentsService.saveMmsWxMoments(wxMoments);
        if(StringUtils.isBlank(id)){
			result.put("code", "10001");
			result.put("msg", "发布失败");
			renderJson(result);
			return;
		}

        MmsWxMoments wxMomentsSave = new MmsWxMoments();
		wxMomentsSave.setId(id);

		result.put("code", "0");
		result.put("msg", "发布成功");
		result.put("momentId",wxMomentsSave);
		renderJson(result);
	}



	/**
	 * 获取用户图文列表
	 */
	@Clear(LogInterceptor.class)
	public void getUserMoments(){

		String userId = getPara("userId");
		Integer pageNumber = getParaToInt("pageNumber");
		Integer pageSize = getParaToInt("pageSize");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(userId) || pageNumber == null || pageSize == null){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}

        Page<Object> page= mmsWxMomentsService.getWxMomentsList(pageNumber,pageSize,userId);
		result.put("code", "0");
		result.put("msg", "获取成功");
		if(page != null) {
			result.put("data", page.getList());
			result.put("pageNumber", page.getPageNumber());
			result.put("pageSize", page.getPageSize());
			result.put("totalPage", page.getTotalPage());
			result.put("totalRow", page.getTotalRow());
		}else {
			result.put("data", new ArrayList<>());
			result.put("pageNumber", 1);
			result.put("pageSize", pageSize);
			result.put("totalPage", 0);
		}
		renderJson(result);
	}


	/**
	 * 获取用户图文评论、点赞内容接口
	 */
	@Clear(LogInterceptor.class)
	public void getMomentsReplies(){

		String momentId = getPara("momentId");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(momentId)){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}

		Record record = mmsWxMomentsService.getMomentById(momentId);
		if(record == null){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}

		//转为list
		List<Record> list = new ArrayList<>();
		list.add(record);
		//获取评论及点赞
		list.stream().forEach(item->{
			if(item != null && StringUtils.isNotBlank(item.getStr("id"))){
                 List<Record> replies = mmsWxRepliesService.getReplies(item.getStr("id"));
                 if(replies == null) replies = new ArrayList<>();
				 item.set("replies",replies);

				 List<Record> thumbsups = mmsWxThumbsupService.getThumbsup(item.getStr("id"));
				 if(thumbsups == null) thumbsups = new ArrayList<>();
				 item.set("thumbsup",thumbsups);
			}
			if(item != null && StringUtils.isNotBlank(item.getStr("userId"))){
				MmsWxUser wxUser = mmsWxUserService.findById(item.getStr("userId"));
				item.set("wxUser",wxUser);
			}
		});

		result.put("code", "0");
		result.put("msg", "获取成功");
		result.put("data", JSONArray.parseArray(JSON.toJSONString(list, SerializerFeature.WriteDateUseDateFormat)));
		renderJson(result);
	}

	@JCors
	public void checkBlacklist(){
		String cardNumber=getPara("cardNumber");
		String idCard=getPara("idCard");
		if(StrKit.notBlank(idCard)){
			MmsBlackList blackList = mmsBlackListService.getByIdCard(idCard);
			if(blackList!=null) {
				renderCodeFailed(idCard+"该身份证号码已被列入黑名单");
				return;
			}
		}
		if(StrKit.notBlank(cardNumber)){
			Record record=Db.findFirst("select b.idcard from fina_membership_card a left join mms_member b on a.member_id=b.id " +
					" where a.card_number='1' ORDER BY a.del_flag asc,a.create_time desc limit 1 ");
			if(record!=null && StrKit.notBlank(record.getStr("idcard"))){
				MmsBlackList blackList = mmsBlackListService.getByIdCard(record.getStr("idcard"));
				if(blackList!=null) {
					renderCodeFailed(idCard+"该会员卡持卡人身份证号码已被列入黑名单");
					return;
				}
			}
		}
		renderCodeSuccess("success");
	}


	/**
	 * 用户发表评论
	 */
	public void postReplies(){

		MmsWxReplies wxReplies = getBean(MmsWxReplies.class,"",true);
		//如果接受不到to_user，则：
		String toUser = getPara("to_user");
		wxReplies.setToUser(toUser);
		Map<String, Object> result = new HashMap<String, Object>();

		String flag = mmsWxRepliesService.saveMmsWxReplies(wxReplies);
		if("suc".equals(flag)){
			result.put("code", "0");
			result.put("msg", "评论成功");
		}else if("".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "评论失败，动态不存在");
		}else{
			result.put("code", "10001");
			result.put("msg", "评论失败");
		}
		renderJson(result);
	}


	/**
	 * 用户发表点赞
	 */
	public void postThumbsup(){

		MmsWxThumbsup wxThumbsup = getBean(MmsWxThumbsup.class,"",true);
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(wxThumbsup.getMomentId()) || StringUtils.isBlank(wxThumbsup.getUserId())){
			result.put("code", "10001");
			result.put("msg", "图文或当前用户不存在");
			renderJson(result);
			return;
		}
		String flag = mmsWxThumbsupService.saveMmsWxThumbsup(wxThumbsup);
		if("suc".equals(flag)){
			result.put("code", "0");
			result.put("msg", "点赞成功");
		}else if("".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "您已点赞");
		}else{
			result.put("code", "10001");
			result.put("msg", "点赞失败");
		}
		renderJson(result);
	}


	/**
	 * 获取关注的用户
	 */
	@Clear(LogInterceptor.class)
	public void getUserSubscriptions(){

		String userId = getPara("userId");
		Integer pageNumber = getParaToInt("pageNumber");
		Integer pageSize = getParaToInt("pageSize");
		Map<String, Object> result = new HashMap<String, Object>();

		if(pageNumber == null || pageSize == null || StringUtils.isBlank(userId)){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}
		Page<Record> page = mmsWxUserService.getSubscriptionsByUser(pageNumber,pageSize,userId,null);
		result.put("code", "0");
		result.put("msg", "成功");
		if(page != null) {
			result.put("data", page.getList());
			result.put("pageNumber", page.getPageNumber());
			result.put("pageSize", page.getPageSize());
			result.put("totalPage", page.getTotalPage());
			result.put("totalRow", page.getTotalRow());
		}else {
			result.put("data", new ArrayList<>());
			result.put("pageNumber", 1);
			result.put("pageSize", pageSize);
			result.put("totalPage", 0);
		}
		renderJson(result);
	}


	/**
	 * 通过昵称查找用户
	 */
	@Clear(LogInterceptor.class)
	public void findUser(){

		String nickName = getPara("nickName");
		Map<String, Object> result = new HashMap<String, Object>();

		List<MmsWxUser> list = mmsWxUserService.getWxUserByNickName(nickName);
		if(list == null){
			result.put("data", new ArrayList<>());
		}else{
			result.put("data", list);
		}
		result.put("code", "0");
		result.put("msg", "成功");
		renderJson(result);
	}



	/**
	 * 获取关注的人的图文消息
	 */
	@Clear(LogInterceptor.class)
	public void getSubscriptionMoments(){

		String userId = getPara("userId");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StrKit.isBlank(userId)){
			result.put("code", "10001");
			result.put("msg", "当前用户不存在");
			renderJson(result);
			return;
		}
		JSONArray jsonArray = mmsWxMomentsService.getSubMoments(userId);
		result.put("code", "0");
		result.put("msg", "成功");
		result.put("data", jsonArray);
		renderJson(result);
	}


	/**
	 * 关注用户
	 */
	public void subscribeUser(){

		MmsWxSubscriptions wxSubscriptions = getBean(MmsWxSubscriptions.class,"",true);
		Map<String, Object> result = new HashMap<String, Object>();

		if(wxSubscriptions == null || StringUtils.isBlank(wxSubscriptions.getUserId()) || StringUtils.isBlank(wxSubscriptions.getSubscriberId())){
			result.put("code", "10001");
			result.put("msg", "关注失败");
			renderJson(result);
			return;
		}

		boolean flag = mmsWxSubscriptionsService.saveSubscriptions(wxSubscriptions);
		if(flag){
			result.put("code", "0");
			result.put("msg", "关注成功");
		}else{
			result.put("code", "10001");
			result.put("msg", "关注失败");
		}
		renderJson(result);
	}


	/**
	 * 获取常用入住人列表
	 */
	@Clear(LogInterceptor.class)
	public void getBookPersions(){

		String userId = getPara("userId");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(userId)){
			result.put("code", "10001");
			result.put("msg", "当前用户不存在");
			renderJson(result);
			return;
		}

		List<MmsWxBookPerson> list = mmsWxBookPersonService.getWxBookPersons(userId);
		JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(list,SerializerFeature.WriteNullStringAsEmpty));
		result.put("code", "0");
		result.put("msg", "获取成功");
		result.put("data", jsonArray);
		renderJson(result);
	}


	/**
	 * 保存常用入住人
	 */
	public void saveBookPerson(){

		MmsWxBookPerson person = getBean(MmsWxBookPerson.class,"",true);
		Map<String, Object> result = new HashMap<String, Object>();

		if(person == null){
			result.put("code", "10001");
			result.put("msg", "常用入住人不存在");
			renderJson(result);
			return;
		}
		if(StringUtils.isBlank(person.getUserId())){
			result.put("code", "10001");
			result.put("msg", "当前用户不存在");
			renderJson(result);
			return;
		}
		if(StringUtils.isBlank(person.getName()) || StringUtils.isBlank(person.getIdcard())){
			result.put("code", "10001");
			result.put("msg", "姓名或身份证不存在");
			renderJson(result);
			return;
		}
		String flag = mmsWxBookPersonService.saveWxBookPerson(person);
		if("suc".equals(flag)){
			result.put("code", "0");
			result.put("msg", "保存成功");
		}else if("".equals(flag)){
			result.put("code", "10001");
			result.put("msg", "该身份证对应联系人已被添加");
		}else{
			result.put("code", "10001");
			result.put("msg", "保存失败");
		}
		renderJson(result);
	}


	/**
	 * 删除常用入住人
	 */
	public void delBookPerson(){

		String id = getPara("id");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(id)){
			result.put("code", "10001");
			result.put("msg", "参数未传入");
			renderJson(result);
			return;
		}

		boolean flag = mmsWxBookPersonService.delWxBookPerson(id);
		if(flag){
			result.put("code", "0");
			result.put("msg", "删除成功");
		}else{
			result.put("code", "10001");
			result.put("msg", "删除失败");
		}
		renderJson(result);
	}


	/**
	 * 获取用户粉丝
	 */
	@Clear(LogInterceptor.class)
	public void getUserFans(){

		String subscriberId = getPara("userId");
		Integer pageNumber = getParaToInt("pageNumber");
		Integer pageSize = getParaToInt("pageSize");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(subscriberId)){
			result.put("code", "10001");
			result.put("msg", "当前用户不存在");
			renderJson(result);
			return;
		}
		if(pageNumber == null || pageSize == null){
			result.put("code", "10001");
			result.put("msg", "获取失败");
			renderJson(result);
			return;
		}
		Page<Record> page = mmsWxUserService.getSubscriptionsByUser(pageNumber,pageSize,null,subscriberId);
		result.put("code", "0");
		result.put("msg", "获取成功");
		if(page != null) {
			result.put("data", page.getList());
			result.put("pageNumber", page.getPageNumber());
			result.put("pageSize", page.getPageSize());
			result.put("totalPage", page.getTotalPage());
			result.put("totalRow", page.getTotalRow());
		}else {
			result.put("data", new ArrayList<>());
			result.put("pageNumber", 1);
			result.put("pageSize", pageSize);
			result.put("totalPage", 0);
		}
		renderJson(result);
	}


	/**
	 * 获取用户分享、点赞、关注、粉丝统计
	 */
	@Clear(LogInterceptor.class)
	public void getShareStatistics(){

		String userId = getPara("userId");
		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(userId)){
			result.put("code", "10001");
			result.put("msg", "当前用户不存在");
			renderJson(result);
			return;
		}
		Map<String,Object> counts = new HashMap<>();
		counts.put("userId",userId);
		//关注数
		Long subscriptionCount =  mmsWxSubscriptionsService.getSubscriptionCount(userId);
		counts.put("subscriptions",subscriptionCount);
		//评论数
		Long replieCount = mmsWxRepliesService.getReplieCount(userId);
		counts.put("replies",replieCount);
		//点赞数
		Long thumbsupCount = mmsWxThumbsupService.getThumbsupCount(userId);
		counts.put("thumbsup",thumbsupCount);
		//分享数
		Long momentCount = mmsWxMomentsService.getMomentCount(userId);
		counts.put("share",momentCount);
		//粉丝数
		Long fanCount = mmsWxSubscriptionsService.getubscriptionedCount(userId);
		counts.put("fans",fanCount);

		result.put("code", "0");
		result.put("data",counts);
		renderJson(result);
	}



	/**
	 * 会员上传人脸图片接口
	 */
	public void uploadFace(){
		String name=getPara("name");
		String idcard=getPara("idcard");
		String image=getPara("image");

		Map<String, Object> result = new HashMap<String, Object>();

		if(StringUtils.isBlank(name)){
			result.put("code", "10001");
			result.put("msg", "姓名不能为空");
			renderJson(result);
			return;
		}

		if(StringUtils.isBlank(idcard)){
			result.put("code", "10001");
			result.put("msg", "身份证不能为空");
			renderJson(result);
			return;
		}

		if(StringUtils.isBlank(image)){
			result.put("code", "10001");
			result.put("msg", "人脸照片不能为空");
			renderJson(result);
			return;
		}

		if(image.contains(",")){
			image = image.substring(image.indexOf(",") + 1,image.length());
		}

		MmsMember member =mmsMemberService.getMemberByNameAndIdcard(name,idcard);
		if(member == null){
			result.put("code", "10001");
			result.put("msg", "您的会员信息不存在,无法上传人脸照片");
			renderJson(result);
			return;
		}

		//去重
		Long count = Db.queryLong("SELECT COUNT(*) FROM mms_member_face WHERE verify_flag in (0,1) and member_id = ?",member.getId());
		if(count > 0){
			result.put("code", "10001");
			result.put("msg", "系统已存在您的人脸照片");
			renderJson(result);
			return;
		}

		boolean flag=mmsMemberFaceService.uploadFace(member.getId(),image);
		if(flag){
			result.put("code", "0");
			result.put("msg", "success");
		}else{
			result.put("code", "10001");
			result.put("msg", "error");
		}
		renderJson(result);
	}

	/**
	 * 获取最新人脸图片及审核结果
	 */
	@Clear(LogInterceptor.class)
	public void getFace(){
		String idcard=getPara("idcard");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StringUtils.isBlank(idcard)){
			result.put("code", "10001");
			result.put("msg", "身份证不能为空");
			renderJson(result);
			return;
		}
		Record record=mmsMemberFaceService.getFace(idcard);
		if(record == null){
			record = new Record();
			record.set("id","");
			record.set("memberId","");
			record.set("picture","");
			record.set("verifyFlag","-1");//此人信息不存在
			record.set("verifyDescribe","");
			record.set("collectTime","");

			result.put("code","0");
			result.put("msg","success");
			result.put("data",record);
		}else{
			result.put("code","0");
			result.put("msg","success");
			result.put("data",record);
		}
		renderJson(result);
	}


	/**
	 * 根据合同创建会员卡和会员档案
	 */
	public void contractCreateMember(){
		logger.info("根据合同创建会员卡和会员档案begin...");
		//姓名
		String name=getPara("name");
		//身份证号
		String idcard=getPara("idcard");
		//电话号码
		String telephone=getPara("telephone");
		//开卡时间
		Date openTime=getParaToDate("openTime");
		//销售人Id
		String salesman=getPara("salesman");
		//性别
		String gender=getPara("gender");
		//金额
		String balance=getPara("balance");
		//赠送金额
		String giveBalance=getPara("giveBalance");
		//天数
		Integer consumeTimes=getParaToInt("consumeTimes");
		//赠送天数
		Integer giveConsumeTimes=getParaToInt("giveConsumeTimes");
		//合同编号
		String contractNumber=getPara("contractNumber");
		//会员卡类别Id
		String cardTypeId=getPara("cardTypeId");
		//规则id
		String ruleId=getPara("ruleId");

		logger.info("根据合同创建会员卡和会员档案接收入参01：姓名：[{}]，身份证：[{}]，电话号码：[{}]，开卡时间：[{}]，销售人Id：[{}]",name,idcard,telephone,openTime,salesman);
		logger.info("根据合同创建会员卡和会员档案接收入参02：性别：[{}]，金额：[{}]，赠送金额：[{}]，天数：[{}]",gender,balance,giveBalance,consumeTimes);
		logger.info("根据合同创建会员卡和会员档案接收入参03：赠送天数：[{}]，合同编号：[{}]，会员卡类别Id：[{}]，规则id：[{}]",giveConsumeTimes,contractNumber,cardTypeId,ruleId);


		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(name)){
			result.put("code", "10001");
			result.put("msg", "姓名不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(idcard)){
			result.put("code", "10001");
			result.put("msg", "身份证不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(telephone)){
			result.put("code", "10001");
			result.put("msg", "手机号不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(contractNumber)){
			result.put("code", "10001");
			result.put("msg", "合同编号不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(cardTypeId)){
			result.put("code", "10001");
			result.put("msg", "会员卡类型id不能为空");
			renderJson(result);
			return;
		}

		if(finaMembershipCardService.findBycontractNumber(contractNumber)!=null){
			result.put("code", "10001");
			result.put("msg", "该合同已经生成过了");
			renderJson(result);
			return;
		}
		//会员档案
		MmsMember mmsMember=new MmsMember();
		mmsMember.setIdcard(idcard);
		//通过身份证获取出生年月日
		if(idcard!=null && idcard.length()==18){
			mmsMember.setBirthday(idcard.substring(6,10)+"-"+idcard.substring(10,12)+"-"+idcard.substring(12,14));
		}else if(idcard!=null && idcard.length()==15){
			mmsMember.setBirthday("19"+idcard.substring(6,8)+"-"+idcard.substring(8,10)+"-"+idcard.substring(10,12));
		}
		mmsMember.setFullName(name);
        mmsMember.setSurname(MiniProgramUtils.dealSurname(name));
//		mmsMember.setTelephone(telephone);
		mmsMember.setGender(gender);


		//会员卡
		FinaMembershipCard card=new FinaMembershipCard();
		card.setContractNumber(contractNumber);
		card.setOperator(salesman);
		if(StrKit.notBlank(balance)){
			card.setBalance(0.00);//余额暂无，暂时统一为0
			card.setPurchaseCardBalance(Double.valueOf(balance));
		}
		card.setConsumeTimes(consumeTimes != null ? Double.valueOf(consumeTimes):null);
		card.setTelephone(telephone);
		card.setOpenTime(openTime);
		card.setCardTypeId(cardTypeId);
		if(StrKit.notBlank(giveBalance)){
			card.setGiveBalance(Double.valueOf(giveBalance));
		}
		card.setGiveConsumeTimes(giveConsumeTimes != null ? Double.valueOf(giveConsumeTimes):null);

		//生成会员卡号begin...
		logger.info("生成会员卡号begin...");
		Long count = null;
		do{
			try {
				Map<String, Object> numMap = finaMembershipCardService.dealRandomNumber(cardTypeId);
				logger.info("生成会员卡返回数据：[{}]", JSON.toJSONString(numMap));
				if (numMap.containsKey("ok")) {
					card.setCardNumber((String) numMap.get("ok"));
				} else if (numMap.containsKey("fail")) {
					result.put("code", "10001");
					result.put("msg", numMap.get("fail"));
					renderJson(result);
					return;
				} else {
					result.put("code", "10001");
					result.put("msg", "生成会员卡失败");
					renderJson(result);
					return;
				}
			} catch (Exception e) {
				logger.info("生成会员卡异常：[{}]", e.getMessage());
				result.put("code", "10001");
				result.put("msg", "生成会员卡失败");
				renderJson(result);
				return;
			}
			count = Db.queryLong("select count(*) from fina_membership_card where del_flag = 0 and card_number = ?",card.getCardNumber());
		}while(count > 0);
		logger.info("生成会员卡号end...");
		//生成会员卡号end...

		Ret returnRet;
		try {
			returnRet=finaMembershipCardService.saveCard(card,mmsMember,ruleId,AuthUtils.getUserId(),null,null,null);
		}catch (Exception e){
			e.printStackTrace();
			result.put("code", "10001");
			result.put("msg", "创建会员卡和会员档案失败");
			renderJson(result);
			return;
		}
		if(returnRet.isOk()){
			Map<String,String> dataMap=new HashMap<>();
			dataMap.put("cardId",card.getId());
			dataMap.put("cardNumber",card.getCardNumber());
			dataMap.put("memberId",card.getMemberId());

			result.put("code","0");
			result.put("msg","success");
			result.put("data",dataMap);
			renderJson(result);
		}else{
			renderCodeFailed(returnRet.getStr("msg"));
		}
		logger.info("根据合同创建会员卡和会员档案end...");
	}

	/**
	 * 获取最新一条步数数据
	 */
	@Clear(LogInterceptor.class)
	public void latestSteps(){
		String cardNumber=getPara("cardNumber");

		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "会员卡号不能为空");
			renderJson(result);
			return;
		}
		Map<String,Object> data=mmsMeasureStepsService.latestSteps(cardNumber);
		result.put("code","0");
		result.put("msg","success");
		result.put("data",data);
		renderJson(result);
	}

	/**
	 *
	 * 获取最新一条血压数据
	 */
	@Clear(LogInterceptor.class)
	public void latestBp(){
		String cardNumber=getPara("cardNumber");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "会员卡号不能为空");
			renderJson(result);
			return;
		}
		Map<String,Object> data=mmsMeasureBpService.latestBp(cardNumber);
		result.put("code","0");
		result.put("msg","success");
		result.put("data",data);
		renderJson(result);
	}

	/**
	 * 获取最新一条心率数据
	 */
	@Clear(LogInterceptor.class)
	public void latestHr(){
		String cardNumber=getPara("cardNumber");

		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "会员卡号不能为空");
			renderJson(result);
			return;
		}
		Map<String,Object> data=mmsMeasureHrService.latestHr(cardNumber);
		result.put("code","0");
		result.put("msg","success");
		result.put("data",data);
		renderJson(result);
	}

	/**
	 * 获取会员步数数据
	 */
	@Clear(LogInterceptor.class)
	public void memberSteps(){
		String cardNumber=getPara("cardNumber");
		String type=getPara("type");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "会员卡号不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(type)){
			result.put("code", "10001");
			result.put("msg", "周期不能为空");
			renderJson(result);
			return;
		}

		Map<String,List> stepsData=mmsMeasureStepsService.memberSteps(cardNumber,type);
		result.put("code","0");
		result.put("msg","success");
		result.put("data",stepsData);
		renderJson(result);
	}

	/**
	 * 获取会员心血压数据
	 */
	@Clear(LogInterceptor.class)
	public void memberBp(){
		String cardNumber=getPara("cardNumber");
		String type=getPara("type");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "会员卡号不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(type)){
			result.put("code", "10001");
			result.put("msg", "周期不能为空");
			renderJson(result);
			return;
		}

		Map<String, List> bpData=mmsMeasureBpService.memberBp(cardNumber,type);
		result.put("code","0");
		result.put("msg","success");
		result.put("data",bpData);
		renderJson(result);
	}

	/**
	 * 获取会员心率数据
	 */
	@Clear(LogInterceptor.class)
	public void memberHr(){
		String cardNumber=getPara("cardNumber");
		String type=getPara("type");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StrKit.isBlank(cardNumber)){
			result.put("code", "10001");
			result.put("msg", "会员卡号不能为空");
			renderJson(result);
			return;
		}
		if(StrKit.isBlank(type)){
			result.put("code", "10001");
			result.put("msg", "周期不能为空");
			renderJson(result);
			return;
		}
		Map<String,List> hrData=mmsMeasureHrService.memberHr(cardNumber,type);
		result.put("code","0");
		result.put("msg","success");
		result.put("data",hrData);
		renderJson(result);
	}



	/**
	 * 解绑会员卡
	 */
	public void untyingUserCard(){
		String userId = getPara("userId");
		String userCards = getPara("userCards");
		Map<String, Object> result = new HashMap<String, Object>();
		if(StringUtils.isBlank(userId) || StringUtils.isBlank(userCards)){
			result.put("code", "10001");
			result.put("msg", "微信用户id或会员卡为空");
			renderJson(result);
			return;
		}
		String[] userCardsArr = userCards.split(",");
		boolean flag = mmsWxUserCardRelaService.untyingUserCard(userId,userCardsArr);
		if(flag){
			result.put("code","0");
			result.put("msg","解绑成功");
		}else{
			result.put("code","10001");
			result.put("msg","解绑失败");
		}
		renderJson(result);
	}
	
    /**
     * 判断是否开启密码验证
     */
    public void checkMemberCardPasswordEnable(){
    	String cardNumber=getPara("cardNumber");
    	String isEnablePwd = "";

    	Map<String,Object> obj=new HashMap<>();
		obj.put("code","10001");
		if(StrKit.isBlank(cardNumber)){
			obj.put("msg","参数为空!");
			renderJson(obj);
			return;
    	}else{
    		FinaMembershipCard memberCard = finaMembershipCardService.getByCardNumber(cardNumber);
    		if(memberCard!=null){
    			isEnablePwd = memberCard.getIsEnablePwd();
    			if(StrKit.notBlank(isEnablePwd)){
					obj.put("code","0");
    				obj.put("msg","密码正确!");
					obj.put("isEnablePwd",isEnablePwd);
					renderJson(obj);
					return;
    			}else{
					obj.put("msg","密码启用标识为空!");
					obj.put("isEnablePwd",isEnablePwd);
					renderJson(obj);
					return;
    			}
    		}else{
				obj.put("msg","卡号不存在!");
				obj.put("isEnablePwd",isEnablePwd);
				renderJson(obj);
				return;
    		}
    	}
    }
    
    /**
     * 判断会员卡密码是否正确
     */
    public void checkMemberCardPassword(){
    	String cardNumber=getPara("cardNumber");
    	String password=getPara("password");

    	Map<String,Object> obj=new HashMap<>();
		obj.put("code","10001");
    	if(StrKit.isBlank(cardNumber)||StrKit.isBlank(password)){
			obj.put("msg","参数为空!");
			renderJson(obj);
			return;
    	}else{
    		FinaMembershipCard memberCard = finaMembershipCardService.getByCardNumber(cardNumber);
    		if(memberCard!=null){
    			String pwd = memberCard.getPassword();
    			String pwdSalt = memberCard.getSalt();
    			if(StrKit.notBlank(pwd)){
    				SimpleHash hash = new SimpleHash("md5", password, pwdSalt, 2);
    				if (pwd.equals(hash.toHex()) == false) {
						obj.put("msg","密码不正确!");
    					renderJson(obj);
    					return;
    		        }else{
						obj.put("code","0");
						obj.put("msg","密码正确!");
						renderJson(obj);
						return;
    		        }
    			}else{
					obj.put("msg","密码为空,请先设置密码!");
					renderJson(obj);
					return;
    			}
    		}else{
				obj.put("msg","卡号不存在!");
				renderJson(obj);
				return;
    		}
    	}
    }

    /**
     * 保存会员卡密码
     */
    public void saveMemberCardPassword(){
    	String cardNumber=getPara("cardNumber");
    	String oldPassword=getPara("oldPassword");
    	String password=getPara("password");

    	Map<String,Object> obj=new HashMap<>();
		obj.put("code","10001");
    	if(StrKit.isBlank(cardNumber)||StrKit.isBlank(oldPassword)||StrKit.isBlank(password)){
			obj.put("msg","参数为空!");
			renderJson(obj);
			return;
    	}else{
    		FinaMembershipCard memberCard = finaMembershipCardService.getByCardNumber(cardNumber);
    		if(memberCard!=null){
    			SimpleHash cardHash = new SimpleHash("md5", oldPassword, memberCard.getSalt(), 2);
    			if (memberCard.getPassword().equals(cardHash.toHex()) == false) {
					obj.put("msg","旧密码输入不正确!");
					renderJson(obj);
					return;
    	        }else{
    	        	FinaMembershipCard mCard = new FinaMembershipCard();
    	        	String salt = new SecureRandomNumberGenerator().nextBytes().toHex();
    	        	SimpleHash hash = new SimpleHash("md5", password, salt, 2);
    	        	final String pwd = hash.toHex();
    	        	mCard.setId(memberCard.getId());
    	        	mCard.setPassword(pwd);
    	        	mCard.setSalt(salt);
    	        	mCard.setIsEnablePwd("1");
    	        	mCard.setUpdateTime(new Date());
    	        	boolean flag=finaMembershipCardService.update(mCard);
    	        	if(flag){
						obj.put("code","0");
						obj.put("msg","操作成功!");
						renderJson(obj);
						return;
    	        	}else{
						obj.put("msg","操作失败!");
						renderJson(obj);
						return;
    	        	}
    	        }
    		}else{
				obj.put("msg","卡号不存在!");
				renderJson(obj);
				return;
    		}
    	}
    }
    
	/**
	 * 根据微信unionid获取用户积分会员卡列表
	 */
    @JCors
	@Clear(LogInterceptor.class)
	public void getUserntegralCards(){

		final String unionid = getPara("unionid");
		if(StrKit.isBlank(unionid)) {
			renderJson(RetApi.fail("msg", "unionid参数不能为空!").set("data", null));
			return;
		}
		List<Record> cardList = mmsWxUserService.getIntegralCardListByUnionid(unionid);
		renderJson(RetApi.ok("msg", "返回成功!").set("data", cardList));
	}

	/**
	 * 根据微信unionid获取用户积分会员卡列表
	 */
	@JCors
	@Clear(LogInterceptor.class)
	public void getCardRollInfo(){

		String id= getPara("id");
		if(StrKit.isBlank(id)) {
			renderCodeFailed("参数缺失");
			return;
		}
		CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(id);
		if(rollRecord==null){
			renderCodeFailed("查询失败");
			return;
		}
		if(finaCardRollService.findByRollId(rollRecord.getId())!=null){
			renderCodeFailed("该券已被绑定");
			return;
		}

		CrmCardRoll roll=crmCardRollService.findById(rollRecord.getCardRollId());
		Record record=new Record();
		record.set("rollNumber", rollRecord.getRollNumber());
		record.set("rollName",roll.getRollName());

		renderCodeSuccess("success",record);
	}

	@JCors
	@Clear(LogInterceptor.class)
	public void getRollType(){
		List<CrmCardRollType> rollList=crmCardRollTypeService.findList();

		List<Record> recordList=new ArrayList<>();
		for (CrmCardRollType rollType : rollList) {
			Record record = new Record();
			record.set("Text",rollType.getTypeName());
			record.set("Value",rollType.getId());
			recordList.add(record);
		}
		renderCodeSuccess("success",recordList);
	}


	@JCors
	public void saveCardRoll(){
		CrmCardRoll model = getBean(CrmCardRoll.class, "", true);
		final String fitBranchOffice = getPara("fitBranchOffice");
		final String fitBase = getPara("fitBase");
		String cardCouponApplyId=getPara("CardCouponApplyId");
		boolean returnFlag = false;
		String returnMsg = "操作失败！";
		/*if(model.getDeployStatus().equals("0")) {//发布状态
			model.setDeployTime(new Date());
		}*/
		model.setDeployStatus("0");
		model.setDeployTime(new Date());
		model.setCardCouponApplyId(cardCouponApplyId);

		if(StrKit.isBlank(model.getId())) {
			final String resultStr = HttpClientsUtils.get(Global.codeGenerateUrl +"?userId="+AuthUtils.getUserId()+"&formNo="+Global.formNo);
			if(StrKit.notBlank(resultStr)) {
				JSONObject returnJson = JSONObject.parseObject(resultStr);
				final int rType = returnJson.getIntValue("Type");
				final String rMsg = returnJson.getString("Msg");
				System.out.println("rType="+rType);
				System.out.println("rMsg="+rMsg);
				if(rType==1) {
					model.setId(IdGen.getUUID());
					model.setDelFlag(DelFlag.NORMAL);
					//model.setCreateBy(AuthUtils.getUserId());
					model.setCreateTime(new Date());
					final String formNo = returnJson.getString("Data");
					model.setRollCode(formNo);
					returnFlag = model.save();
					returnMsg = "操作成功!";
				}else {

				}
			}
		}else {
			//model.setUpdateBy(AuthUtils.getUserId());
			model.setUpdateTime(new Date());
			returnFlag = model.update();
			returnMsg = "操作成功!";
			if(returnFlag) {
				//先删除原来的数据
				Db.update("delete from crm_card_roll_branch_office where card_roll_id=?", model.getId());
				Db.update("delete from crm_card_roll_base where card_roll_id=?", model.getId());
			}
		}
		if(returnFlag){
			if(StrKit.notBlank(fitBranchOffice)) {
				final String[] fitBranchOfficeArray = fitBranchOffice.split(",");
				for(String branchOfficeId : fitBranchOfficeArray) {
					CrmCardRollBranchOffice crmCardRollBranchOffice = new CrmCardRollBranchOffice();
					crmCardRollBranchOffice.setId(IdGen.getUUID());
					crmCardRollBranchOffice.setCardRollId(model.getId());
					crmCardRollBranchOffice.setBranchOfficeId(branchOfficeId);
					crmCardRollBranchOffice.save();
				}
			}
			if(StrKit.notBlank(fitBase)) {
				final String[] fitBaseArray = fitBase.split(",");
				for(String baseId : fitBaseArray) {
					CrmCardRollBase crmCardRollBase = new CrmCardRollBase();
					crmCardRollBase.setId(IdGen.getUUID());
					crmCardRollBase.setCardRollId(model.getId());
					crmCardRollBase.setBaseId(baseId);
					crmCardRollBase.save();
				}
			}
			if(model.getDeployStatus().equals("0")) {//发布状态
				if(model.getRollQuantity()!=null && model.getRollQuantity()>0) {
					int digit = 5;
					if(model.getRollQuantity()>=10000 && model.getRollQuantity()<100000) {
						digit = 6;
					}else if(model.getRollQuantity()>=100000 && model.getRollQuantity()<1000000) {
						digit = 7;
					}else if(model.getRollQuantity()>=1000000 && model.getRollQuantity()<10000000) {
						digit = 8;
					}
					for (int i = 1; i <= model.getRollQuantity(); i++) {
						CrmCardRoll roll=crmCardRollService.findById(model.getId());

						final String rollNumber = String.format("%"+digit+"d", i).replace(" ", "0");
						CrmCardRollRecord crmCardRollRecord = new CrmCardRollRecord();
						crmCardRollRecord.setId(IdGen.getUUID());
						crmCardRollRecord.setCardRollId(model.getId());
						crmCardRollRecord.setRollValue(roll.getRollValue());
						crmCardRollRecord.setBalanceRollValue(roll.getRollValue());
						crmCardRollRecord.setRollNumber(model.getRollCode()+rollNumber);
						crmCardRollRecord.setIsEnable("0");
						crmCardRollRecord.setDelFlag(DelFlag.NORMAL);
						//crmCardRollRecord.setCreateBy(AuthUtils.getUserId());
						crmCardRollRecord.setCreateTime(new Date());
						returnFlag = crmCardRollRecord.save();
						if(!returnFlag) {
							break;
						}
					}
				}
			}
			if(returnFlag) {
				renderCodeSuccess("success",returnMsg);
			}else {
				renderCodeFailed("生成记录失败！");
			}
		}else{
			renderCodeFailed(returnMsg);
		}

	}

	@JCors
	public void cardBindRoll(){
		String cardId=getPara("cardId");
		String rollId=getPara("rollId");
		if(StrKit.isBlank(cardId) || StrKit.isBlank(rollId)){
			renderCodeFailed("参数缺失");
			return;
		}
		FinaMembershipCard card=finaMembershipCardService.findById(cardId);
		if(card==null){
			renderCodeFailed("获取会员卡失败");
			return;
		}
		if("1".equals(card.getIsLock())){
			renderCodeFailed(card.getCardNumber() + "卡已锁定,不能绑定");
			return;
		}
		if("1".equals(card.getDelFlag())){
			renderCodeFailed(card.getCardNumber() + "卡已作废,不能绑定");
			return;
		}
		MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
		if(cardType==null){
			renderCodeFailed("获取会员卡类型失败");
			return;
		}
		if(!"consume_card".equals(cardType.getTypeCategory())){
			renderCodeFailed("目前只允许消费卡绑定卡券");
			return;
		}
		if(crmCardRollRecordService.isNotbind(rollId)){
			renderCodeFailed("该券已超过最大可绑定时间");
			return;
		}
		Record record=Db.findFirst("select * from fina_card_roll where roll_id=?",rollId);
		if(record!=null){
			FinaMembershipCard bindCard = finaMembershipCardService.findById(record.getStr("card_id"));
			renderCodeFailed("该券已被"+bindCard.getCardNumber()+"会员卡绑定");
			return;
		}
		CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(rollId);
		if(rollRecord==null){
			renderCodeFailed("该券不存在");
			return;
		}else if("1".equals(rollRecord.getIsEnable())){
			renderCodeFailed(rollRecord.getRollNumber() + "券已过期");
			return;
		}
		if("1".equals(rollRecord.getDelFlag())){
			renderCodeFailed("该券已被作废");
			return;
		}

		List<String> rollNumberList = Arrays.asList(new String[]{rollRecord.getRollNumber()});
		final String rollNumberArrayStr = "["+rollNumberList.stream().map(s -> "\"" + s + "\"").collect(Collectors.joining(","))+"]";
		JSONObject paramJson = new JSONObject();
		paramJson.put("CardId", cardId);
		paramJson.put("cardCouponNos", rollNumberArrayStr);
		paramJson.put("description", "wmp");
		paramJson.put("isCancel", false);
		final String returnStr = HttpClientsUtils.httpPostRaw(Global.sojournUrl+"/Member/EdiCardCoupon",paramJson.toJSONString(),null,null);
		if(StrKit.notBlank(returnStr)){
			if(returnStr.startsWith("{") && returnStr.endsWith("}")){
				JSONObject returnObj = JSON.parseObject(returnStr);
				if(returnObj.containsKey("Type") && returnObj.getInteger("Type")==1){

					FinaCardRoll cardRoll=new FinaCardRoll();
					cardRoll.setId(IdGen.getUUID());
					cardRoll.setCardId(cardId);
					cardRoll.setRollId(rollId);
					cardRoll.setBindingTime(new Date());
					cardRoll.setBindChannel("wmp");
					cardRoll.setCreateTime(new Date());
					if(cardRoll.save()){
						renderCodeSuccess("操作成功");
					}else{
						renderCodeFailed("保存失败");
					}

				}else{
					renderCodeFailed("操作失败，旅居同步接口返回失败");
				}
			}else{
				renderCodeFailed("操作失败，旅居同步接口返回格式有误");
			}
		}else{
			renderCodeFailed("操作失败，旅居同步接口返回数据为空");
		}
	}

	@JCors
	public void cardBatchBindRoll(){
		final String jsonStr = getRawData();
		StringBuilder sb = new StringBuilder();
		if(StrKit.isBlank(jsonStr) || StrKit.isBlank(jsonStr)){
			renderCodeFailed("参数不能为空");
			return;
		}
		JSONObject returnJson = JSONObject.parseObject(jsonStr);
		final JSONArray cardCouponNos = returnJson.getJSONArray("cardCouponNos");//卡券编号数组
		if(cardCouponNos==null || cardCouponNos.size()==0){
			renderCodeFailed("cardCouponNos参数缺失");
			return;
		}
		final boolean isCancel = returnJson.getBoolean("isCancel");//取消绑定true,默认false
		//作废
		if(isCancel){
			JSONObject paramJson = new JSONObject();
			paramJson.put("cardCouponNos", cardCouponNos);
			paramJson.put("description", "cmp");
			paramJson.put("isCancel", isCancel);
			final String returnStr = HttpClientsUtils.httpPostRaw(Global.sojournUrl+"/Member/EdiCardCoupon",paramJson.toJSONString(),null,null);
			if(StrKit.notBlank(returnStr)){
				if(returnStr.startsWith("{") && returnStr.endsWith("}")){
					JSONObject returnObj = JSON.parseObject(returnStr);
					if(returnObj.containsKey("Type") && returnObj.getInteger("Type")==1){
						for (int i = 0; i < cardCouponNos.size(); i++) {
							final String cardCouponNo = cardCouponNos.getString(i);
							logger.info("cardCouponNo===",cardCouponNo);
							CrmCardRollRecord rollRecord = crmCardRollRecordService.findByRollNumber(cardCouponNo);
							if(rollRecord!=null){
								Db.update("delete from fina_card_roll where roll_id=?", rollRecord.getId());
							}else{
								sb.append(cardCouponNo).append("卡券不存在").append(System.lineSeparator());
							}
						}
					}else{
						sb.append("操作失败，旅居同步接口返回失败");
					}
				}else{
					sb.append("操作失败，旅居同步接口返回格式有误");
				}
			}else{
				sb.append("操作失败，旅居同步接口返回数据为空");
			}
		}else{//绑定
			final String CardId = returnJson.getString("CardId");//会员卡id
			if(StrKit.isBlank(CardId) || StrKit.isBlank(CardId)){
				renderCodeFailed("CardId参数缺失");
				return;
			}
			FinaMembershipCard card = finaMembershipCardService.findById(CardId);
			if(card==null){
				renderCodeFailed(card.getCardNumber() + "会员卡不存在");
				return;
			}
			if("1".equals(card.getIsLock())){
				renderCodeFailed(card.getCardNumber() + "卡已锁定,不能绑定");
				return;
			}
			if("1".equals(card.getDelFlag())){
				renderCodeFailed(card.getCardNumber() + "卡已作废,不能绑定");
				return;
			}
			MainMembershipCardType cardType=mainMembershipCardTypeService.findById(card.getCardTypeId());
			if(cardType==null){
				renderCodeFailed(card.getCardNumber() + "会员卡类型不存在");
				return;
			}
			if(!"consume_card".equals(cardType.getTypeCategory())){
				renderCodeFailed("目前只允许消费卡绑定卡券");
				return;
			}
			List<FinaCardRoll> cardRollList = new ArrayList<FinaCardRoll>();
			if(cardCouponNos!=null && cardCouponNos.size()>0){
				for (int i = 0; i < cardCouponNos.size(); i++) {
					final String cardCouponNo = cardCouponNos.getString(i);
					logger.info("cardCouponNo===",cardCouponNo);
					Record record = Db.findFirst("select r.* from fina_card_roll r " +
							"left join crm_card_roll_record rr on rr.id=r.roll_id " +
							"where rr.roll_number=?",cardCouponNo);
					if(record!=null){
						FinaMembershipCard tempCard = finaMembershipCardService.findById(record.getStr("card_id"));
						sb.append(cardCouponNo).append("已被").append(tempCard.getCardNumber()).append("会员卡绑定").append(System.lineSeparator());
						continue;
					}
					CrmCardRollRecord rollRecord = crmCardRollRecordService.findByRollNumber(cardCouponNo);
					if(rollRecord==null){
						sb.append(cardCouponNo).append("卡券不存在").append(System.lineSeparator());
						continue;
					}else if("1".equals(rollRecord.getIsEnable())){
						sb.append(cardCouponNo).append("卡券已过期").append(System.lineSeparator());
						continue;
					}
					FinaCardRoll cardRoll=new FinaCardRoll();
					cardRoll.setId(IdGen.getUUID());
					cardRoll.setCardId(card.getId());
					cardRoll.setRollId(rollRecord.getId());
					cardRoll.setBindingTime(new Date());
					cardRoll.setBindChannel("cmp");
					cardRoll.setCreateTime(new Date());
					cardRollList.add(cardRoll);
				}
			}
			//cardRollList不为空的时候就保存数据
			if(cardRollList!=null && cardRollList.size()>0){
				Db.batchSave(cardRollList, cardRollList.size());
			}
		}
		if(sb.length()==0){
			renderCodeSuccess("操作成功");
		}else{
			renderCodeFailed(sb.toString());
		}
	}

	@JCors
	public void beanCouponsDetailPageList(){
		String cardId=getPara("cardId");

		String sql="select b.*,c.roll_name,c.valid_type,c.valid_days,c.valid_end_date,a.create_time as binding_time,c.final_valid_date " +
				" from fina_card_roll a INNER JOIN crm_card_roll_record b on a.roll_id=b.id " +
				" left join crm_card_roll c on c.id=b.card_roll_id " +
				"where a.card_id=? and b.del_flag='0' order by create_time,roll_number ";

		List<Record> recordList=Db.find(sql,cardId);

		for(Record record:recordList){
			if("2".equals(record.getStr("valid_type"))){
				Calendar calendar=Calendar.getInstance();
				calendar.setTime(record.getDate("binding_time"));
				calendar.add(Calendar.DATE, record.getInt("valid_days"));

				if(record.getDate("final_valid_date")!=null){
					Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(record.getDate("final_valid_date"),"yyyy-MM-dd")+" 23:59:59");
					if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
						record.set("max_use_time",DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss"));
					}else{
						record.set("max_use_time",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
					}
				}else{
					record.set("max_use_time",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
				}
			}else{
				record.set("max_use_time", DateUtils.formatDate(record.getDate("valid_end_date")
						,"yyyy-MM-dd")+" 23:59:59");
			}

		}

		renderCodeSuccess("success",recordList);
	}

	@JCors
	public void beanCouponsDetailPage(){
		String cardId=getPara("cardId");
		String status=getPara("status");
		Integer pageIndex = getParaToInt("pageIndex");
		Integer pageSize = getParaToInt("pageSize");
		if(pageIndex==null){
			pageIndex=1;
		}
		if(pageSize==null){
			pageSize=10;
		}
		String select=" select b.*,c.roll_name,c.valid_type,c.valid_days,c.valid_end_date,a.create_time as binding_time,c.final_valid_date ";
		String sql=" from fina_card_roll a INNER JOIN crm_card_roll_record b on a.roll_id=b.id " +
				" left join crm_card_roll c on c.id=b.card_roll_id " +
				"where a.card_id=? and b.del_flag='0' ";

		if(StrKit.notBlank(status)){
			if("1".equals(status)){
				sql+=" and b.is_use='1' ";
			}else if("2".equals(status)){
				sql+=" and b.is_use='0' and b.is_enable='0' ";
			}else if("3".equals(status)){
				sql+=" and b.is_enable='1' ";
			}
		}
		sql+=" order by create_time,roll_number ";

		Page<Record> pageRecordList=Db.paginate(pageIndex,pageSize,select,sql,cardId);
		if(pageRecordList.getList()!=null && pageRecordList.getList().size()>0){
			for(Record record:pageRecordList.getList()){
				if("2".equals(record.getStr("valid_type"))){
					Calendar calendar=Calendar.getInstance();
					calendar.setTime(record.getDate("binding_time"));
					calendar.add(Calendar.DATE, record.getInt("valid_days"));

					if(record.getDate("final_valid_date")!=null){
						Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(record.getDate("final_valid_date"),"yyyy-MM-dd")+" 23:59:59");
						if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
							record.set("max_use_time",DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss"));
						}else{
							record.set("max_use_time",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
						}
					}else{
						record.set("max_use_time",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
					}
				}else{
					record.set("max_use_time", DateUtils.formatDate(record.getDate("valid_end_date")
							,"yyyy-MM-dd")+" 23:59:59");
				}

			}
		}

		renderCodeSuccess("success",new DataTable<Record>(pageRecordList));
	}


	public void rollUseById(){
		String id=getPara("id");
		String userId=getPara("userId");
		//Integer useValue=getParaToInt("useValue");
		String idcard=getPara("idcard");
		String fullName=getPara("fullName");
		String telephone=getPara("telephone");

		if(StrKit.isBlank(id)){
			renderCodeFailed("id不能为空");
			return;
		}
		CrmCardRollRecord rollRecord=null;
		if(id.indexOf("-")!=-1){
			rollRecord=crmCardRollRecordService.findById(id);
		}else{
			if(!"f".equalsIgnoreCase(id.substring(id.length()-1,id.length()))){
				renderCodeFailed("该卡券编号不符合要求");
				return;
			}
			rollRecord=crmCardRollRecordService.findByRollNumber(id);
		}

		if(rollRecord==null){
			renderCodeFailed("该id查询不到卡券");
			return;
		}else if("1".equals(rollRecord.getDelFlag())){
			renderCodeFailed("该卡券已作废");
			return;
		}
		CrmCardRoll cardRoll=crmCardRollService.findById(rollRecord.getCardRollId());
		if("1".equals(cardRoll.getIsBingUse())){
			//绑定了才能使用
			if(Db.findFirst("select * from fina_card_roll where roll_id=?",rollRecord.getId())==null){
				renderCodeFailed("该卡券设置了绑定才能使用，请先绑定会员卡");
				return;
			}
		}

		if("1".equals(rollRecord.getIsEnable())){
			renderCodeFailed("该卡券已失效，操作失败");
			return;
		}
		if("1".equals(rollRecord.getIsUse())){
			renderCodeFailed("该卡券已被使用，操作失败");
			return;
		}
		CrmCardRollRecordUse rollRecordUse=new CrmCardRollRecordUse();
		if(rollRecord.getBalanceRollValue()!=null && rollRecord.getBalanceRollValue()>0){
			rollRecordUse.setUseRollValue(rollRecord.getBalanceRollValue());
			rollRecord.setBalanceRollValue(0.0);
		}else{
			rollRecord.setUpdateTime(new Date());
			rollRecord.setUpdateBy(userId);
		}
		rollRecord.setIsUse("1");
		rollRecordUse.setId(IdGen.getUUID());
		rollRecordUse.setUseType("2");
		rollRecordUse.setRecordId(rollRecord.getId());
		rollRecordUse.setRollNumber(rollRecord.getRollNumber());
		rollRecordUse.setUseTime(new Date());
		rollRecordUse.setIdcard(idcard);
		rollRecordUse.setFullName(fullName);
		rollRecordUse.setPhoneNumber(telephone);
		rollRecordUse.setDelFlag("0");
		rollRecordUse.setCreateBy(userId);
		rollRecordUse.setCreateTime(new Date());
		rollRecordUse.setUpdateBy(userId);
		rollRecordUse.setUpdateTime(new Date());

		if(rollRecord.update() && rollRecordUse.save()){
			renderCodeSuccess("操作成功");
		}else{
			renderCodeFailed("操作失败，请稍后重试");
		}
	}

	public void cancelRollUseById(){
		String id=getPara("id");
		String userId=getPara("userId");
		if(StrKit.isBlank(id) || StrKit.isBlank(userId)){
			renderCodeFailed("参数缺失");
			return;
		}
		CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(id);
		if(rollRecord==null){
			renderCodeFailed("该id查询不到卡券");
			return;
		}else if("1".equals(rollRecord.getDelFlag())){
			renderCodeFailed("该卡券已作废");
			return;
		}

		if("0".equals(rollRecord.getIsUse())){
			renderCodeFailed("该券未使用状态，撤销失败");
			return;
		}
		String useId=Db.queryStr("select id from crm_card_roll_record_use where record_id=? and del_flag='0' ORDER BY create_time desc limit 1",rollRecord.getId());
		CrmCardRollRecordUse rollRecordUse=crmCardRollRecordUseService.findById(useId);
		if(rollRecordUse==null){
			renderCodeFailed("获取该券使用记录失败");
			return;
		}
		CrmCardRoll cardRoll=crmCardRollService.findById(rollRecord.getCardRollId());

		Record record=Db.findFirst("select b.*,c.roll_name,c.valid_type,c.valid_days,c.valid_end_date,a.create_time as binding_time,c.final_valid_date  " +
				"from crm_card_roll_record b left JOIN  fina_card_roll a on a.roll_id=b.id  " +
				"left join crm_card_roll c on c.id=b.card_roll_id  " +
				"where b.id=? and b.del_flag='0'  ",rollRecord.getId());

		Date maxUseTime=null;
		//获取卡券的过期时间
		if("2".equals(cardRoll.getValidType())){
			if(record.getDate("binding_time")==null){
				renderCodeFailed("获取该券的绑定时间为空，撤销失败");
				return;
			}
			Calendar calendar=Calendar.getInstance();
			calendar.setTime(record.getDate("binding_time"));
			calendar.add(Calendar.DATE, record.getInt("valid_days"));

			if(record.getDate("final_valid_date")!=null){
				Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(record.getDate("final_valid_date"),"yyyy-MM-dd")+" 23:59:59");
				if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
					maxUseTime=DateUtils.parseDate(DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss"));
				}else{
					maxUseTime=DateUtils.parseDate(DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
				}
			}else{
				maxUseTime=DateUtils.parseDate(DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
			}
		}else{
			maxUseTime=DateUtils.parseDate(DateUtils.formatDate(record.getDate("valid_end_date")
					,"yyyy-MM-dd")+" 23:59:59");
		}
		if(DateUtils.compareDays(maxUseTime,new Date())<0){
			renderCodeFailed("该券最大可用时间为："+DateUtils.formatDate(record.getDate("max_use_time"),"yyyy-MM-dd HH:mm:ss")+"，撤销失败");
			return;
		}
		rollRecord.setIsUse("0");
		rollRecord.setBalanceRollValue(rollRecordUse.getUseRollValue());
		rollRecord.setUpdateBy(useId);
		rollRecord.setUpdateTime(new Date());

		rollRecordUse.setDelFlag("1");
		rollRecordUse.setUpdateTime(new Date());
		rollRecordUse.setUpdateBy(userId);

		if(rollRecordUse.update() && rollRecord.update()){
			renderCodeSuccess("操作成功");
		}else{
			renderCodeFailed("操作失败");
		}

	}

	public void checkRollStatus(){
		String id=getPara("id");
		if(StrKit.isBlank(id)){
			renderCodeFailed("参数缺失");
			return;
		}
		CrmCardRollRecord rollRecord=null;
		if(id.indexOf("-")!=-1){
			rollRecord=crmCardRollRecordService.findById(id);
		}else{
			if(!"f".equalsIgnoreCase(id.substring(id.length()-1,id.length()))){
				renderCodeFailed("该卡券编号不符合要求");
				return;
			}
			rollRecord=crmCardRollRecordService.findByRollNumber(id);
		}
		if(rollRecord==null){
			renderCodeFailed("该id查询不到卡券");
			return;
		}else if("1".equals(rollRecord.getDelFlag())){
			renderCodeFailed("该卡券已作废");
			return;
		}
		CrmCardRoll roll=crmCardRollService.findById(rollRecord.getCardRollId());

		Record record=new Record();
		record.set("id",rollRecord.getId());
		record.set("rollName",roll.getRollName());
		record.set("rollNumber",rollRecord.getRollNumber());
		record.set("rollValue",rollRecord.getBalanceRollValue());
		record.set("balanceRollValue",rollRecord.getBalanceRollValue());
		if("0".equals(rollRecord.getIsEnable())){
			record.set("isEnable","1");
		}else{
			record.set("isEnable","0");
			renderCodeFailed("该卡券为不可用状态");
			return;
		}
		record.set("isUse",rollRecord.getIsUse());
		if("1".equals(rollRecord.getIsUse())){
			renderCodeFailed("该卡券已被使用");
			return;
		}
		if("1".equals(roll.getIsBingUse())){
			FinaCardRoll finaCardRoll = finaCardRollService.findByRollId(rollRecord.getId());
			if(finaCardRoll==null){
				renderCodeFailed("该卡券为未绑定状态");
				return;
			}
		}

		Record bindingRecord=Db.findFirst("select * from fina_card_roll where roll_id=? ",rollRecord.getId());
		//
		if("2".equals(roll.getValidType())){
			if(bindingRecord!=null){
				Calendar calendar=Calendar.getInstance();
				calendar.setTime(bindingRecord.getDate("binding_time"));
				calendar.add(Calendar.DATE, roll.getValidDays());

				if(roll.getFinalValidDate()!=null){
					Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(roll.getFinalValidDate(),"yyyy-MM-dd")+" 23:59:59");
					if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
						record.set("maxUseTime",DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss"));
					}else{
						record.set("maxUseTime",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
					}
				}else{
					record.set("maxUseTime",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
				}
			}else{
				Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(roll.getFinalValidDate(),"yyyy-MM-dd")+" 23:59:59");
				record.set("maxUseTime", DateUtils.formatDate(finalValidDate
						,"yyyy-MM-dd")+" 23:59:59");
			}
		}else{
			record.set("maxUseTime", DateUtils.formatDate(roll.getValidEndDate()
					,"yyyy-MM-dd")+" 23:59:59");
		}
		if(DateUtils.compareDays(DateUtils.parseDate(record.getStr("maxUseTime")),new Date())<0){
			renderCodeFailed("该卡券已过期");
			return;
		}
		renderCodeSuccess("success",record);
	}

	public void getRollById(){
		String id=getPara("id");
		if(StrKit.isBlank(id)){
			renderCodeFailed("参数缺失");
			return;
		}
		CrmCardRollRecord rollRecord=crmCardRollRecordService.findById(id);
		if(rollRecord==null){
			renderCodeFailed("该id查询不到卡券");
			return;
		}
		CrmCardRoll roll=crmCardRollService.findById(rollRecord.getCardRollId());

		Record record=new Record();
		record.set("id",rollRecord.getId());
		record.set("rollName",roll.getRollName());
		record.set("rollNumber",rollRecord.getRollNumber());
		record.set("rollValue",rollRecord.getBalanceRollValue());
		record.set("balanceRollValue",rollRecord.getBalanceRollValue());
		if("0".equals(rollRecord.getIsEnable())){
			record.set("isEnable","1");
		}else{
			record.set("isEnable","0");
			renderCodeFailed("该卡券为不可用状态");
			return;
		}
		record.set("isUse",rollRecord.getIsUse());
		if("1".equals(rollRecord.getIsUse())){
			renderCodeFailed("该卡券已被使用");
			return;
		}
		if("1".equals(roll.getIsBingUse())){
			FinaCardRoll finaCardRoll = finaCardRollService.findByRollId(rollRecord.getId());
			if(finaCardRoll==null){
				renderCodeFailed("该卡券为未绑定状态");
				return;
			}
		}

		Record bindingRecord=Db.findFirst("select * from fina_card_roll where roll_id=? ",rollRecord.getId());
		//
		if("2".equals(roll.getValidType())){
			if(bindingRecord!=null){
				Calendar calendar=Calendar.getInstance();
				calendar.setTime(bindingRecord.getDate("binding_time"));
				calendar.add(Calendar.DATE, roll.getValidDays());

				if(roll.getFinalValidDate()!=null){
					Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(roll.getFinalValidDate(),"yyyy-MM-dd")+" 23:59:59");
					if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
						record.set("maxUseTime",DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss"));
					}else{
						record.set("maxUseTime",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
					}
				}else{
					record.set("maxUseTime",DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss"));
				}
			}else{
				Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(roll.getFinalValidDate(),"yyyy-MM-dd")+" 23:59:59");
				record.set("maxUseTime", DateUtils.formatDate(finalValidDate
						,"yyyy-MM-dd")+" 23:59:59");
			}
		}else{
			record.set("maxUseTime", DateUtils.formatDate(roll.getValidEndDate()
					,"yyyy-MM-dd")+" 23:59:59");
		}

		renderCodeSuccess("success",record);
	}

	public void updateRollRecord(){

		List<Record> recordList=Db.find("select * from crm_card_roll_record where del_flag='0' and is_enable='1' ");
		for (Record r : recordList) {

			CrmCardRollRecord record=crmCardRollRecordService.findById(r.getStr("id"));


			Record bindingRecord=Db.findFirst("select * from fina_card_roll where roll_id=? ",record.getId());

			CrmCardRoll roll=crmCardRollService.findById(record.getCardRollId());
			String maxUseTime="";
			if("2".equals(roll.getValidType())){
				if(bindingRecord!=null){
					Calendar calendar=Calendar.getInstance();
					calendar.setTime(bindingRecord.getDate("binding_time"));
					calendar.add(Calendar.DATE, roll.getValidDays());

					if(roll.getFinalValidDate()!=null){
						Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(roll.getFinalValidDate(),"yyyy-MM-dd")+" 23:59:59");
						if(DateUtils.compareDays(finalValidDate,calendar.getTime())==-1){
							maxUseTime=DateUtils.formatDate(finalValidDate,"yyyy-MM-dd HH:mm:ss");
						}else{
							maxUseTime=DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss");
						}
					}else{

						maxUseTime=DateUtils.formatDate(calendar.getTime(),"yyyy-MM-dd HH:mm:ss");
					}
				}else{
					Date finalValidDate=DateUtils.parseDate(DateUtils.formatDate(roll.getFinalValidDate(),"yyyy-MM-dd")+" 23:59:59");
					maxUseTime=DateUtils.formatDate(finalValidDate
							,"yyyy-MM-dd")+" 23:59:59";
				}
			}else{
				maxUseTime=DateUtils.formatDate(roll.getValidEndDate()
						,"yyyy-MM-dd")+" 23:59:59";
			}
			if(StrKit.notBlank(maxUseTime)){
				record.setExpirationTime(DateUtils.parseDate(maxUseTime));
				record.update();
			}
		}

	}

	public void verifyNumberInfo(){
		String cardNumber=getPara("cardNumber");
		String telephone=getPara("telephone");
		String name=getPara("name");

		if(StrKit.isBlank(cardNumber) || StrKit.isBlank(telephone) || StrKit.isBlank(name)){
			renderCodeFailed("参数缺失");
			return;
		}
		FinaMembershipCard card=finaMembershipCardService.findCardByCardNumber(cardNumber.trim());
		if(card==null){
			renderCodeFailed("该会员号不存在");
			return;
		}
		if("1".equals(card.getIsLock())){
			renderCodeFailed("该会员卡已被锁定");
			return;
		}
		MmsMember member = mmsMemberService.findById(card.getMemberId());
		if(!telephone.trim().equals(card.getTelephone()) || !name.trim().equals(member.getFullName())){
			renderCodeFailed("电话号码或姓名与会员卡信息不一致");
			return;
		}
		renderCodeSuccess("success");
	}

	public void getUnionIdByCardNumber(){
		String cardNumbers=getPara("cardNumbers");
		if(StrKit.isBlank(cardNumbers)){
			renderCodeFailed("参数缺失");
			return;
		}
		JSONArray jsonArray=JSON.parseArray(cardNumbers);
		if(jsonArray==null || jsonArray.size()==0){
			renderCodeFailed("参数数量不能为0");
			return;
		}
		Map<String,List<String>> returnMap=new HashMap<>();
		for (int i = 0; i < jsonArray.size(); i++) {
			String cardNumber=jsonArray.getString(i);
			FinaMembershipCard card=finaMembershipCardService.findCardByCardNumber(cardNumber);
			if(card==null){
				returnMap.put(cardNumber,new ArrayList<String>());
			}else{
				List<String> unionidList=Db.query("select b.unionid from mms_wx_user_card_rela a inner join mms_wx_user b on b.id=a.user_id " +
						"where a.del_flag='0' and a.card_id=? GROUP BY b.unionid ",card.getId());
				returnMap.put(cardNumber,unionidList);
			}
		}
		renderCodeSuccess("success",returnMap);
	}

	//Oracle
	public static void main(String[] args) throws Exception {
		InputStream inputStream=new FileInputStream("D:\\zhuo\\公立医院审计数据标准表（27张表）.xlsx");
		XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
		inputStream.close();
		XSSFSheet sheet = workbook.getSheet("17.病案首页表");
		String tableName="病案首页表";
		String str="CREATE TABLE "+tableName+" (\n";
		for(int i=1;i<=sheet.getLastRowNum();i++) {
			String name =  sheet.getRow(i).getCell(1).getStringCellValue();
			String type =  sheet.getRow(i).getCell(3).getStringCellValue();
			String remark =  sheet.getRow(i).getCell(2).getStringCellValue();

			if(type.equals("varchar2")){
				type="varchar2(255)";
			}else if(type.equals("number")){
				type="number(18,4)";
			}
			str+=name+" "+type+" , \n";
		}
		/*str+="fstring CLOB , \n";
		str+="create_time DATE , \n";
		str+="cjsj_time DATE  \n";*/
		//
		String str2="";
		String str3="";
		for(int i=1;i<=sheet.getLastRowNum();i++) {
			String name =  sheet.getRow(i).getCell(1).getStringCellValue();
			String type =  sheet.getRow(i).getCell(3).getStringCellValue();
			String remark =  sheet.getRow(i).getCell(2).getStringCellValue();
			//System.out.println(roomName);
			String isYStr="";

			str2+="COMMENT ON COLUMN "+tableName+"."+name+" IS '"+remark+"';\n";

			String fieldType="String";
			if(type.indexOf("DATE")!=-1){
				fieldType="Date";
			}

			str3+="@TableField(value = \""+name+"\")\n";
			str3+="private "+fieldType+" "+name+";\n";
			//System.out.println();
			//System.out.println();
		}
		str+=");";
		//str2+="COMMENT ON COLUMN "+tableName+"."+"fstring"+" IS '"+"原始数据"+"';\n";
		//str2+="COMMENT ON COLUMN "+tableName+"."+"create_time"+" IS '"+"病历书写日期"+"';\n";
		//str2+="COMMENT ON COLUMN "+tableName+"."+"cjsj_time"+" IS '"+"病历采集日期"+"';\n";

		str3+="@TableField(value = \""+"fstring"+"\")\n";
		str3+="private "+"String"+" "+"fstring"+";\n";

		str3+="@TableField(value = \""+"create_time"+"\")\n";
		str3+="private "+"Date"+" "+"create_time"+";\n";

		str3+="@TableField(value = \""+"cjsj_time"+"\")\n";
		str3+="private "+"Date"+" "+"cjsj_time"+";\n";

		System.out.println(str);
		System.out.println();
		System.out.println();
		System.out.println();
		System.out.println();
		System.out.println(str2);
		System.out.println();
		System.out.println();
		System.out.println();
		System.out.println();
		//System.out.println(str3);
	}

	public void getAllCardRoll() {
		CrmCardRoll model = getBean(CrmCardRoll.class, "", true);
		model.setIsEnable("0");
		model.setDeployStatus("0");
		Page<CrmCardRoll> page = crmCardRollService.paginate(model, 1, 10000);

		List<Record> recordList=new ArrayList<>();
		if(page.getList()!=null && page.getList().size()>0){
			String str="";
			List<String> idList=new ArrayList<>();
			for (CrmCardRoll roll : page.getList()) {
				idList.add(roll.getId());
				str+="?,";
				Record record=new Record();
				record.set("num",0);
				record.set("id",roll.getId());
				record.set("rollName",roll.getRollName());
				record.set("typeName",roll.getTypeName());
				recordList.add(record);
			}
			str=str.substring(0,str.length()-1);
			List<Record> numRecordList=Db.find("select card_roll_id,COUNT(id) as num from crm_card_roll_record where del_flag='0' and is_use='0' and is_enable='0' " +
					"and id not in (select roll_id from fina_card_roll) " +
					"and card_roll_id in ("+str+") " +
					"GROUP BY card_roll_id",idList.toArray());
			Map<String,Integer> numMap=new HashMap<>();
			for (Record record : numRecordList) {
				numMap.put(record.getStr("card_roll_id"),record.getInt("num"));
			}
			for (Record record : recordList) {
				if(numMap.containsKey(record.getStr("id"))){
					record.set("num",numMap.get(record.getStr("id")));
				}
			}
		}

		renderCodeSuccess("success",recordList);
	}

	public void getRollProductList(){
		String id=getPara("id");
		if(StrKit.isBlank(id)){
			renderCodeFailed("参数缺失");
			return;
		}
		List<Record> recordList = Db.find("select d.id,d.`name`,d.standard,d.barcode_number as barcodeNumber,c.num from crm_card_roll_record a " +
				"inner join crm_card_roll b on a.card_roll_id=b.id " +
				"inner join crm_card_roll_model_rel c on b.id=c.roll_id " +
				"inner join wms_stock_models d on d.id=c.model_id " +
				"where a.id=? ", id);
		renderCodeSuccess("success",recordList);
	}

	public void getRollProductListByTypeId(){
		String id=getPara("typeId");
		if(StrKit.isBlank(id)){
			renderCodeFailed("参数缺失");
			return;
		}
		List<Record> recordList = Db.find("select d.id,d.`name`,d.standard,d.barcode_number as barcodeNumber,c.num from crm_card_roll b " +
				"inner join crm_card_roll_model_rel c on b.id=c.roll_id " +
				"inner join wms_stock_models d on d.id=c.model_id " +
				"where b.id=? ", id);
		renderCodeSuccess("success",recordList);
	}

	public void getCardRoll(){
		String cardId = getPara("memberCardId");
		String ids=getPara("ids");
		if(StrKit.isBlank(cardId)){
			renderCodeFailed("参数缺失");
			return;
		}
		String sql="select b.id,c.id as rollId,b.roll_number as rollNumber,d.type_name as typeName,c.roll_name as rollName,b.roll_value as rollValue,b.balance_roll_value as balanceRollValue " +
				" from fina_card_roll a " +
				"inner join crm_card_roll_record b on a.roll_id=b.id " +
				"inner join crm_card_roll c on c.id=b.card_roll_id " +
				"inner join crm_card_roll_type d on d.id=c.roll_type_id " +
				"where card_id=? and b.del_flag='0' and c.del_flag='0' and b.is_use='0' and b.is_enable='0' ";
		List<String> params=new ArrayList<>();
		params.add(cardId);
		if(StrKit.notBlank(ids)){
			JSONArray jsonArray = JSONArray.parseArray(ids);
			if(jsonArray!=null && jsonArray.size()>0){
				String str="";
				for (int i = 0; i < jsonArray.size(); i++) {
					params.add(jsonArray.getString(i));
					str+="?,";
				}
				str=str.substring(0,str.length()-1);
				sql+=" and b.id in ("+str+") ";
			}
		}

		List<Record> recordList = Db.find(sql,params.toArray());





		if(recordList.size()>0){
			Set<String> typeIds=new HashSet<>();
			String str="";
			for (Record record : recordList) {
				typeIds.add(record.getStr("rollId"));
			}
			for (String typeId : typeIds) {
				str+="?,";
			}

			str=str.substring(0,str.length()-1);
			List<Record> rollModelRelList=Db.find("select a.roll_id as rollId,b.id as modelId,a.num,b.`name` as modelName from crm_card_roll_model_rel a " +
					"inner join wms_stock_models b on a.model_id=b.id " +
					"where a.roll_id in ("+str+")",typeIds.toArray());
			Map<String,List<Record>> rollModelRelMap=new HashMap<>();
			for (Record record : rollModelRelList) {
				List<Record> modelList=null;
				if(rollModelRelMap.containsKey(record.getStr("rollId"))){
					modelList=rollModelRelMap.get(record.getStr("rollId"));
				}else{
					modelList=new ArrayList<>();
					rollModelRelMap.put(record.getStr("rollId"),modelList);
				}
				modelList.add(record);
			}

			for (Record record : recordList) {
				record.set("good",rollModelRelMap.get(record.getStr("rollId")));
			}
		}

		renderCodeSuccess("success",recordList);
	}

}
