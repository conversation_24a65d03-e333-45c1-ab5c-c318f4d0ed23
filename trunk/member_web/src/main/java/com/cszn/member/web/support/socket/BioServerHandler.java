package com.cszn.member.web.support.socket;


import com.cszn.integrated.base.utils.BioHelper;
import com.cszn.integrated.base.utils.SendBioHelper;
import com.cszn.integrated.service.api.member.BioApiService;
import com.jfinal.aop.Inject;
import com.jfinal.ext.interceptor.LogInterceptor;
import io.jboot.Jboot;
import io.jboot.aop.annotation.Bean;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.DataInputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.net.Socket;
import java.util.Map;

public class BioServerHandler implements Runnable {

    private static Logger logger = LoggerFactory.getLogger(LogInterceptor.class);
	 
    private Socket socket;

    private BioApiService bioApiService;

 
    public BioServerHandler(Socket socket) {
        this.socket = socket;
    }

    public BioServerHandler(Socket socket,BioApiService bioApiService) {
        this.socket = socket;
        this.bioApiService = bioApiService;
    }



    public void run() {
    	DataInputStream in = null;
    	PrintStream out = null;
        try {
        	out = new PrintStream(socket.getOutputStream());
			while (true) {
				// 接受数据，但不允许有中文，因为会乱码
				in = new DataInputStream(socket.getInputStream());
				byte[] buffer = new byte[1024]; // 缓冲区的大小
				in.read(buffer); // 处理接收到的报文，转换成字符串
				// C++传递过来的中文字，需要转化一下。C++默认使用GBK。
				// GB2312是GBK的子集，只有简体中文。因为数据库用GB2312，所以这里直接转为GB2312
				String messageFromDM = new String(buffer, "GB2312").trim();
				if (null == messageFromDM || "".equals(messageFromDM)) {
					break;
				}
                logger.info("bio手环数据接收：[{}]",messageFromDM);
				//处理socketMap存放
                String msg = messageFromDM.replace("[","").replace("]","");
                String[] msgArr = msg.split(",");
                String equipmentNo = BioHelper.dealEquipmentNumber(msgArr[0]);
                if(msgArr[0].contains("PEDO") || msgArr[0].contains("WALKTIME")) {
                    bioApiService.saveCommandRecord(equipmentNo, msgArr[0]);
                }
                SendBioHelper.getSocketMap().put(equipmentNo,socket);
                //socketMap end
                String replyCommand = bioApiService.dealBioData(messageFromDM);
                logger.info("回复指令：[{}]",replyCommand);
                if(StringUtils.isNotBlank(replyCommand)) {
                    out.print(replyCommand);
                }
			}
        } catch (IOException e) {
            logger.info("手环外部异常：[{}]",e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (out != null) {
                out.close();
            }
        }
    }
}