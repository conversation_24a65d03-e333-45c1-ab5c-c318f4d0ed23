package com.cszn.member.web.support.cron;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cszn.integrated.base.utils.HttpClientsUtils;
import com.cszn.integrated.base.utils.IdGen;
import com.cszn.integrated.service.entity.enums.SendType;
import com.cszn.integrated.service.entity.sms.SmsSendRecord;
import com.cszn.integrated.service.entity.status.Global;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import io.jboot.components.schedule.annotation.Cron;

import java.util.*;

@Cron("45 9 * * *")
public class MemberBirthdayTask implements Runnable {

    @Override
    public void run() {
        List<String> phoneNumList= Db.query("select telephone from mms_member where del_flag='0' and right(birthday, 5)=DATE_FORMAT(now(),'%m-%d') GROUP BY telephone ");
        if(phoneNumList!=null && phoneNumList.size()>0){
            String regex = "^1[3-9][0-9]\\d{8}$";

            Map<String,String> params=new HashMap<>();
            params.put("tempId", SendType.customContent.getTemplateId());
            List<SmsSendRecord> recordList=new ArrayList<>();
            //获取发送内容
            String content=Db.queryStr("select every_day_send_content from mms_sms_type where del_flag='0' and sending_method='everyDay' limit 1 ");
            if(StrKit.isBlank(content)){
                return;
            }
            for(String phoneNum:phoneNumList){
                if(StrKit.isBlank(phoneNum) || !phoneNum.matches(regex)){
                    continue;
                }
                //发送短信
                params.put("mobile",phoneNum);
                params.put("data", "{\"content\":\""+ content+"\"}");

                String resultStr= HttpClientsUtils.httpPostForm(Global.sendMessageUrl, params,null,"UTF-8");

                if(resultStr.startsWith("{") && resultStr.endsWith("}")){
                    JSONObject object= JSON.parseObject(resultStr);
                    if(object.containsKey("Type") && "1".equals(object.getString("Type"))){

                    }else{
                        //保存一条短信发送失败记录
                        SmsSendRecord record=new SmsSendRecord();
                        record.setId(IdGen.getUUID());
                        record.setSendType(SendType.customContent.getKey());
                        record.setPhoneNum(phoneNum);
                        record.setSmsParam(JSON.toJSONString(params));
                        record.setSmsTemplateId(SendType.customContent.getTemplateId());
                        record.setSmsSendStatus("send_fail");
                        record.setDelFlag("0");
                        record.setCreateDate(new Date());
                        record.setUpdateDate(new Date());
                        recordList.add(record);
                    }
                }
            }

            if(recordList.size()>0){
                Db.batchSave(recordList,recordList.size());
            }
        }
    }
}
