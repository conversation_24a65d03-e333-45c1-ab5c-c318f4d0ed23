package com.cszn.member.web.controller.member;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.member.MmsFaceRecognitionRecordService;
import com.cszn.integrated.service.entity.member.MmsFaceRecognitionRecord;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;

/**
 * @Description 终端识别数据管理
 * <AUTHOR>
 * @Date 2019/6/25
 **/
@RequestMapping(value="/mms/faceRecognitionRecord", viewPath="/modules_page/mms/faceRecognitionRecord")
public class FaceRecognitionRecordController extends BaseController {

    @Inject
    private MmsFaceRecognitionRecordService mmsFaceRecognitionRecordService;


    /**
     * 跳转人脸识别记录
     */
    public void index(){
        render("faceRecognitionRecordIndex.html");
    }


    /**
     * 人脸识别列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        String fullName = getPara("fullName");
        String idcard = getPara("idcard");
        Page<Record> page = mmsFaceRecognitionRecordService.findList(getParaToInt("page"), getParaToInt("limit"),fullName,idcard);
        renderJson(new DataTable<Record>(page));
    }



    /**
     * 查看
     */
    public void form(){
        String id = getPara("id");
        MmsFaceRecognitionRecord fr = mmsFaceRecognitionRecordService.get(id);
        if(fr != null && fr.getImgBase64() != null){
            setAttr("imgBase64Str","data:image/jpg;base64," + new String(fr.getImgBase64()));
        }
        render("faceRecognitionRecordForm.html");
    }



    /**
     * 删除
     */
    public void delete(){
        String id = getPara("id");
        MmsFaceRecognitionRecord faceRecord = mmsFaceRecognitionRecordService.get(id);
        if(faceRecord == null){ renderJson(Ret.ok("msg", "人脸识别记录不存在"));return; }
        if(mmsFaceRecognitionRecordService.deleteById(id)){
            renderJson(Ret.ok("msg", "作废成功"));
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }
}
