package com.cszn.member.web.controller.equipment;

import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.main.MainBaseBuildingService;
import com.cszn.integrated.service.api.main.MainBaseFloorService;
import com.cszn.integrated.service.api.main.MainBaseService;
import com.cszn.integrated.service.api.member.MmsGatewayService;
import com.cszn.integrated.service.entity.main.MainBase;
import com.cszn.integrated.service.entity.main.MainBaseBuilding;
import com.cszn.integrated.service.entity.main.MainBaseFloor;
import com.cszn.integrated.service.entity.member.MmsGateway;
import com.cszn.member.web.support.auth.AuthUtils;
import com.cszn.member.web.support.log.LogInterceptor;
import com.google.common.collect.Lists;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import io.jboot.web.controller.annotation.RequestMapping;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @Description 网关设备信息管理
 * <AUTHOR>
 * @Date 2019/6/26
 **/
@RequestMapping(value="/mms/gateway", viewPath="/modules_page/mms/gateway")
public class GatewayController extends BaseController {

    @Inject
    private MmsGatewayService mmsGatewayService;

    @Inject
    private MainBaseService mainBaseService;

    @Inject
    private MainBaseBuildingService mainBaseBuildingService;

    @Inject
    private MainBaseFloorService mainBaseFloorService;



    /**
     * 跳转网关设备信息界面
     */
    public void index(){
        List<MainBase> baseList = mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        render("gatewayIndex.html");
    }



    /**
     * 联动获取所有楼栋
     */
    @Clear(LogInterceptor.class)
    public void getBuildings(){
        String id = getPara("id");
        List<MainBaseBuilding> buildingList = Lists.newArrayList();
        Ret restRsult = Ret.ok("msg", "加载楼栋成功!");
        try {
            buildingList = mainBaseBuildingService.getBuildingListByBaseIds(new String[]{id});
        } catch (Exception e) {
            restRsult = Ret.fail("msg", "加载楼栋失败！");
        }
        restRsult.set("resultData", buildingList);
        renderJson(restRsult);
    }



    /**
     * 联动获取所有楼层
     */
    @Clear(LogInterceptor.class)
    public void getFloors(){
        String id = getPara("id");
        List<MainBaseFloor> floorList = Lists.newArrayList();
        Ret restRsult = Ret.ok("msg", "加载楼层成功!");
        try {
            floorList = mainBaseFloorService.getFloorListByBuildingIds(new String[]{id});
        } catch (Exception e) {
            restRsult = Ret.fail("msg", "加载楼层失败！");
        }
        restRsult.set("resultData", floorList);
        renderJson(restRsult);
    }



    /**
     * 上传数据设备信息列表
     */
    @Clear(LogInterceptor.class)
    public void findListPage(){
        MmsGateway gatewayType = getBean(MmsGateway.class,"",true);
        Page<Record> page = mmsGatewayService.findListPage(getParaToInt("page"), getParaToInt("limit"),gatewayType);
        renderJson(new DataTable<Record>(page));
    }


    /**
     * 跳转新增/修改界面
     */
    public void form(){
        String id = getPara("id");
        MmsGateway gateway = mmsGatewayService.get(id);
        List<MainBase> baseList = mainBaseService.findBaseList();
        setAttr("baseList",baseList);
        setAttr("gateway",gateway);
        render("gatewayForm.html");
    }


    /**
     * 保存
     */
    public void save(){
        MmsGateway gateway = getBean(MmsGateway.class,"gateway",true);
        if(gateway == null){renderJson(Ret.fail("msg", "保存失败")); return;}

        if(StringUtils.isNotBlank(gateway.getId())){
            MmsGateway gatewayExist = mmsGatewayService.get(gateway.getId());
            if(gatewayExist == null){ renderJson(Ret.fail("msg", "保存失败")); return; }
        }
        String flag = mmsGatewayService.saveGateway(gateway,AuthUtils.getUserId());
        if("suc".equals(flag)){
            renderJson(Ret.ok("msg", "保存成功"));
        }else if("".equals(flag)){
            renderJson(Ret.fail("msg", "网关编号(MAC)不能重复"));
        }else{
            renderJson(Ret.fail("msg", "保存失败"));
        }
    }


    /**
     * 删除
     */
    public void delete(){
        String id = getPara("id");
        MmsGateway gateway = mmsGatewayService.get(id);
        if(gateway != null){
            boolean flag = mmsGatewayService.delGateway(id, AuthUtils.getUserId());
            if (flag) {
                renderJson(Ret.ok("msg", "作废成功"));
            } else {
                renderJson(Ret.fail("msg", "作废失败"));
            }
        }else{
            renderJson(Ret.fail("msg", "作废失败"));
        }
    }


}
