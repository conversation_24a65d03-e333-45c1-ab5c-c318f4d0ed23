/**
 * 
 */
package com.cszn.member.web.controller.sys;

import com.cszn.integrated.base.common.ZTree;
import com.cszn.integrated.base.rest.datatable.DataTable;
import com.cszn.integrated.base.web.base.BaseController;
import com.cszn.integrated.service.api.sys.MenuService;
import com.cszn.integrated.service.api.sys.RoleService;
import com.cszn.integrated.service.entity.status.SystemType;
import com.cszn.integrated.service.entity.sys.Role;
import com.cszn.member.web.support.log.LogInterceptor;
import com.jfinal.aop.Clear;
import com.jfinal.aop.Inject;
import com.jfinal.kit.Ret;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.web.controller.annotation.RequestMapping;

import java.util.List;

/**
 * Created by LiangHuiLing on 2019年3月28日
 *
 * RoleController
 */
@RequestMapping(value="/role", viewPath="/modules_page/sys/role")
public class RoleController extends BaseController {

	@Inject
    private MenuService menuService;
	@Inject
    private RoleService roleService;
	
    public void index() {
        render("roleIndex.html");
    }
    
    /**
     * 角色分页表格数据
     */
    @Clear(LogInterceptor.class)
    public void pageTable() {
    	Role role = getBean(Role.class, "", true);
        role.setRoleSystem(SystemType.MEMBER);
        Page<Role> rolePage = roleService.paginateByCondition(role, getParaToInt("page", 1), getParaToInt("limit", 10));
        renderJson(new DataTable<Role>(rolePage));
    }

    /**
     * 添加页面方法
     */
    public void add() {
    	Role role = new Role();
    	setAttr("model", role);
        render("roleForm.html");
    }

    /**
     * 修改页面方法
     */
    public void edit() {
        final String roleId = getPara("id");
        setAttr("model", roleService.findById(roleId));
        render("roleForm.html");
    }
    
    /**
     * 角色对应的菜单权限树
     */
    @Clear(LogInterceptor.class)
    public void roleMenuTree() {
        final String roleId = getPara("roleId");
        List<ZTree> treeNodeList = menuService.roleMenuTree(roleId, SystemType.MEMBER);
        renderJson(treeNodeList);
    }
    
    public void save() {
    	String menuIds = getPara("menuIds");
    	Role role = getBean(Role.class, "", true);
        role.setRoleSystem(SystemType.MEMBER);
    	final boolean isExist = roleService.checkRoleNameRepeat(role.getRoleName(),SystemType.MEMBER);
    	if (StrKit.isBlank(role.getId()) && isExist) {
    		renderJson(Ret.fail("msg", "角色名称已存在！"));
    	}else{
    		if(roleService.roleSave(role, menuIds)){
    			renderJson(Ret.ok("msg", "操作成功!"));
    		}else{
    			renderJson(Ret.fail("msg", "操作失败！"));
    		}
    	}
    }

    public void del(){
        String id=getPara("id");
        if(StrKit.isBlank(id)){
            renderJson(Ret.fail("msg","操作失败"));
            return;
        }else{
            Role role=roleService.findById(id);
            role.setDelFlag("1");
            if(role.update()){
                Db.update("delete from sys_role_menu where role_id=? ",role.getId());
                renderJson(Ret.ok("msg","操作成功"));
                return;
            }
        }
    }
}
